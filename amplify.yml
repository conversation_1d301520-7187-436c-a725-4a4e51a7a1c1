version: 1
frontend:
  phases:
    preBuild:
      commands:
        - echo "Environment is $ENV"
        - nvm install 12
        - nvm list
        - nvm use 12
        - npm cache clean --force
        - npm config set strict-ssl false
        - npm install
        - if [ "$ENV" = "test" ]; then sed -i 's/const env = .*$/const env = "magic-curriculum-test";/g' src/utils/setBaseUrl.js; fi
        - if [ "$ENV" = "stage" ]; then sed -i 's/const env = .*$/const env = "magic-curriculum-stage";/g' src/utils/setBaseUrl.js; fi
        - if [ "$ENV" = "prod" ]; then sed -i 's/const env = .*$/const env = "magic-curriculum-prod";/g' src/utils/setBaseUrl.js; fi
        - sed -i 's/const region = .*$/const region = "us";/g' src/utils/setBaseUrl.js
        - if [ "$PROJECT" = "CURRICULUM-GENIE" ]; then sed -i 's/let platform = .*$/let platform = "CURRICULUM-GENIE";/g' src/utils/setBaseUrl.js; fi
        - |
          if [ "$PROJECT" = "MAGIC-CURRICULUM" ]; then
            sed -i ':a;N;$!ba;s/<noscript>.*<\/noscript>/<noscript>Magicurriculum is an innovative AI-powered tool that brings magic to lesson planning, making it easy and efficient for teachers.<\/noscript>/g' public/index.html
            sed -i 's/<title>Learning Genie<\/title>/<title>Magicurriculum<\/title>\n    <meta name="description" content="Magicurriculum is an innovative AI-powered tool that brings magic to lesson planning, making it easy and efficient for teachers.">/g' public/index.html
          fi
        - cat src/utils/setBaseUrl.js
        - cat public/index.html
    build:
      commands:
        - npm run $ENV-build
  artifacts:
    baseDirectory: v2
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*