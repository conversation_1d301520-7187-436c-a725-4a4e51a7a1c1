module.exports = {
  root: true,
  env: {
    node: true
  },
  'extends': [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-eq-null': 2,// 禁止对null使用==或!=运算符
    'comma-spacing': 0,// 逗号前后的空格
    'eqeqeq': 0,// 必须使用全等
    'new-cap': 0,
    'no-eval': 0, // 不能有eval
    'indent': 'off',
    'handle-callback-err': 0 // callback有err参数必须写err或error
    // 'semi': ['error', 'always']// 行尾必须加分号
  },
  parserOptions: {
    parser: 'babel-eslint'
  },
  globals: {
    '$': false,
    'jquery': false,
    '$i18n': false,
    '$axios': false,
    '$router': false,
    'WebIM': false,
    '$api': false
  }
}
