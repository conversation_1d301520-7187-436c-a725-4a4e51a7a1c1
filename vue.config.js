// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const webpack = require('webpack')
const path = require('path')
// 引入删除注释插件
const terserPlugin = require('terser-webpack-plugin')
// 引入 gzip 插件
const compressionWebpackPlugin = require('compression-webpack-plugin')
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin')
// 判断是否为生产环境或预发布环境
const needMinimizer = (process.env.VUE_APP_CURRENTMODE).toLocaleUpperCase() === 'PROD' || (process.env.VUE_APP_CURRENTMODE).toLocaleUpperCase() === 'STAGE'
module.exports = {
  chainWebpack: config => {
    config.module
      .rule('thejs')
      .test(/\.js$/)
      .include
        .add(path.resolve('src'))
        .add(path.resolve('node_modules/element-ui/packages'))
        .end()
      .use('babel-loader')
        .loader('babel-loader')
        .end()
    config.plugins.delete('prefetch'); // 删除 preload 配置
    config.plugins.delete('preload'); // 删除 prefetch 配置
    
    // 添加简单的基本优化
    if (process.env.NODE_ENV === 'production') {
      // 生产环境优化
      config.optimization.minimize(true);
      config.optimization.usedExports(true);
    }
    // 取消文件名中的 Hash，以参数形式控制版本
    const version = '?h=' + '[hash]'
    config.output.filename('js/[name].js' + version).end()
    config.output.chunkFilename('js/[name].js' + version).end()
    config.plugin('extract-css').tap(args => [{
      filename: 'static/css/[name].css' + version,
      chunkFilename: 'static/css/[name].css' + version
    }])
  },
  publicPath: './',
  outputDir: 'v2',
  lintOnSave: true, // 语法检查
  assetsDir: 'static',
  runtimeCompiler: true,
  productionSourceMap: false, // production 环境源码映射
  css: {
    extract: true, // CSS 文件分离
    sourceMap: true // CSS 源码映射
  },
  pluginOptions: {
    i18n: {
      locale: 'zh-CN',
      fallbackLocale: 'en-US',
      localeDir: 'locales',
      enableInSFC: true
    }
  },
  configureWebpack: config => {
    var myConfig = null
    if (process.env.NODE_ENV === 'production') {
      // 为生产环境修改配置...
      myConfig = {
        optimization: {         
          splitChunks: {
            chunks: 'all', // 针对所有的代码块（同步和异步）
            minSize: 500 * 1024, // 降低单个包体积门槛为500KB，更好的拆分
            maxSize: 1000 * 1024, // 最大体积限制为1000KB
            minChunks: 1, // 代码块最少被引用 1 次
            maxAsyncRequests: 15, // 提高异步请求上限，允许更细粒度拆分
            maxInitialRequests: 10, // 提高入口请求上限
            automaticNameDelimiter: '~', // 文件名称分隔符
            cacheGroups: {
             
              elementUI: {
                name: 'chunk-elementUI',
                priority: 28, // 最高优先级
                test: /[\\/]node_modules[\\/]element-ui[\\/]/,
                chunks: 'all'
              },
              // vue-easy-tree 单独分包
              vueEasyTree: {
                name: 'chunk-vue-easy-tree',
                test: /[\\/]node_modules[\\/]@wchbrad[\\/]vue-easy-tree[\\/]/,
                priority: 27, // 高优先级，确保单独分包
                chunks: 'async', // 异步加载，只有使用时才加载
                reuseExistingChunk: true
              },
              quill: {
                name: 'chunk-quill', // quill 单独拆包
                test: /[\\/]node_modules[\\/](quill|vue-quill-editor)[\\/]/,
                priority: 26, // 调整优先级
                chunks: 'async', // 异步拆包（quill 主要在特定功能页面使用，首页不需要）
                reuseExistingChunk: true
              },
              echarts: {
                name: 'chunk-echarts', // echarts单独拆包
                test: /[\\/]node_modules[\\/]echarts[\\/]/,
                priority: 25, // 调整优先级
                chunks: 'all'
              },               
              // async: {
              //   name: 'chunk-async',
              //   test: /[\\/]node_modules[\\/](?!element-ui)(?!echarts)/,
              //   chunks: 'async',
              //   priority: 20, // 提高优先级，确保异步模块优先进入async组
              //   reuseExistingChunk: true
              // },
              libs: {
                name: 'chunk-libs', // 只打包初始时依赖的第三方
                test: /[\\/]node_modules[\\/](?!element-ui)(?!echarts)/, // 排除已单独分离的库
                priority: 15, // 提高优先级
                chunks: 'initial'
              },
              locales: {
                name: 'chunk-locales', // locales 拆包
                test: /[\\/]src[\\/]locales[\\/]/,
                priority: 20,
                chunks: 'all'
              },
              commons: {// 'src/utils' 下的js文件
                name: 'chunk-commons',
                test: /[\\/]src[\\/]utils[\\/]/,
                minChunks: 2, // 最小共用次数
                priority: 10, // 提高优先级，避免被打包到app中
                reuseExistingChunk: true,
                chunks: 'all'
              },            
              styles: {
                name: 'styles',
                test: /\.(css|scss|less)$/,
                chunks: 'all',
                enforce: true,
                priority: 15
              }
            },
          },
          runtimeChunk: 'single',
          minimizer: [
            new terserPlugin({
              terserOptions: {
                compress: {
                  drop_console: needMinimizer, // 移除 console.log
                  drop_debugger: needMinimizer // 移除 debugger
                },
                output: {
                  comments: false, // 移除注释
                }
              },
              parallel: true // 支持多线程      
            }),
            new OptimizeCSSAssetsPlugin({})
          ]
        },
        plugins: [
          // new BundleAnalyzerPlugin({
          //   openAnalyzer: false,	          
          //   analyzerPort: 8001
          // }),
          new compressionWebpackPlugin({
            filename: '[path].gz[query]', // 输出的 gzip 文件名
            algorithm: 'gzip', // 使用 gzip 压缩
            test: /\.(js|css|json|html)$/, // 压缩 js, css, json, html 文件
            threshold: 10240, // 只有大小大于 10KB 的文件才会被压缩
            minRatio: 0.8, // 只有压缩率小于 0.8 的文件才会被压缩
            deleteOriginalAssets: false // 是否删除原始文件，建议为 false
          }),
          new webpack.ProvidePlugin({
            jQuery: 'jquery',
            $: 'jquery',
            'window.Quill': 'quill/dist/quill.js',
            'Quill': 'quill/dist/quill.js'
          }),
          new webpack.IgnorePlugin({
            resourceRegExp: /^\.\/locale$/,
            contextRegExp: /moment$/
          })
        ],
        externals: {
          'jquery': 'jQuery',
          'bootstrap': 'bootstrap'
        },
        // 性能提示
        performance: {
          hints: 'warning',
          maxAssetSize: 1024 * 1024, 
          maxEntrypointSize: 2 * 1024 * 1024
        }
      }
    } else {
      // 为开发环境修改配置...
      myConfig = {
        // 更好的开发环境源映射
        // devtool: 'eval-cheap-module-source-map',
        plugins: [
          new webpack.ProvidePlugin({
            jQuery: 'jquery',
            $: 'jquery',
            'window.Quill': 'quill/dist/quill.js',
            'Quill': 'quill/dist/quill.js'
          }),
          new webpack.IgnorePlugin({
            resourceRegExp: /^\.\/locale$/,
            contextRegExp: /moment$/
          })
        ]
      }
    }
    return myConfig
  }
}

