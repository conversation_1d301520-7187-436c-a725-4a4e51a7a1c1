// 插件名称缩写
const NAME_ABBR = 'CG'

/**
 * 元素事件名称，避免重复，都使用插件缩写开头
 */
export const EventName = {
  GOOGLE_DOC_REQ: `${NAME_ABBR}_GOOGLE_DOC_REQ`, // 插件向页面发送 Google Doc 相关请求事件
  GOOGLE_DOC_RES: `${NAME_ABBR}_GOOGLE_DOC_RES`, // 页面向插件发送 Google Doc 相关响应事件
  UPDATE_SESSION_REQ: `${NAME_ABBR}_UPDATE_SESSION_REQ`, // 插件向页面发送更新会话状态请求事件
  UPDATE_SESSION_RES: `${NAME_ABBR}_UPDATE_SESSION_RES`, // 页面向插件发送更新会话状态响应事件
  SYNC_SESSION_REQ: `${NAME_ABBR}_SYNC_SESSION_REQ`, // 插件向页面发送同步会话状态请求事件
  SYNC_SESSION_RES: `${NAME_ABBR}_SYNC_SESSION_RES`, // 页面向插件发送同步会话状态响应事件
  OPEN_INVITATION_DIALOG: `${NAME_ABBR}_OPEN_INVITATION_DIALOG`, // 打开邀请弹窗
  SHOW_INSTALL_GUIDE_REQ: `${NAME_ABBR}_SHOW_INSTALL_GUIDE_REQ`, // 页面向插件发送展示提示事件
}

/**
 * 根据环境增加事件后缀，避免不同环境之间的事件冲突
 * @param eventName 事件名称
 * @param env 环境
 * @returns {string} 增加后缀的事件名称
 */
export const addEnvSuffix = (eventName, env) => {
  if (!env) {
    return eventName
  }
  return env.toUpperCase() === 'PROD' ? eventName : eventName + '_' + env.toUpperCase()
}
