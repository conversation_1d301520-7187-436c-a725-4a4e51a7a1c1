<template>
  <div id="app" class="app" :class="{'app-header-fixed app-aside-fixed': (!$store.getters.viewSource || $store.getters.viewSource !== 'H5') && !isInKindDonate && v1}">
    <transition name="el-fade-in">
      <router-view class="h-full" :loginLoading="loading"/>
    </transition>
    <LgHelp v-if="!isChina" :currentUser="currentUser" :ishowHelpChat="ishowHelpChat" />
    <LgLoading/>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import LgLoading from '@/components/LgLoading'
import LgHelp from '@/components/LgHelp'
import tools from '@/utils/tools'
import { setLocale } from '@/utils/i18n'
import { platform } from '@/utils/setBaseUrl'
import { isAuthenticated } from '@/utils/common'
import { LESSON_PLAN_NEW_USER_UTC, LOOP_TIME, SESSION_LOCALSTRAGE_KEYS } from './utils/const'
import { renewalSessionTime, toSignOut } from './utils/autoSignOut'
import { addEnvSuffix, EventName } from '@/constants/event'
import { useAuthApi } from '@/api/cg/auth'
import { setPlatform } from '@/utils/setBaseUrl'
import routePreloader from '@/utils/routePreloader'

export default {
  name: 'app',
  components: {
    LgLoading,
    LgHelp
  },
  data() {
      return {
          timerId: null,
          env: process.env.VUE_APP_CURRENTMODE,
          loading: false,
          metaData: [] // 添加 meta 数据数组
      }
  },
  mounted () {
    // 增加对 storage 的监听
    window.addEventListener('storage', this.handleStorageChange)
  },
  beforeDestroy () {
    // 移除对 storage 的监听
    window.removeEventListener('storage', this.handleStorageChange)
  },
  created: function () {   
    // 初始化 Datadog RUM
    this.$analytics.initDatadogRum()
    this.checkPluginInstalled()
    // this.setAuthHeader()
    this.$store.dispatch('cgAuth/initAuthInfo')
    // 初始化 meta 数据
    this.initMetaData()
  },
  mounted () {    
    // 监听屏幕大小变化
    this.checkScreenSize(); // 初始化检查
    window.addEventListener('resize', this.checkScreenSize); // 监听窗口大小变化
    this.syncSessionInfo()
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.checkScreenSize);
  },
  computed: {    
    ...mapState({
      currentUser: state => state.user.currentUser,
      ishowHelpChat: state => state.chat.ishowHelpChat,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      cgAuth: state => state.cgAuth,
      cgCurrentUser: state => state.cgAuth.user
    }),
    isChina () {
      return this.$store.getters.isChina
    },
    // 是否是捐赠页面
    isInKindDonate () {
      return this.$route.path.indexOf('inKindSubmission') > -1
    },
    // 旧版页面（家长端）
    v1 () {
      return this.$route.meta && this.$route.meta.v1
    },
    // 判断是否显式 help 按钮
    notShowHelpBtn () {
      return this.$route.path.indexOf('in-kind-grants-step1') > -1 || this.$route.path.indexOf('in-kind-grants-step2') > -1 ||
        this.$route.path.indexOf('in-kind-grants-editor') > -1 || this.$route.path.indexOf('in-kind-goals-editor') > -1
    }
  },
  methods: {
    ...mapActions('cgAuth', ['getEventNotifyAction', 'clearAuth', 'setHasPlugin', 'setAuthInfo',  'isSessionLatest', 'getSessionUpdateTime']),
    /**
     * 同步认证信息
     */
    syncSessionInfo() {
      window.addEventListener(addEnvSuffix(EventName.UPDATE_SESSION_RES, this.env), async (event) => {      
        // 标记为安装了插件
        this.setHasPlugin(true)
        // 接收到的事件
        const customEvent = event
        // 事件详情
        const detail = customEvent.detail
        // Token
        const token = detail.token
        // 插件用户更新时间戳
        const pluginSessionUpdateTime = detail.sessionUpdateTime
        // // 同步状态
        // await this.setAuthInfo({ access_token: token, session_update_time: pluginSessionUpdateTime, sync: false })
        
        if (token) {
          this.loading = true
          await this.setAuthInfo({ access_token: token, session_update_time: pluginSessionUpdateTime, sync: false })        
          // 如果是登录页则跳转到首页
          if (this.$route.name === 'cg-login') {
            // 如果当前时间大于 LESSON_PLAN_NEW_USER_UTC，则跳转到 Lesson Plan 页面
            if (this.cgCurrentUser && tools.timeIsAfter(this.cgCurrentUser.created_at_utc, LESSON_PLAN_NEW_USER_UTC)) {
              this.$router.push('/lessons')
            } else {
              this.$router.push('/curriculum-genie/unit-planner')
            }
          }
          this.loading = false
        } else {
          // 同步状态
          await this.setAuthInfo({ access_token: '', session_update_time: pluginSessionUpdateTime, sync: false })          
          toSignOut()
        }
      })
      // 监听插件认证状态事件
      window.addEventListener(addEnvSuffix(EventName.SYNC_SESSION_RES, this.env), async (event) => {        
        // 标记为安装了插件
        this.setHasPlugin(true)
        // 接收到的事件
        const customEvent = event
        // 事件详情
        const detail = customEvent.detail
        // Token
        const token = detail.token
        // 插件用户更新时间戳
        const pluginSessionUpdateTime = detail.sessionUpdateTime
        // 网页当前用户 Token
        const currentUserToken = isAuthenticated() ? tools.getCookie('cg_session') : ''
        // 网页当前会话更新时间
        const currentSessionUpdateTime = await this.getSessionUpdateTime()
        
        // 插件和网页都未登录，不用同步
        if (!token && !currentUserToken) {
          return
        }
        const isLatest = await this.isSessionLatest(pluginSessionUpdateTime)
        // 插件未登录，网页已登录
        if (!token && currentUserToken) {          
          // 如果插件会话更新时间为空，或者网页会话时间是最新的，则同步登录状态到插件
          if (!pluginSessionUpdateTime || isLatest) {
            const event = new CustomEvent(addEnvSuffix(EventName.UPDATE_SESSION_REQ, this.env), { detail: { token: currentUserToken, sessionUpdateTime: currentSessionUpdateTime } })
            window.dispatchEvent(event)
            return
          } else {
            // 否则退出网页登录
            await this.clearAuth(undefined, false)                        
            toSignOut()
            return
          }
        }
        // 插件已登录，网页未登录
        if (token && !currentUserToken) {          
          // 如果网页用户信息不是最新的，则更新用户信息
          if (!isLatest) {
            this.loading = true
            // 更新认证信息
            await this.setAuthInfo({ access_token: token, session_update_time: pluginSessionUpdateTime, sync: false, updateAuth: true })
            // 如果当前时间大于 LESSON_PLAN_NEW_USER_UTC，则跳转到 Lesson Plan 页面
            if (this.cgCurrentUser && tools.timeIsAfter(this.cgCurrentUser.created_at_utc, LESSON_PLAN_NEW_USER_UTC)) {
              this.$router.push('/lessons')
            } else {
              this.$router.push('/curriculum-genie/unit-planner')
            }
            this.loading = false
            return
          } else {
            // 如果网页用户信息是最新的，则退出插件登录
            const event = new CustomEvent(addEnvSuffix(EventName.UPDATE_SESSION_REQ, this.env), { detail: { token: '', sessionUpdateTime: currentSessionUpdateTime } })
            window.dispatchEvent(event)
            return
          }
        }
        // 双方都登录，但 Token 不同
        if (token && currentUserToken && token !== currentUserToken) {
          // 如果网页登录状态是最新的，则同步到插件
          if (!pluginSessionUpdateTime || isLatest) {
            const event = new CustomEvent(addEnvSuffix(EventName.UPDATE_SESSION_REQ, this.env), { detail: { token: currentUserToken, sessionUpdateTime: currentSessionUpdateTime } })
            window.dispatchEvent(event)
            return
          } else {
            // 否则网页同步为插件登录状态
            await this.setAuthInfo({ access_token: token, session_update_time: pluginSessionUpdateTime, sync: false, updateAuth: true })
            return
          }
        }
      })
    },
    /**
     * 检测插件是否安装
     */
    checkPluginInstalled() {
      // 初始检查
      this.$nextTick(() => {
        const hasPlugin = document && !!document.getElementById('cg-shadow-root')
        this.$store.dispatch('cgAuth/setHasPlugin', hasPlugin)
        // 记录插件状态变化
        if (typeof localStorage !== 'undefined') {
          const prevPluginState = localStorage.getItem('cg_plugin_installed') === 'true';

          // 如果之前有插件，现在没有了，可能是卸载了插件
          if (prevPluginState && !hasPlugin) {
            localStorage.setItem('cg_plugin_installed', 'false');
          }
          // 如果之前没有插件，现在有了，是新安装了插件
          else if (!prevPluginState && hasPlugin) {
            localStorage.setItem('cg_plugin_installed', 'true');
            // 记录新的安装时间，用于 Banner 显示逻辑
            localStorage.setItem('cg_extension_install_time', Date.now().toString());
          }
          // 首次检测
          else if (localStorage.getItem('cg_plugin_installed') === null) {
            localStorage.setItem('cg_plugin_installed', hasPlugin ? 'true' : 'false');
            if (hasPlugin) {
              localStorage.setItem('cg_extension_install_time', Date.now().toString());
            }
          }
        }

        // 如果已经检测到插件，不需要继续观察
        if (hasPlugin) {
          return
        }

        // 创建 MutationObserver 实例
        const observer = new MutationObserver((mutations) => {
          const hasPlugin = document && !!document.getElementById('cg-shadow-root')
          this.$store.dispatch('cgAuth/setHasPlugin', hasPlugin)

          // 记录插件状态变化
          if (typeof localStorage !== 'undefined' && hasPlugin) {
            const prevPluginState = localStorage.getItem('cg_plugin_installed') === 'true';
            if (!prevPluginState) {
              localStorage.setItem('cg_plugin_installed', 'true');
              localStorage.setItem('cg_extension_install_time', Date.now().toString());
            }
          }

          // 如果检测到插件，断开观察器
          if (hasPlugin) {
            observer.disconnect()
          }
        })

        // 配置 observer
        const config = {
          childList: true,
          subtree: true
        }

        // 开始观察 document.body
        if (document && document.body) {
          observer.observe(document.body, config)
        }
      })
    },
    // 检测当前屏幕大小
    checkScreenSize() {
      let isSmallScreen = window.innerWidth <= 767;
      this.$store.commit('SET_LG_IS_MOBILE', isSmallScreen)
    },
    /**
     * 检查会话数据是否过期
     * 如果会话过期，则删除本地存储中的相关数据
     * 如果用户勾选了 "保持登录状态"，但该状态也过期，则同样删除相关数据
     */
    checkSessionIsExpired() {
        // 获取当前时间戳
        const currentTime = Date.now(); 
        // 从 localStorage 获取 sessionExpireTime 和 currentUser
        const sessionExpireTime = localStorage.getItem('sessionExpireTime') || 0;
        const loginExpireTime = localStorage.getItem('loginExpireTime') || 0;
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        // 过期删除指定字段
        if (sessionExpireTime && sessionExpireTime < currentTime) {
            // 删除与 session 相关的数据
            let keysToRemove = SESSION_LOCALSTRAGE_KEYS;
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            // 如果用户未勾选 "保持登录状态"，删除 currentUser 和 currentUserInfo
            if (!currentUser.rememberMe) {
              toSignOut()
            }
        }
        // 如果勾选了保持登录状态，检查登录过期
        if (currentUser.rememberMe && loginExpireTime && loginExpireTime < currentTime) {
            toSignOut()
        }
    },

    // // 初始化设置认证信息
    // setAuthHeader () {
    //   let uid = this.$route.query.uid
    //   let token = this.$route.query.t
    //   let language = this.$route.query.lang
    //   let env = this.$route.query.env
    //   let source = this.$route.query.source
    //   // 判断是否是以 H5 方式打开，如果是则存储到 sessionStorage 中
    //   if (source == 'H5') {
    //     sessionStorage.setItem('currentViewSource', 'H5')
    //   } else {
    //     source = sessionStorage.getItem('currentViewSource')
    //   }
    //   this.$store.commit('SET_VIEW_SOURCE', source)
    //   let userrole = this.$route.query.role
    //   // 非 H5 页面不处理
    //   if (!source || source !== 'H5') {
    //     return
    //   }
    //   if (!language) {
    //     language = 'en-US'
    //   }
    //   if (language == 'en') {
    //     language = 'en-US'
    //   }
    //   if (language == 'zh') {
    //     language = 'zh-CN'
    //   }
    //   if (language == 'es') {
    //     language = 'es-ES'
    //   }
    //   setLocale(language)
    //   let auth = {
    //     language: language,
    //     token: token,
    //     env: env,
    //     source: source,
    //     role2: userrole,
    //     role: this.getCompatibleRole(userrole),
    //     type: 'Normal',
    //     user_id: uid,
    //     userInfo: {
    //       id: uid,
    //       role: userrole,
    //       avatarUrl: null,
    //       displayName: null,
    //       email: null,
    //       lastName: null,
    //       phoneNumber: null
    //     }
    //   }
    //   this.$store.commit('SET_TOKEN', token)
    //   this.$store.commit('SET_UID', uid)
    //   // 存储用户信息
    //   this.$store.dispatch('setCurrentUserAction', auth)
    //   // 用户是否退出
    //   tools.sessionItem('USER_SIGN_OUT', false)
    // },

    getCompatibleRole (role) {
      role = role.toUpperCase()
      if (role == 'AGENCY_ADMIN' || role == 'AGENCY_OWNER' || role == 'SITE_ADMIN') {
        return 'OWNER'
      } else if (role == 'COLLABORATOR' || role == 'TEACHING_ASSISTANT' || role == 'FAMILY_SERVICE') {
        return 'COLLABORATOR'
      }
    },
    /**
     * 处理 storage 发生改变的方法
     * @param event 事件对象
     */
    handleStorageChange (event) {
      // 当 storage 中 reload 键值发生变化时刷新页面
      if (event.key === 'sessionChanged') {
        window.location.href = '/'
      }
    },
    /**
     * 初始化 meta 数据
     */
    initMetaData() {
      // 从 hash 中获取查询参数
      const hash = window.location.hash;
      const queryString = hash.split('?')[1];
      const urlParams = new URLSearchParams(queryString || '');
      const title = this.$route.query.title || urlParams.get('title');
      const description = this.$route.query.description || urlParams.get('description');
      let image = this.$route.query.image || urlParams.get('image');
      const siteName = "Learning Genie"

      // 对于图片，如果它不是完整的 URL，则添加前缀
      if (image) {
        // 先进行解码
        const decodedImage = decodeURIComponent(image)
        if (!/^https?:\/\//.test(decodedImage)) {
          image = `https://s3.amazonaws.com//com.learning-genie.prod.us/${image}`
        } else {
          image = decodedImage
        }
      }

      // 如果存在用户自定义元数据，则使用它
      if (title && description && image) {
        this.metaData = [
          { property: 'og:type', content: 'website' },
          { property: 'og:title', content: title },
          { property: 'og:description', content: description },
          { property: 'og:image', content: image },
          { property: 'og:site_name', content: siteName },
          { name: 'twitter:card', content: 'summary_large_image' },
          { name: 'twitter:site', content: siteName },
          { name: 'twitter:title', content: title },
          { name: 'twitter:description', content: description },
          { name: 'twitter:image', content: image },
        ]
        this.updateMetaTags(this.metaData)
      }
    },

    /**
     * 更新 DOM 中的 meta 标签
     * @param {Array} metas - meta 数据数组
     */
    updateMetaTags(metas) {
      if (typeof document === 'undefined') return

      metas.forEach(meta => {
        // 根据 property 或 name 属性查找已存在的 meta 标签
        const selector = meta.property ? `meta[property="${meta.property}"]` : `meta[name="${meta.name}"]`
        let metaTag = document.querySelector(selector)

        if (metaTag) {
          // 如果标签已存在，更新其 content
          metaTag.setAttribute('content', meta.content)
        } else {
          // 如果标签不存在，创建新的 meta 标签
          metaTag = document.createElement('meta')
          if (meta.property) {
            metaTag.setAttribute('property', meta.property)
          } else {
            metaTag.setAttribute('name', meta.name)
          }
          metaTag.setAttribute('content', meta.content)
          document.head.appendChild(metaTag)
        }
      })
    },
  },
  watch: {
    currentUser: {
      handler (user) {
        if (user) {
          // 设置数据统计用户信息
          this.$analytics.setUserProfile(user)
          // 设置 PostHog 用户信息
          this.$analytics.initPostHog(user)
          // 设置 Datadog RUM 用户信息
          this.$analytics.setDatadogRumUser(user)
        }
      },
      immediate: true
    },
    
    // 监听路由变化，预加载相关组件
    '$route': {
      handler(to, from) {
        // 只有路由名称发生变化时才预加载
        if (to.name && to.name !== (from && from.name)) {
          routePreloader.preloadByRoute(to.name)
        }
      },
      immediate: false // 不在组件初始化时触发
    },
    
    // 监听是否显式 help 按钮
    notShowHelpBtn (newValue) {
      if (newValue) {
        // 隐藏 help 按钮 和 叉号
        if (window.zE && window.zE.show) {
          window.zE.hide()
          let mobile = document.getElementById('mobile')
          if (mobile) {
            mobile.style.display = 'none'
          }
        }
      } else {
       if (window.zE && window.zE.hide) {
         // 显式 help 按钮 和 叉号
         window.zE.show()
         let mobile = document.getElementById('mobile')
         if (mobile) {
           mobile.style.display = 'block'
           mobile.innerHTML = '<i class="fa fa-times" aria-hidden="true"></i>'
           mobile.style.marginTop = '0px'
           mobile.style.paddingTop = '0px';
           mobile.style.marginRight = '0px'
           mobile.style.color = '#484848'
         }
       }
      }
    }
  }
}
</script>

<style lang="less" scope>
#app{
  overflow: hidden;
}

.el-scrollbar .el-scrollbar__bar {
  opacity: 1 !important;
}
</style>
