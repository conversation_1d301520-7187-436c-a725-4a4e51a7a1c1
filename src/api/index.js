let _api = {
    /* eslint-disable */
    urls: function (userId, centerId, typeId, groupId, enrollmentId, eventId) {
        return {
            // getSurveyDot: `/forms/getStaffFormFillRemind`, // 获取问卷提醒
            releasenote: `/messages/user/unread/releasenote`, // 获取通知信息
            loginTime: `/users/loginTime`, // 上次登录时间
            msgTypes: `/messages/types`, // 没有读取的通知
            imTranslationOpen: `/users/${userId}/imTranslationOpen`, // 获取是否char开启翻译
            translate: `/notes/translate`, // 翻译
            login: `/account/login`, // 登录
            language: `/users/language`, // 设置当前用户语言
            logout: `/account/logout`, // 用户退出
            alias: `/app/child/alias`, // 获取评分周期
            items: `/inkinds/centers/${centerId}/items`, // 学校的活动项目列表
            rename: `/inkinds/types/${typeId}/rename/`, // 更新活动分类名称
            //上传图片
            uploadFile: `/medias/fileUpload`, // 上传文件
            createMediaWithThumbnail: `/medias/createMediaWithThumbnail`, // 生成缩略图
            remindParent: `/inkinds/remindParent`, // 提醒家长
            // point: `/inkinds/educator/point`, // 获取inKind红点接口
            centersAndGroups: `/users/${userId}/getCenterGroupByUserId`, // 获取所有学校
            eventNotify: `/events/notify`, // event未读红点标识
            // dashboard 功能接口
            surveyStats: '/forms/getDashboardStatistics',
            registerStats: `/dashboard/getRegisterStats`, // 获取注册统计数据
            notificationStats: `/dashboard/getNotificationStats`, // 获取学校通知统计数据
            activeStats: `/dashboard/getActiveStats`, // 获取家长活跃度统计数据
            chatStats: `/dashboard/getChatStats`, // 获取聊天统计数据
            eventStats: `/dashboard/getEventStats`, // 获取 Event 统计数据
            engageStats: `/dashboard/getEngageStats`,// 获取 engagement 统计数据
            bookStats: `/dashboard/getBookStats`, // 获取 Book 统计数据
            mediaStats: `/dashboard/getMediaStats`, // 获取 Learning Media 统计数据
            pdfList: `/reports/pdf`,
            adAutoOpenSwitch: `/medias/adAutoOpenSwitch`, // 关闭下次不在自动弹出
            sendTryRemindEmail: `/medias/sendTryOutRemindEmail`, // 向客服发送邮件
            approvalReadTip: `/app/notify`, // approval queue导航栏红点
            // in-kind2.0 接口
            createSource: `/inkinds/createSource`, // 添加 Scource
            manageChildren: `/students`, // 获取管理学生页面数据
            // 评分周期
            snippet: `/domains/snippet`, // 获取评分框架列表
            periodGroupNotify: `/periods/periodGroupNotify`, // 添加新学年提示
            notification: `/notification`, // 轮询接口
            setLessonNotificationStatus: `/notification/setLessonNotificationStatus`, //修改通知消息状态
            setFileNotificationStatusByBatchId: `/notification/setFileNotificationStatusByBatchId`, //修改通知消息状态
            // 家长端
            updateDisplayName: `/users/updateDisplayName`,// 修改昵称

            // listAgencyTips: '/agencies/listAgencyTips',
            setAgencyTips: '/agencies/setAgencyTips',


            // 小孩信息
            getEnrollmentAttrs: `/students/attrs`,
            // channel
            getCenterIdNameByUserId: `/users/${userId}/getCenterIdNameByUserId`,
            getCenterById: `/centers/${centerId}`,
            getGrantStatisticsData : '/inkinds/getGrantStatisticsData', // 获取 In-Kind Grant 列表
            getGoalValueStatistics : '/inkinds/goals/getGoalValueStatistics', // 获取 Grant 捐赠金额
            // 新版drdp整合
            sendHelpEmail: '/reports/sendHelpEmail',
            // DRDP Report
            getCohortViewList: '/reports/cohort/getViewList',
            // key measures
            // learning Media Channel List
            // prompts
            createRecoverOrUpdatePrompt: '/prompts/createRecoverOrUpdatePrompt',  // 创建或者恢复更新 prompt
            getPrompt: '/prompts/getPrompt', // 根据 ID 获取 Prompt
            // getCustomizePrompt: '/prompts/getCustomizePrompt', // 查询所有自定义的 prompt
            getCustomizePromptContent: '/prompts/getCustomizePromptContent', // 获取用户自定义 Prompt 生成的内容
            saveCustomizePromptContent: '/prompts/saveCustomizePromptContent', // 保存用户自定义 Prompt 生成的内容
            getPromptByScene: '/prompts/getDefaultPrompt', // 根据场景获取 Prompt
            createPromptVersion: '/prompts/createPromptVersion', // 创建 Prompt 版本
            listPromptsByScene: '/prompts/listPromptsByScene', // 根据场景获取 Prompt 列表
            setUserCurrentPrompt: '/prompts/setUserCurrentPrompt', // 设置用户当前 Prompt
            setUserBestPrompt: '/prompts/setUserBestPrompt', // 设置用户最佳 Prompt
            setSystemCurrentPrompt: '/prompts/setSystemCurrentPrompt',// 设置系统当前 Prompt
            getComparisonInfo: '/prompts/getComparisonInfo', // 获取 Prompt 对比信息
            deletePrompt: '/prompts/deletePrompt', // 删除当前Prompt
            listPromptTestRecords: '/prompts/listPromptTestRecords', // 获取测试 Prompt 记录
            getFeedback: '/prompts/getFeedback', // 获取用户反馈
            createFeedBack: '/prompts/createFeedback', // 创建用户反馈
            // lessons2
            createUnit: '/curriculums/createUnit', // 创建单元
            createPluginUnit: '/curriculums/createPluginUnit', // 创建单元
            generateContentStream: '/curriculums/generateContentStream', // 给定 prompt Id 进行给定的内容生成
            updateCurriculumUnit: '/curriculums/updateUnit', // 更新单元
            updateUnitLessonTemplateInfo: '/curriculums/updateUnitLessonTemplateInfo', // 更新单元使用课程模板信息
            renameUnit: '/curriculums/renameUnit', // 重命名单元
            createUnitPromptHistory: '/curriculums/createUnitPromptHistory', // 创建单元 prompt 历史
            deleteUnitPromptHistory: '/curriculums/deleteUnitPromptHistory', // 删除单元 prompt 历史
            getUnitPromptHistory: '/curriculums/getUnitPromptHistory', // 获取单元 prompt 历史
            createCreateLessonPromptHistory: '/curriculums/createCreateLessonPromptHistory', // 创建创建课程的 prompt 历史记录
            createAdaptLessonPromptHistory: '/curriculums/createAdaptLessonPromptHistory', // 创建改编课程的 prompt 历史记录
            getCreateLessonPromptHistory: '/curriculums/getCreateLessonPromptHistory', // 获取创建课程的 prompt 历史记录
            getAdaptLessonPromptHistory: '/curriculums/getAdaptLessonPromptHistory', // 获取改编课程的 prompt 历史记录
            deleteCreateLessonPromptHistory: '/curriculums/deleteCreateLessonPromptHistory', // 删除创建课程的 prompt 历史记录
            deleteAdaptLessonPromptHistory: '/curriculums/deleteAdaptLessonPromptHistory', // 删除改编课程的 prompt 历史记录
            unitPlanerStep1RecommendMeasure: '/curriculums/unitPlanerStep1RecommendMeasure', // unit planner step1 推荐测评点
            createWeeklyPlans: '/curriculums/createWeekPlans', // 创建周计划
            updateWeeklyPlans: '/curriculums/updateWeekPlans', // 更新周计划
            createPlanItems: '/curriculums/createPlanItems', // 创建计划项
            createLessonOverviewSecondAllocation: '/curriculums/createLessonOverviewSecondAllocation', // 创建课程概览第二次分配
            updatePlanItems: '/curriculums/updatePlanItems', // 更新计划项
            distributeCornerActivities: '/curriculums/distributeCornerActivities', // 分配区角周活动到课程周
            getOriginalCornerData: '/curriculums/getOriginalCornerData', // 根据单元 ID 查询区角周的原始数据
            evaluationLessonIdeas: '/curriculums/evaluationLessonIdeas', // 评估课程想法
            evaluationLessonDetail: '/curriculums/evaluationLessonDetail', // 评估课程详情
            updatePlanItemsInfo: '/curriculums/updatePlanItemsInfo', // 更新周计划 items 基础信息
            checkLessonContainBooks: '/lessons2/lessons/checkLessonContainBooks', // 检查课程是否包含书籍
            recommendLessonTemplate: '/curriculums/recommendLessonTemplate', // 推荐课程模板
            createLessonGoogleSlide: 'lessons2/lessons/createLessonGoogleSlide', // 创建课程模板 google slide 数据
            updateLessonGoogleSlideMetadata: 'lessons2/lessons/updateLessonGoogleSlideMetadata', // 更新课程模板 google slide 数据
            getLessonTemplates: '/lessons2/lessons/getLessonTemplates', // 获取课程模板
            deleteLessonTemplate: '/lessons2/lessons/deleteLessonTemplate', // 删除课程模板
            createLessonSlides: '/lessons2/lessons/createLessonSlides', // 创建课程幻灯片
            updateLessonSlides: '/lessons2/lessons/updateLessonSlides', // 更新课程幻灯片
            generateLessonSlides: '/lessons2/lessons/generateLessonSlides', // 生成课程幻灯片
            saveLessonSlidesToDrive: '/lessons2/lessons/saveLessonSlidesToDrive', // 保存课程幻灯片到用户 Google Drive
            deleteTempLessonSlides: '/lessons2/lessons/deleteTempLessonSlides', // 删除临时课程幻灯片
            uploadLessonSlides: '/lessons2/lessons/uploadLessonSlides', // 上传课程 slides
            getLessonSlides: '/lessons2/lessons/getLessonSlides', // 获取课程 slides
            generateLessonSlidesStream: '/curriculums/generateLessonSlidesStream', // 生成课程 slides
            createLesson: '/curriculums/createLesson', // 创建课程
            updateLesson: '/curriculums/updateLesson', // 更新课程
            updateUnitItem: '/curriculums/updateUnitPlanItem', // 更新单元下的周计划项
            getLessonMediaModel: '/lessons2/plans/getLessonMediaModel', // 更新单元下的周计划项
            getUserNewCustomFoundationModules:  '/curriculums/getUserNewCustomFoundationModules', // 获取用户新的自定义基础模块
            createGenerateLessonTasks: '/lessons2/lessons/createGenerateLessonTasks', // 创建批量生成课程任务
            getBatchGenerateLessonTasks: '/lessons2/lessons/getBatchGenerateLessonTasks', // 查询批量生成课程任务
            generateLessonDetail: '/lessons2/lessons/generateLessonDetail', // 生成课程详情
            batchGenerateLesson: '/lessons2/lessons/batchGenerateLesson', // 重新执行生成失败任务
            getUnitGeneratingLessonTask: '/lessons2/lessons/getUnitGeneratingLessonTask', // 获取单元生成课程任务
            stopGenerateTasks: '/lessons2/lessons/stopGenerateLessonTasks', // 停止生成任务
            createUserActivityRecord: '/curriculums/createUserActivityRecord', // 用户活动记录
            getUnit: '/curriculums/getUnit', // 获取单元
            getUnitNavigationData: '/curriculums/getUnitNavigationData', // 获取单元导航数据
            addCornerItem: '/curriculums/addCornerItem', // 向课程周添加指定区角活动项
            removeCornerItem: '/curriculums/removeCornerItem', // 向课程周移除指定区角活动项
            switchCornerItem: '/curriculums/switchCornerItem', // 切换课程周活动项
            getUnitInfo: '/curriculums/getUnitDetail', // 获取单元信息
            getUnitResources: '/curriculum/resources/getUnitResources', // 获取单元资源
            downloadMaterials: '/curriculum/resources/downloadMaterials', // 下载材料
            exportVocabularies: '/curriculum/resources/exportVocabularies', // 导出单元词汇
            createDownloadQuizTask: '/curriculum/resources/createDownloadQuizTask', // 下载 Quiz 文件
            getDownloadQuizTask: '/curriculum/resources/getDownloadQuizTask', // 获取下载 Quiz 文件任务
            getUnitEditStatus: '/curriculums/getUnitEditStatus', // 获取单元编辑状态
            unlockUnit: '/curriculums/unlockUnit', // 解锁单元
            getPlanThemeAndOverview: '/curriculums/getPlanThemeAndOverview', // 获取计划主题和概览
            extractBooksFromUnitPlanOverviews: '/curriculums/extractBooksFromUnitPlanOverviews', // 从单元计划概览中提取书籍
            getUnitAllMeasure: '/curriculums/getUnitAllMeasure', // 获取单元所有测评点汇总
            getUnitWeekLessonProfile: '/curriculums/getUnitWeekLessonProfile', // 获取单元课程校训汇总
            getItemById: '/curriculums/getItemById', // 获取 Item 信息
            getPlanActivities: '/curriculums/getPlanActivities', // 获取计划活动
            checkCurrentUserToMagic: '/lessons2/curriculums/checkCurrentUserToMagic', // 检查当前用户是否可以继续上传 unit 到 magic
            generateTeachingTipsForLearnerProfileStream: '/curriculums/generateTeachingTipsForLearnerProfileStream', // 生成单元学习者素养培养指南接口
            deleteLearnerProfile: '/curriculums/deleteLearnerProfile', // 删除校训接口
            getAgencyLearnerProfile: '/curriculums/getAgencyLearnerProfile', // 新增获取用户默认校训接口
            generateAppendLearnerProfileStream: '/curriculums/generateAppendLearnerProfileStream', // 新增增加用户默认校训接口
            generateLearnerProfileStream: '/curriculums/generateLearnerProfileStream', // 生成各年级校训教学标准接口流
            generateLearnerProfileExpectationsStream: '/curriculums/generateLearnerProfileExpectationsStream', // 生成校训期望接口
            createLearnerProfile: '/curriculums/createLearnerProfile', // 创建各年级校训教学标准接口
            updateLearnerProfile: '/curriculums/updateLearnerProfile', // 更新各年级校训教学标准接口
            generateUnitCover: '/lessons2/curriculums/units/generateUnitCover', // 生成单元封面
            extractAndSearchUnitCover: '/lessons2/curriculums/units/extractAndSearchUnitCover', // 提取和搜索单元封面
            generateUnitOverviewStream: '/curriculums/generateUnitOverviewStream', // 生成单元概览流
            generateUnitDescriptionAnalysis: '/curriculums/generateUnitDescriptionAnalysis', // 生成单元描述分析
            addPlanCenters: '/lessons2/plans/addCenters', // 批量创建周计划 centers 组
            getCentersActivityChangeSetting: '/lessons2/plans/getCentersActivityChangeSetting', // 获取 centers 组活动变更设置
            saveCentersActivityChangeSetting: '/lessons2/plans/saveCentersActivityChangeSetting', // 保存 centers 组活动变更设置
            getPlanCenterActivityOverviews: '/lessons2/plans/getPlanCenterActivityOverviews', // 获取 centers 组活动概览
            evaluateUnitOverview: '/curriculums/evaluateUnitOverview', // 评估单元概览
            createUnitOverviewTest: '/curriculums/createUnitOverviewTest', // 创建单元概览测试
            createPlanOverviewTest: '/curriculums/createPlanOverviewTest', // 创建计划概览测试
            createLessonOverviewTest: '/curriculums/createLessonOverviewTest', // 创建课程概览测试
            createLessonTest: '/curriculums/createLessonTest', // 创建课程测试
            createTypicalBehaviorsTest: '/curriculums/createTypicalBehaviorsTest', // 创建典型行为测试
            createUniversalDesignForLearningTest: '/curriculums/createUniversalDesignForLearningTest', // 生成创建差异化教学测试请求
            createHomeActivityTest: '/curriculums/createHomeActivityTest', // 生成创建家庭活动测试
            createCulturallyResponsiveInstructionTest: '/curriculums/createCulturallyResponsiveInstructionTest', // 生成创建文化响应教学测试
            createCustomPromptTest: '/curriculums/createCustomPromptTest', // 生成创建自定义提示测试
            createDEITest: '/curriculums/createDEITest', // 生成创建设计请求测试
            generatePlanOverviewStream: '/curriculums/generatePlanOverviewStream', // 生成计划概览流
            generateSinglePlanOverviewStream: '/curriculums/generateSinglePlanOverviewStream', // 生成单个计划概览流
            generateLessonOverviewStream: '/curriculums/generateLessonOverviewStream', // 生成课程概览流
            generateCenterOverviewStream: '/lessons2/lessons/generateCenterOverviewStream', // 生成 center 组概览流
            generateSingleCenterOverviewStream: '/lessons2/lessons/generateSingleCenterOverviewStream', // 生成单个 center 组概览流
            generateSingleLessonOverviewStream: '/curriculums/generateSingleLessonOverviewStream', // 生成单个课程概览流
            generateLessonStream: '/curriculums/generateLessonStream', // 生成课程流
            generateCenterLessonStream: '/lessons2/lessons/generateCenterLessonStream', // 生成 center 组课程流
            generateTypicalBehaviorsStream: '/curriculums/generateTypicalBehaviorsStream', // 生成典型行为流
            shardUnitToMagic:'/lessons2/curriculums/shardUnitToMagic', // 將当期那 unit 发布到 magic
            checkCurrentUserCreateUnit:'/lessons2/curriculums/checkCurrentUserCreateUnit', // 检测创建 unit 次数
            checkCurrentUserCompleteUnit:'/lessons2/curriculums/checkCurrentUserCompleteUnit', //  判断当前用户是否有完成 unit
            setLessonObjectiveType:'/curriculums/setLessonObjectiveType', // 保存用户选择的 objective 类型
            getLessonObjectiveType:'/curriculums/getLessonObjectiveType', // 获取用户选择的 objective 类型

            generateUniversalDesignForLearningStream: '/curriculums/generateUniversalDesignForLearningStream', // 生成差异化教学内容
            generateCulturallyResponsiveInstructionStream: '/curriculums/generateCulturallyResponsiveInstructionStream', // 生成文化响应式教学内容
            generateUniversalDesignForLearningGroupStream: '/curriculums/generateUniversalDesignForLearningGroupStream', // 生成差异化教学内容
            generateCulturallyResponsiveInstructionGroupStream: '/curriculums/generateCulturallyResponsiveInstructionGroupStream', // 生成文化响应式教学内容
            generateHomeActivityStream: '/curriculums/generateHomeActivityStream', // 生成家庭活动内容
            generateLessonQuiz: '/curriculums/generateLessonQuizStream', // 生成课程测验
            regenerateLessonQuiz: '/curriculums/regenerateLessonQuizStream', // 重新生成课程测验
            getLessonQuizLevelStandard: '/curriculums/getLessonQuizLevelStandard', // 获取课程测验等级标准
            updateLessonQuizLevelStandard: '/curriculums/updateLessonQuizLevelStandard', // 更新课程测验等级标准
            generateCurriculumHomeActivity: '/curriculums/generateCurriculumHomeActivity', // 生成 Curriculum 家庭活动内容
            getCGUserDefaultCenter: '/curriculums/getUserDefaultCenter', // 获取用户默认学校
            getOrCreateUserDefaultCenter: '/curriculums/getOrCreateUserDefaultCenter', // 获取用户默认学校，如果不存在则创建，如果已经存在了，则不在进行创建
            createChildrenForGroup: '/curriculums/createChildrenForGroup', // 为生成的学校和班级生成对应的小孩的属性
            generateCurriculumLessonPlanStream : '/curriculums/generateCurriculumLessonPlanStream', // 生成课程计划
            generateAILessonTemplateStream : '/curriculums/generateAILessonTemplateStream', // 生成课程计划
            generateLessonTemplateStream : '/curriculums/generateLessonTemplateStream', // 生成课程模版
            generateCustomLessonTemplateStream: '/lessons2/lessons/generateCustomLessonTemplateStream', // 生成自定义课程模板
            checkLessonContentByAI: '/curriculums/checkLessonContentByAI', // AI 检查课程内容
            generateMeasuresByLesson: '/curriculums/generateMeasuresByLesson', // 根据课程内容生成测评点
            generateUniversalDesignForLearningByLessonContentStream: '/curriculums/generateUniversalDesignForLearningByLessonContentStream', // 根据课程内容生成差异化教学内容
            generateCulturallyResponsiveInstructionByLessonContentStream: '/curriculums/generateCulturallyResponsiveInstructionByLessonContentStream', // 根据课程内容生成文化响应式教学内容
            generateTypicalBehaviorsByLessonContentStream: '/curriculums/generateTypicalBehaviorsByLessonContentStream', // 根据课程内容生成课程典型行为
            generateTeachingTipsByLessonContentStream: '/curriculums/generateTeachingTipsStream', // 根据课程内容生成标准教学指导
            generateLessonLearnerProfileStream: '/curriculums/generateLessonLearnerProfileStream', // 根据课程内容生成校训
            generateLessonCover: '/curriculums/generateLessonCover', // 根据课程内容生成课程封面
            extractAndSearchLessonCover: '/curriculums/extractAndSearchLessonCover', // 提取和搜索课程封面
            searchCover: '/curriculums/searchCover', // 搜索图片
            setCoverInfo: '/curriculums/setCoverInfo', // 设置图片封面信息
            setLessonSlideImage: '/curriculums/setLessonSlideImage', // 设置课程 slides 中的图片
            getAllFrameworkInfoByState: '/domains/getAllFrameworkInfoByState', // 获取所有框架信息
            getAllFrameworkInfoByCountry: '/domains/getAllFrameworkInfoByCountry', // 获取所有框架信息
            createBatchGenerateLessonTask: '/lessons2/lessons/createBatchGenerateLessonTask', // 创建课程完整资源下载任务
            createBatchGenerateTask: '/lessons2/curriculums/units/createBatchGenerateTask', // 创建批量生成任务
            getFileGenerateProgress: '/lessons2/curriculums/report/getFileGenerateProgress', // 创建批量生成任务
            getLessonSlidesDownloadModel: '/lessons2/lessons/getLessonSlidesDownloadModel', // 课程 slides 需要先创建后下载
            getLessonTemplateDownloadModels: '/lessons2/curriculums/getLessonTemplateDownloadModels', // 课程模板需要先创建后下载
            createLessonTemplatesDownload: '/lessons2/lessons/createLessonTemplatesDownload', // 课程多个模板可能需要先创建后下载
            createAdaptedUnit: '/lessons2/curriculums/units/createAdaptedUnit', // 创建改编单元
            createUnitFoundationAndWeeklyTheme: '/lessons2/curriculums/units/createUnitFoundationAndWeeklyTheme', // 生成单元和周信息
            checkDomainConformOfUnitByAI: '/curriculums/checkDomainConformOfUnitByAI', // 检查单元内容和学科/测评点是否匹配
            getLightAdaptOptions: '/lessons2/curriculums/units/getLightAdaptOptions', // 获取轻量改编选项数据缓存
            validateUrl: '/lessons2/import/validateUrl', // 校验链接是否有效
            performImportUnitContentFromUrl: '/lessons2/import/performImportUnitContentFromUrl', // 解析上传内容
            analyzeExtractedContent: '/lessons2/import/analyzeExtractedContent', // 使用提示词提取解析内容
            userExampleImportUnit: '/lessons2/import/userExampleImportUnit', // 用户使用样例课程
            getAnalyzeContentById: '/lessons2/import/getAnalyzeContentById', // 使用 ID 查询分析结果
            getOcrResultById: '/lessons2/import/getOcrResultById', // 使用 ID 查询 OCR 识别结果
            saveUserChangeAnalyzeResult: '/lessons2/import/saveUserChangeAnalyzeResult', // 保存用户修改后的分析结果
            checkSubjects: '/lessons2/import/checkSubjects', // 校验学科

            getBookVolumes: '/google/books/volumes',
            searchBookVideo: 'notes/book/video',
            publishLesson: '/lessons2/lessons/publish',
            lessonGenerateUniversalDesignForLearningGroupStream: '/lessons2/lessons/generateUniversalDesignForLearningGroupStream',
            universalDesignForLearningStream: '/lessons2/lessons/generateUniversalDesignForLearningStream',
            saveLessonDraft: '/lessons2/lessons/saveDraft',
            getLessonsVersionInfo: '/lessons2/lessons/getLessonsVersionInfo',
            getAgeGroups: '/lessons2/lessons/getAgeGroups',
            getThemes: '/lessons2/lessons/getThemes',
            listDomains: '/lessons2/lessons/listDomains',
            getFrameworks: '/lessons2/lessons/getFrameworks',
            getExtendFrameworks: '/lessons2/lessons/getExtendFrameworks',
            getMeasures2: '/lessons2/lessons/getMeasures',
            getOnlyDomainMapFrameworkMeasures: '/lessons2/lessons/getOnlyDomainMapFrameworkMeasures',
            getMeasuresContainTopAndBottom: '/lessons2/lessons/getMeasuresContainTopAndBottom',
            getPublicLessons: '/lessons2/lessons/getPublicLessons',
            getAgencyLessons: '/lessons2/lessons/getAgencyLessons',
            getAgencyLessonsPlan: '/lessons2/lessons/getLessonPlanList',
            getMyLessons: '/lessons2/lessons/getMyLessons',
            getMyFavoriteLessons: '/lessons2/lessons/getMyFavoriteLessons',
            getRecoverableLesson: '/lessons2/lessons/getRecoverableLesson',
            deleteLessonPermanently: '/lessons2/lessons/deletePermanently',
            recoverLesson: '/lessons2/lessons/recover',
            recoverDeletedLesson: '/lessons2/lessons/recoverDeletedLesson',
            getCopyLessons: '/lessons2/lessons/getCopyLessons',
            getComments: '/lessons2/lessons/getComments',
            getSubComments: '/lessons2/lessons/getSubComments',
            commentLesson: '/lessons2/lessons/commentLesson',
            deleteComment: '/lessons2/lessons/deleteComment',
            updateVersion: '/lessons2/lessons/updateVersion',
            increaseLessonViewCount: '/lessons2/lessons/increaseLessonViewCount',
            getLessonDetail: '/lessons2/lessons/detail',
            getCurriculumLessonDetail: '/lessons2/lessons/getCurriculumLessonDetail',
            getCenterLessonDetail: '/lessons2/lessons/getCenterLessonDetail',
            getLessonLastDraftDetail: '/lessons2/lessons/lastDraftDetail',
            generateLessonSource: '/curriculums/generateResources', // 根据 CLR 生成资源内容
            generateLessonMixedAgeGroupStream: '/curriculums/generateLessonMixedAgeGroupStream', // 生成混龄组课程内容
            getShowMixedAgeGroupData: '/curriculums/getShowMixedAgeGroupData', // 判断是否有权限显示混龄组标签
            checkCanContinueAdapt: '/curriculums/checkCanContinueAdapt', // 检查是否可以继续改编
            generateResourcesByImpStep: '/curriculums/generateResourcesByImpStep', // 根据实施步骤生成资源内容
            like: '/lessons2/lessons/like',
            cancelLike: '/lessons2/lessons/cancelLike',
            favorite: '/lessons2/lessons/favorite',
            cancelFavorite: '/lessons2/lessons/cancelFavorite',
            copyLesson: '/lessons2/lessons/copyLesson',
            copyAdaptLesson: '/lessons2/lessons/copyAdaptLesson',
            promoteLesson: '/lessons2/lessons/promoteToAgency',
            deleteLesson: '/lessons2/lessons/delete',
            getLessonRecommendStatus: '/lessons2/lessons/getLessonRecommendStatus',
            getCenterTeachers: '/lessons2/lessons/admin/getCenterTeachers',
            getAgencyStaffs: '/lessons2/lessons/admin/getAgencyStaffs',
            getAllPlanByUserId: '/lessons2/plans/listApprovedPlansByUserId',
            getTeacherDialog: '/lessons2/lessons/getTipStatus',
            getManageTeacherLessons: '/lessons2/lessons/admin/getTeacherLessons',
            addTipRecords: '/lessons2/lessons/addTipRecords',
            getUserOpen: '/users/admin/getUserOpen',
            cancelPromoteToAgency: '/lessons2/lessons/cancelPromoteToAgency',
            setUserOpen: '/users/admin/setUserOpen',
            getPreSignedURL: '/medias/getPreSignedUrl',
            generateLessonPDF: '/lessons2/lessons/generateOwnLessonPDF', //获取课程详情 PDF
            generateLessonDetailDoc: '/lessons2/lessons/generateLessonDetailDoc', //获取课程详情 docs
            saveLessonGoogleSlide: '/lessons2/lessons/saveLessonGoogleSlide', //将创建出来的 PPT 导出给用户
            checkGoogleAuth: '/lessons2/lessons/checkGoogleAuth', //验证谷歌登录
            shareLesson: '/notes',
            sendFileDownloadEmail: '/lessons2/lessons/sendFileDownloadEmail', // 发送邮件下载文件
            addLessonTheme: '/lessons2/lessons/addLessonTheme',
            deleteLessonTheme: '/lessons2/lessons/deleteLessonTheme',

            // Lesson Curriculum
            createCurriculum: '/lessons2/curriculums/create',
            updateCurriculum: '/lessons2/curriculums/update',
            publishCurriculum: '/lessons2/curriculums/publish',
            getAgencyCurriculums: '/lessons2/curriculums/getAgencyCurriculums',
            getDraftCurriculums: '/lessons2/curriculums/getDraftCurriculums',
            getCurriculumDetail: '/lessons2/curriculums/detail',
            getCurriculumBookList: '/lessons2/curriculums/getBookList',
            getPrintableList: '/lessons2/curriculums/getPrintableList',
            getActivitiesList: '/lessons2/curriculums/getActivitiesList',
            getAssessmentList: '/lessons2/curriculums/getAssessmentList',
            applyPlans:'/lessons2/curriculums/apply',
            getApplyRecords: '/lessons2/plans/getApplyRecords',
            editPublishedCurriculum: '/lessons2/curriculums/editPublishedCurriculum',
            copyCurriculum: '/lessons2/curriculums/copy',
            removeCurriculum: '/lessons2/curriculums/remove',
            deleteCurriculum: '/lessons2/curriculums/delete',
            getCurriculumUnitPlan: '/lessons2/curriculums/selectUnitPlan',
            updateCurriculumUnitWeek: '/lessons2/curriculums/units/updateUnitWeek',
            addCurriculumUnitWeek: '/lessons2/curriculums/units/addUnitWeek',
            deleteCurriculumUnitWeek: '/lessons2/curriculums/units/deleteUnitWeek',
            getUnitDetail: '/lessons2/curriculums/units/getUnitDetail',
            addUnit: '/lessons2/curriculums/units/addUnit',
            updateUnit: '/lessons2/curriculums/units/updateUnit',
            deleteUnit: '/lessons2/curriculums/units/deleteUnit',
            updateUnitBaseInfo: '/curriculums/updateUnitBaseInfo', // 更新周计划校训
            updatePlanAdapted: '/curriculums/updatePlanAdapted', // 更新周计划的改编状态
            listAgencyUnits: '/lessons2/curriculums/units/listAgencyUnits',
            getUnits: '/lessons2/curriculums/units',
            getUnitList: '/lessons2/curriculums/units/getUnitList',
            getUnitPlanOverView: '/lessons2/curriculums/units/getUnitPlanOverView', // 获取课程单元周计划概览
            getUnitPlanMaterials: '/lessons2/curriculums/units/getUnitPlanMaterials', // 获取课程单元资源
            getKeyVocabularyList: '/lessons2/curriculums/getKeyVocabularyList',
            selectWeekPlan: '/lessons2/curriculums/units/selectWeekPlan',
            copyAdaptUnit: '/lessons2/curriculums/units/copyAdaptUnit', // 复制改编单元
            getUnitPlanAndActivities: '/lessons2/curriculums/units/getUnitPlanAndActivities', // 获取单元周计划活动
            createUnitBatchAdaptLessonTask: '/lessons2/plans/createUnitBatchAdaptLessonTask', // 创建单元批量改编任务
            batchUpdateUnitPlanItemLesson: '/lessons2/plans/batchUpdateUnitPlanItemLesson', // 批量更新计划项课程
            updateWeeklyMeasureIds: '/lessons2/plans/updateWeeklyMeasureIds', // 更新周计划测评点
            updateWeeklyRubrics: '/lessons2/plans/updateWeeklyRubrics', // 更新周计划校训
            getLessonVersions: '/lessons2/lessons/getVersionList', // 获取课程历史版本列表
            getLessonVersionDetail: '/lessons2/lessons/getVersionDetail', // 获取课程历史版本详情
            // Data Review
            // 生成 School Readiness Measure 报告 PDF
            // 下载 School Readiness Measure 报告 Excel
            getObservationStatiscs: '/lessons2/data/getObservationRatingStats',
            getDomainScoreStats: '/lessons2/data/getDomainScoreStats',
            getTopInterests: '/lessons2/data/getTopInterests',
            // Weekly Plan
            createPlan: '/lessons2/plans/create',
            updatePlan: '/lessons2/plans/update',
            unitPlan: '/lessons2/plans/unit/get',
            updatePlanBaseInfo: '/lessons2/plans/updateBaseInfo',
            submitPlanReview: '/lessons2/plans/submitReview',
            lockPlan: '/lessons2/plans//lockPlan',
            submitAdminTemplate: '/lessons2/plans/submitAdminTemplate',
            revertPlanReview: '/lessons2/plans/revertReview',
            deletePlan: '/lessons2/plans/delete',
            getPlan: '/lessons2/plans/get',
            // 通过周计划 ID 获取得到当前周计划使用的框架 ID
            getPlanFrameworkIds: '/lessons2/plans/getPlanFrameworkIds',
            getStateFrameworkInfoByState : '/domains/unit/getStateFrameworkInfoByState',
            getDomainInfosByFrameworkId: '/domains/unit/getDomainInfosByFrameworkId',
            getStates: '/domains/unit/getStates',
            submitStateStandardsRequest: '/domains/unit/submitStateStandardsRequest', // 提交州标准请求
            createCurriculumWeekPlan: '/lessons2/plans/createCurriculumWeekPlan',
            addPlanCenter:'/lessons2/plans/addCenter',
            deletePlanCenter:'/lessons2/plans/deleteCenter',
            getPlanLessons: '/lessons2/plans/getPlanLessons', // 获取周计划中的课程列表
            updatePlanCenter:'/lessons2/plans/updateCenter',
            unlockEditing: '/lessons2/plans/unlockEditing',
            endApplyFlowPath: '/lessons2/plans/endApplyFlowPath',
            deleteTag: '/lessons2/plans/deleteTag',
            createPlanItemCategory: '/lessons2/plans/createItemCategory',
            updatePlanItemCategory: '/lessons2/plans/updateItemCategory',
            deletePlanItemCategory: '/lessons2/plans/deleteItemCategory',
            createPlanItem: '/lessons2/plans/createItem',
            updatePlanItem: '/lessons2/plans/updateItem',
            updateUnitOverviewItemInfo: '/lessons2/plans/updateUnitOverviewItemInfo',
            deletePlanItem: '/lessons2/plans/deleteItem',
            getPlanReflection: '/lessons2/plans/getReflection',
            savePlanReflection: '/lessons2/plans/saveReflection',
            listPlans: '/lessons2/plans/list',
            listAdminTemplates: '/lessons2/plans/listAdminTemplates',
            reviewPlan: '/lessons2/plans/review',
            listPlanWeeks: '/lessons2/plans/listWeeks',
            getPlanPDF: '/lessons2/plans/getPDF',
            getGroupTeachers: '/users/group/getTeachers',
            getFrameworkInfo: '/portfolios/groupDomain',
            getFrameworkMeasures: '/portfolios/getFrameworkMeasures',
            getFrameworkMeasureMap: '/lessons2/lessons/getFrameworkMeasureMap',
            getGroupFrameworkMeasures: '/portfolios/getGroupFrameworkMeasures',
            searchLessons: '/lessons2/plans/searchLessons',
            replicatePlan: '/lessons2/plans/replicate',
            getLessonReflection: '/lessons2/plans/getLessonReflection',
            saveLessonReflection: '/lessons2/plans/saveLessonReflection',
            deleteLessonReflection: '/lessons2/plans/deleteLessonReflection',
            listLessonReflections: '/lessons2/plans/listLessonReflections',
            listLessonsWithReflections: '/lessons2/plans/listLessonsWithReflections',
            getLessonNotes: '/lessons2/plans/getLessonNotes',
            getCentersWithTeachers: '/users/getCentersWithTeachers', // 获取要分享周计划的老师列表
            sharePlan: '/lessons2/plans/sharePlan', // 分享周计划，
            getUserPlanCount: '/lessons2/plans/getUserPlanCount', // 获取某用户某时间范围内周计划数
            listShareUsers: '/lessons2/plans/listShareUsers', // 获取管理员分享过来的用户列表
            readPlan: '/lessons2/plans/readPlan', // 标记分享来的周计划为已读状态
            listSharedPlansByUser: '/lessons2/plans/listSharedPlansByUser',
            listSharedPlans: '/lessons2/plans/listSharedRecords',
            getSharedReadStats: '/lessons2/plans/getSharedReadStats',
            listSharedRecordPlans: '/lessons2/plans/listSharedRecordPlans',
            deleteShareRecord: '/lessons2/plans/deleteShareRecord',
            listWeeklyReflections: '/lessons2/plans/listWeeklyReflections', // 获取周反思列表
            getWeeklyReflectionsPDF: '/lessons2/plans/getWeeklyReflectionsPDF', // 获取周反思列表PDF
            saveExternalMedia:'lessons2/lessons/externalMediaUrl', // 保存外部链接
            batchPublishAdaptedLesson:'lessons2/lessons/batchPublishAdaptedLesson', // 批量发布改编课程
            batchRemoveAdaptedLesson:'lessons2/lessons/batchRemoveAdaptedLesson', // 批量移除发布改编课程
            createBatchAdaptLessonTask:'lessons2/plans/createBatchAdaptLessonTask', // 批量创建任务
            queryBatchAdaptLessonTask:'lessons2/plans/getBatchAdaptLessons', // 获取批量改编课程任务
            //dll
            getGroupLanguageList: '/dll/homework/getGroupLanguageList', // 获取选择的语言
            setGroupLanguage: '/dll/homework/setGroupLanguage', // 设置班级选择语言
            createDllHomework: '/dll/homework/create', // 创建双语作业
            editDllHomework: '/dll/homework/edit', // 编辑双语作业
            homeworkTranslate: '/dll/homework/translate', // 一个单词批量翻译为多种语言
            batchTranslate: '/dll/homework/batchTranslate', // 多个单词批量翻译为多种语言
            getTeacherDllList: '/dll/homework/getHomeworkList', // 老师获取作业列表
            deleteHomework: '/dll/homework/delete', // 老师删除作业
            dllStats: '/dll/homework/getDashboardStatistics', // 获取dll dashboard 首页统计信息
            getCoachHomeworkList: '/dll/homework/getCoachHomeworkList', // 获取 教练作业
            getLanguageTextToSpeech: '/dll/homework/getTextToSpeech', // 获取dll 各种语言播放的语音
            getHaveDLLChild: '/dll/homework/getHaveDLLChild', // 获取该班级是否有dll学生
            getGroupDLLShareOnlyDLLChild: '/dll/getGroupDLLShareOnlyDLLChild', // 查询班级 DLL 是否只发送给 DLL 小孩
            setGroupDLLShareOnlyDLLChild: '/dll/setGroupDLLShareOnlyDLLChild', // 设置班级 DLL 只发送给 DLL 小孩
            getDLLSettingShowStatus: '/dll/getDLLSettingShowStatus', // 查询班级是否显示过设置弹窗
            closeGroupDllSettingTips: '/dll/closeGroupDllSettingTips', // 添加设置弹窗显示记录
            getSubjects: '/dll/getSubjects', // 获取主体信息
            getGroupTeams: '/groups/getGroupTeams', // 获取班级小孩分组信息
            hasGroupTeams: '/groups/hasGroupTeams', // 获取班级小孩是否存在分组信息
            saveGroupTerms: '/groups/updateGroupTeams', // 获取班级小孩分组信息
            getAgencyHomeworkList: "/dll/homework/getAgencyHomeworkList", // 获取all staff的内容
            getTeacherHomeworkList:"/dll/homework/getTeacherHomeworkList", // 获取某个老师创建的作业
            getAgencyTeacherList:"/dll/homework/getAgencyTeacher", // 获取机构的老师
            createResourceTheme:"/dll/homework/createResourceTheme", // 添加家庭作业资源模版分类
            getResourceTheme:"/dll/homework/getResourceTheme", // 获取模版资源主题
            editResourceTheme:'/dll/homework/editResourceTheme',
            editResource:'/dll/homework/editResource',
            deleteResourceTheme:"/dll/homework/deleteResourceTheme", // 删除家庭资源模版
            deleteResource:"/dll/homework/deleteResource", // 删除家庭资源模版
            getResource:"/dll/homework/getResource", // 获取模版资源
            createResource:"/dll/homework/createResource", // 添加家庭作业资源模版内容
            batchCreateDll:"/dll/homework/batchCreate", // 通过模版批量创建作业
            closeLessonPlanningWelcomeGuide: '/users/closeLessonPlanningWelcomeGuide', // 关闭 AI 课程欢迎引导
            getUserNeedGuideFeatures :'/users/getUserNeedGuideFeatures', // 获得用户需要做引导的功能
            hideGuide: '/users/hideGuide', // 设置用户不再进行的功能引导
            checkThirdLoginToken:'/account/social/callback', // 对三方token进行校验
            addVocabularyResource: '/lessons2/curriculums/units/lesson/addVocabulary', // 添加词汇资源
            updateVocabularyResource: '/lessons2/curriculums/units/lesson/deleteVocabulary', // 更新词汇资源
            addBookResource: '/lessons2/curriculums/units/lesson/addBook', // 添加书资源
            updateBookResource: '/lessons2/curriculums/units/lesson/deleteBook', // 更新书资源
            addAttachmentResource: '/lessons2/curriculums/units/lesson/addAttachment', // 添加附件资源
            updateAttachmentResource: '/lessons2/curriculums/units/lesson/deleteAttachment', // 更新附件资源
            updateDomainMeasures: '/lessons2/curriculums/units/plan/updateMeasures', // 更新测评领域和测评点
            generatePlanMaterialsAndResource: '/lessons2/curriculums/units/generatePlanMaterialsAndResource', // 生成系列课程单元周计划的材料和资源详情
            getPlanMaterialsAndResource: '/lessons2/curriculums/units/getUnitPlanSource', // 获取系列课程单元周计划的材料和资源详细
            getItemInfo: '/lessons2/curriculums/getPlanItem', // 获取活动信息
            getWeekPlanTemplateSwitch: '/lessons2/curriculums/getWeekPlanTemplateSwitch', // 获取周计划模板老师可自定义开关状态
            getAddPlanMaterialInfo: '/lessons2/curriculums/units/plan/getAddPlanMaterialInfo', // 获取添加材料的选择弹窗信息
            addPlanMaterial: '/lessons2/curriculums/units/plan/addPlanMaterial', // 添加单元周计划活动材料
            deletePlanMaterial: '/lessons2/curriculums/units/plan/deletePlanMaterial', // 删除单元周计划活动材料
            editPlanMaterial: '/lessons2/curriculums/units/plan/updatePlanMaterial', // 更新单元周计划活动材料
            getEditPlanMaterialInfo: '/lessons2/curriculums/units/plan/getUpdatePlanMaterialInfo', // 获取编辑材料的选择弹窗信息
            addAction : '/lessons2/curriculums/units/addAction', // 添加操作记录信息
            hideGroupInfoConfirmTip: '/lessons2/plans/hideGroupInfoConfirmTip', // 隐藏班级信息确认弹窗
            setTeacherGroupEnabled: '/lessons2/plans/setTeacherGroupEnabled', // 设置班级改编课程是否开启老师分组
            saveOpenMixedAgeGroup: 'lessons2/lessons/saveOpenMixedAgeGroup', // 保存混龄班级元数据
            getOpenMixedAgeGroup: 'lessons2/lessons/getOpenMixedAgeGroup', // 获取
            getGroupAdaptSetting: '/lessons2/plans/getGroupAdaptSetting', // 获取班级 Adapt 课程设置
            batchAdaptLessonTasks: '/lessons2/plans/batchAdaptLessonTasks', // 批量改编课程任务
            // getRatifySetting: `/inkinds/ratifies/settings/get`,
            generateDeiBestPracticeByUnitStream :'/curriculums/generateDEIBestPracticeByUnitStream', // 生成 DEI
            getCountryState :'/users/getCountryState', // 获取国家下州信息
            createGenieCurriculum: '/curriculums/createCurriculum', // 创建 genie 类型的 curriculum
            updateGenieCurriculum: '/curriculums/updateCurriculum', // 更新 genie 类型的 curriculum
            getAutoAdaptUnitMeasures: '/curriculums/getAutoAdaptUnitMeasures', // genie 类型的 curriculum 生成测评点
            getAutoAdaptWeekPlanMeasures: '/curriculums/getAutoAdaptWeekPlanMeasures', // genie 类型的 curriculum 生成测评点
            getGenieCurriculumDetail: '/curriculums/getCurriculumDetail', // 获取 genie 类型的 curriculum 详情
            getCurriculumDesignerExcel: '/curriculums/getCurriculumDesignerExcel', // 下载 Excel 表格

            // Resource 相关接口
            getUserLessonResourceSettings: '/resource/getUserLessonResourceSettings', // 获取用户课程资源配置
            saveUserResourceSettings: '/resource/saveUserResourceSettings', // 保存用户课程资源配置
            suggestNewResource: '/resource/suggestNewResource', // 建议新的课程资源
            getResourceHistory: '/curriculums/getResourceHistory', // 获取资源版本历史记录
            updateResource: '/curriculums/updateResource', // 更新课程资源
            clearResourceHistory: '/curriculums/clearResourceHistory', // 清空资源版本历史记录
        }
    }
}
window.$api = _api
export default _api