import axios from "@/utils/axios";

export default {
  // 获取 TTS 支持的语言及当前登录人最近使用的语言
  listLanguages() {
    return axios.get('/dll/listLanguages')
  },
  // 记录当前登录人最近使用的语言
  setLastUsedLanguages(langCodes) {
    return axios.post('/dll/setLastUsedLanguages', {}, {params: {langCodes}})
  },
  // 获取班级 DLL 语言
  listGroupLanguages(groupId) {
    return axios.get('/dll/homework/getGroupLanguageList', {params: {groupId}})
  },
  // 语言翻译,保留换行符
  async translate(content, langCodes) {
    let contents = content.split('\n');
    let results = [];
    for (const con of contents) {
      results.push(axios.post($api.urls().homeworkTranslate, {content: con, langCodes}))
    }
    let langMap = {};
    for (const result of results) {
      for (const translation of await result) {
        let langContents = langMap[translation.langCode] = langMap[translation.langCode] || []
        langContents.push(translation);
      }
    }
    return langCodes.map(langCode => {
      let translations = langMap[langCode] || [];
      let translation = translations[0] || {content: '', langCode}
      translation.content = translations.map(item => item.content).join("\n");
      return translation;
    });
  },
  // TTS
  tts(content, language) {
    return axios.get($api.urls().getLanguageTextToSpeech, {params: {content, language}})
  },
  // 设置对象 DLL
  setSubjects(sourceId, sourceType, subjects) {
    return axios.post('/dll/setSubjects', {sourceId, sourceType, subjects});
  },
  // 查询班级选择的语言
  getGroupLanguageList(groupId) {
    return axios.get($api.urls().getGroupLanguageList, {params: {groupId}})
  },
  // 设置班级选择语言
  setGroupLanguage(DLLGroupLanguage) {
    return axios.post($api.urls().setGroupLanguage, {...DLLGroupLanguage})
  },
  // 查询班级 DLL 是否只发送给 DLL 小孩
  GetGroupDLLShareOnlyDLLChildResponse(groupId) {
    return axios.get($api.urls().getGroupDLLShareOnlyDLLChild, {params: {groupId}})
  },
  // 设置班级 DLL 只发送给 DLL 小孩
  setGroupDLLShareOnlyDLLChild(groupId, onlyDLLChildOpen) {
    return axios({
      method: 'post',
      url: $api.urls().setGroupDLLShareOnlyDLLChild,
      params: {
        groupId,
        onlyDLLChildOpen
      }
    })
  },
  // 查询班级是否显示过设置弹窗
  getDLLSettingShowStatus(groupId) {
    return axios.get($api.urls().getDLLSettingShowStatus, {params: {groupId}})
  },
  // 添加 DLL 设置的显示记录
  closeGroupDllSettingTips(groupId) {
    return axios({
      method: 'post',
      url: $api.urls().closeGroupDllSettingTips,
      params: {
        groupId
      }
    })
  },
  // 判断对象是否存在主体信息
  getSubjects(sourceId) {
    return axios.get($api.urls().getSubjects, {params: {sourceId}})
  }
}
