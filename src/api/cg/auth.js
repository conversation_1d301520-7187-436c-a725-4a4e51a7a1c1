/**
 * CG 认证相关 API
*/
import { _cgAxios } from '@/utils/axios'

export const useAuthApi = () => {
  // 登录
  const login = (body) => {
    return _cgAxios({
      url: '/auth/login',
      method: 'POST',
      data: body
    })
  }

  // 登出
  const logout = () => {
    return _cgAxios({
      url: '/auth/logout',
      method: 'POST'
    })
  }

  // 检查邮箱是否存在
  const checkEmail = (email) => {
    return _cgAxios({
      url: '/auth/check_email',
      method: 'POST',
      data: { email }
    })
  }

  // 发送邮箱验证码
  const sendVerifyEmailCode = (email) => {
    return _cgAxios({
      url: '/auth/send_register_verification_email',
      method: 'POST',
      data: { email }
    })
  }

  // 验证邮箱验证码
  const verifyEmailCode = (body) => {
    return _cgAxios({
      url: '/auth/check_code',
      method: 'POST',
      data: body
    })
  }

  // 注册
  const register = (body) => {
    return _cgAxios({
      url: '/auth/register',
      method: 'POST',
      data: body
    })
  }

  // 获取当前用户信息
  const getCurrentUser = (body) => {
    return _cgAxios({
      url: '/auth/me',
      method: 'POST',
      data: body
    })
  }

  // 发送重置密码邮件
  const sendPasswordResetEmail = (email) => {
    return _cgAxios({
      url: '/auth/send_reset_password_email',
      method: 'POST',
      data: { email }
    })
  }

  // 通过 Google 登录
  const googleLogin = (params) => {
    return _cgAxios({
      url: '/auth/login_with_google',
      method: 'POST',
      data: params
    })
  }

  // 获取授权码
  const getAuthCode = () => {
    return _cgAxios({
      url: '/auth/get_auth_code',
      method: 'GET'
    })
  }

  return {
    login,
    logout,
    checkEmail,
    verifyEmailCode,
    sendVerifyEmailCode,
    register,
    getCurrentUser,
    sendPasswordResetEmail,
    googleLogin,
    getAuthCode
  }
} 