/**
 * CG 邀请相关 API
 */
import { _cgAxios } from '@/utils/axios'

export const useInvitationApi = () => {
  /**
   * 获取邀请记录
   */
  const getInvitationRecords = (params) => {
    return _cgAxios({
      url: '/invitations/get_invitation_records',
      method: 'GET',
      params: params
    })
  }

  /**
   * 发送邀请邮件
   */
  const sendInvitationEmail = (body) => {
    return _cgAxios({
      url: '/invitations/send_share_email',
      method: 'POST',
      data: body
    })
  }

  /**
   * 获取邀请链接
   */
  const getInvitationLink = () => {
    return _cgAxios({
      url: '/invitations/get_invitation_link',
      method: 'POST',
      data: {}
    })
  }

  /**
   * 创建分享奖励
   */
  const createShareReward = (body) => {
    return _cgAxios({
      url: '/invitations/create_share_reward',
      method: 'POST',
      data: body
    })
  }

  /**
   * 获取邀请信息
   */
  const getInvitationInfo = (params) => {
    return _cgAxios({
      url: '/invitations/get_invitation_info',
      method: 'GET',
      params: params
    })
  }

  /**
   * 创建邀请记录（首次安装插件）
   */
  const createInvitationRecord = (params) => {
    return _cgAxios({
      url: '/invitations/create_invitation_record',
      method: 'POST',
      data: params
    })
  }

  return {
    getInvitationRecords,
    sendInvitationEmail,
    getInvitationLink,
    createShareReward,
    getInvitationInfo,
    createInvitationRecord
  }
} 