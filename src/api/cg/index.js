/**
 * CG API 模块导出
 */
import { useAuthApi } from './auth'
import { useHealthApi } from './health'
import { useFeedbackApi } from './feedback'
import { useInvitationApi } from './invitation'
import { useUserApi } from './user'
import { useMarketing<PERSON>pi } from './marketing'

export {
  useAuthApi,
  useHealthApi,
  useFeedbackApi,
  useInvitationApi,
  useUserApi,
  useMarketingApi
}

export default {
  useAuthApi,
  useHealthApi,
  useFeedbackApi,
  useInvitationApi,
  useUserApi,
  useMarketingApi
} 