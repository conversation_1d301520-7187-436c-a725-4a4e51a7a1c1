 /**
 * CG 营销相关 API
 */
import { _cgAxios } from '@/utils/axios'


export const useMarketingApi = () => {
  /**
   * 获取活跃的营销活动模板
   */
  const getMarketingTemplates = () => {
    return _cgAxios({
      url: '/marketing/templates',
      method: 'GET'
    })
  }

  /**
   * 获取24小时内已邀请邮箱列表
   */
  const getRecentInvitedEmails = (params) => {
    return _cgAxios({
      url: '/marketing/get_recent_invited_emails',
      method: 'POST',
      data: params
    })
  }

  return {
    getMarketingTemplates,
    getRecentInvitedEmails
  }
}