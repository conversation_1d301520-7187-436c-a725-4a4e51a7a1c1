/**
 * CG 用户相关 API
 */
import { _cgAxios } from '@/utils/axios'

export const useUserApi = () => {
  /**
   * 获取用户分享元数据
   */
  const getUserMeta = (userId) => {
    return _cgAxios({
      url: '/users/get_user_meta',
      method: 'GET',
      params: { user_id: userId }
    })
  }

  /**
   * 更新用户分享元数据
   */
  const updateUserMeta = (metaData) => {
    return _cgAxios({
      url: '/users/update_user_share_meta',
      method: 'PUT',
      data: metaData
    })
  }

  /**
   * 更新用户头像
   */
  const updateUserAvatar = (metaData) => {
    return _cgAxios({
      url: '/users/update_avatar',
      method: 'PUT',
      data: metaData
    })
  }

  /**
   * 更新用户名
   */
  const updateUserName = (userName) => {
    return _cgAxios({
      url: '/users/update_user_name',
      method: 'PUT',
      data: {user_name: userName}
    })
  }

  return {
    getUserMeta,
    updateUserMeta,
    updateUserAvatar,
    updateUserName
  }
} 