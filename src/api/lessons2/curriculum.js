import axios from '@/utils/axios'

export default {

  /**
   * 创建系列课程
   */
   createCurriculum () {
    return axios.post(
      $api.urls().createCurriculum
    )
  },

  /**
   * 更新系列课程基本信息
   */
  updateCurriculum (params) {
    return axios.post(
      $api.urls().updateCurriculum,
      params
    )
  },

  /**
   * 获取机构的系列课程
   */
  getAgencyCurriculums (params) {
    return axios.get(
      $api.urls().getAgencyCurriculums,
      {
        params: params
      }
    )
  },

  /**
   * 获取草稿列表
   */
  getDraftCurriculums (params) {
    return axios.get(
      $api.urls().getDraftCurriculums,
      {
        params: params
      }
    )
  },

  /**
   * 获取系列课程详情
   */
  getCurriculumDetail (params) {
    return axios.get(
      $api.urls().getCurriculumDetail,
      {
        params: params
      }
    )
  },

  /**
   * 应用系列课程的周计划
   */
   applyPlans (params) {
    return axios.post(
      $api.urls().applyPlans,
      params
    )
   },

   /**
    * 获取系列课程应用记录
    */
   getApplyRecords (params) {
    return axios.get(
      $api.urls().getApplyRecords,
      {
        params: params
      }
    )
   },

  /**
   * 编辑已发布的系列课程，获取系列课程的副本 ID
   */
  editPublishedCurriculum (params, queryParams) {
    return axios.post(
      $api.urls().editPublishedCurriculum,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 复制系列课程
   */
  copyCurriculum (params, queryParams) {
    return axios.post(
      $api.urls().copyCurriculum,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 移除系列课程
   */
  removeCurriculum (params, queryParams) {
    return axios.post(
      $api.urls().removeCurriculum,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 删除系列课程
   */
   deleteCurriculum (params, queryParams) {
      return axios.post(
        $api.urls().deleteCurriculum,
        params,
        {
          params: queryParams
        }
      )
    },

  getCurriculumUnitPlan (params) {
    return axios.post(
      $api.urls().getCurriculumUnitPlan,
      params
    )
  },

  updateCurriculumUnitWeek (params) {
    return axios.post(
      $api.urls().updateCurriculumUnitWeek,
      params
    )
  },

  addCurriculumUnitWeek (params) {
    return axios.post(
      $api.urls().addCurriculumUnitWeek,
      params
    )
  },

  deleteCurriculumUnitWeek (params) {
    return axios.post(
      $api.urls().deleteCurriculumUnitWeek,
      params
    )
  },

  /**
   * 获取系列课程详情
   */
  getUnitDetail (params) {
    return axios.get(
      $api.urls().getUnitDetail,
      {
        params: params
      }
    )
  },

  /**
   * 新增基础的系列课程单元
   */
  addUnit (params, queryParams) {
    return axios.post(
      $api.urls().addUnit,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 更新系列课程单元
   */
  updateUnit (params, queryParams) {
    return axios.post(
      $api.urls().updateUnit,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 删除系列课程单元
   */
  deleteUnit (params) {
    return axios.get(
      $api.urls().deleteUnit,
      {
        params: params
      }
    )
  },

  /**
   * 获取系列课程单元列表
   */
  getUnitList (params) {
    return axios.get(
      $api.urls().getUnitList,
      {
        params: params
      }
    )
  },

  /**
   * 发布系列课程
   */
   publishCurriculum (params) {
    return axios.post(
      $api.urls().publishCurriculum,
      params
    )
  },

  /**
   * 获取课程单元周计划概览
   */
  getUnitPlanOverView (params) {
    return axios.get(
      $api.urls().getUnitPlanOverView,
      {
        params: params
      }
    )
  },
  /**
  * 获取课程单元资源
  */
  getUnitPlanMaterials (params) {
    return axios.get(
      $api.urls().getUnitPlanMaterials,
      {
        params: params
      }
    )
  },

  /**
   * 获取系列课程 bookList 列表
   */
  getCurriculumBookList (id) {
    return axios.get(
      $api.urls().getCurriculumBookList,
      {
       params: { id: id }
      }
    )
  },

  /**
   * 获取系列课程词汇
   */
  getKeyVocabularyList (id) {
    return axios.get(
      $api.urls().getKeyVocabularyList,
      {
        params: { id: id }
      }
    )
  },

  /**
   * 获取打印材料列表
   */
  getPrintableList (id) {
    return axios.get(
      $api.urls().getPrintableList,
      {
        params: { id: id }
      }
    )
  },

  /**
   * 获取活动列表
   */
  getActivitiesList (id) {
    return axios.get(
      $api.urls().getActivitiesList,
      {
        params: { id: id }
      }
    )
  },

  /**
   * 获取测评点列表
   */
  getAssessmentList (id) {
    return axios.get(
      $api.urls().getAssessmentList,
      {
        params: { id: id }
      }
    )
  },

  /**
   * 选择周计划
   */
  SelectWeekPlan (planId, frameworkId) {
    return axios.get(
      $api.urls().selectWeekPlan,
      {
        params: { planId: planId, frameworkId: frameworkId }
      }
    )
  },

  /**
   * 生成单元封面
   */
  generateUnitCover (params) {
    return axios.post(
      $api.urls().generateUnitCover,
      params
    )
  },

  /**
   * 批量添加周计划 Centers 组
   */
  addPlanCenters (params) {
    return axios.post(
      $api.urls().addPlanCenters,
      params
    )
  },

  /**
   * 获取 Centers Activity 变化设置
   */
  getCentersActivityChangeSetting () {
    return axios.get(
      $api.urls().getCentersActivityChangeSetting
    )
  },

  /**
   * 保存 Centers Activity 变化设置
   */
  saveCentersActivityChangeSetting (params) {
    return axios.post(
      $api.urls().saveCentersActivityChangeSetting,
      params
    )
  },

  /**
   * 获取 centers 组活动概览
   */
  getPlanCenterActivityOverviews (params) {
    return axios.get(
      $api.urls().getPlanCenterActivityOverviews,
      {
        params: params
      }
    )
  },

  /**
   * 创建用户活动记录
   */
  createUserActivityRecord (params) {
    return axios.post(
      $api.urls().createUserActivityRecord,
      params
    )
  },

  /**
   * 获取周计划模板老师可自定义开关状态
   */
  getWeekPlanTemplateSwitch () {
    return axios.get(
      $api.urls().getWeekPlanTemplateSwitch
    )
  },

  /**
   * 根据 CLR 生成资源内容
   */
  generateLessonSource (params) {
    return axios.post(
      $api.urls().generateLessonSource,
      params
    )
  },

  /**
   * 创建 genie curriculum
   */
  createGenieCurriculum (params) {
    return axios.post(
      $api.urls().createGenieCurriculum,
      params
    )
  },

  /**
   * 更新 genie curriculum
   */
  updateGenieCurriculum (params) {
    return axios.post(
      $api.urls().updateGenieCurriculum,
      params
    )
  },

  /**
   *  genie 类型的 curriculum 生成测评点
   */
  getAutoAdaptUnitMeasures (params) {
    return axios.get(
      $api.urls().getAutoAdaptUnitMeasures,
      {
        params: params
      }
    )
  },

  /**
   * 获取 genie 类型的 curriculum 详情
   */
  getGenieCurriculumDetail (params) {
    return axios.get(
      $api.urls().getGenieCurriculumDetail,
      {
        params: params
      }
    )
  },

  /**
   * 下载 Excel 文件
   */
  getCurriculumDesignerExcel (params) {
    return axios.post(
      $api.urls().getCurriculumDesignerExcel,
      params
    )
  },

  /**
   * 获取用户最新的自定义模板
   * @returns {*}
   */
  getUserNewCustomFoundationModules () {
    return axios({
      method: 'GET',
      url: $api.urls().getUserNewCustomFoundationModules
    })
  }
}
