import axios from '@/utils/axios'

export default {

  /**
   * 获取用户课程资源配置
   */
  getUserLessonResourceSettings () {
    return axios.get(
      $api.urls().getUserLessonResourceSettings
    )
  },

  /**
   * 保存用户课程资源配置
   * @param {Array} resourceIds 资源ID列表
   */
  saveUserResourceSettings (resourceIds) {
    return axios.post(
      $api.urls().saveUserResourceSettings,
      resourceIds
    )
  },

  /**
   * 建议新的课程资源
   * @param {Object} data 建议内容
   */
  suggestNewResource (data) {
    return axios.post(
      $api.urls().suggestNewResource,
      data
    )
  }
}
