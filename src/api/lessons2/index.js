import axios from "../../utils/axios";
import { configBaseUrl, serverlessApiUrl } from "../../utils/setBaseUrl";
import plan from "./plan"
import resource from "./resource"
import curriculum from './curriculum'

export default {
  // 系列课程相关接口
  ...curriculum,
  // 周计划相关接口
  ...plan,
  // 课程资源相关接口
  ...resource,
  //搜索书
  getBookVolumes(keyword) {
    return axios.get($api.urls().getBookVolumes, {
      baseURL: configBaseUrl,
      params:{
        q:keyword
      }
    })
  },
  // 查询 复制该课程的课程
  getCopyLessons(lessonId) {
    return axios({
      method: 'get',
      url: $api.urls().getCopyLessons,
      baseURL: configBaseUrl,
      params:{
        lessonId
      }
    })
  },
  // 获取评论列表
  getComments(pageSize, pageNum, lessonId) {
    return axios({
      method: "GET",
      url: $api.urls().getComments,
      baseURL: configBaseUrl,
      params:{
        pageSize,
        pageNum,
        lessonId
      }
    })
  },
  // 获取子评论列表
  getSubComments(pageSize, pageNum, rootCommentId) {
    return axios({
      method: "GET",
      url: $api.urls().getSubComments,
      baseURL: configBaseUrl,
      params:{
        pageSize,
        pageNum,
        rootCommentId
      }
    })
  },
  // 评论课程
  commentLesson(content, lessonId, parentCommentId) {
    return axios({
      method: "POST",
      url: $api.urls().commentLesson,
      data: {
        content: content,
        lessonId: lessonId,
        parentCommentId: parentCommentId
      },
      baseURL: configBaseUrl
    })
  },
  // 删除评论
  deleteComment(commentId) {
    return axios({
      method: "POST",
      url: $api.urls().deleteComment,
      baseURL: configBaseUrl,
      params:{
        commentId
      }
    })
  },
  // 查询课程更新版本
  getUpdateHistory(lessonId) {
    return axios({
      method: 'get',
      url: $api.urls().updateVersion,
      baseURL: configBaseUrl,
      params:{
        lessonId
      }
    })
  },
  // 查询公共课程
  getPublicLessons(param) {
    return axios({
      method: 'get',
      url: $api.urls().getPublicLessons,
      params: {
        pageSize: param.pageSize,
        pageNum: param.pageNum,
        ages: param.ages.join(","),
        domainIds: param.domainIds.join(","),
        themeIds: param.themeIds.join(","),
        keyword: param.keyword,
        orderKey: param.orderKey,
        mappedFrameworkId: param.mappedFrameworkId,
        otherMeasureIds: param.otherMeasureIds.join(","),
        mappedDrdpFrameworkIds: (param.mappedDrdpFrameworkIds || []).join(",")
      },
      baseURL: configBaseUrl
    })
  },

  // 查询机构课程
  getAgencyLessons(param) {
    return axios({
      method: 'get',
      url: $api.urls().getAgencyLessons,
      params: {
        pageSize: param.pageSize,
        pageNum: param.pageNum,
        ages: param.ages.join(","),
        domainIds: param.domainIds.join(","),
        themeIds: param.themeIds.join(","),
        keyword: param.keyword,
        orderKey: param.orderKey,
        mappedFrameworkId: param.mappedFrameworkId,
        otherMeasureIds: param.otherMeasureIds.join(","),
        mappedDrdpFrameworkIds: (param.mappedDrdpFrameworkIds || []).join(",")
      },
      baseURL: configBaseUrl
    })
  },
  // 查询LessonPlan机构课程
  getAgencyLessonsPlan(param) {
    return axios({
      method: 'get',
      url: $api.urls().getAgencyLessonsPlan,
      params: {
        pageSize: param.pageSize,
        pageNum: param.pageNum,
      },
      baseURL: configBaseUrl
    })
  },
  // 查询我创建的课程
  getMyLessons(param) {
    return axios({
      method: 'get',
      url: $api.urls().getMyLessons,
      params: {
        pageSize: param.pageSize,
        pageNum: param.pageNum,
        ages: param.ages.join(","),
        domainIds: param.domainIds.join(","),
        themeIds: param.themeIds.join(","),
        keyword: param.keyword,
        orderKey: param.orderKey,
        status: param.status,
        mappedFrameworkId: param.mappedFrameworkId,
        otherMeasureIds: param.otherMeasureIds.join(","),
        mappedDrdpFrameworkIds: (param.mappedDrdpFrameworkIds || []).join(","),
        all: param.all, // 添加参数：是否查询所有课程
        publicOpen: param.publicOpen, // 添加参数：查询公共课程开关
        isAdaptedLesson: param.isAdaptedLesson // 添加参数：是否为公共课程
      },
      baseURL: configBaseUrl
    })
  },
  // 查询我收藏的课程
  getMyFavoriteLessons(param) {
    return axios({
      method: 'get',
      url: $api.urls().getMyFavoriteLessons,
      params: {
        pageSize: param.pageSize,
        pageNum: param.pageNum,
        ages: param.ages.join(","),
        domainIds: param.domainIds.join(","),
        themeIds: param.themeIds.join(","),
        keyword: param.keyword,
        orderKey: param.orderKey,
        mappedFrameworkId: param.mappedFrameworkId,
        otherMeasureIds: param.otherMeasureIds.join(","),
        mappedDrdpFrameworkIds: (param.mappedDrdpFrameworkIds || []).join(","),
        isAdaptedLesson: param.isAdaptedLesson // 添加参数：是否为公共课程
      },
      baseURL: configBaseUrl
    })
  },
  // 查询回收站课程
  getRecoverableLesson(pageSize, pageNum) {
    return axios({
      method: 'get',
      url: $api.urls().getRecoverableLesson,
      params: {
        pageSize: pageSize,
        pageNum: pageNum
      },
      baseURL: configBaseUrl
    })
  },
  // 删除回收站课程
  deleteLessonPermanently(lessonId) {
    return axios({
      method: 'POST',
      url: $api.urls().deleteLessonPermanently,
      baseURL: configBaseUrl,
      params:{
        lessonId
      }
    })
  },
  // 查询年龄组
  getAgeGroups(region) {
    return axios({
      method: 'get',
      url: $api.urls().getAgeGroups,
      params: {region:region}
    })
  },
  // 查询课程主题
  getThemes(includeCustom) {
    return axios({
      method: 'get',
      url: $api.urls().getThemes,
      params: { includeCustom: includeCustom }
    })
  },
  // 查询领域
  listDomains() {
    return axios({
      method: 'get',
      url: $api.urls().listDomains
    })
  },
  getFrameworks() {
    return axios({
      method: 'get',
      url: $api.urls().getFrameworks
    })
  },
  getExtendFrameworks(getFrameworkRequest) {
    return axios({
      method: 'post',
      url: $api.urls().getExtendFrameworks,
      data: getFrameworkRequest
    })
  },

  // 查询测评点(包括领域)
  getMeasures(frameworkId) {
    return axios({
      method: 'get',
      url: $api.urls().getMeasures2,
      params: {
        frameworkId: frameworkId
      }
    })
  },
  /**
   * 查询测评点(查询测评点(仅仅包括可映射的))
   * @param {*} frameworkId 框架 ID 
   * @param {*} compress 是否对测评点进行压缩处理
   * @returns 
   */
  getOnlyDomainMapFrameworkMeasures(frameworkId, compress = true) {
    return axios({
      method: 'get',
      url: $api.urls().getOnlyDomainMapFrameworkMeasures,
      params: {
        frameworkId: frameworkId,
        compress: compress
      }
    })
  },
  // 发布课程
  publishLesson(lesson) {
    return axios({
      method: 'POST',
      url: $api.urls().publishLesson,
      data: lesson
    })
  },
  // 保存课程草稿
  saveLessonDraft(lesson) {
    return axios({
      method: 'POST',
      url: $api.urls().saveLessonDraft,
      data: lesson
    })
  },
  // 点赞
  like(lessonId) {
    return axios({
      method: 'post',
      url: $api.urls().like,
      params:{
        lessonId
      }
    })
  },
  // 取消点赞：
  cancelLike(lessonId) {
    return axios({
      method: 'post',
      url: $api.urls().cancelLike,
      params:{
        lessonId
      }
    })
  },
  favorite(lessonId) {
    return axios({
      method: 'post',
      url: $api.urls().favorite,
      params:{
        lessonId
      }
    })
  },
  // 取消收藏
  cancelFavorite(lessonId) {
    return axios({
      method: 'post',
      url: $api.urls().cancelFavorite,
      params:{
        lessonId
      }
    })
  },
  // 添加课程浏览量
  increaseLessonViewCount(lessonId) {
    return axios({
      method: 'POST',
      url: $api.urls().increaseLessonViewCount,
      params:{
        lessonId
      }
    })
  },
  // 查询课程详情
  getLessonDetail(params) {
    return axios({
      method: 'GET',
      url: $api.urls().getLessonDetail,
      params: params
    })
  },
  // 查询单元课程中的课程详情
  getCurriculumLessonDetail (lessonId) {
    return axios({
      method: 'GET',
      url: $api.urls().getCurriculumLessonDetail,
      params: {
        lessonId
      }
    })
  },
  // 获取center组课程信息
  getCenterLessonDetail (lastWeeklyPlanId, itemId) {
    return axios({
      method: 'GET',
      url: $api.urls().getCenterLessonDetail,
      params: {
        lastWeeklyPlanId,
        itemId
      }
    })
  },
  // 查询课程最新草稿详情
  getLessonLastDraftDetail(lessonId) {
    return axios({
      url: $api.urls().getLessonLastDraftDetail,
      params: {lessonId}
    })
  },
  // 复制课程
  copyLesson (lessonId, useBeforeName = false) {
    return axios({
      method: 'POST',
      url: $api.urls().copyLesson,
      params: { lessonId, useBeforeName }
    })
  },
  // 复制改编课程
  copyAdaptLesson(lessonId) {
      return axios({
          method: 'POST',
          url: $api.urls().copyAdaptLesson,
          params: {lessonId}
      })
  },
  // 恢复课程
  recoverLesson(lessonId) {
    return axios({
      method: 'POST',
      url: $api.urls().recoverLesson,
      params: {lessonId}
    })
  },
  // 恢复课程
  recoverDeletedLesson(lessonId) {
    return axios({
      method: 'POST',
      url: $api.urls().recoverDeletedLesson,
      params: {lessonId}
    })
  },
  // 推荐课程
  promoteLesson(lessonId) {
    return axios({
      method: 'post',
      url: $api.urls().promoteLesson,
      params: {lessonId}
    })
  },
  // 取消推荐课程
  cancelPromoteToAgency(lessonId) {
    return axios({
      method: 'post',
      url: $api.urls().cancelPromoteToAgency,
      params: {lessonId}
    })
  },
  // 删除课程
  deleteLesson(lessonId) {
    return axios({
      method: 'post',
      url: $api.urls().deleteLesson,
      params: {lessonId}
    })
  },
  // 判断课程是否有推荐的权限
  getLessonRecommendStatus(lessonId) {
    return axios({
      method: 'GET',
      url: $api.urls().getLessonRecommendStatus,
      params: {lessonId}
    })

  },
  // 管理端课程过滤
  getCenterTeachers() {
    return axios({
      method: 'GET',
      url: $api.urls().getCenterTeachers,
      baseURL: configBaseUrl
    })
  },
  // 获取机构的员工
  getAgencyStaffs() {
    return axios({
      method: 'GET',
      url: $api.urls().getAgencyStaffs,
      baseURL: configBaseUrl
    })
  },
  // 查询老师是否点过对话框提示
  getTeacherDialog() {
    return axios({
      method: 'get',
      url: $api.urls().getTeacherDialog,
      baseURL: configBaseUrl
    })
  },
  // 插入对话框提示记录
  addTipRecords() {
    return axios({
      method: 'post',
      url: $api.urls().addTipRecords,
      baseURL: configBaseUrl
    })
  },
  // 查询管理端教师课程
  getManageTeacherLessons(param) {
    return axios({
      method: 'GET',
      url: $api.urls().getManageTeacherLessons,
      params: param,
      baseURL: configBaseUrl
    })
  },
  // 查询课程配置相关老师信息
  getUserOpen(metaKeys) {
    return axios({
      method: 'GET',
      url: $api.urls().getUserOpen,
      params: { metaKeys:metaKeys.join(',') },
      baseURL: configBaseUrl
    })
  },
  // 课程配置
  setUserOpen(userIds, metaKey, configure) {
    return axios({
      method: 'post',
      url: $api.urls().setUserOpen,
      data: {
        userIds: userIds,
        metaKey: metaKey,
        metaValue: configure
      },
      baseURL: configBaseUrl
    })
  },
  generateLessonPDF (lessonId, mappedFrameworkId, showDescription, onlyShowCore, showClrSource, langCode, showImpStepSourceMap, showMappedTypicalBehaviors) {
    return axios({
      method: 'post',
      url: $api.urls().generateLessonPDF,
      params: { lessonId, mappedFrameworkId, showDescription, onlyShowCore, showClrSource, ...((langCode && langCode !== '') && { langCode }), showImpStepSourceMap, showMappedTypicalBehaviors }
    })
  },
  // 获取学校
  getCenters (userId) {
    return axios({
      method: 'GET',
      url: $api.urls(userId).getCenterIdNameByUserId
    })
  },
  // 获取学校下的班级及学生
  getGroupsAndEnrollments (centerId) {
    return axios({
      method: 'GET',
      url: $api.urls("", centerId).getCenterById
    })
  },
  // 分享课程
  shareLesson(localId, childIds, lessonName, annexMediaIds, props, userId, createTime, from, to) {
    return axios({
      method: 'POST',
      url: $api.urls().shareLesson,
      data: {
        local_id: localId,
        type: "LESSON",
        Children: childIds,
        payload: lessonName,
        annexMedias: annexMediaIds,
        props: props,
        user_id: userId,
        createTime: createTime,
        from: from,
        to: to,
        batchAddType: "add"
      }
    })
  },

  // 添加主题
  addLessonTheme (params) {
    return axios.post(
      $api.urls().addLessonTheme,
      params
    )
  },

  // 删除主题
  deleteLessonTheme (queryParams) {
    return axios.post(
      $api.urls().deleteLessonTheme,
      {},
      {
        params: queryParams
      }
    )
  },
  // 获取课程封面
  generateLessonCover (params) {
    return axios.post(
      $api.urls().generateLessonCover,
      params
    )
  },
  // 提取和搜索课程封面
  extractAndSearchLessonCover (params) {
    return axios.post(
      $api.urls().extractAndSearchLessonCover,
      params
    )
  },
  // 获取单元编辑状态
  getUnitEditStatus (unitId) {
    return axios.get(
      $api.urls().getUnitEditStatus,
      {
        params: {
          unitId
        }
      }
    )
  },
  // 创建批量生成课程任务
  createGenerateLessonTasks (params) {
    return axios.post(
      $api.urls().createGenerateLessonTasks,
      params
    )
  },
  // 获取批量生成课程任务
  getBatchGenerateLessonTasks (batchId) {
    return axios.get(
      $api.urls().getBatchGenerateLessonTasks,
      {
        params: {
          batchId
        }
      }
    )
  },
  // 生成课程详情
  generateLessonDetail (taskId) {
    return axios.post(
      $api.urls().generateLessonDetail,
      {
        taskId
      }
    )
  },
  // 重新执行生成课程任务
  batchGenerateTasks (taskIds) {
    return axios({
      method: 'POST',
      url: $api.urls().batchGenerateLesson,
      params: {
        taskIds
      }
    })
  },
  // 停止生成任务
  stopGenerateTasks (taskIds) {
    return axios({
      method: 'POST',
      url: $api.urls().stopGenerateTasks,
      params: {
        taskIds
      }
    })
  },
  // 获取单元正在生成课程的任务
  getUnitGeneratingLessonTask (unitId) {
    return axios({
      method: 'GET',
      url: $api.urls().getUnitGeneratingLessonTask,
      params: {
        unitId
      }
    })
  },
  // 增加单元的操作记录信息
  addAction (params) {
    return axios.post(
      $api.urls().addAction,
      {},
      {
        params
      }
    )
  },
  // 批量更新周计划项课程
  batchUpdateUnitPlanItemLesson (params) {
    return axios.post(
      $api.urls().batchUpdateUnitPlanItemLesson,
      params
    )
  },

  /**
   * 更新周计划测评点
   *
   * @param params
   * @returns {*}
   */
  updateWeeklyMeasureIds (params) {
    return axios.post(
      $api.urls().updateWeeklyMeasureIds,
      params
    )
  },

  /**
   * 更新周计划 items 基础信息
   * @param params
   * @returns {*}
   */
  updatePlanItemsInfo (params) {
    return axios.post(
      $api.urls().updatePlanItemsInfo,
      params
    )
  },

  // 获取单个媒体资源
  getLessonMediaModel (mediaId) {
    return axios({
      method: 'GET',
      url: $api.urls().getLessonMediaModel,
      params: {
        mediaId
      }
    })
  },

  /**
   * 更新单元下的周计划项
   */
  updateUnitItem(params) {
    return axios.post(
      $api.urls().updateUnitItem,
      params
    )
  },

  /**
   * 检查课程是否包含书籍
   */
  checkLessonContainBooks (params) {
    return axios({
      method: 'GET',
      url: serverlessApiUrl + $api.urls().checkLessonContainBooks,
      params
    })
  },

  /**
   * 获取课程模板列表
   */
  getLessonTemplates (lessonId, ageGroup) {
    return axios({
      method: 'GET',
      url: $api.urls().getLessonTemplates,
      params: {
        lessonId,
        ageGroup
      }
    })
  },

  /**
   * 删除课程模板
   */
  deleteLessonTemplate (id) {
    return axios({
      method: 'POST',
      url: $api.urls().deleteLessonTemplate,
      params: {
        id
      }
    })
  },

  /**
   * 评估周计划项 idea
   *
   * @param params
   * @returns {*}
   */
  evaluationLessonIdeas (params) {
    return axios.post(
      $api.urls().evaluationLessonIdeas,
      params
    )
  },

  /**
   * 评估课程详情
   *
   * @param params
   * @returns {*}
   */
  evaluationLessonDetail (params) {
    return axios.post(
      $api.urls().evaluationLessonDetail,
      params
    )
  },

  /**
   * 分配区角周活动到课程周
   *
   * @param params
   * @returns {*}
   */
  distributeCornerActivities (params) {
    return axios.post(
      $api.urls().distributeCornerActivities,
      params
    )
  },

  /**
   * 根据单元 ID 查询区角周的原始数据
   *
   * @param unitId 单元 ID
   * @returns {*}
   */
  getOriginalCornerData (unitId) {
    return axios.get(
      $api.urls().getOriginalCornerData,
      {
        params: {
          unitId
        }
      }
    )
  },

  /**
   * 向课程周添加指定区角活动项
   *
   * @param params
   * @returns {*}
   */
  addCornerItem (params) {
    return axios.post(
      $api.urls().addCornerItem,
      params
    )
  },

  /**
   * 向课程周添加指定区角活动项
   *
   * @param params
   * @returns {*}
   */
  removeCornerItem (params) {
    return axios.post(
      $api.urls().removeCornerItem,
      params
    )
  },

  /**
   * 切换课程周活动项
   *
   * @param params
   * @returns {*}
   */
  switchCornerItem (params) {
    return axios.post(
      $api.urls().switchCornerItem,
      params
    )
  },

  /**
   * 获取课程历史版本列表
   *
   * @lessonId 课程 ID
   * @page 页码
   * @pageSize 每页条数
   * @returns 课程历史版本列表
   */
  getLessonVersions(lessonId, page, pageSize) {

    return axios.get(
      $api.urls().getLessonVersions,
      {
        params: {
          lessonId,
          page,
          pageSize
        }
      }
    )
  },

  /**
   * 获取课程历史版本详情
   *
   * @versionId 版本 ID
   * @returns 课程历史版本详情
   */
  getLessonVersionDetail(versionId) {
    return axios.get(
      $api.urls().getLessonVersionDetail,
      {
        params: {
          versionId
        }
      }
    )
  },

  // 获取单元资源
  getUnitResources(params) {
    return axios.get($api.urls().getUnitResources, { params: params })
  },

  /**
   * 下载材料资源
   * @param {Object} params - 请求参数
   * @param {String} params.unitId - 单元 ID
   * @param {String} params.planId - 周计划 ID
   * @returns {Promise} 返回下载信息
   */
  downloadMaterials(params) {
    return axios.get($api.urls().downloadMaterials, { params })
  },

  /**
   * 导出单元词汇
   * @param {Object} params - 请求参数
   * @param {String} params.unitId - 单元 ID
   * @returns {Promise} 返回导出信息
   */
  exportVocabularies(params) {
    return axios.post($api.urls().exportVocabularies, {}, { params: params })
  },
  
  /**
   * 下载 Quiz 文件
   * @param {Object} params - 请求参数
   * @param {String} params.unitId - 单元 ID，当下载整个单元的 Quiz 时使用
   * @param {String} params.lessonId - 课程 ID，当下载单个课程的 Quiz 时使用
   * @param {String} params.type - 下载类型：ALL（默认）、WITH_ANSWER、WITHOUT_ANSWER
   * @returns {Promise} 返回下载信息
   */
  createDownloadQuizTask(params) {
    return axios.post($api.urls().createDownloadQuizTask, params)
  },

  /**
   * 获取下载 Quiz 文件任务
   * @param {Object} params - 请求参数
   * @param {String} params.taskId - 任务 ID
   */
  getDownloadQuizTask(taskId) {
    return axios.get($api.urls().getDownloadQuizTask, { params: { taskId } })
  },

  /**
   * 搜索图片
   * @param {Object} params - 请求参数
   * @param {String} params.keyword - 搜索关键词
   * @param {Number} params.page - 页码
   * @param {Number} params.pageSize - 每页条数
   * @returns {Promise} 返回搜索结果
   */
  searchCover(params) {
    return axios.get($api.urls().searchCover, { params })
  },

  /**
   * 设置图片封面信息
   * @param {Object} params - 请求参数
   * @returns {Promise} 返回设置结果
   */
  setCoverInfo(params) {
    return axios.post($api.urls().setCoverInfo, params)
  },

  /**
   * 设置课程 slides 中的图片
   * @param {Object} params - 请求参数
   * @returns {Promise} 返回设置结果
   */
  setLessonSlideImage(imageUrl) {
    return axios.post(serverlessApiUrl + $api.urls().setLessonSlideImage, {}, {
      params: {
        imageUrl
      }
    })
  },

  /**
   * 保存用户选择的 objective 类型
   * 
   * @param objectiveType 用户选择的 objective 类型
   */
  setLessonObjectiveType(objectiveType) {
    return axios.post(serverlessApiUrl + $api.urls().setLessonObjectiveType, {}, {
      params: {
        objectiveType
      }
    })
  },

  /**
   * 创建 Slides 后再下载
   * @param {String} lessonId - 课程 ID
   */
  getLessonSlidesDownloadModel (lessonId) {
    return axios.get($api.urls().getLessonSlidesDownloadModel, { params: { lessonId } })
  },

  /**
   * 创建模板后再下载
   * @param {String} lessonId - 课程 ID
   */
  getLessonTemplateDownloadModels (lessonId) {
    return axios.get($api.urls().getLessonTemplateDownloadModels, { params: { lessonId } })
  },

  /**
   * 多个模板打包
   * @param {String} lessonId - 课程 ID
   */
  createLessonTemplatesDownload (lessonId) {
    return axios.get($api.urls().createLessonTemplatesDownload, { params: { lessonId } })
  },

  /**
   * 校验 URL
   * @param {String} url - 文件地址
   */
  checkUrlViaApi (url) {
    return axios.get($api.urls().validateUrl, { params: { url } })
  },

  /**
   * 解析用户向 Unit 导入的文件
   * @param params - 请求
   */
  performImportUnitContentFromUrl (params) {
    return axios.post($api.urls().performImportUnitContentFromUrl, params)
  },

  /**
   * 使用 prompt 分析提取完的文档信息
   * @param params - 请求
   */
  analyzeExtractedContent (params) {
    return axios.post($api.urls().analyzeExtractedContent, params)
  },

  /**
   * 用户使用样例课程
   * @param params - 请求
   */
  userExampleImportUnit (params) {
    return axios.post($api.urls().userExampleImportUnit, params)
  },

  /**
   * 使用 ID 查询分析结果
   * @param id - 结果 ID
   */
  getAnalyzeContentById (id) {
    return axios.get($api.urls().getAnalyzeContentById, { params: { id } })
  },

  /**
   * 使用 ID 查询 OCR 识别结果
   * @param id - 结果 ID
   */
  getOcrResultById (id) {
    return axios.get($api.urls().getOcrResultById, { params: { id } })
  },

  /**
   * 保存用户修改后的分析结果
   * @param params - 入参
   */
  saveUserChangeAnalyzeResult (params) {
    return axios.post(serverlessApiUrl + $api.urls().saveUserChangeAnalyzeResult, params)
  },

  /**
   * 学科校验
   * @param params - 入参
   */
  checkSubjects (params) {
    return axios.post(serverlessApiUrl + $api.urls().checkSubjects, params)
  }
}
