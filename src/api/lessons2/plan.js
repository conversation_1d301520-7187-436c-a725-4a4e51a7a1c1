import axios from '@/utils/axios'
import qs from 'qs'
export default {
  /**
   * 获取周计划列表
   */
  listPlans (params) {
    return axios.post(
      $api.urls().listPlans,
      params
    )
  },

  /**
   * 获取周计划列表
   */
   listAdminTemplates (params) {
    return axios.post(
      $api.urls().listAdminTemplates,
      params
    )
  },

  /**
   * 通过用户 ID 获取周计划
   */
  getAllPlanByUserId (params) {
    return axios.get(
      $api.urls().getAllPlanByUserId,
      {
        params: params
      }
    )
  },

  /**
   * 创建周计划
   */
  createPlan (params) {
    return axios.post(
      $api.urls().createPlan,
      params
    )
  },

  /**
   * 获取周计划详情
   */
  getPlan (params) {
    return axios.get(
      $api.urls().getPlan,
      {
        params: params
      }
    )
  },

  /**
   * 获取周计划的中所有的可以映射的框架的 ID 集合
   */
  getPlanMappedFrameworkIds (params) {
    return axios.get(
      $api.urls().getPlanFrameworkIds,
      {
        params: params
      }
    )
  },

  /**
   * 解锁编辑状态
   */
  unlockEditing (params) {
    return axios.get(
      $api.urls().unlockEditing,
      {
        params: params
      }
    )
  },

  /**
   * 获取班级中老师
   */
  getGroupTeachers (params) {
    return axios.get(
      $api.urls().getGroupTeachers,
      {
        params: params
      }
    )
  },

  /**
   * 添加周计划分类
   */
  createCategory (params) {
    return axios.post(
      $api.urls().createPlanItemCategory,
      params
    )
  },

  /**
   * 删除周计划分类
   */
  deleteCategory (params, queryParams) {
    return axios.post(
      $api.urls().deletePlanItemCategory,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 添加周计划分类
   */
   updateCategory (params) {
    return axios.post(
      $api.urls().updatePlanItemCategory,
      params
    )
  },

  /**
   * 更新周计划基础信息
   */
   updatePlanBaseInfo (params) {
    return axios.post(
      $api.urls().updatePlanBaseInfo,
      params
    )
  },

  /**
   * 获取框架及测评点信息
   */
  getFrameworkInfo (params) {
    return axios.get(
      $api.urls().getFrameworkInfo,
      {
        params: params
      }
    )
  },

  /**
   * 获取班级框架列表
   */
   getGroupFrameworkMeasures (params) {
    return axios.get(
      $api.urls().getGroupFrameworkMeasures,
      {
        params: params
      }
    )
  },

  /**
   * 获取框架测评点信息
   */
  getFrameworkMeasures (params) {
    return axios.get(
      $api.urls().getFrameworkMeasures,
      {
        params: params
      }
    )
  },

  /**
   * 获取框架映射测评点信息
   */
  getFrameworkMeasureMap (params) {
    return axios.get(
      $api.urls().getFrameworkMeasureMap,
      {
        params: params
      }
    )
  },

  /**
   * 获取小孩列表
   */
  getChildren (params) {
    return axios.get(
      $api.urls().manageChildren,
      {
        params: params
      }
    )
  },

  /**
   * 创建周计划项目
   */
  createItem (params) {
    return axios.post(
      $api.urls().createPlanItem,
      params
    )
  },

  /**
   * 更新周计划项目
   */
  updateItem (params) {
    return axios.post(
      $api.urls().updatePlanItem,
      params
    )
  },

  /**
   * 更新周计划项目
   */
  updateUnitOverviewItemInfo (params) {
    return axios.post(
      $api.urls().updateUnitOverviewItemInfo,
      params
    )
  },

  listWeeks (params) {
    return axios.get(
      $api.urls().listPlanWeeks,
      {
        params: params
      }
    )
  },

  /**
   * 更新周计划详情
   */
  updatePlan (params) {
    return axios.post(
      $api.urls().updatePlan,
      params
    )
  },

  /**
   * 周计划提交审核
   */
  submitReview (params) {
    return axios.post(
      $api.urls().submitPlanReview,
      params
    )
  },

  /**
   * 管理员提交周计划模板
  */
  submitAdminTemplate (params) {
    return axios.post(
      $api.urls().submitAdminTemplate,
      params
    )
  },

  /**
   * 撤回提交的周计划
   */
  revertReview (params) {
    return axios.post(
      $api.urls().revertPlanReview,
      params
    )
  },

  review (params) {
    return axios.post(
      $api.urls().reviewPlan,
      params
    )
  },

  /**
   * 保存周计划反思
   */
  savePlanReflection (params) {
    return axios.post(
      $api.urls().savePlanReflection,
      params
    )
  },

  /**
   * 获取周计划反思
   */
  getPlanReflection (params) {
    return axios.get(
      $api.urls().getPlanReflection,
      {
        params: params
      }
    )
  },

  /**
   * 删除周计划
   */
  deletePlan (params, queryParams) {
    return axios.post(
      $api.urls().deletePlan,
      params,
      {
        params: queryParams
      }
    )
  },

  getPlanPDF (params) {
    return axios.get(
      $api.urls().getPlanPDF,
      {
        params: params
      }
    )
  },

  getPDFList (params) {
    return axios.get(
      $api.urls().pdfList,
      {
        params: params
      }
    )
  },

  searchLessons (params) {
    return axios.get(
      $api.urls().searchLessons,
      {
        params: params
      }
    )
  },

  replicatePlan (params) {
    return axios.post(
      $api.urls().replicatePlan,
      params
    )
  },

  getObservationStatiscs (params) {
    return axios.get(
      $api.urls().getObservationStatiscs,
      {
        params: params,
        paramsSerializer: params => qs.stringify(params, { arrayFormat: 'indices', allowDots: true })
      }
    )
  },

  getDomainScoreStats (params) {
    return axios.get(
      $api.urls().getDomainScoreStats,
      {
        params: params
      }
    )
  },

  getBenchmarks (params) {
    return axios.get(
      $api.urls().getCohortViewList,
      {
        params: params
      }
    )
  },

  getLessonReflection (params) {
    return axios.get(
      $api.urls().getLessonReflection,
      {
        params: params
      }
    )
  },

  saveLessonReflection (params) {
    return axios.post(
      $api.urls().saveLessonReflection,
      params
    )
  },

  deleteLessonReflection (params, queryParams) {
    return axios.post(
      $api.urls().deleteLessonReflection,
      params,
      {
        params: queryParams
      }
    )
  },

  listLessonReflections (params) {
    return axios.get(
      $api.urls().listLessonReflections,
      {
        params: params
      }
    )
  },

  listLessonsWithReflections (params) {
    return axios.get(
      $api.urls().listLessonsWithReflections,
      {
        params: params
      }
    )
  },

  getLessonNotes (params) {
    return axios.get(
      $api.urls().getLessonNotes,
      {
        params: params
      }
    )
  },

  getTopInterests (params) {
    return axios.get(
      $api.urls().getTopInterests,
      {
        params: params
      }
    )
  },
  // 添加讲解：planId、mediaId
  addInterpret(interpret) {
    return axios.post('/lessons2/plans/addInterpret', interpret);
  },
  // 删除讲解
  deleteInterpret(interpretId) {
    return axios.post('/lessons2/plans/deleteInterpret', {}, {params: {interpretId}});
  },
  // 查询讲解
  listInterprets(planId) {
    return axios.get('/lessons2/plans/listInterprets', {params: {planId}});
  },
  // 设置周计划讲解新手引导记录
  setIntroduced() {
    return axios.post('/lessons2/plans/setIntroduced',{});
  },
  // 隐藏周计划讲解红点
  hideNewBadge() {
    return axios.post('/lessons2/plans/hideNewBadge',{});
  },
  // 添加或修改笔记：id修改必填、planId、content
  addOrUpdateNote(note) {
    return axios.post('/lessons2/plans/addOrUpdateNote', note);
  },
  // 删除笔记
  deleteNote(noteId) {
    return axios.post('/lessons2/plans/deleteNote', {}, {params: {noteId}});
  },
  // 查询我的笔记，planId选填
  listNotes(planId, pageSize, pageNum) {
    return axios.get('/lessons2/plans/listNotes', {params: {planId, pageSize, pageNum}});
  },
  // 评论:content、planId、parentCommentId
  commentPlan(comment) {
    return axios.post('/lessons2/plans/commentPlan', comment);
  },
  // 删除评论
  deletePlanComment(commentId) {
    return axios.post('/lessons2/plans/deleteComment', {}, {params: {commentId}});
  },
  // 查询根评论
  getPlanComments(planId, pageSize, pageNum) {
    return axios.get('/lessons2/plans/getComments', {params: {planId, pageSize, pageNum}});
  },
  // 查询子评论
  getPlanSubComments(rootCommentId, pageSize, pageNum) {
    return axios.get('/lessons2/plans/getSubComments', {params: {rootCommentId, pageSize, pageNum}});
  },

  /**
   * 获取分享周计划列表
   */
   listSharedPlans (params) {
     return axios.post(
       $api.urls().listSharedPlans,
       params
     )
   },

   /**
    * 获取周计划分享已读统计
    */
    getSharedReadStats (params) {
      return axios.get(
        $api.urls.getSharedReadStats,
        {
          params: params
        }
      )
    },

    /**
     * 取消周计划分享
     */
     recallShadowed (params, queryParams) {
      return axios.post(
        $api.urls().deleteShareRecord,
        params,
        {
          params: queryParams
        }
      )
    },
    /**
     * 查询周计划家庭互动资源
     */
    listEngagementResources(planId) {
      return axios.get('/lessons2/plans/listEngagementResources', {params: {planId}});
    },
  // 添加周计划 planCenter
  addPlanCenter (params) {
    return axios.post(
      $api.urls().addPlanCenter,
      params
    )
  },
  // 删除周计划 planCenter
  deletePlanCenter (centerId) {
    return axios.post($api.urls().deletePlanCenter,{},{params: { centerId } });
  },
  // 更新周计划 planCenter
  updatePlanCenter (params) {
    return axios.post(
      $api.urls().updatePlanCenter,
      params
    )
  },

  /**
   * 创建单元课程周计划
   */
  createCurriculumWeekPlan (params, queryParams) {
    return axios.post(
      $api.urls().createCurriculumWeekPlan,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 删除Item
   */
  deleteItem (itemId, planId) {
    return axios.post(
      $api.urls().deletePlanItem,
      {},
      {params: { itemId,planId } }
    )
  },

  /**
   * 结束周计划 apply 流程
   */
  endApplyFlowPath (batchId) {
    return axios.post(
      $api.urls().endApplyFlowPath,
      {},
      { params: { batchId } }
    )
  },
  /**
   * 删除 planCenter 标签
   */
  deleteTag (name) {
    return axios.post(
      $api.urls().deleteTag,
      {},
      { params: { name } }
    )
  },

  /**
   * 获取单元周计划详情
   */
  getUnitPlan (params) {
    return axios.get(
      $api.urls().unitPlan,
      {
        params: params
      }
    )
  },

  /**
   * 隐藏班级信息确认弹窗
   */
  hideGroupInfoConfirm (groupId) {
    return axios.post(
      $api.urls().hideGroupInfoConfirmTip,
      {},
      {
        params: { groupId }
      }
    )
  },

  /**
   * 设置班级改编课程是否开启老师分组
   */
  setTeacherGroupEnabled (groupId, enabled) {
    return axios.post(
      $api.urls().setTeacherGroupEnabled,
      {},
      {
        params: { groupId, enabled }
      }
    )
  },

  /**
   * 保存混龄班级元数据
   * @param params 参数
   * @param queryParams 查询参数
   */
  saveOpenMixedAgeGroup (params, queryParams) {
    return axios.post(
      $api.urls().saveOpenMixedAgeGroup,
      params,
      {
        params: queryParams
      }
    )
  },

  /**
   * 获取班级 Adapt 课程设置
   */
  getGroupAdaptSetting (groupId) {
    return axios.get(
      $api.urls().getGroupAdaptSetting,
      {
        params: { groupId }
      }
    )
  },
  /**
   * 重新执行失败的任务
   * @param failTaskIds 失败的任务 id 数组
   */
  retryFailedTasks (failTaskIds) {
    return axios.post(
      $api.urls().batchAdaptLessonTasks,
      {},
      {
        params: { taskIds: failTaskIds }
      }
    )
  },
  /**
   * 锁定周计划
   * @returns {AxiosPromise}
   */
  lockPlan (planId, userId) {
    return axios({
      method: 'POST',
      url: $api.urls().lockPlan,
      params: { planId, userId }
    })
  },

  /**
   * 更新周计划校训
   *
   * @param params
   * @returns {*}
   */
  updateWeeklyRubrics (params) {
    return axios.post(
      $api.urls().updateWeeklyRubrics,
      params
    )
  }
}
