<template>
<!-- DLL 中选择语言的弹框 -->
  <div>
    <el-dialog custom-class="div-select-language"  :title="title" width="550px"  :visible.sync="SelectLanguageDialogVisible" :close-on-click-modal=false  :lock-scroll="false" @close="beforeCloseLanguageDialog" top="8vh" >

      <!-- 搜索语言 -->
      <div class="div-content-select-language">
        <el-input
          class="w-full"
          :placeholder="$t('loc.searchLanguage')"
          suffix-icon="el-icon-search"
          @input="searchLanguage"
          v-model="searchedLanguage">
        </el-input>
      </div>

      <div v-if="haveSelectedLanguageList.length === 0" class="divide-gap-theme-background"></div>

      <!-- 已选择的语言集合 -->
      <div v-if="haveSelectedLanguageList.length !== 0">
        <div class="w-full height-28 add-padding-l-16 line-height-28 content-vertical-middle display-inline-block" style="background-color: #f5f6f8">
          {{$t('loc.selectedLanguages')}}
        </div>
        <transition-group name="drag" class="have-language-list" tag="ul">
          <li
            v-for="(item,index) in haveSelectedLanguageList"
            :key="item.code"
            class="language-item"
            @dragenter="dragenter($event, index)"
            @dragover="dragover($event, index)"
            @dragstart="dragstart(index)"
            draggable="true"
          >
            <div class="div-language-item">
              <img src="../../assets/img/dll/icon_subtraction.png" @click.stop="cancelSelectLanguage(item,index)" class="lg-pointer add-padding-r-8">
              <div class="lg-pointer display-inline-flex flex-direction-col flex-grow-1" @click.stop="cancelSelectLanguage(item,index)" >
                <span class="dll-text-title-color font-size-16">{{item.name}}</span>
                <span class="dll-text-title-color font-size-14">{{item.originalName}}</span>
              </div>
              <img src="../../assets/img/dll/menu.png" alt="">
            </div>
          </li>
        </transition-group>
      </div>
    <!-- 其他语言的集合 -->
      <div v-if="haveSelectedLanguageList.length !== 0" class="w-full height-28 add-padding-l-16 line-height-28 content-vertical-middle display-inline-block" style="background-color: #f5f6f8">
        {{$t('loc.otherLanguages')}}
      </div>
      <div v-for="(item,index) in otherLanguageList" :key="item.code" class="display-flex flex-direction-col">
        <div @click.stop="selectLanguage(item,index)" class="lg-pointer flex-row-vertical-center add-padding-tb-10 add-padding-lr-16" >
          <img src="../../assets/img/dll/icon_add.png" class="add-padding-r-8">
          <div class="display-inline-flex flex-direction-col flex-grow-1">
            <span class="dll-text-title-color font-size-16">{{item.name}}</span>
            <span class="dll-text-title-color font-size-14">{{item.originalName}}</span>
          </div>
      </div>
        <span class="divide-line-gray"></span>
      </div>

      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button @click="cancelLanguageDialog" plain="" class="cancelBtn">{{$t('loc.cancel')}}</el-button>
        <el-button :loading="sureSettingLanguageLoading" :disabled="haveSelectedLanguageList.length === 0" type="primary" @click="sureSelectLanguage()" class="saveBtn">{{$t('loc.save')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SelectLanguageDialog',
  data () {
    return {
      title: this.$t('loc.selectYourLanguage'),
      SelectLanguageDialogVisible: false, // 选择语言弹框是否显式
      searchedLanguage: '', // 搜索的内容
      haveSelectedLanguageList: [], // 已经选择的语言集合
      otherLanguageList: [], // 未选择的语言集合，配合搜索动态变化
      originLanguageArray: [], // 原有的未选择的语言集合
      dragIndex: '', // 要拖拽源对象的位置
      enterIndex: '', // 拖拽之后的位置
      groupId: '', // 班级id
      sureSettingLanguageLoading: false // 设置选择语言的loading
    }
  },
  methods: {
    // 源对象开始被拖动时触发
    dragstart (index) {
      this.dragIndex = index
    },
    // 源对象开始进入目标对象范围内触发
    dragenter (e, index) {
     // dragenter的默认行为是拒绝接受任何被拖放的元素,所以这里使用preventDefault来阻止浏览器的默认行为。
      e.preventDefault()
      // 避免源对象触发自身的dragenter事件
      if (this.dragIndex !== index) {
        const source = this.haveSelectedLanguageList[this.dragIndex]
        // 1.先删除要拖拽位置的元素
        this.haveSelectedLanguageList.splice(this.dragIndex, 1)
        // 2.在要插入的位置放置原拖拽的元素
        this.haveSelectedLanguageList.splice(index, 0, source)
        // 排序变化后目标对象的索引变成源对象的索引
        this.dragIndex = index
      }
    },
    // 源对象在目标对象范围内移动时触发
    dragover (e) {
      // dragover的默认行为是拒绝接受任何被拖放的元素,所以这里使用preventDefault来阻止浏览器的默认行为。
      e.preventDefault()
    },
    shuffle () {
      this.haveSelectedLanguageList = this.$shuffle(this.haveSelectedLanguageList)
    },
    // 弹出选择语言的弹框
    show (selectGroupId) {
      this.groupId = selectGroupId
      this.SelectLanguageDialogVisible = true
      this.getHaveSelectedLanguage()
    },
    // 选中某个语言
    selectLanguage (item,index) {
      // 添加到已选择的语言数组中
      this.haveSelectedLanguageList.push(item)
      // 从其他语言数组中删除该选择的语言
      this.otherLanguageList.splice(index,1)
      // 从原有语言数组中也删除该选择的语言
      this.originLanguageArray.splice(this.originLanguageArray.indexOf(item),1)
    },
    // 取消选中某个语言
    cancelSelectLanguage (item,index) {
      // 添加到已选择的语言数组中
      this.otherLanguageList.push(item)
      // 添加到原有的语言数组中
      this.originLanguageArray.push(item)
      // 从其他语言数组中删除该选择的语言
      this.haveSelectedLanguageList.splice(index,1)
    },
    // 确认设置班级选择的语言
    sureSelectLanguage () {
      let updateSelectedLanguageList = []
      let selectedLanguageCodes = ''
      let languageCount = this.haveSelectedLanguageList.length
      if (languageCount > 10) {
        this.$message.error(this.$t('loc.limitSelectLanguageNum'))
        return
      }
      this.haveSelectedLanguageList.forEach(function (languageItem, index) {
        let tempLanguageObject = {}
        tempLanguageObject.langCode = languageItem.code
        tempLanguageObject.sortIndex = index + ''
        updateSelectedLanguageList.push(tempLanguageObject)
        if (index === languageCount - 1) {
          selectedLanguageCodes += languageItem.code
        } else {
        selectedLanguageCodes += languageItem.code + ','
        }
      })
      this.sureSettingLanguageLoading = true
       this.$axios.post($api.urls().setGroupLanguage,{
        groupId: this.groupId,
        languageLists: updateSelectedLanguageList
      })
        .then(data => {
          // 关闭设置的loading
          this.sureSettingLanguageLoading = false
          // 给添加界面回传值
          this.$emit('func',selectedLanguageCodes)
          // 关闭语言设置的弹框
          this.beforeCloseLanguageDialog()
          this.SelectLanguageDialogVisible = false
        }).catch(error => {
         this.sureSettingLanguageLoading = false
         this.$message.error(error.response.data.error_message)
      })
    },
    searchLanguage () {
      let searchContent = this.searchedLanguage
      if (searchContent.trim().length === 0) {
        // 清空
        this.otherLanguageList.splice(0,this.otherLanguageList.length)
        this.originLanguageArray.forEach(languageItem => {
          this.otherLanguageList.push(languageItem)
        })
      } else {
        let searchLanguageArray = []
        this.otherLanguageList = [] // 展示的语言先置空
        // 根据语言的英语名称和原有名称进行搜索对比
        this.originLanguageArray.forEach(languageItem => {
          if (languageItem.name.toLowerCase().indexOf(searchContent.toLowerCase()) != -1 || languageItem.originalName.toLowerCase().indexOf(searchContent.toLowerCase()) != -1) {
            searchLanguageArray.push(languageItem)
          }
        })
        searchLanguageArray.forEach(languageItem => {
          this.otherLanguageList.push(languageItem)
        })
      }
    },
    // 获取已经选择的语言
    getHaveSelectedLanguage () {
      this.$axios
        .get($api.urls().getGroupLanguageList + '?groupId=' + this.groupId)
        .then(res => {
          this.haveSelectedLanguageList = res.selectList
          this.otherLanguageList = res.unSelectList
          // 注意这里不能把res.unSelectList也赋值给originLanguageArray，这样他们指向同一地址，删除的话都会有影响
          this.otherLanguageList.forEach(languageItem => {
            this.originLanguageArray.push(languageItem)
          })
        })
        .catch(error => {
          this.$message.error(error.response.data.error_message)
        })
    },
    // 点击取消按钮的操作
    cancelLanguageDialog () {
      this.beforeCloseLanguageDialog()
      this.SelectLanguageDialogVisible = false
    },
    // 关闭界面之前的操作
    beforeCloseLanguageDialog () {
      this.searchedLanguage = ''
      this.haveSelectedLanguageList = []
      this.otherLanguageList = []
      this.originLanguageArray = []
      this.$emit('closeSelectLanguageDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.have-language-list{
  list-style: none;
  .drag-move {
    transition: transform .3s;
  }
  .language-item {
    width: 100%;
    background: white;
    cursor: move;
    .div-language-item {
      display: inline-flex;
      width: 100%;
      flex-direction: row;
      align-items: center;
      padding: 9px 16px;
    }
  }
}
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}
.cancelBtn{
  color: #606266;
  font-size: 16px;
  padding: 10px 18px !important;
}
.saveBtn{
  color: #FFFFFF;
  font-size: 16px;
  padding: 10px 18px !important;
}
.div-content-select-language {
  margin: 13px 16px;
}
.height-28 {
  height: 28px;
}
</style>
<style>
.div-select-language  .el-dialog__footer {
  border-top: 1px solid #EEEEEE;
  padding-top: 20px;
}
.div-select-language  .el-dialog__header {
  border-bottom: 1px solid #EEEEEE;
}
.div-select-language  .el-dialog__body {
  padding: 0 !important;
  max-height: 420px;
  min-height: 200px;
  overflow-y: auto !important;
}

.div-select-language  ::-webkit-scrollbar
{
  width: 5px;
}

/*定义滚动条轨道 内阴影+圆角*/
.div-select-language  ::-webkit-scrollbar-track
{
  background-color: white;
}
/*定义滑块 内阴影+圆角*/
.div-select-language  ::-webkit-scrollbar-thumb
{
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
  background-color: rgba(0,0,0,0.1);
}
</style>
