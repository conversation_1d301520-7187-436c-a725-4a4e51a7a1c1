<template>
  <!-- 添加DLL弹框界面 -->
  <div>
    <el-dialog :title="title" width="700px" @close="clearContent" :visible.sync="addDLLDialogVisible"
               v-loading="languageLoading" :lock-scroll="false" top="1vh" :close-on-click-modal="false"
               custom-class="add-dll-dialog-class">
      <!-- 填写 DLL 的具体内容 -->
      <div ref="dllTitleInput" class="add-margin-l-10 add-margin-r-10 add-padding-t-5">
        <el-form ref="ruleForm" :model="ruleForm" class="form-add-dll" label-position="top" :rules="rules">
          <el-form-item :label="$t('loc.addDLLTitle')" prop="subjectName">
            <el-input v-model="ruleForm.subjectName" class="title-input"
                      :placeholder="$t('loc.addDLLTitle')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('loc.addDLLContentTitle')" prop="content">
            <div class="display-flex align-items">
              <el-input
                class="content-input flex-1"
                type="textarea"
                v-model="ruleForm.content"
                resize="none"
                maxlength="200"
                show-word-limit
                :placeholder="$t('loc.addDLLContentTip')"></el-input>
              <!-- 选择照片的控件 -->
              <el-upload
                class="add-margin-l-10 add-margin-b-0"
                :class="[{'add-dll-upload-plus': uploadHavePlus}, {'add-dll-upload-no-plus': uploadHaveNoPlus}, 'add-dll-upload']"
                v-if="fileType !== 'mp4' && fileType !== 'aac'"
                v-loading="fileUploadLoading"
                list-type="picture-card"
                action
                :http-request="(file) => fileUpload(file, attach)"
                :on-success="
                (res, file, fileList) => {
                  uploadSuccess(res, file, fileList, attach)
                }
              "
                :on-change="
                (file, fileList) => {
                  upAttachChange(file, fileList, attach)
                }
              "
                :on-remove="
                (file, fileList) => {
                  handleRemove(file, attach)
                }
              "
                accept=".jpg,.png,.jpeg"
                :limit="1"
                :file-list="attach.medias"
                :before-upload="beforeAvatarUpload"
              >
                <i slot="default" class="el-icon-picture-outline"></i>
                <div slot="file" slot-scope="{ file }">
                  <img class="el-upload-list__item-thumbnail"
                       style="object-fit: cover;"
                       :src="file.url">
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview"
                          @click="handlePictureCardPreview(file)">
                      <i class="el-icon-search"></i>
                    </span>
                    <span
                      class="el-upload-list__item-delete"
                      @click="handleRemove(file, attach)">
                        <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
            </div>
          </el-form-item>

          <el-form-item :label="$t('loc.dll7')" prop="description">
            <el-input
              type="textarea"
              v-model="ruleForm.description"
              resize="none"
              :autosize="{ minRows: 3, maxRows: 3}"
              class="title-input"
              maxlength="200"
              show-word-limit
              :placeholder="$t('loc.dll5')">
            </el-input>
          </el-form-item>

          <el-form-item prop="selectedLanguageNum">
            <div class="display-flex align-items">
              <el-input
                suffix-icon="el-icon-arrow-down"
                readonly
                :placeholder="$t('loc.pleaseSelectLanguage')"
                @click.native="showSelectLanguageDialog"
                class="flex-1 select-language-input lg-pointer"
                v-model="ruleForm.selectedLanguageNum">
              </el-input>
              <el-button id="btn-translate" type="primary" class="translateBtn add-margin-l-8"
                         :loading="translateLoading"
                         :disabled="ruleForm.selectedLanguageNum.trim().length === 0 ||ruleForm.content.trim().length === 0 "
                         @click="dealTranslate">{{ $t('loc.translate') }}
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <!-- 展示翻译的内容 -->
      <div v-show="translationList && translationList.length > 0" class="show-translation-div">
        <div v-for="(item,translateIndex) in translationList" :key="item.langCode"
             class="translation-div add-margin-t-10">
          <div class="show-language-div">
            <span class="show-language-title">{{ item.originalName }} <span class="font-normal">({{ item.name }})</span></span>
            <span v-show="!item.voiceLoading && !item.isPlaying"
                  class="show-language-play lg-pointer inline-flex-row-vertical-center"
                  @click="getLanguageAudioUrl(item)"><span class="show-play-icon"></span>{{ $t('loc.play') }}</span>
            <i v-show="item.voiceLoading" class="el-icon-loading"></i>
            <!-- 展示正在播放语音的动画 -->
            <div v-if="!item.voiceLoading && item.isPlaying" class="lg-pointer inline-flex-row-vertical-center"
                 @click="getLanguageAudioUrl(item)">
              <span class="voice-playing lg-pointer"></span>
              <span class="show-language-play add-margin-l-3">{{ $t('loc.play') }}</span>
            </div>
          </div>
          <el-input v-model="item.content" type="textarea" ref="langCode" class="translation-input"
                    @change="translateInputChange(item.content,item,translateIndex)"
                    :placeholder="$t('loc.dllTranslatePlaceholder')"></el-input>
        </div>
      </div>
      <!-- 添加其他的设置 -->
      <div class="setting-title gap-8">
        {{ $t('loc.advancedSettings') }}
      </div>
      <div class="add-margin-l-10 add-margin-r-10 lg-border-radius-8 bg-f5f6f8">
        <div class="margin-auto gap-12 add-padding-tb-12 add-padding-lr-18">
          <!-- 是否开启分享功能 -->
          <div @click="clickDisableTip"
               class="add-padding-lr-10 add-padding-t-10 add-padding-b-6 flex-row-between w-full position-relative">
            <span class="font-size-16 line-height-22 font-weight-400">{{
                $t('loc.shareWithParents')
              }}</span>
            <el-switch
              v-model="shareWithParents"
              :disabled="!canEditDate"
            >
            </el-switch>
            <div v-if="!canEditDate && isShareWithParentShowTip"
                 class="position-absolute font-size-12 bottom-negative-1 text-error-color">{{ $t('loc.notEditDLL') }}
            </div>
          </div>
          <hr class="add-margin-lr-10 lg-margin-t-b-20 hr-color-border-height" v-if="shareWithParents">
          <!--  发送给全部家长  -->
          <div v-if="shareWithParents" @click="clickDisableTip"
               class="add-padding-lr-10  position-relative">
            <el-radio
              v-model="shareWithDLLParents"
              :disabled="!canEditDate"
              :label="false"
              @change="shareDLLParentChange"
              class="flex-1 h-auto add-margin-r-20 "
            >{{
                $t('loc.sendToAllParent')
              }}
            </el-radio>
            <el-radio
              v-model="shareWithDLLParents"
              :disabled="!canEditDate"
              :label="true"
              @change="shareDLLParentChange"
              class="flex-1 h-auto add-margin-l-20"
            >{{
                $t('loc.onlySendToDLLParent')
              }}
            </el-radio>
          </div>
          <!-- 选择是否要求父母发布语音作业 -->
          <div v-if="shareWithParents"
               class="add-padding-lr-10 add-padding-tb-12 flex-row-between w-full position-relative"
               @click="clickDisableTip">
            <span class="add-margin-r-10 font-size-16">{{
                $t('loc.requestSubmit')
              }}</span>
            <el-switch
              v-model="requestParents"
              :disabled="!canEditDate"
            ></el-switch>
            <div v-if="!canEditDate && isRequestRecordShowTip"
                 class="position-absolute font-size-12 bottom-negative-1 text-error-color">{{ $t('loc.notEditDLL') }}
            </div>
          </div>
        </div>
      </div>
      <!-- 选择定时发送给父母的日期和时间 -->
      <!-- 注意：这里的时间格式要在后面加上毫秒值 -->
      <div class="div-choose-date lg-pa-10 lg-pa-down-0 lg-margin-l-r-8" @click="clickDisableTip">
        <el-checkbox v-model="isScheduledSend" @change="isScheduledSendChange" :disabled="!canEditDate" class="line-height-42">
          {{ $t('loc.settingDataSend') }}
        </el-checkbox>
        <el-date-picker
          v-show="isScheduledSend || !canEditDate"
          v-model="selectedDate"
          type="datetime"
          :disabled="!canEditDate"
          class="add-margin-l-10"
          format="MM/dd/yyyy hh:mm A"
          value-format="yyyy-MM-dd HH:mm:ss.000"
          :placeholder="$t('loc.selectDateAndTime')">
        </el-date-picker>
      </div>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button @click="clickDialogCancel" plain="" class="cancelBtn">{{ $t('loc.cancel') }}</el-button>
        <el-button id="btn-save-dll" type="primary" @click="sureAdd('ruleForm')" class="saveBtn"
                   :loading="submitLoading">{{ $t('loc.save') }}</el-button>
      </span>
    </el-dialog>
    <SelectLanguageDialog ref="selectLanguageDialog" @func="getSelectedLanguage"></SelectLanguageDialog>
    <!-- 查看大图的弹框 -->
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <!-- 编辑提示对话框 -->
    <el-dialog :title="$t('loc.confirmation')" custom-class="show-edit-tip" :visible.sync="editTipVisibleDialog"
               :lock-scroll="false" width="35%" :close-on-click-modal="false" :close-on-press-escape="false">
      <span class="dll-text-title-alt-color font-size-14">{{ $t('loc.editTip') }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="editTipVisibleDialog = false">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="atOnceSubmit">{{ $t('loc.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SelectLanguageDialog from './SelectLanguageDialog'
import configBaseUrl from '@/utils/setBaseUrl'
import axios from 'axios'
import store from '@/store'

export default {
  name: 'add-dll-dialog',
  components: {
    SelectLanguageDialog
  },
  data () {
    return {
      title: '', // 弹框的title
      ruleForm: {
        subjectName: '', // DLL的标题
        content: '', // DLL的内容
        selectedLanguageNum: '', // DLL的选择语言
        description: '' // DLL 关键词描述
      },
      // 输入的内容的校验，包括是否填写和字数限制
      rules: {
        // DLL 的title
        subjectName: [
          {
            required: true,
            message: this.$t('loc.fieldIsRequired'),
            trigger: 'blur'
          },
          {
            max: 500,
            message: this.$t('loc.limitInputNum500'),
            trigger: 'blur'
          }
        ],
        // DLL的内容
        content: [
          {
            required: true,
            message: this.$t('loc.fieldIsRequired'),
            trigger: 'blur'
          },
          {
            max: 2000,
            message: this.$t('loc.limitInputNum2000'),
            trigger: 'blur'
          }
        ],
        // 选择的语言
        selectedLanguageNum: [
          {
            required: true,
            message: this.$t('loc.pleaseSelectLanguage'),
            trigger: 'change'
          }
        ]
      },
      addDLLDialogVisible: false, // 添加 DLL homework 弹框是否展示
      shareWithParents: true, // homework是否分享给家长
      requestParents: true, // 是否要求家长提交语音作业
      selectedDate: '', // 给家长发送该 DLL 作业的日期和时间,默认为空
      translateLoading: false, // 翻译的loading
      translationList: [], // 翻译的数组
      showLanguageDialog: false, // 判断是否展示选择语言的dialog
      groupId: '', // 班级id
      haveSelectedLanguageCodes: '', // 已经选择的语言code值
      homeworkId: '', // DLL 作业id，用于编辑使用，
      isAddOrEdit: 'add', // 判断是新增，还是编辑
      fileType: 'image', // 文件类型，用于编辑
      fileUploadLoading: false, // 上传图片的标识
      attach: { medias: [], mediakeys: [] }, // 上传图片的文件数组和图片id的数组
      dialogImageUrl: '', //  查看大图的url
      dialogVisible: false, // 查看大图弹框的标识,
      submitLoading: false, // add DLL 的loading
      translateBeforeText: '', // 记录点击翻译或者从编辑进行时的内容
      translateBeforeLanguage: '', // 记录点击翻译或者从编辑进行时的语言内容
      editTipVisibleDialog: false,
      languageLoading: true, // dialog正在加载的loading,
      haveSelectedLanguageList: [], // 已选择的语言
      otherLanguageList: [], // 未选择的语言,
      canEditDate: true, // 是否可以编辑时间
      uploadHavePlus: true, // 上传图片含有plus的class
      uploadHaveNoPlus: false, // 上传图片不含有plus的class
      isScheduledSend: false, // 选择发送给家长 DLL 信息的时间，false 为 立即发送 true 定时发送
      isPlayLanguage: false, // 语言的音频是否正在播放
      languageAudio: undefined, // 播放语音控制器
      isGetVoiceLoading: false, // 是否界面有获取语音的加载框
      canSendNow: true,
      shareWithDLLParents: false, // homework是否只分享给DLL家长
      isHaveDLLChild: false, // 判断是否有 DLL 小孩
      isShareWithParentShowTip: false, // 是否展示分享给父母的提示
      isShareWithDLLParentShowTip: false, // 是否展示分享给DLL父母的提示
      isRequestRecordShowTip: false // 是否要求提交语音的提示
    }
  },
  methods: {
    // 显示创建作业的弹框
    show (selectGroupId) {
      this.groupId = selectGroupId
      this.addDLLDialogVisible = true
      this.isAddOrEdit = 'add'
      this.title = this.$t('loc.pAddDLL')
      this.isScheduledSend = false
      this.canSendNow = true
      this.selectedDate = ''
      this.uploadHavePlus = true
      this.uploadHaveNoPlus = false
      this.requestParents = true
      this.canEditDate = true
      // 获取已设置的语言
      this.getHaveSelectedLanguage()
      // 获取是否该班级是否有 DLL 小孩
      this.getIsHaveDLLChild()
      // 滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.dllTitleInput) {
          this.$refs.dllTitleInput.scrollIntoView()
        }
        if (this.$refs.ruleForm) {
          // 重置输入信息，防止出现之前的红框
          this.$refs.ruleForm.resetFields()
        }
      })
      this.shareWithParents = true
    },
    // 编辑作业的弹框
    showFromEdit (selectGroupId, homeworkObject) {
      this.groupId = selectGroupId
      this.addDLLDialogVisible = true
      this.title = this.$t('loc.pEditDLL')
      // 给界面的内容赋值
      this.homeworkId = homeworkObject.id
      this.ruleForm.subjectName = homeworkObject.title
      this.ruleForm.content = homeworkObject.content
      this.ruleForm.description = homeworkObject.description
      this.translateBeforeText = homeworkObject.content
      this.isAddOrEdit = 'edit'
      this.canEditDate = homeworkObject.canEditDate
      this.ruleForm.selectedLanguageNum = this.$t('loc.selectedLanguageNum', { num: homeworkObject.languages })
      // 这里我们将发布日期与创建日期进行对比，如果发布日期 > 创建日期  说明需要勾选， 否则不需要勾选
      let publishDate = new Date(homeworkObject.publishDateOriginStr).getTime()
      let createDate = new Date(homeworkObject.createTime).getTime()
      if (publishDate > createDate) {
        this.isScheduledSend = true
      } else {
        this.isScheduledSend = false
      }
      let translationCodes = []
      this.translationList = homeworkObject.contentModels.map(languageItem => {
        let tempLanguageItem = {}
        tempLanguageItem.langCode = languageItem.langCode
        tempLanguageItem.originalName = languageItem.lang
        tempLanguageItem.name = languageItem.lang_en
        tempLanguageItem.content = languageItem.content
        tempLanguageItem.voiceLoading = false
        tempLanguageItem.voiceUrl = ''
        tempLanguageItem.isPlaying = false
        translationCodes.push(languageItem.langCode)
        return tempLanguageItem
      })
      this.haveSelectedLanguageCodes = translationCodes.toString()
      this.translateBeforeLanguage = translationCodes.toString()
      this.shareWithParents = homeworkObject.shareParent
      this.requestParents = homeworkObject.remindParent
      this.selectedDate = homeworkObject.publishDateOriginStr
      this.shareWithDLLParents = homeworkObject.onlyShareDLLChild // 赋值是否分享给 DLL 家长
      // 处理如果创建的时候没有选择share with parent，再编辑的时候选择该选项时，应该可以选择立即发布
      if (this.shareWithParents) {
        if (this.canEditDate) {
          // 如果编辑的时候还没有到发布时间，可以选择立即发布
          this.canSendNow = true
        } else {
          this.canSendNow = false
        }
      } else {
        this.canSendNow = true
      }
      // 处理图片
      if (homeworkObject.mediaModels.length > 0) {
        this.fileType = homeworkObject.mediaModels[0].fileType ? homeworkObject.mediaModels[0].fileType : 'image'
        if (this.fileType !== 'mp4' && this.fileType !== 'aac') {
          let medias = []
          let mediaKeys = []
          // 给图片的list赋值
          homeworkObject.mediaModels.forEach((item,index) => {
            // 只赋值一张图片，兼容之前的老数据，之前可以上传4张图片
            if (index === 0) {
              let file = {}
              file.fileKey = item.mediaId // 文件id
              file.url = item.mediaUrl // 文件url
              medias.push(file)
              // id
              mediaKeys.push(item.mediaId) // 文件id
            }
          })
          this.attach.medias = medias
          this.attach.mediaKeys = mediaKeys
        }
      } else {
        this.fileType = 'image'
      }
      // 修改只能选择一种图片
      if (this.attach.medias.length >= 1) {
        this.uploadHavePlus = false
        this.uploadHaveNoPlus = true
      }
      // 获取已设置的语言
      this.getHaveSelectedLanguage()
      // 获取是否该班级是否有 DLL 小孩
      this.getIsHaveDLLChild()
      // 滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.dllTitleInput) {
          this.$refs.dllTitleInput.scrollIntoView()
        }
      })
    },
    // 获取已设置的语言
    getHaveSelectedLanguage () {
      this.$axios
        .get($api.urls().getGroupLanguageList + '?groupId=' + this.groupId)
        .then(res => {
          this.languageLoading = false
          this.haveSelectedLanguageList = res.selectList
          this.otherLanguageList = res.unSelectList
          // 如果是新增的话，把之前设置的语言默认展示出来
          if (this.isAddOrEdit === 'add' && this.haveSelectedLanguageList.length > 0) {
            this.ruleForm.selectedLanguageNum = this.$t('loc.selectedLanguageNum', { num: this.haveSelectedLanguageList.length })
            let translationCodes = []
            this.haveSelectedLanguageList.forEach(languageItem => {
              translationCodes.push(languageItem.code)
            })
            this.haveSelectedLanguageCodes = translationCodes.toString()
          }
        })
        .catch(error => {
          this.languageLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 展示选中语言的弹框
    showSelectLanguageDialog () {
      this.$analytics.sendEvent('web_dll_click_select_languages')
      this.$refs.selectLanguageDialog.show(this.groupId)
    },
    // 处理翻译的操作
    dealTranslate () {
      this.$analytics.sendEvent('web_dll_add_click_translate')
      if (this.ruleForm.content.length > 2000) {
        this.$message.error(this.$t('loc.limitInputNum2000'))
        return
      }
      this.translateLoading = true
      this.translateBeforeText = this.ruleForm.content
      this.translateBeforeLanguage = this.haveSelectedLanguageCodes
      this.$axios.post($api.urls().homeworkTranslate, {
        content: this.ruleForm.content,
        format: 'text', // 原文文本格式 text/html
        langCodes: this.haveSelectedLanguageCodes.split(',')
      })
        .then(data => {
          this.translateLoading = false
          this.translationList = data.map(item => {
            item.voiceLoading = false
            item.isPlaying = false
            item.voiceUrl = ''
            return item
          })
          this.translateOfContent = true
        }).catch(error => {
        this.translateLoading = false
        this.$message.error(error.response.data.error_message)
      })
    },
    // 确定发布双语作业的操作
    sureAdd (formName) {
      this.$analytics.sendEvent('web_dll_add_click_save')
      // 检测标题是否输入为空
      if (this.ruleForm.subjectName.trim().length === 0) {
        this.ruleForm.subjectName = ''
      }
      // 检测内容是否输入为空
      if (this.ruleForm.content.trim().length === 0) {
        this.ruleForm.content = ''
      }
      // 检验选项是否填写正确
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 检测是否选择翻译的语言
          if (this.translationList.length === 0) {
            this.$message.error(this.$t('loc.noTranslateByAdd'))
            return
          }
          // 判断输入的内容和选择的语言是否有改变
          if (this.translateBeforeText.trim() !== this.ruleForm.content.trim() || this.languageCodeIsChange()) {
            // 弹框提示
            this.editTipVisibleDialog = true
          } else {
            // 提交 DLL
            this.submitDLLInfo()
          }
        } else {
          // 滚动到顶部
          this.$nextTick(() => {
            if (this.$refs.dllTitleInput) {
              this.$refs.dllTitleInput.scrollIntoView()
            }
          })
          if (this.ruleForm.subjectName.length === 0 || this.ruleForm.content.length === 0) {
            this.$message.error(this.$t('loc.fieldIsRequired'))
            return false
          }
          // 检测是否选择语言
          if (this.ruleForm.selectedLanguageNum.trim().length === 0) {
            this.$message.error(this.$t('loc.pleaseSelectLanguage'))
            return false
          }
          return false
        }
      })
    },
    // 请求接口提交 DLL 信息
    submitDLLInfo () {
      let langContentModels = []
      let isContentEmpty = false
      for (let i = 0; i < this.translationList.length; i++) {
        let item = this.translationList[i]
        if (!item.content || item.content.trim().length === 0) {
          // 如果翻译的内容有的为空，把该条目滑到中间，并进行提示
          this.$nextTick(() => {
            if (this.$refs.langCode && this.$refs.langCode.length > 0 && this.$refs.langCode[i]) {
              this.$refs.langCode[i].$el.scrollIntoView({ block: 'center' })
            }
          })
          this.$message.error(this.$t('loc.noPlayContent'))
          isContentEmpty = true
          break
        }
        let tempLangContentModels = {}
        tempLangContentModels.langCode = item.langCode
        tempLangContentModels.content = item.content
        langContentModels.push(tempLangContentModels)
      }
      if (isContentEmpty) {
        return
      }
      this.submitLoading = true
      if (this.isScheduledSend === false) {
        // 如果是立即发布，修改为当前时间
        this.selectedDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss.000')
      } else if (this.isAddOrEdit === 'edit' && (this.selectedDate === '' || this.selectedDate === null)) {
        this.selectedDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss.000')
      }
      if (this.isAddOrEdit === 'add') {
        this.$axios.post($api.urls().createDllHomework, {
          content: this.ruleForm.content,
          groupId: this.groupId,
          publishDate: this.selectedDate,
          remindParent: this.shareWithParents ? this.requestParents : false,
          shareParent: this.shareWithParents,
          title: this.ruleForm.subjectName,
          lang: 'English',
          langContentModels: langContentModels,
          mediaIds: this.attach.mediaKeys,
          onlyShareDLLChild: this.shareWithParents ? this.shareWithDLLParents : false,
          sourceType: 'COMMON',
          description: this.ruleForm.description
        })
          .then(data => {
            this.submitLoading = false
            this.addDLLDialogVisible = false
            this.$message.success(this.$t('loc.DLLAddSuccess'))
            this.clearContent()
            // 请求父布局重新请求数据
            this.$parent.getDLLListFromAdd()
          }).catch(error => {
          this.submitLoading = false
          this.$message.error(error.response.data.error_message)
        })
      } else if (this.isAddOrEdit === 'edit') {
        this.$axios.post($api.urls().editDllHomework, {
          content: this.ruleForm.content,
          groupId: this.groupId,
          homeworkId: this.homeworkId,
          publishDate: this.selectedDate,
          remindParent: this.shareWithParents ? this.requestParents : false,
          shareParent: this.shareWithParents,
          title: this.ruleForm.subjectName,
          lang: 'English',
          langContentModels: langContentModels,
          mediaIds: this.attach.mediaKeys,
          onlyShareDLLChild: this.shareWithParents ? this.shareWithDLLParents : false,
          sourceType: 'COMMON'
        })
          .then(data => {
            this.submitLoading = false
            this.addDLLDialogVisible = false
            this.$message.success(this.$t('loc.setSucs'))
            this.clearContent()
            // 请求父布局重新请求数据
            this.$parent.$parent.getDLLListFromEdit()
          }).catch(error => {
          this.submitLoading = false
          this.$message.error(error.response.data.error_message)
        })
      }
    },
    // 获取选择语言的codes
    getSelectedLanguage (val) {
      this.haveSelectedLanguageCodes = val
      this.ruleForm.selectedLanguageNum = this.$t('loc.selectedLanguageNum', { num: val.split(',').length })
    },
    // 清空填写的内容
    clearContent () {
      this.ruleForm.content = ''
      this.ruleForm.subjectName = ''
      this.haveSelectedLanguageCodes = ''
      this.selectedDate = ''
      this.translationList = []
      this.attach.medias = []
      this.attach.mediaKeys = []
      this.dialogVisible = false
      this.dialogImageUrl = ''
      if (this.languageAudio) {
        this.languageAudio.pause()
        this.languageAudio = undefined
      }
      this.shareWithDLLParents = false
      this.isHaveDLLChild = false
      this.isShareWithParentShowTip = false
      this.isShareWithDLLParentShowTip = false
      this.isRequestRecordShowTip = false
    },
    // 上传照片
    fileUpload (content, question) {
      // 上传图片，把上传的站位图隐藏
      this.uploadHavePlus = false
      this.uploadHaveNoPlus = true
      // 展示上传图片的进度条
      this.fileUploadLoading = true
      let file = content.file
      let param = new FormData()
      // 通过append向form对象添加数据
      // param.append('file', content.file)
      param.append('file', file)
      param.append('type', 'jpg') // 这里要添加传递文件的类型，图片传jpg,视频传mp4，录音文件传aac
      // FormData私有类对象，访问不到，可以通过get判断值是否传进去
      const configs = {
        baseURL: configBaseUrl,
        headers: {
            'Content-Type': 'multipart/form-data',
            'X-UID': store.state.user.uid
          }
      }
      axios.post($api.urls().uploadFile, param, configs)
        .then(res => {
          this.fileUploadLoading = false
          content.onSuccess(res.data, question)
        }).catch(error => {
        if (error.response) {
          content.onError()
          this.$message.error(error.response.data.error_message)
          this.fileUploadLoading = false
          // 上传图片失败，把上传的站位图显式出来
          this.uploadHavePlus = true
          this.uploadHaveNoPlus = false
        }
      })
    },
    // 图片上传到服务器成功后
    uploadSuccess (res, file, fileList, question) {
      let medias = question.medias || []
      file.fileKey = res.id // 文件id
      medias.push(file)
      question.medias = medias
      // key
      let mediaKeys = question.mediaKeys || []
      mediaKeys.push(res.id)
      question.mediaKeys = mediaKeys
      // 修改只能选择一种图片
      if (medias.length >= 1) {
        this.uploadHavePlus = false
        this.uploadHaveNoPlus = true
      }
    },
    upAttachChange (file, fileList, question) {
      this.hideAttachUploadEdit = fileList.length >= 1
    },
    // 删除图片的操作
    handleRemove (file, question) {
      let medias = []
      let mediaKeys = []
      for (let media of question.medias) {
        if (media.fileKey !== file.fileKey) {
          medias.push(media)
          mediaKeys.push(media.fileKey)
        }
      }
      question.medias = medias
      question.mediaKeys = mediaKeys
      // 修改只能选择一种图片
      if (medias.length < 1) {
        this.uploadHavePlus = true
        this.uploadHaveNoPlus = false
      }
    },
    // 查看大图的操作
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 内容有改变但立即提交的操作
    atOnceSubmit () {
      this.editTipVisibleDialog = false
      this.submitDLLInfo()
    },
    // 判断选择的语言是否有改变
    languageCodeIsChange () {
      // 选择语言后的操作
      let nowLanguageArray = this.haveSelectedLanguageCodes.split(',')
      // 点击翻译或者进入add弹框时选择的语言
      let beforeLanguageArray = this.translateBeforeLanguage.split(',')
      if (nowLanguageArray.length !== beforeLanguageArray.length) {
        return true
      } else {
        let isChange = false
        for (let i = 0; i < nowLanguageArray.length; i++) {
          if (beforeLanguageArray.indexOf(nowLanguageArray[i]) > -1) {
            if (i === nowLanguageArray.length - 1) {
              // 说明所有的元素都相等，选择的语言没有变化
            } else {
              continue
            }
          } else {
            // 说明有不一样的元素，选择的语言有变化
            isChange = true
            break
          }
        }
        return isChange
      }
    },
    // 获取语音播放的地址
    getLanguageAudioUrl (item) {
      if (this.isGetVoiceLoading) {
        this.$message.info(this.$t('loc.loadingDataNow'))
        return
      }
      if (this.languageAudio) {
        this.languageAudio.pause()
        this.languageAudio = undefined
      }
      // 去除正在播放的按钮
      this.setVoicePlayingToStop()
      if (!item.langCode) {
        item.langCode = 'en'
      }
      // 处理当翻译内容为空，设置不能播放语音
      if (!item.content || item.content.trim().length === 0) {
        this.$message.error(this.$t('loc.noPlayContent'))
        return
      }
      // 如果该条语音已经播放过，使用缓存的链接播放，不用再请求接口
      if (item.voiceUrl && item.voiceUrl.length > 0) {
        this.languageAudio = new Audio(item.voiceUrl)
        this.languageAudio.play()
        this.listenPlayEnd()
        item.isPlaying = true
      } else {
        // 请求获取播放链接
        // 展示该条目的获取语音的加载框
        item.voiceLoading = true
        this.isGetVoiceLoading = true
        this.$axios
          .get($api.urls().getLanguageTextToSpeech + '?content=' + encodeURIComponent(item.content) + '&language=' + item.langCode)
          .then(res => {
            item.voiceLoading = false
            this.isGetVoiceLoading = false
            if (res.voiceUrl && res.voiceUrl.length > 0) {
              // 进行语音播报
              this.languageAudio = new Audio(res.voiceUrl)
              item.voiceUrl = res.voiceUrl
              this.languageAudio.play()
              this.listenPlayEnd()
              item.isPlaying = true
            }
          })
          .catch(error => {
            item.voiceLoading = false
            this.isGetVoiceLoading = false
            item.isPlaying = false
            this.$message.error(error.response.data.error_message)
          })
      }
    },
    // 语言语音播放结束的回调
    listenPlayEnd () {
      if (this.languageAudio) {
        this.languageAudio.addEventListener('ended', () => {
          this.setVoicePlayingToStop()
        })
      }
    },
    // 关闭弹框
    clickDialogCancel () {
      this.addDLLDialogVisible = false
      this.clearContent()
    },
    // 保持时间框里不保持上一次选择的时间
    isScheduledSendChange () {
      this.selectedDate = ''
    },
    // 当时间和switch不能编辑的时候给予提示信息
    clickDisableTip () {
      if (!this.canEditDate) {
        this.$message.error(this.$t('loc.notEditDLL'))
      }
    },
    // 设置把所有语言的正在播放按钮置空
    setVoicePlayingToStop () {
      this.translationList.forEach(item => {
        item.isPlaying = false
      })
    },
    // 判断该班级是否有 DLL 小孩
    getIsHaveDLLChild () {
      this.$axios
        .get($api.urls().getHaveDLLChild + '?groupId=' + this.groupId)
        .then(res => {
          this.isHaveDLLChild = res.haveDLLChild
        })
        .catch(error => {
          this.$message.error(error.response.data.error_message)
        })
    },
    // 只分享给 DLL 家长的状态监听
    shareDLLParentChange (shareDLLParentValue) {
      if (shareDLLParentValue && !this.isHaveDLLChild) {
        this.$message.error(this.$t('loc.noDLLChildren'))
        this.shareWithDLLParents = false
      }
    },
    // 上传图片之前的判断
    beforeAvatarUpload (file) {
      let supportImgFormat = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/JPG', 'image/JPEG', 'image/PNG', 'image/GIF']
      let isSupportImgFormat = supportImgFormat.indexOf(file.type) > -1
      let isLt10M = file.size / 1024 / 1024 < 10
      if (!isSupportImgFormat) {
        this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'))
      }
      if (!isLt10M) {
        this.$message.error(this.$t('loc.dllImageUploadTips'))
      }
      return isSupportImgFormat && isLt10M
    },
    // 翻译的内容框失去焦点调用
    translateInputChange (content, item, index) {
      item.voiceUrl = ''
      // 判断翻译的内容是否为空
      if (!content || content.trim().length === 0) {
        this.$nextTick(() => {
          if (this.$refs.langCode && this.$refs.langCode.length > 0 && this.$refs.langCode[index]) {
            this.$refs.langCode[index].$el.style.border = '1px solid #f56c6c'
          }
        })
      } else {
        this.$nextTick(() => {
          if (this.$refs.langCode && this.$refs.langCode.length > 0 && this.$refs.langCode[index]) {
            this.$refs.langCode[index].$el.style.border = 'none'
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-textarea .el-input__count {
  position: absolute;
  right: 5px;
  display: flex;
  width: 98%;
  justify-content: end;
  background: var(--color-white) !important;
  bottom: 1px;
  line-height: 20px;
}
/deep/ .el-textarea {
  textarea {
    padding-bottom: 30px;
  }
}
.title-input {
  font-size: 14px;
  color: #303133;
}
.content-input {
  font-size: 14px;
  color: #303133;
}

.translation-div {
  border: #eeeeee solid 1px;
  border-radius: 4px;
}

.show-language-div {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background: #F4F7FA;
  align-items: center;
  padding: 8px;
}

.show-language-title {
  color: #303133;
  font-weight: Bold;
  font-size: 16px;
}

.show-language-play {
  font-size: 14px;
  color: #10B3B7;
  text-align: center;
}

.translation-input {
  color: #606266 !important;
  font-size: 14px !important;
}

.date-title {
  color: #606266;
  font-size: 14px;
  margin-right: 10px;
}

.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.cancelBtn {
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}

.saveBtn {
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}

.translateBtn {
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 14px !important;
}

.setting-title {
  color: #303133;
  font-size: 16px;
  line-height: 25px;
  font-weight: 600;
  margin: 0px 10px;
  margin-top: 18px;
  margin-bottom: 6px;
}

.bottom-negative-1 {
  bottom: -1px;
}

.show-translation-div {
  margin-left: 10px;
  padding: 6px 10px 16px;
  max-width: 652px;
  background: #F6F6F6;
}
</style>

<style>
.add-dll-dialog-class {
  margin-bottom: 0 !important;
}

.add-dll-dialog-class .el-dialog__footer {
  padding: 0px 24px 24px;
}

.add-dll-dialog-class .el-dialog__header {
  font-size: 20px;
  padding: 22px 24px 12px 14px;
  margin-left: 10px;
}

.add-dll-dialog-class .el-dialog__header .el-dialog__title {
  font-size: 20px !important;
  color: #111c1c !important;
}

.add-dll-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}

.add-dll-dialog-class .el-dialog__body {
  padding: 0 14px 20px !important;
  max-height: 605px;
  overflow-y: auto;
}
.select-language-input .el-input__inner {
  color: #10B3B7;
  font-size: 14px;
  cursor: pointer;
}

.form-add-dll .el-form-item {
  margin-bottom: 15px !important;
}

.form-add-dll .el-form-item .el-form-item__label {
  color: #111c1c !important;
  font-size: 16px !important;
  line-height: 25px !important;
  font-weight: 600;
  font-family: 'Inter';
  padding: 0;
}

.form-add-dll .el-form-item .el-form-item__error {
  padding-top: 0px;
  padding-left: 0 !important;
}

.translation-input .el-textarea__inner {
  border: none;
}

.content-input .el-textarea__inner {
  min-height: 90px !important;
  overflow-x: hidden;
}
/*将 dll 弹窗内滚动条的宽度设置为0px，以达到视觉隐藏的效果*/
.add-dll-dialog-class ::-webkit-scrollbar {
  width: 0px;
}

/*定义滚动条轨道 内阴影+圆角*/
.add-dll-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.add-dll-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}

.add-dll-upload .el-upload-list__item-thumbnail {
  width: 90px;
  height: 90px;
}

.add-dll-upload .el-upload-list__item {
  height: 90px;
  width: 90px;
  /*去掉加载图片的动画*/
  transition: none !important;
  display: inline-flex;
  margin: 0 !important;
}

.add-dll-upload-plus .el-upload--picture-card {
  height: 90px;
  width: 90px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.add-dll-upload-no-plus .el-upload--picture-card {
  height: 90px;
  width: 90px;
  display: none;
}

.show-edit-tip .el-dialog__body {
  padding: 10px 20px !important;;
}

.div-choose-date .el-radio-group label {
  margin-bottom: 0px !important;
}

.lg-pa-down-0 {
  padding-bottom: 0px;
  height: 52px;
}
.gap-8 {
  gap: 8px;
}
.hr-color-border-height {
  background-color: #dcdfe6;
  height: 1px;
  border: none;
}
.bg-f5f6f8 {
  background-color: #F5F6F8;
}
</style>
