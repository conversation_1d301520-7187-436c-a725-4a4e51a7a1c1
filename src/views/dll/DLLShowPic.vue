<template>
<div @click.stop="">
  <!-- 展示图片 -->
  <div class="w-full height-100 add-margin-b-8">
    <a class="noteImgBox"
       :key="mediaModels[0].mediaId || mediaModels[0].id">
      <img class="img-cover height-100 width-100 border-radius-4"
           style="border: 1px solid #DCDFE6;"
           @click="showImage(mediaModels,index)"
           :src="mediaModels[0].mediaUrl || mediaModels[0].public_url">
    </a>
  </div>
  <!-- 图片左右滚动的viewer -->
  <ElImageViewer class="el-image-view"
                 style="z-index: 3000;"
                 v-if="showImg"
                 :on-close="closeShowImgBox"
                 :initialIndex = 'initialIndex'
                 :url-list="imgList" />
</div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'DLLShowPic',
  components: {
    ElImageViewer
  },
  data () {
    return {
      showImg: false, // 是否打开图片左右滚动的弹框
      imgList: [], // 要打开图片左右可滚动的地址,
      initialIndex: 0 // 预览的index
    }
  },
  props: {
    mediaModels: {
      type: Array
    }
  },
  methods: {
    // 打开图片，图片可以左右滑动
    showImage (media, index) {
      this.showImg = true
      this.initialIndex = index
      let count = 0
      for (var i = 0; i < media.length; i++) {
        this.imgList[count++] = media[i].mediaUrl || media[i].public_url
      }
    },
    // 关闭图片
    closeShowImgBox () {
      this.showImg = false
      this.imgList = []
    }
  }
}
</script>

<style scoped>
.noteImgBox {
  width: 100px;
  float: left;
  margin-right: 10px;
}
.height-100 {
  height: 100px;
}
.height-95 {
  height: 95px;
}
.width-95 {
  width: 95px;
}
.width-100 {
  width: 100px;
}
</style>
<style>
.el-image-view .el-image-viewer__close {
  color: white;
}
.el-image-view .el-image-viewer__canvas img{
  max-height: 90%;
}
</style>
