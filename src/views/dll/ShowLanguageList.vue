<template>
  <!-- 展示翻译的语言内容 -->
  <div @click.stop=""  :class="['div-show-language',{'div-vocabulary-show-language':isFromDllVocabulary}]">
    <el-collapse>
      <el-collapse-item :class="[{'show-white-header': showWhiteHeader},{'show-gray-header': showGrayHeader},{'show-parent-header': showParentHeader}]">
        <span slot="title" :style="titleBackground" class="font-size-14 font-color-primary">
          {{ title }}
        </span>
        <!-- DLL 翻译的多语言内容 -->
        <div v-for="languageItem in translateLanguageList"
             :key="languageItem.id" class="dll-translation-content-div">
          <div class="flex-row-between" style="min-height: 36px">
            <div class="display-flex align-items flex-1" style="min-width: 0">
               <span style="width: 45%" class="translation-language-title">
                {{ languageItem.lang }}
                <span class="font-normal">({{ languageItem.lang_en }})</span>
               </span>
               <span class="translation-content">{{ languageItem.content }}</span>
            </div>
            <div style="min-width: 66px">
              <span v-show="!languageItem.voiceLoading && !languageItem.isPlaying"
                    class="dll-content-play lg-pointer inline-flex-row-vertical-center"
                    style="border-radius: 26px;background-color: #10B3B7;padding: 8px;"
                    @click="onSpeak(languageItem)">
              <span class="show-play-icon-new"></span>
              {{ $t('loc.play') }}
            </span>
              <i v-show="languageItem.voiceLoading" class="el-icon-loading"></i>
              <!-- 展示正在播放语音的动画 -->
              <div v-if="!languageItem.voiceLoading && languageItem.isPlaying"
                   style="border-radius: 26px;background-color: #10B3B7;padding: 8px;"
                   class="lg-pointer inline-flex-row-vertical-center" @click="onSpeak(languageItem)">
                <span class="voice-playing-new lg-pointer"></span>
                <span class="dll-content-play add-margin-l-3">{{ $t('loc.play') }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'ShowLanguageList',
  data () {
    return {
      titleBackground: {background: '#FFFFFF'},
      showWhiteHeader: true,
      showGrayHeader: false,
      showParentHeader: false
    }
  },
  props: {
    translateLanguageList: {
      type: Array
    },
    isFromNewComment: {
      type: Boolean,
      default: false
    },
    isFromParent: {
      type: Boolean,
      default: false
    },
    isFromDllVocabulary: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    title() {
      let num = this.translateLanguageList ? this.translateLanguageList.length : 0;
      let key = this.isFromParent ? 'loc.selectedOtherLanguageNum' : 'loc.selectedLanguageNum';
      return this.$t(key, {num: num});
    }
  },
  // created () {
  //   if (this.isFromNewComment) {
  //     // this.titleBackground = { background: '#F2F2F2' }
  //     this.titleBackground = { background: '#EFEFEF' }
  //     this.showWhiteHeader = false
  //     this.showGrayHeader = true
  //     this.showParentHeader = false
  //   } if (this.isFromParent) {
  //     this.titleBackground = { background: '#EFEFEF' }
  //     this.showWhiteHeader = false
  //     this.showGrayHeader = false
  //     this.showParentHeader = true
  //   } else {
  //     this.titleBackground = { background: '#FFFFFF' }
  //     this.showWhiteHeader = true
  //     this.showGrayHeader = false
  //     this.showParentHeader = false
  //   }
  // },
  methods: {
    // 播放多语言教学的语音
    onSpeak(item) {
      // 从上层节点查找播放方法
      let parent = this;
      while (parent) {
        if (parent.getLanguageAudioUrl) {
          break;
        }
        parent = parent.$parent;
      }
      if (!parent) {
        return;
      }
      parent.getLanguageAudioUrl(item)
    }
  },
  watch: {
    isFromNewComment: {
      handler(newValue) {
        if (newValue) {
          this.titleBackground = {background: '#F2F2F2'}
          this.showWhiteHeader = false
          this.showGrayHeader = true
          this.showParentHeader = false
        }
      },
      immediate: true
    },
    isFromParent: {
      handler(newValue) {
        if (newValue) {
          this.titleBackground = {background: '#EFEFEF'}
          this.showWhiteHeader = false
          this.showGrayHeader = false
          this.showParentHeader = true
        }
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
.translation-language-title {
  font-size: 16px;
  color: #111c1c;
  font-weight: bold;
}

.translation-content {
  font-size: 16px;
  color: #323338;
  display: inline-block;
  margin-left: 16px;
  margin-right: 16px;
  line-height: 1.5;
}

.dll-translation-content-div {
  background: #F5F6F8;
  padding: 12px;
}

.div-show-language {
  width: 100%;
}

.flex-row-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dll-content-play {
  font-size: 14px;
  color: white;
  text-align: center;
}

.div-show-language >>> .el-collapse {
  border-top: 0 solid #eee;
  border-bottom: 0 solid #eee;
}

.div-show-language >>> .el-collapse-item__header {
  border-bottom: 0 solid #eee;
  height: 20px;
  line-height: 20px;
  color: #10B3B7;
}

.div-show-language >>> .el-collapse-item__header .el-collapse-item__arrow {
  margin: 3px auto 0 10px !important;
}

.div-show-language >>> .el-collapse-item__wrap {
  border-bottom: 0 solid #eee;
  border-radius: 4px;
}

.div-show-language >>> .el-collapse-item__content {
  line-height: 1;
  padding-bottom: 0;
}
/*解决在dll300中展开语言和share距离过近的问题*/
.div-vocabulary-show-language >>> .el-collapse-item__content {
  margin-bottom: 16px;
}

</style>
<style>
.show-white-header .el-collapse-item__header {
  background: white;
  margin-bottom: 4px;
}

.show-gray-header .el-collapse-item__header {
  background: #F2F2F2;
  margin-bottom: 4px;
}

.show-parent-header .el-collapse-item__header {
  background: #EFEFEF;
  padding-left: 12px;
  padding-right: 12px;
  height: 30px !important;
  line-height: 30px !important;
  margin-bottom: 4px;
}

.show-white-header .el-collapse-item__wrap {
  background: white;
}

.show-gray-header .el-collapse-item__wrap {
  background: #F2F2F2;
}

.show-parent-header .el-collapse-item__wrap {
  background: #EFEFEF;
}

</style>
