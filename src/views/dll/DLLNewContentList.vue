<template>
  <!-- DLL 双语教学列表中具体 DLL 的内容 -->
  <div :class="[isFromDLLVocabulary ? 'h-full' : 'min-height-200',{'scrollbar2':isFromDLLVocabulary},'new-font-color',{'add-padding-l-10': fromType === 'dllVocabulary'},{'add-padding-r-10': fromType === 'dllVocabulary'}]"   v-loading="dllListLoading">
    <div  class="white-background lg-border-radius-8 box-shadow-8" :class="[{'add-margin-t-16': index !== 0 || fromType ==='dll'},{'add-margin-t-1':index === 0 && isFromDLLVocabulary}]" v-for="(item,index) in dllContentList" :key="item.id">
      <!-- dll300词汇中展示老师的信息 -->
      <div v-if="item.isAllStaff" class="display-flex w-full align-items bg-fafafa add-padding-tb-8 add-padding-lr-12 add-border-top-radius-4">
        <el-avatar class="add-margin-r-10 min-width-40" :src="item.avatarUrl"></el-avatar>
        <span class="add-margin-l-10">{{item.createUserName}}</span>
      </div>
      <!-- 新的 DLL 评论展示入口 -->
      <div v-if="isHaveNewComment && fromType==='dll' && index === 0" class="w-full text-center add-margin-tb-16 add-border-top-radius-4">
      <span class="show-new-comment lg-pointer add-margin-t-16" @click="goCommentDetails">
        <img class="circle-img add-margin-r-10" :src="newCommentAvatarUrl" alt="">
        <span>{{newCommentNum === 1 ? $t('loc.receiveNewFeedback',{num: newCommentNum}) : $t('loc.receiveMoreNewFeedback',{num: newCommentNum})}}</span>
      </span>
      </div>
      <div class="lg-pa-16 padding-b-1 lg-pointer" @click="toDllPreview(index)">
        <!-- DLL title -->
        <div v-if="false" class="flex-row-between">
          <span class="dll-title flex-1 overflow-ellipsis-two line-height-22">{{item.title}}</span>
          <span class="dll-title-date">
          <span  class="add-margin-r-10 font-size-12 dll-tag" v-if="item.source && item.source !== 'COMMON'">
            {{ item.source === 'DLL_LIBRARY' ? $t('loc.dllFromDllLibrary') : $t('loc.dllFromLesson')}}
          </span>
          {{item.publishDateStr}}
        </span>
        </div>

        <!-- DLL 英语内容 -->
        <div class="dll-english-content">
          <div v-if="item.mediaModels.length > 0" style="flex: none">
            <DLLShowPic v-if="item.mediaModels.length > 0" :mediaModels="item.mediaModels"></DLLShowPic>
          </div>
          <div style="grid-area: b">
            <div class="flex-row-between">
              <div class="dll-content-english font-weight-semibold">{{item.content}}</div>
              <span v-show="!item.voiceLoading && !item.isPlaying"
                    class="dll-content-play lg-pointer inline-flex-row-vertical-center"
                    style="border-radius: 26px;background-color: #10B3B7;padding: 8px;"
                    @click.stop="getLanguageAudioUrl(item)">
              <span class="show-play-icon-new"></span>
              {{$t('loc.play')}}
            </span>
              <i v-show="item.voiceLoading" class="el-icon-loading"></i>
              <div v-if="!item.voiceLoading && item.isPlaying"
                   class="lg-pointer inline-flex-row-vertical-center"
                   style="border-radius: 26px;background-color: #10B3B7;padding: 8px;"
                   @click.stop="getLanguageAudioUrl(item)">
                <span class="voice-playing-new lg-pointer"></span>
                <span class="dll-content-play add-margin-l-3">{{$t('loc.play')}}</span>
              </div>
            </div>
            <div class="display-flex align-items lg-color-text-primary add-margin-b-8">
              <span class="font-weight-normal overflow-ellipsis-two">{{item.title}}</span>
              <span class="add-margin-lr-6">|</span>
              <span v-if="item.source && item.source !== 'COMMON'">{{ item.source === 'DLL_LIBRARY' ? $t('loc.dllFromDllLibrary') : $t('loc.dllFromLesson')}}</span>
              <span v-if="item.source && item.source === 'COMMON'">{{$t('loc.manuallyAdded')}}</span>
            </div>
            <!-- DLL 翻译的多语言内容 -->
            <ShowLanguageList :translateLanguageList="item.contentModels" :is-from-dll-vocabulary="isFromDLLVocabulary"></ShowLanguageList>
            <!-- share按钮 -->
            <div v-if="isFromDLLVocabulary" class="display-flex add-margin-b-20">
              <span class="flex-1"></span>
              <el-button class="share-btn"
                         @click.stop="shareDllByStaff(item.title,item.content,item.mediaModels.length > 0 ? item.mediaModels[0].mediaUrl : '',item.mediaModels.length > 0 ? item.mediaModels[0].mediaId : '')"
                         type="primary">
                {{$t('loc.shareDll')}}
              </el-button>
            </div>
            <!-- DLL 发布的时间以及操作-->
            <DLLEditAndDelete v-if="!isFromDLLVocabulary" :dllInfo="item" :formatType="formatType" :isCanComment="true && fromType==='dll'" :isCanEdit="!isInactive" :isCanDelete="true && fromType==='dll' && !isInactive" :groupId="groupId || item.groupId" ></DLLEditAndDelete>
          </div>
        </div>
      </div>
    </div>
    <!-- 空布局 -->
    <div v-if="dllContentList.length == 0 && !isFromDLLVocabulary && !dllListLoading" class="flex-column-center height-400 w-full white-background lg-border-radius-8 box-shadow-8 position-relative">
      <!-- 新的 DLL 评论展示入口 -->
      <div v-if="isHaveNewComment && fromType==='dll'" class="w-full text-center add-border-top-radius-4"
           :class="[{'position-absolute': (this.searchContent && this.searchContent.trim().length > 0) || this.dllType !== '1'},
             {'position-absolute-top-20': (this.searchContent && this.searchContent.trim().length > 0) || this.dllType !== '1'}]"
      >
      <span class="show-new-comment lg-pointer" @click="goCommentDetails">
        <img class="circle-img add-margin-r-10" :src="newCommentAvatarUrl" alt="">
        <span>{{newCommentNum === 1 ? $t('loc.receiveNewFeedback',{num: newCommentNum}) : $t('loc.receiveMoreNewFeedback',{num: newCommentNum})}}</span>
      </span>
      </div>
      <img src="@/assets/img/dll/pic_empty_dll.png">
      <div class="w-full add-margin-t-8 font-size-14 color-dll-title text-center">
        <span v-if="(!this.searchContent || this.searchContent.trim().length === 0) && this.dllType === '1'" v-html="$t('loc.dllWelcomeContent1')"></span>
        <span v-else>{{$t('loc.plan36')}}</span>
      </div>
      <!-- 空界面的按钮,当不是搜索并且选择的类型为全部时，展示按钮 -->
      <div v-if="(!this.searchContent || this.searchContent.trim().length === 0) && this.dllType === '1'" class="flex-center-center add-margin-t-16 w-full">
        <!-- 添加新的dll -->
        <el-button v-show="!isInactive" @click="addNewDll">+ {{$t('loc.addDllActivity')}}</el-button>
        <!-- 进入dll模板库 -->
        <el-button type="primary" class="add-margin-l-16" @click="toDllLibrary">{{$t('loc.dllLibrary')}}</el-button>
      </div>
    </div>
    <div v-if="dllContentList.length == 0 && isFromDLLVocabulary" class="flex-column-center h-full w-full white-background lg-border-radius-8">
      <img src="@/assets/img/dll/pic_empty_dll.png" style="margin-top: -40px">
      <div class="text-center add-margin-t-8 font-size-14 el-icon-question-color" >
        {{$t('loc.plan36')}}
      </div>
    </div>
    <!-- 编辑 DLL 的弹框 -->
    <add-dll-dialog ref="editDllDialog"></add-dll-dialog>
  </div>
</template>

<script>
import DLLEditAndDelete from './DLLEditAndDelete'
import ShowLanguageList from './ShowLanguageList'
import DLLShowPic from './DLLShowPic'
import { mapState } from 'vuex'
import tools from '../../utils/tools'
import AddDllDialog from '@/views/dll/AddDLLDialog'
export default {
  name: 'DLLNewContentList',
  components: {
    AddDllDialog,
    DLLEditAndDelete,
    ShowLanguageList,
    DLLShowPic
  },
  data () {
    return {
      dllListLoading: true,
      dllContentList: [], // DLL 内容列表
      originDllContentList: [],// 原始的 DLL 的内容，用于搜索
      formatType: '', // 日期格式
      formatTypeForDate: '',
      fromDate: '',
      toDate: '',
      isPlayLanguage: false, // 语言的音频是否正在播放
      isGetVoiceLoading: false, // 是否界面有获取语音的加载框
      languageAudio: undefined, // 播放语音控制器
      isFromDLLVocabulary: false, // 是否来自于dll300词汇的类别
      searchContent: '', // 搜索内容
      dllType: '1', // dll的类型
      isHaveNewComment: false, // 判断是否有新的评论
      newCommentNum: 0, // DLL 评价的数量
      newCommentAvatarUrl: '' // 默认的头像地址
    }
  },
  // 定义从父组件传递过来的值
  props: {
    groupId: {
      type: String
    },
    fromType: {
      type: String
    },
    isInactive: {
      type: Boolean,
      default: false
    },
    // 开始日期
    fromDateStr: {
      type: String
    },
    // 结束日期
    toDateStr: {
      type: String
    }
  },
  created () {
    if (this.fromType === 'dll') {
      // 如果有传开始结束时间，则使用传递过来的时间，否则默认为最近一周
      if (this.fromDateStr && this.toDateStr) {
        this.fromDate = this.fromDateStr
        this.toDate = this.toDateStr
      } else {
        let selectedDate = [this.formatDate(new Date().getTime() - 3600 * 1000 * 24 * 7), this.formatDate(new Date().getTime())]
        this.fromDate = this.$moment(selectedDate[0]).format('YYYY-MM-DD')
        this.toDate = this.$moment(selectedDate[1]).format('YYYY-MM-DD')
      }
      if (this.groupId) {
        this.getDllContentList()
      }
      this.isFromDLLVocabulary = false
    } else if (this.fromType === 'dllCoach') {
      this.dllListLoading = true
      this.isFromDLLVocabulary = false
    } else if (this.fromType === 'dllVocabulary') {
      this.dllListLoading = true
      this.isFromDLLVocabulary = true
    }
    if (this.language != 'zh-CN') {
      // 注意这里的年月日都要大写
      this.formatType = 'MM/DD/YYYY hh:mm A'
      this.formatTypeForDate = 'MM/DD/YYYY'
    } else {
      this.formatType = 'YYYY-MM-DD HH:mm'
      this.formatTypeForDate = 'YYYY-MM-DD'
    }
  },
  methods: {
    onSpeak (item) {
    },
    goDLLDetails (homeworkId) {
      this.$parent.toDLLDetails(homeworkId)
    },
    // 当 DLL 教师端日期变化请求数据
    getContentDateChange (startDate,endDate) {
      this.fromDate = startDate
      this.toDate = endDate
      this.dllListLoading = true
      if (this.groupId) {
        this.getDllContentList()
      }
    },
    // 获取教师端 DLL 内容列表
    getDllContentList () {
      this.$axios
        .get($api.urls().getTeacherDllList + '?groupId=' + this.groupId + '&fromDate=' + this.fromDate + '&toDate=' + this.toDate)
        .then(data => {
          this.dllListLoading = false
          this.originDllContentList = []
          this.dllContentList = []
          this.originDllContentList = data.map(dllItem => {
            let publishDateOriginStr = dllItem.publishDateStr
            dllItem.createTime = this.$moment(dllItem.createTime).format(this.formatType)
            dllItem.publishDateOriginStr = publishDateOriginStr
            dllItem.publishDateStr = this.$moment(dllItem.publishDateStr).format(this.formatTypeForDate)
            dllItem.replyNum = dllItem.replyModels.length
            dllItem.contentModels = dllItem.contentModels.map(contentItem => {
              contentItem.voiceLoading = false // 设置各种语言获取语音的loading
              contentItem.isPlaying = false // 设置语言是否正在播放
              contentItem.voiceUrl = '' // 设置语言播放地址
              return contentItem
            })
            dllItem.voiceLoading = false // english语言的loading
            dllItem.isPlaying = false // 设置english语言是否正在播放
            dllItem.voiceUrl = '' // 设置english语言播放地址
            // 默认来源为自己添加的dll，适配之前发布的内容
            if (!tools.isNotEmpty(dllItem.source)) {
              dllItem.source = 'COMMON'
            }
            return dllItem
          })
          this.originDllContentList.forEach(item => {
            if (this.checkHaveSearchContent(item) && this.checkEqualSelectedDllType(item)) {
              this.dllContentList.push(item)
            }
          })
          this.$emit('showDLLNum',this.dllContentList.length)
        })
        .catch(error => {
          this.dllListLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 搜索 DLL 的内容
    searchDllContentList (searchContent) {
      this.dllContentList = []
      this.searchContent = searchContent
      this.originDllContentList.forEach(item => {
        if (this.checkHaveSearchContent(item) && this.checkEqualSelectedDllType(item)) {
          this.dllContentList.push(item)
        }
      })
      this.$emit('showDLLNum',this.dllContentList.length)
    },
    // 根据dll的内容展示不同的内容，1表示所有类型，2表示dll，3表示lesson，4表示模板
    changeDllType (dllType) {
      this.dllContentList = []
      this.dllType = dllType
      this.originDllContentList.forEach(item => {
        if (this.checkHaveSearchContent(item) && this.checkEqualSelectedDllType(item)) {
          this.dllContentList.push(item)
        }
      })
      this.$emit('showDLLNum',this.dllContentList.length)
    },
    // 获取教练审查列表
    getDLLCoachList (groupIds,fromDate,endDate,pageNum,pageSize,languageCodes) {
      this.dllListLoading = true
      this.$axios.post($api.urls().getCoachHomeworkList,{
        groupIds: groupIds,
        fromDate: fromDate,
        endDate: endDate,
        pageNum: pageNum,
        pageSize: pageSize,
        langCodes: languageCodes
      })
        .then(data => {
          this.dllListLoading = false
          this.dllContentList = data.homeworkListResponses.map(dllItem => {
            let publishDateOriginStr = dllItem.publishDateStr
            dllItem.createTime = this.$moment(dllItem.createTime).format(this.formatType)
            dllItem.publishDateOriginStr = publishDateOriginStr
            dllItem.publishDateStr = this.$moment(dllItem.publishDateStr).format(this.formatTypeForDate)
            dllItem.publishName = dllItem.editRecordModels.length > 0 ? dllItem.editRecordModels[0].name : ''
            dllItem.publishRole = dllItem.editRecordModels.length > 0 ? dllItem.editRecordModels[0].role : ''
            dllItem.replyNum = dllItem.replyModels.length
            dllItem.contentModels = dllItem.contentModels.map(contentItem => {
              contentItem.voiceLoading = false // 设置各种语言获取语音的loading
              contentItem.isPlaying = false // 设置语言是否正在播放
              contentItem.voiceUrl = '' // 设置语言播放地址
              return contentItem
            })
            dllItem.voiceLoading = false // english语言的loading
            dllItem.isPlaying = false // 设置english语言是否正在播放
            dllItem.voiceUrl = '' // 设置english语言播放地址
            return dllItem
          })
          this.$parent.total = data.totalCount
        })
        .catch(error => {
          this.dllListLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 获取全部staff的内容
    getAllStaffDllList (agencyId,fromDate,endDate,pageNum,pageSize,searchContent) {
      this.dllListLoading = true
      let requestContent = { agencyId: agencyId,fromDate: fromDate,endDate: endDate,pageSize: pageSize,pageNum: pageNum,searchContent: searchContent }
      this.$axios
        .post($api.urls().getAgencyHomeworkList, requestContent)
        .then(res => {
          this.dllListLoading = false
          this.originDllContentList = []
          this.dllContentList = []
          this.originDllContentList = res.homeworkListResponses.map(dllItem => {
            let publishDateOriginStr = dllItem.publishDateStr
            dllItem.isAllStaff = true
            dllItem.createTime = this.$moment(dllItem.createTime).format(this.formatType)
            dllItem.publishDateOriginStr = publishDateOriginStr
            dllItem.publishDateStr = this.$moment(dllItem.publishDateStr).format(this.formatTypeForDate)
            dllItem.contentModels = dllItem.contentModels.map(contentItem => {
              contentItem.voiceLoading = false // 设置各种语言获取语音的loading
              contentItem.isPlaying = false // 设置语言是否正在播放
              contentItem.voiceUrl = '' // 设置语言播放地址
              return contentItem
            })
            dllItem.voiceLoading = false // english语言的loading
            dllItem.isPlaying = false // 设置english语言是否正在播放
            dllItem.voiceUrl = '' // 设置english语言播放地址
            return dllItem
          })
          this.originDllContentList.forEach(item => {
            this.dllContentList.push(item)
          })
          this.$emit('showDLLNum',res.total)
        })
        .catch(error => {
          this.dllListLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 获取当个staff的内容
    getStaffDllList (staffId,agencyId,fromDate,endDate,pageNum,pageSize,searchContent) {
      let requestContent = { agencyId: agencyId, teacherId: staffId,fromDate: fromDate,endDate: endDate,pageSize: pageSize,pageNum: pageNum,searchContent: searchContent }
      this.$axios
        .post($api.urls().getTeacherHomeworkList , requestContent)
        .then(res => {
          this.dllListLoading = false
          this.originDllContentList = []
          this.dllContentList = []
          this.originDllContentList = res.homeworkListResponses.map(dllItem => {
            let publishDateOriginStr = dllItem.publishDateStr
            dllItem.isAllStaff = false
            dllItem.createTime = this.$moment(dllItem.createTime).format(this.formatType)
            dllItem.publishDateOriginStr = publishDateOriginStr
            dllItem.publishDateStr = this.$moment(dllItem.publishDateStr).format(this.formatTypeForDate)
            dllItem.contentModels = dllItem.contentModels.map(contentItem => {
              contentItem.voiceLoading = false // 设置各种语言获取语音的loading
              contentItem.isPlaying = false // 设置语言是否正在播放
              contentItem.voiceUrl = '' // 设置语言播放地址
              return contentItem
            })
            dllItem.voiceLoading = false // english语言的loading
            dllItem.isPlaying = false // 设置english语言是否正在播放
            dllItem.voiceUrl = '' // 设置english语言播放地址
            return dllItem
          })
          this.originDllContentList.forEach(item => {
            this.dllContentList.push(item)
          })
          this.$emit('showDLLNum',res.total)
        })
        .catch(error => {
          this.dllListLoading = false
          this.$message.error(error.response.data.error_message)
          // 如果用户不存在，界面没有来及刷新，把界面置空
          if (error.response.data.code && error.response.data.code.toLowerCase() === 'user_not_found') {
            this.$emit('showDLLNum',0)
            this.originDllContentList = []
            this.dllContentList = []
          }
        })
    },
    // 删除 DLL
    deleteDll (homeworkId) {
      this.dllContentList = this.dllContentList.filter(x => x.id !== homeworkId)
      this.originDllContentList = this.originDllContentList.filter(x => x.id !== homeworkId)
      if (this.fromType === 'dll') {
        // 请求是否还有新的信息，因为可能新的消息就是删除的 DLL
        this.$parent.getNewDllComments()
      }
    },
    getLanguageAudioUrl (item) {
      if (this.isGetVoiceLoading) {
        this.$message.info(this.$t('loc.loadingDataNow'))
        return
      }
      if (this.languageAudio) {
        this.languageAudio.pause()
        this.languageAudio = undefined
      }
      // 去除正在播放的按钮
      this.setVoicePlayingToStop()
      if (!item.langCode) {
        item.langCode = 'en'
      }
      // 如果该条语音已经播放过，使用缓存的链接播放，不用再请求接口
      if (item.voiceUrl && item.voiceUrl.length > 0) {
        this.languageAudio = new Audio(item.voiceUrl)
        this.languageAudio.play()
        this.playEnd()
        item.isPlaying = true
      } else {
        // 请求获取播放链接
        // 展示该条目的获取语音的加载框
        item.voiceLoading = true
        this.isGetVoiceLoading = true
        this.$axios
          .get($api.urls().getLanguageTextToSpeech + '?content=' + encodeURIComponent(item.content) + '&language=' + item.langCode)
          .then(res => {
            item.voiceLoading = false
            this.isGetVoiceLoading = false
            if (res.voiceUrl && res.voiceUrl.length > 0) {
              // 进行语音播报
              this.languageAudio = new Audio(res.voiceUrl)
              item.voiceUrl = res.voiceUrl
              this.languageAudio.play()
              this.playEnd()
              item.isPlaying = true
            }
          })
          .catch(error => {
            item.voiceLoading = false
            this.isGetVoiceLoading = false
            item.isPlaying = false
            this.$message.error(error.response.data.error_message)
          })
      }
    },
    // 语言语音播放结束的回调
    playEnd () {
      this.languageAudio.addEventListener('ended', () => {
        this.setVoicePlayingToStop()
      })
    },
    // 格式化日期格式
    formatDate (time) {
      let date = new Date(time)
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = date.getDate()
      d = d < 10 ? '0' + d : d
      return m + '/' + d + '/' + y
    },
    // 设置把所有语言的正在播放按钮置空
    setVoicePlayingToStop () {
      this.dllContentList.forEach(item => {
        item.isPlaying = false
        item.contentModels.forEach(contentModel => {
          contentModel.isPlaying = false
        })
      })
    },
    clearAudioInfo () {
      if (this.languageAudio) {
        this.languageAudio.pause()
        this.languageAudio = undefined
      }
    },
    // 在dll 词汇界面中点击分享dll
    shareDllByStaff (dllTitle,dllContent,dllImgUrl,dllImgId) {
      this.$emit('shareDllByStaff',dllTitle,dllContent,dllImgUrl,dllImgId)
    },
    // 判断当前的item的title或content是否包含搜索的内容
    checkHaveSearchContent (item) {
      return !this.searchContent || this.searchContent.trim().length === 0 || item.title.toLowerCase().indexOf(this.searchContent.toLowerCase()) !== -1 || item.content.toLowerCase().indexOf(this.searchContent.toLowerCase()) !== -1
    },
    // 判断选择的dll类型是否和当前item的相同
    checkEqualSelectedDllType (item) {
      return this.dllType === '1' || (this.dllType === '2' && item.source === 'COMMON') || (this.dllType === '3' && item.source === 'LESSON') || (this.dllType === '4' && item.source === 'DLL_LIBRARY')
    },
    // 进入添加 DLL 的弹框界面
    addNewDll () {
      this.$emit('addNewDll')
    },
    // 进入 DLL 模板库
    toDllLibrary () {
      this.$emit('toDllLibrary')
    },
    // 赋值新的评论
    setNewCommentInfo (haveNewComment,parentUrl,replyNum) {
      this.isHaveNewComment = haveNewComment
      this.newCommentAvatarUrl = parentUrl
      this.newCommentNum = replyNum
    },
    // 去新评论的详情
    goCommentDetails () {
      this.$emit('toNewComment')
    },
    // 跳转到 dll 预览界面
    toDllPreview (index) {
      this.$emit('toDllPreview',index)
    },
    // 添加 DLL 的操作
    openEditDLLDialog (dllInfo) {
      this.$refs.editDllDialog.showFromEdit(this.groupId, dllInfo)
    }
  },
  computed: {
    ...mapState({
      language: (state) => state.user.language // 用户语言
    })
  },
  watch: {
    groupId (newVal) {
      if (newVal) {
        this.dllListLoading = true
        if (this.groupId) {
          this.getDllContentList()
        }
      }
    }
  },
  beforeDestroy () {
    this.clearAudioInfo()
  }
}
</script>

<style scoped>
.dll-english-content{
  display: grid;
  grid-template-areas: "a b";
  grid-template-columns: max-content 1fr;
  margin-top: 11px;
}

.flex-row-between{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.dll-title {
  font-size: 18px;
  color: #303133;
  font-weight: 400;
}
.dll-title-date {
  font-size: 16px;
  color: #909399;
}
/*.english-language-title {*/
/*  font-size: 18px;*/
/*  color: #303133;*/
/*}*/
.dll-content-play {
  font-size: 14px;
  color: #ffffff;
  text-align: center;
}
.dll-content-english {
  font-size: 16px;
  margin: 8px 0 12px;
  line-height: 19px;
  white-space: pre-wrap;
  word-break: break-word;
}
.dll-tag{
  background-color: #67C23A33;
  color: #67C23A;
  border-radius: 10px;
  padding: 3px 8px;
}
.bg-fafafa {
  background-color: #FAFAFA;
}
.min-height-200 {
  min-height: 200px;
}
.share-btn {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  margin-top: -5px !important;
}
.padding-b-8 {
  padding-bottom: 8px !important;
}
.padding-b-1 {
  padding-bottom: 1px !important;
}
.show-new-comment {
  display: inline-flex;
  background: #67C23A;
  border-radius: 22px;
  line-height:44px;
  color: white;
  font-size: 16px;
  text-align: center;
  align-items: center;
  padding-left: 2px;
  padding-right: 20px;
}
.circle-img {
  border-radius: 50%;
  width: 40px;
  height: 40px;
}
.add-margin-t-1 {
  margin-top: 1px;
}
.position-absolute-top-20 {
  top: 20px;
}
</style>
