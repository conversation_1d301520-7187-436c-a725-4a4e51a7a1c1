<template>
<!-- DLL 词汇库列表 -->
  <div class="h-full">
    <el-row :gutter="20" class="h-full remove-margin-l-0 remove-margin-r-0">
      <!-- 选择词汇主题列表 -->
      <el-col :span="isFromResource ? 5 : 6" class="h-full remove-padding-l-0" v-loading="dllThemeLoading">
        <div class="white-background h-full scrollbar2 lg-border-radius-8" :class="[isFromResource ? 'bg-color-F5F6F8' : 'white-background']">
          <div v-if="!isFromResource" class="text-bolder add-padding-lr-20 height-48 font-size-16 display-flex align-items">{{$t('loc.dllVocabulary')}}</div>
          <div v-if="!isFromResource" class="divide-line-gray"></div>
          <!-- 如果是admin，展示添加主题的按钮 -->
          <div class="text-primary add-padding-lr-20 height-48 display-flex align-items lg-pointer" v-if="isAdmin && !isFromResource" @click="clickAddNewTheme">+ {{$t('loc.addNew')}}</div>
          <!-- 展示主题的列表 -->
          <div v-for="(dllVocabularyItem,index) in dllVocabularyThemeList" :key="dllVocabularyItem.id"
               :class="{'child-item-active':dllVocabularyItem.id === currentThemeId,'list-hover-bg-color':hoverIndex === index}"
               class="display-flex align-items height-48 lg-pointer"
               @click="selectTheme(dllVocabularyItem)" @mouseover = "hoverIndex = index" @mouseleave = "hoverIndex = -1">
            <!-- 设置该条目选中的效果 -->
            <div :class="[{'background-primary':dllVocabularyItem.id === currentThemeId},'h-full',' width-3','border-radius-3']"></div>
            <!-- 展示主题名称 -->
            <div :title="dllVocabularyItem.themeName" class="add-margin-l-16 flex-1 text-ellipsis add-margin-r-8">{{dllVocabularyItem.themeName}}</div>
            <!-- 如果是admin，可以进行主题的删除和编辑 -->
            <el-popover placement="bottom-end" popper-class="menu-dialog"  trigger="hover" >
              <el-row class="white-background add-padding-tb-6 remove-padding-lr-0">
                <!-- 展示编辑按钮 -->
                <el-col :span="24"><span class="menu-item"
                                         @click="editResourceTheme(dllVocabularyItem.id,dllVocabularyItem.themeName)"><i class="el-icon-edit add-margin-r-6"></i>{{$t('loc.edit')}}</span></el-col>
                <!-- 展示删除按钮 -->
                <el-col :span="24"><span class="menu-item" style="color: #f56c6c"
                                         @click="clickDeleteToShowDialog(deleteThemeType,dllVocabularyItem.id)"><i class="el-icon-delete add-margin-r-6"></i>{{$t('loc.delete')}}</span></el-col>
              </el-row>
              <i slot="reference" v-if="dllVocabularyItem.type === 'CUSTOM' && isAdmin && !isFromResource"  class="el-icon-more lg-pointer add-padding-r-16"></i>
            </el-popover>
          </div>
        </div>
      </el-col>
      <!-- 选择词汇资源列表 -->
      <el-col :span="isFromResource ? 19 : 18" v-loading="dllResourceLoading" class="h-full remove-padding-r-0">
        <div class="flex-direction-col display-flex h-full">
          <!-- 展示主题信息与分享资源的数量和按钮 -->
          <div v-if="!isFromResource" class="display-flex  align-items add-margin-tb-6">
            <!-- 主题信息 -->
            <span class="add-margin-r-10 flex-1 font-bold font-size-16 overflow-ellipsis-two">{{currentThemeName + (currentResourceCount > 0 ? ' (' + currentResourceCount + ')' : '')}}</span>
            <!-- 分享资源的数量和按钮 -->
            <div>
              <!-- 分享的数量 -->
              <span v-if="showBatchShare && haveCheckNum > 0">{{$t('loc.selectedTotalDllVocabulary')}}{{haveCheckNum + '/' + (isAdmin ? dllVocabularyResourceList.length - 1 : dllVocabularyResourceList.length)}}</span>
              <!-- 取消批量分享按钮 -->
              <el-button class="add-margin-lr-6 btn-pa-tb-10" v-if="showBatchShare" @click="clickCancelShare">{{$t('loc.cancel')}}</el-button>
              <!-- 该按钮点击一次能选择资源，再点击一次确认批量分享 -->
              <el-button type="primary" :disabled="isAdmin ? dllVocabularyResourceList.length === 1 : dllVocabularyResourceList.length === 0" class="btn-pa-tb-10" @click="clickBatchShareSubject">{{$t('loc.shareDll')}}</el-button>
            </div>
          </div>
          <!-- 资源列表，一行展示4列 -->
          <el-row ref="imgRowRef"
                  :gutter="16"
                  :class="[{'dll-resource-div': !isFromResource},{'height-0':!isFromResource}]"
                  class="flex-1  scrollbar2  remove-margin-l-0 remove-margin-r-0 white-background" >
            <el-col :span="6" :class="[isFromResource ? 'add-margin-b-16' : 'add-margin-t-16']" v-for="(resourceItem,index) in dllVocabularyResourceList" :key="resourceItem.id">
              <!-- 只有管理员才能进行增加资源 -->
              <div v-if="index === 0 && isAdmin && !isFromResource" :style="{'height':addImgHeight + 'px'}" @click="addNewResource" class="border-1-grey  border-radius-4  flex-direction-col flex-center-center white-background lg-pointer">
                <i class="el-icon-plus font-size-24"></i>
                <span>{{$t('loc.addNew')}}</span>
              </div>
              <div v-else-if="isFromResource">
                <key-vocabulary-item not-show-delete="true" is-show-select="true" @select="keyVocabularySelectChange" :dll="resourceItem"></key-vocabulary-item>
              </div>
              <div v-else class="display-flex flex-direction-col position-relative white-background lg-pointer">
                <!-- 自定义资源的标签 -->
                <div v-if="resourceItem.type === 'CUSTOM'" class="position-absolute height-20 add-padding-lr-6 text-center border-radius-3 custom-tag">
                  {{$t('loc.custom')}}
                </div>
                <!-- 选中分享的按钮 -->
                <div v-if="resourceItem.isShowShare" class="position-absolute position-absolute-top-4 check-resource-div">
                  <el-checkbox @change="changeCheck" v-model="resourceItem.isCheck" @click.stop.native="()=>{}"></el-checkbox>
                </div>
                <!-- 资源图片 -->
                <img style="object-fit: cover;" :style="{'height':imageHeight + 'px'}" class="w-full img-border" :src="resourceItem.mediaUrl" @click="previewDllSubject(resourceItem,index)">
                <!-- 资源名称和资源的操作 -->
                <div  class="add-padding-lr-10 display-flex  align-items height-40 content-border">
                  <!-- 资源名称 -->
                  <span :title="resourceItem.name" class="overflow-ellipsis-two display-inline-block flex-1 l-h-1x" style="white-space: pre;">{{resourceItem.name}}</span>
                  <div class="display-flex align-items">
                    <!-- 分享资源的按钮 -->
                    <el-tooltip effect="dark" :content="$t('loc.shareDll')" placement="bottom-start">
                      <i class="el-icon-share lg-pointer color-icon" @click.stop="clickShare(resourceItem)"></i>
                    </el-tooltip>
                    <!-- 只有管理员可以对资源进行编辑 -->
                    <el-popover placement="bottom-end" popper-class="menu-dialog"  trigger="click" >
                      <el-row class="white-background add-padding-tb-6 remove-padding-lr-0">
                        <!-- 编辑资源 -->
                        <el-col :span="24"><span class="menu-item"
                                                 @click="editResource(currentThemeId,resourceItem.mediaId,resourceItem.mediaUrl,resourceItem.id,resourceItem.name, resourceItem.description)"><i class="el-icon-edit add-margin-r-6"></i>{{$t('loc.edit')}}</span></el-col>
                        <!-- 删除资源 -->
                        <el-col  :span="24"><span class="menu-item" style="color: #f56c6c"
                                                 @click="clickDeleteToShowDialog(deleteResourceType,resourceItem.id)"><i class="el-icon-delete add-margin-r-6"></i>{{$t('loc.delete')}}</span></el-col>
                      </el-row>
                      <i slot="reference" v-if="resourceItem.type === 'CUSTOM' && isAdmin" class="el-icon-more add-margin-l-10 lg-pointer color-icon"></i>
                    </el-popover>
                  </div>
                </div>
              </div>
            </el-col>
            <!-- 如果当前账号为非管理员，展示空界面 -->
            <el-col :span="24" v-if="dllVocabularyResourceList.length == 0 && !isAdmin" class="flex-column-center h-full w-full white-background">
              <img src="@/assets/img/dll/pic_empty_dll.png" style="margin-top: -40px">
              <div class="text-center add-margin-t-8 font-size-14 el-icon-question-color" >
                {{$t('loc.plan36')}}
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <!-- 添加编辑主题的弹框 -->
    <deal-dll-resource-theme-dialog @getThemeList="getThemeList" ref="dllResourceThemeRef"></deal-dll-resource-theme-dialog>
    <!-- 添加编辑资源的弹框 -->
    <deal-dll-resource-dialog @getThemeResourceList="getResourceList" ref="dllResourceRef"></deal-dll-resource-dialog>
    <!-- 预览资源的弹框 -->
    <dll-subject-preview-dialog ref="dllSubjectPreviewRef"  @shareSuccessByPreview="shareSuccessByPreview"></dll-subject-preview-dialog>
    <!-- 删除提示对话框 -->
    <delete-sure-dialog ref="deleteDialogRef" @sureDelete="sureDelete" :tip-content="$t('loc.dllResourceDeleteTip')"></delete-sure-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { acrossRole, getCurrentUser } from '../../../utils/common'
import DealDllResourceThemeDialog from './DealDLLResourceThemeDialog'
import DealDllResourceDialog from './DealDLLResourceDialog'
import DllSubjectPreviewDialog from './DLLSubjectPreviewDialog'
import DeleteSureDialog from './DeleteSureDialog'
import KeyVocabularyItem from '../../modules/lesson2/lessonCurriculum/components/CurriculumResources/keyVocabularyItem'
export default {
  name: 'DllVocabularyList',
  components: { KeyVocabularyItem, DeleteSureDialog, DllSubjectPreviewDialog, DealDllResourceDialog, DealDllResourceThemeDialog },
  data () {
    return {
      dllVocabularyThemeList: [], // 主题列表
      currentThemeId: '', // 当前主题id
      currentThemeName: '', // 当前主题名称
      currentResourceCount: 0, // 当前主题的资源数量
      dllVocabularyResourceList: [],// 资源列表
      dllThemeLoading: false, // 主题列表的加载框
      dllResourceLoading: false, // 资源列表的加载框
      hoverIndex: -1, // 主题列表hover的位置
      showBatchShare: false, // 是否展示分享的操作
      haveCheckNum: 0,// 已经选中资源的数量
      isAdmin: false, // 判断是否是管理者,只有管理员可以添加，编辑,删除
      deleteThemeType: 'DELETE_THEME', // 删除类型为主题
      deleteResourceType: 'DELETE_RESOURCE', // 删除类型为资源
      imageHeight: 145, // 资源图片默认的高度
      addImgHeight: 185 // 添加资源图片默认的高度
    }
  },
  mounted () {
    // 判断当前用户的类型
    if (acrossRole('agency_admin','agency_owner')) {
      // 管理员
      this.isAdmin = true
    } else {
      // 非管理员
      this.isAdmin = false
    }
    // 进入界面请求主题列表
    this.getThemeList()
  },
  props: ['isFromResource'],
  methods: {
    // 选中主题的操作
    selectTheme (dllVocabularyItem) {
      this.$analytics.sendEvent('web_dll_library_click_dll_vocabulary')
      this.currentThemeId = dllVocabularyItem.id
      this.currentThemeName = dllVocabularyItem.themeName
      this.showBatchShare = false // 设置分享的状态回到初始状态
      this.haveCheckNum = 0 // 选中的数量置空
      // 获取该主题的资源列表
      this.getResourceList()
    },
    // 点击添加主题的操作
    clickAddNewTheme () {
      this.checkIsBatchShareThenCancel()
      this.$refs.dllResourceThemeRef.showThemeDialog()
    },
    // 通过用户id获取主题列表信息
    getThemeList () {
      this.dllThemeLoading = true
      this.$axios
        .get($api.urls().getResourceTheme + '?userId=' + this.currentUser.user_id)
        .then(res => {
          this.dllThemeLoading = false
          this.dllVocabularyThemeList = res.resourceModels
          if (this.dllVocabularyThemeList && this.dllVocabularyThemeList.length > 0) {
            this.currentThemeId = this.dllVocabularyThemeList[0].id
            this.currentThemeName = this.dllVocabularyThemeList[0].themeName
            this.getResourceList()
          }
        })
        .catch(error => {
          this.dllThemeLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 通过选择的主题和当前机构id获取资源列表信息
    getResourceList () {
      this.dllResourceLoading = true
      this.$axios
        .get($api.urls().getResource + '?themeId=' + this.currentThemeId + '&agencyId=' + this.currentUser.default_agency_id)
        .then(res => {
          this.dllResourceLoading = false
          this.dllVocabularyResourceList = res.dllResourceModels.map((item,index) => {
            item.isCheck = false // 设置是否选中
            item.isShowShare = false // 设置是否展示选择框
            return item
          })
          // 如果是管理员的话，需要展示添加新资源的入口
          if (this.isAdmin && !this.isFromResource) {
            this.dllVocabularyResourceList.splice(0,0,{ id: 'addNew' })
          }
          // 获取当前主题的资源数量
          this.currentResourceCount = this.isAdmin ? this.dllVocabularyResourceList.length - 1 : this.dllVocabularyResourceList.length
          this.changeImgHeight()
          if (this.isFromResource) {
            this.$emit('settingCheck')
          }
        })
        .catch(error => {
          this.dllResourceLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 展示编辑主题的弹框
    editResourceTheme (id,themeName) {
      this.checkIsBatchShareThenCancel()
      this.$refs.dllResourceThemeRef.editTheme(id,themeName)
    },
    // 点击删除主题或者资源
    clickDeleteToShowDialog (deleteFrom,deleteId) {
      this.checkIsBatchShareThenCancel()
      this.$refs.deleteDialogRef.showDeleteSureDialog(deleteFrom,deleteId)
    },
    // 删除主题
    deleteResourceTheme (id) {
      this.$axios
        .get($api.urls().deleteResourceTheme + '?themeId=' + id)
        .then(res => {
          // 删除成功，刷新主题列表，并且关闭删除弹框
          this.$refs.deleteDialogRef.hindDeleteSureDialog()
          this.getThemeList()
          this.$message.success(this.$t('loc.deletedSuccessfully'))
        })
        .catch(error => {
          this.$refs.deleteDialogRef.deleteError(error.response.data.error_message)
        })
    },
    // 点击删除资源
    deleteResource (id) {
      this.$axios
        .get($api.urls().deleteResource + '?resourceId=' + id)
        .then(res => {
          // 删除成功，刷新资源列表，并且关闭删除弹框
          this.$refs.deleteDialogRef.hindDeleteSureDialog()
          this.getResourceList()
          this.$message.success(this.$t('loc.deletedSuccessfully'))
        })
        .catch(error => {
          this.$refs.deleteDialogRef.deleteError(error.response.data.error_message)
        })
    },
    // 点击编辑资源
    editResource (themeId, fileId, fileUrl, resourceId, resourceName, description) {
      this.checkIsBatchShareThenCancel()
      this.$refs.dllResourceRef.editResource(themeId, fileId, fileUrl, resourceId, resourceName, description)
    },
    // 点击添加新的资源
    addNewResource () {
      this.checkIsBatchShareThenCancel()
      this.$refs.dllResourceRef.showResourceDialog(this.currentThemeId)
    },
    // 点击单个资源的分享按钮
    clickShare (resourceItem) {
      this.checkIsBatchShareThenCancel()
      let tempObject = {}
      let tempArray = []
      tempObject.title = this.currentThemeName
      tempObject.content = resourceItem.name
      tempObject.imgUrl = resourceItem.mediaUrl
      tempObject.imgId = resourceItem.mediaId
      tempObject.resourceId = resourceItem.id
      tempArray.push(tempObject)
      // 传递资源信息给父布局，展示分享资源的弹框
      this.$emit('shareDllBySubject',JSON.stringify(tempArray))
    },
    // 点击取消批量分享的按钮
    clickCancelShare () {
      this.showBatchShare = false
      this.haveCheckNum = 0 // 选中的数量置空
      if (this.dllVocabularyResourceList && this.dllVocabularyResourceList.length > 0) {
        for (let i = this.isAdmin ? 1 : 0; i < this.dllVocabularyResourceList.length; i++) {
          this.dllVocabularyResourceList[i].isShowShare = false
          this.dllVocabularyResourceList[i].isCheck = false
        }
      }
    },
    // 点击批量分享的按钮
    clickBatchShareSubject () {
      // 点击一次能选择资源，再点击一次确认批量分享
      if (!this.showBatchShare) {
        this.showBatchShare = true
        for (let i = this.isAdmin ? 1 : 0; i < this.dllVocabularyResourceList.length; i++) {
          this.dllVocabularyResourceList[i].isShowShare = true
        }
      } else {
        // 获取需要分享的资源数组
        let batchShareArray = []
        for (let i = this.isAdmin ? 1 : 0; i < this.dllVocabularyResourceList.length; i++) {
          if (this.dllVocabularyResourceList[i].isCheck) {
            batchShareArray.push(this.dllVocabularyResourceList[i])
          }
        }
        if (batchShareArray.length === 0) {
          this.$message.error(this.$t('loc.selectOneVocabulary'))
          return
        }
        // 处理批量分享资源列表
        let tempArray = []
        for (let i = 0; i < batchShareArray.length; i++) {
          let tempObject = {}
          let resourceItem = batchShareArray[i]
          tempObject.title = this.currentThemeName
          tempObject.content = resourceItem.name
          tempObject.imgUrl = resourceItem.mediaUrl
          tempObject.imgId = resourceItem.mediaId
          tempObject.resourceId = resourceItem.id
          tempArray.push(tempObject)
        }
        this.$emit('shareDllBySubject',JSON.stringify(tempArray))
      }
    },
    // 获取资源选中的数量
    getCheckNum () {
      let checkNum = 0
      for (let i = this.isAdmin ? 1 : 0; i < this.dllVocabularyResourceList.length; i++) {
        if (this.dllVocabularyResourceList[i].isCheck) {
          checkNum++
        }
      }
      return checkNum
    },
    // 点击check的回调方法，获取当前资源的选中数量
    changeCheck () {
      this.haveCheckNum = this.getCheckNum()
    },
    // 点击某个资源进行预览
    previewDllSubject (resourceItem,clickIndex) {
      this.$analytics.sendEvent('web_dll_click_dll_card')
      // 如果当前处于分享的状态，就不进行预览，只是改变选中的状态
      if (this.showBatchShare) {
        resourceItem.isCheck = !resourceItem.isCheck
        // 获取点击选中的数量
        this.changeCheck()
      } else {
        // 打开单个的预览
        let tempArray = []
        for (let i = this.isAdmin ? 1 : 0; i < this.dllVocabularyResourceList.length; i++) {
          let tempObject = {}
          let resourceItem = this.dllVocabularyResourceList[i]
          tempObject.title = this.currentThemeName
          tempObject.content = resourceItem.name
          tempObject.imgUrl = resourceItem.mediaUrl
          tempObject.imgId = resourceItem.mediaId
          tempObject.resourceId = resourceItem.id
          tempObject.description = resourceItem.description
          tempArray.push(tempObject)
        }
        this.$refs.dllSubjectPreviewRef.show(JSON.stringify(tempArray),this.isAdmin ? clickIndex - 1 : clickIndex)
      }
    },
    // 分享成功，刷新界面
    shareSuccess () {
      // 设置分享状态回到初始状态,和取消分享的操作一样
      this.clickCancelShare()
    },
    // 从预览界面中分享成功
    shareSuccessByPreview () {
      this.$emit('shareSuccessByPreview')
    },
    // 点击确认删除的操作
    sureDelete (deleteFrom,deleteId) {
      if (deleteFrom === this.deleteThemeType) {
        this.deleteResourceTheme(deleteId)
      } else if (deleteFrom === this.deleteResourceType) {
        this.deleteResource(deleteId)
      }
    },
    // 判断是否在批量分享操作
    checkIsBatchShareThenCancel () {
      if (this.showBatchShare) {
        this.clickCancelShare()
      }
    },
    changeImgHeight () {
      // 动态计算按照图片16:9 展示
      this.$nextTick(() => {
        if (this.$refs.imgRowRef) {
          let imgWidth = (this.$refs.imgRowRef.$el.offsetWidth - 80) / 4
          this.imageHeight = 0.562 * imgWidth
          this.addImgHeight = this.imageHeight + 40
        }
      })
    },
    keyVocabularySelectChange (selectDll) {
      this.$emit('select',selectDll)
    },
    // 在资源列表中添加成功后，把之前选中的状态置空
    dataInitialize () {
      this.dllVocabularyResourceList = this.dllVocabularyResourceList.map(dll => {
        dll.isCheck = false
        return dll
      })
    },
    settingDllSelected (haveSelectDll) {
      if (this.isFromResource && haveSelectDll && haveSelectDll.length > 0) {
        // 如果是从资源列表进来，判断之前选中的资源是否包含有
        this.dllVocabularyResourceList = this.dllVocabularyResourceList.map(dll => {
          for (let i = 0; i < haveSelectDll.length; i++) {
            if (dll.id === haveSelectDll[i].id) {
              dll.isCheck = true
              break
            }
          }
          return dll
        })
      }
    }
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser() // 当前用户
    })
  }
}
</script>

<style scoped>
.menu-item:hover{
  background-color: #edf1f2;
  cursor: pointer;
}
.menu-item{
  padding: 6px 12px;
  display: inline-block;
  width: 100%;
}
.btn-pa-tb-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
.resource-height {
  height: 185px;
}
.custom-tag {
  color: #67C23A;
  background-color: #E1F3D8;
}
.img-border {
  border-width: 1px 1px 0px 1px;
  border-style: solid;
  border-color: #DCDFE6;
  border-radius: 4px 4px 0px 0px;
}
.content-border {
  border-width: 0px 1px 1px 1px;
  border-style: solid;
  border-color: #DCDFE6;
  border-radius: 0px 0px 4px 4px;
}
.check-resource-div {
  right: 4px;
  padding: 1px 5px 3px;
  background-color: white;
  box-shadow: 0px 0px 8px rgba(0,0,0,0.1);
  border-radius: 3px;
}
.dll-resource-div {
  border-radius: 8px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
  padding-left: 8px;
  padding-right: 8px;
  padding-bottom: 16px;
}
</style>
<style>
  .menu-dialog {
    padding: 0px !important;
    min-width: 100px;
  }
  .check-resource-div  label {
    margin-bottom: 0px !important;
  }
  .check-resource-div label .el-checkbox__input .el-checkbox__inner {
    border-width: 2px;
    border-style: solid;
    border-color: #999999;
  }
  .check-resource-div label .el-checkbox__input.is-checked .el-checkbox__inner,
  .check-resource-div label .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    border-color: #10b3b7 !important;
    border-width: 2px;
    background-color: #10b3b7 !important;
  }
  .check-resource-div label .el-checkbox__input.is-checked+.el-checkbox__label{
    color: #FFFFFF !important;
  }
  .check-resource-div label .el-checkbox__input .el-checkbox__inner:hover{
    border-color: #10b3b7 !important;
    border-width: 2px;
  }
  .check-resource-div label .el-checkbox__input.is-focus .el-checkbox__inner {
    border-color: #10b3b7 !important;
    border-width: 2px;
  }
  .check-resource-div label .el-checkbox__input .el-checkbox__inner::after {
    left: 3px;
    top: 0;
  }
</style>
