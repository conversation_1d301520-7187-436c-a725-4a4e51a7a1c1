<template>
<!-- 展示选择的员工dll列表 -->
  <div class="h-full w-full display-flex flex-direction-col">
    <!-- 日期和内容筛选条件 -->
    <div class="add-padding-tb-10 display-flex align-items justify-content-between w-full add-padding-lr-10"
         :class="{'add-padding-tb-10': !isShowResource}"
    >
      <div v-if="!isShowResource" class="display-flex align-items flex-1 add-margin-r-16">
        <el-avatar v-if="currentStaffId.length > 0" :src="avatarUrlForStaff" class="add-margin-r-10 min-width-40" ></el-avatar>
        <span v-if="currentStaffId.length > 0"  :title="nameForStaff" class="add-margin-r-16 text-ellipsis">{{nameForStaff}}</span>
        <span style="white-space: nowrap;">
        {{$t('loc.totalEntriesNum',{count:totalDllNum})}}
        </span>
      </div>
      <div class="flex-align-center w-full">
        <!-- 开始日期 -->
        <el-date-picker class="select-start-end-date"
                        v-model="startTime"
                        @change="checkStartEndDate"
                        type="date"
                        :clearable="false"
                        :placeholder="$t('loc.from')"
                        :picker-options="pickerOptions"
                        value-format="yyyy-MM-dd"
                        :format="dateFormat">
        </el-date-picker>
        <span class="add-margin-lr-6"> - </span>
        <!-- 结束日期 -->
        <el-date-picker class="select-start-end-date"
                        @change="checkStartEndDate"
                        :clearable="false"
                        v-model="endTime"
                        type="date"
                        :placeholder="$t('loc.to')"
                        :picker-options="pickerOptions"
                        value-format="yyyy-MM-dd"
                        :format="dateFormat">
        </el-date-picker>
        <div v-if="isShowResource" class="flex-1">
        </div>
        <!--关键词搜索-->
        <!-- <el-input  class="add-margin-l-10"
                  style="width: 320px"
                  v-model="searchDllContent"
                  :placeholder="$t('loc.dllSearch')"
                  @keyup.enter.native="searchDLL" size="medium">
          <el-button icon="el-icon-search" slot="append" @click="searchDLL" size="medium"/>
        </el-input> -->
        <el-input style="width: 320px;margin-left: 20px;"
                  @blur="blurSearch"
                  v-model="searchDllContent"
                  :placeholder="$t('loc.dllSearch')"
                  @keyup.enter.native="searchDLL">
            <i
                class="el-icon-search el-input__icon lg-pointer"
                slot="suffix">
            </i>
          </el-input>
      </div>
    </div>
    <div v-if="!isShowResource" class="flex-1 height-0">
      <!-- DLL 列表 -->
      <DLLNewContentList @showDLLNum="getDLLNum" fromType="dllVocabulary" ref="dllVocabularyContentList" @shareDllByStaff="shareDllByStaff"></DLLNewContentList>
    </div>
    <!-- 资源列表，一行展示4列 -->
    <el-row v-else
            :gutter="16"
            v-loading="dllListLoading"
            class="flex-1 scrollbar2 remove-margin-l-0 remove-margin-r-0 white-background" >
      <el-col class="add-margin-t-16" :span="6" v-for="resourceItem in dllStaffResourceList" :key="resourceItem.id">
          <key-vocabulary-item not-show-delete="true" is-show-select="true" @select="keyVocabularySelectChange" :dll="resourceItem"></key-vocabulary-item>
      </el-col>
      <!-- 如果当前账号为非管理员，展示空界面 -->
      <el-col :span="24" v-if="dllStaffResourceList.length == 0" class="flex-column-center h-full w-full white-background">
        <img src="@/assets/img/dll/pic_empty_dll.png" style="margin-top: -40px">
        <div class="text-center add-margin-t-8 font-size-14 el-icon-question-color" >
          {{$t('loc.plan36')}}
        </div>
      </el-col>
    </el-row>
    <!-- 分页 -->
    <div v-if="totalDllNum > 10" class="dll-coach-page-class"  style="text-align: right;">
      <el-pagination  class="add-margin-t-16 font-normal" small
                      @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-sizes="[10, 20]"
                      :page-size="pageSize" :current-page="pageNum" layout="total, sizes, prev, pager, next" :total="totalDllNum">
      </el-pagination>
    </div>
 </div>
</template>

<script>
import { dateFormat } from '@/utils/common'
import tools from '../../../utils/tools'
import KeyVocabularyItem from '../../modules/lesson2/lessonCurriculum/components/CurriculumResources/keyVocabularyItem'
import DLLNewContentList from '../DLLNewContentList'
export default {
  name: 'SchoolDllContentList',
  components: { DLLNewContentList, KeyVocabularyItem },
  data () {
    return {
      totalDllNum: 0, // dll总数
      avatarUrlForStaff: '', // 单个老师的头像
      nameForStaff: '', // 单个老师的名称
      dateFormat: dateFormat(),// 开始结束日期的格式
      searchDllContent: '', // 搜索的dll内容,
      currentStaffId: '', // 当前staff id
      pageNum: 1, // 页数
      pageSize: 10, // 每页总条数
      pickerOptions: {}, // 选择日期的限制条件
      startTime: this.$moment(new Date().getTime() - 3600 * 1000 * 24 * 7).format('YYYY-MM-DD'), // 选择的开始时间
      endTime: this.$moment(new Date().getTime()).format('YYYY-MM-DD'), // 选择的结束时间
      dllStaffResourceList: [], // 从增加材料资源的选择员工发布的词汇列表
      dllListLoading: false
    }
  },
  created () {
    // 设置选择的日期不能大于今天
    this.pickerOptions = {
      disabledDate (time) {
        return time.getTime() > Date.now()
      }
    }
  },
  props: {
    agencyId: {
      type: String
    },
    isShowResource: {
      type: Boolean
    },
    selectedVocabularies: {
      type: Array
    }
  },
  methods: {
    // 搜索 DLL 的操作
    searchDLL () {
      // 搜索从第一页开始
      this.handleCurrentChange(1)
    },
    getAllStaffDllContentList () {
      this.currentStaffId = ''
      if (this.isShowResource) {
        this.getAllStaffDllList(this.agencyId, this.startTime, this.endTime, this.pageNum, this.pageSize,this.searchDllContent)
      } else {
        this.$refs.dllVocabularyContentList.getAllStaffDllList(this.agencyId, this.startTime, this.endTime, this.pageNum, this.pageSize,this.searchDllContent)
      }
    },
    // 获取老师的dll列表
    getStaffDllContentList (staffId,staffUrl,staffName) {
      this.currentStaffId = staffId
      this.avatarUrlForStaff = staffUrl
      this.nameForStaff = staffName
      if (this.isShowResource) {
        this.getStaffDllList(staffId,this.agencyId, this.startTime, this.endTime, this.pageNum, this.pageSize,this.searchDllContent)
      } else {
        this.$refs.dllVocabularyContentList.getStaffDllList(staffId,this.agencyId, this.startTime, this.endTime, this.pageNum, this.pageSize,this.searchDllContent)
      }
    },
    // 获取 DLL 的列表
    getDllContentList () {
      if (tools.isNotEmpty(this.currentStaffId)) {
        this.getStaffDllContentList(this.currentStaffId,this.avatarUrlForStaff,this.nameForStaff)
      } else {
        this.getAllStaffDllContentList()
      }
    },
    // 页数变化的调用
    handleCurrentChange (val) {
      this.pageNum = val
      this.getDllContentList()
    },
    // 每页数量的变化调用
    handleSizeChange (val) {
      this.pageNum = 1
      this.pageSize = val
      this.getDllContentList()
    },
    // 获取总的数量
    getDLLNum (totalNum) {
      this.totalDllNum = totalNum
    },
    // 点击分享dll 内容
    shareDllByStaff (dllTitle,dllContent,dllImgUrl,dllImgId) {
      let tempObject = {}
      let tempArray = []
      tempObject.title = dllTitle
      tempObject.content = dllContent
      tempObject.imgUrl = dllImgUrl
      tempObject.imgId = dllImgId
      tempArray.push(tempObject)
      this.$emit('shareDllByStaff',JSON.stringify(tempArray))
    },
    // 置空搜索框的内容
    clearSearchContent () {
      this.searchDllContent = ''
    },
    clearPageNum () {
      this.pageNum = 1
    },
    // 开始结束日期选中后回调的方法
    checkStartEndDate () {
      let temp = null
      // 判断结束日期是否小于开始日期
      if (this.startTime && this.endTime && this.endTime < this.startTime) {
        temp = ''
        temp = this.startTime
        this.startTime = this.endTime
        this.endTime = temp
      }
      if (this.startTime && this.endTime) {
        this.getDllContentList()
      }
    },
    // 获取全部staff的内容
    getAllStaffDllList (agencyId,fromDate,endDate,pageNum,pageSize,searchContent) {
      this.dllListLoading = true
      let requestContent = { agencyId: agencyId,fromDate: fromDate,endDate: endDate,pageSize: pageSize,pageNum: pageNum,searchContent: searchContent }
      this.$axios
        .post($api.urls().getAgencyHomeworkList, requestContent)
        .then(res => {
          this.dllListLoading = false
          this.totalDllNum = res.total
          this.dllStaffResourceList = res.homeworkListResponses.map(dllItem => {
            let dll = {}
            dll.id = dllItem.id
            dll.mediaId = dllItem.mediaModels && dllItem.mediaModels.length > 0 ? dllItem.mediaModels[0].mediaId : ''
            dll.mediaUrl = dllItem.mediaModels && dllItem.mediaModels.length > 0 ? dllItem.mediaModels[0].mediaUrl : ''
            dll.name = dllItem.content
            if (this.selectedVocabularies.find(x => x.id == dll.id)) {
              dll.isCheck = true
            } else {
              dll.isCheck = false
            }
            return dll
          })
          if (this.isShowResource) {
            this.$emit('settingCheck')
          }
        })
        .catch(error => {
          this.dllListLoading = false
          this.dllStaffResourceList = []
          this.$message.error(error.response.data.error_message)
        })
    },
    // 获取当个staff的内容
    getStaffDllList (staffId,agencyId,fromDate,endDate,pageNum,pageSize,searchContent) {
      let requestContent = { agencyId: agencyId, teacherId: staffId,fromDate: fromDate,endDate: endDate,pageSize: pageSize,pageNum: pageNum,searchContent: searchContent }
      this.$axios
        .post($api.urls().getTeacherHomeworkList , requestContent)
        .then(res => {
          this.dllListLoading = false
          this.totalDllNum = res.total
          this.dllStaffResourceList = res.homeworkListResponses.map(dllItem => {
            let dll = {}
            dll.id = dllItem.id
            dll.mediaId = dllItem.mediaModels && dllItem.mediaModels.length > 0 ? dllItem.mediaModels[0].mediaId : ''
            dll.mediaUrl = dllItem.mediaModels && dllItem.mediaModels.length > 0 ? dllItem.mediaModels[0].mediaUrl : ''
            dll.name = dllItem.content
            if (this.selectedVocabularies.find(x => x.id == dll.id)) {
              dll.isCheck = true
            } else {
              dll.isCheck = false
            }
            return dll
          })
          if (this.isShowResource) {
            this.$emit('settingCheck')
          }
        })
        .catch(error => {
          this.dllListLoading = false
          this.$message.error(error.response.data.error_message)
          // 如果用户不存在，界面没有来及刷新，把界面置空
          if (error.response.data.code && error.response.data.code.toLowerCase() === 'user_not_found') {
            this.dllStaffResourceList = []
          }
        })
    },
    keyVocabularySelectChange (selectDll) {
      this.$emit('select',selectDll)
    },
    dataInitialize () {
      this.dllStaffResourceList = this.dllStaffResourceList.map(dll => {
        dll.isCheck = false
        return dll
      })
    },
    settingDllSelected (haveSelectDll) {
      if (this.isShowResource && haveSelectDll && haveSelectDll.length > 0) {
        // 如果是从资源列表进来，判断之前选中的资源是否包含有
        this.dllStaffResourceList = this.dllStaffResourceList.map(dll => {
          for (let i = 0; i < haveSelectDll.length; i++) {
            if (dll.id === haveSelectDll[i].id) {
              dll.isCheck = true
              break
            }
          }
          return dll
        })
      }
    }
  }
}
</script>

<style scoped>
.dll-coach-page-class >>> .el-pagination__sizes .el-input {
  width: 60%;
}
</style>
<style>
.select-start-end-date {
  width: 120px !important;
}
.select-start-end-date .el-input__inner {
  padding-right: 2px !important;
  height: 40px !important;
}
.select-start-end-date .el-input__prefix .el-input__icon {
  line-height: 40px !important;
}
</style>
