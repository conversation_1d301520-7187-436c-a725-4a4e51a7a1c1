<template>
    <!-- 展示翻译的内容 -->
  <div class="display-flex flex-direction-col">
          <div class="display-flex justify-content-between align-items lg-pa-8" style="background-color: #F5F6F8">
            <span class="font-weight-semibold color-dll-title font-size-16">{{ item.originalName }} <span class="font-normal">({{ item.name }})</span></span>
            <dll-speaker :content="item.content" :lang-code="item.langCode"/>
          </div>
          <el-input v-model="item.content"
                    type="textarea"
                    class="translation-input"
                    :class="[item.content.trim().length > 0 ? 'border-none' : 'input-error-border']"
                    :placeholder="$t('loc.dllTranslatePlaceholder')"></el-input>
  </div>
</template>

<script>
import DllSpeaker from '../../modules/lesson2/lessonLibrary/editor/dll/DllSpeaker'
export default {
  name: 'DllShowTranslate',
  components: { DllSpeaker },
  props: {
    item: {
      type: Object
    }
  }
}
</script>

<style scoped>
.translation-input {
  color: #606266 !important;
  font-size: 15px !important;
}
.input-error-border {
  border: 1px solid #f56c6c;
}
</style>
