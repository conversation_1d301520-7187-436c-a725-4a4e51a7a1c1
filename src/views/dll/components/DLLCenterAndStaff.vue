<template>
<!-- 展示机构中学校和员工 -->
  <div class="w-full scrollbar2 h-full lg-border-radius-8" :class="[isFromResource ? 'bg-color-F5F6F8' : 'white-background']">
      <div class="w-full text-center lg-pa-20">
        <!-- 搜索员工的框 -->
        <el-input class="search-name-input"
                  :placeholder="$t('loc.searchStaffName')"
                  clearable
                  v-model="searchStaffNameContent"
                  @input="searchStaff"
        >
          <i slot="prefix" class="el-input__icon  el-icon-search" ></i>
        </el-input>
      </div>
      <!-- 所有的 DLL -->
      <div :class="[{'child-item-active':isShowAllStaffList,'list-hover-bg-color':hoverContent === 'ALL_DLL'}]"
           class="add-padding-r-20 font-weight-semibold display-flex height-48 align-items"
           @click="selectAllStaff"
           @mouseover = "hoverContent = 'ALL_DLL'" @mouseleave = "hoverContent = ''"
      >
        <!-- 设置该条目选中的效果 -->
        <span :class="[{'background-primary':isShowAllStaffList},'h-full',' width-3','border-radius-3']" style="margin-right: 17px"></span>
        <span>{{$t('loc.allDllActivities')}}</span>
      </div>
      <!-- 展示该机构所有的员工 -->
      <div :class="[isFromResource ? 'div-center-staff-from-resource' : 'div-center-staff']">
        <el-collapse v-model="activeNames">
          <el-collapse-item v-for="dllCenter in dllCentersArray" :key="dllCenter.centerId" :name="dllCenter.centerId">
            <!-- 自定义title 表示学校名称 -->
            <template slot="title">
              <span class="flex-1 text-ellipsis">{{dllCenter.centerName}}</span>
            </template>
            <!-- 展示该学校所有的员工信息 -->
            <div v-if="dllCenter.userModels.length > 0">
              <div v-for="dllStaff in dllCenter.userModels" :key="dllStaff.id"
                   :class="{'child-item-active':dllStaff.centerStaffId === clickCenterStaffId,'list-hover-bg-color':hoverContent === dllStaff.centerStaffId }"
                   class="display-flex align-items height-48 add-padding-r-20 lg-pointer"
                   @click.stop="selectStaff(dllStaff)" @mouseover = "hoverContent = dllStaff.centerStaffId" @mouseleave = "hoverContent = ''">
                <span :class="[{'background-primary':dllStaff.centerStaffId === clickCenterStaffId},'h-full',' width-3','border-radius-3']" style="margin-right: 17px"></span>
                <!-- 展示员工的头像 -->
                <el-avatar :src="dllStaff.avatarMediaUrl" class="add-margin-r-10" :size="36" style="min-width: 36px" ></el-avatar>
                <!-- 展示员工的名字 -->
                <div :title="dllStaff.displayName" class="flex-1 text-ellipsis font-size-14 font-normal">{{dllStaff.displayName | formatUserName}}</div>
              </div>
            </div>
            <!-- 如果该学校没有员工，展示空界面 -->
            <div v-else class="height-40 flex-center-center color-dll-title">
              {{$t('loc.noStaffInCenter')}}
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <!-- 展示所有学校都没有员工的提示语 -->
      <div v-if="dllCentersArray.length === 0" class="flex-center-center add-margin-t-20">
        {{$t('loc.plan36')}}
      </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCurrentUser } from '../../../utils/common'

export default {
  name: 'DllCenterAndStaff',
  props: ['isFromResource'],
  data () {
    return {
      searchStaffNameContent: '', // 搜索员工的名称
      isShowAllStaffList: true, // 是否展示所有的员工的内容，默认展示
      hoverContent: '', // 鼠标悬浮的位置
      dllCentersArray: [], // 机构中的dll学校列表
      originDllCentersArray: [], // 请求接口获取的机构中的dll学校列表，用于搜索
      currentShowTeacherId: '', // 当前展示的dll老师id
      clickCenterStaffId: '', // 点击选中的班级名字和学校id
      activeNames: [] // 展开的item位置
    }
  },
  methods: {
    // 选择所有的员工
    selectAllStaff () {
      this.isShowAllStaffList = true
      this.currentShowTeacherId = ''
      this.clickCenterStaffId = ''
      this.getAllStaffDllContent()
    },
    // 选择单个员工
    selectStaff (staffInfo) {
      this.isShowAllStaffList = false
      this.currentShowTeacherId = staffInfo.id
      this.clickCenterStaffId = staffInfo.centerStaffId
      this.getStaffDllContent(staffInfo)
    },
    // 通过机构获取所有的班级和老师
    getCenterAndTeachersByAgency () {
      this.$axios
        .get($api.urls().getAgencyTeacherList + '?userId=' + this.currentUser.user_id)
        .then(res => {
          if (res.agencyAdmins && res.agencyAdmins.length > 0) {
            let agencyCenter = {}
            // 赋值admins的信息
            agencyCenter.centerName = this.$t('loc.inkindAdmin')
            agencyCenter.centerId = 'ALL_ADMINS'
            agencyCenter.userModels = []
            res.agencyAdmins.forEach(item => {
              item.centerStaffId = agencyCenter.centerId + item.id
              agencyCenter.userModels.push(item)
            })
            this.originDllCentersArray.push(agencyCenter)
          }
          // 赋值机构员工的信息
          if (res.centerTeacherModels && res.centerTeacherModels.length > 0) {
              res.centerTeacherModels.forEach(item => {
              let centerName = item.centerName
              item.userModels = item.userModels.map(staffItem => {
                // 定义学校员工id，防止员工在多个学校里
                staffItem.centerStaffId = centerName + staffItem.id
                return staffItem
              })
              this.originDllCentersArray.push(item)
            })
            this.originDllCentersArray.forEach(item => {
              this.dllCentersArray.push(item)
            })
          }
        })
        .catch(error => {
          this.$message.error(error.response.data.error_message)
        })
    },
    // 获取所有员工的dll信息
    getAllStaffDllContent () {
      this.$emit('getDllList','','','')
    },
    // 获取某个员工的dll信息
    getStaffDllContent (staffInfo) {
      this.$emit('getDllList',staffInfo.id,staffInfo.avatarMediaUrl,staffInfo.displayName)
    },
    // 根据员工姓名键搜索
    searchStaff () {
      let searchContent = this.searchStaffNameContent
      this.dllCentersArray = []
      this.activeNames = []
      this.originDllCentersArray.forEach(item => {
        if (searchContent && searchContent.trim() !== '') {
          let tempUserModels = []
          let tempItem = JSON.parse(JSON.stringify(item))
          // 判断员工的姓名是否含有搜索的内容
          tempItem.userModels.forEach(staffItem => {
            if (staffItem.displayName.toLowerCase().indexOf(searchContent.toLowerCase()) !== -1) {
              tempUserModels.push(staffItem)
            }
          })
          if (tempUserModels.length > 0) {
            tempItem.userModels = tempUserModels
            // 搜索结果默认展开
            this.activeNames.push(tempItem.centerId)
            this.dllCentersArray.push(tempItem)
          }
        } else {
          this.dllCentersArray.push(item)
        }
      })
    }
  },
  mounted () {
    // 通过机构获取所有的班级和老师
    this.getCenterAndTeachersByAgency()
    // 默认展示所有的员工信息
    this.selectAllStaff()
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser() // 当前用户
    })
  }
}
</script>

<style scoped>
.div-center-staff >>> .el-collapse-item__content {
  padding-bottom: 0 !important;
}
.div-center-staff >>> .el-collapse-item__header {
  font-size: 14px;
  font-weight: 600;
  padding-left: 20px;
  padding-right: 20px;
  border-bottom: none;
}
.div-center-staff >>> .el-collapse-item__wrap {
  border-bottom: none;
}
.div-center-staff-from-resource >>> .el-collapse-item__content {
  padding-bottom: 0 !important;
}
.div-center-staff-from-resource >>> .el-collapse-item__header {
  font-size: 14px;
  font-weight: 600;
  padding-left: 20px;
  padding-right: 20px;
  border-bottom: none;
  background-color: #f5f6f8;
}
.div-center-staff-from-resource >>> .el-collapse-item__wrap {
  border-bottom: none;
  background-color: #f5f6f8;
}

.height-48 {
  height: 48px;
}

</style>
