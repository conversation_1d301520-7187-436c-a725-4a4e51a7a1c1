<template>
<!-- 通过树形结构展示学校和班级,单选班级 -->
<div>
  <el-popover v-model="selectGroupVisible" :disabled="isSingleGroup" placement="bottom" width="670" trigger="click" popper-class="select-group-tree-popover">
    <div class="div-group-tree">
      <!-- 树形结构展示学校和班级 -->
      <el-tree :data="centerGroupData"
               :props="defaultProps"
               node-key="id"
               highlight-current
               ref="tree"
               :default-expanded-keys="defaultExpandedCenterIds"
               :current-node-key="currentGroupId"
               @node-click="handleNodeClick">
      <!-- 自定义班级的展示样式 -->
      <span class="display-flex flex-1 justify-content-between lg-pa-6 align-items"
            :class="{disabled:data.disable}"
            slot-scope="{ node,data }">
        <!-- 班级名称 -->
        <span>{{ node.label }}</span>
        <!-- 班级是否选中的标识 -->
        <span v-show="data.isGroupSelect">
          <i class="el-icon-check text-primary"></i>
        </span>
      </span>
      </el-tree>
    </div>
    <!-- 展示选中的班级名称 -->
    <el-input  suffix-icon="el-icon-arrow-down"
               readonly
               slot="reference"
               class="w-full select-group-tree-input"
               :class="[{'lg-pointer': !isSingleGroup}]"
               v-model="currentGroupName">
      <i slot="prefix" v-if="centerGroupLoading" class="el-input__icon el-icon-loading"></i>
    </el-input>
  </el-popover>
</div>
</template>

<script>
import { mapState } from 'vuex'
import { getCurrentUser } from '../../../utils/common'

export default {
  name: 'SelectGroupByTree',
  data () {
    return {
      centerGroupData: [], // 学校和班级列表
      currentGroupName: '', // 当前选择的班级名称
      defaultProps: {
        children: 'children', // 设置默认支的数组名称
        label: 'label', // 设置默认的树和支名称
        isLeaf: 'leaf' // 设置是否可以展示
      },
      selectGroupVisible: false, // 弹框是否展示
      centerGroupLoading: false, // 加载学校和班级列表
      isSingleGroup: false, // 是否是单个班级
      defaultExpandedCenterIds: [] // 进入界面默认展示的学校id
    }
  },
  props: {
    // 选中的班级名称
    currentGroupId: {
      type: String,
      default: ''
    },
    // 选中的学校名称
    currentCenterId: {
      type: String,
      default: ''
    }
  },
  created () {
    // 获取学校和班级信息
    this.getEngagementList()
  },
  methods: {
    // 获取学校和班级信息
    getEngagementList () {
      this.centerGroupLoading = true
      let agencyId = this.currentUser.default_agency_id
      let uid = this.currentUser.user_id
      this.$axios.get($api.urls(uid).centersAndGroups, { params: { agencyId: agencyId } })
        .then(res => {
          for (let i = 0; i < res.length; i++) {
            for (let j = 0; j < res[i].groups.length; j++) {
              // 去除离校的班级和没有学生的班级,不能进行选择
              if (res[i].groups[j].inactive == true) {
                res[i].groups.splice(j,1)
              } else {
                // 判断班级的小孩数量
                if (res[i].groups[j].childCount == 0) {
                  res[i].groups[j].noChild = true
                } else {
                  res[i].groups[j].noChild = false
                }
              }
            }
            // 对应没有班级的学校，不能进行选择
            if (!res[i].groups || res[i].groups.length === 0) {
              res[i].noClass = true
            } else {
              // 判断该学校是否有可以操作的班级
              let tempNoClass = true
              for (let j = 0; j < res[i].groups.length; j++) {
                if (!res[i].groups[j].noChild) {
                  tempNoClass = false
                  break
                }
              }
              res[i].noClass = tempNoClass
            }
          }
          // 判断是否是单学校单班级
          if (res && res.length == 1 && res[0].groups && res[0].groups.length == 1) {
            // 如果是单班级的话就不展示选择班级的弹框
            this.isSingleGroup = true
          } else {
            this.isSingleGroup = false
          }
          this.setCentersAndGroups(res)
        })
        .catch(error => {
          this.$message.error(error.response.data.error_message)
          this.centerGroupLoading = false
        })
    },
    // 赋值学校和班级信息
    setCentersAndGroups (centerlists) {
      this.centerGroupLoading = false
      for (let i = 0; i < centerlists.length; i++) {
        let tempCenterGroup = {}
        let tempCenter = centerlists[i]
        let tempGroupArray = []
        tempCenterGroup.id = tempCenter.id.toUpperCase() // 学校id
        tempCenterGroup.label = tempCenter.name // 学校名称
        tempCenterGroup.disable = tempCenter.noClass // 学校是否有班级
        tempCenterGroup.leaf = true // 可以展开
        tempCenterGroup.isGroupSelect = false // 是否选中该项
        for (let j = 0; j < tempCenter.groups.length; j++) {
          let tempGroupObject = {}
          tempGroupObject.id = tempCenter.groups[j].id.toUpperCase() // 班级id
          tempGroupObject.label = tempCenter.groups[j].name // 班级名称
          tempGroupObject.disable = tempCenter.groups[j].noChild // 班级是否有小孩
          tempGroupObject.leaf = false // 不可以展开
          if (tempCenter.groups[j].id.toUpperCase() === this.currentGroupId.toUpperCase()) {
            tempGroupObject.isGroupSelect = true // 是否选中该班级
            this.currentGroupName = tempCenter.groups[j].name
          } else {
            tempGroupObject.isGroupSelect = false // 是否选中该班级
          }
          tempGroupArray.push(tempGroupObject)
        }
        tempCenterGroup.children = tempGroupArray
        this.centerGroupData.push(tempCenterGroup)
      }
      // 设置默认展开的学校id
      this.defaultExpandedCenterIds = []
      this.defaultExpandedCenterIds.push(this.currentCenterId)
    },
    // 点击某个节点
    handleNodeClick (data) {
      if (!data.leaf) {
        // 点击的是班级
        if (data.disable) {
          this.$message.error(this.$t('loc.noChildrenInClass'))
          // 回到之前选中的班级，展示选中的背景色
          this.$refs.tree.setCurrentKey(this.currentGroupId)
          return
        }
        this.currentGroupId = data.id
        // 赋值当前选中学校和班级信息
        for (let i = 0; i < this.centerGroupData.length; i++) {
          for (let j = 0; j < this.centerGroupData[i].children.length; j++) {
            if (this.centerGroupData[i].children[j].id === this.currentGroupId) {
              this.centerGroupData[i].children[j].isGroupSelect = true
              this.currentCenterId = this.centerGroupData[i].id
              this.currentGroupName = this.centerGroupData[i].children[j].label
            } else {
              this.centerGroupData[i].children[j].isGroupSelect = false
            }
          }
        }
        // 关闭弹框
        this.selectGroupVisible = false
        this.$emit('changeGroupId',this.currentGroupId)
      } else {
        this.$refs.tree.setCurrentKey(this.currentGroupId)
      }
    },
    // 设置当前班级的信息，第一个才打开的时候默认展开选中的学校和班级
    setCurrentGroup () {
      // 设置默认展开的学校id
      this.defaultExpandedCenterIds = []
      this.defaultExpandedCenterIds.push(this.currentCenterId)
      this.$refs.tree.setCurrentKey(this.currentGroupId)
      // 赋值当前选中学校和班级信息
      for (let i = 0; i < this.centerGroupData.length; i++) {
        for (let j = 0; j < this.centerGroupData[i].children.length; j++) {
          if (this.centerGroupData[i].children[j].id === this.currentGroupId) {
            this.centerGroupData[i].children[j].isGroupSelect = true
            this.currentGroupName = this.centerGroupData[i].children[j].label
          } else {
            this.centerGroupData[i].children[j].isGroupSelect = false
          }
        }
      }
    }
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser() // 当前用户
    })
  }
}
</script>

<style scoped>
.div-group-tree >>> .el-tree-node__content {
  height: 40px !important;
}
</style>
<style>
.select-group-tree-popover {
  padding: 0 !important;
  max-height: 300px;
  overflow: auto;
  /* 兼容火狐 */
  scrollbar-width: thin;
}
.select-group-tree-popover::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 6px; /* 高宽分别对应横竖滚动条的尺寸 */
  height: 1px;
}

.select-group-tree-popover::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 10px;
  /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1); */
  background: #dddee0;
}

.select-group-tree-popover::-webkit-scrollbar-track {
  /* 滚动条里面轨道 */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  /* border-radius: 10px; */
  /* background: #EDEDED; */
}
.select-group-tree-input .el-input__inner {
  cursor: inherit;
}
</style>
