<template>
  <!-- 添加编辑主题的弹框 -->
  <div>
    <el-dialog :title="isEdit ? $t('loc.lessons2LessonEdit') : $t('loc.addNewSubject')" width="510px" @close="clearData"  custom-class="add-new-subject"  :visible.sync="addNewSubjectVisible" :lock-scroll="false" top="30vh" :close-on-click-modal="false">
      <div>
        <div class="add-margin-b-10 font-size-16">
          {{$t('loc.subjectName')}}
        </div>
        <el-input
          type="text"
          class="w-full"
          :placeholder="$t('loc.inputSubjectName')"
          v-model="themeName"
          maxlength="50"
          show-word-limit
        >
        </el-input>
      </div>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button @click="clickCancel" plain="" class="cancelBtn">{{$t('loc.cancel')}}</el-button>
        <el-button :disabled="themeName.trim().length ===0 "  type="primary" @click="clickAddNewSubject" class="saveBtn" :loading="submitLoading">{{$t('loc.save')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DealDllResourceThemeDialog',
  data () {
    return {
      submitLoading: false,
      addNewSubjectVisible: false,
      themeName: '',
      themeId: '',
      isEdit: false
    }
  },
  methods: {
    // 点击添加新的资源
    clickAddNewSubject () {
      this.submitLoading = true
      // 编辑资源的操作
      if (this.isEdit) {
        this.$axios
          .post($api.urls().editResourceTheme,{ id: this.themeId,themeName: this.themeName })
          .then(res => {
            if (res.success) {
              this.submitLoading = false
              this.clearData()
              this.addNewSubjectVisible = false
              this.$message.success(this.$t('loc.saveSuccess'))
              this.$emit('getThemeList') // 刷新资源列表
            }
          })
          .catch(error => {
            this.submitLoading = false
            this.$message.error(error.response.data.error_message)
          })
      } else {
        // 新增资源的操作
        this.$axios
          .post($api.urls().createResourceTheme,{ id: this.themeId,themeName: this.themeName })
          .then(res => {
            if (res.success) {
              this.submitLoading = false
              this.clearData()
              this.addNewSubjectVisible = false
              this.$message.success(this.$t('loc.saveSuccess'))
              this.$emit('getThemeList') // 刷新资源列表
            }
          })
          .catch(error => {
            this.submitLoading = false
            this.$message.error(error.response.data.error_message)
          })
      }
    },
    // 打开新增资源弹框
    showThemeDialog () {
      this.addNewSubjectVisible = true
    },
    // 打开编辑资源弹框
    editTheme (themeId,themeName) {
      this.themeId = themeId
      this.themeName = themeName
      this.addNewSubjectVisible = true
      this.isEdit = true
      this.submitLoading = false
    },
    // 点击取消操作
    clickCancel () {
      this.addNewSubjectVisible = false
      this.clearData()
    },
    // 清空数据
    clearData () {
      this.themeId = ''
      this.themeName = ''
      this.isEdit = false
    }
  }
}
</script>

<style scoped>
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}
.cancelBtn{
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}
.saveBtn{
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}
</style>
<style>
.add-new-subject {
  margin-bottom: 0 !important;
}
.add-new-subject .el-dialog__footer {
  border-top: 0px solid #EEEEEE;
  padding: 20px 24px 24px;
  color:#323338;
}
.add-new-subject .el-dialog__header {
  padding: 24px 24px 15px;
  border-bottom: 0px solid #EEEEEE;
  color:#323338;
}
.add-new-subject .el-dialog__header .el-dialog__title {
  font-size: 20px !important;
}
.add-new-subject .el-dialog__header .el-dialog__headerbtn {
  top: 24px;
}
.add-new-subject .el-dialog__body {
  padding: 0 24px;
  color:#323338;
}
</style>
