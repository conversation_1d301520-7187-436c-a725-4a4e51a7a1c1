<template>
  <!-- 确认删除对话框 -->
  <el-dialog :title="$t('loc.confirmation')" custom-class="deleteDialog" :append-to-body="isInnerDialog"  :visible.sync="deleteDialogVisible" :lock-scroll="false" width="35%" :close-on-click-modal="false" :close-on-press-escape="false" >
    <span class="font-size-16" v-text="tipContent"></span>
    <span slot="footer" class="dialog-footer">
      <el-button class="lg-plain-btn cancelBtn" @click="deleteDialogVisible = false">{{$t('loc.cancel')}}</el-button>
      <el-button type="danger" class="confirmBtn" @click="deletesSureConfirm" :loading="btnDelLoading">{{$t('loc.confirm')}}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'DeleteSureDialog',
  props: {
    isInnerDialog: {
      type: Boolean,
      default: false
    },
    tipContent: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      deleteFrom: '', // 弹出删除弹框的来源
      deleteDialogVisible: false, // 展示删除的弹框
      btnDelLoading: false, // 展示删除的进度条
      deleteId: '' // 要删除的id
    }
  },
  methods: {
    // 确认删除的操作
    deletesSureConfirm () {
      this.btnDelLoading = true
      this.$emit('sureDelete',this.deleteFrom,this.deleteId)
    },
    // 删除成功后关闭此弹框
    hindDeleteSureDialog () {
      this.deleteFrom = ''
      this.deleteId = ''
      this.btnDelLoading = false
      this.deleteDialogVisible = false
    },
    // 展示删除弹框
    showDeleteSureDialog (deleteFrom,deleteId) {
      this.deleteDialogVisible = true
      this.deleteFrom = deleteFrom
      this.deleteId = deleteId
    },
    // 展示删除错误的提示
    deleteError (deleteErrorInfo) {
      this.btnDelLoading = false
      this.$message.error(deleteErrorInfo)
    }
  }
}
</script>

<style scoped>
.cancelBtn{
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}
.cancelBtn:hover{
  color: #10B3B7;
}

.confirmBtn{
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
  border: none;
}
</style>
<style>
.deleteDialog .el-dialog__body {
  padding: 10px 20px !important;
}
.deleteDialog .el-dialog__title {
  font-size: 20px !important;
}
</style>
