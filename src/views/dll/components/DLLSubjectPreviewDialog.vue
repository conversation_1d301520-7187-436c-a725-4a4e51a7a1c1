<template>
  <!-- DLL 词汇库演示的功能弹框 -->
  <div class="dll_preview_dialog">
    <el-dialog :show-close="false" @close="beforeClosePreview" top="10vh" :visible.sync="dLLHomeworkDemoDialogVisible" width="80%" :close-on-click-modal="false" :lock-scroll="false">
      <!-- 自定义弹框的title内容 -->
      <span slot="title" class="flex-space-between w-full">
        <span class="font-size-24 font-weight-semibold dll-text-title-color">{{dllContentList.length >0 ? dllContentList[carouseIndex].title : ''}}<span style="font-weight: 400;">{{showPosition}}</span></span>
      </span>
      <!-- DLL 词汇库左右滑动 -->
      <el-carousel
        class="demo-carousel h-full"
        v-if="dllContentList && dllContentList.length > 0"
        :initial-index = "carouseIndex"
        indicator-position="none"
        arrow="never"
        :autoplay="false"
        ref="showContent"
        @change="carouselChange">
        <el-carousel-item  v-for="item in dllContentList" :key="item.resourceId">
          <div class="display-flex white-background h-full">
            <!-- 展示图片 高度，宽度自适应 -->
            <div class="width-60-percent h-full add-padding-lr-12">
              <el-image class="show-equal-rate-img" :src="item.imgUrl" fit="contain"></el-image>
            </div>
            <!-- 展示 DLL 具体内容 -->
            <div class="h-full add-padding-lr-12" v-loading="languageLoading" style="width:40%;overflow-y: hidden">
              <div  class="h-full display-flex flex-direction-col scrollbar2 add-padding-r-6 new-font-color">
                <!-- DLL 英语语言播放 -->
                <div class="display-flex justify-content-between align-items">
                  <span class="font-size-18 lg-color-text-primary">{{$t('loc.addDLLContentTitle')}}</span>
                  <dll-speaker :content="item.content" :lang-code="defaultEnglishCode"/>
                </div>
                <!-- DLL 内容 -->
                <div class="font-size-20 add-margin-tb-6 pre-wrap font-weight-600">{{item.content}}</div>
                <div class="add-margin-tb-6">{{item.description}}</div>
                <!-- 选择的语言 -->
                <div style="display: flex;align-items: center">
                  <el-input
                    suffix-icon="el-icon-arrow-down"
                    readonly
                    :placeholder="$t('loc.pleaseSelectLanguage')"
                    @click.native="showSelectLanguageDialog"
                    class="flex-1 select-language-input lg-pointer"
                    v-model="selectedLanguageNum">
                  </el-input>
                  <el-button  type="primary" class="translateBtn add-margin-l-8"
                              :loading="translateLoading"
                              :disabled="selectedLanguageNum.trim().length === 0"
                              @click="clickTranslate()">{{ $t('loc.translate') }}
                  </el-button>
                </div>
                <!-- 展示翻译的内容 -->
                <div v-for="(translateItem) in item.translationList" :key="translateItem.langCode"
                     class="translation-div add-margin-t-10">
                  <dll-show-translate ref="langCode" :item="translateItem"></dll-show-translate>
                </div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button @click="closeDemoDialog" plain class="cancelBtn">{{$t('loc.cancel')}}</el-button>
        <el-button  type="primary" @click="clickAddNewSubject" class="saveBtn" :loading="submitLoading">{{$t('loc.shareDll')}}</el-button>
      </span>
    </el-dialog>
    <!-- 关闭弹框按钮 -->
    <el-button  v-if="dLLHomeworkDemoDialogVisible" @click="closeDemoDialog" style="right:6.5%;top: 15%;" :class="['el-icon-btn',{'z-index-1':isShowElseDialog}]"><i class="el-icon-close"></i></el-button>
    <!-- 切换下一个按钮 -->
    <el-button  v-if="dLLHomeworkDemoDialogVisible" @click="nextContent" style="right:6.5%;top: 52.5%;" :class="['el-icon-btn',{'z-index-1':isShowElseDialog}]"><i class="el-icon-arrow-right"></i></el-button>
    <!-- 切换上一个按钮 -->
    <el-button  v-if="dLLHomeworkDemoDialogVisible" @click="prevContent" style="left:6.5%;top: 52.5%;" :class="['el-icon-btn',{'z-index-1':isShowElseDialog}]"><i class="el-icon-arrow-left"></i></el-button>
    <!-- 选择语言的弹框 -->
    <select-language-dialog ref="selectLanguageDialog"
                            @func="getSelectedLanguage"
                            @closeSelectLanguageDialog="closeSelectLanguageDialog">
    </select-language-dialog>
    <!-- 分享的弹框 -->
    <dll-share-dialog ref="dllShareFromPreviewDialogRef"
                      @shareSuccess="shareSuccess"
                      @changeLanguage="getSelectedLanguage"
                      @closeShareDialog="closeShareDialog">
    </dll-share-dialog>
  </div>
</template>

<script>
import SelectLanguageDialog from '../SelectLanguageDialog'
import DllShowTranslate from './DLLShowTranslate'
import DllSpeaker from '../../modules/lesson2/lessonLibrary/editor/dll/DllSpeaker'
import DllShareDialog from './DLLShareDialog'
export default {
  name: 'DllSubjectPreviewDialog',
  components: { DllShareDialog, DllSpeaker, DllShowTranslate, SelectLanguageDialog },
  data () {
    return {
      dLLHomeworkDemoDialogVisible: false, // 判断演示模式弹框是否弹出
      dllContentList: [], // 演示的内容数组
      carouseIndex: 0, // 演示界面滑动的下标
      showPosition: '', // 演示内容的位置占总位置的值 1/11
      isPlayLanguage: false, // 语言的音频是否正在播放
      isGetVoiceLoading: false, // 是否界面有获取语音的加载框
      languageLoading: false, // 加载选择的语言和翻译的内容
      selectedLanguageNum: '', // 展示选择的语言数
      haveSelectedLanguageCodes: '', // 已经选择的语言code值
      translateBeforeLanguage: '', // 记录点击翻译或者从编辑进行时的语言内容
      responseFinishNum: 0, // 记录批量请求翻译的完成请求数量
      submitLoading: false, // 加载提交
      currentSelectDllInfo: {}, // 当前选择的dll信息
      groupId: '', // 当前的班级id
      translateLoading: false, // 点击翻译的进度条
      defaultEnglishCode: 'en', // 默认播放英语语言
      isShowElseDialog: false // 是否弹出选择语言和分享的弹框
    }
  },
  methods: {
    // 展示 DLL 词汇库演示的弹框
    show (dllInfoString,clickIndex) {
      let tempArray = JSON.parse(dllInfoString)
      this.dllContentList = tempArray.map(item => {
        item.translationList = []
        return item
      })
      this.dLLHomeworkDemoDialogVisible = true
      // 设置当前默认展示的位置
      this.carouseIndex = clickIndex
      // 设置默认展示的 DLL 信息
      this.currentSelectDllInfo = this.dllContentList[this.carouseIndex]
      this.showPosition = ' (' + (this.carouseIndex + 1) + '/' + (this.dllContentList.length) + ')'
      this.groupId = sessionStorage.getItem('dllGroupId')
      this.submitLoading = false
      // 获取当前班级的语言信息
      this.getHaveSelectedLanguage()
    },
    // 切换 DLL 内容的回调
    carouselChange (newIndex) {
      this.carouseIndex = newIndex
      this.showPosition = ' (' + (this.carouseIndex + 1) + '/' + (this.dllContentList.length) + ')'
      this.currentSelectDllInfo = this.dllContentList[this.carouseIndex]
    },
    // 进入下一个
    nextContent () {
      this.$refs.showContent.next()
    },
    // 进入上一个
    prevContent () {
      this.$refs.showContent.prev()
    },
    // 关闭演示弹框
    closeDemoDialog () {
      this.beforeClosePreview()
      this.dLLHomeworkDemoDialogVisible = false
    },
    // 关闭弹框，清除数据
    beforeClosePreview () {
      this.dllContentList = []
      this.carouseIndex = 0
      this.selectedLanguageNum = ''
      this.haveSelectedLanguageCodes = ''
      this.translateBeforeLanguage = ''
      this.responseFinishNum = 0
      this.submitLoading = false
      this.isShowElseDialog = false
    },
    // 点击分享按钮，进入分享界面
    clickAddNewSubject () {
      this.isShowElseDialog = true
      let tempArray = []
      tempArray.push(this.currentSelectDllInfo)
      this.$refs.dllShareFromPreviewDialogRef.showShareDialog(JSON.stringify(tempArray),false,true)
    },
    // 处理翻译的操作
    dealTranslate (dllItem,isClick) {
      if (isClick) {
        this.translateLoading = true
      }
      this.$axios.post($api.urls().homeworkTranslate, {
        content: dllItem.content,
        format: 'text', // 原文文本格式 text/html
        langCodes: this.haveSelectedLanguageCodes.split(',')
      })
        .then(data => {
          if (data && data.length > 0) {
            // 如果通过分享 dll 和 单词点击翻译的情况
            if (isClick) {
              this.currentSelectDllInfo.translationList = data
            } else {
              let tempOriginalContent = data[0].originalContent
              for (let i = 0; i < this.dllContentList.length; i++) {
                if (this.dllContentList[i].content === tempOriginalContent) {
                  this.dllContentList[i].translationList = data
                }
              }
            }
          }
          this.setLoadingHide(isClick)
        }).catch(error => {
        this.$message.error(error.response.data.error_message)
        this.setLoadingHide(isClick)
      })
    },
    // 获取已设置的语言
    getHaveSelectedLanguage () {
      if (!this.groupId) {
        return
      }
      this.languageLoading = true
      this.$axios
        .get($api.urls().getGroupLanguageList + '?groupId=' + this.groupId)
        .then(res => {
          let haveSelectedLanguageList = res.selectList
          if (haveSelectedLanguageList && haveSelectedLanguageList.length > 0) {
            // 表示该班级已经设置过语言
            this.selectedLanguageNum = this.$t('loc.selectedLanguageNum', { num: haveSelectedLanguageList.length })
            let translationCodes = []
            haveSelectedLanguageList.forEach(languageItem => {
              translationCodes.push(languageItem.code)
            })
            this.haveSelectedLanguageCodes = translationCodes.toString()
            // 批量翻译内容
            this.batchDealTranslate()
          } else {
            this.selectedLanguageNum = ''
            this.haveSelectedLanguageCodes = ''
            this.languageLoading = false
          }
        })
        .catch(error => {
          this.languageLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 批量处理翻译的操作
    batchDealTranslate () {
      for (let i = 0; i < this.dllContentList.length; i++) {
        this.dealTranslate(this.dllContentList[i],false)
      }
    },
    // 设置翻译加载框的消失
    setLoadingHide (isClick) {
      if (isClick) {
        this.translateLoading = false
      } else {
        this.responseFinishNum++
        if (this.responseFinishNum === this.dllContentList.length) {
          // 表示所有的内容已经翻译完成
          this.languageLoading = false
        }
      }
    },
    // 点击预览界面中的翻译按钮
    clickTranslate () {
      this.dealTranslate(this.currentSelectDllInfo,true)
    },
    // 展示选中语言的弹框
    showSelectLanguageDialog () {
      this.isShowElseDialog = true
      this.$refs.selectLanguageDialog.show(this.groupId)
    },
    // 获取到选择的语言
    getSelectedLanguage (val) {
      this.selectedLanguageNum = this.$t('loc.selectedLanguageNum', { num: val.split(',').length })
      this.haveSelectedLanguageCodes = val
      // 选择语言后自动更新翻译内容
      this.batchDealTranslate()
    },
    // 关闭选择语言或者进享的弹框操作
    closeSelectLanguageDialog () {
      this.isShowElseDialog = false
    },
    // 从预览界面中分享成功，关闭界面
    shareSuccess () {
      this.$emit('shareSuccessByPreview')
      this.closeDemoDialog()
    },
    // 关闭分享弹框界面
    closeShareDialog () {
      this.isShowElseDialog = false
    }
  }
}
</script>

<style scoped>

.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.cancelBtn{
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}

.saveBtn{
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}

.dll_preview_dialog >>> .el-dialog__header{
  padding: 10px 24px;
  border-bottom: #eee solid 0;
}

.dll_preview_dialog >>> .el-dialog__body{
  padding: 0px;
  height: calc(73vh - 70px);
  overflow-y: hidden;
  color:#323338;
  font-size: 16px;
}

.dll_preview_dialog >>> .el-dialog__footer{
  border-top: 0px solid #EEEEEE;
  padding: 10px 24px 24px;
  color:#323338;
}

.el-icon-btn {
  border: none;
  outline: 0;
  padding: 0;
  margin: 0;
  height: 36px;
  width: 36px;
  cursor: pointer;
  -webkit-transition: .3s;
  transition: .3s;
  border-radius: 50%;
  background-color: #9CA4AB;
  color: #FFF;
  position: fixed;
  z-index: 9999;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  text-align: center;
  font-size: 18px;
}
.show-equal-rate-img {
  display: inline-flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}
.width-60-percent {
  width: 60%;
}
.translation-div {
  border: #eeeeee solid 1px;
  border-radius: 4px;
}
.z-index-1 {
  z-index: 1;
}
</style>
<style>
.show-equal-rate-img  .el-image__inner {
  max-width: 100%;
  max-height: 100%;
  width: 100% !important;
  height: initial !important;
}
.demo-carousel {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.demo-carousel .el-carousel__container {
  height: calc(73vh - 70px);
}
.select-language-input .el-input__inner {
  color: #10B3B7;
  font-size: 16px;
  cursor: pointer;
}
</style>
