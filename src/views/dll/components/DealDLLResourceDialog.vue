<template>
  <!-- 添加编辑资源的弹框 -->
  <div>
    <el-dialog :title="isEdit ? $t('loc.lessons2LessonEdit') :$t('loc.addNew')" width="510px" @close="clearData"  custom-class="add-new-vocabulary-dialog"  :visible.sync="addNewVocabularyVisible" :lock-scroll="false" top="20vh" :close-on-click-modal="false">
      <div class="display-flex flex-direction-col">
        <div class="add-margin-b-10 font-size-16">
          <span class="font-color-red font-size-14">* </span>{{$t('loc.addDLLContentTitle')}}
        </div>
        <!-- 资源内容的输入框 -->
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 6}"
          class="w-full"
          resize="none"
          maxlength="200"
          show-word-limit
          :placeholder="$t('loc.inputKeyVocabulary')"
          v-model="resourceName"
        >
        </el-input>

        <div class="add-margin-tb-10 font-size-16">
          {{ $t('loc.dll7') }}
        </div>
        <!-- 资源内容的输入框 -->
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 6}"
          class="w-full"
          resize="none"
          maxlength="200"
          show-word-limit
          :placeholder="$t('loc.dll5')"
          v-model="description"
        >
        </el-input>

        <div class="add-margin-tb-10 font-size-16">
          <span class="font-color-red font-size-14">* </span>{{$t('loc.selectImage')}}
        </div>
        <!-- 选择照片的控件 -->
        <el-upload
          :class="[{'add-resource-upload-plus': uploadHavePlus}, {'add-resource-upload-no-plus': uploadHaveNoPlus || fileUploadLoading}, 'add-resource-upload']"
          v-loading="fileUploadLoading"
          list-type="picture-card"
          action
          :http-request="(file) => fileUpload(file, attach)"
          :on-success="
                (res, file, fileList) => {
                  uploadSuccess(res, file, fileList, attach)
                }
              "
          :on-change="
                (file, fileList) => {
                  upAttachChange(file, fileList, attach)
                }
              "
          :on-remove="
                (file, fileList) => {
                  handleRemove(file, attach)
                }
              "
          accept=".jpg,.png,.jpeg"
          :limit="1"
          :file-list="attach.medias"
          :before-upload="beforeAvatarUpload"
        >
          <i slot="default" class="el-icon-picture-outline"></i>
          <div slot="file" slot-scope="{file}">
            <!-- 展示选中的图片 -->
            <img class="el-upload-list__item-thumbnail"
                 style="object-fit: cover;"
                 :src="file.url">
            <span class="el-upload-list__item-actions">
              <!-- 预览的按钮 -->
              <span
                class="el-upload-list__item-preview"
                @click="handlePictureCardPreview(file)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <!-- 删除的按钮 -->
              <span
                class="el-upload-list__item-delete"
                @click="handleRemove(file, attach)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
      </div>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button @click="clickCancel" plain="" class="cancelBtn">{{$t('loc.cancel')}}</el-button>
        <el-button :disabled="resourceName.trim().length === 0 || attach.medias.length === 0"  type="primary" @click="clickAddNewResource" class="saveBtn" :loading="submitLoading">{{$t('loc.save')}}</el-button>
      </span>
    </el-dialog>
    <!-- 查看大图的弹框 -->
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import configBaseUrl from '@/utils/setBaseUrl'
import axios from 'axios'
import store from '@/store'

export default {
  name: 'DealDllResourceDialog',
  data () {
    return {
      submitLoading: false, // 提交资源的进度条
      addNewVocabularyVisible: false, // 展示添加编辑的弹框
      resourceName: '', // 资源名称
      resourceId: '', // 资源id
      isEdit: false, // 是否是编辑
      fileUploadLoading: false, // 上传图片的进度图
      attach: { medias: [],mediakeys: [] }, // 上传图片的文件数组和图片id的数组
      uploadHavePlus: true, // 上传图片含有plus的class
      uploadHaveNoPlus: false, // 上传图片不含有plus的class
      dialogImageUrl: '', //  查看大图的url
      dialogVisible: false, // 查看大图弹框的标识
      themeId: '', // 主题id
      description: '' // 关键词描述
    }
  },
  methods: {
    // 上传资源
    clickAddNewResource () {
      this.submitLoading = true
      // 如果是编辑，请求编辑接口
      if (this.isEdit) {
        this.$axios
          .post($api.urls().editResource,{ id: this.resourceId,content: this.resourceName,mediaId: this.attach.medias[0].fileKey,themeId: this.themeId, description: this.description })
          .then(res => {
            if (res.success) {
              this.submitLoading = false
              this.clearData()
              this.addNewVocabularyVisible = false
              // 编辑成功后，获取资源列表
              this.$emit('getThemeResourceList')
            }
          })
          .catch(error => {
            this.submitLoading = false
            this.$message.error(error.response.data.error_message)
          })
      } else {
        // 如果是添加，请求添加接口
        this.$axios
          .post($api.urls().createResource,{ id: this.resourceId,content: this.resourceName,mediaId: this.attach.medias[0].fileKey,themeId: this.themeId, description: this.description })
          .then(res => {
            if (res.success) {
              this.submitLoading = false
              this.clearData()
              this.addNewVocabularyVisible = false
              this.$emit('getThemeResourceList')
            }
          })
          .catch(error => {
            this.submitLoading = false
            this.$message.error(error.response.data.error_message)
          })
      }
    },
    // 添加资源的弹框
    showResourceDialog (themeId) {
      this.themeId = themeId
      this.description = '' // 重置关键词描述
      this.addNewVocabularyVisible = true
    },
    // 编辑资源的弹框
    editResource (themeId, fileId, fileUrl, resourceId, resourceName, description) {
      this.themeId = themeId
      this.resourceId = resourceId
      this.resourceName = resourceName
      this.isEdit = true
      this.submitLoading = false
      this.description = description
      // 给图片赋值
      let medias = []
      let mediaKeys = [fileId]
      let file = {}
      file.fileKey = fileId // 文件id
      file.url = fileUrl // 文件url
      medias.push(file)
      this.attach.medias = medias
      this.attach.mediaKeys = mediaKeys
      this.uploadHavePlus = false
      this.uploadHaveNoPlus = true
      this.addNewVocabularyVisible = true
    },
    // 点击取消按钮
    clickCancel () {
      this.addNewVocabularyVisible = false
      this.clearData()
    },
    // 弹框消失清除数据
    clearData () {
      this.themeId = ''
      this.isEdit = false
      this.submitLoading = false
      this.resourceName = ''
      this.resourceId = ''
      this.fileUploadLoading = false
      this.attach = { medias: [],mediakeys: [] }
      this.uploadHavePlus = true
      this.uploadHaveNoPlus = false
      this.dialogImageUrl = ''
      this.dialogVisible = false
    },
    // 上传照片
    fileUpload (content, question) {
      this.fileUploadLoading = true
      let file = content.file
      let param = new FormData()
      // 通过append向form对象添加数据
      // param.append('file', content.file)
      param.append('file', file)
      param.append('type','jpg') // 这里要添加传递文件的类型，图片传jpg,视频传mp4，录音文件传aac
      // FormData私有类对象，访问不到，可以通过get判断值是否传进去
      const configs = {
        baseURL: configBaseUrl,
        headers: {
            'Content-Type': 'multipart/form-data',
            'X-UID': store.state.user.uid
          }
      }
      axios.post($api.urls().uploadFile, param, configs)
        .then(res => {
          this.fileUploadLoading = false
          content.onSuccess(res.data,question)
        }).catch(error => {
        if (error.response) {
          content.onError()
          this.$message.error(error.response.data.error_message)
          this.fileUploadLoading = false
        }
      })
    },
    // 图片上传到服务器成功后
    uploadSuccess (res,file, fileList, question) {
      let medias = question.medias || []
      file.fileKey = res.id // 文件id
      medias.push(file)
      question.medias = medias
      // key
      let mediaKeys = question.mediaKeys || []
      mediaKeys.push(res.id)
      question.mediaKeys = mediaKeys
      // 修改只能选择一种图片
      if (medias.length >= 1) {
        this.uploadHavePlus = false
        this.uploadHaveNoPlus = true
      }
    },
    upAttachChange (file, fileList,question) {
      this.hideAttachUploadEdit = fileList.length >= 1
    },
    // 删除图片的操作
    handleRemove (file,question) {
      let medias = []
      let mediaKeys = []
      for (let media of question.medias) {
        if (media.fileKey !== file.fileKey) {
          medias.push(media)
          mediaKeys.push(media.fileKey)
        }
      }
      question.medias = medias
      question.mediaKeys = mediaKeys
      // 修改只能选择一种图片
      if (medias.length < 1) {
        this.uploadHavePlus = true
        this.uploadHaveNoPlus = false
      }
    },
    // 查看大图的操作
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 上传图片之前的判断
    beforeAvatarUpload (file) {
      // 判断图片的类型
      let supportImgFormat = ['image/jpg','image/jpeg','image/png','image/gif','image/JPG','image/JPEG','image/PNG','image/GIF']
      // 判断图片的大小
      let isSupportImgFormat = supportImgFormat.indexOf(file.type) > -1
      let isLt10M = file.size / 1024 / 1024 < 10
      if (!isSupportImgFormat) {
        this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'))
      }
      if (!isLt10M) {
        this.$message.error(this.$t('loc.dllImageUploadTips'))
      }
      return isSupportImgFormat && isLt10M
    }
  }
}
</script>

<style scoped>
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}
.cancelBtn{
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}
.saveBtn{
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}
</style>
<style>
.add-new-vocabulary-dialog {
  margin-bottom: 0 !important;
}
.add-new-vocabulary-dialog .el-dialog__footer {
  border-top: 0px solid #EEEEEE;
  padding: 20px 24px 24px;
  color:#323338;
}
.add-new-vocabulary-dialog .el-dialog__header {
  padding: 24px 24px 15px;
  border-bottom: 0px solid #EEEEEE;
  color:#323338;
}
.add-new-vocabulary-dialog .el-dialog__header .el-dialog__title {
  font-size: 20px !important;
}
.add-new-vocabulary-dialog .el-dialog__header .el-dialog__headerbtn {
  top: 24px;
}
.add-new-vocabulary-dialog .el-dialog__body {
  padding: 0 24px;
  color:#323338;
}

.add-resource-upload {
  width: 80px;
}
.add-resource-upload .el-upload-list__item-thumbnail {
  width: 80px;
  height: 80px;
}
/*.add-resource-upload  .el-upload-list__item-thumbnail .el-image__inner{*/
/*  width: auto !important;*/
/*}*/
.add-resource-upload  .el-upload-list__item{
  height: 80px;
  width: 80px;
  /*去掉加载图片的动画*/
  transition: none !important;
  display: inline-flex;
  margin: 0 !important;
}

.add-resource-upload-plus  .el-upload--picture-card{
  height: 80px;
  width: 80px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.add-resource-upload-no-plus  .el-upload--picture-card{
  height: 80px;
  width: 80px;
  display: none;
}
</style>
