<template>
  <div>
    <!-- 分享dll的弹框 -->
    <el-dialog :title="$t('loc.shareDll')" width="712px"   custom-class="dll-share-dialog" @close="closeShareDialog"  :visible.sync="shareDLLVisible" :lock-scroll="false" top="4vh" :close-on-click-modal="false">
      <div v-loading="languageLoading" class="display-flex flex-direction-col h-full">
      <!-- 这里设置编辑班级的组件 -->
        <div class="add-margin-b-10">
          <div class="font-weight-semibold add-margin-b-6">
            {{$t('loc.selectClassTitle')}}
          </div>
          <!--  选择班级的树形组件 -->
          <select-group-by-tree ref="selectGroupByTree" :current-group-id="selectGroupId" :current-center-id="selectCenterId" @changeGroupId="changeGroupId"></select-group-by-tree>
        </div>
        <!-- 选择 DLL 分享给父母的操作 -->
        <div class="add-margin-tb-10 font-weight-semibold">
          {{$t('loc.shareDllSelectTip')}}
        </div>
        <!-- 选择是否分享给 DLL 父母 -->
        <el-radio-group v-model="shareDLLParent">
          <el-radio label="parent">{{$t('loc.shareWithParents')}}</el-radio>
          <el-radio label="dll_parent" @click.native="clickDllChildren" :disabled="!haveDllChildren">{{$t('loc.shareOnlyDllParent')}}</el-radio>
        </el-radio-group>
        <div class="divide-line-gray add-margin-tb-6"></div>
        <div class="add-margin-tb-10 font-weight-semibold" >
          {{!isFromDll ? $t('loc.selectedDllVocabularyNum',{count:dllVocabularyList.length}) : $t('loc.selectedDllVocabularyNoNum')}}
        </div>
        <el-row :gutter="10" class="flex-1 height-0">
          <!-- 左边的 DLL 图片信息 -->
          <el-col class="h-full" v-if="!(isFromDll && currentSelectDllInfo.imgUrl.length === 0)"  :span="isFromDll ? 4 : 7">
            <!-- 展示来自于 DLL 的分享 -->
            <img v-if="isFromDll" style="object-fit: cover; height: 100px;width: 100px"  :src="currentSelectDllInfo.imgUrl">
            <!-- 展示来自于 DLL词汇库 的分享 -->
            <div v-else class="h-full scrollbar2">
              <div v-for="(resource,index) in dllVocabularyList" :key="resource.resourceId"
              :class="{'list-item-active':resource.resourceId === currentSelectResourceId,'list-hover-bg-color':index === hoverIndex}"
                   class="display-flex align-items height-50"
                   @click="clickShareResource(resource)"
                   @mouseover = "hoverIndex = index"
                   @mouseleave = "hoverIndex = -1">
                <div :class="[{'background-primary':resource.resourceId === currentSelectResourceId},'h-full',' width-3','border-radius-3']"></div>
                <img class="add-margin-l-6 add-margin-r-10"  style="object-fit: cover; height: 40px;width: 40px"  :src="resource.imgUrl">
                <div :title="resource.content"  class="flex-1 overflow-ellipsis-two l-h-1x" :class="[{'text-bolder':resource.resourceId === currentSelectResourceId}]">{{resource.content}}</div>
              </div>
            </div>
          </el-col>
          <!-- 展示具体的 DLL 的信息 -->
          <el-col :span="isFromDll ? (currentSelectDllInfo.imgUrl.length > 0 ? 20 : 24) : 17" class="h-full" style="padding-right: 0 !important;">
            <div  class="h-full display-flex flex-direction-col scrollbar2 add-padding-r-6 new-font-color">
              <!-- DLL 名称 -->
              <span  class="font-size-18 add-margin-b-10">
                {{currentSelectDllInfo.title}}
              </span>
              <!-- DLL 内容播放 -->
              <div class="display-flex justify-content-between">
                <span class="color-dll-title">{{$t('loc.dllEnglishTitle')}}</span>
                <dll-speaker  :content="currentSelectDllInfo.content" :lang-code="defaultEnglishCode"/>
              </div>
              <!-- DLL 内容 -->
              <div class="add-margin-tb-6">{{currentSelectDllInfo.content}}</div>
              <!-- 展示选择的语言和翻译按钮 -->
              <div class="display-flex align-items">
                <el-input
                  suffix-icon="el-icon-arrow-down"
                  readonly
                  :placeholder="$t('loc.pleaseSelectLanguage')"
                  @click.native="showSelectLanguageDialog"
                  class="flex-1 select-language-input lg-pointer"
                  v-model="selectedLanguageNum">
                </el-input>
                <el-button  type="primary" class="translateBtn add-margin-l-8"
                            :loading="translateLoading"
                            :disabled="selectedLanguageNum.trim().length === 0"
                            @click="clickTranslate">{{ $t('loc.translate') }}
                </el-button>
              </div>
              <!-- 展示翻译的内容 -->
              <div v-for="(item) in currentSelectDllInfo.translationList" :key="item.langCode"
                   class="translation-div add-margin-t-10">
                <dll-show-translate ref="langCode" :item="item"></dll-show-translate>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button @click="closeShareDialog" plain="" class="cancelBtn">{{$t('loc.cancel')}}</el-button>
        <el-button  type="primary" @click="clickAddNewSubject" class="saveBtn" :loading="submitLoading">{{$t('loc.confirmAndShare')}}</el-button>
      </span>
    </el-dialog>
    <select-language-dialog ref="selectLanguageDialog" @func="getSelectedLanguage"></select-language-dialog>
  </div>
</template>

<script>
import SelectGroupByTree from './SelectGroupByTree'
import SelectLanguageDialog from '../SelectLanguageDialog'
import DllSpeaker from '../../modules/lesson2/lessonLibrary/editor/dll/DllSpeaker'
import tools from '../../../utils/tools'
import DllShowTranslate from './DLLShowTranslate'
export default {
  name: 'DllShareDialog',
  components: { DllShowTranslate, DllSpeaker, SelectLanguageDialog, SelectGroupByTree },
  data () {
    return {
      submitLoading: false, // 提交的进度条
      shareDLLVisible: false, // 分享弹框是否展示
      selectGroupId: '', // 选中的班级id
      selectCenterId: '', // 选中的学校id
      shareDLLParent: 'parent', // 有parent和dll_parent两种类型
      isFromDll: false, // 判断是否自于dll的内容还是dll模板资源
      dllVocabularyList: [], // 分享的dll模板词汇列表
      currentSelectResourceId: '', // 当前选择的资源id
      hoverIndex: -1, // 鼠标滑动的位置
      currentSelectDllInfo: {}, // 当前选中的资源或者dll信息
      languageLoading: false, // 加载选择的语言和翻译的内容
      selectedLanguageNum: '', // 展示选择的语言数量
      haveSelectedLanguageCodes: '', // 已经选择的语言code值
      responseFinishNum: 0, // 记录批量请求翻译的完成请求数量
      defaultEnglishCode: 'en', // 默认的语言类型为英语
      translateLoading: false, // 翻译的进度条
      haveDllChildren: true, // 是否有 DLL 学生
      isFromPreview: false // 是否来自于预览
    }
  },
  methods: {
    // 点击分享按钮
    clickAddNewSubject () {
      this.submitLoading = true
      let requestArray = []
      if (this.isFromDll) {
        this.dllVocabularyList = []
        this.dllVocabularyList.push(this.currentSelectDllInfo)
      }
      for (let i = 0; i < this.dllVocabularyList.length; i++) {
        let tempDllInfo = this.dllVocabularyList[i]
        let langContentModels = []
        // 循环判断翻译的内容是否有值
        for (let j = 0; j < tempDllInfo.translationList.length; j++) {
          let tempLangContentModels = {}
          let item = tempDllInfo.translationList[j]
          tempLangContentModels.langCode = item.langCode
          // 判断翻译的内容是否为空
          if (item.content && item.content.trim().length > 0) {
            tempLangContentModels.content = item.content
          } else {
            // 如果翻译的内容为空，就展示当前翻译的位置
            if (this.isFromDll) {
              this.$refs.langCode[j].$el.scrollIntoView({ block: 'center' })
            } else {
              this.currentSelectDllInfo = this.dllVocabularyList[i]
              this.currentSelectResourceId = this.dllVocabularyList[i].resourceId
              this.$nextTick(() => {
                this.$refs.langCode[j].$el.scrollIntoView({ block: 'center' })
              })
            }
            this.submitLoading = false
            this.$message.error(this.$t('loc.noPlayContent'))
            return
          }
          langContentModels.push(tempLangContentModels)
        }
        // 判断是否选择了语言
        if (langContentModels.length === 0) {
          this.$message.error(this.$t('loc.pleaseSelectLanguage'))
          this.submitLoading = false
          return
        }
        // 定义传递给后台的字段
        let requestObj = {}
        requestObj.content = tempDllInfo.content
        requestObj.groupId = this.selectGroupId
        requestObj.publishDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss.000')
        requestObj.remindParent = true
        requestObj.shareParent = true
        requestObj.title = tempDllInfo.title
        requestObj.lang = 'English'
        requestObj.langContentModels = langContentModels
        if (tools.isNotEmpty(tempDllInfo.imgId)) {
          requestObj.mediaIds = [tempDllInfo.imgId]
        } else {
          requestObj.mediaIds = []
        }
        requestObj.onlyShareDLLChild = this.shareDLLParent !== 'parent'
        requestObj.sourceType = 'DLL_LIBRARY'
        requestArray.push(requestObj)
      }
        this.$axios
          .post($api.urls().batchCreateDll,{ dllLanguageRequests: requestArray })
          .then(res => {
            if (res.success) {
              this.submitLoading = false
              this.shareDLLVisible = false
              this.$message.success(this.$t('loc.dllShareSuccess'))
              // 分享成功，关闭弹框，刷新界面
              this.$emit('shareSuccess',this.isFromDll)
            }
          })
          .catch(error => {
            this.submitLoading = false
            this.$message.error(error.response.data.error_message)
          })
    },
    // 打开分享的弹框
    showShareDialog (dllShareString,isFromDll,isFromPreview) {
      // 初始化数据信息
      this.shareDLLVisible = true
      this.isFromDll = isFromDll
      this.isFromPreview = isFromPreview
      this.responseFinishNum = 0
      this.submitLoading = false
      this.hoverIndex = -1
      this.shareDLLParent = 'parent'
      this.hoverIndex = -1
      this.translateLoading = false
      this.selectGroupId = sessionStorage.getItem('dllGroupId').toUpperCase()
      this.selectCenterId = sessionStorage.getItem('dllCenterId').toUpperCase()
      // 每次打开分享弹框默认展示当前班级
      if (this.$refs.selectGroupByTree) {
        if (this.$refs.selectGroupByTree.$data.centerGroupData.length > 0) {
          this.$nextTick(() => {
            this.$refs.selectGroupByTree.setCurrentGroup()
          })
        } else {
          // 如果班级信息为空，重新请求
          this.$nextTick(() => {
            this.$refs.selectGroupByTree.getEngagementList()
          })
        }
      }
      let tempArray = JSON.parse(dllShareString)
      // 如果来自于预览界面，就使用预览界面的翻译内容
      if (!this.isFromPreview) {
        tempArray = tempArray.map(item => {
          item.translationList = []
          return item
        })
      }
      if (tempArray && tempArray.length > 0) {
        if (this.isFromDll) {
          this.dllVocabularyList = []
          this.currentSelectDllInfo = tempArray[0]
        } else {
          this.dllVocabularyList = tempArray
          this.currentSelectDllInfo = this.dllVocabularyList[0]
          this.currentSelectResourceId = this.currentSelectDllInfo.resourceId
        }
        // 获取当前班级的语言数
        this.getHaveSelectedLanguage(false)
        // 获取当前班级是否有 DLL 小孩
        this.getIsHaveDLLChild()
      }
    },
    // 关闭分享弹框
    closeShareDialog () {
      // 如果来自于预览界面，关闭分享界面展示预览界面的删除按钮
      if (this.isFromPreview) {
        this.$emit('closeShareDialog')
      }
      this.shareDLLVisible = false
    },
    // 切换选择的资源
    clickShareResource (resourceInfo) {
      if (this.currentSelectResourceId !== resourceInfo.resourceId) {
        this.currentSelectResourceId = resourceInfo.resourceId
        this.currentSelectDllInfo = resourceInfo
      }
    },
    // 切换班级的操作
    changeGroupId (groupId) {
      this.selectGroupId = groupId
      this.shareDLLParent = 'parent'
      this.getHaveSelectedLanguage(true)
      this.getIsHaveDLLChild()
    },
    // 处理翻译的操作
    dealTranslate (dllItem,isClick) {
      if (isClick) {
        this.translateLoading = true
      }
      this.$axios.post($api.urls().homeworkTranslate, {
        content: dllItem.content,
        format: 'text', // 原文文本格式 text/html
        langCodes: this.haveSelectedLanguageCodes.split(',')
      })
        .then(data => {
          if (data && data.length > 0) {
            // 如果通过分享 dll 和 单词点击翻译的情况
            if (this.isFromDll || isClick) {
              this.currentSelectDllInfo.translationList = data
            } else {
              let tempOriginalContent = data[0].originalContent
              for (let i = 0; i < this.dllVocabularyList.length; i++) {
                if (this.dllVocabularyList[i].content === tempOriginalContent) {
                  this.dllVocabularyList[i].translationList = data
                }
              }
            }
          }
          this.setLoadingHide(isClick)
        }).catch(error => {
        this.$message.error(error.response.data.error_message)
        this.setLoadingHide(isClick)
      })
    },
    // 获取已设置的语言
    getHaveSelectedLanguage (groupChange) {
      this.languageLoading = true
      this.responseFinishNum = 0
      this.$axios
        .get($api.urls().getGroupLanguageList + '?groupId=' + this.selectGroupId)
        .then(res => {
          let haveSelectedLanguageList = res.selectList
          if (haveSelectedLanguageList && haveSelectedLanguageList.length > 0) {
            // 表示该班级已经设置过语言
            this.selectedLanguageNum = this.$t('loc.selectedLanguageNum', { num: haveSelectedLanguageList.length })
            let translationCodes = []
            haveSelectedLanguageList.forEach(languageItem => {
              translationCodes.push(languageItem.code)
            })
            this.haveSelectedLanguageCodes = translationCodes.toString()
            // 批量翻译内容,如果从预览进来，不需要再翻译
            if (this.isFromPreview && !groupChange) {
              this.languageLoading = false
            } else {
              this.batchDealTranslate()
            }
          } else {
            // 如果该班级没有设置语言，置空界面数据
            this.selectedLanguageNum = ''
            this.haveSelectedLanguageCodes = ''
            this.languageLoading = false
            if (this.isFromDll) {
              this.currentSelectDllInfo.translationList = []
            } else {
              for (let i = 0; i < this.dllVocabularyList.length; i++) {
                this.dllVocabularyList[i].translationList = []
              }
              this.currentSelectDllInfo.translationList = []
            }
          }
        })
        .catch(error => {
          this.languageLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 批量处理翻译的操作
    batchDealTranslate () {
      // 如果是来自于dll分享，只需要分享单个就行
      if (this.isFromDll) {
        this.dealTranslate(this.currentSelectDllInfo,false)
      } else {
        for (let i = 0; i < this.dllVocabularyList.length; i++) {
          this.dealTranslate(this.dllVocabularyList[i],false)
        }
      }
    },
    // 关闭加载框
    setLoadingHide (isClick) {
      if (isClick) {
        this.translateLoading = false
      } else {
        if (this.isFromDll) {
          this.languageLoading = false
        } else {
          this.responseFinishNum++
          if (this.responseFinishNum === this.dllVocabularyList.length) {
            // 表示所有的内容已经翻译完成
            this.languageLoading = false
          }
        }
      }
    },
    // 手动点击翻译按钮
    clickTranslate () {
      this.dealTranslate(this.currentSelectDllInfo,true)
    },
    // 展示选中语言的弹框
    showSelectLanguageDialog () {
      this.$refs.selectLanguageDialog.show(this.selectGroupId)
    },
    // 成功选择语言后的回调
    getSelectedLanguage (val) {
      this.selectedLanguageNum = this.$t('loc.selectedLanguageNum', { num: val.split(',').length })
      this.haveSelectedLanguageCodes = val
      // 选择语言后自动更新翻译内容
      this.batchDealTranslate()
      // 如果分享界面从预览进入，切换语言的话也需要更新预览界面的语言
      if (this.isFromPreview) {
        this.$emit('changeLanguage',val)
      }
    },
    // 判断该班级是否有 DLL 小孩
    getIsHaveDLLChild () {
      this.$axios
        .get($api.urls().getHaveDLLChild + '?groupId=' + this.selectGroupId)
        .then(res => {
          this.haveDllChildren = res.haveDLLChild
        })
        .catch(error => {
          this.$message.error(error.response.data.error_message)
        })
    },
    // 点击 分享给DLL 的操作
    clickDllChildren () {
      if (!this.haveDllChildren) {
        this.$message.error(this.$t('loc.noDLLChildren'))
      }
    }
  }
}
</script>

<style scoped>
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}
.cancelBtn{
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}
.saveBtn{
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}
.list-item-active {
  color: #10B3B7;
  background-color: #DEF3F2;
  font-weight: bold;
}
.translation-div {
  border: #eeeeee solid 1px;
  border-radius: 4px;
}

</style>
<style>
.dll-share-dialog {
  margin-bottom: 0 !important;
}
.dll-share-dialog .el-dialog__footer {
  border-top: 0px solid #EEEEEE;
  padding: 10px 20px 20px;
  color:#323338;
}
.dll-share-dialog .el-dialog__header {
  padding: 20px 20px 15px;
  border-bottom: 0px solid #EEEEEE;
  color:#323338;
}
.dll-share-dialog .el-dialog__header .el-dialog__title {
  font-size: 20px !important;
}
.dll-share-dialog .el-dialog__header .el-dialog__headerbtn {
  top: 20px;
}
.dll-share-dialog .el-dialog__body {
  padding: 0 20px !important;
  color:#323338;
  overflow-y: hidden;
  height: 450px !important;
  font-size: 16px;
}
.select-language-input .el-input__inner {
  color: #10B3B7;
  font-size: 16px;
  cursor: pointer;
}
</style>
