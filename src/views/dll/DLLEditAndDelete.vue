<template>
<div class="w-full" @click.stop="">
  <!-- DLL 发布的时间以及编辑和删除操作-->
  <div class="w-full flex-row-between add-margin-t-16 add-margin-b-16 white-background" >
    <el-dropdown v-if="dllInfo.editRecordModels && dllInfo.editRecordModels.length > 1">
      <span class="el-dropdown-link color-dll-title">
        {{ (dllInfo.editRecordModels && dllInfo.editRecordModels.length > 0) ? dllInfo.editRecordModels[0].dateTimeInfo : ''}}<i  class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <el-dropdown-menu  slot="dropdown">
        <el-dropdown-item v-for="(item,index) in dllInfo.editRecordModels" :key="index">
          {{item.dateTimeInfo}}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <span v-else class="el-dropdown-link color-dll-title">{{ (dllInfo.editRecordModels && dllInfo.editRecordModels.length > 0) ?dllInfo.editRecordModels[0].dateTimeInfo : ''}}</span>
    <div class="display-inline-block">
      <span v-if="isCanComment" class="dll-publish-time lg-pointer" @click="goDLLDetails(dllInfo)"><img class="add-margin-l-2 add-margin-r-2" src="@/assets/img/dll/comment.svg">{{feedbackNum}}</span>
      <span v-if="isCanComment" class="add-margin-lr-10">|</span>
      <span v-if="isCanEdit" class="dll-publish-time add-margin-l-6 lg-pointer" @click="editDll(dllInfo)"><img class="add-margin-l-2 add-margin-r-2" src="@/assets/img/dll/edit.svg"></span>
      <span v-if="isCanDelete" class="dll-delete-icon add-margin-l-6 lg-pointer" @click="deleteDll(dllInfo)"><img class="add-margin-l-2 add-margin-r-2" src="@/assets/img/dll/delete.svg"></span>
    </div>
  </div>
  <!-- 删除提示对话框 -->
  <el-dialog :title="$t('loc.confirmation')" custom-class="delete-dialog" :visible.sync="deleteDialogVisible" :lock-scroll="false" width="35%" :close-on-click-modal="false" :close-on-press-escape="false" >
    <span class="font-size-16">{{$t('loc.deleteDLLTip')}}</span>
    <span slot="footer" class="dialog-footer">
        <el-button class="lg-plain-btn cancelBtn" plain @click="deleteDialogVisible = false">{{$t('loc.cancel')}}</el-button>
        <el-button type="danger" class="saveBtn" @click="deleteFormConfirm" :loading="btnDelLoading">{{$t('loc.pConfirm')}}</el-button>
      </span>
  </el-dialog>
</div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'DLLEditAndDelete',
  data () {
    return {
      deleteDialogVisible: false, // 删除弹框是否展示
      btnDelLoading: false, // 删除的loading
      deleteDllId: '', // 要删除的homeworkid,
      createDateAndTime: '', // 创建时间
      feedbackNum: 0, // feedback和相应的数字
      roles: {
        'collaborator': 'Teacher',
        'site_admin': 'Site Admin',
        'agency_admin': 'Agency Admin',
        'agency_owner': 'Agency Owner',
        'family_service': 'Family Service',
        'teaching_assistant': 'Teaching Assistant',
        'parent': 'Family Member'
      }
    }
  },
  methods: {
    // 删除 DLL
    deleteDll (item) {
      this.deleteDllId = item.id
      this.deleteDialogVisible = true
    },
    // 确认删除 DLL
    deleteFormConfirm () {
      // 停止播放语音
      this.$parent.clearAudioInfo()
      this.$parent.setVoicePlayingToStop()
      this.btnDelLoading = true
      this.$axios
        .get($api.urls().deleteHomework + '?homeworkId=' + this.deleteDllId)
        .then(res => {
          this.btnDelLoading = false
          if (res.success) {
            this.$message.success(this.$t('loc.deleteDLLSuccess'))
            this.$parent.deleteDll(this.deleteDllId)
            this.deleteDialogVisible = false
          }
        })
        .catch(error => {
          this.btnDelLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 编辑 DLL 的内容
    editDll (item) {
      // 停止播放语音
      this.$parent.clearAudioInfo()
      this.$parent.setVoicePlayingToStop()
      this.$parent.openEditDLLDialog(item)
    },
    goDLLDetails (item) {
      this.$analytics.sendEvent('web_dll_click_parent_comments')
      // 停止播放语音
      this.$parent.clearAudioInfo()
      this.$parent.setVoicePlayingToStop()
      // 去 DLL 详情查看评论数据
      this.$router.push({
        name: this.skipType === 'DLL' ? 'dllDetail' : 'dllDetails',
        params: {
          homeworkId: item.id
        }
      })
    },
    // 设置角色名
    getUserRoleName (sysRoleName) {
      if (sysRoleName) {
        sysRoleName = sysRoleName.toLowerCase()
        if (this.roles && this.roles.hasOwnProperty(sysRoleName)) {
          return this.roles[sysRoleName]
        }
      }
      return ''
    },
    getDLLListFromAdd () {

    },
    formatUserName (name) {
      if (name.indexOf('Ms.') === 0) {
        let separator = 'Ms.'
        if (name.replace(separator, '').charAt(0) !== ' ') {
          name = separator + ' ' + name.replace(separator, '')
        }
      } else if (name.indexOf('Mr.') === 0) {
        let separator = 'Mr.'
        if (name.replace(separator, '').charAt(0) !== ' ') {
          name = separator + ' ' + name.replace(separator, '')
        }
      }
      return name
    }
  },
  computed: {
    ...mapState({
      skipType: state => state.common.skipType // 获取进入 DLL 和睡眠检查的方式
    })
  },
  props: {
    dllInfo: {
      type: Object
    },
    isCanEdit: {
      type: Boolean,
      default: false
    },
    isCanDelete: {
      type: Boolean,
      default: false
    },
    isCanComment: {
      type: Boolean,
      default: false
    },
    groupId: {
      type: String,
      default: ''
    },
    formatType: {
      type: String
    }
  },
  watch: {
    dllInfo: {
      handler (newValue) {
        if (newValue) {
          // 设置评价的数量
          if (this.isCanComment) {
            if (newValue.replyNum === 0) {
              // this.feedbackNum = this.$t('loc.singleComment')
               this.feedbackNum = 0
            } else {
              this.feedbackNum = this.dllInfo.replyNum
            }
            // else if (newValue.replyNum === 1) {
            //   this.feedbackNum = this.$t('loc.oneComment')
            // } else if (newValue.replyNum > 1) {
            //   this.feedbackNum = this.$t('loc.moreComment',{ num: this.dllInfo.replyNum })
            // }
          }
          // 设置编辑的时间信息
          if (newValue.editRecordModels && newValue.editRecordModels.length > 0) {
            newValue.editRecordModels = newValue.editRecordModels.map(editRecordItem => {
              let createName = this.formatUserName(editRecordItem.name) + ' (' + this.getUserRoleName(editRecordItem.role) + ')'
              let dateAndTime = this.$moment(editRecordItem.editTime).format(this.formatType).split(' ')
              let createDate = dateAndTime[0]
              let createTime = dateAndTime[1] + (dateAndTime.length === 3 ? ' ' + dateAndTime[2] : '')
              if (editRecordItem.editType === 'CREATE') {
                editRecordItem.dateTimeInfo = this.$t('loc.createInfo',{ people: createName, date: createDate,time: createTime })
              } else {
                editRecordItem.dateTimeInfo = this.$t('loc.lastUpdatedInfoByUser',{ people: createName, date: createDate,time: createTime })
              }
              return editRecordItem
            })
          } else if (newValue.actions && newValue.actions.length > 0) {
            newValue.editRecordModels = newValue.actions.map(editRecordItem => {
              let createName = editRecordItem.byUser + ' (' + editRecordItem.role + ')'
              let dateAndTime = this.$moment(editRecordItem.utcDate).format(this.formatType).split(' ')
              let createDate = dateAndTime[0]
              let createTime = dateAndTime[1] + (dateAndTime.length === 3 ? ' ' + dateAndTime[2] : '')
              if (editRecordItem.action === 'CREATE') {
                editRecordItem.dateTimeInfo = this.$t('loc.createInfo',{ people: createName, date: createDate,time: createTime })
              } else {
                editRecordItem.dateTimeInfo = this.$t('loc.lastUpdatedInfoByUser',{ people: createName, date: createDate,time: createTime })
              }
              return editRecordItem
            })
          }
        }
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
.flex-row-between{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}
.dll-publish-time {
  color: #676879;
  font-size: 14px;
}
.dll-delete-icon {
  color: #676879;
  font-size: 14px;
}
.dll-delete-icon:hover{
  color: #F56C6C;
  font-size: 14px;
}
.add-margin-l-2 {
  margin-left: 2px;
}
.add-margin-r-2 {
  margin-right: 2px;
}
.cancelBtn{
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}
.saveBtn{
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}
</style>
<style>
.delete-dialog .el-dialog__body {
  padding: 10px 20px !important;
}
</style>
