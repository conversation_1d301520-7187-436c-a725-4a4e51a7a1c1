<template>
<!-- DLL 作用教师演示的功能弹框 -->
  <div class="dll_demo_dialog">
    <el-dialog custom-class="dll-homework-demo-dialog" :append-to-body="true" :show-close="false" @close="beforeCloseDemo" top="10vh" :visible.sync="dLLHomeworkDemoDialogVisible" width="80%" :close-on-click-modal="false" :lock-scroll="false">
      <span slot="title" class="flex-space-between w-full">
        <span class="font-size-24 font-weight-semibold dll-text-title-color">{{lessonTitle}}<span style="font-weight: 400;">{{showPosition}}</span></span>
        <span v-if="dllContentList && dllContentList.length > 0" class="font-size-20 el-icon-question-color">{{dllContentList.length > 0 ? dllContentList[carouseIndex].publishDateStr : ''}}</span>
      </span>
      <el-carousel  class="dll-demo-carousel h-full" v-if="dllContentList && dllContentList.length > 0" :initial-index="carouseIndex"  indicator-position="none" arrow="never" :autoplay="false" ref="showContent" @change="carouselChange">
        <el-carousel-item  v-for="item in dllContentList" :key="item.id">
          <div class="display-flex white-background h-full">
            <!-- 展示图片 -->
            <div class="add-padding-r-12 width-60-percent h-full add-padding-b-12" v-if="hasMediaUrl(item)">
              <el-image class="show-equal-rate-img border-radius-4" :src="mediaUrl(item)" fit="contain"></el-image>
            </div>
            <!-- 展示语音内容 -->
            <div class="display-flex flex-direction-col lg-scrollbar-hidden lg-padding-left-12" :style="hasMediaUrl(item) ? {'width':'40%'} : {'width':'100%'}">
            <!-- English内容 -->
              <div class="display-flex flex-direction-col">
                <div class="flex-row-between">
                  <span class="font-size-18 lg-color-text-primary">
                   {{$t('loc.dllEnglishTitle')}}
                  </span>
                  <span v-show="!item.voiceLoading && !item.isPlaying" class="dll-content-play lg-pointer inline-flex-row-vertical-center"  @click="getLanguageAudioUrl(item)" ><span class="show-play-icon"></span>{{$t('loc.play')}}</span>
                  <i v-show="item.voiceLoading" class="el-icon-loading"></i>
                  <!-- 展示正在播放语音的动画 -->
                  <div v-if="!item.voiceLoading && item.isPlaying" class="lg-pointer inline-flex-row-vertical-center" @click="getLanguageAudioUrl(item)">
                    <span class="voice-playing lg-pointer"></span>
                    <span class="dll-content-play add-margin-l-3">{{$t('loc.play')}}</span>
                  </div>
                </div>
                <span class="font-size-20  add-margin-tb-6 pre-wrap font-weight-600" style="color: var(--color-text-primary);">{{item.content}}</span>
                <span class="font-size-16 color-676879">{{item.description}}</span>
                <span class="display-inline-block divide-vertical-2"></span>
              </div>
              <span class="lg-margin-bottom-12 font-color-primary font-size-18" v-if="item.languages && item.languages.length > 0">{{$t('loc.selectedLanguageNum',{num: languagesNum(item.languages)})}}</span>
              <div class="display-flex flex-direction-col lg-padding-16 bg-color-F5F6F8" v-for="(languageItem,index) in item.contentModels" :key="languageItem.id" >
                <div class="flex-row-between">
                    <span class="title-font-18-regular">
                      {{languageItem.lang}} <span class="font-normal">({{languageItem.lang_en}})</span>
                    </span>
                  <span v-show="!languageItem.voiceLoading && !languageItem.isPlaying" class="dll-content-play lg-pointer inline-flex-row-vertical-center"  @click="getLanguageAudioUrl(languageItem)" ><span class="show-play-icon"></span>{{$t('loc.play')}}</span>
                  <i v-show="languageItem.voiceLoading" class="el-icon-loading"></i>
                  <!-- 展示正在播放语音的动画 -->
                  <div v-if="!languageItem.voiceLoading && languageItem.isPlaying" class="lg-pointer inline-flex-row-vertical-center" @click="getLanguageAudioUrl(languageItem)">
                    <span class="voice-playing lg-pointer"></span>
                    <span class="dll-content-play add-margin-l-3">{{$t('loc.play')}}</span>
                  </div>
                </div>
                <span class="font-size-20 dll-text-title-alt-color add-margin-tb-6 font-weight-600">{{languageItem.content}}</span>
                <span v-if="index !== item.contentModels.length - 1" class="divide-line-gray"></span>
            </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
      <!--  空布局  -->
      <div v-else class="flex-column-center height-400 w-full white-background"
           >
        <img src="@/assets/img/dll/no_record.png">
        <div  class="text-center add-margin-t-8 font-size-14 el-icon-question-color">{{$t('loc.plan36')}}</div>
      </div>
      <el-button  v-if="dLLHomeworkDemoDialogVisible" @click="closeDemoDialog" style="right:6.5%;top: 15%;z-index: 99999" class="el-icon-btn"><i class="el-icon-close"></i></el-button>
      <el-button  v-if="dLLHomeworkDemoDialogVisible" @click="nextContent" style="right:6.5%;top: 52.5%;z-index: 99999" class="el-icon-btn"><i class="el-icon-arrow-right"></i></el-button>
      <el-button  v-if="dLLHomeworkDemoDialogVisible" @click="prevContent" style="left:6.5%;top: 52.5%;z-index: 99999" class="el-icon-btn"><i class="el-icon-arrow-left"></i></el-button>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DLLHomeworkDemoDialog',
  data () {
    return {
      lessonTitle: '', // 课程名称
      dLLHomeworkDemoDialogVisible: false, // 判断演示模式弹框是否弹出
      dllContentList: [], // 演示的内容数组
      carouseIndex: 0, // 演示界面滑动的下标
      showPosition: '', // 演示内容的位置占总位置的值 1/11
      isPlayLanguage: false, // 语言的音频是否正在播放
      isGetVoiceLoading: false, // 是否界面有获取语音的加载框
      languageAudio: undefined // 播放语音控制器
    }
  },
  computed: {
    // 是否存在图片
    hasMediaUrl () {
      return function (item) {
        return item.mediaModels && item.mediaModels.length > 0 && (item.mediaModels[0].mediaUrl || item.mediaModels[0].url)
      }
    },
    mediaUrl () {
      return function (item) {
        if (item.mediaModels[0]) {
          if (item.mediaModels[0].mediaUrl) {
            return item.mediaModels[0].mediaUrl
          } else {
            return item.mediaModels[0].url
          }
        }
      }
    }
  },
  methods: {
    show (dllInfoArray,index,lessonTitle) {
      this.lessonTitle = lessonTitle
      this.dllContentList = dllInfoArray
      this.dLLHomeworkDemoDialogVisible = true
      this.carouseIndex = index
      this.showPosition = ' (' + (this.carouseIndex + 1) + '/' + (this.dllContentList.length) + ')'
    },
    // 语言数量
    languagesNum (languages) {
      // 如果 languages 是数字，则直接返回
      if ((typeof languages) === 'number') {
        return languages
        // 如果 languages 是数组，则返回数组长度
      } else if ((typeof languages) === 'object' && Array.isArray(languages)) {
        return languages.length
      } else {
        return 0
      }
    },
    carouselChange (newIndex) {
      this.carouseIndex = newIndex
      this.showPosition = ' (' + (this.carouseIndex + 1) + '/' + (this.dllContentList.length) + ')'
    },
    // 进入下一个
    nextContent () {
      this.$refs.showContent.next()
    },
    // 进入上一个
    prevContent () {
      this.$refs.showContent.prev()
    },
    // 获取第一个位置
    backToFirst (index) {
      if (this.$refs.showContent) {
        this.$refs.showContent.setActiveItem(index)
      }
    },
    getLanguageAudioUrl (item) {
      if (this.isGetVoiceLoading) {
        this.$message.info(this.$t('loc.loadingDataNow'))
        return
      }
      if (this.languageAudio) {
        this.languageAudio.pause()
        this.languageAudio = undefined
      }
      // 去除正在播放的按钮
      this.setVoicePlayingToStop()
      if (!item.langCode) {
        item.langCode = 'en'
      }
      // 如果该条语音已经播放过，使用缓存的链接播放，不用再请求接口
      if (item.voiceUrl && item.voiceUrl.length > 0) {
        this.languageAudio = new Audio(item.voiceUrl)
        this.languageAudio.play()
        this.playEnd()
        item.isPlaying = true
      } else {
        // 请求获取播放链接
        // 展示该条目的获取语音的加载框
        item.voiceLoading = true
        this.isGetVoiceLoading = true
        this.$axios
          .get($api.urls().getLanguageTextToSpeech + '?content=' + encodeURIComponent(item.content) + '&language=' + item.langCode)
          .then(res => {
            item.voiceLoading = false
            this.isGetVoiceLoading = false
            if (res.voiceUrl && res.voiceUrl.length > 0) {
              // 进行语音播报
              this.languageAudio = new Audio(res.voiceUrl)
              item.voiceUrl = res.voiceUrl
              this.languageAudio.play()
              this.playEnd()
              item.isPlaying = true
            }
          })
          .catch(error => {
            item.voiceLoading = false
            this.isGetVoiceLoading = false
            item.isPlaying = false
            this.$message.error(error.response.data.error_message)
          })
      }
    },
    // 语言语音播放结束的回调
    playEnd () {
      if (this.languageAudio) {
        this.languageAudio.addEventListener('ended', () => {
          this.setVoicePlayingToStop()
        })
      }
    },
    closeDemoDialog () {
      this.beforeCloseDemo()
      this.dLLHomeworkDemoDialogVisible = false
    },
    // 编辑弹框的操作
    beforeCloseDemo () {
      if (this.languageAudio) {
        this.languageAudio.pause()
        this.languageAudio = undefined
      }
      this.setVoicePlayingToStop()
      this.dllContentList = []
      this.carouseIndex = 0
    },
    // 设置把所有语言的正在播放按钮置空
    setVoicePlayingToStop () {
      this.dllContentList.forEach(item => {
        item.isPlaying = false
        item.contentModels && item.contentModels.forEach(contentModel => {
          contentModel.isPlaying = false
        })
      })
    }
  }
}
</script>

<style scoped>
.flex-row-between{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.dll-content-play {
  font-size: 16px;
  color: #10B3B7;
  text-align: center;
}

.dll_demo_dialog >>> .el-dialog__header{
  padding: 10px 24px;
  border-bottom: #eee solid 0px;
}
.dll_demo_dialog >>> .el-dialog__body{
  padding: 0px 24px;
  height: calc(73vh - 70px);
}
.dll_demo_dialog >>> .el-dialog__footer{
  display: none;
}
.el-icon-btn {
  border: none;
  outline: 0;
  padding: 0;
  margin: 0;
  height: 36px;
  width: 36px;
  cursor: pointer;
  -webkit-transition: .3s;
  transition: .3s;
  border-radius: 50%;
  background-color: #9CA4AB;
  color: #FFF;
  position: fixed;
  z-index: 9999;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  text-align: center;
  font-size: 18px;
}
.show-equal-rate-img {
  display: inline-flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}
.divide-vertical-2 {
  height: 2px;
  background: #F1F1F1;
  margin: 9px 0px;
}
.width-60-percent {
  width: 60%;
}
</style>
<style>
.show-equal-rate-img  .el-image__inner {
  max-width: 100%;
  max-height: 100%;
  width: 100% !important;
  height: initial !important;
}
.dll-demo-carousel {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.demo-img-carousel {
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.dll-demo-carousel .el-carousel__container {
  height: calc(73vh - 70px);
}
</style>
