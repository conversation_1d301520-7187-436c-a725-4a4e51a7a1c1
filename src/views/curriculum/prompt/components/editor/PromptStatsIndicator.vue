<template>
    <div class="display-flex m-t-sm">
        <!-- 模型信息 -->
        <div class="stats-card add-width-4 display-flex m-r-sm">
            <!-- 测试数量 -->
            <div class="stats-item b-r flex-auto">
                <div>Entries</div>
                <div class="font-bold">{{ testCount }}</div>
            </div>
            <!-- 模型 -->
            <div class="stats-item b-r flex-auto">
                <div>Model</div>
                <div class="font-bold">{{ promptInfo ? promptInfo.model : '--' }}</div>
            </div>
            <!-- 温度 -->
            <div class="stats-item">
                <div>Temperature</div>
                <div class="font-bold flex-auto">{{ promptInfo ? promptInfo.temperature : '--' }}</div>
            </div>
        </div>
        <!-- 使用信息 -->
        <div class="stats-card flex-auto display-flex">
            <!-- 平均通过率 -->
            <div class="stats-item b-r flex-auto" v-if="promptInfo && promptInfo.averagePassRuleRate">
                <div>Average Pass Rate</div>
                <div class="font-bold">{{ promptInfo && promptInfo.averagePassRuleRate ? promptInfo.averagePassRuleRate + '%' : '--' }}</div>
            </div>
            <!-- 平均分数 -->
            <div class="stats-item b-r flex-auto">
                <div>Average Score</div>
                <div class="font-bold">{{ promptInfo && promptInfo.averageScore ? promptInfo.averageScore : '--' }}</div>
            </div>
            <!-- 平均时长 -->
            <div class="stats-item b-r flex-auto">
                <div>Average Duration</div>
                <div class="font-bold">{{ promptInfo && promptInfo.averageDuration ? promptInfo.averageDuration + 's' : '--' }}</div>
            </div>
            <!-- 平均费用 -->
            <div class="stats-item flex-auto">
                <div>Average Cost              </div>
                <div class="font-bold">{{ promptInfo && promptInfo.averageCost ? '$' + promptInfo.averageCost : '--' }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        // Prompt 信息
        promptInfo: {
            type: Object,
            default: {},
        },

        // 数量
        testCount: {
            type: Number,
            default: '--',
        },
    },

    components: {
    },

    data() {
        return {
        }
    },

    watch: {
    },

    methods: {
    },
}
</script>

<style lang="less" scoped>
.stats-card {
    background-color: #F3FBFC;
    padding: 10px;
    border-radius: 12px;
    .stats-item {
        padding: 0 6px;
    }
}
</style>