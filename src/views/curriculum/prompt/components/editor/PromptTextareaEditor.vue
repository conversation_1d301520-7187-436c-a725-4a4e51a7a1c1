<template>
  <div class="prompt-text-editor-textarea" @click="handleClickEditorContainer">
    <div class="textarea-item-div">
      <div class="textarea-item p prompt-textarea lg-scrollbar"
           :style="promptTextareaStyle"
           ref="textareaItem"
           contenteditable="plaintext-only"
           @input="handleInput"
           @focus="handleFocus"
           @blur="handleBlur"
           @keydown="handleKeyDown"
           @compositionstart="onCompositionStart"
           @compositionend="onCompositionEnd"
           @click.stop>
      </div>
    </div>
  </div>
</template>

<script>
import emitter from 'element-ui/src/mixins/emitter'
import {v4 as uuidv4} from 'uuid'
import Vue from 'vue'
import PromptVariableTag from '@/views/curriculum/prompt/components/editor/PromptVariableTag.vue';
import {mapState} from "vuex";
import {equalsIgnoreCase} from "@/utils/common";
import tools from "@/utils/tools";

const promptVariableTag = Vue.extend(PromptVariableTag)

export default {
  name: 'PromptTextareaEditor',
  components: {PromptVariableTag},
  mixins: [emitter],
  props: [
    'simple', // 是否是快速添加课程
    'from', // 来源 用于响应事件的时候作区分
    'value',
    'promptTextareaStyle',
    'validateEvent',
    'fromTemplate',
    'notShowUrlCard'
  ],
  computed: {
    ...mapState({
      promptSourcesList: state => state.curriculum.promptSourcesList,
    }),
    editorInitialized: {
      get() {
        return this.$store.state.curriculum.editorInitialized
      },
      set(value) {
        this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', value)
      }
    },
    _validateEvent() {
      return this.validateEvent || this.validateEvent === undefined
    },
    formatSource() {
      return (source) => {
        // 去除 source 中的前后空格包括制表符和换行符,包括 \r\n \r \n
        let replace = source.replace(/(^\s*)|(\s*$)/g, '');
        // 如果存在多个 /r /n /r/n 替换为一个
        // 如果存在多个 <br> 则将多个转化为一个
        replace = replace.replace(/(<br>)+/g, '<br>')
        return replace
      }
    }
  },
  data() {
    return {
      editorContentLength: 0, //
      isFocus: false, // 编辑器是否聚焦
      inputLock: false, // input 锁
      currentPosition: -1, // 当前光标位置
      openVerify: false, // 编辑器是否验空 （校验空方式：值传递到父组件校验）
      isRevokeOrRecover: false, // 撤销、恢复使用标志位
      revoke: [], // ctrl + z 撤销缓存
      recover: [], // ctrl + y 恢复缓存
      generatedCards: [], // 生成的 card
      refreshCardLoading: false, // 刷新 card 加载状态
      timeoutID: null, // 定时器，用于刷新 card
    }
  },
  created() {
    // 监听 addSource 事件
    this.$bus.$on('addSources', ({fromTemplate, from, source}) => {
      if (from === this.from && fromTemplate === this.fromTemplate) {
        this.addSources(source)
      }
    })
  },
  mounted() {
    // 如果 value 是空的，那么清空编辑器内容
    this.initEmptyEditor(this.value)
    if (this.editorInitialized || !this.value) {
      // 由于 value 是空的，或者 editorInitialized 已经初始化，那么直接返回，并修改 editorInitialized 为 true
      this.editorInitialized = true
      return
    }
    this.$emit('textareaEditorMounted')
    // 初始化 prompt text 的编辑器
    this.initPromptTextEditor(this.value)
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (value) {
        // 如果 value 是空的，那么清空编辑器内容
        this.initEmptyEditor(value)
        if (this.editorInitialized || !value) {
          // 由于 value 是空的，或者 editorInitialized 已经初始化，那么直接返回，并修改 editorInitialized 为 true
          this.editorInitialized = true
          return
        }
        // 初始化 prompt text 的编辑器
        this.initPromptTextEditor(value)
      }
    },
    promptSourcesList: {
      deep: true,
      handler: function (value) {
        // 将所有的挂载的 card 重新挂载
        this.refreshCards(value)
      }
    },
    editorInitialized: {
      handler: function (value) {
        // 如果 value 是空的，那么清空编辑器内容
        this.initEmptyEditor(this.value)
        if (value || !this.value) {
          // 由于 value 是空的，或者 editorInitialized 已经初始化，那么直接返回，并修改 editorInitialized 为 true
          this.editorInitialized = true
          return
        }
        // 初始化 prompt text 的编辑器
        this.initPromptTextEditor(this.value)
      }
    }
  },
  methods: {
    initEmptyEditor(value) {
      // 如果 value 是空的，那么清空编辑器内容
      if (this.$el && (value === undefined || value === null || value === '')) {
        let nodeDom = this.$el && this.$el.querySelector('.textarea-item.p');
        this.clearValue()
        this.preprocessNodes(nodeDom)
      }
    },
    // 刷新 cards，主要是为了更新 sourceValue，这里多次调用会导致卡顿，所以需要设置一个定时器
    refreshCards(lastPromptSourcesList) {
      // 如果存在 timeoutID，那么清除掉
      if (this.timeoutID) {
        clearTimeout(this.timeoutID)
      }
      this.timeoutID = setTimeout(() => {
        this.refreshCardsHelper(lastPromptSourcesList)
      }, 1500)
    },
    // 刷新 card
    refreshCardsHelper(lastPromptSourcesList) {
      // 如果正在刷新中，则直接返回
      if (this.refreshCardLoading) {
        return
      }
      // 设置刷新状态
      this.refreshCardLoading = true
      // 克隆节点
      let nodeDom = this.$el && this.$el.querySelector('.textarea-item.p')
      // 获取所有的 card 节点
      let cardTemp = []
      // 去除 card 节点
      nodeDom.childNodes.forEach((item, index) => {
        if (item.id && item.id.indexOf('variable-') > -1) {
          // 获取变量的 source
          let attribute = item.getAttribute('data-source')
          let sourceName = item.getAttribute('source-name')
          // 定义 source
          const source = {
            sourceName: JSON.parse(sourceName)
          }
          // 从 lastPromptSourcesList 中获取 source
          let sourceTemp = lastPromptSourcesList.flatMap(item => item.sources)
            .find(item => equalsIgnoreCase(item.sourceName.trim(), source.sourceName.trim()))
          // 如果 source 为空，sourceValue 则为空
          if (attribute !== 'undefined' && attribute !== null && attribute !== '') {
            let sourceValue = JSON.parse(attribute).sourceValue
            // 判断 sourceValue 是否存在，并且没有更新
            if (sourceTemp && sourceTemp.sourceValue !== sourceValue) {
              source.sourceValue = sourceTemp.sourceValue
            } else {
              source.sourceValue = sourceValue
            }
          } else {
            if (sourceTemp) {
              source.sourceValue = sourceTemp.sourceValue
            } else {
              source.sourceValue = ''
            }
          }
          // 将 source 的 sourceValue 重新变为 json 字符串
          item.setAttribute('data-source', JSON.stringify(source))

          // 如果是变量节点，将其放入 cardTemp 中
          cardTemp.push(item)
        }
      })
      // 挂载 card
      cardTemp.forEach((item, index) => {
        this.generateCards(item)
      })
      // 挂载完成之后，设置刷新状态
      this.refreshCardLoading = false
    },
    // 初始化 prompt text 的编辑器
    initPromptTextEditor(value) {
      // 判断是否已经 mounted
      if (!this.$el) {
        return
      }
      // 解析 promptContent 的数据
      let promptContent = this.parsePromptContent(value)
      // 清空编辑器内容
      this.clearValue()
      // 构建编辑器内容
      if (this.$el) {
        // 设置编辑器内容
        this.$el.querySelector('.textarea-item.p').innerHTML = this.buildPromptDom(promptContent)
      }
      // 获取编辑器内容长度
      this.editorContentLength = this.$el && this.$el.querySelector('.textarea-item.p').innerText.trim().length
      this.$nextTick(() => {
        // 监测编辑器 dom 变动
        this.domObserver = new MutationObserver(() => {
          this.syncValue()
        })
        // 挂载监视器
        this.domObserver.observe(this.$el, {
          attributes: true,
          subtree: true,
          childList: true
        })
      })
      this.handleInput()
      this.editorInitialized = true
    },
    // 添加资源
    addSources(source) {
      // 获取当前光标位置
      let position
      // 判断是否已经 mounted
      if (!this.$el) {
        return
      }
      // 获取当前编辑器内容
      let element = this.$el.querySelector('.textarea-item.p');
      // 如果上一次记录的光标位置存在，那么使用上一次的光标位置
      if (this.currentPosition && this.currentPosition > -1) {
        position = this.currentPosition;
      } else {
        position = this.getPosition(element)
        // 更新当前光标位置
        this.currentPosition = position
      }
      // 将 node 添加到当前光标位置
      let nodeDom = element
      // 获取当前节点
      let {node: currentNode, offset} = this.getDeepestNode(nodeDom, position)
      // 如果找到的 position 在 nodeDom 里面，那么说明节点没有初始化，使用 nodeDom 添加一个 prompt-text 节点
      if (currentNode === nodeDom) {
        // 创建一个 span
        let spanNode = document.createElement('span')
        spanNode.appendChild(document.createTextNode(" "))
        // 设置 span 节点的 class
        spanNode.classList.add('prompt-text')
        // 将 span 节点挂载到 nodeDom 上
        nodeDom.appendChild(spanNode)
        // 修改 current Node
        currentNode = spanNode
        this.setEditorCursor(spanNode, false)
        // 再次尝试添加
        return this.addSources(source)
      }
      // 向当前光标添加一个 span
      let node = document.createElement('span')
      // 设置 node 的 class
      node.classList.add('prompt-text')
      // 设置 node id
      node.id = 'variable-' + uuidv4()
      // 设置 node 的 data-source 的属性为 source
      node.setAttribute('data-source', JSON.stringify(source))
      node.setAttribute('source-name', JSON.stringify(source.sourceName))
      // 设置 node 的内容
      node.innerHTML = this.formatSource(source.sourceName)
      let afterNode = this.insertOffset(nodeDom, node, currentNode, offset);
      // 设置光标位置
      if (afterNode) {
        this.setEditorCursor(afterNode, false)
      } else {
        this.setEditorCursor(node, true)
      }
      // 将当前编辑器内容放入撤销缓存
      this.revoke.push({content: nodeDom.innerHTML, range: this.getPosition(nodeDom)})
      this.revoke.length > 10 && this.revoke.shift()
      this.Recover = []
      this.syncValue()

      // 向当前光标挂载一个资源
      this.generateCards(node)
      // 输出当前光标的所在位置
      let selection = document.getSelection();
      let rangeAt = selection.getRangeAt(0);
      let container = rangeAt.commonAncestorContainer;
    },
    insertOffset(nodeDom, sourceNode, sourceCurrentNode, offset) {
      // 返回后一个 node
      let returnAfterNode = null
      const insertOffsetHelper = (node, currentNode, offset) => {
        if (currentNode.nodeType === Node.TEXT_NODE) {
          // 获取当前节点的文本内容
          const textContent = currentNode.textContent;
          const beforeText = textContent.slice(0, offset);
          let afterText = textContent.slice(offset);
          // 定义一个临时的 node 用作后续如果存在一个非空的 text 节点，那么可以将光标移动到这个节点的前面
          let tempEmptyNode
          // 如果 afterText Node 为空，那么设置为空字符串
          if (afterText === '') {
            afterText = ' '
          } else {
            tempEmptyNode = currentNode.parentNode.cloneNode();
            tempEmptyNode.appendChild(document.createTextNode(' '));
          }

          const beforeNode = document.createTextNode(beforeText);
          const afterNode = document.createTextNode(afterText);

          // 获取当前节点的父节点
          const parentNode = currentNode.parentNode;
          // 克隆一个新的父节点
          let newBeforeSpan = parentNode.cloneNode();
          newBeforeSpan.appendChild(beforeNode);
          newBeforeSpan.classList.add('before-variable')
          // 克隆一个新的父节点
          let newAfterSpan = parentNode.cloneNode();
          newAfterSpan.appendChild(afterNode);

          // 获取当前节点的父节点
          let sourceParent = sourceCurrentNode.parentNode;
          // 插入新的节点，如果原父节点是 prompt-text
          if (sourceParent.classList.contains('prompt-text')) {
            nodeDom.insertBefore(newBeforeSpan, sourceParent);
            nodeDom.insertBefore(node, sourceParent);
            // 如果临时节点存在就先放临时节点
            if (tempEmptyNode) {
              nodeDom.insertBefore(tempEmptyNode, sourceParent);
              // 记录最后一个 node
              returnAfterNode = tempEmptyNode
            } else {
              // 记录最后一个 node
              returnAfterNode = newAfterSpan
            }
            nodeDom.insertBefore(newAfterSpan, sourceParent);
            // 移除原来的文本节点
            // parentNode.removeChild(currentNode);
            nodeDom.removeChild(sourceParent);
          }
        } else if (currentNode.nodeType === Node.ELEMENT_NODE) {
          // 元素节点情况，通常是光标在元素之间
          const childNodes = Array.from(currentNode.childNodes);
          let totalLength = 0;
          let found = false;

          for (let i = 0; i < childNodes.length; i++) {
            const childNode = childNodes[i];
            const nodeLength = childNode.textContent.length;

            if (totalLength + nodeLength >= offset) {
              const innerOffset = offset - totalLength;
              insertOffsetHelper(node, childNode, innerOffset);
              found = true;
              break;
            }

            totalLength += nodeLength;
          }

          if (!found) {
            currentNode.appendChild(node);
          }
        }
      }
      insertOffsetHelper(sourceNode, sourceCurrentNode, offset);
      return returnAfterNode
    },
    /**
     * 解析内容
     */
    parseContents() {
      // 解析结果
      let result = ''
      // 遍历内容中元素列表
      let htmlEle = this.$el && this.$el.querySelector('.textarea-item.p')
      if (!htmlEle) {
        return ''
      }
      // 获取元素下有类名 prompt-text 的子元素列表
      let childEles = htmlEle.querySelectorAll('.prompt-text')
      // 获取元素下子元素列表
      // let childEles = htmlEle.children
      // 遍历子元素
      for (let i = 0; i < childEles.length; i++) {
        result += this.getPromptContent(childEles[i])
      }
      return result
    },
    // 遍历 dom 元素，获取元素内部每一个子节点，包含子节点的子节点，一直到 node 为 text_node 为最底层节点，然后获取节点是否是变量，如果是变量就在它的 innerText 左右拼接 {{ 和 }}，如果不是变量，则直接拼接 innerText
    getPromptContent(dom, promptContent = '') {
      // 如果节点的类型是文本节点，直接返回，说明找到了最底层的节点
      if (dom.nodeType === Node.TEXT_NODE) {
        // 如果是变量，假设父节点是元素节点并且有属性 variable
        if (dom.textContent.trim() !== ''
          && dom.parentNode.nodeType === Node.ELEMENT_NODE
          && dom.parentNode.getAttribute('variable')) {
          promptContent += `{{ ${dom.textContent.trim()} }}`
        } else if (!dom.parentNode.getAttribute('data-no-show')) {
          promptContent += dom.textContent
        }
        return promptContent
      }

      // 获取子节点
      let childNodes = dom.childNodes;
      // 遍历子节点
      for (const childNode of childNodes) {
        promptContent = this.getPromptContent(childNode, promptContent);
      }

      return promptContent;
    },
    // 构建编辑器内容
    buildPromptDom(promptContent) {
      // 循环文本，生成节点
      // 创建一个元素作为容器
      let nodeParent = document.createElement('div')
      promptContent.forEach(content => {
        // 将 content 按照 <br> 拆分
        let contents = content.content.split('<br>')
        // 遍历拆分后的内容
        contents.forEach((item, index) => {
          // 创建编辑器元素
          this.createEditElement({
            content: item,
            promptSource: content.promptSource,
            isVariable: content.isVariable
          }, nodeParent)
          // 如果不是最后一个元素，则添加一个 <br> 元素
          if (index !== contents.length - 1) {
            let br = document.createElement('br')
            nodeParent.appendChild(br)
          }
        })
      })
      // 返回节点内部所有的 dom 元素
      return nodeParent.innerHTML
    },
    // 创建编辑器元素
    createEditElement(content, nodeParent) {
      // 创建一个 span 元素
      let node = document.createElement('span')
      // 设置 node 的 class
      node.classList.add('prompt-text')
      // 设置 node 的 data-source 的属性为 source
      node.setAttribute('data-source', JSON.stringify(content.promptSource))
      node.setAttribute('source-name', JSON.stringify(content.content))
      // 设置 node 的内容
      node.innerHTML = this.formatSource(content.content)
      // 如果是变量，添加变量标志
      if (content.isVariable) {
        // 设置父节点 Id
        let uuidv = uuidv4()
        // 设置 node 的 class
        node.classList.add('variable-text')
        // 设置 node 的属性
        node.setAttribute('variable', 'true')
        // 同时这个 node 是后续需要动态挂载的对象，为其添加一个 ID
        node.id = 'variable-' + uuidv
        // 同时这个元素本身是不允许编辑的
        node.setAttribute('contenteditable', 'false')
      }
      // 将 node 添加到 nodeParent 中
      nodeParent.appendChild(node)
    },
    parsePromptContent(prompt) {
      // 没有内容，返回空数组
      if (!prompt) {
        return []
      }
      // 获取 promptSourceList
      let promptSourcesList = this.promptSourcesList.flatMap(item => item.sources)
      // 拆分结果
      let results = []
      // 按照 {{ 拆分内容
      let splitLeftContents = prompt.split('{{')
      // 遍历拆分内容
      splitLeftContents.forEach((splitLeftContent, index) => {
        // 如果内容包含 }}，则再次拆分
        if (splitLeftContent.includes('}}')) {
          let splitRightContents = splitLeftContent.split('}}')
          // 遍历拆分内容
          splitRightContents.forEach((splitRightContent, index) => {
            // 第一个元素为变量
            let isVariable = index === 0
            // 加入结果列表中
            let replace = splitRightContent.replace(/<([^>]*)>/g, '&lt;$1&gt;')
            // 获取 replace 对应的 promptSource
            let promptSource
            // 如果是变量，获取对应的 promptSource
            if (isVariable) {
              promptSource = promptSourcesList.find(item => equalsIgnoreCase(item.sourceName.trim(), replace.trim()))
            }
            results.push({
              content: this.formatSource(replace),
              isVariable: isVariable,
              promptSource: promptSource
            })
          })
        } else {
          // 如果不包含 }}，则直接添加
          let result = splitLeftContent.replace(/<([^>]*)>/g, '&lt;$1&gt;')
          results.push({
            content: this.formatSource(result),
            isVariable: false
          })
        }
      })
      return results
    },
    onCompositionStart() {
      this.inputLock = true
    },
    onCompositionEnd() {
      this.inputLock = false
      this.handleInput()
    },
    handleClickEditorContainer() {
      this.isFocus = true
      this.$el && this.$el.querySelector('.textarea-item.p').focus()
    },
    handleKeyDown(event) {
      // 获取当前选中的文本
      let selection = document.getSelection()
      // 获取光标所在的第一个位置
      let range = selection.getRangeAt(0)
      // 如果使用了 ctrl + z，则将上一步的操作撤销
      if (event.code === 'KeyZ' && event.ctrlKey === true && event.altKey === false) {
        if (this.revoke.length > 0) {
          let textareaDom = this.$el && this.$el.querySelector('.textarea-item.p')
          this.recover.push(this.revoke.pop())
          if (this.revoke.length > 0) {
            let text = this.revoke[this.revoke.length - 1]
            textareaDom.innerHTML = text.content
            this.setPosition(textareaDom, text.range)
          }
          this.isRevokeOrRecover = true
        }
      }
      // 如果哦使用了 ctrl + y，则将上一步的操作恢复
      if (event.code === 'KeyY' && event.ctrlKey === true && event.altKey === false) {
        if (this.recover.length > 0) {
          let textareaDom = this.$el && this.$el.querySelector('.textarea-item.p')
          let text = this.recover.pop()
          textareaDom.innerHTML = text.content
          this.setPosition(textareaDom, text.range)
          this.revoke.push(text)
          this.isRevokeOrRecover = true
        }
      }
      // 如果使用了 delete
      if (event.code === 'Delete' || event.key === 'Delete') {
        // 获取当前选中的文本
        if (range.startOffset === range.endOffset && range.collapsed && range.endContainer === range.startContainer && range.endOffset === range.startContainer.textContent.length) {
          let currentNode = range.startContainer.parentElement || range.startContainer.parentNode
          let nextNode = currentNode.nextSibling
          // 如果下一个节点是 card 节点，光标应直接定位到 card 的下方起始
          if ((nextNode && nextNode.id) && nextNode.id.indexOf('variable-') > -1) {
            // 已经到最后了，但是下一个是 card，则直接将 card 移除
            nextNode.remove()
            // 光标定位到链接节点的开头
            this.setEditorCursor(nextNode.nextSibling, true)
          }
        }
      }
      // 如果使用了 backspace
      if (event.key === 'Backspace' || event.code === 'Backspace') {
        // 获取当前选中的文本，如果选中的文本此时内容只有一个字符，并且这个字符是空格，那么直接当做 startOffset 和 endOffset 都是 0 处理
        let sameContainer = range.endContainer === range.startContainer;
        // 如果是相同的容器
        if (sameContainer && range.startContainer.textContent.length === 1) {
          range.startContainer.textContent = range.startContainer.textContent.trim()
        }
        // 如果前后相等，则说明元素已经全部删除了，或者 textContainer 里面字符为 1 经过 trim 处理之后也为空了
        // 同时满足起始容器和结束容器是一个容器
        if (((range.startOffset === 0 && range.endOffset === 0) || (range.startContainer.textContent === ''))
          && range.endContainer === range.startContainer) {
          let brotherNode = range.startContainer.previousSibling
          // 如果上一个节点是 card 节点，光标应直接定位到到链接节点的末尾
          if ((brotherNode && brotherNode.id) && brotherNode.id.indexOf('variable-') > -1) {
            // previousElementSibling: 返回元素节点之前的兄弟元素节点（不包括文本节点、注释节点）；
            // previousSibling: 返回元素节点之前的兄弟节点（包括文本节点、注释节点）
            // 由于删除到的是 varibale 节点，所以直接将其移除掉
            brotherNode.remove()
            // 光标定位到链接节点的末尾
            let previousSibling = brotherNode.previousSibling
            if (!previousSibling) {
              previousSibling = range.startContainer.previousSibling
            }
            if (previousSibling.classList.contains('before-variable')) {
              this.setEditorCursor(previousSibling, false)
            } else {
              this.setEditorCursor(previousSibling, false)
            }
          }
        }
        // 如果当前选中的文本只有一个，说明这个字符就要被移除了，并且这个容器的 class 是 prompt-text
        if ((range.startContainer.textContent.length <= 1) && !range.startContainer.parentElement.classList.contains('before-variable')
         && range.startContainer.parentElement.classList.contains('prompt-text')) {
          range.startContainer.remove()
        }
      }
    },
    // 合并相邻的 text 节点
    mergeAdjacentTextNodes(nodes, nodeDom) {
      // 如果是多个节点的情况下去判断是否有直接是 text 节点的情况
      let textNodes = []
      nodes.forEach((item, index) => {
        if (item.nodeType === Node.TEXT_NODE) {
          textNodes.push(item)
        }
      })
      // 如果 textNodes 存在，那么将当前 textNode 转化为 span 节点
      if (textNodes.length > 0) {
        textNodes.forEach(textNode => {
          // 克隆一个节点
          const cloneNode = textNode.cloneNode(true);
          // 创建一个 span
          const tempSpanNode = document.createElement('span')
          // 设置 span 节点的 class
          tempSpanNode.classList.add('prompt-text')
          tempSpanNode.appendChild(cloneNode)
          // 移除掉之前的节点，将 span 节点挂载到 nodeDom 上
          // 将 span 节点挂载到 nodeDom 上
          nodeDom.appendChild(tempSpanNode)
          // 移除老结点
          textNode.remove()
        })
      }
    },
    // 对接点预处理
    preprocessNodes(nodeDom) {
      // 获取所有的节点
      let nodes = nodeDom.childNodes;
      // 克隆节点
      let textNode
      // 如果节点只有一个，并且当前节点是 text 节点，那么对当前节点进行包装
      if (nodes.length === 1 && nodes[0].nodeType === Node.TEXT_NODE) {
        textNode = nodes[0].cloneNode(true)
        // 移除掉之前的节点，将 span 节点挂载到 nodeDom 上
        nodes[0].remove()
      } else {
        // 将直接子 text 节点合并
        this.mergeAdjacentTextNodes(nodes, nodeDom)
        return
      }
      // 创建一个 span 节点
      let spanNode = document.createElement('span')
      // 设置 span 节点的 class
      spanNode.classList.add('prompt-text')
      // 设置 span 节点的 id
      spanNode.appendChild(textNode)
      // 将 span 节点挂载到 nodeDom 上
      nodeDom.appendChild(spanNode)
      // 把光标移动到最后
      this.keepLastIndex(nodeDom)
    },
    handleInput() {
      if (this.inputLock) {
        return
      }
      let nodeDom = this.$el && this.$el.querySelector('.textarea-item.p')
      // 预处理节点
      this.preprocessNodes(nodeDom)
      // 克隆节点
      let cloneNodeDom = this.$el && this.$el.querySelector('.textarea-item.p').cloneNode(true)
      // 解析文本框节点
      let parsedNodes = []
      let nodeTemp = []
      let newChildList = []
      let cardTemp = []
      // 去除 card 节点
      let nodes = cloneNodeDom.childNodes
      nodes.forEach((item, index) => {
        if (item.id && item.id.indexOf('variable-') > -1) {
          // 如果是变量节点，将其放入 cardTemp 中
          cardTemp.push(item)
          let itemEle = document.createElement('span')
          // 将变量节点替换为文本节点，后续是要替换这个文本节点的
          let items = document.createTextNode(';0fj0ew^2*5$@2')
          itemEle.appendChild(items)
          // 为了后续挂在，为临时节点赋值 id
          items.id = item.id
          itemEle.id = item.id
          // 将临时的文本节点放置到新的子节点列表中
          newChildList.push(itemEle)
          return
        }
        // 火狐等浏览器兼容性问题, <br> 转义
        if (item.nodeName === "BR") {
          item = document.createTextNode('\r\n')
        }
        newChildList.push(item)
      })
      // 以 a 标签为界限分割子节点
      newChildList.forEach((item, index) => {
        if (index === newChildList.length - 1) {
          nodeTemp.push(item)
          this.parsedNode(nodeTemp).forEach((item, index) => {
            parsedNodes.push(item)
          })
          nodeTemp = []
        }
        nodeTemp.push(item)
      })
      let nodeParent = document.createElement('div')
      parsedNodes.forEach((item, index) => {
        nodeParent.appendChild(item)
      })
      // 格式化编辑器内容（合并 text 节点，使光标定位正确）
      // nodeDom.normalize()
      nodeParent.innerHTML = nodeParent.innerHTML.replace(/;0fj0ew\^2\*5\$@2/g, '') // ;0fj0ew^2*5$@2
      let position = this.getPosition(nodeDom)
      // 更新 position 的位置
      this.currentPosition = position
      nodeDom.innerHTML = nodeParent.innerHTML
      // 设置光标位置
      this.setPosition(nodeDom, position)
      // 挂载 card
      cardTemp.forEach((item, index) => {
        this.generateCards(item)
      })
      // 如果本次 input 事件为 ctrl + z 或者 ctrl + y 事件，那么不处理
      if (!this.isRevokeOrRecover && nodeDom.innerHTML !== this.revoke[this.revoke.length - 1]) {
        // 将当前编辑器内容放入撤销缓存
        this.revoke.push({content: nodeDom.innerHTML, range: position})
        this.revoke.length > 10 && this.revoke.shift()
        this.Recover = []
      }
      this.isRevokeOrRecover && (this.isRevokeOrRecover = false)
      this.syncValue()
    },
    // 给卡片按钮增加关闭事件
    insertAfter(newElement, targetElement) {
      let parent = targetElement.parentNode//获取目标节点的父级标签
      if (parent.lastChild === targetElement) {//如果目标节点正好是最后一个节点，使用appendChild插入
        parent.appendChild(newElement)
      } else {
        parent.insertBefore(newElement, targetElement.nextSibling)//一般情况下要取得目标节点的下一个节点，再使用insertBefore()方法。
      }
    },
    generateCards(item) {
      // 获取变量的 source
      let attribute = item.getAttribute('data-source')
      let sourceName = item.getAttribute('source-name')
      // 定义 source
      const source = {
        sourceName: JSON.parse(sourceName)
      }
      // 每次挂载之前，看一下之前的 card 是否已经挂载，如果挂载了，那么就把旧的给移除掉
      let oldCard = this.generatedCards.find(card => equalsIgnoreCase(card.id, item.id));
      // 如果 oldCard 存在，那么就移除掉
      if (oldCard) {
        // 移除 card
        oldCard.variableTag.$el.innerHTML = '';
        // 销毁 card
        oldCard.variableTag.$destroy();
        // 移除 card
        this.generatedCards = this.generatedCards.filter(card => !equalsIgnoreCase(card.id, item.id));
      }

      // 如果 source 为空，sourceValue 则为空
      if (attribute !== 'undefined' && attribute !== null && attribute !== '') {
        source.sourceValue = JSON.parse(attribute).sourceValue
      } else {
        source.sourceValue = ''
      }
      // 挂载 card
      let data = {
        propsData: {
          itemId: item.id,
          attribute: attribute,
          source: {
            sourceName: source.sourceName,
            sourceValue: source.sourceValue
          }
        }
      }
      // 创建一个 card
      let variableTag = new promptVariableTag(data);
      // 将 card 添加到 generatedCards 中
      this.generatedCards.push({id: item.id, sourceName: sourceName, variableTag: variableTag});

      // 挂载 card
      variableTag.$mount('#' + item.id);
    },
    parsedNode(nodeTemp) {
      if (!nodeTemp || nodeTemp.length === 0) {
        return []
      }
      // 拿到 doms 节点中的文本
      let nodeParent = document.createElement('div')
      nodeTemp.forEach((item, index) => {
        nodeParent.appendChild(item.cloneNode(true))
      })
      // 返回节点内部所有的 dom 元素
      return nodeParent.childNodes
    },
    // 获取光标位置
    getPosition(dom) {
      // 编辑器未初始化不获取光标
      if (!this.editorInitialized) {
        return
      }
      // 获取光标所在位置
      let selection = document.getSelection();
      // 如果 selection 为空，返回 undefined
      if (selection.rangeCount <= 0) {
        return
      }
      // 获取 range
      let range = selection.getRangeAt(0)
      // 获取光标所在节点
      let node = range.commonAncestorContainer
      // 获取光标所在位置
      let offset = range.startOffset
      // 记录偏移量
      let position = 0
      // 获取传入 dom 节点的子节点，判断光标真实存在在那个子节点中
      let childNodes = dom.childNodes
      // 遍历子节点
      for (const childNode of childNodes) {
        // 过滤 card 节点
        if (childNode.id && childNode.id.indexOf('variable-') > -1) {
          continue
        }
        // 如果子节点是当前节点
        if (childNode === node) {
          // 返回当前节点的偏移量
          position += offset
          break
        }
        // 如果子节点包含当前节点
        if (childNode.contains(node)) {
          // 获取当前节点的 position
          position += this.getPosition(childNode)
          break
        }
        // 如果到最后的文本节点，那么再加上文本的偏移量
        position += childNode.textContent.length
      }
      return position
    },
    // 设置光标位置
    setPosition(dom, position) {
      // 编辑器未初始化不设置光标
      if (!this.editorInitialized) {
        return
      }
      let {node, offset} = this.getDeepestNode(dom, position)
      let selection = document.getSelection()
      let range = document.createRange()
      range.setEnd(node, offset)
      range.collapse()
      // 清除所有光标对象
      selection.removeAllRanges()
      // 添加新的光标对象
      selection.addRange(range)
    },
    getDeepestNode(dom, position) {
      // 如果节点的类型是文本节点，直接返回，说明找到了最底层的节点
      if (dom.nodeType === Node.TEXT_NODE) {
        return {node: dom, offset: position}
      }
      // 获取子节点
      let childNodes = dom.childNodes
      // 遍历子节点
      for (const childNode of childNodes) {
        // 过滤 card 节点
        if (childNode.id && childNode.id.indexOf('variable-') > -1) {
          continue
        }
        // 获取 length
        let length = childNode.textContent.length
        // 如果 position 小于等于 length，说明光标在当前节点中
        if (position <= length) {
          // 递归寻找最底层包含光标的节点
          return this.getDeepestNode(childNode, position)
        }
        // 减少 position 的位置
        position -= length
      }
      // 返回 dom 元素
      return {node: dom, offset: position}
    },
    handleBlur() {
      // 当编辑器失去焦点时，打开校验
      this.isFocus = false
      this.openVerify = true
      // 记录焦点位置
      let dom = this.$el && this.$el.querySelector('.textarea-item.p');
      // 传递最顶层的 dom，获取光标所在位置并存储起来
      this.currentPosition = this.getPosition(dom)
    },
    handleFocus() {
      this.isFocus = true // 为了校验失败的样式
    },
    // 设置光标
    setEditorCursor(node, toStart) {
      // 如果 node 不存在，则无法设置光标
      if (!node) {
        return
      }
      this.$nextTick(() => {
        // 如果 node 是一个函数，则执行函数
        // 将光标聚焦在 node 上
        (node.focus) && node.focus()
        // 获取光标所在位置
        let selection = document.getSelection()
        // 创建一个光标对象
        let range = document.createRange()
        // 将光标设置在 node 里
        range.selectNodeContents(node)
        // 设置光标的位置
        range.collapse(toStart)
        // !toStart && range.setStart(node, node.childNodes.length)
        // !toStart && range.setEnd(node, node.childNodes.length)
        // 清除所有光标对象
        selection.removeAllRanges()
        // 添加新的光标对象
        selection.addRange(range)
      })
    },
    syncValue() {
      // 同步编辑器内容
      // 获取编辑器的顶层 dom
      let textareaDom = this.$el && this.$el.querySelector('.textarea-item.p')
      // 计算长度 this.editorContentLength
      this.editorContentLength = textareaDom.innerText.trim().length
      // 如果未开启校验，直接返回
      if (!this.openVerify) {
        return
      }
      // 获取编辑器内容，如果编辑器内容不为空则解析编辑器中的内容
      let description = this.editorContentLength !== 0 && this.parseContents()
      // 将内容传递给父组件
      this.$emit('input', description)
      this.$emit('change', description)
    },
    clearValue() {
      // 获取编辑器的顶层 dom
      if (this.$el) {
        let textareaDom = this.$el.querySelector('.textarea-item.p')
        // 清空编辑器内容
        if (textareaDom) {
          textareaDom.innerText = ''
        }
        // 组将更新
        this.$forceUpdate()
      }
    },
    dataInitialize() {
      this.editorInitialized = false // 编辑器数据初始化
    },
    // 保持光标在最后
    keepLastIndex(obj) {
      if (window.getSelection) { // ie11 10 9 ff safari
        obj.focus() // 解决ff不获取焦点无法定位问题
        let range = window.getSelection() // 创建range
        range.selectAllChildren(obj) // range 选择obj下所有子内容
        range.collapseToEnd() // 光标移至最后
      } else if (document.selection) { // ie10 9 8 7 6 5
        let range = document.selection.createRange() // 创建选择对象
        range.moveToElementText(obj) // range定位到obj
        range.collapse(false) // 光标移至最后
        range.select()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.is-error .prompt-text-editor-textarea {
  border-color: #f56c6c;
}

.prompt-textarea {
  overflow-y: auto;
  overflow-x: hidden;
  padding: 5px 15px;
  line-height: 26px;
  color: #606266;
  //display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
}

.variable-text {
  color: #5e6d82;
  background-color: #f9fafc;
  padding: 0px 4px;
  border: 1px solid #eaeefb;
  border-radius: 4px;
  margin: 0 4px;
  cursor: pointer;
}

.prompt-text-editor-textarea {
  line-height: 18px;
  display: flex;
  flex-flow: column;
  cursor: text;
  // 兼容 Safari 浏览器
  -webkit-user-select: text;
  user-select: text;
  margin: 0 0 10px;
  height: 100%;
}

.textarea-item {
  padding: 0 15px;
  -webkit-user-modify: read-write-plaintext-only;
}

.textarea-item.p {
  height: 100%;
  width: 100%;
  word-break: break-word;
  // 兼容 Safari 浏览器
  -webkit-user-select: text;
  user-select: text;
  cursor: text;
  // 兼容 Safari、火狐（换行失效）
  white-space: break-spaces;
  //display: inline-block;
}

/deep/ .textarea-item.p a, a:hover, a:focus {
  cursor: text;
  color: #10B3B7;
  word-break: break-all;
}

.textarea-item-div {
  padding-top: 5px;
  height: 100%;
}

.prompt-text-editor-textarea p {
  width: calc(100% - 30px);
  line-height: 1.5;
}
</style>