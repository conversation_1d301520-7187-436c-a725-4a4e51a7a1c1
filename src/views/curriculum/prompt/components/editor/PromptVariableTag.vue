<template>
  <span :id="itemId"
        :data-source="attribute"
        :source-name="JSON.stringify(source.sourceName)"
        class="variable-tag prompt-text"
        contenteditable="false">
        <el-tag
          effect="light"
          type="info"
          :title="formatSource(source.sourceValue)"
          size="mini"
          variable="true"
          v-html="formatSource(source.sourceName)"
          :disable-transitions="true">
        </el-tag>
  </span>
</template>
<script>
export default {
  name: 'PromptVariableTag',
  props: ['itemId', 'attribute', 'source'],
  data() {
    return {
      leavedPage: false // 是否离开页面
    }
  },
  destroyed() {
    this.leavedPage = true
  },
  computed: {
    formatSource() {
      return (source) => {
        if (source) {
          // 如果 source 不是字符串，则直接返回 source 本身, 如果 source 是空的，那么直接返回
          if (typeof source !== 'string') {
            return source
          }
          if (source.trim() === '') {
            return '-'
          }
          // 去除 source 中的前后空格包括制表符和换行符
          let replace = source.replace(/(^\s*)|(\s*$)/g, '')
          // 将 replace 当做 html 解析，然后返回对应的文本
          let div = document.createElement('div')
          div.innerHTML = replace
          replace = div.innerText
          // 返回结果
          return replace
        }
        return '-'
      }
    }
  }
}
</script>

<style scoped lang="less">

</style>