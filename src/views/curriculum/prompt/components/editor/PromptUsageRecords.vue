<template>
    <div class="display-flex">
        <!-- 选择菜单 -->
        <div class="usage-menu">
            <div v-for="(usageRecord, index) in usageRecords" :key="index"
                class="display-flex justify-content-between align-items lg-pointer menu-item position-relative"
                :class="{'active': activeUsageRecord && activeUsageRecord.id === usageRecord.id}"
                @click="changedActiveUsageRecord(usageRecord)">
                <!-- 序号 -->
                <div class="">Entry {{ index + 1 }}</div>
                <!-- 分数 -->
                <div>
                    <!-- 处理中 -->
                    <span v-if="usageRecord.executeStatus === 'PENDING' || usageRecord.executeStatus === 'PROCESSING'"><i class="el-icon-loading"></i></span>
                    <!-- 出错 -->
                    <span v-else-if="usageRecord.executeStatus === 'ERROR'"><i class="text-error el-icon-warning-outline"></i></span>
                    <!-- 成功 -->
                    <div v-else-if="usageRecord.score || usageRecord.averageScore || usageRecord.testRuleCount" class="display-flex flex-direction-col flex-auto-vertical flex-justify-end align-items-end">
                        <!-- 通过规则校验 -->
                        <div v-if="usageRecord.passRuleCount && usageRecord.testRuleCount">{{ usageRecord.passRuleCount }} / {{ usageRecord.testRuleCount }}</div>
                        <!-- 分数 -->
                        <div>
                            <span v-if="usageRecord.averageScore">{{ 'Score: ' + usageRecord.averageScore }}</span>
                            <span v-else-if="usageRecord.score">{{ 'Score: ' + usageRecord.score }}</span>
                        </div>
                    </div>
                    <!-- 其他情况 -->
                    <span v-else>{{ '--' }}</span>
                </div>
                <!-- 选中标志 -->
                <div class="active-menu-mark" v-show="activeUsageRecord && activeUsageRecord.id === usageRecord.id"></div>
            </div>
        </div>
        <!-- 详情 -->
        <div class="m-l-sm m-r-sm flex-auto">
            <!-- 头部 -->
            <div class="display-flex justify-content-between align-items">
                <!-- 标题 -->
                <div class="font-size-18 font-bold">Evaluation Summary</div>
                <!-- 标签 -->
                <div class="lg-tabs-small m-b-sm">
                    <el-tabs v-model="currentView">
                        <el-tab-pane label="Content Details" name="CONTENT"></el-tab-pane>
                        <el-tab-pane label="Validation Results" name="EVALUATE"></el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <!-- 生成信息 -->
            <div class="m-t-sm m-b-md">
                <el-skeleton :rows="1" animated :loading="activeUsageRecordLoading">
                    <!-- 生成时长 -->
                    <span class="tag-default">Duration: {{ activeUsageRecord ? activeUsageRecord.executeDuration : '--' }}</span>
                    <!-- 费用 -->
                    <span class="tag-default m-l-sm">Cost: ${{ activeUsageRecord ? activeUsageRecord.executeCost : '--' }}</span>
                </el-skeleton>
            </div>
            <!-- 内容 -->
            <div v-if="activeUsageRecord">
                <el-skeleton :rows="10" animated :loading="activeUsageRecordLoading">
                    <template>
                        <div v-show="currentView === 'CONTENT'" v-html="convertContent(activeUsageRecord.completion)"></div>
                        <div v-show="currentView === 'EVALUATE'">
                            <!-- 直接展示评估内容 -->
                            <div v-if="!activeUsageRecord.testRules || activeUsageRecord.testRules.length === 0" v-html="convertContent(activeUsageRecord.evaluateCompletion)"></div>
                            <!-- 评估结果表格 -->
                            <div v-else>
                                <el-table
                                    :data="activeUsageRecord.testRules"
                                    class="w-full">
                                    <!-- 规则名称 -->
                                    <el-table-column label="Name" min-width="110" :show-overflow-tooltip="true">
                                        <template slot-scope="scope">
                                            <span>{{ scope.row.name }}</span>
                                        </template>
                                    </el-table-column>
                                    <!-- 规则内容 -->
                                    <el-table-column label="Rule" min-width="110" :show-overflow-tooltip="true">
                                        <template slot-scope="scope">
                                            <span v-if="scope.row.verificationType === 'CONTAINS'">
                                                Result contains "{{ scope.row.verificationValue }}"
                                            </span>
                                            <span v-else-if="scope.row.verificationType === 'NOT_CONTAINS'">
                                                Result not contains "{{ scope.row.verificationValue }}"
                                            </span>
                                            <span v-else-if="scope.row.verificationType === 'EQUALS_IGNORE_CASE'">
                                                Result equals "{{ scope.row.verificationValue }}"
                                            </span>
                                            <span v-else-if="scope.row.verificationType === 'EVALUATE_SCORE_BY_GPT'">
                                                Evaluate score through GPT
                                            </span>
                                            <span v-else-if="scope.row.verificationType === 'CUSTOM_EVALUATE_BY_GPT'">
                                                Custom evaluate through GPT
                                            </span>
                                        </template>
                                    </el-table-column>
                                    <!-- 规则描述 -->
                                    <el-table-column label="Description" min-width="110" :show-overflow-tooltip="true">
                                        <template slot-scope="scope">
                                            <span>{{ scope.row.description }}</span>
                                        </template>
                                    </el-table-column>
                                    <!-- 分数 -->
                                    <el-table-column label="Score" min-width="110" :show-overflow-tooltip="true">
                                        <template slot-scope="scope">
                                            <span>{{ scope.row.score }}</span>
                                        </template>
                                    </el-table-column>
                                    <!-- 验证结果 -->
                                    <el-table-column label="Result" min-width="110" :show-overflow-tooltip="true">
                                        <template slot-scope="scope">
                                            <span>{{ scope.row.result }}</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </template>
                </el-skeleton>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        // 记录列表
        usageRecords: {
            type: Array,
            default: null,
        },
    },

    data() {
        return {
            activeUsageRecord: null, // 当前活跃记录
            currentView: 'CONTENT', // 当前视图
        }
    },

    computed: {
        // 当前活跃记录的内容
        convertContent() {
            return function (content) {
                if (!content) {
                    return ''
                }
                return content.replace(/\n/g, '<br>')
            }
        },

        // 是否处于加载状态
        activeUsageRecordLoading () {
            if (!this.activeUsageRecord) {
                return false
            }
            return this.activeUsageRecord.executeStatus === 'PENDING' || this.activeUsageRecord.executeStatus === 'PROCESSING' || (this.currentView == 'EVALUATE' && (!this.activeUsageRecord.evaluateCompletion && (!this.activeUsageRecord.testRules || this.activeUsageRecord.testRules.length === 0)))
        }
    },

    watch: {
        // 监听记录列表变化
        usageRecords: {
            handler() {
                this.updateActiveUsageRecord()
            },
            deep: true,
        },
    },

    methods: {
        // 切换活跃记录
        changedActiveUsageRecord(usageRecord) {
            this.activeUsageRecord = usageRecord
        },

        // 重置活跃记录
        resetActiveUsageRecord() {
            if (!this.usageRecords || this.usageRecords.length === 0) {
                return
            }
            this.activeUsageRecord = this.usageRecords[0]
        },

        // 更新活跃记录
        updateActiveUsageRecord() {
            if (!this.activeUsageRecord || !this.activeUsageRecord.id || !this.usageRecords || this.usageRecords.length === 0) {
                return
            }
            // 遍历记录列表
            for (let i = 0; i < this.usageRecords.length; i++) {
                const usageRecord = this.usageRecords[i]
                // 找到活跃记录
                if (usageRecord.id === this.activeUsageRecord.id) {
                    // 更新活跃记录
                    this.activeUsageRecord = usageRecord
                    break
                }
            }
        },
    },
}
</script>

<style lang="less" scoped>
.usage-menu {
    width: 200px;
    flex-shrink: 0;
    border-radius: 4px;
    .menu-item {
        padding: 0 10px;
        height: 50px;
        background-color: #F5F6F8;
    }
    .menu-item:hover {
        background-color: #DDF2F3;
    }
    .menu-item.active {
        background-color: #DDF2F3;
        color: #10B3B7;
    }
    .active-menu-mark {
        width: 3px;
        height: 100%;
        background-color: #10B3B7;
        position: absolute;
        left: 0;
        top: 0;
    }
}
</style>