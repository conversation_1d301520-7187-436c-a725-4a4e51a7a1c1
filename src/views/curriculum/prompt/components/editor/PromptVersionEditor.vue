<template>
    <div>
        <PromptEditor
            ref="promptEditorRef"
            :promptId="promptId"
            :scenes="scenes"
            :canAddTab="canAddTab"
            :showGenerateBtn="showGenerateBtn"
            :needGetCustomPrompt="needGetCustomPrompt"
            :showUpdatePromptBtn="true"
            :generateFun="generateFun"
            @openPromptListDialog="openPromptListDialog"
            @batchGenerateResultsSuccess="batchGenerateResultsSuccess"
            @updatePromptId="updatePromptId"
        />
        <!-- 版本列表弹窗 -->
        <PromptListDialog
            v-if="promptListDialogVisible"
            :scene="$refs.promptEditorRef.currentScene"
            :visible.sync="promptListDialogVisible"
            :disableEdit="disableListEdit"
            @changedActivePrompt="changedActivePrompt"
        />
    </div>
</template>

<script>
import PromptEditor from '@/views/curriculum/prompt/components/editor/PromptEditor'
import PromptListDialog from '@/views/curriculum/prompt/components/list/PromptListDialog.vue'

export default {
    props: {
        // Prompt 场景
        scenes: {
            type: Array,
            default: () => [],
        },

        needGetCustomPrompt: {
            type: Boolean,
            default: true,
        },
        // Prompt ID
        promptId: {
            type: String,
            default: null,
        },

        // 是否显示生成按钮
        showGenerateBtn: {
            type: Boolean,
            default: false,
        },

        generateFun: {
            type: Function,
            default: null,
        },
        // 是否可以添加 Tab
        canAddTab: {
            type: Boolean,
            default: true,
        },
    },

    components: {
        PromptEditor, // Prompt 编辑器
        PromptListDialog, // Prompt 版本列表弹窗
    },

    data() {
        return {
            promptListDialogVisible: false, // Prompt 版本列表弹窗是否显示
        }
    },

    computed: {
        // 是否禁用列表编辑
        disableListEdit() {
            let promptRef = this.$refs.promptEditorRef
            if (!promptRef || !promptRef.prompt) {
                return
            }
            let prompt = promptRef.prompt
            return !prompt.evaluatePromptTemplate
        }
    },

    methods: {
        // 切换活跃场景
        changedActivePrompt(prompt) {
            this.$refs.promptEditorRef.initPrompt()
        },

        // 打开 Prompt 版本列表弹窗
        openPromptListDialog() {
            this.promptListDialogVisible = true
        },

        // 批量生成结果成功
        batchGenerateResultsSuccess(result) {
            console.log('batchGenerateResultsSuccess')
            this.$emit('batchGenerateResultsSuccess', result)
        },

        // 更新 Prompt ID
        updatePromptId(promptId) {
            this.$emit('updatePromptId', promptId)
        },

        // 测试完成
        testCompleted() {
            // 获取测试结果
            this.$refs.promptEditorRef.testCompleted()
        },

        // 测试未完成
        testIncomplete() {
            console.log('testIncomplete unit overview')
            // 获取测试结果
            this.$refs.promptEditorRef.testIncomplete()
        },
    },
}
</script>

<style lang="less" scoped>
</style>