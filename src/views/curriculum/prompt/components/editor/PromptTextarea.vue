<template>
    <div>
        <PromptTextareaEditor
            :promptTextareaStyle="promptTextareaStyle"
            ref="promptTextareaEditor"
            from="promptTextareaToolbar"
            :fromTemplate="fromTemplate"
            :value="value"/>
        <!-- 功能栏   -->
        <PromptTextareaToolbar
            class="prompt-textarea"
            from="from"
            :fromTemplate="fromTemplate"
            :value="value"
        />
    </div>
</template>

<script>
import PromptTextareaToolbar from './PromptTextareaToolbar.vue'
import PromptTextareaEditor from './PromptTextareaEditor.vue'

export default {
    props: {
        value: {
            type: String,
            default: ''
        },
        fromTemplate: {
            type: String,
            default: ''
        }
    },
    computed: {
        promptTextareaStyle() {
            return {
                maxHeight: '328px'
            }
        }
    },
    watch: {
        value: {
            handler: function (val) {
                // 如果没有值，就初始化编辑器
                this.$refs.promptTextareaEditor && this.$refs.promptTextareaEditor.initEmptyEditor(val)
                // 如果有编辑器，就调用编辑器的方法
                if (val && this.$refs.promptTextareaEditor) {
                    this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
                    this.$refs.promptTextareaEditor && this.$refs.promptTextareaEditor.initPromptTextEditor(this.value)
                }
            }
        }
    },
  components: {
        PromptTextareaEditor, // 编辑器
        PromptTextareaToolbar // 功能栏
    },
    methods: {
        // 添加方法去获取编辑框中的数据
        getPromptContent() {
            // 如果有编辑器，就调用编辑器的方法
            if (this.$refs.promptTextareaEditor) {
                return this.$refs.promptTextareaEditor.parseContents()
            } else {
                // 否则就返回原本给的值
                return this.value
            }
        },
        // 更新内容，如果 prompt 发生了变化，这里需要调用一下内容更新
        updateContent(content) {
            this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
            this.$refs.promptTextareaEditor && this.$refs.promptTextareaEditor.initPromptTextEditor(content)
        }
    }
}
</script>

<style lang="less" scoped>
.prompt-textarea {
    max-height: 328px;
    overflow-y: auto;
    overflow-x: hidden !important;
    padding: 5px 15px;
    line-height: 22px;
    color: #606266;
}

.variable-text {
    color: #5e6d82;
    background-color: #f9fafc;
    padding: 0px 4px;
    border: 1px solid #eaeefb;
    border-radius: 4px;
    margin: 0 4px;
    cursor: pointer;
}
</style>