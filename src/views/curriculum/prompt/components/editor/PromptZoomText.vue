<template>
  <el-dialog
    :append-to-body="true"
    :visible.sync="showZoomDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="updatePromptTemplate"
    class="dialog-vertical"
    :top="'50px'"
    :destroy-on-close="true">
    <el-tabs v-if="showZoomDialog" v-loading="zoomTextLoading" type="border-card" v-model="currentTagName" class="ai-assistant-tab
     display-flex flex-direction-col justify-content align-items" @tab-click="switchType">
      <el-tab-pane :label="submodules[0]" :name="submodules[0]" @click="selectTextOption">
        <div class="display-flex flex-direction-col flex-justify-end align-items-end gap-10">
          <!-- 查看 Source 信息 -->
          <el-button style="position: relative; right: 10px;" type="primary" plain size="medium"
                     @click.stop="viewSources">
            <div class="display-flex justify-content btn-center">
              <span v-show="!showHideSources">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M2.9095 13.1718C2.56793 12.4286 2.56793 11.5714 2.9095 10.8282C4.4906 7.38843 7.96659 5 12.0004 5C16.0343 5 19.5102 7.38843 21.0913 10.8282C21.4329 11.5714 21.4329 12.4286 21.0913 13.1718C19.5102 16.6116 16.0343 19 12.0004 19C7.96659 19 4.4906 16.6116 2.9095 13.1718Z"
                  stroke="#10B3B7" stroke-width="1.5"/>
                <path
                  d="M15.0004 12C15.0004 13.6569 13.6573 15 12.0004 15C10.3436 15 9.00042 13.6569 9.00042 12C9.00042 10.3431 10.3436 9 12.0004 9C13.6573 9 15.0004 10.3431 15.0004 12Z"
                  stroke="#10B3B7" stroke-width="1.5"/>
              </svg>
              </span>

              <span v-show="showHideSources">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M2.9095 10.8282L3.59096 11.1415H3.59096L2.9095 10.8282ZM21.0913 13.1718L20.4099 12.8585L21.0913 13.1718ZM2.9095 13.1718L2.22804 13.485H2.22804L2.9095 13.1718ZM21.0913 10.8282L20.4099 11.1415L21.0913 10.8282ZM17.832 5.9678C17.4842 5.74282 17.0199 5.84238 16.7949 6.19017C16.5699 6.53795 16.6695 7.00227 17.0173 7.22725L17.832 5.9678ZM7.02699 16.8007C6.67795 16.5776 6.21419 16.6798 5.99115 17.0288C5.76812 17.3779 5.87026 17.8416 6.2193 18.0647L7.02699 16.8007ZM3.4008 15.4515C3.64961 15.7826 4.11977 15.8494 4.45093 15.6006C4.78209 15.3518 4.84885 14.8816 4.60004 14.5505L3.4008 15.4515ZM13.8512 5.93505C14.2572 6.01745 14.653 5.75517 14.7354 5.34924C14.8178 4.9433 14.5556 4.54743 14.1496 4.46503L13.8512 5.93505ZM20.5307 4.53033C20.8236 4.23744 20.8236 3.76256 20.5307 3.46967C20.2378 3.17678 19.763 3.17678 19.4701 3.46967L20.5307 4.53033ZM3.47008 19.4697C3.17719 19.7626 3.17719 20.2374 3.47008 20.5303C3.76297 20.8232 4.23785 20.8232 4.53074 20.5303L3.47008 19.4697ZM12.0004 4.25C7.6629 4.25 3.92696 6.81889 2.22804 10.515L3.59096 11.1415C5.05425 7.95797 8.27027 5.75 12.0004 5.75V4.25ZM12.0004 19.75C16.3379 19.75 20.0739 17.1811 21.7728 13.485L20.4099 12.8585C18.9466 16.042 15.7306 18.25 12.0004 18.25V19.75ZM2.22804 10.515C1.79508 11.4569 1.79508 12.5431 2.22804 13.485L3.59096 12.8585C3.34077 12.3142 3.34077 11.6858 3.59096 11.1415L2.22804 10.515ZM20.4099 11.1415C20.6601 11.6858 20.6601 12.3142 20.4099 12.8585L21.7728 13.485C22.2058 12.5431 22.2058 11.4569 21.7728 10.515L20.4099 11.1415ZM21.7728 10.515C20.9154 8.64972 19.5404 7.07295 17.832 5.9678L17.0173 7.22725C18.4886 8.17901 19.6724 9.53708 20.4099 11.1415L21.7728 10.515ZM6.2193 18.0647C7.88909 19.1316 9.87369 19.75 12.0004 19.75V18.25C10.1683 18.25 8.46277 17.7181 7.02699 16.8007L6.2193 18.0647ZM2.22804 13.485C2.5495 14.1844 2.94368 14.8431 3.4008 15.4515L4.60004 14.5505C4.20646 14.0266 3.86735 13.4598 3.59096 12.8585L2.22804 13.485ZM14.1496 4.46503C13.4546 4.32394 12.7357 4.25 12.0004 4.25V5.75C12.635 5.75 13.2538 5.81378 13.8512 5.93505L14.1496 4.46503ZM9.34877 14.6517C10.8132 16.1161 13.1876 16.1161 14.6521 14.6517L13.5914 13.591C12.7127 14.4697 11.2881 14.4697 10.4094 13.591L9.34877 14.6517ZM14.6521 14.6517C16.1165 13.1872 16.1165 10.8128 14.6521 9.34835L13.5914 10.409C14.4701 11.2877 14.4701 12.7123 13.5914 13.591L14.6521 14.6517ZM19.4701 3.46967L3.47008 19.4697L4.53074 20.5303L20.5307 4.53033L19.4701 3.46967Z"
                  fill="#10B3B7"/>
              </svg>
              </span>

              <span>View Sources</span>
            </div>
          </el-button>
          <el-row class="display-flex w-full gap-10">
            <el-col :span="showHideSources ? 16 : 24">
              <!--prompt zoom 的文本区域-->
              <PromptTextareaEditor
                :promptTextareaStyle="promptTextareaStyle"
                ref="promptZoomPromptEditor"
                fromTemplate="promptTemplate"
                @textareaEditorMounted="initPromptTextEditor"
                from="promptZoomText" v-model="prompt.promptTemplate"/>
            </el-col>
            <el-col v-show="showHideSources" :span="8">
              <!-- prompt source list-->
              <PromptSourceList
                @clickSource="addSources"/>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="submodules[1]" :name="submodules[1]" @click="selectTextOption">
        <div class="display-flex flex-direction-col flex-justify-end align-items-end gap-10">
          <!-- 查看 Source 信息 -->
          <el-button style="position: relative; right: 10px;" type="primary" plain size="medium"
                     @click.stop="viewSources">
            <div class="display-flex justify-content btn-center">
              <span v-show="!showHideSources">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M2.9095 13.1718C2.56793 12.4286 2.56793 11.5714 2.9095 10.8282C4.4906 7.38843 7.96659 5 12.0004 5C16.0343 5 19.5102 7.38843 21.0913 10.8282C21.4329 11.5714 21.4329 12.4286 21.0913 13.1718C19.5102 16.6116 16.0343 19 12.0004 19C7.96659 19 4.4906 16.6116 2.9095 13.1718Z"
                  stroke="#10B3B7" stroke-width="1.5"/>
                <path
                  d="M15.0004 12C15.0004 13.6569 13.6573 15 12.0004 15C10.3436 15 9.00042 13.6569 9.00042 12C9.00042 10.3431 10.3436 9 12.0004 9C13.6573 9 15.0004 10.3431 15.0004 12Z"
                  stroke="#10B3B7" stroke-width="1.5"/>
              </svg>
              </span>

              <span v-show="showHideSources">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M2.9095 10.8282L3.59096 11.1415H3.59096L2.9095 10.8282ZM21.0913 13.1718L20.4099 12.8585L21.0913 13.1718ZM2.9095 13.1718L2.22804 13.485H2.22804L2.9095 13.1718ZM21.0913 10.8282L20.4099 11.1415L21.0913 10.8282ZM17.832 5.9678C17.4842 5.74282 17.0199 5.84238 16.7949 6.19017C16.5699 6.53795 16.6695 7.00227 17.0173 7.22725L17.832 5.9678ZM7.02699 16.8007C6.67795 16.5776 6.21419 16.6798 5.99115 17.0288C5.76812 17.3779 5.87026 17.8416 6.2193 18.0647L7.02699 16.8007ZM3.4008 15.4515C3.64961 15.7826 4.11977 15.8494 4.45093 15.6006C4.78209 15.3518 4.84885 14.8816 4.60004 14.5505L3.4008 15.4515ZM13.8512 5.93505C14.2572 6.01745 14.653 5.75517 14.7354 5.34924C14.8178 4.9433 14.5556 4.54743 14.1496 4.46503L13.8512 5.93505ZM20.5307 4.53033C20.8236 4.23744 20.8236 3.76256 20.5307 3.46967C20.2378 3.17678 19.763 3.17678 19.4701 3.46967L20.5307 4.53033ZM3.47008 19.4697C3.17719 19.7626 3.17719 20.2374 3.47008 20.5303C3.76297 20.8232 4.23785 20.8232 4.53074 20.5303L3.47008 19.4697ZM12.0004 4.25C7.6629 4.25 3.92696 6.81889 2.22804 10.515L3.59096 11.1415C5.05425 7.95797 8.27027 5.75 12.0004 5.75V4.25ZM12.0004 19.75C16.3379 19.75 20.0739 17.1811 21.7728 13.485L20.4099 12.8585C18.9466 16.042 15.7306 18.25 12.0004 18.25V19.75ZM2.22804 10.515C1.79508 11.4569 1.79508 12.5431 2.22804 13.485L3.59096 12.8585C3.34077 12.3142 3.34077 11.6858 3.59096 11.1415L2.22804 10.515ZM20.4099 11.1415C20.6601 11.6858 20.6601 12.3142 20.4099 12.8585L21.7728 13.485C22.2058 12.5431 22.2058 11.4569 21.7728 10.515L20.4099 11.1415ZM21.7728 10.515C20.9154 8.64972 19.5404 7.07295 17.832 5.9678L17.0173 7.22725C18.4886 8.17901 19.6724 9.53708 20.4099 11.1415L21.7728 10.515ZM6.2193 18.0647C7.88909 19.1316 9.87369 19.75 12.0004 19.75V18.25C10.1683 18.25 8.46277 17.7181 7.02699 16.8007L6.2193 18.0647ZM2.22804 13.485C2.5495 14.1844 2.94368 14.8431 3.4008 15.4515L4.60004 14.5505C4.20646 14.0266 3.86735 13.4598 3.59096 12.8585L2.22804 13.485ZM14.1496 4.46503C13.4546 4.32394 12.7357 4.25 12.0004 4.25V5.75C12.635 5.75 13.2538 5.81378 13.8512 5.93505L14.1496 4.46503ZM9.34877 14.6517C10.8132 16.1161 13.1876 16.1161 14.6521 14.6517L13.5914 13.591C12.7127 14.4697 11.2881 14.4697 10.4094 13.591L9.34877 14.6517ZM14.6521 14.6517C16.1165 13.1872 16.1165 10.8128 14.6521 9.34835L13.5914 10.409C14.4701 11.2877 14.4701 12.7123 13.5914 13.591L14.6521 14.6517ZM19.4701 3.46967L3.47008 19.4697L4.53074 20.5303L20.5307 4.53033L19.4701 3.46967Z"
                  fill="#10B3B7"/>
              </svg>
              </span>

              <span>View Sources</span>
            </div>
          </el-button>
          <el-row class="display-flex w-full gap-10">
            <el-col :span="showHideSources ? 16 : 24">
              <!--prompt zoom 的文本区域-->
              <PromptTextareaEditor
                :promptTextareaStyle="promptTextareaStyle"
                fromTemplate="evaluatePromptTemplate"
                @textareaEditorMounted="initPromptTextEditor"
                ref="promptZoomEvaluateEditor"
                from="promptZoomText" v-model="prompt.evaluatePromptTemplate"/>
            </el-col>
            <el-col v-show="showHideSources" :span="8">
              <!-- prompt source list-->
              <PromptSourceList
                @clickSource="addSources"/>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
import PromptSourceList from '@/views/curriculum/prompt/components/editor/PromptSourceList.vue';
import PromptTextareaEditor from '@/views/curriculum/prompt/components/editor/PromptTextareaEditor.vue';

export default {
  name: 'PromptZoomText',
  components: {
    PromptTextareaEditor,
    PromptSourceList
  },
  computed: {
    promptTextareaStyle() {
      return {
        maxHeight: '549px',
        height: '549px',
        width: '100%'
      }
    }
  },
  data() {
    return {
      prompt: {}, // 当前的 prompt
      showZoomDialog: false,
      leavedPage: false, // 是否离开页面
      canInitPrompt: false, // 是否可以开始初始化 prompt 的 编辑器
      zoomTextLoading: false, // 是否正在加载
      fromTemplate: '', // 来源
      showHideSources: false,
      currentTagName: 'Creation Prompt',
      submodules: ['Creation Prompt', 'Evaluation Prompt'],
      promptZoomPromptTextContent: '',
      promptZoomEvaluateTextContent: ''
    }
  },
  created() {
    // 初始化的时候不需要修改值，但是需要对方来该自己的数据
    this.initPromptListener(null, null, this.updatePromptTemplateFunc, this.updateEvaluatePromptTemplateFunc)
  },
  destroyed() {
    // 组件销毁后标记离开页面
    this.leavedPage = true
  },
  watch: {},
  methods: {
    initPromptListener(promptTemplate, evaluatePromptTemplate,
                       updatePromptTemplateFunc, updateEvaluatePromptTemplateFunc) {

      const listenerEmitterParam = {
        promptTemplate: promptTemplate,
        evaluatePromptTemplate: evaluatePromptTemplate,
        updatePromptTemplateFunc: updatePromptTemplateFunc,
        updateEvaluatePromptTemplateFunc: updateEvaluatePromptTemplateFunc
      }
      // 监听
      this.$bus.$emit('prompt_editor_sync_prompt', listenerEmitterParam)
    },
    updatePromptTemplateFunc(promptTemplate) {
      if (this.leavedPage) {
        return
      }
      this.prompt.promptTemplate = promptTemplate
      // 数据已经获取得到了，可以初始化编辑器
      if (promptTemplate) {
        this.canInitPrompt = true
        this.initPromptTextEditor()
      }
    },
    updateEvaluatePromptTemplateFunc(evaluatePromptTemplate) {
      if (this.leavedPage) {
        return
      }
      this.prompt.evaluatePromptTemplate = evaluatePromptTemplate
      // 数据已经获取得到了，可以初始化编辑器
      if (evaluatePromptTemplate) {
        this.canInitPrompt = true
        this.initPromptTextEditor()
      }
    },
    updatePromptTemplate() {
      let promptTemplate
      let evaluatePromptTemplate
      if (this.$refs.promptZoomPromptEditor) {
        promptTemplate = this.$refs.promptZoomPromptEditor.parseContents();
      }
      if (this.$refs.promptZoomEvaluateEditor) {
        evaluatePromptTemplate = this.$refs.promptZoomEvaluateEditor.parseContents();
      }
      // 更新 prompt
      this.initPromptListener(promptTemplate, evaluatePromptTemplate, null, null)
    },
    initPromptTextEditor() {
      let timeout = setInterval(() => {
        if (this.canInitPrompt) {
          // 清除定时器
          clearInterval(timeout)
          // 通知编辑器进行初始化
          this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
          // 初始化数据
          if (this.fromTemplate) {
            if (this.fromTemplate === 'promptTemplate') {
              this.currentTagName = this.submodules[0]
              this.$refs.promptZoomPromptEditor && this.$refs.promptZoomPromptEditor.initPromptTextEditor(this.prompt.promptTemplate)
            } else if (this.fromTemplate === 'evaluatePromptTemplate') {
              this.currentTagName = this.submodules[1]
              this.$refs.promptZoomEvaluateEditor && this.$refs.promptZoomEvaluateEditor.initPromptTextEditor(this.prompt.evaluatePromptTemplate)
            }
          }
          // 编辑器进行初始化成功
          this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', true)
          // loading 结束
          this.zoomTextLoading = false
        } else {
          // 数据还没有准备好，等待
          // 初始化的时候不需要修改值，但是需要对方来该自己的数据
          this.initPromptListener(null, null, this.updatePromptTemplateFunc, this.updateEvaluatePromptTemplateFunc)
        }
      }, 1000)
    },
    openZoomDialog(fromTemplate) {
      this.leavedPage = false
      this.fromTemplate = fromTemplate
      // 如果数据准备好了，可以开始初始化了
      // 数据渲染完成之后再打开
      this.showZoomDialog = true
      // 开始 loading
      this.zoomTextLoading = true
      // 初始化的时候不需要修改值，但是需要对方来该自己的数据
      this.initPromptListener(null, null, this.updatePromptTemplateFunc, this.updateEvaluatePromptTemplateFunc)
    },
    switchType(val) {
      switch (val.name) {
        case this.submodules[0]:
          this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
          this.$refs.promptZoomPromptEditor && this.$refs.promptZoomPromptEditor.initPromptTextEditor(this.prompt.promptTemplate)
          break
        case this.submodules[1]:
          this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
          this.$refs.promptZoomEvaluateEditor && this.$refs.promptZoomEvaluateEditor.initPromptTextEditor(this.prompt.evaluatePromptTemplate)
          break
        default:
          break
      }
      // 编辑器进行初始化成功
      this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', true)
    },
    selectTextOption() {
      this.$emit('select-text-option', this.currentTagName)
    },
    viewSources() {
      this.showHideSources = !this.showHideSources
    },
    // 添加资源
    addSources(source) {
      // 添加资源的时候是出现一个浮动框，类似于 facebook 中的表情包，再编辑框上方浮动出来一个框，里面有一些资源，可以点击添加到编辑框中
      let fromTemplate = this.fromTemplate
      if (this.currentTagName === this.submodules[0]) {
        fromTemplate = 'promptTemplate'
      } else if (this.currentTagName === this.submodules[1]) {
        fromTemplate = 'evaluatePromptTemplate'
      }
      // 向光标的位置添加资源
      this.$bus.$emit('addSources', {
        fromTemplate: fromTemplate,
        from: 'promptZoomText',
        source: source
      })
    }
  }
}
</script>

<style scoped lang="less">
.prompt-textarea {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 5px 15px;
  line-height: 22px;
  color: #606266;
}

/deep/ .ai-assistant-tab {

  border-radius: 4px;
  background: #ffffff;
  border: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;

  .el-tabs__header {
    border: none !important;
    margin: 0 !important;
  }

  .el-tabs__nav {
    padding: 6px !important;
    border-radius: 4px !important;
    gap: 6px;
    display: inline-flex;
    border: none !important;
    width: 100% !important;
    background: var(--color-primary-light, #ddf2f3);
    justify-content: center;
    align-items: center;
  }

  .el-tabs__item.is-active {
    color: #ffffff !important;
    background: var(--color-primary, #10B3B7);
    font-weight: 600;
    border-radius: 4px;
  }

  .el-tabs__item {
    color: var(--color-primary, #10B3B7);
    font-weight: 400;
    font-size: 16px;
    height: 32px !important;
    line-height: 22px !important;
    padding: 0 12px !important;
    border-width: 0 !important;
    width: 50% !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .el-tabs__content {
    padding: 5px 0 !important;
    width: 100%;
  }
}

/deep/ .el-dialog__body {
  padding-top: 0px;
}

.variable-text {
  color: #5e6d82;
  background-color: #f9fafc;
  padding: 0px 4px;
  border: 1px solid #eaeefb;
  border-radius: 4px;
  margin: 0 4px;
  cursor: pointer;
}

/deep/ .prompt-text-editor-textarea {
  border-radius: 8px;
  border: 1px solid var(--color-inner-dashed-border);
}

/deep/ .source-layout {
  border-radius: 8px;
  border: 1px solid var(--color-inner-dashed-border);
  height: 549px;
  min-height: 564px;
  padding: 5px;
}

.dialog-vertical {
  display: flex;
  padding: 20px;
  ::v-deep .el-dialog {
    height: 90vh;
    width: 90%;
    margin: auto !important;
    display: flex;
    flex-direction: column;
    .el-dialog__body {
      flex: auto;
      min-height: 0;
      display: flex;
      flex-direction: column;
      .no-shrink {
        flex: auto;
        min-height: 0;
      }
    }
  }
}
</style>