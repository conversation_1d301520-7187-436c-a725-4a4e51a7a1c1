<template>
  <!-- 功能栏 -->
  <div class="display-flex flex-justify-end align-items gap-12">
    <el-popover
      placement="right"
      width="400"
      ref="promptSourceList"
      trigger="click">
      <!-- prompt source list-->
      <PromptSourceList
        :contents="contents"
        @clickSource="addSources"/>
      <!-- Add sources 按钮 -->
      <el-button type="text"
                 slot="reference">
        <div class="lg-icon align-items display-flex flex-nowrap justify-content-between add-sources">
          <div class="add-sources-text lg-icon-add">Add Sources</div>
        </div>
      </el-button>
    </el-popover>
    <!-- 将文本放大的按钮 -->
    <el-tooltip content="Expand window" effect="dark" placement="bottom">
      <el-button type="text" @click="zoomIn" class="lg-icon">
        <svg class="icons" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.0833 19.25H2.75V11.9166" stroke="#676879" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round"/>
          <path d="M11.9166 2.75H19.25V10.0833" stroke="#676879" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round"/>
        </svg>
      </el-button>
    </el-tooltip>
    <PromptZoomText ref="promptZoomText"/>
  </div>
</template>
<script>
import PromptSourceList from './PromptSourceList.vue'
import PromptZoomText from './PromptZoomText.vue'

export default {
  name: 'PromptTextareaToolbar',
  components: {
    PromptSourceList,
    PromptZoomText
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    contents: {
      type: Array,
      default: () => []
    },
    fromTemplate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showZoomDialog: false // 是否显示放大的对话框
    }
  },
  methods: {
    // 添加资源
    addSources(source) {
      // 关闭 promptSourceList
      this.$refs.promptSourceList.doClose()
      // 添加资源的时候是出现一个浮动框，类似于 facebook 中的表情包，再编辑框上方浮动出来一个框，里面有一些资源，可以点击添加到编辑框中
      // 向光标的位置添加资源
      this.$bus.$emit('addSources', {
          fromTemplate: this.fromTemplate,
          from: 'promptTextareaToolbar',
          source: source
      })
    },
    // 放大
    zoomIn() {
      this.$refs.promptZoomText.openZoomDialog(this.fromTemplate)
    }
  }
}
</script>

<style scoped lang="less">
.add-sources {
  height: 30px;
  font-size: 14px;
  text-align: center;
  padding: 8px;
  gap: 4px;
  border-radius: 25px;
  background: var(--color-primary-light);
  color: var(--color-text-placeholder);
}

/deep/ .el-button--text {
  padding: 0 !important;
  color: var(--color-text-primary) !important;
}

.lg-icon .icons path {
  transition: stroke 0.3s; /* 添加过渡效果 */
}

.lg-icon:hover .icons path {
  stroke: #10b3b7; /* 设置悬浮时的颜色 */
}

.lg-icon:hover .add-sources-text {
  color: #10b3b7;
}
</style>