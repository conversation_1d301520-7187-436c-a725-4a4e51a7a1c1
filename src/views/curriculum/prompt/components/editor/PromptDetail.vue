<template>
    <div class="display-flex h-full">
        <!-- Prompt 详情 -->
        <div class="prompt-editor h-full">
            <PromptEditor
                ref="promptEditorRef"
                :promptId="promptId"
                :showGenerateBtn="true"
                @updatePromptId="updatePromptId"
                @batchGenerateResultsSuccess="batchGenerateResultsSuccess"
            />
        </div>
        <!-- 测试结果 -->
        <div class="flex-auto">
            <!-- 切换按钮 -->
            <!-- <div class="lg-tabs-small m-b-sm" v-show="false">
                <el-tabs v-model="currentView">
                    <el-tab-pane label="Unit Info" name="RESULT"></el-tab-pane>
                    <el-tab-pane label="Statistics Validation" name="STATS_RESULT"></el-tab-pane>
                </el-tabs>
            </div> -->
            <!-- 单条结果 -->
            <div v-show="currentView === 'RESULT'">
            </div>
            <!-- 统计列表 -->
            <PromptStatsResult
                class="m-l-md"
                v-show="currentView === 'STATS_RESULT'"
                ref="promptStatsResultRef"
                :promptId="promptId"
                :testId="testId"
                @testCompleted="testCompleted"
                @testIncomplete="testIncomplete"
            />
        </div>
    </div>
</template>

<script>
import PromptEditor from '@/views/curriculum/prompt/components/editor/PromptEditor'
import PromptStatsResult from '@/views/curriculum/prompt/components/editor/PromptStatsResult.vue'

export default {
    props: {
        // Prompt ID
        promptId: {
            type: String,
            default: null,
        },
    },

    components: {
        PromptEditor, // Prompt 编辑器
        PromptStatsResult, // Prompt 统计结果
    },

    data() {
        return {
            testId: null, // 测试 ID
            currentView: 'STATS_RESULT', // 当前视图
        }
    },

    created() {
        console.log('init.....')
    },

    methods: {
        // 批量生成结果成功
        batchGenerateResultsSuccess(result) {
            // 更新测试记录 ID
            this.testId = result.testRecordId
            // 获取测试结果
            this.$refs.promptStatsResultRef.getTestResults(true)
        },

        updatePromptId(promptId) {
            this.$emit('updatePromptId', promptId)
        },

        // 测试完成
        testCompleted() {
            // 获取测试结果
            this.$refs.promptEditorRef.testCompleted()
        },

        // 测试未完成
        testIncomplete() {
            // 获取测试结果
            this.$refs.promptEditorRef.testIncomplete()
        },
    },
}
</script>

<style lang="less" scoped>
.prompt-editor {
    width: 40%;
    flex-shrink: 0;
    padding-right: 20px;
    overflow-y: auto;
}
</style>