<template>
    <div v-loading="getPromptLoading">
        <!-- 内容 -->
        <div>
            <!-- 切换场景按钮 -->
            <div class="lg-tabs-small m-b-sm prompt-editor-tab" v-show="promptScenes.length > 1">
                <el-tabs v-model="currentScene" style="width: calc(100% - 30px)" @tab-remove="handleTabsEdit">
                    <el-tab-pane :label="sceneName(scene)"
                                 :name="scene"
                                 v-for="(scene, index) in promptScenes"
                                 :key="index"
                                 :closable="canCloseScene(scene)"
                    ></el-tab-pane>
                </el-tabs>
                <el-button v-if="canAddTab" class="h-full lg-icon" type="text" @click="addNewPrompt">
                    <svg class="icons" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M12.25 11.25V11.75H12.75H16.25C16.3881 11.75 16.5 11.8619 16.5 12C16.5 12.1381 16.3881 12.25 16.25 12.25H12.75H12.25V12.75V16.25C12.25 16.3881 12.1381 16.5 12 16.5C11.8619 16.5 11.75 16.3881 11.75 16.25V12.75V12.25H11.25H7.75C7.61194 12.25 7.5 12.1381 7.5 12C7.5 11.8619 7.61194 11.75 7.75 11.75H11.25H11.75V11.25V7.75C11.75 7.61194 11.8619 7.5 12 7.5C12.1381 7.5 12.25 7.61194 12.25 7.75V11.25ZM3.5 6.25C3.5 4.73121 4.73121 3.5 6.25 3.5H17.75C19.2688 3.5 20.5 4.73122 20.5 6.25V17.75C20.5 19.2688 19.2688 20.5 17.75 20.5H6.25C4.73122 20.5 3.5 19.2688 3.5 17.75V6.25ZM6.25 4C5.00736 4 4 5.00736 4 6.25V17.75C4 18.9926 5.00736 20 6.25 20H17.75C18.9926 20 20 18.9926 20 17.75V6.25C20 5.00736 18.9926 4 17.75 4H6.25Z"
                            fill="#676879" stroke="#676879"/>
                    </svg>
                </el-button>
            </div>
            <el-form
                ref="promptFormRef"
                label-position="top"
                label-width="100px"
                :model="prompt"
                :rules="promptFormRules"
            >
                <!-- 模型参数 -->
                <div class="display-flex">
                    <!-- 模型, 如果 scenes 大于 1 的时候 model 放在下面，否则放在上面 -->
                    <el-form-item v-if="promptScenes.length > 1" label="Model" prop="model" class="m-r-xs w-full">
                        <el-select v-model="prompt.model" filterable placeholder="Please Select" class="w-full">
                            <el-option v-for="(model, index) in models" :key="index" :label="model.name"
                                       :value="model.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 温度 -->
                    <el-form-item label="Temperature" prop="temperature" class="m-r-xs w-full">
                        <el-input v-model="prompt.temperature" type="number" step="0.1"/>
                    </el-form-item>
                    <!-- 版本 -->
                    <el-form-item label="Version" prop="version" class="w-full">
                        <el-input v-model="prompt.version" :readonly="true"/>
                    </el-form-item>
                </div>
                <!-- 提示 去除 -->
                <!--<div class="m-b-sm">-->
                <!--    <span class="text-danger">-->
                <!--        Note: the bold red text in the prompt represents fixed or dynamically changing modules and cannot be modified at the moment.-->
                <!--    </span>-->
                <!--</div>-->
                <!-- Prompt 模板 -->
                <el-form-item prop="promptTemplate">
                    <CollapseCard :theme="'textarea-card'" :shadow="'never'">
                        <!-- 标题 -->
                        <template slot="header">
                            <span class="section-title">Creation Prompt</span>
                        </template>
                        <!-- 内容 -->
                        <PromptTextarea ref="promptTemplate" fromTemplate="promptTemplate"
                                        :value="prompt.promptTemplate"/>
                    </CollapseCard>
                </el-form-item>
                <!-- 评价 Prompt 模板 -->
                <el-form-item prop="evaluatePromptTemplate">
                    <!--添加默认折叠-->
                    <CollapseCard :theme="'textarea-card'" :collapse="true" :shadow="'never'">
                        <!-- 标题 -->
                        <template slot="header">
                            <span class="section-title">Evaluation Prompt</span>
                        </template>
                        <!-- 内容 -->
                        <PromptTextarea ref="evaluatePromptTemplate" fromTemplate="evaluatePromptTemplate"
                                        :value="prompt.evaluatePromptTemplate"/>
                    </CollapseCard>
                </el-form-item>
            </el-form>
            <!-- 操作 -->
            <div class="display-flex justify-content-between" v-show="showUpdatePromptBtn">
                <div>
                    <!-- 重置 -->
                    <el-button type="default" size="medium" @click="resetPrompt" icon="el-icon-refresh-left"
                               :disabled="!canReset">Reset
                    </el-button>
                    <!-- 版本列表 -->
                    <el-button type="" size="medium" @click="openPromptListDialog" icon="el-icon-coin">All versions
                    </el-button>
                </div>
                <div>
                    <!--保存为草稿-->
                    <el-button type="primary" plain size="medium" @click="createPromptVersion(true)"
                               :loading="savePromptLoading">Save as draft
                    </el-button>
                    <!-- 保存 -->
                    <el-button type="primary" plain size="medium" @click="createPromptVersion(false)"
                               :loading="savePromptLoading">Apply
                    </el-button>
                </div>
            </div>
            <!-- 生成按钮 -->
            <div v-show="showGenerateBtn" class="m-t-sm">
                <!-- 单个生成 -->
                <div>
                    <el-button type="primary" class="w-full btn-primary-gradient"
                               @click="generateResult"
                               :loading="(batchGenerateResultsLoading && !generateFun) || generateResultLoading">
                        Generate
                    </el-button>
                </div>
                <!-- 批量测试 -->
                <div class="display-flex m-t-sm" v-show="prompt.evaluatePromptTemplate">
                    <!-- 选择次数 -->
                    <div class="display-flex m-r-sm justify-content-between align-items flex-auto">
                        <div class="data-trial-text">Data Trials</div>
                        <div>
                            <el-select v-model="testCount" placeholder="Please select">
                                <el-option :label="5" :value="5"></el-option>
                                <el-option :label="10" :value="10"></el-option>
                                <el-option :label="30" :value="30"></el-option>
                                <el-option :label="50" :value="50"></el-option>
                            </el-select>
                        </div>
                    </div>
                    <!-- 测试按钮 -->
                    <el-button type="primary" class="w-full" @click="batchGenerateResults"
                               :loading="batchGenerateResultsLoading">Statistics Validation
                    </el-button>
                </div>
            </div>
            <!-- 添加新的 prompt   -->
            <AddNewPrompt ref="addNewPrompt"
                          @saveNewPrompt="updateNewPrompt"></AddNewPrompt>
        </div>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {bus} from '@/utils/bus'
import CollapseCard from '@/views/curriculum/components/card/CollapseCard.vue'
import PromptTextarea from './PromptTextarea.vue'
import AddNewPrompt from './AddNewPrompt.vue'
import {equalsIgnoreCase} from "@/utils/common";

export default {
    props: {
        // Prompt 场景
        scenes: {
            type: Array,
            default: () => [],
        },

        // Prompt ID
        promptId: {
            type: String,
            default: null,
        },

        // 是否显示更新 Prompt 和版本列表按钮
        showUpdatePromptBtn: {
            type: Boolean,
            default: false,
        },

        // 是否显示生成按钮
        showGenerateBtn: {
            type: Boolean,
            default: false,
        },

        // 生成内容函数
        generateFun: {
            type: Function,
            default: null,
        },

        // 是否需要获取自定义的 prompt
        needGetCustomPrompt: {
          type: Boolean,
          default: true,
        },
        // 是否可以添加场景
        canAddTab: {
            type: Boolean,
            default: true,
        }
    },

    components: {
        CollapseCard, // 折叠卡片
        PromptTextarea, // Prompt 编辑框
        AddNewPrompt // 添加新的 Prompt
    },

    data() {
        return {
            currentScene: null, // 当前场景
            prompt: {}, // Prompt 信息
            promptFormRules: {}, // Prompt 表单校验规则
            getPromptLoading: false, // 获取 Prompt 详情 Loading
            savePromptLoading: false, // 保存 Prompt Loading
            testCount: 5, // 测试次数
            generateResultLoading: false, // 生成结果 Loading
            batchGenerateResultsLoading: false, // 批量生成结果 Loading
            generateParams: {}, // 生成参数
            defaultPromptId: null, // 默认 Prompt ID
            promptScenes: [], // Prompt 场景
            customizePrompt: [], // 客户自定义的 Prompt
        }
    },

    created() {
        // 默认场景
        if (this.scenes.length > 0) {
            this.currentScene = this.scenes[0]
        }
        // 初始化表单验证
        this.initPromptFormRules()
        // 初始化 Prompt，获取和设置，进行事件监听
        this.initPromptListener()
    },

    computed: {
        // 全局信息
        ...mapState({
            models: state => state.curriculum.models, // 模型列表
            systemTabs: state => state.curriculum.systemTabs, // 系统场景
            promptCustomDetailHasUpdated: state => state.curriculum.promptCustomDetailHasUpdated, // prompt 自定义详情是否更新
            currentModel: state => state.curriculum.currentModel, // 当前模型
            promptExtensionParams: state => state.curriculum.promptExtensionParams, // 当前模型
        }),
        editorInitialized: {
            get() {
                return this.$store.state.curriculum.editorInitialized
            },
            set(value) {
                this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', value)
            }
        },
        // Prompt 模板 Html 格式内容
        promptTemplateHtml() {
            if (!this.prompt || !this.prompt.promptTemplate) {
                return
            }
            return this.prompt.promptTemplate.replace(/\n/g, '<br>').replace(/\{\{/g, '<p class="text-danger">{{').replace(/\}\}/g, '}}</p>')
        },
        // 场景对应的名称
        sceneName() {
            return function (scene) {
                switch (scene) {
                    case 'LESSON_DETAIL':
                        return 'Lesson Plan'
                    case 'TYPICAL_BEHAVIOR':
                        return 'Typical Behaviors'
                    case 'UNIVERSAL_DESIGN_FOR_LEARNING':
                        return 'UDL'
                    case 'CULTURALLY_RESPONSIVE_INSTRUCTION':
                        return 'Culturally Responsive Practice'
                    case 'HOME_ACTIVITY':
                        return 'At-Home Activities'
                    case 'UNIT_OVERVIEW':
                        return 'Unit Foundations'
                    case 'PLAN_OVERVIEW':
                        return 'Weekly Theme'
                    case 'LESSON_OVERVIEW':
                        return 'Lesson Plan Ideas'
                    case 'DEI_FOR_TEACHER':
                        return 'DEI - Teachers'
                    case 'DEI_FOR_ADMINISTRATORS':
                        return 'DEI - Admins'
                    case 'DEI_FOR_ALL':
                        return 'DEI - All Staff'
                    default:
                        let findScene = this.customizePrompt.find(item => item.scene === scene)
                        if (findScene) {
                            return findScene.name
                        }
                        return scene
                }
            }
        },

        canCloseScene() {
            // 不能关闭的场景
            let notCanCloseScenes = this.systemTabs
            return function (scene) {
                return notCanCloseScenes.findIndex(item => equalsIgnoreCase(item, scene)) < 0
            }
        },

        // 是否可以重置
        canReset() {
            return this.prompt && this.defaultPromptId && this.prompt.id !== this.defaultPromptId
        },
    },

    watch: {
        // 监听场景变化
        currentScene: {
            handler: function (val, oldVal) {
                if (val && val !== '0') {
                    this.initPrompt()
                    // 场景发生了变化，将值赋值给 currentTab
                    this.$store.dispatch('curriculum/setCurrentTab', this.sceneName(val))
                    this.$store.dispatch('curriculum/setCurrentScene', val)
                }
            },
            immediate: true,
        },
        editorInitialized: {
            handler: function (val, oldVal) {
                if (!val) {
                    this.$forceUpdate()
                }
            }
        },
        // 监听 Prompt ID 变化
        promptId: {
            handler: function (val, oldVal) {
                if (val) {
                    this.initPrompt()
                    this.$store.commit('curriculum/SET_CURRENT_PROMPT_ID', val)
                }
            },
            immediate: true,
        },
        prompt: {
            // 如果 prompt 中的 model 发生了变化，则更新 store 中的当前模型
            handler: function (val, oldVal) {
                if (val && val.model) {
                    // 从 model 中获取当前模型
                    let currentModel = this.models.find(item => item.value === val.model);
                    if (currentModel && currentModel !== this.currentModel) {
                        this.$store.dispatch('curriculum/setCurrentModel', currentModel)
                    }
                }
                // 保存 prompt 的值
                this.$store.commit('curriculum/SET_PROMPT', val)
            },
            deep: true
        },
        currentModel: {
            // 如果 currentModel 发生了变化，则更新 prompt 中的 model
            handler: function (val, oldVal) {
                this.prompt.model = val.value
            },
            immediate: true,
        },
        scenes: {
            // 监听场景变化
            handler: function (val, oldVal) {
                if (val && val.length > 0 && this.promptScenes.length === 0) {
                    this.promptScenes = val
                    // 如果 val 的长度大于 1 的时候，获取所有的自定义的 prompt
                    if (val.length > 1 && this.needGetCustomPrompt) {
                        // 获取所有的自定义的 prompt
                        // this.getCustomizePrompt().then((res) => {
                        //     this.customizePrompt = res.items
                        //     this.promptScenes = val.concat(this.customizePrompt.map(item => item.scene))
                        //     // 保存 customizePrompt 的值
                        //     this.$store.commit('curriculum/SET_CUSTOMIZE_PROMPT', res.items)
                        // })
                    }
                }
            },
            immediate: true,
        },
        promptCustomDetailHasUpdated: {
            handler: function (val, oldVal) {
                if (val && this.needGetCustomPrompt) {
                    // 重新获取自定义的 prompt，并重新设置 currentScene
                    // this.getCustomizePrompt().then((res) => {
                    //     this.customizePrompt = res.items
                    //     this.promptScenes = this.scenes.concat(this.customizePrompt.map(item => item.scene))
                    //     // 将 currentScene 设置为第一个
                    //     this.currentScene = this.promptScenes[0]
                    //     // 保存 customizePrompt 的值
                    //     this.$store.commit('curriculum/SET_CUSTOMIZE_PROMPT', res.items)
                    // })
                    // this.$store.commit('curriculum/SET_PROMPT_CUSTOM_DETAIL_HAS_UPDATED', false)
                }
            },
            immediate: true,
        }
    },

    beforeDestroy() {
        // 关闭事件
        bus.$off('closePromptListDialog')
    },

    methods: {
        // 初始化 Prompt 监听
        initPromptListener() {
            // 监听
            this.$bus.$on('prompt_editor_sync_prompt', ({
                                                            promptTemplate, evaluatePromptTemplate,
                                                            updatePromptTemplateFunc, updateEvaluatePromptTemplateFunc
                                                        }) => {
                // 更新编辑器内容
                this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
                // 更新 Prompt 模板
                if (promptTemplate) {
                    this.prompt.promptTemplate = promptTemplate
                }
                // 更新评估 Prompt 模板
                if (evaluatePromptTemplate) {
                    this.prompt.evaluatePromptTemplate = evaluatePromptTemplate
                }
                // 更新对方的数据
                if (updatePromptTemplateFunc) {
                    const {promptContent} = this.updatePromptContent(true)
                    updatePromptTemplateFunc(promptContent)
                }
                if (updateEvaluatePromptTemplateFunc) {
                    const {evaluatePromptContent} = this.updatePromptContent(true)
                    updateEvaluatePromptTemplateFunc(evaluatePromptContent)
                }
            })
        },
        // 获取自定义的 prompt
        getCustomizePrompt() {
            // return this.$axios.get($api.urls().getCustomizePrompt)
        },
        // 初始化表单验证
        initPromptFormRules() {
            // 温度参数校验，介于 0 - 1
            let validateTemperature = (rule, value, callback) => {
                if (!value) {
                    callback(new Error('Please enter temperature!'))
                    return
                }
                console.log(value)
                // 转换为数字
                value = parseFloat(value)
                if (!value && value !== 0) {
                    callback(new Error('Please enter an integer number!'))
                } else if (value < 0 || value > 1) {
                    callback(new Error('Please enter a number between 0 and 1!'))
                } else {
                    callback()
                }
            }
            // Prompt 模板校验
            let validatePromptTemplate = (rule, value, callback) => {
                if (!value) {
                    callback(new Error('Please enter creation prompt!'))
                    return
                }
                callback()
            }
            // 评估 Prompt 模板校验
            let validateEvaluatePromptTemplate = (rule, value, callback) => {
                console.log('0-0000', value)
                if (!value) {
                    console.log('1-1111')
                    callback(new Error('Please enter evaluation prompt!'))
                    return
                }
                callback()
            }
            this.promptFormRules = {
                model: [
                    {required: true, message: 'Please select model', trigger: 'change'},
                ],
                temperature: [
                    {required: true, message: 'Please enter temperature', trigger: 'blur'},
                    {validator: validateTemperature, trigger: 'blur'}
                ],
                promptTemplate: [
                    {validator: validatePromptTemplate, trigger: 'blur'},
                ],
                // evaluatePromptTemplate: [
                //     { validator: validateEvaluatePromptTemplate, trigger: 'blur' },
                // ],
            }
        },
        // 添加新的 Prompt
        addNewPrompt() {
            this.$refs.addNewPrompt.showAddNewPrompt = true
        },
        updateCustomizePrompt(newPrompt) {
            // 新的 scene 也是 custom 的，需要设置给 customizePrompt
            let findScene = this.customizePrompt.find(item => item.scene === newPrompt.scene)
            // 如果没有找到直接就放到 customizePrompt 中
            if (!findScene) {
                this.customizePrompt.push(newPrompt)
                this.$store.commit('curriculum/SET_CUSTOMIZE_PROMPT', this.customizePrompt)
            }
        },
        // 弹窗回调保存新的 Prompt
        async updateNewPrompt(newPromptId) {
            // 开始 Loading
            this.getPromptLoading = true
            // 获取新的 prompt
            await this.getPromptById(newPromptId).then(res => {
                this.$set(this, 'prompt', res)
                // 通知编辑器进行初始化
                this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
                this.getPromptLoading = false
                // 更新 Prompt ID
                let promptId = this.prompt.id
                this.$emit('updatePromptId', promptId)
                // 设置默认 Prompt ID
                if (!this.defaultPromptId) {
                    this.defaultPromptId = promptId
                }
                // 获取当前场景
                let scene = this.prompt.scene
                // 添加新的 prompt 场景，响应式的添加
                let promptScenes = this.promptScenes
                // 如果没有找到，就添加
                if (promptScenes.indexOf(scene) < 0) {
                    promptScenes.push(scene)
                    this.promptScenes = promptScenes
                }
                // 更新自定义场景
                this.updateCustomizePrompt(res)
                // 设置当前场景
                this.$set(this, 'currentScene', this.prompt.scene)
                let eventName = scene.toLowerCase() + ':generateParams'
                // 触发事件
                bus.$emit(eventName, (params) => {
                    // 获取生成参数
                    if (params) {
                        this.generateParams = params
                    }
                })
            }).catch(error => {
                this.getPromptLoading = false
                console.log(error)
                this.$message.error(error.response.data.error_message)
            })
        },
        // 切换场景

        handleTabsEdit(targetName) {
            // 弹出确认框
            this.$confirm(this.$createElement('div', {
                domProps: {
                    innerHTML: 'Do you want to delete this module? \n' +
                        '<div style="color: var(--color-danger)">All generated content and the history of prompt adjustments will be cleared and cannot be restored.</div>'
                }
                }),
                'Confirmation', {
                    confirmButtonText: 'Delete',
                    confirmButtonClass: 'el-button--danger',
                    cancelButtonText: 'Cancel'
                }).then(() => {
                // 删除 Prompt
                this.$axios.post($api.urls().deletePrompt, {id: this.prompt.id}).then(res => {
                    this.$message.success('Prompt deleted successfully')
                    // 关闭弹窗，要求更换新的 prompt
                    this.$store.commit('curriculum/SET_PROMPT_CUSTOM_DETAIL_HAS_UPDATED', true)
                }).catch(error => {
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                })
            }).catch(() => {

            })
        },

        // 初始化 Prompt 信息
        async initPrompt() {
            // 开始 Loading
            this.getPromptLoading = true
            // 获取 Prompt 详情
            await this.getPrompt().then(res => {
                this.prompt = res
                // 通知编辑器进行初始化
                this.$store.commit('curriculum/SET_EDITOR_INITIALIZED', false)
                this.getPromptLoading = false
                // 更新 Prompt ID
                let promptId = this.prompt.id
                this.$emit('updatePromptId', promptId)
                // 设置默认 Prompt ID
                if (!this.defaultPromptId) {
                    this.defaultPromptId = promptId
                }
                // 获取当前场景
                let scene = this.prompt.scene
                let eventName = scene.toLowerCase() + ':generateParams'
                // 触发事件
                bus.$emit(eventName, (params) => {
                    // 获取生成参数
                    if (params) {
                        this.generateParams = params
                    }
                })
            }).catch(error => {
                this.getPromptLoading = false
                console.log(error)
                this.$message.error(error.response.data.error_message)
            })
        },

        // 获取 Prompt 详情
        getPrompt() {
            // 通过 ID 获取
            if (this.promptId) {
                return this.getPromptById(this.promptId)
            }
            // 通过场景获取
            if (this.currentScene) {
                return this.getPromptByScene(this.currentScene)
            }
        },

        // 根据 ID 获取 Prompt
        getPromptById(promptId) {
            return new Promise((resolve, reject) => {
                this.$axios.get($api.urls().getPrompt, {params: {promptId: promptId}}).then((res) => {
                    resolve(res)
                    // 更新数据
                    this.updatePromptEditorContent()
                }).catch((err) => {
                    reject(err)
                })
            })
        },
        // 预处理场景请求参数
        preProcessSceneRequestParam(promptRequestParam) {
            // 获取参数
            let params = promptRequestParam.params
            // 获取场景
            let promptExtensionParams = this.promptExtensionParams
            // 循环遍历 promptRequestParam 所有的属性
            for (let key in promptExtensionParams) {
                // 为 params 赋值
                if (promptExtensionParams && promptExtensionParams[key]) {
                    params[key] = promptExtensionParams[key]
                }
            }
            // 返回参数
            return {
                params: params
            }
        },

        // 根据场景获取 Prompt
        getPromptByScene(scene) {
            // 定义请求参数
            // 对原有的 scene 进行预处理
            const promptRequestParam = this.preProcessSceneRequestParam({params: {scene: scene}})
            // 调用接口获取 Prompt
            return new Promise((resolve, reject) => {
                this.$axios.get($api.urls().getPromptByScene, promptRequestParam).then((res) => {
                    resolve(res)
                    // 更新数据
                    this.updatePromptEditorContent()
                }).catch((err) => {
                    reject(err)
                })
            })
        },

        // 保存 Prompt 草稿
        createPromptDraftVersion() {
            // 更新 Prompt 内容
            this.updatePromptContent()
            this.$refs.promptFormRef.validate(valid => {
                if (valid) {
                    // 更新 Prompt
                    this.createPromptVersionRequest(true)
                }
            })
        },
        // 保存 Prompt
        createPromptVersion(draft) {
            // 更新 Prompt 内容
            this.updatePromptContent()
            this.$refs.promptFormRef.validate(valid => {
                if (valid) {
                    // 更新 Prompt
                    this.createPromptVersionRequest(draft)
                }
            })
        },

        // 创建 Prompt 版本
        createPromptVersionRequest(draft) {
            // 开始 Loading
            this.savePromptLoading = true
            // 请求参数
            let params = {
                ...this.prompt,
                draft: draft, // 是否是草稿
                createSource: 'USER', // 创建来源
            }
            // 请求接口
            this.$axios.post($api.urls().createPromptVersion, params).then(res => {
                this.prompt.id = res.id
                if (res.version) {
                    this.prompt.version = res.version
                }
                this.$emit('updatePromptId', res.id)
                this.$emit('createPromptVersionSuccess', res)
                this.savePromptLoading = false
            }).catch(error => {
                console.log(error)
                this.$message.error(error.response.data.error_message)
                this.savePromptLoading = false
            })
        },

        // 更新 Prompt 内容并保存
        async updatePromptContentAndCreatePromptVersionPromise(draft) {
            this.updatePromptContent()
            return this.createPromptVersionPromise(draft)
        },
        // 保存 Prompt
        async createPromptVersionPromise(draft) {
            // 开始 Loading
            this.savePromptLoading = true
            // 请求参数
            let params = {
                ...this.prompt,
                draft: draft, // 是否是草稿
                createSource: 'USER', // 创建来源
            }
            return new Promise((resolve, reject) => {
                this.$refs.promptFormRef.validate(valid => {
                    if (valid) {
                        // 请求接口
                        this.$axios.post($api.urls().createPromptVersion, params).then(res => {
                            resolve(res)
                            this.prompt.id = res.id
                            this.$emit('updatePromptId', res.id)
                            this.savePromptLoading = false
                        }).catch(error => {
                            reject(error)
                            console.log(error)
                            this.$message.error(error.response.data.error_message)
                            this.savePromptLoading = false
                        })
                    } else {
                        reject()
                        this.savePromptLoading = false
                    }
                })

            })
        },

        // 打开 Prompt 版本列表弹窗
        openPromptListDialog() {
            this.$emit('openPromptListDialog')
        },

        // 内容修改
        onEditorChange(data) {
            if (!data || !data.html || !data.text) {
                return
            }
            console.log(data)
            // this.prompt.promptTemplate = data.text
        },

        // 重置 Prompt
        async resetPrompt() {
            if (!this.defaultPromptId) {
                return
            }
            // 切换到默认 Prompt
            await this.setDefaultPrompt(this.defaultPromptId)
            // 重新加载 Prompt
            this.initPrompt()
        },

        // 设置为默认 Prompt
        setDefaultPrompt(promptId) {
            return new Promise((resolve, reject) => {
                // 请求接口
                this.$axios.post($api.urls().setUserCurrentPrompt, {
                    id: promptId, // Prompt ID
                    scene: this.currentScene, // Prompt 场景
                }).then(() => {
                    // 成功提示
                    this.$message.success('Prompt has been successfully reset.')
                    resolve()
                }).catch(error => {
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },

        // 生成结果
        async generateResult() {
            try {
                await this.updatePromptContentAndCreatePromptVersionPromise(true)
            } catch (error) {
                // 表单校验或添加失败
                console.log(error)
                return
            }
            if (this.generateFun) {
                // 开始 Loading
                this.generateResultLoading = true
                this.batchGenerateResultsLoading = true
                this.generateFun(() => {
                    this.generateResultLoading = false
                    this.batchGenerateResultsLoading = false
                    // 获取当前场景
                    let scene = this.prompt.scene
                    let eventName = scene.toLowerCase() + ':generateParams'
                    // 触发事件
                    bus.$emit(eventName, (params) => {
                        // 获取生成参数
                        if (params) {
                            this.generateParams = params
                        }
                    })
                }, this.currentScene)
                return
            }
            // 当前场景
            let currentScene = this.getCurrentScene()
            if (!currentScene) {
                return
            }
            // 开始 Loading
            this.batchGenerateResultsLoading = true
            // 请求参数
            let params = {
                promptId: this.prompt.id,
                testCount: 1,
                ...this.generateParams,
            }
            // 请求接口
            this.$axios.post(this.getApiUrlByScene(), params).then(res => {
                this.$emit('batchGenerateResultsSuccess', res)
            }).catch(error => {
                console.log(error)
                this.$message.error(error.response.data.error_message)
                this.batchGenerateResultsLoading = false
            })
        },

        // 批量生成结果
        async batchGenerateResults() {
            // 当前场景
            let currentScene = this.getCurrentScene()
            if (!currentScene) {
                return
            }
            await this.updatePromptContentAndCreatePromptVersionPromise()
            // 开始 Loading
            this.batchGenerateResultsLoading = true
            // 请求参数
            let params = {
                promptId: this.prompt.id,
                testCount: this.testCount,
                ...this.generateParams,
            }
            // 请求接口
            this.$axios.post(this.getApiUrlByScene(), params).then(res => {
                this.$emit('batchGenerateResultsSuccess', res)
            }).catch(error => {
                console.log(error)
                this.$message.error(error.response.data.error_message)
                this.batchGenerateResultsLoading = false
            })
        },

        // 测试结束
        testCompleted() {
            this.batchGenerateResultsLoading = false
        },

        // 测试未完成
        testIncomplete() {
            this.batchGenerateResultsLoading = true
        },

        // 根据当前场景，获取接口地址
        getApiUrlByScene() {
            let currentScene = this.getCurrentScene()
            console.log('currentScene', currentScene)
            if (!currentScene) {
                return null
            }
            // 判断当前场景，返回对应的地址
            switch (currentScene) {
                case 'UNIT_OVERVIEW':
                    return $api.urls().createUnitOverviewTest
                case 'PLAN_OVERVIEW':
                    return $api.urls().createPlanOverviewTest
                case 'LESSON_OVERVIEW':
                    return $api.urls().createLessonOverviewTest
                case 'LESSON_DETAIL':
                    return $api.urls().createLessonTest
                case 'TYPICAL_BEHAVIOR':
                    return $api.urls().createTypicalBehaviorsTest
                case 'UNIVERSAL_DESIGN_FOR_LEARNING':
                    return $api.urls().createUniversalDesignForLearningTest
                case 'CULTURALLY_RESPONSIVE_INSTRUCTION':
                    return $api.urls().createCulturallyResponsiveInstructionTest
                case 'HOME_ACTIVITY':
                    return $api.urls().createHomeActivityTest
                case 'DEI_FOR_TEACHER':
                case 'DEI_FOR_ADMINISTRATORS':
                case 'DEI_FOR_ALL':
                    return $api.urls().createDEITest
                default:
                    return $api.urls().createCustomPromptTest
            }
        },

        // 更新提示内容
        updatePromptContent(onlyContent = false) {
            let promptContent
            let evaluatePromptContent
            // 获取编辑器内容
            if (this.$refs.promptTemplate) {
                promptContent = this.$refs.promptTemplate.getPromptContent();
            }
            if (this.$refs.evaluatePromptTemplate) {
                evaluatePromptContent = this.$refs.evaluatePromptTemplate.getPromptContent();
            }
            // 更新提示内容
            if (!onlyContent) {
                if (promptContent) {
                    this.prompt.promptTemplate = promptContent
                }
                if (evaluatePromptContent) {
                    this.prompt.evaluatePromptTemplate = evaluatePromptContent
                }
            }
            return {promptContent, evaluatePromptContent}
        },
        updatePromptEditorContent() {
            if (this.prompt.promptTemplate) {
                this.$refs.promptTemplate.updateContent(this.prompt.promptTemplate)
            }
            if (this.prompt.evaluatePromptTemplate) {
                this.$refs.evaluatePromptTemplate.updateContent(this.prompt.evaluatePromptTemplate)
            }
        },
        // 获取当前场景
        getCurrentScene() {
            if (this.prompt) {
                return this.prompt.scene
            }
            if (this.currentScene && this.currentScene !== '0') {
                return this.currentScene
            }
            return null
        },
    },
}
</script>

<style lang="less" scoped>
.editor {
    height: 200px;
}

.data-trial-text {
    min-width: 100px;
}

.prompt-editor-tab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 4px;
    padding-right: 12px;
    height: 41px;
    background-color: var(--color-gray-white);

    .el-button--text {
        padding: 0px !important;
    }
}

.lg-icon .icons path {
  transition: stroke 0.3s; /* 添加过渡效果 */
}

.lg-icon:hover .icons path {
  stroke: #10b3b7; /* 设置悬浮时的颜色 */
}

.lg-icon:hover .add-sources-text {
  color: #10b3b7;
}
</style>