<template>
  <div class="lg-scrollbar source-layout">
    <!--标题-->
    <div class="display-flex flex-justify-start align-items gap-12">
      <div class="source-list-title">Prompt Source List</div>
      <el-tooltip effect="dark"
                  popper-class="max-width-300 font-size-14"
                  content="Click the tab name to add content from that module to the text box. Better to enclose tabs in quotation marks or brackets [], ensuring more accurate and effective output."
                  placement="bottom">
        <div class="source-tooltip">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M10.2088 13.9584V13.9584L10.2058 9.38767L10.2022 9.33476C10.1842 9.2385 10.099 9.16549 9.99738 9.1655L10.2088 13.9584ZM10.2088 13.9584C10.2089 14.0735 10.1157 14.1668 10.0007 14.1668H10.0006M10.2088 13.9584L10.0006 14.1668M10.0006 14.1668C9.89906 14.1669 9.81384 14.0939 9.79574 13.9976M10.0006 14.1668L9.79574 13.9976M9.79574 13.9976L9.79215 13.9447L9.78916 9.37405C9.78916 9.37402 9.78916 9.374 9.78916 9.37397C9.78914 9.25888 9.88234 9.16563 9.9973 9.1655L9.79574 13.9976ZM10.0002 2.08171C14.3731 2.08171 17.9181 5.62669 17.9181 9.99967C17.9181 14.3726 14.3731 17.9176 10.0002 17.9176C5.62718 17.9176 2.08219 14.3726 2.08219 9.99967C2.08219 5.62669 5.62718 2.08171 10.0002 2.08171ZM10.0002 2.49837C5.8573 2.49837 2.49886 5.85681 2.49886 9.99967C2.49886 14.1425 5.8573 17.5009 10.0002 17.5009C14.143 17.5009 17.5014 14.1425 17.5014 9.99967C17.5014 5.85682 14.143 2.49837 10.0002 2.49837ZM10.0005 6.25071C10.23 6.25071 10.4161 6.43677 10.4161 6.66627C10.4161 6.89577 10.23 7.08182 10.0005 7.08182C9.77105 7.08182 9.58499 6.89579 9.58499 6.66627C9.58499 6.43674 9.77105 6.25071 10.0005 6.25071Z"
              fill="#676879" stroke="#676879" stroke-width="0.833333"/>
          </svg>
        </div>
      </el-tooltip>
    </div>
    <!-- 内容区域 -->
    <div class="source-list-content">
      <div v-for="(content, index) in promptSourcesList" :key="index"
           v-show="contentCanShow(content)"
           class="gap-12 display-flex flex-direction-col flex-justify-start align-items">
        <!-- 内容中的标题 -->
        <div class="content-title">{{ content.title }}</div>
        <!-- 变量 -->
        <div class="display-flex gap-12 flex-wrap flex-justify-start align-items">
          <!-- 提示信息 -->
          <el-tag
            v-for="(source, sourceIndex) in content.sources"
            v-show="stepsCanShow(source)"
            :key="sourceIndex"
            effect="dark"
            type="info"
            size="small"
            @click="clickSource(source)"
            :disable-transitions="true">
            {{ source.sourceName }}
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>
<!--通过接口去获取每一个阶段存在的变量值？-->
<script>
import {mapState} from 'vuex';

export default {
  name: 'PromptSourceList',
  props: {},
  computed: {
    ...mapState({
      baseInfo: state => state.curriculum.unit.baseInfo,
      unitOverview: state => state.curriculum.unit.overview,
      currentWeekPlan: state => state.curriculum.unit.currentWeekPlan,
      promptSourcesList: state => state.curriculum.promptSourcesList,
    }),
    // 当前步骤
    step() {
      const path = this.$route.path
      if (path.includes('/unit-overview')) {
        return 0
      } else if (path.includes('/weekly-plan-overview')) {
        return 1
      } else if (path.includes('/lesson-overview')) {
        return 2
      } else if (path.includes('/lesson-detail')) {
        return 3
      }
      return 0
    },
    stepsCanShow() {
      return function (source) {
        // source 的值不能是空的
        let noBlank = source.sourceValue !== 'undefined' && source.sourceValue !== null && source.sourceValue !== '';
        // 如果不是空的，那么需要获取对应的 step
        if (noBlank) {
          let currentStep = this.step
          return source.sourceStep <= currentStep + 1
        } else {
          return false
        }
      }
    },
    contentCanShow() {
      return function (content) {
        // 如果 source 的长度大于 0 并且有一些 source 可以显示
        return content.sources.length > 0 && content.sources.some(source => this.stepsCanShow(source))
      }
    }
  },
  data() {
    return {}
  },
  watch: {
    baseInfo: {
      handler: function (val) {
        if (val) {
          // 更新 store 中的 prompt source list 的信息
          this.updatePromptSourceList(1)
        }
      },
      deep: true,
      immediate: true
    },
    unitOverview: {
      handler: function (val) {
        if (val) {
          // 更新 store 中的 prompt source list 的信息
          this.updatePromptSourceList(1)
        }
      },
      deep: true,
      immediate: true
    },
    currentWeekPlan: {
      handler: function (val) {
        if (val) {
          // 更新 store 中的 prompt source list 的信息
          this.updatePromptSourceList(2)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 获取领域标签
    getLabel(value) {
      // 根据 value 获取对应的 label 如果为空则返回空字符串
      if (!value) {
        return ''
      }
      let domain = this.baseInfo.allDomains.find(option => option.value === value)
      return domain && domain.label;
    },
    // 更新 prompt source list
    updatePromptSourceList(step) {
      // 获取当前的 baseInfo 和 unitOverview
      let baseInfo = this.baseInfo
      let unitOverview = this.unitOverview
      let currentWeekPlan = this.currentWeekPlan;
      // 定义资源
      let sources = []
      // 如果是第一步
      if (step === 1) {
        // 定义选中的 subjectsDomains
        let subjectsDomains
        if (baseInfo.domainIds && baseInfo.domainIds.length > 0) {
          subjectsDomains = baseInfo.domainIds.map(id => this.getLabel(id)).join(', ')
        }
        // 定义一个资源数组
        sources = [
          {sourceStep: 1, sourceName: 'unit name', sourceValue: baseInfo.title},
          {sourceStep: 1, sourceName: 'unit description', sourceValue: baseInfo.description},
          {sourceStep: 1, sourceName: 'overall learning standards (with detailed info)', sourceValue: '-'},
          {sourceStep: 1, sourceName: 'grade', sourceValue: baseInfo.grade},
          {sourceStep: 1, sourceName: 'total number of week', sourceValue: baseInfo.weekCount},
          {sourceStep: 1, sourceName: 'learning standards', sourceValue: baseInfo.frameworkName},
          {
            sourceStep: 1,
            sourceName: 'subjects/domains',
            sourceValue: subjectsDomains
          },
          {sourceStep: 1, sourceName: 'country', sourceValue: baseInfo.country},
          {sourceStep: 1, sourceName: 'state', sourceValue: baseInfo.state},
          {sourceStep: 1, sourceName: 'city', sourceValue: baseInfo.city},
          {sourceStep: 2, sourceName: 'unit overview', sourceValue: unitOverview.overview},
          {sourceStep: 2, sourceName: 'unit trajectory', sourceValue: unitOverview.trajectory},
          {sourceStep: 2, sourceName: 'concepts', sourceValue: unitOverview.concepts},
          {sourceStep: 2, sourceName: 'guiding questions', sourceValue: unitOverview.guidingQuestions}
        ]
      } else {
        // 定义一个资源数组
        sources = [
          {sourceStep: 3, sourceName: 'weekly theme', sourceValue: currentWeekPlan.theme},
          {sourceStep: 3, sourceName: 'weekly overview', sourceValue: currentWeekPlan.overview},
          {sourceStep: 3, sourceName: 'week number', sourceValue: currentWeekPlan.week}
        ]
      }
      // 更新 store 中的 prompt source list 的信息
      this.$store.dispatch('curriculum/updatePromptSourcesList', {
        step: step,
        sources: sources
      })
    },
    // 当点击 source 的时候
    clickSource(sourceTag) {
      // 点击了 source 之后传递出去
      this.$emit('clickSource', sourceTag)
    }
  }
}
</script>

<style scoped lang="less">
.source-layout {
  width: 100%;
  max-height: 452px;
  gap: 10px;
  border-radius: 8px;
  border: 1px;
}

.source-list-title {
  height: 24px;
  gap: 0px;
  font-size: 16px;
  //styleName: Semi Bold/16px;
  font-family: Inter;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: var(--color-text-primary);
}

.source-tooltip {
  max-height: 24px;
}

.max-width-600 {
  max-width: 600px;
}

.source-list-content {
  max-height: 386px;
  gap: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;

  .content-title {
    font-family: Inter;
    font-size: 14px;
    font-weight: 600;
    line-height: 26px;
    text-align: left;
    width: 100%;
    height: 26px;
    gap: 0px;
    color: var(--color-text-primary);
  }
}
/deep/ .custom-el-tag {
  margin: 5px; /* 设置每个标签的外边距 */
}
/deep/ .el-tag.el-tag--info {
  color: #5e6d82;
  background-color: #f9fafc;
  padding: 0px 4px;
  border: 1px solid #eaeefb;
  border-radius: 4px;
  // margin: 0 4px;
  cursor: pointer;
}

/deep/ .el-tag.el-tag--info:hover {
  background-color: #DEE7F4;
}
</style>
