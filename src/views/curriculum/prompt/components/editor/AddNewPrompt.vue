<template>
    <div>
        <el-dialog
            :append-to-body="true"
            :visible.sync="showAddNewPrompt"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            width="600"
            custom-class="add-prompt-dialog"
            :destroy-on-close="true">
            <!--弹窗的头部信息-->
            <div v-if="showAddNewPrompt" slot="title" class="dialog-title">
                <div class="dialog-title-content display-flex align-items" style="gap: 5px">
                    <span>{{ newPromptTitle }}</span>
                </div>
                <div class="dialog-close" @click="handleClose(false)">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.99951 7L16.9995 17" stroke="#111C1C" stroke-width="2" stroke-linecap="round"
                              stroke-linejoin="round"/>
                        <path d="M6.99951 17L16.9995 7" stroke="#111C1C" stroke-width="2" stroke-linecap="round"
                              stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
            <el-form ref="newPrompt"
                     v-if="showAddNewPrompt"
                     label-position="top"
                     label-width="100px"
                     :model="newPrompt"
                     :rules="newPromptRules">
                <!-- 课程标题 -->
                <el-form-item label="Prompt Name" prop="name">
                    <el-input v-model="newPrompt.name" placeholder="Module Name"/>
                </el-form-item>
                <!-- 目标 -->
                <el-form-item ref="introduction" label="Description" prop="introduction">
                    <el-input
                        v-model="newPrompt.introduction"
                        type="textarea"
                        placeholder="Briefly describe this module here."
                        :autosize="{ minRows: 4, maxRows: 10}"/>
                </el-form-item>
            </el-form>
            <div v-if="showAddNewPrompt" slot="footer">
                <el-button @click="handleClose">{{ $t('loc.cancel') }}</el-button>
                <!-- 一个 Save 按钮 -->
                <el-button @click="saveNewPrompt" :loading="saveLoading" type="primary">Save</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import {mapState} from 'vuex';

export default {
    name: 'AddNewPrompt',
    props: {
        edit: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        // 全局信息
        ...mapState({
            currentScene: state => state.curriculum.currentScene, // 当前场景
            currentPromptId: state => state.curriculum.currentPromptId, // 当前场景 Id
        }),
        newPromptTitle() {
            return this.edit ? 'Edit Prompt' : 'Add New'
        }
    },
    watch: {
        showAddNewPrompt(val) {
            // 如果是编辑状态，就获取当前的 Prompt 信息
            if (val && this.edit) {
                // 如果是 true，则调用 get Prompt 方法
                this.getPrompt().then(res => {
                    const newPrompt = {
                        name: res.name,
                        introduction: res.introduction
                    }
                    // 响应式赋值
                    this.$set(this, 'newPrompt', newPrompt)
                    // 保存旧的场景
                    this.oldScene = res.scene
                })
            }
        }
    },
    data() {
        return {
            showAddNewPrompt: false,
            newPrompt: {}, // 新的提示
            oldScene: '', // 旧的场景
            newPromptRules: {
                name: [
                    {required: true, message: this.$t('loc.fieldReq'), trigger: 'blur'},
                    {required: true, message: this.$t('loc.fieldReq'), trigger: 'change'}
                ]
            },
            saveLoading: false // 保存的时候的 loading
        }
    },
    methods: {
        saveNewPrompt() {
            this.$refs.newPrompt.validate(async (valid) => {
                console.log('valid', valid)
                if (valid) {
                    // 保存进入数据库中
                    let newPromptFromDb = await this.saveNewPromptToDB()
                    // 回调事件
                    this.$emit('saveNewPrompt', newPromptFromDb)
                    // 关闭弹窗
                    this.handleClose(false)
                }
            })
        },
        // 获取 Prompt 详情
        getPrompt() {
            console.log(this.currentPromptId, this.currentScene)
            // 通过 ID 获取
            if (this.currentPromptId) {
                return this.getPromptById(this.currentPromptId)
            }
            // 通过场景获取
            if (this.currentScene) {
                return this.getPromptByScene(this.currentScene)
            }
        },

        // 根据 ID 获取 Prompt
        getPromptById(promptId) {
            return this.$axios.get($api.urls().getPrompt, {params: {promptId: promptId}})
        },

        // 根据场景获取 Prompt
        getPromptByScene(scene) {
            return this.$axios.get($api.urls().getPromptByScene, {params: {scene: scene}})
        },
        // 关闭弹窗
        handleClose() {
            this.showAddNewPrompt = false
            this.$refs.newPrompt.resetFields()
        },
        saveNewPromptToDB() {
            // this.newPrompt.name 将 name 非法字符转化为 _ 来拼接
            // 定义文件名称,只能是字母和数字还有一些规定的符号
            const regex = /^[a-zA-Z0-9,-]+$/
            // 如果文件名不符合规定，就将所有不符合的字符替换成 _
            let newPromptName = this.newPrompt.name.trim()
            //  如果不符合规定就替换成 _
            const newPromptScene = newPromptName.replace(/./g, (char) => {
                return regex.test(char) ? char : '_'
            })
            // 保存进入数据库中
            return new Promise((resolve, reject) => {
                this.$axios.post($api.urls().createRecoverOrUpdatePrompt, {
                    oldScene: this.oldScene,
                    name: newPromptName,
                    scene: newPromptScene,
                    promptSource: 'UNIT_LESSON_DETAIL_PROMPT',
                    introduction: this.newPrompt.introduction
                }).then(res => {
                    resolve(res.id)
                }).catch(error => {
                    reject(error)
                })
            })
        }
    }
}
</script>

<style scoped lang="less">
.add-prompt-dialog {
    display: flex;
    width: 600px;
    padding: 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 8px;
    background: #FFF;

    /* 卡片投影 */
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);

    .dialog-title {
        display: flex;
        align-items: flex-start;
        align-self: center;

        .dialog-title-content {
            flex: 1 0 0;
            color: var(--111-c-1-c, #111C1C);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Semi Bold/20px */
            font-family: Inter;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 26px; /* 130% */
        }

        .dialog-close {
            cursor: pointer;
            width: 20px;
            height: 20px;
        }
    }

    .dialog-body {
        display: flex;
        margin-top: -24px;
        margin-bottom: -24px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .body-text {
            color: var(--color-text-placeholder);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Regular/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
}

/deep/ .el-dialog {
    width: 600px;
}

/deep/ .el-dialog__body {
    padding: 0px 20px;
}

::v-deep {
    .el-form-item__label {
        margin-bottom: 0;
        line-height: 22px;
        font-weight: 600;
    }

    .el-form-item__error {
        padding-left: 0;
    }

    .el-radio-group {
        .el-radio-button {
            background-color: #DDF2F3 !important;
            padding: 4px;
            box-sizing: border-box;
        }

        .el-radio-button__inner {
            padding: 6px 10px;
            background-color: #DDF2F3;
            color: #10b3b7;
            border: none;
            border-radius: 4px;
        }

        .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            background-color: #10b3b7;
            color: #fff;
        }
    }
}
</style>