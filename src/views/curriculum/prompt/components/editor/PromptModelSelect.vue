<template>
  <el-dropdown class="prompt-model-select" trigger="click" @command="selectMenuItem">
    <div class="models-description">
      <div class="models-description-item">
        <!-- 图标 -->
        <el-avatar
          :size="24"
          style="min-width: 24px"
          :src="currentModel.avatarUrl"
        >
          <span slot="default" v-html="currentModel.defaultSvg"></span>
        </el-avatar>
        <div>
          <span class="font-bold">{{ currentModel.name }}</span>
        </div>
        <!--下拉-->
        <i class="el-icon-arrow-down el-icon--right"></i>
      </div>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="(model, index) in models" :command="model" :key="index"
        :class="{'selected-background' : model === currentModel}">
        <div class="display-flex align-items gap-12">
          <!-- 图标 -->
          <el-avatar
            :size="24"
            style="min-width: 24px"
            :src="model.avatarUrl"
          >
            <span slot="default" v-html="model.defaultSvg"></span>
          </el-avatar>
          <div>
            <span class="font-bold">{{ model.name }}</span>
          </div>
        </div>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import {mapState} from 'vuex'

export default {
  name: 'PromptModelSelect',
  computed: {
    // 全局信息
    ...mapState({
      models: state => state.curriculum.models, // 模型列表
      currentModel: state => state.curriculum.currentModel, // 当前模型
    }),
  },
  methods: {
    // 选择模型
    selectMenuItem(model) {
      this.$store.commit('curriculum/SET_CURRENT_MODEL', model)
    },
  },
}
</script>

<style scoped lang="less">
.models-description {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.models-description-item {
  display: flex;
  min-height: 32px;
  height: fit-content;
  line-height: normal;
  padding: 4px 8px;
  align-items: center;
  gap: 8px;
  border-radius: 90px;
  background: var(--color-primary-light);
  color: var(--color-text-primary);
  cursor: pointer;
  border: 1px solid var(--color-primary-light);
}

.selected-background {
  background: var(--color-primary-light);
  color: var(--color-text-primary);
}
</style>