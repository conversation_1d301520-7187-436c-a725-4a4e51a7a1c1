<template>
  <div class="display-flex flex-direction-col justify-content align-items gap-16">
    <!--New Tab 显示的名称-->
    <el-card shadow="never" class="w-full" body-style="height: 100%;">
      <div @mouseover="showOrHide('showEditOrDelete',true)"
           @mouseleave="showOrHide('showEditOrDelete',false)"
           class="display-flex flex-justify-start align-items gap-8">
        <el-button type="text"><span class="font-size-18">{{ currentTab }}</span></el-button>
        <!--编辑按钮-->
        <el-button v-show="showEditOrDelete" type="text" @click="editPrompt">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.5 20.5H20.5" stroke="#111C1C" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round"/>
            <path d="M5.38867 13.2844V16.7222H8.84402L18.6109 6.95105L15.1614 3.5L5.38867 13.2844Z" stroke="#111C1C"
                  stroke-width="1.5" stroke-linejoin="round"/>
          </svg>
        </el-button>
        <!--删除按钮-->
        <el-button v-show="showEditOrDelete" @click="deletePrompt" type="text">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_4650_11385)">
              <path
                d="M5.125 5.5835V18.9668C5.125 20.1819 6.10997 21.1668 7.325 21.1668H16.675C17.89 21.1668 18.875 20.1819 18.875 18.9668V5.5835H5.125Z"
                stroke="#111C1C" stroke-width="1.5" stroke-linejoin="round"/>
              <path d="M10.1665 10.1665V16.1248" stroke="#111C1C" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path d="M13.8335 10.1665V16.1248" stroke="#111C1C" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path d="M2.8335 5.5835H21.1668" stroke="#111C1C" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path d="M8.3335 5.5835L9.84095 2.8335H14.1897L15.6668 5.5835H8.3335Z" stroke="#111C1C" stroke-width="1.5"
                    stroke-linejoin="round"/>
            </g>
            <defs>
              <clipPath id="clip0_4650_11385">
                <rect width="22" height="22" fill="white" transform="translate(1 1)"/>
              </clipPath>
            </defs>
          </svg>
        </el-button>
      </div>
    </el-card>

    <!--富文本框-->
    <el-card shadow="never" class="w-full teacherChildrenContent" body-style="height: 100%;">
      <editor v-model="unitLessonNewPromptContent" @blur="handleBlur"
              v-loading="getCustomizePromptContentLoading"
              placeholder="Your outputs will show up here."/>
    </el-card>

    <!-- 添加新的 prompt   -->
    <AddNewPrompt ref="addNewPrompt"
                  :edit="true"
                  @saveNewPrompt="updateNewPrompt"></AddNewPrompt>
  </div>
</template>
<script>
import Editor from '@/views/modules/lesson2/component/editor/index.vue';
import AddNewPrompt from '@/views/curriculum/prompt/components/editor/AddNewPrompt.vue';
import {bus} from '@/utils/bus';
import {mapState} from 'vuex';

export default {
  name: 'PromptCustomerDetail',
  components: {AddNewPrompt, Editor},
  props: {
    // 调用组件时传入的 gpt 的内容
    content: {
      type: String,
      default: ''
    },
    // 课程信息
    lesson: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 当前的 prompt id, 用于获取自定义的 prompt 内容
    promptId: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      // 从 store 中获取当前的 tab
      currentPromptId: state => state.curriculum.currentPromptId,
      customizePrompt: state => state.curriculum.customizePrompt,
      currentScene: state => state.curriculum.currentScene,
      currentTab: state => state.curriculum.currentTab
    })
  },
  data() {
    return {
      unitLessonNewPromptContent: '', // 当前的 prompt 的内容
      prompt: {}, // 当前的 prompt
      getCustomizePromptContentLoading: false, // 获取自定义的 prompt 内容
      leavedPage: false, // 是否离开页面
      showEditOrDelete: false, // 是否显示编辑或删除按钮
    }
  },
  mounted() {
    if (this.promptId || this.currentPromptId) {
      this.prompt.id = this.promptId || this.currentPromptId
      this.getCustomizePromptContent(this.promptId || this.currentPromptId, this.lesson.id)
    } else {
      let findPrompt = this.customizePrompt.find(item => item.scene === this.currentScene);
      if (findPrompt) {
        this.prompt = findPrompt
        this.getCustomizePromptContent(findPrompt.id, this.lesson.id)
      }
    }
  },
  destroyed() {
    this.leavedPage = true
  },
  methods: {
    // 弹窗回调保存新的 Prompt
    async updateNewPrompt(newPromptId) {
      // 获取新的 prompt
      await this.getPromptById(newPromptId).then(res => {
        this.prompt = res
        // 更新 Prompt ID
        let promptId = this.prompt.id
        this.$emit('updatePromptId', promptId)
        // 获取当前场景
        let scene = this.prompt.scene
        // 关闭弹窗，要求更换新的 prompt
        this.$store.commit('curriculum/SET_PROMPT_CUSTOM_DETAIL_HAS_UPDATED', true)
        let eventName = scene.toLowerCase() + ':generateParams'
        // 触发事件
        bus.$emit(eventName, (params) => {
          // 获取生成参数
          if (params) {
            this.generateParams = params
          }
        })
      }).catch(error => {
        this.getPromptLoading = false
        console.log(error)
        this.$message.error(error.response.data.error_message)
      })
    },
    // 显示或隐藏
    showOrHide(key, value) {
      this.$set(this, key, value)
    },
    // 根据 ID 获取 Prompt
    getPromptById(promptId) {
      return this.$axios.get($api.urls().getPrompt, {params: {promptId: promptId}})
    },
    // 编辑 Prompt
    editPrompt() {
      this.$refs.addNewPrompt.showAddNewPrompt = true
    },
    // 删除 Prompt
    deletePrompt() {
      // 弹出确认框
      this.$confirm(this.$createElement('div', {
          domProps: {
            innerHTML: 'Do you want to delete this module? \n' +
              '<div style="color: var(--color-danger)">All generated content and the history of prompt adjustments will be cleared and cannot be restored.</div>'
          }
        }),
        'Confirmation', {
          confirmButtonText: 'Delete',
          confirmButtonClass: 'el-button--danger',
          cancelButtonText: 'Cancel'
        }).then(() => {
        // 定义 prompt ID
        let promptId = this.promptId || this.currentPromptId
        // 从自定义的 prompt 中获取当前 scene 对应的 prompt
        let findPrompt = this.customizePrompt.find(item => item.scene === this.currentScene)
        // 若找到了，则使用找到的 prompt
        if (findPrompt) {
          promptId = findPrompt.id
        }
        // 删除 Prompt
        this.$axios.post($api.urls().deletePrompt, {id: promptId}).then(res => {
          this.$message.success('Prompt deleted successfully')
          // 关闭弹窗，要求更换新的 prompt
          this.$store.commit('curriculum/SET_PROMPT_CUSTOM_DETAIL_HAS_UPDATED', true)
        }).catch(error => {
          console.log(error)
          this.$message.error(error.response.data.error_message)
        })
      }).catch(() => {

      })
    },
    // 获取自定义的 prompt 详情内容
    getPromptCustomDetailContent() {
      return this.unitLessonNewPromptContent
    },
    savePromptCustomDetail() {
      // 获取内容
      let content = this.unitLessonNewPromptContent
      let promptId = this.promptId || this.currentPromptId
      if (!promptId) {
        let findPrompt = this.customizePrompt.find(item => item.scene === this.currentScene)
        if (findPrompt) {
          promptId = findPrompt.id
        }
      }
      // 调用接口保存
      this.$axios.post($api.urls().saveCustomizePromptContent, {
        promptId: promptId,
        objectId: this.lesson.id,
        content: content
      }).then().catch(error => {
        console.log(error)
        this.$message.error(error.response.data.error_message)
      })
    },
    // 失去焦点
    handleBlur() {
      this.unitLessonNewPromptContent = this.unitLessonNewPromptContent.replace(/<p><br><\/p>/g, '')
    },
    // 获取自定义的 prompt 内容
    getCustomizePromptContent(promptId, lessonId) {
      // 开始 loading
      this.getCustomizePromptContentLoading = true

      // 获取内容
      this.$axios.get($api.urls().getCustomizePromptContent, {
        params: {
          promptId: promptId,
          ObjectId: lessonId
        }
      }).then(res => {
        this.getCustomizePromptContentLoading = false
        if (res && res.promptCustomContent && res.promptCustomContent.content) {
          this.unitLessonNewPromptContent = res.promptCustomContent.content
        } else {
          this.unitLessonNewPromptContent = ''
        }
      }).catch(error => {
        console.log(error)
        this.$message.error(error.response.data.error_message)
        this.getCustomizePromptContentLoading = false
      })
    },
  },
  watch: {
    currentPromptId: {
      handler: function (val) {
        if (val && val !== '' && !this.leavedPage) {
          const interval = setInterval(() => {
              // 如果 lesson id 存在
              if (this.lesson.id) {
                // 获取当前的 prompt
                this.getCustomizePromptContent(val, this.lesson.id)
                clearInterval(interval)
              }
            }, 1000)
        }
      },
      immediate: true
    },
    promptId: {
      handler: function (val) {
        // 如果没有初始化过
        if (val && this.lesson.id) {
          // 获取当前的 prompt
          this.getCustomizePromptContent(val, this.lesson.id)
        }
      },
      immediate: true,
      deep: true
    },
    content: {
      handler: function (val) {
        if (val) {
          this.unitLessonNewPromptContent = val
        }
      },
      immediate: true
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-button--text {
  padding: 0;
}

// iep support 使用 editor 组件，这里对其进行样式修改
.teacherChildrenContent /deep/ .quill-editor {
  line-height: normal;
  min-height: 500px;

  .ql-editor {
    min-height: 500px;
  }

  .ql-editor {
    overflow-y: auto;
    min-height: 500px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
  }
}
</style>
