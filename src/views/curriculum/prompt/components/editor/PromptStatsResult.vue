<template>
    <div class="flex-auto display-flex flex-direction-col h-full">
        <!-- Loading -->
        <el-skeleton class="m-t-sm" animated v-show="getTestResultLoading && (!usageRecords || usageRecords.length === 0)" />
        <!-- 标题 -->
        <div class="font-size-18 font-bold" v-show="showTitle">Statistics Validation</div>
        <!-- 空页面 -->
        <template v-if="!getTestResultLoading && (!usageRecords || usageRecords.length === 0)">
            <EmptyPage/>
        </template>
        <!-- 内容 -->
        <div v-show="usageRecords && usageRecords.length > 0" class="display-flex flex-direction-col flex-auto stats-result-content">
            <!-- 统计信息 -->
            <PromptStatsIndicator
                class="m-b-sm"
                :promptInfo="promptInfo"
                :testCount="usageRecords.length"
            />
            <!-- 测试记录列表 -->
            <PromptUsageRecords
                ref="promptUsageRecordsRef"
                class="h-full usage-records flex-auto"
                :usageRecords="usageRecords"
            />
        </div>
    </div>
</template>

<script>
import PromptStatsIndicator from '@/views/curriculum/prompt/components/editor/PromptStatsIndicator.vue'
import PromptUsageRecords from '@/views/curriculum/prompt/components/editor/PromptUsageRecords.vue'
import EmptyPage from '@/views/curriculum/components/EmptyPage.vue'

export default {
    props: {
        // Prompt ID
        promptId: {
            type: String,
            default: null,
        },

        // 测试 ID
        testId: {
            type: String,
            default: null,
        },

        // 是否显示标题
        showTitle: {
            type: Boolean,
            default: true,
        },
    },

    components: {
        PromptStatsIndicator, // Prompt 统计指标
        PromptUsageRecords, // Prompt 测试记录列表
        EmptyPage, // 空卡片
    },

    data() {
        return {
            getTestResultLoading: false, // 获取测试结果加载状态
            promptInfo: null, // Prompt 信息
            usageRecords: [], // 测试记录
        }
    },

    watch: {
        // 测试 ID 变化，更新测试结果
        testId: {
            handler(val) {
                console.log('testId', val)
            },
            immediate: true,
        },

        // Prompt ID 变化，更新测试结果
        promptId: {
            handler(val) {
                // 清空数据
                this.usageRecords = []
                // 获取测试结果
                this.getTestResults(true)
            },
            immediate: true,
        },
    },

    created() {
    },

    methods: {
        // 获取测试结果
        getTestResults(resetActive) {
            if (!this.promptId) {
                return
            }
            // 更新加载状态
            this.getTestResultLoading = true
            // 请求参数
            const params = {
                promptId: this.promptId, // Prompt ID
                pageNum: 1, // 页码
                pageSize: 100, // 每页条数
            }
            // 请求接口
            this.$axios.get($api.urls().listPromptTestRecords, { params: params }).then(res => {
                this.promptInfo = res.prompt
                this.usageRecords = res.usageRecords
                // 更新加载状态
                this.getTestResultLoading = false
                // 没有选中的，则选择第一个
                if (!this.$refs.promptUsageRecordsRef) {
                    return
                }
                let activeUsageRecord = this.$refs.promptUsageRecordsRef.activeUsageRecord
                if (!activeUsageRecord || resetActive) {
                    this.$nextTick(() => {
                        this.$refs.promptUsageRecordsRef.resetActiveUsageRecord()
                    })
                }
                // 如果没有全部完成，等待 10 秒再次获取
                if (!res.allCompleted) {
                    setTimeout(() => {
                        this.getTestResults()
                    }, 10000)
                    this.$emit('testIncomplete', this.usageRecords)
                } else {
                    this.$emit('testCompleted', this.usageRecords)
                }
            }).catch(error => {
                console.log(error)
                this.$message.error(error.response.data.error_message)
                // 更新加载状态
                this.getTestResultLoading = false
            })
        },
    },
}
</script>

<style lang="less" scoped>
.usage-records {
    overflow-y: auto;
    flex: auto;
    min-height: 0;
}
.stats-result-content {
    min-height: 0;
}
</style>
