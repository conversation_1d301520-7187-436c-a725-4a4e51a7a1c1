<template>
    <div>
        <!-- 弹窗 -->
        <el-dialog
            :append-to-body="true"
            :close-on-press-escape="false"
            :destroy-on-close="true"
            class="dialog-vertical"
            :close-on-click-modal="false"
            :class="{'no-body-top-padding': view === 'PROMPT_DIFF'}"
            :visible="visible"
            :before-close="handleClose">
            <template slot="title">
                <span class="font-size-18" v-show="view === 'PROMPT_LIST'">
                    <span>Prompt Management - {{ currentTab }}</span>
                    <div class="sub-title">Choose and set your best and greatest prompt here.</div>
                </span>
                <!-- Prompt 统计 -->
                <div class="font-size-18 lg-pointer" v-if="view === 'PROMPT_DETAIL'" @click="backToList">
                    <i class="el-icon-arrow-left"></i>
                    <span>Statistics Validation</span>
                </div>
                <!-- Prompt 对比 -->
                <div class="font-size-18 lg-pointer" v-if="view === 'PROMPT_DIFF'" @click="backToList">
                    <i class="el-icon-arrow-left"></i>
                    <span>Prompt Comparison</span>
                </div>
            </template>
            <!-- Prompt 版本列表 -->
            <PromptVersionList
                v-show="view === 'PROMPT_LIST'"
                ref="promptVersionListRef"
                :scene="scene"
                :disableEdit="disableEdit"
                @previewPrompt="previewPrompt"
                @comparePrompt="comparePrompt"
            />
            <!-- 编辑 Prompt -->
            <PromptDetail
                v-if="view === 'PROMPT_DETAIL'"
                ref="promptDetailRef"
                @updatePromptId="updatePromptId"
                :promptId="currentPromptId"
            />
            <!-- 对比 Prompt -->
            <PromptDiff
                v-if="view === 'PROMPT_DIFF'"
                ref="promptDiffRef"
                :oldPromptId="oldPromptId"
                :newPromptId="newPromptId"
            />
        </el-dialog>
    </div>
</template>

<script>
import PromptVersionList from './PromptVersionList.vue'
import PromptDetail from '../editor/PromptDetail.vue'
import PromptDiff from '../diff/PromptDiff.vue'
import {mapState} from "vuex";

export default {
    props: {
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false,
        },

        // Prompt 场景
        scene: {
            type: String,
            default: null,
        },

        // 能否编辑
        disableEdit: {
            type: Boolean,
            default: false
        }
    },

    components: {
        PromptVersionList, // Prompt 版本列表
        PromptDetail, // Prompt 详情
        PromptDiff, // Prompt Diff
    },

    data() {
        return {
            currentPromptId: null, // 当前 Prompt ID
            view: 'PROMPT_LIST', // 当前视图
            oldPromptId: null, // 旧 Prompt ID
            newPromptId: null, // 新 Prompt ID
            diffType: null, // 对比类型
        }
    },

    computed: {
        // 全局信息
        ...mapState({
            currentTab: state => state.curriculum.currentTab, // 当前场景
        })
    },
    methods: {
        // 关闭弹窗前回调
        handleClose(done) {
            // 是否修改了活跃 Prompt
            // if (this.$refs.promptVersionListRef && this.$refs.promptVersionListRef.changedActivePrompt) {
            //     // 关闭后重新加载 Prompt
            //     this.$emit('changedActivePrompt')
            // }
            this.$emit('changedActivePrompt')
            // 更新显示状态
            this.$emit('update:visible', false)
            // 关闭弹窗
            done()
        },

        // 预览 Prompt
        previewPrompt(promptId) {
            this.currentPromptId = promptId
            console.log('abc,', this.currentPromptId)
            // 更新视图
            this.view = 'PROMPT_DETAIL'
        },

        updatePromptId(promptId) {
            this.currentPromptId = promptId
        },

        // 对比 Prompt
        comparePrompt(oldPromptId, newPromptId, type) {
            this.oldPromptId = oldPromptId
            this.newPromptId = newPromptId
            this.diffType = type
            // 更新视图
            this.view = 'PROMPT_DIFF'
            this.$nextTick(() => {
                // 获取对比数据
                this.$refs.promptDiffRef.getPromptComparisonInfo()
            })
        },

        // 返回列表
        backToList() {
            // 更新视图
            this.view = 'PROMPT_LIST'
            // 更新列表数据
            this.$refs.promptVersionListRef.getPromptList()
        },
    },
}
</script>

<style lang="less" scoped>
.dialog-vertical {
  display: flex;
  padding: 20px;
  ::v-deep .el-dialog { 
    height: 90vh;
    width: 90%;
    margin: auto !important;
    display: flex;
    flex-direction: column;
    .el-dialog__body {
        flex: auto;
        min-height: 0;
        display: flex;
        flex-direction: column;
        .no-shrink {
            flex: auto;
            min-height: 0;
        }
    }
  }
}
.sub-title {
    font-size: 14px;
    color: var(--color-text-secondary);
    margin-top: 10px;
}
::v-deep {
    .no-body-top-padding {
        .el-dialog__body {
            padding-top: 0 !important;
        }
    }
}
</style>