<template>
    <!-- 表格 -->
    <div v-loading="listPromptsLoading" class="display-flex flex-direction-col prompt-table">
        <el-table
            :data="prompts"
            @row-click="handleRowClick"
            height="calc(80vh - 200px)"
            class="w-full">
            <!-- 版本 -->
            <el-table-column label="Version" min-width="140">
                <template slot-scope="scope">
                    <div class="flex-wrap btn-center">
                        <span class="">{{ scope.row.version }}</span>
                        <div v-show="scope.row.systemActive || scope.row.userBestActive || scope.row.isDraft">
                            <!--scope.row.isDraft-->
                            <el-tag class="el-tag-danger m-r-xs border-none" size="small" type="danger" v-show="scope.row.isDraft" disable-transitions>Draft</el-tag>
                            <el-tag class="el-tag-primary m-r-xs border-none" size="small" type="primary" v-show="scope.row.systemActive" disable-transitions>System</el-tag>
                            <el-tag class="el-tag-warning border-none" size="small" type="success" v-show="!!scope.row.userBestActive" disable-transitions>Best</el-tag>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <!-- 创建人 -->
            <el-table-column label="Creator" min-width="110" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                    <span>{{ scope.row.createUserName }}</span>
                </template>
            </el-table-column>
            <!-- 创建时间 -->
            <el-table-column label="Creation Time" min-width="180">
                <template slot-scope="scope">
                    <span>{{ formatDate(scope.row.createAtUtc) }}</span>
                </template>
            </el-table-column>
            <!-- 模型 -->
            <el-table-column label="Model" min-width="130">
                <template slot-scope="scope">
                    <span>{{ scope.row.displayModel }}</span>
                </template>
            </el-table-column>
            <!-- 温度 -->
            <el-table-column label="Temperature" min-width="130">
                <template slot-scope="scope">
                    <span>{{ scope.row.temperature }}</span>
                </template>
            </el-table-column>
            <!-- 总费用 -->
            <el-table-column label="Total Cost" min-width="120">
                <template slot-scope="scope">
                    <span>{{ scope.row.totalCost ? scope.row.totalCost : '--' }}</span>
                </template>
            </el-table-column>
            <!-- 平均费用 -->
            <el-table-column label="Average Cost" min-width="100">
                <template slot-scope="scope">
                    <span>{{ scope.row.averageCost ? '$' + scope.row.averageCost : '--' }}</span>
                </template>
            </el-table-column>
            <!-- 平均时长 -->
            <el-table-column label="Average Duration" min-width="100">
                <template slot-scope="scope">
                    <span>{{ scope.row.averageDuration ? scope.row.averageDuration + 's' : '--' }}</span>
                </template>
            </el-table-column>
            <!-- 平均分数 -->
            <el-table-column label="Average Score" min-width="100">
                <template slot-scope="scope">
                    <span>{{ scope.row.averageScore ? scope.row.averageScore : '--' }}</span>
                </template>
            </el-table-column>
            <!-- 生成次数 -->
            <el-table-column label="Entries" min-width="100">
                <template slot-scope="scope">
                    <span>{{ scope.row.contentCount ? scope.row.contentCount : '0' }}</span>
                </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column label="Actions" min-width="220" fixed="right">
                <template slot-scope="scope">
                    <!-- 编辑 -->
                    <el-button type="text" icon="el-icon-edit" v-show="!disableEdit" @click.stop="editPrompt(scope.row)">Edit</el-button>
                    <!-- 应用 -->
                    <el-button type="text" icon="el-icon-setting" @click.stop="applyPrompt(scope.row)"
                        :loading="scope.row.applyPromptLoading" :disabled="scope.row.userActive">Apply</el-button>
                    <!-- 更多操作 -->
                    <el-dropdown trigger="click" @command="handleCommand($event, scope.row)">
                        <span class="el-dropdown-link" @click.stop="">
                            <el-button type="text" class="m-l-sm more-btn" icon="el-icon-more"></el-button>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <!-- 和最佳对比 -->
                            <el-dropdown-item icon="el-icon-s-data" command="compareWithBest" v-show="!scope.row.userBestActive && personalBestPromptId && !scope.row.isDraft">Compare with Previous Best</el-dropdown-item>
                            <!-- 和系统默认对比 -->
                            <el-dropdown-item icon="el-icon-s-data" command="compareWithSystemDefault" v-show="false && promptManagement && !scope.row.systemActive && !scope.row.isDraft">Compare with System Default</el-dropdown-item>
                            <!-- 设为最佳 -->
                            <el-dropdown-item icon="el-icon-star-off" command="setAsBest" v-show="!scope.row.userBestActive && !scope.row.isDraft && scope.row.createUserName !== 'System'">Set as Best Version</el-dropdown-item>
                            <!-- 设为系统默认 -->
                            <!--<el-dropdown-item icon="el-icon-setting" command="setAsSystemDefault" v-show="promptManagement && !scope.row.systemActive && !scope.row.isDraft">Set as System Default</el-dropdown-item>-->
                            <!-- 删除 -->
                            <el-dropdown-item icon="el-icon-delete" class="text-danger" command="deletePrompt" :disabled="scope.row.createUserName === 'System' || scope.row.systemActive">Delete</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
        <div class="display-flex flex-justify-end">
            <!-- 分页 -->
            <el-pagination
                class="m-t-sm right"
                background
                layout="total, prev, pager, next, sizes"
                @size-change="handleSizeChange"
                @current-change="handlePageChange"
                :current-page="currentPage"
                :page-sizes="[10, 20]"
                :page-size="pageSize"
                :total="total">
            </el-pagination>
        </div>
        <!-- 对比弹窗 -->
        <PromptDiffDialog
            v-if="promptDiffDialogVisible"
            :visible.sync="promptDiffDialogVisible"
        />
    </div>
</template>

<script>
import { mapState } from 'vuex'
import PromptDiffDialog from '../diff/PromptDiffDialog.vue'

export default {
    components: {
        PromptDiffDialog, // 对比弹窗
    },
    
    props: {
        // Prompt 场景
        scene: {
            type: String,
            default: null,
        },

        // 能否编辑
        disableEdit: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            prompts: [], // Prompt 列表
            currentPage: 1, // 当前页
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            listPromptsLoading: false, // 获取 Prompt 列表 loading
            changedActivePrompt: false, // 是否修改了活跃 Prompt
            promptDiffDialogVisible: false, // 对比弹窗
            personalBestPromptId: null, // 个人最佳 Prompt ID
        }
    },

    created() {
        console.log('init PromptVersionList.vue')
        this.getPromptList()
    },

    computed: {
        ...mapState({
            promptDebugging: state => state.curriculum.promptDebugging, // Prompt 调试
            promptManagement: state => state.curriculum.promptManagement, // Prompt 管理
        }),

        // 格式化日期
        formatDate() {
            return function (date) {
                if (!date) {
                    return ''
                }
                date = this.$moment.utc(date).local()
                return date.format('MM/DD') + ' ' + date.format('hh:mm a')
            }
        },
    },
 
    methods: {
        // 获取 Prompt 列表
        getPromptList() {
            // 开始 loading
            this.listPromptsLoading = true
            // 请求接口
            this.$axios.get($api.urls().listPromptsByScene, {
                params: {
                    pageNum: this.currentPage,
                    pageSize: this.pageSize,
                    scene: this.scene,
                }
            }).then(res => {
                this.prompts = res.items // Prompt 列表
                this.total = res.total // 总条数
                this.personalBestPromptId = res.personalBestPromptId // 个人最佳 Prompt ID
                this.listPromptsLoading = false
            }).catch(error => {
                this.listPromptsLoading = false
                console.log(error)
                this.$message.error(error.response.data.error_message)
            })
        },

        // 编辑 Prompt
        editPrompt(prompt) {
            if (this.disableEdit) {
                return
            }
            // Prompt ID
            let promptId = prompt.id
            // 打开预览页面
            this.$emit('previewPrompt', promptId)
        },

        // 应用 Prompt
        applyPrompt(prompt) {
            // Prompt ID
            let promptId = prompt.id
            // 开始 loading
            this.$set(prompt, 'applyPromptLoading', true)
            // 请求接口
            this.$axios.post($api.urls().setUserCurrentPrompt, {
                id: promptId, // Prompt ID
                scene: this.scene, // Prompt 场景
            }).then(() => {
                this.changedActivePrompt = true
                this.$store.commit('curriculum/SET_CURRENT_PROMPT_ID', promptId)
                // 其余 Prompt 设为不活跃
                this.prompts.forEach(item => {
                    if (item.userActive) {
                        item.userActive = false
                    }
                })
                // 当前 Prompt 设置为活跃
                prompt.userActive = true
            this.$set(prompt, 'applyPromptLoading', false)
                // 成功提示
                this.$message.success('This prompt has been applied as the default.')
            }).catch(error => {
            this.$set(prompt, 'applyPromptLoading', false)
                console.log(error)
                this.$message.error(error.response.data.error_message)
            })
        },

        setAsBest(prompt) {
            // Prompt ID
            let promptId = prompt.id
            // 开始 loading
            this.$set(prompt, 'applyPromptLoading', true)
            // 请求接口
            this.$axios.post($api.urls().setUserBestPrompt, {
                id: promptId, // Prompt ID
                scene: this.scene, // Prompt 场景
            }).then(() => {
                // 其余 Prompt 设为不活跃
                this.prompts.forEach(item => {
                    if (item.userBestActive) {
                        item.userBestActive = false
                    }
                })
                // 当前 Prompt 设置为活跃
                prompt.userBestActive = true
                this.$set(prompt, 'applyPromptLoading', false)
                this.personalBestPromptId = promptId
                // 成功提示
                this.$message.success('This prompt has been set up as your personal best version.')
            }).catch(error => {
                this.$set(prompt, 'applyPromptLoading', false)
                console.log(error)
                this.$message.error(error.response.data.error_message)
            })
        },

        setAsSystemDefault(prompt) {
            // Prompt ID
            let promptId = prompt.id
            this.$set(prompt, 'applyPromptLoading', true)
            // 请求接口
            this.$axios.post($api.urls().setSystemCurrentPrompt, {
                id: promptId, // Prompt ID
                scene: this.scene, // Prompt 场景
            }).then(() => {
                // 其余 Prompt 设为不活跃
                this.prompts.forEach(item => {
                    if (item.systemActive) {
                        item.systemActive = false
                    }
                })
                // 当前 Prompt 设置为活跃
                prompt.systemActive = true
                this.$set(prompt, 'applyPromptLoading', false)
                // 成功提示
                this.$message.success('Set System Default Prompt Success.')
            }).catch(error => {
                this.$set(prompt, 'applyPromptLoading', false)
                console.log(error)
                this.$message.error(error.response.data.error_message)
            })
        },

        deletePrompt(prompt) {
            // Prompt ID
            let promptId = prompt.id
            this.$confirm('Are you sure you want to delete this prompt?', 'Confirm', {
                confirmButtonText: 'Confirm',
                cancelButtonText: 'Cancel',
            }).then(() => {
                this.$axios.post($api.urls().deletePrompt, {
                    id: promptId,
                }).then(() => {
                    this.$message({
                        type: 'success',
                        message: 'Delete successfully!'
                    });
                    this.getPromptList()
                }).catch(error => {
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                })
            }).catch(() => {
            })
        },

        // 和系统默认比较
        compareWithSystemDefault(prompt) {
            // this.promptDiffDialogVisible = true
            let promptId = prompt.id
            this.$emit('comparePrompt', promptId, null, 'WITH_SYSTEM_DEFAULT')
        },

        // 和个人最佳比较
        compareWithBest(prompt) {
            let promptId = prompt.id
            this.$emit('comparePrompt', promptId, this.personalBestPromptId, 'WITH_PERSONAL_BEST')
        },

        // 页码改变
        handlePageChange(page) {
            this.currentPage = page
            this.getPromptList()
        },

        // 每页条数改变
        handleSizeChange(size) {
            this.pageSize = size
            this.getPromptList()
        },

        // 点击行事件
        handleRowClick(row, column, event) {
            this.editPrompt(row)
        },

        // 更多菜单
        handleCommand(command, prompt) {
            if (command === 'setAsBest') {
                this.setAsBest(prompt)
            } else if (command === 'setAsSystemDefault') {
                this.setAsSystemDefault(prompt)
            } else if (command === 'deletePrompt') {
                this.deletePrompt(prompt)
            } else if (command === 'compareWithSystemDefault') {
                this.compareWithSystemDefault(prompt)
            } else if (command === 'compareWithBest') {
                this.compareWithBest(prompt)
            }
        }
    },
}
</script>

<style lang="less" scoped>
.prompt-table {
    flex: auto;
    min-height: 0;
}
::v-deep {
    .el-table__row {
        cursor: pointer;
    }
    .el-tag-danger {
        border-radius: 35px;
        background-color: #FDE6E6;
        color: #F56C6C;
    }
    .el-tag-primary {
        border-radius: 35px;
        background-color: #DDF2F3;
        color: #10B3B7;
    }
    .el-tag-warning {
        border-radius: 35px;
        background-color: #FFF3DC;
        color: #FF7F41;
    }
    .more-btn {
        padding-right: 30px;
        margin-right: -30px;
    }
}
</style>