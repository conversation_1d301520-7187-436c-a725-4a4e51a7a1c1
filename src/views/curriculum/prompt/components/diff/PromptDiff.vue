<template>
    <div class="display-flex flex-direction-col prompt-diff">
        <!-- 切换按钮 -->
        <div class="lg-tabs-small m-b-md display-flex justify-content">
            <el-tabs v-model="currentView">
                <el-tab-pane label="Creation Prompt Comparison" name="CREATION_PROMPT"></el-tab-pane>
                <el-tab-pane label="Evaluation Prompt Comparison" name="EVALUATION_PROMPT" v-if="oldPrompt && oldPrompt.evaluatePromptTemplate"></el-tab-pane>
                <el-tab-pane label="Content Comparison" name="CONTENT" v-if="oldPromptCompletion && newPromptCompletion"></el-tab-pane>
            </el-tabs>
        </div>
        <!-- 对比标题 -->
        <el-card class="m-b-md dff-prompt-info" v-if="oldPrompt && newPrompt">
            <div class="display-flex justify-content-between">
                <!-- 原版本 -->
                <div class="old-prompt">
                    <div class="font-size-16 font-bold m-b-xs">Prompt {{ oldPrompt.version }}</div>
                    <div>
                        <span>{{ oldPrompt.createUserName }}</span>
                        <span class="m-l-xs">{{ oldPrompt.createAtUtc }}</span>
                    </div>
                </div>
                <!-- 图标 -->
                <div class="display-flex justify-content align-items">
                    <div class="vs-icon display-flex justify-content align-items">VS</div>
                </div>
                <!-- 新版本 -->
                <div class="new-prompt text-right m-b-xs">
                    <div class="font-size-16 font-bold">Prompt {{ newPrompt.version }}</div>
                    <div>
                        <span>{{ oldPrompt.createUserName }}</span>
                        <span class="m-l-xs">{{ oldPrompt.createAtUtc }}</span>
                    </div>
                </div>
            </div>
        </el-card>
        <!-- 对比内容 -->
        <div v-html="diffHtml" class="diff-html"></div>
    </div>
</template>

<script>
import * as Diff2Html from 'diff2html';
import * as Diff from 'diff';
import 'diff2html/bundles/css/diff2html.min.css';

export default {
    props: {
        // 旧 Prompt ID
        oldPromptId: {
            type: String,
            default: null,
        },

        // 新 Prompt ID
        newPromptId: {
            type: String,
            default: null,
        },
    },

    data() {
        return {
            currentView: 'CREATION_PROMPT', // 当前视图
            getPromptComparisonLoading: false, // 获取 Prompt 对比信息加载状态
            oldPrompt: null, // 旧 Prompt
            oldPromptCompletion: null, // 旧 Prompt 生成内容
            newPrompt: null, // 新 Prompt
            newPromptCompletion: null, // 新 Prompt 生成内容
            diffStr: null, // 对比内容
        }
    },

    computed: {
        diffHtml() {
            if (!this.diffStr) {
                return ''
            }
            console.log(this.diffStr)
            return Diff2Html.html(this.diffStr, {
                drawFileList: false,
                matching: 'lines',
                outputFormat: 'side-by-side'
            });
        }
    },

    watch: {
        // 监听视图变化
        currentView() {
            this.updateDiffStr()
        },
    },

    methods: {
        // 获取 Prompt 对比信息
        async getPromptComparisonInfo() {
            // 开始加载
            this.getPromptComparisonLoading = true
            return new Promise((resolve, reject) => {
                // 请求接口
                this.$axios.get($api.urls().getComparisonInfo, {
                    params: {
                        oldPromptId: this.oldPromptId,
                        newPromptId: this.newPromptId,
                    }
                }).then(res => {
                    this.oldPrompt = res.oldPrompt // 旧 Prompt
                    this.oldPromptCompletion = res.oldPromptCompletion // 旧 Prompt 生成内容
                    this.newPrompt = res.newPrompt // 新 Prompt
                    this.newPromptCompletion = res.newPromptCompletion // 新 Prompt 生成内容
                    this.updateDiffStr() // 更新对比内容
                    this.getPromptComparisonLoading = false
                    resolve()
                }).catch(error => {
                    this.getPromptComparisonLoading = false
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },

        // 更新对比内容
        updateDiffStr() {
            // 没有加载完成
            if (!this.oldPrompt || !this.newPrompt) {
                return null
            }
            let oldStr = null
            let newStr = null
            // 内容 Prompt 对比
            if (this.currentView === 'CREATION_PROMPT') {
                oldStr = this.oldPrompt.promptTemplate
                newStr = this.newPrompt.promptTemplate
            } else if (this.currentView === 'EVALUATION_PROMPT') {
                // 评估 Prompt 对比
                oldStr = this.oldPrompt.evaluatePromptTemplate
                newStr = this.newPrompt.evaluatePromptTemplate
            } else if (this.currentView === 'CONTENT') {
                // 生成内容对比
                oldStr = this.oldPromptCompletion
                newStr = this.newPromptCompletion
            }
            // 结尾添加不一样的内容，否则内容一样时，对比结果为空
            newStr = newStr + '\na'
            oldStr = oldStr + '\nb'
            this.diffStr = Diff.createTwoFilesPatch('a', 'b', oldStr, newStr, undefined, undefined, {
                context: 1000,
                ignoreWhitespace: false
            })
        },
    },
}
</script>

<style lang="less" scoped>
.dff-prompt-info {
    flex: auto;
    flex-shrink: 0;
    flex-grow: 0;
}
.prompt-diff, .diff-html {
    flex: auto;
    min-height: 0;
}
.vs-icon {
    width: 29px;
    height: 29px;
    background: linear-gradient(135deg, #11EE69 12.5%, #15B6E9 84.72%);
    border-radius: 90px;
    color: #fff;
}
.old-prompt, .new-prompt {
    width: 40%;
}

::v-deep {
    .d2h-diff-table {
        table-layout: fixed;
    }
    .d2h-file-header, .d2h-code-side-linenumber, .d2h-diff-tbody > tr:first-child, .d2h-diff-tbody > tr:last-child, .d2h-code-line-prefix {
        display: none;
    }
    .d2h-code-side-line {
        padding: 0 10px;
        white-space: normal;
    }
    .d2h-code-line-ctn {
        white-space: normal;
    }
    .d2h-files-diff > .d2h-file-side-diff:first-child {
        margin-right: 5px;
    }
    .d2h-files-diff > .d2h-file-side-diff:last-child {
        margin-left: 5px;
    }
    .d2h-file-side-diff {
        overflow-x: auto;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        border-radius: 4px;
        padding: 15px;
    }
    .d2h-file-wrapper {
        border: none;
    }
    .diff-html {
        overflow-y: auto;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    }
}

</style>