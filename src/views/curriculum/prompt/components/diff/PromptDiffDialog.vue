<template>
    <div>
        <el-dialog
            class=""
            fullscreen
            append-to-body
            :close-on-click-modal="false"
            :visible="visible">
            <PromptDiff/>
        </el-dialog>
    </div>
</template>

<script>
import PromptDiff from './PromptDiff.vue'

export default {
    components: {
        PromptDiff, // Prompt Diff
    },

    props: {
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
        }
    },
}
</script>

<style lang="less" scoped>

</style>