<template>
    <div class="h-full display-flex flex-direction-col">
        <!-- 头部信息 -->
        <div class="display-flex justify-content-between align-items m-b">
            <!-- 小孩总数 -->
            <div>
                <span v-show="childCount" class="font-bold">Total children: {{ childCount }}</span>
            </div>
            <!-- 操作 -->
            <div v-show="editable">
                <el-button type="" @click="createChild" icon="el-icon-coin">Add Child</el-button>
            </div>
            <!-- 跳转到单独页面 -->
            <el-button v-show="!editable">
                <a class="lg-pointer" :href="rosterPath" target="_blank">
                    <i class="el-icon-s-tools font-size-18" ></i>
                    Classroom Demographics Management
                </a>
            </el-button>
        </div>
        <div v-loading="listChildrenLoading" class="display-flex flex-direction-col prompt-table flex-auto min-height-0">
            <el-table
                :data="children"
                :height="tableHeight"
                class="w-full">
                <!-- 小孩名字 -->
                <el-table-column label="Child Name" :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                        <span>{{ scope.row.displayName }}</span>
                    </template>
                </el-table-column>
                <!-- 种族 -->
                <el-table-column label="Hispanic">
                    <template slot-scope="scope">
                        <el-tooltip  placement="top">
                            <div slot="content">{{ getAttrValue(scope.row, 'Hispanic') }} </div>
                            <span class="ellipsis">{{ getAttrValue(scope.row, 'Hispanic') }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <!-- 种族 -->
                <el-table-column label="Race">
                    <template slot-scope="scope">
                        <el-tooltip  placement="top">
                            <div slot="content">{{ getAttrValue(scope.row, 'Race') }} </div>
                            <span class="ellipsis">{{ getAttrValue(scope.row, 'Race') }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <!-- 语言 -->
                <el-table-column label="Home Language">
                    <template slot-scope="scope">
                        <span>{{ getAttrValue(scope.row, 'Language') }}</span>
                    </template>
                </el-table-column>
                <!-- ELD -->
                <el-table-column label="ELD" width="100">
                    <template slot-scope="scope">
                        <span>{{ getAttrValue(scope.row, 'ELD') }}</span>
                    </template>
                </el-table-column>
                <!-- IEP -->
                <el-table-column label="IEP/IFSP" width="100">
                    <template slot-scope="scope">
                        <span>{{ getAttrValue(scope.row, 'IEP/IFSP') }}</span>
                    </template>
                </el-table-column>
                <!-- Special education eligibility -->
                <el-table-column label="Special Education Eligibility">
                    <template slot-scope="scope">
                        <span>{{ getAttrValue(scope.row, 'Special education eligibility') }}</span>
                    </template>
                </el-table-column>
                <!-- Comments -->
                <el-table-column label="Comments">
                    <template slot-scope="scope">
                        <el-tooltip  placement="top">
                            <div slot="content">{{ getAttrValue(scope.row, 'Comments') }} </div>
                            <span class="ellipsis">{{ getAttrValue(scope.row, 'Comments') }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <!-- Adaptations -->
                <el-table-column label="Adaptations">
                    <template slot-scope="scope">
                        <el-tooltip  placement="top">
                            <div slot="content">{{ getAttrValue(scope.row, 'Adaptations') }} </div>
                            <span class="ellipsis">{{ getAttrValue(scope.row, 'Adaptations') }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <!-- IEP Goal -->
                <el-table-column label="IEP Goal">
                    <template slot-scope="scope">
                        <el-tooltip  placement="top">
                            <div slot="content">{{ getAttrValue(scope.row, 'IEP Goal') }} </div>
                            <span class="ellipsis">{{ getAttrValue(scope.row, 'IEP Goal') }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column label="Actions" width="120" fixed="right" v-if="editable">
                    <template slot-scope="scope">
                        <!-- 编辑 -->
                        <el-button class="font-size-18 color-676879" type="text" icon="el-icon-edit" @click.stop="editChild(scope.row)"></el-button>
                        <!-- 删除 -->
                        <el-button class="font-size-18 color-676879" type="text" icon="el-icon-delete" @click.stop="deleteChild(scope.row)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 编辑小孩弹窗 -->
        <EditChildDialog
            v-if="editChildDialogVisible"
            :visible.sync="editChildDialogVisible"
            :child="currentEditChild"
            :groupId="groupId"
            @updateChild="updateChild"
        ></EditChildDialog>
    </div>
</template>

<script>
import EditChildDialog from './EditChildDialog.vue'

export default {
    components: {
        EditChildDialog
    },

    props: {
        // 班级 ID
        groupId: {
            type: String,
            default: null,
        },

        // 表格高度
        tableHeight: {
            type: String,
            default: '100%'
        },

        // 是否可编辑
        editable: {
            type: Boolean,
            default: true,
        },
    },

    data() {
        return {
            listChildrenLoading: false, // 获取小孩列表 Loading
            children: [], // 小孩列表
            childCount: null, // 小孩数
            editChildDialogVisible: false, // 是否显示小孩弹窗
            currentEditChild: null, // 当前编辑的小孩
            rosterPath: this.$router.resolve({ name: 'roster', }).href, // Roster 页面路由
        }
    },

    created() {
    },

    watch: {
        // 监听班级 ID 变化
        groupId: {
            handler: function (val, oldVal) {
                if (val) {
                    this.listChildren()
                }
            },
            immediate: true,
        },
    },

    computed: {
        // 获取指定属性的值
        getAttrValue() {
            return function(child, attrName) {
                // 不存在返回空
                if (!child || !attrName) {
                    return ''
                }
                // 属性列表
                let attrs = child.attrs
                if (!attrs) {
                    return ''
                }
                // 匹配到的属性值
                let matchValues = null
                // 遍历属性列表
                attrs.forEach(attr => {
                    // 匹配属性名称
                    if (attr && attr.name && attr.name.trim().toLowerCase() === attrName.trim().toLowerCase()) {
                        // 属性值
                        let attrValues = attr.values
                        if (attrValues && attrValues.length > 0) {
                            matchValues = attrValues
                        }
                    }
                })
                // 如果有属性值，以逗号分割
                if (matchValues) {
                    return matchValues.join(', ')
                }
                // 没有值
                return ''
            }
        }
    },

    methods: {
        // 获取小孩列表
        listChildren() {
            // 开始 Loading
            this.listChildrenLoading = true
            // 参数
            let params = {
                pageNum: 1,
                pageSize: 1000,
                sort: 'lastName',
                order: 'asc',
                groupId: this.groupId
            }
            // 调用接口
            return new Promise((resolve, reject) => {
                this.$axios.get($api.urls().manageChildren, { params: params }).then(res => {
                    this.children = res.results // 小孩列表
                    this.childCount = res.total // 小孩总数
                    // 停止 Loading
                    this.listChildrenLoading = false
                    resolve()
                }).catch(error => {
                    // 停止 Loading
                    this.listChildrenLoading = false
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },

        // 添加小孩
        createChild() {
            this.currentEditChild = null
            // 打开编辑弹窗
            this.editChildDialogVisible = true
        },

        // 编辑小孩
        editChild(child) {
            this.currentEditChild = child
            // 打开编辑弹窗
            this.editChildDialogVisible = true
        },

        // 删除小孩
        deleteChild(child) {
            this.$confirm('Are you sure you want to delete this child?', 'Confirm', {
                confirmButtonText: 'Confirm',
                cancelButtonText: 'Cancel',
            }).then(() => {
                this.$axios.delete($api.urls(null, null, null, null, child.id).deleteChild).then(() => {
                    this.$message({
                        type: 'success',
                        message: 'Delete successfully!'
                    });
                    this.listChildren()
                }).catch(error => {
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                })
            }).catch(() => {
            })
        },

        // 更新小孩后刷新
        updateChild() {
            this.listChildren()
        },
    },
}
</script>

<style lang="less" scoped>
.ellipsis {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 显示两行 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
    word-break: keep-all;
}

</style>