<template>
    <div class="">
        <el-dialog
            class=""
            width="600px"
            :close-on-click-modal="false"
            :before-close="handleClose"
            :visible="visible">
            <!-- 小孩信息 -->
            <ChildInfo
                ref="childInfo"
                :child="child"
                :groupId="groupId"
            />
            <!-- 操作 -->
            <div class="display-flex justify-content-end m-t-sm">
                <!-- 取消 -->
                <el-button type="default" @click="close">Cancel</el-button>
                <!-- 保存 -->
                <el-button type="primary" @click="saveChild" :loading="saveChildLoading">Save</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import ChildInfo from './ChildInfo.vue'

export default {
    components: {
        ChildInfo
    },

    props: {
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false,
        },
        
        // 小孩信息
        child: {
            type: Object,
            default: null
        },

        // 班级 ID
        groupId: {
            type: String,
            default: null
        }
    },

    data() {
        return {
            saveChildLoading: false, // 保存小孩 Loading
        }
    },

    created() {
    },

    methods: {
        // 关闭弹窗前回调
        handleClose(done) {
            // 更新显示状态
            this.$emit('update:visible', false)
            // 关闭弹窗
            done()
        },

        // 关闭
        close() {
            this.$emit('update:visible', false)
        },

        // 保存小孩
        async saveChild() {
            this.saveChildLoading = true
            try {
                let updated = await this.$refs.childInfo.saveChild()
                this.saveChildLoading = false
                // 没有保存
                if (!updated) {
                    return
                }
                this.$emit('updateChild')
                this.close()
            } catch(err) {
                this.saveChildLoading = false
            }
        },
    },
}
</script>

<style lang="less" scoped>

</style>