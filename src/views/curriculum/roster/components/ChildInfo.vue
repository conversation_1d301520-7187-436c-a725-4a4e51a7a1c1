<template>
    <div class="">
        <el-form
            ref="childInfoFormRef"
            label-position="top"
            :model="childInfo"
            :rules="childInfoFormRules"
        >
            <!-- 姓名 -->
            <el-row :gutter="10" class="">
                <el-col :span="8" class="">
                    <!-- First Name -->
                    <el-form-item label="First Name" prop="firstName" class="w-full">
                        <el-input v-model="childInfo.firstName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" class="">
                    <!-- Middle Name -->
                    <el-form-item label="Middle Name" prop="middleName" class="w-full">
                        <el-input v-model="childInfo.middleName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" class="">
                    <!-- Last Name -->
                    <el-form-item label="Last Name" prop="lastName" class="w-full">
                        <el-input v-model="childInfo.lastName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <!-- 属性信息 -->
            <el-skeleton :rows="18" animated :loading="getAttrLoading">
                <template>
                    <div v-for="(attrGroup, groupIndex) in attrGroups" :key="'group-' + groupIndex" v-show="displayAttrGroup(attrGroup)">
                        <!-- 属性组名称 -->
                        <div class="font-size-16 font-bold bg m-b-sm attr-group-desc">
                            <span>{{ attrGroup.name }}</span>
                        </div>
                        <!-- 属性 -->
                        <div v-for="(attr, attrIndex) in attrGroup.attrs" :key="'attr-' + attrIndex" class="m-l-sm" v-show="displayAttr(attr)">
                            <!-- 多选 -->
                            <template v-if="attr.typeValue === 'MULTIPLE_CHOICES'">
                                <el-form-item :label="attrTitle(attr)" :prop="attr.name" class="w-full">
                                    <el-checkbox-group v-model="attr.values">
                                        <el-checkbox v-for="(item, valueIndex) in attr.valueList" :key="'value-' + valueIndex" :label="item.name">{{ item.displayName }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </template>
                            <!-- 单选 -->
                            <template v-if="attr.typeValue === 'SINGLE_CHOICE'">
                                <el-form-item :label="attrTitle(attr)" :prop="attr.name" class="w-full">
                                    <el-radio-group v-model="attr.values" :disabled="attr.name === 'ELD'">
                                        <el-radio v-for="(item, valueIndex) in attr.valueList" :key="'value-' + valueIndex" :label="item.name">{{ item.displayName }}</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </template>
                            <!-- 文本 -->
                            <template v-if="attr.typeValue === 'TEXT_FIELD'">
                                <el-form-item :label="attrTitle(attr)" :prop="attr.name" class="w-full">
                                    <el-input v-model="attr.values" :rows="5"  type="textarea"  maxlength="1000" v-if="attr.description == 'Comments'" :placeholder="$t('loc.IEPComments')" />
                                    <el-input v-model="attr.values" :rows="5"  type="textarea"  maxlength="1000"  v-else :placeholder="$t('loc.IEPGoal')" />
                                </el-form-item>
                            </template>
                        </div>
                    </div>
                </template>
            </el-skeleton>
        </el-form>
    </div>
</template>

<script>

export default {
    components: {
    },

    props: {
        // 小孩信息
        child: {
            type: Object,
            default: null
        },

        // 班级 ID
        groupId: {
            type: String,
            default: null
        }
    },

    data() {
        return {
            childInfoFormRules: {}, // 表单校验规则
            childInfo: {}, // 学生信息
            edit: false, // 是否是编辑
            getAttrLoading: false, // 获取属性 Loading
            attrGroups: [], // 属性组
            displayAttrNames: ['Hispanic', 'Race', 'Language', 'ELD', 'IEP/IFSP', 'Adaptations', 'Special education eligibility','Comments','IEP Goal'], // 显示的属性名称列表
            iepAttrNames: ['Adaptations', 'Special education eligibility','Comments','IEP Goal'],
            showIepAttrs: false, // 是否显示 IEP 相关属性
        }
    },

    created() {
        this.initChildInfo()
        this.initFormRules()
    },

    computed: {
        // 是否显示属性
        displayAttr() {
            return function(attr) {
                if (!attr) {
                    return false
                }
                // 属性名
                let attrName = attr.name
                // 是否是 IEP 属性
                if (!this.showIepAttrs && this.iepAttrNames.includes(attrName)) {
                    return false
                }
                return this.displayAttrNames.includes(attrName)
            }
        },

        // 是否显示属性组
        displayAttrGroup() {
            return function(attrGroup) {
                if (!attrGroup) {
                    return false
                }
                // 组下有要显示的属性，则显示该组
                let attrs = attrGroup.attrs
                // 是否有要显示的属性
                let hasDisplayAttr = false
                attrs.forEach(attr => {
                    if (this.displayAttr(attr)) {
                        hasDisplayAttr = true
                    }
                })
                return hasDisplayAttr
            }
        },

        // 属性标题
        attrTitle() {
            return function(attr) {
                let no = attr.no ? attr.no + '. ' : ''
                return no + attr.description
            }
        },

        // 语言属性
        languageValues() {
            return this.getAttrValues('Language')
        },

        // IEP 属性
        iepValue() {
            return this.getAttrValues('IEP/IFSP')
        },
    },

    watch: {
        // 监听语言变化
        languageValues: {
            handler: function(values) {
                if (!this.attrGroups) {
                    return
                }
                // 没有值，则清空 ELD
                if (!values || values.length === 0) {
                    this.setAttrValues('ELD', null)
                    return
                }
                // 有且只有英语，ELD 为 no，否则为 yes
                if (values.length === 1 && values[0] === 'English') {
                    this.setAttrValues('ELD', 'No')
                } else {
                    this.setAttrValues('ELD', 'Yes')
                }
            }
        },

        // 监听 IEP 变化
        iepValue: {
            handler: function(values) {
                if (values && values.length > 0 && values[0] === 'Yes') {
                    this.showIepAttrs = true
                } else {
                    this.showIepAttrs = false
                }
            }
        }
    },

    methods: {
        // 初始化小孩信息
        initChildInfo() {
            // 获取小孩属性
            this.getChildAttrs()
            // 没有传小孩信息，则为新增
            if (!this.child || !this.child.id) {
                this.edit = false
                this.childInfo = {}
                return
            }
            // 编辑小孩
            this.childInfo = JSON.parse(JSON.stringify(this.child))
            this.edit = true
        },

        // 设置表单校验规则
        initFormRules() {
            this.childInfoFormRules = {
                firstName: [
                    { required: true, message: 'Please enter first name.', trigger: 'change' },
                ],
                lastName: [
                    { required: true, message: 'Please enter last name.', trigger: 'change' },
                ],
                // Comments: [
                //     { required: true, message: 'Please provide more specific details about the child\'s unique needs and challenges for tailored support.', trigger: 'blur' },
                // ],
                // IEPGoal: [
                //     { required: true, message: 'Please enter child\'s IEP goals here...', trigger: 'blur' },
                // ],
            }
        },

        // 获取小孩属性
        getChildAttrs() {
            this.getAttrLoading = true
            return new Promise((resolve, reject) => {
                let params = {}
                if (this.child && this.child.id) {
                    params['childId'] = this.child.id
                } else {
                    params['type'] = 'psc'
                }
                this.$axios($api.urls().getEnrollmentAttrs, { params: params }).then(res => {
                    this.attrGroups = res
                    this.parseAttrValues()
                    // 停止 Loading
                    this.getAttrLoading = false
                    resolve()
                }).catch(error => {
                    // 停止 Loading
                    this.getAttrLoading = false
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },

        // 解析属性选中值
        parseAttrValues() {
            let attrGroups = this.attrGroups
            attrGroups.forEach(attrGroup => {
                // 分组下属性
                let attrs = attrGroup.attrs
                if (!attrs) {
                    return
                }
                // 遍历属性
                attrs.forEach(attr => {
                    let valueList = attr.valueList
                    // 文本
                    if (attr.value) {
                        if (attr.typeValue === 'TEXT_FIELD') {
                        this.$set(attr, 'values', attr.value)
                    }
                    }
                    if (!valueList) {
                        return
                    }
                    let values = []
                    valueList.forEach(value => {
                        if (value && value.selected) {
                            values.push(value.name)
                        }
                    })
                    if (!values || values.length === 0) {
                        return
                    }
                    // 多选
                    if (attr.typeValue === 'MULTIPLE_CHOICES') {
                        this.$set(attr, 'values', values)
                    }
                    // 单选
                    if (attr.typeValue === 'SINGLE_CHOICE') {
                        this.$set(attr, 'values', values[0])
                    }
                })
            })
        },

        // 格式保存信息
        formatSaveParams() {
            // 没有学生信息
            if (!this.childInfo) {
                return
            }
            // 学生信息
            let params = {
                birthDate: '01/01/2022',
                enrollmentDate: '07/01/2023',
                gender: 'FEMALE',
                groupId: this.groupId,
                ...this.childInfo
            }
            // 属性信息
            if (!this.attrGroups) {
                params['attrs'] = []
                return
            }
            // 是否是 IEP
            let isIEP = false
            // 属性值
            let attrs = []
            // 遍历属性组
            this.attrGroups.forEach(attrGroup => {
                if (!attrGroup.attrs) {
                    return
                }
                // 遍历属性
                attrGroup.attrs.forEach(attr => {
                    // 选择的值
                    let values = attr.values
                    // 没有则跳过
                    if (!values) {
                        return
                    }
                    // 要保存的属性
                    let updateAttr = {
                        name: attr.name,
                        values: []
                    }
                    // 多选
                    if (attr.typeValue === 'MULTIPLE_CHOICES' && values.length > 0) {
                        values.forEach(value => {
                            updateAttr.values.push({
                                name: value
                            })
                        })
                    }
                    // 单选
                    if (attr.typeValue === 'SINGLE_CHOICE' && values.length > 0) {
                        updateAttr.values.push({
                            name: values
                        })
                    }
                    // 文本
                    if (attr.typeValue === 'TEXT_FIELD' && values.length > 0) {
                        updateAttr.values.push({
                            name: values
                        })
                    }
                    if (!updateAttr.values || updateAttr.values.length === 0) {
                        updateAttr.values = []
                    }
                    if (updateAttr.name === 'IEP/IFSP' && updateAttr.values[0].name === 'Yes') {
                        isIEP = true
                    }
                    attrs.push(updateAttr)
                })
            })
            // 如果不是 IEP，清掉相关的属性值
            if (!isIEP) {
                attrs.forEach(attr => {
                    if (this.iepAttrNames.includes(attr.name)) {
                        attr.values = []
                    }
                })
            }
            console.log(attrs, 'attrs')
            params['attrs'] = attrs
            return params
        },

        // 根据属性名称获取属性值
        getAttrValues(attrName) {
            if (!this.attrGroups || this.attrGroups.length === 0) {
                return []
            }
            let result = []
            this.attrGroups.forEach(attrGroup => {
                // 分组下属性
                let attrs = attrGroup.attrs
                if (!attrs) {
                    return
                }
                // 遍历属性
                attrs.forEach(attr => {
                    let valueList = attr.valueList
                    if (!valueList) {
                        return
                    }
                    // 属性名
                    let name = attr.name
                    if (!name || name !== attrName) {
                        return
                    }
                    // 属性值
                    let values = attr.values
                    // 多选
                    if (attr.typeValue === 'MULTIPLE_CHOICES' && values.length > 0) {
                        result = values
                    }
                    // 单选
                    if (attr.typeValue === 'SINGLE_CHOICE' && values.length > 0) {
                        result = [values]
                    }
                })
            })
            return result
        },

        // 设置属性值
        setAttrValues(attrName, attrValues) {
            if (!this.attrGroups || this.attrGroups.length === 0) {
                return []
            }
            this.attrGroups.forEach(attrGroup => {
                // 分组下属性
                let attrs = attrGroup.attrs
                if (!attrs) {
                    return
                }
                // 遍历属性
                attrs.forEach(attr => {
                    // 属性名
                    let name = attr.name
                    if (!name || name !== attrName) {
                        return
                    }
                    // 设置属性值
                    this.$set(attr, 'values', attrValues)
                })
            })
        },

        // 保存小孩信息
        async saveChild() {
            // 验证表单信息
            let valid = await new Promise((resolve, reject) => {
                this.$refs.childInfoFormRef.validate(valid => {
                    if (valid) {
                        resolve(true)
                    } else {
                        resolve(false)
                    }
                })
            })
            console.log('valid', valid)
            if (!valid) {
                return false
            }
            // 更新
            if (this.childInfo && this.childInfo.id) {
                await this.updateChild()
                this.$message.success('Update child successfully.')
                return true
            }
            // 新增
            await this.createChild()
            await this.updateChild()
            this.$message.success('Create child successfully.')
            return true
        },

        // 创建小孩
        async createChild() {
            // 更新参数
            let params = this.formatSaveParams()
            return new Promise((resolve, reject) => {
                this.$axios.post('/students', params).then(res => {
                    resolve()
                }).catch(error => {
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },

        // 更新小孩
        async updateChild() {
            // 小孩 ID
            let childId = this.childInfo.id
            if (!childId) {
                return
            }
            // 更新参数
            let params = this.formatSaveParams()
            return new Promise((resolve, reject) => {
                this.$axios.put('/students/' + childId + '?web=1', params).then(res => {
                    resolve()
                }).catch(error => {
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },
    },
}
</script>

<style lang="less">
.cg-child-info-dialog {
    width: 200px;   
}
</style>

<style lang="less" scoped>
.attr-group-desc {
    padding: 6px 12px;
}
::v-deep {
    .el-form-item__content {
        .el-radio-group, .el-checkbox-group {
            line-height: 20px;
        }
    }
}
</style>