<template>
    <div>
        <!-- 布局容器 -->
        <el-row :gutter="10">
            <!-- 上传文件 -->
            <el-col :span="uploadColSpan" v-show="canUploadFile">
                <div class="" :class="[hasFiles ? 'aspect-ratio-box-' + hasFileUploaderRatio : 'aspect-ratio-box-' + uploaderRatio]">
                    <div class="aspect-ratio-content">
                        <el-upload
                            class="medias-uploader"
                            :class="[uploaderTheme]"
                            drag
                            action
                            :file-list="files"
                            :show-file-list="false"
                            :http-request="uploadFile"
                            :accept="acceptTypes"
                            :before-upload="verifyUploadFile"
                            multiple>
                            <!-- 默认内容占位信息 -->
                            <slot name="placeholder-default" v-if="!hasFiles">
                                <i class="lg-icon lg-icon-picture font-size-40 text-primary"></i>
                                <el-button type="primary" round size="medium">
                                    <div class="display-flex justify-content align-items">
                                        <i class="lg-icon lg-icon-upload font-size-20 m-r-xs"></i>
                                        <span>Upload image</span>
                                    </div>
                                </el-button>
                                <div class="el-upload__text m-t-sm">
                                    <div>
                                        Drop/upload your Jpg, Png, Jpeg files here
                                    </div>
                                    <div class="text-muted font-size-12">
                                        Up to {{ maxFileCount }} images, max 20 MB each
                                    </div>
                                </div>
                            </slot>
                            <!-- 有文件时内容占位信息 -->
                            <slot name="placeholder-non-empty" v-else>
                                <i class="lg-icon lg-icon-add font-size-40 text-primary"></i>
                            </slot>
                        </el-upload>
                    </div>
                </div>
            </el-col>
            <!-- 文件预览列表 -->
            <el-col :span="priviewColSpan" v-show="hasFiles" v-for="(file, index) in files" :key="index">
                <div :class="['aspect-ratio-box-' + priviewRatio]" class="media-viewer">
                    <div class="aspect-ratio-content">
                        <template v-if="file.url">
                            <el-image
                                class="w-full h-full r-2x"
                                :src="file.url"
                                :preview-src-list="previewUrls"
                                :fit="'cover'">
                            </el-image>
                            <i class="el-icon-close" @click.stop="deleteFile(file)"></i>
                        </template>
                        <!-- 上传进度条 -->
                        <div v-else class="display-flex h-full flex-direction-col flex-justify-end upload-placeholder">
                            <el-progress
                                class="w-full"
                                :percentage="file.uploadProgressRate"
                                :show-text="false"
                                :stroke-width="5"/>
                        </div>
                    </div>
                    <div v-if="file.source" class="media-viewer__source" :title="file.source">
                        Image Source: {{ file.source }}
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import fileUtils from '@/utils/file.js'

export default {
    props: {
        // 接受的文件类型
        acceptTypes: {
            type: String,
            default: 'image/png,image/jpg,image/jpeg'
        },

        // 最大文件数量
        maxFileCount: {
            type: Number,
            default: 2
        },

        // 上传器宽高比例
        uploaderRatio: {
            type: String,
            default: '3-1'
        },

        // 有文件时上传器宽高比例
        hasFileUploaderRatio: {
            type: String,
            default: '1-1'
        },

        // 文件预览元素列比例
        priviewColSpan: {
            type: Number,
            default: 6
        },

        // 文件预览宽高比例
        priviewRatio: {
            type: String,
            default: '1-1'
        },

        // 上传器主题
        uploaderTheme: {
            type: String,
            default: 'theme-1'
        },

        // 展示文件列表
        previewFiles: {
            type: Array,
            default: () => []
        },
        // 当前课程
        currentLesson: {
          type: Object,
          default: () => {}
        }
    },

    data() {
        return {
            files: [], // 文件列表
            uploadProgressRate: 0, // 上传进度
            showProgress: false, // 是否显示进度条
        }
    },

    created() {
    },

    watch: {
        files: {
            handler (files) {
                this.$emit('change', files)
            },
            deep: true
        },

        previewFiles: {
            handler (files) {
                this.files = files
            },
            deep: true,
            immediate: true
        }
    },

    computed: {
        /**
         * 是否有文件
         */
        hasFiles () {
            return this.files && this.files.length > 0
        },

        /**
         * 文件上传元素列占比
         */
        uploadColSpan () {
            return this.hasFiles ? 6 : 24
        },

        /**
         * 是否可以上传文件
         */
        canUploadFile () {
            return !this.files || this.files.length < this.maxFileCount
        },

        /**
         * 预览地址列表
         */
        previewUrls () {
            if (!this.files || this.files.length === 0) {
                return []
            }
            return this.files.map(f => f.url)
        }
    },

    methods: {
        /**
         * 验证上传文件
         */
         verifyUploadFile (file) {
            // 上传文件是否为图片类型
            let isImage = /image\/.+/i.test(file.type)
            // 上传文件是否为视频类型
            let isVideo = /video\/.+/i.test(file.type)
            let fileSize = file.size / 1024 / 1024
            // 不能超出上传数量限制
            if (this.files.length >= this.maxFileCount) {
                return false
            }
            // 图片大小限制为 20M
            if (isImage && fileSize > 20) {
                this.$message.error(this.$t('loc.lessons2ImageUploadTips'))
                return false
            }
            // 视频大小限制为 100M
            if (isVideo && fileSize > 100) {
                this.$message.error(this.$t('loc.lessons2VideoUploadTips'))
                return false
            }
            // 校验文件类型
            let acceptable = this.acceptTypes.split(',').find(a => a.toLowerCase().trim() === file.type.toLowerCase())
            if (!acceptable) {
                this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'))
                return false
            }
            return true
        },

        /**
         * 上传文件
         */
         async uploadFile ({ file }) {
            // 定义文件内容
            let uploadedFile = {
                uploadProgressRate: 0, // 进度
                showProgress: true // 是否显示进度条
            }
            // 定义要更新的对象
            let needUpdateObject = this.currentLesson
            this.files.push(uploadedFile)
            // 视频暂不处理
            if (/video\/.+/i.test(file.type)) {
                return
            }
            fileUtils.uploadFile(file, {
                privateFile: false,
                processEventHandler: (progressEvent) => {
                    // 上传进度处理,图片显示出来才算彻底完成
                    let progressRate = (progressEvent.loaded / progressEvent.total).toFixed(2) * 100
                    if (progressRate === 100) {
                        progressRate -= 1
                    }
                    uploadedFile.uploadProgressRate = progressRate
                }
            }).then(res => {
                if (!res || !res.id) {
                    this.$message.error(this.$t('loc.lessons2UploadFailed'))
                    return
                }
                // 上传成功
                this.$set(uploadedFile, 'id', res.id) // 文件 ID
                this.$set(uploadedFile, 'url', res.public_url) // 文件 URL
                this.$set(uploadedFile, 'size', file.size) // 文件大小
                this.$set(uploadedFile, 'name', file.name) // 文件名称
                // 触发上传文件事件
                // 由于父组件可能使用的是 v-if 会导致父组件无法通过 $emit 接收到数据
                // 所以这里使用 $store 存储 this.$bus.$emit('mediaUploaderUploadSuccessFile', { file: uploadedFile, currentLesson: needUpdateObject })
                this.$store.dispatch('unit/setMediaUploaderUploadSuccessFile', { file: uploadedFile, currentLesson: needUpdateObject })
            }).catch(() => {
                this.$message.error(this.$t('loc.lessons2UploadFailed'))
                // 上传失败之后将上传文件重新赋值为空数组
                this.files = []
            }).finally(() => {
                // 关闭进度条
                this.showProgress = false
            })
            return false
        },

        /**
         * 删除文件
         */
        deleteFile (file) {
            this.files.splice(this.files.indexOf(file), 1)
        },

        /**
         * 更新文件列表
         */
        updateFiles (files) {
            this.files = files
        }
    },
}
</script>

<style lang="less" scoped>
/deep/ .medias-uploader {
    height: 100%;
    .el-upload {
        width: 100%;
        height: 100%;
        .el-upload-dragger {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
    }
}

/deep/ .medias-uploader.theme-1 {
    border-radius: 8px;
    .el-upload-dragger {
        border: 0.2em dashed var(--10-b-3-b-7, #10B3B7);
        background: rgba(221, 242, 243, 0.40);
    }
    .el-upload-dragger.is-dragover {
        border: 0.25em dashed var(--10-b-3-b-7, #10B3B7);
        background: rgba(221, 242, 243, 1.50);
    }
}

/deep/ .medias-uploader.theme-2 {
    border-radius: 8px;
    .el-upload-dragger {
        border: none;
        background: #F2F2F2;
    }
    .el-upload-dragger.is-dragover {
        border: 0.2em dashed var(--10-b-3-b-7, #10B3B7);
    }
}

// 16:9 的元素
.aspect-ratio-box-16-9 {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
}

// 3:1 的元素
.aspect-ratio-box-3-1 {
    position: relative;
    width: 100%;
    padding-top: 33.33%;
}

// 2:1 的元素
.aspect-ratio-box-2-1 {
    position: relative;
    width: 100%;
    padding-top: 50%;
}

// 1:1 的元素
.aspect-ratio-box-1-1 {
    position: relative;
    width: 100%;
    padding-top: 100%;
}

.aspect-ratio-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
// 删除图表
.el-icon-close {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #676879;
    border-radius: 50%;
    color: #fff;
    width: 14px;
    height: 14px;
    font-size: 10px;
    display: inline-block;
    cursor: pointer;
    padding: 2px;
    z-index: 1;
}
.el-icon-upload {
    margin: 20px 0 16px;
}
.upload-placeholder {
    background-color: #FAFAFA;
}
.media-viewer {
    position: relative;
}
.media-viewer:hover .media-viewer__source {
  display: block;
}

.media-viewer__source {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #3A3F51B2;
  color: #fff;
  padding: 8px 10px;
  line-height: 16px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>