<template>
    <el-card class="box-card" :class="[theme, isCollapse ? 'collapsed-card' : '']" :shadow="shadow">
        <div slot="header" class="display-flex w-full justify-content-between align-items"
             :class="{ 'lg-pointer' : allHeaderPointer }" @click="toggleCollapse">
            <!-- 标题 -->
            <div class="flex-1">
                <slot name="header"></slot>
            </div>
            <!-- 折叠图标 -->
            <div class="">
                <i class="el-icon-arrow-right collapse-icon" v-show="isCollapse && collapsible"></i>
                <i class="el-icon-arrow-down collapse-icon" v-show="!isCollapse && collapsible"></i>
            </div>
        </div>
        <!-- 卡片内容 -->
        <transition name="collapse">
            <div v-show="!isCollapse">
                <slot></slot>
            </div>
        </transition>
    </el-card>
</template>

<script>
export default {
    props: {
        // 是否启用折叠
        collapsible: {
            type: Boolean,
            default: true,
        },
        // 是否折叠
        collapse: {
            type: Boolean,
            default: false,
        },
        // 样式主题
        theme: {
            type: String,
            default: 'default',
        },
        // 阴影
        shadow: {
            type: String,
            default: 'always',
        },
    },

    computed: {
        // 是否整个 Header 都显示 pointer
        allHeaderPointer () {
            return (this.isCollapse && this.collapsible) || (!this.isCollapse && this.collapsible)
        }
    },
    components: {
    },

    data() {
        return {
            isCollapse: this.collapse, // 是否折叠
        }
    },

    methods: {
        // 切换折叠状态
        toggleCollapse() {
            if (!this.collapsible) {
                return
            }
            this.isCollapse = !this.isCollapse;
        },
    },

    watch: {
        collapse(value) {
            this.isCollapse = value;
        },
    },
}
</script>

<style lang="less" scoped>
.collapse-icon {
    padding: 20px 20px;
    margin: -20px -20px;
}
// 折叠时取消内边距
.el-card.collapsed-card {
    ::v-deep {
        .el-card__body {
            padding: 0 !important;
        }
    }
}
// 无头部，隐藏下边框
.el-card.no-header {
    ::v-deep {
        > .el-card__header {
            border-bottom: none !important;
        }
        > .el-card__body {
            padding-top: 0 !important;
        }
    }
}
// 文本编辑卡片
.el-card.textarea-card {
    ::v-deep {
        .el-card__header {
            padding: 0 10px !important;
            line-height: 40px;
            background-color: #F1F6FC;
        }
        .el-card__body {
            padding: 0 !important;
        }
    }
}

</style>