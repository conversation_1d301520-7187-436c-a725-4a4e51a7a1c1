<template>
    <!--两个按钮的显示-->
    <div class="m-t-xs" :style="feedbackFormStyle" :class="styleClass" v-show="feedbackResult.feedbackId">
        <div v-show="showFeedback && !feedbackLoading" class="feedback-group display-flex justify-content-between">
            <!-- UP -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.positiveFeedback')" placement="top"
                        :open-delay="500">
                <div class="feedback-up"
                     :class="{'lg-pointer': !feedbackResult.feedbackResult, 'cursor-disabled': feedbackResult.feedbackResult}"
                     v-show="!feedbackResult.feedbackResult || feedbackResult.feedbackResult === 'UP'"
                     @click="selectFeedback('UP')"
                     @mouseenter="feedbackUpHover = true" @mouseleave="feedbackUpHover = false">
                    <i class="fa feedback-up-icon lg-icon" aria-hidden="true"
                       :class="{'fa-thumbs-up text-success': needFeedbackUpHoverStyle && (feedbackUpHover || feedbackResult.feedbackResult === 'UP'),
                        'lg-icon-positive-feedback': !feedbackUpHover}"></i>
                </div>
            </el-tooltip>
            <!-- 分割线 -->
            <div class="feedback-middle" v-show="!feedbackResult.feedbackResult"></div>
            <!-- DOWN -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.negativeFeedback')" placement="top"
                        :open-delay="500">
                <div class="feedback-down"
                     :class="{'lg-pointer': !feedbackResult.feedbackResult, 'cursor-disabled': feedbackResult.feedbackResult}"
                     v-show="!feedbackResult.feedbackResult || feedbackResult.feedbackResult === 'DOWN'"
                     @click="selectFeedback('DOWN')"
                     @mouseenter="feedbackDownHover = true" @mouseleave="feedbackDownHover = false">
                    <i class="fa feedback-down-icon lg-icon" aria-hidden="true"
                       :class="{'fa-thumbs-down text-danger': needFeedbackUpHoverStyle && (feedbackDownHover || feedbackResult.feedbackResult === 'DOWN'),
                        'lg-icon-negative-feedback': !feedbackDownHover}"></i>
                </div>
            </el-tooltip>
        </div>

        <!-- 反馈弹窗 -->
        <el-dialog append-to-body
                   :show-close=false
                   width="636px"
                   custom-class="feedback-dialog"
                   :close-on-click-modal="false"
                   :visible.sync="feedbackDialogShow">
            <!--弹窗的头部信息-->
            <div slot="title" class="dialog-title">
                <div class="feedback-icon">
                    <div class="icon-up" v-show="showIcon && feedbackResult.feedbackResult === 'UP'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path
                                d="M14.7814 9.42864V5.57153C14.7814 4.16306 14.0571 3.40544 12.7746 3.12652C12.0486 2.96862 11.4388 3.60551 11.4448 4.34849V4.34849C11.4645 6.80747 10.6625 9.20261 9.16582 11.1538L9 11.37V20.9999H18.047C18.9125 21.01 19.653 20.3573 19.7827 19.4699L20.9803 11.37C21.0568 10.8478 20.9082 10.3172 20.5741 9.91871C20.2399 9.52022 19.8365 9.42264 19.3273 9.42864H14.7814Z"
                                stroke="#67C23A" stroke-width="2" stroke-linejoin="round"/>
                            <path
                                d="M6 12L4.28512 12.0002C3.57535 12.0002 3 12.5756 3 13.2854L3 19.7147C3 20.4245 3.57549 20.9999 4.28535 20.9999L6 20.9997V12Z"
                                stroke="#67C23A" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="icon-down" v-show="showIcon && feedbackResult.feedbackResult === 'DOWN'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path
                                d="M14.7814 14.5714V18.4285C14.7814 19.8369 14.0571 20.5946 12.7746 20.8735C12.0486 21.0314 11.4388 20.3945 11.4448 19.6515V19.6515C11.4645 17.1925 10.6625 14.7974 9.16582 12.8462L9 12.63V3.00012H18.047C18.9125 2.98997 19.653 3.64266 19.7827 4.53011L20.9803 12.63C21.0568 13.1522 20.9082 13.6828 20.5741 14.0813C20.2399 14.4798 19.8365 14.5774 19.3273 14.5714H14.7814Z"
                                stroke="#F56C6C" stroke-width="2" stroke-linejoin="round"/>
                            <path
                                d="M6 12L4.28512 11.9998C3.57535 11.9998 3 11.4244 3 10.7146L3 4.28535C3 3.57549 3.57549 3.00005 4.28535 3.00011L6 3.00027V12Z"
                                stroke="#F56C6C" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <span>
              {{ feedbackTitle }}
          </span>
                </div>
                <span v-show="showClose" class="dialog-close" @click="closeDialog">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M6.99951 7L16.9995 17" stroke="#111C1C" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path d="M6.99951 17L16.9995 7" stroke="#111C1C" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round"/>
            </svg>
          </span>
            </div>
            <!--弹窗的内容区域-->
            <div v-loading="feedbackLoading" class="dialog-content">
                <!--提醒用户的文字解释的区域-->
                <div class="feedback-content" v-show="feedbackContent !== ''">{{ feedbackContent }}</div>
                <!--打分的区域-->
                <div class="feedback-level" v-if="needFeedbackLevel">
                    <!--有多少打分的项-->
                    <div class="display-flex justify-content-between w-full"
                         v-for="(levelContext, lebelIndex) in feedbackLevelLabels" :key="lebelIndex">
                        <div style="width: 65%" class="feedback-content" v-html="levelContext"></div>
                        <!--星星的展示区域-->
                        <div class="display-flex justify-content-between" style="gap: 8px;">
              <span class="select-start"
                    @click="selectStart(lebelIndex, index)"
                    v-for="index in (feedbackResult.feedbackData.feedbackLevel[lebelIndex])">
                <img class="block" src="@/assets/img/curriculumGenie/start-select.png"/>
              </span>
                            <span class="select-start"
                                  @click="selectStart(lebelIndex, feedbackResult.feedbackData.feedbackLevel[lebelIndex] + index)"
                                  v-for="index in (feedbackLevelNum - feedbackResult.feedbackData.feedbackLevel[lebelIndex])">
                <img class="block" src="@/assets/img/curriculumGenie/start-noselect.png"/>
              </span>
                        </div>
                    </div>
                </div>
                <!--用户反馈输入的地方-->
                <el-form class="feedback-input" :model="feedbackForm" ref="feedbackForm">
                    <el-input type="textarea"
                              maxlength="1000"
                              show-word-limit
                              :autosize="{ minRows: 6, maxRows: 10}"
                              :placeholder="feedbackInputPlaceholder"
                              v-show="showFeedbackInput"
                              v-model="feedbackForm.feedback">
                    </el-input>
                </el-form>
            </div>
            <!--底部区域-->
            <div slot="footer" class="dialog-footer">
                <el-checkbox v-show="showReminders" label="No more reminders" name="type">
                </el-checkbox>
                <div class="save-cancel">
                    <el-button v-show="showCancelFeedback" @click="cancelFeedback" :loading="feedbackSubmitLoading">
                        {{
                            $t('loc.cancel')
                        }}
                    </el-button>
                    <el-button type="primary" @click="submitFeedback(true)" :loading="feedbackSubmitLoading">{{
                            feedbackSubmit
                        }}
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'FeedbackForm',
    props: {
        // 要反馈的内容的 Prompt Usage Record Id
        promptUsageRecordIds: {
            type: Array,
            default: () => [],
            required: true
        },
        // 由调用方来控制合适的时机来显示反馈
        showFeedback: {
            type: Boolean,
            default: false
        },
        // 反馈的标题
        feedbackTitle: {
            type: String,
            default: 'Generate content feedback'
        },
        // 反馈的内容提醒区域
        feedbackContent: {
            type: String,
            default: ''
        },
        // 反馈的内容评级区域的 label
        feedbackLevelLabel: {
            type: Array,
            default: () => ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions']
        },
        // 反馈的内容评级区域的 等级最高为多少
        feedbackLevelNum: {
            type: Number,
            default: 5
        },
        // 是否展示反馈的输入框
        showFeedbackInput: {
            type: Boolean,
            default: true
        },
        // 反馈的输入框的 placeholder
        feedbackInputPlaceholder: {
            type: String,
            default: ''
        },
        // 如果用户之前从来没有反馈过当前的内容，展示默认的反馈内容
        defaultFeedbackResult: {
            type: Object,
            default: () => {
            }
        },
        // 外界可以自定义反馈的样式
        feedbackStyle: {
            type: Function,
            default: () => {
            },
            required: false
        },
        // 是否显示反馈的 icon
        showIcon: {
            type: Boolean,
            default: false
        },
        // 是否显示关闭按钮
        showClose: {
            type: Boolean,
            default: true
        },
        // 是否需要反馈等级
        needFeedbackLevel: {
            type: Boolean
        },
        // 是否显示不再提醒
        showReminders: {
            type: Boolean,
            default: true
        },
        // 是否展示取消反馈按钮
        showCancelFeedback: {
            type: Boolean,
            default: true
        },
        // 是否需要 hover 的样式
        needFeedbackUpHoverStyle: {
            type: Boolean,
            default: false
        },
        feedbackSubmit: {
            type: String,
            default: 'Submit'
        },
        styleClass: {
            type: String,
            default: 'right'
        }
    },
    computed: {
        feedbackFormStyle () {
            return this.feedbackStyle()
        },
        feedbackLevelLabels () {
            // 获取后台请求得到的 feedbackLevelLabel
            const feedbackLevelLabel = this.feedbackResult.feedbackData.feedbackLevelLabel
            //  如果 feedbackLevelLabel 存在并且长度大于 0，就返回 feedbackLevelLabel
            if (feedbackLevelLabel && feedbackLevelLabel.length > 0) {
                return feedbackLevelLabel
            } else {
                // 返回默认的 feedbackLevelLabel
                return this.feedbackLevelLabel
            }
        },
        // 反馈的 hover 状态
        feedbackUpHover: {
            get () {
                return this.needFeedbackUpHoverStyle ? this.feedbackUpHover : false
            },
            set (val) {
                if (this.needFeedbackUpHoverStyle) {
                    this.feedbackUpHover = val
                }
            }
        },
        feedbackDownHover: {
            get () {
                return this.needFeedbackUpHoverStyle ? this.feedbackDownHover : false
            },
            set (val) {
                if (this.needFeedbackUpHoverStyle) {
                    this.feedbackDownHover = val
                }
            }
        }
    },
    data () {
        return {
            // 反馈的表单
            feedbackForm: {
                feedback: ''
            },
            // 反馈的类型
            feedBackType: {
                UP: 'UP',
                DOWN: 'DOWN'
            },
            feedbackResult: {
                //  反馈的 id
                feedbackId: '',
                //  反馈的类型
                feedbackResult: '',
                //  反馈的数据内容
                feedbackData: {},
            },
            feedbackDialogShow: false, // 反馈弹窗是否显示
            feedbackSubmitLoading: false, // 反馈提交 loading
            feedbackLoading: false // 获取反馈 loading
        }
    },
    watch: {
        showFeedback: {
            handler (val) {
                // 如果 showFeedback 为 true 的时候，就获取反馈的结果
                if (val) {
                    this.getFeedBackResult()
                }
            },
            immediate: true,
            deep: true
        },
        promptUsageRecordIds: {
            handler (val) {
                // 如果 showFeedback 为 true 的时候，就获取反馈的结果
                if (val) {
                    // 每次 promptUsageRecordIds 发生变化的时候，都说明需要重新获取反馈的结果，那么此时旧的 feedbackResult 就没有用了，所以需要将其置空
                    this.$set(this.feedbackResult, 'feedbackResult', '')
                    this.getFeedBackResult()
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        getFeedBackResult () {
            // 如果没有展示的时候，不获取
            if (!this.showFeedback) {
                return
            }
            // 设置 feedbackDataLoading 为 true
            this.feedbackLoading = true
            // 如果 this.promptUsageRecordIds 为空就不用获取了
            if (this.promptUsageRecordIds && this.promptUsageRecordIds.length === 0) {
                this.feedbackLoading = false
                return
            }
            // 定义参数
            let param = {
                promptUsageRecordIds: this.promptUsageRecordIds
            }
            // 请求接口获取对应的 feedback
            this.$axios.post($api.urls().getFeedback, param)
                .then((res) => {
                    if (res) {
                        this.$nextTick(() => {
                            // 由于 res.feedbackData 是一个 JSON 字符串，所以需要将其转换为 JSON 对象
                            res.feedbackData = JSON.parse(res.feedbackData)
                            console.log(res.feedbackData)
                            // 设置 feedbackResult 的值
                            this.$set(this.feedbackResult, 'feedbackId', res.feedbackId)
                            this.$set(this.feedbackResult, 'feedbackResult', res.feedbackResult)
                            this.$set(this.feedbackResult, 'feedbackData', res.feedbackData)
                            // 通知父组件修改 feedbackResult 的值
                            // 仅当 feedbackResult 的值不为空的时候才会通知父组件
                            if (res.feedbackResult) {
                                this.$emit('submitFeedback', this.feedbackResult)
                            }
                            // 设置加载完毕
                            this.feedbackLoading = false
                        })
                    } else {
                        this.$nextTick(() => {
                            if (this.defaultFeedbackResult) {
                                // 设置 feedbackResult 的值
                                this.$set(this.feedbackResult, 'feedbackId', this.defaultFeedbackResult.feedbackId)
                                // 如果 feedbackResult 为空，就将 defaultFeedbackResult 的值赋值给 feedbackResult
                                // 如果都是空的，那么就设置 feedbackResult 为空
                                // defaultFeedbackResult 默认是空的
                                if (this.feedbackResult.feedbackResult === '' && this.defaultFeedbackResult.feedbackResult === '') {
                                    this.$set(this.feedbackResult, 'feedbackResult', '')
                                } else {
                                    // 获取当前的 feedbackResult 的值
                                    let feedbackResult = this.feedbackResult.feedbackResult
                                    // 如果 feedbackResult 为空，就将 空 的值赋值给 feedbackResult
                                    if (feedbackResult) {
                                        this.$set(this.feedbackResult, 'feedbackResult', feedbackResult)
                                    } else {
                                        this.$set(this.feedbackResult, 'feedbackResult', '')
                                    }
                                }
                                // 将默认的 feedbackData 赋值给 feedbackResult
                                this.$set(this.feedbackResult, 'feedbackData', this.defaultFeedbackResult.feedbackData)
                                // 设置加载完毕
                                this.feedbackLoading = false
                            }
                        })
                    }
                    this.$nextTick(() => {
                        // 将数据库中的反馈数据赋值给表单
                        this.$set(this.feedbackForm, 'feedback', this.feedbackResult.feedbackData.feedback)
                    })
                })
                .catch((error) => {
                    if (this.defaultFeedbackResult) {
                        this.feedbackResult = this.defaultFeedbackResult
                        // 设置加载完毕
                        this.feedbackLoading = false
                    }
                })
        },
        closeDialog () {
            this.feedbackDialogShow = false
        },
        selectStart (startLevel, selectLevel) {
            console.log('startLevel', startLevel, 'selectLevel', selectLevel)
            // 选择的等级
            this.$nextTick(() => {
                // 在selectStart方法中更新feedbackLevel的值
                this.$set(this.feedbackResult.feedbackData.feedbackLevel, startLevel, selectLevel)
                // 设置 feedbackForm 下面的 feedbackLevel 的值
                this.feedbackForm.feedbackLevel = this.feedbackResult.feedbackData.feedbackLevel
                // 输出feedbackLevel的值
                console.log(this.feedbackResult.feedbackData.feedbackLevel)
            })
        },
        //   选择正反馈或者负反馈的时候，调用的方法
        selectFeedback (result) {
            // 如果已经有反馈结果了就不允许再点击了
            if (this.feedbackResult.feedbackResult) {
                return
            }
            if (this.$route.name === 'AddLesson' && result === 'UP') {
                // 创建课程时点击正反馈埋点
                this.$analytics.sendEvent('cg_lesson_plan_cre_click_feedback_p')
            } else if (this.$route.name === 'AddLesson' && result === 'DOWN') {
                // 创建课程时点击负反馈埋点
                this.$analytics.sendEvent('cg_lesson_plan_cre_click_feedback_n')
            }
            // 反馈结果
            this.$set(this.feedbackResult, 'feedbackResult', result)
            // 保存 feedback 的内容
            this.$set(this.feedbackResult.feedbackData, 'feedback', this.feedbackForm.feedback)
            // 反馈内容置空
            this.$set(this.feedbackForm, 'feedback', '')
            // 添加 loading 为 false
            this.$set(this, 'feedbackSubmitLoading', false)
            // 显示反馈弹窗
            this.$set(this, 'feedbackDialogShow', true)
            // 点击按钮之后，调用获取反馈的方法
            this.getFeedBackResult()
            // 发送反馈的结果，true 为 正反馈，false 为 负反馈
            this.$emit('clickFeedback', result === 'UP')
        },
        // 取消反馈
        cancelFeedback () {
            // 如果点击的 cancel，那么移除掉 feedbackResult 的值
            this.feedbackResult.feedbackResult = ''
            // 关闭弹窗
            this.feedbackDialogShow = false
        },
        //   提交反馈
        submitFeedback (haveFeedback) {
            console.log(this.feedbackForm)
            // 反馈内容去除前后空格
            if (this.feedbackForm.feedback) {
                this.feedbackForm.feedback = this.feedbackForm.feedback.trim()
            }
            // 将星等级设置给 feedbackForm
            this.feedbackForm.feedbackLevel = this.feedbackResult.feedbackData.feedbackLevel
            // 将星等级说明设置给 feedbackForm
            this.feedbackForm.feedbackLevelLabel = this.feedbackResult.feedbackData.feedbackLevelLabel
            // 提交反馈
            this.submitFeedbackData()
        },
        submitFeedbackData () {
            this.feedbackSubmitLoading = true
            let param = {
                promptUsageRecordIds: this.promptUsageRecordIds,
                feedbackResult: this.feedbackResult.feedbackResult,
                feedbackData: this.feedbackForm
            }
            this.$axios.post($api.urls().createFeedBack, param)
                .then((res) => {
                    console.log(res)
                    // 定义要保存的 feedback 信息
                    const feedbackResult = {
                        ...param,
                        feedbackId: res ? res.id : ''
                    }
                    this.$emit('submitFeedback', feedbackResult)
                    if (res) {
                        this.feedbackId = res.id
                        this.feedbackDialogShow = false
                        this.feedbackSubmitLoading = false
                        this.$message.success(this.$t('loc.feedbackSuccess'))
                    } else {
                        this.feedbackSubmitLoading = false
                        this.$message.error('Please confirm if you have passed the promptUsageRecordIds.')
                    }
                })
                .catch((error) => {
                    this.feedbackSubmitLoading = false
                    this.$message.error('Please confirm if you have passed the promptUsageRecordIds.')
                })
        },
    }
}
</script>

<style scoped lang="less">
.feedback-group {
    border-radius: 16px;
    background: var(--ffffff, #FFF);
    border: 1px solid #dcdfe6;
    align-items: center;
    height: 28px;

    .feedback-up-icon, .feedback-down-icon {
        padding: 8px 16px;
        font-size: 16px;
        display: flex;
        color: var(--color-text-primary);
    }

    .feedback-middle {
        width: 1px;
        height: 100%;
        background-color: #dcdfe6;
    }

    .feedback-up, .feedback-down {
        height: 28px;
        width: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.feedback-dialog {
    display: flex;
    width: 636px;
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    border-radius: 8px;
    background: var(--ffffff, #FFF);
    /* 卡片投影 */
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);

    .dialog-title {
        display: flex;
        justify-content: space-between;
        color: var(--111-c-1-c, #111C1C);
        font-feature-settings: 'clig' off, 'liga' off;

        /* Semi Bold/20px */
        font-family: Inter;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 130% */

        .dialog-close {
            cursor: pointer;
            width: 24px;
            height: 24px;
        }

        .feedback-icon {
            display: flex;
            align-items: center;
            gap: 8px;
            align-self: stretch;

            .icon-up {
                display: flex;
                width: 36px;
                height: 36px;
                justify-content: center;
                align-items: center;
                gap: 8px;
                border-radius: 25px;
                background: rgba(103, 194, 58, 0.20);
            }

            .icon-down {
                display: flex;
                width: 36px;
                height: 36px;
                justify-content: center;
                align-items: center;
                gap: 8px;
                border-radius: 25px;
                background: rgba(245, 108, 108, 0.20);
            }
        }
    }

    .dialog-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
        align-self: stretch;

        .feedback-content {
            color: var(--111-c-1-c, #111C1C);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Regular/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }

        .feedback-level {
            display: flex;
            padding: 20px;
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
            align-self: stretch;
            border-radius: 8px;
            background: var(--fafafa, #FAFAFA);

            .select-start {
                cursor: pointer;
            }
        }

        .feedback-input {
            width: 100%;
            display: flex;
            align-items: flex-start;
            gap: 10px;
            align-self: stretch;
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .save-cancel {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
        }
    }
}

/deep/ .el-dialog__body {
    // 重置弹窗的 body 样式，设计上写的是 20，但是 title 与 body 存在 10，所以这里设置 10
    padding-top: 10px;
    padding-bottom: 10px;
}
</style>