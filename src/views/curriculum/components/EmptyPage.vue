<template>
    <div class="w-full h-full display-flex justify-content align-items flex-direction-col">
        <div class="text-center m-b-md m-t-md">
            <img src="@/assets/img/curriculumGenie/unit_empty.jpg" alt="">
            <div class="mt-2 font-size-18" v-if="tip">{{ tip }}</div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        tip: {
            type: String,
            default: () => 'No records.'
        }
    },
}
</script>

<style lang="less" scoped>
ul, li {
    list-style: disc;
}
</style>