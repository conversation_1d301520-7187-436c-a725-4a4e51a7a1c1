<template>
  <div class="designer-measure padding-10 position-relative"
       :class="{ 'auto-active-border': domainCheckedDropdownVisible}">
    <el-popover
      placement="right-start"
      ref="isClick"
      trigger="click"
      :append-to-body="false"
      :visible-arrow="false"
      popper-class="domains-checked-popover"
      v-model="domainCheckedDropdownVisible">
      <!-- 按领域自动分配 -->
      <el-checkbox :indeterminate="frameworkIndeterminate" v-model="allDomainChecked" @change="handleCheckAllDomain">
        <span style="font-weight: bold;">{{frameworkName}}</span>
      </el-checkbox>
      <el-checkbox-group v-model="checkedDomains" @change="handleCheckedDomainChange">
        <el-checkbox v-for="domain in domains" :label="domain.name" :key="domain.id"></el-checkbox>
      </el-checkbox-group>
      <div slot="reference" class="lg-color-text-placeholder">
        <!-- 触发弹出框的元素 -->
        <!-- 测评点内容 -->
        <div>
          <div v-show="(!this.checkedDomains || this.checkedDomains.length === 0)"
                class="lg-color-text-placeholder focus-color"
                :class="{'measures-font': !domainCheckedDropdownVisible, 'measures-font-focus': domainCheckedDropdownVisible}">
            {{ $t('loc.curriculumUnitSelectSubject') }}
          </div>
          <span v-for="(domain, index) in (unitInfo ? unitInfo.allDomains : [])" :key="index">
            <el-tag  type="info" size="small" class="m-r-xs pos-rlt m-b-xs" disable-transitions>
              <span style="color: #676879">
                {{ domain.name }}
              </span>
              <i @click.stop="removeSelectedItem(domain)" class="flag el-icon-close red lg-pointer"></i>
            </el-tag>
          </span>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script>
export default {
  props: {
    frameworkName: {
      type: String,
      default: ''
    },
    unitIndex: {
      type: Number,
      default: 0
    },
    domains: {
      type: Array,
      default: () => []
    },
    // 是否需要过滤领域
    needFilterDomain: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 单元基本信息
    unitInfo: {
      get () {
        const units = this.$store.state.designer.unitsFromDesigner
        if (units && units.length > this.unitIndex) {
          return this.$store.state.designer.unitsFromDesigner[this.unitIndex].baseInfo
        }
        return {}
      },
      set (value) {
        this.$store.commit('SET_UNIT_BASE_INFO', { unitIndex: this.unitIndex, baseInfo: value })
      }
    }
  },
  data () {
    return {
      checkedDomains: [], // 选择的领域
      allDomainChecked: false, // 框架下的所有领域都被选中
      frameworkIndeterminate: false, // 框架是否处于半选状态
      domainCheckedDropdownVisible: false // 领域下拉框是否显示
    }
  },
  watch: {
    'unitInfo.allDomains': {
      deep: true,
      handler (val) {
        if (val && val.length > 0) {
          this.initCheckedDomains()
        }
      }
    }
  },
  mounted () {
    // Foundation 页面初始化已选择测评点
    this.$nextTick(() => {
      this.initCheckedDomains()
    })
  },
  methods: {
    // 初始化选择的领域
    initCheckedDomains () {
      this.checkedDomains = (this.unitInfo && this.unitInfo.allDomains) ? this.unitInfo.allDomains.map(domain => {
        if (domain.name && domain.name !== '') {
          return domain.name
        }
      }) : []
      const checkedCount = this.checkedDomains.length
      // 设置是否全选
      this.allDomainChecked = checkedCount === this.domains.length
      // 设置框架是否半选状态
      this.frameworkIndeterminate = checkedCount > 0 && checkedCount < this.domains.length
    },
    // 如果全选框架下的所有领域
    handleCheckAllDomain (val) {
      this.checkedDomains = val ? this.domains && this.domains.map(domain => {
        if (domain.name && domain.name !== '') {
          return domain.name
        }
      }) : []
      this.frameworkIndeterminate = false
      // 更新单元选择的测评点信息
      const allDomains = this.domains.filter(domain => this.checkedDomains.includes(domain.name))
      this.$emit('updateBaseInfo', allDomains)
    },
    // 选择框架下的某个领域
    handleCheckedDomainChange (val) {
      const checkedCount = val.length
      // 设置是否全选
      this.allDomainChecked = checkedCount === this.domains.length
      // 设置框架是否半选状态
      this.frameworkIndeterminate = checkedCount > 0 && checkedCount < this.domains.length
      // 更新单元选择的测评点信息
      const allDomains = this.domains.filter(domain => val.includes(domain.name))
      this.$emit('updateBaseInfo', allDomains)
    },
    // 移除领域
    removeSelectedItem (domain) {
      this.checkedDomains = this.checkedDomains.filter(name => name !== domain.name)
      const allDomains = this.unitInfo.allDomains.filter(item => item.name !== domain.name)
      // 设置是否全选状态和半选状态
      if (!allDomains || allDomains.length === 0) {
        this.allDomainChecked = false
        this.frameworkIndeterminate = false
      }
      this.$emit('updateBaseInfo', allDomains)
    },
    // 显示下拉框,给外部调用
    showDropdown () {
      this.domainCheckedDropdownVisible = true
    }
  }
}
</script>
<style scope lang="less">

.after .designer-measure {
  display: flex;
  width: 100%;
  height: 100%;
  border-bottom: 1px solid #ccc;
}

.after .designer-measure:last-child {
  border-bottom: 0;
}

.auto-active-border {
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid!important;
  border-left-style: solid;
  border-width: 1px!important;
  border-color: #10b3b7!important;
  font-size: 15px;
}

.measures-font-focus {
  margin-left: 20px;
  color: #10b3b7;
}

.spanTruncate {
  margin-left: 5px;
}

.measures-font {
  margin-left: 20px;
  color: var(--color-text-placeholder);
}

.vertical-checkbox-group .el-checkbox {
  display: block;
  margin-left: 8px;
  margin-bottom: 10px;
}
.padding-10 {
  padding: 10px !important;
}
</style>
<style lang="less">
.domains-checked-popover > .el-checkbox-group {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
}
</style>
