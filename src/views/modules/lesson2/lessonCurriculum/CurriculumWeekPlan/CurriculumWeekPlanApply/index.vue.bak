<template>
  <div>
    <el-button @click="openDialog">应用到班级</el-button>
    <el-dialog :visible="dialogVisable" :before-close="closeDialog">
      <div>
        <el-row :gutter="10">
          <el-col :span="9">
            <div style="margin-top: -30px;" class="font-color-black-bold font-size-24 add-margin-b-12">Apply Curriculum to Classroom</div>
            <el-row>
              <el-row>
                <div class="add-margin-b40">
                  <div class="font-bold font-size-16 font-color-black">Select Units and Weeks</div>
                  <el-row :gutter="6" class="display-flex">
                    <el-col :span="12">
                      <el-select v-model="value1" multiple placeholder="请选择">
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="value" placeholder="请选择">
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10" class="add-margin-tb-6">
                    <el-col class="add-padding-tb-20" :span="24/3" v-for="o in 3">
                      <el-card :body-style="{ padding: '0px' }">
                        <div class="plan-card">
                          <div class="plan-action">
                            <div class="text-center" style="margin-top:30%">
                              <el-button type="">Preview</el-button>
                              <el-button type="primary">Select</el-button>
                            </div>
                          </div>
                          <!--                    <div style="height: 200px; background: #145280;" > </div>-->
                          <div style="background-color: #BFE8FF;padding: 10px;" class="border-radius-6">
                            <div style="margin-top: -10px;" class="clearfix">
                              <div class="font-color-black-bold font-size-16"><span>Out Tree Named Stvev</span></div>
                              <div class="display-flex justify-content-between">
                                <div>10/31-11/04</div>
                                <div>Week2</div>
                              </div>
                            </div>
                            <div>
                              <img style="width:100%;height:100%" src="@/assets/img/lesson2/plan/week_plan_preview.png">
                            </div>
                          </div>
                        </div>
                      </el-card>
                    </el-col>
                  </el-row>
                </div>
                <div class="add-margin-tb40">
                  <div class="font-bold font-size-16 font-color-black">Select Classroom</div>
                  <el-row :gutter="6" class="display-flex">
                    <el-col :span="12">
                      <el-select v-model="value" placeholder="请选择">
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="value" placeholder="请选择">
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="add-margin-tb40">
                  <div class="font-bold font-size-16 font-color-black">Setup Start Date</div>
                  <el-row :gutter="6" class="display-flex">
                    <el-col :span="12" class="display-flex">
                      <el-date-picker
                        popper-class="date-popper"
                        v-model="value2"
                        type="daterange"
                        align="right"
                        unlink-panels
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :picker-options="pickerOptions">
                      </el-date-picker>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="value" placeholder="请选择">
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
              </el-row>
            </el-row>
          </el-col>
          <el-col :span="15">
            <div style="background-color: #f6f6f6;border-radius: 8px 8px 8px 8px;">
              <div class="display-flex justify-content-between">
                <div class="font-color-black-bold font-size-22 add-padding-l-20 add-padding-t-20">Calendar</div>
                <div class="display-flex add-padding-tb-6">
                  <div class="display-flex add-padding-r-20 add-padding-t-20"><i style="display: inline-block;height: 15px;width: 15px;background-color: #E3E3E3;margin-top: 2px;"></i><div class="add-padding-l-6">Holidays</div></div>
                  <div class="display-flex add-padding-r-20 add-padding-t-20"><i style="display: inline-block;height: 15px;width: 15px;background-color: #FFE8B9;margin-top: 2px;"></i><div class="add-padding-l-6">Units</div></div>
                </div>
              </div>
              <el-row style="padding-bottom: 20px;" class="add-padding-lr-20" :gutter="40">
                <el-col :span="8">
                  <week-plan-calender :year="2022" :month="11" :activeDays="[]" :holidays="[]"></week-plan-calender>
                </el-col>
                <el-col :span="8">
                  <week-plan-calender :year="2022" :month="12" :activeDays="[]" :holidays="[]"></week-plan-calender>
                </el-col>
                <el-col :span="8">
                  <week-plan-calender :year="2023" :month="1" :activeDays="[]" :holidays="[]"></week-plan-calender>
                </el-col>
              </el-row>
              <el-row style="padding-bottom: 20px;" class="add-padding-lr-20" :gutter="40">
                <el-col :span="8">
                  <week-plan-calender :year="2022" :month="11" :activeDays="[]" :holidays="[]"></week-plan-calender>
                </el-col>
                <el-col :span="8">
                  <week-plan-calender :year="2022" :month="12" :activeDays="[]" :holidays="[]"></week-plan-calender>
                </el-col>
                <el-col :span="8">
                  <week-plan-calender :year="2023" :month="1" :activeDays="[]" :holidays="[]"></week-plan-calender>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="display-flex justify-content-end add-padding-t-20">
        <el-button @click="closeDialog">Cancel</el-button>
        <el-button style="background-color: #10B3B7;color: white">Apply</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import weekPlanCalender from './components/weekPlanCalender.vue'
export default {
  name: 'CurriculumWeekPlanApply',
  components: { weekPlanCalender },
  data () {
    return {
      dialogVisable: true,
      options: [{
        value: '选项1',
        label: '黄金糕'
      }, {
        value: '选项2',
        label: '双皮奶'
      }, {
        value: '选项3',
        label: '蚵仔煎'
      }, {
        value: '选项4',
        label: '龙须面'
      }, {
        value: '选项5',
        label: '北京烤鸭'
      }],
      value: '',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      value1: '',
      value2: ''
    }
  },
  methods: {
    openDialog () {
      this.dialogVisable = true
    },
    closeDialog () {
      this.dialogVisable = false
    }
  }
}
</script>

<style scoped>
.plan-action {
  position: absolute;
  visibility: hidden;
  height: 100%;
  width: 100%;
  background: rgba(196, 197, 203, 0.8);
}
.plan-card {
  position:relative
}

.plan-card:hover {
  cursor: pointer;
  .plan-action {
  visibility: visible ;
  }
}

/deep/ .el-dialog {
  height: 80vh;
  width: 190vh;
  overflow: auto;
  top: -30px;
}

/deep/ .el-range-editor{
  width: 100%;
}

/deep/ .el-select{
  width: 100%;
}

/deep/ date-popper{
  background-color: chocolate;
  top: 290px;
  left: 110px;
}

/deep/ .calendar-table{
  width: 100%;
  height: 250px;
}
</style>
