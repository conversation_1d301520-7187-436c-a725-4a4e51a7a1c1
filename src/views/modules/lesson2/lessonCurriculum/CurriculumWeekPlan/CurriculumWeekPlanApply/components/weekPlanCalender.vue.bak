<template>
  <div>
    <!-- <div id="calendar" v-html="calendarHtml"></div> -->
    <div>
      <table class="calendar-table">
        <tr class="calendar-header"><th class="text-center" colspan="7">{{ title }}</th></tr>
        <tr>
          <td class="calendar-header-day text-center day-cell" v-for="(i, index) in weekdays" :key="index">{{ i }}</td>
        </tr>
        <tr  v-for="(date,index) in rows" :key="index">
          <!-- <td class="selectDate"> -->
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 0 : index * 7 ].isActive, 'holiday' : showdays[index == 0 ? 0 : index * 7 ].isHoliday }">
            {{ showdays[index == 0 ? 0 : index * 7 ].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 1 : index * 7 + 1].isActive, 'holiday' : showdays[index == 0 ? 1 : index * 7 + 1].isHoliday }">
            {{ showdays[index == 0 ? 1 : index * 7 + 1].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 2 : index * 7 + 2].isActive, 'holiday' : showdays[index == 0 ? 2 : index * 7 + 2].isHoliday }">
            {{ showdays[index == 0 ? 2 : index * 7 + 2].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 3 : index * 7 + 3].isActive, 'holiday' : showdays[index == 0 ? 3 : index * 7 + 3].isHoliday }">
            {{ showdays[index == 0 ? 3 : index * 7 + 3].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 4 : index * 7 + 4].isActive, 'holiday' : showdays[index == 0 ? 4 : index * 7 + 4].isHoliday }">
            {{ showdays[index == 0 ? 4 : index * 7 + 4].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 5 : index * 7 + 5].isActive, 'holiday' : showdays[index == 0 ? 5 : index * 7 + 5].isHoliday }">
            {{ showdays[index == 0 ? 5 : index * 7 + 5].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 6 : index * 7 + 6].isActive, 'holiday' : showdays[index == 0 ? 6 : index * 7 + 6].isHoliday }">
            {{ showdays[index == 0 ? 6 : index * 7 + 6].dayOfMonth }}
          </td>
          <!-- <td class="text-center day-cell" >{{ showdays[index == 0 ? 0 : index * 7 + 0].dayOfMonth }}</td>
          <td class="text-center day-cell" >{{ showdays[index == 0 ? 1 : index * 7 + 1].dayOfMonth }}</td>
          <td class="text-center day-cell" >{{ showdays[index == 0 ? 2 : index * 7 + 2].dayOfMonth }}</td>
          <td class="text-center day-cell" >{{ showdays[index == 0 ? 3 : index * 7 + 3].dayOfMonth }}</td>
          <td class="text-center day-cell" >{{ showdays[index == 0 ? 4 : index * 7 + 4].dayOfMonth }}</td>
          <td class="text-center day-cell" >{{ showdays[index == 0 ? 5 : index * 7 + 5].dayOfMonth }}</td>
          <td class="text-center day-cell" >{{ showdays[index == 0 ? 6 : index * 7 + 6].dayOfMonth }}</td> -->
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    year: {
      type: Number,
      default: new Date().getFullYear()
    },
    month: {
      type: Number,
      default: new Date().getMonth()
    }
    // activeDays: {
    //   type: Array
    // },
    // holidays: {
    //   type: Array
    // }

  },
  created () {
    this.initCalendar(this.year, this.month)
  },
  data () {
    return {
      rows: 6,
      weekdays: ['S','M','T','W','T','F','S'],
      showdays: [],
      activeDays: [11, 12, 13, 14],
      holidays: [1, 3, 5, 7,15, 18]
    }
  },
  computed: {
    title () {
      let dateStr = this.year + '-' + this.month
      return this.$moment(dateStr).format('MMMM YYYY')
    }
  },
  methods: {
    initCalendar (year, month) {
      let firstdayOfWeek = new Date(year, month - 1).getDay()
      for (let i = 0; i < firstdayOfWeek; i++) {
        let day = {
          dayOfMonth: '',
          isActive: false,
          isHoliday: false
        }
        this.showdays.push(day)
      }
      var max = new Date(year, month, 0).getDate()
      for (let i = 1; i < max; i++) {
        let isActive = this.activeDays.indexOf(i) >= 0
        let isHoliday = this.holidays.indexOf(i) >= 0
        let day = {
          dayOfMonth: i,
          isActive: isActive,
          isHoliday: isHoliday
        }
        this.showdays.push(day)
      }
      this.rows = Math.ceil(this.showdays.length / 7)
      if (this.showdays.length % 7 > 0) {
        for (let i = 0; i < (this.showdays.length % 7); i++) {
          let day = {
            dayOfMonth: '',
            isActive: false,
            isHoliday: false
          }
          this.showdays.push(day)
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.calendar-table{
  border-collapse: collapse;
}
.calendar-header {
  background: #C7E9EB;
}
.calendar-header-day {
  background-color:#10B3B7;
  color: #FFFFFF;
}
.day-cell {
  border: 1px solid #ccc;
  padding: 5px;
}
.holiday {
  background: #E3E3E3 !important;
}
.active {
  background: #FFE8B9;
}
</style>
