<template>
  <div>
    <div
      style="
        text-align: center;
        border: 1px solid #dcdfe6;
        height: 40px;
        border-bottom: none;
        background-color: #f5f6f8;
        padding: 6px;
      "
      class="font-color-black-bold font-size-18"
    >
      <span>Centers</span>
    </div>
    <div class="display-flex">
      <el-table
        :show-header="false"
        border
        :data="tableData"
        style="width: 100%"
      >

        <el-table-column width="110">
          <template slot-scope="scope">
            <span  >{{ scope.row.cardName }}</span>
          </template>
        </el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <div @mouseenter="go" @mouseleave="leave">
              <div class="smallTop">
                <span> Paper Ladybug Craft</span>
                <i
                  v-show="isMaterialList"
                  @click="edit(scope.row)"
                  class="el-icon-edit"
                  style="margin-left: 5px"
                ></i>
              </div>
              <ul class="smallList">
                <li>scissor</li>
                <li>black/red construction paper</li>
                <li>black/red construction paper</li>
                <li>scissor</li>
              </ul>
            </div>
          </template>
        </el-table-column>
        <el-table-column  width="110">
          <template slot-scope="scope">
            <span @click="Edit">{{
              scope.row.cardName1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <div @mouseenter="go" @mouseleave="leave">
              <div class="smallTop">
                <span> Paper Ladybug Craft</span>
                <i
                  v-show="isMaterialList"
                  @click="edit(scope.row)"
                  class="el-icon-edit"
                  style="margin-left: 5px"
                ></i>
              </div>
              <ul class="smallList">
                <li>white piece of paper</li>
                <li>Foam shapes tray: circles, triangles, squares</li>
                <li>Bigger foam sheets.-scissors tacky glue with medium size stick</li>
              </ul>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import Lessons2 from "@/api/lessons2";
export default {
  name: "MaterialCenters",
  data() {
    return {
      tableData: [
        {
          cardName: "Math",
          materialList: "Animal Habitats",
          cardName1: "Math",
          materialList1: "Animal Habitats",
        },
        {
          cardName: "Science",
          materialList: "Animal Habitats",
          cardName1: "Math",
          materialList1: "Animal Habitats",
        },
        {
          cardName: "Visual Art",
          materialList: "Animal Habitats",
          cardName1: "Math",
          materialList1: "Animal Habitats",
        },
        {
          cardName: "Chinese",
          materialList: "Animal Habitats",
          cardName1: "Math",
          materialList1: "Animal Habitats",
        },
      ],
      isEdit: false,
    };
  },
  created() {
    Lessons2.getThemes();
  },
  methods: {
    go() {
      this.isEdit = true;
    },
    leave() {
      this.isEdit = false;
    },
    async Edit () {
     let res = await Lessons2.addPlanMaterial();
    }
  }
}
</script>

<style  lang="less"  scoped>
/deep/.el-table tbody tr > td:nth-child(odd) {
  width: 110px;

  font-family: "Inter";
  font-style: normal;
  font-weight: 600;

  text-align: center;
  color: #323338;
  font-size: 13px;
  background: #fafafa;
}
/deep/ .el-table tbody tr:hover > td:nth-child(even) {
  background-color: transparent !important;
}
/deep/ .el-table tbody tr > td {
  border-top: 1px solid #ebeef5;
  box-sizing: border-box;
}
.smallTop {
  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-size: 13px;
  line-height: 20px;
  color: #323338;
}
.smallList {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  padding-left: 20px;
  color: #323338;
  li {
    list-style: disc;
    line-height: 20px;
  }
}
</style>