<template>
  <!-- 展示材料列表组件   -->
  <div>
    <div v-if="groupCenterName && materials && materials.length > 0"
         style="border: 1px solid #dcdfe6;border-bottom: none;background-color: #f5f6f8; color:#323338; font-weight: bold;"
         class="flex-center-center  font-size-16 height-40 lg-pa-6">
      {{groupCenterName}}
    </div>
    <div v-if="materials && materials.length > 0"
         class="grid-half">
      <div class="display-flex"
           v-for="(material,index) in materials"
           :key="index"
           :style="materials.length - 1 == index && !index % 2 ? 'grid-column-start: 1;grid-column-end: 3;':''">
        <div class="display-flex justify-content align-items font-weight-semibold font-size-14 "
             style="width: 110px;background-color:#FAFAFA; border-top: 1px solid #dcdfe6;color:#323338;">
          <span class="overflow-ellipsis-two word-break text-center add-padding-lr-6"
                :title="isCenter ? material.centerName :material.categoryName">{{isCenter ? material.centerName :material.categoryName}}</span>
        </div>
        <div class="flex-1"
             style="border: 1px solid #dcdfe6;border-bottom: none;min-width: 0">
          <curriculum-materials :is-edit="isEdit"
                                class="h-full"
                                :lesson-materials="material.lessonMaterials"
                                @edit="editMaterial"></curriculum-materials>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import CurriculumMaterials from '../components/CurriculumMaterials'
export default {
  name: 'MaterialList',
  components: { CurriculumMaterials },
  data () {
    return {
    }
  },
  methods: {
    editMaterial (lessonMaterial) {
      this.$emit('edit', lessonMaterial)
    }
  },
  props: {
    materials: {
      type: Array,
      default: () => []
    },
    isCenter: {
      type: Boolean
    },
    isEdit: {
      type: Boolean
    }
  },
  computed: {
    ...mapState({
      planCenterType: state => state.lesson.planCenterType
    }),
    groupCenterName () {
      if (!this.isCenter) {
        let tempName = ''
        this.materials.forEach((material, index) => {
          if (index === 0 && material.categoryName) {
            tempName = material.categoryName
          } else {
            if (material.categoryName) {
              tempName += ' / ' + material.categoryName
            }
          }
        })
        return tempName
      } else {
        if (this.planCenterType === 'K') {
          return this.$t('loc.unitPlannerStep3Stations')
        } else {
          return this.$t('loc.unitPlannerStep3Centers')
        }
      }
    }
  }
}
</script>

<style  lang="less"  scoped>
.grid-half {
  display: grid;
  grid-template-columns: repeat(2, 50%);
  gap: 0;
  border: 1px solid #dcdfe6;
  border-top: none;
}
/deep/.el-table tbody tr > td:nth-child(odd) {
  width: 110px;

  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;

  text-align: center;
  color: #323338;
  font-size: 13px;
  background: #fafafa;
}
/deep/ .el-table tbody tr:hover > td:nth-child(even) {
  background-color: transparent !important;
}
/deep/ .el-table tbody tr > td {
  border-top: 1px solid #ebeef5;
  box-sizing: border-box;
}
.smallTop {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 13px;
  line-height: 20px;
  color: #323338;
}
.smallList {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  padding-left: 20px;
  color: #323338;
  li {
    list-style: disc;
    line-height: 20px;
  }
}
</style>
