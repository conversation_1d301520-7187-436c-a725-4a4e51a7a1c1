<template>
  <div>
    <div class="display-flex justify-content-between add-padding-b-10">
      <div>
        <el-button style=" background-color: #f9bb80; color: white; cursor: default;border: none;">{{$t('loc.materialsList')}}</el-button>
      </div>
      <div>
        <download-materials v-if="!isEdit && (otherMaterials.length > 0 || centerMaterials.length > 0)" :unit-id="unitId" :plan-id="planId" button-type="plain"/>
        <el-button v-if="isEdit && planId && isCanAdd" icon="el-icon-plus" @click="addMaterialDialog">{{$t('loc.addMaterial')}}</el-button>
      </div>
    </div>
    <div style="max-height: 500px; overflow: auto;" class="lg-scrollbar-show">
      <material-list  :materials="otherMaterials" :is-center="false" :is-edit="isEdit" @edit="editMaterial"></material-list>
      <material-list class="add-margin-t-10" :is-edit="isEdit"  :materials="centerMaterials" :is-center="true" @edit="editMaterial"></material-list>
    </div>
    <!-- 材料的空界面 -->
    <div class="flex-column-center" v-if="otherMaterials.length === 0 && centerMaterials.length === 0">
      <img src="@/assets/img/dll/pic_empty_dll.png" style="margin-top: -20px">
      <div class="text-center add-margin-t-8 font-size-14 " >
        {{$t('loc.curriculum73')}}
      </div>
    </div>
    <div v-if="isEdit" id="unit-resource-info">
      <resource-info ref="resourceInfoRef" :plan-id="planId" :curriculum-id="curriculumId" :unit-id="unitId" :unit-week-title="unitWeekTitle" :framework-id="frameworkId" :framework-name="frameworkName" :is-edit="isEdit" :framework-link-url="frameworkLinkUrl"></resource-info>
    </div>
    <add-materials-dialog :plan-id="planId" :curriculum-id="curriculumId" :unit-id="unitId" ref="addMaterialRef" @success="dealMaterialsSuccess"></add-materials-dialog>
  </div>
</template>

<script>
// import Lessons2 from "@/api/lessons2";
// import MediaUploader from "../components/MediaUploader/index.vue";
import MaterialList from "@/views/modules/lesson2/lessonCurriculum/CurriculumWeekPlan/MaterialList";
import ResourceInfo from "../CurriculumUnitDetail/components/ResourceInfo";
import ResourceCard from "../CurriculumUnitDetail/components/ResourceCard";
// import LessonMaterialInput from '@/views/modules/lesson2/lessonLibrary/editor/lessonMaterialInput/index'
import AddMaterialsDialog from '../components/AddMaterialsDialog'
import DownloadMaterials from '../components/DownloadMaterials'
import { mapState } from 'vuex'
export default {
  name: 'GenerateMaterialsList',
  props: ['planId','curriculumId','unitId','unitNum','weekTitle','frameworkId','frameworkName','isEdit','frameworkLinkUrl','show'],
  components: { AddMaterialsDialog, ResourceInfo, ResourceCard, MaterialList, DownloadMaterials },
  data () {
    return {
      otherMaterials: [],
      centerMaterials: [],
      isCanAdd: false,
      checkCompleted: false,
      unitPlanSourceLoaded: false, // Source 是否加载完成
      planMaterialInfoLoaded: false // Material 是否加载完成
    }
  },
  watch: {
    planId (val) {
      if (val) {
        // 获取周计划详情，判断是否与内容源语言不同来决定是否传递翻译参数
        this.getUnitPlanSource(this.contentLanguage)
        this.getAddPlanMaterialInfo()
      }
    },
    show: {
      immediate: true,
      handler (val) {
        if (val) {
          // 获取周计划详情，判断是否与内容源语言不同来决定是否传递翻译参数
          // 获取第一次之后，不再获取
          if (!this.unitPlanSourceLoaded) {
            this.getUnitPlanSource(this.contentLanguage)
          }
          // 获取第一次之后，不再获取
          if (!this.planMaterialInfoLoaded) {
            this.getAddPlanMaterialInfo()
          }
        }
      }
    },
    // 监听当前内容语言类型变更
    contentLanguage (val) {
      if (val) {
        // 获取翻译后的周计划详情
        this.getUnitPlanSource(val)
      }
    }
  },
  mounted () {
    // this.$bus.$on("showGenerate", this.show)
    this.$nextTick(() => {
      //  从单元详情跳转过来的时候, 判断是否需要跳转到资源信息 防止页面渲染过慢, 导致页面跳转不准确
      if (this.$route.params.isToResource) {
        setTimeout(() => {
          let dom = document.getElementById('unit-resource-info')
          if (dom) {
            dom.scrollIntoView({ block: 'start', behavior: 'smooth' })
          }
          this.$route.params.isToResource = false
        }, 1000)
      }
    })
  },
  methods: {
    // async save() {
    //   this.dialogFormVisible = false;
    //   let res = await Lessons2.addPlanMaterial(this.addPlanMaterialData);
    // },
    // 获取系列课程单元周计划的材料和资源详细
    getUnitPlanSource (langCode) {
      if (this.planId && this.planId.trim().length > 0) {
        const needTranslate = (langCode && !this.isSameLanguage)
        let requestParams = {
          'curriculumId': this.curriculumId,
          'unitId': this.unitId,
          'planId': this.planId,
          ...(needTranslate && { langCode })
        }
        this.$axios.get($api.urls().getPlanMaterialsAndResource, { params: requestParams })
          .then(res => {
            // 处理材料中的大小组
            if (res.material && res.material.otherMaterials && res.material.otherMaterials.length > 0) {
              this.otherMaterials = res.material.otherMaterials
            } else {
              this.otherMaterials = []
            }
            // 处理材料中的center
            if (res.material && res.material.centerMaterials && res.material.centerMaterials.length > 0) {
              this.centerMaterials = res.material.centerMaterials
            } else {
              this.centerMaterials = []
            }
            this.$refs.resourceInfoRef && this.$refs.resourceInfoRef.getMaterialsResourceList(JSON.stringify(res))
            this.unitPlanSourceLoaded = true // 标记 getUnitPlanSource 已加载完成
          })
          .catch(error => {
            this.unitPlanSourceLoaded = false // 加载失败时重置标志
          })
      }
    },
    getMaterialsList (res) {
      let tempRes = JSON.parse(res)
      // 处理材料中的大小组
      if (tempRes.material && tempRes.material.otherMaterials && tempRes.material.otherMaterials.length > 0) {
        this.otherMaterials = tempRes.material.otherMaterials
      } else {
        this.otherMaterials = []
      }
      // 处理材料中的center
      if (tempRes.material && tempRes.material.centerMaterials && tempRes.material.centerMaterials.length > 0) {
        this.centerMaterials = tempRes.material.centerMaterials
      } else {
        this.centerMaterials = []
      }
      this.$refs.resourceInfoRef.getMaterialsResourceList(res)
      this.getAddPlanMaterialInfo()
    },
    addMaterialDialog () {
      // 点击添加材料按钮埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_materials_add')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_materials_add')
      }
      this.$emit('checkCompleted')
      if (!this.checkCompleted) {
        return
      }
      this.$refs.addMaterialRef.show(true,false,'')
    },
    editMaterial (lessonMaterial) {
      // 点击编辑材料按钮埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_materials_edit')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_materials_edit')
      }
      this.$emit('checkCompleted')
      if (!this.checkCompleted) {
        return
      }
      this.$refs.addMaterialRef.show(true,true,lessonMaterial)
    },
    // 获取添加材料的选择弹窗信息
    getAddPlanMaterialInfo () {
      if (this.planId && this.planId.trim().length > 0) {
        this.$axios
          .get($api.urls().getAddPlanMaterialInfo + '?planId=' + this.planId)
          .then(res => {
            if (res.centerModels && res.centerModels.length > 0) {
              this.isCanAdd = true
            } else {
              this.isCanAdd = false
            }
            this.planMaterialInfoLoaded = true // 标记 getAddPlanMaterialInfo 已加载完成
          })
          .catch(error => {
            this.isCanAdd = false
            this.planMaterialInfoLoaded = false // 加载失败时重置标志
          })
      }
    },
    // 添加材料成功后的处理
    dealMaterialsSuccess () {
      this.unitPlanSourceLoaded = false // 重置标志，确保可以重新加载
      this.planMaterialInfoLoaded = false // 重置标志，确保可以重新加载
      this.getUnitPlanSource()
      this.getAddPlanMaterialInfo()
    }
  },
  computed: {
    ...mapState({
      contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 内容的源语言码
    }),
    unitWeekTitle () {
      return this.$t('loc.curriculum28') + this.unitNum + ' / ' + this.weekTitle
    },
    // 判断内容的当前语言码是否与源语言码相同
    isSameLanguage () {
      return this.contentLanguage === this.contentOriginalLanguage
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-dialog {
  width: 900px;
}

/deep/.el-dialog__title {
  line-height: 24px;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.tip-text {
  margin-top: 16px;
  margin-bottom: 8px;

  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  color: #323338;
}

.select-box {
  box-sizing: border-box;

  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  width: auto;
  height: 36px;
  background: #ffffff;
  border-radius: 4px;
}

/deep/ .el-dialog__body {
  padding: 0px 20px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

/deep/ .el-dialog__header {
  padding: 20px;
  padding-bottom: 0;
}

.input-text {
  width: 55%;
  height: 185px;
}

.lesson-form-cover {
  width: 40%;
  height: 185px;
  margin-right: 20px;
}

.text {
  font-size: 14px;
}
.box-card {
  width: 60%;
}

.attach-box {
  margin: 30px 10px;

  width: 474px;
  border-top: 1px solid #e4e7ed;
}

/deep/.el-card__body {
  padding: 15px 20px 1px 20px;
}

/deep/.el-divider--horizontal {
  display: block;
  height: 1px;
  width: 100%;
  margin: -1px 0;
}

.media-uploader-cover {
  width: 340px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
