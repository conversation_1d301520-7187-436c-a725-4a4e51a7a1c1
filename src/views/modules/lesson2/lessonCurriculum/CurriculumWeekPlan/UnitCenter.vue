<template>
  <div :key="planCenter.renderKey" class="add-margin-b-12" :class="{'mobile-container': isMobile}">
    <el-card style="width: 100%" :class="'box-card-' + this.getColorIndex" v-if="!isFromUnitDetail">
      <div style="justify-content: space-between;font-weight: bold;font-size: 14px;padding: 0" slot="header" class="clearfix display-flex">
        <div style="" v-if="edit">
          <el-select
            popper-class="header-select"
            v-model="planCenter.center.name"
            @change="updateCenter"
            filterable
            allow-create
            :maxlength="50"
            default-first-option
            automatic-dropdown
            :placeholder="$t('loc.curriculum86')">
            <el-option-group
              v-for="group in tags"
              :key="group.label"
              :label="$t(group.label)">
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item"
                :disabled="item.disabled">
                <div class="display-flex justify-content-between">
                  <div>
                    <span style="float: left">{{ item.label }}</span>
                  </div>
                  <div>
                    <i v-if="!item.isDefault" @click.stop="deleteTag(item)" class="el-icon-close"></i>
                  </div>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </div>
        <div v-else style="text-align: start;" :title="planCenter.center.name">{{planCenter.center.name}}</div>
        <div class="display-flex align-items lg-margin-left-8"><i v-if="edit" @click.stop="deleteCenter(planCenter.center.id)" class="el-icon-delete lg-pointer"></i></div>
      </div>
      <div class="plan-center plan-drag-category" :data-category-id="category.id" :data-category-info="getCategoryInfo(category.id, 1, planCenter.center.id)">
        <plan-item
          :showQuickAddLesson="showQuickAddLesson"
          :isOnlyView="isOnlyView"
          v-for="item in getItems(1)"
          :key="item.id"
          :item="item"
          :planCenter="planCenter"
          ref="weekItem"
          :type="'CENTER_ROW'"
          :edit="edit && !isTemplate"
          :isTemplate="isTemplate"
          :frameworkData="frameworkData"
          :children="children"
          :isLocked="isLocked"
          :planType="planType"
          :long="true"
          :isFromUnitDetail="isFromUnitDetail"
          :unitNumber="unitNumber"
          :dayLessonCount="getDayLessonCount(getItems(1))"
          :copyLessonLoadingDay="copyLessonLoadingDay"
          @copyLesson="copyLesson"
          @hideAllAiAddLessonGuide="hideAllAiAddLessonGuide"
          @callOpenLessonModal="openLessonModal"
          @callOpenAddLessonModal="openAddLessonModal"
          @callBatchSave="batchSave"
          @callViewReflection="viewReflection"
          @singleEditLesson="singleEditLesson"
          @adaptUnitPlan="adaptUnitPlan"
          @editUnitLesson="editUnitLesson"
          @callDeleteItem="deleteItem"
          @callUpdateItem="updateItem">
        </plan-item>
        <!--当复制课程时，渲染一个虚拟的 div ，防止一天的课程超出 5 个-->
        <div :class="{'plan-drag-info' : showUnrealLesson()}" v-if="showUnrealLesson()"></div>
      </div>
    </el-card>
    <el-card style="width: 100%" v-else class="unit-planner-center">
      <div style="justify-content: space-between;font-weight: bold;font-size: 14px;padding: 0" slot="header" class="clearfix display-flex">
        <div style="" v-if="edit">
          <el-select
            popper-class="header-select"
            v-model="planCenter.center.name"
            @change="updateCenter"
            filterable
            allow-create
            :maxlength="50"
            default-first-option
            automatic-dropdown
            :placeholder="$t('loc.curriculum86')">
            <el-option-group
              v-for="group in tags"
              :key="group.label"
              :label="$t(group.label)">
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item"
                :disabled="item.disabled">
                <div class="display-flex justify-content-between">
                  <div>
                    <span style="float: left">{{ item.label }}</span>
                  </div>
                  <div>
                    <i v-if="!item.isDefault" @click.stop="deleteTag(item)" class="el-icon-close"></i>
                  </div>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </div>
        <div v-else style="text-align: start;gap: 10px" :title="planCenter.center.name" class="display-flex align-items w-full">
          <i :class="'lg-icon ' + getIconName(planCenter.center.name)"></i>
          <span class="overflow-ellipsis">{{planCenter.center.name}}</span>
        </div>
        <div class="display-flex align-items lg-margin-left-8"><i v-if="edit" @click.stop="deleteCenter(planCenter.center.id)" class="el-icon-delete lg-pointer"></i></div>
      </div>
      <div class="plan-center plan-drag-category" :data-category-id="category.id" :data-category-info="getCategoryInfo(category.id, 1, planCenter.center.id)">
        <plan-item
          :isOnlyView="isOnlyView"
          :showQuickAddLesson="showQuickAddLesson"
          v-for="item in getItems(1)"
          :key="item.id"
          :item="item"
          :planCenter="planCenter"
          ref="weekItem"
          :type="'CENTER_ROW'"
          :edit="edit && !isTemplate"
          :isTemplate="isTemplate"
          :planType="planType"
          :isLocked="isLocked"
          :frameworkData="frameworkData"
          :children="children"
          :long="true"
          :isFromUnitDetail="isFromUnitDetail"
          :showApply="showApply"
          :unitNumber="unitNumber"
          :dayLessonCount="getDayLessonCount(getItems(1))"
          :copyLessonLoadingDay="copyLessonLoadingDay"
          @copyLesson="copyLesson"
          @hideAllAiAddLessonGuide="hideAllAiAddLessonGuide"
          @callOpenLessonModal="openLessonModal"
          @callOpenAddLessonModal="openAddLessonModal"
          @callBatchSave="batchSave"
          @callViewReflection="viewReflection"
          @singleEditLesson="singleEditLesson"
          @adaptUnitPlan="adaptUnitPlan"
          @editUnitLesson="editUnitLesson"
          @callDeleteItem="deleteItem"
          @callUpdateItem="updateItem">
        </plan-item>
        <!--当复制课程时，渲染一个虚拟的 div ，防止一天的课程超出 5 个-->
        <div :class="{'plan-drag-info' : showUnrealLesson()}" v-if="showUnrealLesson()"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
import PlanItem from '@/views/modules/lesson2/lessonPlan/components/PlanItem'
import tools from '@/utils/tools'
import Api from '@/api/lessons2'
import LessonApi from '@/api/lessons2'
import LessonUtils from '@/utils/lessonUtils'
export default {
  name: 'PlanCenter',
  components: { PlanItem },
  data () {
    return {
      value: '',
      icons: {
        'Math': 'lg-icon-c-math',
        'Art Studio': 'lg-icon-c-art',
        'Science & Engineering': 'lg-icon-c-science',
        'Dramatic Play': 'lg-icon-c-dramatic',
        'Sensory Table': 'lg-icon-c-sensory',
        'Blocks': 'lg-icon-c-blocks',
        'Library & Listening': 'lg-icon-c-library',
        'Writing & Drawing': 'lg-icon-c-writing',
        'Art': 'lg-icon-c-art',
        'Fine Motor Area': 'lg-icon-c-sensory',
        'Music and Movement': 'lg-icon-c-science',
        'Library/Quiet Area': 'lg-icon-c-library',
        'Nature Science/Discovery Area': 'lg-icon-c-writing',
        'Sand and/or Water Area': 'lg-icon-c-sand',
        'Building': 'lg-icon-c-blocks',
        'Drama': 'lg-icon-c-dramatic',
        'Outdoor': 'lg-icon-c-outdoor'
      },
      copyLessonLoadingDay: [], // 复制课程时需要 Loading 的天数
      isMobile: false,
    }
  },
  created () {
    this.initTag()
    this.checkIsMobile()
    window.addEventListener('resize', this.checkIsMobile)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkIsMobile)
  },
  props: {
    tags: {
      type: Array
    },
    colorIndex: {
      type: Number,
      default: 0
    },
    showQuickAddLesson: {
      type: Boolean
    },
    edit: {
      type: Boolean
    },
    showReflection: {
      type: Boolean
    },
    category: {
      type: Object
    },
    frameworkData: {
      type: Array
    },
    children: {
      type: Array
    },
    lastReflection: {
      type: String
    },
    lastReflectionLoading: {
      type: Boolean
    },
    activeGuideDomId: {
      type: String
    },
    // 是否是模板
    isTemplate: {
      type: Boolean
    },
    // 是否能编辑模板
    canEditTemplate: {
      type: Boolean
    },
    planCenter: {
      type: Object
    },
    unitNumber: {
      type: Number
    },
    draging: {
      type: Boolean
    },
    isFromUnitDetail: {
      type: Boolean,
      default: false
    },
    showApply: {
      type: Boolean,
      default: false
    },
    planType: {
      type: String,
      default: ''
    },
    isLocked: {
      type: Object,
      default: null
    },
    isOnlyView: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 周计划复制课程
    copyLesson (item, planCenter, inLessonDialog, callback) {
      const lessonCount = this.getDayLessonCount(this.getItems(item.dayOfWeek))
      LessonUtils.copyLesson(item, planCenter, lessonCount, this.copyLessonLoadingDay, inLessonDialog, (copyItem) => {
        if (copyItem && copyItem.id) {
          // 周计划活动数组中添加新 item
          LessonUtils.updateCategory(copyItem, item.id, this.planCenter.items)
          this.$nextTick(() => {
            // 复制完成后滚动到对应位置
            this.scrollToCopyLesson(copyItem.id)
          })
        }
        // 回调关闭课程拖拽禁用
        callback()
      })
    },

    // 滚动页面到复制的课程的位置
    scrollToCopyLesson (toItemId) {
      this.$nextTick(() => {
        // 获取课程的 ref
        const elements = this.$refs.weekItem
        this.$emit('scrollToCopyLessonHandle', elements, toItemId)
      })
    },

    // 获取当前活动这天课程数量
    getDayLessonCount (lesson) {
      return lesson.filter(plan => plan.lessonId || plan.name || plan.link).length
    },

    // 当复制课程时，成功之前，渲染一个虚拟的 div ，模拟一个课程，防止一天的课程超出 5 个
    showUnrealLesson () {
      return this.copyLessonLoadingDay.includes(this.getItems(1)[0].centerId)
    },
    singleEditLesson (itemId) {
      this.$emit('singleEditLesson', itemId)
    },
    // 改编单元周计划项
    adaptUnitPlan (item) {
      this.$emit('adaptUnitPlan', item)
    },
    // 编辑单元课程
    editUnitLesson (item) {
      this.$emit('editUnitLesson', item)
    },
    /**
     * 获取拖拽分组 item 信息
     */
    getCategoryInfo (categoryId, dayOfWeek, centerId) {
      return JSON.stringify({
        categoryId: categoryId,
        dayOfWeek: dayOfWeek,
        centerId: centerId
      })
    },
    initTag () {
    },
    // 打开选择课程弹窗
    openLessonModal (selectLessonFunc) {
      this.$emit('callOpenLessonModal', selectLessonFunc)
    },

    // 打开快速添加课程弹窗
    openAddLessonModal (selectLessonFunc, name) {
      this.$emit('callOpenAddLessonModal', selectLessonFunc, name, 'Center')
    },

    updateItem (params) {
      this.$emit('callUpdateItem')
    },

    batchSave () {
      this.$emit('callBatchSave')
    },

    viewReflection (lessonId) {
      this.$emit('callViewReflection', lessonId)
    },
    // 更新卡片信息
    updateCenter (tag) {
      let id = this.planCenter.center.id
      let name = tag.value ? tag.value : this.planCenter.center.name
      let colorIndex = this.planCenter.center.colorIndex || '1'
      // 判断 center 卡片名字是否大于50字符
      if (name && name.toString().length > 50) {
        name = name.toString().substring(0, 50)
      }
      let isDefault = tag.isDefault ? tag.isDefault : false
      let params = {
        id: id,
        name: name,
        colorIndex: colorIndex,
        planId: this.category.planId,
        defaultTag: isDefault
      }
      this.planCenter.center.name = name
      this.$emit('callUpdateCenter', params)
    },
    // 删除单个Center卡片
    deleteCenter (id) {
      let hasItem = false
      this.planCenter.items.forEach(x => {
        if (x.name) {
          hasItem = true
        }
      })
      this.$emit('callUpdateTag',undefined, this.planCenter.center.name)
      this.$emit('callDeleteCenter', id, hasItem)
    },
    isEmptyItem (item) {
      if (!item) {
        return true
      }
      return (!item.name || item.name.trim().length === 0) && (!item.measureIds || item.measureIds.length === 0)
    },
    deleteItem (id, categoryId, centerId) {
      this.$emit('callDeleteItem', id, categoryId, centerId)
    },
    deleteTag (tag) {
      this.$emit('callDeleteTag', tag)
    },
    // 根据 CenterName 获取对应的 Icon 图标
    getIconName (centerName) {
      centerName = centerName && centerName.toLowerCase()
      for (let iconsKey in this.icons) {
        const key = iconsKey.toLowerCase()
        if (key === centerName ||
          centerName.indexOf(key) > -1 ||
          key.indexOf(centerName) > -1) {
          return this.icons[iconsKey]
        }
      }
    },
    // 关闭之前的 Ai 生成周计划引导
    hideAllAiAddLessonGuide () {
      this.$emit('hideAllAiAddLessonGuide')
    },
    checkIsMobile() {
      this.isMobile = window.innerWidth <= 768
    }
  },
  computed: {
    getItems () {
      return function (dayOfWeek) {
        // 分类下指定日期的项目列表
        let items = []
        // 如果为传入dayOfWeek参数，说明这个分类是长单元格，项目直接取items，否则生成新的item数组进行展示
        if (!dayOfWeek) {
          items = this.planCenter.items
        } else {
          items = this.planCenter.items.filter(item => item.dayOfWeek === dayOfWeek)
        }
        // 每个单元格做限制最多添加五个项目，超过5个项目，直接返回
        let filterItems = items.filter(item => !this.isEmptyItem(item))
        if (filterItems.length >= 5) {
          // 循环周计划项，给每个项排序
          filterItems.forEach((item, index) => {
            item.sortNum = index
          })
          return filterItems
        }
        // 是否有空的项目可以新增
        let hasEmpty = false
        if (items.length > 0) {
          // 循环项目，判断是否存在空项目
          for (let i = 0; i < items.length; i++) {
            let lastItem = items[i]
            hasEmpty = this.isEmptyItem(lastItem) || hasEmpty
          }
          // 如果有空项目，过滤掉空项目，身于一个空项目
          if (hasEmpty) {
            let empty = items.find(item => this.isEmptyItem(item))
            items = items.filter(item => !this.isEmptyItem(item))
            items.push(empty)
          }
        }
        // 如果没有空单元格，新加一个空的单元格
        if (!hasEmpty) {
          // 新生成的item如果是长单元格的 dayofweek 默认是周一
          let newItem = {
            id: tools.uuidv4(),
            dayOfWeek: dayOfWeek == undefined ? 1 : dayOfWeek,
            categoryId: this.category.id,
            name: '',
            planId: this.category.planId,
            centerId: this.planCenter.center.id || '',
            sortNum: items.length
          }
          // 向分组中加入新的空项目
          // this.$emit('callAddItem', newItem, this.category.id)
          this.planCenter.items.push(newItem)
          this.category.items = this.planCenter.items
          // 如果是长单元格，显示的就是分组的项目，不需要在向显示的items中添加
          if (dayOfWeek) {
            items.push(newItem)
          }
        }
        // 循环周计划项，给每个项排序
        items.forEach((item, index) => {
            item.sortNum = index
        })
        return items
      }
    },
    getNotEmptyItems () {
      return function (dayOfWeek) {
        // 分类下指定日期的项目列表
        let items = []
        // 如果为传入dayOfWeek参数，说明这个分类是长单元格，项目直接取items，否则生成新的item数组进行展示
        if (!dayOfWeek) {
          items = this.planCenter.items
        } else {
          items = this.planCenter.items.filter(item => item.dayOfWeek === dayOfWeek)
        }
        // 只要超过一个就把空的过滤掉
        if (items.length > 1) {
          // 每个单元格做限制最多添加五个项目，超过5个项目，直接返回
          return items.filter(item => !this.isEmptyItem(item))
        }
        return items
      }
    },
    getColorIndex () {
      return this.colorIndex % 10 + 1
    }
  },
  watch: {
    'planCenter.center.name': {
      deep: true,
      handler (newVal, oldVal) {
        this.planCenter.center.name = newVal
        this.$emit('callUpdateTag', newVal, oldVal)
      }
    }
  }
}
</script>

<style lang="less" scoped>

.clearfix:before,
.clearfix:after {
  display: none;
  content: "";
}
.clearfix:after {
  clear: none
}

.box-card-1 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #D0D7F0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #D0D7F0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-2 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #E8D0F0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #E8D0F0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-3 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F0D0D0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F0D0D0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-4 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F0DCD0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F0DCD0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-5 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F0E9D0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F0E9D0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-6 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #DEF0D0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #DEF0D0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-7 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #D0EBF0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #D0EBF0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-8 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #C0E1F3 !important;
  }

  /deep/ .el-input__inner {
    background-color: #C0E1F3;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-9 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #D1D0F0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #D1D0F0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}

.box-card-10 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F7CBE8 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F7CBE8;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;
  }
}
.unit-planner-center {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #EBF7F1 !important;
    height: 32px;
    line-height: 25px;
    color: var(--color-success);
  }
}
/deep/.reflection-item {
  text-align: start;
}

/deep/.lesson-media {
  width: 100% !important;
}

/* 移动端适配样式 */
@media screen and (max-width: 768px) {
  .mobile-container {
    padding: 0 10px;
  }

  .mobile-item-area {
    flex-direction: column;
    
    .lesson-media {
      width: 100% !important;
      height: auto !important;
    }
    
    .preview-lesson {
      .preview-icon,
      .adapt-icon {
        transform: scale(0.9);
      }
    }
  }

  .mobile-item-row {
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
    
    .item-icon {
      margin-bottom: 8px;
    }
    
    .item-content {
      width: 100%;
    }

    .measure-row {
      width: 100%;
    }

    .el-tag {
      margin: 4px;
    }
  }

  .drag-handle-box {
    display: none;
  }

  .lesson-area-item {
    height: 50px;
    line-height: 50px;
  }

  .custom-textarea {
    /deep/ .el-textarea__inner {
      font-size: 14px;
    }
  }

  .lessons-cover {
    .preview-lesson {
      .preview-icon,
      .adapt-icon {
        padding: 5px 10px;
        font-size: 12px;
      }
    }
  }

  .truncate {
    max-width: 90vw;
  }

  .child-check-row {
    padding: 8px;
    
    .child-avatar {
      max-width: 28px;
    }
  }
}
</style>
