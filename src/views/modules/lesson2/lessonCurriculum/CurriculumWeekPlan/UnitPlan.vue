<template>
<!--  周计划表格部分组件，调用需传入以下关键prop参数-->
<!--  1:edit(Boolean) 是否为编辑状态-->
<!--  2:planId(String) 该周计划的周计划Id-->
<!--  3:showLastReflection(Boolean) 是否显示上周反思-->
<!--  v-loading="calendarLoading"-->
  <div v-loading="calendarLoading">
    <div class="calendar-bottom" id="calendar-container">
      <!-- 日历表格 -->
      <div class="plan-table" :class="{'lg-margin-top-20' : !previewing}" id="plan-table">
        <div :class="{'lg-padding-bottom-20': (!edit && planData.showCoreMeasureOpen) || existInterpretation}" class="flex-row-between" style="width: 100%">
          <!-- 解读组件 -->
          <plan-interpretation v-if="planId" :plan-id="planData.id" :teacher-ids="planData.teacherIds" :isShared="false" :canRemove="canRemovePlanInterpretation" :canAdd="canAddPlanInterpretation" @haveInterpretation="haveInterpretation"/>
          <!-- 显示核心测评点开关 -->
          <div class="core-measure display-flex align-items add-margin-b-20" style="gap: 10px;">
            <div v-show="!edit && planData.showCoreMeasureOpen">
              <!-- 核心测评点开关 -->
              <el-switch
                v-model="showCore"
                class="m-l-xs"
                size="mini">
              </el-switch>
              <span class="m-l-xs">
                {{ $t('loc.showCoreMeasureOnly') }}
              </span>
            </div>
            <!-- 改编班级 -->
            <div v-if="adaptedGroupName && !isCurriculumPlugin">
              <span class="font-weight-600">{{ $t('loc.unitAdaptedGroup') }} </span>
              <span>{{ adaptedGroupName }}</span>
            </div>
          </div>
        </div>
        <!-- 主题 -->
        <plan-category
          :canEditTemplate="canEditTemplate"
          :category="themeCategory"
          :edit="edit || review"
          :draging="draging"
          :editTemplate="editTemplate"
          :showReflection="showLastReflection"
          @callAddCategory="addCategory"
          @callDeleteCategory="deleteCategory"
          @callUpdateTheme="updateTheme"
          @callUpdateCategory="updateCategory">
        </plan-category>
        <!-- 上周反思 -->
        <plan-category
          :canEditTemplate="canEditTemplate"
          :edit="edit || review"
          :draging="draging"
          v-if="showLastReflection"
          :editTemplate="editTemplate"
          :category="reflectionCategory"
          :showReflection="showLastReflection"
          :lastReflection="lastReflection"
          :lastReflectionLoading="lastReflectionLoading"
          @callAddCategory="addCategory"
          @callDeleteCategory="deleteCategory"
          @callUpdateCategory="updateCategory">
        </plan-category>
        <!-- 上半部分自定义内容 -->
        <plan-category
          ref="topCategory"
          :canEditTemplate="canEditTemplate"
          :category="category"
          :editTemplate="editTemplate"
          :frameworkData="frameworkData"
          :children="children"
          :edit="edit || review"
          :draging="draging"
          v-for="category in topCategories" :key="category.id"
          @callOpenLessonModal="openLessonModal"
          @callOpenAddLessonModal="openAddLessonModal"
          @callAddCategory="addCategory"
          @callUpdateItem="changeItem"
          @callSingleEditLesson="callSingleEditLesson"
          @callAdaptUnitPlan="callAdaptUnitPlan"
          @callEditUnitLesson="callEditUnitLesson"
          @callDeleteCategory="deleteCategory"
          @callUpdateCategory="updateCategory">
        </plan-category>
        <!-- 周信息 -->
        <plan-category
          :canEditTemplate="canEditTemplate"
          :edit="edit || review"
          :draging="draging"
          :category="weekCategory"
          :editTemplate="editTemplate"
          @callAddCategory="addCategory"
          @callSingleEditLesson="callSingleEditLesson"
          @callAdaptUnitPlan="callAdaptUnitPlan"
          @callEditUnitLesson="callEditUnitLesson"
          @callDeleteCategory="deleteCategory"
          @callUpdateCategory="updateCategory">
        </plan-category>
        <!-- 下半部分自定义内容 -->
        <plan-category
          v-for="category in bottomCategories" :key="category.id"
          v-show="(category && category.items && category.items.length > 1) || category.type !== 'BOTTOM_CENTER_ROW'"
          :isOnlyView="isOnlyView"
          :showQuickAddLesson="showQuickAddLesson"
          :adminEdit="adminEdit"
          :canEditTemplate="canEditTemplate"
          :editTemplate="editTemplate"
          :category="category"
          :showApply="showApply"
          :frameworkData="frameworkData"
          :children="children"
          :edit="edit || review"
          :draging="draging"
          :canAddCenters="canAddCenters"
          :planCenters="planCenters"
          :centerTags="centerTags"
          :isFromUnitDetail="isFromUnitDetail"
          @callAddCenter="addCenter"
          @callDeleteCenter="deleteCenter"
          @callUpdateCenter="updateCenter"
          @callViewReflection="viewReflection"
          @callOpenLessonModal="openLessonModal"
          @callOpenAddLessonModal="openAddLessonModal"
          @callAddCategory="addCategory"
          @callUpdateItem="changeItem"
          @callBatchSave="batchSave"
          @callDeleteCategory="deleteCategory"
          @callUpdateCategory="updateCategory"
          @callAdaptUnitPlan="callAdaptUnitPlan"
          @callEditUnitLesson="callEditUnitLesson"
          @callSingleEditLesson="callSingleEditLesson"
          @callDeleteItem="deleteItem">
        </plan-category>
        <!-- 框架映射信息 -->
        <plan-measure-map
          v-if="showMeasureMap"
          :categories="bottomCategories"
          :frameworkData="frameworkData"
          :reverse="isCAPTKLF"
          :addMargin="canEditTemplate && (edit || review) && !draging"
        ></plan-measure-map>
      </div>
      <!-- 选择课程弹窗 -->
      <select-lesson-modal
        @callSelectLesson="selectLesson"
        ref="selectLessonModal"
      ></select-lesson-modal>
      <!-- 快速添加课程弹窗 -->
      <quick-add-lesson
        v-if="showQuickAddLesson"
        @callSelectLesson="selectLesson"
        ref="addLessonModal"
      ></quick-add-lesson>
    </div>
    <div style="text-align: center;">
      <el-button plian :loading="generateMaterialLoading" v-if="this.hasItem && !previewing && edit" @click="generateMaterialsList">{{ $t('loc.curriculum56') }}</el-button>
    </div>
    <!-- 展示 LessonEditor，当点击了生成内容之后，Editor 显示  -->
    <LessonEditor ref="lessonEditor" :planId="currentPlanId"/>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import PlanCategory from '@/views/modules/lesson2/lessonPlan/components/PlanCategory'
import SelectLessonModal from '@/views/modules/lesson2/lessonPlan/components/SelectLessonModal'
import tools from '@/utils/tools'
import PlanInterpretation from '@/views/modules/lesson2/lessonPlan/components/PlanInterpretation'
import QuickAddLesson from '@/views/modules/lesson2/lessonPlan/components/QuickAddLesson.vue'
// 引导
import Driver from 'driver.js' // import driver.js
import 'driver.js/dist/driver.min.css' // import driver.js css
import steps from '@/assets/js/guide/weeklyPlanner'
import Sortable from 'sortablejs'
import PersonalizePlan from '@/views/modules/lesson2/lessonPlan/components/PersonalizePlan.vue'
import LessonEditor from '@/views/modules/lesson2/lessonBatchAdapter/LessonEditor.vue'
import AdaptUnitTips from '@/views/modules/lesson2/unitPlanner/components/adaptUnits/AdaptUnitTips.vue'
import PlanMeasureMap from '@/views/modules/lesson2/lessonPlan/components/PlanMeasureMap.vue'
import frameworkUtils from '@/utils/frameworkUtils'
export default {
  name: 'UnitPlan',
  components: {
    AdaptUnitTips,
    LessonEditor, PersonalizePlan,
    PlanInterpretation,
    PlanCategory,
    SelectLessonModal,
    QuickAddLesson,
    PlanMeasureMap
  },
  inject: ['curriculum', 'isAuthor', 'isPublic'],
  props: {
    previewing: {
      type: Boolean
    },
    adminEdit: {
      type: Boolean
    },
    edit: {
      type: Boolean
    },
    review: {
      type: Boolean // 是否是管理员审批
    },
    preview: {
      type: Boolean
    },
    planId: {
      type: String
    },
    // 是否生成了材料
    generatedMaterials: {
      type: Boolean
    },
    showLastReflection: {
      type: Boolean
    },
    firstVisit: {
      type: Boolean
    },
    editTemplate: {
      type: Boolean
    },
    curriculumId: {
      type: String
    },
    unitId: {
      type: String
    },
    frameworkId: {
      type: String
    },
    frameworkData: {
      type: Array,
      default: () => []
    },
    viewOnly: {
      type: Boolean
    },
    draging: {
      type: Boolean
    },
    isFromUnitDetail: {
      type: Boolean,
      default: false
    },
    showApply: {
      type: Boolean,
      default: false
    },
    isInterrupted: {
      type: Boolean,
      default: false
    },
    exemplar: {
      type: Boolean,
      default: false
    },
    isOnlyView: {
      type: Boolean,
      default: false
    },
    adaptedGroupName: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      showQuickAddLesson: false,
      calendarLoading: true,
      theme: undefined,
      activityPopoverVisible: false,
      textarea1: undefined,
      weekDays: [],
      planData: {},
      categories: [],
      fixedRows: [{}, {}, {}],
      themeCategory: {},
      reflectionCategory: {},
      weekCategory: {},
      centerCategory: {},
      showMeasureMap: false, // 是否显示测评点映射
      children: [],
      approveModalVisible: false,
      rejectModalVisible: false,
      continueModalVisible: false,
      completeModalVisible: false,
      existModalVisible: false,
      nextPlanId: undefined,
      approveText: '',
      rejectText: '',
      rejectTextError: false,
      reflectionText: '',
      showPlanReflection: false,
      lessonType: 'CREATION',
      searchLessonStr: '',
      selectLessonVisible: false,
      myLessons: [],
      favoritesLessons: [],
      selectLessonFunc: undefined,
      saveReflectionLoading: false,
      submitLoading: false,
      revertLoading: false,
      saveDraftLoading: false,
      reflectionLoading: false,
      approveLoading: false,
      rejectLoading: false,
      lastReflection: '',
      lastReflectionLoading: false,
      planShareRecords: [], // 周计划对应的分享记录
      hideSubmitTip: false, // 是否隐藏提交周计划提示
      timeoutID: undefined,
      approveStatus: '',
      collapse: false,
      activeGuideDomId: '',
      getSharePlannerLoading: false, // 周计划分页加载loading
      totalPlanner: 0, // 分享来的周计划总数
      pageNum: 1, // 查询周计划页数
      hasNextPage: true, // 是否还有下一页周计划
      sharedWeeks: [], // 分享来的周计划
      currentWeekPlan: {}, // 当前的周计划
      hasLastWeekPlan: false, // 有无上一个周计划
      hasNextWeekPlan: false, // 有无下一个周计划
      hasShared: true, // 有无分享的老师
      sharedTeachers: [], // 分享的老师列表
      currentTeacher: {}, // 当前老师
      timmer: null, // 定时器
      replicateModelVisible: false, // 复制周计划弹窗
      replicateLoading: false, // 复制按钮loading
      selectedGroupId: '', // 周计划复制选择的班级
      showNotSelectGroupTip: false, // 复制周计划选择的班级提示
      centers: [], // 当前老师所在的学校,
      pdfLoading: false,
      planNote: null,
      currentGroupId: '', // 查看的周计划的班级Id
      currentCenterId: '', // 查看的周计划的学校Id
      driver: null, // 引导插件对象
      canEditTemplate: false, // 是否可以编辑模板
      planCenters: [],
      generateMaterialLoading: false,
      needGenerateResource: false,
      centerTags: [],
      hasGenerated: false, // 是否已经生成过资源
      existInterpretation: false, // 是否存在周计划解读
      showCore: true,
      customThemeRowName: '', // 自定义主题行名称
      planLessons: [], // 周计划下的课程列表
      currentItemId: '', // 当前需要改编或编辑的 itemId
      newPlanId: '', // 复制的新的周计划 ID
      timeInterval: 5000, // 自动保存的时间间隔
      debouncedSaveDraft: null, // 节流函数
      promiseQueue: Promise.resolve(), // 初始的空 Promise 队列，用于顺序执行 promise
      firstItemMounted: false // 是否首次挂载 planItem 组件
    }
  },
  created () {
    // 等待 planItem 组件挂载完成,通知是否显示核心测评点
    this.$bus.$on('itemMounted', () => {
      if (!this.firstItemMounted) {
        this.showCore = this.planData.showCoreMeasureOpen
        this.firstItemMounted = true
      }
      this.$bus.$emit('changeShowCore', { planId: this.planId, open: this.showCore })
      // 编辑模式下初始化拖拽
      if (this.edit || this.unitOverviewPreviewDrag) {
        // item 挂在完成后初始化拖拽逐渐
        this.initDrag()
      }
    })
    // 等待 planItem 组件挂载完成,通知是否显示核心测评点
    this.$bus.$on('itemMounted', () => {
      this.$bus.$emit('changeShowCore', { planId: this.planId, open: this.showCore })
    })
    this.initWeekDays()
    if (this.preview) {
      this.edit = false
      return
    }
    if (!this.isFromUnitDetail && this.planId) {
      // 获取周计划详情，判断是否与内容源语言不同来决定是否传递翻译参数
      this.getPlan(null, this.contentLanguage)
    }
    // 如果是进入  unit 详情查看周计划
    if (this.isFromUnitDetail && this.planId) {
      // 获取周计划详情，判断是否与内容源语言不同来决定是否传递翻译参数
      this.getUnitPlan(this.contentLanguage)
    }
    this.initFixedRow()
    this.$nextTick(() => {
      if (this.isFromUnitDetail) {
        this.timeInterval = 2000
      }
    })
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      unitOverviewPreviewDrag: state => state.curriculum.unit.unitOverviewPreviewDrag, // 全局 unitOverviewPreviewDrag 用于启用特殊情况下的拖拽预览
      contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 内容的源语言码
    }),
    // 判断内容的当前语言码是否与源语言码相同
    isSameLanguage () {
      return this.contentLanguage === this.contentOriginalLanguage
    },
    // 当前的周计划 ID
    currentPlanId () {
      return this.newPlanId || this.planId
    },
    topCategories () {
      return this.categories.filter(c => c.type && c.type.indexOf('TOP_') != -1)
    },
    bottomCategories () {
      return this.categories.filter(c => c.type && c.type.indexOf('BOTTOM_') != -1)
    },
    isAdmin () {
      if (!this.currentUser) {
        return false
      }
      let role = this.currentUser.role
      return role && role.toUpperCase() === 'OWNER'
    },

    lessons () {
      if (this.lessonType == 'CREATION') {
        return this.myLessons
      } else {
        return this.favoritesLessons
      }
    },

    reflectionConvertText () {
      if (!this.reflectionText) {
        return ''
      }
      return this.reflectionText.replace(/\n/g, '<br />')
    },
    isShared () {
      // 如果过是查看分享，获取分享来的用户
      if (this.$route.name === 'teacherShadow') {
        this.getShareUsers()
        return true
      }
      return false
    },

    shareRecords () {
      if (this.planShareRecords && this.planShareRecords.length > 0) {
        return this.planShareRecords
      }
      return this.currentWeekPlan ? this.currentWeekPlan.shareRecords : []
    },

    // 是否正在引导
    isGuiding () {
      if (this.driver) {
        return this.driver.hasHighlightedElement()
      } else {
        return false
      }
    },
    canAddCenters () {
      return this.categories.filter(x => x.type === 'BOTTOM_CENTER_ROW').length === 0
    },
    computeIsAuthor () {
      return this.isAuthor
    },
    // 能否移除周计划解读
    canRemovePlanInterpretation () {
      // 如果是范例，不能移除
      if (this.exemplar) {
        return false
      }
      let isOwner = this.currentUser && this.currentUser.role2 === 'agency_owner'
      let isAuthor = false
      if (this.curriculum) {
        // 系列课程作者或者 Agency Owner 才能移除
        isAuthor = this.curriculum.authorId === this.currentUser.user_id
      } else if (this.computeIsAuthor) {
        // 系列课程作者或者 Agency Owner 才能移除
        isAuthor = this.computeIsAuthor()
      }
      return (isOwner || isAuthor) && !this.isPublicTemplate && !this.previewing
    },
    // 能否添加周计划录屏
    canAddPlanInterpretation () {
      return this.canRemovePlanInterpretation && this.$route.name == 'curriculumUnitDetail' && !this.previewing && this.planData.status !== 'A_DRAFT'
    },
    // 是否是公共模板下的周计划
    isPublicTemplate () {
      return (this.isPublic && this.isPublic()) || false
    },
    // 是否是 CAPTKLF 框架的周计划
    isCAPTKLF () {
      return frameworkUtils.isCAPTKLF(this.frameworkId)
    }

  },
  methods: {
    // 初始分组高度
    initCategoryHeight () {
      let tops = this.$refs.topCategory
      if (tops && tops.length > 0) {
        tops.forEach(top => {
          top.resizeTextarea()
        })
      }
    },
    /**
     * 初始化拖拽实例
     */
    initDrag () {
      // 拿到所有的拖拽分组
      var categories = this.$el.querySelectorAll('.plan-drag-category')
      // 遍历所有的拖拽分组，创建拖拽实例
      var _this = this
      var tip = this.$t('loc.plan151')
      var actToTextMsg = this.$t('loc.doPlan5')
      var oneTextMsg = this.$t('loc.doPlan6')
      for (var dragCategory of categories) {
        // 如果已经创建拖拽实例，跳过
        if (dragCategory.sort) {
          continue
        }

        // unit overview 时 center 不做拖拽
        if (this.unitOverviewPreviewDrag) {
          const dragInfo = dragCategory.getAttribute('data-category-info')
          if (dragInfo) {
            const dragInfoVal = JSON.parse(dragInfo)
            if (dragInfoVal && dragInfoVal.centerId) {
              continue
            }
          }
        }

        // 创建拖拽实例
        dragCategory.putTip = true
        // 创建拖拽实例
        dragCategory.sort = Sortable.create(dragCategory, {
          group: {
            name: 'shared',
            // 拖拽到目标分组时，判断是否可以放置
            put: function (to, from, el) {
              // 拿到目标分组的可拖拽项目数量
              let length = to.el.querySelectorAll('.plan-drag-info').length
              let toTextLength = to.el.querySelectorAll('.text-col').length
              // 目标单元格的类型
              let toType = toTextLength > 0 ? 'text' : 'activity'
              let fromActivityLength = from.el.querySelectorAll('.activity-col').length
              // 分组内移动或要移动到的分组内小于 5 个项目就可以拖拽
              let canPut = length < 5 || (length === 5 && (to.el === from.el || el.parentElement === to.el))

              // unit overview 时大小组之间不做拖拽
              if (_this.unitOverviewPreviewDrag) {
                const toDragInfo = to.el.getAttribute('data-category-info')
                const fromDragInfo = from.el.getAttribute('data-category-info')
                if (toDragInfo && fromDragInfo) {
                  const toDragInfoVal = JSON.parse(toDragInfo)
                  const fromDragInfoVal = JSON.parse(fromDragInfo)
                  if (toDragInfoVal.categoryId !== fromDragInfoVal.categoryId) {
                    canPut = false
                    tip = _this.$t('loc.planUnitOverviewDrag')
                    // 有概率 dragCategory.putTip 变为 undefined，进行兼容处理
                    if (dragCategory.putTip === undefined) {
                      // console.log('有概率 dragCategory.putTip 变为 undefined')
                      dragCategory.putTip = true
                    }
                  }
                }
              }

              // 如果已有 5 个项目，提示不能再拖拽
              if (dragCategory.putTip && !canPut) {
                dragCategory.putTip = false
                _this.$message({
                  message: tip,
                  type: 'error',
                  duration: 2000,
                  onClose: function () {
                    dragCategory.putTip = true
                  }
                })
              }
              if (canPut) {
                // 判断是否活动单元格拖拽到文本单元格
                let activityToText = fromActivityLength !== 0 && toTextLength !== 0
                if (activityToText) canPut = false
                // 如果活动单元格拖拽到文本单元格就不可以拖拽
                if (dragCategory.putTip && activityToText) {
                  dragCategory.putTip = false
                  _this.$message({
                    message: actToTextMsg,
                    type: 'error',
                    duration: 2000,
                    onClose: function () {
                      dragCategory.putTip = true
                    }
                  })
                }
              }
              // 文本单元格分组内移动并且要移动到的分组内小于 1 个项目就可以拖拽
              if (canPut && toType === 'text') {
                canPut = length < 1
                if (dragCategory.putTip && !canPut) {
                  dragCategory.putTip = false
                  _this.$message({
                    message: oneTextMsg,
                    type: 'error',
                    duration: 2000,
                    onClose: function () {
                      dragCategory.putTip = true
                    }
                  })
                }
              }
              return canPut
            }
          },
          handle: '.drag-handle',
          ghostClass: 'plan-drag-ghost',
          animation: 150,
          scroll: true,
          scrollSensitivity: 50,
          scrollSpeed: 10,
          bubbleScroll: true,
          forceFallback: true,
          onEnd: evt => {
            // 拖拽结束，更新数据
            // 拿到拖拽的目标分组和原分组
            var from = evt.from
            var to = evt.to
            // 拿到拖拽的项目
            var item = evt.item
            var oldSort = JSON.parse(item.dataset.dragInfo).sortNum
            var newSortNum = 0
            if (item.previousElementSibling) {
              newSortNum = JSON.parse(item.previousElementSibling.dataset.dragInfo).sortNum
            } else {
              newSortNum = 0
            }
            // 如果目标分组和原分组不一致，则更新数据
            if (from !== to || oldSort !== newSortNum) {
              // 拿到原分组的信息
              var fromCategory = JSON.parse(from.dataset.categoryInfo)
              // 拿到目标分组的信息
              var toCategory = JSON.parse(to.dataset.categoryInfo)
              // 拿到拖拽项目的信息
              var dragInfo = JSON.parse(item.dataset.dragInfo)
              // 拿到周计划的分类和中心区角活动分组
              var categories = JSON.parse(JSON.stringify(_this.categories))
              var planCenters = JSON.parse(JSON.stringify(_this.planCenters))
              var currentItem = null
              if (fromCategory.centerId) {
                // 遍历周计划的中心区角活动分组，找到原项目所在的中心区角活动分组，把原项目从中心区角活动分组中移除
                planCenters.forEach(center => {
                  if (center.center.id === fromCategory.centerId) {
                    var items = center.items
                    currentItem = items.find(x => x.id === dragInfo.id)
                    var delSortNum = items.indexOf(currentItem)
                    items.forEach(item => {
                      if (item.sortNum > delSortNum) {
                        item.sortNum = item.sortNum - 1
                      }
                    })
                    var filterItems = items.filter(x => x.id !== dragInfo.id)
                    center.items = filterItems
                    center.renderKey = tools.uuidv4()
                  }
                })
              } else {
                // 遍历周计划的分类，找到原项目所在的分类，把原项目从分类中移除
                categories.forEach(category => {
                  if (category.id === fromCategory.categoryId) {
                    var items = category.items
                    var filterItems = items.filter(x => x.dayOfWeek === fromCategory.dayOfWeek)
                    currentItem = items.find(x => x.id === dragInfo.id)
                    var delSortNum = filterItems.indexOf(currentItem)
                    filterItems.forEach(item => {
                      if (item.sortNum > delSortNum) {
                        item.sortNum = item.sortNum - 1
                      }
                    })
                    items.forEach(item => {
                      filterItems.forEach(x => {
                        if (item.id === x.id) {
                          item.sortNum = x.sortNum
                        }
                      })
                    })
                    category.items = items.filter(x => x.id !== dragInfo.id)
                    category.renderKey = tools.uuidv4()
                  }
                })
              }
              // 遍历周计划的分类，找到目标分组，把原项目添加到目标分组
              if (toCategory.centerId) {
                // 遍历周计划的中心区角活动分组，找到目标分组，把原项目添加到目标分组
                planCenters.forEach(center => {
                  if (center.center.id === toCategory.centerId) {
                    currentItem.dayOfWeek = toCategory.dayOfWeek
                    currentItem.centerId = toCategory.centerId
                    currentItem.categoryId = toCategory.categoryId
                    currentItem.sortNum = newSortNum
                    center.items.splice(newSortNum, 0, currentItem)
                    center.items.forEach((x, index) => {
                      item.sortNum = index
                    })
                    center.renderKey = tools.uuidv4()
                  }
                })
              } else {
                categories.forEach(category => {
                  if (category.id === toCategory.categoryId) {
                    var items = category.items
                    var filterItems = category.items.filter(x => x.dayOfWeek === toCategory.dayOfWeek)
                    currentItem.dayOfWeek = toCategory.dayOfWeek
                    currentItem.centerId = toCategory.centerId
                    currentItem.categoryId = toCategory.categoryId
                    // 如果新位置大于等于分组中元素的个数并且分组中的最后一个元素是临时元素时，与分组中的最后一个元素交换位置
                    if (newSortNum >= filterItems.length && filterItems[filterItems.length - 1].name === '') {
                      // 创建一个临时存放位置的变量
                      let tempIndex
                      // 把最后一个元素的位置赋值给临时变量
                      tempIndex = filterItems[filterItems.length - 1].sortNum
                      // 新位置的索引重新赋值给最后一个元素
                      filterItems[filterItems.length - 1].sortNum = newSortNum
                      // 把临时变量存储的位置赋值给原项目
                      currentItem.sortNum = tempIndex
                      // 将临时变量赋值给新的位置
                      newSortNum = tempIndex
                    } else {
                      currentItem.sortNum = newSortNum
                    }
                    // 将当前的 item 插入到新的位置
                    filterItems.splice(newSortNum, 0, currentItem)
                    filterItems.forEach((item, index) => {
                      item.sortNum = index
                    })
                    var otherItems = items.filter(x => x.dayOfWeek !== toCategory.dayOfWeek)
                    category.items = otherItems.concat(filterItems)
                    category.renderKey = tools.uuidv4()
                  }
                })
              }
              // 给周计划的分类和中心区角活动分组重新赋值
              _this.categories = categories
              _this.planCenters = planCenters
              _this.planData.categories = _this.categories
              // 拿到周计划项内容
              let params = {
                id: currentItem.id,
                name: currentItem.name,
                sortNum: currentItem.sortNum,
                categoryId: currentItem.categoryId,
                planId: currentItem.planId,
                lessonId: currentItem.lessonId,
                link: currentItem.link ? currentItem.link.trim() : null,
                dayOfWeek: currentItem.dayOfWeek,
                childIds: currentItem.childIds,
                measureIds: currentItem.measureIds,
                frameworkId: currentItem.frameworkId,
                centerId: currentItem.centerId ? currentItem.centerId : null
              }
              // unit overview 时记录拖拽的周
              if (this.unitOverviewPreviewDrag) {
                // 记录有拖拽的周计划
                this.setUnitOverviewDragPlanId(currentItem.planId)

                // 将新的 Promise 添加到 promiseQueue 中
                this.$store.commit('curriculum/INC_UNIT_PLAN_DRAG_ITEM_COUNT')
                this.promiseQueue = this.promiseQueue
                  .then(() => {
                    // 更新周计划项
                    return LessonApi.updateUnitOverviewItemInfo(params).then(response => {
                    }).catch(error => {
                    })
                  })
                  .finally(() => {
                    this.$store.commit('curriculum/DEC_UNIT_PLAN_DRAG_ITEM_COUNT')
                    // 这里是每个 Promise 完成后的处理
                    // console.log('Promise finished')
                  })
              } else {
                // 不是通过 unit planner 中 unit overview 进行拖拽，直接更新周计划项
                LessonApi.updateItem(params).then(response => {
                }).catch(error => {
                })
              }
            }
          }
        })
      }
    },

    /**
     * 记录拖拽的周
     */
    setUnitOverviewDragPlanId (planId) {
      this.$store.commit('curriculum/SET_UNIT_OVERVIEW_PREVIEW_DRAG_PLAN_IDS', planId)
    },

    /**
     * 初始化周信息
     */
    initWeekDays () {
      this.weekDays = [
        {
          day: 1,
          name: 'Monday'
        }, {
          day: 2,
          name: 'Tuesday'
        }, {
          day: 3,
          name: 'Wednesday'
        }, {
          day: 4,
          name: 'Thursday'
        }, {
          day: 5,
          name: 'Friday'
        }
      ]
    },

    initFixedRow () {
      this.customThemeRowName = this.planData && this.planData.customThemeRowName
      this.themeCategory = {
        type: 'THEME_ROW',
        name: (this.planData && this.planData.customThemeRowName) || this.$t('loc.plan4'),
        items: [{
          name: this.theme
        }],
        sortNum: 0,
        guideRowDomId: 'guide-weekly-plan-theme'
      }
      this.reflectionCategory = {
        type: 'REFLECTION_ROW',
        name: this.$t('loc.plan25'),
        items: [{
          name: this.theme
        }],
        sortNum: 0
      }
      this.centerCategory = {
        type: 'CENTER_ROW',
        name: 'Centers',
        sortNum: 0
      }
      this.weekCategory = {
        type: 'WEEK_ROW',
        sortNum: 0
      }
    },

    setPreviewData (previewData) {
      this.planData = previewData
      this.theme = previewData.theme
      this.categories = previewData.categories
      this.initFixedRow()
      this.calendarLoading = false
      this.$forceUpdate()
    },

    // 获取单元下周计划详情
    getUnitPlan (langCode) {
      this.calendarLoading = true
      // 获取周计划参数
      LessonApi.getUnitPlan({
        id: this.planId,
        ...((langCode && !this.isSameLanguage) && { langCode })
      }).then(planData => {
        // 判断是否需要中断，不需要再去判断是否还有未生成课程的活动
        if (!this.isInterrupted) {
          // 是否中断
          let interrupted = false
          // 过滤出底部分类
          const categories = planData.categories.filter(category => category.type.indexOf('BOTTOM_') !== -1)
          for (let i = 0; i < categories.length; i++) {
            const category = categories[i]
            for (let j = 0; j < category.items.length; j++) {
              const item = category.items[j]
              // 如果有未生成课程的活动，需要中断
              if (!item.lessonId) {
                this.$store.commit('SET_PLAN_ITEM', item)
                this.$emit('callInterrupted')
                interrupted = true
                break
              }
            }
            // 中断后跳出循环
            if (interrupted) {
              break
            }
          }
        }
        this.showCore = planData.showCoreMeasureOpen
        // 能否编辑模板
        this.canEditTemplate = planData.canEditTemplate
        this.centerTags = planData.centerTags
        this.planData = planData
        this.showMeasureMap = planData.showMeasureMap
        this.theme = planData.theme
        this.categories = planData.categories
        this.planCenters = planData.planCenters
        // 获取单元 ID
        const unitId = this.$route.query.unitId
        // 如果单元 ID 存在，则设置单元 ID
        if (unitId && this.isFromUnitDetail) {
          this.planData.unitId = unitId
        }
        this.loadMeasures(this.frameworkId)
        // 更新分类索引值
        this.updateCategoryIndex()
        this.initFixedRow()
        // 开始的周计划可以写反思
        let fromDate = planData.fromAtLocal
        this.showPlanReflection = fromDate && this.$moment(fromDate).isSameOrBefore(this.$moment())
        this.calendarLoading = false
        // 如果需要显示反思内容，则显示反思
        if (this.showPlanReflection) {
          this.getReflection()
        }
        // 是否隐藏提交周计划提示
        this.hideSubmitTip = planData.hideSubmitTip
      }).catch(() => {
        this.calendarLoading = false
      })
    },

    // 获取周计划详情
    getPlan (isShared, langCode) {
      this.calendarLoading = true
      this.planData.status = undefined
      // 获取周计划参数
      let params = {
        id: this.planId,
        edit: this.edit
      }
      // 如果是分享来的，参数加上创作者id
      if (isShared) {
        params = {
          id: this.currentWeekPlan.id,
          edit: this.edit,
          shareUserId: this.currentTeacher.id
        }
        // 获取周计划前，先判断定时器是否为空，不空则清除定时任务
        if (this.timmer) {
          clearTimeout(this.timmer)
        }
      }
      // 如果传递了语言码参数，则添加
      if (langCode && !this.isSameLanguage) {
        params.langCode = langCode
      }
      // 当前页面类型
      let viewType = 'NORMAL_VIEW'
      if (this.$route.query) {
        let shareId = this.$route.query.shareId
        if (shareId && this.isAdmin) {
          viewType = 'SHARE_VIEW'
          params.shareId = shareId
        }
      }
      params.source = viewType
      // 调用接口获取周计划详情
      LessonApi.getPlan(params).then(planData => {
        this.showCore = planData.showCoreMeasureOpen
        // 能否编辑模板
        this.canEditTemplate = planData.canEditTemplate
        // 如果是查看分享的周计划，不能直接定位到这个周计划的班级，否则，可直接看这个班的周反思
        if (isShared) {
          this.currentGroupId = ''
          this.currentCenterId = ''
        } else {
          this.currentGroupId = planData.groupId
          this.currentCenterId = planData.centerId
        }
        this.centerTags = planData.centerTags
        this.planData = planData
        this.showMeasureMap = planData.showMeasureMap
        this.theme = planData.theme
        this.categories = planData.categories
        this.planCenters = planData.planCenters
        this.loadMeasures(this.frameworkId)
        // 更新分类索引值
        this.updateCategoryIndex()
        // 数据传给父组件，用于头部显示
        this.$emit('setPlanInfo', this.planData)
        this.initFixedRow()
        // 开始的周计划可以写反思
        let fromDate = planData.fromAtLocal
        this.showPlanReflection = fromDate && this.$moment(fromDate).isSameOrBefore(this.$moment())
        this.calendarLoading = false
        // 如果需要显示反思内容，则显示反思
        if (this.showPlanReflection) {
          this.getReflection()
        }
        // 是否隐藏提交周计划提示
        this.hideSubmitTip = planData.hideSubmitTip
        // 如果是查看分享，等待5s,调用已读接口
        if (isShared) {
          this.timmer = setTimeout(() => {
            this.$axios.post($api.urls().readPlan,{
              planId: this.currentWeekPlan.id
            })
          }, 5000)
        }
        // 显示分享记录
        if (viewType === 'SHARE_VIEW') {
          this.planShareRecords = planData.shareRecords
        }
        // this.setGuide()
      }).catch(error => {
        this.calendarLoading = false
        // 其他人正在编辑，跳转到查看页
        if (error && error.response && error.response.data.error_code === 'plan_editing') {
          this.edit = false
          this.$router.replace({
            name: 'view-plan',
            params: {
              planId: this.planId
            }
          })
        }
      })
    },

    setGuide () {
      if (!this.firstVisit) {
        return
      }
      this.firstVisit = false
      // 是否有目标行
      let hasGoal = false
      // 第一个活动项目
      let firstItem
      // 遍历分类
      this.categories.forEach(category => {
        let type = category.type
        let name = category.name
        // 找到目标行，设置引导使用的 DOM ID
        if (!hasGoal && type && name && type.indexOf('TOP_') != -1) {
          hasGoal = true
          // 设置目标行引导步骤标题
          steps[1].popover.title = this.$t('loc.plan169')
          category.guideRowDomId = 'guide-weekly-plan-goal'
          category.guideCategoryInputDomId = 'guide-weekly-plan-editTip'
          category.guideOperationDomId = 'guide-weekly-plan-add-del-tooltip'
        }
        // 找到第一个活动项目
        if (!firstItem && type.indexOf('BOTTOM_') != -1) {
          // 如果分组类型是整行的，将lesson引导弹窗显示到下面
          if (type == 'BOTTOM_WEEK_COL') {
            steps[2].popover.position = 'bottom'
          }
          category.items.forEach(item => {
            if (item && item.dayOfWeek == 1) {
              firstItem = item
            }
          })
          // 如果分类中没有项目，默认添加一个
          if (!firstItem) {
            firstItem = {
              dayOfWeek: 1,
              categoryId: category.id,
              name: '',
              planId: category.planId
            }
            category.items.push(firstItem)
          }
          // 设置引导使用的 DOM ID
          firstItem.guideItemDomId = 'guide-weekly-plan-lesson'
        }
      })
      // 创建引导插件对象
      this.driver = new Driver({
        className: 'guide-style',
        opacity: 0.5, // 屏蔽罩透明度
        allowClose: false,
        closeBtnText: this.$t('loc.planGuideSkip'),
        nextBtnText: this.$t('loc.planGuideNext'),
        doneBtnText: this.$t('loc.planGuideDone'),
        // 引导内容高亮前执行的方法
        onHighlightStarted: (element) => {
          // 添加分类按钮
          let categoryAddDom = document.getElementById('guide-weekly-plan-add-del-tooltip-add')
          // 移除分类按钮
          let categoryRemoveDom = document.getElementById('guide-weekly-plan-add-del-tooltip-remove')
          // 当前引导元素 DOM
          let dom = element.node
          // 当前引导元素 DOM ID
          let domId = dom.id
          // 分类添加、删除引导
          if (domId === 'guide-weekly-plan-add-del-tooltip') {
            // 元素设为可见
            dom.style.display = 'block'
            // 添加、删除按钮样式修改
            if (categoryAddDom) {
              categoryAddDom.classList.add('add-category-hover')
            }
            if (categoryRemoveDom) {
              categoryRemoveDom.classList.add('remove-category-hover')
            }
          }
          // 将上一步修改的 DOM 属性还原
          if (domId === 'guide-weekly-plan-editTip') {
            let operationDom = document.getElementById('guide-weekly-plan-add-del-tooltip')
            // 隐藏 DOM
            if (operationDom) {
              operationDom.style.display = 'none'
            }
            // 还原添加、删除按钮样式
            if (categoryAddDom) {
              categoryAddDom.classList.remove('add-category-hover')
            }
            if (categoryRemoveDom) {
              categoryRemoveDom.classList.remove('remove-category-hover')
            }
          }
          // 最后一步，添加边框class
          if (domId === 'guide-weekly-plan-editTip') {
            var editDom = document.getElementById('guide-weekly-plan-editTip')
            editDom.children[0].classList.add('row-border')
          }
          if (domId === 'step2-info') {
            dom.style.display = 'block'
          }
          if (domId === 'step3-info') {
            dom.style.display = 'block'
            const step2 = document.getElementById('step2-info')
            step2.style.display = 'none'
          }
          // 设置当前的引导元素 ID
          this.activeGuideDomId = domId
        },
        // 完全高亮时执行的方法
        onHighlighted: (element) => {
          // 如果没有目标，引导共两步，第一步执行后就将高亮的元素切换到课程上
          if (!hasGoal) {
            this.activeGuideDomId = 'guide-weekly-plan-lesson'
          }
          // 当第二步高亮的时候，提前将第三步要高亮的元素切换，防止监听不及时样式不正确
          if (element.node.id === 'guide-weekly-plan-goal') {
            this.activeGuideDomId = 'guide-weekly-plan-lesson'
          }
        },
        // 引导步骤完成时执行的方法
        onDeselected: (element) => {
          // 当前引导元素 DOM
          let dom = element.node
          // 当前引导元素 DOM ID
          let domId = dom.id
          if (domId === 'step2-info') {
            dom.style.display = 'none'
          }
          if (domId === 'step3-info') {
            dom.style.display = 'none'
          }
          let result = { 'features': ['WEEKLY_PLAN_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then()
          this.$store.commit('SET_FIRSTVISIT', false)
          localStorage.setItem(this.currentUser.user_id + 'WEEKLY_PLAN_GUIDE', false)
          element.node.style.pointerEvents = 'auto'
        },
        // 所有引导步骤执行后后执行的方法
        onReset: () => {
          // 清除高亮domId
          this.activeGuideDomId = undefined
        }
      })
      this.$nextTick(() => {
        // 设置引导步骤
        // 如果机构使用了模板，去除引导步骤的后步，并按照机构模板剩余的分组进行引导
        if (!this.canEditTemplate) {
          steps.splice(1, 1)
          steps[0].popover.prevBtnText = '(1/2)'
          steps[1].popover.prevBtnText = '(2/2)'
          steps[1].popover.className = 'guide-weekly-plan-editTip'
        } else {
          steps[2].popover.closeBtnText = ''
          steps[2].popover.className = 'guide-weekly-plan-editTip'
        }
        this.driver.defineSteps(steps)
        // 开始引导
        this.driver.start()
        // 禁用引导元素鼠标事件
        this.driver.steps.forEach(el => { el.node.style.pointerEvents = 'none' })
      })
    },

    getFrameworkData (groupId) {
      // 非编辑状态不调用接口直接返回
      if ((!this.edit) || this.editTemplate) {
        return
      }
      LessonApi.getGroupFrameworkMeasures({
        groupId: groupId,
        filterIep: true
      }).then(response => {
        this.frameworkData = this.handleFrameworkData(response)
      }).catch(error => {})
    },
    // 处理框架数据，将框架的所有叶子节点提取到一级 domain 种，适配 plan item 组件中的数据结构
    handleFrameworkData (data) {
      // 遍历每个 framework
      return data.frameworks.map(framework => {
        const newFramework = {
          ...framework,
          child_domains: framework.child_domains.map(domain => {
            // 创建一级领域对象
            const newDomain = {
              ...domain,
              child_domains: []
            }
            // 递归提取所有叶子节点并放入领域的 child_domains 中
            const collectLeafNodes = (node, parent) => {
              if (node.child_domains && node.child_domains.length > 0) {
                node.child_domains.forEach(child => collectLeafNodes(child, parent));
              } else {
                parent.child_domains.push(node)
              }
            }
            domain.child_domains.forEach(child => collectLeafNodes(child, newDomain));
            return newDomain
          })
        }
        return newFramework
      })
    },
    // 加载框架的测评点（改为从外部传递）
    loadMeasures (frameworkId) {
      // if (!frameworkId) {
      //   return
      // }
      // let params = {
      //   frameworkId: frameworkId
      // }
      // LessonApi.getFrameworkMeasures(params)
      //   .then(res => {
      //     this.frameworkData = this.handleFrameworkData(res)
      //   })
    },
    getChildren (groupId) {
      if (!this.edit) {
        return
      }
      LessonApi.getChildren({
        groupId: groupId,
        pageNum: 1,
        pageSize: 1000,
        sort: 'lastName',
        order: 'asc'
      }).then(response => {
        this.children = response.results
      }).catch(error => {})
    },

    updateCategoryIndex () {
      let categoryIndex = 0
      this.categories.forEach(c => {
        c.index = categoryIndex++
        c.planId = this.planId
      })
    },

    // 返回周计划列表
    goBack () {
      this.$router.push({
        name: 'list-plan'
      })
    },
    // 是否有周计划讲解
    haveInterpretation (value) {
      this.existInterpretation = value
    },
    // 获取管理员分享过来的用户列表
    getShareUsers () {
      this.$axios.get($api.urls().listShareUsers)
        .then(res => {
          if (res.users.length > 0) {
            this.hasShared = true
            this.sharedTeachers = res.users
            this.currentTeacher = this.sharedTeachers[0]
            // 获取分享过来的该老师的周计划
            this.getPlanList()
          } else {
            // 空页面
            this.hasShared = false
          }
        })
    },
    // 下一页周计划
    nextPageWeeklyPlanner () {
      this.pageNum += 1
      this.getPlanList()
    },
    // 获取分享过来的周计划列表
    getPlanList () {
      this.getSharePlannerLoading = true
      this.$axios.post($api.urls().listSharedPlansByUser, {
        shareUserId: this.currentTeacher.id,
        pageSize: 20, // 每页默认20个周计划
        page: this.pageNum
      })
        .then(res => {
          this.getSharePlannerLoading = false
          this.totalPlanner = res.total
          // 如果现有的周计划不足所有周计划数量，可以继续获取
          if (this.totalPlanner > this.pageNum * 20) {
            this.hasNextPage = true
          } else {
            this.hasNextPage = false
          }
          res.items.forEach(x => {
            x.fromAtLocal = this.$moment(x.fromAtLocal).format('MM/DD')
            x.toAtLocal = this.$moment(x.toAtLocal).format('MM/DD')
          })
          // 将新获取的周计划加入到新的周计划列表中
          res.items.forEach(x => {
            this.sharedWeeks.push(x)
          })
          this.currentWeekPlan = this.sharedWeeks[0]
          if (!this.planId) {
            this.planId = this.currentWeekPlan.id
            this.getPlan(true)
            this.hasLastWeekPlan = false
            // 判断是否可以切换周计划
            if (this.sharedWeeks.length > 1) {
              this.hasNextWeekPlan = true
            } else {
              this.hasNextWeekPlan = false
            }
          }
        })
    },
    // 切换老师
    changeTeacher (item) {
      if (this.currentTeacher == item) {
        return
      }
      this.pageNum = 1
      this.sharedWeeks = []
      this.currentTeacher = item
      // 获取该老师被分享给该用户的所有周计划
      this.getPlanList()
    },
    // 切换周计划
    changeWeek (week) {
      if (week === 'getMore') {
        this.nextPageWeeklyPlanner()
        return
      }
      if (this.currentWeekPlan == week) {
        return
      }
      this.currentWeekPlan = week
      this.planId = this.currentWeekPlan.id
      // 获取周计划详情
      this.getPlan(true)
      // 调整切换周计划逻辑
      let index = this.sharedWeeks.indexOf(this.currentWeekPlan)
      if (index == 0) {
        this.hasLastWeekPlan = false
        this.hasNextWeekPlan = true
      }
      if (index == this.sharedWeeks.length - 1) {
        this.hasNextWeekPlan = false
        this.hasLastWeekPlan = true
      }
    },

    // 上一个周计划
    lastWeekPlan () {
      // 调整切换周计划逻辑
      let index = this.sharedWeeks.indexOf(this.currentWeekPlan)
      if (index > 0) {
        // 将切换下一个周计划按钮打开
        this.hasNextWeekPlan = true
        this.currentWeekPlan = this.sharedWeeks[index - 1]
        this.planId = this.currentWeekPlan.id
        // 获取周计划
        this.getPlan(true)
        if (index - 1 == 0) {
          this.hasLastWeekPlan = false
        } else {
          this.hasLastWeekPlan = true
        }
      }
    },

    // 下一个周计划
    nextWeekPlan () {
      // 调整切换逻辑
      let index = this.sharedWeeks.indexOf(this.currentWeekPlan)
      index += 1
      if (index < this.sharedWeeks.length) {
        // 将切换上一个周计划按钮打开
        this.hasLastWeekPlan = true
        this.currentWeekPlan = this.sharedWeeks[index]
        this.planId = this.currentWeekPlan.id
        // 获取周计划
        this.getPlan(true)
        if (index == this.sharedWeeks.length - 1) {
          this.hasNextWeekPlan = false
        } else {
          this.hasNextWeekPlan = true
        }
      }
    },
    // 复制周计划弹窗
    showReplicateModel () {
      // 如果该周计划是管理范围内的，直接复制
      let groupIds = []
      this.centers.forEach(c => {
        c.groups.forEach(g => {
          groupIds.push(g.id)
        })
      })
      if (groupIds.indexOf(this.currentWeekPlan.groupId) > 0) {
        this.replicateWeekPlan(false)
      } else {
        this.replicateModelVisible = true
      }
    },
    // 复制周计划
    replicateWeekPlan (flag) {
      // 如果没选班级，return
      if (flag && this.selectedGroupId == '') {
        this.showNotSelectGroupTip = true
        return
      }
      this.showNotSelectGroupTip = false
      this.replicateLoading = true
      LessonApi.replicatePlan({
        planId: this.planId,
        groupId: this.selectedGroupId === '' ? undefined : this.selectedGroupId
      }).then(response => {
        this.replicateLoading = false
        this.cancelReplicate()
        this.$router.push({
          name: 'edit-plan',
          params: {
            planId: response.id
          }
        })
      }).catch(error => {
        this.replicateLoading = false
      })
    },
    // 取消复制周计划
    cancelReplicate () {
      this.replicateModelVisible = false
      this.selectedGroupId = ''
      this.showNotSelectGroupTip = false
    },
    // 生成pdf
    generatePDF () {
      this.pdfLoading = true
      LessonApi.getPlanPDF({
        planId: this.planId,
        onlyShowCore: this.showCore,
        aiGenerated: this.isFromUnitDetail
      }).then(response => {
        this.getPlanPDF()
      }).catch(error => {})
    },
    // 获取周计划pdf
    getPlanPDF () {
      LessonApi.getPDFList({
        type: 'WEEKLY_PLAN'
      }).then(response => {
        if (response && response.length > 0) {
          let pdf = response[0]
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getPlanPDF()
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED') {
            // 成功弹窗下载
            this.$alert('<div class="display-flex align-items"><img style="height: 30px; margin: 5px" src="' + this.getfilePhoto(pdf.pdfName) + '"> <span class="text-ellipsis" title="' + pdf.pdfName + '">' + pdf.pdfName + '</span></div>', this.$t('loc.plan61'), {
              dangerouslyUseHTMLString: true,
              confirmButtonText: this.$t('loc.download'),
              customClass: 'el-message-box-font',
              callback: action => {
                if (action == 'confirm') {
                  if (tools.isComeFromIPad()) {
                    let requestData = {
                      'emailTemplate': 'weekly_lesson_planning',
                      'downloadFileUrl': pdf.pdfUrl,
                      'fileName': pdf.pdfName,
                      'week': this.planInfo.week,
                      'className': this.planInfo.groupName,
                      'siteName': this.planInfo.centerName,
                      'fromDate': this.planInfo.fromAtLocal,
                      'toDate': this.planInfo.toAtLocal,
                      'courseName': this.planInfo.theme
                    }
                    this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
                      .then(() => {
                        this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                          confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                          showCancelButton: false
                        })
                      }).catch(error => {
                      this.$message.error(error.message)
                    })
                  } else {
                    const eleLink = document.createElement('a')
                    eleLink.style.display = 'none'
                    eleLink.href = pdf.pdfUrl
                    // 触发点击
                    document.body.appendChild(eleLink)
                    eleLink.click()
                    // 移除
                    document.body.removeChild(eleLink)
                  }
                }
              }
            })
            this.pdfLoading = false
          }
        }
      })
    },

    getfilePhoto (fileName) {
      if (fileName.endsWith('doc')) {
        return doc
      }
      if (fileName.endsWith('docx')) {
        return docx
      }
      if (fileName.endsWith('pdf')) {
        return pdf
      }
      if (fileName.endsWith('ppt')) {
        return ppt
      }
      if (fileName.endsWith('pptx')) {
        return pptx
      }
      if (fileName.endsWith('xls')) {
        return xls
      }
      if (fileName.endsWith('xlsx')) {
        return xlsx
      }
      return file
    },

    batchSave () {
      // 判断执行过节流操作
      if (!this.debouncedSaveDraft) {
        this.debouncedSaveDraft = tools.debounce(() => {
          this.saveDraft(true)
        }, this.timeInterval)
      }
      // 执行保存
      this.debouncedSaveDraft()
    },

    async saveDraft (silent) {
      this.saveDraftLoading = true
      this.planData.silent = silent
      let centerItems = []
      this.planCenters.forEach(c => {
        c.items.forEach(i => {
          centerItems.push(i)
        })
      })
      this.planData.categories.forEach(c => {
        if (c.type == 'BOTTOM_CENTER_ROW') {
          c.items = centerItems
        }
      })
      // 如果是 Unit 详情中保存
      if (this.isFromUnitDetail) {
        return LessonApi.updateUnitItem(this.planData).then(response => {
          this.saveDraftLoading = false
          if (!silent) {
            this.goBack()
          }
        }).catch(error => {
          this.saveDraftLoading = false
          this.$message.error('Save weekly planner failed!')
        }).finally(() => {
          this.debouncedSaveDraft = null
        })
      } else {
        return LessonApi.updatePlan(this.planData).then(response => {
          this.saveDraftLoading = false
          if (!silent) {
            this.goBack()
          }
        }).catch(error => {
          this.saveDraftLoading = false
          this.$message.error('Save weekly planner failed!')
        }).finally(() => {
          this.debouncedSaveDraft = null
        })
      }
    },

    hasItem () {
      if (this.categories.length === 0) {
        return false
      }
      let existItem = false
      let existCenter = false
      this.categories.forEach(c => {
        let bottomItem = c && c.type.indexOf('BOTTOM_') != -1
        let items = c.items
        if (bottomItem && items) {
          items.forEach(i => {
            if (i && (i.name && i.name.trim().length > 0 || i.measureIds && i.measureIds.length > 0 || i.childIds && i.childIds.length > 0)) {
              existItem = true
            }
          })
        }
        if (this.planCenters) {
          if (this.planCenters.length > 0) {
            this.planCenters.forEach(x => {
              if (x && (x.center.name && x.center.name.trim().length > 0)) {
                existCenter = true
                if (x.items) {
                  x.items.forEach(i => {
                    if (i && (i.name && i.name.trim().length > 0 || i.measureIds && i.measureIds.length > 0 || i.childIds && i.childIds.length > 0)) {
                      existItem = true
                    }
                  })
                }
              }
            })
          } else {
            existCenter = true
          }
        } else {
          existCenter = true
        }
      })
      return existItem && existCenter
    },

    addCategory (lastCategory, type, callback) {
      let categoryIndex = this.getCategoryIndex(lastCategory)
      let newCategory
      // 判断需要创建的category是否为Centers，如果是，则确定name为Centers，否则name由用户自行输入
      if (type === 'BOTTOM_CENTER_ROW') {
        newCategory = {
          planId: this.planId,
          type: type,
          name: 'Centers',
          sortNum: categoryIndex + 1,
          items: [],
          lastId: lastCategory ? lastCategory.id : undefined
        }
      } else {
        newCategory = {
          planId: this.planId,
          type: type,
          sortNum: categoryIndex + 1,
          items: [],
          lastId: lastCategory ? lastCategory.id : undefined
        }
      }
      // 调用接口创建分类
      LessonApi.createCategory(newCategory).then(response => {
        // 分类信息加入日历中
        newCategory.id = response.id
        this.categories.splice(lastCategory.index + 1, 0, newCategory)
        this.updateCategoryIndex()
        callback()
      }).catch(error => {
        callback()
      })
    },

    getCategoryIndex (lastCategory) {
      let type = lastCategory.type
      if (!type) {
        return 0
      }
      if (type === 'THEME_ROW' || type === 'REFLECTION_ROW') {
        return 0
      }
      if (type === 'WEEK_ROW') {
        // 计算上半部分分类数量
        let topCount = 0
        this.categories.forEach(category => {
          let categoryType = category.type
          if (categoryType && categoryType.indexOf('TOP_') != -1) {
            topCount++
          }
        })
        return topCount
      }
      return lastCategory.index + 1
    },

    deleteCategory (deleteCategory) {
      LessonApi.deleteCategory({}, {
        planId: this.planId,
        categoryId: deleteCategory.id
      }).then(response => {
        this.categories.splice(deleteCategory.index, 1)
        if (deleteCategory.type === 'BOTTOM_CENTER_ROW') {
          this.planCenters = []
        }
        this.updateCategoryIndex()
      }).catch(error => {})
    },

    async callSingleEditLesson (itemId) {
      await this.saveDraft(true)
      this.$refs.lessonEditor.initEdit = true
      this.$refs.lessonEditor.singleEditLesson = true
      this.$refs.lessonEditor.currentItemId = itemId
    },
    // 改编单元 Unit 回调
    callAdaptUnitPlan (item) {
      this.$emit('callAdaptUnitPlan', item)
    },
    // 更新 Unit 课程回调函数
    callEditUnitLesson (item) {
      this.$emit('callEditUnitLesson', item)
    },
    updateCategory (updateCategory) {
      // 如果是主题行，更新自定义主题名称
      if (updateCategory.type === 'THEME_ROW') {
        this.customThemeRowName = updateCategory.name
        let params = {
          id: this.planId,
          theme: this.theme,
          adminTemplate: true,
          teacherIds: this.planData.teacherIds,
          customThemeRowName: this.customThemeRowName && this.customThemeRowName.trim()
        }
        LessonApi.updatePlanBaseInfo(params).then(response => {
        }).catch(error => {})
        return
      }
      updateCategory.planId = this.planId
      LessonApi.updateCategory(updateCategory).then(response => {
      }).catch(error => {})
    },

    updateTheme (theme) {
        let params = {
          id: this.planId,
          theme: theme,
          adminTemplate: true,
          teacherIds: this.planData.teacherIds,
          customThemeRowName: this.customThemeRowName
        }
        LessonApi.updatePlanBaseInfo(params).then(response => {
        }).catch(error => {})
    },

    selectLesson (lesson) {
      this.selectLessonFunc(lesson)
    },

    openLessonModal (selectLessonFunc) {
      this.selectLessonFunc = selectLessonFunc
      this.$refs.selectLessonModal.open()
    },

    // 打开快速添加课程弹窗
    openAddLessonModal (selectLessonFunc, name) {
      // 选择课程回调
      this.selectLessonFunc = selectLessonFunc
      // 将课程名称带过去
      this.$refs.addLessonModal.openQuickAddLesson(name)
    },

    // item 发生变化
    changeItem () {
      // 生成过材料后，在进行修改，是否需要重新生成材料
      if (this.hasGenerated) {
        this.needGenerateResource = true
      }
    },

    /**
     * 查看反思
     */
    viewReflection (lessonId) {
      this.$refs.lessonReflectionList.selectLessonById(lessonId)
    },

    addCenter (params) {
      let newAddCenter = {
        center: {
          name: params.name,
          colorIndex: params.colorIndex,
          planId: params.planId
        },
        items: []
      }
      LessonApi.addPlanCenter(newAddCenter.center)
        .then(res => {
          newAddCenter.center.id = res.id
          this.planCenters.push(newAddCenter)
        }).catch(error => {})
    },
    deleteCenter (id, hasItem) {
      if (hasItem) {
        this.$confirm(this.$t('loc.curriculum99'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
          LessonApi.deletePlanCenter(id)
            .then(res => {
              this.planCenters = this.planCenters.filter(x => x.center.id != id)
              this.$message.success(this.$t('loc.planDeleteSuccess'))
            })
            .catch(error => {})
        }).catch(() => {
        })
      } else {
        LessonApi.deletePlanCenter(id)
          .then(res => {
            this.planCenters = this.planCenters.filter(x => x.center.id != id)
            this.$message.success(this.$t('loc.planDeleteSuccess'))
          })
          .catch(error => {})
      }
    },
    updateCenter (params) {
      LessonApi.updatePlanCenter(params)
        .then(res => {})
        .catch(error => {})
    },
    // 检查周计划分组是否有空的
    checkCompleted () {
      // 判断centers中是否有分组名为空的
      if (this.planCenters.length > 0) {
        let hasEmptyName = false
        this.planCenters.forEach(center => {
          if (!center.center.name) {
            hasEmptyName = true
          }
        })
        if (hasEmptyName) {
          this.$message.error(this.$t('loc.curriculum79'))
          return false
        }
      }
      // 判断是否有项目分组空的
      if (this.categories.length > 0) {
        let hasEmptyName = false
        this.categories.forEach(category => {
          if (!category.name) {
            hasEmptyName = true
          }
        })
        if (hasEmptyName) {
          this.$message.error(this.$t('loc.curriculum80'))
          return false
        }
      }
      return true
    },
    // 生成材料
    generateMaterialsList () {
      this.$store.commit('SET_GENERATERESOURCE', false)
      if (!this.checkCompleted()) {
        return
      }
      this.generateMaterialLoading = true
      let requestParams = {
        'curriculumId': this.curriculumId,
        'unitId': this.unitId,
        'planId': this.planId
      }
      this.$axios
        .post($api.urls().generatePlanMaterialsAndResource, {}, { params: requestParams })
        .then(res => {
          this.hasGenerated = true
          this.generateMaterialLoading = false
          this.needGenerateResource = false
          this.$emit('generate',JSON.stringify(res))
          // 点击生成材料按钮埋点
          if (this.$route.params.add) {
            this.$analytics.sendEvent('web_curriculum_add_generate')
          } else {
            this.$analytics.sendEvent('web_curriculum_edit_generate')
          }
        })
        .catch(error => {
          this.generateMaterialLoading = false
          this.needGenerateResource = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 删除 item 回调
    deleteItem (id, categoryId, centerId) {
      LessonApi.deleteItem(id,this.planId)
        .then(res => {
          this.categories.filter(category => {
            if (category.id === categoryId) {
              category.items = category.items.filter(item => item.id !== id)
            }
          })
          if (centerId) {
            this.planCenters.filter(center => {
              if (center.center.id === centerId) {
                center.items = center.items.filter(item => item.id !== id)
              }
            })
          }
        })
        .catch(error => {})
    }
  },
  destroyed () {
    if (this.timeoutID) {
      clearTimeout(this.timeoutID)
    }
  },

  watch: {
    planId (val) {
      if (this.isFromUnitDetail) {
        val && !this.isShared && this.getUnitPlan(this.contentLanguage)
      } else {
        val && !this.isShared && this.getPlan(null, this.contentLanguage)
      }
    },
    // 监听当前内容语言类型变更
    contentLanguage (val) {
      // 编辑状态下不翻译
      if (this.edit) {
        return
      }
      if (!this.calendarLoading) {
        if (!this.isFromUnitDetail && this.planId) {
          // 获取周计划详情，判断是否与内容源语言不同来决定是否传递翻译参数
          this.getPlan(null, val)
        }
        // 如果是进入 unit 详情查看周计划
        if (this.isFromUnitDetail && this.planId && !this.unitOverviewPreviewDrag) {
          // 获取周计划详情，判断是否与内容源语言不同来决定是否传递翻译参数
          this.getUnitPlan(val)
        }
      }
    },
    // 监听是否正在引导，如果没有在引导，取消事件禁用,以及将第五步添加的class去除
    isGuiding (newVal, oldVal) {
      if (!newVal && oldVal && this.driver) {
        this.driver.steps.forEach(el => { el.node.style.pointerEvents = 'auto' })
        var editDom = document.getElementById('guide-weekly-plan-editTip')
        editDom.children[0].classList.remove('row-border')
      }
    },
    // 如果框架选择发生了变化，重新加载测评点
    frameworkId (val) {
      this.loadMeasures(val)
    },
    needGenerateResource (val) {
      this.$store.dispatch('setGenerateResource', val)
    },
    // 监听是否生成了材料
    generatedMaterials: {
      immediate: true,
      handler (val) {
        this.hasGenerated = val
      }
    },
    showCore (val) {
      this.$bus.$emit('changeShowCore', { planId: this.planId, open: val })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .plan-drag-ghost {
  height: 250px !important;
  background-color: var(--color-page-background-white); /* 设置拖拽时的背景颜色 */
  /* 隐藏所有的子元素 */
  > * {
    display: none;
  }
}

@media only screen and (min-width:1200px){
  //web
  .group-area {
    padding: 10px;
    // min-width: 1120px; 小屏幕适配
  }

  .activity-item {
    background-color:#e7f7f8;
    color:#40c2c5
  }

  /deep/ .el-dropdown-menu__item {
    padding:5px 10px;
  }

  .text-hidden {
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
  }

  .flex-row-between {
    // display:flex;
    align-items: center;
    justify-content: space-between;
  }

  .shared-plan-header{
    border-bottom: none !important;
    border-left: solid 1px #DADADA;
  }

  .week-date-color {
    color: #606266;
  }

  .display-week {
    min-height: 20px;
  }

  .display-week-container {
    position: absolute;
    top: 5px;
    left: calc(50% - 100px);
    i {
      font-size: 24px;
    }
  }

  .display-week {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .plan-calendar {
    min-height: 0;
  }

  .calendar-area {
    color: #323338;
    flex: auto;
    position: relative;;
    display: flex;
    flex-direction: column;
    overflow-x: auto;
  }

  .calendar-bottom {
    padding: 0 0px 20px 0;
  }

  // 周一至周五颜色
  @week-1-color: #DD3E78;
  @week-2-color: #F4C18F;
  @week-3-color: #97B02E;
  @week-4-color: #7FC2A7;
  @week-5-color: #795198;
  // 周计划表格
  .plan-table {
    width: 100%;
    // overflow-x: auto;
  }
  .plan-row {
    display: flex;
    position: relative;
  }
  .core-measure {
    display: flex;
    align-items: center;
  }
  .plan-col, .plan-classified-col {
    background-color: #fff;
    padding: 10px;
    border-right: solid 1px #DADADA;
    border-bottom: solid 1px #DADADA;
    border-top: solid 1px #DADADA;
    overflow-wrap: break-word;
  }
  .plan-col {
    min-width: 720px;
    flex: 1 0 auto;
  }
  .operation-col {
    width: 20px;
    flex: auto;
    // position: sticky;
    left: 0;
    background-color: #f0f3f4;
    // z-index: 1;
    // overflow: visible;
  }
  .plan-classified-col {
    width: 120px;
    flex: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    // position: sticky;
    left: 20px;
    background-color: #fff;
    // z-index: 1;
    border-left: solid 1px #DADADA;
  }
  .week-col {
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .week-col.week-1 {
    background: @week-1-color;
  }
  .week-col.week-2 {
    background: @week-2-color;
  }
  .week-col.week-3 {
    background: @week-3-color;
  }
  .week-col.week-4 {
    background: @week-4-color;
  }
  .week-col.week-5 {
    background: @week-5-color;
  }
  .merged-col {
    min-height: 40px;
  }
  .activity-col {
    min-height: 100px;
    padding: 6px;
  }

  .activity-input /deep/ textarea {
    padding: 0;
    border: none;
    overflow: hidden;
    resize : none;
  }

  .footer-area {
    text-align: right;
  }

  .category-operation {
    z-index: 20;
    // background-color: #f0f3f4;
    position: absolute;
    left: 0px;
    bottom: -20px;
    // height: 100%;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .pre-area {
    white-space: pre-line;
    margin-top: -20px;
  }

  /deep/ .el-dialog__body {
    font-size: 16px;
  }
  .toolbar-area {
    position: relative;
    z-index: 3;
  }
  .collapse-btn {
    z-index: 4;
    position: absolute;
    top: -12px;
    right: -10px;
  }
  /deep/ .plan-reflection-card {
    .el-card__body {
      padding: 10px;
    }
  }
  /deep/ .operation-col {
    background-color: #FDF6ED;
  }
}
</style>