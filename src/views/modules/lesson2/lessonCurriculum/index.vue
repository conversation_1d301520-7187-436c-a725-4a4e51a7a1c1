<template>
  <div class="h-full lg-padding-top-4">
    <!-- 顶部操作栏 -->
    <curriculum-top-bar
      ref="curriculumTopBar"
      :isAdmin="isAdmin"
      :contentLangCode="currentContentLangCode"
      :currentCurriculum="currentCurriculum"
      @setNewCurriculumLangCode="setNewCurriculumLangCode"
      @changeTypeCallBack="changeCurriculumType"
    ></curriculum-top-bar>
    <!-- 系列课程列表 -->
    <curriculum-list
      v-loading="loading"
      :loading="loading"
      ref="curriculumList"
      :isAdmin="isAdmin"
      :currentContentLangCode="currentContentLangCode"
      :curriculums="curriculums"
      :type="type"
      @updateCurriculum="updateCurriculum"
      @setCurrentCurriculum="setCurrentCurriculum"
      @setCurriculumLangCode="setCurriculumLangCode"
      class="add-margin-t-16"
      style="height: calc(100% - 60px)"
    ></curriculum-list>
  </div>
</template>

<script>
import { acrossRole } from '@/utils/common'
import Lessons2 from '@/api/lessons2'
import CurriculumTopBar from './components/CurriculumTopBar.vue'
import CurriculumList from './components/CurriculumList.vue'

export default {
  name: 'curriculum',
  components: {
    CurriculumTopBar,
    CurriculumList
  },
  computed: {
    isAdmin () {
      return acrossRole('agency_admin', 'agency_owner')
    }
  },
  data () {
    return {
      type: 'ALL', // 单元课程列表 Tab 类型
      curriculums: [], // 单元课程列表
      loading: false, // 加载中
      curriculumId: '', // 单元课程 ID
      currentCurriculum: {}, // 当前单元课程
      currentContentLangCode: 'en-US' // 当前内容的语言码
    }
  },
  created () {
    let type = this.$route.params.type
    let curriculumId = this.$route.params.id
    if (type && curriculumId) {
      this.curriculumId = curriculumId
      this.changeCurriculumType(type)
    } else {
      this.curriculumId = ''
      this.getAllCurriculums()
    }
    this.$analytics.sendEvent('web_curriculum_exposure')
  },
  methods: {
    // 设置 curriculum 内容的初始语言类型
    setCurriculumLangCode (curriculumLangCode) {
      this.currentContentLangCode = curriculumLangCode
    },
    // 设置新内容的当前语言类型
    setNewCurriculumLangCode (newCurriculumLangCode) {
      this.currentContentLangCode = newCurriculumLangCode
    },
    // 切换系列课程类型
    changeCurriculumType (e) {
      this.type = e
      this.$nextTick(() => {
        this.$refs['curriculumTopBar'].type = e
      })
      if (this.type == 'ALL') {
        this.$analytics.sendEvent('web_curriculum_curriculum_list')
        this.getAllCurriculums()
      } else if (this.type == 'DRAFT') {
        this.$analytics.sendEvent('web_curriculum_draft_list')
        this.getDraftCurriculums()
      } else {
        this.getFavoriteCurriculums()
      }
    },
    // 获取机构所有的系列课程
    getAllCurriculums () {
      let params = {
        pageSize: 10000,
        pageNum: 1
      }
      this.loading = true
      Lessons2.getAgencyCurriculums(params)
      .then(res => {
        this.curriculums = res.items
        if (this.curriculumId && this.curriculums.find(x => x.id == this.curriculumId)) {
          this.$nextTick(() => {
            this.$refs['curriculumList'].selectCurriculum(this.curriculumId)
            this.curriculumId = ''
          })
        } else {
          this.$refs['curriculumList'].setSelectCurriculum()
        }
        this.loading = false
      })
      .catch(err => {
        this.loading = false
      })
    },
    // 获取收藏列表
    getFavoriteCurriculums () {
      this.curriculums = []
    },
    // 获取草稿列表
    getDraftCurriculums () {
      let params = {
        pageSize: 10000,
        pageNum: 1
      }
      this.loading = true
      Lessons2.getDraftCurriculums(params)
      .then(res => {
        this.curriculums = res.items
        if (this.curriculumId) {
          this.$nextTick(() => {
            this.$refs['curriculumList'].selectCurriculum(this.curriculumId)
            this.curriculumId = ''
          })
        } else {
          this.$refs['curriculumList'].setSelectCurriculum()
        }
        this.loading = false
      })
      .catch(err => {
        this.loading = false
      })
    },
    // 更新系列课程列表
    updateCurriculum () {
      this.changeCurriculumType(this.type)
    },
    // 设置当前单元课程
    setCurrentCurriculum (curriculum) {
      this.currentCurriculum = curriculum
    }
  }
}
</script>

<style>

</style>
