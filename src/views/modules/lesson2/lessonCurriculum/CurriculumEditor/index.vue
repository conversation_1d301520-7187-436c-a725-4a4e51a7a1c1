<template>
  <div v-loading="loading">
    <el-row v-if="false" class="header">
      <div>
        <!-- <router-link to> -->
          <img style="height: 33px;padding-top: 15px; cursor: pointer;" src="@/assets/img/us/back_home.png" @click="back()">
        <!-- </router-link> -->
        <span class="m-l-md" v-if="leftTitle">{{ leftTitle }}</span>
      </div>
      <span class="header-title">{{ title }}</span>
    </el-row>
    <el-row class="curriculum-editor-content lg-box-shadow lg-border-radius-8">
      <el-form ref="curriculum" id="curriculum" :model="curriculum" label-position="top" :rules="rules">
        <!--封面-->
        <el-form-item prop="cover" class="curriculum-form-cover">
          <media-uploader :showUrlBtn="true" typeDistinguish="coverMedia" v-model="curriculum.cover"  class="media-uploader-lesson-cover" :tips="$t('loc.lesson2NewLessonFormPlaceHolderCover')">
            <div slot="tips">
              <ul style="text-align: left;padding: 0 32px;">
                <li style="list-style:disc;">{{ $t('loc.coverExternalMediaUploadSize') }}</li>
                <li style="list-style:disc;">{{ $t('loc.curriculum105') }}</li>
                <li style="list-style:disc;">{{ $t('loc.curriculum106') }}</li>
              </ul>
            </div>
          </media-uploader>
        </el-form-item>
        <div style="align-items: end;padding-right: 10px;" class="display-flex flex-direction-col">
          <div>
            <!--课程名称-->
            <el-form-item :label="$t('loc.curriculum32')" prop="name" class="curriculum-form-name">
              <el-input ref="curriculumNameInputRef" v-model="curriculum.name" :placeholder="$t('loc.curriculum81')"
                        auto-complete="off" type="text" maxlength="200" />
            </el-form-item>
          </div>
          <div>
            <!--年龄组-->
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelAgeGroup')" prop="ages" class="curriculum-form-age">
              <el-select v-model="ageValues" class="input-select"
                         multiple
                         :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                         @change="changeAgeValues">
                <el-option v-for="item in ageGroup" :key="item.value" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>
          </div>
          <div>
            <!--框架-->
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelFramework')" prop="framework"
                          class="curriculum-form-framework ">
              <el-select v-model="frameworkId" class="input-select"
                        @change="changeFramework"
                        :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')">
                <el-option v-for="item in frameworks" :key="item.frameworkId" :label="item.frameworkName"
                          :value="item.frameworkId"/>
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelDomain')" prop="domains" class="curriculum-form-domain"
                      v-if="curriculum.framework">
              <el-select v-model="domainValues" class="input-select"
                multiple
                @change="changeDomains"
                        :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')">
                <el-option v-for="item in domains" :key="item.abbreviation" :label="item.abbreviation"
                          :value="item.id"/>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <!-- 描述 -->
        <el-form-item :label="$t('loc.description')" prop="description" class="curriculum-form-desc">
          <el-input type="textarea"
                     v-model="curriculum.description" :placeholder="$t('loc.curriculum34')"
                    auto-complete="off" :autosize="{ minRows: 3}" maxlength="5000"/>
        </el-form-item>
        <!-- 视频介绍 -->
        <el-form-item :label="$t('loc.curriculum10')" class="curriculum-form-introduction">
          <span slot="label">
            <span>{{ $t('loc.curriculum10') }}</span>
            <span class="m-l-xs font-weight-normal">{{ $t('loc.curriculum35') }}</span>
          </span>
          <introduction-videos @changeIntroductionVideos="changeIntroductionVideos" v-model="curriculum.introductionVideos"></introduction-videos>
        </el-form-item>
      </el-form>
    </el-row>
    <div>
      <unit-edit ref="unitEdit" v-model="units" :domains="domains"
                 :frameworkId="frameworkId" :frameworks="frameworks" :framework-name="frameworkName"
                 :isEditor="isEditor" id="unitEdit" v-if="units && units.length > 0" :curriculumId="curriculum.id" :units="units"
                 :framework-link-url="frameworkLinkUrl"></unit-edit>
      <!-- 底部保存草稿和发布按钮 -->
      <el-row class="curriculum-editor-button-group">
        <div class="display-flex justify-content" style="border-radius: 0 0 8px 8px; background: #fff; padding-bottom: 48px; box-shadow: 0px 5px 8px 0px rgba(0, 0, 0, 0.101025);">
          <el-button @click="saveDraft" plain :loading="saving">
            {{ $t('loc.plan_saveDraft') }}
          </el-button>
          <el-button v-if="false" type="primary" @click="handleClickPreview">{{ $t('loc.preview') }}</el-button>
          <el-button type="primary" @click="publishCurriculum" :loading="saving">
            {{ $t('loc.lessons2Save') }}
          </el-button>
        </div>
      </el-row>
    </div>
  </div>
</template>

<script>
import tools from '@/utils/tools'
import { mapState } from 'vuex'
import Lessons2 from '@/api/lessons2'
import MediaUploader from '../components/MediaUploader/index.vue'
import IntroductionVideos from '../components/MediaUploader/IntroductionVideos.vue'
import UnitEdit from '../CurriculumUnitDetail/components/UnitEdit.vue'
export default {
  name: 'CurriculumEditor',
  components: { MediaUploader, IntroductionVideos , UnitEdit },
  data () {
    let checkCover = (rule,value,callback) => {
      // 若没有图片、视频和外部链接，则进行提示
      if (value === null && this.curriculum.cover === null) {
        return callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    return {
      loading: false, // 加载中
      type: '', // 跳转类型
      sourceCurriculumId: '', // 源课程 ID
      title: '', // 编辑页标题
      leftTitle: '', // 顶部左侧标题
      ageGroup: [], // 年龄组
      ageValues: [], // 年龄组值
      frameworks: [], // 框架
      frameworkId: null, // 框架 ID
      domains: [], // 领域
      domainValues: [], // 领域值
      domainValuesTemp: [], // 领域值临时变量
      curriculum: { // 课程信息
        id: null, // 课程 ID
        cover: null, // 课程封面，对象，id、name、url
        name: null, // 课程名称
        ages: [], // 年龄组，对象数组，name、value
        framework: null, // 框架，对象，frameworkId、frameworkName
        domains: [], // 领域和测评点，对象数组，id、name、abbreviation、children
        description: null,// 系列课程描述
        introductionVideos: [], // 系列课程引导视频
        authorId: null // 作者 ID
      },
      units: [], // 单元
      rules: { // 表单验证规则
        cover: [{
          validator: checkCover,type: 'object'
        }],
        name: [{ message: this.$t('loc.fieldReq'), required: true, whitespace: true }],
        ages: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        framework: [{ type: 'object', message: this.$t('loc.fieldReq'), required: true }],
        domains: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        description: [{ message: this.$t('loc.fieldReq'), required: true, whitespace: true }]
      },
      isEditor: true, // 当前是否为编辑页
      saving: false // 保存状态，0：未保存，1：保存中，2：保存草稿中
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      needGenerateResource: state => state.lesson.needGenerateResource
    }),
    // 框架链接
    frameworkLinkUrl () {
      return (this.curriculum.framework && this.curriculum.framework.linkUrl) || (this.curriculum.framework && this.curriculum.framework.frameworkUrl) || null
    },
    // 框架名称
    frameworkName () {
      return (this.curriculum.framework && this.curriculum.framework.name) || (this.curriculum.framework && this.curriculum.framework.frameworkName) || null
    }
  },
  watch: {
    // 监听框架值变化，获取对应的领域
    frameworkId (newVal) {
      this.loadMeasures(newVal)
    }
  },
  mounted () {
    // 默认计划中心为 PS
    this.$store.commit('SET_PLANCENTERTYPE', 'PS')
    this.type = this.$route.params.type
    this.sourceCurriculumId = this.$route.params.sourceCurriculumId
    if (this.$route.params.add) {
      this.title = 'Add New Curriculum'
      this.leftTitle = this.$t('loc.curriculum30')
      this.$analytics.sendEvent('web_curriculum_add_exposure')
    } else {
      this.$analytics.sendEvent('web_curriculum_edit_exposure')
    }
    if (this.$route.params.title) {
      this.title = this.$route.params.title
    }
    if (this.$route.params.curriculumId) {
      this.getDetail(this.$route.params.curriculumId)
        .then(() => {
          this.setPlanCenterType()
        })
    }
  },
  created () {
    this.loadAgeGroup()
    this.loadFrameworks()
  },
  provide () {
    return {
      curriculum: this.curriculum,
      saveFun: this.saveFun
    }
  },
  methods: {
    back () {
      let id = this.curriculum.id
      if (this.sourceCurriculumId) {
        id = this.sourceCurriculumId
      }
      this.$router.push({
        name: 'lessonCurriculum',
        params: {
          type: this.type,
          id: id
        }
      })
    },
    // 设置 Plan Center 类型
    setPlanCenterType (ageValues) {
      let ages = ageValues || this.ageValues
      // 处理年龄数据，避免空指针
      ages = ages || []
      let type = 'PS' // 默认为 PS
      // 按照年龄大小判断 Plan Center 类型，从低到高适配
      if (ages.includes('0') || ages.includes('1') || ages.includes('2') || ages.includes('1,2')) {
        type = 'IT'
      } else if (ages.includes('3') || ages.includes('4')) {
        type = 'PS'
      } else if (ages.includes('5') || ages.includes('Grade 1') || ages.includes('Grade 2')) {
        type = 'K'
      } else {
        type = 'PS'
      }
      this.$store.commit('SET_PLANCENTERTYPE', type)
    },
    // 获取系列课程详情
    getDetail(id) {
      return new Promise((resolve, reject) => {
        this.loading = true
        let params = { id: id }
        Lessons2.getCurriculumDetail(params)
          .then(res => {
            this.curriculum.id = res.id
            this.curriculum.cover = res.coverMedias.length > 0 ? res.coverMedias[0] : null
            this.curriculum.name = res.name
            this.curriculum.ages = res.ages ? JSON.parse(res.ages) : []
            this.ageValues = res.ageValues
            this.curriculum.framework = res.framework
            this.curriculum.domains = res.domains
            this.domainValuesTemp = res.domains.map(x => x.id)
            this.frameworkId = res.framework ? res.framework.id : null
            this.curriculum.description = res.description
            this.curriculum.introductionVideos = res.introductionMedias
            this.curriculum.authorId = res.authorId
            this.units = res.units
            this.loading = false
            // 监听系列课程对象，自动保存
            this.$watch('curriculum', {
              deep: true,
              handler(val) {
                this.saving = true
                this.autoSave()
              }
            })
            resolve()
          })
          .catch(error => {
            this.loading = false
            reject(error)
          })
      })
    },
    // 加载框架信息
    loadFrameworks () {
      Lessons2.getFrameworks().then(res => {
        this.frameworks = res.frameworks
      })
    },
    // 加载框架的领域
    loadMeasures (frameworkId) {
      Lessons2.getMeasures(frameworkId)
      .then(res => {
        this.domains = res.measures
        this.domainValues = this.domainValuesTemp
      })
    },
    // 加载年龄组
    loadAgeGroup () {
      Lessons2.getAgeGroups(this.currentUser.default_agency_state)
      .then(res => {
        this.ageGroup = res.ageGroups
      })
    },
    // 修改年龄组
    changeAgeValues(values) {
      values = values || []
      let ageGroup = this.ageGroup || [] // 当前用户的全部年龄组
      let ages = ageGroup.filter(item => values.includes(item.value))
      this.curriculum.ages = ages // 更新后的用户的全部年龄组
      // ages 为数组，其中每个元素是一个对象，对象中包含 name 和 value 两个属性 若 value 为 0 1 2 3 1,2 时，显示 IT 的 center
      if (ages && ages.length > 0) {
        ages = ages.map(item => item.value)
      }
      // 设置 Plan Center 类型
      this.setPlanCenterType(ages)
    },
    // 修改框架
    changeFramework (value) {
      let framework = this.frameworks.find(x => x.frameworkId == value)
      this.curriculum.framework = framework
      this.domainValues = []
      this.domainValuesTemp = []
      this.curriculum.domains = []
    },
    // 修改领域
    changeDomains (values) {
      this.curriculum.domains = this.domains.filter(item => values.includes(item.id))
    },
    // 修改引导视频
    changeIntroductionVideos (values) {
      this.curriculum.introductionVideos = values
    },
    // 修改后2s，自动保存
    autoSave: tools.debounce(function () {
      let params = this.getCurriculumBaseInfo()
      Lessons2.updateCurriculum(params)
      .then(res => {
        this.saving = false
      })
      .catch(error => {
        this.saving = false
        this.$message.error(error.message)
      })
    }, 2000),
    // 获取系列课程基本信息
    getCurriculumBaseInfo () {
      // id
      let curriculumId = this.curriculum.id
      // name
      let curriculumName = this.curriculum.name
      // cover
      let cover = {}
      let covers = []
      if (this.curriculum.cover) {
        if (this.curriculum.cover.externalMediaUrlId) {
          cover.type = 'EXTERNAL'
          cover.id = this.curriculum.cover.externalMediaUrlId
        } else if (this.curriculum.cover.type === 'externalMedia') {
          cover.type = 'EXTERNAL'
          cover.id = this.curriculum.cover.id
        } else {
          cover.type = 'UPLOAD'
          cover.id = this.curriculum.cover.id
        }
        covers.push(cover)
      }
      // introductionVideos
      let introductionVideos = []
      if (this.curriculum.introductionVideos.length > 0) {
        this.curriculum.introductionVideos.forEach(x => {
          let introductionVideo = {}
          if (x.externalMediaUrlId) {
            introductionVideo.type = 'EXTERNAL'
            introductionVideo.id = x.externalMediaUrlId
          } else if (x.type === 'externalMedia') {
            introductionVideo.type = 'EXTERNAL'
            introductionVideo.id = x.id
          } else {
            introductionVideo.type = 'UPLOAD'
            introductionVideo.id = x.id
          }
          introductionVideos.push(introductionVideo)
        })
      }
      // framework
      let frameworkId = this.curriculum.framework ? this.curriculum.framework.frameworkId : null
      // domain
      let domainIds = this.domainValues ? this.domainValues : []
      // description
      let description = this.curriculum.description ? this.curriculum.description : ''
      // ages
      let ages = this.ageValues
      let ageGroupNames = this.curriculum.ages ? JSON.stringify(this.curriculum.ages) : ''
      let params = {
        id: curriculumId,
        coverMedias: covers,
        name: curriculumName,
        ages: ages,
        ageGroupNames: ageGroupNames,
        frameworkId: frameworkId,
        domainIds: domainIds,
        description: description,
        introductionVideos: introductionVideos
      }
      return params
    },
    handleClickPreview () {
    },
    // 保存草稿
    saveDraft () {
      // 点击保存课程为草稿埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_save_draft')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_save_draft')
      }
      // 系列课程名字不能为空
      if (!this.curriculum.name || !this.curriculum.name.trim()) {
        this.$message.error(this.$t('loc.curriculum88'))
        return
      }
      this.$message.success(this.$t('loc.lesson2LessonSave'))
      // 不会退出
      // this.$router.push({
      //   name: 'lessonCurriculum'
      // })
    },
    async publishCurriculum () {
      // 点击发布课程埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_publish')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_publish')
      }
      // 系列课程表单校验
      try {
        await this.$refs['curriculum'].validate()
      } catch (err) {
        setTimeout(() => {
          this.$message.error(this.$t('loc.curriculum78'))
          let formItemsWithError = this.$el.querySelectorAll('.is-error')
          let firstItem = formItemsWithError[0]
          if (firstItem) {
            firstItem.dispatchEvent(new CustomEvent('validate-failed', {}))
            firstItem.scrollIntoView(true)
          }
        }, 0)
        return
      }
      // 需要重新生成资源
      if (this.needGenerateResource) {
        this.$message.error(this.$t('loc.curriculum75'))
        return
      }
      // 发布课程
      let params = {
        id: this.curriculum.id
      }
      await Lessons2.publishCurriculum(params)
      .then(res => {
      // 发布成功
      if (res.success) {
          this.$router.push({
            name: 'lessonCurriculum',
            params: {
              id: this.curriculum.id,
              type: 'ALL'
            }
          })
        } else if (res.materialMissing) {
          // 缺失周计划材料，提示用户是否忽略
          // 弹窗提示
          this.$confirm(this.$t('loc.curriculum70',{ num1: res.errorWeekNum, num2: res.errorUnitNum }), this.$t('loc.confirmation'), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$t('loc.curriculum71'),
            cancelButtonText: this.$t('loc.cancel')
          })
          .then(() => {
            // 忽略后直接发布
            params.ignoreMaterialMissing = true
            Lessons2.publishCurriculum(params)
            .then(() => {
              this.$router.push({
                name: 'lessonCurriculum',
                params: {
                  id: this.curriculum.id,
                  type: 'ALL'
                }
              })
            })
          })
        } else if (res.errorWeekId && res.errorUnitId) {
          // 发布失败,某个单元中某一周没有选择周计划
          this.$message.error(this.$t('loc.curriculum76',{ num1: res.errorWeekNum, num2: res.errorUnitNum }))
        } else if (res.errorUnitId) {
          // 发布失败，某个单元缺失必填项
          this.$refs.unitEdit.currentIndex = res.errorUnitNum
          document.getElementById('unitEdit').scrollIntoView()
          this.$message.error(this.$t('loc.curriculum77',{ num1: res.errorUnitNum }))
          this.$refs.unitEdit.toValidateUnit()
        } else {
          this.$message.error(this.$t('loc.curriculum78'))
          this.$refs.curriculum.validate()
          document.getElementById('curriculum').scrollIntoView()
        }
      })
    },
    // 保存按钮状态函数
    saveFun (saving) {
      this.saving = saving || false
    }
  }
}
</script>

<style lang="less" scoped>
  .header {
    position: sticky;
    top: 0;
    z-index: 100;
    text-align: center;
    height: 50px;
    padding: 0 35px;
    line-height: 50px;
    color: #111C1C;
    background-color: #e4eaec;

    :first-child {
      float: left;
    }

    :last-child {
      font-size: 18px;
      color: #111C1C;
    }
  }
  .input-select {
    width: 100%;
  }
  .curriculum-editor-content {
    margin: 20px auto  0 auto;
    background-color: #fff;
    width: 1200px;
    padding: 50px;
  }
  .curriculum-editor-button-group {
    margin: 0 auto;
    width: 1200px;
    padding-bottom: 24px;
  }

  .el-form-item {
    display: inline-block;

    &.curriculum-form-cover {
      width: 640px;
      height: 340px;
      float: left;
      margin-right: 20px;
    }

    &.curriculum-form-name, &.curriculum-form-age,&.curriculum-form-framework, &.curriculum-form-domain {
      width: 439px;
    }
    &.curriculum-form-desc, &.curriculum-form-introduction {
      width: 100%;
      margin-top: 20px;
    }
  }
/deep/ .media-uploader-lesson-cover {
  width: 620px;
  height: 355px;
}
/deep/ .media-uploader-lesson-cover .media-uploader-select {
  width: 620px;
  height: 348px;
  margin-top: 0;
}
/deep/ .media-uploader-lesson-cover .media-uploader {
  width: 620px;
  height: 348px;
}
/deep/ .media-uploader-lesson-cover .media-uploader-selected {
  width: 620px;
  height: 348px;
}

/deep/ .el-form-item__label {
  padding: 0;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #111c1c;
  line-height: 22px;
  marker-end: 80px;
  margin-bottom: 8px
}
/deep/.el-form-item {
  margin-bottom: 24px;
}
.header-title{
  //超出自动换行
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 30%;
  display: inline-block;
  vertical-align: middle;

}
</style>
