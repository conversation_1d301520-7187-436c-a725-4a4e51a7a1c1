<template>
  <div class="unitDetailContent lg-border-radius-8 lg-box-shadow lg-scrollbar-show h-full">
    <!-- 预览 PDF -->
    <div class="display-flex flex-row-between align-items">
      <div v-if="false">
        <el-button type="text" style="padding: 0">
          <div class="display-flex align-items">
            <icon-alibaba class="icon-alibaba-view-mini" />
            <span>{{ unit.viewCount || 0 }}</span>
          </div>
        </el-button>
      </div>
      <div>
        <el-button class="m-r-sm" icon="fa fa-file-pdf-o" v-if="false">PDF</el-button>
      </div>
    </div>
    <!-- 单元封面 -->
    <el-row class="unit-header m-t-sm">
      <!-- 单元封面 -->
      <el-col class="unit-header-left" :span="12">
        <curriculum-media-viewer :url="(unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].url : ''"
          :type="(unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].type : ''"/>
      </el-col>
      <el-col class="unit-header-right" :span="12">
        <div class="m-l-md curriculum-unit-box">
          <!-- 单元名称 -->
          <div class="curriculum-unit-name">
            <span>
              {{ unit.number >= 1 ? $t('loc.curriculum102', {num: unit.number}) : 'Special Unit ' + unit.number }}:
            </span>
            <span :title="unit.title">{{ unit.title }}</span>
          </div>
          <!-- 周 活动信息 -->
          <div class="currriculum-unit-info">
            <div class="curriculum-info-item">
              <div class="currriculum-unit-info-num">{{ unit.weeksNum || 0 }}</div>
              <div class="currriculum-unit-info-title">{{ $t('loc.curriculum7') }}</div>
            </div>
            <el-divider direction="vertical" class="curriculum-info-item-divider"></el-divider>
            <div class="curriculum-info-item">
              <div class="currriculum-unit-info-num">{{ unit.activitiesNum || 0 }}</div>
              <div class="currriculum-unit-info-title">{{ $t('loc.curriculum8') }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- tab  tab栏 及内容-->
    <over-view-tab :unit="unit" :curriculumId="curriculumId" :domains="domains" :isEditor="isEditor" :frameworkId="frameworkId"></over-view-tab>
  </div>
</template>

<script>
import CurriculumMediaViewer from '../../components/CurriculumMediaViewer.vue'
import IconAlibaba from '@/views/modules/lesson2/lessonLibrary/components/IconAlibaba'
import OverViewTab from './OverViewTab.vue'
export default {
  name: 'unitDetailContent',
  components: {
    IconAlibaba,
    CurriculumMediaViewer,
    OverViewTab
  },
  props: ['unit', 'domains', 'curriculumId', 'frameworkId'],
  data () {
    return {
      userLiked: false,
      userFavorite: false,
      desc: '',
      isEditor: false // 当前是否为编辑页
    }
  },
  created () {},
  watch: {
    unit: {
      deep: true,
      handler (val) {
        this.$nextTick(() => {
          if (!this.$route.params.weekNum) {
            document.querySelector('.unitDetailContent').scrollIntoView()
          } else {
            this.$route.params.weekNum = undefined
          }
        })
      }
    }
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.unitDetailContent {
  background: #fff;
  width: 80%;
  margin-right: 24px;
  padding: 20px;
}

.curriculum-name {
  font-size: 24px;
  font-weight: bold;
}

.curriculum-unit-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.curriculum-unit-name {
  text-align: center;
  font-size: 24px;
  color: #323338;
  font-weight: bold;
  margin-top: 35px;
  //自动换行
  word-break: break-word;
  // display: -webkit-box;
  // overflow: hidden;
  // -webkit-line-clamp: 4;
  // -webkit-box-orient: inherit;
  //宽度80%
  // width: 100%;
}

.currriculum-unit-info {
  height: 80px;
  width: 100%;
  background: #f5f6f8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  border-radius: 15px;
  padding: 10px;
  margin-bottom: 40px;

  .curriculum-info-item {
    width: 49.3%;
  }

  .curriculum-info-item-divider {
    height: 100%;
  }

  .currriculum-unit-info-num {
    color: #323338;
    font-weight: 600;
    font-size: 18px;
  }

  .currriculum-unit-info-title {
    color: #323338;
    font-size: 16px;
  }
}

.currriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 18px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}

.curriculum-info-value {
  padding: 0 0 0 16px;
  margin: 10px 0;
}
.unit-header {
  display: flex;
}
</style>
