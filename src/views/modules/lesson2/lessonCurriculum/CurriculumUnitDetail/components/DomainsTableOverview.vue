<template>
  <div>
    <table class="domain-table" v-if="unitAddressDomains && unitAddressDomains.length > 0">
      <thead>
        <tr>
          <th style="width:200px; background: #FFFDFA;">{{$t('loc.viewsample1')}}</th>
          <th>{{$t('loc.viewsample2')}}</th>  
        </tr>
      </thead>
      <tbody>
        <tr v-if="unitAddressDomains && (item.describe || (item.measureIds && item.measureIds.length > 0))" v-for="(item, i) in unitAddressDomains" :key="i" class="table-row">
          <td style="padding: 12px 8px; background: #FFFDFA;">
            <div style="font-weight: bold; font-size: 14px;">{{  measuresDisplayTransformed(item.measureIds)  }}</div>
          </td>
          <td style="padding: 12px 8px;">
            <div style="padding: 5px 15px;">{{ item.describe }}</div>
            <li v-for="measure in getMeasureInfo(item.measureIds)" class="display-flex;"
                style="padding: 5px 15px;line-height: 150%;font-size: 14px;">
              <span style="font-weight: bold; ">{{ measure.abbreviation }}: </span>
              <span>{{ measure.description }}</span>
            </li>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'DomainsTableOverview',
  props: ['unitAddressDomains', 'domains'],
  data() {
    return {
      measures: [] // 当前框架下领域所有测评点信息
    }
  },
  watch: {
    domains: {
      deep: true,
      immediate: true,
      handler() {
        if (this.domains) {
          let measures = []
          this.domains.forEach(item => {
            if (item.children) {
              measures.push(...item.children)
            }
          })
          this.measures = measures
        }
      }
    }
  },
  computed: {
    // 获取测评点信息
    getMeasureInfo () {
      return function (ids) {
        return this.measures.filter(item => ids.includes(item.id))
      }
    },
    // 测评点显示转换
    measuresDisplayTransformed () {
      return function (measureIds) {
        if (!measureIds) {
          return ''
        }
        let measures = this.measures.filter(item => measureIds.includes(item.id))
        if (measures.length > 0) {
          let measureTitle = measures[0].abbreviation.match(/[a-zA-Z-]*/ig)[0]
          let measureNumber = measures.map(measure => {
            return measure.abbreviation.replace(/[^0-9]/ig, '')
          }).join(', ')
          return measureTitle + ' ' + measureNumber
        }
        return ''
      }
    },
  }
}
</script>

<style lang="less" scoped>
.domain-table {
  border-radius: 4px;
  border: 1px solid #F49628;
  width: 100%;
  border-collapse: collapse;
  color: #323338;

  tr,
  th,
  td {
    border: 1px solid #F49628;
  }
  th {
    height: 40px;
  }

  td {
    padding-left: 5px;
  }
}

.table-row {
  position: relative;
}

.table-row:hover {
  .del-button {
    visibility: visible;
  }
}

.del-button {
  visibility: hidden;
  position: absolute;
  right: -20px;
  top: 0;
  color: red;
  height: -webkit-fill-available;
  align-items: center;
  display: flex;
}

/deep/.el-select {
  margin: 10px;
}

/deep/.el-input__inner {
  border-style: dashed;
}

/deep/.el-textarea__inner {
  border: none;
  resize: none;
}
</style>
