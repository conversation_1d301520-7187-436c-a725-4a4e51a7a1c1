<template>
  <div v-loading="isLoading" style="width: 100%;height: 100%;" id="unit-materials-table" class="unit-materials-table">
    <div v-if="otherMaterials && otherMaterials.length > 0" v-show="isShowTable" class="h-full">
      <div class="curriculum-unit-info-tag">
        {{ getOtherCategoryName }}
      </div>
      <el-table :data="getOtherCategory" border height="740"
                :cell-class-name="categoryCellClassName" header-row-class-name="table-header" style="width: 100%">
        <el-table-column fixed prop="categoryName" width="120">
        </el-table-column>
        <el-table-column v-for="week in getAllWeeks" :label="$t('loc.curriculum20',{ num: week })"
                         :width='getAllWeeks.length > 4 && "400px"'>
          <template slot-scope="scope">
            <div v-for="item in getMaterialsByWeekAndCategory(week, scope.row.categoryName)" class="materials-card"
                 v-if="(item.lessons && item.lessons.length > 0)">
              <CurriculumMaterials :lesson-materials="item.lessons">
              </CurriculumMaterials>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="centerMaterials && centerMaterials.length > 0" v-show="isShowTable">
      <div class="curriculum-unit-info-tag">
        Centers
      </div>
      <el-table :data="getCenterGroupCategory" border
                height="740"
                :cell-class-name="categoryCellClassName" header-row-class-name="table-header"
    :row-style="{ maxLength: '160px'}">
        <el-table-column fixed label="Centers" prop="categoryName" width="120">
        </el-table-column>
        <el-table-column v-for="week in getAllWeeks" :label="$t('loc.curriculum20',{ num: week })"
                         :width='getAllWeeks.length > 4 && "400px"'>
          <template slot-scope="scope">
            <div v-for="item in getCenterGroupMaterialsByWeekAndCategory(week, scope.row.categoryName)"
                 v-if="(item.lessons && item.lessons.length > 0)" class="materials-card">
              <CurriculumMaterials :lesson-materials="item.lessons">
              </CurriculumMaterials>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--  空页面  -->
    <div class="flex-column-center" style="height: 100%;" v-if="!isLoading && otherMaterials.length <= 0 && centerMaterials.length <= 0">
      <img src="@/assets/img/dll/pic_empty_dll.png" style="margin-top: -20px">
      <div class="text-center add-margin-t-8 font-size-14 " >
        {{$t('loc.plan36')}}
      </div>
    </div>
  </div>
</template>

<script>
import CurriculumMaterials from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMaterials'

export default {
  name: 'MaterialsOverview',
  components: { CurriculumMaterials },
  props: {
    otherMaterials: {
      type: Array
    },
    centerMaterials: {
      type: Array
    },
    isLoading: {
      type: Boolean
    }
  },
  watch: {
  },
  data() {
    return {
      isShowTable: true
    }
  },
  mounted () {
  },
  computed: {
    // 获取所有周计划的周次,并去重升序排序
    getAllWeeks () {
      let otherWeeks = []
      let centerGroupWeeks = []
      otherWeeks = this.otherMaterials.map(item => item.week)
      centerGroupWeeks = this.centerMaterials.map(item => item.week)
      let allWeeks = otherWeeks.concat(centerGroupWeeks)
      return allWeeks.filter((item, index) => {
        return allWeeks.indexOf(item) === index
      }).sort((a, b) => a - b)
    },
    // 获取其他分组的类别, 去重
    getOtherCategory () {
      if (!this.otherMaterials || this.otherMaterials.length <= 0) {
        return []
      }
      let categories = this.otherMaterials.map(item => item.categoryName)
      return categories.filter((item, index) => {
        return categories.indexOf(item) === index
      }).map(item => {
        return { categoryName: item }
      })
    },
    getOtherCategoryName () {
      return this.getOtherCategory.map(item => item.categoryName).join('/ ')
    },
    // 获取所有 Center 分组的类别, 去重
    getCenterGroupCategory () {
      if (!this.centerMaterials || this.centerMaterials.length <= 0) {
        return []
      }
      let categories = this.centerMaterials.map(item => item.categoryName)
      return categories.filter((item, index) => {
        return categories.indexOf(item) === index
      }).map(item => {
        return { categoryName: item }
      })
    },
    getMaterialsByWeekAndCategory () {
      return function (weekNumber, categoryName) {
        let materials = this.otherMaterials.filter(item => item.week === weekNumber && item.categoryName === categoryName)
        return materials
      }
    },
    getCenterGroupMaterialsByWeekAndCategory () {
      return function (weekNumber, categoryName) {
        let materials = this.centerMaterials.filter(item => item.week === weekNumber && item.categoryName === categoryName)
        return materials
      }
    }
  },
  methods: {
    categoryCellClassName ({ row, column, rowIndex, columnIndex }) {
      let classNames = 'unit-materials-table-call'
      if (columnIndex === 0) {
        classNames = 'table-call-header'
        return classNames
      }
      return classNames
    }
  }
}
</script>
<style lang="less" scoped>
.curriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0 16px;
  font-size: 16px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
  margin: 10px 0;
}

/deep/ .table-header th {
  background-color: #ecf9ff;
  white-space: nowrap;
  text-align: center;
  font-weight: bold;
  color: #323338;
}
/deep/ .el-table__cell {
  vertical-align: top;
}
/deep/ .table-call-header {
  background-color: #ecf9ff;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle !important;
  font-weight: bold;
  color: #323338;
}

/deep/ :hover .table-call-header {
  background-color: #ecf9ff !important;
  color: #323338 !important;
}
/deep/ :hover .unit-materials-table-call {
  background-color: #fff!important;
}
/deep/ .el-table .cell {
  padding: 0;
}
.unit-materials-table {
  /deep/ .el-table__cell {
    border: 1px solid #DCDFE6;
  }
  /deep/ .el-table__body-wrapper {
    &::-webkit-scrollbar {
      /* 滚动条整体样式 */
      width: 8px; /* 高宽分别对应横竖滚动条的尺寸 */
      height: 8px;
    }

    &::-webkit-scrollbar-thumb {
      /* 滚动条里面小方块 */
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: RGBA(182, 182, 182, 0.45);
    }

    &::-webkit-scrollbar-track {
      /* 滚动条里面轨道 */
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      background: #ededed;
    }
  }

}

.materials-card {
  overflow-y: hidden;
  overflow-x: hidden;
  padding: 5px;
}

.row-name {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  color: #323338;
}

.col-name {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  color: #323338;
}
</style>
