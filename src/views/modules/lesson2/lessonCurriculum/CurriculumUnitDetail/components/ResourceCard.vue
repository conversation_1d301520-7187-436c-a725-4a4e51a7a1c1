<template>
  <div>
    <!-- 资源卡片 -->
    <el-row :gutter="20" class="resource-items">
      <el-col v-for="(i, index) in resources" :key="index" :span="8">
        <div class="resource-item lg-pointer" :class="'rescouce-item-' + i.type" @click="viewAllResource(i.type)">
          <!-- 资源名称 -->
          <div>{{ i.name }}</div>
          <!-- 资源数量 -->
          <div class="rescouce-num" >{{ i.num }}</div>
          <!-- More 按钮 -->
          <div class="display-flex" style="height: 40px">
            <div>
              <el-button :class="'rescouce-more-' + i.type" round size="small" class="rescouce-more m-t-sm">
                {{ $t('loc.moreButten') }}
              </el-button>
            </div>
            <!-- 资源图标 -->
            <div class="rescouce-icon">
              <el-avatar size="large" shape="square" :src="i.icon"></el-avatar>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 资源详情弹窗 -->
    <view-resource-list-dialog  ref="viewResourceListDialogRef" :unit-week-title="unitWeekTitle" :showCoreMeasureOpen="showCoreMeasureOpen"></view-resource-list-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import bookList from '@/assets/img/lesson2/curriculum/book_list.png'
import keyVocabularies from '@/assets/img/lesson2/curriculum/key_vocabularies.png'
import printables from '@/assets/img/lesson2/curriculum/printables.png'
import assessment from '@/assets/img/lesson2/curriculum/assessment.png'
import ViewResourceListDialog from '../../components/CurriculumResources/ViewResourceListDialog'
export default {
  name: 'ResourceCards',
  components: {
    ViewResourceListDialog
  },
  props: {
    lesson: {
      type: Object,
      default: function () {
        return {}
      }
    },
    planId: {
      type: String
    },
    curriculumId: {
      type: String
    },
    unitId: {
      type: String
    },
    unitWeekTitle: {
      type: String
    },
    frameworkId: {
      type: String
    },
    frameworkName: {
      type: String
    },
    isEdit: {
      type: Boolean
    },
    frameworkLinkUrl: {
      type: String
    }
  },
  data () {
    return {
      resources: [], // 资源列表
      books: [], // 书
      dlls: [], // dll
      files: [], // 文件
      showFiles: [], // 显示的文件
      haveSelectMeasures: [], // 已选择的措施
      domainList: [], // 领域列表
      showDomainList: [], // 显示的领域列表
      viewALLBooks: [], // 查看全部的书
      viewALLDlls: [], // 查看全部的dll
      viewALLFiles: [], // 查看全部的文件
      showCoreMeasureOpen: false //是否显示核心测评点开关
    }
  },
  computed: {
    ...mapState({
      open: state => state.common.open
    }),
    dllOpen () {
      return this.open && this.open.dllopen
    }
  },
  watch: {
    // 监听planId变化，获取资源
    planId () {
      this.getUnitPlanReSource()
    }
  },
  mounted () {
    // this.getUnitPlanReSource()
  },
  methods: {
    // 获取系列课程单元周计划的材料和资源详细
    getUnitPlanReSource () {
      if (this.planId && this.planId.trim().length > 0) {
        this.resourceLoading = true
        let requestParams = {
          'curriculumId': this.curriculumId,
          'unitId': this.unitId,
          'planId': this.planId
        }
        this.$axios.get($api.urls().getPlanMaterialsAndResource, { params: requestParams })
        .then(res => {
          this.showCoreMeasureOpen = res.showCoreMeasureOpen
          // 通过全局总线将是否显示核心测评点开关值传递给子组件
          this.$bus.$emit('showCoreMeasureOpen', this.showCoreMeasureOpen)
          let materialsResourceInfo = res
          // 处理书
          let tempBooks = []
          let tempViewAllBooks = []
          if (materialsResourceInfo.books) {
            // 处理大小组的数据
            if (materialsResourceInfo.books.otherBooks && materialsResourceInfo.books.otherBooks.length > 0) {
              materialsResourceInfo.books.otherBooks.forEach(otherBook => {
                let tempOtherBook = {}
                tempOtherBook.resourceList = []
                tempOtherBook.categoryName = otherBook.categoryName
                if (otherBook.bookModels && otherBook.bookModels.length > 0) {
                  otherBook.bookModels.forEach(bookModel => {
                    if (bookModel.book) {
                      let tempBookArray = JSON.parse(bookModel.book)
                      let tempBook = tempBookArray[0]
                      tempBook.platformId = bookModel.id
                      tempOtherBook.resourceList.push(tempBook)
                      tempBooks.push(tempBook)
                    }
                  })
                }
                tempViewAllBooks.push(tempOtherBook)
              })
            }
            // 处理centers 的数据
            if (materialsResourceInfo.books.centerBooks && materialsResourceInfo.books.centerBooks.length > 0) {
              materialsResourceInfo.books.centerBooks.forEach(centerBook => {
                let tempCenterBook = {}
                tempCenterBook.resourceList = []
                tempCenterBook.categoryName = centerBook.categoryName
                if (centerBook.bookModels && centerBook.bookModels.length > 0) {
                  centerBook.bookModels.forEach(bookModel => {
                    if (bookModel.book) {
                      let tempBookArray = JSON.parse(bookModel.book)
                      let tempBook = tempBookArray[0]
                      tempBook.platformId = bookModel.id
                      tempCenterBook.resourceList.push(tempBook)
                      tempBooks.push(tempBook)
                    }
                  })
                }
                tempViewAllBooks.push(tempCenterBook)
              })
            }
          }
          this.books = tempBooks
          this.viewALLBooks = tempViewAllBooks
          // 处理词汇
          let tempVocabularies = []
          let tempViewAllVocabularies = []
          if (materialsResourceInfo.vocabularies) {
            // 处理大小组的数据
            if (materialsResourceInfo.vocabularies.otherVocabularies && materialsResourceInfo.vocabularies.otherVocabularies.length > 0) {
              materialsResourceInfo.vocabularies.otherVocabularies.forEach(otherVocabulary => {
                let tempOtherVocabulary = {}
                tempOtherVocabulary.resourceList = []
                tempOtherVocabulary.categoryName = otherVocabulary.categoryName
                if (otherVocabulary.vocabularyModels && otherVocabulary.vocabularyModels.length > 0) {
                  otherVocabulary.vocabularyModels.forEach(vocabularyModel => {
                    let tempVocabulary = {
                      'id': vocabularyModel.id,
                      'name': vocabularyModel.content,
                      'type': vocabularyModel.type,
                      'mediaUrl': vocabularyModel.media ? vocabularyModel.media.url : '',
                      'mediaId': vocabularyModel.media ? vocabularyModel.media.id : ''
                    }
                    tempVocabularies.push(tempVocabulary)
                    tempOtherVocabulary.resourceList.push(tempVocabulary)
                  })
                }
                tempViewAllVocabularies.push(tempOtherVocabulary)
              })
            }
            // 处理centers 的数据
            if (materialsResourceInfo.vocabularies.centerVocabularies && materialsResourceInfo.vocabularies.centerVocabularies.length > 0) {
              materialsResourceInfo.vocabularies.centerVocabularies.forEach(centerVocabulary => {
                let tempCenterVocabulary = {}
                tempCenterVocabulary.resourceList = []
                tempCenterVocabulary.categoryName = centerVocabulary.categoryName
                if (centerVocabulary.vocabularyModels && centerVocabulary.vocabularyModels.length > 0) {
                  centerVocabulary.vocabularyModels.forEach(vocabularyModel => {
                    let tempVocabulary = {
                      'id': vocabularyModel.id,
                      'name': vocabularyModel.content,
                      'type': vocabularyModel.type,
                      'mediaUrl': vocabularyModel.media ? vocabularyModel.media.url : '',
                      'mediaId': vocabularyModel.media ? vocabularyModel.media.id : ''
                    }
                    tempVocabularies.push(tempVocabulary)
                    tempCenterVocabulary.resourceList.push(tempVocabulary)
                  })
                }
                tempViewAllVocabularies.push(tempCenterVocabulary)
              })
            }
          }
          this.dlls = tempVocabularies
          this.viewALLDlls = tempViewAllVocabularies
          // 处理附件
          let tempAttachments = []
          let tempViewAllAttachments = []
          if (materialsResourceInfo.attachments) {
            // 处理大小组的数据
            if (materialsResourceInfo.attachments.otherAttachments && materialsResourceInfo.attachments.otherAttachments.length > 0) {
              materialsResourceInfo.attachments.otherAttachments.forEach(otherAttachment => {
                let tempOtherFile = {}
                tempOtherFile.resourceList = []
                tempOtherFile.categoryName = otherAttachment.categoryName
                if (otherAttachment.attachmentModels && otherAttachment.attachmentModels.length > 0) {
                  otherAttachment.attachmentModels.forEach(attachmentModel => {
                    let tempFile = {
                      'id': attachmentModel.id,
                      'name': attachmentModel.media.sourceFileName ? attachmentModel.media.sourceFileName : '',
                      'size': attachmentModel.media.size,
                      'url': attachmentModel.media.url ? attachmentModel.media.url : '',
                      'fileType': attachmentModel.media.fileType ? attachmentModel.media.fileType : ''
                    }
                    tempAttachments.push(tempFile)
                    tempOtherFile.resourceList.push(tempFile)
                  })
                }
                tempViewAllAttachments.push(tempOtherFile)
              })
            }
            // 处理centers 的数据
            if (materialsResourceInfo.attachments.centerAttachments && materialsResourceInfo.attachments.centerAttachments.length > 0) {
              materialsResourceInfo.attachments.centerAttachments.forEach(centerAttachment => {
                let tempCenterFile = {}
                tempCenterFile.resourceList = []
                tempCenterFile.categoryName = centerAttachment.categoryName
                if (centerAttachment.attachmentModels && centerAttachment.attachmentModels.length > 0) {
                  centerAttachment.attachmentModels.forEach(attachmentModel => {
                    let tempFile = {
                      'id': attachmentModel.id,
                      'name': attachmentModel.media.sourceFileName ? attachmentModel.media.sourceFileName : '',
                      'size': attachmentModel.media.size,
                      'url': attachmentModel.media.url ? attachmentModel.media.url : '',
                      'fileType': attachmentModel.media.fileType ? attachmentModel.media.fileType : ''
                    }
                    tempAttachments.push(tempFile)
                    tempCenterFile.resourceList.push(tempFile)
                  })
                }
                tempViewAllAttachments.push(tempCenterFile)
              })
            }
          }
          this.files = tempAttachments
          this.viewALLFiles = tempViewAllAttachments
          // 处理测评点
          if (materialsResourceInfo.measures && materialsResourceInfo.measures.length > 0) {
            this.haveSelectMeasures = []
            materialsResourceInfo.measures.forEach(measure => {
              this.haveSelectMeasures.push(measure.id)
            })
          } else {
            this.haveSelectMeasures = []
          }
          let tempDomains = []
          if (materialsResourceInfo.domains && materialsResourceInfo.domains.length > 0) {
            materialsResourceInfo.domains.forEach(domain => {
              let tempDomain = {}
              tempDomain.id = domain.id
              tempDomain.abbreviation = domain.abbreviation
              tempDomain.name = domain.name
              tempDomain.resourceList = []
              if (materialsResourceInfo.measures && materialsResourceInfo.measures.length > 0) {
                materialsResourceInfo.measures.forEach(measure => {
                  if (measure.parentId.toUpperCase() === domain.id.toUpperCase()) {
                    let tempChild = {
                      'id': measure.id,
                      'abbreviation': measure.abbreviation,
                      'name': measure.name,
                      'core': measure.core
                    }
                    tempDomain.resourceList.push(tempChild)
                  }
                })
              }
              tempDomains.push(tempDomain)
            })
          }
          this.domainList = tempDomains
          let resources = [
              {
                'name': this.$t('loc.curriculum14'),
                'type': 'books',
                'icon': bookList,
                'num': this.books.length
              },
              {
                'name': this.$t('loc.curriculum15') ,
                'type': 'vocabularies',
                'icon': keyVocabularies,
                'num': this.dlls.length
              },
              {
                'name': this.$t('loc.curriculum16'),
                'type': 'printables',
                'icon': printables,
                'num': this.files.length
              },
              {
                'name': this.$t('loc.curriculum18'),
                'type': 'measures',
                'icon': assessment
              }
          ]
          // dll 功能未开启，过滤掉词汇模块
          if (!this.dllOpen) {
            resources = resources.filter(item => item.type !== 'vocabularies')
          }
          this.resources = resources
        })
        .catch(error => {
          this.$message.error(error.error_message)
        })
      }
    },
    // 查看全部资源
    viewAllResource (resourceName) {
      if (resourceName === 'books') {
        this.$refs.viewResourceListDialogRef.showViewResourceListDialog(true, false, false, false, this.$t('loc.curriculum14'), this.viewALLBooks)
      } else if (resourceName === 'vocabularies') {
        this.$refs.viewResourceListDialogRef.showViewResourceListDialog(false, true, false, false, this.$t('loc.curriculum15'), this.viewALLDlls)
      } else if (resourceName === 'printables') {
        this.$refs.viewResourceListDialogRef.showViewResourceListDialog(false, false, true, false, this.$t('loc.curriculum16'), this.viewALLFiles)
      } else if (resourceName === 'measures') {
        this.$refs.viewResourceListDialogRef.showViewResourceListDialog(false, false, false, true, this.$t('loc.curriculum18'), this.domainList)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.resource-items {
  display: flex;
  flex-wrap: wrap;
  row-gap: 20px
}
.resource-item {
  height: 100%;
  margin-bottom: 10px;
  padding: 15px;
  background: #F2F6FE;
  border-radius: 15px;
  position: relative;
}
.rescouce-item-books {
  background: #F2F6FE;
}
.rescouce-item-vocabularies {
  background: #EFF9FF;
}
.rescouce-item-printables {
  background: #FDF6ED;
}
.rescouce-item-activities {
  background: #E4FFFA;
}
.rescouce-item-measures {
  background: #FDEDED;
}
.rescouce-num {
  font-size: 24px;
  font-weight: 500;
  height: 24px;
}
.rescouce-more {
  position: absolute;
  bottom: 10px;
  color:#FFF;
  font-size: 14px;
  font-weight: 500;
  border: none;
}
.rescouce-icon {
  background: #FFF;
  border-radius: 15px;
  padding: 5px;
  text-align: center;
  margin-bottom: 10px;
  right: 10px;
  bottom: 0;
  position: absolute;
  /deep/ .el-avatar {
    background: #FFF;
  }
  /deep/ .el-avatar--large {
    width: 60px;
    height: 60px;
    line-height: 60px;
  }
}
.rescouce-more-books {
  background: #85ABF0;
}
.rescouce-more-vocabularies {
  background: #97D7FE;
}
.rescouce-more-printables {
  background: #F9BB80;
}
.rescouce-more-activities {
  background: #3DDEC2;
}
.rescouce-more-measures {
  background: #F98080;
}
/deep/ .el-col-8 {
  /**兼容safari 浏览器 */
  width: 33.3%;
}
</style>
