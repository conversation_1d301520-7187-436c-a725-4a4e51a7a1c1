<template>
  <div v-if="unit">
    <div style="position: -webkit-sticky; position: sticky; top: -21px;z-index: 999;background: #FFFFFF;font-family: Inter;font-weight:bold;">
      <el-tabs v-model="tabName" class="customer-tab" @tab-click="goNavList">
        <el-tab-pane v-for="(tab, index) in tabs" :name="tab.name" :key="index" :label="tab.name"></el-tab-pane>
      </el-tabs>
    </div>
    <div style="width: fit-content;">
      <!--   Overview -->
      <div class="div1">
        <div style="margin-bottom: -50px"></div>
        <div style="padding-top: 50px;">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.unitOverview') }}
            </div>
            <div class="curriculum-info-value" style="text-align: left;">
              {{ unit.description }}
            </div>
            <div class="curriculum-info-value" style="text-align: left;">
              <el-button plain @click="openPlannerDialog">{{ $t('loc.descfont')  }}</el-button>
              <el-button plain @click="openMaterialsDialog">{{ $t('loc.materials')  }}</el-button>
            </div>
          </el-row>
        </div>
      </div>
      <!--   Guiding Questions -->
      <div class="div2">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.unitaddress1') }}
            </div>
            <div class="curriculum-info-value unit-list-algin" v-html="unit.guidingQuestions">
            </div>
          </el-row>
        </div>
      </div>
      <!-- Weekly Lesson Planning -->
      <div class="div3">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-30"></div>
        <WeeklyInfo :curriculumId="curriculumId" :unitId="unit.id" :unitTitle="unit.title" :unitNum="unit.number" :plans="unit.plans" :isEditor="isEditor" @changeWeek="changeWeek" :frameworkId="frameworkId"></WeeklyInfo>
      </div>
      <!-- Resource -->
      <div class="div4">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.resources') }}
            </div>
            <div class="curriculum-info-value">
              <resource-card ref="resourceInfoRef" :plan-id="planId" :curriculum-id="curriculumId" :unit-id="unit.id" :unit-week-title="unitWeekTitle"></resource-card>
            </div>
          </el-row>
        </div>
      </div>
      <!--  Unit Trajectory -->
      <div class="div5">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.trajectory') }}
            </div>
            <div class="curriculum-info-value" style="text-align: left;" v-html="unit.trajectory">
            </div>
          </el-row>
        </div>
      </div>
      <!-- Concepts -->
      <div class="div6">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <!-- Concepts -->
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.concepts') }}
            </div>
            <div class="curriculum-info-value" style="text-align: left;" v-html="unit.concepts">
            </div>
          </el-row>
        </div>
      </div>
    </div>
    <div class="planner-materials-over-view-dialog">
      <!-- 单元概览 材料概览弹窗 -->
      <el-dialog :visible="plannerDialogShow"
                 v-if="plannerDialogShow"
                 width="70%"
                 :before-close="closePlannerDialog" :title="plannerTitle">
        <!-- 仅展示核心测评点 switch start -->
        <div class="core-measure" style="margin-bottom: 10px; display: flex; align-items: center" v-show="showCoreMeasureOpen">
          <el-switch
            v-model="showCoreMeasure"
            @change="changeCoreMeasureState">
        </el-switch>
          <span class="lesson-switch" style="padding-left:5px">{{ $t('loc.showCoreMeasureOnly') }}</span>
        </div>
        <!-- 仅展示核心测评点 switch end -->
        <UnitPlannerOverview ref="unit-plan-overview" :unitPlanners="unitPlanners" :isLoading="!unitPlannersIsLoaded"/>
        <div slot="footer" class="pdfBtn">
          <el-button class="m-l-sm" icon="fa fa-file-pdf-o" v-if="false" v-show="unitPlannersIsLoaded && unitPlanners && unitPlanners.length > 0">PDF</el-button>
          <curriculum-apply :curriculum="unitCurriculum" source="overviewCard" buttonType="primary" :lessionTitle="$t('loc.curriculum3')"></curriculum-apply>
        </div>
      </el-dialog>
      <el-dialog class="materials-over-view-dialog" width="70%" :visible="materialsDialogShow" :before-close="closeMaterialsDialog" :title="materialsTitle">
        <MaterialsOverview v-if="materialsDialogShow"
                           :otherMaterials="unitMaterials && unitMaterials.otherMaterials"
                           :centerMaterials="unitMaterials && unitMaterials.centerMaterials"
                           :isLoading="!unitMaterialsIsLoaded"
        />
        <div class="pdfBtn" slot="footer">            
          <download-materials v-show="unitMaterialsIsLoaded && (unitMaterials.otherMaterials.length > 0 || unitMaterials.centerMaterials.length > 0)" :unit-id="unit.id" />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import DomainsTableOverview from './DomainsTableOverview.vue'
import UnitPlannerOverview from './UnitPlannerOverview.vue'
import MaterialsOverview from './MaterialsOverview.vue'
import WeeklyInfo from './WeeklyInfo.vue'
import ResourceCard from './ResourceCard'
import CurriculumApply from '../../components/CurriculumApply'
import DownloadMaterials from '@/views/modules/lesson2/lessonCurriculum/components/DownloadMaterials'
import Lessons2 from '@/api/lessons2'
import {mapState} from "vuex";

export default {
  name: 'OverViewTab',
  props: {
    isEditor: {
      type: Boolean
    },
    unit: {
      type: Object
    },
    curriculumId: {
      type: String
    },
    // 框架 ID
    frameworkId: {
      type: String
    },
    domains: { // 框架信息
      type: Array
    }
  },
  components: {
    DomainsTableOverview,
    CurriculumApply,
    WeeklyInfo,
    ResourceCard,
    UnitPlannerOverview,
    MaterialsOverview,
    DownloadMaterials
  },
  data () {
    return {
      plannerDialogShow: false,
      materialsDialogShow: false,
      plannerTitle: this.$t('loc.descfont'),
      materialsTitle: this.$t('loc.materials'),
      unitPlanners: [],
      unitMaterials: null,
      unitPlannersIsLoaded: false,
      unitMaterialsIsLoaded: false,
      tabs: [
        {
          name: this.$t('loc.unitOverview'),
          refName: 'setOneRef'
        },
        {
          name: this.$t('loc.unitaddress1'),
          refName: 'setSevenRef'
        },
        {
          name: this.$t('loc.lesson2TabName3'),
          refName: 'setTwoRef'
        },
        {
          name: this.$t('loc.resources'),
          refName: 'setThreeRef'
        },
        {
          name: this.$t('loc.trajectory'),
          refName: 'setFourRef'
        },
        {
          name: this.$t('loc.concepts'),
          refName: 'setFiveRef'
        }
      ],
      tabName: this.$t('loc.unitOverview'),
      weekNum: 1,
      planId: '',
      showCoreMeasure: true, // 仅显示核心测评点开关
      showCoreMeasureOpen: false,
      previousLangCode: '' // 上次切换语言时的语言类型
    }
  },
  watch: {
    unit: {
      deep: true,
      immediate: true,
      handler () {
        this.unitPlanners = []
        this.unitMaterials = null
        if (this.unit.plans && this.unit.plans.length > 0) {
          this.planId = this.unit.plans[0].planId
        }
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.previousLangCode = this.contentLanguage
    })
  },
  computed: {
    ...mapState({
      contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 课程源语言类型
    }),
    // 内容的当前语言是否与源语言相同
    isSameLanguage () {
      return this.contentLanguage === this.contentOriginalLanguage
    },
    unitWeekTitle () {
      if (this.unit.number >=1 ) {
        return 'Unit ' + this.unit.number + ' / Week ' + this.weekNum
      } else {
        return 'Specail Unit ' + this.unit.number + ' / Week ' + this.weekNum
      }
    },
    // 单元课程信息
    unitCurriculum () {
      return {
        id: this.curriculumId,
        units: [this.unit]
      }
    }
  },
  methods: {
    openPlannerDialog () {
      this.$analytics.sendEvent('web_curriculum_detial_overview_plan')
      if (!this.unit) {
        return
      }
      this.plannerDialogShow = true
      if (this.unitPlanners.length <= 0 || this.previousLangCode !== this.contentLanguage) {
        this.previousLangCode = this.contentLanguage
        this.unitPlannersIsLoaded = false
        let params = {
          unitId: this.unit.id,
          ...((!this.isSameLanguage && this.contentLanguage) && { langCode: this.contentLanguage })
        }
        Lessons2.getUnitPlanOverView(params)
          .then(res => {
            this.showCoreMeasureOpen = res.showCoreMeasureOpen
            this.showCoreMeasure = res.showCoreMeasureOpen
            this.$nextTick(() => {
              this.changeCoreMeasureState(this.showCoreMeasure)
            })
            this.unitPlanners = res.unitPlanners.filter(item => (item.planItems && item.planItems.length > 0))
            this.unitPlannersIsLoaded = true
          })
          .catch(error => {
            this.$message.error(error.response.data.error_message)
          })
        this.$nextTick(() => {
          this.changeCoreMeasureState(this.showCoreMeasure)
        })
      }
    },
    openMaterialsDialog () {
      this.$analytics.sendEvent('web_curriculum_detial_overview_materials')
      if (!this.unit) {
        return
      }
      this.materialsDialogShow = true
      if (!this.unitMaterials) {
        this.unitMaterialsIsLoaded = false
        let params = { unitId: this.unit.id,
          ...((!this.isSameLanguage && this.contentLanguage) && { langCode: this.contentLanguage })
        }
        Lessons2.getUnitPlanMaterials(params)
          .then(res => {
            this.unitMaterials = {
              centerMaterials: res.centerMaterials.filter(item => (item.lessons && item.lessons.length > 0)),
              otherMaterials: res.otherMaterials.filter(item => (item.lessons && item.lessons.length > 0))
            }
            this.unitMaterialsIsLoaded = true
          })
          .catch(error => {
            this.$message.error(error.response.data.error_message)
          })
      }
    },

    closePlannerDialog () {
      this.plannerDialogShow = false
      this.unitPlanners = []
    },
    closeMaterialsDialog () {
      this.materialsDialogShow = false
    },
    // 页面锚点定位
    goNavList (tab) {
      this.$nextTick(() => {
        // 获取 内容DIV元素在页面中的位置，div1、div2、div3..
        switch (tab.index) {
          case '0':
            this.$el.querySelector('.div1').scrollIntoView({ block: 'start', behavior: 'smooth' })
            break
          case '1':
            this.$el.querySelector('.div2').scrollIntoView({ block: 'start', behavior: 'smooth' })
            break
          case '2':
            this.$el.querySelector('.div3').scrollIntoView({ block: 'start', behavior: 'smooth' })
            break
          case '3':
            this.$el.querySelector('.div4').scrollIntoView({ block: 'start', behavior: 'smooth' })
            break
          case '4':
            this.$el.querySelector('.div5').scrollIntoView({ block: 'start', behavior: 'smooth' })
            break
          case '5':
            this.$el.querySelector('.div6').scrollIntoView({ block: 'start', behavior: 'smooth' })
            break
          default:
            break
        }
      })
    },
    // 切换周计划
    changeWeek (val) {
      this.weekNum = val.week
      this.$nextTick(() => {
        if (this.unit && this.unit.plans) {
          this.unit.plans.forEach(item => {
            if (item.number == this.weekNum) {
              this.planId = item.planId
            }
          })
        }
        if (val.scroll) {
          this.goNavList({ index: '2' })
        }
      })
    },
    changeCoreMeasureState (val) {
      this.$bus.$emit('overViewChangeShowCore', val)
      this.$nextTick(() => {
        this.$refs['unit-plan-overview'].doLayout()
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ #unit-resource-info {
  padding-top: 40px;
}

/deep/ .customer-tab {
  width: 100%;
  height: 50px;
  padding: 4px;
}

/deep/ .el-tabs__header {
  margin: 0;
}

/deep/ .customer-tab {
  width: 100%;
  height: 50px;
  padding: 4px;
}

/deep/ .el-tabs--card>.el-tabs__header .el-tabs__item {
  color: #fff;
}

/deep/ .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  background: #FFF !important;
  color: #FCBA4C !important;
  font-weight: bold;
  border-radius: 4px !important;
}
/deep/ .el-tabs__item.is-active {
  font-size: 16px;
}
/deep/.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
  padding-left: 20px;
}
/deep/ .el-tabs--top .el-tabs__item.is-top:last-child {
  padding-left: 20px;
  padding-right: 20px;
}
.curriculum-unit-info {
  height: 80px;
  width: 100%;
  background: #f5f6f8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  border-radius: 15px;
  padding: 10px;

  .curriculum-info-item {
    width: 49.3%;
  }

  .curriculum-info-item-divider {
    height: 100%;
  }

  .curriculum-unit-info-num {
    color: #111111;
    font-weight: 600;
    font-size: 24px;
  }

  .curriculum-unit-info-title {
    font-size: 14px;
  }
}

.curriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 16px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}

.curriculum-info-value {
  font-size: 16px;
  padding: 0 0 0 16px;
  margin: 12px 0 24px 0;
  color: #111c1c;
  //自动换行
  word-break: break-word;
}

.unit-list-algin {
  text-align: left;
}

.over-top {
  display: flex;
  justify-content: space-between;
}

.desc_font {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #323338;
}

.pdfBtn {
  text-align: right;
  padding: 10px;
}
.planner-materials-over-view-dialog {
  .materials-over-view-dialog {
    /deep/ .el-dialog__body {
      overflow-y: auto;
    }
  }
  /deep/ .el-dialog {
    margin: 0!important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 95%;
    display: flex;
    flex-direction: column;
  }
  /deep/ .el-dialog__body {
    color: #606266;
    word-break: break-all;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 24px;
    font-size: 14px;

    &::-webkit-scrollbar {
      /* 滚动条整体样式 */
      width: 8px; /* 高宽分别对应横竖滚动条的尺寸 */
      height: 8px;
    }

    &::-webkit-scrollbar-thumb {
      /* 滚动条里面小方块 */
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: RGBA(182, 182, 182, 0.45);
    }

    &::-webkit-scrollbar-track {
      /* 滚动条里面轨道 */
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      background: #ededed;
    }
  }

  /deep/ .el-dialog__title {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #323338;
  }

  /deep/ .el-dialog__header {
    text-align: left;
    padding: 24px 24px 0;
  }
}
</style>
