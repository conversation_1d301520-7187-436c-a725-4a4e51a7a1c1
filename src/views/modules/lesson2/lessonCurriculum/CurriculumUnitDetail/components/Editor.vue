<template>
  <div>
    <quill-editor v-model="content" :options="editorOption" ref="editor" @change="handleChange($event)"
                  @blur="handleBlur"
                  @click.native="handleClick"
                  :class="['editor', { validate: _validateEvent }]"/>
  </div>
</template>
<script>
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import {quillEditor} from 'vue-quill-editor'
import Quill from 'quill'
import emitter from 'element-ui/src/mixins/emitter'

const Size = Quill.import('attributors/style/size')
Size.whitelist = ['14px', '16px', '18px', '20px', '22px']
Quill.register(Size, true)
export default {
  name: 'Editor',
  components: {quillEditor},
  props: [
    'value',
    'placeholder',
    'maxlength',
    'validateEvent'
  ],
  mixins: [emitter],
  computed: {
    _maxLength () {
      return this.maxLength || 5000
    },
    _validateEvent () {
      return this.validateEvent || this.validateEvent === undefined
    }
  },
  mounted () {
    this.$refs.editor.quill.enable(false)
  },
  data() {
    return {
      content: this.value || '',
      editorOption: {
        modules: {
          toolbar: [
            [{font: []}],
            [{size: Size.whitelist}],
            [{header: []}],
            [{'list': 'ordered'}, {'list': 'bullet'}],
            [{'align': []}],
            [{'indent': '-1'}, {'indent': '+1'}],
            ['bold', 'italic', 'underline', 'strike'],
            [{'color': []}, {'background': []}],
          ]
        },
        theme: 'snow',
        placeholder: this.placeholder
      }
    }
  },
  watch: {
    value () {
      this.content = this.value
    },
    content (value) {
      this.$emit('input', value)
      if (this._validateEvent) {
        this.dispatch('ElFormItem', 'el.form.change', [value])
      }
    }
  },
  methods: {
    handleClick () {
        this.$refs.editor.quill.enable(true)
        this.$refs.editor.quill.focus()
    },
    handleChange(event) {
      let length = event.quill.getLength()
      if (length > this._maxLength) {
        event.quill.deleteText(this._maxLength, length - this._maxLength)
      }
    },
    handleBlur(event) {
      this.$emit('blur', event);
      if (this.validateEvent) {
        this.dispatch('ElFormItem', 'el.form.blur', [this.value]);
      }
    }
  }
}
</script>
<style scoped lang="less">
.editor /deep/ & {
  word-break: break-word;
  line-height: normal;
  height: 100%;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.42 !important;
  }

  .ql-toolbar {
    border-bottom: none;
    padding-bottom: 0;

    .ql-size.ql-picker {
      width: 60px;
    }
  }

  .ql-container {
    height: calc(100% - 35px)
  }

  .ql-editor {
    overflow-y: hidden;
    min-height: 100px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮

    &.ql-blank:before {
      font-style: normal;
    }
  }
}

.el-form-item.is-error .editor.validate /deep/ & {

  .ql-toolbar,
  .ql-container {
    border-color: red;
  }
}
</style>
<style lang="less">
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='12px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='12px']::before {
  content: '12px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='14px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='14px']::before {
  content: '14px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='16px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  content: '16px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='18px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='18px']::before {
  content: '18px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='20px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='20px']::before {
  content: '20px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='22px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='22px']::before {
  content: '22px';
}
</style>
