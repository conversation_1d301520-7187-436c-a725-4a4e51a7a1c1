<template>
  <div>
    <table class="domain-table">
      <thead>
        <tr>
          <th style="width:200px">{{ $t('loc.viewsample1') }}</th>
          <th>{{ $t('loc.viewsample2') }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in unitAddressDomains" :key="index" class="table-row">
          <td>
            <el-select multiple v-model="item.measureIds" placeholder="Domain" @visible-change="selectMeasureVisibleHandler" ref="unitAddressesDomainsSelect">
              <el-option-group v-for="domain in domains" :key="domain.id" :label="domain.abbreviation" :disabled="optionDisabled(domain, item.measureIds)">
                <el-option v-for="measure in domain.children" :key="measure.id" :label="measure.abbreviation"
                  :disabled="optionDisabled(domain, item.measureIds)"
                  :value="measure.id">
                </el-option>
              </el-option-group>
            </el-select>
          </td>
          <td>
            <el-input type="textarea" placeholder="1-2 sentences about how unit addresses this DRDP domain"
                      maxlength="5000"
              v-model="item.describe" autosize>
            </el-input>
            <div v-for="measure in getMeasureInfo(item.measureIds)" style="padding: 5px 15px;line-height: 150%;">
              <span style="font-weight: bold;">{{ measure.abbreviation }}: </span>
              <span>{{ measure.description }}</span>
            </div>
            <div class="del-button">
              <el-button type="text" icon="el-icon-close" style="color: red" @click="delRow(index)"></el-button>
            </div>
          </td>
        </tr>
        <tr align="center" cols>
          <td colspan="2">
            <el-button icon="el-icon-plus" type="text" @click="addUnitDomains">{{$t('loc.viewsample3')}}</el-button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'DomainsTableEditor',
  props: [
    'value', // 单元领域学习 [{measureIds: [], describe: ''}]
    'domains',// 框架领域
    'frameworkId'// 选择的框架 ID
  ],
  data () {
    return {
      unitAddressDomains: [{ describe: '', measureIds: [] }],
      measures: null
    }
  },
  watch: {
    value: {
      deep: true,
      handler (value) {
          this.unitAddressDomains = value || [{ describe: '', measureIds: [] }]
        }
    },
    unitAddressDomains: {
      deep: true,
      handler () {
        this.$emit('input', this.unitAddressDomains)
      }
    },
    domains: {
      deep: true,
      immediate: true,
      handler (value) {
        if (!this.unitAddressDomains) {
            return
        }
        let measures = []
        this.domains.forEach(item => {
          if (item.children) {
            measures.push(...item.children)
          }
        })
        this.measures = measures
        // 切换框架，保留重复的测评点
        this.unitAddressDomains.forEach(item => {
          // 取两者交集
            item.measureIds = item.measureIds.filter(id => measures.find(measure => measure.id === id))
        })
      }
    }
  },
  computed: {
    // 根据测评 IDs 获取测评点信息
    getMeasureInfo () {
      return function(ids) {
        if (!this.measures || this.measures.length <= 0) {
          return
        }
        return this.measures.filter(item => ids.includes(item.id))
      }
    },
    // 根据领域、选择的测评点 IDs 判断该领域是否不可选择
    optionDisabled () {
      return function (domain, measureIds) {
        // 拿到改行的已选择的测评点的领域 ID
        let itemDomainId = ''
        this.domains.forEach(item => {
          if (item.children && item.children.find(measure => measureIds.includes(measure.id))) {
            itemDomainId = item.id
          }
        })
        // 如果沒有选择测评点或者选择的是该领域的测评点，则该domain可选
        if (itemDomainId == '' && !this.selectedDomainIds.includes(domain.id)) {
          return false
        }
        if (this.selectedDomainIds.includes(domain.id) && itemDomainId == domain.id) {
          return false
        }
        return true
      }
    },
    // 已选的测评点 IDs
    selectedMeasureIds () {
      let selectedMeasureIds = []
      this.unitAddressDomains.forEach(item => {
        selectedMeasureIds = selectedMeasureIds.concat(item.measureIds)
      })
      return selectedMeasureIds
    },
    // 已选的领域 IDs
    selectedDomainIds () {
      let selectedDomainIds = []
      this.unitAddressDomains.forEach(item => {
        this.domains.forEach(domain => {
          if (domain.children && domain.children.find(measure => item.measureIds.includes(measure.id)) && !selectedDomainIds.includes(domain.id)) {
            selectedDomainIds.push(domain.id)
          }
        })
      })
      return selectedDomainIds
    }
  },
  methods: {
    selectMeasureVisibleHandler (visible) {
      if (!this.frameworkId && visible) {
        this.$message.error('Please select the framework first')
        this.$refs.unitAddressesDomainsSelect[0].blur()
      }
    },
    addUnitDomains () {
      this.unitAddressDomains.push({
          describe: '',
          measureIds: []
        })
    },
    delRow (index) {
      if (this.unitAddressDomains.length > 1) {
        this.unitAddressDomains.splice(index, 1)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.domain-table {
  border-radius: 4px;
  border: 1px solid #F49628;
  width: 100%;
  border-collapse: collapse;
  color: #323338;

  tr,
  th,
  td {
    border: 1px solid #F49628;
  }
  th {
    height: 40px;
  }
}

.is-error {
  .domain-table {
    border: 1px solid red;

    tr,
    th,
    td {
      border: 1px solid red;
    }
  }
}

.table-row {
  position: relative;
}

.table-row:hover {
  .del-button {
    visibility: visible;
  }
}

.del-button {
  visibility: hidden;
  position: absolute;
  right: -20px;
  top: 0;
  color: red;
  height: -webkit-fill-available;
  align-items: center;
  display: flex;
}

/deep/.el-select {
  margin: 10px;
}

/deep/.el-input__inner {
  border-style: dashed;
}

/deep/.el-textarea__inner {
  border: none;
  resize: none;
}
</style>
