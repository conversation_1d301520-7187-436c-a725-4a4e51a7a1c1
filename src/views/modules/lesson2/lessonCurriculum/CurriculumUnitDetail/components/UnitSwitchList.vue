<template>
  <!-- 课程侧边栏组件 -->
  <div class="over-view-card">
    <!-- 应用单元课程（仅已发布的单元课程显示） -->
    <curriculum-apply v-if="published" :key="selectedUnitId" class="curriculum-apply lg-margin-bottom-12" source="unit" :lessionTitle="$t('loc.curriculum3')"/>
    <!-- 单元列表 -->
    <div class="lg-border-radius-8 lg-box-shadow bg-white lg-scrollbar-show" :style="{'max-height':'calc(100% - ' + (published ? '56px': '0px') + ')'}">
      <div class="title-name">{{ $t('loc.overview') }}</div>
      <div class="curriculum-item lg-pointer" v-loading="!unitList || unitList.length <= 0" v-for="(unit, index) in unitList" :key="index" @click="selectedClick(unit)"
          :class="{ 'curriculum-item-selected': selectedUnitId === unit.id }">
        <!-- card title -->
        <div class="card-title"  :title="unit.title">
          <template v-if="unit.number >= 1">
          {{ $t('loc.curriculum28') }} {{ unit.number }}: {{ unit.title }}
          </template>
          <template v-else>
          {{ 'Special Unit'}} {{ unit.number }}: {{ unit.title }}
          </template>
        </div>
        <!-- card content -->
        <div class="card-content">
          <span>{{ $t('loc.curriculum7') }}: {{ unit.weeks }}</span><span>{{ $t('loc.curriculum8') }}: {{ unit.activitiesNum }}</span>
        </div>
      </div>
    </div>
    <el-dialog :visible="waitDialogVisable" style="margin-top:15vh;" :append-to-body="true"  :before-close="closeWaitDialog">
      <div style="height:400px;">
        <empty-view text="Please stay tuned for our upcoming units."></empty-view>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import EmptyView from '../../components/EmptyView'
import CurriculumApply from '../../components/CurriculumApply'
import { mapState } from 'vuex'
export default {
  name: 'UnitSwitchList',
  components: { EmptyView, CurriculumApply },
  data () {
    return {
      currentIndex: 0,
      waitDialogVisable: false
    }
  },
  props: ['unitList', 'selectedUnitId', 'type'],
  inject: ['isPublished'],
  computed: {
    ...mapState({
      planRecording: state => state.lesson.planRecording // 是否录制周计划中
    }),
    // 是否已发布的课程
    published () {
      return this.isPublished()
    }
  },
  methods: {
    // 切换单元点击事件
    selectedClick (unit) {
      // 正在录制周计划讲解，禁止切换单元
      if (this.planRecording) {
        this.$message.warning(this.$t('loc.plan145'))
        return
      }
      this.selectedUnitId = unit.id
      this.$router.replace({
        name: 'curriculumUnitDetail',
        params: {
          unitId: this.selectedUnitId,
          curriculumId: this.$route.params.curriculumId,
          frameworkId: this.$route.params.frameworkId,
          weekNum: 1,
          curriculumName: this.$route.params.curriculumName || null,
          type: this.type || null
        }
      })
    },
    closeWaitDialog () {
      this.waitDialogVisable = false
    }
  }
}
</script>

<style lang="less" scoped>
.over-view-card {
  height: 100%;
  position: sticky;
  top: 0;
  width: 20%;
  // background: #fff;
  color: #323338;
  overflow: hidden;
  height: 100%;

  .title-name {
    width: 100%;
    height: 50px;
    padding: 12px 16px;

    font-weight: 600;
    font-size: 16px;
    line-height: 24px;

    flex: none;
    order: 0;
    flex-grow: 0;
  }

  .curriculum-item {
    padding: 12px 16px;
    border-left: 3px solid #fff;
    width: 100%;

    .card-title {
      padding-bottom: 8px;
      text-align: start;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
      //超出自动换行,省略号展示
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .card-content {
      display: flex;

      span {
        margin-right: 8px;
        color: #676879;
      }
    }
  }

  .curriculum-item-selected {
    border-left: 3px solid #10b3b7;
    background: #e7f7f8;
    color: #10b3b7;
    .card-title {
      font-weight: bold;
    }
  }

  .curriculum-apply {
    padding: 2px;
    /deep/ .el-button {
      width: 100%;
    }
  }
}
</style>
