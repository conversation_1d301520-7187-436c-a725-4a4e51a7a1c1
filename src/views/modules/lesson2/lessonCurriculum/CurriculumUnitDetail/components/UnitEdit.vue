<template>
  <div>
    <el-row class="curriculum-editor-content lg-box-shadow"  style="border-radius: 8px 8px 0px 0px;">
      <el-tabs v-model="currentIndex" type="card" @tab-remove="removeUnit" :before-leave="beforeLeave"
        class="lg-tabs">
        <el-tab-pane v-for="unit in units" :key="unit.id" :name="unit.number" :closable="units.length>1 && isEditor">
          <span slot="label" style="padding: 8px;">
            <span>{{ $t('loc.curriculum102', {num: unit.number}) }}</span>
          </span>
          <unit-template :ref="'unitTemplate' + unit.number" :frameworkId="frameworkId" :frameworks="frameworks" :framework-name="frameworkName" :isEditor="isEditor" :curriculumId="curriculumId" :isActive="currentIndex == unit.number" :unit="unit" :domains="domains" :framework-link-url="frameworkLinkUrl" ></unit-template>
        </el-tab-pane>
        <el-tab-pane v-loading="unitLoading" name="add">
          <span slot="label" style="padding: 8PX;font-size: 20PX;font-weight: bold;">
            +
          </span>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
import unitTemplate from './UnitTemplate.vue'
import Lessons2 from '@/api/lessons2'

export default {
  name: 'UnitEdit',
  components: {
    unitTemplate
  },
  props: {
    curriculumId: {
      type: String
    },
    units: {
      type: Array
    },
    domains: {
      type: Array
    },
    isEditor: {
      type: Boolean
    },
    frameworks: {
      type: Array
    },
    frameworkId: {
      type: String
    },
    value: {
      type: Array
    },
    frameworkName: {
      type: String
    },
    frameworkLinkUrl: {
      type: String
    }
  },
  data() {
    return {
      currentIndex: 1,
      unitLoading: false, // unit tab 栏操作
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler (value) {
        if (!this.units || this.units.length <= 0) {
          this.units = value
        }
      }
    },
    units: {
      deep: true,
      handler () {
        this.$emit('input', this.units)
      }
    },
  },
  created () {
  },
  methods: {
    toValidateUnit () {
      this.$refs['unitTemplate' + this.currentIndex][0].toValidateUnit()
    },
    addUnit () {
      if (this.unitLoading) {
        return
      }
      this.unitLoading = true
      let number = this.units.length + 1
      let param = {
        curriculumId: this.curriculumId,
        number: number
      }
      Lessons2.addUnit(param)
        .then(res => {
          this.$analytics.sendEvent('web_curriculum_add_unit')
          this.units.push({
            id: res.id,
            number: number,
          })
          this.currentIndex = number
          this.unitLoading = false
          // 点击添加单元按钮埋点
          if (!this.$route.params.add) {
            this.$analytics.sendEvent('web_curriculum_edit_unit')
          }
        }).catch(error => {
          this.unitLoading = false
          this.$message.error(error)
        })
    },
    removeUnit (targetName) {
      if (this.unitLoading || this.units.length <= 1) {
        return false
      }
      let unitTabs = this.units
      let activeNumber = this.currentIndex
      let targetNumber = Number(targetName)
      console.log('targetNumber', targetNumber)
      // if (this.unitLoading || this.units.length <= 1) {
      //   return false
      // }
      this.unitLoading = true
      this.$confirm(this.$t('loc.curriculum89', { unit: targetNumber }), this.$t('loc.curriculum58'), {
        confirmButtonText: this.$t('loc.curriculum59'),
        cancelButtonText: this.$t('loc.curriculum60'),
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        this.unitLoading = true
        let unitDelete = unitTabs.filter(tab => tab.number == targetNumber)
        let param = {
          unitId: unitDelete[0].id
        }
        Lessons2.deleteUnit(param)
          .then(res => {
            // 删除接口调用成功
            if (res.success && res.id) {
              this.units = unitTabs.filter(tab => tab.number !== targetNumber)
              this.units.forEach((tab, index) => {
                if (tab.number >= targetNumber) {
                  tab.number = tab.number - 1
                }
              })
              if (targetNumber <= activeNumber && this.currentIndex != 1) {
                this.currentIndex = activeNumber - 1
              }
            }
            this.unitLoading = false
          }).catch(error => {
            this.unitLoading = false
            this.$message.error(error)
          })
      })
      .catch(() => {
        this.unitLoading = false
        return false
      })

    },
    /* Unit标签切换时触发 */
    beforeLeave(currentName, oldName) {
      // 如果name是add
      if (currentName == 'add') {
         this.addUnit()
         return false
      } else {
        this.currentIndex = Number(currentName)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.curriculum-editor-content {
  margin: 20px auto 0 auto;
  background-color: #fff;
  width: 1200px;
  padding: 50px 50px 0 50px;
}

//  .my-tab-pane{
//   background: #DDF2F3;
//  }
//  /deep/ .el-tabs__header {
//   padding: 0;
//   position: relative;
//   margin: 0 0 0px;
// }
.el-tabs--card>.el-tabs__header {
  border: none;
}

.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  background: #10b3b7 ;
  color: #FFF ;
  border-radius: 4px ;
}

.el-tabs--card>.el-tabs__header .el-tabs__item {
  color: #10B3B7;
}

.el-tabs--card>.el-tabs__header .el-tabs__nav {
  padding: 5px;
  background: #DDF2F3;
  border-radius: 4px;
  display: flex;
}

.el-tabs__item:hover {
  background: #C9ECED;
}

.el-tabs--top.el-tabs--card>.el-tabs__header .el-tabs__item:last-child {
  padding-right: 10px;
  padding-left: 10px;
}

.el-tabs__content {
  overflow: visible;
}
.curriculum-editor-content /deep/ .el-tabs__nav {
  margin-bottom: 24px;
}
</style>
