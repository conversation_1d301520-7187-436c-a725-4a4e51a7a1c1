<template>
  <div v-loading="!unitLoaded">
    <el-row type="flex" justify="start">
      <el-form :model="unitTemplate" :rules="rules" ref="unitTemplateFromRef" label-width="100px" label-position="top" class="unit-template-from">
        <div class="cover">
          <div>
            <el-form-item :label="$t('loc.title')" prop="title">
              <el-input type="textarea" :placeholder="$t('loc.title')" v-model="unitTemplate.title"
                        :row="3" resize="none" maxlength="200"
                        style="width: 500px"></el-input>
            </el-form-item>
            <el-form-item :label="$t('loc.overview')" prop="description">
              <el-input type="textarea"
                        rows="9"
                        resize="none" :maxlength="2000"
                        :placeholder="$t('loc.curriculum104')"
                        v-model="unitTemplate.description" style="width:500px;max-height: 198px"></el-input>
            </el-form-item>
          </div>
          <div>
            <!--封面-->
            <el-form-item prop="coverMedias" class="lesson-form-cover">
              <media-uploader :validate-event="true"
                              :showUrlBtn="true" typeDistinguish="coverMedia" v-model="unitTemplate.coverMedias"
                              class="media-uploader-lesson-cover unit-template-cove"
                              :tips="$t('loc.lesson2NewLessonFormPlaceHolderCover')">
                <div slot="tips">
                  <ul style="text-align: left;padding: 0 32px;">
                    <li style="list-style:disc;">{{ $t('loc.coverExternalMediaUploadSize') }}</li>
                    <li style="list-style:disc;">{{ $t('loc.curriculum105') }}</li>
                <li style="list-style:disc;">{{ $t('loc.curriculum106') }}</li>
                  </ul>
                </div>
              </media-uploader>
            </el-form-item>
          </div>
        </div>
        <!-- 引导问题 -->
        <el-form-item :label="$t('loc.unitaddress1')" prop="guidingQuestions" style="margin-top: 22px;">
          <el-tooltip placement="top-end" effect="light" popper-class="atooltip">
            <div slot="content">
              <img width="600" src="@/assets/img/lesson2/curriculum/guiding_question.png">
            </div>
            <el-button type="text" size="small" class="sample-button">{{ $t('loc.viewsample') }}</el-button>
          </el-tooltip>
          <Editor :validate-event="true" :placeholder="$t('loc.unitaddress1')" v-model="unitTemplate.guidingQuestions"></Editor>
        </el-form-item>
        <el-row>
          <div>
            <WeeklyInfo :frameworkId="frameworkId" :frameworks="frameworks" :framework-name="frameworkName"
                        :curriculumId="curriculumId" :unitId="unitTemplate.id" :plans="unitTemplate.plans"
                        :isEditor="isEditor" :unit-num="unitTemplate.number" :framework-link-url="frameworkLinkUrl"></WeeklyInfo>
          </div>
        </el-row>
        <!-- 单元轨迹 -->
        <el-form-item :label="$t('loc.trajectory')" prop="trajectory" style="margin-top: 22px;">
          <el-tooltip placement="top-end" effect="light" popper-class="atooltip">
            <div slot="content">
            <img width="600" src="@/assets/img/lesson2/curriculum/unit_trajectory.png">
            </div>
            <el-button type="text" size="small" class="sample-button">{{ $t('loc.viewsample') }}</el-button>
          </el-tooltip>
          <Editor :validate-event="true" :placeholder="$t('loc.trajectory')" v-model="unitTemplate.trajectory"></Editor>
        </el-form-item>
        <!-- 单元概念 -->
        <el-form-item :label="$t('loc.concepts')" prop="concepts" style="margin-top: 22px;">
          <el-tooltip placement="top-end" effect="light" popper-class="atooltip">
            <div slot="content">
              <img width="600" src="@/assets/img/lesson2/curriculum/concepts.png">
            </div>
            <el-button type="text" size="small" class="sample-button">{{ $t('loc.viewsample') }}</el-button>
          </el-tooltip>
          <Editor :validate-event="true" :placeholder="$t('loc.concepts')" v-model="unitTemplate.concepts"></Editor>
        </el-form-item>
      </el-form>
    </el-row>
  </div>
</template>

<script>
import tools from '@/utils/tools'
import MediaUploader from '../../components/MediaUploader/index.vue'
import DomainsTableEditor from './DomainsTableEditor.vue'
import Editor from './Editor.vue'
import WeeklyInfo from './WeeklyInfo.vue'
// import ResourceInfo from './ResourceInfo.vue'
import Lessons2 from '@/api/lessons2'

export default {
  name: 'UnitTemplate',
  components: { Editor, MediaUploader, DomainsTableEditor, WeeklyInfo },
  props: {
    curriculumId: {
      type: String
    },
    unit: {
      type: Object
    },
    domains: {
      type: Array
    },
    isEditor: {
      type: Boolean
    },
    isActive: {
      type: Boolean,
      default: false
    },
    frameworks: {
      type: Array
    },
    frameworkId: {
      type: String
    },
    frameworkName: {
      type: String
    },
    frameworkLinkUrl: {
      type: String
    }
  },
  inject: ['saveFun'],
  watch: {
    isActive: {
      immediate: true,
      handler () {
        // 选中时加载单元详情
        if (!this.unitLoaded && this.isActive && this.unit) {
          this.loadUnitDetail(this.unit.id)
        }
      }
    }
  },
  data () {
    let checkCover = (rule, value, callback) => {
      // 若没有图片、视频和外部链接，则进行提示
      if (value === null && this.unitTemplate.coverMedias === null) {
        return callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }

    return {
      unitTemplate: {
        id: '',
        title: '',
        number: '',
        coverMedias: null,
        description: '',
        trajectory: '',
        concepts: '',
        guidingQuestions: '',
        plans: []
      },
      unitLoaded: false, // 单元信息是否被加载
      isEdited: false,
      coverMedia: null,
      rules: {
        title: [
          { required: true, message: this.$t('loc.fieldReq') }
        ],
        description: [
          { required: true, message: this.$t('loc.fieldReq') }
        ],
        coverMedias: [{
          validator: checkCover,type: 'object'
        }],
        trajectory: [
          {
            message: this.$t('loc.fieldReq'),
            required: true,
            fields: {
              description: { message: this.$t('loc.fieldReq'), required: true, whitespace: true }
            }
          }
        ],
        concepts: [
          {
            message: this.$t('loc.fieldReq'),
            required: true,
            whitespace: true
          }
        ],
        guidingQuestions: [
          {
            message: this.$t('loc.fieldReq'),
            required: true,
            fields: {
              description: { message: this.$t('loc.fieldReq'), required: true, whitespace: true }
            }
          }
        ]
      }
    }
  },
  methods: {
    async toValidateUnit () {
      try {
        await this.$refs.unitTemplateFromRef.validate()
      } catch (err) {
        let formItemsWithError = this.$el.querySelectorAll('.is-error')
        let firstItem = formItemsWithError[0]
        if (firstItem) {
          firstItem.dispatchEvent(new CustomEvent('validate-failed', {}))
          firstItem.scrollIntoView(true)
        }
      }
    },
    // 自动保存单元内容
    autoSave: tools.debounce(function () {
      let params = this.getUnitBaseInfo()
      if (!params || !params.unitId) {
        return
      }
      Lessons2.updateUnit(params).then(res => {
        this.saveFun(false)
      }).catch(error => {
        this.saveFun(false)
        this.$message.error(error)
      })
    }, 2000),
    // 加载单元详情
    loadUnitDetail (unitId) {
      if (!unitId || !this.isActive) {
        return
      }
      Lessons2.getUnitDetail({ unitId: unitId }).then(res => {
        this.unitLoaded = true
        if (res.coverMedias && res.coverMedias.length > 0) {
          this.coverMedia = res.coverMedias[0]
        }
        this.unitTemplate = {
          id: res.id,
          title: res.title || '',
          number: res.number || '',
          coverMedias: this.coverMedia || null,
          description: res.overview || '',
          trajectory: res.trajectory || '',
          concepts: res.concepts || '',
          guidingQuestions: res.guidingQuestions || '',
          plans: res.plans || []
        }
        this.$nextTick(() => {
          let ref = this.$refs['unitTemplateFromRef']
          ref && ref.clearValidate()
          // 监听系列单元对象，自动保存
          this.$watch('unitTemplate', {
            deep: true,
            handler () {
              this.saveFun(true)
              this.autoSave()
            }
          })
        })
      }).catch(error => {
        this.$message.error(error)
      })
    },
    // 收集单元基本信息
    getUnitBaseInfo () {
      if (!this.unitTemplate || !this.unitTemplate.id || !this.unitTemplate.number) {
        return null
      }
      let id = ''
      let type = ''
      let coverMedias = []
      if (this.unitTemplate.coverMedias && this.unitTemplate.coverMedias.externalMediaUrlId) {
        id = this.unitTemplate.coverMedias.externalMediaUrlId
        type = 'EXTERNAL'
      }
      if (this.unitTemplate.coverMedias && this.unitTemplate.coverMedias.id) {
        id = this.unitTemplate.coverMedias.id
        type = 'UPLOAD'
      }
      if (this.unitTemplate.coverMedias && this.unitTemplate.coverMedias.type === 'externalMedia') {
        id = this.unitTemplate.coverMedias.id
        type = 'EXTERNAL'
      }
      if (id && type) {
        coverMedias = [{
          id: id,
          type: type
        }]
      }
      let param = {
        unitId: this.unitTemplate.id,
        coverMedias: coverMedias, // 单元封面
        overview: this.unitTemplate.description,
        guidingQuestions: this.unitTemplate.guidingQuestions, // 单元引导问题
        number: this.unitTemplate.number,
        title: this.unitTemplate.title,
        trajectory: this.unitTemplate.trajectory, // 单元轨迹
        concepts: this.unitTemplate.concepts // 单元概念
      }
      return param
    }
  }
}
</script>

<style lang="less" scoped>
.cover {
  display: flex;
  justify-content: space-between;
}

lesson-form-cover {
  width: 640px;
  height: 340px;
  float: left;
  margin-right: 20px;
}

.sample-button {
  float: right;
  margin-top: -40px;
  padding: 0 16px !important;
  background: #F5F6F8;
  border-radius: 4px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  color: #676879;
}

.new-lesson-operation {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 20px;
}
.unit-template-from {
  width: 1100px;
}
/deep/ .el-form-item__label {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  padding: 0;
}

@media only screen and (min-width: 1200px) {
  //web
  /deep/ .media-uploader-lesson-cover {
    width: 550px !important;
    height: 350px !important;
  }
  /deep/ .media-uploader-lesson-cover .media-uploader-select {
    width: 550px !important;
    height: 310px !important;
    margin-top: 0 !important;
  }
  /deep/ .media-uploader-lesson-cover .media-uploader {
    width: 550px !important;
    height: 310px !important;
    margin-top: 30px ;
  }
  /deep/ .media-uploader-lesson-cover .media-uploader-selected {
    width: 550px !important;
    height: 310px !important;
  }
}

.is-error /deep/ .domain-table {

}
</style>
<style>
.atooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow {
  border-top-color: white;
}

.atooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow:after {
  border-top-color: white;
}

.el-tooltip__popper.is-light[x-placement^=bottom] .popper__arrow {
  border-bottom-color: white;
}
.el-tooltip__popper.is-light[x-placement^=bottom] .popper__arrow:after {
  border-bottom-color: white;
}

.atooltip {
  padding: 0 !important;
  background: white !important;
  border-color: white !important;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}
</style>
