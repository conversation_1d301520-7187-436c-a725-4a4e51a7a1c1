<template>
  <div style="height: 100%;width: 100%;" v-loading="isLoading" id="unit-planner-table" class="unit-planner-table">
    <el-table v-show="isShowTable" v-if="unitPlanners.length > 0" :data="unitPlannersFormatted"
              border
              :height="tableHeight"
              ref="planner-table"
              :cell-class-name="categoryCellClassName" header-row-class-name="table-header"
              >
      <el-table-column fixed label="" prop="categoryName" width="130">
      </el-table-column>
      <template v-for="week in getAllWeeks">
        <el-table-column
          :prop="week.toString()"
          :label="$t('loc.curriculum20',{ num: week })"
          :width='getAllWeeks.length > 4 && "200px"'>
          <template slot-scope="scope">
            <template v-for="item in scope.row[week]">
              <div class="unit-planner-table-call-plan-item" v-if="!item.centerId">
                <plan-item :overView="true"
                           :item="item"
                           :type="'CENTER_ROW'"
                           :edit="false"
                           :isTemplate="false"
                           :long="true">
                </plan-item>
              </div>
              <el-card v-if="item.centerId" style="width: 100%" :class="'box-card-' + item.planCenter.colorIndex" class="lg-margin-top-12">
                <div
                  style="font-weight: bold;font-size: 14px;padding: 0"
                  slot="header"
                  class="clearfix display-flex">
                  <div style="text-align: start;word-break: normal">{{ item.planCenter.name }}</div>
                </div>
                <div class="unit-planner-table-call-plan-item">
                  <plan-item
                    :overView="true"
                    :item="item"
                    :type="'CENTER_ROW'"
                    :edit="false"
                    :isTemplate="false"
                    :long="true">
                  </plan-item>
                </div>
              </el-card>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <!--  空页面  -->
    <div class="flex-column-center" style="height: 100%;" v-if="!isLoading && unitPlanners.length <= 0">
      <img src="@/assets/img/dll/pic_empty_dll.png" style="margin-top: -20px">
      <div class="text-center add-margin-t-8 font-size-14 " >
        {{$t('loc.plan36')}}
      </div>
    </div>
  </div>
</template>

<script>
import PlanItem from '@/views/modules/lesson2/lessonPlan/components/PlanItem'

export default {
  name: 'UnitPlannerOverview',
  components: { PlanItem },
  props: {
    unitPlanners: {
      type: Array
    },
    isLoading: {
      type: Boolean
    }
  },
  data () {
    return {
      unitPlannersFormatted: [],
      tableHeight: 0,
      isShowTable: true
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.doLayout()
    })
    this.calHeight()
    // 监听窗口变化，计算页面高度
    window.addEventListener('resize', () => {
      this.calHeight()
      this.$nextTick(() => {
        this.doLayout()
      })
    })
  },
  watch: {
    unitPlanners: {
      deep: true,
      immediate: true,
      handler () {
        this.formatUnitPlanners()
      }
    }
  },
  computed: {
    // 获取所有周计划的周次
    getOtherUnitPlanners () {
      let otherPlanners = this.unitPlanners.filter(item => (item.planItems && item.planItems.length > 0 && item.categoryId))
      return otherPlanners
    },
    getAllWeeks () {
      let weeks = []
      weeks = this.unitPlanners.map(item => item.week)
      return weeks.filter((item, index) => {
        return weeks.indexOf(item) === index
      }).sort((a, b) => a - b)
    },
    getOtherUnitPlannersGroupByCategory () {
      let planners = this.getOtherUnitPlanners.reduce((group, item) => {
        let name = item.categoryName || ''
        group[name] = group[name] || []
        group[name].push(item)
        return group
      }, {})
      let res = []
      for (const key in planners) {
        res.push({
          categoryName: key,
          planners: planners[key]
        })
      }
      return res
    }
  },
  methods: {
    calHeight: function () {
      this.isShowTable = false
      this.$nextTick(() => {
        let dom = document.getElementById('unit-planner-table')
        if (dom) {
          this.tableHeight = dom.offsetHeight
          this.isShowTable = true
        }
      })
    },
    formatUnitPlanners () {
      let unitPlannersFormattedTemp = []
      if (this.unitPlanners && this.unitPlanners.length > 0) {
        let weeks = this.getAllWeeks
        let otherUnitPlannersGroupByCategory = this.getOtherUnitPlannersGroupByCategory
        // 设置 非 Centers 组的周计划
        otherUnitPlannersGroupByCategory.forEach(unitPlannersCategory => {
          let temp = {}
          temp['categoryName'] = unitPlannersCategory.categoryName || ''
          for (let i = 0; i < weeks.length; i++) {
            let week = weeks[i]
            temp[week] = unitPlannersCategory.planners.filter(item => item.week === week).flatMap(planner => planner.planItems)
          }
          unitPlannersFormattedTemp.push(temp)
        })
        // // 设置 Centers 组的周计划
        // let centerUnitPlanners = this.getCenterUnitPlanners
        // if (centerUnitPlanners.length > 0) {
        //   let temp = {}
        //   temp['categoryName'] = 'Centers'
        //   for (let i = 0; i < weeks.length; i++) {
        //     let week = weeks[i]
        //     plannerItems = []
        //     this.getItemsByWeek(centerUnitPlanners, week).forEach(planner => {
        //       plannerItems = plannerItems.concat(planner.planItems)
        //     })
        //     temp[week] = plannerItems
        //   }
        //   unitPlannersFormattedTemp.push(temp)
        // }
      }
      this.unitPlannersFormatted = unitPlannersFormattedTemp
      this.$nextTick(() => {
        this.calHeight()
      })
    },
    categoryCellClassName ({ row, column, rowIndex, columnIndex }) {
      let classNames = 'unit-planner-table-call'
      if (columnIndex === 0) {
        classNames = 'table-call-header'
        return classNames
      }
      return classNames
    },
    // el-table 对齐,重新布局
    doLayout () {
      if (this.$refs['planner-table']) {
        this.$refs['planner-table'].doLayout()
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .table-header th {
  background-color: #ecf9ff;
  white-space: nowrap;
  vertical-align: middle;
  font-weight: bold;
  color: #323338;
  text-align: center;
}
/deep/ .el-table__cell {
  border: 1px solid #DCDFE6;
  vertical-align: top;
}
/deep/ .table-call-header {
  background-color: #ecf9ff;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle !important;
  font-weight: bold;
  color: #323338;
}

/deep/ :hover .table-call-header {
  background-color: #ecf9ff !important;
  color: #323338 !important;
}
/deep/ :hover .unit-planner-table-call {
  background-color: #fff!important;
}
/deep/ :hover .unit-planner-table {
  background-color: #fff!important;
}

// /deep/ .el-table__body-wrapper {
//   &::-webkit-scrollbar {
//     /* 滚动条整体样式 */
//     width: 8px; /* 高宽分别对应横竖滚动条的尺寸 */
//     height: 8px;
//   }

//   &::-webkit-scrollbar-thumb {
//     /* 滚动条里面小方块 */
//     border-radius: 10px;
//     box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//     background: RGBA(182, 182, 182, 0.45);
//   }

//   &::-webkit-scrollbar-track {
//     /* 滚动条里面轨道 */
//     box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//     border-radius: 10px;
//     background: #ededed;
//   }
// }
.box-card-1 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #D0D7F0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #D0D7F0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-2 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #E8D0F0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #E8D0F0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-3 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F0D0D0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F0D0D0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-4 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F0DCD0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F0DCD0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-5 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F0E9D0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F0E9D0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-6 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #DEF0D0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #DEF0D0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-7 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #D0EBF0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #D0EBF0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-8 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #C0E1F3 !important;
  }

  /deep/ .el-input__inner {
    background-color: #C0E1F3;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-9 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #D1D0F0 !important;
  }

  /deep/ .el-input__inner {
    background-color: #D1D0F0;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}

.box-card-10 {
  /deep/ .el-card__header {
    padding: 5px 20px !important;
    background-color: #F7CBE8 !important;
  }

  /deep/ .el-input__inner {
    background-color: #F7CBE8;
    height: 22px;
    color: black;
    font-weight: bold;
    font-size: 16px;
    border: 1px dashed #999999;;
  }
}
.unit-planner-table-call-plan-item {
  margin-top: 12px;
}
</style>
