<template>
   <div class="week-edit-div">
      <el-row class="curriculum-editor-content">
        <el-col :span="!isEditor && published() && !planRecording ? 18 : 24">
          <el-tabs ref="weeksTab" v-model="editableTabsValue" type="card" @tab-remove="removeTab" :before-leave="beforeLeave" class="lg-tabs week-edit">
            <el-tab-pane :lazy="true" v-for="(item,index) in weeks" :key="index" :label="item.title" :name="item.name" :closable="weeks.length>1 && isEditor">
              <span slot="label" style="padding: 8px;">
                <span>{{item.title}}</span>
              </span>
            </el-tab-pane>
            <el-tab-pane v-if="isEditor" key="add" name="add">
                <span slot="label" style="padding: 8px;font-size:20px;font-weight:bold;">                  +
                </span>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="6">
          <div v-if="!isEditor && published() && !planRecording" class="display-flex justify-content-end">
            <curriculum-apply v-if="!isFromUnitDetail" :curriculum="curriculum" source="weeklyPlan" :lessionTitle="$t('loc.curriculum3')"></curriculum-apply>
              <div v-if="showApply && !isMC" class="display-flex align-items" style="gap: 10px">
                <UnitApply ref="unitApply" :unit="unit" applyType="weekly" v-if="false"/>
                <div v-if="!isCurriculumPlugin">
                  <el-popover
                    placement="bottom"
                    width="150"
                    v-model="pdfPopoverVisible"
                    trigger="click">
                    <!-- PDF 下载和打印 -->
                    <div class="act_div_wapper">
                      <el-link
                        :underline="false"
                        @click="generatePDF(false)">
                        <i class="font-size-24 lg-icon lg-icon-download lg-margin-right-16"></i>
                        <span>{{$t('loc.download')}}</span>
                      </el-link>
                    </div>
                    <div class="act_div_wapper">
                      <el-link
                        :underline="false"
                        @click="generatePDF(true)">
                        <i class="font-size-24 lg-icon lg-icon-print lg-margin-right-16"></i>
                        <span>{{$t('loc.plan174')}}</span>
                      </el-link>
                    </div>
                    <el-button slot="reference" plain class="btn-style" icon="lg-icon lg-icon-more-horizontal" :loading="pdfLoading" @click="pdfPopoverVisible = !pdfPopoverVisible">
                    </el-button>
                  </el-popover>
                </div>
                <!-- <div class="btn-center" v-else>
                  <el-button type="primary" :loading="pdfLoading" class="btn-style" @click="generatePDF(false)"
                             >Download</el-button>
                  <el-button type="primary" :loading="pdfLoading" class="btn-style" @click="generatePDF(true)"
                  >Print</el-button>
                </div> -->
              </div>
            <el-button v-if="false" class="m-l-sm" icon="fa fa-file-pdf-o">PDF</el-button>
          </div>
        </el-col>
        <template v-for="(currentWeek,index) in weeks">
          <week-plan-selector
            v-if="loadedWeekIndices.includes(index)"
            v-show="index === currentWeekIndex"
            :isOnlyView="isOnlyView"
            :key="index"
            :showApply="showApply"
            :show="index === currentWeekIndex"
            :index="index"
            :planId="currentWeek && currentWeek.planId"
            :isEditor="isEditor"
            :week="currentWeek"
            :adaptedGroupName="groupName"
            :frameworkId="frameworkId" :frameworks="frameworks"
            :framework-name="frameworkName"
            :framework-link-url="frameworkLinkUrl"
            :framework-data="frameworkData"
            @callAddWeekPlan="addWeekPlan"
            @callAdaptUnitWeekPlan="callAdaptUnitWeekPlan"
            @callEditUnitWeekPlan="callEditUnitWeekPlan"
            :isFromUnitDetail="isFromUnitDetail"
            :exemplar="exemplar"
            ref="weekPlanSelector"
        ></week-plan-selector>
        </template>
      </el-row>
    </div>
</template>

<script>
import WeekPlanSelector from '@/views/modules/lesson2/lessonCurriculum/components/WeekPlanSelector'
import UnitApply from '@/views/modules/lesson2/unitPlanner/components/unitApply/UnitApply.vue'
import LessonApi from '@/api/lessons2'
import CurriculumApply from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumApply'
import LessonEditor from '@/views/modules/lesson2/lessonBatchAdapter/LessonEditor.vue'
import AdaptUnitTips from '@/views/modules/lesson2/unitPlanner/components/adaptUnits/AdaptUnitTips.vue'
import { mapState } from 'vuex'
import pdf from '@/assets/img/file/pdf.png'
import file from '@/assets/img/mediaForm/file.png'
import tools from '@/utils/tools'
export default {
  name: 'WeekEdit',
  components: {
    WeekPlanSelector,
    CurriculumApply,
    UnitApply,
    LessonEditor,
    AdaptUnitTips
  },
  props: {
    curriculumId: {
      type: String
    },
    curriculumType: {
      type: String
    },
    unitId: {
      type: String
    },
    lessionTitle: {
      type: String
    },
    plans: {
      type: Array,
      default: function () {
        return []
      }
    },
    isEditor: {
      type: Boolean
    },
    unitNum: {
      type: Number
    },
    unitTitle: {
      type: String
    },
    frameworks: {
      type: Array
    },
    frameworkId: {
      type: String
    },
    frameworkName: {
      type: String
    },
    frameworkLinkUrl: {
      type: String
    },
    isFromUnitDetail: {
      type: Boolean,
      default: false
    },
    showApply: {
      type: Boolean,
      default: false
    },
    exemplar: {
      type: Boolean,
      default: false
    },
    isOnlyView: {
      type: Boolean,
      default: false
    },
    groupName: {
      type: String,
      default: ''
    }
  },
  inject: ['isPublished'],
  data () {
    return {
      currentWeekIndex: 0,
      editableTabsValue: '1',
      currentIndex: 1,
      tabIndex: 1,
      addIndex: 1,
      weeks: [],
      curriculum: {},
      pdfPopoverVisible: false, // PDF 下载和打印弹窗
      pdfLoading: false, // PDF 下载和打印 loading
      // Week常量
      weekConst: this.$t('loc.weekName') + ' ',
      unit: null, // unit 单元信息
      frameworkData: [], // 存储框架测评点数据
      loadedWeekIndices: [0] // 已经被创建过的 Week 组件索引
    }
  },
  created () {
    // 初始化weeks，即 向weeks中添加一个默认的week实体，名字为week1
    // this.initWeeks()
    // 如果是从单元详情页跳转过来的，默认选择第一周
    if (this.isFromUnitDetail) {
      this.editableTabsValue = '0'
    }
    // 初始化 Unit 单元详情信息
    this.unit = {
      id: this.unitId,
      title: this.unitTitle,
      plans: this.plans
    }
  },
  mounted () {
    this.$nextTick(() => {
      // 从课程详情页跳转过来的时候，需要根据 weekNum 来切换 tab
      if (this.$route.params.weekNum) {
        this.editableTabsValue = this.$route.params.weekNum.toString()
        this.currentIndex = this.$route.params.weekNum
        this.$emit('changeWeek', { week: this.editableTabsValue , scroll: true })
      }
    })
  },
  methods: {
    // 预先获取框架测评点数据
    preloadFrameworkMeasures(frameworkId) {
      if (!frameworkId) {
        return
      }
      // 如果 frameworkData 中有该框架，则不重复加载
      if (this.frameworkData && this.frameworkData.length > 0 && this.frameworkData[0].id === frameworkId) {
        return
      }
      LessonApi.getFrameworkMeasures({
        frameworkId: frameworkId
      })
      .then(res => {
        this.frameworkData = this.handleFrameworkData(res)
      })
    },
    // 处理框架数据，将框架的所有叶子节点提取到一级 domain 中，适配 plan item 组件中的数据结构
    handleFrameworkData(data) {
      // 遍历每个 framework
      return data.frameworks.map(framework => {
        const newFramework = {
          ...framework,
          child_domains: framework.child_domains.map(domain => {
            // 创建一级领域对象
            const newDomain = {
              ...domain,
              child_domains: []
            }

            // 使用迭代而不是递归来收集叶子节点
            const queue = [...domain.child_domains]
            let head = 0 // 使用指针追踪队列头部
            
            while (head < queue.length) {
              const node = queue[head] // O(1) 访问队列头部
              head++ // 移动指针到下一个位置
              
              if (node.child_domains && node.child_domains.length > 0) {
                // 将子节点加入队列尾部继续处理
                queue.push(...node.child_domains)
              } else {
                // 叶子节点直接加入结果中
                newDomain.child_domains.push(node)
              }
            }

            return newDomain
          })
        }
        return newFramework
      })
    },
    addTab () {
      let newTabIndex = ++this.tabIndex + ''
      let week = {
        title: this.$t('loc.curriculum103', { num: ++this.addIndex }),
        name: newTabIndex,
        planId: undefined,
        id: undefined,
        curriculumId: this.curriculumId,
        unitId: this.unitId,
        number: this.addIndex,
        unitNum: this.unitNum
      }
      let params = {
        curriculumId: this.curriculumId,
        unitId: this.unitId,
        number: this.addIndex
      }
      LessonApi.addCurriculumUnitWeek(params).then(res => {
        this.$analytics.sendEvent('web_curriculum_add_week')
        week.id = res.id
        this.weeks.push(week)
        this.currentWeekIndex = (this.addIndex - 1)
        // 点击添加周按钮埋点
        if (!this.$route.params.add) {
          this.$analytics.sendEvent('web_curriculum_edit_week')
        }
      }).catch(error => {})
      this.editableTabsValue = newTabIndex
      this.currentIndex = newTabIndex
    },
    removeTab (targetName) {
      var self = this
      if (this.weeks.length <= 1) {
        return false
      }
      let tabs = self.weeks
      let activeName = self.editableTabsValue
      tabs.forEach((tab, index) => {
        if (tab.name === targetName) {
          let params = {
            id: tab.id,
            unitId: this.unitId
          }
          if (tab.planId) {
            let weekindex = tab.title.match(/\d+/g)[0]
            this.$confirm(this.$t('loc.curriculum61', { week: weekindex }), this.$t('loc.curriculum58'), {
              confirmButtonText: this.$t('loc.curriculum59'),
              cancelButtonText: this.$t('loc.curriculum60'),
              confirmButtonClass: 'el-button--danger'
            }).then(() => {
              let nextTab = tabs[index + 1] || tabs[index - 1]
              if (nextTab) {
                self.currentWeekIndex = index
                activeName = nextTab.name
              }
              this.deleteWeekPlan(params)
              this.editableTabsValue = activeName
              this.weeks = tabs.filter(tab => tab.name !== targetName)
              this.weeks.map((tab, index) => {
                tab.title = this.$t('loc.curriculum103', { num: index + 1 })
                self.addIndex = (index + 1)
              })
              self.currentIndex = self.editableTabsValue
              tab.title = this.$t('loc.curriculum103', { num: self.weeks.length })
            }).catch(() => {
            })
          } else {
            let nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              self.currentWeekIndex = index
              activeName = nextTab.name
            }
            this.deleteWeekPlan(params)
            this.editableTabsValue = activeName
            this.weeks = tabs.filter(tab => tab.name !== targetName)
            this.weeks.map((tab, index) => {
              tab.title = this.$t('loc.curriculum103', { num: index + 1 })
              self.addIndex = (index + 1)
            })
            self.currentIndex = self.editableTabsValue
            tab.title = this.$t('loc.curriculum103', { num: self.weeks.length })
          }
        }
      })
    },
    beforeLeave (currentName, oldName) {
      // 正在录制周计划讲解，禁止切换周次
      if (this.planRecording) {
        this.$message.warning(this.$t('loc.plan145'))
        return false
      }
      var self = this
      // 重点，如果name是add，则什么都不触发
      if(currentName === 'add') {
        this.addTab()
        return false
      } else {
        // this.$refs.weekPlanSelector.getPlan()
        this.currentIndex = currentName
        // 切换周次时将路由的weekNum参数改为当前周次
        this.$router.replace({
          params: {
            weekNum: currentName
          },
          query: this.$route.query
        })
        // 离开当前周计划时，暂停播放周计划讲解
        let planInterpretationAudio = window.planInterpretationAudio
        if (planInterpretationAudio && !planInterpretationAudio.paused) {
          planInterpretationAudio.pause()
        }
      }
    },
    callAdaptUnitWeekPlan (item) {
      this.$emit('callAdaptUnitWeekPlan', item, true)
    },
    callEditUnitWeekPlan (item) {
      this.$emit('callEditUnitWeekPlan', item)
    },
    addWeekPlan (planId, id, index) {
      let week = this.weeks.filter(x => x.id === id)
      if (planId) {
        let params = {
          id: id,
          curriculumId: this.curriculumId,
          planId: planId,
          unitId: this.unitId,
          number: week[0].number,
          unitNum: this.unitNum
        }
        if (this.weeks[index].planId) {
          this.$confirm(this.$t('loc.curriculum57'), this.$t('loc.curriculum58'), {
            confirmButtonText: this.$t('loc.curriculum59'),
            cancelButtonText: this.$t('loc.curriculum60')
          }).then(() => {
            this.weeks[index].planId = planId
            LessonApi.updateCurriculumUnitWeek(params).then(x => {
            }).catch(error => {})
          }).catch(() => {})
          } else {
          this.weeks[index].planId = planId
          LessonApi.updateCurriculumUnitWeek(params).then(x => {
          }).catch(error => {})
        }
      } else {
        let param = {}
        if (this.weeks[index].planId) {
          this.$confirm(this.$t('loc.curriculum57'), this.$t('loc.curriculum58'), {
            confirmButtonText: this.$t('loc.curriculum59'),
            cancelButtonText: this.$t('loc.curriculum60')
          }).then(() => {
            LessonApi.createCurriculumWeekPlan({},{
              frameworkId: this.frameworkId
            })
              .then(res => {
                this.weeks[index].planId = res.id
                let params = {
                  id: id,
                  curriculumId: this.curriculumId,
                  planId: res.id,
                  unitId: this.unitId,
                  number: week[0].number,
                  unitNum: this.unitNum
                }
                LessonApi.updateCurriculumUnitWeek(params).then(x => {
                }).catch(error => {})
              }).catch(error => {})
          }).catch(() => {
          });
        } else {
          LessonApi.createCurriculumWeekPlan({},{
            frameworkId: this.frameworkId
          })
            .then(res => {
              this.weeks[index].planId = res.id
              let params = {
                id: id,
                curriculumId: this.curriculumId,
                planId: res.id,
                unitId: this.unitId,
                number: week[0].number,
                unitNum: this.unitNum
              }
              LessonApi.updateCurriculumUnitWeek(params).then(x => {
              }).catch(error => {})
            }).catch(error => {})
        }
      }
    },
    deleteWeekPlan (params) {
      LessonApi.deleteCurriculumUnitWeek(params).then(x => {
      }).catch(error => {})
    },
    initWeeks () {
      this.weeks = []
      this.plans.forEach(x => {
        this.weeks.push({
          id: x.weekId,
          title: this.$t('loc.curriculum103', { num: this.isFromUnitDetail ? x.number + 1 : x.number }),
          name: x.number.toString(),
          planId: x.planId,
          generatedMaterials: x.generatedMaterials,
          curriculumId: this.curriculumId,
          unitId: this.unitId,
          number: x.number,
          unitNum: this.unitNum
        })
      })
      this.currentIndex = 1
      this.tabIndex = this.weeks.length
      this.addIndex = this.weeks.length
      
      // 确保当前周及相邻周被加入已加载索引
      this.$nextTick(() => {
        this.addLoadedIndex(this.currentWeekIndex)
        this.addLoadedIndex(this.currentWeekIndex - 1)
        this.addLoadedIndex(this.currentWeekIndex + 1)
      })
    },
    initCurriculum () {
      this.curriculum = {
        id: this.curriculumId,
        units: [
          {
            id: this.unitId,
            title: this.unitTitle,
            plans: this.plans,
            number: this.unitNum
          }
        ]
      }
    },
    // 生成 PDF
    generatePDF (print) {
      // 如果 PDF 正在生成，则不重复生成
      if (this.pdfLoading) {
        return
      }
      if (print) {
        this.$analytics.sendEvent('cg_unit_preview_weeks_print')
      } else {
        this.$analytics.sendEvent('cg_unit_preview_weeks_download')
      }
      this.pdfPopoverVisible = false
      const currentWeek = this.weeks[this.currentWeekIndex]
      // 火狐浏览器不支持打印
      if (print && tools.isFirefox()) {
        this.$alert(this.$t('loc.plan176'), this.$t('loc.plan135'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('loc.download'),
          customClass: 'el-message-box-font',
          callback: action => {
            if (action === 'confirm') {
              this.generatePDF(false)
            }
          }
        })
        return
      }
      this.pdfLoading = true
      LessonApi.getPlanPDF({
        planId: currentWeek.planId,
        showCore: false,
        aiGenerated: this.isFromUnitDetail,
        ...(!this.isSameLanguage && { langCode: this.contentLanguage })
      }).then(response => {
        this.getPlanPDF(print)
      }).catch(error => {})
    },
    /**
     * 获取周计划 PDF
     */
    getPlanPDF (print) {
      LessonApi.getPDFList({
        type: 'WEEKLY_PLAN'
      }).then(response => {
        if (response && response.length > 0) {
          let pdf = response[0]
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getPlanPDF(print)
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED' && print) {
            this.$print({
              printable: pdf.pdfUrl,
              type: 'pdf',
              onLoadingStart: () => {
              },
              onLoadingEnd: () => {
                this.pdfLoading = false
              },
              onError: () => {
                this.downloadPDFWithAlert(pdf)
                this.pdfLoading = false
              }
            })
          } else {
            this.downloadPDFWithAlert(pdf)
            this.pdfLoading = false
          }
        }
      })
    },
    // 去除 PDF 后缀
    removePDFSuffix (pdfName) {
      if (pdfName.trim() === '') {
        return ''
      }
      return pdfName.replace(/\.pdf$/i, '') + '_' + this.$moment(new Date().getTime()).format('DD-MM-YYYY')
    },
    /**
     * 下载 PDF
     */
    downloadPDFWithAlert (pdf) {
      // 成功弹窗下载
      this.$alert('<div class="display-flex align-items"><img style="height: 30px; margin: 5px" src="' + this.getfilePhoto(pdf.pdfName) + '"> <span  title="' + this.removePDFSuffix(pdf.pdfName) + '">' + this.removePDFSuffix(pdf.pdfName) + '</span></div>', this.$t('loc.plan61'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download'),
        customClass: 'el-message-box-font',
        callback: action => {
          if (action === 'confirm') {
            this.$analytics.sendEvent('web_weekly_plan_manage_click_pop_download')
            if (tools.isComeFromIPad()) {
              let requestData = {
                'emailTemplate': 'weekly_lesson_planning',
                'downloadFileUrl': pdf.pdfUrl,
                'fileName': pdf.pdfName,
                'week': this.planData.week,
                'className': this.planData.groupName,
                'siteName': this.planData.centerName,
                'fromDate': this.planData.fromAtLocal,
                'toDate': this.planData.toAtLocal,
                'courseName': this.planData.theme
              }
              this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
                .then(() => {
                  this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                    confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                    showCancelButton: false
                  })
                }).catch(error => {
                this.$message.error(error.message)
              })
            } else {
              const eleLink = document.createElement('a')
              eleLink.style.display = 'none'
              eleLink.href = pdf.pdfUrl
              // 触发点击
              document.body.appendChild(eleLink)
              eleLink.click()
              // 移除
              document.body.removeChild(eleLink)
            }
          }
        }
      })
    },

    /**
     * 获取文件类型 icon
     */
    getfilePhoto (fileName) {
      if (fileName.endsWith('pdf')) {
        return pdf
      }
      return file
    },
    /**
     * 将周索引加入已加载集合
     * 
     * @param index 周索引
     */
    addLoadedIndex(index) {
      if (index < 0 || index >= this.weeks.length) {
        return
      }
      if (!this.loadedWeekIndices.includes(index)) {
        this.loadedWeekIndices.push(index)
      }
    }
  },
  watch: {
    plans: {
      deep: true,
      immediate: true,
      handler () {
        this.initWeeks()
        this.initCurriculum()
      }
    },
    // 监听 frameworkId 变化，重新加载框架测评点数据
    frameworkId: {
      immediate: true,
      handler (val) {
        if (val) {
          this.preloadFrameworkMeasures(val)
        }
      }
    },
    editableTabsValue (val) {
      this.$nextTick(() => {
        let tabs = this.$refs.weeksTab.panes
        if (tabs) {
          let tab = tabs.find(x => x.name === val)
          if (tab) {
            this.currentWeekIndex = parseInt(tab.index)
          }
        }
        let week = val
        let scroll = this.$route.params.weekNum || false
        this.$emit('changeWeek', { week: week, scroll: scroll })
      })
    },
    // 监听路由参数变化，切换周次
    $route () {
      if (this.$route.params.weekNum) {
        this.editableTabsValue = this.$route.params.weekNum.toString()
        this.currentIndex = this.$route.params.weekNum
        this.$emit('changeWeek', { week: this.editableTabsValue , scroll: true })
      }
    },
    // 监听当前周索引变化，提前加载相邻周
    currentWeekIndex (val) {
      // 将当前周及其相邻一周提前加入已加载集合，实现预测性预加载
      this.addLoadedIndex(val)
      this.addLoadedIndex(val - 1)
      this.addLoadedIndex(val + 1)
    }
  },
  computed: {
    ...mapState({
      isCG: state => state.curriculum.isCG, // 是否是 Curriculum Genie 平台
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      planRecording: state => state.lesson.planRecording, // 是否录制周计划中
      currentUser: state => state.user.currentUser,
      contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 内容的源语言码
    }),
    // 判断内容的当前语言码是否与源语言码相同
    isSameLanguage () {
      return this.contentLanguage === this.contentOriginalLanguage
    },
    published () {
      return this.isPublished
    },
    currentWeek () {
      return this.weeks[this.currentWeekIndex]
    }
  }
}
</script>

<style lang="scss" scoped>
.curriculum-editor-content {
  margin: 20px auto 0 0;
  background-color: #FDF6ED;
}

.week-edit-div /deep/ .lg-tabs.week-edit .el-tabs__nav-wrap.is-scrollable {
  background: #F9BB80 !important;
}

.week-edit-div /deep/ .week-edit .el-tabs__item.is-active {
  background: #FFFFFF !important;
  color: #FCBA4C !important;
  border-radius: 4px !important;
  border-bottom: none;
  border-width: 0;
  border-right: none;
}

.week-edit-div /deep/ .week-edit .el-tabs__item {
  color: #FFFFFF;
  border-width: 0;
  // border-right: 1px solid rgba($color: #fff, $alpha: 0.7);
}
.week-edit-div /deep/.week-edit .el-tabs__nav {
  padding: 5px;
  background: #F9BB80 !important;
  border-radius: 4px;
  border-width: 0;
  border: none;
}

.week-edit-div /deep/ .week-edit .el-tabs__item:hover {
  background: #fff !important;
  color: #F9BB80 !important;
}

.week-edit-div /deep/.week-edit .el-tabs__item:last-child {
  padding-right: 10px;
  padding-left: 10px;
  border-width: 0;
  border-right: none;
}

.week-edit-div /deep/ .week-edit .el-tabs__content {
  overflow: visible;
}
.settings-style {
  padding: 8px 16px;
  line-height: 24px;
}
/deep/ .divider-custom {
  margin: 0px!important;
}
.adapted-group {
  white-space: pre;
}

// 移动端样式适配
@media screen and (max-width: 767px) {
  /deep/ .week-edit {
    .el-tabs__header {
      margin-bottom: 10px;
    }

    .el-tabs__nav {
      width: 100%;
      display: flex;
      overflow-x: auto;
      white-space: nowrap;

      &::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 */
      }

      .el-tabs__item {
        flex-shrink: 0;
        font-size: 14px;
        padding: 0 10px !important;
        height: 36px;
        line-height: 36px;
      }
    }
  }

  .curriculum-editor-content {
    .el-col {
      width: 100% !important;
    }

    .justify-content-end {
      justify-content: center !important;
      margin-top: 10px;
      gap: 10px;

      .btn-style {
        padding: 8px !important;
        font-size: 12px !important;
      }
    }
  }
}

// 更小屏幕适配
@media screen and (max-width: 480px) {
  /deep/ .week-edit {
    .el-tabs__item {
      font-size: 13px;
      height: 32px;
      line-height: 32px;
    }
  }
}
</style>
