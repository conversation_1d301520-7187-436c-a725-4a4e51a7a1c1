<template>
  <div ref="resourceInfoRef" class="resource-share-info" v-loading="resourceLoading">
    <!--    style="font-size: 24px;"-->
    <div class="resource-share-info-first-div" style="font-size: 24px;">
      <img src="@/assets/img/lesson2/lessons2-lesson-resources2.png" class="lessons2-lesson-resources"
           :alt="$t('loc.lessons2LessonResources')"/>
      <div class="flex-center-center lessons2-lesson-resources-info">
        <span>{{$t('loc.resources')}}</span>
      </div>
    </div>
    <div class="add-margin-t-10">
      <div class="font-size-14 font-color-gray">
        {{$t('loc.resourceListNote')}}
      </div>
      <!-- Books list -->
      <div class="add-margin-t-10">
        <div  class="flex-row-between">
          <span class="text-black-mon title-font-16">
            {{$t('loc.curriculum14')}}
            <span class="color-676879 font-weight-normal">({{ books.length }})</span></span>
          <el-button v-if="isEdit" @click="addResourceBook" class="btn-padding-12" type="success" icon="el-icon-plus">{{$t('loc.addBook')}}
          </el-button>
        </div>
        <div class="add-margin-t-10 grid-book-width-auto">
          <book-item v-for="(book,index) in showBooks" :key="book.platformId" :book="book"
                     @viewAll="viewAllResource"
                     @delete="deleteBook"
                     :not-show-delete="!isEdit"
                     :is-show-shadow="haveBookListMore && index === showBooks.length -1"></book-item>
        </div>
      </div>
      <!-- Key Vocabularies list -->
      <div v-if="dllOpen" class="add-margin-t-20">
        <div class="flex-row-between">
          <span class="text-black-mon title-font-16">{{$t('loc.curriculum15')}} <span
            class="color-676879 font-weight-normal">({{ dlls.length }})</span></span>
          <el-button v-if="isEdit" @click="addDllBook" class="btn-padding-12" type="success" icon="el-icon-plus">{{$t('loc.addKeyVocabulary')}}
          </el-button>
        </div>
        <div class="add-margin-t-16 grid-dll-width-auto">
          <key-vocabulary-item v-for="(dll,index) in showDlls" :key="dll.id" :dll="dll"
                               @viewAll="viewAllResource"
                               :is-in-material="true"
                               @delete="deleteDll"
                               :not-show-delete="!isEdit"
                               :is-show-shadow="haveDllListMore && index === showDlls.length -1"></key-vocabulary-item>
        </div>
      </div>
      <!-- Printables File list -->
      <div class="add-margin-t-20">
        <div class="flex-row-between">
          <span class="text-black-mon title-font-16">{{$t('loc.curriculum16')}} <span
            class="color-676879 font-weight-normal">({{ files.length }})</span></span>
          <el-button v-if="isEdit" @click="addAttachFile" class="btn-padding-12" type="success" icon="el-icon-paperclip">{{$t('loc.attachFile')}}
          </el-button>
        </div>
        <div class="add-margin-t-10 grid-file">
          <printable-attachment-item v-for="(file,index) in showFiles" :key="index"
                                     @viewAll="viewAllResource"
                                     :is-in-material="true"
                                     @delete="deleteFile"
                                     :not-show-delete="!isEdit"
                                     :not-download="isEdit"
                                     :file="file"></printable-attachment-item>
        </div>
      </div>
      <!-- Domain/Measure List -->
      <div class="add-margin-t-20">
        <div class="flex-row-between">
          <span class="text-black-mon title-font-16">{{$t('loc.curriculum18')}}
            <span class="color-676879 font-weight-normal"> ({{$t('loc.domain')}}: {{ domainList.length }}, {{$t('loc.measure')}}: {{measureNum}})</span></span>
          <div  class="display-flex align-items">
            <el-button v-if="frameworkLinkUrl" plain @click="checkReference" class="btn-padding-12" icon="el-icon-link">{{$t('loc.checkRef')}}
            </el-button>
            <el-button v-if="isEdit" style="margin-left: 12px;" @click="selectDomains" class="btn-padding-12" type="success" icon="el-icon-plus">{{$t('loc.selectDomains')}}
            </el-button>
          </div>
        </div>
        <div class="add-margin-t-10 white-background add-padding-lr-16">
          <domain-measure-item
            class="add-margin-t-8"
            v-for="domain in showDomainList" :key="domain.id" :domain="domain">
          </domain-measure-item>
          <!-- 点击查看所有的测评点 -->
          <div @click="viewAllMeasures"
               v-if="domainList && domainList.length > 0 && measureNum !== showMeasureNum"
               class="flex-center-center lg-pointer lg-pa-6">
            <span class="text-primary font-weight-semibold line-height-22">{{$t('loc.viewAll')}}</span>
            <i style="color: #10B3B7" class="add-margin-l-5 el-icon-arrow-down"></i>
          </div>
        </div>
      </div>
    </div>
    <slot name="share"/>
    <!-- 查看各种资源的view all 弹框   -->
    <view-resource-list-dialog  ref="viewResourceListDialogRef" :unit-week-title="unitWeekTitle"  @update="getUnitPlanSource" :is-edit="isEdit"></view-resource-list-dialog>
    <!-- 选择测评点的弹框 -->
    <select-domain-measure-dialog
      ref="selectDomainMeasureDialogRef"
      :framework-id="frameworkId"
      :framework-name="frameworkName"
      :plan-id="planId"
      :curriculum-id="curriculumId"
      :unit-id="unitId"
      :value="haveSelectMeasures"
      @update="getUnitPlanSource"
    >
    </select-domain-measure-dialog>
    <!-- 添加书籍的弹框 -->
    <add-book-resource-dialog :plan-id="planId" :curriculum-id="curriculumId" :unit-id="unitId" :unit-week-title="unitWeekTitle" ref="addBookResourceDialogRef" @success="getUnitPlanSource"></add-book-resource-dialog>
    <!-- 添加附件资源的弹框 -->
    <add-attachment-resource-dialog :plan-id="planId" :curriculum-id="curriculumId" :unit-id="unitId" :unit-week-title="unitWeekTitle" ref="addAttachmentResourceDialogRef" @success="getUnitPlanSource"></add-attachment-resource-dialog>
    <!-- 添加词汇的弹框 -->
    <add-vocabulary-resource-dialog :plan-id="planId" :curriculum-id="curriculumId" :unit-id="unitId" :unit-week-title="unitWeekTitle" ref="addVocabularyResourceDialogRef" @success="getUnitPlanSource"></add-vocabulary-resource-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import BookItem from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumResources/BookItem'
import KeyVocabularyItem from '../../components/CurriculumResources/keyVocabularyItem'
import PrintableAttachmentItem from '../../components/CurriculumResources/PrintableAttachmentItem'
import DomainMeasureItem from '../../components/CurriculumResources/DomainMeasureItem'
import ViewResourceListDialog from '../../components/CurriculumResources/ViewResourceListDialog'
import SelectDomainMeasureDialog from '../../components/CurriculumResources/SelectDomainMeasureDialog'
import AddBookResourceDialog from '../../components/CurriculumResources/AddBookResourceDialog'
import AddAttachmentResourceDialog from '../../components/CurriculumResources/AddAttachmentResourceDialog'
import AddVocabularyResourceDialog from '../../components/CurriculumResources/AddVocabularyResourceDialog'
import tools from '../../../../../../utils/tools'

export default {
  name: 'ResourceInfo',
  data () {
    return {
      books: [],
      showBooks: [],
      dlls: [],
      showDlls: [],
      files: [],
      showFiles: [],
      domainList: [],
      showDomainList: [],
      measureNum: 0,
      haveBookListMore: false,
      haveDllListMore: false,
      haveSelectMeasures: [],
      resourceLoading: false,
      viewALLBooks: [],
      viewALLDlls: [],
      viewALLFiles: [],
      showMeasureNum: 0
  }
  },
  components: {
    AddVocabularyResourceDialog,
    AddAttachmentResourceDialog,
    AddBookResourceDialog,
    SelectDomainMeasureDialog,
    ViewResourceListDialog,
    DomainMeasureItem,
    PrintableAttachmentItem,
    KeyVocabularyItem,
    BookItem
  },
  props: {
    lesson: {
      type: Object,
      default: function () {
        return {}
      }
    },
    planId: {
      type: String
    },
    curriculumId: {
      type: String
    },
    unitId: {
      type: String
    },
    unitWeekTitle: {
      type: String
    },
    frameworkId: {
      type: String
    },
    frameworkName: {
      type: String
    },
    isEdit: {
      type: Boolean
    },
    frameworkLinkUrl: {
      type: String
    }
  },
  computed: {
    ...mapState({
      open: state => state.common.open
    }),
    dllOpen () {
      return this.open && this.open.dllopen
    },
    attachments () {
      return this.lesson.attachmentMedias
    },
    // books () {
    //   return this.lesson.books && this.lesson.books[0]
    // },
    videos () {
      return this.lesson.videoBooks && this.lesson.videoBooks[0]
    }
  },
  methods: {
    // 添加书籍的操作
    addResourceBook () {
      this.$refs.addBookResourceDialogRef.showAddBookResourceDialog()
    },
    // 添加词汇的操作
    addDllBook () {
      this.$refs.addVocabularyResourceDialogRef.showAddVocabularyResourceDialog()
    },
    // 添加附件的操作
    addAttachFile () {
      this.$refs.addAttachmentResourceDialogRef.showAddAttachmentResourceDialog()
    },
    // 打开框架检查参考 PDF
    checkReference () {
      tools.openUrlWithWebAndIPad(this.frameworkLinkUrl)
    },
    // 选择测评点的操作
    selectDomains () {
      this.$analytics.sendEvent('web_curriculum_add_measure_edit')
      if (!this.frameworkId) {
        this.$message.error('Please select the framework first')
        document.getElementById('curriculum').scrollIntoView()
        return
      }
      // 通过全局总线将数据发送给子组件
      this.$bus.$emit('selectDomainMeasureDialog', this.haveSelectMeasures)
      this.$refs.selectDomainMeasureDialogRef.showSelectDomainMeasureDialog()
    },
    // 处理判断展示资源 View All 的逻辑
    changeResourceShowView () {
      this.$nextTick(() => {
        // 处理资源的 book 列表
        // let bookList = this.$refs.bookListRef
        let resourceWidth = this.$refs.resourceInfoRef.offsetWidth - 68
        if (this.books && this.books.length > 0) {
          if (this.books.length * 152 - 24 <= resourceWidth) {
            this.haveBookListMore = false
            this.showBooks = this.books.map(book => {
              return book
            })
          } else {
            let showBookListNum = (resourceWidth + 24) / 152
            this.showBooks = this.books.slice(0, showBookListNum)
            this.haveBookListMore = true
          }
        } else {
          this.showBooks = []
        }
        // 处理资源的 dll 列表
        // let bookList = this.$refs.bookListRef
        if (this.dlls && this.dlls.length > 0) {
          if (this.dlls.length * 171 - 24 <= resourceWidth) {
            this.haveDllListMore = false
            this.showDlls = this.dlls.map(dll => {
              return dll
            })
          } else {
            let showDllListNum = (resourceWidth + 24) / 171
            this.showDlls = this.dlls.slice(0, showDllListNum)
            this.haveDllListMore = true
          }
        } else {
          this.showDlls = []
        }

        // 处理附件的列表
        if (this.files && this.files.length > 0) {
          if (this.files.length > 4) {
            this.showFiles = this.files.slice(0, 3)
            this.showFiles.push({
              'isShowViewAll': true
            })
          } else {
            this.showFiles = this.files.map(file => {
              return file
            })
          }
        } else {
          this.showFiles = []
        }
        // 处理domain
        if (this.domainList && this.domainList.length > 0) {
          let measureNum = 0
          this.domainList.forEach(domain => {
            measureNum += domain.children.length
          })
          this.measureNum = measureNum
          // 判断展示的领域和测评点的总行数是否大于8，如果是就隐藏
          let domainMeasureLineNum = 0
          let tempShowDomainList = []
          for (let i = 0; i < this.domainList.length; i++) {
            domainMeasureLineNum += 1
            let domain = this.domainList[i]
            let tempDomain = {
              'id': domain.id,
              'abbreviation': domain.abbreviation,
              'name': domain.name,
              'children': []
            }
            tempShowDomainList.push(tempDomain)
            if (domainMeasureLineNum === 8) {
              break
            } else {
              // 处理测评点
              let isEven = domain.children.length % 2 === 0 // 是否是偶数
              let tempMeasureLineNum = isEven ? domain.children.length / 2 : domain.children.length / 2 + 1
              if (domainMeasureLineNum + tempMeasureLineNum >= 8) {
                let tempIndex = 0 // 定义达到置顶行数的index
                for (let j = 0; j < domain.children.length; j++) {
                  if (isEven) {
                    if ((j + 1) % 2 === 0) {
                      domainMeasureLineNum += 1
                      if (domainMeasureLineNum === 8) {
                        tempIndex = j
                        break
                      }
                    }
                  } else {
                    if (j % 2 === 0) {
                      domainMeasureLineNum += 1
                      if (domainMeasureLineNum === 8) {
                        tempIndex = j
                        break
                      }
                    }
                  }
                }
                tempDomain.children = tempDomain.children.concat(domain.children.slice(0,tempIndex + 1))
                break
              } else {
                domainMeasureLineNum = parseInt(domainMeasureLineNum + tempMeasureLineNum)
                tempDomain.children = tempDomain.children.concat(domain.children)
              }
            }
          }
          this.showDomainList = tempShowDomainList
        } else {
          this.measureNum = 0
          this.showDomainList = []
        }
      })
    },
    // 查看资源 View all 的操作
    viewAllResource (resourceName) {
      if (resourceName === 'book') {
        this.$refs.viewResourceListDialogRef.showViewResourceListDialog(true,false,false,false,'Books list',this.viewALLBooks)
      } else if (resourceName === 'vocabulary') {
        this.$refs.viewResourceListDialogRef.showViewResourceListDialog(false,true,false,false,'Key Vocabularies',this.viewALLDlls)
      } else if (resourceName === 'file') {
        this.$refs.viewResourceListDialogRef.showViewResourceListDialog(false,false,true,false,'Printables',this.viewALLFiles)
      }
    },
    // 获取材料和资源的数据
    getMaterialsResourceList (res) {
      let materialsResourceInfo = JSON.parse(res)
      // 处理选中的领域和测评点
      if (materialsResourceInfo.measures && materialsResourceInfo.measures.length > 0) {
        this.haveSelectMeasures = []
        materialsResourceInfo.measures.forEach(measure => {
          this.haveSelectMeasures.push(measure.id)
        })
      } else {
        this.haveSelectMeasures = []
      }
      let tempDomains = []
      if (materialsResourceInfo.domains && materialsResourceInfo.domains.length > 0) {
        materialsResourceInfo.domains.forEach(domain => {
          let tempDomain = {}
          tempDomain.id = domain.id
          tempDomain.abbreviation = domain.abbreviation
          tempDomain.name = domain.name
          tempDomain.children = []
          if (materialsResourceInfo.measures && materialsResourceInfo.measures.length > 0) {
            materialsResourceInfo.measures.forEach(measure => {
              if (measure.parentId.toUpperCase() === domain.id.toUpperCase()) {
                let tempChild = {
                  'id': measure.id,
                  'abbreviation': measure.abbreviation,
                  'name': measure.name,
                  'children': [],
                  'core': measure.core
                }
                tempDomain.children.push(tempChild)
              }
            })
          }
          tempDomains.push(tempDomain)
        })
      }
      this.domainList = tempDomains
      // 处理书
      let tempBooks = []
      let tempViewAllBooks = []
      if (materialsResourceInfo.books) {
        // 处理大小组的数据
        if (materialsResourceInfo.books.otherBooks && materialsResourceInfo.books.otherBooks.length > 0) {
          materialsResourceInfo.books.otherBooks.forEach(otherBook => {
            let tempOtherBook = {}
            tempOtherBook.resourceList = []
            tempOtherBook.categoryName = otherBook.categoryName
            if (otherBook.bookModels && otherBook.bookModels.length > 0) {
              otherBook.bookModels.forEach(bookModel => {
                if (bookModel.book) {
                  let tempBookArray = JSON.parse(bookModel.book)
                  let tempBook = tempBookArray[0]
                  tempBook.platformId = bookModel.id
                  tempOtherBook.resourceList.push(tempBook)
                  tempBooks.push(tempBook)
                }
              })
            }
            tempViewAllBooks.push(tempOtherBook)
          })
        }
        // 处理centers 的数据
        if (materialsResourceInfo.books.centerBooks && materialsResourceInfo.books.centerBooks.length > 0) {
          materialsResourceInfo.books.centerBooks.forEach(centerBook => {
            let tempCenterBook = {}
            tempCenterBook.resourceList = []
            tempCenterBook.categoryName = centerBook.categoryName
            if (centerBook.bookModels && centerBook.bookModels.length > 0) {
              centerBook.bookModels.forEach(bookModel => {
                if (bookModel.book) {
                  let tempBookArray = JSON.parse(bookModel.book)
                  let tempBook = tempBookArray[0]
                  tempBook.platformId = bookModel.id
                  tempCenterBook.resourceList.push(tempBook)
                  tempBooks.push(tempBook)
                }
              })
            }
            tempViewAllBooks.push(tempCenterBook)
          })
        }
      }
      this.books = tempBooks
      this.viewALLBooks = tempViewAllBooks
      // 处理词汇
      let tempVocabularies = []
      let tempViewAllVocabularies = []
      if (materialsResourceInfo.vocabularies) {
        // 处理大小组的数据
        if (materialsResourceInfo.vocabularies.otherVocabularies && materialsResourceInfo.vocabularies.otherVocabularies.length > 0) {
          materialsResourceInfo.vocabularies.otherVocabularies.forEach(otherVocabulary => {
            let tempOtherVocabulary = {}
            tempOtherVocabulary.resourceList = []
            tempOtherVocabulary.categoryName = otherVocabulary.categoryName
            if (otherVocabulary.vocabularyModels && otherVocabulary.vocabularyModels.length > 0) {
              otherVocabulary.vocabularyModels.forEach(vocabularyModel => {
                let tempVocabulary = {
                  'id': vocabularyModel.id,
                  'name': vocabularyModel.content,
                  'type': vocabularyModel.type,
                  'mediaUrl': vocabularyModel.media ? vocabularyModel.media.url : '',
                  'mediaId': vocabularyModel.media ? vocabularyModel.media.id : ''
                }
                tempVocabularies.push(tempVocabulary)
                tempOtherVocabulary.resourceList.push(tempVocabulary)
              })
            }
            tempViewAllVocabularies.push(tempOtherVocabulary)
          })
        }
        // 处理centers 的数据
        if (materialsResourceInfo.vocabularies.centerVocabularies && materialsResourceInfo.vocabularies.centerVocabularies.length > 0) {
          materialsResourceInfo.vocabularies.centerVocabularies.forEach(centerVocabulary => {
            let tempCenterVocabulary = {}
            tempCenterVocabulary.resourceList = []
            tempCenterVocabulary.categoryName = centerVocabulary.categoryName
            if (centerVocabulary.vocabularyModels && centerVocabulary.vocabularyModels.length > 0) {
              centerVocabulary.vocabularyModels.forEach(vocabularyModel => {
                let tempVocabulary = {
                  'id': vocabularyModel.id,
                  'name': vocabularyModel.content,
                  'type': vocabularyModel.type,
                  'mediaUrl': vocabularyModel.media ? vocabularyModel.media.url : '',
                  'mediaId': vocabularyModel.media ? vocabularyModel.media.id : ''
                }
                tempVocabularies.push(tempVocabulary)
                tempCenterVocabulary.resourceList.push(tempVocabulary)
              })
            }
            tempViewAllVocabularies.push(tempCenterVocabulary)
          })
        }
      }
      this.dlls = tempVocabularies
      this.viewALLDlls = tempViewAllVocabularies
      // 处理附件
      let tempAttachments = []
      let tempViewAllAttachments = []
      if (materialsResourceInfo.attachments) {
        // 处理大小组的数据
        if (materialsResourceInfo.attachments.otherAttachments && materialsResourceInfo.attachments.otherAttachments.length > 0) {
          materialsResourceInfo.attachments.otherAttachments.forEach(otherAttachment => {
            let tempOtherFile = {}
            tempOtherFile.resourceList = []
            tempOtherFile.categoryName = otherAttachment.categoryName
            if (otherAttachment.attachmentModels && otherAttachment.attachmentModels.length > 0) {
              otherAttachment.attachmentModels.forEach(attachmentModel => {
                let tempFile = {
                  'id': attachmentModel.id,
                  'name': attachmentModel.media.sourceFileName ? attachmentModel.media.sourceFileName : '',
                  'size': attachmentModel.media.size,
                  'url': attachmentModel.media.url ? attachmentModel.media.url : '',
                  'fileType': attachmentModel.media.fileType ? attachmentModel.media.fileType : ''
                }
                tempAttachments.push(tempFile)
                tempOtherFile.resourceList.push(tempFile)
              })
            }
            tempViewAllAttachments.push(tempOtherFile)
          })
        }
        // 处理centers 的数据
        if (materialsResourceInfo.attachments.centerAttachments && materialsResourceInfo.attachments.centerAttachments.length > 0) {
          materialsResourceInfo.attachments.centerAttachments.forEach(centerAttachment => {
            let tempCenterFile = {}
            tempCenterFile.resourceList = []
            tempCenterFile.categoryName = centerAttachment.categoryName
            if (centerAttachment.attachmentModels && centerAttachment.attachmentModels.length > 0) {
              centerAttachment.attachmentModels.forEach(attachmentModel => {
                let tempFile = {
                  'id': attachmentModel.id,
                  'name': attachmentModel.media.sourceFileName ? attachmentModel.media.sourceFileName : '',
                  'size': attachmentModel.media.size,
                  'url': attachmentModel.media.url ? attachmentModel.media.url : '',
                  'fileType': attachmentModel.media.fileType ? attachmentModel.media.fileType : ''
                }
                tempAttachments.push(tempFile)
                tempCenterFile.resourceList.push(tempFile)
              })
            }
            tempViewAllAttachments.push(tempCenterFile)
          })
        }
      }
      this.files = tempAttachments
      this.viewALLFiles = tempViewAllAttachments
      this.changeResourceShowView()
      // 获取了数据后，设置生成资源的状态为false
      this.$store.dispatch('setGenerateResource', false)
    },
    // 获取系列课程单元周计划的材料和资源详细
    getUnitPlanSource () {
      if (this.planId && this.planId.trim().length > 0) {
        this.resourceLoading = true
        let requestParams = {
          'curriculumId': this.curriculumId,
          'unitId': this.unitId,
          'planId': this.planId
        }
        this.$axios.get($api.urls().getPlanMaterialsAndResource, { params: requestParams })
          .then(res => {
            this.resourceLoading = false
            this.getMaterialsResourceList(JSON.stringify(res))
          })
          .catch(error => {
            this.resourceLoading = false
            this.$message.error(error.response.data.error_message)
          })
      }
    },
    // 删除资源书的逻辑
    deleteBook (bookId) {
      this.resourceLoading = true
      let requestUrl = $api.urls().updateBookResource
      this.$axios
        .post(requestUrl, { 'ids': [bookId] })
        .then(res => {
          if (res.success) {
            this.getUnitPlanSource()
          }
        })
        .catch(error => {
          this.resourceLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 删除资源词汇的逻辑
    deleteDll (dllId) {
      this.resourceLoading = true
      let requestUrl = $api.urls().updateVocabularyResource
      this.$axios
        .post(requestUrl, { 'ids': [dllId] })
        .then(res => {
          if (res.success) {
            this.getUnitPlanSource()
          }
        })
        .catch(error => {
          this.resourceLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 删除资源附件的逻辑
    deleteFile (deleteId) {
      this.resourceLoading = true
      let requestUrl = $api.urls().updateAttachmentResource
      this.$axios
        .post(requestUrl, { 'ids': [deleteId] })
        .then(res => {
          if (res.success) {
            this.getUnitPlanSource()
          }
        })
        .catch(error => {
          this.resourceLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 查看所有的测评点
    viewAllMeasures () {
      this.showDomainList = this.domainList.map(domain => {
        return domain
      })
    }
  },
  watch: {
    // 检测测评点是否展示完全，动态去除测评点 View All 的信息
    showDomainList (value) {
      let tempShowMeasureNum = 0
      value.forEach(domain => {
        tempShowMeasureNum += domain.children.length
      })
      this.showMeasureNum = tempShowMeasureNum
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-share-info {
  position: relative;
  margin: 40px auto 0;
  border-radius: 4px;
  background: #D9F0CE;
  padding: 48px 34px 30px;

  .lessons2-lesson-resources {
    position: relative;
    top: 19px;
  }

  .lessons2-lesson-resources-info {
    width: 467px;
    position: relative;
    bottom: 39px;
    color: #FFFFFF;
  }

  & > .resource-share-info-first-div {
    width: 467px;
    height: 58px;
    position: absolute;
    left: calc(50% - 233.5px);
    top: -29px;
    font-size: 16px;
    text-align: center;
    line-height: 58px;
  }

  & > .attachment-list {
    margin-top: 31px;
  }
}

.grid-book-width-auto {
  display: grid;
  grid-template-columns: repeat(auto-fill, 128px);
  grid-column-gap: 24px;
}

.grid-dll-width-auto {
  display: grid;
  grid-template-columns: repeat(auto-fill, 171px);
  grid-column-gap: 18px;
}

.grid-file {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 24px;
  grid-row-gap: 12px;
}

.bg-green-btn {
  background-color: #7ECC57 !important;
  color: #FFFFFF !important;
}

.bg-green-btn:hover {
  border-color: #7ECC57 !important;
}

.btn-padding-12 {
  padding: 12px !important;
}
</style>
