<template>
    <div class="lesson-share-info">
      <div style="font-size: 24px;">
            <img src="@/assets/img/lesson2/lessons2-lesson-resources.png" class="lessons2-lesson-resources"
                :alt="$t('loc.lessons2LessonResources')" />
            <div class="flex-center-center lessons2-lesson-resources-info">
                <span>{{$t('loc.curriculum53')}}</span>
            </div>
        </div>
      <week-edit
        ref="weekEdit"
        :groupName="groupName"
        :isOnlyView="isOnlyView"
        :isEditor="isEditor"
        :curriculumId="curriculumId"
        :unitId="unitId"
        :unitTitle="unitTitle"
        :unitNum="unitNum"
        :plans="plans"
        :showApply="showApply"
        :frameworkId="frameworkId"
        :frameworks="frameworks"
        :framework-name="frameworkName"
        @callAdaptUnitWeekPlan="callAdaptUnitWeekPlan"
        @callEditUnitWeekPlan="callEditUnitWeekPlan"
        :framework-link-url="frameworkLinkUrl"
        @changeWeek="changeWeek"
        :isFromUnitDetail="isFromUnitDetail"
        :exemplar="exemplar"
    ></week-edit>
    </div>
</template>
<script>
import WeekEdit from './WeekEdit.vue'
export default {
    name: 'WeeklyInfo',
    components: {
      WeekEdit
    },
    props: {
      isEditor: {
        type: Boolean
      },
      curriculumId: {
        type: String
      },
      unitId: {
        type: String
      },
      unitTitle: {
        type: String
      },
      plans: {
        type: Array
      },
      lesson: {
        type: Object,
        default: function () {
            return {}
        }
      },
      unitNum: {
        type: Number
      },
      frameworks: {
        type: Array
      },
      frameworkId: {
        type: String
      },
      frameworkName: {
        type: String
      },
      frameworkLinkUrl: {
        type: String
      },
      isFromUnitDetail: {
        type: Boolean,
        default: false
      },
      showApply: {
        type: Boolean,
        default: false
      },
      exemplar: {
        type: Boolean,
        default: false
      },
      isOnlyView: {
        type: Boolean,
        default: false
      },
      groupName: {
        type: String,
        default: ''
      }
    },
    computed: {
        attachments () {
            return this.lesson.attachmentMedias
        },
        book () {
            return this.lesson.books && this.lesson.books[0]
        },
        video () {
            return this.lesson.videoBooks && this.lesson.videoBooks[0]
        }
    },
  methods: {
    changeWeek (week) {
      this.$emit('changeWeek', week)
    },
    callAdaptUnitWeekPlan (item, isLesson) {
      this.$emit('callAdaptUnitWeekPlan', item, isLesson)
    },
    callEditUnitWeekPlan (item) {
      this.$emit('callEditUnitWeekPlan', item)
    }
  }
}
</script>
<style lang="scss" scoped>
.lesson-share-info {
    min-width: fit-content;
    position: relative;
    margin: 60px auto 0;
    border-radius: 4px;
    background: #FDF6ED;
    padding: 48px 34px 30px;

    .lessons2-lesson-resources {
        position: relative;
        top: 19px;
    }

    .lessons2-lesson-resources-info {
        width: 467px;
        position: relative;
        bottom: 39px;
        color: #FFFFFF;
    }

    &> :first-child {
        width: 467px;
        height: 58px;
        position: absolute;
        left: calc(50% - 233.5px);
        top: -29px;
        font-size: 16px;
        text-align: center;
        line-height: 58px;
    }

    &>.attachment-list {
        margin-top: 31px;
    }
}

/* 移动端适配样式 */
@media screen and (max-width: 767px) {
    .lesson-share-info {
        min-width: auto;
        width: 100%;
        padding: 40px 16px 20px;
        margin-top: 40px;
        
        .lessons2-lesson-resources {
            width: 100%;
            max-width: 320px;
            height: auto;
            object-fit: contain;
        }
        
        .lessons2-lesson-resources-info {
            width: 100%;
            max-width: 320px;
            bottom: 32px;
            font-size: 14px;
        }
        
        &> :first-child {
            width: 320px;
            height: 48px;
            left: calc(50% - 160px);
            top: -24px;
            font-size: 14px;
            line-height: 48px;
        }
    }
}

/* 更小屏幕的进一步优化 */
@media screen and (max-width: 480px) {
    .lesson-share-info {
        padding: 30px 12px 16px;
        margin-top: 30px;
        overflow-x: scroll;
        
        .lessons2-lesson-resources {
            max-width: 280px;
        }
        
        .lessons2-lesson-resources-info {
            max-width: 280px;
            bottom: 28px;
            font-size: 13px;
        }
        
        &> :first-child {
            width: 280px;
            height: 40px;
            left: calc(50% - 140px);
            top: -20px;
            font-size: 13px;
            line-height: 40px;
        }
    }
}
</style>
