<template>
  <div class="curriculum-unit-detail-index-info">
    <!-- header -->
    <el-row v-if="false" class="header">
      <div style="position: absolute;overflow: hidden; max-width: 30%;">
        <div class="display-flex">
          <!-- <router-link> -->
            <img
              style="height: 20px;margin: 15px 0;cursor: pointer;"
              src="@/assets/img/us/back_home.png"
              @click="goBack"
            />
          <!-- </router-link> -->
          <span class="m-l-md left-title" :title="'Curriculum Library / ' + curriculumName">
            <span @click="goBack" style="color: #676879;cursor:pointer">
              Curriculum Library&nbsp;/&nbsp;
            </span>
            <span style="color: #323338">
              {{ curriculumName }}
            </span>
          </span>
        </div>
      </div>
      <div class="title">{{ $t('loc.curriculum91') }}</div>
    </el-row>
    <LanguageToggle class="language-toggle" v-model="currentLangCode" v-show="!profileOpenDropdown"/>
    <!-- center -->
    <el-row class="curriculum-unit-detail-center lg-scrollbar-show">
      <div class="curriculum-unit-detail-center-box">
        <!-- info content -->
        <UnitDetailContent v-loading="unitLoading" :curriculumId="curriculumId" :unit="unit" :domains="domains" :frameworkId="frameworkId"></UnitDetailContent>
        <!-- 侧边栏组件 -->
        <unit-switch-list :selectedUnitId="selectedUnitId" :unitList="unitList" :type="type"></unit-switch-list>
      </div>
    </el-row>
    <el-backtop target=".curriculum-unit-detail-center" :right="20" :bottom="80"></el-backtop>
  </div>
</template>

<script>
import UnitSwitchList from '@/views/modules/lesson2/lessonCurriculum/CurriculumUnitDetail/components/UnitSwitchList.vue'
import UnitDetailContent from './components/UnitDetailContent'
import Lessons2 from '@/api/lessons2'
import { mapState } from 'vuex'
import LanguageToggle from '@/views/modules/lesson2/component/LanguageToggle.vue'
import unit from "../../../../../store/modules/unit";

export default {
  name: 'CurriculumUnitDetail',
  components: { UnitSwitchList, UnitDetailContent, LanguageToggle },
  data () {
    return {
      domains: null,
      unit: {},
      unitList: [],
      selectedUnitId: '',
      unitLoading: true,
      curriculumId: '',
      planId: '',
      edit: false,
      curriculumName: '',
      curriculumAuthorId: '',
      type: '', // 课程类型
      public: false, // 是否是公共系列课程
      published: false, // 是否已发布
      frameworkId: '',
      currentLangCode: '', // 当前内容的语言码
      isNeedDetectLanguage: false // 是否是识别语言状态
    }
  },
  provide () {
    return {
      isAuthor: () => this.curriculumAuthorId === this.$store.state.user.uid,
      isPublic: () => this.public,
      isPublished: () => this.published
    }
  },
  computed: {
    ...mapState({
      planRecording: state => state.lesson.planRecording,
      contentOriginalLanguage: state => state.translate.originalContentLangCode, // 当前内容的源语言码
      profileOpenDropdown: state => state.common.profileOpenDropdown // profile 的下拉框是否打开
    }),
    // 当前语言是否和内容的源语言相同
    isSameLanguage () {
      return this.currentLangCode === this.contentOriginalLanguage
    }
  },
  watch: {
    // 监听路由
    $route (to, from) {
      this.selectedUnitId = this.$route.params.unitId
    },
    selectedUnitId: {
      handler (val, oldval) {
        if (this.unit.id === this.selectedUnitId) {
          return
        }
        this.selectUnit(this.selectedUnitId, !oldval)
      }
    },
    // 监听当前语言类型变更
    currentLangCode (val) {
      // 如果不是识别语言导致的语言类型变更
      if (!this.isNeedDetectLanguage) {
        // 获取翻译的单元详情
        this.getUnitDetail(this.selectedUnitId, false, this.currentLangCode)
        this.getUnitList(this.curriculumId, this.currentLangCode)
        // 设置当前语言码
        this.$store.commit('SET_CURRENT_LANGUAGE', this.currentLangCode)
      } else {
        // 设置识别语言状态为 false
        this.isNeedDetectLanguage = false
      }
    }
  },
  created () {
    this.init()
  },
  mounted () {
    // 进入页面定位到顶端
    // window.scrollTo(0, 0)
  },
  methods: {
    init () {
      let unitId = this.$route.params.unitId
      this.curriculumId = this.$route.params.curriculumId
      this.planId = this.$route.params.planId
      this.edit = this.$route.params.edit
      this.selectedUnitId = unitId
      this.curriculumName = this.$route.params.curriculumName
      this.curriculumAuthorId = this.$route.params.authorId && this.$route.params.authorId.toUpperCase()
      this.public = this.$route.params.public
      this.published = this.$route.params.published
      this.type = this.$route.params.type
      this.loadCurriculumInfo(this.curriculumId)
      this.getUnitList(this.curriculumId)
    },
    //  选择当前单元
    selectUnit (id, isDetectLanguage) {
      if (!id) {
        return
      }
      this.getUnitDetail(id, isDetectLanguage, this.currentLangCode)
    },
    //  获取单元详情
    getUnitDetail (id, needDetectLanguage, langCode) {
      this.unitLoading = true
      Lessons2.getUnitDetail({
        unitId: id,
        ...(needDetectLanguage && { isDetect: needDetectLanguage }),
        ...((langCode && !this.isSameLanguage) && { langCode })
      }).then(res => {
        let unitRes = {
          id: res.id,
          title: res.title || '',
          number: res.number,
          description: res.overview || '',
          trajectory: res.trajectory || '',
          coverMedias: res.coverMedias || [],
          concepts: res.concepts || '',
          unitAddressDomains: res.unitAddressDomains || [],
          guidingQuestions: res.guidingQuestions || '',
          plans: res.plans || [],
          weeksNum: res.weeksNum || [],
          activitiesNum: res.activitiesNum || '',
        }
        // 如果返回了语言码结果，则设置
        if (needDetectLanguage && res.langCode && res.langCode.trim() !== '') {
          this.$store.commit('SET_ORIGINAL_LANGUAGE', res.langCode)
          // 设置内容的当前语言码
          this.$store.commit('SET_CURRENT_LANGUAGE', res.langCode)
          // 设置当前语言码
          this.currentLangCode = res.langCode
          this.isNeedDetectLanguage = true
        }
        this.curriculumAuthorId = res.curriculumAuthorId && res.curriculumAuthorId.toUpperCase()
        this.public = res.curriculumType === 'PUBLIC'
        this.published = res.curriculumStatus === 'PUBLISHED'
        this.unit = unitRes
        this.selectedUnitId = unitRes.id
        this.unitLoading = false
      }).catch(error => {
        this.unitLoading = false
        this.$message.error(error)
      })
    },
    getUnitList (id, langCode) {
      Lessons2.getUnitList({
        curriculumId: id,
        ...(langCode && !this.isSameLanguage && { langCode })
      }).then(res => {
        this.unitList = res.units
      }).catch(error => {
        this.$message.error(error)
      })
    },
    loadCurriculumInfo (curriculumId) {
      let frameworkId = this.$route.params.frameworkId
      if (frameworkId && this.curriculumName) {
        this.loadMeasures(frameworkId)
        return
      }
      let params = { id: curriculumId }
      Lessons2.getCurriculumDetail(params)
        .then(res => {
          // 设置 Plan Center 类型
          this.setPlanCenterType(res.ageValues)
          this.curriculumName = res.name
          frameworkId = res.framework ? res.framework.id : null
          this.loadMeasures(frameworkId)
        })
        .catch(error => {
          this.$message.error(error)
        })
    },
    // 设置 Plan Center 类型
    setPlanCenterType (ages) {
      // 处理年龄数据，避免空指针
      ages = ages || []
      let type = 'PS' // 默认为 PS
      // 按照年龄大小判断 Plan Center 类型，从低到高适配
      if (ages.includes('0') || ages.includes('1') || ages.includes('2') || ages.includes('1,2')) {
        type = 'IT'
      } else if (ages.includes('3') || ages.includes('4')) {
        type = 'PS'
      } else if (ages.includes('5') || ages.includes('Grade 1') || ages.includes('Grade 2')) {
        type = 'K'
      } else {
        type = 'PS'
      }
      this.$store.commit('SET_PLANCENTERTYPE', type)
    },
    // 加载框架的领域
    loadMeasures (frameworkId) {
      if (frameworkId) {
        this.frameworkId = frameworkId
        Lessons2.getMeasures(frameworkId)
          .then(res => {
            this.domains = res.measures
          })
      }
    },
    async goBack () {
      // 如果正在录制，提示是否退出录制
      let exist = true
      if (this.planRecording) {
        // 警告提示， 如果退出会终止录制
        await this.$confirm(this.$t('loc.plan142'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
        }).catch(() => {
          exist = false
        })
      }
      if (!exist) {
        return
      }
      // 从周计划跳转过来的返回到周计划
      if (this.planId) {
        this.$router.push({
          name: this.edit ? 'edit-plan' : 'view-plan',
          params: {
            planId: this.planId
          }
        })
      } else {
        this.$router.push({
          name: 'lessonCurriculum',
          params: {
            type: this.type,
            id: this.curriculumId
          }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.curriculum-unit-detail-index-info {
  font-family: Inter;
  height: 100%;
  width: 100%;
  background: #f5f6f8;

  .curriculum-unit-detail-header {
    height: 50px;
    padding: 0 35px;
    line-height: 50px;
    color: #111C1C;
    background-color: #e4eaec;
    text-align: center;

    .unit-detail-title {
      font-weight: bold;
      color: #323338;
      font-size: 18px;
      //超出自动换行
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // width: 200px;
    }

  }

  .curriculum-unit-detail-center {
    overflow-y: hidden;
    height: 100%;
    padding: 24px 24px 0;
    width: 100%;

    .curriculum-unit-detail-center-box {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
    }
  }

  .nav-title {
    //超出部分显示省略号
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .span-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

}

.header {
  height: 50px;
  padding: 0 35px;
  line-height: 50px;
  color: #111C1C;
  background-color: #e4eaec;
  text-align: center;
}
.title {
  color: #323338;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "Inter";
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  max-width: 20%;
  margin: 0 auto;
}

.left-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.language-toggle {
  top: 66px;
  right: 28px;
  position: absolute;
  z-index: 1001;
}
</style>
