<template>
  <div v-loading="loading" class="h-full display-flex flex-direction-col" style="overflow: auto">
    <header-bar :title="$t('loc.curriculum11')" :leftTitle="curriculumName" :curriculumId="curriculumId" :type="type"></header-bar>
    <div v-if="sum > 0"  class="h-full">
      <el-row type="flex" class="h-full">
        <el-col :span="18" class="bg-white lg-box-shadow lg-border-radius-8 h-full lg-scrollbar-show">
          <!-- Resource name / PDF title -->
          <div class="top-info">
            <span class="unit-title">{{ $t('loc.curriculum14') }}</span>
            <div class="pdf">
              <el-button v-show="false"
                         type="primary"
                         size="small"
                         icon="fa fa-file-pdf-o"
                         class="pdf-button">
                <span>PDF</span>
              </el-button>
            </div>
          </div>
          <div v-for="unit in units" :key="unit.unitNum">
            <div v-for="week in unit.weeks" :key="week.week">
              <!-- Unit / week title -->
              <div :class="$t('loc.unitweek') + unit.unitNum + $t('loc.unitweek1') + week.week">
                <unit-week-title
                  :unit="unit"
                  :week="week"></unit-week-title>
              </div>
              <!-- other 分组 -->
              <div v-for="(category, index) in week.categories" :key="index">
                <div v-if="category.sum > 0">
                  <group-title :groupTitle="category.categoryName" :groups="category.sum"></group-title>
                </div>
                <book-card v-if="category.sum > 0" :groups="category.bookResourceModels"></book-card>
              </div>
              <!-- center 分组-->
              <div v-for="(category, index) in week.centerCategories" :key="index">
                <div v-if="category.sum > 0">
                  <group-title :groupTitle="category.categoryName" :groups="category.sum"></group-title>
                </div>
                <book-card v-if="category.sum > 0" :groups="category.bookResourceModels"></book-card>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <unit-directory @goSelect="goSelect" :units="units" :title="$t('loc.unitweek2')" :totalNum="sum"></unit-directory>
        </el-col>
      </el-row>
    </div>
    <div v-else style="height: 100%; background: #fff;">
      <empty-view></empty-view>
    </div>
  </div>
</template>

<script>
import HeaderBar from '../components/HeaderBar.vue'
import UnitDirectory from '../components/UnitDirectory.vue'
import GroupTitle from '../components/GroupTitle.vue'
import UnitWeekTitle from '../components/UnitWeekTitle.vue'
import BookCard from '../components/BookCard.vue'
import EmptyView from '@/views/modules/lesson2/lessonCurriculum/components/EmptyView'
import LessonApi from '@/api/lessons2'
export default {
  name: 'Books',
  components: { HeaderBar, UnitDirectory, BookCard, GroupTitle, UnitWeekTitle, EmptyView },
  data () {
    return {
      currentUnit: 1,
      coverURL:
        'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
      name: '',
      curriculumId: undefined,
      type: '',
      curriculumName: '',
      curriculumBookList: undefined,
      sum: undefined,
      units: undefined,
      loading: false
    }
  },
  computed: {
  },
  created () {
    this.initCurriculumId()
    this.initCurriculumBookList()
  },
  methods: {
    initCurriculumId () {
      this.curriculumId = this.$route.params.id
      this.curriculumName = this.$route.params.curriculumName
      this.type = this.$route.params.type
    },
    initCurriculumBookList () {
      this.loading = true
      LessonApi.getCurriculumBookList(this.curriculumId).then(res => {
        this.curriculumBookList = res
        this.sum = res.sum
        this.units = res.units
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    },
    goSelect (i, j) {
      this.$el.querySelector('.Unit' + i + 'Week' + j).scrollIntoView({ block: 'start', behavior: 'smooth' })
    }
  }
}
</script>

<style lang="less" scoped>
.resource-box {
  height: calc(100% - 50px);
  overflow: auto;
}
.bg-shadow {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}
.top-info {
  margin: 23px 24px 23px;
}

.pdf {
  float: right;
}

.pdf-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-color: #dcdfe6;
  color: #606266;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: 0.1s;
  transition: 0.1s;
  font-weight: 400;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
  font-family: 'Inter';
  font-style: normal;
  font-size: 14px;
  line-height: 17px;
}
.el-button:focus, .el-button:hover {
    color: #10b3b7;
    border-color: #b7e8e9;
    background-color: #e7f7f8;
}
.unit-title {
  width: 713px;
  height: 22px;

  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #323338;
  flex: none;
  order: 0;
  flex-grow: 1;
}

.unit-line {
  margin: 23.5px 24px 16px 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 12px 0px 0px;
  gap: 12px;
  width: auto;
  height: 32px;
  background: #f2f6fe;
  border-radius: 2px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.unit {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 12px;
  gap: 8px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #ffffff;
  flex: none;
  order: 0;
  flex-grow: 0;
  width: auto;
  height: 32px;
  background: #85abf0;
  border-radius: 2px;
}
.week-total {
  float: right;
  margin-left: auto;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #676879;
}

</style>
