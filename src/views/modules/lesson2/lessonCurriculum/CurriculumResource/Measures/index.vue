<template>
  <div v-loading="loading" class="h-full display-flex flex-direction-col" style="overflow: auto">
    <header-bar :title="$t('loc.curriculum11')" :leftTitle="curriculumName" :curriculumId="curriculumId" :type="type"></header-bar>
    <div v-if="units.length > 0" class="h-full">
      <el-row type="flex" class="h-full">
        <el-col :span="18" class="bg-white lg-box-shadow lg-border-radius-8 h-full lg-scrollbar-show">
          <!-- Resource name / PDF title -->
          <div class="top-info">
            <span class="unit-title">{{ $t('loc.assessment2') }} </span>
            <div class="pdf">
              <el-button class="add-margin-r-8" size="medium" plain v-if="frameworkLinkUrl" @click="openPDF()"><i class="fa el-icon-link"></i>{{ $t('loc.checkRef') }}</el-button>
              <el-button  v-show="false"
                          type="primary"
                          size="small"
                          icon="fa fa-file-pdf-o"
                          class="pdf-button"><span>PDF</span></el-button>
            </div>
          </div>
          <!-- 仅展示核心测评点 switch start -->
          <div class="core-measure" v-show="showCoreMeasureOpen">
            <el-switch
              v-model="showCoreMeasure">
            </el-switch>
            <span class="lesson-switch">{{ $t('loc.showCoreMeasureOnly') }}</span>
          </div>
          <!-- 仅展示核心测评点 switch start -->
          <div v-for="unit in units"
               :key="unit.num">
            <div>
              <!-- Unit / week title -->
              <div class="unit-line">
                <span class="unit">{{ unit.unitNum >=1 ? 'Unit' : 'Specail Unit' }} {{ unit.unitNum }} {{ unit.unitName }}</span>
                <span class="unit-total">{{$t('loc.domainsitem')}}: {{" " + domainTotalNums(unit.weeks)[0] }} {{$t('loc.domainsitem1')}}: {{ " " + domainTotalNums(unit.weeks)[1] }}</span>
              </div>
              <el-row :gutter="0" type="flex" style="flex-wrap: wrap;"
                      class="weeks">
                <el-col :span="6"
                        v-for="week in unit.weeks"
                        :key="week.num"
                        :class="week.measureNum && week.measureNum > 0 ? 'week' : ''">
                  <div v-if="week.measureNum && week.measureNum > 0" :class="'Unit' + unit.unitNum + 'Week' + week.week" class="week-title">
                    <span class="week-title-font1"> {{ $t('loc.weekName') }} {{week.week}}</span>
                    <span class="week-title-font2">({{$t('loc.domainsitem')}}:{{ " " + changeTitleData(week)[0] }} {{$t('loc.domainsitem1')}}:{{ " " + changeTitleData(week)[1] }})</span>
                  </div>
                  <div v-if="week.measureNum && week.measureNum > 0" class="week-context">
                    <!-- {{week.context}} -->
                    <div v-if="changeTitleData(week)[0] === 0 ">{{ $t('loc.noCoreMeasureTip') }}</div>
                    <div v-else>
                      <div v-for="domain in week.domains">
                        <div class="domain-name" v-if="!isHasDomainMappedMeasure(domain.id,week.measures)">
                          {{  domain.abbreviation }} - {{ domain.name }}
                        </div>
                        <div v-for="measure in getMeasures(domain.id, week.measures)" class="measure-name">
                          {{  measure.abbreviation }} - {{ measure.name }}
                          <span v-show="measure.core" style="color: red">*</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <unit-directory @goSelect="goSelect" :title="$t('loc.assessment1')" :units="units" :totalNum="totalNum"></unit-directory>
        </el-col>
      </el-row>
    </div>
    <div v-else style="height: 100%; background: #fff;">
      <empty-view></empty-view>
    </div>
  </div>
</template>

<script>
import HeaderBar from '../components/HeaderBar.vue'
import UnitDirectory from '../components/UnitDirectory.vue'
import EmptyView from '@/views/modules/lesson2/lessonCurriculum/components/EmptyView'

import tools from '@/utils/tools'

import Lessons2 from '@/api/lessons2'
export default {
  name: 'Measures',
  components: { HeaderBar, UnitDirectory, EmptyView },
  data () {
    return {
      currentUnit: 1,
      frameworkLinkUrl: '',
      name: '',
      units: [],
      totalNum: 0,
      curriculumId: '',
      type: '',
      curriculumName: '',
      loading: false,
      showCoreMeasure: true, // 是否仅展示核心测评点
      measures: [], // 测评点
      showCoreMeasureOpen: false // 是否展示仅展示核心测评点开关
    }
  },
  created () {
    this.curriculumId = this.$route.params.id
    this.curriculumName = this.$route.params.curriculumName
    this.type = this.$route.params.type
    this.getResource()
  },
  methods: {
    getResource () {
      this.loading = true
      Lessons2.getAssessmentList(this.curriculumId)
      .then(res => {
        this.showCoreMeasureOpen = res.showCoreMeasureOpen
        this.showCoreMeasure = this.showCoreMeasureOpen
        this.frameworkLinkUrl = res.frameworkLinkUrl
        // 把没有数据的单元过滤掉
        this.units = res.units.filter(x => x.domainNum && x.domainNum > 0)
        this.units.forEach(x => {
          x.weeks = x.weeks.filter(w => w.domainNum && w.domainNum > 0)
        })
        this.loading = false
      })
      .catch(err => {
        this.loading = false
      })
    },
    openPDF () {
      tools.openUrlWithWebAndIPad(this.frameworkLinkUrl)
    },
    getMeasures (domainId, measures) {
      if (this.showCoreMeasure) {
        return measures.filter(x => {
          if (x.parentId === domainId && x.core) {
            return x
          }
        })
      } else {
        return measures.filter(x => x.parentId === domainId)
      }
    },
    goSelect (i, j) {
      this.$el.querySelector('.Unit' + i + 'Week' + j).scrollIntoView({ block: 'start', behavior: 'smooth' })
    },
    // 处理 week 标题 domain 和 measure 数量
    changeTitleData (week) {
      let measureNum = 0
      let domainNum = 0
      week.domains.forEach(domain => {
        const num = this.getMeasures(domain.id,week.measures).length
        if (num !== 0) {
          measureNum += num
          domainNum++
        }
      })
      return [domainNum, measureNum]
    },
    domainTotalNums (weeks) {
      let domainNums = 0
      let measureNums = 0
      weeks.forEach((week, index) => {
        const res = this.changeTitleData(week)
        domainNums += res[0]
        measureNums += res[1]
      })
      return [domainNums, measureNums]
    },
    // 根据开关处理完成之后，判断当前domain下面是否有测评点数据
    isHasDomainMappedMeasure (domainId, measures) {
      if (this.showCoreMeasure) {
        return measures.filter(x => {
          if (x.parentId === domainId && x.core) {
            return x
          }
        }).length === 0
      } else {
        return measures.filter(x => x.parentId === domainId).length === 0
      }
    }
  }
}
</script>

<style lang="less" scoped>
.resource-box {
  height: calc(100% - 50px);
  overflow: auto;
}
.bg-shadow {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}
.top-info {
  margin: 23px 24px 23px;
}
.core-measure {
  margin-left: 24px;
  display: flex;
  align-items: center;
  /deep/ .el-switch {
    margin-right: 5px;
  }
}

.pdf {
  float: right;
}

.pdf-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-color: #dcdfe6;
  color: #606266;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: 0.1s;
  transition: 0.1s;
  font-weight: 400;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
  font-family: 'Inter';
  font-style: normal;
  font-size: 14px;
  line-height: 17px;
}


.unit-title {
  width: 713px;
  height: 22px;

  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #323338;
  flex: none;
  order: 0;
  flex-grow: 1;
}

.unit-line {
  margin: 23.5px 24px 16px 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 12px 0px 0px;
  gap: 12px;
  width: auto;
  height: 32px;
  background: #f2f6fe;
  border-radius: 2px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.unit {
  flex-direction: row;
  align-items: center;
  padding: 0px 12px;
  gap: 8px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #ffffff;
  flex: none;
  order: 0;
  flex-grow: 0;

  height: 32px;
  background: #85abf0;
  border-radius: 2px;

  //超出部分省略号
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  max-width: 30%;
  line-height: 32px;




}
.unit-total {
  float: right;
  margin-left: auto;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #676879;
}

.weeks {
  margin: 15px 28px 24px 28px;
  // border-top: 1px solid #f49628;
  // border-left: 1px solid #f49628;
}

.week-title {
  border-bottom: 1px solid #f49628;

  box-sizing: border-box;
  background: #FFFDFA;

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  gap: 4px;
  width: 100%;
  height: 40px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.week {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;

  width: 50%;

  background: #ffffff;
  border: 1px solid #f49628;
  border-bottom: 1px solid #f49628;

  flex: none;
  order: 0;
  flex-grow: 0;
}
.week-title-font1 {
  height: 17px;
  // font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 17px;

  color: #323338;
  flex: none;
  order: 0;
  flex-grow: 0;
}
.week-title-font2 {
  height: 17px;
  // font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #323338;
  flex: none;
  order: 1;
  flex-grow: 0;
}
.unit-total {
  float: right;
  margin-left: auto;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #676879;
}

.week-context{
  padding: 0px 51px 11px 16px;
  color: #111c1c;

}
.domain-name {
  font-size: 16px;
  font-weight: 500;
}
.measure-name {
  margin-left:20px;
}
</style>
