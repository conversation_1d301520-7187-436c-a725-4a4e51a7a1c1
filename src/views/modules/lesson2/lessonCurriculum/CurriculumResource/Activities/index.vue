<template>
  <div v-loading="loading" class="h-full display-flex flex-direction-col" style="overflow: auto">
    <header-bar :title="$t('loc.curriculum11')" :leftTitle="curriculumName" :curriculumId="curriculumId" :type="type"></header-bar>
    <div v-if="totalNum>0" class="h-full">
      <el-row type="flex" class="h-full">
        <el-col :span="18" class="bg-white lg-box-shadow lg-border-radius-8 h-full lg-scrollbar-show">
          <!-- Resource name / PDF title -->
          <div class="top-info">
            <span class="unit-title">{{$t('loc.curriculum17')}}</span>
            <div class="pdf">
              <el-button
                v-show="false"
                type="primary"
                size="small"
                icon="fa fa-file-pdf-o"
                class="pdf-button"
                ><span>PDF</span></el-button
              >
            </div>
          </div>
          <div v-for="(unit, i) in units" :key="unit.unitNum">
            <div v-for="(week, j) in unit.weeks" :key="week.num">
              <!-- Unit / week title -->
              <div :class="'Unit' + unit.unitNum + 'Week' + week.week">
                <unit-week-title
                  :ref="'unit' + (i + 1) + '' + (j + 1)"
                  :unit="unit"
                  :week="week"></unit-week-title>
              </div>
              <div v-for="(category, index) in week.categories" :key="index">
                <div v-if="category.sum > 0">
                  <group-title
                    :groupTitle="category.categoryName"
                    :groups="category.sum"
                  ></group-title>
                </div>
                <div class="activities">
                  <div v-for="activity in category.activities"  :key="activity.id" class="m-b-sm">
                    <lesson-card :lesson="activity" @click.native="previewLesson(activity.id)"></lesson-card>
                  </div>
                  <div v-if="category.customActivities.length > 0">
                    <el-card class="el-card">
                      <div class="text-box">
                        <div :id="category.id">
                          <el-tag type="success" class="m-b-xs">Other Activities ({{ category.customActivities.length }})</el-tag>
                          <ul v-for="(activity, index) in category.customActivities" :key="index">
                            <li :title="activity.name" class="custom-activity-name">
                              {{ activity.name }}
                            </li>
                            <div class="activity-measures">
                              <el-tag v-for="(m, index) in activity.measure" :key="index" size="mini" type="info">{{ m }}</el-tag>
                            </div>
                          </ul>
                        </div>
                       </div>
                      <!-- 分隔线-->
                      <el-divider v-show="category.overflow" />
                      <div v-show="category.overflow" class="custom-lesson-card-footer">
                        <el-button type="text" @click="openSeeMoreDialog(category.customActivities)">
                          See More
                        </el-button>
                      </div>
                    </el-card>
                  </div>
                </div>
              </div>
              <!-- center 组 -->
              <div v-for="(category, index) in week.centerCategories" :key="index">
                <div v-if="category.sum > 0">
                  <group-title
                    :groupTitle="groupCenterName"
                    :groups="category.sum"
                  ></group-title>
                </div>
                <div class="activities">
                  <div v-for="activity in category.activities"  :key="activity.id" class="m-b-sm">
                    <lesson-card :lesson="activity" @click.native="previewLesson(activity.id)"></lesson-card>
                  </div>
                  <div v-if="category.customActivities.length > 0">
                    <el-card class="el-card">
                      <div class="text-box">
                        <div :id="category.id">
                          <el-tag type="success" class="m-b-xs">Other Activities ({{ category.customActivities.length }})</el-tag>
                          <ul v-for="(activity, index) in category.customActivities" :key="index">
                            <li :title="activity.name" class="custom-activity-name">
                              {{ activity.name }}
                            </li>
                            <div class="activity-measures">
                              <el-tag v-for="(m, index) in activity.measure" :key="index" size="mini" type="info">{{ m }}</el-tag>
                            </div>
                          </ul>
                        </div>
                       </div>
                      <!-- 分隔线-->
                      <el-divider v-if="category.overflow" />
                      <div v-if="category.overflow" class="custom-lesson-card-footer">
                        <el-button type="text" @click="openSeeMoreDialog(category.customActivities)">
                          See More
                        </el-button>
                      </div>
                    </el-card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6" class="">
          <unit-directory @goSelect="goSelect" :units="units" :title="$t('loc.activitys')" :totalNum="totalNum"></unit-directory>
        </el-col>
      </el-row>
    </div>
    <div v-else style="height: 100%; background: #fff;">
      <empty-view></empty-view>
    </div>
    <!--模态窗口展示详情页-->
    <lesson-detail-dialog :lesson-id="previewLessonId" :show.sync="showDetail" new-tab-to="PublicLessonDetail">
      <!--详情页-->
      <lesson-detail :is-from-library="true" :lessonId="previewLessonId" :isDialog="true">
        <template slot="header-left" slot-scope="{lesson}">
          <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/> -->
          <lesson-like :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
          <lesson-favorite :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/>
        </template>
        <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
          <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                           :lesson="lesson"/>
        </template>
      </lesson-detail>
    </lesson-detail-dialog>
    <el-dialog :visible="showSeeMoreDialog" :before-close="closeSeeMoreDialog" append-to-body width="35%" class="custom-activities-detail">
      <h2 slot="title" class="m-b-xs">Other Activities ({{ customActivities.length }})</h2>
      <div class="custom-activities scrollbar">
        <ul>
          <li v-for="(activity, index) in customActivities" :key="index" :title="activity.name">
            <div class="m-b-sm">
              <div class="custom-activity-name">
                {{ activity.name }}
              </div>
              <div class="activity-measures">
                <el-tag v-for="(m, index) in activity.measure" :key="index" size="mini" type="info">{{ m }}</el-tag>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import HeaderBar from '../components/HeaderBar.vue'
import UnitDirectory from '../components/UnitDirectory.vue'
import UnitWeekTitle from '../components/UnitWeekTitle.vue'
import GroupTitle from '../components/GroupTitle.vue'
import LessonCard from '../../../lessonLibrary/components/LessonCard.vue'
import LessonDetailDialog from '../../../lessonLibrary/components/LessonDetailDialog'
import LessonLike from '../../../lessonLibrary/components/LessonLike'
import LessonFavorite from '../../../lessonLibrary/components/LessonFavorite'
import LessonDetail from '../../../lessonLibrary/components/LessonDetail'
// import LessonReadCount from '../../../lessonLibrary/components/LessonReadCount'
import LessonDownload from '../../../lessonLibrary/components/LessonDownload'
import EmptyView from '@/views/modules/lesson2/lessonCurriculum/components/EmptyView'
import Lessons2 from '@/api/lessons2'
import tools from '@/utils/tools'
import { mapState } from 'vuex'
export default {
  name: 'Activities',
  components: {
    HeaderBar,
    UnitDirectory,
    LessonCard,
    UnitWeekTitle,
    GroupTitle,
    LessonDetailDialog,
    LessonLike,
    LessonFavorite,
    // LessonReadCount,
    LessonDetail,
    LessonDownload,
    EmptyView
  },
  data () {
    return {
      curriculumId: '',
      curriculumName: '',
      type: '',
      units: [],
      loading: false,
      totalNum: 0,
      showDetail: false, // 是否显示预览课程弹窗
      previewLessonId: '', // 预览的课程 ID
      showSeeMoreDialog: false,
      customActivities: [] // 显示所有的手动活动
    }
  },
  computed: {
    ...mapState({
      planCenterType: state => state.lesson.planCenterType,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin
    }),
    // 课程中心名称
    groupCenterName () {
      if (this.planCenterType === 'K') {
        return this.$t('loc.unitPlannerStep3Stations')
      } else {
        return this.$t('loc.unitPlannerStep3Centers')
      }
    }
  },
  created () {
    this.curriculumId = this.$route.params.id
    this.curriculumName = this.$route.params.curriculumName
    this.type = this.$route.params.type
    this.public = this.$route.params.public
    this.getResource()
  },
  methods: {
    getResource () {
      this.loading = true
      Lessons2.getActivitiesList(this.curriculumId)
      .then(res => {
        this.totalNum = res.sum
        this.units = res.units
        this.units.forEach(unit => {
          unit.weeks.forEach(week => {
            week.categories.forEach(category => {
              category.id = tools.uuidv4()
              category.overflow = false
            })
            week.centerCategories.forEach(category => {
              category.id = tools.uuidv4()
              category.overflow = false
            })
          })
        })
        this.$nextTick(() => {
          let units = this.units
          units.forEach(unit => {
            unit.weeks.forEach(week => {
              week.categories.forEach(category => {
                let domId = category.id
                let dom = document.getElementById(domId)
                category.overflow = dom && dom.scrollHeight > 240
              })
              week.centerCategories.forEach(category => {
                let domId = category.id
                let dom = document.getElementById(domId)
                category.overflow = dom && dom.scrollHeight > 240
              })
            })
          })
          this.units = units
          this.$forceUpdate()
        })
        this.loading = false
      })
      .catch(err => {
        this.loading = false
      })
    },
    goSelect (i, j) {
      this.$el.querySelector('.Unit' + i + 'Week' + j).scrollIntoView({ block: 'start', behavior: 'smooth' })
    },
    // 课程预览
    previewLesson (lessonId) {
      if (!lessonId || lessonId.trim().length === 0) {
        return
      }
      this.previewLessonId = lessonId
      this.showDetail = true
    },
    openSeeMoreDialog (customActivities) {
      this.customActivities = customActivities
      this.showSeeMoreDialog = true
    },
    closeSeeMoreDialog () {
      this.showSeeMoreDialog = false
    }
  }
}
</script>

<style lang="less" scoped>
.resource-box {
  height: calc(100% - 50px);
  overflow: auto;
}
.bg-shadow {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}
.box-card {
  height: 100%;
}
.top-info {
  margin: 23px 24px 23px;
}
.pdf {
  float: right;
}
.pdf:hover {
  color: #10b3b7 !important;
  border-color: #b7e8e9;
  background-color: #e7f7f8;
}

.pdf-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-color: #dcdfe6;
  color: #606266;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: 0.1s;
  transition: 0.1s;
  font-weight: 400;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
  font-family: "Inter";
  font-style: normal;
  font-size: 14px;
  line-height: 17px;
}

.unit-title {
  width: 713px;
  height: 22px;

  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #323338;
  flex: none;
  order: 0;
  flex-grow: 1;
}

.activities {
  padding-left: 46px;
  padding-right: 55px;
  margin: 10px 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, 250px);
  grid-column-gap: 24px;
}
.activity {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px 0px 10px;
  gap: 8px;

  width: 240px;
  height: 286px;

  background: #ffffff;

  flex: none;
  order: 0;
  flex-grow: 0;
}

/deep/.el-card__body:first-of-type {
  padding: 20px;
  height: 285px;
  //超出边框部分隐藏
  overflow: hidden;
}

.custom-activity-name {
  list-style: disc;
  list-style-position: inside;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.text-box{
  //底部溢出的文字隐藏
  overflow: hidden;
  height: 240px;
  padding: 10px;
}
.el-card /deep/ .el-card__body {
  padding: 0;

  & > .lesson-name {
    //padding-left: 5px;
    //height: 32px;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    //line-height: 32px;
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
    padding: 0 5px;
  }

  & > .el-divider {
    //margin: 0 2px;
    //width: calc(100% - 4px);
    margin: 0;
    background-color: rgba(216, 216, 216, 0.4);
  }

  & > .custom-lesson-card-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    // padding: 0 5px;
  }
}
.activity-measures {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}
.custom-activities {
  height: 400px;
  overflow: auto;
  ul {
    padding-left: 18px;
    li {
      list-style: disc;
    }
  }
}

/deep/ .text-box > ul{
  padding-left: 18px;
}
/deep/ .text-box > ul > li {
  list-style: disc;
}

.custom-activities-detail {
  /deep/ .el-dialog__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  /deep/ .el-dialog__headerbtn {
    position: inherit;
  }
  /deep/ .el-dialog__body {
    padding: 0 30px 30px;
  }
}
</style>
