<template>
  <div v-loading="loading" class="h-full display-flex flex-direction-col" style="overflow: auto">
    <header-bar :title="$t('loc.curriculum11')" :leftTitle="curriculumName" :curriculumId="curriculumId" :type="type"></header-bar>
    <div v-if="totalNum > 0" class="h-full">
      <el-row type="flex" class="h-full">
        <el-col :span="18" class="bg-white lg-box-shadow lg-border-radius-8 h-full lg-scrollbar-show">
            <!-- Resource name / PDF title -->
          <div class="top-info">
            <span class="unit-title">{{ $t('loc.curriculum16') }}</span>
            <div class="pdf">
              <el-button type="primary"
                          v-show="false"
                        size="small"
                        icon="fa fa-file-pdf-o"
                        class="pdf-button"><span>PDF</span></el-button>
            </div>
          </div>
          <div v-for="unit in units" :key="unit.unitNum">
            <div v-for="week in unit.weeks" :key="week.week">
              <!-- Unit / week title -->
              <div :class="'Unit' + unit.unitNum + 'Week' + week.week">
                <unit-week-title
                  :unit="unit"
                  :week="week"></unit-week-title>
              </div>
              <!-- 其他分组 -->
              <div v-for="(category, index) in week.categories" :key="index">
                <div v-if="category.sum > 0">
                  <group-title :groupTitle="category.categoryName" :groups="category.sum"></group-title>
                  <div class="m-t-md m-l-xl m-r-xl">
                    <el-row :gutter="20">
                      <el-col :span="12" v-for="(item, index) in category.medias" :key="index">
                        <printable-attachment-item class="m-b-md" :file="getFile(item)" :notShowDelete="true"></printable-attachment-item>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <!-- center 分组 -->
              <div v-for="(category, index) in week.centerCategories" :key="index">
                <div v-if="category.sum > 0">
                  <group-title :groupTitle="category.categoryName" :groups="category.sum"></group-title>
                  <div class="m-t-md m-l-xl m-r-xl">
                    <el-row :gutter="20">
                      <el-col :span="12" v-for="(item, index) in category.medias" :key="index">
                        <printable-attachment-item class="m-b-md" :file="getFile(item)" :notShowDelete="true"></printable-attachment-item>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <unit-directory @goSelect="goSelect" :title="$t('loc.curriculum16')" :units="units" :totalNum="totalNum"></unit-directory>
        </el-col>
      </el-row>
    </div>
    <div v-else style="height: 100%; background: #fff;">
      <empty-view></empty-view>
    </div>
  </div>
</template>

<script>
import HeaderBar from '../components/HeaderBar.vue'
import UnitDirectory from '../components/UnitDirectory.vue'
import GroupTitle from '../components/GroupTitle.vue'
import UnitWeekTitle from '../components/UnitWeekTitle.vue'
import PrintableAttachmentItem from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumResources/PrintableAttachmentItem'
import EmptyView from '@/views/modules/lesson2/lessonCurriculum/components/EmptyView'
import Lessons2 from '@/api/lessons2'
export default {
  name: 'Printables',
  components: { HeaderBar, UnitDirectory, GroupTitle, UnitWeekTitle, PrintableAttachmentItem, EmptyView },
  data () {
    return {
      currentUnit: 1,
      coverURL:
        'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
      pptxUrl: require('../../../../../../assets/img/file/pptx.png'),
      doxUrl: require('../../../../../../assets/img/file/docx.png'),
      xlsUrl: require('../../../../../../assets/img/file/xls.png'),

      name: '',
      units: [],
      totalNum: 0,
      curriculumId: '',
      type: '',
      curriculumName: '',
      loading: false
    }
  },
  created () {
    this.curriculumId = this.$route.params.id
    this.curriculumName = this.$route.params.curriculumName
    this.type = this.$route.params.type
    this.getResource()
  },
  methods: {
    getResource () {
      this.loading = true
      Lessons2.getPrintableList(this.curriculumId)
      .then(res => {
        this.totalNum = res.sum
        this.units = res.units
        this.loading = false
      })
      .catch(err => {
        this.loading = false
      })
    },
    getFile (file) {
      file.name = file.sourceFileName
      return file
    },
    goSelect (i, j) {
      this.$el.querySelector('.Unit' + i + 'Week' + j).scrollIntoView({ block: 'start', behavior: 'smooth' })
    }
  }
}
</script>

<style lang="less" scoped>
.resource-box {
  height: calc(100% - 50px);
  overflow: auto;
}
.bg-shadow {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}
.top-info {
  margin: 23px 24px 23px;
}

.pdf {
  float: right;
}
.pdf-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-color: #dcdfe6;
  color: #606266;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: 0.1s;
  transition: 0.1s;
  font-weight: 400;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
  font-family: 'Inter';
  font-style: normal;
  font-size: 14px;
  line-height: 17px;
}

.el-button:focus, .el-button:hover {
    color: #10b3b7;
    border-color: #b7e8e9;
    background-color: #e7f7f8;
}
.unit-title {
  width: 713px;
  height: 22px;

  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #323338;
  flex: none;
  order: 0;
  flex-grow: 1;
}

.items {
  padding-left: 46px;
  padding-right: 55px;
  margin-top: 10px;
}

.small-group {
  margin-top: 20px;
  margin-left: 50px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 8px;
  gap: 4px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  color: #10b3b7;
  width: 160px;
  height: 26px;
  background: rgba(16, 179, 183, 0.1);
  border: 1px solid #10b3b7;
  border-radius: 14px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.week-line {
  margin-top: 11.5px;
  margin-left: 38px;
  margin-right: 24px;
  margin-bottom: 18px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 12px 0px 0px;
  gap: 12px;
  width: auto;
  height: 32px;
  background: #f2f6fe;
  border-radius: 2px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.week {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 12px;
  gap: 8px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #ffffff;
  flex: none;
  order: 0;
  flex-grow: 0;
  width: auto;
  height: 32px;
  background: #85abf0;
  border-radius: 2px;
}
.week-total {
  float: right;
  margin-left: auto;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #676879;
}

.itemImage {
  float: left;
  width: 32.98px;
  height: 40px;

  left: 8.54%;
  right: 9.01%;
  top: 0%;
  bottom: 0%;

  background: #ff7861;
}
.item {
  margin-left: 16px;
  margin-bottom: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  gap: 4px;

  width: 382.5px;
  height: 60px;

  background: #f5f6f8;
  border-radius: 4px;

  /* Inside auto layout */

  flex: none;
  order: 0;
  flex-grow: 1;
}
.itemTitle {
  width: 314.5px;
  height: 22px;

  /* Regular/14px */

  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;

  color: #323338;

  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.itemSize {
  width: 314.5px;
  height: 22px;
  margin-right: 56px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;

  color: #999999;

  align-self: stretch;
}
</style>
