<template>
  <div class="add-margin-b-36">
    <div class="books">
      <div v-for="(book, index) in groups"
                :key="index">
        <book v-if="book.type == 'BOOK'" :book="getBookInfo(book)"></book>
        <video-book v-else :video="getBookInfo(book)" ></video-book>
      </div>
    </div>
  </div>
</template>

<script>
import Book from './Book.vue'
import VideoBook from './VideoBook.vue'
export default {
  props: ['groups'],
  components: { Book, VideoBook },
  methods: {
    getBookInfo (book) {
      let bookArray = JSON.parse(book.book)
      return bookArray[0]
    }
  }
}
</script>

<style lang="less" scoped>
.bookImage {
  width: 240px;
  height: 137px;
  margin-bottom: 5px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.books {
  padding-left: 46px;
  padding-right: 55px;
  margin: 10px 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, 140px);
  grid-column-gap: 24px;
}
.book {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px 0px 10px;

  width: auto;
  height: auto;

  background: #ffffff;
  border-radius: 4px;

  flex: none;
  order: 0;
  flex-grow: 0;
}

.bookTitle {
  width: 127.5px;
  height: 22px;

  /* Regular/14px */

  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  /* identical to box height, or 157% */

  /* 文字颜色/一级 #323338 */

  color: #323338;

  /* Inside auto layout */

  flex: none;
  order: 0;
  flex-grow: 0;
}
.bookImage {
  left: 0%;
  right: 0%;
  top: 0%;
  bottom: 0%;
  width: 143px;
  height: 152px;
  border-radius: 4px;
}
.bookAuthor {
  width: 127.5px;
  height: 22px;
  /* Regular/14px */

  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  /* identical to box height, or 157% */

  /* 文字颜色/二级 #676879 */

  color: #676879;

  /* Inside auto layout */

  flex: none;
  order: 1;
  flex-grow: 0;
}
</style>
