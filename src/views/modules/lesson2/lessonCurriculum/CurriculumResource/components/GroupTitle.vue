<template>
  <div>
    <div class="small-group">
      {{groupTitle}} ({{ groups }})
    </div>
  </div>
</template>

<script>
export default {
  props: ['groupTitle', 'groups']
}
</script>

<style lang="less" scoped>

.small-group {
  margin-top: 10px;
  margin-left: 40px;
  margin-bottom: 6px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 8px;
  gap: 4px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  color: #10b3b7;
  width: fit-content;
  height: 26px;
  background: rgba(16, 179, 183, 0.1);
  border: 1px solid #10b3b7;
  border-radius: 14px;
  flex: none;
  order: 0;
  flex-grow: 0;
}
</style>