<template>
  <div style="position:sticky; top: 0; z-index: 1000;">
    <el-row v-if="false" class="header">
      <div style="position: absolute;overflow: hidden; max-width: 40%;">
        <div class="display-flex">
          <!-- <router-link to> -->
            <img
              style="height: 20px;margin: 15px 0; cursor: pointer;"
              src="@/assets/img/us/back_home.png"
              @click="goBack"
            />
          <!-- </router-link> -->
          <span class="m-l-md left-title" :title="'Curriculum / ' + leftTitle + ' / Key Resources'">
            <span @click="goBack" style="color: #676879;cursor:pointer">
              Curriculum&nbsp;/&nbsp;
            </span>
            <span style="color: #323338">
              {{ leftTitle }}
            </span>
            <span>
              &nbsp;/&nbsp;Key Resources
            </span>
          </span>
        </div>
      </div>
      <div class="title">{{ title }}</div>
    </el-row>
  </div>
</template>

<script>
export default {
  props: ['leftTitle', 'title', 'curriculumId', 'type'],
  methods: {
    goBack () {
      this.$router.push({
        name: 'lessonCurriculum',
        params: {
          type: this.type,
          id: this.curriculumId
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.header {
  height: 50px;
  padding: 0 35px;
  line-height: 50px;
  color: #111C1C;
  background-color: #e4eaec;
  text-align: center;
}
.title {
  color: #323338;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "Inter";
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  max-width: 15%;
  margin: 0 auto;
}

.back-title1 {
  //超出部分显示省略号
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 30%;
}
.left-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
