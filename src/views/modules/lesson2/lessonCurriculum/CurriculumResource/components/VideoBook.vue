<template>
  <div>
    <div class="video" @click="preview">
      <div style="position: relative">
        <el-image fit="cover" :src="thumbnailUrl"></el-image>
        <img src="@/assets/img/icon/video.png" class="play-button lg-pointer"/>
      </div>
      <div class="lesson-share-media-info-title color-323338" :title="title"
           style="overflow:hidden; white-space:nowrap; text-overflow:ellipsis">
        {{ title }}
      </div>
      <div class="lesson-share-media-info-author color-676879" v-if="false">
        {{ $t('loc.lessons2LessonDetailToAuthorName') }} {{ authorName }}
      </div>
    </div>
    <!--视频预览弹框-->
    <el-dialog :visible.sync="showPlayer" width="691px" class="popup-container" append-to-body>
      <div slot="title" style="height: 21px;font-size: 24px">{{ $t('loc.preview') }}</div>
      <iframe :src="showPlayer && `https://www.youtube.com/embed/${video.id.videoId}`" v-if="video"
              width="647px" height="428px" allowfullscreen/>
      <div slot="footer">
        <el-button @click="closePreview">{{ $t('loc.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LessonMediaViewer from "@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer";

export default {
  name: "LessonShareVideoBook",
  components: {LessonMediaViewer},
  props: ['video', 'book', 'playable'],
  computed: {
    thumbnailUrl() {
      let {snippet: {thumbnails: {default: {url}}}} = this.video;
      return url
    },
    title() {
      let {snippet: {title}} = this.video;
      return title
    },
    authorName() {
      let {volumeInfo = {}} = this.book || {};
      let {authors = []} = volumeInfo;
      let [authorName] = authors;
      return authorName
    }
  },
  data() {
    return {
      showPlayer: false
    }
  },
  methods: {
    preview() {
      if (window.lessonVideo) {
        window.lessonVideo.pause();
      }
      this.showPlayer = true;
    },
    closePreview() {
      this.showPlayer = false;
    },
  }
}
</script>

<style lang="less" scoped>
.video {
  cursor: pointer;
  width: 140px;
  display: flex;
  flex-flow: column;
  justify-content: center;
  gap: 5px;
  font-size: 14px;
}

.lesson-share-media-info-title {
  text-align: center;
  color: #2A343A;
  font-weight: 500;
}

.lesson-share-media-info-author {
  text-align: center;
  color: rgba(52, 62, 67, 0.73);
  font-weight: 400;
}
/deep/.el-image {
  width: 140px;
  height: 150px;
}
.play-button {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
</style>