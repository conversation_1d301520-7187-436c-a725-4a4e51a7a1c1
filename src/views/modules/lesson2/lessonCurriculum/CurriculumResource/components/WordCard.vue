<template>
  <div>
    <div >
      <!-- small group title todo-->
      <!-- <div class="word-group-title"> {{wordGroup.name}} ({{wordGroup.words.length}})</div> -->
      <!-- small group body -->
      <div class="words">
        <el-row :gutter="20"
                v-for="item in category.itemModels"
                :key="item.itemId">
          <div v-if="item.resourceModels.length > 0" style="margin-top:16px" class="item-name">{{ item.itemName }} ({{ item.resourceModels.length }})</div>
          <el-col :xs="8" :sm="4" :md="4" :lg="4" :xl="4" v-for="word in item.resourceModels"
                  :key="word.id"
                  class="word">
            <div class="wordImage">
              <el-image class="wordImage"
                   :src="word.mediaUrl">
                <div slot="error" class="image-slot">
                  <div class="flex-center-center wordImage">
                    <span class="overflow-ellipsis-three word-break font-weight-semibold font-size-18 line-height-22 text-black-mon">
                      {{ word.name }}
                    </span>
                  </div>
                </div>
              </el-image>
            </div>
            <div class="wordTitle flex-center-center" :title="word.name">
              <span class="overflow-ellipsis l-h-1x text-black-mon font-weight-semibold font-size-16">
                {{ word.name }}
              </span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>

</template>

<script>
export default {
  props: ['category']
}
</script>

<style lang="less" scoped>
.only-word {
  font-size: 35px;
  text-align: center;
}
.words {
  padding-left: 46px;
  padding-right: 55px;
  margin: 10px 0;
}

.wordImage {
  width: 183px;
  height: 104px;
  padding-left: 0px;
  padding-right: 0px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  flex: none;
  order: 0;
  flex-grow: 0;
  overflow: unset;
}
.word {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 16px;
  width: auto;
  height: 144px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.wordTitle {
  display: flex;
  flex-direction: row;
  justify-content: center;
  overflow: hidden;
  gap: 10px;

  width: 183.25px;
  height: 40px;

  background: #f5f6f8;

  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;

  border-bottom-left-radius: 4px;
  border-bottom-left-radius: 4px;
  color: #323338;
}
.overflow-ellipsis-three {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.item-name {
  font-weight: bold;
  margin-left: 10px;
  font-size: 16px;
  color: #323338 !important;
}
</style>
