<template>
  <div class="m-l-md bg-white unit-directory">
    <el-row class="tac">
      <el-col  >
        <h5 class="title">{{ title }} <span v-if="totalNum">({{ totalNum }})</span></h5>
        <el-collapse accordion v-model="activeName">
          <template v-for="(unit, index) in units" >
            <el-collapse-item  :key="index" :name="index" v-if="unit.weeks.length > 0">
              <!-- 单元title -->
              <template slot="title">
                <span class="unit-title" :title="`Unit` + unit.unitNum + `: ` + unit.unitName">
                  {{ unit.unitNum >=1 ? $t('loc.curriculum102', {num: unit.unitNum}) : 'Special Unit ' + unit.unitNum }} {{ unit.unitName }}
                </span>
              </template>
              <div v-for="(week, weekIndex) in unit.weeks" id="itemTitle" :key="weekIndex" :class="unitNumer == unit.unitNum && weekNumber == week.week ? 'action' : ''" @click="selectUnitWeek(unit.unitNum, week.week)">
                {{ $t('loc.curriculum103', {num: week.week}) }}
              </div>
            </el-collapse-item>
          </template>
        </el-collapse>
      </el-col>
    </el-row>
  </div>
</template>
<script>

export default {
  props: {
    title: {
      type: String
    },
    totalNum: {
      type: Number
    },
    units: {
      type: Array
    }
  },
  data () {
    return {
      activeName: 0,
      unitNumer: 1,
      weekNumber: 1
    }
  },
  mounted () {
    // 组件挂载完成后找到第一个单元和第一个周赋值选中的单元和周次
    this.$nextTick(() => {
      this.unitNumer = this.units[0].unitNum
      this.weekNumber = this.units[0].weeks[0].week
    })
  },
  methods: {
    selectUnitWeek (i, j) {
      this.unitNumer = i
      this.weekNumber = j
      this.$emit("goSelect", i, j);
    }
  }
}

</script>

<style lang="less" scoped>
.unit-directory {
  position: sticky;
  top: 0;
  font-family: "Inter";
  font-size: 14px;
  color: #323338;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
  border-radius: 8px;
}
.unit-title {
  text-overflow: ellipsis;
  overflow: hidden;
  width: max-content;
  white-space: nowrap;
}

.title {
  height: 19px;
  margin: 22.5px 20px 14px 14.5px;
  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #323338;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/deep/ .el-collapse-item__header {
  font-size: 14px;
  padding: 12px 16px;
  border-bottom: none;
  border-radius: 8px;
}
.el-collapse {
  border-top: none;
  border-bottom: none;
}
/deep/.el-collapse-item__wrap {
  border-bottom: none;
}
/deep/.el-collapse-item__content {
  padding-bottom: 0 !important;
}
/deep/ .el-collapse-item__content > div {
  padding: 12px 33px;

  height: 48px;
  font-family: "Inter";
  font-weight: 400;
  font-size: 14px;
  border-left: 3px solid transparent;
}
/deep/ .el-collapse-item__content > div:hover {
  background: rgba(16, 179, 183, 0.1);
  color: #10b3b7;
  cursor: pointer;
}
.action {
  background: rgba(16, 179, 183, 0.1);
  font-weight: bold;
  color: #10b3b7;
  border-left: 3px solid #10b3b7 !important;
}
</style>
