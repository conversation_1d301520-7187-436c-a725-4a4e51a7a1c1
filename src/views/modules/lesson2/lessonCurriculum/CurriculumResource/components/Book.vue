<template>
  <div class="book">
    <a @click=" openBookUrl(bookLink)" >
      <el-image :src="thumbnailUrl" fit="cover"></el-image>
    </a>
    <a @click="openBookUrl(bookLink)" >
      <div class="lesson-share-media-info-title color-323338" :title="title">
        {{ title }}
      </div>
    </a>
    <div class="lesson-share-media-info-author color-676879" :title="authorName" v-if="authorName">
      by {{ authorName }}
    </div>
  </div>
</template>

<script>
import LessonMediaViewer from "@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer";
import tools from '@/utils/tools'
export default {
  name: "Book",
  components: {LessonMediaViewer},
  props: ['book'],
  computed: {
    thumbnailUrl() {
      let {volumeInfo = {}} = this.book || {};
      let {imageLinks = {}} = volumeInfo;
      let {thumbnail} = imageLinks;
      return thumbnail;
    },
    title() {
      let {volumeInfo = {}} = this.book || {};
      let {title} = volumeInfo;
      return title;
    },
    authorName() {
      let {volumeInfo = {}} = this.book || {};
      let {authors = []} = volumeInfo;
      let [authorName] = authors;
      return authorName
    },
    bookId() {
      return this.book.id;
    },

    /**
     * 书的链接
     */
    bookLink () {
      let { volumeInfo = {} } = this.book || {}
      let { infoLink } = volumeInfo
      return infoLink
    }
  },
  methods:{
    openBookUrl(url) {
      tools.openUrlWithWebAndIPad(url)
    },
  }
}
</script>

<style lang="less" scoped>

.book {
  width: 140px;
  display: flex;
  flex-flow: column;
  justify-content: center;
  font-size: 14px;
}

.lesson-share-media-info-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 140px;
  color: #2A343A;
  font-family: Inter;
}

.lesson-share-media-info-author {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-family: Inter;
  width: 140px;
  color: rgba(52, 62, 67, 0.73);
  font-weight: 400;
}

/deep/.el-image {
  width: 140px;
  height: 150px;
}
</style>
