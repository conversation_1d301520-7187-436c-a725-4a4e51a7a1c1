<template>
  <div v-if="week.sum > 0" class="week-line">
    <span class="week" :title="`Unit` + unit.unitNum + ` ` + unit.unitName + ` / Week ` + week.week">
      <span class="unit-info">
        {{ unit.unitNum >=1 ? $t('loc.curriculum102', {num: unit.unitNum}) : 'Special Unit ' + unit.unitNum }} {{ unit.unitName }}&nbsp;
      </span>
      <span style="">
        / {{ $t('loc.curriculum103', {num: week.week}) }}
      </span>
    </span>
    <span class="week-total">{{ $t('loc.total') }} ({{ week.sum }})</span>
  </div>
</template>

<script>
export default {
  props: ['unit', 'week']
}
</script>

<style lang="less" scoped>
.week-line {
  margin: 23.5px 24px 16px 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 12px 0px 0px;
  gap: 12px;
  width: auto;
  height: 32px;
  background: #f2f6fe;
  border-radius: 2px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.week {
  display: flex;
  align-items: center;
  padding: 0px 12px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #ffffff;
  height: 32px;
  background: #85abf0;
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  max-width: 30%;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.unit {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.week-total {
  float: right;
  margin-left: auto;
  font-family: 'Inter';
  font-style: normal;
  font-weight: bold;
  font-size: 14px;
  line-height: 22px;
  color: #676879;
}

.unit-info{
  //超出部分显示省略号
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  display: inline-block;
  width: auto;
}
</style>