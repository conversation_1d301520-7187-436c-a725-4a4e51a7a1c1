<template>
  <div>
    <div class="curriculum-item lg-pointer" :class="{'curriculum-item-selected': selected}">
      <!-- 课程名称 -->
      <div class="curriculum-name-text">{{ curriculum.name ? curriculum.name: $t('loc.draftCurriculum')}}</div>
      <div class="display-flex m-t-4">
      <!-- 头像 -->
        <div>
          <el-avatar size="small" :src="getUserAvatar(curriculum.authorAvatar)"></el-avatar>
        </div>
        <!-- 姓名 -->
        <div class="curriculum-author author-text">{{ curriculum.authorName | formatUserName }}</div>
      </div>
      <div class="m-t-4">
        <el-tag size="small" type="info" class="m-r-xs age-tag" v-for="(age, index) in getAgeGroups(curriculum.ages)" :key="index">{{ age.name }}</el-tag>
      </div>
    </div>
  </div>
</template>

<script>
import constants from '@/utils/constants'
export default {
  props: ['selected', 'curriculum'],
  methods: {
    // 获取用户头像
    getUserAvatar (str) {
      if (str === null || str === '') {
        return constants.userAvatarURL
      }
      return str
    },
    // 获取年龄组
    getAgeGroups (ages) {
      let ageGroups = []
      if (ages) {
        let agesArray = JSON.parse(ages)
        return agesArray
      }
      return ageGroups
    }
  }
}
</script>

<style lang="less" scoped>
.curriculum-item {
  background: #fff;
  padding: 16px;
  border-left: 3px solid #fff;
}
.curriculum-item-selected {
  border-left: 3px solid #10b3b7;
  background: #E7F7F8;
}
.curriculum-name-text{
  color: #111c1c;
  margin-bottom: 3px;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 21px;
  word-wrap: break-word;
  word-break: break-word;
  // 让文本只能展示两行
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.author-text {
  color: #676879;
  height: 28px;
  margin-left: 8px;
  line-height: 28px;
  word-wrap: break-word;
  word-break: break-all;
  width: 100%;
  white-space: nowrap;  /*把文本强制显示在一行*/
  overflow: hidden;  /*隐藏超出部分的文字*/
  text-overflow: ellipsis;  /*超出显示省略号*/
}
.age-tag {
  color: #676879;
}
.m-t-4 {
  margin-top: 4px;
}
</style>
