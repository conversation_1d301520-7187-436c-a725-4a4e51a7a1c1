<template>
  <div style="display: table; width: 100%;">
    <div v-if="isEditor">
      <div class="display-flex align-items">
        <el-button plain size="small" icon="el-icon-my-export" class="font-color-black font-size-14" @click="openDialog">{{$t('loc.curriculum54')}}</el-button>
        <el-button plain size="small" icon="el-icon-plus" class="font-color-black font-size-14" @click="addWeekPlan">{{$t('loc.curriculum55')}}</el-button>
      </div>
    </div>
    <el-dialog :visible="dialogVisable" :before-close="closeDialog">
      <el-dialog
        :visible.sync="previewDialog"
        custom-class="preview-plan-dialog"
        append-to-body>
        <div slot="title" class="lg-color-text-primary font-size-20">{{$t('loc.planpre')}}</div>
        <unit-plan :previewing="previewDialog" :edit="false" :planId="previewPlanId" :showLastReflection="showLastReflection" :viewOnly="true"></unit-plan>
        <span slot="footer" class="dialog-footer">
          <el-button plain @click="previewDialog = false">{{ $t('loc.close') }}</el-button>
        </span>
      </el-dialog>
      <div slot="title" class="lg-color-text-primary font-size-20">{{$t('loc.curriculum54')}}</div>
      <div>
        <el-row :gutter="20">
          <el-col :span="6">
            <!-- <teacher-filter :userId="userId"></teacher-filter> -->
            <agency-staff-filter class="lg-scrollbar-hidden plan-box" @selectedUserId="selectedUserId"></agency-staff-filter>
          </el-col>
          <el-col :span="18" class="lg-scrollbar-show plan-box" style="max-height:400px;">
            <el-row :gutter="20" v-if="plans.length > 0">
              <el-col  class="m-t-sm" v-for="i in plans" :key="i.id" :span="12">
                <el-card :body-style="{ padding: '0px' }">
                  <div class="plan-card">
                    <div class="plan-action">
                      <div class="text-center" style="margin-top:30%">
                        <el-button plain type="" @click="previewPlan(i.id)">{{ $t('loc.planPreview') }}</el-button>
                        <el-button type="primary" @click="selectPlan(i.id)">{{$t('loc.select')}}</el-button>
                      </div>
                    </div>
                    <div style="background-color: #BFE8FF;padding: 10px;" class="border-radius-6">
                      <div style="margin-top: -10px; height:43px;" class="clearfix" >
                        <div class="lg-color-text-primary title-font-16"><span class="overflow-ellipsis">{{ i.theme }}</span></div>
                        <div class="display-flex justify-content-between">
                          <div v-if="i.type=='NORMAL'">{{ $moment(i.fromAtLocal).format('MM/DD') }}-{{ $moment(i.toAtLocal).format('MM/DD') }}</div>
                          <div v-if="i.week">Week {{ i.week }}</div>
                        </div>
                      </div>
                      <div>
                        <img style="width:100%;height:100%" src="@/assets/img/lesson2/plan/week_plan_preview.png">
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-row v-else>
              <empty-view :text="$t('loc.curriculum37')"></empty-view>
            </el-row>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!-- 占位div -->
    <unit-plan
      :isOnlyView="isOnlyView"
      :isFromUnitDetail="isFromUnitDetail"
      :isInterrupted="isInterrupted"
      :key="renderKey"
      ref="unitPlan"
      v-if="planId"
      :adminEdit="true"
      :previewing="previewDialog"
      :generatedMaterials="week.generatedMaterials"
      :frameworkId="frameworkId"
      :framework-data="frameworkData"
      :edit="isEditor"
      :draging="draging"
      :planId="planId"
      :adaptedGroupName="adaptedGroupName"
      :showLastReflection="showLastReflection"
      :exemplar="exemplar"
      @generate="getMaterialsList"
      @callAdaptUnitPlan="callAdaptUnitPlan"
      @callEditUnitLesson="callEditUnitLesson"
      @callInterrupted="callInterrupted"
      :curriculum-id="week && week.curriculumId"
      :unit-id="week && week.unitId"></unit-plan>
    <generate-materials-list  :is-edit="isEditor" ref="generateMaterialsListRef" v-show="planId && showMaterialsList" :plan-id="planId" :curriculum-id="week && week.curriculumId" :unit-id="week && week.unitId" :unit-num="week && week.unitNum" :week-title="week && week.title" :frameworkId="frameworkId" :framework-name="frameworkName" :framework-link-url="frameworkLinkUrl" @checkCompleted="checkCompleted" :show="show"></generate-materials-list>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import AgencyStaffFilter from './AgencyStaffFilter.vue'
import Lessons2 from '@/api/lessons2'
import EmptyView from './EmptyView.vue'
import UnitPlan from '@/views/modules/lesson2/lessonCurriculum/CurriculumWeekPlan/UnitPlan'
import GenerateMaterialsList from '@/views/modules/lesson2/lessonCurriculum/CurriculumWeekPlan/GenerateMaterialsList'
import LessonDrag from '@/views/modules/lesson2/lessonPlan/components/LessonDrag'
export default {
  name: 'WeekPlanSelector',
  components: {
    EmptyView,
    AgencyStaffFilter,
    UnitPlan,
    GenerateMaterialsList,
    LessonDrag
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    userId () {
      return this.currentUser.user_id
    }
  },
  props: {
    week: {
      type: Object
    },
    index: {
      type: Number
    },
    planId: {
      type: String
    },
    edit: {
      type: Boolean,
      default: true
    },
    showLastReflection: {
      type: Boolean,
      default: false
    },
    isEditor: {
      type: Boolean
    },
    frameworks: {
      type: Array
    },
    frameworkId: {
      type: String
    },
    frameworkName: {
      type: String
    },
    frameworkLinkUrl: {
      type: String
    },
    frameworkData: {
      type: Array,
      default: () => []
    },
    show: {
      type: Boolean
    },
    isFromUnitDetail: {
      type: Boolean,
      default: false
    },
    showApply: {
      type: Boolean,
      default: false
    },
    exemplar: {
      type: Boolean,
      default: false
    },
    isOnlyView: {
      type: Boolean,
      default: false
    },
    adaptedGroupName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogVisable: false,
      keyword: '',
      plans: [],
      previewPlanId: undefined,
      previewDialog: false,
      renderKey: 0,
      draging: false,
      showMaterialsList: false, // 是否显示资源列表
      isInterrupted: false // 是否中断，不需要再去判断是否还有未生成课程的活动
    }
  },
  watch: {
    week: {
      immediate: true,
      handler (val) {
        if (val) {
          this.showMaterialsList = val.generatedMaterials
        }
      }
    },
    planId (val) {
      this.resetDragStatus()
      if (val && this.week && this.week.planId && !this.week.generatedMaterials) {
        this.showMaterialsList = false
      }
    },
    // 当前周计划是否显示
    show (val) {
      if (val) {
        this.$refs.unitPlan && this.$refs.unitPlan.initCategoryHeight()
      }
    }
  },
  methods: {
    getPlan () {
      this.$refs.unitPlan.getPlan()
    },
    // 打开选择周计划弹窗
    openDialog () {
      // 点击选择周计划按钮埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_week_plan_select')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_week_plan_select')
      }
      if (this.frameworkId) {
        this.dialogVisable = true
      } else {
        this.$message.error(this.$t('loc.curriculum74'))
      }
    },
    // 关闭选择周计划弹窗
    closeDialog () {
      this.dialogVisable = false
    },
    // 获取该老师的周计划列表
    getWeekPlans (userId) {
      let params = { userId: userId, pageSize: 1000 }
      Lessons2.getAllPlanByUserId(params)
      .then(res => {
        this.plans = res.items
      })
    },
    // 设置选中的教师
    selectedUserId (userId) {
      if (userId) {
        this.getWeekPlans(userId)
      }
    },
    // 预览周计划
    previewPlan (planId) {
      this.previewPlanId = planId
      this.previewDialog = true
    },
    // 选择周计划
    selectPlan (planId) {
      Lessons2.SelectWeekPlan(planId, this.frameworkId)
      .then(res => {
        this.$emit('callAddWeekPlan', res.id, this.week.id, this.index)
      })
      .catch(error => {})
      this.dialogVisable = false
    },
    // 添加周计划
    addWeekPlan () {
      // 点击添加周计划按钮埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_week_plan_add')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_week_plan_add')
      }
      if (this.frameworkId) {
        this.$emit('callAddWeekPlan', '', this.week.id, this.index)
      } else {
        this.$message.error(this.$t('loc.curriculum74'))
      }
    },
    // 获取周计划材料
    getMaterialsList (res) {
      this.showMaterialsList = true
      this.$refs.generateMaterialsListRef.getMaterialsList(res)
    },
    // 中断回调
    callInterrupted () {
      this.isInterrupted = true
    },
    callAdaptUnitPlan (item) {
      this.$emit('callAdaptUnitWeekPlan', item)
    },
    callEditUnitLesson (item) {
      this.$emit('callEditUnitWeekPlan', item)
    },
    // 检查是否完整
    checkCompleted () {
      let completed = this.$refs.unitPlan.checkCompleted()
      this.$refs.generateMaterialsListRef.checkCompleted = completed
    },
    // 重置拖拽状态
    resetDragStatus () {
      if (this.draging) {
        this.$nextTick(() => {
          this.draging = false
          let lessonDrag = this.$refs.lessonDrag
          if (lessonDrag) {
            lessonDrag.draging = false
          }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.plan-box {
  max-height: 400px;
  overflow: auto;
}
.plan-action {
  position: absolute;
  visibility: hidden;
  height: 100%;
  width: 100%;
  background: rgba(196, 197, 203, 0.8);
}
.plan-card {
  position:relative
}

.plan-card:hover {
  cursor: pointer;
  .plan-action {
    visibility: visible ;
  }
}

/deep/ .el-icon-my-export {
  width: 12px;
  height: 12px;
  background: url('../../../../../assets/img/lesson2/curriculum/select_week_plan_icon.svg') no-repeat;
  font-size: 16px;
  background-size: cover;
}

/deep/ .el-icon-my-export:before{
  font-size: 16px;
}
/deep/.preview-plan-dialog {
  width: fit-content;
  min-width: 60%;
  .el-dialog__body {
    padding: 24px 20px;
  }
}
</style>
