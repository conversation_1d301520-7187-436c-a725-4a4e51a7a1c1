<template>
<!-- 单个材料的组件 -->
 <div class="lg-pa-8 scrollbar2 white-background">
   <div v-for="(lessonMaterial,index) in lessonMaterials" :key="index" class="add-padding-b-8">
     <div class="font-weight-semibold font-size-13 line-height-20 text-black-mon word-break">
       {{lessonMaterial.name}}
       <i  @click="editMaterial(lessonMaterial)" v-if="lessonMaterial.type === 'CUSTOM' && isEdit" class="el-icon-edit add-margin-l-5 lg-pointer"></i>
     </div>
     <el-row>
       <el-col :span="getMaterialUrl(lessonMaterial.material) ? 18 : 24">
         <div v-for="(description,index) in lessonMaterial.material.descriptions" :key="index">
           <span class="lesson-field-value" v-html="getDescription(description)"></span>
         </div>
         <div class="lesson-material-attachment-item" v-for="attachment in lessonMaterial.material && lessonMaterial.material.attachmentMedias" :key="attachment.id">
           <img class="lesson-material-attachment-item-img" :src="fileIcon(attachment)" alt="">
           <div class="lesson-material-attachment-item-title" :title="attachment.sourceFileName">
             <span class="overflow-ellipsis">{{ attachment.sourceFileName.substring(0, attachment.sourceFileName.lastIndexOf('.')).trim() }}</span>
             <span style="flex-shrink: 0">.{{ attachment.sourceFileName.substring(attachment.sourceFileName.lastIndexOf('.') + 1) }}</span>
             <span style="flex-shrink: 0">({{ formatSize(attachment.size) }})</span>
           </div>
           <a class="add-margin-lr-6"  @click="downloadMaterialAttachFile(attachment)">
             <el-button icon="el-icon-download" size="medium" type="text">
             </el-button>
           </a>
         </div>
       </el-col>
       <el-col   :span="6" v-if="getMaterialUrl(lessonMaterial.material)" style="float: right">
          <curriculum-media-viewer :url="getMaterialUrl(lessonMaterial.material)" :coverMediaImg="getMaterialCover(lessonMaterial.material)" :preview="true" :clickToPreview="true"/>
       </el-col>
     </el-row>

   </div>
 </div>
</template>

<script>
import AppUtil from '../../../../../utils/app'
import tools from '@/utils/tools'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
import CurriculumMediaViewer from './CurriculumMediaViewer.vue'
import { mapState } from 'vuex'
export default {
  name: 'CurriculumMaterials',
  components: { CurriculumMediaViewer },
  data () {
    return {
      fileIcons: { doc, docx, pdf, ppt, pptx, xls, xlsx, file }
    }
  },
  props: ['lessonMaterials','isEdit'],
  methods: {
    // 获取材料的内容
    getDescription (description) {
      if (tools.isComeFromIPad()) {
        // APP H5页面跳转浏览器
        $('a.textarea-item-a, a.lesson-material-card-link').on('click', function ($this) {
          AppUtil.appOpenUrlByBrowser($(this).attr('href'))
          return false
        })
      }
      // H5页面渲染
      return description.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ') + ' </a>'.replace('href')
    },
    // 获取材料附件的图标
    fileIcon (attachment) {
      let name = attachment.sourceFileName
      let extension = name && name.substring(name.lastIndexOf('.') + 1)
      return this.fileIcons[extension] || file
    },
    // 获取材料附件的大小
    formatSize (size) {
      let value = size
      let suffix = 'B'
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'KB'
      }
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'MB'
      }
      return `${value}${suffix}`
    },
    // 下载课程材料附件
    downloadMaterialAttachFile (file) {
      if (tools.isComeFromIPad()) {
        var fileSize = tools.calFilesize(file.size)
        let requestData = {
          'emailTemplate': 'lesson_library_material_attach',
          'downloadFileUrl': file.url,
          'fileName': file.sourceFileName,
          'fileSize': fileSize,
          'courseName': ''
        }
        this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
          .then(() => {
            this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
              confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
              showCancelButton: false
            })
          }).catch(error => {
          this.$message.error(error.message)
        })
      } else {
        window.location.href = file.url
      }
    },
    // 编辑课程材料
    editMaterial (lessonMaterial) {
      if (this.needGenerateResource) {
        this.$message.error(this.$t('loc.curriculum75'))
        return
      }
      this.$emit('edit',JSON.stringify(lessonMaterial))
    }
  },
  computed: {
    // 获取材料的图片或视频地址
    getMaterialUrl () {
      return function (material) {
        return (material.media && material.media.url) || material.externalMediaUrl
      }
    },
    // 获取材料的视频封面
    getMaterialCover () {
      return function (material) {
        return (material.media && material.media.coverUrl) || material.externalMediaCoverUrl
      }
    },
    ...mapState({
      needGenerateResource: state => state.lesson.needGenerateResource
    })
  }
}
</script>

<style lang="less" scoped>
.lesson-material-attachment-item {
  display: flex !important;
  background-color: #f2f2f2 !important;
  align-items: center !important;
  min-height: 35px !important;
  border-radius: 5px !important;
  margin-top: 5px !important;
}

.lesson-material-attachment-item .el-button span {
  margin-left: 0 !important;
}

.lesson-material-attachment-item .el-button {
  padding: 0 !important;
}
.lesson-material-attachment-item-img {
  width: 20px !important;
  margin-left: 5px !important;
  margin-right: 5px !important;
}
.lesson-material-attachment-item-title {
  line-height: 16px !important;
  display: flex !important;
  align-items: center;
  flex: 1;
  min-width: 0;
}
/deep/ .lesson-material-card {
  margin-top: 6px !important;
  margin-bottom: 10px !important;
  line-height: 18px !important;
}

/deep/ .lesson-material-card-link {
  display: flex !important;
  flex-direction: row !important;
  width: 350px !important;
  align-items: center !important;
  height: 90px !important;
  background-color: #fff !important;
  margin: 0 !important;
  border-radius: 5px !important;
  gap: 10px !important;
}

/deep/ .lesson-material-card-link .lesson-material-card-link-icon {
  max-width: calc(107px) !important;
  min-width: 90px !important;
  height: 90px !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  object-fit: contain !important;
}

/deep/ .lesson-material-card-link .lesson-material-card-link-text {
  display: flex !important;
  flex-direction: column !important;
  align-content: flex-start !important;
  gap: 10px !important;
  flex: 1 !important;
  padding-right: 10px !important;
  max-width: 430px;
}

/deep/ .lesson-material-card-site {
  display: none;
  width: max-content !important;
  position: relative !important;
  border: 1px solid #c0c4cc !important;
  border-radius: 5px !important;
  background-color: #fff !important;
}

/deep/ .editor-card-link-describe {
  color: #606266 !important;
  font-size: 14px;
  font-weight: bold !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2 !important;
}

/deep/ .editor-card-link-origin {
  color: #606266 !important;
  font-size: 16px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/deep/ .lesson-material-card-img {
  position: relative !important;
  width: 192px !important;
  height: 108px !important;
}

/deep/ .lesson-material-card-img img {
  width: 192px !important;
  height: 108px !important;
}

/deep/ .lesson-material-attachment-item {
  display: flex !important;
  background-color: #f2f2f2 !important;
  align-items: center !important;
  min-height: 35px !important;
  border-radius: 5px !important;
  margin-top: 5px !important;
}

/deep/ .lesson-material-attachment-item-img {
  width: 20px !important;
  margin-left: 5px !important;
  margin-right: 5px !important;
}

/deep/ .lesson-material-attachment-item .el-button span {
  margin-left: 0 !important;
}

/deep/ .lesson-material-attachment-item .el-button {
  padding: 0 !important;
}

/deep/ .lesson-material-card-video-play {
  width: 40px !important;
  height: 40px !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  margin: auto !important;
}
/deep/ .textarea-item.p {
  padding: 0 15px 0 0 !important;
  margin: 0 !important;
  color: #323338;
  line-height: 25px !important;
  -webkit-user-modify: read-only !important;
  word-break: break-word;
}
/deep/ .lesson-field-value {
  word-break: break-word;
}
/deep/ .lesson-material-card-close {
  display: none !important;
}
</style>
