<template>
  <div class="h-full">
    <el-row v-if="curriculums.length > 0" style="height: 100%;">
      <!-- 系列课程列表 -->
      <el-col v-if="curriculums.length > 1" :span="6" style="height: 100%;overflow: auto;" class="lg-scrollbar-show bg-white lg-box-shadow lg-border-radius-8">
        <div v-for="(item, index) in curriculums" :key="item.id">
          <curriculum-card @click.native="selectCurriculum(item.id, index)" :curriculum="item" :selected="item.id === currentCurriculum.id"></curriculum-card>
        </div>
      </el-col>
      <!-- 内容 -->
      <el-col :span="curriculums.length > 1 ? 18 : 24" style="height: 100%;overflow: auto;" class="lg-scrollbar-show">
        <curriculum-detail class="m-l-md lg-border-radius-8 lg-box-shadow bg-white"
                           :id="currentCurriculum.id" :type="type" :currentContentLangCode="currentContentLangCode"
                           :isAdmin="isAdmin" @updateCurriculum="updateCurriculum"
                           @setCurrentCurriculum="setCurrentCurriculum"
                           @setCurriculumLangCode="setCurriculumLangCode">
        </curriculum-detail>
      </el-col>
    </el-row>
    <el-row class="bg-white h-full" v-if="curriculums.length === 0 && !loading">
      <empty-view></empty-view>
    </el-row>
  </div>
</template>

<script>
import CurriculumDetail from './CurriculumDetail'
import CurriculumCard from './CurriculumCard.vue'
import EmptyView from './EmptyView.vue'
import Lesson2 from '@/api/lessons2'
import { mapState } from 'vuex'
export default {
  name: 'CurriculumList',
  props: ['isAdmin','curriculums', 'type', 'loading', 'currentContentLangCode'],
  components: {
    CurriculumDetail,
    CurriculumCard,
    EmptyView
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open
    }),
    // 创建系列课程开关
    createAgencyCurriculumOpen () {
      return this.open && this.open.createAgencyCurriculumOpen
    }
  },
  data () {
    return {
      ageGroups: [],
      domains: [],
      ageValues: [],
      currentCurriculum: {}
    }
  },
  created () {
    this.loadAgeGroup()
    this.setSelectCurriculum()
  },
  mounted () {
    this.setSelectCurriculum()
  },
  methods: {
    // 设置选中系列课程
    setSelectCurriculum () {
      this.$nextTick(() => {
        if (this.curriculums.length > 0) {
          this.selectCurriculum(this.curriculums[0].id)
        }
      })
    },
    // 设置当前 curriculum 内容的语言类型
    setCurriculumLangCode (curriculumLangCode) {
      this.$emit('setCurriculumLangCode', curriculumLangCode)
    },
    // 选中系列课程
    selectCurriculum (id, index) {
      // 如果点击当前选中的系列课程，直接返回
      if (this.currentCurriculum.id === id) {
        return
      }
      if (this.type === 'ALL' && index !== 0) {
        this.$analytics.sendEvent('web_curriculum_user_detail_exposure')
      } else if (this.type === 'DRAFT') {
        this.$analytics.sendEvent('web_curriculum_draft_detail_exposure')
      }
      this.$analytics.sendEvent('web_curriculum_detial')
      let current = this.curriculums.find(x => x.id === id)
      if (current) {
        this.currentCurriculum = current
      } else {
        this.setSelectCurriculum()
      }
    },
    // 加载年龄组信息
    loadAgeGroup () {
      Lesson2.getAgeGroups(this.currentUser.default_agency_state)
      .then(res => {
        this.ageGroups = res.ageGroups
      })
      .catch(err => {
        this.$message.error(err)
      })
    },
    // 添加系列课程
    addCurriculum () {
      // 调接口创建新的系列课程
      Lesson2.createCurriculum()
      .then(res => {
        // 跳转编辑页面
        let id = res.id
        this.$router.push({
          name: 'curriculumEdit',
          params: {
            curriculumId: id,
            add: true,
            type: 'DRAFT'
          }
        })
      })
      .catch(error => {
        this.$message.error(error)
      })
    },
    // 通知父组件更新系列课程列表
    updateCurriculum () {
      this.$emit('updateCurriculum')
    },
    // 设置当前选中的单元课程
    setCurrentCurriculum (curriculum) {
      this.$emit('setCurrentCurriculum', curriculum)
    }
  }
}
</script>

<style lang="less" scoped>
.add-curriculum-button {
  width: -webkit-fill-available;
  border-width: 2px;
}
</style>
