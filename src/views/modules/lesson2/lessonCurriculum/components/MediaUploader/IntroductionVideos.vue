<template>
  <div>
    <el-upload :disabled="fileList.length >= 3" action :accept="mediaType" :multiple="false" :http-request="upload" :limit="3" :file-list="fileList"
              :on-exceed="handleExceed" :show-file-list="false" :before-upload="beforeUpload">
      <div>
        <el-button plain :disabled="fileList.length >= 3" :loading="showProgress" ><i class="el-icon-upload2 el-icon--left"/>{{ $t('loc.upload') }}</el-button>
        <el-button plain :disabled="fileList.length >= 3" @click.stop="showUrl"><i class="lg-icon lg-icon-link font-size-16 el-icon--left"/>{{$t('loc.url')}}
        </el-button>
      </div>
      <!-- <el-progress :percentage="uploadProgressRate" v-if="showProgress" :show-text="false"
                    style="position: absolute;bottom:0;left:0;width: 100%;"/> -->
      <!-- 视频辅助元素 -->
      <div style="display: none" ref="videoHelper">
        <video crossorigin="anonymous" muted autoplay/>
        <canvas/>
      </div>
    </el-upload>
    <el-row v-if="fileList.length > 0" class="m-t-md" :gutter="15">
      <el-col :span="5" v-for="(i, index) in fileList" :key="index" >
        <div style="position:relative">
          <curriculum-media-viewer :url="i.url" :coverMediaImg="i.coverImg" :type="i.type" :preview="true" :clickToPreview="true"/>
          <i class="el-icon-close" @click.stop="deleteFile(i)"/>
        </div>
      </el-col>
    </el-row>
  <!--视频预览弹框-->
      <div style="max-height: calc(100vh);overflow-y: auto;scrollbar-gutter:stable" class="scrollbar-new">
        <el-dialog
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="isShowUrl"
          width="600px"
          style="max-height: 100vh;"
          class="popup-container"
          top="6vh"
          :before-close="closeShowUrl">
          <div slot="title" style="height: 21px;font-size: 24px;">{{$t('loc.uploadViaURL')}}</div>
          <div style="line-height: 25px;margin-bottom: 15px">
         <span style="font-size: 16px">{{$t('loc.uploadExternalMediaTip1')}}</span>
            <el-input :placeholder="$t('loc.externalMediaUrlInputTip')" type="text" v-model="externalMediaUrl"
                      @input="linkValidator()" style="margin-top: 5px"></el-input>
            <span v-show="showErrMsg" style="color: red;font-size: 14px">{{$t('loc.ResolutionFailed')}}</span>
          </div>
          <div>
            <iframe v-show="showIframe" :src="showIframe && externalMediaUrlToRecognize" width="560px" height="315px"
                    allowfullscreen style="border: 0px"/>
            <el-image v-show="showImage" :src="showImage && externalMediaUrlToRecognize"
                      style="width: 560px;height: 315px"/>
          </div>
          <div slot="footer" style="margin-top: -5px">
            <el-button @click="closeShowUrl()">{{ $t('loc.cancel') }}</el-button>
            <el-button :disabled="confirmBtnShow" type="primary" @click="confirmUrl()">{{$t('loc.confirm')}}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </div>
</template>
<script>
import fileUtils from '@/utils/file.js'
import CurriculumMediaViewer from '../CurriculumMediaViewer.vue'
import emitter from 'element-ui/src/mixins/emitter'
import external from '@/api/lessons2/externalMedia'
import media from '@/api/lessons2/media'

export default {
  name: 'IntroductionVideos',
  components: {
    CurriculumMediaViewer
  },
  props: ['value'],
  mixins: [emitter],
  inject: ['ElFormItem'],
  computed: {
    mediaType () {
      return this.accept || '.mp4,.mov'
    }
  },
  data () {
    return {
      fileList: [],
      file: null, // 已上传的媒体文件 id、name、url
      uploadProgressRate: 0, // 上传进度
      showProgress: false,  // 是否显示进度条
      isShowUrl: false, // 是否显示视频预览框
      externalMediaUrl: null, // 输入框中输入的链接
      externalMediaUrlToRecognize:null, // 拼接成识别的链接
      externalMediaUrlToRecognize2:null, // 传入课程详情的链接
      showErrMsg: false, // 链接解析错误提示
      showIframe: false, // iframe内嵌页面是否要打开
      vimeoGuideUrl: "https://vimeo.zendesk.com/hc/en-us/articles/4416937940365-Share-or-embed-your-showcase", // vimeo 网站复制链接指导视频
      youTubeGuideUrl: "https://support.google.com/youtube/answer/57741?hl=en&ref_topic=9257102", // youTobe 网站复制链接指导视频
      externalMediaId: null, // 保存外部媒体链接之后接收 Id
      externalMedia: { // 用于保存外部链接资源
        externalMediaUrl: null,
        coverMediaImg: null,
        type: null
      },
      YouTubeId: "", // YouTube 的唯一 ID
      promise: null,
      confirmBtnShow: true, // 是否禁用确认按钮
      showImage: false, // 是否启用图片预览框
      isNextStep: false, // 保存步骤是否往下执行
      uploadTask: null // 当前上传任务
    }
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler (newVal) {
        this.fileList = newVal
      }
    }
  },
  mounted () {
    // 编辑模式
    this.$nextTick(() => {
      this.changeIntroductionVideos()
    })
  },
  created () {

  },
  methods: {
    // 视频预览弹窗确认
    async confirmUrl () {
      // 关闭弹窗
      this.isShowUrl = false
      // 关闭 iframe 内嵌
      this.showIframe = false
      // 关闭 Image 预览框
      this.showImage = false
      // 提示语展示置为 false
      this.showErrMsg = false
      // 是否禁用确认按钮
      this.confirmBtnShow = true
      // 进行外部链接的保存
      await this.saveExternalMedia()
      // 链接保存成功后执行下面的步骤
      if (this.isNextStep) {
        // 把链接传入到子模块进行封面展示
        this.externalMediaUrlToRecognize2 = this.externalMediaUrlToRecognize
        // 保存上传后的 Id
        let media = { type: 'externalMedia', coverImg: this.externalMedia.coverMediaImg, externalMediaUrlId: this.externalMediaId, url: this.externalMediaUrlToRecognize } // externalMediaUrl 预览时使用
        this.fileList.push(media)
        this.changeIntroductionVideos()
      }
      // 清除填入的链接
      this.externalMediaUrl = null
    },
    // 验证链接是否合法
    linkValidator () {
      // 以下两行防止用户两次输入类型不一致而出现两个框的情况，所以验证前把两个框关掉
      this.showImage = false
      this.showIframe = false
      // 去除链接的前后空格
      let trim = this.externalMediaUrl.trim()
      // 输入框内容为空,提示关闭
      if (trim.length === 0) {
        this.showErrMsg = false
        this.confirmBtnShow = true
        return
      }
      // 获取 YouTube 的链接 Id
      this.YouTubeId = this.getYouTubeID(trim)
      if (trim.includes("youtu") && this.YouTubeId) {
        // 提示信息关闭
        this.showErrMsg = false
        // 内嵌窗口打开
        this.showIframe = true
        // 是否禁用确认按钮
        this.confirmBtnShow = false
        // 拼接成识别的链接形式
        this.externalMediaUrlToRecognize = "https://www.youtube.com/embed/" + this.YouTubeId
      } else if (trim.startsWith("https://vimeo.com/") || trim.startsWith("http://vimeo.com/")) {
        this.showErrMsg = false
        this.showIframe = true
        // 是否禁用确认按钮
        this.confirmBtnShow = false
        let str
        if (trim.startsWith("https://vimeo.com/")) {
          str = trim.slice(18)
        } else {
          str = trim.slice(17)
        }
        this.externalMediaUrlToRecognize = "https://player.vimeo.com/video/" + str
      } else {
        // 链接不合法，开启提示
        this.showErrMsg = true
        // 关闭 Iframe 内嵌视频
        this.showIframe = false
        // 是否禁用确认按钮
        this.confirmBtnShow = true
      }
    },
    // 展开视频预览框
    showUrl () {
      this.isShowUrl = true
    },
    // 关闭视频预览框
    closeShowUrl () {
      // 关闭弹窗
      this.isShowUrl = false
      // 关闭 iframe 内嵌
      this.showIframe = false
      // 关闭 image 图片预览框
      this.showImage = false
      // 清除填入的链接
      this.externalMediaUrl = ""
      // 提示语展示置为 false
      this.showErrMsg = false
      // 禁用上传确认按钮
      this.confirmBtnShow = true
    },
    // 保存外部链接
    async saveExternalMedia () {
      // 如果是 YouTube 视频
      if (this.externalMediaUrlToRecognize.startsWith("https://www.youtube.com/embed/")) {
          this.externalMedia.type = "YouTube"
          this.externalMedia.externalMediaUrl = this.externalMediaUrlToRecognize
          this.externalMedia.coverMediaImg = "https://i3.ytimg.com/vi/" + this.YouTubeId+"/hqdefault.jpg";
          await external.saveExternalMedia(this.externalMedia).then(r => {
            // 保存成功后，把 Id 赋值给 externalMediaId
            this.externalMediaId = r.id
            // 保存成功后允许执行接下来的步骤
            this.isNextStep = true
          }).catch(e => {
            this.$message.error(this.$t('loc.saveUrlTip').toString())
          })
      } else if (this.externalMediaUrlToRecognize.startsWith("https://player.vimeo.com/video/")) { // 如果是 Vimeo 视频
          this.externalMedia.type = "vimeo"
          this.externalMedia.externalMediaUrl = this.externalMediaUrlToRecognize
          await fetch("https://vimeo.com/api/oembed.json?url=" + this.externalMediaUrl)
          .then((result) => result.json())
          .then(result => {
              // 获取封面图片
            this.externalMedia.coverMediaImg = result.thumbnail_url
          })
          .catch((error) => {
            this.$message.error(this.$t('loc.saveUrlTip').toString())
          })
        // 如果有封面，才进行保存
        if (this.externalMedia.coverMediaImg) {
          await external.saveExternalMedia(this.externalMedia).then(r => {
            // 保存成功 返回 id
            this.externalMediaId = r.id
            // 保存成功后允许执行接下来的步骤
            this.isNextStep = true
          }).catch(e => {
            this.$message.error(this.$t('loc.saveUrlTip').toString())
          })
        }
      } else if (this.externalMediaUrlToRecognize.startsWith("https://") && this.isImg(this.externalMediaUrlToRecognize) || this.externalMediaUrlToRecognize.startsWith("http://") && this.isImg(this.externalMediaUrlToRecognize)) {
        this.externalMedia.type = "Image"
        this.externalMedia.externalMediaUrl = this.externalMediaUrlToRecognize
        this.externalMedia.coverMediaImg = this.externalMediaUrlToRecognize
        await external.saveExternalMedia(this.externalMedia).then(r => {
          // 保存成功后，把 Id 赋值给 externalMediaId
          this.externalMediaId = r.id
          // 保存成功后允许执行接下来的步骤
          this.isNextStep = true
        }).catch(e => {
          this.$message.error(this.$t('loc.saveUrlTip').toString())
        })
      }
    },
    /**
     * 获取大部分 YouTube 网站链接上的视频 ID
     * @param url
     * @returns {null|*}
     * 2022
     */
    getYouTubeID (url) {
      const youtube_regex = /^.*(youtu\.be\/|vi?\/|u\/\w\/|embed\/|\?vi?=|\&vi?=)([^#\&\?]*).*/
      const parsed = url.match(youtube_regex)
      if (parsed && parsed[2] && url.includes('youtu')) {
        return parsed[2]
      }
      return null
    },
    // 判断链接格式是否是图片
    isImg (url) {
      let reg = /.+(\.jpeg|\.png|\.jpg)/i
      return reg.test(url)
    },
    // 限制文件上传大小
    beforeUpload (file) {
      // 上传文件是否为图片类型
      let isImage = /image\/.+/i.test(file.type);
      // 上传文件是否为视频类型
      let isVideo = /video\/.+/i.test(file.type);
      let fileSize = file.size / 1024 / 1024;
      // 图片大小限制为3M
      if (isImage && fileSize > 3) {
        this.$message.error(this.$t('loc.lessons2ImageUploadTips'));
        return false
      }
      // 视频大小限制为100M
      if (isVideo && fileSize > 100) {
        this.$message.error(this.$t('loc.lessons2VideoUploadTips'))
        return false
      }
      // 校验文件类型
      let extension = file.name.substring(file.name.lastIndexOf('.'))
      let acceptable = this.mediaType.split(',').find(a => a.toLowerCase().trim() === extension.toLowerCase());
      if (!acceptable) {
        this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'))
        return false
      }
      return true
    },
    async upload ({ file }) {
      // 显示进度条
      this.uploadProgressRate = 0
      this.showProgress = true
      // 视频与图片分开处理，视频直接上传至S3
      if (/video\/.+/i.test(file.type)) {
        this.uploadVideo(file)
        return
      }
      fileUtils.uploadFile(file, {
        privateFile: false,
        processEventHandler: (progressEvent) => {
          // 上传进度处理,图片显示出来才算彻底完成
          let progressRate = (progressEvent.loaded / progressEvent.total).toFixed(2) * 100;
          if (progressRate === 100) {
            progressRate -= 1
          }
          this.uploadProgressRate = progressRate
        }
      })
        .then(res => {
          if (!res || !res.id) {
            this.$message.error(this.$t('loc.lessons2UploadFailed'));
            return
          }
          // 上传成功
          this.file = {
            id: res.id,
            name: file.name,
            url: URL.createObjectURL(file)
          }
          let media = { type: 'image', id: res.id, name: file.name, url: res.public_url, size: file.size }
          this.fileList.push(media)
          this.changeIntroductionVideos()
        })
        .catch(() => {
          this.$message.error(this.$t('loc.lessons2UploadFailed'));
        })
        .finally(() => {
          // 关闭进度条
          this.showProgress = false
        })
      return false
    },
    // 直接上传视频至S3
    async uploadVideo (file) {
      if (this.uploadTask) {
        // 已有上传任务，则关闭上传任务
        this.uploadTask.abort()
        this.uploadTask = null
      }
      // 填充视频信息
      await fileUtils.getVideoInfo(file, this.$refs.videoHelper)
      let _this = this
      this.uploadTask = fileUtils.uploadToS3(file, {
        onUploadProgress: evt => {
          // 处理上传进度
          let progressRate = parseInt((evt.loaded * 100) / evt.total)
          // 上传完成之后需要请求后端接口生成记录及缩略图，因此进度置为99%
          if (progressRate === 100) {
            progressRate -= 1
          }
          _this.uploadProgressRate = progressRate
        },
        completeHandler: (err, data) => {
          if (err || !data || !data.Location) {
            // 不是主动中断的，则提示上传失败
            if (err.toString().indexOf("RequestAbortedError") === -1) {
              // 上传失败
              _this.$message.error(this.$t('loc.lessons2UploadFailed'))
              // 关闭进度条
              _this.showProgress = false
            }
            return
          }
          // 调用接口创建课程缩略图
          media.createMediaWithThumbnail({
            type: 'mp4',
            base64_snapshot_file: file.base64Str.substring(file.base64Str.indexOf('base64,')+7),
            height: file.height,
            width: file.width,
            key: data.Key,
            fileName: file.name,
            size: file.size,
            duration: file.duration,
            mediaSource: 'lesson'
          }).then(res => {
            // 给file赋值
            _this.file = {
              id: res.id,
              name: file.name,
              url: URL.createObjectURL(file)
            }
            let media = { type: 'video', coverImg: res.snapshot_url, id: res.id, name: file.name, url: data.Location, size: file.size }
            _this.fileList.push(media)
            _this.changeIntroductionVideos()
          }).catch(() => {
            this.$message.error(this.$t('loc.lessons2UploadFailed'))
          }).finally(() => {
            if (_this.uploadProgressRate == 99) {
              // 关闭进度条
              _this.showProgress = false
            }
            // 关闭上传任务
            _this.uploadTask = null
          })
        }
      })
    },
    deleteFile (file) {
      this.fileList = this.fileList.filter(x => x.id != file.id || x.externalMediaUrlId != file.externalMediaUrlId)
      this.changeIntroductionVideos()
    },
    // 修改视频
    changeIntroductionVideos () {
      this.$emit('changeIntroductionVideos', this.fileList)
    },
    handleExceed () {
      this.$message.warning('当前限制选择 3 个文件')
    }
  }
}
</script>
<style lang="less" scoped>
@media only screen and (max-width:1199px){
  //ipad
  /deep/ .el-dialog__body{
    padding: 10px 20px;
  }
  .media-uploader {
    background-color: #F2F2F2;
    width: 640px;
    height: 360px;
    position: relative;
    border: 1px solid transparent;

    .media-uploader-select {
      // margin-left: 20px;
      width: 640px;
      height: 360px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & > :first-child {
        width: 176px;
        height: 126px;
      }

      & > :nth-child(2) {
        width: 549px;
        color: #676879;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        margin: 16px auto 16px auto;
      }
    }

    .media-uploader-selected {
      width: 640px;
      height: 360px;
      position: relative;
    }
  }
  .media-uploader-small {
    width: 330px;
    height: 185px;

    .media-uploader {
      width: 330px;
      height: 185px;
    }
    .media-uploader-select {
      width: 330px;
      height: 185px;

      & > :first-child {
        width: 86px;
        height: 55px;
      }

      & > :nth-child(2) {
        width: 202px;
        color: #676879;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        margin: 9px auto 7.75px auto;
      }
    }

    .media-uploader-selected {
      width: 330px;
      height: 185px;
      position: relative;
    }
  }
  .media-uploader-lesson-cover {
    width: 540px;
    height: 310px;
    position: relative;
    border: 1px solid transparent;
    .media-uploader {
      width: 540px;
      height: 310px;
      margin-left: -10px;
    }

    .media-uploader-select {
      width: 540px;
      margin-top: -20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & > :first-child {
        width: 176px;
        height: 126px;
      }

      & > :nth-child(2) {
        width: 549px;
        color: #676879;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        margin: 16px auto 16px auto;
      }
    }

    .media-uploader-selected {
      width: 540px;
      height: 400px;
      position: relative;
    }
  }
  .el-icon-close {
    text-align: center;
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #888888;
    border-radius: 50%;
    color: #fff;
    width: 20px;
    height: 20px;
    display: inline-block;
    cursor: pointer;
    line-height: 20px;
    z-index: 1;
  }

  .el-form-item.is-error .media-uploader.validate {
    border-color: red;
  }
}
@media only screen and (min-width:1200px){
  //web
  /deep/ .el-dialog__body{
    padding: 10px 20px;
  }
  .media-uploader {
    background-color: #F2F2F2;
    width: 640px;
    height: 360px;
    position: relative;
    border: 1px solid transparent;

    .media-uploader-select {
      width: 640px;
      height: 360px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & > :first-child {
        width: 176px;
        height: 126px;
      }

      & > :nth-child(2) {
        width: 549px;
        color: #676879;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        margin: 16px auto 16px auto;
      }
    }

    .media-uploader-selected {
      width: 640px;
      height: 360px;
      position: relative;
    }
  }
  .media-uploader-small {
    width: 330px;
    height: 185px;

    .media-uploader {
      width: 330px;
      height: 185px;
    }
    .media-uploader-select {
      width: 330px;
      height: 185px;

      & > :first-child {
        width: 86px;
        height: 55px;
      }

      & > :nth-child(2) {
        width: 202px;
        color: #676879;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        margin: 9px auto 7.75px auto;
      }
    }

    .media-uploader-selected {
      width: 330px;
      height: 185px;
      position: relative;
    }
  }
  .media-uploader-lesson-cover {
    width: 540px;
    height: 310px;
    position: relative;
    border: 1px solid transparent;
    .media-uploader {
      width: 540px;
      height: 310px;
      margin-left: -10px;
    }

    .media-uploader-select {
      width: 540px;
      margin-top: -20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & > :first-child {
        width: 176px;
        height: 126px;
      }

      & > :nth-child(2) {
        width: 549px;
        color: #676879;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        margin: 16px auto 16px auto;
      }
    }

    .media-uploader-selected {
      width: 540px;
      height: 400px;
      position: relative;
    }
  }
  .el-icon-close {
    text-align: center;
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #888888;
    border-radius: 50%;
    color: #fff;
    width: 20px;
    height: 20px;
    display: inline-block;
    cursor: pointer;
    line-height: 20px;
    z-index: 1;
  }

  .el-form-item.is-error .media-uploader.validate {
    border-color: red;
  }
}
</style>
