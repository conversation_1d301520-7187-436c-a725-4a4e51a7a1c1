<template>
  <div>
    <div class="capsule " :class="['capsule-' + color ,'capsule-' + size]">
      <div class="capsule-left">{{ leftText }}</div>
      <div class="capsule-right">{{ rightText }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Capsule',
  props: {
    leftText: {
      type: String
    },
    rightText: {
      type: String
    },
    color: {
      type: String,
      default: 'green'
    },
    size: {
      type: String,
      default: 'default'
    }
  }
}
</script>

<style lang="less" scoped>
.capsule {
  display: inline-flex;
  min-height: 32px;
  line-height: 12px;
  .capsule-left {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    color: #FFFFFF;
    padding: 5px 8px;
    //垂直水平居中
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .capsule-right {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 4px 5px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.capsule-green {
  display: inline-flex;
  .capsule-left {
    background: #36BF8A;
  }
  .capsule-right {
    background: #E0F8EF;
    color: #289B1C;
  }
}
.capsule-orange {
  .capsule-left {
    background: #F9BB80;
  }
  .capsule-right {
    background: #FEF5E9;
    color: #F49628;
  }
}
.capsule-pink {
  .capsule-left {
    background: #EA8985;
  }
  .capsule-right {
    background: #FFEFE5;
    color: #FD8238;
  }
}
.capsule-default {
  .capsule-left {
    text-align: start;
  }
}

.capsule-large {
  line-height: 1.2;
  .capsule-left {
    width: 105px;
    text-align: start;
    padding: 0 12px;
  }
  .capsule-right {
    text-align: start;
    padding: 0 12px;
  }
}

.capsule-mini {
  .capsule-left {
    width: 50px;
    text-align: start;
  }
}
</style>
