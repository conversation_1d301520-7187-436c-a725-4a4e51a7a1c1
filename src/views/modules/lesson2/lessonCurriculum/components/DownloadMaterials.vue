<template>
  <el-button 
    :type="buttonType" 
    icon="lg-icon lg-icon-pdf"
    :loading="loading"
    @click="handleDownload">
    PDF
  </el-button>
</template>

<script>
import Lessons2 from '@/api/lessons2'
import { mapState } from 'vuex'

export default {
  name: 'DownloadMaterials',
  props: {
    // 单元 ID
    unitId: {
      type: String,
      default: ''
    },
    // 周计划 ID
    planId: {
      type: String,
      default: ''
    },
    // 按钮类型
    buttonType: {
      type: String,
      default: 'primary'
    }
  },
  computed: {
    ...mapState({
      contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 课程源语言类型
    }),
    // 内容的当前语言是否与源语言相同
    isSameLanguage () {
      return this.contentLanguage === this.contentOriginalLanguage
    },
  },
  data() {
    return {
      loading: false // 是否正在下载
    }
  },
  methods: {
    // 下载材料
    handleDownload() {
      // 设置正在下载
      this.loading = true
      // 创建下载参数
      let params = {}
      // 如果单元 ID 存在，则添加单元 ID
      if (this.unitId) {
        params.unitId = this.unitId
      }
      // 如果周计划 ID 存在，则添加周计划 ID
      if (this.planId) {
        params.planId = this.planId
      }
      // 如果当前语言与源语言不相同，则添加语言码
      if (!this.isSameLanguage && this.contentLanguage) {
        params.langCode = this.contentLanguage
      }
      // 调用下载 API
      Lessons2.downloadMaterials(params)
      .then(() => {
        // 获取周计划 PDF
        this.getMaterialPDF()
      }).catch (error => {
        // 设置下载完成
        this.loading = false
        // 提示错误信息
        this.$message.error(error.message)
      })
    },
    /**
     * 获取周计划 PDF
     */
     getMaterialPDF () {
      Lessons2.getPDFList({
        type: 'UNIT_MATERIALS'
      }).then(response => {
        // 如果返回了数据，则获取周计划 PDF
        if (response && response.length > 0) {
          let pdf = response[0]
          // 如果 PDF 状态为成功或失败，则停止轮询
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getMaterialPDF()
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED') {
            // 设置下载完成
            this.loading = false
            // 打开新窗口下载 pdf 文件
            window.open(pdf.pdfUrl, '_blank')
          } else {
            // 设置下载完成
            this.loading = false
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          }
        }
      })
      .catch(error => {
        // 设置下载完成
        this.loading = false
        // 提示错误信息
        this.$message.error(error.message)
      })
    }
  }
}
</script>

<style scoped>
</style>