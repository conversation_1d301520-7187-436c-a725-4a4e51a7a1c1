<template>
  <div style="display: flex; flex-direction: column; align-items: center;">
      <img :style="{'margin-top': top + 'px'}" height="200" src="../../component/assets/img/empty.jpg" >
      <div style="text-align: center; color:#676879" >{{text || $t('loc.plan36')}}</div>
  </div>
</template>

<script>
export default {
name: 'EmptyView',
props: {
    text: {
        type: String
    },
    top: {
        type: Number,
        default: 50
    }
}
}
</script>

<style>

</style>
