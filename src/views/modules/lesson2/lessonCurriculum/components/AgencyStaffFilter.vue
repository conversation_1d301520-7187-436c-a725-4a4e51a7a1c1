<template>
  <el-collapse class="el-collapse" v-model="activeName" accordion @change="teacherTip">
    <el-collapse-item name="Admins">
      <span class="collapse-title" slot="title" :title="$t('loc.curriculum36')">
        {{ $t('loc.curriculum36') }}
      </span>
      <template v-for="user in admins">
        <el-button class="user-info" @click="clickedHandler(user.id, index)" :type="selectedUserId === user.id ? 'primary':''">
          <el-avatar size="small" :src="get(user.avatarMediaUrl)"></el-avatar>
          <span :title="user.displayName">{{ user.displayName }}</span>
        </el-button>
      </template>
    </el-collapse-item>
    <el-collapse-item v-for="(center,index) in centers" :key="center.centerId" :name="center.centerName">
      <span class="collapse-title" slot="title" :title="center.centerName">
        {{ center.centerName }}
      </span>
      <template v-for="user in center.users">
        <el-button class="user-info" @click="clickedHandler(user.userId, index)" :type="selectedUserId === user.userId ? 'primary':''">
          <el-avatar size="small" :src="get(user.userAvatar)"></el-avatar>
          <span :title="user.userName">{{ user.userName }}</span>
        </el-button>
      </template>
      <div v-if="!center.users || center.users.length === 0" class="noResult">
        <span>{{ $t('loc.TeacherNoResult') }}</span>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import Api from '@/api/lessons2/index'
import constants from '@/utils/constants'
export default {
  name: 'TeacherFilter',
  props: ['userId'],
  components: {
  },
  data () {
    return {
      activeName: 'Admins',
      admins: [],
      centers: null,
      selectedUserId: '',
      centerIndex: -1,
      loading: 0
    }
  },
  watch: {
    selectedUserId: {
      deep: true,
      immediate: true,
      handler () {
        this.$emit('selectedUserId', this.selectedUserId)
      }
    }
  },
  created () {
    this.getCenterTeachers()
  },
  methods: {
    compare (value) {
      return function (a, b) {
        let val1 = a[value]
        let val2 = b[value]
        return val1.localeCompare(val2)
      }
    },
    teacherTip (val) {
      for (let i = 0; i < val.length; i++) {
        let element = val[i]
        if (element === this.activeName) {
        }
      }
    },
    // 默认头像
    get (str) {
      if (str === null || str === '') {
        return constants.userAvatarURL
      }
      return str
    },
    clickedHandler (id) {
      this.selectedUserId = id
    },

    // 获取 center和 center下的老师信息
    getCenterTeachers () {
      this.loading = 1
      Api.getAgencyStaffs()
      .then(
        response => {
          let admins = response.admins || []
          this.admins = admins
          let centers = response.centerModels || []
          if (centers && centers.length > 0) {
            centers.sort(this.compare('centerName'))
            centers.forEach(center => center.users && center.users.sort(this.compare('userName')))
          }
          this.centers = centers
          if (this.admins.length > 0) {
            this.selectedUserId = this.admins[0].id
          } else {
            for (let center of this.centers) {
              if (center.users && center.users.length > 0) {
                this.selectedUserId = center.users[0].userId
                break
              }
            }
          }
        })
        .finally(() => {
          this.loading = 0
        })
    }
  }
}
</script>

<style scoped lang="less">
.el-collapse /deep/ & {
  border: none;
  background-color: #ffffff;

  .collapse-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .el-collapse-item__wrap{
    border: none;
  }

  .el-collapse-item__header {
    height: 48px;
    line-height: 48px;
    color: #111c1c;
    border: none;
    margin-left: 20px;
    //font-size: 14px;
    font-size: 15px;
    font-weight: 600;
    font-family: inherit;
  }

  .el-collapse-item__content {
    padding-bottom: 0;

    .el-button {
      border: none;
      //border-top: 1px solid #eee;
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  .el-avatar--small {
    width: 36px;
    height: 36px;
    vertical-align: middle;
    margin-left: 0px;
  }

}

/* 作者头像和名 */
.user-info /deep/ & {
  box-shadow: none;
  display: flex;
  align-items: center;
  margin-left: 1px;
  width: 100%;
  border-radius: 0;
  height: 50px;
  line-height: 50px;
  & > span {
    line-height: 42px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    & > span {
      margin-left: 8px;
      color: #333333;
      vertical-align: middle;
      font-size: 14px;
      //font-weight: bolder;
      font-family: inherit;
    }
  }
}

.user-info.el-button--primary /deep/ & {
  border-radius: 5px;
  height: 50px;
  line-height: 50px;
  // background-color: #e7f4f9;
  & > span > span {
    color: #fff;
  }
}

.noResult {
  display: flex;
  align-items: center;
  color: #C0C4CC;
  height: 42px;
  font-size: 14px;
  padding-left: 20px;
  //border-top: 1px solid #eee;
}
</style>
