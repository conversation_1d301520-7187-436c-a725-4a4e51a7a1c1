<template>
  <div class="display-flex align-items flex-row-between">
    <div v-if="createAgencyCurriculumOpen && isAdmin">
      <!-- 类型切换 -->
      <el-tabs class="lg-tabs" v-model="type" @tab-click="changeCurriculumType">
        <el-tab-pane :label="$t('loc.curriculum')" name="ALL"></el-tab-pane>
        <el-tab-pane v-if="false" :label="$t('loc.curriculum1')" name="FAVORITE"></el-tab-pane>
        <el-tab-pane v-if="createAgencyCurriculumOpen && isAdmin" :label="$t('loc.curriculum64')" name="DRAFT"></el-tab-pane>
      </el-tabs>
      <curriculum-recycle v-if="isAdmin && false" class="m-l-xs"></curriculum-recycle>
    </div>
    <!-- 搜索栏 -->
    <div>
      <el-input v-if="false" v-model="input" prefix-icon="el-icon-search" placeholder="Search" size="medium" style="width: 300px;"></el-input>
    </div>
    <!-- 应用单元课程按钮和创建单元课程按钮 -->
    <div class="display-flex" style="align-items: center">
      <LanguageToggle v-model="currentContentLangCode"/>
      <!-- 创建按钮 -->
      <el-button v-if="createAgencyCurriculumOpen && isAdmin && type == 'ALL'"
                 icon="el-icon-plus"
                 type="prinmary"
                 plain class="lg-margin-left-12"
                 @click="addCurriculum">{{ $t('loc.curriculum65') }}</el-button>
      <!-- 应用单元课程按钮 -->
      <curriculum-apply class="lg-margin-left-12" v-if="type == 'ALL' && currentCurriculum" source="curriculum" :curriculum="currentCurriculum" :lessionTitle="$t('loc.curriculum3')"></curriculum-apply>
    </div>
  </div>
</template>

<script>

import CurriculumRecycle from './CurriculumRecycle.vue'
import CurriculumApply from './CurriculumApply'
import { mapState } from 'vuex'
import Lesson2 from '@/api/lessons2'
import LanguageToggle from '@/views/modules/lesson2/component/LanguageToggle.vue'
export default {
  props: ['isAdmin', 'currentCurriculum', 'contentLangCode'],
  components: { CurriculumRecycle, CurriculumApply, LanguageToggle },
  data () {
    return {
      type: 'ALL', // 默认展示所有的系列课程
      input: '',
      currentContentLangCode: '' // 当前语言
    }
  },
  watch: {
    // 监听当前内容语言类型变更
    contentLangCode: {
      immediate: true,
      handler (val) {
        // 设置当前内容的当前语言类型
        this.currentContentLangCode = val
      }
    },
    currentContentLangCode (val) {
      // 设置当前内容的当前语言类型
      this.$emit('setNewCurriculumLangCode', val)
    }
  },
  computed: {
    ...mapState({
      open: state => state.common.open
    }),
    // 创建系列课程开关
    createAgencyCurriculumOpen () {
      return this.open && this.open.createAgencyCurriculumOpen
    }
  },
  methods: {
    changeCurriculumType (e) {
      this.$emit('changeTypeCallBack', this.type)
    },
    // 添加系列课程
    addCurriculum () {
      // 调接口创建新的系列课程
      Lesson2.createCurriculum()
      .then(res => {
        // 跳转编辑页面
        let id = res.id
        this.$router.push({
          name: 'curriculumEdit',
          params: {
            curriculumId: id,
            add: true,
            type: 'DRAFT'
          }
        })
      })
      .catch(error => {
        this.$message.error(error)
      })
    }
  }
}
</script>

<style lang="less" scoped>

/deep/.curriculum-type-radio-group .el-radio-button {
  margin-bottom: 0 !important;
}
/deep/.curriculum-type-radio-group .el-radio-button .el-radio-button__inner {
  // background: inherit;
  border: none;
  color: #676879;
  font-weight: 600;
  font-size: 16px;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
/deep/.curriculum-type-radio-group .el-radio-button__orig-radio:checked+.el-radio-button__inner {
  color: #10b3b7 !important;
  background-color: #E7F7F8 !important;
  border-width: 0;
  box-shadow: none;
}
/deep/.curriculum-type-radio-group .el-radio-button .el-radio-button__inner {
  border-left: none;
  border-radius: 4px;
}

.light-primary-bg {
  background: #e7e7e7;
}
.bg-shadow {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}
</style>
