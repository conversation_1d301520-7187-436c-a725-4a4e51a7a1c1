<template>
  <div>
    <el-button @click="openDialog" :type="buttonType" class="m-l-r-2" :class="_buttonClass" :loading="unitsLoading">{{ $t('loc.curriculum3') }}</el-button>
    <el-dialog :title="lessionTitle" :visible="dialogVisable" :before-close="closeDialog" width="80%" :append-to-body="true" :close-on-click-modal="false" :close-on-press-escape="false" custom-class="curriculum-apply-dialog">
      <div>
        <el-row :gutter="16">
          <el-col :span="9">
            <div v-if="false" style="margin-top: -30px;" class="lg-color-text-primary font-size-20 add-margin-b-12 text-bolder">{{ lessionTitle}}</div>
            <el-row>
              <el-row>
                <div class="m-b-16">
                  <div class="font-bold font-size-16 font-color-black m-b-8">{{$t('loc.curriculum40')}}</div>
                  <el-row>
                    <el-popover v-model="show" placement="bottom-start" popper-class="popover-width-350" :visible-arrow="false">
                      <el-button slot="reference" plain style="width: 100%;">
                        <div style="text-overflow: ellipsis; overflow: hidden;" :title="selectedUnitsLabel || 'Please Select'">
                          {{ selectedUnitsLabel || 'Please Select' }}
                        </div>
                      </el-button>
                      <el-checkbox v-model="selectAllWeek" @change="selectAllWeekplan" :indeterminate="isIndeterminate">All</el-checkbox>
                      <el-tree :data="units" @check-change="handleSelect"
                        ref="tree" show-checkbox default-expand-all node-key="id"
                              class="lg-scrollbar-show max-height-300" :props="defaultProps">
                        <span slot-scope="{node, data}" style="max-width:300px;" class="text-ellipsis" :title="data.disabled ? 'Please stay tuned for our upcoming units.' : data.plans ? `Unit ` + data.number + `: ` + data.title : `Week ` + data.number">
                            {{ data.plans ? ( !data.isSpecail ? $t('loc.unitweek') +' '+ data.number + `: ` + data.title : 'Special Unit'  +' '+ data.number + `: ` + data.title) : $t('loc.unitweek1')+' ' + data.number }}
                        </span>
                      </el-tree>
                    </el-popover>
                  </el-row>
                  <el-row class="add-margin-tb-6 lg-scrollbar-show max-height-300">
                    <el-col v-for="unit in selectedUnits" :key="unit.id" class="add-padding-t-10" style="background-color: #f6f6f6;" :span="24" >
                      <div class="add-padding-l-10"><span style="font-size: 14px;font-weight: 600;color: #111c1c;">
                        {{ unit.number >= 1 ? $t('loc.curriculum102', { num: unit.number }) + ': ' + unit.title : 'Special Unit ' + unit.number + ': ' + unit.title }}
                      </span></div>
                      <div class="add-padding-l-10"><span style="font-size: 15px">{{ getWeeks(unit.id) }}</span></div>
                    </el-col>
                  </el-row>
                </div>
                <div class="m-b-16">
                  <div class="font-bold font-size-16 font-color-black m-b-8">{{ $t('loc.curriculum23') }}</div>
                  <el-row :gutter="6" class="display-flex w-full select-center">
                    <!-- 学校 -->
                    <el-col class="center-option">
                      <el-select v-model="currentCenterId" @change="changeCenter" :disabled="loading"
                                 class="add-margin-r-20 border-bold" :popper-append-to-body="false">
                        <el-option v-for="center in centers" :key="center.id" :label="center.name"
                                   :value="center.id"></el-option>
                      </el-select>
                    </el-col>
                    <!-- 班级 -->
                    <el-col class="group-option">
                      <el-select v-model="currentGroupId" :disabled="loading" class="border-bold"
                                 :popper-append-to-body="false">
                        <el-option v-for="group in groups" :key="group.id" :label="group.name"
                                   :value="group.id"></el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="m-b-16">
                  <div class="font-bold font-size-16 font-color-black m-b-8">{{ $t('loc.curriculum24') }}</div>
                  <el-row :gutter="6" class="display-flex">
                    <el-col :span="12" class="display-flex">
                      <el-date-picker
                        class="border-bold"
                        style="width:100%"
                        v-model="startDate"
                        @change="changeStartDate"
                        type="date"
                        :pickerOptions="pickerOptions"
                        format="MM/dd/yyyy"
                        :placeholder="$t('loc.curriculum83')">
                      </el-date-picker>
                    </el-col>
                    <el-col :span="12">
                      <el-select class="border-bold" v-model="startWeek" :placeholder="$t('loc.curriculum25')">
                        <el-option v-for="week in 54" :key="week" :label="$t('loc.curriculum103', {num: week})" :value="week"></el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
              </el-row>
              <el-row class=" color-676879">
                <div>
                  {{ $t('loc.curriculum92')}}
                </div>
                <div>
                  {{ $t('loc.curriculum93')}}
                </div>
                <div>
                  {{ $t('loc.curriculum94')}}
                </div>
              </el-row>
            </el-row>
          </el-col>
          <el-col :span="15">
            <div style="background-color: #f6f6f6;border-radius: 8px 8px 8px 8px; padding: 0 16px;">
              <div class="display-flex justify-content-between">
                <div class="lg-color-text-primary font-size-16 add-padding-l-10 add-padding-t-20 text-bolder">{{ $t('loc.curriculum26') }}</div>
                <div class="display-flex add-padding-tb-6">
                  <div class="display-flex add-padding-r-20 add-padding-t-20"><i style="display: inline-block;height: 15px;width: 15px;background-color: #E3E3E3;margin-top: 2px;"></i><div class="add-padding-l-6">{{ $t('loc.curriculum27') }}</div></div>
                  <div class="display-flex add-padding-r-20 add-padding-t-20"><i style="display: inline-block;height: 15px;width: 15px;background-color: #FFE8B9;margin-top: 2px;"></i><div class="add-padding-l-6">{{ $t('loc.curriculum28') }}</div></div>
                </div>
              </div>
              <el-row style="max-height: 550px;" class="scroll-y lg-scrollbar-show add-padding-r-20" :gutter="16">
                <el-col v-for="(calendar, index) in calendars" :key="index" :span="8">
                  <calendar :year="calendar.year" :month="calendar.month" :activeDays="calendar.activeDays" :holidays="[]"></calendar>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="display-flex align-items justify-content-between">
        <el-checkbox v-if="showAutoAdapt" v-model="isAutoAdapt">{{ $t('loc.autoAdapteAgencyTemplate') }}</el-checkbox>
        <div class="display-flex align-items justify-content-end w-full">
          <el-button plain @click="closeDialog">{{ $t('loc.cancel') }}</el-button>
          <el-button type="primary" @click="applyPlans" :loading="applying">{{ $t('loc.confirm') }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCurrentUser } from '@/utils/common'
import Calendar from './components/Calendar.vue'
import Lessons2 from '@/api/lessons2'
export default {
  name: 'CurriculumApply',
  props: ['curriculum', 'source', 'lessionTitle', 'buttonType'],
  components: { Calendar },
  data () {
    return {
      dialogVisable: false, // 周计划应用弹窗显示
      unitsLoading: false, // 单元加载中
      loading: false, // 加载
      currentCenterId: '', // 选择的当前学校 ID
      currentGroupId: '', // 选择的当前班级 ID
      centers: [], // 所有的学校
      groups: [], // 所有的班级
      noGroup: false, // 是否没有班级
      show: false, // 是否显示选择单元周计划 popover
      selectAllWeek: false, // 选择所有的周
      selectedUnits: [], // 已选的单元
      selectedPlans: [], // 已选的周计划
      defaultProps: {
        children: 'plans',
        label: 'number'
      }, // el-tree 配置
      startDate: '', // 开始时间
      startWeek: 1, // 开始周次
      calendars: [], // 日历信息
      applying: false, // 正在应用周计划
      isAutoAdapt: false, // 是否自动适配机构周计划模版
      showAutoAdapt: false, // 是否显示自动适配机构周计划模版
      units: [], // 单元周计划
      curriculumId: '' // 课程 ID
    }
  },
  computed: {
    // 按钮样式
    _buttonClass () {
      return !this.buttonType && 'el-button-warning-dark'
    },
    // 所有的周计划 ID
    allPlanIds () {
      let planIds = []
      this.units.forEach(x => x.plans.forEach(plan => planIds.push(plan.planId)))
      return planIds
    },
    // 所有的周计划对象
    allPlans () {
      let plans = []
      this.units.forEach(x => x.plans.forEach(plan => plans.push(plan)))
      return plans
    },
    // 选择单元周计划label名称
    selectedUnitsLabel () {
      let units = this.selectedUnits
      if (this.selectedPlans.length === this.allPlanIds.length && units.length > 1) {
        return this.$t('loc.curriculum41')
      } else {
        return units.map(x => x.number >= 1 ? this.$t('loc.curriculum102', { num: x.number }) : 'Special Unit ' + x.number).join(', ')
      }
    },
    // 单元周计划半选状态
    isIndeterminate () {
      return this.selectedPlans.length > 0 && this.selectedPlans.length < this.allPlanIds.length
    },
    // 日期选择组件配置
    pickerOptions () {
      return {
        disabledDate: (time) => {
          return time.getTime() < this.$moment().day(6).valueOf()
        }
      }
    }
  },
  watch: {
    curriculum: {
      deep: true,
      handler (value) {
        this.units = this.dealUnitData(value.units)
        this.curriculumId = value.id
        // 设置默认周次
        this.startWeek = 1
      }
    }
  },
  created () {
    // 获取学校班级信息
    this.getGroups()
  },
  mounted () {
    // 组件挂载成功后如果没有单元课程，则通过路由中的单元课程 ID，获取单元课程
    if (!this.curriculum) {
      this.unitsLoading = true
      let params = { id: this.$route.params.curriculumId }
      Lessons2.getCurriculumDetail(params)
        .then(res => {
          this.setPlanCenterType(res.ageValues)
          this.unitsLoading = false
          this.curriculumId = res && res.id
          // 如果 curriculum 数据存在，则初始化单元周计划数据
          if (res && res.units) {
            // 初始化单元周计划数据
            this.units = this.dealUnitData(res.units)
          }
        })
        .catch(error => {
          this.unitsLoading = false
          this.$message.error(error.message)
        })
    } else {
      // 如果 curriculum 数据存在，则初始化单元周计划数据
      if (this.curriculum && this.curriculum.units) {
        this.setPlanCenterType(this.curriculum.ageValues)
        this.units = this.curriculum.units
        // 初始化单元周计划数据
        this.units = this.dealUnitData(this.units)
        this.curriculumId = this.curriculum.id
      }
    }
  },
  methods: {
    // 设置 Plan Center 类型
    setPlanCenterType (ages) {
      // 处理年龄数据，避免空指针
      ages = ages || []
      let type = 'PS' // 默认为 PS
      // 按照年龄大小判断 Plan Center 类型，从低到高适配
      if (ages.includes('0') || ages.includes('1') || ages.includes('2') || ages.includes('1,2')) {
        type = 'IT'
      } else if (ages.includes('3') || ages.includes('4')) {
        type = 'PS'
      } else if (ages.includes('5') || ages.includes('Grade 1') || ages.includes('Grade 2')) {
        type = 'K'
      } else {
        type = 'PS'
      }
      this.$store.commit('SET_PLANCENTERTYPE', type)
    },
    // 获取最近的下一个周一
    getNextMonday () {
      const today = new Date()
      // 获取当前是星期几（0-6，0表示周日，1表示周一，以此类推）
      const day = today.getDay()
      // 计算距离下一个周一的天数
      let daysUntilMonday = 1 - day + 7
      // 判断今天是否是周一
      if (day === 1) {
        // 如果今天就是周一，则需要跳过今天，找到下一个周一
        daysUntilMonday += 7
      }
      const nextMonday = new Date(today)
      // 计算下一个周一的日期
      nextMonday.setDate(today.getDate() + daysUntilMonday)
      return nextMonday
    },
    // 打开应用弹窗
    async openDialog () {
      this.dialogVisable = true
      // 曝光事件埋点
      this.$analytics.sendEvent('web_curriculum_apply_pop_exposure')
      // 根据不同的组件，发送不同的埋点
      switch (this.source) {
        case 'curriculum':
          this.$analytics.sendEvent('web_curriculum_apply')
          break
        case 'unit':
          this.$analytics.sendEvent('web_curriculum_unitcard_apply')
          break
        case 'weeklyPlan':
          this.$analytics.sendEvent('web_curriculum_unit_week_apply')
          break
        case 'unitCard':
          this.$analytics.sendEvent('web_curriculum_unitcard_apply')
          break
        case 'overviewCard':
          this.$analytics.sendEvent('web_curriculum_unit_ov_apply')
          break
      }
      this.$nextTick(() => {
        // 初始化日历
        this.initCalandar()
        this.selectAllWeekplan(true)
      })
      // 设置默认周次为第一周
      this.startWeek = 1
      this.startDate = this.getNextMonday()
      const data = await Lessons2.getWeekPlanTemplateSwitch()
      this.showAutoAdapt = data.allowTeacherCustomWeeklyPlanTemplate
      this.isAutoAdapt = data.allowTeacherCustomWeeklyPlanTemplate
    },
    // 关闭应用弹窗
    closeDialog () {
      this.$analytics.sendEvent('web_curriculum_apply_pop_cancel')
      this.dialogVisable = false
      this.selectAllWeek = false
      this.selectedUnits = []
      this.selectedPlans = []
      this.startDate = ''
      this.startWeek = ''
      this.calendars = []
      this.changeCenter()
      this.selectAllWeekplan(false)
    },
    // 处理 units 数据，以适配树节点
    dealUnitData (units) {
      units.forEach(unit => {
        if (unit.number >= 1) {
          unit.isSpecail = false
        } else {
          unit.isSpecail = true
        }
        unit.plans.forEach(plan => {
          plan.id = plan.planId
          plan.isSpecail = unit.isSpecail
        })
      })
      return units
    },
    // 获取学校班级
    getGroups () {
      this.loading = true
      this.$axios({
        url: $api.urls(getCurrentUser().user_id).centersAndGroups,
        method: 'get'
      }).then(response => {
        // 如果老师所在的学校数量大于1，显示center选择下拉框
        this.centers = response.filter(x => x.groups.length > 0)
        // 过滤学校班级中离校班级
        for (var i = 0; i < this.centers.length; i++) {
          this.centers[i].groups = this.centers[i].groups.filter(x => !x.inactive)
        }
        this.centers = this.centers.filter(x => x.groups.length > 0)
        // 如果所有班级都为空，直接返回
        if (this.centers.length == 0) {
          this.noGroup = true
          return
        }
        this.currentCenterId = this.centers[0].id
        // 去除离校班级
        this.groups = this.centers[0].groups.filter(x => !x.inactive)
        if (this.groups.length == 0) {
          this.noGroup = true
          return
        }
        this.noGroup = false
        this.currentGroupId = this.groups[0].id
      }).catch(error => {
      }).finally(() => {
        this.loading = false
      })
    },
    // 切换学校
    changeCenter (centerId) {
      // 通过学校Id，切换对应的班级下拉框
      if (centerId) {
        let groups = this.centers.find(x => x.id === centerId).groups
        this.groups = groups.filter(x => !x.inactive)
        this.currentGroupId = groups[0].id
      } else {
        // 找到第一个学校和班级
        this.currentCenterId = this.centers[0].id
        let groups = this.centers[0].groups
        this.groups = groups.filter(x => !x.inactive)
        this.currentGroupId = groups[0].id
      }
    },
    // 关闭选择单元周计划 popover
    handleCancel () {
      this.show = false
    },
    // 全选周计划
    selectAllWeekplan (val) {
      if (val) {
        this.$refs.tree.setCheckedKeys(this.allPlanIds)
      } else {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    // 设置选择的周计划信息
    handleSelect () {
      let tree = this.$refs.tree
      let selectedNode = [...tree.getCheckedKeys(), ...tree.getHalfCheckedKeys()]
      let selectedUnits = this.units.filter(x => selectedNode.indexOf(x.id) >= 0)
      this.selectedUnits = selectedUnits
      let selectedPlans = this.allPlanIds.filter(x => selectedNode.indexOf(x) >= 0)
      this.selectedPlans = selectedPlans
      this.selectAllWeek = this.allPlanIds.length == selectedPlans.length
      this.initCalandar(this.startDate)
    },
    // 获取单元的周计划信息
    getWeeks (unitId) {
      let unit = this.units.find(x => x.id === unitId)
      let planIds = this.selectedPlans
      let selectedPlans = unit.plans.filter(x => planIds.indexOf(x.planId) >= 0).map(x => this.$t('loc.curriculum103', { num: x.number }))
      return selectedPlans.join(', ')
    },
    // 修改应用起始日期
    changeStartDate () {
      this.initCalandar(this.startDate)
    },
    // 初始化日历信息
    initCalandar (startDate) {
      startDate = this.$moment(startDate).day(1).valueOf()
      let weeks = this.selectedPlans.length
      let activeDays = []
      let defaultMonthNum = 6
      if (startDate) {
        let endDay = this.$moment(startDate).add(weeks * 7, 'day')
        let duration = this.$moment(endDay).diff(this.$moment(startDate), 'months')
        defaultMonthNum =  duration > 6 ? duration : 6
        for (let i = 0; i < weeks * 7; i++) {
          let day = this.$moment(startDate).add(i, 'day')
          if (day.weekday() < 6 && day.weekday() > 0) {
            activeDays.push({ year: day.year(), month: day.month() + 1, day: Number(day.format('DD')) })
          }
        }
      }
      this.calendars = []
      for (let i = 0; i < defaultMonthNum; i++) {
        let calendarDate = this.$moment().add(i, 'months')
        if (startDate) {
          calendarDate = this.$moment(startDate).add(i, 'months')
        }
        let year = calendarDate.year()
        let month = calendarDate.month() + 1
        let monthActiveDays = []
        if (activeDays.length > 0) {
          activeDays.forEach(x => {
            if (x.year === year && x.month === month) {
              monthActiveDays.push(x.day)
            }
          })
        }
        this.calendars.push({ year: year, month: month, activeDays: monthActiveDays })
      }
    },
    // 应用周计划
    applyPlans () {
      if (this.selectedPlans.length == 0) {
        this.$message.error(this.$t('loc.curriculum66'))
        return
      }
      if (!this.currentGroupId) {
        this.$message.error('Please select group first.')
        return
      }
      if (!this.startDate) {
        this.$message.error(this.$t('loc.curriculum67'))
        return
      }
      if (!this.startWeek) {
        this.$message.error(this.$t('loc.curriculum68'))
        return
      }
      // 开始日期取周一
      let startDate = this.$moment(this.startDate).day(1).valueOf()
      let params = {
        curriculumId: this.curriculumId,
        planIds: this.selectedPlans,
        centerId: this.currentCenterId,
        groupId: this.currentGroupId,
        startDate: this.$moment(startDate).format('YYYY-MM-DD'),
        startWeek: this.startWeek,
        autoAdapt: this.isAutoAdapt
      }
      this.applying = true
      // 根据不同的显示组件，发送不同的埋点
      switch (this.source) {
        case 'curriculum':
          this.$analytics.sendEvent('web_curriculum_apply_confirm')
          break
        case 'unit':
          this.$analytics.sendEvent('web_curriculum_unit_apply_confirm')
          break
        case 'weeklyPlan':
          this.$analytics.sendEvent('web_curriculum_unit_week_apply_confirm')
          break
        case 'unitCard':
          this.$analytics.sendEvent('web_curriculum_unitcard_apply_confirm')
          break
        case 'overviewCard':
          this.$analytics.sendEvent('web_curriculum_unit_ov_apply_confirm')
          break
      }
      Lessons2.applyPlans(params)
      .then(res => {
        this.$analytics.sendEvent('web_curriculum_apply_pop_apply')
        this.applying = false
        let planId = res.planId
        this.$store.dispatch('setShowMappedTip', res.mapped) // 设置是否显示映射提示
        this.$router.push({
          name: 'edit-plan',
          params: {
            planId: planId,
            apply: true
          }
        })
      })
      .catch(error => {
        this.applying = false
        this.$message.error(error.message)
      })
    }
  }
}
</script>

<style scoped lang="less">
.select-center {
  margin-left: unset!important;
  margin-right: unset!important;
}
.center-option {
  padding-left: unset!important;
}
.group-option {
  padding-right: unset!important;
}
.m-l-r-2 {
  margin: 0 2px !important;
}
.m-b-8 {
  margin-bottom: 8px !important;
}
.m-b-16 {
  margin-bottom: 16px !important;
}
.dialog-footer {
  position: sticky;
  bottom: 0;
  text-align: center;
  background-color: #fff;
}
/deep/ .el-range-editor{
  width: 100%;
}

/deep/ .el-select{
  width: 100%;
}

/deep/ date-popper{
  background-color: chocolate;
  top: 290px;
  left: 110px;
}

/deep/ .calendar-table{
  width: 100%;
  height: 240px;
}
.max-height-300 {
  max-height: 300px;
  overflow: auto;
}

/deep/ .el-tree-node__content {
  label {
    margin-bottom: 0;
  }
}
/deep/ .el-dialog {
    max-height: calc(100% - 80px) !important;
    height: 100%;
    margin-top: 40px !important;
    overflow: hidden;
}
/deep/.el-dialog__body {
  padding: 0 20px;
  overflow: auto;
  height: calc(100% - 130px);
}
/deep/ .el-input .el-input__icon {
  font-weight: 400!important;
}
</style>

<style>
.popover-width-350 {
  min-width: 350px;
}
.el-date-editor .el-input__prefix .el-icon-date::before {
  font-family: 'lg-icon' !important;
  content: '\e631';
  font-size: 18px;
}

</style>
