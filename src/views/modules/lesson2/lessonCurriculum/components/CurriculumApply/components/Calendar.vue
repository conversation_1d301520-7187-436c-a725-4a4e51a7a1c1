<template>
  <div>
    <table class="calendar-table">
      <tr class="calendar-header"><th class="text-center" colspan="7">{{ title }}</th></tr>
      <tr>
          <td class="calendar-header-day text-center" v-for="(i, index) in weekdays" :key="index">{{ i }}</td>
      </tr>
      <tr  v-for="(date,index) in rows" :key="index">
          <!-- <td class="selectDate"> -->
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 0 : index * 7 ].isActive, 'holiday' : showdays[index == 0 ? 0 : index * 7 ].isHoliday }">
              {{ showdays[index == 0 ? 0 : index * 7 ].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 1 : index * 7 + 1].isActive, 'holiday' : showdays[index == 0 ? 1 : index * 7 + 1].isHoliday }">
              {{ showdays[index == 0 ? 1 : index * 7 + 1].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 2 : index * 7 + 2].isActive, 'holiday' : showdays[index == 0 ? 2 : index * 7 + 2].isHoliday }">
              {{ showdays[index == 0 ? 2 : index * 7 + 2].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 3 : index * 7 + 3].isActive, 'holiday' : showdays[index == 0 ? 3 : index * 7 + 3].isHoliday }">
              {{ showdays[index == 0 ? 3 : index * 7 + 3].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 4 : index * 7 + 4].isActive, 'holiday' : showdays[index == 0 ? 4 : index * 7 + 4].isHoliday }">
              {{ showdays[index == 0 ? 4 : index * 7 + 4].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 5 : index * 7 + 5].isActive, 'holiday' : showdays[index == 0 ? 5 : index * 7 + 5].isHoliday }">
              {{ showdays[index == 0 ? 5 : index * 7 + 5].dayOfMonth }}
          </td>
          <td class="text-center day-cell"
              :class="{'active': showdays[index == 0 ? 6 : index * 7 + 6].isActive, 'holiday' : showdays[index == 0 ? 6 : index * 7 + 6].isHoliday }">
              {{ showdays[index == 0 ? 6 : index * 7 + 6].dayOfMonth }}
          </td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    year: {
      type: Number
    },
    month: {
      type: Number
    },
    activeDays: {
      type: Array,
      default: () => []
    },
    holidays: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    activeDays () {
      this.initCalendar(this.year, this.month)
    }
  },
  created () {
    this.initCalendar(this.year, this.month)
  },
  data () {
    return {
      rows: 6,
      weekdays: ['S','M','T','W','T','F','S'],
      showdays: []
    }
  },
  computed: {
    title () {
      let dateStr = this.year + '-' + this.month
      // 兼容safari浏览器，月份小于10时，需要在月份前面加0
      if (this.month < 10) {
        dateStr = this.year + '-0' + this.month
      }
      return this.$moment(dateStr).format('MMMM YYYY')
    }
  },
  methods: {
    // 初始化日历
    initCalendar (year, month) {
      this.showdays = []
      let firstdayOfWeek = new Date(year, month - 1).getDay()
      for (let i = 0; i < firstdayOfWeek; i++) {
        let day = {
          dayOfMonth: '',
          isActive: false,
          isHoliday: false
        }
        this.showdays.push(day)
      }
      var max = new Date(year, month, 0).getDate()
      for (let i = 1; i <= max; i++) {
        let isActive = this.activeDays.indexOf(i) >= 0
        let isHoliday = this.holidays.indexOf(i) >= 0
        let day = {
          dayOfMonth: i,
          isActive: isActive,
          isHoliday: isHoliday
        }
        this.showdays.push(day)
      }
      this.rows = Math.ceil(this.showdays.length / 7)
      if (this.showdays.length % 7 > 0) {
        for (let i = 0; i < (this.showdays.length % 7); i++) {
          let day = {
            dayOfMonth: '',
            isActive: false,
            isHoliday: false
          }
          this.showdays.push(day)
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.calendar-table{
  background: #FFFFFF;
  border-collapse: collapse;
  margin: 10px;
}
.calendar-header {
  background: #C7E9EB;
}
.calendar-header-day {
  background-color:#10B3B7;
  color: #FFFFFF;
}
.day-cell {
  border: 1px solid #ccc;
  padding: 3px;
  font-size: 12px;
}
.holiday {
  background: #E3E3E3 !important;
}
.active {
  background: #FFE8B9;
}
</style>
