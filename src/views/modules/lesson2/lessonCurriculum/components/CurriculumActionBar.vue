<template>
  <div>
    <div v-if="type == 'ALL' || type == 'FAVORITE'" class="display-flex align-items justify-content-end">
      <div v-show="false">
        <!-- 浏览量 -->
        <el-button type="text" style="padding: 0">
          <div class="display-flex align-items">
            <icon-alibaba class="icon-alibaba-view-mini"/>
            <span>{{ viewCount }}</span>
          </div>
        </el-button>
        <!-- 点赞 -->
        <el-button type="text" @click.stop="like" style="padding: 0">
          <div class="display-flex align-items">
            <icon-alibaba :class="[`icon-alibaba-like-${userLiked ? 'on' : 'off'}`]"/>
            <span>{{ likeCount }}</span>
          </div>
        </el-button>
        <!-- 收藏 -->
        <el-button type="text" @click.stop="addFavorite" style="padding: 0">
          <div class="display-flex align-items">
            <icon-alibaba :class="[`icon-alibaba-favorite-${userFavorite ? 'on' : 'off'}`]"/>
            <span>{{ favoriteCount }}</span>
          </div>
        </el-button>
      </div>
      <!-- 编辑、复制、删除、pdf -->
      <div class="display-flex">
        <!-- 隐藏单元课程详情中的应用按钮 -->
        <curriculum-apply v-if="false" :curriculum="curriculum" :lessionTitle="$t('loc.curriculums21')"></curriculum-apply>
        <el-button v-show="false" class="m-l-sm" icon="fa fa-file-pdf-o">PDF</el-button>
        <el-dropdown v-if="canEditOrDelete" trigger="click" @command="curriculumAction">
          <el-button plain class="m-l-sm" icon="el-icon-more"></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-edit" command="edit">{{ $t('loc.edit') }}</el-dropdown-item>
            <!-- 草稿状态的系列课程只有删除和编辑按钮 -->
            <el-dropdown-item v-if="false" icon="el-icon-copy-document" command="replicate">{{ $t('loc.plan66') }}</el-dropdown-item>
            <el-dropdown-item icon="el-icon-delete" command="delete">{{ $t('loc.delete') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!-- 单元课程复制按钮隐藏 isAdmin && !canEditOrDelete -->
        <el-button class="m-l-sm" v-if="false" @click="replicateCurriculum" icon="el-icon-copy-document">{{ $t('loc.lessons2LessonReplicate') }}</el-button>
      </div>
    </div>
    <!-- 草稿状态的系列课程只有删除和编辑按钮 -->
    <div v-if="type == 'DRAFT'" class="display-flex justify-content-end">
      <el-button plain class="m-l-sm" @click="removeCurriculum" icon="el-icon-delete">{{ $t('loc.delete') }}</el-button>
      <el-button plain class="m-l-sm" @click="editCurriculum" icon="el-icon-edit">{{ $t('loc.edit') }}</el-button>
    </div>
  </div>
</template>

<script>
import IconAlibaba from '@/views/modules/lesson2/lessonLibrary/components/IconAlibaba'
import CurriculumApply from './CurriculumApply'
import Lessons2 from '@/api/lessons2'
export default {
  name: 'ActionBar',
  components: {
    IconAlibaba,
    CurriculumApply
  },
  props: {
    curriculum: {
      type: Object
    },
    type: {
      type: String,
      default: 'ALL'
    },
    lessionTitle: {
      type: String
    },
    isAdmin: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    canEditOrDelete () {
      return this.curriculum.canEditOrDelete || false
    }
  },
  data () {
    return {
      viewCount: 1024,
      likeCount: 101,
      favoriteCount: 11,
      userLiked: false,
      userFavorite: false
    }
  },
  methods: {
    // 操作
    curriculumAction (command) {
      this.$analytics.sendEvent('web_curriculum_more_action')
      if (command === 'edit') {
        this.editCurriculum()
      } else if (command === 'replicate') {
        this.replicateCurriculum()
      } else if (command === 'delete') {
        this.removeCurriculum()
      }
    },
    // 编辑课程
    editCurriculum () {
      // 如果是编辑已发布课程，获取课程副本进行编辑
      if (this.type == 'ALL') {
        let params = { id: this.curriculum.id }
        Lessons2.editPublishedCurriculum({}, params)
        .then(res => {
          this.$analytics.sendEvent('web_curriculum_edit')
          this.$router.push({
            name: 'curriculumEdit',
            params: {
              curriculumId: res.id,
              sourceCurriculumId: this.curriculum.id,
              title: this.curriculum.name,
              type: 'ALL'
            }
          })
        })
        .catch(err => {
          this.$message.error(err)
        })
      } else {
        this.$analytics.sendEvent('web_curriculum_draft_edit')
        // 其他状态的课程，直接跳转编辑
        this.$router.push({
          name: 'curriculumEdit',
          params: {
            curriculumId: this.curriculum.id,
            title: this.curriculum.name,
            type: 'DRAFT'
          }
        })
      }
    },
    // 复制
    replicateCurriculum () {
      if (this.curriculum && this.curriculum.type == 'PUBLIC') {
        this.$analytics.sendEvent('web_curriculum_template_replicate')
      } else {
        this.$analytics.sendEvent('web_curriculum_replicate')
      }
      let params = { id: this.curriculum.id }
      Lessons2.copyCurriculum({}, params)
      .then(res => {
        this.$router.push({
          name: 'curriculumEdit',
          params: {
            curriculumId: res.id,
            title: this.curriculum.name,
            type: 'DRAFT'
          }
        })
      })
    },
    // 移除系列课程
    removeCurriculum () {
      let messageName = this.curriculum.name ? this.curriculum.name : this.$t('loc.draftCurriculum')
      var content = this.$t('loc.curriculum72', { name: messageName })
      this.$confirm(content, this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
          this.$message.success(this.$t('loc.delSfy'))
          // 点击确认按钮，调接口移除系列课程
          let params = { id: this.curriculum.id }
          Lessons2.removeCurriculum({}, params)
          .then(res => {
            if (res.success) {
              this.$analytics.sendEvent('web_curriculum_delete_draft_confirm')
              this.updateCurriculum()
              if (this.type == 'ALL') {
                this.$analytics.sendEvent('web_curriculum_delete')
              } else {
                this.$analytics.sendEvent('web_curriculum_draft_delete')
              }
            }
          })
        }).catch(() => {    
          this.$analytics.sendEvent('web_curriculum_delete_draft_cancel')
        })
    },
    // 通知父组件更新系列课程列表
    updateCurriculum () {
      this.$emit('updateCurriculum')
    },
    // 点赞
    like () {
      this.userLiked = !this.userLiked
      if (this.userLiked) {
        this.likeCount = this.likeCount + 1
      } else {
        this.likeCount = this.likeCount - 1
      }
    },
    // 收藏
    addFavorite () {
      this.userFavorite = !this.userFavorite
      if (this.userFavorite) {
        this.favoriteCount = this.favoriteCount + 1
      } else {
        this.favoriteCount = this.favoriteCount - 1
      }
    }
  }
}
</script>

<style>

</style>
