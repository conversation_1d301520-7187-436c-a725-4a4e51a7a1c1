<template>
  <div>
    <el-row :gutter="20" class="resource-items">
      <el-col v-for="(i, index) in resources" :key="index" :span="8">
        <div class="resource-item lg-pointer" :class="'rescouce-item-' + i.type" @click="getResourceDetail(i.routeName)">
          <div>{{ i.name }}</div>
          <div class="rescouce-num">{{ i.num }}</div>
          <div class="display-flex" style="height: 40px">
            <div>
              <el-button :class="'rescouce-more-' + i.type" round size="small" class="rescouce-more m-t-sm">
                {{ $t('loc.moreButten') }}
              </el-button>
            </div>
            <div class="rescouce-icon">
              <el-avatar size="large" shape="square" :src="i.icon"></el-avatar>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import bookList from '@/assets/img/lesson2/curriculum/book_list.png'
import keyVocabularies from '@/assets/img/lesson2/curriculum/key_vocabularies.png'
import printables from '@/assets/img/lesson2/curriculum/printables.png'
import activitiesList from '@/assets/img/lesson2/curriculum/activities_list.png'
import assessment from '@/assets/img/lesson2/curriculum/assessment.png'
import { mapState } from 'vuex'

export default {
  name: 'KeyResources',
  props: {
    curriculumId: {
      type: String
    },
    curriculumName: {
      type: String
    },
    type: {
      type: String
    },
    public: {
      type: Boolean
    },
    books: {
      type: Number,
      default: 0
    },
    vocabularies: {
      type: Number,
      default: 0
    },
    printables: {
      type: Number,
      default: 0
    },
    activities: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      resources: []
    }
  },
  watch: {
    curriculumId () {
      this.initResourcNumber()
    }
  },
  created () {
    this.initResourcNumber()
  },
  computed: {
    ...mapState({
      open: state => state.common.open
    }),
    dllOpen () {
      return this.open && this.open.dllopen
    }
  },
  methods: {
    initResourcNumber () {
      let resources = [
        {
          'name': this.$t('loc.curriculum14'),
          'type': 'books',
          'routeName': 'curriculumBooks',
          'icon': bookList,
          'num': this.books
        },
        {
          'name': this.$t('loc.curriculum15') ,
          'type': 'vocabularies',
          'num': this.vocabularies,
          'icon': keyVocabularies,
          'routeName': 'curriculumVocabularies'
        },
        {
          'name': this.$t('loc.curriculum16'),
          'type': 'printables',
          'routeName': 'curriculumPrintables',
          'num': this.printables,
          'icon': printables
        },
        {
          'name': this.$t('loc.curriculum17'),
          'type': 'activities',
          'routeName': 'curriculumActivities',
          'icon': activitiesList,
          'num': this.activities
        },
        {
          'name': this.$t('loc.curriculum18'),
          'type': 'measures',
          'routeName': 'curriculumMeasures',
          'icon': assessment
        }
      ]
      // dll 功能未开启，过滤掉词汇模块
      if (!this.dllOpen) {
        resources = resources.filter(item => item.type !== 'vocabularies')
      }
      this.resources = resources
    },
    getResourceDetail (routeName) {
      switch (routeName) {
        case 'curriculumBooks':
            this.$analytics.sendEvent('web_curriculum_book')
          break
        case 'curriculumVocabularies':
          this.$analytics.sendEvent('web_curriculum_vocabularies')
          break
        case 'curriculumPrintables':
          this.$analytics.sendEvent('web_curriculum_attachment')
          break
        case 'curriculumActivities':
          this.$analytics.sendEvent('web_curriculum_activity')
          break
        case 'curriculumMeasures':
          this.$analytics.sendEvent('web_curriculum_measure')
          break
      }
      this.$router.push({
        name: routeName,
        params: {
          id: this.curriculumId,
          curriculumName: this.curriculumName,
          type: this.type,
          public: this.public
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.resource-items {
  color: #676879;
  font-size: 16px;
  display: flex;
  flex-wrap: wrap;
  row-gap: 20px
}
.resource-item {
  height: 100%;
  margin-bottom: 10px;
  padding: 24px;
  background: #F2F6FE;
  border-radius: 20px;
  position: relative;
}
.rescouce-item-books {
  background: #F2F6FE;
}
.rescouce-item-vocabularies {
  background: #EFF9FF;
}
.rescouce-item-printables {
  background: #FDF6ED;
}
.rescouce-item-activities {
  background: #E4FFFA;
}
.rescouce-item-measures {
  background: #FDEDED;
}
.rescouce-num {
  height: 24px;
  font-size: 24px;
  color: #323338;
  font-weight: 500;
}
.rescouce-more {
  position: absolute;
  bottom: 24px;
  color:#FFF;
  font-size: 14px;
  font-weight: 500;
  border: none;
}
.rescouce-icon {
  background: #FFF;
  border-radius: 15px;
  padding: 5px;
  text-align: center;
  margin-bottom: 10px;
  right: 24px;
  bottom: 16px;
  position: absolute;
  height: 70px;
  width: 70px;
  /deep/ .el-avatar {
    background: #FFF;
  }
  /deep/ .el-avatar--large {
    width: 60px;
    height: 60px;
    line-height: 60px;
  }
}
.rescouce-more-books {
  background: #85ABF0;
}
.rescouce-more-vocabularies {
  background: #97D7FE;
}
.rescouce-more-printables {
  background: #F9BB80;
}
.rescouce-more-activities {
  background: #3DDEC2;
}
.rescouce-more-measures {
  background: #F98080;
}

/deep/ .el-col-8 {
  /**兼容safari 浏览器 */
  width: 33.3%;
}
</style>
