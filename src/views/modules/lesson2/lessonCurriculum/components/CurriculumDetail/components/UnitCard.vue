<template>
  <div class="lg-pointer">
    <!-- 单元标题 -->
    <div @click="viewUnit()" class="unit-title" :title="$t('loc.curriculum102', {num: unit.number}) + `: ` +  unit.title ">
      <template v-if="unit.number >= 1">
        {{ $t('loc.curriculum102', {num: unit.number}) }}: {{ unit.title }}
        </template>
        <template v-else>
        {{ 'Special Unit'}} {{ unit.number }}: {{ unit.title }}
        </template>
    </div>
    <div @click="viewUnit()" class="unit-card">
      <!-- 单元描述 -->
      <div class="unit-desc">
        {{ unit.description }}
      </div>
      <!-- 单元封面和周次等信息 -->
      <el-row class="m-t-xs" type="flex">
        <!-- 封面 -->
        <el-col :span="10">
          <div style="cursor: pointer;">
            <curriculum-media-viewer v-if="getCoverMedia" :url="getCoverMedia.url" :coverMediaImg="getCoverMedia.coverImg" :type="getCoverMedia.type" :preview="true"/>
          </div>
        </el-col>
        <!-- 周次等信息 -->
        <el-col :span="14" >
          <div class="m-l-sm unit-info">
            <!-- Overview -->
            <div>
              <el-button class="no-padding font-size-16 w-full overflow-ellipsis" type="text" @click.stop="openDialog" :title="$t('loc.curriculum19')">{{ $t('loc.curriculum19') }}</el-button>
            </div>
            <!-- Weeks 和 Activity -->
            <div class="m-t-xs">
              <div class="currriculum-unit-info">
                <div class="curriculum-info-item">
                  <div class="currriculum-unit-info-num">{{ unit.weeks || 0 }}</div>
                  <div class="currriculum-unit-info-title">{{ $t('loc.curriculum7') }}</div>
                </div>
                <el-divider direction="vertical" class="curriculum-info-item-divider"></el-divider>
                <div class="curriculum-info-item">
                  <div class="currriculum-unit-info-num">{{ unit.activitiesNum || 0 }}</div>
                  <div class="currriculum-unit-info-title">{{ $t('loc.curriculum8') }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-dialog width="70%" :append-to-body="true" v-if="dialogVisable" :visible="dialogVisable" :before-close="closeDialog" 
      :title="unit.number >= 1 ? ($t('loc.curriculum19') + ' ' + unit.number) : ('Overview and Arc of Special Unit' + ' ' + unit.number)">
      <div>
        <div class="over-top">
          <div>
             <span class="desc_font">{{ $t('loc.description') }}</span>
          </div>
          <div class="pdfBtn">
            <el-button class="m-l-sm" icon="fa fa-file-pdf-o" v-if="false" v-show="unitPlanners && unitPlanners.length > 0">PDF</el-button>
          </div>
        </div>
        <div style="word-break: keep-all">{{ unit.description }}</div>
      </div>
      <div style="display: flex;flex-direction: column;flex: 1;">
        <div style="display: flex; justify-content: space-between; align-items: center">
          <span class="desc_font">{{ $t('loc.descfont') }}</span>
          <!-- 仅展示核心测评点 switch start -->
          <div class="core-measure" v-show="showCoreMeasureOpen">
            <el-switch
              v-model="showCoreMeasure"
              @change="changeCoreMeasureState">
            </el-switch>
            <span class="lesson-switch" style="margin-left: 5px">{{ $t('loc.showCoreMeasureOnly') }}</span>
          </div>
          <!-- 仅展示核心测评点 switch end -->
        </div>
        <unit-planner-overview ref="unit-plan-overview" :isLoading="unitPlannerIsLoading" :unitPlanners="unitPlanners" :unitCurriculum="unitCurriculum" :currentContentLangCode="currentContentLangCode"></unit-planner-overview>
      </div>
      <!-- 弹窗底部应用单元课程按钮 -->
      <div v-if="unitPlanners && unitPlanners.length > 0" slot="footer">
        <curriculum-apply :curriculum="unitCurriculum" source="unitCard" buttonType="primary" :lessionTitle="$t('loc.curriculum3')"></curriculum-apply>
      </div>
    </el-dialog>
    <el-dialog :visible="waitDialogVisable" :before-close="closeWaitDialog" custom-class="wait-dialog">
      <div style="height:400px;">
        <empty-view text="Please stay tuned for our upcoming units."></empty-view>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import CurriculumMediaViewer from '../../CurriculumMediaViewer.vue'
import CurriculumApply from '../../CurriculumApply'
import EmptyView from '../../EmptyView.vue'
import UnitPlannerOverview from '@/views/modules/lesson2/lessonCurriculum/CurriculumUnitDetail/components/UnitPlannerOverview'
import Lessons2 from '@/api/lessons2'
import { mapState } from 'vuex'

export default {
  name: 'UnitCard',
  components: { CurriculumMediaViewer , UnitPlannerOverview, EmptyView, CurriculumApply },
  props: ['unit', 'curriculum', 'frameworkId', 'public', 'type', 'currentContentLangCode'],
  data () {
    return {
      coverURL: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
      dialogTitle: 'Overview and Arc of Unit 1',
      dialogVisable: false,
      waitDialogVisable: false,
      unitPlanners: [],
      unitPlannerIsLoading: false,
      showCoreMeasure: true, // 展示核心测评点
      showCoreMeasureOpen: false, // 展示核心测评点开关
      previousLangCode: '' // 上次切换语言时的语言类型
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.previousLangCode = this.currentContentLangCode
    })
  },
  computed: {
    ...mapState({
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 课程源语言类型
    }),
    // 内容的当前语言是否与源语言相同
    isSameLanguage () {
      return this.currentContentLangCode === this.contentOriginalLanguage
    },
    // 单元封面
    getCoverMedia () {
      if (this.unit.coverMedias && this.unit.coverMedias.length > 0) {
        return this.unit.coverMedias[0]
      }
      return {}
    },
    published () {
      return this.type && this.type !== 'DRAFT'
    },
    // 当前单元的课程单元信息
    unitCurriculum () {
      return {
        id: this.curriculum.id,
        units: [this.unit],
        type: this.public ? 'PUBLIC' : 'AGENCY'
      }
    }
  },
  provide () {
    return {
      isPublic: () => this.public
    }
  },
  methods: {
    openDialog () {
      this.$analytics.sendEvent('web_curriculum_overview_unit_week_plan')
      if (this.unitPlanners.length <= 0 || this.previousLangCode !== this.currentContentLangCode) {
        this.previousLangCode = this.currentContentLangCode
        this.getUnitPlanOverView(this.currentContentLangCode)
      }
      this.dialogVisable = true
      this.$nextTick(() => {
        this.changeCoreMeasureState(this.showCoreMeasure)
      })
    },
    // 获取周计划概览
    getUnitPlanOverView (langCode) {
      let params = {
        unitId: this.unit.id,
        ...((!this.isSameLanguage && langCode) && { langCode })
      }
      this.unitPlannerIsLoading = true
      Lessons2.getUnitPlanOverView(params)
        .then(res => {
          this.showCoreMeasureOpen = res.showCoreMeasureOpen
          this.showCoreMeasure = this.showCoreMeasureOpen
          this.$nextTick(() => {
            this.changeCoreMeasureState(this.showCoreMeasure)
          })
          this.unitPlanners = res.unitPlanners.filter(item => (item.planItems && item.planItems.length > 0))
          this.unitPlannerIsLoading = false
        })
        .catch(error => {
          this.$message.error(error.response.data.error_message)
        })
    },
    closeDialog () {
      this.dialogVisable = false
      if (this.showCoreMeasureOpen) {
        this.showCoreMeasure = true
      }
    },
    openWaitDialog () {
      this.waitDialogVisable = true
    },
    closeWaitDialog () {
      this.waitDialogVisable = false
    },
    // 跳转课程单元详情
    viewUnit () {
      this.$router.push({
        name: 'curriculumUnitDetail',
        params: {
          type: this.type,
          unitId: this.unit.id,
          curriculumId: this.curriculum && this.curriculum.id,
          frameworkId: this.frameworkId || null,
          curriculumName: this.curriculum && this.curriculum.name,
          authorId: this.curriculum && this.curriculum.authorId,
          public: this.public,
          published: this.published
        }
      })
    },
    changeCoreMeasureState (val) {
      this.$bus.$emit('overViewChangeShowCore', val)
      this.$nextTick(() => {
        this.$refs['unit-plan-overview'].doLayout()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.unit-card {
  border-radius: 15px;
  border: 1px solid #FCDDBF;
  border-top-left-radius: 0;
  padding: 10px;
}
.unit-info {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px 0;
}
.unit-title {
  background: #F9BB80;
  color: #FFF;
  border-radius: 5px 5px 0 0;
  width: fit-content;
  padding: 5px 10px;
  position: relative;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// .unit-title::after {
//   content: '';
//   right: -60px;
//   left: 147%;
//   top: 0;
//   bottom: 0;
//   background: #F9BB80;
//   transform: translateX(-50px) skewX(25deg);
//   position: absolute;
//   border-top-right-radius: 5px;
// }
.unit-desc {
  height: 48px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.currriculum-unit-info {
  height: 80px;
  width: 100%;
  background: #f5f6f8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  border-radius: 15px;
  padding: 10px;

  .curriculum-info-item {
    width: 50%;
  }

  .curriculum-info-item-divider {
    height: 100%;
  }

  .currriculum-unit-info-num {
    font-weight: 600;
    font-size: 18px;
    color: #111c1c;
  }

  .currriculum-unit-info-title {
    font-size: 16px;
  }
}
.no-padding {
  padding: 0;
}
.over-top{
  display: flex;
  justify-content: space-between;
}
.desc_font{
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 40px;
    color: #111c1c;
}
/deep/ .el-dialog__title {
  font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #111c1c;
}

/deep/ .el-dialog__header {
  padding: 20px 20px 0;
}
/deep/ .el-dialog {
  margin: 0!important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 96%;
  display: flex;
  flex-direction: column;
}
/deep/.el-dialog__body {
  padding: 16px 20px;
  color: #111c1c;
  font-size: 14px;
  word-break: break-all;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}
/deep/ .wait-dialog {
  height: auto;
}
</style>
