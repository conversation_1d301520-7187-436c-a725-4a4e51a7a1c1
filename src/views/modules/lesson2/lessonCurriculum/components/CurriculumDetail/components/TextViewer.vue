<template>
  <div>
    <!-- <textarea rows="3" readonly tabindex="-1"></textarea> -->
    <span class="text-wrap">{{ showText }} </span>
    <span v-if="canShowMore">
      <el-button class="no-padding" v-if="show" type="text" @click="showLess">{{$t('loc.readmore2')}}  <i class="el-icon-arrow-up"></i></el-button>
      <el-button class="no-padding" v-else type="text" @click="showMore">{{$t('loc.readmore1')}} <i class="el-icon-arrow-down"></i></el-button>
    </span>
  </div>
</template>

<script>
export default {
  name: 'TextViwer',
  props: {
    text: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default: 500
    }
  },
  data () {
    return {
      canShowMore: false,
      show: false,
      showText: ''
    }
  },
  watch: {
    text () {
      this.initText()
    }
  },
  created () {
    this.initText()
  },
  methods: {
    // 初始化文字显示
    initText () {
      this.$nextTick(() => {
      // 判断是否能够显示 showmore 按钮
      // 如果能显示，显示 showmore 按钮
      if (this.text && this.text.length > this.max) {
        this.canShowMore = true
        this.showLess()
      } else {
        // 如果不能显示 showmore 按钮，只显示文字
        this.showText = this.text
        this.canShowMore = false
      }
    })
    },
    // 显示完整文字
    showMore () {
      this.showText = this.text
      this.show = true
    },
    // 显示 showmore 按钮
    showLess () {
      let textArray = this.text.slice(0, this.max)
      this.showText = textArray
      this.show = false
    }

  }

}
</script>

<style lang="less" scoped>
.no-padding {
  padding: 0;
}
.text-wrap {
  overflow-wrap: break-word;
}

</style>
