<template>
  <div id="curriculum" class="curriculum-detail" v-loading="loading">
    <curriculum-action-bar ref="top" :curriculum="curriculum" :type="type" :isAdmin="isAdmin" @updateCurriculum="updateCurriculum"></curriculum-action-bar>
    <el-row class="m-t-sm">
      <!-- 课程封面 -->
      <el-col :span="12">
        <curriculum-media-viewer :url="getCoverMedia" />
      </el-col>
      <el-col :span="12">
        <div class="m-l-md">
          <!-- 课程名 -->
          <div class="curriculum-name" :title="curriculum.name">
            {{ curriculum.name ? curriculum.name : $t('loc.draftCurriculum') }}
          </div>
          <!-- 作者名 -->
          <div class="curriculum-author-name">
            {{ $t('loc.curriculum4') }} {{ curriculum.authorName | formatUserName }}
          </div>
          <!-- 年龄组 -->
          <capsule
            v-if="getAgeGroupNames"
            class="add-margin-t-24"
            size="large"
            :leftText="$t('loc.ageGp')"
            :rightText="getAgeGroupNames"
            color="orange"
          ></capsule>
          <!-- 领域 -->
          <capsule
            v-if="getDomainNames"
            class="add-margin-t-12"
            size="large"
            :leftText="$t('loc.curriculum5')"
            :rightText="getDomainNames"
            color="pink"
          ></capsule>
          <!-- 系列课程信息 -->
          <div class="currriculum-info add-margin-t-12">
            <div class="curriculum-info-item">
              <div class="currriculum-info-num">{{ curriculum.unitsNum ? curriculum.unitsNum : 0 }}</div>
              <div class="currriculum-info-title">{{ $t('loc.curriculum6') }}</div>
            </div>
            <el-divider
              direction="vertical"
              class="curriculum-info-item-divider"
            ></el-divider>
            <div class="curriculum-info-item">
              <div class="currriculum-info-num">{{ curriculum.weeksNum ? curriculum.weeksNum : 0 }}</div>
              <div class="currriculum-info-title">{{ $t('loc.curriculum7') }}</div>
            </div>
            <el-divider
              direction="vertical"
              class="curriculum-info-item-divider"
            ></el-divider>
            <div class="curriculum-info-item">
              <div class="currriculum-info-num">{{ curriculum.activitiesNum ? curriculum.activitiesNum : 0 }}</div>
              <div class="currriculum-info-title">{{ $t('loc.curriculum8') }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 框架 -->
    <el-row v-if="curriculum.framework" class="add-margin-t-36">
      <div class="currriculum-info-tag">
        {{ $t('loc.lesson2NewLessonFormLabelFramework') }}
      </div>
      <div class="curriculum-info-value">
        {{ curriculum.framework.name }}
      </div>
    </el-row>
    <!-- 描述 -->
    <el-row v-show="curriculum.description" class="add-margin-t-36">
      <div class="currriculum-info-tag">
        {{ $t('loc.curriculum9') }}
      </div>
      <div class="curriculum-info-value">
        <text-viewer :text="curriculum.description"></text-viewer>
      </div>
    </el-row>
    <!-- 视频介绍 -->
    <el-row v-if="curriculum.introductionMedias && curriculum.introductionMedias.length > 0" class="add-margin-t-36">
      <div class="currriculum-info-tag">
        {{ $t('loc.curriculum10') }}
      </div>
      <div class="curriculum-info-value">
        <el-row>
          <el-col class="m-r-md" v-for="(i, index) in curriculum.introductionMedias" :key="index" :span="5">
            <curriculum-media-viewer :url="i.url" :coverMediaImg="i.coverImg" :type="i.type" :preview="true" :clickToPreview="true"/>
          </el-col>
        </el-row>
      </div>
    </el-row>
    <!-- 关键资源 -->
    <el-row class="add-margin-t-36">
      <div class="currriculum-info-tag">
        {{ $t('loc.curriculum11') }}
      </div>
      <div class="curriculum-info-value">
        <key-resources :curriculumId="curriculum.id" :curriculumName="curriculum.name" :books="curriculum.books" :vocabularies="curriculum.vocabularyNum" :printables="curriculum.printableNum" :activities="curriculum.activitiesNum" :type="type" :public="curriculum.type==='PUBLIC'"></key-resources>
      </div>
    </el-row>
    <!-- 单元 -->
    <el-row class="add-margin-t-36" v-if="curriculum.units && curriculum.units.length > 0">
      <div class="currriculum-info-tag">
        {{ $t('loc.curriculum12') }}
      </div>
      <div class="curriculum-info-value">
        <el-row :gutter="20">
          <el-col v-for="i in curriculum.units" :key="i.id" :span="12" class="m-t-sm">
            <unit-card :unit="i" :curriculum="curriculum" :public="curriculum.type==='PUBLIC'" :type="type" :frameworkId="curriculum.framework ? curriculum.framework.id : ''" :currentContentLangCode="currentContentLangCode"></unit-card>
          </el-col>
        </el-row>
      </div>
    </el-row>
  </div>
</template>

<script>
import CurriculumActionBar from '../CurriculumActionBar.vue'
import CurriculumMediaViewer from '../CurriculumMediaViewer.vue'
import Capsule from '../Capsule.vue'
import TextViewer from './components/TextViewer.vue'
import KeyResources from './components/KeyResources.vue'
import UnitCard from './components/UnitCard.vue'
import Lessons2 from '@/api/lessons2'
import { mapState } from 'vuex'
export default {
  props: ['id','type','isAdmin', 'currentContentLangCode'],
  name: 'CurriculumDetail',
  components: {
    CurriculumActionBar,
    CurriculumMediaViewer,
    Capsule,
    TextViewer,
    KeyResources,
    UnitCard
  },
  watch: {
    id: {
      immediate: true,
      handler (newVal) {
        if (newVal) {
          this.scrollToTop()
          this.getDetail(newVal, true)
        }
      }
    },
    currentContentLangCode (val) {
      this.getDetail(this.id, false, val)
    }
  },
  computed: {
    ...mapState({
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 内容的源语言码
    }),
    // 判断内容的当前语言码是否与源语言码相同
    isSameLanguage () {
      return this.currentContentLangCode === this.contentOriginalLanguage
    },
    // 系列课程封面
    getCoverMedia () {
      if (this.curriculum.coverMedias && this.curriculum.coverMedias.length > 0) {
        return this.curriculum.coverMedias[0].url
      }
      return ''
    },
    // 获取领域信息
    getDomainNames () {
      if (this.curriculum.domains) {
        return this.curriculum.domains.map(x => x.abbreviation).join(', ')
      }
      return ''
    },
    // 获取年龄组信息
    getAgeGroupNames () {
      if (this.curriculum.ages) {
        let ages = JSON.parse(this.curriculum.ages).map(x => x.name)
        return ages.join(', ')
      } else {
        return ''
      }
    }
  },
  data () {
    return {
      loading: false, // 页面加载
      curriculum: {} // 系列课程信息
    }
  },
  destroyed () {
    // 设置内容源语言码
    this.$store.commit('SET_ORIGINAL_LANGUAGE', '')
    // 设置内容的当前语言码
    this.$store.commit('SET_CURRENT_LANGUAGE', '')
  },
  mounted () {
  },
  created () {
  },
  methods: {
    // 获取系列课程详情
    async getDetail (id, needDetectLanguage, langCode) {
      this.loading = true
      let params = {
        id: id,
        ...(needDetectLanguage && { isDetect: needDetectLanguage }),
        ...((langCode && !this.isSameLanguage) && { langCode })
      }
      await Lessons2.getCurriculumDetail(params)
      .then(res => {
        this.setPlanCenterType(res.ageValues)
        this.curriculum = res
        // 如果返回了识别的语言码
        if (needDetectLanguage && res.langCode && res.langCode.trim() !== '') {
          // 设置内容源语言码
          this.$store.commit('SET_ORIGINAL_LANGUAGE', res.langCode)
          // 设置内容的当前语言码
          this.$store.commit('SET_CURRENT_LANGUAGE', res.langCode)
          // 更新当前语言码回调
          this.$emit('setCurriculumLangCode', res.langCode)
        }
        // 通知父组件更新当前单元课程
        this.$emit('setCurrentCurriculum', this.curriculum)
        this.loading = false
      })
      .catch(error => {
        this.loading = false
      })
    },
    // 设置 Plan Center 类型
    setPlanCenterType (ages) {
      // 处理年龄数据，避免空指针
      ages = ages || []
      let type = 'PS' // 默认为 PS
      // 按照年龄大小判断 Plan Center 类型，从低到高适配
      if (ages.includes('0') || ages.includes('1') || ages.includes('2') || ages.includes('1,2')) {
        type = 'IT'
      } else if (ages.includes('3') || ages.includes('4')) {
        type = 'PS'
      } else if (ages.includes('5') || ages.includes('Grade 1') || ages.includes('Grade 2')) {
        type = 'K'
      } else {
        type = 'PS'
      }
      this.$store.commit('SET_PLANCENTERTYPE', type)
    },
    // 通知父组件更新系列课程列表
    updateCurriculum () {
      this.$emit('updateCurriculum')
    },
    // 滚动到顶部
    scrollToTop () {
      this.$nextTick(() => {
        let top = document.getElementById('curriculum')
        if (top) {
          top.scrollIntoView({ block: 'start', behavior: 'smooth' })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.curriculum-detail {
  padding: 20px 36px;
  font-size: 16px;
  color: #323338;
}
.curriculum-name {
  font-size: 26px;
  font-weight: bold;
  color: #323338;
  //超出自动换行，显示省略号
  // overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;
  word-break: break-word;
}
.curriculum-author-name {
  font-size: 18px;
  color: #676879;
  margin-top: 8px;
}
.currriculum-info {
  height: 80px;
  width: 100%;
  background: #f5f6f8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  border-radius: 15px;
  padding: 10px;
  .curriculum-info-item {
    width: 33.3%;
  }
  .curriculum-info-item-divider {
    height: 100%;
  }
  .currriculum-info-num {
    color: #111111;
    font-weight: 600;
    font-size: 18px;
    color: #323338
  }
  .currriculum-info-title {
    font-size: 16px;
  }
}
.currriculum-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 18px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}
.curriculum-info-value {
  padding: 16px 0 0 16px;
}
</style>
