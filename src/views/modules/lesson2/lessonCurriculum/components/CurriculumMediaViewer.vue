<template>
  <div class="media-viewer">
    <div style="position: relative;padding-top: 56.25%;">
      <div class="media-viewer__inner">
        <!--如果用户上传的是图片资源或图片链接-->
        <el-image v-if="(!failed && _type === 'image') || (!failed &&_type === 'externalMediaImg')" :src="url" :fit="fit|| 'cover'" @error="imageLoadError"/>
        <!--如果用户上传的是视频资源-->
        <!-- <el-image v-if="!failed && _type === 'video'" :src="coverMediaImg" @click.stop="showPreviewDialog=true"/> -->
        <!--如果用户上传的是视频链接-->
        <div v-if="!failed && _type === 'video' && preview" style="position: relative" @click.stop="openVideo" class="lg-pointer">
          <el-image :src="coverMediaImg" fit="cover"/>
          <img v-if="clickToPreview" src="@/assets/img/icon/video.png" class="play-button lg-pointer"/>
          <img v-else src="@/assets/img/icon/video.png" class="play-button lg-pointer"/>
        </div>
        <video v-if="!failed && _type === 'video' && !preview" :src="url" controls></video>
        <iframe class="iframeStyle"
                :src="url"
                v-if="!failed && _type === 'externalMedia' && !preview"></iframe>
        <div v-if="!failed && _type === 'externalMedia' && preview" @click.stop="openVideo" class="lg-pointer">
          <el-image :src="coverMediaImg" fit="cover"/>
          <img v-if="clickToPreview" src="@/assets/img/icon/video.png" class="play-button lg-pointer"/>
          <img v-else src="@/assets/img/icon/video.png" class="play-button lg-pointer"/>
        </div>
        <div class="media-viewer__error" v-if="failed" style="background-color: #e7e7e7">
          <el-image style="width: 35%" :src="errorImageURL" :fit="fit || 'cover'"/>
          <span>{{ $t('loc.lesson2FailedLoadingMedia') }}</span>
        </div>
      </div>
      <div class="media-viewer__source" v-if="source" :title="source">
        Image Source: {{ source }}
      </div>
    </div>
    <el-dialog title="Preview" :append-to-body="true" :visible.sync="showPreviewDialog" :before-close="closeVideo">
      <iframe ref="vimeoPlayer"
              v-if="_type === 'externalMedia'"
              class="iframeStyle"
              :src="videoUrl"
              :class="{ 'mobile-iframe': isMobileView }"
              :width="isMobileView ? '' : '640'"
              :height="isMobileView ? '' : '360'"
              allowfullscreen
              >
      </iframe>
      <video v-else controls width="640" height="360" ref="video" :src="videoUrl"></video>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeVideo">{{ $t('loc.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import errorImage from '@/assets/img/lesson2/image.png'
import errorVideoImage from '@/assets/img/lesson2/Video.png'

export default {
  name: 'CurriculumMediaViewer',
  props: [
    'preview',
    'clickToPreview',
    'url',
    'source',
    'coverMediaImg',
    'fit',
    'type', // image 图片、video 视频
    'failedType'// 加载失败时，显示图片类型：image 图片、video 视频,默认根据type设置
  ],
  computed: {
    _type () {
      if (!this.url) {
        return 'unknown'
      }
      if (this.type) {
        return this.type
      }
      return /.+(\.jpeg|\.png|\.jpg)/i.test(this.url) && 'image' || // 以资源的形式上传的图片
        /.+(\.mp4|\.mov)/i.test(this.url) && 'video' || // 以资源的形式上传的视频
        /^https:\/\/i.vimeocdn.com\/video\/.*/.test(this.url) && 'externalMediaImg' ||
        /^https:.*/.test(this.url) && 'externalMedia' || // 以链接的形式上传的视频
        'unknown' // 未知
    }
  },
  data () {
    return {
      failed: false,
      errorImageURL: null,
      showPreviewDialog: false,
      videoUrl: '',
      isMobileView: false
    }
  },
  watch: {
    url () {
      this.failed = false
    },
    // 监听预览视频弹窗是否显示
    showPreviewDialog (val) {
      // 显示时将视频链接赋值，隐藏时将视频链接去除掉
      if (val) {
        this.videoUrl = this.url
      } else {
        this.videoUrl = ''
      }
    }
  },
  created() {
    // 检查是否为移动端
    this.checkMobileView()
    // 监听窗口大小变化
    window.addEventListener('resize', this.checkMobileView)
  },
  beforeDestroy() {
    // 移除监听器
    window.removeEventListener('resize', this.checkMobileView)
  },
  methods: {
    imageLoadError () {
      this.failed = true
      this.errorImageURL = this.getErrorImageURL(errorImage)
    },
    videoLoadError () {
      this.failed = true
      this.errorImageURL = this.getErrorImageURL(errorVideoImage)
    },
    getErrorImageURL (defaultValue) {
      if (!this.failedType) {
        return defaultValue
      }
      if (this.failedType === 'image') {
        return errorImage
      }
      return errorVideoImage
    },
    // 打开视频弹窗
    openVideo () {
      this.showPreviewDialog = true
      this.$analytics.sendEvent('web_curriculum_video')
    },
    // 关闭视频弹窗
    closeVideo () {
      this.showPreviewDialog = false
    },
    checkMobileView() {
      this.isMobileView = window.innerWidth <= 768
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-image {
  height: 100%;
  width: 100%;
}
/deep/
.play-button {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
.media-viewer {
  overflow: hidden;
}

.media-viewer__inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  & > * {
    height: 100%;
    width: 100%;
  }

  .media-viewer__error {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-flow: column;
    background-color: #f2f2f2;
    color: #999;
  }
  .iframeStyle{
    z-index: 1;
    opacity: 1;
    border: 0px;
  }
}
/deep/ .el-dialog{
  width: fit-content;
}
/deep/ .el-dialog__body{
  padding: 16px 20px;
}

.media-viewer:hover .media-viewer__source {
  display: block;
}

.media-viewer__source {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #3A3F51B2;
  color: #fff;
  padding: 8px 10px;
  line-height: 16px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
