<template>
  <div>
    <div v-if="size == 'large'" class="curriculum-card-large" @click="detail">
      <div class="curriculum-hot">
        推荐
      </div>
      <!-- 标题 -->
      <div class="curriculum-name">
        Family Unit for Kindergarten
      </div>
      <!-- 作者 -->
      <div class="display-flex m-t-sm">
        <!-- 头像 -->
        <div>
          <el-avatar size="small" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
        </div>
        <!-- 姓名 -->
        <div class="curriculum-author"><PERSON></div>
      </div>
      <!-- 年龄组 -->
      <div class="m-t-sm">
        <div class="curriculum-age">
          <PERSON><PERSON> (1-3)
        </div>
      </div>
      <!-- 课程信息 -->
      <div class="currriculum-info m-t-sm">
        <div class="curriculum-info-item">
          <div class="currriculum-info-num">3</div>
          <div class="currriculum-info-title">Units</div>
        </div>
        <div class="curriculum-info-item">
          <div class="currriculum-info-num">8</div>
          <div class="currriculum-info-title">Weeks</div>
        </div>
        <div class="curriculum-info-item">
          <div class="currriculum-info-num">36</div>
          <div class="currriculum-info-title">Lesson activities</div>
        </div>
      </div>
    </div>
    <div v-else>
      <el-card class="box-card m-t-sm" :body-style="{ padding: '0px', cursor: 'pointer' }" @click.native="detail">
        <div style="position: relative;">
          <div v-if="isAdmin" style="position: absolute;top: 0;left: 0;width: 100%;z-index: 100;">
            <!-- 右上角内容：操作 -->
            <div style="float:right;">
              <el-dropdown style="margin-right: 10px;" @click.native.stop trigger="click">
                <curriculum-item-operation class="el-dropdown-link"></curriculum-item-operation>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item icon="el-icon-edit" @click.native.stop="editClickHandler()">
                      {{ $t('loc.edit') }}
                    </el-dropdown-item>
                    <el-dropdown-item icon="el-icon-copy-document" @click.native.stop="replicateClickHandler()">
                      {{ $t('loc.plan66') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
            </div>
          </div>
          <!-- 封面 -->
          <el-image :src="src"></el-image>
          <!-- 系列课程基本信息 -->
          <div class="m-sm">
            <div>Title</div>
            <div class="m-t-xs">
              <el-tag size="small" type="info">Toddler (1-3)</el-tag>
            </div>
            <div class="display-flex m-t-xs">
              <capsule leftText="3" rightText="Units" color="green"></capsule>
              <capsule leftText="8" rightText="Weeks" color="green" class="m-l-xs"></capsule>
              <capsule leftText="36" rightText="Lesson activities" color="green" class="m-l-xs"></capsule>
            </div>
          </div>
          <!-- 作者信息和点赞收藏 -->
          <div class="display-flex flex-row-between m-l-xs m-r-xs">
            <div class="display-flex">
            <!-- 头像 -->
              <div>
                <el-avatar size="small" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
              </div>
              <!-- 姓名 -->
              <div class="curriculum-author">Jane Cooper</div>
            </div>
            <!-- 点赞和收藏信息 -->
            <div>
              <el-button type="text" @click.stop="like" style="padding: 0">
                <icon-alibaba :class="[`icon-alibaba-like-${userLiked ? 'on' : 'off'}`]"/>
                <span>{{ likeCount }}</span>
              </el-button>
              <el-button type="text" @click.stop="addFavorite($event)" style="padding: 0">
                <icon-alibaba :class="[`icon-alibaba-favorite-${userFavorite ? 'on' : 'off'}`]"/>
                <span>{{ favoriteCount }}</span>
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import CurriculumItemOperation from './CurriculumItemOperation.vue'
import Capsule from './Capsule.vue'
import IconAlibaba from '@/views/modules/lesson2/lessonLibrary/components/IconAlibaba'

export default {
  name: 'CurriculumItem',
  props: ['size', 'isAdmin'],
  components: {
    CurriculumItemOperation,
    Capsule,
    IconAlibaba
  },
  data () {
    return {
      src: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
      likeCount: 101,
      favoriteCount: 11,
      userLiked: false,
      userFavorite: false
    }
  },
  methods: {
    // 编辑课程
    editClickHandler () {
      this.$router.push({
        name: 'curriculumEdit',
        params: {
          curriculumId: 11111111
        }
      })
    },
    // 系列课程详情
    detail () {
      this.$router.push({
        name: 'curriculumEdit',
        params: {
          curriculumId: 11111111
        }
      })
    },
    // 点赞
    like () {
      this.userLiked = !this.userLiked
      if (this.userLiked) {
        this.likeCount = this.likeCount + 1
      } else {
        this.likeCount = this.likeCount - 1
      }
    },
    addFavorite (e) {
    }
  }
}
</script>

<style lang="less" scoped>

.curriculum-hot {
  position: absolute;
  top: 10px;
  font-size: 14px;
  padding: 5px 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 5px;
  left: 0px;
  background: #FFDE8A;
  color: #4D4329;
  box-shadow: 3px 0px 0px 0px #A3AFC4
}
.curriculum-card-large {
  cursor: pointer;
  background: #658FEA;
  padding: 40px;
  border: 1px solid #ccc;
  margin: 10px 0;
  border-radius: 5px;
  .curriculum-name {
    font-style: italic;
    color: #FFFFFF;
    font-weight: 600;
    font-size: 32px;
  }
  .curriculum-author {
    color: #FFFFFF;
  }
  .curriculum-age {
    background: #FFFFFF;
    border-radius: 20px;
    padding: 3px 12px;
    width: fit-content;
  }
  .currriculum-info {
    background: #93B1F0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 30px;
    text-align: center;
    width: fit-content;
    border-radius: 15px;
    padding: 10px;
    .curriculum-info-item {
      width: 100px;
    }
    .currriculum-info-num {
      font-style: italic;
      color: #1111111;
      font-weight: 600;
      font-size: 32px;
    }
    .currriculum-info-title {
      font-size: 14px;
    }
  }
}
.curriculum-card-small {
  padding: 20px;
  border: 1px solid #ccc;
  margin: 5px 0;

}
.curriculum-author {
  margin-left: 5px;
  line-height: 28px;
}
</style>
