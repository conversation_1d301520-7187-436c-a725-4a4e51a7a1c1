<template>
  <div>
    <!-- 添加材料的弹框 -->
    <el-dialog :title="$t('loc.addMaterial')" :visible.sync="dialogFormVisible" width="900px" :lock-scroll="false" top="10vh"
               :close-on-click-modal="false"
               @close="clearData"
               :append-to-body="true"
               custom-class="add-materials-dialog-class">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <!-- Add to -->
        <div class="tip-text">{{$t('loc.addTo')}}</div>
        <el-form-item prop="group">
          <el-select :disabled="isEdit" v-model="form.group" class="select-box" @change="groupChange" popper-class="popper-select-group">
            <el-option v-for="item in groupsArray" :key="item.centerId" :label="item.name"
                       :title="item.name"
                       :value="item.centerId"></el-option>
          </el-select>
        </el-form-item>
        <!-- Select Activity -->
        <div class="tip-text">
          <span>{{$t('loc.selectActivity')}}</span>
          <span style="color: #676879; font-weight: 400">
             {{$t('loc.selectManuallyActivityTip')}}
            </span>
        </div>
        <el-form-item prop="activity">
          <el-select :disabled="isEdit" v-model="form.activity" class="select-box" popper-class="popper-select-activity">
            <el-option v-for="item in activities" :key="item.id" :label="item.name"
                       :title="item.name"
                       :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- Add Materials -->
        <div class="tip-text">{{$t('loc.addMaterial')}}</div>
        <!--课程材料-->
        <el-form-item prop="material">
          <lesson-material-input ref="lessonMaterialInputRef" v-model="form.material" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="display-flex justify-content-between">
        <div>
          <el-button :loading="deleteLoading" v-show="isEdit" type="danger" @click="deleteMaterial">{{$t('loc.delete')}}</el-button>
        </div>
        <div class="rf">
          <el-button @click="cancelDialog">{{ $t('loc.cancel') }}</el-button>
          <el-button :loading="submitLoading" type="primary" @click="save('ruleForm')">{{ $t('loc.save') }}</el-button>
        </div>
      </div>
      <delete-sure-dialog :is-inner-dialog="true" @sureDelete="sureDeleteMaterials" ref="deleteSureDialogRef" :tip-content="$t('loc.dllResourceDeleteTip')"></delete-sure-dialog>
    </el-dialog>
  </div>
</template>

<script>
import LessonMaterialInput from '../../lessonLibrary/editor/lessonMaterialInput'
import tools from '../../../../../utils/tools'
import DeleteSureDialog from '../../../../dll/components/DeleteSureDialog'
export default {
  name: 'AddMaterialsDialog',
  components: { DeleteSureDialog, LessonMaterialInput },
  data () {
    return {
      dialogFormVisible: false,
      isEdit: false,
      form: {
        group: '',
        activity: '',
        material: '' // 课程材料，对象，media、description、attachmentMedias (附件，对象数组)
      },
      groupsArray: [],
      activities: [],
      submitLoading: false,
      deleteLoading: false,
      editMaterialsId: '',
      centerId: '',
      rules: {
        group: [
          {
            required: true,
            message: this.$t('loc.fieldReq'),
            trigger: 'change'
          }
        ],
        activity: [
          {
            required: true,
            message: this.$t('loc.fieldReq'),
            trigger: 'change'
          }
        ],
        material: [
          {
            type: 'object',
            message: this.$t('loc.fieldReq'),
            required: true,
            fields: {
              description: { message: this.$t('loc.fieldReq'), required: true, whitespace: true }
            }
          }
        ]
      },
      originalActivities: []
    }
  },
  methods: {
    show (show, edit, lessonMaterial) {
      this.dialogFormVisible = show
      this.isEdit = edit
      if (this.isEdit) {
        this.$nextTick(() => {
          this.$refs.lessonMaterialInputRef.showMaterialsDialog()
          let tempMaterial = JSON.parse(lessonMaterial)
          if (tempMaterial) {
            this.form.activity = tempMaterial.planItemId
            this.form.group = tempMaterial.centerId
            this.centerId = tempMaterial.centerId
            let tempFormMaterial = {
              'description': '',
              'media': {},
              'attachmentMedias': []
            }
            tempFormMaterial.description = '<!---->' + tempMaterial.material.descriptions[0]
            if (tempMaterial.material.media && tempMaterial.material.media.id) {
              tempFormMaterial.media = {
                'id': tempMaterial.material.media.id,
                'size': tempMaterial.material.media.size,
                'name': tempMaterial.material.media.sourceFileName,
                'url': tempMaterial.material.media.url
              }
            } else if (tempMaterial.material.externalMediaUrlId) {
              tempFormMaterial.media = {
                'externalMediaUrlId': tempMaterial.material.externalMediaUrlId,
                'externalMediaUrl': tempMaterial.material.externalMediaUrl
              }
            }
            if (tempMaterial.material.attachmentMedias && tempMaterial.material.attachmentMedias.length > 0) {
              tempFormMaterial.attachmentMedias = tempMaterial.material.attachmentMedias.map(attachmentMedia => {
                return {
                  'id': attachmentMedia.id,
                  'size': attachmentMedia.size,
                  'name': attachmentMedia.sourceFileName,
                  'url': attachmentMedia.url
                }
              })
            }
            this.form.material = tempFormMaterial
            this.editMaterialsId = tempMaterial.id
            this.getAddPlanMaterialInfo()
          }
        })
      } else {
        this.getAddPlanMaterialInfo()
      }
      this.$nextTick(() => {
        if (this.$refs.ruleForm && !this.isEdit) {
          // 重置输入信息，防止出现之前的红框
          this.$refs.ruleForm.resetFields()
        }
      })
    },
    // 获取添加材料的选择弹窗信息
    getAddPlanMaterialInfo () {
      if (this.planId && this.planId.trim().length > 0) {
        if (this.isEdit) {
          this.$axios
            .get($api.urls().getEditPlanMaterialInfo + '?planId=' + this.planId + '&centerId=' + this.centerId)
            .then(res => {
              if (res.centerModels && res.centerModels.length > 0) {
                this.groupsArray = res.centerModels
                this.groupsArray.forEach(groupModel => {
                  let tempActivities = []
                  let activityNames = []
                  groupModel.itemEntityList.forEach(itemEntity => {
                    if (itemEntity.name && itemEntity.name.trim().length > 0) {
                      activityNames.push(itemEntity.name.trim())
                    }
                  })
                  groupModel.itemEntityList.forEach(itemEntity => {
                    if (itemEntity.name && itemEntity.name.trim().length > 0) {
                      let tempActivity = {
                        'id': itemEntity.id,
                        'name': itemEntity.name
                      }
                      // 如果有相同的名称，就拼接星期几
                      if (activityNames.filter(name => { return name === itemEntity.name.trim() }).length > 1 && itemEntity.dayOfWeek) {
                        if (!itemEntity.centerId) {
                          tempActivity.name = itemEntity.name + ' (' + tools.calWeekDay(itemEntity.dayOfWeek) + ')'
                        }
                      }
                      tempActivities.push(tempActivity)
                    }
                  })
                  this.activities = tempActivities
                })
              }
            })
            .catch(error => {
            })
        } else {
          this.$axios
            .get($api.urls().getAddPlanMaterialInfo + '?planId=' + this.planId)
            .then(res => {
              if (res.centerModels && res.centerModels.length > 0) {
                this.groupsArray = res.centerModels.filter(centerModel => centerModel.name)
              }
            })
            .catch(error => {
            })
        }
      }
    },
    // 选择分组的改变
    groupChange () {
      this.groupsArray.forEach(centerModel => {
        if (centerModel.centerId === this.form.group) {
          let tempActivities = []
          let activityNames = []
          let tempItemEntityList = centerModel.itemEntityList
          // 去除name为空的活动
          tempItemEntityList = tempItemEntityList.filter(itemEntity => {
            return itemEntity.name
          })
          // 添加一个变量用于记录同一天的相同活动名的标识
          tempItemEntityList = tempItemEntityList.map(itemEntity => {
            if (itemEntity.centerId) {
              itemEntity.tempCategoty = itemEntity.centerId + '-' + itemEntity.name
            } else if (itemEntity.categoryId) {
              itemEntity.tempCategoty = itemEntity.categoryId + '-' + itemEntity.name + '-' + itemEntity.dayOfWeek
            } else {
              itemEntity.tempCategoty = ''
            }
            return itemEntity
          })
          tempItemEntityList.forEach(itemEntity => {
            if (itemEntity.name && itemEntity.name.trim().length > 0) {
              activityNames.push(itemEntity.name.trim())
            }
          })
          this.originalActivities = JSON.parse(JSON.stringify(tempItemEntityList))
          let hashTemp = {}
          tempItemEntityList = tempItemEntityList.reduce(function (init,item) {
            if (!hashTemp[item.tempCategoty]) {
              hashTemp[item.tempCategoty] = true
              init.push(item)
            }
            return init
          },[])
          tempItemEntityList.forEach(itemEntity => {
            if (itemEntity.name && itemEntity.name.trim().length > 0) {
              let tempActivity = {
                'id': itemEntity.id,
                'name': itemEntity.name,
                'originName': itemEntity.name,
                'dayOfWeek': itemEntity.dayOfWeek
              }
              if (activityNames.filter(name => { return name === itemEntity.name.trim() }).length > 1) {
                if (!itemEntity.centerId) {
                  tempActivity.name = itemEntity.name + ' (' + tools.calWeekDay(itemEntity.dayOfWeek) + ')'
                }
              }
              tempActivities.push(tempActivity)
            }
          })
          this.activities = tempActivities
        }
      })
    },
    // 保存添加材料的信息
    save (formName) {
      // 检验选项是否填写正确
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          let tempMaterial = {
            'descriptions': []
          }
          // 处理上传的附件
          let tempAttachmentMediaIds = []
          let attachmentMedias = []
          if (this.form.material && this.form.material.attachmentMedias && this.form.material.attachmentMedias.length > 0) {
            this.form.material.attachmentMedias.forEach(attachmentMedia => {
              tempAttachmentMediaIds.push(attachmentMedia.id)
              attachmentMedias.push({
                'id': attachmentMedia.id,
                'size': attachmentMedia.size,
                'sourceFileName': attachmentMedia.name,
                'url': attachmentMedia.url
              })
            })
          }
          tempMaterial.attachmentMediaIds = tempAttachmentMediaIds
          tempMaterial.attachmentMedias = attachmentMedias
          // 处理上传的图片或视频
          if (this.form.material && this.form.material.media) {
            if (this.form.material.media.externalMediaUrlId) {
              tempMaterial.externalMediaUrlId = this.form.material.media.externalMediaUrlId
              tempMaterial.externalMediaUrl = this.form.material.media.externalMediaUrl
            } else if (this.form.material.media.id) {
              tempMaterial.mediaId = this.form.material.media.id
            }
            tempMaterial.media = this.form.material.media
          }
          // 处理富文本的描述
          if (this.form.material && this.form.material.description) {
            let nodes = this.form.material.description && new DOMParser().parseFromString(this.form.material.description, 'text/html')
            // 处理富文本结构：关闭 Dom 的可编辑状态
            nodes = this.lessonMaterialDescriptionFormat(nodes)
            this.form.material.description = nodes.body.innerHTML
            tempMaterial.descriptions.push(this.form.material.description)
          }
          let requestUrl = $api.urls().addPlanMaterial
          let requestData = {
            'curriculumId': this.curriculumId,
            'planId': this.planId,
            'unitId': this.unitId,
            'material': tempMaterial
          }
          if (this.isEdit && this.editMaterialsId) {
            requestData.id = this.editMaterialsId
            requestData.planItemId = this.form.activity
            requestUrl = $api.urls().editPlanMaterial
          } else {
            let selectedItemObject = this.activities.find(activity => activity.id === this.form.activity)
            let itemIds = []
            this.originalActivities.forEach(activity => {
              if (activity.centerId) {
                if (activity.name === selectedItemObject.originName) {
                  itemIds.push(activity.id)
                }
              } else {
                if (activity.name === selectedItemObject.originName && activity.dayOfWeek === selectedItemObject.dayOfWeek) {
                  itemIds.push(activity.id)
                }
              }
            })
            requestData.planItemIds = itemIds
          }
          this.$axios
            .post(requestUrl, requestData)
            .then(res => {
              this.submitLoading = false
              if (res.success) {
                if (this.isEdit) {
                  this.$message.success(this.$t('loc.uptSfy'))
                } else {
                  this.$message.success(this.$t('loc.DLLAddSuccess'))
                }
                this.clearData()
                this.$emit('success')
                this.dialogFormVisible = false
              }
            })
            .catch(error => {
              this.submitLoading = false
              this.$message.error(error.response.data.error_message)
            })
        } else {
          return false
        }
      })
    },
    // 修改材料中内容多媒体信息的格式
    lessonMaterialDescriptionFormat (nodes) {
      if (nodes) {
        let deleteNode = []
        let listDomes = nodes.querySelectorAll('.textarea-item')
        listDomes.forEach(item => {
          item.setAttribute('contenteditable', 'false')
          // 移除加载中的 card
          if (item.className.indexOf('lesson-material-card') > -1 && item.getAttribute('isLoading') == 'true') {
            deleteNode.push(item.parentElement)
          }
        })
        deleteNode.forEach(item => {
          item.parentNode.replaceChild(document.createTextNode('\r\n'), item)
          item.remove()
        })
      }
      return nodes
    },
    // 删除材料的操作
    deleteMaterial () {
      this.$analytics.sendEvent('web_curriculum_add_materials_delete')
      this.$refs.deleteSureDialogRef.showDeleteSureDialog('ADDMATERIALS',this.editMaterialsId)
    },
    sureDeleteMaterials (deleteFrom,deleteId) {
      if (deleteFrom === 'ADDMATERIALS') {
        this.$axios
          .get($api.urls().deletePlanMaterial + '?id=' + deleteId)
          .then(res => {
            if (res.success) {
              this.$message.success('delete Success')
              this.$refs.deleteSureDialogRef.hindDeleteSureDialog()
              this.clearData()
              this.$emit('success')
              this.dialogFormVisible = false
            }
          })
          .catch(error => {
            this.$refs.deleteSureDialogRef.deleteError(error.response.data.error_message)
          })
      }
    },
    // 点击取消弹框的操作
    cancelDialog () {
      this.clearData()
      this.dialogFormVisible = false
    },
    // 关闭弹框清除数据
    clearData () {
      this.$refs.lessonMaterialInputRef.clearData()
      this.editMaterialsId = ''
      this.form.group = ''
      this.form.activity = ''
      this.form.material = ''
      this.groupsArray = []
      this.activities = []
    }
  },
  props: ['planId', 'curriculumId', 'unitId']
}
</script>

<style scoped>
.tip-text {
  margin-top: 16px;
  margin-bottom: 8px;

  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  color: #323338;
}

.select-box {
  box-sizing: border-box;

  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  width: auto;
  height: 36px;
  background: #ffffff;
  border-radius: 4px;
}

</style>
<style>
.add-materials-dialog-class {
  margin-bottom: 0 !important;
}

.add-materials-dialog-class .el-dialog__footer {
  border-top: none;
  padding-top: 10px;
  padding-bottom: 10px;
}

.add-materials-dialog-class .el-dialog__header {
  padding: 17px 20px 12px;
  border-bottom: none;
  font-size: 20px;
}

.add-materials-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}

.add-materials-dialog-class .el-dialog__header .el-dialog__title {
  color: #323338;
  font-weight: 600;
}

.add-materials-dialog-class .el-dialog__body {
  padding: 10px 24px !important;
  max-height: 450px;
  overflow-y: auto;
  word-break: break-all;
}

.add-materials-dialog-class ::-webkit-scrollbar {
  width: 5px;
}

/*定义滚动条轨道 内阴影+圆角*/
.add-materials-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.add-materials-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}
.popper-select-group {
  max-width: 852px;
}
.popper-select-activity {
  max-width: 852px;
}
</style>
