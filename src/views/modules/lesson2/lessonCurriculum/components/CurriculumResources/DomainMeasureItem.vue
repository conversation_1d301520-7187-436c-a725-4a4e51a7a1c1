<template>
  <!-- 展示领域和测评点的组件 -->
  <div class="white-background">
    <div class="lg-text-color-primary font-weight-semibold line-height-28">
      {{domain.abbreviation}}-{{domain.name}}
    </div>
    <div class="grid-measure">
      <div v-for="measure in domain.children" :key="measure.id" class="display-flex align-items add-padding-l-12 line-height-28 lg-text-color-primary">
        <span class="add-margin-r-4" style="font-weight: 500">{{measure.abbreviation}}</span>
        <span class="font-weight-normal overflow-ellipsis" >{{measure.name}}</span>
        <span v-show="measure.core" style="color:red"> * </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DomainMeasureItem',
  props: ['domain']
}
</script>

<style scoped>
.grid-measure {
  display: grid;
  grid-template-columns: repeat(2,1fr);
}
</style>
