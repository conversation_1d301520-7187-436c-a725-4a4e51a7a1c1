<template>
    <!-- 资源书的组件 -->
    <div class="position-relative">
       <div style="width: 128px" class="display-flex flex-direction-col font-size-14">
         <div class="position-relative img-div add-margin-b-6" >
           <el-image class="img-size border-radius-4" :src="thumbnailUrl" :fit="imgFit">
            <div slot="error" class="el-image__error">
            {{ $t('loc.imageLoadFailed') }}
            </div>
          </el-image>
           <img v-if="!book.volumeInfo" src="@/assets/img/icon/video.png" class="play-button lg-pointer" @click.stop="preview"/>
         </div>
         <div :title="title" class="lg-text-color-primary line-height-22 overflow-ellipsis">{{title}}</div>
         <div :title="authorName" v-if="authorName" class="lg-text-color-primary line-height-22 overflow-ellipsis ">{{ $t('loc.lessons2LessonDetailToAuthorName') }} {{authorName}}</div>
         <div v-if="showAddVideo && title" @click="searchBookVideo" class="lg-pointer add-padding-tb-8 add-padding-lr-12  display-inline-flex align-items justify-content border-radius-4 add-margin-t-5" style="background-color: #EBEEF5;height: 32px">
           <span class="line-height-22 font-weight-semibold lg-text-color-primary font-size-12">{{$t('loc.addVideoBook')}}</span>
         </div>
       </div>
        <i v-show="!isShowShadow && !notShowDelete" style="font-size: 8px !important;" class="el-icon-close" @click.stop="deleteBook"/>
        <div v-if="isShowShadow"
           style="background-color: rgba(58, 63, 81, 0.7); z-index: 99; position: absolute; top: 0; left: 0; right: 0;bottom: 44px"
             class="display-flex flex-center-center">
            <div class="lg-pointer font-weight-semibold text-white font-size-14" @click="viewAllBookList">{{$t('loc.viewAll')}} <i class="el-icon-arrow-right"></i></div>
        </div>

      <!--视频预览弹框-->
      <el-dialog :visible.sync="showPlayer" width="691px" class="popup-container" :append-to-body="true">
        <div slot="title" style="height: 21px;font-size: 24px">{{ $t('loc.preview') }}</div>
        <iframe :src="showPlayer && `https://www.youtube.com/embed/${book.id.videoId}`" v-if="book"
                width="647px" height="428px" allowfullscreen/>
        <div slot="footer">
          <el-button @click="closePreview">{{ $t('loc.close') }}</el-button>
        </div>
      </el-dialog>
    </div>
</template>
<script>
import tools from '@/utils/tools'
export default {
  name: 'BookItem',
  data () {
    return {
      imgFit: 'cover',
      showPlayer: false
    }
  },
  props: ['book','isShowShadow','notShowDelete','isAdd'],
  computed: {
    // 书的封面
    thumbnailUrl () {
      if (this.book.volumeInfo) {
        let { volumeInfo = {} } = this.book || {}
        let { imageLinks = {} } = volumeInfo
        let { thumbnail } = imageLinks
        return thumbnail
      } else {
        let {snippet: {thumbnails: {high: {url}}}} = this.book
        return url
      }
    },
    // 书的标题
    title () {
      if (this.book.volumeInfo) {
        let { volumeInfo = {} } = this.book || {}
        let { title } = volumeInfo
        return title
      } else {
        let {snippet: {title}} = this.book
        return title
      }
    },
    // 书的作者
    authorName () {
      let { volumeInfo = {} } = this.book || {}
      let { authors = [] } = volumeInfo
      let [authorName] = authors
      return authorName
    },
    // 书的id
    bookId () {
      if (this.book.volumeInfo) {
        return this.book.id
      } else {
        return this.book.id.videoId
      }
    },
    // 是否显示添加视频书
    showAddVideo () {
      return this.book.showAddVideo
    }
  },
  methods: {
    deleteBook () {
      this.$analytics.sendEvent('web_curriculum_add_book_delete')
      if (this.isAdd) {
        this.$emit('delete',this.bookId)
      } else {
        this.$emit('delete',this.book.platformId)
      }
    },
    openBookUrl (url) {
      tools.openUrlWithWebAndIPad(url)
    },
    // 查看所有的资源书
    viewAllBookList () {
      this.$emit('viewAll','book')
    },
    // 预览视频书
    preview () {
      if (window.lessonVideo) {
        window.lessonVideo.pause()
      }
      this.showPlayer = true
    },
    closePreview () {
      this.showPlayer = false
    },
    showBookVideo () {
      if (this.book.volumeInfo) {
        this.openBookUrl(this.book.volumeInfo.infoLink)
      } else {
        // 表示的是视频
        this.preview()
      }
    },
    // 搜索该书对应的视频书
    searchBookVideo () {
      if (this.title) {
        this.$emit('searchVideo',this.book)
      }
    }
  }
}
</script>

<style scoped>
.img-size{
  height: 152px;
  width: 128px;
}
.el-icon-close {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: rgba(50, 51, 56, 0.7);
  border-radius: 50%;
  color: #fff;
  width: 16px;
  height: 16px;
  display: inline-block;
  cursor: pointer;
  line-height: 16px;
  text-align: center;
}
.play-button {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
.img-div {
  height: 152px;
}
</style>
