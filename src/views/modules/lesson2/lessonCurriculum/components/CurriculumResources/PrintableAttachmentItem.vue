<template>
  <!-- 展示资源附件的组件 -->
  <div v-if="!file.isShowViewAll"
       class="display-flex align-items  add-padding-lr-12 border-radius-4"
       :class="[isInMaterial ? 'white-background' : 'bg-color-F5F6F8']"
       style="height: 60px;min-width: 0">
    <img class="img-size" :src="fileIcon" alt="">
    <div @click="downloadResourceAttachment"
         :title="file.name"
         class="display-flex flex-direction-col flex-1 add-margin-l-8 lg-"
         :class="[{'lg-pointer': !notDownload}]"
         style="min-width: 0">
      <div class="display-flex align-items w-full lg-text-color-primary line-height-22">
        <span class="overflow-ellipsis">{{ file.name.substring(0, file.name.lastIndexOf('.')).trim() }}</span>
        <span style="flex-shrink: 0">.{{file.fileType ? file.fileType : file.name.substring(file.name.lastIndexOf('.') + 1) }}</span>
      </div>
      <div style="color: #999999" class="line-height-22">{{ formatSize(file.size) }}</div>
    </div>
    <i v-if="!notShowDelete" class="el-icon-close add-margin-l-8 lg-pa-5 lg-pointer" @click="clickDeleteAttachment"></i>
    <i v-if="notShowDelete && !notDownload" style="color: #10B3B7;font-size: 18px" class="el-icon-download add-margin-l-8 lg-pa-5 lg-pointer" @click="downloadResourceAttachment"></i>
  </div>
  <div v-else  class="white-background flex-center-center border-radius-4" style="height: 60px;">
    <div class="lg-pointer font-weight-semibold text-primary font-size-14" @click="viewAllFilesList">{{$t('loc.viewAll')}} <i class="el-icon-arrow-right"></i></div>

  </div>
</template>

<script>
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
export default {
  name: 'PrintableAttachmentItem',
  props: ['file','isShowViewAll','isInMaterial','notShowDelete','notDownload'],
  computed: {
    fileIcon () {
      let name = this.file.name
      let extension = this.file.fileType ? this.file.fileType : name.substring(name.lastIndexOf('.') + 1)
      return this.fileIcons[extension] || file
    }
  },
  data () {
    return {
      fileIcons: { doc, docx, pdf, ppt, pptx, xls, xlsx, file },
      notJumpUrl: 'javascript:;'
    }
  },
  methods: {
    clickDeleteAttachment () {
      this.$analytics.sendEvent('web_curriculum_add_attachment_delete')
      this.$emit('delete',this.file.id)
    },
    viewAllFilesList () {
      this.$emit('viewAll','file')
    },
    formatSize (size) {
      let value = size
      let suffix = 'B'
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'KB'
      }
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'MB'
      }
      return `${value}${suffix}`
    },
    // 下载资源附件
    downloadResourceAttachment () {
      if (this.file.url && !this.notDownload) {
        window.open('_blank').location = this.file.url
      }
    }
  }
}
</script>

<style scoped>
.img-size {
  height: 40px;
  width: 33px;
}
</style>
