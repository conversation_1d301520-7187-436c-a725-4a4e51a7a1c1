<template>
  <div>
    <!-- 添加词汇的弹框 -->
    <el-dialog :title="$t('loc.addKeyVocabulary')" width="1124px" @close="clearContent" :visible.sync="addVocabularyResourceDialogVisible"
               :lock-scroll="false" top="2vh" :close-on-click-modal="false"
               custom-class="add-vocabulary-resource-dialog-class" >
      <add-resource-head-info ref="addResourceHeadInfoFromDllRef" :select-resource-num="haveSelectedNum" :unit-week-title="unitWeekTitle" @select="selectItem"></add-resource-head-info>
      <div v-show="isPreview" class="add-margin-t-10 grid-dll">
        <key-vocabulary-item v-for="dll in previewSelectedDllArray" :key="dll.id" :dll="dll"
                             not-show-delete="true" is-show-select="true" @select="keyVocabularySelectChange"
                             ></key-vocabulary-item>
      </div>
      <div v-show="!isPreview">
        <!-- 切换 DLL 类型按钮 -->
        <div class="w-full flex-center-center height-48 add-margin-t-10 add-margin-b-16">
          <el-radio-group class="lg-pa-6 border-radius-4 light-primary-bg title-radio-group"  v-model="type">
            <el-radio-button label="SUBJECT">{{$t('loc.dllVocabularyBySubjects')}}</el-radio-button>
            <el-radio-button label="STAFF">{{$t('loc.dllActivitiesByStaff')}}</el-radio-button>
          </el-radio-group>
        </div>
        <!-- <el-tabs class="flex-center-center lg-tabs" v-model="type">
          <el-tab-pane :label="$t('loc.dllVocabularyBySubjects')" name="SUBJECT"></el-tab-pane>
          <el-tab-pane :label="$t('loc.dllActivitiesByStaff')" name="STAFF"></el-tab-pane>
        </el-tabs> -->
        <div style="height: 356px">
          <!-- 员工 DLL 词汇活动 -->
          <div class="h-full" v-show="type === 'STAFF'">
            <el-row :gutter="20" class="h-full">
              <el-col class="h-full" :span="6">
                <!-- 展示机构学校和员工名称 -->
                <dll-center-and-staff is-from-resource="true"  @getDllList="getDllList" ></dll-center-and-staff>
              </el-col>
              <el-col class="h-full" :span="18">
                <!-- 展示员工或机构的 DLL 列表 -->
                <school-dll-content-list :selectedVocabularies="haveSelectedByStaffDllArray" @select="keyVocabularySelectChange" :is-show-resource="true"  :agencyId="currentUser.default_agency_id" ref="schoolDllContentListRef"  @settingCheck="settingStaffVocabularyCheck"></school-dll-content-list>
              </el-col>
            </el-row>
          </div>
          <!-- DLL 词汇库-->
          <div class="h-full" v-show="type === 'SUBJECT'">
            <el-row class="h-full">
              <el-col class="h-full" :span="24">
                <!-- DLL 词汇库主题和资源列表 -->
                <dll-vocabulary-list is-from-resource="true" @select="keyVocabularySelectChange"  ref="dllVocabularyListRef" @settingCheck="settingVocabularyCheck"></dll-vocabulary-list>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <!-- 设置dialog的footer -->
      <span v-show="!isPreview" slot="footer" class="close-footer">
        <el-button @click="cancelDialog" plain class="cancelBtn">{{ $t('loc.cancel') }}</el-button>
        <el-button  type="primary" @click="previewSelectedDll"  class="saveBtn">{{ $t('loc.previewAndSave') }}</el-button>
      </span>
      <!-- 设置dialog的footer -->
      <span v-show="isPreview" slot="footer" class="w-full display-flex justify-content-between">
        <el-button plain @click="isPreview = false" class="cancelBtn">{{ $t('loc.back') }}</el-button>
        <el-button  type="primary" :loading="submitLoading" @click="saveDllResource" :disabled="haveSelectedNum == 0" class="saveBtn">{{ $t('loc.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import AddResourceHeadInfo from './AddResourceHeadInfo'
import DllCenterAndStaff from '../../../../../dll/components/DLLCenterAndStaff'
import DllVocabularyList from '../../../../../dll/components/DLLVocabularyList'
import SchoolDllContentList from '../../../../../dll/components/SchoolDLLContentList'
import { mapState } from 'vuex'
import { getCurrentUser } from '../../../../../../utils/common'
import tools from '../../../../../../utils/tools'
import KeyVocabularyItem from './keyVocabularyItem'
import LgTabs from '../../../../../../components/LgTabs'
export default {
  name: 'AddVocabularyResourceDialog',
  components: { LgTabs, KeyVocabularyItem, SchoolDllContentList, DllVocabularyList, DllCenterAndStaff, AddResourceHeadInfo },
  data () {
    return {
      addVocabularyResourceDialogVisible: false,
      searchBookContent: '',
      submitLoading: false,
      type: 'SUBJECT', // 默认展示 DLL 300 列表
      haveSelectedBySubjectDLLArray: [], // 从 subject 中选中的 item
      haveSelectedByStaffDllArray: [], // 从 staff 中选中的 item
      previewSelectedDllArray: [], // 展示预览的时候选中的 dll
      isPreview: false, // 是否是在预览界面
      selectItemIds: []
    }
  },
  methods: {
    showAddVocabularyResourceDialog () {
      this.addVocabularyResourceDialogVisible = true
      this.$nextTick(() => {
        this.$refs.addResourceHeadInfoFromDllRef.getItemInfo(this.planId)
      })
    },
    // 判断词汇操作来自那个模块
    keyVocabularySelectChange (selectDll) {
      if (this.isPreview) {
        if (selectDll.dllType === 'SUBJECT') {
          this.dealIsSelectedDll(this.haveSelectedBySubjectDLLArray,selectDll)
        } else if (selectDll.dllType === 'STAFF') {
          this.dealIsSelectedDll(this.haveSelectedByStaffDllArray,selectDll)
        }
      } else {
        this.$set(selectDll,'dllType',this.type)
        if (this.type === 'SUBJECT') {
          this.dealIsSelectedDll(this.haveSelectedBySubjectDLLArray,selectDll)
        } else if (this.type === 'STAFF') {
          this.dealIsSelectedDll(this.haveSelectedByStaffDllArray,selectDll)
        }
      }
    },
    // 处理词汇的选中和取消选中的逻辑
    dealIsSelectedDll (currentArray,selectDll) {
      // 选中该 dll
      if (selectDll.isCheck) {
        currentArray.push(selectDll)
      } else {
        if (selectDll.dllType === 'SUBJECT') {
          this.haveSelectedBySubjectDLLArray = currentArray.filter(item => {
            return item.id !== selectDll.id
          })
        } else {
          this.haveSelectedByStaffDllArray = currentArray.filter(item => {
            return item.id !== selectDll.id
          })
        }
      }
    },
    // 获取机构所有员工的 DLL 列表或者单个员工的 DLL 列表
    getDllList (staffId,staffUrl,staffName) {
      // 切换员工清空搜索框
      this.$refs.schoolDllContentListRef.clearSearchContent()
      if (tools.isNotEmpty(staffId)) {
        // 获取单个员工的 DLL 列表
        this.$refs.schoolDllContentListRef.getStaffDllContentList(staffId,staffUrl,staffName)
      } else {
        // 获取机构所有员工的 DLL 列表
        this.$refs.schoolDllContentListRef.getAllStaffDllContentList()
      }
    },
    // 预览选中的 dll
    previewSelectedDll () {
      this.previewSelectedDllArray = []
      this.haveSelectedBySubjectDLLArray.forEach(item => { this.previewSelectedDllArray.push(item) })
      this.haveSelectedByStaffDllArray.forEach(item => { this.previewSelectedDllArray.push(item) })
      if (this.previewSelectedDllArray.length === 0) {
        let messErro = this.$t('loc.selectvocabulary')
        this.$message.error(messErro)
      } else {
        this.isPreview = true
      }
    },
    // 保存 dll 资源
    saveDllResource () {
      // 点击添加词汇按钮
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_vocabulary_add')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_vocabulary_add')
      }
      if (!this.selectItemIds || this.selectItemIds.length === 0) {
        this.$message.error(this.$t('loc.selecterror'))
        return
      }
      this.submitLoading = true
      let dllResource = []
      this.previewSelectedDllArray.filter(item => item.isCheck).forEach(item => {
        let tempDllResource = {}
        tempDllResource.content = item.name
        tempDllResource.id = item.id
        tempDllResource.mediaId = item.mediaId || undefined
        dllResource.push(tempDllResource)
      })
      let curriculumId = this.curriculumId
      let planId = this.planId
      let unitId = this.unitId
      let planItemIds = this.selectItemIds
      let requestContent = { curriculumId: curriculumId, planId: planId,unitId: unitId,planItemIds: planItemIds,dllResource: dllResource }
      this.$axios
        .post($api.urls().addVocabularyResource , requestContent)
        .then(res => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success(this.$t('loc.DLLAddSuccess'))
            this.clearContent()
            this.addVocabularyResourceDialogVisible = false
            this.$emit('success')
          }
        })
        .catch(error => {
          this.submitLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 获取选中的活动
    selectItem (itemId) {
      this.selectItemIds = itemId
    },
    // 根据选中的词汇列表，设置词汇库请求数据的选中
    settingVocabularyCheck () {
      this.$refs.dllVocabularyListRef.settingDllSelected(this.haveSelectedBySubjectDLLArray)
    },
     // 根据选中的词汇列表，设置词汇库请求数据的选中
    settingStaffVocabularyCheck () {
      this.$refs.dllVocabularyListRef.settingDllSelected(this.haveSelectedBySubjectDLLArray)
    },
    clearContent () {
      this.$refs.addResourceHeadInfoFromDllRef.dataInitialize()
      this.haveSelectedByStaffDllArray = []
      this.haveSelectedBySubjectDLLArray = []
      this.previewSelectedDllArray = []
      this.isPreview = false
      this.$refs.schoolDllContentListRef.dataInitialize()
      this.$refs.dllVocabularyListRef.dataInitialize()
      this.selectItemIds = []
    },
    cancelDialog () {
      this.clearContent()
      this.addVocabularyResourceDialogVisible = false
    }
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser() // 当前用户
    }),
    haveSelectedNum () {
      return this.haveSelectedBySubjectDLLArray.length + this.haveSelectedByStaffDllArray.length
    }
  },
  props: ['planId','curriculumId','unitId','unitWeekTitle']
}
</script>

<style scoped>
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.cancelBtn {
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}

.saveBtn {
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}

.light-primary-bg {
  background: #DDF2F3 !important;
}
.grid-dll {
  display: grid;
  grid-template-columns: repeat(auto-fill, 175px);
  gap: 16px;
}
</style>
<style>
.add-vocabulary-resource-dialog-class {
  margin-bottom: 0 !important;
}

.add-vocabulary-resource-dialog-class .el-dialog__footer {
  border-top: none;
  padding-top: 10px;
  padding-bottom: 24px;
}

.add-vocabulary-resource-dialog-class .el-dialog__header {
  padding: 17px 20px 12px;
  border-bottom: none;
  font-size: 20px;
}

.add-vocabulary-resource-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}
.add-vocabulary-resource-dialog-class .el-dialog__header .el-dialog__title {
  color: #111c1c;
  font-weight: 600;
  font-size: 20px;
}

.add-vocabulary-resource-dialog-class .el-dialog__body {
  padding: 10px 24px !important;
  max-height: 500px;
  overflow-y: auto;
}
.add-vocabulary-resource-dialog-class ::-webkit-scrollbar {
  width: 5px;
}

/*定义滚动条轨道 内阴影+圆角*/
.add-vocabulary-resource-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.add-vocabulary-resource-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}
.title-radio-group .el-radio-button {
  margin-bottom: 0 !important;
}
.title-radio-group .el-radio-button .el-radio-button__inner {
  color: #10B3B7;
  background: #DDF2F3;
  border: none;
  font-weight: 600;
  font-size: 16px;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
.title-radio-group .el-radio-button__orig-radio:checked+.el-radio-button__inner {
  color: #fff !important;
  background-color: #10b3b7 !important;
}
.title-radio-group .el-radio-button:first-child .el-radio-button__inner {
  border-left: none;
  border-radius: 6px;
}
.title-radio-group .el-radio-button:last-child .el-radio-button__inner {
  border-right: none;
  border-radius: 6px;
}
</style>
