<template>
   <!-- 展示词汇的组件 -->
   <div style="width: 173px;height: 139px;background-color: transparent"
        class="display-flex flex-direction-col position-relative"
        :class="[dll.isCheck ? 'theme-border-line' : 'transparent-border-line']"
        @click="clickKeyVocabulary"
   >
     <!-- 选中分享的按钮 -->
     <div v-if="isShowSelect" class="position-absolute position-absolute-top-4 check-resource-div" :class="[dll.isCheck ? 'position-absolute-right-2' : 'position-absolute-right-4']">
       <el-checkbox @change="changeCheck"  v-model="dll.isCheck" @click.stop.native="()=>{}"></el-checkbox>
     </div>
     <!-- 资源图片 -->
     <el-image v-if="thumbnailUrl" class="img-size img-border" :src="thumbnailUrl" :fit="imgFit">
      <div slot="error" class="el-image__error">
        {{ $t('loc.imageLoadFailed') }}
      </div>
      </el-image>
     <div v-else class="no-img-border img-size flex-center-center white-background" >
       <span class="overflow-ellipsis-three break-word font-weight-semibold font-size-18 line-height-22 lg-text-color-primary">{{name}}</span>
     </div>
     <!-- 资源名称和资源的操作 -->
     <div style="width: 171px"
          :class="[isInMaterial ? 'white-background' : 'bg-color-F5F6F8']"
          class="flex-center-center height-40 content-border">
       <!-- 资源名称 -->
       <span :title="name" class="overflow-ellipsis l-h-1x lg-text-color-primary font-weight-semibold font-size-16">{{name}}</span>
     </div>
     <i v-show="!isShowShadow && !notShowDelete" style="font-size: 8px !important;" class="el-icon-close" @click.stop="deleteDll"/>
     <div v-if="isShowShadow"
          style="background-color: rgba(58, 63, 81, 0.7); z-index: 99; position: absolute; top: 0; left: 0; right: 0;bottom: 0"
          class="display-flex flex-center-center">
       <div class="lg-pointer font-weight-semibold text-white font-size-14" @click="viewAllDllList">{{$t('loc.viewAll')}} <i class="el-icon-arrow-right"></i></div>
     </div>
   </div>
</template>

<script>
export default {
  name: 'keyVocabularyItem',
  data () {
    return {
      imgFit: 'cover'
    }
  },
  props: ['dll','isShowShadow','isInMaterial','notShowDelete','isShowSelect'],
  computed: {
    thumbnailUrl () {
      return this.dll.mediaUrl
    },
    name () {
      return this.dll.name
    },
    dllId () {
      return this.book.id
    }
  },
  methods: {
    changeCheck () {
      this.$emit('select', this.dll)
    },
    deleteDll () {
      this.$analytics.sendEvent('web_curriculum_add_vocabulary_delete')
      this.$emit('delete', this.dll.id)
    },
    viewAllDllList () {
      this.$emit('viewAll','vocabulary')
    },
    clickKeyVocabulary () {
      if (this.isShowSelect) {
        this.dll.isCheck = !this.dll.isCheck
        this.$emit('select', this.dll)
      }
    }
  }
}
</script>

<style scoped>
.img-size{
  height: 97px;
  width: 171px;
}
.img-border {
  /*border-width: 1px 1px 0px 1px;*/
  /*border-style: solid;*/
  /*border-color: #DCDFE6;*/
  border-radius: 4px 4px 0px 0px;
}
.no-img-border {
  border-width: 1px 1px 0px 1px;
  border-style: solid;
  border-color: #DCDFE6;
  border-radius: 4px 4px 0px 0px;
}
.content-border {
  /*border-width: 0px 1px 1px 1px;*/
  /*border-style: solid;*/
  /*border-color: #DCDFE6;*/
  border-radius: 0px 0px 4px 4px;
}
.el-icon-close {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: rgba(50, 51, 56, 0.7);
  border-radius: 50%;
  color: #fff;
  width: 16px;
  height: 16px;
  display: inline-block;
  cursor: pointer;
  line-height: 16px;
  text-align: center;
}
.theme-border-line {
  border: 1px solid #10B3B7;
  border-radius: 4px;
}
.transparent-border-line {
  border: 1px solid transparent;
}
.check-resource-div>>>.el-checkbox__inner{
  border-radius: 50% !important;
}
.position-absolute-right-4 {
  right: 4px;
}
.position-absolute-right-2 {
  right: 2px;
}
.overflow-ellipsis-three {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
