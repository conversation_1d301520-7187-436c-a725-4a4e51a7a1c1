<template>
  <!-- 展示书的搜索已经搜索界面弹框界面 -->
  <div class="book-select">
    <div class="w-full">
      <el-input :placeholder="$t('loc.inputBookTitleTip')" v-model="keyword" size="medium" @keyup.enter.native="search">
        <el-button style="background-color: #10B3B7;color: white" slot="append" @click="search" size="medium" :loading="loading">{{ $t('loc.search') }}</el-button>
      </el-input>
    </div>
    <!--书搜索弹框-->
    <el-dialog :visible.sync="show" width="691px" style="margin-top: -2vh" class="popup-container" :append-to-body="true">
      <div slot="title">{{ $t('loc.searchRes') }}</div>
      <div class="book-list lg-scrollbar-show" v-loading="videoBookListLoading">
        <div v-for="(item,index) in books" :key="index" class="book-item">
          <el-image :src="getBookImage(item)" fit="cover">
            <div slot="error" style="
                background-color: #e7e7e7;
                line-height: 0px;
                padding: 10px 20px;
                width: 110px;
                height: 76px;
                display: flex;
                flex-flow: column;
                align-items: center;
                justify-content: center;">
              <el-image :src="errorImage"></el-image>
              <span style="line-height: 20px;font-size: 12px;color: #999;">{{ $t('loc.lesson2FailedLoadingMedia') }}</span>
            </div>
          </el-image>
          <el-tooltip style="cursor: pointer" :enterable="false" :open-delay="Number(1000)" effect="light" placement="bottom-start" v-if="item.volumeInfo.title && item.volumeInfo.title.length>0">
            <div slot="content">{{ item.volumeInfo.title }}</div>
            <div style="cursor: pointer">{{ item.volumeInfo.title }}</div>
          </el-tooltip>
          <div v-if="!item.volumeInfo.title">{{ item.volumeInfo.title }}</div>
          <el-tooltip :enterable="false" :open-delay="Number(1000)" effect="light" placement="bottom-start"  v-if="item.volumeInfo.description && item.volumeInfo.description.length>0">
            <div slot="content" style="width: 600px">{{ item.volumeInfo.description }}</div>
            <div style="cursor: pointer">{{ item.volumeInfo.description }}</div>
          </el-tooltip>
          <div v-if="!item.volumeInfo.description">{{ item.volumeInfo.description }}</div>
          <el-button type="primary" @click="select(item)">{{ $t('loc.select') }}</el-button>
        </div>
      </div>
      <!--无视频书-->
      <div v-if="isShowNoBook" class="video-empty">
        <el-image :src="noBook" fit="cover"></el-image>
        <div>
          {{ $t('loc.lessons2NoBookOrVideoBook') }}
        </div>
      </div>
      <div slot="footer">
        <el-button plain @click="closeBooks">{{ $t('loc.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from '@/utils/axios'
import configBaseUrl from '@/utils/setBaseUrl'
import _image from '@/views/modules/lesson2/component/assets/img/book.png'
import sensitiveWord from '@/utils/sensitiveWord'
import _errorImage from '@/assets/img/lesson2/image.png'
import _noBook from '@/views/modules/lesson2/component/assets/img/empty.jpg'

export default {
  name: 'SearchBookResultDialog',
  data () {
    return {
      book: null, // 从搜索结果中选择的书
      keyword: null, // 搜索关键词
      books: null, // 搜索结果
      show: false, // 是否展示搜索结果弹窗
      image: _image,
      errorImage: _errorImage,
      noBook: _noBook,
      loading: false,
      isShowNoBook: false, // 是否显式没有书的标识
      isVideoBook: false, // 判断是否是视频书
      videoBookListLoading: false // 加载视频书的loading
    }
  },
  watch: {
    book () {
      if (this.book) {
        this.$emit('selectBook', this.book)
      }
    }
  },
  methods: {
    search () {
      if (!this.keyword || !this.keyword.trim()) {
        this.$message.error(this.$t('loc.inptSerch'))
        return
      }
      if (!sensitiveWord(this.keyword)) {
        this.$message.error(this.$t('loc.sensitiveWordViolationMessage').toString().replace('{{word}}', this.keyword))
        return
      }
      this.loading = true
      axios
        .get(`${$api.urls().getBookVolumes}?q=${this.keyword}`, {
          baseURL: configBaseUrl
        })
        .then(res => {
          this.books = res
          this.show = true
        })
        .catch(error => {
          this.$message.error(error.message)
        })
        .finally(() => {
          this.loading = false
        })
    },
    select (book) {
      this.book = book
      this.closeBooks()
    },
    closeBooks () {
      this.show = false
    },
    getBookImage (book) {
      return book && book.volumeInfo && book.volumeInfo.imageLinks &&
        book.volumeInfo.imageLinks.thumbnail
    },
    reset () {
      this.keyword = ''
      this.book = null
      this.books = []
      this.isVideoBook = false
      this.videoBookListLoading = false
      this.isShowNoBook = false
    },
    // 搜索视频书
    searchVideoBook (originalBook) {
      if (!sensitiveWord(this.videoName)) {
        this.$message.error(this.$t('loc.sensitiveWordViolationMessage').toString().replace('{{word}}', this.keyword))
        return
      }
      this.videoBookListLoading = true
      this.isVideoBook = true
      let params = new URLSearchParams({
        author: originalBook && originalBook.volumeInfo.authors && originalBook.volumeInfo.authors[0],
        type: 'BOOK_VIDEO',
        search: originalBook.volumeInfo.title,
        isbn: originalBook.volumeInfo.industryIdentifiers &&
          originalBook.volumeInfo.industryIdentifiers.map(isbn => isbn.identifier).join(',')
      })
      axios
        .get(`${$api.urls().searchBookVideo}?${params.toString()}`, {
          baseURL: configBaseUrl
        })
        .then(res => {
          this.videos = res
          this.show = true
        })
        .catch(error => {
          this.$message.error(error.message)
        })
        .finally(() => this.searching = false)
    },
  }
}
</script>

<style scoped lang="less">
.book-list {
  width: 651px;
  height: 398px;
  overflow-y: auto;
  border-radius: 4px;
  border: 1px solid #eeeeee;
  display: flex;
  flex-flow: column;
  gap: 9px;
}

.book-item {
  display: grid;
  grid-template-areas: "a b c" "a d c";
  grid-template-columns: 140px 1fr 110px;
  border-bottom: 1px solid #eeeeee;

  & > :first-child {
    height: 78.75px;
    margin: 9px;
    grid-area: a;
    width: 117px;
  }

  & > :nth-child(2) {
    height: 32px;
    font-size: 16px;
    line-height: 32px;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    grid-area: b;
  }

  & > :nth-child(3) {
    height: 42px;
    color: #111c1c;
    line-height: 21px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    grid-area: d;
  }

  & > :nth-child(4) {
    color: #fff;
    grid-area: c;
    align-self: center;
    justify-self: center;
    padding: 10px 15px;
  }

  &:last-child {
    border-bottom: none;
  }
}

.book-select /deep/ .el-dialog__wrapper {
  & > .el-dialog {
    margin-top: 5vh !important;

    & > .el-dialog__body {
      padding: 0 22px;
    }

    & > .el-dialog__header {
      line-height: 21px;
      padding: 15px 20px;
    }

    & > .el-dialog__footer {
      height: 68px;
    }
  }
}

.video-empty {
  text-align: center;
  height: 300px;
  display: flex;
  flex-flow: nowrap column;
  align-items: center;
  justify-items: center;
  justify-content: center;
}
</style>
