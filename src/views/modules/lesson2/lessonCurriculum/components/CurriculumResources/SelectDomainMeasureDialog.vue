<template>
  <div>
    <!-- 选择测评点的弹框   -->
    <el-dialog :title="title" width="800px" @close="clearContent" :visible.sync="SelectDomainDialogVisible"
               :lock-scroll="false" top="3vh" :close-on-click-modal="false"
               custom-class="select-domain-dialog-class">
      <el-input
        :placeholder="$t('loc.search')"
        class="add-margin-tb-10"
        v-model="searchContent">
        <i
          class="el-icon-search el-input__icon lg-pointer"
          slot="suffix">
        </i>
      </el-input>
      <div class="layout-domain">
        <div style="overflow: hidden">
          <el-tree :data="domains"
                  ref="tree"
                  show-checkbox
                  default-expand-all
                  :props="defaultProps"
                  node-key="id"
                  :filter-node-method="filterNode"
                  v-loading="domainsLoading"
                  @check-change="handleCheckChange"
                  :default-checked-keys="selectedMeasureIds"
                  >
          <span slot-scope="{node,data}" :class="{'font-bold':node.data.children.length}">
            <span>{{ data.label }}</span>
            <span v-if="data.core" style="color: red">*</span>
          </span>
          </el-tree>
        </div>
        <!-- 仅展示核心测评点 switch start -->
        <div class="display-flex" v-show="showCoreMeasureOpen">
          <el-switch
              v-model="showCoreMeasure"
              @change="changeCoreMeasureState">
            </el-switch>
            <span class="lesson-switch" style="margin-left: 5px; word-break: break-word;">{{ $t('loc.showCoreMeasureOnly') }}</span>
        </div>
        <!-- 仅展示核心测评点 switch end -->
      </div>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button @click="cancelDialog" plain="" class="cancelBtn">{{ $t('loc.cancel') }}</el-button>
        <el-button :disabled="selectedMeasureIds.length === 0 && allMeasures.length === 0" :loading="submitLoading"  type="primary"  class="saveBtn" @click="saveMeasureResource">{{ $t('loc.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Lessons2 from '@/api/lessons2'
export default {
  name: 'SelectDomainMeasureDialog',
  data () {
    return {
      title: this.$t('loc.selectDomains'),
      SelectDomainDialogVisible: false,
      searchContent: '',
      domains: [],
      selectedMeasureIds: [], // 选中的测评点数组
      originalDomains: [],
      domainsLoading: false,
      submitLoading: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      haveSelect: false,
      showCoreMeasure: true, // 仅展示核心测评点
      cacheDomains: [],
      showCoreMeasureOpen: false, // 是否展示仅展示核心测评点开关
      allMeasures: [], // 所有的测评点
      cacheAllMeasures: [],
      cacheCoreMeasure: [],
      coreMeasures: []
    }
  },
  props: {
    value: {
      type: Array,
      default () {
        return []
      }
    },
    frameworkId: {
      type: String,
      default: ''
    },
    frameworkName: {
      type: String,
      default: ''
    },
    planId: {
      type: String
    },
    curriculumId: {
      type: String
    },
    unitId: {
      type: String
    }
  },
  methods: {
    showSelectDomainMeasureDialog () {
      this.SelectDomainDialogVisible = true
      this.loadMeasures(this.frameworkId)
    },
    // 加载框架的领域
    loadMeasures (frameworkId) {
      this.domainsLoading = true
      Lessons2.getMeasures(frameworkId)
        .then(res => {
          this.showCoreMeasureOpen = res.showCoreMeasureOpen
          this.showCoreMeasure = this.showCoreMeasureOpen
          this.domainsLoading = false
          res.measures = res.measures.map(domain => {
            domain.label = domain.abbreviation + '-' + domain.name
            domain.children = domain.children.map(measure => {
              measure.label = measure.abbreviation + '-' + measure.name
              return measure
            })
            return domain
          })
          // 如果有框架名称，就把框架名称放在列表的第一层
          if (this.frameworkName) {
            let tempDoMains = []
            tempDoMains.push({
              'id': 'ALL',
              'label': this.frameworkName,
              'children': res.measures
            })
            this.domains = tempDoMains
            this.originalDomains = JSON.parse(JSON.stringify(tempDoMains))
            this.cacheDomains = this.domains
            this.changeCoreMeasureState()
            this.allCoreMeasures()
          } else {
            this.domains = res.measures
            this.originalDomains = JSON.parse(JSON.stringify(res.measures))
            this.cacheDomains = this.domains
            this.changeCoreMeasureState()
            this.allCoreMeasures()
          }
          this.$nextTick(() => {
            if (this.$refs.tree) {
              let measureIds = this.$refs.tree.getCheckedKeys()
              this.haveSelect = measureIds && measureIds.length > 0
            }
          })
        })
    },
    // 重置测评点数据
    resetCoreMeasure (selectedIds, cacheAllMeasures) {
      // 如果没有取消选择的所有核心测评点，则删除即可
      if (cacheAllMeasures.length === 0) {
        return this.cancelCoreMeasure(selectedIds)
      } else {
        // 如果选择部分核心测评点，就需要重置数据
        const temp = this.coreMeasures.filter(x => cacheAllMeasures.indexOf(x) === -1)
        return selectedIds.filter(x => {
          return temp.indexOf(x) === -1
        })
      }
    },
    changeCoreMeasureState () {
      let data = JSON.stringify(this.domains)
      const cacheData = JSON.stringify(this.selectedMeasureIds)
      // 仅展示核心测评点
      if (this.showCoreMeasure) {
        this.allMeasures = JSON.parse(cacheData)
        this.cacheAllMeasures = JSON.parse(cacheData)
        this.filterCoreMeasure(data)
      } else {
        this.selectedMeasureIds = this.selectedMeasureIds.filter(item => {
          return item !== 'ALL'
        })
        this.domains = this.cacheDomains
        this.cacheCoreMeasure = JSON.parse(cacheData)
        // 过滤出已取消的核心测评点ID
        let selectedCoreMeasures = this.unique(this.coreMeasures.filter(x => this.cacheAllMeasures.indexOf(x) === -1))
        this.cacheAllMeasures = this.unique(this.cacheAllMeasures.concat(this.allMeasures))
        // 如果缓存的核心测评点为空，则选的测评点全部为非核心测评点 如果缓存的测评点中包含核心测评点，则去除
        if (this.cacheCoreMeasure.length === 0) {
          this.cacheAllMeasures = this.cancelCoreMeasure(this.cacheAllMeasures)
        } else {
          this.cacheAllMeasures = this.unique(this.cacheAllMeasures.concat(this.cacheCoreMeasure))
          this.cacheAllMeasures = this.unique(this.cacheAllMeasures.filter(x => selectedCoreMeasures.indexOf(x) === -1))
        }
        this.selectedMeasureIds = this.cacheAllMeasures
      }
      // 开关值切换后再次进行节点搜索
      this.$nextTick(() => {
        this.$refs.tree.filter(this.searchContent)
      })
    },
    // 去除选中的核心测评点
    cancelCoreMeasure (cacheAllMeasures) {
      let allMeasures = cacheAllMeasures
      return allMeasures.filter(item => {
        return this.coreMeasures.indexOf(item) === -1
      })
    },
    // 获取所有的核心测评点
    allCoreMeasures () {
      let coreMeasures = []
      this.cacheDomains.forEach(item => {
        item.children.forEach(domain => {
          domain.children.forEach(measure => {
            if (measure.core) {
              coreMeasures.push(measure.id)
            }
          })
        })
      })
      this.coreMeasures = coreMeasures
    },
    filterCoreMeasure (data) {
      let domains = JSON.parse(data)
      domains.map(item => {
        item.children.map(domain => {
          domain.children = domain.children.filter(val => {
            return val.core
          })
          return domain.children
        })
      })
      this.filterNotCoreMeasure(domains)
    },
    filterNotCoreMeasure (data) {
      data.map(item => {
        item.children = item.children.filter((val) => {
          return val.children.length !== 0
        })
        return item.children
      })
      this.domains = data
    },
    // 搜索内容
    filterNode (value, data) {
      if (!value) return true
      return data.label.toLowerCase().indexOf(value.toLowerCase().trim()) !== -1
    },
    // 保存选择的领域
    saveMeasureResource () {
      this.submitLoading = true
      let curriculumId = this.curriculumId
      let planId = this.planId
      let unitId = this.unitId
      // 将测评点Id从选中的节点中过滤出来
      // this.cacheAllMeasures = this.cacheAllMeasures.concat(this.allMeasures)
      // this.selectedMeasureIds = this.cacheAllMeasures
      if (this.showCoreMeasure) {
        this.selectedMeasureIds = this.unique(this.selectedMeasureIds.concat(this.allMeasures))
        this.selectedMeasureIds = this.resetCoreMeasure(this.selectedMeasureIds, this.cacheAllMeasures)
      } else {
        this.selectedMeasureIds = this.unique(this.selectedMeasureIds.concat(this.cacheAllMeasures))
      }
      let requestContent = { curriculumId: curriculumId, planId: planId,unitId: unitId,measureIds: this.selectedMeasureIds }
      this.$axios
        .post($api.urls().updateDomainMeasures, requestContent)
        .then(res => {
          this.submitLoading = false
          if (res.success) {
            this.clearContent()
            this.SelectDomainDialogVisible = false
            this.$emit('update')
          }
        })
        .catch(error => {
          this.submitLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    unique (arr) {
      return Array.from(new Set(arr))
    },
    handleCheckChange (data, checked, indeterminate) {
      if (this.$refs.tree) {
        let checkedOnlyOneMeasureIds = []
        let checkedMeasureIds = []
        // 如果选中的节点不止一个，说明选中了核心测评点，需要将核心测评点过滤出来
        if (this.$refs.tree.getCheckedNodes().length > 1) {
          this.$refs.tree.getCheckedNodes().forEach(item => {
            if (item.children.length === 0) {
              checkedOnlyOneMeasureIds.push(item.id)
            }
          })
        } else {
          checkedMeasureIds = this.$refs.tree.getCheckedKeys()
        }
        this.selectedMeasureIds = this.unique(checkedOnlyOneMeasureIds.concat(checkedMeasureIds))
        this.cacheAllMeasures = this.selectedMeasureIds
        if (!this.showCoreMeasure && this.selectedMeasureIds.length === 0) {
          this.allMeasures = []
        }
      }
    },
    clearContent () {
      this.haveSelect = false
      this.submitLoading = false
      this.domainsLoading = false
      this.searchContent = ''
      this.domains = []
      this.originalDomains = []
    },
    cancelDialog () {
      this.clearContent()
      this.SelectDomainDialogVisible = false
    }
  },
  watch: {
    searchContent (val) {
      this.$refs.tree.filter(val)
    }
  },
  created () {
    this.$bus.$on('selectDomainMeasureDialog', haveSelectMeasures => {
      this.selectedMeasureIds = haveSelectMeasures
    })
    this.changeCoreMeasureState(this.showCoreMeasure)
  }
}
</script>

<style scoped>
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.cancelBtn {
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}

.layout-domain {
  display: flex;
  justify-content: space-between;
}
.saveBtn {
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}
</style>
<style>
.select-domain-dialog-class {
  max-height: 94%;
  display: flex;
  margin-bottom: 0 !important;
  flex-direction: column;
}
.select-domain-dialog-class .el-dialog__body {
  padding: 0 20px !important;
  flex: 1;
  overflow-y: auto !important;
}
.select-domain-dialog-class .el-dialog__header .el-dialog__title {
  color: #111c1c;
  font-weight: 600;
  font-size: 20px;
}
.select-domain-dialog-class .el-dialog__body::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 6px; /* 高宽分别对应横竖滚动条的尺寸 */
  height: 1px;
}

.select-domain-dialog-class .el-dialog__body::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 10px;
  /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1); */
  background: #dddee0;
}

.select-domain-dialog-class .el-dialog__body::-webkit-scrollbar-track {
  /* 滚动条里面轨道 */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  /* border-radius: 10px; */
  /* background: #EDEDED; */
}
</style>
