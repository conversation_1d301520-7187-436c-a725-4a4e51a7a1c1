<template>
  <!-- 展示视频书的搜索结果弹框 -->
  <div class="book-searcher">
    <!--搜索弹框-->
    <el-dialog :append-to-body="true" @close="clearData" :visible.sync="show" width="691px" style="margin-top: -3vh;margin-left: -4vh" class="popup-container">
      <div slot="title" style="height: 21px;font-size: 24px">{{ $t('loc.searchRes') }}</div>
      <div v-if="!isShowNoBook" class="book-list scrollbar2" v-loading="videoBookListLoading">
        <div  v-for="(item,index) in videos" :key="index" class="book-item">
          <el-image :src="getVideoImage(item)" fit="cover">
            <div slot="error" style="
                background-color: #e7e7e7;
                line-height: 0px;
                padding: 10px 20px;
                width: 110px;
                height: 76px;
                display: flex;
                flex-flow: column;
                align-items: center;
                justify-content: center;">
              <el-image :src="errorImage"></el-image>
              <span style="line-height: 20px;font-size: 12px;color: #999;">{{ $t('loc.lesson2FailedLoadingMedia') }}</span>
            </div>
          </el-image>
          <div :title="item.snippet.title">{{ item.snippet.title }}</div>
          <div :title="item.snippet.description">{{ item.snippet.description }}</div>
          <el-button type="success" @click="preview(item)">{{ $t('loc.preview') }}</el-button>
          <el-button type="primary" @click="select(item)">{{ $t('loc.select') }}</el-button>
        </div>
      </div>
      <!--无视频书-->
      <div  v-else class="video-empty">
        <el-image :src="noBookVideos" fit="cover"></el-image>
        <div>
          {{ $t('loc.lessons2NoBookOrVideoBook') }}
        </div>
      </div>
      <div slot="footer">
        <el-button @click="closeVideos">{{ $t('loc.close') }}</el-button>
      </div>
    </el-dialog>
    <!-- 视频预览弹框 -->
    <el-dialog :visible.sync="showPlayer" width="691px" :append-to-body="true" style="margin-left: -4vh;margin-top: -3vh" class="popup-container">
      <div slot="title" style="height: 21px;font-size: 24px">{{ $t('loc.preview') }}</div>
      <iframe :src="showPlayer && `https://www.youtube.com/embed/${previewVideo.id.videoId}`" v-if="previewVideo"
              width="647px" height="428px" allowfullscreen/>
      <div slot="footer">
        <el-button @click="closePreview">{{ $t('loc.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import axios from '@/utils/axios'
import configBaseUrl from '@/utils/setBaseUrl'
import _image from '@/views/modules/lesson2/component/assets/img/video.png'
import _noBookVideos from '@/assets/img/lesson2/no_book_videos.png'
import _errorImage from '@/assets/img/lesson2/image.png'

export default {
  name: 'SearchVideoResultDialog',
  data () {
    return {
      videos: null,
      show: false,
      showPlayer: false,
      previewVideo: null,
      video: {},
      image: _image,
      noBookVideos: _noBookVideos,
      errorImage: _errorImage,
      searching: false,
      reset: new Date().getTime(), // key值
      isShowNoBook: false, // 是否显式没有视频书的标识
      videoBookListLoading: false, // 加载视频书的loading
      bookId: ''
    }
  },
  watch: {
    video (value) {
      if (value) {
        this.$emit('selectVideo', value)
      }
    }
  },
  methods: {
    search (book) {
      if (!book) {
        return
      }
      this.bookId = book.id
      this.show = true
      this.videos = []
      let params = new URLSearchParams({
        author: book && book.volumeInfo.authors && book.volumeInfo.authors[0],
        type: 'BOOK_VIDEO',
        search: book && book.volumeInfo.title,
        isbn: book.volumeInfo.industryIdentifiers &&
          book.volumeInfo.industryIdentifiers.map(isbn => isbn.identifier).join(',')
      })
      this.videoBookListLoading = true
      axios
        .get(`${$api.urls().searchBookVideo}?${params.toString()}`, {
          baseURL: configBaseUrl
        })
        .then(res => {
          this.videos = res
          if (this.videos.length === 0) {
            this.isShowNoBook = true
          } else {
            this.isShowNoBook = false
          }
        })
        .catch(error => {
          this.$message.error(error.message)
        })
        .finally(() => this.videoBookListLoading = false)
    },
    select (video) {
      video.tempBookId = this.bookId
      this.video = video
      this.closeVideos()
    },
    getVideoImage (video) {
      return video && video.snippet && video.snippet.thumbnails &&
        video.snippet.thumbnails.default.url
    },
    closeVideos () {
      this.show = false
      this.clearData()
    },
    preview (video) {
      if (window.lessonVideo) {
        window.lessonVideo.pause()
      }
      this.showPlayer = true
      this.previewVideo = video
    },
    closePreview () {
      this.showPlayer = false
    },
    deleteVideoBook () {
      this.video = null
    },
    clearData () {
      this.isShowNoBook = false
      this.videoBookListLoading = false
      this.bookId = ''
    }
  }
}
</script>
<style scoped lang="less">
.book-searcher /deep/ .show-unselected {

  & > .el-input {
    margin-top: 19px;
  }
}

.show-selected {
  width: 100%;
  text-align: center;
  position: relative;

  div {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    line-height: normal;

    &:nth-child(2) {
      margin-top: 5px;
    }
  }
}

.book-list {
  width: 651px;
  height: 398px;
  overflow-y: auto;
  border-radius: 4px;
  border: 1px solid #eeeeee;
}

.book-item {
  display: grid;
  grid-template-areas: "a b c e" "a d c e";
  grid-template-columns: 140px 1fr 90px 90px;
  border-bottom: 1px solid #eeeeee;

  & > :first-child {
    height: 78.75px;
    margin: 9px;
    grid-area: a;
    width: 117px;
  }

  & > :nth-child(2) {
    height: 32px;
    font-size: 16px;
    line-height: 32px;
    margin-top: 9px;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    grid-area: b;
  }

  & > :nth-child(3) {
    height: 42px;
    color: #777777;
    line-height: 21px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    grid-area: d;
  }

  & > :nth-child(4) {
    color: #fff;
    grid-area: c;
    align-self: center;
    justify-self: center;
    padding: 10px 15px;
  }

  & > :nth-child(5) {
    color: #fff;
    grid-area: e;
    align-self: center;
    justify-self: center;
    padding: 10px 15px;
  }

  &:last-child {
    border-bottom: none;
  }
}

.book-searcher /deep/ .el-dialog__wrapper {
  & > .el-dialog {
    margin-top: 5vh !important;

    & > .el-dialog__body {
      padding: 0 22px;
    }

    & > .el-dialog__header {
      line-height: 21px;
      padding: 15px 20px;
    }

    & > .el-dialog__footer {
      height: 68px;
    }
  }
}

.el-icon-close {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #888888;
  border-radius: 50%;
  color: #fff;
  width: 20px;
  height: 20px;
  display: inline-block;
  cursor: pointer;
  line-height: 20px;
}
.video-empty {
  text-align: center;
  height: 300px;
  display: flex;
  flex-flow: nowrap column;
  align-items: center;
  justify-items: center;
  justify-content: center;
}

.video-book-title{
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  line-height: normal;
  margin-top: 5px;
  text-align: center;
}

.book_video_cover{
  width: 240px;
  height: 135px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e7e7e7;
}
</style>
