<template>
 <div>
   <!-- 展示资源的全部内容弹框  -->
   <el-dialog :title="title" width="790px" @close="clearContent" :visible.sync="viewResourceListDialogVisible"
              v-loading="viewResourceListLoading" :lock-scroll="false" top="10vh" :close-on-click-modal="false"
              custom-class="resource-list-dialog-class">
              <!-- {{ resourcesViewArray }} -->
      <!-- 仅展示核心测评点 switch start -->
      <div class="core-measure" style="margin-bottom: 10px; display: flex; align-items: center" v-show="isFromMeasure">
        <el-switch
          v-show="showCoreMeasureOpen"
          v-model="showCoreMeasure"
          @change="changeCoreMeasureState">
        </el-switch>
        <span class="lesson-switch" v-show="showCoreMeasureOpen" style="padding-left:5px">{{ $t('loc.showCoreMeasureOnly') }}</span>
      </div>
      <!-- 仅展示核心测评点 switch end -->
     <div class="flex-row-between add-padding-r-10" style="background-color: #F2F6FE;height: 32px;">
       <div class="h-full position-relative add-padding-l-12 add-padding-r-6 display-flex align-items font-weight-semibold font-size-16 line-height-22 text-white bg-unit-weekly" style="max-width: 90%">
         <div class="overflow-ellipsis">
           {{unitWeekTitle}}
         </div>
       </div>
       <!-- 显示测评点数量信息 -->
       <div v-if="isFromMeasure" class="color-676879">
        {{ measureTotalTitle }}
       </div>
      <div v-else class="color-676879">
        {{$t('loc.total')}} ({{resourceTotal}})
       </div>
     </div>
     <!-- 如果不是展示测评点 -->
     <template v-if="!isFromMeasure">
      <template v-if="resourcesViewArray.length > 0">
        <div v-for="(resourcesView,index) in resourcesViewArray" :key="index">
          <span  class="add-margin-t-16 display-inline-flex align-items justify-content add-padding-lr-12 text-primary" style="height:28px;background-color: rgba(16, 179, 183, 0.1);border: 1px solid #10B3B7;border-radius: 14px;">
          {{resourcesView.categoryName}} ({{resourcesView.resourceList.length}})
          </span>
          <!-- 展示书籍 -->
          <div v-if="isFromBook" class="add-margin-t-16 grid-book-width-auto">
            <book-item v-for="book in resourcesView.resourceList" :key="book.platformId" :book="book" :not-show-delete="!isEdit" @delete="deleteResource"></book-item>
          </div>
          <!-- 展示词汇 -->
          <div v-if="isFromVocabulary" class="add-margin-t-16 grid-vocabulary-width-auto">
            <key-vocabulary-item :not-show-delete="!isEdit" v-for="vocabulary in resourcesView.resourceList" :key="vocabulary.id" :dll="vocabulary" @delete="deleteResource"></key-vocabulary-item>
          </div>
          <!-- 展示打印附件 -->
          <div v-if="isFromFile" class="add-margin-t-16 grid-file">
            <printable-attachment-item :not-show-delete="!isEdit" :not-download="isEdit" v-for="(file,index) in resourcesView.resourceList" :key="index" :file="file" @delete="deleteResource"></printable-attachment-item>
          </div>
          <!-- 展示测评点 -->
          <div v-if="isFromMeasure">
           <el-row :gutter="40">
             <el-col :span="12" v-for="(measure, index) in resourcesView.resourceList" :key="index">
               <span class="resource-measure-title">
               {{ measure.abbreviation }}
               </span>
               <span>
                 {{ measure.name }}
               </span>
             </el-col>
           </el-row>
            <!-- <div v-for="(measure, index) in resourcesView.resourceList" :key="index">
             <span class="resource-measure-title">
               {{ measure.abbreviation }}
             </span>
             <span>
               {{ measure.name }}
             </span>
            </div> -->
          </div>
        </div>
      </template>
      <template v-else>
        <EmptyView :text="$t('loc.nodataTip')"/>
      </template>
     </template>
     <!-- 如果是展示测评点 -->
     <template v-else>
       <div v-for="(resourcesView,index) in resourcesViewArray" :key="index" class="m-t-sm">
          <div v-if="computedDomainMeasureTotal(resourcesViewArray)[1] !== 0">
            <!-- 领域 -->
            <div class="resource-domain-title m-b-sm" v-if="showCoreMeasure && !isAppearDomainName(resourcesView)">
            {{resourcesView.abbreviation}} {{resourcesView.name}}
            </div>
            <div class="resource-domain-title m-b-sm" v-if="!showCoreMeasure">
              {{resourcesView.abbreviation}} {{resourcesView.name}}
            </div>
            <!-- 测评点 -->
            <el-row :gutter="40">
              <el-col :span="12" v-for="(measure, index) in getCoreMeasure(resourcesView.resourceList)" :key="index">
                <span class="resource-measure-title m-t-sm" >
                {{ measure.abbreviation }}
                </span>
                <span>
                  {{ measure.name }}
                </span>
                <!-- 添加核心测评点标识 -->
                <span v-show="measure.core" style="color:red">*</span>
              </el-col>
            </el-row>
          </div>
      </div>
      <div v-if="computedDomainMeasureTotal(resourcesViewArray)[1] === 0">
            <EmptyView :text="$t('loc.noCoreMeasureTip')"/>
          </div>
     </template>
     <!-- 设置dialog的footer -->
     <span slot="footer" class="close-footer">
        <el-button @click="cancelDialog" plain="" class="cancelBtn">{{ $t('loc.close') }}</el-button>
        <el-button v-if="isEdit"  type="primary"  class="saveBtn" @click="saveDate"
                   :loading="submitLoading">{{ $t('loc.confirm') }}</el-button>
      </span>
   </el-dialog>
 </div>
</template>

<script>
import BookItem from './BookItem'
import KeyVocabularyItem from './keyVocabularyItem'
import PrintableAttachmentItem from './PrintableAttachmentItem'
import EmptyView from '@/views/modules/lesson2/lessonCurriculum/components/EmptyView'
export default {
  name: 'ViewResourceListDialog',
  components: { PrintableAttachmentItem, KeyVocabularyItem, BookItem, EmptyView },
  data () {
    return {
      title: 'Books list',
      viewResourceListDialogVisible: false,
      viewResourceListLoading: false,
      submitLoading: false,
      isFromBook: false,
      isFromVocabulary: false,
      isFromFile: false,
      isFromMeasure: false,
      resourcesViewArray: [],
      resourceTotal: 0,
      deleteIds: [],
      showCoreMeasure: false // 仅显示核心测评点开关
    }
  },
  created () {
    this.$bus.$on('showCoreMeasureOpen', showCoreMeasureOpen => {
      this.showCoreMeasure = showCoreMeasureOpen
    })
  },
  methods: {
    showViewResourceListDialog (isFromBook,isFromVocabulary,isFromFile,isFromMeasure,title,resourceArray) {
      this.viewResourceListDialogVisible = true
      this.isFromBook = isFromBook
      this.isFromVocabulary = isFromVocabulary
      this.isFromFile = isFromFile
      this.isFromMeasure = isFromMeasure
      this.title = title
      this.resourcesViewArray = JSON.parse(JSON.stringify(resourceArray))
    },
    deleteResource (deleteId) {
      this.deleteIds.push(deleteId)
      this.resourcesViewArray = this.resourcesViewArray.map(resourceView => {
        let tempResourceList = []
        // 如果展示的是书，过滤platformId
        if (this.isFromBook) {
          tempResourceList = resourceView.resourceList = resourceView.resourceList.filter(resource => {
            return resource.platformId !== deleteId
          })
          return resourceView
        } else {
          tempResourceList = resourceView.resourceList.filter(resource => {
            return resource.id !== deleteId
          })
        }
        resourceView.resourceList = tempResourceList
        return resourceView
      })
    },
    saveDate () {
      // 如果没有要删除的数据，关闭弹窗
      if (this.deleteIds.length == 0) {
        this.cancelDialog()
        return
      }
      // 如果有删除的数据，调用删除接口

      this.submitLoading = true
      let requestUrl = ''
      // 保存词汇数据
      if (this.isFromVocabulary) {
        requestUrl = $api.urls().updateVocabularyResource
      }
      // 保存书籍数据
      if (this.isFromBook) {
        requestUrl = $api.urls().updateBookResource
      }
      // 保存附件数据
      if (this.isFromFile) {
        requestUrl = $api.urls().updateAttachmentResource
      }
      this.$axios
        .post(requestUrl, { 'ids': this.deleteIds })
        .then(res => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success(this.$t('loc.uptSfy'))
            this.clearContent()
            this.viewResourceListDialogVisible = false
            this.$emit('update')
          }
        })
        .catch(error => {
          this.submitLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    clearContent () {
      this.resourcesViewArray = []
      this.deleteIds = []
      this.resourceTotal = 0
    },
    cancelDialog () {
      this.clearContent()
      this.viewResourceListDialogVisible = false
    },
    changeCoreMeasureState (state) {
    },
    getCoreMeasure (resourcesArr) {
      if (this.showCoreMeasure) {
        return resourcesArr.filter(x => x.core)
      } else {
        return resourcesArr
      }
    },
    computedDomainMeasureTotal (resourcesViewArray) {
      let measureNum = 0
      let domainNum = 0
      let cacheNum = 0
      resourcesViewArray.forEach(resource => {
        if (this.showCoreMeasure) {
          const size = resource.resourceList.filter(item => item.core).length
          cacheNum = measureNum
          measureNum += size
          if (cacheNum !== measureNum) {
            domainNum += 1
          }
        } else {
          measureNum += resource.resourceList.length
          domainNum += 1
        }
      })
      return [domainNum,measureNum]
    },
    isAppearDomainName (resourcesView) {
      return resourcesView.resourceList.filter(x => x.core).length === 0
    }
  },
  props: ['unitWeekTitle', 'isEdit', 'showCoreMeasureOpen'],
  watch: {
    resourcesViewArray (value) {
      this.resourceTotal = 0
      value.forEach(resource => {
        this.resourceTotal += resource.resourceList.length
      })
    }
  },
  computed: {
    // 计算测评点的总数title
    measureTotalTitle () {
      if (this.isFromMeasure) {
        return 'Domain:' + this.computedDomainMeasureTotal(this.resourcesViewArray)[0] + ' Measure:' + this.computedDomainMeasureTotal(this.resourcesViewArray)[1]
      }
    }
  }
}
</script>

<style scoped>
.overflow-ellipsis {
  font-family: Inter;
  font-size: 14px;
}
.grid-book-width-auto {
  display: grid;
  grid-template-columns: repeat(auto-fill, 128px);
  gap: 16px;
}
.grid-vocabulary-width-auto {
  display: grid;
  grid-template-columns: repeat(auto-fill, 171px);
  gap: 16px;
}
.grid-file {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.cancelBtn {
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}

.saveBtn {
  color: #FFFFFF;
  font-size: 16px;
  padding: 10px 18px !important;
}
.bg-unit-weekly {
  background-color: #85ABF0;
}
.bg-unit-weekly:after {
  content: '';
  display: block;
  position: absolute;
  right: -16px;
  width: 0;
  height: 0;
  border-bottom: 32px solid #85ABF0;
  border-right: 16px solid transparent;
}
</style>
<style>
.resource-list-dialog-class {
  margin-bottom: 0 !important;
}

.resource-list-dialog-class .el-dialog__footer {
  border-top: none;
  padding-top: 10px;
  padding-bottom: 10px;
}

.resource-list-dialog-class .el-dialog__header {
  padding: 17px 20px 12px;
  border-bottom: none;
  font-size: 20px;
}

.resource-list-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}
.resource-list-dialog-class .el-dialog__header .el-dialog__title {
  color: #323338;
  font-weight: 600;
}

.resource-list-dialog-class .el-dialog__body {
  padding: 10px 24px !important;
  max-height: 450px;
  overflow-y: auto;
}
.resource-list-dialog-class ::-webkit-scrollbar {
  width: 5px;
}

/*定义滚动条轨道 内阴影+圆角*/
.resource-list-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.resource-list-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}
.resource-domain-title {
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
}
.resource-measure-title {
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
}
</style>
