<template>
  <div>
    <!-- 添加资源的弹框 -->
    <el-dialog :title="$t('loc.addBook')" width="700px" @close="clearContent" :visible.sync="addBookResourceDialogVisible"
               :lock-scroll="false" top="10vh" :close-on-click-modal="false"
               custom-class="add-book-resource-dialog-class" >
      <add-resource-head-info ref="addResourceHeadInfoRef" :select-resource-num="books.length" :unit-week-title="unitWeekTitle" @select="selectItem"></add-resource-head-info>
      <div class="add-margin-tb-10 line-height-24 font-size-16 font-weight-semibold">{{$t('loc.searchStuBtn')}}</div>
      <search-book-result-dialog ref="searchBookResultRef" @selectBook="selectBook"></search-book-result-dialog>
      <div v-if="books.length > 0" class="add-margin-tb-10 line-height-24 font-size-16 font-weight-semibold">{{$t('loc.preview')}}</div>
      <!-- 展示选择的书籍 -->
      <div class="grid-book-width-auto">
        <book-item :is-add="true" v-for="book in books" :key="book.id || book.id.videoId" :book="book" @delete="deleteResource" @searchVideo="searchVideoBook"></book-item>
      </div>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button plain @click="cancelDialog" class="cancelBtn">{{ $t('loc.cancel') }}</el-button>
        <el-button  type="primary"  class="saveBtn" @click="saveBookResource" :disabled="books.length === 0"
                    :loading="submitLoading">{{ $t('loc.save') }}</el-button>
      </span>
      <search-video-result-dialog ref="searchVideoResultRef" @selectVideo="selectVideo"></search-video-result-dialog>
    </el-dialog>
  </div>
</template>

<script>
import AddResourceHeadInfo from './AddResourceHeadInfo'
import BookItem from './BookItem'
import SearchBookResultDialog from './SearchBookResultDialog'
import SearchVideoResultDialog from './SearchVideoResultDialog'
export default {
  name: 'AddBookResourceDialog',
  components: { SearchVideoResultDialog, SearchBookResultDialog, BookItem, AddResourceHeadInfo },
  data () {
    return {
      books: [],
      addBookResourceDialogVisible: false,
      submitLoading: false,
      selectItemIds: []
    }
  },
  methods: {
    showAddBookResourceDialog () {
      this.addBookResourceDialogVisible = true
      this.$nextTick(() => {
        this.$refs.addResourceHeadInfoRef.getItemInfo(this.planId)
      })
    },
    // 获取选择书的逻辑，并去重
    selectBook (book) {
      let tempBookIds = []
      this.books.forEach(tempBook => {
        tempBookIds.push(tempBook.id)
      })
      if (tempBookIds.indexOf(book.id) === -1) {
        this.$set(book,'showAddVideo',true)
        this.books.push(book)
      }
    },
    // 选择视频书，并去重
    selectVideo (video) {
      let tempBookIds = []
      this.books.forEach(tempBook => {
        tempBookIds.push(tempBook.id || tempBook.id.videoId)
      })
      let spliceIndex = -1
      if (tempBookIds.indexOf(video.id.videoId) === -1) {
        // 寻找和该视频书关联的书，并把该书的添加视频书按钮去掉
        spliceIndex = this.books.findIndex(book => book.id === video.tempBookId)
        this.books = this.books.map(book => {
          if (video.tempBookId === book.id) {
            book.showAddVideo = false
          }
          return book
        })
        // 把添加的视频书添加到指定的书后面
        if (spliceIndex > -1) {
          if (spliceIndex + 1 === this.books.length) {
            this.books.push(video)
          } else {
            this.books.splice(spliceIndex + 1,0,video)
          }
        }
      }
    },
    // 保存书籍资源
    saveBookResource () {
      // 点击添加书保存按钮埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_book_add')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_book_add')
      }
      if (!this.selectItemIds || this.selectItemIds.length === 0) {
        this.$message.error(this.$t('loc.selecterror'))
        return
      }
      this.submitLoading = true
      let curriculumId = this.curriculumId
      let planId = this.planId
      let unitId = this.unitId
      let planItemIds = this.selectItemIds
      let requestBooks = []
      let requestVideoBooks = []
      this.books.forEach(book => {
        let tempRequestBookArray = []
        // 去除视频的tempBookId和showAddVideo字段
        if (book.tempBookId) {
          book.tempBookId = undefined
        } else {
          book.showAddVideo = undefined
        }
        tempRequestBookArray.push(book)
        if (book.id && book.id.videoId) {
          requestVideoBooks.push(tempRequestBookArray)
        } else {
          requestBooks.push(tempRequestBookArray)
        }
      })
      let requestContent = { curriculumId: curriculumId, planId: planId,unitId: unitId,planItemIds: planItemIds,books: requestBooks,videoBooks: requestVideoBooks,planItemId: '1' }
      this.$axios
        .post($api.urls().addBookResource, requestContent)
        .then(res => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success(this.$t('loc.DLLAddSuccess'))
            this.clearContent()
            this.$emit('success')
            this.addBookResourceDialogVisible = false
          }
        })
        .catch(error => {
          this.submitLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    deleteResource (deleteId) {
      // 根据 id 查找删除的是视频书还是书
      // 先查找视频书
      let deleteBook = this.books.find(book => book.id && book.id.videoId === deleteId)
      // 视频书未找到则查找书
      if (!deleteBook) {
        deleteBook = this.books.find(book => book.id === deleteId)
      }
      if (!deleteBook.volumeInfo && deleteBook.tempBookId) {
        // 说明删除的是视频书,把对应的书的添加视频书按钮显示出来
        this.books = this.books.map(book => {
          if (book.id === deleteBook.tempBookId) {
            book.showAddVideo = true
          }
          return book
        })
      }
      this.books = this.books.filter(book => {
        let bookId = book.volumeInfo ? book.id : book.id.videoId
        return bookId !== deleteId
      })
    },
    clearContent () {
      this.$refs.addResourceHeadInfoRef.dataInitialize()
      this.books = []
      this.$refs.searchBookResultRef.reset()
      this.selectItemIds = []
      // this.books = []
      // this.$refs.searchBookResultRef.reset()
    },
    // 获取选中的活动
    selectItem (itemId) {
      this.selectItemIds = itemId
    },
    cancelDialog () {
      this.addBookResourceDialogVisible = false
      this.clearContent()
    },
    // 搜索视频书
    searchVideoBook (book) {
      this.$refs.searchVideoResultRef.search(book)
    }
  },
  props: ['planId','curriculumId','unitId','unitWeekTitle']
}
</script>

<style scoped>
.grid-book-width-auto {
  display: grid;
  grid-template-columns: repeat(auto-fill, 128px);
  grid-column-gap: 24px;
  grid-row-gap: 16px;
}
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.cancelBtn {
  color: #606266;
  font-size: 16px;
  padding: 10px 18px !important;
}

.saveBtn {
  color: #FFFFFF;
  font-size: 16px;
  padding: 10px 18px !important;
}
</style>
<style>
.add-book-resource-dialog-class {
  margin-bottom: 0 !important;
}

.add-book-resource-dialog-class .el-dialog__footer {
  border-top: none;
  padding-top: 10px;
  padding-bottom: 24px;
}

.add-book-resource-dialog-class .el-dialog__header {
  padding: 17px 20px 12px;
  border-bottom: none;
  font-size: 20px;
}

.add-book-resource-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}
.add-book-resource-dialog-class .el-dialog__header .el-dialog__title {
  color: #111c1c;
  font-weight: 600;
  font-size: 20px;
}

.add-book-resource-dialog-class .el-dialog__body {
  padding: 10px 24px !important;
  max-height: 450px;
  overflow-y: auto;
}
.add-book-resource-dialog-class ::-webkit-scrollbar {
  width: 5px;
}

/*定义滚动条轨道 内阴影+圆角*/
.add-book-resource-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.add-book-resource-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}

</style>
