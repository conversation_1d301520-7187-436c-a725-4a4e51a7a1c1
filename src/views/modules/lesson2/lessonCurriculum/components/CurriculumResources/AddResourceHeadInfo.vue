<template>
<!-- 添加资源时选择活动的组件 -->
 <div class="display-flex flex-row-between align-items add-margin-t-10">
   <div class="display-flex align-items">
     <span>{{$t('loc.addTo')}}</span>
     <span class="add-margin-lr-6 border-radius-4 border-1-grey add-padding-tb-8 add-padding-lr-12" style="border: 2px solid #DCDFE6; cursor: no-drop;">
       {{unitWeekTitle}}
     </span>
     <el-select v-model="selectedActivityId"
                :placeholder="$t('loc.addResourceSelectActivityTip')"
                popper-class="popper-res-header-class"
                :loading="loading"
                :loading-text="$t('loc.loading')"
                @change="selectActivityChange"
                class="border-bold">
       <el-option
         v-for="item in activities"
         :title="item.name"
         :key="item.id"
         :label="item.name"
         :value="item.id">
       </el-option>
     </el-select>
   </div>
   <span v-if="selectResourceNum > 0">{{$t('loc.selectResourceNum',{num:selectResourceNum})}}</span>
 </div>
</template>

<script>
import tools from '../../../../../../utils/tools'

export default {
  name: 'AddResourceHeadInfo',
  props: {
    selectResourceNum: {
      type: Number,
      default: 0
    },
    unitWeekTitle: {
      type: String
    }
  },
  data () {
    return {
      selectedActivityId: '',
      activities: [],
      loading: false,
      originActivities: []
    }
  },
  methods: {
    // 获取活动列表
    getItemInfo (planId) {
      if (planId && planId.trim().length > 0) {
        this.loading = true
        this.$axios
          .get($api.urls().getItemInfo + '?planId=' + planId)
          .then(res => {
            this.loading = false
            if (res.itemEntities && res.itemEntities.length > 0) {
              let tempActivities = []
              let activityNames = []
              // 去除name为空的活动
              res.itemEntities = res.itemEntities.filter(itemEntity => {
                return itemEntity.name
              })
              // 添加一个变量用于记录同一天的相同活动名的标识
               res.itemEntities = res.itemEntities.map(itemEntity => {
                 if (itemEntity.centerId) {
                   itemEntity.tempCategoty = itemEntity.centerId + '-' + itemEntity.name
                 } else if (itemEntity.categoryId) {
                   itemEntity.tempCategoty = itemEntity.categoryId + '-' + itemEntity.name + '-' + itemEntity.dayOfWeek
                 } else {
                   itemEntity.tempCategoty = ''
                 }
                return itemEntity
              })
              this.originActivities = JSON.parse(JSON.stringify(res.itemEntities))
              res.itemEntities.forEach(itemEntity => {
                  activityNames.push(itemEntity.name.trim())
              })
              // 判断活动名相同的逻辑
              // 1.如果是同一天的，做去重的操作
              // 2. 如果是center的，拼接tag名
              // 3. 如果是同一分类的，拼接星期
              // 4. 如果是非center非同类的，拼接分类名
              let hashTemp = {}
              res.itemEntities = res.itemEntities.reduce(function (init,item) {
                if (!hashTemp[item.tempCategoty]) {
                  hashTemp[item.tempCategoty] = true
                  init.push(item)
                }
                return init
              },[])
              res.itemEntities.forEach(itemEntity => {
                if (itemEntity.name && itemEntity.name.trim().length > 0) {
                  let tempActivity = {
                    'id': itemEntity.id,
                    'name': itemEntity.name,
                    'originName': itemEntity.name,
                    'categoryId': itemEntity.categoryId,
                    'dayOfWeek': itemEntity.dayOfWeek,
                    'centerId': itemEntity.centerId
                  }
                  if (activityNames.filter(name => { return name === itemEntity.name.trim() }).length > 1) {
                    // 表示是center下面的，就拼接centerName
                    if (itemEntity.centerId) {
                      tempActivity.name = itemEntity.centerName ? itemEntity.name + ' (' + itemEntity.centerName + ')' : itemEntity.name
                    } else {
                      // 判断是否在同一个分类
                      let haveEqualCategory = false
                      for (let i = 0; i < res.itemEntities.length; i++) {
                        let innerItemEntity = res.itemEntities[i]
                        if (innerItemEntity.id === itemEntity.id) {
                          continue
                        } else {
                          if (innerItemEntity.categoryId === itemEntity.categoryId && innerItemEntity.name === itemEntity.name) {
                            haveEqualCategory = true
                            break
                          }
                        }
                      }
                      // 判断是否在同一天
                      let haveEqualDayOfWeek = false
                      for (let i = 0; i < res.itemEntities.length; i++) {
                        let innerItemEntity = res.itemEntities[i]
                        if (innerItemEntity.id === itemEntity.id) {
                          continue
                        } else {
                          if (innerItemEntity.categoryId !== itemEntity.categoryId && innerItemEntity.name === itemEntity.name && innerItemEntity.dayOfWeek === itemEntity.dayOfWeek) {
                            haveEqualDayOfWeek = true
                            break
                          }
                        }
                      }
                      if (haveEqualCategory && haveEqualDayOfWeek) {
                        if (itemEntity.categoryName) {
                          tempActivity.name = itemEntity.name + ' (' + itemEntity.categoryName + ' ' + tools.calWeekDay(itemEntity.dayOfWeek) + ')'
                        } else {
                          tempActivity.name = itemEntity.name + ' (' + tools.calWeekDay(itemEntity.dayOfWeek) + ')'
                        }
                      } else if (haveEqualCategory) {
                        tempActivity.name = itemEntity.name + ' (' + tools.calWeekDay(itemEntity.dayOfWeek) + ')'
                      } else {
                        tempActivity.name = itemEntity.categoryName ? itemEntity.name + ' (' + itemEntity.categoryName + ')' : itemEntity.name
                      }
                    }
                  }
                  tempActivities.push(tempActivity)
                }
              })
              this.activities = tempActivities
            }
          })
          .catch(error => {
            this.loading = false
          })
      }
    },
    // 选择活动
    selectActivityChange () {
      let selectedItemObject = this.activities.find(activity => activity.id === this.selectedActivityId)
      let itemIds = []
      // 判断是否有去重的，并把去重的id都返回
      this.originActivities.forEach(activity => {
        if (activity.centerId) {
          if (activity.name === selectedItemObject.originName && activity.centerId === selectedItemObject.centerId) {
            itemIds.push(activity.id)
          }
        } else {
          if (activity.name === selectedItemObject.originName && activity.categoryId === selectedItemObject.categoryId && activity.dayOfWeek === selectedItemObject.dayOfWeek) {
            itemIds.push(activity.id)
          }
        }
      })
      this.$emit('select',itemIds)
    },
    dataInitialize () {
      this.selectedActivityId = ''
      this.activities = []
      this.originActivities = []
    }
  }
}
</script>

<style>
.popper-res-header-class {
  max-width: 180px;
}
</style>
