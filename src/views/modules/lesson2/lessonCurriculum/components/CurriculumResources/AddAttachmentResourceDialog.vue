<template>
  <div>
    <!-- 添加附件弹框界面 -->
    <el-dialog :title="$t('loc.attachFile1')" width="700px" @close="clearContent" :visible.sync="addAttachmentResourceDialogVisible"
               :lock-scroll="false" top="10vh" :close-on-click-modal="false"
               custom-class="add-attachment-resource-dialog-class" >
      <add-resource-head-info ref="addResourceHeadInfoFromFileRef" @select="selectItem" :select-resource-num="files.length" :unit-week-title="unitWeekTitle"></add-resource-head-info>
      <div class="add-margin-tb-10 line-height-24 font-size-16 font-weight-semibold">{{$t('loc.upload')}}</div>
      <!-- 上传附件的控件 -->
      <attachment-uploader ref="attachmentUploaderRef" :is-resource="true" @input="getUploadFile" :limit="1000"></attachment-uploader>
      <div v-if="files.length > 0" class="add-margin-tb-10 line-height-24 font-size-16 font-weight-semibold">{{$t('loc.preview')}}</div>
      <!-- 展示上传的附件 -->
      <div class="grid-file">
        <printable-attachment-item v-for="(file,index) in files"
                                   :key="index" @delete="deleteFile"
                                   :not-download="true"
                                   :file="file">
        </printable-attachment-item>
      </div>
      <!-- 设置dialog的footer -->
      <span slot="footer" class="close-footer">
        <el-button plain @click="cancelDialog" plain="" class="cancelBtn">{{ $t('loc.cancel') }}</el-button>
        <el-button :disabled="files.length === 0" type="primary"  class="saveBtn" :loading="submitLoading" @click="saveAttachmentResource">{{ $t('loc.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import AddResourceHeadInfo from './AddResourceHeadInfo'
import PrintableAttachmentItem from './PrintableAttachmentItem'
import AttachmentUploader from '../../../component/attachmentUploader'
export default {
  name: 'AddAttachmentResourceDialog',
  components: { AttachmentUploader, PrintableAttachmentItem, AddResourceHeadInfo },
  data () {
    return {
      addAttachmentResourceDialogVisible: false,
      submitLoading: false,
      files: [],
      selectItemIds: []
    }
  },
  methods: {
    showAddAttachmentResourceDialog () {
      this.addAttachmentResourceDialogVisible = true
      this.$nextTick(() => {
        this.$refs.addResourceHeadInfoFromFileRef.getItemInfo(this.planId)
      })
    },
    getUploadFile (value) {
      this.files = value
    },
    deleteFile (fileId) {
      let tempFile = this.files.find(file => file.id === fileId)
      this.$refs.attachmentUploaderRef.handleFileDeleteClick(tempFile)
    },
    clearContent () {
      this.$refs.attachmentUploaderRef.reset()
      this.$refs.addResourceHeadInfoFromFileRef.dataInitialize()
      this.selectItemIds = []
    },
    // 保存附件资源
    saveAttachmentResource () {
      // 点击添加附件按钮埋点
      if (this.$route.params.add) {
        this.$analytics.sendEvent('web_curriculum_add_attachment_add')
      } else {
        this.$analytics.sendEvent('web_curriculum_edit_attachment_add')
      }
      if (!this.selectItemIds || this.selectItemIds.length === 0) {
        this.$message.error(this.$t('loc.selecterror'))
        return
      }
      this.submitLoading = true
      let curriculumId = this.curriculumId
      let planId = this.planId
      let unitId = this.unitId
      let planItemIds = this.selectItemIds
      let mediaIds = []
      this.files.forEach(file => {
       mediaIds.push(file.id)
      })
      let requestContent = { curriculumId: curriculumId, planId: planId,unitId: unitId,planItemIds: planItemIds,mediaIds: mediaIds }
      this.$axios
        .post($api.urls().addAttachmentResource, requestContent)
        .then(res => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success(this.$t('loc.DLLAddSuccess'))
            this.clearContent()
            this.addAttachmentResourceDialogVisible = false
            this.$emit('success')
          }
        })
        .catch(error => {
          this.submitLoading = false
          this.$message.error(error.response.data.error_message)
        })
    },
    // 获取选中的活动
    selectItem (itemId) {
      this.selectItemIds = itemId
    },
    cancelDialog () {
      this.addAttachmentResourceDialogVisible = false
      this.clearContent()
    }
  },
  props: ['planId','curriculumId','unitId','unitWeekTitle']
}
</script>

<style scoped>
.grid-file {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 24px;
  grid-row-gap: 12px;
}
.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.cancelBtn {
  color: #606266;
  font-size: 14px;
  padding: 10px 18px !important;
}

.saveBtn {
  color: #FFFFFF;
  font-size: 14px;
  padding: 10px 18px !important;
}
</style>
<style>
.add-attachment-resource-dialog-class {
  margin-bottom: 0 !important;
}

.add-attachment-resource-dialog-class .el-dialog__footer {
  border-top: none;
  padding-top: 10px;
  padding-bottom: 24px;
}

.add-attachment-resource-dialog-class .el-dialog__header {
  padding: 17px 20px 12px;
  border-bottom: none;
  font-size: 20px;
}

.add-attachment-resource-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}
.add-attachment-resource-dialog-class .el-dialog__header .el-dialog__title {
  color: #111c1c;
  font-weight: 600;
}

.add-attachment-resource-dialog-class .el-dialog__body {
  padding: 10px 24px !important;
  max-height: 450px;
  overflow-y: auto;
}
.add-attachment-resource-dialog-class ::-webkit-scrollbar {
  width: 5px;
}

/*定义滚动条轨道 内阴影+圆角*/
.add-attachment-resource-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.add-attachment-resource-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}

</style>
