<template>
  <div>
    <el-link :underline="false" type="info" @click="openDialog">
      <i class="el-icon-delete"></i>
      <span>{{ $t('loc.curriculum2') }}</span>
    </el-link>
    <el-dialog :visible="dialogVisable" :before-close="closeDialog">
      <div v-if="curriculums.length > 0" class="scrollbar recycle-height">
        <el-row class="curriculum-item" v-for="item in curriculums" :key="item.id">
          <el-col :span="4">
            <curriculum-media-viewer url="https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg"></curriculum-media-viewer>
          </el-col>
          <el-col :span="12">
            <div class="m-l-xs">
              <div class="curriculum-name">{{ item.name }}</div>
              <div class="display-flex align-items">
              <!-- 头像 -->
                <div>
                  <el-avatar size="small" :src="item.authorAvatar"></el-avatar>
                </div>
                <!-- 姓名 -->
                <div class="curriculum-author">{{ item.authorName }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div>
              <el-tag size="small" type="info">Toddler (1-3)</el-tag>
            </div>
          </el-col>
          <el-col :span="4">
            <el-tooltip class="item" effect="dark" :content="$t('loc.lessons2PermanentlyDeleteButtonTips')"
                        placement="top">
              <el-button type="text" icon="el-icon-delete"
                          @click.stop="deleteCurriculum(item.id)" class="button-delete"/>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="$t('loc.lessons2RestoreTips')" placement="top">
              <el-button type="text" @click.stop="restoreCurriculum(item.id)" class="button-restore">
                <img src="@/assets/img/lesson2/Restore.png" style="width: 16px;height: 14px" alt=""/>
              </el-button>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <empty-view>  </empty-view>
      </div>
      <div slot="footer">
        <el-button @click="closeDialog">{{ $t('loc.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CurriculumMediaViewer from './CurriculumMediaViewer.vue'
import EmptyView from './EmptyView.vue'
export default {
  name: 'CurriculumRecycle',
  components: { CurriculumMediaViewer, EmptyView },
  data () {
    return {
      dialogVisable: false,
      curriculums: []
    }
  },
  created () {
    this.getRemovedCurriculums()
  },
  methods: {
    openDialog () {
      this.dialogVisable = true
    },
    closeDialog () {
      this.dialogVisable = false
    },
    getRemovedCurriculums () {
      this.curriculums = []
      for (let i = 0; i < 10; i++) {
        this.curriculums.push({
          'id': i,
          'name': 'Curriculum A ' + i,
          'authorName': 'Emily Smith' + i,
          'authorAvatar': 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        })
      }
    },
    deleteCurriculum (id) {
      this.curriculums = this.curriculums.filter(x => x.id != id)
      this.$message.success('delete ' + id)
    },
    restoreCurriculum (id) {
      this.curriculums = this.curriculums.filter(x => x.id != id)
      this.$message.success('restore ' + id)
    }
  }
}
</script>

<style lang="less" scoped>

.recycle-height {
  max-height: 400px;
  overflow: auto;
}
.plan-action {
  position: absolute;
  visibility: hidden;
  height: 100%;
  width: 100%;
  background: rgba(196, 197, 203, 0.8);
}
.plan-card {
  position:relative
}

.plan-card:hover {
  cursor: pointer;
  .plan-action {
    visibility: visible ;
  }
}

.curriculum-item {
  display: flex;
  align-items: center;
  padding: 10px 10px 10px 0;
  border-top: 1px solid #CCC;
}
.curriculum-item:last-child {
  border-bottom: 1px solid #CCC;
}
.curriculum-name {
  font-size: 16px;
  font-weight: 500;
}
/deep/ .el-dialog__body {
  padding: 30px 30px 0 30px;  
}
.button-delete {
  color: red;
}

</style>
