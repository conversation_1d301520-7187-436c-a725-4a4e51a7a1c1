<template>
    <div>
        <el-button class="header-button"
                   style="height: 36px;line-height: 30px"
                   :loading="editLessonPlanLoading"
                   plain @click="editLessonPlan()">
            <!-- 编辑分组按钮 -->
            <div class="cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                     fill="none">
                    <path d="M1.5 18.5H18.5" stroke="#111C1C" stroke-width="1.5" stroke-linecap="round"
                          stroke-linejoin="round"/>
                    <path d="M3.38867 11.2844V14.7222H6.84402L16.6109 4.95105L13.1614 1.5L3.38867 11.2844Z"
                          stroke="#111C1C" stroke-width="1.5" stroke-linejoin="round"/>
                </svg>
                <span class="font-size-14 font-weight-400">{{$t('loc.batchAdaptLesson4')}}</span>
            </div>
        </el-button>

        <!-- 展示 LessonEditor，当点击了生成内容之后，Editor 显示   -->
        <LessonEditor
            :planId="planId"
            :preview="preview"
            :planType="planType"
            :lessonInfo="planLessons"
            ref="lessonEditor"></LessonEditor>
    </div>
</template>

<script>
import LessonEditor from '@/views/modules/lesson2/lessonBatchAdapter/LessonEditor.vue'

export default {
    name: 'BatchEditPlan',
    components: { LessonEditor },
    props: {
        planId: {
            type: String,
            default: ''
        },
        hasLessons: {
            type: Function,
            default: () => {
                return () => {
                }
            }
        },
        // 修改之前的方法
        beforeEdit: {
            type: Function,
            default: () => {
                return () => {
                }
            }
        },
        planType: {
          type: String,
          default: ''
        },
        preview: {
          type: Boolean,
          default: false
        }
    },
    data () {
        return {
            planLessons: [], // 当前这个 plan 下面的所有的课程
            editLessonPlanLoading: false // 编辑课程的 loading
        }
    },
    methods: {
        // 点击批量编辑课程的按钮
        editLessonPlan () {
            // 点击编辑课程按钮弹窗埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_click_edit_lesson')
            // 先判断是否存在可以改编的课程
            if (!this.hasLessons()) {
                this.$message.warning(this.$t('loc.batchAdaptLesson14'))
                return
            }
            if (this.planId && this.planId !== '') {
                this.editLessonPlanLoading = true
                // 如果 planId 存在，那么就获取当前 plan 下面的所有的课程 ID
                this.getAllPlanLesson().then(() => {
                    // 请求结束，关闭 loading
                    this.editLessonPlanLoading = false
                    // 批量修改课程弹窗曝光埋点
                    this.$analytics.sendEvent('web_weekly_plan_edit_lesson_edit_pop')
                    this.$refs.lessonEditor && this.$refs.lessonEditor.initLessons(this.planLessons)
                    // 设置单个编辑课程的状态是因为，编辑的时候执行的逻辑和单个编辑的逻辑是一样的
                    this.$refs.lessonEditor.singleEditLesson = true
                    // 设置批量编辑课程的状态是为了在 LessonEditor 组件中不再请求获取周计划课程列表的接口
                    this.$refs.lessonEditor.batchEdit = true
                    // 初始化操作课程的 Id 为第一个课程的 Id
                    this.$refs.lessonEditor.currentItemId = this.planLessons && this.planLessons.length > 0 && this.planLessons[0].itemId
                })
            }
        },
        // 获取当前 plan 下面的所有的课程的信息
        async getAllPlanLesson () {
            // 在 adapter 之前先调用保存
            if (this.beforeEdit) {
                await this.beforeEdit()
            }
            // 定义参数
            const params = {
                planId: this.planId,
                includeCenterLesson: true
            }
            return new Promise((resolve, reject) => {
                // 发送请求
                this.$axios.get($api.urls().getPlanLessons, { params: params }).then(res => {
                    // 如果 res 存在，并且 res.lessons 是存在的
                    if (res && res.lessons) {
                        // 设置 planLessons
                        this.$set(this, 'planLessons', res.lessons.filter(item => item.lessonId))
                    }
                    resolve()
                }).catch(error => {
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },
    }
}
</script>

<style lang="less" scoped>
.header-button {
    display: flex;
    height: 40px;
    padding: 8px 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    width: fit-content;
}

.cursor-pointer {
    cursor: pointer;
    display: flex;
    gap: 8px;
    height: 24px;
    padding: 3.5px;
    justify-content: center;
    align-items: center;
}
</style>