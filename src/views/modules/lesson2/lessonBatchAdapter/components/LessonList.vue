<template>
    <div>
        <!-- 编辑课程弹窗 -->
        <el-dialog :visible.sync="dialogVisible"
                   :show-close=false
                   :append-to-body="true"
                   :modal-append-to-body="true"
                   :close-on-click-modal="false"
                   width="680px">
            <!--弹窗的头部信息-->
            <div slot="title" class="dialog-title">
                <div class="dialog-title-content display-flex flex-justify-start align-items gap-8">
                    <span>{{$t('loc.batchAdaptLessonPlan')}}</span>
                    <el-tooltip effect="dark"
                                popper-class="max-width-300 font-size-14"
                                :content="$t('loc.batchAdaptLesson13')"
                                placement="bottom">
                        <div>
                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7.54917 6.76791L7.54811 6.77835L7.54758 6.78883L7.54216 6.89534L7.54163 6.90591V6.9165C7.54163 7.03156 7.44834 7.12484 7.33329 7.12484C7.21824 7.12484 7.12496 7.03156 7.12496 6.9165C7.12496 5.88097 7.96441 5.0415 8.99996 5.0415C10.0355 5.0415 10.875 5.88097 10.875 6.9165C10.875 7.29372 10.82 7.55883 10.7109 7.79533C10.5989 8.03783 10.4169 8.27908 10.1168 8.59173L9.97925 8.73179C9.72085 8.99025 9.51872 9.21203 9.38933 9.45102C9.24843 9.71127 9.20829 9.96233 9.20829 10.2498C9.20829 10.3649 9.11501 10.4582 8.99996 10.4582C8.88491 10.4582 8.79163 10.3649 8.79163 10.2498C8.79163 9.87262 8.84657 9.60751 8.95573 9.37102C9.06766 9.12851 9.24968 8.88726 9.5498 8.57462L9.68736 8.43452C9.94574 8.17607 10.1479 7.9543 10.2773 7.71532C10.4182 7.45507 10.4583 7.20401 10.4583 6.9165C10.4583 6.11109 9.80542 5.45817 8.99996 5.45817C8.24462 5.45817 7.62389 6.03209 7.54917 6.76791ZM8.99996 1.08317C13.3723 1.08317 16.9166 4.62826 16.9166 8.99984C16.9166 13.3714 13.3723 16.9165 8.99996 16.9165C4.62761 16.9165 1.08329 13.3714 1.08329 8.99984C1.08329 4.62826 4.62761 1.08317 8.99996 1.08317ZM8.99996 1.639C4.94067 1.639 1.63913 4.94055 1.63913 8.99984C1.63913 13.0591 4.94067 16.3607 8.99996 16.3607C13.0592 16.3607 16.3608 13.0591 16.3608 8.99984C16.3608 4.94055 13.0592 1.639 8.99996 1.639ZM8.99996 12.3332C9.23009 12.3332 9.41663 12.5197 9.41663 12.7498C9.41663 12.98 9.23009 13.1665 8.99996 13.1665C8.76983 13.1665 8.58329 12.98 8.58329 12.7498C8.58329 12.5197 8.76983 12.3332 8.99996 12.3332Z"
                                    fill="#111C1C" stroke="#111C1C" stroke-width="0.833333"/>
                            </svg>
                        </div>
                    </el-tooltip>
                </div>
                <div class="dialog-close" @click="handleClose(false)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <g clip-path="url(#clip0_3538_30865)">
                            <path d="M20 0H0V20H20V0Z" fill="white" fill-opacity="0.01"/>
                            <path d="M5.83203 5.83337L14.1654 14.1667" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                            <path d="M5.83203 14.1667L14.1654 5.83337" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_3538_30865">
                                <rect width="20" height="20" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            </div>

            <!--内容区域-->
            <div class="display-flex flex-direction-col gap-24">
                <span class="font-size-16">{{$t('loc.batchAdaptLesson5')}}</span>

                <!--课程展示-->
                <el-table
                    ref="lessonTable"
                    v-loading="getAllPlanLessonLoading"
                    :data="lessonsInfo"
                    max-height="330"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="selectLessonItem">
                    <el-table-column
                        type="selection"
                        width="55">
                    </el-table-column>
                    <el-table-column
                        width="115"
                        :label="$t('loc.batchAdaptLesson15')"
                        show-overflow-tooltip>
                        <template class="overflow-ellipsis w-full" slot-scope="scope">{{
                                currentDayOfWeek(scope.row.day)
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('loc.batchAdaptLesson6')"
                        show-overflow-tooltip>
                        <template class="overflow-ellipsis w-full" slot-scope="scope">{{
                                scope.row.lessonTitle
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('loc.batchAdaptLesson7')"
                        width="165">
                        <template slot-scope="scope">
                            <div style="color: #67C23A;" class="gap-4 display-flex flex-justify-start align-items"
                                 v-if="scope.row.isAdaptedLesson">
                                <div class="el-icon-success"></div>
                                <div>
                                    {{ $t('loc.comple') }}
                                </div>
                            </div>
                            <div v-else style="color: #E6A23C;"
                                 class="gap-4 display-flex flex-justify-start align-items">
                                <div class="el-icon-warning"></div>
                                <div>
                                    {{ $t('loc.pending') }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!--弹窗的底部按钮区域-->
            <div slot="footer">
                <!-- 一个 取消 按钮 -->
                <el-button plain @click="handleClose(false)">
                    <span>{{$t('loc.cancel')}}</span>
                </el-button>
                <!-- 一个 Save 按钮 -->
                <el-button @click="handleClose(true)" :disabled="selectionLessonList.length==0" type="primary">{{$t('loc.confirm')}}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'LessonList',
    props: {
        planLessons: {
            type: Array,
            default: () => {
                return []
            }
        },
        getAllPlanLesson: {
            type: Function,
            default: () => {
                return () => {
                }
            }
        },
        previewAdapter: {
          type: Boolean,
          default: false
        }
    },
    data () {
        return {
            dialogVisible: false,
            lessonsInfo: [],
            getAllPlanLessonLoading: false, // 获取全部课程的 loading 状态
            selectionLessonList: [] // 选择的课程列表
        }
    },
    computed: {
        // 当前周几，将 1 转换为 Monday
        currentDayOfWeek () {
            return (day) => {
                // 首先判断 day 是否符合 [1-5] 之间的数字，使用正则
                if (!/^[1-5]$/.test(day)) {
                    return day
                }
                // 否则就执行匹配 week
                let week = day
                let weekMap = {
                    1: 'Monday',
                    2: 'Tuesday',
                    3: 'Wednesday',
                    4: 'Thursday',
                    5: 'Friday'
                }
                return weekMap[week]
            }
        }
    },
    watch: {
        planLessons: {
            handler: function (val) {
                // 如果 val 存在，并且长度大于 0
                if (val && val.length > 0) {
                    this.lessonsInfo = val
                }
            },
            immediate: true
        }
    },
    methods: {
        // 关闭弹窗，如果 saveClose 是 true 的时候，说明是通过点击保存而关闭弹窗的，否则就是通过点击关闭按钮关闭的
        handleClose (confirm) {
            this.dialogVisible = false
            // 向父组件发送关闭弹窗的事件
            if (confirm) {
                // 判断是否是预览状态下点击批量适配按钮的确认操作
                if (this.previewAdapter) {
                  this.$analytics.sendEvent('web_weekly_plan_view_ba_pop_confirm')
                }
                // 收集选中的课程
                // 点击确认按钮埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_ba_pop_confirm')
                this.$emit('selectionLessonList', this.selectionLessonList)
            }
        },
        showLessonList () {
            // 判断是否是预览状态下点击批量适配按钮的操作
            if (this.previewAdapter) {
              this.$analytics.sendEvent('web_weekly_plan_view_ba_select_pop')
            } else {
              // 显示批量改编选择课程弹窗曝光埋点
              this.$analytics.sendEvent('web_weekly_plan_edit_ba_select_pop')
            }
            this.dialogVisible = true
            // 设置 loading 状态
            this.getAllPlanLessonLoading = true
            // 调用父组件的方法，获取当前 plan 下面的所有的课程
            this.getAllPlanLesson(false).then(() => {
                // 请求获取完成之后，设置 loading 状态
                this.getAllPlanLessonLoading = false
            })
        },
        selectLessonItem (val) {
            this.selectionLessonList = val
        }
    }
}
</script>

<style lang="less" scoped>
.dialog-title {
    display: flex;
    align-items: flex-start;
    align-self: center;

    .dialog-title-content {
        flex: 1 0 0;
        color: var(--111-c-1-c, #111C1C);
        font-feature-settings: 'clig' off, 'liga' off;

        /* Semi Bold/20px */
        font-family: Inter;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 130% */
    }

    .dialog-close {
        cursor: pointer;
        width: 20px;
        height: 20px;
    }
}

.gap-8 {
    gap: 8px;
}

.gap-4 {
    gap: 4px;
}

.gap-24 {
    gap: 24px;
}

/deep/ .el-dialog__body {
    padding: 14px 20px !important;
}
</style>