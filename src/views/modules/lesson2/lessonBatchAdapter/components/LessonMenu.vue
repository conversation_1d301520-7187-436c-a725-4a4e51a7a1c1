<template>
    <div>
        <el-card>
            <!-- 课程 -->
            <div ref="scrollBar" class="lg-scrollbar-show" style="max-height: calc(100vh - 185px);  overflow-x: hidden;">
                <div class="menu-item sub-menu-item display-flex align-items justify-content-between"
                     style="gap: 10px"
                     :class="{'active-menu': isCurrent(item)}" v-for="item in lessonInfos" :key="item.itemId"
                     @click="selectLesson(item)">
                    <div class="flex-auto">
                        <!-- 周几 -->
                        <div style="font-weight: 600;gap: 5px" class="display-flex align-items">
                            <span>{{ currentDayOfWeek(item.day) }}</span>
                            <el-tag v-if="showAdaptTag(item)" effect="plain" class="adapted-tag" size="small">{{ $t('loc.adaptUnitPlanner25')}}</el-tag>
                        </div>
                        <!-- 课程名称 -->
                        <div v-if="item.centerName === null" @mouseover="checkTextOverflow(item)" class="overflow-ellipsis" style="max-width: 200px">
                            <el-tooltip :disabled="isShowTooltip" placement="top-start">
                                <div style="width: 300px;font-size: 14px;font-weight: 400" slot="content">{{ item.lessonTitle }}</div>
                                <span :ref="'overflewText' + item.itemId" class="ellipsis">{{ item.lessonTitle }}</span>
                            </el-tooltip>
                        </div>
                        <div v-else @mouseover="checkTextOverflow(item)" class="overflow-ellipsis" style="max-width: 200px">
                            <el-tooltip :disabled="isShowTooltip" placement="top-start">
                                <div style="width: 300px;font-size: 14px;font-weight: 400" slot="content">{{ item.centerName }}</div>
                                <span :ref="'overflewText' + item.itemId" class="ellipsis">{{ item.centerName }}</span>
                            </el-tooltip>
                        </div>
                    </div>
                    <!-- 确认状态 -->
                    <div class="lesson-confirm-status">
                        <!-- 已确认 -->
                        <span class="text-success"
                              v-if="lessonStatus.get(item.itemId.trim().toUpperCase()) === 'SUCCESS'">
                        <i class="lg-icon lg-icon-circle-check"></i>
                        </span>
                        <!--处理中-->
                        <span v-else-if="lessonStatus.get(item.itemId.trim().toUpperCase()) === 'PROCESSING'">
                            <i class="el-icon-loading"></i>
                        </span>
                        <!--处理失败-->
                        <span class="text-danger"
                              v-else-if="lessonStatus.get(item.itemId.trim().toUpperCase()) === 'FAIL'">
                            <i class="lg-icon lg-icon-reject"></i>
                        </span>
                    </div>
                    <div class="active-menu-mark" v-show="isCurrent(item)"></div>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script>
import { UnitPlanCenterIconMap } from '@/utils/constants'
import { mapState } from 'vuex'
import { equalsIgnoreCase } from '@/utils/common'

export default {
    props: {
        // 当前展示的课程的 Id
        currentItemId: {
            type: String,
            default: ''
        },
        // 周计划列表
        lessonInfos: {
            type: Array,
            default: () => []
        },
        // 是否禁用切换
        disableSwitch: {
            type: Boolean,
            default: false
        },
        // 通过 itemId 来获取课程的信息
        lessonStatus: {
            type: Map,
            default: () => new Map()
        },
        adaptGenerateLoading: {
          type: Boolean,
          default: false
        }
    },

    data () {
        return {
            iconMap: UnitPlanCenterIconMap, // Center 课程图标
            isShowTooltip: false, // 是否显示 tooltip
            activeItem: null, // 当前选中的活动项
            lastActiveLesson: null, // 上一次的选中的课程
            activeLesson: null // 当前选中的课程
        }
    },

    created () {
        // 初始化当前课程
        this.initCurrentLesson()
    },

    computed: {
        ...mapState({
            plans: state => state.curriculum.unit.lessonInfos,
            unit: state => state.curriculum.unit, // 单元信息
            baseInfo: state => state.curriculum.unit.baseInfo, // 单元基本信息
            planCenterType: state => state.lesson.planCenterType // Center 课程类型
        }),
        // 是否是当前选中的
        isCurrent () {
            return function (obj) {
                return equalsIgnoreCase(this.activeLesson.itemId, obj.itemId)
            }
        },
        // 当前周几，将 1 转换为 Monday
        currentDayOfWeek () {
            return (day) => {
                // 首先判断 day 是否符合 [1-5] 之间的数字，使用正则
                if (!/^[1-5]$/.test(day)) {
                    // 如果是 Center 课程，根据 planCenterType 来判断显示 station / center 前缀
                    if (this.planCenterType == 'K' && day.centerName !== null) {
                        return this.$t('loc.unitPlannerStep3Stations') + day.replace('Center', '')
                    }
                    return day
                }
                // 否则就执行匹配 week
                let week = day
                let weekMap = {
                    1: 'Monday',
                    2: 'Tuesday',
                    3: 'Wednesday',
                    4: 'Thursday',
                    5: 'Friday'
                }
                return weekMap[week]
            }
        },
        // menu 的高度
        menuHeight () {
            return this.lessonInfos.length * 60 + 'px'
        },
      // 是否显示 Adapt 标签
      showAdaptTag () {
        return function (item) {
          const adaptSuccess = this.lessonStatus.get(item.itemId.trim().toUpperCase()) === 'SUCCESS'
          // 如果正在改编，去判断是否是改编课程
          if (this.adaptGenerateLoading) {
            return item.isAdaptedLesson
          } else {
            return adaptSuccess || item.isAdaptedLesson
          }
        }
      }
    },
    methods: {
        // 初始化当前课程
        initCurrentLesson () {
            // 没有数据则跳过
            if (!this.lessonInfos || this.lessonInfos.length === 0) {
                return
            }
            // 如果有当前课程 ID，则初始化当前课程
            if (this.currentItemId) {
                // 为 activeLesson 赋值
                this.activeLesson = this.lessonInfos.find(item => item.itemId.toUpperCase() === this.currentItemId.toUpperCase())
            } else {
                this.activeLesson = this.lessonInfos[0]
            }
        },
        // 选择课程
        selectLesson (item) {
            // 如果当前选中的课程和点击的课程相同，则不做任何操作
            if (this.activeLesson.itemId.toUpperCase() === item.itemId.toUpperCase()) {
                return
            }
            if (this.disableSwitch) {
                return
            }
            // 判断选择的课程的加载状态是不是 PROCESSING，如果是，则不允许切换
            if (this.lessonStatus.get(item.itemId.trim().toUpperCase()) === 'PROCESSING') {
                return
            }
            // 更新 lastActiveLesson 为当前选中的课程
            this.lastActiveLesson = this.activeLesson
            // 更新当前课程
            this.activeLesson = item
            // 发送事件修改课程
            // 向父组件传递事件
            this.$emit('changeLesson', item)
        },
        // 检查文本是否溢出
        checkTextOverflow (item) {
            // 拼接要获取的元素的 ref
            const ref = 'overflewText' + item.itemId
            // 校验 ref 存在
            if (this.$refs[ref] && this.$refs[ref].length > 0) {
                // 获取元素父级可视宽度
                const parentWidth = this.$refs[ref][0].parentNode.offsetWidth
                // 获取元素可视宽度
                const contentWidth = this.$refs[ref][0].offsetWidth
                this.isShowTooltip = contentWidth <= parentWidth
            }
        }
    }
}
</script>

<style lang="less" scoped>
.menu-item {
    position: relative;
    padding: 10px 14px;
    cursor: pointer;

    &:hover {
        background-color: #E7F7F8;
    }
}

.sub-menu-item {
    padding-left: 24px;
}

.active-menu {
    color: #10B3B7;
    background-color: #E7F7F8;
}

.active-menu-mark {
    width: 3px;
    height: 100%;
    background-color: #10B3B7;
    position: absolute;
    left: 0;
    top: 0;
}

.lesson-confirm-status {
    flex-shrink: 0;
    flex-grow: 0;
}

/deep/ .el-collapse-item__header {
    line-height: unset !important;
    padding-left: 16px;
    padding-right: 4px;
    height: 60px;
}

/deep/ .active-week .el-collapse-item__header {
    background-color: #E7F7F8 !important;
}

/deep/ .el-card__body {
    padding: 10px 0 !important;
}

/deep/ .is-horizontal {
    height: 0px;
    left: 0px;
    display: none;
}

/deep/ .el-scrollbar__wrap {
    overflow-x: hidden;
}

/deep/ .confirm-generated-dialog {
    & .el-form-item__label {
        margin-bottom: 0;
        line-height: 22px;
        font-weight: 600;
    }

    & .el-dialog__body {
        padding: 0 20px;
    }

    & .el-tag.el-tag--info {
        color: #676879;
    }
}
.adapted-tag {
  color: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
}
</style>
