<template>
    <div>
        <!-- adapter 按钮及其引导-->
        <el-popover
            placement="bottom"
            width="380"
            :visible-arrow="true"
            v-model="isShowAdaptUDLAndCLRGuide"
            ref="settingGuide"
            popper-class="adapt-UDL-and-CLR-guide-color-text"
            trigger="manual">
            <div class="text-white">
                <!-- 引导文字 -->
                <div class="lg-margin-bottom-24 word-break text-left">
                    <!-- 用户引导内容 -->
                    <span class="font-size-16 font-weight-400 line-height-24">{{
                            $t('loc.batchAdaptLessonPlanTip')
                        }}</span>
                </div>
                <div class="display-flex flex-justify-end gap-6 align-items">
                    <el-button type="primary" @click="endAdaptUDLAndCLRGuide">
                        {{$t('loc.gotIt')}}
                    </el-button>
                </div>
            </div>
            <div slot="reference">
                <div class="position-relative">
                  <!--当用户的权限符合并且打开了 adapt udl 和 clr 的开关-->
                  <el-button v-if="showBatchAdapted && showOperateBtn && !isLocked" :disabled="isLocked" size="medium" @click="showAdapterTips(true)" class="ai-btn header-button" :style="{ 'margin-right': preview ? '0px': '12px' }">
                    <template #icon>
                      <i class="lg-icon lg-icon-generate"></i>
                    </template>
                    {{ getGenerateButtonName() }}
                  </el-button>
                  <el-button
                    v-if="isOpenAdaptUDLAndCLR && canAdapter && ((review || !!isLocked) || !preview || equalsIgnoreCase(planStatus, 'B_PENDING'))"
                    :disabled="isLocked"
                    size="medium"
                    @click="showAdapterTips"
                    class="ai-btn header-button"
                    :style="{ 'margin-right': !(review || !preview) ? '0px': '12px' }">
                    <template #icon>
                      <i class="lg-icon lg-icon-generate"></i>
                    </template>
                    {{ getGenerateButtonName() }}
                  </el-button>
                  <div class="position-absolute new-tag" v-if="showBatchAdapted && showAdaptNewTag && showOperateBtn" style="">New</div>
                </div>
            </div>
        </el-popover>
        <!-- 编辑课程弹窗 -->
        <el-dialog :visible.sync="dialogVisible"
                   :show-close=false
                   :append-to-body="true"
                   :modal-append-to-body="true"
                   :close-on-click-modal="false"
                   @opened="handlerBatchAdaptDialogOpen"
                   width="600px">
            <!--弹窗的头部信息-->
            <div slot="title" class="dialog-title">
                <div class="dialog-title-content">
                    {{$t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClass')}}
                </div>
                <div class="dialog-close" @click="batchAdaptTip(false)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <g clip-path="url(#clip0_3538_30865)">
                            <path d="M20 0H0V20H20V0Z" fill="white" fill-opacity="0.01"/>
                            <path d="M5.83203 5.83337L14.1654 14.1667" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                            <path d="M5.83203 14.1667L14.1654 5.83337" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_3538_30865">
                                <rect width="20" height="20" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            </div>

            <!--内容区域-->
            <div class="display-flex flex-direction-col gap-8">
                <span class="font-size-16">{{$t('loc.unitPlannerPersonalizePlanTitle')}}</span>
                <img class="w-full" src="@/assets/img/lesson2/plan/adapt.png"/>
            </div>

            <!--弹窗的底部按钮区域-->
            <div slot="footer">
                <!-- 一个 取消 按钮 -->
                <el-button plain>
                    <span @click="batchAdaptTip(false)">{{$t('loc.later')}}</span>
                </el-button>
                <!-- 一个 Save 按钮 -->
                <el-button :loading="getAllPlanLessonLoading" @click="batchAdaptTip(true)" type="primary">{{$t('loc.batchAdaptLessonPlan')}}
                </el-button>
            </div>
        </el-dialog>
        <!-- 显示课程的列表   -->
        <LessonList ref="lessonList"
                    :planLessons="planLessons"
                    :previewAdapter="previewAdapter"
                    :getAllPlanLesson="getAllPlanLesson"
                    @selectionLessonList="selectionLessonList"
        ></LessonList>
        <!--  展示 PersonalizePlan-->
        <PersonalizePlan
            :planId="planId"
            :itemIds="itemIds"
            @updateGenerateUniversalDesignAndCLRData="generateUniversalDesignAndCLRData"
            @callbackAddChildrenTip="handleCallBackAddChildrenTip"
            ref="personalizePlan">
        </PersonalizePlan>
        <!-- 展示 LessonEditor，当点击了生成内容之后，Editor 显示   -->
        <LessonEditor
            :planId="planId"
            :planStatus="planStatus"
            :lessonInfo="lessonList"
            :preview="preview"
            :planType="planType"
            @callbackAddChildrenTip="handleCallBackAddChildrenTip"
            :generateClassSpecificValue="true"
            ref="lessonEditor"></LessonEditor>
    </div>
</template>

<script>
import generateDefaultIcon from '@/assets/img/lesson2/assistant/generate.png'
import LessonList from './components/LessonList.vue'
import LessonEditor from './LessonEditor.vue'
import { mapState } from 'vuex'
import tools from '@/utils/tools'
import store from '@/store'
import PersonalizePlan from '@/views/modules/lesson2/lessonPlan/components/PersonalizePlan.vue'
import {equalsIgnoreCase} from "@/utils/common";

export default {
    name: 'AdapterTips',
    components: {
        PersonalizePlan,
        LessonList,
        LessonEditor
    },
    inject: ['dllContext'],
    props: {
        // 最初是的引导弹窗是否显示
        comeFromUnit: {
            type: Boolean,
            default: false
        },
        // 所有的 categories
        planId: {
            type: String,
            default: ''
        },
        hasBatchAdapterLesson: {
            type: Function,
            default: () => {
                return () => {
                }
            }
        },
        // 在 adapter 之前
        beforeAdapter: {
            type: Function,
            default: () => {
                return () => {
                }
            }
        },
        planStatus: {
            type: String,
            default: ''
        },
        // 预览模式
        preview: {
          type: Boolean,
          default: false
        },
        // 是否锁定（其他人正在编辑周计划）
        isLocked: {
          type: Object,
          default: null
        },
        showAdaptNewTag: {
          type: Boolean,
          default: false
        },
        planType: {
          type: String,
          default: ''
        },
        review: {
          type: Boolean,
          default: false
        }
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser,
            // adaptUDLAndCLROpenState: state => state.lesson.adaptUDLAndCLROpen, // UDL 和 CLR 开关
            open: state => state.common.open // UDL 和 CLR 开关
        }),
      /**
       * 选中的课程列表
       * @returns {*[]}
       */
        itemIds () {
          // eslint-disable-next-line no-unused-expressions
          this.triggerItemIds
          return this.lessonList.map(item => item.itemId)
        },
        /**
         * 获取用户 id
         */
        currentUserId () {
            if (!this.currentUser) {
                return ''
            }
            return this.currentUser.user_id
        },
        isOpenAdaptUDLAndCLR () {
          return this.open && this.open.adaptUDLAndCLROpen
        },
        /**
         * 是否来自于 ipad
         */
        isFromIpad () {
            return tools.isComeFromIPad()
        },
        isShowAdaptUDLAndCLRGuide: {
            get () {
                return this.canAdapter && this.isOpenAdaptUDLAndCLR && this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide
                // return true
            },
            set (val) {
                this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = val
            }
        },
        // 是否开启了 adapter 权限
        canAdapter () {
            if (!this.currentUser || this.isFromIpad) return false
            const { role2 = '' } = this.currentUser || {}
            let role = role2.toUpperCase()
            return role === 'COLLABORATOR' || role === 'TEACHING_ASSISTANT' || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
        },
        // 是否展示批量改编课程按钮
        showBatchAdapted () {
          return this.isOpenAdaptUDLAndCLR && this.canAdapter && this.preview && this.planType === 'NORMAL' && this.planStatus !== 'B_PENDING'
        },
       // 是否显示操作按钮
        showOperateBtn () {
          const routerNames = ['view-template', 'assigned-plan', 'edit-template', 'shared-plan-detail']
          return routerNames.indexOf(this.$route.name) === -1
        }
    },
    data () {
        return {
            dialogVisible: false, // 弹窗是否显示
            adaptUDLAndCLROpen: undefined, // 是否开启了 UDL 和 CLR
            adaptUDLAndCLRGuide: {
                showAdaptUDLAndCLRGuide: false // 展示 UDL 和 CLR 引导
            },
            getAllPlanLessonLoading: true, // 获取所有的课程的 loading
            // 为了触发 itemIds 的计算属性
            triggerItemIds: false,
            lessonList: [], // 选中的课程列表
            planLessons: [], // 当前 plan 下面的所有的课程
            previewAdapter: false // 是否是预览模式下的 adapter
        }
    },
    methods: {
        equalsIgnoreCase,
        // 打开弹窗的回调
        handlerBatchAdaptDialogOpen () {
            this.$nextTick(() => {
                // 弹窗打开的时候获取所有的课程
                // 设置 loading 为 true
                this.getAllPlanLessonLoading = true
                // 获取当前 plan 下面的所有的课程
                this.getAllPlanLesson(false, true).then(() => {
                    // 设置 loading 为 false
                    this.getAllPlanLessonLoading = false
                })
            })
        },
        // 获取当前 plan 下面的所有的课程的信息
        async getAllPlanLesson (includeCenterLesson, notNeedSave) {
            // 在 adapter 之前先调用保存
            if (this.beforeAdapter && !notNeedSave) {
                await this.beforeAdapter()
            }
            // 定义参数
            const params = {
                planId: this.planId,
                includeCenterLesson: includeCenterLesson
            }
            return new Promise((resolve, reject) => {
                // 发送请求
                this.$axios.get($api.urls().getPlanLessons, { params: params }).then(res => {
                    // 如果 res 存在，并且 res.lessons 是存在的
                    if (res && res.lessons) {
                        // 设置 planLessons
                        this.$set(this, 'planLessons', res.lessons)
                    }
                    resolve()
                }).catch(error => {
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },
        // 关闭弹窗，如果 saveClose 是 true 的时候，说明是通过点击保存而关闭弹窗的，否则就是通过点击关闭按钮关闭的
        batchAdaptTip (batchAdapt) {
            this.dialogVisible = false
            // 如果点击了保存按钮，则展示课程列表
            if (batchAdapt) {
                // 先判断是否存在可以改编的课程
                if (!this.hasBatchAdapterLesson()) {
                  this.$message.warning(this.$t('loc.batchAdaptLesson14'))
                  return
                }
                // 点击批量改编课程按钮埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_adapt_pop_batch')
                // 如果是在引导弹窗中直接点击的 batch adapt，那么选中的课程就是所有的课程
                // 当请求结束之后，将 planLessons 赋值给所有选中的课程
                this.lessonList = this.planLessons
                // 打开弹窗
                this.showPersonalizePlan()
            }
            // 向父组件发送关闭弹窗的事件
            this.$emit('close', batchAdapt)
            // 点击 Later 直接弹出添加小孩提示
            if (!batchAdapt) {
                // 点击关闭批量弹窗按钮埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_ba_exit')
                // 向父组件发送添加小孩提示的事件
                this.$emit('addChildrenTips')
                // 设置用户已经显示过添加小孩的提示
                this.$store.dispatch('setIsOpenAddChildrenTip', true)
                // 如果已经弹出了改编提示，则删除路由中的 apply 参数
                if (this.$route.params.apply) {
                    // 获取当前路由的 params 和 query
                    const currentParams = this.$route.params
                    const currentQuery = this.$route.query
                    delete currentParams.apply
                    // 使用 replace 方法替换当前路由
                    this.$router.replace({
                        name: this.$route.name, // 或者使用 path: this.$route.path
                        params: currentParams,
                        query: currentQuery
                    })
                }
            }
        },
        // 展示课程列表
        showAdapterTips (viewPlan = false) {
            // 判断是否从预览页进行批量改编课程
            if (viewPlan) {
              this.$analytics.sendEvent('web_weekly_plan_view_click_batch_adapt')
              this.previewAdapter = true
            }
            // 如果显示了 New 标签，则隐藏, 点击 Adapt 之后隐藏 New 标签
            if (this.showAdaptNewTag) {
                this.$emit('hideNewTag')
                localStorage.setItem(this.currentUser.user_id + 'BATCH_ADAPTED_NEW_TAG_GUIDE', false)
                let result = { 'features': ['BATCH_ADAPTED_NEW_TAG_GUIDE'] }
                this.$axios.post($api.urls().hideGuide, result).then()
            }
            // 点击批量改编课程按钮埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_click_batch_adapt')
            // 先判断是否存在可以改编的课程
            if (!this.hasBatchAdapterLesson()) {
                this.$message.warning(this.$t('loc.batchAdaptLesson14'))
                return
            }
            // 点击则展示课程列表，这个时候获取对应的课程信息
            this.$refs.lessonList && this.$refs.lessonList.showLessonList()
        },
        // 选中的课程列表
        selectionLessonList (lessonList) {
            // 设置 lessonList
            this.lessonList = lessonList
            // 将选中的课程列表传递给 PersonalizePlan 组件
            this.showPersonalizePlan()
        },
        // 生成 Universal Design And CLR Data
        generateUniversalDesignAndCLRData () {
            // 定义要加载的课程
            let loadLesson = []
            // 对 loadLesson 进行全部去重
            loadLesson = Array.from(new Set(this.lessonList))
            // 批量改编课程弹窗曝光埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_ba_exposure')
            // 要开始生成数据了，所以要展示 LessonEditor，将要加载的课程传递给 LessonEditor
            this.$refs.lessonEditor && this.$refs.lessonEditor.initLessons(loadLesson)
        },
        // 展示 PersonalizePlan
        showPersonalizePlan () {
            // 展示 PersonalizePlan
            this.triggerItemIds = !this.triggerItemIds
            this.$refs.personalizePlan.showPersonalizePlan()
        },
        /**
         * 关闭设置引导
         */
        endAdaptUDLAndCLRGuide () {
            // 如果引导开启了才会关闭
            if (this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide) {
                this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = false
                tools.localItem(store.state.user.uid + '_BATCH_ADAPT_LESSONS_GUIDE', JSON.stringify(false))
                // 同时告诉后台,引导已经完成了
                this.handleHideBatchAdaptGuide()
                // 关闭引导
                this.isShowAdaptUDLAndCLRGuide = false
                // 开启周计划 AI 生成课程引导
                this.$store.dispatch('setPlanGenerateLessonGuide', true)
            }
        },
        // 发起请求，隐藏批量 Adapt 课程引导
        handleHideBatchAdaptGuide () {
          let result = { 'features': ['SHOW_BATCH_ADAPT_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then()
        },
        /**
         * 获取生成按钮的图标
         */
        getGenerateButtonIcon () {
            return generateDefaultIcon
        },
        /**
         * 获取生成按钮的名称：Generate、Generating Measures、Generating Lesson Plan、Regenerate
         */
        getGenerateButtonName () {
            // 已经生成过课程信息了
            return this.$t('loc.batchAdaptLessonPlan')
        },
        /**
         * 判断是否需要引导
         */
        judgeNeedAdaptUDLAndCLRGuide () {
            // 如果 fromIpad 直接将 adaptUDLAndCLROpen 设置为 false
            if (this.isFromIpad) {
                // 开启周计划 AI 生成课程引导
                this.$store.dispatch('setPlanGenerateLessonGuide', true)
                return
            }
            // 如果不是 老师且未开启 ，就直接 return
            if (!(this.isOpenAdaptUDLAndCLR && this.canAdapter)) {
                this.$store.dispatch('setPlanGenerateLessonGuide', true)
                return
            }
            // 先从本地浏览器缓存中查看是否存储了该缓存信息
            let batchAdaptLessonsGuide = tools.localItem(store.state.user.uid + '_BATCH_ADAPT_LESSONS_GUIDE')
            // 如果没有则查询数据库
            if (batchAdaptLessonsGuide === undefined || batchAdaptLessonsGuide === null) {
                this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
                    // 获取数据库中的信息
                    if (result !== null && result.showBatchAdaptLessonsGuide) {
                        // 展示并存入缓存中
                        this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = true
                        tools.localItem(store.state.user.uid + '_BATCH_ADAPT_LESSONS_GUIDE', false)
                        // 同时告诉后台,引导已经完成了
                        this.handleHideBatchAdaptGuide()
                    } else {
                        // 不展示并存入缓存中
                        this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = false
                        tools.localItem(store.state.user.uid + '_BATCH_ADAPT_LESSONS_GUIDE', false)
                        this.$store.dispatch('setPlanGenerateLessonGuide', true)
                    }
                })
            } else {
                // 如果浏览器缓存中有，则解析浏览器中缓存的对象，判断是否需要展示
                batchAdaptLessonsGuide = JSON.parse(batchAdaptLessonsGuide)
                this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = batchAdaptLessonsGuide
                // 继续后续引导
                if (!batchAdaptLessonsGuide){
                  this.$store.dispatch('setPlanGenerateLessonGuide', true)
                }
            }
        },
        /**
         * 处理是否显示添加小孩的提示弹窗
         */
        handleCallBackAddChildrenTip () {
          // 向父组件发送添加小孩提示的事件
          this.$emit('addChildrenTips')
          // 设置用户已经显示过添加小孩的提示
          this.$store.dispatch('setIsOpenAddChildrenTip', true)
        }
    }
}
</script>

<style lang="less" scoped>
.dialog-title {
    display: flex;
    align-items: flex-start;
    align-self: center;

    .dialog-title-content {
        flex: 1 0 0;
        color: var(--111-c-1-c, #111C1C);
        font-feature-settings: 'clig' off, 'liga' off;

        /* Semi Bold/20px */
        font-family: Inter;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 130% */
    }

    .dialog-close {
        cursor: pointer;
        width: 20px;
        height: 20px;
    }
}

.header-button {
    display: flex;
    height: 36px;
    padding: 8px 12px;
    margin-right: 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    width: fit-content;
}

.ai-btn, .ai-btn:hover, .ai-btn:focus {
    background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
    border-color: none !important;
    border: 0 !important;
    color: #FFFFFF;

}

.ai-btn:active {
    opacity: 0.5 !important;
}

.gap-8 {
    gap: 8px;
}

/deep/ .el-dialog__body {
    padding: 14px 20px !important;
}
.new-tag {
  z-index: 2;
  position: absolute;
  right: -10px;
  top: -8px;
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
}
</style>
<style lang="less">

.el-popper.adapt-UDL-and-CLR-guide-color-text {
    background: var(--color-ai-assistant);
    color: #FFFFFF;
    padding: 24px;
    border: none;

    &.el-popper[x-placement^=left] .popper__arrow::after {
        border-left-color: var(--color-ai-assistant);
    }

    &.el-popper[x-placement^=right] .popper__arrow::after {
        border-right-color: var(--color-ai-assistant);
    }

    &.el-popper[x-placement^=bottom] .popper__arrow {
        display: block !important;
        border-bottom-color: var(--color-ai-assistant) !important;
    }

    &.el-popper[x-placement^=bottom] .popper__arrow::after {
        border-bottom-color: var(--color-ai-assistant) !important;
    }

    &.el-popper[x-placement^=top] .popper__arrow::after {
        border-top-color: var(--color-ai-assistant);
    }

    p {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 150%;
    }

    ul {
        padding-left: 24px;
        margin-bottom: 24px;

        li {
            list-style: disc;
            font-family: 'Inter';
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 150%;
        }
    }

    .el-button {
        //color: var(--color-ai-assistant);
        //background: var(--color-white);
        //border-color: var(--color-ai-assistant);
        border-radius: 4px;
        border: 2px solid var(--dcdfe-6, #DCDFE6);
        background: var(--ffffff, #FFF);
        color: #676879;
    }

    .el-button:hover {
        border-radius: 4px;
        border: 2px solid var(--dcdfe-6, #DCDFE6);
        background: var(--ffffff, #FFF);
    }
}
</style>
