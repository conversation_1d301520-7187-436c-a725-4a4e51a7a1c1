<template>
    <div>
        <!-- 编辑课程弹窗 -->
        <el-dialog
            :title="$t('loc.batchAdaptLessonDialogTitle')"
            :append-to-body="true"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :top="'50px'"
            :fullscreen="true"
            :destroy-on-close="true"
            @close="closeLessonEditDialog"
            :before-close="cancelEditLesson">
            <div class="display-flex flex-start-center" v-if="dialogVisible">
                <LessonMenu
                    class="lesson-menu"
                    :currentItemId="currentItemId"
                    :lessonInfos="planLessonsNotCenter"
                    :lessonStatus="lessonStatus"
                    :adaptGenerateLoading="adaptLoading"
                    :disableSwitch="loading || adaptLoading || generating"
                    @changeLesson="changeLesson"
                    ref="lessonMenu"></LessonMenu>
                <div class="display-flex flex-direction-col position-relative" style="width: fit-content; max-width: calc(100% - 280px)">
                  <el-card style="position:sticky;top: 0px;z-index: 999;margin-left: 20px;border: unset;box-shadow: unset">
                    <div class="display-flex align-items justify-content-between">
                      <span class="font-size-18 font-weight-600 text-ellipsis" :style="{'display': 'inline-block', 'width': 'calc(100% - 204px)', 'maxWidth': '903px'}" :title="currentLesson && currentLesson.lessonTitle">{{ currentLesson && currentLesson.lessonTitle }}</span>
                      <el-tooltip :content="$t('loc.adaptUDLAndCLRDesc')" popper-class="max-width-400">
                      <el-button class="ai-btn" v-if="(canAdapter && isOpenAdaptUDLAndCLR && showOperateBtn && isNormalTemplate)" @click="adaptLesson" :loading="adaptLoading" :disabled="generating">
                        <template #icon>
                          <i class="lg-icon lg-icon-generate"></i>
                        </template>
                        {{ $t('loc.adaptUDLAndCLRButton') }}
                      </el-button>
                      </el-tooltip>
                    </div>
                  </el-card>
                  <el-card style="border: unset;margin-left: 20px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;margin-top: -20px;" class="lesson-editor-content">
                    <div class="lg-scrollbar-show h-full">
                      <!-- 复制课程副本时 loading的骨架屏 -->
                      <lesson-detail-skeleton class="lesson-detail-skeleton" v-show="loading"></lesson-detail-skeleton>
                      <div v-for="(lesson) in loadLessons" :key="lesson.lessonId"
                           v-show="currentItemId.toUpperCase() === lesson.itemId.toUpperCase()">
                        <!-- 课程编辑页面 -->
                        <LessonDetail v-show="!loading" class="lesson-detail"
                                      @updateGenerateStatus="updateGenerateStatus"
                                      @updateAdaptedStatus="updateAdaptedStatus"
                                      @updateLessonName="updateLessonName"
                                      :ref="'lessonDetail' + lesson.itemId"
                                      :centerName="lesson.centerName"
                                      :weeklyEditLesson="true"
                                      :adaptLoading="adaptLoading"
                                      :itemId="lesson.itemId"
                                      :planStatus="planStatus"
                                      :batchEdit="true"
                                      :singleAdaptLesson="singleAdaptLesson"
                                      :isAdaptedLesson="lesson.isAdaptedLesson"
                                      :singleEditLesson="singleEditLesson"
                                      :lessonId="lesson.lessonId" :inDialog="true"></LessonDetail>
                      </div>
                    </div>
                  </el-card>
                </div>
            </div>
            <!-- 底部操作按钮 -->
            <span slot="footer" class="dialog-footer">
            <div v-if="dialogVisible" class="display-flex align-items position-relative justify-content" style="margin-left: 300px">
                <el-checkbox v-show="showRecommendCheckbox && !adaptLoading" class="position-absolute position-absolute-left-0"
                             v-model="recommendToAgency">{{ $t('loc.saveToAgencyWideLesson') }}</el-checkbox>
                <div>
                    <el-button v-if="showNext && !singleEditLesson && !singleAdaptLesson" :disabled="loading || generating"
                               :loading="!generateUniversalDesignForLearningCompleted || !generateCLRCompleted || hasGenerating || saveLoading"
                               plain
                               @click="nextLesson">{{ $t('loc.batchAdaptLesson20') }}</el-button>
                    <el-button v-if="(singleEditLesson || singleAdaptLesson) && showNext"
                               :loading="loading || singleAdapting || saveLoading" :disabled="loading || generating" plain
                               @click="nextLesson">{{ $t('loc.batchAdaptLesson20') }}</el-button>
                    <!-- 更新和重新生成按钮 -->
                    <el-button slot="reference"
                               type="primary"
                               class="ai-btn"
                               :loading="loading || generating || singleAdapting || !generateUniversalDesignForLearningCompleted || !generateCLRCompleted || hasGenerating || saveLoading"
                               @click="openRedesignLessonDialog">
                        <template #icon>
                            <i class="lg-icon lg-icon-generate"></i>
                        </template>
                      {{ $t('loc.enhanceLesson') }}
                    </el-button>
                    <el-button v-if="!singleEditLesson && !singleAdaptLesson" :disabled="loading || generating" type="primary"
                               :loading="!generateUniversalDesignForLearningCompleted || !generateCLRCompleted || hasGenerating || saveLoading"
                               @click="saveAllLesson">{{$t('loc.batchAdaptLesson16')}}</el-button>
                    <el-button v-if="singleEditLesson || singleAdaptLesson" :loading="loading || singleAdapting || saveLoading"
                               :disabled="loading || generating" type="primary"
                               @click="saveCurrentLesson(true)">{{ $t('loc.publish') }}</el-button>
                </div>
            </div>
        </span>
        </el-dialog>

        <!-- 更新和重新生成课程弹窗 -->
        <LessonEnhanceDialog
                          :dialogVisible.sync="regenerateLessonVisible"
                          :lessonMeasures="redesignLessonInfo.measures"
                          :lessonActivityTime="redesignLessonInfo.activityTime"
                          :frameworkId="redesignLessonInfo.frameworkId"
                          @confirmRedesignLesson="confirmRedesignLesson">
        </LessonEnhanceDialog>
    </div>
</template>

<script>
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton.vue'
import LessonDetail from '@/views/modules/lesson2/lessonLibrary/editor/index.vue'
import LessonMenu from './components/LessonMenu.vue'
import LessonEnhanceDialog from '@/views/modules/lesson2/lessonLibrary/components/LessonEnhanceDialog.vue'
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import { equalsIgnoreCase } from '@/utils/common'
import tools from '@/utils/tools'
import Lessons2 from '@/api/lessons2'

export default {
    name: 'LessonEditor',
    props: {
        lessonInfo: {
            type: Array,
            default: () => {
                return []
            }
        },
        planId: {
            type: String,
            default: ''
        },
        // 是否是批量进来要给所有的课程进行生成数据的
        generateClassSpecificValue: {
            type: Boolean,
            default: false
        },
        // 周计划状态
        planStatus: {
          type: String,
          default: ''
        },
        // 是否预览状态
        preview: {
          type: Boolean,
          default: false
        },
        planType: {
          type: String,
          default: ''
        }
    },
    components: { LessonDetailSkeleton, LessonDetail, LessonMenu, LessonEnhanceDialog },
    data () {
        return {
            dialogVisible: false, // 是否显示弹窗
            batchId: undefined, // 批量生成的任务 id
            taskCompleted: false, // 任务是否完成
            selectGroupId: null, // 选择的 groupId
            loading: false, // 是否正在加载
            recommendToAgency: true, // 是否推荐给机构
            currentItemId: '', // 当前编辑的课程id
            loadedLessonCount: 0, // 已经加载的课程数量
            lessonsPerBatch: 1, // 每次加载的课程数量
            loadLessons: [], // 需要加载的课程
            children: [], // 当前班级中所有的小孩
            planLessons: [], // 当前班级中所有的课程
            queryBatchTaskInterval: null, // 查询批量任务的定时器
            planLessonIds: [], // 所有的 plan 课程
            savedLessons: [], // 有修改已保存的课程
            lessonStatus: new Map(), // 每一个课程的生成状态
            singleEditLesson: false, // 是否是单个编辑课程
            batchEdit: false, // 是否是批量编辑
            generateUniversalDesignForLearningCompleted: false, // 是否生成 UDL 完成
            generateCLRCompleted: false, // 是否生成 CLR 完成
            showNext: true, // 是否显示下一步按钮
            singleAdaptLesson: false, // 是否单个 Adapt
            currentLessonIsProcessing: false, // 是否是我的课程
            initEdit: false, // 是否初始化编辑
            secondToLast: false, // 是否倒数第二个课程
            successTaskItemIds: [], // 成功的任务的 ItemIds
            updateCount: 0, // 更新次数
            currentPublished: new Map(), // 当前课程是否已经发布
            initAdapted: false, // 是否初始化改编
            generating: false, // 是否正在生成
            currentPlanId: '', // 当前操作的周计划 ID
            regenerateLessonVisible: false, // 重新生成课程内容弹窗
            // 重新生成课程弹窗信息
            redesignLessonInfo: {
              measures: [], // 课程测评点信息
              activityTime: '', // 课程活动时间
              frameworkId: '' // 课程框架 ID
            },
            saveLoading: false, // 保存课程的 Loading
            originalAdaptItemIds: [], // 原始的改编课程数量
            currentPublishedCopy: new Map()
        }
    },
    watch: {
        // 监听弹框打开关闭状态
        dialogVisible(val) {
          if (val) {
            // 打开时清空已发布的 Map 状态，保存原始改编过的课程
            this.currentPublishedCopy = new Map()
            this.originalAdaptItemIds = this.planLessonsNotCenter.filter(item => item.isAdaptedLesson).map(item => item.itemId)
          } else {
            // 关闭时过滤出所有改编的课程
            const adaptLessonIds = this.planLessonsNotCenter.filter(item => item.isAdaptedLesson).map(item => item.itemId)
            let count = 0
            for (let i = 0; i < adaptLessonIds.length; i++) {
              // 判断是否是新改编的
              if (!this.originalAdaptItemIds.includes(adaptLessonIds[i]) && this.currentPublishedCopy.get(adaptLessonIds[i])) {
                count++
              }
            }
            // 发送事件，单元数据进行修改
            this.$bus.$emit('updateUnitAdaptedActivityCount', count)
          }
        },
        // 监听 lessonInfo
        lessonInfo: {
            handler (val) {
                // 如果 lessonInfo 有值，那么就将 lessonInfo 中的 lessonId 放入 planLessonIds 中
                if (val && val.length > 0) {
                    // 将对应的 itemId 转化为大写，然后放入 planLessonIds 中
                    val.forEach((itemLesson, index) => {
                        itemLesson.itemId = itemLesson.itemId.toUpperCase()
                        // 如果是批量改编，则需要将第一个课程的 isAdaptedLesson 设置为 false
                        if (index === 0 && this.generateClassSpecificValue && !this.initAdapted) {
                          this.$set(itemLesson, 'isAdaptedLesson', false)
                        }
                    })
                    this.planLessons = val
                }
            },
            immediate: true,
            deep: true
        },
        // 监听单个编辑课程状态
        singleEditLesson () {
            // 如果是单个编辑课程且 singleEditLesson 为 true 时打开弹窗
            if (this.singleEditLesson && !this.batchEdit) {
                this.getPlanLessons()
            }
        },
        // 当前编辑的 itemId
        currentItemId () {
            // 编辑时，在侧边栏选择编辑课程时，判断是否显示下一步按钮
            if (this.singleEditLesson || this.singleAdaptLesson) {
                // 如果周计划课程存在，且课程数大于 0
                if (this.planLessons && this.planLessons.length > 0) {
                    // 获取当前编辑的课程的 index
                    const currentItemIndex = this.planLessons.findIndex(lesson => lesson.itemId.toUpperCase() === this.currentItemId.toUpperCase())
                    // 如果当前编辑的课程是最后一个课程，则不显示下一步按钮
                    this.showNext = currentItemIndex !== this.planLessons.length - 1
                }
            } else {
                const loadLessons = this.planLessonIds.filter((planLesson, index) => {
                    if (index === 0) {
                        this.$set(planLesson, 'isAdaptedLesson', true)
                    }
                    return planLesson.isAdaptedLesson || index === 0
                })
                if (loadLessons.length !== 0) {
                    // 获取未选中的且是改编课程的课程
                    const unselectedAdaptedLessons = loadLessons.filter(lesson => !lesson.selected && lesson.isAdaptedLesson)
                    // 如果存在，则显示
                    if (unselectedAdaptedLessons.length > 0) {
                        this.showNext = true
                    } else {
                        this.showNext = false
                    }
                }
            }
        },
        lessonStatus (val) {
            // 获取当前课程的 Adapt 状态值
            const currentLessonStatus = val.get(this.currentItemId.trim().toUpperCase())
            // 如果当前课程的 Adapt 状态值不存在，或者不是 PROCESSING 状态，那么就直接返回
            if (!currentLessonStatus) {
                this.currentLessonIsProcessing = false
            }
            if (equalsIgnoreCase(currentLessonStatus, 'PROCESSING')) {
                this.currentLessonIsProcessing = true
            }
        },
        loading (val) {
          if (!val) {
            this.initEdit = false
          }
        }
    },
    destroyed () {
        // 清理数据
        this.clearOldData()
    },
    created () {
        // 监听事件
        this.$bus.$on('updateSelectedGroupId', () => {
            this.selectGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
        })
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser,
            open: state => state.common.open,
            isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
            originUnitId: state => state.unit.originUnitId // 源单元 ID
        }),
        groupId () {
            // 判断 this.selectGroupId 是否存在
            if (this.selectGroupId) {
                return this.selectGroupId
            }
            // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
            const selectedGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
            if (selectedGroupId) {
                return selectedGroupId
            }
            // 如果没有获取得到 selectGroupId
            return ''
        },
        currentUserId () {
            if (!this.currentUser) {
                return ''
            }
            return this.currentUser.user_id
        },
        // 是否正在加载中
        hasGenerating () {
            return Array.from(this.lessonStatus.values()).some(status => status.trim().toUpperCase() === 'PROCESSING')
        },
        // 单个 Adapt 正在加载中
        singleAdapting () {
            return (this.singleEditLesson || this.singleAdaptLesson) && this.lessonStatus.get(this.currentItemId.trim().toUpperCase()) === 'PROCESSING'
        },
        //  获取指定属性的值
        getAttrValue () {
            return function (child, attrName) {
                // 不存在返回空
                if (!child || !attrName) {
                    return ''
                }
                // 属性列表
                let attrs = child.attrs
                if (!attrs) {
                    return ''
                }
                // 匹配到的属性值
                let matchValues = null
                // 遍历属性列表
                attrs.forEach(attr => {
                    // 匹配属性名称
                    if (attr && attr.name && attr.name.trim().toLowerCase() === attrName.trim().toLowerCase()) {
                        // 属性值
                        let attrValues = attr.values
                        if (attrValues && attrValues.length > 0) {
                            matchValues = attrValues
                        }
                    }
                })
                // 如果有属性值，以逗号分割
                if (matchValues) {
                    return matchValues.join(', ')
                }
                // 没有值
                return ''
            }
        },
        /**
         * 是否是管理员
         */
        isAdmin () {
            if (!this.currentUser) {
                return false
            }
            let role = this.currentUser.role2
            return (role.toUpperCase() === 'AGENCY_ADMIN' || role.toUpperCase() === 'AGENCY_OWNER')
        },
        /**
         * 推荐到机构课程库开关
         */
        recommendToAgencyOpen () {
            return this.open && this.open.recommendLessonToAgencyOpen && !this.open.educator
        },
        /**
         * 是否显示保存到机构课程库的复选框
         */
        showRecommendCheckbox: {
            get () {
              const adaptedStatus = this.lessonStatus.get(this.currentItemId.toUpperCase())
              return !this.isAdmin && this.recommendToAgencyOpen && (!this.currentLessonIsAdapted() || (adaptedStatus && !equalsIgnoreCase(adaptedStatus, 'SUCCESS')))
            }
        },
        // 当前课程
        currentLesson () {
          return this.planLessons.find(lesson => equalsIgnoreCase(lesson.itemId, this.currentItemId))
        },
        // 是否开启了 adapter 权限
        canAdapter () {
          if (!this.currentUser) return false
          const { role2 = '' } = this.currentUser || {}
          let role = role2.toUpperCase()
          return role === 'COLLABORATOR' || role === 'TEACHING_ASSISTANT' || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
        },
        // 是否开启了 UDL And CLR 开关
        isOpenAdaptUDLAndCLR () {
          return this.open && this.open.adaptUDLAndCLROpen && !this.isFromIpad
        },
        // 是否 IPAD
        isFromIpad () {
          return tools.isComeFromIPad()
        },
        // 是否正在 Adapted 进行中
        adaptLoading () {
          return this.loading || this.singleAdapting || this.hasGenerating
        },
        // 是否显示改编按钮
        showOperateBtn () {
          const routerNames = ['view-template', 'assigned-plan', 'edit-template', 'shared-plan-detail', 'edit-agency-template', 'curriculumUnitDetail', 'curriculumEdit']
          return routerNames.indexOf(this.$route.name) === -1
        },
        // 是否是普通周计划
        isNormalTemplate () {
          return equalsIgnoreCase(this.planType, 'NORMAL') || this.planType === ''
        },
        // 过滤掉区角课程
        planLessonsNotCenter() {
          return this.planLessons.filter(lesson => !lesson.centerName)
        },
    },
    methods: {
        // 确认增加想法改编课程
        async confirmRedesignLesson (data) {
          // ref 判空
          if (!this.$refs['lessonDetail' + this.currentItemId]){
            return
          }
          // 获取该课程的 ref
          const lessonDetail = this.$refs['lessonDetail' + this.currentItemId][0]
          if (!lessonDetail) {
            return
          }
          // 开始 Loading
          this.adaptLoading = true
          // 关闭弹框
          this.regenerateLessonVisible = false
          // 调用重新生成方法
          await lessonDetail.generalConfirmRedesignLesson(data)
          // 结束 Loading
          this.adaptLoading = false
        },

        // 打开重新生成课程详情
        async openRedesignLessonDialog () {
            // console.log('openRedesignLessonDialog')
          // 获取该课程的 ref
          const lessonDetail = this.$refs['lessonDetail' + this.currentItemId][0]
          if (lessonDetail) {
            // 获取课程测评点信息
            this.redesignLessonInfo.measures = lessonDetail.lesson.measures
            // 获取课程活动时间
            this.redesignLessonInfo.activityTime = lessonDetail.lesson.activityTime
            // 获取课程框架 ID
            this.redesignLessonInfo.frameworkId = lessonDetail.lesson.framework.frameworkId
            // console.log('this.redesignLessonInfo', this.redesignLessonInfo)
            // 校验表达必填项
            const result = await lessonDetail.checkFromValidate()
            if (result) {
              // 打开重新生成课程弹框
              this.regenerateLessonVisible = true
            }
          }
        },

        // 更新生成状态
        updateGenerateStatus (generating) {
          this.generating = generating
        },

        // 更新改编状态
        updateAdaptedStatus () {
            this.planLessons.forEach(lesson => {
                // 找到当前课程，将其 isAdaptedLesson 设置为 false
                if (lesson.itemId.toUpperCase() === this.currentItemId.toUpperCase()) {
                    // 从 lessonStatus 中移除当前课程的改编状态
                    this.lessonStatus.delete(this.currentItemId.toUpperCase())
                    this.lessonStatus = new Map(this.lessonStatus)
                }
            })
        },

        // 更新课程名称
        updateLessonName (lessonId, lessonName) {
            // 遍历 planLessons，找到对应的课程，将其 lessonTitle 设置为 lessonName
            this.planLessons.forEach(lesson => {
                if (equalsIgnoreCase(lesson.lessonId, lessonId)) {
                    lesson.lessonTitle = lessonName
                }
            })
        },

        // 判断是否是我的课程
        async isMyLesson (lessonsToLoad) {
            let isMyLesson = false
            // 使用 currentItem 查询 item
            let item = this.planLessons.find(lesson => lesson.itemId.toUpperCase() === lessonsToLoad.itemId.toUpperCase())
            // 判断是否是我的课程
            if (item.lessonAuthorId && this.currentUser.user_id) {
                const { role2 = '' } = this.currentUser || {}
                let role = role2.toUpperCase()
                isMyLesson = item.lessonAuthorId.toLowerCase() === this.currentUser.user_id.toLowerCase() && (role.trim().toUpperCase() === 'COLLABORATOR' || role.trim().toUpperCase() === 'TEACHING_ASSISTANT')
            }
            // 如果是我的课，
            if (isMyLesson) {
                // 那就还需要判断是否被推荐到机构课程了
                let lessonRecommendStatusRes = await LessonApi.getLessonRecommendStatus(item.lessonId)
                // 如果已经被推荐到机构课程了，则就表示不单纯是我的课了，就需要重新复制一份
                if (lessonRecommendStatusRes.agencyStatus === 'PROMOTED') {
                    isMyLesson = false
                }
            }
            // 返回对课程的校验结果
            return isMyLesson
        },
        // 判断当前课程是否已经 Adapted
        currentLessonIsAdapted () {
          // 获取当前课程
          const currentLesson = this.planLessonIds.find(lesson => equalsIgnoreCase(lesson.itemId, this.currentItemId))
          // 如果当前课程已经 adapted 或者当前课程正在处理中，那么就返回 true
          if (currentLesson.isAdaptedLesson || this.currentLessonIsProcessing) {
            return true
          }
          // 否则返回 false
          return false
        },
        // 添加需要批量编辑的课程
        async addLoadLesson (lessonsToLoad) {
            // 如果没有要加载的课程就直接返回
            if (!lessonsToLoad) {
                return
            }
            // 如果是批量改编的形式，则直接复制对应的课程
            if (this.generateClassSpecificValue || this.singleAdaptLesson) {
                // 打开弹窗
                this.dialogVisible = true
                // 如果课程已经 adapted 就不用复制了
                if (!lessonsToLoad.isAdaptedLesson) {
                    // 设置 loading
                    this.loading = true
                    await this.copyLesson(lessonsToLoad)
                }
            } else {
                // 如果不是批量改编的形式，那么就判断是不是我的课程
                let isMyLesson = await this.isMyLesson(lessonsToLoad)
                // 赋值是否属于我的课程
                // 如果不是我的课程就进行 copy
                if (!isMyLesson) {
                    // 设置 loading
                    this.loading = true
                    this.dialogVisible = true
                    await this.copyLesson(lessonsToLoad)
                }
            }
            // 将要加载的课程放入 loadLessons 中
            this.loadLessons.push(lessonsToLoad)
            // 设置 loading
            this.loading = false
        },
        // 初始化对应的课程
        async initLessons (loadLesson) {
            // 如果预览状态下，改编时需要锁定周计划
            if (this.preview) {
              // 编辑模式锁定周计划
              LessonApi.lockPlan(this.planId, this.currentUser.user_id)
            }
            // 由于 lesson 需要加载，那么此时将要加载的课程放入 loadingLessonMap 中
            this.loading = true
            // 如果是第一次，那么 this.planLessonIds 是空的，这个时候判断是否存在外界传递的 loadLesson
            if (loadLesson && loadLesson.length > 0) {
                // 将 loadLesson 的 itemId 转化为大写
                loadLesson.forEach(itemLesson => {
                    itemLesson.itemId = itemLesson.itemId.toUpperCase()
                    if (this.generateClassSpecificValue) {
                      // 要初始化的课程的状态
                      itemLesson.isAdaptedLesson = false
                    }
                })
                // 为当前的  data 赋值
                this.planLessonIds = loadLesson
                let lessonsToLoad
                // 如果是非单个编辑课程
                if (!this.singleEditLesson && !this.singleAdaptLesson) {
                    // 如果 loadLesson 有值，说明是编辑课程
                    this.currentItemId = loadLesson[0].itemId
                    // 每次加载一个课程
                    const start = this.loadedLessonCount
                    const end = start + this.lessonsPerBatch
                    // 切割出需要加载的课程
                    lessonsToLoad = this.planLessonIds.slice(start, end)
                    // 如果有课程需要加载
                } else {
                    lessonsToLoad = this.planLessonIds.filter(planLesson => equalsIgnoreCase(planLesson.itemId, this.currentItemId))
                    // // 如果是单个改编，则需要将当前课程的 isAdaptedLesson 设置为 false
                    // if (lessonsToLoad.length > 0 && this.singleAdaptLesson) {
                    //   lessonsToLoad[0].isAdaptedLesson = false
                    // }
                }
                if (lessonsToLoad.length > 0) {
                    // 为 loadLessons 赋值
                    await this.addLoadLesson(...lessonsToLoad)
                    // 恢复课程
                    this.recoverLesson()
                    // 加载课程数量
                    this.loadedLessonCount += lessonsToLoad.length
                    // 加载课程
                    this.loadLesson()
                    // 加载课程结束后
                    this.loading = false
                }
                this.$nextTick(() => {
                    // 如果只有一个课程，则不需要显示下一步按钮
                    if (this.planLessonIds.length === 1) {
                        this.showNext = false
                    }
                })
            }
        },
        // 拷贝课程
        async copyLesson (item) {
            // 如果 item 存在
            if (item && item.lessonId) {
                // 如果不是我的课程，先复制课程副本，再加载
                const newLesson = await LessonApi.copyAdaptLesson(item.lessonId)
                // 保存复制的课程
                this.$set(item, 'oldLessonId', item.lessonId)
                // 并为对应的 item 进行更新
                this.$set(item, 'lessonId', newLesson.id)
                this.$set(item, 'isCopyLesson', true)
            }
        },
        // 开始加载课程信息，包括第一个课程的生成以及其余课程的任务创建
        loadLesson () {
            // 监听第一个课程的生成任务完成事件
            this.listenLessonCompleted()
            // 开始调用创建任务的方法
            this.createBatchTask()
            // 开始展示对应的 Editor
            this.showLessonEditor()
        },
        // 设置对应的 itemId 的状态为 SUCCESS
        setLessonStatusCompleted (itemId) {
            // 如果 itemId 不存在，就直接返回
            if (!itemId) {
                return false
            }
            // 如果 item 存在，并且 this.$refs['lessonDetail' + itemId] 的长度大于 0
            if (!this.$refs['lessonDetail' + itemId] || this.$refs['lessonDetail' + itemId].length <= 0) {
                return false
            }
            // 拼接 itemId 和 lessonDetail
            const lessonDetail = this.$refs['lessonDetail' + itemId][0]
            // 由于发生了 lessonLibraryEditorIndexMounted 事件，说明当前课程已经加载完毕了
            // 那么就可以调用 lessonDetail 的生成 UDL 和 CLR 数据了
            // 如果 lessonDetail 存在，并且是批量生成数据
            if (lessonDetail) {
                // 修改生成的状态为开始
                const itemKey = itemId.trim().toUpperCase()
                // 判断生成状态是否已经生成完了
                const loading = lessonDetail.generateDataLoading()
                // 如果是 false，就说明内容生成完了
                if (!loading) {
                    // 设置给对应的 itemId 的状态为 SUCCESS
                    this.lessonStatus.set(itemKey, 'SUCCESS')
                    // 响应式的更新 lessonStatus
                    this.lessonStatus = new Map(this.lessonStatus)
                }
                return loading
            }
            return false
        },
        /**
         * 处理 Universal Design For Learning 生成完成事件
         * @param itemId
         */
        processUniversalDesignForLearningCompleted(itemId) {
            // 设置对应的 itemId 的状态为 SUCCESS
            const generateLoading = this.setLessonStatusCompleted(itemId)
            // 从 planLessons 找到对应的课程
            let planLesson = this.planLessons.find(lesson => lesson.itemId.toUpperCase() === itemId.toUpperCase())
            // 如果 planLesson 存在，那么就将对应的课程的 isAdaptedLesson 设置为 false
            if (planLesson) {
                // 第一个课程改编完成之后，将 initAdapted 设置为 true
                this.initAdapted = true
                planLesson.isAdaptedLesson = true
            }
            const lessonDetail = this.$refs['lessonDetail' + itemId][0]
            // 如果 lessonDetail 存在，通知 lessonDetail 更新生成状态
            if (lessonDetail) {
                lessonDetail.updateConvertLessonLoading(false)
            }
            // 如果 itemId 是第一个课程的 itemId，那么就说明第一个课程的生成任务完成了
            if (itemId.toUpperCase() === this.planLessonIds[0].itemId.toUpperCase()) {
                // 修改状态
                this.generateUniversalDesignForLearningCompleted = true
                // 当 UDL 完成的时候，如果 CLR 也完成了，那么就说明第一个课程的生成任务完成了
                if (this.generateCLRCompleted && this.queryBatchTaskInterval === null && !generateLoading) {
                    // 查询任务
                    this.queryBatchTask()
                }
            } else {
                // 修改状态
                this.generateUniversalDesignForLearningCompleted = true
                if (this.generateCLRCompleted) {
                    this.$message.success(this.$t('loc.batchAdaptLesson19'))
                }
            }
        },
        /**
         * 处理 CLR 生成完成事件
         * @param itemId
         */
        processCLRCompleted(itemId) {
            // 设置对应的 itemId 的状态为 SUCCESS
            const generateLoading = this.setLessonStatusCompleted(itemId)
            const lessonDetail = this.$refs['lessonDetail' + itemId][0]
            // 如果 lessonDetail 存在，通知 lessonDetail 更新生成状态
            if (lessonDetail) {
                lessonDetail.updateConvertLessonLoading(false)
            }
            // 如果 itemId 是第一个课程的 itemId，
            if (itemId.toUpperCase() === this.planLessonIds[0].itemId.toUpperCase()) {
                // 修改状态
                this.generateCLRCompleted = true
                // 当 CLR 完成的时候，如果 UDL 也完成了，那么就说明第一个课程的生成任务完成了
                if (this.generateUniversalDesignForLearningCompleted && this.queryBatchTaskInterval === null && !generateLoading) {
                  // 查询任务
                    this.queryBatchTask()
                }
            } else {
                // 修改状态
                this.generateCLRCompleted = true
                if (this.generateUniversalDesignForLearningCompleted) {
                    this.$message.success(this.$t('loc.batchAdaptLesson19'))
                }
            }
        },
        /**
         * 处理 Universal Design For Learning 开始生成的事件
         * @param itemId
         */
        processUniversalDesignForLearningStart(itemId) {
            // 修改生成的状态为开始
            const itemKey = itemId.trim().toUpperCase()
            // 设置状态
            this.lessonStatus.set(itemKey, 'PROCESSING')
            // 响应式的更新 lessonStatus
            this.lessonStatus = new Map(this.lessonStatus)
            this.planLessonIds = this.planLessons
            // 修改状态
            this.generateUniversalDesignForLearningCompleted = false
        },
        /**
         * 处理 CLR 生成开始事件
         * @param itemId
         */
        processCLRStart(itemId) {
            // 修改生成的状态为开始
            const itemKey = itemId.trim().toUpperCase()
            // 设置状态
            this.lessonStatus.set(itemKey, 'PROCESSING')
            // 响应式的更新 lessonStatus
            this.lessonStatus = new Map(this.lessonStatus)
            this.planLessonIds = this.planLessons
            // 修改状态
            this.generateCLRCompleted = false
        },
        // 监听课程的生成任务完成事件
        listenLessonCompleted() {
            // 初始化状态
            // 修改状态
            this.generateUniversalDesignForLearningCompleted = true
            this.generateCLRCompleted = true
            // 当 UDL 完成的时候会 this.$bus.$emit('generateUniversalDesignForLearningCompleted', this.itemId)
            // let generateUniversalDesignForLearningCompleted = false
            // 当 CLR 完成的时候会 this.$bus.$emit('generateCLRCompleted', this.lessonId)
            // let generateCLRCompleted = false
            // 如果两个都完成了，那么就说明第一个课程的生成任务完成了，此时执行查询任务的方法
            this.$bus.$on('generateUniversalDesignForLearningCompleted', this.processUniversalDesignForLearningCompleted)
            this.$bus.$on('generateCLRCompleted', this.processCLRCompleted)
            // 监听 generateUniversalDesignForLearningStart 事件
            this.$bus.$on('generateUniversalDesignForLearningStart', this.processUniversalDesignForLearningStart)
            // 监听 generateCLRStart 事件
            this.$bus.$on('generateCLRStart', this.processCLRStart)
        },
        // 更新 itemId 对应的那个 lessonDetail 的 UDL 和 CLR 生成状态
        updateUDLAndCLRGenerateStatus (itemId) {
            // 如果 itemId 不存在，就直接返回
            if (!itemId) {
                return
            }
            // 如果 item 存在，并且 this.$refs['lessonDetail' + itemId] 的长度大于 0
            if (!this.$refs['lessonDetail' + itemId] || this.$refs['lessonDetail' + itemId].length <= 0) {
                return
            }
            // 判断当前 itemId 对应的状态是不是 SUCCESS，如果不是 SUCCESS 就跳过
            if (this.lessonStatus.get(itemId.trim().toUpperCase()) !== 'SUCCESS') {
                return
            }
            // 拼接 itemId 和 lessonDetail
            const lessonDetail = this.$refs['lessonDetail' + itemId][0]
            // 由于发生了 lessonLibraryEditorIndexMounted 事件，说明当前课程已经加载完毕了
            // 那么就可以调用 lessonDetail 的生成 UDL 和 CLR 数据了
            // 如果 lessonDetail 存在，并且是批量生成数据
            if (lessonDetail && !this.singleEditLesson) {
                // 移动到指定位置
                lessonDetail.updateUDLAndCLRGenerateStatus()
            }
        },
        // 将当前课程跳转到对应的 UDL 和 CLR 位置
        scrollIntoUDLOrCLR (itemId) {
            // 如果 itemId 不存在，就直接返回
            if (!itemId || this.singleEditLesson) {
                return
            }
            // 如果 item 存在，并且 this.$refs['lessonDetail' + itemId] 的长度大于 0
            if (this.$refs['lessonDetail' + itemId].length <= 0) {
                return
            }
            // 拼接 itemId 和 lessonDetail
            const lessonDetail = this.$refs['lessonDetail' + itemId][0]
            // 由于发生了 lessonLibraryEditorIndexMounted 事件，说明当前课程已经加载完毕了
            // 那么就可以调用 lessonDetail 的生成 UDL 和 CLR 数据了
            // 如果 lessonDetail 存在，并且是批量生成数据
            if (lessonDetail && !this.singleEditLesson) {
                // 移动到指定位置
                lessonDetail.scrollIntoUDLOrCLR()
            }
        },
        // 开始展示对应的 Editor
        showLessonEditor () {
            // 判断是否是预览状态下进行的改编操作
            if (this.preview) {
              this.$analytics.sendEvent('web_weekly_plan_view_ba_exposure')
            }
            this.dialogVisible = true
            // 定义一个布尔值表示是否可以开始正常的生成数据了
            let lessonDetailCompleted = false
            let lessonLibraryUDLMounted = false
            let lessonLibraryCLRMounted = false
            // 定义一个变量表示只生成一次数据
            let generateData = false
            // 注册第一个课程的加载完成的回调，然后调用对应的 LessonDetail 开始生成第一个的课程的内容
            this.$bus.$on('lessonDetailCompleted', (itemId) => {
                // 那么就可以调用 lessonDetail 的生成 UDL 和 CLR 数据了
                lessonDetailCompleted = true
                // 如果 lessonDetailCompleted 和 lessonLibraryUDLMounted 和 lessonLibraryCLRMounted 都为 true，那么就说明第一个课程应该开始生成了
                if (lessonDetailCompleted && lessonLibraryUDLMounted && lessonLibraryCLRMounted && !generateData) {
                    this.generateUniversalDesignAndCLRData(itemId)
                    // 设置为 true，表示已经生成过数据了
                    generateData = true
                }
            })
            // 监听 lessonLibraryUDLMounted 和 lessonLibraryCLRMounted 事件，如果加载完成，就继续加载下面的课程
            this.$bus.$on('lessonLibraryUDLMounted', (itemId) => {
              if (!this.initEdit) {
                // 跳转到对应的 UDL 和 CLR 位置
                this.scrollIntoUDLOrCLR(itemId)
                // 跳转到指定位置之后，修改对应的生成状态
                this.updateUDLAndCLRGenerateStatus(itemId)
                lessonLibraryUDLMounted = true
                // 如果 lessonDetailCompleted 和 lessonLibraryUDLMounted 和 lessonLibraryCLRMounted 都为 true，那么就说明第一个课程应该开始生成了
                if (lessonDetailCompleted && lessonLibraryUDLMounted && lessonLibraryCLRMounted && !generateData) {
                  this.generateUniversalDesignAndCLRData(itemId)
                  // 设置为 true，表示已经生成过数据了
                  generateData = true
                }
              }
            })
            // 监听 lessonLibraryCLRMounted 事件，如果加载完成，就继续加载下面的课程
            this.$bus.$on('lessonLibraryCLRMounted', (itemId) => {
                if (!this.initEdit) {
                  // 跳转到对应的 UDL 和 CLR 位置
                  this.scrollIntoUDLOrCLR(itemId)
                  // 跳转到指定位置之后，修改对应的生成状态
                  this.updateUDLAndCLRGenerateStatus(itemId)
                  lessonLibraryCLRMounted = true
                  // 如果 lessonDetailCompleted 和 lessonLibraryUDLMounted 和 lessonLibraryCLRMounted 都为 true，那么就说明第一个课程应该开始生成了
                  if (lessonDetailCompleted && lessonLibraryUDLMounted && lessonLibraryCLRMounted && !generateData) {
                    this.generateUniversalDesignAndCLRData(itemId)
                    // 设置为 true，表示已经生成过数据了
                    generateData = true
                  }
                }
            })
        },
        // 发布全部课程时，更新周计划项
        updatePlanItems () {
            // 如果是批量改编，则需要从课程改编状态中对比一下课程是否已经改编成功
            if (this.generateClassSpecificValue) {
              // 更新之前先判断是否有课程内容发生了改动
              this.planLessons.forEach(lessonItem => {
                if (equalsIgnoreCase(this.lessonStatus.get(lessonItem.itemId.toUpperCase()), 'SUCCESS')) {
                  this.$set(lessonItem, 'isAdaptedLesson', true)
                }
              })
            }
            // 遍历所有的课程，将所有的新课程内容更新到周计划对应的周计划项中
            this.planLessons.forEach(item => {
                // 从已发布的课程中找到对应的课程
                let publishedLesson = this.savedLessons.find(lesson => lesson.itemId.toLowerCase() === item.itemId.toLowerCase())
                let lessonId = ''
                let lesson = {}
                let isAdaptedLesson = false
                // 如果课程内容有改动，则将整个课程同步到周计划项中，否则只更新周计划项的课程 ID 即可
                if (publishedLesson) {
                    lesson = {
                        id: publishedLesson.id,
                        name: publishedLesson.name,
                        coverMediaUrls: publishedLesson.coverMediaUrls || [],
                        measure: publishedLesson.measure,
                        authorId: this.currentUserId
                    }
                    isAdaptedLesson = publishedLesson.isAdaptedLesson
                } else {
                    lessonId = item.lessonId
                    isAdaptedLesson = item.isAdaptedLesson
                }
                this.$bus.$emit('updateAdaptedItem', { itemId: item.itemId, lesson: lesson, lessonId: lessonId, isAdaptedLesson: isAdaptedLesson })
            })
        },
        // 生成 UDL 和 CLR 的数据
        generateUniversalDesignAndCLRData (itemId) {
            // console.log('generateUniversalDesignAndCLRData', itemId)
            this.$nextTick(() => {
                // 先判断 itemId 是不是第一个课程的 itemId，如果不是则直接返回
                // 当课程加载完毕之后，将当前课程跳转到对应的 UDL 和 CLR 位置
                // 拼接 itemId 和 lessonDetail
                if (!this.$refs['lessonDetail' + itemId] || this.$refs['lessonDetail' + itemId].length === 0) {
                    return
                }
                const lessonDetail = this.$refs['lessonDetail' + itemId][0]
                // 跳转到对应的 UDL 和 CLR 位置
                this.scrollIntoUDLOrCLR(itemId)
                // 如果不是第一个 ItemId 就不自动生成内容
                if (itemId !== this.planLessonIds[0].itemId && !this.singleAdaptLesson) {
                    return
                }
                // 由于发生了 lessonLibraryEditorIndexMounted 事件，说明当前课程已经加载完毕了
                // 那么就可以调用 lessonDetail 的生成 UDL 和 CLR 数据了
                // 如果 lessonDetail 存在，并且是批量生成数据
                if (lessonDetail && (this.generateClassSpecificValue || this.singleAdaptLesson)) {
                    // 为当前课程生成数据
                    lessonDetail.generateUniversalDesignAndCLRData()
                }
            })
        },
        // 更改课程
        async changeLesson (lesson) {
            // 如果 loading 加载中，直接返回
            if (this.loading) {
                // 修改 lessonMenu 的当前课程
                const lessonMenu = this.$refs['lessonMenu']
                // 校验失败了，重新恢复原有的课程
                lessonMenu.activeLesson = lessonMenu.lastActiveLesson
                return
            }
            // 接下来要发布调用接口，所以此时添加 loading
            this.loading = true
            // 课程发生了改变，先校验当前的课程是否符合标准
            const lessonDetail = this.$refs['lessonDetail' + this.currentItemId][0]
            if (!lessonDetail) {
                // 发布成功解除 loading
                this.loading = false
                return
            }
            // 获取校验的结果
            const validated = await lessonDetail.validateLesson()
            // 如果校验成功，并且课程存在
            // 如果课程存在
            if (validated && lesson && lesson.itemId) {
                // 关闭自定义模板引导，K12 没有此功能，暂时隐藏
                // lessonDetail.closeCustomTemplateGuide()
                // 如果是编辑课程时则需要判断当前课程是否发生改变，如果改变则需要 publish 后才允许切换
                if (this.singleEditLesson || this.singleAdaptLesson) {
                    const hasUpdated = lessonDetail.hasUpdated()
                    // 如果有更新，则先保存课程
                    if (hasUpdated) {
                        let itemLesson = await lessonDetail.publishLesson()
                        // 将最新的课程信息放入 savedLessons 中
                        itemLesson.itemId = this.currentItemId
                        this.addSavedLesson(itemLesson)
                        this.updatePlanItems()
                        // 设置已经发布过了
                        this.currentPublished.set(this.currentItemId, true)
                    }
                    // 如果单个编辑或者单个改编，此时课程没有发生过任何的改变，想要继续操作，
                    // 那么后续如果是第一次执行，一定会添加一个课程，那么此时的课程则需要移除
                    // 如果是第一次执行，那么就需要移除当前课程
                    this.cancelLesson()
                }
                if (!this.singleEditLesson && !this.singleAdaptLesson) {
                    // 课程参数校验成功后，选中时，为当前课程添加 selected 属性，表示当前课程被选中
                    this.$set(lesson, 'selected', true)
                    // 如果校验成功，那么就直接将课程进行发布
                    let itemLesson = await lessonDetail.publishLesson()
                    // 如果 itemLesson 不存在，直接返回
                    if (!itemLesson) {
                        // 修改 lessonMenu 的当前课程
                        const lessonMenu = this.$refs['lessonMenu']
                        // 校验失败了，重新恢复原有的课程
                        lessonMenu.activeLesson = lessonMenu.lastActiveLesson
                        // 发布成功解除 loading
                        this.loading = false
                        return
                    }
                    // 将最新的课程信息放入 savedLessons 中
                    itemLesson.itemId = this.currentItemId
                    this.addSavedLesson(itemLesson)
                }
                // 如果是倒数第二个课程，并且已经通过保存校验，那么就不显示下一步按钮
                if ((this.singleEditLesson || this.singleAdaptLesson) && this.secondToLast) {
                  this.showNext = false
                }
                // 切换课程成功后，将当前课程的 Adapt 状态设置为 false
                this.currentLessonIsProcessing = false
                // 将当前课程的 Id 替换成新的课程的 Id
                this.currentItemId = lesson.itemId
                // 推荐到机构课程库重新设置为 true
                this.recommendToAgency = true
                // 由于课程发生了改变，那么就需要重新加载课程
                // 切割出需要加载的课程
                const lessonsToLoad = this.planLessonIds.find(planLesson => planLesson.itemId.toUpperCase() === lesson.itemId.toUpperCase())
                // 如果有课程需要加载
                if (lessonsToLoad) {
                    // 判断课程是否已经被加载过了 lessonsToLoad
                    if (this.loadLessons.find(loadLesson => loadLesson.itemId.toUpperCase() === lessonsToLoad.itemId.toUpperCase())) {
                        if (!this.singleEditLesson) {
                            // 如果当前课程是不需要加载的，那么直接跳转到对应的位置
                            this.$nextTick(() => {
                                // 跳转到对应的 UDL 和 CLR 位置
                                this.scrollIntoUDLOrCLR(lessonsToLoad.itemId)
                            })
                        }
                        // 发布成功解除 loading
                        this.loading = false
                        // 课程恢复
                        this.recoverLesson()
                        return
                    }
                    // 为 loadLessons 赋值
                    await this.addLoadLesson(lessonsToLoad)
                    // 课程恢复
                    this.recoverLesson()
                }
            } else {
                // 修改 lessonMenu 的当前课程
                const lessonMenu = this.$refs['lessonMenu']
                // 校验失败了，重新恢复原有的课程
                lessonMenu.activeLesson = lessonMenu.lastActiveLesson
            }
            // 发布成功解除 loading
            this.loading = false
        },
        // 当关闭弹窗的时候的回调
        cancelEditLesson () {
            // 如果存在课程正在加载
            // 从 lessonStatus 中获取加载状态，判断是否存在 value 为 PROCESSING 的状态
            const processing = Array.from(this.lessonStatus.values()).some(status => status === 'PROCESSING')
            // 如果存在正在加载的课程，那么就弹出新的弹窗
            if (processing) {
                this.$confirm(this.$t('loc.batchAdaptLesson8'),
                    this.$t('loc.confirmation'), {
                        confirmButtonText: this.$t('loc.batchAdaptLesson9'),
                        cancelButtonText: this.$t('loc.cancel'),
                        showClose: true,
                        closeOnClickModal: false,
                        customClass: 'plan-message-box',
                        confirmButtonClass: 'el-button--danger',
                        distinguishCancelAndClose: true,
                        callback: async action => {
                            if (action === 'confirm') {
                                // 判断用户是否是批量进来的
                                if (this.generateClassSpecificValue) {
                                    // 调用后台的取消保存接口
                                    this.batchRemoveLesson()
                                } else { // 正常进入的，那么此时取消保存只是取消掉当前现在改变的课程
                                    this.cancelLesson()
                                }
                                this.dialogVisible = false
                                this.clearOldData()
                                this.judgeIsOpenAddChildrenTip()
                                // 如果是 Unit 详情内，取消保存需要删除当前的 Unit 并且跳转到源单元
                                await this.backOriginUnit()
                            } else if (action === 'cancel') {
                            } else if (action === 'close') {
                            }
                        }
                    })
            } else {
                // 此时判断当前课程是否修改过
                const lessonDetail = this.$refs['lessonDetail' + this.currentItemId][0]
                // 获取课程是否被改变过标志
                const hasUpdated = lessonDetail.hasUpdated()
                // 判断是不是批量编辑
                if (this.singleEditLesson || this.singleAdaptLesson) {
                    // 如果没有发生过改变，那么就直接返回
                    if (!hasUpdated) {
                        // 获取当前 unitId
                        const unitId = this.$route.query.unitId
                        // 如果是在 unit planer 中操作课程，增加单元的操作记录
                        if (unitId) {
                            Lessons2.addAction({ unitId: unitId })
                            .catch(err => {
                            })
                        }
                        this.dialogVisible = false
                        // 如果此前没有发布过，那么就删除，否则就不删除
                        this.cancelLesson()
                        this.clearOldData()
                        return
                    }
                }
                this.$confirm(this.$t('loc.batchAdaptLesson2'),
                    this.$t('loc.confirmation'), {
                        confirmButtonText: this.$t('loc.save'),
                        cancelButtonText: this.$t('loc.noSave'),
                        showClose: true,
                        closeOnClickModal: false,
                        distinguishCancelAndClose: true,
                        callback: async action => {
                            if (action === 'confirm') {
                                if (this.preview) {
                                  // 点击保存全部按钮埋点
                                  this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_ada_pu')
                                  // 点击保存全部按钮埋点
                                  this.$analytics.sendEvent('web_weekly_plan_view_ba_click_save_all')
                                } else {
                                  // 点击保存全部按钮埋点
                                  this.$analytics.sendEvent('web_weekly_plan_edit_ba_click_save_all')
                                }
                                // 如果是单个编辑或单个改编时，则需要触发单个发布接口
                                if (this.singleEditLesson || this.singleAdaptLesson) {
                                  this.currentPublishedCopy.set(this.currentItemId, true)
                                  await this.saveCurrentLesson()
                                } else {
                                  // 调用后台的保存接口
                                  const validated = await this.batchSaveLesson()
                                  // 如果校验失败直接返回
                                  if (!validated) {
                                    return
                                  }
                                }
                                // 获取当前 unitId
                                const unitId = this.$route.query.unitId
                                // 如果是 unit planer 部分的操作
                                if (hasUpdated && unitId) {
                                  await Lessons2.addAction({ unitId: unitId })
                                    .catch(err => {
                                    })
                                }
                                this.dialogVisible = false
                                // 清空 loadLesson 和 currentItemId
                                this.clearOldData()
                                // 取消编辑后刷新组件
                                this.$emit('refreshComponent')
                                this.judgeIsOpenAddChildrenTip()
                            } else if (action === 'cancel') {
                                if (this.preview) {
                                  this.$analytics.sendEvent('web_weekly_plan_view_ba_click_not_save')
                                } else {
                                  // 点击不保存按钮埋点
                                  this.$analytics.sendEvent('web_weekly_plan_edit_ba_click_not_save')
                                }
                                // 判断用户是否是批量进来的
                                if (this.generateClassSpecificValue) {
                                    // 调用后台的取消保存接口
                                    this.batchRemoveLesson()
                                } else { // 正常进入的，那么此时取消保存只是取消掉当前现在改变的课程
                                    this.cancelLesson()
                                }
                                // 如果是 Unit 详情内，取消保存需要删除当前的 Unit 并且跳转到源单元
                                await this.backOriginUnit()
                                this.dialogVisible = false
                                this.clearOldData()
                            } else if (action === 'close') {
                            }
                        }
                    })
            }
        },
        // 回到源单元
        async backOriginUnit () {
          // 如果是 Unit 详情内，取消保存需要删除当前的 Unit 并且跳转到源单元
          const originUnitId = this.originUnitId
          if (originUnitId) {
            // 清除 vuex 中的源单元 ID
            this.$store.dispatch('unit/setOriginUnitId', '')
            const routerName = this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail'
            // 跳转回源单元
            this.$router.push({
              name: routerName,
              query: {
                unitId: originUnitId
              }
            })
          }
        },
        // 创建批量执行的任务
        createBatchTask () {
            // 判断是否是批量进来的，如果是才创建任务
            if (!this.generateClassSpecificValue) {
                return
            }
            // adaptItems 是除去第一个课程之外的所有的课程，一开始第一个课程的 Id 指的是 currentItemId
            const adaptItems = this.lessonInfo
                .filter(lesson => lesson.itemId !== this.currentItemId)
                .map(lesson => {
                    return {
                        lessonId: lesson.lessonId,
                        itemId: lesson.itemId
                    }
                })
            // 判断是否存在 adaptItems
            if (adaptItems.length === 0) {
                // 只有一个课程这个时候不需要创建任务，直接返回
                return
            }
            // 批量请求发送的时候，将剩余所有的 item 进行 loading
            // 设置当前 itemId 为 PROCESSING
            this.lessonStatus.set(this.currentItemId.trim().toUpperCase(), 'PROCESSING')
            // 批量将其他任务都设置为 PROCESSING
            adaptItems.forEach(item => {
                if (this.singleAdaptLesson) {
                    if (this.currentItemId.trim().toUpperCase() === item.trim().toUpperCase()) {
                        // 修改生成的状态为开始
                        const itemKey = item.itemId.trim().toUpperCase()
                        // 设置状态
                        this.lessonStatus.set(itemKey, 'PROCESSING')
                    }
                } else {
                    // 修改生成的状态为开始
                    const itemKey = item.itemId.trim().toUpperCase()
                    // 设置状态
                    this.lessonStatus.set(itemKey, 'PROCESSING')
                }
                // 从 planLessons 找到对应的课程
                let planLesson = this.planLessons.find(lesson => lesson.itemId.toUpperCase() === item.itemId.toUpperCase())
                // 如果 planLesson 存在，那么就将对应的课程的 isAdaptedLesson 设置为 false
                if (planLesson) {
                    planLesson.isAdaptedLesson = false
                }
            })
            // 处理完毕后，将 planLessonIds 重新赋值
            this.planLessonIds = this.planLessons
            // 响应式的更新 lessonStatus
            this.lessonStatus = new Map(this.lessonStatus)
            // 获取当前班级 Id
            const selectedGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
            // 定义参数
            let param = {
                planId: this.planId,
                groupId: selectedGroupId,
                adaptItems: adaptItems
            }
            // 调用接口
            this.$axios.post($api.urls().createBatchAdaptLessonTask, param)
                .then(res => {
                    // 获取到 batchId
                    this.batchId = res.id
                })
                .catch(error => {
                    this.$message.error(error.response.data.error_message)
                })
        },
        // 查询批量任务
        queryBatchTask () {
            // 参数校验，如果没有 batchId 就不进行数据获取
            if (!this.batchId) {
                // 如果 batchId 不存在，但是其实是因为被清理掉了
                if (!this.dialogVisible) {
                    // 则直接返回
                    return
                }
                // 没有 batchId，但是查询了任务，说明前面的任务已经结束了，这里直接提示即可
                if (!this.taskCompleted) {
                    // 提示改编完成
                    this.$message.success(this.$t('loc.batchAdaptLesson19'))
                    // 设置任务完成
                    this.taskCompleted = true
                }
                return
            }
            // 定义变量为全部都完成
            let allCompleted = false
            // 定义变量表示已经存在有部分任务完成
            let someCompleted = false
            // 定义变量表示存在失败的任务
            let hasFailed = false
            // 定义一个变量表示要处理的课程
            let processingLesson = []
            // 定义一个变量表示已经处理的课程
            let processedLesson = []
            // 判断方法是否正在执行或者准备执行
            let hasTimerExecuted = false
            // 定义重试次数
            let retryCount = 0
            // 处理任务的方法定义
            const processBatchTaskFunc = () => {
                // 首先先判断任务是否已经在执行了，或者已经存在 setTimeout 了
                if (hasTimerExecuted || !this.batchId) {
                    return
                }
                hasTimerExecuted = true
                this.$axios.get($api.urls().queryBatchAdaptLessonTask, {
                    params: {
                        batchId: this.batchId
                    }
                })
                    .then(res => {
                        // 请求发送完成之后设置为 false
                        hasTimerExecuted = false
                        // 如果此時 batchId 已经被清理了，那么直接返回
                        if (!this.batchId || !this.dialogVisible) {
                            return
                        }
                        // 如果有任务的状态是 FAIL, 则说明存在失败的任务
                        hasFailed = res.tasks.some(task => task.status === 'FAIL')
                        // 如果有失败的，弹出弹窗
                        if (hasFailed) {
                            // 调用方法去处理失败的任务
                            this.processFailedTask(res.tasks, processBatchTaskFunc)
                            // 任务失败直接返回
                            return
                        }
                        // res.tasks 是所有的任务，当所有的任务的 status 都是 SUCCESS 的时候，那么就说明所有的任务都完成了
                        allCompleted = res.completed && res.tasks.every(task => task.status === 'SUCCESS')
                        // 当有部分任务的状态是 SUCCESS 的时候，并且任务不在 processedLesson 中
                        const successTasks = res.tasks.filter(task => task.status === 'SUCCESS')
                        // 定义 needProcess 的状态
                        let needProcess = false
                        // 循环遍历所有的成功的任务，如果有一个任务的 itemId 不在 processedLesson 中，那么就说明存在没有处理的课程
                        successTasks.forEach(task => {
                            if (!processedLesson.find(processed => equalsIgnoreCase(processed.itemId, task.itemId))) {
                                // 如果有一个任务的 itemId 不在 processedLesson 中，那么就说明存在没有处理的课程
                                processingLesson.push(task)
                                // 设置需要处理
                                needProcess = true
                            }
                        })
                        someCompleted = res.tasks.some(task => task.status === 'SUCCESS') && needProcess
                        // 如果任务完成了，那么就清除定时器
                        if (allCompleted) {
                            // 任务完成了，在判断一下是否还存在没有处理的课程
                            // 将所有成功的任务放入 processedLesson 中
                            processingLesson.push(...res.tasks.filter(task => task.status === 'SUCCESS'))
                            // 过滤掉重复的课程
                            processingLesson = processingLesson.filter((lesson, index, self) => self.findIndex(t => t.itemId === lesson.itemId) === index)
                            // 如果 processingLesson 中存在一个 itemId 是 processedLesson 中没有的，那么说明还有没有处理的课程，这个时候进行处理
                            if (processingLesson.some(processing => !processedLesson.find(processed => processing.itemId === processed.itemId))) {
                                // 如果存在没有处理的课程，那么就继续加载课程
                                this.processLesson(processingLesson)
                                // 处理过后，将课程添加进入 processedLesson 中
                                processedLesson.push(...processingLesson)
                                // 将课程从 processingLesson 中移除
                                processingLesson = []
                            }
                            // 如果任务没有完成，那么此时任务算作完成
                            if (!this.taskCompleted && this.batchId && this.dialogVisible) {
                                // 提示改编完成
                                this.$message.success(this.$t('loc.batchAdaptLesson3'))
                                this.$nextTick(() => {
                                    // 将 this.planLessons 中所有的 itemId 对应的状态全部修改为 success
                                    this.planLessonIds.forEach(planLesson => {
                                        // 修改生成的状态为开始
                                        const itemKey = planLesson.itemId.trim().toUpperCase()
                                        // 设置状态
                                        this.lessonStatus.set(itemKey, 'SUCCESS')
                                    })
                                    // 响应式处理 lessonStatus
                                    this.lessonStatus = new Map(this.lessonStatus)
                                })
                                // 设置任务完成
                                this.taskCompleted = true
                            }
                            // 所有都成功结束掉任务
                            clearTimeout(this.queryBatchTaskInterval)
                        } else if (someCompleted) {
                            // 取到 PROCESSING 的任务，修改状态为 loading
                            res.tasks.filter(task => task.status === 'PROCESSING').map(task => task.itemId).forEach(itemId => {
                                // 修改生成的状态为开始
                                const itemKey = itemId.trim().toUpperCase()
                                // 设置状态
                                this.lessonStatus.set(itemKey, 'PROCESSING')
                            })
                            // 响应式处理 lessonStatus
                            this.lessonStatus = new Map(this.lessonStatus)
                            // 过滤掉重复的课程
                            processingLesson = processingLesson.filter((lesson, index, self) => self.findIndex(t => t.itemId === lesson.itemId) === index)
                            // 如果 processedLesson 的长度大于 0，那么就说明存在已经处理的课程
                            if (processingLesson.length > 0) {
                                // 如果存在已经处理的课程，那么就继续加载课程
                                this.processLesson(processingLesson)
                                // 处理过后，将课程添加进入 processedLesson 中
                                processedLesson.push(...processingLesson)
                                // 将课程从 processingLesson 中移除
                                processingLesson = []
                            }
                            // 重新执行任务
                            this.queryBatchTaskInterval = setTimeout(processBatchTaskFunc, 3000)
                        } else {
                            // 如果还存在部分 PENDING 的任务，那么此时直接进行再次调用
                            const hasPendingTasks = res.tasks.some(task => task.status === 'PENDING')
                            // 如果存在
                            if (hasPendingTasks) {
                                this.retryPendingTasks(res.tasks, 'PENDING')
                            }
                            // 取到 PROCESSING 的任务，修改状态为 loading
                            res.tasks.filter(task => task.status === 'PROCESSING').map(task => task.itemId).forEach(itemId => {
                                // 修改生成的状态为开始
                                const itemKey = itemId.trim().toUpperCase()
                                // 设置状态
                                this.lessonStatus.set(itemKey, 'PROCESSING')
                            })
                            // 响应式处理 lessonStatus
                            this.lessonStatus = new Map(this.lessonStatus)
                            // 重新执行任务
                            this.queryBatchTaskInterval = setTimeout(processBatchTaskFunc, 3000)
                        }
                    })
                    .catch(error => {
                        // 如果发生了错误，则再次发送请求，最多尝试 3 次
                        if (retryCount >= 3) {
                            // 如果超过了次数，那么如果还存在在 planLesson 中的课程，但是不在 loadLesson 中的课程的状态全部修改为 FAIL
                            this.planLessonIds.forEach(planLesson => {
                                // 如果 lesson 不在 loadLesson 中，那么就将对应的 ItemId 的加载状态修改为 FAIL
                                if (!this.loadLessons.find(loadLesson => loadLesson.itemId === planLesson.itemId)) {
                                    // 修改生成的状态为开始
                                    const itemKey = planLesson.itemId.trim().toUpperCase()
                                    // 设置状态
                                    this.lessonStatus.set(itemKey, 'FAIL')
                                }
                            })
                            // 响应式处理 lessonStatus
                            this.lessonStatus = new Map(this.lessonStatus)
                            return
                        }
                        // 次数加 1
                        retryCount += 1
                        // 重新执行任务
                        this.queryBatchTaskInterval = setTimeout(processBatchTaskFunc, 3000)
                        // 如果存在错误信息
                        if (error.response) {
                            this.$message.error(error.response.data.error_message)
                        }
                    })
            }
            // 使用 get 方法通过 batchId 去查询批量执行的任务的状态，每隔三秒执行一次
            processBatchTaskFunc()
        },
        // 处理失败的任务
        processFailedTask (tasks, processBatchTaskFunc) {
            // 弹出弹窗
            this.$confirm(this.$t('loc.batchAdaptLesson11'),
                this.$t('loc.batchAdaptLesson10'), {
                    confirmButtonText: this.$t('loc.batchAdaptLesson12'),
                    showClose: false,
                    type: 'warning',
                    closeOnClickModal: false,
                    customClass: 'plan-message-box',
                    callback: async action => {
                        if (action === 'confirm') {
                            // 重新调用执行任务的方法
                            let failTaskIds = tasks.filter(task => task.status === 'FAIL').map(task => task.taskId)
                            // 如果确实存在失败的任务
                            if (failTaskIds.length > 0) {
                                failTaskIds = failTaskIds.join(',')
                            }
                            // 清除定时器
                            clearTimeout(this.queryBatchTaskInterval)
                            // 如果 failTaskIds 存在
                            if (failTaskIds.replace(',', '') !== '') {
                                LessonApi.retryFailedTasks(failTaskIds)
                                    .then(res => {
                                        // 重新执行任务
                                        this.queryBatchTaskInterval = setTimeout(processBatchTaskFunc, 3000)
                                    })
                                    .catch(error => {
                                        this.$message.error(error.response.data.error_message)
                                    })
                            }
                        } else if (action === 'cancel') {
                            // 如果取消了，则直接将对应的 ItemId 的加载状态修改为 FAIL
                            tasks.forEach(task => {
                                // 修改生成的状态为开始
                                const itemKey = task.itemId.trim().toUpperCase()
                                // 设置状态
                                this.lessonStatus.set(itemKey, 'FAIL')
                            })
                            // 响应式处理 lessonStatus
                            this.lessonStatus = new Map(this.lessonStatus)
                        }
                    }
                })
        },
        // 开始处理那些需要处理的课程
        processLesson (processedLesson) {
            // 循环遍历 processedLesson
            processedLesson.forEach(lesson => {
                // 按照 itemId 替换掉对应的 lessonId
                this.planLessons.forEach(planLesson => {
                    // 如果 planLesson.itemId === lesson.itemId 并且 planLesson.lessonId !== lesson.lessonId
                    if (planLesson.itemId === lesson.itemId) {
                        // 将 lesson.lessonId 替换掉 planLesson.lessonId
                        if (planLesson.lessonId !== lesson.lessonId) {
                            planLesson.lessonId = lesson.lessonId
                        }
                        // 判断是不是 center 课程
                        if (lesson.isCenterLesson) {
                            // 如果是 center 课程，那么就将 centerLessonId 替换掉 lesson.lessonId
                            planLesson.centerName = lesson.centerName
                        } else {
                            // 修改对应的 lessonTitle
                            planLesson.lessonTitle = lesson.lessonTitle
                        }
                        // 修改 day
                        planLesson.day = lesson.day
                        // 将 planLesson 的 isAdaptedLesson 设置为 true
                        planLesson.isAdaptedLesson = true
                        // 将 planLesson 的 lessonAuthorId 设置为当前用户的 user_id
                        planLesson.lessonAuthorId = this.currentUserId
                    }
                })
            })
            // 处理完毕后，将 planLessonIds 重新赋值
            this.planLessonIds = this.planLessons
            // 将 this.planLessonIds 里面所有的 isAdaptedLesson 为 true 的，并且不在 loadLesson 中的课程放入 loadLesson 中，不在的逻辑应该是 itemId 不在 loadLesson 中
            // 对 this.planLessonIds 进行过滤，得到需要加载的课程
            const needLoadLessons = this.planLessonIds.filter(planLesson => {
                return planLesson.isAdaptedLesson && !this.loadLessons.find(lesson => lesson.itemId === planLesson.itemId)
            })
            // 批量修改 needLoadLessons 的 itemId 在 lessonStatus 中的状态为 SUCCESS
            needLoadLessons.forEach(lesson => {
                // 修改生成的状态为开始
                const itemKey = lesson.itemId.trim().toUpperCase()
                // 设置状态
                this.lessonStatus.set(itemKey, 'SUCCESS')
            })
            // 响应式的更新 lessonStatus
            this.lessonStatus = new Map(this.lessonStatus)
        },
        // 重新加载指定的 task
        retryPendingTasks (tasks, status) {
            // 重新调用执行任务的方法
            let failTaskIds = tasks.filter(task => task.status === status).map(task => task.taskId)
            // 如果确实存在失败的任务
            if (failTaskIds.length > 0) {
                failTaskIds = failTaskIds.join(',')
            }
            // 如果 failTaskIds 存在
            if (failTaskIds.replace(',', '') !== '') {
                LessonApi.retryFailedTasks(failTaskIds)
            }
        },
        // 非改变的情况下的批量取消保存课程
        cancelLesson () {
            // 从 this.loadLessons 中找到 itemId 为 currentItemId 的课程
            const currentLesson = this.loadLessons.find(lesson => lesson.itemId === this.currentItemId)
            // 如果是拷贝的课程，那么就调用对应的移除接口
            // 判断课程是否发布过
            let published = this.currentPublished.get(this.currentItemId)
            if (currentLesson.isCopyLesson && !published) {
              // 调用对应的移除接口
              LessonApi.deleteLessonPermanently(currentLesson.lessonId)
                .then(res => {
                })
                .catch(error => {
                  // 取消保存失败
                  this.$message.error(error.response.data.error_message)
                })
            }
        },
        // 恢复课程
        recoverLesson () {
          // 从 this.loadLessons 中找到 itemId 为 currentItemId 的课程
          const currentLesson = this.loadLessons.find(lesson => lesson.itemId === this.currentItemId)
          // 调用对应的移除接口，因为 updatePlanItems 会将可能删除的课程 ID 复制给原有的 plans 中
          LessonApi.recoverDeletedLesson(currentLesson.lessonId)
        },
        // 批量取消保存课程
        batchRemoveLesson () {
            // 批量取消的时候需要得到第一个课程 Id，因为第一个课程不是后台任务生成的
            const firstLessonId = this.planLessonIds[0].lessonId
            // 获取 batchId
            if (this.batchId && firstLessonId) {
                const params = {
                  batchId: this.batchId,
                  clearLessonIds: [firstLessonId]
                }
                // 调用对应的移除接口
                this.$axios.post($api.urls().batchRemoveAdaptedLesson, params)
                    .then(res => {
                    })
                    .catch(error => {
                        // 取消保存失败
                        this.$message.error(error.response.data.error_message)
                    })
            }
        },
        // 点击保存全部课程的时候
        async saveAllLesson () {
            if (this.preview) {
              // 点击保存全部按钮埋点
              this.$analytics.sendEvent('web_weekly_plan_view_ba_click_save_all')
            } else {
              // 点击保存全部按钮埋点
              this.$analytics.sendEvent('web_weekly_plan_edit_ba_click_save_all')
            }
            // 调用后台的保存接口
            const validated = await this.batchSaveLesson()
            // 如果校验失败直接返回
            if (!validated) {
                return
            }
            this.dialogVisible = false
            // 清空 loadLesson 和 currentItemId
            this.clearOldData()
            // 取消编辑后刷新组件
            this.$emit('refreshComponent')
            this.judgeIsOpenAddChildrenTip()
            // 关闭编辑弹窗，解锁当前周计划
            if (this.preview) {
              LessonApi.unlockEditing({
                id: this.planId
              })
            }
        },
        // 批量保存课程
        async batchSaveLesson () {
            // 调用保存接口的时候需要准备对应的参数
            // 获取当前的课程详情
            const lessonDetail = this.$refs['lessonDetail' + this.currentItemId][0]
            // 先进行必填项校验
            const validated = await lessonDetail.validateLesson()
            // 如果校验失败，那么就直接返回
            if (!validated) {
                return false
            }
            // 调用对应的课程进行填充数据
            lessonDetail.fillUDLAndCLRData()
            // 获取对应的数据
            let lessonData = lessonDetail.collectData()
            // 从 lessonStatus 中获取所有失败的任务
            const failedTasks = Array.from(this.lessonStatus.entries()).filter(([key, value]) => value === 'FAIL')
            // 此时 lessonData 就是当前课程的数据
            // unmodifiedLessonIds 中过滤掉已经存在的失败的任务
            const unmodifiedLessonIds = this.planLessonIds
                .filter(planLesson => planLesson.itemId !== this.currentItemId)
                .filter(planLesson => !failedTasks.find(itemEntry => itemEntry[0] === planLesson.itemId.trim().toUpperCase()))
                .map(planLesson => planLesson.lessonId)
            // 判断是否存在 unmodifiedLessonIds
            // 如果 unmodifiedLessonIds 不存在，但是 lessonData 是存在的，说明只有一个课程，那么此时不在调用批量接口，而是调用单个保存的接口
            if (unmodifiedLessonIds.length === 0 && lessonData) {
                // 调用对应的课程进行发布，并且在保存完成之后弹出提示
                try {
                    let itemLesson = await lessonDetail.publishLesson()
                    // 将最新的课程信息放入 savedLessons 中
                    itemLesson.itemId = this.currentItemId
                    this.addSavedLesson(itemLesson)
                    this.$message.success(this.$t('loc.lesson2Publish'))
                    // 更新周计划项
                    this.updatePlanItems()
                } catch (error) {
                    this.$message.error(error.response.data.error_message)
                }
                // 请求发送之后，返回 false
                return true
            }
            // 符合批量保存的逻辑进行批量保存
            const param = {
                lessonIds: unmodifiedLessonIds, // 当前的可能发生了变化，所以当前的课程不属于未修改的课程
                lessonPublishRequest: lessonData // 当前课程的详细信息
            }
            // 调用接口进行数据保存
            await this.$axios.post($api.urls().batchPublishAdaptedLesson, param)
                .then(res => {
                    // 保存成功
                    this.$message.success(this.$t('loc.lesson2Publish'))
                    let itemLesson = lessonDetail.getWeekPlanLessonDetail()
                    // 将最新的课程信息放入 savedLessons 中
                    itemLesson.itemId = this.currentItemId
                    this.addSavedLesson(itemLesson)
                    // 更新周计划项
                    this.updatePlanItems()
                })
                .catch(error => {
                    // 保存失败
                    this.$message.error(error.response.data.error_message)
                })
            // 请求发送之后，返回 true
            return true
        },

        // 将新的课程放入 loadLessons 中
        addSavedLesson (lesson) {
            // 如果 savedLessons 中存在当前的课程，那么就替换掉，否则就直接 push 进去
            const index = this.savedLessons.findIndex(item => item.itemId.toLowerCase() === lesson.itemId.toLowerCase())
            if (index !== -1) {
                // 如果lesson已经存在，则更新
                this.savedLessons[index] = lesson
            } else {
                // 否则，将其添加到列表中
                this.savedLessons.push(lesson)
            }
        },

        // 清除旧数据
        clearOldData () {
            // 清理要加载的课程集合
            this.loadLessons = []
            // 清理掉当前的 ItemId
            this.currentItemId = ''
            // 清除掉当前加载课程的 count
            this.loadedLessonCount = 0
            // 清理掉 batchId
            this.batchId = undefined
            // 清理掉小孩数据
            this.$set(this, 'children', [])
            // 清理 lessonStatus
            this.lessonStatus = new Map()
            // 清理完成状态
            this.taskCompleted = false
            this.currentPublishedCopy = new Map([...this.currentPublishedCopy, ...this.currentPublished])
            // 清理旧数据，标识当前课程是否已经发布过
            this.currentPublished = new Map()
            // 每次清理数据要清理自己的
            // 清除定时器
            clearTimeout(this.queryBatchTaskInterval)
            // 将 this.queryBatchTaskInterval 置位 null
            this.queryBatchTaskInterval = null
            // 设置单个 Adapt 课程的状态为 false
            this.singleAdaptLesson = false
            // 注销 bus 事件
            this.$bus.$off('updateSelectedGroupId')
            this.$bus.$off('generateUniversalDesignForLearningCompleted')
            this.$bus.$off('generateCLRCompleted')
            this.$bus.$off('generateUniversalDesignForLearningStart')
            this.$bus.$off('generateCLRStart')
            this.$bus.$off('lessonDetailCompleted')
            this.$bus.$off('lessonLibraryUDLMounted')
            this.$bus.$off('lessonLibraryCLRMounted')
        },
        // 保存当前的课程
        async saveCurrentLesson (publish) {
            // 点击发布课程按钮埋点
            if (publish) {
                this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_publish')
            }
            // 开始保存 Loading
            this.saveLoading = true
            // 通过调用 lessonDetail 的保存接口来保存当前的课程
            const lessonDetail = this.$refs['lessonDetail' + this.currentItemId][0]
            // 调用对应的课程进行发布
            try {
                // 获取当前课程的信息
                const currentLesson = this.planLessonIds.filter(lesson => equalsIgnoreCase(lesson.itemId, this.currentItemId))
                // 点击发布时，如果是 AdaptedLesson，那么就不需要推荐给机构
                if ((currentLesson && currentLesson[0].isAdaptedLesson) || this.isAdmin || !this.recommendToAgencyOpen) {
                    this.recommendToAgency = false
                }
                // 如果用户是 educator，不显示推荐课程的复选框
                if (!this.open.educator) {
                    lessonDetail.showRecommendCheckbox = true
                }
                lessonDetail.recommendToAgency = this.recommendToAgency
                let itemLesson = await lessonDetail.publishLesson()
                // 设置已经发布过了
                this.currentPublished.set(this.currentItemId, true)
                // 将最新的课程信息放入 savedLessons 中
                itemLesson.itemId = this.currentItemId
                this.addSavedLesson(itemLesson)
                this.updatePlanItems()
                this.$message.success(this.$t('loc.lesson2Publish'))
                // 如果编辑课程成功，如果当前周计划下只有一个课程，直接退出编辑状态
                if (this.planLessons.length === 1) {
                    // 获取当前 unitId
                    const unitId = this.$route.query.unitId
                    // 如果是 unit planer 部分的操作
                    if (unitId) {
                        await Lessons2.addAction({ unitId: unitId })
                        .catch(err => {
                        })
                    }
                    // 弹窗关闭
                    this.dialogVisible = false
                }
                  // 弹窗关闭
                  // this.dialogVisible = false
            } catch (error) {
                this.$message.error(error.response.data.error_message)
                // 弹窗关闭
                this.dialogVisible = false
            } finally {
                // 关闭保存 Loading
                this.saveLoading = false
            }
        },
        // 关闭编辑弹窗
        closeLessonEditDialog () {
            this.dialogVisible = false
            this.singleEditLesson = false
            this.singleAdaptLesson = false
            this.clearOldData()
            this.judgeIsOpenAddChildrenTip()
            // 关闭编辑弹窗，解锁当前周计划
            if (this.preview) {
              LessonApi.unlockEditing({
                id: this.planId
              })
            }
        },
        judgeIsOpenAddChildrenTip () {
            // 判断周计划是否是应用来的
            if (this.$route.params.apply) {
                // 判断是否显示过添加小孩提示
                if (!this.$store.state.lesson.isOpenAddChildrenTip) {
                    this.$emit('callbackAddChildrenTip')
                    this.$store.dispatch('setIsOpenAddChildrenTip', true)
                }
            }
        },
        getPlanLessons () {
            // 定义参数
            const params = {
                planId: this.planId || this.currentPlanId,
                includeCenterLesson: true
            }
            return new Promise((resolve, reject) => {
                // 发送请求
                this.$axios.get($api.urls().getPlanLessons, { params: params }).then(async res => {
                    // 如果 res 存在，并且 res.lessons 是存在的
                    if (res && res.lessons) {
                        // 设置 planLessons
                        this.planLessons = res.lessons.filter(item => item.lessonId)
                        // 初始化 Lesson 课程
                        await this.initLessons(res.lessons)
                        // 设置进入编辑状态时当前课程是否需要显示下一步按钮
                        const currentIndex = this.planLessons.findIndex(lesson => lesson.itemId === this.currentItemId)
                        this.showNext = currentIndex !== -1 && currentIndex < this.planLessons.length - 1
                    }
                    resolve()
                }).catch(error => {
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },
        // 继续下一个课程
        async nextLesson () {
            // 如果当前课程的 Id 是存在的
            if (this.currentItemId && !this.singleEditLesson && !this.singleAdaptLesson) {
                // 过滤出 Adapt 成功的课程
                const loadLessons = this.planLessonIds.filter((planLesson, index) => {
                    // 可以执行下一步时说明第一个课程已经 Adapt 完成，即将第一个课程的 isAdaptedLesson 设置为 true
                    if (index === 0) {
                        this.$set(planLesson, 'isAdaptedLesson', true)
                        // 点击 nextLesson 时第一个课程的选中状态默认时 true
                        // 设置当前课程的选中状态为 true, 表示当前课程已被选中，在后续的逻辑中会根据这个状态来判断进行下一步时是否可以再次选中当前课程
                        this.$set(planLesson, 'selected', true)
                    }
                    return planLesson.isAdaptedLesson || index === 0
                })
                // 获取当前课程的索引
                const currentIndex = loadLessons.findIndex(lesson => lesson.itemId === this.currentItemId)
                // 如果当前课程的索引是存在的
                if (currentIndex !== -1) {
                    // 课程发生了改变，先校验当前的课程是否符合标准
                    const lessonDetail = this.$refs['lessonDetail' + this.currentItemId][0]
                    // 获取校验的结果
                    const validated = await lessonDetail.validateLesson()
                    if (!validated) {
                        return
                    }
                    // 如果当前编辑的课程的索引是倒数第二个课程，那么就说明没有下一个课程了
                    if (currentIndex === this.planLessons.length - 2) {
                        this.showNext = false
                    }
                    // 过滤出当前选中的课程
                    // const currentLesson = loadLessons[currentIndex]
                    // 获取 LoadLesson 中已经 Adapt 成功但没有选中的课程
                    const unselectedAdaptedLessons = loadLessons.filter(lesson => !lesson.selected && lesson.isAdaptedLesson)
                    // 如果所选择的需要 Adapted 的课程的数量不等于已经 Adapted 的课程的数量，说明其他的还在进行中，此时点击下一个课程是无效的且提示
                    if (loadLessons.length !== this.planLessons.length && unselectedAdaptedLessons.length === 0) {
                        this.$message.warning(this.$t('loc.lessonsAreBeingAdapted'))
                        return
                    } else {
                        // 如果已经 Adapt 完成所选的课程，那么判断未选中的只有一个元素的时候，则下一个课程按钮需要隐藏
                        if (unselectedAdaptedLessons.length === 1) {
                            this.showNext = false
                        }
                    }
                    // 如果存在已经 Adapt 成功且还未选中的课程
                    if (unselectedAdaptedLessons.length > 0) {
                        // 获取未选中的已经 Adapter 成功的第一个课程为下一个课程即可
                        const nextLesson = unselectedAdaptedLessons[0]
                        this.$refs.lessonMenu.selectLesson(nextLesson)
                    }
                }
            } else {
                // 如果是编辑课程，直接按课程顺序下一个即可
                const currentIndex = this.planLessons.findIndex(lesson => lesson.itemId === this.currentItemId)
                if (currentIndex !== -1) {
                    // 如果当前编辑的课程的索引是倒数第二个课程，那么就说明没有下一个课程了
                    if (currentIndex === this.planLessons.length - 2) {
                        // this.showNext = false
                        this.secondToLast = true
                    }
                    const nextIndex = currentIndex + 1
                    if (nextIndex < this.planLessons.length) {
                        const nextLesson = this.planLessons[nextIndex]
                        this.$refs.lessonMenu.selectLesson(nextLesson)
                    } else {
                        this.showNext = false
                    }
                }
            }
        },
        // 适配课程
        adaptLesson () {
          // 获取当前操作的 itemId
          const itemId = this.currentLesson.itemId
          // 如果是单个编辑或单个改编的情况下，需要将当前的课程的 isAdaptedLesson 设置为 false
          if (this.singleAdaptLesson || this.singleEditLesson) {
            const lessonsToLoad = this.planLessonIds.filter(planLesson => equalsIgnoreCase(planLesson.itemId, itemId))
            if (lessonsToLoad.length > 0) {
              lessonsToLoad[0].isAdaptedLesson = false
            }
          }
          const lessonDetail = this.$refs['lessonDetail' + itemId][0]
          // 移动到指定位置
          lessonDetail.scrollIntoUDLOrCLR()
          // 为当前课程生成数据
          lessonDetail.updateGenerateUniversalDesignAndCLRData()
        }
    }
}
</script>

<style lang="less" scoped>
@media screen and (max-width: 1599px) {
    .lesson-detail-skeleton {
        margin: 10px 0;
        width: 1000px;
        padding: 0 20px;
    }

    /deep/ .new-lesson > .content {
        padding: 0 20px;
    }
    /deep/ .content {
      margin: 10px 0;
      min-width: 1000px;
      form {
        background-color: #fff;
        width: 980px;
      }

      .form-container {
        overflow-x: hidden;
        height: auto;
      }

      .assistant-toolbar {
        width: 365px;
        height: calc(100vh - 148px);
        // overflow: auto;
        position: sticky;
        top: 0;
      }
    }
    /deep/ .el-dialog__footer {
        max-width: 1375px!important;
        margin: 0 auto!important;
    }

    /deep/ .el-form-item {

        &.lesson-form-name, &.lesson-form-age, &.lesson-form-theme, &.lesson-form-framework, &.lesson-form-domain {
            max-width: 455px;
        }
        &.lesson-form-prepare, &.lesson-form-activity {
            max-width: 220px;
        }
        &.lesson-form-activity {
            margin-left: 15px;
        }
    }

    /deep/ .media-uploader-lesson-cover {
        width: 450px;
        height: 310px;
        position: relative;
        border: 1px solid transparent;

        .media-uploader {
            width: 450px;
            height: 310px;
            margin-left: -10px;
        }

        .media-uploader-select {
            width: 460px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            & > :first-child {
                width: 176px;
                height: 126px;
            }

            & > :nth-child(2) {
                width: 460px;
                color: #676879;
                font-size: 12px;
                line-height: 22px;
                text-align: center;
                margin: 12px auto;
            }
        }

        .media-uploader-selected {
            width: 450px;
            height: 310px;
            position: relative;

            & > .media-viewer {
                height: 100%;
            }
        }
  }
}

/deep/ .lesson-detail .content .lesson-editor-container .lesson-form {
    box-shadow: none !important;
    height: auto !important;
}

@media screen and (min-width: 1600px) {
    .lesson-detail-skeleton {
        width: 1147px;
        margin: 10px 0;
        padding: 0 20px;
    }

    /deep/ .new-lesson > .content {
        padding-left: 20px;
    }
    /deep/ .content {
      margin: 0 0 20px 0;
      min-width: 1147px;
      form {
        width: 1115px;
        background-color: #fff;
        padding: 20px 0px 0px 0px!important;
      }
      .assistant-toolbar {
        width: 415px;
        height: calc(100vh - 148px);
        // overflow-y: auto;
        position: sticky;
        top: 0;
      }
      .assistant-toolbar-full{
        width: calc(100vw - 300px);
        height: calc(100vh - 148px);
        // overflow-y: auto;
        position: sticky;
        top: 0;
      }
    }
    /deep/ .el-dialog__footer {
        max-width: 1450px!important;
        margin: 0 auto!important;
    }
}

.gap-20 {
    gap: 20px;
}

.lesson-editor {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
}

.flex-start-center {
    display: flex;
    -webkit-box-align: center;
    align-items: flex-start;
    justify-content: center;
}

.lesson-menu {
    width: 280px;
    height: calc(100vh - 170px);
    position: sticky;
    top: 0;
}

/deep/ .el-dialog {
    max-height: calc(100% - 40px) !important;
    height: 100%;
    background-color: var(--color-page-background-white);
    margin-top: 40px;
    overflow: hidden;
}

/deep/ .el-dialog__body {
    padding: 0 20px;
    overflow: auto;
    height: calc(100% - 130px);
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
  color: var(--color-text-placeholder);
}
/deep/ .lesson-editor-content .el-card__body {
    padding: 20px 0;
}

/deep/ .lesson-dll .el-card__body {
    padding: 20px;
}

/deep/ .lesson-dll-input .el-card__body {
    padding: 20px 0 0 0 !important;
}
</style>
