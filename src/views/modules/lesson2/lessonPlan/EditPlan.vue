<template>
  <div class="edit-paln h-full display-flex flex-direction-col lg-margin-right-24 lg-margin-left-24">
    <!-- 头部 -->
    <calendar-header
        ref="calendarHeader"
        :edit="true"
        :title="title"
        :planInfo="planInfo"
        @callUpdateBaseInfo="updateBaseInfo"
        @callUpdateWeek="updateWeek"
        @callShowBatchAdaptLessonsGuide="handleShowBatchAdaptLessonsGuide"
        @callDownloading="callDownloading">
    </calendar-header>
    <!-- 日历表格 -->
    <plan-calendar
        :key="renderKey"
        ref="calendar"
        :editTemplate="editTemplate"
        :edit="true"
        :planId="planId"
        :firstVisit="firstVisit"
        :showLastReflection="showLastReflection"
        @setPlanInfo="setPlanInfo"
        @callUpdateBaseInfo="updateBaseInfo"
        @reRender="reRender"
        @handleAddChildrenTips="handleAddChildrenTips"
        @callWeeklyPlanSettingGuide="handleJudgeNeedSettingGuide"
        @callChangeLastReflection="changeLastReflection">
    </plan-calendar>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import CalendarHeader from './components/CalendarHeader'
import PlanCalendar from './components/PlanCalendar'

export default {
  name: 'EditPlan',
  components: {
    CalendarHeader,
    PlanCalendar
  },
  provide () {
    return {
      dllContext: this.dllContext
    }
  },
  data () {
    return {
      editTemplate: false,
      planId: undefined,
      planInfo: {},
      showLastReflection: false,
      firstVisit: false, // 是否首次创建周计划
      dllContext: {
        groupId: null,
        lessonId: null,
        langCodes: null,
        onlyDllChildOpen: null
      },
      title: '',
      renderKey: 0 // 组件key，用于重新渲染
    }
  },

  created () {
    // 获取周计划 ID
    let planId = this.$route.params.planId
    // 获取是否是从应用周计划页面跳转过来的
    let apply = this.$route.params.apply
    // 如果是从系列课程应用过来的，弹出提示框
    if (apply) {
      this.$store.dispatch('setIsOpenAddChildrenTip', false)
      // 从系列课程应用过来直接隐藏引导过程
      let result = { 'features': ['WEEKLY_PLAN_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then()
      this.$store.commit('SET_FIRSTVISIT', false)
      localStorage.setItem(this.currentUser.user_id + 'WEEKLY_PLAN_GUIDE', false)
    }
    // 如果是编辑状态非创建非应用
    if (!apply && planId !== 'new') {
      this.$nextTick(() => {
        this.$refs.calendarHeader.judgeNeedSettingGuide()
      })
    }
    // 新建周计划，调用创建接口
    if (planId === 'new') {
      // 获取默认班级 ID
      let defaultGroupId = this.$route.query.defaultGroupId
      let isAdmin = this.$route.query.isAdmin
      this.createPlan(defaultGroupId, isAdmin)
      return
    } else {
      this.title = this.$t('loc.plan119')
      this.$bus.$emit('setPageName', { name: this.title, routeName: this.$route.name })
    }
    this.planId = planId
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open, // UDL 和 CLR 开关
      showMappedTip: state => state.lesson.showMappedTip // 是否显示映射提示
    }),
    routePlanId () {
      return this.$route.params.planId
    },
    isOpenAdaptUDLAndCLR () {
      return this.open && this.open.adaptUDLAndCLROpen
    }
  },

  methods: {
    // 重新渲染
    reRender () {
      this.firstVisit = false
      this.renderKey += 1
    },
    // 创建周计划
    createPlan (groupId, isAdmin) {
      let params = {
        groupId: groupId,
        adminNormalPlan: isAdmin
      }
      LessonApi.createPlan(params).then(response => {
        let planId = response.id
        this.planId = planId
        // 给是否为首次创建周计划赋值
        this.firstVisit = response.firstVisit
        // 如果不是首次创建周计划，则直接去判断是否显示批量改编课程引导
        if (!this.firstVisit) {
          this.$nextTick(() => {
            this.$refs.calendarHeader.judgeNeedSettingGuide()
          })
        }
        // 创建成功后跳转到编辑页面
        this.$router.replace({
          name: 'edit-plan',
          params: {
            planId: planId
          }
        })
        if (isAdmin) {
          this.title = this.$t('loc.plan112')
        } else {
          this.title = this.$t('loc.plan2')
        }
        this.$bus.$emit('setPageName', { name: this.title, routeName: this.$route.name })
      }).catch(error => {
      })
    },

    setPlanInfo (planInfo) {
      this.planInfo = planInfo
      this.showLastReflection = planInfo.showLastReflection
      // 当前周计划信息设置完成之后再调用接口获取学校班级信息
      this.$nextTick(() => {
        this.$refs.calendarHeader.getCenterGroups()
        const apply = this.$route.params.apply
        const isAdapted = this.$route.params.isAdapted
        // 如果周计划是应用过来的，且 UDL 和 CLR 开关打开，则显示改编课程弹窗
        if (apply && this.isOpenAdaptUDLAndCLR && !isAdapted) {
          // 显示改编课程弹窗
          this.$refs.calendar.$refs.adapterTips.dialogVisible = true
          // 显示改编课程弹窗埋点
          this.$analytics.sendEvent('web_weekly_plan_edit_adapt_pop')
        }
        // 如果是应用过来且该周计划是否已改编过，则直接显示添加小孩提示信息
        if (isAdapted) {
          this.$refs.calendar.$refs.adapterTips.batchAdaptTip(false)
        }
      })
    },
    changeLastReflection () {
      this.showLastReflection = this.$refs.calendar.showLastReflection
      this.updateBaseInfo()
    },
    // 更新周计划基本信息
    updateBaseInfo (type) {
      let params = {
        id: this.planId,
        theme: this.$refs.calendar.theme,
        groupId: this.$refs.calendarHeader.currentGroupId,
        teacherIds: this.$refs.calendarHeader.currentTeacherIds,
        week: this.$refs.calendarHeader.currentWeek,
        fromAtLocal: this.$refs.calendarHeader.fromDate,
        toAtLocal: this.$refs.calendarHeader.toDate,
        showLastReflection: this.$refs.calendar.showLastReflection,
        customThemeRowName: this.$refs.calendar.customThemeRowName && this.$refs.calendar.customThemeRowName.trim()
      }
      LessonApi.updatePlanBaseInfo(params).then(response => {
        // 更新班级后，重新渲染周计划表格
        if (type && type === 'UPDATE_GROUP') {
          this.renderKey += 1
        }
      }).catch(error => {})
      // 更新周计划对象信息
      this.planInfo.theme = this.$refs.calendar.theme
      this.planInfo.week = this.$refs.calendarHeader.currentWeek
      this.planInfo.groupId = this.$refs.calendarHeader.currentGroupId
      this.planInfo.teacherIds = this.$refs.calendarHeader.currentTeacherIds
      this.planInfo.showLastReflection = this.$refs.calendarHeader.showLastReflection
      this.planInfo.fromAtLocal = this.$refs.calendarHeader.fromDate
      this.planInfo.toAtLocal = this.$refs.calendarHeader.toDate
      // 更新班级
      // if (type && type === 'UPDATE_GROUP') {
      //   this.updateGroup()
      // }
      // 更新自动退出时间
      this.$refs.calendar.autoExist()
    },
    // 更新周
    updateWeek (week) {
      this.$refs.calendar.updateWeek(week)
    },

    updateGroup () {
      // 当前周
      let week = this.$refs.calendarHeader.currentWeek
      // 更新反思内容
      this.$refs.calendar.getLastReflection(week)
      // 更新测评点
      this.$refs.calendar.getFrameworkData(this.planInfo.groupId)
      // 更新小孩
      this.$refs.calendar.getChildren(this.planInfo.groupId)
      // 更新拖拽信息
      this.$refs.calendar.resetDragStatus()
    },

   /**
   * PDF 下载页面 loading
   */
    callDownloading (val) {
      this.$refs.calendar.calendarLoading = val
    },
    // 处理添加小孩弹窗提示
    handleAddChildrenTips () {
      this.$nextTick(() => {
        this.$refs.calendarHeader.editPlanGuided = false
        // 提示信息
        let confirmMsg = this.$t('loc.plan69')
        if (this.showMappedTip) {
          this.$store.dispatch('setShowMappedTip', false) // 清空映射提示 vuex 状态
          confirmMsg = '<div>' + this.$t('loc.plan69') + '</div>' + '<div style="background: #f5f6f8; border-radius: 8px; padding: 16px; margin-top: 12px;">' + this.$t('loc.drdpMeasureMapTip') + '</div>'
        }
        // 弹出提示框
        this.$alert(confirmMsg, this.$t('loc.noteTip'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('loc.gotIt'),
          customClass: 'plan-message-box'
        }).then(() => {
          // 点击确定按钮后，设置为已引导
          this.$refs.calendarHeader.editPlanGuided = true
          // 判断是否需要显示引导
          this.$refs.calendarHeader.judgeNeedSettingGuide()
        }).catch(error => {
          // 点击确定按钮后，设置为已引导
          this.$refs.calendarHeader.editPlanGuided = true
          // 点击确定按钮后，设置为已引导
          this.$refs.calendarHeader.judgeNeedSettingGuide()
        })
      })
    },
    // 判断是否需要显示设置引导
    handleJudgeNeedSettingGuide () {
      this.$nextTick(() => {
        this.$refs.calendarHeader.judgeNeedSettingGuide()
      })
    },
    // 显示批量适配课程引导
    handleShowBatchAdaptLessonsGuide () {
      this.$refs.calendar.$refs.adapterTips.judgeNeedAdaptUDLAndCLRGuide()
    }
  },

  watch: {
    routePlanId (val) {
      if (val) {
        this.planId = val
      }
    },
    'planInfo.groupId': {
      handler (value) {
        this.dllContext.groupId = value
      }
    }
  }
}
</script>

<style lang="less">
.guide-weekly-plan-editTip .driver-close-btn{
  display: none !important;
}
.guide-weekly-plan-editTip .driver-prev-btn{
  display: none !important;
}
</style>
