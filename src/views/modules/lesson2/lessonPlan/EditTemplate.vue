<template>
  <div class="edit-paln h-full display-flex flex-direction-col">
    <!-- 头部 -->
    <!-- <template-edit-header :title="headerTitle" :planInfo="planInfo"></template-edit-header> -->
    <template v-if="isComeFromIPad">
      <div class="display-flex">
        <div style="position:relative; text-align: center; width: 100%;">
          <div style="position: absolute;">
            <img class="go-back lg-pointer" src="@/assets/img/us/back_home.png" @click="beforeGoBack">
          </div>
          <span class="title-font-18">{{ headerTitle }}</span>
        </div>
      </div>
    </template>
    <!-- 日历表格 -->
    <plan-calendar :key="renderKey" ref="calendar" :adminEdit="true" :editTemplate="editTemplate" :edit="true" :planId="planId" :firstVisit="firstVisit"  :showLastReflection="showLastReflection" @setPlanInfo="setPlanInfo" @callUpdateBaseInfo="updateBaseInfo"  @reRender="reRender" style="margin-left: -20px;"></plan-calendar>
  </div>
</template>

<script>
import tools from '@/utils/tools'
import LessonApi from '@/api/lessons2'
import PlanCalendar from '@/views/modules/lesson2/lessonPlan/components/PlanCalendar'
import { mapState } from 'vuex'
import TemplateEditHeader from '@/views/modules/lesson2/lessonPlan/components/TemplateEditHeader'
export default {
  name: 'EditTemplate',
  components: { PlanCalendar, TemplateEditHeader },
  data () {
    return {
      showQuickAddLesson: false,
      editTemplate: true,
      planId: '',
      planInfo: {},
      showLastReflection: false,
      firstVisit: false, // 是否首次创建周计划
      headerTitle: '', // 顶部标题
      renderKey: 0 // 组件key，用于重新渲染
    }
  },
  methods: {
    // 重新渲染组件
    reRender () {
      this.renderKey++
    },
    createPlan (params) {
      let name = 'edit-template'
      // 如果是创建机构周计划，跳转到创建机构周计划页面
      if (params.adminNormalPlan) {
        name = 'edit-agency-template'
      }
      LessonApi.createPlan(params).then(response => {
        let planId = response.id
        this.planId = planId
        // 创建成功后跳转到编辑页面
        this.$router.replace({
          name: name,
          params: {
            planId: planId
          }
        })
      }).catch(error => {
      })
    },
    updateBaseInfo () {
      let params = {
        id: this.planId,
        theme: this.$refs.calendar.theme,
        frameworkId: this.$refs.calendar.frameworkId,
        adminTemplate: true,
        teacherIds: this.$refs.calendar.planData.teacherIds,
        customThemeRowName: this.$refs.calendar.customThemeRowName && this.$refs.calendar.customThemeRowName.trim()
      }
      LessonApi.updatePlanBaseInfo(params).then(response => {
      }).catch(error => {})
      // 更新周计划对象信息
      this.planInfo.theme = this.$refs.calendar.theme
      this.planInfo.frameworkId = this.$refs.calendar.frameworkId
      // 更新自动退出时间
      this.$refs.calendar.autoExist()
    },
    setPlanInfo (planInfo) {
      this.planInfo = planInfo
    },
    async beforeGoBack () {
      // 如果正在录制，提示是否退出录制
      let exist = true
      if (this.planRecording) {
        // 警告提示， 如果退出会终止录制
        await this.$confirm(this.$t('loc.plan142'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
        }).catch(() => {
          exist = false
        })
      }
      if (!exist) {
        return
      }
      // 编辑状态，更新编辑锁定信息
      if (this.edit && this.planInfo.id) {
        LessonApi.unlockEditing({
          id: this.planInfo.id
        })
      }
      if (this.planInfo && this.planInfo.type == 'NORMAL_TEMPLATE') {
        this.$router.push({
          name: 'list-plan'
        })
        return
      }
      // 如果过是管理员查看，跳转到管理员分配页面
      this.$router.push({
        name: 'template-list'
      })
    }
  },
  created () {
    // 获取周计划 ID
    let planId = this.$route.params.planId
    // 新建周计划，调用创建接口
    if (planId === 'new') {
      this.headerTitle = this.$t('loc.curriculum96')
      if (this.$route.query.isAdmin) {
        this.headerTitle = this.$t('loc.plan109')
      } else {
        this.headerTitle = this.$t('loc.curriculum98')
      }
      // 获取默认班级 ID
      let frameworkId = this.$route.query.frameworkId
      let createTemplate = this.$route.params.createTemplate
      let params = {
        frameworkId: frameworkId,
        adminNormalPlan: this.$route.query.isAdmin,
        adminTemplatePlan: createTemplate
      }
      this.createPlan(params)
      return
    } else if (this.$route.params.normal) {
      this.headerTitle = this.$t('loc.curriculum96')
    } else if (this.$route.params.create) {
      this.headerTitle = this.$t('loc.curriculum98')
    } else if (this.$route.params.replicate) {
      this.headerTitle = this.$t('loc.plan2')
    } else {
      this.headerTitle = this.$t('loc.curriculum48')
    }
    this.$bus.$emit('setPageName', { name: this.headerTitle, routeName: this.$route.name })
    this.planId = planId
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      planRecording: state => state.lesson.planRecording
    }),
    routePlanId () {
      return this.$route.params.planId
    },
    // 是否来自iPad
    isComeFromIPad () {
      return tools.isComeFromIPad()
    }
  },
  watch: {
    routePlanId (val) {
      if (val) {
        this.planId = val
      }
    }
  }
}
</script>
<style scoped>
  .go-back {
    max-width: 24px;
  }
</style>
