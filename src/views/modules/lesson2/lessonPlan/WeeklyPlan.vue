<template>
    <div class="h-full display-flex flex-direction-col lg-scrollbar-show lg-padding-right-24 lg-padding-left-24">
        <!-- 管理员切换菜单 -->
        <div v-if="isAdmin && !firstVisit" class="lg-tabs position-relative lg-margin-bottom-20 left-create-weekly-play-btn">
            <el-tabs v-model="submoduleName">
                <el-tab-pane :label="$t('loc.weeklyPlannersManagement')" name="PlannersManagement"></el-tab-pane>
                <el-tab-pane :label="$t('loc.curriculum49')" name="WeekPlannerTemplates"></el-tab-pane>
                <el-tab-pane :label="$t('loc.createVirtualShadowList')" name="VirtualShadowList"></el-tab-pane>
            </el-tabs>
            <div class="action-bar create-weekly-play-btn-m-t-10">
                <el-input
                    v-if="submoduleName === 'PlannersManagement' || submoduleName === 'WeekPlannerTemplates'"
                    class="border-bold lg-margin-right-12 input-width"
                    :placeholder="$t('loc.plan1')"
                    prefix-icon="lg-icon lg-icon-search font-size-14"
                    clearable
                    v-model="keyword">
                </el-input>
                <!-- 管理员创建周计划按钮 -->
                <el-button v-if="allowCreatePlan && submoduleName === 'PlannersManagement'" class="el-button-warning-dark" icon="lg-icon lg-icon-add font-size-16 lg-margin-right-4" @click="createPlan(true)">{{$t('loc.create')}}</el-button>
                <!-- 创建周计划模板按钮 -->
                <el-button v-if="submoduleName === 'WeekPlannerTemplates'" class="el-button-warning-dark" icon="lg-icon lg-icon-add  font-size-16 lg-margin-right-4" @click="createTemplate()">{{ $t('loc.create') }}</el-button>
            </div>
        </div>
        <!-- 老师切换菜单 -->
        <div v-show="(submoduleName !== 'ShadowedList' || isComeFromIPad) && !firstVisit" v-else class="lg-tabs position-relative lg-margin-bottom-20">
            <el-tabs v-model="submoduleName">
                <el-tab-pane :label="$t('loc.weeklyPlannerList')" name="PlannerList"></el-tab-pane>
                <el-tab-pane :label="$t('loc.curriculum49')" name="WeekPlannerTemplates"></el-tab-pane>
                <el-tab-pane :label="$t('loc.shadowedList')" name="ShadowedList"></el-tab-pane>
            </el-tabs>
            <div class="action-bar">
                <el-input
                    v-if="submoduleName === 'PlannerList' || submoduleName === 'WeekPlannerTemplates'"
                    class="border-bold lg-margin-right-12 input-width"
                    :placeholder="$t('loc.plan1')"
                    prefix-icon="lg-icon lg-icon-search font-size-14"
                    clearable
                    v-model="keyword">
                </el-input>
                <!-- 老师创建周计划按钮 -->
                <el-button v-if="submoduleName === 'PlannerList'" class="el-button-warning-dark" icon="lg-icon lg-icon-add font-size-16 lg-margin-right-4" @click="createPlan(false)">{{$t('loc.create')}}</el-button>
            </div>
        </div>
        <!-- 路由内容 -->
        <div class="h-full view-content flex-auto">
            <router-view ref="planTable" />
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import { isTeacher } from '@/utils/common'
import tools from '../../../../utils/tools'
export default {
    name: 'WeeklyPlan',
    created () {
        // 是否是教师角色
        this.isTeacher = isTeacher()
        if (this.$route.path === '/lesson2/plan') {
            if (this.isTeacher) {
                this.submoduleName = 'ShadowedList'
            } else {
                this.submoduleName = 'PlannersManagement'
            }
        } else if (this.$route.name === 'list-plan') {
            if (this.isTeacher) {
                this.submoduleName = 'PlannerList'
            } else {
                this.submoduleName = 'PlannersManagement'
            }
        } else if (this.$route.path.match('weekly-lesson-planning/shadowed-list') || this.$route.path.match('weekly-lesson-planning/create-virtual-shadow-list')) {
            if (this.isTeacher) {
                this.submoduleName = 'ShadowedList'
            } else {
                this.submoduleName = 'VirtualShadowList'
            }
        } else {
          this.submoduleName = 'WeekPlannerTemplates'
        }
    },
    data () {
        return {
            submoduleName: '',
            isTeacher: false,
            keyword: ''
        }
    },
    watch: {
        // 监听 radio button,进行路由跳转
        submoduleName (newVal, oldVal) {
            this.keyword = ''
            if (newVal === 'WeekPlannerTemplates') {
              if (isTeacher()) {
                this.$analytics.sendEvent('web_weekly_plan_expl_exposure_teacher')
              } else {
                this.$analytics.sendEvent('web_weekly_plan_expl_exposure_agency')
              }
              this.WeekPlannerTemplates()
            } else if (newVal === 'PlannersManagement' || newVal === 'PlannerList') {
                this.PlannersManagement()
            } else if (newVal === 'VirtualShadowList') {
              this.$analytics.sendEvent('web_weekly_plan_virtual_exposure')
                this.VirtualShadowList()
            } else {
                this.ShadowList()
            }
        },
        // 监听路由变化，进行 radio button 高亮切换
        $route () {
            if (this.$route.name === 'list-plan') {
                if (this.isTeacher) {
                    this.submoduleName = 'PlannerList'
                } else {
                    this.submoduleName = 'PlannersManagement'
                }
            } else if (this.$route.path.match('weekly-lesson-planning/shadowed-list') || this.$route.path.match('weekly-lesson-planning/create-virtual-shadow-list')) {
                if (this.isTeacher) {
                    this.submoduleName = 'ShadowedList'
                } else {
                    this.submoduleName = 'VirtualShadowList'
                }
            } else {
                this.submoduleName = 'WeekPlannerTemplates'
            }
        },
        keyword (newVal, oldVal) {
            if (isTeacher()) {
              this.$analytics.sendEvent('web_weekly_plan_list_click_search')
            } else {
              this.$analytics.sendEvent('web_weekly_plan_manage_click_search')
            }
            if (!this.$refs['planTable']) {
                return
            }
            if (this.$refs['planTable'].$children[0]) {
                this.$refs['planTable'].$children[0].keyword = newVal
            }
        }
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser,
            open: state => state.common.open,
            firstVisit: state => state.lesson.firstVisit
        }),
        isAdmin () {
            if (!this.currentUser) {
                return false
            }
            let role = this.currentUser.role
            return role && role.toUpperCase() === 'OWNER'
        },
        allowCreatePlan () {
            return this.open && this.open.adminCreateWeekPlan
        },
        isComeFromIPad () {
            return tools.isComeFromIPad()
        }
    },
    methods: {
        // 跳转周计划列表
        PlannersManagement () {
            this.$router.push({
                name: 'list-plan'
            })
        },
        // 跳转管理员管理周计划模板列表
        WeekPlannerTemplates () {
            this.$router.push({
            name: 'template-list'
            })
        },
        // 跳转管理员分配列表
        VirtualShadowList () {
            this.$router.push({
                name: 'assigned-plan'
            })
        },
        // 跳转老师被分享界面
        ShadowList () {
            this.$router.push({
                name: 'teacherShadow'
            })
        },
        createTemplate () {
          this.$analytics.sendEvent('web_weekly_plan_exemplar_click_add')
            this.$router.push({
                name: 'edit-template',
                params: {
                    planId: 'new',
                    createTemplate: true
                }
            })
        },
        createPlan (isAdmin) {
            if (isAdmin) {
                this.$analytics.sendEvent('web_weekly_plan_manage_click_add')
                this.$refs['planTable'].$children[0].createModelVisible = true
            } else {
                this.$analytics.sendEvent('web_weekly_plan_list_click_add')
                this.$refs['planTable'].$children[0].createPlan()
            }
        }
    }
}
</script>

<style lang="less" scoped>
.view-content {
    min-height: 0;
}
.action-bar {
    position: absolute;
    right: 0px;
    top: 0px;
    display: flex;
    padding: 4px 0;
}

@media screen and (max-width: 1199px) {
  /deep/.lg-tabs .el-tabs__item {
    padding: 0 4px !important;
    font-size: 14px !important;
  }
  /deep/.input-width {
      width: 110px;
  }
}
@media screen and (max-width: 1345px) {
  .action-bar {
    position: unset;
  }
  .left-create-weekly-play-btn {
    display: flex!important;
    flex-direction: column!important;
  }
  .create-weekly-play-btn-m-t-10 {
    margin-top: 10px;
  }
  .input-width {
    width: 300px!important;
  }
}
</style>
