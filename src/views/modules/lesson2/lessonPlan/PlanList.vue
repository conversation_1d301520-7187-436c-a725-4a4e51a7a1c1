<template>
  <div class="h-full">
    <plan-table v-if="!firstVisit" ref="planTable" :loader="listPlans"></plan-table>
    <weekly-plan-white-board :centers="centers" :defaultGroupId="defaultGroupId" v-else/>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import PlanTable from './components/PlanTable.vue'
import WeeklyPlanWhiteBoard from './components/WeeklyPlanWhiteBoard'
import { mapState } from 'vuex'

export default {
  name: 'PlanList',
  components: { WeeklyPlanWhiteBoard, PlanTable },

  data () {
    return {
      centers: [],
      groups: [],
      defaultGroupId: ''
    }
  },
  created () {
    this.getCenterGroups()
  },
  // beforeRouteEnter (to, from, next) {
  //   next(vm => {})
  // },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      firstVisit: state => state.lesson.firstVisit
    })
  },
  methods: {
    listPlans (params) {
      params['type'] = 'NORMAL'
      return LessonApi.listPlans(params)
    },
    // 获取当前用户学校、班级列表
    getCenterGroups () {
      this.$axios({
        url: $api.urls(this.currentUser.user_id).centersAndGroups,
        method: 'get'
      }).then(response => {
        let centers = []
        // 过滤离校班级
        response.forEach(c => {
          let groups = c.groups.filter(x => !x.inactive)
          if (groups.length > 0) {
            c.groups = groups
            centers.push(c)
          }
        })
        // 加入学校列表
        this.centers = centers
        // 选择第一个学校中第一个班级作为创建周计划的默认班级
        if (centers.length > 0) {
          this.defaultGroupId = centers[0].groups[0].id
        }
      }).catch(error => {})
    }
  }
}
</script>
