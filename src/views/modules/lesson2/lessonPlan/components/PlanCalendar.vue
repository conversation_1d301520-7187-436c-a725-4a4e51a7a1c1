<template>
  <div class="plan-calendar h-full">
    <!-- 周计划复制弹窗 -->
    <el-dialog
      :visible.sync="replicateModelVisible"
      width="30%"
      :before-close="cancelReplicate"
      custom-class="modal-p-x">
      <span class="font-size-20" slot="title">{{$t('loc.plan66')}}</span>
      <!-- 班级选择 -->
      <div>{{$t('loc.plan120')}}</div>
      <el-select v-model="selectedGroupId" style="width: 100%;">
        <div style="max-height: 250px; overflow:auto;" class="scrollbar">
          <el-option-group
            v-for="center in centers"
            :key="center.id"
            :label="center.name">
            <el-option
              v-for="group in center.groups"
              :key="group.id"
              :label="group.name"
              :value="group.id">
            </el-option>
          </el-option-group>
        </div>
      </el-select>
      <div v-show="showNotSelectGroupTip" style="color:red">{{$t('loc.plan97')}}</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelReplicate">{{ $t('loc.cancel' )}}</el-button>
        <el-button type="primary" :loading="replicateLoading" @click="replicateWeekPlan(true)">{{ $t('loc.save') }}</el-button>
      </span>
    </el-dialog>
    <!-- 确认复制周计划弹窗 -->
    <el-dialog
      :title="$t('loc.confirmation')"
      custom-class="lg-el-dialog"
      :visible.sync="confirmReplicateModelVisible"
      :before-close="closeReplicateModel"
      width="30%">
      <span class="lg-color-text-primary">{{$t('loc.plan67')}}</span>
      <span slot="footer" class="dialog-footer">
        <el-button class="font-weight-normal" plain @click="closeReplicateModel" :disabled="replicateLoading">{{$t('loc.cancel')}}</el-button>
        <el-button class="font-weight-normal" type="primary" @click="confirmReplicate" :loading="replicateLoading">{{$t('loc.confirm')}}</el-button>
      </span>
    </el-dialog>
    <!-- 如果是看分享页面，且无分享内容，显示空页面 -->
    <div v-if="isShared && !hasShared" class="bg-white-only h-full box-shadow-8 lg-border-radius-8">
      <div class="display-flex flex-direction-col align-items">
        <img style="margin-top: 200px" src="@/assets/img/lesson2/plan/empty.png" >
        <div class="text-center" style="color:#98a6ad">{{$t('loc.noShadowedPlannerYet')}}</div>
      </div>
    </div>
    <div v-else class="h-full position-relative" v-loading="calendarLoading">
      <div v-if="isShared" class="display-flex flex-space-between lg-padding-t-b-4 bg-white lg-border-radius-8 lg-padding-left-24 lg-padding-right-24">
        <div class="display-flex align-items">
          <button v-if="false" class="shared-plan-pre-btn" :class="{'lg-color-text-secondary cursor-disabled': !hasLastWeekPlan}" @click="lastWeekPlan"><i class="lg-icon lg-icon-arrow-left"></i></button>
          <button v-if="false" class="shared-plan-next-btn" :class="{'lg-color-text-secondary cursor-disabled': !hasNextWeekPlan}" @click="nextWeekPlan"><i class="lg-icon lg-icon-arrow-right"></i></button>
          <img style="width: 60px;margin-right: 14px;" src="@/assets/img/lesson2/plan/approved.png">
          <!-- 上周 -->
          <el-tooltip v-if="!hasLastWeekPlan" class="item" effect="dark" :content="$t('loc.plan98')" placement="top">
            <div class="m-r-sm lg-pointer" @click="lastWeekPlan" :class="{'lg-color-text-secondary cursor-disabled': !hasLastWeekPlan}">
              <i class="lg-icon lg-icon-arrow-left font-size-20 text-default"></i>
            </div>
          </el-tooltip>
          <div v-else class="m-r-sm lg-pointer" @click="lastWeekPlan" :class="{'lg-color-text-secondary cursor-disabled': !hasLastWeekPlan}">
            <i class="lg-icon lg-icon-arrow-left font-size-20 text-default"></i>
          </div>
          <!-- 下周 -->
          <el-tooltip v-if="!hasNextWeekPlan" class="item" effect="dark" :content="$t('loc.plan99')" placement="top">
            <div class="m-l-sm lg-pointer" @click="nextWeekPlan" :class="{'lg-color-text-secondary cursor-disabled': !hasNextWeekPlan}">
              <i class="lg-icon lg-icon-arrow-right font-size-20 text-default"></i>
            </div>
          </el-tooltip>
          <div v-else class="m-l-sm lg-pointer" @click="nextWeekPlan" :class="{'lg-color-text-secondary cursor-disabled': !hasNextWeekPlan}">
            <i class="lg-icon lg-icon-arrow-right font-size-20 text-default"></i>
          </div>
          <div class="display-flex align-items">
            <!-- 周次信息展示 -->
            <div class="display-flex align-items">
              <!-- 展示周次 -->
              <div v-if="currentWeekPlan.type === 'NORMAL'" class="lg-margin-left-12 title-font-20 lg-color-text-primary">
                  <span v-if="currentWeekPlan.week">{{$t('loc.planWeek')}} {{currentWeekPlan.week}}</span>
              </div>
              <!-- 展示开始/结束日期 -->
              <div v-if="currentWeekPlan.type === 'NORMAL'" class="lg-margin-left-12">
                {{currentWeekPlan.fromAtLocal}} - {{currentWeekPlan.toAtLocal}}
              </div>
              <div v-if="currentWeekPlan.type === 'NORMAL_TEMPLATE'" class="lg-margin-right-4 font-size-18 font-weight-600">Agency-wide Weekly Planner</div>
              <el-dropdown trigger="click" placement="bottom"  @command="changeWeek">
                <div class="display-flex align-items">
                  <i class="el-icon-caret-bottom lg-color-text-primary"></i>
                </div>
                <!-- 周计划列表 -->
                <el-dropdown-menu v-infinite-scroll="nextPageWeeklyPlanner" :infinite-scroll-disabled="!hasNextPage" style="max-height:300px;width: 250px" class="lg-scrollbar-show" slot="dropdown">
                  <el-dropdown-item v-for="(item, index) in sharedWeeks"
                                    :key="index"
                                    :class="item.id === currentWeekPlan.id ? 'activity-item' : ''"
                                    :command="item">
                    <div style="display: flex; align-items: center; line-height: 20px; flex-direction: column;">
                      <div class="shared-plan-theme" :title="item.theme">{{item.theme}}</div>
                      <div class="week-date-color" v-if="item.type === 'NORMAL'">Week {{item.week}} ({{item.fromAtLocal}} - {{item.toAtLocal}})</div>
                    </div>
                  </el-dropdown-item>
                  <!-- 加载更多 -->
                  <div style="text-align: center;">
                    <p v-if="getSharePlannerLoading">loading...</p>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
        <!-- 分享创作者切换、复制周计划按钮、pdf下载按钮 -->
        <div class="display-flex align-items">
          <!-- 老师名字和切换老师popover -->
          <div v-if="sharedTeachers.length > 1">
            <el-dropdown trigger="click" placement="bottom" @command="changeTeacher">
              <div class="display-flex align-items lg-margin-t-b-12">
                <el-avatar size="medium" :src="currentTeacher.avatarUrl"></el-avatar>
                <i class="lg-icon lg-icon-arrow-down lg-margin-left-16 title-font-16"></i>
              </div>
              <!-- 老师列表 -->
              <el-dropdown-menu slot="dropdown" class="lg-padding-t-b-20">
                <div class="lg-margin-l-r-20">
                  <div class="title-font-18 lg-color-text-primary">Creator List</div>
                  <div class="lg-padding-top-8 lg-margin-t-b-8 b-t lg-color-text-secondary title-font-14-regular">
                    {{ $t('loc.CreatedbyNum',{ num: 1 }) }}
                  </div>
                  <div class="display-flex align-items">
                    <el-avatar size="medium" :src="currentTeacher.avatarUrl"></el-avatar>
                    <span class="lg-margin-left-12">{{currentTeacher.displayName | formatUserName}}</span>
                  </div>
                </div>
                <template v-if="sharedTeachers.length - 1 > 0">
                  <div class="lg-margin-l-r-20 lg-padding-top-8 lg-margin-t-b-8 b-t lg-color-text-secondary title-font-14-regular">
                    {{ $t('loc.OtherPlannerCreators',{ num: sharedTeachers.length - 1 }) }}
                  </div>
                  <div style="max-height:300px;" class="lg-scrollbar-show">
                    <el-dropdown-item v-for="(item, index) in sharedTeachers.filter(teacher => teacher.id !== currentTeacher.id)"
                                      :key="index" :command="item">
                      <div class="display-flex align-items">
                        <el-avatar size="medium" :src="item.avatarUrl"></el-avatar>
                        <span class="lg-margin-left-12">{{item.displayName | formatUserName}}</span>
                      </div>
                    </el-dropdown-item>
                  </div>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div v-else>
            <el-avatar size="medium" :src="currentTeacher.avatarUrl"></el-avatar>
          </div>
          <!-- 复制 -->
          <el-button class="lg-margin-left-16" :loading="replicateLoading" @click="showReplicateModel" :disabled="planRecording"><i class="lg-icon lg-icon-copy lg-margin-right-4"></i>{{$t('loc.plan66')}}</el-button>
          <!-- 笔记 -->
          <plan-note-creator class="lg-margin-left-16" :note.sync="planNote" :planId="planId"
                              v-if="isShared &&  planId && !isAdmin" :key="planId"/>
          <!-- PDF 下载 打印 菜单 -->
          <el-button type="primary" class="lg-margin-left-16" @click.stop="generatePDF(true)">
            <i class="lg-icon lg-icon-print lg-margin-right-8"></i>{{$t('loc.plan174')}}
          </el-button>
          <el-popover
            placement="bottom"
            width="150"
            trigger="click"
            v-model="morePopVisable">
            <!-- 复制按钮 -->
            <div>
              <el-link
                :underline="false"
                :disabled="planRecording"
                @click.stop="showReplicateModel()">
                <i class="lg-icon lg-icon-copy font-size-24 lg-margin-right-16"></i>
                <span>{{$t('loc.plan66')}}</span>
              </el-link>
            </div>
            <el-divider class="action-divider"></el-divider>
            <!-- PDF 下载和打印 -->
            <div>
              <el-link
                :underline="false"
                @click.stop="generatePDF(false)">
                <i class="lg-icon lg-icon-download font-size-24 lg-margin-right-16"></i>{{$t('loc.download')}}
              </el-link>
            </div>
            <div>
              <el-link
                :underline="false"
                @click.stop="generatePDF(true)">
                <i class="lg-icon lg-icon-print font-size-24 lg-margin-right-16"></i>{{$t('loc.plan174')}}
              </el-link>
            </div>
            <el-divider class="action-divider"></el-divider>
            <!-- 删除 -->
            <div>
              <el-link
                class="action-delete"
                :underline="false"
                :disabled="planData.lockedData || !planData.canEditOrDelete || planRecording"
                @click.stop="deletePlan()">
                <i class="lg-icon lg-icon-delete font-size-24 lg-margin-right-16"></i>{{$t('loc.delete')}}</el-link>
            </div>
            <el-button v-if="planData && planData.id" class="lg-margin-left-16" icon="lg-icon lg-icon-more-horizontal" slot="reference" @click="morePopVisable=!morePopVisable"></el-button>
          </el-popover>
        </div>
      </div>
      <div class="plan-calendar display-flex" :class="{'shared-plan': isShared || isSharedDetail}">
        <div v-if="!editTemplate" id="toolbar-area" class="toolbar-area">
          <el-tooltip class="item" effect="dark" :content="planData.type === 'NORMAL' ? $t('loc.classOverView') : (isAdmin ? $t('loc.planCommentTitle') : $t('loc.teacherPlanCommentTitle'))" :disabled="!collapse" placement="right">
            <el-button plain size="small" style="width: 32px;padding: 8px 10px 10px 8px !important" class="collapse-btn" @click="collapseMenu" circle :icon="collapse ? 'el-icon-arrow-right': 'el-icon-arrow-left'"></el-button>
          </el-tooltip>
          <plan-toolbar :planId="planId" :groupId="planData.groupId" :week="planData.week" :note.sync="planNote"
                        :records="shareRecords" :status="{isShared,edit,approved:planData.status === 'D_APPROVED'}"
                        v-show="!collapse" ref=" "></plan-toolbar>
          <div v-show="collapse" class="w-2x"></div>
        </div>
        <div class="calendar-area" :class="canEditTemplate && (edit || review) && !draging ? '' : 'm-l-md'">
          <div v-if="!isShared && (curriculumInfo.curriculumId || !curriculumInfo.curriculumId && curriculumInfo.unitId) && !isComeFromIPad || ((edit || review) && !editTemplate) || (edit && canEditTemplate && !editTemplate)" class="flex-space-between lg-padding-bottom-20 lg-margin-top-4" :class="canEditTemplate && (edit || review) && !draging ? 'm-l-md' : ''">
            <div class="display-flex align-items">
              <!-- 跳转单元课程按钮-->
              <el-button class="m-r-md" v-if="curriculumInfo.curriculumId && !isComeFromIPad && !edit" plain @click="goToCurriculum"  :title="'Unit ' + curriculumInfo.unitNumber + ': ' + curriculumInfo.unitTitle + ' (Week' + ' ' + curriculumInfo.unitPlanNumber + ')'">
                <span>
                  {{ curriculumInfo.unitNumber < 1 ? ('Special Unit ' + curriculumInfo.unitNumber) : ('Unit ' + curriculumInfo.unitNumber) }}:{{ ' ' + curriculumInfo.unitTitle }}{{ ' ' + ' (Week ' + curriculumInfo.unitPlanNumber + ')' }}
               </span>
                <i class="lg-icon lg-icon-arrow-right font-size-16"></i>
              </el-button>
              <el-button style="height: 36px;line-height: 30px; max-width: 300px;" class="m-r-md" v-if="!curriculumInfo.curriculumId && curriculumInfo.unitId  && !isComeFromIPad && !edit" plain @click="goToUnitInfo" :title="curriculumInfo.unitTitle + '' + ' (Week ' + (curriculumInfo.unitPlanNumber + 1) + ')'">
                <div class="display-flex justify-content align-items">
                  <img src="~@/assets/img/lesson2/unitPlanner/curriculum_unit.svg" style="width: 24px;height: 24px" alt="curriculum_unit.png">
                  <span class="lg-margin-left-8 add-width-9 display-inline-block overflow-ellipsis">Unit Overview: {{ curriculumInfo.unitTitle }}{{ ' ' + ' (Week ' + (curriculumInfo.unitPlanNumber + 1) + ')' }}</span>
                  <i class="lg-icon lg-icon-arrow-right font-size-16"></i>
                </div>
              </el-button>
                <!--添加 adapter 按钮-->
                <AdapterTips
                    v-if="edit || review"
                    :planId="planId"
                    :planStatus="planStatus"
                    :review="review"
                    :planType="planType"
                    :preview="viewPlan"
                    :hasBatchAdapterLesson="validateBatchAdapterLesson"
                    :before-adapter="() => saveDraft(true)"
                    ref="adapterTips"
                    @addChildrenTips="handleAddChildrenTips"
                ></AdapterTips>
              <el-button plain size="medium" :loading="submitLoading ||  $refs.familyResourcePreview && $refs.familyResourcePreview.loading" v-if="edit || (!edit && planData.status === 'B_PENDING')">
                <span @click="openPlanResourcePreview">{{ $t('loc.planResourcePreview') }} </span>
<!--                <el-divider direction="vertical"></el-divider>-->
<!--                <i @click="openDllSettings" class="lg-icon lg-icon-settings"> Assign </i>-->
              </el-button>
              <family-resource-preview ref="familyResourcePreview" v-show="false" :plan="planData" :before-preview="()=> saveDraft(true)"/>
            </div>
            <!-- 保存 -->
            <div v-if="edit || (!edit && planData.status === 'B_PENDING')" class="display-flex align-items" style="float: right;" :class="{'m-l-md': canEditTemplate}">
              <!-- 编辑操作 -->
              <el-button v-if="edit && planData.status == 'A_DRAFT' && !draging" class="lg-margin-left-12" type="primary" plain size="medium" @click="saveDraft()" :loading="saveDraftLoading" :disabled="submitLoading">{{$t('loc.plan_saveDraft')}}</el-button>
              <el-button v-if="edit && !draging" type="primary" size="medium" class="lg-margin-left-12" @click="submit()" :loading="submitLoading" :disabled="saveDraftLoading">
                <span v-if="!planData.privilegeUser">{{$t('loc.plan13')}}</span>
                <span v-if="planData.privilegeUser">{{$t('loc.plan68')}}</span>
              </el-button>
              <!-- 审核操作 -->
              <el-button class="m-l-sm" v-if="(!edit && isAdmin && planData.status === 'B_PENDING') || review" type="danger" size="medium" @click="rejectModal()">{{$t('loc.plan63')}}</el-button>
              <el-button v-if="(!edit && isAdmin && planData.status === 'B_PENDING') || review" type="primary" size="medium" @click="approveModal()">{{$t('loc.plan62')}}</el-button>
            </div>
          </div>
          <div v-else-if="((edit || review || planData.lockedData) && !editTemplate)" :class="[canEditTemplate && (edit || review) && !draging ? 'm-l-md' : '', planData.lockedData && planData.status === 'B_PENDING' ? 'flex-justify-end lg-padding-bottom-20 lg-margin-top-4': 'flex-space-between']">
              <family-resource-preview ref="familyResourcePreview" v-show="(!edit && isAdmin && planData.status === 'B_PENDING' && !planData.lockedData) || review" :plan="planData" :before-preview="()=> saveDraft(true)"/>
              <!-- 保存 -->
              <div v-if="edit || (!edit && planData.status === 'B_PENDING')" class="display-flex align-items" style="float: right;" :class="{'m-l-md': canEditTemplate}">
                  <!-- 审核操作 -->
                  <el-button class="m-l-sm" v-if="(!edit && isAdmin && planData.status === 'B_PENDING') || review" type="danger" size="medium" @click="rejectModal()">{{$t('loc.plan63')}}</el-button>
                  <el-button v-if="(!edit && isAdmin && planData.status === 'B_PENDING') || review" type="primary" size="medium" @click="approveModal()">{{$t('loc.plan62')}}</el-button>
              </div>
          </div>
          <div class="calendar-bottom lg-scrollbar-show" id="calendar-container" style="overflow-x: auto;">
            <div v-if="!isShared && !editTemplate"
                 :class="[(existInterpretation || canAdd || !edit) && !isGuiding || curriculumInfo.curriculumId || curriculumInfo.unitId ? 'group-area' : '', edit ? '' : 'b-t b-l b-r', edit && existInterpretation || curriculumInfo.curriculumId ? 'b-t b-l b-r' : '', canEditTemplate && (edit || review) && !draging ? 'm-l-md' : '']"
                 class="bg-white lg-border-top-right-radius-8 lg-border-top-left-radius-8 flex-space-between align-items"
                 :style="edit? 'flex-flow: row-reverse;' : ''"
                 style="min-width: 720px;">
              <!-- 班级信息 -->
              <div class="display-flex lg-padding-t-b-8" v-if="!edit">
                <span v-if="!!this.planData.centerName" class="m-r-md">{{$t('loc.center')}}: {{this.planData.centerName}}</span>
                <span v-if="!!this.planData.groupName" class="m-r-md">{{$t('loc.class')}}: {{this.planData.groupName}}</span>
                <span class="m-r-md">{{$t('loc.plan117')}}: {{this.planData.teacherNames | formatUserName}}</span>
              </div>
              <!-- 课程拖拽核心测评点开关和周计划讲解 -->
              <div class="display-flex" style="margin-left: auto">
                <!-- 核心测评点开关 -->
                <div v-if="planData.showCoreMeasureOpen && !edit && !review" class="display-flex align-items w-full white-space">
                  <el-switch
                    v-model="showCore"
                    class="m-l-xs"
                    size="mini"
                  ></el-switch>
                  <span  class="switch-title lg-color-text-primary m-l-xs m-r">
                    {{ $t('loc.showCoreMeasureOnly') }}
                  </span>
                </div>
                <!-- 普通周计划讲解 -->
                <div v-show="!isGuiding" class="display-flex align-items" style="gap: 10px">
                  <plan-interpretation class="" v-if="!isShared && planData"
                                    :plan-id="planData.id" :isShared="isShared" :canAdd="canAdd" :canRemove="edit || canAdd"
                                    @haveInterpretation="haveInterpretation"
                                    :teacher-ids="planData.teacherIds" style="justify-self: end;" />
                  <AdapterTips
                    v-if="!edit && !review"
                    :planId="planId"
                    :planStatus="planStatus"
                    :preview="viewPlan"
                    :showAdaptNewTag="showAdaptNewTag"
                    :isLocked="isLocked"
                    :planType="planType"
                    :hasBatchAdapterLesson="validateBatchAdapterLesson"
                    :before-adapter="() => saveDraft(true)"
                    ref="adapterTips"
                    @hideNewTag="hideNewTag"
                    @addChildrenTips="handleAddChildrenTips"
                  ></AdapterTips>
                </div>
              </div>
              <!-- 是否显示上周反思 -->
              <div class="m-r-sm display-flex align-items max-width-120" v-if="edit && canEditTemplate && !editTemplate">
                <span class="lg-margin-left-20 lg-margin-right-8">{{$t('loc.plan25')}}</span>
                <el-switch
                  @change="changeLastReflection"
                  v-model="showLastReflection">
                </el-switch>
              </div>
              <!--    批量编辑 课程 -->
              <BatchEditPlan :hasLessons="hasLessons"
                             v-show="edit || review"
                             :planType="planType"
                             :preview="viewPlan"
                             :before-edit="() => saveDraft(true)"
                             :class="!((existInterpretation || canAdd || !edit) && !isGuiding || curriculumInfo.curriculumId || curriculumInfo.unitId) ? 'group-area' : ''"
                             :planId="planId">

              </BatchEditPlan>
              <!-- 跳转单元课程按钮-->
              <el-button style="height: 36px;line-height: 30px; max-width: 300px;" class="m-r-md" v-if="curriculumInfo.curriculumId && !isComeFromIPad && edit" plain @click="goToCurriculum"  :title="'Unit ' + curriculumInfo.unitNumber + ': ' + curriculumInfo.unitTitle + ' (Week' + ' ' + curriculumInfo.unitPlanNumber + ')'">
                <div class="display-flex justify-content align-items">
                  <span class="overflow-ellipsis">
                  {{ curriculumInfo.unitNumber < 1 ? ('Special Unit ' + curriculumInfo.unitNumber) : ('Unit ' + curriculumInfo.unitNumber) }}:{{ ' ' + curriculumInfo.unitTitle }}{{ ' ' + ' (Week ' + curriculumInfo.unitPlanNumber + ')' }}
                </span>
                  <i class="lg-icon lg-icon-arrow-right font-size-16"></i>
                </div>
              </el-button>
              <el-button style="height: 36px;line-height: 30px; max-width: 300px;" class="m-r-md" v-if="!curriculumInfo.curriculumId && curriculumInfo.unitId  && !isComeFromIPad && edit" plain @click="goToUnitInfo" :title="curriculumInfo.unitTitle + '' + ' (Week ' + (curriculumInfo.unitPlanNumber + 1) + ')'">
                <div class="display-flex justify-content align-items">
                  <img src="~@/assets/img/lesson2/unitPlanner/curriculum_unit.svg" style="width: 24px;height: 24px" alt="curriculum_unit.png">
                  <span class="lg-margin-left-8 add-width-9 display-inline-block overflow-ellipsis">Unit Overview: {{ curriculumInfo.unitTitle }}{{ ' ' + ' (Week ' + (curriculumInfo.unitPlanNumber + 1) + ')' }}</span>
                  <i class="lg-icon lg-icon-arrow-right font-size-16"></i>
                </div>
              </el-button>
            </div>
            <!-- 日历表格 -->
            <div class="plan-table position-relative" id="plan-table" :class="{'lg-padding-l-r-20':editTemplate && edit}">
              <!-- 分享过来的周计划，显示学校班级信息、核心测评点开关和周计划讲解 -->
              <div v-if="isShared" class="flex-space-between bg-white lg-padding-16 lg-border-top-left-radius-8 lg-border-top-right-radius-8 b-t b-l b-r">
                <!-- 学校班级信息 -->
                <div class="display-flex flex-wrap">
                  <span v-if="!!this.planData.centerName" class="m-r-md">{{$t('loc.center')}}: {{this.planData.centerName}}</span>
                  <span v-if="!!this.planData.groupName" class="m-r-md">{{$t('loc.class')}}: {{this.planData.groupName}}</span>
                  <span v-if="this.planData.type !== 'NORMAL'">{{$t('loc.plan117')}}: {{this.planData.teacherNames | formatUserName}}</span>
                </div>
                <div class="display-flex align-items">
                  <template v-if="planData.showCoreMeasureOpen">
                    <el-switch
                      v-show="!edit && showCoreMeasureOpen"
                      v-model="showCore"
                      size="mini"
                    ></el-switch>
                    <span v-show="!edit" class="m-l-xs m-r-xs">
                      {{ $t('loc.showCoreMeasureOnly') }}
                    </span>
                  </template>
                    <!-- 分享来的周计划解读组件 -->
                  <plan-interpretation v-if="isShared && planData"
                    :canAdd="false" :canRemove="false"
                    :plan-id="planData.id" :teacher-ids="planData.teacherIds" :isShared="isShared"/>
                  </div>
              </div>
              <!-- 编辑模板周计划的顶部 -->
              <div v-if="editTemplate" :class="edit ? 'flex-space-between': 'bg-white lg-border-top-left-radius-8 lg-border-top-right-radius-8 b-t b-l b-r'" class="lg-padding-top-12 lg-padding-bottom-12 lg-padding-l-r-20">
                <div v-if="edit" class="display-flex align-items">
                  <span class="font-color-black font-size-16  add-padding-r-16">{{ $t('loc.plan118') }}:</span>
                  <el-select style="width: 400px;" v-model="frameworkId" @change="updateFramework" placeholder="Please Select">
                    <el-option
                      v-for="item in frameworks"
                      :key="item.frameworkId"
                      :label="item.frameworkName"
                      :value="item.frameworkId">
                    </el-option>
                  </el-select>
                </div>
                <div class="display-flex align-items flex-space-between">
                  <!-- 周计划创建者信息 -->
                  <div v-if="!edit">{{$t('loc.plan117')}}: {{this.planData.teacherNames | formatUserName}}</div>
                  <!-- 核心测评点开关 -->
                  <div class="display-flex align-items">
                    <template v-if="planData.showCoreMeasureOpen">
                      <div class="lg-margin-right-16">
                        <el-switch
                          v-show="!edit && showCoreMeasureOpen"
                          v-model="showCore"
                          @change="changeExamplarPlanCoreMeasureOpen"
                          size="mini"
                        ></el-switch>
                        <span v-show="!edit" class="m-l-xs" style="white-space: nowrap">
                          {{ $t('loc.showCoreMeasureOnly') }}
                        </span>
                      </div>
                    </template>
                    <!-- 周计划模板讲解 -->
                    <div>
                      <plan-interpretation :plan-id="planData.id" :isShared="isShared"
                                          :teacher-ids="planData.teacherIds" style="justify-self: end;" :canAdd="canAdd" :canRemove="edit || canAdd"/>
                    </div>
                  </div>
                  <!-- 保存草稿 -->
                  <el-button v-if="edit && planData.status == 'A_DRAFT' && !draging" class="lg-margin-left-12" type="primary" plain size="medium" @click="saveDraft()" :loading="saveDraftLoading" :disabled="submitLoading">{{$t('loc.plan_saveDraft')}}</el-button>
                  <!-- 保存按钮 -->
                  <el-button v-if="edit && !draging" type="primary" size="medium" class="lg-margin-left-12" @click="submit()" :loading="submitLoading" :disabled="saveDraftLoading">{{$t('loc.save')}}</el-button>
                  <!-- 下载 打印 菜单 -->
                  <el-popover
                    placement="bottom"
                    width="150"
                    trigger="click"
                    v-if="planData && planData.id && edit"
                    v-model="morePopVisable">
                    <!-- 复制按钮 -->
                    <div>
                      <el-link
                        :underline="false"
                        @click.stop="confirmReplicateModelVisible = true">
                        <i class="lg-icon lg-icon-copy font-size-24 lg-margin-right-16"></i>
                        <span>{{$t('loc.plan66')}}</span>
                      </el-link>
                    </div>
                    <el-divider class="action-divider"></el-divider>
                    <!-- PDF 下载和打印 -->
                    <div>
                      <el-link
                        :underline="false"
                        @click.stop="generatePDF(false)">
                        <i class="lg-icon lg-icon-download font-size-24 lg-margin-right-16"></i>{{$t('loc.download')}}
                      </el-link>
                    </div>
                    <div>
                      <el-link
                        :underline="false"
                        @click.stop="generatePDF(true)">
                        <i class="lg-icon lg-icon-print font-size-24 lg-margin-right-16"></i>{{$t('loc.plan174')}}
                      </el-link>
                    </div>
                    <el-divider class="action-divider"></el-divider>
                    <!-- 删除 -->
                    <div>
                      <el-link
                        class="action-delete"
                        :underline="false"
                        :disabled="planData.lockedData || !planData.canEditOrDelete || planRecording"
                        @click.stop="deletePlan()">
                        <i class="lg-icon lg-icon-delete font-size-24 lg-margin-right-16"></i>{{$t('loc.delete')}}</el-link>
                    </div>
                    <el-button v-if="planData && planData.id && edit" class="m-l-sm" icon="lg-icon lg-icon-more-horizontal" slot="reference" @click="morePopVisable=!morePopVisable"></el-button>
                  </el-popover>
                </div>
                <!-- 周计划模板的讲解 -->
              </div>
              <!-- 主题 -->
              <plan-category
                :canEditTemplate="canEditTemplate"
                :category="themeCategory"
                :edit="edit"
                :review="review"
                :draging="draging"
                :showBorder="!existInterpretation && edit && !curriculumInfo.curriculumId "
                :editTemplate="editTemplate"
                :showReflection="showLastReflection"
                @callAddCategory="addCategory"
                @callDeleteCategory="deleteCategory"
                @callUpdateTheme="updateTheme"
                @callUpdateCategory="updateCategory">
              </plan-category>
              <!-- 上周反思 -->
              <plan-category
                :canEditTemplate="canEditTemplate"
                :edit="edit"
                :review="review"
                :draging="draging"
                v-if="showLastReflection"
                :editTemplate="editTemplate"
                :category="reflectionCategory"
                :showReflection="showLastReflection"
                :lastReflection="lastReflection"
                :lastReflectionLoading="lastReflectionLoading"
                @callAddCategory="addCategory"
                @callSingleEditLesson="callSingleEditLesson"
                @callDeleteCategory="deleteCategory"
                @callUpdateCategory="updateCategory">
              </plan-category>
              <!-- 上半部分自定义内容 -->
              <plan-category
                :canEditTemplate="canEditTemplate"
                :category="category"
                :editTemplate="editTemplate"
                :frameworkData="frameworkData"
                :children="children"
                :edit="edit"
                :review="review"
                :draging="draging"
                v-for="category in topCategories" :key="category.id"
                @callOpenLessonModal="openLessonModal"
                @callOpenAddLessonModal="openAddLessonModal"
                @callAddCategory="addCategory"
                @callUpdateItem="autoExist"
                @callDeleteCategory="deleteCategory"
                @callDeleteItem="deleteItem"
                @callSingleEditLesson="callSingleEditLesson"
                @callUpdateCategory="updateCategory">
              </plan-category>
              <!-- 周信息 -->
              <plan-category
                :canEditTemplate="canEditTemplate"
                :edit="edit"
                :review="review"
                :draging="draging"
                :category="weekCategory"
                :editTemplate="editTemplate"
                @callAddCategory="addCategory"
                @callDeleteCategory="deleteCategory"
                @callSingleEditLesson="callSingleEditLesson"
                @callUpdateCategory="updateCategory">
              </plan-category>
              <!-- 下半部分自定义内容 -->
              <plan-category
                ref="planCategory"
                :showQuickAddLesson="showQuickAddLesson"
                :adminEdit="adminEdit"
                :canEditTemplate="canEditTemplate"
                :editTemplate="editTemplate"
                :category="category"
                :frameworkData="frameworkData"
                :children="children"
                :edit="edit"
                :mask="index"
                :firstActivityIndex="firstActivityIndex"
                :review="review"
                :draging="draging"
                :planType="planType"
                :canAddCenters="canAddCenters"
                :activeGuideDomId="activeGuideDomId"
                :planCenters="planCenters"
                :unitNumber="unitNumber"
                :isLocked="isLocked"
                :centerTags="centerTags"
                v-for="(category, index) in bottomCategories" :key="category.id"
                :lastRow="!showMeasureMap && index === bottomCategories.length - 1"
                @hideAllAiAddLessonGuide="hideAllAiAddLessonGuide"
                @callAddCenter="addCenter"
                @callDeleteCenter="deleteCenter"
                @callUpdateCenter="updateCenter"
                @callViewReflection="viewReflection"
                @callOpenLessonModal="openLessonModal"
                @callOpenAddLessonModal="openAddLessonModal"
                @callAddCategory="addCategory"
                @callUpdateItem="autoExist"
                @callBatchSave="batchSave"
                @callDeleteItem="deleteItem"
                @callAddItem="addItem"
                @callSingleEditLesson="callSingleEditLesson"
                @callDeleteCategory="deleteCategory"
                @callUpdateCategory="updateCategory">
              </plan-category>
              <!-- 框架映射信息 -->
              <plan-measure-map
                v-show="showMeasureMap"
                :categories="bottomCategories"
                :frameworkData="frameworkData"
                :reverse="isCAPTKLF"
                :mapFrameworkData="mapFrameworkData"
                :addMargin="canEditTemplate && (edit || review) && !draging"
                :class="{'m-l-md': editTemplate && canEditTemplate && (edit || review) && !draging}"
              ></plan-measure-map>
              <div id="step2-info" style="width: 220px;position:absolute;top: 0px;left: 20px;display: none;">
                <img src="~@/assets/img/lesson2/guide/plan_guide_step_2.svg" alt="weekly_plan_step_2">
              </div>
            </div>
            <!-- 反思内容 -->
            <div class="reflection-area lg-border-radius-8 lg-margin-top-24 ipad_min_width" v-if="!edit && !editTemplate && showPlanReflection && planData.status === 'D_APPROVED'" v-loading="reflectionLoading">
              <!-- 反思内容，如果是分享来的或者管理员查看，就只能查看，不可以编辑 -->
              <div v-if="isAdmin || isShared" class="plan-reflection-card lg-border-radius-8 bg-white">
                <div class="flex-row-between lg-padding-20 b-b">
                  <!-- 模块名 -->
                  <div class="title-font-18 lg-color-text-primary">
                    {{$t('loc.plan15')}}
                  </div>
                  <!-- 周反思列表入口 -->
                  <div>
                    <weekly-reflection-list buttonType="primary" :centers="centers" :groupId="currentGroupId" :centerId="currentCenterId" :buttonPlain="true"></weekly-reflection-list>
                  </div>
                </div>
                <div class="lg-padding-20" v-if="reflectionText && reflectionText.trim().length > 0">
                  {{reflectionText}}
                </div>
                <!-- 没有反思内容 -->
                <div class="display-flex justify-content align-items flex-direction-col lg-padding-20" v-if="!reflectionLoading && !reflectionText && reflectionText.trim().length === 0">
                  <img src="@/assets/img/lesson2/plan/no_record.png"/>
                  <span class="lg-color-text-secondary">{{$t('loc.plan16')}}</span>
                </div>
              </div>
              <div v-else class="bg-white plan-reflection-card lg-border-radius-8">
                <div class="flex-row-between lg-padding-20 b-b">
                  <!-- 模块名 -->
                  <div class="title-font-18 lg-color-text-primary">
                    {{$t('loc.plan15')}}
                  </div>
                  <!-- 周反思列表入口 -->
                  <div>
                    <weekly-reflection-list buttonType="plain" :centers="centers" :groupId="currentGroupId" :centerId="currentCenterId"></weekly-reflection-list>
                  </div>
                </div>
                <div class="lg-padding-20">
                  <el-input type="textarea" :rows="3" style="color: #111c1c;" :class="{'error-input': reflectionText.length > 1000}"
                    :readonly="isAdmin"
                    :placeholder="$t('loc.plan43')" v-model="reflectionText">
                  </el-input>
                  <span class="text-danger" v-if="reflectionText.length > 1000">
                    {{$t('loc.plan17', {num: '1000'})}}
                  </span>
                  <div class="text-right m-t-sm">
                    <el-button type="primary" size="medium" @click="saveReflection()" :loading="saveReflectionLoading">{{$t('loc.save')}}</el-button>
                  </div>
                </div>
              </div>
              <!-- 课程反思 -->
              <lesson-reflection-list class="lg-margin-top-20" ref="lessonReflectionList" :planId="planId" @callDeleteReflection="deleteReflection"></lesson-reflection-list>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核通过弹窗 -->
      <el-dialog :title="$t('loc.plan62')" :close-on-click-modal=false :visible.sync="approveModalVisible" custom-class="modal-p-x">
        <el-input type="textarea" :rows="3" class="font-size-16" :class="{'error-input': approveText.length > 1000}"
          :placeholder="$t('loc.plan38')" v-model="approveText">
        </el-input>
        <span class="text-danger" v-if="approveText.length > 1000">
          {{$t('loc.plan17', {num: '1000'})}}
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button plain @click="approveModalVisible = false">{{$t('loc.cancel')}}</el-button>
          <el-button type="primary" @click="approve" :loading="approveLoading">{{$t('loc.confirm')}}</el-button>
        </span>
      </el-dialog>
      <!-- 审核拒绝弹窗 -->
      <el-dialog :title="$t('loc.plan63')" :close-on-click-modal=false :visible.sync="rejectModalVisible" custom-class="modal-p-x">
        <el-input type="textarea" :rows="3" class="font-size-16" :class="{'error-input': rejectText.length > 1000 || !rejectText && rejectTextError}"
          :placeholder="$t('loc.plan37')" v-model="rejectText">
        </el-input>
        <div class="text-danger" v-if="rejectText.length > 1000">
          {{$t('loc.plan17', {num: '1000'})}}
        </div>
        <div class="text-danger m-t-sm" v-if="(!rejectText || rejectText.trim().length === 0) && rejectTextError">
          {{$t('loc.plan20')}}
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button plain @click="rejectModalVisible = false">{{$t('loc.cancel')}}</el-button>
          <el-button type="primary" @click="reject" :loading="rejectLoading">{{$t('loc.confirm')}}</el-button>
        </span>
      </el-dialog>
      <!-- 继续审核弹窗 -->
      <el-dialog width="600px" :show-close=false :close-on-click-modal=false :close-on-press-escape=false :visible.sync="continueModalVisible" custom-class="modal-p-x">
        <div class="text-center">
          <div class="font-size-20 m-b-sm">
            <i v-if="approveStatus === 'D_APPROVED'" class="el-icon-success text-success m-r-sm"></i>
            <i v-if="approveStatus === 'C_REJECTED'" class="el-icon-error text-danger m-r-sm"></i>
            <span v-if="approveStatus === 'D_APPROVED'">{{$t('loc.planApproved')}}!</span>
            <span v-if="approveStatus === 'C_REJECTED'">{{$t('loc.plannerReviseNewTip')}}!</span>
          </div>
          <span class="font-size-16 text-black-mon">{{$t('loc.plan21')}}</span>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button plain @click="finishApprove">{{$t('loc.plan22')}}</el-button>
          <el-button type="primary" @click="nextApprove">{{$t('loc.plan23')}}</el-button>
        </span>
      </el-dialog>
      <!-- 审核完成弹窗 -->
      <el-dialog width="600px" :show-close=false :close-on-click-modal=false :close-on-press-escape=false :visible.sync="completeModalVisible" custom-class="modal-p-x">
        <div class="text-center">
          <div class="font-size-20 m-b-sm">
            <i class="el-icon-success text-success m-r-sm"></i>
            <span>{{$t('loc.plan24')}}</span>
          </div>
        </div>
        <span slot="footer" class="dialog-footer text-center">
          <el-button type="primary" @click="completeApprove">{{$t('loc.ok')}}</el-button>
        </span>
      </el-dialog>

      <!-- 选择课程弹窗 -->
      <select-lesson-modal
        @callSelectLesson="selectLesson"
        ref="selectLessonModal"
        ></select-lesson-modal>

      <!-- 提交提示弹窗 -->
      <submit-plan-tip
        @callConfirmTip="confirmTip"
        ref="submitTipModal"
        ></submit-plan-tip>

      <!-- 快速添加课程弹窗 -->
      <quick-add-lesson
      v-if="showQuickAddLesson"
      @callSelectLesson="selectLesson"
      ref="addLessonModal"
      ></quick-add-lesson>

      <!-- 快速生成课程弹窗 -->
      <quick-generate-lesson
        @callSelectLesson="selectLesson"
        :selectedFramework="selectedFramework"
        ref="generateLessonModal"
      ></quick-generate-lesson>

      <!-- 提交周计划提醒 -->
      <plan-apply-records
      ref="applyRecordsModal"
      ></plan-apply-records>
      <!-- 展示 LessonEditor，当点击了生成内容之后，Editor 显示  -->
      <LessonEditor ref="lessonEditor" :planId="planId" :lessonInfo="planLessons" :planType="planType" :preview="viewPlan" :planStatus="planStatus"/>
      <!--  展示 PersonalizePlan-->
      <PersonalizePlan ref="personalizePlan"
                       :planId="planId"
                       :itemIds="[currentItemId]"
                       @updateGenerateUniversalDesignAndCLRData="updateGenerateUniversalDesignAndCLRData"/>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import ActivityCard from './ActivityCard'
import PlanCategory from './PlanCategory'
import SelectLessonModal from './SelectLessonModal'
import SubmitPlanTip from './SubmitPlanTip'
import PlanToolbar from './PlanToolbar'
import LessonReflectionList from './LessonReflectionList'
import tools from '@/utils/tools'
import { PublicLessonAssistantInfoAgeGroup, } from '@/utils/constants'
import WeeklyReflectionList from '@/views/modules/lesson2/component/WeeklyReflectionList.vue'
import PlanInterpretation from '@/views/modules/lesson2/lessonPlan/components/PlanInterpretation'
import PlanNoteCreator from '@/views/modules/lesson2/lessonPlan/components/PlanNote/Creator'
import QuickAddLesson from '@/views/modules/lesson2/lessonPlan/components/QuickAddLesson.vue'
import QuickGenerateLesson from '@/views/modules/lesson2/lessonPlan/components/QuickGenerateLesson.vue'
import AdapterTips from '@/views/modules/lesson2/lessonBatchAdapter/AdapterTips.vue'
import BatchEditPlan from '@/views/modules/lesson2/lessonBatchAdapter/BatchEditPlan.vue'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
// 引导
import Driver from 'driver.js' // import driver.js
import 'driver.js/dist/driver.min.css' // import driver.js css
import steps from '@/assets/js/guide/weeklyPlanner'
import DllSettings from '@/views/modules/lesson2/lessonLibrary/components/WeeklyPlan/DllSettings'
import FamilyResourcePreview from '@/views/modules/lesson2/lessonPlan/components/FamilyResourcePreview'
import PlanApplyRecords from '@/views/modules/lesson2/lessonPlan/components/PlanApplyRecords'
import LessonDrag from '@/views/modules/lesson2/lessonPlan/components/LessonDrag'
import Sortable from 'sortablejs/modular/sortable.core.esm.js'
import PersonalizePlan from '@/views/modules/lesson2/lessonPlan/components/PersonalizePlan.vue'
import LessonEditor from '@/views/modules/lesson2/lessonBatchAdapter/LessonEditor.vue'
import {equalsIgnoreCase} from "@/utils/common";
import PlanMeasureMap from '@/views/modules/lesson2/lessonPlan/components/PlanMeasureMap.vue'
import frameworkUtils from '@/utils/frameworkUtils'

export default {
  name: 'PlanCalendar',
  components: {
    LessonEditor,
    PersonalizePlan,
    DllSettings,
    FamilyResourcePreview,
    AdapterTips,
    BatchEditPlan,
    WeeklyReflectionList,
    PlanNoteCreator,
    PlanInterpretation,
    ActivityCard,
    PlanCategory,
    SelectLessonModal,
    SubmitPlanTip,
    PlanToolbar,
    LessonReflectionList,
    QuickAddLesson,
    PlanApplyRecords,
    LessonDrag,
    QuickGenerateLesson,
    PlanMeasureMap
  },

  props: {
    showQuickAddLesson: {
      type: Boolean,
      default: true
    },
    adminEdit: {
      type: Boolean
    },
    edit: {
      type: Boolean
    },
    review: {
      type: Boolean // 是否是管理员审批
    },
    preview: {
      type: Boolean
    },
    planId: {
      type: String
    },
    firstVisit: {
      type: Boolean
    },
    editTemplate: {
      type: Boolean
    },
    // 周计划状态
    planStatus: {
      type: String,
      default: ''
    },
    // 是否锁定（其他人正在编辑周计划）
    isLocked: {
      type: Object,
      default: null
    },
    viewPlan: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      calendarLoading: true,
      theme: undefined,
      activityPopoverVisible: false,
      textarea1: undefined,
      weekDays: [],
      planData: {},
      categories: [],
      fixedRows: [{}, {}, {}],
      themeCategory: {},
      reflectionCategory: {},
      weekCategory: {},
      centerCategory: {},
      frameworkData: [],
      mapFrameworkData: [], // 框架映射测评点信息
      showMeasureMap: true, // 是否显示测评点映射
      children: [],
      approveModalVisible: false,
      rejectModalVisible: false,
      continueModalVisible: false,
      completeModalVisible: false,
      existModalVisible: false,
      nextPlanId: undefined,
      approveText: '',
      rejectText: '',
      rejectTextError: false,
      reflectionText: '',
      showPlanReflection: false,
      lessonType: 'CREATION',
      searchLessonStr: '',
      selectLessonVisible: false,
      myLessons: [],
      favoritesLessons: [],
      selectLessonFunc: undefined,
      saveReflectionLoading: false,
      submitLoading: false,
      revertLoading: false,
      saveDraftLoading: false,
      reflectionLoading: false,
      approveLoading: false,
      rejectLoading: false,
      lastReflection: '',
      lastReflectionLoading: false,
      planShareRecords: [], // 周计划对应的分享记录
      hideSubmitTip: false, // 是否隐藏提交周计划提示
      timeoutID: undefined,
      approveStatus: '',
      collapse: false,
      activeGuideDomId: '',
      getSharePlannerLoading: false, // 周计划分页加载loading
      totalPlanner: 0, // 分享来的周计划总数
      pageNum: 1, // 查询周计划页数
      hasNextPage: true, // 是否还有下一页周计划
      sharedWeeks: [], // 分享来的周计划
      currentWeekPlan: {}, // 当前的周计划
      hasLastWeekPlan: false, // 有无上一个周计划
      hasNextWeekPlan: false, // 有无下一个周计划
      hasShared: true, // 有无分享的老师
      sharedTeachers: [], // 分享的老师列表
      currentTeacher: {}, // 当前老师
      timmer: null, // 定时器
      replicateModelVisible: false, // 复制周计划弹窗
      replicateLoading: false, // 复制按钮loading
      selectedGroupId: '', // 周计划复制选择的班级
      showNotSelectGroupTip: false, // 复制周计划选择的班级提示
      centers: [], // 当前老师所在的学校,
      pdfLoading: false,
      planNote: null,
      currentGroupId: '', // 查看的周计划的班级Id
      currentCenterId: '', // 查看的周计划的学校Id
      driver: null, // 引导插件对象
      canEditTemplate: false, // 是否可以编辑模板
      frameworks: [],
      frameworkId: '',
      selectedFramework: null, // 周计划的框架和年级
      ageGroups: PublicLessonAssistantInfoAgeGroup,
      planCenters: [],
      unitNumber: undefined,
      centerTags: [],
      existInterpretation: false, // 是否有周计划讲解
      showCore: true, // 是否仅显示核心测评点
      showCoreMeasureOpen: false,
      showLastReflection: false, // 是否显示上周反思
      draging: false, // 是否拖拽课程
      customThemeRowName: '', // 自定义主题行名称
      previewResourceLoading: false, // 预览家庭资源loading
      submited: false, // 是否已提交
      morePopVisable: false, // 更多操作 popover
      confirmReplicateModelVisible: false, // 确认复制周计划弹窗
      planLessons: [], // 周计划课程
      currentItemId: '', // 当前课程 ItemId
      showAdaptNewTag: false, // 是否显示新的适应性课程标签
      itemPlanLessons: [], // 课程计划
      planType: '', // 周计划类型
      allowUnLock: true // 是否允许解锁
    }
  },

  created () {
    this.getSingleAdaptLessonGuide()
    // 管理员审批模式下初始化拖拽
    const review = this.$route.query.review
    // 等待planItem组件挂载完成,通知是否显示核心测评点
    this.$bus.$on('itemMounted', () => {
      this.showCore = this.planData.showCoreMeasureOpen
      this.$bus.$emit('changeShowCore', { planId: this.planId, open: this.showCore })
      // 编辑模式下初始化拖拽
      if (this.edit || review) {
        // item 挂在完成后初始化拖拽逐渐
        this.initDrag()
      }
    })
    let collapse = sessionStorage.getItem('plan-left-menu-collapse')
    if (collapse) {
      this.collapse = JSON.parse(collapse)
    }
    this.loadFrameworks()
    this.getCenterGroups()
    this.initWeekDays()
    if (this.preview) {
      this.edit = false
      return
    }
    if (this.planId) {
      this.getPlan()
    }
    this.initFixedRow()
    if (this.edit) {
      this.autoExist()
    }
    // 锁定周计划
    if (this.edit || this.review) {
      // 必须周计划 ID 不为空时才进行周计划锁定
      if (this.planId) {
        // 编辑模式锁定周计划
        LessonApi.lockPlan(this.planId, this.currentUser.user_id)
      }
    }
  },
  mounted () {
    // 组件挂载完成后初始化 bus 事件
    this.singleAdaptLesson()
  },

  computed: {
    ...mapState({
      open: state => state.common.open,
      currentUser: state => state.user.currentUser,
      planRecording: state => state.lesson.planRecording,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      planFrameworks: state => state.lesson.frameworks, // 周计划模板可选的框架
      weeklyCreateAILessonOpen: state => state.common.open ? state.common.open.weeklyCreateAILessonOpen : false, // 是否开启了 AI 生成周计划
    }),
    // 周计划日历高度
    planCalendarHeight () {
      // 顶部设置上周反思开关和跳转单元课程按钮
      let hasHeader = (!this.isShared && this.curriculumInfo.curriculumId) || (this.edit && this.canEditTemplate && this.planData.type == 'NORMAL') || this.review
      // 是否有提交按钮
      let hasFooter = this.edit || (!this.edit && this.planData.status === 'B_PENDING')
      if (hasHeader && hasFooter) {
        return 'calc(100% - 124px)'
      } else if (!hasHeader && hasFooter) {
        return 'calc(100% - 64px)'
      } else {
        return '100%'
      }
    },
    dllOpen () {
      return this.open && this.open.dllopen
    },
    curriculumInfo () {
      let curriculum = {
        curriculumId: this.planData.curriculumId,
        unitPlanNumber: this.planData.unitPlanNumber,
        unitId: this.planData.unitId,
        unitNumber: this.planData.unitNumber,
        unitTitle: this.planData.unitTitle
      }
      return curriculum
    },
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    topCategories () {
      return this.categories.filter(c => c.type && c.type.indexOf('TOP_') != -1)
    },
    bottomCategories () {
      return this.categories.filter(c => c.type && c.type.indexOf('BOTTOM_') != -1)
    },
    isAdmin () {
      if (!this.currentUser) {
        return false
      }
      let role = this.currentUser.role
      return role && role.toUpperCase() === 'OWNER'
    },
    isAgencyOwner () {
      if (!this.currentUser) {
        return false
      }
      let role = this.currentUser.role2
      return role && role.toUpperCase() === 'AGENCY_OWNER'
    },
    lessons () {
      if (this.lessonType == 'CREATION') {
        return this.myLessons
      } else {
        return this.favoritesLessons
      }
    },

    reflectionConvertText () {
      if (!this.reflectionText) {
        return ''
      }
      return this.reflectionText.replace(/\n/g, '<br />')
    },
    isShared () {
      // 如果过是查看分享，获取分享来的用户
      if (this.$route.name === 'teacherShadow') {
        this.getShareUsers()
        return true
      }
      return false
    },

    // 管理员分享的周计划详情
    isSharedDetail () {
      if (this.$route.name === 'view-plan' && this.$route.query.shareId) {
        return true
      }
      return false
    },

    shareRecords () {
      if (this.planShareRecords && this.planShareRecords.length > 0) {
        return this.planShareRecords
      }
      return this.currentWeekPlan ? this.currentWeekPlan.shareRecords : []
    },

    // 是否正在引导
    isGuiding () {
      if (this.driver) {
        return this.driver.hasHighlightedElement()
      } else {
        return false
      }
    },
    canAddCenters () {
      return this.categories.filter(x => x.type === 'BOTTOM_CENTER_ROW').length === 0
    },
    // 能否管理周计划讲解
    canManage () {
      // agency owner 角色和周计划的创建者有权限
      if (this.currentUser && this.planData) {
        // 是否是创建者
        let isAuthor = this.planData.teacherIds && this.planData.teacherIds.includes(this.currentUser.user_id)
        // 是否是 agency owner
        let isAgencyOwner = this.currentUser && this.currentUser.role2.toUpperCase() === 'AGENCY_OWNER'
        return isAuthor || isAgencyOwner
      }
      return false
    },
    // 是否只读周计划讲解
    viewOnly () {
      // agency owner 角色和周计划的创建者有权限
      if (this.currentUser && this.planData) {
        // 是否是创建者
        let isAuthor = this.planData.teacherIds && this.planData.teacherIds.includes(this.currentUser.user_id)
        // 是否是 agency owner
        let isAgencyOwner = this.currentUser && this.currentUser.role2.toUpperCase() === 'AGENCY_OWNER'
        return !isAuthor && !isAgencyOwner
      }
      return true
    },
    // 是否添加周计划讲解
    canAdd () {
      if (this.planData) {
        if (this.planData.type == 'NORMAL') {
          return this.planData.status == 'D_APPROVED' && !this.edit
        } else {
           // 审核通过且非编辑状态且是机构owner或者作者自己
          return this.planData.status == 'D_APPROVED' &&
          !this.edit &&
          (this.isAgencyOwner ||
          (this.planData.teacherIds &&
          this.planData.teacherIds.includes(this.currentUser.user_id)))
        }
      }
      return false
    },
    isComeFromIPad () {
      return tools.isComeFromIPad()
    },
    // 查询第一个能通过 Add Activity 添加课程的模块
    firstActivityIndex () {
      // 能通过 Add Activity 添加课程的模块
      const hasActivityCategorieItems = ['BOTTOM_DAY_COL', 'BOTTOM_WEEK_COL']
      return this.bottomCategories.findIndex(categorie =>
        hasActivityCategorieItems.includes(categorie.type)
      )
    },
    // 是否是 CAPTKLF 框架的周计划
    isCAPTKLF () {
      return frameworkUtils.isCAPTKLF(this.frameworkId)
    }
  },
  methods: {
    // 关闭所有 AI 生成周计划的引导
    hideAllAiAddLessonGuide () {
      const planCategorys = this.$refs.planCategory
      if (planCategorys) {
        // 判断是否获取到多个对象
        if (typeof planCategorys[Symbol.iterator] === 'function') {
          for (const planCategory of planCategorys) {
            planCategory.hideBeforeAiAddLessonGuide()
          }
        } else {
          planCategorys.hideBeforeAiAddLessonGuide()
        }
      }
    },
    handleAddChildrenTips () {
      this.$emit('handleAddChildrenTips')
    },
    // 隐藏 New Tag 标签
    hideNewTag () {
      this.showAdaptNewTag = false
    },
    // 获取单个适配课程引导
    getSingleAdaptLessonGuide () {
      // 首先从本地缓存中获取是否存在引导值
      const showPreviewPlanDetailAdaptGuide = localStorage.getItem(this.currentUser.user_id + 'SHOW_PREVIEW_PLAN_DETAIL_ADAPT_GUIDE')
      const showBatchAdaptNewTagGuide = localStorage.getItem(this.currentUser.user_id + 'BATCH_ADAPTED_NEW_TAG_GUIDE')
      // 如果不存在时调用接口获取相应的引导状态值
      if (!showPreviewPlanDetailAdaptGuide || !showBatchAdaptNewTagGuide) {
        this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
          if (result) {
            this.showAdaptNewTag = result.showBatchAdaptNewTagGuide
            localStorage.setItem(this.currentUser.user_id + 'SHOW_PREVIEW_PLAN_DETAIL_ADAPT_GUIDE', result.showPreviewPlanDetailAdaptGuide)
            localStorage.setItem(this.currentUser.user_id + 'BATCH_ADAPTED_NEW_TAG_GUIDE', result.showBatchAdaptNewTagGuide)
          }
        })
      } else {
        // 存在时直接从缓存中获取
        this.showAdaptNewTag = JSON.parse(showBatchAdaptNewTagGuide)
      }
    },
    /**
     * 关闭复制弹窗
     */
    closeReplicateModel () {
      this.confirmReplicateModelVisible = false
    },

    /**
     * 确认复制周计划
     */
    confirmReplicate () {
      LessonApi.replicatePlan({
        planId: this.planId
      }).then(response => {
        this.confirmReplicateModelVisible = false
        let routeName = 'edit-template'
        if (this.planData.type === 'NORMAL_TEMPLATE') {
          routeName = 'edit-agency-template'
        }
        this.$router.push({
          name: routeName,
          params: {
            planId: response.id
          }
        })
      })
    },

    /**
     * 家庭资源预览
     */
    openPlanResourcePreview () {
      this.$refs.familyResourcePreview.handlePreview()
    },

    /**
     * 打开 DLL 语言设置弹窗
     */
    openDllSettings () {
      this.$refs.dllSettings.openSettingDialog()
    },

    /**
     * 跳转单元课程
     */
    async goToCurriculum () {
      if (this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_virtual_detail_curriculum')
      } else if (this.$route.name == 'view-plan') {
        this.$analytics.sendEvent('web_weekly_plan_detail_click_curriculum')
      }
      // 如果正在录制，提示是否退出录制
      if (this.planRecording) {
        this.$message.warning(this.$t('loc.plan145'))
        return
      }
      this.$router.push({
        name: 'curriculumUnitDetail',
        params: {
          unitId: this.curriculumInfo.unitId,
          curriculumId: this.curriculumInfo.curriculumId,
          weekNum: this.curriculumInfo.unitPlanNumber,
          planId: this.planId,
          edit: this.edit,
          frameworkId: null,
          curriculumName: null
        }
      })
    },
    goToUnitInfo () {
      // 如果正在录制，提示是否退出录制
      if (this.planRecording) {
        this.$message.warning(this.$t('loc.plan145'))
        return
      }
      const routerName = this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail'
      const { href } = this.$router.resolve({
        name: routerName,
        query: {
          unitId: this.curriculumInfo.unitId
        }
      })
      window.open(href, '_blank')
    },

    /**
     * 更新显示上周反思开关
     */
    changeLastReflection () {
      this.$emit('callChangeLastReflection')
    },

    /**
     * 初始化周信息
     */
    initWeekDays () {
      this.weekDays = [
        {
          day: 1,
          name: 'Monday'
        }, {
          day: 2,
          name: 'Tuesday'
        }, {
          day: 3,
          name: 'Wednesday'
        }, {
          day: 4,
          name: 'Thursday'
        }, {
          day: 5,
          name: 'Friday'
        }
      ]
    },

    /**
     * 初始化固定行
     */
    initFixedRow () {
      this.customThemeRowName = this.planData && this.planData.customThemeRowName
      this.themeCategory = {
        type: 'THEME_ROW',
        name: (this.planData && this.planData.customThemeRowName) || this.$t('loc.plan4'),
        items: [{
          name: this.theme
        }],
        sortNum: 0,
        guideRowDomId: 'guide-weekly-plan-theme'
      }
      this.reflectionCategory = {
        type: 'REFLECTION_ROW',
        name: this.$t('loc.plan25'),
        items: [{
          name: this.theme
        }],
        sortNum: 0
      }
      this.centerCategory = {
        type: 'CENTER_ROW',
        name: 'Centers',
        sortNum: 0
      }
      this.weekCategory = {
        type: 'WEEK_ROW',
        sortNum: 0
      }
    },

    setPreviewData (previewData) {
      this.planData = previewData
      this.theme = previewData.theme
      this.categories = previewData.categories
      this.initFixedRow()
      this.calendarLoading = false
      this.$forceUpdate()
    },

    // 获取活动项的课程
    getPlanItemLessons () {
      const params = {
        planId: this.planId,
        includeCenterLesson: true
      }
      this.$axios.get($api.urls().getPlanLessons, { params: params }).then(res => {
        this.itemPlanLessons = res.lessons
        this.categories.forEach(category => {
          const items = category.items || []
          items.forEach(item => {
            this.itemPlanLessons.forEach(lesson => {
              if (equalsIgnoreCase(lesson.lessonId, item.lessonId) && equalsIgnoreCase(lesson.itemId, item.id)) {
                this.$set(item, 'isAdaptedLesson', lesson.isAdaptedLesson)
              }
            })
          })
        })
      })
    },

    /**
     * 获取周计划详情
     */
    getPlan (isShared) {
      this.calendarLoading = true
      this.planData.status = undefined
      // 获取周计划参数
      let params = {
        id: this.planId,
        edit: this.edit || this.$route.query.review
      }
      // 如果是分享来的，参数加上创作者id
      if (isShared) {
        params = {
          id: this.currentWeekPlan.id,
          edit: this.edit,
          shareUserId: this.currentTeacher.id
        }
        // 获取周计划前，先判断定时器是否为空，不空则清除定时任务
        if (this.timmer) {
          clearTimeout(this.timmer)
        }
      }
      // 当前页面类型
      let viewType = 'NORMAL_VIEW'
      if (this.$route.query) {
        let shareId = this.$route.query.shareId
        if (shareId && this.isAdmin) {
          viewType = 'SHARE_VIEW'
          params.shareId = shareId
        }
      }
      params.source = viewType
      // 调用接口获取周计划详情
      LessonApi.getPlan(params).then(planData => {
        this.planType = planData.type
        this.$nextTick(() => {
          this.showCore = planData.showCoreMeasureOpen
        })
        // 能否编辑模板
        this.canEditTemplate = planData.canEditTemplate
        // 如果是查看分享的周计划，不能直接定位到这个周计划的班级，否则，可直接看这个班的周反思
        if (isShared) {
          this.currentGroupId = ''
          this.currentCenterId = ''
        } else {
          this.currentGroupId = planData.groupId
          this.currentCenterId = planData.centerId
        }
        this.showCoreMeasureOpen = planData.showCoreMeasureOpen
        this.centerTags = planData.centerTags
        this.planData = planData
        this.showLastReflection = planData.showLastReflection
        this.showMeasureMap = planData.showMeasureMap
        this.theme = planData.theme
        this.categories = planData.categories
        this.planCenters = planData.planCenters
        this.$store.commit('SET_PLANCENTERTYPE', planData.planCenterType)
        this.frameworkId = planData.frameworkId
        const grade = this.ageGroups.find(item => item.name === planData.grade)
        this.selectedFramework = {
          grade: grade && grade.value,
          frameworkId: planData.planFrameworkId,
          frameworkName: planData.frameworkName
        }
        // 如果没有设置框架，默认第一个
        if (this.editTemplate && (this.frameworkId === null || this.frameworkId === '')) {
          this.updateFramework(this.frameworks[0].frameworkId)
        } else if (this.frameworkId) {
          this.loadMeasures(this.frameworkId)
        }
        this.getPlanItemLessons()
        this.unitNumber = planData.unitNumber
        // 获取框架下测评点信息
        this.getFrameworkData(planData.groupId)
        // 获取班级下小孩信息
        this.getChildren(planData.groupId)
        // 更新分类索引值
        this.updateCategoryIndex()
        // 数据传给父组件，用于头部显示
        this.$emit('setPlanInfo', this.planData)
        this.initFixedRow()
        // 开始的周计划可以写反思
        let fromDate = planData.fromAtLocal
        this.showPlanReflection = fromDate && this.$moment(fromDate).isSameOrBefore(this.$moment())
        this.calendarLoading = false
        // 如果需要显示反思内容，则显示反思
        if (this.showPlanReflection) {
          this.getReflection()
        }
        // 上周反思
        this.getLastReflection(planData.week)
        // 是否隐藏提交周计划提示
        this.hideSubmitTip = planData.hideSubmitTip
        // 如果是查看分享，等待5s,调用已读接口
        if (isShared) {
          this.timmer = setTimeout(() => {
            this.$axios.post($api.urls().readPlan,{
              planId: this.currentWeekPlan.id
            })
          }, 5000)
        }
        // 显示分享记录
        if (viewType === 'SHARE_VIEW') {
          this.planShareRecords = planData.shareRecords
        }
        this.setGuide()
        // 判断是否存在 planLesson
        this.validateBatchAdapterLesson(planData)
      }).catch(error => {
        // 其他人正在编辑，跳转到查看页
        if (error && error.response && error.response.data.error_code === 'plan_editing') {
          this.edit = false
          let routeName = 'view-plan'
          // 如果是在编辑模板，跳转到查看模板
          if (this.$route.name == 'edit-template') {
            routeName = 'view-template'
          }
          // 如果有人在编辑周计划，是不允许解锁的
          this.allowUnLock = false
          this.$router.replace({
            name: routeName,
            params: {
              planId: this.planId,
              refresh: this.$route.name == 'view-plan'
            }
          })
        }
        // 周计划被删除
        if (error && error.response && error.response.data.error_code === 'plan_deleted') {
          this.$message.error(this.$t('loc.planHasBeenDeleted'))
          // 跳转到计划列表页
          this.$router.replace({
            name: 'list-plan'
          })
        }
      })
    },
    /**
     * 获取周计划下的课程列表数据
     */
    hasLessons () {
      // 判断是否存在 planData
      if (this.planData && this.planData.categories) {
        // 获取所有不是 center 课程的分类
        let categories = this.planData.categories
        // 定义是否存在 planLesson
        let hasBatchAdapterLesson = false
        // 循环遍历所有的分类
        for (let i = 0; i < categories.length; i++) {
          // 获取每个分类的课程
          let category = categories[i]
          // 如果 item 存在
          if (category.items && category.items.length > 0) {
            // 获取所有的课程
            let items = category.items
            // 循环遍历所有的课程
            for (let j = 0; j < items.length; j++) {
              // 获取每个课程
              let item = items[j]
              // 如果课程存在，并且 lessonId 不为空
              if (item && item.lessonId && item.lessonId !== '') {
                hasBatchAdapterLesson = true
                break
              }
            }
          }
        }
        return hasBatchAdapterLesson
      }
    },

    /**
     * 校验是否存在可以批量改编的 planLesson
     */
    validateBatchAdapterLesson () {
      // 判断是否存在 planData
      if (this.planData && this.planData.categories) {
        // 获取所有不是 center 课程的分类
        let categories = this.planData.categories.filter(category => category.type !== 'BOTTOM_CENTER_ROW')
        // 定义是否存在 planLesson
        let hasBatchAdapterLesson = false
        // 循环遍历所有的分类
        for (let i = 0; i < categories.length; i++) {
          // 获取每个分类的课程
          let category = categories[i]
          // 如果 item 存在
          if (category.items && category.items.length > 0) {
            // 获取所有的课程
            let items = category.items
            // 循环遍历所有的课程
            for (let j = 0; j < items.length; j++) {
              // 获取每个课程
              let item = items[j]
              // 如果课程存在，并且 lessonId 不为空
              if (item && item.lessonId && item.lessonId !== '') {
                hasBatchAdapterLesson = true
                break
              }
            }
          }
        }
        return hasBatchAdapterLesson
      }
    },
    /**
     * 设置引导信息
     */
    setGuide () {
      if (!this.firstVisit) {
        return
      }
      this.firstVisit = false
      // 是否有目标行
      let hasGoal = false
      // 第一个活动项目
      let firstItem
      // 遍历分类
      this.categories.forEach(category => {
        let type = category.type
        let name = category.name
        // 找到目标行，设置引导使用的 DOM ID
        if (!hasGoal && type && name && type.indexOf('TOP_') != -1) {
          hasGoal = true
          // 设置目标行引导步骤标题
          steps[1].popover.title = this.$t('loc.plan169')
          category.guideRowDomId = 'guide-weekly-plan-goal'
          category.guideCategoryInputDomId = 'guide-weekly-plan-editTip'
          category.guideOperationDomId = 'guide-weekly-plan-add-del-tooltip'
        }
        // 找到第一个活动项目
        if (!firstItem && type.indexOf('BOTTOM_') != -1) {
          // 如果分组类型是整行的，将lesson引导弹窗显示到下面
          if (type == 'BOTTOM_WEEK_COL') {
            steps[2].popover.position = 'bottom'
          }
          category.items.forEach(item => {
            if (item && item.dayOfWeek == 1) {
              firstItem = item
            }
          })
          // 如果分类中没有项目，默认添加一个
          if (!firstItem) {
            firstItem = {
              dayOfWeek: 1,
              categoryId: category.id,
              name: '',
              planId: category.planId
            }
            category.items.push(firstItem)
          }
          // 设置引导使用的 DOM ID
          firstItem.guideItemDomId = 'guide-weekly-plan-lesson'
        }
      })
      // 创建引导插件对象
      this.driver = new Driver({
        className: 'guide-style',
        opacity: 0.5, // 屏蔽罩透明度
        allowClose: false,
        closeBtnText: this.$t('loc.planGuideSkip'),
        nextBtnText: this.$t('loc.planGuideNext'),
        doneBtnText: this.$t('loc.planGuideDone'),
        // 引导内容高亮前执行的方法
        onHighlightStarted: (element) => {
          // 添加分类按钮
          let categoryAddDom = document.getElementById('guide-weekly-plan-add-del-tooltip-add')
          // 移除分类按钮
          let categoryRemoveDom = document.getElementById('guide-weekly-plan-add-del-tooltip-remove')
          // 当前引导元素 DOM
          let dom = element.node
          // 当前引导元素 DOM ID
          let domId = dom.id
          // 分类添加、删除引导
          if (domId === 'guide-weekly-plan-add-del-tooltip') {
            // 元素设为可见
            dom.style.display = 'block'
            // 添加、删除按钮样式修改
            if (categoryAddDom) {
              categoryAddDom.classList.add('add-category-hover')
            }
            if (categoryRemoveDom) {
              categoryRemoveDom.classList.add('remove-category-hover')
            }
          }
          // 将上一步修改的 DOM 属性还原
          if (domId === 'guide-weekly-plan-editTip') {
            let operationDom = document.getElementById('guide-weekly-plan-add-del-tooltip')
            // 隐藏 DOM
            if (operationDom) {
              operationDom.style.display = 'none'
            }
            // 还原添加、删除按钮样式
            if (categoryAddDom) {
              categoryAddDom.classList.remove('add-category-hover')
            }
            if (categoryRemoveDom) {
              categoryRemoveDom.classList.remove('remove-category-hover')
            }
          }
          // 最后一步，添加边框class
          if (domId === 'guide-weekly-plan-editTip') {
            var editDom = document.getElementById('guide-weekly-plan-editTip')
            editDom.children[0].classList.add('row-border')
          }
          if (domId === 'step2-info') {
            dom.style.display = 'block'
          }
          if (domId === 'step3-info') {
            dom.style.display = 'block'
            const step2 = document.getElementById('step2-info')
            step2.style.display = 'none'
          }
          // 设置当前的引导元素 ID
          this.activeGuideDomId = domId
        },
        // 完全高亮时执行的方法
        onHighlighted: (element) => {
          // 如果没有目标，引导共两步，第一步执行后就将高亮的元素切换到课程上
          if (!hasGoal) {
            this.activeGuideDomId = 'guide-weekly-plan-lesson'
          }
          // 当第二步高亮的时候，提前将第三步要高亮的元素切换，防止监听不及时样式不正确
          if (element.node.id === 'guide-weekly-plan-goal') {
            this.activeGuideDomId = 'guide-weekly-plan-lesson'
          }
        },
        // 引导步骤完成时执行的方法
        onDeselected: (element) => {
          // 当前引导元素 DOM
          let dom = element.node
          // 当前引导元素 DOM ID
          let domId = dom.id
          if (domId === 'toolbar-area') {
            this.$analytics.sendEvent('web_weekly_plan_guide_click_step1_next')
          } else if (domId === 'step2-info') {
            this.$analytics.sendEvent('web_weekly_plan_guide_click_step2_next')
            dom.style.display = 'none'
          } else if (domId === 'step3-info') {
            this.$analytics.sendEvent('web_weekly_plan_guide_click_got_it')
            dom.style.display = 'none'
          } else {
            this.$analytics.sendEvent('web_weekly_plan_guide_click_skip')
          }
          let result = { 'features': ['WEEKLY_PLAN_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then()
          this.$store.commit('SET_FIRSTVISIT', false)
          localStorage.setItem(this.currentUser.user_id + 'WEEKLY_PLAN_GUIDE', false)
          element.node.style.pointerEvents = 'auto'
        },
        // 所有引导步骤执行后后执行的方法
        onReset: () => {
          this.$emit('callWeeklyPlanSettingGuide')
          // 清除高亮domId
          this.activeGuideDomId = undefined
        }
      })
      this.$nextTick(() => {
        // 设置引导步骤
        // 如果机构使用了模板，去除引导步骤的后步，并按照机构模板剩余的分组进行引导
        if (!this.canEditTemplate) {
          steps.splice(1, 1)
          steps[0].popover.prevBtnText = '(1/2)'
          steps[1].popover.prevBtnText = '(2/2)'
          steps[1].popover.className = 'guide-weekly-plan-editTip'
        } else {
          steps[2].popover.closeBtnText = ''
          steps[2].popover.className = 'guide-weekly-plan-editTip'
        }
        this.driver.defineSteps(steps)
        // 开始引导
        this.driver.start()
        // 禁用引导元素鼠标事件
        this.driver.steps.forEach(el => { el.node.style.pointerEvents = 'none' })
      })
    },

    /**
     * 获取框架班级信息
     */
    getFrameworkData (groupId) {
      // 非编辑状态不调用接口直接返回 (!this.edit && !this.review) || this.editTemplate ||  预览的情况下不调用接口
      if (!groupId) {
        return
      }
      LessonApi.getGroupFrameworkMeasures({
        groupId: groupId,
        filterIep: true
      }).then(response => {
        this.frameworkData = response.frameworks
      }).catch(error => {})
    },

    /**
     * 加载框架的测评点
     */
    loadMeasures (frameworkId) {
      if (!frameworkId) {
        return
      }
      let params = {
        frameworkId: frameworkId
      }
      LessonApi.getFrameworkMeasures(params)
      .then(res => {
        this.frameworkData = res.frameworks
      })
    },

    /**
     * 获取班级小孩
     */
    getChildren (groupId) {
      // 非编辑状态或非审核状态不调用接口直接返回
      if (!this.edit && !this.review) {
        return
      }
      LessonApi.getChildren({
        groupId: groupId,
        pageNum: 1,
        pageSize: 1000,
        sort: 'firstName',
        order: 'asc'
      }).then(response => {
        this.children = response.results
      }).catch(error => {})
    },

    updateCategoryIndex () {
      let categoryIndex = 0
      this.categories.forEach(c => {
        c.index = categoryIndex++
        c.planId = this.planId
      })
    },

    // 返回周计划列表
    goBack () {
      if (this.planData.batchId && this.planData.applyUserId === this.currentUserId) {
        this.endApplyFlowPath(this.planData.batchId)
      }
      if (this.$route.name == 'edit-plan') {
        this.$router.push({
          name: 'list-plan'
        })
      } else if (this.$route.name == 'edit-template' && this.planData.type == 'ADMIN_TEMPLATE') {
        this.$router.push({
          name: 'template-list'
        })
      } else {
        this.$router.push({
          name: 'list-plan'
        })
      }
    },

    // 获取管理员分享过来的用户列表
    getShareUsers () {
      this.$axios.get($api.urls().listShareUsers)
      .then(res => {
        if (res.users.length > 0) {
          this.hasShared = true
          this.sharedTeachers = res.users
          this.currentTeacher = this.sharedTeachers[0]
          // 获取分享过来的该老师的周计划
          this.getPlanList()
        } else {
          // 空页面
          this.hasShared = false
        }
      })
    },
    // 下一页周计划
    nextPageWeeklyPlanner () {
      this.pageNum += 1
      this.getPlanList()
    },
    // 获取分享过来的周计划列表
    getPlanList () {
      this.getSharePlannerLoading = true
      this.$axios.post($api.urls().listSharedPlansByUser, {
        shareUserId: this.currentTeacher.id,
        pageSize: 20, // 每页默认20个周计划
        page: this.pageNum
      })
      .then(res => {
        this.getSharePlannerLoading = false
        this.totalPlanner = res.total
        // 如果现有的周计划不足所有周计划数量，可以继续获取
        if (this.totalPlanner > this.pageNum * 20) {
          this.hasNextPage = true
        } else {
          this.hasNextPage = false
        }
        res.items.forEach(x => {
          x.fromAtLocal = this.$moment(x.fromAtLocal).format('MM/DD')
          x.toAtLocal = this.$moment(x.toAtLocal).format('MM/DD')
        })
        // 将新获取的周计划加入到新的周计划列表中
        res.items.forEach(x => {
          this.sharedWeeks.push(x)
        })
        this.currentWeekPlan = this.sharedWeeks[0]
        if (this.planId !== this.currentWeekPlan.id) {
          this.planId = this.currentWeekPlan.id
          this.getPlan(true)
          this.hasLastWeekPlan = false
          // 判断是否可以切换周计划
          if (this.sharedWeeks.length > 1) {
            this.hasNextWeekPlan = true
          } else {
            this.hasNextWeekPlan = false
          }
        }
      })
    },
    // 切换老师
    changeTeacher (item) {
      if (this.currentTeacher == item) {
        return
      }
      this.pageNum = 1
      this.sharedWeeks = []
      this.currentTeacher = item
      // 获取该老师被分享给该用户的所有周计划
      this.getPlanList()
    },
    // 切换周计划
    changeWeek (week) {
      if (week === 'getMore') {
        this.nextPageWeeklyPlanner()
        return
      }
      if (this.currentWeekPlan == week) {
        return
      }
      this.currentWeekPlan = week
      this.planId = this.currentWeekPlan.id
      // 获取周计划详情
      this.getPlan(true)
      // 调整切换周计划逻辑
      let index = this.sharedWeeks.indexOf(this.currentWeekPlan)
      if (index == 0) {
        this.hasLastWeekPlan = false
        this.hasNextWeekPlan = true
      } else if (index == this.sharedWeeks.length - 1) {
        this.hasNextWeekPlan = false
        this.hasLastWeekPlan = true
      } else {
        this.hasNextWeekPlan = true
        this.hasLastWeekPlan = true
      }
    },

    // 上一个周计划
    lastWeekPlan () {
      // 调整切换周计划逻辑
      let index = this.sharedWeeks.indexOf(this.currentWeekPlan)
      if (index > 0) {
        // 将切换下一个周计划按钮打开
        this.hasNextWeekPlan = true
        this.currentWeekPlan = this.sharedWeeks[index - 1]
        this.planId = this.currentWeekPlan.id
        // 获取周计划
        this.getPlan(true)
        if (index - 1 == 0) {
          this.hasLastWeekPlan = false
        } else {
          this.hasLastWeekPlan = true
        }
      }
    },

    // 下一个周计划
    nextWeekPlan () {
      // 调整切换逻辑
      let index = this.sharedWeeks.indexOf(this.currentWeekPlan)
      index += 1
      if (index < this.sharedWeeks.length) {
         // 将切换上一个周计划按钮打开
        this.hasLastWeekPlan = true
        this.currentWeekPlan = this.sharedWeeks[index]
        this.planId = this.currentWeekPlan.id
        // 获取周计划
        this.getPlan(true)
        if (index == this.sharedWeeks.length - 1) {
          this.hasNextWeekPlan = false
        } else {
          this.hasNextWeekPlan = true
        }
      }
    },
    // 复制周计划弹窗
    showReplicateModel () {
      this.$analytics.sendEvent('web_weekly_virtual_detail_replicate')
      // 如果该周计划是管理范围内的，直接复制
      let groupIds = []
      this.centers.forEach(c => {
        c.groups.forEach(g => {
          groupIds.push(g.id)
        })
      })
      if (groupIds.indexOf(this.currentWeekPlan.groupId) > 0) {
        this.replicateWeekPlan(false)
      } else {
        this.replicateModelVisible = true
      }
    },
    // 复制周计划
    replicateWeekPlan (flag) {
      this.morePopVisable = false
      this.$analytics.sendEvent('web_weekly_virtual_detail_replicate_save')
      // 如果没选班级，return
      if (flag && this.selectedGroupId == '') {
        this.showNotSelectGroupTip = true
        return
      }
      this.showNotSelectGroupTip = false
      this.replicateLoading = true
      LessonApi.replicatePlan({
        planId: this.planId,
        groupId: this.selectedGroupId === '' ? undefined : this.selectedGroupId
      }).then(response => {
        this.replicateLoading = false
        this.cancelReplicate()
        this.$router.push({
          name: 'edit-plan',
          params: {
            planId: response.id
          }
        })
      }).catch(error => {
        this.replicateLoading = false
      })
    },
    // 取消复制周计划
    cancelReplicate () {
      this.replicateModelVisible = false
      this.selectedGroupId = ''
      this.showNotSelectGroupTip = false
    },
    // 获取学校班级
    getCenterGroups () {
      // 获取学校、班级列表
      this.$axios.get(
        $api.urls(this.currentUser.user_id).centersAndGroups
      ).then(response => {
        // 过滤离校班级
        response.forEach(c => {
          c.groups = c.groups.filter(x => !x.inactive)
        })
        // 过滤没有班级的学校
        this.centers = response.filter(c => c.groups.length > 0)
      }).catch(error => {})
    },
    // 生成pdf
    generatePDF (print) {
      this.morePopVisable = false
      // 如果是火狐浏览器，不支持打印
      if (print && tools.isFirefox()) {
        this.$alert(this.$t('loc.plan176'), this.$t('loc.plan135'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('loc.download'),
          customClass: 'el-message-box-font',
          callback: action => {
            if (action == 'confirm') {
              this.generatePDF(false)
            }
          }
        })
        return
      }
      this.calendarLoading = true
      LessonApi.getPlanPDF({
        planId: this.planId,
        showCore: this.showCore && !this.edit && !this.review
      }).then(response => {
        this.getPlanPDF(print)
      }).catch(error => {})
    },

    /**
     * 获取周计划 PDF
     */
    getPlanPDF (print) {
      LessonApi.getPDFList({
        type: 'WEEKLY_PLAN'
      }).then(response => {
        if (response && response.length > 0) {
          let pdf = response[0]
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getPlanPDF(print)
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED' && print) {
            this.$print({
              printable: pdf.pdfUrl,
              type: 'pdf',
              onLoadingStart: () => {
              },
              onLoadingEnd: () => {
                this.calendarLoading = false
              },
              onError: (error) => {
                this.downloadPDFWithAlert(pdf)
                this.calendarLoading = false
              }
            })
          } else {
            this.downloadPDFWithAlert(pdf)
            this.calendarLoading = false
          }
        }
      })
    },

    /**
     * 下载 PDF
     */
    downloadPDFWithAlert (pdf) {
      // 成功弹窗下载
      this.$alert('<div class="display-flex align-items"><img style="height: 30px; margin: 5px" src="' + this.getfilePhoto(pdf.pdfName) + '"> <span  title="' + pdf.pdfName + '">' + pdf.pdfName + '</span></div>', this.$t('loc.plan61'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download'),
        customClass: 'el-message-box-font',
        callback: action => {
          if (action == 'confirm') {
            if (tools.isComeFromIPad()) {
              let requestData = {
                'emailTemplate': 'weekly_lesson_planning',
                'downloadFileUrl': pdf.pdfUrl,
                'fileName': pdf.pdfName,
                'week': this.planData.week,
                'className': this.planData.groupName,
                'siteName': this.planData.centerName,
                'fromDate': this.planData.fromAtLocal,
                'toDate': this.planData.toAtLocal,
                'courseName': this.planData.theme
              }
              this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
              .then(() => {
                this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                  confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                  showCancelButton: false
                })
              }).catch(error => {
                this.$message.error(error.message)
              })
            } else {
              const eleLink = document.createElement('a')
              eleLink.style.display = 'none'
              eleLink.href = pdf.pdfUrl
              // 触发点击
              document.body.appendChild(eleLink)
              eleLink.click()
              // 移除
              document.body.removeChild(eleLink)
            }
          }
        }
      })
    },

    /**
     * 删除周计划
     */
     deletePlan () {
      this.morePopVisable = false
      this.$confirm(this.$t('loc.plan11'), this.$t('loc.cfm'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        LessonApi.deletePlan({}, {
          id: this.planData.id
        }).then(response => {
          this.$message({
            type: 'success',
            message: this.$t('loc.plan12')
          })
          if (this.planData.type == 'ADMIN_TEMPLATE') {
            this.$router.push({
              name: 'template-list'
            })
          } else {
            this.$router.push({
              name: 'list-plan'
            })
          }
        })
      }).catch(() => {
      })
    },

    /**
     * 获取文件 icon
     */
    getfilePhoto (fileName) {
      if (fileName.endsWith('doc')) {
        return doc
      }
      if (fileName.endsWith('docx')) {
        return docx
      }
      if (fileName.endsWith('pdf')) {
        return pdf
      }
      if (fileName.endsWith('ppt')) {
        return ppt
      }
      if (fileName.endsWith('pptx')) {
        return pptx
      }
      if (fileName.endsWith('xls')) {
        return xls
      }
      if (fileName.endsWith('xlsx')) {
        return xlsx
      }
      return file
    },

    changeClass () {
    },

    /**
     * 鼠标移入行中，显示添加/删除图标
     */
    enterRow (data) {
      this.$set(data, 'showOperation', true)
    },

    /**
     * 鼠标移除行外，隐藏添加/删除图标
     */
    leaveRow (data) {
      data.showOperation = false
    },

    focusActivityInput () {
      this.activityPopoverVisible = true
    },

    closeActivityPopover () {
      this.activityPopoverVisible = false
    },

    batchSave: tools.debounce(function () {
      this.saveDraft(true)
    }, 5000),

    async saveDraft (silent) {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_save_draft')
      this.planData.silent = silent
      if (this.isShared || this.submitLoading || this.submited) {
        return
      }
      this.saveDraftLoading = true
      let centerItems = []
      this.planCenters.forEach(c => {
        c.items.forEach(i => {
          centerItems.push(i)
        })
      })
      this.planData.categories.forEach(c => {
        if (c.type == 'BOTTOM_CENTER_ROW') {
          c.items = centerItems
        }
      })
      // 调用 update 方法之前，将 planCenters 中的items与 Centers Categoriy 中的items同步
      return LessonApi.updatePlan(this.planData).then(response => {
        if (!silent) {
          this.goBack()
        }
        // this.getPlanItemLessons()
        // 如果是预览页面，则改编或编辑成功之后，解锁周计划
        if (this.preview && this.planId) {
          LessonApi.unlockEditing({
            id: this.planId
          })
        }
      }).catch(error => {
        // 用户主动点击保存，如果失败了，再提示保存失败
        if (!silent) {
          this.$message.error('Save weekly planner failed!')
        }
      }).finally(() => {
        this.saveDraftLoading = false
      })
    },
    // 单个课程改编
    singleAdaptLesson () {
      this.$bus.$on('showPersonalizePlanDialog', (lessonId) => {
        this.currentItemId = lessonId
        this.$refs.personalizePlan.showPersonalizePlan()
      })
    },
    // 更新生成通用设计和 CLR 数据
    updateGenerateUniversalDesignAndCLRData () {
      // 定义参数
      const params = {
        planId: this.planId,
        includeCenterLesson: true
      }
      return new Promise((resolve, reject) => {
        // 发送请求
        this.$axios.get($api.urls().getPlanLessons, { params: params }).then(async res => {
          // 如果 res 存在，并且 res.lessons 是存在的
          if (res && res.lessons) {
            // 设置 planLessons
            this.$set(this, 'planLessons', res.lessons.filter(item => item.lessonId))
            let loadLesson = Array.from(new Set(this.planLessons))
            if (this.$refs.lessonEditor) {
              this.$refs.lessonEditor.singleAdaptLesson = true
              this.$refs.lessonEditor.currentItemId = this.currentItemId
              // 要开始生成数据了，所以要展示 LessonEditor，将要加载的课程传递给 LessonEditor
              await this.$refs.lessonEditor.initLessons(loadLesson)
            }
          }
          resolve()
        }).catch(error => {
          this.$message.error(error.response.data.error_message)
          reject(error)
        })
      })
    },
    // 提交周计划
    submit () {
      if (this.editTemplate) {
        this.submitTemplate()
        return
      }
      // 检查必填项
      if (!this.theme || this.theme.trim().length === 0) {
        let themeRowTitle = this.customThemeRowName || this.$t('loc.plan4')
        this.$message.error(this.$t('loc.plan45', { rowTitle: themeRowTitle }))
        return
      }
      // // 检查周次
      // if (!this.planData.week) {
      //   this.$message.error(this.$t('loc.plan46'))
      //   return
      // }
      // 检查老师
      if (!this.planData.teacherIds || this.planData.teacherIds.length === 0) {
        this.$message.error(this.$t('loc.plan47'))
        return
      }
      // 检查项目
      if (!this.hasItem()) {
        this.$message.error(this.$t('loc.plan48'))
        return
      }
      // 检查 center
      if (!this.checkCenter()) {
        this.$message.error(this.$t('loc.curriculum79'))
        return
      }
      // 提示语
      let confirmMsg = this.$t('loc.plan69')
      this.$confirm(confirmMsg, this.$t('loc.noteTip'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.curriculum90'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'plan-message-box'
      }).then(() => {
        // 提交提示
        if (!this.hideSubmitTip) {
          this.$refs.submitTipModal.hideTip = false
          this.$refs.submitTipModal.visible = true
          return
        }
        this.submitPlan()
      }).catch(error => {
        this.submitLoading = false
      })
    },

    // 提交周计划模板
    submitTemplate () {
      // 检查必填项
      if (!this.theme || this.theme.trim().length === 0) {
        let themeRowTitle = this.customThemeRowName || this.$t('loc.plan4')
        this.$message.error(this.$t('loc.plan45', { rowTitle: themeRowTitle }))
        return
      }
      if (!this.frameworkId) {
        this.$message.error('Please Select Framework')
        return
      }
      // 检查项目
      if (!this.hasItem()) {
        this.$message.error(this.$t('loc.plan48'))
        return
      }
      // 检查 center
      if (!this.checkCenter()) {
        this.$message.error(this.$t('loc.curriculum79'))
        return
      }
      this.submitLoading = true
      let param = this.planData
      if (param.categories.includes(x => x.type === 'BOTTOM_CENTER_ROW')) {
        param.categories.filter(x => x.type === 'BOTTOM_CENTER_ROW')[0].items.forEach(y => {
          if (y.mediaIds) {
            y.mediaIds = [y.mediaIds]
          }
        })
      }
      LessonApi.submitAdminTemplate(param)
      .then(res => {
        this.submited = true
        this.submitLoading = false
        // 返回列表页面
        this.goBack()
        this.$message({
          type: 'success',
          message: this.$t('loc.plan50')
        })
      })
      .catch(error => {
        this.submitLoading = false
        this.$message.error(error.response.data.error_message)
      })
    },

    // 提交周计划
    submitPlan (hideSubmitTip) {
      // 设置提交时不再提醒
      if (hideSubmitTip) {
        this.planData.hideSubmitTip = true
      }
      this.submitLoading = true
      let param = this.planData
      if (param.categories.includes(x => x.type === 'BOTTOM_CENTER_ROW')) {
        param.categories.filter(x => x.type === 'BOTTOM_CENTER_ROW')[0].items.forEach(y => {
          if (y.mediaIds) {
            y.mediaIds = [y.mediaIds]
          }
        })
      }
      if (this.planData.batchId) {
        param.curriculumApply = true
      }
      LessonApi.submitReview(param).then(response => {
        this.submitLoading = false
        this.submited = true
        if (this.planData.batchId && this.planData.applyUserId === this.currentUserId) {
          LessonApi.getApplyRecords({ batchId: this.planData.batchId })
            .then(res => {
              if (res.notSubmit > 0) {
                this.$refs.applyRecordsModal.openDialog(res, true, this.planData.batchId)
              } else {
                // 返回列表页面
                this.goBack()
              }
            })
        } else {
          this.$message({
            type: 'success',
            message: this.$t('loc.plan50')
          })
          this.goBack()
        }
      }).catch(error => {
        this.submitLoading = false
        this.$message.error(error.response.data.error_message)
      })
    },

    // 提交周计划提示弹窗确定事件
    confirmTip (hideSubmitTip) {
      this.submitPlan(hideSubmitTip)
    },

    hasItem () {
      if (this.categories.length === 0) {
        return false
      }
      // 区角活动行检查
      let centerItems = []
      this.planCenters.forEach(c => {
        c.items.forEach(i => {
          centerItems.push(i)
        })
      })
      // 将区角活动行的项目加入到对应的分类中
      this.planData.categories.forEach(c => {
        if (c.type == 'BOTTOM_CENTER_ROW') {
          c.items = centerItems
        }
      })
      let existItem = false
      // let existCenter = false
      this.categories.forEach(c => {
        let bottomItem = c && c.type.indexOf('BOTTOM_') != -1
        let items = c.items
        if (bottomItem && items) {
          items.forEach(i => {
            if (i && (i.name && i.name.trim().length > 0 || i.measureIds && i.measureIds.length > 0 || i.childIds && i.childIds.length > 0)) {
              existItem = true
            }
          })
        }
      })
      return existItem
    },

    // 检查中心名称是否为空且有项目
    checkCenter () {
      let flag = true
      if (this.planCenters && this.planCenters.length > 0) {
        this.planCenters.forEach(x => {
          // 如果中心名称为空，但有项目，则提示
          if (x.center.name.trim().length === 0 && x.items && x.items.length > 0) {
            flag = false
          }
        })
      }
      return flag
    },

    revert () {
      this.$analytics.sendEvent('web_weekly_plan_detail_click_recall')
      this.$confirm(this.$t('loc.plan51'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box'
      }).then(() => {
        this.revertLoading = true
        LessonApi.revertReview({
          id: this.planId
        }).then(response => {
          this.revertLoading = false
          this.$router.push({
            name: 'edit-plan',
            params: {
              planId: this.planId
            }
          })
          this.$message({
            type: 'success',
            message: this.$t('loc.plan52')
          })
        }).catch(error => {
          this.revertLoading = false
          let errorCode = error.response.data.error_code
          if (errorCode === 'plan_reviewed') {
            this.$message.error('Weekly planner has been reviewed!')
          }
        })
      }).catch(error => {
      })
    },

    addCategory (lastCategory, type, callback) {
      let categoryIndex = this.getCategoryIndex(lastCategory)
      let newCategory
      // 判断需要创建的category是否为Centers，如果是，则确定name为Centers，否则name由用户自行输入
      if (type === 'BOTTOM_CENTER_ROW') {
        newCategory = {
          planId: this.planId,
          type: type,
          name: 'Centers',
          sortNum: categoryIndex + 1,
          items: [],
          lastId: lastCategory ? lastCategory.id : undefined
        }
      } else {
        newCategory = {
          planId: this.planId,
          type: type,
          sortNum: categoryIndex + 1,
          items: [],
          lastId: lastCategory ? lastCategory.id : undefined
        }
      }
      // 调用接口创建分类
      LessonApi.createCategory(newCategory).then(response => {
        // 分类信息加入日历中
        newCategory.id = response.id
        this.categories.splice(lastCategory.index + 1, 0, newCategory)
        this.updateCategoryIndex()
        callback()
      }).catch(error => {
        callback()
      })
      this.autoExist()
    },

    getCategoryIndex (lastCategory) {
      let type = lastCategory.type
      if (!type) {
        return 0
      }
      if (type === 'THEME_ROW' || type === 'REFLECTION_ROW') {
        return 0
      }
      if (type === 'WEEK_ROW') {
        // 计算上半部分分类数量
        let topCount = 0
        this.categories.forEach(category => {
          let categoryType = category.type
          if (categoryType && categoryType.indexOf('TOP_') != -1) {
            topCount++
          }
        })
        return topCount
      }
      return lastCategory.index + 1
    },
    async callSingleEditLesson (itemId) {
      await this.saveDraft(true)
      this.$refs.lessonEditor.initEdit = true
      this.$refs.lessonEditor.singleEditLesson = true
      this.$refs.lessonEditor.currentItemId = itemId
    },
    deleteCategory (deleteCategory) {
      LessonApi.deleteCategory({}, {
        planId: this.planId,
        categoryId: deleteCategory.id
      }).then(response => {
        this.categories.splice(deleteCategory.index, 1)
        this.updateCategoryIndex()
        if (deleteCategory.type === 'BOTTOM_CENTER_ROW') {
          this.planCenters = []
        }
        this.$message.success(this.$t('loc.planDeleteSuccess'))
      }).catch(error => {})
      this.autoExist()
    },
    updateCategory (updateCategory) {
      // 如果是主题行，更新自定义主题名称
      if (updateCategory.type === 'THEME_ROW') {
        this.customThemeRowName = updateCategory.name
        this.$emit('callUpdateBaseInfo')
        return
      }
      updateCategory.planId = this.planId
      LessonApi.updateCategory(updateCategory).then(response => {
      }).catch(error => {})
      this.autoExist()
    },

    updateTheme (theme) {
      this.theme = theme
      this.$emit('callUpdateBaseInfo')
      this.autoExist()
    },

    // 更新框架，获取测评点以及更新周计划的基本信息
    updateFramework (frameworkId) {
      this.frameworkId = frameworkId
      this.planData.frameworkId = frameworkId
      this.showMeasureMap = this.isCAPTKLF
      // 更新课程中默认选择的框架
      this.updateLessonSelectFramework()
      this.loadMeasures(frameworkId)
      this.$emit('callUpdateBaseInfo')
      this.autoExist()
    },
    // 更新课程中默认选择的框架
    updateLessonSelectFramework(){
      // 查询框架及年龄详细数据
      const selectedFramework = this.frameworks.find(framework => framework.frameworkId === this.frameworkId)
      const grade = this.ageGroups.find(item => item.name === selectedFramework.defaultGrade)
      this.selectedFramework = {
        grade: grade && grade.value,
        frameworkId: selectedFramework.frameworkId,
        frameworkName: selectedFramework.frameworkName
      }
    },
    rejectModal () {
      this.$analytics.sendEvent('web_weekly_plan_detail_click_reject')
      this.rejectText = ''
      this.rejectModalVisible = true
      this.rejectTextError = false
    },

    approveModal () {
      this.$analytics.sendEvent('web_weekly_plan_detail_click_approve')
      this.approveText = ''
      this.approveModalVisible = true
    },

    reject () {
      if (!this.rejectText || this.rejectText.trim().length === 0 || this.rejectText.length > 1000) {
        this.rejectTextError = true
        return
      }
      this.rejectTextError = false
      this.rejectLoading = true
      LessonApi.review({
        planId: this.planId,
        status: 'C_REJECTED',
        comment: this.rejectText
      }).then(response => {
        this.rejectModalVisible = false
        this.rejectLoading = false
        let nextPlanId = response.nextPlanId
        if (nextPlanId) {
          this.nextPlanId = nextPlanId
          this.continueModalVisible = true
          this.approveStatus = 'C_REJECTED'
        } else {
          this.completeModalVisible = true
        }
      }).catch(error => {
        this.rejectLoading = false
        this.handleApproveError(error)
      })
    },

    approve () {
      if (this.approveText.length > 1000) {
        return
      }
      this.approveLoading = true
      LessonApi.review({
        planId: this.planId,
        status: 'D_APPROVED',
        comment: this.approveText
      }).then(response => {
        this.approveModalVisible = false
        this.approveLoading = false
        let nextPlanId = response.nextPlanId
        if (nextPlanId) {
          this.nextPlanId = nextPlanId
          this.continueModalVisible = true
          this.approveStatus = 'D_APPROVED'
        } else {
          this.completeModalVisible = true
        }
      }).catch(error => {
        this.approveLoading = false
        this.handleApproveError(error)
      })
    },

    handleApproveError (error) {
      if (!error || !error.response || !error.response.data) {
        return
      }
      let errorCode = error.response.data.error_code
      if (errorCode === 'plan_deleted') {
        this.$alert(this.$t('loc.plan54'), this.$t('loc.tips'), {
          confirmButtonText: this.$t('loc.ok'),
          callback: action => {
            this.goBack()
          }
        })
      }
      if (errorCode === 'plan_reviewed') {
        this.$alert(this.$t('loc.plan55'), this.$t('loc.tips'), {
          confirmButtonText: this.$t('loc.ok'),
          callback: action => {
            // this.getPlan()
            // 重新渲染预览组件
            this.$emit('reRender')
            // 跳转到预览周计划组件
            this.$router.push({
              name: 'view-plan',
              params: {
                planId: this.planId
              }
            })
            this.approveModalVisible = false
            this.rejectModalVisible = false
          }
        })
      }
      if (errorCode === 'plan_reverted') {
        this.$alert(this.$t('loc.plan56'), this.$t('loc.tips'), {
          confirmButtonText: this.$t('loc.ok'),
          callback: action => {
            this.goBack()
          }
        })
      }
    },

    saveReflection () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_submit')
      let content = this.reflectionText
      if (content.length > 1000) {
        return
      }
      this.saveReflectionLoading = true
      LessonApi.savePlanReflection({
        planId: this.planId,
        content: content
      }).then(response => {
        this.$message({
          type: 'success',
          message: this.$t('loc.plan39')
        })
        this.saveReflectionLoading = false
      }).catch(error => {
        this.saveReflectionLoading = false
      })
    },

    getReflection () {
      this.reflectionLoading = true
      LessonApi.getPlanReflection({
        planId: this.planId
      }).then(response => {
        let reflection = response.lessonReflection
        if (reflection) {
          this.reflectionText = reflection
        }
        this.reflectionLoading = false
      }).catch(error => {
        this.reflectionLoading = false
      })
    },

    finishApprove () {
      this.continueModalVisible = false
      this.goBack()
    },

    nextApprove () {
      this.continueModalVisible = false
      if (!this.nextPlanId) {
        this.goBack()
        return
      }
      this.$router.push({
        name: 'view-plan',
        params: {
          planId: this.nextPlanId
        },
        query: {
          review: true
        }
      })
    },

    completeApprove () {
      this.completeModalVisible = false
      this.goBack()
    },

    previewLesson () {},

    selectLesson (lesson) {
      this.selectLessonFunc(lesson)
    },

    openLessonModal (selectLessonFunc) {
      this.selectLessonFunc = selectLessonFunc
      this.$refs.selectLessonModal.open()
    },

    // 打开快速添加课程弹窗
    openAddLessonModal (selectLessonFunc, name, categoryName) {
      // 选择课程回调
      this.selectLessonFunc = selectLessonFunc
      // 将课程名称带过去
      // 根据是否开启 WeekPLan AI 功能跳转不同页面
      if (this.weeklyCreateAILessonOpen && !this.isComeFromIPad) {
        this.$refs.generateLessonModal.openQuickAddLesson(name, categoryName)
      } else {
        this.$refs.addLessonModal.openQuickAddLesson(name)
      }
    },

    updateWeek (week) {
      this.getLastReflection(week)
    },

    getLastReflection (week) {
      if (!week) {
        return
      }
      this.lastReflectionLoading = true
      LessonApi.getPlanReflection({
        groupId: this.planData.groupId,
        week: week - 1
      }).then(response => {
        let reflection = response.lessonReflection
        if (reflection) {
          this.lastReflection = reflection
        } else {
          this.lastReflection = ''
        }
        this.lastReflectionLoading = false
      }).catch(error => {
        this.lastReflectionLoading = false
      })
    },

    /**
     * 定时退出编辑
     */
    autoExist () {
      if (this.timeoutID) {
        clearTimeout(this.timeoutID)
      }
      // 当前路由
      let path = this.$route.path
      // 非编辑页面直接跳过
      if (!path.match('weekly-lesson-planning/edit')) {
        return
      }
      this.timeoutID = setTimeout(() => {
        this.$alert(this.$t('loc.plan57'), this.$t('loc.tips'), {
          confirmButtonText: this.$t('loc.ok'),
          showClose: false,
          callback: action => {
            let name = 'view-plan'
            if (this.editTemplate) {
              name = 'template-list'
            }
            if (this.planData.type == 'NORMAL_TEMPLATE') {
              name = 'view-template'
            }
            this.$router.replace({
              name: name,
              params: {
                planId: this.planId
              }
            })
          }
        })
      }, 1000 * 60 * 9)
    },

    /**
     * 查看反思
     */
    viewReflection (lessonId) {
      this.$refs.lessonReflectionList.selectLessonById(lessonId)
    },

    collapseMenu () {
      this.collapse = !this.collapse
      sessionStorage.setItem('plan-left-menu-collapse', this.collapse)
    },

    /**
     * 删除反思
     */
    deleteReflection (itemId) {
      if (!itemId) {
        return
      }
      // 删除周计划中项目反思数量
      this.categories.forEach(category => {
        let items = category.items
        if (items && items.length > 0) {
          items.forEach(item => {
            if (itemId === item.id) {
              let reflectionCount = item.reflectionCount
              if (reflectionCount && reflectionCount > 0) {
                item.reflectionCount = reflectionCount - 1
              }
            }
          })
        }
      })
    },
    loadFrameworks () {
      this.$nextTick(() => {
        this.frameworks = this.planFrameworks
        this.$parent.frameworks = this.frameworks
      })
      // LessonApi.getFrameworks().then(res => {
      //   this.frameworks = res.frameworks
      //   this.$parent.frameworks = this.frameworks
      // })
    },
    addCenter (params) {
      let newAddCenter = {
        center: {
          name: params.name,
          colorIndex: params.colorIndex,
          planId: params.planId
        },
        items: []
      }
      LessonApi.addPlanCenter(newAddCenter.center)
        .then(res => {
        newAddCenter.center.id = res.id
        this.planCenters.push(newAddCenter)
        }).catch(error => {})
    },
    deleteCenter (id, hasItem) {
      if (hasItem) {
        this.$confirm(this.$t('loc.curriculum99'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
          LessonApi.deletePlanCenter(id)
          .then(res => {
            this.planCenters = this.planCenters.filter(x => x.center.id != id)
            // 把center下的item过滤掉
            this.categories.forEach(category => {
              category.items = category.items.filter(item => item.centerId !== id)
            })
            this.$message.success(this.$t('loc.planDeleteSuccess'))
          })
          .catch(error => {})
        }).catch(() => {
        })
      } else {
        LessonApi.deletePlanCenter(id)
        .then(res => {
          this.planCenters = this.planCenters.filter(x => x.center.id != id)
          this.$message.success(this.$t('loc.planDeleteSuccess'))
        })
        .catch(error => {})
      }
    },
    // 根本 center 卡片信息
    updateCenter (params) {
      LessonApi.updatePlanCenter(params)
      .then(res => {})
      .catch(error => {})
    },
    // 结束周计划的 apply 流程
    endApplyFlowPath (batchId) {
      LessonApi.endApplyFlowPath(batchId)
      .then(res => {})
      .catch(error => {})
    },
    deleteItem (id, categoryId, centerId, callback) {
      LessonApi.deleteItem(id,this.planId)
        .then(res => {
          this.categories.forEach(category => {
            if (category.id === categoryId) {
              // 找到需要删除的课程下标
              const index = category.items.findIndex(item => item.id === id)
              if (index > -1) {
                category.items.splice(index, 1)
              }
            }
          })
          if (centerId) {
            this.planCenters.forEach(center => {
              if (center.center.id === centerId) {
                // 找到需要删除的课程下标
                const index = center.items.findIndex(item => item.id === id)
                if (index > -1) {
                  center.items.splice(index, 1)
                }
              }
            })
          }
          if (callback && typeof callback === 'function') {
            callback()
          }
        })
        .catch(error => {})
    },
    addItem (newItem, categoryId) {
      this.categories.filter(x => x.id === categoryId)[0].items.push(newItem)
    },
    // 是否有周计划讲解
    haveInterpretation (value) {
      this.existInterpretation = value
    },
    // 修改周计划范例核心测评点开关
    changeExamplarPlanCoreMeasureOpen (val) {
      if (val) {
        this.$analytics.sendEvent('web_weekly_example_close_core_measure')
      } else {
        this.$analytics.sendEvent('web_weekly_example_open_core_measure')
      }
    },
    /**
     * 初始化拖拽实例
     */
    initDrag () {
      // 拿到所有的拖拽分组
      var categories = document.querySelectorAll('.plan-drag-category')
      // 遍历所有的拖拽分组，创建拖拽实例
      var _this = this
      var tip = this.$t('loc.plan151')
      var actToTextMsg = this.$t('loc.doPlan5')
      var oneTextMsg = this.$t('loc.doPlan6')
      for (var dragCategory of categories) {
        // 如果已经创建拖拽实例，跳过
        if (dragCategory.sort) {
          continue
        }
        dragCategory.putTip = true
        // 创建拖拽实例
        dragCategory.sort = Sortable.create(dragCategory, {
          group: {
            name: 'shared',
            // 拖拽到目标分组时，判断是否可以放置
            put: function (to, from, el) {
              // 拿到目标分组的可拖拽项目数量
              let length = to.el.querySelectorAll('.plan-drag-info').length
              let toTextLength = to.el.querySelectorAll('.text-col').length
              // 目标单元格的类型
              let toType = toTextLength > 0 ? 'text' : 'activity'
              let fromActivityLength = from.el.querySelectorAll('.activity-col').length
              // 分组内移动或要移动到的分组内小于 5 个项目就可以拖拽
              let canPut = length < 5 || (length === 5 && (to.el === from.el || el.parentElement === to.el))
              // 如果已有 5 个项目，提示不能再拖拽
              if (dragCategory.putTip && !canPut) {
                dragCategory.putTip = false
                _this.$message({
                  message: tip,
                  type: 'error',
                  duration: 2000,
                  onClose: function () {
                    dragCategory.putTip = true
                  }
                })
              }
              if (canPut) {
                // 判断是否活动单元格拖拽到文本单元格
                let activityToText = fromActivityLength !== 0 && toTextLength !== 0
                if (activityToText) canPut = false
                // 如果活动单元格拖拽到文本单元格
                if (dragCategory.putTip && activityToText) {
                  dragCategory.putTip = false
                  _this.$message({
                    message: actToTextMsg,
                    type: 'error',
                    duration: 2000,
                    onClose: function () {
                      dragCategory.putTip = true
                    }
                  })
                }
              }
              // 文本单元格分组内移动并且要移动到的分组内小于 1 个项目就可以拖拽
              if (canPut && toType === 'text') {
                canPut = length < 1
                if (dragCategory.putTip && !canPut) {
                  dragCategory.putTip = false
                  _this.$message({
                    message: oneTextMsg,
                    type: 'error',
                    duration: 2000,
                    onClose: function () {
                      dragCategory.putTip = true
                    }
                  })
                }
              }

              return canPut
            }
          },
          handle: '.drag-handle',
          ghostClass: 'plan-drag-ghost',
          animation: 150,
          scroll: true,
          scrollSensitivity: 50,
          scrollSpeed: 10,
          bubbleScroll: true,
          forceFallback: true,
          onEnd: evt => {
            // 拖拽结束，更新数据
            // 拿到拖拽的目标分组和原分组
            var from = evt.from
            var to = evt.to
            // 拿到拖拽的项目
            var item = evt.item
            var oldSort = JSON.parse(item.dataset.dragInfo).sortNum
            var newSortNum = 0
            if (item.previousElementSibling && item.previousElementSibling.dataset && item.previousElementSibling.dataset.dragInfo) {
              newSortNum = JSON.parse(item.previousElementSibling.dataset.dragInfo).sortNum + 1
            } else {
              newSortNum = 0
            }
            // 如果目标分组和原分组不一致，则更新数据
            if (from !== to || oldSort !== newSortNum) {
              // 拿到原分组的信息
              var fromCategory = JSON.parse(from.dataset.categoryInfo)
              // 拿到目标分组的信息
              var toCategory = JSON.parse(to.dataset.categoryInfo)
              // 拿到拖拽项目的信息
              var dragInfo = JSON.parse(item.dataset.dragInfo)
              // 拿到周计划的分类和中心区角活动分组
              var categories = JSON.parse(JSON.stringify(_this.categories))
              var planCenters = JSON.parse(JSON.stringify(_this.planCenters))
              var currentItem = null
              if (fromCategory.centerId) {
                // 遍历周计划的中心区角活动分组，找到原项目所在的中心区角活动分组，把原项目从中心区角活动分组中移除
                planCenters.forEach(center => {
                  if (center.center.id === fromCategory.centerId) {
                    var items = center.items
                    currentItem = items.find(x => x.id === dragInfo.id)
                    var delSortNum = items.indexOf(currentItem)
                    items.forEach(item => {
                      if (item.sortNum > delSortNum) {
                        item.sortNum = item.sortNum - 1
                      }
                    })
                    var filterItems = items.filter(x => x.id !== dragInfo.id)
                    center.items = filterItems
                    center.renderKey = tools.uuidv4()
                  }
                })
              } else {
                // 遍历周计划的分类，找到原项目所在的分类，把原项目从分类中移除
                categories.forEach(category => {
                  if (category.id === fromCategory.categoryId) {
                    var items = category.items
                    var filterItems = items.filter(x => x.dayOfWeek === fromCategory.dayOfWeek)
                    currentItem = items.find(x => x.id === dragInfo.id)
                    var delSortNum = filterItems.indexOf(currentItem)
                    filterItems.forEach(item => {
                      if (item.sortNum > delSortNum) {
                        item.sortNum = item.sortNum - 1
                      }
                    })
                    items.forEach(item => {
                      filterItems.forEach(x => {
                        if (item.id === x.id) {
                          item.sortNum = x.sortNum
                        }
                      })
                    })
                    category.items = items.filter(x => x.id !== dragInfo.id)
                    category.renderKey = tools.uuidv4()
                  }
                })
              }
              // 遍历周计划的分类，找到目标分组，把原项目添加到目标分组
              if (toCategory.centerId) {
                // 遍历周计划的中心区角活动分组，找到目标分组，把原项目添加到目标分组
                planCenters.forEach(center => {
                  if (center.center.id === toCategory.centerId) {
                    currentItem.dayOfWeek = toCategory.dayOfWeek
                    currentItem.centerId = toCategory.centerId
                    currentItem.categoryId = toCategory.categoryId
                    currentItem.sortNum = newSortNum
                    center.items.splice(newSortNum, 0, currentItem)
                    center.items.forEach((x, index) => {
                      item.sortNum = index
                    })
                    center.renderKey = tools.uuidv4()
                  }
                })
              } else {
                categories.forEach(category => {
                  if (category.id === toCategory.categoryId) {
                    var items = category.items
                    var filterItems = category.items.filter(x => x.dayOfWeek === toCategory.dayOfWeek)
                    currentItem.dayOfWeek = toCategory.dayOfWeek
                    currentItem.centerId = toCategory.centerId
                    currentItem.categoryId = toCategory.categoryId
                    // 如果新位置大于等于分组中元素的个数并且分组中的最后一个元素是临时元素时，与分组中的最后一个元素交换位置
                    if (newSortNum >= filterItems.length && filterItems[filterItems.length - 1].name === '') {
                      // 创建一个临时存放位置的变量
                      let tempIndex
                      // 把最后一个元素的位置赋值给临时变量
                      tempIndex = filterItems[filterItems.length - 1].sortNum
                      // 新位置的索引重新赋值给最后一个元素
                      filterItems[filterItems.length - 1].sortNum = newSortNum
                      // 把临时变量存储的位置赋值给原项目
                      currentItem.sortNum = tempIndex
                      // 将临时变量赋值给新的位置
                      newSortNum = tempIndex
                    } else {
                      currentItem.sortNum = newSortNum
                    }
                    // 将当前的 item 插入到新的位置
                    filterItems.splice(newSortNum, 0, currentItem)
                    filterItems.forEach((item, index) => {
                      item.sortNum = index
                    })
                    var otherItems = items.filter(x => x.dayOfWeek !== toCategory.dayOfWeek)
                    category.items = otherItems.concat(filterItems)
                    category.renderKey = tools.uuidv4()
                  }
                })
              }
              // 给周计划的分类和中心区角活动分组重新赋值
              _this.categories = categories
              _this.planCenters = planCenters
              _this.planData.categories = _this.categories
              // 拿到周计划项内容
              let params = {
                id: currentItem.id,
                name: currentItem.name,
                sortNum: currentItem.sortNum,
                categoryId: currentItem.categoryId,
                planId: currentItem.planId,
                lessonId: currentItem.lessonId,
                link: currentItem.link ? currentItem.link.trim() : null,
                dayOfWeek: currentItem.dayOfWeek,
                childIds: currentItem.childIds,
                measureIds: currentItem.measureIds,
                frameworkId: currentItem.frameworkId,
                centerId: currentItem.centerId ? currentItem.centerId : null
              }
              // 更新周计划项
              LessonApi.updateItem(params).then(response => {
              }).catch(error => {})
            }
          }
        })
      }
    },
    // 重置拖拽状态
    resetDragStatus () {
      if (this.draging) {
        this.$nextTick(() => {
          this.draging = false
          let lessonDrag = this.$refs.lessonDrag
          if (lessonDrag) {
            lessonDrag.draging = false
          }
        })
      }
    }
  },

  // 組件销毁前注销 bus 事件
  beforeDestroy () {
    this.$store.dispatch('setSingleAdaptGuide', false)
    this.$bus.$off('singleEditLesson')
    this.$bus.$off('showPersonalizePlanDialog')
  },

  destroyed () {
    this.batchSave = null
    if (this.timeoutID) {
      clearTimeout(this.timeoutID)
    }
    if (this.planId && this.allowUnLock && (!this.viewPlan || this.review)) {
      LessonApi.unlockEditing({
        id: this.planId
      })
    }
    // if (!this.planData.hasNextApplyPlan) {
    //   if (this.planData.batchId) {
    //     this.endApplyFlowPath(this.planData.batchId)
    //   }
    // }
  },

  watch: {
    planId (val) {
      // 路由的 planId 发生变化后，将 submited 更改为 fasle
      this.submited = false
      val && !this.isShared && this.getPlan()
      // 更换周计划时，清空周计划拖拽状态
      this.resetDragStatus()
    },
    // 监听是否正在引导，如果没有在引导，取消事件禁用,以及将第五步添加的class去除
    isGuiding (newVal, oldVal) {
      if (!newVal && oldVal && this.driver) {
        this.driver.steps.forEach(el => { el.node.style.pointerEvents = 'auto' })
        var editDom = document.getElementById('guide-weekly-plan-editTip')
        editDom.children[0].classList.remove('row-border')
      }
    },
    // 监听核心测评点开关值
    showCore (val) {
      this.$bus.$emit('changeShowCore', { planId: this.planId, open: val })
      this.$parent.showCore = val
    },
    // 监听路变化，重新获取周计划详情
    $route (to, from) {
      if (to.params.refresh) {
        this.getPlan()
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .plan-drag-ghost {
  height: 250px !important;
  background-color: var(--color-page-background-white); /* 设置拖拽时的背景颜色 */
  /* 隐藏所有的子元素 */
  > * {
    display: none;
  }
}
.action-divider {
  margin: 8px 0px !important;
}
.action-delete:hover:not(.is-disabled) {
  color: #f56c6c !important;
}
/deep/ .el-link--inner {
  display: flex;
  align-items: center;
}
.shared-plan-theme {
  max-width: 200px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.flex-row-reverse {
  display: flex;
  flex-flow: row-reverse;
}

.shared-plan {
  padding: 24px 0px;
  background: transparent;
  // background: linear-gradient(0deg, #DDF2F3 -0.74%, #10B3B7 99.26%);
}

.shared-plan-pre-btn {
  z-index: 5;
  border: none;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  position: absolute;
  left: 0;
  top: 250px;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #11EE69 12.5%, #15DCE9 84.72%);
  box-shadow: 0px 4px 8px rgba(16, 179, 183, 0.3);
  color: #fff;
}

.shared-plan-next-btn {
  z-index: 5;
  border: none;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  position: absolute;
  right: 0;
  top: 250px;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #11EE69 12.5%, #15DCE9 84.72%);
  box-shadow: 0px 4px 8px rgba(16, 179, 183, 0.3);
  color: #fff;
}

@media only screen and (max-width:1199px){
  //ipad
  .group-area {
    padding: 10px;
     min-width: 720px;
  }

  .activity-item {
    background-color:#e7f7f8;
    color:#40c2c5
  }

  /deep/ .el-dropdown-menu__item {
    padding: 12px 20px;
  }

  .text-hidden {
    overflow: hidden;
    text-overflow:ellipsis;
    // white-space: nowrap;
  }

  .flex-row-between {
    // display:flex;
    align-items: center;
    justify-content: space-between;
  }

  .shared-plan-header{
    border-bottom: none !important;
    border-left: solid 1px #DADADA;
  }

  .week-date-color {
    color: #606266;
  }

  .display-week {
    min-height: 20px;
  }

  .display-week-container {
    position: absolute;
    top: 5px;
    left: calc(50% - 100px);
    i {
      font-size: 24px;
    }
  }

  .display-week {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .plan-calendar {
    min-height: 0;
    height: 100%;
  }

  .calendar-area {
    flex: auto;
    position: relative;;
    display: flex;
    flex-direction: column;
    overflow-x: auto;
  }

  .calendar-bottom {
    overflow: auto;
    padding: 0 0 0 0;
  }

  // 周一至周五颜色
  @week-1-color: #DD3E78;
  @week-2-color: #F4C18F;
  @week-3-color: #97B02E;
  @week-4-color: #7FC2A7;
  @week-5-color: #795198;
  // 周计划表格
  .plan-table {
    width: 100%;
    // overflow-x: auto;
  }
  .plan-row {
    display: flex;
    position: relative;
  }
  .plan-col, .plan-classified-col {
    background-color: #fff;
    padding: 10px;
    border-right: solid 1px #DADADA;
    border-bottom: solid 1px #DADADA;
    border-top: solid 1px #DADADA;
    overflow-wrap: break-word;
  }
  .plan-col {
    min-width: 720px;
    flex: 1 0 auto;
  }
  .operation-col {
    width: 20px;
    flex: auto;
    // position: sticky;
    left: 0;
    background-color: transparent;
    // z-index: 1;
    // overflow: visible;
  }
  .plan-classified-col {
    width: 120px;
    flex: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    // position: sticky;
    left: 20px;
    background-color: #fff;
    // z-index: 1;
    border-left: solid 1px #DADADA;
  }

  .merged-col {
    min-height: 40px;
  }

  .activity-col {
    min-height: 100px;
    padding: 6px;
  }

  .activity-input /deep/ textarea {
    padding: 0;
    border: none;
    overflow: hidden;
    resize : none;
  }

  .footer-area {
    text-align: right;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
  }

  .category-operation {
    z-index: 20;
    // background-color: #f0f3f4;
    position: absolute;
    left: 0px;
    bottom: -20px;
    // height: 100%;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .pre-area {
    white-space: pre-line;
    margin-top: -20px;
  }

  /deep/ .el-dialog__body {
    font-size: 16px;
  }
  .toolbar-area {
    position: relative;
    z-index: 3;
  }
  .collapse-btn {
    z-index: 4;
    position: absolute;
    top: -12px;
    right: -10px;
  }
  /deep/ .plan-reflection-card {
    .el-card__body {
      padding: 10px;
    }
  }
  .ipad_min_width{
    min-width: 740px;
  }
}
@media only screen and (min-width:1200px){
  //web
  .group-area {
    padding: 10px;
    // min-width: 1120px; 小屏幕适配
  }

  .activity-item {
    background-color:#e7f7f8;
    color:#40c2c5
  }

  /deep/ .el-dropdown-menu__item {
    padding: 12px 20px;
  }

  .text-hidden {
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
  }

  .flex-row-between {
    // display:flex;
    align-items: center;
    justify-content: space-between;
  }

  .shared-plan-header{
    border-bottom: none !important;
    border-left: solid 1px #DADADA;
  }

  .week-date-color {
    color: #606266;
  }

  .display-week {
    min-height: 20px;
  }

  .display-week-container {
    position: absolute;
    top: 5px;
    left: calc(50% - 100px);
    i {
      font-size: 24px;
    }
  }

  .display-week {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .plan-calendar {
    min-height: 0;
    height: 100%;
  }

  .calendar-area {
    flex: auto;
    position: relative;;
    display: flex;
    flex-direction: column;
    overflow-x: auto;
  }

  .calendar-bottom {
    overflow: auto;
    padding: 0;
  }

  // 周一至周五颜色
  @week-1-color: #DD3E78;
  @week-2-color: #F4C18F;
  @week-3-color: #97B02E;
  @week-4-color: #7FC2A7;
  @week-5-color: #795198;
  // 周计划表格
  .plan-table {
    width: 100%;
    // overflow-x: auto;
  }
  .plan-row {
    display: flex;
    position: relative;
  }
  .plan-col, .plan-classified-col {
    background-color: #fff;
    padding: 10px;
    border-right: solid 1px #DADADA;
    border-bottom: solid 1px #DADADA;
    border-top: solid 1px #DADADA;
    overflow-wrap: break-word;
  }
  .plan-col {
    min-width: 720px;
    flex: 1 0 auto;
  }
  .operation-col {
    width: 20px;
    flex: auto;
    // position: sticky;
    left: 0;
    background-color: transparent;
    // z-index: 1;
    // overflow: visible;
  }
  .plan-classified-col {
    width: 120px;
    flex: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    // position: sticky;
    left: 20px;
    background-color: #fff;
    // z-index: 1;
    border-left: solid 1px #DADADA;
  }

  .merged-col {
    min-height: 40px;
  }
  .activity-col {
    min-height: 100px;
    padding: 6px;
  }

  .activity-input /deep/ textarea {
    padding: 0;
    border: none;
    overflow: hidden;
    resize : none;
  }

  .footer-area {
    text-align: right;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
  }

  .category-operation {
    z-index: 20;
    // background-color: #f0f3f4;
    position: absolute;
    left: 0px;
    bottom: -20px;
    // height: 100%;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .pre-area {
    white-space: pre-line;
    margin-top: -20px;
  }

  /deep/ .el-dialog__body {
    font-size: 16px;
  }
  .toolbar-area {
    position: relative;
    z-index: 3;
  }
  .collapse-btn {
    z-index: 4;
    position: absolute;
    top: -12px;
    right: -10px;
  }
  /deep/ .plan-reflection-card {
    .el-card__body {
      padding: 10px;
    }
  }
}
</style>
