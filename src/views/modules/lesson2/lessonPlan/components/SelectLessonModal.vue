<template>
  <div class="">
    <!-- 选择课程弹窗 -->
    <el-dialog
      :visible.sync="selectLessonVisible"
      :close-on-click-modal=false
      custom-class="select-lesson-modal"
      width="800px"
      min-height="600px;">
      <!-- 课程切换 -->
      <el-tabs class="lg-tabs m-b-sm" v-model="lessonType" @tab-click="loadLessons(false)">
        <el-tab-pane name="ALL" :label="$t('loc.lessons2AgencyTagName1')"></el-tab-pane>
        <el-tab-pane v-if="publicOpen || isAdmin" name="PUBLIC" :label="$t('loc.lessonLibraryTabName1')"></el-tab-pane>
        <el-tab-pane v-if="isAgencyUser" name="AGENCY-WIDE" :label="$t('loc.lessonLibraryTabName2')"></el-tab-pane>
        <el-tab-pane name="CREATION" :label="$t('loc.lessons2TeacherLessonTagName1')"></el-tab-pane>
        <el-tab-pane v-if="!isAdmin" name="FAVORITES" :label="$t('loc.lessons2TeacherLessonTagName2')"></el-tab-pane>
      </el-tabs>
      <div class="lesson-modal">
        <!-- 搜索课程 -->
        <el-input
          :placeholder="$t('loc.lessons2LessonListSearchPlaceholder')"
          suffix-icon="el-icon-search"
          @input="loadLessons(false)"
          v-model="keywords">
        </el-input>
        <!-- 课程列表 -->
        <div
          class="lesson-area m-t-xs lg-scrollbar-show"
          :class="{'b-a': lessons.length !== 0}"
          v-loading="lessonLoading"
          v-infinite-scroll="loadMoreLesson"
          infinite-scroll-distance="10"
          :infinite-scroll-disabled="!hasNaxtPage">
          <div class="lesson-row display-flex justify-content-between align-items m-b-sm m-t-sm" :class="{'b-t lg-pt-10': index != 0}" v-for="(lesson, index) in lessons" :key="lesson.id">
            <!-- 图片 -->
            <div class="lesson-media">
              <media-viewer class="h-full" v-if="lesson.coverMediaUrls" :adaptedLesson="lesson.adaptedLesson" :url="lesson.coverMediaUrls[0]" :hideVideo=true></media-viewer>
              <!-- <img class="lesson-media" :src="lesson.coverMediaUrls[0]" /> -->
            </div>
            <!-- 名称、测评点 -->
            <div class="lesson-content">
              <div style="color:#111c1c;" class="font-bold m-b-sm overflow-ellipsis-two" :title="lesson.name">
                {{lesson.name}}
              </div>
              <div>
                <!-- 年龄组 -->
                <el-tag type="success" size="small" v-for="(age, index) in getAgeGroups(lesson.ageGroupNames)" class="m-r-xs pos-rlt m-b-xs" :key="index" disable-transitions>{{ age }}</el-tag>
                <!-- 测评点 -->
                <el-tag style="color:#111c1c;" type="info" size="small" class="m-r-xs pos-rlt m-b-xs" v-for="(m, index) in lesson.measure" :key="index" disable-transitions>
                  {{m}}
                </el-tag>
              </div>
            </div>
            <!-- 操作 -->
            <div class="lesson-btn">
              <el-button size="medium" type="success" @click="previewLesson(lesson)">{{$t('loc.preview')}}</el-button>
              <el-button size="medium" type="primary" @click="selectLesson(lesson)">{{$t('loc.select')}}</el-button>
            </div>
          </div>
          <div v-if="lessonLoading && lessons.length > 0" v-loading="lessonLoading" style="height: 20px;"></div>
          <!-- 没有课程 -->
          <div class="display-flex justify-content align-items flex-direction-col add-padding-t-30" v-if="!lessonLoading && lessons.length === 0">
            <img src="@/assets/img/lesson2/plan/empty2.png" style="height: 50%;width: 50%;" />
            <span>{{$t('loc.plan36')}}</span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!--模态窗口展示详情页-->
    <lesson-detail-dialog :lesson-id="previewLessonId" :show.sync="showDetail" new-tab-to="PublicLessonDetail">
      <!--详情页-->
      <lesson-detail :isFromLibrary="true" :lessonId="previewLessonId" :isDialog="true">
        <template slot="header-left" slot-scope="{lesson}">
          <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/> -->
          <lesson-like :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
          <lesson-favorite :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/>
        </template>
      </lesson-detail>
    </lesson-detail-dialog>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import { acrossRole } from '@/utils/common'
import tools from '@/utils/tools'
import { mapState } from 'vuex'
import LessonDetail from '../../lessonLibrary/components/LessonDetail'
import LessonDetailDialog from '../../lessonLibrary/components/LessonDetailDialog'
import LessonDetailTips from '../../lessonLibrary/components/LessonDetailTips'
import LessonFavorite from '../../lessonLibrary/components/LessonFavorite'
import LessonLike from '../../lessonLibrary/components/LessonLike'
import LessonPdf from '../../lessonLibrary/components/LessonPdf'
// import LessonReadCount from '../../lessonLibrary/components/LessonReadCount'
import LessonReplicate from '../../lessonLibrary/components/LessonReplicate'
import MediaViewer from './MediaViewer'

export default {
  name: 'SelectLessonModal',
  components: {
    LessonDetailDialog,
    LessonDetailTips,
    LessonReplicate,
    // LessonReadCount,
    LessonDetail,
    LessonLike,
    LessonFavorite,
    LessonPdf,
    MediaViewer
  },

  data () {
    return {
      lessonType: 'CREATION', // 搜索范围tab
      keywords: '', // 课程搜索关键字
      selectLessonVisible: false,
      lessons: [], // 课程列表
      pageNum: 1, // 当前页码
      pageSize: 10, // 每页条数
      totalNum: 0, // 总数量
      hasNaxtPage: false, // 是否有更多
      favoritesLessons: [], // 收藏的课程
      lessonLoading: true, // 搜索课程加载
      showDetail: false, // 详情页弹窗的开关
      previewLessonId: '', // 弹框课程Id
      version: 0 // 课程加载乐观锁
    }
  },

  created () {
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      educator: state => state.common.open && state.common.open.educator,
      publicOpen: state => state.common.open ? state.common.open.publicLessonSearchOpen : false, // 公共课程开关
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
    }),
    isAgencyUser () {
      return !this.educator
    },
    // 是否是管理员
    isAdmin () {
      return acrossRole('agency_admin', 'agency_owner')
    },
    // 获取年龄组信息
    getAgeGroups () {
      return (ageGroupNames) => {
        return JSON.parse(ageGroupNames)
      }
    }
  },

  methods: {
    open () {
      // 老师角色没有查看公共课程的权限的话，直接搜索自己的课程
      if (!this.isAdmin && !this.publicOpen) {
        this.lessonType = 'CREATION'
      } else {
        this.lessonType = 'PUBLIC'
      }
      this.selectLessonVisible = true
      this.keywords = ''
      this.loadLessons(false)
    },
    // 更换课程搜索范围，触发搜索方法
    loadLessons (isLoadMore) {
      this.lessonLoading = true
      // 如果不是分页加载，初始化分页页数，清空课程
      if (!isLoadMore) {
        this.totalNum = 0
        this.pageNum = 1
        this.lessons = []
        this.hasNaxtPage = false
        this.version = 0
      }
      if (this.lessonType === 'CREATION') {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_my_creation')
        this.searchMyLesson()
      }
      if (this.lessonType === 'FAVORITES') {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_my_favorites')
        this.searchFavoriteLesson()
      }
      if (this.lessonType === 'ALL') {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_all_lesson')
        this.searchAllLesson()
      }
      if (this.lessonType === 'PUBLIC') {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_public_les')
        this.searchPublicLesson()
      }
      if (this.lessonType === 'AGENCY-WIDE') {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_agency_les')
        this.searchAgencyLesson()
      }
    },

    // 分页加载课程
    loadMoreLesson () {
      if (this.lessonLoading) {
        return
      }
      this.pageNum++
      this.loadLessons(true)
    },

    // 加载我的课程
    loadMyLessons () {
      this.lessonLoading = true
      let version = this.version = Math.random()
      LessonApi.getMyLessons({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keywords,
        orderKey: 'UPLOAD_TIME',
        status: 'PUBLISHED',
        ages: [],
        domainIds: [],
        themeIds: [],
        otherMeasureIds: [],
        isAdaptedLesson: false
      }).then(response => {
        // 锁检查
        if (this.version !== version || this.lessonType !== 'CREATION') {
          return
        }
        this.totalNum = response.total
        response.items.forEach(x => {
          this.lessons.push(x)
        })
        if (this.totalNum > this.pageNum * 10) {
          this.hasNaxtPage = true
        } else {
          this.hasNaxtPage = false
        }
        this.lessonLoading = false
      }).catch(error => {})
    },
    // 加载我的收藏课程
    loadMyFavorites () {
      this.lessonLoading = true
      let version = this.version = Math.random()
      LessonApi.getMyFavoriteLessons({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keywords,
        orderKey: 'UPLOAD_TIME',
        ages: [],
        domainIds: [],
        themeIds: [],
        otherMeasureIds: [],
        isAdaptedLesson: false
      }).then(response => {
        // 锁检查
        if (this.version !== version || this.lessonType !== 'FAVORITES') {
          return
        }
        this.totalNum = response.total
        response.items.forEach(x => {
          this.lessons.push(x)
        })
        if (this.totalNum >= this.pageNum * 10) {
          this.hasNaxtPage = true
        } else {
          this.hasNaxtPage = false
        }
        this.lessonLoading = false
      }).catch(error => {})
    },
    // 加载公共课程
    loadPublicLesson () {
      this.lessonLoading = true
      let version = this.version = Math.random()
      LessonApi.getPublicLessons({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keywords,
        orderKey: 'UPLOAD_TIME',
        ages: [],
        domainIds: [],
        themeIds: [],
        otherMeasureIds: []
      }).then(response => {
        // 锁检查
        if (this.version !== version || this.lessonType !== 'PUBLIC') {
          return
        }
        this.totalNum = response.total
        response.items.forEach(x => {
          this.lessons.push(x)
        })
        if (this.totalNum >= this.pageNum * 10) {
          this.hasNaxtPage = true
        } else {
          this.hasNaxtPage = false
        }
        this.lessonLoading = false
      }).catch(error => {})
    },
    // 加载机构课程
    loadAgencyWideLesson () {
      this.lessonLoading = true
      let version = this.version = Math.random()
      LessonApi.getAgencyLessons({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keywords,
        orderKey: 'UPLOAD_TIME',
        ages: [],
        domainIds: [],
        themeIds: [],
        otherMeasureIds: []
      }).then(response => {
        // 锁检查
        if (this.version !== version || this.lessonType !== 'AGENCY-WIDE') {
          return
        }
        this.totalNum = response.total
        response.items.forEach(x => {
          this.lessons.push(x)
        })
        if (this.totalNum >= this.pageNum * 10) {
          this.hasNaxtPage = true
        } else {
          this.hasNaxtPage = false
        }
        this.lessonLoading = false
      }).catch(error => {})
    },
    // 加载所有课程
    loadAllLesson () {
      this.lessonLoading = true
      let version = this.version = Math.random()
      LessonApi.getMyLessons({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keywords,
        orderKey: 'UPLOAD_TIME',
        status: 'PUBLISHED',
        ages: [],
        domainIds: [],
        themeIds: [],
        otherMeasureIds: [],
        all: true,
        publicOpen: this.publicOpen,
        isAdaptedLesson: false
      }).then(response => {
        // 锁检查
        if (this.version !== version || this.lessonType !== 'ALL') {
          return
        }
        this.totalNum = response.total
        response.items.forEach(x => {
          this.lessons.push(x)
        })
        if (this.totalNum >= this.pageNum * 10) {
          this.hasNaxtPage = true
        } else {
          this.hasNaxtPage = false
        }
        this.lessonLoading = false
      }).catch(error => {})
    },
    // 搜索我的课程
    searchMyLesson: tools.debounce(function () {
      this.loadMyLessons()
    }, 2000),
    // 搜索我的收藏课程
    searchFavoriteLesson: tools.debounce(function () {
      this.loadMyFavorites()
    }, 2000),
    // 搜索公共课程
    searchAgencyLesson: tools.debounce(function () {
      this.loadAgencyWideLesson()
    }, 2000),
    // 搜索公共课程
    searchPublicLesson: tools.debounce(function () {
      this.loadPublicLesson()
    }, 2000),
    // 搜索所有课程
    searchAllLesson: tools.debounce(function () {
      this.loadAllLesson()
    }, 2000),
    // 课程预言
    previewLesson (lesson) {
      this.previewLessonId = lesson.id
      this.showDetail = true
    },
    // 选择课程
    selectLesson (lesson) {
      // 如果是自己创建的课程，将课程作者 id 设置为自己
      if (this.lessonType === 'CREATION') {
        lesson.authorId = this.currentUser.user_id
      }
      this.$emit('callSelectLesson', lesson)
      this.selectLessonVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .lg-tabs {
  width: fit-content;
}
.lesson-modal {
  display: flex;
  flex-direction: column;
}
.lesson-area {
  padding: 5px 0;
  height: 400px;
  overflow-y: auto;
  margin-top: 15px;
  margin-bottom: 20px;
}
.lesson-row {
  padding: 0 10px;
}
.lesson-media {
  flex: none;
  object-fit: cover;
  width: 160px;
  height: 90px;
}
.lesson-content {
  padding: 10px;
  flex: auto;
}
.lesson-btn {
  width: 190px;
  flex: none;
  /deep/ .el-button {
    margin-bottom: 2px;
    margin-left: 0;
    margin-right: 10px;
  }
}
/deep/ .select-lesson-modal .el-dialog__body {
  padding: 0 20px 20px;
}

// 移动端适配样式
@media screen and (max-width: 768px) {
  .lesson-modal {
    padding: 0;
  }

  .lesson-area {
    height: calc(100vh - 250px);
    margin: 10px 0;
  }

  .lesson-row {
    flex-direction: column;
    padding: 15px;
    gap: 10px;
  }

  .lesson-media {
    width: 100%;
    height: 200px;
  }

  .lesson-content {
    width: 100%;
    padding: 0;
  }

  .lesson-btn {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;

    /deep/ .el-button {
      flex: 1;
      margin: 0;
      padding: 10px;
      height: 44px;
    }
  }

  /deep/ .el-dialog {
    width: 95% !important;
    margin: 10px auto !important;
  }

  /deep/ .el-tabs__item {
    padding: 0 10px;
  }

  /deep/ .el-tag {
    margin-bottom: 5px;
  }

  // 搜索框优化
  /deep/ .el-input__inner {
    height: 44px;
    font-size: 16px;
  }

  // 触摸区域优化
  .el-button,
  .el-checkbox {
    min-height: 44px;
  }
}
</style>
