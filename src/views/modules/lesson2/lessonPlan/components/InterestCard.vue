<template>
  <el-card class="box-card" shadow="never" v-loading="loading" v-if="loading || interests && interests.length > 0">
    <el-divider class="m-t-md m-b-md"></el-divider>
    <!-- 标题 -->
    <div class="title-font-18 lg-color-text-primary lg-margin-bottom-20">
      <span class="">{{$t('loc.plan88')}}</span>
    </div>
    <!-- 表头 -->
    <el-row class="display-flex align-items bg-light add-padding-t-6 add-padding-b-6 m-b-xs">
      <el-col :span="16" class="text-left line-height-18 interest-cell title-font-14">
        {{$t('loc.lessons2Interests')}}
      </el-col>
      <el-col :span="8" class="text-left line-height-18 interest-cell title-font-14">
        {{$t('loc.Children')}}
      </el-col>
    </el-row>
    <!-- 兴趣统计内容 -->
    <template v-if="!!interests">
      <div v-for="(interest, index) in interests" :key="index">
        <el-row class="">
          <!-- 兴趣名称 -->
          <el-col :span="16">
            <div class="overflow-ellipsis font-size-14 text-left level-item interest-cell" :title="interest.interest">
              {{interest.interest}}
            </div>
          </el-col>
          <!-- 小孩数量 -->
          <el-col :span="8">
            <div class="overflow-ellipsis font-size-14 text-left level-item interest-cell">
              <el-popover
                placement="right"
                width="200"
                trigger="hover">
                <!-- 小孩信息列表 -->
                <div>
                  <div v-for="child in interest.children" :key="child.id" class="interest-child-item text-ellipsis">
                    <img height="30" style="border-radius:50%;" :src="child.avatarUrl" alt="">
                    <span style="margin-left: 10px; color: #000;  white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" :title="child.name">{{ child.name }}</span>
                  </div>
                </div>
                <!-- 小孩数量 -->
                <el-link slot="reference"  :underline="false">
                  {{interest.childCount}}
                </el-link>
              </el-popover>
            </div>
          </el-col>
        </el-row>
        <el-divider v-if="index != interests.length - 1" class="m-t-xs m-b-xs"></el-divider>
      </div>
    </template>
  </el-card>
</template>

<script>
import { mapState } from "vuex"
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'

export default {
  name: 'InterestCard',
  components: {
  },

  props: {
    groupId: {
      type: String
    }
  },

  data () {
    return {
      loading: true,
      interests: []
    }
  },

  created () {
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    })
  },

  methods: {
    loadInterests () {
      if (!this.groupId) {
        return
      }
      this.loading = true
      LessonApi.getTopInterests({
        groupId: this.groupId,
        periodAlias: 'non'
      }).then(res => {
        this.interests = res.interests
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    }
  },

  watch: {
    groupId: {
      immediate: true,
      handler() {
        this.groupId && this.loadInterests()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.interest-cell {
  padding: 16px;
  color: #323338;
}
.interest-child-item:not(:last-child) {
  margin-bottom: 12px;
}
</style>
