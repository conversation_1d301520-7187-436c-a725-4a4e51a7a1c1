<!-- eslint-disable no-mixed-spaces-and-tabs -->
<template>
  <div class="display-flex flex-direction-col h-full" v-loading="pageLoading">
    <!-- 表格头部筛选、添加操作 -->
    <div class="bg-white flex-space-between flex-none lg-padding-12 lg-box-shadow lg-border-radius-8">
      <!-- 筛选、搜索 -->
      <!-- 周次 -->
      <el-select size="medium" v-model="currentWeekNum"
                 @change="changeWeek" class="border-bold display-flex-1">
        <el-option v-for="(week, index) in weeks" :key="index" :label="week.name" :value="week.num"></el-option>
      </el-select>
      <!-- 学校，单个学校时不显示 -->
      <el-select size="medium" v-if="showCenter"
                 v-model="currentCenterId" @change="changeCenter"
                 :disabled="centers.length < 2" class="border-bold m-l-md display-flex-1">
        <el-option v-for="(center, index) in centers" :key="index" :label="center.name" :value="center.id"></el-option>
      </el-select>
      <!-- 班级 -->
      <el-select size="medium" v-model="currentGroupId"
                 @change="changeClass" :disabled="groups.length < 1 || (groups.length === 1 && !groups[0].id)"
                 class="border-bold m-l-md display-flex-1">
        <el-option v-for="(group, index) in groups" :key="index" :label="group.name" :value="group.id"></el-option>
      </el-select>
      <!-- 创建人标签 -->
      <el-select size="medium" v-model="currentCreateUserLabelKey"
                 :clearable="showCreateUserLabelClearIcon" @change="changeCreateUserLabels"
                 @clear="clearCreateUserLabels" class="border-bold m-l-md display-flex-1">
        <el-option v-for="(createUserLabel, index) in createUserLabels" :key="index" :label="createUserLabel.val" :value="createUserLabel.key"></el-option>
      </el-select>
      <!-- 周计划状态标签 -->
      <el-select size="medium" v-model="currentWeeklyPlansStateKey"
                 :clearable="showWeeklyPlansStateClearIcon" @change="changeWeeklyPlansStatus"
                 @clear="clearWeeklyPlansStatus" class="border-bold display-flex-1 m-l-md">
        <el-option v-for="(weeklyPlansState, index) in weeklyPlansStatus" :key="index" :label="weeklyPlansState.val" :value="weeklyPlansState.key"></el-option>
      </el-select>
      <!-- 添加操作 -->
      <!-- <div> -->
        <!-- 搜索框 -->
        <!-- <el-input size="medium" :clearable=true :placeholder="$t('loc.plan1')" v-model="keyword" class="m-r-sm search-input">
          <el-button slot="append" icon="el-icon-search"></el-button>
        </el-input>
        <el-button v-if="!isAdmin" size="medium" type="primary" :disabled="!defaultGroupId" @click="createPlan()">{{$t('loc.plan2')}}</el-button>
        <el-button v-if="isAdmin && allowCreatePlan" size="medium" type="primary" @click="createModelVisible = true">{{$t('loc.plan2')}}</el-button> -->
      <!-- </div> -->
    </div>
    <!-- 周计划表格 -->
    <div class="bg-white flex-auto lg-padding-20 lg-box-shadow lg-border-radius-8 lg-margin-top-20">
      <el-table
        :data="plans"
        header-row-class-name="table-header"
        @sort-change="sortChange"
        @row-click="viewPlan"
        row-class-name="lg-pointer"
        :default-sort="defaultSort"
        :key="renderKey"
        v-loading="tableLoading && !pageLoading"
        class="lg-form"
        ref="table"
        style="width: 100%">
        <!-- 周次 -->
        <el-table-column
          sortable="custom"
          :label="$t('loc.planWeek')"
          prop="Week"
          width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.week">Week {{scope.row.week}}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <!-- 周计划日期范围 -->
        <el-table-column
          :label="$t('loc.plan3')"
          width="130">
          <template slot-scope="scope">
            <span v-if="scope.row.type == 'NORMAL'">
              <span>{{removeYear(scope.row.fromAtLocal)}}</span> - <span>{{removeYear(scope.row.toAtLocal)}}</span>
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <!-- 周计划主题 -->
        <el-table-column
          :label="$t('loc.plan4')"
          width="310"
        >
          <template slot-scope="scope">
            <div class="overflow-ellipsis w-full" :title="scope.row.theme">{{scope.row.theme}}</div>
          </template>
        </el-table-column>
        <!-- 周计划状态 -->
        <el-table-column
          sortable="custom"
          :column-key="'Status'"
          :label="$t('loc.planStatus')"
          prop="Status"
          width="110">
          <template slot-scope="scope">
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'A_DRAFT'" type="info">{{$t('loc.pDraft')}}</el-tag>
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'C_REJECTED'" type="danger">{{$t('loc.planRejected')}}</el-tag>
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'B_PENDING'" type="warning">{{$t('loc.approvalPENDING')}}</el-tag>
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'D_APPROVED'" type="success">{{$t('loc.planApproved')}}</el-tag>
          </template>
        </el-table-column>
        <!-- 周计划学校 -->
        <el-table-column
          v-if="isAdmin"
          :label="$t('loc.site')"
          width="150">
          <template slot-scope="scope">
            <div v-if="scope.row.centerName" class="overflow-ellipsis w-full" :title="scope.row.centerName">{{scope.row.centerName}}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <!-- 周计划班级 -->
        <el-table-column
          prop="groupName"
          :label="$t('loc.class')"
          width="150">
          <template slot-scope="scope">
            <div v-if="scope.row.groupName" class="overflow-ellipsis w-full" :title="scope.row.groupName">{{scope.row.groupName}}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <!-- 周计划框架 -->
        <el-table-column
          :label="$t('loc.plan118')"
          width="200">
          <template slot-scope="scope">
            <span>{{ getFrameworkName(scope.row) }}</span>
          </template>
        </el-table-column>
        <!-- 周计划作者(创建者) -->
        <el-table-column
          minWidth="150"
          :label="$t('loc.planCreator')">
          <template slot-scope="scope">
            <div class="overflow-ellipsis w-full" :title="addSpaceAfterMrMs(scope.row.teacherNames)">{{addSpaceAfterMrMs(scope.row.teacherNames)}}</div>
          </template>
        </el-table-column>
        <!-- 老师角色操作菜单-->
        <el-table-column
          v-if="!isAdmin"
          fixed="right"
          :label="$t('loc.plan9')"
          width="120">
          <template slot-scope="scope">
            <!-- 编辑按钮 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.edit')" placement="top">
              <el-link
                :underline="false"
                v-show="scope.row.canEditOrDelete"
                @click.stop="edit(scope.row)"> <i class="font-size-24 lg-icon lg-icon-edit"></i> </el-link>
            </el-tooltip>
            <!-- 预览 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.detailInfo')" placement="top">
              <el-link
                :underline="false"
                v-show="!scope.row.canEditOrDelete"
                @click.stop="view(scope.row)"><i class="font-size-24 lg-icon lg-icon-eye"></i> </el-link>
            </el-tooltip>
            <el-popover
              placement="bottom"
              width="150"
              :ref="`plan-popover-${scope.row.id}`"
              v-model="scope.row.showPop"
              trigger="click">
              <!-- 复制按钮 -->
              <div class="act_div_wapper">
                <el-link
                  :underline="false"
                  @click.stop="replicateConfirm(scope.row)">
                  <i class="font-size-24 lg-icon lg-icon-copy lg-margin-right-16"></i>
                  <span>{{$t('loc.plan66')}}</span>
                </el-link>
              </div>
              <el-divider class="action-divider"></el-divider>
              <!-- PDF 下载和打印 -->
              <div class="act_div_wapper">
                <el-link
                  :underline="false"
                  @click.stop="generatePDF(scope.row.id, false)">
                  <i class="font-size-24 lg-icon lg-icon-download lg-margin-right-16"></i>
                  <span>{{$t('loc.download')}}</span>
                </el-link>
              </div>
              <div class="act_div_wapper">
                <el-link
                  :underline="false"
                  @click.stop="generatePDF(scope.row.id, true)">
                  <i class="font-size-24 lg-icon lg-icon-print lg-margin-right-16"></i>
                  <span>{{$t('loc.plan174')}}</span>
                </el-link>
              </div>
              <el-divider class="action-divider"></el-divider>
              <!-- 删除 -->
              <div class="act_div_wapper">
                <el-link
                  class="action-delete"
                  :underline="false"
                  :disabled="scope.row.lockedData || !scope.row.canEditOrDelete"
                  @click.stop="deletePlan(scope.row)">
                  <i class="font-size-24 lg-icon lg-icon-delete lg-margin-right-16"></i>
                  <span>{{$t('loc.delete')}}</span>
                </el-link>
              </div>
              <el-button style="border: 0; margin-left: 10px; height: 24px; width: 24px; padding: 0px !important;" size="mini" icon="el-icon-more" slot="reference" @click.stop="showPop(scope.row)"></el-button>
            </el-popover>
          </template>
        </el-table-column>
        <!-- 管理员角色操作菜单-->
        <el-table-column
          v-if="isAdmin"
          fixed="right"
          :label="$t('loc.plan9')"
          width="120">
          <template slot-scope="scope">
            <!-- 编辑按钮 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.edit')" placement="top">
              <el-link
                :underline="false"
                v-show="scope.row.canEditOrDelete && scope.row.status !== 'B_PENDING'"
                @click.stop="edit(scope.row)"> <i class="font-size-24 lg-icon lg-icon-edit"></i> </el-link>
            </el-tooltip>
            <!-- 预览 -->
            <!-- <el-tooltip class="item" effect="dark" :content="$t('loc.detailInfo')" placement="top">
              <el-link
                :underline="false"
                v-show="!scope.row.canEditOrDelete"
                @click.stop="view(scope.row)"><i class="font-size-24 lg-icon lg-icon-eye"></i> </el-link>
            </el-tooltip> -->
            <!-- 审核 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.p_review')" placement="top">
              <el-link
                v-show="scope.row.status == 'B_PENDING'"
                :underline="false"
                @click.stop="view(scope.row, true)"><i class="font-size-24 lg-icon lg-icon-review"></i></el-link>
            </el-tooltip>
            <!-- 更多 -->
            <el-popover
              placement="bottom"
              width="150"
              :ref="`plan-popover-${scope.row.id}`"
              v-model="scope.row.showPop"
              trigger="click">
              <!-- 复制按钮 -->
              <div class="act_div_wapper" v-if="allowCreatePlan">
                <el-link
                  :underline="false"
                  @click.stop="replicateConfirm(scope.row)">
                  <i class="font-size-24 lg-icon lg-icon-copy lg-margin-right-16"></i>
                  <span>{{$t('loc.plan66')}}</span>
                </el-link>
              </div>
              <el-divider class="action-divider"></el-divider>
              <!-- PDF 下载 -->
              <div class="act_div_wapper">
                <el-link
                  :underline="false"
                  @click.stop="generatePDF(scope.row.id, false)">
                  <i class="font-size-24 lg-icon lg-icon-download lg-margin-right-16"></i>
                  <span>{{$t('loc.download')}}</span>
                </el-link>
              </div>
              <!-- PDF 打印 -->
              <div class="act_div_wapper">
                <el-link
                  :underline="false"
                  @click.stop="generatePDF(scope.row.id, true)">
                  <i class="font-size-24 lg-icon lg-icon-print lg-margin-right-16"></i>
                  <span>{{$t('loc.plan174')}}</span>
                </el-link>
              </div>
              <el-divider class="action-divider"></el-divider>
              <!-- 删除 -->
              <div class="act_div_wapper">
                <el-link
                  class="action-delete"
                  :underline="false"
                  :disabled="scope.row.lockedData || !scope.row.canEditOrDelete"
                  @click.stop="deletePlan(scope.row)">
                  <i class="font-size-24 lg-icon lg-icon-delete lg-margin-right-16"></i>
                  <span>{{$t('loc.delete')}}</span>
                </el-link>
              </div>
              <el-button style="border: 0; height: 24px; margin-left: 10px; width: 24px; padding: 0px !important;" size="mini" icon="lg-icon lg-icon-more-horizontal" slot="reference" @click.stop="showPop(scope.row)"></el-button>
            </el-popover>
          </template>
        </el-table-column>
        <div slot="empty">
          <!-- 没有数据 -->
          <div class="empty-area" v-if="!tableLoading">
            <img src="@/assets/img/lesson2/plan/empty.png"/>
            <span>{{$t('loc.plan10')}}</span>
          </div>
        </div>
      </el-table>
      <!-- 分页 -->
      <div v-if="total >= 10" class="table-footer flex-none">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30]"
          small
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 复制确认弹窗 -->
    <el-dialog
      :title="$t('loc.confirmation')"
      custom-class="lg-el-dialog"
      :visible.sync="replicateModelVisible"
      :before-close="beforeCloseReplicate"
      width="30%">
      <span class="lg-color-text-primary">{{$t('loc.plan67')}}</span>
      <span slot="footer" class="dialog-footer">
        <el-button class="font-weight-normal" plain @click="replicateModelVisible = false" :disabled="replicateLoading">{{$t('loc.cancel')}}</el-button>
        <el-button class="font-weight-normal" type="primary" @click="replicate" :loading="replicateLoading">{{$t('loc.confirm')}}</el-button>
      </span>
    </el-dialog>
    <!-- 管理员创建周计划选择学校班级或框架弹窗 -->
    <el-dialog
      :title="$t('loc.plan2')"
      :width="isComeFromIPad ? '50%' : '30%'"
      custom-class="create-plan-dialog"
      :visible.sync="createModelVisible"
      :before-close="beforeCloseCreate">
      <div>
        <div class="create-plan-text add-margin-b-10">
          {{ $t('loc.plan108') }}
        </div>
        <el-radio v-model="createPlanType" label="template" border style="width: 100%;height: auto;">
          <span style="font-size: 16px;font-weight: 600;font-family: 'Inter';">
            {{ $t('loc.plan109') }}
          </span>
          <div class="add-margin-t-8" style="margin-left: 32px;font-family: 'Inter';font-style: normal;font-weight: 400;font-size: 14px;line-height: 22px;">
            {{ $t('loc.plan110') }}
          </div>
          <div style="margin-left: 32px;margin-top: 4px;">
            <el-select v-model="createPlanFrameworkId" :placeholder="$t('loc.plan111')" @change="changeCreatePlanFramework">
              <el-option
                v-for="item in frameworks"
                :key="item.frameworkId"
                :label="item.frameworkName"
                :value="item.frameworkId">
              </el-option>
            </el-select>
          </div>
        </el-radio>
      </div>
      <div class="m-t-sm">
        <el-radio v-model="createPlanType" label="group" border style="width: 100%;height: auto;">
          <span style="font-size: 16px;font-weight: 600;font-family: 'Inter';">
            {{ $t('loc.plan112') }}
          </span>
          <el-row class="add-margin-t-8" style="margin-left: 27px;" type="flex" :gutter="10">
            <el-col :span="12">
              <div style="margin-bottom: 4px;" class="create-plan-text">{{ $t('loc.site') }}</div>
              <el-select v-model="createPlanCenterId" :placeholder="$t('loc.plan111')" @change="changeCreatePlanCenter" >
                <el-option
                  v-for="item in createPlanCenterOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <div style="margin-bottom: 4px;" class="create-plan-text">{{ $t('loc.class') }}</div>
              <el-select v-model="createPlanGroupId" :placeholder="$t('loc.plan111')" @change="changeCreatePlanGroup">
                <el-option
                  v-for="item in createPlanGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-radio>
      </div>
      <div slot="footer">
        <el-button plain @click="beforeCloseCreate">{{ $t('loc.cancel') }}</el-button>
        <el-button :disabled="!createPlanType" type="primary" @click="createPlan()">{{ $t('loc.save') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import { isTeacher } from '@/utils/common'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'

export default {
  name: 'PlanTable',

  props: {
    loader: {
      type: Function
    }
  },

  data () {
    return {
      plans: [],
      weeks: [],
      showCenter: false,
      centers: [],
      groups: [],
      keyword: '',
      currentCenterId: undefined,
      currentGroupId: undefined,
      currentWeekNum: undefined,
      // 创建周计划时默认班级
      defaultGroupId: undefined,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      sortField: undefined,
      sortOrder: undefined,
      tableLoading: true,
      pageLoading: true,
      hasPlans: false,
      lang: 'en-US',
      replicateModelVisible: false,
      replicatePlan: undefined,
      replicateLoading: false,
      createModelVisible: false,
      frameworks: [],
      createPlanFrameworkId: '',
      createPlanCenterOptions: [],
      createPlanGroupOptions: [],
      createPlanCenterId: '',
      createPlanGroupId: '',
      createPlanType: '',
      renderKey: 0,
      defaultSort: {}, // 排序顺序
      createUserLabels: [
        { key: 'ALL', val: this.$t('loc.createdUserLabelAll') },
        { key: 'ME', val: this.$t('loc.createdUserLabelMe') },
        { key: 'OTHERS', val: this.$t('loc.createdUserLabelOthers') }
      ], // 筛选创建者的标签
      currentCreateUserLabelKey: 'ALL', // 当前选中的创建者标签 id
      weeklyPlansStatus: [
        { key: 'ALL', val: this.$t('loc.weeklyPlanStatusLabelAll') },
        { key: 'A_DRAFT', val: this.$t('loc.weeklyPlanStatusLabelDraft') },
        { key: 'D_APPROVED', val: this.$t('loc.weeklyPlanStatusLabelApproved') },
        { key: 'B_PENDING', val: this.$t('loc.weeklyPlanStatusLabelPending') },
        { key: 'C_REJECTED', val: this.$t('loc.weeklyPlanStatusLabelRevise') }
      ], // 周计划状态
      currentWeeklyPlansStateKey: 'ALL' // 当前选中的周计划状态
    }
  },

  created () {
    this.getWeeks()
    this.getCenterGroups()
    this.loadFrameworks()
    this.lang = tools.localItem('NG_TRANSLATE_LANG_KEY')
    // 渲染排序三角
    this.restoreSortStatus()
    window.addEventListener('resize', this.reCalTable)
  },

  mounted () {
    // 进入周计划列表曝光事件
    if (isTeacher()) {
        this.$analytics.sendEvent('web_weekly_plan_list_exposure')
    } else {
        this.$analytics.sendEvent('web_weekly_plan_manage_exposure')
    }
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      planFrameworks: state => state.lesson.frameworks // 周计划模板可选的框架
    }),
    // 是否显示创建者下拉框的清除标记
    showCreateUserLabelClearIcon () {
      return this.currentCreateUserLabelKey !== 'ALL'
    },
    // 是否显示周计划状态下拉框的清除标记
    showWeeklyPlansStateClearIcon () {
      return this.currentWeeklyPlansStateKey !== 'ALL'
    },
    allowCreatePlan () {
      return this.open && this.open.adminCreateWeekPlan
    },
    isAdmin () {
      if (!this.currentUser) {
        return false
      }
      let role = this.currentUser.role
      return role && role.toUpperCase() === 'OWNER'
    },
    eidtingTip () {
      return (plan) => {
        return plan && plan.lockedData ? plan.lockedData.userName + this.$t('loc.plan59') : ''
      }
    },
    isComeFromIPad () {
      return tools.isComeFromIPad()
    }
  },

  methods: {
    addSpaceAfterMrMs (str) {
      if (!str) {
        return ''
      }
      return str.replace(/(Mr\.|Ms\.)/g, '$1 ')
    },
    // 修改创建者下拉框选择的回调
    changeCreateUserLabels () {
      this.currentPage = 1
      this.loadData()
    },
    // 清除创建者下拉框选择的回调
    clearCreateUserLabels () {
      this.currentCreateUserLabelKey = 'ALL'
      this.changeCreateUserLabels()
    },
    // 修改周计划状态下拉框选择的回调
    clearWeeklyPlansStatus () {
      this.currentWeeklyPlansStateKey = 'ALL'
      this.changeWeeklyPlansStatus()
    },
    // 清除周计划状态下拉框选择的回调
    changeWeeklyPlansStatus () {
      this.currentPage = 1
      this.loadData()
    },

    /**
     * 恢复上次的排序字段和顺序
     */
    restoreSortStatus () {
      this.$nextTick(() => {
        // 恢复上次的获取上次排序方式
        this.sortField = localStorage.getItem(this.currentUser.user_id + 'PLAN_SORT_FIELD')
        this.sortOrder = localStorage.getItem(this.currentUser.user_id + 'PLAN_SORT_ORDER')
        if (this.sortOrder == 'DESC') {
          this.defaultSort = { prop: this.sortField, order: 'descending' }
        } else if (this.sortOrder == 'ASC') {
          this.defaultSort = { prop: this.sortField, order: 'ascending' }
        }
        // 重新渲染表格，让排序三角显示
        this.renderKey++
      })
    },

    /**
     * 获取框架名
     */
    getFrameworkName (plan) {
      return plan.frameworkAbbreviation || plan.frameworkName
    },

    /**
     * 生成 PDF
     */
    generatePDF (id, print) {
      // 火狐浏览器不支持打印
      if (print && tools.isFirefox()) {
        this.$alert(this.$t('loc.plan176'), this.$t('loc.plan135'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('loc.download'),
          customClass: 'el-message-box-font',
          callback: action => {
            if (action == 'confirm') {
              this.generatePDF(false)
            }
          }
        })
        return
      }
      if (print) {
        if (this.isAdmin) {
          this.$analytics.sendEvent('web_weekly_plan_manage_click_print')
        } else {
          this.$analytics.sendEvent('web_weekly_plan_list_click_print')
        }
      } else {
        if (this.isAdmin) {
          this.$analytics.sendEvent('web_weekly_plan_manage_click_download')
        } else {
          this.$analytics.sendEvent('web_weekly_plan_list_click_download')
        }
      }
      this.tableLoading = true
      this.showPop()
      LessonApi.getPlanPDF({
        planId: id,
        showCore: false
      }).then(response => {
        this.getPlanPDF(print)
      }).catch(error => {})
    },

    /**
     * 获取周计划 PDF
     */
    getPlanPDF (print) {
      LessonApi.getPDFList({
        type: 'WEEKLY_PLAN'
      }).then(response => {
        if (response && response.length > 0) {
          let pdf = response[0]
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getPlanPDF(print)
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED' && print) {
            this.$print({
              printable: pdf.pdfUrl,
              type: 'pdf',
              onLoadingStart: () => {
              },
              onLoadingEnd: () => {
                this.tableLoading = false
              },
              onError: () => {
                this.downloadPDFWithAlert(pdf)
                this.tableLoading = false
              }
            })
          } else {
            this.downloadPDFWithAlert(pdf)
            this.tableLoading = false
          }
        }
      })
    },

    /**
     * 下载 PDF
     */
    downloadPDFWithAlert (pdf) {
      // 成功弹窗下载
      this.$alert('<div class="display-flex align-items"><img style="height: 30px; margin: 5px" src="' + this.getfilePhoto(pdf.pdfName) + '"> <span  title="' + pdf.pdfName + '">' + pdf.pdfName + '</span></div>', this.$t('loc.plan61'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download'),
        customClass: 'el-message-box-font',
        callback: action => {
          if (action == 'confirm') {
            this.$analytics.sendEvent('web_weekly_plan_manage_click_pop_download')
            if (tools.isComeFromIPad()) {
              let requestData = {
                'emailTemplate': 'weekly_lesson_planning',
                'downloadFileUrl': pdf.pdfUrl,
                'fileName': pdf.pdfName,
                'week': this.planData.week,
                'className': this.planData.groupName,
                'siteName': this.planData.centerName,
                'fromDate': this.planData.fromAtLocal,
                'toDate': this.planData.toAtLocal,
                'courseName': this.planData.theme
              }
              this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
              .then(() => {
                this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                  confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                  showCancelButton: false
                })
              }).catch(error => {
                this.$message.error(error.message)
              })
            } else {
              const eleLink = document.createElement('a')
              eleLink.style.display = 'none'
              eleLink.href = pdf.pdfUrl
              // 触发点击
              document.body.appendChild(eleLink)
              eleLink.click()
              // 移除
              document.body.removeChild(eleLink)
            }
          }
        }
      })
    },

    /**
     * 获取文件类型 icon
     */
    getfilePhoto (fileName) {
      if (fileName.endsWith('doc')) {
        return doc
      }
      if (fileName.endsWith('docx')) {
        return docx
      }
      if (fileName.endsWith('pdf')) {
        return pdf
      }
      if (fileName.endsWith('ppt')) {
        return ppt
      }
      if (fileName.endsWith('pptx')) {
        return pptx
      }
      if (fileName.endsWith('xls')) {
        return xls
      }
      if (fileName.endsWith('xlsx')) {
        return xlsx
      }
      return file
    },

    /**
     * 重新计算表格高度
     */
    reCalTable () {
      this.$nextTick(() => {
        this.$refs.table && this.$refs.table.doLayout()
        let tableDom = this.$el.querySelector('.lg-form')
        let dom = this.$el.querySelector('.lg-form .el-table__fixed-right')
        if (dom) {
          let table = this.$el.querySelector('.lg-form .el-table__body-wrapper')
          if (table.scrollHeight > table.offsetHeight) {
            dom.style.right = '6px'
          }
          dom.style.height = (tableDom.clientHeight) + 'px'
          let body = dom.querySelector('.el-table__fixed-body-wrapper')
          if (body) {
            body.style.height = (table.clientHeight - 6) + 'px'
          }
        }
      })
    },

    /**
     * 显示更多操作菜单
     */
    showPop (row) {
      if (this.isAdmin) {
        this.$analytics.sendEvent('web_weekly_plan_manage_click_more')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_list_click_more')
      }
      this.plans.forEach((item) => {
        // 显示当前行的操作菜单
        if (row && item.id === row.id) {
          item.showPop = true
        } else {
          // 隐藏其他行的操作菜单
          item.showPop = false
          const key = 'plan-popover-' + item.id
          this.$nextTick(() => {
            document.getElementById(this.$refs[key].$refs.popper.id).style.display = 'none'
          })
        }
      })
    },

    /**
     * 获取周次列表
     */
    getWeeks () {
      this.weeks = [{
        name: this.$t('loc.allWeeks')
      }]
      for (let i = 0; i < 54; i++) {
        this.weeks.push({
          num: i + 1,
          name: 'Week ' + (i + 1)
        })
      }
    },

    formatTime (row, column, cellValue, index) {
      if (!cellValue) {
        return cellValue
      }
      return this.$moment(cellValue).format('MM/DD/YYYY hh:mm A')
    },

    // 获取当前用户学校、班级列表
    getCenterGroups () {
      this.centers = [{
        name: this.$t('loc.allCenter')
      }]
      this.groups = [{
        name: this.$t('loc.allClass')
      }]
      this.$axios({
        url: $api.urls(this.currentUser.user_id).centersAndGroups,
        method: 'get'
      }).then(response => {
        let centers = []
        // 过滤离校班级
        response.forEach(c => {
          let groups = c.groups.filter(x => !x.inactive)
          if (groups.length > 0) {
            c.groups = groups
            centers.push(c)
          }
        })
        this.createPlanCenterOptions = centers
        // 超过一个学校时显示筛选学校
        this.showCenter = centers.length > 1
        // 加入学校列表
        this.centers = this.centers.concat(centers)
        // 如果只有一个学校，直接显示学校下的班级
        if (centers.length === 1) {
          let centerGroups = centers[0].groups
          if (centerGroups && centerGroups.length === 1) {
            this.groups = centerGroups
            this.currentGroupId = centerGroups[0].id
          } else {
            this.groups = this.groups.concat(centerGroups)
          }
        }
        // 选择第一个学校中第一个班级作为创建周计划的默认班级
        if (centers.length > 0) {
          this.defaultGroupId = centers[0].groups[0].id
        }
        // 加载周计划
        this.loadData()
      }).catch(error => {})
    },

    // 加载框架
    loadFrameworks () {
      // LessonApi.getFrameworks().then(res => {
      //   this.frameworks = res.frameworks
      // })
      this.$nextTick(() => {
        this.frameworks = this.planFrameworks
      })
    },
    // 切换框架是将创建周计划类型选中机构模板
    changeCreatePlanFramework () {
      this.createPlanType = 'template'
    },
    // 创建周计划切换学校
    changeCreatePlanCenter () {
      this.createPlanType = 'group'
      // 将选中学校下班级加入列表中
      this.createPlanCenterOptions.forEach(c => {
        if (c && c.id === this.createPlanCenterId) {
          let groups = c.groups.filter(x => !x.inactive)
          if (groups && groups.length > 0) {
            this.createPlanGroupOptions = groups
            this.createPlanGroupId = groups[0].id
          }
        }
      })
    },
    // 创建周计划切换班级
    changeCreatePlanGroup () {
      this.createPlanType = 'group'
    },
    // 关闭创建周计划弹窗
    beforeCloseCreate () {
      this.createModelVisible = false
      this.createPlanCenterId = ''
      this.createPlanGroupId = ''
      this.createPlanType = ''
      this.createPlanFrameworkId = ''
      this.createPlanGroupOptions = []
    },

    loadData () {
      this.tableLoading = true
      // this.plans = []
      let params = {
        page: this.currentPage,
        pageSize: this.pageSize,
        centerId: this.currentCenterId,
        groupId: this.currentGroupId,
        week: this.currentWeekNum,
        ...(this.currentCreateUserLabelKey !== 'ALL' && { createBy: this.currentCreateUserLabelKey }),
        ...(this.currentWeeklyPlansStateKey !== 'ALL' && { status: this.currentWeeklyPlansStateKey })
      }
      // 搜索关键字非空时加入查询条件
      if (this.keyword && this.keyword.trim().length > 0) {
        params['keyword'] = this.keyword
      }
      // 选择了排序，则按照字段排序
      if (this.sortField && this.sortOrder) {
        params['orderKey'] = this.sortField
        params['orderType'] = this.sortOrder
      }
      this.loader(params).then(data => {
        this.pageLoading = false
        this.plans = data.items
        this.plans.forEach(item => {
          item.showPop = false
        })
        this.total = data.total
        this.tableLoading = false
        this.reCalTable()
        // 是否有过周计划
        if (this.plans && this.plans.length > 0) {
          this.hasPlans = true
        }
        // 老师角色，如果列表中没有周计划，则跳转到新增页面
        if (this.defaultGroupId && !this.hasPlans && !this.isAdmin && (!this.plans || this.plans.length === 0)) {
          this.createPlan()
        }
      }).catch(error => {
        this.tableLoading = false
      })
    },

    handleSizeChange (size) {
      this.pageSize = size
      this.currentPage = 1
      // 刷新数据
      this.loadData()
    },

    handleCurrentChange (page) {
      this.currentPage = page
      // 刷新数据
      this.loadData()
    },

    changeWeek () {
      this.currentPage = 1
      // 刷新数据
      this.loadData()
    },

    // 切换学校时，更新班级列表
    changeCenter () {
      this.groups = [{
        name: this.$t('loc.allClass')
      }]
      this.currentGroupId = undefined
      this.currentPage = 1
      // 刷新数据
      this.loadData()
      // 学校全选
      if (!this.currentCenterId) {
        return
      }
      // 将选中学校下班级加入列表中
      this.centers.forEach(c => {
        if (c && c.id === this.currentCenterId) {
          let groups = c.groups.filter(x => !x.inactive)
          if (groups && groups.length === 1) {
            this.groups = groups
            this.currentGroupId = groups[0].id
          } else {
            this.groups = this.groups.concat(groups)
          }
        }
      })
    },

    changeClass () {
      this.currentPage = 1
      // 刷新数据
      this.loadData()
    },

    // 创建周计划
    createPlan () {
      let query = {}
      let routeName = 'edit-plan'
      // 创建机构周计划
      if (this.createPlanType == 'template') {
        this.$analytics.sendEvent('web_weekly_plan_manage_click_add_agency')
        if (!this.createPlanFrameworkId) {
          this.$message.error(this.$t('loc.plan114'))
          return
        }
        routeName = 'edit-agency-template'
        query.isAdmin = true
        query.frameworkId = this.createPlanFrameworkId
      } else if (this.createPlanType == 'group') {
        this.$analytics.sendEvent('web_weekly_plan_manage_click_add_class')
        // 创建班级周计划
        if (!this.createPlanGroupId) {
          this.$message.error(this.$t('loc.plan113'))
          return
        }
        query.defaultGroupId = this.createPlanGroupId
        query.isAdmin = true
      } else {
        // 老师角色创建班级周计划
        query.defaultGroupId = this.defaultGroupId
      }
      this.$router.push({
        name: routeName,
        params: {
          planId: 'new'
        },
        query: query
      })
    },

    /**
     * 查看周计划
     */
    view (plan, edit) {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_weekly_plan_list_click_detail')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_manage_click_detail')
      }
      let router = {
        name: 'view-plan',
        params: {
          planId: plan.id
        }
      }
      if (edit) {
        router.query = {
          review: edit
        }
      }
      this.$router.push(router)
    },

    /**
     * 点击整行查看周计划
     */
    viewPlan (plan) {
      // 如果周计划是草稿状态，跳转编辑页面
      if (plan.status === 'A_DRAFT') {
        this.edit(plan)
        return
      }
      // 如果是待审核状态，跳转审核页面，并且可以编辑
      if (plan.status === 'B_PENDING' && this.isAdmin) {
        this.view(plan, true)
        return
      }
      this.view(plan, false)
    },

    /**
     * 编辑周计划
     */
    edit (plan) {
      if (plan.status == 'C_REJECTED') {
        this.$analytics.sendEvent('web_weekly_plan_detail_reject_click_edit')
      }
      if (isTeacher()) {
        this.$analytics.sendEvent('web_weekly_plan_list_click_edit')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_manage_click_edit')
      }
      if (plan.type && plan.type == 'NORMAL_TEMPLATE') {
        this.$router.push({
          name: 'edit-agency-template',
          params: {
            planId: plan.id,
            normal: true
          }
        })
        return
      }
      this.$router.push({
        name: 'edit-plan',
        params: {
          planId: plan.id,
          edit: true
        }
      })
    },

    /**
     * 复制周计划弹窗关闭回调，复制中的不能关闭
     */
    beforeCloseReplicate (done) {
      if (!this.replicateLoading) {
        done()
      }
    },

    /**
     * 显示复制弹窗
     */
    replicateConfirm (plan) {
      this.replicatePlan = plan
      this.replicateModelVisible = true
    },

    /**
     * 复制周计划
     */
    replicate () {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_weekly_plan_list_click_replicate')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_manage_click_replicate')
      }
      let groupId = this.defaultGroupId
      let name = 'edit-plan'
      // 判断复制的是什么类型的
      if (this.replicatePlan.type == 'NORMAL_TEMPLATE' && this.isAdmin) {
        groupId = ''
        name = 'edit-agency-template'
      } else if (this.replicatePlan.type == 'NORMAL' && this.isAdmin) {
        groupId = this.replicatePlan.groupId
      }
      this.replicateLoading = true
      LessonApi.replicatePlan({
        planId: this.replicatePlan.id,
        groupId: groupId
      }).then(response => {
        this.$router.push({
          name: name,
          params: {
            planId: response.id,
            replicate: true
          }
        })
      }).catch(error => {
        this.replicateLoading = false
      })
    },

    /**
     * 删除周计划
     */
    deletePlan (plan) {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_weekly_plan_list_click_delete')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_manage_click_delete')
      }
      this.$confirm(this.$t('loc.plan11'), this.$t('loc.cfm'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        LessonApi.deletePlan({}, {
          id: plan.id
        }).then(response => {
          this.$message({
            type: 'success',
            message: this.$t('loc.plan12')
          })
          this.loadData()
        })
      }).catch(() => {
      })
    },

    /**
     * 格式化日期，移除年份
     */
    removeYear (date) {
      if (!date) {
        return date
      }
      return this.$moment(date).format('MM/DD')
    },

    /**
     * 排序状态改变
     */
    sortChange (params) {
      let order = params.order
      if (!order) {
        this.sortField = undefined
        this.sortOrder = undefined
        localStorage.removeItem(this.currentUser.user_id + 'PLAN_SORT_FIELD')
        localStorage.removeItem(this.currentUser.user_id + 'PLAN_SORT_ORDER')
      } else {
        let columnKey = params.column.columnKey
        this.sortField = columnKey || params.prop
        this.sortOrder = params.order === 'ascending' ? 'ASC' : 'DESC'
        localStorage.setItem(this.currentUser.user_id + 'PLAN_SORT_FIELD', this.sortField)
        localStorage.setItem(this.currentUser.user_id + 'PLAN_SORT_ORDER', this.sortOrder)
        // 根据周次排序和角色,发送埋点事件
        if (this.sortField.toUpperCase() === 'WEEK') {
          if (this.isAdmin) {
            this.$analytics.sendEvent('web_weekly_plan_manage_week_sort')
          } else {
            this.$analytics.sendEvent('web_weekly_plan_list_week_sort')
          }
        }
        // 根据状态排序和角色,发送埋点事件
        if (this.sortField.toUpperCase() === 'STATUS') {
          if (this.isAdmin) {
            this.$analytics.sendEvent('web_weekly_plan_manage_status_sort')
          } else {
            this.$analytics.sendEvent('web_weekly_plan_list_status_sort')
          }
        }
      }
      // 刷新数据
      this.loadData()
    },
    changeHid (plan) {
      this.Hid = !this.Hid
    }
  },
  watch: {
    // 监听搜索框内容，延迟 500ms 调用接口
    keyword: tools.debounce(function (newVal) {
      this.loadData()
    }, 500)
  }
}
</script>

<style lang="less" scoped>

.action-divider {
  margin: 8px 0px !important;
}
.action-delete:hover:not(.is-disabled) {
  color: #f56c6c !important;
}
/deep/ .el-link--inner {
  display: flex;
  align-items: center;
}

.el-dialog__header {
  font-weight: 600;
  color: #111c1c;
}

@media only screen and (max-width:1199px){
  //ipad
  .plan-table {
    margin: 0 10px;
  }
  /deep/ .table-header th {
    font-weight: bold;
    // color: #323338 !important;
    background-color: #fafafa;
  }
  /deep/.table-header .cell {
    word-break: break-word;
  }
  .operation-area {
    background-color: #fff;
    padding: 10px 10px;
    margin: 0 0 10px;
  }
  .table-footer {
    padding: 10px 0;
    margin-bottom: 0px;
    text-align: right;
  }
  .search-input {
    width: 220px;
  }

  /deep/ .el-input.is-disabled .el-input__inner {
    color: #606266;
  }
  /deep/ .el-table table{
    width: 0px;
  }
  .empty-area {
    display: flex;
    flex-direction: column;
    padding: 50px 0;
    align-items: center;
    justify-content: center;
  }
  .act_div_wapper{
    display: flex;
    justify-content: flex-start;
    margin-top: 10px;
  }
  .table-header{
    color: #323338;
  }
}
@media only screen and (min-width:1200px){
  //web
  .plan-table {
    margin: 0 30px;
  }
  /deep/ .table-header th {
    font-weight: bold;
    // color: #323338 !important;
    background-color: #fafafa;
  }
  /deep/.table-header .cell {
    word-break: break-word;
  }
  .operation-area {
    background-color: #fff;
    padding: 10px 10px;
    margin: 0 0 10px;
  }
  .table-footer {
    padding: 10px 0;
    text-align: right;
  }
  .search-input {
    width: 300px;
  }

  /deep/ .el-input.is-disabled .el-input__inner {
    color: #606266;
    opacity: 0.4;
  }
  /deep/ .el-table table{
    width: 0px;
  }
  .empty-area {
    display: flex;
    flex-direction: column;
    padding: 50px 0;
    align-items: center;
    justify-content: center;
  }
  .act_div_wapper{
    display: flex;
    justify-content: flex-start;
    margin-top: 5px;
  }
}
/deep/ .el-table thead {
  color: #323338;
}
/deep/ .el-table .cell {
  text-overflow: clip;
  word-break: break-word;
}
/deep/ .el-radio.is-bordered {
  padding: 12px 20px 12px 10px;
  background: #F5F6F8;
}
/deep/ .create-plan-dialog {
  border-radius: 8px;
  .el-dialog__title {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #111c1c;
  }
  .el-dialog__body {
    padding: 10px 20px;
    color: #111c1c;
  }
  .el-select {
    width: 100%;
  }
  .el-radio.is-bordered {
    border: none;
  }
  .el-radio.is-bordered.is-checked {
    background: #DDF2F3;
  }
  .create-plan-text {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #111c1c;
  }
}
/deep/ .lg-el-dialog {
  border-radius: 8px;
  .el-dialog__title {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #111c1c;
  }
}
.display-flex-1 {
  display: flex;
  flex: 1;
}
</style>
