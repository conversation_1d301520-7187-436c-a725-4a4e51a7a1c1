<template>
  <div class="reflect-observation-detail h-full lg-scrollbar-show bg-white lg-padding-l-r-24 lg-padding-bottom-24" :class="{'lg-padding-top-24': !hideHeader}">
    <!-- 标题 -->
    <div class="header display-flex justify-content-between" v-if="!hideHeader">
      <div class="display-flex align-items text-primary">
        <div class="color-block-primary"></div>
        <div class="font-size-16">{{$t('loc.plan74')}}</div>
      </div>
    </div>
    <!-- 观察列表 -->
    <div class="m-t-sm note-list" v-loading="notesLoading" :class="{'display-flex align-items justify-content': !notesLoading && notes.length === 0}">
      <!-- 没有观察记录 -->
      <div class="display-flex justify-content align-items flex-direction-col note-blank" :class="{'m-t-md': subList}" v-if="!notesLoading && notes.length === 0">
        <img v-show="!subList" class="w-xs" src="@/assets/img/lesson2/dataReview/empty.png"/>
        <span class="lg-color-secondary">{{$t('loc.lessons2NoResult')}}</span>
      </div>
      <observation-item v-for="(note, index) in notes" :key="index" :note="note" :class="{'b-b': index != notes.length - 1}"></observation-item>
    </div>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import ObservationItem from './ObservationItem'

export default {
  name: 'ReflectionObservationDetail',
  components: {
    ObservationItem
  },

  props: {
    planId: {
      type: String
    },
    lessonId: {
      type: String
    },
    objectId: {
      type: String
    },
    hideHeader: {
      type: Boolean
    },
    subList: {
      type: Boolean
    }
  },

  data () {
    return {
      notes: [],
      notesLoading: true
    }
  },

  methods: {
    /**
     * 加载观察记录
     */
    loadNotes () {
      this.notesLoading = true
      LessonApi.getLessonNotes({
        planId: this.planId,
        lessonId: this.lessonId,
        reflectionObjectId: this.objectId
      }).then(res => {
        this.notes = res.notes
        this.notesLoading = false
      }).catch(error => {
        this.notesLoading = false
      })
    }
  },

  watch: {
    lessonId: {
      immediate: true,
      handler() {
        this.lessonId && this.loadNotes()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.reflect-observation-detail {
  overflow: auto;
  min-height: 100px;
}
.note-list {
  min-height: 70%;
}
</style>