<template>
  <div :class="{ 'lg-padding-bottom-20': lastRow, 'm-l-md' : editTemplate && canEditTemplate && (edit || review) && !draging }" :key="category.renderKey">
    <div :id="category && category.guideRowDomId" class="plan-row" @mouseenter="enterRow(category)" @mouseleave="leaveRow(category)" :class="{'remove-border': category && category.showRemoveBorder}" :data-id="category.id" >
      <div v-show="canEditTemplate && (edit || review) && !draging" class="operation-col">
        <!-- 添加/删除分类按钮 -->
        <div :id="category.guideOperationDomId" class="category-operation" v-show="category.showOperation && (category.type != 'THEME_ROW' || !showReflection)">
          <div @mouseenter.stop="enterRemove()" @mouseleave.stop="leaveRemove()" @click.stop="removeCategory()" @click="deleteCategory">
            <el-tooltip class="item" effect="dark" :content="canRemove ? $t('loc.plan26') : $t('loc.plan27')" placement="top" manual v-model="deleteVisible">
              <i class="el-icon-remove-outline lg-color-text-secondary" :id="category.guideOperationDomId + '-remove'" :class="{'remove-category': canRemove, 'remove-category-hover': guideRemoveHover}"></i>
            </el-tooltip>
          </div>
          <div @mouseenter.stop="enterAdd()" @mouseleave.stop="leaveAdd()">
            <el-tooltip class="item" effect="dark" :content="$t('loc.plan28')" placement="top" manual v-model="addVisible">
              <i class="el-icon-circle-plus-outline add-category lg-color-text-secondary" :id="category.guideOperationDomId + '-add'" :class="{'add-category-hover': guideAddHover}"></i>
            </el-tooltip>
          </div>
          <el-card v-if="category.showAddBorder" shadow="always" class="col-type" @mouseenter.native.stop="enterAdd(true)" @mouseleave.native.stop="leaveAdd(true)" :style="{'bottom':lastRow ? '10px' : 'unset', 'top': lastRow ? 'unset' : '30px'}">
            <!-- 添加一行五列活动 -->
            <el-button class="w-full text-left" type="text" @click="addCategory(5, category)"  v-if="canAddFive"
                       :disabled="!canAddFive || addCategoryOneLoading|| addCategoryFiveLoading ||
                       addCategoryCentersLoading || addCategoryFiveTextLoading || addCategoryOneTextLoading"
                       :loading="addCategoryFiveLoading">
              {{ $t('loc.doPlan4') }}
            </el-button>
            <!-- 添加一行五列文本 -->
            <el-button class="w-full text-left" type="text" @click="addCategory(4, category)"  v-if="canAddFive"
                       :disabled="!canAddFive || addCategoryOneLoading|| addCategoryFiveLoading ||
                       addCategoryCentersLoading || addCategoryFiveTextLoading || addCategoryOneTextLoading"
                       :loading="addCategoryFiveTextLoading">
              {{ $t('loc.doPlan3') }}
            </el-button>
            <!-- 添加一行长活动 -->
            <el-button class="w-full text-left" type="text" @click="addCategory(1, category)"  v-if="canAddFive"
                       :disabled="addCategoryOneLoading || addCategoryFiveLoading || addCategoryCentersLoading ||
                        addCategoryFiveTextLoading || addCategoryOneTextLoading" :loading="addCategoryOneLoading">
              {{ $t('loc.doPlan1') }}
            </el-button>
            <!-- 添加一行长文本 -->
            <el-button class="w-full text-left" type="text" @click="addCategory(3, category)"
                       :disabled="addCategoryOneLoading|| addCategoryFiveLoading || addCategoryCentersLoading ||
                       addCategoryFiveTextLoading || addCategoryOneTextLoading" :loading="addCategoryOneTextLoading">
              {{ $t('loc.doPlan2') }}
            </el-button>
            <!-- 添加 Center -->
            <el-button class="w-full text-left" type="text" @click="addCategory(2, category)"  v-if="canAddFive" :disabled="!canAddCenters" :loading="addCategoryCentersLoading">{{ getAddCenterTitle }}</el-button>
          </el-card>
        </div>
      </div>
      <div :id="category.guideCategoryInputDomId" class="plan-classified-col" @mouseenter="hoverCategory = true;" @mouseleave="hoverCategory = false;" :class="{'b-t': category.type == 'THEME_ROW', 'lg-border-top-left-radius-8': showBorder && category.type == 'THEME_ROW', 'lg-border-bottom-left-radius-8' :lastRow}">
        <span class="word-break text-default" v-if="!canEdit">{{getCategoryName}}</span>
        <el-input
          ref="categoryInput"
          maxlength=200
          v-if="canEdit"
          :class="{'row-border': hoverCategory || focusInput}"
          @focus="focusInput = true"
          type="textarea"
          @blur="changeCategoryName($event);focusInput = false;"
          autosize
          :placeholder="$t('loc.plan44')"
          v-model="category.name">
        </el-input>
      </div>
      <!-- 主题 -->
      <div v-if="category.type == 'THEME_ROW'" class="plan-col merged-col b-t" @mouseenter="hoverTheme = true;" @mouseleave="hoverTheme = false;" :class="{'lg-border-top-right-radius-8': showBorder}">
        <span v-if="isTemplate" class="plan-category-info">{{ $t('loc.planPleaseEnter') }}</span>
        <el-input
          ref="themeInput"
          :placeholder="category.name"
          maxlength=1000
          :class="{'row-border': hoverTheme && !isTemplate, 'area-disabled' : isTemplate}"
          v-if="firstItem && (edit || review) && !draging && !isTemplate"
          type="textarea"
          @blur="updateTheme($event)"
          autosize
          v-model="firstItem.name">
        </el-input>
        <span v-else v-analysisUrl="firstItem && firstItem.name" class="text-default"></span>
      </div>
      <!-- 上周反思 -->
      <div v-if="category.type == 'REFLECTION_ROW'" class="plan-col merged-col display-flex align-items" v-loading="lastReflectionLoading">
        <div class="pre-area" v-if="lastReflection">
          <span v-analysisUrl="lastReflection"></span>
        </div>
      </div>
      <!-- （本周目标）或自定义顶部内容 -->
      <div v-if="category.type == 'TOP_WEEK_COL' || category.type === 'TOP_WEEK_TEXT_COL'" class="plan-col merged-col min-col-height">
        <span v-if="isTemplate" class="plan-category-info">{{ $t('loc.planPleaseEnter') }}</span>
        <plan-item
          ref="planItemInput"
          :type="'WEEK_COL'"
          :edit="(edit || review) && !draging && !isTemplate"
          :isTemplate="isTemplate"
          :frameworkData="frameworkData"
          :children="children"
          :item="getItem(1)"
          :categoryName="category.name"
          @callUpdateItem="updateItem">
        </plan-item>
      </div>
      <!--      Centers-->
      <!-- text-align: center; -->
      <div style="line-height: 100%;" v-if="category.type == 'BOTTOM_CENTER_ROW'"  @mouseenter="hoverCenter=true" @mouseleave="hoverCenter=false" class="plan-col merged-col min-col-height" :class="{'lg-border-bottom-right-radius-8': lastRow}">
        <el-row
          v-if="!temp && (edit || review) && !draging && !isTemplate" :gutter="12">
          <el-col :span="8" v-for="(planCenter,index) in planCenters" :key="index" style="min-height: 300px;">
            <plan-center
              :showQuickAddLesson="showQuickAddLesson"
              :index="index"
              :isOnlyView="isOnlyView"
              :edit="(edit || review) && !draging && !isTemplate"
              :isTemplate="isTemplate"
              :frameworkData="frameworkData"
              :planCenter="planCenter"
              :long="true"
              :isLocked="isLocked"
              :planType="planType"
              :category="category"
              :unitNumber="unitNumber"
              :color-index="index"
              :tags="tags"
              :children="children"
              :draging="draging"
              @hideAllAiAddLessonGuide="hideAllAiAddLessonGuide"
              @scrollToCopyLessonHandle="scrollToCopyLessonHandle"
              @callUpdateTag="updateTag"
              @callDeleteTag="deleteTag"
              @callDeleteCenter="deleteCenter"
              @callUpdateCenter="updateCenter"
              @callOpenLessonModal="openLessonModal"
              @callOpenAddLessonModal="openAddLessonModal"
              @callBatchSave="batchSave"
              @singleEditLesson="singleEditLesson"
              @adaptUnitPlan="adaptUnitPlan"
              @editUnitLesson="editUnitLesson"
              @callViewReflection="viewReflection"
              @callDeleteItem="deleteItem"
              @callUpdateItem="updateItem">
            </plan-center>
          </el-col>
        </el-row>
        <vue-flex-waterfall
            v-else
            col="3"
            ref="category-flex-waterfall"
            col-spacing="12"
            :breakByContainer="true"
          >
          <div v-for="(planCenter,index) in planCenters" :key="index" style="width: calc(33% - 6px)">
            <plan-center
              :class="'planCenter' + index"
              :showQuickAddLesson="showQuickAddLesson"
              :index="index"
              :isOnlyView="isOnlyView"
              :edit="(edit || review) && !draging && !isTemplate"
              :isTemplate="isTemplate"
              :frameworkData="frameworkData"
              :planCenter="planCenter"
              :long="true"
              :isFromUnitDetail="isFromUnitDetail"
              :showApply="showApply"
              :category="category"
              :planType="planType"
              :isLocked="isLocked"
              :unitNumber="unitNumber"
              :color-index="index"
              :tags="tags"
              :children="children"
              :draging="draging"
              @hideAllAiAddLessonGuide="hideAllAiAddLessonGuide"
              @scrollToCopyLessonHandle="scrollToCopyLessonHandle"
              @callUpdateTag="updateTag"
              @callDeleteTag="deleteTag"
              @callDeleteCenter="deleteCenter"
              @callUpdateCenter="updateCenter"
              @callOpenLessonModal="openLessonModal"
              @callOpenAddLessonModal="openAddLessonModal"
              @callBatchSave="batchSave"
              @singleEditLesson="singleEditLesson"
              @adaptUnitPlan="adaptUnitPlan"
              @editUnitLesson="editUnitLesson"
              @callViewReflection="viewReflection"
              @callDeleteItem="deleteItem"
              @callUpdateItem="updateItem">
            </plan-center>
          </div>
        </vue-flex-waterfall>
        <el-row v-if="!temp" :gutter="12">
          <el-col :span="8">
            <el-button v-if="edit && !draging" style="width: 100%;height: 30px;font-weight: normal;color: #111c1c;" class="add-padding-tb-6" icon="el-icon-plus" @click="addCenter()">
              {{ $t('loc.curriculum52') }}
            </el-button>
          </el-col>
          <div class="add-padding-t-30">
            <span class="font-size-16" v-show="(!edit || draging) && planCenters.length === 0">{{ getCenterEmptyTip }}</span>
          </div>
        </el-row>
        <span v-if="isTemplate" class="plan-category-info">
          <i class="lg-icon lg-icon-book-open"></i>
          {{ $t('loc.enterActivity') }}
        </span>
      </div>
      <!-- 下方需要可添加课程的内容 -->
      <!-- 一列 -->
      <div v-if="category.type === 'BOTTOM_WEEK_COL' || category.type === 'BOTTOM_WEEK_TEXT_COL'" class="plan-col merged-col plan-drag-category" :data-category-info="getCategoryInfo(category.id, 1)" :class="[isTemplate ? 'activity-col-min' : 'activity-col', lastRow ? 'lg-border-bottom-right-radius-8': '' ]">
        <span v-if="isTemplate && category.type === 'BOTTOM_WEEK_COL'" class="plan-category-info lg-padding-left-4">
          <i class="lg-icon lg-icon-book-open"></i>
         {{ $t('loc.enterActivity') }}
        </span>
        <span v-if="isTemplate && category.type === 'BOTTOM_WEEK_TEXT_COL'" class="plan-category-info lg-padding-left-4">{{ $t('loc.planPleaseEnter') }}</span>
        <plan-item
          :showQuickAddLesson="showQuickAddLesson"
          :activeGuideDomId="activeGuideDomId"
          v-for="(item,index) in getItems(1)"
          :key="item.id"
          ref="weekItem"
          :type="getType"
          :edit="(edit || review) && !draging && !isTemplate"
          :adminEdit="adminEdit"
          :isTemplate="isTemplate"
          :frameworkData="frameworkData"
          :children="children"
          :item="item"
          :planType="planType"
          :long="true"
          :unitNumber="unitNumber"
          :editTemplate="editTemplate"
          :showAiGuide="getShowAiGuide(null,index,getItems(1),'ONE_ROW')"
          :categoryName="category.name"
          :dayLessonCount="getDayLessonCount(getItems(1))"
          :copyLessonLoadingDay="copyLessonLoadingDay"
          @copyLesson="copyLesson"
          @hideAllAiAddLessonGuide="hideAllAiAddLessonGuide"
          @callOpenLessonModal="openLessonModal"
          @callOpenAddLessonModal="openAddLessonModal"
          @callBatchSave="batchSave"
          @adaptUnitPlan="adaptUnitPlan"
          @editUnitLesson="editUnitLesson"
          @singleEditLesson="singleEditLesson"
          @callViewReflection="viewReflection"
          @callDeleteItem="deleteItem"
          @callUpdateItem="updateItem">
        </plan-item>
      </div>

      <!-- 五列 -->
      <template v-if="category.type === 'TOP_DAY_COL' || category.type === 'BOTTOM_DAY_COL' || category.type === 'BOTTOM_DAY_TEXT_COL'">
        <div v-for="(weekDay, i) in weekDays" :key="i" :data-category-info="getCategoryInfo(category.id, weekDay.day)" class="plan-col plan-drag-category" :class="[`week-${weekDay.day}`, isTemplate ? 'activity-col-min' : 'activity-col', lastRow && i === 4 ? 'lg-border-bottom-right-radius-8': '']" @click="clickDayCol(weekDay.day)" >
          <span v-if="isTemplate && i === 0 && category.type === 'BOTTOM_DAY_COL'" class="plan-category-info lg-padding-left-4">
            <i class="lg-icon lg-icon-book-open"></i>
            {{ $t('loc.enterActivity') }}
          </span>
          <span v-if="isTemplate && i === 0 && category.type === 'BOTTOM_DAY_TEXT_COL'" class="plan-category-info lg-padding-left-4">{{ $t('loc.planPleaseEnter') }}</span>
          <plan-item
            class="plan-item-drag"
            :isOnlyView="isOnlyView"
            :showQuickAddLesson="showQuickAddLesson"
            :adminEdit="adminEdit"
            :editTemplate="editTemplate"
            :showAiGuide="getShowAiGuide(i,index,getItems(weekDay.day),'MANY_ROW')"
            :activeGuideDomId="activeGuideDomId"
            v-for="(item, index) in getItems(weekDay.day)"
            :key="item.id"
            :ref="'item' + weekDay.day"
            :type="getType"
            :planStatus="planStatus"
            :edit="(edit || review) && !draging && !isTemplate"
            :review="review"
            :itemIndex="index"
            :planType="planType"
            :isTemplate="isTemplate"
            :frameworkData="frameworkData"
            :children="children"
            :isFromUnitDetail="isFromUnitDetail"
            :showApply="showApply"
            :isLocked="isLocked"
            :categoryName="category.name"
            :isFirstActivity="isFirstActivity(i)"
            :unitNumber="unitNumber"
            :class="{'m-t-sm': index != 0}"
            :dayLessonCount="getDayLessonCount(getItems(weekDay.day))"
            :copyLessonLoadingDay="copyLessonLoadingDay"
            @copyLesson="copyLesson"
            @hideAllAiAddLessonGuide="hideAllAiAddLessonGuide"
            @callOpenLessonModal="openLessonModal"
            @callOpenAddLessonModal="openAddLessonModal"
            @callBatchSave="batchSave"
            @singleEditLesson="singleEditLesson"
            @adaptUnitPlan="adaptUnitPlan"
            @editUnitLesson="editUnitLesson"
            @callViewReflection="viewReflection"
            :item="item"
            @callDeleteItem="deleteItem"
            @callUpdateItem="updateItem">
          </plan-item>
          <!--当复制课程时，渲染一个虚拟的 div ，防止一天的课程超出 5 个-->
          <div :class="{'plan-drag-info' : showUnrealLesson(weekDay.day)}" v-if="showUnrealLesson(weekDay.day)"></div>
        </div>
      </template>

      <!-- 周一至周五 -->
      <template v-if="category.type == 'WEEK_ROW'">
        <div v-for="(weekDay, index) in weekDays" :key="index" class="plan-col week-col font-bold" :class="`week-${weekDay.day}`">
          {{weekDay.name}}
        </div>
      </template>
    </div>
    <!-- 添加分割线 -->
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { planCenterTags, planITCenterTags, planKCenterTags } from '@/utils/constants'
import PlanItem from './PlanItem'
import PlanCenter from '../../lessonCurriculum/CurriculumWeekPlan/UnitCenter'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import VueFlexWaterfall from 'vue-flex-waterfall'
import Api from '@/api/lessons2'
import LessonUtils from '@/utils/lessonUtils'

export default {
  name: 'PlanCategory',
  components: { PlanItem, PlanCenter, VueFlexWaterfall },

  props: {
    mask: {
      type: Number
    },
    firstActivityIndex: {
      type: Number
    },
    showQuickAddLesson: {
      type: Boolean
    },
    editTemplate: {
      type: Boolean
    },
    temp: {
      type: Boolean
    },
    edit: {
      type: Boolean
    },
    showReflection: {
      type: Boolean
    },
    category: {
      type: Object
    },
    frameworkData: {
      type: Array
    },
    children: {
      type: Array
    },
    lastReflection: {
      type: String
    },
    lastReflectionLoading: {
      type: Boolean
    },
    activeGuideDomId: {
      type: String
    },
    // 是否是模板
    isTemplate: {
      type: Boolean
    },
    // 是否能编辑模板
    canEditTemplate: {
      type: Boolean
    },
    planCenters: {
      type: Array
    },
    canAddCenters: {
      type: Boolean
    },
    unitNumber: {
      type: Number
    },
    adminEdit: {
      type: Boolean
    },
    centerTags: {
      type: Array
    },
    lastRow: {
      type: Boolean
    },
    showBorder: {
      type: Boolean
    },
    review: {
      type: Boolean
    },
    draging: {
      type: Boolean
    },
    isFromUnitDetail: {
      type: Boolean,
      default: false
    },
    // 周计划状态
    planStatus: {
      type: String,
      default: ''
    },
    showApply: {
      type: Boolean,
      default: false
    },
    // 是否锁定（其他人正在编辑周计划）
    isLocked: {
      type: Object,
      default: null
    },
    // 周计划类型
    planType: {
      type: String,
      default: ''
    },
    isOnlyView: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      weekDays: [],
      hoverTheme: false,
      hoverCategory: false,
      addCategoryOneLoading: false,
      addCategoryFiveLoading: false,
      addCategoryCentersLoading: false,
      addCategoryOneTextLoading: false, // 控制一行长文本加载
      addCategoryFiveTextLoading: false, // 控制一行五列文本加载
      guideAddHover: false, // 引导时显示加号的颜色
      guideRemoveHover: false, // 引导时显示减号的颜色
      n: 3,
      centerName: undefined,
      hoverCenter: false,
      colorIndex: 1,
      tags: [],
      focusInput: false, // 是否聚焦分组名称输入框
      addVisible: false, // 控制添加气泡显示
      deleteVisible: false, // 控制删除气泡显示
      resizeObserver: null, // center 课程尺寸变化监听器
      copyLessonLoadingDay: [] // 复制课程时需要 Loading 的天数
    }
  },

  created () {
    let param = {
      label: 'loc.curriculum85',
      options: []
    }
    if (this.centerTags && this.centerTags.length > 0) {
      this.centerTags.forEach(x => {
        if (this.tags[0].options.find(y => y.value == x.tagName)) {
          this.tags[0].options.filter(y => y.value == x.tagName).forEach(y => {
            y.disabled = x.disabled
          })
        } else if (x.tagName && x.tagName.trim() != '') {
          param.options.push({
            value: x.tagName,
            label: x.tagName,
            disabled: x.disabled,
            isDefault: false
          })
        }
      })
      this.tags.push(param)
    }
    this.initWeekDays()
    if (this.category && this.category.type === 'BOTTOM_CENTER_ROW') {
      this.category.items = this.planCenters.items
      this.category.name = this.$t('loc.centerWeek')
    }
    window.addEventListener('resize', () => {
      this.resizeTextarea()
    })
  },
  mounted () {
    this.$nextTick(() => {
      // 获取第一个 center 课程的 dom 元素
      const centerDom = this.$el.querySelector('.planCenter0')
      if (centerDom) {
        // 监听 center 课程 dom 元素宽度变化，只需要监听第一个即可，宽度是百分比变化，第一个变化，其他几个必定也变化
        this.resizeObserver = new ResizeObserver((entries) => {
          // 宽度一旦变化，重新计算尺寸
          this.resizeAllArea()
        })
        // 开启监听
        this.resizeObserver.observe(centerDom)
      }
    })
  },
  beforeDestroy () {
    // 组件销毁前去除监听
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      planCenterType: state => state.lesson.planCenterType
    }),
    // 获取添加 Centers / Stations 标题
    getAddCenterTitle () {
      if (this.$route.name === 'lessonsSetting') {
        return this.$t('loc.unitPlannerStep3Centers') + ' / ' + this.$t('loc.unitPlannerStep3Stations')
      } else if (this.planCenterType == 'IT' || this.planCenterType == 'PS') {
          return this.$t('loc.unitPlannerStep3Centers')
      } else {
        // 其他分组显示 Stations
        return this.$t('loc.unitPlannerStep3Stations')
      }
    },
    // 获取分组名称
    getCategoryName () {
      // 如果是 Center 分组，显示 Centers 或 Stations
      if (this.category && this.category.type == 'BOTTOM_CENTER_ROW') {
        // 如果是课程设置页面，显示 Centers / Stations
        if (this.$route.name === 'lessonsSetting') {
          return this.$t('loc.unitPlannerStep3Centers') + ' / ' + this.$t('loc.unitPlannerStep3Stations')
        }
        // IT 和 PS 分组显示 Centers
        if (this.planCenterType == 'IT' || this.planCenterType == 'PS') {
          return this.category.name
        } else {
          // 其他分组显示 Stations
          return this.$t('loc.unitPlannerStep3Stations')
        }
      }
      // 非 Center 分组，显示分组名称
      return this.category && this.category.name
    },
    // 获取 Centers 活动为空的提示语
    getCenterEmptyTip () {
      // IT 和 PS 分组显示 Centers 空提示语
      if (this.planCenterType == 'IT' || this.planCenterType == 'PS') {
        return this.$t('loc.curriculum69')
      } else {
        // 其他分组显示 Stations 空提示语
        return this.$t('loc.curriculum107')
      }
    },
    canAddFive () {
      let type = this.category.type
      return type == 'WEEK_ROW' || type.indexOf('BOTTOM_') != -1
    },
    canRemove () {
      let type = this.category.type
      return type != 'THEME_ROW' && type != 'REFLECTION_ROW' && type != 'WEEK_ROW'
    },
    isTopRow () {
      let type = this.category.type
      return !(type == 'WEEK_ROW' || type.indexOf('BOTTOM_') != -1)
    },
    canEdit () {
      if ((!this.edit && !this.review) || !this.canEditTemplate || this.draging) {
        return false
      }
      let type = this.category.type
      return (type != 'THEME_ROW' && type != 'REFLECTION_ROW' && type != 'WEEK_ROW' && type != 'BOTTOM_CENTER_ROW') || (this.canEditTemplate && type == 'THEME_ROW')
    },
    firstItem () {
      if (this.category.items && this.category.items.length > 0) {
        return this.category.items[0]
      }
      let newItem = {
        id: tools.uuidv4(),
        dayOfWeek: 1,
        categoryId: this.category.id,
        name: '',
        planId: this.category.planId
      }
      this.category.items.push(newItem)
      return newItem
    },
    getItems () {
      return function (dayOfWeek) {
        // 分类下指定日期的项目列表
        let items = []
        // 如果未传入dayOfWeek参数，说明这个分类是长单元格，项目直接取items，否则生成新的item数组进行展示
        if (!dayOfWeek) {
          items = this.category.items
        } else {
          items = this.category.items.filter(item => item.dayOfWeek === dayOfWeek)
        }
        // 每个单元格做限制最多添加五个项目，超过5个项目，直接返回
        let filterItems = items.filter(item => !this.isEmptyItem(item))
       if (filterItems.length >= 5) {
          // 循环周计划项，给每个项设置排序
          items.forEach((item, index) => {
            if (!item.sortNum) {
              item.sortNum = index
            }
          })
          return filterItems
        }
        // 是否有空的项目可以新增
        let hasEmpty = false
        if (items.length > 0) {
          // 循环项目，判断是否存在空项目
          for (let i = 0; i < items.length; i++) {
            let lastItem = items[i]
            hasEmpty = this.isEmptyItem(lastItem) || hasEmpty
          }
          // 如果有空项目并且不是文本单元格，过滤掉空项目，剩余一个空项目
          if (hasEmpty) {
            let empty = items.find(item => this.isEmptyItem(item))
            items = items.filter(item => !this.isEmptyItem(item))
            // 不是文本单元格
            if (this.category.type.indexOf('_TEXT_') === -1 || items.length === 0) { items.push(empty) }
          }
        }
        // 如果没有空单元格并且不是文本单元格，新加一个空的单元格
        if ((!hasEmpty && this.category.type.indexOf('_TEXT_') === -1) || (items.length === 0 && this.category.type.indexOf('_TEXT_') != -1)) {
          // 新生成的item如果是长单元格的 dayofweek 默认是周一
          let newItem = {
            id: tools.uuidv4(),
            dayOfWeek: dayOfWeek == undefined ? 1 : dayOfWeek,
            categoryId: this.category.id,
            name: '',
            planId: this.category.planId,
            sortNum: items.length
          }
          // 向分组中加入新的空项目
          // this.$emit('callAddItem', newItem, this.category.id)
          this.category.items.push(newItem)
          // 如果是长单元格，显示的就是分组的项目，不需要在向显示的items中添加
          if (dayOfWeek) {
            items.push(newItem)
          }
        }
        // 循环周计划项，给每个项排序
        items.forEach((item, index) => {
          if (!item.sortNum) {
            item.sortNum = index
          }
        })
        return items
      }
    },
    getType () {
      if (!this.category) {
        return '';
      }
      switch (this.category.type) {
        case 'BOTTOM_DAY_TEXT_COL':
          return 'DAY_TEXT_COL';
        case 'BOTTOM_WEEK_TEXT_COL':
          return 'WEEK_TEXT_COL';
        case 'BOTTOM_WEEK_COL':
          return 'WEEK_COL';
        case 'TOP_DAY_COL':
        case 'BOTTOM_DAY_COL':
          return 'DAY_COL';
        default:
          return '';
      }
    }
  },
  watch: {
    // 监听 planCenterType 变化，重新设置标签
    planCenterType: {
      handler(val) {
        if (val) {
          // 如果 val 是 IT，则 tags 展示 planITCenterTags
          if (val.toUpperCase() === 'IT') {
            this.tags = JSON.parse(JSON.stringify(planITCenterTags))
          } else if (val.toUpperCase() === 'K') {
            this.tags = JSON.parse(JSON.stringify(planKCenterTags))
          } else {
            this.tags = JSON.parse(JSON.stringify(planCenterTags))
          }
        } else {
          this.tags = JSON.parse(JSON.stringify(planCenterTags))
        }

        if (this.centerTags && this.centerTags.length > 0) {
          this.centerTags.forEach(x => {
            if (this.tags[0].options.find(y => y.value == x.tagName)) {
              this.tags[0].options.filter(y => y.value == x.tagName).forEach(y => {
                 y.disabled = true
              })
            }
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    // 滚动页面到复制的课程的位置
    scrollToCopyLesson (toItemId) {
      // 获取课程的 ref
      const elements = [this.$refs.weekItem, this.$refs.item1, this.$refs.item2, this.$refs.item3, this.$refs.item4, this.$refs.item5]
        .filter(arr => Array.isArray(arr)) // 过滤掉 null 或 undefined 的数组
        .flat()
      this.scrollToCopyLessonHandle(elements, toItemId)
    },
    // 滚动到新复制出的课程的位置
    scrollToCopyLessonHandle (elements, toItemId) {
      if (elements) {
        // 找到课程 id 等于新复制课程的 id
        const targetElement = elements.find(
          element => element && element.item.id === toItemId
        )
        // 新复制的课程滚动到页面中间
        if (targetElement) {
          targetElement.$el.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }
    },
    // 获取当前活动这天课程数量
    getDayLessonCount (lesson) {
      return lesson.filter(plan => plan.lessonId || plan.name || plan.link).length
    },
    // 当复制课程时，成功之前，渲染一个虚拟的 div ，模拟一个课程，防止一天的课程超出 5 个
    showUnrealLesson (day) {
      return this.copyLessonLoadingDay.includes(day)
    },

    // 周计划复制课程
    copyLesson (item, planCenter, inLessonDialog, callback) {
      const lessonCount = this.getDayLessonCount(this.getItems(item.dayOfWeek))
      LessonUtils.copyLesson(item, planCenter, lessonCount, this.copyLessonLoadingDay, inLessonDialog, (copyItem) => {
        if (copyItem && copyItem.id) {
          // 周计划活动数组中添加新 item
          LessonUtils.updateCategory(copyItem, item.id, this.category.items)
          this.$nextTick(() => {
            // 复制完成后滚动到对应位置
            this.scrollToCopyLesson(copyItem.id)
          })
        }
        // 回调关闭课程拖拽禁用
        callback()
      })
    },

    // 获取当前周计划 item 是否需要引导
    getShowAiGuide (planIndex, planItemIndex, plans, type) {
      // 过滤周一的课程
      const effectivePlans = plans.filter(plan => plan.dayOfWeek === 1 && ((plan.lessonId && plan.lessonId != '') || plan.name || plan.link))
      // 周一的课程是否满
      const isFallday = effectivePlans && effectivePlans.length === 5
      // 需要开启引导的课程位置，如果课程满，给第一个课程展示引导框
      const showAiGuideIndex = isFallday ? 0 : effectivePlans.length
      // 判断该模块是不是能 Add Activity 的第一个模块
      const isFirstActivity = this.mask === this.firstActivityIndex
      // 第一个模块中的最后一个
      const isLastPlanItem = planItemIndex === showAiGuideIndex

      if ((type === 'ONE_ROW' && isFirstActivity && isLastPlanItem) ||
        (type === 'MANY_ROW' && isFirstActivity && planIndex === 0 && isLastPlanItem)) {
        // 当有新的课程需要引导时，关闭之前的引导
        this.hideAllAiAddLessonGuide()
        return { show: true, isFallday: isFallday }
      } else {
        return { show: false, isFallday: isFallday }
      }
    },
    // 关闭之前的课程引导
    hideBeforeAiAddLessonGuide(){
      const elements = this.$refs.weekItem || this.$refs.item1
      if (elements){
        for (const element of elements){
          element.hideAiAddLessonGuide()
        }
      }
    },
    // 关闭之前的 Ai 生成周计划引导
    hideAllAiAddLessonGuide () {
      this.$emit('hideAllAiAddLessonGuide')
    },
    resizeAllArea () {
      // 计算 category-flex-waterfall 的高度
      this.resizeTextarea()
      this.$refs['category-flex-waterfall'] && this.$nextTick(() => {
        this.$refs['category-flex-waterfall'].containerHeight += 10
      })
    },
    /**
     * 获取拖拽分组 item 信息
     */
    getCategoryInfo (categoryId, dayOfWeek) {
      return JSON.stringify({
        categoryId: categoryId,
        dayOfWeek: dayOfWeek
      })
    },
    /**
     * 初始化周信息
     */
    initWeekDays () {
      this.weekDays = [
        {
          day: 1,
          name: 'Monday'
        }, {
          day: 2,
          name: 'Tuesday'
        }, {
          day: 3,
          name: 'Wednesday'
        }, {
          day: 4,
          name: 'Thursday'
        }, {
          day: 5,
          name: 'Friday'
        }
      ]
    },
    // 是否是第一个 Activity
    isFirstActivity (index) {
      return index === 4 && this.mask === 0
    },
    /**
     * 鼠标移入行中，显示添加/删除图标
     */
    enterRow (data) {
      if (!this.edit) {
        return
      }
      this.$set(data, 'showOperation', true)
    },

    /**
     * 鼠标移除行外，隐藏添加/删除图标
     */
    leaveRow (data) {
      if (!this.edit) {
        return
      }
      this.$set(data, 'showOperation', false)
    },

    enterRemove () {
      if (!this.edit) {
        return
      }
      if (!this.canRemove) {
        this.deleteVisible = true
        return
      }
      this.$set(this.category, 'showRemoveBorder', true)
      this.deleteVisible = true
    },

    leaveRemove () {
      if (!this.edit) {
        return
      }
      this.$set(this.category, 'showRemoveBorder', false)
      this.deleteVisible = false
    },

    enterAdd (isAddCard = false) {
      if (!this.edit) {
        return
      }
      this.$set(this.category, 'showAddBorder', true)
      if (!isAddCard) {
        // 不是悬停在操作按钮卡片上,控制汽包显示
        this.addVisible = true
      }
    },

    leaveAdd () {
      if (!this.edit) {
        return
      }
      this.$set(this.category, 'showAddBorder', false)
      this.addVisible = false
    },

    removeCategory () {},

    addCategory (col, data) {
      let newType = this.isTopRow ? 'TOP_' : 'BOTTOM_'
      if (col === 2) {
        // 添加 Center
        newType = 'BOTTOM_CENTER_ROW'
      } else if (col === 3) {
        // 一行长文本
        newType = newType + 'WEEK_TEXT_COL'
      } else if (col === 4) {
        // 一行五列文本
        newType = newType + 'DAY_TEXT_COL'
      } else {
        newType = newType + (this.canAddFive && col === 5 ? 'DAY_COL' : 'WEEK_COL')
      }
      if (col === 1) {
        this.addCategoryOneLoading = true
      } else if (col === 2) {
        this.addCategoryCentersLoading = true
      } else if (col === 3) {
        // 添加一行长文本加载
        this.addCategoryOneTextLoading = true
      } else if (col === 4) {
        // 添加一行五列文本加载
        this.addCategoryFiveTextLoading = true
      } else {
        this.addCategoryFiveLoading = true
      }
      this.$emit('callAddCategory', this.category, newType, () => {
        this.addCategoryOneLoading = false
        this.addCategoryFiveLoading = false
        this.addCategoryCentersLoading = false
        this.addCategoryFiveTextLoading = false // 添加完成取消加载状态
        this.addCategoryOneTextLoading = false
        this.$set(data, 'showOperation', false)
        this.$set(this.category, 'showAddBorder', false)
      })
    },

    deleteCategory () {
      if (!this.canRemove) {
        return
      }
      if (!this.category.items || this.category.items.length === 0) {
        this.$emit('callDeleteCategory', this.category)
        this.resetCenterTag()
        return
      }
      this.$confirm(this.$t('loc.plan40'), this.$t('loc.cfm'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        this.$emit('callDeleteCategory', this.category)
        this.resetCenterTag()
      }).catch(() => {
      })
    },

    // 重置center分组的标签
    resetCenterTag () {
      if (this.category.type == 'BOTTOM_CENTER_ROW') {
        this.tags.forEach(tag => {
          tag.options.forEach(option => {
            option.disabled = false
          })
        })
      }
    },

    changeCategoryName () {
      this.$emit('callUpdateCategory', this.category)
    },

    updateTheme () {
      this.$emit('callUpdateTheme', this.firstItem ? this.firstItem.name : '')
    },

    getItem (dayOfWeek) {
      let items = this.category.items
      let currentItem
      items.forEach(i => {
        if (i.dayOfWeek === dayOfWeek) {
          currentItem = i
        }
      })
      if (currentItem) {
        return currentItem
      }
      currentItem = {
        dayOfWeek: dayOfWeek,
        categoryId: this.category.id,
        name: '',
        planId: this.category.planId
      }
      this.category.items.push(currentItem)
      return currentItem
    },

    // 打开选择课程弹窗
    openLessonModal (selectLessonFunc) {
      this.$emit('callOpenLessonModal', selectLessonFunc)
    },

    // 打开快速添加课程弹窗
    openAddLessonModal (selectLessonFunc, name, categoryName) {
      this.$emit('callOpenAddLessonModal', selectLessonFunc, name, categoryName)
    },

    updateItem () {
      this.$emit('callUpdateItem')
    },

    batchSave () {
      this.$emit('callBatchSave')
    },

    viewReflection (lessonId) {
      this.$emit('callViewReflection', lessonId)
    },
    singleEditLesson (itemId) {
      this.$emit('callSingleEditLesson', itemId)
    },
    // 改编单元周计划项回调函数
    adaptUnitPlan (item) {
      this.$emit('callAdaptUnitPlan', item)
    },
    // 编辑单元课程信息回调函数
    editUnitLesson (item) {
      this.$emit('callEditUnitLesson', item)
    },
    // 添加Center卡片项
    addCenter () {
      let params = {
        name: '',
        colorIndex: this.colorIndex,
        planId: this.category.planId
      }
      this.$emit('callAddCenter',params)
    },
    // 删除Center卡片项
    deleteCenter (id, hasItem) {
      this.$emit('callDeleteCenter', id, hasItem)
    },
    // 更新Center卡片中的Item项
    updateCenterItem () {},
    // 更新Centers行中的Center卡片
    updateCenter (params) {
      this.$emit('callUpdateCenter',params)
    },
    // 是否是空项目
    isEmptyItem (item) {
      if (!item) {
        return true
      }
      return (!item.name || item.name.trim().length === 0) && (!item.measureIds || item.measureIds.length === 0) && (!item.childIds || item.childIds.length === 0)
    },

    // 点击活动格，自动聚焦
    clickDayCol (dayOfWeek) {
      if (this.draging) {
        return
      }
      let items = this.getItems(dayOfWeek)
      if (items) {
        this.$refs['item' + dayOfWeek][items.length - 1].clickItem()
      }
    },
    updateTag (newVal, oldVal) {
      // 默认添加的标签不是默认标签
      let isDefault = false
      // 如果新的标签已经存在，isDefault属性就是原来的值
      if (this.tags[0].options.find(x => x.value === newVal)) {
        isDefault = this.tags[0].options.filter(x => x.value === newVal)[0].isDefault
      }
      // 如果新的标签已经存在，就把它的disabled属性设置为true
      if (newVal) {
        if (this.tags[0].options.find(x => x.value === newVal)) {
          this.tags[0].options.filter(x => x.value === newVal)[0].disabled = true
        } else if (this.tags[1].options.find(x => x.value === newVal)) {
          this.tags[1].options.filter(x => x.value === newVal)[0].disabled = true
        } else {
          // 如果新的标签不存在，就添加一个新的标签
          this.tags[1].options.push({
            value: newVal,
            label: newVal,
            disabled: true,
            isDefault: isDefault
          })
        }
      }
      // 如果旧的标签存在，就把它的disabled属性设置为false
      if (oldVal) {
        if (this.tags[0] && this.tags[0].options.find(x => x.value === oldVal)) {
          this.tags[0].options.filter(x => x.value === oldVal)[0].disabled = false
        }
        if (this.tags[1] && this.tags[1].options.find(x => x.value === oldVal)) {
          this.tags[1].options.filter(x => x.value === oldVal)[0].disabled = false
        }
      }
    },
    deleteTag (tag) {
      // 被选中的不能删除
      if (tag.disabled) {
        return
      }
      LessonApi.deleteTag(tag.value).then(res => {
        this.tags[1].options = this.tags[1].options.filter(x => x.value !== tag.value)
      }).catch(error => {})
    },
    // 根据 Id 删除 item
    deleteItem (id ,categoryId, centerId, callback) {
      this.$emit('callDeleteItem', id, categoryId, centerId, callback)
    },
    // 重新计算textarea大小
    resizeTextarea () {
      this.$nextTick(() => {
        this.$refs.categoryInput && this.$refs.categoryInput.resizeTextarea()
        this.$refs.themeInput && this.$refs.themeInput.resizeTextarea()
        this.$refs.planItemInput && this.$refs.planItemInput.resizeTextarea()
        // 计算 category-flex-waterfall 的高度
        this.$refs['category-flex-waterfall'] && this.$refs['category-flex-waterfall'].updateOrder()
      })
    }
    // 点击整周的活动格，自动聚焦
    // clickWeekCol () {
    //   this.$refs.weekItem.clickItem()
    // }
  }
}
</script>

<style lang="less" scoped>
// 周一至周五颜色
@week-1-color: #10B3B7;
@week-1-color-background: #10B3B733;
@week-2-color: #F2994A;
@week-2-color-background: #F2994A33;
@week-3-color: #67C23A;
@week-3-color-background: #67C23A33;
@week-4-color: #F56C6C;
@week-4-color-background: #F56C6C33;
@week-5-color: #9B51E0;
@week-5-color-background: #9B51E033;

.plan-category-info {
  color: #909399;
}

.plan-row {
  display: flex;
  position: relative;
}
.plan-col, .plan-classified-col {
  background-color: #fff;
  padding: 10px;
  border-right: solid 1px #DADADA;
  border-bottom: solid 1px #DADADA;
  // border-top: solid 1px #DADADA;
}
.plan-col {
  width: 120px;
  flex: 1 0 auto;
}
.operation-col {
  width: 20px;
  flex: none;
  // position: sticky;
  left: 0;
  background-color: transparent;
  // z-index: 1;
  // overflow: visible;
}
.plan-classified-col {
  width: 120px;
  flex: none;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  // position: sticky;
  // left: 20px;
  background-color: #fff;
  // z-index: 1;
  border-left: solid 1px #DADADA;
}
.row-border {
  border: dashed 1px #98a6ad;
}
.week-col {
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.week-col.week-1 {
  position: relative;
  color: @week-1-color;
}
.week-col.week-2 {
  position: relative;
  color: @week-2-color;
}
.week-col.week-3 {
  position: relative;
  color: @week-3-color;
}
.week-col.week-4 {
  position: relative;
  color: @week-4-color;
}
.week-col.week-5 {
  position: relative;
  color: @week-5-color;
}
.week-col.week-1:after {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: @week-1-color-background;
}
.week-col.week-2:after {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: @week-2-color-background;
}
.week-col.week-3:after {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: @week-3-color-background;
}
.week-col.week-4:after {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: @week-4-color-background;
}
.week-col.week-5:after {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: @week-5-color-background;
}
.merged-col {
  min-height: 40px;
  min-width: 600px;
}
.min-col-height {
  min-height: 100px;
}
.activity-col {
  min-height: 150px;
}
.activity-col-min {
  min-height: 100px;
  padding: 6px;
}

.merged-col /deep/ textarea, .plan-classified-col /deep/ textarea {
  padding: 8px 2px !important;
}

.plan-col /deep/ textarea, .plan-classified-col /deep/ textarea {
  padding: 0;
  border: none;
  overflow: hidden;
  resize : none;
}

.plan-classified-col /deep/ textarea {
  text-align: center;
}

.footer-area {
  text-align: right;
  margin-bottom: 50px;
}

.category-operation {
  z-index: 20;
  // background-color: #f0f3f4;
  position: absolute;
  left: 0px;
  bottom: -22px;
  // height: 100%;
  i {
    font-size: 20px;
    width: 20px;
    cursor: pointer;
  }
}
.remove-category:hover, .remove-category-hover {
  color: #f05050;
}
.add-category:hover, .add-category-hover {
  color: #10B3B7;
}

.remove-border {
  .plan-classified-col {
    border-left: dashed 1px #f05050 !important;
    border-top: dashed 1px #f05050 !important;
    border-bottom: dashed 1px #f05050 !important;
  }
  .plan-col {
    border-top: dashed 1px #f05050 !important;
    border-bottom: dashed 1px #f05050 !important;
  }
  .week-5, .merged-col {
    border-right: dashed 1px #f05050 !important;
  }
}
.add-line {
  margin-left: 20px;
  height: 5px;
  width: calc(100% - 20px);
  border-top: dashed 1px #10B3B7 !important;
  border-bottom: dashed 1px #10B3B7 !important;
}
.col-type {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  /deep/ .el-card__body {
    padding: 0;
  }
  /deep/ .el-button--text {
    margin-left: 0;
    padding: 5px 10px !important;
    color: #323338;
  }
  /deep/ .el-button--text:hover {
    color: #10B3B7;
    background-color: #e7f7f8;
  }
  /deep/ .el-button--text.is-disabled:hover {
    color: #323338;
    background-color: #fff;
  }
}

.pre-area {
  white-space: pre-line;
}
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.box-card {
}

.lesson-media {
  flex: none;
  width: 35%;
  // height: 60px;
}

///deep/ .el-card__header{
//  padding: 13px 20px !important;
//  background-color: cornflowerblue !important;
//}
//
///deep/ .el-input__inner{
//  background-color: cornflowerblue;
//  height: 22px;
//  color: black;
//  font-weight: bold;
//  font-size: 16px;
//  border: 1px dashed black;
//}

/deep/ .el-input__icon {
  line-height: 20px !important;
}
textarea::placeholder {
  color: #676879;
}
textarea::-webkit-input-placeholder {
  color: #676879;
}

.merged-col /deep/.lesson-media, .merged-col /deep/.lesson-media-small {
  width: 18%;
}
.week-1, .week-2, .week-3, .week-4, .week-5 {
  /deep/.lesson-media {
    width: 100%;
  }
  /deep/.lesson-media-small {
    width: 100%;
  }
}
</style>
