<template>
    <div>
        <!-- @opened="getPersonalizeChildListData" -->
        <el-dialog :visible.sync="personalizePlanShow"
                   :show-close=false
                   custom-class="assign-teacher-dialog"
                   :append-to-body="true"
                   :modal-append-to-body="true"
                   :close-on-click-modal="false"
                   :top="'60px'"
                   @close="isOpenAddChildrenTipModal()"
                   :width="emptyPageShow ? '905px': isCurriculumPlugin ? '800px' : '70%'">
            <!--弹窗的头部信息-->
            <div slot="title" class="dialog-title">
                <div class="dialog-title-content">
                    {{
                        $t('loc.unitPlannerPersonalizePlan')
                    }}
                </div>
                <div class="dialog-close" @click="handleClose">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <g clip-path="url(#clip0_3538_30865)">
                            <path d="M20 0H0V20H20V0Z" fill="white" fill-opacity="0.01"/>
                            <path d="M5.83203 5.83337L14.1654 14.1667" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                            <path d="M5.83203 14.1667L14.1654 5.83337" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_3538_30865">
                                <rect width="20" height="20" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            </div>
            <!--按照不同的平台显示不同的内容-->
            <div class="dialog-body">
                <div class="w-full">
                    <div class="m-b-sm">
                        {{
                            $t('loc.adaptUnitPlanner2')
                        }}
                    </div>
                    <div class="add-margin-t-16 add-margin-b-16">
                        <el-alert
                            class="add-margin-b-10"
                            style="margin-top: -10px;"
                            v-if="showIEPButEligibilityIsEmptyPlugin"
                            :title="$t('loc.unitPlannerChildListTitle2')"
                            type="error"
                            :closable="false"
                            show-icon></el-alert>
                    </div>
                    <div class="w-full">
                        <PersonalizeToLearner v-if="isCurriculumPlugin"
                                              ref="personalizeToLearner"
                                              :childId="defaultChildId"
                                              :initPersonalizedForm="initPersonalizeForm"
                                              :updatePersonalizeValidate="personalizeValidate"
                                              :updatePersonalizeForm="personalizeUpdate"
                        />
                    </div>
                </div>
            </div>
            <div slot="footer">
                <div class="display-flex flex-row-between-reverse">
                    <!-- 一个 Save 按钮 -->
                    <el-button v-show="!emptyPageShow"
                               :loading="adaptedLoading"
                               :disabled="!hasTeacher || !initialized"
                               @click="updateGenerateUniversalDesignAndCLRData" type="primary">
                        {{
                            $t('loc.unitPlannerPersonalizePlanConfirm')
                        }}
                    </el-button>
                   <el-checkbox v-if="!isCurriculumPlugin" v-model="notAskAgain">Don't ask again</el-checkbox>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// import PersonalizeChildList from '@/views/modules/lesson2/lessonPlan/components/PersonalizeChildList.vue'
import PersonalizeToLearner from '@/views/modules/lesson2/unitPlanner/components/adaptUnits/PersonalizeToLearner.vue'
// import AssignTeacherGroups from '@/views/modules/lesson2/lessonPlan/components/AssignTeacherGroups.vue'
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import tools from "@/utils/tools";

export default {
    name: 'PersonalizePlan',
    components: { PersonalizeToLearner },
    props: {
      itemIds: {
        type: Array
      },
      lessonIds: {
        type: Array
      },
      planId: {
        type: String,
        default: ''
      }
    },
    data () {
        return {
            notAskAgain: false, // 后续是否还弹出当前页面
            selectGroupId: '', // 班级 Id
            selectCenterId: '', // 班级 Id
            hasTeacher: false, // 是否有老师
            personalizePlanShow: false, // Personalize Your Lesson Plan 弹窗是否展示
            emptyPageShow: false, // 空页面是否展示
            children: [], // 小孩列表
            personalizeChildListDataLoading: false, // PersonalizeChildList 组件的数据是否正在加载
            showGroupTeamDescription: false, // 是否展示 UDL 分组描述信息
            teacherGroups: [], // 老师的分组信息
            hasGroupTeams: false, // 是否存在老师分组信息
            personalizedForm: null, // 个性化表单
            validated: false, // 个性化表单验证
            getCenterLoading: false, // 获取学校和班级信息的 Loading
            defaultChildId: null, // 默认小孩 ID
            currentTeacherId: '', // 当前选中的老师的 Id
            initialized: false, // 是否初始化成功了
            showIEPButEligibilityIsEmptyChildren: false, // 是否展示 IEP 但是 Eligibility 为空的小孩的提示
            additionInfo: {}, // 从 PersonalizeChildList 中获取的数据
            adaptedLoading: false, // loading 状态
            isConfirm: false // 是否点击确认关闭的弹窗
        }
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser,
            isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
            showAIGroup: state => state.lesson.showAIGroup // 隐藏 AI 分组功能
        }),
        /**
         * 如果选择了 IEP 但 Eligibility 是空的小孩，则展示提示
         */
        showIEPButEligibilityIsEmptyPlugin() {
            // 如果 iep 为 true 但是 characteristics 为空，则展示提示
            if (this.personalizedForm &&
                this.personalizedForm.iep && (
                    !this.personalizedForm.characteristics
                    || this.personalizedForm.characteristics.length < 1
                )) {
                return true
            }
            // personalizedForm 中的其他数据都为空
            // 如果种族，地区，特征有一个不为空，验证通过
            const hasRace = this.personalizedForm && this.personalizedForm.race && this.personalizedForm.race.length > 0;
            const hasRegion = this.personalizedForm && this.personalizedForm.region && this.personalizedForm.region.length > 0;
            const hasCharacteristics = this.personalizedForm && this.personalizedForm.characteristics && this.personalizedForm.characteristics.length > 0;
            // 如果 language 存在，但是仅有 English，则展示提示
            if (this.personalizedForm && !hasRace && !hasRegion && !hasCharacteristics && this.personalizedForm.language && this.personalizedForm.language.length === 1 && this.personalizedForm.language.indexOf('English') !== -1) {
                return true
            }
            // 默认返回 false
            return false
        },
        emptyTeacher () {
            return this.$t('loc.noTeacher')
        },
        currentUserId () {
            if (!this.currentUser) {
                return ''
            }
            return this.currentUser.user_id
        },
        // 获取指定属性的值
        getAttrValue () {
            return function (child, attrName) {
                // 不存在返回空
                if (!child || !attrName) {
                    return ''
                }
                // 属性列表
                let attrs = child.attrs
                if (!attrs) {
                    return ''
                }
                // 匹配到的属性值
                let matchValues = null
                // 遍历属性列表
                attrs.forEach(attr => {
                    // 匹配属性名称
                    if (attr && attr.name && attr.name.trim().toLowerCase() === attrName.trim().toLowerCase()) {
                        // 属性值
                        let attrValues = attr.values
                        if (attrValues && attrValues.length > 0) {
                            matchValues = attrValues
                        }
                    }
                })
                // 如果有属性值，以逗号分割
                if (matchValues) {
                    return matchValues.join(', ')
                }
                // 没有值
                return ''
            }
        },
        /**
         * 判断是否是老师
         */
        canAdapter () {
            const { role2 = '' } = this.currentUser || {}
            let role = role2.toUpperCase()
            return role === 'COLLABORATOR' || role === 'FAMILY_SERVICE' || role === 'TEACHING_ASSISTANT' || role === 'TEACHER' || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
        },
        resolvedRouteHref () {
            if (this.canAdapter) {
                return this.$router.resolve({
                    name: 'manageChildren',
                    query: {
                        centerId: this.centerId,
                        groupId: this.groupId
                    }
                }).href
            } else {
                return '/#/manage_children'
            }
        },
        groupId: {
            get () {
                if (this.selectGroupId && this.selectGroupId !== '') {
                    return this.selectGroupId
                }
                // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
                const selectedGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
                if (selectedGroupId) {
                    return selectedGroupId
                }
            },
            set (val) {
                this.selectGroupId = val
            }
        },
        centerId: {
          get () {
            if (this.selectCenterId && this.selectCenterId !== '') {
              return this.selectCenterId
            }
            // 判断 sessionStorage 中是否存在 selectedCenterId，如果 'selectedCenterId' + this.currentUserId 对应的值是存在的，那么就将它返回出
            const selectedCenterId = sessionStorage.getItem('selectedCenterId' + this.currentUserId)
            if (selectedCenterId) {
              return selectedCenterId
            }
          },
          set (val) {
            this.selectCenterId = val
          }
        }
    },
    created () {
        // 添加 Unit Planner 设置的埋点
        this.$analytics.sendEvent('web_weekly_plan_edit_lesson_adapt_pop')
        // 监听事件
        this.$bus.$on('updateSelectedGroupId', () => {
            this.groupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
        })
      // 监听事件
      this.$bus.$on('updateSelectedCenterId', () => {
        this.centerId = sessionStorage.getItem('selectedCenterId' + this.currentUserId)
      })
    },
    methods: {
        /**
         * 初始化默认的学校和班级
         */
        initDefaultCenterOrGroup () {
            this.getCenterLoading = true
            // 调用接口
            return new Promise((resolve, reject) => {
                // 如果不是 Curriculum Plugin 平台，则不需要获取学校和班级信息
                if (!this.isCurriculumPlugin) {
                    resolve()
                }
                this.$axios.get($api.urls().getOrCreateUserDefaultCenter).then((res) => {
                    // 班级列表
                    const groups = res.groups
                    // 获取学校 ID
                    const centerId = res.id
                    // 停止 Loading
                    this.getCenterLoading = false
                    // 默认选择第一个班级
                    if (groups && groups.length > 0) {
                        this.currentGroupId = groups[0].id
                        this.groups = groups
                    }
                    // 获取班级中小孩信息
                    if (groups[0].enrollments && groups[0].enrollments.length > 0) {
                        this.defaultChildId = groups[0].enrollments[0].id
                    }
                    // 默认选择第一个学校
                    this.currentCenterId = centerId
                    // 处理获取的数据
                    this.processChildInfo(res.childInfo)
                    resolve()
                }).catch(error => {
                    // 停止 Loading
                    this.getCenterLoading = false
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },
        /**
         * 处理小孩信息
         * @param childInfo 小孩信息
         */
        processChildInfo(childInfo) {
            // 如果小孩信息不为空，则进行处理
            if (!childInfo) {
                return
            }
            function transformToPersonalizedForm(studentAttrEntities) {
                // 定义映射关系
                const attrNameMapping = {
                    'Language': 'language',
                    'Race': 'race',
                    'IEP/IFSP': 'iep',
                    'Place of Origin': 'region',
                    'Special education eligibility': 'characteristics'
                };

                // 定义 personalizedForm 对象
                const personalizedForm = {
                    language: [],
                    race: [],
                    iep: false,
                    region: [],
                    characteristics: []
                };

                // 遍历 studentAttrEntities 数组
                studentAttrEntities.forEach(entity => {
                    const key = attrNameMapping[entity.attrName];
                    if (key) {
                        if (key === 'iep') {
                            personalizedForm[key] = entity.attrValue.toLowerCase() === 'true';
                        } else {
                            personalizedForm[key].push(entity.attrValue);
                        }
                    }
                });

                return personalizedForm;
            }
            // 解析 string 为对象
            const childInfoObj = JSON.parse(childInfo)
            // childInfoObj 为一个数组
            this.personalizedForm = transformToPersonalizedForm(childInfoObj)
        },
        /**
         * 初始化个性化表单
         * @returns {{}}
         */
        initPersonalizeForm() {
            this.$nextTick(() => {
                // 更新一下状态
                this.$forceUpdate();
            })
            return this.personalizedForm;
        },
        /**
         * 个性化表单验证
         * @param validated
         */
        personalizeValidate(validated) {
            this.validated = validated
        },
        /**
         * 更新个性化表单
         * @param personalizedForm
         */
        personalizeUpdate(personalizedForm) {
            this.personalizedForm = personalizedForm
        },
        /**
         * 处理其他平台的逻辑
         * @returns {Promise<void>}
         */
        async processOtherPlatform () {
            if (!this.isCurriculumPlugin) {
                return
            }
            // 如果是插件平台，这里需要进行特殊处理
            await this.initEnrollmentMetadata()
        },
        // 初始化对应的小孩信息
        async initEnrollmentMetadata() {
            // 转换 personalizedForm 到 StudentAttrEntity 的方法
            function transformToStudentAttrEntities(personalizedForm, enrollmentId) {
                // 定义映射关系
                const attrNameMapping = {
                    language: 'Language',
                    race: 'Race',
                    iep: 'IEP/IFSP',
                    region: 'Place of Origin',
                    characteristics: 'Special education eligibility'
                }

                // 定义 StudentAttrEntity 数组
                const studentAttrEntities = []

                // 遍历 personalizedForm
                for (const key in personalizedForm) {
                    // 如果 personalizedForm 有这个属性
                    if (personalizedForm.hasOwnProperty(key)) {
                        const attrName = attrNameMapping[key] // 根据映射找到 attrName
                        const attrValues = personalizedForm[key] // 获取值
                        // 如果是数组
                        if (Array.isArray(attrValues)) {
                            // 多值情况
                            attrValues.forEach((value) => {
                                studentAttrEntities.push(createStudentAttrEntity(attrName, value, enrollmentId))
                            })
                        } else {
                            // 单值情况
                            const attrValue = attrValues ? attrValues.toString() : 'false' // 处理布尔值和空值
                            studentAttrEntities.push(createStudentAttrEntity(attrName, attrValue, enrollmentId))
                        }
                    }
                }

                return studentAttrEntities
            }

            // 创建单个 StudentAttrEntity 对象
            function createStudentAttrEntity(attrName, attrValue, enrollmentId) {
                return {
                    attrName: attrName,
                    attrValue: attrValue,
                    enrollmentId: enrollmentId
                }
            }

            // 如果 this.personalizedForm 中的 iep 为 false，则清空 characteristics
            if (this.personalizedForm.iep === false) {
                this.personalizedForm.characteristics = []
            }
            // 定义请求参数
            const checkAttrRequest = {
                groupId: this.currentGroupId,
                centerId: this.currentCenterId,
                studentAttrs: transformToStudentAttrEntities(this.personalizedForm, this.currentGroupId)
            }
            try {
                await this.$axios.post($api.urls().createChildrenForGroup, checkAttrRequest)
            } catch (e) {
                this.adaptedLoading = false
            }
        },
        // 隐藏班级信息确认弹窗
        async hideGroupInfoConfirmTip () {
            // 如果 notAskAgain 为 true，那么就调用隐藏班级信息确认弹窗的接口
            if (this.notAskAgain) {
                // 点击不再提示埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_adapt_set_pop_donot')
                await LessonApi.hideGroupInfoConfirm(this.groupId)
            }
        },
        isOpenAddChildrenTipModal () {
            // 如果是应用来的周计划，在关闭弹窗时判断是否已经弹出过添加小孩提示
            if (this.$route.params.apply && !this.isConfirm) {
                // 如果为 true，则说明已经弹出过，不需要二次弹出, 否则在关闭弹窗时给出提示
                if (!this.$store.state.lesson.isOpenAddChildrenTip) {
                    this.$emit('callbackAddChildrenTip')
                }
            }
        },
        // 从 PersonalizeChildList 中获取想要得到的数据
        getAdditionInfo (additionInfo) {
            // 如果 loading 为 false，就设置 personalizeChildListDataLoading 为 false
            if (additionInfo) {
                this.personalizeChildListDataLoading = additionInfo.loading
            }
            // 将 additionInfo 的值赋值给 children
            this.additionInfo = additionInfo
            // 从 additionInfo 中获取 iepButEligibilityIsEmptyChildren
            const iepButEligibilityIsEmptyChildren = additionInfo.iepButEligibilityIsEmptyChildren
            // 如果 iepButEligibilityIsEmptyChildren 存在，并且 length >= 1,那么就展示提示框
            if (iepButEligibilityIsEmptyChildren && iepButEligibilityIsEmptyChildren.length >= 1) {
                this.showIEPButEligibilityIsEmptyChildren = true
            } else {
                this.showIEPButEligibilityIsEmptyChildren = false
            }
            // 如果 childCount 为 0
            if (additionInfo && additionInfo.childCount === 0) {
                // 如果 childCount 为 0，那么就展示 PersonalizeChildList 组件
                this.emptyPageShow = true
            } else {
                this.emptyPageShow = false
            }
        },
        // 关闭 Personalize Your Lesson Plan 弹窗
        handleClose () {
            this.personalizePlanShow = false
        },
        // 初始化 showMixedAge
        async initShowMixedAge() {
            // 如果 groupId 不存在，那么就直接返回
            if (!this.groupId || this.groupId === '') {
                return
            }
            // 获取班级 Adapt 的设置信息 // 请求参数
            let request = {
                groupId: this.groupId,
                lessonIds: this.lessonIds,
                planIds: [this.planId],
                planItemIds: this.itemIds
            }
            // 将 showMixedAge 转化为布尔值
            const res = await this.$axios.post($api.urls().checkCanContinueAdapt, request)
            this.checkCanContinueAdapt = !!res.success
        },
        // 展示 Personalize Your Lesson Plan 弹窗
        showPersonalizePlan () {
            this.groupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
            // 设置初始化为 false
            this.initialized = false
            LessonApi.getGroupAdaptSetting(this.groupId).then(res => {
                this.notAskAgain = res.showConfirmTip
                this.showGroupTeamDescription = res.teacherGroupEnabled
                // 如果不需要在展示了，那么这里就不在打开页面了
                if (this.notAskAgain) {
                    this.listChildren(this.notAskAgain).then(res => {
                        // 由于不需要再打开 Personalize Your Lesson Plan 弹窗了，所以这里就直接开始生成
                        this.updateGenerateUniversalDesignAndCLRData()
                    })
                } else {
                    // 否则，打开当前弹窗
                    this.personalizePlanShow = true
                    this.$emit('openPersonalizePlan')
                    this.listChildren(this.notAskAgain).then(() => {
                        this.teacherGroups = []
                        // this.personalizeChildListDataLoading = false
                        // 打开弹窗判断是否有分组信息
                        this.$axios.get($api.urls().hasGroupTeams + '?groupId=' + this.groupId).then(res => {
                            // 如果 res 是存在的，那么就将 res 的值赋值给 hasGroupTeams
                            this.hasGroupTeams = res.success
                            // 如果启用了老师分组并且有分组信息则，获取分组信息
                            if (this.showGroupTeamDescription && this.hasGroupTeams) {
                                this.getGroupTerms()
                            } else {
                              // 如果没有启用老师分组或者没有分组信息则，这里也需要进行一次初始化数据
                              this.getGroupTerms(false, true)
                            }
                        })
                        this.$forceUpdate()
                    })
                }
            })
        },
        // 发送更新 UDL 和 CLR 数据的事件
        async updateGenerateUniversalDesignAndCLRData () {
            this.isConfirm = true
            // 设置 adaptedLoading 为 true
            this.adaptedLoading = true
            // 隐藏班级信息确认弹窗
            await this.hideGroupInfoConfirmTip()
            // 如果是插件平台，这里需要进行特殊处理
            await this.processOtherPlatform()
            await this.initShowMixedAge()
            // 判断 ad ditionInfo 是否为空，如果 additionInfo 不为空，获取对应的小孩总数，判断 childCount 是否和 noIEPChild 的 length 是否相等 并且和 noELDChild 的 length 是否相等，如果相等说明所有的小孩都没有 IEP 和 ELD，那么此时需要弹窗提示
            if (!this.checkCanContinueAdapt) {
                // 此时 loading 效果消失
                this.adaptedLoading = false
                // 如果不满足条件，那么就弹窗提示
                let title = this.$t('loc.noIEPOrELDNote')
                let text = this.isCurriculumPlugin ? this.$t('loc.unitPlannerPersonalizePlanErrorTitle2') : this.$t('loc.unitPlannerPersonalizePlanErrorTitle')

                this.$confirm(text, title, {
                    confirmButtonText: this.$t('loc.noIEDOrELDClose'),
                    cancelButtonText: this.$t('loc.noIEDOrELDClose'),
                    customClass: 'width-600',
                    showCancelButton: false
                }).then(() => {
                }).catch(() => {
                })
                return
            }
            // 如果直接点击，那么此时调用获取分组信息，目的是为了创建一个默认分组
            this.getGroupTerms()
            // 将 showGroupTeamDescription 的值存储到 sessionStorage 中
            sessionStorage.setItem('enableGroupTeams' + this.groupId, JSON.stringify(this.showGroupTeamDescription))
            this.$analytics.sendEvent('cg_unit_adapt_lesson_adaptstart')
            // 存储完成之后发送事件去更新数据
            this.$emit('updateGenerateUniversalDesignAndCLRData', true)
            // 此时 loading 效果消失
            this.adaptedLoading = false
            // 关闭 Personalize Your Lesson Plan 弹窗
            this.handleClose()
        },
        //  关闭 Assign Teacher Groups 弹窗的回调
        closeAssignTeacherGroups (saveClose) {
            // 如果是点击了保存，那么就开始更新数据，同时修改 isFirst
            if (saveClose) {
                localStorage.setItem(this.groupId + 'personalizeGroupIsFirst' + this.currentUserId, 'true')
                // 由于 Assign Teacher Groups 弹窗中的数据发生了改变，所以需要重新获取一下分组信息
                this.getGroupTerms(saveClose)
            } else {
                // 判断当前 this.teacherGroups.length 是否为 1，如果为 1，那么就修改 showGroupTeamDescription
                if (this.teacherGroups.length <= 1) {
                    this.showGroupTeamDescription = false
                    // 更新班级是否启用老师分组
                    LessonApi.setTeacherGroupEnabled(this.groupId, false)
                }
            }
        },
        // 获取分组信息
        getGroupTerms (saveClose, initialize) {
            // 如果班级 Id 存在并且 showGroupTeamDescription 为 true，那么此时获取分组信息
            if (this.groupId && this.showGroupTeamDescription) {
                // 获取班级的信息
                this.$axios.get($api.urls().getGroupTeams + '?groupId=' + this.groupId + '&enableGroupTeams=' + this.showGroupTeamDescription)
                    .then(res => this.processGetGroupTerms(res, saveClose, initialize))
                    .catch((error) => {
                        this.groupTeams = {}
                        this.hasTeacher = false
                        // 初始化成功
                        this.initialized = true
                    })
            } else if (initialize) {
                // 如果班级 Id 存在并且 showGroupTeamDescription 为 false，那么此时获取分组信息
                if (this.groupId) {
                    // 获取班级的信息
                    this.$axios.get($api.urls().getGroupTeams + '?groupId=' + this.groupId + '&enableGroupTeams=' + this.showGroupTeamDescription)
                        .then(res => this.processGetGroupTerms(res, saveClose, initialize))
                        .catch((error) => {
                            this.groupTeams = {}
                            this.hasTeacher = false
                            // 初始化成功
                            this.initialized = true
                        })
                }
            }
        },
        /**
         * 处理获取分组信息
         * @param res
         * @param saveClose
         * @param initialize 是否初始化,初始化的时候不需要弹窗
         */
        processGetGroupTerms (res, saveClose, initialize) {
            if (res) {
                this.hasTeacher = res.teacherList && res.teacherList.length > 0
                // 初始化成功
                this.initialized = true
                this.$nextTick(() => {
                    // 如果 res 是存在的，那么就将 res 的值赋值给 groupTeams
                    this.processGroupTermData(res)
                    // 如果 filterGroupData 为 1
                    if (!saveClose && this.teacherGroups && this.teacherGroups.length === 1 && !initialize) {
                        // 如果是 1，就弹出弹窗
                        // TODO 只有一个分组时是否需要弹出老师分组弹窗
                        this.showAssignTeacherGroups()
                    } else if (saveClose && this.teacherGroups && this.teacherGroups.length === 1 && !initialize) {
                        // 修改 showGroupTeamDescription
                        this.showGroupTeamDescription = false
                        // 更新班级是否启用老师分组
                        LessonApi.setTeacherGroupEnabled(this.groupId, false)
                    }
                })
            } else {
                this.hasTeacher = false
                // 初始化成功
                this.initialized = true
            }
        },
        // 展示 Assign Teacher Groups 弹窗
        showAssignTeacherGroups () {
            this.$refs.assignTeacherGroups.enableTecherGroups = this.showGroupTeamDescription
            this.$refs.assignTeacherGroups.showAssignTeacherGroups(this.groupId, '')
        },
        // 处理请求得到的 groupTerm 数据
        processGroupTermData (groupTerm) {
            // 处理 groupTerm 数据
            const groupData = groupTerm.teacherList.map(teacher => {
                return {
                    teacherId: teacher.id,
                    teacherName: teacher.display_name,
                    teacherAvatar: teacher.avatar_url,
                    iepChildren: [], // 这个老师所管理的所有的 iep 小孩数据
                    children: groupTerm.enrollmentTeamModelList
                        .filter(child => child.teacherId === teacher.id)
                        .map(child => {
                            return {
                                id: child.enrollmentId,
                                label: child.displayName,
                                avatarUrl: child.avatarUrl,
                                iep: child.iep,
                                eld: child.eld,
                                description: ''
                            }
                        }), // 这个老师所管理的所有的小孩数据
                    groupData: [{
                        id: '00000000-0000-0000-0000-000000000000', // 一开始只有一个分组，所以这里的 id 都是一样的
                        label: 'Group 1',
                        children: groupTerm.enrollmentTeamModelList
                            .filter(child => child.teacherId === teacher.id)
                            .map(child => {
                                return {
                                    id: child.enrollmentId,
                                    label: child.displayName,
                                    avatarUrl: child.avatarUrl,
                                    iep: child.iep,
                                    eld: child.eld,
                                    description: ''
                                }
                            }) // gpt 返回的小孩的数据
                    }]
                }
            })
            // 从 groupData 中去除那些没有小孩的老师
            const filterGroupData = groupData.filter(teacher => teacher.children.length > 0)
            // 将 res 的值赋值给 groupTeams
            this.$set(this, 'teacherGroups', filterGroupData)
            // 获取 groupData 中的第一个老师的 Id 作为 currentTeacherId
            this.currentTeacherId = filterGroupData[0].teacherId
        },
        // 当 showGroupTeamDescription 发生改变的时候，获取分组信息
        changeShowGroupTeamDescription (val) {
            this.showGroupTeamDescription = val
            // 判断是否存在分组信息，如果不存在，那么就展示 Assign Teacher Groups 弹窗
            if (this.showGroupTeamDescription && !this.hasGroupTeams) {
                // 如果班级 Id 存在并且 showGroupTeamDescription 为 true，那么此时获取分组信息
                if (this.groupId) {
                    // 判断是否存在班级分组信息
                    this.$axios.get($api.urls().hasGroupTeams + '?groupId=' + this.groupId)
                        .then((res) => {
                            if (res) {
                                this.$nextTick(() => {
                                    // 如果 res 是存在的，那么就将 res 的值赋值给 hasGroupTeams
                                    this.hasGroupTeams = res.success
                                    // 如果 res.success 为 true，那么就展示 Assign Teacher Groups 弹窗
                                    if (!res.success) {
                                        this.showAssignTeacherGroups()
                                    } else {
                                        // 如果 res.success 为 false，那么就获取分组信息
                                        this.getGroupTerms()
                                    }
                                })
                            }
                        })
                        .catch((error) => {
                            this.hasGroupTeams = false
                            // 出错了，当做没有分组处理
                            this.showAssignTeacherGroups()
                        })
                }
            } else {
                // 如果不需要展示的情况下，这个时候就需要获取一下分组信息
                this.getGroupTerms()
            }
            // 判断 showGroupTeamDescription 是否为 true
            if (this.showGroupTeamDescription) {
                // 添加 Unit Planner 设置的埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_adapt_g_open')
            } else {
                // 添加 Unit Planner 设置的埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_adapt_g_close')
            }
            LessonApi.setTeacherGroupEnabled(this.groupId, this.showGroupTeamDescription)
        },
        // 获取小孩列表
        listChildren (noAskAgain) {
            // 如果是 curriculum plugin 则不需要获取小孩列表
            if (this.isCurriculumPlugin) {
                this.$analytics.sendEvent('cg_unit_adapt_lesson_adapt')
                return this.initDefaultCenterOrGroup();
            }
            // 设置 PersonalizeChildList 组件的数据正在加载
            this.personalizeChildListDataLoading = true
            // 参数
            let params = {
                pageNum: 1,
                pageSize: 1000,
                sort: 'lastName',
                order: 'asc',
                groupId: this.groupId
            }
            // 调用接口
            return new Promise((resolve, reject) => {
                this.$axios.get($api.urls().manageChildren, { params: params }).then(res => {
                    this.personalizeChildListDataLoading = false
                    if (!noAskAgain) {
                        this.$nextTick(() => {
                            this.$refs.personalizeChildList.children = res.results // 小孩列表
                            this.$refs.personalizeChildList.childCount = res.total // 小孩总数
                            // 从 res.results 中获取小孩的信息
                            if (res.results && res.results.length > 0) {
                                this.$refs.personalizeChildList.groupName = res.results[0].groupName // 班级名称
                            } else {
                                this.$refs.personalizeChildList.groupName = this.$refs.personalizeChildList.currentGroupName // 班级名称
                            }
                            // 判断是否是空页面，如果小孩人数为空或者小孩列表为空
                            this.$refs.personalizeChildList.emptyPageShow = res.total === 0 || (res.results && res.results.length === 0)
                        })
                    }
                    // 获取非 IEP 小孩
                    const noIEPChildren = this.getNoIEPChildren(res.results)
                    // 获取非 ELD 的小孩
                    const noELDChildren = this.getNoELDChildren(res.results)
                    // 获取是 IEP 的小孩 但是这个小孩的 Special education eligibility 是空的小孩
                    const iepButEligibilityIsEmptyChildren = this.getIEPChildButEligibilityIsEmpty(res.results)
                    // 封装为对象发送出去
                    this.additionInfo = {
                        noIEPChildren: noIEPChildren,
                        noELDChildren: noELDChildren,
                        iepButEligibilityIsEmptyChildren: iepButEligibilityIsEmptyChildren,
                        childCount: res.total,
                        loading: false
                    }
                    this.getAdditionInfo(this.additionInfo)
                    resolve()
                }).catch(err => {
                    reject(err)
                })
            })
        },
        // 获取是 IEP 的小孩 但是这个小孩的 Special education eligibility 是空的小孩
        getIEPChildButEligibilityIsEmpty (children) {
            // 校验小孩，将是 IEP 的小孩获取得到
            let iepButEligibilityIsEmptyChildren = []
            children.forEach(child => {
                // 是否是 IEP
                let isIEP = false
                // 获取属性
                const attrIEP = child.showIep
                // 判断 attrIEP 是否是 true
                if (attrIEP) {
                    isIEP = true
                }
                // 是 IEP 的小孩
                if (isIEP) {
                    // 获取属性
                    const attrIEPGoal = this.getAttrValue(child, 'Special education eligibility')
                    // 判断 attrIEPGoal 是否是空
                    if (this.isEmptyOrBlank(attrIEPGoal)) {
                        iepButEligibilityIsEmptyChildren.push(child)
                    }
                }
            })
            return iepButEligibilityIsEmptyChildren
        },
        // 获取非 ELD 的小孩
        getNoELDChildren (children) {
            // 校验小孩，将不是 ELD 的小孩获取得到
            let noELDChildren = []
            children.forEach(child => {
                // 是否是 ELD
                let isELD = false
                // 获取属性
                const attrELD = child.eldDB
                // 判断 attrELD 是否是 true
                if (attrELD) {
                    isELD = true
                }
                // 不是 ELD 的小孩
                if (!isELD) {
                    noELDChildren.push(child)
                }
            })
            return noELDChildren
        },
        // 获取非 IEP 小孩
        getNoIEPChildren (children) {
            // 校验小孩，将不是 IEP 的小孩获取得到
            let noIEPChildren = []
            children.forEach(child => {
                // 是否是 IEP
                let isIEP = false
                // 获取属性
                const attrIEP = child.showIep
                // 判断 attrIEP 是否是 true
                if (attrIEP) {
                    isIEP = true
                }
                // 不是 IEP 的小孩
                if (!isIEP) {
                    noIEPChildren.push(child)
                }
            })
            return noIEPChildren
        },
        // 判断属性是否是 true
        isEmptyOrBlank (s) {
            return !s || s.trim() === ''
        }
    }
}
</script>

<style lang="less" scoped>
.child-list {
    max-height: calc(100vh - 330px);
    height: calc(100vh - 350px);
}

.child-list-open {
  height: calc(100vh - 392px);
}

.empty-child-list {
    height: calc(100vh - 450px);
}

.assign-teacher-dialog {
    display: flex;
    width: 880px;
    padding: 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 8px;
    background: #FFF;

    /* 卡片投影 */
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);

    .dialog-title {
        display: flex;
        align-items: flex-start;
        align-self: center;

        .dialog-title-content {
            flex: 1 0 0;
            color: var(--111-c-1-c, #111C1C);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Semi Bold/20px */
            font-family: Inter;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 26px; /* 130% */
        }

        .dialog-close {
            cursor: pointer;
            width: 20px;
            height: 20px;
        }
    }

    .dialog-body {
        display: flex;
        margin-top: -24px;
        margin-bottom: -24px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        overflow: auto;
        min-height: 350px;

        .teacher-base-group {
            display: flex;
            width: 100%;
            padding-left: 0px;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: 10px;
        }

        .teacher-switch {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
        }

        .teacher-info {
            display: flex;
            width: 100%;
            align-items: center;
            gap: 10px;
            align-self: flex-start;
            background-color: #F5F6F8;

            .cursor-pointer {
                cursor: pointer;
                display: flex;
                width: 24px;
                height: 24px;
                padding: 3.5px;
                justify-content: center;
                align-items: center;
            }

            .group-child {
                display: flex;
                background-color: #F5F6F8;
                height: 32px;
                padding: 4px 8px;
                align-items: center;
                gap: 8px;
                border-radius: 90px;
                cursor: pointer;
            }

            .selected-teacher {
                color: var(--color-white);
                background: var(--color-primary);
            }
        }

        .body-text {
            color: var(--111-c-1-c, #111C1C);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Regular/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }

        .teacher-groups {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
            align-self: stretch;
        }

        .teacher-groups-tag {
            display: flex;
            height: 340px;
            align-items: flex-start;
            justify-content: space-between;
            gap: 10px;
            flex: 1 0 0;
            align-self: stretch;
            background: #FFF;
            // 保证元素的空间不会被挤压
            & > div {
                flex-grow: 1
            }

            .teacher-groups-tag-title {
                display: flex;
                height: 52px;
                padding: 4px 12px;
                align-items: center;
                gap: 8px;
                justify-content: space-between;
                align-self: center;
                border-top: 1px solid var(--dcdfe-6, #DCDFE6);
                border-right: 1px solid var(--dcdfe-6, #DCDFE6);
                border-left: 1px solid var(--dcdfe-6, #DCDFE6);
                background: var(--f-5-f-6-f-8, #F5F6F8);
            }

            .teacher-groups-tag-content {
                display: flex;
                padding: 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                flex: 1 0 0;
                align-self: stretch;
                border-right: 1px solid var(--dcdfe-6, #DCDFE6);
                border-bottom: 1px solid var(--dcdfe-6, #DCDFE6);
                border-left: 1px solid var(--dcdfe-6, #DCDFE6);
                background: #FFF;

                .teacher-groups-tag-content-title {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    align-self: stretch;

                    .select-child-btn {
                        display: flex;
                        height: 32px;
                        padding: 8px 12px;
                        justify-content: center;
                        align-items: center;
                        gap: 8px;
                        border-radius: 4px;
                        border: 2px solid var(--10-b-3-b-7, #10B3B7);
                        background: var(--ffffff, #FFF);
                        cursor: pointer;
                    }
                }

                .teacher-groups-children {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 10px;
                }

                .enrollment-tag {
                    display: flex;
                    height: 32px;
                    padding: 4px 8px;
                    align-items: center;
                    justify-content: flex-start;
                    gap: 8px;
                }
            }
        }

        .teacher-groups-size {
            display: flex;
            align-items: center;
            gap: 16px;
            align-self: stretch;

            .min-max-group-size {
                display: flex;
                align-items: center;
                gap: 4px;

                .min-group-size, .max-group-size {
                    /deep/ .el-input__inner {
                        display: flex;
                        width: 70px;
                        height: 40px;
                        padding: 9px 12px;
                        align-items: center;
                        gap: 4px;
                        border-radius: 4px;
                        border: 2px solid var(--dcdfe-6, #DCDFE6);
                        background: var(--ffffff, #FFF);
                    }
                }

                .divide-line-gray {
                    color: var(--676879, #676879);
                    text-align: center;
                    font-feature-settings: 'clig' off, 'liga' off;
                    height: 1px;
                    width: 14px;
                    /* Regular/14px */
                    font-family: Inter;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                }
            }
        }
    }
}

.empty-status {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

/deep/ .width-full {
    width: 100% !important;

    & {
        width: 100% !important;
    }
}

.height-22 {
    height: 22px;
}

/deep/ .el-image {
    border-radius: 4px !important;
}

/deep/ .el-form-item {
    margin-bottom: 0;
}

@media only screen and (max-width: 1199px) {
    //ipad
    .ipad_width {
        display: flex;
        justify-content: flex-start;
        max-width: 15px;
    }
}

.child-area {
    padding: 5px 10px;
    min-height: 60px;
    max-height: 300px;
    overflow-y: auto;
}

.child-area {
    padding: 0 !important;
    margin-bottom: 12px;
}

.search-child-area {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 2;
    padding: 10px 10px 5px 10px;
}

.child-list {
    overflow-y: auto;
}

/deep/ .el-tag--info {
    color: #323338;
}

.child-check-row {
    .child-avatar {
        max-width: 32px;
        border-radius: 50%;
    }

    /deep/ .el-checkbox {
        margin-bottom: 0 !important;
    }

    /deep/ .el-checkbox__label {
        width: calc(100% - 15px);
    }

    padding: 10px 10px 0 10px;
}

.search-lesson-content {
    min-height: 45px;
    display: flex;
    align-items: center;
}

/deep/ .el-scrollbar .is-horizontal {
    display: none;
}

/deep/ .el-scrollbar__wrap {
    overflow-x: hidden;
}

/deep/ .el-icon-error:before {
    font-family: 'lg-icon';
    content: "\e6a7";
}

/deep/ .el-alert__title {
    font-size: 16px;
}

/deep/ .el-table .cell {
    -webkit-line-clamp: 2; /* 显示两行 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap !important;
    word-break: keep-all !important;
}
</style>
<style lang="less">
.select-child-popover {
    padding: 4px 0px !important;

    .enrollment-tag {
        display: flex;
        height: 32px;
        padding: 4px 8px;
        align-items: center;
        justify-content: flex-start;
        gap: 4px;
    }
}

.personalize-plan {
    /deep/ .el-message-box__content {
        width: 600px;
    }
}

.select-teacher-popover {
    padding: 0;

    .teacher-select {
        display: flex;
        padding: 0px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 4px;
        background: var(--color-white);
        cursor: pointer;

        .teacher-groups-tag-title {
            width: 100%;
            padding: 8px;
        }
    }

    .teacher-select :hover {
        background: var(--color-primary-light);
    }
}
</style>
