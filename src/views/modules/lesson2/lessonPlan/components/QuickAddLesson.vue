<template>
  <el-dialog :title="$t('loc.addLessonPlan')" :close-on-click-modal="false" :visible.sync="dialogVisible" @close="cancel" class="ipad_width">
    <div style="overflow:auto" class="scrollbar">
      <el-form ref="LessonForm" class="add-margin-r-5" :model="lesson" label-position="top" :rules="rules">
        <!--课程名称-->
        <el-form-item id="name" :label="$t('loc.lesson2NewLessonFormLabelLessonName')" prop="name" class="lesson-form-name">
          <el-input v-model="lesson.name" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderLessonName')"
                    auto-complete="off" type="text" maxlength="200" />
        </el-form-item>
        <!--年龄组-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelAgeGroup')" prop="ages" class="lesson-form-age">
          <el-select v-model="ageValues" multiple :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                      class="input-select">
            <el-option v-for="item in ageGroup" :key="item.value" :label="item.name" :value="item.value"/>
          </el-select>
        </el-form-item>
        <!--框架-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelFramework')" prop="framework"
                      class="lesson-form-framework ">
          <el-select v-model="frameworkId" class="input-select"
                      :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')">
            <el-option v-for="item in frameworks" :key="item.frameworkId" :label="item.frameworkName" :value="item.frameworkId"/>
          </el-select>
        </el-form-item>
        <!--领域、测评点-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelMeasures')" prop="measures" class="lesson-form-domain"
                      v-if="lesson.framework">
          <lesson-measure-select v-model="lesson.measures" :domains="domains" :loading="loadingDomains" class="input-select"/>
        </el-form-item>
        <lesson-measure-label v-model="lesson.measures" style="margin-bottom: 31px;margin-top: 10px;"/>
        <!--课程目标-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelObjectives')" prop="objective" class="lesson-form-objective">
          <el-input v-model="lesson.objective" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderObjectives')"
                    :autosize="{ minRows: 4}" resize="none" type="textarea" :maxlength="10000"/>
        </el-form-item>
        <!--课程材料-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelMaterials')" prop="material" class="lesson-form-material">
          <lesson-material-input style="gap:none" :simple="true" v-model="lesson.material"/>
        </el-form-item>
        <!--课程步骤-->
        <el-form-item v-for="(step,index) in lesson.steps" :prop="`steps.${index}`" :key="step.ageGroupName" :id="'steps.'+index +'.content'"
                      :label="`${$t('loc.lesson2NewLessonFormPlaceHolderSteps')} ${step.ageGroupName}`" :rules="stepRules">
          <lesson-step-input :simple="true" :noMarginLeft="true" v-model="lesson.steps[index]"/>
        </el-form-item>
        <!--与家长分享-->
        <el-form-item class="lesson-share">
          <div slot="label">
            <img src="@/assets/img/lesson2/lessons2-lesson-resources2.png"
                 class="lessons2-lesson-resources"  :alt="$t('loc.lessons2LessonResources')"/>
            <div class="flex-center-center lessons2-lesson-resources-info">
              <span>{{ $t('loc.lessons2LessonResources') }}</span>
            </div>
          </div>
          <div class="hidden-lg-and-up" style="display: flex;justify-content: space-between">
            <el-form-item :label="$t('loc.lessons2LessonBook')" class="lesson-form-book">
              <book-select ref="bookSearcher" :simple="true" v-model="lesson.book"/>
            </el-form-item>
            <el-form-item :label="$t('loc.lessons2LessonVideo')" class="lesson-form-video">
              <video-book-select :simple="true" v-model="lesson.videoBook" :book="lesson.book"/>
            </el-form-item>
          </div>
          <div class="hidden-md-and-down" style="display: flex;justify-content: flex-start">
            <el-form-item :label="$t('loc.lessons2LessonBook')" class="lesson-form-book">
              <book-select ref="bookSearcher" :simple="true" v-model="lesson.book"/>
            </el-form-item>
            <el-form-item :label="$t('loc.lessons2LessonVideo')" style="margin-left: 35px;" class="lesson-form-video">
              <video-book-select :simple="true" v-model="lesson.videoBook" :book="lesson.book"/>
            </el-form-item>
          </div>
          <el-form-item :label="$t('loc.lessons2LessonAttachments')" class="lesson-form-attachment">
            <attachment-uploader ref="attachmentUploader" v-model="lesson.attachments"/>
          </el-form-item>
        </el-form-item>
        <div id="bottom"></div>
      </el-form>
    </div>
    <div slot="footer">
      <!-- 分享到机构课程复选框 -->
      <el-checkbox class="pull-left" v-if="showRecommendCheckbox" v-model="recommendToAgency">{{ $t('loc.saveToAgencyWideLesson') }}</el-checkbox>
      <!-- 课程操作（发布 / 取消） -->
        <el-button @click="cancel">{{$t('loc.cancel')}}</el-button>
        <el-button @click="save" type="primary" :loading="publishing">{{$t('loc.save')}}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Lesson2 from '@/api/lessons2'
import { mapState } from 'vuex'
import LessonMeasureSelect from '@/views/modules/lesson2/lessonLibrary/editor/LessonMeasureSelect'
import LessonMeasureLabel from '@/views/modules/lesson2/lessonLibrary/editor/LessonMeasureLabel'
import LessonMaterialInput from '@/views/modules/lesson2/lessonLibrary/editor/lessonMaterialInput/index'
import LessonStepInput from '@/views/modules/lesson2/lessonLibrary/editor/LessonStepInput'
import BookSelect from '@/views/modules/lesson2/component/BookSelect.vue'
import VideoBookSelect from '@/views/modules/lesson2/component/VideoBookSelect.vue'
import AttachmentUploader from '@/views/modules/lesson2/component/attachmentUploader'
import { getRecommendToAgencyKey } from '@/utils/common'
export default {
  name: 'QuickAddLesson',
  components: {
    LessonMeasureSelect,
    LessonMeasureLabel,
    LessonMaterialInput,
    LessonStepInput,
    BookSelect,
    VideoBookSelect,
    AttachmentUploader
  },
  data () {
    return {
      lesson: { // 课程信息
        id: null, // 课程 ID
        cover: null, // 课程封面，对象，id、name、url
        name: null, // 课程名称
        ages: [], // 年龄组，对象数组，name、value
        prepareTime: null, // 准备时长
        activityTime: null, // 活动时长
        themes: [], // 课程主题，对象数组，id、name
        framework: null, // 框架，对象，frameworkId、frameworkName
        measures: [], // 领域和测评点，对象数组，id、name、abbreviation、children
        objective: null, // 课程目标
        material: null, // 课程材料，对象，media、description
        steps: [], // 课程步骤，对象数组，content、media、ageGroup
        book: null, // 书，对象
        videoBook: null, // 视频书，对象
        attachments: [] // 附件，对象数组
      },
      // 验证规则
      rules: {
        name: [ { message: this.$t('loc.fieldReq'), required: true, whitespace: true } ],
        ages: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        themes: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        framework: [{ type: 'object', message: this.$t('loc.fieldReq'), required: true }],
        measures: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        objective: [{ message: this.$t('loc.fieldReq'), required: true, whitespace: true }],
        material: [{
          type: 'object',
message: this.$t('loc.fieldReq'),
required: true,
          fields: {
            description: { message: this.$t('loc.fieldReq'), required: true, whitespace: true }
          }
        }]
      },
      stepRules: {
        type: 'object',
required: true,
        fields: {
          content: { message: this.$t('loc.fieldReq'), required: true, transform: value => this.innerText(value) }
        }
      },
      dialogVisible: false, // 弹窗是否可见
      ageValues: [], // 当前选择的年龄组值，字符串数组
      ageGroup: null, // 当前登录人地区年龄组
      frameworkId: null, // 当前选择的框架 ID
      frameworks: null, // 可选框架
      domains: null, // 可选领域和测评点
      publishing: false,
      recommendToAgency: false, // 推荐到机构课程库复选框值
      showRecommendCheckbox: false // 是否显示推荐课程的复选框
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open
    }),
    /**
     * 是否开启了推荐到机构课程开关
     */
    recommendToAgencyOpen () {
      return this.open && this.open.recommendLessonToAgencyOpen && !this.open.educator
    }
  },
  created () {
    this.loadAgeGroup()
    this.loadFrameworks()
  },
  methods: {
    /**
     * 是否是管理员
     */
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    /**
     * 打开添加课程弹窗
     */
    openQuickAddLesson (name) {
      // 将搜索的名称带入到新加的课程
      if (name) {
        this.lesson.name = name
      }
      // 只有老师角色并且开通了推荐到机构课程库的开关才显示
      this.showRecommendCheckbox = !this.isAdmin() && this.recommendToAgencyOpen
      if (this.showRecommendCheckbox) {
        var defaultValue = localStorage.getItem(getRecommendToAgencyKey())
        this.recommendToAgency = defaultValue !== 'false'
      }
      this.dialogVisible = true
    },
    // 加载年龄段
    async loadAgeGroup () {
      let response = await Lesson2.getAgeGroups(this.currentUser.default_agency_state)
      this.ageGroup = response.ageGroups
      return response.ageGroups
    },
    // 加载框架
    async loadFrameworks () {
      let response = await Lesson2.getFrameworks()
      this.frameworks = response.frameworks
      return response.frameworks
    },
    // 加载框架测评点
    async loadMeasures (framework) {
      this.loadingDomains = true
      let response = await Lesson2.getMeasures(framework.frameworkId)
      this.domains = response.measures
      this.loadingDomains = false
      return response.measures
    },
    // 收集提交表单数据
    collectData () {
      let measureIds = this.lesson.measures.reduce((prev, domain) => {
        let domains = [domain]
        let measures = []
        while (domains.length) {
          let temp = domains.pop()
          // 测评点无孩子节点
          if (!temp.children || !temp.children.length) {
            measures.push(temp)
            continue
          }
          // 有孩子节点的为领域
          domains.push(...temp.children)
        }
        return prev.concat(measures.map(m => m.id))
      }, [])
      let materials = { descriptions: [] }
      if (this.lesson.material) {
        this.lesson.material.media && (materials.mediaId = this.lesson.material.media.id)
        this.lesson.material.description && (materials.descriptions.push(this.lesson.material.description))
      }
      return {
        id: this.lesson.id,
        name: this.lesson.name,
        ages: this.lesson.ages.map(item => item.value),
        ageGroupNames: this.lesson.ages.map(item => item.name),
        themeIds: this.lesson.themes.map(item => item.id),
        prepareTime: this.lesson.prepareTime,
        activityTime: this.lesson.activityTime,
        frameworkId: this.lesson.framework && this.lesson.framework.frameworkId,
        measureIds: measureIds,
        objectives: this.lesson.objective && [this.lesson.objective] || [],
        materials: materials,
        steps: this.lesson.steps.map(step => ({
          ageGroupName: step.ageGroupName,
ageGroupValue: step.ageGroupValue,
          content: step.content,
mediaId: step.media && step.media.id
        })),
        coverMediaIds: this.lesson.cover && [this.lesson.cover.id] || [],
        books: this.lesson.book && [this.lesson.book] || [],
        videoBooks: this.lesson.videoBook && [this.lesson.videoBook] || [],
        attachmentMediaIds: this.lesson.attachments.map(media => media.id),
        quickAdd: true,
        recommendToAgency: this.recommendToAgency // 是否推荐到机构课程
      }
    },
    // 取消添加课程，关闭弹窗
    cancel () {
      // 关闭弹窗
      this.dialogVisible = false
      // 清空表单
      this.$refs['LessonForm'].resetFields()
      // 清空视频书和搜索关键字
      this.$refs.bookSearcher.keyword = ''
      this.lesson.book = null
      this.lesson.videoBook = null
      // 清空附件
      this.$refs.attachmentUploader.fileList = []
      this.lesson.attachments = []
      // 清空选择的年龄段
      this.ageValues = []
      // 清空选择的框架和测评点
      this.frameworkId = ''
      this.domains = null
    },
    // 保存新加课程
    save () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_add_les_save')
      this.$refs['LessonForm'].validate((valid, element) => {
        if (valid) {
          // 保存按钮loading
          this.publishing = false
          // 表单验证成功后生成课程
          let lessonData = this.collectData()
          Lesson2.publishLesson(lessonData)
          .then(res => {
            // 设置课程详情，带入到项目中
            this.publishing = false
            let LessonDetail = {}
            this.dialogVisible = false
            LessonDetail['id'] = res.lessonId
            LessonDetail['name'] = this.lesson.name
            let measures = []
            this.lesson.measures.forEach(domain => {
              domain.children.forEach(measure => {
                measures.push(measure.abbreviation)
              })
            })
            // 发布成功后，更新本地推荐到机构课程库的值
            if (this.showRecommendCheckbox) {
              localStorage.setItem(getRecommendToAgencyKey(), this.recommendToAgency)
            }
            // 如果勾选了推荐到机构课程库复选框，则提示用户成功发布课程并推荐到机构课程库
            if (!this.isAdmin() && this.recommendToAgency) {
              this.$message({
                message: this.$t('loc.lessonPublishAndRecommendAgency'),
                type: 'success'
              })
            } else {
              this.$message({
                message: this.$t('loc.lesson2Publish'),
                type: 'success'
              })
            }
            LessonDetail['authorId'] = this.currentUser.user_id
            LessonDetail['measure'] = measures
            // 传值设置项目课程为新加的课程
            this.$emit('callSelectLesson', LessonDetail)
          })
        } else {
          // 表单验证失败，滚动到错误位置
          setTimeout(() => {
            let formItemsWithError = this.$el.querySelectorAll('.is-error')
            let firstItem = formItemsWithError[0]
            if (firstItem) {
              firstItem.dispatchEvent(new CustomEvent('validate-failed', {}))
              firstItem.scrollIntoView(true)
            }
          }, 0)
          return false
        }
      })
    },
    innerText (html) {
      let htmlDivElement = document.createElement('div')
      htmlDivElement.innerHTML = html || ''
      return htmlDivElement.innerText.trim()
    }
  },
  watch: {
    frameworkId (val) {
      if (val) {
        let frameworks = this.frameworks || []
        let framework = frameworks.find(item => val === item.frameworkId)
        this.lesson.framework = framework
        this.lesson.measures = []
        this.loadMeasures(framework)
      }
    },
    ageValues (values) {
      values = values || []
      let ageGroup = this.ageGroup || []
      let ages = ageGroup.filter(item => values.includes(item.value))
      this.lesson.ages = ages
      this.lesson.steps = ages.map(age => {
        let value = this.lesson.steps.find(item => item.ageGroupName === age.name)
        return value || {
          ageGroupName: age.name,
          ageGroupValue: age.value
        }
      })
    },
    dialogVisible (val) {
      // 弹窗隐藏时滚动条置顶
      if (!val) {
        document.getElementById('name').scrollIntoView()
      }
    },
    // 监听附件数量，大于一个附件时，将滚动条拉倒底部
    'lesson.attachments' (val) {
      if (val.length > 1) {
        document.getElementById('bottom').scrollIntoView()
      }
    }
  }
}
</script>
<style lang="less" scoped>
@media only screen and (max-width:1199px){
  //ipad
  .ipad_width{
    margin-left: -245px;
    width: 150%;
  }

  /deep/ .el-dialog__body {
    padding: 0px 10px 0px 20px;
  }

  .new-lesson /deep/ {
    font-size: 14px;
    color: #333333;

    .el-form-item {
      margin-bottom: 31px;
      display: inline-block;
    }
  }

  .header {
    text-align: center;
    height: 50px;
    padding: 0 35px;
    line-height: 50px;
    color: #111C1C;
    background-color: #e4eaec;

    :first-child {
      float: left;
    }

    :last-child {
      font-size: 18px;
      color: #111C1C;
    }
  }

  .content {
    margin: 20px auto;
    background-color: #fff;
    width: 1200px;
    padding: 50px;
  }

  .input-select {
    width: 100%;
  }

  .el-form-item {

    &.lesson-form-framework {
      display: inline-block;
      width: 49%;
      margin-bottom: 0;
      margin-right: 1%;
    }
    &.lesson-form-domain {
      display: inline-block;
      width: 49%;
      margin-bottom: 0;
      margin-left: 1%;
    }

    &.lesson-form-book, &.lesson-form-video {
      // display: inline-block;
      width: 240px;
    }

    &.lesson-form-video {
      vertical-align: top;
    }

    &.lesson-form-attachment {
      width: 100%;
    }

    &.lesson-form-step {
      width: 100%;
    }

    &.lesson-form-step {
      margin-bottom: 0;

      .el-form-item {
        padding-left: 15px;
        width: 100%;
      }
    }
  }
  .lesson-share {
    position: relative;
    margin: 40px auto 0;
    border-radius: 4px;
    background: #D9F0CE;
    padding: 48px 34px 30px;

    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
    .lessons2-lesson-resources-info {
      width: 467px;
      position: relative;
      bottom: 39px;
      font-size: 24px;
      color: #FFFFFF;
      font-weight: normal;
    }

    & > :first-child {
      position: absolute;
      display: block;
      width: 467px;
      height: 56px;
      border-radius: 4px;
      margin: 0 auto;
      padding: 0;
      top: -29px;
      left: calc(50% - 233px);
      text-align: center;
      line-height: 58px;
      color: #ffffff;
      font-size: 16px;
    }
  }

  .new-lesson-operation {
    display: flex;
    justify-content: center;
    gap: 40px;
  }

  .el-form-item /deep/ .el-form-item__error {
    padding-left: 0;
    padding-top: 5px;
  }

  .el-button--primary.is-disabled,.el-button--primary.is-disabled:active,.el-button--primary.is-disabled:focus,.el-button--primary.is-disabled:hover {
    color: #fff !important;
    background-color: #88d9db !important;
    border-color: #88d9db !important;
  }

  /deep/ .el-form-item__label {
    padding: 0;
    font-weight: bold;
  }

  /deep/.el-form-item {
    margin-bottom: 10px;
  }
}

@media only screen and (min-width:1200px){
  //web
  /deep/ .el-dialog__body {
    padding: 0px 24px;
  }
  /deep/ .el-dialog__header .el-dialog__title {
    color: #111c1c;
    font-size: 20px;
    font-weight: 600;
  }

  .new-lesson /deep/ {
    font-size: 14px;
    color: #333333;

    .el-form-item {
      margin-bottom: 31px;
      display: inline-block;
    }
  }

  .header {
    text-align: center;
    height: 50px;
    padding: 0 35px;
    line-height: 50px;
    color: #111C1C;
    background-color: #e4eaec;

    :first-child {
      float: left;
    }

    :last-child {
      font-size: 18px;
      color: #111C1C;
    }
  }

  .content {
    margin: 20px auto;
    background-color: #fff;
    width: 1200px;
    padding: 50px;
  }

  .input-select {
    width: 100%;
  }

  .el-form-item {

    &.lesson-form-framework {
      display: inline-block;
      width: 49%;
      margin-bottom: 0;
      margin-right: 1%;
    }
    &.lesson-form-domain {
      display: inline-block;
      width: 49%;
      margin-bottom: 0;
      margin-left: 1%;
    }

    &.lesson-form-book, &.lesson-form-video {
      // display: inline-block;
      width: 240px;
    }

    &.lesson-form-video {
      vertical-align: top;
    }

    &.lesson-form-attachment {
      width: 100%;
    }

    &.lesson-form-step {
      width: 100%;
    }

    &.lesson-form-step {
      margin-bottom: 0;

      .el-form-item {
        padding-left: 15px;
        width: 100%;
      }
    }
  }
  .lesson-share {
    position: relative;
    margin: 40px auto 0;
    border-radius: 4px;
    background: #D9F0CE;
    padding: 48px 34px 30px;

    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
    .lessons2-lesson-resources-info {
      width: 467px;
      position: relative;
      bottom: 39px;
      font-size: 24px;
      color: #FFFFFF;
      font-weight: normal;
    }

    & > :first-child {
      position: absolute;
      display: block;
      width: 467px;
      height: 56px;
      border-radius: 4px;
      margin: 0 auto;
      padding: 0;
      top: -29px;
      left: calc(50% - 233px);
      text-align: center;
      line-height: 58px;
      color: #ffffff;
      font-size: 16px;
    }
  }

  .new-lesson-operation {
    display: flex;
    justify-content: center;
    gap: 40px;
  }

  .el-form-item /deep/ .el-form-item__error {
    padding-left: 0;
    padding-top: 5px;
  }

  .el-button--primary.is-disabled,.el-button--primary.is-disabled:active,.el-button--primary.is-disabled:focus,.el-button--primary.is-disabled:hover {
    color: #fff !important;
    background-color: #88d9db !important;
    border-color: #88d9db !important;
  }

  /deep/ .el-form-item__label {
    padding: 0;
    font-weight: bold;
  }

  /deep/.el-form-item {
    margin-bottom: 10px;
  }
  /deep/.el-textarea__inner::-webkit-input-placeholder {
    color: #676879;
  }
  /deep/.el-textarea__inner::-ms-input-placeholder {
    color: #676879;
  }
  /deep/.el-input__inner::-webkit-input-placeholder {
    color: #676879;
  }
  /deep/.el-input__inner::-ms-input-placeholder {
    color: #676879;
  }
}

// 移动端适配样式
@media screen and (max-width: 768px) {
  .lesson-modal {
    padding: 0;
  }

  /deep/ .el-dialog {
    width: 95% !important;
    margin: 10px auto !important;
  }

  /deep/ .el-dialog__body {
    padding: 10px 15px;
    max-height: calc(100vh - 180px);
    overflow-y: auto;
  }

  .el-form-item {
    width: 100% !important;
    margin-right: 0 !important;
    margin-left: 0 !important;

    &.lesson-form-framework,
    &.lesson-form-domain {
      width: 100% !important;
      margin: 0 0 20px 0 !important;
    }

    &.lesson-form-book,
    &.lesson-form-video {
      width: 100% !important;
      margin-bottom: 15px !important;
    }
  }

  .lesson-share {
    margin: 20px 0;
    padding: 30px 15px 15px;

    & > :first-child {
      width: 90%;
      left: 5%;
    }
  }

  // 输入框优化
  /deep/ .el-input__inner,
  /deep/ .el-textarea__inner {
    font-size: 16px;
  }

  // 触摸区域优化
  .el-button,
  .el-checkbox,
  .el-select {
    min-height: 44px;
  }

  /deep/ .el-select-dropdown__item {
    height: 44px;
    line-height: 44px;
  }

  // 底部按钮
  .dialog-footer {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .el-button {
      width: 100%;
      margin: 0;
    }
  }

  // 调整表单间距
  .el-form-item {
    margin-bottom: 20px;
  }

  // 文件上传区域优化
  .attachment-uploader {
    width: 100%;
  }
}
</style>
