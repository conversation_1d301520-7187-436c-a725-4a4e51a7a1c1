<template>
  <div>
    <div v-html="content" :id="htmlId" class="overflowHidden html-content ql-editor"></div>
    <div v-if="showMore" class="text-center lg-margin-top-8">
      <span class="text-primary lg-pointer v-middle" @click="toggleHtmlContentHeight">{{isExpand ? $t('loc.lessons2ShowLess')  : $t('loc.lessons2ShowMore')}}
      <i :class="isExpand ? 'lg-icon-arrow-up' : 'lg-icon-arrow-down'"
         class="lg-icon m-l-sm font-size-16 lg-color-text-secondary lg-border-radius-8 lg-padding-l-r-8 lg-padding-t-b-2 bg-gray"></i>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HtmlContentToggle',
  data () {
    return {
      showMore: false,
      isExpand: false,
      maxHeightValue: '200'
    }
  },
  props: {
    content: {
      type: String
    },
    htmlId: {
      type: String
    }
  },
  watch: {
    content: {
      handler: function (val) {
        this.$nextTick(() => {
          // 获取加载 html 的高度，如果高度大于设定的高度，则显示 Show more
          const htmlContent = document.getElementById(this.htmlId)
          // 判断 html 中是否有图片
          const imgs = htmlContent.querySelectorAll('img')
          if (imgs && imgs.length > 0) {
            let imgCount = 0;
            imgs.forEach(img => {
              // 图片加载完成后再判断高度
              img.onload = () => {
                imgCount++
                if (imgCount === imgs.length) {
                  this.updateHtmlContentHeight(htmlContent)
                }
              }
              // 图片加载失败也要判断高度
              img.onerror = () => {
                imgCount++
                if (imgCount === imgs.length) {
                  this.updateHtmlContentHeight(htmlContent)
                }
              }
            })
          } else {
            this.updateHtmlContentHeight(htmlContent)
          }
        })
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 展开/收起 html 内容
     */
    toggleHtmlContentHeight () {
      this.isExpand = !this.isExpand
     if (this.isExpand) {
       document.getElementById(this.htmlId).style.maxHeight = 'fit-content'
     } else {
       document.getElementById(this.htmlId).style.maxHeight = this.maxHeightValue + 'px'
     }
    },
    /**
     * 更新 html 内容高度
     *
     * @param {HTMLElement} 加载的 html 元素
     */
    updateHtmlContentHeight (htmlContent) {
      if (htmlContent.offsetHeight > this.maxHeightValue) {
        htmlContent.style.maxHeight = this.maxHeightValue + 'px'
        this.showMore = true
        this.isExpand = false
      } else {
        htmlContent.style.maxHeight = 'fit-content'
        this.showMore = false
        this.isExpand = false
      }
    }
  }
}
</script>

<style scoped lang="less">
.html-content {
  padding: 0 !important;
  overflow-y: hidden !important;
  word-break: break-word;
  /deep/ a{
    color: var(--color-primary) !important;
    text-decoration: underline;
  }
  /deep/ a{
    color: var(--color-primary) !important;
  }
}
</style>