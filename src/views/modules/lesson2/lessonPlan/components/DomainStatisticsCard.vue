<template>
  <el-card class="box-card" shadow="never" v-loading="loading" v-if="!loading && Object.keys(domainScoreData).length > 0">
    <!-- 标题 -->
    <div class="title-font-18 lg-color-text-primary lg-margin-bottom-12">
      <span class="">{{$t('loc.domainStats')}}</span>
      <span class="text-danger">({{domainScoreData.periodAlias}})</span>
    </div>
    <!-- 选择评分基准 -->
    <div v-if="benchmarks.length > 0" class="m-b-sm display-flex align-items">
      <span class="m-r-sm lg-color-text-primary">Benchmark: </span>
      <el-select v-model="benchmarkId" size="small" @change="changeBenchmark">
        <el-option
          :title="benchmark.name"
          style="max-width: 200px; text-overflow: ellipsis;"
          v-for="benchmark in benchmarks"
          :key="benchmark.id"
          :label="benchmark.name"
          :value="benchmark.id">
        </el-option>
      </el-select>
    </div>
    <!-- 显示子领域开关和eld、iep小孩颜色设置 -->
    <div class="display-flex align-items flex-space-between  m-b-sm">
      <div class="display-flex flex-auto m-r-xs align-items">
        <template v-if="domainScoreData && domainScoreData.extraDomainKeys && domainScoreData.extraDomainKeys.length > 0">
          <el-tooltip class="item" effect="dark" :content="subDomainTip" placement="top">
            <i class="lg-icon lg-icon-info lg-margin-right-8 lg-pointer"></i>
          </el-tooltip>
          <div class="overflow-ellipsis lg-color-text-primary">{{$t('loc.plan91')}}</div>
          <el-switch
            class="m-l-sm"
            v-model="domainScoreData.showSubDomain"
            @change="changeBenchmark"
            active-color="#10B3B7"
            inactive-color="#e8eef0">
          </el-switch>
        </template>
      </div>
      <div>
        <el-popover
              style="padding-right: 7px"
              placement="bottom"
              width="280"
              trigger="click">
              <div class="flex-space-between">
                  <div class="flex-row-center white-space">
                    <span class="dot-eld"></span>
                    <span style="margin-left:5px;">{{ $t('loc.plan172') }}</span>
                  </div>
                  <el-switch
                      class="defineSwitch"
                      v-model="eldShow"
                      @change="changeEldShow"
                      active-color="rgb(136, 204, 136)"
                      inactive-color="rgb(224, 224, 224)"
                      :active-value=true
                      :inactive-value=false
                      active-text="on"
                      inactive-text="off">
                  </el-switch>
              </div>
              <div class="flex-space-between" style="padding-top: 10px;">
                  <div class="flex-row-center white-space">
                    <span class="dot-iep"></span>
                    <span style="margin-left:5px;">{{ $t('loc.plan173') }}</span>
                  </div>
                  <el-switch
                      class="defineSwitch"
                      v-model="iepShow"
                      @change="changeIepShow"
                      active-color="rgb(136, 204, 136)"
                      inactive-color="rgb(224, 224, 224)"
                      :active-value=true
                      :inactive-value=false
                      active-text="on"
                      inactive-text="off">
                  </el-switch>
              </div>
              <el-button size="medium" slot="reference"><i class="fa fa-cog" @click="openSettingPopover"></i>{{ $t('loc.settingType') }}</el-button>
        </el-popover>
      </div>
    </div>
    <!-- eld、iep小孩颜色图例 -->
    <div class="flex-content-start">
      <!-- ELD child -->
      <div v-show="eldShow" class="legend-child">
        <div class="child-eld"></div>
        <div>ELD Children</div>
      </div>
      <!-- IEP child -->
      <div v-show="iepShow" class="legend-child">
        <div class="child-iep" :class="eldShow ? 'add-margin-l-20' : ''"></div>
        <div>IEP Children</div>
      </div>
    </div>
    <!-- IEP & ELD child -->
    <div class="flex-content-start m-b-sm" v-show="eldShow && iepShow">
      <div class="child-iep-eld"></div>
      <div>IEP and ELD children</div>
    </div>
    <!-- 表头 -->
    <el-row class="display-flex align-items bg-light add-padding-t-6 add-padding-b-6 m-b-xs lg-color-text-primary">
      <el-col :span="domainScoreData.showBenchmark ? 8 : 10" class="text-center line-height-18">
        {{$t('loc.meanScore')}}
      </el-col>
      <el-col :span="8" class="text-center line-height-18" v-if="domainScoreData.showBenchmark">
        {{$t('loc.benchmark')}}
      </el-col>
      <el-col :span="4" class="text-center line-height-18" v-if="domainScoreData.showBenchmark">
        {{$t('loc.above')}}
      </el-col>
      <el-col :span="4" class="text-center line-height-18" v-if="domainScoreData.showBenchmark">
        {{$t('loc.below')}}
      </el-col>
    </el-row>
    <!-- 领域分内容 -->
    <template v-if="!!domainScoreData">
      <div v-for="(domain, index) in domainScoreData.domains" :key="index">
        <!-- 领域信息 -->
        <div class="drdp-report display-flex align-items">
          <h2 class="domain-icon" :class="'icon-' + getDomainIconName(domain.measureAbbr)" />
          <div>
            <div class="font-bold line-height-18">{{domain.measureAbbr.toUpperCase()}}</div>
            <div class="overflow-ellipsis line-height-18 lg-color-text-primary" :title="domain.measureName">{{domain.measureName}}</div>
          </div>
        </div>
        <!-- 分数信息 -->
        <el-row class="">
          <el-col :span="domainScoreData.showBenchmark ? 8 : 10">
            <div class="overflow-ellipsis font-size-12 text-center level-item lg-color-text-primary" :style="{ background: getDRDPRatingColor(domain.levelName)}" :title="domain.notRated || domain.levelName === '' ? '' : domain.levelName">
              {{domain.notRated || domain.levelName === '' ? '--' : domain.levelName}}
            </div>
          </el-col>
          <el-col :span="8" v-if="domainScoreData.showBenchmark">
            <div class="overflow-ellipsis font-size-12 text-center level-item lg-color-text-primary" :style="{ background: getDRDPRatingColor(domain.benchmarkLevelName)}" :title="domain.noBenchmark ? '' : domain.benchmarkLevelName">
              {{domain.noBenchmark ? '--' : domain.benchmarkLevelName}}
            </div>
          </el-col>
          <el-col :span="4" v-if="domainScoreData.showBenchmark">
            <el-popover
              :disabled="!domain.aboveChildrenList || domain.aboveChildrenList.length === 0"
              popper-class="domain-stats-child-list scrollbar"
              trigger="hover"
              width="160">
              <div>
                <div v-for="(child, index) in domain.aboveChildrenList" :key="index" :class="{'b-t': index != 0}" class="display-flex align-items m-b-xs add-padding-t-4">
                  <el-avatar size="small" :src="child.avatarUrl" class="flex-none"></el-avatar>
                  <div class="m-l-sm add-padding-t-4 overflow-ellipsis" :class="childNameColor(child)" :title="child.firstName + ' ' + child.lastName">{{child.firstName}} {{child.lastName}}</div>
                </div>
              </div>
              <div class="text-center lg-pointer" slot="reference">
                {{domain.notRated || domain.noBenchmark ? '--' : domain.aboveChildrenList.length}}
              </div>
            </el-popover>
          </el-col>
          <el-col :span="4" v-if="domainScoreData.showBenchmark">
            <el-popover
              :disabled="!domain.belowChildrenList || domain.belowChildrenList.length === 0"
              popper-class="domain-stats-child-list scrollbar"
              trigger="hover"
              width="160">
              <div>
                <div v-for="(child, index) in domain.belowChildrenList" :key="index" :class="{'b-t': index != 0}" class="display-flex align-items m-b-xs add-padding-t-4">
                  <el-avatar size="small" :src="child.avatarUrl" class="flex-none"></el-avatar>
                  <div class="m-l-sm add-padding-t-4 overflow-ellipsis" :class="childNameColor(child)" :title="child.firstName + ' ' + child.lastName">{{child.firstName}} {{child.lastName}}</div>
                </div>
              </div>
              <div class="text-center lg-pointer" slot="reference" :class="{'text-danger': !domain.notRated && !domain.noBenchmark}">
                {{domain.notRated || domain.noBenchmark ? '--' : domain.belowChildrenList.length}}
              </div>
            </el-popover>
          </el-col>
        </el-row>
        <el-divider v-if="index != domainScoreData.domains.length - 1" class="add-margin-t-12 add-margin-b-12"></el-divider>
      </div>
    </template>
  </el-card>
</template>

<script>
import { mapState } from "vuex"
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'

export default {
  name: 'DomainStatisticsCard',
  components: {
  },

  props: {
    groupId: {
      type: String
    },
    domainScoreData: {
      type: Object
    },
    benchmarks: {
      type: Array
    },
    loading: {
      type: Boolean
    }
  },

  data () {
    return {
      eldShow: false,
      iepShow: false
    }
  },

  created () {
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    }),
    subDomainTip () {
      if (!this.domainScoreData || !this.domainScoreData.extraDomainKeys || this.domainScoreData.extraDomainKeys.length === 0) {
        return ''
      }
      return this.$t('loc.plan92', {subDomains: this.domainScoreData.extraDomainKeys.join(', ')})
    }
  },

  methods: {
    // 小孩颜色
    childNameColor (child) {
      // 如果特殊小孩的名字颜色开关打开，就显示特殊颜色
      const eld = child.eld
      const iep = child.iep
      if (eld && iep) {
        if (this.eldShow && this.iepShow) {
          return 'child-name-eld-iep'
        }
        if (this.eldShow && !this.iepShow) {
          return 'child-name-eld'
        }
        if (!this.eldShow && this.iepShow) {
          return 'child-name-iep'
        }
      } else if (eld && !iep) {
        if (this.eldShow) {
          return 'child-name-eld'
        }
      } else if (!eld && iep) {
        if (this.iepShow) {
          return 'child-name-iep'
        }
      }
    },

    /**
     * 子领域显示开关
     */
    changeBenchmark () {
      // 根据不同的页面和开关的值发送不同的埋点事件（设置子领域开关值）
      if (this.domainScoreData.showSubDomain && this.$route.name == 'edit-plan') {
        this.$analytics.sendEvent('web_weekly_plan_edit_open_sub_domain')
      } else if (!this.domainScoreData.showSubDomain && this.$route.name == 'edit-plan') {
        this.$analytics.sendEvent('web_weekly_plan_edit_close_sub_domain')
      } else if (this.domainScoreData.showSubDomain && this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_vir_detail_open_sub_domain')
      } else if (!this.domainScoreData.showSubDomain && this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_vir_detail_close_sub_domain')
      }
      this.$emit('callChangeBenchmark')
    },

    getDomainIconName (name) {
      if (name.indexOf('–') != -1) {
        return name.replace('–', '-').toLocaleLowerCase()
      } else if (name.indexOf(':') != -1) {
        return name.split(':')[0].toLocaleLowerCase()
      }
      return name.toLocaleLowerCase()
    },

    getDRDPRatingColor (levelName) {
      return tools.getDRDPRatingColor(levelName)
    },

    /**
     * 打开设置弹窗
     */
    openSettingPopover () {
      // 根据不同的页面发送不同的埋点事件（设置 IEP/ELD 小孩名颜色弹窗）
      if (this.$route.name == 'edit-plan') {
        this.$analytics.sendEvent('web_weekly_plan_edit_children_setting')
      } else if (this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_vir_detail_children_setting')
      }
    },

    /**
     * 切换 eld 小孩颜色开关
     */
    changeEldShow (val) {
      // 根据不同的页面和开关的值发送不同的埋点事件（设置 ELD 小孩名颜色开关值）
      if (this.$route.name == 'edit-plan' && val) {
        this.$analytics.sendEvent('web_weekly_plan_edit_open_eld_children')
      } else if (this.$route.name == 'edit-plan' && !val) {
        this.$analytics.sendEvent('web_weekly_plan_edit_close_eld_children')
      } else if (this.$route.name == 'shared-plan-detail' && val) {
        this.$analytics.sendEvent('web_weekly_vir_detail_open_eld_children')
      } else if (this.$route.name == 'shared-plan-detail' && !val) {
        this.$analytics.sendEvent('web_weekly_vir_detail_close_eld_children')
      }
    },

    /**
     * 切换 iep 小孩颜色开关
     */
    changeIepShow (val) {
      // 根据不同的页面和开关的值发送不同的埋点事件（设置 IEP 小孩名颜色开关值）
      if (this.$route.name == 'edit-plan' && val) {
        this.$analytics.sendEvent('web_weekly_plan_edit_open_iep_children')
      } else if (this.$route.name == 'edit-plan' && !val) {
        this.$analytics.sendEvent('web_weekly_plan_edit_close_iep_children')
      } else if (this.$route.name == 'shared-plan-detail' && val) {
        this.$analytics.sendEvent('web_weekly_vir_detail_open_iep_children')
      } else if (this.$route.name == 'shared-plan-detail' && !val) {
        this.$analytics.sendEvent('web_weekly_vir_detail_close_iep_children')
      }
    }
  },

  watch: {
  }
}
</script>

<style lang="less" scoped>
.child-name-eld {
  color:  #2D9CDB;
}
.child-name-iep {
  color:   #FF7F41;
}
.child-name-eld-iep {
  color: #BB6BD9;
}
.domain-icon {
  margin: 0;
  height: 30px;
}
.level-item {
  margin: 4px;
  padding: 0 4px;
}
.flex-content-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.child-eld {
  height:8px;
  width:8px;
  background: #2D9CDB;
  border-radius:50%;
  margin-right: 3px;
}
.child-iep {
  height:8px;
  width:8px;
  background:  #FF7F41;
  border-radius:50%;
  margin-right: 3px;
}
.child-iep-eld {
  height:8px;
  width:8px;
  background: #BB6BD9;
  border-radius:50%;
  margin-right: 3px;
}
.dot-eld {
  width: 8px;
  height: 8px;
  background:  #2D9CDB;
  border-radius: 50%;
}
.dot-iep {
  width: 8px;
  height: 8px;
  background:   #FF7F41;
  border-radius: 50%;
}
.legend-child {
  display: flex;
  align-items: center;
  // justify-content: flex-start;
}
</style>

<style lang="less">
.domain-stats-child-list {
  max-height: 200px;
  overflow-y: auto;
}
</style>