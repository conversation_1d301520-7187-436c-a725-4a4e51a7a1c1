<template>
  <div class="reflection-editor h-full bg-white lg-padding-24" v-loading="loading">
    <!-- 课程反思 -->
    <div class="header display-flex justify-content-between">
      <div class="display-flex align-items text-primary">
        <div class="color-block-primary"></div>
        <div class="font-size-18">{{$t('loc.plan79')}}</div>
      </div>
    </div>
    <el-input
      class="m-t-xs"
      type="textarea"
      :rows="3"
      :placeholder="$t('loc.plan75')"
      v-model="lessonReflection">
    </el-input>
    <span class="text-danger m-t-sm m-b-sm" v-if="lessonReflection && lessonReflection.length > 2000">
      {{$t('loc.plan17', {num: '2000'})}}
    </span>
    <!-- 小孩反思 -->
    <div class="header display-flex justify-content-between m-t-sm">
      <div class="display-flex align-items text-success">
        <div class="color-block-success"></div>
        <div class="font-size-18">{{$t('loc.plan80')}}</div>
      </div>
    </div>
    <el-input
      class="m-t-xs"
      type="textarea"
      :rows="3"
      :placeholder="$t('loc.plan76')"
      v-model="childReflection">
    </el-input>
    <span class="text-danger m-t-sm m-b-sm" v-if="childReflection && childReflection.length > 2000">
      {{$t('loc.plan17', {num: '2000'})}}
    </span>
    <!-- 按钮 -->
    <div class="m-t-sm text-right bottom-area">
      <el-button
        type="primary"
        :disabled="!canSave"
        @click="saveReflection"
        :loading="saveLoading">
        {{$t('loc.save')}}
      </el-button>
    </div>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
 
export default {
  name: 'ReflectionEditor',
  components: {  },

  props: {
    objectId: {
      type: String
    }
  },

  data () {
    return {
      lessonReflection: '',
      childReflection: '',
      saveLoading: false,
      loading: true
    }
  },

  computed: {
    canSave () {
      let noLessonReflection = !this.lessonReflection || this.lessonReflection.trim().length === 0
      let noChildReflection = !this.childReflection || this.childReflection.trim().length === 0
      let overLessonReflection = this.lessonReflection && this.lessonReflection.length > 2000
      let overChildReflection = this.childReflection && this.childReflection.length > 2000
      return !overLessonReflection && !overChildReflection && (!noLessonReflection || !noChildReflection)
    }
  },

  methods: {
    loadReflection () {
      this.loading = true
      LessonApi.getLessonReflection({
        itemId: this.objectId
      }).then(res => {
        if (res) {
          this.lessonReflection = res.lessonReflection
          this.childReflection = res.childReflection
        }
        this.$emit('callLoadReflection')
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    },

    saveReflection () {
      let noLessonReflection = !this.lessonReflection || this.lessonReflection.trim().length === 0
      let noChildReflection = !this.childReflection || this.childReflection.trim().length === 0
      if (noLessonReflection && noChildReflection) {
        this.$message.error(this.$t('loc.plan87'))
        return
      }
      this.saveLoading = true
      LessonApi.saveLessonReflection({
        itemId: this.objectId,
        lessonReflection: this.lessonReflection,
        childReflection: this.childReflection
      }).then(res => {
        this.saveLoading = false
        this.$message({
          type: 'success',
          message: this.$t('loc.plan39')
        })
      }).catch(error => {
        this.saveLoading = false
      })
    }
  },

  watch: {
    objectId: {
      immediate: true,
      handler() {
        this.objectId && this.loadReflection()
      }
    }
  }
}
</script>

<style lang="less" scoped>
</style>