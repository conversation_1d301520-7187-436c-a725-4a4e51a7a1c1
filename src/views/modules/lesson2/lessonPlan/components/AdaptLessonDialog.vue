<template>
  <div>
    <el-dialog
      custom-class="adapt-lesson-dialog"
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :width="isCurriculumPlugin? '800px': '70%'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :append-to-body="true"
      :before-close="closeAdaptUnitDialog">
      <div slot="title">
        <div>
          <span>{{ $t('loc.adaptUDLAndCLRButton') }}</span>
        </div>
      </div>
      <div v-if="dialogVisible">
        <!-- 顶部提示语 -->
        <div class="lg-margin-bottom-20 font-size-16" v-if="isCurriculumPlugin">
          {{ $t('loc.pluginUnit8') }}
        </div>
        <div class="lg-margin-bottom-20 font-size-16" v-else>
          {{ $t('loc.adaptUnitPlanner2') }}
        </div>
        <el-alert
          class="add-margin-b-10"
          style="margin-top: -10px;"
          v-if="showIEPButEligibilityIsEmptyPlugin"
          :title="$t('loc.unitPlannerChildListTitle2')"
          type="error"
          :closable="false"
          show-icon></el-alert>
        <PersonalizeToLearner v-if="isCurriculumPlugin && dialogVisible"
                              ref="personalizeToLearner"
                              :childId="defaultChildId"
                              :isFromUnit="true"
                              :initPersonalizedForm="initPersonalizeForm"
                              :updatePersonalizeValidate="personalizeValidate"
                              :updatePersonalizeForm="personalizeUpdate"
        />
      </div>
      <div slot="footer">
        <!--:disabled="copyAdaptUnitDisabled"-->
        <el-button type="primary"
                   :class="{'disabled' : copyAdaptUnitDisabled}"
                   :loading="copyAdaptUnitLoading || adaptedLoading"
                   @click="beforeStartBatchAdapt">{{ $t('loc.adaptUnitPlanner7') }}
        </el-button>
        <!--{{copyAdaptUnitDisabled}}-->
      </div>
    </el-dialog>
    <!--Assign Teacher Groups-->
    <AssignTeacherGroups @close="closeAssignTeacherGroups"
                         @changeEnableTeacherGroupStatus="changeTeacherGroupSwitch"
                         :teacherGroupEnabled="teacherGroupEnabled"
                         ref="assignTeacherGroups"></AssignTeacherGroups>
  </div>
</template>
<script>
import LessonApi from '@/api/lessons2'
import { mapState } from 'vuex'
import { equalsIgnoreCase, getCurrentUser } from '@/utils/common'
import PersonalizeToLearner from '@/views/modules/lesson2/unitPlanner/components/adaptUnits/PersonalizeToLearner.vue'
import AssignTeacherGroups from '@/views/modules/lesson2/lessonPlan/components/AssignTeacherGroups.vue'

export default {
  name: 'AdaptLessonDialog',
  components: {
    PersonalizeToLearner,
    AssignTeacherGroups
  },
  props: {
    adapted: {
      type: Boolean,
      default: false
    },
    adaptedGroupId: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    adaptedCenterId: {
      type: String,
      default: ''
    },
    isFromUnitDetail: {
      type: Boolean,
      default: false
    },
    // Lesson Id
    adaptLessonId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      showAdaptUDLAndCLRGuide: false, // 是否显示引导
      dialogVisible: false, // 改编弹窗显示
      popoverWidth: 300, // 选择单元弹窗宽度
      pageSize: 10, // 每页显示数量
      pageNum: 1, // 当前页码
      total: 0, // 总数
      units: [], // 单元列表
      unit: null, // 单个单元
      selectUnitPopoverVisable: false, // 选择单元弹窗显示
      selectedUnit: null, // 选中的单元
      checkAll: false, // 是否全选
      isIndeterminate: false, // 是否非全选
      selectedPlans: [], // 选中的周计划
      currentCenterId: '', // 当前学校 id
      currentGroupId: '', // 当前班级 id
      defaultChildId: '', // 默认小孩 id
      centers: [], // 学校列表
      groups: [], // 班级列表
      emptyPageShow: false, // 空页面是否展示
      childCount: 0, // 小孩总数
      hasTeacher: false, // 是否有老师
      initialized: false, // 是否初始化成功了
      showIEPButEligibilityIsEmptyChildren: false, // 是否展示 IEP 但 EligibilityIsEmpty 的小孩
      additionInfo: {}, // 从 PersonalizeChildList 中获取的数据
      personalizeChildListDataLoading: false, // PersonalizeChildList 组件的数据是否正在加载
      applyToGroup: false, // 是否应用到班级
      teacherGroupEnabled: false, // 是否开启分组
      teacherGroups: [], // 老师的分组信息
      copyAdaptUnitLoading: false, // 复制改编单元是否正在加载
      adaptedLoading: false, // loading 状态
      checkCanContinueAdapt: false, // 是否显示混龄班
      unitLoading: false, // 单元是否正在加载
      validated: false, // 是否验证通过，在 curriculum plugin 中使用
      personalizedForm: {}, // 个性化表单
      getCenterLoading: false // 获取学校信息 Loading
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open, // UDL 和 CLR 开关
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      guideFeatures: state => state.common.guideFeatures // 功能引导
    }),
    showBatchTitle () {
      return this.$t('loc.adaptUnitPlanner20')
    },
    /**
     * 如果选择了 IEP 但 Eligibility 是空的小孩，则展示提示
     */
    showIEPButEligibilityIsEmptyPlugin () {
      // 如果 iep 为 true 但是 characteristics 为空，则展示提示
      if (this.personalizedForm &&
        this.personalizedForm.iep && (
          !this.personalizedForm.characteristics
          || this.personalizedForm.characteristics.length < 1
        )) {
        return true
      }
      // personalizedForm 中的其他数据都为空
      // 如果种族，地区，特征有一个不为空，验证通过
      const hasRace = this.personalizedForm && this.personalizedForm.race && this.personalizedForm.race.length > 0
      const hasRegion = this.personalizedForm && this.personalizedForm.region && this.personalizedForm.region.length > 0
      const hasCharacteristics = this.personalizedForm && this.personalizedForm.characteristics && this.personalizedForm.characteristics.length > 0
      // 如果 language 存在，但是仅有 English，则展示提示
      if (this.personalizedForm && !hasRace && !hasRegion && !hasCharacteristics && this.personalizedForm.language && this.personalizedForm.language.length === 1 && this.personalizedForm.language.indexOf('English') !== -1) {
        return true
      }
      // 默认返回 false
      return false
    },
    /**
     * 获取用户 id
     */
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    resolvedRouteHref () {
      if (!this.currentCenterId || !this.currentGroupId) {
        return null
      }
      return this.$router.resolve({
        name: 'manageChildren',
        query: {
          centerId: this.currentCenterId,
          groupId: this.currentGroupId
        }
      }).href
    },
    showAdaptUnitApplyToNewTag: {
      get () {
        return this.guideFeatures && this.guideFeatures.showAdaptUnitApplyToNewTag
      },
      set (value) {
        if (this.guideFeatures) {
          this.guideFeatures.showAdaptUnitApplyToNewTag = value
          this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
        }
      }
    },
    // 不满足改编条件
    copyAdaptUnitDisabled () {
      // 如果是插件平台
      if (this.isCurriculumPlugin) {
        return this.curriculumPluginValidate || this.disabled
      }
      // 如果是单个改编课程，按钮是否禁用只需判断学校班级 ID 是否选择以及当前班级下是否有小孩即可
      return !this.hasTeacher || !this.currentGroupId || this.childCount === 0 || this.personalizeChildListDataLoading
    },
    // curriculum plugin 验证
    curriculumPluginValidate () {
      // 如果是单个改编课程，按钮是否禁用只需判断学校班级 ID 是否选择以及当前班级下是否有小孩即可
      return !this.hasTeacher || !this.validated
    },
    // 获取指定属性的值
    getAttrValue () {
      return function (child, attrName) {
        // 不存在返回空
        if (!child || !attrName) {
          return ''
        }
        // 属性列表
        let attrs = child.attrs
        if (!attrs) {
          return ''
        }
        // 匹配到的属性值
        let matchValues = null
        // 遍历属性列表
        attrs.forEach(attr => {
          // 匹配属性名称
          if (attr && attr.name && attr.name.trim().toLowerCase() === attrName.trim().toLowerCase()) {
            // 属性值
            let attrValues = attr.values
            if (attrValues && attrValues.length > 0) {
              matchValues = attrValues
            }
          }
        })
        // 如果有属性值，以逗号分割
        if (matchValues) {
          return matchValues.join(', ')
        }
        // 没有值
        return ''
      }
    }
  },

  watch: {
    currentGroupId (val) {
      if (val) {
        sessionStorage.setItem('selectedGroupId' + this.currentUserId, this.currentGroupId)
        this.getGroupAdaptSetting()
        if (this.dialogVisible) {
          this.listChildren()
          this.getGroupTeams()
        }
      }
    }
  },

  mounted () {
    this.computePopoverWidth()
  },

  methods: {
    equalsIgnoreCase: equalsIgnoreCase,

    // 刷新单元列表
    refreshUnits () {
      this.$emit('refreshUnits')
    },
    // 初始化 showMixedAge
    async initShowMixedAge () {
      // 如果 groupId 不存在，那么就直接返回
      if (!this.currentGroupId || this.currentGroupId === '') {
        return
      }
      // 如果没有选择任何的 UnitId 则直接返回
      if (this.adaptLessonId) {
        await this.initShowMixedAgeHelper()
      }
    },
    // 初始化 showMixedAge
    async initShowMixedAgeHelper () {
      let request = {
        groupId: this.currentGroupId,
        planIds: this.selectedPlans,
        lessonIds: [this.adaptLessonId]
      }
      // 将 showMixedAge 转化为布尔值
      const res = await this.$axios.post($api.urls().checkCanContinueAdapt, request)
      this.checkCanContinueAdapt = !!res.success
    },
    /**
     * 初始化个性化表单
     * @returns {{}}
     */
    initPersonalizeForm () {
      this.$nextTick(() => {
        // 更新一下状态
        this.$forceUpdate()
      })
      return this.personalizedForm
    },
    /**
     * 个性化表单验证
     * @param validated
     */
    personalizeValidate (validated) {
      this.validated = validated
    },
    /**
     * 更新个性化表单
     * @param personalizedForm
     */
    personalizeUpdate (personalizedForm) {
      this.personalizedForm = personalizedForm
    },
    // 初始化对应的小孩信息
    async initEnrollmentMetadata () {
      // 转换 personalizedForm 到 StudentAttrEntity 的方法
      function transformToStudentAttrEntities (personalizedForm, enrollmentId) {
        // 定义映射关系
        const attrNameMapping = {
          language: 'Language',
          race: 'Race',
          iep: 'IEP/IFSP',
          region: 'Place of Origin',
          characteristics: 'Special education eligibility'
        }

        // 定义 StudentAttrEntity 数组
        const studentAttrEntities = []

        // 遍历 personalizedForm
        for (const key in personalizedForm) {
          // 如果 personalizedForm 有这个属性
          if (personalizedForm.hasOwnProperty(key)) {
            const attrName = attrNameMapping[key] // 根据映射找到 attrName
            const attrValues = personalizedForm[key] // 获取值
            // 如果是数组
            if (Array.isArray(attrValues)) {
              // 多值情况
              attrValues.forEach((value) => {
                studentAttrEntities.push(createStudentAttrEntity(attrName, value, enrollmentId))
              })
            } else {
              // 单值情况
              const attrValue = attrValues ? attrValues.toString() : 'false' // 处理布尔值和空值
              studentAttrEntities.push(createStudentAttrEntity(attrName, attrValue, enrollmentId))
            }
          }
        }

        return studentAttrEntities
      }

      // 创建单个 StudentAttrEntity 对象
      function createStudentAttrEntity (attrName, attrValue, enrollmentId) {
        return {
          attrName: attrName,
          attrValue: attrValue,
          enrollmentId: enrollmentId
        }
      }

      // 如果 this.personalizedForm 中的 iep 为 false，则清空 characteristics
      if (this.personalizedForm.iep === false) {
        this.personalizedForm.characteristics = []
      }
      // 定义请求参数
      const checkAttrRequest = {
        groupId: this.currentGroupId,
        centerId: this.currentCenterId,
        studentAttrs: transformToStudentAttrEntities(this.personalizedForm, this.currentGroupId)
      }
      try {
        await this.$axios.post($api.urls().createChildrenForGroup, checkAttrRequest)
      } catch (e) {
        this.adaptedLoading = false
      }
    },
    /**
     * 处理其他平台的逻辑
     * @returns {Promise<void>}
     */
    async processOtherPlatform () {
      if (!this.isCurriculumPlugin) {
        return
      }
      // 如果是插件平台，这里需要进行特殊处理
      await this.initEnrollmentMetadata()
    },
    async beforeStartBatchAdapt () {
      // 如果禁止直接返回
      if (this.copyAdaptUnitDisabled) {
        return
      }
      // 如果是禁止状态
      if (this.disabled) {
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'RENEWAL'
        }
        this.$bus.$emit('message', data)
        return
      }
      // loading 状态
      this.adaptedLoading = true
      // 如果是插件平台，这里需要进行特殊处理
      await this.processOtherPlatform()
      // 初始化 showMixedAge
      await this.initShowMixedAge()
      // 检查是否无 IEP 或 ELD 的小孩
      if (!this.checkCanContinueAdapt) {
        // 关闭 loading
        this.adaptedLoading = false
        let title = this.$t('loc.noIEPOrELDNote')
        let text = this.isCurriculumPlugin ? this.$t('loc.unitPlannerPersonalizePlanErrorTitle2') : this.$t('loc.unitPlannerPersonalizePlanErrorTitle')
        this.$confirm(text, title, {
          confirmButtonText: this.$t('loc.noIEDOrELDClose'),
          cancelButtonText: this.$t('loc.noIEDOrELDClose'),
          customClass: 'width-600',
          showCancelButton: false
        }).then(() => {
        }).catch(() => {
        })
        return
      }
      // 关闭 loading
      this.adaptedLoading = false
      this.$emit('updateAdaptLesson')
      this.$bus.$emit('close-resources-guide')
      this.closeAdaptUnitDialog(false)
    },
    /**
     * 初始化默认的学校和班级
     */
    initDefaultCenterOrGroup () {
      this.getCenterLoading = true
      // 调用接口
      return new Promise((resolve, reject) => {
        // 如果不是 Curriculum Plugin 平台，则不需要获取学校和班级信息
        if (!this.isCurriculumPlugin) {
          resolve()
        }
        this.$axios.get($api.urls().getOrCreateUserDefaultCenter).then((res) => {
          // 班级列表
          const groups = res.groups
          // 获取学校 ID
          const centerId = res.id
          // 停止 Loading
          this.getCenterLoading = false
          // 默认选择第一个班级
          if (groups && groups.length > 0) {
            this.currentGroupId = groups[0].id
            this.groups = groups
          }
          // 获取班级中小孩信息
          if (groups[0].enrollments && groups[0].enrollments.length > 0) {
            this.defaultChildId = groups[0].enrollments[0].id
          }
          // 默认选择第一个学校
          this.currentCenterId = centerId
          // 处理获取的数据
          this.processChildInfo(res.childInfo)
          resolve()
        }).catch(error => {
          // 停止 Loading
          this.getCenterLoading = false
          console.log(error)
          this.$message.error(error.response.data.error_message)
          reject(error)
        })
      })
    },
    /**
     * 处理小孩信息
     * @param childInfo 小孩信息
     */
    processChildInfo (childInfo) {
      // 如果小孩信息不为空，则进行处理
      if (!childInfo) {
        return
      }

      function transformToPersonalizedForm (studentAttrEntities) {
        // 定义映射关系
        const attrNameMapping = {
          'Language': 'language',
          'Race': 'race',
          'IEP/IFSP': 'iep',
          'Place of Origin': 'region',
          'Special education eligibility': 'characteristics'
        }

        // 定义 personalizedForm 对象
        const personalizedForm = {
          language: [],
          race: [],
          iep: false,
          region: [],
          characteristics: []
        }

        // 遍历 studentAttrEntities 数组
        studentAttrEntities.forEach(entity => {
          const key = attrNameMapping[entity.attrName]
          if (key) {
            if (key === 'iep') {
              personalizedForm[key] = entity.attrValue.toLowerCase() === 'true'
            } else {
              personalizedForm[key].push(entity.attrValue)
            }
          }
        })

        return personalizedForm
      }

      // 解析 string 为对象
      const childInfoObj = JSON.parse(childInfo)
      // childInfoObj 为一个数组
      this.personalizedForm = transformToPersonalizedForm(childInfoObj)
    },
    /**
     * 打开改编单元弹窗
     */
    openAdaptUnitDialog () {
      if (this.disabled && this.isCurriculumPlugin) {
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'RENEWAL'
        }
        this.$bus.$emit('message', data)
        return
      }
      // 打开弹窗的时候需要先创建生成对应的班级和学校
      this.initDefaultCenterOrGroup()
        .then(() => {
          // 调用之前的逻辑
          this.openAdaptUnitDialogHelper()
        })
    },
    // 改编单元
    openAdaptUnitDialogHelper () {
      // 如果是点击单个课程操作时，不需要显示单元列表，直接 loading 显示学校班级列表
      if (!this.centers || this.centers.length === 0) {
        this.personalizeChildListDataLoading = true
      }
      this.dialogVisible = true
      this.computePopoverWidth()
      this.getGroups()
    },

    // 计算 Popver 选择框宽度
    computePopoverWidth () {
      this.$nextTick(() => {
        if (this.$refs.customSelect) {
          this.popoverWidth = this.$refs.customSelect.offsetWidth
        }
      })
    },
    // 获取小孩列表
    listChildren () {
      if (!this.currentGroupId) return
      this.personalizeChildListDataLoading = true
      // 参数
      let params = {
        pageNum: 1,
        pageSize: 1000,
        sort: 'lastName',
        order: 'asc',
        groupId: this.currentGroupId
      }
      // 调用接口
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().manageChildren, { params: params }).then(res => {
          this.personalizeChildListDataLoading = false
          this.$nextTick(() => {
            this.$refs.personalizeChildList.children = res.results // 小孩列表
            this.childCount = res.total // 小孩总数
            // 判断是否是空页面，如果小孩人数为空或者小孩列表为空
            this.$refs.personalizeChildList.emptyPageShow = res.total === 0 || (res.results && res.results.length === 0)
            // 获取非 IEP 小孩
            const noIEPChildren = this.getNoIEPChildren(res.results)
            // 获取非 ELD 的小孩
            const noELDChildren = this.getNoELDChildren(res.results)
            // 获取是 IEP 的小孩 但是这个小孩的 Special education eligibility 是空的小孩
            const iepButEligibilityIsEmptyChildren = this.getIEPChildButEligibilityIsEmpty(res.results)
            // 封装为对象发送出去
            this.additionInfo = {
              noIEPChildren: noIEPChildren,
              noELDChildren: noELDChildren,
              iepButEligibilityIsEmptyChildren: iepButEligibilityIsEmptyChildren,
              childCount: res.total,
              loading: false
            }
            this.getAdditionInfo(this.additionInfo)
          })
          resolve()
        }).catch(err => {
          reject(err)
        })
      })
    },

    // 获取是 IEP 的小孩 但是这个小孩的 Special education eligibility 是空的小孩
    getIEPChildButEligibilityIsEmpty (children) {
      // 校验小孩，将是 IEP 的小孩获取得到
      let iepButEligibilityIsEmptyChildren = []
      children.forEach(child => {
        // 是否是 IEP
        let isIEP = false
        // 获取属性
        const attrIEP = child.showIep
        // 判断 attrIEP 是否是 true
        if (attrIEP) {
          isIEP = true
        }
        // 是 IEP 的小孩
        if (isIEP) {
          // 获取属性
          const attrIEPGoal = this.getAttrValue(child, 'Special education eligibility')
          // 判断 attrIEPGoal 是否是空
          if (this.isEmptyOrBlank(attrIEPGoal)) {
            iepButEligibilityIsEmptyChildren.push(child)
          }
        }
      })
      return iepButEligibilityIsEmptyChildren
    },
    // 获取非 ELD 的小孩
    getNoELDChildren (children) {
      // 校验小孩，将不是 ELD 的小孩获取得到
      let noELDChildren = []
      children.forEach(child => {
        // 是否是 ELD
        let isELD = false
        // 获取属性
        const attrELD = child.eldDB
        // 判断 attrELD 是否是 true
        if (attrELD) {
          isELD = true
        }
        // 不是 ELD 的小孩
        if (!isELD) {
          noELDChildren.push(child)
        }
      })
      return noELDChildren
    },
    // 获取非 IEP 小孩
    getNoIEPChildren (children) {
      // 校验小孩，将不是 IEP 的小孩获取得到
      let noIEPChildren = []
      children.forEach(child => {
        // 是否是 IEP
        let isIEP = false
        // 获取属性
        const attrIEP = child.showIep
        // 判断 attrIEP 是否是 true
        if (attrIEP) {
          isIEP = true
        }
        // 不是 IEP 的小孩
        if (!isIEP) {
          noIEPChildren.push(child)
        }
      })
      return noIEPChildren
    },
    // 判断属性是否是 true
    isEmptyOrBlank (s) {
      return !s || s.trim() === ''
    },

    // 当 showGroupTeamDescription 发生改变的时候，获取分组信息
    changeTeacherGroupSwitch (val) {
      this.teacherGroupEnabled = val
      // 判断是否存在分组信息，如果不存在，那么就展示 Assign Teacher Groups 弹窗
      LessonApi.setTeacherGroupEnabled(this.currentGroupId, this.teacherGroupEnabled)
        .then(() => {
          if (this.teacherGroupEnabled) {
            this.showAssignTeacherGroups()
            // 如果不需要展示的情况下，这个时候就需要获取一下分组信息
          }
        })
      sessionStorage.setItem('enableGroupTeams' + this.currentGroupId, JSON.stringify(this.teacherGroupEnabled))
    },

    //  关闭 Assign Teacher Groups 弹窗的回调
    closeAssignTeacherGroups (saveClose) {
      // 如果是点击了保存，那么就开始更新数据，同时修改 isFirst
      if (saveClose) {
        // 由于 Assign Teacher Groups 弹窗中的数据发生了改变，所以需要重新获取一下分组信息
        this.getGroupTeams(saveClose)
      } else {
        // 判断当前 this.teacherGroups.length 是否为 1，如果为 1，那么就修改 showGroupTeamDescription
        if (this.teacherGroups.length <= 1) {
          this.teacherGroupEnabled = false
          // 更新班级是否启用老师分组
          LessonApi.setTeacherGroupEnabled(this.currentGroupId, false)
          sessionStorage.setItem('enableGroupTeams' + this.currentGroupId, JSON.stringify(this.teacherGroupEnabled))
        }
      }
    },

    // 获取分组信息
    getGroupTeams (saveClose) {
      // 如果班级 Id 存在并且 showGroupTeamDescription 为 true，那么此时获取分组信息
      if (this.currentGroupId) {
        // 初始化为 false
        this.initialized = false
        // 获取班级的信息
        this.$axios.get($api.urls().getGroupTeams + '?groupId=' + this.currentGroupId + '&enableGroupTeams=true')
          .then((res) => {
            if (res) {
              this.hasTeacher = res.teacherList && res.teacherList.length > 0
              // 设置初始化为 true
              this.initialized = true
              this.$nextTick(() => {
                // 如果 res 是存在的，那么就将 res 的值赋值给 groupTeams
                // 如果 filterGroupData 为 1
                if (!saveClose && this.teacherGroups && this.teacherGroups.length === 1) {
                  this.showAssignTeacherGroups()
                } else if (saveClose && this.teacherGroups && this.teacherGroups.length === 1) {
                  // 修改 showGroupTeamDescription
                  this.teacherGroupEnabled = false
                  // 更新班级是否启用老师分组
                  LessonApi.setTeacherGroupEnabled(this.currentGroupId, false)
                  sessionStorage.setItem('enableGroupTeams' + this.currentGroupId, JSON.stringify(this.teacherGroupEnabled))
                }
              })
            } else {
              // 设置初始化为 true
              this.hasTeacher = false
              this.initialized = true
            }
          })
          .catch(() => {
            // 设置初始化为 true
            this.hasTeacher = false
            this.initialized = true
          })
      }
    },

    // 展示 Assign Teacher Groups 弹窗
    showAssignTeacherGroups () {
      this.$refs.assignTeacherGroups.enableTecherGroups = this.teacherGroupEnabled
      this.$refs.assignTeacherGroups.showAssignTeacherGroups(this.currentGroupId, '')
    },

    // 获取班级改编设置信息
    getGroupAdaptSetting () {
      LessonApi.getGroupAdaptSetting(this.currentGroupId)
        .then((res) => {
          if (res) {
            this.teacherGroupEnabled = res.teacherGroupEnabled
            sessionStorage.setItem('enableGroupTeams' + this.currentGroupId, JSON.stringify(this.teacherGroupEnabled))
          }
        })
      this.getGroupTeams()
    },

    // 从 PersonalizeChildList 中获取想要得到的数据
    getAdditionInfo (additionInfo) {
      // 如果 loading 为 false，就设置 personalizeChildListDataLoading 为 false
      if (additionInfo) {
        this.personalizeChildListDataLoading = additionInfo.loading
      }
      // 将 additionInfo 的值赋值给 children
      this.additionInfo = additionInfo
      // 从 additionInfo 中获取 iepButEligibilityIsEmptyChildren
      const iepButEligibilityIsEmptyChildren = additionInfo.iepButEligibilityIsEmptyChildren
      // 如果 iepButEligibilityIsEmptyChildren 存在，并且 length >= 1,那么就展示提示框
      if (iepButEligibilityIsEmptyChildren && iepButEligibilityIsEmptyChildren.length >= 1) {
        this.showIEPButEligibilityIsEmptyChildren = true
      } else {
        this.showIEPButEligibilityIsEmptyChildren = false
      }
      // 如果 childCount 为 0
      if (additionInfo && additionInfo.childCount === 0) {
        // 如果 childCount 为 0，那么就展示 PersonalizeChildList 组件
        this.emptyPageShow = true
      } else {
        this.emptyPageShow = false
      }
    },

    // 获取学校班级
    getGroups () {
      this.loading = true
      this.$axios({
        url: $api.urls(getCurrentUser().user_id).centersAndGroups,
        method: 'get'
      }).then(response => {
        // 如果老师所在的学校数量大于 1，显示 center 选择下拉框
        this.centers = response.filter(x => x.groups.length > 0)
        // 过滤学校班级中离校班级
        for (var i = 0; i < this.centers.length; i++) {
          this.centers[i].groups = this.centers[i].groups.filter(x => !x.inactive)
        }
        this.centers = this.centers.filter(x => x.groups.length > 0)
        // 如果所有班级都为空，直接返回
        if (this.centers.length === 0) {
          this.noGroup = true
          return
        }
        // 如果 props 中存在已改编的班级和学校 ID，则当前的选择的学校班级 ID 直接取 props 即可
        if (this.adaptedCenterId && this.adaptedGroupId && this.hiddenAdaptBtn) {
          this.currentCenterId = this.adaptedCenterId
          this.currentGroupId = this.adaptedGroupId
          // 去除离校班级
          this.groups = this.centers.find(x => x.id === this.adaptedCenterId).groups.filter(x => !x.inactive)
          if (this.groups.length === 0) {
            this.noGroup = true
            return
          }
          this.noGroup = false
        } else {
          this.currentCenterId = this.centers[0].id
          // 去除离校班级
          this.groups = this.centers[0].groups.filter(x => !x.inactive)
          if (this.groups.length === 0) {
            this.noGroup = true
            return
          }
          this.noGroup = false
          this.currentGroupId = this.groups[0].id
        }
      }).catch(error => {
      }).finally(() => {
        this.loading = false
      })
    },

    // 切换学校
    changeCenter (centerId) {
      // 通过学校 Id，切换对应的班级下拉框
      if (centerId) {
        let groups = this.centers.find(x => x.id === centerId).groups
        this.groups = groups.filter(x => !x.inactive)
        this.currentGroupId = groups[0].id
      } else {
        // 找到第一个学校和班级
        this.currentCenterId = this.centers[0].id
        let groups = this.centers[0].groups
        this.groups = groups.filter(x => !x.inactive)
        this.currentGroupId = groups[0].id
      }
    },
    // 弹窗关闭前的回调
    closeAdaptUnitDialog (clearCenterGroup = true) {
      if (clearCenterGroup) {
        this.currentCenterId = ''
        this.currentGroupId = ''
      }
      this.dialogVisible = false
      this.selectedUnit = null
      this.applyToGroup = false
      this.selectUnitPopoverVisable = false
      this.unitLoading = false
      this.units = []
      this.selectedPlans = []
      this.unit = null
      this.checkAll = false
      this.isIndeterminate = false
      this.teacherGroupEnabled = false
      this.pageNum = 1
    }
  }
}
</script>
<style lang="less" scoped>

.center-group-select {
  width: 180px;
}

@media screen and (max-width: 1366px) {

  .center-group-select {
    width: 165px;
  }

  /deep/ .adapt-lesson-dialog {
    margin-top: 5vh !important;
  }
}

/deep/ .adapt-lesson-dialog {
  .el-dialog__body {
    padding: 0px 20px !important;
  }
}

/deep/ .el-table > .table-header > .cell {
  word-break: break-word;
}

.custom-select {
  height: 40px;
  line-height: 40px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-white);
  color: var(--color-text-primary);

  &.active {
    border-color: var(--color-primary);
  }
}

.adapt-unit-list {
  max-height: 300px;
  padding-right: 12px;
}

.adapt-unit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px;
  border-radius: 4px;
  height: 38px;
  line-height: 38px;
  cursor: pointer;
  background: var(--color-white);
  color: var(--color-text-primary);

  &.selected {
    background: var(--color-primary-light) !important;
  }
}

.new-tag {
  background: var(--color-danger);
  color: var(--color-white);
  border-radius: 10px;
  padding: 2px 5px;
  font-size: 12px;
  margin-left: 5px;
}

.adapted-tag {
  background: var(--color-white) !important;
  color: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
}

.child-list {
  max-height: calc(100vh - 330px);
}

.child-list-open {
  height: calc(100vh - 392px);
}

.empty-child-list {
  height: calc(100vh - 450px);
}

</style>

<style lang="less">
.adapt-unit {
  background: var(--color-table-header-background);
  padding-right: 0px;
}

.el-popper.adapt-UDL-and-CLR-guide-color-text {
  background: var(--color-ai-assistant);
  color: #FFFFFF;
  padding: 24px;
  border: none;

  &.el-popper[x-placement^=left] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=bottom] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }

  p {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
  }

  ul {
    padding-left: 24px;
    margin-bottom: 24px;

    li {
      list-style: disc;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
    }
  }

  .el-button {
    //color: var(--color-ai-assistant);
    //background: var(--color-white);
    //border-color: var(--color-ai-assistant);
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
    color: #676879;
  }

  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
  }
}
</style>
