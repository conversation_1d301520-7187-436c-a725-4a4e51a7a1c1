<template>
  <div class="reflection-detail bg-white lg-padding-24">
    <div class="display-flex justify-content-between">
      <!-- 作者和日期 -->
      <div class="display-flex">
        <!-- 头像 -->
        <el-avatar :src="reflection.author.avatarUrl"></el-avatar>
        <div class="m-l-xs">
          <!-- 老师名称 -->
          <div class="">
            {{reflection.author.displayName}}
          </div>
          <!-- 添加时间 -->
          <div>
            {{reflectionTime}}
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="lg-pointer font-size-20">
        <el-dropdown trigger="click" @command="handleCommand" :disabled="!lessonId" v-if="!!userReflection && showEdit">
          <span class="el-dropdown-link">
            <i class="el-icon-more font-size-20"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="EDIT" icon="el-icon-edit">
              <span type="text">{{$t('loc.edit')}}</span>
            </el-dropdown-item>
            <el-dropdown-item command="DELETE" icon="el-icon-delete" class="text-button-danger">
              <span type="text">{{$t('loc.delete')}}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <!-- 反思内容 -->
    <div class="text-black-mon">
      <!-- 课程反思 -->
      <div class="m-t-sm" v-if="reflection.lessonReflection">
        <span class="lg-color-text-primary">{{$t('loc.plan79')}}: </span>
        <span class="space-pre-line lg-color-text-primary">{{reflection.lessonReflection}}</span>
      </div>
      <!-- 小孩反思 -->
      <div class="m-t-sm" v-if="reflection.childReflection">
        <span class="lg-color-text-primary">{{$t('loc.plan80')}}: </span>
        <span class="space-pre-line lg-color-text-primary">{{reflection.childReflection}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'

export default {
  name: 'ReflectionDetail',
  components: { },

  props: {
    reflection: {
      type: Object
    },
    lessonId: {
      type: String
    },
    planId: {
      type: String
    },
    showEdit: {
      type: Boolean
    }
  },

  data () {
    return {
      reflectionTime: ''
    }
  },

  created () {
    this.initData()
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    userReflection () {
      return this.reflection && this.reflection.author && this.reflection.author.id === this.currentUser.user_id
    }
  },

  methods: {
    initData () {
      let updateAtLocal = this.reflection.updateAtLocal
      if (updateAtLocal) {
        let date = this.$moment(updateAtLocal).format('MMM DD, YYYY')
        let time = this.$moment(updateAtLocal).format('hh:mm a')
        this.reflectionTime = date + ' at ' + time
      }
    },

    handleCommand (command) {
      if (command === 'EDIT') {
        this.editReflection()
      } else if (command === 'DELETE') {
        this.deleteReflection()
      }
    },

    editReflection () {
      if (!this.userReflection) {
        return
      }
      this.$router.push({
        name: 'lesson-reflection',
        params: {
          planId: this.planId,
          lessonId: this.lessonId,
          objectId: this.reflection.reflectionObjectId
        }
      })
    },

    deleteReflection () {
      if (!this.userReflection) {
        return
      }
      this.$confirm(this.$t('loc.plan84'), this.$t('loc.cfm'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        LessonApi.deleteLessonReflection({}, {
          itemId: this.reflection.reflectionObjectId
        }).then(response => {
          this.$message({
            type: 'success',
            message: this.$t('loc.delBenchmarkView')
          })
          this.$emit('callDeleteReflection', this.reflection.reflectionObjectId)
        })
      }).catch(() => {
      })
    }
  },

  watch: {
  }
}
</script>

<style lang="less" scoped>
</style>
