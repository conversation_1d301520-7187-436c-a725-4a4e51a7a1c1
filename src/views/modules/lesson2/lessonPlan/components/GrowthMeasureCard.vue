<template>
  <el-card class="box-card" shadow="never" v-loading="loading" v-if="loading || !!domainScoreData && domainScoreData.periodAlias">
    <!-- 标题 -->
    <div class="title-font-18 lg-color-text-primary lg-margin-bottom-12">
      <span class="">{{$t('loc.growthMeasures')}}</span>
      <span class="text-danger m-l-xs">({{!!domainScoreData && domainScoreData.periodAlias}})</span>
    </div>
    <!-- 领域分内容 -->
    <div v-if="!!domainScoreData && domainScoreData.showCoreMeasureOpen" style="margin-bottom: 12px">
      {{ $t('loc.showCoreMeasureOnly') }}
      <el-switch
      v-model="showCore"
      @change="showCoreChange"
      class="m-l-xs"
      size="mini"
      ></el-switch>
    </div>
    <template v-if="!!domainScoreData">
      <div v-for="(measure, index) in measureList" :key="index" >
        <div class="display-flex justify-content-between align-items">
          <!-- 测评点信息 -->
          <div class="measure-item">
            <div class="font-bold line-height-18">{{measure.measureAbbr.toUpperCase()}} <span v-if="measure.core" style="color: red;">*</span></div>
            <div class="overflow-ellipsis line-height-18" :title="measure.measureName">{{measure.measureName}}</div>
          </div>
          <!-- 分数信息 -->
          <div class="overflow-ellipsis font-size-14 text-center level-item" :style="{ background: getDRDPRatingColor(measure.levelName)}" :title="measure.levelName">
            {{measure.levelName}}
          </div>
        </div>
        <el-divider v-if="index != measureList.length - 1" class="m-t-xs m-b-xs"></el-divider>
      </div>
    </template>
  </el-card>
</template>

<script>
import { mapState } from "vuex"
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'

export default {
  name: 'GrowthMeasureCard',
  components: {
  },

  props: {
    groupId: {
      type: String
    },
    domainScoreData: {
      type: Object
    },
    loading: {
      type: Boolean
    }
  },

  data () {
    return {
      showCore: true
    }
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    }),
    // 劣势测评点集合，最多10个
    measureList () {
      let measures = this.domainScoreData.measures || []
      return measures.slice(0, 10)
    }
  },

  methods: {
    getDomainIconName (name) {
      if (name.indexOf('–') != -1) {
        return name.replace('–', '-').toLocaleLowerCase()
      } else if (name.indexOf(':') != -1) {
        return name.split(':')[0].toLocaleLowerCase()
      }
      return name.toLocaleLowerCase()
    },

    getDRDPRatingColor (levelName) {
      return tools.getDRDPRatingColor(levelName)
    },

    /**
     * 更换核心测评点开关,通知父组件刷新数据
     */
    showCoreChange (val) {
      // 根据不同页面和开关值发送不同的埋点事件（仅显示核心测评点开关）
      if (val && this.$route.name == 'edit-plan') {
        this.$analytics.sendEvent('web_weekly_plan_edit_gmd_open_key_meas')
      } else if (!val && this.$route.name == 'edit-plan') {
        this.$analytics.sendEvent('web_weekly_plan_edit_orm_close_key_meas')
      } else if (val && this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_vir_detail_gmd_open_key_meas')
      } else if (!val && this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_vir_detail_gmd_close_key_meas')
      }
      this.$emit('showCoreChange', val)
    }
  }

}
</script>

<style lang="less" scoped>
.domain-icon {
  margin: 0;
  height: 30px;
}
.measure-item {
  flex: auto;
  min-width: 0;
}
.level-item {
  margin: 4px;
  width: 110px;
  flex-grow: 0;
  flex-shrink: 0;
  padding: 0 4px;
}
</style>