<template>
  <el-dialog
    :title="$t('loc.tips')"
    :visible.sync="visible"
    custom-class="lg-el-dialog"
    top="calc(50vh - 283px)"
    width="550px">
    <!-- 提示语 -->
    <div class="m-b-sm submit-plan-tip" v-html="$t('loc.plan95')">
    </div>
    <!-- 实例图片 -->
    <div class="bg-light img-area">
      <el-image
        class="w-full"
        :src="require('@/assets/img/lesson2/plan/submit_tip.png')"
        fit="fit"></el-image>
    </div>
    <span slot="footer" class="dialog-footer">
      <!-- 不再提示 -->
      <div class="pull-left m-t-sm">
        <el-checkbox v-model="hideTip">{{ $t('loc.plan96') }}</el-checkbox>
      </div>
      <!-- 确认按钮 -->
      <el-button type="primary" @click="confirm">{{ $t('loc.gotIt') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SubmitPlanTip',

  props: {},

  data() {
    return {
      visible: false, // 是否显示弹窗
      hideTip: false // 是否隐藏提示
    }
  },

  methods: {
    confirm() {
      this.visible = false
      this.$emit('callConfirmTip', this.hideTip)
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-dialog{
  margin-bottom: calc(50vh - 283px);
}
.img-area {
  padding: 5px 5px 0 5px;
}

.submit-plan-tip /deep/ & {
  ul, li {
    list-style: initial;
    padding: initial;
    margin: initial;
  }

  ul {
    display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0;
    margin-inline-end: 0;
    padding-inline-start: 20px;
    margin-top: 0;
    font-size: 15px;
  }

  li {
    display: list-item;
    text-align: -webkit-match-parent;
  }
}
</style>