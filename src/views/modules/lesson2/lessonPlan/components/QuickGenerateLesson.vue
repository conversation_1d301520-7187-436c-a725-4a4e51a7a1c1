<template>
  <div>
    <!--生成课程弹框-->
    <el-dialog
      :title="$t('loc.addLessonPlan')"
      custom-class="lesson-dialog"
      :fullscreen="true"
      :append-to-body="true"
      :visible.sync="lessonDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleBeforeClose">
      <div class="lg-scrollbar-show h-full" v-if="lessonDialogVisible">
        <lesson-editor ref="lessonEditor"
                       :inWeeklyPlannDialog="true"
                       :selectedFramework="selectedFramework"
                       :showAssistantTool="true"
                       :showAiAssistant="false"
                       :inDialog="false"
                       @callSelectLesson="callSelectLesson"
                       @closeLessonGenerateDialog="closeLessonGenerateDialog"
        ></lesson-editor>
      </div>
    </el-dialog>
    <!--退出校验弹框-->
    <el-dialog
      :title="$t('loc.confirmation')"
      custom-class="check-dialog"
      width="600px"
      :append-to-body="true"
      :visible.sync="checkDialogVisible"
      :close-on-click-modal='false'
    >
      <div class="lg-padding-l-r-16 font-weight-400 font-size-16 line-height-24 text-default add-padding-t-6">
        {{ $t('loc.weekPlanLessonNotSave') }}
      </div>
      <div slot="footer" class="dialog-footer add-padding-t-6">
        <el-button @click="notSave">{{ $t('loc.noSave') }}</el-button>
        <el-button type="primary" @click="save">{{ $t('loc.save') }}</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import LessonEditor from '@/views/modules/lesson2/lessonLibrary/editor/index'

export default {
  name: 'QuickAddLesson',
  components: {
    LessonEditor
  },
  props: {
    // 周计划的框架及年龄数据
    selectedFramework: {
      type: Object,
      default: null
    },
  },
  data () {
    return {
      lessonDialogVisible: false, // 课程生成弹框
      checkDialogVisible: false, // 退出校验弹框
      lessonIsUpdate: false
    }
  },
  computed: {},
  created () {
  },

  watch: {},
  methods: {
    // 关闭课程弹框时回调，判断课程是否更新
    handleBeforeClose (done) {
      // 判断课程内容是否更新
      if (this.$refs.lessonEditor && this.$refs.lessonEditor.isUpdateLesson()) {
        this.$refs.lessonEditor.publishLesson()
      }
      done()
    },

    // 保存周计划课程冒泡方法
    callSelectLesson (LessonDetail) {
      this.$emit('callSelectLesson', LessonDetail)
    },

    // 保存课程
    async save () {
      if (this.$refs.lessonEditor) {
        this.checkDialogVisible = false
        await this.$refs.lessonEditor.publishLesson()
      }
    },

    // 不保存退出
    notSave () {
      this.checkDialogVisible = false
      this.lessonDialogVisible = false
    },

    // 打开添加课程弹窗
    openQuickAddLesson (name, categoryName) {
      this.selectedFramework.categoryName = categoryName
      this.lessonDialogVisible = true
    },

    // 关闭课程弹窗
    closeLessonGenerateDialog () {
      this.lessonDialogVisible = false
    }

  }
}
</script>
<style lang="scss" scoped>
/deep/ .lesson-dialog.el-dialog {
  max-height: calc(100% - 40px) !important;
  height: 100%;
  background-color: var(--color-page-background-white);
  margin-top: 40px !important;
  overflow: hidden;
}

/deep/ .lesson-dialog .el-dialog__header{
  margin-bottom: 20px;
}

/deep/ .el-dialog__body {
  padding: 0 11px;
  height: calc(100% - 80px);
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
  color: var(--color-text-placeholder);
}

/deep/ .el-dialog__footer {
  max-width: 1375px !important;
  margin: 0 auto !important;
}

/deep/ .check-dialog.el-dialog {
  margin-top: 220px !important;
}

@media screen and (max-width: 1535px) {
  /deep/ .lesson-form-name,
  /deep/ .lesson-form-age,
  /deep/ .lesson-form-theme {
    width: 350px !important;
  }

  /deep/ .lesson-form-prepare,
  /deep/ .lesson-form-activity, {
    width: 168px !important;
  }

  /deep/ .media-uploader-lesson-cover {
    width: 450px;
    height: 310px;
    position: relative;
    border: 1px solid transparent;

    .media-uploader {
      width: 450px;
      height: 310px;
      margin-left: -10px;
    }

    .media-uploader-select {
      width: 460px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & > :first-child {
        width: 176px;
        height: 126px;
      }

      & > :nth-child(2) {
        width: 460px;
        color: #676879;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        margin: 12px auto;
      }
    }

    .media-uploader-selected {
      width: 450px;
      height: 310px;
      position: relative;

      & > .media-viewer {
        height: 100%;
      }
    }
  }

  /deep/ .el-form-item {

    &.lesson-form-name, &.lesson-form-age, &.lesson-form-theme, &.lesson-form-framework, &.lesson-form-domain {
      max-width: 455px;
    }

    &.lesson-form-prepare, &.lesson-form-activity {
      max-width: 220px;
    }

    &.lesson-form-activity {
      margin-left: 15px;
    }
  }
}
@media screen and (max-width: 1367px) {
  /deep/ .el-form-item {
    &.lesson-form-cover {
      float: left;
      margin-right: 40px;
      margin-top: 20px;

      .lesson-cover-el-skeleton-item {
        height: 310px;
        width: 450px;
      }
    }
  }
}
</style>
