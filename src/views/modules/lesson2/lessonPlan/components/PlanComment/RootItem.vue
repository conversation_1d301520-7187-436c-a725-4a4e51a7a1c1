<template>
  <comment-item :root="root" :comment="root">
    <sub-comment-list :root="root"/>
  </comment-item>
</template>
<script>
import CommentEditor from "./Editor";
import SubCommentList from "./SubList";
import CommentItem from "./Item";

export default {
  name: 'CommentRootItem',
  components: {
    CommentItem,
    SubCommentList,
    CommentEditor
  },
  props: [
    // 根评论
    'root',
  ]
}
</script>