<!-- 周计划评论 -->
<template>
  <el-card shadow="never" class="plan-comment box-card">
    <!-- 评论标题 -->
    <!-- <div class="clearfix font-size-16 m-b-sm m-t-sm">
      <i class="el-icon-s-comment text-default"/>
      <span class="font-bold m-l-xs text-black-mon">{{ isAdmin ? $t('loc.planCommentTitle') : $t('loc.teacherPlanCommentTitle') }}</span>
    </div> -->
    <div class="lg-scrollbar-show" style="overflow-y: auto;max-height: 360px;padding-right: 20px;padding-top: 10px;">
      <!--发表评论-->
      <comment-editor @comment="commentHandler" class="plan-comment-editor"/>
      <el-divider v-if="roots && roots.length && editorFocus"/>
      <!--评论显示区域-->
      <div v-infinite-scroll="loadPage" :class="[!(roots && roots.length && editorFocus)&&'m-t-sm']">
        <div v-for="(comment,index) in roots" :key="comment.id">
          <!-- 根评论 -->
          <comment-root-item :root="comment"/>
          <!-- 分隔线 -->
          <div v-if="noMore && index === roots.length - 1 && !onePage" class="the-end">
            <el-divider>
           <span>
              {{ $t('loc.lessons2CommentEndTitle') }}
           </span>
            </el-divider>
          </div>
          <el-divider v-else-if="index < roots.length - 1" class="el-divider"></el-divider>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { acrossRole } from '@/utils/common'
import Api from '../../../../../../api/lessons2'
import CommentEditor from './Editor'
import CommentRootItem from './RootItem'
import { mapState } from 'vuex'

export default {
  name: 'PlanComment',
  components: {
    CommentRootItem,
    CommentEditor
  },
  props: [
    'planId'
  ],
  provide () {
    return {
      planId: this.planId
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    isAdmin () {
      return acrossRole('agency_admin', 'agency_owner', 'site_admin')
    }
  },
  data () {
    return {
      roots: [], // 根评论列表
      total: 0, // 评论总数
      rootCommentTotal: 0, // 根评论总数
      pageSize: 10, // 页大小
      pageNum: 1, // 分页信息
      noMore: false, // 加载完成
      loading: false,
      added: 0,// 加载后手动添加的根评论数量
      onePage: false,
      editorFocus: false
    }
  },
  methods: {
    // 编辑器监听函数：submit、cancel
    commentHandler (type, data) {
      if (type === 'submit') {
        let { content, replyTo } = data
        Api.commentPlan({ content, planId: this.planId }).then(comment => {
          this.roots.unshift({
            ...comment,
            userId: this.currentUser.userInfo.id,
            userName: this.currentUser.userInfo.firstName + ' ' + this.currentUser.userInfo.lastName,
            userAvatarURL: this.currentUser.userInfo.avatarUrl,
            replyTo,
            descendants: []
          })
          this.added++
          this.total++
          this.rootCommentTotal++
        })
      }
      if (type === 'focus') {
        this.editorFocus = true
      } else {
        this.editorFocus = false
      }
    },
    // 分页加载根评论列表
    loadPage () {
      if (this.loading || this.noMore) {
        return
      }
      this.loading = true
      let page = this.getPage(this.added + (this.pageNum - 1) * this.pageSize, this.pageSize)
      Api.getPlanComments(this.planId, page.pageSize, page.pageNum)
        .then((response) => {
          if (this.pageNum) {
            this.pageNum++
          }
          // 加载成功，进行页面数据渲染
          this.total = response.totalCommentCount
          this.rootCommentTotal = response.total
          this.roots = this.roots.concat(...response.items.slice(page.from, page.to))
          this.noMore = this.rootCommentTotal <= this.roots.length
          if (this.pageNum === 2 && this.noMore) {
            this.onePage = true
          }
        })
        .finally(() => this.loading = false)
    },
    // 重新计算分页数据 offset 已加载数，size页大小
    getPage (offset, size) {
      let from, to, pageSize, pageNum
      for (pageSize = size; pageSize < 2 * size; pageSize++) {
        if (offset % pageSize + size <= pageSize) {
          break
        }
      }
      from = offset % pageSize
      to = from + size
      pageNum = (offset - from) / pageSize + 1
      return { from, to, pageNum, pageSize }
    }
  },
  created () {
    // 进行评论的初始化显示
    this.loadPage()
  }
}
</script>

<style scoped lang="less">
.el-divider /deep/ {
  margin: 10px 0;

  & .el-divider__text {
    color: #C0C4CC;
  }
}

.the-end {
  margin: 0 auto
}

.plan-comment {
  background-color: #fff;

  & > .plan-comment-title {
    margin: 18px 0;
  }

  & > .plan-comment-editor {
    margin-bottom: 21px;
  }
}

//  提示语样式
/deep/ .el-textarea__inner::-moz-placeholder {
  color: #777;
}

/deep/ .el-textarea__inner::-webkit-input-placeholder {
  color: #777;
}
.el-card {
  border: none;
  border-radius:0;
}
.el-card /deep/ .el-card__body {
  padding-right: 0 !important;
  padding-top: 0 !important;
}
</style>
