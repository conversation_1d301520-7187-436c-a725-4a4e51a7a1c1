<template>
  <div class="comment-item">
    <el-avatar :size="30" :src="userAvatarURL" shape="circle" class="m-r-sm"></el-avatar>
    <div>
      <div class="pull-right" v-if="!deleted">
        <!-- 回复按钮 -->
        <el-tooltip :content="$t('loc.lessons2Reply')">
          <el-button v-if="showReply" @click="showEditor = true;" icon="el-icon-chat-dot-round" size="small" type="text"/>
        </el-tooltip>
        <!-- 删除按钮 -->
        <el-tooltip :content="$t('loc.delete')">
          <el-button v-if="showDelete" @click="deleteComment" icon="el-icon-delete" size="small" type="text"
                     style="color:#909399"/>
        </el-tooltip>
      </div>
      <!-- 评论人名称 -->
      <div>{{ comment.userName | formatUserName}}</div>
      <!-- 创建时间 -->
      <div class="small font-color-gray"> {{ createTime }}</div>
      <div class="word-break">
          <span v-if="comment.replyTo && !deleted">
            @{{ comment.replyTo.userName }}:
          </span>
        <!-- 评论内容 -->
        <span v-html="content" :class="[deleted && 'font-color-gray']"/>
      </div>
      <!-- 评论输入框 -->
      <comment-editor :reply-to="comment" @comment="commentHandler" v-if="showEditor" ref="comment-item-editor"/>
      <slot/>
    </div>
  </div>
</template>
<script>
import {mapState} from "vuex";
import CommentEditor from "./Editor";
import Api from "../../../../../../api/lessons2";
import Constants from "../../../../../../utils/constants";

export default {
  name: 'CommentItem',
  components: {
    CommentEditor
  },
  props: [
    // 根评论
    'root',
    // 当前评论
    'comment',
  ],
  inject: ['planId'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    }),
    // 评论时间
    createTime() {
      return this.$moment(this.comment.createAtUtc)
        .format("MMM DD, YYYY");
    },
    // 是否展示删除按钮，仅评论人自己可以删除自己的评论
    showDelete() {
      return this.currentUser.userInfo.id === this.comment.userId;
    },
    // 是否展示回复按钮，不能自己回复自己的评论
    showReply() {
      return this.currentUser.userInfo.id !== this.comment.userId;
    },
    // 评论人头像
    userAvatarURL() {
      return this.comment.userAvatarURL || Constants.userAvatarURL;
    },
    // 评论删除标识
    deleted: {
      get() {
        return this.comment.deleted
      },
      set(value) {
        this.comment.deleted = value;
      }
    },
    // 评论内容
    content: {
      get() {
        this.comment.content = this.comment.content.trim()
        return this.comment.content.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ')
      },
      set(value) {
        this.comment.content = value;
      }
    }
  },
  data() {
    return {
      showEditor: false
    }
  },
  watch: {
    showEditor(value) {
      value && this.$refs['comment-item-editor'] && this.$refs['comment-item-editor'].focus();
    }
  },
  methods: {
    // 编辑器监听函数：success、fail、cancel
    commentHandler(type, data) {
      if (type !== 'focus') {
        this.showEditor = false;
      }
      if (type === 'submit') {
        let {content, replyTo} = data;
        Api.commentPlan({content, planId: this.planId, parentCommentId: replyTo.id})
          .then(comment => {
            this.root.descendants.unshift({
              ...comment,
              userId: this.currentUser.userInfo.id,
              userName: this.currentUser.userInfo.firstName + ' ' + this.currentUser.userInfo.lastName,
              userAvatarURL: this.currentUser.userInfo.avatarUrl,
              replyTo,
              descendants: []
            })
          });
      }
    },
    // 删除评论
    deleteComment() {
      const h = this.$createElement
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.commentDeleteTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            Api.deletePlanComment(this.comment.id)
              .then(() => {
                done()
                this.deleted = true;
                this.content = this.$t('loc.commentDeleteShow');
                this.$emit('delete', this.comment)
              })
          } else {
            done()
          }
        }
      })
    },
  }
}
</script>
<style scoped lang="less">
.comment-item {
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  overflow-wrap: break-word;

  & > :first-child {
    flex: none;
  }

  & > :last-child {
    flex: auto;
    margin-top: 3px;
  }
}

.pull-right > * {
  position: relative;
  top: -6px;
}
</style>