<!-- 发表评论 -->
<template>
  <div class="comment-editor">
    <el-input type="textarea" class="lg-scrollbar-show" :autosize="{minRows:3}" v-model="comment.content" ref="comment-editor-inner"
              @focus="handleFocus(true)" :placeholder="replyTo?`@${ replyTo.userName }`:(isAdmin? $t('loc.adminPlanCommentPlaceholder') : $t('loc.planCommentPlaceholder'))" maxlength="1000"/>
    <!--发表按钮及取消按钮-->
    <div v-show="showOperation" class="comment-editor-operation">
      <el-button size="small" @click="cancelCommentHandler" plain>
        {{ $t('loc.cancel') }}
      </el-button>
      <el-button size="small" @click="submitCommentHandler" type="primary"
                 :disabled="!comment.content || !comment.content.trim()">
        {{ $t('loc.submit') }}
      </el-button>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { acrossRole } from '@/utils/common'

export default {
  name: 'CommentEditor',
  props: ['replyTo'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    isAdmin () {
      return acrossRole('agency_admin', 'agency_owner', 'site_admin')
    }
  },
  data () {
    return {
      comment: {
        content: ''
      },
      showOperation: false
    }
  },
  methods: {
    submitCommentHandler () {
      if (this.$route.fullPath.indexOf('create-virtual-shadow-list/view') !== -1) {
        this.$analytics.sendEvent('web_weekly_virtual_detail_submit_comment')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_detail_submit_comment')
      }
      this.$emit('comment', 'submit', { content: this.comment.content, replyTo: this.replyTo })
      this.comment.content = ''
      this.showOperation = false
    },
    cancelCommentHandler () {
      this.$emit('comment', 'cancel', { replyTo: this.replyTo })
      this.comment.content = ''
      this.showOperation = false
    },
    focus () {
      let ref = this.$refs['comment-editor-inner']
      ref && ref.focus()
    },
    handleFocus (focus) {
      this.showOperation = focus
      this.$emit('comment', 'focus', focus)
    }
  }
}
</script>
<style scoped lang="less">
.comment-editor {
  color: #999999;
  font-size: 14px;
}

.comment-editor-operation {
  text-align: right;
  margin-top: 13px;

  & > * {
    border: none;
  }
}
</style>
