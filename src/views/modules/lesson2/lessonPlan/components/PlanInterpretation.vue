<template>
  <div style="display: flex; align-items: center; justify-content: end;"
       :class="[introducing &&'plan-interpretation-guiding introjs-showElement']">
    <!-- 添加讲解 -->
    <template v-if="!existInterpretation && canAdd">
      <!-- 开始录音按钮-->
      <el-popover
        trigger="manual"
        :append-to-body="false"
        width="550px"
        popper-class="plan-interpretation-guide-popover"
        placement="bottom-end"
        v-model="guideVisible">
        <!-- 用户引导内容 -->
        <p>{{ $t('loc.plan127') }}</p>
        <ul>
          <li>{{ $t('loc.plan128') }}</li>
          <li>{{ $t('loc.plan129') }}</li>
        </ul>
        <!-- 关闭引导按钮 -->
        <div style="text-align: right; margin: 0">
          <el-button slot="reference" @click="endGuide">{{ $t('loc.plan130') }}</el-button>
        </div>
        <!-- 录制按钮 -->
        <el-button slot="reference" style="position: relative;" v-show="status==null" :loading="loading" size="medium" type="primary" icon="el-icon-video-camera" @click="openVoiceSourceDialog()">
        {{ $t('loc.plan121') }}
        <span v-show="showNewBadge" class="new-feature-badge">New</span>
        </el-button>
      </el-popover>
    </template>
      <!-- 展示-->
    <template v-if="existInterpretation && status == null">
      <!-- 录音播放器 -->
      <div v-if="interpretation.media.type === 'mp3'" class="display-flex align-items">
        <lg-audio @play="handlePlay($event)" :src="interpretation.media.url"/>
        <i class="el-icon-close" @click="deleteInterpretation" v-if="canRemove"/>
      </div>
      <div v-else-if="canAdd || canRemove" style="position: relative; white-space: nowrap;">
        <span v-show="showNewBadge" class="new-feature-badge">New</span>
        <el-dropdown class="plan-dropdown" split-button type="primary" size="medium" trigger="click" @click="viewInterpretation"  @command="handleCommand">
          <i class="el-icon-view m-r-xs" style="font-size: 13px;"></i>{{ $t('loc.plan131') }}
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="canAdd" command="add">{{ $t('loc.plan132') }}</el-dropdown-item>
            <el-dropdown-item v-if="canRemove" command="delete">{{ $t('loc.plan133') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div v-else>
        <el-button icon="el-icon-view" style="position: relative;" type="primary" size="medium"  @click="viewInterpretation">
          {{ $t('loc.plan131') }}
          <span v-show="showNewBadge" class="new-feature-badge">New</span>
        </el-button>
      </div>
    </template>
    <div v-if="status != null && !previewDialogVisible" class="recorder-box">
      <div class="recorder-btn-box">
        <!-- 自动开始，不需要开始按钮 -->
        <button v-if="status === 0" class="recorder-btn" @click="startRecorder()">REC</button>
        <button v-if="status === 1" class="recorder-btn" @click="stopScreenRecording()">Stop</button>
      </div>
      <!-- 计时器 -->
      <div v-if="timer" class="recorder-timmer">{{ duration }}</div>
    </div>
    <!-- 引导-->
    <div style="display: none">
      <div ref="intro">
        <el-container>
          <el-main>
            {{ $t('loc.planInterpretIntroTips') }}
            <img src="@/assets/img/lesson2/plan/guide_note.jpg" alt=""/>
          </el-main>
          <el-footer>
            <div class="pull-right">
              <el-button type="primary" onclick="planInterpretation_handleGuideOKClick()">OK</el-button>
            </div>
          </el-footer>
        </el-container>
      </div>
    </div>
    <!-- 讲解录制视频预览弹窗 -->
    <el-dialog
      append-to-body
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      custom-class="preview-dialog"
      :top="clientWidth > 1500 ? '12vh':'10vh'"
      :title="$t('loc.plan144')"
      :visible="previewDialogVisible"
      :before-close="closePreviewDialog">
      <div>
        <!-- 根据大小屏幕自动调整video大小 -->
        <div class="video-wrapper" :style="{height: clientWidth > 1500 ? '500px' : '300px'}">
          <!-- 预览视频 -->
          <video :src="videoSrc" controls controlslist="nodownload noremoteplayback noplaybackrate" disablePictureInPicture style="overflow: hidden; width: 100%; height: 100%;"></video>
        </div>
        <el-progress text-inside :stroke-width="16" v-if="showProgress" :percentage="progressRate"></el-progress>
      </div>
      <!-- 新录制的视频，显示取消和保存按钮 -->
      <span v-if="newVideo" slot="footer">
        <el-button :disabled="showProgress" @click="closePreviewDialog">{{ $t('loc.cancel') }}</el-button>
        <el-button :loading="showProgress" type="primary" @click="save">{{ $t('loc.save') }}</el-button>
      </span>
      <!-- 预览显示关闭按钮 -->
      <span v-else slot="footer">
        <el-button @click="closePreviewDialog">{{ $t('loc.close') }}</el-button>
      </span>
    </el-dialog>
    <!-- 录制声源选择 -->
    <el-dialog
      append-to-body
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      custom-class="voice-source-dialog"
      :title="$t('loc.plan122')"
      :visible="voiceSourceDialogVisible"
      :before-close="closeVoiceSourceDialog">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-radio v-model="voiceSource" label="ALL" border>
            <div>
              {{ $t('loc.plan123') }}
            </div>
          </el-radio>
        </el-col>
        <el-col :span="12">
          <el-radio v-model="voiceSource" label="MIC" border>
            <div>
              {{ $t('loc.plan124') }}
            </div>
          </el-radio>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeVoiceSourceDialog">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="openNewTabForRecording">{{ $t('loc.confirm') }}</el-button>
      </span>
    </el-dialog>
    <!-- 视频辅助元素 -->
    <div style="display: none" ref="videoHelper">
      <video crossorigin="anonymous" muted autoplay/>
      <canvas/>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
// import '@/assets/js/recorder/recorder.mp3.min.js'
// import '@/assets/js/recorder/recorder-core.js'
// import '@/assets/js/recorder/engine/mp3'
// import '@/assets/js/recorder/extensions/waveview.js'
import LgAudio from '@/components/LgAudio.vue'
import cameraIcon from '@/assets/img/lesson2/plan/camera.png'
import fileUtil from '@/utils/file'
import tools from '@/utils/tools'
import LessonApi from '@/api/lessons2'
import media from '@/api/lessons2/media'
import * as EBML from 'ts-ebml'

export default {
  name: 'PlanInterpretation',
  components: {
    LgAudio
  },
  props: [
    'planId',
    'teacherIds',
    'isShared',
    'canAdd',
    'canRemove',
    'canManage'
  ],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    isAdmin () {
      if (!this.currentUser) {
        return false
      }
      let role = this.currentUser.role
      return role && role.toUpperCase() === 'OWNER'
    },
    // 当前登录人是否可对讲解进行管理
    manageable () {
      let isManager = this.isAdmin || (this.teacherIds && this.teacherIds.includes(this.currentUser.user_id))
      return !this.isShared &&
        (!this.existInterpretation && isManager ||
          this.existInterpretation && this.interpretation.createUserId === this.currentUser.user_id || this.canRemove || this.canManage)
    },
    existInterpretation () {
      return this.interpretation && this.interpretation.id
    },
    previewPlan () {
      let previewPlanRouteNames = ['view-plan', 'view-template']
      let routeName = this.$route.name
      return previewPlanRouteNames.includes(routeName)
    }
  },
  data () {
    return {
      guided: true,
      introducing: 0,
      intro: null,
      interpretation: {
        id: null,
        createUserId: null,
        media: null
      },
      recorder: {
        recorderStatus: 0, // 录制状态：0未录制，1录制中，2录制完成
        stream: null, // 设备媒体流
        audioContext: null,// 音频上下文
        sourceNode: null, // 源音频节点
        instance: null, // recorder-js插件
        time: 0,// 当前采样时间
        sample: 0, // 当前采样值
        timer: null
      },
      loading: false, // 加载中
      voiceSource: 'ALL', // 声源
      voiceSourceDialogVisible: false, // 声源选择弹窗
      status: null, // 录制状态
      screenRecorder: null, // 屏幕录制对象
      stream: null, // 录制流
      duration: '00:00:00', // 录制时长
      videoDuration: 0, // 视频时长
      startTime: null, // 开始录制时间
      timer: null, // 录制计时器
      videoSrc: '', // 视频地址
      file: null, // 视频文件
      previewDialogVisible: false, // 预览弹窗
      newVideo: false, // 是否为录制的视频
      newRecording: false, // 是否重新录制
      showProgress: false, // 是否显示进度条
      progressRate: 0, // 上传进度条进度
      guideVisible: false, // 引导 popover 是否显示
      showNewBadge: false, // 是否新功能标记
      uploadTask: null, // 上传任务,
      clientWidth: document.body.clientWidth, // 监听窗口的宽度
      streamClosed: false // 流是否已关闭
    }
  },
  created () {
  },
  destroyed () {
    // 如果正在录制，销毁recorder
    if (this.screenRecorder) {
      this.$store.dispatch('setPlanRecording', false)
      this.screenRecorder.stop()
    }
    window.removeEventListener('beforeunload', this.beforeunload)
  },
  mounted () {
    // 添加监听事件，用于关闭页面的提示
    window.addEventListener('beforeunload', this.beforeunload)
    // 监听窗口的大小，决定dialog的位置
    const that = this
    window.addEventListener('resize', () => {
      that.clientWidth = document.body.clientWidth
    })
  },
  methods: {
    beforeunload (e) {
      // 如果正在录制或者已经录制完成未保存，关闭页面前提示
      if (this.status === 1 || this.file) {
        e = e || window.event
        e.preventDefault()
        e.returnValue = ''
      }
    },
    // 播放录音
    handlePlay (e) {
      // 暂停其他播放
      let previous = window.planInterpretationAudio
      if (previous && previous !== e.target) {
        previous.pause()
      }
      window.planInterpretationAudio = e.target
    },

    async startRecorder () {
      this.removeUrlParam()
      if (this.existInterpretation && !this.newRecording) {
        this.$message.error("can't start recording, a record already exist")
        return
      }
      // 录音请求
      var audioStream = await navigator.mediaDevices.getUserMedia({ audio: true })
      .catch(e => {
        return null
      })
      // 未授权录音权限，提示并退出
      if (!audioStream) {
        await this.$alert(this.$t('loc.plan140', { 'icon': '<img src="' + cameraIcon + '" />' }), this.$t('loc.plan139'), {
          dangerouslyUseHTMLString: true,
          showConfirmButton: false,
          showCancelButton: true,
          cancelButtonText: this.$t('loc.close')
        }).finally(() => {
        })
      }
      // 录屏请求
      let systemVoice = true
      if (this.voiceSource == 'MIC') {
        systemVoice = false
        this.voiceSource = 'ALL'
      }
      var videoStream = await navigator.mediaDevices.getDisplayMedia({ video: true, audio: systemVoice, preferCurrentTab: true, surfaceSwitching: 'exclude', selfBrowserSurface: 'include' })
      .catch(e => {
        return null
      })
      // 取消分享屏幕，提示并退出
      if (!videoStream) {
        // 关闭流
        audioStream.getTracks().forEach((track) => {
          track.stop()
        })
        return
      }
      // 录制新视频
      this.newVideo = true
      let _this = this
      var tracks = []
      // 如果video流中有声音流，合并音频流
      if (videoStream.getAudioTracks().length > 0) {
        const audioContext = new AudioContext()
        const displayAudioSource = audioContext.createMediaStreamSource(videoStream)
        const micAudioSource = audioContext.createMediaStreamSource(audioStream)
        const dest = audioContext.createMediaStreamDestination()
        displayAudioSource.connect(dest)
        micAudioSource.connect(dest)
        const mixedAudioStream = dest.stream
        mixedAudioStream.getAudioTracks().forEach(t => tracks.push(t))
        videoStream.getAudioTracks().forEach(t => tracks.push(t))
      } else {
        audioStream.getAudioTracks().forEach(t => tracks.push(t))
      }
      // 视频流
      videoStream.getVideoTracks().forEach(t => tracks.push(t))
      // mic音频流
      this.stream = new MediaStream(tracks)
      // 监听流关闭事件
      this.streamClosed = false
      this.stream.getTracks().forEach(t => {
        t.onended = function () {
          // 如果还没开始录制就结束了分享，直接关闭
          if (_this.screenRecorder.state == 'inactive') {
            _this.streamClosed = true
          } else {
            // 正在录制时，结束录制
            _this.screenRecorder.stop()
          }
        }
      })
      this.screenRecorder = new MediaRecorder(this.stream)
      // 开始录制事件
      this.screenRecorder.onstart = function () {
        // 开始录制前检查是否有关闭的流，有则关闭
        _this.stream.getTracks().forEach((track) => {
          if (track.readyState == 'ended') {
            // 关闭计时器
            _this.hideRecorderTimmer()
            // 关闭流
            _this.stream.getTracks().forEach((track) => {
              track.stop()
            })
            _this.stream = null
            _this.screenRecorder = null
          }
        })
        if (_this.screenRecorder.state === 'recording') {
          // 显示计时器
          _this.showRecorderTimmer()
        }
      }
      // 录制数据事件
      var data = []
      this.screenRecorder.ondataavailable = function (e) {
        data.push(e.data)
      }
      // 停止录制事件
      this.screenRecorder.onstop = function () {
        // 关闭计时器
        _this.hideRecorderTimmer()
        // 关闭流
        audioStream.getTracks().forEach((track) => {
          track.stop()
        })
        _this.stream.getTracks().forEach((track) => {
          track.stop()
        })
        _this.stream = null
        _this.screenRecorder = null
        let fileType = 'video/webm;codecs:vp8'
        // 生成blob
        var blob = new Blob(data, { type: fileType })
        _this.getSeekableBlob(blob)
        .then(res => {
          let file = new File([res], 'interpretation.webm', { type: fileType })
          _this.file = file
          // 生成预览地址
          _this.videoSrc = window.URL.createObjectURL(res)
          // 显示预览弹窗
          _this.newVideo = true
          _this.previewDialogVisible = true
        })
      }
      // 根据选择录制的类型给出提示
      // browser window monitor
      if (videoStream.getVideoTracks()[0].getSettings().displaySurface === 'window') {
        this.$confirm(this.$t('loc.plan136'), this.$t('loc.plan135'), {
          cancelButtonText: this.$t('loc.close'),
          showConfirmButton: false,
          showCancelButton: true,
          customClass: 'custom-message-box'
        }).finally(() => {
          // 如果流已经关闭，直接返回，不再开始录制
          if (this.streamClosed) {
            return
          }
          // elementUI messageBox 关闭动画为300ms，延迟350ms开始录制
          setTimeout(() => {
            this.screenRecorder.start(100)
          }, 350)
        })
      } else {
        // 选择录制窗口弹窗关闭后开始录制，延迟350ms开始录制
        setTimeout(() => {
          this.screenRecorder.start(100)
        }, 350)
      }
    },

    // 生成可seek的blob
    getSeekableBlob (inputBlob) {
      let isFirefox = tools.isFirefox()
      return new Promise((resolve, reject) => {
        // firefox不需要处理
        if (isFirefox) {
          resolve(inputBlob)
        }
        var _this = this
        var reader = new EBML.Reader()
        var decoder = new EBML.Decoder()
        var tools = EBML.tools
        var fileReader = new FileReader()
        fileReader.onload = function () {
          var ebmlElms = decoder.decode(this.result)
          ebmlElms.forEach((element) => {
            reader.read(element)
          })
          reader.stop()
          var refinedMetadataBuf = tools.makeMetadataSeekable(reader.metadatas, _this.videoDuration * 1000, reader.cues)
          var body = this.result.slice(reader.metadataSize)
          var newBlob = new Blob([refinedMetadataBuf, body], {
            type: 'video/webm'
          })
          resolve(newBlob)
        }
        fileReader.readAsArrayBuffer(inputBlob)
      })
    },

    // 获取周计划讲解
    listInterprets () {
      if (this.planId) {
        this.loading = true
        return LessonApi.listInterprets(this.planId)
          .then(response => {
            this.loading = false
            let { guided, showNewBadge, interpretations = [] } = response || {}
            this.guided = guided
            this.showNewBadge = showNewBadge
            this.interpretation = interpretations[0]
            this.$emit('haveInterpretation', this.interpretation !== undefined)
          })
      }
    },
    // 删除周计划讲解
    deleteInterpretation () {
      this.$confirm(this.$t('loc.plan126'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        customClass: 'custom-message-box'
      }).then(() => {
        // 根据不同页面发送不同页面的埋点事件（删除周计划讲解）
        if (this.$route.name == 'view-plan') {
          this.$analytics.sendEvent('web_weekly_plan_detail_del_interpre_cfm')
        } else if (this.$route.name == 'shared-plan-detail') {
          this.$analytics.sendEvent('web_weekly_vir_detail_del_interpre_cfm')
        }
        LessonApi.deleteInterpret(this.interpretation.id)
        .then(response => {
          this.$message.success(this.$t('loc.deletedSuccessfully').toString())
          this.interpretation = null
          this.$emit('haveInterpretation', false)
          this.$nextTick(() => {
            if (!this.guided && !this.existInterpretation && this.previewPlan) {
              this.guideVisible = true
            }
          })
        })
        .catch(error => {
          let message = 'failed to delete interpretation'
          error && error.message(message + ',reason：' + error.message)
          this.$message.error(message)
        })
      })
    },
    // 结束引导
    endGuide () {
      if (this.guideVisible) {
        this.guideVisible = false
        this.guided = true
        LessonApi.setIntroduced()
        this.$store.dispatch('setPlanInterpretationGuide', true)
      }
    },
    isInViewPort (element) {
      if (!element) {
        return false
      }
      const viewWidth = window.innerWidth || document.documentElement.clientWidth
      const viewHeight = window.innerHeight || document.documentElement.clientHeight
      const {
        top,
        right,
        bottom,
        left
      } = element.getBoundingClientRect()

      return (
        top >= 0 &&
        left >= 0 &&
        right <= viewWidth &&
        bottom <= viewHeight
      )
    },
    // 隐藏new标记
    hideNewBadge () {
      if (this.showNewBadge) {
        this.showNewBadge = false
        // 调接口隐藏new标记
        LessonApi.hideNewBadge()
      }
    },
    // 处理删除或录制新的讲解
    handleCommand (command) {
      this.hideNewBadge()
      // 删除
      if (command === 'delete') {
        this.deleteInterpretation()
      } else {
        if (!this.checkSupport()) {
          return
        }
        // 录制新的讲解
        this.$confirm(this.$t('loc.plan137'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          customClass: 'custom-message-box'
        }).then(() => {
          // 先删除原先的讲解，再开始录制新的讲解
          this.newRecording = true
          this.openVoiceSourceDialog()
        })
      }
    },
    // 关闭声源选择弹窗
    closeVoiceSourceDialog () {
      this.voiceSourceDialogVisible = false
      this.voiceSource = 'ALL'
    },
    // 打开声源选择弹窗
    openVoiceSourceDialog () {
      if (this.$route.fullPath.indexOf('create-virtual-shadow-list/view') !== -1) {
        this.$analytics.sendEvent('web_weekly_virtual_detail_add_interpre')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_detail_add_interpre')
      }
      this.hideNewBadge()
      this.endGuide()
      // Safaril浏览器或者iPad不支持录制
      if (!this.checkSupport()) {
        return
      }
      // firefox 只能录制麦克风声音，不进行选择音频操作，直接开始录制
      if (tools.isFirefox()) {
        this.status = 0
        return
      }
      this.voiceSourceDialogVisible = true
    },
    // 检查是否支持录制
    checkSupport () {
      // Safaril浏览器或者iPad不支持录制
      if (tools.isComeFromIPad()) {
        this.$message.error(this.$t('loc.plan134'))
        return false
      }
      if (tools.isSafari()) {
        this.$message.error(this.$t('loc.plan143'))
        return false
      }
      // mac 版本的firefox不支持录制
      if (tools.isFirefox() && tools.isMacOS()) {
        this.$message.error(this.$t('loc.plan143'))
        return false
      }
      return true
    },
    // 打开新窗口录制
    openNewTabForRecording () {
      new Promise((resolve, reject) => {
        this.voiceSourceDialogVisible = false
        setTimeout(() => {
          resolve(tools.isChrome())
        }, 200)
      }).then(res => {
        // 谷歌浏览器打开新标签页录制
        if (res) {
          // // 判断url中是否有其他参数，如果有则在后面加上&，如果没有则在后面加上?
          // let url = window.location.href
          // let connector = url.indexOf('?') > -1 ? '&' : '?'
          // // 复制当前窗口
          // let newUrl = url + connector + 'voiceSource=' + this.voiceSource + '&new=' + this.newRecording
          // window.open(newUrl, '_blank')
          // this.voiceSource = 'ALL'
          this.startRecorder()
        } else {
          this.status = 0
        }
      })
    },
    // 停止录制
    stopScreenRecording () {
      this.screenRecorder.stop()
    },
    // 保存视频
    async save () {
      // 点击保存按钮后先把进度条显示出来，按钮开始 loading，防止用户重复点击
      this.showProgress = true
      if (!this.newRecording) {
        await this.listInterprets().then(() => {
          if (this.interpretation) {
            // 隐藏进度条，提示已经存在讲解
            this.showProgress = false
            this.previewDialogVisible = false
            this.$message.error(this.$t('loc.plan141'))
          }
        })
      }
      // 如果弹窗已经关闭，不再执行保存操作，隐藏进度条
      if (!this.previewDialogVisible) {
        this.showProgress = false
        return
      }
      let _this = this
      let file = this.file
      // 如果视频文件为空，提示上传失败，隐藏进度条
      if (!file) {
        this.showProgress = false
        this.$message.error(this.$t('loc.lessons2UploadFailed'))
        return
      }
      if (this.$route.name == 'view-plan') {
        this.$analytics.sendEvent('web_weekly_plan_detail_add_interpre_save')
      } else if (this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_virtual_detail_add_interpre_save')
      }
      fileUtil.getVideoInfo(file, this.$refs.videoHelper)
      this.uploadTask = fileUtil.uploadToS3(file, {
        onUploadProgress: evt => {
          // 处理上传进度
          let progressRate = parseInt((evt.loaded * 100) / evt.total)
          // 上传完成之后需要请求后端接口生成记录及缩略图，因此进度置为99%
          if (progressRate === 100) {
            progressRate -= 1
          }
          this.progressRate = progressRate
        },
        completeHandler: (err, data) => {
          if (err || !data || !data.Location) {
            // 不是主动中断的，则提示上传失败
            if (err.toString().indexOf('RequestAbortedError') === -1) {
              // 上传失败
              _this.$message.error(this.$t('loc.lessons2UploadFailed'))
              // 关闭进度条
              _this.showProgress = false
              // 进度清零
              _this.progressRate = 0
            }
            return
          }
          // 调用接口创建引导视频的封面缩略图
          media.createMediaWithThumbnail({
            type: 'mp4',
            base64_snapshot_file: file.base64Str.substring(file.base64Str.indexOf('base64,') + 7),
            height: file.height,
            width: file.width,
            key: data.Key,
            fileName: file.name,
            size: file.size,
            duration: _this.videoDuration,
            mediaSource: 'lesson'
          }).then(res => {
            // 删除原先的引导
            LessonApi.listInterprets(_this.planId)
            .then(response => {
              this.loading = false
              let interpretations = response.interpretations
              if (interpretations[0]) {
                LessonApi.deleteInterpret(interpretations[0].id)
              }
              LessonApi.addInterpret({ planId: _this.planId, mediaId: res.id })
              .then(response => {
                _this.file = null
                _this.$message.success(_this.$t('loc.saveSuccess'))
                // 获取新的引导
                _this.listInterprets()
                // 关闭预览弹窗
                _this.previewDialogVisible = false
              })
              .catch(error => {
                _this.file = null
                let message = 'failed to add interpretation'
                error && error.message(message + ',reason：' + error.message)
                this.$message.error(message)
                // 关闭预览弹窗
                _this.previewDialogVisible = false
              })
            })
            // 调用接口添加引导
          }).catch(() => {
            // 创建失败
            this.$message.error(this.$t('loc.lessons2UploadFailed'))
          }).finally(() => {
            if (_this.progressRate == 99) {
              // 关闭进度条
              _this.showProgress = false
              // 进度清零
              _this.progressRate = 0
            }
          })
        }
      })
    },
    // 显示计时器
    showRecorderTimmer () {
      this.status = 1
      this.startTime = new Date().getTime()
      this.timer = setInterval(() => {
        var time = new Date().getTime() - this.startTime
        // 计算时分秒
        // 小时
        var hours = Math.floor(time / 1000 / 60 / 60)
        // 分钟
        var minutes = Math.floor(time / 1000 / 60) - (hours * 60)
        // 秒
        var seconds = Math.floor(time / 1000) - (hours * 60 * 60) - (minutes * 60)
        // 时分秒不足10 补 0
        this.duration = (hours > 9 ? hours : '0' + hours) + ':' + (minutes > 9 ? minutes : '0' + minutes) + ':' + (seconds > 9 ? seconds : '0' + seconds)
      }, 1000)
    },
    // 隐藏计时器
    hideRecorderTimmer () {
      // 结束时间
      let endTime = new Date().getTime()
      // 计算录制时长
      this.videoDuration = (endTime - this.startTime) / 1000
      this.startTime = null
      clearInterval(this.timer)
      this.duration = '00:00:00'
      this.status = null
    },
    removeUrlParam () {
      // 除路由参数
      // this.$router.query = {}
      let newQuery = JSON.parse(JSON.stringify(this.$route.query)) // 深拷贝
      delete newQuery.new
      delete newQuery.voiceSource
      this.$router.replace({ query: newQuery })
    },
    // 显示预览弹窗
    viewInterpretation () {
      this.$analytics.sendEvent('web_weekly_virtual_detail_view_interpre')
      this.hideNewBadge()
      this.newVideo = false
      this.videoSrc = this.interpretation.media.url
      this.previewDialogVisible = true
    },
    // 关闭预览弹窗
    closePreviewDialog () {
      // 如果是新录制的视频，需要提示是否保存
      if (this.newVideo) {
        this.$confirm(this.$t('loc.plan125'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger',
          customClass: 'custom-message-box'
        }).then(() => {
          // 如果有正在上传的视频，则取消上传
          if (this.uploadTask) {
            this.uploadTask.abort()
            this.uploadTask = null
            this.showProgress = false
          }
          // 如果正在录制，则停止录制关闭所有流
          if (this.status == 1) {
            // 关闭计时器
            this.hideRecorderTimmer()
            // 关闭流
            this.stream.getTracks().forEach((track) => {
              track.stop()
            })
            this.stream = null
            this.screenRecorder = null
          }
          this.previewDialogVisible = false
          this.videoSrc = ''
          this.file = null
        })
      } else {
        this.previewDialogVisible = false
        this.videoSrc = ''
        this.file = null
      }
    }
  },
  watch: {
    planId: {
      immediate: true,
      handler (val) {
        if (val) {
          this.listInterprets().then(() => {
            this.$nextTick(() => {
              // 没有引导过、没有引导视频、且在周计划的预览页，进行引导
              if (!this.guided && !this.existInterpretation && this.previewPlan) {
                this.guideVisible = true
              } else {
                this.$store.dispatch('setPlanInterpretationGuide', true)
              }
            })
          })
        }
      }
    },
    // 监听录制状态，设置全局变量值
    status (val) {
      if (val && val == 1) {
        this.$store.dispatch('setPlanRecording', true)
      } else {
        this.$store.dispatch('setPlanRecording', false)
      }
    }
  }
}
</script>

<style lang="less" scoped>
@text-color-mian: #323338;

.video-wrapper {
  background: #000000;
  max-width: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  max-height: 500px;
  video::-webkit-media-controls-fullscreen-button {
    display: block !important;
  }
  video::-webkit-media-controls-volume-slider {
    display: block !important;
    color:#F56C6C;
  }
  video::-webkit-media-controls-mute-button {
    display: block !important;
  }
}
.new-feature-badge {
  z-index: 2;
  position: absolute;
  right: -10px;
  top: -10px;
  color: #FFFFFF;
  padding: 2px 5px;
  border-radius: 8px;
  background: #F56C6C;
  font-size: 12px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
}
/deep/.voice-source-dialog {
  width: 450px;
  .el-dialog__body {
    padding-top: 24px;
    padding-bottom: 24px;
  }
  .el-dialog__footer {
    padding-top: 0;
  }
  .el-dialog__title {
    font-family: Inter;
    font-size: 20px;
    font-weight: 600;
    line-height: 26px;
    color: @text-color-mian;
  }
  .el-radio.is-bordered {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 20px 10px;
    height: 70px;
  }
  .el-radio__label {
    overflow-wrap: break-word;
    white-space: normal;
    line-height: 1.5;
    font-size: 16px;
  }
  .el-radio.is-bordered.is-checked {
    background: rgba(16, 179, 183, 0.1);
  }
}
/deep/ .preview-dialog {
  .el-dialog__title {
    font-family: Inter;
    font-size: 20px;
    font-weight: 600;
    line-height: 26px;
    color: @text-color-mian;
  }
  .el-dialog__body {
    padding: 6px 24px 0 24px;
  }
}
/deep/ .el-icon-microphone, /deep/ .el-icon-video-pause {
  font-size: 22px;
}

/deep/ .custom-message-box {
  font-size: 20px!important;
  /deep/ .el-message-box__title{
    font-size: 20px!important;
  }
}

.el-icon-close {
  cursor: pointer;
}

.plan-interpretation-guiding {
  background-color: #fff;
  border-radius: 5px;
}

.recorder-box {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  font-family: 'Source Sans Pro'
}
.recorder-btn-box {
  position: relative;
  border-radius: 50%;
  border: 3px solid red;
  padding: 7px;
  width: 80px;
}
.recorder-timmer {
  width: 80px;
  position: absolute;
  bottom: -30px;
  font-size: 24px;
  text-align: center;
}
.recorder-btn {
  height: 60px;
  width: 60px;
  border-radius: 50%;
  background: red;
  border: none;
  color: white;
  font-size: 18px;
}
.plan-dropdown /deep/ .el-button {
  border-width: 1px !important;
}
/deep/ .el-popper.plan-interpretation-guide-popover {
  left: unset!important;
  right: 10px!important;
}

// 移动端适配样式
@media screen and (max-width: 768px) {
  .video-wrapper {
    max-height: 300px !important;
  }

  .recorder-box {
    bottom: 20px;
    z-index: 9999;
  }

  .recorder-btn-box {
    width: 60px;
    padding: 5px;
  }

  .recorder-btn {
    height: 45px;
    width: 45px;
    font-size: 14px;
  }

  .recorder-timmer {
    width: 60px;
    bottom: -25px;
    font-size: 18px;
  }

  /deep/ .el-dialog {
    width: 95% !important;
    margin: 10px auto !important;
  }

  /deep/ .el-dialog__body {
    padding: 10px;
  }

  .voice-source-dialog {
    width: 95% !important;

    .el-radio.is-bordered {
      height: auto;
      padding: 15px 10px;
      margin-bottom: 10px;
    }
  }

  .preview-dialog {
    .el-dialog__body {
      padding: 10px;
    }
  }

  // 触摸区域优化
  .el-button,
  .el-switch,
  .el-radio {
    min-height: 44px;
  }

  .el-radio__label {
    font-size: 14px !important;
  }

  // 录制按钮优化
  .el-button[type="primary"] {
    width: 100%;
    margin-bottom: 10px;
  }
}

// 移动端弹窗样式优化
@media screen and (max-width: 768px) {
  .plan-interpretation-guide-popover {
    max-width: 90vw !important;
    
    p {
      font-size: 16px;
      line-height: 1.4;
    }

    ul li {
      font-size: 14px;
      line-height: 1.4;
    }
  }
}
</style>

<style lang="less">
.el-popper.plan-interpretation-guide-popover {
  background: #79B948;
  color: #FFFFFF;
  padding: 24px;
  border: none;
  &.el-popper[x-placement^=left] .popper__arrow::after{
    border-left-color: #79B948;
  }
  &.el-popper[x-placement^=right] .popper__arrow::after{
    border-right-color: #79B948;
  }
  &.el-popper[x-placement^=bottom] .popper__arrow::after{
    border-bottom-color: #79B948;
  }
  &.el-popper[x-placement^=top] .popper__arrow::after{
    border-top-color: #79B948;
  }
  p {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
  }
  ul {
    padding-left: 24px;
    margin-bottom: 24px;
    li {
      list-style: disc;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
    }
  }
  .el-button {
    color:#79B948;
  }
}
</style>
