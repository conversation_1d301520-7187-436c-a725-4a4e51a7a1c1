<template>
  <div class="">
    <!-- 观察评分统计 -->
    <observation-card :groupId="groupId" :planId="planId"></observation-card>
    <el-divider class="m-t-xs m-b-md class-overview-divider"></el-divider>
    <!-- 周反思 -->
    <reflection-card :groupId="groupId" :planId="planId" :week="week"></reflection-card>
    <el-divider  v-if="domainScoreData && Object.keys(domainScoreData).length > 0 " class="m-t-md m-b-md class-overview-divider"></el-divider>
    <!-- 领域分统计 -->
    <domain-statistics-card
      v-if="domainScoreData"
      ref="domainStatisticsCard"
      @callChangeBenchmark="loadDomainScoreData"
      :domainScoreData="domainScoreData"
      :benchmarks="benchmarks"
      :loading="domainLoading"
      :groupId="groupId">
    </domain-statistics-card>

    <el-divider  v-if="domainScoreDataCopy && Object.keys(domainScoreDataCopy).length > 0 " class="m-t-md m-b-md class-overview-divider"></el-divider>

    <!-- 劣势测评点 -->
    <growth-measure-card
      v-if="domainScoreDataCopy"
      ref="growthMeasureCard"
      @showCoreChange="showCoreChange"
      :domainScoreData="domainScoreDataCopy"
      :loading="domainLoading"
      :groupId="groupId">
    </growth-measure-card>

    <!-- 兴趣排行 -->
    <interest-card :groupId="groupId"></interest-card>
  </div>
</template>

<script>
import { mapState } from "vuex"
import LessonApi from '@/api/lessons2'
import ObservationCard from './ObservationCard'
import DomainStatisticsCard from './DomainStatisticsCard'
import GrowthMeasureCard from './GrowthMeasureCard'
import ReflectionCard from './ReflectionCard'
import InterestCard from './InterestCard'

export default {
  name: 'PlanToolbar',
  components: {
    ObservationCard,
    DomainStatisticsCard,
    GrowthMeasureCard,
    ReflectionCard,
    InterestCard
  },

  props: {
    planId: {
      type: String
    },
    groupId: {
      type: String
    },
    week: {
      type: Number
    }
  },

  data () {
    return {
      domainScoreData: {},
      domainScoreDataCopy: {},
      benchmarkTemplate: {},
      benchmarks: [],
      frameworkId: undefined,
      domainLoading: true
    }
  },

  created () {
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    })
  },

  methods: {
    /**
     * 加载基准设置列表
     */
    loadBenchmarks () {
      if (!this.groupId) {
        return
      }
      this.domainLoading = true
      LessonApi.getBenchmarks({
        groupId: this.groupId
      }).then(response => {
        // 基准模板
        this.benchmarkTemplate = response.benchmarkSettingTemplate
        // 基准设置列表
        this.benchmarks = response.viewList
        // 如果设置了基准，默认选择第一个基准
        if (this.benchmarks && this.benchmarks.length > 0) {
          this.$refs.domainStatisticsCard.benchmarkId = this.benchmarks[0].id
        } else {
          this.$refs.domainStatisticsCard.benchmarkId = undefined
        }
        // 框架 ID
        this.frameworkId = response.frameworkId
        // 获取领域分数据
        this.loadDomainScoreData()
      }).catch(error => {
        this.loadDomainScoreData()
        this.domainLoading = false
      })
    },

    /**
     * 加载观察、评分统计数据
     */
    loadDomainScoreData () {
      if (!this.groupId) {
        this.domainLoading = false
        return
      }
      this.domainLoading = true
      LessonApi.getDomainScoreStats({
        groupId: this.groupId,
        benchmarkId: this.$refs.domainStatisticsCard.benchmarkId,
        showSubDomain: this.domainScoreData ? this.domainScoreData.showSubDomain : undefined
      }).then(response => {
        this.domainScoreData = this.sortDomain(response)
        this.domainScoreDataCopy = JSON.parse(JSON.stringify(this.domainScoreData))
        this.showCoreChange(response.showCoreMeasureOpen)
        this.domainLoading = false
      }).catch(error => {
        this.domainScoreData = {}
        this.domainScoreDataCopy = {}
        this.domainLoading = false
      })
    },

    /**
     * 领域排序 TODO 需要放在后台排
     */
    sortDomain (domainScoreData) {
      if (this.benchmarkTemplate && this.benchmarkTemplate.domains && domainScoreData && domainScoreData.domains) {
        let domains = []
        this.benchmarkTemplate.domains.forEach(templateDomain => {
          domainScoreData.domains.forEach(domain => {
            if (templateDomain.className.toLowerCase() === domain.measureAbbr.toLowerCase()) {
              domains.push(domain)
            }
          })
        })
        domainScoreData.domains = domains
      }
      return domainScoreData
    },

    // 切换核心测评点开关
    showCoreChange (val) {
      let filterData = JSON.parse(JSON.stringify(this.domainScoreData))
      this.$refs.growthMeasureCard.showCore = val
      if (val) {
        // 将核心测评点过滤出来
        filterData.measures = filterData.measures.filter(x => x.core)
      }
      this.domainScoreDataCopy = filterData
    }
  },

  watch: {
    /**
     * 切换班级时重新加载数据
     */
    groupId: {
      immediate: true,
      handler() {
        this.groupId && this.loadBenchmarks()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.class-overview-divider {
  width: calc(100% - 40px);
  margin-left: 20px !important;
  margin-right: 20px !important;
}
</style>