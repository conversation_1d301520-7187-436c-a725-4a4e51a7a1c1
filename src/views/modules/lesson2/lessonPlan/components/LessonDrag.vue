<template>
  <div>
    <!-- 结束拖拽 -->
    <el-button :size="size" plain v-if="draging" @click="endDrag">{{ $t('loc.plan147') }}</el-button>
    <!-- 开始拖拽 -->
    <el-button :size="size" plain v-else @click="startDrag">{{ $t('loc.plan146') }}</el-button>
    <!-- 拖拽功能提示 -->
    <el-dialog :visible.sync="dialogVisible" custom-class="lesson-drag-dialog">
      <span slot="title" v-html="$t('loc.plan148')"></span>
      <div>
        <div v-html="$t('loc.plan149')"></div>
        <img style="width: 100%;" class="lg-margin-top-16" src="@/assets/img/lesson2/plan/lesson_drag_tip.png">
      </div>
      <div slot="footer">
        <el-button type="primary" @click="dialogVisible = false">{{ $t('loc.plan150') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'LessonDrag',
  props: {
    size: {
      type: String
    }
  },
  data () {
    return {
      draging: false, // 是否正在拖拽
      dialogVisible: false // 拖拽功能提示
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    })
  },
  methods: {
    // 开始拖拽
    startDrag () {
      let key = 'LESSON_DRAG_TIP_' + this.currentUser.user_id
      let tip = localStorage.getItem(key)
      if (!tip) {
        localStorage.setItem(key, true)
        this.dialogVisible = true
      }
      this.draging = true
      // 防止火狐浏览器拖拽后打开新标签页
      document.body.ondrop = function (event) {
        event.preventDefault()
        event.stopPropagation()
      }
      this.$emit('change', this.draging)
    },
    // 结束拖拽
    endDrag () {
      this.draging = false
      this.$emit('change', this.draging)
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .lesson-drag-dialog {
  .el-dialog__body {
    padding-top: 16px;
    padding-bottom: 16px;
  }
}
</style>
