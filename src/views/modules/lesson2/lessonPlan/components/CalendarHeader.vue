<template>
  <div class="lg-margin-bottom-24">
    <!-- iPad 顶部返回 -->
    <template v-if="isComeFromIPad && edit">
      <div class="display-flex">
        <div style="position:relative; text-align: center; width: 100%; height: 54px;padding-left: 10px; line-height: 54px;">
          <div style="position: absolute;">
            <img class="goback lg-pointer" src="@/assets/img/us/back_home.png" @click="beforeGoBack">
          </div>
          <span class="title-font-18">{{ title }}</span>
        </div>
      </div>
    </template>
    <!-- 编辑状态 -->
    <template v-if="edit">
      <div class="flex-space-between">
        <!-- 基础信息 -->
        <div class="display-flex align-items">
          <!-- 学校 -->
          <el-select class="w-sm border-bold" v-model="selectedCenterId" @change="changeCenter">
            <el-option
              v-for="center in centers"
              :key="center.id"
              :label="center.name"
              :value="center.id">
            </el-option>
          </el-select>
          <!-- 班级 -->
          <el-select class="w-sm lg-margin-left-16 lg-margin-right-8 border-bold" v-model="selectedGroupId" @change="changeGroup">
            <el-option
              v-for="group in groups"
              :key="group.id"
              :label="group.name"
              :value="group.id">
            </el-option>
          </el-select>
          <el-divider direction="vertical"></el-divider>
          <!-- 选择周次 -->
          <el-select v-model="currentWeek" @change="changeWeek()" class="w-sm lg-margin-left-8 border-bold" :placeholder="$t('loc.plan41')">
            <el-option style="z-index: 1" v-for="week in 54" :key="week" :label="$t('loc.weekName')+' ' + week" :value="week"></el-option>
          </el-select>
          <el-date-picker
            class="lg-margin-left-16 w-md border-bold week-select-start-end-date"
            :clearable="false"
            v-model="dataRange"
            type="daterange"
            @change="changeDateRange"
            range-separator="-"
            format="MM/dd/yyyy"
            value-format="MM/dd/yyyy"
            start-placeholder="Start Date"
            end-placeholder="End Date">
          </el-date-picker>
        </div>
        <!-- 老师、单元课程、上周反思 -->
        <div class="display-flex align-items">
          <!-- 老师 -->
          <div class="teacher-area m-l">
            <div style="display: flex; align-items: center;">
              <el-select v-model="selectTeacherIds" multiple collapse-tags @change="changeTeacher" class="border-bold teacher-select" :placeholder="$t('loc.plan42')" style="width: 200px">
                <el-option v-for="(teacher, index) in teachers" :key="index" :label="teacher.name | formatUserName" :value="teacher.id"></el-option>
              </el-select>
            </div>
          </div>
          <!-- 周计划 DLL 设置按钮 -->
          <el-popover
            placement="bottom"
            width="150"
            trigger="click"
            v-model="morePopVisable">
            <!-- 复制按钮 -->
            <div>
              <el-link
                :underline="false"
                :disabled="planRecording"
                @click.stop="showReplicatePlanDialog()">
                <i class="lg-icon lg-icon-copy font-size-24 lg-margin-right-16"></i>
                <span>{{$t('loc.plan66')}}</span>
              </el-link>
            </div>
            <el-divider class="action-divider"></el-divider>
            <!-- PDF 下载和打印 -->
            <div>
              <el-link
                :underline="false"
                @click.stop="generatePDF(false)">
                <i class="lg-icon lg-icon-download font-size-24 lg-margin-right-16"></i>{{$t('loc.download')}}
              </el-link>
            </div>
            <div>
              <el-link
                :underline="false"
                @click.stop="generatePDF(true)">
                <i class="lg-icon lg-icon-print font-size-24 lg-margin-right-16"></i>{{$t('loc.plan174')}}
              </el-link>
            </div>
            <el-divider class="action-divider"></el-divider>
            <!-- 删除 -->
            <div>
              <el-link
                class="action-delete"
                :underline="false"
                :disabled="planInfo.lockedData || !planInfo.canEditOrDelete || planRecording"
                @click.stop="deletePlan()">
                <i class="lg-icon lg-icon-delete font-size-24 lg-margin-right-16"></i>{{$t('loc.delete')}}</el-link>
            </div>
            <el-button slot="reference" style="padding: 0 10px"
                       icon="el-icon-more" class="m-l-xs"></el-button>
          </el-popover>
            <!-- 齿轮 + 三个展示的标题及其引导部分 -->
            <div>
                <el-popover
                    placement="bottom-end"
                    width="440"
                    v-model="canAdapter && settingGuide.showSettingGuide"
                    ref="settingGuide"
                    popper-class="adapt-UDL-and-CLR-guide-color-text"
                    trigger="manual">
                    <div class="text-white">
                        <!-- 标题 -->
                        <div class="title-font-20 lg-margin-bottom-12">{{ $t('loc.unitPlannerPlanItemNewSetting') }}</div>
                        <!-- 引导文字 -->
                        <div class="lg-margin-bottom-24 word-break text-left">
                            <!-- 用户引导内容 -->
                            <img class="w-full" src="@/assets/img/lesson2/guide/week_plan_setting_img.png">
                            <ul>
                                <li>{{ $t('loc.unitPlannerPlanItemNewSettingGuideContent1') }}</li>
                                <li>{{ $t('loc.unitPlannerPlanItemNewSettingGuideContent2') }}</li>
                                <li>{{ $t('loc.mixAgeGroup6') }}</li>
                            </ul>
                        </div>
                        <div class="display-flex flex-justify-end gap-6 align-items">
                            <el-button type="primary" @click="endSettingGuide">{{
                                    $t('loc.ikonw')
                                }}
                            </el-button>
                        </div>
                    </div>
                    <div slot="reference">
                        <el-popover
                            placement="bottom-end"
                            trigger="click">
                            <div class="display-flex flex-direction-col" style="align-items: baseline">
                                <el-link class="settings-style"
                                         :underline="false"
                                         @click="openDllSettings()">{{$t('loc.unitPlannerPlanItemAssignHomeLanguageHomeworkToFamilies')}}
                                </el-link>
                                <!--dll settings-->
                                <!-- <dll-settings ref="dllSettings" v-show="false"/> -->
                                <el-link class="settings-style"
                                         :underline="false"
                                         v-show="showAdapterFeature"
                                         @click="manageClassRoomDemographics">{{$t('loc.unitPlannerPlanItemManageClassroomDemographics')}}
                                </el-link>
                                <el-link class="settings-style"
                                         :underline="false"
                                         v-show="showAdapterFeature"
                                         @click="showAssignTeacherGroups()">{{
                                        $t('loc.unitPlannerAssignTeacherGroup')
                                    }}
                                </el-link>
                              <el-link class="settings-style"
                                       :underline="false"
                                       v-show="showAdapterFeature"
                                       @click="showMixedAgeDifferentiation">{{$t('loc.mixAgeGroup2')}}
                                <div  v-show="showNewPointer" >
                                  <span class="newPoint point-relative">{{ $t('loc.new') }}</span>
                                </div>
                              </el-link>
                            </div>
                            <el-button slot="reference" @click="endSettingGuide" style="padding: 0 10px"
                                       class="m-l-xs lg-icon lg-icon-settings"></el-button>
                        </el-popover>
                    </div>
                </el-popover>
            </div>
        </div>

      </div>
    </template>
    <template v-else>
      <div class="bg-white flex-space-between align-items lg-border-radius-8 lg-padding-left-20 lg-padding-right-20">
        <div class="display-flex align-items">
          <div v-if="isComeFromIPad">
            <img class="goback lg-pointer" src="@/assets/img/us/back_home.png" @click="beforeGoBack">
          </div>
          <!-- 审核状态 -->
          <div class="m-l-xs font-size-16" style="height:60px; width: 60px;">
            <div v-if="planInfo.status == 'B_PENDING'">
              <img style="width: 60px;" class="add-margin-r-16" src="@/assets/img/lesson2/plan/pending.png">
            </div>
            <div v-if="planInfo.status == 'C_REJECTED'">
              <img style="width: 60px;" class="add-margin-r-16" src="@/assets/img/lesson2/plan/rejected.png">
            </div>
            <div v-if="planInfo.status == 'D_APPROVED'">
              <img style="width: 60px;" class="add-margin-r-16" src="@/assets/img/lesson2/plan/approved.png">
            </div>
            <div v-if="planInfo.status == 'A_DRAFT'">
              <img style="width: 60px;" class="add-margin-r-16" src="@/assets/img/lesson2/plan/draft.png">
            </div>
          </div>
          <!-- 查看状态 -->
          <div class="display-flex align-items" v-if="planInfo || (fromDate && toDate) ">
            <template v-if="!isSharedDetail">
              <!-- 上周 -->
              <el-tooltip class="item" effect="dark" :content="$t('loc.plan98')" placement="top" :disabled="!!planInfo.lastPlanId">
                <div class="m-r-sm lg-pointer" @click="lastPlan" :class="{'lg-color-text-secondary cursor-disabled': !planInfo.lastPlanId || planRecording}">
                  <i class="lg-icon lg-icon-arrow-left font-size-20 text-default"></i>
                </div>
              </el-tooltip>
              <!-- 下周 -->
              <el-tooltip class="item" effect="dark" :content="$t('loc.plan99')" placement="top" :disabled="!!planInfo.nextPlanId">
                <div class="m-l-sm lg-pointer" @click="nextPlan" :class="{'lg-color-text-secondary cursor-disabled': !planInfo.nextPlanId || planRecording}">
                  <i class="lg-icon lg-icon-arrow-right font-size-20 text-default"></i>
                </div>
              </el-tooltip>
            </template>
            <div class="display-flex align-items">
              <!-- 展示周次 -->
              <div class="lg-margin-left-12 title-font-20 lg-color-text-primary">
                  <span v-if="planInfo.week">Week {{planInfo.week}}</span>
              </div>
              <!-- 展示开始/结束日期 -->
              <div v-if="fromDate && toDate" class="lg-margin-left-12">
                {{fromDate}} - {{toDate}}
              </div>
              <!-- 没有开始结束时间说明是周计划模板，显示框架名称 -->
              <div v-else>
                {{ frameworkName }}
              </div>
            </div>
          </div>
        </div>
        <div class="display-flex">
          <!-- 分享周计划统计,只在查看打开统计时显示 -->
          <el-popover
            v-if="showOpenRate"
            placement="bottom-end"
            width="310"
            :visible-arrow="false"
            v-model="openRatePop"
            trigger="click">
            <div v-loading="loadingReadStats">
              <div class="title-font-18 lg-color-text-primary">{{$t('loc.openSts')}}</div>
              <div v-if="openStats.readUsers.length > 0" class="font-size-14 m-t-xs lg-color-text-primary">{{$t('loc.shareOpened', {num : openStats.readUsers.length})}}</div>
              <div v-if="openStats.readUsers.length > 0" class="lg-scrollbar-show" style="max-height: 400px">
                <div class="flex-col-center lg-padding-t-b-12" v-for="(teacher, index) in openStats.readUsers" :key="index">
                  <el-avatar size="medium" :src="teacher.avatarUrl"></el-avatar>
                  <span class="lg-margin-left-12 lg-color-text-primary">{{teacher.displayName | formatUserName}}</span>
                </div>
              </div>
              <div v-if="openStats.unreadUsers.length > 0" class="font-size-14 m-t-xs lg-color-text-primary">{{$t('loc.shareNotOpened', {num : openStats.unreadUsers.length})}}</div>
              <div v-if="openStats.unreadUsers.length > 0" class="lg-scrollbar-show" style="max-height: 400px">
                <div class="flex-col-center lg-padding-t-b-12" v-for="(teacher, index) in openStats.unreadUsers" :key="index">
                  <el-avatar size="medium" :src="teacher.avatarUrl"></el-avatar>
                  <span class="lg-margin-left-12 lg-color-text-primary">{{teacher.displayName | formatUserName}}</span>
                </div>
              </div>
            </div>
            <el-button v-show="showOpenRate" class="lg-margin-right-16" slot="reference" type="primary" plain size="medium"><i class="fa fa-line-chart"></i>{{$t('loc.openRate')}}: {{openStats.percent}}</el-button>
          </el-popover>
          <!-- 草稿查看状态下可以编辑 -->
          <!-- <el-tooltip class="item" effect="dark" :content="eidtingTip" placement="top">
          </el-tooltip> -->
          <span class="m-l-sm m-r-sm" v-if="planInfo.id && !showOpenRate">
            <el-button
              plain
              size="medium"
              v-if="(isAdmin && planInfo.status !== 'B_PENDING') || (planInfo.type === 'NORMAL' && !isAdmin) || (isAdmin && planInfo.lockedData)"
              :disabled="planInfo.lockedData"
              @click="goEdit()">
              <i class="lg-icon lg-icon-edit"></i>
              {{$t('loc.edit')}}
            </el-button>
          </span>
          <el-button
            plain
            size="medium"
            :loading="revertLoading"
            :disabled="planInfo.lockedData"
            v-if="isTeacher && planInfo.status === 'B_PENDING'"
            @click="revert()"> <i class="lg-icon lg-icon-efresh-left"></i> {{$t('loc.plan14')}}</el-button>
          <!-- 分享周计划 -->
          <el-button v-if="!isTeacher && planInfo.status === 'D_APPROVED'" type="primary" size="medium" class="lg-margin-right-8 lg-margin-left-8" @click="beforeShare"><i class="fa fa-share"></i>{{$t('loc.createVirtualShadow1')}}</el-button>
          <!-- 打印按钮 -->
          <el-button v-if="planInfo.id" type="primary" size="medium" class="lg-margin-right-8 lg-margin-left-8" @click.stop="generatePDF(true)">
            <i class="lg-icon lg-icon-print lg-margin-right-8"></i>{{$t('loc.plan174')}}
          </el-button>
          <!-- 复制 下载 打印 删除 菜单 -->
          <el-popover
            placement="bottom"
            width="150"
            trigger="click"
            v-model="morePopVisable">
            <!-- 复制按钮 -->
            <div>
              <el-link
                :underline="false"
                :disabled="planRecording"
                @click.stop="showReplicatePlanDialog()">
                <i class="lg-icon lg-icon-copy font-size-24 lg-margin-right-16"></i>
                <span>{{$t('loc.plan66')}}</span>
              </el-link>
            </div>
            <el-divider class="action-divider"></el-divider>
            <!-- PDF 下载和打印 -->
            <div>
              <el-link
                :underline="false"
                @click.stop="generatePDF(false)">
                <i class="lg-icon lg-icon-download font-size-24 lg-margin-right-16"></i>
                <span>{{$t('loc.download')}}</span>
              </el-link>
            </div>
            <div>
              <el-link
                :underline="false"
                @click.stop="generatePDF(true)">
                <i class="lg-icon lg-icon-print font-size-24 lg-margin-right-16"></i>
                <span>{{$t('loc.plan174')}}</span>
              </el-link>
            </div>
            <el-divider class="action-divider"></el-divider>
            <!-- 删除 -->
            <div>
              <el-link
                class="action-delete"
                :underline="false"
                :disabled="planInfo.lockedData || !planInfo.canEditOrDelete || planRecording"
                @click.stop="deletePlan()">
                <i class="lg-icon lg-icon-delete font-size-24 lg-margin-right-16"></i>{{$t('loc.delete')}}</el-link>
            </div>
            <el-button slot="reference" @click="showMorePop" v-if="planInfo.id" style="padding: 0 10px; height: 36px;width: 40px"
                       icon="el-icon-more" class="add-margin-l-10"></el-button>
          </el-popover>
            <!-- 齿轮 + 三个展示的标题及其引导部分 -->
            <div v-if="edit || review || (planInfo.lockedData && planInfo.status === 'B_PENDING')">
                <el-popover
                    placement="bottom-end"
                    width="440"
                    v-model="canAdapter && settingGuide.showSettingGuide"
                    ref="settingGuide"
                    popper-class="adapt-UDL-and-CLR-guide-color-text"
                    trigger="manual">
                    <div class="text-white">
                        <!-- 标题 -->
                        <div class="title-font-20 lg-margin-bottom-12">{{ $t('loc.unitPlannerPlanItemNewSetting') }}</div>
                        <!-- 引导文字 -->
                        <div class="lg-margin-bottom-24 word-break text-left">
                            <!-- 用户引导内容 -->
                            <img class="w-full" src="@/assets/img/lesson2/guide/week_plan_setting_img.png">
                            <ul>
                                <li>{{ $t('loc.unitPlannerPlanItemNewSettingGuideContent1') }}</li>
                                <li>{{ $t('loc.unitPlannerPlanItemNewSettingGuideContent2') }}</li>
                                <li>{{ $t('loc.mixAgeGroup6') }}</li>
                            </ul>
                        </div>
                        <div class="display-flex flex-justify-end gap-6 align-items">
                            <el-button type="primary" @click="endSettingGuide">{{
                                    $t('loc.ikonw')
                                }}
                            </el-button>
                        </div>
                    </div>
                    <div slot="reference">
                        <el-popover
                            placement="bottom-end"
                            trigger="click">
                            <div class="display-flex flex-direction-col" style="align-items: baseline">
                                <el-link class="settings-style"
                                         :underline="false"
                                         @click="openDllSettings()">{{$t('loc.unitPlannerPlanItemAssignHomeLanguageHomeworkToFamilies')}}
                                </el-link>
                                <el-link class="settings-style"
                                         :underline="false"
                                         v-show="showAdapterFeature"
                                         @click="manageClassRoomDemographics">{{$t('loc.unitPlannerPlanItemManageClassroomDemographics')}}
                                </el-link>
                                <el-link class="settings-style"
                                         :underline="false"
                                         v-show="showAdapterFeature"
                                         @click="showAssignTeacherGroups()">{{
                                        $t('loc.unitPlannerAssignTeacherGroup')
                                    }}
                                </el-link>
                              <el-link class="settings-style"
                                       :underline="false"
                                       v-show="showAdapterFeature"
                                       @click="showMixedAgeDifferentiation">{{$t('loc.mixAgeGroup2')}}
                                <div  v-show="showNewPointer" >
                                  <span class="newPoint point-relative">{{ $t('loc.new') }}</span>
                                </div>
                              </el-link>
                            </div>
                            <el-button slot="reference" @click="endSettingGuide" style="padding: 0 10px; height: 36px;"
                                       class="add-margin-l-16 lg-icon lg-icon-settings"></el-button>
                        </el-popover>
                    </div>
                </el-popover>
            </div>
        </div>
      </div>
      <!-- 审核通过或拒绝的原因 -->
      <div v-if="planInfo.status && planInfo.reviewComment && !isSharedDetail || planInfo.lockedData" class="lg-margin-top-12 title-font-16-regular">
        <span :class="{'lg-color-danger': planInfo.status === 'C_REJECTED', 'lg-color-success': planInfo.status === 'D_APPROVED'}">
          <span v-if="planInfo.status === 'C_REJECTED'">{{ $t('loc.plan175') }}</span>
          <span>{{ planInfo.reviewComment }}</span>
          <span :class="{ 'lg-color-warning': eidtingTip && eidtingTip !== 'Edit'}" class="lg-margin-left-12" v-if="eidtingTip !== 'Edit'">{{ eidtingTip }}</span>
        </span>
      </div>
    </template>
    <!--Assign Teacher Groups-->
    <AssignTeacherGroups :hideIEP="hideIEPOpen" ref="assignTeacherGroups"></AssignTeacherGroups>
    <!-- Mixed-age Differentiation Settings -->
    <MixedAgeDifferentiationSettings ref="mixedAgeDifferentiation"></MixedAgeDifferentiationSettings>
    <!-- 预览弹窗 -->
    <div class="full-modal-area">
      <el-dialog
        custom-class="full-modal"
        @open="previewPlan"
        :fullscreen=true
        :visible.sync="previewModalVisible">
        <div class="header flex-space-between font-size-16">
          <div class="filter-container display-flex" :style="{'min-height': edit ? '50px' : '50px'}" style="align-items: end;">
            <!-- <span class="m-r-md">{{$t('loc.class')}}: {{planInfo.groupName}}</span>
            <span class="m-r-md">{{$t('loc.planTeacher')}}: {{planInfo.teacherNames}}</span> -->
          </div>
          <!-- 展示周次 -->
          <div class="display-week-container display-flex align-items">
            <div class="display-week display-flex add-little-padding-tb">
              <div class="font-bold display-week">
                <span v-if="planInfo.week">Week {{planInfo.week}}</span>
              </div>
              <!-- 展示开始/结束日期 -->
              <div class="display-flex align-items">
                {{fromDate}} - {{toDate}}
              </div>
            </div>
          </div>
          <!-- 操作按钮 -->
          <div class="display-flex align-items m-r-lg">
            <el-button type="primary" size="medium">PDF</el-button>
          </div>
        </div>
        <!-- 预览内容 -->
        <plan-calendar ref="previewCalendar" :preview="true" :planId="planInfo.id" :showLastReflection="planInfo.showLastReflection"></plan-calendar>
      </el-dialog>
    </div>
    <!-- 分享弹窗 -->
    <el-dialog
      v-if="!isTeacher"
      id="share-card-body"
      :title="$t('loc.createVirtualShadow2')"
      width="60%"
      :before-close="cancelShare"
      :visible.sync="shareModalVisible">
      <div style="display:flex; flex-direction:column">
        <!-- 周计划作者选择 -->
        <div class="font-size-16 font-color-black font-weight-semibold">{{$t('loc.creatorOfPlanner')}}</div>
        <el-select size="small" v-if="creators.length > 1" v-model="creatorUserId">
          <el-option v-for="item in creators" :key="item.id" :label="item.displayName" :value="item.id"></el-option>
        </el-select>
        <div v-else>{{creators.length > 0 ? creators[0].displayName : ''}}</div>
        <div v-show="showSelectCreatorTip" style="color:red;">{{$t('loc.plan20')}}</div>
        <!-- 分享类型选择 -->
        <div class="m-t-xs font-size-16 font-color-black font-weight-semibold">{{$t('loc.shadowedPlanner')}}</div>
        <div>
          <el-radio v-model="shareType" label="PART">{{$t('loc.selectedWeeks')}}</el-radio>
          <el-radio v-model="shareType" label="ALL" class="add-margin-l-20">{{$t('loc.entireSet')}}</el-radio>
        </div>
        <!-- 日期时间段选择框 -->
        <div class="flex-col-center" v-show="shareType == 'PART'">
          <!-- 开始时间 -->
          <el-date-picker
            @focus="closeKeyboard"
            editable="false"
            class="from-to-picker"
            size="small"
            :clearable="false"
            v-model="shareFromDate"
            type="date"
            :picker-options="shareFromPickerOptions"
            @change="getPlanCount"
            format="MM/dd/yyyy"
            value-format="MM/dd/yyyy"
            placeholder="Start Date">
          </el-date-picker>
          <div class="m-l-xs m-r-xs">-</div>
          <!-- 结束时间 -->
          <el-date-picker
            @focus="closeKeyboard"
            editable="false"
            class="from-to-picker"
            size="small"
            :clearable="false"
            v-model="shareToDate"
            type="date"
            @change="getPlanCount"
            :picker-options="shareToPickerOptions"
            format="MM/dd/yyyy"
            value-format="MM/dd/yyyy"
            placeholder="End Date">
          </el-date-picker>
        </div>
        <!-- 所选时间段内没有周计划提示 -->
        <div v-show="noPlansTip">
          <span style="color:red;">{{$t('loc.noPlanTip')}}</span>
        </div>
        <div v-show="showSelectShareTypeTip" style="color:red;">{{$t('loc.plan20')}}</div>
      </div>
      <div class="m-t-xs font-size-16 font-color-black font-weight-semibold add-margin-t-10">
        {{$t('loc.shareWith')}}
      </div>
      <div>
        <el-checkbox v-model="selectAll" @change="isSelectAll">{{$t('loc.selectAll')}}</el-checkbox>
        <el-tree
          :style="{'max-height': treeHeight + 'px'}"
          style="overflow:auto;"
          class="scrollbar"
          @check-change="handleCheckChange"
          ref="tree"
          :data="treeData"
          show-checkbox
          node-key="treeNodeId"
          :props="defaultProps">
          <div slot-scope="{data}">
            <!-- img 和 font 的style都要设置vertical-align: -webkit-baseline-middle;才会一起对齐 -->
            <div class="custom-tree-node">
              <img  v-if="!data.teachers" :src="data.avatarUrl" alt="" class="tree-node-img">
              <span>{{data.name}}</span>
            </div>
          </div>
        </el-tree>
      </div>
      <div v-show="showSelectShareWithTip" style="color:red;">{{$t('loc.plan20')}}</div>
      <div class="m-t-xs font-size-16 font-color-black font-weight-semibold">
        {{$t('loc.shareComment')}}
      </div>
      <el-input
        type="textarea"
        :rows="4"
        :placeholder="$t('loc.commentTip')"
        maxlength="1000"
        show-word-limit
        v-model="comment">
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelShare()">{{$t('loc.cancel')}}</el-button>
        <el-button type="primary" :loading="shareLoading" @click="share()">{{$t('loc.save')}}</el-button>
      </span>
    </el-dialog>
    <!-- 提交周计划提醒 -->
    <plan-apply-records
    ref="applyRecordsModal"
    ></plan-apply-records>
    <!--dll settings-->
    <dll-settings ref="dllSettings" v-show="false"/>
    <!-- 复制确认弹窗 -->
    <el-dialog
      :title="$t('loc.confirmation')"
      custom-class="lg-el-dialog"
      :visible.sync="replicateModelVisible"
      width="30%">
      <span class="lg-color-text-primary">{{$t('loc.plan67')}}</span>
      <span slot="footer" class="dialog-footer">
        <el-button class="font-weight-normal" plain @click="replicateModelVisible = false" :disabled="replicateLoading">{{$t('loc.cancel')}}</el-button>
        <el-button class="font-weight-normal" type="primary" @click="replicatePlan" :loading="replicateLoading">{{$t('loc.confirm')}}</el-button>
      </span>
    </el-dialog>
    <!-- 重新选择学校班级弹窗 -->
    <el-dialog
      :visible.sync="selectCenterGroupModelVisible"
      :title="$t('loc.plan119')"
      :width="isComeFromIPad ? '50%' : '30%'"
      custom-class="edit-plan-dialog"
      :before-close="saveNewCenterGroup">
      <el-row class="center-group-background" :gutter="10">
        <el-col :span="12">
          <div class="edit-plan-text">{{ $t('loc.site') }}</div>
          <el-select v-model="newCenterId" :placeholder="$t('loc.plan111')" >
            <el-option
              v-for="item in centers"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="12">
          <div class="edit-plan-text">{{ $t('loc.class') }}</div>
          <el-select v-model="newGroupId" :placeholder="$t('loc.plan111')">
            <el-option
              v-for="item in groups"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button plain @click="saveNewCenterGroup">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="saveNewCenterGroup">{{ $t('loc.save') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import PlanCalendar from './PlanCalendar'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
import { isTeacher } from '@/utils/common'
import tools from '@/utils/tools'
import DllSettings from '@/views/modules/lesson2/lessonLibrary/components/WeeklyPlan/DllSettings'
import PlanApplyRecords from '@/views/modules/lesson2/lessonPlan/components/PlanApplyRecords'
import AssignTeacherGroups from '@/views/modules/lesson2/lessonPlan/components/AssignTeacherGroups'
import MixedAgeDifferentiationSettings from '@/views/modules/lesson2/lessonPlan/components/MixedAgeDifferentiationSettings'
import store from '@/store'

export default {
  name: 'CalendarHeader',
  components: {
    PlanCalendar,
    DllSettings,
    PlanApplyRecords,
    AssignTeacherGroups,
    MixedAgeDifferentiationSettings
  },

  props: {
    edit: {
      type: Boolean
    },
    planInfo: {
      type: Object
    },
    title: {
      type: String
    },
    review: {
      type: Boolean
    },
    // 框架列表
    frameworks: {
      type: Array
    }
  },

  data () {
    return {
      weeks: [],
      centers: [],
      groups: [],
      teachers: [],
      showLastReflection: false,
      selectedCenterId: undefined,
      currentCenterId: undefined,
      selectedGroupId: undefined,
      currentGroupId: undefined,
      currentWeek: undefined,
      emptyTeacherIds: [],
      currentTeacherIds: [],
      fromDate: undefined,
      toDate: undefined,
      dataRange: '',
      fromPickerOptions: undefined,
      toPickerOptions: undefined,
      previewModalVisible: false,
      pdfLoading: false,
      isTeacher: false,
      canAdapter: false, // 是否可以使用 adapter 功能
      shareModalVisible: false,
      creatorUserId: '', // 创建者id
      shareType: '', // 分享类型
      pickerDate: [], // 选择的时间段
      shareFromPickerOptions: '',
      shareToPickerOptions: '',
      shareFromDate: '',
      shareToDate: '',
      noPlansTip: false, // 无周计划提示
      shareLoading: false, // 分享按钮loading
      allTeachers: [], // 所有老师
      treeData: [], // el-tree的数据
      defaultProps: {
        children: 'teachers',
        label: 'name'
      }, // el-tree数据源
      treeHeight: 0,
      selectAll: false, // 是否全选了
      selectedData: [], // 所选的数据
      comment: '', // 分享心得
      showOpenRate: false, // 是否显示打开统计
      shareId: '', // 分享id
      loadingReadStats: false, // 统计加载
      openRatePop: false, // 统计pop打开
      openStats: {}, // 统计数据
      showSelectCreatorTip: false, // 选择创作者提示
      showSelectShareTypeTip: false, // 选择分享类型提示
      showSelectShareWithTip: false, // 选择分享人提示
      editGroupDialogVisable: false, // 编辑班级弹窗
      showCore: true, // 是否显示核心测评点
      morePopVisable: false, // 更多操作弹窗
      replicateLoading: false, // 复制按钮 loading
      replicateModelVisible: false, // 复制确认弹窗
      revertLoading: false, // 撤回周计划 loading
      newCenterId: '', // 新的学校id
      newGroupId: '', // 新的班级id
      selectCenterGroupModelVisible: false, // 重新选择学校班级弹窗
      settingGuide: { // 引导设置
        showSettingGuide: false // 展示课程引导
        // title: "New Settings", // 引导标题
        // ulContent: ["Easily manage children's attributes to customize each lesson plan to address your class's unique needs.",
        //   "Manage teacher groups here for more effective implementation of each lesson plan."] // 引导内容
      },
      adaptUDLAndCLROpen: false, // 判断是否开启了 UDL 和 CLR
      hideIEPOpen: false, // 是否隐藏 IEP
      editPlanGuided: true, // 判断 editPlan 是否引导过
      triggerNewPointer: false // 是否触发 New 的标志
    }
  },

  created () {
    // this.getEventNotify()
    this.$bus.$on('itemMounted', () => {
      this.showCore = this.planInfo.showCoreMeasureOpen
      this.$bus.$emit('changeShowCore', { planId: this.planInfo.id, open: this.showCore })
    })
    // 设置开始、结束日期限制规则
    this.setDateOptions()
    // 获取学校、班级信息
    // this.getCenterGroups()
    // 是否是教师角色
    const role2 = this.currentUser.role2
    this.isTeacher = isTeacher()
    this.canAdapter = isTeacher() || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
    // 非教师角色，获取可分享的老师列表
    if (!this.isTeacher) {
      this.getAllTeachers()
    }
    // 动态计算选择老师列表展开的最大高度
    this.treeHeight = document.documentElement.clientHeight / 4 - 50
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      planRecording: state => state.lesson.planRecording
    }),
    showNewPointer() {
      // eslint-disable-next-line no-unused-expressions
      this.triggerNewPointer
      const item = sessionStorage.getItem('showMixedAgeDifferentiation' + this.currentUserId);
      return !item;
    },
    // 管理员分享的周计划详情
    isSharedDetail () {
      if (this.$route.name === 'view-plan' && this.$route.query.shareId) {
        return true
      }
      return false
    },
    dllOpen () {
      return this.open && this.open.dllopen
    },
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    // 是否展示 adapter 相关功能
    showAdapterFeature () {
      return this.open && this.open.adaptUDLAndCLROpen && this.canAdapter && !this.isComeFromIPad
    },
    isAdmin () {
      if (!this.currentUser) {
        return false
      }
      let role = this.currentUser.role
      return role && role.toUpperCase() === 'OWNER'
    },
    eidtingTip () {
      return this.planInfo && this.planInfo.lockedData ? this.planInfo.lockedData.userName + ' is editing this weekly planner.' : 'Edit'
    },
    selectTeacherIds: {
      get () {
        // 老师未加载时不显示
        if (!this.teachers || this.teachers.length === 0) {
          return []
        }
        // 过滤掉没有的老师
        this.currentTeacherIds = this.currentTeacherIds.filter(teacherId => this.teachers.find(teacher => teacher && teacherId && teacherId === teacher.id))
        return this.currentTeacherIds
      },
      set (val) {
        this.currentTeacherIds = val
      }
    },
    creators () {
      // 如果创建者只有一个，自动选择创建者
      if (this.planInfo.teachers && this.planInfo.teachers.length === 1) {
        this.creatorUserId = this.planInfo.teachers[0].id
      }
      return this.planInfo.teachers || []
    },
    curriculumInfo () {
      let curriculum = {
        curriculumId: this.planInfo.curriculumId,
        unitPlanNumber: this.planInfo.unitPlanNumber,
        unitId: this.planInfo.unitId,
        unitNumber: this.planInfo.unitNumber,
        unitTitle: this.planInfo.unitTitle
      }
      return curriculum
    },
    // 是否显示编辑按钮
    showEdit () {
      // 如果是编辑状态或已审核通过，返回false
      if (this.edit || this.planInfo.status === 'D_APPROVED' || this.planInfo.status === 'B_PENDING') {
        return false
      }
      // 如果是草稿或者被驳回，且不是管理员，或者是周计划的应用者，返回true
      if (!this.isAdmin || this.planInfo.applyUserId == this.currentUserId) {
        return true
      }
      return false
    },
    isComeFromIPad () {
      return tools.isComeFromIPad()
    },
    // 周计划的框架名称
    frameworkName () {
      if (this.planInfo && this.planInfo.frameworkId && this.frameworks && this.frameworks.length > 0) {
        let framework = this.frameworks.find(item => item.frameworkId.toUpperCase() == this.planInfo.frameworkId.toUpperCase())
        if (framework) {
          return framework.frameworkName
        }
      }
      return ''
    }
  },

  mounted () {
    if (this.$route.query.param) {
      this.planId = this.$route.params.planId
      this.shareId = this.$route.query.shareId
    }
  },

  methods: {

    /**
     * 显示更多操作弹窗
     */
    showMorePop () {
      this.morePopVisable = !this.morePopVisable
      // 根据不同页面发送不同的埋点事件（点击展开操作菜单）
      if (this.$route.name == 'edit-plan') {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_more')
      } else if (this.$route.name == 'view-plan') {
        this.$analytics.sendEvent('web_weekly_plan_detail_click_more')
      } else if (this.$route.name == 'shared-plan-detail') {
        this.$analytics.sendEvent('web_weekly_virtual_detail_click_more')
      }
    },

    /**
     * 撤回周计划
     */
    revert () {
      this.$analytics.sendEvent('web_weekly_plan_detail_click_recall')
      this.$confirm(this.$t('loc.plan51'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box'
      }).then(() => {
        this.revertLoading = true
        LessonApi.revertReview({
          id: this.planInfo.id
        }).then(response => {
          this.revertLoading = false
          this.$router.push({
            name: 'edit-plan',
            params: {
              planId: this.planInfo.id
            }
          })
          this.$message({
            type: 'success',
            message: this.$t('loc.plan52')
          })
        }).catch(error => {
          this.revertLoading = false
          let errorCode = error.response.data.error_code
          if (errorCode === 'plan_reviewed') {
            this.$message.error('Weekly planner has been reviewed!')
          }
        })
      }).catch(error => {
      })
    },

    /**
     * 获取学校、班级列表
     */
    getCenterGroups () {
      return this.$axios.get(
        $api.urls(this.currentUser.user_id).centersAndGroups
      ).then(response => {
        // 过滤离校班级
        response.forEach(c => {
          c.groups = c.groups.filter(x => !x.inactive)
        })
        // 过滤没有班级的学校
        this.centers = response.filter(c => c.groups.length > 0)
        this.setSelectedCenterIdAndGroupId()
        // 如果是编辑周计划，获取周计划学校的班级信息
        if (this.edit) {
          this.$nextTick(() => {
            let center = this.centers.find(x => x.id === this.selectedCenterId)
            // 如果当前学校存在，则获取当前学校的班级信息
            if (center) {
              this.groups = center.groups
              let group = this.groups.find(x => x.id === this.planInfo.groupId)
              // 如果当前班级不存在，则默认选择第一个班级，需要重新选择周计划的学校班级
              if (!group) {
                this.selectedGroupId = undefined
                this.newCenterId = center.id
                this.newGroupId = center.groups[0].id
                this.selectCenterGroupModelVisible = true
              }
            } else {
              // 如果当前学校不存在，则默认选择第一个学校及第一个学校的第一个班级作为默认，需要重新选择周计划的学校班级
              this.selectedCenterId = undefined
              this.selectedGroupId = undefined
              this.groups = this.centers[0].groups
              this.newCenterId = this.centers[0].id
              this.newGroupId = this.centers[0].groups[0].id
              this.selectCenterGroupModelVisible = true
            }
          })
        }
      }).catch(error => {})
    },

    /**
     * 保存新的学校班级
     */
    saveNewCenterGroup () {
      this.currentCenterId = this.newCenterId
      this.currentGroupId = this.newGroupId
      this.selectedCenterId = this.newCenterId
      this.selectedGroupId = this.newGroupId
      this.selectCenterGroupModelVisible = false
      this.$emit('callUpdateBaseInfo', 'UPDATE_GROUP')
    },

    /**
     * 获取班级下老师列表
     */
    getTeachers (groupId) {
      LessonApi.getGroupTeachers({
        groupId: groupId
      }).then(response => {
        if (response && response.length > 0) {
          this.teachers = response.filter(t => !t.role || t.role.toLowerCase() != 'family_service')
        }
        // 如果planData的老师不存在于当前 teachers，则添加进去
        if (this.planInfo.teachers) {
          this.planInfo.teachers.forEach(t => {
            if (!this.teachers.find(item => item.id.toUpperCase() === t.id.toUpperCase())) {
              this.teachers.push({
                id: t.id,
                name: t.displayName
              })
            }
          })
        }
        // 如果当前 teachers 中不存在当前用户，则添加
        if (!this.teachers.find(item => item.id.toUpperCase() === this.currentUserId.toUpperCase())) {
          this.teachers.push({
            id: this.currentUserId,
            name: this.currentUser.display_name
          })
        }
        if (this.planInfo.batchId) {
          if (!this.currentTeacherIds.find(x => x.toUpperCase() === this.planInfo.applyUserId)) {
            this.currentTeacherIds.push(this.planInfo.applyUserId)
          }
        }
      }).catch(error => {})
    },

    // 返回周计划列表
    async goBack () {
      let exist = await this.leaveCheck()
      if (!exist) {
        return
      }
      // 编辑状态，更新编辑锁定信息
      if (this.edit && this.planInfo.id) {
        LessonApi.unlockEditing({
          id: this.planInfo.id
        })
      }
      // 如果过是管理员查看，跳转到管理员分配页面
      if (this.showOpenRate) {
        this.$router.push({
          name: 'assigned-plan'
        })
      } else {
        // 如果过是查看详情，跳转周计划管理页面
        this.$router.push({
          name: 'list-plan'
        })
      }
    },

    // 点击返回按钮检查
    beforeGoBack () {
      // 如果是编辑状态，返回前检查应用记录
      if (this.planInfo.batchId && this.edit && this.planInfo.applyUserId === this.currentUserId) {
        LessonApi.getApplyRecords({ batchId: this.planInfo.batchId })
        .then(res => {
          // 如果有未提交记录，进行提示
          if (res.notSubmit > 0) {
            this.$refs.applyRecordsModal.openDialog(res, false,this.planInfo.batchId)
          } else {
            this.goBack()
          }
        })
      } else {
        this.goBack()
      }
    },

    changeWeek () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_select_week')
      // 更新周计划基本信息
      this.$emit('callUpdateBaseInfo')
      // 更新周次
      this.$emit('callUpdateWeek', this.currentWeek)
    },

    changeDateRange (daterange) {
      this.fromDate = daterange[0]
      this.toDate = daterange[1]
      // 更新周计划基本信息
      this.$emit('callUpdateBaseInfo')
    },

    syncEndDate () {
      if (this.fromDate && this.toDate) {
        if (new Date(this.fromDate).getTime() > new Date(this.toDate).getTime()) {
          // 选择的开始日期在结束日期之后，则将结束日期修改为开始日期 + 4天
          this.toDate = this.$moment(new Date(this.fromDate).getTime() + 4 * 24 * 60 * 60 * 1000).format('MM/DD/YYYY')
        }
      }
    },

    changeGroup (groupId) {
      if (this.hasMeasureChild()) {
        this.$confirm(this.$t('loc.plan58'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          customClass: 'lg-message-box'
        }).then(() => {
          this.currentCenterId = this.selectedCenterId
          this.currentGroupId = this.selectedGroupId
          // 更新周计划基本信息
          this.$emit('callUpdateBaseInfo', 'UPDATE_GROUP')
        }).catch(error => {
          // 将学校班级切换回原来的
          this.selectedGroupId = this.currentGroupId
          this.selectedCenterId = this.currentCenterId
        })
      } else {
        this.currentCenterId = this.selectedCenterId
        this.currentGroupId = this.selectedGroupId
        // this.planInfo.groupId = this.currentGroupId
        // 更新周计划基本信息
        this.$emit('callUpdateBaseInfo', 'UPDATE_GROUP')
      }
    },

    // 切换学校
    changeCenter (centerId) {
        if (this.hasMeasureChild()) {
          this.$confirm(this.$t('loc.plan58'), this.$t('loc.confirmation'), {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel'),
            customClass: 'lg-message-box'
          }).then(() => {
            this.currentCenterId = this.selectedCenterId
            let center = this.centers.find(x => x.id === centerId)
            this.groups = center.groups
            this.selectedGroupId = this.groups[0].id
            this.currentGroupId = this.selectedGroupId
            // 更新周计划基本信息
            this.$emit('callUpdateBaseInfo', 'UPDATE_GROUP')
          }).catch(error => {
            // 将学校班级切换回原来的
            this.selectedGroupId = this.currentGroupId
            this.selectedCenterId = this.currentCenterId
          })
        } else {
          this.currentCenterId = this.selectedCenterId
          let center = this.centers.find(x => x.id === centerId)
          this.groups = center.groups
          this.selectedGroupId = center.groups[0].id
          this.currentGroupId = this.selectedGroupId
          // 更新周计划基本信息
          this.$emit('callUpdateBaseInfo', 'UPDATE_GROUP')
        }
    },

    editPlanGroup () {
      this.editGroupDialogVisable = true
    },

    cancelEditGroup () {
      this.editGroupDialogVisable = false
    },

    hasMeasureChild () {
      if (!this.planInfo || !this.planInfo.categories) {
        return false
      }
      let result = false
      this.planInfo.categories.forEach(category => {
        if (category.items && category.items.length > 0) {
          category.items.forEach(item => {
            if ((item.childIds && item.childIds.length > 0) || (item.measureIds && item.measureIds.length > 0)) {
              result = true
            }
          })
        }
      })
      return result
    },

    changeTeacher (val) {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_select_editor')
      // 更新周计划基本信息
      this.$emit('callUpdateBaseInfo')
    },

    changeLastReflection () {
      // 更新显示上周反思开关
      this.$emit('callChangeLastReflection')
    },

    setDateOptions () {
      this.fromPickerOptions = {
        // 开始日期不能晚于结束日期
        disabledDate: (time) => {
          if (this.toDate) {
            return time.getTime() > new Date(this.toDate).getTime()
          }
        }
      }
      this.toPickerOptions = {
        // 结束日期不能早于开始日期
        disabledDate: (time) => {
          if (this.fromDate) {
            return time.getTime() < new Date(this.fromDate).getTime()
          }
        }
      }
      this.shareFromPickerOptions = {
        // 开始日期不能晚于结束日期
        disabledDate: (time) => {
          if (this.shareToDate) {
            return time.getTime() > new Date(this.shareToDate).getTime()
          }
        }
      }
      this.shareToPickerOptions = {
        // 结束日期不能早于开始日期
        disabledDate: (time) => {
          if (this.shareFromDate) {
            return time.getTime() < new Date(this.shareFromDate).getTime()
          }
        }
      }
    },

    async leaveCheck () {
      // 如果正在录制，提示是否退出录制
      let exist = true
      if (this.planRecording) {
        // 警告提示， 如果退出会终止录制
        await this.$confirm(this.$t('loc.plan142'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
        }).catch(() => {
          exist = false
        })
      }
      return exist
    },

    /**
     * 查看上个周计划
     */
    lastPlan () {
      let lastPlanId = this.planInfo.lastPlanId
      if (!lastPlanId || this.planRecording) {
        return
      }
      this.$router.push({
        name: 'view-plan',
        params: {
          planId: lastPlanId
        }
      })
    },

    /**
     * 查看下个周计划
     */
    nextPlan () {
      let nextPlanId = this.planInfo.nextPlanId
      if (!nextPlanId || this.planRecording) {
        return
      }
      this.$router.push({
        name: 'view-plan',
        params: {
          planId: nextPlanId
        }
      })
    },

    /**
     * 跳转编辑页
     */
    goEdit () {
      // 根据周计划状态发送不同的埋点事件
      if (this.planInfo.status.toUpperCase() === 'D_APPROVED') {
        this.$analytics.sendEvent('web_weekly_plan_detail_approved_edit')
      } else if (this.planInfo.status.toUpperCase() === 'B_PENDING') {
        this.$analytics.sendEvent('web_weekly_plan_detail_pending_edit')
      }
      let routePath = ''
      let params = {}
      // 根据周计划类型跳转不同的编辑页
      if (this.planInfo.type && this.planInfo.type === 'NORMAL_TEMPLATE') {
        routePath = 'edit-agency-template'
        params = {
          planId: this.planInfo.id,
          normal: true
        }
      } else {
        routePath = 'edit-plan'
        params = {
          planId: this.planInfo.id
        }
      }
      this.$router.push({
        name: routePath,
        params
      })
    },

    updateSelectTeacherIds () {
      if (!this.planTeacherIds || this.planTeacherIds.length === 0) {
        return
      }
      if (!this.teachers || this.teachers.length === 0) {

      }
    },

    previewPlan () {
      this.$nextTick(() => {
        this.$refs.previewCalendar.setPreviewData(this.planInfo)
      })
    },

    /**
     * 生成周计划 PDF
     */
    generatePDF (print) {
      this.morePopVisable = false
      // 如果是火狐浏览器，不支持打印
      if (print && tools.isFirefox()) {
        this.$alert(this.$t('loc.plan176'), this.$t('loc.plan135'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('loc.download'),
          customClass: 'el-message-box-font',
          callback: action => {
            if (action == 'confirm') {
              this.generatePDF(false)
            }
          }
        })
        return
      }
      // 根据不同的页面和操作（打印 / 下载），发送不同的事件
      if (this.$route.name == 'edit-plan' && !print) {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_download')
      } else if (this.$route.name == 'edit-plan' && print) {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_print')
      } else if (this.$route.name == 'view-plan' && print) {
        this.$analytics.sendEvent('web_weekly_plan_detail_click_print')
      } else if (this.$route.name == 'view-plan' && !print) {
        this.$analytics.sendEvent('web_weekly_plan_detail_click_download')
      } else if (this.$route.name == 'shared-plan-detail' && print) {
        this.$analytics.sendEvent('web_weekly_virtual_detail_click_print')
      } else if (this.$route.name == 'shared-plan-detail' && !print) {
        this.$analytics.sendEvent('web_weekly_virtual_detail_click_pdf')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_detail_click_pdf')
      }
      // 是否仅显示核心测评点
      let showCore = false
      // 如果显示核心测评点开关，按照开关值设置，否则显示全部测评点
      if (this.planInfo.showCoreMeasureOpen && !this.edit && !this.review) {
        showCore = this.showCore
      }
      this.pdfLoading = true
      LessonApi.getPlanPDF({
        planId: this.planInfo.id,
        showCore: showCore
      }).then(response => {
        this.getPlanPDF(print)
      }).catch(error => {})
    },

    /**
     * 获取周计划 PDF
     */
    getPlanPDF (print) {
      LessonApi.getPDFList({
        type: 'WEEKLY_PLAN'
      }).then(response => {
        if (response && response.length > 0) {
          let pdf = response[0]
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getPlanPDF(print)
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED' && print) {
            this.$print({
              printable: pdf.pdfUrl,
              type: 'pdf',
              onLoadingStart: () => {
              },
              onLoadingEnd: () => {
                this.pdfLoading = false
              },
              onError: () => {
                this.downloadPDFWithAlert(pdf)
                this.pdfLoading = false
              }
            })
          } else {
            this.downloadPDFWithAlert(pdf)
            this.pdfLoading = false
          }
        }
      })
    },

    /**
     * PDF 下载周计划 PDF
     */
    downloadPDFWithAlert (pdf) {
      // 成功弹窗下载
      this.$alert('<div class="display-flex align-items"><img style="height: 30px; margin: 5px" src="' + this.getfilePhoto(pdf.pdfName) + '"> <span title="' + pdf.pdfName + '">' + pdf.pdfName + '</span></div>', this.$t('loc.plan61'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download'),
        customClass: 'el-message-box-font',
        callback: action => {
          if (action == 'confirm') {
            if (this.$route.name == 'edit-plan') {
              this.$analytics.sendEvent('web_weekly_plan_edit_click_pop_download')
            } else if (this.$route.name == 'view-plan') {
              this.$analytics.sendEvent('web_weekly_plan_detail_click_pop_download')
            }
            if (tools.isComeFromIPad()) {
              let requestData = {
                'emailTemplate': 'weekly_lesson_planning',
                'downloadFileUrl': pdf.pdfUrl,
                'fileName': pdf.pdfName,
                'week': this.planInfo.week,
                'className': this.planInfo.groupName,
                'siteName': this.planInfo.centerName,
                'fromDate': this.planInfo.fromAtLocal,
                'toDate': this.planInfo.toAtLocal,
                'courseName': this.planInfo.theme
              }
              this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
              .then(() => {
                this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                  confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                  showCancelButton: false
                })
              }).catch(error => {
                this.$message.error(error.message)
              })
            } else {
              const eleLink = document.createElement('a')
              eleLink.style.display = 'none'
              eleLink.target = '_blank'
              eleLink.href = pdf.pdfUrl
              // 触发点击
              document.body.appendChild(eleLink)
              eleLink.click()
              // 移除
              document.body.removeChild(eleLink)
            }
          }
        }
      })
    },

    /*
    * 显示复制周计划弹窗
    */
    showReplicatePlanDialog () {
      this.replicateModelVisible = true
      this.$analytics.sendEvent('web_weekly_plan_edit_click_replicate')
    },

    /**
     * 复制周计划
     */
    replicatePlan () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_replicate_confirm')
      this.replicateLoading = true
      this.morePopVisable = false
      let groupId = this.planInfo.groupId
      let name = 'edit-plan'
      // 判断复制的是什么类型的
      if (this.planInfo.type == 'NORMAL_TEMPLATE' && this.isAdmin) {
        groupId = ''
        name = 'edit-agency-template'
      } else if (this.planInfo.type == 'NORMAL' && this.isAdmin) {
        groupId = this.planInfo.groupId
      }
      LessonApi.replicatePlan({
        planId: this.planInfo.id,
        groupId: groupId
      }).then(response => {
        this.replicateLoading = false
        this.replicateModelVisible = false
        this.$router.push({
          name: name,
          params: {
            planId: response.id,
            replicate: true
          }
        })
      }).catch(error => {
        this.replicateLoading = false
        this.replicateModelVisible = false
      })
    },

    /**
     * 打开 DLL 语言设置弹窗
     */
    openDllSettings() {
      this.$refs.dllSettings.openSettingDialog()
    },

    showAssignTeacherGroups () {
        this.$refs.assignTeacherGroups.showAssignTeacherGroups(this.selectedGroupId, this.selectedCenterId)
        // 添加 Unit Planner 设置的埋点
        this.$analytics.sendEvent('web_weekly_plan_edit_click_atg')
    },
    showMixedAgeDifferentiation () {
      this.$refs.mixedAgeDifferentiation.showMixedAgeDifferentiation(this.selectedGroupId, this.selectedCenterId)
      // session storage 中存储一个数据
      this.triggerNewPointer = true
      sessionStorage.setItem('showMixedAgeDifferentiation' + this.currentUserId, 'true')
    },
    // 管理小孩
    manageClassRoomDemographics () {
      if (!this.canAdapter) {
        window.open('/#/manage_children')
      } else {
        // 跳转路由
        let { href } = this.$router.resolve({
          name: 'manageChildren',
          query: {
            centerId: this.selectedCenterId,
            groupId: this.selectedGroupId
          }
        })
        // 打开新标签页
        window.open(href, '_blank')
      }
      // 添加 Unit Planner 设置的埋点
      this.$analytics.sendEvent('web_weekly_plan_edit_click_manage_demogra')
    },
    /**
     * 删除周计划
     */
    deletePlan () {
      this.morePopVisable = false
      this.$confirm(this.$t('loc.plan11'), this.$t('loc.cfm'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        if (this.$route.name == 'edit-plan') {
          this.$analytics.sendEvent('web_weekly_plan_edit_click_delete')
        } else if (this.$route.name == 'view-plan') {
          this.$analytics.sendEvent('web_weekly_plan_detail_click_delete')
        } else if (this.$route.name == 'shared-plan-detail') {
          this.$analytics.sendEvent('web_weekly_virtual_detail_click_delete')
        }
        LessonApi.deletePlan({}, {
          id: this.planInfo.id
        }).then(response => {
          this.$message({
            type: 'success',
            message: this.$t('loc.plan12')
          })
          this.$router.push({
            name: 'list-plan'
          })
        })
      }).catch(() => {
      })
    },

    /**
     * 获取文件类型 icon
     */
    getfilePhoto (fileName) {
      if (fileName.endsWith('doc')) {
        return doc
      }
      if (fileName.endsWith('docx')) {
        return docx
      }
      if (fileName.endsWith('pdf')) {
        return pdf
      }
      if (fileName.endsWith('ppt')) {
        return ppt
      }
      if (fileName.endsWith('pptx')) {
        return pptx
      }
      if (fileName.endsWith('xls')) {
        return xls
      }
      if (fileName.endsWith('xlsx')) {
        return xlsx
      }
      return file
    },

    /**
     * 获取管理的所有老师
     */
    getAllTeachers () {
      this.$axios.get($api.urls().getCentersWithTeachers)
      .then(res => {
        // 防止同一个老师出现在不同学校时id重复导致el-tree组件的node-key不唯一，添加treenodeId字段
        let treeNodeId = 1
        let centers = []
        // 循环学校，添加nodeId
        res.centers.forEach(center => {
          center.treeNodeId = treeNodeId
          treeNodeId++
          let id = center.centerId
          let name = center.centerName
          // 循环老师，添加nodeId
          center.teachers.forEach(teacher => {
            teacher.treeNodeId = treeNodeId
            treeNodeId++
          })
          let teachers = center.teachers
          let data = { 'id': id, 'treeNodeId': center.treeNodeId, 'name': name, 'teachers': teachers }
          centers.push(data)
        })
        this.allTeachers = centers
        // treeData作为展示数据
        let teachers = JSON.parse(JSON.stringify(centers))
        if (this.creatorUserId) {
          teachers.forEach(x => {
            x.teachers = x.teachers.filter(y => y.id !== this.creatorUserId)
          })
          // 过滤完创建者后过滤空学校
          teachers = teachers.filter(c => c.teachers.length > 0)
        }
        this.treeData = teachers
      })
      .catch(error => {
        this.$message.error(error)
      })
    },

    // 全选老师
    isSelectAll (flag) {
      if (flag) {
        let keys = []
        // 将节点全选
        this.treeData.forEach(center => {
          keys.push(center.treeNodeId)
          center.teachers.forEach(teacher => {
            keys.push(teacher.treeNodeId)
          })
        })
        this.selectAll = true
        this.$refs.tree.setCheckedKeys(keys)
      } else {
        this.selectAll = false
        this.$refs.tree.setCheckedKeys([])
        this.selectedData = []
      }
    },

    // 勾选要分享的老师
    handleCheckChange () {
      // 已经勾选的数据
      let selected = this.$refs.tree.getCheckedNodes()
      let selectedKeys = []
      selected.forEach((key) => {
        if (!key.teachers) {
          selectedKeys.push(key.id)
        }
      })
      // 判断是否全选
      let allKeys = 0
      this.treeData.forEach(center => {
          allKeys += center.teachers.length
        })
      if (selectedKeys.length == allKeys) {
        this.selectAll = true
      } else {
        this.selectAll = false
      }
      // 将勾选的数据添加进去
      this.selectedData = selectedKeys
    },
    // 点击分享按钮
    beforeShare () {
      this.$analytics.sendEvent('web_weekly_plan_detail_click_create_vir')
      if (this.treeData.length === 0 || (this.treeData.length === 1 && this.treeData[0].teachers.length === 1 && this.treeData[0].teachers[0].id === this.creatorUserId)) {
        this.$message.error('Please add a teacher first.')
        return
      }
      this.shareModalVisible = true
    },
    // 分享，给按钮加loading
    share () {
      // 判断必选项是否选择了
      // 创建者判断
      if (!this.creatorUserId) {
        this.showSelectCreatorTip = true
        return
      }
      this.showSelectCreatorTip = false
      // 分享类型判断
      if (!this.shareType) {
        this.showSelectShareTypeTip = true
        return
      } else {
        // 如果是部分，时段必选
        if (this.shareType === 'PART' && (!this.shareFromDate || !this.shareToDate)) {
            return
        }
      }
      this.showSelectShareTypeTip = false
      // 分享人判断
      if (this.selectedData.length == 0) {
        this.showSelectShareWithTip = true
        return
      }
      this.showSelectShareWithTip = false
      // 所选的周计划创建者无周计划或者所选时间段内无周计划时不可分享
      if (this.noPlansTip) {
        return
      }
      this.shareLoading = true
      // 调接口保存
      this.$axios.post($api.urls().sharePlan, {
        shareUserId: this.creatorUserId,
        type: this.shareType,
        fromDate: this.$moment(this.shareFromDate).format('YYYY-MM-DD'),
        toDate: this.$moment(this.shareToDate).format('YYYY-MM-DD'),
        sharedUserIds: this.selectedData,
        comment: this.comment
      })
      .then(res => {
        this.shareLoading = false
        this.$message.success(this.$t('loc.shareSuccess'))
        this.cancelShare()
      })
    },
    // 取消分享,清空勾选的选项,关闭弹窗
    cancelShare () {
      // 关闭弹窗
      this.shareModalVisible = false
      // 清空选项
      this.noPlansTip = false
      // 如果是多个创建者，关闭弹窗时取消选择
      if (this.creators.length > 1) {
        this.creatorUserId = ''
      }
      this.shareType = ''
      this.isSelectAll(false)
      this.comment = ''
      this.shareFromDate = ''
      this.shareToDate = ''
      this.showSelectCreatorTip = false
      this.showSelectShareTypeTip = false
      this.showSelectShareWithTip = false
    },
    // 获取时间范围内周计划数
    getPlanCount () {
      if (!this.creatorUserId) {
        return
      }
      if (!this.shareType) {
        return
      }
      if (this.shareType == 'PART' && (!this.shareFromDate || !this.shareToDate)) {
        return
      }
      this.$axios.get($api.urls().getUserPlanCount,{
        params: {
          fromDate: this.shareFromDate ? this.$moment(this.shareFromDate).format('YYYY-MM-DD') : undefined,
          shareUserId: this.creatorUserId,
          toDate: this.shareToDate ? this.$moment(this.shareToDate).format('YYYY-MM-DD') : undefined,
          type: this.shareType
          }
        })
      .then(res => {
        // 根据周计划数量判断是否显示提示语
        if (res.count == 0) {
          this.noPlansTip = true
        } else {
          this.noPlansTip = false
        }
      })
    },
    // 获取周计划已读未读统计
    getSharedReadStats () {
      this.loadingReadStats = true
      this.$axios.get($api.urls().getSharedReadStats + '?planId=' + this.planId + '&shareId=' + this.shareId)
      .then(res => {
        this.openStats = res
        this.showOpenRate = true
        this.loadingReadStats = false
        // 如果是点击打开率跳转的详情，直接自动打开统计popover
        if (this.$route.query.param === 'openRate') {
          this.$nextTick(() => {
            setTimeout(() => {
              this.openRatePop = true
            }, 200)
          })
        }
      })
    },
    closeKeyboard () {
      document.activeElement.blur()
    },
    async goToCurriculum () {
      // 如果正在录制，提示是否退出录制
      if (this.planRecording) {
        this.$message.warning(this.$t('loc.plan145'))
        return
      }
      this.$router.push({
        name: 'curriculumUnitDetail',
        params: {
          unitId: this.curriculumInfo.unitId,
          curriculumId: this.curriculumInfo.curriculumId,
          weekNum: this.curriculumInfo.unitPlanNumber,
          planId: this.planInfo.id,
          edit: this.edit,
          frameworkId: null,
          curriculumName: null
        }
      })
    },
    // 设置选中的学校以及班级 ID
    setSelectedCenterIdAndGroupId () {
      if (!this.planInfo) {
        return
      }
      // 是否显示核心测评点开关，如果显示则开关值置为true
      this.showCore = this.planInfo.showCoreMeasureOpen
      // 周计划的学校信息
      this.currentCenterId = this.planInfo.centerId
      // 学校选择框赋值
      this.selectedCenterId = this.planInfo.centerId
      // 周计划的班级信息
      this.selectedGroupId = this.planInfo.groupId
      // 班级选择框赋值
      this.currentGroupId = this.planInfo.groupId
      // 如果是编辑周计划，判断是否需要重新选择学校班级
      this.currentTeacherIds = this.planInfo.teacherIds
      this.currentWeek = this.planInfo.week
      this.fromDate = this.planInfo.fromAtLocal
      this.toDate = this.planInfo.toAtLocal
      this.dataRange = [this.planInfo.fromAtLocal, this.planInfo.toAtLocal]
      this.showLastReflection = this.planInfo.showLastReflection
      if (this.$route.query.param) {
        // 获取已读未读统计
        this.getSharedReadStats()
      }
    },

    /**
     * 判断是否需要显示设置引导
     */
    judgeNeedSettingGuide () {
        // 如果不是 老师且未开启 ，就直接 return
        if (!(this.showAdapterFeature)) {
          // 开启周计划 AI 生成课程引导
          this.$store.dispatch('setPlanGenerateLessonGuide', true)
          return
        }
        // 先获取浏览器缓存
        let weeklyPlannerSettingGuideFeatures = tools.localItem(store.state.user.uid + '_WEEKLY_PLANNER_SETTING_GUIDE')
        // 如果缓存中没有
        if (weeklyPlannerSettingGuideFeatures === undefined || weeklyPlannerSettingGuideFeatures === null) {
            // 发送查询请求
            this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
                if (result !== null && result.weeklyPlannerSettingGuide) {
                    this.adaptUDLAndCLROpen = true
                    // 如果 editPlan 已经引导过来，那么就开始引导，否则等到对方引导完
                    if (this.editPlanGuided) {
                        this.settingGuide.showSettingGuide = true
                    }
                    tools.localItem(store.state.user.uid + '_WEEKLY_PLANNER_SETTING_GUIDE', 'true')
                } else {
                    this.settingGuide.showSettingGuide = false
                    tools.localItem(store.state.user.uid + '_WEEKLY_PLANNER_SETTING_GUIDE', 'false')
                    this.$emit('callShowBatchAdaptLessonsGuide')
                }
                // 如果请求为空或者请求中需要展示周计划设置引导
                this.settingGuide.showSettingGuide = !!(result !== null && result.weeklyPlannerSettingGuide) && this.editPlanGuided
            })
        } else {
            // 如果浏览器缓存中有，则解析浏览器中缓存的值，判断是否需要展示
            if ((weeklyPlannerSettingGuideFeatures.toLowerCase() === 'true')) {
                // 如果 editPlan 已经引导过来，那么就开始引导，否则等到对方引导完
                if (this.editPlanGuided) {
                    this.settingGuide.showSettingGuide = true
                }
            } else {
                this.settingGuide.showSettingGuide = false
                this.$emit('callShowBatchAdaptLessonsGuide')
            }
        }
    },

    /**
     * 关闭设置引导
     */
    endSettingGuide () {
        // 如果在引导状态下
        if (this.settingGuide.showSettingGuide) {
            tools.localItem(store.state.user.uid + '_WEEKLY_PLANNER_SETTING_GUIDE', 'false')
            // 不展示引导
            this.settingGuide.showSettingGuide = false
            // 同时告诉后台,引导已经完成了
            let result = { 'features' : [ 'WEEKLY_PLANNER_SETTING_GUIDE' ] }
            this.$axios.post($api.urls().hideGuide, result).then()
            // 先弹出 Settings 的引导，然后再弹出批量改编课程的引导
            this.$emit('callShowBatchAdaptLessonsGuide')
        }
    }
  },

  watch: {
    // 监听是否显示核心测评点开关值变化
    showCore (val) {
      this.$bus.$emit('changeShowCore', { planId: this.planInfo.id, open: val })
      if (this.planInfo.type == 'NORMAL') {
        if (val) {
          this.$analytics.sendEvent('web_weekly_class_open_core_measure')
        } else {
          this.$analytics.sendEvent('web_weekly_class_close_core_measure')
        }
      } else {
        if (val) {
          this.$analytics.sendEvent('web_weekly_agency_open_core_measure')
        } else {
          this.$analytics.sendEvent('web_weekly_agency_close_core_measure')
        }
      }
    },
    selectedGroupId (val) {
      if (val) {
        // 如果 val 是存在的，那么就将其保存到 sessionStorage 中， key 是通过 currentUserId 进行拼接的
        sessionStorage.setItem('selectedGroupId' + this.currentUserId, val)
        // 从 groups 中获取当前班级 Id 对应的班级 name
        this.$nextTick(() => {
          let currentGroup = this.groups.find(x => x.id === val)
          // 如果当前班级存在，那么就将班级名称也随着保存到 sessionStorage 中
          if (currentGroup) {
            sessionStorage.setItem('selectedGroupName' + this.currentUserId, currentGroup.name)
          }
        })
        // 使用 $bus 发送事件
        this.$bus.$emit('updateSelectedGroupId')
      }
    },
    selectedCenterId (val) {
       if (val) {
         // 如果 val 是存在的，那么就将其保存到 sessionStorage 中， key 是通过 currentUserId 进行拼接的
         sessionStorage.setItem('selectedCenterId' + this.currentUserId, val)
         // 使用 $bus 发送事件
         this.$bus.$emit('updateSelectedCenterId')
       }
    },
    currentGroupId (val) {
      val && this.getTeachers(val)
    },

    creatorUserId (val) {
      // 将创建者从分享人员中过滤掉
      let newAllTeachers = JSON.parse(JSON.stringify(this.allTeachers))
      newAllTeachers.forEach(x => {
        x.teachers = x.teachers.filter(y => y.id !== val)
      })
      // 过滤完创建者后过滤空学校
      this.treeData = newAllTeachers.filter(c => c.teachers.length > 0)
      // 查询plan个数
      this.getPlanCount()
    },

    shareType (newVal, oldVal) {
      // 种类切换时从all切换到part清空选择的时间
      if (oldVal === 'ALL') {
        this.shareFromDate = ''
        this.shareToDate = ''
      }
      this.getPlanCount()
    },

    // 监听 PDF 下载, 加载时页面loading
    pdfLoading (val) {
      this.$emit('callDownloading', val)
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-select__tags {
   max-width: 170px!important;
}
.el-divider--horizontal {
  margin: 12px 0px !important;
}
/deep/ .el-link--inner {
  display: flex;
  align-items: center;
}
@media only screen and (max-width:1199px){
  //ipad
  .custom-tree-node {
    display: flex;
    height: 30px;
    flex-direction: row;
    align-items: center;
  }
  .tree-node-img {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    vertical-align: middle;
    margin-right: 5px;
  }
  .flex-col-center {
    display: flex;
    align-items: center;
  }
  #share-card-body {
    /deep/ .el-dialog__body {
      border-top: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
      padding:10px 20px !important;
    }
    /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
      width: 200px !important;
    }
  }
  /deep/ .el-tree-node__content {
    height: 30px;
    label {
      margin-bottom: 0px;
    }
  }
  .header {
    position: relative;
    // top: 0;
    // z-index: 5;
    // background-color: #f0f3f4;
    padding: 10px 5px 10px 10px;
  }
  .filter-container {
    min-height: 70px;
    width: 270px;
    padding-right: 1px;
    align-items: center;
    justify-content: flex-start;
  }
  .class-area {
    display: flex;
    flex-direction: column;
  }
  .teacher-area {
    display: flex;
    flex-direction: column;
    .teacher-select {
      width: calc(100% - 100px);
      min-width: 150px;
    }
  }
  .week-container {
    //position: absolute;
    //top: 5px;
    display: flex;
    left: calc(50% - 90px);
    flex-direction: column;
    align-items: center;
  }
  .display-week {
    min-height: 20px;
  }
  .display-week-container {
    //position: absolute;
    //top: 5px;
    left: calc(50% - 100px);
    i {
      font-size: 24px;
    }
  }
  .display-week {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .from-to-picker {
    width: 120px;
    /deep/ .el-input__inner {
      padding-right: 0;
    }
  }
  .goback {
    max-width: 24px;
  }

  .full-modal-area {
    /deep/ .el-dialog__wrapper {
      padding: 2px;
    }

    /deep/ .full-modal {
      border-radius: 0 !important;
    }

    /deep/ .el-dialog__header {
      height: 0;
      padding: 0;

      & > :first-child {
        height: 56px;
        line-height: 56px;
        font-size: 16px;
        text-align: right;
        margin-right: 60px;
      }

      .el-dialog__headerbtn {
        position: fixed;
        width: 55px;
        height: 55px;
        border-radius: 0 0 0 100%;
        background-color: rgba(0, 0, 0, 0.55);
        top: 0;
        right: 0;
        font-size: 40px;
        color: #fff;
        z-index: 100;

        & > .el-dialog__close {
          position: relative;
          top: -5px;
          right: -5px;
          color: #fff;
        }
      }
    }

    /deep/ .el-dialog__body {
      padding: 0;
    }
  }
  .ipad_padding_right_35{
    padding-right: 35px;
  }
  .max-width-120 {
    max-width: 120px;
  }
  .ipad_margin_right{
    margin-right: 0px;
    font-size: 14px;
  }
}
// ipad 端展示时间居中显示
@media only screen and (max-width: 1200px) {
  .appear-time {
   text-align: center!important;
  }
}
// ipad 端隐藏跳转
@media only screen and (max-width: 1200px) {
  .disappearJumpUp {
    display: none!important;
  }
}

// ipad 端核心测评点开关标题自动换行
@media only screen and (max-width:1200px) {
  .switch-title {
    white-space: break-spaces!important;
  }
}

@media only screen and (max-width: 1200px) {
  .ipad-adapter {
    flex-direction: column;
    justify-content: center;
  }
  .m-l-md {
    margin-left: 0px!important;
  }
}

@media only screen and (min-width:1200px){
  //web
  .custom-tree-node {
    display: flex;
    height: 30px;
    flex-direction: row;
    align-items: center;
  }
  .tree-node-img {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    vertical-align: middle;
    margin-right: 5px;
  }
  .flex-col-center {
    display: flex;
    align-items: center;
  }
  #share-card-body {
    /deep/ .el-dialog__body {
      //border-top: 1px solid #ccc;
      //border-bottom: 1px solid #ccc;
      padding:10px 20px !important;
    }
    /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
      width: 200px !important;
    }
  }
  /deep/ .el-tree-node__content {
    height: 30px;
    label {
      margin-bottom: 0px;
    }
  }
  .header {
    position: relative;
    // top: 0;
    // z-index: 5;
    // background-color: #f0f3f4;
    padding: 10px 30px 10px 15px;
  }
  .filter-container {
    min-height: 70px;
    padding-right: 120px;
    align-items: center;
    justify-content: flex-start;
  }
  .teacher-area {
    flex: auto;
    .teacher-select {
      width: calc(100% - 100px);
      min-width: 180px;
      max-width: 205px;
    }
  }
  .week-container {
    position: absolute;
    top: 5px;
    left: calc(50% - 110px);
    flex-direction: column;
    align-items: center;
  }
  .display-week {
    min-height: 20px;
  }
  .display-week-container {
    position: absolute;
    top: 5px;
    left: calc(50% - 100px);
    i {
      font-size: 24px;
    }
  }
  .display-week {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .from-to-picker {
    width: 120px;
    /deep/ .el-input__inner {
      padding-right: 0;
    }
  }
  .goback {
    max-width: 24px;
  }

  .max-width-120 {
    max-width: 120px;
  }

  .full-modal-area {
    /deep/ .el-dialog__wrapper {
      padding: 2px;
    }

    /deep/ .full-modal {
      border-radius: 0 !important;
    }

    /deep/ .el-dialog__header {
      height: 0;
      padding: 0;

      & > :first-child {
        height: 56px;
        line-height: 56px;
        font-size: 16px;
        text-align: right;
        margin-right: 60px;
      }

      .el-dialog__headerbtn {
        position: fixed;
        width: 55px;
        height: 55px;
        border-radius: 0 0 0 100%;
        background-color: rgba(0, 0, 0, 0.55);
        top: 0;
        right: 0;
        font-size: 40px;
        color: #fff;
        z-index: 100;

        & > .el-dialog__close {
          position: relative;
          top: -5px;
          right: -5px;
          color: #fff;
        }
      }
    }

    /deep/ .el-dialog__body {
      padding: 0;
    }
  }
  .web_padding_right {
    padding-right: 8px;
  }
  .teacherTurnBtn {
    color: #10B3B7;
    background-color: white;
  }
  .adminTurnBtn {
    color: #10B3B7;
    border-color: #10B3B7;
  }
}
.unit-info-button {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/deep/.edit-group-dialog {
  .el-select {
    width: 100%
  }
  .el-dialog__body {
    padding: 10px 20px;
  }
}
.week-select-start-end-date {
  width: 240px;
  /deep/ .el-range__icon {
    color: #606060!important;
  }
}
.week-select-start-end-date /deep/.el-range__close-icon {
  display: none;
}
.teacher-select /deep/  .el-select__tags-text {
  max-width: 80px !important;
}
.action-divider {
  margin: 8px 0px !important;
}
.action-delete:hover:not(.is-disabled) {
  color: #f56c6c !important;
}
/deep/ .edit-plan-dialog {
  border-radius: 8px;
  .el-dialog__title {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #111c1c;
  }
  .el-dialog__body {
    padding: 10px 20px;
    color: #111c1c;
  }
  .el-select {
    width: 100%;
  }
  .center-group-background {
    background: #f5f6f8;
    padding: 20px;
  }
  .edit-plan-text {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #111c1c;
    margin-bottom: 8px;
  }
}
.settings-style{
    padding: 8px 16px;
    line-height: 24px;
}

/deep/ .el-popper.adapt-UDL-and-CLR-guide-color-text {
    left: unset !important;
    right: 10px !important;
}
</style>
<style lang="less">

</style>
