<template>
  <div class="h-full bg-white position-relative border-radius-4">
    <div class="position-absolute white-board display-flex">
      <div style="width: 522px;font-size: 16px">
        <div class="font-size-24 font-weight-600 lg-margin-bottom-20">{{ $t('loc.lesson2TabName3') }}</div>
        <div class="lg-margin-bottom-20">{{ $t('loc.plan160') }}</div>
        <ul class="lg-margin-left-20 lg-margin-bottom-20">
          <li>{{ $t('loc.plan161') }}</li>
          <li>{{ $t('loc.plan162') }}</li>
          <li>{{ $t('loc.plan163') }}</li>
        </ul>
        <el-button @click="createWeeklyPlan" class='el-button-warning-dark'><i class="el-icon-plus lg-margin-right-8"></i> {{ $t('loc.create') }} </el-button>
        <el-button plain @click="jumpUrl">{{ $t('loc.more') }}</el-button>
      </div>
      <div class="lg-margin-left-20">
        <img src="~@/assets/img/lesson2/white-board.png" alt="white-board">
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { acrossRole, getCurrentUser } from '@/utils/common'

export default {
  name: 'WeeklyPlanWhiteBoard',
  props: {
    centers: {
      type: Array
    },
    defaultGroupId: {
      type: String
    }
  },
  data () {
    return {
      createPlanType: '',
      createPlanFrameworkId: '',
      createPlanGroupId: ''
    }
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser() // 当前用户
    }),
    knowledgeBaseUrl () {
      // 根据不同的角色跳转到不同的链接
      let roleKnowledgeUrl = {
        'admin': 'https://learninggenie.zendesk.com/hc/en-us/articles/18462755955732',
        'teacher': 'https://learninggenie.zendesk.com/hc/en-us/articles/17867134565780--For-Teachers-Reflective-Planning-Creating-a-Weekly-Planner'
      }
      let role = this.currentUser.role
      // 根据角色跳转不同的路径
      return role && role.toUpperCase() === 'OWNER' ? roleKnowledgeUrl.admin : roleKnowledgeUrl.teacher
    }
  },
  methods: {
    createWeeklyPlan () {
      // 添加埋点
      this.$analytics.sendEvent('web_weekly_plan_home_click_create')
      if (this.centers.length === 0 || this.centers[0].groups.length === 0) {
        if (acrossRole('AGENCY_OWNER')) {
          this.$message.warning(this.$t('loc.ownerTip'))
        }
        if (acrossRole('SITE_ADMIN')) {
          this.$message.warning(this.$t('loc.siteTip'))
        }
        if (acrossRole('COLLABORATOR')) {
          this.$message.warning(this.$t('loc.teacherTip'))
        }
        return
      }
      this.createPlan()
    },
    jumpUrl () {
      this.$analytics.sendEvent('web_weekly_plan_home_click_learn_more')
      window.open(this.knowledgeBaseUrl)
    },
    // 创建周计划
    createPlan () {
      let query = {}
      let routeName = 'edit-plan'
      // 老师角色创建班级周计划
      query.defaultGroupId = this.defaultGroupId
      this.$router.push({
        name: routeName,
        params: {
          planId: 'new'
        },
        query: query
      })
    }
  }
}
</script>

<style scoped lang="less">
.white-board {
  left: 50%;
  top: 20%;
  transform: translate(-50%);
  ul {
    list-style: auto;
    li {
      list-style: disc!important;
    }
  }
}
</style>
