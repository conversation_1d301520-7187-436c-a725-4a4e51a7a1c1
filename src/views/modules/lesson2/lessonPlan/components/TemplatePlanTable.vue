<template>
  <div class="display-flex flex-direction-col h-full" v-loading="pageLoading">
    <!-- 周计划表格 -->
    <div class="bg-white flex-auto lg-padding-20 lg-box-shadow lg-border-radius-8">
      <el-table
        :data="plans"
        header-row-class-name="table-header"
        @sort-change="sortChange"
        @row-click="viewPlan"
        row-class-name="lg-pointer"
        v-loading="tableLoading && !pageLoading"
        class="lg-form"
        ref="table"
        style="width: 100%">
        <el-table-column
          :label="$t('loc.planTheme')"
          min-width="200">
          <template slot-scope="scope">
            <span v-if="scope.row.theme" :title="scope.row.theme" class="overflow-ellipsis w-full">{{scope.row.theme}}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('loc.plan110')"
          min-width="150">
          <template slot-scope="scope">
            <span>{{ getFrameworkName(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('loc.curriculum50')"
          width="200">
          <template slot-scope="scope">
            <div class="overflow-ellipsis w-full" :title="scope.row.creteUserName | formatUserName">{{scope.row.creteUserName | formatUserName}}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="isAdmin"
          :column-key="'Status'"
          :label="$t('loc.planStatus')"
          width="100">
          <template slot-scope="scope">
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'A_DRAFT'" type="info">{{$t('loc.pDraft')}}</el-tag>
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'C_REJECTED'" type="danger">{{$t('loc.planRejected')}}</el-tag>
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'B_PENDING'" type="warning">{{$t('loc.approvalPENDING')}}</el-tag>
            <el-tag size="medium" :disable-transitions=true v-if="scope.row.status == 'D_APPROVED'" type="success">{{$t('loc.curriculum51')}}</el-tag>
          </template>
        </el-table-column>
        <!-- 管理员角色操作菜单-->
        <el-table-column
          v-if="isAdmin"
          fixed="right"
          :label="$t('loc.plan9')"
          width="150">
          <template slot-scope="scope">
            <div class="display-flex align-items">
              <!-- 编辑按钮 -->
              <el-tooltip v-if="scope.row.canEditOrDelete" class="item" effect="dark" :content="$t('loc.edit')" placement="top">
                <el-link
                  :underline="false"
                  @click.stop="edit(scope.row)"> <i class="font-size-24 lg-icon lg-icon-edit"></i> </el-link>
              </el-tooltip>
              <!-- 复制 -->
              <el-tooltip v-else class="item" effect="dark" :content="$t('loc.plan66')" placement="top">
                <el-link
                  :underline="false"
                  @click.stop="replicateConfirm(scope.row)"> <i class="font-size-24 lg-icon lg-icon-copy"></i> </el-link>
              </el-tooltip>
              <el-popover
                placement="bottom"
                width="150"
                v-model="scope.row.showPop"
                trigger="click">
                <div>
                  <template v-if="scope.row.canEditOrDelete">
                    <!-- 复制按钮 -->
                    <div class="act_div_wapper">
                      <el-link
                        :underline="false"
                        @click.stop="replicateConfirm(scope.row)">
                        <i class="font-size-24 lg-icon lg-icon-copy lg-margin-right-16"></i>
                        <span>{{$t('loc.plan66')}}</span>
                      </el-link>
                    </div>
                    <el-divider class="action-divider"></el-divider>
                  </template>
                  <!-- PDF 下载 -->
                  <div class="act_div_wapper">
                    <el-link
                      :underline="false"
                      @click.stop="generatePDF(scope.row.id, false)">
                      <i class="font-size-24 lg-icon lg-icon-download lg-margin-right-16"></i>
                      <span>{{$t('loc.download')}}</span>
                    </el-link>
                  </div>
                  <!-- PDF 打印 -->
                  <div class="act_div_wapper">
                    <el-link
                      :underline="false"
                      @click.stop="generatePDF(scope.row.id, true)">
                      <i class="font-size-24 lg-icon lg-icon-print lg-margin-right-16"></i>
                      <span>{{$t('loc.plan174')}}</span>
                    </el-link>
                  </div>
                  <el-divider class="action-divider"></el-divider>
                  <!-- 删除 -->
                  <div class="act_div_wapper">
                    <el-link
                      class="action-delete"
                      :underline="false"
                      :disabled="!scope.row.canEditOrDelete || scope.row.lockedData"
                      @click.stop="deletePlan(scope.row)">
                      <i class="font-size-24 lg-icon lg-icon-delete lg-margin-right-16"></i>
                      <span>{{$t('loc.delete')}}</span>
                    </el-link>
                  </div>
                </div>
                <el-button style="border: 0;margin-left: 10px; height: 24px; width: 24px; padding: 0px !important;" size="mini" icon="el-icon-more" slot="reference" @click.stop="showPop(scope.row)"></el-button>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <!-- 老师角色操作菜单-->
        <el-table-column
          v-if="!isAdmin"
          fixed="right"
          :label="$t('loc.plan9')"
          :width="lang && lang.toLowerCase().indexOf('en') != -1 ? 150 : 180">
          <template slot-scope="scope">
            <!-- 复制周计划 -->
            <el-link
              :underline="false"
              @click.stop="replicateConfirm(scope.row)">
              <i class="font-size-24 lg-icon lg-icon-copy"></i>
            </el-link>
            <el-popover
              placement="bottom"
              width="150"
              v-model="scope.row.showPop"
              trigger="click">
              <div>
                <!-- PDF 下载 -->
                <div class="act_div_wapper">
                  <el-link
                    :underline="false"
                    @click.stop="generatePDF(scope.row.id, false)">
                    <i class="font-size-24 lg-icon lg-icon-download lg-margin-right-16"></i>
                    <span>{{$t('loc.download')}}</span>
                  </el-link>
                </div>
                <!-- PDF 打印 -->
                <div class="act_div_wapper">
                  <el-link
                    :underline="false"
                    @click.stop="generatePDF(scope.row.id, true)">
                    <i class="font-size-24 lg-icon lg-icon-print lg-margin-right-16"></i>
                    <span>{{$t('loc.plan174')}}</span>
                  </el-link>
                </div>
              </div>
              <el-button style="border: 0;margin-left: 10px; height: 24px; width: 24px; padding: 0px !important;" size="mini" icon="el-icon-more" slot="reference" @click.stop="showPop(scope.row)"></el-button>
            </el-popover>
          </template>
        </el-table-column>
        <div slot="empty">
          <!-- 没有数据 -->
          <div class="empty-area" v-if="!tableLoading">
            <img src="@/assets/img/lesson2/plan/empty.png"/>
            <span>{{$t('loc.plan10')}}</span>
          </div>
        </div>
      </el-table>
      <!-- 分页 -->
      <div v-if="total >= 10" class="table-footer flex-none">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 复制确认弹窗 -->
    <el-dialog
      :title="$t('loc.confirmation')"
      custom-class="lg-el-dialog"
      :visible.sync="replicateModelVisible"
      :before-close="beforeCloseReplicate"
      width="30%">
      <span>{{$t('loc.plan67')}}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="replicateModelVisible = false" :disabled="replicateLoading">{{$t('loc.cancel')}}</el-button>
        <el-button type="primary" @click="replicate" :loading="replicateLoading">{{$t('loc.confirm')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { acrossRole } from '@/utils/common'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'

export default {
  name: 'TemplatePlanTable',

  props: {
    loader: {
      type: Function
    }
  },

  data () {
    return {
      plans: [],
      weeks: [],
      showCenter: false,
      centers: [],
      groups: [],
      keyword: undefined,
      currentCenterId: undefined,
      currentGroupId: undefined,
      currentWeekNum: undefined,
      // 创建周计划时默认班级
      defaultGroupId: undefined,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      sortField: undefined,
      sortOrder: undefined,
      tableLoading: true,
      pageLoading: true,
      hasPlans: false,
      lang: 'en-US',
      replicateModelVisible: false,
      replicatePlan: undefined,
      replicateLoading: false
    }
  },

  created () {
    this.getWeeks()
    // 管理员不需要获取学校班级信息，直接获取周计划列表
    if (this.isAdmin) {
      this.loadData()
    } else {
      this.getCenterGroups()
    }
    this.lang = tools.localItem('NG_TRANSLATE_LANG_KEY')
    window.addEventListener('resize', this.reCalTable)
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    isAdmin () {
      return acrossRole('agency_admin', 'agency_owner', 'site_admin')
    },
    isSiteAdmin () {
      return acrossRole('site_admin')
    },
    eidtingTip () {
      return (plan) => {
        return plan && plan.lockedData ? plan.lockedData.userName + this.$t('loc.plan59') : ''
      }
    }
  },

  methods: {
    // 获取框架名
    getFrameworkName (plan) {
      return plan.frameworkAbbreviation || plan.frameworkName
    },
    // 重新计算表格高度
    reCalTable () {
      this.$nextTick(() => {
        this.$refs.table && this.$refs.table.doLayout()
        let tableDom = this.$el.querySelector('.lg-form')
        let dom = this.$el.querySelector('.lg-form .el-table__fixed-right')
        if (dom) {
          let table = this.$el.querySelector('.lg-form .el-table__body-wrapper')
          if (table.scrollHeight > table.offsetHeight) {
            dom.style.right = '6px'
          }
          dom.style.height = (tableDom.clientHeight) + 'px'
          let body = dom.querySelector('.el-table__fixed-body-wrapper')
          if (body) {
            body.style.height = (table.clientHeight - 6) + 'px'
          }
        }
      })
    },
    // 显示更多操作菜单
    showPop (row) {
      this.plans.forEach((item) => {
        // 显示当前行的操作菜单
        if (row && item.id === row.id) {
          item.showPop = true
        } else {
          // 隐藏其他行的操作菜单
          item.showPop = false
          const key = 'plan-popover-' + item.id
          this.$nextTick(() => {
            document.getElementById(this.$refs[key].$refs.popper.id).style.display = 'none'
          })
        }
      })
    },
    /**
     * 获取周次列表
     */
    getWeeks () {
      this.weeks = [{
        name: this.$t('loc.allWeeks')
      }]
      for (let i = 0; i < 54; i++) {
        this.weeks.push({
          num: i + 1,
          name: 'Week ' + (i + 1)
        })
      }
    },

    formatTime (row, column, cellValue, index) {
      if (!cellValue) {
        return cellValue
      }
      return this.$moment(cellValue).format('MM/DD/YYYY hh:mm A')
    },

    // 获取当前用户学校、班级列表
    getCenterGroups () {
      this.centers = [{
        name: this.$t('loc.allCenter')
      }]
      this.groups = [{
        name: this.$t('loc.allClass')
      }]
      this.$axios({
        url: $api.urls(this.currentUser.user_id).centersAndGroups,
        method: 'get'
      }).then(response => {
        let centers = []
        // 过滤离校班级
        response.forEach(c => {
          let groups = c.groups.filter(x => !x.inactive)
          if (groups.length > 0) {
            c.groups = groups
            centers.push(c)
          }
        })
        // 超过一个学校时显示筛选学校
        this.showCenter = centers.length > 1
        // 加入学校列表
        this.centers = this.centers.concat(centers)
        // 如果只有一个学校，直接显示学校下的班级
        if (centers.length === 1) {
          let centerGroups = centers[0].groups
          if (centerGroups && centerGroups.length === 1) {
            this.groups = centerGroups
            this.currentGroupId = centerGroups[0].id
          } else {
            this.groups = this.groups.concat(centerGroups)
          }
        }
        // 选择第一个学校中第一个班级作为创建周计划的默认班级
        if (centers.length > 0) {
          this.defaultGroupId = centers[0].groups[0].id
        }
        // 加载周计划
        this.loadData()
      }).catch(error => {})
    },

    loadData () {
      this.tableLoading = true
      // this.plans = []
      let params = {
        page: this.currentPage,
        pageSize: this.pageSize
      }
      // 搜索关键字非空时加入查询条件
      if (this.keyword && this.keyword.trim().length > 0) {
        params['keyword'] = this.keyword
      }
      // 选择了排序，则按照字段排序
      if (this.sortField && this.sortOrder) {
        params['orderKey'] = this.sortField
        params['orderType'] = this.sortOrder
      }
      this.loader(params).then(data => {
        this.plans = data.items
        this.total = data.total
        this.tableLoading = false
        // 是否有过周计划
        if (this.plans && this.plans.length > 0) {
          this.hasPlans = true
        }
        this.pageLoading = false
        this.reCalTable()
      }).catch(error => {
        this.tableLoading = false
      })
    },

    handleSizeChange (size) {
      this.pageSize = size
      this.currentPage = 1
      // 刷新数据
      this.loadData()
    },

    handleCurrentChange (page) {
      this.currentPage = page
      // 刷新数据
      this.loadData()
    },

    changeWeek () {
      this.currentPage = 1
      // 刷新数据
      this.loadData()
    },

    // 切换学校时，更新班级列表
    changeCenter () {
      this.groups = [{
        name: this.$t('loc.allClass')
      }]
      this.currentGroupId = undefined
      this.currentPage = 1
      // 刷新数据
      this.loadData()
      // 学校全选
      if (!this.currentCenterId) {
        return
      }
      // 将选中学校下班级加入列表中
      this.centers.forEach(c => {
        if (c && c.id === this.currentCenterId) {
          let groups = c.groups.filter(x => !x.inactive)
          if (groups && groups.length === 1) {
            this.groups = groups
            this.currentGroupId = groups[0].id
          } else {
            this.groups = this.groups.concat(groups)
          }
        }
      })
    },

    changeClass () {
      this.currentPage = 1
      // 刷新数据
      this.loadData()
    },

    /**
     * 点击整行查看周计划
     */
    viewPlan (plan) {
      // 如果周计划是草稿状态，跳转编辑页面
      if (plan.status === 'A_DRAFT') {
        this.edit(plan)
        return
      }
      this.$router.push({
        name: 'view-template',
        params: {
          planId: plan.id,
          defaultGroupId: this.isAdmin ? '' : this.defaultGroupId
        }
      })
    },

    /**
     * 编辑周计划
     */
    edit (plan) {
      this.$analytics.sendEvent('web_weekly_plan_exemplar_click_edit')
      this.$router.push({
        name: 'edit-template',
        params: {
          planId: plan.id
        }
      })
    },

    /**
     * 复制周计划弹窗关闭回调，复制中的不能关闭
     */
    beforeCloseReplicate (done) {
      if (!this.replicateLoading) {
        done()
      }
    },

    /**
     * 显示复制弹窗
     */
    replicateConfirm (plan) {
      this.replicatePlan = plan
      this.replicateModelVisible = true
    },

    /**
     * 复制周计划
     */
    replicate () {
      this.$analytics.sendEvent('web_weekly_plan_exemplar_click_replicate')
      this.replicateLoading = true
      LessonApi.replicatePlan({
        planId: this.replicatePlan.id,
        groupId: this.defaultGroupId
      }).then(response => {
        if (this.isAdmin) {
          this.$router.push({
            name: 'edit-template',
            params: {
              planId: response.id,
              create: true
            }
          })
        } else {
          this.$router.push({
            name: 'edit-plan',
            params: {
              planId: response.id
            }
          })
        }
      }).catch(error => {
        this.replicateLoading = false
      })
    },

    /**
     * 删除周计划
     */
    deletePlan (plan) {
      this.$confirm(this.$t('loc.plan11'), this.$t('loc.cfm'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        LessonApi.deletePlan({}, {
          id: plan.id
        }).then(response => {
          this.$message({
            type: 'success',
            message: this.$t('loc.plan12')
          })
          this.$analytics.sendEvent('web_weekly_plan_exemplar_click_delete')
          this.loadData()
        })
      }).catch(() => {
      })
    },

    /**
     * 生成周计划 PDF
     */
    generatePDF (id, print) {
      // 如果是打印，判断浏览器是否支持打印
      if (print && tools.isFirefox()) {
        this.$alert(this.$t('loc.plan176'), this.$t('loc.plan135'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('loc.download'),
          customClass: 'el-message-box-font',
          callback: action => {
            if (action == 'confirm') {
              this.generatePDF(false)
            }
          }
        })
        return
      }
      if (print) {
        this.$analytics.sendEvent('web_weekly_plan_exemplar_click_print')
      } else {
        this.$analytics.sendEvent('web_weekly_plan_exemplar_click_download')
      }
      this.tableLoading = true
      this.showPop()
      LessonApi.getPlanPDF({
        planId: id,
        showCore: false
      }).then(response => {
        this.getPlanPDF(print)
      }).catch(error => {})
    },

    /**
     * 获取周计划 PDF
     */
    getPlanPDF (print) {
      LessonApi.getPDFList({
        type: 'WEEKLY_PLAN'
      }).then(response => {
        if (response && response.length > 0) {
          let pdf = response[0]
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getPlanPDF(print)
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED' && print) {
            this.$print({
              printable: pdf.pdfUrl,
              type: 'pdf',
              onLoadingStart: () => {
              },
              onLoadingEnd: () => {
                this.tableLoading = false
              },
              onError: (error) => {
                this.downloadPDFWithAlert(pdf)
                this.tableLoading = false
              }
            })
          } else {
            this.downloadPDFWithAlert(pdf)
            this.tableLoading = false
          }
        }
      })
    },

    /**
     * 下载周计划 PDF 弹窗
     */
    downloadPDFWithAlert (pdf) {
      // 成功弹窗下载
      this.$alert('<div class="display-flex align-items"><img style="height: 30px; margin: 5px" src="' + this.getfilePhoto(pdf.pdfName) + '"> <span  title="' + pdf.pdfName + '">' + pdf.pdfName + '</span></div>', this.$t('loc.plan61'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download'),
        customClass: 'el-message-box-font',
        callback: action => {
          if (action == 'confirm') {
            this.$analytics.sendEvent('web_weekly_plan_exemplar_click_pop_download')
            if (tools.isComeFromIPad()) {
              let requestData = {
                'emailTemplate': 'weekly_lesson_planning',
                'downloadFileUrl': pdf.pdfUrl,
                'fileName': pdf.pdfName,
                'week': this.planData.week,
                'className': this.planData.groupName,
                'siteName': this.planData.centerName,
                'fromDate': this.planData.fromAtLocal,
                'toDate': this.planData.toAtLocal,
                'courseName': this.planData.theme
              }
              this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
              .then(() => {
                this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                  confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                  showCancelButton: false
                })
              }).catch(error => {
                this.$message.error(error.message)
              })
            } else {
              const eleLink = document.createElement('a')
              eleLink.style.display = 'none'
              eleLink.href = pdf.pdfUrl
              // 触发点击
              document.body.appendChild(eleLink)
              eleLink.click()
              // 移除
              document.body.removeChild(eleLink)
            }
          }
        }
      })
    },

    /**
     * 获取文件类型 icon
     */
    getfilePhoto (fileName) {
      if (fileName.endsWith('doc')) {
        return doc
      }
      if (fileName.endsWith('docx')) {
        return docx
      }
      if (fileName.endsWith('pdf')) {
        return pdf
      }
      if (fileName.endsWith('ppt')) {
        return ppt
      }
      if (fileName.endsWith('pptx')) {
        return pptx
      }
      if (fileName.endsWith('xls')) {
        return xls
      }
      if (fileName.endsWith('xlsx')) {
        return xlsx
      }
      return file
    },
    /**
     * 格式化日期，移除年份
     */
    removeYear (date) {
      if (!date) {
        return date
      }
      return this.$moment(date).format('MM/DD')
    },

    sortChange (params) {
      let order = params.order
      if (!order) {
        this.sortField = undefined
        this.sortOrder = undefined
      } else {
        let columnKey = params.column.columnKey
        this.sortField = columnKey || params.column.label
        this.sortOrder = params.order === 'ascending' ? 'ASC' : 'DESC'
      }
      // 刷新数据
      this.loadData()
    },
    isComeFromIPad () {
      return tools.isComeFromIPad()
    },
    changeHid (plan) {
      this.Hid = !this.Hid
    },
    // 管理员创建模板
    createTemplate () {
      this.$router.push({
        name: 'edit-template',
        params: {
          planId: 'new',
          createTemplate: true
        }
      })
    }
  },
  watch: {
    // 监听搜索框内容，延迟 500ms 调用接口
    keyword: tools.debounce(function (newVal) {
      this.loadData()
    }, 500)
  }
}
</script>

<style lang="less" scoped>
.action-divider {
  margin: 8px 0px !important;
}

.action-delete:hover:not(.is-disabled) {
  color: #f56c6c !important;
}

/deep/ .el-link--inner {
  display: flex;
  align-items: center;
}
/deep/ .el-table thead {
  color: #323338;
}
/deep/ .el-table .cell {
  text-overflow: clip;
  word-break: break-word;
}
@media only screen and (max-width:1199px){
  //ipad
  .plan-table {
    margin: 0 10px;
  }
  /deep/ .table-header th {
    font-weight: bold;
    // color: #323338 !important;
    background-color: #fafafa;
  }
  /deep/.table-header .cell {
    word-break: break-word;
  }
  .operation-area {
    background-color: #fff;
    padding: 10px 10px;
    margin: 0 0 10px;
  }
  .table-footer {
    padding: 10px 0;
    margin-bottom: 0px;
    text-align: right;
  }
  .search-input {
    width: 220px;
  }

  /deep/ .el-input.is-disabled .el-input__inner {
    color: #606266;
  }
  /deep/ .el-table table{
    width: 0px;
  }
  .empty-area {
    display: flex;
    flex-direction: column;
    padding: 50px 0;
    align-items: center;
    justify-content: center;
  }
  .w-110 {
    width: 110px;
  }
  .act_div_wapper{
    display: flex;
    justify-content: flex-start;
    margin-top: 10px;
  }
  .act_icon_detail{
    display: block;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    background-image: url("../../../../../assets/img/icon/button/detail_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }
  .act_icon_replicate{
    display: block;
    width: 18px;
    height: 18px;
    background-image: url("../../../../../assets/img/icon/button/replicate_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }
  .act_icon_edit{
    display: block;
    width: 18px;
    height: 18px;
    background-image: url("../../../../../assets/img/icon/button/edit_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }
  .act_icon_delete{
    display: block;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    background-image: url("../../../../../assets/img/icon/button/delete_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }
  .table-header{
    color: #323338;
  }
}
@media only screen and (min-width:1200px){
  //web
  .plan-table {
    margin: 0 30px;
  }
  /deep/ .table-header th {
    font-weight: bold;
    // color: #323338 !important;
    background-color: #fafafa;
  }
  /deep/.table-header .cell {
    word-break: break-word;
  }
  .operation-area {
    background-color: #fff;
    padding: 10px 10px;
    margin: 0 0 10px;
  }
  .table-footer {
    padding: 10px 0;
    text-align: right;
  }
  .search-input {
    width: 300px;
  }

  /deep/ .el-input.is-disabled .el-input__inner {
    color: #606266;
  }
  /deep/ .el-table table{
    width: 0px;
  }
  .empty-area {
    display: flex;
    flex-direction: column;
    padding: 50px 0;
    align-items: center;
    justify-content: center;
  }
  .act_div_wapper{
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .act_icon_detail{
    display: block;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    background-image: url("../../../../../assets/img/icon/button/detail_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }
  .act_icon_replicate{
    display: block;
    width: 18px;
    height: 18px;
    background-image: url("../../../../../assets/img/icon/button/replicate_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }
  .act_icon_edit{
    display: block;
    width: 18px;
    height: 18px;
    background-image: url("../../../../../assets/img/icon/button/edit_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }
  .act_icon_delete{
    display: block;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    background-image: url("../../../../../assets/img/icon/button/delete_icon.png");
    background-position: center center;
    background-size: 18px 18px;
  }

}
</style>
