<template>
  <div class="observation-item m-b-sm">
    <!-- 头部 -->
    <div class="display-flex justify-content-between m-b-sm">
      <!-- 作者和日期 -->
      <div class="display-flex">
        <!-- 头像 -->
        <el-avatar :src="noteUser.avatarUrl"></el-avatar>
        <div class="m-l-xs">
          <!-- 老师名称 -->
          <div class="">
            {{noteUser.displayName | formatUserName}}
          </div>
          <!-- 观察日期 -->
          <div>
            {{noteDate}}
          </div>
        </div>
      </div>
      <!-- 小孩 -->
      <div v-if="note.children.length > 0" :title="childNames" class="child-name-area overflow-ellipsis">
        <span>Children ({{note.children.length}}): </span>
        <span v-for="(child, index) in note.children" :key="index">{{child.displayName}}<span v-if="index != note.children.length - 1">, </span></span>
      </div>
    </div>
    <!-- 内容 -->
    <div class="m-b-sm text-black-mon">
      {{note.payload}}
    </div>
    <!-- Learning Story 内容 -->
    <div class="m-b" v-if="note.noteType && note.noteType === 'LEARNING_STORY' && note.storyContents && note.storyContents.length > 0">
      <html-content-toggle :content="note.storyContentsHtml" :html-id="note.storyContentsId"></html-content-toggle>
    </div>
    <!-- 图片 -->
    <div class="m-b-sm display-flex" v-if="otherMedias.length > 0">
      <div class="note-media m-r-sm" v-for="(m, index) in otherMedias" :key="index">
        <media-viewer :url="m.public_url" :snapshotUrl="m.snapshot_url" :ratio="1/1" :preview=true :imgList="imgList"></media-viewer>
      </div>
    </div>
    <!-- 语音 -->
    <div class="m-b-sm display-flex" v-if="audioMedias.length > 0">
      <div class="" v-for="(m, index) in audioMedias" :key="index">
        <media-viewer :url="m.public_url" :ratio="'0'" :preview=true :imgList="imgList" :duration="note.voiceTime"></media-viewer>
      </div>
    </div>
    <!-- 测评点 -->
    <div class="" v-if="note.domains.length > 0">
      <el-tag type="info" size="small" effect="plain" class="m-r-sm text-black-mon m-b-sm" v-for="(measure, index) in note.domains" :key="index">{{measure.abbreviation}}: {{measure.name || measure.description}}</el-tag>
    </div>
    <!-- 附件 -->
    <div v-if="note.annexMedias.length > 0" class="m-b-xs">
      <el-popover
        placement="bottom-start"
        width="530"
        v-model="showAnnexPopover"
        trigger="click">
        <div v-for="(annex, index) in note.annexMedias" :key="index" class="display-flex lg-pa-5 align-items" @mouseenter="showDownloadBtn(annex, true)" @mouseleave="showDownloadBtn(annex, false)">
          <!-- 图标 -->
          <img class="w-1x" style="object-fit: cover;"
                v-if="annex.fileType"
                :src="require(`@/assets/img/file/${annex.fileType}.png`)" />
          <img class="w-1x" style="object-fit: cover;"
                v-else
                :src="require(`@/assets/img/file/doc.png`)" />
          <!-- 名称 -->
          <div class="m-l-sm overflow-ellipsis" style="max-width: 160px;" :title="annex.fileName">{{annex.fileName}}</div>
          <!-- 大小 -->
          <div class="m-l-xs">({{toDecimal2(annex.size/1024)}}K)</div>
          <!-- 时间 -->
          <div class="m-l-sm">{{annexTime(annex.createAtUtc)}}</div>
          <!-- 下载 -->
          <a :href="annex.public_url" target="_blank" v-show="annex.showDownload" class="m-l-sm lg-pointer" style="color: #10b3b7;" @click="showAnnexPopover = false">Download</a>
        </div>
        <span class="text-muted lg-pointer" slot="reference">
          <i class="el-icon-paperclip m-r-xs"></i>
          <span>{{note.annexMedias.length + ' ' + (note.annexMedias.length <= 1 ? $t('loc.lowerAttach') : $t('loc.lowerAttachs'))}} </span>
          <i class="el-icon-arrow-down m-l-xs"></i>
        </span>
      </el-popover>
    </div>
    <!-- 修改记录 -->
    <div v-if="note.actions.length > 0" class="m-b-sm">
      <el-popover
        placement="bottom-start"
        width="400"
        :disabled="note.actions.length < 2"
        trigger="click">
        <div v-for="(action, index) in note.actions" :key="index">
          <span v-if="index != 0">{{actionContent(action)}}</span>
        </div>
        <span class="text-muted lg-pointer" slot="reference">
          {{actionContent(note.actions[0])}}
          <i class="el-icon-arrow-down m-l-xs" v-if="note.actions.length > 1"></i>
        </span>
      </el-popover>
    </div>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import MediaViewer from './MediaViewer'
import HtmlContentToggle from '@/views/modules/lesson2/lessonPlan/components/HtmlContentToggle'

export default {
  name: 'ObservationItem',
  components: {
    HtmlContentToggle,
    MediaViewer
  },

  props: {
    note: {
      type: Object
    }
  },

  data () {
    return {
      noteDate: '',
      imgList: [],
      historyVisable: false
    }
  },

  created () {
    this.initData()
  },

  methods: {
    initData () {
      this.noteDate = this.$moment(this.note.create_at).format('MM/DD/YYYY')
      this.setImgList()
      this.setAnnexList()
    },

    actionContent (action) {
      if (!action) {
        return ''
      }
      let operation = 'Updated by '
      if ('CREATE' === action.action) {
        operation = 'Created by '
      } else if ('DELETE' === action.action) {
        operation = 'Deleted by '
      } else if ('RESTORE' === action.action) {
        operation = 'Restored by '
      }
      let userName = action.byUser
      let utcDate = action.utcDate
      let date = this.$moment.utc(utcDate).local().format('MMM DD, YYYY')
      let time = this.$moment.utc(utcDate).local().format('hh:mm a')
      return operation + userName + ' on ' + date + ' at ' + time
    },

    setImgList () {
      let medias = this.note.media
      if (!medias || medias.length === 0) {
        return
      }
      medias.forEach(media => {
        let url = media.public_url
        if (this.isImage(url)) {
          this.imgList.push(url)
        }
      })
    },

    setAnnexList () {
      let medias = this.note.annexMedias
      if (!medias || medias.length === 0) {
        return
      }
      medias.forEach(media => {
        media.showDownload = false
      })
    },

    isImage (url) {
      return url && /.+(\.jpeg|\.png|\.jpg)/i.test(url)
    },

    isAudio (url) {
      return url && /.+(\.aac)/i.test(url)
    },

    toDecimal2 (number) {
      var number1 = parseFloat(number)
      if (isNaN(number1)) return false
      number1 = Math.round(number * 100) / 100
      var str = number1.toString()
      var rs = str.indexOf('.')
      if (rs < 0) {
        rs = str.length
        str += '.'
      }
      while (str.length <= rs + 2) {
        str += '0'
      }
      return str
    },

    annexTime (time) {
      return this.$moment.utc(time).local().format('MMM DD, YYYY hh:mm A')
    },

    showDownloadBtn (annex, show) {
      this.$set(annex, 'showDownload', show)
      this.$forceUpdate()
    }
  },

  computed: {
    noteUser () {
      if (!this.note) {
        return {}
      }
      return this.note.author
    },

    otherMedias () {
      let medias = this.note.media
      if (!medias) {
        return []
      }
      return medias.filter(m => !this.isAudio(m.public_url))
    },

    audioMedias () {
      let medias = this.note.media
      if (!medias) {
        return []
      }
      return medias.filter(m => this.isAudio(m.public_url))
    },

    childNames () {
      if (this.note.children.length === 0) {
        return ''
      }
      let childNames = []
      this.note.children.forEach(child => childNames.push(child.displayName))
      return 'Children (' + this.note.children.length + '): ' + childNames.join(', ')
    }
  },

  watch: {
    note: {
      handler: function (val) {
        // 处理 Note 中 Learning Story 中的 HTML 标签
        if (val.noteType && val.noteType === 'LEARNING_STORY' && val.storyContents && val.storyContents.length > 0) {
          let storyContentsHtml = ''
          val.storyContents.forEach(storyContent => {
            // 有内容才显示, 把 title 和 content 拼接在一起
            if (storyContent.content) {
              if (storyContent.title) {
                storyContentsHtml += '<div class="font-size-16 font-bold m-t-sm">' + storyContent.title + '</div>'
              }
              storyContentsHtml += storyContent.content
            }
          })
          this.$set(val, 'storyContentsHtml', storyContentsHtml)
          // 拼接当前毫秒数作为 storyContentsId
          this.$set(val, 'storyContentsId', val.id_str + '_' + new Date().getTime())
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.note-media {
  width: 96px;
  height: 96px;
}
.child-name-area {
  max-width: 50%;
}
</style>