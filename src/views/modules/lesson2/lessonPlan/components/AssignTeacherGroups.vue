<template>
    <div>
        <!--Assign Teacher Groups 的弹窗-->
        <el-dialog :visible.sync="assignTeacherGroupsShow"
                   :show-close=false
                   :before-close="clearOldData"
                   custom-class="assign-teacher-dialog"
                   :append-to-body="true"
                   @closed="handleTeacherGroups"
                   :modal-append-to-body="true"
                   :close-on-click-modal="false"
                   width="880px">
            <!--弹窗的头部信息-->
            <div slot="title" class="dialog-title">
                <div class="dialog-title-content display-flex align-items" style="gap: 5px">
                    <span>{{ $t('loc.unitPlannerAssignTeacherGroup') }}</span>
                    <!-- 隐藏解释 -->
                    <el-tooltip v-if="showAIGroup" effect="dark" :content="$t('loc.unitPlannerAssignTeacherGroupTitle')" placement="bottom"
                                popper-class="body-text">
                        <i class="lg-icon lg-icon-info font-size-24 font-thin"></i>
                    </el-tooltip>
                </div>
                <div class="dialog-close" @click="handleClose(false)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <g clip-path="url(#clip0_3538_30865)">
                            <path d="M20 0H0V20H20V0Z" fill="white" fill-opacity="0.01"/>
                            <path d="M5.83203 5.83337L14.1654 14.1667" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                            <path d="M5.83203 14.1667L14.1654 5.83337" stroke="#676879" stroke-width="1.66667"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_3538_30865">
                                <rect width="20" height="20" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            </div>
            <!--当 groupTeams 存在，并且 groupTeams 的老师信息也是存在的情况下-->
            <div v-if="groupTeams && groupTeams.teacherList && groupTeams.teacherList.length > 0" class="dialog-body">
                <div class="teacher-switch">
                        <span class="font-weight-600 font-size-16">{{
                                $t('loc.unitPlannerPersonalizePlanEnableGroup')
                            }}</span>
                    <el-switch
                        v-model="enableTecherGroups"
                        @change="changeTeacherGroups"
                        unselectable="on" onselectstart="return false;"
                        style="-moz-user-select:none;-webkit-user-select:none;">
                    </el-switch>
                </div>
                <div class="teacher-groups">
                    <div class="font-bold" v-if="groupTeams && groupTeams.enrollmentTeamModelList && groupTeams.enrollmentTeamModelList.length > 0">
                        {{ groupTeams.groupName }} ({{
                            $t('loc.unitPlannerPersonalizePlanTotalChildren', { count: groupTeams.enrollmentTeamModelList.length })
                        }})
                    </div>
                    <div>
                        <el-popover
                            placement="bottom"
                            trigger="click"
                            popper-class="select-teacher-popover"
                            width="260"
                            :visible-arrow="false"
                            v-model="showSelectTeacher">
                            <div v-for="teacher in allTeacher" class="teacher-select" :key="teacher.id">
                                <div class="teacher-groups-tag-title" @click="selectTeacher(teacher)">
                                    <div class="display-flex flex-justify-start align-items flex-grow-1">
                                        <!-- 展示员工的头像 -->
                                        <el-avatar :src=" teacher.avatar_url" class="add-margin-r-10" :size="30"
                                                   style="min-width: 30px"></el-avatar>
                                        <!-- 展示员工的名字 -->
                                        <div class="flex-1 text-ellipsis font-size-14 font-normal">
                                            {{ teacher.display_name | formatUserName }}
                                        </div>
                                        <div v-show="selectedTeacher.includes(teacher)"
                                             style="width: 24px; height: 24px;">
                                            <!--  选中的状态 -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                 viewBox="0 0 24 24" fill="none">
                                                <path d="M4.99902 11.1924L9.94877 16.1421L19.1412 6.94975"
                                                      stroke="#10B3B7" stroke-width="2"
                                                      stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <el-button slot="reference" icon="el-icon-plus" @click="addTeacherAssignGroups" type="primary" plain>
                                {{ $t('loc.unitPlannerAssignTeacherGroupAdd') }}
                            </el-button>
                        </el-popover>
                    </div>
                </div>
                <div class="display-flex">
                    <div class="lg-scrollbar teacher-groups-tag" style="height: 351px; width: 840px; overflow-y: hidden;">
                        <div style="width: 270px; flex-grow: 1;" v-for="teacher in selectedTeacher"
                                :key="teacher.id">
                            <div class="teacher-groups-tag-title">
                                <div class="width-90-percent display-flex flex-justify-start align-items flex-grow-1">
                                    <!-- 展示员工的头像 -->
                                    <el-avatar :src=" teacher.avatar_url" class="add-margin-r-10" :size="30"
                                               style="min-width: 30px"></el-avatar>
                                    <!-- 展示员工的名字 -->
                                    <div
                                        class="width-90-percent overflow-ellipsis flex-1 text-ellipsis font-size-14 font-normal">
                                        {{ teacher.display_name | formatUserName }}
                                    </div>
                                </div>
                                <div>
                                    <el-button :disabled="selectedTeacher.length === 1"
                                               @click="deleteTeacherAssignGroups(teacher)" type="text">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                                             fill="none">
                                            <path
                                                d="M9.99935 1.66663C14.6017 1.66663 18.3327 5.39758 18.3327 9.99996C18.3327 14.6023 14.6017 18.3333 9.99935 18.3333C5.39697 18.3333 1.66602 14.6023 1.66602 9.99996C1.66602 5.39758 5.39697 1.66663 9.99935 1.66663ZM12.9413 7.05802L12.8712 6.9975C12.6537 6.83613 12.3552 6.83413 12.1357 6.99153L12.0574 7.05802L9.99935 9.11579L7.94129 7.05802L7.87119 6.9975C7.6537 6.83613 7.35519 6.83413 7.1357 6.99153L7.05741 7.05802L6.99689 7.12812C6.83552 7.34561 6.83352 7.64412 6.99092 7.86361L7.05741 7.9419L9.11518 9.99996L7.05741 12.058L6.99689 12.1281C6.83552 12.3456 6.83352 12.6441 6.99092 12.8636L7.05741 12.9419L7.12751 13.0024C7.345 13.1638 7.64351 13.1658 7.863 13.0084L7.94129 12.9419L9.99935 10.8841L12.0574 12.9419L12.1275 13.0024C12.345 13.1638 12.6435 13.1658 12.863 13.0084L12.9413 12.9419L13.0018 12.8718C13.1632 12.6543 13.1652 12.3558 13.0078 12.1363L12.9413 12.058L10.8835 9.99996L12.9413 7.9419L13.0018 7.8718C13.1632 7.65431 13.1652 7.3558 13.0078 7.13631L12.9413 7.05802Z"
                                                fill="#676879"/>
                                            <path
                                                d="M12.8718 6.99754L12.9419 7.05806L13.0084 7.13635C13.1658 7.35584 13.1638 7.65435 13.0024 7.87184L12.9419 7.94194L10.8842 10L12.9419 12.0581L13.0084 12.1363C13.1658 12.3558 13.1638 12.6543 13.0024 12.8718L12.9419 12.9419L12.8637 13.0084C12.6442 13.1658 12.3457 13.1638 12.1282 13.0024L12.0581 12.9419L10 10.8842L7.94194 12.9419L7.86365 13.0084C7.64416 13.1658 7.34565 13.1638 7.12816 13.0024L7.05806 12.9419L6.99157 12.8637C6.83418 12.6442 6.83617 12.3457 6.99754 12.1282L7.05806 12.0581L9.11584 10L7.05806 7.94194L6.99157 7.86365C6.83418 7.64416 6.83617 7.34565 6.99754 7.12816L7.05806 7.05806L7.13635 6.99157C7.35584 6.83418 7.65435 6.83617 7.87184 6.99754L7.94194 7.05806L10 9.11584L12.0581 7.05806L12.1363 6.99157C12.3558 6.83418 12.6543 6.83617 12.8718 6.99754Z"
                                                fill="white"/>
                                        </svg>
                                    </el-button>
                                </div>
                            </div>
                            <div class="teacher-groups-tag-content">
                                <div class="teacher-groups-tag-content-title">
                                    <!--如果只有一个老师的时候禁止弹出-->
                                    <el-popover
                                        placement="bottom"
                                        trigger="click"
                                        :disabled="selectedTeacher.length === 1"
                                        popper-class="select-child-popover"
                                        width="244"
                                        :visible-arrow="false"
                                        v-model="childPopoverVisible[teacher.id]">
                                        <!-- 小孩列表 -->
                                        <div class="child-area m-t-xs m-b-xs pos-rlt lg-scrollbar-show">
                                            <template
                                                v-if="groupTeams.enrollmentTeamModelList && groupTeams.enrollmentTeamModelList.length > 0">
                                                <!-- 选择所有小孩 -->
                                                <div class="display-flex align-items search-child-area lg-pointer"
                                                     v-if="groupTeams.enrollmentTeamModelList && groupTeams.enrollmentTeamModelList.length > 0">
                                                    <el-checkbox
                                                        style="width: 100%"
                                                        :indeterminate="isIndeterminate[teacher.id]"
                                                        @change="selectAllChild(teacher.id, $event)"
                                                        v-model="selectAllChildren[teacher.id]">
                                                        <div class="m-l-xs">
                                                            {{ $t('loc.selectAll') }}
                                                        </div>
                                                    </el-checkbox>
                                                </div>
                                                <div class="child-list">
                                                    <div v-for="(child, index) in groupTeams.enrollmentTeamModelList"
                                                         :key="child.enrollmentId"
                                                         class="m-b-xs child-check-row lg-pointer">
                                                        <div @click.stop>
                                                            <el-checkbox
                                                                style="width: 100%; display: flex; align-items: center;"
                                                                @change="changeChild(child, teacher.id, $event)"
                                                                v-model="selectChildrenselected[teacher.id][child.enrollmentId]">
                                                                <div class="enrollment-tag">
                                                                    <!-- 展示员工的头像 -->
                                                                    <el-avatar :src="child.avatarUrl"
                                                                               class="add-margin-r-10" :size="24"
                                                                               style="min-width: 24px">
                                                                        <div slot="default" class="image-slot">
                                                                            <img
                                                                                src="https://d2urtjxi3o4r5s.cloudfront.net/images/child_avatar_2023.png"
                                                                                class="child-avatar"/>
                                                                        </div>
                                                                    </el-avatar>
                                                                    <!-- 展示员工的名字 -->
                                                                    <div
                                                                        :title="child.firstName + ' ' + child.lastName"
                                                                        class="flex-1 text-ellipsis font-size-14 font-normal">
                                                                        {{ child.firstName + ' ' + child.lastName }}
                                                                    </div>
                                                                    <!-- 是否是 IEP -->
                                                                    <div v-show="child.iep && !hideIEPOpen" style="color: var(--color-iep)">
                                                                        IEP
                                                                    </div>
                                                                    <!-- 是否是 ELD -->
                                                                    <div v-show="child.eld" style="color: var(--color-eld)">
                                                                        ELD
                                                                    </div>
                                                                </div>
                                                            </el-checkbox>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                            <!-- 没有小孩 -->
                                            <div
                                                class="display-flex justify-content align-items flex-direction-col add-padding-t-30 add-padding-b-30"
                                                v-if="!groupTeams.enrollmentTeamModelList || groupTeams.enrollmentTeamModelList.length === 0">
                                                <img class="add-padding-b-20"
                                                     src="@/assets/img/lesson2/plan/child_header.png"/>
                                                <span class="add-padding-b-20">{{ $t('loc.plan35') }}</span>
                                                <el-button size="small" @click="childPopoverVisible[teacher.id] = false">
                                                    {{ $t('loc.close') }}
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 确认按钮 -->
                                        <div style="text-align: right; margin: 0; padding: 8px 12px;"
                                             v-if="groupTeams.enrollmentTeamModelList && groupTeams.enrollmentTeamModelList.length > 0">
                                            <el-button type="primary" size="small"
                                                       @click="childPopoverVisible[teacher.id] = false">
                                                {{ $t('loc.confirm') }}
                                            </el-button>
                                        </div>
                                        <div slot="reference" @click="showChildPopoverVisible(teacher.id)"
                                             :style="{'opacity': selectedTeacher.length === 1 ? '0.4': '1'}"
                                             class="select-child-btn el-button--primary">
                                            <!-- Select Children 按钮 -->
                                            <div style="color: var(--color-primary)">
                                                {{
                                                    $t('loc.unitPlannerAssignTeacherGroupSelectChild')
                                                }}
                                            </div>
                                            <div class="line-height-22 height-22">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                     viewBox="0 0 24 24" fill="none">
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                          d="M12.6699 15.3124L12 14.6182L11.3301 15.3124C11.7001 15.6958 12.2999 15.6958 12.6699 15.3124ZM12 13.2297L7.61726 8.68754C7.24729 8.30412 6.64745 8.30412 6.27748 8.68754C5.90751 9.07097 5.90751 9.69262 6.27748 10.076L11.3301 15.3124L12 14.6182L12.6699 15.3124L17.7225 10.076C18.0925 9.69262 18.0925 9.07097 17.7225 8.68754C17.3526 8.30412 16.7527 8.30412 16.3827 8.68754L12 13.2297Z"
                                                          fill="#10B3B7"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </el-popover>
                                    <span>{{
                                            selectdChildLength(teacher.id)
                                        }} / {{ groupTeams.enrollmentTeamModelList.length }}</span>
                                </div>
                                <div class="width-full">
                                    <el-scrollbar class="width-full" style="height: 214px;">
                                        <div class="teacher-groups-children width-full">
                                            <div v-for="enrollment in selectdChild(teacher.id)" class="enrollment-tag"
                                                 :key="enrollment.enrollmentId">
                                                <!-- 展示员工的头像 -->
                                                <el-avatar :src="enrollment.avatarUrl" class="add-margin-r-10" :size="24"
                                                           style="min-width: 24px"></el-avatar>
                                                <!-- 展示员工的名字 -->
                                                <div
                                                  :title="enrollment.firstName + ' ' + enrollment.lastName"
                                                  class="flex-1 text-ellipsis text-ellipsis font-size-14 font-normal">
                                                    {{ enrollment.firstName + ' ' + enrollment.lastName }}
                                                </div>
                                                <!-- 是否是 IEP -->
                                                <div v-show="enrollment.iep && !hideIEPOpen" style="color: var(--color-iep)">IEP</div>
                                                <!-- 是否是 ELD -->
                                                <div v-show="enrollment.eld" style="color: var(--color-eld)">ELD</div>
                                            </div>
                                        </div>
                                    </el-scrollbar>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="showAIGroup"  class="teacher-groups-size">
                    <div class="display-flex flex-direction-col">
                        <div class="display-flex gap-12 align-items">
                            <div class="font-weight-600 font-size-16">{{
                                $t('loc.unitPlannerAssignTeacherGroupSetGroupSize')
                                }}</div>
                            <el-form :model="groupTeams" class="min-max-group-size" :rules="groupTeamsRules"
                                     ref="groupTeamsFormRef">
                                <el-form-item prop="minTeamSize" class="min-group-size">
                                    <el-input v-model="minTeamSize" type="number"/>
                                </el-form-item>
                                <el-divider class="divide-line-gray" ></el-divider>
                                <el-form-item prop="maxTeamSize" class="max-group-size">
                                    <el-input v-model="maxTeamSize" type="number"/>
                                </el-form-item>
                            </el-form>
                        </div>
                        <!-- span 错误提示框 fromValidedError   -->
                        <span v-show="fromValidedError" class="display-flex add-margin-r-42 text-danger line-height-22 flex-justify-end">
                                {{
                                $t('loc.unitPlannerAssignTeacherGroupSetGroupSizeErrorTitle')
                            }}
                            </span>
                    </div>
                </div>
                <div v-show="showAIGroup" class="body-text">
                    {{
                        $t('loc.unitPlannerAssignTeacherGroupSetGroupSizeTitle')
                    }}
                </div>
            </div>
            <div v-else class="dialog-body">
                <div class="teacher-switch">
                        <span class="font-weight-600 font-size-16">{{
                                $t('loc.unitPlannerPersonalizePlanEnableGroup')
                            }}</span>
                    <el-switch
                        v-model="enableTecherGroups"
                        @change="changeTeacherGroups"
                        unselectable="on" onselectstart="return false;"
                        style="-moz-user-select:none;-webkit-user-select:none;">
                    </el-switch>
                </div>
                <div class="teacher-groups">
                    <div class="font-bold">
                        <span class="font-size-16">{{ groupTeams.groupName }}</span>
                        <span v-if="groupTeams && groupTeams.enrollmentTeamModelList && groupTeams.enrollmentTeamModelList.length > 0">({{
                                $t('loc.unitPlannerPersonalizePlanTotalChildren', { count: groupTeams.enrollmentTeamModelList.length })
                            }})</span>
                    </div>
                </div>
                <div class="empty-status">
                    <LgEmptyPage :text="emptyTeacher"></LgEmptyPage>
                </div>
            </div>
            <div slot="footer">
                <!-- 一个 Save 按钮 -->
                <el-button v-if="groupTeams && groupTeams.teacherList && groupTeams.teacherList.length > 0" @click="saveGroupTerms" :loading="saveLoading" type="primary">Save</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>

import { formatUserName } from '@/filters'
import LgEmptyPage from '@/components/LgEmptyPage'
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
export default {
    name: 'AssignTeacherGroups',
    components: {
        LgEmptyPage
    },
    data () {
        return {
            selectedGroupId: undefined, // 选择的班级的 ID
            selectedCenterId: undefined, // 选择的学校的 ID
            isIndeterminate: [], // 是否是不确定的
            selectAllChildren: [], // 是否选择所有的小孩
            enableTecherGroups: false, // 是否开启了分组
            childPopoverVisible: [], // 选择小孩的弹窗是否展示
            showSelectTeacher: false, // 展示选择老师的弹窗
            selectChildrenselected: [], // 选择的小孩，为二维数组，第一维是老师的 id ，第二维是小孩的 enrollmentId
            assignTeacherGroupsShow: false, // Assign Teacher Groups 弹窗是否展示
            groupTeams: {}, // 班级的信息
            fromValidedError: false, // 是否有校验错误
            selectedTeacher: [], // 选择的老师
            groupTeamsRules: {}, // 班级的信息的校验规则
            saveLoading: false // 保存的 loading
        }
    },
    watch: {
      assignTeacherGroupsShow (val) {
        if (val) {
          this.getGroupAdaptSettings()
        }
      },
      enableTecherGroups (val) {
        // 将 enableTecherGroups 的值存储到 sessionStorage 中
        sessionStorage.setItem('enableGroupTeams' + this.groupId, JSON.stringify(val))
      }
    },
    computed: {
        ...mapState({
            hideIEPOpen: state => state.lesson.hideIEPOpen,
            currentUser: state => state.user.currentUser,
            showAIGroup: state => state.lesson.showAIGroup // 隐藏 AI 分组功能的提示
        }),
        /**
         * 获取用户 id
         */
        currentUserId () {
          if (!this.currentUser) {
            return ''
          }
          return this.currentUser.user_id
        },
        /**
         * 获取班级 id
         */
        groupId () {
          // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
          const selectedGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
          if (selectedGroupId) {
            return selectedGroupId
          }
        },
        noSelectdEnrollment () {
            // 从 this.groupTeams.teacherList 中获取所有选择过的学生
            const selectChild = this.groupTeams.teacherList.flatMap((teacher) => {
                return teacher.currentEnrollment
            })
            // 从  groupTeams.enrollmentTeamModelList 中获取没有被选择的 enrollment
            const noSelectEnrollment = this.groupTeams.enrollmentTeamModelList.filter((enrollment) => {
                return !selectChild.includes(enrollment.enrollmentId)
            })
            // 返回没有被选择的 enrollment
            return noSelectEnrollment
        },
        emptyTeacher () {
            return this.$t('loc.unitPlannerAssignTeacherEmptyPage')
        },
        teacherSpan () {
            return (divid) => {
                return Math.floor(24 / divid)
            }
        },
        allTeacher () {
            // 先处理一下，将老师的名字进行排序
            this.sortTeacher()
            //  返回老师的列表
            return this.groupTeams.teacherList
        },
        minTeamSize: {
            get () {
                return this.groupTeams.minTeamSize
            },
            set (value) {
                // 把 value 使用 Math.floor 进行取整
                value = isNaN(value) ? 1 : Math.floor(value)
                // 确保 minTeamSize 是数字
                if (isNaN(value) || value < 1) {
                    this.groupTeams.minTeamSize = 1
                } else if (value <= this.groupTeams.enrollmentTeamModelList.length) {
                    // 设置 minTeamSize
                    this.groupTeams.minTeamSize = value
                } else {
                    // 确保 minTeamSize 不超过最大小孩数
                    this.groupTeams.minTeamSize = this.groupTeams.enrollmentTeamModelList.length
                }

                // 确保 minTeamSize 小于等于 maxTeamSize
                if (this.groupTeams.minTeamSize > this.groupTeams.maxTeamSize) {
                    this.groupTeams.maxTeamSize = this.groupTeams.minTeamSize
                }
            }
        },
        maxTeamSize: {
            get () {
                return this.groupTeams.maxTeamSize
            },
            set (value) {
                // 把 value 使用 Math.floor 进行取整
                value = isNaN(value) ? 1 : Math.floor(value)
                // 确保 maxTeamSize 是数字
                if (isNaN(value) || value < 1) {
                    this.groupTeams.maxTeamSize = 1
                } else if (value > this.groupTeams.enrollmentTeamModelList.length) {
                    // 确保 maxTeamSize 不超过20
                    this.groupTeams.maxTeamSize = this.groupTeams.enrollmentTeamModelList.length
                } else {
                    // 设置 maxTeamSize
                    this.groupTeams.maxTeamSize = value
                }

                // 确保 minTeamSize 小于等于 maxTeamSize
                if (this.groupTeams.minTeamSize > this.groupTeams.maxTeamSize) {
                    this.groupTeams.minTeamSize = this.groupTeams.maxTeamSize
                }
            }
        }
    },
    methods: {
        // 获取老师分组开关状态
        async getGroupAdaptSettings () {
          let groupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
          const data = await LessonApi.getGroupAdaptSetting(groupId)
          this.enableTecherGroups = data.teacherGroupEnabled
        },
        // 是否开启老师分组
        changeTeacherGroups (val, toast = true) {
          LessonApi.setTeacherGroupEnabled(this.groupId, val).then(res => {
            if (res.success) {
              if (toast) {
                this.$message.success(this.$t('loc.saveSuccess'))
              }
              this.$emit('changeEnableTeacherGroupStatus', val)
            }
          })
        },
        // 选择老师
        selectTeacher (teacher) {
            // 如果已经选择了这个老师，则不做任何处理
            if (this.selectedTeacher.includes(teacher)) {
                return
            }

            // 将这个老师添加到 selectedTeacher 中
            this.selectedTeacher.push(teacher)
            // 直接赋值给 selectAllChildren 和 isIndeterminate，因为它们是对象，Vue 会自动处理响应性
            this.$set(this.selectAllChildren, teacher.id, false)
            this.$set(this.isIndeterminate, teacher.id, false)
            // 选择完老师之后关闭弹窗
            this.$nextTick(() => {
                this.showSelectTeacher = false
            })
        },
        // 添加老师分组
        addTeacherAssignGroups () {
            this.showSelectTeacher = true
            // 添加 Unit Planner 设置的埋点
            this.$analytics.sendEvent('web_weekly_plan_atg_add')
        },
        // 删除老师分组
        deleteTeacherAssignGroups (teacher) {
            this.showSelectTeacher = false
            // 删除的时候必须要留一个老师，如果只有一个老师了，那么就不允许在删除
            if (this.selectedTeacher.length === 1) {
                return
            }
            // 将 teacher 从 selectedTeacher 中删除，按照 teacherId 进行移除
            this.selectedTeacher = this.selectedTeacher.filter((selectedTeacher) => {
                return selectedTeacher.id !== teacher.id
            })

            // 这个老师移除成功之后，要将这个老师中的所有的学生全部给移动到第一个老师的学生下面
            const firstTeacher = this.selectedTeacher.sort((a, b) => {
                return a.display_name.localeCompare(b.display_name)
            })[0]

            // 将 teacher 这个老师的 enrollmentId 添加到 selectedTeacher 下面的 firstTeacher 的 enrollmentId 中
            this.$set(firstTeacher, 'currentEnrollment', firstTeacher.currentEnrollment.concat(teacher.currentEnrollment))

            // 将 teacher 这个老师的 enrollmentId 添加到 selectChildrenselected 下面的 firstTeacher 的 enrollmentId 中
            teacher.currentEnrollment.forEach((enrollmentId) => {
                this.$set(this.selectChildrenselected[firstTeacher.id], enrollmentId, true)
            })

            // 将原有的 teacher 的 enrollmentId 设置为 false
            teacher.currentEnrollment.forEach((enrollmentId) => {
                this.$set(this.selectChildrenselected[teacher.id], enrollmentId, false)
            })

            // 将 teacher 的 enrollmentId 设置为 []
            this.$set(teacher, 'currentEnrollment', [])

            // 将 selectAllChildren 设置为 false
            this.$set(this.selectAllChildren, teacher.id, false)

            // 检查 firstTeacher 是否全选
            this.checkSelectAllChildren(firstTeacher.id)
            // 添加 Unit Planner 设置的埋点
            this.$analytics.sendEvent('web_weekly_plan_atg_delete')
        },
        // 为 teacherId 的老师，选择或者取消所有的学生
        selectAllChild (teacherId, val) {
            let checkedChildren = []
            let checkedChildIds = []
            this.groupTeams.enrollmentTeamModelList.forEach((child) => {
                if (val) {
                    child.selected = true
                    this.$set(this.selectChildrenselected[teacherId], child.enrollmentId, true)
                    checkedChildren.push(child)
                    checkedChildIds.push(child.enrollmentId)
                } else {
                    child.selected = false
                    this.$set(this.selectChildrenselected[teacherId], child.enrollmentId, false)
                }
            })

            // 通过 teacherId 找到对应的 teacher
            this.groupTeams.teacherList.forEach((teacher) => {
                if (teacher.id === teacherId) {
                    // 使用 this.$set 设置 teacher 对象的 currentEnrollment 属性
                    this.$set(teacher, 'currentEnrollment', checkedChildIds)

                    // 同时将 selectChildrenselected 中的 enrollmentId 设置为 true
                    checkedChildIds.forEach((enrollmentId) => {
                        this.$set(this.selectChildrenselected[teacher.id], enrollmentId, true)
                    })

                    // 将全选给添加上，给其他人的全选取消
                    this.$set(this.selectAllChildren, teacherId, true)
                } else {
                    // 如果不是当前的 teacher ，则将 enrollmentId 从 currentEnrollment 中删除
                    teacher.currentEnrollment = teacher.currentEnrollment.filter((enrollmentId) => {
                        return !checkedChildIds.includes(enrollmentId)
                    })

                    // 同时将 selectChildrenselected 中的 enrollmentId 设置为 false
                    checkedChildIds.forEach((enrollmentId) => {
                        this.$set(this.selectChildrenselected[teacher.id], enrollmentId, false)
                    })

                    // 将全选给添加上，给其他人的全选取消
                    this.$set(this.selectAllChildren, teacherId, false)
                }
            })

            this.checkSelectAllChildren(teacherId)
            // 添加 Unit Planner 设置的埋点
            this.$analytics.sendEvent('web_weekly_plan_atg_select_children')
        },
        // 检查 teacherId 对应的老师选择的学生是否是全选的
        checkSelectAllChildren (teacherId) {
            // 通过 teacherId 找到对应的 teacher
            let currentTeacher = this.groupTeams.teacherList.find((teacher) => {
                return teacher.id === teacherId
            })

            // 如果 currentTeacher 存在
            if (currentTeacher) {
                let checkedCount = currentTeacher.currentEnrollment.length

                // 使用 this.$set 设置 selectAllChildren 对象的属性
                this.$set(this.selectAllChildren, teacherId, checkedCount === this.groupTeams.enrollmentTeamModelList.length)

                // 使用 this.$set 设置 isIndeterminate 对象的属性
                this.$set(this.isIndeterminate, teacherId, checkedCount > 0 && checkedCount < this.groupTeams.enrollmentTeamModelList.length)
            }
        },
        // 选择一个小孩，选择这个小孩以及选中的这个老师
        changeChild (enrollment, teacherId, val) {
            // 定义一个数组，用于存放选中的小孩
            let checkedChildren = []
            // 定义一个数组，用于存放选中的小孩的 enrollmentId
            let checkedChildIds = []

            this.groupTeams.enrollmentTeamModelList.forEach((child) => {
                if (this.selectChildrenselected[teacherId][child.enrollmentId]) {
                    checkedChildren.push(child)
                    checkedChildIds.push(child.enrollmentId)
                }
            })

            // 通过 teacherId 找到对应的 teacher
            this.groupTeams.teacherList.forEach((teacher) => {
                if (teacher.id === teacherId) {
                    // 使用 this.$set 设置 teacher 对象的 currentEnrollment 属性
                    this.$set(teacher, 'currentEnrollment', checkedChildIds)
                } else {
                    // 如果不是当前的 teacher ，则将 enrollmentId 从 currentEnrollment 中删除
                    teacher.currentEnrollment = teacher.currentEnrollment.filter((enrollmentId) => {
                        return enrollmentId !== enrollment.enrollmentId
                    })

                    // 同时将 selectChildrenselected 中的 enrollmentId 设置为 false
                    this.$set(this.selectChildrenselected[teacher.id], enrollment.enrollmentId, false)
                }
            })

            this.checkSelectAllChildren(teacherId)
            // 添加 Unit Planner 设置的埋点
            this.$analytics.sendEvent('web_weekly_plan_atg_select_children')
        },
        // 格式化用户的名称
        formatUserName () {
            return formatUserName
        },
        // 关闭弹窗，如果 saveClose 是 true 的时候，说明是通过点击保存而关闭弹窗的，否则就是通过点击关闭按钮关闭的
        handleClose (saveClose) {
            this.assignTeacherGroupsShow = false
            // 向父组件发送关闭弹窗的事件
            this.$emit('close', saveClose)
            // 添加 Unit Planner 设置的埋点
            // 如果点击关闭，即为 false 的时候发送关闭埋点
            if (!saveClose) {
                this.$analytics.sendEvent('web_weekly_plan_atg_close')
            }
        },
        // 是否展示选择学生的弹窗
        showChildPopoverVisible (teacherId) {
            // 首先判断总共有多少个老师,如果 teacherList 为 1,那么就不允许弹窗
            if (this.groupTeams.teacherList.length === 1) {
                // 使用 this.$set 设置 childPopoverVisible 对象的属性
                this.$set(this.childPopoverVisible, teacherId, false)
                return
            }
            this.$set(this.childPopoverVisible, teacherId, true)
        },
        // 展示弹窗
        showAssignTeacherGroups (selectGroupId, selectCenterId) {
            this.$analytics.sendEvent('web_weekly_plan_assign_teacher_groups')
            // 设置 selectedGroupId
            this.selectedGroupId = selectGroupId
            // 设置 selectedCenterId
            this.selectedCenterId = selectCenterId
            this.assignTeacherGroupsShow = true
            // 发送请求之前清理旧数据
            this.getGroupTeams()
            this.initGroupTeamsFormRules()
        },
        sortTeacher () {
            // 对老师数据进行排序
            this.groupTeams.teacherList.sort((a, b) => {
                return a.display_name.localeCompare(b.display_name)
            })
        },
        // 当前 teacherId 对应的老师选择的小孩的长度
        selectdChildLength (teacherId) {
            return this.selectdChild(teacherId).length
        },
        // 当前 teacherId 对应的老师选择的小孩都有那些
        selectdChild (teacherId) {
            // 从 this.groupTeams.teacherList 中获取所有选择过的学生
            let selectTeacher = this.groupTeams.teacherList.filter((teacher) => {
                return teacher.id === teacherId
            })
            // 从 teacher 中获取所有选择过的学生
            if (selectTeacher) {
                selectTeacher = selectTeacher[0]
            }
            const selectEnrollment = selectTeacher.currentEnrollment || []
            // 返回选择过的学生
            return this.groupTeams.enrollmentTeamModelList.filter((enrollment) => {
                return selectEnrollment.includes(enrollment.enrollmentId)
            })
        },
        // 为 teacherId 添加 currentChild
        selectChild (teacherId, currentChild) {
            if (!currentChild) {
                currentChild = this.selectdChild(teacherId)
            }
            // 如果 currentChild.length 等于 0 ，则不需要进行判断
            if (currentChild.length === 0) {
                return
            }
            // 对选中的学生进行去重，使用 enrollmentId 进行去重
            this.groupTeams.teacherList.forEach((teacher) => {
                if (teacher.id === teacherId) {
                    const enrollment = teacher.currentEnrollment || []
                    // 对 enrollment 进行去重，按照 enrollmentId 对 enrollment 进行去重
                    teacher.currentEnrollment = enrollment.filter((item, index) => {
                        // 如果得到的这个 item 在 enrollment 中的 index 等于当前的 index，则说明这个 item 是第一次出现，否则就是重复出现
                        return enrollment.findIndex((enrollmentItem) => {
                            return enrollmentItem === item
                        }) === index
                    })
                }
            })
            // 选中的学生不能超过最大的学生数，最小数也不能小于最小的学生数
            if (currentChild.length > this.groupTeams.maxTeamSize) {
                this.$refs['selectEnrollment' + teacherId].blur()
            }
            if (currentChild.length < this.groupTeams.minTeamSize) {
                this.$refs['selectEnrollment' + teacherId].blur()
            }
        },
        // 初始化选中的学生
        initSelectChildrenselected () {
            this.groupTeams.teacherList.forEach((teacher) => {
                // 使用 this.$set 设置 selectChildrenselected 对象的属性
                this.$set(this.selectChildrenselected, teacher.id, [])

                this.groupTeams.enrollmentTeamModelList.forEach((enrollment) => {
                    // 使用 this.$set 设置 selectChildrenselected 对象的属性
                    this.$set(this.selectChildrenselected[teacher.id], enrollment.enrollmentId, teacher.currentEnrollment.includes(enrollment.enrollmentId))
                })

                // 将这些老师添加到 selectedTeacher
                if (teacher.currentEnrollment.length > 0) {
                    this.selectedTeacher.push(teacher)
                }
            })
        },
        // 处理请求得到的数据
        processGroupTermData (groupTerm) {
            // 将 res 的值赋值给 groupTeams
            this.$set(this, 'groupTeams', groupTerm)
            // 初始化 selectTeacher
            this.$set(this, 'selectedTeacher', [])
            // 处理数据
            // 将 enrollmentTeamModelList 中的数据转换为 teacherList.currentEnrollment 中的数据，如果 enrollmentTeamModelList 中的小孩的 teacherId 与 teacherList 中的 teacherId 相同，则将 enrollmentTeamModelList 中的小孩添加到 teacherList.currentEnrollment 中
            this.groupTeams.teacherList.forEach((teacher) => {
                // 将对应的 enrollmentTeamModelList 中的小孩的 selcted 设置为 true
                teacher.currentEnrollment = this.groupTeams.enrollmentTeamModelList.filter((enrollment) => {
                    return enrollment.teacherId === teacher.id
                }).map((enrollment) => {
                    return enrollment.enrollmentId
                })
                teacher.currentEnrollment.forEach((enrollmentId) => {
                    this.groupTeams.enrollmentTeamModelList.forEach((enrollment) => {
                        if (enrollment.enrollmentId === enrollmentId) {
                            enrollment.selected = true
                        }
                    })
                })
                this.checkSelectAllChildren(teacher.id)
            })
            this.initSelectChildrenselected()
        },
        // 获取班级的信息
        getGroupTeams () {
            if (this.selectedGroupId) {
                // 获取班级的信息
                this.$axios.get($api.urls().getGroupTeams + '?groupId=' + this.selectedGroupId + '&enableGroupTeams=true')
                    .then((res) => {
                        if (res) {
                            this.$nextTick(() => {
                                // 如果 res 是存在的，那么就将 res 的值赋值给 groupTeams
                                this.processGroupTermData(res)
                            })
                        }
                    })
                    .catch((error) => {
                        this.groupTeams = {}
                    })
            }
        },
        // 保存 group terms 信息
        saveGroupTerms () {
            // 校验元素是否合理
            this.$refs.groupTeamsFormRef.validate((valid) => {
                // 如果校验通过，则调用保存的方法
                if (valid && !this.fromValidedError) {
                    // 将 this.groupTeams 转化为 UpdateGroupTeamsRequest
                    let param = null
                    if (this.groupTeams) {
                        param = this.convertParamToRequest(this.groupTeams)
                    }
                    this.handleTeacherGroups(param)
                    this.saveLoading = true
                    // 调用方法保存 group terms 信息
                    this.$axios.post($api.urls().saveGroupTerms, param)
                        .then((res) => {
                            if (res) {
                                if (res.success) {
                                    this.$message({
                                        message: this.$t('loc.saveSuccess'),
                                        type: 'success'
                                    })
                                } else {
                                    this.$message({
                                        message: this.$t('loc.unitPlannerStep3SavedUnsuccessfully'),
                                        type: 'error'
                                    })
                                }
                                this.saveLoading = false
                                // 关闭弹窗
                                this.handleClose(true)
                            }
                        })
                        .catch((error) => {
                            this.$message({
                                message: this.$t('loc.unitPlannerStep3SavedUnsuccessfully'),
                                type: 'error'
                            })
                            this.saveLoading = false
                            // 关闭弹窗
                            this.handleClose(true)
                        })
                    // 添加 Unit Planner 设置的埋点
                    this.$analytics.sendEvent('web_weekly_plan_atg_save')
                }
            })
        },
        // 清理老数据
        clearOldData () {
            const params = this.convertParamToRequest(this.groupTeams)
            this.handleTeacherGroups(params)
            this.$set(this, 'selectedGroupId', undefined)
            this.$set(this, 'selectedCenterId', undefined)
            this.$set(this, 'isIndeterminate', [])
            this.$set(this, 'selectAllChildren', [])
            this.$set(this, 'childPopoverVisible', [])
            this.$set(this, 'showSelectTeacher', false)
            this.$set(this, 'selectChildrenselected', [])
            this.$set(this, 'groupTeams', {})
            this.selectedGroupId = undefined
            this.selectedCenterId = undefined
            this.isIndeterminate = []
            this.selectAllChildren = []
            this.childPopoverVisible = []
            this.showSelectTeacher = false
            this.selectChildrenselected = []
            this.groupTeams = {}
        },
        // 将 groupTeams 属性 转化为 Request 对象
        convertParamToRequest (jsObject) {
            const javaRequest = {
                groupId: this.selectedGroupId,
                groupName: jsObject.groupName,
                enrollmentTeamModelList: [],
                minTeamSize: jsObject.minTeamSize,
                maxTeamSize: jsObject.maxTeamSize
            }

            // 循环遍历所有选中的老师
            this.selectedTeacher.forEach((teacher) => {
                // 循环遍历所有的学生
                jsObject.enrollmentTeamModelList.forEach((child) => {
                    // 如果这个学生对应的这个选中的老师也是选中的状态，则将这个学生添加到 enrollmentTeamModelList 中
                    const isSelected = this.selectChildrenselected[teacher.id] && this.selectChildrenselected[teacher.id][child.enrollmentId]

                    if (isSelected) {
                        // 添加到 enrollmentTeamModelList 中
                        javaRequest.enrollmentTeamModelList.push({
                            enrollmentId: child.enrollmentId,
                            teacherId: teacher.id,
                            firstName: child.firstName,
                            lastName: child.lastName,
                            avatarUrl: child.avatarUrl,
                            iep: child.iep,
                            eld: child.eld
                        })
                    }
                })
            })

            // 如果 javaRequest 中的 enrollmentTeamModelList 是空的，那么就将 jsObject.enrollmentTeamModelList 所有的小孩都放在 this.selectedTeacher 的第一个老师中
            if (javaRequest.enrollmentTeamModelList.length === 0) {
                // 获取第一个老师
                const firstTeacher = this.selectedTeacher[0]
                // 循环遍历所有的学生
                jsObject.enrollmentTeamModelList.forEach((child) => {
                    // 添加到 enrollmentTeamModelList 中
                    javaRequest.enrollmentTeamModelList.push({
                        enrollmentId: child.enrollmentId,
                        teacherId: firstTeacher.id,
                        firstName: child.firstName,
                        lastName: child.lastName,
                        avatarUrl: child.avatarUrl,
                        iep: child.iep,
                        eld: child.eld
                    })
                })
            }
            // 如果 jsObject.enrollmentTeamModelList 这些小孩中存在在任意一个老师中都没有被选择，那么就将这些没有被选择的小孩都放在第一个老师的下面
            // 循环遍历所有的学生
            jsObject.enrollmentTeamModelList.forEach((child) => {
                // 定义一个标识，用于标识这个小孩是否被选择
                let isSelected = false
                // 循环遍历所有选中的老师
                this.selectedTeacher.forEach((teacher) => {
                    // 如果这个学生对应的这个选中的老师也是选中的状态，则将这个学生添加到 enrollmentTeamModelList 中
                    if (this.selectChildrenselected[teacher.id] && this.selectChildrenselected[teacher.id][child.enrollmentId]) {
                        isSelected = true
                    }
                })
                // 如果这个小孩没有被选择，则将这个小孩添加到第一个老师的下面
                if (!isSelected) {
                    // 获取第一个老师
                    const firstTeacher = this.selectedTeacher[0]
                    // 添加到 enrollmentTeamModelList 中
                    javaRequest.enrollmentTeamModelList.push({
                        enrollmentId: child.enrollmentId,
                        teacherId: firstTeacher.id,
                        firstName: child.firstName,
                        lastName: child.lastName,
                        avatarUrl: child.avatarUrl,
                        iep: child.iep,
                        eld: child.eld
                    })
                }
            })
            // 返回转化成的 Request 对象
            return javaRequest
        },
        // 初始化 GroupTeams 表单验证规则
        initGroupTeamsFormRules () {
            // 初始化单元基本信息表单验证规则
            // 周数校验规则
            let validateWeekCount = (rule, value, callback) => {
                // 开始校验的时候，先将 fromValidedError 设置为 false
                this.fromValidedError = false
                if (!value) {
                    this.fromValidedError = true
                    callback()
                    return
                }
                // 输入的是一个字符串，将它转化为 Number 类型
                value = Number(value)
                // 判断是否是数字
                // 如果不是数字，直接报错
                if (!Number.isInteger(value)) {
                    this.fromValidedError = true
                    callback()
                }
                // 转换为数字
                value = parseInt(value)
                if (value < 1 || value > this.groupTeams.enrollmentTeamModelList.length) {
                    this.fromValidedError = true
                    callback()
                } else {
                    callback()
                }
                // 最小值不能大于最大值
                if (this.groupTeams.minTeamSize > this.groupTeams.maxTeamSize) {
                    this.fromValidedError = true
                    callback()
                }
            }
            // 表单校验规则
            this.groupTeamsRules = {
                minTeamSize: [
                    { required: true, message: 'Please enter the number of min group size!', trigger: 'blur' },
                    { validator: validateWeekCount, trigger: 'change' }
                ],
                maxTeamSize: [
                    { required: true, message: 'Please enter the number of max group size!', trigger: 'blur' },
                    { validator: validateWeekCount, trigger: 'change' }
                ]
            }
        },
        handleTeacherGroups (params) {
          let teacherIds = []
          if (params) {
            teacherIds = params.enrollmentTeamModelList.map(item => item.teacherId)
          } else {
            params = this.convertParamToRequest(this.groupTeams)
            teacherIds = params.enrollmentTeamModelList.map(item => item.teacherId)
          }
          // 如果分组中没有选择老师或者只选择了一个老师，此时点击保存，那么就将分组关闭
          if (teacherIds.length <= 1) {
            this.changeTeacherGroups(false, false)
          }
        }
    }
}
</script>

<style lang="less" scoped>
.assign-teacher-dialog {
    display: flex;
    width: 880px;
    padding: 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 8px;
    background: #FFF;

    /* 卡片投影 */
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);

    .dialog-title {
        display: flex;
        align-items: flex-start;
        align-self: center;

        .dialog-title-content {
            flex: 1 0 0;
            color: var(--111-c-1-c, #111C1C);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Semi Bold/20px */
            font-family: Inter;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 26px; /* 130% */
        }

        .dialog-close {
            cursor: pointer;
            width: 20px;
            height: 20px;
        }
    }

    .dialog-body {
        display: flex;
        margin-top: -24px;
        margin-bottom: -24px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .body-text {
            color: var(--color-text-placeholder);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Regular/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }

        .teacher-switch {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
        }

        .teacher-groups {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
            align-self: stretch;
        }

        .teacher-groups-tag {
            display: flex;
            height: 340px;
            align-items: flex-start;
            justify-content: space-between;
            gap: 10px;
            flex: 1 0 0;
            align-self: stretch;
            background: #FFF;
            // 保证元素的空间不会被挤压
            & > div {
                flex-grow: 1
            }

            .teacher-groups-tag-title {
                display: flex;
                height: 52px;
                padding: 4px 12px;
                align-items: center;
                gap: 8px;
                justify-content: space-between;
                align-self: center;
                border-top: 1px solid var(--dcdfe-6, #DCDFE6);
                border-right: 1px solid var(--dcdfe-6, #DCDFE6);
                border-left: 1px solid var(--dcdfe-6, #DCDFE6);
                background: var(--f-5-f-6-f-8, #F5F6F8);
            }

            .teacher-groups-tag-content {
                display: flex;
                padding: 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                flex: 1 0 0;
                align-self: stretch;
                border-right: 1px solid var(--dcdfe-6, #DCDFE6);
                border-bottom: 1px solid var(--dcdfe-6, #DCDFE6);
                border-left: 1px solid var(--dcdfe-6, #DCDFE6);
                background: #FFF;

                .teacher-groups-tag-content-title {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    align-self: stretch;

                    .select-child-btn {
                        display: flex;
                        height: 32px;
                        padding: 8px 12px;
                        justify-content: center;
                        align-items: center;
                        gap: 8px;
                        border-radius: 4px;
                        border: 2px solid var(--10-b-3-b-7, #10B3B7);
                        background: var(--ffffff, #FFF);
                        cursor: pointer;
                    }
                }

                .teacher-groups-children {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 10px;
                }

                .enrollment-tag {
                    display: flex;
                    height: 32px;
                    padding: 4px 8px;
                    align-items: center;
                    justify-content: flex-start;
                    gap: 8px;
                }
            }
        }

        .teacher-groups-size {
            display: flex;
            align-items: center;
            gap: 16px;
            align-self: stretch;

            .min-max-group-size {
                display: flex;
                align-items: center;
                gap: 4px;

                .min-group-size, .max-group-size {
                    /deep/ .el-input__inner {
                        display: flex;
                        width: 70px;
                        height: 40px;
                        padding: 9px 12px;
                        align-items: center;
                        gap: 4px;
                        border-radius: 4px;
                        border: 2px solid var(--dcdfe-6, #DCDFE6);
                        background: var(--ffffff, #FFF);
                    }
                }

                .divide-line-gray {
                    color: var(--676879, #676879);
                    background-color: var(--color-border) !important;
                    text-align: center;
                    font-feature-settings: 'clig' off, 'liga' off;
                    height: 2px;
                    width: 14px;
                    /* Regular/14px */
                    font-family: Inter;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                }
            }
        }
    }
}

.empty-status {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

/deep/ .width-full {
    width: 100% !important;

    & {
        width: 100% !important;
    }
}

.height-22 {
    height: 22px;
}

/deep/ .el-image {
    border-radius: 4px !important;
}

/deep/ .el-form-item {
    margin-bottom: 0;
}

@media only screen and (max-width: 1199px) {
    //ipad
    .ipad_width {
        display: flex;
        justify-content: flex-start;
        max-width: 15px;
    }
}

.child-area {
    padding: 5px 10px;
    min-height: 60px;
    max-height: 300px;
    overflow-y: auto;
}

.child-area {
    padding: 0 !important;
    margin-bottom: 12px;
}

.search-child-area {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 2;
    padding: 10px 10px 5px 10px;
}

.child-list {
    overflow-y: auto;
}

/deep/ .el-tag--info {
    color: #323338;
}

.child-check-row {
    .child-avatar {
        max-width: 32px;
        border-radius: 50%;
    }

    /deep/ .el-checkbox {
        margin-bottom: 0 !important;
    }

    /deep/ .el-checkbox__label {
        width: calc(100% - 15px);
    }

    padding: 10px 10px 0 10px;
}

.search-lesson-content {
    min-height: 45px;
    display: flex;
    align-items: center;
}

/deep/ .el-scrollbar .is-horizontal {
    display: none;
}

/deep/ .el-scrollbar__wrap {
    overflow-x: hidden;
}

/deep/ .el-col {
    padding: 0 !important;
}

/deep/ .el-icon-error:before {
    font-family: 'lg-icon';
    content: "\e6a7";
}
.add-margin-r-42 {
    margin-right: 42px;
}
</style>
<style lang="less">
.select-child-popover {
    padding: 4px 0px !important;

    .enrollment-tag {
        display: flex;
        height: 32px;
        padding: 4px 8px;
        align-items: center;
        justify-content: flex-start;
        gap: 4px;
    }
}

.select-teacher-popover {
    padding: 0;

    .teacher-select {
        display: flex;
        padding: 0px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 4px;
        background: var(--color-white);
        cursor: pointer;

        .teacher-groups-tag-title {
            width: 100%;
            padding: 8px;
        }
    }

    .teacher-select :hover {
        background: var(--color-primary-light);
    }
}
</style>