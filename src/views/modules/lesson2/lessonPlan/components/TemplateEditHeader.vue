<template>
  <div class="bg-white flex-space-between align-items lg-border-radius-8 lg-margin-bottom-20">
    <div class="display-flex align-items">
      <div v-if="isComeFromIPad">
        <img class="go-back lg-pointer" src="@/assets/img/us/back_home.png" @click="goBack">
      </div>
      <!-- 审核状态 -->
      <div class="m-l-md font-size-16 add-padding-tb-10">
        <div v-if="planInfo.status == 'D_APPROVED' && false">
          <img style="width: 60px;" class="m-r-xs" src="@/assets/img/lesson2/plan/approved.png">
        </div>
        <div v-if="planInfo.status == 'A_DRAFT'">
          <img style="width: 60px;" class="m-r-xs" src="@/assets/img/lesson2/plan/draft.png">
        </div>
      </div>
      <!-- 框架信息 -->
      <div>{{ frameworkName }}</div>
    </div>
    <!-- 生成PDF按钮 -->
    <div class="lg-margin-right-20 lg-padding-t-b-12">
      <!-- 复制按钮 -->
      <el-button plain v-if="planInfo && planInfo.id" :loading="replicateLoading" @click="replicateModelVisible = true"><i class="lg-icon lg-icon-copy lg-margin-right-8"></i>{{ $t('loc.plan66') }}</el-button>
      <!-- 打印 -->
      <el-button v-if="planInfo && planInfo.id" type="primary" @click.stop="generatePDF(true)"><i class="lg-icon lg-icon-print lg-margin-right-8"></i>{{ $t('loc.plan174') }}</el-button>
      <!-- 下载 打印 菜单 -->
      <el-popover
        placement="bottom"
        width="150"
        trigger="click"
        v-model="morePopVisable">
        <!-- 复制按钮 -->
        <div>
          <el-link
            :underline="false"
            :disabled="planRecording"
            @click.stop="replicateModelVisible = true">
            <i class="lg-icon lg-icon-copy font-size-24 lg-margin-right-16"></i>
            <span>{{$t('loc.plan66')}}</span>
          </el-link>
        </div>
        <el-divider class="action-divider"></el-divider>
        <!-- PDF 下载和打印 -->
        <div>
          <el-link
            :underline="false"
            @click.stop="generatePDF(false)">
            <i class="lg-icon lg-icon-download font-size-24 lg-margin-right-16"></i>{{$t('loc.download')}}
          </el-link>
        </div>
        <div>
          <el-link
            :underline="false"
            @click.stop="generatePDF(true)">
            <i class="lg-icon lg-icon-print font-size-24 lg-margin-right-16"></i>{{$t('loc.plan174')}}
          </el-link>
        </div>
        <el-divider class="action-divider"></el-divider>
        <!-- 删除 -->
        <div>
          <el-link
            class="action-delete"
            :underline="false"
            :disabled="planInfo.lockedData || !planInfo.canEditOrDelete || planRecording"
            @click.stop="deletePlan()">
            <i class="lg-icon lg-icon-delete font-size-24 lg-margin-right-16"></i>{{$t('loc.delete')}}</el-link>
        </div>
        <el-button v-if="planInfo && planInfo.id" class="m-l-sm" icon="lg-icon lg-icon-more-horizontal" slot="reference" @click="morePopVisable=!morePopVisable"></el-button>
      </el-popover>
    </div>
    <!-- 复制确认弹窗 -->
    <el-dialog
      :title="$t('loc.confirmation')"
      custom-class="lg-el-dialog"
      :visible.sync="replicateModelVisible"
      width="30%">
      <span class="lg-color-text-primary">{{$t('loc.plan67')}}</span>
      <span slot="footer" class="dialog-footer">
        <el-button class="font-weight-normal" plain @click="replicateModelVisible = false" :disabled="replicateLoading">{{$t('loc.cancel')}}</el-button>
        <el-button class="font-weight-normal" type="primary" @click="replicatePlan" :loading="replicateLoading">{{$t('loc.confirm')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import { acrossRole } from '@/utils/common'
import { mapState } from 'vuex'
export default {
  name: 'TemplateEditHeader',
  props: {
    title: {
      type: String
    },
    planInfo: {
      type: Object
    },
    frameworks: {
      type: Array
    },
    showCore: {
      type: Boolean,
      default: false
    },
    defaultGroupId: {
      type: String
    }
  },
  data () {
    return {
      pdfLoading: false, // 下载 PDF 加载状态
      replicateLoading: false, // 复制按钮加载状态
      replicateModelVisible: false, // 复制确认弹窗状态
      morePopVisable: false // 下载 PDF 弹窗状态
    }
  },
  watch: {
    // 监听 PDF 下载, 加载时页面loading
    pdfLoading: {
      handler: function (val) {
        this.$emit('callDownloading', val)
      }
    }
  },
  computed: {
    ...mapState({
      planRecording: state => state.lesson.planRecording
    }),
    isAdmin () {
      return acrossRole('agency_admin', 'agency_owner', 'site_admin')
    },
    frameworkName () {
      if (this.planInfo && this.planInfo.frameworkId && this.frameworks && this.frameworks.length > 0) {
        let framework = this.frameworks.find(item => item.frameworkId.toUpperCase() == this.planInfo.frameworkId.toUpperCase())
        if (framework) {
          return framework.frameworkName
        }
      }
      return ''
    },
    isComeFromIPad () {
      return tools.isComeFromIPad()
    }
  },
  methods: {
    async goBack () {
      // 如果正在录制，提示是否退出录制
      let exist = true
      if (this.planRecording) {
        // 警告提示， 如果退出会终止录制
        await this.$confirm(this.$t('loc.plan142'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
        }).catch(() => {
          exist = false
        })
      }
      if (!exist) {
        return
      }
      // 编辑状态，更新编辑锁定信息
      if (this.edit && this.planInfo.id) {
        LessonApi.unlockEditing({
          id: this.planInfo.id
        })
      }
      if (this.planInfo && this.planInfo.type == 'NORMAL_TEMPLATE') {
        this.$router.push({
          name: 'list-plan'
        })
        return
      }
      // 如果过是管理员查看，跳转到管理员分配页面
      this.$router.push({
        name: 'template-list'
      })
    },
    /**
     * 生成 PDF
     */
    generatePDF (print) {
      this.morePopVisable = false
      // 如果是火狐浏览器，提示不支持
      if (print && tools.isFirefox()) {
        this.$alert(this.$t('loc.plan176'), this.$t('loc.plan135'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('loc.download'),
          customClass: 'el-message-box-font',
          callback: action => {
            if (action == 'confirm') {
              this.generatePDF(false)
            }
          }
        })
        return
      }
      this.pdfLoading = true
      LessonApi.getPlanPDF({
        planId: this.planInfo.id,
        showCore: this.showCore
      }).then(response => {
        this.getPlanPDF(print)
      }).catch(error => {})
    },
    /**
     * 获取周计划 PDF
     */
    getPlanPDF (print) {
      LessonApi.getPDFList({
        type: 'WEEKLY_PLAN'
      }).then(response => {
        if (response && response.length > 0) {
          let pdf = response[0]
          if (pdf && pdf.status !== 'SUCCEED' && pdf.status !== 'FAILED') {
            // 继续轮询
            setTimeout(() => {
              this.getPlanPDF(print)
            }, 3000)
          } else if (pdf && pdf.status === 'SUCCEED' && print) {
            this.$print({
              printable: pdf.pdfUrl,
              type: 'pdf',
              onLoadingStart: () => {
              },
              onLoadingEnd: () => {
                this.pdfLoading = false
              },
              onError: (error) => {
                this.downloadPDFWithAlert(pdf)
                this.pdfLoading = false
              }
            })
          } else {
            this.downloadPDFWithAlert(pdf)
            this.pdfLoading = false
          }
        }
      })
    },

    /**
     * 下载 PDF 弹窗
     */
    downloadPDFWithAlert (pdf) {
      // 成功弹窗下载
      this.$alert('<div class="display-flex align-items"><img style="height: 30px; margin: 5px" src="' + this.getfilePhoto(pdf.pdfName) + '"> <span class="text-ellipsis" title="' + pdf.pdfName + '">' + pdf.pdfName + '</span></div>', this.$t('loc.plan61'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download'),
        customClass: 'el-message-box-font',
        callback: action => {
          if (action == 'confirm') {
            if (tools.isComeFromIPad()) {
              let requestData = {
                'emailTemplate': 'weekly_lesson_planning',
                'downloadFileUrl': pdf.pdfUrl,
                'fileName': pdf.pdfName,
                'week': this.planData.week,
                'className': this.planData.groupName,
                'siteName': this.planData.centerName,
                'fromDate': this.planData.fromAtLocal,
                'toDate': this.planData.toAtLocal,
                'courseName': this.planData.theme
              }
              this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
              .then(() => {
                this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                  confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                  showCancelButton: false
                })
              }).catch(error => {
                this.$message.error(error.message)
              })
            } else {
              const eleLink = document.createElement('a')
              eleLink.style.display = 'none'
              eleLink.href = pdf.pdfUrl
              // 触发点击
              document.body.appendChild(eleLink)
              eleLink.click()
              // 移除
              document.body.removeChild(eleLink)
            }
          }
        }
      })
    },
    /**
     * 复制周计划
     */
     replicatePlan () {
      this.replicateLoading = true
      LessonApi.replicatePlan({
        planId: this.planInfo.id,
        groupId: this.defaultGroupId
      }).then(response => {
        this.replicateLoading = false
        this.replicateModelVisible = false
        if (this.isAdmin) {
          this.$router.push({
            name: 'edit-template',
            params: {
              planId: response.id,
              create: true
            }
          })
        } else {
          this.$router.push({
            name: 'edit-plan',
            params: {
              planId: response.id
            }
          })
        }
      }).catch(error => {
        this.replicateLoading = false
        this.replicateModelVisible = false
      })
    },
    /**
     * 删除周计划
     */
    deletePlan () {
      this.morePopVisable = false
      this.$confirm(this.$t('loc.plan11'), this.$t('loc.cfm'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        customClass: 'lg-message-box',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        LessonApi.deletePlan({}, {
          id: this.planInfo.id
        }).then(response => {
          this.$message({
            type: 'success',
            message: this.$t('loc.plan12')
          })
          this.$router.push({
            name: 'list-plan'
          })
        })
      }).catch(() => {
      })
    },
    /**
     * 获取文件类型 icon
     */
    getfilePhoto (fileName) {
      if (fileName.endsWith('doc')) {
        return doc
      }
      if (fileName.endsWith('docx')) {
        return docx
      }
      if (fileName.endsWith('pdf')) {
        return pdf
      }
      if (fileName.endsWith('ppt')) {
        return ppt
      }
      if (fileName.endsWith('pptx')) {
        return pptx
      }
      if (fileName.endsWith('xls')) {
        return xls
      }
      if (fileName.endsWith('xlsx')) {
        return xlsx
      }
      return file
    }
  }
}
</script>

<style lang="less" scoped>
.action-divider {
  margin: 8px 0px !important;
}

.action-delete:hover:not(.is-disabled) {
  color: #f56c6c !important;
}

/deep/ .el-link--inner {
  display: flex;
  align-items: center;
}
.go-back {
  max-width: 24px;
  margin-left: 12px;
}
.header {
  text-align: center;
  height: 50px;
  padding: 0 20px;
  line-height: 50px;
  color: #111C1C;

  :first-child {
    float: left;
  }

  :last-child {
    font-size: 18px;
    color: #111C1C;
  }
}
</style>
