<template>
  <div class="reflect-lesson-detail h-full lg-scrollbar-show bg-white lg-padding-24 lg-margin-right-24">
    <div class="header display-flex justify-content-between">
      <div class="display-flex align-items text-warning">
        <div class="color-block-warning"></div>
        <div class="font-size-16">{{$t('loc.plan73')}}</div>
      </div>
    </div>
    <div class="m-t-sm">
      <lesson-detail :isFromLibrary="isFromLibrary" :lessonId="lessonId" :planPreview=true>
      </lesson-detail>
    </div>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import LessonDetail from '../../lessonLibrary/components/LessonDetail'

export default {
  name: 'ReflectionLessonDetail',
  components: {
    LessonDetail
  },

  props: {
    lessonId: {
      type: String
    }
  },

  data () {
    return {
      isFromLibrary: false
    }
  },

  methods: {
  }
}
</script>

<style lang="less" scoped>
.reflect-lesson-detail {
  overflow: auto;
}
/deep/ .lesson-detail {
  padding: 0;
}
</style>
