<template>
  <div v-clickoutside="closePopover"
       :id="item.guideItemDomId"
       :style="{'min-height': itemHeight + 'px'}"
       :class="[hasContent ? 'plan-drag-info' : '', isTextCol ? 'text-col':'activity-col' , !edit && item.canAddReflection && !isAdmin ? 'has-relfection-item': '', isTemplate ? 'area-disabled': '']"
       class="position-relative"
       @mouseenter="enterItem()"
       @mouseleave="leaveItem()"
       :data-drag-info="dragInfo"
       v-if="hideEmptyItem"
       ref="itemArea">
    <div v-if="isFirstActivity" id="step3-info" class="position-absolute w-full h-full"
         style="right: 0px;top: 0px;z-index: 99;display: none;width: 200px;height: 180px">
      <img class="w-full h-full" src="~@/assets/img/lesson2/guide/plan_guide.png" alt="plan_guide">
    </div>
    <!--拖拽及复制按钮-->
    <div v-if="showDragHandle" class="display-flex position-absolute add-padding-b-10" style="top: -45px" :style="dragHoverWidth">
      <div class="drag-handle-box">
        <div :class="{'drag-handle':!copyLessonLoading}">
          <el-tooltip class="item" effect="dark" :content="$t('loc.dragItemTip')" placement="top">
            <el-button @mousedown.native="beforeDragHandler" @ondragstart.native="changeDragStatus(true)" :disabled="copyLessonLoading" style="opacity: 1!important;"
                       @mouseleave.native="changeDragStatus(false)" class="el-button-icon"><i
              class="lg-icon lg-icon-move font-size-24"></i></el-button>
          </el-tooltip>
        </div>
      </div>
      <el-tooltip class="item" effect="dark" :content="$t('loc.plan66')" placement="top" v-if="showCopyButton">
        <el-button @click="copyLesson(false)" class="add-margin-l-10 lesson-copy-button" :loading="showCopyLessonLoading">
           <i class="lg-icon lg-icon-copy font-size-24" v-show="!showCopyLessonLoading"></i>
        </el-button>
      </el-tooltip>
    </div>

    <!--需要添加活动或课程的内容 -->
    <div class="item-area"
         v-if="type === 'DAY_COL' || (type === 'WEEK_COL' && long) || type === 'DAY_TEXT_COL' || type === 'WEEK_TEXT_COL'">
      <!-- 课程封面 -->
      <div
        class="display-flex align-items add-margin-b-3 justify-content position-relative lessons-cover"
        :class="type === 'WEEK_COL' ? 'lesson-media-small' : 'lesson-media'"
        v-show="item.mediaUrl && edit">
        <media-viewer :url="item.mediaUrl" :hideVideo=true></media-viewer>
        <div class="preview-lesson btn-center flex-direction-col">
          <div class="w-full display-flex justify-content align-items">
            <el-button size="mini" class="preview-icon" :style="{ top: showAdaptBtn ? '' : '40%' }"
                       icon="el-icon-view" @click="previewLesson(true, true)">{{ $t('loc.preview') }}
            </el-button>
          </div>
          <div class="w-full display-flex justify-content align-items">
            <el-button size="mini" class="adapt-icon" v-if="showAdaptBtn" @click="adaptUDLAndCLR(false)">
              <template #icon>
                <i class="lg-icon lg-icon-generate"></i>
              </template>
              {{ $t('loc.lessonAdapt') }}
            </el-button>
          </div>
        </div>
        <el-tag v-if="item.isAdaptedLesson" class="adapted-tag" size="mini">{{
            $t('loc.adaptUnitPlanner25')
          }}
        </el-tag>
      </div>
      <!--  一行内容 / 五列课程内容 -->
      <el-popover
        v-if="type === 'DAY_COL' || (type === 'WEEK_COL' && long)"
        :placement="showAddThirdPartyLessonPopover || item.link || item.lessonId ? 'bottom-start' : 'right-start'"
        trigger="manual"
        popper-class="input-lesson-popover lg-scrollbar-show"
        ref="lessonPopover"
        :width="showAddThirdPartyLessonPopover ? '400' : '220'"
        :visible-arrow="false"
        :disabled="!edit"
        @hide="closeLinkPopover"
        v-model="lessonPopoverVisible">
        <!-- 添加三方链接 -->
        <div v-if="showAddThirdPartyLessonPopover" class="lg-padding-left-12 lg-padding-right-12">
          <div class="display-flex align-items">
            <div class="link-form-label">{{ $t('loc.planItemTitle') }}</div>
            <el-input class="active-input-cater" v-model="title" :placeholder="$t('loc.plan155')"
                      clearable></el-input>
          </div>
          <div class="display-flex align-items lg-margin-top-8">
            <div class="link-form-label">{{ $t('loc.planItemLink') }}</div>
            <div class="custom-textarea" @mouseenter="showClear" @mouseleave="hideClear">
              <el-input ref="linkInput" @focus="showClear" @input="showClear" @blur="hideClear"
                        class="active-input-cater" v-model="link" type="textarea" :placeholder="$t('loc.plan156')"
                        rows="2"></el-input>
              <i v-show="showClearLink" @mousedown.prevent @click="clearLink"
                 class="textarea-clear-btn lg-pointer el-icon-error"></i>
            </div>
          </div>
          <div class="pull-right lg-margin-top-8">
            <el-button v-if="link" size="medium" plain @click="unLink"
                       icon="lg-icon lg-icon-cancel-link lg-margin-right-8">{{ $t('loc.unlink') }}
            </el-button>
            <el-button v-else size="medium" plain @click="closeLinkPopover">{{ $t('loc.cancel') }}</el-button>
            <el-button size="medium" type="primary" @click="saveLink">{{ $t('loc.save') }}</el-button>
          </div>
        </div>
        <!-- 编辑三方链接 -->
        <div v-else-if="item.link" class="lg-padding-left-12 lg-padding-right-12">
          <div class="display-flex align-items">
            <i class="lg-icon lg-icon-earth lg-margin-right-8"></i>
            <div class="text-ellipsis" style="width:200px;">
              <el-link @click="openUrl(item.link)" type="primary">
                {{ item.link }}
              </el-link>
            </div>
            <i @click="editLink" class="lg-icon lg-icon-edit lg-margin-left-8 lg-pointer"></i>
            <i @click="unLink" class="lg-icon lg-icon-cancel-link lg-margin-left-8 lg-pointer"></i>
          </div>
        </div>
        <!-- 编辑课程 -->
        <div v-else-if="item.lessonId" class="lg-padding-left-12 lg-padding-right-12">
          <div class="display-flex align-items">
            <div class="display-flex align-items" style="width:200px;">
              <!-- 课程封面 -->
              <media-viewer style="height: 36px; cursor: pointer;" :url="item.mediaUrl" :hideVideo=true
                            @click.native="previewLesson(true)"></media-viewer>
              <!-- 课程名称 -->
              <div class="lg-margin-left-8">
                <span class="overflow-ellipsis-two lg-color-primary lg-pointer lesson-link word-break text-left"
                      :title="item.name" @click="previewLesson(true)">
                  {{ item.name }}
                </span>
              </div>
            </div>
            <!-- 移除课程按钮 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.remove')" placement="top">
              <i @click="removeLesson" class="lg-icon lg-icon-remove lg-margin-left-8 lg-pointer"></i>
            </el-tooltip>
            <!-- 编辑课程按钮 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.edit')" placement="top">
              <i @click="editLesson(true)" class="lg-icon lg-icon-edit lg-margin-left-8 lg-pointer"></i>
            </el-tooltip>
          </div>
        </div>
        <!-- 搜索、添加课程、添加三方链接入口 -->
        <div v-else class="lesson-area">
          <!-- 分割线及添加课程入口，仅在普通周计划内显示 -->
          <template v-if="showQuickAddLesson">
            <!-- 添加课程入口 -->
            <div class="lesson-area-item display-flex align-items" @click="openAddLesson">
              <i v-if="weeklyCreateAILessonOpen && !isFromIpad" class="lg-icon lg-icon-magic font-size-20 add-margin-r-5"
                 style="color: #8B63FF"/>
              <span :class="{'lesson-area-item-ai': weeklyCreateAILessonOpen && !isFromIpad}"> {{ $t('loc.plan159') }}</span>
            </div>
            <el-divider></el-divider>
          </template>
          <!-- 添加课程 -->
          <div class="lesson-area-item" @click="openSelectLesson">
            {{ $t('loc.plan157') }}
          </div>
          <!-- 添加三方链接 -->
          <div class="lesson-area-item" @click="addThirdPartyLesson">
            {{ $t('loc.plan158') }}
          </div>
        </div>
        <!-- 内容区域 -->
        <div slot="reference" class="item-row"
             :class="{'row-border': showEditView && !unitOverviewPreviewDrag, 'active-border': lessonPopoverVisible, 'lg-pointer': !draging && !edit && item.lessonId && item.lessonId.trim().length > 0}"
             :style="{'display': (hoverItem || lessonPopoverVisible || measurePopoverVisible || childPopoverVisible || !hasContent) && !unitOverviewPreviewDrag   ? 'flex' : 'block'}"
             style="bordr: 1px dashed #10b3b7"
             @click="previewLesson()">
          <!-- 图标 -->
          <div class="item-icon" v-show="(showEditView || !item.name) && edit">
            <i class="lg-icon lg-icon-book-open"></i>
          </div>
          <el-popover
            v-if="!isFromUnitDetail && !edit"
            v-model="showAdaptGuidePopover"
            width="450"
            placement="right"
            ref="settingGuide"
            popper-class="adapt-UDL-and-CLR-guide-color-text"
            trigger="manual">
            <div class="text-white">
              <!-- 标题 -->
              <div class="title-font-20 lg-margin-bottom-12">
                {{ $t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClass') }}
              </div>
              <!-- 引导文字 -->
              <div class="lg-margin-bottom-24 word-break text-left">
                <!-- 用户引导内容 -->
                <span class="font-size-16 font-weight-400 line-height-24">{{
                    $t('loc.unitPlannerPersonalizePlanTitle')
                  }}</span>
                <img class="w-full" src="~@/assets/img/lesson2/unitPlanner/adapt.png" alt="">
              </div>
              <div class="display-flex flex-justify-end gap-6 align-items">
                <el-button type="primary" @click="hideGuideOperate(item)">{{ $t('loc.gotIt') }}</el-button>
              </div>
            </div>
            <div slot="reference">
              <!-- 课程封面 -->
              <div class="display-flex align-items position-relative lesson-cover-area"
                   :class="type === 'WEEK_COL' ? 'lesson-media-small' : 'lesson-media'"
                   v-show="item.mediaUrl && !showEditView">
                <el-tag v-if="item.isAdaptedLesson" class="adapted-tag" size="mini">{{
                    $t('loc.adaptUnitPlanner25')
                  }}
                </el-tag>
                <media-viewer :url="item.mediaUrl" :hideVideo="true"></media-viewer>
                <el-tooltip class="item" effect="dark" :content="$t('loc.adaptUDLAndCLRButton')" placement="top">
                  <el-button
                    v-show="item.mediaUrl && !isFromUnitDetail && !isLocked && showAdaptBtn && showOperateBtn"
                    @click.stop="adaptUDLAndCLR()" class="ai-btn adapt-btn">
                    <template #icon>
                      <i class="lg-icon lg-icon-generate"></i>
                    </template>
                  </el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="$t('loc.adaptUDLAndCLRButton')" placement="top">
                  <el-button
                    v-show="item.mediaUrl && !isFromUnitDetail && showAdaptGuidePopover && !isLocked && showAdaptBtn && showOperateBtn"
                    @click.stop="adaptUDLAndCLR()" class="ai-btn adapt-btn-guide">
                    <template #icon>
                      <i class="lg-icon lg-icon-generate"></i>
                    </template>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </el-popover>
          <div class="display-flex align-items lesson-media lesson-cover-area"
               v-show="(item.mediaUrl && !showEditView && isFromUnitDetail) || (item.mediaUrl && unitOverviewPreviewDrag)">
            <el-tag v-if="item.isAdaptedLesson" class="adapted-tag" size="mini">{{
                $t('loc.adaptUnitPlanner25')
              }}
            </el-tag>
            <media-viewer :url="item.mediaUrl" :hideVideo="true" :showViewButton="isFromUnitDetail" ></media-viewer>
            <div class="unit-adapt-btn display-flex align-items" v-if="!showUnitAdaptGuideStatus"
                 :style="{'right': (isOpenAdaptUDLAndCLR && false) ? '30px': '10px'}">
              <el-button @click.stop="editUnitLesson()" v-if="false" icon="lg-icon lg-icon-edit" size="small"
                         :class="isOpenAdaptUDLAndCLR ? 'unit-edit': ''"></el-button>
              <el-tooltip class="item" effect="dark" content="Adapt UDL and CLR" placement="top">
                <el-button v-if="isOpenAdaptUDLAndCLR && unitBaseInfo.progress === 100 && !isOnlyView && (!isCG || isCurriculumPlugin)"
                           @click.stop="unitAdaptUDLAndCLR()" icon="lg-icon lg-icon-generate"
                           class="ai-btn unit-adapt" size="small">
                </el-button>
              </el-tooltip>
            </div>
            <el-popover
              v-if="isOpenAdaptUDLAndCLR && showUnitAdaptGuideStatus"
              v-model="showUnitAdaptGuideStatus"
              width="500"
              placement="left"
              ref="settingGuide"
              popper-class="unit-adapted-guide-style"
              trigger="manual">
              <div class="text-white">
                <!-- 标题 -->
                <div class="title-font-20 lg-margin-bottom-12">
                  Adapt UDL and CLR
                </div>
                <!-- 引导文字 -->
                <div class="lg-margin-bottom-24 word-break text-left">
                  <!-- 用户引导内容 -->
                  <span class="font-size-16 font-weight-400 line-height-24">{{
                      $t('loc.unitPlannerPersonalizePlanTitle')
                    }}</span>
                </div>
                <div class="display-flex align-items" style="gap: 10px;justify-content: end">
                  <span class="font-size-16">(2/2)</span>
                  <el-button type="primary" @click.stop="hideUnitAdaptGuide()">{{ $t('loc.gotIt') }}</el-button>
                </div>
              </div>
              <div slot="reference" class="unit-adapt-btn-guide display-flex align-items">
                <el-button @click.stop="editUnitLesson()" v-if="false" icon="lg-icon lg-icon-edit" size="small"
                           :class="isOpenAdaptUDLAndCLR ? 'unit-edit': ''"></el-button>
                <el-tooltip class="item" effect="dark" content="Adapt UDL and CLR" placement="top">
                  <el-button v-if="isOpenAdaptUDLAndCLR" @click.stop="unitAdaptUDLAndCLR()"
                             icon="lg-icon lg-icon-generate" class="ai-btn unit-adapt" style="right: -10px"
                             size="small"></el-button>
                </el-tooltip>
              </div>
            </el-popover>
          </div>
          <!-- 课程内容 -->
          <div class="display-flex align-items">
            <span class="word-break space-pre-line lg-margin-top-8 lg-margin-bottom-8"
                  :style="{'display': (!hoverItem && !!item.lessonId) || !edit  ? 'block' : 'none'}"
                  :class="{'font-bold': !!item.lessonId}" style="width: 100%">
              <div v-if="item.link && item.name" class="display-flex align-items justify-content-between" @click="openUrl(item.link)">
                <a class="lg-link-under-line title-font-14 lg-color-primary">{{ item.name }}</a>
                <i class="lg-icon lg-icon-arrow-right lg-color-primary font-size-20"></i>
              </div>
              <div v-else-if="item.name" class="display-flex align-items justify-content-between" @click="showLessonDialog()">
                <span v-analysisUrl="item.name" class="lg-pointer lg-color-primary"></span>
                <i class="lg-icon lg-icon-arrow-right lg-color-primary font-size-20"></i>
              </div>
            </span>
          </div>

          <div class="w-full">
            <el-popover
              placement="right"
              width="410"
              v-model="showAiAddLessonGuide"
              popper-class="add-activity-guide"
              trigger="manual">
              <div class="lg-padding-12">
                <div class="line-height-26 font-size-20 font-weight-600 lg-color-white word-break">
                  {{ $t('loc.weekPlanAiOnline') }}
                </div>
                <div class="line-height-22 font-size-14 font-weight-600 lg-color-white add-margin-t-12 word-break">
                  {{ $t('loc.weekPlanAiAssistantToGenerate') }}
                </div>
                <!-- 引导图片 -->
                <img class="w-full lg-border-radius-8 add-margin-t-12"
                     src="~@/assets/img/lesson2/plan/add-activity.png" alt="">
                <div class="display-flex align-items add-margin-t-24">
                  <div class="line-height-22 font-size-14 font-weight-600 lg-color-white lg-pointer"
                       style="margin-left: auto"
                       @click="foreverHideAiAddLessonGuide">{{ $t('loc.unitPlannerRedesignGuide2') }}
                  </div>
                  <el-button plain style="color: #878BF9;margin-left: 12px;font-weight: 600"
                             @click="applyAiAddLesson">
                    {{ $t('loc.applyNow') }}
                  </el-button>
                </div>
              </div>

              <div slot="reference">
                <div class="custom-textarea" @mouseenter="showNameClear" @mouseleave="hideNameClear"
                     :style="{'display': (!hoverItem && !!item.lessonId) ? 'none' : 'block'}">
                  <el-input
                    ref="dayInput"
                    maxlength=2000
                    v-if="edit"
                    class="item-content active-input-cater"
                    :class="{'font-bold': !!item.lessonId, 'input-color-primary': !!item.link}"
                    type="textarea"
                    @input="changeItemName($event)"
                    @focus="focusInputName"
                    @blur="blurInput"
                    :placeholder="$t('loc.plan107')"
                    autosize
                    v-model="item.name">
                  </el-input>
                  <i v-show="showClearName" @mousedown.prevent @click="clearName"
                     class="textarea-clear-btn lg-pointer el-icon-error"></i>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
      </el-popover>
      <!-- 课程内容 -->
      <el-popover
        v-if="type === 'DAY_TEXT_COL' || (type === 'WEEK_TEXT_COL' && long)"
        :placement="showAddThirdPartyLessonPopover || item.link || item.lessonId ? 'bottom-start' : 'right-start'"
        trigger="manual"
        popper-class="input-lesson-popover lg-scrollbar-show"
        ref="lessonPopover"
        :width="showAddThirdPartyLessonPopover ? '400' : '220'"
        :visible-arrow="false"
        :disabled="!edit"
        @hide="closeLinkPopover"
        v-model="lessonPopoverVisible">
        <!-- 编辑课程 -->
        <div v-if="item.name" class="lg-padding-left-12 lg-padding-right-12">
          <div class="display-flex align-items">
            <div class="display-flex align-items" style="width:200px;">
              <!-- 课程名称 -->
              <div class="lg-margin-left-8">
                <span class="overflow-ellipsis-two lg-color-primary lg-pointer lesson-link word-break text-left"
                      :title="item.name" @click="previewLesson(true)">
                  {{ item.name }}
                </span>
              </div>
            </div>
            <!-- 移除课程按钮 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.remove')" placement="top">
              <i @click="removeLesson" class="lg-icon lg-icon-remove lg-margin-left-8 lg-pointer"></i>
            </el-tooltip>

          </div>
        </div>
        <!-- 内容区域 -->
        <div slot="reference" class="item-row"
             :class="{'row-border': showEditView, 'active-border': focusInput, 'lg-pointer': !draging && !edit && item.lessonId && item.lessonId.trim().length > 0}"
             :style="{'display': hoverItem || lessonPopoverVisible || measurePopoverVisible || childPopoverVisible || !hasContent   ? 'flex' : 'block'}"
             style="bordr: 1px dashed #10b3b7"
             @click="previewLesson()">

          <!-- 文本内容 -->
          <span class="word-break space-pre-line lg-margin-top-8 "
                :style="{'display': (!hoverItem && !!item.lessonId) || !edit  ? 'block' : 'none'}"
                :class="{'font-bold': !!item.lessonId,'lg-margin-bottom-10':!edit}" style="width: 100%">
            <span v-if="item.link">
              <a @click="openUrl(item.link)" class="lg-link-under-line title-font-14">{{ item.name }}</a>
            </span>
            <span v-else v-analysisUrl="item.name" class="lg-pointer" @click="showLessonDialog()"></span>
          </span>

          <div @mouseenter="showNameClear" @mouseleave="hideNameClear"
               class="custom-textarea"
               style="{display: block}">
            <el-input
              ref="dayInput"
              maxlength=2000
              v-if="edit"
              class="word-break space-pre-line "
              :class="{ 'input-color-primary': !!item.link,'lg-margin-bottom-10':!edit}"
              type="textarea"
              @input="changeItemName($event)"
              @focus="focusInputName"
              @blur="blurInput"
              :placeholder="defaultText"
              autosize
              v-model="item.name">
            </el-input>
            <i v-show="showClearName" @mousedown.prevent @click="clearName"
               class="textarea-clear-btn lg-pointer el-icon-error"></i>
          </div>
        </div>
      </el-popover>
      <!-- 测评点 -->
      <el-popover
        placement="right-start"
        trigger="click"
        :width="long ? '200' : itemWidth"
        :visible-arrow="false"
        :disabled="!edit || !item.name"
        :class="{'option-disabled' : edit && !item.name}"
        v-model="measurePopoverVisible">
        <!-- 搜索测评点 -->
        <el-input
          maxlength=100
          :placeholder="$t('loc.plan60')"
          suffix-icon="el-icon-search"
          v-model="searchMeasureStr">
        </el-input>
        <!-- 框架列表 -->
        <div class="w-full m-t-sm" v-if="frameworkData && frameworkData.length > 1">
          <el-select class="w-full" v-model="item.frameworkId" placeholder="Please selectFramework"
                     @change="changeFramework">
            <el-option
              v-for="framework in frameworkData"
              :key="framework.id"
              :label="framework.name"
              :value="framework.id">
            </el-option>
          </el-select>
        </div>
        <!-- 测评点列表 -->
        <div class="measure-area m-t-sm m-b-xs b-a lg-scrollbar-show" v-if="measurePopoverVisible">
          <div v-for="domain in filterDomains" :key="domain.id">
            <div class="font-bold">
              {{ domain.abbreviation }}
            </div>
            <div v-for="measure in domain.measures" :key="measure.id">
              <el-checkbox
                v-model="measure.selected"
                @change="changeMeasure"
                :label="measure.abbreviation">
                {{ measure.abbreviation }} <span v-if="measure.core" style="color: red;">*</span>
              </el-checkbox>
            </div>
          </div>
          <div v-if="filterDomains.length == 0">{{ $t('loc.nodataTip') }}</div>
        </div>
        <!-- 确认按钮 -->
        <div style="text-align: right; margin: 0">
          <el-button type="primary" size="small" @click="saveMeasures">{{ $t('loc.save') }}
          </el-button>
        </div>
        <div slot="reference" v-show="showEditView || hasContent" class="item-row measure-row"
             :class="{'row-border': showEditView && !unitOverviewPreviewDrag, 'active-border': measurePopoverVisible, 'lg-pointer': edit, 'option-disabled': edit && !item.name}">
          <!-- 图标 -->
          <div class="item-icon" v-show="showEditView && !unitOverviewPreviewDrag">
            <i class="lg-icon lg-icon-measure"></i>
          </div>
          <!-- 测评点内容 -->
          <div style="text-align: start;">
            <div v-if="(!checkedMeasures || checkedMeasures.length === 0) && edit && showEditView"
                 class="lg-color-text-placeholder">{{ $t('loc.plan33') }}
            </div>
            <span v-for="(measure, index) in checkedMeasures" :key="index">
              <el-tag v-if="measure.core || !onlyShowCoreMeasure || edit" type="info" size="small"
                      class="m-r-xs pos-rlt m-b-xs" disable-transitions>
                <div class="truncate core-measure">
                  <span :title="measure.abbreviation" class="spanTruncate">
                    {{ measure.abbreviation }}
                  </span>
                  <span v-if="measure.core" style="color: red;">*</span>
                </div>
                <i v-if="edit && showEditView" @click.stop="removeMeasure(measure)"
                   class="el-icon-error close-btn lg-pointer"></i>
              </el-tag>
            </span>
          </div>
        </div>
      </el-popover>
      <!-- 小孩 -->
      <el-popover
        v-if="!adminEdit"
        placement="right-start"
        trigger="click"
        :width="long ? '200' : itemWidth"
        :visible-arrow="false"
        :disabled="!edit || !item.name"
        :class="{'option-disabled': edit && !item.name}"
        v-model="childPopoverVisible">
        <!-- 选择小孩 -->
        <div>
          <div class="font-bold">{{ $t('loc.plan34') }}</div>
        </div>
        <!-- 小孩列表 -->
        <div class="child-area m-t-xs m-b-xs pos-rlt lg-scrollbar-show"
             :class="{'b-a': allChildren && allChildren.length > 0}">
          <template v-if="allChildren && allChildren.length > 0">
            <!-- 选择所有小孩 -->
            <div class="display-flex align-items search-child-area b-b lg-pointer"
                 v-if="allChildren && allChildren.length > 0">
              <el-checkbox
                style="width: 100%"
                :indeterminate="isIndeterminate"
                @change="selectAllChild"
                v-model="selectAllChildren">
                <div class="m-l-xs">
                  {{ $t('loc.selectAll') }}
                </div>
              </el-checkbox>
            </div>
            <div class="child-list">
              <div v-for="(child, index) in allChildren" :key="child.id" class="m-b-xs child-check-row lg-pointer"
                   :class="{'b-t': index != 0}">
                <div @click.stop>
                  <el-checkbox
                    style="width: 100%; display: flex; align-items: center;"
                    @change="changeChild(child)"
                    v-model="child.selected">
                    <div class="display-flex align-items">
                      <div class="m-r-sm flex-none">
                        <el-image :src="child.avatarUrl" class="child-avatar">
                          <div slot="error" class="image-slot">
                            <img src="https://d2urtjxi3o4r5s.cloudfront.net/images/child_avatar_2023.png"
                                 class="child-avatar"/>
                          </div>
                        </el-image>
                      </div>
                      <div class="overflow-ellipsis flex-auto" :title="child.displayName">
                        {{ child.displayName }}
                      </div>
                    </div>
                  </el-checkbox>
                </div>
              </div>
            </div>
          </template>
          <!-- 没有小孩 -->
          <div class="display-flex justify-content align-items flex-direction-col add-padding-t-30 add-padding-b-30"
               v-if="!allChildren || allChildren.length === 0">
            <img class="add-padding-b-20" src="@/assets/img/lesson2/plan/child_header.png"/>
            <span class="add-padding-b-20">{{ $t('loc.plan35') }}</span>
            <el-button size="small" @click="childPopoverVisible = false">{{ $t('loc.close') }}</el-button>
          </div>
        </div>
        <!-- 确认按钮 -->
        <div style="text-align: right; margin: 0" v-if="allChildren && allChildren.length > 0">
          <el-button type="primary" size="small" @click="childPopoverVisible = false">{{ $t('loc.save') }}</el-button>
        </div>
        <div slot="reference" v-show="showEditView || hasContent" class="item-row measure-row"
             :class="{'row-border': showEditView, 'active-border': childPopoverVisible, 'lg-pointer': edit, 'option-disabled': edit && !item.name}">
          <!-- 图标 -->
          <div class="item-icon" v-show="showEditView">
            <i class="lg-icon lg-icon-baby"></i>
          </div>
          <!-- 小孩名称 -->
          <div>
            <div v-if="(!checkedChildren || checkedChildren.length === 0) && edit && showEditView"
                 class="lg-color-text-placeholder">{{ $t('loc.plan34') }}
            </div>
            <span v-for="(child, index) in checkedChildren" :key="index">
              {{ child.firstAbbr }}<span v-if="index !== checkedChildren.length - 1">,</span>
            </span>
          </div>
        </div>
      </el-popover>
      <!-- 反思按钮 -->
      <div v-if="!edit && item.canAddReflection && !isAdmin && canAddReflection" class="reflection-item">
        <!-- 添加 -->
        <el-button
          style="padding-left: 5px;"
          v-show="item.reflectionCount === 0 && showAddReflectionButton"
          plain size="mini" icon="el-icon-plus"
          @click="createReflection">{{ $t('loc.plan70') }}
        </el-button>
        <!-- 查看 -->
        <el-button
          style="margin-left: 0px;"
          v-if="item.reflectionCount > 0"
          type="text"
          @click="viewReflection">
          <i class="el-icon-chat-line-round m-r-xs"></i>{{ $t('loc.plan71') }}
        </el-button>
      </div>
    </div>

    <!-- 一行内容 -->
    <template v-if="type === 'WEEK_COL' && !long">
      <!-- 编辑状态输入框 -->
      <div class="custom-textarea" @mouseenter="showNameClear" @mouseleave="hideNameClear"
          :style="{'display': (!hoverItem && !!item.lessonId) ? 'none' : 'block'}"
        >
        <el-input
          ref="dayInput"
          maxlength=2000
          v-if="edit"
          class="item-content active-input-cater"
          :class="{'font-bold': !!item.lessonId, 'input-color-primary': !!item.link}"
          type="textarea"
          @input="changeItemName($event)"
          @focus="focusInputName"
          @blur="blurInput"
          :placeholder="categoryName"
          autosize
          v-model="item.name">
        </el-input>
        <i v-show="showClearName" @mousedown.prevent @click="clearName" class="textarea-clear-btn lg-pointer el-icon-error"></i>
      </div>
      <!-- 非编辑状态显示内容 -->
      <div class="pre-area text-default" v-if="!edit" v-analysisUrl="item.name">
      </div>
    </template>
    <!-- Centers -->
    <div class="item-area" :style="{'cursor': draging ? 'move' : ''}" v-if="type === 'CENTER_ROW'">
      <!-- 课程封面 -->
      <div
        class="display-flex align-items add-margin-b-3 justify-content position-relative lessons-cover"
        :class="type === 'WEEK_COL' ? 'lesson-media-small' : 'lesson-media'"
        v-show="item.mediaUrl && edit">
        <el-tag v-if="item.isAdaptedLesson" class="adapted-tag" size="mini">{{ $t('loc.adaptUnitPlanner25') }}</el-tag>
        <media-viewer :url="item.mediaUrl" :hideVideo=true></media-viewer>
        <div class="preview-lesson btn-center flex-direction-col">
          <div class="w-full display-flex justify-content align-items">
            <el-button size="mini" class="preview-icon" :style="{ top: showAdaptBtn ? '' : '40%' }" icon="el-icon-view" @click="previewLesson(true, true)">{{ $t('loc.preview') }}</el-button>
          </div>
          <div class="w-full display-flex justify-content align-items">
            <el-button size="mini" class="adapt-icon" v-if="showAdaptBtn" @click="adaptUDLAndCLR(false)">
              <template #icon>
                <i class="lg-icon lg-icon-generate"></i>
              </template>
              {{ $t('loc.lessonAdapt') }}
            </el-button>
          </div>
        </div>
      </div>
      <!-- 课程内容 -->
      <el-popover
        :placement="showAddThirdPartyLessonPopover || item.link || item.lessonId ? 'bottom-start' : 'right-start'"
        trigger="manual"
        popper-class="input-lesson-popover lg-scrollbar-show"
        ref="lessonPopover"
        :width="showAddThirdPartyLessonPopover ? '400' : '220'"
        :visible-arrow="false"
        :disabled="!edit"
        @hide="closeLinkPopover"
        v-model="lessonPopoverVisible">
        <!-- 添加三方链接 -->
        <div v-if="showAddThirdPartyLessonPopover" class="lg-padding-left-12 lg-padding-right-12">
          <div class="display-flex align-items">
            <div class="link-form-label">{{ $t('loc.planItemTitle') }}</div>
            <el-input class="active-input-cater" v-model="title" :placeholder="$t('loc.plan155')" clearable></el-input>
          </div>
          <div class="display-flex align-items lg-margin-top-8">
            <div class="link-form-label">{{ $t('loc.planItemLink') }}</div>
            <div class="custom-textarea" @mouseenter="showClear" @mouseleave="hideClear">
              <el-input ref="linkInput" @focus="showClear" @input="showClear" @blur="hideClear" class="active-input-cater" v-model="link" type="textarea" :placeholder="$t('loc.plan156')" rows="2"></el-input>
              <i v-show="showClearLink" @mousedown.prevent @click="clearLink" class="textarea-clear-btn lg-pointer el-icon-error"></i>
            </div>
          </div>
          <div class="pull-right lg-margin-top-8">
            <el-button v-if="link" size="medium" plain @click="unLink" icon="lg-icon lg-icon-cancel-link lg-margin-right-8">{{ $t('loc.unlink') }}</el-button>
            <el-button v-else size="medium" plain @click="closeLinkPopover">{{ $t('loc.cancel') }}</el-button>
            <el-button size="medium" type="primary" @click="saveLink">{{ $t('loc.save') }}</el-button>
          </div>
        </div>
        <!-- 编辑三方链接 -->
        <div v-else-if="item.link">
          <div class="display-flex align-items">
            <i class="lg-icon lg-icon-earth lg-margin-right-8"></i>
            <div class="text-ellipsis" style="width:200px;">
              <el-link @click="openUrl(item.link)" type="primary">
                {{ item.link }}
              </el-link>
            </div>
            <i @click="editLink" class="lg-icon lg-icon-edit lg-margin-left-8 lg-pointer"></i>
            <i @click="unLink" class="lg-icon lg-icon-cancel-link lg-margin-left-8 lg-pointer"></i>
          </div>
        </div>
        <!-- 编辑课程 -->
        <div v-else-if="item.lessonId" class="lg-padding-left-12 lg-padding-right-12">
          <div class="display-flex align-items">
            <div class="display-flex align-items" style="width:200px;">
              <!-- 课程封面 -->
              <media-viewer style="height: 36px; cursor: pointer;" :url="item.mediaUrl" :hideVideo=true @click.native="previewLesson(true)"></media-viewer>
              <!-- 课程名称 -->
              <div class="lg-margin-left-8">
                <span class="overflow-ellipsis-two lg-color-primary lg-pointer lesson-link word-break text-left" :title="item.name" @click="previewLesson(true)">
                  {{ item.name }}
                </span>
              </div>
            </div>
            <!-- 移除课程按钮 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.remove')" placement="top">
              <i @click="removeLesson" class="lg-icon lg-icon-remove lg-margin-left-8 lg-pointer"></i>
            </el-tooltip>
            <!-- 编辑课程按钮 -->
            <el-tooltip class="item" effect="dark" :content="$t('loc.edit')" placement="top">
              <i @click="editLesson(true)" class="lg-icon lg-icon-edit lg-margin-left-8 lg-pointer"></i>
            </el-tooltip>
          </div>
        </div>
        <!-- 搜索、添加课程、添加三方链接入口 -->
        <div v-else class="lesson-area">
          <!-- 分割线及添加课程入口，仅在普通周计划内显示 -->
          <template v-if="showQuickAddLesson">
            <!-- 添加课程入口 -->
            <div class="lesson-area-item display-flex align-items" @click="openAddLesson">
              <i v-if="weeklyCreateAILessonOpen && !isFromIpad" class="lg-icon lg-icon-magic font-size-20 add-margin-r-5" style="color: #8B63FF"/>
              <span :class="{'lesson-area-item-ai': weeklyCreateAILessonOpen && !isFromIpad}"> {{ $t('loc.plan159') }}</span>
            </div>
            <el-divider></el-divider>
          </template>
          <!-- 添加课程 -->
          <div class="lesson-area-item" @click="openSelectLesson">
            {{ $t('loc.plan157') }}
          </div>
          <!-- 添加三方链接 -->
          <div class="lesson-area-item" @click="addThirdPartyLesson">
            {{ $t('loc.plan158') }}
          </div>
        </div>
        <!-- 内容区域 -->
        <div slot="reference" class="item-row" :class="{'row-border': showEditView, 'active-border': lessonPopoverVisible, 'lg-pointer': !draging && !edit && item.lessonId && item.lessonId.trim().length > 0}"
             :style="{'display': hoverItem || lessonPopoverVisible || measurePopoverVisible || childPopoverVisible || !hasContent   ? 'flex' : 'block'}"
             @click="previewLesson()">
          <!-- 图标 -->
          <div class="item-icon" v-show="(showEditView || !item.name) && edit">
            <i class="lg-icon lg-icon-book-open"></i>
          </div>
          <el-popover
            v-if="!isFromUnitDetail && !edit"
            v-model="showAdaptGuidePopover"
            width="450"
            placement="right"
            ref="settingGuide"
            popper-class="adapt-UDL-and-CLR-guide-color-text"
            trigger="manual">
            <div class="text-white">
              <!-- 标题 -->
              <div class="title-font-20 lg-margin-bottom-12">
                {{ $t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClass') }}
              </div>
              <!-- 引导文字 -->
              <div class="lg-margin-bottom-24 word-break text-left">
                <!-- 用户引导内容 -->
                <span class="font-size-16 font-weight-400 line-height-24">{{ $t('loc.unitPlannerPersonalizePlanTitle') }}</span>
                <img class="w-full" src="~@/assets/img/lesson2/unitPlanner/adapt.png" alt="">
              </div>
              <div class="display-flex flex-justify-end gap-6 align-items">
                <el-button type="primary" @click="hideGuideOperate(item)">{{ $t('loc.gotIt')}}</el-button>
              </div>
            </div>
            <div slot="reference">
              <!-- 课程封面 -->
              <div class="display-flex align-items position-relative lesson-cover-area" :class="type === 'WEEK_COL' ? 'lesson-media-small' : 'lesson-media'"
                   v-show="item.mediaUrl && !showEditView">
                <el-tag v-if="item.isAdaptedLesson" class="adapted-tag" size="mini">{{ $t('loc.adaptUnitPlanner25') }}</el-tag>
                <media-viewer :url="item.mediaUrl" :hideVideo="true"></media-viewer>
                <el-tooltip class="item" effect="dark" :content="$t('loc.adaptUDLAndCLRButton')" placement="top">
                  <el-button v-show="item.mediaUrl && !isFromUnitDetail && !isLocked && showAdaptBtn && showOperateBtn" @click.stop="adaptUDLAndCLR()" class="ai-btn adapt-btn">
                    <template #icon>
                      <i class="lg-icon lg-icon-generate"></i>
                    </template>
                  </el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="$t('loc.adaptUDLAndCLRButton')" placement="top">
                  <el-button v-show="item.mediaUrl && !isFromUnitDetail && showAdaptGuidePopover && !isLocked && showOperateBtn" @click.stop="adaptUDLAndCLR()" class="ai-btn adapt-btn-guide">
                    <template #icon>
                      <i class="lg-icon lg-icon-generate"></i>
                    </template>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </el-popover>
          <!-- 课程封面 -->
          <div class="display-flex align-items lesson-media lesson-cover-area position-relative"
               v-show="item.mediaUrl && !showEditView && isFromUnitDetail">
            <media-viewer :url="item.mediaUrl" :hideVideo=true :showViewButton="isFromUnitDetail"></media-viewer>
            <div class="unit-adapt-btn display-flex align-items position-absolute" style="right: 10px">
              <el-button @click.stop="editUnitLesson()" v-if="false" icon="lg-icon lg-icon-edit" size="small" class="unit-edit">
              </el-button>
              <el-button v-if="false" @click.stop="unitAdaptUDLAndCLR()" icon="lg-icon lg-icon-generate" class="ai-btn unit-adapt" size="small">
              </el-button>
            </div>
          </div>
          <!-- 课程内容 -->
          <span class="word-break space-pre-line lg-margin-top-12 lg-margin-bottom-12" :style="{'display': (!hoverItem && !!item.lessonId) || !edit  ? 'block' : 'none'}" :class="{'font-bold': !!item.lessonId}">
            <div v-if="item.link && item.name" class="display-flex align-items justify-content-between" @click="openUrl(item.link)">
                <a class="lg-link-under-line title-font-14 lg-color-primary">{{ item.name }}</a>
                <i class="lg-icon lg-icon-arrow-right lg-color-primary font-size-20"></i>
            </div>
            <div v-else-if="item.name" class="display-flex align-items justify-content-between" @click="showLessonDialog()">
                <span v-analysisUrl="item.name" class="lg-pointer lg-color-primary"></span>
                <i class="lg-icon lg-icon-arrow-right lg-color-primary font-size-20"></i>
            </div>
          </span>
          <div class="custom-textarea" @mouseenter="showNameClear" @mouseleave="hideNameClear"
              :style="{'display': (!hoverItem && !!item.lessonId) ? 'none' : 'block'}"
            >
            <el-input
              ref="dayInput"
              maxlength=2000
              v-if="edit"
              class="item-content active-input-cater"
              :class="{'font-bold': !!item.lessonId, 'input-color-primary': !!item.link}"
              type="textarea"
              @input="changeItemName($event)"
              @focus="focusInputName"
              @blur="blurInput"
              :placeholder="$t('loc.plan107')"
              autosize
              v-model="item.name">
            </el-input>
            <i v-show="showClearName" @mousedown.prevent @click="clearName" class="textarea-clear-btn lg-pointer el-icon-error"></i>
          </div>
        </div>
      </el-popover>
      <!-- 测评点 -->
      <el-popover
        placement="right-start"
        trigger="click"
        :width="long ? '200' : itemWidth"
        :visible-arrow="false"
        :disabled="!edit || !item.name"
        :class="{'option-disabled': edit && !item.name}"
        v-model="measurePopoverVisible">
        <!-- 搜索测评点 -->
        <el-input
          maxlength=100
          :placeholder="$t('loc.plan60')"
          suffix-icon="el-icon-search"
          v-model="searchMeasureStr">
        </el-input>
        <!-- 框架列表 -->
        <div class="w-full m-t-sm" v-if="frameworkData && frameworkData.length > 1">
          <el-select class="w-full" v-model="item.frameworkId" placeholder="Please selectFramework" @change="changeFramework">
            <el-option
              v-for="framework in frameworkData"
              :key="framework.id"
              :label="framework.name"
              :value="framework.id">
            </el-option>
          </el-select>
        </div>
        <!-- 测评点列表 -->
        <div class="measure-area m-t-sm m-b-xs b-a lg-scrollbar-show" v-if="lessonPopoverVisible">
          <div v-for="domain in filterDomains" :key="domain.id">
            <div class="font-bold">
              {{domain.abbreviation}}
            </div>
            <div v-for="measure in domain.measures" :key="measure.id">
              <el-checkbox
                v-model="measure.selected"
                @change="changeMeasure"
                :label="measure.abbreviation">
                {{measure.abbreviation}} <span v-if="measure.core" style="color: red;">*</span>
              </el-checkbox>
            </div>
          </div>
          <div v-if="filterDomains.length == 0">{{ $t('loc.nodataTip') }}</div>
        </div>
        <!-- 确认按钮 -->
        <div style="text-align: right; margin: 0">
          <el-button type="primary" size="small" @click="saveMeasures">{{$t('loc.save')}}</el-button>
        </div>
        <div slot="reference" v-show="showEditView || hasContent" class="item-row measure-row" :class="{'row-border': showEditView, 'active-border': measurePopoverVisible, 'active-border': measurePopoverVisible, 'lg-pointer': edit}">
          <!-- 图标 -->
          <div class="item-icon" v-show="showEditView">
            <i class="lg-icon lg-icon-measure"></i>
          </div>
          <!-- 测评点内容 -->
          <div style="text-align: start; max-width: 100%">
            <div v-if="(!checkedMeasures || checkedMeasures.length === 0) && edit && showEditView" class="lg-color-text-placeholder">{{$t('loc.plan33')}}</div>
            <span v-for="(measure, index) in checkedMeasures" :key="index">
              <el-tag v-if="measure.core || !onlyShowCoreMeasure || edit" type="info" size="small" class="m-r-xs pos-rlt m-b-xs" style="max-width: 95%" disable-transitions>
               <div class="center-truncate core-measure">
                  <span :title="measure.abbreviation" class="spanTruncate">
                    {{ measure.abbreviation }}
                  </span>
                  <span v-if="measure.core" style="color: red;">*</span>
                </div>
                <i v-if="edit && showEditView" @click.stop="removeMeasure(measure)" class="el-icon-error close-btn lg-pointer"></i>
              </el-tag>
            </span>
          </div>
        </div>
      </el-popover>
      <!-- 小孩 -->
      <el-popover
        v-if="canAddChildren"
        placement="right-start"
        trigger="click"
        :width="long ? '200' : itemWidth"
        :visible-arrow="false"
        :disabled="!edit || !item.name"
        :class="{'option-disabled': edit && !item.name}"
        v-model="childPopoverVisible">
        <!-- 选择小孩 -->
        <div>
          <div class="font-bold">{{$t('loc.plan34')}}</div>
        </div>
        <!-- 小孩列表 -->
        <div class="child-area m-t-xs m-b-xs pos-rlt lg-scrollbar-show" :class="{'b-a': allChildren && allChildren.length > 0}">
          <template v-if="allChildren && allChildren.length > 0">
            <!-- 选择所有小孩 -->
            <div class="display-flex align-items search-child-area b-b lg-pointer" v-if="allChildren && allChildren.length > 0">
              <el-checkbox
                style="width: 100%"
                :indeterminate="isIndeterminate"
                @change="selectAllChild"
                v-model="selectAllChildren">
                <div class="m-l-xs">
                  {{$t('loc.selectAll')}}
                </div>
              </el-checkbox>
            </div>
            <div class="child-list">
              <div v-for="(child, index) in allChildren" :key="child.id" class="m-b-xs child-check-row lg-pointer" :class="{'b-t': index != 0}">
                <div @click.stop>
                  <el-checkbox
                    style="width: 100%; display: flex; align-items: center;"
                    @change="changeChild(child)"
                    v-model="child.selected">
                    <div class="display-flex align-items">
                      <div class="m-r-sm flex-none">
                        <el-image :src="child.avatarUrl" class="child-avatar">
                          <div slot="error" class="image-slot">
                            <img src="https://d2urtjxi3o4r5s.cloudfront.net/images/child_avatar_2023.png" class="child-avatar"/>
                          </div>
                        </el-image>
                      </div>
                      <div class="overflow-ellipsis flex-auto" :title="child.displayName">
                        {{child.displayName}}
                      </div>
                    </div>
                  </el-checkbox>
                </div>
              </div>
            </div>
          </template>
          <!-- 没有小孩 -->
          <div class="display-flex justify-content align-items flex-direction-col add-padding-t-30 add-padding-b-30" v-if="!allChildren || allChildren.length === 0">
            <img class="add-padding-b-20" src="@/assets/img/lesson2/plan/child_header.png"/>
            <span class="add-padding-b-20">{{$t('loc.plan35')}}</span>
            <el-button size="small" @click="childPopoverVisible = false">{{$t('loc.close')}}</el-button>
          </div>
        </div>
        <!-- 确认按钮 -->
        <div style="text-align: right; margin: 0" v-if="allChildren && allChildren.length > 0">
          <el-button type="primary" size="small" @click="childPopoverVisible = false">{{$t('loc.save')}}</el-button>
        </div>
        <div slot="reference" v-show="showEditView || hasContent" class="item-row measure-row" :class="{'row-border': showEditView, 'active-border': childPopoverVisible, 'lg-pointer': edit}">
          <!-- 图标 -->
          <div class="item-icon" v-show="showEditView">
            <i class="lg-icon lg-icon-baby"></i>
          </div>
          <!-- 小孩名称 -->
          <div>
            <div v-if="(!checkedChildren || checkedChildren.length === 0) && edit && showEditView" class="lg-color-text-placeholder">{{$t('loc.plan34')}}</div>
            <span v-for="(child, index) in checkedChildren" :key="index">
              {{child.firstAbbr}}<span v-if="index != checkedChildren.length - 1">,</span>
            </span>
          </div>
        </div>
      </el-popover>
      <!-- 反思按钮 -->
      <div v-if="!edit && item.canAddReflection && !isAdmin && canAddReflection" class="reflection-item">
        <!-- 添加 -->
        <el-button
          style="padding-left: 5px;"
          v-show="item.reflectionCount === 0 && showAddReflectionButton"
          plain size="mini" icon="el-icon-plus"
          @click="createReflection">{{$t('loc.plan70')}}
        </el-button>
        <!-- 查看 -->
        <el-button
          style="margin-left: 0px;"
          v-if="item.reflectionCount > 0"
          type="text"
          @click="viewReflection">
          <i class="el-icon-chat-line-round m-r-xs"></i>{{$t('loc.plan71')}}
        </el-button>
      </div>
    </div>
    <!--模态窗口展示详情页-->
    <lesson-detail-dialog v-if="showDetail" :lesson-id="previewLessonId" :show.sync="showDetail" :new-tab-to="newTabTo">
      <!--详情页-->
      <lesson-detail ref="lessonDetail" @mountedAfter="lessonDetailMountedAfter" :is-from-library="true" :lessonId="previewLessonId" :isDialog="true" :itemId="item.id">
        <template slot="header-left" slot-scope="{lesson}">
          <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/>
          <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
          <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
        </template>
        <!-- 课程详情页右侧菜单 -->
        <!-- 在编辑或者审批时,可以编辑或移除周计划中的课程 -->
        <template slot="header-right" slot-scope="{ lesson, mappedFrameworkId }">
          <div class="display-flex align-items" style="gap: 10px">
            <!-- adapter 按钮及其引导-->
            <el-popover
              v-if="canAdapter && adaptUDLAndCLROpen && showAdapterButton"
              placement="bottom"
              width="380"
              v-model="showAdaptUDLAndCLRGuide"
              ref="settingGuide"
              popper-class="adapt-UDL-and-CLR-guide-color-text"
              trigger="manual">
              <div class="text-white">
                <!-- 标题 -->
                <div class="title-font-20 lg-margin-bottom-12">
                  {{ $t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClass') }}
                </div>
                <!-- 引导文字 -->
                <div class="lg-margin-bottom-24 word-break text-left">
                  <!-- 用户引导内容 -->
                  <span class="font-size-16 font-weight-400 line-height-24">{{
                      $t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClassGuideContent')
                    }}</span>
                </div>
                <div class="display-flex flex-justify-end gap-6 align-items">
                  <!-- 操作区-->
                  <div class="font-size-16 lg-margin-right-8 line-height-24 font-weight-600 lg-pointer"
                       @click="endAdaptUDLAndCLRGuide">{{ $t('loc.later') }}
                  </div>
                  <el-button type="primary" @click="adaptUDLAndCLR()">{{ $t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClassGuideAdapt') }}</el-button>
                </div>
              </div>
              <div slot="reference">
                <el-tooltip :content="$t('loc.adaptUDLAndCLRDesc')">
                <el-button class="ai-btn" size="small" :disabled="!!isLocked" v-if="showOperateBtn && !isAgencyTemplate || (isFromUnitDetail && !planCenter && unitBaseInfo.progress === 100)" @click="adaptUDLAndCLR()">
                  <template #icon>
                    <i class="lg-icon lg-icon-generate"></i>
                  </template>
                  <!-- <span v-if="edit">{{ $t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClass') }}</span> -->
                  <span>{{ $t('loc.adaptUDLAndCLRButton') }}</span>
                </el-button>
                </el-tooltip>
              </div>
            </el-popover>
            <!-- 课程增强对话框 -->
          <LessonEnhanceDialog
            :dialogVisible.sync="regenerateLessonVisible"
            :lessonMeasures="lesson.measures"
            :lessonActivityTime="lesson.activityTime"
            :domains="domains"
            :frameworkId="lesson.framework.id"
            @confirmRedesignLesson="confirmRedesignLesson"/>
            <!-- 更新和重新生成按钮 -->
            <el-button slot="reference" v-if="(!unitBaseInfo || !unitBaseInfo.exemplar)"
                        class="ai-btn height-32"
                        @click="openRedesignLessonDialog">
                <template #icon>
                  <i class="lg-icon lg-icon-generate"></i>
                </template>
                {{ $t('loc.enhanceLesson') }}
              </el-button>
            <lesson-template-select-modal
              v-if="showLessonTemplate(lesson.ages, lesson.activityType, lesson.createUserId)"
              :lessonAges="lesson.ages"
              buttonSize="small"
              :inDialog="true"
              :showGuidePopover="true"
              :lessonId="lesson.id"
              v-model="lesson.templateType"
              @change="changeTemplateType"
              />
          </div>
          <lesson-download v-if="showOperateBtn" :lesson-id="item.lessonId" :lesson-name="item.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id" :viewLesson="true" :lesson="lesson"/>
          <!--周计划编辑按钮-->
          <el-button icon="el-icon-edit" type="primary" plain size="small" @click="editLesson"
                     :disabled="!!isLocked"
                     v-if="!isFromUnitDetail && !isVirtualShadowing && !isPublicTemplate && notCurriculumEditPreview">
            {{ $t('loc.edit') }}
          </el-button>
          <el-dropdown trigger="click" v-if="(!isFromUnitDetail && !isVirtualShadowing && !isPublicTemplate && notCurriculumEditPreview && edit) || edit">
            <el-button class="el-dropdown-link" style="width: 44px; height: 32px;padding: 0px"><i
              class="lg-icon lg-icon-more-horizontal font-size-20"></i></el-button>
            <el-dropdown-menu slot="dropdown" class="dropdown-menu-more">
              <!--周计划复制按钮-->
              <el-dropdown-item v-if="edit" @click.native="copyLesson(true)" :disabled="showCopyLessonLoading">
                <i class="lg-icon lg-icon-copy"></i>
                <span>{{ $t('loc.plan66') }}</span>
              </el-dropdown-item>

              <!-- 移除周计划中的课程按钮 -->
              <el-dropdown-item v-if="edit" @click.native="removeLesson(true)">
                <i class="lg-icon lg-icon-remove" style="color:#F56C6C"></i>
                <span style="color:#F56C6C">{{ $t('loc.plan177') }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </lesson-detail>
    </lesson-detail-dialog>
    <!-- 编辑课程弹窗 -->
    <edit-lesson-dialog @loadingLessonAfter="editorPageRenderAfter" ref="editLesson" :key="refreshKey" :lessonId="item.lessonId" @saveLesson="selectLesson" @refreshComponent="refreshComponent"></edit-lesson-dialog>
    <el-dialog v-if="showLessonDialogVisible"
      :append-to-body="true"
      :title="item.name"
      :visible.sync="showLessonDialogVisible"
      width="45%"
      custom-class="unCompleted-lesson"
      :before-close="handleShowLessonDialogClose">
      <div>
        <div class="display-flex align-items flex-wrap" style="gap: 10px">
          <el-tag type="info" size="medium" v-for="measure in item.measures" :key="measure.id">{{ measure.measureAbbr }}</el-tag>
          <el-tag v-if="planCenter && planCenter.center">{{planCenter.center.name}}</el-tag>
          <el-tag v-if="categoryName && !planCenter && showSmallAndLargeGroupFlag">{{categoryName}}</el-tag>
        </div>
        <div class="add-margin-t-10 font-size-16" style="line-height: 25px">{{ item.description }}</div>
      </div>
      <span v-if="!isOnlyView" slot="footer" class="dialog-footer">
        <el-button @click="handleShowLessonDialogClose">{{ $t('loc.close') }}</el-button>
        <el-button class="ai-btn" @click="continueToGenerate">
          <template #icon>
            <i class="lg-icon lg-icon-generate"></i>
          </template>
            Generate Details
        </el-button>
      </span>
    </el-dialog>
    <!-- 课程模板推荐弹窗 -->
    <lesson-template-recomend
      ref="LessonTemplateRecomendRef"
      @recommendLessonTemplate="recommendLessonTemplate"
      @cancelRecommendLessonTemplate="cancelRecommendLessonTemplate"
      />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import AppUtil from '@/utils/app'
import MediaViewer from './MediaViewer'
import LessonDetailDialog from '../../lessonLibrary/components/LessonDetailDialog'
// import LessonLike from '../../lessonLibrary/components/LessonLike'
// import LessonFavorite from '../../lessonLibrary/components/LessonFavorite'
import LessonDetail from '../../lessonLibrary/components/LessonDetail'
import LessonDetailMC from '@/views/magicCurriculum/components/lesson/LessonDetail'
// import LessonReadCount from '../../lessonLibrary/components/LessonReadCount'
import EditLessonDialog from './EditLessonDialog'
import store from '@/store'
import LessonPdf from '@/views/modules/lesson2/lessonLibrary/components/LessonPdf.vue'
import LessonDownload from '@/views/modules/lesson2/lessonLibrary/components/LessonDownload.vue'
import { equalsIgnoreCase, isTeacher } from '@/utils/common'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import LessonTemplateRecomend from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateRecomend.vue'
import LessonEnhanceDialog from '@/views/modules/lesson2/lessonLibrary/components/LessonEnhanceDialog.vue'

export default {
  name: 'PlanItem',
  components: {
    LessonDownload,
    LessonPdf,
    MediaViewer,
    LessonDetailDialog,
    // LessonReadCount,
    LessonDetail,
    LessonEnhanceDialog,
    // LessonLike,
    EditLessonDialog,
    // LessonFavorite,
    LessonDetailMC,
    LessonTemplateSelectModal,
    LessonTemplateRecomend
  },
  props: {
    isFirstActivity: {
      type: Boolean
    },
    showQuickAddLesson: {
      type: Boolean
    },
    planCenter: {
      type: Object
    },
    adminEdit: {
      type: Boolean
    },
    edit: {
      type: Boolean
    },
    item: {
      type: Object
    },
    frameworkData: {
      type: Array
    },
    children: {
      type: Array
    },
    type: {
      type: String
    },
    activeGuideDomId: {
      type: String
    },
    // 是否是长单元格item，默认是false
    long: {
      type: Boolean,
      default: false
    },
    // 是否是显示在模板中
    isTemplate: {
      type: Boolean,
      default: false
    },
    unitNumber: {
      type: Number
    },
    overView: {
      type: Boolean
    },
    // 分组名称
    categoryName: {
      type: String
    },
    isFromUnitDetail: {
      type: Boolean,
      default: false
    },
    // 周计划状态
    planStatus: {
      type: String,
      default: ''
    },
    showApply: {
      type: Boolean,
      default: false
    },
    review: {
      type: Boolean,
      default: false
    },
    // 是否锁定（其他人正在编辑周计划）
    isLocked: {
      type: Object,
      default: null
    },
    planType: {
      type: String,
      default: ''
    },
    isOnlyView: {
      type: Boolean,
      default: false
    },
    itemIndex: {
      type: Number,
      default: 0
    },
    // 是否展示周计划 AI 引导
    showAiGuide: {
      type: Object,
      default: () => ({ showAiGuide: false, isFallday: false })
    },
    editTemplate: {
      type: Boolean
    },
    // 复制课程 Loading
    copyLessonLoadingDay: {
      type: Array,
      default: () => []
    },
    // 本天课程数量
    dayLessonCount: {
      type: Number,
      default: 0
    },
  },
  inject: {
    dllContext: {
      from: 'dllContext',
      default: null
    },
    isPublic: {
      default: () => {
        return false
      }
    }
  },
  data () {
    return {
      hoverItem: false,
      showSelectBtn: false,
      showMeasureRemove: false,
      measurePopoverVisible: false,
      childPopoverVisible: false,
      selectLessonVisible: false,
      lessonPopoverVisible: false,
      searchMeasureStr: '',
      searchLessonStr: '',
      itemWidth: 240,
      regenerateLessonVisible: false, // 重新生成课程内容弹窗
      isEnhanceLesson: false, // 是否是增强课程
      itemHeight: 0,
      itemHeightTemp: 0,
      imgHeight: 60,
      domains: [],
      checkedMeasures: [],
      allChildren: [],
      checkedChildren: [],
      searchLessons: [],
      selectAllChildren: false,
      groupCheckedChildren: [],
      isIndeterminate: false,
      focusInput: false,
      loadedChild: false,
      loadedDomain: false,
      showDetail: false,
      previewLessonId: '',
      dllSetting: { // DLL 设置弹窗的参数
        open: false,
        groupId: ''
      },
      showAddReflectionButton: false, // 显示添加反思按钮
      updated: false, // 是否已经更新过
      onlyShowCoreMeasure: false, // 是否只显示核心测评点
      draging: false, // 是否正在拖拽
      title: '', // 三方课程名称
      link: '', // 三方课程链接
      showClearLink: false, // 是否显示清除链接按钮
      showClearName: false, // 是否显示清除名称按钮
      nameCleared: false, // 是否已经清除了 item 名称
      showAddThirdPartyLessonPopover: false, // 显示第三方课程添加弹窗
      refreshKey: 0,
      adaptUDLAndCLRGuide: {
          showAdaptUDLAndCLRGuide: false // 展示 UDL 和 CLR 引导
          // title: "Adapt UDL and CLR for My Class",
          // content: "Integrates UDL and CLR Strategies that adapt to your children's unique needs into every lesson plan, fostering an inclusive and diverse classroom environment.",
      },
      hasGroupTeams: false, // 是否已经分过组了
      adaptUDLAndCLROpen: undefined, // 是否开启了 UDL 和 CLR
      hideIEPOpen: undefined, // 是否开启了隐藏 IEP
      clickAdapterButton: false, // 是否点击了 adapt 按钮
      editorPageRenderComplete: false, // editor 页渲染完毕
      confirmUpdateGenerateUniversalDesignAndCLRData: false, // 点击了确认弹窗
      needUpdateLessonId: undefined, // 需要更新的 lessonId
      planLessons: [], // 周计划下的课程列表
      showLessonDialogVisible: false, // 是否显示课程弹窗
      showAdapterGuide: false, // 是否显示 adapter 引导
      showUnitAdaptGuideStatus: false, // 是否显示单元适配引导
      defaultText: this.$t('loc.planPleaseEnter'), // 文本单元格默认显示文字
      notCurriculumEditPreview: true, // 是否为 lg curriculum 页的预览,默认不是
      showAiAddLessonGuide: false, // 是否展示 AI 生成周计划引导
      copyLessonLoading: false, // 复制课程的 Loading
      unitInfo: null, // 单元信息
      mapFrameworkData: []  // 映射的框架数据
    }
  },

  created () {
    // 如果非编辑状态，监听是否展示核心测评点事件，否则默认展示全部测评点
    this.$bus.$on('changeShowCore', showCore => {
      if (!this.edit && equalsIgnoreCase(this.item.planId, showCore.planId)) {
        this.onlyShowCoreMeasure = showCore.open
      }
    })
    if (this.overView) {
      this.$bus.$on('overViewChangeShowCore', show => {
        this.onlyShowCoreMeasure = show
      })
    }
    this.initItem()
    this.convertDomains()
    this.convertChildren(true)
    if (!this.item.id) {
      this.item.id = tools.uuidv4()
    }
    // 初始化图片信息
    this.initItemMedia()
    // 初始化更新方法
    this.updateItem = tools.debounce(this.updateItemCallback, 2000)
    // 注册回调事件
    this.$bus.$on('updateWeeklyLessonId', ({ affiliationItemId, oldLessonId, newLessonId }) => {
        // 如果当前 item 的 lessonId 和 oldLessonId 相同，那么就更新 lessonId
        if (affiliationItemId === this.item.id && this.item.lessonId === oldLessonId && !oldLessonId) {
            // 更新 lessonId
            this.$set(this, 'needUpdateLessonId', newLessonId)
            this.$set(this.item, 'lessonId', newLessonId)
            // 更新 item
            this.updateItem()
        }
    })
    // 注册完成课程改编后更新 item 事件
    this.$bus.$on('updateAdaptedItem',({ itemId, lesson, lessonId, isAdaptedLesson }) => {
      if (itemId.toUpperCase() === this.item.id.toUpperCase()) {
        if (!lessonId) {
          this.item.lessonId = lesson.id
          this.item.lessonAuthorId = lesson.authorId
          if (lesson.coverMediaUrls && lesson.coverMediaUrls[0]) {
            this.item.mediaUrl = lesson.coverMediaUrls[0]
          } else {
            // 没有封面显示默认封面图
            this.item.mediaUrl = 'https://s3.amazonaws.com/com.learning-genie.cdn/images/defaultLessonCover.jpg'
          }
          this.item.name = lesson.name
          // 测评点
          let lessonMeasures = lesson.measure || []
          // 找出映射的测评点
          if (this.mapFrameworkData && this.mapFrameworkData.length > 0) {
            this.mapFrameworkData.forEach(measure => {
              measure.mappedMeasures && Object.values(measure.mappedMeasures).forEach(mappedList =>
                mappedList.forEach(item => {
                  // 如果有映射关系，且删除的测评点中没有映射的测评点，则加入到删除的测评点中
                  if (lessonMeasures.includes(item.abbreviation) && !lessonMeasures.includes(measure.abbreviation)) {
                    lessonMeasures.push(measure.abbreviation)
                  }
                }
              ))
            })
          }
          // 使用课程的测评点
          if (lessonMeasures) {
            this.domains.forEach(domain => {
              domain.measures.forEach(measure => {
                measure.selected = false
              })
            })
            lessonMeasures.forEach(lessonMeasure => {
              this.domains.forEach(domain => {
                domain.measures.forEach(measure => {
                  if (measure.abbreviation === lessonMeasure) {
                    measure.selected = true
                  }
                })
              })
            })
            this.changeMeasure(true)
          }
        } else {
          this.$set(this.item, 'lessonId', lessonId)
        }
        this.$set(this.item, 'isAdaptedLesson', isAdaptedLesson)
        this.$emit('callBatchSave')
      }
    })

    // 是否为 curriculumPreview
    this.isNotCurriculumEditPreview()
  },

  beforeDestroy () {
    // 移除总线事件。不移除，会在拖动组件后，发生多次总线事件注册
    this.$bus.$off('updateLessonItemBusOn')
  },

  mounted () {
    // 挂载结束通知父组件
    if (!this.overView) {
      this.$nextTick(() => {
        this.$bus.$emit('itemMounted')
      })
    }
    var width = this.$refs.itemArea.clientWidth + 2
    this.itemWidth = width > 200 ? width + 2 : 280
    this.calHeight()
    window.addEventListener('resize', () => {
      this.resizeTextarea()
    })
    this.$nextTick(() => {
      // 判断是否显示单个 Adapted 引导
      this.showAdaptGuide(this.item)
    })
    this.showUnitAdaptGuide(this.item)
    // 是否打开 AI 助手生成周计划的的引导
    this.getNeedShowAiAddLessonGuide()

    // 注册完成课程更新封面或视频后需要更新 item 数据
    this.$bus.$on('updateLessonItemBusOn', this.handleUpdateLessonItemMedia)
  },

  computed: {
    ...mapState({
      isCG: state => state.curriculum.isCG, // 是否是 Curriculum Genie
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      unit: state => state.curriculum.unit, // 单元数据
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      currentUser: state => state.user.currentUser,
      publicOpen: state => state.common.open ? state.common.open.publicLessonSearchOpen : false, // 公共课程搜索开关
      adaptUDLAndCLROpenState: state => state.lesson.adaptUDLAndCLROpen, // UDL 和 CLR 开关
      open: state => state.common.open,
      weeklyCreateAILessonOpen: state => state.common.open ? state.common.open.weeklyCreateAILessonOpen : false, // 是否开启了 AI 生成周计划
      singleAdaptGuide: state => state.lesson.singleAdaptGuide, // 单个 Adapted 引导
      planInterpretationGuide: state => state.lesson.planInterpretationGuide, // 周计划讲解引导
      planGenerateLessonGuide: state => state.lesson.planGenerateLessonGuide, // 周计划 AI 生成课程引导
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      unitAdaptGuide: state => state.unit.unitAdaptGuide, // 单元 Adapted 引导
      unitBaseInfo: state => state.unit.unitBaseInfo, // 单元作者 ID
      isMagic: state => state.curriculum.isMC, // 是否是魔法课程
      showSmallAndLargeGroupFlag: state => state.curriculum.showSmallAndLargeGroupFlag, // 是否显示 Small Group 和 Large Group
      unitOverviewPreviewDrag: state => state.curriculum.unit.unitOverviewPreviewDrag, // 全局 unitOverviewPreviewDrag 用于启用特殊情况下的拖拽预览
      planFrameworkId: state => state.lesson.planFrameworkId, // 框架映射数据的 Plan Framework ID
      mapFrameworkDataMap: state => state.lesson.mapFrameworkDataMap, // 框架映射数据
      mapFrameworkDataDB: state => state.lesson.mapFrameworkData, // 框架映射数据
      isGenieCurriculumToUnitPlanThird: state => state.curriculum.isGenieCurriculumToUnitPlanThird, // 是否由 genie 生成的 curriculum 进入的
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      unitAdapted: state => state.lesson.unitAdapted // 单元是否已经适配
    }),
    // unit 详情改编课程时显示操作栏
    unitDetail() {
      return equalsIgnoreCase(this.$route.name, 'unitDetail') || equalsIgnoreCase(this.$route.name, 'unit-detail-cg') || equalsIgnoreCase(this.$route.name, 'unit-detail-cg-desinger') || this.$route.path.includes('/lessons/unit-planner') || this.$route.path.includes('/curriculum-genie/unit-planner')
    },
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType, createUserId) {
        // 默认都可以编辑课程
        let canEdit = true
        // 再 unit 详情页中，如果是老师，只有自己创建的 Unit 的课程才能编辑
        if (this.isFromUnitDetail && this.isTeacher()) {
          canEdit = equalsIgnoreCase(createUserId, this.currentUserId)
        }
        // 如果是课程模板功能开启了，且不是范例课程，且可以编辑，且符合年龄和活动类型才会显示课程模板功能
        return !(this.unitBaseInfo && this.unitBaseInfo.exemplar) && canEdit && tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    },
    /**
     * 获取用户 id
     */
    currentUserId () {
        if (!this.currentUser) {
            return ''
        }
        return this.currentUser.user_id
    },
    showAdapterButton () {
        const routerNames = ['edit-plan', 'view-plan', 'shared-plan-detail', 'unitDetail']
        return routerNames.indexOf(this.$route.name) !== -1
    },
    showOperateBtn () {
      const routerNames = ['view-template', 'assigned-plan', 'edit-template', 'shared-plan-detail']
      return routerNames.indexOf(this.$route.name) === -1
    },
    isOpenAdaptUDLAndCLR () {
      return this.open && this.open.adaptUDLAndCLROpen
    },
    isAgencyTemplate () {
      return !equalsIgnoreCase(this.planType, 'NORMAL')
    },
    // 是否是跟踪学习计划
    isVirtualShadowing () {
      return equalsIgnoreCase(this.$route.name, 'shared-plan-detail')
    },
    isShowAdaptUDLAndCLRGuide: {
        get () {
            return this.canAdapter && this.adaptUDLAndCLROpen && this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide
        },
        set (val) {
            this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = val
        }
    },
    showAdaptUDLAndCLRGuide: {
        get () {
          return this.isShowAdaptUDLAndCLRGuide && !this.isLocked && !this.isAgencyTemplate && this.showOperateBtn
        },
        set (value) {
          return value
        }
    },
    /**
     * 是否来自于 ipad
     */
    isFromIpad () {
        return tools.isComeFromIPad()
    },
    /**
     * 获取班级 id
     */
    groupId () {
        // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
        const selectedGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
        if (selectedGroupId) {
            return selectedGroupId
        }
    },
    containerHeight () {
      // 计算容器的最小高度
      return this.$refs.itemArea.scrollHeight
    },
    filterDomains () {
      // 如果相关区域不显示，直接返回空数组，避免不必要的计算
      if (!this.measurePopoverVisible && !this.lessonPopoverVisible) {
        return [];
      }
  
      // 领域列表
      let domains = this.domains
      // 没有搜索，直接返回
      if (!this.searchMeasureStr || this.searchMeasureStr.trim().length === 0) {
        return domains
      }
      let searchMeasureStr = this.searchMeasureStr.trim().toLowerCase()
      // 过滤测评点
      let filterDomains = []
      domains.forEach(domain => {
        let filterMeasures = []
        domain.measures.forEach(measure => {
          let abbr = measure.abbreviation
          if (abbr && abbr.toLowerCase().indexOf(searchMeasureStr) != -1) {
            filterMeasures.push(measure)
          }
        })
        // 有符合条件的测评点，加入领域
        if (filterMeasures.length > 0) {
          filterDomains.push({
            id: domain.id,
            abbreviation: domain.abbreviation,
            measures: filterMeasures
          })
        }
      })
      return filterDomains
    },

    hasContent () {
      return (this.item.name && this.item.name.length > 0) || (this.checkedMeasures && this.checkedMeasures.length > 0) || (this.checkedChildren && this.checkedChildren.length > 0)
    },

    showEditView () {
      return (this.hoverItem && this.hasContent) || this.childPopoverVisible || this.measurePopoverVisible || this.lessonPopoverVisible || this.focusInput
    },

    isAdmin () {
      if (!this.currentUser) {
        return true
      }
      let role = this.currentUser.role
      return role && role.toUpperCase() === 'OWNER'
    },
    // 是否开启了 adapter 权限
    canAdapter () {
      if (!this.currentUser) return false
      const { role2 = '' } = this.currentUser || {}
      let role = role2.toUpperCase()
      return role === 'COLLABORATOR' || role === 'TEACHING_ASSISTANT' || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
    },
    canAddChildren () {
      return this.$route.name == 'edit-plan' || this.$route.name == 'view-plan'
    },
    canAddReflection () {
      return this.$route.name == 'view-plan'
    },
    /**
     * 是否显示拖拽的 handle
     */
    showDragHandle () {
      return ((this.type === 'DAY_COL' || (this.type === 'WEEK_COL' && this.long) || this.type === 'CENTER_ROW' || this.type === 'DAY_TEXT_COL' || this.type === 'WEEK_TEXT_COL') &&
              this.showEditView && this.edit && this.hasContent) || this.draging ||
        //  此条件当前用于 unit planner step2 and step3  unit overview 点击时使用
        this.showUnitOverviewPreviewDrag
    },

    // unitOverview 拖拽判断
    showUnitOverviewPreviewDrag () {
      // 如果当前是 center 不出现拖拽按钮
      if (this.planCenter) {
        return false
      }
      return this.unitOverviewPreviewDrag && this.type === 'DAY_COL' && this.showEditView
    },

    /**
     * 拖拽 item 的信息
     */
    dragInfo () {
      let id = this.item.id
      let planId = this.item.planId
      let lessonId = this.item.lessonId
      let childIds = this.checkedChildren.map(item => item.id) || []
      let measureIds = this.checkedMeasures.map(item => item.id) || []
      let name = this.item.name || ''
      let dayOfWeek = this.item.dayOfWeek
      let sortNum = this.item.sortNum
      let link = this.item.link || ''
      let frameworkId = this.item.frameworkId
      return JSON.stringify({
        id: id,
        name: name,
        dayOfWeek: dayOfWeek,
        sortNum: sortNum,
        link: link,
        planId: planId,
        lessonId: lessonId,
        childIds: childIds,
        measureIds: measureIds,
        frameworkId: frameworkId
      })
    },

    /**
     * 新标签页的路由名称
     */
    newTabTo () {
      let tab = 'PublicLessonDetail'
      // 如果在 Unit Planner 详情页，特殊处理
      if (this.$route.name === 'unitDetail') {
        tab = 'unitPlanLessonDetail'
      }
      return tab
    },
    // 是否展示 Adapted 按钮
    showAdaptBtn () {
      return this.canAdapter && this.isOpenAdaptUDLAndCLR && equalsIgnoreCase(this.planType, 'NORMAL') && !this.isFromIpad
    },
    // 是否显示 Adapted 引导 Popover
    showAdaptGuidePopover () {
      return this.showAdapterGuide && this.showAdaptBtn && this.showOperateBtn
    },
    // 是否是公共模板下的周计划
    isPublicTemplate () {
      return (this.isPublic && this.isPublic()) || false
    },
    // 是否是文本单元格
    isTextCol () {
      return this.type === 'DAY_TEXT_COL' || this.type === 'WEEK_TEXT_COL'
    },
    // 是否展示课程复制按钮
    showCopyButton () {
      return this.edit && !this.isTextCol
    },
    // 本天的课程是否展示复制课程按钮 Loading
    showCopyLessonLoading () {
      return this.copyLessonLoadingDay.includes(this.item.centerId ? this.item.centerId : this.item.dayOfWeek)
    },
    // 如果本天有 4 个课程且正在复制中，隐藏空 item，防止一天课程超过 5 节
    hideEmptyItem () {
      return !(this.dayLessonCount === 4 && this.showCopyLessonLoading && this.isEmptyItem(this.item))
    },
    // 拖拽及复制功能的热区
    dragHoverWidth () {
      return this.type === 'WEEK_COL' ? { width: '250px' } : { width: '100%' }
    }
  },
  methods: {
    // 是否是老师
    isTeacher,
    // 字符串比较
    equalsIgnoreCase,
     // 打开重新生成课程详情
    async openRedesignLessonDialog() {
      this.regenerateLessonVisible = true
    },

    // 关闭重新生成课程详情
    closeRedesignLessonDialog() {
      this.regenerateLessonVisible = false
    },

    // 确认并重新生成课程详情
    async confirmRedesignLesson(data) {
      // 将 data 存储到 vuex 中
      await this.$store.dispatch('setRegenerateLesson', data)
      let unitId = this.$route.query.unitId || this.$route.params.unitId
      let lessonId = this.item.id
      const routerName = this.unit.adaptedType ? `/curriculum-genie/unit-adapt/unit-planner/unit-editor/${unitId}/lesson-detail/${lessonId}` : `/curriculum-genie/unit-planner/unit-editor/${unitId}/lesson-detail/${lessonId}`
      // 跳转到单元课程编辑页面
      this.$router.push({
        path: routerName
      })
    },
    // 修改课程模板类型
    changeTemplateType (type) {
      // 如果是在 unitDetail 页面,直接清空当前课程和课程模板，再跳转到生成课程页面
      if (this.isFromUnitDetail) {
        this.updateLessonTemplateTypeAndRegenerate(type)
      } else {
        if (type) {
          this.$store.dispatch('setTemplateType', type)
          this.editLesson(true)
        }
      }
    },
    /**
     * 保存选中的测评点
     * 该方法用于保存用户在界面中选择的测评点信息。
     * 保存完成后，关闭测评点选择弹窗，并触发批量保存事件通知父组件。
     */
    saveMeasures() {
      this.measurePopoverVisible = false;
      this.$emit('callBatchSave')
    },
    // 更新 item 课程模板类型并且重新生成课程
    updateLessonTemplateTypeAndRegenerate (type) {
      // 收集更新参数
      let unitId = this.$route.query.unitId || this.$route.params.unitId
      let params = {
        unitId: unitId,
        planId: this.item.planId,
        frameworkId: this.item.frameworkId,
        items: [
          {
            id: this.item.id,
            planId: this.item.planId,
            categoryId: this.item.categoryId,
            lessonId: null,
            title: this.item.name,
            dayOfWeek: this.item.dayOfWeek,
            activitySortNum: this.item.dayOfWeek,
            measures: this.item.measures.map(x => x.measureAbbr),
            measuresString: this.item.measures.map(x => x.measureAbbr).join(','),
            description: this.item.description,
            confirmed: true,
            lessonTemplateType: type
          }
        ],
        clearItemLesson: false,
        notUpdateRubric: true
      }
      // 调接口更新
      this.$axios.post($api.urls().updatePlanItems, params).then(() => {
        const isImportAdapted = this.unit.adaptedType
        // 跳转前清除 vuex
        this.$store.commit('curriculum/RESET_UNIT')
        // 设置需要更新课程模板
        this.$store.dispatch('setNeedUpdateLessonTemplate', true)
        // 跳转到生成课程页面
        let lessonDetailRoute = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-detail-cg-designer' : 'lesson-detail-cg' : 'lessonDetail'
        if (isImportAdapted) {
          lessonDetailRoute = 'lesson-detail-cg-adapt'
        }
        
        // 进入下一步
        this.$router.push({
          name: lessonDetailRoute,
          params: {
            unitId: unitId,
            itemId: this.item.id
          }
        })
      })
    },
    // Unit 操作权限
    operateAuth () {
      // 如果当前角色是老师，则需要判断当前的 Unit 是不是本人创建的
      // 同时判断当前老师是否具有创建权限
      if (this.unitBaseInfo && this.unitBaseInfo.exemplar) {
        return false
      }
      if (this.isTeacher()) {
        return this.equalsIgnoreCase(this.currentUser.user_id, this.unitBaseInfo.authorId) && this.open && this.open.createUnitPlannerOpen
      } else {
        return true
      }
    },
    // 是否为 curriculumPreview
    isNotCurriculumEditPreview () {
      if (this.$route.name && this.$route.name.indexOf('curriculumUnitDetail') !== -1 && this.edit) {
        this.notCurriculumEditPreview = false
      }
    },

    // 课程发布后资源数据同步到 item
    async handleUpdateLessonItemMedia ({ itemId, mediaId }) {
      // 找到 item
      if (itemId.toUpperCase() !== this.item.id.toUpperCase()) {
        return
      }
      // 需要清空数据
      if (mediaId === null) {
        this.$set(this.item, 'medias', [])
        return
      }
      // 如果资源已是最新的，不用更新
      if (this.item.medias && this.item.medias.length > 0 && this.item.medias[0].id === mediaId) {
        return
      }
      // 同步媒体资源到课程
      await this.updateLessonMediaInfo(mediaId)
    },

      // 查询单个媒体资源并更新数据
    async updateLessonMediaInfo (mediaId) {
      // 查询单个媒体资源
      let mediaInfo = await LessonApi.getLessonMediaModel(mediaId)
      let medias = [
        mediaInfo
      ]
      // 更新 item 媒体资源
      this.$set(this.item, 'medias', medias)
    },
    /**
     * 课程详情页组件挂载后
     */
      lessonDetailMountedAfter () {
        // 判断是否需要引导
        this.getEventNotify()
    },
    refreshComponent () {
      this.refreshKey = this.refreshKey + 1
    },
    // 是否显示单个改编课程引导弹窗
    showAdaptGuide (item) {
      this.$nextTick(() => {
        // 当前活动项有课程封面，有课程信息并且没有关闭引导时才会去触发显示引导
        if (item && item.mediaUrl && item.lessonId && !this.singleAdaptGuide && this.planInterpretationGuide) {
          // 设置已经触发了显示引导的状态，后续的周计划项不需要再设置
          this.$store.dispatch('setSingleAdaptGuide', true)
          this.showAdapterGuide = JSON.parse(localStorage.getItem(this.currentUser.user_id + 'SHOW_PREVIEW_PLAN_DETAIL_ADAPT_GUIDE'))
        }
      })
    },
    // 展示 Unit 改编引导
    showUnitAdaptGuide (item, value) {
      if (this.isFromUnitDetail && item.dayOfWeek === 2) {
        this.showUnitAdaptGuideStatus = value || this.$store.state.unit.unitAdaptGuide
      }
    },
    hideGuideOperate (item) {
      // 设置不需要引导
      this.$set(item, 'allowed', false)
      // 隐藏引导
      this.showAdapterGuide = false
      localStorage.setItem(this.currentUser.user_id + 'SHOW_PREVIEW_PLAN_DETAIL_ADAPT_GUIDE', false)
      // 发起请求隐藏引导
      let result = { 'features': ['SHOW_PREVIEW_PLAN_DETAIL_ADAPT_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then()
    },
    /**
     * 编辑课程
     */
    async editLesson (flag) {
      // 如果是 Unit 详情中点击编辑课程时执行如下逻辑
      if (this.isFromUnitDetail) {
        this.editUnitLesson()
        return
      }
      // this.$refs.lessonEditor.initEdit = true
      // this.$refs.lessonEditor.singleEditLesson = true
      // this.$refs.lessonEditor.currentItemId = this.item.id.toUpperCase()
      this.$emit('singleEditLesson', this.item.id.toUpperCase())
      // 非编辑状态下查看预览课程详情点击编辑课程的埋点
      if (!this.edit) {
        this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_pop_ed')
      } else {
        // 添加埋点，从课程详情 popover 点击编辑课程或者从课程详情页点击编辑课程
        if (flag) {
          this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_edit')
        } else {
          this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_pop_edit')
        }
      }
      // 隐藏课程详情弹窗
      this.showDetail = false
      let isMyLesson = false
      // 判断是否是我的课程
      if (this.item.lessonAuthorId && this.currentUser.user_id) {
          const { role2 = '' } = this.currentUser || {}
          let role = role2.toUpperCase()
        isMyLesson = this.item.lessonAuthorId.toLowerCase() === this.currentUser.user_id.toLowerCase() && (role === 'COLLABORATOR' || role === 'TEACHING_ASSISTANT')
      }
      // 如果是我的课，
      if (isMyLesson) {
          // 那就还需要判断是否被推荐到机构课程了
          let lessonRecommendStatusRes = await LessonApi.getLessonRecommendStatus(this.item.lessonId)
          // 如果已经被推荐到机构课程了，则就表示不单纯是我的课了，就需要重新复制一份
          if (lessonRecommendStatusRes.agencyStatus === 'PROMOTED') {
              isMyLesson = false
          }
      }
      // var defaultValue = localStorage.getItem(getRecommendToAgencyKey())
      // this.$refs.editLesson.recommendToAgency = defaultValue !== 'false'
      // 加载课程信息
      // this.$refs.editLesson.loadLesson(isMyLesson, this.item.id)
    },

    /**
     * 移除课程
     */
    removeLesson (inDialog) {
      this.lessonPopoverVisible = false
      // 调用删除方法删除课程
      this.$emit('callDeleteItem', this.item.id, this.item.categoryId, this.item.centerId, () => {
        // 在课程详情弹窗中点击移除课程时显示提示语
        if (inDialog) {
          this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_pop_re')
          this.$message.success(this.$t('loc.lessons2RemoveSuccessTips'))
        } else {
          this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_delete')
        }
      })
      // 隐藏课程详情弹窗
      this.showDetail = false
      this.nameCleared = true
    },

    /**
     * 拖拽前预处理
     */
    beforeDragHandler () {
      this.hoverItem = false
      this.draging = true
    },

    /**
     * 拖拽状态改变事件
     */
    changeDragStatus (val) {
      // 开始拖拽时修改为拖拽状态，隐藏课程、小孩、测评点 popover
      if (val) {
        this.draging = true
        this.hoverItem = false
        this.lessonPopoverVisible = false
        this.childPopoverVisible = false
        this.measurePopoverVisible = false
        this.$analytics.sendEvent('web_weekly_plan_edit_drag')
      } else {
        // 结束拖拽时标记为未拖拽状态
        this.draging = false
      }
    },
    // 关闭所有的 popover
    closePopover () {
      this.focusInput = false
      this.lessonPopoverVisible = false
      this.measurePopoverVisible = false
      this.childPopoverVisible = false
    },
    // 添加三方课程
    addThirdPartyLesson () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_third_les')
      this.showAddThirdPartyLessonPopover = true
      this.title = this.item.name || ''
      this.link = this.item.link || ''
      setTimeout(() => {
        this.$refs.lessonPopover && this.$refs.lessonPopover.updatePopper()
        this.$refs.linkInput && this.$refs.linkInput.focus()
      }, 200)
    },
    // 保存链接
    saveLink () {
      this.$analytics.sendEvent('web_weekly_plan_edit_third_lesson_save')
      this.title = this.title.trim()
      this.link = this.link.trim()
      // 如果 Link 和 Ttile 都未填写，则不保存
      if (!this.title && !this.link) {
        return
      }
      // 清空课程信息
      this.item.lessonId = undefined
      this.item.mediaUrl = undefined
      // 保存链接信息
      this.item.name = this.title
      this.item.link = this.link
      // 关闭添加三方链接 popover
      this.lessonPopoverVisible = false
      this.showAddThirdPartyLessonPopover = false
      // 如果 link 不空, name 为空，则将 name 保存为与 link 同名
      if (this.item.link && !this.item.name) {
        this.item.name = this.item.link
      }
      // 调接口更新 item
      this.updateItem()
    },
    // 关闭添加链接popover
    closeLinkPopover () {
      this.lessonPopoverVisible = false
      this.showAddThirdPartyLessonPopover = false
      // 重置编辑链接内容
      this.title = ''
      this.link = ''
    },
    // 取消链接
    unLink () {
      this.item.link = ''
      this.lessonPopoverVisible = false
      this.showAddThirdPartyLessonPopover = false
      // 调接口更新 item
      this.updateItem()
    },
    // 编辑链接
    editLink () {
      // 赋值链接信息
      this.title = this.item.name || ''
      this.link = this.item.link || ''
      // 打开弹窗
      this.showAddThirdPartyLessonPopover = true
      this.lessonPopoverVisible = true
      this.$nextTick(() => {
        this.$refs.lessonPopover && this.$refs.lessonPopover.updatePopper()
      })
    },
    // 显示清除 link 内容按钮
    showClear () {
      this.showClearLink = !!this.link
    },
    // 隐藏清除 link 内容按钮
    hideClear () {
      this.showClearLink = this.$refs.linkInput && this.$refs.linkInput.focused && !!this.link
    },
    // 清除 link 内容按钮
    clearLink () {
      this.link = ''
    },
    /**
     * 清除 item name
     */
    clearName () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_activity_delete')
      this.nameCleared = true
      // 调用删除方法删除课程
      this.$emit('callDeleteItem', this.item.id, this.item.categoryId, this.item.centerId, () => {
        this.$message({
          message: this.$t('loc.planDeleteSuccess'),
          type: 'success'
        })
      })
    },
    /**
     * Name 输入框聚焦事件
     */
    focusInputName () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_add_activity')
      this.focusInput = true
      this.showNameClear()
      // 关闭所有周计划 AI 生成课程引导
      this.hideAllAiAddLessonGuide()
    },
    /**
     * 显示清除 name 内容按钮
     */
    showNameClear () {
      this.showClearName = !!this.item.name
      this.resizeTextarea()
    },
    /**
     * 隐藏清除 name 内容按钮
     */
    hideNameClear () {
      this.showClearName = this.$refs.dayInput && this.$refs.dayInput.focused && !!this.item.name
    },
    calHeight (m) {
      if (!this.hasContent || (this.type == 'WEEK_COL' && !this.long)) {
        return
      }
      this.$nextTick(() => {
        var element = this.$refs.itemArea
        if (element) {
          var height = element.clientHeight
          this.itemHeight = m ? height + 110 : height - 110
        }
      })
    },
    enterItem () {
      // 全局 unitOverviewPreviewDrag 用于启用特殊情况下的拖拽预览
      if (this.unitOverviewPreviewDrag && !this.planCenter) {
        this.hoverItem = true
      }
      // 光标划入item，如果是非编辑状态，显示添加周反思按钮
      if (!this.edit) {
        this.showAddReflectionButton = true
        return
      }
      this.hoverItem = true
      this.showSelectBtn = true
    },

    leaveItem () {
      // 全局 unitOverviewPreviewDrag 用于启用特殊情况下的拖拽预览
      if (this.unitOverviewPreviewDrag) {
        this.hoverItem = false
      }
      // 光标移出item，如果是非编辑状态，隐藏添加周反思按钮
      if (!this.edit) {
        this.showAddReflectionButton = false
        return
      }
      this.hoverItem = false
      this.showSelectBtn = false
      // 光标移出 item 时， 如果内容被清空了，直接删除 item
      if (!this.item.name && this.nameCleared) {
        this.$emit('callDeleteItem', this.item.id, this.item.categoryId, this.item.centerId)
        this.nameCleared = false
      }
    },

    initItemMedia () {
      let medias = this.item.medias
      if (medias && medias.length > 0 && !this.item.mediaUrl) {
        this.item.mediaUrl = medias[0].url
      }
    },

    changeItemName () {
      this.updated = true
      // 手动输入清空课程信息
      this.item.lessonId = undefined
      this.item.mediaUrl = undefined
      // 根据内容搜索课程
      var keywords = this.item.name.replace(/\s*/g,'')
      if (this.item.name && keywords.length > 0) {
        // 更新课程信息
        this.updateItem()
        this.showClearName = true
        // this.searchLesson()
      }
      this.$refs.lessonPopover && this.$refs.lessonPopover.updatePopper()
    },

    changeMeasure (batchUpdate) {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_measures_save')
      let checkedMeasures = []
      let checkedMeasureIds = []
      this.domains.forEach(domain => {
        domain.measures.forEach(measure => {
          if (measure.selected) {
            measure.measureAbbr = measure.abbreviation
            checkedMeasures.push(measure)
            checkedMeasureIds.push(measure.id)
          }
        })
      })
      this.checkedMeasures = checkedMeasures
      this.item.measureIds = checkedMeasureIds
      this.item.measures = checkedMeasures
      if (!batchUpdate) {
        this.updated = true
        this.updateItem()
      } else {
        // this.updateItem()
        if (this.loadedDomain && this.edit) {
          this.$emit('callBatchSave')
        }
      }
    },

    initItem () {
      // 设置选择的测评点
      if (this.item.measures) {
        let checkedMeasures = []
        let checkedMeasureIds = []
        this.item.measures.forEach(measure => {
          checkedMeasures.push({
            id: measure.id,
            abbreviation: measure.measureAbbr,
            core: measure.core
          })
          checkedMeasureIds.push(measure.id)
        })
        this.checkedMeasures = checkedMeasures
        this.item.measureIds = checkedMeasureIds
      }
      // 设置选择的小孩
      if (this.item.children) {
        let checkedChildren = []
        let checkedChildIds = []
        // 如果周计划每个分组的课程项的小孩 ID 集合不为空时，遍历当前选中的小孩，在拖动时为拖动的课程小孩赋值
        if (this.item.childIds) {
          this.children && this.children.forEach(child => {
            this.item.childIds.forEach(childId => {
              if (child.id.toLowerCase() === childId.toLowerCase()) {
                checkedChildren.push({
                  id: child.id,
                  displayName: child.name,
                  firstAbbr: child.name && child.name.length > 3 ? child.name.substring(0, 3) : child.name
                })
                checkedChildIds.push(child.id)
              }
            })
          })
        }
        // 如果周计划每个分组的课程项的小孩集合不为空时，直接遍历当前拖动的课程中所包含的小孩即可
        if (this.item.children) {
          this.item.children.forEach(child => {
            checkedChildren.push({
              id: child.id,
              displayName: child.name,
              firstAbbr: child.name && child.name.length > 3 ? child.name.substring(0, 3) : child.name
            })
            checkedChildIds.push(child.id)
          })
        }
        this.checkedChildren = checkedChildren
        this.item.childIds = checkedChildIds
      }
    },

    removeMeasure (measure) {
      let measureId = measure.id
      measure.selected = false
      this.checkedMeasures = this.checkedMeasures.filter(m => m.id !== measureId)
      this.item.measureIds = this.item.measureIds.filter(id => id !== measureId)
      // 将删除的测评点进行剔除
      this.item.measures = this.item.measures.filter(m => m.id !== measureId)
      // 取消测评点的选中状态
      this.cancelBottomMeasureSelectedByMeasureId(this.domains, measureId)
      this.updateItem()
      // this.updateItem()
      if (this.loadedDomain && this.edit) {
        this.$emit('callBatchSave')
      }
    },
    // 深度遍历的方式来取消底层测评点的选中状态
    cancelBottomMeasureSelectedByMeasureId (measure, measureId) {
      function dfsCancel (measure, measureId) {
        if (!measure) {
          return false
        }
        if ((!measure.measures || measure.measures.length === 0) && (measure.id === measureId)) {
          measure.selected = false
          return true
        }
        let measures = []
        if (Array.isArray(measure)) {
          measures = measure
        } else if (typeof measure === 'object') {
          measures = measure.measures
        }
        measures && measures.forEach(child => {
          // 剪枝操作，如果已经找到则停止进一步递归
          if (dfsCancel(child, measureId)) {
            return true
          }
        })
      }
      dfsCancel(measure, measureId)
    },

    // 切换框架事件
    changeFramework () {
      this.convertDomains()
    },

    convertDomains () {
      if (!this.frameworkData || this.frameworkData.length === 0) {
        return
      }
      // 当前项使用的框架
      let frameworkId = this.item.frameworkId
      let filterFrameworks = frameworkId ? this.frameworkData.filter(f => f.id === frameworkId) : null
      let framework = !filterFrameworks || filterFrameworks.length === 0 ? null : filterFrameworks[0]
      // 如果当前项没有设置框架信息，或者没有找到该框架，则取班级默认框架
      if (!framework) {
        framework = this.frameworkData[0]
        this.item.frameworkId = framework.id
      }
      let domains = []
      framework.child_domains.forEach(domain => {
        let measures = []
        domain.child_domains.forEach(measure => {
          measures.push({
            id: measure.id,
            abbreviation: measure.abbreviation ? measure.abbreviation : measure.name,
            core: measure.core,
            selected: this.item.measureIds && this.item.measureIds.indexOf(measure.id) != -1
          })
        })
        if (measures.length > 0) {
          domains.push({
            id: domain.id,
            abbreviation: domain.abbreviation ? domain.abbreviation : domain.name,
            measures: measures
          })
        }
      })
      this.domains = domains
      // 编辑时再重新展示选中的测评点
      this.edit && this.changeMeasure(true)
      // 第一次加载测评点后更新状态
      this.loadedDomain = true
    },

    selectAllChild (val) {
      let checkedChildren = []
      let checkedChildIds = []
      this.allChildren.forEach(child => {
        if (val) {
          child.selected = true
          checkedChildren.push(child)
          checkedChildIds.push(child.id)
        } else {
          child.selected = false
        }
      })
      this.checkedChildren = checkedChildren
      this.item.childIds = checkedChildIds
      this.checkSelectAllChildren()
      this.updateItem()
    },

    checkSelectAllChildren () {
      let checkedCount = this.checkedChildren.length
      this.selectAllChildren = checkedCount === this.allChildren.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.allChildren.length
    },

    changeChild (child, batchUpdate) {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_add_child_save')
      let checkedChildren = []
      let checkedChildIds = []
      this.allChildren.forEach(child => {
        if (child.selected) {
          checkedChildren.push(child)
          checkedChildIds.push(child.id)
        }
      })
      this.checkedChildren = checkedChildren
      this.item.childIds = checkedChildIds
      this.checkSelectAllChildren()
      if (!batchUpdate) {
        this.updated = true
        this.updateItem()
      } else {
        if (this.loadedChild && this.edit) {
          this.$emit('callBatchSave')
        }
      }
    },

    convertChildren (first) {
      if (!this.children || first && (!this.children || this.children.length === 0)) {
        return
      }
      let allChildren = []
      this.children.forEach(child => {
        let firstName = child.firstName.trim()
        allChildren.push({
          id: child.id,
          displayName: child.displayName,
          firstName: child.firstName,
          lastName: child.lastName,
          avatarUrl: child.avatarUrl,
          firstAbbr: firstName && firstName.length > 3 ? firstName.substring(0, 3) : firstName,
          selected: this.item.childIds && this.item.childIds.indexOf(child.id) != -1
        })
      })
      this.allChildren = allChildren
      // 重新展示选中的小孩
      if (this.children) {
        this.changeChild(undefined, true)
      }
      // 第一次加载小孩后更新状态
      this.loadedChild = true
    },

    openSelectLesson () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_library')
      this.$emit('callOpenLessonModal', this.selectLesson)
    },

    // 打开快速添加课程弹窗
    openAddLesson () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_add_les')
      // 打开 AI 助手页面， Center 有可能是用户自定义模块，按照 Activities 处理
      this.$emit('callOpenAddLessonModal', this.selectLesson, this.item.name, this.categoryName === 'Center' ? 'Activities' : this.categoryName)
    },

    // 搜索课程使用搜索所有课程接口
    searchLesson: tools.debounce(function () {
      this.searchLessons = []
      this.$refs.lessonPopove && this.$refs.lessonPopover.updatePopper()
      var keywords = this.item.name.replace(/\s*/g,'')
      if (keywords.length > 0) {
        LessonApi.getMyLessons({
          pageNum: 1,
          pageSize: 200,
          keyword: this.item.name,
          orderKey: 'UPLOAD_TIME',
          status: 'PUBLISHED',
          ages: [],
          domainIds: [],
          themeIds: [],
          otherMeasureIds: [],
          all: true,
          publicOpen: this.publicOpen,
          isAdaptedLesson: false
        }).then(response => {
          this.searchLessons = response.items
          // 更新poper位置
          this.$refs.lessonPopover.updatePopper()
        }).catch(error => {})
      } else {
        this.searchLessons = []
        this.$refs.lessonPopover.updatePopper()
      }
    }, 300),

    loadMyCreation () {

    },

    loadMyFavorites () {

    },

    selectLesson (lesson) {
      this.lessonPopoverVisible = false
      this.item.lessonId = lesson.id
      this.item.lessonAuthorId = lesson.authorId
      // 向外层传递 lessonId
      this.dllContext && (this.dllContext.lessonId = lesson.id)
      if (lesson.coverMediaUrls && lesson.coverMediaUrls[0]) {
        this.item.mediaUrl = lesson.coverMediaUrls[0]
      } else {
        // 没有封面显示默认封面图
        this.item.mediaUrl = 'https://s3.amazonaws.com/com.learning-genie.cdn/images/defaultLessonCover.jpg'
      }
      this.item.name = lesson.name
      // 测评点
      let lessonMeasures = lesson.measure || []
      // 找出映射的测评点
      if (this.mapFrameworkData && this.mapFrameworkData.length > 0) {
        this.mapFrameworkData.forEach(measure => {
          measure.mappedMeasures && Object.values(measure.mappedMeasures).forEach(mappedList =>
            mappedList.forEach(item => {
              // 如果有映射关系，且删除的测评点中没有映射的测评点，则加入到删除的测评点中
              if (lessonMeasures.includes(item.abbreviation) && !lessonMeasures.includes(measure.abbreviation)) {
                lessonMeasures.push(measure.abbreviation)
              }
            }
          ))
        })
      }
      // 使用课程的测评点
      if (lessonMeasures) {
        this.domains.forEach(domain => {
          domain.measures.forEach(measure => {
            measure.selected = false
          })
        })
        lessonMeasures.forEach(lessonMeasure => {
          this.domains.forEach(domain => {
            domain.measures.forEach(measure => {
              if (measure.abbreviation === lessonMeasure) {
                measure.selected = true
              }
            })
          })
        })
        this.changeMeasure()
      }
      this.updateItem()
    },

    updateItemCallback (param) {
      if (param) {
        LessonApi.updateItem(param).then(response => {
        }).catch(error => {})
      } else {
        // 如果item参数缺失 id，直接return
        if (!this.item.id) {
          return
        }
        let params = {
          id: this.item.id,
          name: this.item.name,
          categoryId: this.item.categoryId,
          planId: this.item.planId,
          lessonId: this.needUpdateLessonId ? this.needUpdateLessonId : this.item.lessonId,
          link: this.item.link ? this.item.link.trim() : null,
          dayOfWeek: this.item.dayOfWeek,
          childIds: this.item.childIds,
          measureIds: this.item.measureIds,
          frameworkId: this.item.frameworkId,
          sortNum: this.item.sortNum,
          centerId: this.planCenter ? this.planCenter.center.id : null
        }
        LessonApi.updateItem(params).then(response => {
            this.$emit('callUpdateItem',params)
        }).catch(error => {})
      }
    },

    /**
     * 更新项目信息
     */
    updateItem () {},

    /**
     * 预览课程
     */
    previewLesson (forcePreview, preview = false) {
      // 判断是否点击封面上的预览按钮
      if (preview) {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_preview')
      }
      // 根据编辑状态及是否正在拖拽判断以及是否强制预览，判断是否展示课程详情
      if ((this.edit || this.draging) && !forcePreview) {
        return
      }
      let lessonId = this.item.lessonId
      if (!lessonId || lessonId.trim().length === 0) {
        return
      }
      // 添加埋点，在课程详情中点击预览课程
      if (this.isFromUnitDetail) {
        this.$analytics.sendEvent('web_unit_detail_overview_lesson')
      }
      // 添加埋点，在课程 popover 中点击预览课程
      if (forcePreview) {
        this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson')
      }
      this.previewLessonId = lessonId
      // 将 Adapter UDL and CLR 引导设为默认关闭，是否展示后面会有额外的判断
      this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = false
      if (this.isMagic) {
        this.$router.push({
          name: 'magicLessonDetail',
          params: {
            lessonId: lessonId
          },
          // query: {
          //   lessonId: lessonId
          // }
        })
      } else {
        this.showDetail = true
      }
    },
    blurInput () {
      setTimeout(() => {
        this.focusInput = false
        this.hideNameClear()
        // this.lessonPopoverVisible = false
        // 如果活动名被清空，则删除该Item
        if (this.updated && this.item.name.length === 0) {
          this.searchLessons = []
          this.checkedMeasures = []
          this.domains.forEach(domain => {
            domain.measures.forEach(measure => {
              measure.selected = false
            })
          })
          this.$emit('callDeleteItem', this.item.id, this.item.categoryId, this.item.centerId)
          this.updated = false
        }
      }, 200)
    },

    clickItem () {
      if (this.showEditView || !this.edit) {
        return
      }
      if (this.type === 'WEEK_COL' && !this.long) {
        this.$refs.weekInput.focus()
      }

      // if (this.type === 'DAY_COL') {
      //   this.$refs.dayInput.focus()
      // }
    },

    createReflection () {
      let lessonId = this.item.lessonId
      if (!lessonId) {
        return
      }
      this.$router.push({
        name: 'lesson-reflection',
        params: {
          planId: this.item.planId,
          lessonId: lessonId,
          objectId: this.item.id
        }
      })
    },

    viewReflection () {
      this.$emit('callViewReflection', this.item.lessonId)
    },

    // 重新计算textarea大小
    resizeTextarea () {
      this.$nextTick(() => {
        this.$refs.dayInput && this.$refs.dayInput.resizeTextarea()
      })
    },

    // 打开链接
    openUrl (link) {
      if (tools.isComeFromIPad()) {
        AppUtil.appOpenUrlByBrowser(link)
      } else {
        window.open(link, '_blank')
      }
    },
    /**
     * 判断是否需要引导
     */
    judgeNeedAdaptUDLAndCLRGuide () {
        // 如果 fromIpad 直接将 adaptUDLAndCLROpen 设置为 false
        if (this.isFromIpad) {
            return
        }
        // 如果不是 老师且未开启 ，就直接 return
        if (!(this.adaptUDLAndCLROpen && this.canAdapter)) {
            return
        }
        // 先从本地浏览器缓存中查看是否存储了该缓存信息
        let adaptUDLAndCLRGuideFeatures = tools.localItem(store.state.user.uid + '_ADAPT_UDL_AND_CLR_GUIDE')
        // 如果没有则查询数据库
        if (adaptUDLAndCLRGuideFeatures === undefined || adaptUDLAndCLRGuideFeatures === null) {
            this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
                // 获取数据库中的信息
                if (result != null && result.adaptUDLAndCLRGuide) {
                    // 展示并存入缓存中
                    this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = true
                    tools.localItem(store.state.user.uid + '_ADAPT_UDL_AND_CLR_GUIDE', 'true')
                } else {
                    // 不展示并存入缓存中
                    this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = false
                    tools.localItem(store.state.user.uid + '_ADAPT_UDL_AND_CLR_GUIDE', 'false')
                }
            })
        } else {
            // 如果浏览器缓存中有，则解析浏览器中缓存的对象，判断是否需要展示
            if ((adaptUDLAndCLRGuideFeatures.toLowerCase() === 'true')) {
                this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = true
            } else {
                this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = false
            }
        }
    },

    /**
     * 关闭设置引导
     */
    endAdaptUDLAndCLRGuide () {
        // 如果引导开启了才会关闭
        if (this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide) {
            this.adaptUDLAndCLRGuide.showAdaptUDLAndCLRGuide = false
            tools.localItem(store.state.user.uid + '_ADAPT_UDL_AND_CLR_GUIDE', 'false')
            // 同时告诉后台,引导已经完成了
            let result = { 'features': ['ADAPT_UDL_AND_CLR_GUIDE'] }
            this.$axios.post($api.urls().hideGuide, result).then()
        }
    },
    /**
     * 页面渲染完成后来自子组件编辑课程的回调，用于生成课程的 UDL 和 CLR 数据
     */
    editorPageRenderAfter () {
        // 只有点击了按钮才会进行跳转
        if (this.clickAdapterButton) {
            // 打开教师分组的确认弹窗
            this.$refs.editLesson.$refs.lessonEditor.updateGenerateUniversalDesignAndCLRData(false)
            // 重置是否点击了按钮
            this.clickAdapterButton = false
        }
    },
    /**
     * 获取 event notify 中的信息
     */
    async getEventNotify () {
        // 如果 fromIpad 直接将 adaptUDLAndCLROpen 设置为 false
        if (this.isFromIpad) {
            this.adaptUDLAndCLROpen = false
            return
        }
        // 如果状态中不为空，则不用调请求
        if (this.adaptUDLAndCLROpenState !== undefined) {
            // 直接赋值即可
            this.adaptUDLAndCLROpen = this.adaptUDLAndCLROpenState
        } else if (this.adaptUDLAndCLROpen === undefined) {
            this.adaptUDLAndCLROpen = this.open && this.open.adaptUDLAndCLROpen
            const hideIEPOpen = this.open && this.open.hideIEPOpen
            this.$store.dispatch('setAdaptUDLAndCLROpen', this.adaptUDLAndCLROpen)
            this.$store.dispatch('setHideIEP', hideIEPOpen)
        }
        // 如果打开了开关，判断下是否需要引导
        if (this.adaptUDLAndCLROpen) {
            this.judgeNeedAdaptUDLAndCLRGuide()
        }
    },
    // 隐藏 Unit 改编引导弹窗
    hideUnitAdaptGuide () {
      // 将引导状态值设置为 false
      this.showUnitAdaptGuideStatus = false
      // 更新 vuex 中的值
      this.$store.dispatch('unit/setUnitAdaptGuide', false)
      const guideFeatures = this.guideFeatures
      guideFeatures.showAdaptUnitAndApplyGuide = false
      this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', guideFeatures)
      // 发起请求隐藏引导
      let result = { 'features': ['ADAPT_UNIT_AND_APPLY_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then()
    },
    /**
     * 启用 URL 和 CLR
     */
    async adaptUDLAndCLR (isDetail = true) {
        if (this.isFromUnitDetail) {
          // 关闭课程预览弹窗
          this.showDetail = false
          this.unitAdaptUDLAndCLR()
          return
        }
        if (!isDetail && !this.edit) {
            // 添加个性化课程适应埋点
            this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_adapt')
        }
        // 表示是点击的 adapter 按钮进入的编辑课程详情中
        if (!isDetail) {
          this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_adapt')
          this.clickAdapterButton = true
          // 关闭引导
          this.endAdaptUDLAndCLRGuide()
        }
        this.showDetail = false
        // 进入编辑课程
        // await this.editLesson()
        // 打开教师分组的确认弹窗
        this.$bus.$emit('showPersonalizePlanDialog', this.item.id.toUpperCase())
        // this.$refs.personalizePlan.showPersonalizePlan()
        if (!this.edit) {
          // 添加个性化课程适应埋点
          this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_pop_ad')
        } else {
          // 添加个性化课程适应埋点
          this.$analytics.sendEvent('web_weekly_plan_edit_lesson_adapt')
        }
    },
    // 显示未完成课程详情弹窗
    showLessonDialog () {
      // 如果课程名称不存在或者不是 Unit 详情中的课程，则不展示课程详情
      if (!this.item.name || !this.isFromUnitDetail) {
        return
      }
      if (this.item.lessonId) {
        this.previewLesson()
      } else {
        this.showLessonDialogVisible = true
      }
    },
    // 关闭未完成课程详情弹窗
    handleShowLessonDialogClose () {
      this.showLessonDialogVisible = false
    },
    // 检查单元的编辑状态
    async checkEditStatus () {
      const unitId = this.$route.query.unitId
      var res = await LessonApi.getUnitEditStatus(unitId)
      if (res) {
        let userName = res.userName
        this.$message.error(userName + this.$t('loc.unitPlannerIsEditing'))
        return false
      } else {
        return true
      }
    },
    // 获取单元是否展示使用课程模板弹窗信息
    async getUnitShowUseLessonTemplateInfo () {
      const unitId = this.$route.query.unitId
      // 获取单元详情
      let res = await this.$axios.get($api.urls().getUnitInfo, { params: { unitId: unitId } })
      this.unitInfo = res.unit
      let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12']
      // 如果已经开启课程模板功能且单元未出现过推荐模板弹窗，单元年龄段在 K12 年级范围内，则表示可以需要展示课程模板弹窗
      return this.eduProtocolsTemplateApplyOpen && res.unit && !res.unit.showLessonTemplateTip && k12Grades.includes(res.unit.grade)
    },
    // 继续生成课程
    async continueToGenerate () {
      // 检查是否可以编辑
      const canEdit = await this.checkEditStatus()
      if (!canEdit) {
        return
      }
      // 跳转到课程详情生成
      let goNextPage = () => {
        this.$store.commit('curriculum/RESET_UNIT')
        let params = {
          unitId: this.$route.query.unitId,
          itemId: this.item.id
        }
        // FOLC 和课程竞赛跳转页面相同 ， 与 LG 不同
        let routeName = this.isCG || this.isMC ? 'lesson-detail-cg' : 'lessonDetail'
        this.$router.push({
          name: routeName,
          params: params
        })
      }
      // 获取单元是否展示使用课程模板
      let showLessonTemplateTip = this.type !== 'CENTER_ROW' && await this.getUnitShowUseLessonTemplateInfo()
      // 如果单元展示使用课程模板弹窗，则打开弹窗，否则直接跳转到生成课程页面
      if (showLessonTemplateTip) {
        // 跳转的时候同时隐藏 dialog
        this.showLessonDialogVisible = false
        this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.openDialog(goNextPage, this.item.id, this.item.planId)
      } else {
        goNextPage()
      }
    },
    // 不使用自动推荐模板
    async cancelRecommendLessonTemplate (callback) {
      await this.updateUnitUseLessonTemplateInfo(false)
      this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.closeDialog()
      callback && callback()
    },
    // 推荐课程模板
    async recommendLessonTemplate (callback, itemId, planId) {
      let params = {
        unitId: this.$route.query.unitId,
        planId: planId,
        itemId: itemId
      }
      await this.$axios.post($api.urls().recommendLessonTemplate, params)
      .then(async () => {
        // 遍历当前周计划活动项，将推荐的课程模板赋值给对应的活动项
        await this.updateUnitUseLessonTemplateInfo(true)
        this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.closeDialog()
        callback && callback()
      })
    },
    // 更新单元是否使用课程模板信息
    async updateUnitUseLessonTemplateInfo (useTemplate) {
      let params = {
        unitId: this.unitInfo.id,
        useTemplate: useTemplate
      }
      await this.$axios.post($api.urls().updateUnitLessonTemplateInfo, params)
    },
    // Unit 改编课程
    async unitAdaptUDLAndCLR () {
      // 检查是否可以编辑
      const canEdit = await this.checkEditStatus()
      if (!canEdit) {
        return
      }
      this.$emit('adaptUnitPlan', this.item)
    },
    // Unit 编辑课程
    async editUnitLesson () {
      // 检查是否可以编辑
      const canEdit = await this.checkEditStatus()
      if (!canEdit) {
        return
      }
      this.$emit('editUnitLesson', this.item)
    },
    // 周计划复制课程
    copyLesson (inLessonDialog) {
      // 开始复制课程 Loading
      this.copyLessonLoading = true
      this.$emit('copyLesson', this.item, this.planCenter, inLessonDialog, () => {
        // 结束复制课程 Loading
        this.copyLessonLoading = false
      })
    },
    // 是否是空项目
    isEmptyItem (item) {
      if (!item) {
        return true
      }
      return (!item.name || item.name.trim().length === 0) && (!item.measureIds || item.measureIds.length === 0) && (!item.childIds || item.childIds.length === 0)
    },
    // 获取是否显示周计划 AI 生成课程引导状态
    getNeedShowAiAddLessonGuide () {
      if (this.showAiGuide.show && this.edit && !this.isTemplate && this.showQuickAddLesson && (this.planGenerateLessonGuide || this.editTemplate)) {
        // 首先从缓存中获取状态值，如果本地缓存中不存在时再去调用接口
        const guideState = localStorage.getItem(this.currentUser.user_id + 'WEEKLY_PLAN_ADD_LESSON')
        // 如果缓存中状态值不存在，则调用接口获取状态值
        if (!guideState) {
          this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
            // 对状态值进行赋值
            this.showAiAddLessonGuide = result.showWeeklyPlanAiAddLessonGuide && !this.isFromIpad
            // 将接口中的值存到本地
            if (typeof this.showAiAddLessonGuide === 'boolean') {
              localStorage.setItem(this.currentUser.user_id + 'WEEKLY_PLAN_ADD_LESSON', this.showAiAddLessonGuide)
            }
          })
        } else {
          // 如果有值则直接赋值
          this.showAiAddLessonGuide = JSON.parse(guideState) && !this.isFromIpad
        }
      }
    },
    // 打开 AI 助手页面，并关闭引导
    applyAiAddLesson () {
      // 永久关闭
      this.foreverHideAiAddLessonGuide()
      // 如果当天的课已经有 5 节，不能再新添课程
      if (this.showAiGuide.isFallday) {
        this.$message.error(this.$t('loc.plan151'))
        return
      }
      // 打开 AI 助手页面， Center 有可能是用户自定义模块，按照 Activities 处理
      this.$emit('callOpenAddLessonModal', this.selectLesson, this.item.name, this.categoryName === 'Center' ? 'Activities' : this.categoryName)
    },
    // 发起请求，隐藏批量 Adapt 课程引导
    foreverHideAiAddLessonGuide () {
      // 关闭弹框
      this.showAiAddLessonGuide = false
      // 发送请求永久关闭
      let result = { 'features': ['WEEKLY_PLAN_ADD_LESSON'] }
      this.$axios.post($api.urls().hideGuide, result).then()
      // 保存缓存关闭状态
      localStorage.setItem(this.currentUser.user_id + 'WEEKLY_PLAN_ADD_LESSON', false)
    },
    // 关闭所有周计划 AI 生成课程引导
    hideAllAiAddLessonGuide () {
      // 当缓存中为 true 时， 弹框才为打开状态
      const guideState = localStorage.getItem(this.currentUser.user_id + 'WEEKLY_PLAN_ADD_LESSON')
      if (guideState && JSON.parse(guideState)) {
        this.$emit('hideAllAiAddLessonGuide')
        this.foreverHideAiAddLessonGuide()
      }
    },
    // 关闭弹框
    hideAiAddLessonGuide () {
      this.showAiAddLessonGuide = false
    },
    // 获取框架测评点映射
    getFrameworkMeasureMap (frameworkId) {
      this.mapFrameworkData = this.mapFrameworkDataMap.get(frameworkId) || this.mapFrameworkDataDB
      localStorage.setItem('PLAN_MAPPED_FRAMEWORK_ID_' + frameworkId, JSON.stringify(this.mapFrameworkData))
    }
  },

  watch: {
    'item.medias': {
      deep: true,
      handler () {
        this.initItemMedia()
      }
    },
    mapFrameworkDataMap: {
      deep: true,
      handler (mapFrameworkDataMap) {
        if (this.planFrameworkId && mapFrameworkDataMap) {
          this.mapFrameworkData = mapFrameworkDataMap.get(this.planFrameworkId)
        }
      }
    },
    planFrameworkId: {
      deep: true,
      immediate: true,
      handler (planFrameworkId) {
        // 从本地存储中获取 planFrameworkId
        const mapFrameworkData = localStorage.getItem('PLAN_MAPPED_FRAMEWORK_ID_' + planFrameworkId)
        // 如果本地存储中有数据，就直接赋值
        if (mapFrameworkData) {
          this.mapFrameworkData = JSON.parse(mapFrameworkData)
          return
        }
        // 如果 planFrameworkId 不是空的
        if (planFrameworkId) {
          // 尝试通过 vuex 获取数据
          this.getFrameworkMeasureMap(planFrameworkId)
        } else if (this.item.planId) {
          // 如果 planFrameworkId 为空，但是 planId 不为空，就需要重新获取 planFrameworkId
          LessonApi.getPlanMappedFrameworkIds({
            planId: this.item.planId,
            isUnit: this.unitDetail
          }).then(res => {
            // 获取到 planFrameworkId 后，再次获取测评点映射
            this.planFrameworkId = res.id
            this.$store.dispatch('setPlanFrameworkId', res.id)
            this.$store.dispatch('getMapFrameworkData', res.id).then(res => {
              this.mapFrameworkData = res
              localStorage.setItem('PLAN_MAPPED_FRAMEWORK_ID_' + planFrameworkId, JSON.stringify(this.mapFrameworkData))
            })
          })
        }
      }
    },
    showEditView (val) {
      // unit overview 拖拽时不需要变更高度
      if (this.unitOverviewPreviewDrag) {
        return
      }
      this.calHeight(val)
    },
    frameworkData (val) {
      val && this.convertDomains()
    },
    'isMagic': {
      handler (val) {
        if (val) {
          console.log('isMagic1111111111111111111', val)
        }
      }
    },
    children (val) {
      val && this.convertChildren()
    },
    // 监听引导高亮的dom元素
    activeGuideDomId (val) {
      // 如果引导步骤到了第二步，展示出三个popover，否则进行隐藏
      if (this.item.guideItemDomId && val === this.item.guideItemDomId) {
        this.focusInput = true
      } else {
        this.focusInput = false
      }
    },
    showAdaptUDLAndCLRGuide () {
      this.$nextTick(() => {
        this.$refs.settingGuide && this.$refs.settingGuide.updatePopper()
      })
    },
    focusInput (val) {
      // 如果聚焦了当前输入且引导domId已销毁，才能展示课程popover
      // 并且不是文本单元格
      if (this.type.indexOf('_TEXT_') === -1 && val && !this.activeGuideDomId) {
        this.lessonPopoverVisible = true
        this.$nextTick(() => {
          this.$refs.lessonPopover && this.$refs.lessonPopover.updatePopper()
        })
      }
    },
    // 监听引导弹出状态
    'showAiGuide.show': {
      deep: true,
      handler () {
        this.getNeedShowAiAddLessonGuide()
      }
    },
    // 监听父组件传值来的item变化，变化后再次初始化item
    'item.measures': {
      deep: true,
      handler () {
        let checkedMeasures = []
        if (this.item.measures) {
          this.item.measures.forEach(measure => {
            checkedMeasures.push({
              id: measure.id,
              abbreviation: measure.measureAbbr,
              core: measure.core
            })
          })
        }
        this.checkedMeasures = checkedMeasures
      }
    },
    'item.children': {
      deep: true,
      handler () {
        let checkedChildren = []
        if (this.item.children) {
          this.item.children.forEach(child => {
            checkedChildren.push({
              id: child.id,
              displayName: child.name,
              firstAbbr: child.name && child.name.length > 3 ? child.name.substring(0, 3) : child.name
            })
          })
        }
        this.checkedChildren = checkedChildren
      }
    },
    // 测评点选择 popover
    measurePopoverVisible (val) {
      if (val) {
        this.lessonPopoverVisible = false
      } else {
        // 如果 popover 关闭且发生过更新,则发送更新事件
        if (this.updated && this.loadedChild && this.edit) {
          this.$emit('callBatchSave')
        }
      }
    },
    // 小孩选择 popover
    childPopoverVisible (val) {
      if (val) {
        this.lessonPopoverVisible = false
      }
    },
    adaptUDLAndCLROpen (val) {
        // 进行判断
        if (val) {
            // 如果开启了，就需要判断是否需要展示引导
            this.judgeNeedAdaptUDLAndCLRGuide()
        }
    },
    planInterpretationGuide (value) {
      if (value) {
        this.$nextTick(() => {
          this.showAdaptGuide(this.item)
        })
      }
    },
    unitAdaptGuide (newValue) {
      this.showUnitAdaptGuide(this.item, newValue)
    },
    // 监听是否要开启引导
    planGenerateLessonGuide () {
      this.getNeedShowAiAddLessonGuide()
    }
  }
}
</script>

<style lang="less" scoped>
.lesson-link:hover {
  text-decoration: underline;
}
/deep/ .el-image {
  border-radius: 4px !important;
}
@media only screen and (max-width:1199px){
  //ipad
  .ipad_width{
    display: flex;
    justify-content: flex-start;
    max-width: 15px;
  }
}
.has-relfection-item {
  margin-bottom: 32px;
}
.item-area {
  display: flex;
  flex-direction: column;
}
.item-row {
  display: flex;
  padding-left: 2px;
  align-items: center;
  min-height: 20px;
  /deep/ .el-button--text {
    padding: 5px 0;
  }
  .lesson-media {
    flex: none;
    width: 100%;
    // height: 60px;
  }
  .lesson-media-small {
    flex: none;
    width: 18%;
  }
}
.item-row.row-border {
  min-height: 40px;
  margin-bottom: 5px;
}
.measure-row {
  padding-bottom: 0 !important;
}
.item-icon {
  flex: none;
  width: 20px;
  i {
    font-size: 16px;
  }
}
.item-content {
  flex: auto;
  width: 90%;
}
.item-operation {
  flex: none;
}
.row-border {
  border: dashed 1px #98a6ad;
}
.measure-area, .child-area {
  padding: 5px 10px;
  min-height: 60px;
  max-height: 300px;
  overflow-y: auto;
}
.child-area {
  padding: 0 !important;
  margin-bottom: 12px;
}
.search-child-area {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 2;
  padding: 10px 10px 5px 10px;
}
.child-list {
  overflow-y: auto;
}
/deep/ .el-tag--info {
  color: #323338;
}
.close-btn {
  position: absolute;
  top: -4px;
  right: -4px;
}
.child-check-row {
  .child-avatar {
    max-width: 32px;
    border-radius: 50%;
  }
  /deep/ .el-checkbox {
    margin-bottom: 0 !important;
  }
  /deep/ .el-checkbox__label {
    width: calc(100% - 15px);
  }
  padding: 10px 10px 0 10px;
}
.search-lesson-row:hover {
  min-height: 45px;
  background: #e8eef0;
}
.search-lesson-media {
  flex: none;
  height: 45px;
  width: 80px;
  padding: 5px;
  display: flex;
  justify-content: center;
}
.search-lesson-content {
  min-height: 45px;
  display: flex;
  align-items: center;
}
.search-lesson-content:hover {
  color: #10B3B7 !important;
}
.pre-area {
  white-space: pre-line;
  word-break: break-word;
}
.lesson-area /deep/ .media-viewer {
  height: 100%;
}

.lesson-area /deep/ .el-divider--horizontal {
  margin: 12px 0;
}

.lesson-area-item {
  padding: 0 12px;
  color: #111c1c;
  cursor: pointer;
  height: 40px;
  line-height: 40px;
}

.lesson-area-item-ai {
  font-weight: 600;
  font-size: 14px;
  background: linear-gradient(to right, #2D9CDB, #8B63FF);
  -webkit-background-clip: text;
  color: transparent;
}

.lesson-area-item:hover {
  background: #ddf2f3;
  color: #10b3b7;
}

.link-form-label {
  margin-right: 8px;
  word-break: normal;
  color: #111c1c;
}

.active-border {
  border: 1px dashed #10b3b7 !important;
}

.option-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.truncate {
  white-space: nowrap;
  // overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  margin-bottom: 5px;
  max-width: 10vw
}
.center-truncate {
  white-space: nowrap;
  // overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  margin-bottom: 5px;
}
.spanTruncate {
  width: 100%;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.unitTag {
  margin-top: -7px;
  margin-left: -6px;
  height: 10%;
  width: 13%;
  background-color: #02A7F0;
  color: white;
  border-radius: 2px 2px 2px 2px;
}

.input-color-primary /deep/.el-textarea__inner,
.input-color-primary /deep/.el-input__inner {
  color: #10b3b7 !important;
  font-weight: 600;
}

.custom-textarea {
  position: relative;
  width: 100%;
  /deep/.el-textarea__inner {
    word-break: break-word;
    resize: none;
    &::-webkit-scrollbar {
      height: 5px;
      width: 5px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #c5c7d0;
    }
  }
  /deep/.textarea-clear-btn {
    color: #323338;
    font-weight: 600;
    position: absolute;
    bottom: 6px;
    right: 6px;
  }
}

.drag-handle-box {
  cursor: pointer;
  text-align: center;
  z-index: 99;
}

/deep/ .el-icon-error:before {
  font-family: 'lg-icon';
  content: "\e6a7";
}
.drag-handle-box {
  cursor: pointer;
  text-align: center;
  z-index: 99;
}

.ai-btn, .ai-btn:hover, .ai-btn:focus {
    background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
    border-color: none !important;
    border: 0 !important;
    color: #FFFFFF;

}

.ai-btn:active {
    opacity: 0.5 !important;
}

.lessons-cover {
  .preview-lesson {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .5);
    justify-content: center;
    align-items: center;
    visibility: hidden;
    .preview-icon {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .adapt-icon {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .adapted-tag {
    position: absolute;
    top: 0px;
    left: 0px;
    color: var(--color-white) !important;
    background: var(--color-ai-assistant) !important;
    border: 1px solid var(--color-ai-assistant) !important;
    z-index: 1;
  }
  .center-preview-lesson {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .5);
    visibility: hidden;
    .center-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%);
    }
  }
}
.lessons-cover:hover {
  .preview-lesson {
    visibility: visible;
  }
  .center-preview-lesson {
    visibility: visible;
  }
  .adapted-tag {
    visibility: hidden;
  }
}
.img-background {
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 4px;
}
.lesson-cover-area {
  .adapt-btn {
    position:absolute;
    right: 5px;
    top: 5px;
    width: 34px;
    height: 34px;
    visibility: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .unit-adapt-btn {
    position:absolute;
    right: 30px;
    top: 5px;
    width: 34px;
    height: 34px;
    visibility: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 997;
    .unit-edit {
      border-bottom-right-radius: unset;
      border-top-right-radius: unset;
    }
    // .unit-adapt {
    //   margin-left: 0px!important;
    //   border-bottom-left-radius: unset;
    //   border-top-left-radius: unset!important;
    // }
  }
  .unit-adapt-btn-guide {
    position:absolute;
    right: 30px;
    top: 5px;
    width: 34px;
    height: 34px;
    visibility: visible;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 997;
    .unit-edit {
      border-bottom-right-radius: unset;
      border-top-right-radius: unset;
    }
    // .unit-adapt {
    //   margin-left: 0px!important;
    //   border-bottom-left-radius: unset;
    //   border-top-left-radius: unset!important;
    // }
  }
  .adapt-btn-guide {
    position:absolute;
    right: 5px;
    top: 5px;
    width: 34px;
    height: 34px;
    visibility: visible;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .adapted-tag {
    position: absolute;
    top: 0px;
    left: 0px;
    color: var(--color-white) !important;
    background: var(--color-ai-assistant) !important;
    border: 1px solid var(--color-ai-assistant) !important;
    z-index: 1;
  }
}
.lesson-cover-area:hover {
  .adapt-btn {
    visibility: visible;
  }
  .unit-adapt-btn {
    visibility: visible;
  }
}
/deep/ .el-dialog__body {
  padding-top: 10px!important;
}
.core-measure {
  display: flex;
  align-items: center;
  gap: 5px;
}

.dropdown-menu-more {
  i {
    font-size: 20px;
    margin-right: 8px;
  }

  span {
    font-size: 16px
  }

  li {
    display: flex;
    align-items: center;
  }
}

/deep/.lesson-copy-button{
  width: 40px;
  padding: 0px;
  opacity: 1 !important;
  z-index: 99;
  .el-icon-loading{
    margin-left: 4px!important;
  }
}

/deep/ .unCompleted-lesson {
  & .el-dialog__title {
    word-break: break-word!important;
  }
}
</style>
<style lang="less">
.add-activity-guide{
  background-color: #878BF9;
  border-radius: 8px;
  .popper__arrow:after {
    border-right-color: #878BF9 !important;
  }
}
.input-lesson-popover {
  margin-top: 0 !important;
  max-height: 300px !important;
  overflow-y: auto;
  padding: 12px 0 !important;
}

.el-popper.adapt-UDL-and-CLR-guide-color-text {
    background: var(--color-ai-assistant);
    color: #FFFFFF;
    padding: 24px;
    border: none;
    &.el-popper[x-placement^=left] .popper__arrow::after{
        border-left-color: var(--color-ai-assistant);
    }
    &.el-popper[x-placement^=right] .popper__arrow::after{
        border-right-color: var(--color-ai-assistant);
    }
    &.el-popper[x-placement^=bottom] .popper__arrow {
        display: block !important;
        border-bottom-color: var(--color-ai-assistant) !important;
    }
    &.el-popper[x-placement^=bottom] .popper__arrow::after{
        border-bottom-color: var(--color-ai-assistant) !important;
    }
    &.el-popper[x-placement^=top] .popper__arrow::after{
        border-top-color: var(--color-ai-assistant);
    }
    p {
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 150%;
    }
    ul {
        padding-left: 24px;
        margin-bottom: 24px;
        li {
            list-style: disc;
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 150%;
        }
    }
    .el-button {
        border-radius: 4px;
        border: 2px solid var(--dcdfe-6, #DCDFE6);
        background: var(--ffffff, #FFF);
        color: #676879;
    }
    .el-button:hover {
        border-radius: 4px;
        border: 2px solid var(--dcdfe-6, #DCDFE6);
        background: var(--ffffff, #FFF);
    }
}

.el-popper.unit-adapted-guide-style {
  top: 5px!important;
  background: var(--color-ai-assistant);
  color: #FFFFFF;
  padding: 24px;
  border: none;
  z-index: 998;
  &.el-popper[x-placement^=left] .popper__arrow::after{
    display: block!important;
    position: absolute;
    right: -190px;
    border-left-color: var(--color-ai-assistant);
  }
  &.el-popper[x-placement^=right] .popper__arrow::after{
    display: block !important;
    border-right-color: var(--color-ai-assistant);
  }
  &.el-popper[x-placement^=bottom] .popper__arrow {
    border-bottom-color: var(--color-ai-assistant) !important;
  }
  &.el-popper[x-placement^=bottom] .popper__arrow::after{
    border-bottom-color: var(--color-ai-assistant) !important;
  }
  &.el-popper[x-placement^=top] .popper__arrow::after{
    border-top-color: var(--color-ai-assistant);
  }
  p {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
  }
  ul {
    padding-left: 24px;
    margin-bottom: 24px;
    li {
      list-style: disc;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
    }
  }
  .el-button {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
    color: #676879;
  }
  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
  }
}
.el-popper.unit-adapted-guide-style::before {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  background: #878BF9;
  top: 9px;
  left: -8px;
  transform: rotate(45deg);
}
</style>
