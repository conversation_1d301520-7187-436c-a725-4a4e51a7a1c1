<template>
  <div v-if="showAdaptLessonButton">
    <!-- adapt 图标 -->
    <div v-if="isAdaptIcon">
      <el-popover v-model="showGuide" width="500" placement="right" popper-class="adapt-icon-btn-guide-style"
                  :append-to-body="false" :visible-arrow="false" trigger="manual">
        <div class="text-white">
          <!-- 标题 -->
          <div class="title-font-20 lg-margin-bottom-12">
            {{ $t('loc.adaptUDLAndCLRButton') }}
          </div>
          <!-- 引导文字 -->
          <div class="lg-margin-bottom-24 word-break text-left">
            <span class="font-size-16 font-weight-400 line-height-24">{{
                $t('loc.unitPlannerPersonalizePlanTitle')
              }}</span>
          </div>
          <img class="w-full" :src="require('@/assets/img/lesson2/unitPlanner/adapt.png')" alt="">
          <!--关闭引导按钮-->
          <div class="pull-right lg-margin-top-24">
            <el-button plain @click.stop="showGuide = false">
              <span class="font-size-14 font-weight-600 line-height-22"> {{ $t('loc.gotIt') }}</span>
            </el-button>
          </div>
        </div>
        <div slot="reference" class="unit-adapt-btn display-flex align-items" :class="{'visible': showGuide}">
          <el-tooltip class="item" effect="dark" :content="$t('loc.adaptUDLAndCLRButton')" placement="top">
            <el-button @click.stop="handleLessonAdapt" icon="lg-icon lg-icon-generate" class="ai-btn" size="small">
            </el-button>
          </el-tooltip>
        </div>
      </el-popover>
    </div>
    <!-- adapt 按钮 -->
    <div v-else>
      <el-popover placement="bottom" popper-class="adapt-lesson-btn-guide-style" v-model="showGuide"
                  :disabled="!showGuide"
                  :append-to-body="false" trigger="manual">
        <div class="text-white">
          <!-- 标题 -->
          <div class="title-font-20 lg-margin-bottom-12">
            {{ $t('loc.adaptUDLAndCLRButton') }}
          </div>
          <!-- 引导文字 -->
          <div class="lg-margin-bottom-24 word-break text-left">
                    <span class="font-size-16 font-weight-400 line-height-24">{{
                        $t('loc.unitPlannerPersonalizePlanTitle')
                      }}</span>
          </div>
          <!--关闭引导按钮-->
          <div class="pull-right lg-margin-top-24">
            <el-button plain @click.stop="showGuide = false">
              <span class="font-size-14 font-weight-600 line-height-22"> {{ $t('loc.gotIt') }}</span>
            </el-button>
          </div>
        </div>
        <div slot="reference" class="display-flex align-items">
          <el-tooltip class="item" effect="dark" popper-class="adapt-lesson-btn-tooltip-style"
                      :disabled="showGuide || disabledAdaptTooltip"
                      :content="$t('loc.adaptLessonDetailTip')"
                      placement="bottom">
            <el-button class="ai-btn" @click="handleLessonAdapt"
                       ref="adaptLessonBtn"
                       :disabled="disabledAdaptBtn"
                       :class="isLessonDetail ? 'height-32' : ''">
              <i class="lg-icon lg-icon-generate"></i>
              {{ $t('loc.adaptUDLAndCLRButton') }}
            </el-button>
          </el-tooltip>
        </div>
      </el-popover>
    </div>
    <!-- adapt 弹框 -->
    <adapt-lesson-dialog ref="adaptLessonDialogRef" :adapt-lesson-id="adaptLessonId"
                         @updateAdaptLesson="updateAdaptLesson"></adapt-lesson-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import AdaptLessonDialog from '@/views/modules/lesson2/lessonPlan/components/AdaptLessonDialog'
import { LESSON_PLAN_NEW_USER_UTC } from '@/utils/const'
import tools from '@/utils/tools'

export default {
  name: 'AdaptLessonButton',
  data () {
    return {
      showGuide: false,
      adaptLessonBtnMounted: false // 按钮是否加载完成
    }
  },
  components: { AdaptLessonDialog },
  props: {
    // 用于 adapt 的 lesson id
    adaptLessonId: {
      type: String,
      default: ''
    },
    isFirstLesson: {
      type: Boolean,
      default: false
    },
    // 是否 adapt 图标按钮
    isAdaptIcon: {
      type: Boolean,
      default: false
    },
    // 是否禁用 tooltip
    disabledAdaptTooltip: {
      type: Boolean,
      default: false
    },
    // 是否禁用按钮
    disabledAdaptBtn: {
      type: Boolean,
      default: false
    },
    // 是否是 lessonDetail 页面
    isLessonDetail: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 处理单元适配
    handleLessonAdapt () {
      // 关闭引导状态
      this.showGuide = false
      this.$refs.adaptLessonDialogRef.openAdaptUnitDialog()
      // 让按钮失去焦点, 解决 tooltip 从其他界面返回自动获取焦点展示提示的问题
      if (!this.isAdaptIcon && !this.disabledAdaptTooltip && this.$refs.adaptLessonBtn && this.$refs.adaptLessonBtn.$el) {
        this.$refs.adaptLessonBtn.$el.blur()
      }
    },
    // adapt 弹框设置完成之后的处理
    updateAdaptLesson () {
      this.$emit('lessonAdapt')
    }
  },
  watch: {
    // 监听 showAdaptIconGuide 的值
    showAdaptIconGuide: {
      handler (newVal) {
        // 如果 showAdaptIconGuide 为 true，则显示引导状态
        if (newVal) {
          this.$nextTick(() => {
            this.showGuide = true
          })
          // 关闭 adapt 引导，并更新 store 中设置的值
          let result = { 'features': ['ADAPT_LESSON_PLAN_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then((res) => {
            this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
              ...this.guideFeatures,
              showAdaptLessonPlanGuide: false
            })
          })
        }
      },
      immediate: true
    },
    // 监听 showAdaptButtonGuide 的值
    showAdaptButtonGuide: {
      handler (newVal) {
        // 如果 showAdaptButtonGuide 为 true，则显示引导状态
        if (newVal) {
          this.showGuide = true
          // 关闭 adapt 引导，并更新 store 中设置的值
          let result = { 'features': ['ADAPT_LESSON_PLAN_DETAIL_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then((res) => {
            this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
              ...this.guideFeatures,
              showAdaptLessonPlanDetailGuide: false
            })
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      open: state => state.common.open, // 功能开通
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 curriculum plugin
      currentUser: state => state.user.currentUser
    }),
    // 是否显示 adapt 图标引导,根据当前引导是否展示过，当前 Lesson 是否是第一个，是否是 adapt 图标按钮
    showAdaptIconGuide () {
      let isShowAdaptIconGuide = this.showAdaptLessonButton && this.guideFeatures && this.guideFeatures.showAdaptLessonPlanGuide && this.isFirstLesson && this.isAdaptIcon
      // 如果当前是 Lesson Plan 新用户，则需要判断是否展示过 Curriculum Lesson Plan To Unit 引导或 Curriculum Unit Planner 引导
      if (this.isLessonPlanNewUser) {
        isShowAdaptIconGuide = isShowAdaptIconGuide && (this.guideFeatures && (!this.guideFeatures.showCurriculumLessonPlanToUnitGuide || !this.guideFeatures.showCurriculumUnitPlannerGuide))
      }
      return isShowAdaptIconGuide
    },
    // 是否显示 adapt 按钮引导,根据当前引导是否展示过，当前按钮是否加载完成，是否是 adapt 按钮，是否是 lessonDetail 页面
    showAdaptButtonGuide () {
      return this.showAdaptLessonButton && this.guideFeatures && this.guideFeatures.showAdaptLessonPlanDetailGuide && this.adaptLessonBtnMounted && !this.isAdaptIcon && this.isLessonDetail
    },
    // 是否显示 adapt 按钮
    showAdaptLessonButton () {
      return this.open && this.open.adaptUDLAndCLROpen && this.isCurriculumPlugin
    },
    // 是否为 Lesson 新用户
     isLessonPlanNewUser () {
      return this.currentUser && this.currentUser.userInfo && tools.timeIsAfter(this.currentUser.userInfo.createdAtUtc, LESSON_PLAN_NEW_USER_UTC)
    },
  },
  mounted () {
    // 设置 adapt 按钮加载成功
    this.$nextTick(() => {
      this.adaptLessonBtnMounted = true
    })
  }
}
</script>

<style lang="less" scoped>
// 组件样式
.unit-adapt-btn {
  position: absolute;
  right: 10px;
  top: 5px;
  width: 34px;
  height: 34px;
  visibility: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.visible {
  visibility: visible;
}
</style>
<style lang="less">
.el-popper.adapt-icon-btn-guide-style {
  top: -180px !important;
  background: var(--color-ai-assistant);
  color: #FFFFFF;
  padding: 24px;
  border: none;
  margin-left: 20px;

  p {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
  }

  ul {
    padding-left: 24px;
    margin-bottom: 24px;

    li {
      list-style: disc;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
    }
  }

  .el-button,
  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--color-border);
    background: var(--color-ai-assistant);
    color: var(--color-white);
  }

  .el-button:hover {
    box-shadow: 0 0 0 3px #dcdfe666;
  }
}

.el-popper.adapt-icon-btn-guide-style::before {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  background: #878BF9;
  top: 193px;
  left: -8px;
  transform: rotate(45deg);
}

.el-popper.adapt-lesson-btn-guide-style {
  background: var(--color-ai-assistant);
  color: #FFFFFF;
  padding: 24px;
  border: none;
  max-width: 500px;

  &.el-popper[x-placement^=left] .popper__arrow::after {

    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=bottom] .popper__arrow {
    display: block !important;
    top: -5px;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }


  p {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
  }

  ul {
    padding-left: 24px;
    margin-bottom: 24px;

    li {
      list-style: disc;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
    }
  }

  .el-button,
  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--color-border);
    background: var(--color-ai-assistant);
    color: var(--color-white);
  }

  .el-button:hover {
    box-shadow: 0 0 0 3px #dcdfe666;
  }
}

.adapt-lesson-btn-tooltip-style {
  width: 600px;
  line-height: 1.5;
}
</style>