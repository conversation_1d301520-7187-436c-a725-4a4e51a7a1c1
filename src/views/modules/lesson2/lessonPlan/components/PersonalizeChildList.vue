<template>
    <div class="h-full display-flex flex-direction-col">
        <!-- 头部信息 -->
        <div v-if="needAdditionInfo" class="display-flex justify-content-between align-items m-b">
            <!-- 小孩总数 -->
            <div class="font-bold gap-5">
                <span class="font-size-16">{{ groupName }}</span>
                <span>({{
                        $t('loc.unitPlannerPersonalizePlanTotalChildren', { count: childCount })
                    }})</span>
            </div>
            <!-- 操作 -->
            <div v-show="editable">
                <el-button type="" @click="createChild" icon="el-icon-coin">Add Child</el-button>
            </div>
            <!-- 跳转到单独页面 -->
            <a class="lg-pointer" :href="rosterPath" target="_blank">
                <el-button @click="clickManager" v-show="!editable && !emptyPageShow" type="primary" plain>
                    <i class="el-icon-s-tools font-size-18"></i>
                    <span>{{ $t('loc.unitPlannerPersonalizePlanClassroomDemographics') }}</span>
                </el-button>
            </a>
        </div>
        <div v-show="!emptyPageShow"
             class="display-flex flex-direction-col prompt-table flex-auto min-height-0">
            <el-table
                :data="children"
                :height="tableHeight"
                row-class-name="w-full"
                header-row-class-name="w-full"
                class="w-full">
                <!-- 小孩名字 -->
                <el-table-column min-width="140" :label="$t('loc.unitPlannerChildListChildName')"
                                 :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                        <span>{{ scope.row.displayName }}</span>
                    </template>
                </el-table-column>
                <!-- 种族 -->
                <el-table-column :show-overflow-tooltip="true" min-width="140"
                                 :label="$t('loc.unitPlannerChildListHispanic')">
                    <template slot-scope="scope">
                        <div class="overflow-ellipsis w-full">{{ getAttrValue(scope.row, 'Hispanic') }}</div>
                    </template>
                </el-table-column>
                <!-- 种族 -->
                <el-table-column :show-overflow-tooltip="true" min-width="200"
                                 :label="$t('loc.unitPlannerChildListRace')">
                    <template slot-scope="scope">
                        <div class="overflow-ellipsis w-full">{{ getAttrValue(scope.row, 'Race') }}</div>
                    </template>
                </el-table-column>
                <!-- 语言 -->
                <el-table-column min-width="140" :show-overflow-tooltip="true"
                                 :label="$t('loc.unitPlannerChildListHomeLanguage')">
                    <template slot-scope="scope">
                        <span class="overflow-ellipsis w-full"
                              :style="{'color': getLanguageValue(scope.row, 'Language').danger ? 'var(--color-danger)' : 'var(--color-text-primary)' }"
                              v-html="getLanguageValue(scope.row, 'Language').value"></span>
                    </template>
                </el-table-column>
                <!-- Country -->
                <el-table-column :show-overflow-tooltip="true" min-width="160" :label="$t('loc.palceOrigin')">
                  <template slot-scope="scope">
                    <span class="overflow-ellipsis w-full">{{ getAttrValue(scope.row, 'Place of Origin') }}</span>
                  </template>
                </el-table-column>
                <!-- ELD -->
                <el-table-column :show-overflow-tooltip="true" min-width="100" :label="$t('loc.eld')">
                    <template slot-scope="scope">
                        <span class="overflow-ellipsis w-full">{{ getAttrValue(scope.row, 'ELD') }}</span>
                    </template>
                </el-table-column>
                <!-- IEP -->
                <el-table-column :show-overflow-tooltip="true" min-width="100" :label="$t('loc.iep')">
                    <template slot-scope="scope">
                        <span class="overflow-ellipsis w-full">{{ getAttrValue(scope.row, 'IEP/IFSP') }}</span>
                    </template>
                </el-table-column>
                <!-- Special education eligibility -->
                <el-table-column :show-overflow-tooltip="true" min-width="200"
                                 :label="$t('loc.unitPlannerChildListSpecialEducationEligibility')">
                    <template slot-scope="scope">
                        <span
                            class="overflow-ellipsis w-full"
                            :style="{'color': getSpeeGoalValue(scope.row, 'Special education eligibility').danger ? 'var(--color-danger)' : 'var(--color-text-primary)' }"
                            v-html="getSpeeGoalValue(scope.row, 'Special education eligibility').value"></span>
                    </template>
                </el-table-column>
                <!-- Comments -->
                <el-table-column :show-overflow-tooltip="true" min-width="140"
                                 :label="$t('loc.unitPlannerChildListComments')">
                    <template slot-scope="scope">
                        <div class="overflow-ellipsis w-full"
                             v-html="getSpeeGoalValue(scope.row, 'Comments').value"></div>
                    </template>
                </el-table-column>
                <!-- Adaptations -->
                <el-table-column :show-overflow-tooltip="true" min-width="140"
                                 :label="$t('loc.unitPlannerChildListAdaptations')">
                    <template slot-scope="scope">
                        <div class="overflow-ellipsis w-full"
                             v-html="getSpeeGoalValue(scope.row, 'Adaptations').value"></div>
                    </template>
                </el-table-column>
                <!-- IEP Goal -->
                <el-table-column :show-overflow-tooltip="true" min-width="140"
                                 :label="$t('loc.unitPlannerChildListIEPGoal')">
                    <template slot-scope="scope">
                        <div class="overflow-ellipsis w-full"
                             v-html="getSpeeGoalValue(scope.row, 'IEP Goal').value"></div>
                    </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column min-width="140" :label="$t('loc.unitPlannerChildListAction')" fixed="right"
                                 v-if="editable">
                    <template slot-scope="scope">
                        <!-- 编辑 -->
                        <el-button class="font-size-18 color-676879" type="text" icon="el-icon-edit"
                                   @click.stop="editChild(scope.row)"></el-button>
                        <!-- 删除 -->
                        <el-button class="font-size-18 color-676879" type="text" icon="el-icon-delete"
                                   @click.stop="deleteChild(scope.row)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div v-show="emptyPageShow">
            <LgEmptyPage :text="$t('loc.unitPlannerEmpty5')" :top="20"/>
        </div>
        <!-- 编辑小孩弹窗 -->
        <EditChildDialog
            v-if="editChildDialogVisible"
            :visible.sync="editChildDialogVisible"
            :child="currentEditChild"
            :groupId="groupId"
            @updateChild="updateChild"
        ></EditChildDialog>
    </div>
</template>

<script>
import EditChildDialog from '@/views/curriculum/roster/components/EditChildDialog.vue'
import LgEmptyPage from '@/components/LgEmptyPage.vue'
import { mapState } from 'vuex'

export default {
    name: 'PersonalizeChildList',
    components: {
        LgEmptyPage,
        EditChildDialog
    },

    props: {
        // 班级 ID
        groupId: {
            type: String,
            default: null,
        },

        // 表格高度
        tableHeight: {
            type: String,
            default: '100%'
        },

        // 是否可编辑
        editable: {
            type: Boolean,
            default: true,
        },
        // 获取 IEP 小孩等附加信息
        needAdditionInfo: {
            type: Boolean,
            default: false
        },
        // 页面路由
        resolvedRouteHref: {
            type: String,
            default: ''
        }
    },

    data () {
        return {
            children: [], // 小孩列表
            groupName: null, // 班级名称
            childCount: null, // 小孩数
            editChildDialogVisible: false, // 是否显示小孩弹窗
            currentEditChild: null, // 当前编辑的小孩
            rosterPath: this.$router.resolve({ name: 'roster' }).href, // Roster 页面路由
            emptyPageShow: false // 空页面是否展示
        }
    },

    watch: {
        // 监听路由的变化
        resolvedRouteHref: {
            handler: function (val, oldVal) {
                if (val && val !== '') {
                    this.rosterPath = val
                }
            },
            immediate: true
        }
    },

    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser
        }),
        currentUserId () {
            if (!this.currentUser) {
                return ''
            }
            return this.currentUser.user_id
        },
        currentGroupName () {
            // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
            const selectedGroupId = sessionStorage.getItem('selectedGroupName' + this.currentUserId)
            if (selectedGroupId) {
                return selectedGroupId
            }
        },
        getSpeeGoalValue () {
            return function (child, attrName) {
                // 不存在返回空
                if (!child) {
                    return { danger: false, value: '--' }
                }
                // 是否是 IEP
                let isIEP = false
                // 获取属性
                const attrIEP = child.showIep
                // 判断 attrIEP 是否是 true
                if (attrIEP) {
                    isIEP = true
                }
                // 是 IEP 的小孩
                if (isIEP) {
                    // 获取属性
                    const attrIEPGoal = this.getAttrValue(child, attrName)
                    // 如果 'Special education eligibility' 对应的 属性值是空的
                    if (this.isEmptyOrBlank(attrIEPGoal) && attrName === 'Special education eligibility') {
                        return { danger: true, value: 'Please Complete' }
                    } else if (!this.isEmptyOrBlank(attrIEPGoal)) {
                        return { danger: false, value: attrIEPGoal }
                    } else {
                        return { danger: false, value: '--' }
                    }
                }
                // 没有值
                return { danger: false, value: '--' }
            }
        },

        // 获取语言的值
        getLanguageValue () {
            return function (child, attrName) {
                // 不存在返回空
                if (!child) {
                    return { danger: false, value: '--' }
                }
                // 是否是 ELD
                let valid = false
                // 是否是 ELD
                const showEld = child.showEld
                // 如果 showEld 为 true
                if (showEld) {
                    valid = true
                }
                // 获取属性
                const language = this.getAttrValue(child, attrName)
                // 是 ELD 的小孩
                if (valid) {
                    // ELD 属性是否符合校验规则
                    const eldValid = child.eldLanguageValid
                    // 判断 attrIEP 是否是 true，如果 eld 展示并且符合校验规则
                    if (eldValid || !this.isEmptyOrBlank(language)) {
                        return { danger: false, value: language }
                    } else {
                        return { danger: true, value: 'Please Complete' }
                    }
                }
                // 没有值
                return { danger: false, value: language }
            }
        },
        // 获取指定属性的值
        getAttrValue () {
            return function (child, attrName) {
                // 不存在返回空
                if (!child || !attrName) {
                    return ''
                }
                // 属性列表
                let attrs = child.attrs
                if (!attrs) {
                    return ''
                }
                // 匹配到的属性值
                let matchValues = null
                // 遍历属性列表
                attrs.forEach(attr => {
                    // 匹配属性名称
                    if (attr && attr.name && attr.name.trim().toLowerCase() === attrName.trim().toLowerCase()) {
                        // 属性值
                        let attrValues = attr.values
                        if (attrValues && attrValues.length > 0) {
                            matchValues = attrValues
                        }
                    }
                })
                // 如果有属性值，以逗号分割
                if (matchValues) {
                    return matchValues.join(', ')
                }
                // 没有值
                return ''
            }
        }
    },

    methods: {
        // 判断属性是否是 true
        isEmptyOrBlank (s) {
          return !s || s.trim() === ''
        },
        isTrue (value) {
            if (this.isEmptyOrBlank(value)) {
                return false
            }

            // 拿到的 value 有可能是 yes,yes
            // 由于导入出现的问题导致的,后续会对导入进行解决
            // 对数据进行去重, 如果数据是大于 1 的,那么可能出现了未知错误,这个先不处理
            const valueList = value.split(';').map(str => str.trim()).filter((value, index, self) => self.indexOf(value) === index)

            if (valueList.length === 1) {
                return value.toLowerCase() === 'true' || value === '1' || value.toLowerCase() === 'yes'
            }

            return false
        },
        // 添加小孩
        createChild () {
            this.currentEditChild = null
            // 打开编辑弹窗
            this.editChildDialogVisible = true
        },

        // 点击管理小孩的事件
        clickManager () {
            // 添加 Unit Planner 设置的埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_pop_manage_demogra')
        },
        // 编辑小孩
        editChild (child) {
            this.currentEditChild = child
            // 打开编辑弹窗
            this.editChildDialogVisible = true
        },

        // 删除小孩
        deleteChild (child) {
            this.$confirm('Are you sure you want to delete this child?', 'Confirm', {
                confirmButtonText: 'Confirm',
                cancelButtonText: 'Cancel',
            }).then(() => {
                this.$axios.delete($api.urls(null, null, null, null, child.id).deleteChild).then(() => {
                    this.$message({
                        type: 'success',
                        message: 'Delete successfully!'
                    })
                    this.listChildren()
                }).catch(error => {
                    console.log(error)
                    this.$message.error(error.response.data.error_message)
                })
            }).catch(() => {
            })
        },

        // 更新小孩后刷新
        updateChild () {
            this.listChildren()
        }
    }
}
</script>

<style lang="less" scoped>

/deep/ .el-table .el-table__header .cell {
  text-overflow: clip;
  word-break: break-word;
}

.ellipsis {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 显示两行 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
    word-break: keep-all;
}

.gap-5 {
    gap: 5px;
    display: flex;
    align-items: center;
}
</style>