<template>
  <div>
    <el-dialog
      class="lg-scrollbar-hidden"
      title="Edit Child's Info"
      top="2vh"
      @close="closeEditChildInfoDialog"
      :visible.sync="editChildInfoVisible"
      width="765px">
      <!-- 小孩信息 -->
      <div class="display-flex align-items" style="gap: 20px">
        <!-- 小孩头像 -->
        <div class="position-relative">
          <img :src="childInfo.avatarUrl" alt="avatar" class="avatar-size" style="border-radius: 10px">
          <!-- 是否私有 -->
          <div class="position-absolute avatar-private display-flex align-items justify-content">
            <el-checkbox v-model="isPrivate">
              <span class="lg-color-white">Private</span>
            </el-checkbox>
          </div>
        </div>
        <!-- 基本信息 -->
        <div>
          <el-form label-position="top" :inline="true" label-width="80px" ref="childInfoRef" :model="child" :rules="childInfoFormRules">
            <el-form-item label="First Name" prop="firstName">
              <el-input v-model="child.firstName"></el-input>
            </el-form-item>
            <el-form-item label="Middle Name">
              <el-input v-model="child.middleName" placeholder="Middle Name"></el-input>
            </el-form-item>
            <el-form-item label="Last Name" prop="lastName">
              <el-input v-model="child.lastName"></el-input>
            </el-form-item>
            <el-form-item label="Date of Birth" prop="birthDate">
              <el-date-picker
                :clearable="false"
                v-model="child.birthDate"
                type="date"
                format="MM/dd/yyyy"
                value-format="MM/dd/yyyy"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="Gender" class="gender-form-item" prop="gender">
              <div class="gender-style">
                <el-radio-group v-model="child.gender">
                  <el-radio label="MALE">{{ $t('loc.male') }}</el-radio>
                  <el-radio label="FEMALE">{{ $t('loc.female') }}</el-radio>
                  <el-radio label="NONBINARY">{{ $t('loc.nonbinary') }}</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 附加信息 -->
      <div class="additional-info display-flex justify-content lg-margin-top-24">
        <lg-tabs :tabs='tabs' value="additional"></lg-tabs>
      </div>
      <div class="add-margin-t-24 additional-list lg-scrollbar-hidden">
        <el-skeleton :rows="18" animated :loading="getAttrLoading">
          <el-collapse v-model="activeItem">
            <el-collapse-item :name="index" class="custom-collapse-item" v-for="(item, index) in attrGroups"
                              :key="index">
              <template slot="title">
                <span class="display-inline-block lg-margin-left-24 font-size-16 font-bold">{{ item.name }}</span>
              </template>

              <div class="additional-topic" v-for="(topic, index) in item.attrs" :key="index">
                <!-- 题目 -->
                <div>
                  <!-- 序号 -->
                  <span class="display-inline-block font-bold" v-if="topic.name !== 'Comments' && !topic.isHide && topic.name !== 'Place of Origin'">
                    {{ topic.no }}. {{ topic.description }}
                    <span v-show="topic.required"
                          class="color-text-danger display-inline-block lg-margin-left-4">*</span>
                    <span class="lg-margin-left-4 font-normal" v-show="topic.name !== 'IEP Goal'">{{ transformAttrNameToLower(topic.name) }}</span>
                  </span>
                </div>
                <!-- 填充内容 -->
                <div class="answer-type" :style="{ 'margin-bottom': (index !== item.attrs.length - 1) ? '16px' : '' }">
                  <div v-if="topic.typeValue === additionalType.INPUT && topic.name !== 'Comments' && !topic.isHide">
                    <div v-if="topic.name !== 'IEP Goal'">
                      <el-input :class="{ 'error-input-border': topic.error }" v-model="topic.values" :placeholder="transformAttrNameToLower(topic.displayName)"/>
                    </div>
                    <el-input v-if="topic.name === 'IEP Goal' && !topic.isHide" type="textarea" :autosize="{ minRows: 2, maxRows: 3 }" v-model="topic.values" placeholder="Please enter child's IEP goals here..."/>
                  </div>
                  <div v-else-if="topic.typeValue === additionalType.RADIO">
                    <el-radio-group v-if="topic.valueList && topic.valueList.length <= 3 && !topic.isHide" v-model="topic.values" :disabled="topic.name === 'ELD'" @input="handleChangeValue(topic)">
                      <el-radio v-for="(option, index) in topic.valueList" :key="index" :label="option.name">
                        {{ option.name }}
                      </el-radio>
                    </el-radio-group>
                    <div v-if="topic.name === 'Special education eligibility' && !topic.isHide">
                      <el-select v-model="topic.values" class="w-full" placeholder="Please Select">
                        <el-option
                            v-for="option in topic.valueList"
                            :key="option.name"
                            :label="option.name"
                            :value="option.name">
                        </el-option>
                      </el-select>
                      <el-input v-if="topic.values && topic.values.length > 0" type="textarea" :autosize="{ minRows: 2, maxRows: 3 }" class="lg-margin-bottom-16 add-margin-t-8" v-model="commentsContent" placeholder="Please provide more specific details about the child's unique needs and challenges for tailored support."/>
                    </div>
                  </div>
                  <div v-if="topic.typeValue === additionalType.SELECT && !topic.isHide">
                    <el-select v-model="topic.values" class="w-full" multiple placeholder="Please Select" @change="handleChange(topic)">
                      <el-option
                        v-for="option in topic.valueList"
                        :key="option.name"
                        :label="option.name"
                        :value="option.name">
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <!-- 国家和州信息需要特殊处理 -->
                <div style="margin-bottom: 16px; margin-top: 16px">
                  <span class="display-inline-block font-bold" v-if="topic.name === 'Language'">
                        {{ $t('loc.childOrigin')}}
                     <span class="lg-margin-left-4 font-normal" > {{ $t('loc.palceOrigin')}} </span>
                  </span>
                </div>
                <!-- 需要在 Language 下添加国家和州信息 -->
                <!-- 国家和州的下拉框 -->
                <div v-if="topic.name === 'Language'" style="display: flex; align-items: center; margin-bottom: 16px" class="answer-type">
                  <!-- 国家选择框 -->
                  <el-select clearable v-model="selectedCountry" filterable placeholder="Select Country" @change="onCountryChange" class="w-full">
                    <el-option v-for="country in countries" :key="country.code" :label="country.name" :value="country.name">
                    </el-option>
                  </el-select>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-skeleton>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="disAppearEditInfoDialog">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="updateChild" :loading="loading">{{ $t('loc.submit') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import LgTabs from '@/components/LgTabs.vue'
import Country from '@/assets/data/country.json'
// 附加属性类型
const additionalType = {
  INPUT: 'TEXT_FIELD', // 输入框
  RADIO: 'SINGLE_CHOICE', // 单选框
  SELECT: 'MULTIPLE_CHOICES' // 下拉选择框
}
export default {
  name: 'EditChildInfo',
  // eslint-disable-next-line vue/no-unused-components
  components: { LgTabs, Country },
  props: {
    groupId: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      editChildInfoVisible: false, // 是否显示编辑小孩信息弹窗
      childInfo: {}, // 小孩信息
      child: {},
      isPrivate: true, // 小孩头像是否私有
      activeItem: [0, 1, 2], // 当前激活的选项
      attributes: [], // 小孩属性列表
      attrGroups: [], // 小孩属性组
      displayAttrNames: ['Hispanic', 'Race', 'Language', 'ELD', 'IEP/IFSP', 'Adaptations', 'Special education eligibility', 'Comments', 'IEP Goal'], // 显示的属性名称列表
      iepAttrNames: ['Adaptations', 'Special education eligibility', 'Comments', 'IEP Goal'],
      loading: false, // 是否正在加载
      additionalType, // 附加属性类型
      showIepAttrs: false, // 是否显示 IEP 相关属性
      getAttrLoading: false, // 骨架屏 Loading 效果
      executeUpdate: true, // 是否可以执行更新操作
      childInfoFormRules: {}, // 表单校验规则
      needAttributes: ['IEP/IFSP', 'Language', 'Adaptations', 'ELD', 'IEP Goal', 'Comments', 'Race', 'Special education eligibility', 'Hispanic', 'Place of Origin'], // 需要的属性
      attrs: [], // 属性
      iepValues: false, // IEP 的值 true Or false
      commentsContent: '', // 评论内容
      homeLanguagesOnlyIncludeEnglish: false, // 语言属性值是否只包含英语
      countries: Country.countries, // 国家列表
      selectedCountry: '', // 选中的国家
      selectedCountryTemp: '' // 选中的国家临时变量
    }
  },
  computed: {
      tabs () {
          return [
              {
                  label: this.$t('loc.basicInfo'),
                  name: 'basic',
                  disabled: true
              },
              {
                  label: this.$t('loc.unitPlannerAdditionalInformation'),
                  name: 'additional'
              }
          ] // 选项卡
      }
  },
  watch: {
    editChildInfoVisible (val) {
      if (val) {
        this.child = this.childInfo
        // 设置小孩性别大写
        this.child.gender = this.child.gender.toUpperCase()
        // 获取小孩属性
        this.getChildAttrs()
        // 初始化表单校验
        this.initFormRules()
      }
    }
  },
  methods: {
    // 关闭编辑小孩信息弹窗的回调函数，将小孩属性组清空
    closeEditChildInfoDialog () {
      this.attrGroups = []
    },
    // 选择国家时触发
    onCountryChange () {
    },
    // 更改多选属性值
    handleChange (topic) {
      // 获取小孩语言信息属性分组
      const attrGroup = this.attrGroups.filter(attr => attr.name === "Child's Language Information")[0]
      // 如果当前操作的是语言下拉选
      this.setEldValue(topic, attrGroup, false)
      // 更新组件数据
      this.$forceUpdate()
    },
    // 更改单选属性值
    handleChangeValue (topic) {
      // 如果当前操作的是 IEP/IFSP 属性
      if (topic.name === 'IEP/IFSP') {
        // 如果 IEP/IFSP 的值为 选项中第一个值，即为 yes 时，才会显示特殊的属性，否则隐藏
        if (topic.values === topic.valueList[0].name) {
          this.isShowSpecialAttributes(false)
        } else {
          this.isShowSpecialAttributes(true)
        }
      }
    },
    // 设置表单校验规则
    initFormRules () {
      this.childInfoFormRules = {
        firstName: [
          { required: true, message: 'Please enter first name.', trigger: 'change' }
        ],
        lastName: [
          { required: true, message: 'Please enter last name.', trigger: 'change' }
        ],
        birthDate: [
          { required: true, message: 'Please select birth date.', trigger: 'change' }
        ],
        gender: [
          { required: true, message: 'Please select the gender.', trigger: 'change' }
        ]
      }
    },
    // 关闭编辑小孩信息弹窗
    disAppearEditInfoDialog () {
      // 关闭弹窗时，应将国家和州信息清空
      this.selectedCountry = ''
      this.editChildInfoVisible = false
    },
    // 获取小孩属性
    getChildAttrs () {
      this.getAttrLoading = true
      return new Promise((resolve, reject) => {
        let params = {}
        params['childId'] = this.child.id
        params['agencyId'] = ''
        this.$axios($api.urls().getEnrollmentAttrs, { params: params }).then(res => {
          this.attrGroups = this.filterNoNeedAttributes(res)
          this.attrGroups.forEach(item => {
            item.attrs.forEach(attr => {
              // 如果当前属性是 IEP/IFSP，则判断是否显示特殊属性
              if (attr.name === 'IEP/IFSP') {
                // 如果 IEP/IFSP 的值为 YES 时，才会显示特殊的属性，否则隐藏
                if (attr.values && (attr.values === attr.valueList[0].name || attr.valueList[0].selected)) {
                  this.isShowSpecialAttributes(false)
                } else {
                  this.isShowSpecialAttributes(true)
                }
              }
              // 初始值 ELD 的值
              this.setEldValue(attr, item, true)
              // 国家和州信息特殊处理
              if (attr.name === 'Place of Origin') {
                this.selectedCountry = attr.values[0]
                this.selectedCountryTemp = attr.values[0]
              }
            })
          })
          // 移除 attrGroups 中的 Country 或 State/Province 属性
          this.attrGroups = this.attrGroups.map(item => {
            return {
              ...item,
              attrs: item.attrs.filter(attr => attr.name !== 'Place of Origin')
            }
          })
          this.parseAttrValues()
          // 停止 Loading
          this.getAttrLoading = false
          resolve()
        }).catch(error => {
          // 停止 Loading
          this.getAttrLoading = false
          console.log(error)
          this.$message.error(error.response.data.error_message)
          reject(error)
        })
      })
    },
    // 是否显示特殊属性
    isShowSpecialAttributes (isHide) {
      // 遍历属性组
      this.attrGroups.forEach(item => {
        // 遍历属性
        item.attrs.forEach(attr => {
          const specialEducationEligibility = 'Special education eligibility'
          const iepGoal = 'IEP Goal'
          const adaptations = 'Adaptations'
          // 如果属性名符合判断中的任意一个，则设置 isHide 属性根据 IEP/IFSP 的值进行显示或隐藏
          if (attr.name.toUpperCase() === specialEducationEligibility.toUpperCase() || attr.name.toUpperCase() === iepGoal.toUpperCase() || attr.name.toUpperCase() === adaptations.toUpperCase()) {
            this.$set(attr, 'isHide', isHide)
          }
        })
      })
    },
    // 设置 eld 属性值
    setEldValue (attr, item, isInit) {
      if (attr.name === 'Language') {
        // 是否选择了英语
        let isSelectedEnglish = false
        // 是否选择了其他语种
        let isSelectedOther = false
        // 初始化时，判断是否选择了英语，设置 ELD 属性值
        if (isInit) {
          // 遍历小孩属性组下的属性
          attr.valueList.forEach(value => {
            // 如果选择了英语，则 isSelectedEnglish 为 true
            if (value.name === 'English' && value.selected) {
              isSelectedEnglish = true
            } else {
              // 如果选择了其他语种，则 isSelectedOther 为 true
              if (value.selected) {
                isSelectedOther = true
              }
            }
          })
          // 如果修改小孩信息时，只要选择了除英语外的其他语种，则 ELD 属性为 Yes
          if (isSelectedOther) {
            item.attrs.forEach(attribute => {
              if (attribute.name === 'ELD') {
                this.$set(attribute, 'values', attribute.valueList[0].name)
              }
            })
          } else {
            item.attrs.forEach(attribute => {
              if (attribute.name === 'ELD') {
                // 如果没有选择语言或者只选择了语言，但是 eld 的属性值为 yes，则保持 eld 的属性值不变
                if (((!isSelectedEnglish && !isSelectedOther) || (isSelectedEnglish && !isSelectedOther)) && attribute.valueList[0].selected) {
                  this.$set(attribute, 'values', attribute.valueList[0].name)
                  return
                }
                // 如果语言未选择，并且 ELD 属性值不存在或者 ELD 属性值为空时，保持空置
                if (!isSelectedEnglish && !isSelectedOther && (attribute.values && attribute.values.length === 0) && (!attribute.valueList[0].selected && !attribute.valueList[1].selected)) {
                  return
                }
                this.$set(attribute, 'values', attribute.valueList[1].name)
              }
            })
          }
        } else {
          // 如果当前选择的语言不为空，并且包含英语语种且语言不止一种，则 ELD 属性为 Yes
          if (!attr.values || (attr.values && (attr.values.length === 0 || (attr.values.length === 1 && attr.values.includes('English'))))) {
            item.attrs.forEach(attribute => {
              if (attribute.name === 'ELD') {
                this.$set(attribute, 'values', attribute.valueList[1].name)
              }
            })
          } else {
            // 否则，其余情况都为 Yes
            item.attrs.forEach(attribute => {
              if (attribute.name === 'ELD') {
                this.$set(attribute, 'values', attribute.valueList[0].name)
              }
            })
          }
        }
      }
    },

    // 过滤不需要的属性
    filterNoNeedAttributes (allAttributes) {
      let attrGroups = []
      allAttributes.forEach(item => {
        const attrs = item.attrs.filter(attr => this.needAttributes.indexOf(attr.name) !== -1)
        let attrGroup = {
          ...item,
          attrs: attrs
        }
        if (attrs.length !== 0) {
          attrGroups.push(attrGroup)
        }
      })
      return attrGroups
    },
    // 解析属性选中值
    parseAttrValues () {
      let attrGroups = this.attrGroups
      attrGroups.forEach(attrGroup => {
        // 分组下属性
        let attrs = attrGroup.attrs
        if (!attrs) {
          return
        }
        // 遍历属性
        attrs.forEach(attr => {
          let valueList = attr.valueList
          // 文本
          if (attr.value) {
            if (attr.typeValue === this.additionalType.INPUT) {
              this.$set(attr, 'values', attr.value)
            }
          }
          if (!valueList) {
            return
          }
          let values = []
          valueList.forEach(value => {
            if (value && value.selected) {
              values.push(value.name)
            }
          })
          if (!values || values.length === 0) {
            return
          }
          // 多选
          if (attr.typeValue === this.additionalType.SELECT) {
            this.$set(attr, 'values', values)
          }
          // 单选
          if (attr.typeValue === this.additionalType.RADIO && attr.name !== 'ELD') {
            this.$set(attr, 'values', values[0])
          }
        })
      })
    },
    // 格式保存信息
    formatSaveParams () {
      // 没有学生信息
      if (!this.childInfo) {
        return
      }
      // 学生信息
      let params = {
        birthDate: this.child.birthDate,
        gender: this.child.gender,
        groupId: this.groupId,
        ...this.child
      }
      // 属性信息
      if (!this.attrGroups) {
        params['attrs'] = []
        return
      }
      // 是否是 IEP
      let isIEP = false
      // 属性值
      let attrs = []
      // 遍历属性组
      this.attrGroups.forEach(attrGroup => {
        if (!attrGroup.attrs) {
          return
        }
        // 遍历属性
        attrGroup.attrs.forEach(attr => {
          // 选择的值
          let values = attr.values
          // 如果属性有校验正则表达式，判断是否符合正则要求，如果不符合则设置 error 属性为 true
          if (attr.regex && !new RegExp(attr.regex).test(values)) {
            this.executeUpdate = false
            this.$set(attr, 'error', true)
            return
          } else {
            this.$set(attr, 'error', false)
          }
          // 要保存的属性
          let updateAttr = {
            name: attr.name,
            values: []
          }
          // 多选
          if (attr.typeValue === additionalType.SELECT && values.length > 0) {
            values.forEach(value => {
              updateAttr.values.push({
                name: value
              })
            })
          }
          // 单选
          if (attr.typeValue === additionalType.RADIO && values.length > 0) {
            updateAttr.values.push({
              name: values
            })
          }
          // 文本
          if (attr.typeValue === additionalType.INPUT && values.length > 0) {
            updateAttr.values.push({
              name: values
            })
          }
          if (!updateAttr.values || updateAttr.values.length === 0) {
            updateAttr.values = []
          }
          if (updateAttr.name === 'IEP/IFSP' && updateAttr.values.length > 0 && updateAttr.values[0].name === 'Yes') {
            isIEP = true
          }
          attrs.push(updateAttr)
          this.attrs.push(updateAttr)
        })
      })
      // 如果不是 IEP，清掉相关的属性值
      if (!isIEP) {
        attrs.forEach(attr => {
          if (this.iepAttrNames.includes(attr.name)) {
            attr.values = []
          }
        })
      }
      params['attrs'] = attrs
      return params
    },
    // 更新小孩
    updateChild () {
      this.executeUpdate = true
      // 小孩 ID
      let childId = this.childInfo.id
      if (!childId) {
        return
      }
      // 更新参数
      let params = this.formatSaveParams()
      // 如果评论不为空，则为 Comments 属性赋值
      params['attrs'].forEach(item => {
        if (item.name === 'Comments') {
          item.values = [{
            name: this.commentsContent
          }]
        }
        // 如果是语言属性
        if (item.name === 'Language') {
          // 判断语言是否为空或者只有英语
          if (item.values.length === 0) {
            params['attrs'].push({
              name: 'ELD',
              values: [{
                name: 'No'
              }]
            })
          }
          if (item.values.length === 1 && item.values[0].name === 'English') {
            params['attrs'].push({
              name: 'ELD',
              values: [{
                name: 'No'
              }]
            })
          }
        }
      })
      // 先将 params['attrs'] 中的 Place of Origin 属性删除，因为数据填充结构有问题
      params['attrs'] = params['attrs'].filter(item => item.name !== 'Place of Origin')
      // 需要将国家和州信息添加到属性中
        params['attrs'].push({
          name: 'Place of Origin',
          values: [{
            name: this.selectedCountry,
            textboxValue: null
          }]
        })
      if (this.executeUpdate) {
        this.$refs['childInfoRef'].validate((valid) => {
          if (valid) {
            this.loading = true
            return new Promise((resolve, reject) => {
              this.$axios.put('/students/' + childId + '?web=1', params).then(res => {
                this.editChildInfoVisible = false
                this.loading = false
                this.$emit('updateSuccess')
                resolve()
              }).catch(error => {
                console.log(error)
                this.$message.error(error.response.data.error_message)
                reject(error)
              })
            })
          }
        })
      }
    },
    // 将属性名转换为小写字符串
    transformAttrNameToLower (attrName) {
      return attrName && attrName.toLowerCase()
    }
  }
}
</script>

<style scoped lang="less">
.avatar-size {
  width: 156px;
  height: 156px;
}

/deep/ .el-form-item {
  margin-bottom: 5px !important;
}

/deep/ .el-form--label-top {
  .el-form-item__label {
    margin-bottom: 0 !important;
    padding: 0px !important;
  }
}

.gender-style {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: space-evenly;

  /deep/ .el-radio-group {
    display: flex;
    margin-top: 5px;
    gap: 50px;
  }
  /deep/ .el-radio {
    margin-right: 0;
  }
}

.gender-form-item {
  width: calc(100% - 202px);
}

/deep/ .el-input__inner {
  width: 170px !important;
}

/deep/ .el-date-editor.el-input {
  width: 170px !important;
}

.avatar-private {
  width: 100%;
  height: 32px;
  background: rgba(58, 63, 81, 0.7);
  border-radius: 0px 0px 10px 10px;
  bottom: 0px;
}

.additional-info {
}

/deep/ .el-collapse {
  border-bottom: transparent !important;
}

.custom-collapse-item {
  margin-bottom: 10px !important;

  /deep/ .el-collapse-item__header {
    position: relative !important;
    background: var(--color-gray-white);
  }

  /deep/ .el-collapse-item__content {
    margin: 10px 24px !important;
  }

  /deep/ .el-collapse-item__arrow {
    position: absolute !important;
    font-size: 20px;
  }
  /deep/ .el-collapse-item__wrap {
    border-bottom: transparent!important;
  }
}

.additional-list {
  max-height: 470px;
  overflow-y: auto !important;
}

.answer-type {
  margin-top: 16px;

  /deep/ .el-input__inner {
    width: 100% !important;
  }
}

.custom-radio-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.error-input-border {
  border: 1px solid var(--color-danger)!important;
  border-radius: 4px!important;
}

.error-tip {
  color: var(--color-danger);
}
/deep/ .el-form-item__label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
  gap: 5px;
}
/deep/ .el-tabs__item.is-disabled {
  cursor: not-allowed;
}

/deep/ .el-tabs__item.is-disabled:hover {
  position: relative;
}
/deep/ .el-dialog__body {
  padding: 0 20px;
  overflow: auto;
  height: calc(100% - 130px);
}
</style>
