<template>
    <!-- 周计划分享记录列表 -->
    <div class="display-flex flex-direction-col h-full" v-loading="pageLoading">
        <div class="bg-white flex-auto lg-padding-20 lg-box-shadow lg-border-radius-8">
            <el-table
                v-if="sharedPlans.length > 0"
                ref="sharedPlansTable"
                :data="sharedPlans"
                row-key="id"
                header-row-class-name="table-header"
                :row-class-name="tableRowBgColor"
                @sort-change="sortChange"
                lazy
                :load="getSharedRecordPlans"
                class="lg-form"
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                @row-click="viewDetail">
                <!-- 创建人 -->
                <el-table-column
                  :label="$t('loc.assignedBy2')"
                  sortable="custom"
                  width="145"
                  class-name="creator-col"
                  :column-key="'CREATE_SHARE_USER'">
                  <template slot-scope="scope">
                    <div class="overflow-ellipsis creator-name" :title="scope.row.createUsername | formatUserName">{{scope.row.createUsername | formatUserName}}</div>
                  </template>
                </el-table-column>
                <!-- 周计划作者 -->
                <el-table-column
                    v-if="!isTeacherShared"
                    :label="$t('loc.planCreator')"
                    sortable="custom"
                    width="110"
                    :column-key="'SHARE_USER'">
                    <template slot-scope="scope">
                        <!-- 跳转周计划老师分享列表 -->
                        <el-button
                            size="medium"
                            type="text"
                            class="overflow-ellipsis w-full"
                            :title="scope.row.shareUser.displayName | formatUserName"
                            @click.native.stop="viewTeacherShared(scope.row.shareUser)">{{scope.row.shareUser.displayName | formatUserName}}
                        </el-button>
                    </template>
                </el-table-column>
                <!-- 周计划 -->
                <el-table-column
                  width="165"
                  :label="$t('loc.shadowedPlanner')">
                  <template slot-scope="scope">
                    <span v-if="scope.row.shareType == 'PART' || !scope.row.shareType">{{removeYear(scope.row.fromDate)}} - {{removeYear(scope.row.toDate)}}</span>
                    <span v-if="scope.row.shareType == 'ALL'">{{$t('loc.entireSet')}}</span>
                  </template>
                </el-table-column>
                <!-- 周计划总数 -->
                <el-table-column
                  :label="$t('loc.totalCount')"
                  width="115">
                  <template slot-scope="scope">
                    <span>{{scope.row.planCount}}</span>
                  </template>
                </el-table-column>
                <!-- 周计划主题 -->
                <el-table-column
                  :label="$t('loc.plan4')">
                  <template slot-scope="scope">
                      <span class="overflow-ellipsis w-full" :title="scope.row.theme">{{scope.row.theme}}</span>
                  </template>
                </el-table-column>
                <!-- 分享事件 -->
                <el-table-column
                  :label="$t('loc.assignedTime')"
                  prop="sharedAtUtc"
                  width="145"
                  :formatter="formatTime">
                </el-table-column>
                <!-- 被分享人 -->
                <el-table-column
                  :label="$t('loc.assignedTo')"
                  width="130">
                  <template slot-scope="scope">
                  <el-popover
                    placement="left"
                    width="315"
                    offset="-80"
                    trigger="click">
                    <div>
                      <div style="font-size: 19px;text-align: center;font-weight: bold;color: black">
                        <div>{{ $t('loc.assignedTo') }} ({{scope.row.sharedUsers.length}})</div>
                      </div>
                      <div class="lg-scrollbar-show" style="border: 1px solid rgb(228,234,236);mso-cellspacing: 1px;margin-top: 5px;border-radius: 3px;max-height: 600px;overflow-y: auto;">
                        <div v-for="item in scope.row.sharedUsers" style="display: flex;justify-content: flex-start;border-bottom: 1px solid rgb(228,234,236);padding: 10px;font-size: 15px;color: black">
                          <div style="display: inline-block;width: 45px;height: 45px;border-radius: 100%;background-color: coral;overflow: hidden">
                            <img style="width: 100%;height: 100%;" :src="item.avatarUrl">
                          </div>
                          <div>
                            <div style="margin-left: 10px;margin-top: 12px;">
                              <span>{{item.displayName | formatUserName}}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <span class="overflow-ellipsis w-full" :title="scope.row.sharedUserNames"></span>
                    <el-button @click.stop="" style="padding: 10px 0px;font-size: 13px;border: 0;background-color: rgb(235,248,248);color: rgb(67,195,198);background: inherit"  slot="reference">{{$t('loc.recipients')}} ({{scope.row.sharedUsers.length}})</el-button>
                  </el-popover>
                  </template>
                </el-table-column>
                <!-- 打开率 -->
                <el-table-column
                    :label="$t('loc.openRate')"
                    width="110">
                    <template slot-scope="scope">
                        <!-- 跳转查看分享阅读详情 -->
                        <el-button
                            type="text"
                            :disabled="scope.row.planCount > 1 || scope.row.planCount === 0"
                            @click="viewOpenRate(scope.row.shareId, scope.row.planId)"><span style="font-size: 14px;">{{scope.row.percent}}</span>
                        </el-button>
                    </template>
                </el-table-column>
                <!-- 取消分享/查看详情 -->
                <el-table-column width="60" fixed="right">
                    <template slot-scope="scope">
                      <span v-if="!isTeacherShared && !scope.row.isChildren && scope.row.canDelete"
                            @click.stop="recallShadowed(scope.row.shareId)">
                            <el-tooltip placement="top" effect="dark" :content="$t('loc.plan14')">
                              <el-link :underline="false">
                                <i style="font-size: 24px;" class="lg-icon lg-icon-efresh-left lg-pointer"></i>
                              </el-link>
                            </el-tooltip>
                      </span>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div v-if="sharedPlans.length > 0 && total > 10" class="table-footer flex-none">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next"
                    :total="total">
                </el-pagination>
            </div>
          <!-- 空布局 -->
          <div v-if="!pageLoading && sharedPlans.length === 0 "  class="flex-column-center w-full white-background add-padding-t-16 position-relative h-full">
            <img src="@/assets/img/dll/no_record.png">
            <div class="text-center add-margin-t-8 font-size-14 el-icon-question-color" >{{$t('loc.plan36')}}</div>
          </div>
        </div>
    </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'

export default {
    name: 'SharedPlanTable',

    props: {
        loader: {
            type: Function
        }
    },

    created () {
        this.shareUserId = this.$route.params.teacherId
        this.loadData()
        this.lang = tools.localItem('NG_TRANSLATE_LANG_KEY')
        window.addEventListener('resize', this.reCalTable)
    },

    data () {
        return {
            currentPage: 1, // 当前页
            pageSize: 10, // 每页展示记录条数
            total: 0, // 记录总数
            sortField: '', // 拍序列
            sortOrder: '', // 排序规则
            shareUserId: '', // 分享人 id(老师分享界面接口参数)
            tableLoading: false, // 表格 loading
            pageLoading: false, // 页面 loading
            sharedPlans: [], // 表格数据
            lang: 'en-US' // 语言
        }
    },

    computed: {
        isTeacherShared () {
            if (this.$route.name === 'shared-plan') {
                return true
            }
            return false
        }
    },
    methods: {
      // 重新计算表格高度
      reCalTable () {
        this.$nextTick(() => {
          this.$refs.sharedPlansTable && this.$refs.sharedPlansTable.doLayout()
          let tableDom = this.$el.querySelector('.lg-form')
          let dom = this.$el.querySelector('.lg-form .el-table__fixed-right')
          if (dom) {
            let table = this.$el.querySelector('.lg-form .el-table__body-wrapper')
            if (table.scrollHeight > table.offsetHeight) {
              dom.style.right = '6px'
            }
            dom.style.height = (tableDom.clientHeight) + 'px'
            let body = dom.querySelector('.el-table__fixed-body-wrapper')
            if (body) {
              body.style.height = (table.clientHeight - 6) + 'px'
            }
          }
        })
      },

      // 加载分享记录数据
      loadData () {
          this.pageLoading = true
          let params = {
              page: this.currentPage,
              pageSize: this.pageSize,
              shareUserId: this.shareUserId
          }

          // 选择了排序，则按照字段排序
          if (this.sortField && this.sortOrder) {
              params['orderKey'] = this.sortField
              params['orderType'] = this.sortOrder
          }

          this.loader(params).then(data => {
              this.tableLoading = false
              this.pageLoading = false
              if (data.items && data.items.length > 0) {
                  for (let i = 0; i < data.items.length; i++) {
                      let item = data.items[i]
                      // 遍历分享记录，添加子记录标识
                      if (item.planCount > 1) {
                          item.hasChildren = true
                      } else {
                          item.hasChildren = false
                      }
                      // 分享记录下包含两个以上，theme 追加省略
                      if (item.planCount > 2) {
                          item.theme += '...'
                      }
                      // 添加被分享人名属性
                      item.sharedUserNames = this.sharedUserNames(item.sharedUsers)
                  }
              }
              this.sharedPlans = data.items ? data.items : []
              this.reCalTable()
              this.total = data.total ? data.total : 0
          }).catch(error => {
              this.tableLoading = false
            this.pageLoading = false
          })
      },

      handleSizeChange (size) {
          this.pageSize = size
          this.currentPage = 1
          // 刷新数据
          this.loadData()
      },

      handleCurrentChange (page) {
          this.currentPage = page
          // 刷新数据
          this.loadData()
      },

      sortChange (params) {
          let order = params.order
          if (!order) {
              this.sortField = undefined
              this.sortOrder = undefined
          } else {
              let columnKey = params.column.columnKey
              this.sortField = columnKey || params.column.label
              this.sortOrder = params.order === 'ascending' ? 'ASC' : 'DESC'
          }
          // 刷新数据
          this.loadData()
      },

      // 去除日期的年、时、分、秒部分
      removeYear (date) {
          if (!date) {
              return date
          }
          return this.$moment(date).format('MM/DD')
      },

      // 去除日期的年部分
      formatTime (row, column, cellValue, index) {
          if (!cellValue) {
              return cellValue
          }
          return this.$moment.utc(cellValue).local().format('MM/DD hh:mm A')
      },

      // 存在子记录，相应列添加背景色
      tableRowBgColor ({ row }) {
          if (row.hasChildren) {
              return 'has-children-row'
          }
          if (row.isChildren) {
              return 'is-children-row'
          }
          return ''
      },

      // 获取分享记录下周计划列表
      getSharedRecordPlans (tree, treeNode, resolve) {
          let params = {
              shareId: tree.id,
              pageSize: 50
          }
          this.$axios.post($api.urls().listSharedRecordPlans, params)
              .then(response => {
                  // 分享记录下周计划列表
                  let childrenItems = []
                  if (response.items.length > 0) {
                      // 向子记录中添加父记录内容
                      response.items.map((item) => {
                          childrenItems.push(Object.assign({}, item, {
                              'shareUser': tree.shareUser,
                              'planCount': 1,
                              'sharedAtUtc': tree.sharedAtUtc,
                              'sharedUsers': tree.sharedUsers,
                              'sharedUserNames': tree.sharedUserNames,
                              'comment': tree.comment,
                              'isChildren': true
                          }))
                      })
                  }
                  resolve(childrenItems)
              }).catch(error => {})
      },

      // 取消周计划分享
      recallShadowed (shareId) {
          this.$analytics.sendEvent('web_weekly_plan_virtual_click_recall')
          this.$confirm(this.$t('loc.sureRecallShadowed'), this.$t('loc.cfm'), {
              confirmButtonText: this.$t('loc.confirm'),
              cancelButtonText: this.$t('loc.cancel'),
              customClass: 'lg-message-box',
              confirmButtonClass: 'el-button--danger'
          }).then(() => {
              LessonApi.recallShadowed({}, {
                  shareId: shareId
              }).then(response => {
                  this.$message({
                  type: 'success',
                  message: this.$t('loc.recalledSuccessfully')
                  })
                  this.loadData()
              })
          }).catch(() => {
          })
      },

      // 拼接被分享人名
      sharedUserNames (sharedUsers) {
          let sharedUserNames = ''
          sharedUserNames = sharedUsers.map(function (sharedUser) {
              return sharedUser.displayName
          }).join(', ')
          return sharedUserNames
      },

      // 查看周计划 Detail 跳转
      viewDetail (row) {
        this.$analytics.sendEvent('web_weekly_plan_virtual_click_detail')
        if (row.planCount === 1) {
          this.$router.push({
            name: 'shared-plan-detail',
            params: {
              planId: row.planId
            },
            query: {
              shareId: row.shareId,
              param: 'detail'
            }
          })
        }
      },

      // 查看周计划 Open Rate 跳转
      viewOpenRate (shareId, planId) {
        this.$analytics.sendEvent('web_weekly_plan_virtual_click_open_rate')
          this.$router.push({
              name: 'view-plan',
              params: {
                  planId: planId
              },
              query: {
                  shareId: shareId,
                  param: 'openRate'
              }
          })
      },

      // 查看老师分享列表
      viewTeacherShared (shareUser) {
          this.$analytics.sendEvent('web_weekly_plan_virtual_click_creator')
          this.$router.push({
              name: 'shared-plan',
              params: {
                  shareUserId: shareUser.id
              }
          })
      },

    // 判断是否来源于iPad
    isComeFromIPad () {
      return tools.isComeFromIPad()
    }
  }
}
</script>

<style>
.el-table .has-children-row {
    background: #FAFAFA;
}
.el-table .is-children-row {
    background: #FFFFFF;
}
</style>
<style lang="less" scoped>
@media only screen and (max-width:1199px){
  //ipad
  .admin-shared-table {
    margin: 0 10px;
  }
  .empty-area {
    display: flex;
    flex-direction: column;
    padding: 0;
    align-items: center;
    justify-content: center;
  }
  .table-footer {
    padding: 10px 0;
    text-align: right;
  }
  /deep/ .table-header th {
    font-weight: bold;
    background-color: #fafafa;
  }
  /deep/ .el-button.is-disabled {
    color: #606266 !important;
    opacity: 1 !important;
  }
  /deep/ .creator-col > .cell {
    display: flex;
    align-items: center;
    .el-table__expand-icon {
      flex: none;
    }
    .creator-name {
      flex: auto;
      min-width: 0;
    }
  }
  /deep/ .el-table__expand-icon .el-icon-arrow-right {
    font-size: 14px;
  }
  /deep/ .el-table table{
    width: 0px;
  }
  .table-header{
    color: #323338;
  }
}

@media only screen and (min-width:1200px){
  //web
  .admin-shared-table {
    margin: 0 30px;
  }
  .empty-area {
    display: flex;
    flex-direction: column;
    padding: 50px 0;
    align-items: center;
    justify-content: center;
  }
  .table-footer {
    padding: 10px 0;
    text-align: right;
  }
  /deep/ .table-header th {
    font-weight: bold;
    background-color: #fafafa;
  }
  /deep/ .el-button.is-disabled {
    color: #606266 !important;
    opacity: 1 !important;
  }
  /deep/ .creator-col > .cell {
    display: flex;
    align-items: center;
    .el-table__expand-icon {
      flex: none;
    }
    .creator-name {
      flex: auto;
      min-width: 0;
    }
  }
  /deep/ .el-table__expand-icon .el-icon-arrow-right {
    font-size: 18px;
    font-weight: 600;
  }
  /deep/ .el-table table{
    width: 0px;
  }
}
/deep/ .el-table thead {
  color: #323338;
}
</style>
