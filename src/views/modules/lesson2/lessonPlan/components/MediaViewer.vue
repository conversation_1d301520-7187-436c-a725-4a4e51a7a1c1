<template>
  <div :style="{'aspect-ratio': ratio || '16/9'}" class="media-viewer display-flex align-items">
    <!-- 图片 -->
    <el-image
      :src="url" :fit="fit|| 'cover'"
      :preview-src-list="preview && imgList ? imgList : []"
      v-if="isImage">
      <div slot="error" class="image-slot h-full display-flex align-items justify-content">
        <svg t="1641387794918" class="icon" viewBox="0 0 1067 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9141" width="56" height="56"><path d="M1024.517478 170.368c-9.472-10.24-22.528-16.256-36.48-16.768l-397.056-15.616-30.08 66.304 41.344 124.8L527.493478 471.04l30.464 114.304 75.392 98.048 109.312-100.992c5.12-4.736 11.904-7.168 18.816-6.912 6.912 0.256 13.568 3.328 18.176 8.448L920.197478 736c7.168 7.808 8.96 19.2 4.608 28.8-4.48 9.6-14.208 15.616-24.832 15.232l-385.152-15.104-19.2 55.168 20.864 49.792L959.237478 887.04c13.952 0.512 27.392-4.48 37.632-13.952s16.256-22.528 16.768-36.48l24.704-628.608c0.64-13.952-4.352-27.392-13.824-37.632zM739.973478 458.624c-43.392-1.664-77.184-38.272-75.52-81.664 1.664-43.392 38.272-77.184 81.664-75.52s77.184 38.272 75.52 81.664c-1.664 43.392-38.272 77.184-81.664 75.52zM454.533478 820.992l13.312-57.856L155.141478 784.64c-10.624 0.768-20.608-4.992-25.344-14.464-4.736-9.472-3.2-20.864 3.712-28.928l231.68-266.112c4.864-5.504 11.776-8.832 19.072-8.96 7.296-0.128 14.464 2.688 19.584 8.064l77.568 80.896-33.408-86.272 58.88-148.864-54.272-119.168 22.656-68.992L48.773478 161.28c-28.928 2.048-50.688 27.008-48.64 55.936L43.397478 844.928c0.896 13.824 7.296 26.752 17.792 35.84 10.496 9.088 24.192 13.696 38.016 12.8l380.8-26.24-25.472-46.336z" p-id="9142" fill="#bfbfbf"></path></svg>
      </div>
    </el-image>
    <!-- 添加查看按钮 -->
    <span v-if="showViewButton && isImage"
         class="view-button">
      View
    </span>
    <!-- 添加特殊课程标签 -->
    <span v-if="adaptedLesson" class="special-tag">
      {{ $t('loc.adaptedLesson') }}
    </span>
    <el-image
      :class="{'lg-pointer': preview}"
      :src="snapshotUrl" :fit="fit|| 'cover'"
      @click="playVideo(url)"
      v-if="isVideo && snapshotUrl">
      <div slot="error" class="image-slot h-full display-flex align-items justify-content">
        <svg t="1641387794918" class="icon" viewBox="0 0 1067 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9141" width="56" height="56"><path d="M1024.517478 170.368c-9.472-10.24-22.528-16.256-36.48-16.768l-397.056-15.616-30.08 66.304 41.344 124.8L527.493478 471.04l30.464 114.304 75.392 98.048 109.312-100.992c5.12-4.736 11.904-7.168 18.816-6.912 6.912 0.256 13.568 3.328 18.176 8.448L920.197478 736c7.168 7.808 8.96 19.2 4.608 28.8-4.48 9.6-14.208 15.616-24.832 15.232l-385.152-15.104-19.2 55.168 20.864 49.792L959.237478 887.04c13.952 0.512 27.392-4.48 37.632-13.952s16.256-22.528 16.768-36.48l24.704-628.608c0.64-13.952-4.352-27.392-13.824-37.632zM739.973478 458.624c-43.392-1.664-77.184-38.272-75.52-81.664 1.664-43.392 38.272-77.184 81.664-75.52s77.184 38.272 75.52 81.664c-1.664 43.392-38.272 77.184-81.664 75.52zM454.533478 820.992l13.312-57.856L155.141478 784.64c-10.624 0.768-20.608-4.992-25.344-14.464-4.736-9.472-3.2-20.864 3.712-28.928l231.68-266.112c4.864-5.504 11.776-8.832 19.072-8.96 7.296-0.128 14.464 2.688 19.584 8.064l77.568 80.896-33.408-86.272 58.88-148.864-54.272-119.168 22.656-68.992L48.773478 161.28c-28.928 2.048-50.688 27.008-48.64 55.936L43.397478 844.928c0.896 13.824 7.296 26.752 17.792 35.84 10.496 9.088 24.192 13.696 38.016 12.8l380.8-26.24-25.472-46.336z" p-id="9142" fill="#bfbfbf"></path></svg>
      </div>
    </el-image>
    <!-- 视频缩略图，后台未生成 -->
    <video class="hide-control" :src="url" controls :style="{objectFit: fit||'cover'}" v-if="isVideo && !snapshotUrl" ref="videoElement"/>
    <!-- 播放按钮 -->
    <div class="video-player" :class="{'lg-pointer': preview}" v-if="isVideo || isExternalMediaVideo" @click="playVideo(url)">
      <img src="@/assets/img/lesson2/plan/video_play.png"/>
    </div>
    <!-- 视频弹窗 -->
    <el-dialog custom-class="video-modal" :visible.sync="videoModal" @close="closeVideo">
        <video :src="videoUrl" controls autoplay class="video" ref="videoPlay" style="max-height: 100vh;"></video>
    </el-dialog>
    <!-- 语音条 -->
    <a href="javascript:;" @click="playAudio()" v-if="isAudio">
      <div class="audio-item">
        <div class="audio-img" :class="{'audio-img-run' : audioRunning}"></div>
        <span class="font-size-16 voice-time">{{duration}}</span>
      </div>
    </a>
    <!-- 语音 -->
    <audio :src="url" ref="audioEle" class="note-audio" style="display:none" v-if="isAudio"  @ended="playAudioEnd()">
        Faild
    </audio>
  </div>
</template>

<script>
import tools from '@/utils/tools'
export default {
  name: 'MediaViewer',
  props: ['url', 'snapshotUrl', 'imgList', 'fit', 'ratio', 'preview', 'duration','adaptedLesson', 'showViewButton'],
  data () {
    return {
      videoModal: false,
      videoUrl: '',
      audioRunning: false,
      audioId: ''
    }
  },

  created () {
    this.audioId = tools.uuidv4()
  },

  computed: {
    isImage () {
      // 因为 vimeo 视频的封面链接后缀不带 .jpeg|.png|.jpg 所以采用前缀匹配
      return this.url && /.+(\.jpeg|\.png|\.jpg|\.webp)/i.test(this.url) || /^https:\/\/i.vimeocdn.com\/video\/.*/.test(this.url)
    },
    isVideo () {
      return this.url && /.+(\.mp4)/i.test(this.url)
    },
    isAudio () {
      return this.url && /.+(\.aac)/i.test(this.url)
    },
    // 因为youtube/vimeo此处展示的是图片，所以此处用来判断是否要加上播放图标
    isExternalMediaVideo(){
      return  this.url && /^https:\/\/i3.ytimg.com\/vi\/.*/.test(this.url)
              || this.url && /^https:\/\/i.vimeocdn.com\/video\/.*/.test(this.url)
    }
  },

  beforeDestroy() {
    this.$refs.videoElement && this.$refs.videoElement.pause();
  },

  methods: {
    playVideo (url) {
      if (!this.isVideo || !this.preview) {
        return
      }
      // 获取所有观察记录语音
      let audioEles = document.getElementsByClassName('note-audio')
      // 暂停所有语音
      for (let i = 0; i < audioEles.length; i++) {
        let audioEle = audioEles[i]
        audioEle.pause()
        audioEle.currentTime = 0
      }
      this.videoUrl = url
      this.videoModal = true
    },

    closeVideo () {
      this.videoUrl = ''
    },

    playAudio () {
      this.audioRunning = !this.audioRunning
      this.$nextTick(() => {
        // 获取所有观察记录语音
        let audioEles = document.getElementsByClassName('note-audio')
        // 暂停所有语音
        for (let i = 0; i < audioEles.length; i++) {
          let audioEle = audioEles[i]
          audioEle.pause()
          audioEle.currentTime = 0
        }
        // 播放当前语音
        let audioPlayer = this.$refs.audioEle
        if (this.audioRunning) {
          audioPlayer.play()
        }
      })
    },

    playAudioEnd () {
      this.audioRunning = false
    }
  }
}
</script>

<style scoped lang="less">
.media-viewer {
  position: relative;
  & > * {
    height: 100%;
    width: 100%;
  }
}

.hide-control::-webkit-media-controls {
   display:none !important;
}

.video-player {
  img {
    width: 32px;
  }
  position: absolute;
  top: calc(50% - 16px);
  left: calc(50% - 16px);
}

.special-tag {
  position: absolute;
  top: 0px; /* 调整垂直位置 */
  left: 0px; /* 调整水平位置 */
  width: fit-content; /* 设置宽度 */
  height: auto; /* 设置高度 */
  line-height: 24px; /* 设置行高 */
  text-align: center; /* 文字居中 */
  border-radius: 0 0 8px 0; /* 设置边框半径 */
  //padding: 6px 8px; /* 设置内边距 */
  background-color: #878BF9; /* 设置背景颜色 */
  color: #ffffff; /* 设置文字颜色 */
  font-size: 12px; /* 设置字体大小 */
  font-family: 'Inter', sans-serif; /* 设置字体为Inter */
  font-weight: 600; /* 设置字体粗细 */
}

.view-button {
  position: absolute;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  top: 50%;
  left: 50%;
  width: fit-content; /* 设置宽度 */
  transform: translate(-50%, -50%);
  background-color: #10B3B7;
  color: white;
  padding-left: 24px;
  padding-right: 24px;
  border-radius: 50px;
  font-size: 14px;
  cursor: pointer;
  z-index: 10;
  text-transform: capitalize;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  visibility: hidden; /* 默认隐藏 */
  transition: visibility 0.3s ease; /* 添加过渡效果 */
}

.media-viewer:hover .view-button {
  visibility: visible; /* 悬停时显示 */
}
</style>