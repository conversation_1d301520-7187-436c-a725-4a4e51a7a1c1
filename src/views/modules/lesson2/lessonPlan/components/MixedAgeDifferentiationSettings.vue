<template>
  <div>
    <!--Assign Teacher Groups 的弹窗-->
    <el-dialog :visible.sync="mixedAgeDifferentiationShow"
               :show-close=false
               :before-close="clearOldData"
               custom-class="assign-teacher-dialog"
               :append-to-body="true"
               @closed="handleTeacherGroups"
               :modal-append-to-body="true"
               :close-on-click-modal="false"
               width="1017px">
      <!--弹窗的头部信息-->
      <div slot="title" class="dialog-title">
        <div class="dialog-title-content display-flex align-items" style="gap: 5px">
          <span>{{ $t('loc.mixAgeGroup2') }}</span>
          <!-- 隐藏解释 -->
          <el-tooltip v-if="showAIGroup" effect="dark" :content="$t('loc.unitPlannerAssignTeacherGroupTitle')"
                      placement="bottom"
                      popper-class="body-text">
            <i class="lg-icon lg-icon-info font-size-24 font-thin"></i>
          </el-tooltip>
        </div>
        <div class="dialog-close" @click="handleClose(false)">
          <i class="lg-icon lg-icon-close font-size-18" style="color: var(--color-text-primary)"></i>
        </div>
      </div>
      <!--body 区域-->
      <div class="dialog-body">
        <div class="body-content">
          <span>{{ $t('loc.mixAgeGroup7') }}</span>
        </div>
        <div class="body-flex">
          <div class="flex-left">
            <div class="w-full display-flex justify-content-between align-items">
              <span class="differentiation-title">{{ $t('loc.mixAgeGroup8') }}</span>
              <span>
                <el-switch
                  v-model="enableMixedAgeDifferentiation"
                  @change="changeMixAgeDiff"
                  unselectable="on" onselectstart="return false;"
                  style="-moz-user-select:none;-webkit-user-select:none;">
                    </el-switch>
              </span>
            </div>
            <div>
              <span class="differentiation-content">
                {{ $t('loc.mixAgeGroup9') }}
              </span>
            </div>
            <div class="display-flex justify-content align-items-baseline gap-10 flex-direction-col"
                 v-for="mixedAgeGroup in mixedAgeGroups"
                 :key="mixedAgeGroup.id">
              <div class="mixed-age-group-title" v-html="mixedAgeGroup.name"></div>
              <div class="display-flex justify-content align-items gap-10"
                   v-for="availableAge in mixedAgeGroup.availableAges"
                   :key="availableAge">
                <i class="lg-icon lg-icon-approve font-size-18" style="color: var(--color-success)"></i>
                <div v-html="availableAge"></div>
              </div>
            </div>
          </div>
          <div class="flex-right">
            <div class="right-title">{{ $t('loc.mixAgeGroup10') }}</div>
            <div>
              <img width="403" height="380"
                   src="@/assets/img/lesson2/plan/mixed_age_differentiation.png">
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <!-- 一个 Save 按钮 -->
        <el-button @click="closeMixedDifferentiation" plain :loading="saveLoading">{{
            $t('loc.close')
          }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {mapState} from 'vuex'
import LessonApi from '@/api/lessons2'

export default {
  name: 'MixedAgeDifferentiationSettings',
  data() {
    return {
      mixedAgeGroups: [], // 混龄分组的数据
      enableMixedAgeDifferentiation: false, // 是否开启了分组
      mixedAgeDifferentiationShow: false, // 混合年龄组弹窗是否展示
      saveLoading: false // 保存的 loading
    }
  },
  created() {
    this.initMixedAgeGroups()
    // 查询接口中获取开关状态 getMixedAgeGroupMeta
    this.initEnableMixedAgeDifferentiation();
  },
  watch: {
    mixedAgeDifferentiationShow(val) {
      // 获取开关的状态
      if (val) {
        this.initEnableMixedAgeDifferentiation()
      }
    },
    enableMixedAgeDifferentiation(val) {
      // 将 enableMixedAgeDifferentiation 的值存储到 sessionStorage 中
      sessionStorage.setItem('enableGroupTeams' + this.groupId, JSON.stringify(val))
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      showAIGroup: state => state.lesson.showAIGroup // 是否展示 AI 分组
    }),
    /**
     * 获取用户 id
     */
    currentUserId() {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    }
  },
  methods: {
    initMixedAgeGroups() {
      this.mixedAgeGroups = [
        {
          id: '1',
          name: this.$t('loc.mixAgeGroup11'),
          availableAges: ['PS/PK (3-4)', 'Toddler (2-3)']
        },
        {
          id: '2',
          name: this.$t('loc.mixAgeGroup12'),
          availableAges: ['Toddler (2-3)']
        }
      ]
    },
    // 获取 mixed age 开关
    // 获取 mixed age 分组的设置
    initEnableMixedAgeDifferentiation() {
      this.$axios.get($api.urls().getOpenMixedAgeGroup)
        .then(res => {
          this.enableMixedAgeDifferentiation = res.success
        })
    },
    // 当 mix age diff 的按钮发生改变的时候
    changeMixAgeDiff(val) {
      // 先将数据保存在本地
      sessionStorage.setItem('enableMixedAgeDifferentiation' + this.currentUserId, val)
      // 保存开启老师分组的状态
      const params = {
        userId: this.currentUserId,
        isMixedAgeGroup: val
      }
      // 按照不同的状态发送不同的消息
      const message = val ? this.$t('loc.mixAgeGroup15') : this.$t('loc.mixAgeGroup14');
      LessonApi.saveOpenMixedAgeGroup(params, {}).then(res => {
        this.$message.success(message)
        this.$emit('changeEnableTeacherGroupStatus', val)
      })
    },
    // 关闭弹窗，如果 saveClose 是 true 的时候，说明是通过点击保存而关闭弹窗的，否则就是通过点击关闭按钮关闭的
    handleClose(saveClose) {
      this.mixedAgeDifferentiationShow = false
      // 向父组件发送关闭弹窗的事件
      this.$emit('close', saveClose)
    },
    // 展示弹窗
    showMixedAgeDifferentiation(selectGroupId, selectCenterId) {
      // 设置 selectedGroupId
      this.selectedGroupId = selectGroupId
      // 设置 selectedCenterId
      this.selectedCenterId = selectCenterId
      this.mixedAgeDifferentiationShow = true
    },

    // 关闭弹窗
    closeMixedDifferentiation() {
      this.handleClose(true)
    },
    // 清理老数据
    clearOldData() {
      this.$set(this, 'enableMixedAgeDifferentiation', false)
      this.$set(this, 'mixedAgeDifferentiationShow', false)
      this.$set(this, 'saveLoading', false)
    }
  }
}
</script>

<style lang="less" scoped>
.assign-teacher-dialog {
  display: flex;
  width: 880px;
  padding: 20px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  border-radius: 8px;
  background: #FFF;

  /* 卡片投影 */
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);

  .dialog-title {
    display: flex;
    align-items: flex-start;
    align-self: center;

    .dialog-title-content {
      flex: 1 0 0;
      color: var(--111-c-1-c, #111C1C);
      font-feature-settings: 'clig' off, 'liga' off;

      /* Semi Bold/20px */
      font-family: Inter;
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px; /* 130% */
    }

    .dialog-close {
      cursor: pointer;
      width: 20px;
      height: 20px;
    }
  }

  .dialog-body {
    display: flex;
    margin-top: -24px;
    margin-bottom: -24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .body-content {
      font-family: Inter;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      text-align: left;
    }

    .body-flex {
      width: 967px;
      height: 417px;
      gap: 18px;
      display: flex;
      justify-content: center;
      align-items: center;

      .flex-left {
        width: 534px;
        height: 417px;
        gap: 10px;
        display: flex;
        padding: 24px;
        border-radius: 8px;
        justify-content: flex-start;
        align-items: flex-start;
        flex-direction: column;
        background: #F4F9FA;

        .differentiation-title {
          //styleName: Semi Bold/18px;
          font-family: Inter;
          font-size: 18px;
          font-weight: 600;
          line-height: 26px;
          text-align: left;
        }

        .differentiation-content {
          //styleName: Semi Bold/16px;
          font-family: Inter;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          text-align: left;
        }

        .mixed-age-group-title {
          font-family: Inter;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          text-align: left;
        }
      }

      .flex-right {
        width: 415px;
        height: 417px;
        padding: 24px;
        gap: 16px;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background: #F4F9FA;

        .right-title {
          position: relative;
          top: 8px;
          font-size: 16px;
        }
      }
    }
  }
}

.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/deep/ .width-full {
  width: 100% !important;

  & {
    width: 100% !important;
  }
}

.height-22 {
  height: 22px;
}

/deep/ .el-image {
  border-radius: 4px !important;
}

/deep/ .el-form-item {
  margin-bottom: 0;
}

@media only screen and (max-width: 1199px) {
  //ipad
  .ipad_width {
    display: flex;
    justify-content: flex-start;
    max-width: 15px;
  }
}

.child-area {
  padding: 5px 10px;
  min-height: 60px;
  max-height: 300px;
  overflow-y: auto;
}

.child-area {
  padding: 0 !important;
  margin-bottom: 12px;
}

.search-child-area {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 2;
  padding: 10px 10px 5px 10px;
}

.child-list {
  overflow-y: auto;
}

/deep/ .el-tag--info {
  color: #323338;
}

.child-check-row {
  .child-avatar {
    max-width: 32px;
    border-radius: 50%;
  }

  /deep/ .el-checkbox {
    margin-bottom: 0 !important;
  }

  /deep/ .el-checkbox__label {
    width: calc(100% - 15px);
  }

  padding: 10px 10px 0 10px;
}

.search-lesson-content {
  min-height: 45px;
  display: flex;
  align-items: center;
}

/deep/ .el-scrollbar .is-horizontal {
  display: none;
}

/deep/ .el-scrollbar__wrap {
  overflow-x: hidden;
}

/deep/ .el-col {
  padding: 0 !important;
}

/deep/ .el-icon-error:before {
  font-family: 'lg-icon';
  content: "\e6a7";
}

.add-margin-r-42 {
  margin-right: 42px;
}
</style>
<style lang="less">
.select-child-popover {
  padding: 4px 0px !important;

  .enrollment-tag {
    display: flex;
    height: 32px;
    padding: 4px 8px;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
  }
}

.select-teacher-popover {
  padding: 0;

  .teacher-select {
    display: flex;
    padding: 0px;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 4px;
    background: var(--color-white);
    cursor: pointer;

    .teacher-groups-tag-title {
      width: 100%;
      padding: 8px;
    }
  }

  .teacher-select :hover {
    background: var(--color-primary-light);
  }
}
</style>