<template>
  <div>
    <el-dialog :visible.sync="dialogVisible" width="50%" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-result v-if="showSubmit && !goBack" icon="success" :title="$t('loc.curriculum42')" :subTitle="successTip">
      </el-result>
      <el-result v-else-if="goBack" icon="warning" title="Confirmation" :subTitle="goBackTip">
      </el-result>
      <el-result v-else icon="warning" title="Confirmation" :subTitle="warningTip">
      </el-result>
      <el-table :data="applyRecord.records" stripe header-row-class-name="table-header"  :height="tableHeight">
        <el-table-column property="unit" :label="$t('loc.curriculum28')" width="130">
          <template slot-scope="scope">
            <span v-if="scope.row.unitNumber"> {{ scope.row.unitNumber >=1 ? 'Unit ' + scope.row.unitNumber : 'Special Unit ' + scope.row.unitNumber  }} </span>
            <span v-else> -- </span>
          </template>
        </el-table-column>
        <el-table-column property="week" :label="$t('loc.weekName')">
          <template slot-scope="scope">
            <span class="text-ellipsis">{{ $moment(scope.row.fromDate).format('MM-DD') }} - {{ $moment(scope.row.toDate).format('MM-DD') }} <span v-if="scope.row.planWeek">(Week {{ scope.row.planWeek }})</span></span>
          </template>
        </el-table-column>
        <el-table-column property="planTheme" :label="$t('loc.themeUper')">
          <template slot-scope="scope">
            <div class="cell-wrap">{{ scope.row.planTheme }}</div>
          </template>
        </el-table-column>
        <el-table-column property="planStatus" :label="$t('loc.curriculum44')" width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.planStatus == 'A_DRAFT'" style="color: #E6A23C;"><i class="el-icon-warning"></i>{{ $t('loc.curriculum46') }}</span>
            <span style="color: #67C23A;"  v-else><i class="el-icon-success"></i>{{ $t('loc.curriculum45') }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="showSubmit && !goBack" class="m-t-sm display-flex justify-content-end">
        <el-button @click="goBack=true">{{ $t('loc.close') }}</el-button>
        <el-button type="primary" @click="nextPlan()">{{ $t('loc.curriculum47') }}</el-button>
      </div>
      <div v-else-if="goBack" class="m-t-sm display-flex justify-content-end">
        <el-button @click="goBack=false">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="endApplyFlowPath()">{{ $t('loc.confirm') }}</el-button>
      </div>
      <div v-else class="m-t-sm display-flex justify-content-end">
        <el-button @click="dialogVisible = false">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="endApplyFlowPath()">{{ $t('loc.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LessonApi from "@/api/lessons2";

export default {
  name: 'PlanApplyRecordsModal',
  data () {
    return {
      // 应用记录
      applyRecord: {
        total: 0,
        submit: 0,
        notSubmit: 0,
        records: [],
        nextPlanId: ''
      },
      dialogVisible: false,
      showSubmit: true,
      goBack: false,
      batchId: undefined
    }
  },
  computed: {
    tableHeaderClass () {
      return 'bg-gray'
    },
    successTip () {
      if (this.applyRecord.submit > 1) {
        return this.$t('loc.curriculums43', { num1: this.applyRecord.submit,num2: this.applyRecord.total })
      }
      return this.$t('loc.curriculum43', { num1: this.applyRecord.submit,num2: this.applyRecord.total })
    },
    warningTip () {
      if (this.applyRecord.notSubmit === 1) {
        return this.$t('loc.curriculum62', { num: this.applyRecord.notSubmit })
      }
      return this.$t('loc.curriculums62', { num: this.applyRecord.notSubmit })
    },
    goBackTip () {
      if (this.applyRecord.notSubmit > 1) {
        return this.$t('loc.curriculums62', { num: this.applyRecord.notSubmit })
      }
      return this.$t('loc.curriculum62', { num: this.applyRecord.notSubmit })
    },
    tableHeight () {
      let height = 50
      if (this.applyRecord.records.length > 4) {
        height = 250
      } else {
        height = 50 * this.applyRecord.records.length
      }
      return height + 50
    }
  },
  methods: {
    openDialog (data, showSubmit, batchId) {
      this.applyRecord = data
      this.showSubmit = showSubmit
      this.dialogVisible = true
      this.batchId = batchId
    },
    nextPlan () {
      this.dialogVisible = false
      let nextPlanId = this.applyRecord.nextPlanId
      if (nextPlanId) {
        this.$router.push({
          name: 'edit-plan',
          params: {
            planId: nextPlanId
          }
        })
      }
    },
    endApplyFlowPath () {
      let batchId = this.batchId
      LessonApi.endApplyFlowPath(batchId)
      .then(res => {
        this.$router.push({
          name: 'list-plan'
        })
      })
      .catch(error => {})
    }
  }

}
</script>

<style lang="less" scoped>
/deep/.el-table {
  border: 1px #DCDFE6 solid;
}
/deep/ .table-header th {
  background-color: #FAFAFA;
}
/deep/.el-result__icon svg {
  height: 130px;
  width: 130px;
}
/deep/.el-result__title p{
  margin: 0;
  font-size: 20px;
  color: #303133;
  font-weight: 600;
}
/deep/.el-result {
  padding-top: 0;
}
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-result__subtitle p{
  font-size: 16px;
  color: #303133;
}
.cell-wrap {
  white-space: normal; /* 允许换行 */
  word-wrap: break-word; /* 自动换行 */
}
</style>
