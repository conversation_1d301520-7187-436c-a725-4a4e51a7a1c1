<template>
  <div class="toolbar-menu lg-scrollbar-show h-full">
    <!-- 分享记录及评语 -->
    <!-- <share-record-card :records="records"></share-record-card> -->
    <!-- <plan-note-viewer :plan-id="planId" :note.sync="_note" v-if="planId && status && status.isShared" :key="planId"/> -->
    <!-- <plan-comment :planId="planId" v-if="planId && status && status.approved" :key="planId"/> -->
    <el-collapse v-model="activeName" accordion>
      <!-- 分享记录 -->
      <el-collapse-item v-if="records && records.length > 0" name="SHARED_RECORDS">
        <template slot="title">
          <i class="lg-icon lg-icon-reviewer-notes font-size-20 lg-color-text-secondary m-l-sm lg-color-text-placeholder"></i>
          <span class="m-l-xs font-size-16 lg-color-text-primary font-bold">{{ $t('loc.plan93') }}</span>
        </template>
        <share-record-card :records="records"></share-record-card>
      </el-collapse-item>
      <!-- 周计划笔记 -->
      <el-collapse-item v-if="_note" name="NOTES">
        <template slot="title">
          <i class="lg-icon lg-icon-note font-size-20 lg-color-text-secondary m-l-sm lg-color-text-placeholder"></i>
          <span class="m-l-xs font-size-16 lg-color-text-primary font-bold">My Notes</span>
        </template>
        <plan-note-viewer :plan-id="planId" :note.sync="_note" v-if="planId && status && status.isShared" :key="planId"/>
      </el-collapse-item>
      <!-- 评论，仅在审核通过的周计划预览页显示 -->
      <el-collapse-item v-if="planId && status && status.approved && !status.edit" name="COMMMENTS">
        <template slot="title">
          <i class="lg-icon lg-icon-feedback font-size-20 lg-color-text-secondary m-l-sm lg-color-text-placeholder"></i>
          <span class="m-l-xs font-size-16 lg-color-text-primary font-bold">{{ $t('loc.commentAndFeedback') }}</span>
        </template>
        <plan-comment :planId="planId" v-if="planId && status && status.approved" :key="planId"/>
      </el-collapse-item>
      <!-- Assessment Cohort View 暂时隐藏-->
      <router-link v-if="groupId" :to="{name:'assessmentCompare'}" target="_blank" class="lg-pointer">
        <el-tooltip effect="dark" :content="this.$t('loc.openInNewTab')" placement="right">
          <el-collapse-item :disabled="true" name="ASSESSMENT_COHORT_VIEW" style="margin-top: 10px">
            <template slot="title">
              <i class="lg-icon lg-icon-cohort-view font-size-20 lg-color-text-secondary m-l-sm lg-color-text-placeholder"></i>
              <span class="m-l-xs font-size-16 lg-color-text-primary font-bold">{{ $t('loc.assessmentCohortView') }}</span>
            </template>
          </el-collapse-item>
        </el-tooltip>
      </router-link>
      <!-- 班级概览 -->
      <el-collapse-item v-if="groupId" name="CLASS_OVERVIEW">
        <template slot="title">
          <i class="lg-icon lg-icon-classoverview font-size-20 lg-color-text-secondary m-l-sm lg-color-text-placeholder"></i>
          <span class="m-l-xs font-size-16 lg-color-text-primary font-bold">{{ $t('loc.classOverView') }}</span>
        </template>
        <class-overview :planId="planId" :groupId="groupId" :week="week" ref="classOverview"></class-overview>
      </el-collapse-item>
      <!-- 我的笔记:创建、编辑周计划时的参考 -->
      <el-collapse-item name="MY_NOTES" v-if="status && status.edit && !isAdmin">
        <template slot="title">
          <i class="lg-icon lg-icon-note font-size-20 m-l-sm lg-color-text-placeholder"></i>
          <span class="m-l-xs font-size-16 lg-color-text-primary font-bold">{{ $t('loc.planNoteTitle') }}</span>
        </template>
        <plan-note-list-viewer/>
      </el-collapse-item>
    </el-collapse>
    <!-- <el-drawer
      :visible.sync="drawer"
      custom-class="custom-drawer-fullscreen"
      :append-to-body="true"
      direction="btt"
      :destroy-on-close="true"
      size="80%"
      :before-close="handleClose">
      <assessment-compare :showGroupId="groupId"></assessment-compare>
    </el-drawer> -->
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ClassOverview from './ClassOverview'
import assessmentCompare from '../../dataReview/assessmentCompare.vue'
import ShareRecordCard from './ShareRecordCard'
import PlanComment from '@/views/modules/lesson2/lessonPlan/components/PlanComment'
import PlanNoteViewer from '@/views/modules/lesson2/lessonPlan/components/PlanNote/Viewer'
import PlanNoteListViewer from '@/views/modules/lesson2/lessonPlan/components/PlanNote/ListViewer'

export default {
  name: 'PlanToolbar',
  components: {
    PlanNoteListViewer,
    PlanNoteViewer,
    PlanComment,
    ClassOverview,
    assessmentCompare,
    ShareRecordCard
  },

  props: {
    planId: {
      type: String
    },
    groupId: {
      type: String
    },
    week: {
      type: Number
    },
    note: {
      type: Object
    },
    records: {
      type: Array
    },
    status: {
      type: Object
    }
  },
  data () {
    return {
      activeName: ['CLASS_OVERVIEW','MY_NOTES'], // 当前展开的菜单
      drawer: false // Assessment Cohort Report 是否显示
    }
  },
  watch: {
    // 监听激活的菜单
    activeName: {
      handler: function (val) {
        // 根据激活的菜单发送不同的埋点事件
        if (val === 'MY_NOTES') {
          this.$analytics.sendEvent('web_weekly_plan_edit_click_my_notes')
        } else if (val === 'COMMMENTS') {
          this.$analytics.sendEvent('web_weekly_virtual_detail_comment')
        } else if (val === 'SHARED_RECORDS') {
          this.$analytics.sendEvent('web_weekly_virtual_detail_view_comment')
        } else if (val == 'ASSESSMENT_COHORT_VIEW') {
          if (this.$route.name == 'edit-plan') {
            this.$analytics.sendEvent('web_weekly_plan_edit_click_cohort_view')
          }
          // this.$router.push({ name: 'assessmentCompare' })
        }
      },
      immediate: true
    },
    // 监听是否有班级
    groupId (val) {
      // 如果没有班级，将评论菜单项展开
      if (!val) {
        this.activeName = ['COMMMENTS']
      } else {
        this.activeName = ['CLASS_OVERVIEW','MY_NOTES']
      }
    }
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    _note: {
      get () {
        return this.note
      },
      set (value) {
        this.$emit('update:note', value)
      }
    },
    // 是否是管理员
    isAdmin () {
      if (!this.currentUser) {
        return false
      }
      let role = this.currentUser.role
      return role && role.toUpperCase() === 'OWNER'
    }
  },

  methods: {
    /**
     * 关闭 ASSESSMENT COHORT VIEW 事件
     */
    handleClose () {
      // 隐藏抽屉组件
      this.drawer = false
      // 还原菜单
      this.activeName = ['CLASS_OVERVIEW','MY_NOTES']
    }
  }
}
</script>

<style lang="less" scoped>
.toolbar-menu {
  width: 380px;
  background: transparent;
  border-radius: 8px;

  /deep/ .el-card {
    border-radius: 0;
    border: none;
    // border-top: 1px solid #DCDFE6;
  }
  /deep/ .el-card__header, /deep/ .el-card__body {
    padding: 0 20px;
    border: none;
  }

  /deep/ .el-card__header {
    background-color: #FAFDFE;
  }

  /deep/ .el-card__body {
    font-size: 14px;
  }

  /deep/ .el-card__header {
    font-size: 15px;
  }
  /deep/ .el-collapse-item__content {
    padding-bottom: 20px;
  }
}
.el-collapse {
  border: none;
}
.el-collapse-item__content {
  padding: 0px;
}
/deep/.el-collapse-item__header {
  border-radius: 8px;
  height: 66px;
  padding-right: 12px;
  padding-left: 10px;
  cursor: pointer!important;
}
/deep/.el-collapse-item__header.is-active {
  border-radius: 8px 8px 0 0;
}
/deep/.el-collapse-item__wrap {
  border-radius: 0 0 8px 8px;
}
.el-collapse-item {
  margin-top: 12px;
}
.el-collapse-item:first-child {
  margin-top: 0px;
}
.el-collapse-item__content {
  padding-bottom: 0;
}
/deep/.el-collapse-item__header .el-collapse-item__arrow {
  color: #111c1c;
  font-size: 20px;
}

/deep/ .el-collapse-item__header {
  height: 66px;
  line-height: 66px;
}

</style>
<style lang="less">
.custom-drawer-fullscreen {
  height: calc(100% - 64px) !important;
  border-radius: 20px 20px 0 0;
  .el-drawer__header {
    margin-bottom: 0px !important;
  }
  .el-drawer__close-btn {
    position: fixed;
    width: 64px;
    height: 64px;
    top: 0px;
    right:12px;
    font-size: 40px;
    color: #fff;
    z-index: 9999;

    & > .el-dialog__close {
      position: relative;
      top: 0px;
      right: 0px;
      color: #fff;
    }
  }
}
</style>
