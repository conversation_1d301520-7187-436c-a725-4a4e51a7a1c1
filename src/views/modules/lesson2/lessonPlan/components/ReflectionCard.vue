<template>
  <el-card class="box-card" shadow="never" v-loading="loading">
    <!-- 标题 -->
    <div class="title-font-18 lg-color-text-primary lg-margin-bottom-12">{{$t('loc.weekReflection')}}</div>
    <div class="display-flex lg-margin-bottom-20">
      <div v-if="!!weeks && weeks.length > 0" class="action-bar">
        <!-- 上一周按钮 -->
        <el-tooltip :disabled="lastWeek" class="item" effect="dark" :content="$t('loc.preWeek')" placement="top">
          <i class="lg-icon lg-icon-arrow-left lg-margin-right-24 font-size-16" :class="{'disable-color':lastWeek}" @click="changeWeek(true)"></i>
        </el-tooltip>
        <!-- 下一周按钮 -->
        <el-tooltip :disabled="firstWeek" class="item" effect="dark" :content="$t('loc.nextWeek')" placement="top">
          <i class="lg-icon lg-icon-arrow-right lg-margin-right-16 font-size-16" :class="{'disable-color':firstWeek}" @click="changeWeek(false)"></i>
        </el-tooltip>
        <!-- 周次 -->
        <span class="title-font-18 lg-color-text-primary">{{ $t('loc.planWeek') }} {{currentWeek.week }} </span>
        <!-- 日期 -->
        <span class="lg-margin-left-8 title-font-14-regular lg-color-text-secondary">{{ $moment(currentWeek.fromAtLocal).format('MM/DD') }} - {{ $moment(currentWeek.toAtLocal).format('MM/DD') }}</span>
      </div>
    </div>
    <div class="reflection-card">
      <!-- 反思内容 -->
      <div v-loading="reflectionLoading" class="scrollbar-new" :class="{'reflection-content space-pre-line': weekReflection}">
        <span>{{weekReflection}}</span>
      </div>
      <!-- 没有反思 -->
      <div class="display-flex justify-content align-items flex-direction-col" v-if="!loading && !weekReflection">
        <span class="lg-color-text-secondary">{{$t('loc.noReflection')}}</span>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapState } from "vuex"
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'

export default {
  name: 'ReflectionCard',
  components: {
  },

  props: {
    groupId: {
      type: String
    },
    planId: {
      type: String
    },
    week: {
      type: Number
    }
  },

  data () {
    return {
      weeks: [],
      currentWeek: {},
      firstWeek: false,
      lastWeek: false,
      weekReflection: undefined,
      loading: true,
      reflectionLoading: false
    }
  },

  created () {
    this.loadWeek()
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    })
  },

  methods: {
    /**
     * 加载周次列表
     */
    loadWeek () {
      if (!this.groupId) {
        return
      }
      this.loading = true
      LessonApi.listWeeks({
        groupId: this.groupId
      }).then(response => {
        // 周列表
        this.weeks = response.weeks
        // 设置当前周
        this.setCurrentWeek()
        // 获取周反思
        this.loadReflection()
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    },

    setCurrentWeek () {
      // 当前周
      let currentWeeks = this.weeks.filter(week => week.currentWeek)
      // 上一周
      let lastWeeks = this.weeks.filter(week => week.lastWeek)
      if (currentWeeks.length > 0) {
        // 如果有当前周，则取当前周
        this.currentWeek = currentWeeks[0]
      } else if (lastWeeks.length > 0) {
        // 没有则取上一周
        this.currentWeek = lastWeeks[0]
      } else if (this.weeks.length > 0) {
        this.currentWeek = this.weeks[0]
      }
      // 如果当前周计划有周信息，去当前周的上一周
      if (this.week) {
        // 上一周的周计划
        let lastWeeks = this.weeks.filter(week => week.week === this.week - 1)
        if (lastWeeks.length > 0) {
          // 上一周有反思的周计划
          let hasReflectionWeeks = lastWeeks.filter(week => week.planIds && week.planIds.length > 0)
          if (hasReflectionWeeks.length > 0) {
            this.currentWeek = hasReflectionWeeks[0]
          } else {
            this.currentWeek = lastWeeks[0]
          }
        } else {
          //
        }
      }
    },

    loadReflection () {
      this.setWeekSelectable()
      if (!this.currentWeek) {
        this.weekReflection = undefined
        return
      }
      let planIds = this.currentWeek.planIds
      if (planIds.length === 0) {
        this.weekReflection = undefined
        return
      }
      this.reflectionLoading = true
      LessonApi.getPlanReflection({
        planId: planIds[0]
      }).then(response => {
        if (response) {
          this.weekReflection = response.lessonReflection
        }
        this.reflectionLoading = false
      }).catch(error => {
        this.reflectionLoading = false
      })
    },

    setWeekSelectable () {
      let index = this.weeks.indexOf(this.currentWeek)
      this.firstWeek = index === 0
      this.lastWeek = index === this.weeks.length - 1
    },

    changeWeek (last) {
      // 当前周索引
      let index = this.weeks.indexOf(this.currentWeek)
      if (!last) {
        index = index - 1
        if (index == -1) {
          return
        }
      } else {
        index = index + 1
        if (index == this.weeks.length) {
          return
        }
      }
      this.currentWeek = this.weeks[index]
      this.loadReflection()
    }
  },

  watch: {
    /**
     * 切换班级时重新加载数据
     */
    groupId (val) {
      val && this.loadWeek()
    },
    planId (val) {
      val && this.loadWeek()
    },
    week (val) {
      this.setCurrentWeek()
    }
  }
}
</script>

<style lang="less" scoped>
.action-bar {
  border-radius: 4px;
  padding: 3px 12px;
  border: 1px solid #DCDFE6;
  margin-right: 12px;
  i {
    cursor: pointer;
  }
}

.reflection-card {
  padding: 20px 18px;
  border-radius: 8px;
  background: #fafafa;
  border: 1px solid #dcdfe6;
  display: flex;
  flex-direction: column;
}

.reflection-content {
  max-height: 170px;
  overflow: auto;
}
.week-switch {
  line-height: 20px;
  margin-bottom: 4px;
  /deep/ .el-button {
    padding: 0;
  }
}
.disable-color {
  color: #99676879 !important;
}
</style>
