<!-- eslint-disable no-mixed-spaces-and-tabs -->
<template>
  <div class="lg-padding-bottom-20 text-default">
    <!-- PTKLF 框架 -->
    <div class="display-flex" :class="{ 'lg-margin-left-20' : addMargin }">
      <div class="measure-title">
        <span>{{ $t('loc.ptklfFoundations') }}</span>
        <el-tooltip effect="dark" :content="$t('loc.ptklfFoundationsTip')" placement="top">
          <i class="lg-icon lg-icon-question lg-pointer font-weight-400"></i>
        </el-tooltip>
      </div>
      <div class="measure-details display-flex align-items">
        <el-row :gutter="10" class="w-full">
          <el-col :span="12" v-for="(domain, index) in reverse ? drdpDomains : mappedDomains" :key="index">
            <span class="font-weight-600">
              {{ domain.abbreviation }}:
            </span>
            <el-tooltip effect="dark" :content="measure.name" placement="top" v-for="(measure, index) in domain.measures" :key="index" :open-delay="300">
              <span class="word-break" v-if="index < domain.measures.length - 1" style="display: inline-block;">
                {{ measure.abbreviation }};&nbsp;
              </span>
              <span class="word-break" v-else style="display: inline-block;">
                {{ measure.abbreviation }}
              </span>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- PSC 框架 -->
    <div class="display-flex" :class="{ 'lg-margin-left-20' : addMargin }">
      <div class="measure-title lg-border-bottom-left-radius-8">
        <span>{{ $t('loc.drdpStandards') }}</span>
      </div>
      <div class="measure-details display-flex align-items lg-border-bottom-right-radius-8">
        <el-row :gutter="10" class="w-full">
          <el-col :span="12" v-for="(domain, index) in reverse ? reservedMappedDomains : drdpDomains" :key="index">
            <span class="font-weight-600">
              {{ domain.abbreviation }}:
            </span>
            <el-tooltip effect="dark" :content="measure.name" placement="top" v-for="(measure, index) in domain.measures" :key="index" :open-delay="300">
              <span class="word-break" v-if="index < domain.measures.length - 1" style="display: inline-block;">
                {{ measure.abbreviation }};&nbsp;
              </span>
              <span class="word-break" v-else style="display: inline-block;">
                {{ measure.abbreviation }}
              </span>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import frameworkUtils from '@/utils/frameworkUtils'
import { equalsIgnoreCase } from '@/utils/common'

export default {
  name: 'PlanMeasureMap',

  props: {
    // 周计划分组
    categories: {
      type: Array,
      default: () => []
    },
    // 框架数据
    frameworkData: {
      type: Array,
      default: () => []
    },
    // 是否添加左边距
    addMargin: {
      type: Boolean,
      default: false
    },
    // 是否是反向映射测评点
    reverse: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      mapFrameworkData: [] // 映射框架数据
    }
  },

  created () {
  },

  mounted () {
    this.getFrameworkMeasureMap()
  },

  computed: {
    // 获取 DRDP 框架的 domains
    drdpDomains () {
      // 从分组中拿到所有的活动项
      let items = this.categories.flatMap(x => x && x.items)
      // 提取出所有的 measures
      let measures = items.flatMap(x => x && x.measures).filter(x => x)
      // 提取出所有的 measure id
      let allMeasureIds = measures.flatMap(x => x && x.id)
      let result = []
      // 获取框架中的领域
      let domains = this.frameworkData[0] && this.frameworkData[0].child_domains
      // 遍历领域，找到包含在 allMeasureIds 中的 measure
      // 定义递归函数
      const collectMeasures = (node, parentAbbreviation) => {
        if (!node.child_domains || node.child_domains.length === 0) {
          // 当前节点是叶子节点，返回符合条件的 measure
          return allMeasureIds.includes(node.id) ? [node] : []
        }
        // 如果有子节点，递归收集子节点的 measures
        let collectedMeasures = []
        node.child_domains.forEach(child => {
          collectedMeasures.push(...collectMeasures(child, node.abbreviation));
        })
        // 如果当前节点是第一层，将结果记录
        if (node.abbreviation === parentAbbreviation) {
          if (collectedMeasures.length > 0) {
            result.push({
              abbreviation: parentAbbreviation,
              measures: collectedMeasures
            })
          }
        }
        return collectedMeasures
      }
      // 遍历 domains 的每一个顶级节点
      domains && domains.forEach(domain => {
        collectMeasures(domain, domain.abbreviation)
      })
      return result
    },
    // 映射的测评点
    mappedDomains () {
      // 从分组中拿到所有的活动项
      let items = this.categories.flatMap(x => x && x.items)
      // 提取出所有的 measures
      let measures = items.flatMap(x => x && x.measures).filter(x => x)
      // 提取出所有的 measure id
      let allMeasureIds = measures.flatMap(x => x && x.id)
      // 获取框架中的领域
      let allMeasures = this.mapFrameworkData.filter(x => allMeasureIds.includes(x.id))
      let mappedMeasures = []
      allMeasures.forEach(measure => {
        Object.keys(measure.mappedMeasures).forEach(key => {
          mappedMeasures = mappedMeasures.concat(measure.mappedMeasures[key])
        })
      })
      let domains = []
      mappedMeasures.sort((a, b) => a.sortIndex - b.sortIndex)
      // 遍历映射上的测评点，将其按照领域分类
      mappedMeasures.forEach(measure => {
        // 找到对应的领域
        let domain = domains.find(x => x.abbreviation === measure.domainAbbreviation)
        // 如果没有找到，创建一个新的领域
        if (!domain) {
          domain = {
            abbreviation: measure.domainAbbreviation,
            measures: []
          }
          domains.push(domain)
        }
        // 如果测评点不存在，添加到领域中
        let measureExists = domain.measures.some(m => m.abbreviation === measure.abbreviation && m.name === measure.name)
        if (!measureExists) {
          domain.measures.push({
            abbreviation: measure.abbreviation,
            name: measure.name,
            sortIndex: measure.sortIndex
          })
        }
        // 按照测评点缩写排序
        domain.measures.sort((a, b) => a.sortIndex - b.sortIndex)
      })
      return domains
    },
    // 映射的 DRDP 的测评点
    reservedMappedDomains () {
      // 从分组中拿到所有的活动项
      let items = this.categories.flatMap(x => x && x.items)
      // 提取出所有的 measures
      let measures = items.flatMap(x => x && x.measures).filter(x => x)
      // 提取出所有的 measure id
      let allMeasureIds = measures.flatMap(x => x && x.id)
      // 获取框架中的领域
      let allMeasures = this.mapFrameworkData.filter(measure =>
        measure.mappedMeasures && Object.values(measure.mappedMeasures).some(mappedList =>
          mappedList.some(mappedMeasure => allMeasureIds.includes(mappedMeasure.id))
        )
      )
      let domains = []
      // 遍历映射上的测评点，将其按照领域分类
      allMeasures.forEach(measure => {
        // 找到对应的领域
        let domain = domains.find(x => x.abbreviation === measure.domainAbbreviation)
        // 如果没有找到，创建一个新的领域
        if (!domain) {
          domain = {
            abbreviation: measure.domainAbbreviation,
            measures: []
          }
          domains.push(domain)
        }
        // 如果测评点不存在，添加到领域中
        let measureExists = domain.measures.some(m => m.abbreviation === measure.abbreviation && m.name === measure.name)
        if (!measureExists) {
          domain.measures.push({
            abbreviation: measure.abbreviation,
            name: measure.name,
            sortIndex: measure.sortIndex
          })
        }
        // 按照测评点缩写排序
        domain.measures.sort((a, b) => a.sortIndex - b.sortIndex)
      })
      return domains
    },
    // 是否是 CAPTKLF 框架的周计划
    isCAPTKLF () {
      const frameworkId = this.frameworkId
      return frameworkUtils.isCAPTKLF(frameworkId)
    },
    abbr () {
      const frameworkId = this.frameworkId
      // 获取框架的缩写
      return frameworkUtils.getFrameworkAbbr(frameworkId)
    },
    foundationsContent () {
      if (this.isCAPTKLF) {
        return this.$t('loc.ptklfFoundationsTip')
      } else {
        return this.$t('loc.domainMappingMap2', {abbr: this.abbr})
      }
    },
    isMELS () {
      const frameworkId = this.frameworkId
      return frameworkUtils.isMELS(frameworkId)
    },
    isILEARN () {
      const frameworkId = this.frameworkId
      return frameworkUtils.isILEARN(frameworkId)
    },
    // unit 详情改编课程时显示操作栏
    unitDetail () {
      return equalsIgnoreCase(this.$route.name, 'unitDetail') || equalsIgnoreCase(this.$route.name, 'unit-detail-cg') || equalsIgnoreCase(this.$route.name, 'unit-detail-cg-desinger') || this.$route.path.includes('/lessons/unit-planner') || this.$route.path.includes('/curriculum-genie/unit-planner')
    }
  },

  methods: {
    // 获取框架测评点映射
    getFrameworkMeasureMap () {
      this.$store.dispatch('getMapFrameworkData').then(res => {
        this.mapFrameworkData = res
      })
    }
  },
  watch: {
  }
}
</script>

<style lang="less" scoped>
.measure-title {
  background: #f5f6f8;
  padding: 10px;
  border-right: solid 1px #DADADA;
  border-bottom: solid 1px #DADADA;
  border-left: solid 1px #DADADA;
  width: 120px;
  flex: none;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: normal;
}

.measure-details {
  background-color: #fff;
  padding: 10px;
  border-right: solid 1px #DADADA;
  border-bottom: solid 1px #DADADA;
  width: 120px;
  flex: 1 0 auto;
  min-height: 40px;
  min-width: 600px;
}

// 移动端适配样式
@media screen and (max-width: 768px) {
  .measure-title {
    width: 100%;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    padding: 15px;
    min-width: 120px;
  }

  .measure-details {
    width: max-content;
    min-width: 100%;
    border-radius: 0 0 8px 8px;
    padding: 15px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 2px;
    }
  }

  .display-flex {
    flex-direction: column;
  }

  /deep/ .el-col {
    width: 100% !important;
    margin-bottom: 10px;
    min-width: 300px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  /deep/ .el-tooltip {
    display: inline-block;
    margin-bottom: 5px;
  }

  .font-weight-600 {
    display: block;
    margin-bottom: 5px;
  }

  // 触摸区域优化
  .lg-icon {
    padding: 10px;
    font-size: 18px;
  }
}
</style>
