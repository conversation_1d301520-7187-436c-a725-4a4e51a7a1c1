<template>
  <div class="lesson-reflection-list lg-border-radius-8" id="lesson-reflection-container" ref="lessonReflectionList" v-if="lessons && lessons.length > 0">
    <!-- 反思内容 -->
    <div class="bg-white lg-border-radius-8" v-loading="loading">
      <!-- 标题 -->
      <div class="lg-padding-20 title-font-18 lg-color-text-primary">
        {{$t('loc.plan77')}}
      </div>
      <!-- 没有反思内容 -->
      <div class="display-flex justify-content align-items flex-direction-col" v-if="!loading && lessons.length === 0">
        <img src="@/assets/img/lesson2/dataReview/empty.png"/>
        <span>{{$t('loc.plan16')}}</span>
      </div>
      <el-row class="h-full b-a" v-if="lessons && lessons.length > 0">
        <!-- 课程列表 -->
        <el-col :sm=7 :lg=6 class="h-full lesson-list">
          <!-- 课程详情 -->
          <div class="display-flex align-items b-b lesson-item lg-pointer" v-for="(lesson, index) in lessons" :key="index" :class="{'bg-selected': selectedLesson && selectedLesson.id === lesson.id}" @click="selectLesson(lesson)">
            <!-- 课程封面 -->
            <div class="lesson-cover">
              <media-viewer :url="lesson.coverMediaUrls[0]" :ratio="1/1" :hideVideo="true"></media-viewer>
            </div>
            <!-- 课程名称、作者 -->
            <div class="m-l-sm lesson-info">
              <div class="font-bold overflow-ellipsis-two lg-color-text-primary" :title="lesson.name">{{lesson.name}}</div>
              <div class="display-flex align-items">
                <el-avatar style="flex: none;" size="small" :src="lesson.author.avatarUrl"></el-avatar>
                <div class="m-l-xs overflow-ellipsis lesson-info word-break" :title="lesson.author.displayName">by {{lesson.author.displayName}}</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :sm=17 :lg=18 class="h-full b-l" v-if="selectedLesson">
          <!-- 本周反思标题 -->
          <div class="bg-light lg-padding-t-b-16 lg-padding-l-r-24 display-flex justify-content-between">
            <div class="font-size-16 font-bold text-primary">{{$t('loc.plan78')}}</div>
          </div>
          <!-- 本周反思列表 -->
          <div v-for="(reflection, index) in selectedLesson.reflections" :key="index" class="b-b">
            <reflection-detail
              :lessonId="selectedLesson.id"
              :planId="planId"
              :showEdit=true
              @callDeleteReflection="deleteReflection"
              :reflection="reflection">
            </reflection-detail>
          </div>
          <!-- 观察记录标题 -->
          <div class="add-padding-t-24 add-margin-l-24">
            <span class="font-size-16 font-bold">{{$t('loc.plan74')}}</span>
          </div>
          <!-- 观察记录列表 -->
          <div class="observation-detail">
            <reflection-observation-detail
              :lessonId="selectedLesson.id"
              :planId="planId"
              :hideHeader="true">
            </reflection-observation-detail>
          </div>
          <!-- 其他反思列表 -->
          <reflection-detail-list
            ref="otherReflections"
            @callDeleteReflection="deleteReflection"
            :excludePlanId="planId"
            :lessonId="selectedLesson.id"
            :subList=true :showHeader2=true
            :showEdit=true>
          </reflection-detail-list>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex"
import LessonApi from '@/api/lessons2'
import ObservationItem from './ObservationItem'
import MediaViewer from './MediaViewer'
import ReflectionDetail from './ReflectionDetail'
import ReflectionObservationDetail from './ReflectionObservationDetail'
import ReflectionDetailList from './ReflectionDetailList'

export default {
  name: 'LessonReflectionList',
  components: {
    ObservationItem,
    MediaViewer,
    ReflectionDetail,
    ReflectionObservationDetail,
    ReflectionDetailList
  },

  props: {
    planId: {
      type: String
    }
  },

  data () {
    return {
      lessons: [],
      selectedLesson: {},
      loading: true
    }
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    })
  },

  methods: {
    /**
     * 加载课程列表
     */
    loadLessons () {
      this.loading = true
      LessonApi.listLessonsWithReflections({
        planId: this.planId
      }).then(res => {
        this.lessons = res.lessons
        if (this.lessons.length > 0) {
          this.selectedLesson = this.lessons[0]
        }
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    },

    /**
     * 加载观察记录
     */
    loadNotes () {
      LessonApi.getLessonNotes({
        planId: this.planId,
        lessonId: this.lessonId
      }).then(res => {
        this.notes = res.notes
      }).catch(error => {})
    },

    /**
     * 选择课程
     */
    selectLesson (lesson) {
      this.selectedLesson = lesson
    },

    selectLessonById (lessonId) {
      if (!lessonId) {
        return
      }
      let filterLessons = this.lessons.filter(l => l.id === lessonId)
      if (filterLessons.length > 0) {
        this.selectLesson(filterLessons[0])
        this.$nextTick(() => {
          document.getElementById('calendar-container').scrollTop = document.getElementById('plan-table').scrollHeight + 150
        })
      }
    },

    deleteReflection (itemId, otherReflection) {
      if (otherReflection) {
        this.$refs.otherReflections.loadReflections()
      } else {
        this.loadLessons()
        this.$emit('callDeleteReflection', itemId)
      }
    }
  },

  watch: {
    planId: {
      immediate: true,
      handler() {
        this.loadLessons()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.lesson-cover {
  height: 64px;
  width: 64px;
  flex-shrink: 0;
}
.lesson-info {
  flex-grow: 0;
  min-width: 0;
}
.lesson-item {
  padding: 5px;
}
</style>