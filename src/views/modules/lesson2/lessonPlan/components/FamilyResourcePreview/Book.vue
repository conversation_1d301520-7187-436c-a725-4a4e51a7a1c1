<template>
  <el-row :gutter="10" type="flex" style="align-items: center">
    <el-col :span="4">
      <div class="book">
        <a @click="openFamilyResourceUrl(bookLink)" >
          <lesson-media-viewer :url="thumbnailUrl" type="image"/>
        </a>
      </div>
    </el-col>
    <el-col :span="20">
      <span>{{ title }}</span>
    </el-col>
  </el-row>
</template>

<script>
import LessonMediaViewer from "@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer";
import tools from '@/utils/tools'
export default {
  name: "LessonShareBook",
  components: {LessonMediaViewer},
  props: [
    'book'
  ],
  computed: {
    thumbnailUrl() {
      let {volumeInfo = {}} = this.book || {};
      let {imageLinks = {}} = volumeInfo;
      let {thumbnail} = imageLinks;
      return thumbnail;
    },
    bookId() {
      let {id} = this.book || {};
      return id;
    },
    title() {
      let {volumeInfo = {}} = this.book || {};
      let {title} = volumeInfo;
      return title;
    },

    /**
     * 书的链接
     */
    bookLink () {
      let { volumeInfo = {} } = this.book || {}
      let { infoLink } = volumeInfo
      return infoLink
    }
  },
  methods:{
    openFamilyResourceUrl(url) {
      tools.openUrlWithWebAndIPad(url)
    },
  }
}
</script>

<style scoped>
.book {
  display: flex;
  flex-flow: column;
  justify-content: center;
  gap: 5px;
  font-size: 14px;
}

.lesson-share-media-info-title {
  text-align: center;
  color: #2A343A;
  font-weight: 500;
}

.lesson-share-media-info-author {
  text-align: center;
  color: rgba(52, 62, 67, 0.73);
  font-weight: 400;
}
</style>