<template>
  <div>
    <div v-for="(dll,index) in dlls" :key="dll.id">
      <div class="dll-detail-body">
        <dll-item :dll="dll" :media-span="5" :translation-list-span="19" :languages="languages">
          <el-button slot="edit"
                     type="text"
                     @click="handleClickEdit(dll)"
                     class="m-r-sm dll-edit-button">
            <i class="el-icon-edit" style="font-size: 19px"></i>
            {{ $t('loc.lessons2LessonEdit') }}
          </el-button>
        </dll-item>
      </div>
      <el-divider v-if="!(dlls.length-1 === index)" class="dll-divider"></el-divider>
    </div>
    <el-dialog @close="clearVideo"
               :title="resource.name"
               :visible.sync="editor.showDialog"
               width="1030px"
               top="8vh"
               append-to-body
               :close-on-click-modal="false">
      <el-form :model="editor.dll">
        <dll-input-panel v-model="editor.dll"
                         :languages="languages"
                         v-if="editor.dll"
                         :lang-codes="langCodes"
                         class="scrollbar-new"
                         ref="editPanel"/>
      </el-form>
      <div slot="footer" style="text-align: right">
        <el-button @click="editor.showDialog = false" plain>{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="confirm()">{{ $t('loc.save') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LessonMediaViewer from '@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer'
import DllInputPanel from "@/views/modules/lesson2/lessonLibrary/editor/dll/DllInputPanel";
import DLLApi from "@/api/dll/DLL";
import DllSpeaker from "@/views/modules/lesson2/lessonLibrary/editor/dll/DllSpeaker";
import DllItem from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/DllItem'

export default {
  name: 'LessonDll',
  props: [
    'dlls',// DLL 详细信息，格式参照课程详情
    'resource',
    'languages',
    'langCodes'
  ],
  components: {
    DllItem,
    DllInputPanel,
    LessonMediaViewer,
    DllSpeaker
  },
  data() {
    return {
      editor: {
        dll: null, // 编辑用的 DLL 格式参照课程编辑
        showDialog: false,
      }
    }
  },
  methods: {
    clearVideo() {
      let previous = window.lessonVideo;
      if (previous) {
        previous.pause();
      }
    },
    getLangOriginalName(langCode) {
      return this.languages.find(item => item.code === langCode).originalName;
    },
    getLangName(langCode) {
      return this.languages.find(item => item.code === langCode).name;
    },
    handleClickEdit(dll) {
      let {id, title, content, medias = [], languages = [], description} = dll;
      // 将 DLL 详情格式数据转换成编辑格式
      this.editor.dll = {
        id,
        title,
        content,
        media: medias[0] && {id: medias[0].mediaId, url: medias[0].mediaUrl, name: medias[0].fileName},
        languages: languages.map(({langCode, content}) => ({langCode, content})),
        description: description
      };
      this.editor.showDialog = true;
    },
    confirm() {
      if (!this.validate()) {
        return;
      }
      // 将编辑格式数据转换成详情格式数据
      let { id, title, content, media, languages = [], description } = this.editor.dll
      let relevantDLL = this.dlls.find(item => item.id === id)
      let dll = { ...relevantDLL }
      let dlls = [...this.dlls]
      let deletable = !content || !content.trim()
      let dllIndex = dlls.findIndex(item => item.id === dll.id)
      if (deletable) {
        dlls.splice(dllIndex, 1)
      } else {
        dlls.splice(dllIndex, 1, dll)
        dll.title = title
        dll.content = content
        dll.medias = media && [{ mediaId: media.id, mediaUrl: media.url, fileName: media.name }] || []
        dll.description = description
        dll.languages.forEach(language => {
          let { content } = languages.find(item => item.langCode === language.langCode);
          language.content = content
        })
      }
      let { planItemId } = this.resource
      DLLApi.setSubjects(planItemId, 'PLAN_ITEM_LESSON',
        dlls.map(item => ({ // 将详情格式数据转换成接口格式数据
          id: item.id,
          title: item.title,
          content: item.content,
          description: item.description,
          mediaIds: item.medias && item.medias[0] && [item.medias[0].mediaId] || [],
          languages: item.languages
            .map(language => ({
              langCode: language.langCode,
              content: language.content
            }))
        })))
        .then(() => {
          this.$message.success(this.$t('loc.lesson2LessonSave').toString());
          // 删除
          if (deletable) {
            this.dlls.splice(dllIndex, 1);
          } else {
            this.dlls.splice(dllIndex, 1, dll);
          }
          this.$emit('command', 'changeDLL', this.resource, this.dlls, dll, deletable);
          this.editor.showDialog = false;
        })
        .catch(error => {
          error.message && this.$message.error(error.message);
        })
    },
    validate() {
      let {status = true, dll, el} = this.$refs.editPanel && this.$refs.editPanel.validate() || {};
      if (!status) {
        el && this.$nextTick(() => {
          if (!this.isInViewPort(el)) {
            el.scrollIntoView(true);
          }
        })
        return false;
      }
      return true;
    },
    isInViewPort(element) {
      if (!element) {
        return false;
      }
      const viewWidth = window.innerWidth || document.documentElement.clientWidth;
      const viewHeight = window.innerHeight || document.documentElement.clientHeight;
      const {
        top,
        right,
        bottom,
        left,
      } = element.getBoundingClientRect();

      return (
        top >= 0 &&
        left >= 0 &&
        right <= viewWidth &&
        bottom <= viewHeight
      );
    },
    unfold(dll, index) {
      this.$set(this.dlls, index, {...dll, isUnfold: !dll.isUnfold})
    }
  }
}
</script>

<style scoped lang="less">
.dll-input-panel {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 16vh - 134px);
}

.dll-detail-body /deep/ & {
  padding-bottom: 10px;

  .selected-header {
    justify-content: start;

    & > :last-child {
      color: #10B3B7;
    }
  }
}

.el-dialog__wrapper /deep/ & {
  .el-dialog__header {
    padding-bottom: 20px;

    .el-dialog__title {
      font-size: 20px;
    }
  }

  .el-dialog__body {
    padding: 0 !important;
  }
}

.dll-input-panel /deep/ .el-form-item .el-form-item__label {
  line-height: 15px;
  margin-bottom: 0;
  font-weight: 700;
  padding-bottom: 10px;
}

.dll-edit-button /deep/ & {
  padding: 0;

  & > span:last-child {
    display: inline-flex;
    align-items: center;
    gap: 3px;
  }
}
</style>