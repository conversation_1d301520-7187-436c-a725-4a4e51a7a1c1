<!-- 展示某一天的所有 DLL，按课程分组 -->
<template>
  <div style="display: flex;flex-flow: column;gap:20px;">
    <div v-for="item in items">
      <div style="line-height: 1">
        <span class="font-size-16 m-b-xs display-b" style="font-weight: bolder">{{ item.resource.name }}</span>
      </div>
      <lesson-dll :dlls="item.dlls"
                  :resource="item.resource"
                  :languages="item.languages"
                  :langCodes="item.langCodes"
                  @command="handleCommand"/>
    </div>
  </div>
</template>

<script>
import LessonDll from '@/views/modules/lesson2/lessonPlan/components/FamilyResourcePreview/Dll/LessonDll'
import DLLApi from "@/api/dll/DLL";
import {deepEqual} from "@/utils/common";

export default {
  name: 'DllItem',
  props: [
    'resource',
  ],
  components: {
    LessonDll
  },
  data() {
    return {
      items: [], // 课程数组：resource 课程（周计划项）相关信息，dlls DLL，languages 语言基本信息，langCodes 语言设置
      langCodes: null,
      languages: null
    }
  },
  watch: {
    'resource.lessonDLLs': {
      immediate: true,
      async handler(value) {
        let [{langCodes, languages}] = value;
        let lessonDlls = await this.transform(this.resource.lessonDLLs, langCodes);
        if (!deepEqual(lessonDlls, this.items)) {
          this.items = lessonDlls;
          this.langCodes = langCodes;
          this.languages = languages;
        }
      }
    }
  },
  methods: {
    async transform(lessonDLLs, langCodes) {
      let result = [];
      let contents = {};// 需要翻译的内容
      lessonDLLs.forEach(({dlls, resource}, index) => {
        // 按课程去重, 合并内容不同的 DLL：图片、内容、翻译

        let item = result.find(temp => temp.resource.lessonId === resource.lessonId);
        let exist = !!item;
        if (!exist) {
          item = {...lessonDLLs[index], dlls: []};
        }
        for (const dll of dlls) {
          // 忽略无内容的或重复的 DLL
          if (!dll.content || item.dlls.some(temp => temp.content === dll.content)) {
            continue;
          }
          let languages = [];
          item.dlls.push({...dll, languages});
          for (const langCode of langCodes) {
            let language = dll.languages && dll.languages.find(item => item.langCode === langCode) || {langCode};
            languages.push(language);
            if (!language.content) { // 该语言未被翻译
              let langArray = contents[dll.content] = contents[dll.content] || [];
              langArray.push(langCode);
            }
          }
        }
        if (!exist && item.dlls.length) {
          result.push(item);
        }
      })
      // 进行翻译
      let translations = {};
      let keys = Object.keys(contents);
      let translationList = [];
      for (const content of keys) {
        translationList.push({content, list: DLLApi.translate(content, contents[content])});
      }
      for (const {content, list} of translationList) {
        (await list).forEach(item => {
          translations[content] = translations[content] || {};
          translations[content][item.langCode] = item;
        })
      }
      // 应用翻译结果
      if (keys.length) {
        result.forEach(item => item.dlls.forEach(dll => {
          let map = translations[dll.content] || {};
          dll.languages.filter(language => !language.content).forEach(language => {
            let {originalName, content} = map[language.langCode] || {};
            language.lang = originalName;
            language.content = content;
          })
        }))
      }
      return result;
    },
    handleCommand(action, ...args) {
      // 处理 dll 变更
      if (action === 'changeDLL') {
        let [planItem, dlls, , deleted] = args;
        // 修改不需处理：同天同课程，不同 plan item 修改时，修改该课程所有的 plan item，使同天同课程的 plan item 的 DLL 保持相同
        if (!deleted || dlls.length) {
          return;
        }
        // 删除了课程的所有 DLL：后端与修改同逻辑，前端移除该天的该课程
        let itemIndex = this.items.findIndex(temp => temp.resource.lessonId === planItem.lessonId);
        let item = this.items[itemIndex];
        this.items.splice(itemIndex, 1);
        this.$emit('command', 'deleteLessonDLL', this.resource, item.resource.lessonId);
      }
    }
  }
}
</script>

<style scoped lang="less">

</style>