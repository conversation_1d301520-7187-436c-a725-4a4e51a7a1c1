<template>
  <div>
    <img :src="fileIcon" class="resource-img" alt=""/>
    <span style="margin-right: 10px;">
      {{ file.sourceFileName }}
    </span>
    <el-button size="medium" type="text">
      <a :href="file.url" :download="file.sourceFileName" target="_blank">
        {{ $t('loc.download') }}
      </a>
    </el-button>
  </div>
</template>

<script>
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'

export default {
  name: "AttachmentItem",
  props: ['file'],
  computed: {
    fileIcon() {
      let name = this.file.sourceFileName;
      let extension = name.substring(name.lastIndexOf('.') + 1);
      return this.fileIcons[extension] || file;
    }
  },
  data() {
    return {
      fileIcons: {doc, docx, pdf, ppt, pptx, xls, xlsx, file}
    }
  }
}
</script>

<style scoped lang="less">
.resource-img {
  display: inline-block;
  width: 16px;
  height: 18px;
  vertical-align: text-bottom;
  margin-right: 10px;
}
</style>