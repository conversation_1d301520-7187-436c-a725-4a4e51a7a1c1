<template>
  <el-row :gutter="10" type="flex" style="align-items: center">
    <el-col :span="4">
      <div class="video" @click="preview">
        <lesson-media-viewer :url="thumbnailUrl" type="image" failed-type="video">
          <template slot-scope="{failed}" v-if="!failed">
            <el-image :src="require('@/assets/img/lesson2/standard/video_play.png')"
             class="video-icon"/>
          </template>
        </lesson-media-viewer>

      </div>
      <!--视频预览弹框-->
      <el-dialog :visible.sync="showPlayer" width="691px" class="popup-container" append-to-body>
        <div slot="title" style="height: 21px;font-size: 24px">{{ $t('loc.preview') }}</div>
        <iframe :src="showPlayer && `https://www.youtube.com/embed/${video.id.videoId}`" v-if="video"
                width="647px" height="428px" allowfullscreen/>
        <div slot="footer">
          <el-button @click="closePreview">{{ $t('loc.close') }}</el-button>
        </div>
      </el-dialog>
    </el-col>
    <el-col :span="20">
      <span>{{ title }}</span>
    </el-col>
  </el-row>
</template>

<script>
import LessonMediaViewer from "@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer";

export default {
  name: "VideoBook",
  components: {LessonMediaViewer},
  props: ['video'],
  computed: {
    thumbnailUrl() {
      let {snippet: {thumbnails: {default: {url}}}} = this.video;
      return url
    },
    title() {
      let {snippet = {}} = this.video || {};
      let {title} = snippet
      return title;
    }
  },
  data() {
    return {
      showPlayer: false
    }
  },
  methods: {
    preview() {
      if (window.lessonVideo) {
        window.lessonVideo.pause();
      }
      this.showPlayer = true;
    },
    closePreview() {
      this.showPlayer = false;
    },
  }
}
</script>

<style scoped>
.video {
  cursor: pointer;
  display: flex;
  flex-flow: column;
  justify-content: center;
  gap: 5px;
  font-size: 14px;
  position: relative;
}
.video-icon{
  position: absolute;
  top: calc(50% - 24px);
  left: calc(50% - 24px);
  max-height: 48px;
  max-width: 48px;
}
</style>