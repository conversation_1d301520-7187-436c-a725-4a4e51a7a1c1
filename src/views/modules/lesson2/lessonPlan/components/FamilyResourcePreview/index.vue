<template>
  <div class="family-resource-preview">
    <!-- 预览按钮 -->
    <el-button style="font-weight: normal;color: #111c1c" size="medium" @click="handlePreview" :loading="loading">
      {{ $t('loc.planResourcePreview') }}
    </el-button>
    <!-- 预览弹窗 -->
    <el-dialog :visible.sync="dialogVisible"
               width="830px"
               top="8vh"
               :append-to-body="true"
               @close="handleClose"
               :close-on-click-modal="false">
      <div style="font-size: 20px;font-family: 'Source Sans Pro';color: #000" slot="title">
        {{ $t('loc.planResourcePreview') }}
      </div>
      <div style="display: flex;flex-flow: column;max-height: calc(100vh - 16vh - 56px - 67px)">
        <div style="padding: 0 20px;flex:none;" class="m-b-sm font-size-16">
          {{ $t('loc.planResourcePreviewTips') }}
        </div>
        <weekly-share-content class="scrollbar-new"
                              :resources="resources"
                              v-if="resources && resources.length"
                              @command="handleCommand"/>
      </div>
      <div class=" flex-column-center height-400 w-full white-background"
           v-if="resources && !resources.length">
        <img src="@/assets/img/lesson2/dataReview/empty.png" alt=""/>
        <span class="lg-color-text-secondary font-size-14">{{ $t('loc.planResourcePreviewNoData') }}</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WeeklyShareContent from './WeeklyShareContent'
import LessonApi from '@/api/lessons2'
import DLLApi from '@/api/dll/DLL'

export default {
  name: 'FamilyResourcePreview',
  props: [
    'plan',
    'beforePreview'
  ],
  components: {
    WeeklyShareContent
  },
  watch: {
    'plan.groupId': {
      immediate: true,
      async handler (value) {
        if (value) {
          let { selectList = [], unSelectList = [] } = await DLLApi.listGroupLanguages(value)
          this.languages = [...selectList, ...unSelectList]
          this.langCodes = selectList.map(item => item.code)
        }
      }
    },
    'dllContext.langCodes': {
      handler (value) {
        this.langCodes = value || []
      }
    }
  },
  inject: ['dllContext'],
  data () {
    return {
      resources: null,
      loading: false,
      dialogVisible: false,
      languages: null,
      langCodes: []// 班级 DLL 语言
    }
  },
  methods: {
    async handlePreview () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_dll_review')
      this.loading = true
      if (this.beforePreview) {
        await this.beforePreview()
      }
      let { resources = [] } = await LessonApi.listEngagementResources(this.plan.id)
      this.resources = this.transformData(resources)
      this.loading = false
      this.dialogVisible = true
    },
    transformData (resources) {
      let { fromAtLocal, toAtLocal } = resources[0] || {}
      if (!fromAtLocal || !toAtLocal) {
        return []
      }
      return resources
        .reduce((prev, current) => {
          let day = prev[current.dayOfWeek] = prev[current.dayOfWeek] || {}
          day.resources = day.resources || []
          day.resources.push(current)
          return prev
        }, [])
        .filter(item => item && item.resources && item.resources.length)
        .map(item => {
          item.date = this.calculateDate(fromAtLocal, item.resources[0].dayOfWeek)
          item.books = []
          item.videoBooks = []
          item.attachments = []
          item.lessonDLLs = []
          for (const resource of item.resources) {
            // 书
            if (resource.books) {
              resource.books.forEach(book => {
                if (item.books.every(itemBook => itemBook.book.id !== book.id)) {
                  item.books.push({ book, resource })
                }
              })
            }
            // 视频书
            if (resource.videoBooks) {
              resource.videoBooks.forEach(videoBook => {
                if (item.videoBooks.every(itemVideoBook => itemVideoBook.videoBook.id.videoId !== videoBook.id.videoId)) {
                  item.videoBooks.push({ videoBook, resource })
                }
              })
            }
            // 附件
            resource.attachmentMedias && resource.attachmentMedias.length &&
              item.attachments.every(attachment => attachment.resource.lessonId !== resource.lessonId) &&
            item.attachments
              .push({ resource, attachmentMedias: resource.attachmentMedias })
            // DLL
            resource.dlls && resource.dlls.length && this.langCodes.length && item.lessonDLLs
              .push({ resource, dlls: resource.dlls, langCodes: this.langCodes, languages: this.languages })
          }
          item.entryCount = item.books.length + item.videoBooks.length +
            item.attachments.length + item.lessonDLLs.length
          return item
        })
        .filter(item => item.entryCount)
    },
    /**
     * 计算周计划中第 n 天的日期
     */
    calculateDate (date, dayOfWeek) {
      return this.$moment(date).add(dayOfWeek - 1 , 'days').toDate()
    },
    handleClose () {
      window.lessonVideo && window.lessonVideo.pause()
    },
    // 命令处理器，用于处理子组件通知的事件
    handleCommand (action, ...args) {
      // 某一天的资源都被删除
      if (action === 'delete') {
        let [dayResource] = args
        let index = this.resources.indexOf(dayResource)
        if (index > -1) {
          this.resources.splice(index, 1)
        }
      }
    }
  }
}

</script>

<style lang="less" scoped>
.family-resource-preview /deep/ & {
  display: inline-block;
  text-align: left;

  & > .el-dialog__wrapper > .el-dialog > .el-dialog__body {
    padding: 0 !important;
  }
}

.weekly-share-preview {
  padding: 1px 20px;
  overflow-y: auto;
  scrollbar-gutter: stable;
}
</style>
