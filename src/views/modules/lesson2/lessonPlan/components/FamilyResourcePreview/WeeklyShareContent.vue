<template>
  <div class="weekly-share-preview">
    <el-collapse v-model="itemNames">
      <el-collapse-item v-for="resource in resources" :key="resource.date.getTime()" :name="resource.date.getTime()"
                        style="font-size: 16px;">
        <template slot="title">
          <span style="font-size: 16px;font-weight: bolder">
            {{ $moment(resource.date).format('dddd (MM/DD)') }}
          </span>
          <span style="color:#777;" class="font-size-14">{{$t('loc.lessons2AddDLLTotal')}} {{ resource.entryCount }}</span>
        </template>
        <!--书-->
        <el-row v-if="resource.books.length">
          <el-col :span="2">
            <div style="text-align: center">
              <img src="@/assets/img/lesson2/Book.png" style="border: 0" alt=""/>
              <div class="font-size-14">{{ $t("loc.book") }}</div>
            </div>
          </el-col>
          <el-col :span="22" style="display: flex;flex-flow: column;gap: 10px;">
            <div v-for="(book,index) in resource.books" :key="book.resource.planItemId">
              <book :book="book.book" />
              <el-divider v-if="!(index === resource.books.length-1)" class="dll-divider-item"></el-divider>
            </div>
          </el-col>
        </el-row>
        <el-divider v-if="resource.books.length && (resource.videoBooks.length || resource.attachments.length || resource.lessonDLLs.length)" class="dll-divider"></el-divider>
        <!--视频书-->
        <el-row v-if="resource.videoBooks.length">
          <el-col :span="2">
            <div style="text-align: center">
              <img src="@/assets/img/lesson2/Video_Book.png" style="border: 0" alt=""/>
              <div class="font-size-14">{{ $t("loc.videoBook") }}</div>
            </div>
          </el-col>
          <el-col :span="22" style="display: flex;flex-flow: column;gap: 10px;">
            <div v-for="(video,index) in resource.videoBooks" :key="video.resource.planItemId">
              <video-book :video="video.videoBook"/>
              <el-divider v-if="!(index === resource.videoBooks.length-1)" class="dll-divider-item"></el-divider>
            </div>
          </el-col>
        </el-row>
        <el-divider v-if="resource.videoBooks.length && (resource.attachments.length || resource.lessonDLLs.length)" class="dll-divider"></el-divider>
        <!--活动-->
        <el-row v-if="resource.attachments.length">
          <el-col :span="2">
            <div style="text-align: center">
              <img src="@/assets/img/lesson2/activity.png" style="border: 0" alt=""/>
              <div class="font-size-14">{{ $t("loc.activity") }}</div>
            </div>
          </el-col>
          <el-col :span="22" style="display: flex;flex-flow: column;gap: 10px;">
            <div v-for="(attachment,index) in resource.attachments" :key="attachment.resource.planItemId">
              <activity :attachment="attachment"/>
              <el-divider v-if="!(index === resource.attachments.length-1)" class="dll-divider-item"></el-divider>
            </div>
          </el-col>
        </el-row>
        <el-divider v-if="resource.attachments.length && resource.lessonDLLs.length" class="dll-divider"></el-divider>
        <!--dll-->
        <el-row v-if="resource.lessonDLLs.length">
          <el-col :span="2">
            <div style="text-align: center">
              <img src="@/assets/img/lesson2/DLL.png" style="border: 0" alt=""/>
              <div class="font-size-14">{{ $t('loc.DLL') }}</div>
            </div>
          </el-col>
          <el-col :span="22">
            <lesson-dll-list :resource="resource" @command="handleCommand"/>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import LessonDllList from "./Dll/LessonDllList";
import Activity from "./Activity";
import Book from './Book'
import VideoBook from "./VideoBook";

export default {
  name: "WeeklyShareContent",
  props: [
    'resources',
  ],
  components: {
    Activity,
    LessonDllList,
    Book,
    VideoBook
  },
  data() {
    return {
      itemNames: [],
    }
  },
  watch: {
    resources: {
      immediate: true,
      handler() {
        this.itemNames = (this.resources || []).map(item => item.date.getTime());
      }
    },
  },
  methods:{
    // 子组件事件处理器
    handleCommand(action, ...args) {
      // 删除了某个课程的全部 DLL
      if (action === 'deleteLessonDLL') {
        let [dayResource, lessonId] = args;
        let planItems = dayResource.lessonDLLs.filter(item => item.resource.lessonId === lessonId);
        planItems.forEach(item => {
          dayResource.lessonDLLs.splice(dayResource.lessonDLLs.indexOf(item), 1);
        })
        dayResource.entryCount -= planItems.length;
        if (dayResource.entryCount <= 0) {
          this.$emit('command', 'delete', dayResource);
        }
      }
    }
  }
}
</script>

<style scoped lang="less">

.weekly-share-preview /deep/ & {

  & > .el-collapse {
    border: 0 !important;

    & > .el-collapse-item {
      & > .el-collapse-item__wrap {
        border: 0 !important;
        & > .el-collapse-item__content {
          display: flex;
          flex-flow: column;
        /*  gap: 20px;*/
          padding: 20px 0;
          font-size: 16px;
        }
      }

      & > * {
        & > .el-collapse-item__header {
          display: grid;
          grid-template-columns: max-content 1fr max-content;
          border: 0 !important;
          padding: 0 10px;
          background-color: #f5f6f8;

          & > .el-collapse-item__arrow {
            order: -1;
          }
        }
      }
    }
  }
}

.dll-divider{
  background-color: #F2F6FC;
}

.dll-divider-item{
  margin: 10px 0 0;
  background-color: #F2F6FC;
}
</style>