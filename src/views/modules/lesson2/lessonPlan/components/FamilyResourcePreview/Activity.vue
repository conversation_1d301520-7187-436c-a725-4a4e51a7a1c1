<template>
  <div>
    <div>Family Resources Associated with Each Lesson</div>
    <el-popover placement="bottom-start" trigger="click" v-model="show">
      <el-link :underline="false" type="primary" slot="reference">
        {{ attachment.attachmentMedias.length + ' ' + (attachment.attachmentMedias.length>1 && $t('loc.lessons2LessonAttachments') || $t('loc.attachment')) }}
        <el-icon :class="[`el-icon-arrow-${show ? 'down' : 'right'}`]"></el-icon>
      </el-link>
      <attachment-item :file="file" v-for="(file,index) in attachment.attachmentMedias" :key="file.id"/>
    </el-popover>
  </div>
</template>

<script>

import AttachmentItem from "./AttachmentItem";
export default {
  name: 'ActivityList',
  props: ['attachment'],
  components: {
    AttachmentItem
  },
  data() {
    return {
      show: false
    }
  }
}
</script>

<style scoped>

</style>