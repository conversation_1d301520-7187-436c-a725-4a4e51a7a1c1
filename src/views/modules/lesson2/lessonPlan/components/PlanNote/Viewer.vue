<template>
  <el-card shadow="never" v-if="note" class="box-card">
    <el-row class="lg-scrollbar-show" style="overflow-y: auto;max-height: 360px;padding-right: 20px;padding-top: 10px;">
      <el-col :span="22">
        <div v-html="_content" style="padding-right: 10px;width:calc(100% + 10px);"></div>
      </el-col>
      <el-col :span="2">
        <!-- 标题 -->
        <div class="clearfix font-size-16 add-margin-b-8">
          <!-- <i class="el-icon-s-management text-default"/> -->
          <!-- <span class="font-bold m-l-xs text-black-mon">{{ $t('loc.planNoteTitle') }}</span> -->
          <div class="pull-right">
            <el-dropdown @command="handleMenuItemClicked">
              <el-link :underline="false" icon="el-icon-more"/>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">
                  <i class="el-icon-edit m-r-sm"/>
                  {{ $t('loc.edit') }}
                </el-dropdown-item>
                <el-dropdown-item command="delete">
                  <i class="el-icon-delete m-r-sm"/>
                  {{ $t('loc.delete') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog width="650px" :title="$t('loc.planNoteEditTitle')" :visible.sync="showDialog" append-to-body
               custom-class="lesson-plan-note-editor-dialog">
      <el-input v-model="content" type="textarea" :autosize="{minRows:7}" :placeholder="$t('loc.planNoteAddTips')"
                maxlength="10000" ref="noteInput"/>
      <div slot="footer">
        <el-button size="medium" plain @click="cancel">{{ $t('loc.cancel') }}</el-button>
        <el-button size="medium" type="primary" @click="save" :disabled="saveDisabled || !this.content" native-type="button">
          {{ $t('loc.save') }}
        </el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import LessonApi from '@/api/lessons2'

export default {
  name: "PlanNoteViewer",
  props: [
    'note',
    'planId'
  ],
  computed: {
    _content() {
      let content = this.note && this.note.content || '';
      return content.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ')
    }
  },
  data() {
    return {
      content: '',
      showDialog: false,
      saveDisabled: false
    }
  },
  watch: {
    planId: {
      immediate: true,
      handler (value) {
        value && LessonApi.listNotes(value, 1, 1)
          .then(page => this.$emit('update:note', page.items[0]))
      }
    }
  },
  methods: {
    handleMenuItemClicked (command) {
      if (command === 'edit') {
        this.content = this.note && this.note.content || ''
        this.showDialog = true
        this.$nextTick(() => {
          this.$refs.noteInput && this.$refs.noteInput.focus()
        })
        return
      }
      if (command === 'delete') {
        this.deleteNote()
      }
    },
    cancel () {
      this.showDialog = false
    },
    save () {
      this.$analytics.sendEvent('web_weekly_virtual_detail_add_note')
      let content = this.content && this.content.trim()
      if (!content) {
        this.$message.error('Note content can not be empty')
        return
      }
      this.saveDisabled = true
      let note = { ...this.note, content }
      LessonApi.addOrUpdateNote(note)
        .then(note => {
          this.$message.success('Saved successfully')
          this.$emit('update:note', note)
          this.showDialog = false
        })
        .catch(error => {
          error && error.message && this.$message.error(error.message)
        })
        .finally(() => this.saveDisabled = false)
    },
    deleteNote () {
      this.$confirm(this.$t('loc.planNoteDeleteConfirmTips'), {
        type: 'warning',
        title: this.$t('loc.confirmation'),
        cancelButtonText: this.$t('loc.cancel'),
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        callback: action => {
          if (action === 'confirm') {
            LessonApi.deleteNote(this.note.id)
              .then(() => {
                this.$message.success(this.$t('loc.deletedSuccessfully').toString())
                this.$emit('update:note', null);
              })
              .catch(error => {
                error && error.message && this.$message.error(error.message);
              });
          }
        }
      })
    }
  }
}
</script>
<style lang="less">
.lesson-plan-note-editor-dialog .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
</style>
<style scoped>
.el-card {
  border: none;
  border-radius:0;
}
.el-card /deep/ .el-card__body {
  padding-right: 0 !important;
  padding-top: 0 !important;
}
</style>