<template>
  <div class="m-l-sm m-r-sm font-size-14">
    <div v-infinite-scroll="loadPage" style="max-height: 360px;overflow-y: auto;" class="scrollbar-new">
      <div v-for="(note, index) in notes" :key="index">
        <div v-html="formatContent(note)"></div>
        <span class="font-color-gray">
          Created on {{
            $moment.utc(note.createAtUtc).local().format('MM/DD/YYYY hh:mm A')
          }}
        </span>
      </div>
    </div>
    <!-- 没有笔记 -->
    <div class="display-flex justify-content align-items flex-direction-col m-t-md" v-if="!loading && !notes.length">
      <span class="lg-color-text-secondary">{{ $t('loc.planNoteNoData') }}</span>
    </div>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'

export default {
  name: 'PlanNoteListViewer',
  props: [
    'note',
    'planId'
  ],
  data () {
    return {
      notes: [],
      pageNum: 1,
      pageSize: 5,
      total: 0,
      ended: false,
      loading: false
    }
  },
  methods: {
    formatContent (note) {
      let content = note && note.content || ''
      return content.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ')
    },
    loadPage () {
      if (this.ended || this.loading) {
        return
      }
      this.loading = true
      LessonApi.listNotes(null, this.pageSize, this.pageNum)
        .then(page => {
          let { total, items } = page
          this.pageNum++
          this.total = total
          this.notes = [...this.notes, ...items];
          this.ended = this.notes.length >= total
        })
        .catch(() => this.ended = true)
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="less">
.lesson-plan-note-editor-dialog .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
</style>
<style scoped>

</style>
