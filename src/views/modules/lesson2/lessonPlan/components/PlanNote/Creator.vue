<template>
  <div v-if="!note" style="display: inline-block">
    <el-button icon="el-icon-plus" @click="addNoteClickHandler">{{ $t('loc.planNoteAdd') }}</el-button>
    <el-dialog width="650px" title="Notes" :visible.sync="showDialog" append-to-body
               custom-class="lesson-plan-note-editor-dialog">
      <el-input v-model="content" type="textarea" :autosize="{minRows:7}" :placeholder="$t('loc.planNoteAddTips')"
                maxlength="10000" ref="noteInput"/>
      <div slot="footer">
        <el-button style="height: 40px;" size="small" plain @click="cancel">{{ $t('loc.cancel') }}</el-button>
        <el-button style="height: 40px;" size="small" type="primary" @click="save" :disabled="saveDisabled" native-type="button">
          {{ $t('loc.save') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'

export default {
  name: 'PlanNoteCreator',
  props: [
    'planId',
    'note'
  ],
  data () {
    return {
      content: '',
      showDialog: false,
      saveDisabled: false
    }
  },
  methods: {
    addNoteClickHandler () {
      this.content = ''
      this.showDialog = true
      this.$nextTick(() => {
        this.$refs.noteInput && this.$refs.noteInput.focus()
      })
    },
    cancel () {
      this.showDialog = false
    },
    save () {
      let content = this.content && this.content.trim()
      if (!content) {
        this.$message.error('Note content can not be empty')
        return
      }
      this.saveDisabled = true
      LessonApi.addOrUpdateNote({ content, planId: this.planId })
        .then(note => {
          this.$message.success(this.$t('loc.plan39').toString())
          this.$emit('update:note', note)
          this.showDialog = false
        })
        .catch(error => {
          error && error.message && this.$message.error(error.message)
        })
        .finally(() => this.saveDisabled = false)
    }
  }
}
</script>
<style lang="less">
.lesson-plan-note-editor-dialog {
  border-radius: 8px;
}
.lesson-plan-note-editor-dialog .el-dialog__header .el-dialog__title {
  font-size: 20px;
  font-weight: bold;
  color: #111c1c;
}
.lesson-plan-note-editor-dialog .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
</style>
<style scoped lang="less">
</style>
