<template>
  <div class="reflection-detail-list" :class="{'lg-pa-10': !showHeader, 'lg-py-10': showHeader}" v-show="reflections.length > 0" v-loading="loading">
    <!-- 标题 -->
    <div class="lg-py-10" v-if="showHeader">
      <span class="font-size-16 font-bold">{{$t('loc.plan89')}} ({{reflections.length}})</span>
    </div>
    <!-- 其他反思标题 -->
    <div class="bg-light lg-pa-10 header2" v-if="showHeader2">
      <span class="font-size-16 font-bold text-primary">{{$t('loc.plan81')}}</span>
    </div>
    <!-- 没有反思 -->
    <div class="display-flex justify-content align-items flex-direction-col" v-if="!loading && reflections.length === 0">
      <img class="w-64" src="@/assets/img/lesson2/dataReview/empty.png"/>
      <span class="lg-color-text-secondary">{{$t('loc.lessons2NoResult')}}</span>
    </div>
    <!-- 反思列表 -->
    <div v-for="(reflection, index) in reflections" :key="index">
      <!-- 反思详情 -->
      <reflection-detail
        :reflection="reflection"
        :lessonId="lessonId"
        :planId="excludePlanId"
        :showEdit="showEdit"
        @callDeleteReflection="deleteReflection"
        :style="{'padding-left': showHeader ? '0' : '10px'}">
      </reflection-detail>
      <!-- 观察记录 - 折叠 -->
      <div v-show="!reflection.showNotes" class="text-center b-a lg-pointer add-margin-lr-10" @click="foldNotes(reflection)">
        <span>{{$t('loc.plan82')}}</span><i class="el-icon-arrow-down m-l-xs"></i>
      </div>
      <!-- 观察记录 - 展开 -->
      <div v-if="reflection.showNotes" class="observation-detail lg-margin-right-24" :class="{'sub-list bg-light b-a': subList}">
        <reflection-observation-detail
          :lessonId="lessonId"
          :objectId="reflection.reflectionObjectId"
          :subList="subList"
          :hideHeader="true">
        </reflection-observation-detail>
        <div v-show="reflection.showNotes" class="lg-pa-10 text-center b-a lg-pointer bg-white" @click="foldNotes(reflection)">
          <span>{{$t('loc.plan83')}}</span><i class="el-icon-arrow-up m-l-xs"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import ReflectionDetail from './ReflectionDetail'
import ReflectionObservationDetail from './ReflectionObservationDetail'

export default {
  name: 'ReflectionDetailList',
  components: {
    ReflectionDetail,
    ReflectionObservationDetail
  },

  props: {
    excludePlanId: {
      type: String
    },
    lessonId: {
      type: String
    },
    subList: {
      type: Boolean
    },
    showHeader: {
      type: Boolean
    },
    showHeader2: {
      type: Boolean
    },
    showEdit: {
      type: Boolean
    }
  },

  data () {
    return {
      reflections: [],
      showNotes: false,
      loading: true
    }
  },

  created () {
  },

  methods: {
    loadReflections () {
      this.loading = true
      LessonApi.listLessonReflections({
        excludePlanId: this.excludePlanId,
        lessonId: this.lessonId
      }).then(res => {
        this.reflections = res.reflections
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    },

    foldNotes (reflection) {
      this.$set(reflection, 'showNotes', !reflection.showNotes)
    },

    deleteReflection (itemId) {
      this.$emit('callDeleteReflection', itemId, true)
    }
  },

  watch: {
    lessonId: {
      immediate: true,
      handler() {
        this.lessonId && this.loadReflections()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.sub-list {
  /deep/ .reflect-observation-detail {
    background-color: #f3f5f6;
  }
}
.header2 {
  margin: 0 -10px;
}

/deep/ .note-blank {
  img {
    display: none;
  }
}
</style>