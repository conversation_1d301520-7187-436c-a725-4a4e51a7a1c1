<template>
  <el-card shadow="never" class="box-card plan-toolbar-card" v-show="records && records.length > 0">
    <div class="lg-scrollbar-show" style="overflow-y: auto;max-height: 360px;padding-right: 20px;padding-top: 10px;">
      <!-- 分享记录 -->
      <div v-for="(record, index) in records" :key="index">
        <!-- 有评语 -->
        <template v-if="record.comment && record.comment.trim().length > 0">
          <!-- 评语 -->
          <div class="m-b-xs space-pre-line">
            <span>{{record.comment}}</span>
          </div>
        </template>
        <div class="display-flex align-items">
          <!-- 头像 -->
          <el-avatar :size="30" :src="record.createUserAvatar" shape="circle" class="m-r-sm flex-none"></el-avatar>
          <!-- 分享日期 -->
          <div class="flex-auto" :class="{'text-success-dark font-bold': !hasComment, 'lg-color-text-secondary': record.comment && record.comment.trim().length > 0}">
            {{recordInfo(record)}}
          </div>
        </div>
        <!-- 分割线 -->
        <el-divider v-if="index != records.length - 1"></el-divider>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'ShareRecordCard',
  components: {
  },

  props: {
    records: {
      type: Array
    }
  },

  data () {
    return {
      hasComment: false
    }
  },

  created () {
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    })
  },

  methods: {
    recordInfo (record) {
      if (!record) {
        return ''
      }
      let sharedAtUtc = record.sharedAtUtc
      let date = this.$moment.utc(sharedAtUtc).local().format('MM/DD/YYYY')
      return this.$t('loc.plan94', { userName: this.formatUserName(record.createUsername), date: date })
    },
    formatUserName (name) {
      if (name.indexOf('Ms.') === 0) {
        let separator = 'Ms.'
        if (name.replace(separator, '').charAt(0) !== ' ') {
          name = separator + ' ' + name.replace(separator, '')
        }
      } else if (name.indexOf('Mr.') === 0) {
        let separator = 'Mr.'
        if (name.replace(separator, '').charAt(0) !== ' ') {
          name = separator + ' ' + name.replace(separator, '')
        }
      }
      return name
    },
    // 是否有带评论的记录
    checkHasComment () {
      if (!this.records || this.records.length === 0) {
        return false
      }
      let hasComment = false
      this.records.forEach(r => {
        if (r.comment && r.comment.trim().length > 0) {
          hasComment = true
        }
      })
      return hasComment
    }
  },

  watch: {
    // records () {
    //   this.hasComment = this.checkHasComment()
    // }
  }
}
</script>

<style lang="less" scoped>
.share-content {
  max-height: 170px;
  overflow: auto;
}

/deep/ .el-divider--horizontal {
  margin: 10px 0;
}
.el-card {
  border: none;
  border-radius:0;
}
.el-card /deep/ .el-card__body {
  padding-right: 0 !important;
  padding-top: 0 !important;
}
</style>
