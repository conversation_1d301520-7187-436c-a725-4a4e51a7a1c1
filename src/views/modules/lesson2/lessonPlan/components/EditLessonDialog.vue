<template>
  <div>
    <!-- 编辑课程弹窗 -->
    <el-dialog
        :title="$t('loc.lessons2EditLessonTitle')"
        :append-to-body="true"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="cancelEditLesson">
        <div class="lg-scrollbar-show h-full">
            <!-- 复制课程副本时 loading的骨架屏 -->
            <lesson-detail-skeleton class="lesson-detail-skeleton" v-if="loading"></lesson-detail-skeleton>
            <!-- 课程编辑页面 -->
            <lesson-editor v-else ref="lessonEditor" @updateWeeklyLessonId="updateWeeklyLessonId" :lessonId="editLessonId" :inDialog="true"></lesson-editor>
        </div>
        <!-- 底部操作按钮 -->
        <span slot="footer" class="dialog-footer">
            <div :class="{'display-flex flex-space-between align-items' : showRecommendCheckbox}">
                <el-checkbox v-if="showRecommendCheckbox" v-model="recommendToAgency">{{ $t('loc.saveToAgencyWideLesson') }}</el-checkbox>
                <div>
                    <el-button :disabled="loading" plain @click="cancelEditLesson">{{ $t('loc.cancel') }}</el-button>
                    <el-button :disabled="loading" type="primary" @click="publishLesson">{{ $t('loc.lessons2Save') }}</el-button>
                </div>
            </div>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import LessonEditor from '@/views/modules/lesson2/lessonLibrary/editor/index.vue'
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton'
import LessonApi from '@/api/lessons2'
import { mapState } from 'vuex'
import { getRecommendToAgencyKey } from '@/utils/common'

export default {
    name: 'EditLessonDialog',
    components: {
        LessonEditor,
        LessonDetailSkeleton
    },
    props: {
        // 当前的课程 id
        lessonId: {
            type: String
        }
    },
    data () {
        return {
            dialogVisible: false, // 弹窗是否显示
            loading: true, // 课程加载中
            editLessonId: '', // 编辑的课程 id
            isMyLesson: false, // 是否是我的课程
            recommendToAgency: false, // 保存到机构课程库
            hasUDLOrCLRData: true, // 当前 lessonId 对应的课程是否有 UDL 或 CLR 数据
            affiliationItemId: null // 课程所属 item 的 Id
        }
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser,
            open: state => state.common.open
        }),
        /**
         * 推荐到机构课程库开关
         */
        recommendToAgencyOpen () {
            return this.open && this.open.recommendLessonToAgencyOpen && !this.open.educator
        },
        /**
         * 是否是管理员
         */
        isAdmin () {
            if (!this.currentUser) {
                return false
            }
            let role = this.currentUser.role
            return role && role.toUpperCase() === 'OWNER'
        },
        /**
         * 是否显示保存到机构课程库的复选框
         */
        showRecommendCheckbox: {
            get () {
                return !this.isAdmin && this.recommendToAgencyOpen && !this.isMyLesson && !this.hasUDLOrCLRData
            }
        }
    },
    created () {
        // 使用 this.$bus.on 监听事件 lessonLibraryEditorIndexMounted
        // 当事件被触发时，获取 this.$refs.lessonEditor.hasUDLOrCLRData 的值
        // 并将值赋给 this.hasUDLOrCLRData
        this.$bus.$on('lessonLibraryEditorIndexMounted', (hasUDLOrCLRData) => {
            this.hasUDLOrCLRData = hasUDLOrCLRData
        })
    },
    watch: {
        /**
         * 监听推荐到机构课程库的复选框,给编辑器组件赋值
         */
        recommendToAgency (val) {
            this.$nextTick(() => {
                if (this.$refs.lessonEditor) {
                    this.$refs.lessonEditor.recommendToAgency = val
                }
            })
        },
        /**
         * 设置是否显示推荐课程的复选框以及默认值
         */
        showRecommendCheckbox (val) {
            if (val) {
                var defaultValue = localStorage.getItem(getRecommendToAgencyKey())
                this.recommendToAgency = defaultValue !== 'false'
            }
        }
    },
    methods: {
        /**
         * 取消编辑课程
         */
        cancelEditLesson () {
            this.$confirm(this.$t('loc.plan179'), this.$t('loc.confirmation'), {
                confirmButtonText: this.$t('loc.confirm'),
                cancelButtonText: this.$t('loc.cancel')
            })
            .then(() => {
                if (this.isMyLesson) {
                    this.editLessonId = ''
                    this.dialogVisible = false
                } else {
                    LessonApi.deleteLessonPermanently(this.editLessonId)
                    .then(() => {
                        this.editLessonId = ''
                        this.dialogVisible = false
                    })
                }
                // 取消编辑后刷新组件
                this.$emit('refreshComponent')
            })
            .catch(() => {})
        },
        /**
         * 更新课程 id
         */
        updateWeeklyLessonId (lessonInfo) {
            if (lessonInfo.oldLessonId !== lessonInfo.newLessonId) {
                this.$bus.$emit('updateWeeklyLessonId', {
                    oldLessonId: lessonInfo.oldLessonId,
                    newLessonId: lessonInfo.newLessonId,
                    affiliationItemId: this.affiliationItemId
                })
            }
        },
        /**
         * 加载编辑的课程
         */
        loadLesson (isMyLesson, affiliationItemId) {
            // 打开编辑弹窗，如果是编辑我的课程，直接加载
            this.dialogVisible = true
            if (isMyLesson) {
                this.isMyLesson = true
                this.editLessonId = this.lessonId
                this.loading = false
                // 课程加载完毕后，增加回调，通知父组件
                this.$nextTick(() => {
                    // 接受父组件传递的课程所属 item 的 Id
                    this.affiliationItemId = affiliationItemId
                    this.loadingLessonAfter()
                })
            } else {
                // 如果不是我的课程，先复制课程副本，再加载
                LessonApi.copyLesson(this.lessonId)
                .then(res => {
                    this.editLessonId = res.id
                    this.loading = false
                    // 课程加载完毕后，增加回调，通知父组件
                    this.$nextTick(() => {
                        // 接受父组件传递的课程所属 item 的 Id
                        this.affiliationItemId = affiliationItemId
                        this.loadingLessonAfter()
                    })
                })
            }
        },
        /**
         * 发布到课程库
         */
        async publishLesson () {
            let lesson = await this.$refs.lessonEditor.publishLesson()
            if (lesson) {
                this.dialogVisible = false
                this.$emit('saveLesson', lesson)
                this.$analytics.sendEvent('web_weekly_plan_edit_click_lesson_publish')
            }
        },

        /**
         * 通知父组件课程加载完毕
         */
        loadingLessonAfter () {
            this.$emit('loadingLessonAfter')
        }
    }

}
</script>

<style lang="less" scoped>

@media screen and (max-width:1599px) {
    .lesson-detail-skeleton {
        margin: 10px 0;
        width: 1100px;
        padding: 0 20px;
    }
    /deep/ .new-lesson>.content {
        padding: 0 20px;
    }
}

@media screen and (min-width: 1600px) {
    .lesson-detail-skeleton {
        width: 1300px;
        padding: 0 50px;
        margin-top: 30px;
    }
    /deep/ .new-lesson>.content {
        padding: 0 50px;
    }
}
/deep/ .el-dialog {
    width: fit-content;
    height: calc(100% - 10vh) !important;
    margin-top: 4vh !important;
    overflow: hidden;
}
/deep/ .el-dialog__body {
    padding: 12px 0;
    height: calc(100% - 100px);
}
/deep/ .el-dialog__footer {
    position: absolute;
    bottom: 0;
    background: #fff;
    width: 100%;
}

///deep/ .new-lesson>.content {
//    padding: 0 50px;
//}
</style>
