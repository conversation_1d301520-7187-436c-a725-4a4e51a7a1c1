<template>
  <div @mouseenter="enterCard()" @mouseleave="showCardRemove = false">
    <el-card shadow="always" class="activity-card">
      <!-- 删除按钮 -->
      <i v-if="edit && showCardRemove" class="el-icon-error close-btn lg-pointer"></i>
      <!-- 左侧边框 -->
      <div class="card-left"></div>
      <!-- 课程 -->
      <div class="lesson-area">
        <div class="lesson-media flex-align-center">
          <img src="https://s3.cn-north-1.amazonaws.com.cn/com.learning-genie.dev/8bc9c8c8-733b-4614-ac3f-a3e11a8a2369.jpg"/>
        </div>
        <div class="lesson-title">
          Clouds Exploration By Janice <PERSON>
        </div>
      </div>
      <!-- 测评点 -->
      <div class="m-t-sm">
        <el-tag type="info" size="small" class="m-r-xs pos-rlt">
          ATL-REG1
          <i v-if="edit && showMeasureRemove" class="el-icon-error close-btn lg-pointer"></i>
        </el-tag>
        <el-tag type="info" size="small" class="m-r-xs pos-rlt">
          SED2
          <i v-if="edit && showMeasureRemove" class="el-icon-error close-btn lg-pointer"></i>
        </el-tag>
        <el-tag type="info" size="small" class="m-r-xs pos-rlt">
          LLD10
          <i v-if="edit && showMeasureRemove" class="el-icon-error close-btn lg-pointer"></i>
        </el-tag>
      </div>
      <!-- 小孩 -->
      <div class="m-t-sm">
        <span>Eli, Jas, Joe, Mic, Sel, Mar</span>
      </div>
    </el-card>
  </div>
</template>

<script>

export default {
  name: 'ActivityCard',
  components: { },

  props: {
    edit: {
      type: Boolean
    }
  },

  data () {
    return {
      showCardRemove: false,
      showMeasureRemove: false
    }
  },

  methods: {
    enterCard () {
      this.showCardRemove = true
    }
  }
}
</script>

<style lang="less" scoped>
// 周一至周五颜色
@week-1-color: #DD3E78;
@week-2-color: #F4C18F;
@week-3-color: #97B02E;
@week-4-color: #7FC2A7;
@week-5-color: #795198;
.activity-card {
  position: relative;
  padding: 10px;
  overflow: unset;
}
.card-left {
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
}
.week-1 .card-left {
  background-color: @week-1-color;
}
.week-2 .card-left {
  background-color: @week-2-color;
}
.week-3 .card-left {
  background-color: @week-3-color;
}
.week-4 .card-left {
  background-color: @week-4-color;
}
.week-5 .card-left {
  background-color: @week-5-color;
}

/deep/ .el-card__body {
  padding: 0;
}
/deep/ .el-tag--info {
  color: #323338;
}

.lesson-area {
  display: flex;
}
.lesson-media {
  width: 30%;
  flex: none;
  img {
    max-width: 100%;
  }
}
.lesson-title {
  padding-left: 6px;
  width: 65%;
  flex: auto;
}

.close-btn {
  position: absolute;
  top: -4px;
  right: -4px;
}
</style>