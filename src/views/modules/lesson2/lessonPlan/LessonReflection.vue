<template>
  <div class="h-full display-flex flex-direction-col">
    <div v-if="isComeFromIPad" class="header text-center bg-light dk">
      <img class="goback lg-pointer" src="@/assets/img/us/back_home.png" @click="goBack">
      <div class="font-size-18">
        <span v-if="!edit">{{$t('loc.plan70')}}</span>
        <span v-if="edit">{{$t('loc.plan90')}}</span>
      </div>
    </div>
    <div class="content h-full">
      <el-row class="h-full">
        <el-col :span="14" class="h-full">
          <reflection-lesson-detail class="lg-box-shadow lg-border-radius-4" :lessonId="lessonId"></reflection-lesson-detail>
        </el-col>
        <el-col :span="10" class="h-full display-flex flex-direction-col">
          <div class="observation-detail lg-margin-right-24">
            <reflection-observation-detail class="lg-box-shadow lg-border-radius-4" :lessonId="lessonId" :objectId="objectId" :planId="planId"></reflection-observation-detail>
          </div>
          <div class="lg-margin-top-24 lg-margin-right-24">
            <reflection-editor class="lg-box-shadow lg-border-radius-4" :objectId="objectId" @callLoadReflection="loadReflection" ref="reflectionEditor"></reflection-editor>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import ReflectionLessonDetail from './components/ReflectionLessonDetail'
import ReflectionObservationDetail from './components/ReflectionObservationDetail'
import ReflectionEditor from './components/ReflectionEditor'
import tools from '@/utils/tools'

export default {
  name: 'LessonReflection',
  components: {
    ReflectionLessonDetail,
    ReflectionObservationDetail,
    ReflectionEditor
  },

  data () {
    return {
      lessonId: undefined,
      objectId: undefined,
      planId: undefined,
      edit: false
    }
  },

  computed: {
    // 是否来自iPad
    isComeFromIPad () {
      return tools.isComeFromIPad()
    }
  },

  created () {
    this.lessonId = this.$route.params.lessonId
    this.objectId = this.$route.params.objectId
    this.planId = this.$route.params.planId
    this.$nextTick(() => {
      tools.setHelpBtnVisiable(false)
    })
  },

  destroyed () {
    this.$nextTick(() => {
      tools.setHelpBtnVisiable(true)
    })
  },

  methods: {
    goBack () {
      this.$router.push({
        name: 'view-plan',
        params: {
          planId: this.planId
        }
      })
    },

    loadReflection () {
      if (this.$refs.reflectionEditor.lessonReflection || this.$refs.reflectionEditor.childReflection) {
        this.edit = true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.header {
  position: relative;
  height: 50px;
  padding: 10px 30px 10px 40px;
  .goback {
    position: absolute;
    top: 15px;
    left: 40px;
    max-width: 24px;
  }
}
.content {
  padding: 0;
  min-height: 0;
}
.observation-detail {
  flex-grow: 1;
  min-height: 200px;
}
</style>