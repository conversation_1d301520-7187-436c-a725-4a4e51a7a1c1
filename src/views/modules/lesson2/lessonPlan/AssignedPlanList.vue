<template>
    <div class="h-full">
        <shared-plan-table ref="sharededPlanTable" :loader="listAssignedPlans"></shared-plan-table>
    </div>
</template>

<script>
import LessonApi from '@/api/lessons2'
import SharedPlanTable from './components/SharedPlanTable'

export default {
    name: 'AssignedPlanList',
    components: {SharedPlanTable},

    methods: {
        // 获取管理员管理范围内的周计划分享记录
        listAssignedPlans (params) {
            return LessonApi.listSharedPlans(params)
        }
    }
}
</script>