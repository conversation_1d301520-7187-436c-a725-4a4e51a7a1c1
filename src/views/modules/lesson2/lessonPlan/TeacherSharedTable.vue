<template>
    <div class="h-full display-flex flex-direction-col">
        <div v-show="isComeFromIPad" class="header font-size-16 bg-light add-margin-b-10 flex-none">
            <!-- 老师分享列表头 -->
            <div>
                <img class="goback lg-pointer" src="@/assets/img/us/back_home.png" @click="goBack">
            </div>
            <!-- 老师分享列表 -->
            <div v-if="teacherInfo.id" class="flex-center-center">
                <img :src="teacherInfo.avatarUrl" style="height: 24px;border-radius: 50%;">
                <span style="padding-left: 3px;">{{teacherInfo.displayName}}</span><span>'s Weekly Planner</span>
            </div>
        </div>
        <div class="h-full flex-auto">
            <shared-plan-table ref="sharededPlanTable" :loader="listSharedPlans"></shared-plan-table>
        </div>
    </div>
</template>

<script>
import tools from '@/utils/tools'
import LessonApi from '@/api/lessons2'
import SharedPlanTable from './components/SharedPlanTable'

export default {
    name: 'SharedPlanList',
    components: {SharedPlanTable},

    created () {
        // 获取路由参数中的老师 id
        this.shareUserId = this.$route.params.shareUserId
    },

    data () {
        return {
            shareUserId: '',  //分享老师 Id
            teacherInfo: {},  // 老师信息
        }
    },
    computed: {
      // 判断是否来源于iPad
      isComeFromIPad () {
        return tools.isComeFromIPad()
      }
    },
    methods: {
        // 获取老师被分享的周计划记录
        listSharedPlans (params) {
            params['shareUserId'] = this.shareUserId
            let sharedPlansPromise = LessonApi.listSharedPlans(params)
            sharedPlansPromise.then(response => {
                this.teacherInfo = response.shareUser
                this.$bus.$emit('setPageName', {
                    name: this.$t('loc.plan152', { 'user': this.formatUserName(this.teacherInfo.displayName) }),
                    routeName: this.$route.name
                })
            })
            return sharedPlansPromise
        },
      formatUserName (name) {
        if (name.indexOf('Ms.') === 0) {
          let separator = 'Ms.'
          if (name.replace(separator, '').charAt(0) !== ' ') {
            name = separator + ' ' + name.replace(separator, '')
          }
        } else if (name.indexOf('Mr.') === 0) {
          let separator = 'Mr.'
          if (name.replace(separator, '').charAt(0) !== ' ') {
            name = separator + ' ' + name.replace(separator, '')
          }
        }
        return name
      },
        // 返回管理员分配周计划列表
        goBack () {
            this.$router.push({
                name: 'assigned-plan'
            })
        }
    }
}
</script>

<style lang="less" scoped>
.header {
  position: relative;
  padding: 10px 30px 10px 40px;
}
.goback {
  max-width: 24px;
  float: left;
}
</style>