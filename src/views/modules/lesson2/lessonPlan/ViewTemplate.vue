<template>
  <div class="edit-paln h-full display-flex flex-direction-col lg-margin-left-24 lg-margin-right-24">
    <!-- 头部 -->
    <template-edit-header :defaultGroupId="defaultGroupId" :title="$t('loc.curriculum97')" :planInfo="planInfo" :frameworks="frameworks" :showCore="showCore" @callDownloading="callDownloading"></template-edit-header>
    <!-- 日历表格 -->
    <plan-calendar  ref="calendar" :adminEdit="true" :editTemplate="true" :edit="false" :planId="routePlanId" :firstVisit="firstVisit" :showLastReflection="showLastReflection" @setPlanInfo="setPlanInfo" style="margin-left: -20px;"></plan-calendar>
  </div>
</template>

<script>
import PlanCalendar from '@/views/modules/lesson2/lessonPlan/components/PlanCalendar'
import TemplateEditHeader from '@/views/modules/lesson2/lessonPlan/components/TemplateEditHeader'
export default {
  name: 'ViewTemplate',
  components: { PlanCalendar,TemplateEditHeader },
  data () {
    return {
      showQuickAddLesson: false,
      editTemplate: true,
      showLastReflection: false, // 是否展示上周反思
      planInfo: {}, // 周计划信息
      frameworks: [], // 框架
      showCore: false, // 是否展示核心测评点
      firstVisit: false // 是否首次创建周计划
    }
  },
  methods: {
    setPlanInfo (planInfo) {
      this.planInfo = planInfo
    },

   /**
   * PDF 下载页面 loading
   */
    callDownloading (val) {
      this.$refs.calendar.calendarLoading = val
    }
  },
  created () {
  },
  computed: {
    routePlanId () {
      return this.$route.params.planId
    },
    defaultGroupId () {
      return this.$route.params.defaultGroupId
    }
  }
}
</script>

<style scoped>

</style>
