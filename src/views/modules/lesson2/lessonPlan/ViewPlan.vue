<template>
 <div class="h-full display-flex flex-direction-col lg-margin-right-24 lg-margin-left-24">
    <!-- 头部 -->
    <calendar-header ref="calendarHeader" :edit="edit" :review="review" :planInfo="planInfo" @callDownloading="callDownloading" :frameworks="frameworks"></calendar-header>
    <!-- 日历表格 -->
    <plan-calendar :key="renderKey" :viewPlan="true" :isLocked="planInfo.lockedData" :planStatus="planInfo.status" ref="calendar" :edit="edit" :review="review" :planId="planId" @setPlanInfo="setPlanInfo" @reRender="reRender"  @callUpdateBaseInfo="updateBaseInfo" ></plan-calendar>
 </div>
</template>

<script>
import { mapState } from 'vuex'
import { acrossRole } from '@/utils/common'
import CalendarHeader from './components/CalendarHeader'
import PlanCalendar from './components/PlanCalendar'
import LessonApi from '@/api/lessons2'

export default {
  name: 'ViewPlan',
  components: {
    CalendarHeader,
    PlanCalendar
  },
  provide () {
    return {
      dllContext: this.dllContext
    }
  },
  data () {
    return {
      // dll 班级课程语言信息
      dllContext: {
        groupId: null,
        lessonId: null,
        langCodes: null,
        onlyDllChildOpen: null
      },
      edit: false,
      review: false,
      planId: undefined,
      planInfo: {},
      showLastReflection: false,
      showCore: undefined,
      frameworks: [], // 框架
      renderKey: 0 // 组件key，用于重新渲染
    }
  },

  created () {
    // 获取周计划 ID
    this.planId = this.$route.params.planId
    this.setReview()
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    routePlanId () {
      return this.$route.params.planId
    },
    isAdmin () {
      return acrossRole('agency_admin', 'agency_owner', 'site_admin')
    }
  },

  methods: {
    setPlanInfo (planInfo) {
      this.planInfo = planInfo
      this.showLastReflection = planInfo.showLastReflection
      this.setReview()
      // 对周计划选中的学校和班级 ID 进行赋值
      this.$nextTick(() => {
        this.$refs.calendarHeader.setSelectedCenterIdAndGroupId()
      })
    },
    setReview () {
      this.review = (this.$route.query.review && JSON.parse(this.$route.query.review) && !this.planInfo.lockedData) || (this.planInfo.status === 'B_PENDING' && this.isAdmin && !this.planInfo.lockedData) || false
    },
    // 重新渲染
    reRender () {
      this.renderKey++
    },
    // 更新基本信息
    updateBaseInfo () {
      let params = {
        id: this.planId,
        theme: this.$refs.calendar.theme,
        groupId: this.$refs.calendarHeader.currentGroupId,
        teacherIds: this.$refs.calendarHeader.currentTeacherIds,
        week: this.$refs.calendarHeader.currentWeek,
        fromAtLocal: this.$refs.calendarHeader.fromDate,
        toAtLocal: this.$refs.calendarHeader.toDate,
        showLastReflection: this.$refs.calendar.showLastReflection,
        customThemeRowName: this.$refs.calendar.customThemeRowName && this.$refs.calendar.customThemeRowName.trim()
      }
      LessonApi.updatePlanBaseInfo(params).then(response => {
      }).catch(error => {})
      // 更新自动退出时间
      this.$refs.calendar.autoExist()
    },

   /**
   * PDF 下载页面 loading
   */
    callDownloading (val) {
      this.$refs.calendar.calendarLoading = val
    }
  },

  watch: {
    routePlanId (val) {
      if (val) {
        this.planId = val
      }
    },
    'planInfo.groupId': {
      handler (value) {
        this.dllContext.groupId = value
      }
    },
    // 监听是否显示核心测评点，给子组件传递
    showCore (val) {
      this.$refs.calendarHeader.showCore = val
    }
  }
}
</script>

<style lang="less" scoped>

</style>
