<template>
<template-plan-table :loader="listPlans"></template-plan-table>
</template>

<script>
import TemplatePlanTable from '@/views/modules/lesson2/lessonPlan/components/TemplatePlanTable'
import LessonApi from "@/api/lessons2";
export default {
  name: 'TemplatePlanTableList',
  components: { TemplatePlanTable },
  methods: {
    listPlans (params) {
      params['type'] = 'ADMIN_TEMPLATE'
      return LessonApi.listAdminTemplates(params)
    }
  }
}
</script>

<style scoped>

</style>