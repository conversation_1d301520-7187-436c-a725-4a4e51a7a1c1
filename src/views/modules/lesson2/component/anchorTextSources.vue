<template>
  <div v-loading="generateLessonSourceLoading"
       :class="{
          'imp-step-source-content-preview imp-step-preview-break': sourcePreview,
          'imp-step-source-content-preview': !sourcePreview
       }" style="width: 100%">
    <div style="display: flex;justify-content: space-between;align-items: center;padding-right: 10px; position: relative;padding-bottom:4px">
      <span class="font-size-16 font-weight-600">{{ $t('loc.unitPlannerSources') }}:</span>
      <div class="resources-button-container" v-if="!sourcePreview">
        <el-button type="primary" :icon="hasResources ? 'el-icon-edit' : 'el-icon-plus'" size="medium" @click="editResourcesNow" :disabled="resourceEditDisabled">
          {{ hasResources ? $t('loc.editResourceButton') : $t('loc.addResourceButton') }}
        </el-button>
        <span class="new-tag" v-if="showGuidePopup">New</span>
        <!-- 原生弹窗实现 -->
        <div v-if="showGuidePopup" class="resources-guide-native-popup">
          <div class="lg-color-white lg-padding-20">
            <!-- 引导文字 -->
            <div class="add-margin-b-24 word-break text-left-xs">
              <!-- 用户引导内容 -->
              <span class="title-font-20">{{ $t('loc.resourceGuidedTitle') }}</span>
              <div class="guide-text add-margin-t-12">
                <span class="guide-icon">✨</span>
                {{ $t('loc.resourceEditGuided') }}
              </div>
            </div>
            <div class="flex-justify-end gap-6 flex-align-center">
              <el-button class="guide-btn-got-it" @click="hideGuidePopup">
                <span class="font-size-14 font-weight-600">Got it</span>
              </el-button>
              <el-button class="guide-btn-primary" @click="editResourcesNow">
                <span class="font-size-14 font-weight-600">{{ $t('loc.editResourceNow') }}</span>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 资源内容区域 -->
    <div class="imp-step-sources" v-if="(hasAvailableResources && showImpStepSource) || (sourcePreview && hasAvailableResources)" >
      <div :id="'implStepSubscript' + source.subscript"
           v-for="source in impStepContentAndSources.sources" :key="source.subscript"
           :style="{ 'padding': source.hidden ? '0px 10px' : equalsIgnoreCase(source.type, 'File') ? '0 10px' : '5px 10px 10px' }"
           class="imp-step-source-item">
        <span class="imp-step-source-label" v-if="!source.hidden || sourcePreview">
          <!-- 根据是否为文件类型选择不同的展示方式 -->
          <template v-if="source.type === 'File'">
            <!-- 文件类型资源 -->
                          <div class="file-resource-container">
               <div class="file-download-card" :style="{ backgroundColor: sourcePreview ? 'transparent':'#f2f2f2'}">
                  <!-- 角标和标题 -->
                <div class="file-title-area">
                  <span class="subscript-word">[{{ source.subscript }}]. </span>
                  <span>{{ source.sourceKeywords || "Custom Resource" }}<span v-if="!!source.source"> ({{ source.source }})</span></span>
                </div>

                <div class="file-info-area">
                  <!-- 文件类型图标 -->
                  <div class="file-type-icon">
                    <img :src="getFileIconClass(source.fileName || source.sourceLink.split('/').pop())" alt="File Icon">
                  </div>

                  <!-- 文件名称 -->
                  <div class="file-name" :style="{ color: sourcePreview ? '#676879' : '#333' }">
                    {{ source.fileName || source.sourceLink }}
                  </div>
                  <!-- 下载按钮 -->
                  <!-- 下载按钮 -->
                  <el-tooltip content="Download" placement="top" effect="dark">
                    <div class="download-button" @click="downloadFile(source)">
                      <div class="download-icon-container" >
                        <i class="el-icon-download"></i>
                      </div>
                    </div>
                 </el-tooltip>
                </div>
              </div>
            </div>
          </template>

          <template v-else>
            <!-- 普通资源 -->
            <span class="subscript-word">[{{ source.subscript }}]. </span>
            <span class="display-flex" style="flex-direction: column;gap: 5px">
              <span>{{ source.sourceKeywords || 'Custom Resource' }}<span v-if="!!source.source"> ({{ source.source }})</span></span>
              <a :href="source.sourceLink" class="text-primary" target="_blank"
                 style="text-decoration: underline; word-break: break-all">{{ source.sourceLink }}</a>
              <curriculum-media-viewer v-if="source.videoLink" :url="source.videoLink"
                                       :coverMediaImg="source.cover" :preview="true"
                                       :class="{ 'media-viewer-other': !sourcePreview, 'media-viewer-preview': sourcePreview}"
              />
            </span>
          </template>
        </span>
      </div>
    </div>
    <div v-else-if="!hasAvailableResources && showImpStepSource" style="line-height: 20px">
      <span class="color-676879">{{ $t('loc.unitPlannerNotHasSources') }}</span>
    </div>

    <!-- 资源编辑弹窗 -->
    <resource-edit-modal
      v-model="resourceEditVisible"
      :resources="impStepContentAndSources.sources || []"
      :more-resources="impStepContentAndSources.moreSources || []"
      :lesson-id="this.lessonId || (this.lesson && this.lesson.id)"
      :type="IMP_STEP"
      :append-to-body="true"
      @save-resources="saveResources"
      @close="onResourceEditClose"
    />

    <!-- 添加资源弹窗 -->
    <resource-add-modal
      v-model="resourceAddVisible"
      :current-resource-count="currentFileNum"
      :append-to-body="true"
      @add-resources="handleAddResources"
      @close="onResourceAddClose"
    />
  </div>
</template>

<script>
import emitter from 'element-ui/src/mixins/emitter'
import { mapState } from 'vuex'
import CurriculumMediaViewer from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMediaViewer.vue'
import { serverlessApiUrl } from '@/utils/setBaseUrl'
import ResourceEditModal from './ResourceEditModal.vue'
import ResourceAddModal from './ResourceAddModal.vue'
import FileUtils from '@/utils/file'
import { equalsIgnoreCase } from '@/utils/common'

export default {
  name: 'anchorTextSources',
  components: { CurriculumMediaViewer, ResourceEditModal, ResourceAddModal },
  mixins: [emitter],
  props: {
    // 当年龄组发生改变的时候
    changeAgeGroups: {
      type: Boolean,
      default: false
    },
    // 年龄组名称
    ageGroupName: {
      type: String,
      default: ''
    },
    // 课程 id
    lessonId: {
      type: String,
      default: ''
    },
    // 实施步骤数据
    step: {
      type: Object,
      default: null
    },
    isCurriculum: {
      type: Boolean,
      default: false
    },
    lesson: {
      type: Object,
      default: () => {
      }
    },
    // 实施步骤内容显示主动控制
    showImpStepSource: {
      type: Boolean,
      default: false
    },
    // 实施步骤内容
    impStepContent: {
      type: String,
      default: ''
    },
    // 实施步骤资源
    impStepSource: {
      type: Object,
      // null 为旧数据
      default: null
    },
    // 实施步骤资源是否为预览模式
    sourcePreview: {
      type: Boolean,
      default: false
    },
    parentComponentName: {
      type: String,
      default: ''
    }
  },
  created () {
    this.routeName = this.$route.name
    // 初始化数据来源
    this.determineDataSource()
    if (this.impStepContentAndSources && this.impStepContentAndSources.impStepContent) {
      this.generalValue = this.impStepContentAndSources.impStepContent.replace(/\n/g, '<br>')
    }

    this.$nextTick(() => {
      // 判断资源信息列表是否存在，如果存在，则默认首先展示资源内容
      if (this.impStepContentAndSources && this.impStepContentAndSources.sources && this.impStepContentAndSources.sources.length > 0) {
        this.showSource = true
      } else {
        this.showSource = false
      }
    })
  },

  // 添加 mounted 钩子确保弹窗显示
  mounted() {
    // 确保引导弹窗初始化 - 当不是预览模式时才显示引导弹窗
    if (!this.sourcePreview) {
      this.startResourcesGuide()
    }
    // 计算当前文件数量
    this.calculateCurrentFileNum(this.impStepContentAndSources.sources)

    // 添加事件监听，用于响应显示资源引导
    this.$bus.$on('show-resources-guide', this.handleShowResourcesGuide)

    // 添加事件监听，如果点击增强按钮或者 Adapt UDL and CLR 按钮后关闭引导
    this.$bus.$on('close-resources-guide', this.hideGuidePopup)
  },

  // 在组件销毁前移除事件监听
  beforeDestroy() {
    this.$bus.$off('show-resources-guide', this.handleShowResourcesGuide)
    this.$bus.$off('close-resources-guide', this.hideGuidePopup)
  },
  data () {
    return {
      generalValue: '', // 普通的值
      classSpecificValue: '', // 班级定制的 CLR 的值
      generatedCLR: false, // 是否生成过了 班级定制的 CLR
      showSource: false, // 是否显示资源
      useLesson: null, // true 表示数据来自 lesson，false 表示数据来自 step
      generateLessonSourceLoading: false, // 生成资源内容 loading
      showGuidePopup: false, // 控制弹窗显示
      resourceEditVisible: false, // 控制资源编辑弹窗显示
      resourceAddVisible: false, // 控制资源添加弹窗显示
      IMP_STEP: 'IMP_STEP', // 添加 IMP_STEP 常量
      previousTags: [], // 存储上次解析的 imp-script 标签内容
      currentFileNum: 0, // 当前文件数量
      resourceEditDisabled: false, // 资源编辑按钮是否禁用
      routeName: '', // 当前路由名称
    }
  },
  methods: {
    equalsIgnoreCase,
    // 处理显示资源引导事件
    handleShowResourcesGuide() {
      // 如果不是预览模式，则显示资源引导
      if (!this.sourcePreview && this.guideFeatures.resourceEditGuide) {
        this.setShowResourcesGuide();
      }
    },

    // 显示资源编辑引导弹窗
    startResourcesGuide() {
      // 如果是预览模式，不显示引导弹窗
      if (this.sourcePreview) {
        return;
      }
      if (!this.guideFeatures) {
        this.$axios.get($api.urls().getUserNeedGuideFeatures).then(result => {
          if (result.resourceEditGuide
          && ((equalsIgnoreCase(this.routeName, 'lesson-detail-cg') && !result.showRedesignLessonGuide)
          || (equalsIgnoreCase(this.routeName, 'AddLesson') && !result.showCurriculumLessonPlanEnhanceGuide)
          || equalsIgnoreCase(this.routeName, 'unit-planner-cg')
          || equalsIgnoreCase(this.routeName, 'EditLesson'))) {
            this.setShowResourcesGuide()
          }
        })
      }
      // 如果按钮当前可用，且需要显示引导
      if (this.guideFeatures.resourceEditGuide
      && ((equalsIgnoreCase(this.routeName, 'lesson-detail-cg') && !this.guideFeatures.showRedesignLessonGuide)
          || (equalsIgnoreCase(this.routeName, 'AddLesson') && !this.guideFeatures.showCurriculumLessonPlanEnhanceGuide)
          || equalsIgnoreCase(this.routeName, 'unit-planner-cg')
          || equalsIgnoreCase(this.routeName, 'EditLesson'))) {
        this.setShowResourcesGuide()
      }
    },

    // 设置显示资源编辑引导弹窗
    setShowResourcesGuide() {

      if (this.generateLessonSourceLoading) {
        // 如果当前正在加载，设置延迟检查
        this.$nextTick(() => {
          const checkButtonState = () => {
            if (!this.generateLessonSourceLoading) {
              // 按钮可用，显示引导
              this.showGuidePopup = true
            } else {
              // 按钮仍不可用，继续等待
              setTimeout(checkButtonState, 500)
            }
          }
          // 开始检查
          setTimeout(checkButtonState, 500)
        })
      } else {
        // 按钮当前可用，直接显示引导
        this.showGuidePopup = true
      }
      this.guideFeatures.resourceEditGuide = false
      let requestParam = { 'features': ['RESOURCE_EDIT_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, requestParam).then(() => {
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
      })
    },

    // 判断当前数据来自 lesson 还是 step
    determineDataSource () {
      this.useLesson = !!this.lesson
    },

    // 更新数据到对应对象中
    upDataToSourceObj (lessonImpStepAndSource) {

      // lesson
      if (this.useLesson) {

        this.$emit('upLessonImpStepData', lessonImpStepAndSource)
      } else {
        // step
        this.$emit('upStepImpStepSource', lessonImpStepAndSource)
      }
    },

    // 根据实施步骤生成资源内容 -- 父组件主动调用资源生成
    generateResourcesByImpStep (commonImpStepContent, lessonId) {
      this.generateLessonSourceLoading = true
      let content = commonImpStepContent || this.commonImpStepContent
      const params = {
        impStepContent: content,
        lessonId: lessonId || this.lessonId
      }

      // 重置 previousTags，避免不必要的比对
      this.previousTags = [];

      // 根据实施步骤生成资源内容
      this.$axios.post(serverlessApiUrl + $api.urls().generateResourcesByImpStep, params).then(res => {
        this.generateLessonSourceLoading = false
        if (!res.lessonImpStepAndSource) {
          // 清除 params.impStepContent 文本中的角标
          params.impStepContent = params.impStepContent.replace(/\[\d+\]/g, '')
          // 如果返回数据不存在，资源要置空
          res.lessonImpStepAndSource = {
            impStepContent: params.impStepContent,
            sources: [],
            moreSources: [] // 确保初始化 moreSources
          }
        }
        // 生成结束统一显示资源内容
        this.showSource = true
        // 如果资源信息存在，则默认首先展示资源信息内容
        if (res.lessonImpStepAndSource.sources && res.lessonImpStepAndSource.sources.length > 0) {
          res.lessonImpStepAndSource.sources.forEach(source => {
            // 初始化 hidden 属性为 false
            this.$set(source, 'hidden', false)
          })
        }

        // 确保 moreSources 字段存在
        if (!res.lessonImpStepAndSource.moreSources) {
          res.lessonImpStepAndSource.moreSources = []
        }

        this.upDataToSourceObj(res.lessonImpStepAndSource)
      }).catch(() => {
        this.generateLessonSourceLoading = false
        this.$emit('impStepSourceGeneratedEnd')
      })
    },

    // 隐藏弹窗
    hideGuidePopup () {
      this.showGuidePopup = false
      this.guideFeatures.resourceEditGuide = false
      let requestParam = { 'features': ['RESOURCE_EDIT_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, requestParam).then(() => {
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
      })
    },

    // 编辑资源
    editResourcesNow() {

      // 检查是否需要显示引导弹窗
      if (this.showGuidePopup) {
        this.hideGuidePopup();
      }

      // 重置previousTags，开始新的编辑会话
      this.previousTags = [];

      // 根据是否有资源决定打开哪个弹窗
      if (this.hasResources) {
        // 如果有资源，打开编辑弹窗
        this.resourceEditVisible = true;
      } else {
        // 如果没有资源，直接打开添加资源弹窗
        this.resourceAddVisible = true;
      }
    },

    // 更新资源
    updateResources(updatedResources, updatedMoreResources) {
      if (this.impStepContentAndSources) {
        const updatedData = {
          ...this.impStepContentAndSources,
          sources: updatedResources
        }
        // 批量生成课程，如果没有资源 impStepContentAndSources 内容会为空，此时将 lesson.implementationSteps 赋值给 updatedData
        if (!this.impStepContentAndSources.impStepContent) {
          updatedData.impStepContent = this.lesson.implementationSteps
        }
        // 如果提供了更新的额外资源，也一并更新
        if (updatedMoreResources) {
          updatedData.moreSources = updatedMoreResources;
        }
        this.upDataToSourceObj(updatedData)
      }
    },

    // 资源编辑弹窗关闭
    onResourceEditClose() {
      this.resourceEditVisible = false
      this.calculateCurrentFileNum(this.impStepContentAndSources.sources)
    },

    // 资源添加弹窗关闭
    onResourceAddClose() {
      this.resourceAddVisible = false
      this.calculateCurrentFileNum(this.impStepContentAndSources.sources)
    },

    // 处理资源保存事件
    saveResources(resources, moreResources, isRestore) {
      // 先更新 UI 数据
      this.updateResources(resources, moreResources);
      // 触发保存前事件，通知父组件显示加载状态
      this.$emit('before-save-resources');
      // 然后调用后端保存,IMP 不需要回滚 content
      this.saveResourcesToBackend(resources, moreResources, this.impStepContentAndSources.impStepContent || this.lesson.implementationSteps, isRestore);
    },

    // 保存资源到后端
    saveResourcesToBackend(resources, moreResources, content, isRestore) {

      // 重置 previousTags，确保在保存新资源后不会错误地隐藏资源
      this.previousTags = [];

      // 使用课程 ID
      const effectiveLessonId = this.lessonId || (this.lesson && this.lesson.id);
      if (!effectiveLessonId) {
        // 触发保存失败事件，通知父组件关闭加载状态
        this.$emit('save-resources-failed');
        return;
      }

      // 构建请求数据
      const requestData = {
        lessonId: effectiveLessonId,
        type: this.IMP_STEP,
        lessonResource: {
          content: content,
          sources: resources.filter(resource => !resource.hidden), // 过滤掉hidden为true的资源
          moreSources: moreResources
        },
        rollback: isRestore
      };
      this.resourceEditDisabled = true
      // 调用API保存资源
      this.$axios.post($api.urls().updateResource, requestData)
        .then(response => {

          // 如果响应中包含更新后的资源数据，则用响应数据更新本地数据
          if (response && response.lessonResource) {
            const responseData = response.lessonResource;
            // 更新数据
            const updatedData = { ...this.impStepContentAndSources }
            // 更新资源列表
            if (responseData.sources) {
              updatedData.sources = responseData.sources;
            }
            // 更新额外资源
            if (responseData.moreSources) {
              updatedData.moreSources = responseData.moreSources;
            }

            // 更新实施步骤内容
            if (responseData.content) {
              updatedData.impStepContent = responseData.content;
            }

            // 更新父组件
            this.upDataToSourceObj(updatedData);

            // 触发保存成功事件，传递新的实施步骤内容，通知父组件更新并关闭加载状态
            this.$emit('save-resources-success', responseData.content);
          } else {
            // 没有返回数据但保存成功
            this.$emit('save-resources-success');
          }
          this.resourceEditDisabled = false
          // 显示成功提示
        })
        .catch(error => {

          // 触发保存失败事件，通知父组件关闭加载状态
          this.$emit('save-resources-failed');
          this.resourceEditDisabled = false // 资源编辑按钮是否禁用
        });
    },

    // 下载文件
    downloadFile(source) {
      FileUtils.courseDownload(source.sourceLink, source.fileName)
    },

    // 获取文件图标
    getFileIconClass(fileName) {
      return FileUtils.getFileType(fileName)
    },

    // 处理添加资源的结果
    handleAddResources(resources) {
      if (!resources || resources.length === 0) {
        return;
      }


      // 创建或更新资源数组
      const currentSources = this.impStepContentAndSources.sources || [];
      const updatedResources = [...currentSources];

      // 计算新资源的起始角标：找到当前资源列表中最大的角标，然后加1
      let nextSubscript = 1; // 默认从1开始
      if (updatedResources.length > 0) {
        // 找出当前资源中最大的角标值
        const maxSubscript = Math.max(
          ...updatedResources.map(source => parseInt(source.subscript) || 0)
        );
        nextSubscript = maxSubscript + 1;
      }

      // 添加新资源，并设置正确的序号
      resources.forEach(resource => {
        const newResource = {
          ...resource,
          subscript: nextSubscript.toString(),
          hidden: false
        };
        updatedResources.push(newResource);
        nextSubscript++; // 下一个资源的角标递增
      });


      // 更新资源数据
      this.updateResources(updatedResources, this.impStepContentAndSources.moreSources || []);

      // 保存到后端
      this.$emit('before-save-resources');
      this.saveResourcesToBackend(updatedResources, this.impStepContentAndSources.moreSources || [], this.impStepContentAndSources.impStepContent || this.lesson.implementationSteps);

      // 计算当前文件数量
      this.calculateCurrentFileNum(updatedResources);

    },

    // 计算当前文件数量
    calculateCurrentFileNum(resources) {
      this.currentFileNum = (resources || []).filter(item => equalsIgnoreCase(item.type, 'File')).length;
    },
  },
  computed: {
    ...mapState({
      open: state => state.common.open,
      guideFeatures: state => state.common.guideFeatures // 引导功能状态
    }),

    /**
     * 资源详情
     */
    impStepContentAndSources: {
      get () {
        if (this.useLesson) {
          if (!this.lesson || !this.lesson.lessonImpStepAndSource) {
            return { impStepContent: '', sources: [] }
          } else {
            return this.lesson && this.lesson.lessonImpStepAndSource
          }
        } else {
          if (!this.step || !this.step.lessonImpStepAndSource) {
            return { impStepContent: '', sources: [] }
          } else {
            return this.step && this.step.lessonImpStepAndSource
          }
        }
      }
    },

    // 是否存在可用资源
    hasAvailableResources () {
      // 如果资源不存在则返回 false
      if (!this.impStepContentAndSources) {
        return false
      }
      // 获取资源列表
      let sources = this.impStepContentAndSources.sources
      // 如果资源列表不存在则返回 false
      if (!sources) {
        return false
      }
      return sources.filter(source => !source.hidden).length > 0
    },

    // 是否有实施步骤资源
    hasResources () {
      // 如果资源不存在则返回 false
      if (!this.impStepContentAndSources) {
        return false
      }
      // 获取资源列表
      const sources = this.impStepContentAndSources.sources
      // 如果资源列表不存在或为空则返回 false
      if (!sources || sources.length === 0) {
        return false
      }
      if (sources.filter(source => !source.hidden).length === 0) {
        return false
      }
      return true
    },

    /**
     * 当前用户
     */
    ...mapState({
      currentUser: state => state.user.currentUser
    }),

    /**
     * 获取用户 id
     */
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    }

  },
  watch: {
    impStepContentAndSources: {
      deep: true,
      immediate: true,
      handler (value) {
        if (value && !value.impStepContent && value.sources) {
          this.impStepContentAndSources.sources.forEach(source => {
            this.$set(source, 'hidden', true)
          })
        }
        if (value) {
          // 获取资源列表
          const sources = value && value.sources
          // 获取带有角标的 impStepContent 内容
          const impStepContent = value && value.impStepContent
          if (!sources || !impStepContent || sources.length === 0) {
            return
          }
          const subscripts = sources && sources.map(source => source.subscript)

          // 解析为 dom 处理
          const parser = new DOMParser()
          const doc = parser.parseFromString(impStepContent, 'text/html')
          // 存放根据 imp-script 标签解出来的内容
          const currentTags = []
          // 遍历所有的 <imp-script> 标签
          doc.querySelectorAll('imp-script').forEach(scriptTag => {
            currentTags.push(scriptTag.textContent)
          })

          // 遍历 subscripts 检查
          subscripts.forEach(subscript => {
            const subscriptPattern = `[${subscript}]` // 格式化为带角标的字符串

            // 检查当前 tags 数组中是否包含当前 subscript
            const existsInCurrentTags = currentTags.some(tag => tag.includes(subscriptPattern))
            // 检查上一次 tags 数组中是否包含当前 subscript
            const existsInPreviousTags = this.previousTags.some(tag => tag.includes(subscriptPattern))
            this.impStepContentAndSources.sources.forEach(source => {
              if (source.subscript === subscript) {
                if (existsInCurrentTags) {
                  // 如果当前存在，显示资源
                  this.$set(source, 'hidden', false)
                } else if (!existsInCurrentTags && existsInPreviousTags) {
                  // 如果当前不存在，但上次存在，则隐藏
                  this.$set(source, 'hidden', true)
                } else if (!existsInCurrentTags && !existsInPreviousTags) {
                  // 如果当前和上次都不存在
                  if (source.noMatchSubscript) {
                    // 如果未匹配到关键词，说明自定义不需要隐藏
                    this.$set(source, 'hidden', false)
                  } else {
                    // 如果匹配到关键词，但不存在，需要隐藏
                    this.$set(source, 'hidden', true)
                  }
                }
              }
            })
          })

          // 更新 previousTags 以便下次比较
          this.previousTags = [...currentTags]
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
@media screen and (max-width: 1025px) {
  /deep/ .media-viewer-other {
    width: 180px !important;
  }

  /deep/ .media-viewer-preview {
    width: 180px !important;
  }

  /deep/ .imp-step-source-label {
    width: 100% !important;
    display: flex;
    gap: 8px;
  }

  /deep/ .subscript-word {
    word-break: normal ; /* 取消影响 */
  }

  /deep/  a {
    word-wrap: break-word !important;
  }

}

/deep/ .subscript-word {
  word-break: normal ; /* 取消影响 */
}

@media screen and (min-width: 1366px) {
  /deep/ .media-viewer-other {
    //width: 250px !important;
  }

  /deep/ .media-viewer-preview {
    width: 250px !important;
  }

  /deep/ .imp-step-preview-break {
    word-break: normal !important; /* 取消影响 */
  }

  /deep/ .subscript-word {
    word-break: normal ; /* 取消影响 */
  }
}

.imp-step-source-content {
  min-height: 40px;
  padding: 0 15px 10px;
}

.imp-step-source-content-preview {
  min-height: 40px;
}

.imp-step-source-item {
  display: flex;
  flex-direction: column;
  line-height: 20px;
  margin-bottom: 8px;
  border-radius: 8px;

  .imp-step-source-label {
    width: 100%;
    display: flex;
    gap: 8px;
  }
}

/deep/ .media-viewer {
  border-radius: 8px !important;
}

.img-size {
  height: 152px;
  width: 128px;
}

.new-tag {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: -5px;
  top: -8px;
}

.new-tag-preview {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: 18px;
  top: -7px;
}

.gap-6 {
  gap: 6px;
}

.guide-text {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.guide-icon {
  font-size: 16px;
  margin-right: 5px;
}

.title-font-20 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  display: block;
}

.resources-button-container {
  position: relative;
}

.resources-guide-native-popup {
  position: absolute;
  top: 50px;
  right: 0;
  background-color: #8B7CFF;
  color: white;
  width: 410px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 200;
}

.resources-guide-native-popup::before {
  content: '';
  position: absolute;
  top: -7px;
  right: 70px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #8B7CFF;
}

/deep/ .guide-btn-got-it {
  background-color: transparent !important;
  color: white !important;
  border: 2px solid white !important;
  padding: 10px 18px !important;
  border-radius: 4px;
  transition: all 0.3s;
}

/deep/ .guide-btn-got-it:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/deep/ .guide-btn-primary {
  background-color: white !important;
  color: #8B7CFF !important; /* RGB 135,139,249 */
  border: none !important;
  padding: 10px 18px !important;
  border-radius: 4px;
  transition: all 0.3s;
}

/deep/ .guide-btn-primary:hover {
  background-color: #f5f5f5 !important;
}

@media screen and (max-width: 768px) {
  .resources-guide-native-popup {
    width: 300px;
    right: 0;
  }

  .resources-guide-native-popup::before {
    right: 70px;
  }

  /deep/ .guide-btn-got-it,
  /deep/ .guide-btn-primary {
    padding: 8px 12px !important;
  }

  .display-flex.flex-justify-end {
    flex-direction: column;
    gap: 10px;
  }
}

@media screen and (max-width: 480px) {
  .resources-guide-native-popup {
    width: 250px;
    right: 0;
  }

  .resources-guide-native-popup::before {
    right: 60px;
  }
}

.file-resource-container {
  margin: 8px -10px 8px 0;
  width: 100%;
}

.file-download-card {
  display: flex;
  border-radius: 6px;
  padding: 10px 15px 0 10px;;
  width: 100%;
  margin-left: -10px;
  flex-direction: column !important;
}

.file-title-area {
  border-radius: 6px 6px 0 0;
  padding: 10px 15px 0;
  margin: -10px -15px 0 -15px;
  display: flex;
  align-items: center;
  .subscript-word {
    margin-right: 5px;
  }
}

.file-info-area {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  padding: 0;
}

.file-type-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  i {
    font-size: 24px;
    color: #606266;
  }
}

.file-name {
  font-size: 14px;
  word-break: break-all;
  width: 176px;
  flex: 1;
}

.download-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  cursor: pointer;
  align-self: flex-end;
  margin-top: 15px;
  margin-right: 5px;
}

.download-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 4px;
  position: relative;
  padding-bottom: 10px;
  i {
    color: #10b3b7;
    font-size: 24px;
  }
}



/* 不同文件类型的颜色 */
.pdf {
  background-color: #f40f02;
}

.doc, .docx {
  background-color: #2a5699;
}

.xls, .xlsx {
  background-color: #207245;
}

.ppt, .pptx {
  background-color: #d24726;
}

.default {
  background-color: #606266;
}
</style>

