<template>
  <div id="page">
    <!-- web -->
    <div v-if="webClient">
      <!-- 查看所有周反思 -->
      <div>
        <div>
          <el-button style="padding:10px;" :type="buttonType" :size="buttonSize" :plain="buttonPlain" @click="openReflectionDialog">{{ $t('loc.plan100') }}</el-button>
        </div>
        <el-dialog width="70%" :title="$t('loc.plan101')" :visible.sync="visable" :before-close="closeReflectionDialog">
          <!-- 学校班级日期 -->
          <div class="flex-content-between">
            <!-- 学校选择框,多个学校时显示下拉框 -->
            <div class="flex-content-start">
              <div v-show="centers.length > 0">
                <el-select size="small" v-model="currentCenterId" @change="changeCenter">
                  <el-option v-for="center in centers" :key="center.id" :label="center.name" :value="center.id"></el-option>
                </el-select>
              </div>
              <!-- 班级选择框,多个学校或一个学校多个班级时显示下拉框 -->
              <div v-show="centers.length >= 1 || groups.length >= 1" class="add-margin-l-20">
                <el-select size="small" v-model="currentGroupId" @change="changeGroup" class="add-margin-l-5">
                  <el-option v-for="group in groups" :key="group.id" :label="group.name" :value="group.id"></el-option>
                </el-select>
              </div>
              <!--          <div v-show="centers.length == 1 && groups.length == 1" class="flex-content-between">-->
              <!--            <span :class="groups.length > 1 ? 'add-margin-l-20' : ''">{{$t('loc.class')}}:&nbsp;</span>-->
              <!--            <div v-if="groups.length > 0">{{groups[0].name}}</div>-->
              <!--          </div>-->
            </div>
            <!-- 日期范围 -->
            <div style="margin-left: 3px;display: flex;align-items: center;">
              <div class="flex-content-end">
                <el-date-picker
                  @focus="closeKeyboard"
                  editable="false"
                  class="from-to-picker"
                  size="small"
                  :clearable="false"
                  v-model="fromDate"
                  type="date"
                  :picker-options="fromPickerOptions"
                  @change="listWeeklyReflections"
                  format="MM/dd/yyyy"
                  value-format="MM/dd/yyyy"
                  placeholder="Start Date">
                </el-date-picker>
                <div class="m-l-xs m-r-xs">-</div>
                <el-date-picker
                  @focus="closeKeyboard"
                  editable="false"
                  class="from-to-picker"
                  size="small"
                  :clearable="false"
                  v-model="toDate"
                  type="date"
                  :picker-options="toPickerOptions"
                  @change="listWeeklyReflections"
                  format="MM/dd/yyyy"
                  value-format="MM/dd/yyyy"
                  placeholder="End Date">
                </el-date-picker>
              </div>
            </div>
          </div>
          <!-- 周反思 -->
          <div v-loading="loadingData" v-if="totalReflections > 0">
            <div class="flex-space-between lg-padding-t-b-20">
              <div class="m-t-sm m-b-sm lg-color-text-primary font-weight-semibold">{{ $t('loc.total') }}: {{ totalReflections }}</div>
              <el-button icon="fa fa-file-pdf-o" type="primary" :loading="pdfLoading" :disabled="totalReflections == 0" @click="generatePdf">PDF</el-button>
            </div>
            <el-table
              ref="reflectionTable"
              :data="reflections"
              header-row-class-name="table-header"
              border
              max-height="250"
              style="width: 100%;">
              <el-table-column
                :resizable="false"
                prop="week"
                :label="$t('loc.planWeek')"
                width="180">
                <template slot-scope="scope">
                  <div>week {{ scope.row.week }}</div>
                </template>
              </el-table-column>
              <el-table-column
                cell-class-name="no-l-r-border"
                :resizable="false"
                :label="$t('loc.plan104')"
                width="180">
                <template slot-scope="scope">
                  <div>{{ $moment(scope.row.fromDate).format('MM/DD') }}-{{ $moment(scope.row.toDate).format('MM/DD')}}</div>
                </template>
              </el-table-column>
              <el-table-column
                :resizable="false"
                prop="content"
                :label="$t('loc.plan105')">
                <template slot-scope="scope">
                  <span class="space-pre-line" style="word-break: break-word;">{{ scope.row.content }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 空页面 -->
          <div v-else>
            <div style="display: flex; flex-direction: column; align-items: center;min-height: 285px;">
              <img style="margin-top: 70px" src="@/assets/img/lesson2/dataReview/empty.png">
              <div style="text-align: center; color:#98a6ad">{{ $t('loc.nodataTip') }}</div>
            </div>
          </div>
        </el-dialog>
      </div>
    </div>
    <!-- ipad -->
    <div v-else>
      <!-- 查看所有周反思 -->
      <div>
        <div>
          <el-button style="padding:10px;" :type="buttonType" :size="buttonSize" :plain="buttonPlain" @click="openReflectionDialog">{{ $t('loc.plan100') }}</el-button>
        </div>
        <el-dialog width="70%" :title="$t('loc.plan101')" :visible.sync="visable" :before-close="closeReflectionDialog">
          <!-- 学校班级日期 -->
          <div class="flex-content-between">
            <!-- 学校选择框,多个学校时显示下拉框 -->
              <div v-show="centers.length > 0">
                <el-select size="small" v-model="currentCenterId" @change="changeCenter" style="width: 180px;">
                  <el-option v-for="center in centers" :key="center.id" :label="center.name" :value="center.id"></el-option>
                </el-select>
              </div>
              <!-- 班级选择框,多个学校或一个学校多个班级时显示下拉框 -->
              <div v-show="centers.length >= 1 || groups.length >= 1">
                <el-select size="small" v-model="currentGroupId" @change="changeGroup" style="width: 180px;">
                  <el-option v-for="group in groups" :key="group.id" :label="group.name" :value="group.id"></el-option>
                </el-select>
              </div>
              <!--          <div v-show="centers.length == 1 && groups.length == 1" class="flex-content-between">-->
              <!--            <span :class="groups.length > 1 ? 'add-margin-l-20' : ''">{{$t('loc.class')}}:&nbsp;</span>-->
              <!--            <div v-if="groups.length > 0">{{groups[0].name}}</div>-->
              <!--          </div>-->

              <!-- 日期范围 -->
              <div>
                <div><span>{{ $t('loc.plan102') }}&nbsp;</span></div>
                <div class="flex-content-end">
                  <el-date-picker
                    @focus="closeKeyboard"
                    editable="false"
                    class="from-to-picker"
                    size="small"
                    :clearable="false"
                    v-model="fromDate"
                    type="date"
                    :picker-options="fromPickerOptions"
                    @change="listWeeklyReflections"
                    format="MM/dd/yyyy"
                    value-format="MM/dd/yyyy"
                    placeholder="Start Date">
                  </el-date-picker>
                  <div class="m-l-xs m-r-xs">-</div>
                  <el-date-picker
                    @focus="closeKeyboard"
                    editable="false"
                    class="from-to-picker"
                    size="small"
                    :clearable="false"
                    v-model="toDate"
                    type="date"
                    :picker-options="toPickerOptions"
                    @change="listWeeklyReflections"
                    format="MM/dd/yyyy"
                    value-format="MM/dd/yyyy"
                    placeholder="End Date">
                  </el-date-picker>
                </div>
              </div>
          </div>
          <!-- 周反思 -->
          <div v-loading="loadingData" v-if="totalReflections > 0">
            <div class="flex-space-between lg-padding-t-b-20">
              <div class="m-t-sm m-b-sm">{{ $t('loc.plan103') }}{{ totalReflections }}</div>
              <el-button icon="fa fa-file-pdf-o" type="primary" :loading="pdfLoading" :disabled="totalReflections == 0" @click="generatePdf">PDF</el-button>
            </div>
            <el-table
              ref="reflectionTable"
              :data="reflections"
              header-row-class-name="table-header"
              max-height="250"
              border
              style="width: 100%;">
              <el-table-column
                :resizable="false"
                prop="week"
                :label="$t('loc.planWeek')"
                width="180">
                <template slot-scope="scope">
                  <div class="lg-color-text-primary">week {{ scope.row.week }}</div>
                </template>
              </el-table-column>
              <el-table-column
                cell-class-name="no-l-r-border"
                :resizable="false"
                :label="$t('loc.plan104')"
                width="180">
                <template slot-scope="scope">
                  <div class="lg-color-text-primary">
                    {{ $moment(scope.row.fromDate).format('MM/DD') }}-{{ $moment(scope.row.toDate).format('MM/DD') }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :resizable="false"
                prop="content"
                :label="$t('loc.plan105')">
                <template slot-scope="scope">
                  <span class="space-pre-line lg-color-text-primary" style="word-break: break-word;">{{ scope.row.content }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 空页面 -->
          <div v-else>
            <div style="display: flex; flex-direction: column; align-items: center;min-height: 285px;">
              <img style="margin-top: 70px" src="@/assets/img/lesson2/plan/no_record.png">
              <div style="text-align: center; color:#676879">{{ $t('loc.nodataTip') }}</div>
            </div>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import pdf from '@/assets/img/file/pdf.png'
import tools from '@/utils/tools'

export default {
  name: 'WeeklyReflectionList',
  props: {
    // 学校列表
    centers: {
      type: Array
    },
    // 按钮形态
    buttonType: {
      type: String
    },
    // 按钮大小
    buttonSize: {
      type: String
    },
    // Plain按钮
    buttonPlain: {
      type: Boolean,
      default: false
    },
    // 当前学校Id
    centerId: {
      type: String
    },
    // 当前班级
    groupId: {
      type: String
    }
  },
  data () {
    return {
      groups: [], // 班级列表
      currentCenterId: '', // 当前学校Id
      currentGroupId: '', // 当前班级Id
      fromPickerOptions: '', // 开始时间选择范围
      toPickerOptions: '', // 结束时间选择范围
      fromDate: '', // 开始时间
      toDate: '', // 结束时间
      visable: false, // 弹窗可视
      reflections: [], // 反思列表
      totalReflections: 0, // 反思数量
      loadingData: false, // 表格数据加载
      pdfLoading: false, // pdf生成loading
      webClient: false
    }
  },
  watch: {
    centers: {
      immediate: true,
      handler (newVal, oldVal) {
        if (this.centers.length > 0) {
          this.groups = this.centers[0].groups
          this.currentCenterId = this.centers[0].id
          this.currentGroupId = this.groups[0].id
          this.toDate = this.$moment().format('MM/DD/YYYY')
          this.fromDate = this.$moment().add(-1, 'months').format('MM/DD/YYYY')
          // 设置开始、结束日期限制规则
          this.setDateOptions()
        }
      },
      deep: true
    }
  },
  created () {
    // 尺寸适配
    this.handleSize()
  },
  methods: {
    // 获取周反思
    listWeeklyReflections () {
      this.loadingData = true
      this.reflections = []
      this.reflectionCount = 0
      this.$axios.get($api.urls().listWeeklyReflections, {
        params: {
          groupId: this.currentGroupId,
          fromDate: this.$moment(this.fromDate).format('YYYY-MM-DD'),
          toDate: this.$moment(this.toDate).format('YYYY-MM-DD')
        }
      })
        .then(res => {
          this.loadingData = false
          this.totalReflections = res.reflectionCount
          this.reflections = res.weeklyPlanReflectionModels
          this.$nextTick(() => {
            this.$refs.reflectionTable.doLayout()
          })
        })
        .catch(err => {
          this.loadingData = false
          this.$message.error(err.message)
        })
    },
    // 切换学校
    changeCenter (centerId) {
      // 通过学校Id，切换对应的班级下拉框
      let groups = this.centers.find(x => x.id === centerId).groups
      this.groups = groups.filter(x => !x.inactive)
      this.currentGroupId = groups[0].id
      this.listWeeklyReflections()
    },
    // 切换班级
    changeGroup (groupId) {
      this.listWeeklyReflections()
    },
    // 设置时间选择规则
    setDateOptions () {
      this.fromPickerOptions = {
        // 开始日期不能晚于结束日期
        disabledDate: (time) => {
          if (this.toDate) {
            return time.getTime() > new Date(this.toDate).getTime()
          }
        }
      }
      this.toPickerOptions = {
        // 结束日期不能早于开始日期
        disabledDate: (time) => {
          if (this.fromDate) {
            return time.getTime() < new Date(this.fromDate).getTime()
          }
        }
      }
    },
    // 打开弹窗
    openReflectionDialog () {
      this.$analytics.sendEvent('web_weekly_virtual_detail_reflection')
      this.toDate = this.$moment().format('MM/DD/YYYY')
      this.fromDate = this.$moment().add(-1, 'months').format('MM/DD/YYYY')
      // 如果是查看自己的周计划，定位到当前周计划的班级
      if (this.centerId == '' || this.centerId == undefined || this.groupId == '' || this.groupId == undefined) {
        this.groups = this.centers[0].groups
        this.currentCenterId = this.centers[0].id
        this.currentGroupId = this.groups[0].id
      } else {
        this.currentCenterId = this.centerId
        this.groups = this.centers.filter(x => x.id == this.currentCenterId)[0].groups
        this.currentGroupId = this.groupId
      }
      this.visable = true
      this.listWeeklyReflections()
    },
    // 关闭弹窗
    closeReflectionDialog () {
      this.visable = false
    },
    // 生成pdf
    generatePdf () {
      this.$analytics.sendEvent('web_weekly_virtual_detail_reflection_pdf')
      this.pdfLoading = true
      let centerName = ''
      let groupName = ''
      this.centers.forEach(x => {
        if (x.id === this.currentCenterId) {
          centerName = x.name
        }
      })
      this.groups.forEach(x => {
        if (x.id === this.currentGroupId) {
          groupName = x.name
        }
      })
      this.$axios.get($api.urls().getWeeklyReflectionsPDF, {
        params: {
          groupId: this.currentGroupId,
          fromDate: this.$moment(this.fromDate).format('YYYY-MM-DD'),
          toDate: this.$moment(this.toDate).format('YYYY-MM-DD'),
          centerName: centerName,
          groupName: groupName
        }
      }).then(res => {
        this.getPdfList(centerName,groupName, this.$moment(this.fromDate).format('YYYY-MM-DD') ,this.$moment(this.toDate).format('YYYY-MM-DD'))
      })
        .catch(error => {
          this.pdfLoading = false
        })
    },
    // 获取pdf生成状态
    getPdfList (siteName,className,fromDate,toDate) {
      let requestData = { type: 'WEEKLY_PLAN_REFLECTION' }
      this.$axios.get($api.urls().pdfList, { params: requestData })
        .then(data => {
          let dataIsCreating = false
          let timeout
          if (data.length == 0 || (data[0].status !== 'SUCCEED' && data[0].status !== 'FAILED')) {
            dataIsCreating = true
          } else {
            this.pdfLoading = false
            if (data[0].pdfUrl) {
              this.$alert(
                '<p class="weekly-reflections-pdf-confirm"><img src="' + this.getFileIcon() + '"><span>' + data[0].pdfName + '</span></p>',
                'Download File', {
                  dangerouslyUseHTMLString: true,
                  confirmButtonText: this.$t('loc.download')
                })
                .then(() => {
                  if (tools.isComeFromIPad()) {
                    // 截取文件名
                    let fileName = data[0].pdfName
                    let requestData = {
                      'emailTemplate': 'week_reflection',
                      'siteName': siteName,
                      'className': className,
                      'downloadFileUrl': data[0].pdfUrl,
                      'fromDate': fromDate,
                      'toDate': toDate,
                      'fileName': fileName
                    }
                    this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
                    .then(() => {
                      this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                        confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                        showCancelButton: false
                      })
                    }).catch(error => {
                      this.$message.error(error.message)
                    })
                  } else {
                    window.location.href = data[0].pdfUrl
                  }
                })
            }
            clearTimeout(timeout)
          }
          if (dataIsCreating && this.pdfLoading) {
            timeout = setTimeout(() => {
              this.getPdfList(siteName,className,fromDate,toDate)
            }, 3000)
          }
        })
        .catch(error => {
        })
    },
    // 返回文件图标
    getFileIcon () {
      return pdf
    },

    handleSize () {
      this.webClient = !tools.isComeFromIPad()
    },
    closeKeyboard () {
      document.activeElement.blur()
    }
  },
  destroyed () {
    this.pdfLoading = false
  }
}
</script>

<style lang="less">
.weekly-reflections-pdf-confirm {
  word-break: break-all;
  display: flex;
  align-items: center;

  & > img {
    margin-right: 10px;
  }
}
</style>

<style lang="less" scoped>
@media only screen and (min-width: 1200px) {
  // web
  .el-table::before {
    height: 0px !important;
  }

  .el-table {
    /deep/ .el-table__body {
      width: 100% !important;
    }

    /deep/ .el-table__body-wrapper::-webkit-scrollbar {
      /* 滚动条整体样式 */
      width: 6px; /* 高宽分别对应横竖滚动条的尺寸 */
      height: 1px;
    }

    /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
      /* 滚动条里面小方块 */
      border-radius: 10px;
      /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1); */
      background: #dddee0;
    }

    /deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
      /* 滚动条里面轨道 */
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
      /* border-radius: 10px; */
      /* background: #EDEDED; */
    }
  }

  .flex-content-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .flex-content-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-content-end {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-direction: row;
  }

  .from-to-picker {
    width: 120px;

    /deep/ .el-input__inner {
      padding-right: 0;
    }
  }

  /deep/ .el-dialog__title {
    font-size: 20px !important;
    color: #111c1c;
    font-weight: 600;
  }

  /deep/ .el-dialog__header {
    border-bottom: 0px solid #EEEEEE !important;
  }

  /deep/ .el-dialog__body {
    border-bottom: 0px solid #EEEEEE !important;
    padding: 10px 20px 20px !important;
    font-size: 14px !important;
  }

  /deep/ .table-header th {
    font-weight: bold;
    color: #111c1c;
    background-color: #F2F5F6;
  }

  .web_flex {
    display: flex;
    align-items: center;
  }
}

@media only screen and (max-width: 1199px) {
  // ipad
  .el-table::before {
    height: 0px !important;
  }

  .el-table {
    /deep/ .el-table__body {
      width: 100% !important;
    }

    /deep/ .el-table__body-wrapper::-webkit-scrollbar {
      /* 滚动条整体样式 */
      width: 6px; /* 高宽分别对应横竖滚动条的尺寸 */
      height: 1px;
    }

    /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
      /* 滚动条里面小方块 */
      border-radius: 10px;
      /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1); */
      background: #dddee0;
    }

    /deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
      /* 滚动条里面轨道 */
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
      /* border-radius: 10px; */
      /* background: #EDEDED; */
    }
  }

  .flex-content-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .flex-content-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-content-end {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-direction: row;
  }

  .from-to-picker {
    width: 120px;

    /deep/ .el-input__inner {
      padding-right: 0;
    }
  }

  /deep/ .el-dialog__title {
    font-size: 24px !important;
    color: #111c1c;
    font-weight: 600;
  }

  /deep/ .el-dialog__header {
    border-bottom: 0px solid #EEEEEE !important;
  }

  /deep/ .el-dialog__body {
    border-bottom: 0px solid #EEEEEE !important;
    padding: 10px 20px 20px  !important;
    font-size: 14px !important;
  }

  /deep/ .table-header th {
    font-weight: bold;
    color: #606266;
    background-color: #F2F5F6;
  }
}
</style>
