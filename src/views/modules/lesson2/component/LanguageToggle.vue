<template>
  <div class="title-font-14">
    <el-popover
      placement="bottom-start"
      width="190"
      popper-class="language-toggle-popover"
      v-model="languageDropdownVisible"
      trigger="click">
      <div class="language-toggle-dropdown-container text-left">
        <!--    特殊下拉框项    -->
        <div class="language-toggle-dropdown-item-tip language-toggle-dropdown-item-switch" @click='switchOriginalLanguage' :class="{ 'language-toggle-dropdown-item-switch-hover': !isSameLanguage, 'disabled-style': isSameLanguage }">
          <div class="word-break">{{currentLanguageTip}}</div>
          <i class="lg-icon lg-icon-switch lg-margin-left-4"></i>
        </div>
        <!--    语言选项    -->
        <div class="add-margin-b-8">
          <div v-for="(item, index) in languageList"
               :key="index" @click="handleLanguageCommand(item)"
               class="language-toggle-dropdown-item"
               :class="item.key !== currentLanguage.key ? 'language-toggle-dropdown-item-hover' : 'disabled-item' ">
            {{item.val}}
          </div>
        </div>
      </div>
      <!--   触发按钮   -->
      <el-button slot="reference" class="language-toggle-button language-toggle-button-hover" :class="{ 'language-toggle-button-color': isLessonColor && !languageDropdownVisible }">
        <i class="lg-icon lg-icon-a-translate1 language-translate-icon"></i>
        <div class="language-toggle-button-text">{{currentLanguageButtonText}}</div>
      </el-button>
    </el-popover>
  </div>
</template>

<script>

import { mapState } from 'vuex'

export default {
  name: 'LanguageToggle',
  props: {
    // 是否显示课程预览的翻译按钮颜色
    isLessonColor: {
      type: Boolean,
      default: false
    },
    // 双向绑定，当前内容的语言码
    value: {
      type: String,
      default: ''
    },
    // 传入的原始语言类型
    originalLangCode: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      languageList: [
        { key: 'en-US', val: 'English' },
        { key: 'es-ES', val: 'Español' },
        { key: 'zh-CN', val: '简体中文' },
        { key: 'pt-BR', val: 'Português do Brasil' }
      ], // 语言列表
      currentLanguage: { key: 'en-US', val: 'English' }, // 当前语言
      languageDropdownVisible: false // 下拉框是否显示
    }
  },
  watch: {
    // 双向绑定设置值
    value: {
      deep: true,
      immediate: true,
      handler (val) {
        // 设置当前语言类型
        const languageItem = this.languageList.find(item => item.key === val)
        if (languageItem) {
          this.currentLanguage = languageItem
        }
      }
    },
    // 如果当前语言码变更
    currentLanguage: {
      deep: true,
      handler (val) {
        this.$emit('input', val.key)
      }
    }
  },
  computed: {
    ...mapState({
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 当前内容的源语言码
    }),
    // 当前语言是否和内容的源语言相同
    isSameLanguage () {
      // 如果有通过 prop 传入的内容源语言类型，以传入的为准
      if (this.originalLangCode && this.originalLangCode !== '') {
        return this.currentLanguage && (this.currentLanguage.key === this.originalLangCode)
      }
      // 如果没有传入，则使用 vuex 中的
      return this.currentLanguage && (this.currentLanguage.key === this.contentOriginalLanguage)
    },
    // 翻译按钮的文本
    currentLanguageButtonText () {
      return this.currentLanguage.val
    },
    // 当前语言提示语
    currentLanguageTip () {
      const currentLanguage = this.currentLanguage
      // 如果当前文本语言与源语言不相同
      if (!this.isSameLanguage) {
        return this.$t('loc.languageSwitchOrigin')
      } else {
        return this.$t('loc.currentlyLanguageTip') + ((currentLanguage.val && currentLanguage.val.trim() !== '') ? currentLanguage.val : 'English')
      }
    }
  },
  methods: {
    // 处理下拉框的点击事件
    handleLanguageCommand (currentLanguageItem) {
      if (this.currentLanguage.key === currentLanguageItem.key) {
        return
      }
      this.languageDropdownVisible = false
      this.currentLanguage = currentLanguageItem
    },
    // 切换回源语言
    switchOriginalLanguage () {
      // 如果有传入的源语言类型，那么使用传入的
      if (this.originalLangCode && this.originalLangCode !== '') {
        this.currentLanguage = this.languageList.find(item => item.key === this.originalLangCode)
      } else {
        this.currentLanguage = this.languageList.find(item => item.key === this.contentOriginalLanguage)
      }
      this.currentLanguage = this.currentLanguage || this.languageList[0]
    }
  }
}
</script>

<style scoped lang="less">
.language-toggle-button {
  border-radius: 36px;
  border: none;
  height: 40px;
}
.language-toggle-button-hover:hover {
  background-color: var(--color-primary-light) !important;
}
.language-toggle-button-color {
  background-color: var(--color-page-background-white);
}
.language-toggle-button-text {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
/deep/ .language-toggle-button > span {
  display: flex;
  align-items: center;
}
.language-toggle-dropdown-container {
  display: flex;
  flex-direction: column;
}
.language-toggle-dropdown-item {
  padding: 4px 16px;
  display: flex;
  height: 40px;
  align-items: center;
  color: var(--color-text-primary);
}
.disabled-item {
  color: var(--color-text-disabled);
  cursor: not-allowed;
}
.language-toggle-dropdown-item-hover:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  cursor: pointer;
}
.language-toggle-dropdown-item-tip {
  background-color: var(--color-page-background-white) !important;
  padding: 4px 16px;
  display: flex;
  align-items: center;
  min-height: 44px;
  cursor: pointer;
}
.language-translate-icon {
  font-size: 20px !important;
  margin-right: 8px;
}
.language-toggle-dropdown-item-switch {
  color: var(--color-text-primary);
}
.language-toggle-dropdown-item-switch-hover:hover {
  color: var(--color-primary) !important;
}
.disabled-style {
  opacity: 0.4;
  color: var(--color-text-placeholder);
}
</style>
<style lang="less">
.language-toggle-popover {
  padding: 0 !important;
}
</style>
