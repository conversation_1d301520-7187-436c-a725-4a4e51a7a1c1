<template>
  <div class="book-select">
    <!--未选择书-->
    <div class="show-unselected" v-if="!book" >
<!--      <lesson-media-viewer :url="image" type="image" style="width: 240px;height: 135px;" :key="reset"/>-->
      <div class="book_video_cover">
        <img :src="image"/>
      </div>
      <el-input placeholder="" v-model="keyword" size="medium" @keyup.enter.native="search">
        <el-button slot="append" @click="search" size="medium" :loading="loading">{{ $t('loc.search') }}</el-button>
      </el-input>
    </div>
    <!--选择后-->
    <div class="show-selected" v-else>
      <div>
        <lesson-media-viewer :url="thumbnail" type="image" fit="cover" style="width: 240px;height: 135px;"/>
      </div>
      <div :title="book.volumeInfo.title">{{ book.volumeInfo.title }}</div>
      <div v-if="book.volumeInfo.authors && book.volumeInfo.authors[0]">
        {{ $t('loc.lessons2LessonDetailToAuthorName') }}
        {{ book.volumeInfo.authors && book.volumeInfo.authors[0] }}
      </div>
      <i class="el-icon-close" @click.stop="deleteBook"/>
    </div>
    <!--书搜索弹框-->
    <!-- 如果是快速添加课程， el-dialog append-to-body -->
    <el-dialog :visible.sync="show" width="691px" style="margin-top: -2vh" class="popup-container" :append-to-body="true">
      <div slot="title" style="height: 21px;font-size: 24px">{{ $t('loc.searchRes') }}</div>
      <div class="book-list scrollbar" v-if="books && books.length && show" >
        <div v-for="item in books" class="book-item">
          <el-image :src="getBookImage(item)" fit="cover">
            <div slot="error" style="
                background-color: #e7e7e7;
                line-height: 0px;
                padding: 10px 20px;
                width: 110px;
                height: 76px;
                display: flex;
                flex-flow: column;
                align-items: center;
                justify-content: center;">
              <el-image :src="errorImage"></el-image>
              <span style="line-height: 20px;font-size: 12px;color: #999;">{{ $t('loc.lesson2FailedLoadingMedia') }}</span>
            </div>
          </el-image>
          <el-tooltip style="cursor: pointer" :enterable="false" :open-delay="Number(1000)" effect="light" placement="bottom-start" v-if="item.volumeInfo.title && item.volumeInfo.title.length>0">
            <div slot="content">{{ item.volumeInfo.title }}</div>
            <div style="cursor: pointer">{{ item.volumeInfo.title }}</div>
          </el-tooltip>
          <div v-if="!item.volumeInfo.title">{{ item.volumeInfo.title }}</div>
          <el-tooltip :enterable="false" :open-delay="Number(1000)" effect="light" placement="bottom-start"  v-if="item.volumeInfo.description && item.volumeInfo.description.length>0">
            <div slot="content" style="width: 600px">{{ item.volumeInfo.description }}</div>
            <div style="cursor: pointer">{{ item.volumeInfo.description }}</div>
          </el-tooltip>
          <div v-if="!item.volumeInfo.description">{{ item.volumeInfo.description }}</div>
          <el-button type="primary" @click="select(item)">{{ $t('loc.select') }}</el-button>
        </div>
      </div>
      <!--无视频书-->
      <div v-else class="video-empty">
        <el-image :src="noBook" fit="cover"></el-image>
        <div>
          {{ $t('loc.lessons2NoBookOrVideoBook') }}
        </div>
      </div>
      <div slot="footer">
        <el-button @click="closeBooks">{{ $t('loc.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import axios from '@/utils/axios'
import configBaseUrl from '@/utils/setBaseUrl'
import _image from './assets/img/book.png'
import LessonMediaViewer from '@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer'
import sensitiveWord from '@/utils/sensitiveWord'
import _errorImage from '@/assets/img/lesson2/image.png'
import _noBook from './assets/img/empty.jpg'

export default {
  name: 'BookSearcher',
  components: { LessonMediaViewer },
  props: [
    'simple', // 是否是快速添加课程
    'value'
  ],
  data () {
    return {
      book: null, // 从搜索结果中选择的书
      keyword: null, // 搜索关键词
      books: null, // 搜索结果
      show: false, // 是否展示搜索结果弹窗
      image: _image,
      errorImage: _errorImage,
      noBook: _noBook,
      loading: false,
      reset: new Date().getTime() // key值
    }
  },
  computed: {
    thumbnail () {
      return this.getBookImage(this.book)
    }
  },
  watch: {
    value: {
      immediate: true,
      handler () {
        this.book = this.value
      }
    },
    book () {
      this.$emit('input', this.book)
    }
  },
  methods: {
    search () {
      if (!this.keyword || !this.keyword.trim()) {
        this.$message.error(this.$t('loc.inptSerch'))
        return
      }
      if (!sensitiveWord(this.keyword)) {
        this.$message.error(this.$t('loc.sensitiveWordViolationMessage').toString().replace('{{word}}', this.keyword))
        return
      }
      this.loading = true
      axios
        .get(`${$api.urls().getBookVolumes}?q=${this.keyword}`, {
          baseURL: configBaseUrl
        })
        .then(res => {
          this.books = res
          this.show = true
        })
        .catch(error => {
          this.$message.error(error.message)
        })
        .finally(() => {
          this.loading = false
        })
    },
    select (book) {
      this.book = book
      this.closeBooks()
    },
    closeBooks () {
      this.show = false
    },
    getBookImage (book) {
      return book && book.volumeInfo && book.volumeInfo.imageLinks &&
        book.volumeInfo.imageLinks.thumbnail
    },
    deleteBook () {
      this.book = null
    }
  }
}
</script>
<style scoped lang="less">

.book-select /deep/ .show-unselected {

  & > .el-input {
    width: 240px;
    margin-top: 19px;
  }
}

.show-selected {
  width: 100%;
  text-align: center;
  position: relative;

  div {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    line-height: normal;

    &:nth-child(2) {
      margin-top: 5px;
    }
  }
}

.book-list {
  width: 651px;
  height: 398px;
  overflow-y: auto;
  border-radius: 4px;
  border: 1px solid #eeeeee;
  display: flex;
  flex-flow: column;
  gap: 9px;
}

.book-item {
  display: grid;
  grid-template-areas: "a b c" "a d c";
  grid-template-columns: 140px 1fr 110px;
  border-bottom: 1px solid #eeeeee;

  & > :first-child {
    height: 78.75px;
    margin: 9px;
    grid-area: a;
    width: 117px;
  }

  & > :nth-child(2) {
    height: 32px;
    font-size: 16px;
    line-height: 32px;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    grid-area: b;
  }

  & > :nth-child(3) {
    height: 42px;
    color: #777777;
    line-height: 21px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    grid-area: d;
  }

  & > :nth-child(4) {
    color: #fff;
    grid-area: c;
    align-self: center;
    justify-self: center;
    padding: 10px 15px;
  }

  &:last-child {
    border-bottom: none;
  }
}

.book-select /deep/ .el-dialog__wrapper {
  & > .el-dialog {
    margin-top: 5vh !important;

    & > .el-dialog__body {
      padding: 0 22px;
    }

    & > .el-dialog__header {
      line-height: 21px;
      padding: 15px 20px;
    }

    & > .el-dialog__footer {
      height: 68px;
    }
  }
}

.el-icon-close {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #888888;
  border-radius: 50%;
  color: #fff;
  width: 20px;
  height: 20px;
  display: inline-block;
  cursor: pointer;
  line-height: 20px;
}

.video-empty {
  text-align: center;
  height: 300px;
  display: flex;
  flex-flow: nowrap column;
  align-items: center;
  justify-items: center;
  justify-content: center;
}

.book_video_cover{
  width: 240px;
  height: 135px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e7e7e7;
}
</style>
