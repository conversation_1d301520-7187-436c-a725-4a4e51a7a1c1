<template>
  <div class="lesson-filter-flex-column">
    <div style="font-size: 14px;color: #606266;">
      {{ domain.abbreviation }}
    </div>
    <el-select v-model="subDomainId" size="small" style="width: 100%;">
      <el-option v-for="item in subDomains" :key="item.id" :label="item.name" :value="item.id"/>
    </el-select>
    <div class="scrollbar-new" style="overflow-y: auto;max-height: 300px;">
      <el-checkbox-group v-model="measureIds">
        <template v-for="item in measures">
          <el-tooltip :enterable="false" :open-delay="Number(1000)" effect="dark" placement="top">
            <div style="width: 120px;font-size: 14px;white-space: pre-wrap;" slot="content">{{ item.description }}</div>
            <el-checkbox :label="item.id">
              {{ item.abbreviation }}
            </el-checkbox>
          </el-tooltip>
        </template>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LessonFilterMeasure',
  props: ['domain', 'value'],
  data () {
    return {
      measures: [], // 可选测评点
      measureIds: this.value || [], // 已选测评点 ID
      subDomainId: '',// 已选的次级领域 ID
      subDomains: this.extractDomains(), // 可选的次级领域
    }
  },
  watch: {
    value (value) {
      value = value || []
      let m = this.measureIds || []
      let equals = !value.find(item => !m.includes(item)) && !m.find(item => !value.includes(item))
      if (!equals) {
        this.measureIds = this.value
      }
    },
    measureIds: {
      deep: true,
      handler (value) {
        this.$emit('input', value)
      }
    },
    subDomainId (value) {
      this.measureIds = []
      let subDomain = this.domain.children.find(item => item.id === value)
      let measures = []
      let nodes = []
      subDomain && nodes.push(...subDomain.children)
      while (nodes.length > 0) {
        let measure = nodes.shift()
        measures.push(measure)
        measure.children && nodes.unshift(...measure.children)
      }
      this.measures = measures
    }
  },
  methods: {
    extractDomains () {
      let subDomains = this.domain.children || []
      return [{ id: '', name: 'Domain' }, ...subDomains.map(item => ({
        id: item.id,
        name: `${item.abbreviation}-${item.description}`
      }))]
    }
  }
}
</script>

<style scoped lang="less">
.el-checkbox-group {
  display: grid;
  gap: 10px;
  padding: 0 !important;
  grid-template-columns: repeat(3, 1fr);

  .el-checkbox {
    max-width: max-content;
  }

}

/deep/ .el-input__inner {
  background-color: #EBEEF5;
  border: none;
}

.lesson-filter-flex-column {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

</style>