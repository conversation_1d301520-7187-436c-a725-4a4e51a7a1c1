<template>
  <div style="margin: 10px 34px 10px 16px;">
    <lesson-filter-measure v-for="(topDomain,index) in frameworkMeasures" :key="topDomain.id"
                           :domain="topDomain" v-model="domainMeasureIds[index]"/>
  </div>
</template>

<script>
import LessonFilterMeasure from './lessonFilterMeasure'
export default {
  name: 'MappedFrameworkFilterCcss',
  components: { LessonFilterMeasure },
  props: [
    'frameworkMeasures', // ccss 框架领域和测评点
    'value'
  ],
  data () {
    return {
      domainMeasureIds: [],// 领域的已选测评点 ID
    }
  },
  watch: {
    domainMeasureIds: {
      deep: true,
      handler () {
        let measureIds = this.domainMeasureIds.reduce((prev, item) => {
          item = item || []
          prev.push(...item)
          return prev
        }, [])
        if (!this._arrayEquals(measureIds, this.value)) {
          this.$emit('input', measureIds)
        }
      }
    },
  },
  methods: {
    _arrayEquals (arr1, arr2) {
      arr1 = arr1 || []
      arr2 = arr2 || []
      return !arr1.find(item => !arr2.includes(item))
        && !arr2.find(item => !arr1.includes(item))
    },
  }
}
</script>

<style scoped lang="less">

</style>