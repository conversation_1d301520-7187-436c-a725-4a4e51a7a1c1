<template>
  <div v-if="mappedFramework.id">
    <el-skeleton :loading="loading===1" animated style="background-color: #fff">
      <template slot="template">
        <div style="display: flex; align-items: center; justify-content: space-between;" v-for="i in 3" :key="i">
          <el-skeleton-item style="width: 5%;margin: 10px 10px 10px 0"/>
          <el-skeleton-item style="width: 90%;margin: 10px 0"/>
        </div>
      </template>
      <component :is="mappedFramework.component" :framework-measures="mappedFramework.measures"
                 v-model="selectedMeasureIds" :mappedDrdpFrameworkIds.sync="mappedDrdpFrameworkIds"/>
    </el-skeleton>
  </div>
</template>

<script>
import constants from '../../../../../utils/constants'
import LessonFilterPLF from './ca-plf'
import LessonFilterCcss from './ccss'
import LessonFilterExistPSAndIT from './mappedTemplate/existPSAndITTemplate'
import LessonFilterDefault from './mappedTemplate/defaultLayoutTemplate'
import Api from '../../../../../api/lessons2'
import {MappedStateFramework, LessonAgeGroup} from '@/utils/constants'

export default {
  name: 'MappedFrameworkFilter',
  components: [
    LessonFilterPLF,
    LessonFilterCcss,
    LessonFilterExistPSAndIT,
    LessonFilterDefault
  ],
  props: [
    'opens',
    'mappedFrameworkId',
    'value'
  ],
  data() {
    return {
      loading: 0, // 框架映射加载状态
      // 可选的映射框架
      mappedFrameworks: [],
      // 已选框架
      mappedFramework: {
        id: null,
        label: '',
        component: null,
        measures: null
      },
      // 已选测评点
      selectedMeasureIds: [],
      // 映射测评点对应的 DRDP 框架的 ID
      mappedDrdpFrameworkIds: []
    }
  },
  watch: {
    opens: {
      immediate: true,
      async handler(value) {
        if (value) {
          // 初始化映射框架的信息
          this.setMappedFrameworks()
        }
      }
    },
    mappedFrameworkId: {
      immediate: true,
      async handler(value) {
        sessionStorage.setItem('mappedFrameworkId', this.mappedFrameworkId)
        let framework = this.mappedFrameworks.find(item => item.id === value)
        if (!framework) {
          return
        }
        if (!framework.measures) {
            // 如果当前测评点是存在 PS 和 IT 的
            if (constants.existPSAndITTemplate.includes(value)) {
              framework.measures = await this.getFrameworkAndMeasures(value, false)
            } else {
              framework.measures = await this.getFrameworkAndMeasures(value)
            }
        }
        this.mappedFramework = framework
        this.selectedMeasureIds = []
        this.mappedDrdpFrameworkIds = []
      }
    },

    selectedMeasureIds(value) {
      if (!this._arrayEquals(value, this.value)) {
        this.$emit('input', value)
        this.$emit('update:mappedDrdpFrameworkIds', this.mappedDrdpFrameworkIds)
      }
    },
    value: {
      immediate: true,
      handler(value) {
        this.selectedMeasureIds = value
      }
    }
  },
  methods: {
    // 获取测评点，领域
    async getFrameworkAndMeasures(frameworkId, compress = true) {
      this.loading = 1
      try {
        return await Api.getOnlyDomainMapFrameworkMeasures(frameworkId, compress).then(response => response.measures)
      } finally {
        this.loading = 0
      }
    },
    _arrayEquals(arr1, arr2) {
      arr1 = arr1 || []
      arr2 = arr2 || []
      return !arr1.find(item => !arr2.includes(item))
        && !arr2.find(item => !arr1.includes(item))
    },
    setMappedFrameworks() {
      let openFramework = MappedStateFramework.flatMap(frameworkEntity => {
        // 如果 grades 为空，直接返回一个包含当前框架的 DRDPMappedFrameworkModel
        if (!frameworkEntity.grades || frameworkEntity.grades.size === 0) {
          // 如果没有则就是全年级
          return LessonAgeGroup.map(grade => ({
            id: frameworkEntity.id,
            curGrade: grade.name,
            name: frameworkEntity.name,
            abbreviation: frameworkEntity.abbreviation
          }))
        } else {
          // 如果 grades 不为空，为每个 grade 创建一个 DRDPMappedFrameworkModel
          return Array.from(frameworkEntity.grades).map(([gradeName, gradeId]) => ({
            id: gradeId, // gradeId 是框架 ID
            curGrade: gradeName, // gradeName 是年级名称
            name: frameworkEntity.name,
            abbreviation: frameworkEntity.abbreviation
          }))
        }
      }).filter(item => this.opens.includes(item.id))
        .map(v => ({
          id: v.id,
          open: true,
          component: LessonFilterDefault, // 默认的模板
          measures: null
        }))
      // 使用 Map 去重
      let uniqueMap = new Map()
      openFramework.forEach(item => {
        if (!uniqueMap.has(item.id)) {
          uniqueMap.set(item.id, item)
        }
      })

      // 将 Map 转换回数组
      openFramework = Array.from(uniqueMap.values());
      // 设置用来显示的模板
      openFramework.forEach(item => {
          if (item.id === constants.ccssFrameworkId) {
            item.component = LessonFilterCcss
          }
          if (item.id === constants.caPlfFrameworkId) {
            item.component = LessonFilterPLF
          }
          if (constants.existPSAndITTemplate.includes(item.id)) {
            item.component = LessonFilterExistPSAndIT
          }
        }
      )
      this.mappedFrameworks = openFramework
    }
  }
}
</script>

<style scoped>

</style>