<template>
  <div>
    <div style="padding: 10px 34px 5px 16px;">
      <el-select v-model="topDomainId" size="small" style="width: 100%;">
        <el-option v-for="item in domains" :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </div>
    <div class="scrollbar-new" style="overflow-y: auto;max-height: 300px;padding-top: 5px;padding-bottom: 10px;" >
      <el-checkbox-group v-model="selectedMeasureIds">
        <template v-for="subDomain in childDomains">
          <div :key="subDomain.id" v-if="subDomain.children && subDomain.children.length>0">
            <div style="font-size: 14px;" class="font-bold lg-color-text-primary">
              {{ subDomain.abbreviation }}
            </div>
            <template v-for="item in subDomain.children">
              <el-tooltip :key="item.id" :enterable="false" :open-delay="Number(1000)" effect="dark" placement="top">
                <div style="width: 200px;font-size: 14px;white-space: pre-wrap;" slot="content">{{ item.description }}</div>
                <el-checkbox :label="item.id">
                  {{ item.abbreviation }}
                </el-checkbox>
              </el-tooltip>
            </template>
          </div>
          <div :key="subDomain.id" v-else>
            <el-tooltip :enterable="false" :open-delay="Number(1000)" effect="dark" placement="top">
              <div style="width: 200px;font-size: 14px;white-space: pre-wrap;" slot="content">{{ subDomain.description }}</div>
              <el-checkbox :label="subDomain.id">
                {{ subDomain.abbreviation }}
              </el-checkbox>
            </el-tooltip>
          </div>
        </template>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>

export default {
  name: 'MappedFrameworkFilterPLF',
  props: [
    'frameworkMeasures', // plf 框架领域和测评点
    'value'
  ],
  computed: {
    domains () {
      let domains = this.frameworkMeasures || []
      return [{ id: '', name: 'Domain' }, ...domains]
    }
  },
  data () {
    return {
      topDomainId: '',
      childDomains: [],
      selectedMeasureIds: []
    }
  },
  watch: {
    topDomainId: {
      immediate: true,
      handler (value) {
        let { children = [] } = this.frameworkMeasures.find(item => item.id === value) || {}
        this.childDomains = children
        this.selectedMeasureIds = []
      }
    },
    selectedMeasureIds (value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style scoped lang="less">
.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .el-checkbox {
    max-width: max-content;
  }
}

/deep/ .el-input__inner {
  background-color: #EBEEF5;
  border: none;
}

</style>