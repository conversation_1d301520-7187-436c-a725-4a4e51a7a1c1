<template>
  <div class="lesson-filter-flex-column">
    <div style="margin: 10px 16px 0;">
      <el-select v-model="topDomainId" size="small" style="width: 100%;">
        <el-option v-for="item in domains" :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </div>
    <div class="scrollbar-new" style="overflow-y: auto;max-height: 300px;">
      <el-checkbox-group v-model="selectedMeasureIds" style="padding-bottom: 10px;"
                         v-if="childDomains && childDomains.length>0">
        <div v-for="subDomain in childDomains">
          <div v-if="subDomain.children && subDomain.children.length>0" style="font-size: 14px;color: #777"
               class="font-bold">
            {{ subDomain.abbreviation }}
          </div>
          <div class="lesson-detail-measure">
            <template v-for="measure in subDomain.children && subDomain.children.length>0 ? subDomain.children : subDomain">
              <el-tooltip :enterable="false" :open-delay="Number(1000)" :offset="100" effect="dark" placement="top">
                <div style="width: 120px;font-size: 14px;white-space: pre-wrap;" slot="content">{{ measure.description }}</div>
                <el-checkbox :label="measure.id" style="max-width: max-content;">
                  {{ measure.abbreviation }}
                </el-checkbox>
              </el-tooltip>
            </template>
          </div>
        </div>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>

export default {
  name: 'DefaultLayoutTemplate',
  props: [
    'frameworkMeasures', // 框架领域和测评点
    'value'
  ],
  computed: {
    domains () {
      let domains = this.frameworkMeasures || []
      return [{ id: '', name: 'Domain' }, ...domains]
    },

  },
  data () {
    return {
      topDomainId: '',
      childDomains: [],
      selectedMeasureIds: [],
    }
  },
  watch: {
    topDomainId: {
      immediate: true,
      handler (value) {
        let { children = [] } = this.frameworkMeasures.find(item => item.id === value) || {}
        let measures = children.filter(item => !item.children || !item.children.length > 0)
        let domains = children.filter(item => item.children && item.children.length > 0)
        this.childDomains = []
        // 处理没有父领域的测评点
        if (measures && measures.length > 0) {
          this.childDomains.push({
            abbreviation: '',
            children: measures
          })
        }
        domains.forEach(item => this.childDomains.push(item))
        this.selectedMeasureIds = []
      }
    },
    selectedMeasureIds (value) {
      this.$emit('input', value)
    }
  },
}
</script>

<style scoped lang="less">
.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .el-checkbox {
    max-width: max-content;
  }
}

.lesson-detail-measure {

}

/deep/ .el-input__inner {
  background-color: #EBEEF5;
  border: none;
}

.lesson-filter-flex-column {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

</style>