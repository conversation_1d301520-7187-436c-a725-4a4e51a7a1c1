<template>
  <div style="display: flex;flex-flow: column;gap: 10px;">
    <div style="height: 32px;margin: 10px 16px 0;background-color: rgb(246 246 246);border-radius: 3px">
      <el-radio-group v-model="topDomainId" text-color="#303133" fill="#fff" style="display: grid;grid-template-columns: repeat(2, 1fr);"
                      size="mini">
        <el-radio-button :id="item.id"
                         :label="item.id" v-for="item in frameworkMeasures">
          {{ item.name }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <div style="margin: 0 16px;">
      <el-select v-model="subDomainId" size="small" style="width: 100%;">
        <el-option v-for="subDomain in subDomains" :key="subDomain.id" :label="subDomain.name" :value="subDomain.id"/>
      </el-select>
    </div>
    <div class="scrollbar-new" style="overflow-y: auto;max-height: 300px;">
      <el-checkbox-group v-model="selectedMeasureIds" style="padding-bottom: 10px;" v-if="childDomains && childDomains.length>0">
        <div v-for="subDomain in childDomains" :key="subDomain.id">
          <div style="font-size: 14px;" class="font-bold lg-color-text-primary"
               v-if="subDomain.children && subDomain.children.length>0">
            {{ subDomain.abbreviation }}
          </div>
          <div class="lesson-detail-measure add-padding-t-10">
            <template v-for="measure in subDomain.children">
              <el-tooltip :enterable="false" :open-delay="Number(500)" hide-after="3000" effect="dark" placement="top">
                <div style="width: 120px;font-size: 14px;white-space: pre-wrap;" slot="content">{{ measure.description }}</div>
                <el-checkbox :label="measure.id">
                  {{ measure.abbreviation }}
                </el-checkbox>
              </el-tooltip>
            </template>
          </div>
        </div>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
import { equalsIgnoreCase } from '@/utils/common'

export default {
  name: 'ExistPSAndITTemplate',
  props: [
    'frameworkMeasures', // 框架领域和测评点
    'value'
  ],
  data () {
    return {
      topDomainId: '',
      subDomainId: '',
      childDomains: [],
      subDomains: [],
      selectedMeasureIds: [],
    }
  },
  watch: {
    frameworkMeasures: {
      immediate: true,
      handler (value) {
        if (value && value.length > 0) {
          this.topDomainId = value[0].id || ''
          this.initSubDomains()
        }
      }
    },
    topDomainId: {
      immediate: true,
      handler (value) {
        this.initSubDomains()
      }
    },
    subDomainId: {
      immediate: true,
      handler (value) {
        this.updateChildDomains()
      }
    },
    selectedMeasureIds (value) {
      this.$emit('input', value)
      this.$emit('update:mappedDrdpFrameworkIds', this.getFrameworkIds(value))
    },
  },
  mounted() {
    this.$nextTick(() => {
      // 确保数据初始化
      this.initSubDomains()
      this.updateChildDomains()
    })
  },
  methods: {
    initSubDomains() {
      this.subDomainId = ''
      this.childDomains = []
      
      if (!this.topDomainId || !this.frameworkMeasures) return
      
      const currentTopDomain = this.frameworkMeasures.find(item => equalsIgnoreCase(item.id, this.topDomainId))
      if (currentTopDomain && currentTopDomain.children) {
        this.subDomains = [{ id: '', name: 'Domain' }, ...currentTopDomain.children]
        
        // 如果有子域，默认选择第一个有效的子域
        if (currentTopDomain.children.length > 0) {
          this.subDomainId = currentTopDomain.children[0].id || ''
        }
      } else {
        this.subDomains = [{ id: '', name: 'Domain' }]
      }
      
      this.selectedMeasureIds = []
    },
    
    updateChildDomains() {
      if (!this.subDomainId || !this.subDomains || this.subDomains.length <= 1) {
        this.childDomains = []
        return
      }
      
      const selectedSubDomain = this.subDomains.find(item => equalsIgnoreCase(item.id, this.subDomainId))
      
      if (selectedSubDomain && selectedSubDomain.children) {
        // 检查children是否为空数组
        if (Array.isArray(selectedSubDomain.children) && selectedSubDomain.children.length === 0) {
          // 尝试从frameworkMeasures中查找更完整的数据
          this.tryLoadFromFramework()
          return
        }
        
        this.childDomains = selectedSubDomain.children
      } else {
        this.childDomains = []
      }
      
      this.selectedMeasureIds = []
    },
    
    tryLoadFromFramework() {
      // 尝试直接从frameworkMeasures中找到更完整的子域数据
      if (!this.frameworkMeasures || !this.topDomainId || !this.subDomainId) {
        return
      }
      
      const topDomain = this.frameworkMeasures.find(item => equalsIgnoreCase(item.id, this.topDomainId))
      if (!topDomain || !topDomain.children) {
        return
      }
      
      const subDomain = topDomain.children.find(item => equalsIgnoreCase(item.id, this.subDomainId))
      if (!subDomain) {
        return
      }
      
      // 如果从frameworkMeasures中找到了更完整的数据
      if (subDomain.children && subDomain.children.length > 0) {
        this.childDomains = subDomain.children
      }
    },
    
    getFrameworkIds (value) {
      if(!value || !value.length) {
        return []
      }
      let topDomain = this.frameworkMeasures.find(item => equalsIgnoreCase(item.id, this.topDomainId))
      if(!topDomain){
        return []
      }
      let name = topDomain.name
      if(name.toUpperCase() === "PS") {
        return ['A21BC800-2FF5-E411-AF66-02C72B94B99B','A5845474-BDCE-E411-AF66-02C72B94B99B','3F7D9A2A-32EE-E411-AF66-02C72B94B99B']
      }
      if(name.toUpperCase() === "IT"){
        return ['E163164F-BDCE-E411-AF66-02C72B94B99B']
      }
      return []
    }
  }
}
</script>

<style scoped lang="less">
.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .el-checkbox {
    max-width: max-content;
  }
}

/deep/ .el-input__inner {
  background-color: #EBEEF5;
  border: none;
}

/deep/ .el-radio-button__inner {
  font-size: 14px;
  margin: 5px;
  padding: 4px 10px;
  width: 90%;
  width: -moz-available;
  width: -webkit-fill-available;
  width: fill-available;
  background-color: rgb(246 246 246);
  border: none;
}
/deep/ .el-radio-button:first-child .el-radio-button__inner {
  border: none;
}
/deep/ .is-active .el-radio-button__inner {
  font-weight: bold;
  background-color: rgb(246 246 246);
  border-radius: 3px
}

.lesson-detail-measure {

}

</style>