<template>
  <!-- 课程过滤条件 -->
  <el-collapse v-model="conditionNames" class="el-collapse">
    <!-- Age Group -->
    <el-collapse-item name="ageGroup">
      <span slot="title" class="lesson-filter-title">{{ $t('loc.ageGp') }}</span>
      <lesson-filter-skeleton v-if="ageGroupsLoading===1"/>
      <template v-else>
        <el-checkbox-group class="lesson-filter-age-group-single" v-model="selectedAges">
          <el-checkbox v-for="item in displayedAgeGroups" :label="item.value" :key="item.value">
            {{ item.name }}
          </el-checkbox>
        </el-checkbox-group>

        <!-- show more/less -->
        <div class="show-more-less">
          <el-button type="text" @click="showAllAgeGroups = !showAllAgeGroups" class="show-more-button">
            {{ showAllAgeGroups ? $t('loc.lessons2ShowLess') : $t('loc.lessons2ShowMore') }}
            <i :class="showAllAgeGroups ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-button>
        </div>
      </template>
    </el-collapse-item>

    <el-collapse-item name="mappedFramework" style="align-items: center" v-if="mappedFrameworkIds.length">
      <template slot="title">
        <div class="w-full"  @click="(event) => event.stopPropagation()">
          <span v-if="mappedFrameworkIds.length === 1" class="lesson-filter-title"
                :title="mappedFrameworks[0].label">
            {{ (mappedFrameworks[0] || {}).label }}
           </span>
          <el-cascader
            v-else
            v-model="selectedCascaderValue"
            size="small"
            :options="cascaderOptions"
            :props="cascaderProps"
            style="width: calc(100% - 34px); line-height: initial;"
            @change="handleCascaderChange"
            @click.stop
          ></el-cascader>
        </div>
      </template>
      <mapped-framework-filter
        :opens="mappedFrameworkIds"
        :mapped-framework-id="mappedFrameworkId"
        v-model="selectedMappedMeasureIds"
        :mappedDrdpFrameworkIds.sync="mappedDrdpFrameworkIds"
      />
    </el-collapse-item>

    <!-- DRDP 2015 Domain -->
    <el-collapse-item name="domain">
      <span slot="title" class="lesson-filter-title">DRDP 2015 Domain</span>
      <lesson-filter-skeleton v-if="domainsLoading===1"/>
      <el-checkbox-group class="lesson-filter-flex-column" v-model="selectedDomainIds" v-else>
        <el-checkbox v-for="item in domains" :label="item.domainId" :key="item.domainId">
          <el-tooltip popper-class="max-width-200" :content="item.domainAbbreviation + '-' + item.domainName" placement="top" effect="dark" :open-delay="300">
            <span>{{ item.domainAbbreviation }}</span>
          </el-tooltip>
        </el-checkbox>
      </el-checkbox-group>
    </el-collapse-item>

    <!-- Theme/Topic -->
    <el-collapse-item name="theme">
      <span slot="title" class="lesson-filter-title">{{ $t('loc.lesson2NewLessonFormLabelTheme') }}</span>
      <lesson-filter-skeleton v-if="themesLoading===1"/>
      <el-checkbox-group class="lesson-filter-flex-column" v-model="selectedThemeIds" v-else>
        <el-checkbox v-for="item in themes" :label="item.id" :key="item.id">
          {{ item.name }}
        </el-checkbox>
      </el-checkbox-group>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import Api from '../../../../../api/lessons2/index'
import { mapState } from 'vuex'
import LessonFilterSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonFilterSkeleton'
import { MappedStateFramework, LessonAgeGroup } from '@/utils/constants'
import MappedFrameworkFilter from './MappedFrameworkFilter'

// 默认显示的年龄组
const defaultDisplayedAgeGroups = {
  '3': true,
  '4': true,
  '5': true,
  'Grade 1': true,
  'Grade 2': true,
}

let timer = null
export default {
  name: 'LessonFilter',
  props: ['ages', 'themeIds', 'domainIds', 'mappedMeasureIds'],
  components: {
    MappedFrameworkFilter,
    LessonFilterSkeleton
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      _open: state => state.common.open
    }),
    displayedAgeGroups() {
      if (this.showAllAgeGroups) {
        return this.ageGroups
      } else {
        return this.ageGroups.filter(item => defaultDisplayedAgeGroups[item.value])
      }
    },
    cascaderOptions () {
      // 将 mappedFrameworks 转换为 el-cascader 需要的嵌套结构
      const frameworks = {}

      this.mappedFrameworks.forEach(framework => {
        // 使用 framework.label 作为分组依据
        if (!frameworks[framework.label]) {
          frameworks[framework.label] = {
            id: framework.label, // 使用 label 作为第一层级的 ID
            label: framework.label, // 使用 label 作为第一层级的显示文本
            children: [],
            frameworkId: framework.id
          }
        }

        // 将年龄作为子选项
        frameworks[framework.label].children.push({
          id: framework.id, // 使用框架的 ID 作为第二层级的 ID
          label: framework.curGrade // 使用 curGrade 作为第二层级的显示文本
        })
      })

      // 对每个框架的 children 按照 LessonAgeGroup 中的索引进行排序
      Object.values(frameworks).forEach(framework => {
        if (framework.children && framework.children.length > 0) {
          framework.children.sort((a, b) => {
            return LessonAgeGroup.findIndex(item => item.name === a.label) - 
                   LessonAgeGroup.findIndex(item => item.name === b.label)
          })
        }
      })

      // 检查每个框架的 children 是否包含所有年龄组
      const allAgeGroups = LessonAgeGroup.map(age => age.name) // 获取所有年龄组的 value
      Object.values(frameworks).forEach(framework => {
        const childrenAges = framework.children.map(child => child.label) // 获取当前框架的所有年龄组
        if (childrenAges.length === allAgeGroups.length && childrenAges.every(age => allAgeGroups.includes(age))) {
          // 如果 children 包含所有年龄组，则移除 children，使框架独占一个选项
          delete framework.children
          // 更新当前 framework 的 ID 为 框架 ID
          framework.id = framework.frameworkId
        }
      })

      return Object.values(frameworks)
    }
  },
  data () {
    return {
      // 可选的映射框架
      mappedFrameworks: [],
      mappedFrameworkId: null,
      // 选中的映射框架id和名称的映射关系
      mappedFrameworkName: [],
      // 已开启的映射框架
      mappedFrameworkIds: [],
      // 用于存储 el-cascader 的选择值
      selectedCascaderValue: [],
      // el-cascader 的配置
      cascaderProps: {
        value: 'id',
        label: 'label',
        children: 'children'
      },
      // 已选择的映射框架的测评点 ID
      selectedMappedMeasureIds: [],
      // 年龄组
      ageGroups: [],
      // 年龄组的映射框架
      drdpMappedFrameworkModels: [],
      // 年龄的 Value 和 Name 的映射关系
      ageGroupMap: new Map(),
      // 领域
      domains: [],
      // 主题
      themes: [],
      // 已选择的年龄组值
      selectedAges: [],
      // 已选择的领域 ID
      selectedDomainIds: [],
      // 已选择的主题 ID
      selectedThemeIds: [],
      conditionNames: ['ageGroup', 'domain', 'theme', 'mappedFramework'], // 筛选条件默认展开的列表项
      ageGroupsLoading: 0, // 年龄组加载状态
      domainsLoading: 0, // 测评点加载状态
      themesLoading: 0, // 主题加载状态
      mappedDrdpFrameworkIds: [], // 映射测评点对应的 DRDP 框架的 ID
      showAllAgeGroups: false, // 是否显示所有年龄组
    }
  },
  watch: {
    value: {
      deep: true,
      handler () {
        value
      }
    },
    _open: {
      immediate: true,
      handler: function (newValue) {
        if (!newValue) {
          return
        }
        let { frameworkMappingFunction = false, lessonMappedFramework = '', lessonMappedFrameworks = null } = newValue
        if (!frameworkMappingFunction) {
          return
        }
        this.lessonMappedFrameworks = lessonMappedFrameworks
        // 获取所有年龄组的 ID 集合
        let allFrameworkIds = new Set()
        if (lessonMappedFrameworks) {
          if (this.selectedAges.length === 0) {
            // 如果 selectedAges 为空，则选取所有年龄组的 ID
            Object.values(lessonMappedFrameworks).forEach(ids => {
              ids.forEach(id => allFrameworkIds.add(id));
            })
          } else {
            // 如果 selectedAges 有值，则只选取选中的年龄组的 ID
            this.selectedAges.forEach(age => {
              if (lessonMappedFrameworks[age]) {
                lessonMappedFrameworks[age].forEach(id => allFrameworkIds.add(id));
              }
            })
          }
        }
        this.mappedFrameworkIds = Array.from(allFrameworkIds || [])
        // 如果当前 this.drdpMappedFrameworkModels 为空，则初始化
        if (!this.drdpMappedFrameworkModels || this.drdpMappedFrameworkModels.length === 0) {
          this.initDRDPMappedFrameworkModels()
        }
        // 当前打开的映射框架
        this.mappedFrameworks = this.drdpMappedFrameworkModels
          .filter(item => this.mappedFrameworkIds.includes(item.id)).map(framework => ({
            id: framework.id,
            label: framework.abbreviation,
            curGrade: framework.curGrade
          })).sort((a, b) => {
            // 按照年级排序
            return LessonAgeGroup.findIndex(item => item.name === a.curGrade) - LessonAgeGroup.findIndex(item => item.name === b.curGrade)
          })
        let mappedFrameworkName = []
        this.mappedFrameworks.forEach(item => {
          mappedFrameworkName[item.id] = item.label
        })
        this.mappedFrameworkName = mappedFrameworkName
        // 默认选中为第一个框架
        this.mappedFrameworkId = this.mappedFrameworkIds[0]
      }
    },
    selectedMappedMeasureIds: {
      deep: true,
      handler () {
        this.syncData()
      }
    },
    selectedAges: {
      deep: true,
      handler (newValue) {
        // 使用 ageGroups 获取当前年龄的值
        // 通过映射关系快速获取 Name 集合
        const selectedNames = newValue.map(value => this.ageGroupMap[value]).filter(name => name !== null)
        // 获取所有年龄组的 ID 集合
        let allFrameworkIds = new Set()
        if (this.lessonMappedFrameworks) {
          if (selectedNames.length === 0) {
            // 如果 selectedAges 为空，则选取所有年龄组的 ID
            Object.values(this.lessonMappedFrameworks).forEach(ids => {
              ids.forEach(id => allFrameworkIds.add(id))
            })
          } else {
            // 如果 selectedAges 有值，则只选取选中的年龄组的 ID
            selectedNames.forEach(age => {
              if (this.lessonMappedFrameworks[age]) {
                this.lessonMappedFrameworks[age].forEach(id => allFrameworkIds.add(id))
              }
            })
          }
        }
        this.mappedFrameworkIds = Array.from(allFrameworkIds || [])
        // 当前打开的映射框架
        this.mappedFrameworks = this.drdpMappedFrameworkModels
          .filter(item => this.mappedFrameworkIds.includes(item.id) &&
            (selectedNames.length === 0 || selectedNames.includes(item.curGrade))).map(framework => ({
            id: framework.id,
            label: framework.abbreviation,
            curGrade: framework.curGrade
          }))
        let mappedFrameworkName = []
        this.mappedFrameworks.forEach(item => {
          mappedFrameworkName[item.id] = item.label
        })
        this.mappedFrameworkName = mappedFrameworkName
        // 默认选中为第一个框架
        this.mappedFrameworkId = this.mappedFrameworkIds[0]
        this.syncData()
      }
    },
    selectedDomainIds: {
      deep: true,
      handler () {
        this.syncData()
      }
    },
    selectedThemeIds: {
      deep: true,
      handler () {
        this.syncData()
      }
    },
    cascaderOptions: {
      deep: true,
      handler (cascaderOptions) {
        // 如果 cascaderOptions 变化，这个时候设置 selectedCascaderValue，selectedCascaderValue 的值是 cascaderOptions 的第一个值
        // 获取 cascaderOptions 的第一个值
        if (cascaderOptions && cascaderOptions.length > 0) {
          // 获取第一个 cascaderOption
          const cascaderOption = cascaderOptions[0]
          // 定义 mappedFrameworkId
          let mappedFrameworkId = null
          // 如果 cascaderOption 有 children，则获取第一个 children
          if (cascaderOption.children && cascaderOption.children.length > 0) {
            this.selectedCascaderValue = [cascaderOption.id, cascaderOption.children[0].id]
            mappedFrameworkId = cascaderOption.children[0].id
          } else {
            this.selectedCascaderValue = [cascaderOption.id]
            mappedFrameworkId = cascaderOption.id
          }
          // 为 mappedFrameworkId 赋值
          this.mappedFrameworkId = mappedFrameworkId
        }
      }
    }
  },
  created () {
    this.getAgeGroups()
    this.getThemes()
    this.listDomains()
    // 初始化 DRDP 框架映射模型
    this.initDRDPMappedFrameworkModels()
  },
  methods: {
    /**
     * 处理 el-cascader 的 change 事件
     * @param value 选择的值
     */
    handleCascaderChange (value) {
      if (value && value.length === 2) {
        // value 是一个数组，表示每个层级的选择
        this.$set(this, 'mappedFrameworkId', value[1])
        this.mappedFrameworkId = value[1]
      } else if (value && value.length === 1) {
        // value 是一个数组，表示每个层级的选择
        this.$set(this, 'mappedFrameworkId', value[0])
        this.mappedFrameworkId = value[0]
      }
    },
    /**
     * 初始化 DRDP 框架映射模型
     */
    initDRDPMappedFrameworkModels() {
      // 如果已经初始化过，则直接返回
      if (this.drdpMappedFrameworkModels && this.drdpMappedFrameworkModels.length > 0) {
        return
      }
      this.drdpMappedFrameworkModels = MappedStateFramework.flatMap(frameworkEntity => {
        // 如果 grades 为空，直接返回一个包含当前框架的 DRDPMappedFrameworkModel
        if (!frameworkEntity.grades || frameworkEntity.grades.size === 0) {
          // 如果没有则就是全年级
          return LessonAgeGroup.map(grade => ({
            id: frameworkEntity.id,
            curGrade: grade.name,
            name: frameworkEntity.name,
            abbreviation: frameworkEntity.abbreviation
          }))
        } else {
          // 如果 grades 不为空，为每个 grade 创建一个 DRDPMappedFrameworkModel
          return Array.from(frameworkEntity.grades).map(([gradeName, gradeId]) => ({
            id: gradeId, // gradeId 是框架 ID
            curGrade: gradeName, // gradeName 是年级名称
            name: frameworkEntity.name,
            abbreviation: frameworkEntity.abbreviation
          }))
        }
      })
    },
    extractDomains (domain) {
      let subDomains = domain.children || []
      return [{ id: '', name: 'Domain' }, ...subDomains.map(item => ({
        id: item.id,
        name: `${item.abbreviation}-${item.description}`
      }))]
    },
    _arrayEquals (arr1, arr2) {
      arr1 = arr1 || []
      arr2 = arr2 || []
      return !arr1.find(item => !arr2.includes(item)) &&
        !arr2.find(item => !arr1.includes(item))
    },
    syncData () {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        this.$emit('update:ages', this.selectedAges)
        this.$emit('update:domainIds', this.selectedDomainIds)
        this.$emit('update:themeIds', this.selectedThemeIds)
        this.$emit('update:mappedMeasureIds', this.selectedMappedMeasureIds)
        this.$emit('update:mappedDrdpFrameworkIds', this.mappedDrdpFrameworkIds)
        this.$emit('update:mappedFrameworkId', this.mappedFrameworkId)
      }, 800)
    },
    // 获取年龄组
    async getAgeGroups () {
      this.ageGroupsLoading = 1
      let region = this.currentUser.default_agency_state
      if (region !== null) {
        Api.getAgeGroups(region).then(
          response => {
            this.ageGroups = response.ageGroups
            // 在获取数据的时候构建映射关系
            this.ageGroupMap = this.ageGroups.reduce((map, item) => {
              map[item.value] = item.name
              return map
            }, {})
          }
        ).finally(() => {
          this.ageGroupsLoading = 0
        })
      } else {
        Api.getAgeGroups().then(
          response => {
            this.ageGroups = response.ageGroups
            // 在获取数据的时候构建映射关系
            this.ageGroupMap = this.ageGroups.reduce((map, item) => {
              map[item.value] = item.name
              return map
            }, {})
          }
        ).finally(() => {
          this.ageGroupsLoading = 0
        })
      }
    },
    // 获取主题
    getThemes () {
      this.themesLoading = 1
      Api.getThemes().then(
        response => {
          this.themes = response.themes
        }
      ).finally(() => {
        this.themesLoading = 0
      })
    },
    // 获取领域
    listDomains () {
      this.domainsLoading = 1
      Api.listDomains().then(
        response => {
          this.domains = response.domainModels
        }
      ).finally(() => {
        this.domainsLoading = 0
      })
    },
    // 添加重置筛选条件的方法
    resetFilter() {
      // 重置所有选中的值
      this.selectedAges = []
      this.selectedDomainIds = []
      this.selectedThemeIds = []
      this.selectedMappedMeasureIds = []
      this.selectedCascaderValue = []
      this.mappedDrdpFrameworkIds = []
      
      this.syncData()
    }
  },
  beforeDestroy () {
    sessionStorage.setItem('mappedFrameworkId', '')
  }
}
</script>

<style scoped lang="less">
.el-collapse {
  background-color: #FFFFFF;
  border: none;
}

.el-collapse /deep/ .el-collapse-item {

  .el-collapse-item__header {
    font-size: 15px;
    color: #2B2B2B;
    background-color: #eeeef5;
    padding-left: 16px;
    font-weight: bold;
    justify-content: center;
  }

  .el-collapse-item__content {
    padding-bottom: 0;
  }

  .el-checkbox__input {
    vertical-align: middle;
    display: inline-flex;
    align-items: center;
  }

  .el-checkbox-group {
    padding-left: 16px;
  }

  .el-checkbox {
    margin-right: 0;
    white-space: normal;
    display: flex;

    .el-checkbox__label {
      display: inline-block;
      vertical-align: text-top;
      padding-left: 7px;
    }

    .el-checkbox__inner {
      width: 16px;
      height: 16px;
    }
  }

  .el-collapse-item__wrap {
    margin-top: 5px;
  }

  .el-input__inner {
    &::placeholder {
      color: #333333;
      font-size: 15px;
    }
  }
}

.el-collapse /deep/ .el-collapse-item:last-child {
  .el-collapse-item__wrap {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}
.lesson-filter-title {
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.lesson-filter-flex-column {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 10px 0;
}

.lesson-filter-age-group-single {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 10px 0;
}

.show-more-less {
  padding: 0 16px;
}

.show-more-button {
  font-weight: normal;
  color: black;
  font-size: 14px;
  padding: 5px 10px;
  display: flex;
  align-items: center;
}

.show-more-button:hover {
  color: var(--color-primary);
}

::v-deep .el-checkbox__inner::after {
  left: 4px;
  top: 1px;
}
</style>
