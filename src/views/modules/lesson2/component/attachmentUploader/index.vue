<template>
  <el-upload action :accept="_accept" :limit="fileLimit" :file-list="fileList"
             :disabled="uploading"
             :show-file-list="!isResource"
             ref="uploader" :http-request="upload" :on-success="handleSuccess" :on-error="handleError"
             :before-upload="beforeUploadHandler">
    <el-button :loading="uploading" plain icon="el-icon-paperclip" :disabled="uploading" slot="trigger">
      {{ $t('loc.attachFile') }}
    </el-button>
    <!-- <div v-if="isResource" :loading="uploading" class="add-padding-tb-8 add-padding-lr-12 height-40 display-inline-flex align-items justify-content border-radius-4" style="background-color: #EBEEF5;">
      <i style="font-size: 12px;color: #676879" class="el-icon-paperclip"></i>
      <span class="add-margin-l-8 line-height-22 font-weight-semibold color-676879">{{$t('loc.attachFile')}}</span>
    </div> -->

    <span v-if="!isResource" slot="tip" style="margin-left: 20px;">
      {{ $t('loc.supportFiles') }}
    </span>
    <div slot="file" slot-scope="{file}" style="position: relative">
      <div style="display: flex;align-items: center">
        <el-image class="el-upload-list__item-thumbnail" v-if="file.status !== 'uploading'"
                  :src="file.icon" alt=""/>
        <a class="el-upload-list__item-name" @click="uploadOpenUrlWithWebAndIPad(file)"  :title="file.name"
           style="margin-right: 0;display: inline;max-width: calc(100% - 150px)">
          {{ file.name }}
        </a>
        <span style="margin-left: 10px">{{ formatSize(file.size) }}</span>
        <label class="el-upload-list__item-status-label" style="margin: 0;top: 5px;">
          <i :class="{'el-icon-upload-success': true, 'el-icon-circle-check': 'text'}"></i>
        </label>
        <i class="el-icon-close" @click="handleFileDeleteClick(file)" style="top:11px"></i>
      </div>
    </div>
  </el-upload>
</template>
<script>
import fileUtils from '../../../../../utils/file'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import tools from "@/utils/tools";

export default {
  name: 'AttachmentUploader',
  props: ['value', 'accept', 'limit','isResource'],
  computed: {
    _accept() {
      return this.accept && this.accept.toLowerCase() || '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx';
    },
    fileLimit() {
      return this.limit || 4;
    }
  },
  data() {
    return {
      fileList: [],
      fileIcon: {doc, docx, pdf, ppt, pptx, xls, xlsx},
      files: this.value || [],
      uploading: false // 正在上传文件
    }
  },
  watch: {
    value(value) {
      if (!this.contains(this.fileList, value)) {
        this.fileList = this.value.slice();
      }
      if (!this.arrayEquals(this.files, this.value)) {
        this.files = this.value;
      }
    },
    files(value) {
      this.$emit('input', value)
    }
  },
  methods: {
    handleFileDeleteClick(file) {
      if (!this.isResource) {
        this.fileList = this.fileList.filter(item => item.id !== file.id);
      }
      this.files = this.files.filter(item => item.id !== file.id);
    },
    beforeUploadHandler(file) {
      // 文件类型校验
      let type = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
      if (!this._accept.split(',').includes(type)) {
        this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'));
        return false;
      }
      // 文件大小校验，10MB
      if (file.size / 1000 / 1000 > 10) {
        this.$message.error(this.$t('loc.lessons2FileUploadTips'));
        return false;
      }
      // 开始上传，设置上传状态
      if (this.isResource) {
        this.uploading = true
      }
      return true
    }
    ,
    upload(option) {
      return fileUtils.uploadFile(option.file,
        {
          privateFile: false,
          processEventHandler: e => option.onProgress(e),
          annexType: 'annex'
        });
    },
    handleSuccess(response, file) {
      // 上次成功，上传状态重置
      if (this.isResource) {
        this.uploading = false
      }
      file.icon = this.fileIcon[file.name.substring(file.name.lastIndexOf('.') + 1)]
      if (!response || !response.id) {
        file.status = 'failed';
        return;
      }
      file.id = response.id;
      if (!this.isResource) {
        this.fileList = [...this.fileList, file]
      }
      this.files = [...this.files, { id: response.id, name: file.name, url: response.public_url, size: file.size, icon: file.icon }]
    }
    ,
    handleError(err) {
      // 上次失败，上传状态重置
      if (this.isResource) {
        this.uploading = false
      }
      let message = err && err.error_message;
      message && this.$message.error(message);
    }
    ,
    formatSize(size) {
      let value = size;
      let suffix = 'B';
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2);
        suffix = 'KB'
      }
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2);
        suffix = 'MB'
      }
      return `${value}${suffix}`;
    }
    ,
    // 判断文件是否相同
    fileEquals(a, b) {
      a = a || {};
      b = b || {};
      return a.id === b.id;
    }
    ,
    // 判断文件列表的包含关系
    contains(a, b) {
      a = a || [];
      b = b || [];
      for (let temp of b) {
        if (!a.find(item => this.fileEquals(a, b))) {
          return false;
        }
      }
      return true;
    },
    // 判断文件列表的相等关系
    arrayEquals (a, b) {
      return this.contains(a, b) && this.contains(b, a);
    },
    // 重置
    reset () {
      this.fileList = []
      this.files = []
    },
    // 文件打开方式
    uploadOpenUrlWithWebAndIPad (file) {
      // 通过文件id找到文件的url
      var currentFile = this.files.filter(x => x.id == file.id)[0]
      tools.openUrlWithWebAndIPad(currentFile.url)
    }
  },
  created () {
    this.value && this.value.forEach(file => {
      file.icon = this.fileIcon[file.name.substring(file.name.lastIndexOf('.') + 1)]
    })
    this.fileList = this.value || [];
  }
}
</script>