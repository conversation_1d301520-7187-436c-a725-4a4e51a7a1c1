<template>
  <div class="reading-passage">
    <!-- 使用 LgTabs 作为最外层容器 -->
    <LgTabs :tabs="tabs" :value="activeTab" @change="handleTabChange" size="mini" />
    <!-- <div class="reading-passage-content" v-for="item in value" :key="item.id"> -->
    <div class="reading-passage-content" v-for="item in value" :key="item.id">
      <!-- 标签页内容区域 -->
      <template v-if="activeTab === 'Customize'">
        <!-- 三个下拉框，使用 Element UI 的 el-select 组件 -->
        <div>
          <div class="anchor-title">
            <div>
              <span>Anchor Title</span>
            </div>
            <!-- 第一个输入框，替换下拉框为 el-input -->
            <el-input v-model="avticeRedingPassage.title" placeholder="请输入内容" style="width: 180px;"></el-input>
          </div>
          <div>
            <div>
              <span>Type of Reading</span>
            </div>
            <div>
              <el-button>
                {{ avticeRedingPassage.readingType }}
              </el-button>
            </div>
          </div>
          <div>
            <div>
              <span>Customize</span>
            </div>
            <div>
              <!-- Customize -->
              <el-dropdown trigger="click">
                <el-button type="primary" size="small">
                  Customize
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <i class="el-icon-arrow-up"></i>
                    Make Shorter
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <i class="el-icon-arrow-down"></i>
                    Make Longer
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>

        <div>
          Anchor Text:
        </div>

        <div style="display: flex; flex-direction: row;">
              <!--没有资源/主动不显示资源/按钮置灰也就是生成时，都不显示实施步骤资源-->
              <div
                :style=" !showImpStepSource || isCenterLesson ? 'width:100%' :'width: 72%'">
                <el-form-item ref="implementationSteps" prop="implementationSteps">
                  <el-skeleton :rows="4" animated :loading="loading && !lesson.implementationSteps || this.implementationStepsLoading">
                    <template>
                      <editor ref="editorRef" v-model="item.content"
                              :hiddeImpStepSource="!showImpStepSource" :impStepModel="true"
                              :placeholder="$t('loc.unitPlannerLessonImplementationStepsPlaceholder')"
                              />
                    </template>
                  </el-skeleton>
                </el-form-item>
              </div>
              <!--实施步骤资源内容-->
              <!--按钮被禁用时，也就是重新生成时，无论是否有资源都不展示-->
              <div v-show="showImpStepSource && !isCenterLesson" :style="{ width: showImpStepSource ? '28%' : '0'}">
                <anchorTextSources
                  :sourcePreview="false"
                  :lesson=lesson
                  :showImpStepSource=showImpStepSource
                  parentComponentName="LessonDetail"
                  @upLessonImpStepData="upLessonImpStepData"
                  @before-save-resources="implementationStepsLoading = true"
                  @save-resources-success="saveResourcesSuccess"
                  @save-resources-failed="implementationStepsLoading = false"
                  ref="implementationStepSources"
                  style="margin-left: 10px;"
                />
              </div>
            </div>
      </template>
    </div>
  </div>
</template>

<script>
import LgTabs from '@/components/LgTabs'
import anchorTextSources from '@/views/modules/lesson2/component/anchorTextSources.vue'

export default {
  name: 'ReadingPassage',
  components: {
    LgTabs
  },
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeTab: '',
      tabs: [{
        label: "Customize",
        name: "Customize"
      }],
      avticeRedingPassage: {}
    }
  },
  watch: {
    value: {
      handler(newVal) {

      },
      immediate: true,
      deep: true
    },
  },
  computed: {

  },
  methods: {
    handleTabChange(tab) {
      this.activeTab = tab.name
    }
  }
}
</script>

<style lang="scss" scoped>
</style>