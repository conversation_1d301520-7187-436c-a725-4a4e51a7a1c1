<template>
  <div style="padding-bottom: 20px;">
    <!-- 头部内容 -->
    <slot name="header" :total="total"/>
    <!-- 去除了 header 的 margin-bottom ，所以应该给 lesson-list 添加 margin-top -->
    <!-- 课程列表 -->
    <div class="lesson-list" style="margin-top: 16px;" v-if="items && items.length > 0" v-infinite-scroll="loadPage" ref="lessonList">
      <slot v-for="lesson in items" :lesson="lesson"/>
    </div>
    <div v-if="loading === 1 && (items && items.length <= 0)" class="lesson-list">
      <lesson-list-skeleton v-for="i in 8" :key="i"/>
    </div>
    <div v-else-if="loading===1 && (items && items.length > 0)">
      <div style="margin-top: 20px;font-size: 16px;text-align: center">
        <i class="el-icon-loading"></i> {{$t('loc.lessons2LessonListLoading')}}
      </div>
    </div>
    <div style="margin-top: 20px;font-size: 14px;text-align: center" v-if="ended && endScroll">
      {{$t('loc.lessons2CommentEndTitle')}}
    </div>
    <!-- 脚部内容 -->
    <slot name="footer" :empty="(!items || !items.length) && loading === 2"/>
  </div>
</template>
<script>
import LessonListSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonListSkeleton'
const getStyleComputedProperty = (element, property) => {
  if (element === window) {
    element = document.documentElement
  }

  if (element.nodeType !== 1) {
    return []
  }
  // NOTE: 1 DOM access here
  const css = window.getComputedStyle(element, null)
  return property ? css[property] : css
}
const scope = 'ElInfiniteScroll'
export default {
  name: 'LessonList',
  props: [
    'params',
    'loader'
  ],
  components: {
    LessonListSkeleton
  },
  watch: {
    params: {
      deep: true,
      handler () {
        this.reload()
      }
    },

    /**
     * 监听 loader 的变化, 如果 loader 发生变化, 则重新加载
     */
    loader () {
      this.reload()
    }
  },
  data () {
    return {
      pageSize: 8, // 每次加载的页大小
      pageNum: 1, // 已加载页
      items: [],// 已加载的数据
      total: 0, // 数据总条数
      loading: 0,// 0 未加载，1 加载中，2 已加载
      ended: false, // 数据是否已全部加载
      endScroll: false // 触发二次加载
    }
  },
  created () {
    this.loadPage()
  },
  beforeDestroy () {
    let el = this.$refs.lessonList && this.$refs.lessonList.$el || this.$refs.lessonList
    if (!el) {
      return
    }
    let { container, onScroll, observer } = el[scope]
    container.removeEventListener('scroll', onScroll)
    observer && observer.disconnect()
  },
  methods: {
    async loadPage () {
      // 已加载完或正在加载，则不再加载
      if (this.ended || this.loading === 1) {
        return 0
      }
      this.loading = 1
      let list = this.items
      // 加载下一分页
      let page
      let error

      let el = this.$refs.lessonList && this.$refs.lessonList.$el || this.$refs.lessonList
      // 触发第二次加载
      if (list && list.length > 0) {
        this.endScroll = true
      }

      try {
        page = await this.loader({ ...this.params, pageSize: this.pageSize, pageNum: this.pageNum })
      } catch (e) {
        error = e
      }
      // 如果分页被重置，则丢弃本次加载结果
      if (this.items !== list) {
        return
      }

      this.loading = 2
      // 成功处理
      if (page) {
        this.items = this.items.concat(...page.items)
        // 检查是否存在非系统课程，如果存在则设置第一个课程的 isFirstLesson 为 true
        if (this.items.length > 0 && this.items.some(lesson => lesson.type && lesson.type.toUpperCase() !== 'CURRICULUM-PLUGIN_SYSTEM')) {
          this.items[0].isFirstLesson = true
        }
        this.total = page.total
        this.$emit('update:total', this.total)
        this.ended = this.total <= this.items.length
        this.pageNum++
        return page
      }
      // 失败处理
      this.ended = true
      error && error.message && this.$message.error(error && error.message)
    },
    async reload () {
      // 去掉以前的无限加载
      let el = this.$refs.lessonList && (this.$refs.lessonList.$el || this.$refs.lessonList)
      if (el) {
        let { container, onScroll, observer } = el[scope]
        container && container.removeEventListener('scroll', onScroll)
        observer && observer.disconnect()
        el[scope].observer = null
      }
      this.items = []
      this.pageNum = 1
      this.total = 0
      this.loading = 0
      this.ended = false
      this.endScroll = false
      return await this.loadPage()
      // 无限加载处理
      /*el = this.$refs.lessonList && (this.$refs.lessonList.$el || this.$refs.lessonList)
      if (!el) {
        return await this.loadPage()
      }
      let {container, onScroll, observer} = el[scope]
      if (!observer) {
        observer = el[scope].observer = new MutationObserver(onScroll)
        observer.observe(container, {childList: true, subtree: true})
        return await this.loadPage()
      }*/
    }
  }
}
</script>
<style scoped lang="less">
.lesson-list {
  display: grid;
  grid-gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
}
</style>
