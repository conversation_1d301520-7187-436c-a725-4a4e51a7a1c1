<template>
  <div class="image-generator">
    <div class="preview-container">
      <h2>Shared Image</h2>
      <div ref="captureElement" class="share-image">
        <!-- 背景图片 -->
        <img
          src="~@/assets/img/lesson2/unitPlanner/shared_social_media.png"
          class="background-image"
        >

        <!-- 内容叠加层 -->
        <div class="content-overlay">
          <!-- 右侧上部图片 -->
          <div class="top-image-container" ref="captureCover">
            <img
              :src="canvasImageUrl"
              crossorigin="anonymous"
              class="top-image"
            >
          </div>

          <!-- 右侧内容区 -->
          <div class="content-container">
            <h2 class="course-title">{{ unitTitle }}</h2>

            <div class="info-row">
              <div class="info-box">
                Weeks: <span class="font-set">{{ unitWeekCount }}</span>
              </div>
              <div class="info-box">
                Activities: <span class="font-set">{{ weekActivities }}</span>
              </div>
            </div>

            <p class="course-description">
              🌱 {{ unitDescription }}
            </p>

            <div class="tags-row">
              <span class="tag" style="max-width: 132px;">{{ unitGrade }}</span>
              <span class="tag">{{ getEllipsisText(unitFrameworkName, 27) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="button-group">
        <button @click="uploadImage" class="btn download-btn">Download PNG</button>
      </div>
    </div>

  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import fileUtils from '@/utils/file.js'
import tools from '@/utils/tools'

export default {
  name: 'ImageGenerator',
  props: {
    unit: {
      type: Object,
      default: () => ({})
    },
    unitCoverMediasUrl: {
      type: String,
      default: ''
    },
    unitTitle: {
      type: String,
      default: ''
    },
    unitDescription: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      unitImageLoaded: true // 单元图片加载成功标志
    }
  },
  created () {
  },
  computed: {
    // 单元周数
    unitWeekCount() {
      return this.unit.weekCount || (this.unit.baseInfo && this.unit.baseInfo.weekCount) || ''
    },
    // 单元年级
    unitGrade() {
      return this.unit.grade || (this.unit.baseInfo && this.unit.baseInfo.grade) || ''
    },
    // 单元框架名称
    unitFrameworkName() {
      return this.unit.frameworkName || (this.unit.baseInfo && this.unit.baseInfo.frameworkName) || ''
    },
    // 周计划活动数
    weekActivities () {
      if (this.unit && this.unit.weeklyPlans && this.unit.weeklyPlans.length > 0) {
        return this.unit.weeklyPlans.reduce((total, week) => {
          return total + (week.items ? week.items.length : 0) + (week.centerItems ? week.centerItems.length : 0)
        }, 0)
      }
      return 0
    },
    // 为Canvas操作创建专用的URL
    canvasImageUrl () {
      const urlObj = new URL(this.unitCoverMediasUrl)
      // 添加特殊标记，表明这个请求是为Canvas准备的
      urlObj.searchParams.set('forCanvas', Date.now())
      return urlObj.toString()
    }
  },
  methods: {
    // 是否只截取封面
    async uploadImage (onlyCover) {
      try {
        // 根据要求获取不同的 element
        const element =  onlyCover ? this.$refs.captureCover : this.$refs.captureElement

        // 图片转换选项
        const options = {
          useCORS: true, // 允许加载跨域图片
          backgroundColor: null, // 透明背景
          allowTaint: true, // 允许渲染跨域图片
          taintTest: false, // 禁用taint测试以允许跨域
          width: onlyCover ? 653 : 1200, // 设置固定宽度
          height: onlyCover ? 313 : 675, // 设置固定高度
          scrollX: 0,
          scrollY: 0,
          windowWidth: document.documentElement.offsetWidth,
          windowHeight: document.documentElement.offsetHeight,
          imageTimeout: 5000,
          scale: 2,
          logging: false // 这一行，关掉日志
        }
        // canvas 转换
        const canvas = await html2canvas(element, options)
        const blob = await new Promise((resolve) => {
          canvas.toBlob(resolve, 'image/png')
        })
        if (!blob) return

        const fileName = `/${tools.uuidv4()}.png`
        const file = new File([blob], fileName, { type: 'image/png' })
        const uploadInfo = { uploadProgressRate: 0 }
        // 文件上传
        const res = await fileUtils.uploadFile(file, {
          privateFile: false,
          processEventHandler: (progressEvent) => {
            let progressRate = (progressEvent.loaded / progressEvent.total).toFixed(2) * 100
            if (progressRate === 100) progressRate -= 1
            uploadInfo.uploadProgressRate = progressRate
          }
        })

        if (!res || !res.id) return

        // 返回数据
        return {
          id: res.id,
          url: res.public_url,
          size: file.size,
          fileKey: res.fileKey,
          type: 'image/png',
          uploadTime: new Date()
        }
      } catch (err) {
        return
      }
    },

    // 获取省略号文本
    getEllipsisText(text, maxLen) {
      if (!text) return '';
      return text.length > maxLen ? text.slice(0, maxLen) + '...' : text;
    }

  }
}
</script>

<style scoped>
.image-generator {
  margin: 0 auto;
  padding: 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

h2 {
  margin-bottom: 20px;
  color: #555;
  text-align: center;
}

/* 预览容器 */
.preview-container {
  padding: 20px;
  border-radius: 8px;
}

/* 分享图片样式 */
.share-image {
  display: inline-block;
  margin: 0 auto 20px;
  box-shadow: none;
  border-radius: 0;
  overflow: hidden;
  border: none;
  position: relative;
  width: 1200px;
  height: 675px;
}

/* 背景图片 */
.background-image {
  display: block;
  width: 1200px;
  height: 675px;
  object-fit: cover;
  border-radius: 0;
}

/* 内容叠加层 */
.content-overlay {
  position: absolute;
  z-index: 1;
  width: 654px; /* 使用固定宽度 */
  height: 100%; /* 确保高度100% */
  right: 0; /* 改用靠右定位 */
  top: 0; /* 确保从顶部开始 */
  display: flex;
  flex-direction: column;
  padding: 0;
  /* 移除left属性 */
  overflow: hidden;
}

/* 右侧上部图片 */
.top-image-container {
  height: 313px;
  overflow: hidden;
}

.top-image {
  width: 100%;
  height: 100%;
  max-height: none;
  object-fit: cover;
  object-position: center center;
  display: block;
}

.font-set {
  color: #111C1C;
  font-size: 30px;
  font-style: normal;
  font-weight: 900;
  line-height: 22px;
}

/* 右侧内容容器 */
.content-container {
  background-color: white;
  flex: 1;
  position: relative;
  padding: 30px 30px 30px 0;
  margin-left: 35px;
  border-top: 1px solid #e0e0e0;
}

/* 课程标题 */
.course-title {
  color: #0A7C6E;
  font-size: 32px;
  line-height: 40px;
  height: 50px;
  margin: 0 0 15px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  font-weight: bold;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.info-box {
  background-color: #F5F5F5;
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 31px;
  color: #111C1C;
  width: 48%;
  text-align: center;
}

.course-description {
  font-size: 30px;
  color: #444;
  line-height: 1.4;
  margin: 20px 0 20px 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.tags-row {
  display: flex;
  gap: 40px;
  flex-wrap: nowrap;
}

.tag {
  background-color: #E8F5F4;
  color: #111C1C;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 26px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap; /* 不允许内容内部换行 */
  text-overflow: ellipsis; /* 超出显示省略号 */
}

/* 按钮组 */
.button-group {
  text-align: center;
  margin-top: 20px;
}

.btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.download-btn {
  background-color: #0A7C6E;
}

.download-btn:hover {
  background-color: #086256;
}

/* 消息提示 */
.message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10000;
}

.message.success {
  background-color: #4CAF50;
}

.message.error {
  background-color: #F44336;
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0A7C6E;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto 15px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

</style>