<template>
  <div class="add-padding-b-20  unit-list-limit-width">
    <!--Header 的骨架屏-->
    <el-skeleton :loading="loading" animated>
      <template slot="template">
        <div class="add-padding-t-20 add-padding-b-20 add-padding-lr-10
         display-flex justify-content-between align-items flex-wrap gap-12">
          <div
            class="display-flex gap-12"
          >
            <el-skeleton-item variant="button " style="width: 200px;" />
            <el-skeleton-item variant="button " style="width: 130px;" />
          </div>
          <div class="btn-style-group display-flex align-items gap-10">
            <el-skeleton-item variant="text " style="width: 239px;" />
            <el-skeleton-item variant="button " style="width: 120px;" />
          </div>
        </div>
      </template>
    </el-skeleton>
    <!--card list 骨架屏-->
    <el-row :gutter="24">
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8" v-for="(unit, index) in 8" :key="index">
        <el-card class="lg-pointer unit-card card-box-shadow"
                 style="height: 340px !important;">
          <el-skeleton animated :loading="loading">
            <!-- Loading 骨架屏 -->
            <template slot="template">
              <div>
                <!-- 标题 -->
                <el-skeleton-item variant="p" class="w-70-percent" />
                <!-- 周数、活动数 -->
                <el-row :gutter="10" class="m-t-sm add-padding-tb-6">
                  <el-col :span="12" class="display-flex flex-direction-col align-items">
                    <el-skeleton-item variant="rect" class="h-40" />
                  </el-col>
                  <el-col :span="12" class="display-flex flex-direction-col align-items">
                    <el-skeleton-item variant="rect" class="h-40" />
                  </el-col>
                </el-row>
                <!-- 概览 -->
                <el-skeleton-item variant="p" class="m-t-sm h-60" />
                <!-- 年龄段 -->
                <el-skeleton-item variant="p" class="m-t-sm w-70-percent h-20" />
                <!-- 更新时间 -->
                <el-skeleton-item variant="p" class="m-t-md w-70-percent h-20" />
              </div>
            </template>
          </el-skeleton>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>

export default {
  components: {
  },
  props: {
    loading: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
    }
  },
  computed: {
  },
  async created () {

  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
::v-deep{
  .el-dropdown-menu__item{
    color: #111c1c;
  }
}
.unit-exemplar {
  background: linear-gradient(180deg, #DCF1FC -0.01%, #FFF 23.84%);
  .exemplar-tag {
    height: 24px;
    border-radius: 90px;
    background: #C2A6FD;
  }

}
::v-deep .ai-btn{
  display: flex;
  align-items: center;
  i{
    font-size: 22px;
  }
  &-active {
    border-radius: 4px;
    background: linear-gradient(271deg, rgba(45, 156, 219, 0.10) 0.32%, rgba(135, 139, 249, 0.10) 67.59%, rgba(187, 107, 217, 0.10) 142.72%) !important;
    color: #6E8FF0 !important;
    i{
      color: #40c2c5;
    }
  }
  &-exemplar {
    border-radius: 4px;
    width: 214px;
    background: linear-gradient(271deg, rgba(45, 156, 219, 0.10) 0.32%, rgba(135, 139, 249, 0.10) 67.59%, rgba(187, 107, 217, 0.10) 142.72%) !important;
    color: #6E8FF0;
    justify-content: center;
    .el-icon-upload {
      display: none;
    }
    span {
      margin-left: 0;
    }
  }
}
::v-deep{
  .el-dropdown-menu__item{
    color: #111c1c;
  }
}

::v-deep .ai-btn{
  display: flex;
  align-items: center;
  i{
    font-size: 22px;
  }
  &-active{
    border-radius: 4px;
    background: linear-gradient(271deg, rgba(45, 156, 219, 0.10) 0.32%, rgba(135, 139, 249, 0.10) 67.59%, rgba(187, 107, 217, 0.10) 142.72%) !important;
    color: #6E8FF0 !important;
    i{
      color: #40c2c5;
    }
  }
}
/deep/ .el-progress__text{
  color: var(--color-primary);
  font-weight: 600;
}
/deep/.el-dropdown-link:hover {
  color: var(--color-primary);
}
.edit-dropdown {
  color: var(--color-text-primary);
}
.edit-dropdown:hover {
  color: #40c2c5;
}
.delete-dropdown {
  color: #0B0B0B;
}
.delete-dropdown:hover {
  color: red;
}
.unit-grade-box {
  white-space: nowrap;
  padding: 10px;
}
.unit-framework-box {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 10px;
  min-width: 0;
  max-width: fit-content;
}
.grade-tag {
  background: #DDF2F3;
  border-radius: 27px;
  padding: 4px 10px;
}
.framework-tag {
  background: #DDF2F3;
  border-radius: 27px;
  padding: 4px 10px;
}
.adapted-class {
  background: var(--color-ai-assistant-opacity);
  border-radius: 27px;
  padding: 4px 10px;
}
.unit-overview {
  min-height: 45px;
}
.w-70-percent {
  width: 70%;
}
.h-20 {
  height: 20px;
}
.h-40 {
  height: 40px;
}
.h-60 {
  height: 60px;
}
::v-deep .unit-card {
  border-radius: 8px;
  margin: 0 8px 24px!important;
}
.draft-tag {
  background: rgba(245, 108, 108, 0.2);
  border-radius: 27px;
  padding: 2px 12px;
  color: #CC393E;
  height: 20px;
}
.overflow-ellipsis-three {
  -webkit-line-clamp: 2!important;
}
.user-name {
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}
.card-box-shadow:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.3) !important;
}
.update-date {
  max-width: 165px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-left: 10px;
}
/deep/ .el-dropdown-menu {
  width: 150px!important;
}
.adapted-tag {
  color: var(--color-white) !important;
  background: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
}
/deep/ .el-divider--horizontal {
  margin: 0px!important;
}
.card-share{
  img{
    width: 24px;
    height: 24px;
    margin-right: 16px;
  }
}
</style>
<style lang="less">
// 重命名确认框样式
.unit-card-rename-confirm {
  & > .el-message-box__content {
    padding: 9px 24px 4px 24px;
  }
}
/* 一级菜单项带有子菜单 */
.dropdown-item-with-submenu {
  position: relative;
}
/* 二级菜单样式 */
.submenu {
  position: absolute;
  top: 0;
  left: 100%;
  display: none;
  border-radius: 8px;
  background: var(--ffffff, #FFF);
  /* 卡片投影 (选中) */
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.20);
  z-index: 1000;
  white-space: nowrap;
  padding: 8px;
}

/* 悬停时显示二级菜单 */
.dropdown-item-with-submenu:hover .submenu {
  display: block;
}

/* 二级菜单项样式 */
.submenu-item {
  padding: 4px 8px;
  cursor: pointer;
  white-space: nowrap;
  color: #111C1C;
  img{
    width: 24px;
    height: 24px;
    margin-right: 12px;
  }
}

.submenu-item:hover {
  background-color: #e7f7f8;
  color: #10B3B7;
}
.share-button{
  width: 102px;
  height: 40px;
  background: url(~@/assets/img/magicCurriculum/share.png) no-repeat center center;
  background-size: contain;
  margin-left: 10px;
}
.notification_error {
  top: 80px !important;
  z-index: 2000;
  width: initial;
  align-items: center;
  background-color: #FEF0F0;
}

.submenu-left {
  right: 100%;
  left: auto;
}
.unit-list-limit-width {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
