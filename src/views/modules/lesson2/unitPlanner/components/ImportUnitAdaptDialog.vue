<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    @click.stop
    :before-close="handleClose">
    <div class="feature-reminder" role="dialog" aria-labelledby="tour-title">
      <div class="content-wrapper">
        <div class="lg-margin-l-r-24 lg-color-text-primary import-unit-adapt-title">{{ $t('loc.importUnitAdaptDialogTitle') }}</div>
        <div class="lg-margin-top-16 lg-margin-l-r-24 lg-color-text-primary import-unit-adapt-desc">{{ $t('loc.importUnitAdaptDialogDesc') }}</div>
        <div class="button-container">
        
          <div class="button-wrapper">
            <el-button @click="jumpToAnalysisUnit" class="tour-button">
                <span class="button-text">{{ $t('loc.importUnitAdaptDialogBtn') }}</span>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {mapState} from 'vuex';

export default {
  name: 'ImportUnitAdaptDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      open: state => state.common.open
    })
  },
  methods: {
    handleClose () {
      event.stopPropagation()
      this.$emit('update:dialogVisible', false)
      // 调用父类的 closeDialog 方法
      this.$emit('closeDialog')
    },
    jumpToAnalysisUnit () {
      this.$emit('update:dialogVisible', false)
      // 调用父类的 closeDialog 方法
      this.$emit('closeDialog')
      // 清空已有单元信息
      this.$store.commit('curriculum/RESET_UNIT')
      // 点击立即体验按钮
      this.$analytics.sendEvent('cg_adapt_intro_start_clicked')
      this.$router.push({
        name: 'import-unit-cg'
      })
    }
  }
}
</script>

<style lang="less" scoped>
.feature-reminder {
  border-radius: 12px;
  display: flex;
  max-width: 560px;
  flex-direction: column;
  overflow: hidden;
  color: var(--ffffff, #fff);
  text-align: center;
  font: 600 16px Inter, sans-serif;
}
.import-unit-adapt-title {
  font-size: 20px;
  font-weight: 600;
}
.import-unit-adapt-desc {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}
.content-wrapper {
  background-color: rgba(255, 255, 255, 1);
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: start;
}

.button-container {
  background-color: rgba(255, 255, 255, 1);
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0 5px 0;
}

.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tour-button {
  background: var(--logo, linear-gradient(36deg, #10b3b7 15.6%, #aa89f2 87.76%)) !important;
  border-color: transparent !important;
  border: 0 !important;
  min-height: 45px;
  width: 159px !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.tour-button:hover {
  box-shadow: 0 0 0 3px rgba(45, 156, 219, 0.3) !important;
}
.button-text {
  font-feature-settings: "liga" off, "clig" off;
  font-size: 18px !important;
  padding: 0 4px;
  color: var(--ffffff, #fff);
}
/deep/ .el-dialog__header {
  background-image: url('~@/assets/img/curriculumPlugin/import_unit_adapt.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  height: 240px;
  //设置圆角
  border-radius: 8px 8px 0 0;
}
/deep/ .el-dialog__header {
      button {
        font-size: 20px;
      }
    }

</style>
