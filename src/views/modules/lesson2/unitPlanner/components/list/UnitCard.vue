<template>
    <div class="" :id="isCurriculumPlugin && unit && unit.exemplar ? 'exemplar-unit' : ''"
         @click="openUnit" @mouseenter="checkSubmenuPosition($event)">
        <el-card class="lg-pointer unit-card card-box-shadow"
                 :class="(isMC && unit && unit.exemplar) ? 'unit-exemplar' : ''" style="height: 320px !important;">
            <el-skeleton animated :loading="loading">
                <template v-if="unit">
                    <!-- 单元标题 -->
                    <div class="display-flex align-items justify-content-between">
                        <div class="display-flex align-items" style="width: 85%">
                          <img src="~@/assets/img/lesson2/unitPlanner/curriculum_unit.svg" style="width: 24px;height: 24px" alt="">
                          <div class="exemplar-tag flex-center-center add-padding-l-10 add-padding-r-10 text-white font-size-14 font-bold add-margin-l-10" v-if="unit.exemplar && isMC">Exemplar</div>
                          <div class="font-bold overflow-ellipsis color-323338 font-size-16 lg-margin-left-12 display-flex align-items" style="width: calc(100% - 50px);gap: 10px" :title="unit.title">
                            <span class="display-inline-block overflow-ellipsis">{{ unit.title }}</span>
                            <el-tag v-if="unit.adapted" class="adapted-tag font-weight-400" size="mini">{{ $t('loc.adaptUnitPlanner25') }}</el-tag>
                          </div>
                        </div>
                      <el-tooltip class="item" effect="dark" content="Preview" placement="top">
                        <i class="lg-icon lg-icon-eye font-bold add-margin-r-10" v-if="unit.progress >= 80" @click.stop="preview(unit)"></i>
                      </el-tooltip>
                      <!-- 编辑、删除操作下拉选 -->
                      <el-dropdown ref="opearte-menu" trigger="click" v-if="operateAuth(unit)" @visible-change="$emit('initAdaptComponent')" :hide-on-click="false">
                        <span class="el-dropdown-link" @click.stop>
                          <i class="lg-icon lg-icon-more-vertical font-size-20"></i>
                        </span>
                        <!--如果是 magic 则隐藏-->
                        <el-dropdown-menu slot="dropdown" placement="bottom">
                          <el-dropdown-item :style="{ color: (unit.exemplar || unit.isSharedToMagic) ? 'var(--color-border)' : '' }" :disabled="unit.exemplar || unit.isSharedToMagic" v-if="!unit.isSharedToMagic" class="edit-dropdown" @click.native="toEditUnit()">
                            <template #icon>
                              <i class="lg-icon lg-icon-edit"></i>
                            </template>
                            <span class="">{{ $t('loc.edit') }}</span>
                          </el-dropdown-item>
                          <el-dropdown-item class="edit-dropdown" :style="{ color: unit.progress < 80 ? 'var(--color-border)' : '' }" :disabled="unit.progress < 80" @click.native="preview(unit)">
                            <template #icon>
                              <i class="lg-icon lg-icon-eye"></i>
                            </template>
                            <span>{{ $t('loc.preview') }}</span>
                          </el-dropdown-item>
                          <el-dropdown-item :style="{ color: unit.exemplar || unit.isSharedToMagic ? 'var(--color-border)' : '' }" :disabled="unit.exemplar || unit.isSharedToMagic" class="edit-dropdown" @click.native="renameUnit(unit)">
                            <template #icon>
                              <i class="lg-icon lg-icon-rename"></i>
                            </template>
                            <span class="">{{ $t('loc.unitRename') }}</span>
                          </el-dropdown-item>
                          <!-- loc.unitDownlad 子菜单的实现 -->
                          <el-dropdown-item class="dropdown-item-with-submenu"
                          :style="{ color: unit.progress < 100 ? 'var(--color-border)' : '' }"
                          :disabled="(!unitUsageModel.downloadWord && !unitUsageModel.downloadPdf && !unitUsageModel.exportToGoogleDrive && !unit.exemplar) && unit.progress === 100"
                          @click.native="unit.progress < 100 ? clickDownload() : null">
                            <template #icon>
                              <i class="lg-icon lg-icon-download"></i>
                              <span>{{ $t('loc.download') }}</span>
                            </template>

                            <!-- 自定义二级菜单 -->
                            <div class="submenu" :class="{ 'submenu-left': isSubmenuLeft }" v-if="unit.progress === 100">
                              <span class="dropdown-menu-tip" style="padding: 4px 8px 0;display: block" v-if="showDownloadResourcesTip">{{ $t('loc.fullDownloadResources') }}</span>
                              <div class="submenu-item display-flex align-items"  @click="downloadPDF('pdf')">
                                <img src="~@/assets/img/file/pdf.svg" alt="" class="item-icon-img">
                                <span class="download-font-style" >{{ $t('loc.lessons2DownloadPDF') }}</span>
                              </div>
                              <div class="submenu-item display-flex align-items"  @click="downloadPDF('docx')">
                                <img src="~@/assets/img/file/word.svg" alt="" class="item-icon-img">
                                <span class="download-font-style" >{{ $t('loc.lessons2DownloadWord') }}</span>
                              </div>
                              <div class="submenu-item display-flex align-items"  @click="downloadGoogleDocs()">
                                <img src="~@/assets/img/file/google_drive.svg" class="item-icon-img">
                                <span class="download-font-style" >{{ $t('loc.lessons2SaveDrive') }}</span>
                              </div>
                            </div>
                          </el-dropdown-item>
                          <!-- 下面是另一种下载按钮实现方式（已注释） -->
                          <!--
                          <el-dropdown
                            :disabled="unit.progress !== 100 || (!operateAuth(unit) && showApplyFeature)"
                            trigger="click">
                            <el-button @click.stop size="medium" type="primary" plain icon="el-icon-download"
                                     :disabled="unit.progress !== 100 || (!operateAuth(unit) && showApplyFeature)"
                                     class="el-dropdown-link add-margin-l-10">
                              Download
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item @click.native="downloadPDF('pdf')">
                                <i class="lg-icon lg-icon-pdf2" style="size: 24px;"></i>
                                <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2DownloadPDF') }}</span>
                              </el-dropdown-item>
                              <el-dropdown-item @click.native="downloadPDF('docx')">
                                <i class="lg-icon lg-icon-word1" style="size: 24px;"></i>
                                <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2DownloadWord') }}</span>
                              </el-dropdown-item>
                              <el-divider></el-divider>
                              <el-dropdown-item @click.native="downloadGoogleDocs()">
                                <img class="drive-icon-size" style=" width: 16px; height: 16px;"
                                     src="@/assets/img/lesson2/assistant/google-drive-icon.png">
                                <span class="download-font-style add-margin-l-8">{{ $t('loc.lessons2SaveDrive') }}</span>
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                          -->
                          <!--                          开启了 Adapt UDL 和 CLR 的权限，同时不是 CG 或者是插件平台-->
                          <el-dropdown-item v-if="isOpenAdaptUDLAndCLR && (!isCG || isCurriculumPlugin)" :style="{ color: unit.progress !== 100 ? 'var(--color-border)' : '' }" :disabled="unit.progress !== 100" class="edit-dropdown" :divided="isOpenAdaptUDLAndCLR" @click.native="adaptUnit(unit)">
                            <template #icon>
                              <i class="lg-icon lg-icon-generate"></i>
                            </template>
                            <span class="">{{$t('loc.unitPlannerAdapted')}}</span>
                          </el-dropdown-item>
                          <el-dropdown-item class="dropdown-item-with-submenu" v-if="unit.isSharedToMagic">
                            <template #icon>
                              <i class="lg-icon lg-icon-share1"></i>
                            </template>
                            <span>{{ $t('loc.Share') }}</span>

                            <!-- 自定义二级菜单 -->
                            <div class="submenu" :class="{ 'submenu-left': isSubmenuLeft }">
                              <div class="submenu-item"  @click="shareLink('Facebook', unit)"> <img src="@/assets/img/magicCurriculum/card-facebook.png" alt="" srcset="">
                                <span>Facebook</span>
                              </div>
                              <div class="submenu-item"  @click="shareLink('Twitter', unit)"> <img src="@/assets/img/magicCurriculum/card-X.png" alt="" srcset="">
                                <span>Twitter</span>
                              </div>
                              <div class="submenu-item"  @click="shareLink('CopyLink', unit)">         <img src="@/assets/img/magicCurriculum/card-link.png" alt="" srcset="">
                                <span>Copy Link</span>
                              </div>
                            </div>
                          </el-dropdown-item>

                          <el-divider v-if="!isOpenAdaptUDLAndCLR && !isCG && !isCurriculumPlugin && !unit.isSharedToMagic"></el-divider>
                          <el-dropdown-item v-if="!isCG && !isCurriculumPlugin" :style="{ color: unit.progress !== 100 ? 'var(--color-border)' : '' }" style="display: flex;align-items: center;" :disabled="unit.progress !== 100" class="edit-dropdown" @click.native="applyUnit(unit)">
                            <template #icon>
                              <i class="el-icon-document"></i>
                            </template>
                            <span class="" style="width: 100px;line-height: 20px;padding: 10px 0">{{$t('loc.unitPlannerToWeekLyPlan')}}</span>
                          </el-dropdown-item>
                          <el-dropdown-item v-if="!unit.isSharedToMagic" :style="{ color: unit.exemplar || unit.isSharedToMagic ? 'var(--color-border)' : '' }" :disabled="unit.exemplar || unit.isSharedToMagic" class="delete-dropdown" divided @click.native="removeUnit(unit)">
                            <template #icon>
                              <i class="lg-icon lg-icon-delete"></i>
                            </template>
                            <span class="">{{ $t('loc.delete') }}</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                      <el-dropdown trigger="click" v-if="operateTeacherAuth(unit)">
                        <span class="el-dropdown-link" @click.stop>
                          <i class="lg-icon lg-icon-more-vertical font-size-20"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown" placement="bottom">
                          <el-dropdown-item v-if="isOpenAdaptUDLAndCLR && !isCG" :style="{ color: unit.progress !== 100 ? 'var(--color-border)' : '' }" :disabled="unit.progress !== 100" class="edit-dropdown" @click.native="adaptUnit(unit)">
                            <template #icon>
                              <i class="lg-icon lg-icon-generate"></i>
                            </template>
                            <span class="">{{$t('loc.unitPlannerAdapted')}}</span>
                          </el-dropdown-item>
                          <el-divider v-if="!isOpenAdaptUDLAndCLR && !isCG && !isCurriculumPlugin"></el-divider>
                          <el-dropdown-item v-if="!isCG && !isCurriculumPlugin" :style="{ color: unit.progress !== 100 ? 'var(--color-border)' : '' }" style="display: flex;align-items: center;" :disabled="unit.progress !== 100" class="edit-dropdown" @click.native="applyUnit(unit)">
                            <template #icon>
                              <i class="el-icon-document"></i>
                            </template>
                            <span class="" style="width: 100px;line-height: 20px;padding: 10px 0">{{$t('loc.unitPlannerToWeekLyPlan')}}</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </div>
                    <!-- 周数、活动数 -->
                    <div class="bg-color-F5F6F8 m-t-sm border-radius-4">
                        <el-row class="add-padding-tb-6">
                            <!-- 周数 -->
                            <el-col :span="unit.adapted ? 8 : 12" class="display-flex flex-direction-col align-items b-r">
                                <div class="font-bold font-size-16">{{ unit.weekCount }}</div>
                                <div>{{$t('loc.unitPlannerWeeklyWeeks')}}</div>
                            </el-col>
                            <!-- 活动数 -->
                            <el-col :span="unit.adapted ? 8 : 12" class="display-flex flex-direction-col align-items" :class="unit.adapted ? 'b-r' : ''">
                                <div class="font-bold font-size-16">{{ unit.activityCount }}</div>
                                <div>{{$t('loc.lessons')}}</div>
                            </el-col>
                            <!-- 改编数 -->
                            <el-col v-if="unit.adapted" :span="8" class="display-flex flex-direction-col align-items">
                              <div class="font-bold font-size-16">{{ unit.adaptedActivityCount || 0 }}</div>
                              <div style="white-space: nowrap;">{{ $t('loc.unitAdaptedActivities') }}</div>
                            </el-col>
                        </el-row>
                    </div>
                    <!-- 单元概览 -->
                    <div class="m-t-sm unit-overview">
                        <div class="overflow-ellipsis-three color-676879" :title="getHtmlInnerText(unit.overview)">
                          {{ getHtmlInnerText(unit.overview) }}
                        </div>
                    </div>
                    <div class="display-flex gap-12">
                       <!-- 年龄段 -->
                      <div class="unit-grade-box grade-tag" :title="$t('loc.unitPlannerWeeklyAgeGroup') + ': ' + unit.grade">
                        <span class="font-bold">{{ unit.grade }}</span>
                      </div>
                      <!-- 框架 -->
                      <div class="unit-framework-box framework-tag" :title="unit.frameworkAbbreviation">
                        <span class="font-bold">{{ unit.frameworkAbbreviation }}</span>
                      </div>
                    </div>
                    <!-- 改编班级 -->
                    <div class="display-flex overflow-ellipsis m-t-sm" v-if="unit.adapted && !isCurriculumPlugin">
                        <div class="adapted-class overflow-ellipsis">
                          <span class="overflow-ellipsis" :title="unit.adaptGroupName">{{$t('loc.unitAdaptedGroup')}}{{ unit.adaptGroupName }}</span>
                        </div>
                      </div>
                    <!-- 更新时间 -->
                    <div class="display-flex align-items justify-content-between"
                         :style="{marginTop: unit.adapted && isCurriculumPlugin ? '20px' : ''}"
                         :class="unit.adapted ? 'm-t-sm' : 'm-t-md'">
                      <div class="display-flex align-items">
                        <img v-if="!(isCurriculumPlugin && !unit.exemplar)" :src="unit.createUserAvatar" alt="" class="border-radius-half" style="width: 24px;height: 24px;object-fit: cover">
                        <img v-else :src="currentPluginUser.avatar_url || unit.createUserAvatar" alt="" class="border-radius-half" style="width: 24px;height: 24px;object-fit: cover">
                        <span v-if="!(isCurriculumPlugin && !unit.exemplar)"
                              class="lg-margin-left-8 font-size-14 text-default user-name"
                              :title="unit.createUserName">{{ unit.createUserName }}
                        </span>
                        <span v-if="isCurriculumPlugin && !unit.exemplar" class="lg-margin-left-8 font-size-14 text-default user-name"
                              :title="currentPluginUser.user_name || currentUser.email">
                          {{ currentPluginUser.user_name || currentUser.email }}
                        </span>
                      </div>
                      <span class="text-muted color-676879 update-date" :title="formatEditDate(unit)">{{ formatEditDate(unit) }}</span>
                    </div>
                  <div class="display-flex add-margin-t-10 justify-content"
                       v-if="!isCurriculumPlugin && isMC && unit.progress === 100">
                    <el-button type="primary"
                      v-if="isMC && unit.exemplar"
                      class="ai-btn ai-btn-exemplar">
                        Exemplar Unit
                    </el-button>
                    <el-button type="primary"
                      v-else
                      :class="{
                        'ai-btn-active': (!unit.exemplar && !unit.exemplar && unit.isSharedToMagic),
                      }"
                      :icon="(!unit.exemplar && !unit.exemplar && unit.isSharedToMagic) ? 'el-icon-success' : 'el-icon-upload'"
                      class="ai-btn"
                      :style="isDisabled ? 'opacity: 0.5' : ''"
                      @click.native.stop="publishMagic(unit)">
                        {{ !unit.exemplar && unit.isSharedToMagic ? 'Submitted' : 'Submit Your Entry' }}
                    </el-button>
                    <!-- Share -->
                    <el-popover
                      placement="bottom"
                      width="260"
                      popper-class="entried-card-popover"
                      trigger="click"
                      v-if="!unit.exemplar && unit.isSharedToMagic && unit.progress === 100"
                    >
                        <SharePopover :unit="unit"></SharePopover>
                        <div class="share-button" slot="reference" v-if="!unit.exemplar && unit.isSharedToMagic && unit.progress === 100"
                        @click.stop="toggleSharePopover(unit)">
                        </div>
                    </el-popover>
                  </div>
                  <!--如果是 curriculum plugin 平台-->
                  <!-- :style="{marginTop: unit.adapted && isCurriculumPlugin ? '34px' : ''}" -->
                  <div class="display-flex add-margin-t-10 justify-content"
                       v-else-if="isCurriculumPlugin">
                    <!-- 范例课程 -->
                    <div v-if="unit.exemplar" class="w-full flex-center-center">
                      <!-- 范例课程100%进度 -->
                      <el-dropdown
                        v-if="unit.progress === 100"
                        class="justify-content"
                        trigger="click">
                        <el-button @click.stop size="medium" type="primary" plain icon="el-icon-download"
                                   class="el-dropdown-link">
                          {{ $t('loc.lessons2Download') }}
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                          <span class="dropdown-menu-tip add-margin-l-3">{{ $t('loc.fullDownloadResources') }}</span>
                          <!--PDF 下载按钮-->
                          <el-dropdown-item @click.native="downloadPDF('pdf')">
                            <img src="~@/assets/img/file/pdf.svg" alt="" class="item-icon-img">
                            <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2DownloadPDF') }}</span>
                          </el-dropdown-item>
                          <!--Word 下载按钮-->
                          <el-dropdown-item @click.native="downloadPDF('docx')">
                            <img src="~@/assets/img/file/word.svg" alt="" class="item-icon-img">
                            <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2DownloadWord') }}</span>
                          </el-dropdown-item>
                          <!--谷歌文档下载按钮-->
                          <el-dropdown-item @click.native="downloadGoogleDocs()">
                            <img src="~@/assets/img/file/google_drive.svg" class="item-icon-img">
                            <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2SaveDrive') }}</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                      
                      <!-- 范例课程进度不为100% -->
                      <el-button
                        v-else
                        @click.stop="clickDownload"
                        size="medium"
                        type="primary"
                        plain
                        icon="el-icon-download"
                        class="el-dropdown-link button-disabled">
                        {{ $t('loc.lessons2Download') }}
                      </el-button>
                    </div>
                    
                    <!-- 非范例课程 -->
                    <div class="text-center gap-10 w-full" v-else>
                      <!--编辑按钮-->
                      <el-button size="medium" type="primary" plain icon="el-icon-edit"
                                 :disabled="unit.exemplar"
                                 @click.stop="toEditUnit()">{{ $t('loc.edit') }}
                      </el-button>
                      
                      <!--下载按钮弹框-->
                      <el-dropdown
                        v-if="unit.progress === 100"
                        trigger="click">
                        <el-button @click.stop size="medium" type="primary" plain icon="el-icon-download"
                                   class="el-dropdown-link add-margin-l-10">
                          {{ $t('loc.lessons2Download') }}
                        </el-button>
                        <el-dropdown-menu slot="dropdown" class="card-download-dropdown-menu">
                          <span class="dropdown-menu-tip" v-if="showDownloadResourcesTip">{{ $t('loc.fullDownloadResources') }}</span>
                          <!--PDF 下载按钮-->
                          <el-dropdown-item @click.native="downloadPDF('pdf')">
                            <img src="~@/assets/img/file/pdf.svg" alt="" class="item-icon-img">
                            <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2DownloadPDF') }}</span>
                          </el-dropdown-item>
                          <!--Word 下载按钮-->
                          <el-dropdown-item @click.native="downloadPDF('docx')">
                            <img src="~@/assets/img/file/word.svg" alt="" class="item-icon-img">
                            <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2DownloadWord') }}</span>
                          </el-dropdown-item>
                          <!--谷歌文档下载按钮-->
                          <el-dropdown-item @click.native="downloadGoogleDocs()">
                            <img src="~@/assets/img/file/google_drive.svg" class="item-icon-img">
                            <span class="download-font-style add-margin-l-3">{{ $t('loc.lessons2SaveDrive') }}</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                      
                      <!-- 进度不为100%时显示普通按钮 -->
                      <el-button
                        v-if="unit.progress !== 100"
                        @click.stop="clickDownload"
                        size="medium"
                        type="primary"
                        plain
                        icon="el-icon-download"
                        class="el-dropdown-link button-disabled add-margin-l-10">
                        {{ $t('loc.lessons2Download') }}
                      </el-button>
                    </div>
                  </div>
                </template>
                <!-- Loading 骨架屏 -->
                <template slot="template">
                    <div>
                        <!-- 标题 -->
                        <el-skeleton-item variant="p" class="w-70-percent" />
                        <!-- 周数、活动数 -->
                        <el-row :gutter="10" class="m-t-sm add-padding-tb-6">
                            <el-col :span="12" class="display-flex flex-direction-col align-items">
                                <el-skeleton-item variant="rect" class="h-40" />
                            </el-col>
                            <el-col :span="12" class="display-flex flex-direction-col align-items">
                                <el-skeleton-item variant="rect" class="h-40" />
                            </el-col>
                        </el-row>
                        <!-- 概览 -->
                        <el-skeleton-item variant="p" class="m-t-sm h-60" />
                        <!-- 年龄段 -->
                        <el-skeleton-item variant="p" class="m-t-sm w-70-percent h-20" />
                        <!-- 更新时间 -->
                        <el-skeleton-item variant="p" class="m-t-md w-70-percent h-20" />
                    </div>
                </template>
            </el-skeleton>
        </el-card>

        <!-- 下载提示对话框 -->
         <el-dialog
          title="Download Unavailable"
          :visible.sync="downloadDialogVisible"
          width="600px"
          :close-on-click-modal="false"
          :close-on-press-escape="true"
          custom-class="download-unavailable-dialog"
          @click.native.stop
          @close.stop>
          <div class="dialog-content" @click.stop>
            <div class="dialog-message">
              {{ downloadDialogMessage }}
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click.stop="closeDownloadDialog">Later</el-button>
            <el-button type="primary" class="ai-btnTemp" @click.stop="handleDownloadAction">{{ downloadDialogButton }}</el-button>
          </span>
        </el-dialog>
    </div>
</template>

<script>
// eslint-disable-next-line import/no-duplicates
import {isTeacher, equalsIgnoreCase, isAuthenticated} from '@/utils/common'
import Lessons2 from '@/api/lessons2'
import { mapState } from 'vuex'
// eslint-disable-next-line import/no-duplicates
import { shareLink } from '@/utils/common'
import SharePopover from '@/views/magicCurriculum/components/btnGroup/SharePopover.vue'
import {platform} from "@/utils/setBaseUrl";
import UnitDownload from '@/views/modules/lesson2/lessonLibrary/components/UnitDownload.vue'
import tools from '@/utils/tools'

export default {
    emits: ['initAdaptComponent'],
    components: {
      SharePopover,
      UnitDownload
    },
    props: {
      // 单元信息
      unit: {
          type: Object,
          default: () => {}
      },
      // 是否显示加载中
      loading: {
          type: Boolean,
          default: false
      }
    },
    data () {
        return {
          unitModal: {
            unitName: ''
          }, // 单元名称
          showDownloadDialog: false,
          downloadType: 'Download Unit',
          percentage: 10,
          maxRetryCount: 0,
          downloadUrl: '',
          currentBatchId: [], // 当前批次 ID
          timeoutMap: new Map(),
          unitIdMap: new Map(),
          notify: null,
          isSubmenuLeft: false,
          maxCallCount: 0,
          // 下载提示对话框
          downloadDialogVisible: false,
          downloadDialogMessage: '',
          downloadDialogButton: ''
        }
    },
    computed: {
      ...mapState({
        isCG: state => state.curriculum.isCG, // 是否是 Curriculum Genie 平台
        isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
        isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
        currentUser: state => state.user.currentUser,
        open: state => state.common.open,
        unitUsageModel: (state) => state.magicCurriculum.unitUsageModel, // unit 使用的功能模块
        allowPublicMagic: state => state.magicCurriculum.allowPublicMagic,
        publishedIds: state => state.magicCurriculum.publishedIds,
        currentPluginUser: state => state.cgAuth.user // 当前插件用户
      }),
      // 提交按钮禁用样式
      isDisabled (){
        // 示例模板禁止点击
        if (this.unit.exemplar) {
          return true
        }
        // unit 是否已发布 magic
        if (this.unit.isSharedToMagic) {
          return true
        }
        // 当前用户是否允许发布 magic
        return !this.allowPublicMagic
      },
      // 显示应用功能
      showApplyFeature () {
        return this.unit.progress === 100
      },
      // 是否是 magic unit
      isMagicUnit () {
        return this.isMC && this.unit && this.unit.isSharedToMagic == '1'
      },
      // 处理单元的更新时间格式
      formatEditDate () {
          return function (unit) {
              if (!unit || !unit.updateAtUtc) {
                  return ''
              }
              let date = this.$moment.utc(unit.updateAtUtc).local()
              return this.$t('loc.unitPlannerWeeklyUpdatedOn') + ' ' + date.format('MM/DD/YYYY')
          }
      },
      // 改编开关
      isOpenAdaptUDLAndCLR () {
        return this.open && this.open.adaptUDLAndCLROpen
      },
      // 是否显示下载资源提示
      showDownloadResourcesTip() {
        // TK 及以上才显示
        if (this.unit && this.unit.grade && tools.getAgeValue(this.unit.grade) >= 5) {
          return true
        }
        return false
      },
    },
    async created () {

    },
    methods: {
      isTeacher,
      equalsIgnoreCase,
      shareLink,
      magicExemplarDetail(unit) {

      },
      toggleSharePopover(unit) {
        if (unit.sharePopoverVisible) {
          unit.sharePopoverVisible = false
        } else {
          unit.sharePopoverVisible = true
        }
      },
      // 将当期 unit 发布到 magic
      publishMagic (unit){
        // 样例模板禁止点击
        if (unit.exemplar) {
          this.$message.warning('This exemplar cannot be submitted.')
          return
        }
        // 已发布模板禁止发布
        if (unit.isSharedToMagic) {
          this.$message.warning(this.$t('loc.repeatEntryTip'))
          return
        }
        // 当前用户是否可以发布
        if (!this.allowPublicMagic) {
          this.$message.warning(this.$t('loc.limitOneMagicPublish'))
          return
        }

        this.$confirm(`<p>Are you sure you’re ready to submit?</p>
        <p>Once you click ‘Confirm,’ your submission is final, and no changes can be made.</p>
        <p>You only get one chance, so make it count!</p>`,'Confirmation',{
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        dangerouslyUseHTMLString: true
      })
        .then(async() => {
            await this.$axios.post($api.urls().shardUnitToMagic, {
              unitId: unit.id,
              sharedToMagic: true
            })
            let param = {
              'unitId': unit.id,
              'planId': [],
              'fileType': 'docx',
              'shardedToMagic': true,
              'project': platform
            }
            this.$axios.post($api.urls().createBatchGenerateTask, param)
            unit.isSharedToMagic = true
            this.$emit('publishSuccess',unit)
        })
        .catch((error) => {
          if (error.response.data.error_code === 'param_error') {
            this.$message.error(this.$t('loc.repeatEntryTip'))
          }else{
            this.$message.error(error.response.data.error_message)
          }
        })
      },
      // 下载前检测
      checkLogin() {
        // 如果登录则继续执行
        if (isAuthenticated()) {
          return true
        } else {
          // 如果未登录则弹出登录框
          this.$bus.$emit('checkMagicLogin', 'Sign in')
          return false
        }
      },
      // 关闭下载对话框
      closeDownloadDialog() {
        this.downloadDialogVisible = false
      },
      clickDownload() {
        this.$analytics.sendEvent('cg_unit_list_download')
        
        // 如果未登录则返回
        if (!this.checkLogin()) {
          return
        }
        
        // 如果进度为100%，则下拉菜单会自动打开，不需要执行下面的代码
        if (this.unit.progress === 100) {
          return
        }
        this.downloadDialogMessage = this.$t('loc.downloadDialogMessage')
        
        this.downloadDialogButton = this.$t('loc.generateNow')
        
        //  打开不可下载弹窗埋点
        this.$analytics.sendEvent('cg_unit_popup_nodownload_show')
        // 显示对话框
        this.downloadDialogVisible = true
      },
      
      // 处理下载对话框确认按钮点击
      async handleDownloadAction() {
        // 关闭对话框
        this.downloadDialogVisible = false
        // 点击继续生成按钮埋点
        this.$analytics.sendEvent('cg_unit_popup_nodownload_continue_click')
        await this.toEditUnit(true)
      },
      downloadPDF (fileType) {
        // 如果未登录则返回
        if (!this.checkLogin()) {
          return
        }
        if (fileType === 'docx') {
          this.$analytics.sendEvent('cg_unit_list_download_word')
        } else {
          this.$analytics.sendEvent('cg_unit_list_download_pdf')
        }
        // 关闭下拉菜单
        this.$refs['opearte-menu'].hide()
        this.$emit('downloadPDF', fileType, this.unit)
      },
      async openUnit () {
        // 判断当前 Unit 是否已经完成，如果完成则跳转到 Unit 详情页
        if (this.unit && this.unit.progress === 100) {
          // 进入单元详情曝光埋点
          this.$analytics.sendEvent('web_unit_detail_exposure')
          // 虽然是直接打开的详情页，但是需要判断是否是范例单元
          this.$router.push({
            name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail',
            query: {
              unitId: this.unit.id
            }
          })
          // 查看单元事件
          this.$analytics.sendActivityEvent('UNIT_PLANNER', 'VIEW_UNIT')
          return
        }
        await this.toEditUnit()
        // 查看单元事件
        this.$analytics.sendActivityEvent('UNIT_PLANNER', 'VIEW_UNIT')
      },
      // 编辑 Unit Planner
      async toEditUnit (generateFlag = false) {
        var caneEdit = await this.checkEditStatus()
        if (!caneEdit) {
          return
        }
        // 清空已有单元信息
        this.$store.commit('curriculum/RESET_UNIT')
        // 跳转到单元详情页
        var cgRouteName = 'load-unit-cg'
        if (this.unit.adaptedType) {  
          cgRouteName = 'load-unit-cg-adapt'
        }
        this.$router.push({
          name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'loadUnit',
          params: {
            unitId: this.unit.id,
            generateFlag: generateFlag
          }
        })
      },
      // 删除 Unit Planner
      async removeUnit (unit) {
        var canRemove = await this.checkEditStatus()
        if (!canRemove) {
          return
        }
        // 提示框
        this.$confirm('Are you sure you want to delete the <span style="font-weight: bold">' + unit.title + '</span> unit?', 'Confirmation', {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          dangerouslyUseHTMLString: true,
          customClass: 'lg-message-box',
          cancelButtonClass: 'is-plain',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
          const params = {
            unitId: unit.id
          }
          // 调用接口，进行单元的删除操作
          Lessons2.deleteUnit(params).then(res => {
            if (res.success && res.id) {
              // 删除成功之后，刷新页面
              this.$emit('refreshUnits', true)
              // 删除之后 刷新接口
              this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
              this.$message({
                type: 'success',
                message: 'Unit was deleted successfully.'
              })
            }
          }).catch(err => {
            console.log(err)
            this.$message.error(err.message)
          })
        })
      },
      // 预览
      preview (unit) {
        // 获取当前 Unit 的进度
        const progress = unit.progress
        // 只有 Unit 进度大于 80% 时才可以预览
        if (progress >= 80) {
          this.$analytics.sendEvent('cg_unit_list_preview')
          this.$router.push({
            name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail',
            query: {
              unitId: this.unit.id
            }
          })
        }
      },
      renameUnit () {
        this.$prompt('', this.$t('loc.unitRename'), {
          confirmButtonText: this.$t('loc.unitRenameSave'),
          cancelButtonText: this.$t('loc.cancel'),
          closeOnClickModal: false,
          inputPlaceholder: this.$t('loc.unitRenameInput'),
          inputValue: this.unit.title,
          customClass: 'unit-card-rename-confirm',
          inputValidator: (value) => {
            if (!value.trim()) {
              return this.$t('loc.unitRenameInputWarn')
            }
            if (value.length > 200) {
              return 'The Unit name cannot exceed 200 characters!'
            }
          }
        }).then(({ value }) => {
          const params = {
            unitId: this.unit.id,
            name: value
          }
          this.$axios.post($api.urls().renameUnit, params).then((res) => {
            this.$emit('refreshUnits', true)
            this.$message.success(this.$t('loc.unitRenameSuccess'))
          }, error => {
            this.$message.error(error.response.data.error_message)
          })
        }).catch(() => {
        })
      },
      adaptUnit (unit) {
        this.$emit('adaptSingleUnit', unit)
      },
      applyUnit (unit) {
        this.$emit('applySingleUnit', unit)
      },
      // 获取进度值所代表的枚举值
      getProgress (progress) {
        switch (progress) {
          case 20:
            return 'INITIALIZED'
          case 40:
            return 'WEEK_OVERVIEW_GENERATED'
          case 60:
            return 'WEEK_OVERVIEW_CONFIRMED'
          case 80:
            return 'LESSON_OVERVIEW_GENERATED'
          default:
            return 'COMPLETED'
        }
      },
      // 检查单元的编辑状态
      async checkEditStatus () {
        var res = await Lessons2.getUnitEditStatus(this.unit.id)
        if (res) {
          let userName = res.userName
          this.$message.error(userName + this.$t('loc.unitPlannerIsEditing'))
          return false
        } else {
          return true
        }
      },
      // 获取 HTML 标签中的内容
      getHtmlInnerText (overview) {
        // 创建一个临时的 DOM 元素
        var tempElement = document.createElement('div')
        tempElement.innerHTML = overview
        // 获取纯文本内容
        return tempElement.textContent || tempElement.innerText
      },
      operateAuth (unit) {
        // 如果当前角色是老师，则需要判断当前的 Unit 是不是本人创建的
        // 同时判断当前老师是否具有创建权限
        if (this.isTeacher()) {
          return this.currentUser.user_id.toLowerCase() === unit.createUserId.toLowerCase() && this.open && this.open.createUnitPlannerOpen
        } else {
          return true
        }
      },
      // 老师功能操作入口
      operateTeacherAuth (unit) {
        // 如果是老师角色
        if (this.isTeacher()) {
          return !this.equalsIgnoreCase(this.currentUser.user_id, unit.createUserId)
        } else {
          return false
        }
      },

    /**
     * 检查二级菜单的位置
     * @param event 事件对象
     */
    checkSubmenuPosition (event) {
      const cardRect = event.target.getBoundingClientRect()
      this.isSubmenuLeft = window.innerWidth - cardRect.right < 200
    },
    // 下载谷歌docs文档
    downloadGoogleDocs () {
      // 关闭下拉菜单
      this.$refs['opearte-menu'].hide()
      this.$analytics.sendEvent('cg_unit_list_download_export')
      this.$emit('downloadGoogleDocs', this.unit)
    }
  }
}
</script>


<style lang="less" scoped>
/deep/ .el-dialog__title {
  font-size: 20px !important;
}
/deep/ .el-dialog__header {
  padding: 24px 24px 24px 24px !important;
}
/deep/ .el-dialog__body {
  padding: 0px 24px 24px 24px !important;
}

/deep/ .el-dialog__footer {
  padding: 0px 24px 24px 24px !important;
}

</style>


<style lang="less" scoped>
::v-deep{
  .el-dropdown-menu__item{
    color: #111c1c;
  }
}
.unit-exemplar {
  background: linear-gradient(180deg, #DCF1FC -0.01%, #FFF 23.84%);
  .exemplar-tag {
    height: 24px;
    border-radius: 90px;
    background: #C2A6FD;
  }

}
::v-deep .ai-btn{
  display: flex;
  align-items: center;
  i{
    font-size: 22px;
  }
  &-active {
    border-radius: 4px;
    background: linear-gradient(271deg, rgba(45, 156, 219, 0.10) 0.32%, rgba(135, 139, 249, 0.10) 67.59%, rgba(187, 107, 217, 0.10) 142.72%) !important;
    color: #6E8FF0 !important;
    i{
      color: #40c2c5;
    }
  }
  &-exemplar {
    border-radius: 4px;
    width: 214px;
    background: linear-gradient(271deg, rgba(45, 156, 219, 0.10) 0.32%, rgba(135, 139, 249, 0.10) 67.59%, rgba(187, 107, 217, 0.10) 142.72%) !important;
    color: #6E8FF0;
    justify-content: center;
    .el-icon-upload {
      display: none;
    }
    span {
      margin-left: 0;
    }
  }
}
::v-deep{
  .el-dropdown-menu__item{
    color: #111c1c;
  }
}

::v-deep .ai-btn{
  display: flex;
  align-items: center;
  i{
    font-size: 22px;
  }
  &-active{
    border-radius: 4px;
    background: linear-gradient(271deg, rgba(45, 156, 219, 0.10) 0.32%, rgba(135, 139, 249, 0.10) 67.59%, rgba(187, 107, 217, 0.10) 142.72%) !important;
    color: #6E8FF0 !important;
    i{
      color: #40c2c5;
    }
  }
}
/deep/ .el-progress__text{
  color: var(--color-primary);
  font-weight: 600;
}
/deep/.el-dropdown-link:hover {
  color: var(--color-primary);
}
.edit-dropdown {
  color: var(--color-text-primary);
}
.edit-dropdown:hover {
  color: #40c2c5;
}
.delete-dropdown {
  color: #0B0B0B;
}
.delete-dropdown:hover {
  color: red;
}
.unit-grade-box {
  white-space: nowrap;
  padding: 10px;
}
.unit-framework-box {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 10px;
  min-width: 0;
  max-width: fit-content;
}
.grade-tag {
  background: #DDF2F3;
  border-radius: 27px;
  padding: 4px 10px;
}
.framework-tag {
  background: #DDF2F3;
  border-radius: 27px;
  padding: 4px 10px;
}
.adapted-class {
  background: var(--color-ai-assistant-opacity);
  border-radius: 27px;
  padding: 4px 10px;
}
.unit-overview {
  min-height: 45px;
}
.w-70-percent {
  width: 70%;
}
.h-20 {
  height: 20px;
}
.h-40 {
  height: 40px;
}
.h-60 {
  height: 60px;
}
::v-deep .unit-card {
  border-radius: 8px;
}
.draft-tag {
  background: rgba(245, 108, 108, 0.2);
  border-radius: 27px;
  padding: 2px 12px;
  color: #CC393E;
  height: 20px;
}
.overflow-ellipsis-three {
  -webkit-line-clamp: 2!important;
}
.user-name {
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}
.card-box-shadow:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.3) !important;
}
.update-date {
  max-width: 165px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-left: 10px;
}
/deep/ .el-dropdown-menu {
  width: 150px!important;
}
.adapted-tag {
  color: var(--color-white) !important;
  background: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
}
/deep/ .el-divider--horizontal {
  margin: 0px!important;
}
.card-share{
  img{
    width: 24px;
    height: 24px;
    margin-right: 16px;
  }
}
/* 确保下拉菜单在禁用状态下按钮仍可点击 */
::v-deep .el-dropdown.is-disabled .el-button {
  pointer-events: auto !important;
}

/* 确保下拉菜单禁用时不打开 */
::v-deep .el-dropdown.is-disabled .el-dropdown-menu {
  display: none !important;
}

.item-icon-img {
  height: 24px !important;
  width: 19px !important;
  margin-right: 7px;
}

.dropdown-menu-tip {
  padding-left: 16px;
  color: var(--676879, #676879);
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

.card-download-dropdown-menu{
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
  }
}
</style>
<style lang="less">
// 重命名确认框样式
.unit-card-rename-confirm {
  & > .el-message-box__content {
    padding: 9px 24px 4px 24px;
  }
}
/* 一级菜单项带有子菜单 */
.dropdown-item-with-submenu {
  position: relative;
}
/* 二级菜单样式 */
.submenu {
  position: absolute;
  top: 0;
  left: 100%;
  display: none;
  border-radius: 8px;
  background: var(--ffffff, #FFF);
  /* 卡片投影 (选中) */
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.20);
  z-index: 1000;
  white-space: nowrap;
  padding: 8px;
}

/* 悬停时显示二级菜单 */
.dropdown-item-with-submenu:hover .submenu {
  display: block;
}

/* 二级菜单项样式 */
.submenu-item {
  padding: 8px 8px;
  cursor: pointer;
  white-space: nowrap;
  color: #111C1C;
  img{
    width: 24px;
    height: 24px;
    margin-right: 12px;
  }
}

.submenu-item:hover {
  background-color: #e7f7f8;
  color: #10B3B7;
}
.share-button{
  width: 102px;
  height: 40px;
  background: url(~@/assets/img/magicCurriculum/share.png) no-repeat center center;
  background-size: contain;
  margin-left: 10px;
}
.notification_error {
  top: 80px !important;
  z-index: 2000;
  width: initial;
  align-items: center;
  background-color: #FEF0F0;
}

.submenu-left {
  right: 100%;
  left: auto;
}

.download-font-style {
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
}

.button-disabled {
  opacity: 0.65;
  background-color: #F5F7FA !important;
  border-color: #DCDFE6 !important;
  color: #C0C4CC !important;
}

/* 确保下拉菜单在点击时仍然可以展开 */
::v-deep .el-dropdown {
  pointer-events: auto !important;
}
</style>

<style lang="less">

.download-unavailable-dialog /deep/ .el-dialog__header {
  text-align: left !important;
}

.download-unavailable-dialog /deep/ .el-dialog--center .el-dialog__footer {
  text-align: right !important;
}


/* 下载弹框样式 */
.download-unavailable-dialog /deep/ .el-message-box {
  width: 550px;
}

.download-unavailable-dialog /deep/ .el-message-box__title {
  font-weight: bold;
  font-size: 18px;
}

.download-unavailable-dialog /deep/ .el-message-box__message {
  font-size: 14px;
  line-height: 1.5;
  margin: 10px 0;
}

.download-unavailable-dialog /deep/ .el-button--primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

/* 对话框内容样式 */
.dialog-content {
  display: flex;
  align-items: flex-start;
}

.dialog-icon {
  margin-right: 15px;
  color: #E6A23C;
  font-size: 24px;
}

.dialog-message {
  flex: 1;
  font-size: 16px;
  line-height: 1.6;
}

.flex-center-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-btnTemp{
  background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
    border-color: transparent !important;
    border: 0 !important;
    color: #FFFFFF;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

/* 确保下拉菜单禁用时不打开 */
::v-deep .el-dropdown.is-disabled .el-dropdown-menu {
  display: none !important;
}

/* 禁用状态下的自定义样式，保持可点击 */
.disabled-style {
  cursor: pointer !important;
  pointer-events: auto !important;
}
</style>
