<template>
  <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8" v-if="bannerList.length > 0 || loading">
    <el-card class="lg-pointer banner-card card-box-shadow">
      <el-skeleton v-if="loading" :rows="10" animated :loading="true">
        <template slot="template">
          <div class="lg-padding-24">
            <!-- 标题 -->
            <el-skeleton-item variant="p" class="h-40 w-70-percent" />
            <!-- 周数、活动数 -->
            <el-row :gutter="10" class="m-t-sm add-padding-tb-6">
                <el-col :span="12" class="display-flex flex-direction-col align-items">
                    <el-skeleton-item variant="rect" class="h-40" />
                </el-col>
                <el-col :span="12" class="display-flex flex-direction-col align-items">
                    <el-skeleton-item variant="rect" class="h-40" />
                </el-col>
            </el-row>
            <!-- 概览 -->
            <el-skeleton-item variant="p" class="m-t-sm h-60" />
            <!-- 年龄段 -->
            <el-skeleton-item variant="p" class="m-t-sm w-70-percent h-20" />
            <!-- 更新时间 -->
            <el-skeleton-item variant="p" class="m-t-md w-70-percent h-40" />
          </div>
        </template>
      </el-skeleton>
      <el-carousel v-else :interval="5000" arrow="never" indicator-position="none">
        <el-carousel-item v-for="(item, index) in bannerList" :key="index" @click.native="openDialog(item)">
          <div style="height: 100%; width: 100%;">
            <img :src="item.banner_url" class="banner-img">
          </div>
        </el-carousel-item>
      </el-carousel>
    </el-card>
    <!-- 弹窗 -->
    <el-dialog
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        width="680px"
        :show-close="true"
        custom-class="invite-dialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @click.stop
      >
        <div class="invite-dialog-title">
          <div class="title-font-24">{{ currentBanner.title }}</div>
          <div class="title-font-16-regular lg-margin-top-16" v-html="currentBanner.description"></div>
        </div>
        <!-- 旧描述已移除，标题区块已合并新版 -->
        <div class="share-content-container">
          <div style="z-index: 99">
            <div class="invite-dialog-section">
              <div class="invite-label">Invite colleagues via Email</div>
              <div class="input-row-flex">
                <div
                  class="tag-input-inner-wrap"
                  :class="{ 'is-focus': inputFocus, 'is-disabled': emailTags.length >= maxInviteCount, 'is-error': hasInputError }"
                  @click.stop="focusInput"
                >
                  <template v-for="(tag, idx) in emailTags">
                    <span
                      class="email-tag"
                      :class="{
                        'tag-error': tag.status === 'error' || tag.status === 'self' || tag.status === 'invited'
                      }"
                      :title="tag.email"
                    >
                      <span class="tag-text">{{ tag.email }}</span>
                      <!-- <span v-if="tag.status === 'error'" class="tag-tip">Invalid email address.</span>
                      <span v-else-if="tag.status === 'self'" class="tag-tip">You can't invite yourself.</span>
                      <span v-else-if="tag.status === 'invited'" class="tag-tip">Already sent</span> -->
                      <i class="el-icon-close tag-close" @click.stop="removeTag(idx)"></i>  
                    </span>
                  </template>
                  <input
                    ref="emailInput"
                    v-model="inputEmail"
                    :placeholder="emailTags.length >= 1 ? '' : '<EMAIL>; <EMAIL>'"
                    class="tag-input-real"
                    :disabled="emailTags.length >= maxInviteCount"
                    @keydown="handleInputKeydown"
                    @blur="handleInputBlur"
                    @focus="inputFocus = true"
                    @input="inputWidthUpdate"
                    :style="{ width: inputWidth + 'px' }"
                    autocomplete="off"
                  />
                </div>
                <el-button type="primary" :disabled="validInviteEmails.length === 0 || validInviteEmails.length !== emailTags.length" :loading="sendInvitesLoading" @click="sendInvites">Send Invites</el-button>
              </div>
              <div v-if="hasInputError" class="input-error-tip">{{ inputErrorTip }}</div>
            </div>
            <div class="invite-dialog-section">
              <div class="invite-label">Share exclusive link</div>
              <div class="input-row-flex">
                <el-input
                  v-model="inviteLink"
                  readonly
                  class="invite-input"
                ></el-input>
                <el-button @click="copyLink" plain type="primary">
                  <i class="lg-icon lg-icon-link"></i> Copy Link
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-dialog>
  </el-col>
</template>

<script>
// 新增导入 marketing API 服务
import { useMarketingApi, useInvitationApi } from '@/api/cg'
import { mapState } from 'vuex'
import UnitCard from './UnitCard.vue'
import { equalsIgnoreCase } from '@/utils/common'
import tools from '@/utils/tools'

export default {
  name: 'BannerCard',
  components: {
    UnitCard
  },
  data() {
    return {
      bannerList: [],
      loading: true,
      currentBanner: {},
      dialogVisible: false,
      inputEmail: '',
      emailTags: [], // {email, status, tip}
      maxInviteCount: 20,
      inputFocus: false,
      inputWidth: 200,
      invitationCode: '',
      recentInvitedEmails: [],
      sendInvitesLoading: false
    }
  },
  computed: {
    ...mapState({
      cgUser: state => state.cgAuth.user, // 当前插件用户
    }),
    // 当前用户的邮箱
    currentUserEmail() {
      return this.cgUser && this.cgUser.email
    },
    // 有效的邮箱
    validInviteEmails() {
      // 仅新用户且未被邀请的邮箱可发送
      return this.emailTags.filter(t => t.status === 'valid')
    },
    hasInputError() {
      return !!this.inputErrorTip
    },
    inputErrorTip() {
      // 先根据输入框内容判断是否已邀请或者是自己的邮箱
      if (equalsIgnoreCase(this.inputEmail.trim(), this.currentUserEmail)) return "You can't invite yourself."
      if (tools.arrayContainsIgnoreCase(this.recentInvitedEmails, this.inputEmail.trim())) return 'Already sent'
      // 如果还没有回车添加邮箱，则返回空错误
      if (this.emailTags.length === 0 || !!this.inputEmail.trim()) {
        return ''
      }
      // 如果已经回车添加了邮箱，找到最后一个邮箱
      let inputEmail = this.emailTags[this.emailTags.length - 1].email
      // 如果最后一个邮箱校验不通过，则返回错误
      if (!this.validateEmail(inputEmail.trim())) return 'Invalid email address.'
      // 如果最后一个邮箱是自己的邮箱，则返回错误
      if (equalsIgnoreCase(inputEmail.trim(), this.currentUserEmail)) return "You can't invite yourself."
      // 如果最后一个邮箱已邀请，则返回错误
      if (tools.arrayContainsIgnoreCase(this.recentInvitedEmails, inputEmail.trim())) return 'Already sent'
      return ''
    },
    // 邀请链接
    inviteLink() {
      return `${window.location.origin}/app/index.html#/login?c=${this.invitationCode}&t=${this.currentBanner.id}&f=Link`
    }
  },
  mounted() { 
    this.getBannerList()
  },
  methods: {
    // 获取营销活动模板
    async getBannerList() {
      try {
        this.loading = true
        // 调用封装的 marketing API 服务
        const marketingApi = useMarketingApi()
        let res = await marketingApi.getMarketingTemplates()
        if (res.total > 0) {
          this.invitationCode = res.invitation_code
          this.bannerList = res.templates
          this.currentBanner = this.bannerList[0]
        }
      } catch (error) {
      } finally {
        this.loading = false
      }
    },
    // 打开弹窗
    openDialog(banner) {
      this.emailTags = []
      this.inputEmail = ''
      this.dialogVisible = true
      this.currentBanner = banner
      this.getRecentInvitedEmails()
    },
    async getRecentInvitedEmails() {
      const marketingApi = useMarketingApi()
      const res = await marketingApi.getRecentInvitedEmails({
        marketing_template_id: this.currentBanner.id
      })
      this.recentInvitedEmails = res.invited_emails
    },
    // 聚焦输入框
    focusInput() {
      this.$refs.emailInput && this.$refs.emailInput.focus()
    },
    // 处理输入框按键
    async handleInputKeydown(e) {
      if ([13, 186, 188, 59].includes(e.keyCode)) { // 回车、分号、逗号
        e.preventDefault()
        await this.tryAddInputEmail()
      } else if (e.key === 'Backspace' && !this.inputEmail && this.emailTags.length > 0) {
        // 输入为空时按退格删除最后一个 tag
        this.emailTags.pop()
      }
    },
    async handleInputBlur() {
      this.inputFocus = false
    },
    async tryAddInputEmail() {
      if (!this.inputEmail) return
      let emails = this.inputEmail.split(/[; \s]+/).filter(Boolean)
      for (let email of emails) {
        if (this.emailTags.length >= this.maxInviteCount) break
        await this.addEmailTag(email)
      }
      this.inputEmail = ''
      this.inputWidthUpdate()
    },
    async addEmailTag(email) {
      email = email.trim().toLowerCase()
      if (!email) return
      if (this.emailTags.find(t => equalsIgnoreCase(t.email, email))) return
      if (!this.validateEmail(email)) {
        this.emailTags.push({ email, status: 'error', tip: 'Invalid email address.' })
        return
      }
      if (equalsIgnoreCase(email, this.currentUserEmail)) {
        this.emailTags.push({ email, status: 'self', tip: 'You can\'t invite yourself.' })
        return
      }
      if (tools.arrayContainsIgnoreCase(this.recentInvitedEmails, email)) {
        this.emailTags.push({ email, status: 'invited', tip: 'Already sent' })
      } else {
        this.emailTags.push({ email, status: 'valid', tip: '' })
      }
    },
    // 邮箱校验
    validateEmail(email) {
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
    },
    removeTag(idx) {
      this.emailTags.splice(idx, 1)
      this.inputWidthUpdate()
    },
    async sendInvites() {
      // 只处理 status 为 valid 的邮箱
      const emails = this.validInviteEmails.map(t => t.email)
      if (emails.length === 0) return
      
      try {
        // 步骤1: 参数校验
        if (!this.currentBanner || !this.currentBanner.id) {
          return
        }
        this.sendInvitesLoading = true
        
        // 步骤2: 调用发送邀请邮件的接口
        let params = {
          emails: emails,
          template_id: this.currentBanner.id
        }
        const invitationApi = useInvitationApi()
        await invitationApi.sendInvitationEmail(params)
        // 发送成功后，根据邀请人数显示不同的成功提示
        if (emails.length > 1) {
          this.$message.success(`Success! Your invites are on their way to ${emails.length} colleagues.`)
        } else {
          this.$message.success('Success! Your invite is on its way to 1 colleague.')
        }
        // 清空邮件输入框
        this.emailTags = []
        // 更新最近邀请的邮箱列表
        this.recentInvitedEmails = [...this.recentInvitedEmails, ...emails]
      } catch (error) {
      } finally {
        this.sendInvitesLoading = false
      }
    },
    copyLink() {
      const input = document.createElement('input')
      input.value = this.inviteLink
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$message.success('Link copied!')
    },
    inputWidthUpdate() {
      // 动态调整 input 宽度，体验更佳
      this.$nextTick(() => {
        const val = this.inputEmail || ''
        // 计算宽度，最小60px，最大200px
        const span = document.createElement('span')
        span.style.visibility = 'hidden'
        span.style.position = 'fixed'
        span.style.fontSize = '16px'
        span.style.padding = '0 8px'
        document.body.appendChild(span)
        let w = Math.max(60, Math.min(200, span.offsetWidth + 24))
        document.body.removeChild(span)
        this.inputWidth = w
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-card__body {
  padding: 0 !important;
  margin: 0 !important;
}
/deep/ .el-carousel__container {
  height: 320px !important;
}
.banner-card {
  height: 320px !important;
  border-radius: 8px !important;
  margin: 0px 8px 24px !important;
}
.card-box-shadow:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.3) !important;
}
.banner-img {
  width: 100%;
  height: 100%;
}
.h-20 {
  height: 20px;
}
.h-40 {
  height: 40px;
}
.h-60 {
  height: 60px;
}
// 弹窗样式
/deep/.invite-dialog {
    padding: 0 !important;
    border-radius: 20px 20px 20px 20px;
    background: linear-gradient(344deg, rgba(255, 255, 255, 0.40) 38.49%, rgba(255, 255, 255, 0.20) 54.5%, rgba(16, 179, 183, 0.20) 71.4%, rgba(170, 137, 242, 0.40) 93.6%), #FFF;
    width: 620px;
    // 隐藏头部
    .el-dialog__header {
      padding: 24px 0 0 0 !important;
      button {
        font-size: 20px;
      }
    }

    .el-dialog__body {
      padding: 0;
    }
}

.invite-dialog-title {
  text-align: center;
  margin: 0 24px 24px;
}

.share-content-container {
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 22px;
  padding-bottom: 22px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: white;
  border-radius: 20px;
  border: 1px solid white;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(rgba(170, 137, 242, 0.2), rgba(16, 179, 183, 0.2), rgba(255, 255, 255, 0.2));
    border-radius: 20px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
  }
}

.invite-dialog-section {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}
.tag-input-inner-wrap {
  min-height: 40px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  padding: 3px 12px;
  margin-bottom: 8px;
  cursor: text;
  transition: border-color 0.2s;
  box-sizing: border-box;
}
.tag-input-inner-wrap.is-focus {
  border-color: #10B3B7;
}
.tag-input-inner-wrap.is-disabled {
  background: #f5f7fa;
  cursor: not-allowed;
}
.tag-input-inner-wrap.is-error {
  border-color: #f56c6c;
}
.email-tag {
  display: flex;
  align-items: center;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 20px;
  padding: 0 10px;
  margin-right: 8px;
  margin-bottom: 4px;
  margin-top: 4px;
  font-size: 15px;
  height: 24px;
  line-height: 24px;
  color: #333;
  position: relative;
  max-width: 260px;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
}
.email-tag.tag-error {
  background: #fff0f0;
  color: #f56c6c;
  border: 1px solid #f56c6c;
}
.email-tag.tag-invited,
.email-tag.tag-old {
  background: #f0f1f3;
  color: #b0b0b0;
}
.email-tag.tag-reinvite {
  background: #eaf6ff;
  color: #10B3B7;
  border: 1px solid #10B3B7;
}
.tag-tip {
  margin-left: 8px;
  font-size: 13px;
  color: #f56c6c;
  font-weight: 400;
}
.email-tag.tag-invited .tag-tip,
.email-tag.tag-old .tag-tip {
  color: #b0b0b0;
}
.email-tag.tag-reinvite .tag-tip {
  color: #10B3B7;
}
.tag-close {
  flex-shrink: 0;
  margin-left: 6px;
  cursor: pointer;
  color: #b0b0b0;
  font-size: 16px;
  transition: color 0.2s;
}
.tag-close:hover {
  color: #f56c6c;
}
.tag-input-real {
  border: none;
  outline: none;
  font-size: 14px;
  min-width: 60px;
  max-width: 450px;
  height: 32px;
  line-height: 32px;
  background: transparent;
  flex: 1;
}

.input-error-tip {
  color: #f56c6c;
  font-size: 13px;
  margin-top: -6px;
  margin-left: 12px;
  min-height: 18px;
}

.invite-label {
  font-size: 16px;
  font-weight: 600;
  margin-top: 8px;
}
.invite-input {
  flex: 1;
  min-width: 0;
  margin-bottom: 4px;
  height: 40px;
  font-size: 15px;
  border-radius: 8px;
}

.input-row-flex {
  display: flex;
  align-items: flex-start;
  width: 100%;
  gap: 12px;
}
.tag-input-inner-wrap {
  flex: 1;
  min-width: 0;
  margin-bottom: 0;
}

.tag-text {
  min-width: 0;
  max-width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
}
</style> 