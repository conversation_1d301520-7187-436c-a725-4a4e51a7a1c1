<template>
  <div>
    <el-dropdown placement="bottom-start" trigger="click" ref="dropdown" :class="{ 'profile-width-full': showWidth }">
      <div class="add-margin-r-16 profile-width" :title="selectedGradeText" :class="{ 'profile-width-full': showWidth }">
        <el-input v-model="selectedGradeText" readonly suffix-icon="el-icon-arrow-down" :class="{ 'grade-placeholder': this.computedCheckedGrades.length === 0 }"></el-input>
      </div>
      <el-dropdown-menu slot="dropdown" append-to-body class="vertical-dropdown lg-scrollbar-show">
        <div class="profile-width add-padding-l-12 add-padding-r-12 add-padding-t-8 add-padding-b-8" :class="{ 'profile-width-full': showWidth }">
          <el-checkbox
            v-model="selectAll"
            @change="handleSelectAllChange"
            :indeterminate="selectAllIndeterminate"
          >
            <span>{{ $t('loc.allGrades') }}</span>
          </el-checkbox>
        </div>
        <div
          v-for="item in ageGroupsCopy"
          :key="item.ageGroup"
          class="profile-width add-padding-l-12 add-padding-r-12 add-padding-t-8 add-padding-b-8"
          :class="{ 'profile-width-full': showWidth }"
        >
          <el-checkbox
            v-model="item.selected"
            class="dropdown-checkbox-item"
            @change="selectedItem(item)"
          >
            {{ ageGroupTranslate(item.ageGroup) }}
          </el-checkbox>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { TAILOR_UNIT_AGE_GROUPS } from '@/utils/const'

export default {
  props: {
    ageGroups: {
      type: Array,
      default: () => []
    },
    showWidth: {
      type: Boolean,
      default: false
    },
    mixedAgeGroup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectAll: false,
      ageGroupsCopy: []
    }
  },
  computed: {
    // 计算选中的组
    computedCheckedGrades() {
      return this.ageGroupsCopy.filter((v) => v.selected)
    },
    selectAllIndeterminate() {
      return (
        this.ageGroupsCopy &&
        this.ageGroupsCopy.some((option) => option.selected) &&
        !this.selectAll
      )
    },
    selectedGradeText: {
      get() {
        if (this.computedCheckedGrades.length === 0) {
          return this.mixedAgeGroup ? this.$t('loc.pleaseSelectGradeBand') : this.$t('loc.pleaseSelectGrade')
        } else if (this.selectAll) {
          return this.$t('loc.allGrades')
        } else {
          return this.computedCheckedGrades.map((item) => this.ageGroupTranslate(item.ageGroup)).join(', ')
        }
      }
    }
  },
  watch: {
    ageGroups: {
      handler() {
        this.ageGroupsCopy = JSON.parse(JSON.stringify(this.ageGroups))
        this.updateSelectAllStatus()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    ageGroupTranslate(ageGroup) {
      return (TAILOR_UNIT_AGE_GROUPS.find((v) => v.ageGroup === ageGroup) || {}).label || ageGroup
    },
    // 更新全选状态
    updateSelectAllStatus() {
      this.selectAll = this.ageGroupsCopy.every((option) => option.selected)
    },
    // 全选/取消全选
    handleSelectAllChange(checked) {
      this.selectAll = checked
      // 通知父组件：全选状态变化
      this.$emit('updateSelectAll', checked) 
    },
    // 单个选项的选择变更
    selectedItem(item) {
      this.updateSelectAllStatus()
      // 通知父组件：单个年龄组状态变化
      this.$emit('updateItemChecked', item)
    },
    // 设置年龄组没有勾选
    setAgeGroupUnselected(ageGroup) {
      let selectedItem = this.ageGroupsCopy.find(v => v.ageGroup === ageGroup)
      if (selectedItem) {
        selectedItem.selected = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dropdown-menu {
  padding: 4px;
  max-height: 30vh;
}
::v-deep {
  .grade-placeholder .el-input__inner {
    color: #606266 !important;
  }
  .el-input__inner {
    cursor: pointer;
    padding-left: 12px;
  }
  .el-checkbox__inner {
    width: 16px;
    height: 16px;
  }
  .el-checkbox__inner::after {
    top: 1px;
    left: 4px;
  }
}
.profile-width {
  width: 200px;
  & > label {
    margin-bottom: 0;
  }
}
.vertical-dropdown .profile-width {
  width: 190px;
}
.profile-width-full {
  width: 100% !important;
  & > label {
    margin-bottom: 0;
  }
}
::v-deep .el-input__validateIcon {
  display: none;
}
</style>
