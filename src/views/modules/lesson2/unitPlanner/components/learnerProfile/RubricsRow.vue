<template>
  <div class="rubrics-row">
    <!-- 选择区域 -->
    <div class="rubrics-select-area">
      <!-- 使用整个选择区域作为触发器 -->
      <div
        slot="reference"
        class="select-area"
        @click="openDialog"
        :class="{'is-disabled': disabled, 'disabled-border': disabled && !showSelectedRubrics, 'is-selected': visible}">
        <div v-if="hasSelected && showSelectedRubrics" class="selected-portraits">
          <span
            v-for="(title, index) in localSelected"
            :key="index"
            >
            <el-tooltip
              placement="top"
              trigger="hover"
              popper-class="max-width-400"
              :open-delay="500">
              <div slot="content" v-html="getDescriptionByTitle(title)"></div>
              <el-tag
                size="small"
                type="info"
                disable-transitions>
                <span style="color: #676879;line-height: 24px" :style="{ opacity: disabled ? 0.6 : 1 }">
                  {{ title }}
                </span>
                <i v-if="!disabled" @click.stop="removeRubrics(title)" class="flag el-icon-close red lg-pointer line-height-18"></i>
              </el-tag>
            </el-tooltip>
          </span>
        </div>
        <div v-else class="placeholder-text" :class="{'display-flex align-items justify-content font-weight-600': !showSelectedRubrics, 'lg-color-text-placeholder': showSelectedRubrics, 'lg-color-primary': visible || !showSelectedRubrics, 'lg-color-text-disabled': disabled }">
          <i v-if="!showSelectedRubrics" class="el-icon-circle-plus-outline add-category font-size-20 lg-pointer" :class="{'lg-color-primary': !disabled}" style="margin-right: 4px;"></i>
          <span>{{ $t('loc.curriculumUnitSelectPortrait') }}</span>
        </div>
      </div>
      <el-dialog
        :visible.sync="visibleDialog"
        :append-to-body="true"
        title="Select PoG Attributes/Standards"
        custom-class="rubrics-dialog"
        width="800"
        :close-on-click-modal="false"
        >
        <div class="lg-margin-bottom-24">
          <el-input
            v-model="search"
            placeholder="Search Portrait of a Graduate"
            @input="handleSearch">
          </el-input>
        </div>
        <div class="rubrics-selector">
          <!-- 选项列表 -->
          <div v-if="hasSubStandards">
            <div v-if="treeData.length === 0" class="empty-data">
              <div class="empty-text">
                <span>No data</span>
              </div>
            </div>
            <lg-tree
              v-else
              ref="lgTree"
              :tree-data="treeData"
              :default-props="treeProps"
              :search-query="search"
              :show-search="false"
              :show-checkbox="true"
              :enable-select-all="true"
              :max-items="maxSelectCount"
              :selected-count="tempLocalSelected.length"
              node-key="id"
              @selection-change="handleTreeSelectionChange"
              @limit-warning="handleTreeLimitWarning">
            </lg-tree>
          </div>
          <div class="rubrics-list" v-else>
            <div v-if="filteredRubricsOptions.length === 0" class="empty-data">
              <div class="empty-text">
                <span>No data</span>
              </div>
            </div>
            <el-checkbox-group
              v-else
              v-model="tempLocalSelected"
              @change="handleRubricsChange">
              <el-checkbox
                v-for="option in filteredRubricsOptions"
                :key="option.title"
                :label="option.title"
                :disabled="disabled"
                class="rubrics-option">
                <div class="rubrics-content">
                  <div class="rubrics-title">{{ option.title }}</div>
                  <div class="rubrics-desc">{{ option.description }}</div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <template v-if="tempLocalSelected.length > 0">
            <el-divider></el-divider>
            <div class="selected-portraits">
              <div class="lg-color-text-primary">
                Selected:
              </div>
              <span
                v-for="(title, index) in tempLocalSelected"
                :key="index"
                >
                <el-tooltip
                  placement="top"
                  trigger="hover"
                  popper-class="max-width-400"
                  :open-delay="500">
                  <div slot="content" v-html="getDescriptionByTitle(title)"></div>
                  <el-tag
                    size="small"
                    type="info"
                    disable-transitions>
                    <span style="color: #676879;line-height: 24px" :style="{ opacity: disabled ? 0.6 : 1 }">
                      {{ title }}
                    </span>
                    <i @click.stop="removeRubricsFromDialog(title)" class="flag el-icon-close red lg-pointer line-height-18"></i>
                  </el-tag>
                </el-tooltip>
              </span>
            </div>
          </template>
        </div>
        <div slot="footer">
          <el-button plain @click="handleCancel">Cancel</el-button>
          <el-button type="primary" @click="handleConfirm">Confirm</el-button>  
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import LgTree from '@/components/LgTree.vue'
import { mapState } from 'vuex'

export default {
  name: 'RubricsRow',
  components: {
    LgTree
  },
  props: {
    // 所有校训选项
    rubricsOptions: {
      type: Array,
      default: () => []
    },
    // 已选择的校训
    selectedRubrics: {
      type: Array,
      default: () => []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示已选中的校训
    showSelectedRubrics: {
      type: Boolean,
      default: true
    },
    // 最大选择数量限制（-1 表示不限制）
    maxSelectCount: {
      type: Number,
      default: -1
    },
    // 是否是课程助手
    isLessonAssistant: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false, // 是否显示选择器
      localSelected: [], // 本地选中的校训
      checkAll: false, // 是否全选
      isIndeterminate: false, // 是否半选
      originalDoClose: null, // 原始的 doClose 方法
      visibleDialog: false, // 是否显示弹窗
      search: '', // 搜索关键词
      treeSelectedKeys: [], // 树形选择的keys
      tempLocalSelected: [], // 临时选中状态（弹窗中的选择）
      tempSelectedRubrics: [], // 临时选中的校训数据
    }
  },
  computed: {
    ...mapState({
      agencyLearnerProfile: (state) => state.lesson.agencyLearnerProfile, // 机构校训
      guideFeatures: state => state.common.guideFeatures // 功能引导
    }),
    // 是否显示 POG 转换引导
    showPogTransformGuide() {
      return !this.showSelectedRubrics && this.guideFeatures && this.guideFeatures.showPogTransformGuide // 未引导过
      && this.agencyLearnerProfile && !this.agencyLearnerProfile.mixedAgeGroup // 不是混合年龄组
    },
    // 是否有选中项
    hasSelected() {
      return this.localSelected && this.localSelected.length > 0;
    },
    // 所有可选项的标题列表
    allPortraitTitles() {
      return this.rubricsOptions.map(option => option.title);
    },
    // 获取限制提示信息
    limitMessage() {
      if (this.isLessonAssistant) {
        return this.$t('loc.learnerProfileSelectLimitForLesson');
      } else if (this.maxSelectCount === 3) {
        return this.$t('loc.learnerProfileSelectLimitFor1Week');
      } else if (this.maxSelectCount === 6) {
        return this.$t('loc.learnerProfileSelectLimitFor2Week');
      } else if (this.maxSelectCount === 9) {
        return this.$t('loc.learnerProfileSelectLimitFor3Week');
      } else {
        return '';
      }
    },
    hasSubStandards() {
      return this.rubricsOptions.some(item => item.subStandards && item.subStandards.length > 0)
    },
    // 转换为树形数据结构
    treeData() {
      if (!this.hasSubStandards) {
        return [];
      }
      
      return this.filteredRubricsOptions.map(option => {
        const node = {
          id: option.title,
          abbreviation: option.title,
          description: option.description,
          expectations: option.expectations,
          data: option,
          children: [] // 确保每个节点都有children属性
        };
        
        // 如果有子标准，添加children
        if (option.subStandards && option.subStandards.length > 0) {
          node.children = option.subStandards.map((subStandard, index) => ({
            id: `${option.title}_${index}`,
            abbreviation: subStandard.title,
            description: subStandard.description,
            expectations: subStandard.expectations,
            children: [], // 确保子节点也有children属性
            data: {
              ...subStandard,
              parentTitle: option.title
            }
          }));
        }
        
        return node;
      });
    },
    
    // 树形组件的配置
    treeProps() {
      return {
        children: 'children',
        label: 'abbreviation'  // 使用abbreviation作为label
      };
    },
    
    // 过滤后的选项（用于搜索）
    filteredRubricsOptions() {
      if (this.hasSubStandards) {
        return this.rubricsOptions
      } else {
        if (this.search && this.search.trim().length > 0) {
          return this.rubricsOptions.filter(option => option.title.toUpperCase().includes(this.search.toUpperCase())
          || option.description && option.description.toUpperCase().includes(this.search.toUpperCase()))
        } else {
          return this.rubricsOptions
        }
      }
    }
  },
  watch: {
    // 监听外部传入的选中值变化
    selectedRubrics: {
      immediate: true,
      handler(newVal) {
        if (newVal && Array.isArray(newVal)) {
          // 如果是多层级结构，需要展开所有选中的子标准
          if (this.hasSubStandards) {
            const displayTitles = [];
            newVal.forEach(item => {
              if (typeof item === 'object' && item.title) {
                if (item.subStandards && item.subStandards.length > 0) {
                  // 如果有子标准，添加子标准的标题
                  item.subStandards.forEach(sub => {
                    if (sub.title) {
                      displayTitles.push(sub.title);
                    }
                  });
                } else {
                  // 如果没有子标准，添加主标准的标题
                  displayTitles.push(item.title);
                }
              } else if (typeof item === 'string') {
                displayTitles.push(item);
              }
            });
            this.localSelected = displayTitles;
          } else {
            // 普通列表结构，使用原有逻辑
            this.localSelected = newVal.map(item => {
              if (typeof item === 'object' && item.title) {
                return item.title;
              }
              return item;
            });
          }
          
          // 更新全选状态
          this.updateCheckAllStatus();
        } else {
          this.localSelected = [];
          this.checkAll = false;
          this.isIndeterminate = false;
        }
      }
    },
    // 监听校训选项的变化
    rubricsOptions: {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        if (newVal && Array.isArray(newVal)) {
          // 当校训选项发生变化时，更新全选状态
          this.updateCheckAllStatus();
        }
      }
    },
    // 监听临时选择状态的变化
    tempLocalSelected: {
      handler(newVal, oldVal) {
        // 检查选择数量限制，如果超过限制，则提示用户并恢复到之前的状态
        if (this.maxSelectCount > -1 && newVal.length > this.maxSelectCount) {
          this.$message.warning(this.limitMessage);
          // 恢复到之前的状态
          this.tempLocalSelected = [...oldVal];
          return;
        }
        
        // 同步到树组件
        if (this.hasSubStandards && this.$refs.lgTree && this.visibleDialog) {
          this.$nextTick(() => {
            this.setTreeCheckedKeysForTemp();
          });
        }
      },
      deep: true
    },
    
    // 监听正式选择状态的变化，同步到树组件
    localSelected: {
      handler() {
        if (this.hasSubStandards && this.$refs.lgTree && !this.visibleDialog) {
          this.$nextTick(() => {
            this.setTreeCheckedKeys();
          });
        }
      }
    },

    // 监听树形数据变化，重新设置选中状态（处理搜索场景）
    treeData: {
      handler(newVal, oldVal) {
        if (this.hasSubStandards && this.$refs.lgTree && this.visibleDialog) {
          // 在弹窗中，需要重新设置临时选中状态
          this.$nextTick(() => {
            this.setTreeCheckedKeysForTemp();
          });
        }
      },
      deep: true
    },

  },
  mounted() {
    // 设置 popover 事件
    this.setupPopoverEvents();
    
    // 初始化树组件选中状态
    if (this.hasSubStandards) {
      this.$nextTick(() => {
        this.setTreeCheckedKeys();
      });
    }
  },
  beforeDestroy() {
    // 移除 popover 事件
    this.removePopoverEvents();
  },
  methods: {
    // 设置 popover 事件
    setupPopoverEvents() {
      // 获取 popover 实例
      this.$nextTick(() => {
        if (this.$refs.rubricsPopover) {
          const popover = this.$refs.rubricsPopover;
          // 保存原始的 doClose 方法
          this.originalDoClose = popover.doClose;
          // 重写 doClose 方法
          popover.doClose = () => {
            this.visible = false;
            if (this.originalDoClose) {
              this.originalDoClose.call(popover);
            }
          };
          // 监听 popover 的显示/隐藏事件
          popover.$on('show', () => {
            document.addEventListener('mousedown', this.handleOutsideClick);
          });
          popover.$on('hide', () => {
            document.removeEventListener('mousedown', this.handleOutsideClick);
          });
        }
      });
    },
    // 移除 popover 事件
    removePopoverEvents() {
      document.removeEventListener('mousedown', this.handleOutsideClick);
      // 恢复原始的 doClose 方法
      if (this.$refs.rubricsPopover && this.originalDoClose) {
        this.$refs.rubricsPopover.doClose = this.originalDoClose;
      }
    },
    // 处理外部点击事件
    handleOutsideClick(event) {
      if (this.visible) {
        const popover = this.$refs.rubricsPopover;
        if (popover) {
          const popoverEl = popover.$el;
          const popper = popover.popperElm;
          const reference = popover.referenceElm;

          // 检查点击是否在 popover 内部、触发器内部或选择区域内
          const isClickInside = (popper && popper.contains(event.target)) ||
                              (reference && reference.contains(event.target)) ||
                              (popoverEl && popoverEl.contains(event.target));

          if (!isClickInside) {
            this.$nextTick(() => {
              this.closePopover();
            });
          }
        }
      }
    },
    // 关闭 popover
    closePopover() {
      this.visible = false;
      if (this.$refs.rubricsPopover) {
        this.$refs.rubricsPopover.doClose();
      }
    },

    // 处理校训选择变更
    handleRubricsChange(value) {
      // 在弹窗中，只更新临时状态
      if (this.visibleDialog) {
        this.tempLocalSelected = value;
        // 转换选中的标题为完整的对象数据
        const selectedObjects = this.rubricsOptions.filter(option =>
          value.includes(option.title)
        );
        this.tempSelectedRubrics = selectedObjects;
      } else {
        // 在popover中，保持原有逻辑
        this.localSelected = value;
        // 更新全选状态
        this.updateCheckAllStatus();
        // 转换选中的标题为完整的对象数据
        const selectedObjects = this.rubricsOptions.filter(option =>
          value.includes(option.title)
        );
        this.$emit('updateSelectedRubrics', selectedObjects);
      }
    },
    // 移除校训
    removeRubrics(title) {
      if (this.hasSubStandards) {
        // 树形结构：直接操作数据并同步到树形组件
        
        // 1. 从localSelected中移除该标题
        const newLocalSelected = this.localSelected.filter(t => t !== title);
        this.localSelected = newLocalSelected;
        
        // 2. 重新构建selectedRubrics数据
        const newSelectedRubrics = [];
        const processedParents = new Set();
        
        // 遍历剩余的选中标题，重新构建数据结构
        newLocalSelected.forEach(selectedTitle => {
          // 首先检查是否为主标准标题
          const mainOption = this.rubricsOptions.find(opt => opt.title === selectedTitle);
          if (mainOption && !processedParents.has(mainOption.title)) {
            // 这是一个主标准
            newSelectedRubrics.push({
              title: mainOption.title,
              description: mainOption.description,
              expectations: mainOption.expectations,
              subStandards: mainOption.subStandards || []
            });
            processedParents.add(mainOption.title);
          } else {
            // 检查是否为子标准
            for (const option of this.rubricsOptions) {
              if (option.subStandards && option.subStandards.length > 0) {
                const subStandard = option.subStandards.find(sub => sub.title === selectedTitle);
                if (subStandard) {
                  // 找到已存在的父级或创建新的父级
                  let parentRubric = newSelectedRubrics.find(r => r.title === option.title);
                  if (!parentRubric) {
                    parentRubric = {
                      title: option.title,
                      description: option.description,
                      expectations: option.expectations,
                      subStandards: []
                    };
                    newSelectedRubrics.push(parentRubric);
                    processedParents.add(option.title);
                  }
                  
                  // 添加子标准（如果还没有的话）
                  if (!parentRubric.subStandards.some(sub => sub.title === subStandard.title)) {
                    parentRubric.subStandards.push({
                      title: subStandard.title,
                      description: subStandard.description,
                      expectations: subStandard.expectations
                    });
                  }
                  break;
                }
              }
            }
          }
        });

        // 3. 发送更新事件
        this.$emit('updateSelectedRubrics', newSelectedRubrics);

      } else {
        // 普通列表结构：使用原有逻辑
        const newSelected = this.localSelected.filter(t => t !== title);
        this.handleRubricsChange(newSelected);
      }
    },
    // 根据标题获取描述
    getDescriptionByTitle(title) {
      // 首先检查是否为主标准标题
      const mainItem = this.rubricsOptions.find(opt => opt.title === title);
      if (mainItem) {
        let content = mainItem.title;
        if (mainItem.description) {
          content += '<br/>' + mainItem.description
        }
        if (mainItem.expectations) {
          content += '<br/><br/>Expectations<br/>' + mainItem.expectations
        }

        // 如果有子标准，也显示子标准信息
        if (mainItem.subStandards && mainItem.subStandards.length > 0) {
          content += '<br/><br/>Sub Standards:';
          mainItem.subStandards.forEach((sub, index) => {
            content += `<br/>${index + 1}. ${sub.title || 'Untitled'}`;
            if (sub.description) {
              content += `: ${sub.description}`;
            }
          });
        }

        return content;
      }

      // 如果不是主标准，则检查是否为子标准标题
      for (const option of this.rubricsOptions) {
        if (option.subStandards && option.subStandards.length > 0) {
          const subStandard = option.subStandards.find(sub => sub.title === title);
          if (subStandard) {
            let content = `${subStandard.title}`;
            if (subStandard.description) {
              content += '<br/>' + subStandard.description;
            }
            if (subStandard.expectations) {
              content += '<br/><br/>Expectations<br/>' + subStandard.expectations;
            }
            return content;
          }
        }
      }

      // 如果都找不到，返回标题
      return title;
    },

    // 更新全选状态
    updateCheckAllStatus() {
      const checkedCount = this.localSelected.length;
      const totalCount = this.rubricsOptions.length;

      this.checkAll = checkedCount === totalCount && totalCount > 0;
      this.isIndeterminate = checkedCount > 0 && checkedCount < totalCount;
    },
    // 处理搜索
    handleSearch(value) {
      this.search = value;
    },
    
    // 统一的选择转换方法
    convertSelectedToTreeKeys(selectedTitles) {
      const checkedKeys = [];
      selectedTitles.forEach(title => {
        // 检查是否为主标准
        const mainOption = this.rubricsOptions.find(opt => opt.title === title);
        if (mainOption) {
          if (mainOption.subStandards && mainOption.subStandards.length > 0) {
            // 有子标准：选中所有子标准
            mainOption.subStandards.forEach((sub, index) => {
              checkedKeys.push(`${mainOption.title}_${index}`);
            });
          } else {
            // 无子标准：直接选中主标准
            checkedKeys.push(title);
          }
        } else {
          // 检查是否为子标准
          for (const option of this.rubricsOptions) {
            if (option.subStandards && option.subStandards.length > 0) {
              const subIndex = option.subStandards.findIndex(sub => sub.title === title);
              if (subIndex !== -1) {
                checkedKeys.push(`${option.title}_${subIndex}`);
                break;
              }
            }
          }
        }
      });
      return checkedKeys;
    },

    // 构建选中的 rubrics 数据结构
    buildSelectedRubrics(selectedTitles) {
      const selectedRubrics = [];
      const processedParents = new Set();
      
      selectedTitles.forEach(title => {
        // 检查是否为主标准
        const mainOption = this.rubricsOptions.find(opt => opt.title === title);
        if (mainOption && !processedParents.has(mainOption.title)) {
          // 主标准
          selectedRubrics.push({
            title: mainOption.title,
            description: mainOption.description,
            expectations: mainOption.expectations,
            subStandards: mainOption.subStandards || []
          });
          processedParents.add(mainOption.title);
        } else {
          // 子标准：找到父级并添加
          for (const option of this.rubricsOptions) {
            if (option.subStandards && option.subStandards.length > 0) {
              const subStandard = option.subStandards.find(sub => sub.title === title);
              if (subStandard) {
                let parentRubric = selectedRubrics.find(r => r.title === option.title);
                if (!parentRubric) {
                  parentRubric = {
                    title: option.title,
                    description: option.description,
                    expectations: option.expectations,
                    subStandards: []
                  };
                  selectedRubrics.push(parentRubric);
                  processedParents.add(option.title);
                }
                if (!parentRubric.subStandards.some(sub => sub.title === subStandard.title)) {
                  parentRubric.subStandards.push({
                    title: subStandard.title,
                    description: subStandard.description,
                    expectations: subStandard.expectations
                  });
                }
                break;
              }
            }
          }
        }
      });
      
      return selectedRubrics;
    },

    // 处理树形选择变化
    handleTreeSelectionChange(selectedItems) {
      // LgTree 返回格式：[{id, abbreviation, name}]
      // 需要转换为 RubricsRow 格式：字符串数组 + 完整对象数组
      
      const currentVisibleSelectedTitles = [];
      
      selectedItems.forEach(item => {
        // 根据 ID 判断是主标准还是子标准
        if (item.id.includes('_')) {
          // 子标准：使用 abbreviation 作为标题
          currentVisibleSelectedTitles.push(item.abbreviation);
        } else {
          // 主标准：使用 id 作为标题
          currentVisibleSelectedTitles.push(item.id);
        }
      });
      
      let finalSelectedTitles = currentVisibleSelectedTitles;
      
      // 如果处于搜索状态，需要合并隐藏的选中项
      if (this.search && this.search.trim() && this.visibleDialog) {
        // 获取当前搜索下不可见但之前已选中的项目
        const hiddenSelectedTitles = this.tempLocalSelected.filter(title => {
          // 检查该标题对应的项目是否在当前搜索结果中不可见
          return !this.isItemVisibleInCurrentSearch(title);
        });
        
        // 合并当前可见的选中项和隐藏的选中项
        finalSelectedTitles = [...new Set([...currentVisibleSelectedTitles, ...hiddenSelectedTitles])];
      }
      
      // 重新构建完整的 rubrics 数据结构
      const finalSelectedRubrics = this.buildSelectedRubrics(finalSelectedTitles);
      
      if (this.visibleDialog) {
        this.tempLocalSelected = finalSelectedTitles;
        this.tempSelectedRubrics = finalSelectedRubrics;
      } else {
        this.localSelected = finalSelectedTitles;
        this.$emit('updateSelectedRubrics', finalSelectedRubrics);
      }
    },

    // 处理树形组件的限制警告
    handleTreeLimitWarning(data) {
      this.$message.warning(this.limitMessage);
    },

    // 判断某个项目是否在当前搜索结果中可见
    isItemVisibleInCurrentSearch(title) {
      if (!this.search || !this.search.trim()) {
        return true; // 没有搜索时，所有项目都可见
      }
      
      // 检查是否为主标准标题
      const mainOption = this.filteredRubricsOptions.find(opt => opt.title === title);
      if (mainOption) {
        return true; // 主标准在搜索结果中
      }
      
      // 检查是否为子标准标题
      for (const option of this.filteredRubricsOptions) {
        if (option.subStandards && option.subStandards.length > 0) {
          const subStandard = option.subStandards.find(sub => sub.title === title);
          if (subStandard) {
            return true; // 子标准在搜索结果中
          }
        }
      }
      
      return false; // 不在当前搜索结果中
    },

    // 根据已选中的数据设置树形组件的选中状态
    setTreeCheckedKeys() {
      if (!this.$refs.lgTree || !this.hasSubStandards || this.visibleDialog) return;
      
      this.$nextTick(() => {
        const checkedKeys = this.convertSelectedToTreeKeys(this.localSelected);
        // 验证树形组件是否准备好
        if (this.$refs.lgTree && this.$refs.lgTree.setCheckedKeys) {
          this.$refs.lgTree.setCheckedKeys(checkedKeys);
          // 等待一帧后验证设置是否成功，如果失败则重试
          this.$nextTick(() => {
            const currentChecked = this.$refs.lgTree.getCheckedKeys() || [];
            const expectedCount = checkedKeys.length;
            const actualCount = currentChecked.length;
            
            // 如果选中数量不匹配，尝试重新设置（处理异步渲染问题）
            if (expectedCount > 0 && actualCount !== expectedCount) {
              setTimeout(() => {
                if (this.$refs.lgTree && this.$refs.lgTree.setCheckedKeys) {
                  this.$refs.lgTree.setCheckedKeys(checkedKeys);
                }
              }, 100);
            }
          });
        }
      });
    },

    // 为临时状态设置树形组件的选中状态
    setTreeCheckedKeysForTemp() {
      if (!this.$refs.lgTree || !this.hasSubStandards || !this.visibleDialog) return;
      this.$nextTick(() => {
        const checkedKeys = this.convertSelectedToTreeKeys(this.tempLocalSelected);
        // 验证树形组件是否准备好
        if (this.$refs.lgTree && this.$refs.lgTree.setCheckedKeys) {
          this.$refs.lgTree.setCheckedKeys(checkedKeys);
          // 等待一帧后验证设置是否成功，如果失败则重试
          this.$nextTick(() => {
            const currentChecked = this.$refs.lgTree.getCheckedKeys() || [];
            const expectedCount = checkedKeys.length;
            const actualCount = currentChecked.length;
            // 如果选中数量不匹配，尝试重新设置（处理异步渲染问题）
            if (expectedCount > 0 && actualCount !== expectedCount) {
              setTimeout(() => {
                if (this.$refs.lgTree && this.$refs.lgTree.setCheckedKeys) {
                  this.$refs.lgTree.setCheckedKeys(checkedKeys);
                }
              }, 100);
            }
          });
        }
      });
    },

    // 打开弹窗
    openDialog() {
      if (this.disabled) return;

      // 判断是否需要显示引导
      if (this.showPogTransformGuide) {
        this.$emit('showPogTransformGuide')
        return
      }

      // 如果达到限制且没有已选择项，显示警告并阻止打开弹窗
      if (this.maxSelectCount > -1 && this.localSelected.length >= this.maxSelectCount && !this.hasSelected) {
        this.$message.warning(this.limitMessage);
        return;
      }

      // 初始化临时选择状态为当前选择状态
      this.tempLocalSelected = [...this.localSelected];

      // 根据当前选择重新构建临时的selectedRubrics
      if (this.hasSubStandards) {
        // 树形结构：从当前localSelected重新构建完整的数据结构
        const tempSelectedRubrics = [];
        const processedParents = new Set();

        this.localSelected.forEach(selectedTitle => {
          // 首先检查是否为主标准标题
          const mainOption = this.rubricsOptions.find(opt => opt.title === selectedTitle);
          if (mainOption && !processedParents.has(mainOption.title)) {
            // 这是一个主标准
            tempSelectedRubrics.push({
              title: mainOption.title,
              description: mainOption.description,
              expectations: mainOption.expectations,
              subStandards: mainOption.subStandards || []
            });
            processedParents.add(mainOption.title);
          } else {
            // 检查是否为子标准
            for (const option of this.rubricsOptions) {
              if (option.subStandards && option.subStandards.length > 0) {
                const subStandard = option.subStandards.find(sub => sub.title === selectedTitle);
                if (subStandard) {
                  // 找到已存在的父级或创建新的父级
                  let parentRubric = tempSelectedRubrics.find(r => r.title === option.title);
                  if (!parentRubric) {
                    parentRubric = {
                      title: option.title,
                      description: option.description,
                      expectations: option.expectations,
                      subStandards: []
                    };
                    tempSelectedRubrics.push(parentRubric);
                    processedParents.add(option.title);
                  }

                  // 添加子标准（如果还没有的话）
                  if (!parentRubric.subStandards.some(sub => sub.title === subStandard.title)) {
                    parentRubric.subStandards.push({
                      title: subStandard.title,
                      description: subStandard.description,
                      expectations: subStandard.expectations
                    });
                  }
                  break;
                }
              }
            }
          }
        });

        this.tempSelectedRubrics = tempSelectedRubrics;
      } else {
        // 普通列表结构
        this.tempSelectedRubrics = this.rubricsOptions.filter(option =>
          this.localSelected.includes(option.title)
        );
      }
      // 打开弹窗
      this.visibleDialog = true;
      // 同步选中状态
      this.$nextTick(() => {
        this.setTreeCheckedKeysForTemp()
      });
    },
    

    
    // 确认选择
    handleConfirm() {
      // 将临时选择状态应用到正式状态
      this.localSelected = [...this.tempLocalSelected];
      
      // 通知父组件更新
      this.$emit('updateSelectedRubrics', this.tempSelectedRubrics);
      
      // 清理搜索
      this.search = '';
      
      // 关闭弹窗
      this.visibleDialog = false;
    },
    
    // 取消选择
    handleCancel() {
      // 恢复临时状态为当前正式状态
      this.tempLocalSelected = [...this.localSelected];
      
      // 清理搜索
      this.search = '';
      
      // 关闭弹窗
      this.visibleDialog = false;
    },
    
    // 在弹窗中删除校训
    removeRubricsFromDialog(title) {
      if (this.hasSubStandards) {
        // 树形结构：直接操作临时数据
        
        // 1. 从tempLocalSelected中移除该标题
        const newTempLocalSelected = this.tempLocalSelected.filter(t => t !== title);
        this.tempLocalSelected = newTempLocalSelected;
        
        // 2. 重新构建tempSelectedRubrics数据
        const newTempSelectedRubrics = [];
        const processedParents = new Set();
        
        // 遍历剩余的选中标题，重新构建数据结构
        newTempLocalSelected.forEach(selectedTitle => {
          // 首先检查是否为主标准标题
          const mainOption = this.rubricsOptions.find(opt => opt.title === selectedTitle);
          if (mainOption && !processedParents.has(mainOption.title)) {
            // 这是一个主标准
            newTempSelectedRubrics.push({
              title: mainOption.title,
              description: mainOption.description,
              expectations: mainOption.expectations,
              subStandards: mainOption.subStandards || []
            });
            processedParents.add(mainOption.title);
          } else {
            // 检查是否为子标准
            for (const option of this.rubricsOptions) {
              if (option.subStandards && option.subStandards.length > 0) {
                const subStandard = option.subStandards.find(sub => sub.title === selectedTitle);
                if (subStandard) {
                  // 找到已存在的父级或创建新的父级
                  let parentRubric = newTempSelectedRubrics.find(r => r.title === option.title);
                  if (!parentRubric) {
                    parentRubric = {
                      title: option.title,
                      description: option.description,
                      expectations: option.expectations,
                      subStandards: []
                    };
                    newTempSelectedRubrics.push(parentRubric);
                    processedParents.add(option.title);
                  }
                  
                  // 添加子标准（如果还没有的话）
                  if (!parentRubric.subStandards.some(sub => sub.title === subStandard.title)) {
                    parentRubric.subStandards.push({
                      title: subStandard.title,
                      description: subStandard.description,
                      expectations: subStandard.expectations
                    });
                  }
                  break;
                }
              }
            }
          }
        });
        
        this.tempSelectedRubrics = newTempSelectedRubrics;
        
      } else {
        // 普通列表结构：使用原有逻辑
        const newTempSelected = this.tempLocalSelected.filter(t => t !== title);
        this.tempLocalSelected = newTempSelected;
        this.tempSelectedRubrics = this.rubricsOptions.filter(option =>
          newTempSelected.includes(option.title)
        );
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .rubrics-dialog {
  .el-dialog__header {
    padding-bottom: 0;
  }
  .el-dialog__body {
    padding: 24px 20px !important;
  }
  .el-dialog__footer {
    padding-top: 0;
  }
}
.rubrics-row {
  width: 100%;

  .rubrics-select-area {
    .select-area {
      width: 100%;
      min-height: 40px;
      padding: 8px;
      border: 1px solid transparent;
      border-radius: 4px;
      cursor: pointer;
      background: #FFFFFF;

      &.is-disabled {
        cursor: auto;
        color: var(--color-text-disabled);
      }

      &.disabled-border.is-disabled {
        background-color: #F5F7FA;
        border-color: #E4E7ED;
      }

      .placeholder-text {
        line-height: 24px;
        font-size: 14px;
      }
    }
  }
}

.rubrics-selector {
  padding: 0;
  background: #FFFFFF;

  .select-all-header {
    padding-bottom: 10px;
    background-color: transparent;

    .el-checkbox {
      position: static;
      margin: 0;
    }
    /deep/ .el-checkbox__label {
      margin-top: -2px;
    }
  }

  .rubrics-list {
    max-height: 400px;
    overflow-y: auto;
    
  //   .rubrics-option {
  //     width: 100%;
  //     margin-bottom: 12px;
  //     padding: 8px;
  //     border: 1px solid #E4E7ED;
  //     border-radius: 4px;
      
  //     &:hover {
  //       border-color: #10B3B7;
  //     }
      
  //     .rubrics-content {
  //       .rubrics-title {
  //         font-weight: 600;
  //         color: #303133;
  //         margin-bottom: 4px;
  //       }
        
  //       .rubrics-desc {
  //         font-size: 12px;
  //         color: #606266;
  //         line-height: 1.4;
  //       }
  //     }
  //   }
  // }
  
  // // 树形组件样式
  // .lg-common-tree {
  //   max-height: 400px;
  //   overflow-y: auto;
  //   border: 1px solid #E4E7ED;
  //   border-radius: 4px;
  //   padding: 8px;
    
  //   /deep/ .el-tree-node__content {
  //     height: auto;
  //     min-height: 32px;
  //     padding: 4px 0;
      
  //     .el-tree-node__label {
  //       font-size: 14px;
  //       line-height: 1.4;
  //     }
  //   }
    
  //   /deep/ .el-checkbox {
  //     margin-right: 8px;
  //   }
    
  //   /deep/ .el-tree-node {
  //     .el-tree-node__children {
  //       .el-tree-node__content {
  //         padding-left: 24px;
  //         background-color: #FAFBFC;
          
  //         &:hover {
  //           background-color: #F0F9FF;
  //         }
  //       }
  //     }
  //   }
  }
}

.rubrics-option {
  display: flex;
  margin: 0;
  padding: 16px;
  border-bottom: none;

  & + .rubrics-option {
    margin-top: 6px;
  }

  /deep/ .el-checkbox__input {
    align-self: flex-start;
    margin-top: 4px;
  }

  /deep/ .el-checkbox__label {
    padding-left: 12px;
  }

  .rubrics-content {
    .rubrics-title {
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #111C1C;
      margin-bottom: 2px;
      white-space: pre-line;
      word-break: break-word;
      text-align: left;
    }

    .rubrics-desc {
      font-size: 12px;
      line-height: 18px;
      color: #666666;
      white-space: pre-line;
      word-break: break-word;
      text-align: left;
    }
  }
}

.selected-portraits {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 24px;

  .rubrics-tag-wrapper {
    display: inline-flex;

    .rubrics-tag {
      display: inline-flex;
      align-items: center;
      padding: 0 8px;
      height: 24px;
      background: #F5F7FA;
      border: 1px solid #DCDFE6;
      border-radius: 2px;

      .rubrics-tag-text {
        font-size: 12px;
        line-height: 22px;
        color: #111C1C;
        margin-right: 4px;
      }

      .el-icon-close {
        font-size: 12px;
        cursor: pointer;
        color: #999999;

        &:hover {
          color: #F56C6C;
        }
      }
    }
  }
}

/* 自定义滚动条 */
.rubrics-list {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #E4E7ED;
    border-radius: 3px;

    &:hover {
      background-color: #C0C4CC;
    }
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

/* 弹出层样式 */
/deep/ .rubrics-popover {
  padding: 0;
  border: 1px solid #DCDFE6;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-top: -8px;
}

/* Checkbox 样式覆盖 */
/deep/ .el-checkbox {
  display: flex;
  margin: 0;
  padding: 0;
  height: auto;
}

/deep/ .el-checkbox__input {
  &.is-checked {
    .el-checkbox__inner {
      background-color: #10B3B7;
      border-color: #10B3B7;
    }
  }

  &.is-indeterminate {
    .el-checkbox__inner {
      background-color: #10B3B7;
      border-color: #10B3B7;
    }
  }

  .el-checkbox__inner {
    &:hover {
      border-color: #10B3B7;
    }
  }
}

/deep/ .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #111C1C;
}
.lg-color-text-disabled {
  cursor: not-allowed;
}

/* 空数据提示样式 */
.empty-data {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  
  .empty-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    i {
      font-size: 28px;
      margin-bottom: 12px;
      color: #C0C4CC;
    }
    
    span {
      font-size: 14px;
      line-height: 20px;
    }
  }
}
</style>
