<template>
  <div
    class="lg-card-item font-size-16 font-bold display-flex justify-content-between align-items"
    :style="{ width: promptDebugging ? '33vw' : '100%' }"
  >
    <div class="display-flex align-items">
      <span>{{ $t('loc.learnerProfileFocus') }}</span>
    </div>

    <div class="display-flex align-items">
      <el-tooltip
        v-if="tipGenerateFailed && !tipLoading"
        effect="dark"
        :content="$t('loc.learnerProfileTipsFailed')"
        placement="top"
      >
        <i class="el-icon-error text-danger tips-refresh-error"></i>
      </el-tooltip>
      <el-button
        v-if="tipPractice && !tipLoading"
        @click="showTipDetails(false)"
        type="primary"
        size="medium"
      >
        <span>{{ $t('loc.unitPlannerDEI9') }}</span>
      </el-button>
      <el-button
        v-if="tipLoading"
        type="primary"
        plain
        class="tips-refresh-btn"
        icon="el-icon-refresh-right font-bold"
        :disabled="true"
        :loading="tipLoading"
      ></el-button>
      <el-button
        v-if="!tipPractice && !tipLoading"
        @click="showTipDetails(true)"
        type="primary"
        size="medium"
      >
        <span>{{ $t('loc.generate') }}</span>
      </el-button>
    </div>
    <el-dialog
      :visible.sync="tipDetailsShow"
      :before-close="closeTipDetails"
      width="80%"
      :show-close="false"
      append-to-body
      lock-scroll
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="profiles-dialog"
    >
      <div class="flex-row-between">
        <span class="font-size-20 font-bold">
          {{ $t('loc.learnerProfileFocus') }}
        </span>
        <div class="display-flex gap-24">
          <el-tooltip
            effect="dark"
            :content="$t('loc.unitPlannerStep1Regenerate')"
            placement="left"
          >
            <el-button
              type="primary"
              plain
              icon="el-icon-refresh-right font-bold"
              class="tips-refresh-btn"
              :loading="tipLoading"
              @click="regenerateTeachingTipsByUnitStream()"
            ></el-button>
          </el-tooltip>
          <i
            class="lg-icon lg-icon-close lg-dialog-close font-size-24 lg-pointer"
            @click="closeTipDetails"
          ></i>
        </div>
      </div>
      <!-- 可编辑内容 -->
      <el-form ref="unitOverviewFormRef" label-position="top">
        <el-form-item>
          <el-skeleton :rows="8" animated :loading="tipLoading && !tipPractice">
            <template>
              <div class="lg-scrollbar-hidden dei-content">
                <editor v-model="tipPractice" />
              </div>
            </template>
          </el-skeleton>
        </el-form-item>
      </el-form>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeTipDetails">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="saveTipToUnit" :disabled="tipLoading">
          {{ $t('loc.save') }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import { mapState } from 'vuex'
import { removeUnexpectedCharacters } from '@/utils/eventSource'

export default {
  props: {
    DEIBestPractice: {
      type: Object,
      default: null
    }
  },
  components: {
    Editor
  },
  data() {
    return {
      tipDetailsShow: false, // tip 模态框显示标志
      tipPractice: null, // tip 数据
      tipLoading: false, // tip生成中
      tipGenerateFailed: false, // tip 生成失败
      tipContentChanged: false, // tip 内容是否改动
      leavedPage: false, // 是否离开页面
      tipGenerateCount: 0 // tip 版本
    }
  },
  computed: {
    ...mapState({
      unitId: (state) => state.curriculum.unit.id,
      baseInfo: (state) => state.curriculum.unit.baseInfo,
      unitOverview: (state) => state.curriculum.unit.overview,
      promptDebugging: (state) => state.curriculum.promptDebugging, // Prompt 调试
      isGenieCurriculumToUnitPlanThird: (state) =>
        state.curriculum.isGenieCurriculumToUnitPlanThird, // 是否由 genie 生成的 curriculum 进入的
      unitInfo: (state) => state.curriculum.unit, // 单元基本信息
      unit: (state) => state.curriculum.unit, // 单元详情
      currentUser: (state) => state.user.currentUser // 当前用户
    }),
    tipPracticeCopy() {
      return this.unitInfo.teachingTipsForLearnerProfile
    }
  },
  watch: {
    // 监听 tip 数据,如果还未生成 tip，且是 Curriculum 生成的，则自动生成 DEI
    'unit.tipPractice'(val) {
      if (!val && this.isGenieCurriculumToUnitPlanThird) {
        // 调用 tip 生成方法
        this.generateTeachingTipsForLearnerProfileStream()
      }
    },
    // 监听 teaching tips 内容变化
    tipPractice: {
      handler(newValue, oldValue) {
        // 处理新值和旧值，去除 <p></p> 和 <br> 标签
        const processedNewValue = this.removeTags(newValue || '')
        const processedOldValue = this.unitInfo.teachingTipsForLearnerProfile
          ? this.removeTags(this.unitInfo.teachingTipsForLearnerProfile)
          : newValue
        // 只有在 tipLoading 为 false 时才监听属性值的变化
        if (!this.tipLoading) {
          if (processedNewValue !== processedOldValue) {
            // 返回true或者执行其他操作
            this.tipContentChanged = true
          } else {
            // 返回false或者执行其他操作
            this.tipContentChanged = false
          }
        }
      },
      deep: true // 深度监听对象内部值的变化
    },
    // 刷新页面时，拿到 vux 中的 tips 数据进行初始赋值
    'unitInfo.teachingTipsForLearnerProfile'(newValue, oldValue) {
      if (newValue) {
        // 更新 tip 和 tipCopy
        this.tipPractice = newValue
      }
    }
  },
  mounted() {
    // 页面切换时，将 vuex 中的 tip 数据设置到 data 中
    if (this.unitInfo.teachingTipsForLearnerProfile) {
      this.tipPractice = this.unitInfo.teachingTipsForLearnerProfile
    } else {
      this.$nextTick(() => {
        // 需要确认进度超过 40 是否生成 Ta
        if (this.baseInfo.progress <= 40) {
          // 调用 teaching tips 生成方法
          this.generateTeachingTipsForLearnerProfileStream()
        }
      })
    }
  },
  methods: {
    removeTags(str) {
      // 使用正则表达式去除 <p></p> 和 <br> 标签
      return str.replace(/<p>|<\/p>|<br>/gi, '')
    },
    open() {
      this.tipDetailsShow = true // 打开弹窗
    },
    close() {
      this.tipContentChanged = true
      this.tipDetailsShow = false // 关闭弹窗
    },
    showTipDetails(generate) {
      this.tipDetailsShow = true
      // 点击生成按钮的话开始生成 tips
      if (generate) {
        this.generateTeachingTipsForLearnerProfileStream()
      } else {
        // 点击查看校训事件
        this.$analytics.sendEvent('cg_unit_create_three_graduate_view')
      }
    },
    // 重新生成 tips
    async regenerateTeachingTipsByUnitStream() {
      // 重新生成校训埋点
      this.$analytics.sendEvent('cg_unit_create_three_graduate_pop_reg')
      this.leavedPage = false
      this.tipPractice = null
      await this.generateTeachingTipsForLearnerProfileStream()
    },
    // 获取进度值所代表的枚举值
    getProgress(progress) {
      switch (progress) {
        case 20:
          return 'INITIALIZED'
        case 40:
          return 'WEEK_OVERVIEW_GENERATED'
        case 60:
          return 'WEEK_OVERVIEW_CONFIRMED'
        case 80:
          return 'LESSON_OVERVIEW_GENERATED'
        default:
          return 'COMPLETED'
      }
    },
    // 生成 tips
    async generateTeachingTipsForLearnerProfileStream(callback) {
      // 重置数据
      this.tipLoading = true
      let params = {
        unitId: this.unitId
      }
      let data = ''
      let tipGenerateCount = this.tipGenerateCount
      // 消息回调
      let messageCallback = (message) => {
        // 如果离开页面，则不再执行
        if (this.leavedPage || tipGenerateCount !== this.tipGenerateCount) {
          data = ''
          return
        }

        this.leavedPage = false

        data += message.data

        data &&
          (this.tipPractice = removeUnexpectedCharacters(
            data
              .trim()
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 转换粗体为 <strong>
              .replace(/\n/g, '<br>')
          ))
      }
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource(
          $api.urls().generateTeachingTipsForLearnerProfileStream + '?unitId=' + this.unitId,
          null,
          messageCallback,
          'POST',
          params
        )
          .then((res) => {
            // 如果离开页面，则不再执行
            if (this.leavedPage || tipGenerateCount !== this.tipGenerateCount) {
              return
            }
            // 更新 vuex 中的 tips 信息
            this.$store.commit('curriculum/SET_TEACHING_TIPS_FOR_LEARNER_PROFILE', this.tipPractice)
            // 更新 unit 中的 teaching tips 信息
            let params = {
              unitId: this.unitId,
              ...this.unitOverview,
              ...this.baseInfo,
              progress: this.getProgress(this.baseInfo.progress),
              deiBestPractice: JSON.stringify(this.DEIBestPractice),
              teachingTipsForLearnerProfile: this.tipPractice,
            }

            // 判断 Unit 的 coverMedias 是否为空，如果不为空时将 coverMedia 的 type 设置为 UPLOAD
            if (params.coverMedias && params.coverMedias.length > 0) {
              params.coverMedias.forEach((media) => {
                media.type = 'UPLOAD'
              })
            }
            if (this.unitInfo.learnerProfileId) {
              params.learnerProfileId = this.unitInfo.learnerProfileId
              params.rubrics = this.unitInfo.rubrics

            }
            this.$axios
              .post($api.urls().updateCurriculumUnit, params)
              .then((res) => {
                // 将改变标志设置为 false
                this.tipContentChanged = false
                // 成功标志
                this.$message.success(this.$t('loc.learnerProfileFocusGeneratedSuccess'))
              })
              .catch((error) => {
                console.log(error)
                this.$message.error(error.message)
              })

            // 生成结束
            this.tipLoading = false
            if (callback && typeof callback === 'function') {
              callback()
            }
            resolve()
          })
          .catch((error) => {
            // 生成出错
            // loading 状态设置为 false
            this.tipLoading = false
            // 失败状态设置为 true
            this.tipGenerateFailed = true
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
            if (callback && typeof callback === 'function') {
              callback()
            }
            reject(error)
          })
      })
    },
    closeTipDetails() {
      // 弹窗
      if (this.tipContentChanged) {
        this.$confirm(this.$t('loc.unitPlannerDEI4'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.save'),
          cancelButtonText: this.$t('loc.noSave'),
          cancelButtonClass: 'is-plain',
          distinguishCancelAndClose: true
        })
          .then(() => {
            // 更新 unit 中的 tip 信息
            if (!this.saveTipToUnit()) {
              // 还原 tip 为更新前的状态
              this.tipPractice = this.tipPracticeCopy
            }
            // 将改变标志设置为 false
            this.tipContentChanged = false
            // 设置 dialog 为关闭状态
            this.tipDetailsShow = false
            this.tipLoading = false
            this.leavedPage = true
          })
          .catch((action) => {
            if (action === 'cancel') {
              // 还原 tip 为更新前的状态
              this.tipPractice = this.tipPracticeCopy
              // 将改变标志设置为 false
              this.tipContentChanged = false
              // 设置 dialog 为关闭状态
              this.tipDetailsShow = false
              this.tipLoading = false
              this.leavedPage = true
            }
          })
      } else if (this.tipLoading) {
        this.$confirm(this.$t('loc.unitPlannerDEI5'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain'
        })
          .then(() => {
            this.tipDetailsShow = false
            this.leavedPage = true
            this.tipGenerateCount++
            this.tipLoading = false
            this.tipPractice = this.tipPracticeCopy
          })
          .catch((action) => {
            console.log(action)
          })
      } else {
        this.tipDetailsShow = false
        this.leavedPage = true
      }
    },
    // 判断 tip 内容是否为空
    isTipContentEmpty(htmlString) {
      // 解析富文本 html 字符串
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlString, 'text/html')
      // 判断是否所有的子节点文本内容为空
      return Array.from(doc.body.childNodes)
        .map((node) => node.innerText)
        .every((text) => text.trim().length == 0)
    },
    // 更新 unit 中的 tip 信息
    saveTipToUnit() {
      // 保存校训埋点
      this.$analytics.sendEvent('cg_unit_create_three_graduate_pop_save')
      if (this.isTipContentEmpty(this.tipPractice)) {
        this.$message.error(this.$t('loc.learnerProfileFocusTip'))
        return
      }
      let params = {
        unitId: this.unitId,
        ...this.unitOverview,
        ...this.baseInfo,
        progress: this.getProgress(this.baseInfo.progress),
        teachingTipsForLearnerProfile: this.tipPractice
      }
      // 判断 Unit 的 coverMedias 是否为空，如果不为空时将 coverMedia 的 type 设置为 UPLOAD
      if (params.coverMedias && params.coverMedias.length > 0) {
        params.coverMedias.forEach((media) => {
          media.type = 'UPLOAD'
        })
      }
      if (this.unitInfo.learnerProfileId) {
        params.learnerProfileId = this.unitInfo.learnerProfileId
        params.rubrics = this.unitInfo.rubrics
      }
      this.$axios
        .post($api.urls().updateCurriculumUnit, params)
        .then((res) => {
          // 更新 tipPracticeCopy（用于判断内容变化）
          this.$message.success('Save successfully!')
          // 初始化 tip 改变标记
          this.tipContentChanged = false
          // 更新 vuex 中的 tip 信息
          this.$store.commit('curriculum/SET_TEACHING_TIPS_FOR_LEARNER_PROFILE', this.tipPractice)
          // 设置 dialog 为关闭状态
          this.tipDetailsShow = false
        })
        .catch((error) => {
          this.$message.error(error.message)
        })
      return true
    }
  }
}
</script>
<style scoped lang="scss">
.lg-card-item {
  gap: 5px;
}
::v-deep {
  .profiles-dialog {
    .el-dialog__body {
      padding: 24px;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
    .el-dialog__header {
      display: none;
    }
    .el-form-item {
      margin-bottom: 0;
    }
    .el-dialog__footer {
      padding: 24px;
      padding-top: 0;
    }
  }
}
.tips-refresh-error {
  margin-right: 3px;
}
.tips-refresh-btn {
  width: 36px;
  height: 36px;
  padding: 10px;
}
.dei-content {
  max-height: 400px;
  overflow-y: auto;
  /deep/ .ql-container.ql-snow {
    height: 367px !important;
  }
}
</style>
