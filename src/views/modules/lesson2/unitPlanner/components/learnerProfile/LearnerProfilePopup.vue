<template>
  <div>
    <el-dialog
      :title="isLessonAssistant ? $t('loc.lessonTailorTitle') : $t('loc.tailorTitle')"
      custom-class="manage-learner-profile-dialog"
      :visible.sync="addLearnerProfileDialog"
      :before-close="closeTipDetails"
      :close-on-click-modal="false"
      append-to-body
      :close-on-press-escape="false"
      lock-scroll
      top="5vh"
      width="80%"
    >
      <div
        v-if="addLearnerProfileDialog"
        class="lg-scroll-y add-padding-l-24 add-padding-r-24"
        :class="showResult ? 'add-padding-b-8' : ''"
      >
      <div class="position-relative" :class="{'loading-content-height': generateLoading}">
        <template v-if="stepOne">
          <el-input
            v-model="learnerProfile"
            :placeholder="isLessonAssistant ? $t('loc.lessonLearnerProfilePlaceholder') : $t('loc.learnerProfilePlaceholder')"
            type="textarea"
            class="profile-textarea"
            :rows="5"
          ></el-input>
          <div class="lg-margin-top-16 lg-margin-bottom-8 title-font-14">
            <span>{{ $t('loc.learnerProfileStudentExpectations') }}</span>
          </div>
          <div class="lg-margin-bottom-24 display-flex justify-content-between gap-12">
            <div class="flex-1">
              <ProfileSelectGrade
                style="width: 100%;"
                :ageGroups="ageGroups"
                @updateItemChecked="updateItemChecked"
                @updateSelectAll="updateSelectAll"
                :showWidth="true"
                :mixedAgeGroup="mixedAgeGroup"
                ref="profileSelectGrade"
              ></ProfileSelectGrade>
            </div>
            <div class="flex-1">
              <div v-if="mixedAgeGroup" class="display-flex align-items justify-content-between bg-color-F5F6F8 lg-padding-8 lg-border-radius-8">
                <span class="lg-margin-right-12">{{ $t('loc.learnerProfileGenerateStudentExpectations') }}
                  <el-tooltip placement="top" popper-class="w-450" :open-delay="500" :content="$t('loc.learnerProfileGenerateStudentExpectationsTooltip')">
                    <i class="lg-margin-left-4 lg-icon lg-icon-question"/>
                  </el-tooltip>
                </span>
                <el-switch v-model="generateExpectations"/>
              </div>
            </div>
          </div>
          <div class="lg-margin-bottom-24 display-flex justify-content-end">
            <el-button v-if="generated && false" @click="nextStep" plain>{{ $t('loc.next')}}</el-button>
            <el-tooltip popper-class="w-450" :content="$t('loc.learnerProfileGenerateTip', {grade: pendingGenerateGrade})" placement="top" v-model="showTip" :manual="true">
              <el-button
                type="primary"
                class="ai-btn"
                :loading="generateLoading"
                :disabled="!learnerProfileTrim || !checkedAgeGroup.length || saveLoading"
                @click="generateLearnerProfile"
              >
                {{ generated ? $t('loc.unitPlannerStep1Regenerate') : $t('loc.generate') }}
              </el-button>
            </el-tooltip>
          </div>
        </template>
        <template v-if="stepTwo">
          <div
            class="el-textarea__inners add-margin-t-12"
            style="padding-bottom: 0;"
          >
            <div v-if="isEmptyContent" class="add-margin-t-12">
              <el-input
                readonly
                v-model="rubricsData"
                :autosize="true"
                type="textarea"
                class="age-rubric"
              ></el-input>
            </div>
            <div v-else>
              <!-- 表格内容 -->
              <div v-if="ageGroupRubrics.length > 0">
                <el-skeleton
                  :rows="3"
                  animated
                  :loading="generateLoading && !ageGroupRubrics[0].ageGroupRubrics && !ageGroupRubrics[0].ageGroup"
                >
                  <template>
                    <!-- 显示刷新按钮 -->
                    <div v-if="flatRows.length > 0" class="review-header">
                      <span>{{ $t('loc.learnerProfileCreateTips') }}</span>
                      <div class="display-flex">
                        <!-- 返回上一步 -->
                        <el-button v-if="!stepOne" @click="previousStep" type="primary" size="medium" :disabled="generateLoading" class="display-flex align-items">
                          <div class="display-flex align-items">
                            <svg class="lg-margin-right-4" width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M16.2266 9.75488L12.8457 11.9639C11.7243 12.6964 10.2758 12.6963 9.1543 11.9639L5.77344 9.75488L5 9.25V14.6699L5.13867 14.8154L5.28516 14.9619C5.51819 15.1856 5.87504 15.4864 6.36523 15.7881C7.34518 16.3912 8.86295 17.001 11 17.001C13.1371 17.001 14.6548 16.3912 15.6348 15.7881C16.1249 15.4864 16.4818 15.1856 16.7148 14.9619C16.7709 14.9081 16.82 14.8586 16.8613 14.8154L17 14.6699V9.25L16.2266 9.75488ZM12.5518 1.54297C11.6062 0.93683 10.3938 0.93683 9.44824 1.54297L2.36035 6.08594L1.70996 6.50293L2.35742 6.92578L9.42773 11.5449C10.383 12.169 11.617 12.169 12.5723 11.5449L19.6436 6.92578L20.29 6.50293L19.6396 6.08594L12.5518 1.54297ZM4.5 8.92285L4.27344 8.77539L2.27344 7.46875L1.5 6.96289V13.251C1.49996 13.3891 1.38808 13.501 1.25 13.501C1.11192 13.501 1.00004 13.3891 1 13.251V6.50098L1.00879 6.43652C1.02531 6.37425 1.06538 6.32074 1.12012 6.28711L1.12793 6.28223L9.17871 1.12109C10.2886 0.409773 11.7115 0.409769 12.8213 1.12109L20.8848 6.29004C20.9561 6.33581 20.9996 6.41521 21 6.5C21.0003 6.58471 20.9576 6.66357 20.8867 6.70996L17.7266 8.77539L17.5 8.92285V14.751C17.5 14.8038 17.483 14.855 17.4521 14.8975L17.4502 14.9004L17.4463 14.9053L17.4443 14.9072L17.4395 14.9141C17.4368 14.9175 17.434 14.9216 17.4307 14.9258L17.4287 14.9287C17.4277 14.9299 17.4254 14.9315 17.4229 14.9346L17.4219 14.9365C17.4047 14.9577 17.3809 14.9868 17.3496 15.0225L17.3486 15.0234C17.2845 15.0968 17.189 15.1998 17.0615 15.3223C16.8062 15.5673 16.4211 15.8911 15.8965 16.2139C14.8451 16.8608 13.2378 17.501 11 17.501C8.7622 17.501 7.15489 16.8608 6.10352 16.2139C5.57892 15.8911 5.19377 15.5673 4.93848 15.3223C4.81098 15.1999 4.7155 15.0968 4.65137 15.0234L4.65039 15.0225L4.55371 14.9062C4.55475 14.9077 4.55718 14.9107 4.55957 14.9141C4.56115 14.9163 4.56668 14.9241 4.57324 14.9346C4.57615 14.9392 4.58336 14.9518 4.5918 14.9678C4.59596 14.9757 4.60428 14.9904 4.6123 15.0098C4.61732 15.0219 4.63695 15.0706 4.64551 15.1367C4.64973 15.1694 4.65668 15.2411 4.63379 15.3281C4.6138 15.404 4.5678 15.4971 4.48242 15.5732C4.45651 15.547 4.43103 15.523 4.4082 15.499L4.27441 15.3525C4.23559 15.3082 4.19811 15.2625 4.16211 15.2158L4.5498 14.9004C4.51758 14.8573 4.50002 14.8049 4.5 14.751V8.92285Z" fill="white" stroke="white"/>
                            </svg>
                            Update PoG
                          </div>
                        </el-button>
                        <el-tooltip :content="$t('loc.regenerate')" placement="top" :open-delay="500">
                          <el-button size="medium" type='primary' plain  class="refresh-button" v-if="!stepOne" :disabled="generateLoading"
                            @click="generateLearnerProfileExpectations()">
                            <img :src="getRegenerateButtonIcon()" alt="">
                          </el-button>
                        </el-tooltip>
                      </div>
                    </div>

                    <!-- 统一表格显示所有年龄组 -->
                    <div v-if="flatRows.length > 0"
                         class="rubrics-table"
                         v-loading="tableLoading">
                      <el-table
                        :data="flatRows"
                        :span-method="spanMethod"
                        ref="rubricsTable"
                        border
                        max-height="400"
                        style="width: 100%">

                        <el-table-column prop="attributeTitle" :label="$t('loc.learnerProfileAttributes')" fixed width="150">
                          <template slot-scope="scope">
                            <el-input
                              v-model="scope.row.attributeTitle"
                              :placeholder="$t('loc.learnerProfileAttributePlaceholder')"
                              type="textarea"
                              maxlength="50"
                              :autosize="{ minRows: 1, maxRows: 10 }"
                              :class="{'error-input-wrapper': isErrorInput(scope.row.attributeIndex, -1, 'attribute')}"
                              @input="(val) => onAttributeChange(scope.row.attributeIndex, 'title', val)"
                            ></el-input>
                          </template>
                        </el-table-column>

                        <el-table-column v-if="hasSubStandards" prop="standardTitle" :label="$t('loc.learnerProfileStandards')" fixed width="150">
                          <template slot-scope="scope">
                            <el-input
                              v-model="scope.row.standardTitle"
                              :placeholder="$t('loc.learnerProfileStandardsPlaceholder')"
                              type="textarea"
                              maxlength="50"
                              :autosize="{ minRows: 1, maxRows: 10 }"
                              :class="{'error-input-wrapper': isErrorInput(scope.row.attributeIndex, scope.row.standardIndex, 'standard')}"
                              @input="(val) => onStandardChange(scope.row.attributeIndex, scope.row.standardIndex, 'title', val)"
                            ></el-input>
                          </template>
                        </el-table-column>

                        <el-table-column prop="description" :label="$t('loc.description')" width="300">
                          <template slot-scope="scope">
                            <el-input
                              v-model="scope.row.description"
                              :placeholder="$t('loc.description')"
                              type="textarea"
                              maxlength="500"
                              :autosize="{ minRows: 1, maxRows: 10 }"
                              @input="(val) => onStandardChange(scope.row.attributeIndex, scope.row.standardIndex, 'description', val)"
                            ></el-input>
                          </template>
                        </el-table-column>

                        <!-- 为每个年龄组创建一个期望列 -->
                        <el-table-column
                          v-for="ageGroup in ageGroupRubrics" 
                          :key="ageGroup.ageGroup"
                          :prop="`expectations.${ageGroup.ageGroup}`" 
                          min-width="300">
                          <template slot="header" slot-scope="scope">
                            <div class="display-flex align-items gap-8">
                              <span>{{ $t('loc.learnerProfileExpectations') }} ({{ ageGroup.ageGroup }})</span>
                              <el-tooltip :content="$t('loc.regenerate')" placement="top" :open-delay="500">
                                <el-button size="mini" type='primary' plain class="refresh-button"  v-if="!generateLoading"
                                  @click="generateLearnerProfileExpectations(ageGroup.ageGroup)">
                                  <img :src="getRegenerateButtonIcon()" alt="">
                                </el-button>
                              </el-tooltip>
                              <!-- 删除期望按钮，至少要保留一个年龄组 -->
                              <el-tooltip :content="$t('loc.learnerProfileDelete')" placement="top" :open-delay="500" v-if="!generateLoading">
                                <i
                                  v-if="ageGroupRubrics.length > 1"
                                  class="el-icon-close delete-expectation-icon"
                                  @click="removeExpectation(ageGroup.ageGroup)"
                                ></i>
                              </el-tooltip>
                            </div>
                          </template>
                          <template slot-scope="scope">
                            <div style="display: flex; align-items: center;">
                              <!-- 期望骨架屏 -->
                              <el-skeleton 
                                v-if="generateLoading && !scope.row.expectations[ageGroup.ageGroup]"
                                :rows="3" 
                                animated
                                style="width: 100%;">
                              </el-skeleton>
                              <!-- 期望输入框 -->
                              <el-input
                                v-else
                                v-model="scope.row.expectations[ageGroup.ageGroup]"
                                :placeholder="`${$t('loc.learnerProfileExpectations')} (${ageGroup.ageGroup})`"
                                type="textarea"
                                maxlength="500"
                                :autosize="{ minRows: 1, maxRows: 10 }"
                                @input="(val) => onExpectationChange(scope.row.attributeIndex, scope.row.standardIndex, ageGroup.ageGroup, val)"
                              ></el-input>
                            </div>
                          </template>
                        </el-table-column>
                        <!-- 操作列 -->
                        <el-table-column width="65" fixed="right">
                          <template slot-scope="scope">
                              <!-- 添加标准按钮 - 只在有子标准时显示 -->
                              <el-dropdown
                                v-if="hasSubStandards && scope.row.standardIndex >= 0"
                                trigger="hover"
                                :disabled="generateLoading"
                                placement="bottom-end"
                                @command="handleAddStandardCommand"
                              >
                                <el-button type="text" :disabled="generateLoading">
                                  <i class="el-icon-plus add-icon"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                  <el-dropdown-item 
                                    :command="{type: 'above', attributeIndex: scope.row.attributeIndex, standardIndex: scope.row.standardIndex}"
                                  >
                                    <span class="lg-color-text-primary">
                                      {{ $t('loc.learnerProfileInsertRowAbove') }}
                                    </span>
                                  </el-dropdown-item>
                                  <el-dropdown-item 
                                    :command="{type: 'below', attributeIndex: scope.row.attributeIndex, standardIndex: scope.row.standardIndex}"
                                  >
                                    <span class="lg-color-text-primary">
                                      {{ $t('loc.learnerProfileInsertRowBelow') }}
                                    </span>
                                  </el-dropdown-item>
                                </el-dropdown-menu>
                              </el-dropdown>

                              <!-- 添加属性按钮 - 只在没有子标准时显示 -->
                              <el-dropdown
                                v-if="!hasSubStandards && scope.row.standardIndex === -1"
                                trigger="hover"
                                :disabled="generateLoading"
                                placement="bottom-end"
                                @command="handleAddAttributeCommand"
                              >
                                <el-button type="text" :disabled="generateLoading">
                                  <i class="el-icon-plus add-icon"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                  <el-dropdown-item 
                                    :command="{type: 'above', attributeIndex: scope.row.attributeIndex}"
                                  >
                                    <span class="lg-color-text-primary">
                                      {{ $t('loc.learnerProfileInsertRowAbove') }}
                                    </span>
                                  </el-dropdown-item>
                                  <el-dropdown-item 
                                    :command="{type: 'below', attributeIndex: scope.row.attributeIndex}"
                                  >
                                    <span class="lg-color-text-primary">
                                      {{ $t('loc.learnerProfileInsertRowBelow') }}
                                    </span>
                                  </el-dropdown-item>
                                </el-dropdown-menu>
                              </el-dropdown>

                              <!-- 删除标准按钮 - 在有子标准时显示 -->
                              <el-tooltip :content="$t('loc.learnerProfileDelete')" placement="top" :open-delay="500">
                                <el-button type="text" v-if="hasSubStandards && flatRows.length > 1"
                                  :disabled="generateLoading"
                                  @click="removeSubStandard(scope.row.attributeIndex, scope.row.standardIndex)"
                                  >
                                  <i class="el-icon-close delete-icon" />
                                </el-button>
                              </el-tooltip>

                              <!-- 删除属性按钮 - 只在没有子标准时显示 -->
                              <el-tooltip :content="$t('loc.learnerProfileDelete')" placement="top" :open-delay="500">
                                <el-button type="text" v-if="!hasSubStandards && scope.row.standardIndex === -1 && flatRows.length > 1"
                                  :disabled="generateLoading"
                                  @click="removeAttribute(scope.row.attributeIndex)"
                                  >
                                  <i class="el-icon-close delete-icon" />
                                </el-button>
                              </el-tooltip>
                          </template>
                        </el-table-column>
                      </el-table>
                      <div class="add-row-cell">
                        <el-button type="text" size="mini" class="add-row-btn" @click="addAttribute" :disabled="generateLoading">
                          <i class="el-icon-plus add-icon"></i> {{ $t('loc.learnerProfileAddAttribute') }}
                        </el-button>
                      </div>
                    </div>
                    <!-- 当没有内容时显示空白页 -->
                    <div v-else class="empty-content-container">
                      <div class="empty-content" v-if="!generateLoading && !closeLoading">
                        <div class="empty-icon">
                         <img src="@/assets/img/lesson2/add-empty.png" alt="empty-content">
                        </div>
                        <div class="empty-text">{{ $t('loc.notGenerated') }}</div>
                        <el-button
                            type="primary"
                            class="ai-btn empty-generate-btn"
                            :disabled="!learnerProfileTrim"
                            :class="{'is-disabled': !learnerProfileTrim}"
                            @click="generateLearnerProfile"
                          >
                            {{ $t('loc.generate') }}
                        </el-button>
                      </div>
                    </div>
                  </template>
                </el-skeleton>
              </div>
            </div>
          </div>
        </template>
        <div class="loading-content" v-if="generateLoading">
          <div class="loading-content-wrapper">
            <img class="image-container" src="~@/assets/img/lesson2/unitPlanner/unit_loading.png"/>
            <div class="loader-container">
              <div class="loader"></div>
            </div>
          <div class="loading-text">Hold tight! The magic is happening...</div>
        </div>
      </div>
      </div>
      </div>
      <div class="footer-btns" v-if="showResult && stepTwo && !generateLoading">
        <!-- 修改记录 -->
        <div
          v-if="agencyLearnerProfile && (agencyLearnerProfile.editRecords || []).length > 0"
          class="color-676879"
          style="width: fit-content;"
        >
          <el-popover
            placement="bottom-start"
            :disabled="agencyLearnerProfile.editRecords.length < 2"
            trigger="manual"
            ref="historyPopover"
            popper-class="curriculum-popover"
            v-model="historyPopoverVisible"
          >
            <div class="lg-scroll-y lg-pa-16 max-height-300">
              <div
                v-for="(action, index) in agencyLearnerProfile.editRecords"
                :key="index"
                style="color: #676879;"
              >
                <span
                  v-if="index != 0"
                  class="text-ellipsis"
                  :title="actionContent(action)"
                  :class="
                    index === 0 && index === agencyLearnerProfile.editRecords.length - 1
                      ? ''
                      : 'lg-padding-bottom-8'
                  "
                >
                  {{ actionContent(action) }}
                </span>
              </div>
            </div>

            <span
              class="text-muted lg-pointer text-ellipsis"
              style="color: #676879;"
              :title="actionContent(agencyLearnerProfile.editRecords[0])"
              slot="reference"
              @click="historyPopoverVisible = !historyPopoverVisible"
            >
              {{ actionContent(agencyLearnerProfile.editRecords[0]) }}
              <i
                class="el-icon-arrow-down m-l-xs"
                v-if="agencyLearnerProfile.editRecords.length > 2"
                :style="historyPopoverVisible ? 'transform: rotate(180deg)' : ''"
              ></i>
            </span>
          </el-popover>
        </div>
        <div class="lg-padding-top-16 lg-padding-bottom-24 display-flex justify-content-between">
          <div>
            <!-- 按照年龄组重新生成期望 -->
            <el-button v-if="!mixedAgeGroup" plain :disabled="generateLoading" @click="regenerateByGradeBand">{{ $t('loc.learnerProfileRenerateByGradeBands') }}</el-button>
          </div>
          <div>
            <el-button @click="closeTipDetails" :disabled="saveLoading || generateLoading" plain>
              {{ $t('loc.cancel') }}
            </el-button>
            <el-button
              type="primary"
              @click="handleSave()"
              :loading="saveLoading"
              :disabled="generateLoading"
            >
              {{ $t('loc.save') }}
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { TAILOR_UNIT_AGE_GROUPS, TAILOR_UNIT_MIXED_AGE_GROUPS } from '@/utils/const'
import { equalsIgnoreCase } from '@/utils/common'
import ProfileSelectGrade from './ProfileSelectGrade.vue'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import { mapState } from 'vuex'
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import tools from "@/utils/tools"
import regenerateIcon from '@/assets/img/lesson2/plan/regenerate.svg'

export default {
  components: {
    ProfileSelectGrade,
    Editor
  },
  props: {
    isLessonAssistant: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      agencyLearnerProfile: (state) => state.lesson.agencyLearnerProfile,
      guideFeatures: state => state.common.guideFeatures // 功能引导
    }),
    learnerProfileTrim() {
      // 去除首尾空格
      if (!this.learnerProfile) {
        return ''
      }
      let trimText = this.learnerProfile.trim()
      return trimText
    },
    // 当前选中的年龄组
    checkedAgeGroup() {
      return this.ageGroups.filter((v) => v.selected).map((v) => v.ageGroup)
    },

    // 扁平化数据用于表格显示
    flatRows() {
      // 添加更严格的检查和错误处理
      if (!this.ageGroupRubrics || this.ageGroupRubrics.length === 0) {
        return [];
      }

      // 使用第一个年龄组作为基础结构（所有年龄组的 attributes 和 subStandards 应该一致）
      const baseGroup = this.ageGroupRubrics[0];
      if (!baseGroup.details || !Array.isArray(baseGroup.details)) {
        return [];
      }

      const rows = [];
      baseGroup.details.forEach((attribute, attrIndex) => {
        if (attribute && attribute.subStandards && attribute.subStandards.length > 0) {
          // 有子标准的情况
          attribute.subStandards.forEach((standard, standardIndex) => {
            const row = {
              attributeIndex: attrIndex,
              standardIndex: standardIndex,
              attributeTitle: attribute.title || '',
              standardTitle: standard.title || '',
              description: standard.description || '',
              isFirstRowOfAttribute: standardIndex === 0,
              attributeRowSpan: attribute.subStandards.length,
              expectations: {} // 存储所有年龄组的期望
            };

            // 为每个年龄组添加期望数据
            this.ageGroupRubrics.forEach(ageGroup => {
              if (ageGroup.details && 
                  ageGroup.details[attrIndex] && 
                  ageGroup.details[attrIndex].subStandards && 
                  ageGroup.details[attrIndex].subStandards[standardIndex]) {
                row.expectations[ageGroup.ageGroup] = 
                  ageGroup.details[attrIndex].subStandards[standardIndex].expectations || '';
              } else {
                row.expectations[ageGroup.ageGroup] = '';
              }
            });

            rows.push(row);
          });
        } else if (attribute) {
          // 没有子标准的情况，直接使用属性本身的数据
          const row = {
            attributeIndex: attrIndex,
            standardIndex: -1, // 使用-1表示这不是子标准
            attributeTitle: attribute.title || '',
            standardTitle: '',
            description: attribute.description || '',
            isFirstRowOfAttribute: true,
            attributeRowSpan: 1,
            expectations: {} // 存储所有年龄组的期望
          };

          // 为每个年龄组添加期望数据
          this.ageGroupRubrics.forEach(ageGroup => {
            if (ageGroup.details && ageGroup.details[attrIndex]) {
              row.expectations[ageGroup.ageGroup] = ageGroup.details[attrIndex].expectations || '';
            } else {
              row.expectations[ageGroup.ageGroup] = '';
            }
          });

          rows.push(row);
        }
      });
      return rows;
    },
    // 检查当前年龄组是否有子标准
    hasSubStandards() {
      if (!this.ageGroupRubrics || this.ageGroupRubrics.length === 0) {
        return false;
      }

      // 使用第一个年龄组作为基础结构检查
      const baseGroup = this.ageGroupRubrics[0];
      if (!baseGroup.details) {
        return false;
      }

      // 检查是否有任何属性包含多个子标准，或者子标准的标题不为空
      return baseGroup.details.some(attribute => {
        if (!attribute.subStandards || attribute.subStandards.length === 0) {
          return false;
        }

        // 如果有多个子标准，则显示标准列
        if (attribute.subStandards.length > 0) {
          return true;
        }
      });
    },

  },
  data() {
    return {
      historyPopoverVisible: false, // 历史记录弹窗
      stepOne: true, // 是否显示第一步
      stepTwo: false, // 是否显示第二步
      rubricsBackup: null, // 备份的评分标准
      saveLoading: false, // 保存中
      addLearnerProfileDialog: false, // 新增校训弹窗
      selectedGrade: '', // 选中的年龄段
      learnerProfile: '', // 校训
      pendingGenerateGrade: '', // 待生成校训年龄段
      showTip: false, // 显示提示语
      ageGroups: [], // 年龄段
      ageGroupRubrics: [], // 年龄段评分标准
      mixedAgeGroup: false, // 是否混合年龄段
      generateExpectations: true, // 是否显示期望
      generated: false, // 是否已生成
      generateLoading: false, // 生成中
      showResult: false, // 是否显示结果
      rubricsData: '',
      ageGroupRubricsCopy: '',
      snapshot: {
        learnerProfile: '',
      }, // 快照数据
      isEmptyContent: false,
      observer: null, // 保存 MutationObserver 实例
      leavedPage: false, // 是否离开页面
      streamVersion: 0, // learner profile 版本
      tableValidationError: false,
      tableLoading: false,
      hasModified: false, // 标记是否有修改但还未保存到服务器
      cacheAgeGroups: {}, // 缓存年龄组内容
      closeLoading: false, // 关闭 loading
      errorInputPosition: null, // 记录错误输入框位置
    }
  },
  watch: {
    agencyLearnerProfile: {
      immediate: true,
      deep: true,
      handler() {
        this.initData()
      }
    },
    addLearnerProfileDialog: {
      immediate: true,
      deep: true,
      handler(newValue) {
        let records = this.agencyLearnerProfile.editRecords || []
        if (newValue && records.length > 2) {
          document.removeEventListener('click', this.handleClickOutside)
          document.addEventListener('click', this.handleClickOutside)
        }
        if (!newValue && records.length > 2) {
          document.removeEventListener('click', this.handleClickOutside)
        }
      }
    }
  },
  mounted() {
    this.stepTwo = this.ageGroupRubrics.length > 0;
    this.stepOne = !this.stepTwo
    
    // 初始化防抖方法
    this.debouncedResizeTableAndTextareas = tools.debounce(this.resizeTableAndTextareas, 200)
    this.debouncedSortAgeGroupRubrics = tools.debounce(this.sortAgeGroupRubrics, 300)
  },
  methods: {
    // 获取重新生成的图标
    getRegenerateButtonIcon() {
      // 如果生成过展示重新生成图标
      return regenerateIcon
    },
    handleClickOutside(event) {
      let records = this.agencyLearnerProfile.editRecords || []
      if (records.length > 2 && this.addLearnerProfileDialog) {
        // 检查点击是否在 el-popover 内部
        const popover = this.$refs.historyPopover && this.$refs.historyPopover.$el
        if (popover && !popover.contains(event.target)) {
          this.historyPopoverVisible = false // 如果点击在外部，关闭弹窗
        }
      }
    },
    // 初始化数据
    initData() {
      // 解构赋值
      let { id, learnerProfile = '', ageGroupRubrics = [], newAgeGroupRubrics = [], mixedAgeGroup = false, generateExpectations = true } = JSON.parse(
        JSON.stringify(this.agencyLearnerProfile)
      )
      this.mixedAgeGroup = mixedAgeGroup
      this.generateExpectations = generateExpectations
      // 如果没有 id 或者没有校训，则不处理,直接返回
      if (!id) {
        this.generated = false
        this.stepOne = true
        this.stepTwo = false
        this.updateSelectAll(true)
        return
      }
      // 初始化数据
      this.learnerProfile = learnerProfile
      this.ageGroupRubrics = newAgeGroupRubrics
      this.stepTwo = this.ageGroupRubrics.length > 0;
      this.stepOne = !this.stepTwo
      // 初始化details结构，保持原有数据结构不变
      this.ageGroupRubrics.forEach((group, index) => {
        if (!group.details) {
          this.$set(group, 'details', []);
        }
        // 不再强制为每个detail创建subStandards，保持原有数据结构
      });
      // 初始化年龄组
      this.ageGroups = JSON.parse(JSON.stringify(this.mixedAgeGroup ? TAILOR_UNIT_MIXED_AGE_GROUPS : TAILOR_UNIT_AGE_GROUPS)).map((item) => {
        return {
          ...item,
          selected: newAgeGroupRubrics.some((v) => v.ageGroup === item.ageGroup)
        }
      })
      // 清理所有年龄组中的空白行
      this.cleanAllEmptyRows();

      // 排序年龄组
      this.sortAgeGroupRubrics()
      this.generated = true
      this.generateLoading = false
      this.showResult = true
      this.snapshot = {
        originalLearnerProfile: this.learnerProfile,
        learnerProfile: this.learnerProfile,
      }
      this.ageGroupRubricsCopy = JSON.parse(JSON.stringify(this.ageGroupRubrics))

      // 重置 textarea 高度, 防止二次打开出现滚动问题
      this.$nextTick(() => {
        // 初始化数据后调整评分标准 textarea 高度
        setTimeout(() => {
          this.resizeTableAndTextareas()
        }, 50);
      })
    },

    // 找出组件中所有 textarea 组件，并调用 resizeTextarea 方法
    findAndResizeTextarea(components) {
      components.forEach(component => {
        // 如果是 SlidesImageReplacer 组件，调用其 handleCloseGuide 方法
        if (component.$options.name === 'ElInput' && typeof component.resizeTextarea === 'function') {
          component.resizeTextarea()
        }
        // 递归查找子组件
        if (component.$children && component.$children.length > 0) {
          this.findAndResizeTextarea(component.$children)
        }
      })
    },

    // 调整所有 textarea 高度
    resizeTableAndTextareas() {
      setTimeout(() => {
        this.$nextTick(() => {
          // 先 resize textarea
          this.findAndResizeTextarea(this.$children)
          // 再重新布局表格
          this.$refs.rubricsTable &&this.$refs.rubricsTable.doLayout()
        });
      }, 100)
    },
    
    // 防抖的 resizeTableAndTextareas 方法
    debouncedResizeTableAndTextareas: null,
    
    // 防抖的 sortAgeGroupRubrics 方法
    debouncedSortAgeGroupRubrics: null,

    // 更新全选状态
    updateSelectAll(value) {
      this.ageGroups = this.ageGroups.map(item => {
        return {
          ...item,
          selected: value
        }
      })
      let newSelectedAgeGroups = this.ageGroups.filter(v => v.selected).map(v => v.ageGroup)
      let deletedAgeGroups = this.ageGroupRubrics.filter(item => !newSelectedAgeGroups.includes(item.ageGroup)).map(item => item.ageGroup)
      this.handleGroupExpectations(newSelectedAgeGroups, deletedAgeGroups)
    },  

    // 更新单项
    updateItemChecked(item) {
      // 先进行验证检查，如果有问题直接返回
      const targetAgeGroup = this.ageGroups.find(v => item.ageGroup === v.ageGroup);
      if (!targetAgeGroup) return;

      // 更新 ageGroups
      const v = this.ageGroups.find(group => group.ageGroup === item.ageGroup);
      if (v) {
        v.selected = !v.selected;
      }
      let newAgeGroupRubrics = []
      let deletedAgeGroups = []
      if (item.selected) {
        newAgeGroupRubrics = [item.ageGroup]
      } else {
        deletedAgeGroups = [item.ageGroup]
      }
      this.handleGroupExpectations(newAgeGroupRubrics, deletedAgeGroups)
    },

    // 更新记录内容
    actionContent(action) {
      if (!action) {
        return ''
      }
      let operation = 'Updated by '
      if ('CREATE' === action.type) {
        operation = 'Created by '
      } else if ('DELETE' === action.type) {
        operation = 'Deleted by '
      } else if ('RESTORE' === action.type) {
        operation = 'Restored by '
      }
      let userName = action.userName
      let utcDate = action.updateAtUtc
      let date = this.$moment
        .utc(utcDate)
        .local()
        .format('MMM DD, YYYY')
      let time = this.$moment
        .utc(utcDate)
        .local()
        .format('hh:mm a')
      return operation + userName + ' on ' + date + ' at ' + time
    },
    // 重新打开弹窗重置数据
    resetData() {
      this.learnerProfile = ''
      this.showResult = false
      this.ageGroupRubricsCopy = []
      this.ageGroupRubrics = []
      this.leavedPage = false
      this.ageGroups = JSON.parse(JSON.stringify(this.mixedAgeGroup ? TAILOR_UNIT_MIXED_AGE_GROUPS : TAILOR_UNIT_AGE_GROUPS)).map((v) => {
        return {
          ...v,
          selected: false
        }
      })
      this.rubricsData = ''
      this.isEmptyContent = false
      this.hasModified = false; // 重置修改标记
      this.cacheAgeGroups = {}; // 重置缓存的年龄组
      this.errorInputPosition = null; // 重置错误位置状态
    },
    async open (transformData = false) {
      // 打开校训设置弹窗埋点
      this.$analytics.sendEvent('cg_unit_graduate_pop')
      // 打开弹窗时重置数据
      this.resetData()
      await this.$store.dispatch('getAgencyLearnerProfile')
      this.initData()

      // 确保清理所有空白行
      this.cleanAllEmptyRows();

      this.addLearnerProfileDialog = true

      if (transformData) {
        this.regenerateLearnerProfileByAgeGroup()
      } else {
        this.$nextTick(() => {
          this.showTransFormGuide()
        })
      }

    },

    // 显示引导
    showTransFormGuide() {
      // 如果是旧数据，且未引导过，则展示引导
      if (this.guideFeatures && this.guideFeatures.showPogTransformGuide && !this.mixedAgeGroup) {
        this.$confirm(
          this.$t('loc.learnerProfileGenerateByGradeBandsTip'),
          this.$t('loc.learnerProfileGenerateByGradeBandsTitle'),
          {
            confirmButtonText: this.$t('loc.unitPlannerConfirmRegenerate'),
            cancelButtonText: this.$t('loc.close'),
            closeOnClickModal: false,
            distinguishCancelAndClose: true,
            customClass: 'width-600-confirm-message-box'
          }
        ).then(() => {
          this.regenerateLearnerProfileByAgeGroup()
        })
        .finally(() => {
          let result = { 'features': ['POG_TRANSFORM_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then((res) => {
            this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
              ...this.guideFeatures,
              showPogTransformGuide: false
            })
          })
        })
      }
    },

    // 按照年龄段重新生成校训
    regenerateLearnerProfileByAgeGroup() {
      this.ageGroups = JSON.parse(JSON.stringify(TAILOR_UNIT_MIXED_AGE_GROUPS)).map(item => {
        return {
          ...item,
          selected: true
        }
      })
      this.mixedAgeGroup = true
      this.generateExpectations = true
      this.generateLearnerProfileStream()
    },

    // 上一步
    previousStep() {
      this.stepOne = true
      this.stepTwo = true
      this.snapshot = {
        originalLearnerProfile: this.snapshot && this.snapshot.originalLearnerProfile,
        learnerProfile: this.learnerProfile,
        selectedAgeGroups: this.ageGroupRubrics.map(item => item.ageGroup),
        generateExpectations: this.generateExpectations,
      }
      this.rubricsBackup = this.backupExpectations()
    },

    // 下一步
    nextStep() {
      // 检查校训内容是否为空
      if (!this.learnerProfileTrim) {
        this.$message.error(this.$t('loc.pleaseProceeding'));
        return;
      }
      if (this.checkedAgeGroup.length === 0) {
        // 如果没有任何需要生成的年龄组，提示用户
        this.$message.warning(this.$t('Please select at least one grade to generate'));
        return;
      }

      // 判断是否有 learnerProfile 变化
      const hasLearnerProfileChanged = this.learnerProfile !== this.snapshot.learnerProfile;
      // 判断期望开关是否从关闭到打开
      const expectationsOpenChanged = this.generateExpectations && this.generateExpectations !== this.snapshot.generateExpectations;

      // 如果 learnerProfile 有变化，使用原方法重新生成全部内容
      if (hasLearnerProfileChanged) {
        this.$confirm(
          this.$t('loc.learnerProfileSaveTips'),
          this.$t('loc.confirmation'),
          {
            confirmButtonText: this.$t('loc.regenerate'),
            cancelButtonText: this.$t('loc.learnerProfileOnlySave'),
            closeOnClickModal: false,
            distinguishCancelAndClose: true,
            customClass: 'width-600-confirm-message-box'
          }
        ).then(() => {
          this.generateLearnerProfileStream();
        })
        .catch(action => {
          if (action === 'cancel') {
            this.snapshot = {
              originalLearnerProfile: this.snapshot && this.snapshot.originalLearnerProfile,
              learnerProfile: this.learnerProfile,
              selectedAgeGroups: this.ageGroupRubrics.map(item => item.ageGroup),
            }
            this.stepTwo = true
          }
        });
      } else {
        // 如果校训内容没有变化，判断是否新增了年龄组
        let newSelectedAgeGroups = this.checkedAgeGroup.filter(group => !this.ageGroupRubrics.map(item => item.ageGroup).includes(group))
        // 删掉的年龄组
        let deletedAgeGroups = this.ageGroupRubrics.filter(item => !this.checkedAgeGroup.includes(item.ageGroup)).map(item => item.ageGroup)
        if (newSelectedAgeGroups.length > 0 && this.generateExpectations) {
          this.$confirm(
            this.$t('loc.learnerProfileSaveTips'),
            this.$t('loc.confirmation'),
            {
              confirmButtonText: this.$t('loc.regenerate'),
              cancelButtonText: this.$t('loc.learnerProfileOnlySave'),
              closeOnClickModal: false,
              distinguishCancelAndClose: true,
              customClass: 'width-600-confirm-message-box'
            }
          ).then(() => {
            this.handleGroupExpectations(newSelectedAgeGroups, deletedAgeGroups)
            if (this.mixedAgeGroup) {
              this.generateLearnerProfileExpectations(newSelectedAgeGroups);
            } else {
              this.generateAppendLearnerProfileStream(newSelectedAgeGroups);
            }
          })
          .catch(action => {
            if (action === 'cancel') {
              this.handleGroupExpectations(newSelectedAgeGroups, deletedAgeGroups)
              this.stepTwo = true
            }
          });
        } else if (expectationsOpenChanged) {
          this.$confirm(
            this.$t('loc.learnerProfileSaveTips'),
            this.$t('loc.confirmation'),
            {
              confirmButtonText: this.$t('loc.regenerate'),
              cancelButtonText: this.$t('loc.learnerProfileOnlySave'),
              closeOnClickModal: false,
              distinguishCancelAndClose: true,
              customClass: 'width-600-confirm-message-box'
            }
          ).then(() => {
            this.generateLearnerProfileExpectations();
          })
          .catch(action => {
            if (action === 'cancel') {
              this.handleGroupExpectations(newSelectedAgeGroups, deletedAgeGroups)
              this.stepTwo = true
            }
          });
        } else {
          this.handleGroupExpectations(newSelectedAgeGroups, deletedAgeGroups)
          this.stepTwo = true
        }
      }
    },

    // 处理年龄组期望
    handleGroupExpectations(newAgeGroups, deletedAgeGroups) {
      if (newAgeGroups.length > 0) {
        newAgeGroups.forEach(group => {
          // 如果 ageGroupRubrics 中已经存在，则不进行处理
          if (this.ageGroupRubrics.find(item => item.ageGroup === group)) {
            return
          }
          let copyRubrics = this.ageGroupRubrics[0] || this.rubricsBackup && this.rubricsBackup[0]
          if (!copyRubrics) {
            return
          }
          // 从缓存中找到备份数据
          let backupAgeGroupRubrics = this.rubricsBackup && this.rubricsBackup.find(item => item.ageGroup === group)
          // 如果备份数据存在，则回填历史数据
          if (backupAgeGroupRubrics) {
            // 回填历史数据：能找到的新的 attribute 或 standard 的期望回填回去，找不到的就为空
            let originalDetails = backupAgeGroupRubrics.details
            this.ageGroupRubrics.push({
              ageGroup: group,
              details: copyRubrics.details.map(item => {
                // 在备份数据中查找匹配的属性
                const backupAttribute = originalDetails.find(backupItem => 
                  equalsIgnoreCase(backupItem.title, item.title)
                )
                let expectations = ''
                let subStandards = []
                if (backupAttribute) {
                  // 找到匹配的属性，回填期望
                  expectations = backupAttribute.expectations || ''
                  // 处理子标准
                  if (item.subStandards && item.subStandards.length > 0) {
                    subStandards = item.subStandards.map(subStandard => {
                      // 在备份数据的子标准中查找匹配的子标准
                      const backupSubStandard = backupAttribute.subStandards && 
                        backupAttribute.subStandards.find(backupSub => 
                          equalsIgnoreCase(backupSub.title, subStandard.title)
                        )
                      return {
                        ...subStandard,
                        expectations: backupSubStandard ? (backupSubStandard.expectations || '') : ''
                      }
                    })
                  }
                }
                return {
                  ...item,
                  expectations: expectations,
                  subStandards: subStandards
                }
              })
            })
          } else {
            // 没有历史数据，添加空数据
            this.ageGroupRubrics.push({
              ageGroup: group,
              details: copyRubrics.details.map(item => ({
                ...item,
                expectations: '',
                subStandards: item.subStandards && item.subStandards.length > 0 ? item.subStandards.map(subStandard => {
                  return {
                    ...subStandard,
                    expectations: ''
                  }
                }) : []
              }))
            })
          }
        })
      }
      if (deletedAgeGroups.length > 0) {
        deletedAgeGroups.forEach(group => {
          this.ageGroupRubrics = this.ageGroupRubrics.filter(item => item.ageGroup !== group)
        })
      }
      this.sortAgeGroupRubrics()
      this.$nextTick(() => {
        this.resizeTableAndTextareas()
      })
    },

    // 生成校训
    generateLearnerProfile() {
      this.showTip = false
      this.historyPopoverVisible = false

      // 检查校训内容是否为空
      if (!this.learnerProfileTrim) {
        this.$message.error(this.$t('loc.pleaseProceeding'));
        return;
      }

      // 判断是否有 learnerProfile 变化
      const hasLearnerProfileChanged = this.learnerProfile !== this.snapshot.learnerProfile;

      // 如果 learnerProfile 有变化，使用原方法重新生成全部内容
      if (hasLearnerProfileChanged) {
        this.generateLearnerProfileStream();
        return;
      }

      // 判断是否已有年龄组数据，如果有则使用追加生成方法
      if (this.ageGroupRubrics.length > 0 && this.agencyLearnerProfile.id) {
        // // 获取已有的年龄组列表
        // let existingAgeGroups = this.ageGroupRubrics.map(item => item.ageGroup);
        // // 若 pendingGenerateGrade 存在，需要将 existingAgeGroups 过滤掉 pendingGenerateGrade
        // if (this.pendingGenerateGrade) {
        //   existingAgeGroups = existingAgeGroups.filter(group => group !== this.pendingGenerateGrade);
        // }
        // 获取当前选中的年龄组列表
        const selectedAgeGroups = this.checkedAgeGroup;
        // // 获取新选择的年龄组列表（过滤掉已有的年龄组）
        // const newSelectedAgeGroups = selectedAgeGroups.filter(
        //   group => !this.ageGroupRubrics.map(item => item.ageGroup).includes(group)
        // );
        // // 删除的年龄组
        // const deletedAgeGroups = existingAgeGroups.filter(group => !selectedAgeGroups.includes(group))
        // // 获取已生成过内容的年龄组（有详情的年龄组）
        // const generatedAgeGroups = this.ageGroupRubrics
        //   .filter(item => item.details && item.details.length > 0 && item.details[0].title && item.details[0].title.trim() !== '')
        //   .map(item => item.ageGroup);
        // // 获取已在导航栏但需要生成内容的年龄组
        // const emptyExistingAgeGroups = existingAgeGroups
        //   .filter(group => !generatedAgeGroups.includes(group) && selectedAgeGroups.includes(group));
        // // 合并所有需要生成的年龄组
        // const ageGroupsToGenerate = [...newSelectedAgeGroups, ...emptyExistingAgeGroups];

        let deletedAgeGroups = []
        let ageGroupsToGenerate = []
        let backupAgeGroups = this.rubricsBackup &&this.rubricsBackup.map(item => item.ageGroup) || []
        this.ageGroupRubrics.forEach(item => {
          if (selectedAgeGroups.includes(item.ageGroup) && !backupAgeGroups.includes(item.ageGroup)) {
            ageGroupsToGenerate.push(item.ageGroup)
          }
          if (!selectedAgeGroups.includes(item.ageGroup) && backupAgeGroups.includes(item.ageGroup)) {
            deletedAgeGroups.push(item.ageGroup)
          }
        })

        // 如果是首次生成或全部重新生成，调用完整生成方法
        if (ageGroupsToGenerate.length === 0) {
          this.generateLearnerProfileStream();
        }
        // 如果有新选择的年龄组或空的已存在年龄组，使用追加方法
        else {
          this.handleGroupExpectations(ageGroupsToGenerate, deletedAgeGroups)
          if (!this.generateExpectations) {
            this.stepOne = false
            this.stepTwo = true
            return
          }
          if (this.mixedAgeGroup) {
            // 判断如果是开关打开，重新生成全部的期望
            const expectationsOpenChanged = this.generateExpectations && this.generateExpectations !== this.snapshot.generateExpectations
            this.generateLearnerProfileExpectations(expectationsOpenChanged ? null : ageGroupsToGenerate)
          } else {
            this.generateAppendLearnerProfileStream(ageGroupsToGenerate)
          }
        }
      } else {
        // 如果没有年龄组数据，使用原方法
        this.generateLearnerProfileStream();
      }

      // 无论哪种方式生成，都确保最后进行排序
      setTimeout(() => {
        this.sortAgeGroupRubrics();
      }, 50);
    },

    // 生成各年级相关的标准
    async generateLearnerProfileStream() {
      // 生成校训埋点
      this.$analytics.sendEvent('cg_unit_graduate_pop_generate')
      this.generateLoading = true
      this.rubricsData = ''
      // 生成单元概览参数
      let params = {
        learnerProfile: this.learnerProfile,
        ageGroups: this.checkedAgeGroup,
        mixedAgeGroup: this.mixedAgeGroup
      }
      let streamVersion = this.streamVersion
      let data = ''
      // 备份当前期望数据，使用浅拷贝减少内存开销
      const expectationsBackup = this.shallowBackupExpectations()
      // 消息回调
      let messageCallback = (message) => {
        // 如果离开页面，则不再执行
        if (this.leavedPage || streamVersion !== this.streamVersion) {
          data = ''
          return
        }
        // 减少DOM操作频率，使用防抖
        this.debouncedResizeTableAndTextareas()
        this.leavedPage = false
        this.showResult = true
        this.stepOne = false
        this.stepTwo = true
        this.isEmptyContent = false
        // 更新数据
        data += message.data
        if (this.mixedAgeGroup) {
          // 混合年龄组处理：解析数据并分配给所有选中的年龄组
          let parsedDetails = this.parseRubrics(data)
          // 为每个选中的年龄组创建数据结构，确保 details 结构完整
          this.ageGroupRubrics = this.checkedAgeGroup.map(ageGroup => ({
            ageGroup: ageGroup,
            ageGroupRubrics: data.trim(),
            details: parsedDetails.map(detail => ({
              ...detail,
              subStandards: detail.subStandards ? detail.subStandards.map(sub => ({...sub})) : []
            }))
          }))
          this.rubricsData = data
        } else {
          // 解析数据
          let parsedData = parseStreamData(data, [
            { key: 'ageGroup', name: 'Grade' },
            { key: 'ageGroupRubrics', name: 'Graduate Portrait Expectations' }
          ])
          if (parsedData) {
            parsedData.forEach((item) => {
              // 如果年级存在，则替换成对应的年级,解决 AI 返回年级大小写与系统中不一致问题
              if (item.ageGroup) {
                let ageGroup = this.ageGroups.find((v) =>
                  equalsIgnoreCase(v.ageGroup, item.ageGroup.trim())
                )
                if (ageGroup) {
                  item.ageGroup = ageGroup.ageGroup
                }
              }
  
              // 解析并存储 details
              if (item.ageGroupRubrics) {
                item.ageGroupRubrics = item.ageGroupRubrics.trim()
                if (item.ageGroupRubrics) {
                  item.details = this.parseRubrics(item.ageGroupRubrics)
                } else {
                  item.details = []
                }
              } else {
                item.details = []
                item.ageGroupRubrics = ''
              }
            })
            // 过滤掉不存在 ageGroup 的行
            parsedData = parsedData.filter(item => item.ageGroup && this.ageGroups.find((v) =>
              equalsIgnoreCase(v.ageGroup, item.ageGroup.trim())
            ))
          }
          this.ageGroupRubrics = parsedData
          this.selectedAgeGroupIndex = 0 // 重置选中的年龄段索引
          this.rubricsData = data
          // 减少排序频率，使用防抖
          this.debouncedSortAgeGroupRubrics()
        }
      }
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource(
          $api.urls().generateLearnerProfileStream,
          null,
          messageCallback,
          'POST',
          params
        )
          .then((res) => {
            // 如果离开页面，则不再执行
            if (this.leavedPage || streamVersion !== this.streamVersion) {
              return
            }
            // 如果没有解析出来 ageGroup 则认为生成失败
            if (!this.ageGroupRubrics.length || !this.ageGroupRubrics.find((v) => v.ageGroup)) {
              this.isEmptyContent = true
            }
            // 检查是否有任何年龄组含有详细内容
            const hasAnyDetails = this.ageGroupRubrics.some(group =>
              group.details && group.details.length > 0 &&
              group.details.some(detail => detail.title || detail.description)
            );
            if (!hasAnyDetails) {
              // 若生成失败，需要将当前年龄组在 ageGroupRubrics 中删除
              this.ageGroupRubrics = this.ageGroupRubrics.filter(group =>
                group.details && group.details.length > 0 &&
                group.details.some(detail => detail.title || detail.description)
              );
              this.isEmptyContent = true;
            }
            // 再次确保排序
            this.debouncedSortAgeGroupRubrics()
            // 如果生成内容失败，则提示无法处理请求
            if (this.isEmptyContent) {
              this.generateLoading = false
              this.showResult = false
              this.stepTwo = false
              this.stepOne = true
              this.$message.error(this.$t('loc.unableToProcessRequestAdjustDescription'))
              // 生成失败，恢复备份的期望数据
              this.restoreExpectations(expectationsBackup)
            } else {
              this.rubricsBackup = this.backupExpectations()
              // 如果是混合年龄组并且生成期望，则生成期望
              if (this.generateExpectations && this.mixedAgeGroup) {
                this.generateLearnerProfileExpectations()
              } else {
                this.generateLoading = false
                this.generated = true
                this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
                // 标记有未保存到服务器的修改
                this.hasModified = true;
                // 生成成功后保存快照
                this.snapshot = {
                  originalLearnerProfile: this.snapshot && this.snapshot.originalLearnerProfile,
                  learnerProfile: this.learnerProfile,
                }
                resolve()
              }
            }
            // 更新当前年龄组的详情副本，避免刚生成完就提示保存
            // this.saveCurrentAgeGroupDetailCopy();
            this.debouncedResizeTableAndTextareas()
            resolve()
          })
          .catch((error) => {
            // 生成失败，恢复备份的期望数据
            this.restoreExpectations(expectationsBackup)
            this.stepOne = true
            this.stepTwo = false
            this.generateLoading = false
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
            reject(error)
          })
      })
    },

    // 生成期望数据
    async generateLearnerProfileExpectations(ageGroup) {
      let generateAgeGroups = this.ageGroupRubrics.map(item => item.ageGroup)
      if (ageGroup) {
        if (Array.isArray(ageGroup) && ageGroup.length > 0) {
          generateAgeGroups = ageGroup
        } else {
          generateAgeGroups = [ageGroup]
        }
      }
      if (this.mixedAgeGroup) {
        this.generateLearnerProfileExpectationsStream(generateAgeGroups)
      } else {
        this.generateAppendLearnerProfileStream(generateAgeGroups)
      }
    },

    // 生成期望数据流
    async generateLearnerProfileExpectationsStream(ageGroups) {
      // 验证表格数据
      if (!this.validateTableData()) {
        return Promise.reject('failed');
      }
      this.generateLoading = true
      
      // 备份当前期望数据，使用浅拷贝减少内存开销
      const expectationsBackup = this.shallowBackupExpectations()
      
      // 清空对应年龄组的期望内容
      this.clearExpectations(ageGroups)
      
      let params = {
        ageGroups: ageGroups,
        hasSubStandards: this.hasSubStandards,
        rubricsModels: this.ageGroupRubrics[0].details
      }
      let streamVersion = this.streamVersion
      let data = ''
      let messageCallback = (message) => {
        if (this.leavedPage || streamVersion !== this.streamVersion) {
          return
        }
        // 减少DOM操作频率，使用防抖
        this.debouncedResizeTableAndTextareas()
        this.leavedPage = false
        this.showResult = true
        this.stepOne = false
        this.stepTwo = true
        this.isEmptyContent = false
        data += message.data

        // 解析期望数据并更新到对应的年龄组
        this.parseAndUpdateExpectations(data)
      }
      return new Promise((resolve, reject) => {
        createEventSource(
          $api.urls().generateLearnerProfileExpectationsStream,
          null,
          messageCallback,
          'POST',
          params
        )
        .then((res) => {
          this.generateLoading = false
          this.generated = true
          this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
          // 标记有未保存到服务器的修改
          this.hasModified = true;
          // 生成成功后保存快照
          this.snapshot = {
            originalLearnerProfile: this.snapshot && this.snapshot.originalLearnerProfile,
            learnerProfile: this.learnerProfile,
          }
          this.rubricsBackup = this.shallowBackupExpectations()
          this.debouncedResizeTableAndTextareas()
          resolve()
        })
        .catch((error) => {
          // 生成失败，恢复备份的期望数据
          this.restoreExpectations(expectationsBackup)
          this.generateLoading = false
          this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          reject(error)
        })
      })
    },

    // 清空指定年龄组的期望内容
    clearExpectations(ageGroups) {
      ageGroups.forEach(ageGroup => {
        const ageGroupData = this.ageGroupRubrics.find(group => 
          equalsIgnoreCase(group.ageGroup, ageGroup)
        )
        
        if (ageGroupData && ageGroupData.details) {
          ageGroupData.details.forEach(attribute => {
            if (attribute.subStandards && attribute.subStandards.length > 0) {
              // 有子标准的情况，清空每个子标准的期望
              attribute.subStandards.forEach(subStandard => {
                this.$set(subStandard, 'expectations', '')
              })
            } else {
              // 没有子标准的情况，清空属性的期望
              this.$set(attribute, 'expectations', '')
            }
          })
        }
      })
    },

    // 备份指定年龄组的期望数据
    backupExpectations() {
      return JSON.parse(JSON.stringify(this.ageGroupRubrics))
    },
    
    // 浅拷贝备份期望数据，减少内存开销
    shallowBackupExpectations() {
      return this.ageGroupRubrics.map(group => ({
        ...group,
        details: group.details && Array.isArray(group.details) ? group.details.map(detail => ({
          ...detail,
          subStandards: detail.subStandards && Array.isArray(detail.subStandards) ? detail.subStandards.map(sub => ({...sub})) : []
        })) : []
      }))
    },

    // 恢复备份的期望数据
    restoreExpectations(backup) {
      this.ageGroupRubrics = backup
      this.isEmptyContent = false
      this.showResult = true
    },

    // 解析期望数据并更新到对应的年龄组
    parseAndUpdateExpectations(data) {
      if (!data || !data.trim()) {
        return
      }

      const lines = data.split('\n')
      let currentGradeBand = ''
      let currentAttribute = ''
      let currentSubStandard = ''

      // 检测是否有子标准格式
      const hasSubStandardFormat = data.includes('Sub-standard:')

      if (hasSubStandardFormat) {
        // Case1: 带 sub-standard 的格式
        for (let line of lines) {
          line = line.trim()
          if (!line) continue

          if (line.startsWith('Grade Band:')) {
            currentGradeBand = line.substring('Grade Band:'.length).trim()
          } else if (line.startsWith('Attribute Name:')) {
            currentAttribute = line.substring('Attribute Name:'.length).trim()
          } else if (line.startsWith('Sub-standard:')) {
            currentSubStandard = line.substring('Sub-standard:'.length).trim()
          } else if (line.startsWith('Expectations:') && currentGradeBand && currentAttribute && currentSubStandard) {
            const expectation = line.substring('Expectations:'.length).trim()
            this.updateExpectationForSubStandard(currentGradeBand, currentAttribute, currentSubStandard, expectation)
          }
        }
      } else {
        // Case2: 不带 sub-standard 的格式
        let currentEducationalStage = ''
        let expectationsStarted = false

        for (let line of lines) {
          line = line.trim()
          if (!line) continue

          if (line.startsWith('Educational Stage:')) {
            currentEducationalStage = line.substring('Educational Stage:'.length).trim()
            expectationsStarted = false
          } else if (line.startsWith('Graduate Portrait Expectations:')) {
            expectationsStarted = true
          } else if (expectationsStarted && currentEducationalStage) {
            // 解析数字编号的期望，格式如: "1. Adaptability: Students effectively handle..."
            const match = line.match(/^\d+\.\s*([^:]+):\s*(.+)$/)
            if (match) {
              const attributeName = match[1].trim()
              const expectation = match[2].trim()
              this.updateExpectationForAttribute(currentEducationalStage, attributeName, expectation)
            }
          }
        }
      }
    },

    // 更新带子标准的期望
    updateExpectationForSubStandard(gradeBand, attributeName, subStandardName, expectation) {
      // 直接使用年龄组查找对应的数据
      const ageGroupData = this.ageGroupRubrics.find(group => 
        equalsIgnoreCase(group.ageGroup, gradeBand)
      )

      if (!ageGroupData || !ageGroupData.details) {
        return
      }

      // 找到对应的属性
      const attribute = ageGroupData.details.find(attr => 
        equalsIgnoreCase(attr.title.trim(), attributeName.trim())
      )

      if (!attribute || !attribute.subStandards) {
        return
      }

      // 找到对应的子标准
      const subStandard = attribute.subStandards.find(sub =>
        // 去掉多余空格换行符比较，一致时认为相等
        equalsIgnoreCase(sub.title.trim().replace(/\s+/g, ' '), subStandardName.trim().replace(/\s+/g, ' '))
      )

      if (subStandard) {
        this.$set(subStandard, 'expectations', expectation)
      }
    },

    // 更新不带子标准的期望
    updateExpectationForAttribute(educationalStage, attributeName, expectation) {
      // 直接使用年龄组查找对应的数据
      const ageGroupData = this.ageGroupRubrics.find(group => 
        equalsIgnoreCase(group.ageGroup, educationalStage)
      )

      if (!ageGroupData || !ageGroupData.details) {
        return
      }

      // 找到对应的属性
      const attribute = ageGroupData.details.find(attr =>
        // 去掉多余空格换行符比较，一致时认为相等
        equalsIgnoreCase(attr.title.trim().replace(/\s+/g, ' '), attributeName.trim().replace(/\s+/g, ' '))
      )

      if (attribute) {
        this.$set(attribute, 'expectations', expectation)
      }
    },

    // 追加生成年龄组内容
    async generateAppendLearnerProfileStream(newAgeGroups) {
      // 验证表格数据
      if (!this.validateTableData()) {
        return Promise.reject('failed');
      }
      // 生成校训埋点
      this.$analytics.sendEvent('cg_unit_graduate_pop_generate_append');
      this.generateLoading = true;
      this.rubricsData = '';
      // 生成追加年龄组的参数
      let params = {
        learnerProfile: this.learnerProfile,
        learnerProfileId: this.agencyLearnerProfile.id,
        ageGroups: newAgeGroups,
        rubricsModels: this.ageGroupRubrics[0].details
      };

      let streamVersion = this.streamVersion;
      let data = '';

      // 消息回调
      let messageCallback = (message) => {
        // 如果离开页面，则不再执行
        if (this.leavedPage || streamVersion !== this.streamVersion) {
          data = '';
          return;
        }
        // 减少DOM操作频率，使用防抖
        this.debouncedResizeTableAndTextareas()
        this.leavedPage = false;
        this.showResult = true;
        this.isEmptyContent = false;
        this.stepOne = false
        this.stepTwo = true;

        // 更新数据
        data += message.data;

        // 解析数据
        let parsedData = parseStreamData(data, [
          { key: 'ageGroup', name: 'Grade' },
          { key: 'ageGroupRubrics', name: 'Graduate Portrait Expectations' }
        ]);

        if (parsedData && parsedData.length > 0) {
          // 保存现有年龄组数据的副本
          const currentAgeGroupRubrics = JSON.parse(JSON.stringify(this.ageGroupRubrics));

          // 过滤有效的解析数据，必须同时包含 ageGroup 和 details
          const validParsedData = parsedData.filter(item => {
            // 标准化年龄组名称
            if (item.ageGroup) {
              const ageGroup = this.ageGroups.find(v =>
                equalsIgnoreCase(v.ageGroup, item.ageGroup.trim())
              );

              if (ageGroup) {
                item.ageGroup = ageGroup.ageGroup;

                // 验证是否包含在请求的新年龄组列表中
                return newAgeGroups.includes(item.ageGroup);
              }
            }
            return false;
          });

          // 处理有效的解析数据
          if (validParsedData.length > 0) {
            validParsedData.forEach((item) => {
              // 解析并存储 details
              if (item.ageGroupRubrics) {
                item.ageGroupRubrics = item.ageGroupRubrics.trim();
                if (item.ageGroupRubrics) {
                  item.details = this.parseRubrics(item.ageGroupRubrics);
                } else {
                  item.details = [];
                }
              } else {
                item.details = [];
                item.ageGroupRubrics = '';
              }

              // 只有 details 的年龄组才会被添加或更新
              if (item.details && item.details.length > 0) {
                // 检查新的年龄组是否已经存在
                const existingIndex = currentAgeGroupRubrics.findIndex(
                  existing => equalsIgnoreCase(existing.ageGroup, item.ageGroup)
                );

                if (existingIndex !== -1) {
                  // 如果已存在，更新数据
                  currentAgeGroupRubrics[existingIndex] = item;
                } else {
                  // 如果不存在，添加到数组中
                  currentAgeGroupRubrics.push(item);
                }
              }
            });

            // 更新年龄组数据，合并新旧数据
            this.ageGroupRubrics = currentAgeGroupRubrics;
            // 排序年龄组
            this.sortAgeGroupRubrics();
          }
        }

        this.rubricsData = data;
      };

      return new Promise((resolve, reject) => {
        // 调用追加生成API
        createEventSource(
          $api.urls().generateAppendLearnerProfileStream,
          null,
          messageCallback,
          'POST',
          params
        )
          .then((res) => {
            // 如果离开页面，则不再执行
            if (this.leavedPage || streamVersion !== this.streamVersion) {
              return;
            }

            // 如果没有解析出来有效的年龄组数据则认为生成失败
            const hasValidAgeGroups = this.ageGroupRubrics.some(group => {
              return newAgeGroups.includes(group.ageGroup) &&
                     group.details &&
                     group.details.length > 0 &&
                     group.details.some(detail => detail.title || detail.description);
            });

            if (!hasValidAgeGroups) {
              this.isEmptyContent = true;
            }

            this.generateLoading = false;
            this.generated = true;

            // 再次确保排序
            this.debouncedSortAgeGroupRubrics();

            // 如果生成内容失败，则提示无法处理请求
            if (this.isEmptyContent) {
              this.$message.error(this.$t('loc.unableToProcessRequestAdjustDescription'));
            } else {
              this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'));
              // 标记有未保存到服务器的修改
              this.hasModified = true;
            }

            // 生成成功后保存快照
            this.snapshot = {
              originalLearnerProfile: this.snapshot && this.snapshot.originalLearnerProfile,
              learnerProfile: this.learnerProfile,
            };
            this.rubricsBackup = this.shallowBackupExpectations();

            // 生成完成

            resolve();
          })
          .catch((error) => {
            this.generateLoading = false;
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'));
            reject(error);
          });
      });
    },
    // 关闭新增校训弹窗
    closeTipDetails() {
      this.showTip = false
      this.historyPopoverVisible = false

      // 清理所有年龄组中的空白行
      // this.cleanAllEmptyRows();

      if (!this.showResult && !this.generateLoading) {
        this.addLearnerProfileDialog = false
        return
      }
      // 如果正在生成内容，不提示保存
      if (this.generateLoading || this.tableLoading) {
        this.$confirm(
          this.$t('loc.generationInProgressTip'),
          this.$t('loc.confirmation'),
          {
            confirmButtonText: this.$t('loc.generationInProgressConfirm'),
            cancelButtonText: this.$t('loc.generationInProgressCancel'),
            cancelButtonClass: 'is-plain',
            closeOnClickModal: false,
            distinguishCancelAndClose: true,
            customClass: 'width-600-confirm-message-box'
          }
        )
          .then(() => {
            // 验证表格数据是否有效
            if (!this.validateTableData()) {
              return Promise.reject('failed');
            }
            this.snapshot.learnerProfile = this.learnerProfile
            this.rubricsBackup = this.backupExpectations()
            // 现在才真正调用接口保存
            this.handleSave()
            this.addLearnerProfileDialog = false
            this.streamVersion++
            this.leavedPage = true
          })
          .catch((action) => {
            // 如果选择不保存，则关闭弹窗
            if (action === 'cancel') {
              this.addLearnerProfileDialog = false
              this.streamVersion++
              this.leavedPage = true
            }
          })
        return;
      }

      // 检查是否有任何需要保存的修改
      const hasChangesToSave = this.hasModified ||
        JSON.stringify(this.ageGroupRubricsCopy) !== JSON.stringify(this.ageGroupRubrics) ||
        (this.learnerProfile && this.snapshot.originalLearnerProfile !== this.learnerProfile);

      // 如果已经生成内容但是没有保存，提示是否保存
      // 如果生成失败是不需要确认提示的
      if (!this.isEmptyContent && hasChangesToSave) {
        // 保存当前修改，以便在不保存时恢复
        const currentAgeGroupCopy = this.currentAgeGroupDetail ?
          JSON.parse(this.currentAgeGroupDetail) : null;

        this.$confirm(
          this.$t('loc.unitPlannerStep3SaveConfirmMessage'),
          this.$t('loc.confirmation'),
          {
            confirmButtonText: this.$t('loc.save'),
            cancelButtonText: this.$t('loc.noSave'),
            cancelButtonClass: 'is-plain',
            closeOnClickModal: false,
            distinguishCancelAndClose: true,
            customClass: 'width-600-confirm-message-box'
          }
        )
          .then(() => {
            // 验证表格数据是否有效
            if (!this.validateTableData()) {
              return Promise.reject('failed');
            }

            this.snapshot.learnerProfile = this.learnerProfile
            this.rubricsBackup = this.backupExpectations()
            // 现在才真正调用接口保存
            this.handleSave()
          })
          .catch((action) => {
            if (action === 'cancel') {
              // 如果选择不保存，恢复原始数据
              if (currentAgeGroupCopy && this.ageGroupRubrics[this.selectedAgeGroupIndex]) {
                this.ageGroupRubrics[this.selectedAgeGroupIndex].details = currentAgeGroupCopy;
              }

              this.addLearnerProfileDialog = false
              this.streamVersion++
              this.leavedPage = true
            }
          })
      } else {
        this.addLearnerProfileDialog = false
      }
    },
    // 按照年龄组重新生成期望
    regenerateByGradeBand() {
      this.$confirm(
        this.$t('loc.learnerProfileGenerateByGradeBandsTip'),
        this.$t('loc.learnerProfileGenerateByGradeBandsTitle'),
        {
          confirmButtonText: this.$t('loc.unitPlannerConfirmRegenerate'),
          confirmButtonClass: 'el-button--primary',
          cancelButtonText: this.$t('loc.cancel'),
          cancelButtonClass: 'is-plain',
          closeOnClickModal: false,
          distinguishCancelAndClose: true,
          customClass: 'width-600-confirm-message-box'
        }
      ).then(() => {
        this.ageGroups = JSON.parse(JSON.stringify(TAILOR_UNIT_MIXED_AGE_GROUPS)).map(item => {
          return {
            ...item,
            selected: true
          }
        })
        this.mixedAgeGroup = true
        this.generateExpectations = true
        this.generateLearnerProfileStream()
      }).catch(() => {});
    },
    // 保存校训
    async handleSave() {
      this.closeLoading = true
      if (!this.validateTableData()) {
        this.closeLoading = false
        return Promise.reject('failed');
      }
      // 保存校训埋点
      this.$analytics.sendEvent('cg_unit_graduate_pop_save')
      this.showTip = false
      this.historyPopoverVisible = false
      let params = {
        newAgeGroupRubrics: this.ageGroupRubrics
          .map((ageGroup) => {
            ageGroup.details.forEach((item) => {
              item.title = item.title && item.title.trim()
              item.description = item.description && item.description.trim()
              item.expectations = item.expectations && item.expectations.trim()
              if (item.subStandards) {
                item.subStandards.forEach(subStandard => {
                  subStandard.title = subStandard.title && subStandard.title.trim()
                  subStandard.description = subStandard.description && subStandard.description.trim()
                  subStandard.expectations = subStandard.expectations && subStandard.expectations.trim()
                })
              }
            })
            return ageGroup
          }),
        ...this.snapshot, // 使用快照
        generateExpectations: this.generateExpectations
      }
      // this.ageGroupRubrics = params.newAgeGroupRubrics // 更新年龄组数据为 trim 后的数据
      // 如果没有内容，则提示至少填写一个校训
      if (!params.newAgeGroupRubrics.some((item) => item.details && item.details.length > 0)) {
        this.$alert(this.$t('loc.unitPlannerStep3AtLeastOneRubric'), this.$t('loc.note'), {
          confirmButtonText: this.$t('loc.ikonw'),
          cancelButtonClass: 'is-plain'
        })
        return Promise.reject('no content');
      }
      // 期望开关是否打开了
      const expectationsOpenChanged = this.generateExpectations && this.generateExpectations !== this.snapshot.generateExpectations;
      // 需要重新生成的年龄组
      const selectedAgeGroups = this.checkedAgeGroup;
      let ageGroupsToGenerate = []
      let backupAgeGroups = this.rubricsBackup && this.rubricsBackup.map(item => item.ageGroup) || []
      this.ageGroupRubrics.forEach(item => {
        if (selectedAgeGroups.includes(item.ageGroup) && !backupAgeGroups.includes(item.ageGroup)) {
          ageGroupsToGenerate.push(item.ageGroup)
        }
      })
      // 如果校训变化或者期望打开状态变化或者有需要重新生成的年龄组，则提示保存还是重新生成
      if (this.learnerProfile && this.learnerProfile !== this.snapshot.learnerProfile || this.stepOne && (expectationsOpenChanged || this.generateExpectations && ageGroupsToGenerate.length > 0)) {
        return new Promise((resolve, reject) => {
          this.$confirm(this.$t('loc.learnerProfileSaveTips'), this.$t('loc.confirmation'), {
            cancelButtonText: this.$t('loc.learnerProfileOnlySave'),
            confirmButtonText: this.$t('loc.unitPlannerStep1Regenerate'),
            cancelButtonClass: 'is-plain',
            distinguishCancelAndClose: true,
            closeOnClickModal: false,
            customClass: 'width-600-confirm-message-box'
          })
            .then((res) => {
              this.generateLearnerProfileStream().then(resolve).catch(reject);
            })
            .catch((action) => {
              if (action === 'cancel') {
                this.snapshot.learnerProfile = this.learnerProfile
                this.snapshot.generateExpectations = this.generateExpectations
                this.rubricsBackup = this.backupExpectations()
                this.handleSave().then(resolve).catch(reject);
              } else {
                reject('cancel');
              }
            })
        });
      }
      this.saveLoading = true
      return new Promise((resolve, reject) => {
        let request;
        if (this.agencyLearnerProfile.id) {
          request = this.$axios.post($api.urls().updateLearnerProfile, {
            id: this.agencyLearnerProfile.id,
            ...params
          });
        } else {
          request = this.$axios.post($api.urls().createLearnerProfile, params);
        }

        request.then(res => {
          this.ageGroupRubrics = []
          this.$store.dispatch('getAgencyLearnerProfile')
          this.$message.success(this.$t('loc.saveSuccess'))
          this.addLearnerProfileDialog = false
          // 保存成功后重置修改标记
          this.$nextTick(() => {
            // 重置修改标记
            this.hasModified = false;
          });
          resolve(res);
        }).catch(error => {
          reject(error);
        }).finally(() => {
          this.saveLoading = false
          this.closeLoading = false
        });
      });
    },
    // 解析 rubrics 内容为数据结构
    parseRubrics(content) {
      if (!content) {
        return [];
      }

      const details = [];
      const lines = content.split('\n');

      // 检测是否为新格式（包含 "Attribute Name:"、"Sub-standard:" 或 "Description:"）
      // 优化检测逻辑，避免遍历所有行
      let hasNewFormat = false;
      for (let i = 0; i < Math.min(lines.length, 10); i++) { // 只检查前10行
        const line = lines[i].trim();
        if (line.startsWith('Attribute Name:') || 
            line.startsWith('Sub-standard:') || 
            line.startsWith('Description:')) {
          hasNewFormat = true;
          break;
        }
      }

      if (hasNewFormat) {
        // 解析新格式：Attribute Name: xxx, Sub-standard: xxx, Description: xxx
        let currentAttribute = null;

        for (let line of lines) {
          line = line.trim();
          if (!line) continue;

          if (line.startsWith('Attribute Name:')) {
            // 如果有当前属性，先保存它
            if (currentAttribute) {
              details.push(currentAttribute);
            }

            // 创建新属性
            const attributeName = line.substring('Attribute Name:'.length).trim();
            currentAttribute = {
              title: attributeName,
              description: '',
              expectations: '',
              subStandards: []
            };
          } else if (line.startsWith('Sub-standard:') && currentAttribute) {
            // 添加子标准到当前属性
            const subStandardText = line.substring('Sub-standard:'.length).trim();
            const subStandardIndex = currentAttribute.subStandards.length + 1;
            const subStandardTitle = `${currentAttribute.title} ${subStandardIndex}`;
            
            currentAttribute.subStandards.push({
              title: subStandardTitle, // 生成子标准标题，如 "Responsibility1", "Responsibility2"
              description: subStandardText, // Sub-standard 后面的内容作为描述
              expectations: '' // 期望为空
            });
          } else if (line.startsWith('Description:') && currentAttribute) {
            // 设置属性的描述
            const descriptionText = line.substring('Description:'.length).trim();
            // 如果描述是 "None"，则设置为空字符串
            currentAttribute.description = (descriptionText === 'None') ? '' : descriptionText;
            currentAttribute.expectations = '';
          }
        }

        // 保存最后一个属性
        if (currentAttribute) {
          details.push(currentAttribute);
        }

        // 返回解析后的数据结构
        return details;
      } else {
        // 解析旧格式：1. Title: Description
        for (let line of lines) {
          line = line.trim();
          if (!line) continue;

          const match = line.match(/^\d+\.\s*(.*)/);
          if (match) {
            const fullContent = match[1].trim();
            const colonIndex = fullContent.indexOf(':');

            if (colonIndex > 0) {
              const title = fullContent.substring(0, colonIndex).trim();
              const expectations = fullContent.substring(colonIndex + 1).trim();

              // 创建简单的扁平属性结构，不包含subStandards
              details.push({
                title: title,
                description: '', // 描述为空
                expectations: expectations
              });
            }
          }
        }
      }

      return details;
    },

    // 验证表格数据 - 校验必填字段（空值和重复值）
    validateTableData() {
      if (!this.ageGroupRubrics.length || !this.ageGroupRubrics[0].details) {
        return true;
      }

      let isValid = true;
      let firstErrorPosition = null;

      // 使用第一个年龄组作为基准进行验证
      const baseGroup = this.ageGroupRubrics[0];

      // 检查每个属性及其子标准的必填字段
      baseGroup.details.forEach((attribute, attributeIndex) => {
        const attributeTitleTrimmed = attribute.title ? attribute.title.trim() : '';

        // 检查子标准
        if (attribute.subStandards && attribute.subStandards.length > 0) {
          attribute.subStandards.forEach((standard, standardIndex) => {
            const standardTitleTrimmed = standard.title ? standard.title.trim() : '';
            
            let hasError = false;
            let errorType = {};

            // 1. 检查空值
            if (!attributeTitleTrimmed) {
              hasError = true;
              errorType.missingAttribute = true;
            }
            if (!standardTitleTrimmed) {
              hasError = true;
              errorType.missingStandard = true;
            }

            // 2. 检查属性标题重复（只在标题不为空时检查）
            if (attributeTitleTrimmed && this.checkAttributeTitleDuplicate(attributeTitleTrimmed, attributeIndex)) {
              hasError = true;
              errorType.duplicateAttribute = true;
            }

            // 3. 检查子标准标题重复（只在标题不为空时检查）
            if (standardTitleTrimmed && this.checkSubStandardTitleDuplicate(attributeIndex, standardTitleTrimmed, standardIndex)) {
              hasError = true;
              errorType.duplicateStandard = true;
            }

            if (hasError) {
              isValid = false;
              // 记录第一个错误位置
              if (!firstErrorPosition) {
                firstErrorPosition = {
                  attributeIndex,
                  standardIndex,
                  ...errorType
                };
              }
            }
          });
        } else {
          // 如果没有子标准，只检查属性标题
          let hasError = false;
          let errorType = {};

          // 1. 检查空值
          if (!attributeTitleTrimmed) {
            hasError = true;
            errorType.missingAttribute = true;
          }

          // 2. 检查属性标题重复（只在标题不为空时检查）
          if (attributeTitleTrimmed && this.checkAttributeTitleDuplicate(attributeTitleTrimmed, attributeIndex)) {
            hasError = true;
            errorType.duplicateAttribute = true;
          }

          if (hasError) {
            isValid = false;
            // 记录第一个错误位置
            if (!firstErrorPosition) {
              firstErrorPosition = {
                attributeIndex,
                standardIndex: -1, // 没有子标准
                ...errorType
              };
            }
          }
        }
      });

      this.tableValidationError = !isValid;

      // 如果验证失败且需要滚动到错误位置
      if (!isValid && firstErrorPosition) {
        this.$nextTick(() => {
          this.scrollToErrorPosition(firstErrorPosition);
        });
      }

      // 如果生成内容后没有有效行，确保至少添加一个空行
      if (this.generated && baseGroup.details.length === 0) {
        this.addAttribute();
      }

      return isValid;
    },

    // 排序年龄组的方法
    sortAgeGroupRubrics() {
      // 使用排序函数对 ageGroupRubrics 数组排序
      if (!this.ageGroupRubrics || this.ageGroupRubrics.length <= 1) {
        return;
      }

      // 使用 tools.js 中的 getAgeValueTemp 方法进行倒序排序
      this.ageGroupRubrics.sort((a, b) => {
        const valueA = tools.getAgeValueTemp(a.ageGroup);
        const valueB = tools.getAgeValueTemp(b.ageGroup);
        return valueB - valueA; // 修改为倒序排序
      });
    },

    // 清理所有年龄组中的空白行
    cleanAllEmptyRows() {
      if (!this.ageGroupRubrics || this.ageGroupRubrics.length === 0) {
        return;
      }

      // 遍历所有年龄组，清理每个年龄组中的空白行
      this.ageGroupRubrics.forEach((group) => {
        if (group.details) {
          // 过滤掉标题、描述和期望都为空的行
          group.details = group.details.filter(detail => {
            const titleTrimmed = detail.title ? detail.title.trim() : '';
            const descriptionTrimmed = detail.description ? detail.description.trim() : '';
            
            // 检查所有年龄组的期望是否都为空
            const hasAnyExpectations = this.ageGroupRubrics.some(ageGroup => {
              const correspondingDetail = ageGroup.details && ageGroup.details.find(d => d.title === detail.title);
              return correspondingDetail && correspondingDetail.expectations && correspondingDetail.expectations.trim() !== '';
            });

            return titleTrimmed !== '' || descriptionTrimmed !== '' || hasAnyExpectations;
          });
        }
      });
    },
  
    // 检查属性标题是否重复
    checkAttributeTitleDuplicate(title, excludeIndex = -1) {
      if (!title || !this.ageGroupRubrics.length || !this.ageGroupRubrics[0].details) {
        return false;
      }

      const trimmedTitle = title.trim();
      if (!trimmedTitle) {
        return false;
      }

      // 1. 检查属性之间的重名
      const attrDuplicate = this.ageGroupRubrics[0].details.some((item, index) => {
        if (index === excludeIndex) {
          return false;
        }
        const itemTitle = item.title ? item.title.trim() : '';
        return itemTitle === trimmedTitle;
      });
      if (attrDuplicate) return true; 

      // 2. 检查所有子标准是否有同名
      const subStandardDuplicate = this.ageGroupRubrics[0].details.some(attribute => {
        if (!attribute.subStandards) return false;
        return attribute.subStandards.some(subStandard => {
          const subStandardTitle = subStandard.title ? subStandard.title.trim() : '';
          return subStandardTitle === trimmedTitle;
        });
      });
      return subStandardDuplicate;
    },

    // 检查子标准标题在整个表格中是否重复（跨属性检查）
    checkSubStandardTitleDuplicate(attributeIndex, title, excludeStandardIndex = -1) {
      if (!title || !this.ageGroupRubrics.length || !this.ageGroupRubrics[0].details) {
        return false;
      }

      const trimmedTitle = title.trim();
      if (!trimmedTitle) {
        return false;
      }

      // 1. 检查所有子标准之间的重名
      const subStandardDuplicate = this.ageGroupRubrics[0].details.some((attribute, attrIndex) => {
        if (!attribute.subStandards) {
          return false;
        }
        return attribute.subStandards.some((subStandard, standardIndex) => {
          if (attrIndex === attributeIndex && standardIndex === excludeStandardIndex) {
            return false;
          }
          const subStandardTitle = subStandard.title ? subStandard.title.trim() : '';
          return subStandardTitle === trimmedTitle;
        });
      });
      if (subStandardDuplicate) return true;

      // 2. 检查所有属性名是否有同名
      const attrDuplicate = this.ageGroupRubrics[0].details.some((item) => {
        const itemTitle = item.title ? item.title.trim() : '';
        return itemTitle === trimmedTitle;
      });
      return attrDuplicate;
    },

    // spanMethod 实现
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 属性列
        if (row.isFirstRowOfAttribute) {
          return {
            rowspan: row.attributeRowSpan,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },

    // 属性数据变化处理
    onAttributeChange(attributeIndex, field, value) {
      // 先进行重复性检查，避免在循环中重复检查
      if (field === 'title' && value && value.trim()) {
        if (this.checkAttributeTitleDuplicate(value, attributeIndex)) {
          this.$message.error(this.$t('loc.learnerProfileAttributeTitleAlreadyExistsTip'));
          // 仍然允许数据同步，但标记为有重复错误
        }
      }

      // 同步更新所有年龄组的相应属性
      this.ageGroupRubrics.forEach(ageGroup => {
        if (ageGroup.details && ageGroup.details[attributeIndex]) {
          this.$set(ageGroup.details[attributeIndex], field, value);
        }
      });
      this.hasModified = true;
      this.resizeTableAndTextareas()
    },

    // 标准数据变化处理
    onStandardChange(attributeIndex, standardIndex, field, value) {
      // 先进行重复性检查，避免在循环中重复检查
      if (field === 'title' && value && value.trim()) {
        if (standardIndex === -1) {
          // 检查属性标题重复
          if (this.checkAttributeTitleDuplicate(value, attributeIndex)) {
            this.$message.error(this.$t('loc.learnerProfileAttributeTitleAlreadyExistsTip'));
            // 仍然允许数据同步，但标记为有重复错误
          }
        } else {
          // 检查子标准标题重复
          if (this.checkSubStandardTitleDuplicate(attributeIndex, value, standardIndex)) {
            this.$message.error(this.$t('loc.learnerProfileStandardTitleAlreadyExistsTip'));
            // 仍然允许数据同步，但标记为有重复错误
          }
        }
      }

      // 同步更新所有年龄组的相应标准
      this.ageGroupRubrics.forEach(ageGroup => {
        const attribute = ageGroup.details && ageGroup.details[attributeIndex];
        if (attribute) {
          if (standardIndex === -1) {
            // 没有子标准的情况，直接修改属性本身
            this.$set(attribute, field, value);
          } else if (attribute.subStandards && attribute.subStandards[standardIndex]) {
            // 有子标准的情况，修改对应的子标准
            this.$set(attribute.subStandards[standardIndex], field, value);
          }
        }
      });
      this.hasModified = true;
      this.resizeTableAndTextareas()
    },

    // 期望数据变化处理 - 只更新特定年龄组的期望
    onExpectationChange(attributeIndex, standardIndex, ageGroup, value) {
      const targetAgeGroup = this.ageGroupRubrics.find(ag => ag.ageGroup === ageGroup);
      if (targetAgeGroup && targetAgeGroup.details && targetAgeGroup.details[attributeIndex]) {
        const attribute = targetAgeGroup.details[attributeIndex];
        
        if (standardIndex === -1) {
          // 没有子标准的情况，直接修改属性本身的期望
          this.$set(attribute, 'expectations', value);
        } else if (attribute.subStandards && attribute.subStandards[standardIndex]) {
          // 有子标准的情况，修改对应的子标准期望
          this.$set(attribute.subStandards[standardIndex], 'expectations', value);
        }
      }
      this.hasModified = true;
      this.resizeTableAndTextareas()
    },

    // 添加新属性
    addAttribute() {
      // 确保所有年龄组都有details
      this.ageGroupRubrics.forEach(ageGroup => {
        if (!ageGroup.details) {
          this.$set(ageGroup, 'details', []);
        }
      });

      // 获取当前属性数量，用于确定新属性的位置
      const currentAttributeCount = this.ageGroupRubrics[0] && this.ageGroupRubrics[0].details ? this.ageGroupRubrics[0].details.length : 0;

      let newAttribute;

      // 根据当前是否有子标准来决定创建什么样的结构
      if (this.hasSubStandards) {
        // 如果当前有子标准，创建带有subStandards的结构
        newAttribute = {
          title: '',
          description: '',
          expectations: '',
          subStandards: [{
            title: '',
            description: '',
            expectations: ''
          }]
        };
      } else {
        // 如果当前没有子标准，创建简单的扁平结构
        newAttribute = {
          title: '',
          description: '',
          expectations: ''
        };
      }

      // 同时在所有年龄组中添加新属性
      this.ageGroupRubrics.forEach(ageGroup => {
        const currentDetails = ageGroup.details;
        this.$set(ageGroup, 'details', [...currentDetails, JSON.parse(JSON.stringify(newAttribute))]);
      });

      this.hasModified = true;

      // 滚动到新添加的行
      this.$nextTick(() => {
        // 新属性添加在最后，所以 attributeIndex 是 currentAttributeCount
        const targetAttributeIndex = currentAttributeCount;
        const targetStandardIndex = this.hasSubStandards ? 0 : -1;
        this.scrollToTargetRow(targetAttributeIndex, targetStandardIndex);
        this.resizeTableAndTextareas();
      });
    },

    // 在指定行前添加新标准
    addSubStandardBeforeRow(attributeIndex, standardIndex) {
      const newSubStandard = {
        title: '',
        description: '',
        expectations: ''
      };

      // 同时在所有年龄组中添加新子标准
      this.ageGroupRubrics.forEach(ageGroup => {
        const attribute = ageGroup.details && ageGroup.details[attributeIndex];
        if (attribute) {
          if (!attribute.subStandards) {
            this.$set(attribute, 'subStandards', []);
          }

          // 在指定位置前插入新标准
          const currentSubStandards = [...attribute.subStandards];
          currentSubStandards.splice(standardIndex, 0, JSON.parse(JSON.stringify(newSubStandard)));
          this.$set(attribute, 'subStandards', currentSubStandards);
        }
      });

      this.hasModified = true;
      this.resizeTableAndTextareas();
    },

    // 在指定行后添加新标准
    addSubStandardAfterRow(attributeIndex, standardIndex) {
      const newSubStandard = {
        title: '',
        description: '',
        expectations: ''
      };

      // 同时在所有年龄组中在指定位置后插入新标准
      this.ageGroupRubrics.forEach(ageGroup => {
        const attribute = ageGroup.details && ageGroup.details[attributeIndex];
        if (attribute) {
          if (!attribute.subStandards) {
            this.$set(attribute, 'subStandards', []);
          }

          // 在指定位置插入新标准
          const currentSubStandards = [...attribute.subStandards];
          currentSubStandards.splice(standardIndex + 1, 0, JSON.parse(JSON.stringify(newSubStandard)));
          this.$set(attribute, 'subStandards', currentSubStandards);
        }
      });

      this.hasModified = true;
      this.resizeTableAndTextareas();
    },

    // 添加新标准
    addSubStandard(attributeIndex) {
      const newSubStandard = {
        title: '',
        description: '',
        expectations: ''
      };

      // 获取当前属性的子标准数量，用于计算新添加的行的位置
      const baseAttribute = this.ageGroupRubrics[0] && this.ageGroupRubrics[0].details && this.ageGroupRubrics[0].details[attributeIndex];
      const currentSubStandardsLength = baseAttribute && baseAttribute.subStandards ? baseAttribute.subStandards.length : 0;

      // 同时在所有年龄组中添加新子标准
      this.ageGroupRubrics.forEach(ageGroup => {
        const attribute = ageGroup.details && ageGroup.details[attributeIndex];
        if (attribute) {
          if (!attribute.subStandards) {
            this.$set(attribute, 'subStandards', []);
          }

          // 使用 Vue.set 来添加新元素以确保响应性
          const currentSubStandards = attribute.subStandards;
          this.$set(attribute, 'subStandards', [...currentSubStandards, JSON.parse(JSON.stringify(newSubStandard))]);
        }
      });

      this.hasModified = true;

      // 滚动到新添加的行
      this.$nextTick(() => {
        this.scrollToTargetRow(attributeIndex, currentSubStandardsLength);
        this.resizeTableAndTextareas();
      });
    },

    // 删除属性
    removeAttribute(attributeIndex, skipConfirm = false) {
      const executeDelete = () => {
        // 同时从所有年龄组中删除属性
        this.ageGroupRubrics.forEach(ageGroup => {
          const currentDetails = ageGroup.details;
          if (currentDetails && currentDetails.length > attributeIndex) {
            const newDetails = currentDetails.filter((_, index) => index !== attributeIndex);
            this.$set(ageGroup, 'details', newDetails);
          }
        });

        this.hasModified = true;
        this.resizeTableAndTextareas()
      };

      if (skipConfirm) {
        // 跳过确认直接删除（用于删除最后一个子标准时自动删除属性）
        executeDelete();
      } else {
        // 显示确认对话框
        this.$confirm(
          this.$t('loc.learnerProfileDeleteItemTips'),
          this.$t('loc.confirmation'),
          {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel'),
            confirmButtonClass: 'el-button--danger',
            type: 'warning',
            closeOnClickModal: false,
            distinguishCancelAndClose: true,
            customClass: 'width-600-confirm-message-box'
          }
        ).then(() => {
          executeDelete();
        }).catch(() => {});
      }
    },

    // 删除子标准
    removeSubStandard(attributeIndex, standardIndex) {
      // 检查删除后是否还有子标准（使用第一个年龄组作为基准）
      const baseAttribute = this.ageGroupRubrics[0] && this.ageGroupRubrics[0].details && this.ageGroupRubrics[0].details[attributeIndex];
      const willBeLastStandard = baseAttribute && baseAttribute.subStandards && baseAttribute.subStandards.length === 1;
      
      this.$confirm(
        this.$t('loc.learnerProfileDeleteItemTips'),
        this.$t('loc.confirmation'),
        {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          closeOnClickModal: false,
          distinguishCancelAndClose: true,
          customClass: 'width-600-confirm-message-box'
        }
      ).then(() => {
        if (willBeLastStandard) {
          // 如果是最后一个子标准，删除整个属性（跳过确认）
          this.removeAttribute(attributeIndex, true);
        } else {
          // 否则只删除子标准 - 同时从所有年龄组中删除
          this.ageGroupRubrics.forEach(ageGroup => {
            const attribute = ageGroup.details && ageGroup.details[attributeIndex];
            if (attribute && attribute.subStandards && attribute.subStandards.length > standardIndex) {
              const newSubStandards = attribute.subStandards.filter((_, index) => index !== standardIndex);
              this.$set(attribute, 'subStandards', newSubStandards);
            }
          });
          this.hasModified = true;
          this.resizeTableAndTextareas()
        }
      }).catch(() => {});
    },

    // 删除期望
    removeExpectation(ageGroup) {
      this.$confirm(
        this.$t('loc.learnerProfileDeleteItemTips'),
        this.$t('loc.confirmation'),
        {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          closeOnClickModal: false,
          distinguishCancelAndClose: true,
          customClass: 'width-600-confirm-message-box'
        }
      ).then(() => {
        this.ageGroupRubrics = this.ageGroupRubrics.filter(item => item.ageGroup !== ageGroup)
        // 关键：用新数组整体替换，确保子组件 watch 能响应
        this.ageGroups = this.ageGroups.map(item =>
          item.ageGroup === ageGroup ? { ...item, selected: false } : item
        )
        this.resizeTableAndTextareas()
      }).catch(() => {});
    },

    // 处理添加标准的下拉菜单命令
    handleAddStandardCommand(command) {
      const { type, attributeIndex, standardIndex } = command;
      
      let targetAttributeIndex, targetStandardIndex;

      if (type === 'above') {
        this.addSubStandardBeforeRow(attributeIndex, standardIndex);
        targetAttributeIndex = attributeIndex;
        targetStandardIndex = standardIndex; // 新行插入到原位置
      } else if (type === 'below') {
        this.addSubStandardAfterRow(attributeIndex, standardIndex);
        targetAttributeIndex = attributeIndex;
        targetStandardIndex = standardIndex + 1; // 新行插入到原位置+1
      }
      setTimeout(() => {
        // 滚动到新添加的行
        this.$nextTick(() => {
          this.scrollToTargetRow(targetAttributeIndex, targetStandardIndex);
          this.resizeTableAndTextareas();
        });
      }, 50);
    },

    // 处理添加属性的下拉菜单命令
    handleAddAttributeCommand(command) {
      const { type, attributeIndex } = command;
      let targetAttributeIndex, targetStandardIndex;
      if (type === 'above') {
        this.addAttributeBeforeRow(attributeIndex);
        targetAttributeIndex = attributeIndex; // 新行插入到原位置
      } else if (type === 'below') {
        this.addAttributeAfterRow(attributeIndex);
        targetAttributeIndex = attributeIndex + 1; // 新行插入到原位置+1
      }
      // 根据是否有子标准确定标准索引
      targetStandardIndex = this.hasSubStandards ? 0 : -1;
      setTimeout(() => {
        // 滚动到新添加的行
        this.$nextTick(() => {
          this.scrollToTargetRow(targetAttributeIndex, targetStandardIndex);
          this.resizeTableAndTextareas();
        });
      }, 50);
    },

    // 在指定行前添加新属性
    addAttributeBeforeRow(attributeIndex) {
      // 确保所有年龄组都有details
      this.ageGroupRubrics.forEach(ageGroup => {
        if (!ageGroup.details) {
          this.$set(ageGroup, 'details', []);
        }
      });

      let newAttribute;

      // 根据当前是否有子标准来决定创建什么样的结构
      if (this.hasSubStandards) {
        // 如果当前有子标准，创建带有subStandards的结构
        newAttribute = {
          title: '',
          description: '',
          expectations: '',
          subStandards: [{
            title: '',
            description: '',
            expectations: ''
          }]
        };
      } else {
        // 如果当前没有子标准，创建简单的扁平结构
        newAttribute = {
          title: '',
          description: '',
          expectations: ''
        };
      }

      // 同时在所有年龄组的指定位置前插入新属性
      this.ageGroupRubrics.forEach(ageGroup => {
        const currentDetails = [...ageGroup.details];
        currentDetails.splice(attributeIndex, 0, JSON.parse(JSON.stringify(newAttribute)));
        this.$set(ageGroup, 'details', currentDetails);
      });

      this.hasModified = true;
      this.resizeTableAndTextareas();
    },

    // 在指定行后添加新属性
    addAttributeAfterRow(attributeIndex) {
      // 确保所有年龄组都有details
      this.ageGroupRubrics.forEach(ageGroup => {
        if (!ageGroup.details) {
          this.$set(ageGroup, 'details', []);
        }
      });

      let newAttribute;

      // 根据当前是否有子标准来决定创建什么样的结构
      if (this.hasSubStandards) {
        // 如果当前有子标准，创建带有subStandards的结构
        newAttribute = {
          title: '',
          description: '',
          expectations: '',
          subStandards: [{
            title: '',
            description: '',
            expectations: ''
          }]
        };
      } else {
        // 如果当前没有子标准，创建简单的扁平结构
        newAttribute = {
          title: '',
          description: '',
          expectations: ''
        };
      }

      // 同时在所有年龄组的指定位置后插入新属性
      this.ageGroupRubrics.forEach(ageGroup => {
        const currentDetails = [...ageGroup.details];
        currentDetails.splice(attributeIndex + 1, 0, JSON.parse(JSON.stringify(newAttribute)));
        this.$set(ageGroup, 'details', currentDetails);
      });

      this.hasModified = true;
      this.resizeTableAndTextareas();
    },

    // 滚动到指定位置的行
    scrollToTargetRow(attributeIndex, standardIndex) {
      this.scrollToRow(attributeIndex, standardIndex, (targetRowElement) => {
        // 聚焦到第一个输入框
        const firstInput = targetRowElement.querySelector('.el-input__inner, .el-textarea__inner');
        if (firstInput) {
          firstInput.focus();
        }
      });
    },

    // 滚动到错误位置
    scrollToErrorPosition(errorInfo) {
      const { 
        attributeIndex, 
        standardIndex, 
        missingAttribute, 
        missingStandard,
        duplicateAttribute,
        duplicateStandard
      } = errorInfo;
      
      this.scrollToRow(attributeIndex, standardIndex, (targetRowElement) => {
        // 根据错误类型聚焦到对应的错误字段
        let targetInput = null;
        let errorMessage = '';

        if (missingAttribute || duplicateAttribute) {
          // 聚焦到属性标题输入框
          const attributeCell = targetRowElement.querySelector('td:first-child .el-input__inner, td:first-child .el-textarea__inner');
          targetInput = attributeCell;
          if (missingAttribute) {
            errorMessage = this.$t('loc.pleaseExpectations');
          } else if (duplicateAttribute) {
            errorMessage = this.$t('loc.learnerProfileAttributeTitleAlreadyExistsTip');
          }
        } else if ((missingStandard || duplicateStandard) && this.hasSubStandards) {
          // 聚焦到子标准标题输入框
          const standardCell = targetRowElement.querySelector('td:nth-child(2) .el-input__inner, td:nth-child(2) .el-textarea__inner');
          targetInput = standardCell;
          if (missingStandard) {
            errorMessage = this.$t('loc.pleaseExpectations');
          } else if (duplicateStandard) {
            errorMessage = this.$t('loc.learnerProfileStandardTitleAlreadyExistsTip');
          }
        }
        
        if (targetInput) {
          // 显示对应的错误消息
          if (errorMessage) {
            this.$message.error(errorMessage);
          }
          
          // 使用 Vue 响应式方式设置错误状态
          this.errorInputPosition = {
            attributeIndex,
            standardIndex,
            missingAttribute,
            missingStandard,
            duplicateAttribute,
            duplicateStandard
          };
          
          // 3秒后移除错误状态
          setTimeout(() => {
            this.errorInputPosition = null;
          }, 3000);
        }
      });
    },

    // 基础滚动方法 - 滚动到指定的属性和标准索引位置
    scrollToRow(attributeIndex, standardIndex, callback = null) {
      try {
        const table = this.$refs.rubricsTable;
        if (!table) return false;
        // 在扁平化数据中找到对应的行
        const targetRowIndex = this.flatRows.findIndex(row => 
          row.attributeIndex === attributeIndex && 
          row.standardIndex === standardIndex
        );
        if (targetRowIndex === -1) return false;
        const tableBodyWrapper = table.$el.querySelector('.el-table__body-wrapper');
        const tableBody = table.$el.querySelector('.el-table__body tbody');
        if (tableBodyWrapper && tableBody) {
          // 获取目标行的 DOM 元素
          const targetRowElement = tableBody.children[targetRowIndex];
          if (targetRowElement) {
            // 计算目标行的位置
            const rowOffsetTop = targetRowElement.offsetTop;
            const containerHeight = tableBodyWrapper.clientHeight;
            const scrollTop = Math.max(0, rowOffsetTop - containerHeight / 3);

            // 滚动到目标位置
            tableBodyWrapper.scrollTop = scrollTop;

            // 如果提供了回调函数，则在滚动完成后执行
            if (callback && typeof callback === 'function') {
              setTimeout(() => {
                callback(targetRowElement);
              }, 150);
            }
            return true;
          }
        }
      } catch (error) {
      }
      return false;
    },

    // 判断输入框是否为错误状态
    isErrorInput(attributeIndex, standardIndex, type) {
      if (!this.errorInputPosition) {
        return false;
      }

      const isAttributeMatch = this.errorInputPosition.attributeIndex === attributeIndex;
      const isStandardMatch = this.errorInputPosition.standardIndex === standardIndex;

      if (type === 'attribute') {
        // 属性标题输入框错误检查
        return isAttributeMatch && 
               standardIndex === -1 && 
               (this.errorInputPosition.missingAttribute || this.errorInputPosition.duplicateAttribute);
      } else if (type === 'standard') {
        // 子标准标题输入框错误检查
        return isAttributeMatch && 
               isStandardMatch && 
               (this.errorInputPosition.missingStandard || this.errorInputPosition.duplicateStandard);
      }

      return false;
    },
  }
}
</script>
<style scoped lang="less">
.loading-content-height {
  height:500px !important;
  overflow: hidden;
}

.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: absolute;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(5px);
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  cursor: pointer;
}

.loading-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  background-color: transparent;
  filter: none !important;
  position: relative;
}

.image-container {
  width: 170px;
  height: 178px;
  margin-bottom: 20px;
  filter: none !important;
  position: relative;
  z-index: 20;
}

.loader-container {
  margin-top: 10px;
  filter: none !important;
  position: relative;
  z-index: 20;
}

.loading-text {
  font-size: 18px;
  color: #676879;
  margin-top: 12px;
}

.loader {
  display: block;
  --height-of-loader: 12px;
  width: 600px;
  @media screen and (max-width: 768px) {
    width:320px !important;
  }
  height: var(--height-of-loader);
  border-radius: 15px;
  background-color: rgba(0, 0, 0, 0.2);
  position: relative;
}

.loader::before {
  content: "";
  position: absolute;
  background: linear-gradient(90deg, #AA89F2 0%, #10B3B7 100%);
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  border-radius: 15px;
  animation: moving 3s ease-in-out infinite;
}

@keyframes moving {
  50% {
    width: 100%;
  }

  100% {
    width: 0;
    right: 0;
    left: unset;
  }
}
.el-textarea__inner {
  padding: 0px 0px !important;
}
::v-deep .profile-textarea {
  .el-textarea__inner {
    resize: none !important;
    overflow-y: auto;
    padding: 12px !important;
  }
}

/deep/ .age-rubric {
  .el-textarea__inner {
    display: none; // 隐藏原来的 textarea
  }
}

/deep/ .manage-learner-profile-dialog {
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  .el-dialog__header {
    padding: 24px 24px 0;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: var(--color-text-primary);
  }
  .el-radio {
    margin-bottom: 0;
  }
  .el-dialog__body {
    overflow: hidden;
    padding: 0;
    padding-top: 24px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;

    .footer-btns {
      padding: 0 24px;
      bottom: 0;
      background: white;
    }
  }
}
.max-w-420 {
  max-width: 420px;
}

.regenerate-icon {
  color: #10B3B7;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    opacity: 0.8;
  }

  &.is-loading {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rubrics-table {
  margin-top: 12px;
  margin-bottom: 12px;

  // 定制 el-table 样式
  /deep/ .el-table {
    // 移除悬浮高亮效果
    tr:hover, tr:focus {
      background-color: #FFFFFF !important;
    }

    tr.el-table__row:hover > td {
      background-color: #FFFFFF !important;
    }

    td, th {
      border-color: #DCDFE6;
    }

    // 确保右侧边框线不消失
    &::after {
      display: none !important;
    }

    thead {
      tr {
        th {
          background-color: #F5F7FA;
          color: #111c1c;
          font-weight: 600;
          font-size: 14px;
          height: auto;
          line-height: 1.5;
        }
      }
    }

    tbody {
      tr {
        td {
          height: auto;
          line-height: 1.5;
        }
      }
    }

    .el-input__inner {
      border: none;
      background: transparent;
      padding: 4px 0 !important; // 增加输入框上下间距
      height: auto;
      line-height: 1.5;
      font-size: 14px;
      color: #111c1c;
    }

    // 表格第二列描述信息样式，确保文本完整显示
    .el-table__row td:nth-child(2) .el-input__inner {
      white-space: normal;
      word-break: break-word;
      height: auto;
      min-height: 40px;
      padding: 4px 0 !important; // 增加输入框上下间距
    }

    .input-wrapper {
      display: flex;
      align-items: center; // 使删除图标垂直居中
      width: 100%;

      .el-input {
        flex: 1;
      }

      .delete-icon {
        color: #F56C6C;
        font-size: 16px;
        cursor: pointer;
        margin-left: 8px;
      }
    }

    // 修复边框重叠问题
    border-collapse: collapse;

    &::before {
      display: none;
    }

    .is-leaf {
      border-bottom: 1px solid #DCDFE6;
    }
  }

  .add-row-cell {
    text-align: center;
    background-color: #FFFFFF;
    padding: 5px;
    border: 1px solid #DCDFE6;
  }

  .add-row-btn {
    color: #10B3B7;
    font-size: 14px;
    display: inline-block;
    cursor: pointer;
    transition: opacity 0.2s;
    padding-top: 0 !important;
    padding-bottom: 0 !important;

    &:hover {
      opacity: 0.8;
    }
  }
}

// 修复 Element UI 表格样式覆盖
/deep/ .el-table--border td:first-child .cell,
/deep/ .el-table--border th:first-child .cell {
  padding-left: 16px;
}

/deep/ .el-table--border td:last-child .cell,
/deep/ .el-table--border th:last-child .cell {
  padding-right: 16px;
}

/deep/ .el-table__fixed-right::before,
/deep/ .el-table__fixed::before {
  display: none;
}

/* 强制使用浏览器默认滚动条样式 */
/deep/ .manage-learner-profile-dialog .el-table__body-wrapper {
  scrollbar-width: auto !important;
}

/deep/ .manage-learner-profile-dialog .el-table__body-wrapper::-webkit-scrollbar {
  width: 15px !important;
  height: 15px !important;
  background-color: transparent !important;
}

/deep/ .manage-learner-profile-dialog .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #c1c1c1 !important;
  border: 2.5px solid #f1f1f1 !important;
  border-radius: 10px !important;
  background-clip: padding-box !important;
}

/deep/ .el-table .cell {
  word-break: normal;
  line-height: 1.5;
  padding: 4px 8px !important; // 增加单元格内容上下间距
}

/deep/ .el-table td .cell,
/deep/ .el-table th .cell {
  overflow: visible;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  span {
    font-size: 14px;
    font-weight: 400;
    color: #111c1c;
  }
}

.font-size-16.font-bold.line-height-22 {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }
}

.color-dll-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 添加回主按钮样式 */
/deep/ .el-button--primary.el-button--small {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
  background: #10B3B7;
  border-color: #10B3B7;

  &:hover {
    opacity: 0.8;
    background: #10B3B7;
    border-color: #10B3B7;
  }
}

/* 移除textarea边框 */
.el-textarea__inner {
  border: none !important;
  box-shadow: none !important;
}

/deep/ .el-table tr:hover,
/deep/ .el-table--enable-row-hover .el-table__body tr:hover > td,
/deep/ .el-table--enable-row-hover .el-table__body tr:hover {
  background-color: #FFFFFF !important;
}

/deep/ .el-table__body tr:hover > td {
  background-color: #FFFFFF !important;
}

/deep/ .rubrics-table table tr:hover {
  background-color: #FFFFFF !important;
}

/* 禁用Element表格的悬浮高亮 */
/deep/ .el-table__body tr.hover-row > td,
/deep/ .el-table__body tr.hover-row.current-row > td,
/deep/ .el-table__body tr.hover-row.el-table__row--striped > td,
/deep/ .el-table__body tr.hover-row.el-table__row--striped.current-row > td {
  background-color: #FFFFFF !important;
}



/* 确保表单中的输入框悬浮样式正确 */
/deep/ .el-input__inner {
  &:hover, &:focus {
    background-color: transparent !important;
    border-color: #DCDFE6 !important;
  }
}

/* 修改悬浮时输入框样式，避免高度变化导致内容浮动 */
/deep/ td .el-input__inner {
  border: 1px solid transparent !important; /* 默认透明边框 */
  padding: 12px 0px !important; /* 统一内边距 */
  box-sizing: border-box !important;

  &:hover, &:focus {
    border: 1px dashed #10B3B7 !important; /* 悬浮时显示蓝色虚线边框 */
    /* 不改变内边距，保持高度一致 */
  }
}

.refresh-button {
  border: 2px solid #10B3B7;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.refresh-button.el-button--medium {
  height: 36px !important;
  width: 36px !important;
}

/* 修改文本域样式，避免悬浮时内容上下浮动 */
/deep/ .el-table__row .el-textarea__inner {
  border: 1px solid transparent !important; /* 默认透明边框 */
  padding: 4px 0px !important; /* 统一内边距 */
  background-color: transparent !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
  color: #111c1c !important;
  resize: none !important;
  box-sizing: border-box !important;
  min-height: 24px !important;
}
/deep/ .el-table__row:hover .el-textarea__inner,
/deep/ .el-table__row .el-textarea__inner:focus {
  border: 1px dashed #10B3B7 !important; /* 悬浮时显示蓝色虚线边框 */
}

/deep/ .error-input-wrapper .el-textarea__inner {
  border: 1px dashed #F56C6C !important;
}

/* 空内容显示样式 */
.empty-content-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-top: 12px;
  margin-bottom: 16px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.empty-icon {
  font-size: 48px;
  color: #C0C4CC;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
  margin-bottom: 16px;
}

.add-content-btn {
  background-color: #10B3B7;
  border-color: #10B3B7;

  &:hover, &:focus {
    background-color: #0D9A9E;
    border-color: #0D9A9E;
    opacity: 0.9;
  }

  i {
    margin-right: 4px;
  }
}



/deep/ .el-table--border th,
/deep/ .el-table--border td {
  border-right: 1px solid #DCDFE6 !important;
}

/deep/ .el-table--border th.is-leaf {
  border-bottom: 1px solid #DCDFE6 !important;
}
/* 确保空白页生成按钮在禁用状态下样式明显 */
.empty-generate-btn {
  background-color: #10B3B7;
  border-color: #10B3B7;
  color: #FFFFFF;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-block;
  margin-top: 16px;
  margin-bottom: 16px;
}

.empty-generate-btn:hover {
  opacity: 0.8;
}

.empty-generate-btn.is-disabled,
.empty-generate-btn[disabled] {
  background-color: #CCCCCC !important;
  border-color: #CCCCCC !important;
  color: #FFFFFF !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* 年龄组单元格样式 */
.age-group-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 60px;
  background-color: #F5F7FA;
  border-radius: 4px;
  margin: 4px;
}

.age-group-name {
  font-size: 16px;
  font-weight: 600;
  color: #10B3B7;
  text-align: center;
  line-height: 1.4;
  padding: 8px 12px;
}

/* 操作图标样式 */
.add-icon {
  font-size: 16px !important;
  cursor: pointer !important;
  flex-shrink: 0 !important;
  color: #10B3B7 !important; // 主题色
  align-self: center !important; // 垂直居中
}

 /* 在表格行外的添加图标（如底部添加按钮）始终可见 */
.add-row-cell .add-icon {
  opacity: 1 !important;
  margin-right: 4px !important;
  margin-left: 0 !important;
}

/* 删除图标样式保持原有的delete-icon类 */
.delete-icon {
  font-size: 16px !important;
  cursor: pointer !important;
  margin-left: 8px !important;
  flex-shrink: 0 !important;
  color: #F56C6C !important; // danger 主题色
  align-self: center !important; // 垂直居中
}

/* 删除期望图标样式 - 默认隐藏，hover时显示 */
.delete-expectation-icon {
  font-size: 16px !important;
  cursor: pointer !important;
  margin-left: 8px !important;
  flex-shrink: 0 !important;
  color: #F56C6C !important; // danger 主题色
  opacity: 0 !important; // 默认隐藏
  transition: opacity 0.2s !important;
  align-self: center !important; // 垂直居中

  &:hover {
    opacity: 0.8 !important;
  }
}

/* 添加表格行悬浮样式以显示操作图标 */
/deep/ .el-table__row:hover .add-icon,
/deep/ .el-table__row:hover .delete-icon {
  opacity: 1 !important;
}

/* 期望列头悬浮时显示删除期望图标 */
/deep/ .el-table__header th:hover .delete-expectation-icon {
  opacity: 1 !important;
}

/* 表格行悬浮时显示下拉菜单中的添加图标 */
/deep/ .el-table__row:hover .el-dropdown .add-icon {
  opacity: 1 !important;
}

/* 下拉菜单项样式 */
/deep/ .el-dropdown-menu {
  .el-dropdown-menu__item {
    font-size: 14px !important;
    color: #606266 !important;
    padding: 8px 16px !important;
    
    &:hover {
      background-color: #F5F7FA !important;
      color: #10B3B7 !important;
    }
  }
}
</style>


