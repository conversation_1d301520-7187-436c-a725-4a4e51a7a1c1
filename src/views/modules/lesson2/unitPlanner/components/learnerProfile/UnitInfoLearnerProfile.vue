<template>
  <!-- 选择校训 -->
  <el-form-item prop="profile" :class="{'add-margin-t-16': !isLessonAssistant}">
    <div slot="label" class="flex-row-center">
      <span :class="{'font-size-14': isLessonAssistant}">{{ $t('loc.learnerProfile') }}</span>
      <!--
        :content="$t('loc.learnerProfileUseTip')"
       -->
      <el-tooltip effect="dark" placement="top">
        <div slot="content" class="max-width-320">
          <div>{{ isLessonAssistant ? $t('loc.lessonLearnerProfileUseTip') : $t('loc.learnerProfileUseTip') }}</div>
          <!-- 若有选择的校训内容，显示 My settings -->
          <div v-if="!disabled && gradeText && gradeText !== -1 && gradeText !== -2 && gradeText !== -3"
               class="lg-pointer font-color-primary lg-underline"
               @click="addAgency(false)">
            My settings
          </div>
          <!-- 若当前年龄符合校训年龄且没有选择的校训内容，显示 Setup now -->
          <div v-else-if="isAdmin && gradeText === -2 && !disabled"
               class="lg-pointer font-color-primary lg-underline"
               @click="addAgency(false)">
            Setup now
          </div>
        </div>
        <i
          class="lg-icon lg-icon-info lg-margin-left-4 color-icon lg-pointer font-400-important"
        ></i>
      </el-tooltip>
      <span
        class="newPoint point-relative"
        v-if="guideFeatures && guideFeatures.showLearnerProfileGuideNewTag"
      >
        {{ $t('loc.new') }}
      </span>
    </div>
    <div
      class="profile-result border-radius-4 bg-white w-full position-relative"
      :class="{
        'profile-result--disabled': disabled
      }"
    >
      <!-- 不支持年龄段 -->
      <div class="font-weight-400 font-size-14 line-height-22 lg-pa-6" v-if="gradeText === -1">
        <span class="opacity-40">
          {{ $t('loc.learnerProfileNotSupport') }}
        </span>
      </div>
      <!-- 未启用校训 -->
      <div class="font-weight-400 font-size-14 line-height-22" v-else-if="!disabled && gradeText === -3">
        <span class="opacity-40">
          {{ $t('loc.learnerProfileNotEnabled') }}
        </span>
      </div>
      <!-- 已设置校训 -->
      <template v-else-if="gradeText && gradeText !== -2">

        <!-- 当非禁用状态下，可以选择校训 -->
        <div >
          <RubricsRow
            ref="rubricsRowRef"
            :rubricsOptions="rubricsOptions"
            :selectedRubrics="selectedRubrics"
            :showSelectedRubrics="false"
            :disabled="disabled"
            :maxSelectCount="maxSelectCount"
            :isLessonAssistant="isLessonAssistant"
            @updateSelectedRubrics="updateSelectedRubrics"
            @showPogTransformGuide="showPogTransformGuide"
          />
        </div>
      </template>
      <!-- 禁用状态 -->
      <div class="font-weight-400 font-size-14 line-height-22 lg-padding-8" v-else-if="gradeText === -3">
        <span class="opacity-40">
          {{ $t('loc.learnerProfileNotSetTipsLong', { grade: unitInfo.grade }) }}
        </span>
      </div>
      <!-- 年龄组在校训支持范围内，但校训未设置 -->
      <div class="font-weight-400 font-size-14 line-height-22 lg-padding-8" v-else>
        <template v-if="isAdmin">
          <span
            @click="addAgencyWithLoading"
            class="lg-pointer font-color-primary set-center"
            v-if="!disabled"
          >
            <i v-if="isLoading" class="el-icon-loading" style="margin-right: 4px;"></i>
            {{ $t('loc.learnerProfileSetupNow') }}
          </span>
          <span class="opacity-40" v-else>
          {{ $t('loc.learnerProfileNotAdminTips') }}
        </span>
        </template>

      </div>
    </div>

    <!-- 校训标签回显区域 - 移到框外 -->
    <div v-if="showRubricsDisplayArea"
         class="bg bg-light m-t-sm wrapper-sm rubrics-display-area">
      <div class="flex-grow-1">
        <div>
          <!-- 展开显示所有实际选择的子标准 -->
          <div v-for="displayItem in displayRubrics"
               :key="displayItem.key"
               class="showDom selectPortrait display-ib" :class="{'selectPortrait-disabled': disabled }">
            <el-tooltip popper-class="max-width-400" effect="dark" placement="top" :open-delay="500">
              <div slot="content" v-html="displayItem.tooltip"/>
              <span>{{ displayItem.title }}</span>
            </el-tooltip>
            <i @click="removeDisplayItem(displayItem)" v-if="!disabled"
               class="flag el-icon-close rubrics-close lg-pointer"
               style="margin-left:4px"></i>
          </div>
        </div>
      </div>
    </div>

    <LearnerProfilePopup ref="learnerProfilePopupRef" :isLessonAssistant="isLessonAssistant"></LearnerProfilePopup>
  </el-form-item>
</template>

<script>
import { mapState } from 'vuex'
import LearnerProfilePopup from './LearnerProfilePopup.vue'
import { equalsIgnoreCase } from '@/utils/common'
import RubricsRow from './RubricsRow.vue'
import { TAILOR_UNIT_AGE_GROUPS } from '@/utils/const'
import tools from '@/utils/tools'

export default {
  props: {
    unitInfo: {
      type: Object,
      default: () => ({})
    },
    unitProgress: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    LearnerProfilePopup,
    RubricsRow
  },
  data() {
    return {
      selectedRubrics: this.unitInfo.newRubrics,
      rubricsOptions: [],
      isLoading: false,
      // 每个年级对应的上次选择校训
      lastSelectedRubrics: {},
      // 防抖定时器
      refreshTimer: null
    }
  },
  created() {
    const rubrics = ((this.agencyLearnerProfile || {}).newAgeGroupRubrics || []).find(
        (v) => v.ageGroup === this.unitInfo.grade
      )
      if (rubrics && rubrics.details) {
        // 将校训详情转换为 RubricsRow 组件所需的选项格式
        if (rubrics.details && Array.isArray(rubrics.details) && rubrics.details.length > 0) {
          this.rubricsOptions = rubrics.details.map(item => ({
            title: item.title,
            description: item.description,
            expectations: item.expectations,
            subStandards: (item.subStandards || []).map(subStandard => ({
              title: subStandard.title,
              description: subStandard.description,
              expectations: subStandard.expectations
            }))
          }));
        }
      }
      // 记住上次选择的校训
      if (this.unitInfo.grade) {
        this.lastSelectedRubrics[this.unitInfo.grade] = this.unitInfo.newRubrics
      }
  },
  computed: {
    ...mapState({
      agencyLearnerProfile: (state) => state.lesson.agencyLearnerProfile, // 校训信息
      isCG: (state) => state.curriculum.isCG, // 是否是 CG
      currentUser: (state) => state.user.currentUser, // 当前用户
      guideFeatures: (state) => state.common.guideFeatures // 功能引导
    }),
    isAdmin() {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    getDisplayRubrics() {
      return function (item) {
        // 添加防护性检查
        if (!item || !item.title) {
          return '';
        }
        
        let content = item.title;
        if (item.description) {
          content += '<br/>' + item.description
        }
        if (item.expectations) {
          content += '<br/><br/>Expectations<br/>' + item.expectations
        }
        return content;
      }
    },
    // 将树形结构的 selectedRubrics 转换为展开的平铺显示列表
    displayRubrics() {
      const result = [];
      if (!this.selectedRubrics || !Array.isArray(this.selectedRubrics)) {
        return result;
      }
      
      this.selectedRubrics.forEach(portrait => {
        // 添加防护性检查，确保 portrait 存在且有 title 属性
        if (!portrait || !portrait.title) {
          return; // 跳过无效的数据项
        }
        
        if (portrait.subStandards && Array.isArray(portrait.subStandards) && portrait.subStandards.length > 0) {
          // 如果有子标准，展开显示子标准
          portrait.subStandards.forEach(subStandard => {
            // 确保子标准也有 title 属性
            if (subStandard && subStandard.title) {
              result.push({
                key: `${portrait.title}_${subStandard.title}`,
                title: subStandard.title,
                tooltip: this.getDisplaySubStandardRubrics(subStandard),
                parentTitle: portrait.title,
                subStandard: subStandard,
                portrait: portrait,
                isSubStandard: true
              });
            }
          });
        } else {
          // 如果没有子标准，显示主标准
          result.push({
            key: portrait.title,
            title: portrait.title,
            tooltip: this.getDisplayRubrics(portrait),
            portrait: portrait,
            isSubStandard: false
          });
        }
      });
      return result;
    },
    showRubricsDisplayArea() {
      return (
        this.selectedRubrics &&
        this.selectedRubrics.length > 0 &&
        (
          (
            !this.disabled &&
            this.gradeText &&
            this.gradeText !== -1 &&
            this.gradeText !== -2 &&
            this.gradeText !== -3
          ) ||
          this.disabled
        )
      )
    },
    isLessonAssistant() {
      return this.unitInfo.isLessonAssistant || false
    },
    // 最大选择数量
    maxSelectCount() {
      return this.isLessonAssistant ? 3 : Math.min(parseInt(this.unitInfo.weekCount) * 3, 9)
    },
    gradeText() {
      // 确保 TAILOR_UNIT_AGE_GROUPS 不为 null
      if (!TAILOR_UNIT_AGE_GROUPS) {
        return -2;
      }

      const supportAgeGroups = TAILOR_UNIT_AGE_GROUPS.map((v) => v.ageGroup)
      const isSupport = supportAgeGroups.includes(this.unitInfo.grade)
      const hasProfileId = Boolean((this.agencyLearnerProfile || {}).id)
      // 找到相对应年龄组的校训
      let rubrics = ((this.agencyLearnerProfile || {}).newAgeGroupRubrics || []).find(
        (v) => v.ageGroup === this.unitInfo.grade || v.ageGroup === tools.getAgeStageByAgeGroup(this.unitInfo.grade)
      )

      // 如果没有找到相对应年龄组的，则找到第一个校训作为默认校训
      if (!rubrics && isSupport) {
        const defaultRubrics = JSON.parse(JSON.stringify((this.agencyLearnerProfile || {}).newAgeGroupRubrics || []))[0]
        if (defaultRubrics) {
          // 清空 defaultRubrics 中的期望值
          defaultRubrics.details.forEach(item => {
            item.expectations = ''
            item.subStandards.forEach(subStandard => {
              subStandard.expectations = ''
            })
          })
          rubrics = defaultRubrics
        }
      }

      if (!isSupport) {
        // 不支持年龄段
        // 将 selectedRubrics 和 rubricsOptions 设置为空
        this.selectedRubrics = []
        this.rubricsOptions = []
        return -1
      }

      if ((!hasProfileId) || (!rubrics)) {
        // 未设置校训
        // 将 selectedRubrics 和 rubricsOptions 设置为空
        this.selectedRubrics = []
        this.rubricsOptions = []
        return -2
      }

      if (this.disabled || this.unitProgress >= 20) {
        if (rubrics && rubrics.details) {
            // 将校训详情转换为 RubricsRow 组件所需的选项格式
            if (rubrics.details && Array.isArray(rubrics.details) && rubrics.details.length > 0) {
              this.rubricsOptions = rubrics.details
            }
          }
        if (this.unitInfo.newRubrics) {
          // 返回已设置的校训信息，并转换格式
          return {
            ageGroup: this.unitInfo.grade,
            ageGroupRubrics: this.unitInfo.newRubrics
          }
        }
        return -3
      }

      if (rubrics && rubrics.details) {
        // 将校训详情转换为 RubricsRow 组件所需的选项格式
        if (rubrics.details && Array.isArray(rubrics.details) && rubrics.details.length > 0) {
          this.rubricsOptions = rubrics.details
        }
        // 返回已设置的校训信息，并转换格式
        return {
          ageGroup: rubrics.ageGroup,
          ageGroupRubrics: rubrics.details
        }
      }
      // 年龄组在校训支持范围内，但校训未设置
      return -2
    }
  },
  watch: {
    gradeText: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        this.$emit('updateRubricsOptions', newValue)
      }
    },
    unitInfo: {
      deep: true,
      handler(newVal) {
        if (newVal.newRubrics && Array.isArray(newVal.newRubrics)) {
          this.selectedRubrics = newVal.newRubrics;
        }
      }
    },
    'unitInfo.grade': {
      deep: true,
      handler(newValue, oldValue) {
        if (newValue && oldValue && newValue !== oldValue) {
          // 年龄变化，清空校训
          this.updateSelectedRubrics([])
        }
      }
    },
    // 监听 agencyLearnerProfile 的变化，当从 My settings 保存后重新初始化数据
    agencyLearnerProfile: {
      deep: true,
      handler(newValue, oldValue) {
        // 确保在数据变化后重新初始化校训相关数据
        if (newValue && oldValue && newValue !== oldValue) {
          // 检查当前年龄组的校训数据是否真的有变化
          const currentGradeRubrics = ((newValue || {}).newAgeGroupRubrics || []).find(
            (v) => v.ageGroup === this.unitInfo.grade
          )
          const oldGradeRubrics = ((oldValue || {}).newAgeGroupRubrics || []).find(
            (v) => v.ageGroup === this.unitInfo.grade
          )
          
          // 只有当前年龄组的校训数据发生变化时才重新计算
          if (JSON.stringify(currentGradeRubrics) !== JSON.stringify(oldGradeRubrics)) {
            // 使用防抖，避免频繁更新
            if (this.refreshTimer) {
              clearTimeout(this.refreshTimer)
            }
            
            this.refreshTimer = setTimeout(() => {
              this.$nextTick(() => {
                // 强制重新计算数据
                this.refreshRubricsData()
                // 强制更新组件，确保 RubricsRow 和回显区域能够及时更新
                this.$forceUpdate()
              })
            }, 100) // 100ms 防抖
          }
        }
      }
    }
  },
  mounted() {
    // 确保初始化时同步一次值
    if (this.unitInfo.newRubrics && Array.isArray(this.unitInfo.newRubrics)) {
      this.selectedRubrics = this.unitInfo.newRubrics;
    }
  },
  methods: {
    // 显示 POG 转换引导
    showPogTransformGuide() {
      // 调接口隐藏引导
      let result = { 'features': ['POG_TRANSFORM_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result)
      // 更新引导状态
      this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
        ...this.guideFeatures,
        showPogTransformGuide: false
      })
      // 弹出引导弹窗
      this.$confirm(
        this.$t('loc.learnerProfileGenerateByGradeBandsTip'),
        this.$t('loc.learnerProfileGenerateByGradeBandsTitle'),
        {
          confirmButtonText: this.$t('loc.unitPlannerConfirmRegenerate'),
          cancelButtonText: this.$t('loc.close'),
          closeOnClickModal: false,
          distinguishCancelAndClose: true,
          customClass: 'width-600-confirm-message-box'
        }
      ).then(() => {
        // 打开校训生成弹窗，开始按照年龄段重新生成 POG 数据
        this.$refs.learnerProfilePopupRef && this.$refs.learnerProfilePopupRef.open(true)
      })
      .catch(() => {
        // 如果用户点击了取消或关闭，继续打开校训选择弹窗
        this.$refs.rubricsRowRef && this.$refs.rubricsRowRef.openDialog()
      })
    },
    // 生成子标准的 tooltip 内容
    getDisplaySubStandardRubrics(subStandard) {
      // 添加防护性检查
      if (!subStandard || !subStandard.title) {
        return '';
      }
      
      let content = `${subStandard.title}`;
      if (subStandard.description) {
        content += '<br/>' + subStandard.description;
      }
      if (subStandard.expectations) {
        content += '<br/><br/>Expectations<br/>' + subStandard.expectations;
      }
      return content;
    },
    // 删除回显区域中的项目
    removeDisplayItem(displayItem) {
      if (this.disabled || !displayItem) return;

      if (displayItem.isSubStandard && displayItem.portrait && displayItem.subStandard) {
        // 删除子标准
        this.removeSubStandard(displayItem.portrait, displayItem.subStandard);
      } else if (displayItem.portrait) {
        // 删除主标准
        this.removePortrait(displayItem.portrait);
      }
    },
    // 删除子标准
    removeSubStandard(portrait, subStandard) {
      if (this.disabled || !portrait || !portrait.title || !subStandard || !subStandard.title) return;

      // 找到对应的主标准并删除指定的子标准
      const updatedRubrics = this.selectedRubrics.map(item => {
        if (item && item.title === portrait.title) {
          const updatedSubStandards = (item.subStandards || []).filter(sub => 
            sub && sub.title !== subStandard.title
          );
          return updatedSubStandards.length > 0 ? {
            ...item,
            subStandards: updatedSubStandards
          } : null;
        }
        return item;
      }).filter(item => item !== null);

      this.updateSelectedRubrics(updatedRubrics);
    },
    // 更新选择的校训
    updateSelectedRubrics(selectedPortraits) {
      this.selectedRubrics = selectedPortraits;
      this.$emit('updateDisplayRubrics', this.displayRubrics)
      // 更新 unitInfo 中的校训内容
      this.$emit('updateRubrics', {
        ageGroup: this.unitInfo.grade,
        ageGroupRubrics: this.selectedRubrics
      });
      // 记住上次选择的校训
      this.lastSelectedRubrics[this.unitInfo.grade] = this.selectedRubrics
    },
    ageGroupTranslate(ageGroup) {
      return (TAILOR_UNIT_AGE_GROUPS.find((v) => v.ageGroup === ageGroup) || {}).label || ageGroup
    },
    addAgency() {
      this.$refs.learnerProfilePopupRef && this.$refs.learnerProfilePopupRef.open(false)
      // 打开校训设置弹窗埋点
      this.$analytics.sendEvent('cg_unit_create_setup')
    },
    closeNewTag() {
      if (this.guideFeatures && this.guideFeatures.showLearnerProfileGuideNewTag) {
        let result = { features: ['LEARNER_PROFILE_GUIDE_NEW_TAG'] }
        this.$axios.post($api.urls().hideGuide, result).then((res) => {
          this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
            ...this.guideFeatures,
            showLearnerProfileGuideNewTag: false
          })
        })
      }
    },
    // 移除校训
    removePortrait(portrait) {
      if (this.disabled || !portrait || !portrait.title) return;

      // 从已选中的校训中移除
      this.selectedRubrics = this.selectedRubrics.filter(item => 
        item && item.title !== portrait.title
      );
      // 更新到 RubricsRow 组件
      this.updateSelectedRubrics(this.selectedRubrics);
    },
    // 带加载效果的打开校训设置弹窗
    addAgencyWithLoading() {
      this.isLoading = true;
      // 调用原有的方法
      this.addAgency(false);

      // 监听弹窗的显示状态变化
      const checkPopupStatus = () => {
        // 使用 nextTick 确保在 DOM 更新后执行
        this.$nextTick(() => {
          // 如果弹窗已经打开，重置加载状态
          if (this.$refs.learnerProfilePopupRef.addLearnerProfileDialog) {
            this.isLoading = false;
          } else {
            // 如果弹窗还没打开，继续检查
            setTimeout(checkPopupStatus, 100);
          }
        });
      };

      // 开始检查弹窗状态
      checkPopupStatus();
    },
    refreshRubricsData() {
      // 强制刷新校训数据
      const rubrics = ((this.agencyLearnerProfile || {}).newAgeGroupRubrics || []).find(
        (v) => v.ageGroup === this.unitInfo.grade
      )

      if (!rubrics) {
        this.selectedRubrics = []
        this.rubricsOptions = []
        return
      }

      // 处理禁用状态或进度超过 20% 的情况
      if (this.disabled || this.unitProgress >= 20) {
        if (rubrics && rubrics.details) {
          // 将校训详情转换为 RubricsRow 组件所需的选项格式
          if (rubrics.details && Array.isArray(rubrics.details) && rubrics.details.length > 0) {
            this.rubricsOptions = rubrics.details.map(item => ({
              title: item.title,
              description: item.description,
              expectations: item.expectations,
              subStandards: (item.subStandards || []).map(subStandard => ({
                title: subStandard.title,
                description: subStandard.description,
                expectations: subStandard.expectations
              }))
            }));
            
            // 获取当前 unitInfo 中的 newRubrics
            const currentRubrics = this.unitInfo.newRubrics || []
            // 过滤出标题匹配的 rubrics
            const matchedRubrics = rubrics.details.filter(detail =>
              currentRubrics.some(current => current.title === detail.title)
            )
            this.selectedRubrics = matchedRubrics
            this.unitInfo.newRubrics = matchedRubrics
            this.lastSelectedRubrics[rubrics.ageGroup] = matchedRubrics
            this.$emit('updateRubrics', {
              ageGroup: rubrics.ageGroup,
              ageGroupRubrics: matchedRubrics
            })
            
            // 使用 Vue.set 确保响应式更新
            this.$set(this, 'selectedRubrics', [...matchedRubrics])
            this.$set(this, 'rubricsOptions', [...this.rubricsOptions])
          }
        }
        return
      }

      // 处理正常状态
      if (rubrics && rubrics.details) {
        // 将校训详情转换为 RubricsRow 组件所需的选项格式
        if (rubrics.details && Array.isArray(rubrics.details) && rubrics.details.length > 0) {
          this.rubricsOptions = rubrics.details.map(item => ({
            title: item.title,
            description: item.description,
            expectations: item.expectations,
            subStandards: (item.subStandards || []).map(subStandard => ({
              title: subStandard.title,
              description: subStandard.description,
              expectations: subStandard.expectations
            }))
          }));
          
          // 不再自动全选校训，保持用户选择的状态
          // if (!this.unitInfo.newRubrics || (this.unitInfo.newRubrics && this.unitInfo.newRubrics.length === 0)) {  
          //   this.selectedRubrics = rubrics.details
          //   // 并且设置 this.lastSelectedRubrics
          //   this.lastSelectedRubrics[rubrics.ageGroup] = rubrics.details
          //   // 更新 unitInfo 中的校训内容
          //   this.$emit('updateRubrics', {
          //     ageGroup: rubrics.ageGroup,
          //     ageGroupRubrics: rubrics.details
          //   });
          // }

           // 获取当前 unitInfo 中的 newRubrics
           const currentRubrics = this.unitInfo.newRubrics || []
            // 过滤出标题匹配的 rubrics
            const matchedRubrics = rubrics.details.filter(detail =>
              currentRubrics.some(current => current.title === detail.title)
            )
            this.selectedRubrics = matchedRubrics
            this.unitInfo.newRubrics = matchedRubrics
            this.lastSelectedRubrics[rubrics.ageGroup] = matchedRubrics
            this.$emit('updateRubrics', {
              ageGroup: rubrics.ageGroup,
              ageGroupRubrics: matchedRubrics
            })
            
            // 使用 Vue.set 确保响应式更新
            this.$set(this, 'selectedRubrics', [...matchedRubrics])
            this.$set(this, 'rubricsOptions', [...this.rubricsOptions])
        }
      }
    }
  },
  beforeDestroy() {
    this.closeNewTag()
    // 清理防抖定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-result {
  border: 1px solid #dcdfe6;
  max-height: 150px;
  &--disabled {
    border: 1px solid #dcdfe6;
    background: #eaeef6;
    color: #676879;
  }

  /deep/ .rubrics-row .rubrics-select-area .select-area {
    padding: 4px !important;
    min-height: 36px !important;
  }
}
.newPoint {
  height: 16px;
  line-height: 16px;
}

/* 校训回显样式 */
.bg-light.bg, .bg-light .bg {
  background-color: #F5F6F8;
}

.m-t-sm {
  margin-top: 12px;
}

.wrapper-sm {
  padding: 6px 8px 8px 6px;
}

.rubrics-display-area {
  margin-top: 12px;
  border-radius: 4px;
}

.selectPortrait {
  font-size: 12px;
  margin: 2px;
  padding: 7.5px;
  line-height: 1.1;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  background-color: #FFFFFF;
  font-weight: 400;
  display: inline-flex !important;
  align-items: center;
}
.selectPortrait-disabled {
  background-color: #F4F4F5 !important;
}
.flex-grow-1 {
  line-height: normal !important;
}

.rubrics-close {
  font-size: 12px;
  cursor: pointer;
  &:hover {
    color: #F56C6C;
  }
}

.display-ib {
  display: inline-block;
}
</style>
<style>
.max-width-320 {
  max-width: 320px;
}
.set-center {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
}
</style>
