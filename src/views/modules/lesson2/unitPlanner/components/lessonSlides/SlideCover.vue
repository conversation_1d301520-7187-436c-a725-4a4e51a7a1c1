<template>
  <div :class="[customClass, { 'slide-cover' : !preview }]" @click="previewSlides">
    <img v-if="loading || !presentationId" :src="require('@/assets/img/lesson2/unitPlanner/lesson_slides_cover_cg.png')" style="width: 100%; height: 100%;"/>
    <iframe v-show="!loading && embedLink" :src="embedLink" frameborder="0" :height="_height" :width="_width" @load="loaded"></iframe>
  </div>
</template>

<script>
export default {
    name: 'SlideCover',
    props: {
        // 幻灯片项目 ID
        presentationId: {
            type: String,
            default: ''
        },
        // 自定义类名
        customClass: {
            type: String,
            default: ''
        },
        // 高度
        height: {
            type: String,
            default: '100%'
        },
        // 宽度
        width: {
            type: String,
            default: '100%'
        },
        // 显示显示预览
        preview: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        // 嵌入链接
        embedLink () {
            if (this.presentationId && this.preview) {
                return `https://docs.google.com/presentation/d/${this.presentationId}/embed`
            } else if (this.presentationId) {
                return `https://docs.google.com/presentation/d/${this.presentationId}/embed?rm=minimal`
            } else {
                return ''
            }
        },
        // 高度
        _height () {
            return this.height
        },
        // 宽度
        _width () {
            return this.width
        }
    },
    data () {
        return {
            loading: true // 是否显示骨架屏
        }
    },
    methods: {
        // 加载完成
        loaded () {
            this.loading = false
        },
        previewSlides () {
            this.$emit('previewSlides')
        }
    }
}
</script>

<style lang="less" scoped>
.slide-cover {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);

    iframe {
        pointer-events: none;
        border-radius: 8px;
    }

    img {
        border-radius: 8px;
    }
}
</style>
