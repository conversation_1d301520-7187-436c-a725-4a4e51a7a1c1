<template>
  <div class="slide-footer">
    <div v-if="isPaidUser" class="hidden-copyright">
    </div>
    <div v-else class="brand-logo">
      <div class="logo-circle">
        <img src="~@/assets/cg/images/home/<USER>" alt="Curriculum Genie" class="logo-image">
      </div>
      <div class="copyright">
        <span>Powered by Curriculum Genie © {{ currentYear }}</span>
      </div>
    </div>
    <div class="page-number">
      <span>{{ pageNumber }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SlideFooter',
  props: {
    pageNumber: {
      type: Number
    }
  },
  computed: {
    // 是否为付费用户
    isPaidUser() {
      return false
    },
    // 当前年份
    currentYear() {
      return new Date().getFullYear()
    }
  }
}
</script>

<style lang="less" scoped>
.slide-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 24px;
  position: relative;

  .hidden-copyright {
    height: 40px;
  }

  .brand-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .logo-circle {
      height: 40px;
      width: 145px;
      
      .logo-image {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  .copyright {
    span {
      color: #5e5e5e;
      font-size: 20px;
      font-weight: 400;
    }
  }
  
  .page-number {
    position: absolute;
    right: 16px;
    bottom: 16px;
    span {
      color: #fff;
      font-size: 24px;
      font-weight: 600;
      height: 36px;
      width: 36px;
      border-radius: 50%;
      background-color: #10b3b7;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style> 