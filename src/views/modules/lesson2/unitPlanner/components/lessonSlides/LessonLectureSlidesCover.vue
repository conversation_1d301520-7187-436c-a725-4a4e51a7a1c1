<template>
  <div class="lesson-lecture-slides-cover">
    
    <!-- 主要内容 -->
    <div class="cover-content">
      <!-- <PERSON>go 和品牌 -->
      <div class="cover-header">
        <div class="logo-container">
          <div class="logo-circle">
            <img src="~@/assets/cg/images/logo.png" alt="logo" class="logo-image">
          </div>
          <span class="brand-text">Curriculum Genie</span>
        </div>
      </div>
      
      <!-- 主标题 -->
      <div class="cover-main">
        <div class="lesson-title">
          {{ title }}
        </div>
        
        <!-- 副标题/描述 -->
        <div class="lesson-subtitle">
          <span>{{ subTitle }}</span>
        </div>
      </div>
      
      <!-- 底部版权信息 -->
      <div class="cover-footer">
        <span>Powered by Curriculum Genie © 2025</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LessonLectureSlidesCover',
  props: {
    title: {
      type: String
    },
    subTitle: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less" scoped>
.lesson-lecture-slides-cover {
  width: 100%;
  aspect-ratio: 16 / 9;
  background-image: url('~@/assets/img/lesson2/unitPlanner/slides/cover.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 30px 0 90px 0;
  box-sizing: border-box;
  overflow: hidden;
  
  .cover-content {
    position: relative;
    z-index: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .cover-header {
    .logo-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      
      .logo-circle {
        img {
          height: 60px;
          width: 60px;
        }
      }
      
      .brand-text {
        color: white;
        font-size: 36px;
        font-weight: 600;
      }
    }
  }
  
  .cover-main {
    text-align: center;
    height: 250px;
    position: relative;
    
    .lesson-title {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 50%;
      color: white;
      font-size: 72px;
      font-weight: 700;
      margin: 0 100px;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      line-height: 1.1;
    }
    
    .lesson-subtitle {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 50px;
      padding: 10px 20px;
      display: inline-block;
      overflow: visible;
      margin: 0px 200px;

      
      span {
        color: #333;
        font-size: 24px;
        font-weight: 600;
        line-height: 1.3;
      }
    }
  }
  
  .cover-footer {
    text-align: center;
    
    span {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24px;
      font-weight: 400;
    }
  }
}
</style> 