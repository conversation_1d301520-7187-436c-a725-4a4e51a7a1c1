<template>
  <div class="lesson-lecture-slides-reflect">
    <!-- 主要内容区域 -->
    <div class="reflect-main">
      <!-- 反思问题内容 -->
      <div class="reflect-content">
        <div class="question-bubble">
          <div class="question-text" v-autofit-text>
            <span class="bullet">•</span>
            <span class="text">{{ reflectiveQuestion }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部品牌信息 -->
    <slide-footer :page-number="pageNumber" />
  </div>
</template>

<script>
import SlideFooter from './SlideFooter.vue'

export default {
  name: 'LessonLectureSlidesReflect',
  components: {
    SlideFooter
  },
  props: {
    reflectiveQuestion: {
      type: String
    },
    pageNumber: {
      type: Number
    }
  }
}
</script>

<style lang="less" scoped>
.lesson-lecture-slides-reflect {
  width: 100%;
  aspect-ratio: 16 / 9;
  background-image: url('~@/assets/img/lesson2/unitPlanner/slides/reflect.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
  
  .reflect-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 240px;
    white-space: pre-line;
    
    .reflect-content {
      width: 100%;
      max-width: 800px;
      
      .question-bubble {
        padding: 0 120px;
        
        .question-text {
          display: flex;
          align-items: flex-start;
          gap: 20px;
          height: 250px;
          
          .bullet {
            color: #333;
            font-weight: bold;
            line-height: 1;
            margin-top: 8px;
            flex-shrink: 0;
          }
          
          .text {
            color: #333;
            font-weight: 600;
            line-height: 1.2;
            flex: 1;
          }
        }
      }
    }
  }
}
</style> 