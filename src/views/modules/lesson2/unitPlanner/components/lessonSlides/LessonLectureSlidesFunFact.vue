<template>
  <div class="lesson-lecture-slides-funfact">
    <!-- 主要内容区域 -->
    <div class="funfact-main">
      <!-- 右侧趣味知识内容 -->
      <div class="funfact-content">
        <div class="content-card">
          <div class="fact-item" v-autofit-text>
            <span class="bullet">•</span>
            <span class="fact-text">{{ funFact }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部品牌信息 -->
    <slide-footer :page-number="pageNumber" />
  </div>
</template>

<script>
import SlideFooter from './SlideFooter.vue'

export default {
  name: 'LessonLectureSlidesFunFact',
  components: {
    SlideFooter
  },
  props: {
    funFact: {
      type: String
    },
    pageNumber: {
      type: Number
    }
  }
}
</script>

<style lang="less" scoped>
.lesson-lecture-slides-funfact {
  width: 100%;
  aspect-ratio: 16 / 9;
  background-image: url('~@/assets/img/lesson2/unitPlanner/slides/fun_fact.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  
  .funfact-main {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 60px;
    white-space: pre-line;
    
    .funfact-title {
      flex: 0 0 auto;
      
      .title-circle {
        width: 200px;
        height: 400px;
        border-bottom-right-radius: 400px;
        border-top-right-radius: 400px;
        background: linear-gradient(135deg, #4ECDC4, #26A69A);
        display: flex;
        align-items: center;
        justify-content: center;
        
        .title-text {
          color: white;
          font-size: 48px;
          font-weight: 700;
          margin: 0;
          text-align: center;
          line-height: 1.2;
        }
      }
    }
    
    .funfact-content {
      position: absolute;
      left: 500px;
      right: 70px;
      top: 50px;
      height: 450px;
      
      .content-card {
        height: 100%;
        width: 100%;
        
        .fact-item {
          height: 100%;
          width: 100%;
          display: flex;
          align-items: flex-start;
          gap: 20px;
          
          .bullet {
            color: #333;
            font-weight: bold;
            line-height: 1;
            margin-top: 8px;
            flex-shrink: 0;
          }
          
          .fact-text {
            color: #333;
            font-weight: 600;
            line-height: 1.3;
            flex: 1;
          }
        }
      }
      
      .arrow-decoration {
        position: absolute;
        left: -40px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
      }
    }
  }
}
</style> 