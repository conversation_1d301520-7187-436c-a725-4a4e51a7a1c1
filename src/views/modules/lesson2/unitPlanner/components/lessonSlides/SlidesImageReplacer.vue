<template>
  <div class="slides-image-replacer">
    <!-- 图片容器 -->
    <div 
      class="image-container" 
      @mouseenter="showControls" 
      @mouseleave="hideControls"
      :style="containerStyle"
    >
      <!-- 新功能引导 -->
      <el-popover
        v-show="imageUrl && !isConverting"
        placement="right"
        width="340"
        trigger="manual"
        style="height: 100%; width: 100%;"
        :visible-arrow="false"
        popper-class="lesson-slidesguide-popover"
        v-model="guideVisible"
      >
        <div class="guide-content">
          <img :src="require('@/assets/img/lesson2/unitPlanner/slides/lesson_slides_change_image_guide.png')" width="330" height="130" class="guide-img">
          <i class="lg-icon lg-icon-close close-icon" @click="handleCloseGuide"></i>
        </div>
        <!-- 图片 -->
        <el-image
          slot="reference"  
          v-if="imageUrl && !isConverting"
          :src="imageUrl"
          :alt="imageAlt"
          class="main-image"
          fit="contain"
          @load="handleImageLoad"
          @error="handleImageLoadError"
        >
          <!-- 加载中的占位符 - 使用骨架屏 -->
          <div slot="placeholder" class="image-skeleton-placeholder">
            <el-skeleton 
              :loading="true" 
              animated
              style="height: 100%; width: 100%;"
            >
              <template slot="template">
                <el-skeleton-item variant="image" style="width: 100%; height: 100%;" />
              </template>
            </el-skeleton>
          </div>
          <!-- 加载失败的占位符 -->
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
        </el-image>
      </el-popover>
      <!-- URL转换时的骨架屏 -->
      <div v-if="isConverting" class="image-skeleton-placeholder">
        <el-skeleton 
          :loading="true" 
          animated
          style="height: 100%; width: 100%;"
        >
          <template slot="template">
            <el-skeleton-item variant="image" style="width: 100%; height: 100%;" />
          </template>
        </el-skeleton>
      </div>
      
      <!-- 上传进度条 - 只在文件上传时显示 -->
      <el-progress
        v-if="isUploading"
        :showText="false"
        :percentage="uploadProgressRate"
        style="position: absolute; bottom: 0; width: 100%; z-index: 10;"
      />
      <!-- 悬停遮罩层 -->
      <div class="overlay" v-show="controlsVisible && isEditable && imageUrl && !isUploading && !isConverting && imageLoaded">
        <div class="control-buttons">
            <div>
                <el-button type="primary" @click="openGoogleSearch" icon="lg-icon lg-icon-search lg-margin-right-8">{{ $t('loc.lessonSlide23') }}</el-button>
            </div>
            <div>
                <el-button type="primary" @click="triggerFileUpload" icon="lg-icon lg-icon-upload lg-margin-right-8">{{ $t('loc.lessonSlide24') }}</el-button>
            </div>
        </div>
      </div>
    </div>
    
    <!-- 图片来源信息 -->
    <div v-if="imageSource && imageUrl && !isUploading" class="slides-image-source">
      <span v-if="imageSource == 'Photos provided by Pixabay'">Photos provided by Pixabay</span>
      <span v-else>Powered by Google. Image Source: {{ imageSource }}</span>
    </div>

    <el-dialog
      :visible.sync="searchDialogVisible"
      :append-to-body="true"
      @close="handleSearchDialogClose"
      :title="$t('loc.lessonSlide23')"
      width="670px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-input v-model="searchKeyword" :placeholder="$t('loc.lessonSlide25')" @input="searchImage" clearable>
        <template slot="prefix">
          <i class="lg-icon lg-icon-search search-icon"></i>
        </template>
      </el-input>
      <div v-if="images.length > 0" class="lg-margin-t-b-8">{{ $t('loc.lessonSlide26') }}</div>
      <div class="lg-margin-top-24">
        <div class="image-result-container lg-scrollbar-show"  v-loading="searchLoading && images.length == 0" v-infinite-scroll="getSuggestions" :infinite-scroll-disabled="searchLoading || page > 3" :infinite-scroll-distance="10">
          <div class="image-viewer lg-pointer" v-for="(image, index) in images" :key="index" @click.stop="selectImage(image)">
            <div class="image-viewer-container">
              <el-image :src="image.thumbnailUrl || image.url" :style="{height: '100%', width: '100%'}" lazy fit="cover">
                <div slot="placeholder">
                  <el-skeleton :loading="true" animated>
                    <template slot="template">
                      <el-skeleton-item
                        variant="image"
                        style="width: 190px; height: 110px;"
                        />
                    </template>
                  </el-skeleton>
                </div>
              </el-image>
              <div class="image-source" :title="image.source">Image Source: {{ image.source }}</div>
            </div>
          </div>
          <div style="text-align: center; height: 50px; width: 100%;" v-if="searchLoading && images.length > 0" v-loading="searchLoading"></div>
          <!-- 空页面 -->
          <LgEmptyPage v-if="images.length == 0 && !searchLoading" style="width: 100%;"/>
        </div>
      </div>
    </el-dialog>
    
    <!-- 隐藏的文件输入 -->
    <input 
      type="file" 
      ref="fileInput" 
      @change="handleFileUpload" 
      accept="image/png,image/jpg,image/jpeg"
      style="display: none" 
    />
  </div>
</template>

<script>
import fileUtils from '@/utils/file'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import { mapState } from 'vuex'
import LgEmptyPage from '@/components/LgEmptyPage.vue'

export default {
  name: 'SlidesImageReplacer',
  components: {
    LgEmptyPage
  },
  props: {
    // 是否为缩略图
    isThumbnail: {
      type: Boolean,
      default: false
    },
    // 当前图片URL
    imageUrl: {
      type: String,
      default: ''
    },
    // 图片描述
    imageAlt: {
      type: String,
      default: 'Image'
    },
    // 图片来源信息
    imageSource: {
      type: String,
      default: ''
    },
    // 搜索关键词
    imageSearchKeyword: {
      type: String,
      default: ''
    },
    // 容器宽度
    width: {
      type: String,
      default: '100%'
    },
    // 容器高度
    height: {
      type: String,
      default: '100%'
    },
    // 是否可编辑
    isEditable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      controlsVisible: false,
      searchDialogVisible: false,
      searchLoading: false,
      imageLoaded: false,
      isUploading: false,
      isConverting: false, // URL转换状态
      version: 0, // 版本号
      images: [], // 图片列表
      page: 1, // 页码
      pageSize: 10,
      searchKeyword: '', // 搜索关键词
      uploadProgressRate: 0, // 上传进度
      guideVisible: false // 引导可见
    }
  },
  computed: {
    ...mapState({
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      showLessonSlidesChangeImageGuide: state => state.common.guideFeatures && state.common.guideFeatures.showLessonSlidesChangeImageGuide, // 功能引导
    }),
    containerStyle() {
      return {
        width: this.width,
        height: this.height
      }
    }
  },
  methods: {
    // 显示控制按钮
    showControls() {
      this.controlsVisible = true
    },
    
    // 隐藏控制按钮
    hideControls() {
      this.controlsVisible = false
    },
    
    // 打开Google搜索对话框
    openGoogleSearch() {
      this.searchDialogVisible = true
      this.getSuggestions(true)
    },

    handleSearchDialogClose() {
      this.searchDialogVisible = false
      this.searchKeyword = ''
      this.page = 1
      this.images = []
    },
    
    searchImage: tools.debounce(function() {
      this.getSuggestions(true)
    }, 2000),

    // 获取建议图片
    async getSuggestions(refresh = false) {
      if (refresh) {
        this.page = 1
        this.images = []
        this.searchLoading = false
      }
      // 如果正在加载或图片数量达到30，则返回
      if (this.searchLoading || this.page > 3) return
      // 版本号+1
      let version = this.version + 1
      // 更新版本号
      this.version = version
      try {
        // 设置加载状态
        this.searchLoading = true
        // 搜索图片
        const response = await LessonApi.searchCover({
          keywords: this.searchKeyword || this.imageSearchKeyword,
          source: 'GOOGLE',
          pageNum: this.page,
          pageSize: this.pageSize
        })
        // 如果版本号不一致，则返回
        if (version !== this.version) {
          return
        }
        // 关闭加载状态
        this.searchLoading = false
        // 更新图片列表
        this.images = [...this.images, ...response.images]
        // 增加页码
        this.page++
      } catch (error) {
        this.searchDialogVisible = false
        this.$message.error(this.$t('loc.lessonSlide29'))
      }
    },
    
    // 选择图片
    selectImage(image) {
      this.handleSearchDialogClose()
      this.isConverting = true
      this.imageLoaded = false
      LessonApi.setLessonSlideImage(image.url)
      .then(res => {
        this.$emit('image-changed', {
          imageUrl: res.url,
          imageSource: image.source
        })
      })
      .catch(() => {
        this.$message.error(this.$t('loc.lessonSlide30'))
      })
      .finally(() => {
        this.isConverting = false
        this.imageLoaded = true
      })
    },
    
    // 触发文件上传
    triggerFileUpload() {
      this.$refs.fileInput.click()
    },
    
    // 处理文件上传
    async handleFileUpload(event) {
      const file = event.target.files[0]
      if (!file) return
      
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'))
        // 重置文件输入
        this.$refs.fileInput.value = ''
        return
      }
      
      // 验证文件大小（限制为10MB）
      if (file.size > 10 * 1024 * 1024) {
        this.$message.error(this.$t('loc.lessonSlide27'))
        // 重置文件输入
        this.$refs.fileInput.value = ''
        return
      }
      
      // 开始上传，显示loading状态
      this.isUploading = true
      this.imageLoaded = false
      
      try {
        await this.fileUpload(file)
      } catch (error) {
      } finally {
        this.isUploading = false
        // 重置文件输入，允许重新选择相同文件
        this.$refs.fileInput.value = ''
      }
    },
    
    // 文件上传方法（使用fileUtils.uploadFile）
    fileUpload(file) {
      return new Promise((resolve, reject) => {
        this.uploadProgressRate = 0
        fileUtils.uploadFile(file, {
          privateFile: false, // 公开文件
          processEventHandler: (progressEvent) => {
              // 上传进度处理,图片显示出来才算彻底完成
              let progressRate = (progressEvent.loaded / progressEvent.total).toFixed(2) * 100
              if (progressRate === 100) {
                  progressRate -= 1
              }
              this.uploadProgressRate = progressRate
          }
        })
        .then((response) => {
          // 发送图片更换事件
          this.$emit('image-changed', {
            imageUrl: response.public_url
          })
          
          resolve(response)
        })
        .catch((error) => {
          reject(error)
        })
      })
    },
    
    // 处理图片加载
    handleImageLoad() {
      this.imageLoaded = true
      this.handleShowGuide()
    },
    
    // 处理图片加载错误
    handleImageLoadError() {
      this.imageLoaded = false
    },

    // 显示引导
    handleShowGuide() {
      if (this.isThumbnail || !this.isEditable || !this.showLessonSlidesChangeImageGuide) {
        return
      }
      this.guideVisible = true
      let result = { 'features': ['LESSON_SLIDES_CHANGE_IMAGE_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then((res) => {
        let value = this.guideFeatures
        value.showLessonSlidesChangeImageGuide = false
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', value)
      })
    },

    // 关闭引导
    handleCloseGuide() {
      this.guideVisible = false
    }
  },
  watch: {
    // 监听图片URL变化，重置加载状态
    imageUrl(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.imageLoaded = false
        this.isConverting = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.slides-image-replacer {
  position: relative;
  width: 100%;
  height: 100%;
  
  .image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 20px;
    cursor: pointer;
    
    .main-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
    }
    
    .image-skeleton-placeholder {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f5f7fa;
    }
    
    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
      font-size: 14px;
    }
    
    .image-skeleton {
      width: 100%;
      height: 100%;

      .el-image {
        width: 100%;
        height: 100%;
      }
      
      .skeleton-image {
        width: 100%;
        height: 100%;
      }
    }
    
    // 骨架屏自定义样式
    /deep/ .image-skeleton {
        height: 100%;
        width: 100%;
        .el-skeleton {
            height: 100%;
            width: 100%;
        }
        .el-skeleton__image {
            border-radius: 4px;
        }
    }
    
    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #f5f7fa;
      color: #f56c6c;
      
      i {
        font-size: 32px;
        margin-bottom: 8px;
        color: #f56c6c;
      }
      
      span {
        font-size: 12px;
      }
    }
    
    .no-image-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #f5f7fa;
      color: #909399;
      border: 2px dashed #dcdfe6;
      border-radius: 4px;
      
      i {
        font-size: 48px;
        margin-bottom: 8px;
      }
      
      span {
        font-size: 14px;
      }
    }
    
    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: opacity 0.3s ease;
      
      .control-buttons {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
      }
    }
  }
  
  .slides-image-source {
    margin-top: 16px;
    text-align: center;
    
    span {
      color: #999;
      font-size: 12px;
    }
  }
}

/deep/ .el-dialog__header {
  padding: 24px 24px 0;
  .el-dialog__title {
    font-size: 20px;
    font-weight: 600;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #111c1c;
  }
}

/deep/ .el-dialog__body {
  padding: 24px;
}

.search-icon {
  color: #111c1c;
  font-size: 20px;
  line-height: 20px;
  display: inline-block;
  padding: 10px 0;
}

.image-viewer {
  aspect-ratio: 16 / 9;
  position: relative;
  padding-top: 0;
  width: calc((100% - 32px) / 3);
  border: 3px solid transparent;
  border-radius: 2px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.image-viewer:hover {
  border-color: #10b3b7;
}

.image-result-container {
  overflow: auto;
  max-height: 360px;
  min-height: 200px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
  align-content: flex-start;
}

.image-viewer-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.image-viewer-container:hover .image-source {
  display: block;
}

.image-source {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #3A3F51B2;
  color: #fff;
  padding: 8px 10px;
  line-height: 16px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

@supports not (aspect-ratio: 16 / 9) {
  .image-viewer {
    height: 0;
    padding-top: 56.25%;
  }
  .image-viewer-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

::v-deep .el-image__inner {
  border-radius: 0 !important;
  object-fit: contain;
  width: 100%;
  height: 100%;
}

// 骨架屏占位符样式
/deep/ .image-skeleton-placeholder {
  .el-skeleton {
    height: 100%;
    width: 100%;
  }
  .el-skeleton__image {
    border-radius: 4px;
  }
}
</style> 
<style lang="less">
.lesson-slidesguide-popover {
  padding: 0px !important;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  .guide-content {
    position: relative;
    min-height: 110px;
    .guide-img {
      width: 330px;
      height: 130px;
      object-fit: cover;
    }
  }
  .close-icon {
    position: absolute;
    top: 0px;
    right: -8px;
    font-size: 20px;
    cursor: pointer;
    color: #676879;
  }

}
@media (max-width: 768px) {
  .lesson-slidesguide-popover {
    .guide-content {
      img {
        width: 110px !important;
        height: 40px !important;
      }
    }
  }
}
</style>
