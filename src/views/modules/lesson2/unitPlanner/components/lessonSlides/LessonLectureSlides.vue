<template>
  <div class="lesson-lecture-slides" tabindex="0">
    <!-- 模板预览容器 -->
    <div 
      class="slides-container" 
      ref="slidesContainer"
      :class="{ 'fullscreen-mode': isFullscreen }"
      :tabindex="isFullscreen ? 0 : -1"
      @click="handleContainerClick"
    >
      <!-- 全屏模式控制按钮 -->
      <div v-if="isFullscreen" class="fullscreen-controls">
        <el-button 
          type="primary" 
          icon="el-icon-arrow-left" 
          circle 
          @click.stop="previousSlide"
          :disabled="currentSlideIndex === 0"
          title="Previous"
        />
        <el-button 
          type="primary" 
          icon="el-icon-arrow-right" 
          circle 
          @click.stop="nextSlide"
          :disabled="currentSlideIndex === slidePages.length - 1"
          title="Next"
        />
        <el-button 
          type="info" 
          icon="el-icon-close" 
          circle 
          @click.stop="exitFullscreen"
          title="Exit Fullscreen"
        />
      </div>
      
      <!-- 动态渲染当前选中的模板页 -->
      <div class="slide-content" :style="slideContentStyle">
        <KeepAlive :max="15">
          <component 
            :is="currentSlideComponent" 
            v-bind="currentSlideProps"
            :can-edit="canEdit"
            :key="componentKey"
            @image-changed="handleImageChanged"
          />
        </KeepAlive>
      </div>
    </div>
    
    <!-- 底部控制栏 -->
    <div class="slides-controls" v-show="!isFullscreen">
      <el-button
          class="control-button"
          type="primary"
          icon="el-icon-arrow-left font-size-20"
          circle
          @click.stop="previousSlide"
          :disabled="currentSlideIndex === 0"
          title="Previous"
        />
      <!-- 缩略图导航 -->
      <div class="slide-thumbnails">
        <div 
          v-for="(slide, index) in slidePages" 
          :key="slide.type"
          class="thumbnail-item"
          :class="{ 'active': index === currentSlideIndex }"
          @click="goToSlide(index)"
        >
          <div class="thumbnail-preview">
            <!-- 缩略图内容预览 -->
            <div class="thumbnail-content">
              <component 
                :is="getThumbnailComponent(slide)" 
                v-bind="getThumbnailProps(slide, index)"
                :key="`thumb-${slide.type}`"
              />
            </div>
          </div>
          <div class="thumbnail-number">{{ index + 1 }}</div>
        </div>
      </div>
      <el-button 
        class="control-button"
        type="primary" 
        icon="el-icon-arrow-right font-size-20" 
        circle 
        @click.stop="nextSlide"
        :disabled="currentSlideIndex === slidePages.length - 1"
        title="Next"
        />
      <!-- 新增：全屏按钮 -->
      <div class="fullscreen-control">
        <el-tooltip :content="$t('loc.lessonSlide28')" placement="top">
          <el-button
            type="primary"
            @click="enterFullscreen"
          >
          <i class="el-icon-full-screen font-size-24 lg-pointer"></i>
        </el-button>
      </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import LessonLectureSlidesCover from './LessonLectureSlidesCover.vue'
import LessonLectureSlidesQuestion from './LessonLectureSlidesQuestion.vue'
import LessonLectureSlidesContent from './LessonLectureSlidesContent.vue'
import LessonLectureSlidesFunFact from './LessonLectureSlidesFunFact.vue'
import LessonLectureSlidesReflect from './LessonLectureSlidesReflect.vue'

export default {
  name: 'LessonLectureSlides',
  model: {
    prop: 'templateData',
    event: 'input'
  },
  components: {
    LessonLectureSlidesCover,
    LessonLectureSlidesQuestion,
    LessonLectureSlidesContent,
    LessonLectureSlidesFunFact,
    LessonLectureSlidesReflect
  },
  props: {
    // 模板数据
    templateData: {
      type: Object
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentSlideType: 'cover',
      currentSlideIndex: 0,
      isFullscreen: false, // 新增：全屏状态
      // 缩放相关
      containerWidth: 0,
      containerHeight: 0,
      scaleRatio: 1,
      baseWidth: 1080, // 基准宽度
      baseHeight: 608, // 基准高度 (16:9 比例)
      resizeObserver: null
    }
  },
  computed: {
    // 动态生成幻灯片类型数组
    slidePages () {
      const { templateData } = this
      const slides = []
      
      // 第1页：封面
      slides.push({ type: 'cover', component: 'LessonLectureSlidesCover', label: '封面', icon: '📖' })
      
      // 第2-3页：引导问题
      if (templateData.leadInQuestions && templateData.leadInQuestions.length > 0) {
        templateData.leadInQuestions.forEach((question, index) => {
          slides.push({ 
            type: `question-${index}`, 
            component: 'LessonLectureSlidesQuestion', 
            data: question
          })
        })
      }
      
      // 第4页：学习目标
      if (templateData.objectives) {
        slides.push({ 
          type: 'objective', 
          component: 'LessonLectureSlidesContent', 
          data: templateData.objectives
        })
      }
      
      // 第5页：核心学习
      if (templateData.coreLearning && templateData.coreLearning.length > 0) {
        templateData.coreLearning.forEach((learning, index) => {
          slides.push({ 
            type: `core-learning-${index}`, 
            component: 'LessonLectureSlidesContent', 
            data: learning
          })
        })
      }
      
      // 第6-9页：探索和活动
      if (templateData.explorationAndActivity && templateData.explorationAndActivity.length > 0) {
        templateData.explorationAndActivity.forEach((activity, index) => {
          slides.push({ 
            type: `activity-${index}`, 
            component: 'LessonLectureSlidesContent', 
            data: activity
          })
        })
      }
      
      // 第10-11页：趣味知识
      if (templateData.funFacts && templateData.funFacts.length > 0) {
        templateData.funFacts.forEach((funFact, index) => {
          slides.push({ 
            type: `funfact-${index}`, 
            component: 'LessonLectureSlidesFunFact', 
            data: funFact
          })
        })
      }
      
      // 第12-13页：反思问题
      if (templateData.reflectiveQuestions && templateData.reflectiveQuestions.length > 0) {
        templateData.reflectiveQuestions.forEach((question, index) => {
          slides.push({ 
            type: `reflect-${index}`, 
            component: 'LessonLectureSlidesReflect', 
            data: question
          })
        })
      }
      
      return slides
    },
    
    // 当前幻灯片组件
    currentSlideComponent () {
      const slideType = this.slidePages[this.currentSlideIndex]
      return slideType ? slideType.component : 'LessonLectureSlidesCover'
    },
    
    // 当前幻灯片的 props
    currentSlideProps () {
      const { templateData } = this
      const currentSlide = this.slidePages[this.currentSlideIndex]
      
      if (!currentSlide) return {}
      
      switch (currentSlide.type) {
        case 'cover':
          return {
            title: templateData.lessonTitle,
            subTitle: templateData.lessonSubtitle
          }
        case 'objective':
          return {
            title: templateData.objectives && templateData.objectives.title,
            content: templateData.objectives && templateData.objectives.content || '',
            contentType: 'objective',
            imageSearchKeyword: templateData.objectives && templateData.objectives.imageSearchKeyword || '',
            imageUrl: templateData.objectives && templateData.objectives.imageUrl || '',
            imageSource: templateData.objectives && templateData.objectives.imageSource || '',
            pageNumber: this.currentSlideIndex + 1
          }
        default:
          // 处理动态生成的页面类型
          if (currentSlide.type.startsWith('question-')) {
            return {
              question: currentSlide.data,
              pageNumber: this.currentSlideIndex + 1
            }
          } else if (currentSlide.type.startsWith('core-learning-')) {
            return {
              title: currentSlide.data && currentSlide.data.title || 'Core Learning',
              content: currentSlide.data && currentSlide.data.content || '',
              imageSearchKeyword: currentSlide.data && currentSlide.data.imageSearchKeyword || '',
              imageUrl: currentSlide.data && currentSlide.data.imageUrl || '',
              imageSource: currentSlide.data && currentSlide.data.imageSource || '',
              contentType: 'learning',
              pageNumber: this.currentSlideIndex + 1
            }
          } else if (currentSlide.type.startsWith('activity-')) {
            return {
              title: currentSlide.data && currentSlide.data.title || 'Activity',
              content: currentSlide.data && currentSlide.data.content || '',
              imageSearchKeyword: currentSlide.data && currentSlide.data.imageSearchKeyword || '',
              imageUrl: currentSlide.data && currentSlide.data.imageUrl || '',
              imageSource: currentSlide.data && currentSlide.data.imageSource || '',
              contentType: 'activity',
              pageNumber: this.currentSlideIndex + 1
            }
          } else if (currentSlide.type.startsWith('funfact-')) {
            return {
              funFact: currentSlide.data,
              pageNumber: this.currentSlideIndex + 1
            }
          } else if (currentSlide.type.startsWith('reflect-')) {
            return {
              reflectiveQuestion: currentSlide.data,
              pageNumber: this.currentSlideIndex + 1
            }
          }
          return {}
      }
    },
    
    // 计算缩放比例
    calculatedScale () {
      if (this.containerWidth === 0) return 1
      
      // 计算基于宽度和高度的缩放比例
      const widthScale = this.containerWidth / this.baseWidth
      const heightScale = this.containerHeight / this.baseHeight
      
      // 计算容器的宽高比
      const containerRatio = this.containerWidth / this.containerHeight
      const baseRatio = this.baseWidth / this.baseHeight
      
      // 如果容器比基准更"瘦"（竖屏），优先适配宽度
      // 如果容器比基准更"宽"（横屏），优先适配高度
      if (containerRatio < baseRatio) {
        // 容器更瘦，以宽度为准
        return widthScale
      } else {
        // 容器更宽，以高度为准
        return heightScale
      }
    },
    
    // 缩放后的样式
    slideContentStyle () {
      return {
        transform: `scale(${this.calculatedScale})`,
        transformOrigin: 'center center',
        width: `${this.baseWidth}px`,
        height: `${this.baseHeight}px`
      }
    },
    
    // 获取组件的key（computed属性）
    componentKey () {
      // 使用唯一的slide.type作为key，确保每个幻灯片都有独立的缓存实例
      // 这样可以避免不同幻灯片之间的状态互相干扰（特别是图片加载状态）
      const slideType = this.slidePages[this.currentSlideIndex]
      return slideType ? slideType.type : 'default'
    }
  },
  methods: {   
    
    // 切换到上一张幻灯片
    previousSlide () {
      if (this.currentSlideIndex > 0) {
        this.currentSlideIndex--
        this.currentSlideType = this.slidePages[this.currentSlideIndex].type
        this.$emit('slide-change', this.currentSlideType)
        // 关闭当前slide中的所有引导
        this.closeAllGuides()
        // 自动滚动缩略图到可见区域
        this.$nextTick(() => {
          this.scrollThumbnailIntoView(this.currentSlideIndex)
        })
      }
    },
    
    // 切换到下一张幻灯片
    nextSlide () {
      if (this.currentSlideIndex < this.slidePages.length - 1) {
        this.currentSlideIndex++
        this.currentSlideType = this.slidePages[this.currentSlideIndex].type
        this.$emit('slide-change', this.currentSlideType)
        // 关闭当前slide中的所有引导
        this.closeAllGuides()
        // 自动滚动缩略图到可见区域
        this.$nextTick(() => {
          this.scrollThumbnailIntoView(this.currentSlideIndex)
        })
      }
    },
    
    // 跳转到指定幻灯片
    goToSlide (index) {
      if (index >= 0 && index < this.slidePages.length) {
        this.currentSlideIndex = index
        this.currentSlideType = this.slidePages[index].type
        this.$emit('slide-change', this.currentSlideType)
        // 关闭当前slide中的所有引导
        this.closeAllGuides()
        // 自动滚动缩略图到可见区域
        this.$nextTick(() => {
          this.scrollThumbnailIntoView(index)
        })
      }
    },
    
    // 关闭所有子组件中的引导
    closeAllGuides() {
      // 通过 $refs 找到当前激活的组件实例
      this.$nextTick(() => {
        // 递归查找所有子组件中的 SlidesImageReplacer
        this.findAndCloseGuides(this.$children)
      })
    },

    // 递归查找并关闭引导
    findAndCloseGuides(components) {
      components.forEach(component => {
        // 如果是 SlidesImageReplacer 组件，调用其 handleCloseGuide 方法
        if (component.$options.name === 'SlidesImageReplacer' && typeof component.handleCloseGuide === 'function') {
          component.handleCloseGuide()
        }
        // 递归查找子组件
        if (component.$children && component.$children.length > 0) {
          this.findAndCloseGuides(component.$children)
        }
      })
    },
    
    // 滚动缩略图到可见区域
    scrollThumbnailIntoView (index) {
      const thumbnailContainer = this.$el.querySelector('.slide-thumbnails')
      const thumbnailItems = this.$el.querySelectorAll('.thumbnail-item')
      
      if (thumbnailContainer && thumbnailItems[index]) {
        const targetThumbnail = thumbnailItems[index]
        const containerRect = thumbnailContainer.getBoundingClientRect()
        const thumbnailRect = targetThumbnail.getBoundingClientRect()
        
        // 计算目标缩略图相对于容器的位置
        const thumbnailLeft = thumbnailRect.left - containerRect.left + thumbnailContainer.scrollLeft
        const thumbnailWidth = thumbnailRect.width
        const containerWidth = containerRect.width
        
        // 如果缩略图不在可见区域内，则滚动到合适位置
        if (thumbnailLeft < thumbnailContainer.scrollLeft) {
          // 缩略图在左侧不可见，滚动到左边
          thumbnailContainer.scrollLeft = thumbnailLeft - 20 // 留一些边距
        } else if (thumbnailLeft + thumbnailWidth > thumbnailContainer.scrollLeft + containerWidth) {
          // 缩略图在右侧不可见，滚动到右边
          thumbnailContainer.scrollLeft = thumbnailLeft + thumbnailWidth - containerWidth + 20 // 留一些边距
        }
      }
    },
    
    // 获取缩略图组件
    getThumbnailComponent (slide) {
      // 缩略图使用与主内容相同的组件
      return slide.component
    },
    
    // 获取缩略图的 props
    getThumbnailProps (slide, index) {
      const { templateData } = this
      
      // 与 currentSlideProps 保持一致的逻辑
      switch (slide.type) {
        case 'cover':
          return {
            title: templateData.lessonTitle,
            subTitle: templateData.lessonSubtitle,
            isThumbnail: true
          }
        case 'objective':
          return {
            title: templateData.objectives && templateData.objectives.title,
            content: templateData.objectives && templateData.objectives.content || '',
            contentType: 'objective',
            imageSearchKeyword: templateData.objectives && templateData.objectives.imageSearchKeyword || '',
            imageUrl: templateData.objectives && templateData.objectives.imageUrl || '',
            imageSource: templateData.objectives && templateData.objectives.imageSource || '',
            pageNumber: index + 1,
            isThumbnail: true
          }
        default:
          // 处理动态生成的页面类型
          if (slide.type.startsWith('question-')) {
            return {
              question: slide.data,
              pageNumber: index + 1,
              isThumbnail: true
            }
          } else if (slide.type.startsWith('core-learning-')) {
            return {
              title: slide.data && slide.data.title,
              content: slide.data && slide.data.content || '',
              imageSearchKeyword: slide.data && slide.data.imageSearchKeyword || '',
              imageUrl: slide.data && slide.data.imageUrl || '',
              imageSource: slide.data && slide.data.imageSource || '',
              contentType: 'learning',
              pageNumber: index + 1,
              isThumbnail: true
            }
          } else if (slide.type.startsWith('activity-')) {
            return {
              title: slide.data && slide.data.title,
              content: slide.data && slide.data.content || '',
              imageSearchKeyword: slide.data && slide.data.imageSearchKeyword || '',
              imageUrl: slide.data && slide.data.imageUrl || '',
              imageSource: slide.data && slide.data.imageSource || '',
              contentType: 'activity',
              pageNumber: index + 1,
              isThumbnail: true
            }
          } else if (slide.type.startsWith('funfact-')) {
            return {
              funFact: slide.data,
              pageNumber: index + 1,
              isThumbnail: true
            }
          } else if (slide.type.startsWith('reflect-')) {
            return {
              reflectiveQuestion: slide.data,
              pageNumber: index + 1,
              isThumbnail: true
            }
          }
          return { isThumbnail: true }
      }
    },
    
    // 初始化容器大小监听
    initResizeObserver () {
      const container = this.$refs.slidesContainer
      if (!container) return
      
      // 初始化容器尺寸
      this.updateContainerSize()
      
      // 创建 ResizeObserver
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(entries => {
          for (let entry of entries) {
            this.updateContainerSize()
          }
        })
        this.resizeObserver.observe(container)
      } else {
        // 降级到 window resize 事件
        window.addEventListener('resize', this.updateContainerSize)
      }
    },
    
    // 更新容器尺寸
    updateContainerSize () {
      const container = this.$refs.slidesContainer
      if (!container) return
      
      const rect = container.getBoundingClientRect()
      this.containerWidth = rect.width
      this.containerHeight = rect.height
    },
    
    // 销毁监听器
    destroyResizeObserver () {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      } else {
        window.removeEventListener('resize', this.updateContainerSize)
      }
    },
    
    // 初始化键盘导航
    initKeyboardNavigation () {
      document.addEventListener('keydown', this.handleKeydown)
    },
    
    // 销毁键盘导航
    destroyKeyboardNavigation () {
      document.removeEventListener('keydown', this.handleKeydown)
    },
    
    // 处理键盘事件
    handleKeydown (event) {
      // 全屏模式下直接响应，非全屏模式检查焦点
      const shouldRespond = this.isFullscreen || this.$el.contains(document.activeElement)
      if (!shouldRespond) return
      
      switch (event.key) {
        case 'Escape':
          if (this.isFullscreen) {
            event.preventDefault()
            this.exitFullscreen()
          }
          break
        case 'ArrowLeft':
        case 'ArrowUp':
          event.preventDefault()
          this.previousSlide()
          break
        case 'ArrowRight':
        case 'ArrowDown':
        case ' ': // 空格键也可以切换下一张
          event.preventDefault()
          this.nextSlide()
          break
        case 'Home':
          event.preventDefault()
          this.goToSlide(0)
          break
        case 'End':
          event.preventDefault()
          this.goToSlide(this.slidePages.length - 1)
          break
      }
    },
    
    // 初始化缩略图滚动
    initThumbnailScroll () {
      const thumbnailContainer = this.$el.querySelector('.slide-thumbnails')
      if (thumbnailContainer) {
        thumbnailContainer.addEventListener('wheel', this.handleThumbnailWheel, { passive: false })
      }
    },
    
    // 销毁缩略图滚动
    destroyThumbnailScroll () {
      const thumbnailContainer = this.$el.querySelector('.slide-thumbnails')
      if (thumbnailContainer) {
        thumbnailContainer.removeEventListener('wheel', this.handleThumbnailWheel)
      }
    },
    
    // 处理缩略图区域的滚轮事件
    handleThumbnailWheel (event) {
      // 将垂直滚动转换为水平滚动
      const container = event.currentTarget
      const scrollAmount = event.deltaY * 2 // 调整滚动速度
      
      container.scrollLeft += scrollAmount
    },
    
    // 处理image-changed事件
    handleImageChanged (event) {
      const { imageUrl, imageSource, contentType } = event
      
      if (!this.templateData) return
      
      // 创建 templateData 的深拷贝
      const updatedTemplateData = JSON.parse(JSON.stringify(this.templateData))
      
      // 根据 contentType 和当前幻灯片位置更新对应的图片数据
      switch (contentType) {
        case 'objective':
          if (updatedTemplateData.objectives) {
            updatedTemplateData.objectives.imageUrl = imageUrl
            updatedTemplateData.objectives.imageSource = imageSource
          }
          break
        case 'learning':
          // 找到当前的核心学习幻灯片索引
          const learningIndex = this.getCurrentLearningIndex()
          if (updatedTemplateData.coreLearning && updatedTemplateData.coreLearning[learningIndex]) {
            updatedTemplateData.coreLearning[learningIndex].imageUrl = imageUrl
            updatedTemplateData.coreLearning[learningIndex].imageSource = imageSource
          }
          break
        case 'activity':
          // 找到当前的活动幻灯片索引
          const activityIndex = this.getCurrentActivityIndex()
          if (updatedTemplateData.explorationAndActivity && updatedTemplateData.explorationAndActivity[activityIndex]) {
            updatedTemplateData.explorationAndActivity[activityIndex].imageUrl = imageUrl
            updatedTemplateData.explorationAndActivity[activityIndex].imageSource = imageSource
          }
          break
        default:
          break
      }
      
      // 发出 input 事件，更新父组件的 v-model 数据
      this.$emit('input', updatedTemplateData)
    },
    
    // 获取当前核心学习内容的索引
    getCurrentLearningIndex () {
      const currentSlide = this.slidePages[this.currentSlideIndex]
      if (currentSlide && currentSlide.type.startsWith('core-learning-')) {
        return parseInt(currentSlide.type.split('-')[2]) || 0
      }
      return 0
    },
    
    // 获取当前活动内容的索引
    getCurrentActivityIndex () {
      const currentSlide = this.slidePages[this.currentSlideIndex]
      if (currentSlide && currentSlide.type.startsWith('activity-')) {
        return parseInt(currentSlide.type.split('-')[1]) || 0
      }
      return 0
    },
    
    // 处理容器点击事件
    handleContainerClick(event) {
      // 只在全屏模式下响应点击切换
      if (this.isFullscreen) {
        // 避免点击控制按钮时触发
        if (!event.target.closest('.fullscreen-controls')) {
          this.nextSlide()
        }
      }
    },
    
    // 进入全屏
    async enterFullscreen() {
      try {
        const element = this.$refs.slidesContainer
        if (element.requestFullscreen) {
          await element.requestFullscreen()
        } else if (element.mozRequestFullScreen) {
          await element.mozRequestFullScreen()
        } else if (element.webkitRequestFullscreen) {
          await element.webkitRequestFullscreen()
        } else if (element.msRequestFullscreen) {
          await element.msRequestFullscreen()
        }
      } catch (error) {
      }
    },
    
    // 退出全屏
    async exitFullscreen() {
      try {
        if (document.exitFullscreen) {
          await document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          await document.mozCancelFullScreen()
        } else if (document.webkitExitFullscreen) {
          await document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) {
          await document.msExitFullscreen()
        }
      } catch (error) {
      }
    },
    
    // 监听全屏状态变化
    handleFullscreenChange() {
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      )
      
      // 进入全屏时确保容器获得焦点
      if (this.isFullscreen) {
        this.$nextTick(() => {
          this.$refs.slidesContainer.focus()
        })
      }
    },
    
    // 初始化全屏监听器
    initFullscreenListeners() {
      document.addEventListener('fullscreenchange', this.handleFullscreenChange)
      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('msfullscreenchange', this.handleFullscreenChange)
    },

    // 销毁全屏监听器
    destroyFullscreenListeners() {
      document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('msfullscreenchange', this.handleFullscreenChange)
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initResizeObserver()
      this.initKeyboardNavigation()
      this.initThumbnailScroll()
      this.initFullscreenListeners()
    })
  },
  beforeDestroy () {
    this.destroyResizeObserver()
    this.destroyKeyboardNavigation()
    this.destroyThumbnailScroll()
    this.destroyFullscreenListeners()
  }
}
</script>

<style lang="less" scoped>
.lesson-lecture-slides {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  
  .slides-container {
    flex: 1;
    width: 100%;
    background: #000000;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    
    &.fullscreen-mode {
      border-radius: 0;
      cursor: pointer;
      
      // 移除焦点时的默认outline
      &:focus {
        outline: none;
      }
      
      .slide-content {
        // 全屏模式下禁用内部组件的指针事件，确保点击能触发切换
        pointer-events: none;
        
        // 但保留键盘焦点功能
        * {
          pointer-events: none;
        }
      }
      
      // 全屏控制按钮区域需要恢复指针事件
      .fullscreen-controls {
        pointer-events: auto;
        
        * {
          pointer-events: auto;
        }
      }
    }
    
    .slide-content {
      display: flex;
      align-items: center;
      justify-content: center;
      
      // 确保子组件能正确显示
      & > * {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  .fullscreen-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 12px;
    z-index: 1001;
    
    .el-button {
      // background: rgba(0, 0, 0, 0.7);
      border: none;
      color: white;
      
      &:hover:not(.is-disabled) {
        // background: rgba(0, 0, 0, 0.9);
        color: white;
      }
      
      &.is-disabled {
        background: rgba(0, 0, 0, 0.3);
        color: rgba(255, 255, 255, 0.3);
      }
      
      // 退出按钮特殊样式
      &:last-child {
        background: rgba(255, 0, 25, 0.8);
        
        &:hover {
          background: rgb(255, 0, 25);
        }
      }
    }
  }
  
  .slides-controls {
    flex-shrink: 0;
    width: 100%;
    background: rgba(241,243,244,.95);
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    
    .control-button {
      width: 40px;
      min-width: 40px;
      height: 40px;
      min-height: 40px;
      padding: 0px;
    }

    .slide-thumbnails {
      display: flex;
      gap: 8px;
      justify-content: flex-start;
      max-width: 100%;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 4px;
      white-space: nowrap;
      scroll-behavior: smooth;
      
      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        height: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
      }

      .thumbnail-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 4px;
        border-radius: 8px;
        background: #ffffff;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;
        width: 120px;
        position: relative;
        
        &:hover {
          border-color: #10b3b7;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          transform: translateY(-2px);
        }
        
        &.active {
          border-color: #10b3b7;
          background: #E6F7FF;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          
          .thumbnail-number {
            background: #10b3b7;
            color: white;
          }
        }
      }
      
      .thumbnail-preview {
        width: 108px;
        height: 60.8px;
        margin-bottom: 6px;
        
        .thumbnail-content {
          width: 108px;
          height: 60.8px;
          background: #000;
          border-radius: 4px;
          position: relative;
          overflow: hidden;
          
          // 确保子组件按比例缩放
          & > * {
            width: 1080px !important;
            height: 608px !important;
            transform: scale(0.1) !important;
            transform-origin: top left !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            pointer-events: none !important;
          }
        }
      }
      
      .thumbnail-number {
        position: absolute;
        right: 0;
        bottom: 0;
        font-size: 12px;
        color: white;
        background: #10b3b7;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
      }
    }
    
    .fullscreen-control {
      margin-left: auto;
      padding-left: 20px;
    }
    
    // 移动设备适配
    @media (max-width: 768px) {
      padding: 8px;
      
      .slide-thumbnails {
        gap: 6px;
        
        .thumbnail-item {
          width: 80px;
          padding: 3px;
        }
        
        .thumbnail-preview {
          width: 72px;
          height: 40.5px;
          
          .thumbnail-content {
            width: 72px;
            height: 40.5px;
            overflow: hidden;
            
            & > * {
              width: 1080px !important;
              height: 608px !important;
              transform: scale(0.0667) !important;
              transform-origin: top left !important;
              position: absolute !important;
              top: 0 !important;
              left: 0 !important;
              pointer-events: none !important;
            }
          }
        }
        
        .thumbnail-number {
          width: 18px;
          height: 18px;
          font-size: 10px;
        }
      }
    }
  }
}
</style> 