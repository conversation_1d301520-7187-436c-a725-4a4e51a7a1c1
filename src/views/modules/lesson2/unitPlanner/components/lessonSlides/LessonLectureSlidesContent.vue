<template>
  <div class="lesson-lecture-slides-content" :class="{'no-image': !showImage}">
    <!-- 顶部标题栏 -->
    <div class="content-header">
      <h1 class="header-title">{{ title }}</h1>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="content-main">
      <!-- 左侧内容 -->
      <div class="content-text">
        <div class="text-card">
          <div class="content-body">
            <div class="content-paragraph" v-autofit-text>
              <p v-html="content.trim()"></p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧图片区域 -->
      <div v-if="showImage" class="content-image">
        <div class="image-card">
          <div class="img-container">
            <SlidesImageReplacer
              :is-thumbnail="isThumbnail"
              :image-url="currentImageUrl" 
              :image-alt="imageAlt" 
              :image-source="currentImageSource"
              :image-search-keyword="imageSearchKeyword"
              :is-editable="!isThumbnail && canEdit"
              @image-changed="handleImageChanged"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部品牌信息 -->
    <slide-footer :page-number="pageNumber" />
  </div>
</template>

<script>
import SlideFooter from './SlideFooter.vue'
import SlidesImageReplacer from './SlidesImageReplacer.vue'

export default {
  name: 'LessonLectureSlidesContent',
  components: {
    SlideFooter,
    SlidesImageReplacer
  },
  props: {
    // 标题
    title: {
      type: String,
      default: 'Content Title'
    },
    // 内容文本
    content: {
      type: String,
      default: ''
    },
    // 内容类型：'objective', 'activity', 'learning'
    contentType: {
      type: String,
      default: 'activity'
    },
    // 图片相关
    imageUrl: {
      type: String,
      default: ''
    },
    imageAlt: {
      type: String,
      default: 'Content image'
    },
    imageSource: {
      type: String,
      default: 'www.example.com'
    },
    // 页码
    pageNumber: {
      type: Number,
      default: 1
    },
    // 搜索关键词
    imageSearchKeyword: {
      type: String,
      default: ''
    },
    // 是否为缩略图
    isThumbnail: {
      type: Boolean,
      default: false
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 内部图片状态管理
      currentImageUrl: '',
      currentImageSource: ''
    }
  },
  computed: {
    // 是否显示图片
    showImage() {
      return this.currentImageUrl && this.currentImageUrl.trim() !== ''
    },
  },
  mounted() {
    // 初始化图片数据
    this.currentImageUrl = this.imageUrl
    this.currentImageSource = this.imageSource
  },
  watch: {
    // 监听外部props变化
    imageUrl(newVal) {
      this.currentImageUrl = newVal
    },
    imageSource(newVal) {
      this.currentImageSource = newVal
    }
  },
  methods: {
    
    // 处理图片更换事件
    handleImageChanged(data) {
      this.currentImageUrl = data.imageUrl
      this.currentImageSource = data.imageSource
      
      // 向父组件传递更改事件
      this.$emit('image-changed', {
        imageUrl: data.imageUrl,
        imageSource: data.imageSource,
        contentType: this.contentType
      })
    }
  }
}
</script>

<style lang="less" scoped>

.no-image {
  background-image: url('~@/assets/img/lesson2/unitPlanner/slides/no_image_activity.png') !important;
  .content-text {
    left: 180px !important;
    right: 160px !important;
    top: 40px !important;
    bottom: 20px !important;
    .content-paragraph {
      height: 356px !important;
      width: 100%;  
      font-size: 30px; /* no-image 情况下的字体大小 */
      color: #333;
      font-weight: 600;
      line-height: 1.2;
      margin: 0;
      text-align: left;
    }
  }
}

.lesson-lecture-slides-content {
  width: 100%;
  aspect-ratio: 16 / 9;
  background-image: url('~@/assets/img/lesson2/unitPlanner/slides/activity.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
  
  .content-header {
    padding: 30px 40px;
    
    .header-title {
      color: white;
      font-size: 36px;
      font-weight: 700;
      margin: 0;
      text-align: center;
    }
  }
  
  .content-main {
    flex: 1;
    display: flex;
    white-space: pre-line;
    gap: 36px;
    margin: 24px 35px;
    position: relative;
    
    .content-text {
      position: absolute;
      left: 20px;
      right: 580px;
      top: 10px;
      bottom: 0px;
      display: flex;
      align-items: center;
      
      .content-paragraph {
        height: 400px;
        width: 100%;
        font-size: 24px; /* 添加初始字体大小 */
        color: #333;
        font-weight: 600;
        line-height: 1.2;
        margin: 0;
        text-align: left;
      }
    }
    
    .content-image {
      position: absolute;
      left: 488px;
      right: 10px;
      top: 12px;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .image-card {
        width: 100%;
        height: 100%;
      }

      .img-container {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .image-source {
        position: absolute;
        margin-top: 14px;
        text-align: center;
        width: 100%;
        
        span {
          color: #999;
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
  }
}

// 当没有图片时，使用活动页面的样式
.lesson-lecture-slides-content.activity-style {
  .content-main {
    align-items: center;
    justify-content: center;
    padding: 60px 40px;
    
    .content-text {
      width: 100%;
      max-width: 1000px;
      
      .text-card {
        background: white;
        border: 4px solid #4ECDC4;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        
        .content-body {
          background: white;
          padding: 60px 80px;
          
          .content-instructions .instruction-item .instruction-text {
            font-size: 28px;
          }
        }
      }
    }
  }
}
</style> 