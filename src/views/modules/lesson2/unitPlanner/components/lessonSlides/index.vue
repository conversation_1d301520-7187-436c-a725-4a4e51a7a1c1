<template>
  <div v-if="showSlide && !isCenterLesson" :class="customClass">
    <!-- 课程幻灯片 -->
    <div class="lesson-slide-card">
      <div class="lesson-slide-content">
        <div class="lesson-slide-info">
          <!-- 封面 -->
          <div class="lesson-slide-cover" v-if="slide && slide.custom || !templateData">
            <slide-cover :presentationId="slide && slide.presentationId" height="100%" width="100%" @previewSlides="previewSlides"></slide-cover>
          </div>
          <div class="lesson-slide-cover lesson-slide-cover-custom" @click.stop="previewSlides" v-else>
            <lesson-lecture-slides-cover :title="templateData && templateData.lessonTitle" :subTitle="templateData && templateData.lessonSubtitle"></lesson-lecture-slides-cover>
          </div>
          <!-- 名称、页数、操作按钮 -->
          <div class="lesson-slide-detail">
            <div class="lesson-slide-title text-ellipsis" :title="$t('loc.lessonSlide9') + ': ' + lessonName">{{ $t('loc.lessonSlide9') }}: {{ lessonName }}</div>
            <div class="lesson-slide-desc">{{ $t('loc.lessonSlide17') }}</div>
            <div class="lesson-slide-count">
                {{ $t('loc.lessonSlide1') }}: {{ slide ? slide.pageCount || 0 : 0 }}
            </div>
            <div class="lesson-slide-actions">
              <!-- 查看按钮 -->
              <el-button v-if="slide" type="primary"
                icon="el-icon-view el-icon--left"
                :loading="generating"
                :disabled="lessonLoading || loadingSlides" @click="previewSlides">
                {{ $t('loc.lessonSlide2') }}
              </el-button>
               <!-- 生成按钮 -->
               <el-button v-else type="primary"
                icon="lg-icon lg-icon-generate el-icon--left"
                class="ai-btn"
                :disabled="lessonLoading || loadingSlides || generating"
                @click="generateSlides">
                {{ $t('loc.lessonSlide3') }}
              </el-button>
            </div>
          </div>
          <!-- 重新生成按钮 -->
          <div class="lesson-slide-regenerate" v-if="slide && edit">
              <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
                <!-- 重新生成 -->
                <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                          @click="regenerateSlides"
                          :disabled="lessonLoading"
                          :loading="generating">
                </el-button>
              </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <!-- google slide 预览弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :fullscreen="true"
      :close-on-click-modal="false"
      :append-to-body="true"
      @closed="closeDialog"
      :title="$t('loc.lessonSlide9')"
      custom-class="lesson-slide-preview-dialog">
      <div class="display-flex bg-white lg-padding-24 lg-border-radius-8 lg-box-shadow h-full remove-padding-r" v-if="slide">
        <!-- iframe embed v-loading="loading && !!link" -->
        <div  class="h-full" style="width: calc(100% - 400px);" v-if="dialogVisible">
          <!-- 用户手动上传的幻灯片 -->
          <iframe v-if="slide && slide.custom" :src="embedLink" frameborder="0" style="width: 100%; height:100%;" @load="loaded"
                  class="lg-border-radius-8"></iframe>
          <!-- 系统模板幻灯片 -->
          <lesson-lecture-slides
            v-else
            v-model="templateData"
            :can-edit="canEdit"
            @input="updateLessonSlides"
          />
        </div>
        <!-- 右侧信息及操作按钮 -->
        <div class="lg-margin-left-24 w-full lg-scrollbar-show lg-padding-right-24" style="width: 400px;">
          <!-- 幻灯片信息 -->
          <div class="title-font-20 overflow-ellipsis-two" :title="lessonName">{{ lessonName }}</div>

          <!-- 更新时间 -->
          <div class="lg-margin-top-16">
            <span class="title-font-16-regular">{{ $t('loc.lessonSlide10') }}{{ $moment.utc((new Date(slide.updateAtUtc))).local().format('MM/DD/YYYY [at] HH:mm A') }}</span>
          </div>

          <!-- 操作按钮 -->
          <div class="lg-margin-top-16">
            <!-- 导出到 google slides -->
            <el-button class="w-full display-flex align-items justify-content el-button-warning-dark"
                        :loading="userExported"
                        :disabled="generating"
                        @click="saveLessonSlidesToDrive">
                <span class="display-flex align-items justify-content">
                    <img :src="require('@/assets/img/lesson2/unitPlanner/google_slide.svg')" height="24">
                    <span class="lg-margin-left-8">{{ $t('loc.eduprotocols12') }}</span>
                </span>
            </el-button>
          </div>
          <div class="lg-margin-top-16">
            <!-- 分享到 Classroom -->
            <a :href="shareToClassRoomLink" target="_blank">
                <el-button class="w-full" plain :disabled="generating" @click="shareToClassRoom">
                    <span class="display-flex align-items justify-content">
                        <img :src="require('@/assets/img/lesson2/unitPlanner/share_to_classroom.svg')"
                              height="24">
                        <span class="lg-margin-left-8 title-font-14">{{ $t('loc.eduprotocols13') }}</span>
                    </span>
                </el-button>
            </a>
          </div>
          <div class="lg-margin-top-16">
              <!-- 下载 PPT -->
              <a :href="downloadPPTLink" target="_blank">
                  <el-button class="w-full" plain :disabled="generating" @click="downloadPPT">
                      <span class="display-flex align-items justify-content">
                          <img :src="require('@/assets/img/file/ppt.png')" height="24">
                          <span class="lg-margin-left-8 title-font-14">{{ $t('loc.eduprotocols14') }}</span>
                      </span>
                  </el-button>
              </a>
          </div>

          <!-- 替换幻灯片 -->
          <div class="lg-margin-top-32" v-if="canEdit">
            <div class="lg-margin-bottom-16">
              <span class="title-font-16">{{ $t('loc.lessonSlide4') }}</span>
              <el-tooltip :content="$t('loc.lessonSlide7')" placement="top">
                <i class="lg-icon lg-icon-question font-size-16 lg-margin-left-8"></i>
              </el-tooltip>
            </div>
            <!-- 粘贴链接 -->
            <div class="display-flex align-items w-full link-input">
              <i class="lg-icon lg-icon-link"></i>
              <el-input v-model="slideLink"
                :disabled="parsing"
                clearable
                @keyup.enter.native="replaceByLink"
                @input="checkLink"
                :placeholder="$t('loc.lessonSlide5')"
              />
              <el-button slot="append" type="primary" size="mini" :disabled="linkInputError || !slideLink" :loading="parsing" @click="replaceByLink(true)">
                  {{ $t('loc.apply') }}
                </el-button>
            </div>
            <!-- 错误提示 -->
            <div v-show="linkInputError" class="lg-margin-top-8 title-font-12-regular lg-color-danger">
              <!-- 链接未授权 -->
              <template v-if="linkUnauthorized">
                {{ $t('loc.lessonSlide16') }}
              </template>
              <!-- 链接错误 -->
              <template v-else>
                {{ $t('loc.lessonSlide8') }}
              </template>
            </div>
            <!-- 分割线 -->
            <div class="lg-margin-top-16 lg-margin-bottom-16 text-center lg-color-text-secondary">
              {{ $t('loc.or') }}
            </div>
            <!-- 上传按钮 handleUpload -->
            <el-upload action :accept="mediaType" :multiple="false" :http-request="handleUpload"
               class="upload-slide" :show-file-list="false" :before-upload="beforeUpload" :disabled="parsing">
               <el-button class="w-full" plain :loading="uploading" :disabled="parsing" icon="lg-icon lg-icon-upload font-size-24">
                  <span class="lg-margin-left-8 title-font-14">{{ $t('loc.lessonSlide6') }}</span>
              </el-button>
            </el-upload>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 替换幻灯片弹窗 -->
    <el-dialog
      :visible.sync="replaceDialogVisible"
      :title="$t('loc.lessonSlide4')"
      custom-class="lesson-slide-replace-dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      @close="closeReplaceDialog"
      width="750px">
      <div v-if="parsing" class="replace-dialog-content">
        <div class="display-flex align-items flex-direction-col">
          <div class="lg-margin-bottom-16">
            <i class="lg-color-primary el-icon-loading font-size-40"></i>
          </div>
          <div class="title-font-16-regular">
            {{ $t('loc.lessonSlide11') }}
          </div>
        </div>
      </div>
      <div v-else class="lg-margin-top-24">
        <div class="title-font-16-regular" v-html="$t('loc.lessonSlide12', { num: pageCount })"></div>
        <div class="lg-margin-top-16">
          <slide-cover :presentationId="presentationId" :preview="true" height="430px"></slide-cover>
        </div>
      </div>
      <span slot="footer" v-if="!parsing">
        <el-button @click="replaceDialogVisible = false" :disabled="replacing">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="replaceSlide" :loading="replacing">{{ $t('loc.lessonSlide13') }}</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import credentials from '@/components/googleAuth/credentials.json'
import fileUtils from '@/utils/file.js'
import SlideCover from './SlideCover.vue'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import { equalsIgnoreCase } from '../../../../../../utils/common'
import LessonLectureSlides from './LessonLectureSlides.vue'
import LessonLectureSlidesCover from './LessonLectureSlidesCover.vue'
import lessonUtils from '../../../../../../utils/lessonUtils'

export default {
  name: 'LessonSlides',
  components: {
    SlideCover,
    LessonLectureSlides,
    LessonLectureSlidesCover
  },
  props: {
    // 自定义样式类名
    customClass: {
      type: String,
      default: ''
    },
    // 课程 ID
    lessonId: {
      type: String,
      required: true
    },
    // 课程名称
    lessonName: {
      type: String,
      required: true
    },
    // 是否可以编辑
    edit: {
      type: Boolean,
      default: false
    },
    // 是否有编辑权限
    canEdit: {
      type: Boolean,
      default: true
    },
    // 课程加载中
    lessonLoading: {
      type: Boolean,
      default: false
    },
    // 课程加载中
    activityType: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      loadingSlides: false, // 正在加载课程幻灯片
      showSlide: true, // 是否显示幻灯片卡片
      slide: null, // 幻灯片
      generating: false, // 生成中
      loading: false, // 加载中
      dialogVisible: false, // 预览弹窗
      replaceDialogVisible: false, // 编辑弹窗
      userExported: false, // 是否已导出
      CLIENT_ID: credentials.web.client_id, // google 客户端 ID
      scope: credentials.web.scope, // google 授权范围
      googleDriveTokenClient: null, // google 驱动 token 客户端
      googleAuthCode: null, // google 认证码
      needAuth: false, // 是否需要谷歌认证
      slideLink: '', // 幻灯片链接
      linkInputError: false, // 链接输入错误
      linkUnauthorized: false, // 链接未授权
      parsing: false, // 解析中
      replacing: false, // 替换中
      presentationId: null, // 幻灯片 ID
      fileId: null, // 上传的文件 ID
      pageCount: 0, // 页数
      uploading: false, // 上传中
      version: 0, // 版本
      timeout: null // 定时器
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    }),
    mediaType () {
      return '.ppt,.pptx'
    },
    // google slide 链接
    link () {
      if (this.slide && this.slide.presentationId) {
        return `https://docs.google.com/presentation/d/${this.slide.presentationId}`
      }
      return ''
    },
    // embed 链接
    embedLink () {
      return this.link ? `${this.link}/embed` : ''
    },
    // 分享到 Classroom 链接
    shareToClassRoomLink () {
      return this.link ? `https://classroom.google.com/share?url=${this.link}` : ''
    },
    // 下载 PPT 链接
    downloadPPTLink () {
      return this.link ? `${this.link}/export/pptx` : ''
    },
    // 模板数据
    templateData () {
      return this.slide && this.slide.metadata
    },
    // 是否是 center 课程
    isCenterLesson () {
      return lessonUtils.isCenterLesson(this.activityType)
    }
  },
  watch: {
    // 监听 lessonId 变化, 加载幻灯片
    lessonId: {
      handler () {
        this.loadSlides()
      },
      immediate: true
    },
    slide: {
      handler (val) {
        // 设置是否有模板资源
        if (this.slide) {
          if (this.link && this.link !== '') {
            // 有直接可下载资源
            this.$store.dispatch('setHasSlidesResource', this.downloadPPTLink)
          } else {
            // 只有预下载数据
            this.$store.dispatch('setHasSlidesResource', 'NO_PRESENTATION_ID')
          }
        } else {
          this.$store.dispatch('setHasSlidesResource', '')
        }
      },
      immediate: true
    }
  },
  created () {
    // 监听刷新幻灯片
    this.$bus.$on('refreshLessonSlides', (lessonId) => {
      if (equalsIgnoreCase(lessonId, this.lessonId)) {
        this.loadSlides()
      }
    })
    // 初始化 Google API
    this.gapiLoaded()
    this.gisLoaded()
  },
  beforeDestroy () {
    this.$bus.$off('refreshLessonSlides')
  },
  methods: {
    // 打开预览
    previewSlides () {
      if (this.slide) {
        if (!this.slide.status || equalsIgnoreCase(this.slide.status, 'SUCCESS')) {
          this.openDialog()
        } else {
          this.$message.warning(this.$t('loc.lessonSlide21'))
        }
      }
    },
    // 加载幻灯片
    async loadSlides (autoRefresh = false) {
      if (!this.lessonId) {
        return
      }
      try {
        this.loadingSlides = !autoRefresh // 自动刷新时, 不显示 loading
        // API 调用获取课程 slides
        const res = await this.$axios.get($api.urls().getLessonSlides, {
          params: { lessonId: this.lessonId }
        })
        // 设置幻灯片
        this.slide = res
        // 如果幻灯片状态为处理中, 则定时刷新
        if (this.slide && this.slide.status && (equalsIgnoreCase(this.slide.status, 'PROCESSING'))) {
          this.timeout = setTimeout(() => {
            this.loadSlides(true)
          }, 3000)
        } else if (this.slide && this.slide.status && (equalsIgnoreCase(this.slide.status, 'PENDING') || equalsIgnoreCase(this.slide.status, 'FAILED'))) {
          // 如果幻灯片状态为生成失败或者待生成, 则调接口生成幻灯片
          this.$axios.post($api.urls().generateLessonSlides, {}, { params: { taskId: this.slide.id }})
          .then(() => {
            // 调完接口后, 定时刷新
            this.timeout = setTimeout(() => {
              this.loadSlides(true)
            }, 3000)
          })
        } else {
          // 如果幻灯片状态为成功, 则清除定时器
          this.timeout && clearTimeout(this.timeout)
        }
        // 根据是否可以编辑和是否存在幻灯片设置是否隐藏幻灯片卡片
        this.showSlide = this.edit || this.slide !== null
        this.loadingSlides = false
      } catch (error) {
        this.loadingSlides = false
        this.$message.error(error.message)
      }
    },

    // 文件上传校验
    beforeUpload (file) {
      let fileSize = file.size / 1024 / 1024
      // 限制文件上传大小为 20 MB
      if (fileSize > 20) {
        this.$message.error(this.$t('loc.lessonSlide19'))
        return false
      }
      // 校验文件类型
      let extension = file.name.substring(file.name.lastIndexOf('.'))
      let acceptable = this.mediaType.split(',').find(a => a.toLowerCase().trim() === extension.toLowerCase())
      if (!acceptable) {
        this.$message.error(this.$t('loc.lessonSlide18'))
        return false
      }
      return true
    },

    // 上传 slides 文件
    async handleUpload ({ file }) {
      // 点击上传幻灯片按钮
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_upload')
      this.version += 1
      let version = this.version
      this.uploading = true
      this.parsing = true
      // 显示替换幻灯片弹窗埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_replace')
      this.replaceDialogVisible = true
      fileUtils.uploadFile(file, {
        privateFile: false,
        processEventHandler: (progressEvent) => {
        }
      })
      .then(res => {
        if (version === this.version && this.replaceDialogVisible) {
          this.fileId = res.id
          this.parseSlide(this.fileId, null, version)
        }
      })
      .catch(error => {
        this.replaceDialogVisible = false
        this.uploading = false
        this.parsing = false
        this.$message.error(error.message)
      })
    },

    // 解析幻灯片
    parseSlide (fileId, presentationId, version) {
      if (version !== this.version) {
        return
      }
      let params = {
        fileId: fileId,
        presentationId: presentationId,
        lessonId: this.lessonId
      }
      this.$axios.post($api.urls().uploadLessonSlides, params)
      .then(res => {
        if (version !== this.version || (fileId && !this.replaceDialogVisible)) {
          this.$axios.post($api.urls().deleteTempLessonSlides, {}, { params: { id: res.presentationId } })
          return
        }
        this.slideLink = ''
        // 显示替换幻灯片弹窗埋点
        this.$analytics.sendEvent('cg_unit_lesson_slide_pop_replace')
        this.replaceDialogVisible = true
        this.fileId = res.fileId
        this.presentationId = res.presentationId
        this.pageCount = res.pageCount
        this.uploading = false
        this.parsing = false
      }).catch(error => {
        // 关闭替换弹窗
        this.replaceDialogVisible = false
        this.uploading = false
        this.parsing = false
        let errorCode = error.response.data.code
        // 根据错误码显示不同提示语
        if (errorCode === 'no_auth') {
          this.linkInputError = true
          this.linkUnauthorized = true
        } else if (errorCode === 'no_data') {
          this.linkInputError = true
          this.linkUnauthorized = false
        } else {
          this.$message.error(error.message)
        }
      })
    },

    // 关闭替换幻灯片弹窗回调
    closeReplaceDialog () {
      // 关闭替换幻灯片弹窗埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_replace_cancel')
      if (this.replacing) {
        return
      }
      // 如果 presentationId 存在, 则删除临时幻灯片
      if (this.presentationId) {
        this.$axios.post($api.urls().deleteTempLessonSlides, {}, { params: { id: this.presentationId } })
      }
      this.replaceDialogVisible = false
      this.parsing = false
      this.uploading = false
      this.linkInputError = false
      this.linkUnauthorized = false
    },

    // 替换幻灯片
    replaceSlide () {
      // 替换幻灯片埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_replace_confirm')
      // 设置替换中状态
      this.replacing = true
      // 替换幻灯片
      this.$axios.post($api.urls().updateLessonSlides, {
        lessonId: this.lessonId,
        fileId: this.fileId,
        presentationId: this.presentationId,
        generateSlide: true
      })
      .then(async () => {
        // 替换成功
        this.replacing = false
        this.presentationId = null
        await this.loadSlides()
        this.$bus.$emit('refreshLessonSlides', this.lessonId)
        this.replaceDialogVisible = false
        this.$message.success(this.$t('loc.lessonSlide14'))
      })
      .catch(error => {
        this.replacing = false
        this.$message.error(error.message)
      })
    },

    // 检查链接
    checkLink () {
      // 清空链接无权访问错误显示
      this.linkUnauthorized = false
      if (!this.slideLink.trim()) {
        this.linkInputError = false
        return ''
      }
      // 正则匹配 google slide 链接, 并提取 presentationId
      const regex = /https:\/\/docs\.google\.com\/presentation\/d\/([a-zA-Z0-9_-]+)/
      const match = this.slideLink.match(regex)
      if (match) {
        this.linkInputError = false
        return match[1]
      } else {
        this.linkInputError = true
        return ''
      }
    },

    // 更新幻灯片
    updateLessonSlides (data) {
      this.slide.metadata = data
      this.$axios.post($api.urls().updateLessonSlides, {
        lessonId: this.lessonId,
        presentationId: this.slide.presentationId,
        lessonName: this.lessonName,
        lessonSlides: {
          lessonTitle: this.lessonName,
          ...data
        }
      })
    },
    // 打开预览弹窗
    openDialog () {
      // 点击查看课程幻灯片按钮
      this.$analytics.sendEvent('cg_unit_lesson_slide_view')
      // 显示课程幻灯片弹窗埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop')
      if (this.slide && this.slide.presentationId) {
        if (this.slide.custom || !this.edit) {
          this.dialogVisible = true
          return
        }
        this.generating = true
        this.$axios.post($api.urls().updateLessonSlides, {
          lessonId: this.lessonId,
          presentationId: this.slide.presentationId,
          lessonName: this.lessonName,
          generateSlide: true,
          lessonSlides: {
            ...this.templateData,
            lessonTitle: this.lessonName
          }
        })
        .then(async () => {
          await this.loadSlides()
          this.dialogVisible = true
          this.generating = false
        })
        .catch(error => {
          this.generating = false
        })
      } else {
        this.generating = true
        this.$axios.post($api.urls().createLessonSlides, {
          lessonId: this.lessonId,
          taskId: this.slide.id,
          generateSlide: true
        })
        .then(async () => {
          await this.loadSlides()
          this.dialogVisible = true
          this.generating = false
        })
        .catch(error => {
          this.generating = false
        })
      }
    },

    // 关闭弹窗
    closeDialog () {
      // 关闭课程幻灯片弹窗埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_close')
      this.loading = true
      this.linkInputError = false
      this.linkUnauthorized = false
      this.slideLink = ''
    },

    // Google API 相关方法
    gapiLoaded () {
      if (typeof gapi !== 'undefined') {
        gapi.load('client:picker', this.initializePicker)
      }
    },

    initializePicker () {
      if (typeof gapi !== 'undefined') {
        gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest')
      }
    },

    gisLoaded () {
      if (typeof google !== 'undefined') {
        this.googleDriveTokenClient = google.accounts.oauth2.initCodeClient({
          client_id: this.CLIENT_ID,
          scope: this.scope,
          ux_mode: 'popup',
          callback: ''
        })
      }
    },

    // iframe 加载完成
    loaded () {
      this.loading = false
    },

    // 导出到 Google Slides
    async saveLessonSlidesToDrive () {
      // 导出到 Google Slides 埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_open')
      if (this.needAuth) {
        this.userExported = true
        this.handlerGoogleAuth()
        return
      }

      this.userExported = true
      try {
        const res = await this.$axios.post($api.urls().saveLessonSlidesToDrive, {
          taskId: this.slide.id,
          slideName: this.lessonName,
          scope: 'drivefile'
        })

        // 下载成功弹出跳转页面
        const title = 'loc.lessons2SuccessfullyExported'
        const downloadButton = 'loc.lessons2GoDriveButton'
        const iconUrl = require('@/assets/img/file/ppt.png')
        this.$alert(
            `<p class="word_keep-all lesson-message-box-pdf-confirm">
                            <img src="${iconUrl}">
                            <span>${this.$t('loc.lessonSlide15', { slidesName: this.lessonName })}</span>
                      </p>`,
            this.$t(title), {
                dangerouslyUseHTMLString: true,
                confirmButtonText: this.$t(downloadButton),
                customClass: 'successfully-exported-message-box'
            }).then(() => {
            // 跳转到导出成功的页面
            window.open(res.url)
        })
      } catch (error) {
        let errorCode = error.response.data.error_code
        if (errorCode === 'unauthorized') {
          this.needAuth = true
          this.handlerGoogleAuth()
        } else {
          this.$message.error(error.response.data.message)
        }
      } finally {
        this.userExported = false
      }
    },

    // 分享到 Classroom
    shareToClassRoom () {
      // 分享到 Classroom 埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_share')
    },

    // 下载 PPT
    downloadPPT () {
      // 下载 PPT 埋点
      this.$analytics.sendEvent('cg_unit_lesson_slide_pop_down')
    },
    // 处理 Google 认证
    handlerGoogleAuth () {
      this.googleDriveTokenClient.callback = async (response) => {
        if (response.error !== undefined) {
          this.userExported = false
          throw response
        }

        this.googleAuthCode = response.code
        if (response.scope !== this.scope) {
          this.googleDriveTokenClient.requestCode()
          return
        }

        await this.checkGoogleAuth(true)
      }

      if (this.googleAuthCode === null) {
        this.googleDriveTokenClient.requestCode()
      } else {
        this.googleDriveTokenClient.requestCode()
      }
    },

    // 检查 Google 认证状态
    async checkGoogleAuth (mustBeLoggedIn = false) {
      const credentialsRequest = {
        authCode: this.googleAuthCode,
        scope: this.scope,
        userId: this.currentUser.user_id,
        scopeKey: 'drivefile',
        onlyCheck: ''
      }

      try {
        const res = await this.$axios.post($api.urls().checkGoogleAuth, credentialsRequest)
        this.needAuth = !res.success
        if (res.success && this.userExported) {
          await this.saveLessonSlidesToDrive()
        }
      } catch (error) {
        console.error('Failed to check Google auth:', error)
      }
    },

    // 重新生成幻灯片
    async regenerateSlides () {
      this.generateSlides(true)
    },

    // 生成幻灯片
    async generateSlides (regenerate = false) {
      if (!this.lessonId) {
        return
      }
      if (!this.$route.path.startsWith('/lessons/lesson-library')) {
        if (regenerate) {
          this.$analytics.sendEvent('cg_unit_lesson_slide_regenerate')
        } else {
          this.$analytics.sendEvent('cg_unit_lesson_slide_generate')
        }
      }
      // 复制一份 slide 数据，用于在生成过程中使用
      this.tempSlide = this.slide ? JSON.parse(JSON.stringify(this.slide)) : null
      // 清空当前 slide 数据
      this.slide = null
      // 设置生成状态
      this.generating = true
      this.$emit('updateGenerateLessonSlidesLoading', true)
      this.$emit('updateGenerateLessonSlidesStreamLoading', true)
      // 初始化 lessonSlidesData 和 lessonSlides
      let lessonSlidesData = ''
      let lessonSlides = {}
      // 定义消息回调函数
      let messageCallback = (message) => {
        lessonSlidesData += message.data
        // 解析分多步骤
        // 第一步, 解析出七部分
        let step1Keys = [
          { key: 'section1', name: ['Section 1'] },
          { key: 'section2', name: ['Section 2'] },
          { key: 'section3', name: ['Section 3'] },
          { key: 'section4', name: ['Section 4'] },
          { key: 'section5', name: ['Section 5'] },
          { key: 'section6', name: ['Section 6'] },
          { key: 'section7', name: ['Section 7'] }
        ]
        let step1Data = parseStreamData(lessonSlidesData, step1Keys)[0]
        // 第二步, 解析出七部分各自的内容
        let section1Keys = [
          { key: 'page', name: ['Page'] },
          { key: 'title', name: ['Title'] },
          { key: 'subtitle', name: ['Subtitle'] }
        ]
        let section1Data = parseStreamData(step1Data.section1, section1Keys)[0]
        let section2Keys = [
          { key: 'page', name: ['Page'] },
          { key: 'leadInQuestion', name: ['Lead-in Question'] }
        ]
        let section2Data = parseStreamData(step1Data.section2, section2Keys)
        let section3To5Keys = [
          { key: 'page', name: ['Page'] },
          { key: 'title', name: ['Title'] },
          { key: 'content', name: ['Content'] },
          { key: 'imageSearchKeyword', name: ['Image Search Keyword'] }
        ]
        let section3Data = parseStreamData(step1Data.section3, section3To5Keys)[0]
        let section4Data = parseStreamData(step1Data.section4, section3To5Keys)
        let section5Data = parseStreamData(step1Data.section5, section3To5Keys)
        let section6Keys = [
          { key: 'page', name: ['Page'] },
          { key: 'title', name: ['Title'] },
          { key: 'content', name: ['Content'] }
        ]
        let section6Data = parseStreamData(step1Data.section6, section6Keys)
        let section7Keys = [
          { key: 'page', name: ['Page'] },
          { key: 'reflectiveQuestion', name: ['Reflective Question'] }
        ]
        let section7Data = parseStreamData(step1Data.section7, section7Keys)
        // 第三步, 组装七部分内容
        lessonSlides = {
          lessonTitle: section1Data.title,
          lessonSubtitle: section1Data.subtitle,
          leadInQuestions: section2Data.map(item => item.leadInQuestion),
          objectives: { title: section3Data.title, content: section3Data.content, imageSearchKeyword: section3Data.imageSearchKeyword },
          coreLearning: section4Data.map(item => ({ title: item.title, content: item.content, imageSearchKeyword: item.imageSearchKeyword })),
          explorationAndActivity: section5Data.map(item => ({ title: item.title, content: item.content, imageSearchKeyword: item.imageSearchKeyword })),
          funFacts: section6Data.map(item => item.content),
          reflectiveQuestions: section7Data.map(item => item.reflectiveQuestion)
        }
      }
      let params = {
        lessonId: this.lessonId
      }
      // 创建请求事件源
      createEventSource($api.urls().generateLessonSlidesStream, null, messageCallback, 'POST', params)
      .then(res => {
        // 创建幻灯片
        this.$axios.post($api.urls().createLessonSlides, {
          lessonId: this.lessonId,
          lessonSlides: lessonSlides,
          generateSlide: regenerate
        })
        .then(async () => {
          this.$emit('updateGenerateLessonSlidesStreamLoading', false)
          if (this.$route.path.includes('/lessons/lesson-library/add-lesson')) {
            this.$message.closeAll()
          }
          this.$message.success(this.$t('loc.lessonSlide20'))
          // 加载幻灯片
          await this.loadSlides()
          // 设置生成状态
          this.generating = false
          this.$emit('updateGenerateLessonSlidesLoading', false)
          // 清空临时幻灯片数据
          this.tempSlide = null
        })
        .catch(error => {
          // 恢复临时幻灯片数据
          this.slide = this.tempSlide
          this.tempSlide = null
          // 设置生成状态
          this.generating = false
          this.$emit('updateGenerateLessonSlidesStreamLoading', false)
          this.$emit('updateGenerateLessonSlidesLoading', false)
        })
      })
      .catch(error => {
        // 恢复临时幻灯片数据
        this.slide = this.tempSlide
        this.tempSlide = null
        // 设置生成状态
        this.generating = false
        this.$emit('updateGenerateLessonSlidesStreamLoading', false)
        this.$emit('updateGenerateLessonSlidesLoading', false)
        // 提示生成失败
        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
      })
    },

    // 替换幻灯片
    async replaceByLink (click = false) {
      if (click) {
        // 点击识别幻灯片按钮
        this.$analytics.sendEvent('cg_unit_lesson_slide_pop_apply')
      }
      let presentationId = this.checkLink()
      if (!presentationId) {
        return
      }
      this.parsing = true
      try {
        this.version += 1
        let version = this.version
        this.parseSlide(null, presentationId, version)
      } catch (error) {
        this.$message.error(error.message)
        this.parsing = false
        this.replaceDialogVisible = false
      }
    },
  }
}
</script>

<style lang="less" scoped>
.lesson-slide {
  &-card {
    background-color: #F5FEFF;
    background-image: url('../../../../../../assets/img/lesson2/unitPlanner/lesson_slides_bg.png');
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    border-radius: 8px;
    padding: 20px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, #F5FEFF 60%, transparent);
      pointer-events: none;
    }
  }

  &-content {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    z-index: 1;
  }

  &-info {
    display: flex;
    align-items: flex-start;
    flex: 1;
    width: 100%;
  }

  &-cover {
    width: 240px;
    min-width: 240px;
    height: 135px;
    min-height: 135px;
    border-radius: 8px;
    background: #CBE8EA;
    position: relative;
    margin-right: 12px;
    margin-bottom: 12px;

    img, .slide-cover {
      object-fit: cover;
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 8px;
      left: 12px;
      top: 12px;
    }
  }

  &-cover-custom {
    cursor: pointer;
    overflow: hidden;
    
    // 确保子组件按比例缩放，类似缩略图处理方式
    .lesson-lecture-slides-cover {
      width: 1080px !important;
      height: 608px !important;
      transform: scale(0.222) !important; // 240/1080 ≈ 0.222
      transform-origin: top left !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
    }
  }

  &-detail {
    margin-left: 24px;
    flex: 1;
    line-height: 24px;
    width: calc(100% - 300px);
  }

  &-title {
    font-size: 20px;
    font-weight: 600;
  }

  &-count, &-desc {
    font-size: 16px;
    margin: 12px 0;
    color: var(--color-text-secondary);
  }

  &-tag {
    background: var(--color-primary);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    line-height: 14px;
  }

  &-description {
    font-size: 14px;
    color: var(--color-text-regular);
    margin-bottom: 12px;
  }

  &-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

/deep/ .lesson-slide-preview-dialog {
  max-height: calc(100% - 40px) !important;
  height: 100%;
  background-color: var(--color-page-background-white);
  margin-top: 40px !important;
  overflow: hidden;

  & .el-dialog__header {
    margin-bottom: 10px;
    padding-left: 36px;
    padding-right: 36px;
  }

  & .el-dialog__title {
    font-size: 20px;
  }

  & .el-dialog__headerbtn {
    right: 36px;
  }

  & .el-dialog__body {
    padding: 0 36px;
    height: calc(100% - 80px);
  }

  & .el-dialog__headerbtn .el-dialog__close {
    color: var(--color-text-placeholder);
  }

 .link-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 2px solid var(--color-border);
    border-radius: 4px;
    background-color: var(--color-page-background-white);
    padding: 0 8px;

    .el-input .el-input__inner {
      border: none;
      background-color: inherit;
      padding-left: 6px;
    }
  }

  .upload-slide {
    .el-upload {
      width: 100%;
    }

    .el-button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

/deep/ .lesson-slide-replace-dialog {
  & .el-dialog__header {
    padding: 24px 24px 0px;
  }

  & .el-dialog__title {
    font-size: 20px;
  }

  & .el-dialog__body {
    padding: 0px 24px 0px;
  }

  & .el-dialog__footer {
    padding: 24px;
  }

  .replace-dialog-content {
    display: flex;
    height: 430px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    align-self: stretch;
  }
}
</style>
