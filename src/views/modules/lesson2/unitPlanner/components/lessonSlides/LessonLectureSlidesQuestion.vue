<template>
  <div class="lesson-lecture-slides-question"> 
    <!-- 主要内容 -->
    <div class="question-content">
      <div class="question-content-wrapper" v-autofit-text>
        <span class="bullet">•</span>
        <!-- 问题内容区域 -->
        <div class="text">{{ question }}</div>
      </div>
    </div>
    <!-- 底部品牌信息 -->
    <slide-footer :page-number="pageNumber" />
  </div>
</template>

<script>
import SlideFooter from './SlideFooter.vue'

export default {
  name: 'LessonLectureSlidesQuestion',
  components: {
    SlideFooter
  },
  props: {
    question: {
      type: String
    },
    pageNumber: {
      type: Number
    }
  }
}
</script>

<style lang="less" scoped>
.lesson-lecture-slides-question {
  width: 100%;
  aspect-ratio: 16 / 9;
  background-image: url('~@/assets/img/lesson2/unitPlanner/slides/leading_question.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
  
  .question-content {
    height: 100%;
    padding: 140px 140px 100px 140px;
    white-space: pre-line;
    .question-content-wrapper {
      height: 100%;
      display: flex;
      align-items: center;
      gap: 20px;
      .bullet {
        color: #333;
        font-weight: bold;
        line-height: 1;
        margin-top: 8px;
      }
    
      .text {
        color: #333;
        font-weight: 600;
        line-height: 1.2;
        flex: 1;
      }
    }
  }
}
</style> 