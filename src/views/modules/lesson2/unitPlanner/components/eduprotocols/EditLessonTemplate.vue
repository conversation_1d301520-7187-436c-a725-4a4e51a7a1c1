<template>
  <div>
    <el-dialog
      custom-class="edit-lesson-template-dialog"
      width="600px"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :title="$t('loc.eduprotocols33')"
      :visible.sync="dialogVisible"
      :before-close="closeEditTemplateDialog">
        <div class="lg-margin-bottom-20 title-font-16-regular">{{ $t('loc.eduprotocols34') }}</div>
        <el-form ref="form" :model="templateModel" :rules="rules" :key="lessonTemplate && lessonTemplate.id">
          <!-- Frayer Model -->
          <div v-if="lessonTemplate && lessonTemplate.templateType && lessonTemplate.templateType.toUpperCase() == 'FRAYER_MODEL'">
            <el-form-item prop="frayerList">
              <div style="max-height: 400px; padding-right: 20px; margin-right: -15px;" class="lg-scrollbar-show">
                <div v-for="(content, index) in templateModel.frayerList" :key="index">
                  <div class="display-flex align-items key-word-item lg-margin-bottom-8">
                    <div class="custom-collapse-title">
                      <!-- 标题作为输入框 -->
                      <el-input
                        v-model="content.centralTerm"
                        :placeholder="$t('loc.eduprotocols46')"
                        maxlength="50"
                        size="small"
                        class="template-title"
                      />
                      <i :class="{ 'popover-arrow-roate': content.popverVisable }" class="el-icon-arrow-up popover-arrow" @click="changeFrayerCollapse(content)"></i>
                    </div>
                    <div class="del-button">
                      <el-tooltip effect="dark" :content="$t('loc.eduprotocols39')" placement="top" :disabled="templateModel.frayerList.length > 1">
                        <i class="lg-icon lg-icon-close lg-color-danger lg-pointer"
                          :class="{'del-button-disabled': templateModel.frayerList.length === 1}"
                          @click="deleteFrayer(index)"
                        ></i>
                      </el-tooltip>
                    </div>
                  </div>
                  <el-collapse-transition>
                    <div class="bg-color-F5F6F8 lg-padding-12 lg-border-radius-4 lg-margin-bottom-8" v-show="content.popverVisable">
                      <div v-for="(question, qIndex) in 4" :key="qIndex" class="display-flex align-items m-b-sm">
                        <span class="white-space lg-margin-right-8">{{ $t('loc.eduprotocols48', {num: qIndex+1}) }}</span>
                        <el-input
                          class="un-check-input"
                          v-model="content.dimension[qIndex]"
                          :placeholder="$t('loc.eduprotocols47')"
                          maxlength="200"
                        />
                      </div>
                    </div>
                  </el-collapse-transition>
                </div>
              </div>
            </el-form-item>
            <el-button type="primary" class="add-btn" icon="lg-icon lg-icon-add font-size-20" @click="addFrayer"></el-button>
          </div>
          <!-- Sketch and Tell -->
          <div v-if="lessonTemplate && lessonTemplate.templateType && lessonTemplate.templateType.toUpperCase() == 'SKETCH_AND_TELL'">
            <div style="max-height: 400px; padding-right: 20px; margin-right: -15px;" class="lg-scrollbar">
              <el-form-item prop="keywords">
                <div v-for="(keyword, index) in templateModel.keywords" :key="index" class="display-flex align-items key-word-item">
                  <!-- 词汇 -->
                  <el-input v-model="templateModel.keywords[index]" :placeholder="$t('loc.eduprotocols49')" maxlength="50"></el-input>
                  <!-- 删除按钮 -->
                  <el-tooltip effect="dark" :content="$t('loc.eduprotocols39')" placement="top" :disabled="templateModel.keywords.length > 1">
                    <i class="del-button lg-icon lg-icon-close lg-color-danger lg-pointer lg-padding-l-r-10"
                      :class="{'del-button-disabled': templateModel.keywords.length === 1}"
                      @click="deleteKeyword(index)"
                    ></i>
                  </el-tooltip>
                </div>
              </el-form-item>
            </div>
            <el-button type="primary" class="add-btn" icon="lg-icon lg-icon-add font-size-20" @click="addKeyword"></el-button>
          </div>
          <!-- Thin Slide -->
          <div v-if="lessonTemplate && lessonTemplate.templateType && lessonTemplate.templateType.toUpperCase() == 'THIN_SLIDE'">
            <div style="max-height: 200px; padding-right: 20px; margin-right: -15px;" class="lg-scrollbar-show">
              <el-form-item prop="keyTerms">
                <div v-for="(keyword, index) in templateModel.keyTerms" :key="index" class="display-flex align-items key-word-item">
                  <!-- 词汇 -->
                  <el-input v-model="templateModel.keyTerms[index]" :placeholder="$t('loc.eduprotocols59')" maxlength="50"></el-input>
                  <!-- 删除按钮 -->
                  <el-tooltip effect="dark" :content="$t('loc.eduprotocols39')" placement="top" :disabled="templateModel.keyTerms.length > 1">
                    <i class="del-button lg-icon lg-icon-close lg-color-danger lg-pointer lg-padding-l-r-10"
                      :class="{'del-button-disabled': templateModel.keyTerms.length === 1}"
                      @click="deleteKeyTerms(index)"
                    ></i>
                  </el-tooltip>
                </div>
              </el-form-item>
            </div>
            <el-button type="primary" class="add-btn" icon="lg-icon lg-icon-add font-size-20" @click="addKeyTerm"></el-button>
          </div>
          <!-- Thin Slides Variation -->
          <div v-if="lessonTemplate && lessonTemplate.templateType && lessonTemplate.templateType.toUpperCase() == 'THIN_SLIDES_VARIATIONS'" class="thin-slides-variations-form">
            <el-form-item class="answer-item" prop="answer" :label="$t('loc.eduprotocols50')" required>
              <el-input v-model="templateModel.answer" :placeholder="$t('loc.eduprotocols51')" maxlength="200"></el-input>
            </el-form-item>
          </div>
          <!-- Sketch and Tell O --> 
          <div v-if="lessonTemplate && lessonTemplate.templateType && lessonTemplate.templateType.toUpperCase() == 'SKETCH_AND_TELL_O'" class="sketch-and-tell-o-form">
            <el-form-item class="topic-item" prop="centralTopic" :label="$t('loc.eduprotocols52')" required>
              <el-input v-model="templateModel.centralTopic" :placeholder="$t('loc.eduprotocols53')" maxlength="50"></el-input>
            </el-form-item>
            <el-form-item class="task-item" prop="task" :label="$t('loc.eduprotocols54')" required>
              <el-input type="textarea" v-model="templateModel.task" :autosize="{ minRows: 4, maxRows: 4}" maxlength="300" :placeholder="$t('loc.eduprotocols55')"></el-input>
            </el-form-item>
            <el-form-item class="circle-item" prop="circleCount" :label="$t('loc.eduprotocols56')" required>
              <el-select v-model="templateModel.circleCount">
                <el-option v-for="option in circleOptions" :key="option" :label="option" :value="option"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <!-- BookAkucha -->
          <div v-if="lessonTemplate && lessonTemplate.templateType && lessonTemplate.templateType.toUpperCase() == 'BOOKA_KUCHA'" class="booka-kucha-form">
            <el-form-item class="book-name-item" prop="bookName" :label="$t('loc.eduprotocols40')" required>
              <el-input v-model="templateModel.bookName" :placeholder="$t('loc.eduprotocols43')" maxlength="100"></el-input>
            </el-form-item>
            <el-form-item class="theme-item" prop="theme" :label="$t('loc.eduprotocols41')" required>
              <el-input type="textarea" v-model="templateModel.theme" :autosize="{ minRows: 4, maxRows: 4}" maxlength="200" :placeholder="$t('loc.eduprotocols44')"></el-input>
            </el-form-item>
            <el-form-item class="instruction-item" prop="instruction" :label="$t('loc.eduprotocols42')">
              <el-input type="textarea" v-model="templateModel.instruction" :autosize="{ minRows: 4, maxRows: 4}" maxlength="500" :placeholder="$t('loc.eduprotocols45')"></el-input>
            </el-form-item>
          </div>
          <!-- Wicked Hydra -->
          <div v-if="lessonTemplate && lessonTemplate.templateType && lessonTemplate.templateType.toUpperCase() == 'WICKED_HYDRA'" class="wicked-hydra-form">
            <el-form-item class="template-content-item" prop="templateContent" :label="$t('loc.eduprotocols57')" required>
              <el-input type="textarea" v-model="templateModel.templateContent" :autosize="{ minRows: 4, maxRows: 4}" maxlength="2000" :placeholder="$t('loc.eduprotocols58')"></el-input>
            </el-form-item>
          </div>
        </el-form>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeEditTemplateDialog">{{$t('loc.cancel')}}</el-button>
        <el-button type="primary" @click="confirmAndSaveTemplate">{{$t('loc.eduprotocols35')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'EditLessonTemplate',
  data () {
    return {
      circleOptions: [4, 5, 6, 7, 8, 9], // 圆圈数量选项
      dialogVisible: false, // 是否显示编辑模板对话框
      lessonTemplate: null, // 课程模板
      templateModel: {}, // 模板数据
      templateModelCopy: null, // 模板数据副本
      rules: {} // 表单验证规则
    }
  },
  methods: {
    // 打开编辑模板对话框
    openEditTemplateDialog (template) {
      this.lessonTemplate = template
      this.templateModel = JSON.parse(JSON.stringify(template.templateModel))
      this.templateModelCopy = JSON.stringify(template.templateModel)
      this.initRules()
      this.dialogVisible = true
    },

    // 初始化表单验证规则
    initRules () {
      // 获取模板类型
      let templateType = this.lessonTemplate && this.lessonTemplate.templateType && this.lessonTemplate.templateType.toUpperCase()
      // 如果是 FRAYER_MODEL 模板，则需要验证关键词
      if (templateType == 'FRAYER_MODEL') {
        // 初始化折叠面板数据,第一个展开
        this.templateModel.frayerList.forEach((item, index) => {
          this.$set(item, 'popverVisable', index === 0)
        })
        let checkFrayerList = (rule, value, callback) => {
          let notEmptyWords = (value && value.filter(item => item.centralTerm.trim() !== '')) || []
          if (notEmptyWords.length > 0) {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        this.rules = {
          frayerList: [
            { validator: checkFrayerList, trigger: 'blur' },
            { validator: checkFrayerList, trigger: 'change' }
          ]
        }
      }
      // 如果是 SKETCH_AND_TELL 模板，则需要验证关键词
      if (templateType == 'SKETCH_AND_TELL') {
        let checkKeywords = (rule, value, callback) => {
          let notEmptyWords = (value && value.filter(item => item.trim() !== '')) || []
          if (notEmptyWords.length > 0) {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        this.rules = {
          keywords: [
            { validator: checkKeywords, trigger: 'blur' },
            { validator: checkKeywords, trigger: 'change' }
          ]
        }
      }
      // 如果是 THIN_SLIDE 模板，则需要验证关键词
      if (templateType == 'THIN_SLIDE') {
        let checkKeywords = (rule, value, callback) => {
          let notEmptyWords = (value && value.filter(item => item.trim() !== '')) || []
          if (notEmptyWords.length > 0) {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        this.rules = {
          keyTerms: [
            { validator: checkKeywords, trigger: 'blur' },
            { validator: checkKeywords, trigger: 'change' }
          ]
        }
      }
      // 如果是 SKETCH_AND_TELL_O 模板，则需要验证 Topic、Task、Circle
      if (templateType == 'SKETCH_AND_TELL_O') {
        let checkTopic = (rule, value, callback) => {
          if (value && value.trim() !== '') {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        let checkTask = (rule, value, callback) => {
          if (value && value.trim() !== '') {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        let checkCircle = (rule, value, callback) => {
          if (value && value >= 4 && value <= 9) {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        this.rules = {
          centralTopic: [
            { validator: checkTopic, trigger: 'change' },
            { validator: checkTopic, trigger: 'blur' }
          ],
          task: [
            { validator: checkTask, trigger: 'change' },
            { validator: checkTask, trigger: 'blur' }
          ],
          circleCount: [
            { validator: checkCircle, trigger: 'change' },
            { validator: checkCircle, trigger: 'blur' }
          ]
        }
      }
      // 如果是 BOOKA_KUCHA 模板，则需要验证书名、主题
      if (templateType == 'BOOKA_KUCHA') {
        let checkBookName = (rule, value, callback) => {
          if (value && value.trim() !== '') {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        let checkTheme = (rule, value, callback) => {
          if (value && value.trim() !== '') {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        this.rules = {
          bookName: [
            { validator: checkBookName, trigger: 'change' },
            { validator: checkBookName, trigger: 'blur' }
          ],
          theme: [
            { validator: checkTheme, trigger: 'change' },
            { validator: checkTheme, trigger: 'blur' }
          ]
        }
      }
      // 如果是 THIN_SLIDES_VARIATIONS 模板，则需要验证答案
      if (templateType == 'THIN_SLIDES_VARIATIONS') {
        let checkAnswer = (rule, value, callback) => {
          if (value && value.trim() !== '') {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        this.rules = {
          answer: [
            { validator: checkAnswer, trigger: 'change' },
            { validator: checkAnswer, trigger: 'blur' }
          ]
        }
      }
      // 如果是 WICKED_HYDRA 模板，则需要验证模板内容
      if (templateType == 'WICKED_HYDRA') {
        let checkTemplateContent = (rule, value, callback) => {
          if (value && value.trim() !== '') {
            callback()
          } else {
            callback(new Error(this.$t('loc.fieldReq')))
          }
        }
        this.rules = {
          templateContent: [
            { validator: checkTemplateContent, trigger: 'change' },
            { validator: checkTemplateContent, trigger: 'blur' }
          ]
        }
      }
    },

    // 删除关键词
    deleteKeyword (index) {
      if (this.templateModel.keywords.length === 1) {
        return
      }
      this.templateModel.keywords.splice(index, 1)
    },

    // 添加关键词
    addKeyword () {
      this.templateModel.keywords.push('')
    },

    // 删除关键词
    deleteKeyTerms (index) {
      if (this.templateModel.keyTerms.length === 1) {
        return
      }
      this.templateModel.keyTerms.splice(index, 1)
    },

    // 添加关键词
    addKeyTerm () {
      this.templateModel.keyTerms.push('')
    },

    // 删除 Frayer model 关键词
    deleteFrayer (index) {
      // 如果只有一个则不删除
      if (this.templateModel.frayerList.length === 1) {
        return
      }
      this.templateModel.frayerList.splice(index, 1)
    },

    // 添加 Frayer model 关键词
    addFrayer () {
      this.templateModel.frayerList.push({
        centralTerm: '',
        dimension: ['', '', '', ''],
        popverVisable: true
      })
    },

    // 修改折叠
    changeFrayerCollapse (content) {
      content.popverVisable = !content.popverVisable
    },

    // 关闭编辑模板对话框
    closeEditTemplateDialog () {
      this.dialogVisible = false
    },

    // 保存模板
    confirmAndSaveTemplate () {
      this.$refs.form && this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.lessonTemplate.templateType === 'FRAYER_MODEL') {
            this.templateModel.frayerList = this.templateModel.frayerList.map(item => {
              return {
                centralTerm: item.centralTerm,
                dimension: item.dimension
              }
            })
          }
          // 如果模板数据没有变化，则不保存
          if (this.templateModelCopy === JSON.stringify(this.templateModel)) {
            this.dialogVisible = false
            return
          }
          // 处理模板数据
          let templateModel = this.templateModel
          switch (this.lessonTemplate.templateType.toUpperCase()) {
              case 'FRAYER_MODEL':
                  templateModel = {
                    frayerList: this.templateModel.frayerList.filter(item => item.centralTerm.trim() !== '')
                  }
                  break
              case 'SKETCH_AND_TELL':
                  templateModel = {
                    keywords: this.templateModel.keywords.filter(item => item.trim() !== '')
                  }
                  break
              case 'SKETCH_AND_TELL_O':
                  break
              case 'BOOKA_KUCHA':
                  break
              case 'THIN_SLIDE':
                  templateModel = {
                    keyTerms: this.templateModel.keyTerms.filter(item => item.trim() !== '')
                  }
                  break
              case 'THIN_SLIDES_VARIATIONS':
                  break
              case 'WICKED_HYDRA':
                  break
              case 'THICK_SLIDE':
                break
              case 'CYBER_SANDWICH':
                break
          }
          this.lessonTemplate.templateModel = templateModel
          let updateParams = {
            taskId: this.lessonTemplate.id,
            type: this.lessonTemplate.templateType,
            lessonTemplates: {
              ...templateModel,
              type: this.lessonTemplate.templateType
            }
          }
          this.$emit('updateLessonTemplate', updateParams, true)
          this.dialogVisible = false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>

.del-button-disabled {
  color: var(--color-text-disabled);
  cursor: not-allowed;
}

.edit-lesson-template-dialog {
  /deep/ & > .el-dialog__header .el-dialog__title {
    font-size: 20px !important;
    color: var(--color-text-primary);
  }

  /deep/ & > .el-dialog__body {
    padding: 14px 20px;
  }
}

.add-btn {
  border-radius: 20px;
  padding: 0px 8px;
}

.del-button {
  display: none;
}

.key-word-item {
  margin-bottom: 10px;
}

.key-word-item:hover {
  .del-button {
    display: block;
    padding-left: 4px;
  }
}

.is-error .custom-collapse-title {
  border: 1px solid var(--color-danger) !important;
}

.custom-collapse-title:focus-within {
  border-color: var(--color-primary);
}

.custom-collapse-title {
  width: 100%;
  display: flex;
  align-items: center;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  /deep/ .popover-arrow {
    cursor: pointer;
    padding: 12px;
    transition: transform .3s;
    transform: rotate(180deg);
  }

  /deep/ .popover-arrow-roate {
    transform: rotate(0deg);
  }
  /deep/ .el-input__inner {
    border: none;
  }
}

/deep/ .el-form-item .un-check-input .el-input__inner {
  border-color: var(--color-border) !important;
}

.thin-slides-variations-form, .wicked-hydra-form {
  /deep/ .el-form-item {
    display: flex!important;
    align-items: center!important;
  }

  /deep/ .el-form-item__content {
    width: 100% !important;
  }

  /deep/ .el-form-item__error {
    padding-left: 16px;
    padding-top: 4px;
  }

  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    border: 1px solid transparent;;
    border-radius: 4px;
    padding: 8px;
  }

  /deep/ .el-input__inner:focus,
  /deep/ .el-input__inner:hover,
  /deep/ .el-textarea__inner:focus,
  /deep/ .el-textarea__inner:hover {
    border: 1px dashed var(--color-info);
    border-radius: 4px;
    padding: 8px;
  }

  /deep/ .el-textarea__inner {
    resize: none;
  }

  .answer-item, .template-content-item {
    border: 1px solid var(--color-border) !important;
    border-radius: 8px!important;
    /deep/ .el-form-item__label {
      font-weight: 600;
      margin-bottom: 0px!important;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      border-right: 1px solid var(--color-border)!important;
      background: var(--color-table-background) !important;
      width: 150px!important;
      padding: 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    /deep/ .el-form-item__content {
      padding: 0 16px!important;
    }
  }

  .answer-item {
    /deep/ .el-form-item__label {
      height: 80px!important;
    }
  }

  .template-content-item {
    /deep/ .el-form-item__label {
      height: 150px!important;
    }
  }
}

.sketch-and-tell-o-form {
  /deep/ .el-form-item {
    display: flex!important;
    align-items: center!important;
  }

  /deep/ .el-form-item__content {
    width: 100% !important;
  }

  /deep/ .el-form-item__error {
    padding-left: 16px;
    padding-top: 4px;
  }

  /deep/ .el-textarea__inner {
    resize: none;
  }

  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    border: 1px solid transparent;;
    border-radius: 4px;
    padding: 8px;
  }

  /deep/ .el-input__inner:focus,
  /deep/ .el-input__inner:hover,
  /deep/ .el-textarea__inner:focus,
  /deep/ .el-textarea__inner:hover {
    border: 1px dashed var(--color-info);
    border-radius: 4px;
    padding: 8px;
  }

  .topic-item {
    border: 1px solid var(--color-border) !important;
    border-top-left-radius: 8px!important;
    border-top-right-radius: 8px!important;
    margin-bottom: 0px!important;
    /deep/ .el-form-item__label {
      height: 80px!important;
      border-top-left-radius: 8px;
      border-right: 1px solid var(--color-border)!important;
      background: var(--color-table-background) !important;
    }
  }

  .task-item {
    border-left: 1px solid var(--color-border) !important;
    border-right: 1px solid var(--color-border) !important;
    margin-bottom: 0px!important;
    /deep/ .el-form-item__label {
      height: 150px!important;
      border-right: 1px solid var(--color-border)!important;
      background: var(--color-table-background) !important;
    }
  }

  .circle-item {
    border: 1px solid var(--color-border) !important;
    border-bottom-left-radius: 8px!important;
    border-bottom-right-radius: 8px!important;
    margin-bottom: 0px!important;
    /deep/ .el-form-item__label {
      height: 80px!important;
      border-bottom-left-radius: 8px;
      border-right: 1px solid var(--color-border)!important;
      background: var(--color-table-background) !important;
    }
  }

  .topic-item,
  .task-item,
  .circle-item {
    /deep/ .el-form-item__label {
      font-weight: 600;
      background: var(--color-table-background) !important;
      width: 150px !important;
      margin-bottom: 0px!important;
      padding: 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    /deep/ .el-form-item__content {
      padding: 0 16px!important;
    }
  }
}

.booka-kucha-form {
  /deep/ .el-form-item {
    display: flex!important;
    align-items: center!important;
  }

  /deep/ .el-form-item__content {
    width: 100% !important;
  }

  /deep/ .el-form-item__error {
    padding-left: 16px;
    padding-top: 4px;
  }

  /deep/ .el-textarea__inner {
    resize: none;
  }

  /deep/ .el-textarea__inner,
  /deep/ .el-input__inner {
    border: 1px solid transparent;;
    border-radius: 4px;
    padding: 8px;
  }

  /deep/ .el-input__inner:focus,
  /deep/ .el-input__inner:hover,
  /deep/ .el-textarea__inner:focus,
  /deep/ .el-textarea__inner:hover {
    border: 1px dashed var(--color-info);
    border-radius: 4px;
    padding: 8px;
  }

  .book-name-item {
    border: 1px solid var(--color-border) !important;
    border-top-left-radius: 8px!important;
    border-top-right-radius: 8px!important;
    margin-bottom: 0px!important;
    /deep/ .el-form-item__label {
      height: 80px!important;
      border-top-left-radius: 8px;
      border-right: 1px solid var(--color-border)!important;
      background: var(--color-table-background) !important;
    }
  }

  .theme-item {
    border-left: 1px solid var(--color-border) !important;
    border-right: 1px solid var(--color-border) !important;
    margin-bottom: 0px!important;
    /deep/ .el-form-item__label {
      height: 150px!important;
      border-right: 1px solid var(--color-border)!important;
      background: var(--color-table-background) !important;
    }
  }

  .instruction-item {
    border: 1px solid var(--color-border) !important;
    border-bottom-left-radius: 8px!important;
    border-bottom-right-radius: 8px!important;
    margin-bottom: 0px!important;
    /deep/ .el-form-item__label {
      height: 150px!important;
      border-bottom-left-radius: 8px;
      border-right: 1px solid var(--color-border)!important;
      background: var(--color-table-background) !important;
    }
  }

  .book-name-item,
  .theme-item,
  .instruction-item {
    /deep/ .el-form-item__label {
      font-weight: 600;
      background: var(--color-table-background) !important;
      width: 121px !important;
      margin-bottom: 0px!important;
      padding: 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    /deep/ .el-form-item__content {
      padding: 0 16px!important;
    }
  }

}

</style>
