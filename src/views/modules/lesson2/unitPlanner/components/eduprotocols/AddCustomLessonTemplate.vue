<template>
    <!-- 添加自定义课程模板按钮 -->
    <div class="lg-margin-top-16">
        <el-popover
            placement="top"
            width="390"
            :appendToBody="false"
            v-model="showEduProtocolsTemplateCustomGuide"
            ref="settingGuide"
            popper-class="add-lesson-template-popover"
            trigger="manual">
            <div class="text-white">
                <!-- 引导文字 -->
                <div class="lg-margin-bottom-24 word-break text-left">
                    <!-- 用户引导内容 -->
                    <span class="title-font-14">{{$t('loc.eduprotocols37')}}</span>
                    <img src="~@/assets/img/lesson2/unitPlanner/add_custom_lesson_template_guide.png" class="lg-margin-top-12">
                </div>
                <div class="display-flex flex-justify-end gap-6 align-items">
                    <el-button type="text" @click="hideGuide(false)">{{ $t('loc.unitPlannerRedesignGuide2') }}</el-button>
                    <el-button type="primary" @click="hideGuide(true)">{{ $t('loc.eduprotocols38') }}</el-button>
                </div>
            </div>
            <el-button slot="reference" ref="guideButton" plain class="w-full add-template-button" icon="el-icon-plus" :disabled="disabled" @click="openAddTemplateDialog">{{ $t('loc.eduprotocols28')}}</el-button>
        </el-popover>
        <!-- 添加自定义模板弹窗 -->
        <el-dialog
            custom-class="add-custom-lesson-template-dialog"
            :append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :title="$t('loc.eduprotocols24')"
            :visible.sync="addTemplateVisible"
            :before-close="closeAddTemplateDialog"
            width="600px">
            <el-form ref="addTemplateForm" :rules="rules" :model="addCustomTemplateModel">
                <div class="title-font-16-regular">
                    {{ $t('loc.eduprotocols25') }}
                </div>
                <div class="lg-margin-t-b-20 display-flex align-items">
                    <span class="lg-margin-right-8 title-font-16">{{ $t('loc.eduprotocols26') }}</span>
                    <el-dropdown
                        trigger="hover"
                        @visible-change="popverVisibleChange"
                        @command="handleChangeModel">
                        <el-button
                            class="lesson-template-change-button"
                            plain>
                            <span class="display-flex align-items">
                                <el-image style="width: 20px;height: 20px" :src="require('@/assets/img/lesson2/unitPlanner/lesson_template.svg')" />
                                <span class="lg-margin-left-8 selected-model-name ">{{ selectedModel && selectedModel.name || 'Please Select' }}</span>
                                <i :class="{ 'popover-arrow-roate': popverVisible }" class="el-icon-arrow-up popover-arrow"></i>
                            </span>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                                v-for="(item, index) in models.filter(x => !x.disabled)"
                                :key="index"
                                :command="item"
                                :class="{ 'model-selected': selectedModel && selectedModel.type === item.type }"
                                :disabled="item.notSupport">
                                {{ item.name }}
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <!-- 改编想法 -->
                <el-form-item required prop="adaptIdea">
                    <span class="title-font-16" slot="label">{{ $t('loc.eduprotocols27') }}</span>
                    <el-input type="textarea" v-model="addCustomTemplateModel.adaptIdea" :rows="5" :placeholder="addTemplatePlaceholder" :maxlength="2000" show-word-limit class="adapt-idea-input"></el-input>
                </el-form-item>
            </el-form>
            <!-- 操作 -->
            <span slot="footer" class="dialog-footer">
                <el-button :disabled="checking" @click="closeAddTemplateDialog">{{$t('loc.cancel')}}</el-button>
                <el-button :loading="checking" type="primary" @click="confirmAddTemplate()">{{$t('loc.unitPlannerStep3ConfirmationConfirmGenerate')}}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import tools from '@/utils/tools'
import LessonApi from '@/api/lessons2'
import { mapState } from 'vuex'
import { equalsIgnoreCase } from '@/utils/common'
import { LessonTemplates } from '@/utils/constants'
import { EDU_PROTOCOLS_NEW_USER_UTC } from '@/utils/const'

export default {
    name: 'AddCustomLessonTemplate',
    props: {
        // 课程 ID
        lessonId: {
            type: String,
            required: true
        },
        // 当前课程对应的年龄组信息
        ageGroup: {
            type: String,
            required: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        // 保存课程方法
        saveLesson: {
            type: Function
        }
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser, // 当前用户
            guideFeatures: (state) => state.common.guideFeatures // 功能引导
        }),
        // 是否显示添加自定义模板按钮
        addTemplatePlaceholder () {
            if (this.selectedModel && this.selectedModel.type) {
                switch (this.selectedModel.type.toUpperCase()) {
                    case 'FRAYER_MODEL':
                        return this.$t('loc.frayerModelCustomPlaceholder')
                    case 'SKETCH_AND_TELL':
                        return this.$t('loc.sketchAndTellCustomPlaceholder')
                    case 'SKETCH_AND_TELL_O':
                        return this.$t('loc.sketchAndTellOCustomPlaceholder')
                    case 'BOOKA_KUCHA':
                        return this.$t('loc.bookaKuchaCustomPlaceholder')
                    case 'THIN_SLIDE':
                        return this.$t('loc.thinSlideCustomPlaceholder')
                    case 'THIN_SLIDES_VARIATIONS':
                        return this.$t('loc.thinSlideV2CustomPlaceholder')
                    case 'THICK_SLIDE':
                        return this.$t('loc.thickSlideCustomPlaceholder')
                    case 'CYBER_SANDWICH':
                        return this.$t('loc.cyberSandwichCustomPlaceholder')
                    case 'WICKED_HYDRA':
                        return this.$t('loc.wickedHydraCustomPlaceholder')
                }
            }
            return ''
        },
        // 模板是否支持使用
        modelNotSupport () {
            return function (type) {
                // 非 thin slide 模板不限制，thin slide 模板限制年龄段为 Grade 3-12
                if (type && type.toUpperCase() != 'THIN_SLIDE') {
                    return false
                }
                let grade3To12 = ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
                return !tools.arrayContainsIgnoreCase(grade3To12, this.ageGroup)
            }
        },
        /**
         * 是否为新用户
         * @returns {boolean}
         */
        isEduProtocolsNewUser () {
            return this.currentUser && this.currentUser.userInfo && tools.timeIsAfter(this.currentUser.userInfo.createdAtUtc, EDU_PROTOCOLS_NEW_USER_UTC)
        },
    },
    data () {
        // 校验改编 idea
        let checkAdaptIdea = (rule, value, callback) => {
            if (!value || !value.trim()) {
                callback(new Error(this.$t('loc.fieldReq')))
            } else {
                callback()
            }
        }
        return {
            rules: {
                adaptIdea: [
                    { validator: checkAdaptIdea, trigger: 'blur' },
                    { validator: checkAdaptIdea, trigger: 'change' }
                ]
            },
            models: LessonTemplates,
            selectedModel: null,
            addTemplateVisible: false,
            // 是否显示添加模板表单项
            adaptIdea: '', // 改编想法
            addCustomTemplateModel: {
                selectedModel: '',
                adaptIdea: ''
            },
            popverVisible: false,
            checking: false, // 校验中
            showEduProtocolsTemplateCustomGuide: false, // 是否显示自定义模板引导
            updatePopperInterval: null
        }
    },
    created () {
        this.$bus.$on('showEduProtocolsTemplateCustomGuide', this.showGuide)
    },
    destroyed () {
        this.$bus.$off('showEduProtocolsTemplateCustomGuide', this.showGuide)
    },
    methods: {
        // 显示引导
        showGuide (lessonId) {
            // 如果课程 ID 不匹配或者当前用户是 eduprotocols 新用户，不显示引导
            if (!equalsIgnoreCase(lessonId, this.lessonId) || this.isEduProtocolsNewUser) {
                return
            }
            this.$nextTick(() => {
                if (this.guideFeatures.showEduProtocolsTemplateCustomGuide) {
                    this.$refs.guideButton && this.$refs.guideButton.$el.scrollIntoView({ behavior: 'smooth', block: 'center' })
                    setTimeout(() => {
                        this.showEduProtocolsTemplateCustomGuide = true
                    }, 500)
                    var that = this
                    // 更新 popper 位置
                    this.updatePopperInterval = setInterval(() => {
                        that.$refs.settingGuide && that.$refs.settingGuide.updatePopper()
                        if (!that.guideFeatures.showEduProtocolsTemplateCustomGuide) {
                            clearInterval(that.updatePopperInterval)
                        }
                    }, 100)
                    // 设置 vuex 中的引导状态
                    this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
                        ...this.guideFeatures,
                        showEduProtocolsTemplateCustomGuide: false
                    })
                    // 调接口隐藏引导
                    let params = { features: ['EDUPROTOCOLS_TEMPLATE_CUSTOM_GUIDE'] }
                    this.$axios.post($api.urls().hideGuide, params)
                }
            })
        },
        // 关闭引导
        closeGuide () {
            this.showEduProtocolsTemplateCustomGuide = false
            // 清除定时器
            if (this.updatePopperInterval) {
                clearInterval(this.updatePopperInterval)
            }
        },
        // 隐藏引导
        hideGuide (openAddTemplate) {
            // 关闭引导弹窗
            this.closeGuide()
            // 打开添加模板弹窗
            if (openAddTemplate) {
                this.openAddTemplateDialog()
            }
        },
        // 打开添加模板弹窗
        openAddTemplateDialog () {
            if (this.showEduProtocolsTemplateCustomGuide) {
                this.hideGuide()
            }
            // 设置模板是否可用
            this.models.forEach(m => {
                m.notSupport = this.modelNotSupport(m.type)
            })
            // 按照是否可用排序
            this.models = this.models.sort((a, b) => a.notSupport - b.notSupport)
            // 找到上次选择的模板, 如果没有找到，默认选择第一个模板
            let cacheKey = 'LAST_SELECTED_LESSON_TEMPLATE_' + this.currentUser.user_id
            const lastSelectedModel = localStorage.getItem(cacheKey) || this.models[0].type
            // 如果上次选择的模板存在，且不是不支持的模板，且不是禁用的模板
            if (lastSelectedModel) {
                this.selectedModel = this.models.find(model => model.type === lastSelectedModel && !model.notSupport && !model.disabled)
                this.addCustomTemplateModel.selectedModel = this.selectedModel && this.selectedModel.type
            }
            this.$refs.addTemplateForm && this.$refs.addTemplateForm.resetFields()
            this.addTemplateVisible = true
        },
        // 关闭添加模板弹窗
        closeAddTemplateDialog () {
            if (this.checking) return
            this.addTemplateVisible = false
        },

        // 生成自定义模板
        generateCustomTemplate () {
            this.addTemplateVisible = false
            // 保存选择的模板到本地存储
            let cacheKey = 'LAST_SELECTED_LESSON_TEMPLATE_' + this.currentUser.user_id
            localStorage.setItem(cacheKey, this.selectedModel && this.selectedModel.type)
            let generateCustomTemplateParams = {
                lessonId: this.lessonId,
                lessonTemplateType: this.selectedModel.type,
                adaptIdea: this.addCustomTemplateModel.adaptIdea
            }
            this.$emit('generateCustomTemplate', generateCustomTemplateParams)
        },

        // 确认添加模板
        confirmAddTemplate () {
            // 校验表单
            this.$refs.addTemplateForm && this.$refs.addTemplateForm.validate(async (valid) => {
                // 校验通过后根据模板类型生成自定义模板
                if (valid) {
                    // 如果是 BOOKA_KUCHA 模板，需要检查课程是否包含书籍
                    if (this.addCustomTemplateModel.selectedModel && this.addCustomTemplateModel.selectedModel.toUpperCase() == 'BOOKA_KUCHA') {
                        let params = {
                            id: this.lessonId,
                            adaptIdea: this.addCustomTemplateModel.adaptIdea
                        }
                        this.checking = true
                        // 保存课程
                        if (this.saveLesson && typeof this.saveLesson === 'function') {
                            await this.saveLesson()
                        }
                        // 检查课程是否包含书籍
                        let result = await LessonApi.checkLessonContainBooks(params)
                        this.checking = false
                        // 如果课程包含书籍，生成自定义模板
                        if (result.success) {
                            this.generateCustomTemplate()
                        } else {
                            // 如果课程不包含书籍，提示用户添加书籍
                            this.$message.error(this.$t('loc.eduprotocols61'))
                        }
                    } else {
                        // 非 BOOKA_KUCHA 模板，直接生成自定义模板
                        this.generateCustomTemplate()
                    }
                }
            })
        },
        // 下拉框显示隐藏
        popverVisibleChange (value) {
            this.popverVisible = value
        },
        // 切换模板
        handleChangeModel (model) {
            this.selectedModel = model
            this.addCustomTemplateModel.selectedModel = model.type
        }
    }
}
</script>

<style lang="less" scoped>

/deep/ .adapt-idea-input .el-textarea__inner {
   line-height: 24px !important;
}

/deep/ .add-template-button {
    color: var(--color-primary) !important;
    border: 1px dashed var(--color-border) !important;
    box-shadow: none !important;
    span, i {
        font-weight: 600;
    }
}

/deep/ .add-template-button:hover,
/deep/ .el-button--default.is-plain:hover {
    background: var(--color-primary-light) !important;
    border-color: var(--color-primary) !important;
}

/deep/ .add-template-button.el-button.is-plain.is-disabled {
    color: var(--color-primary) !important;
    border: 1px dashed var(--color-border) !important;
}

.add-custom-lesson-template-dialog {
    /deep/ & .el-dialog__header .el-dialog__title {
        font-size: 20px !important;
    }

    /deep/ & > .el-dialog__body {
        padding: 14px 20px;
    }

    /deep/ & .el-form-item {
        display: block !important;
    }

    /deep/ & .el-input__count {
        line-height: 20px !important;
    }
}

/deep/ .el-button.lesson-template-change-button {
    padding-left: 10px !important;
    padding-right: 10px !important;
    border-width: 1px;
    border-color: #c0c4cc !important;
}

/deep/ .el-button.lesson-template-change-button:hover,
/deep/ .el-button.lesson-template-change-button:focus {
    box-shadow: none !important;
    background: transparent !important;
}

/deep/ .popover-arrow {
    transition: transform .3s;
    transform: rotate(180deg);
}

/deep/ .popover-arrow-roate {
    transform: rotate(0deg);
}

.selected-model-name {
    width: 150px;
    text-align: left;
    overflow: hidden;
}

.model-selected {
    color: var(--color-primary);
}
</style>
<style lang="less">
.el-popper.add-lesson-template-popover {
  background: var(--color-ai-assistant);
  color: var(--color-white);
  padding: 24px;
  border: none;

  &.el-popper[x-placement^=left] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=bottom] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }

  img {
    width: 100%;
  }

  .el-button {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
    color: var(--color-ai-assistant);
  }

  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
  }

  .el-button--text, .el-button--text:hover {
    color: #FFFFFF;
    background: transparent;
    border: none;
  }
}
</style>
