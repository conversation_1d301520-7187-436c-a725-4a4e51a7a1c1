<template>
    <el-dialog
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
        :before-close="handleClose"
        :title="$t('loc.eduprotocols9')"
        custom-class="lesson-template-recomand-dialog">
        <div>
            <div class="font-size-16">{{ $t('loc.eduprotocols1') }}</div>
            <div class="font-size-16 lg-color-warning-dark add-margin-t-4">{{ $t('loc.eduprotocols2') }}</div>
            <img :src="require('@/assets/img/lesson2/unitPlanner/lesson_template_recomand.png')" class="lg-margin-top-20" style="width: 100%; height: auto;" />
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleCancel" :disabled="loading">{{ $t('loc.eduprotocols3') }}</el-button>
            <el-button type="primary" @click="handleConfirm" class="lg-margin-top-12" :loading="loading">{{ $t('loc.eduprotocols4') }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'LessonTemplateRecomand',
    data () {
        return {
            dialogVisible: false, // 是否显示弹窗
            callback: null, // 回调函数
            itemId: null, // 活动 ID
            planId: null, // 计划 ID
            loading: false // 是否正在加载
        }
    },
    methods: {
        // 打开弹窗
        openDialog (callback, itemId, planId) {
            this.callback = callback
            this.itemId = itemId
            this.planId = planId
            this.dialogVisible = true
            this.$analytics.sendEvent('cg_unit_create_three_eduprotocols_show')
        },
        // 关闭弹窗
        closeDialog () {
            this.loading = false
            this.dialogVisible = false
        },
        // 使用课程模板
        handleConfirm () {
            this.$analytics.sendEvent('cg_unit_create_three_eduprotocols_yes')
            this.loading = true
            // 推荐课程模板
            this.$emit('recommendLessonTemplate', this.callback, this.itemId, this.planId)
        },
        // 不使用课程模板
        handleCancel () {
            this.$analytics.sendEvent('cg_unit_create_three_eduprotocols_no')
            this.loading = true
            this.$emit('cancelRecommendLessonTemplate', this.callback)
        },
        // 关闭弹窗
        handleClose () {
            if (this.loading) return
            this.$analytics.sendEvent('cg_unit_create_three_eduprotocols_close')
            this.loading = true
            this.$emit('cancelRecommendLessonTemplate', this.callback)
        }
    }
}
</script>

<style scoped lang="less">
/deep/ .lesson-template-recomand-dialog {
    width: 700px;
    & .el-dialog__header {
        padding: 24px;
    }
    & .el-dialog__title {
        font-size: 20px;
    }
    & .el-dialog__body {
        padding: 0 24px;
    }
    & .el-dialog__footer {
        padding-top: 24px;
    }
}
.dialog-footer {
    text-align: right;
}
</style>
