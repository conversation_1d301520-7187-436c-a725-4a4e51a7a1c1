<template>
    <div>
        <div v-if="lessonTemplates.length > 0">
            <!-- 课程模板介绍 -->
            <div class="bg-color-F5F6F8 lg-border-radius-8 lg-padding-12 lg-margin-bottom-16" v-for="item in lessonTemplates" :key="item.id">
                <div class="display-flex align-items gap-12 justify-content-between hover-show">
                    <div class="display-flex align-items">
                        <!-- 封面 -->
                        <img :src="item.img" style="border-radius: 8px;"/>
                        <!-- 名称、描述 -->
                        <div>
                            <div class="display-flex align-items">
                                <span class="title-font-16">{{ item.name }}</span>
                                <el-tooltip popper-class="max-width-600" effect="dark" :content="item.modelDescription"
                                            placement="top">
                                    <i class="lg-icon lg-icon-question title-font-14-regular lg-margin-left-4"></i>
                                </el-tooltip>
                                <span class="current-template-tag white-space line-height-14" v-if="!item.custom">
                                    Current
                                </span>
                                <span class="newPoint white-space line-height-14" v-if="item.templateType === 'WICKED_HYDRA' || item.type === 'WICKED_HYDRA'">
                                    {{ $t('loc.new') }}
                                </span>
                            </div>
                            <div class="title-font-14-regular">{{ item.description }}</div>
                        </div>
                    </div>
                    <div class="display-flex align-items-end">
                        <!-- 查看 google slide 按钮 -->
                        <el-button type="primary" :disabled="generating && !item.loading || lessonLoading" :loading="item.loading" @click="openDialog(item)">
                            {{ $t('loc.eduprotocols11') }}
                        </el-button>
                        <!-- 编辑内容按钮 -->
                        <el-button v-if="edit" icon="lg-icon lg-icon-edit font-size-20" :disabled="generating || lessonLoading" @click="editLessonTemplate(item)" class="el-button-icon"></el-button>
                    </div>
                    <div v-if="edit" class="del-button">
                        <i class="lg-icon lg-icon-close lg-color-danger lg-pointer" :class="{'opacity-40 cursor-disabled': generating || lessonLoading}" @click="deleteTemplate(item)"></i>
                    </div>
                </div>
            </div>
        </div>
        <!-- 添加自定义模板 -->
        <add-custom-lesson-template
            v-if="edit && eduProtocolsTemplateApplyOpen"
            ref="addCustomLessonTemplate"
            :disabled="generating || lessonLoading"
            :lessonId="lessonId"
            :ageGroup="ageGroup"
            @generateCustomTemplate="generateCustomTemplate">
        </add-custom-lesson-template>
        <edit-lesson-template
            ref="editLessonTemplate"
            @updateLessonTemplate="updateLessonGoogleSlideMetadata">
        </edit-lesson-template>
        <!-- google slide 预览弹窗 -->
        <el-dialog
            :visible.sync="dialogVisible"
            :fullscreen="true"
            :close-on-click-modal="false"
            :append-to-body="true"
            @closed="loading = true; userExported = false"
            title="Student-ready Activities"
            custom-class="lesson-template-preview-dialog">
            <div class="display-flex bg-white lg-padding-24 lg-border-radius-8 lg-box-shadow h-full" v-if="lessonTemplateModel">
                <!-- iframe embed -->
                <div v-loading="loading && !!link" class="h-full" style="width: calc(100% - 400px);">
                    <iframe :src="embedLink" frameborder="0" style="width: 100%; height:100%;" @load="loaded"
                            class="lg-border-radius-8"></iframe>
                </div>
                <!-- 左侧信息及操作按钮 -->
                <div class="lg-margin-left-24 w-full" style="width: 400px;">
                    <!-- 模板信息 -->
                    <div class="title-font-20">{{ lessonTemplateModel.previewTitle }}</div>
                    <div class="title-font-16-regular lg-margin-top-16">{{
                            lessonTemplateModel.previewDescription
                        }}
                    </div>
                    <!-- 操作按钮 -->
                    <div class="lg-margin-top-16">
                        <!-- 编辑模板 -->
                        <el-button v-if="edit" plain type="primary" :loading="generating" class="w-full display-flex align-items justify-content el-button-primary-dark" @click="editLessonTemplate(lessonTemplateModel)">
                            <span class="display-flex align-items justify-content">
                                <i class="lg-icon lg-icon-edit font-size-24"></i>
                                <span class="lg-margin-left-8">{{ $t('loc.eduprotocols32') }}</span>
                            </span>
                        </el-button>
                    </div>
                    <div class="lg-margin-top-16">
                        <!-- 导出到 google slides -->
                        <el-button class="w-full display-flex align-items justify-content el-button-warning-dark"
                                   :loading="userExported"
                                   :disabled="generating"
                                   @click="saveToGoogleSlides">
                            <span class="display-flex align-items justify-content">
                                <img :src="require('@/assets/img/lesson2/unitPlanner/google_slide.svg')" height="24">
                                <span class="lg-margin-left-8">{{ $t('loc.eduprotocols12') }}</span>
                            </span>
                        </el-button>
                    </div>
                    <div class="lg-margin-top-16">
                        <!-- 分享到 Classroom -->
                        <a :href="shareToClassRoomLink" target="_blank">
                            <el-button class="w-full" plain :disabled="generating">
                                <span class="display-flex align-items justify-content">
                                    <img :src="require('@/assets/img/lesson2/unitPlanner/share_to_classroom.svg')"
                                         height="24">
                                    <span class="lg-margin-left-8 title-font-14">{{ $t('loc.eduprotocols13') }}</span>
                                </span>
                            </el-button>
                        </a>
                    </div>
                    <div class="lg-margin-top-16">
                        <!-- 下载 PPT -->
                        <a :href="downloadPPTLink" target="_blank">
                            <el-button class="w-full" plain :disabled="generating">
                                <span class="display-flex align-items justify-content">
                                    <img :src="require('@/assets/img/file/ppt.png')" height="24">
                                    <span class="lg-margin-left-8 title-font-14">{{ $t('loc.eduprotocols14') }}</span>
                                </span>
                            </el-button>
                        </a>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import credentials from '@/components/googleAuth/credentials.json'
import { mapState } from 'vuex'
import AddCustomLessonTemplate from './AddCustomLessonTemplate.vue'
import EditLessonTemplate from './EditLessonTemplate.vue'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import { equalsIgnoreCase } from '@/utils/common'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import { LessonTemplates } from '@/utils/constants'

export default {
    name: 'LessonTemplatePreview',
    components: {
        AddCustomLessonTemplate,
        EditLessonTemplate
    },
    props: {
        // 课程模板信息
        lessonTemplate: {
            type: Object,
            required: false
        },
        // 课程 ID
        lessonId: {
             type: String,
             required: true
        },
        // 课程名称
        lessonName: {
            type: String,
            required: true
        },
        // 当前课程对应的年龄组信息
        ageGroup: {
            type: String,
            required: true
        },
        // 是否可以编辑
        edit: {
            type: Boolean,
            default: false
        },
        // 保存课程方法
        saveLesson: {
            type: Function
        },
        // 课程生成加载中
        lessonLoading: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            models: LessonTemplates,
            generating: false, // google slide 生成中
            loading: true, // google slide 加载中
            dialogVisible: false, // 预览弹窗是否显示
            userExported: false, // 用户是否点击导出
            // Client ID 即 OAuth 2.0 客户端 ID
            CLIENT_ID: credentials.web.client_id, // clientId
            scope: credentials.web.scope, // 作用域，这里面 documents 就是 docs
            googleDriveTokenClient: null, // google drive token客户端
            googleAuthCode: null, // google drive通行token
            pickerInited: false, // 选择器是否已经初始化
            gisInited: false, // gis是否已经初始化
            needAuth: false, // 是否需要授权
            lessonTemplates: [], // 课程模板列表
            lessonTemplateModel: null, // 当前课程模板
            generateCustomTemplateFailed: false, // 生成自定义模板失败
            googleSlideData: null, // google slide 数据
            newLessonId: null // 新的课程 ID
        }
    },
    watch: {
        lessonTemplate: {
            handler (val) {
                // 正在生成时不重新加载模板
                if (this.generating) {
                    return
                }
                this.loadTemplates()
                // 设置是否有模板资源
                this.getTemplatesDownloadUrls()
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        // 初始化 gapi
        this.gapiLoaded()
        // 初始化 gis
        this.gisLoaded()
        // todo 发送接口进行内容请求
        this.needAuth = true
    },
    mounted () {
    },
    computed: {
        ...mapState({
            currentUser: (state) => state.user.currentUser, // 当前用户
            eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
        }),
        // google slide 链接
        link() {
            if (this.lessonTemplateModel && this.lessonTemplateModel.templateUrl) {
                return this.lessonTemplateModel.templateUrl
            }
            return ''
        },
        // embed 链接
        embedLink() {
            if (this.link) {
                return `${this.link}/embed`
            }
            return ''
        },
        // 分享到 Classroom 链接
        shareToClassRoomLink() {
            if (this.link) {
                return `https://classroom.google.com/share?url=${this.link}`
            }
            return ''
        },
        // 下载 PPT 链接
        downloadPPTLink() {
            if (this.link) {
                return `${this.link}/export/pptx`
            }
            return ''
        }
    },
    methods: {
        // 获取模板的下载链接
        getTemplatesDownloadUrls () {
          if (!this.lessonTemplates || this.lessonTemplates.length === 0) {
            this.$store.dispatch('setHasTemplateResource', [])
            return
          }
          const downloadUrls = []
          // 遍历模板，根据类型分类获取下载链接
          this.lessonTemplates.forEach(template => {
            if (template.templateUrl) {
              downloadUrls.push(`${template.templateUrl}/export/pptx`)
            } else {
              downloadUrls.push('NO_PRESENTATION_ID')
            }
          })
          // 更新 Vuex 状态
          this.$store.dispatch('setHasTemplateResource', downloadUrls)
        },
        // 根据模板类型获取模板名称
        getTemplateNameByType (type) {
            if (!type) return ''
            const template = this.models.find(model => model.type === type)
            return template ? template.name : type
        },
        // 删除模板
        deleteTemplate (template) {
            if (this.generating || this.lessonLoading) {
                return
            }
            this.closeCustomTemplateGuide()
            this.$confirm(this.$t('loc.eduprotocols29'), this.$t('loc.confirmation'), {
                confirmButtonText: this.$t('loc.eduprotocols30'),
                cancelButtonText: this.$t('loc.cancel'),
                cancelButtonClass: 'is-plain',
                confirmButtonClass: 'el-button--danger'
            }).then(() => {
                this.lessonTemplates = this.lessonTemplates.filter(x => !equalsIgnoreCase(x.id, template.id))
                LessonApi.deleteLessonTemplate(template.id)
                .then(() => {
                    // 如果是当前课程模板，清空课程模板信息
                    this.$emit('updateLessonTemplateInfo', null, template.id)
                    this.loadTemplates()
                })
            })
        },

        // 编辑模板
        editLessonTemplate (template) {
            this.$refs.editLessonTemplate && this.$refs.editLessonTemplate.openEditTemplateDialog(template)
        },

        // 加载模板
        async loadTemplates () {
            if ((!this.lessonId && !this.newLessonId)) {
                return
            }
            await LessonApi.getLessonTemplates(this.lessonId || this.newLessonId)
            .then(res => {
                let results = []
                // 设置置顶的模板
                res.lessonTemplates.forEach(item => {
                    item.loading = false
                    item.custom = !equalsIgnoreCase(item.id, this.lessonTemplate && this.lessonTemplate.id)
                    item = { ...item, ...this.models.find(model => model.type === item.templateType) }
                    results.push(item)
                })
                // 排序
                results = results.sort((a, b) => a.custom - b.custom)
                this.lessonTemplates = results
                // 模板资源同步
                this.getTemplatesDownloadUrls()
            })
        },

        // 关闭自定义模板引导
        closeCustomTemplateGuide () {
            this.$refs.addCustomLessonTemplate && this.$refs.addCustomLessonTemplate.closeGuide()
        },

        // 生成自定义模板
        async generateCustomTemplate (params) {
            // 添加自定义模板
            this.lessonTemplates.push({
                ...this.models.find(model => model.type === params.lessonTemplateType),
                custom: true,
                loading: true
            })
            this.generating = true
            this.$emit('updateGenerateCustomTemplateLoading', true)
            this.generateCustomTemplateFailed = false
            if (this.saveLesson && typeof this.saveLesson === 'function') {
                let res = await this.saveLesson()
                if (res && res.id) {
                    this.newLessonId = res.id
                    params.lessonId = this.newLessonId
                }
            }
            let lessonGoogleSlideData = ''
            let messageCallback = (message) => {
                lessonGoogleSlideData += message.data
                const template = lessonGoogleSlideData
                    .replace(/[`]/g, '') // 去除反引号
                    .trim() // 去除首尾空格
                const result = template.length >= 3 ? template.slice(0, 3) : template
                if (equalsIgnoreCase('yes', result) && !this.generateCustomTemplateFailed) {
                    // 生成失败
                    this.generateCustomTemplateFailed = true
                    // 结束 loading 状态
                    this.generating = false
                    this.$emit('updateGenerateCustomTemplateLoading', false)
                    // 移除最后一个模板
                    this.lessonTemplates.pop()
                    // 提示生成失败
                    this.$message.error(this.$t('loc.unableToProcessRequestAdjustDescription'))
                    return
                }
                // 解析 Google Slide 数据
                this.googleSlideData = this.parseGoogleSlideData(lessonGoogleSlideData, params.lessonTemplateType)
            }
            // 生成课程 google slide 数据
            createEventSource($api.urls().generateCustomLessonTemplateStream, null, messageCallback, 'POST', params)
            .then(() => {
                if (this.generateCustomTemplateFailed) {
                    return
                }
                // 创建 google slide 数据
                this.createLessonGoogleSlide(true)
            })
            .catch(e => {
                // 结束 loading 状态
                this.lessonTemplates.forEach(item => {
                    item.loading = false
                })
                this.generating = false
                this.$emit('updateGenerateCustomTemplateLoading', false)
            })
        },

        // 解析 Google Slide 数据
        parseGoogleSlideData (data, type) {
          let parseKeys = []
          let parsedData = {}
          switch (type.toUpperCase()) {
            case 'FRAYER_MODEL':
              // 解析 Google Slide 数据
              parseKeys = [
                { key: 'centralTerm', name: ['Central Term:', 'Central Idea'] },
                { key: 'dimension1', name: ['- Dimension 1:', '- Dimension 1', 'Dimension 1:', 'Dimension 1'] },
                { key: 'dimension2', name: ['- Dimension 2:', '- Dimension 2', 'Dimension 2:', 'Dimension 2'] },
                { key: 'dimension3', name: ['- Dimension 3:', '- Dimension 3', 'Dimension 3:', 'Dimension 3'] },
                { key: 'dimension4', name: ['- Dimension 4:', '- Dimension 4', 'Dimension 4:', 'Dimension 4'] }
              ]
              parsedData = parseStreamData(data, parseKeys)
              parsedData = {
                'frayerList': parsedData.map(parseModel => {
                  return {
                      centralTerm: parseModel.centralTerm, // 设置 centralTerm
                      dimension: [
                          parseModel.dimension1,
                          parseModel.dimension2,
                          parseModel.dimension3,
                          parseModel.dimension4
                      ] // 转换四个维度字段为 List<String>
                    }
                })
              }
              break
            case 'SKETCH_AND_TELL':
              parseKeys = [
                { key: 'keyword', name: ['Central Concept:', 'Central Concept'] }
              ]
              parsedData = parseStreamData(data, parseKeys)
              parsedData = { 'keywords': parsedData.map(x => x.keyword) }
              break
            case 'SKETCH_AND_TELL_O':
              parseKeys = [
                { key: 'centralTopic', name: ['Central Topic:', 'Central Topic'] },
                { key: 'task', name: ['Task:', 'Task'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              parsedData.circleCount = this.getSketchAndTellOCircleCount()
              break
            case 'BOOKA_KUCHA':
              parseKeys = [
                { key: 'bookName', name: ['Book name:', 'Book name'] },
                { key: 'theme', name: ['BookaKucha theme:', 'BookaKucha theme'] },
                { key: 'instruction', name: ['Instruction:', 'Instruction'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              break
            case 'THIN_SLIDE':
              parseKeys = [
                { key: 'keyTerm', name: ['Key Term:', 'Key Term'] }
              ]
              parsedData = parseStreamData(data, parseKeys)
              parsedData = { 'keyTerms': parsedData.map(x => x.keyTerm) }
              break
            case 'THIN_SLIDES_VARIATIONS':
              parseKeys = [
                { key: 'answer', name: ['Make a Thin Slide answering:', 'Make a Thin Slide answering'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              break
            case 'WICKED_HYDRA':
              parseKeys = [
                { key: 'templateContent', name: ['Template Content:', 'Template Content'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              break
          }
          parsedData.type = type
          parsedData.lessonName = this.lessonName
          return parsedData
        },

        // 创建 google slide 数据
        async createLessonGoogleSlide (generateSlide = false) {
          // 如果存在 Google Slide 数据，则存储起来
          if (this.googleSlideData) {
            let params = {
              type: this.googleSlideData.type,
              lessonId: this.newLessonId || this.lessonId,
              lessonName: this.lessonName,
              ageGroup: this.ageGroup,
              lessonTemplates: this.googleSlideData,
              generateSlide: generateSlide
            }
            // 如果是 bookakucha 课程模板，但没有生成书，则提示用户生成失败，并把课程模板设置为原始课程模板
            if (equalsIgnoreCase(this.googleSlideData.type, 'BOOKA_KUCHA') && equalsIgnoreCase(this.googleSlideData.bookName, 'None')) {
              this.$message.warning(this.$t('loc.eduprotocols21'))
              return
            }
            // 调接口保存 Google Slide 数据
            try {
                await this.$axios.post($api.urls().createLessonGoogleSlide, params)
                // 提示生成成功
                this.$message.success(this.$t('loc.eduprotocols10'))
                // 重新加载课程模板
                await this.loadTemplates()
                if (generateSlide) {
                    // 结束 loading 状态
                    this.lessonTemplates.forEach(item => {
                        item.loading = false
                    })
                    this.generating = false
                    this.$emit('updateGenerateCustomTemplateLoading', false)
                    // 取最新的课程模板
                    let model = this.lessonTemplates[this.lessonTemplates.length - 1]
                    model && this.openDialog(model)
                }
            } catch (error) {
                // 结束 loading 状态
                this.lessonTemplates.forEach(item => {
                    item.loading = false
                })
                this.generating = false
                this.$emit('updateGenerateCustomTemplateLoading', false)
            }
          }
        },

              /**
       * 获取 SketchAndTellO 圈数
       */
      getSketchAndTellOCircleCount () {
        let ageValue = tools.getAgeValue(this.ageGroup)
        // 1. 从 K 到 Grade 2 是 4 个圈
        if (ageValue >= 6 && ageValue < 9) {
          return 4
        }
        // 2. 从 Grade 3 到 Grade 6 是 6 个圈
        if (ageValue >= 9 && ageValue < 12) {
          return 6
        }
        // 3. Grade 7 及以上均为 8 个圈
        if (ageValue >= 12) {
          return 8
        }
      },

        /**
         * 判断是否需要谷歌认证
         */
        authGoogleDocs() {
            if (this.needAuth) {
                this.handlerGoogleAuth()
            }
            // todo 请求后台接口创建 google docs
        },
        /**
         * 在 api.js 加载之后，这里进行加载对应的操作的 api
         */
        gapiLoaded() {
            gapi.load('client:picker', this.initializePicker)
        },

        /**
         * 等待 api 加载完成之后，这里进行加载 rest 的服务
         */
        initializePicker() {
            gapi.client.load('https:// www.googleapis.com/discovery/v1/apis/drive/v3/rest')
            this.pickerInited = true
            this.checkGoogleAuth()
        },

        /**
         * Google Identity Services 加载完成之后
         */
        gisLoaded() {
            this.googleDriveTokenClient = google.accounts.oauth2.initCodeClient({
                client_id: this.CLIENT_ID,
                scope: this.scope,
                ux_mode: 'popup',
                callback: '',
            })
            this.gisInited = true
            this.checkGoogleAuth()
        },

        /**
         * 如果同时加载完毕之后，这里应该调用后台的一个接口用于接收前台传递的 token
         */
        checkGoogleAuth(mustBeLoggedIn = false) {
            if (this.pickerInited && this.gisInited) {
                // 封装 token 和 scope 来构建凭证信息
                const credentialsRequest = {
                    authCode: this.googleAuthCode,
                    scope: this.scope,
                    userId: this.currentUser.user_id,
                    scopeKey: "drivefile",
                    onlyCheck: ""
                }
                // 接口调用，传递 token
                this.$axios.post($api.urls().checkGoogleAuth, credentialsRequest).then(data => {
                    this.needAuth = !data.success
                    if (data.success && this.userExported) {
                        this.saveToGoogleSlides()
                    }
                })

            } else if (this.pickerInited && mustBeLoggedIn) {
                // 如果 gisInited 是 false，那么就等待 gisInited 加载完成
                this.gisLoaded()
            } else if (mustBeLoggedIn) {
                // 如果 pickerInited 是 false，那么就等待 initializePicker 加载完成
                this.initializePicker()
            }
        },

        /**
         *  点击 google drive 图标触发的操作
         *  1. 登录
         *  2. 选择文件
         *  3. 完成回调
         */
        handlerGoogleAuth() {
            this.googleDriveTokenClient.callback = async (response) => {
                if (response.error !== undefined) {
                    //  如果出现错误，那么就设置导出状态为 false
                    this.userExported = false
                    throw (response)
                }
                this.googleAuthCode = response.code
                // 校验 scope 是否是规定的 scope
                if (response.scope !== this.scope) {
                    // 再一次发送请求
                    this.googleDriveTokenClient.requestCode()
                }
                // 保存对应的 token
                this.checkGoogleAuth(true)
            }

            if (this.googleAuthCode === null) {
                // Prompt the user to select a Google Account and ask for consent to share their data
                // when establishing a new session.
                this.googleDriveTokenClient.requestCode()
            } else {
                // Skip display of account chooser and consent dialog for an existing session.
                this.googleDriveTokenClient.requestCode()
            }
        },

        /**
         *  退出登录，清除 token
         */
        handleSignOutClick() {
            if (this.googleAuthCode) {
                this.googleAuthCode = null
                google.accounts.oauth2.revoke(this.googleAuthCode)
            }
        },
        // 更新课程模板信息
        updateLessonGoogleSlideMetadata (params, showTooltip = false) {
          // loading 状态开始
          this.generating = true
          this.lessonTemplates.forEach(item => {
            if (equalsIgnoreCase(item.id, params.taskId)) {
              item.loading = true
            }
          })
          // 发送请求
          this.$axios.post($api.urls().updateLessonGoogleSlideMetadata, params)
            .then(async res => {
              await this.loadTemplates()
              this.lessonTemplateModel = this.lessonTemplates.find(item => equalsIgnoreCase(item.id, params.taskId))
              this.dialogVisible = true
              if (showTooltip) {
                this.$nextTick(() => {
                  this.$message.success(this.$t('loc.eduprotocols36'))
                })
              }
            })
            .finally(() => {
              this.generating = false
              this.lessonTemplates.forEach(item => {
                if (equalsIgnoreCase(item.id, params.taskId)) {
                    item.loading = false
                }
              })
            })
        },
        // 打开预览弹窗
        openDialog (model) {
            this.lessonTemplateModel = model
            if (this.lessonTemplateModel.presentationId) {
                // 传递课程名由后台来判断是否需要更新幻灯片
                let params = {
                  taskId: this.lessonTemplateModel.id,
                  lessonTemplates: { lessonName: this.lessonName, type: this.lessonTemplateModel.templateType }
                }
                this.updateLessonGoogleSlideMetadata(params)
            } else {
                this.generating = true
                // 如果还未创建 google slide, 先调接口创建再展示
                let params = {
                    taskId: this.lessonTemplateModel.id,
                    lessonId: this.newLessonId || this.lessonId,
                    lessonName: this.lessonName,
                    type: this.lessonTemplateModel.templateType,
                    generateSlide: true
                }
                this.$axios.post($api.urls().createLessonGoogleSlide, params)
                    .then(res => {
                        this.lessonTemplateModel = { ...this.lessonTemplateModel, ...res }
                        this.dialogVisible = true
                        if (this.lessonTemplateModel && !this.lessonTemplateModel.custom) {
                            this.$emit('updateLessonTemplateInfo', res)
                        }
                    })
                    .finally(() => {
                        this.generating = false
                        model.loading = false
                    })
            }
        },
        // iframe 加载完成
        loaded() {
            this.loading = false
        },
        // 保存到 google slides
        saveToGoogleSlides() {
            let requestData = {
                taskId: this.lessonTemplateModel.id,
                lessonId: this.lessonId,
                lessonName: this.lessonName,
                type: this.lessonTemplateModel.templateType,
                scope: 'drivefile',
                generateSlide: true
            }
            // 如果是需要授权的
            if (this.needAuth) {
                // 设置导出状态为 true
                this.userExported = true
                // 判断是否需要认证
                this.authGoogleDocs()
            } else {
                // 设置导出状态为 true
                this.userExported = true
                this.$axios.post($api.urls().saveLessonGoogleSlide, requestData).then(data => {
                    // 下载成功了，此时将 load 状态修改为 false
                    // 设置导出状态为 true
                    this.userExported = false
                    // 下载成功弹出跳转页面
                    const title = 'loc.lessons2SuccessfullyExported'
                    const downloadButton = 'loc.lessons2GoDriveButton'
                    const iconUrl = require('@/assets/img/file/ppt.png')
                    this.$alert(
                        `<p class="word_keep-all lesson-message-box-pdf-confirm">
                                       <img src="${iconUrl}">
                                       <span>${this.$t('loc.eduProtocolsViewInGoogle', { googleSlidesName: data.templateName + ' - ' + this.lessonName })}</span>
                                  </p>`,
                        this.$t(title), {
                            dangerouslyUseHTMLString: true,
                            confirmButtonText: this.$t(downloadButton),
                            customClass: 'successfully-exported-message-box'
                        }).then(() => {
                        // 跳转到导出成功的页面
                        window.open(data.templateUrl);
                    })
                }).catch(error => {
                    let errorCode = error.response.data.error_code
                    if (errorCode === 'unauthorized') {
                        this.userDownload = true
                        // 判断是否需要认证
                        this.checkGoogleAuth()
                    }
                })
                    .finally(() => {
                        if (!this.userDownload) {
                            this.downloadLoading = false
                        }
                    });
            }
        }
    }
};
</script>

<style lang="less">
.lesson-message-box-pdf-confirm {
  word-break: break-word;
  display: flex;
  align-items: center;

  &>img {
    margin-right: 10px;
  }
}

.successfully-exported-message-box {
  width: 515px!important;
}
</style>

<style scoped lang="less">

/deep/ .lesson-template-preview-dialog {
    max-height: calc(100% - 40px) !important;
    height: 100%;
    background-color: var(--color-page-background-white);
    margin-top: 40px !important;
    overflow: hidden;

    & .el-dialog__header {
        margin-bottom: 10px;
        padding-left: 36px;
        padding-right: 36px;
    }

    & .el-dialog__title {
       font-size: 20px;
    }

    & .el-dialog__headerbtn {
        right: 36px;
    }

    & .el-dialog__body {
        padding: 0 36px;
        height: calc(100% - 80px);
    }

    & .el-dialog__headerbtn .el-dialog__close {
        color: var(--color-text-placeholder);
    }

    & .el-dialog__footer {
        max-width: 1375px !important;
        margin: 0 auto !important;
    }
}

.hover-show {
    position: relative;
}

.hover-show::after {
    content: '';
    position: absolute;
    height: 100%;
    width: 30px;
    right: -30px;
}

.hover-show:hover {
    .del-button {
        display: flex;
        align-items: center;
    }
}

.del-button {
    display: none;
    position: absolute;
    right: -30px;
    height: 100%;
    width: 20px;
    z-index: 1;
}

.current-template-tag {
    font-size: 12px;
    margin-left: 10px;
    background-color: var(--color-primary);
    color: var(--color-white);
    padding: 2px 4px;
    border-radius: 14px;
}
</style>
