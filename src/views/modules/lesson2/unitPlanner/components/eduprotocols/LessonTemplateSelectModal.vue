<template>
    <div class="w-full lesson-template-select-modal" :style="isCurriculumPlugin ? { position: 'relative', bottom: '0px' } : {}">
        <el-popover
            placement="bottom"
            width="500"
            :visible-arrow="true"
            :append-to-body="false"
            v-model="showGuide"
            trigger="manual"
            popper-class="lesson-template-guide-popover">
            <div>
                <div class="lg-margin-bottom-24 word-break text-left">
                    <div class="title-font-20">{{ $t('loc.eduprotocols9') }} </div>
                    <!-- 引导文字 -->
                    <div class="font-size-14 font-weight-500 line-height-24 display-inline-block lg-margin-top-12 lg-margin-bottom-16">{{ $t('loc.eduprotocols15') }}</div>
                    <!-- 引导图片 -->
                    <img class="w-full lg-border-radius-8" :src="require('@/assets/img/lesson2/unitPlanner/lesson_template_recomand.png')">
                </div>
                <!--关闭引导按钮-->
                <div class="pull-right">
                    <el-button plain @click="hideGuideOperate()">
                        <span class="font-size-14 font-weight-600 line-height-22" > {{ $t('loc.gotIt') }}</span>
                    </el-button>
                </div>
            </div>
            <!-- 模板切换按钮-->
            <el-button slot="reference"
                plain
                class="w-full lesson-template-button"
                :class="{'is-disabled': disabled, 'lesson-template-button-in-form': !inDialog, 'lesson-template-button-in-top': inDialog}"
                :size="buttonSize"
                :disabled="loading"
                @click="openDialog">
                <span class="display-flex align-items justify-content-between gap-8" v-if="selectedModelName && !disabled">
                    <span class="display-flex align-items">
                        <el-image style="width: 20px;height: 20px"
                                  :src="require('@/assets/img/lesson2/unitPlanner/lesson_template.svg')" />
                        <span class="lg-margin-left-8">{{ selectedModelName }}</span>
                    </span>
                    <i class="lg-icon lg-icon-switch"></i>
                </span>
                <span class="display-flex align-items justify-content-between gap-8" v-else-if="inDialog">
                    <span class="display-flex align-items">
                        <el-image style="width: 20px;height: 20px"
                                  :src="require('@/assets/img/lesson2/unitPlanner/lesson_template.svg')" />
                        <span class="lg-margin-left-8">{{ $t('loc.eduprotocols9') }}</span>
                    </span>
                    <i class="lg-icon lg-icon-switch"></i>
                </span>
                <span class="display-flex align-items justify-content-between gap-8" v-else>
                    <span class="display-flex align-items">
                        <el-image style="width: 20px;height: 20px"
                                  :src="require('@/assets/img/lesson2/unitPlanner/lesson_template.svg')" />
                        <span class="lg-margin-left-8">{{ $t('loc.pleaseSelect') }}</span>
                    </span>
                    <i class="lg-icon lg-icon-switch"></i>
                </span>
            </el-button>
        </el-popover>
        <!-- 模板选择弹窗 -->
        <el-dialog
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            :append-to-body="true"
            :title="$t('loc.eduprotocols19')"
            custom-class="lesson-template-recomand-dialog">
            <div class="font-size-16">
                <div>{{ $t('loc.eduprotocols5') }}</div>
                <div class="lg-margin-top-8 lg-margin-bottom-12 lg-color-warning-dark">
                    <i class="el-icon-warning-outline"></i>
                    {{ $t('loc.eduprotocols6') }}
                </div>
                <!-- 模板列表 -->
                <div class="lg-scrollbar-show lesson-template-models">
                    <el-radio-group v-model="checkedModel" :disabled="generateLoading || checking" class="add-padding-b-12 w-full">
                        <div v-for="(model, index) in models.filter(x => !x.disabled)" :key="index">
                            <el-tooltip :disabled="!model.notSupport" :content="$t('loc.eduprotocols22')" placement="top">
                                <el-radio :label="model.type" :disabled="model.notSupport">
                                    <div class="display-flex">
                                        <img :src="model.img" style="border-radius: 8px; object-fit: contain;" />
                                        <div class="lg-margin-left-16 display-flex justify-content flex-direction-col">
                                            <div class="title-font-16">{{ model.name }}
                                                <el-tooltip popper-class="tooltip-wrapper" class="item" effect="dark" :content="model.modelDescription" placement="top">
                                                    <i class="lg-icon lg-icon-question title-font-14-regular"></i>
                                                </el-tooltip>
                                            </div>
                                            <div class="model-description title-font-14-regular overflow-ellipsis-two" :title="model.description">{{ model.description }}</div>
                                        </div>
                                    </div>
                                </el-radio>
                            </el-tooltip>
                        </div>
                    </el-radio-group>
                    <div class="models-pending title-font-16-regular lg-color-warning">
                        <i class="lg-icon lg-icon-hourglass"></i>
                        <span class="lg-margin-left-8">{{ $t('loc.eduprotocols20') }}</span>
                    </div>
                    <div class="bg-disabled lg-border-radius-8 display-grid grid-colum-2 lg-padding-8">
                        <div v-for="(model, index) in models.filter(x => x.disabled)"
                             :key="index">
                            <div class="display-flex lg-padding-8">
                                <img :src="model.img" style="border-radius: 8px; object-fit: contain;" />
                                <div class="lg-margin-left-16 display-flex justify-content flex-direction-col">
                                    <div class="title-font-16">{{ model.name }}
                                        <el-tooltip popper-class="tooltip-wrapper" class="item" effect="dark" :content="model.modelDescription" placement="top">
                                            <i class="lg-icon lg-icon-question title-font-14-regular"></i>
                                        </el-tooltip>
                                    </div>
                                    <div class="model-description title-font-14-regular lg-color-text-placeholder" :title="model.description">{{ model.description }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button :disabled="generateLoading || checking" @click="dialogVisible = false">{{ $t('loc.cancel') }}</el-button>
                <el-button :loading="generateLoading || checking" type="primary" @click="handleConfirm">{{ $t('loc.confirm') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import { LessonTemplates } from '@/utils/constants'
export default {
    name: 'LessonTemplateSelectModal',
    props: {
        value: {
            type: String,
            default: ''
        },
        // 加载中
        loading: {
            type: Boolean,
            default: false
        },
        // 按钮禁用状态
        lessonAges: {
            type: Array,
            default: () => []
        },
        // 在弹窗内显示
        inDialog: {
            type: Boolean,
            default: false
        },
        // 是否显示引导
        showGuidePopover: {
            type: Boolean,
            default: false
        },
        // 按钮尺寸
        buttonSize: {
            type: String,
            default: 'default'
        },
        // 当前课程创建人信息
        lessonId: {
            type: String
        },
        // 是否是我的课程
        isMyLesson: {
            type: Boolean,
            default: false
        },
        // 修改模板后跳转的路由
        redirectRoute: {
            type: String,
            default: ''
        },
        // 保存课程方法
        saveLesson: {
            type: Function
        }
    },
    data () {
        return {
            showGuide: false, // 是否显示引导
            dialogVisible: false, // 是否显示模板选择弹窗
            generateLoading: false, // 复制课程中
            checkedModel: this.value, // 选中的模板
            // 模板列表
            models: LessonTemplates,
            checking: false // 是否正在检查
        }
    },
    mounted () {
        this.$nextTick(() => {
            // 设置当 adapt 按钮引导展示过，再显式当前引导，防止引导重叠
            this.showGuide = this.showGuidePopover && this.guideFeatures && this.guideFeatures.showLessonTemplateGuide && !this.guideFeatures.showAdaptLessonPlanDetailGuide
        })
    },
    computed: {
        ...mapState({
            guideFeatures: state => state.common.guideFeatures, // 功能引导
            isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
        }),
        // 选中的模板名称
        selectedModelName () {
            let checkedModel = this.models.find(model => model.type === this.value)
            return checkedModel && checkedModel.name
        },
        // 年龄端不适用禁用
        ageGroupDisabled () {
            let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
            return !this.lessonAges.every(age => tools.arrayContainsIgnoreCase(k12Grades, age))
        },
        // 多年龄组禁用
        multipleAgeGroupDisabled () {
            return this.lessonAges.length > 1
        },
        // 按钮是否禁用
        disabled () {
            return this.ageGroupDisabled || this.multipleAgeGroupDisabled || this.lessonAges.length === 0
        },
        // 模板是否支持使用
        modelNotSupport () {
            return function (type) {
                // 非 thin slide 模板不限制，thin slide 模板限制年龄段为 Grade 3-12
                if (type && type.toUpperCase() != 'THIN_SLIDE') {
                    return false
                }
                let grade3To12 = ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
                return !this.lessonAges.every(age => tools.arrayContainsIgnoreCase(grade3To12, age))
            }
        }
    },
    methods: {
        // 打开弹窗
        openDialog () {
            if (this.disabled) {
                if (this.multipleAgeGroupDisabled) {
                    this.$message.error(this.$t('loc.eduprotocols16'))
                } else if (this.ageGroupDisabled) {
                    this.$message.error(this.$t('loc.eduprotocols17'))
                }
                return
            }
            // 设置模板是否可用
            this.models.forEach(m => {
                m.notSupport = this.modelNotSupport(m.type)
            })
            // 按照是否可用排序
            this.models = this.models.sort((a, b) => a.notSupport - b.notSupport)
            this.checkedModel = this.value
            this.dialogVisible = true
        },
        // 关闭弹窗
        async handleConfirm () {
            if (this.checkedModel !== this.value) {
                // 切换模板前先保存课程
                if (this.saveLesson && typeof this.saveLesson === 'function') {
                    this.checking = true
                    await this.saveLesson()
                    this.checking = false
                }
                // 如果选择的是 BOOKA_KUCHA 模板，检查课程是否包含书报告
                if (this.checkedModel == 'BOOKA_KUCHA') {
                    let failed = await this.checkLessonContainBooks()
                    if (failed) {
                        this.$message.error(this.$t('loc.eduprotocols21'))
                        return
                    }
                }
                this.$emit('input', this.checkedModel)
                if (this.inDialog && this.redirectRoute) {
                    let newLessonId = this.lessonId
                    // 如果不是自己的课程，先复制课程再进行后续操作
                    if (!this.isMyLesson) {
                        this.generateLoading = true
                        let copyedLesson = await LessonApi.copyLesson(this.lessonId)
                        newLessonId = copyedLesson.id
                        this.generateLoading = false
                    }
                    this.$store.dispatch('setTemplateType', this.checkedModel)
                    this.$router.push({ name: this.redirectRoute, params: { lessonId: newLessonId, type: 'Draft' } })
                } else {
                    this.$emit('change', this.checkedModel)
                }
            }
            this.dialogVisible = false
        },
        // 检查课程是否包含书报告
        async checkLessonContainBooks () {
            try {
                this.checking = true
                let params = {
                    id: this.lessonId
                }
                let result = await LessonApi.checkLessonContainBooks(params)
                return !result.success
            } finally {
                this.checking = false
            }
        },
        // 隐藏引导
        hideGuideOperate () {
            this.showGuide = false
            const guideFeatures = this.guideFeatures
            guideFeatures.showLessonTemplateGuide = false
            this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', guideFeatures)
            // 发起请求隐藏引导
            let result = { 'features': ['EDUPROTOCOLS_TEMPLATE_GUIDE'] }
            this.$axios.post($api.urls().hideGuide, result).then()
        }
    }
}
</script>
<style>
.tooltip-wrapper {
  max-width: 50vw;
}
</style>
<style lang="less">
.el-popper.lesson-template-guide-popover {
  background: var(--color-ai-assistant);
  color: var(--color-white);
  padding: 24px;
  border: none;

  &.el-popper[x-placement^=left] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=bottom] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }
  .el-button, .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--color-border);
    background: var(--color-ai-assistant);
    color: var(--color-white);
  }
}
</style>
<style scoped lang="less">
/deep/ .el-button.lesson-template-button {
    padding-left: 10px !important;
    padding-right: 10px !important;
    border-width: 1px;
}

/deep/ .el-button.lesson-template-button-in-form:hover {
    border-color: #c0c4cc !important;
    box-shadow: none !important;
}

/deep/ .el-button.lesson-template-button-in-top {
    border-width: 2px !important;
}

/deep/ .el-button.is-disabled.lesson-template-button {
    background: #E6E9F166 !important;
    border: 1px solid var(--color-border) !important;
}

/deep/ .lesson-template-recomand-dialog {
    width: 800px;
    & .el-dialog__header {
        padding: 24px;
    }
    & .el-dialog__title {
        font-size: 20px;
    }
    & .el-dialog__body {
        padding: 0 24px;
    }

    & .el-dialog__footer {
        padding-top: 20px;
    }

    & .el-radio {
        display: flex;
        padding: 12px 16px;
        border-radius: 8px;
    }

    & .el-radio .el-radio__label {
        width: 100%;
    }

    & .el-radio.is-checked {
        background-color: var(--color-primary-light) !important;
    }

    & .el-radio:hover, .bg-disabled {
        background-color: var(--color-page-background-white);
    }

    & .el-radio.is-bordered {
        height: auto;
    }

    & .el-radio__inner {
        margin-top: 35px;
    }

    & .lesson-template-models {
        margin-right: -20px;
        padding-right: 20px;
        max-height: 400px;
    }

    & .model-description {
        text-wrap: auto;
        word-break: break-word;
    }

    & .models-pending {
        border-top: 1px dashed var(--color-border);
        padding: 12px 0;
    }
}
.dialog-footer {
    text-align: right;
}
</style>
