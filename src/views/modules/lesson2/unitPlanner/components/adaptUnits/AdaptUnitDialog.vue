<template>
  <div>
    <el-dialog
        :visible.sync="dialogVisible"
        :fullscreen="true"
        :append-to-body="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="closeDialog">
        <!-- 标题 -->
        <div slot="title">
            {{ $t('loc.adaptUnitPlanner22') }}
        </div>
        <!-- 内容 -->
        <div class="display-flex flex-start-center" v-if="unit">
            <!-- 课程菜单 -->
            <div class="lesson-menu bg-white lg-box-shadow lg-border-radius-8 lg-scrollbar">
                <!-- 单元 -->
                <div class="lg-padding-l-r-16 lg-padding-t-b-12 overflow-ellipsis">
                    <img src="~@/assets/img/lesson2/unitPlanner/curriculum_unit.svg" style="width: 24px;height: 24px" alt="" height="32">
                    <span class="lg-padding-left-12 font-size-16 font-weight-600" :title="unit.unitName">{{ unit.unitName }}</span>
                </div>
                <!-- 周计划 -->
                <el-collapse v-model="activeWeekNum" accordion>
                    <el-collapse-item v-for="(wp, index) in unit.plans" :key="wp.planId" :name="wp.week" :class="{ 'active-week': isActiveWeek(wp) }">
                        <template slot="title">
                            <div>
                                <div class="font-bold">{{$t('loc.unitPlannerStep2WeekX', {week: index+1})}}</div>
                                <div style="width: 240px;" class="overflow-ellipsis" :title="wp.theme">{{ wp.theme }}</div>
                            </div>
                        </template>
                        <div>
                            <!-- 课程 -->
                            <div class="menu-item sub-menu-item display-flex align-items justify-content-between"
                                :class="{'active-menu': isActiveItem(item)}" v-for="item in wp.items" :key="item.id"
                                @click="selectLesson(item)">
                                <div class="flex-auto">
                                    <!-- 周几 -->
                                    <div style="font-weight: 600">
                                        <span>{{ item.day }}</span>
                                        <el-tag v-if="getAdaptStatus(item) == 'SUCCESS'" effect="plain" size="small" class="adapted-tag lg-margin-left-4">
                                            {{ $t('loc.adaptUnitPlanner25') }}
                                        </el-tag>
                                    </div>
                                    <!-- 课程名称 -->
                                    <div class="overflow-ellipsis" :title="item.lessonName">
                                        {{ item.lessonName }}
                                    </div>
                                </div>
                                <!-- 确认状态 -->
                                <!-- 已确认 -->
                                <div class="lesson-confirm-status">
                                    <span v-if="getAdaptStatus(item) == 'PROCESSING' || getAdaptStatus(item) == 'PENDING'">
                                        <i class="el-icon el-icon-loading"></i>
                                    </span>
                                    <span v-else-if="getAdaptStatus(item) == 'FAIL'">
                                        <i class="lg-icon lg-icon-reject color-text-danger"></i>
                                    </span>
                                    <span v-else>
                                        <i class="lg-icon lg-icon-approve text-success"></i>
                                    </span>
                                </div>
                                <div class="active-menu-mark" v-show="isActiveItem(item)"></div>
                            </div>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>
            <!-- 课程内容 -->
            <div class="display-flex flex-direction-col position-relative" style="width: fit-content; max-width: calc(100% - 280px)">
                <el-card style="position:sticky;top: 0px;z-index: 999;margin-left: 20px;border: unset;box-shadow: unset">
                <div class="display-flex align-items justify-content-between">
                    <span :style="{'display': 'inline-block', 'width': 'calc(100% - 204px)', 'maxWidth': '903px'}" class="font-size-18 font-weight-600">{{ activeItem && activeItem.lessonName }}</span>
                    <el-button class="ai-btn" @click="adaptLesson" :loading="adaptLoading || generatingLessonContent">
                    <template #icon>
                        <i class="lg-icon lg-icon-generate"></i>
                    </template>
                    {{ $t('loc.adaptUDLAndCLRButton') }}
                    </el-button>
                </div>
                </el-card>
                <el-card style="border: unset;margin-left: 20px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;margin-top: -20px;" class="lesson-editor-content">
                    <div class="lg-scrollbar-show h-full">
                        <!-- 复制课程副本时 loading的骨架屏 -->
                        <lesson-detail-skeleton class="lesson-detail-skeleton" v-show="loading || loadLessons.filter(x => !!x.lessonId).length == 0"></lesson-detail-skeleton>
                        <div v-for="(lesson) in loadLessons.filter(x => !!x.lessonId)" :key="lesson.itemId">
                            <div v-if="activeItem.itemId.toUpperCase() === lesson.itemId.toUpperCase()">
                                <!-- 课程编辑页面 -->
                                <LessonDetail v-show="!loading" class="lesson-detail"
                                                :ref="'lessonDetail' + lesson.itemId.toUpperCase()"
                                                @lessonLoaded="callLessonLoaded"
                                                @updateLessonName="updateLessonName"
                                                @updateGeneratingLessonContent="updateGeneratingLessonContent"
                                                :itemId="lesson.itemId"
                                                :batchEdit="true"
                                                :isAdaptedLesson="true"
                                                :unitPlannerBatchAdapt="true"
                                                :lessonId="lesson.lessonId"
                                                :inDialog="true"></LessonDetail>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>
        </div>
        <div slot="footer">
            <el-button v-show="hasNextLesson" :loading="loading || adaptLoading || changeLessonLoading || saveLessonLoading || generatingLessonContent" @click="nextLesson">{{ $t('loc.adaptUnitPlanner23') }}</el-button>
            <el-button type="primary" :loading="loading || adaptLoading || changeLessonLoading || saveLessonLoading || generatingLessonContent" @click="saveAll">{{ $t('loc.adaptUnitPlanner24') }}</el-button>
        </div>
    </el-dialog>

    <LgNotification
        style="z-index: 9999; top: 50px; left: 50px;"
        ref="notification"
        position="top-left"
        :autoClose="false"
        :timer="30"
        :progress="progress"
    >
        <span slot="content">
            <span>{{ $t('loc.adaptUnitPlanner21') }}</span>
        </span>
    </LgNotification>
  </div>
</template>

<script>
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton.vue'
import LessonDetail from '@/views/modules/lesson2/lessonLibrary/editor/index.vue'
import LgNotification from '@/components/LgNotification.vue'
import LessonApi from '@/api/lessons2'
import {mapState} from "vuex";
export default {
  name: 'AdaptUnitDialog',
  components: {
    LessonDetailSkeleton,
    LessonDetail,
    LgNotification
  },
  data () {
    return {
        dialogVisible: false, // 弹窗显示
        unit: null, // 单元信息
        applyParams: null, // 应用到周计划参数
        activeWeekNum: '', // 当前高亮周次
        loading: true, // 课程加载中
        batchId: '', // 批量改编任务 Id
        activeItem: null, // 当前高亮课程
        loadLessons: [], // 已加载的课程
        adaptLoading: false, // 正在改编课程
        saveLessonLoading: false, // 正在保存课程
        udlCompleted: false, // UDL 改编完成
        clrCompleted: false, // CLR 改编完成
        progress: 100, // 批量改编课程进度
        batchTaskTimer: null, // 批量任务定时器
        retryCount: 0, // 重试次数
        batchComplete: false, // 批量改编完成
        isSignalLesson: false, // 是否是单课程
        changeLessonLoading: false, // 切换课程loading
        generatingLessonContent: false, // 课程内容生成中
        failCopyLessonIds: [], // 添加失败的课程ID数组
        overwriteLessonVersion: null // 覆盖模式下课程的历史版本对应关系
    }
  },
  computed: {
    ...mapState({
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
    }),
    // 课程是否高亮
    isActiveItem () {
      return function (obj) {
        if (obj && this.activeItem) {
          return (this.activeItem.itemId.toUpperCase() === obj.itemId.toUpperCase())
        } else {
          return false
        }
      }
    },
    // 周是否高亮
    isActiveWeek () {
      return function (obj) {
        let currentPlan = this.unit.plans.find(item => obj.planId === item.planId)
        let items = currentPlan.items
        return this.activeWeekNum !== obj.week && items.some(item => this.isActiveItem(item))
      }
    },
    // 是否能切换下一个课程
    hasNextLesson () {
        if (!this.unit || !this.activeItem) {
            return false
        }
        // 把单元中所有的课程取出来
        let lessonItems = this.unit.plans.map(plan => plan.items.filter(x => !!x.lessonId)).flat()
        // 找到当前课程的位置
        let index = lessonItems.findIndex(item => item.itemId.toUpperCase() === this.activeItem.itemId.toUpperCase())
        // 找到下一个课程
        let nextItem = lessonItems[index + 1]
        return !!nextItem
    },
    getAdaptStatus () {
        return function (item) {
            let lesson = this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === item.itemId.toUpperCase())
            if (lesson) {
                return lesson.status
            }
            return 'PROCESSING'
        }
    },
    // 第一个课程是否改编完成
    lessonAdaptCompleted () {
        return this.clrCompleted && this.udlCompleted
    },

    /**
     * 是否能覆盖处理
     *
     * @returns {null|boolean|boolean}
     */
    overwriteLessons () {
      return this.applyParams && this.applyParams.overwrite && this.overwriteLessonVersion && Object.keys(this.overwriteLessonVersion).length > 0
    }
  },
  watch: {
    // 监听第一个课程改编完成后更新状态
    lessonAdaptCompleted (val) {
        if (val) {
            let activeItemId = this.activeItem.itemId
            let currentItem = this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === activeItemId.toUpperCase())
            if (currentItem) {
                currentItem.status = 'SUCCESS'
            }
            if (this.batchComplete) {
                this.adaptLoading = false
                // 成功了打印提示
                this.showSuccessTips(false)
            } else {
                this.queryUnitBatchAdaptTaskStatus()
            }
        }
    }
  },
  destroyed () {
    this.$bus.$off('generateUniversalDesignForLearningCompleted')
    this.$bus.$off('generateCLRCompleted')
    this.$bus.$off('generateUniversalDesignForLearningStart')
    this.$bus.$off('generateCLRStart')
  },
  methods: {
    // 更新课程生成 loading
    updateGeneratingLessonContent (loading) {
      this.generatingLessonContent = loading
    },

    /**
     * 如果当前是覆盖模板查询当前课程的最新历史版本
     */
    async initLessonVersionInfo () {
      // 非覆盖模板返回
      if (!this.applyParams || !this.applyParams.overwrite) {
        return
      }
      // 组装课程 ID 列表（去重）
      const lessonIds = [
        ...new Set(
          this.unit.plans.flatMap(plan =>
            plan.items.map(item => item.lessonId)
          )
        )
      ]
      // 查询当前的历史版本
      try {
        const res = await this.$axios.post($api.urls().getLessonsVersionInfo, {
          lessonIds: lessonIds
        })
        if (res && res.data && res.data.length > 0) {
          this.overwriteLessonVersion = res.data.reduce((acc, item) => {
            acc[item.lessonId] = item.id
            return acc
          }, {})
        }
      } catch (e) {
      }
    },

    // 初始化课程完成改编时间监听
    initLessonAdaptCompletedListener () {
        this.$bus.$on('generateUniversalDesignForLearningCompleted', (itemId) => {
            this.setLessonStatusCompleted(itemId)
            this.udlCompleted = true
        })
        this.$bus.$on('generateCLRCompleted', (itemId) => {
            this.setLessonStatusCompleted(itemId)
            this.clrCompleted = true
        })
        // 监听 generateUniversalDesignForLearningStart 事件
        this.$bus.$on('generateUniversalDesignForLearningStart', (itemId) => {
            this.udlCompleted = false
            // 修改生成的状态为开始
            const itemKey = itemId.toUpperCase()
            // 设置状态
            let currentItem = this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === itemKey)
            if (currentItem) {
                currentItem.status = 'PROCESSING'
                this.adaptLoading = true
            }
        })
        // 监听 generateCLRStart 事件
        this.$bus.$on('generateCLRStart', (itemId) => {
            this.clrCompleted = false
            // 修改生成的状态为开始
            const itemKey = itemId.toUpperCase()
            // 设置状态
            let currentItem = this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === itemKey)
            if (currentItem) {
                currentItem.status = 'PROCESSING'
                this.adaptLoading = true
            }
        })
    },

    // 设置对应的 itemId 的状态为 SUCCESS
    setLessonStatusCompleted (itemId) {
        // 如果 itemId 不存在，就直接返回
        if (!itemId) {
            return false
        }
        // 如果 item 存在，并且 this.$refs['lessonDetail' + itemId] 的长度大于 0
        if (!this.$refs['lessonDetail' + itemId.toUpperCase()] || this.$refs['lessonDetail' + itemId.toUpperCase()].length <= 0) {
            return false
        }
        // 拼接 itemId 和 lessonDetail
        const lessonDetail = this.$refs['lessonDetail' + itemId.toUpperCase()][0]
        // 由于发生了 lessonLibraryEditorIndexMounted 事件，说明当前课程已经加载完毕了
        // 那么就可以调用 lessonDetail 的生成 UDL 和 CLR 数据了
        // 如果 lessonDetail 存在，并且是批量生成数据
        if (lessonDetail) {
            // 修改生成的状态为开始
            const itemKey = itemId.trim().toUpperCase()
            // 判断生成状态是否已经生成完了
            const loading = lessonDetail.generateDataLoading()
            // 如果是 false，就说明内容生成完了
            if (!loading) {
                // 设置状态
                this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === itemKey).status = 'SUCCESS'
                if (this.batchComplete) {
                    this.adaptLoading = false
                    lessonDetail.convertLessonLoading = false
                }
            }
            return loading
        }
        return false
    },

    // 打开弹窗，当前组件初始化入口
    async openDialog (unit, applyParams) {
        this.unit = unit
        this.applyParams = applyParams
        this.activeWeekNum = this.unit.plans[0].week
        this.activeItem = this.unit.plans[0].items[0]
        await this.initLessonVersionInfo()
        this.loadLesson(this.activeItem.lessonId)
        this.createUnitBatchAdaptTask()
        this.adaptLoading = true
        this.dialogVisible = true
        this.initLessonAdaptCompletedListener()
    },

    // 关闭改编弹窗
    closeDialog () {
        if (this.saveLessonLoading) {
            return
        }
        // 如果正在改编课程
        if (this.adaptLoading) {
            this.$confirm(this.$t('loc.batchAdaptLesson8'),
                this.$t('loc.confirmation'), {
                    confirmButtonText: this.$t('loc.batchAdaptLesson9'),
                    cancelButtonText: this.$t('loc.cancel'),
                    showClose: true,
                    closeOnClickModal: false,
                    customClass: 'plan-message-box',
                    confirmButtonClass: 'el-button--danger',
                    distinguishCancelAndClose: true,
                    callback: async action => {
                        if (action === 'confirm') {
                            // 如果不是覆盖改编，则删除复制出来的课程
                            this.batchRemoveLesson()
                            this.clearData()
                        }
                    }
                }
            )
        } else {
            this.$confirm(this.$t('loc.batchAdaptLesson2'),
                this.$t('loc.confirmation'), {
                    confirmButtonText: this.$t('loc.save'),
                    cancelButtonText: this.$t('loc.noSave'),
                    showClose: true,
                    closeOnClickModal: false,
                    distinguishCancelAndClose: true,
                    callback: async action => {
                        if (action === 'confirm') {
                            this.saveAll()
                        } else if (action === 'cancel') {
                            // 如果不是覆盖改编，则删除复制出来的课程
                            this.batchRemoveLesson()
                            this.clearData()
                        }
                    }
                }
            )
        }
    },

    // 批量取消保存课程
    batchRemoveLesson () {
        // 批量取消的时候需要得到第一个课程 Id，因为第一个课程不是后台任务生成的
        const firstLessonId = this.loadLessons[0].lessonId
        // 获取 batchId
        if (this.batchId && firstLessonId) {
            const clearLessonIds = [
              ...this.failCopyLessonIds,
              firstLessonId
            ]
            const params = {
              batchId: this.batchId,
              clearLessonIds: clearLessonIds,
              type: 'ADD_LESSON',
              overwriteLessonVersion: this.overwriteLessonVersion
            }
            if (this.overwriteLessons) {
              // 对于历史版本数据，删除历史版本回复原数据，且中间版本的数据也需要清除
              params.type = 'ADD_VERSION'
            }
            // 调用对应的移除接口
            this.$axios.post($api.urls().batchRemoveAdaptedLesson, params)
            .then(res => {
            })
            .catch(error => {
                // 取消保存失败
                this.$message.error(error.response.data.error_message)
            })
        }
    },

    // 关闭弹窗
    clearData () {
        this.unit = null
        this.applyParams = null
        this.activeItem = null
        this.activeWeekNum = ''
        this.loadLessons = []
        this.batchId = ''
        this.adaptLoading = false
        this.saveLessonLoading = false
        this.clrCompleted = false
        this.udlCompleted = false
        this.batchComplete = false
        this.retryCount = 0
        this.changeLessonLoading = false
        this.$refs.notification && this.$refs.notification.close()
        this.progress = 100
        this.batchTaskTimer && clearTimeout(this.batchTaskTimer)
        this.dialogVisible = false
        // 发送一个事件，告诉父组件关闭弹窗
        this.$bus.$emit('closeAdaptUnitDialog')
    },

    // 创建单元批量适配任务
    createUnitBatchAdaptTask () {
        let params = {
            type: 'ADD_LESSON',
            unitId: this.unit.unitId,
            groupId: this.applyParams.groupId,
            plans: this.unit.plans.map(plan => {
                return {
                    planId: plan.planId,
                    adaptItems: plan.items.filter(item => item.itemId !== this.unit.plans[0].items[0].itemId).map(item => {
                        return {
                            itemId: item.itemId,
                            lessonId: item.lessonId
                        }
                    })
                }
            })
        }

        // 判断是否有要改编的课程
        if (params.plans.map(plan => plan.adaptItems).flat().length === 0) {
            // 如果没有要改编的课程，则直接改编当前课程
            this.isSignalLesson = true
            this.batchComplete = true
            return
        }
        // 批量创建历史版本，再批量改编
        if (this.overwriteLessons) {
          params.type = 'ADD_VERSION'
        }
        this.$axios.post($api.urls().createUnitBatchAdaptLessonTask, params)
        .then(res => {
            this.batchId = res.id
            this.$refs.notification.show()
        })
        .catch((e) => {
            this.adaptLoading = false
            this.$message.error(e.message)
        })
    },

    // 查询单元批量适配任务状态
    queryUnitBatchAdaptTaskStatus () {
        this.$axios.get($api.urls().queryBatchAdaptLessonTask, {
            params: {
                batchId: this.batchId
            }
        })
        .then(res => {
            this.handleTasks(res.tasks)
            if (res.completed) {
                this.batchTaskTimer && clearTimeout(this.batchTaskTimer)
                this.$refs.notification.close()
                let failTaskIds = res.tasks.filter(task => task.status === 'FAIL').map(task => task.taskId)
                // 如果确实存在失败的任务或者待处理的任务，则重试
                if (failTaskIds.length > 0 && this.retryCount < 3) {
                    failTaskIds = failTaskIds.join(',')
                    this.processFailedTask(failTaskIds)
                    return
                }
                this.batchComplete = true
                this.adaptLoading = this.setLessonStatusCompleted(this.activeItem.itemId)
                // 是否有改编成功的
                if (res.tasks.some(task => task.status === 'SUCCESS')) {
                    this.showSuccessTips(true)
                }
            } else {
                this.batchTaskTimer = setTimeout(() => {
                    this.queryUnitBatchAdaptTaskStatus()
                }, 5000)
            }
        })
        .catch(() => {
            this.adaptLoading = false
        })
    },
    /**
     * 显示成功提示
     */
    showSuccessTips(showUnitMessage) {
      if (this.applyParams.overwrite) {
        this.$message({
          message: this.$t('loc.batchAdaptLesson3'),
          type: 'success'
        })
      } else if (showUnitMessage || this.isSignalLesson) {
        this.$message({
          message: this.$t('loc.adaptUnitPlanner8'),
          type: 'success'
        })
      } else {
        this.$message({
          message: this.$t('loc.batchAdaptLesson19'),
          type: 'success'
        })
      }
    },
    // 处理任务
    handleTasks (tasks) {
        // 未成功的任务
        let processingTaskss = tasks.filter(task => task.status !== 'SUCCESS').map(task => task.taskId)
        // 计算进度
        this.progress = (processingTaskss.length / tasks.length) * 100
        // 失败的任务或者待处理的任务
        let failTaskIds = tasks.filter(task => task.status === 'FAIL' || task.status === 'PENDING').map(task => task.taskId)
        // 如果确实存在失败的任务或者待处理的任务，则重试
        if (failTaskIds.length > 0) {
            failTaskIds = failTaskIds.join(',')
            LessonApi.retryFailedTasks(failTaskIds)
        }
        // 更新任务
        tasks.forEach(task => {
            let lesson = this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === task.itemId.toUpperCase())
            if (lesson) {
                lesson.status = task.status
                lesson.lessonId = task.lessonId
            } else {
                this.loadLessons.push({
                    itemId: task.itemId,
                    lessonId: task.lessonId,
                    status: task.status
                })
            }
        })
    },

    // 处理失败的任务
    processFailedTask (failTaskIds) {
        // 弹出弹窗
        this.$confirm(this.$t('loc.batchAdaptLesson11'),
            this.$t('loc.batchAdaptLesson10'), {
                confirmButtonText: this.$t('loc.batchAdaptLesson12'),
                showClose: false,
                type: 'warning',
                closeOnClickModal: false,
                customClass: 'plan-message-box',
                callback: async action => {
                    if (action === 'confirm') {
                        this.$refs.notification.show()
                        this.retryCount++
                        LessonApi.retryFailedTasks(failTaskIds)
                        this.batchTaskTimer = setTimeout(() => {
                            this.queryUnitBatchAdaptTaskStatus()
                        }, 5000)
                    } else {
                        this.batchComplete = true
                        this.$refs.notification.close()
                        this.adaptLoading = false
                    }
                }
            })
    },

    // 选择课程
    async selectLesson (item) {
        // 1.如果当前活动课程未生成，则不允许切换到当前活动 2.移除所选活动生成失败不能选择 this.getAdaptStatus(item) !== 'SUCCESS' 3.仍在批量生成，不允许切换
        if (!this.batchComplete || this.getAdaptStatus(this.activeItem) === 'PROCESSING' || this.changeLessonLoading) {
            return
        }
        // 如果切换的是当前课程，则不做任何操作
        if (this.activeItem.itemId.toUpperCase() === item.itemId.toUpperCase()) {
            return
        }
        this.changeLessonLoading = true
        // 获取当前操作的 itemId
        const itemId = this.activeItem.itemId
        const lessonDetail = this.$refs['lessonDetail' + itemId.toUpperCase()][0]
        if (!lessonDetail) {
            this.changeLessonLoading = false
            return
        }
        // 移动到指定位置
        let publishInfo = await lessonDetail.publishLesson()
        if (!publishInfo) {
            this.changeLessonLoading = false
            return
        }
        this.changeLessonLoading = false
        this.activeItem.lessonName = publishInfo.name
        // 如果当前活动课程已经生成，则直接切换
        this.activeItem = item
        // 定位到 UDL
        this.$nextTick(async () => {
          // 获取当前操作的 itemId
          const itemId = this.activeItem.itemId
          // 如果此时没有对应的 lessonDetail，则复制重新生成
          if (!this.$refs['lessonDetail' + itemId.toUpperCase()]) {
            // 补偿
            await this.failLessonCompensation()
            return
          }
          const lessonDetail = this.$refs['lessonDetail' + itemId.toUpperCase()][0]
          // 移动到指定位置
          lessonDetail.scrollIntoUDLOrCLR()
        })
    },

    // 如果批量生成失败，用户点击 item 时，进行补偿
    async failLessonCompensation () {
      // 如果当前是失败课程，重新生成
      this.loadLessons = this.loadLessons.filter(lesson => lesson.itemId.toUpperCase() !== this.activeItem.itemId.toUpperCase())
      await this.loadLesson(this.activeItem.lessonId).then(() => {
        this.activeItem.status = 'FAIL'
        const lessonItem = this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === this.activeItem.itemId.toUpperCase())
        lessonItem.status = 'FAIL'
        // 记录下来手动 copy 的课程，用于不保存时清除
        this.failCopyLessonIds.push(lessonItem.lessonId)
      })
    },

    // 加载课程
    loadLesson (lessonId) {
      this.loading = true
      if (this.loadLessons.some(lesson => lesson.itemId.toUpperCase() === this.activeItem.itemId.toUpperCase())) {
        this.loading = false
        return Promise.resolve()
      }

      if (this.overwriteLessons) {
        // 覆盖旧单元时，仍使用原课程 ID，移除时根据历史版本恢复数据
        this.loadLessons.push({
          itemId: this.activeItem.itemId,
          lessonId: lessonId,
          status: 'PROCESSING'
        })
        this.loading = false
        return Promise.resolve()
      } else {
        // 新单元要创建新课程
        return LessonApi.copyAdaptLesson(lessonId)
          .then(res => {
            this.loadLessons.push({
              itemId: this.activeItem.itemId,
              lessonId: res.id,
              status: 'PROCESSING'
            })
          })
          .finally(() => {
            this.loading = false
          })
      }
    },

    // 课程加载完成后开始改编
    callLessonLoaded (lessonId) {
        if (this.loadLessons && this.loadLessons[0].lessonId === lessonId && this.loadLessons[0].status === 'PROCESSING') {
            this.adaptLesson(true, true)
        } else {
            this.adaptLesson(false, true)
        }
    },

    // 更新课程名称
    updateLessonName (lessonId, lessonName) {
        this.activeItem.lessonName = lessonName
    },

    // 改编课程
    adaptLesson (adapt = true, again = false) {
        this.$nextTick(() => {
            // 获取当前操作的 itemId
            const itemId = this.activeItem.itemId
            const lessonDetail = this.$refs['lessonDetail' + itemId.toUpperCase()][0]
            // 移动到指定位置
            lessonDetail.scrollIntoUDLOrCLR()
            if (adapt) {
                if (again) {
                  lessonDetail.generateUniversalDesignAndCLRData()
                  this.adaptLoading = true
                  this.udlCompleted = false
                  this.clrCompleted = false
                  this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === itemId.toUpperCase()).status = 'PROCESSING'
                } else {
                  // 为当前课程生成数据
                  lessonDetail.updateGenerateUniversalDesignAndCLRData()
                }
            }
        })
    },

    // 切换下一个课程
    nextLesson () {
        if (this.changeLessonLoading) {
            return
        }
        // 把单元中所有的课程取出来
        let lessonItems = this.unit.plans.map(plan => plan.items.filter(x => this.getAdaptStatus(x) == 'SUCCESS')).flat()
        // 找到当前课程的位置
        let index = lessonItems.findIndex(item => item.itemId.toUpperCase() === this.activeItem.itemId.toUpperCase())
        // 找到下一个课程
        let nextItem = lessonItems[index + 1]
        // 如果下一个课程存在，则切换到下一个课程
        if (nextItem) {
            this.selectLesson(nextItem)
        }
    },

    // 保存课程
    async saveAll () {
        this.saveLessonLoading = true
        // 保存当前课程
        const itemId = this.activeItem.itemId
        const lessonDetail = this.$refs['lessonDetail' + itemId.toUpperCase()][0]
        if (!lessonDetail) {
            this.saveLessonLoading = false
            return
        }
        // 发布课程
        const planId = this.findPlanByItemId(itemId)
        let publishInfo = await lessonDetail.publishLesson(planId)
        if (!publishInfo) {
            this.saveLessonLoading = false
            return
        }
        // 批量更新课程
        let params = {
            unitId: this.unit.unitId,
            plans: this.unit.plans.map(plan => {
                return {
                    planId: plan.planId,
                    // item 先过滤掉改编失败的课程
                    items: plan.items.filter(item => this.getAdaptStatus(item) == 'SUCCESS').map(item => {
                        return {
                            itemId: item.itemId,
                            lessonId: this.loadLessons.find(lesson => lesson.itemId.toUpperCase() === item.itemId.toUpperCase()).lessonId,
                            lessonName: item.lessonName
                        }
                    })
                }
            }).filter(plan => plan.items.length > 0)
        }
        LessonApi.batchUpdateUnitPlanItemLesson(params)
        .then(() => {
            if (this.applyParams.startDate) {
                this.applyToWeeklyPlan()
            } else {
                this.saveLessonLoading = false
                this.clearData()
                this.$emit('refreshUnits')
            }
            this.$message.success(this.$t('loc.saveSfy'))
        })
        .catch(error => {
            this.saveLessonLoading = false
            // this.$message.error(error.message)
        })
        // 改编完成之后，对于改编后的单元会生成一个操作记录，对该记录进行保存
        LessonApi.addAction({ unitId: this.unit.unitId }).catch(err => {
        })
        // 如果勾选了应用到周计划，用户自主选择跳转的页面
        if (this.applyParams.startDate) {
            return
        }
        const routerName = this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail'
        // 页面跳转到单元详情页面
        this.$router.push({
            name: routerName,
            query: {
                unitId: this.unit.unitId
            }
        })
    },

    findPlanByItemId (itemId) {
      if (!this.unit || !this.unit.plans || !itemId) {
        return null
      }
      // 遍历每个 plan
      for (const plan of this.unit.plans) {
        // 过滤出符合条件的 items
        const matchedItems = plan.items.filter(item =>
          item.itemId.toUpperCase() === itemId.toUpperCase()
        )

        // 如果找到匹配的 items，则返回相关的 plan 数据
        if (matchedItems.length > 0) {
          return plan.planId
        }
      }

      // 如果没有找到对应的 plan，返回 null 或者适当的提示信息
      return null
    },

    // 应用到周计划
    applyToWeeklyPlan () {
        let unitId = this.unit.unitId
        let params = {
            aiGenerated: true,
            unitIds: [unitId],
            planIds: this.unit.plans.map(plan => plan.planId),
            centerId: this.applyParams.centerId,
            groupId: this.applyParams.groupId,
            startDate: this.$moment(this.applyParams.startDate).format('YYYY-MM-DD'),
            startWeek: 1
        }
        LessonApi.applyPlans(params)
        .then(res => {
            this.adaptLoading = false
            this.$confirm(this.$t('loc.adaptUnitPlanner10', { groupName: this.applyParams.groupName }), this.$t('loc.inkindNote'), {
                confirmButtonText: this.$t('loc.adaptUnitPlanner12'),
                cancelButtonText: this.$t('loc.adaptUnitPlanner11'),
                type: 'success',
                cancelButtonClass: 'el-button--primary',
                customClass: 'adapted-confirmation',
                distinguishCancelAndClose: true,
                closeOnClickModal: false
            })
            .then(() => {
              let planId = res.planId
              this.$store.dispatch('setShowMappedTip', res.mapped) // 设置是否显示映射提示
              this.$store.dispatch('setCurrentFrameworkId', res.weeklyFrameworkId) // 设置是否显示映射提示
              this.$router.push({
                name: 'edit-plan',
                params: {
                  planId: planId,
                  apply: true,
                  isAdapted: true
                }
              })
            })
            .catch(action => {
                const routerName = this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail'
                // 跳转到周计划页面
                if (action === 'cancel') {
                  // 跳转到单元详情页面
                  this.$router.push({
                    name: routerName,
                    query: {
                      unitId: unitId
                    }
                  })
                } else {
                    // 点 x 关闭，刷新单元列表
                    this.$emit('refreshUnits')
                }
            })
        })
        .catch(error => {
            this.saveLessonLoading = false
            this.$message.error(error.message)
        })
        .finally(() => {
            this.clearData()
        })
    }
  }
}
</script>

<style lang="less" scoped>
@media screen and (max-width: 1599px) {
    .lesson-detail-skeleton {
        margin: 10px 0;
        width: 1000px;
        padding: 0 20px;
    }

    /deep/ .new-lesson > .content {
        padding: 0 20px;
    }
    /deep/ .content {
      margin: 10px 0;
      min-width: 1000px;
      width: 1000px;
      form {
        background-color: #fff;
        width: 950px;
        padding: 0px 0px 0px 20px!important;
      }

      .form-container {
        overflow-x: hidden;
        height: auto;
      }

      .assistant-toolbar {
        width: 365px;
        height: calc(100vh - 148px);
        // overflow: auto;
        position: sticky;
        top: 0;
      }
    }
    /deep/ .el-dialog__footer {
        max-width: 1375px!important;
        margin: 0 auto!important;
    }

    /deep/ .el-form-item {

        &.lesson-form-name, &.lesson-form-age, &.lesson-form-theme, &.lesson-form-framework, &.lesson-form-domain {
            max-width: 455px;
        }
        &.lesson-form-prepare, &.lesson-form-activity {
            max-width: 220px;
        }
        &.lesson-form-activity {
            margin-left: 15px;
        }
    }

    /deep/ .media-uploader-lesson-cover {
        width: 450px;
        height: 310px;
        position: relative;
        border: 1px solid transparent;

        .media-uploader {
            width: 450px;
            height: 310px;
            margin-left: -10px;
        }

        .media-uploader-select {
            width: 460px;
            margin-top: -20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            & > :first-child {
                width: 176px;
                height: 126px;
            }

            & > :nth-child(2) {
                width: 549px;
                color: #676879;
                font-size: 12px;
                line-height: 22px;
                text-align: center;
                margin: 16px auto 16px auto;
            }
        }

        .media-uploader-selected {
            width: 450px;
            height: 310px;
            position: relative;

            & > .media-viewer {
                height: 100%;
            }
        }
  }
}

@media screen and (min-width: 1600px) {
    .lesson-detail-skeleton {
        width: 1147px;
        margin: 10px 0;
        padding: 0 20px;
    }

    /deep/ .new-lesson > .content {
        padding-left: 20px;
    }
    /deep/ .content {
      margin: 0 0 20px 0;
      min-width: 1147px;
      form {
        width: 1115px;
        background-color: #fff;
        padding: 20px 0px 0px 0px!important;
      }
      .assistant-toolbar {
        width: 415px;
        height: calc(100vh - 148px);
        // overflow-y: auto;
        position: sticky;
        top: 0;
      }
      .assistant-toolbar-full{
        width: calc(100vw - 300px);
        height: calc(100vh - 148px);
        // overflow-y: auto;
        position: sticky;
        top: 0;
      }
    }
    /deep/ .el-dialog__footer {
        max-width: 1450px!important;
        margin: 0 auto!important;
    }
}

.lesson-editor {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
}

/deep/ .el-dialog {
    max-height: calc(100% - 40px) !important;
    height: 100%;
    background-color: var(--color-page-background-white);
    margin-top: 40px;
    overflow: hidden;
}

/deep/ .el-dialog__body {
    padding: 0 20px;
    overflow: auto;
    height: calc(100% - 130px);
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
  color: var(--color-text-placeholder);
}

/deep/ .lesson-editor-content .el-card__body {
    padding: 20px 0;
}

/deep/ .lesson-dll .el-card__body {
    padding: 20px;
}

/deep/ .el-dialog {
    max-height: calc(100% - 40px) !important;
    height: 100%;
    background-color: var(--color-page-background-white);
    margin-top: 40px;
    overflow: hidden;
}

// /deep/ .el-dialog__body {
//     padding: 0 20px;
//     overflow: auto;
//     height: calc(100% - 130px);
//     display: flex;
//     flex-direction: column;
//     align-items: center;
// }

/deep/ .el-dialog__headerbtn .el-dialog__close {
    color: var(--color-text-placeholder);
}

/deep/ .el-dialog__footer {
    max-width: 1375px!important;
    margin: 0 auto!important;
}

.lesson-menu {
    width: 280px;
    max-height: calc(100vh - 170px);
    height: fit-content;
    position: sticky;
    top: 0;
    overflow-x: hidden !important;
}

.flex-start-center {
    display: flex;
    -webkit-box-align: center;
    align-items: flex-start;
    justify-content: center;
}

.menu-item {
    font-size: 14px;
    position: relative;
    padding: 10px 14px;
    cursor: pointer;

    &:hover {
        background-color: #E7F7F8;
    }
}

.sub-menu-item {
    padding-left: 24px;
}

.active-menu {
    color: #10B3B7;
    background-color: #E7F7F8;
}

.active-menu-mark {
    width: 3px;
    height: 100%;
    background-color: #10B3B7;
    position: absolute;
    left: 0;
    top: 0;
}

.lesson-confirm-status {
    flex-shrink: 0;
    flex-grow: 0;
}

.adapted-tag {
  color: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
}

/deep/ .el-collapse-item__header {
    line-height: unset !important;
    padding-left: 16px;
    padding-right: 4px;
    height: 60px;
    font-size: 14px;
}

/deep/ .active-week .el-collapse-item__header {
    background-color: #E7F7F8 !important;
}

/deep/ .el-collapse-item__content {
    padding-bottom: 0px !important;
}

/deep/ .el-collapse-item__arrow {
    font-size: 20px !important;
}

/deep/ .el-collapse-item__wrap {
    border-radius: 0 0 8px 8px;
}
/deep/ .lesson-detail .content .lesson-editor-container .lesson-form {
    box-shadow: none !important;
    height: auto !important;
}
</style>
