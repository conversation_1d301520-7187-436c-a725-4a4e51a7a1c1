<template>
  <div class="personalize-to-learn-container">
    <el-card shadow="never" class="card">
      <div slot="header" class="header">
        <div class="header-content">
          <div>
            <span class="title">Personalize to My Students</span>
          </div>
        </div>
      </div>
      <div class="content" v-loading="attrLoading">
        <el-form :model="personalizedForm" class="w-full" label-position="top">
          <!--region-->
          <el-form-item class="add-margin-bt10">
            <div class="w-full position-relative font-bold font-size-14">
              1. {{$t('loc.pluginUnit5')}}
            </div>
            <div class="w-full">
              <el-select v-model="personalizedForm.region"
                         multiple
                         @change="changePersonalizeForm"
                         ref="region"
                         :placeholder="$t('loc.infoNum')" class="w-full">
                <el-option
                  v-for="item in countries"
                  :key="item.name"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <!--race-->
          <el-form-item class="add-margin-bt10">
            <div class="w-full position-relative font-bold font-size-14">
              2. {{$t('loc.pluginUnit6')}}
            </div>
            <div class="w-full">
              <el-select v-model="personalizedForm.race"
                         multiple
                         @change="changePersonalizeForm"
                         ref="race"
                         :placeholder="$t('loc.infoNum')" class="w-full">
                <el-option
                  v-for="item in childRaceModel"
                  :key="item.name"
                  :label="item.displayName"
                  :value="item.displayName"
                >
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <!--home language-->
          <el-form-item class="add-margin-bt10">
            <div class="w-full position-relative font-bold font-size-14">
              3. {{$t('loc.pluginUnit7')}}
            </div>
            <div class="w-full">
              <el-select v-model="personalizedForm.language"
                         multiple
                         ref="race"
                         @change="changePersonalizeForm"
                         :placeholder="$t('loc.infoNum')" class="w-full">
                <el-option
                  v-for="item in childLanguageModel"
                  :key="item.name"
                  :label="item.displayName"
                  :value="item.displayName"
                >
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item class="add-margin-bt10">
            <div class="w-full position-relative font-bold font-size-14 btn-center">
              4. Any students with IEP/IFSP in your class?
              <el-tooltip effect="dark"
                          popper-class="max-width-500 font-size-14"
                          :content="'IEP (Individualized Education Program) and lFSP (Individualized Family Service Plan) are plans for students requiring special education'"
                          placement="top">
                <div class="display-flex align-items">
                    <span style="font-size: 16px; font-weight: 400;" class="lg-icon lg-icon-question"></span>
                </div>
              </el-tooltip>
            </div>
            <div class="w-full">
              <el-radio-group v-model="personalizedForm.iep"
                @input="changePersonalizeForm">
                <el-radio :label="true">Yes</el-radio>
                <el-radio :label="false">No</el-radio>
              </el-radio-group>
              <el-select v-model="personalizedForm.characteristics"
                         multiple
                         @change="changePersonalizeForm"
                         :placeholder="'Please select special education eligibility'"
                         v-show="personalizedForm.iep" :teleported="false"
                         class="w-full">
                <el-option
                  v-for="item in childCharacteristics"
                  :key="item.name"
                  :label="item.displayName"
                  :value="item.displayName"
                >
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>
<script>
import Country from '@/assets/data/country.json'

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    Country
  },
  props: {
    childId: {
      type: String,
      default: ''
    },
    initPersonalizedForm: {
      type: Function,
      default: () => {
        return () => {
        }
      }
    },
    updatePersonalizeValidate: {
      type: Function,
      default: () => {
        return () => {
        }
      }
    },
    updatePersonalizeForm: {
      type: Function,
      default: () => {
        return () => {
        }
      }
    },
    isFromUnit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      attrLoading: false, // 属性加载状态
      // personalize to learner 表单
      personalizedForm: {
        language: [],
        race: [],
        iep: false,
        region: [],
        characteristics: [],
      },
      // 特殊教育特征
      childCharacteristics: [],
      // 语言
      childLanguageModel: [],
      // 种族
      childRaceModel: [],
      // 国家列表
      countries: Country.countries,
      // 属性列表
      attributeKeys: [],
      // 属性值列表
      attributes: [],
      // 属性类型
      attributeTypeValue: '',
      // 选中的属性
      selectedAttributeKey: '',
    }
  },

  created() {
  },
  watch: {
    childId: {
      handler: function (childId) {
        if (childId) {
          this.attrLoading = true
          this.getAttributes()
            .then(res => this.initSelectData(res))
            .catch(err => {
              this.attrLoading = false
            })
        }
      },
      immediate: true
    }
  },

  methods: {
    initSelectData(res) {
      // 初始化下拉框数据
      this.childCharacteristics = this.attributes.find(item => {
        return item.attributeKey === 'Special education eligibility'
      }).attributeValue
      // this.childCharacteristics 排序
      this.childCharacteristics.sort((a, b) => {
        return a.displayName.localeCompare(b.displayName)
      })
      // 初始化语言选择的下拉框数据
      this.childLanguageModel = this.attributes.find(item => item.attributeKey === 'Language').attributeValue
      // this.childLanguageModel 排序
      this.childLanguageModel.sort((a, b) => {
        return a.displayName.localeCompare(b.displayName)
      })
      // 初始化种族选择的下拉框数据
      this.childRaceModel = this.attributes.find(item => item.attributeKey === 'Race').attributeValue
      // this.childRaceModel 排序
      this.childRaceModel.sort((a, b) => {
        return a.displayName.localeCompare(b.displayName)
      })
      // 初始化表单
      this.personalizedForm = this.initPersonalizedForm()
      // 初始化表单验证
      this.changePersonalizeForm()
      this.attrLoading = false
    },
    // 获取小孩属性
    getAttributes () {
      // 如果没有小孩id，直接返回
      if (!this.childId || this.childId === '') {
        return
      }
      let params = {}
      params['childId'] = this.childId
      params['agencyId'] = ''
      this.attributeKeys = []
      this.attributes = []
      // 发起请求，获取小孩属性列表
     return new Promise((resolve, reject) => {
       this.$axios($api.urls().getEnrollmentAttrs, { params: params }).then(res => {
         // 将属性列表转换为下拉框数据
         res.forEach(item => {
           // 遍历属性列表，将属性名和属性值添加到下拉框数据中
           item.attrs.forEach(attr => {
             this.attributeKeys.push({
               label: attr.name,
               type: attr.typeValue,
               value: attr.name
             })
             // 将属性值添加到属性值列表中
             this.attributes.push({
               attributeKey: attr.name,
               attributeValue: attr.valueList
             })
           })
         })
         // 国家和州信息也需要添加到属性列表中
         this.attributeKeys.push({
           label: 'Place of Origin',
           type: 'SINGLE_CHOICE',
           value: 'Place of Origin'
         })
         this.attributeTypeValue = this.attributeKeys[0].type
         this.selectedAttributeKey = this.attributeKeys[0].value
         resolve(res)
       }).catch(err => {
         reject(err)
       })
      })
    },
    changePersonalizeForm() {
      // 设置表单验证状态
      let validate = false;
      // 如果有IEP，直接验证通过
      if (this.personalizedForm.iep) {
        validate = true;
      } else {
        // 如果有语言，种族，地区，特征，验证通过
        const hasLanguage = this.personalizedForm.language &&
          this.personalizedForm.language.length > 0;
        // 如果种族，地区，特征有一个不为空，验证通过
        const hasRace = this.personalizedForm.race &&
          this.personalizedForm.race.length > 0;
        const hasRegion = this.personalizedForm.region &&
          this.personalizedForm.region.length > 0;
        const hasCharacteristics = this.personalizedForm.characteristics &&
          this.personalizedForm.characteristics.length > 0;

        // 如果有非英语语言，种族，地区，特征，验证通过
        if (hasLanguage || hasRace || hasRegion || hasCharacteristics) {
          validate = true;
        }
      }
      // 向外界传递验证状态和表单数据
      this.updatePersonalizeForm(this.personalizedForm)
      this.updatePersonalizeValidate(validate)
    }
  }
}
</script>
<style lang="less" scoped>
.personalize-to-learn-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  background: #F5F6F8;
  border-radius: 8px;
  border: 1px #DCDFE6 solid;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header {
  align-self: stretch;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 6px;
}

.required {
  color: #F56C6C;
  font-size: 16px;
  font-family: Inter;
  font-weight: 600;
  line-height: 24px;
}

.title {
  color: #111C1C;
  font-size: 16px;
  font-family: Inter;
  font-weight: 600;
  line-height: 24px;
}

.subtitle {
  color: #111C1C;
  font-size: 14px;
  font-family: Inter;
  font-weight: 400;
  line-height: 22px;
}

.content {
  width: 100%;
  align-self: stretch;
  padding: 0px 16px 10px 16px;
  background: white;
  border-radius: 8px;
  border-top: 1px #DCDFE6 solid;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.question-content {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.question-text {
  color: #111C1C;
  font-size: 14px;
  font-family: Inter;
  font-weight: 600;
  line-height: 22px;
}

.input-box {
  align-self: stretch;
  height: 40px;
  padding: 9px 12px;
  background: white;
  border-radius: 4px;
  border: 1px #DCDFE6 solid;
  display: flex;
  align-items: center;
  gap: 4px;
}

.input-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
}

.placeholder {
  color: #676879;
  font-size: 14px;
  font-family: Inter;
  font-weight: 400;
  line-height: 22px;
}

.dropdown-icon {
  width: 20px;
  height: 20px;
  position: relative;
}

.dropdown-icon::before {
  content: "";
  width: 10px;
  height: 6px;
  position: absolute;
  left: 5px;
  top: 13px;
  background: #111C1C;
}

.el-card /deep/ & {

  & > .el-card__header {
    width: 100%;
    border-bottom: none;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 4px 4px 0 0;
  }

  & > .el-card__body {
    padding: 0;
    width: 100%;
    line-height: initial;
  }
}

</style>
