<template>
  <div>
    <div class="import-result-container">
      <!-- 成功提示 -->
      <div class="success-banner" v-if="successTip">
        <i class="el-icon-success"></i>
        <span>{{ $t('loc.importResultSuccessExtracted') }}</span>
      </div>

      <!-- 主要内容容器 - 整体边框 -->
      <div class="content-wrapper lg-scrollbar">
        <el-form
          ref="unitForm"
          :model="formData"
        >
          <div class="main-content-container">
            <div class="unit-header">
              <el-form-item prop="unitTitle" class="title-form-item">
                <el-input
                  v-model="formData.unitName"
                  :autosize="{ minRows: 1 }"
                  type="textarea"
                  class="unit-title-input input-text-no-border-resize"
                  :placeholder="$t('loc.importResultPleaseInputUnitTitle')"
                  :maxlength="200"
                  @input="formData.unitName = formData.unitName.trimStart()"
                >
                </el-input>
              </el-form-item>
              <!--年龄组-->
              <el-form-item prop="ageGroup">
                <template slot="label">
                  <div class="form-label">
                    {{ $t('loc.unitPlannerStep1Grade') }}
                  </div>
                </template>
                <el-select v-model="formData.grade" class="grade-select" :popper-append-to-body="false"
                           :placeholder="$t('loc.pleaseSelect')" @change="handleGradeChange">
                  <el-option v-for="item in ageGroups" :key="item.value" :label="item.name" :value="item.name"/>
                </el-select>
              </el-form-item>
            </div>

            <!-- 标准设置部分 -->
            <h3 class="section-title" style="margin-top: 0">{{ $t('loc.importResultStandardsAdjust') }}</h3>
            <div class="standards-section">
              <!-- 州标准选择 -->
              <div class="form-row table-framework">
                <label class="framework-row-label ">
                  <span class="lg-color-danger">*&nbsp;</span>
                  {{ $t('loc.unitPlannerStep1AssessmentFramework') }}
                </label>
                <div class="form-row-content">
                  <FrameworkSelector
                    v-model="frameworkData"
                    :selectedGrade="formData.grade"
                    :loading="confirmLoading"
                    :showLabel="false"
                    :isDataBackFill="isDataBackFill"
                    style="margin-bottom: 0;width: 100%"
                    @change="handleFrameworkChange"
                    @updateFrameworkName="updateFrameworkName"
                  />
                </div>
              </div>

              <!-- 科目选择 -->
              <el-form-item prop="standards"
                            :class="[isUnitModel ? 'table-subject' : 'table-subject-lesson', 'form-row-subject']"
              >
                <div slot="label"
                     :class="[isUnitModel ? '' : 'table-subject-lesson-border', 'table-subject-label']"
                >
                  <span class="lg-color-danger">*&nbsp;</span>
                  <span>{{ $t('loc.unitPlannerStep1Standards2') }}</span>
                </div>
                <!-- 按领域自动分配 -->
                <el-select
                  ref="domainSelect"
                  v-model="formData.standards"
                  multiple
                  style="width: 100%;"
                  :class="{ 'has-error': formErrorStandards }"
                  :placeholder="$t('loc.lessons2LessonDLLLanguageSelect')"
                  :loading="getDomainLoading"
                  @remove-tag="deleteDomain"
                  @visible-change="handleDropdownVisibleChange"
                  :popper-append-to-body="false"
                  class="lg-pointer import-result-domain-select"
                  popper-class="import-result-domain-select-dropdown">
                  <el-option-group>
                    <el-checkbox
                      v-model="domainSelectAll"
                      @change="handleSelectAllChange"
                      :indeterminate="selectAllIndeterminate"
                      class="domain-select-all w-full">
                      <span class="font-weight-600">{{ frameworkName }}</span>
                    </el-checkbox>
                  </el-option-group>
                  <el-option
                    v-for="item in subjectOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                    <el-checkbox
                      style="pointer-events: none;"
                      :value="formData.standards.includes(item.id)"
                      :label="item.name"
                    >
                    </el-checkbox>
                  </el-option>
                </el-select>
              </el-form-item>

              <!--课程时没有测评点，需要以领域作为表格结尾-->
              <el-form-item v-if="isUnitModel" prop="specifiedStandards"
                            class="table-measure form-row-measure">
                <div slot="label" class="table-measure-label">
                  <span>{{ $t('loc.importResultSpecifiedStandards') }}</span>
                </div>
                <MeasureRow
                  ref="measureRow"
                  :domains="subjectOptions"
                  :selectedMeasures=formData.unit.measureInfos
                  :unitBaseInfo="formData.unit.baseDomainInfo"
                  popoverPlacement="bottom-start"
                  :maxSelectMeasures="unitMaxMeasures"
                  :appendToBody="false"
                  :standardsViewScrollbarHeight="120"
                  :needFilterMeasures="true"
                  :selfPlaceholder="$t('loc.importResultSelectStandardsManually')"
                  @updateSelectedMeasure="(selectedMeasureIds, selectDomainIds) => updateUnitSelectedMeasure(selectedMeasureIds, selectDomainIds)">
                </MeasureRow>
              </el-form-item>
            </div>

            <!-- 可变部分 -->
            <div v-if="isUnitModel">
              <!-- 单元大概念部分 -->
              <div class="big-idea-section">
                <h3 class="section-title">{{ $t('loc.importResultUnitBigIdea') }}</h3>
                <el-form-item prop="bigIdea">
                  <el-input
                    v-model="formData.unit.bigIdea"
                    type="textarea"
                    :autosize="{ minRows: 3 }"
                    :maxlength="10000"
                    :placeholder="$t('loc.importResultUnitBigIdeaPlaceholder')"
                    class="input-text-no-border-resize">
                  </el-input>
                </el-form-item>
              </div>

              <!-- 单元目标部分 -->
              <div class="unit-objective-section">
                <h3 class="section-title">{{ $t('loc.importResultUnitObjective') }}</h3>
                <el-form-item prop="objectives">
                  <el-input
                    v-model="formData.unit.objectives"
                    type="textarea"
                    :autosize="{ minRows: 3}"
                    :maxlength="10000"
                    :placeholder="$t('loc.importResultUnitObjectivePlaceholder')"
                    class="input-text-no-border-resize">
                  </el-input>
                </el-form-item>
              </div>

              <!-- 周描述部分 -->
              <div class="other-descriptions-section">
                <h3 class="section-title">{{ $t('loc.importResultWeeklyDescriptions') }}</h3>
                <div v-if="!emptyWeek">
                  <div v-for="week in formData.weeks" :key="week.weekNumber">
                    <el-form-item :prop="'weekTheme' + week.weekNumber" class="week-form-row-theme" style="width: 100%">
                      <div class="week-theme-row">
                        <span>• </span>
                        <span v-if="week.weekNumber">{{ $t('loc.planWeek') }} {{ week.weekNumber }}:</span>
                        <el-input
                          v-model="week.weekTheme"
                          :maxlength="100"
                          :placeholder="$t('loc.importResultWeeklyTheme')"
                          style="flex: 1; "
                          class="input-normal-no-border">
                        </el-input>
                      </div>
                    </el-form-item>

                    <el-form-item :prop="'weekOverview' + week.weekNumber" class="week-form-row-overview">
                      <el-input
                        v-model="week.weekOverview"
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 10}"
                        :placeholder="$t('loc.importResultWeeklyOverview')"
                        class="input-text-no-border-resize">
                      </el-input>
                    </el-form-item>
                  </div>
                </div>
                <div v-if="emptyWeek">
                  <el-input
                    v-model="emptyWeekDesc"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 10}"
                    :placeholder="$t('loc.importResultNoWeeklyInfo')"
                    class="input-text-no-border-resize">
                  </el-input>
                </div>
              </div>
            </div>

            <!-- 课程 -->
            <div v-if="!isUnitModel">
              <div v-for="(lesson, index) in formData.lessons" :key="lesson.lessonNumber">
                <el-divider></el-divider>

                <el-form-item :prop="'lessonName' + lesson.lessonNumber" :label="$t('loc.importResultLessonPlanName')"
                              class="bold-label lesson-name-inline">
                  <el-input
                    :ref="'lessonNameInput' + lesson.lessonNumber"
                    v-model="lesson.lessonName"
                    :maxlength="200"
                    :minlength="2"
                    :class="{ 'has-error': lessonErrors.lessonName[lesson.lessonNumber] }"
                    :placeholder="$t('loc.importResultPleaseInputLessonName')"
                    class="input-normal-no-border"
                  />
                </el-form-item>

                <el-form-item :prop="'lessonMeasures' + lesson.lessonNumber"
                              :label="$t('loc.importResultSpecifiedStandards')"
                              class="bold-label form-row-measure lesson-measure lesson-measure-row">
                  <div class="display-flex align-items position-relative w-full lg-pointer">
                    <MeasureRow
                      :ref="'lessonMeasureRow' + index"
                      :key="'lessonMeasureRow-' + index + '-' + ((formData.lessons[index] && formData.lessons[index].measureInfos) ? formData.lessons[index].measureInfos.length : 0)"
                      :domains="subjectOptions"
                      :selectedMeasures=lesson.measureInfos
                      :unitBaseInfo="lesson.baseDomainInfo"
                      :maxSelectMeasures="8"
                      popoverPlacement="bottom-start"
                      :standardsViewScrollbarHeight="100"
                      :needFilterMeasures="true"
                      :appendToBody="false"
                      :selfPlaceholder="$t('loc.importResultSelectStandardsManually')"
                      @updateSelectedMeasure="(selectedMeasureIds, selectDomainIds) => updateLessonSelectedMeasure(selectedMeasureIds, selectDomainIds, lesson.lessonNumber-1)">
                    </MeasureRow>
                    <i class="el-icon-circle-plus-outline position-absolute font-size-18"
                       v-if="lesson.measureInfos && lesson.measureInfos.length > 0"
                       style="right: 6px; color: #10B3B7"
                       @click="openMeasureRowDropdown(index)"></i>
                  </div>
                </el-form-item>

                <el-form-item :prop="'lessonObjective' + lesson.lessonNumber"
                              :label="$t('loc.importResultLessonObjective')"
                              class="bold-label">
                  <el-input
                    :ref="'lessonObjectiveInput' + lesson.lessonNumber"
                    v-model="lesson.lessonObjective"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 10}"
                    :class="{ 'has-error': lessonErrors.lessonObjective[lesson.lessonNumber] }"
                    :placeholder="$t('loc.importResultPleaseInputLessonObjective')"
                    class="input-text-no-border-resize">
                  </el-input>
                </el-form-item>

                <el-form-item :prop="'lessonMaterials' + lesson.lessonNumber"
                              :label="$t('loc.importResultLessonMaterials')"
                              class="bold-label">
                  <el-input
                    :ref="'lessonMaterialsInput' + lesson.lessonNumber"
                    v-model="lesson.lessonMaterials"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 10}"
                    :class="{ 'has-error': lessonErrors.lessonMaterials[lesson.lessonNumber] }"
                    :placeholder="$t('loc.importResultPleaseInputLessonMaterials')"
                    class="input-text-no-border-resize">
                  </el-input>
                </el-form-item>

                <el-form-item :prop="'keyVocabularyWords' + lesson.lessonNumber"
                              :label="$t('loc.importResultKeyVocabularyWords')"
                              class="bold-label">
                  <el-input
                    :ref="'keyVocabularyWordsInput' + lesson.lessonNumber"
                    v-model="lesson.keyVocabularyWords"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 10}"
                    :class="{ 'has-error': lessonErrors.keyVocabularyWords[lesson.lessonNumber] }"
                    :placeholder="$t('loc.importResultPleaseInputKeyVocabulary')"
                    class="input-text-no-border-resize">
                  </el-input>
                </el-form-item>

                <el-form-item :prop="'implementationSteps' + lesson.lessonNumber"
                              :label="$t('loc.importResultImplementationSteps')"
                              class="bold-label">
                  <el-input
                    :ref="'implementationStepsInput' + lesson.lessonNumber"
                    v-model="lesson.implementationSteps"
                    type="textarea"
                    :autosize="{ minRows: 2}"
                    :maxlength="10000"
                    :class="{ 'has-error': lessonErrors.implementationSteps[lesson.lessonNumber] }"
                    :placeholder="$t('loc.importResultPleaseInputImplementationSteps')"
                    class="input-text-no-border-resize">
                  </el-input>
                </el-form-item>
              </div>
            </div>
          </div>

          <!-- 书籍链接和学生材料部分 -->
          <div class="materials-section">
            <div class="materials-header">
              <p class="section-title">{{ $t('loc.importResultBooksLinksAndMaterials') }}</p>
              <p class="materials-note">{{ $t('loc.importResultOriginalMaterialsPreserved') }}</p>
            </div>
            <el-form-item ref="materials">
              <template>
                <lesson-material-input class="form-materials" v-model="formData.materials" ref="lessonMaterialInput"
                                       :selfPlaceholder="$t('loc.importResultNoReferenceLinks')"
                                       :key="'import-unit-material-'+analysisResultId"
                                       :notShowUrlCard="false"
                                       :simple="true"/>
              </template>
            </el-form-item>
          </div>
        </el-form>
      </div>

    </div>

    <!-- 底部按钮区域 -->
    <div class="bottom-actions action-container">
      <AdaptImportFeedBack class="position-absolute"></AdaptImportFeedBack>

      <el-button type="primary" class="next-btn" size="medium" @click="handleSubmit" :loading="nextLoading">
        {{ $t('loc.unitPlannerStep1Next') }}
        <i class="el-icon-arrow-right"></i>
      </el-button>
    </div>

    <!-- 标准不匹配确认弹窗 -->
    <el-dialog
      :visible.sync="frameworkMismatchDialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :title="$t('loc.confirmation')"
      width="800px"
      custom-class="framework-mismatch-dialog"
      top="10vh">
      <div class="dialog-content">
        <!-- 警告提示 -->
        <div class="warning-banner">
          <i class="el-icon-warning"></i>
          <span>{{ $t('loc.importResultStandardsMismatchFound') }} ({{
              normalFrameworkData[3]
            }}).</span>
        </div>

        <!-- 说明文字 -->
        <p class="dialog-description">
          {{ $t('loc.importResultPleaseReviewAndSelect') }}
        </p>

        <!-- 选项区域 -->
        <div class="options-container">
          <!-- 选项1：应用选择的框架 -->
          <div class="option-card"
               :class="{ 'selected': selectedOption === 'apply' }"
               @click="selectedOption = 'apply'">
            <div class="option-header">
              <el-radio v-model="selectedOption" label="apply" class="option-radio">
                {{ $t('loc.importResultApplyMyFramework') }}
              </el-radio>
            </div>
            <div class="option-content">
              <div class="framework-display">
                {{ normalFrameworkData[3] || $t('loc.importResultUnknownFramework') }}
              </div>
            </div>
          </div>

          <!-- 选项2：保留检测到的框架 -->
          <div class="option-card"
               :class="{ 'selected': selectedOption === 'keep' }"
               @click="selectedOption = 'keep'">
            <div class="option-header">
              <el-radio v-model="selectedOption" label="keep" class="option-radio" @change="handleOptionChange">
                {{ $t('loc.importResultKeepDetectedFramework') }}
              </el-radio>
            </div>
            <div class="option-content">
              <div class="framework-display">
                {{ frameworkDataOcr[3] || $t('loc.importResultUnknownFramework') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 科目选择 -->
      <div class="keep-subject" v-if="selectedOption === 'keep'">
        <div class="form-label">
          <span class="required">*</span>
          {{ $t('loc.unitPlannerStep1Standards2') }}
        </div>
        <div v-if="isOlderThanKByOcr" class="form-description">
          {{ $t('loc.importResultSelectUpTo3Subjects') }}
        </div>
        <div class="form-row-content">
          <SimpleSubjectSelector ref="simpleSubjectSelector"
                                 v-model="domainIdsOcr"
                                 :frameworkName="frameworkDataOcr[3]"
                                 :selfSubjectOptions="subjectOptionsOcr"
                                 :ageName="gradeOcr"
          />
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="handleFrameworkConfirm"
          :disabled="frameworkConfirm"
          :loading="confirmLoading"
          class="confirm-btn">
          {{ $t('loc.importUnitStandConfirm') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import Lessons2 from '../../../../../../api/lessons2'
import { domainMax3, exampleImportUnit, PublicLessonAssistantInfoAgeGroup } from '../../../../../../utils/constants'
import FrameworkSelector from '../../../lessonLibrary/assistant/FrameworkSelector.vue'
import MeasureRow from '@/views/curriculum/designer/components/MeasureRow'
import LessonMaterialInput from '@/views/modules/lesson2/lessonLibrary/editor/lessonMaterialInput/index'
import AdaptImportFeedBack from '@/components/cg/posthog-survey/AdaptImportFeedBack.vue'
import SimpleSubjectSelector from '../../../lessonLibrary/assistant/SimpleSubjectSelector.vue'
import tools from '@/utils/tools'
import { equalsIgnoreCase } from '@/utils/common'

export default {
  name: 'ImportResult',
  components: { SimpleSubjectSelector, FrameworkSelector, MeasureRow, LessonMaterialInput, AdaptImportFeedBack },
  data () {
    return {
      leavedPage: false, // 是否离开页面
      isDataBackFill: false, // 是否数据回填
      analysisResultId: null, // 分析结果 ID
      formErrorStandards: false, // 学科必填检验
      emptyWeekDesc: null, // 空周填写内容
      // 课程信息校验
      lessonErrors: {
        lessonName: {},
        lessonObjective: {},
        lessonMaterials: {},
        keyVocabularyWords: {},
        implementationSteps: {}
      },
      domainSelectMeasured: ['MEASURE'], // 占位数据
      successTip: false, // 是否显示成功提示
      frameworkMismatch: false, // 是否显示框架确定
      isUnitModel: true, // 是否单元数据展示
      frameworkData: [], // 框架数据
      // 用户所选框架
      normalGrade: null,
      normalMeasureIds: null,
      frameworkId: null,
      frameworkName: null,
      normalFrameworkData: [],
      domainIds: [],
      // 文档提取框架
      gradeOcr: null,
      frameworkIdOcr: null,
      domainIdsOcr: [],
      measureIdsOcr: null,
      frameworkDataOcr: [],
      // 表单数据
      formData: {
        unitName: null, // 单元名称
        standards: [], // 学科
        grade: null, // 年龄

        // 单元数据
        unit: {
          bigIdea: null,
          objectives: null,
          measureInfos: [],
          measureIds: [],
          baseDomainInfo: {}
        },

        // 周数据
        weeks: [{
          weekNumber: null,
          weekTheme: null,
          weekOverview: null
        }],

        // 课程数据
        lessons: [{
          lessonNumber: 1,
          lessonName: null,
          lessonMeasures: [],
          measureInfos: [],
          lessonObjective: null,
          lessonMaterials: null,
          keyVocabularyWords: null,
          implementationSteps: null,
          baseDomainInfo: {}
        }],

        // 材料数据
        materials: {
          attachmentMedias: [],
          media: null,
          description: null
        }

      },
      // 对话框相关
      newSubject: '',
      newStandard: '',
      ageGroups: [...PublicLessonAssistantInfoAgeGroup].reverse(), // 年级选项
      confirmLoading: false, // 确认按钮加载状态
      getDomainLoading: false, // 学科加载状态
      domainSelectAll: false,
      subjectOptions: [], // 科目选项
      subjectOptionsOcr: [], // OCR 框架的领域
      frameworkMismatchDialogVisible: false, // 框架不匹配弹窗可见性
      selectedOption: 'apply', // 选择的选项 ('apply' 或 'keep')
      nextLoading: false
    }
  },
  async created () {
    this.$analytics.sendEvent('cg_adapt_analysis_entered')
    // 如果 VUEX 中没有数据，从路由中拿到值去后台查询
    if (!this.analyzeUnitContent) {
      const id = this.$route.params.id
      // 样例数据，不允许修改
      if (equalsIgnoreCase(id, exampleImportUnit.id)) {
        // 跳转首页
        this.toUnitList()
        return
      }
      if (id) {
        // 发请求获取数据
        await this.fetchDataById(id)
      }
    } else {
      // 样例数据，不允许修改
      if (equalsIgnoreCase(this.analyzeUnitContent.analysisResultId, exampleImportUnit.id)) {
        // 跳转首页
        this.toUnitList()
        return
      }
      await this.analyzeUnitContentToFormData(this.analyzeUnitContent)
    }
  },

  mounted () {
    // 组件挂载后清除表单校验状态，确保页面刷新时不会触发校验
    this.$nextTick(() => {
      this.clearLessonErrors()
    })
  },
  watch: {
    // 监听当前选中的学科
    'formData.standards': {
      deep: true,
      handler (newDomains, oldDomains) {
        this.formErrorStandards = false
        let back = false
        if (newDomains && newDomains.length > domainMax3 && this.isOlderThanK) {
          this.$message.warning(this.$t('loc.unitPlannerDomainMaxThree'))
          back = true
          this.formData.standards = oldDomains
        }

        // 修改学科全选状态
        this.domainSelectAll = (this.subjectOptions && this.formData.standards.length >= domainMax3 && this.isOlderThanK) ||
          (this.subjectOptions && this.formData.standards.length >= this.subjectOptions.length && this.formData.standards.length !== 0)

        if (back) {
          return
        }
        // 移除的领域 ID
        const removed = oldDomains.filter(id => !newDomains.includes(id))
        const removedSubjects = this.subjectOptions.filter(item =>
          removed.includes(item.id)
        )
        let allRemoveMeasures = []
        this.getMeasuresBottom(removedSubjects, allRemoveMeasures)
        const removedMeasureIds = allRemoveMeasures.map(item => item.id)

        // 测评点处理
        if (this.isUnitModel) {
          this.formData.unit.measureIds = this.formData.unit.measureIds.filter(
            id => !removedMeasureIds.includes(id)
          )
          this.formData.unit.measureInfos = this.allMeasures.filter(measures => this.formData.unit.measureIds.includes(measures.id))
            .sort((a, b) => a.sortIndex - b.sortIndex)
          this.formData.unit.baseDomainInfo = {
            domainIds: this.formData.standards,
            measureIds: this.domainSelectMeasured
          }
        } else {
          this.formData.lessons.map(lesson => {
            lesson.measureIds = lesson.measureIds.filter(
              id => !removedMeasureIds.includes(id)
            )
            lesson.measureInfos = this.allMeasures.filter(measures => lesson.measureIds.includes(measures.id))
              .sort((a, b) => a.sortIndex - b.sortIndex)
            lesson.baseDomainInfo = {
              domainIds: this.formData.standards,
              measureIds: this.domainSelectMeasured
            }
          })
        }
        // 领域能选的测评点同步
        this.processDataAfterFrameworkSelection()
      }
    }
  },
  computed: {
    ...mapState({
      analyzeUnitContent: state => state.curriculum.analyzeUnitContent // 存储的分析数据
    }),
    // 选择学科按钮是否全选的半选状态
    selectAllIndeterminate () {
      // 只要选项中有一个是选中的状态，则半选状态为 true
      return this.formData.standards && this.formData.standards.length > 0 && !this.domainSelectAll
    },
    // 当前框架下所有底层测评点
    allMeasures () {
      let measures = []
      this.getMeasuresBottom(this.subjectOptions, measures)
      return measures
    },
    frameworkConfirm () {
      if (this.selectedOption === 'apply') {
        return !this.domainIds || this.domainIds.length === 0
      } else {
        return !this.domainIdsOcr || this.domainIdsOcr.length === 0
      }
    },
    // 单元可选的测评点最大值
    unitMaxMeasures () {
      if (!this.formData.weeks) {
        return 10
      }
      const weekMeasure = this.formData.weeks.length * 10
      return weekMeasure <= 30 ? weekMeasure : 30
    },
    // 是否空的周
    emptyWeek () {
      if (!this.formData.weeks) {
        return true
      }

      if (this.formData.weeks.length > 0) {
        return this.formData.weeks.every(
          week => !week.weekTheme && !week.weekOverview
        )
      }

      return true
    },
    // 获取当前选择的年龄是否为 'K (5-6)' 及以上
    isOlderThanK () {
      // 获取当前选中的年级的详细数据，获取其中的 value 值
      if (!this.formData.grade) {
        return true
      }
      // 获取年龄值
      const ageValue = tools.getAgeValue(this.formData.grade)
      // 判断年龄是否为 'K (5-6)' 及以上
      return ageValue ? ageValue >= 6 : false
    },
    // OCR 当前选择的年龄是否为 'K (5-6)' 及以上
    isOlderThanKByOcr () {
      // 获取当前选中的年级的详细数据，获取其中的 value 值
      if (!this.gradeOcr) {
        return true
      }
      // 获取年龄值
      const ageValue = tools.getAgeValue(this.gradeOcr)
      // 判断年龄是否为 'K (5-6)' 及以上
      return ageValue ? ageValue >= 6 : false
    },
  },
  methods: {
    // 跳转列表页
    toUnitList () {
      this.$router.push({
        name: 'unit-planner-cg'
      })
    },

    // 更新单元所选测评点
    updateUnitSelectedMeasure (selectedMeasureIds, selectDomainIds) {
      const updateMeasure = this.allMeasures.filter(measures => selectedMeasureIds.includes(measures.id))
        .sort((a, b) => a.sortIndex - b.sortIndex)
      this.formData.unit.measureInfos = updateMeasure
      this.formData.unit.measureIds = updateMeasure.map(measure => measure.id)
    },
    // 更新测评点数据
    updateLessonSelectedMeasure (selectedMeasureIds, selectDomainIds, index) {
      const updateMeasure = this.allMeasures.filter(measures => selectedMeasureIds.includes(measures.id))
        .sort((a, b) => a.sortIndex - b.sortIndex)
      // 总是重新构造一个新数组，哪怕内容一样
      this.$set(this.formData.lessons[index], 'measureInfos', updateMeasure)
      this.$set(this.formData.lessons[index], 'measureIds', updateMeasure.map(measure => measure.id))
      this.$forceUpdate()
    },

    /**
     * 分析结果回填
     * @param content 结果
     * @returns {Promise<void>}
     */
    async analyzeUnitContentToFormData (content) {
      if (!content || !content.success) {
        return
      }
      this.successTip = true
      this.$store.commit('curriculum/SET_ANALYZE_UNIT_CONTENT', content)
      this.analysisResultId = content.analysisResultId

      // 框架三级信息回填
      let someMeasureIds = []
      if (!content.frameworkMismatch) {
        this.isDataBackFill = true
        this.frameworkId = content.frameworkId
        await this.fetchMeasuresFromBackend(this.frameworkId)
        this.formData.standards = content.domainIds
        this.frameworkName = content.frameworkData[3]
        // 根据国家区分层级
        if (tools.isTwoLevelCountry(content.frameworkData[0])) {
          this.frameworkData = [content.frameworkData[0], content.frameworkData[2]]
        } else {
          this.frameworkData = content.frameworkData.slice(0, 3)
        }
        this.formData.grade = content.grade
        // 测评点
        let someMeasures = []
        const subjects = this.subjectOptions.filter(item =>
          this.formData.standards.includes(item.id)
        )
        this.getMeasuresBottom(subjects, someMeasures)
        someMeasureIds = someMeasures.map(item => item.id)
      } else {
        this.frameworkId = content.frameworkId
        this.domainIds = content.domainIds
        this.normalFrameworkData = content.frameworkData
        this.normalGrade = content.grade
        this.normalMeasureIds = content.measureIds
        this.fetchMeasuresFromBackend(this.frameworkId)

        this.frameworkIdOcr = content.frameworkIdOcr
        this.frameworkDataOcr = content.frameworkDataOcr
        this.domainIdsOcr = content.domainIdsOcr || []
        this.gradeOcr = content.gradeOcr
        this.measureIdsOcr = content.measureIdsOcr
        // 同步加载用于框架确认弹窗领域回显
        await this.fetchMeasuresFromBackend(this.frameworkIdOcr, 'subjectOptionsOcr')
      }

      // 单元信息
      if (content.type === 'UNIT' && content.unitContent) {
        this.formData.unitName = content.unitContent.unitName
        this.formData.unit.bigIdea = content.unitContent.unitOverview
        this.formData.unit.objectives = content.unitContent.unitObjective
        if (content.weekDataList && content.weekDataList.length > 0) {
          this.formData.weeks = content.weekDataList
        }
        this.formData.unit.measureIds = content.measureIds
        if (!content.frameworkMismatch) {
          // 测评点等先过滤
          this.formData.unit.measureIds = this.formData.unit.measureIds.filter(
            id => someMeasureIds.includes(id)
          )
          this.formData.unit.measureInfos = this.allMeasures.filter(measures => this.formData.unit.measureIds.includes(measures.id))
            .sort((a, b) => a.sortIndex - b.sortIndex)
        }
        this.formData.unit.baseDomainInfo = {
          domainIds: this.formData.standards,
          measureIds: this.domainSelectMeasured
        }
      }
      // 课程信息
      if (content.type === 'LESSON') {
        this.isUnitModel = false
        if (content.lessonDataList && content.lessonDataList.length > 0) {
          this.formData.lessons = content.lessonDataList.map(lesson => {
            if (!content.frameworkMismatch) {
              // 测评点等先过滤
              lesson.measureIds = lesson.measureIds.filter(
                id => someMeasureIds.includes(id)
              )
              lesson.measureInfos = this.allMeasures.filter(measures => lesson.measureIds.includes(measures.id))
                .sort((a, b) => a.sortIndex - b.sortIndex)
            }
            return {
              ...lesson,
              baseDomainInfo: {
                domainIds: this.formData.standards,
                measureIds: this.domainSelectMeasured
              }
            }
          })
        }
      }
      // 材料更新
      this.$nextTick(() => {
        if (this.$refs.lessonMaterialInput) {
          this.$refs.lessonMaterialInput.updateDescription(content.materialUrls)
        }
      })
      // 数据加载完成后清除表单校验状态，避免页面刷新时触发校验
      this.$nextTick(() => {
        this.clearLessonErrors()
        this.isDataBackFill = false
      })

      // 打开框架确认弹窗
      if (content.frameworkMismatch) {
        this.$analytics.sendEvent('cg_adapt_conflict_popup_shown')
        this.showFrameworkMismatchDialog()
      }
    },

    /**
     * 根据 ID 查询解析数据
     * @param id 解析数据 ID
     * @returns {Promise<void>}
     */
    async fetchDataById (id) {
      try {
        const response = await Lessons2.getAnalyzeContentById(id)
        if (!response.success) {
          return
        }
        await this.analyzeUnitContentToFormData(response)
      } catch (error) {
        console.error(error)
      }
    },

    // 获取底层测评点
    getMeasuresBottom (children, measuresBottom) {
      if (!children || children.length === 0) {
        return
      }
      children.forEach(v => {
        const childrenNext = v.children
        if (!childrenNext || childrenNext.length === 0) {
          measuresBottom.push(v)
        } else {
          this.getMeasuresBottom(childrenNext, measuresBottom)
        }
      })
    },

    // 全选/取消全选
    handleSelectAllChange (checked) {
      // 如果当前已经达到限制，点击全选按钮应取消所有选择。否则按照顺序选择 3 个
      if (!checked) {
        this.formData.standards = []
      } else {
        // 距离达到限制缺少的个数
        let lackCount = domainMax3 - this.formData.standards.length
        // 按照顺序选择缺少的 domain
        for (let i = 0; i < this.subjectOptions.length; i++) {
          // 如果当前学科未选择
          if (!this.formData.standards.includes(this.subjectOptions[i].id)) {
            // 未选择则选择
            this.formData.standards.push(this.subjectOptions[i].id)
            // 如果不存在缺少的则退出
            if (this.isOlderThanK) {
              if (--lackCount <= 0) {
                break
              }
            }
          }
        }
      }
    },
    // 点击学科弹窗时关闭测评点弹窗
    handleDropdownVisibleChange () {
      if (this.$refs.measureRow) {
        this.$refs.measureRow.closePopover()
      }
    },
    // 删除学科
    deleteDomain (domainId) {
      // 更新 domainSelected 数组
      this.formData.standards = this.formData.standards.filter(item => item !== domainId)
      if (this.formData.standards.length === 0) {
        this.domainSelectAll = false
      }
    },
    async handleOptionChange () {
      // 一定是三个元素，此时直接获取框架 ID 正常
      await this.fetchMeasuresFromBackend(this.frameworkDataOcr[2])
    },
    // 更新框架名称
    updateFrameworkName (frameworkName) {
      this.frameworkName = frameworkName
    },
    // 框架选择变化时的处理函数
    handleFrameworkChange (data) {
      if (this.frameworkData.length === 0) {
        return
      }
      let standardId
      if (this.frameworkData.length === 2) {
        standardId = this.frameworkData[1]
      } else {
        standardId = this.frameworkData[2]
      }
      if (this.frameworkId === standardId) {
        return
      }
      this.frameworkId = standardId
      // 当框架选择发生变化时，清空学科选择
      this.formData.standards = []
      if (this.isUnitModel) {
        this.formData.unit.measureInfos = []
        this.formData.unit.measureIds = []
      }
      // 根据框架查询学科数据
      this.fetchMeasuresFromBackend(standardId)
    },
    /**
     * 从后端获取测评点数据, 并处理测评点数据
     *
     * @param frameworkId 框架ID
     * @param targetKey 目标字段
     */
    async fetchMeasuresFromBackend (frameworkId, targetKey = 'subjectOptions') {
      this.getDomainLoading = true
      this[targetKey] = []
      try {
        const measures = await this.$store.dispatch('curriculum/getFrameworkDomains', {
          frameworkId,
          compress: false
        })

        this[targetKey] = measures
      } catch (error) {
      } finally {
        this.$nextTick(() => {
          this.getDomainLoading = false
        })
      }
    },

    // 年级选择变化时的处理函数
    handleGradeChange () {
      // 清空学科选择
      this.formData.standards = []
    },

    // 表单提交处理
    handleSubmit () {
      // 清除所有错误状态
      this.formErrorStandards = false
      this.clearLessonErrors()

      // 学科校验
      if (!this.formData.standards || this.formData.standards.length === 0) {
        this.formErrorStandards = true
        this.$nextTick(() => {
          const domainSelect = this.$refs.domainSelect
          if (domainSelect && domainSelect.$el && domainSelect.$el.scrollIntoView) {
            domainSelect.$el.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            })
          }
        })
        return
      }

      // 课程数据校验
      // const noPass = this.checkLessonData()
      // if (noPass) {
      //   return
      // }
      this.$analytics.sendEvent('cg_adapt_analysis_next_clicked')
      // 执行提交逻辑
      this.saveFormDataToBackend()
    },

    // 检查课程数据有效性
    checkLessonData () {
      // 如果是课程模式，校验每个 lesson
      if (!this.isUnitModel) {
        let hasError = false
        let firstErrorField = null

        this.formData.lessons.forEach((lesson, index) => {
          const lessonNumber = lesson.lessonNumber

          // 校验课程名称
          if (!lesson.lessonName || lesson.lessonName.trim() === '') {
            this.lessonErrors.lessonName[lessonNumber] = true
            hasError = true
            if (!firstErrorField) {
              firstErrorField = `lessonName${lessonNumber}`
            }
          }

          // 校验课程目标
          if (!lesson.lessonObjective || lesson.lessonObjective.trim() === '') {
            this.lessonErrors.lessonObjective[lessonNumber] = true
            hasError = true
            if (!firstErrorField) {
              firstErrorField = `lessonObjective${lessonNumber}`
            }
          }

          // 校验课程材料
          if (!lesson.lessonMaterials || lesson.lessonMaterials.trim() === '') {
            this.lessonErrors.lessonMaterials[lessonNumber] = true
            hasError = true
            if (!firstErrorField) {
              firstErrorField = `lessonMaterials${lessonNumber}`
            }
          }

          // 校验关键词汇
          if (!lesson.keyVocabularyWords || lesson.keyVocabularyWords.trim() === '') {
            this.lessonErrors.keyVocabularyWords[lessonNumber] = true
            hasError = true
            if (!firstErrorField) {
              firstErrorField = `keyVocabularyWords${lessonNumber}`
            }
          }

          // 校验实施步骤
          if (!lesson.implementationSteps || lesson.implementationSteps.trim() === '') {
            this.lessonErrors.implementationSteps[lessonNumber] = true
            hasError = true
            if (!firstErrorField) {
              firstErrorField = `implementationSteps${lessonNumber}`
            }
          }
        })

        if (hasError) {
          // 滚动到第一个错误项
          this.$nextTick(() => {
            this.scrollToFirstError(firstErrorField)
          })
          return true
        } else {
          return false
        }
      }
    },

    // 保存数据
    async saveFormDataToBackend () {
      this.nextLoading = true
      let content = {
        analysisResultId: this.analysisResultId,
        unitContent: {
          unitName: this.formData.unitName,
          unitOverview: this.formData.unit.bigIdea,
          unitObjective: this.formData.unit.objectives
        },
        weekDataList: this.formData.weeks,
        lessonDataList: this.formData.lessons,
        grade: this.formData.grade,
        frameworkId: this.frameworkId,
        domainIds: this.formData.standards,
        measureIds: this.isUnitModel ? this.formData.unit.measureIds : [],
        type: this.analyzeUnitContent.type,
        measureModified: true,
        enhanceMode: this.analyzeUnitContent.enhanceMode
      }
      // 空周时不做数据更新
      if (this.emptyWeekDesc && this.emptyWeek) {
        content.weekDataList = []
      }
      try {
        // 非仅深度的需要校验学科
        if (this.analyzeUnitContent.enhanceMode !== 'DEEP') {
          const res = await Lessons2.checkSubjects(content)
          if (!res || !res.success) {
            this.$message.error(this.$t('loc.importResultSubjectNotFit'))
            this.nextLoading = true
            return
          } else {
            const idList = JSON.parse(res.id)
            content.domainIds = idList
            // 移除的领域 ID
            const removed = idList.filter(id => !content.domainIds.includes(id))
            const removedSubjects = this.subjectOptions.filter(item =>
              removed.includes(item.id)
            )
            let allRemoveMeasures = []
            this.getMeasuresBottom(removedSubjects, allRemoveMeasures)
            const removedMeasureIds = allRemoveMeasures.map(item => item.id)
            content.measureIds = content.measureIds.filter(
              id => !removedMeasureIds.includes(id)
            )
          }
        }
        // 数据保存
        const result = await Lessons2.saveUserChangeAnalyzeResult(content)
        // 将数据存入 VUEX 在结果页使用
        this.$store.commit('curriculum/SET_ANALYZE_UNIT_CONTENT', result)
        if (this.leavedPage) {
          return
        }
        this.$router.push({
          name: 'adapt-select',
          params: {
            id: result.analysisResultId
          }
        })
        // 到下个页面
      } catch (error) {
      } finally {
        this.nextLoading = false
      }
    },

    // 清除 lesson 错误状态
    clearLessonErrors () {
      this.lessonErrors = {
        lessonName: {},
        lessonObjective: {},
        lessonMaterials: {},
        keyVocabularyWords: {},
        implementationSteps: {}
      }
    },

    // 滚动到第一个错误项
    scrollToFirstError (errorField) {
      if (!errorField) return

      // 解析错误字段，获取 lessonNumber 和字段类型
      const match = errorField.match(/^(\w+)(\d+)$/)
      if (!match) return

      const [, fieldType, lessonNumber] = match

      // 构建 ref 名称
      const refName = `${fieldType}Input${lessonNumber}`

      // 通过 ref 获取对应的输入框
      const inputRef = this.$refs[refName]
      if (inputRef && inputRef[0] && inputRef[0].$el) {
        inputRef[0].$el.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    },

    // 显示框架不匹配弹窗
    showFrameworkMismatchDialog () {
      this.frameworkMismatchDialogVisible = true
    },

    // 处理框架不匹配确认
    async handleFrameworkConfirm () {
      this.confirmLoading = true
      this.isDataBackFill = true
      try {
        if (this.selectedOption === 'apply') {
          this.$analytics.sendEvent('cg_adapt_conflict_use_selected')
          // 使用当前选择的框架
          this.formData.grade = this.normalGrade
          this.formData.standards = this.domainIds || []
          this.frameworkName = this.normalFrameworkData[3]
          // 根据国家区分层级
          if (tools.isTwoLevelCountry(this.normalFrameworkData[0])) {
            this.frameworkData = [this.normalFrameworkData[0], this.normalFrameworkData[2]]
          } else {
            this.frameworkData = this.normalFrameworkData.slice(0, 3)
          }
          if (this.isUnitModel) {
            this.formData.unit.measureIds = this.normalMeasureIds || []
          }
        } else {
          this.$analytics.sendEvent('cg_adapt_conflict_use_detected')
          // 使用检测到的框架
          this.frameworkId = this.frameworkIdOcr
          this.frameworkName = this.frameworkDataOcr[3]
          this.formData.standards = this.domainIdsOcr || []
          // 根据国家分层
          if (tools.isTwoLevelCountry(this.frameworkDataOcr[0])) {
            this.frameworkData = [this.frameworkDataOcr[0], this.frameworkDataOcr[2]]
          } else {
            this.frameworkData = this.frameworkDataOcr.slice(0, 3)
          }
          this.formData.grade = this.gradeOcr
          if (this.isUnitModel) {
            this.formData.unit.measureIds = this.measureIdsOcr || []
          }
        }
        this.$analytics.sendEvent('cg_adapt_conflict_confirm_clicked')
        // 重新加载测评点数据
        await this.fetchMeasuresFromBackend(this.frameworkId)
        // 先关弹窗，防止框架信息同步出现 Unknown Name
        this.frameworkMismatchDialogVisible = false
        // 标准数据同步
        this.processDataAfterFrameworkSelection()
        // 回填结束
        this.$nextTick(() => {
          this.isDataBackFill = false
        })
      } catch (error) {
      } finally {
        this.confirmLoading = false
      }
    },

    // 框架选择后根据领域能选的测评点数据同步
    processDataAfterFrameworkSelection () {
      // 单元信息
      if (this.isUnitModel) {
        this.formData.unit.measureInfos = this.allMeasures.filter(measures => this.formData.unit.measureIds.includes(measures.id))
          .sort((a, b) => a.sortIndex - b.sortIndex)
        this.formData.unit.baseDomainInfo = {
          domainIds: this.formData.standards,
          measureIds: this.domainSelectMeasured
        }
      } else {
        this.formData.lessons.map(lesson => {
          lesson.measureInfos = this.allMeasures.filter(measures => lesson.measureIds.includes(measures.id))
            .sort((a, b) => a.sortIndex - b.sortIndex)
          lesson.baseDomainInfo = {
            domainIds: this.formData.standards,
            measureIds: this.domainSelectMeasured
          }
        })
      }
    },
    // 打开课程测评点选择
    openMeasureRowDropdown (index) {
      const lessonMeasureRowRef = this.$refs['lessonMeasureRow' + index]
      if (lessonMeasureRowRef && lessonMeasureRowRef[0]) {
        lessonMeasureRowRef[0].showDropdown()
      }
    }
  },
  beforeDestroy () {
    this.leavedPage = true
  },
}
</script>
<style lang="less" scoped>
.import-result-container {
  height: calc(100vh - 214px);
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 关键！ */
  width: 95%;
  margin: 8px auto;
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.10);
}

.content-wrapper {
  overflow-y: auto;
  height: 100%;
  background-color: #FFFFFF;
  box-shadow: -4px 0 6px -4px rgba(0, 0, 0, 0.1), /* 左侧阴影 */ 4px 0 6px -4px rgba(0, 0, 0, 0.1), /* 右侧阴影 */ 0 4px 6px -2px rgba(0, 0, 0, 0.1); /* 下方阴影 */
  padding-top: 10px;
  padding-bottom: 10px;
}

/* 成功提示样式 */
.success-banner {
  border-radius: 4px 0px 0px 0px;
  background: #F0F9EB;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #67C23A;
  font-size: 16px;
  font-weight: 600;
}

.success-banner .el-icon-success {
  color: #28a745;
  font-size: 18px;
}

/* 主要内容容器样式 - 整体边框 */
.main-content-container {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 14px;
  margin: 20px 60px 20px 60px;
  flex: 1 1 auto;
  overflow-y: hidden;
}

/* 单元标题区域 */
.unit-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.title-form-item {
  margin-bottom: 0 !important;
}

.unit-title-input /deep/ .el-textarea__inner {
  font-size: 24px;
  font-weight: 600;
  line-height: 30px; /* 116.667% */
  padding: 4px 2px; /* 上下4px，左右2px */
  min-height: 40px;
}

.input-normal-no-border /deep/ .el-input__inner {
  resize: none;
  outline: none !important;
  box-shadow: none !important;
  transition: border-color 0.1s ease;
  /* 基础样式：使用 1px 的透明边框，确保边框宽度始终存在 */
  border: 1px dashed transparent;
}

.input-normal-no-border /deep/ .el-input__inner:hover {
  border-color: #10B3B7;
  outline: none !important;
  box-shadow: none !important;
}

.input-normal-no-border /deep/ .el-input__inner:focus {
  border-color: #10B3B7;
  outline: none !important;
  box-shadow: none !important;
}

.grade-form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  align-self: flex-start;
}

.grade-label {
  font-weight: 500;
  color: #666;
  margin-right: 12px;
}

.grade-select {
  width: 180px;
}

/* 部分标题样式 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 标准设置部分 */
.standards-section {
  margin-bottom: 32px;
  padding-left: 12px;
  border-radius: 8px;

  .table-framework {
    border-top: 1px solid #dcdfe6;
    border-left: 1px solid #dcdfe6;
    border-right: 1px solid #dcdfe6;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-bottom: 0 !important;
    gap: 10px;

    /deep/ .el-input__inner {
      border-top-right-radius: 8px;
    }

    .framework-row-label {
      background: #FAFAFA;
      border-top-left-radius: 8px;
      border-right: 1px solid #dcdfe6;
      margin-bottom: 0;
      width: 200px;
      height: 42px;
      color: #111C1C;
      flex-shrink: 0;
      /* Semi Bold/14px */
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
  }

  .table-subject-lesson {
    margin-bottom: 0 !important;
    border: 1px solid #dcdfe6;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    align-items: stretch; /* 确保 label 和 content 同高 */
  }

  .table-subject-lesson-border {
    background: #FAFAFA;
    border-bottom-left-radius: 8px;
    border-right: 1px solid #dcdfe6;
  }

  .table-subject {
    border: 1px solid #dcdfe6;
    margin-bottom: 0 !important;
    align-items: stretch; /* 确保 label 和 content 同高 */
  }

  .table-subject-label {
    background: #FAFAFA;
    border-right: 1px solid #dcdfe6;
    margin: 0;
    width: 200px;
    height: auto;
    color: #111C1C;
    flex-shrink: 0;
    /* Semi Bold/14px */
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .table-measure {
    border-left: 1px solid #dcdfe6;
    border-right: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    align-items: stretch; /* 确保 label 和 content 同高 */

    /deep/ .el-form-item__content {
      width: 100%;
      margin-left: 17px !important;
      cursor: pointer;
    }

    /deep/ .active-border {
      border-color: transparent !important;
      font-size: 14px;
    }

    /deep/ .measures-font-focus {
      color: #676879;
    }

    /deep/ .el-input__inner {
      border: 1px solid #dcdfe6;
    }
  }

  .import-result-domain-select {
    /deep/ .el-tag {
      margin: 2px 6px 2px 0px !important;
      color: #676879;

      i {
        background: none;

        &:hover {
          color: #909399 !important;
        }
      }
    }
  }

  .table-measure-label {
    background: #FAFAFA;
    border-bottom-left-radius: 8px;
    border-right: 1px solid #dcdfe6;
    margin: 0;
    padding: 0;
    width: 200px;
    height: auto;
    color: #111C1C;
    flex-shrink: 0;
    /* Semi Bold/14px */
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
}

.table-framework /deep/ .el-input__suffix {
  display: none;
}

.table-subject-lesson /deep/ .el-form-item__label {
  margin: 0;
  padding: 0;
  display: flex;
}

.table-subject-lesson /deep/ .el-input__suffix {
  display: none;
}

.table-subject-lesson /deep/ .el-form-item__content {
  margin-left: 24px;
}

.table-subject-lesson /deep/ .el-input__inner {
  padding: 0;
}

.table-subject /deep/ .el-form-item__label {
  margin: 0;
  padding: 0;
  display: flex;
}

.table-subject /deep/ .el-input__suffix {
  display: none;
}

.table-subject /deep/ .el-form-item__content {
  margin-left: 24px;
}

.table-subject /deep/ .el-input__inner {
  padding: 0;
}

.table-measure /deep/ .el-form-item__label {
  margin: 0;
  padding: 0;
  display: flex;
}

.table-measure /deep/ .el-form-item__content {
  margin-left: 24px;
}

.standards-section /deep/ .el-input__inner {
  border: none;
}

.form-row-subject {
  width: 100%;
  display: flex;
  align-items: flex-start;
}

.form-row-subject /deep/ .el-form-item__content {
  flex: 1;
}

.form-row-measure {
  display: flex;
  align-items: flex-start;
}

.form-row-measure {
  /deep/ .measure {
    min-height: 35px;
    padding: 5px 25px 5px 5px;
  }
}

.week-form-row-theme {
  display: flex;
  align-items: flex-start;
  margin-left: 8px;
}

.week-form-row-theme /deep/ .el-form-item__label {
  padding: 0;
}

.week-form-row-theme /deep/ .el-form-item__content {
  width: 100%;
}

.other-descriptions-section /deep/ .el-form-item {
  margin-bottom: 0;
}

.lesson-measure {
  display: flex;
  align-items: center;
  text-align: center;
}

.lesson-measure /deep/ .el-form-item__label {
  margin: 0;
}

.lesson-measure-row {
  text-align: left;

  /deep/ .el-form-item__content {
    max-width: 80%;
  }

  /deep/ .measure {
    border-radius: 4px;
  }
}

.week-theme-row {
  display: flex;
  align-items: center;
  gap: 8px; /* 控制间距，可根据实际调整 */
}

.week-form-row-overview {
  margin-left: 13px;
}

.week-form-row-overview /deep/ .el-textarea__inner {
  margin-left: 10px;
}

/* 表单行样式 - 标签和内容同行 */
.form-row {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row-content {
  flex: 1;
}

/* 必填项的红色星号 */
.required {
  color: #f56c6c;
}

.keep-subject {
  margin-top: 12px;

  .form-label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.5;
    color: #111C1C;

    .required {
      color: #f56c6c;
    }
  }

  .form-description {
    font-size: 14px;
    font-weight: 400;
    line-height: 1.429;
    color: #676879;
    margin-bottom: 8px;
    font-style: italic;
  }
}

.standards-select {
  width: 250px;
}

.subjects-tags,
.specified-standards {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  flex: 1;
}

.add-subject-btn,
.add-standard-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  border: 1px dashed #d9d9d9;
  background: transparent;
}

/* 大概念部分 */
.big-idea-section {
  margin-bottom: 32px;
}

/* 单元目标部分 */
.unit-objective-section {
  margin-bottom: 32px;
}

.bold-label /deep/ .el-form-item__label {
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px; /* 150% */
}

.input-text-no-border-resize /deep/ .el-textarea__inner {
  resize: none;
  outline: none !important;
  box-shadow: none !important;
  transition: border-color 0.1s ease;
  /* 基础样式：使用 1px 的透明边框，确保边框宽度始终存在 */
  border: 1px dashed transparent;
}

.input-text-no-border-resize /deep/ .el-textarea__inner:hover {
  border-color: #10B3B7; /* 灰色虚线边框 */
  outline: none !important;
  box-shadow: none !important;
}

.input-text-no-border-resize /deep/ .el-textarea__inner:focus {
  border-color: #10B3B7;
  outline: none !important;
  box-shadow: none !important;
}

/* 材料部分 */
.materials-section {
  margin: 20px 60px 0px 60px;

  .materials-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  .materials-header .section-title {
    margin-bottom: 0;
  }

  .materials-note {
    color: #666;
    margin: 0 0 0 10px;
    font-style: italic;
    font-size: 14px;
  }

  .form-materials {
    overflow-y: auto;

    /deep/ .lesson-material-card {
      width: 80%;

      .lesson-material-card-site {
        width: 100%;
        border: 1px solid #DCDFE6;
      }

      .lesson-material-card-link {
        width: 100%;
        height: 56px;
        background: #FAFAFA;
      }

      .lesson-material-card-link-text {
        max-width: calc(100% - 120px);
        gap: 2px;
      }

      .editor-card-link-describe {
        font-weight: 400;
      }

      .lesson-material-card-close {
        background-color: #999;
      }

      .editor-card-link-describe {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .lesson-material-card-link-icon {
        max-height: 56px;
        min-width: unset;
        height: unset;
        min-height: unset;
      }
    }
  }
}

/* 建议加在 <style scoped> 中，或带上父级类名限制范围 */
.no-required-star /deep/ .el-form-item__label::before {
  display: none;
}

/* 底部按钮区域 */
.bottom-actions {
  height: 60px; /* 你可以根据实际需求调整 */
  display: flex;
  align-items: center;
  width: 95%;
  margin: auto;
}

.next-btn {
  height: 40px;
  width: 200px;
  background: #10B3B7;
  border-color: #10B3B7;
  padding: 12px 32px;
  font-size: 16px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.next-btn:hover {
  background: #10B3B7;
  border-color: #10B3B7;
}

/* Element UI 表单项自定义样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  font-weight: 500;
  color: #666;
}

/* Element UI 标签自定义样式 */
.el-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.el-tag--info {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;
}

/* 对话框样式 */
.dialog-footer {
}

.lesson-name-inline {
  display: flex;
  align-items: center;
}

.lesson-name-inline /deep/ .el-input__inner {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  padding: 0;
  margin-bottom: 4px;
}

.lesson-name-inline .el-form-item__label {
  min-width: 120px;
  margin-right: 12px;
  font-weight: bold;
}

.lesson-name-inline /deep/ .el-form-item__content {
  flex: 1;
}

/* 框架不匹配弹窗样式 */
.framework-mismatch-dialog /deep/ .el-dialog {
  border-radius: 8px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
}

/deep/ .framework-mismatch-dialog .el-dialog__title {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px; /* 130% */
}

/deep/ .framework-mismatch-dialog .el-dialog__body {
  padding: 10px 20px 24px 20px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.warning-banner {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 9px 16px;
  background: #FCF6EC;
  border: 1px solid rgba(230, 162, 60, 0.3);
  border-radius: 4px;
}

.warning-banner .el-icon-warning {
  color: #E6A23C;
  font-size: 20px;
  flex-shrink: 0;
}

.warning-banner span {
  color: #E6A23C;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.dialog-description {
  color: #111C1C;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.options-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center; /* 可选，看是否要垂直居中 */
}

.options-container > :first-child {
  margin-right: auto; /* 推动右边元素靠右 */
}

.options-container > :last-child {
  margin-left: 16px; /* 形成 16px 的间距 */
}

.option-card {
  flex: 1;
  background: #FAFAFA;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.option-card.selected {
  background: #F0F9FF;
  border: 1px solid #10B3B7;
}

.option-header {
  margin-bottom: 8px;
}

.option-radio /deep/ .el-radio__label {
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5em;
  color: #111C1C;
}

.option-content {
  padding-left: 26px;
}

.framework-display {
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714285714em;
  color: #111C1C;
  padding: 9px 12px;
  min-height: 40px;
  display: block;
  background: #FFFFFF;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
  box-sizing: border-box;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  gap: 12px;
  height: 40px;
}

.confirm-btn {
  background: #10B3B7;
  border-color: #10B3B7;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.5714285714285714em;
  padding: 8px 12px;
  height: 40px;
  border-radius: 4px;
}

.confirm-btn:hover {
  background: #0EA5A9;
  border-color: #0EA5A9;
}

/* 错误状态样式 */
.has-error /deep/ .el-input__inner,
.has-error /deep/ .el-textarea__inner {
  border-color: #F56C6C !important;
  box-shadow: 0 0 0 1px #f56c6c !important;
}

.has-error /deep/ .el-input__inner:focus,
.has-error /deep/ .el-textarea__inner:focus {
  border-color: #F56C6C !important;
  box-shadow: 0 0 0 1px #f56c6c !important;
}

</style>

<style lang="less">
.import-result-domain-select-dropdown {
  min-width: 300px !important;
  padding: 0;

  /* 调整边距大小 */

  .el-select-dropdown__list {
    padding: 12px 12px 12px 0;
  }

  .el-select-group__wrap {
    padding-left: 16px;
  }

  .el-select-dropdown__item {
    padding-left: 26px;

    .el-checkbox {
      margin: 0;
    }
  }
}
</style>
