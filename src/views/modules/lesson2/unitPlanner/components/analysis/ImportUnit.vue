<template>
  <div class="import-unit-container">
    <!-- 标题 -->
    <div class="title-section">
      <div class="position-relative">
        <span class="title-section-1">{{ $t('loc.importUnitTitle') }}</span>
        <div
          class="font-size-14 font-weight-600 height-20 line-height-20 position-absolute add-padding-lr-6 font-color-whiteing"
          style="background-color: #F56C6C; border-radius: 10px; right: -50px; top: 3px">Beta
        </div>
      </div>
      <span class="title-section-2">{{ $t('loc.importUnitTitleDesc') }}</span>
    </div>
    <!-- 主要内容卡片 -->
    <div class="main-card">
      <!-- Google Docs URL 部分 -->
      <div class="google-docs-section">
        <div class="section-title">
          <span class="title-text">{{ $t('loc.importUnitGoogle') }}</span>
          <span class="hint-text">{{ $t('loc.importUnitGoogleDesc') }}</span>
        </div>

        <div class="url-input-container">
          <el-input
            v-model="googleDocsUrl"
            placeholder="Please paste a Google Docs URL here."
            size="medium"
            clearable
            :disabled="nextLoading"
            :class="{ 'url-invalid': hasInvalidUrl }"
            @input="handleUrlInput"
          >
            <template slot="prefix">
              <img src="~@/assets/img/file/link.svg" style="height: 20px; width: 20px;"/>
            </template>
          </el-input>

          <!-- URL 验证错误提示 -->
          <div v-if="hasInvalidUrl" class="url-error-message">
            {{ $t('loc.importUnitGoogleUrlError') }}
          </div>
        </div>
      </div>

      <!-- 分隔符 -->
      <div class="divider-section">
        <span class="divider-text">{{ $t('loc.or') }}</span>
      </div>

      <!-- 文件上传部分 -->
      <div class="file-upload-section">
        <!-- 上传区域 -->
        <el-upload
          ref="uploadRef"
          class="upload-area"
          action="#"
          :http-request="upload"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :accept="isDragging ? null : acceptedTypes"
          :limit="1"
          :auto-upload="true"
          :show-file-list="false"
          :disabled="nextLoading || notAllowCreateImportUnit"
          drag
        >
          <div class="upload-content">
            <img src="~@/assets/img/file/upload.svg" style=" height: 30px; width: 30px;">
            <div class="upload-text">
              <p class="main-text">{{ $t('loc.importUnitUploadTitle1') }}</p>
              <p class="format-text">{{ $t('loc.importUnitUploadTitle2') }}</p>
              <p class="size-text">{{ $t('loc.importUnitUploadTitle3') }}</p>
            </div>

            <!-- Google Drive 按钮 -->
            <div v-if="showDrive" class="google-drive-section">
              <el-button
                class="google-drive-btn"
                @click.stop="openGoogleDrive"
                size="medium"
                :disabled="nextLoading || notAllowCreateImportUnit"
              >
                <img src="~@/assets/img/file/google_drive.svg" style=" height: 19px; width: 19px;">
                {{ $t('loc.importUnitDrive') }}
              </el-button>
            </div>
          </div>
        </el-upload>
      </div>

      <!-- 显示已选择的文件 -->
      <div v-if="selectedFile" class="selected-file-section">
        <div class="file-item">
          <div class="file-info">
            <img
              :src="fileIcon"
              alt=""
              style="height: 24px; width: 24px;"
              :class="{ 'file-icon-disabled': uploading }"
            >
            <div class="file-details">
              <span class="file-name">{{ selectedFile.name }}</span>
              <!-- 上传进度条 -->
              <div v-if="uploading" class="upload-progress-container">
                <el-progress
                  :percentage="uploadProgress"
                  :show-text="false"
                  :stroke-width="4"
                  color="#10B3B7"
                ></el-progress>
                <span class="upload-progress-text">{{ uploadProgress }}%</span>
              </div>
            </div>
          </div>
          <el-button
            type="text"
            class="remove-btn"
            @click="removeFile"
            icon="el-icon-close"
            :disabled="uploading || nextLoading"
          ></el-button>
        </div>
      </div>

      <!-- 样例文件 -->
      <div class="exemplar-file-section">
        <div class="file-item">
          <div class="file-info">
            <img
              src="@/assets/img/file/pdf.svg"
              alt=""
              style="height: 24px; width: 24px;"
              :class="{ 'file-icon-disabled': uploading }"
            >
            <div class="file-details">
              <span class="file-name">{{ exampleFile.name }}</span>
            </div>
          </div>
          <div class="file-actions">
            <div class="view-btn">
              <el-button
                type="text"
                @click="viewExampleFile"
                :disabled="uploading || nextLoading || notAllowCreateImportUnit"
              >
                View
              </el-button>
            </div>

            <div class="try-unit-btn" :style="{'opacity': uploading || nextLoading ? '0.4' : '1'}">
              <el-tooltip :content="$t('loc.importUnitExemplarTryToolTip')" placement="top">
                <el-button
                  type="text"
                  @click="tryExampleUnit"
                  :disabled="uploading || nextLoading || notAllowCreateImportUnit"
                >
                  Try This Unit
                </el-button>
              </el-tooltip>

            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Next 按钮 -->
    <div class="next-button-section">
      <el-button
        type="primary"
        size="medium"
        class="next-btn"
        :disabled="!canProceed || nextLoading || notAllowCreateImportUnit"
        :loading="nextLoading"
        @click="handleNext"
      >
        <span>{{ $t('loc.next') }}</span>
        <i class="lg-icon lg-icon-arrow-right"></i>
      </el-button>
    </div>

    <!-- 确认单元信息弹框 -->
    <el-dialog
      :visible.sync="showConfirmDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      width="600px"
      custom-class="confirm-unit-dialog"
      center
    >
      <div class="confirm-dialog-content">
        <!-- 标题 -->
        <div class="dialog-title">
          <h3>{{ $t('loc.importUnitStandTitle') }}</h3>
        </div>

        <!-- 描述 -->
        <div class="dialog-description">
          <p>{{ $t('loc.importUnitStandDesc') }}</p>
        </div>

        <!-- 表单内容 -->
        <el-form class="dialog-form" :model="confirmForm" label-position="top">
          <!--年龄组-->
          <el-form-item prop="ageGroup">
            <template slot="label">
              <div class="form-label">
                <span class="required">* </span>
                <span class="font-size-16 font-weight-600">{{ $t('loc.unitPlannerStep1Grade') }}</span>
              </div>
            </template>
            <div class="form-description">
              {{ $t('loc.importUnitStandGrade') }}
            </div>
            <el-select v-model="confirmForm.grade" class="w-full"
                       :placeholder="$t('loc.pleaseSelect')" @change="handleGradeChange">
              <el-option v-for="item in ageGroups" :key="item.value" :label="item.name" :value="item.name"/>
            </el-select>
          </el-form-item>

          <!-- 州标准选择 -->
          <FrameworkSelector
            v-model="confirmForm.stateStandards"
            :selectedGrade="confirmForm.grade"
            :loading="confirmLoading"
            :placeholder="$t('loc.pleaseSelect')"
            @change="handleFrameworkChange"
            @updateFrameworkName="updateFrameworkName"
          />

          <!-- 科目选择 -->
          <el-form-item prop="subjects" class="form-item">
            <template slot="label">
              <div class="form-label">
                <span class="required">* </span>
                <span class="font-size-16 font-weight-600">{{ $t('loc.unitPlannerStep1Standards2') }}</span>
              </div>
            </template>
            <div v-if="isOlderThanK" class="form-description">
              {{ $t('loc.importUnitStandSubjectLimit') }}
            </div>
            <SimpleSubjectSelector ref="simpleSubjectSelector"
                                   v-model="confirmForm.domainIds"
                                   :frameworkName="confirmForm.frameworkName"
                                   :ageName="confirmForm.grade"
            />
          </el-form-item>
        </el-form>

        <!-- 按钮区域 -->
        <div class="dialog-buttons">
          <el-button
            size="medium"
            class="cancel-btn"
            @click="handleDialogCancel"
          >
            {{ $t('loc.cancel') }}
          </el-button>
          <el-button
            type="primary"
            size="medium"
            class="confirm-btn"
            :disabled="!isConfirmFormValid"
            :loading="confirmLoading"
            @click="handleDialogConfirm"
          >
            {{ $t('loc.importUnitStandConfirm') }}
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 解析加载弹框 -->
    <AnalysisImportUnitLoading v-if="analysisLoading"/>
  </div>

</template>

<script>
import { mapState } from 'vuex'
import fileUtils from '@/utils/file.js'
import credentials from '@/components/googleAuth/credentials.json'
import tools from '../../../../../../utils/tools'
import Lessons2 from '@/api/lessons2'
import FrameworkSelector from '../../../lessonLibrary/assistant/FrameworkSelector.vue'
import { exampleImportUnit, PublicLessonAssistantInfoAgeGroup } from '../../../../../../utils/constants'

import AnalysisImportUnitLoading from './AnalysisImportUnitLoading.vue'
import SimpleSubjectSelector from '../../../lessonLibrary/assistant/SimpleSubjectSelector.vue'

export default {
  name: 'ImportUnit',
  components: {
    FrameworkSelector,
    SimpleSubjectSelector,
    AnalysisImportUnitLoading
  },
  data () {
    return {
      showDrive: true, // 是否显示谷歌 Drive
      leavedPage: false, // 是否离开页面
      googleDocsUrl: '', // Google Docs URL 输入值
      checkUrlType: null,
      fileList: [], // Element UI 内部文件管理数组（单文件模式）
      selectedFile: null, // 当前选择的文件
      exampleFile: exampleImportUnit, // 样例课程
      acceptedTypes: '.pdf,.docx,.doc', // 接受的文件类型
      maxFileSize: 20 * 1024 * 1024, // 20MB 文件大小限制
      uploading: false, // 上传状态
      uploadProgress: 0, // 上传进度
      isDragging: false, // 是否正在拖拽（用于控制 accept 属性）
      isUrlValid: true, // URL 是否有效（默认为 true，当输入内容时进行验证）
      nextLoading: false, // Next 按钮加载状态
      urlValidationTimer: null, // URL 验证定时器
      googleClientId: credentials.web.client_id, // Client ID 即 OAuth 2.0 客户端 ID
      pickScope: credentials.web.scope, // 作用域，这里面 documents 就是 docs
      googleDriveTokenClient: null, // google drive token客户端
      googleDriveAccessToken: null, // google drive通行token
      pickerInitialized: false, // 选择器是否已经初始化
      needAuth: false, // 是否需要谷歌认证
      apiKey: 'AIzaSyB3v3Q0TDi4Rnf3ZUaw9QO-xCgVc3wAJbs', // apiKey
      googleAppId: '348091211165', // apiId
      // 文件类型配置
      fileTypeConfig: {
        pdf: {
          icon: require('@/assets/img/file/pdf.svg')
        },
        doc: {
          icon: require('@/assets/img/file/word.svg')
        },
        docx: {
          icon: require('@/assets/img/file/word.svg')
        }
      },
      // 确认弹框相关数据
      showConfirmDialog: false, // 是否显示确认弹框
      confirmLoading: false, // 确认按钮加载状态
      getDomainLoading: false, // 学科加载状态
      domainSelectAll: false, // 学科全选状态
      confirmForm: {
        grade: '', // 选择的年级
        stateStandards: [], // 选择的州标准
        domainIds: [], // 选择的科目（多选）
        frameworkName: '' // 框架名称
      },
      ageGroups: [...PublicLessonAssistantInfoAgeGroup].reverse(), // 年级选项
      analysisResultId: null, // 解析结果 ID
      analysisLoading: false // 解析加载状态
    }
  },
  created () {
    // 如果是ipad，不显示Drive下载
    if (tools.isComeFromIPad()) {
      this.showDrive = false
    } else {
      this.initGoogleDrive()
      // 默认需要授权
      this.needAuth = true
    }
  },
  mounted () {
    // 初始化拖拽事件监听
    this.initDragEvents()
  },
  computed: {
    ...mapState({
      open: state => state.common.open,
      currentUser: (state) => state.user.currentUser // 当前用户
    }),
    // URL 是否有效且非空
    hasValidUrl () {
      return this.googleDocsUrl.trim() && this.isUrlValid
    },

    // URL 有内容但无效
    hasInvalidUrl () {
      return this.googleDocsUrl.trim() && !this.isUrlValid
    },

    // 是否可以进行下一步
    canProceed () {
      return Boolean(!this.nextLoading && (this.hasValidUrl || (this.selectedFile && !this.uploading)))
    },
    // 所选文件 icon
    fileIcon () {
      return this.selectedFile.type && this.fileTypeConfig[this.selectedFile.type].icon
    },

    // 确认表单是否有效
    isConfirmFormValid () {
      return Boolean(
        this.confirmForm.grade &&
        this.confirmForm.stateStandards.length > 0 &&
        this.confirmForm.domainIds.length > 0
      )
    },

    // 可导入改编单元创建权限控制
    notAllowCreateImportUnit () {
      return !this.open || !this.open.cgImportUnitAdaptOpen
    },

    // 获取当前选择的年龄是否为 'K (5-6)' 及以上
    isOlderThanK () {
      // 获取当前选中的年级的详细数据，获取其中的 value 值
      if (!this.confirmForm.grade) {
        return true
      }
      // 获取年龄值
      const ageValue = tools.getAgeValue(this.confirmForm.grade)
      // 判断年龄是否为 'K (5-6)' 及以上
      return ageValue ? ageValue >= 6 : false
    }
  },
  watch: {
    'googleDocsUrl': {
      handler (newVal, oldVal) {
        if (!newVal) {
          this.checkUrlType = null
        }
      }
    }
  },
  methods: {
    // 更新框架名称
    updateFrameworkName (frameworkName) {
      this.confirmForm.frameworkName = frameworkName
    },

    // 删除学科
    deleteDomain (domainId) {
      // 更新 domainSelected 数组
      this.confirmForm.domainIds = this.confirmForm.domainIds.filter(item => item !== domainId)
      if (this.confirmForm.domainIds.length === 0) {
        this.domainSelectAll = false
      }
    },

    // 年级选择变化时的处理函数
    handleGradeChange () {
      // 清空学科选择
      this.confirmForm.domainIds = []
    },

    // 框架选择变化时的处理函数
    handleFrameworkChange (data) {
      // 当框架选择发生变化时，清空学科选择
      this.confirmForm.domainIds = []
      // 根据框架查询学科数据
      if (this.confirmForm.stateStandards.length > 0 && this.$refs.simpleSubjectSelector) {
        let standardId = this.getStandardId()
        this.$refs.simpleSubjectSelector.fetchMeasuresFromBackend(standardId)
      }
    },

    // 获取框架 ID
    getStandardId () {
      let standardId
      if (this.confirmForm.stateStandards.length === 2) {
        standardId = this.confirmForm.stateStandards[1]
      } else {
        standardId = this.confirmForm.stateStandards[2]
      }
      return standardId
    },

    /**
     * google drive 初始化
     */
    initGoogleDrive () {
      this.gisLoaded()
      this.gapiLoaded()
      this.checkGoogleAuth()
    },
    /**
     * 在 api.js 加载之后，这里进行加载对应的操作的 api
     */
    gapiLoaded () {
      gapi.load('client:picker', this.initializePicker)
    },

    /**
     * 等待 api 加载完成之后，这里进行加载 rest 的服务
     */
    initializePicker () {
      this.pickerInitialized = true
      gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest')
        .then(() => {
        })
        .catch(error => {
          console.error('Failed to load Drive API:', error)
        })
    },

    /**
     * 回设 Token
     */
    setToken (token) {
      gapi.client.setToken({ access_token: token })
    },

    /**
     * Google Identity Services 加载完成之后
     */
    gisLoaded () {
      this.googleDriveTokenClient = google.accounts.oauth2.initCodeClient({
        client_id: this.googleClientId,
        scope: this.pickScope,
        ux_mode: 'popup',
        callback: ''
      })
    },

    /**
     * 如果同时加载完毕之后，这里应该调用后台的一个接口用于接收前台传递的 token
     */
    checkGoogleAuth (mustBeLoggedIn = false) {
      if (this.pickerInitialized) {
        // 封装 token 和 scope 来构建凭证信息
        const credentialsRequest = {
          authCode: this.googleAuthCode,
          scope: this.pickScope,
          userId: this.currentUser.user_id,
          scopeKey: 'drivefile',
          onlyCheck: ''
        }
        // 接口调用，传递 token
        this.$axios.post($api.urls().checkGoogleAuth, credentialsRequest).then(data => {
          this.needAuth = !data.success
          if (data.success) {
            this.googleDriveAccessToken = data.id
            // this.setToken(data.id)
          }
        })
      } else if (mustBeLoggedIn) {
        // 如果 pickerInitialized 是 false，那么就等待 initializePicker 加载完成
        this.initializePicker()
      }
    },
    /**
     *  点击 google drive 图标触发的操作
     *  1. 登录
     *  2. 选择文件
     *  3. 完成回调
     */
    handlerGoogleAuth () {
      this.googleDriveTokenClient.callback = async (response) => {
        if (response.error !== undefined) {
          throw (response)
        }
        this.googleAuthCode = response.code
        // 校验 scope 是否是规定的 scope
        // 保存对应的 token
        this.checkGoogleAuth(true)
      }

      // 发起授权
      this.googleDriveTokenClient.requestCode()
    },
    /**
     * 判断是否需要谷歌认证
     */
    authGoogleDocs () {
      if (this.needAuth) {
        this.handlerGoogleAuth()
      }
    },
    /**
     *  构建 google drive 文件选择器
     */
    createPicker () {
      if (!this.googleDriveAccessToken) {
        return
      }

      const view = new google.picker.View(google.picker.ViewId.DOCS)
      view.setMimeTypes([
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'application/pdf',
        'application/vnd.google-apps.document'
      ].join(','))

      const pickerBuilder = new google.picker.PickerBuilder()
      // pickerBuilder.enableFeature(google.picker.Feature.NAV_HIDDEN)
      // pickerBuilder.enableFeature(google.picker.Feature.MULTISELECT_ENABLED)
      pickerBuilder.setAppId(this.googleAppId)
      pickerBuilder.setDeveloperKey(this.apiKey)
      pickerBuilder.setOAuthToken(this.googleDriveAccessToken)
      pickerBuilder.addView(view)
      pickerBuilder.setCallback(this.pickerCallback)

      const picker = pickerBuilder.build()
      picker.setVisible(true)
    },

    /**
     * 选择文件后的回调函数
     * @param {object} data - 选择的文件的数据
     */
    async pickerCallback (data) {
      // 如果行为是选择，则执行操作
      if (data.action === google.picker.Action.PICKED) {
        var doc = data[google.picker.Response.DOCUMENTS][0]
        gapi.client.setToken({ access_token: this.googleDriveAccessToken })
        const fileInfo = await gapi.client.drive.files.get({
          'fileId': doc.id,
          fields: 'id,name,mimeType,size,createdTime'
        })
        if (fileInfo.result.size > this.maxFileSize) {
          this.$message.error(this.$t('loc.importUnitVarUrlSize'))
          return false
        }
        this.uploading = true
        // 进度条默认为0
        this.uploadProgress = 0

        var url = doc[google.picker.Document.URL]
        const fileName = doc.name
        let fileType = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
        if (doc.mimeType === 'application/vnd.google-apps.document') {
          fileType = 'docx'
        }
        // 设置选择的文件
        this.selectedFile = {
          name: fileName,
          type: fileType,
          id: doc.id,
          mimeType: doc.mimeType
        }
        this.uploadProgress = 100
        this.uploading = false
      }
    },
    // 处理 Google Docs URL 输入
    handleUrlInput (value) {
      this.googleDocsUrl = value

      // 清除之前的验证定时器
      if (this.urlValidationTimer) {
        clearTimeout(this.urlValidationTimer)
      }

      // 如果输入为空，立即重置状态
      if (!value || !value.trim()) {
        this.isUrlValid = true
        return
      }

      // 防抖验证：用户停止输入 500ms 后进行 URL 验证
      this.urlValidationTimer = setTimeout(() => {
        if (value && value.trim()) {
          this.isUrlValid = this.isValidUrl(value.trim())
          if (!this.isUrlValid) {
            // 使用更温和的提示方式，不使用 message 弹窗
          }
        }
      }, 500)
      this.$analytics.sendEvent('cg_adapt_link_input')
    },

    // 验证是否为有效的 HTTP/HTTPS URL
    isValidUrl (url) {
      try {
        const urlObj = new URL(url)
        // 检查协议是否为 http 或 https
        const isHttpProtocol = urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
        // 检查是否有有效的主机名
        const hasValidHost = urlObj.hostname && urlObj.hostname.length > 0

        // 基础 URL 格式验证
        if (!isHttpProtocol || !hasValidHost) {
          return false
        }

        // 可选：更严格的 Google Docs URL 验证
        const isGoogleDocsUrl = urlObj.hostname.includes('docs.google.com') ||
          urlObj.hostname.includes('drive.google.com')

        // 如果要严格验证 Google Docs URL，可以启用下面的检查
        return isGoogleDocsUrl
      } catch (error) {
        return false
      }
    },

    // 清空文件选择
    clearFileSelection () {
      this.fileList = []
      this.selectedFile = null
      // 重置 upload 组件
      if (this.$refs.uploadRef) {
        this.$refs.uploadRef.clearFiles()
      }
    },

    // 文件上传前的检查
    beforeUpload (file) {
      // 检查文件类型
      const isValidType = this.isValidFileType(file.name)
      if (!isValidType) {
        this.$message.error(this.$t('loc.importUnitVarUrlType'))
        return false
      }

      // 检查文件大小
      const isValidSize = file.size <= this.maxFileSize
      if (!isValidSize) {
        this.$message.error(this.$t('loc.importUnitVarUrlSize'))
        return false
      }
      // 设置选择的文件
      this.selectedFile = {
        name: file.name,
        size: file.size,
        type: file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase(),
        raw: file
      }

      this.uploading = true
      return true
    },

    // 检查文件类型
    isValidFileType (fileName) {
      const allowedExtensions = ['.pdf', '.docx', '.doc']
      const lowerFileName = fileName.toLowerCase()
      return allowedExtensions.some(function (ext) {
        return lowerFileName.endsWith(ext)
      })
    },

    // 初始化拖拽事件监听
    initDragEvents () {
      this.$nextTick(() => {
        const uploadArea = this.$refs.uploadRef && this.$refs.uploadRef.$el
        if (!uploadArea) return

        // 监听拖拽进入事件
        uploadArea.addEventListener('dragenter', this.handleDragEnter, false)
        uploadArea.addEventListener('dragover', this.handleDragEnter, false)

        // 监听拖拽离开和放下事件
        uploadArea.addEventListener('dragleave', this.handleDragLeave, false)
        uploadArea.addEventListener('drop', this.handleDragLeave, false)
      })
    },

    // 处理拖拽进入
    handleDragEnter (e) {
      e.preventDefault()
      this.isDragging = true
    },

    // 处理拖拽离开
    handleDragLeave (e) {
      e.preventDefault()
      // 延迟重置，确保拖拽操作完成
      setTimeout(() => {
        this.isDragging = false
      }, 100)
    },

    // 移除文件
    removeFile () {
      if (this.uploading) return // 上传中不允许删除
      this.$analytics.sendEvent('cg_adapt_file_delete_clicked')
      this.selectedFile = null
      this.fileList = []
      // 重置 upload 组件
      if (this.$refs.uploadRef) {
        this.$refs.uploadRef.clearFiles()
      }
    },

    // 上传调用
    async upload ({ file }) {
      // 显示进度条
      this.uploadProgress = 0
      this.$analytics.sendEvent('cg_adapt_file_uploaded')
      fileUtils.uploadFile(file, {
        privateFile: false,
        processEventHandler: (progressEvent) => {
          // 上传进度处理,图片显示出来才算彻底完成
          let progressRate = (progressEvent.loaded / progressEvent.total) * 100

          // 保留两位小数
          progressRate = Number(progressRate.toFixed(2))

          // 小于1则设为1
          if (progressRate < 1) {
            progressRate = 1
          }

          // 等于100则减1变成99
          if (progressRate === 100) {
            progressRate = 99.99
          }

          this.uploadProgress = progressRate
        }
      })
        .then(res => {
          if (!res || !res.id) {
            this.$message.error(this.$t('loc.lessons2UploadFailed'))
            return
          }
          // 上传成功
          this.selectedFile.url = res.public_url && res.public_url.trim() ? res.public_url : URL.createObjectURL(file)
        })
        .catch(() => {
          this.$message.error(this.$t('loc.lessons2UploadFailed'))
        })
        .finally(() => {
          // 关闭进度条和重置上传状态
          this.showProgress = false
          this.uploading = false
          this.fileList = []
        })
      return false
    },

    // 打开 Google Drive 选择器
    openGoogleDrive () {
      if (this.needAuth) {
        this.authGoogleDocs()
        return
      }
      this.$analytics.sendEvent('cg_adapt_gdrive_clicked')
      // 打开谷歌文件选择弹窗
      this.createPicker()
    },

    // 处理 Next 按钮点击
    async handleNext () {
      if (!this.canProceed || this.nextLoading) {
        return
      }
      this.$analytics.sendEvent('cg_adapt_upload_next_clicked')
      try {
        // 设置加载状态
        this.nextLoading = true

        // 如果点击下一步链接和文件都存在，弹出提示
        if (this.selectedFile && this.googleDocsUrl) {
          this.$message.warning(this.$t('loc.importUnitTwoFile'))
          this.nextLoading = false
          return
        }

        // 如果是链接进行有效性校验
        if (this.googleDocsUrl && !await this.checkUrlViaApi()) {
          this.nextLoading = false
          return
        }

        // 解析内容
        const params = {
          documentUrl: this.googleDocsUrl || (this.selectedFile && this.selectedFile.url),
          googlePickerFileId: this.selectedFile && this.selectedFile.id,
          fileType: this.checkUrlType || (this.selectedFile && this.selectedFile.type),
          mimeType: this.selectedFile && this.selectedFile.mimeType
        }
        // 链接确定
        const response = await Lessons2.performImportUnitContentFromUrl(params)
        if (!response.success) {
          this.$message.warning(this.$t('loc.uploadFailed'))
          this.nextLoading = false
          return
        }
        this.analysisResultId = response.id

        // 结果查询
        this.timeout = setTimeout(() => {
          this.getOcrResult()
        }, 3000)
      } catch (error) {
        this.$message.warning(this.$t('loc.uploadFailed'))
        this.nextLoading = false
      }
    },

    // 查询 OCR 识别结果
    getOcrResult () {
      if (this.leavedPage) {
        return
      }
      Lessons2.getOcrResultById(this.analysisResultId).then(response => {
        if (response.message === 'OCR_PROCESSING' || response.message === 'OCR_START') {
          // 如果未结束，则轮询获取任务状态
          setTimeout(() => {
            this.getOcrResult()
          }, 3000)
        } else if (response.message === 'OCR_SUCCESS') {
          this.nextLoading = false
          if (this.leavedPage) {
            return
          }
          // 显示确认弹框
          this.$analytics.sendEvent('cg_adapt_keyinfo_popup_shown')
          this.showConfirmDialog = true
        } else {
          this.nextLoading = false
          if (response.message === 'DOCUMENT_DATA_PARSING_INVALID') {
            this.$message.error(this.$t('loc.importUnitOcr1'))
          } else if (response.message === 'DOCUMENT_DATA_PARSING_FAILED') {
            this.$message.error(this.$t('loc.importUnitOcr2'))
          } else if (response.message === 'DOCUMENT_DATA_PARSING_NO_CONTENT') {
            this.$message.error(this.$t('loc.importUnitOcr3'))
          } else {
            this.$message.warning(this.$t('loc.uploadFailed'))
          }
        }
      }).catch(error => {
        this.nextLoading = false
      })
    },

    // 处理确认弹框取消
    handleDialogCancel () {
      this.showConfirmDialog = false
      this.$analytics.sendEvent('cg_adapt_keyinfo_cancel_clicked')
      // 重置表单数据
      this.confirmForm = {
        grade: '',
        stateStandards: [],
        domainIds: [],
        frameworkName: ''
      }
    },

    // 处理确认弹框确认
    async handleDialogConfirm () {
      if (!this.isConfirmFormValid || this.confirmLoading) {
        return
      }
      this.$analytics.sendEvent('cg_adapt_keyinfo_confirm_clicked')
      try {
        // 设置确认按钮加载状态
        this.confirmLoading = true

        // 准备数据
        const data = {
          analysisResultId: this.analysisResultId,
          grade: this.confirmForm.grade,
          frameworkId: this.getStandardId(),
          domainIds: this.confirmForm.domainIds
        }

        // 关闭弹框
        this.showConfirmDialog = false
        // 调用提示词分析提取数据
        this.analysisLoading = true
        this.$analytics.sendEvent('cg_adapt_analysis_animation_shown')
        const response = await Lessons2.analyzeExtractedContent(data)
        // 如果未结束，则轮询获取任务状态
        setTimeout(() => {
          this.getOcrContentResult(response.id)
        }, 3000)
      } catch (error) {
        console.error('error:', error)
        this.$message.error(this.$t('loc.serverInternalError'))
        this.analysisLoading = false
        this.confirmLoading = false
      }
    },

    // 查询 OCR 内容提取结果
    getOcrContentResult (id) {
      if (this.leavedPage) {
        return
      }
      Lessons2.getAnalyzeContentById(id).then(response => {
        if (!response.success) {
          if (response.failedReason === 'DOCUMENT_DATA_PARSING_INVALID') {
            this.$message.error(this.$t('loc.importUnitOcr1'))
            this.analysisLoading = false
            this.confirmLoading = false
            return
          }
          if (response.failedReason === 'INTERNAL_SERVER_ERROR') {
            this.$message.error(this.$t('loc.serverInternalError'))
            this.analysisLoading = false
            this.confirmLoading = false
            return
          }
          // 如果未结束，则轮询获取任务状态
          setTimeout(() => {
            this.getOcrContentResult(id)
          }, 3000)
        } else {
          // 将数据存入 VUEX 在结果页使用
          this.$store.commit('curriculum/SET_ANALYZE_UNIT_CONTENT', response)
          if (this.leavedPage) {
            return
          }
          this.$router.push({
            name: 'import-result-cg',
            params: {
              id: response.analysisResultId
            }
          })
          this.analysisLoading = false
          this.confirmLoading = false
        }
      }).catch(error => {
        console.log(error)
        this.analysisLoading = false
        this.confirmLoading = false
      })
    },

    /**
     * 前提校验链接类型
     */
    checkUrlTypeVia () {
      if (!this.googleDocsUrl) return false

      // ✅ 允许 Google Docs 的 document（Word）链接
      if (this.googleDocsUrl.toLowerCase().includes('docs.google.com/document')) {
        return true
      } else {
        this.$message.warning(this.$t('loc.importUnitVarUrlType'))
        return false
      }
    },
    /**
     * 通过后台检验 URL
     */
    async checkUrlViaApi () {
      const typeVia = this.checkUrlTypeVia()
      if (!typeVia) {
        return false
      }
      try {
        const response = await Lessons2.checkUrlViaApi(this.googleDocsUrl)
        if (response.validationSuccess) {
          this.checkUrlType = response.contentType
          return true
        }
        if (response.errorCode === 'URL_VALIDATION_ACCESS_DENIED' || response.errorCode === 'URL_VALIDATION_NO_CONTENT_TYPE') {
          this.$message.error(this.$t('loc.importUnitVarUrlAccess'))
        } else if (response.errorCode === 'URL_VALIDATION_UNSUPPORTED_FORMAT') {
          this.$message.error(this.$t('loc.importUnitVarUrlType'))
        } else if (response.errorCode === 'URL_VALIDATION_NO_FILE_SIZE' || response.errorCode === 'URL_VALIDATION_FILE_TOO_LARGE') {
          this.$message.error(this.$t('loc.importUnitVarUrlSize'))
        } else if (response.errorCode === 'URL_VALIDATION_NETWORK_ERROR') {
          this.$message.error(this.$t('loc.serverInternalError'))
        }
        return false
      } catch (error) {
        return false
      }
    },

    // 清理拖拽事件
    cleanupDragEvents () {
      const uploadArea = this.$refs.uploadRef && this.$refs.uploadRef.$el
      if (uploadArea) {
        uploadArea.removeEventListener('dragenter', this.handleDragEnter, false)
        uploadArea.removeEventListener('dragover', this.handleDragEnter, false)
        uploadArea.removeEventListener('dragleave', this.handleDragLeave, false)
        uploadArea.removeEventListener('drop', this.handleDragLeave, false)
      }
    },

    // 查看样例文件
    viewExampleFile () {
      this.$analytics.sendEvent('cg_adapt_exemplar_view_clicked')
      // 在新页面中打开样例文件 URL
      window.open(exampleImportUnit.url, '_blank')
    },

    // 尝试样例单元
    async tryExampleUnit () {
      try {
        this.$analytics.sendEvent('cg_adapt_exemplar_use_clicked')
        // 调用提示词分析提取数据
        this.analysisLoading = true
        // 等待 2 秒
        await new Promise(resolve => setTimeout(resolve, 2000))
        if (this.leavedPage) {
          return
        }
        // 准备数据
        const data = {
          analysisResultId: exampleImportUnit.id
        }
        const response = await Lessons2.userExampleImportUnit(data)
        // 将数据存入 VUEX 在结果页使用
        this.$store.commit('curriculum/SET_ANALYZE_UNIT_CONTENT', response)
        if (this.leavedPage) {
          return
        }
        this.$router.push({
          name: 'import-result-cg',
          params: {
            id: response.analysisResultId
          }
        })
      } catch (error) {
        this.$message.error(this.$t('loc.serverInternalError'))
        this.analysisLoading = false
      }
    }
  },
  beforeDestroy () {
    this.leavedPage = true

    // 清理定时器
    if (this.urlValidationTimer) {
      clearTimeout(this.urlValidationTimer)
    }
    // 清理拖拽事件监听器
    this.cleanupDragEvents()
  },
}
</script>

<style scoped lang="less">
.import-unit-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 65%;
  margin: 0 auto;

  .main-card {
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
    width: 100%;
    padding: 30px 10%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .title-section {
    display: flex;
    flex-direction: column; /* 改成纵向排列 */
    align-items: center; /* 横向居中 */
    justify-content: center; /* 纵向居中（可选） */
    width: 100%;
    text-align: center;
    margin-bottom: 4px;

    .title-section-1 {
      color: var(--111-c-1-c, #111C1C);
      text-align: center;
      /* Semi Bold/24px */
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 28px; /* 116.667% */
    }

    .title-section-2 {
      margin-top: 8px;
      color: var(--111-c-1-c, #111C1C);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-left: 12px;
    }
  }

  .google-docs-section {
    width: 100%;

    .section-title {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 16px;

      .title-text {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.5;
        color: #111C1C;
      }

      .hint-text {
        font-size: 12px;
        font-weight: 400;
        line-height: 1.333;
        color: #676879;
      }
    }

    .url-input-container {
      /deep/ .el-input {
        .el-input__inner {
          height: 48px;
          padding-left: 48px;
          padding-right: 30px;
          font-size: 16px;
          line-height: 1.5;
          border: 2px solid #DCDFE6;
          border-radius: 8px;

          &::placeholder {
            color: #676879;
          }

          &:focus {
            border-color: #10B3B7;
            box-shadow: none;
          }

          &:disabled {
            background-color: #F5F5F5;
            border-color: #E4E7ED;
            color: #C0C4CC;
            cursor: not-allowed;
          }
        }

        .el-input__prefix {
          left: 16px;
          top: 76%;
          transform: translateY(-50%);
        }

        .el-input__suffix {
          top: 14%;
        }

        // URL 无效状态样式
        &.url-invalid {
          .el-input__inner {
            border-color: #FA4E4E;

            &:focus {
              border-color: #FA4E4E;
              box-shadow: 0 0 0 2px rgba(250, 78, 78, 0.2);
            }
          }
        }
      }

      // URL 错误提示消息
      .url-error-message {
        margin-top: 4px;
        font-size: 12px;
        font-weight: 400;
        line-height: 1.333;
        color: #FA4E4E;
      }
    }
  }

  .divider-section {
    text-align: center;

    .divider-text {
      font-size: 16px;
      font-weight: 400;
      line-height: 1.5;
      color: #111C1C;
    }
  }

  .file-upload-section {
    width: 100%;

    .upload-area {
      width: 100%;

      /deep/ .el-upload {
        width: 100%;

        .el-upload-dragger {
          width: 100%;
          height: 260px;
          background: #FAFAFA;
          border: 2px dashed #DCDFE6;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover:not(.is-disabled) {
            border-color: #10B3B7;
            background: #F5FEFF;
          }

          &.is-disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background: #F5F5F5;
            border-color: #E4E7ED;
          }
        }
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        height: 100%;
        padding-top: 24px;
        gap: 8px;

        .upload-icon {
          font-size: 32px;
          color: #111C1C;
        }

        .upload-text {
          margin-top: 12px;
          text-align: center;

          .main-text {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5;
            color: #111C1C;
            margin: 0 0 4px 0;
          }

          .format-text {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5;
            color: #676879;
            margin: 0 0 4px 0;
            font-style: italic;
          }

          .size-text {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5;
            color: #676879;
            margin: 0;
            font-style: italic;
          }
        }

        .google-drive-section {
          margin-top: 12px;

          .google-drive-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #FFFFFF;
            border: 2px solid #DCDFE6;
            border-radius: 4px;
            color: #111C1C;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.571;
            height: 40px;

            &:hover:not(:disabled) {
              background: #F5F5F5;
              border-color: #999;
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
              background: #F5F5F5;
              border-color: #E4E7ED;
              color: #C0C4CC;
            }

            .google-drive-icon {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }
  }

  .selected-file-section {
    width: 100%;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 11px 16px;
      background: #FFFFFF;
      border: 1px solid #DCDFE6;
      border-radius: 8px;

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;
        overflow: hidden;

        .file-icon-disabled {
          opacity: 0.5;
          filter: grayscale(100%);
          transition: all 0.3s ease;
        }

        .file-details {
          flex: 1;
          min-width: 0;
          max-width: calc(100% - 32px); // 减去删除按钮和间距的宽度

          .file-name {
            font-size: 14px;
            font-weight: 400;
            line-height: 1.21;
            color: #111C1C;
            display: block;
            /* 以下是省略号样式 */
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100%;
          }

          .upload-progress-container {
            display: flex;
            align-items: center;
            margin-top: 8px;
            gap: 8px;

            /deep/ .el-progress {
              flex: 1;
            }

            .upload-progress-text {
              font-size: 12px;
              color: #909399;
              min-width: 35px;
            }
          }

          .upload-status {
            margin-top: 4px;
            font-size: 12px;

            .uploading-text {
              color: #409EFF;
            }
          }
        }
      }

      .remove-btn {
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #676879;

        &:hover:not(:disabled) {
          color: #FA4E4E;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .exemplar-file-section {
    width: 100%;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 16px;
      background: #FFFFFF;
      border: 1px solid #DCDFE6;
      border-radius: 8px;

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;
        overflow: hidden;

        .file-icon-disabled {
          opacity: 0.5;
          filter: grayscale(100%);
          transition: all 0.3s ease;
        }

        .file-details {
          flex: 1;
          min-width: 0;
          max-width: calc(100% - 32px); // 减去删除按钮和间距的宽度

          .file-name {
            font-size: 14px;
            font-weight: 400;
            line-height: 1.21;
            color: #111C1C;
            display: block;
            /* 以下是省略号样式 */
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100%;
          }

          .upload-progress-container {
            display: flex;
            align-items: center;
            margin-top: 8px;
            gap: 8px;

            /deep/ .el-progress {
              flex: 1;
            }

            .upload-progress-text {
              font-size: 12px;
              color: #909399;
              min-width: 35px;
            }
          }

          .upload-status {
            margin-top: 4px;
            font-size: 12px;

            .uploading-text {
              color: #409EFF;
            }
          }
        }
      }

      .file-actions {
        height: 30px;
        display: flex;
        gap: 8px;
        flex-shrink: 0; // ⬅️ 防止按钮组被压缩

        .view-btn {
          margin-right: 16px;
          height: 30px;
          padding: 0;
          color: #10B3B7;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px; /* 133.333% */

          /deep/ .el-button {
            font-weight: 400;
          }

          /deep/ .el-button--text {
            padding: 0 !important;
            height: 30px;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .try-unit-btn {
          display: flex;
          align-items: center;
          justify-content: center; /* 水平居中（如果需要） */
          height: 30px;
          background: #DDF2F3;
          border: 1px solid #10B3B7;
          border-radius: 100px;
          color: #10B3B7;
          line-height: 1.571;

          /deep/ .el-button--text {
            padding: 8px 12px !important;
            height: 30px;
          }

          /deep/ .is-disabled {
            background: #DDF2F3;
            border-radius: 100px;
            color: #10B3B7;
          }
        }

      }
    }
  }

  .file-item:hover {
    border: 1px solid #10B3B7;
    box-shadow: 0 0 3px rgba(16, 179, 183, 0.4); /* 绿色柔光阴影 */
  }

  .next-button-section {
    .next-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 8px 12px;
      width: 365px;
      height: 48px;
      background: #10B3B7;
      border: 2px solid rgba(16, 179, 183, 0.4);
      border-radius: 4px;
      font-size: 16px;
      font-weight: 600;
      line-height: 1.5;
      color: #FFFFFF;

      &:hover:not(:disabled) {
        background: #0E9CA0;
        border-color: rgba(16, 179, 183, 0.6);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .el-icon-right {
        font-size: 24px;
      }
    }
  }
}

// 覆盖 Element UI 默认的上传图标边距
/deep/ .el-upload-dragger .el-icon-upload {
  margin: 0 0 16px !important;
}

// 确认弹框样式
/deep/ .confirm-unit-dialog {
  .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }

  .el-dialog__body {
    padding: 0;
  }

  .confirm-dialog-content {
    padding: 0 32px 32px 32px;

    .dialog-title {
      text-align: left;
      margin-bottom: 16px;

      h3 {
        font-size: 20px;
        font-weight: 600;
        line-height: 1.167;
        color: #111C1C;
        margin: 0;
      }
    }

    .dialog-description {
      text-align: left;
      margin-bottom: 32px;

      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5;
        color: #676879;
        margin: 0;
      }
    }

    .dialog-form {
      // 覆盖 el-form 的默认样式
      &.el-form {
        .el-form-item {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 32px;
          }

          // 覆盖默认的标签样式
          .el-form-item__label {
            padding: 0;
            margin-bottom: 8px;
            line-height: normal;
          }

          .el-form-item__content {
            line-height: normal;
          }
        }

        .form-label {
          display: block;
          font-size: 16px;
          font-weight: 600;
          line-height: 1.5;
          color: #111C1C;

          .required {
            color: #FA4E4E;
          }
        }

        .form-description {
          font-size: 14px;
          font-weight: 400;
          line-height: 1.429;
          color: #676879;
          margin-bottom: 8px;
          font-style: italic;
        }

        .form-select {
          width: 100%;

          .el-input__inner {
            height: 40px;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            background: #F5F5F5;

            &:focus {
              border-color: #10B3B7;
              background: #FFFFFF;
            }

            &::placeholder {
              color: #C0C4CC;
            }

            &:disabled {
              background-color: #F5F5F5;
              border-color: #E4E7ED;
              color: #C0C4CC;
              cursor: not-allowed;
            }
          }

          .el-input__suffix {
            right: 8px;
          }

          // 禁用状态下的多选标签样式
          &.is-disabled {
            .el-select__tags {
              pointer-events: none;
            }
          }
        }
      }
    }

    .dialog-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding-top: 16px;

      .cancel-btn {
        padding: 8px 16px;
        height: 40px;
        background: #FFFFFF;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        color: #111C1C;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.571;

        &:hover {
          background: #F5F5F5;
          border-color: #C0C4CC;
        }
      }

      .confirm-btn {
        padding: 8px 16px;
        height: 40px;
        background: #10B3B7;
        border: 1px solid #10B3B7;
        border-radius: 4px;
        color: #FFFFFF;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.571;

        &:hover:not(:disabled) {
          background: #0E9CA0;
          border-color: #0E9CA0;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background: #10B3B7;
          border-color: #10B3B7;
        }
      }
    }
  }
}
</style>