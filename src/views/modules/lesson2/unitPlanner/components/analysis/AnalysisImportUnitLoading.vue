<template>
<div class="content-body">
  <div role="dialog" class="dialog-content">
    <div class="backdrop-blur"></div>
    <div class="content-wrapper">
      <img class="image-container" src="~@/assets/img/lesson2/unitPlanner/unit_loading.png"/>
      <div class="loader-container">
        <div class="loader"></div>
      </div>
      <!-- 添加步骤文本滚动显示 -->
      <div class="steps-container">
        <div class="step-text">{{ currentStepText }}</div>
      </div>
    </div>
  </div>
</div>
</template>

<script>
export default {
  name: 'AnalysisImportUnitLoading',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 定义四种步骤文本
      stepTexts: [
        'Identifying unit title and overview...',
        'Outlining weekly topics...',
        'Extracting learning objectives and resources...',
        'Locating referenced resources...'
      ],
      currentStepIndex: 0, // 当前步骤索引
      stepTimer: null // 定时器引用
    }
  },
  computed: {
    // 计算当前显示的步骤文本
    currentStepText () {
      return this.stepTexts[this.currentStepIndex]
    }
  },
  watch: {
    // 监听对话框显示状态
    dialogVisible (newVal) {
      if (newVal) {
        this.startStepRotation()
      } else {
        this.stopStepRotation()
      }
    }
  },
  mounted () {
    // 组件挂载时如果对话框已显示，启动步骤轮换
    this.startStepRotation()
  },
  beforeDestroy () {
    // 组件销毁前清除定时器
    this.stopStepRotation()
  },
  methods: {
    // 启动步骤轮换
    startStepRotation () {
      this.currentStepIndex = 0
      this.stepTimer = setInterval(() => {
        this.currentStepIndex = (this.currentStepIndex + 1) % this.stepTexts.length
      }, 5000)
    },
    // 停止步骤轮换
    stopStepRotation () {
      if (this.stepTimer) {
        clearInterval(this.stepTimer)
        this.stepTimer = null
      }
    }
  }
}
</script>

<style lang="less" scoped>
.content-body {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

.dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* 使用视口高度而不是固定像素 */
  width: 100%;
  position: relative;
}

.backdrop-blur {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: -1;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  background-color: transparent;
  filter: none !important;
  position: relative;
}

.image-container {
  width: 170px;
  height: 178px;
  margin-bottom: 20px;
  filter: none !important;
  position: relative;
  z-index: 20;
}

.loader-container {
  margin-top: 10px;
  filter: none !important;
  position: relative;
  z-index: 20;
}

.loader {
  display: block;
  --height-of-loader: 12px;
  width: 600px;
  @media screen and (max-width: 768px) {
    width: 320px !important;
  }
  height: var(--height-of-loader);
  border-radius: 15px;
  background-color: rgba(0, 0, 0, 0.2);
  position: relative;
}

.loader::before {
  content: "";
  position: absolute;
  background: linear-gradient(90deg, #AA89F2 0%, #10B3B7 100%);
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  border-radius: 15px;
  animation: moving 3s ease-in-out infinite;
}

@keyframes moving {
  50% {
    width: 100%;
  }

  100% {
    width: 0;
    right: 0;
    left: unset;
  }
}

/* 步骤文本容器样式 */
.steps-container {
  margin-top: 20px;
  height: 30px; /* 固定高度防止布局跳动 */
  display: flex;
  align-items: center;
  justify-content: center;
  filter: none !important;
  position: relative;
  z-index: 20;
}

/* 步骤文本样式 */
.step-text {
  color: #111c1c;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  transition: opacity 0.3s ease-in-out; /* 添加淡入淡出过渡效果 */
  opacity: 1;
}

/deep/ .transparent-dialog {
  background-color: white !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  opacity: 0.8;

  .el-dialog__header,
  .el-dialog__body {
    background-color: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
    filter: none !important;
  }

  .el-dialog__wrapper {
    background-color: transparent !important;
  }
}

/deep/ .el-dialog__wrapper {
  background-color: rgba(0, 0, 0, 0) !important;
}

/deep/ .el-dialog {
  background-color: white !important;
  opacity: 0.9;
  filter: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/deep/ .full-screen-dialog {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
  max-width: 100vw !important;

  .el-dialog {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

.loading-text {
  color: #676879;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px; /* 144.444% */
  margin-top: 20px;
  filter: none !important;
  position: relative;
  z-index: 20;
}
</style>