<template>
  <div>
    <table class="calendar-table">
      <tr class="calendar-header">
        <th class="text-center" colspan="7">
          <div class="calendar-title">
            <!-- 切换过去的时间段 -->
            <span class="display-inline-block left" :class="{ 'is-not-optional': !isOptionalPassedDate }" @click="selectPassedDate">
              <i class="el-icon-arrow-left"></i>
            </span>
            <!-- 显示当前年月标题 -->
            <span>{{ title }}</span>
            <!-- 切换未来的时间段 -->
            <span class="display-inline-block right" :class="{ 'is-not-optional': !isOptionalFutureDate }" @click="selectFutureDate">
              <i class="el-icon-arrow-right"></i>
            </span>
          </div>
        </th>
      </tr>
      <tr>
        <td class="calendar-header-day text-center" style="height: 40px" v-for="(i, index) in weekdays" :key="index">{{ i }}</td>
      </tr>
      <tr v-for="(date, index) in rows" :key="index">
        <!-- <td class="selectDate"> -->
        <td class="text-center day-cell"
          :class="{ 'active': showdays[index == 0 ? 0 : index * 7].isActive, 'holiday': showdays[index == 0 ? 0 : index * 7].isHoliday }">
          {{ showdays[index == 0 ? 0 : index * 7].dayOfMonth }}
        </td>
        <td class="text-center day-cell"
          :class="{ 'active': showdays[index == 0 ? 1 : index * 7 + 1].isActive, 'holiday': showdays[index == 0 ? 1 : index * 7 + 1].isHoliday }">
          {{ showdays[index == 0 ? 1 : index * 7 + 1].dayOfMonth }}
        </td>
        <td class="text-center day-cell"
          :class="{ 'active': showdays[index == 0 ? 2 : index * 7 + 2].isActive, 'holiday': showdays[index == 0 ? 2 : index * 7 + 2].isHoliday }">
          {{ showdays[index == 0 ? 2 : index * 7 + 2].dayOfMonth }}
        </td>
        <td class="text-center day-cell"
          :class="{ 'active': showdays[index == 0 ? 3 : index * 7 + 3].isActive, 'holiday': showdays[index == 0 ? 3 : index * 7 + 3].isHoliday }">
          {{ showdays[index == 0 ? 3 : index * 7 + 3].dayOfMonth }}
        </td>
        <td class="text-center day-cell"
          :class="{ 'active': showdays[index == 0 ? 4 : index * 7 + 4].isActive, 'holiday': showdays[index == 0 ? 4 : index * 7 + 4].isHoliday }">
          {{ showdays[index == 0 ? 4 : index * 7 + 4].dayOfMonth }}
        </td>
        <td class="text-center day-cell"
          :class="{ 'active': showdays[index == 0 ? 5 : index * 7 + 5].isActive, 'holiday': showdays[index == 0 ? 5 : index * 7 + 5].isHoliday }">
          {{ showdays[index == 0 ? 5 : index * 7 + 5].dayOfMonth }}
        </td>
        <td class="text-center day-cell"
          :class="{ 'active': showdays[index == 0 ? 6 : index * 7 + 6].isActive, 'holiday': showdays[index == 0 ? 6 : index * 7 + 6].isHoliday }">
          {{ showdays[index == 0 ? 6 : index * 7 + 6].dayOfMonth }}
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    calendars: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    activeDays () {
      this.initCalendar(this.year, this.month)
    }
  },
  created () {
    // 日历默认日期为当前年月
    this.year = new Date().getFullYear()
    this.month = new Date().getMonth() + 1
    // 初始化日历
    this.initCalendar(this.year, this.month)
  },
  data () {
    return {
      rows: 6, // 总行数
      weekdays: ['S', 'M', 'T', 'W', 'T', 'F', 'S'], // 周次排列
      showdays: [], // 显示的日期
      year: 0, // 年份
      month: 0, // 月份
      activeDays: [], // 激活的日期
      holidays: [], // 节假日
      isOptionalPassedDate: false, // 是否可选过去的日期, 默认是第一个日期，所以不可选
      isOptionalFutureDate: true // 是否可选未来的日期, 默认是可选的
    }
  },
  computed: {
    title () {
      let dateStr = this.year + '-' + this.month
      // 兼容safari浏览器，月份小于10时，需要在月份前面加0
      if (this.month < 10) {
        dateStr = this.year + '-0' + this.month
      }
      return this.$moment(dateStr).format('MMMM YYYY')
    }
  },
  methods: {
    // 初始化日历
    initCalendar (year, month) {
      this.showdays = []
      let firstdayOfWeek = new Date(year, month - 1).getDay()
      for (let i = 0; i < firstdayOfWeek; i++) {
        let day = {
          dayOfMonth: '',
          isActive: false,
          isHoliday: false
        }
        this.showdays.push(day)
      }
      var max = new Date(year, month, 0).getDate()
      for (let i = 1; i <= max; i++) {
        let isActive = this.activeDays.indexOf(i) >= 0
        let isHoliday = this.holidays.indexOf(i) >= 0
        let day = {
          dayOfMonth: i,
          isActive: isActive,
          isHoliday: isHoliday
        }
        this.showdays.push(day)
      }
      this.rows = Math.ceil(this.showdays.length / 7)
      if (this.showdays.length % 7 > 0) {
        for (let i = 0; i < (this.showdays.length % 7); i++) {
          let day = {
            dayOfMonth: '',
            isActive: false,
            isHoliday: false
          }
          this.showdays.push(day)
        }
      }
    },
    // 选择未来日期
    selectFutureDate () {
      // 遍历日历, 进行年份月份赋值操作
      for (let i = 0; i < this.calendars.length; i++) {
        // 找到当前切换的日期的索引位置所在，主要用于是否可以进行再次切换
        if (this.year === this.calendars[i].year && this.month === this.calendars[i].month) {
          // 如果是倒数第一个日期，则向右的箭头不可选
          if (i === this.calendars.length - 1) {
            this.isOptionalFutureDate = false
            return
          }
          // 进行年份月份赋值操作
          this.year = this.calendars[i + 1].year
          this.month = this.calendars[i + 1].month
          this.activeDays = this.calendars[i + 1].activeDays
          this.holidays = []
          // 初始化日历
          this.initCalendar(this.year, this.month)
          // 如果是倒数第二个日期，则向右的箭头不可选
          if (i === this.calendars.length - 2) {
            this.isOptionalFutureDate = false
          }
          // 向左的箭头可选
          this.isOptionalPassedDate = true
          break
        }
      }
    },
    // 选择过去日期
    selectPassedDate () {
      // 遍历日历, 进行年份月份赋值操作
      this.calendars.forEach((calendar, index) => {
        // 找到当前切换的日期的索引位置所在，主要用于是否可以进行再次切换
        if (this.year === calendar.year && this.month === calendar.month) {
          // 如果是第一个日期，则向左的箭头不可选
          if (index === 0) {
            this.isOptionalPassedDate = false
            return
          }
          // 进行年份月份赋值操作
          this.year = this.calendars[index - 1].year
          this.month = this.calendars[index - 1].month
          this.activeDays = this.calendars[index - 1].activeDays
          this.holidays = []
          // 初始化日历
          this.initCalendar(this.year, this.month)
          // 如果是第二个日期，则向左的箭头不可选
          if (index === 1) {
            this.isOptionalPassedDate = false
          }
          // 向右的箭头可选
          this.isOptionalFutureDate = true
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.calendar-table {
  background: #FFFFFF;
  height: 318px!important;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: inherit;
  border: solid 1px var(--color-border);
}

.calendar-header {
  background: #C7E9EB;
  height: 36px!important;
}
.calendar-header-day {
  background-color: #10B3B7;
  color: #FFFFFF;
  border: 1px solid var(--color-border);
}

.day-cell {
  border: 1px solid var(--color-border);
  padding: 3px;
  font-size: 12px;
}

.holiday {
  background: #E3E3E3 !important;
}

.active {
  background: #FFE8B9;
}
.calendar-title {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.left {
  width: 16px;
  height: 16px;
  line-height: 16px;
  background: #676879;
  color: #fff;
  border-radius: 50%;
  cursor: pointer;
}
.right {
  width: 16px;
  height: 16px;
  line-height: 16px;
  background: #676879;
  color: #fff;
  border-radius: 50%;
  cursor: pointer;
}
.is-not-optional {
  opacity: .4;
  cursor: not-allowed;
}
</style>
