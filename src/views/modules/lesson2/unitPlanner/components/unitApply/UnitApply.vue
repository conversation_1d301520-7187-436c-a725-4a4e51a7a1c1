<template>
  <div>
    <!-- adapter 按钮及其引导-->
    <el-popover
      placement="bottom"
      width="508"
      v-model="showSimpleUnitGuide"
      ref="settingGuide"
      popper-class="adapt-UDL-and-CLR-guide-color-text"
      trigger="manual">
      <div class="text-white">
        <!-- 标题 -->
        <div class="title-font-20 lg-margin-bottom-12">
          {{$t('loc.seamlesslyIntoWeeklyPlansTitle')}}
        </div>
        <!-- 引导文字 -->
        <div class="lg-margin-bottom-24 word-break text-left">
          <!-- 用户引导内容 -->
          <span class="font-size-16 font-weight-400 line-height-24">{{$t('loc.seamlesslyIntoWeeklyPlansContent')}}</span>
        </div>
        <div class="display-flex align-items" :class="{ 'justify-content-between': isOpenAdaptUDLAndCLR, 'justify-content-end': !isOpenAdaptUDLAndCLR }">
          <span class="font-size-16 lg-pointer" v-if="isOpenAdaptUDLAndCLR" @click="skipUnitAdaptGuide()">Skip</span>
          <div class="display-flex align-items" style="gap: 10px">
<!--            <span class="font-size-16" v-if="isOpenAdaptUDLAndCLR">(1/2)</span>-->
            <el-button type="primary" @click="hideApplyGuide()">
<!--              <span v-if="isOpenAdaptUDLAndCLR">{{$t('loc.next')}}</span>-->
<!--              <span v-else> {{ $t('loc.gotIt') }} </span>-->
              <span> {{ $t('loc.gotIt') }} </span>
            </el-button>
          </div>
        </div>
      </div>
      <div slot="reference">
        <el-button class='el-button-warning-dark' @click="openDialog">{{$t('loc.unitPlannerWeeklyAddUnit')}}</el-button>
      </div>
    </el-popover>
    <el-dialog :title="$t('loc.unitPlannerWeeklyAddUnit')" :visible="dialogVisable" :before-close="closeDialog" width="60%"
               :append-to-body="true" :close-on-click-modal="false" :close-on-press-escape="false"
               custom-class="curriculum-apply-dialog">
      <div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-row>
              <el-row>
                <div class="m-b-16">
                  <div class="font-bold font-size-16 font-color-black m-b-8">
                    <span>{{$t('loc.curriculum40')}}</span>
                    <span v-if="selectPlanIds.length > 0" class="lg-color-primary font-size-14 font-weight-400 lg-margin-left-16">
                      Selected: {{ selectPlanIds.length }}
                      <span v-if="selectPlanIds.length === 1">{{ $t('loc.unitPlannerWeek') }}</span>
                      <span v-if="selectPlanIds.length > 1">{{ $t('loc.unitPlannerWeeks') }}</span>
                    </span>
                  </div>
                  <el-row>
                    <div class="unit-tree" ref="unitTree">
                      <el-checkbox v-model="selectAllWeek" @change="selectAllWeekplan" :indeterminate="isIndeterminate">
                        <span class="font-size-16">{{$t('loc.unitPlannerStep3SelectAll')}}</span>
                      </el-checkbox>
                      <!-- 单元树列表 -->
                      <el-tree :data="units"
                               v-loading="!unitsLoadingEnd"
                               default-expand-all
                               :empty-text="$t('loc.unitPlannerEmpty4')"
                               @check-change="handleSelect" ref="tree"
                               :default-checked-keys="defaultSelectedPlanIds"
                               show-checkbox
                               node-key="unitId"
                               class="lg-scrollbar-show max-height-300" :props="defaultProps">
                        <span slot-scope="{node, data}" class="text-ellipsis font-size-14 display-flex align-items" style="gap: 10px">
                          <span :title="data.title">{{ data.title }}</span>
                          <el-tag v-if="data.adapted" class="adapted-tag font-weight-400" size="mini" :title="data.adaptGroupName">
                            <span v-if="!data.isWeekPlanNode">{{ $t('loc.unitAdaptedGroup') }} {{ data.adaptGroupName }}</span>
                            <span v-else> {{ $t('loc.adaptUnitPlanner25') }} </span>
                          </el-tag>
                        </span>
                      </el-tree>
                    </div>
                  </el-row>
                </div>
                <div class="m-b-16">
                  <div class="font-bold font-size-16 font-color-black m-b-8">{{ $t('loc.curriculum23') }}</div>
                  <el-row :gutter="6" class="display-flex">
                    <!-- 学校 -->
                    <el-col>
                      <el-select v-model="currentCenterId" @change="changeCenter" :disabled="loading" style="margin-left: 3px"
                                 class="add-margin-r-20 border-bold" :popper-append-to-body="false">
                        <el-option v-for="center in centers" :key="center.id" :label="center.name"
                                   :value="center.id"></el-option>
                      </el-select>
                    </el-col>
                    <!-- 班级 -->
                    <el-col>
                      <el-select v-model="currentGroupId" :disabled="loading" class="add-margin-l-5 border-bold" style="padding-right: 2px"
                                 :popper-append-to-body="false">
                        <el-option v-for="group in groups" :key="group.id" :label="group.name"
                                   :value="group.id"></el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                  <span v-if="showTips && !multipleAdaptedUnits && !multipleAdaptedGroups" class="color-text-danger">{{ $t('loc.adaptUnitPlanner14') }}</span>
                  <span v-if="showTips && multipleAdaptedUnits && !multipleAdaptedGroups" class="color-text-danger">{{ $t('loc.adaptUnitPlanner15') }}</span>
                  <span v-if="showTips && multipleAdaptedUnits && multipleAdaptedGroups" class="color-text-danger">{{ $t('loc.adaptUnitPlanner16') }}</span>
                </div>
                <!-- 日期和周次 -->
                <div class="m-b-16 select-component">
                  <div class="font-bold font-size-16 font-color-black m-b-8">{{ $t('loc.curriculum24') }}</div>
                  <el-row :gutter="6" class="display-flex">
                    <el-col class="display-flex">
                      <el-date-picker
                        class="border-bold"
                        style="width:100%"
                        v-model="startDate"
                        @change="changeStartDate"
                        type="date"
                        :pickerOptions="pickerOptions"
                        format="MM/dd/yyyy"
                        :placeholder="$t('loc.curriculum83')">
                      </el-date-picker>
                    </el-col>
                    <el-col >
                      <el-select class="border-bold" v-model="startWeek" :placeholder="$t('loc.curriculum25')">
                        <el-option v-for="week in 54" :key="week" :label="$t('loc.curriculum103', {num: week})"
                                   :value="week"></el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
              </el-row>
            </el-row>
          </el-col>
          <el-col :span="12">
            <!-- 日历组件以及提示 -->
            <el-row class="display-flex justify-content">
              <el-col :span="18">
                <div style="border-radius: 8px">
                  <div class="display-flex justify-content-between lg-margin-bottom-8" style="margin-top: -20px">
                    <div class="lg-color-text-primary font-size-16 add-padding-t-20 text-bolder">
                      {{ $t('loc.curriculum26') }}
                    </div>
                    <div class="display-flex">
                      <div class="display-flex add-padding-t-20">
                        <i
                          style="display: inline-block;height: 15px;width: 15px;background-color: #FFE8B9;margin-top: 2px;"></i>
                        <div class="add-padding-l-6">{{ $t('loc.curriculum28') }}</div>
                      </div>
                    </div>
                  </div>
                  <calendar ref="calendar" :calendars="calendars"></calendar>
                </div>
              </el-col>
            </el-row>
            <div class="tip-content">
              <div>
                {{ $t('loc.curriculum92') }}
              </div>
              <div>
                {{ $t('loc.curriculum93') }}
              </div>
              <div>
                {{ $t('loc.curriculum94') }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="display-flex align-items justify-content-between">
        <el-checkbox v-if="showAutoAdapt" v-model="isAutoAdapt">{{ $t('loc.autoAdapteAgencyTemplate') }}</el-checkbox>
        <div class="display-flex align-items justify-content-end w-full">
          <!-- 取消应用 -->
          <el-button plain @click="closeDialog">{{ $t('loc.cancel') }}</el-button>
          <!-- 确认应用 -->
          <el-button type="primary" @click="applyPlans" :loading="applying">{{ $t('loc.confirm') }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { equalsIgnoreCase, getCurrentUser } from '@/utils/common'
import Calendar from './Calendar.vue'
import Lessons2 from '@/api/lessons2'
import { mapState } from 'vuex'

export default {
  name: 'UnitApply',
  components: { Calendar },
  props: {
    unit: {
      type: Object,
      default: () => {}
    },
    applyType: {
      type: String,
      default: ''
    },
    showSimpleUnitGuide: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      dialogVisable: false, // 周计划应用弹窗显示
      unitsLoading: false, // 单元加载中
      loading: false, // 加载
      currentCenterId: '', // 选择的当前学校 ID
      currentGroupId: '', // 选择的当前班级 ID
      centers: [], // 所有的学校
      groups: [], // 所有的班级
      noGroup: false, // 是否没有班级
      show: false, // 是否显示选择单元周计划 popover
      selectAllWeek: false, // 选择所有的周
      selectedUnits: [], // 已选的单元
      selectedPlans: [], // 已选的周计划
      defaultProps: {
        children: 'planModels',
        label: 'title',
        isLeaf: (data, node) => {
          if (node.level === 2) {
            return true
          }
        }
      }, // el-tree 配置
      startDate: '', // 开始时间
      startWeek: 1, // 开始周次
      calendars: [], // 日历信息
      applying: false, // 正在应用周计划
      units: [], // 单元列表
      pageSize: 8, // 每次加载的页大小
      pageNum: 1, // 已加载页
      ended: false, // 数据是否已全部加载
      endScroll: false, // 触发二次加载
      total: 0, // 数据总条数
      getUnitsLoading: 0, //  0 未加载，1 加载中，2 已加载
      isFirstLoading: true, // 是否第一次加载
      selectPlanIds: [], // 选择的周计划 ID
      selectUnitIds: [], // 选择的单元 ID
      defaultSelectedPlanIds: [], // 默认选择的周计划 ID 集合
      treeLoading: false, // 树加载中
      copySelectedPlanIds: [], // 复制选择的周计划 ID
      isAutoAdapt: false, // 是否自动适配机构周计划模版
      showAutoAdapt: false, // 是否显示自动适配机构周计划模版
      showTips: false, // 是否显示改编班级与当前选择的班级不一致的提示信息
      unitsLoadingEnd: false // 获取单元周计划接口是否响应结束
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      guideFeatures: state => state.common.guideFeatures
    }),
    // 所有的周计划 ID
    allPlanIds () {
      let planIds = []
      this.units.forEach(x => x.planModels.forEach(plan => planIds.push(plan.planId)))
      return planIds
    },
    allUnitIds () {
      let unitIds = []
      this.units.forEach(unit => unit.planModels.forEach(plan => unitIds.push(plan.planId)))
      return unitIds
    },
    // 单元周计划半选状态
    isIndeterminate () {
      return this.selectPlanIds.length > 0 && this.selectPlanIds.length < this.allPlanIds.length
    },
    // 日期选择组件配置
    pickerOptions () {
      return {
        disabledDate: (time) => {
          return time.getTime() < this.$moment().day(6).valueOf()
        }
      }
    },
    // 多个改编 Unit
    multipleAdaptedUnits () {
      const adaptedUnits = []
      this.selectUnitIds.filter(unitId => {
        // 获取改编的单元
        const adaptedUnit = this.units.find(unit => equalsIgnoreCase(unit.unitId, unitId))
        // 如果改编的单元存在，则将改编的单元添加到改编的单元集合中
        if (adaptedUnit.adapted && adaptedUnit.adaptGroupId) {
          adaptedUnits.push(adaptedUnit)
        }
      })
      return adaptedUnits.length > 1
    },
    // 多个改编班级
    multipleAdaptedGroups () {
      // 使用 set 是为了去除符合条件的重复班级
      const repeatSet = new Set()
      this.selectUnitIds.filter(unitId => {
        // 获取改编的单元
        const adaptedUnit = this.units.find(unit => equalsIgnoreCase(unit.unitId, unitId))
        // 如果改编的单元存在，则将改编的单元添加到改编的单元集合中
        if (adaptedUnit.adapted && adaptedUnit.adaptGroupId) {
          repeatSet.add(adaptedUnit.adaptGroupId)
        }
      })
      return repeatSet.size > 1
    },
    // 改编开关
    isOpenAdaptUDLAndCLR () {
      return this.open && this.open.adaptUDLAndCLROpen
    }
  },
  created () {
    // 获取学校班级信息
    this.getGroups()
  },
  watch: {
    selectUnitIds: {
      deep: true,
      immediate: true,
      handler (value) {
        // 如果没有选择 Unit 则不显示提示信息
        if (!value || value.length === 0) {
          this.showTips = false
        }
        this.judgeShowDiffTips(value)
      }
    },
    currentGroupId (value) {
      if (value) {
        this.judgeShowDiffTips()
      }
    }
  },
  methods: {
    removeScrollEventListener () {
      const container = this.$refs.tree.$el
      if (container) {
        container.removeEventListener('scroll', this.handleScroll)
      }
    },
    addScrollEventListener () {
      this.$nextTick(() => {
        const treeElement = this.$refs.tree.$el
        if (treeElement) {
          treeElement.addEventListener('scroll', this.handleScroll)
        }
      })
    },
    handleScroll () {
      const container = this.$refs.tree.$el
      if (container.scrollTop + container.clientHeight >= container.scrollHeight - 10) {
        this.loadWeekPlanData()
      }
    },
    // 判断是否显示提示信息
    judgeShowDiffTips (selectUnitIds) {
      // 如果没有传入 selectUnitIds, 则将 selectUnitIds 重新赋值
      if (!selectUnitIds) {
        selectUnitIds = this.selectUnitIds
      }
      // 定义已改编的班级 Id 集合
      const groupIds = []
      // 遍历选中的单元
      selectUnitIds && selectUnitIds.forEach((unitId) => {
        // 获取单元信息
        const unit = this.units.find(unit => equalsIgnoreCase(unit.unitId, unitId))
        // 如果单元信息存在且单元信息中存在适配班级 ID，则将适配班级 ID 添加到已改编的班级 Id 集合中
        if (unit && unit.adaptGroupId) {
          // 将适配班级 ID 添加到已改编的班级 Id 集合中
          groupIds.push(unit.adaptGroupId)
        }
      })
      // 如果适配班级 ID 列表为空，则同样不显示提示信息
      if (groupIds.length === 0) {
        this.showTips = false
      }
      // 当前班级 ID 是否在已改编的班级 Id
      let showTipsFlg = false
      // 遍历已改编的班级 Id 集合
      for (let i = 0; i < groupIds.length; i++) {
        // 如果当前班级 ID 与已改编的班级 Id 不相等，则显示提示信息
        if (!equalsIgnoreCase(groupIds[i], this.currentGroupId)) {
          showTipsFlg = true
          break
        }
      }
      this.showTips = showTipsFlg
    },
    // 获取最近的下一个周一
    getNextMonday () {
      const today = new Date()
      // 获取当前是星期几（0-6，0表示周日，1表示周一，以此类推）
      const day = today.getDay()
      // 计算距离下一个周一的天数
      let daysUntilMonday = 1 - day + 7
      // 判断今天是否是周一
      if (day === 1) {
        // 如果今天就是周一，则需要跳过今天，找到下一个周一
        daysUntilMonday += 7
      }
      const nextMonday = new Date(today)
      // 计算下一个周一的日期
      nextMonday.setDate(today.getDate() + daysUntilMonday)
      return nextMonday
    },
    // 隐藏应用的引导
    hideApplyGuide () {
      // 隐藏引导
      this.$emit('hideStep1SimpleUnitGuide', false)
      localStorage.setItem(this.currentUser.user_id + 'SHOW_SAMPLE_UNIT_GUIDE', false)
      // 如果改编功能没有打开，则点击第一步引导后就直接发送请求设置隐藏即可
      // if (!this.isOpenAdaptUDLAndCLR) {
        const guideFeatures = {
          ...this.guideFeatures,
          showAdaptUnitAndApplyGuide: false
        }
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', guideFeatures)
        // 发起请求隐藏引导
        let result = { 'features': ['ADAPT_UNIT_AND_APPLY_GUIDE'] }
        this.$axios.post($api.urls().hideGuide, result).then()
      // }
    },
    skipUnitAdaptGuide () {
      this.$emit('callSkipUnitAdaptGuide')
    },
    // 打开应用弹窗并且重置页码和是否结束状态
    async openDialog () {
      // 初始化数据
      this.units = []
      this.isFirstLoading = true
      this.selectPlanIds = []
      this.defaultSelectedPlanIds = []
      this.selectUnitIds = []
      this.unitsLoadingEnd = false
      this.ended = false
      this.total = 0
      this.getUnitsLoading = 0
      // 点击应用 Unit 单元列表首页到周计划按钮的埋点
      if (this.applyType === 'home') {
        this.$analytics.sendEvent('web_unit_click_apply')
      }
      // 点击 Unit 详情页中的顶部的应用的埋点
      if (this.applyType === 'detail') {
        this.$analytics.sendEvent('web_unit_detail_apply')
      }
      // 点击 Unit 详情页中的周计划详情的应用的埋点
      if (this.applyType === 'weekly') {
        this.$analytics.sendEvent('web_unit_detail_week_apply')
      }
      this.dialogVisable = true
      this.pageNum = 1
      this.ended = false
      // 打开弹窗时默认不显示提示
      this.showTips = false
      this.loadWeekPlanData()
      this.startDate = this.getNextMonday()
      this.$nextTick(() => {
        // 初始化日历
        this.initCalandar(this.startDate)
        this.selectAllWeekplan(false)
      })
      const data = await Lessons2.getWeekPlanTemplateSwitch()
      this.showAutoAdapt = data.allowTeacherCustomWeeklyPlanTemplate
      this.isAutoAdapt = data.allowTeacherCustomWeeklyPlanTemplate
      this.addScrollEventListener()
    },
    // 懒加载获取单元周计划节点
    async loadWeekPlanData () {
      // 已加载完或正在加载，则不再加载
      if (this.ended || this.getUnitsLoading === 1 && this.unitsLoadingEnd) {
        return 0
      }
      this.getUnitsLoading = 1
      let units = this.units
      // 加载下一分页
      let page
      let error
      let el = this.$refs.unitTree && this.$refs.unitTree.$el || this.$refs.unitTree
      if (units && units.length > 0) {
        this.endScroll = true
      }
      // 请求参数
      let params = {}
      // 如果 Unit 存在时，则说明是点击的单个应用操作
      if (this.unit && this.unit.id) {
        params.unitId = this.unit.id
      }
      try {
        // 如果是第一次加载或者当前加载的数据小于总数据，则加载下一页
        if (this.isFirstLoading || this.units.length < this.total) {
          // 请求 Unit Planner 数据
          page = await this.$axios.post($api.urls().getUnits, {
            ...params,
            pageSize: this.pageSize,
            pageNum: this.pageNum
          })
          this.unitsLoadingEnd = true
        } else {
          this.ended = true
        }
        this.isFirstLoading = false
      } catch (e) {
        error = e
      }
      // 如果分页被重置，则丢弃本次加载结果
      if (this.units !== units) {
        return
      }
      this.getUnitsLoading = 2
      // 成功处理
      if (page && page.items) {
        page.items.forEach(item => {
          if (!item.exemplar) {
            this.$set(item, 'title', 'Unit: ' + item.title)
          }
          this.$set(item, 'isWeekPlanNode', false)
          // 初始化书节点数据
          item.planModels.forEach(plan => {
            this.$set(plan, 'title', 'Week ' + plan.week)
            this.$set(plan, 'unitId', plan.planId)
            this.$set(plan, 'isWeekPlanNode', true)
          })
        })
        this.units = units.concat(page.items)
        // 如果 Unit 存在时，需要默认选中当前的 Unit 项
        if (this.unit && this.unit.id) {
          const unit = this.units && this.units[0]
          this.defaultSelectedPlanIds.push(unit.unitId)
          const selectedPlanIds = unit.planModels.map(x => x.planId)
          this.selectUnitIds.push(unit.unitId)
          this.selectPlanIds = selectedPlanIds
          // 合并默认选中的 tree 节点
          this.defaultSelectedPlanIds = this.defaultSelectedPlanIds.concat(selectedPlanIds)
          const selectedCount = this.selectUnitIds.length + this.selectPlanIds.length
          this.initCalandar(this.startDate)
          if (this.defaultSelectedPlanIds.length === selectedCount) {
            this.selectAllWeek = true
          }
        }
        // 如果有选中的节点，在触底下拉时，需要重新设置选中的节点
        if (this.selectPlanIds.length > 0 || this.selectUnitIds.length > 0) {
          // 拷贝选中的节点
          const selectedPlanIds = JSON.parse(JSON.stringify(this.selectPlanIds))
          this.copySelectedPlanIds = selectedPlanIds
          this.$nextTick(() => {
            this.$refs.tree.setCheckedKeys(this.copySelectedPlanIds)
          })
        }
        this.treeLoading = false
        this.total = page.total
        this.pageNum++
        return page
      }
      // 失败处理
      this.ended = true
      error && error.message && this.$message.error(error && error.message)
    },
    // 关闭应用弹窗
    closeDialog () {
      this.$analytics.sendEvent('web_curriculum_apply_pop_cancel')
      this.dialogVisable = false
      this.selectAllWeek = false
      this.selectedUnits = []
      this.selectedPlans = []
      this.startDate = ''
      this.calendars = []
      this.changeCenter()
      this.selectAllWeekplan(false)
      this.$emit('clearCurrentUnit')
      this.removeScrollEventListener()
    },
    // 获取学校班级
    getGroups () {
      this.loading = true
      this.$axios({
        url: $api.urls(getCurrentUser().user_id).centersAndGroups,
        method: 'get'
      }).then(response => {
        // 如果老师所在的学校数量大于 1，显示 center 选择下拉框
        this.centers = response.filter(x => x.groups.length > 0)
        // 过滤学校班级中离校班级
        for (var i = 0; i < this.centers.length; i++) {
          this.centers[i].groups = this.centers[i].groups.filter(x => !x.inactive)
        }
        this.centers = this.centers.filter(x => x.groups.length > 0)
        // 如果所有班级都为空，直接返回
        if (this.centers.length === 0) {
          this.noGroup = true
          return
        }
        this.currentCenterId = this.centers[0].id
        // 去除离校班级
        this.groups = this.centers[0].groups.filter(x => !x.inactive)
        if (this.groups.length === 0) {
          this.noGroup = true
          return
        }
        this.noGroup = false
        this.currentGroupId = this.groups[0].id
      }).catch(error => {
      }).finally(() => {
        this.loading = false
      })
    },
    // 切换学校
    changeCenter (centerId) {
      // 通过学校 Id，切换对应的班级下拉框
      if (centerId) {
        let groups = this.centers.find(x => x.id === centerId).groups
        this.groups = groups.filter(x => !x.inactive)
        this.currentGroupId = groups[0].id
      } else {
        // 找到第一个学校和班级
        this.currentCenterId = this.centers[0].id
        let groups = this.centers[0].groups
        this.groups = groups.filter(x => !x.inactive)
        this.currentGroupId = groups[0].id
      }
    },
    // 全选周计划
    selectAllWeekplan (val) {
      if (val) {
        this.$refs.tree.setCheckedKeys(this.allUnitIds)
      } else {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    // 设置选择的周计划信息
    handleSelect () {
      let tree = this.$refs.tree
      let selectedNode = [...tree.getCheckedKeys(), ...tree.getHalfCheckedKeys()]
      let selectUnitIds = this.units.filter(unit => selectedNode.indexOf(unit.unitId) !== -1).map(x => x.unitId)
      this.selectPlanIds = selectedNode.filter(x => selectUnitIds.indexOf(x) === -1)
      this.selectUnitIds = selectUnitIds
      this.initCalandar(this.startDate)
      // 定义所有的子节点
      const childrenNode = []
      this.units.forEach(unit => {
        unit.planModels.forEach(plan => {
          childrenNode.push(plan.planId)
        })
      })
      // 如果所有的子节点都被选中，则全选
      if (this.selectPlanIds.length === childrenNode.length) {
        this.selectAllWeek = true
      } else {
        this.selectAllWeek = false
      }
    },
    // 获取单元的周计划信息
    getWeeks (unitId) {
      let unit = this.units.find(x => x.id === unitId)
      let planIds = this.selectedPlans
      let selectedPlans = unit.plans.filter(x => planIds.indexOf(x.planId) >= 0).map(x => this.$t('loc.curriculum103', { num: x.number }))
      return selectedPlans.join(', ')
    },
    // 修改应用起始日期
    changeStartDate () {
      this.initCalandar(this.startDate)
    },
    // 初始化日历信息
    initCalandar (startDate) {
      startDate = this.$moment(startDate).day(1).valueOf()
      let weeks = this.selectPlanIds.length
      let activeDays = []
      let defaultMonthNum = 6
      if (startDate) {
        let endDay = this.$moment(startDate).add(weeks * 7, 'day')
        let duration = this.$moment(endDay).diff(this.$moment(startDate), 'months')
        defaultMonthNum = duration > 6 ? duration : 6
        for (let i = 0; i < weeks * 7; i++) {
          let day = this.$moment(startDate).add(i, 'day')
          if (day.weekday() < 6 && day.weekday() > 0) {
            activeDays.push({ year: day.year(), month: day.month() + 1, day: Number(day.format('DD')) })
          }
        }
      }
      this.calendars = []
      for (let i = 0; i < defaultMonthNum; i++) {
        let calendarDate = this.$moment().add(i, 'months')
        if (startDate) {
          calendarDate = this.$moment(startDate).add(i, 'months')
        }
        let year = calendarDate.year()
        let month = calendarDate.month() + 1
        let monthActiveDays = []
        if (activeDays.length > 0) {
          activeDays.forEach(x => {
            if (x.year === year && x.month === month) {
              monthActiveDays.push(x.day)
            }
          })
        }
        this.calendars.push({ year: year, month: month, activeDays: monthActiveDays })
      }
      // 初始化日历组件数据
      this.$nextTick(() => {
        this.$refs.calendar.year = this.calendars && this.calendars[0].year
        this.$refs.calendar.month = this.calendars && this.calendars[0].month
        this.$refs.calendar.activeDays = this.calendars && this.calendars[0].activeDays
      })
    },
    // 应用周计划
    applyPlans () {
      if (this.selectPlanIds.length === 0) {
        this.$message.error(this.$t('loc.curriculum66'))
        return
      }
      if (!this.currentGroupId) {
        this.$message.error('Please select group first.')
        return
      }
      if (!this.startDate) {
        this.$message.error(this.$t('loc.curriculum67'))
        return
      }
      if (!this.startWeek) {
        this.$message.error(this.$t('loc.curriculum68'))
        return
      }
      // 判断应用之后是否需要显示批量改编的弹窗提示
      const firstPlanId = this.selectPlanIds[0]
      let isAdapted = false
      this.units.forEach(unit => {
        unit.planModels.forEach(plan => {
          // 找到第一个周计划
          if (equalsIgnoreCase(plan.planId, firstPlanId)) {
            isAdapted = plan.adapted
          }
        })
      })
      // 判断是否选择了改编的单元与所选班级不一致的情况
      // 开始日期取周一
      let startDate = this.$moment(this.startDate).day(1).valueOf()
      let params = {
        planIds: this.selectPlanIds,
        centerId: this.currentCenterId,
        groupId: this.currentGroupId,
        startDate: this.$moment(startDate).format('YYYY-MM-DD'),
        startWeek: this.startWeek,
        unitIds: this.selectUnitIds,
        aiGenerated: true,
        autoAdapt: this.isAutoAdapt
      }
      // 获取选择的单元
      const units = this.units.filter(unit => this.selectUnitIds.indexOf(unit.unitId) !== -1)
      let groupNames = ''
      let originGroupName = ''
      // 过滤出来的单元是否存在
      // 对符合条件的班级进行去重
      let repeatSet = new Set()
      if (units.length > 0) {
        units.forEach(unit => {
          // 如果改编的班级存在，且与当前选择的班级 ID 不相等时
          if (unit.adaptGroupId && !equalsIgnoreCase(unit.adaptGroupId, this.currentGroupId)) {
            if (!repeatSet.has(unit.adaptGroupName)) {
              groupNames = groupNames + unit.adaptGroupName + ','
            }
            repeatSet.add(unit.adaptGroupName)
          }
        })
        // 获取当前选择的班级名称
        const currentGroup = this.groups.find(group => equalsIgnoreCase(group.id, this.currentGroupId))
        if (currentGroup) {
          originGroupName = currentGroup.name
        }
      }
      // 判断是否展示提示
      if (this.showTips) {
        // 判断使用哪种提示，有单个 unit 情况，还有多个 unit 中单个班级和多个班级的情况
        let groupNum = repeatSet.size
        let adaptUnitPlannerTipsNum = null
        if (units.length === 1) {
          adaptUnitPlannerTipsNum = '17'
        } else if (units.length > 1) {
          if (groupNum === 1) {
            adaptUnitPlannerTipsNum = '18'
          } else if (groupNum > 1) {
            adaptUnitPlannerTipsNum = '19'
          }
        }
        // 删除最后一个逗号
        if (groupNames && groupNames !== '') {
          let idx = groupNames.lastIndexOf(',')
          if (idx !== -1) {
            groupNames = groupNames.substring(0, idx)
          }
        }
        this.$alert(this.$t('loc.adaptUnitPlanner' + adaptUnitPlannerTipsNum,{ originGroupName: originGroupName, targetGroupName: groupNames }), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          showCancelButton: true,
          customClass: 'adapted-confirmation',
          confirmButtonClass: 'el-button--danger',
          cancelButtonClass: 'is-plain',
          dangerouslyUseHTMLString: true,
          callback: action => {
            if (equalsIgnoreCase(action, 'confirm')) {
              this.applyUnit(params, isAdapted)
            }
          }
        })
      } else {
        this.applyUnit(params, isAdapted)
      }
    },
    // 应用 Unit
    applyUnit (params, isAdapted) {
      this.applying = true
      Lessons2.applyPlans(params).then(res => {
        // 确认应用单元到周计划埋点
        if (this.applyType === 'home') { // Unit 单元列表首页
          this.$analytics.sendEvent('web_unit_click_apply_confirm')
        } else { // 单元详情页点击确认应用单元
          this.$analytics.sendEvent('web_unit_detail_apply_confirm')
        }
        // 应用单元事件
        this.$analytics.sendActivityEvent('UNIT_PLANNER', 'APPLY_UNIT')
        this.applying = false
        const planId = res.planId
        this.$store.dispatch('setShowMappedTip', res.mapped) // 设置是否显示映射提示
        this.$router.push({
          name: 'edit-plan',
          params: {
            planId: planId,
            apply: true,
            isAdapted: isAdapted
          }
        })
      }).catch(error => {
        this.applying = false
        this.$message.error(error.message)
      })
    }
  }
}
</script>

<style scoped lang="less">
.m-l-r-2 {
  margin: 0 2px !important;
}

.m-b-8 {
  margin-bottom: 8px !important;
}

.m-b-16 {
  margin-bottom: 16px !important;
}

.dialog-footer {
  position: sticky;
  bottom: 0;
  text-align: center;
  background-color: #fff;
}

/deep/ .el-range-editor {
  width: 100%;
}

/deep/ .el-select {
  width: 100%;
}

/deep/ date-popper {
  background-color: chocolate;
  top: 290px;
  left: 110px;
}

/deep/ .calendar-table {
  width: 100%;
  height: 240px;
}

.max-height-300 {
  max-height: 300px;
  overflow: auto;
}

/deep/ .el-tree-node__content {
  label {
    margin-bottom: 0;
  }
}

/deep/ .el-tree {
  background-color: #FAFAFA !important;
  font-size: 16px !important;
  color: #111C1C !important;
}

/deep/ .el-checkbox__inner {
  width: 16px !important;
  height: 16px !important;
}

/deep/ .el-dialog {
  margin-top: 40px !important;
  overflow: hidden;
}

/deep/ .el-tree-node__expand-icon {
  color: #676879 !important;
  font-size: 14px !important;
}

/deep/ .el-dialog__body {
  padding: 0 20px;
  overflow: auto;
  height: calc(100% - 130px);
}

.unit-tree {
  height: 338px;
  background: #FAFAFA;
  padding: 12px 20px !important;
}

/deep/ .is-leaf::before {
    opacity: 0;
}
.select-component {
  /deep/ .el-col {
    padding-left: 7px!important;
    padding-right: 0px!important;
  }
}
/deep/ .el-input .el-input__icon {
  font-weight: 400!important;
}
.adapted-tag {
  color: var(--color-ai-assistant) !important;
  background-color: var(--color-white);
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

<style>
.popover-width-350 {
  min-width: 350px;
}

.el-date-editor .el-input__prefix .el-icon-date::before {
  font-family: 'lg-icon' !important;
  content: '\e631';
  font-size: 18px;
}

.tip-content {
  color: var(--color-text-placeholder);
  width: 80%;
  display: flex;
  flex-direction: column;
  margin: 40px 20px 20px 50px
}
.adapted-confirmation {
  width: 550px!important;
}
</style>
