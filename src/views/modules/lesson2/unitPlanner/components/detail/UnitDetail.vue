<template>
  <div>
    <div style="padding: 12px;background-color: #fff;border-radius: 8px;" v-if="!unit">
      <lesson-detail-skeleton/>
    </div>
    <div style="height: calc(100% - 64px);" class="page-top add-padding-t-4 unit-detail-container" :key="unit.id" :class="{ 'lg-margin-l-r-24': isCG && !isCurriculumPlugin }" v-else>
      <!-- 编辑以及应用单元操作 -->
      <div id="top-bar" class="lg-margin-bottom-16 display-flex-end-relative" style="gap: 12px">
        <!-- PC端按钮组 -->
        <template v-if="!isMobileView">
          <LanguageToggle class="language-toggle"
                        :style="isCurriculumPlugin ? { 'top': showBanner ? '118px' : '66px' } : {}"
                        v-model="currentLangCode" v-show="!profileOpenDropdown"
                        v-if="!isCGCurriculumHistoryEnter"/>
          <!-- Adapt 按钮需要开启功能后才会显示 -->
          <AdaptUnitTips v-show="isOpenAdaptUDLAndCLR && showApplyFeature && (!isCG || isCurriculumPlugin)"
                       :disabled="!unitUsageModel.adaptUnit"
                       :isFromUnitDetail="true"
                       :adapted="unit.adapted" :unitId="unit.id"/>
          <!-- 应用单元到周计划按钮 -->
          <!-- <UnitApply ref="unitApply" v-if="showApplyFeature && !isCG && !isCurriculumPlugin" :unit="unit" :showSimpleUnitGuide="showSimpleUnitGuide" @callSkipUnitAdaptGuide="callSkipUnitAdaptGuide" @hideSimpleUnitGuide="hideSimpleUnitGuide" applyType="detail"/> -->
          <!-- 编辑单元信息操作按钮 -->
          <UnitDownload class="btn-style btn-hover" style="height: 40px; background-color: #FFFFFF " v-if="showApplyFeature && !isMagicUnit"
                      ref="unitDownLoad"
                      :unit="unit"
                      :viewLesson="true"
                      :isFromUnitDetail="true"
                      :showDownloadResources="true"
                      :showUnitDownload="true"/>
          <el-button plain class="btn-style" @click="editUnitInfo" v-if="operateAuth() && showApplyFeature" >
            <template #icon>
              <i class="lg-icon lg-icon-edit font-size-16"></i>
            </template>
            {{$t('loc.edit')}}
          </el-button>
          <!-- adapter 按钮及其引导-->
          <el-popover
            placement="bottom"
            width="390"
            v-if="(showUnitProgress80Tip || !showApplyFeature)"
            v-model="showUnitProgress80Tip"
            ref="settingGuide"
            popper-class="adapt-UDL-and-CLR-guide-color-text"
            trigger="manual">
            <div class="text-white">
              <!-- 标题 -->
              <div class="title-font-20 lg-margin-bottom-12 word-break text-left" style="word-break:break-word;">
                {{$t('loc.editOrContinueUnitPlannerTitle')}}
              </div>
              <!-- 引导文字 -->
              <div class="lg-margin-bottom-24 word-break text-left">
                <!-- 用户引导内容 -->
                <span class="font-size-16 font-weight-400 line-height-24">{{$t('loc.editOrContinueUnitPlannerContent')}}</span>
              </div>
              <div class="display-flex flex-justify-end gap-6 align-items">
                <el-button type="primary" @click="hideUnitDetailGuide()">{{ $t('loc.gotIt') }}</el-button>
              </div>
            </div>
            <div slot="reference">
              <el-button class="ai-btn" @click="continueToGenerate()" v-if="!showApplyFeature" :disabled="!planItem">
                <template #icon>
                  <i class="lg-icon lg-icon-generate"></i>
                </template>
                {{ $t('loc.editOrContinueUnitPlannerButton') }}
              </el-button>
            </div>
          </el-popover>
          <!-- 其他（删除）操作下拉选 -->
          <div v-if="!isCGCurriculumHistoryEnter && !isMagicUnit">
            <el-popover
              placement="bottom-end"
              trigger="click">
              <div class="display-flex flex-direction-col" style="align-items: baseline">
                <el-link v-if="!isCG && !isCurriculumPlugin" class="settings-style"
                       :underline="false"
                       @click="manageClassRoomDemographics()">{{$t('loc.unitPlannerPlanItemManageClassroomDemographics')}}
                </el-link>
                <el-divider class="divider-custom" v-if="operateAuth() && !isCG && !isCurriculumPlugin"/>
                <el-link class="settings-style w-full delete-style"
                       :underline="false"
                       v-if="operateAuth()"
                       @click.native="removeUnit">
                  <span class="display-inline-block w-full">{{ $t('loc.delete') }}</span>
                </el-link>
              </div>
              <el-button slot="reference" plain class="btn-style" v-if="(!isCG && !isCurriculumPlugin) || operateAuth()">
                <template #icon>
                  <i class="lg-icon lg-icon-more-horizontal"></i>
                </template>
              </el-button>
            </el-popover>
          </div>
        </template>

        <!-- 移动端按钮 -->
        <template v-else>
          <div class="mobile-action-buttons">
            <el-button plain class="btn-style mobile-edit-btn" @click="editUnitInfo" v-if="operateAuth() && showApplyFeature" >
              <template #icon>
                <i class="lg-icon lg-icon-edit font-size-16"></i>
              </template>
              {{$t('loc.edit')}}
            </el-button>
            <el-button plain class="btn-style mobile-delete-btn" v-if="operateAuth() && !isCGCurriculumHistoryEnter && !isMagicUnit" @click="removeUnit">
              <template #icon>
                <i class="lg-icon lg-icon-delete font-size-16"></i>
              </template>
            </el-button>
            <el-button class="mobile-more-btn" @click="toggleDrawer">
              <i class="lg-icon lg-icon-more-horizontal"></i>
            </el-button>
          </div>
        </template>
      </div>
      <div class="unitDetailContent lg-border-radius-8 lg-box-shadow lg-scrollbar-show h-full" v-loading="loading">
        <!-- 单元封面 -->
        <div class="display-flex align-items unit-cover-container" id="unitDetailCover" style="margin: 35px 0 20px 0">
          <div class="unit-cover-media" style="min-width: 475px;height: 195px;align-self: baseline;">
            <curriculum-media-viewer :url="(unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].url : 'https://d2urtjxi3o4r5s.cloudfront.net/images/unitDefaultCoverK12.png'"
                                     :source="(unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].source : ''"
                                     :type="(unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].type : ''"/>
          </div>
          <div class="m-l-md curriculum-unit-box">
            <!-- 单元名称 -->
            <div class="curriculum-unit-name display-flex align-items unit-title-container" style="gap: 10px">
              <span :title="unit.title">{{ unit.title }}</span>
              <el-tag v-if="unit.adapted" class="adapted-tag font-weight-400" size="mini">{{ $t('loc.adaptUnitPlanner25') }}</el-tag>
            </div>
            <!-- 操作记录 -->
            <div class="operationRecordUser">
              <el-popover
                placement="bottom-start"
                :width="operationRecordUserPopWidth"
                :disabled="(isMC || isSharedToMagic) && unit.isSharedToMagic"
                trigger="click">
                <div style="max-height: 245px; overflow-y: auto;" v-if="showActionRecord" class="lg-scrollbar-show">
                  <el-row>
                    <el-col v-for="item in unit.unitEditRecords" :key="item.userId" class="lg-margin-bottom-8">
                  <span class="text-ellipsis" v-if="isCurriculumPlugin && !unit.exemplar" style="color: var(--color-text-primary);" :title="item.type + ' by ' + (currentPluginUser.user_name || currentUser.email) + ' on ' + item.updateAtUtc">
                    <img :src="currentPluginUser.avatar_url || item.avatarUrl" class="operationRecordImg"> {{item.type}} by {{currentPluginUser.user_name || currentUser.email}} on {{item.updateAtUtc}}
                  </span>
                  <span v-else class="text-ellipsis" style="color: var(--color-text-primary);" :title="item.type + ' by ' + item.userName + ' on ' + item.updateAtUtc">
                    <img :src="item.avatarUrl" class="operationRecordImg"> {{item.type}} by {{item.userName}} on {{item.updateAtUtc}}
                  </span>
                    </el-col>
                  </el-row>
                </div>
                <el-button style="border: none; color: var(--color-text-record);" slot="reference" class="actionRecordBtn" v-if="showActionRecord">
                <span v-if="(isMC || isSharedToMagic) && unit.isSharedToMagic" class="no-wrap title-font-14-regular"  ref="actionRecordBtnSpan">
                  <img :src="unit.unitEditRecords[unit.unitEditRecords.length - 1].avatarUrl" class="operationRecordImg"> {{unit.unitEditRecords[unit.unitEditRecords.length - 1].type}} by {{unit.unitEditRecords[unit.unitEditRecords.length - 1].userName}} on {{unit.unitEditRecords[unit.unitEditRecords.length - 1].updateAtUtc}}
                </span>
                  <span v-else class="no-wrap title-font-14-regular"  ref="actionRecordBtnSpan">
                  <span v-if="isCurriculumPlugin && !unit.exemplar" >
                    <img :src="currentPluginUser.avatar_url || unit.unitEditRecords[0].avatarUrl" class="operationRecordImg">
                    {{unit.unitEditRecords[0].type}} by {{currentPluginUser.user_name || currentUser.email}} on {{unit.unitEditRecords[0].updateAtUtc}}
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <span v-if="!(isCurriculumPlugin && !unit.exemplar)">
                    <img :src="unit.unitEditRecords[0].avatarUrl" class="operationRecordImg">
                    {{unit.unitEditRecords[0].type}} by {{unit.unitEditRecords[0].userName}} on {{unit.unitEditRecords[0].updateAtUtc}}
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                </span>
                </el-button>
              </el-popover>
            </div>
            <!-- 年龄段 -->
            <div class="m-t-sm display-flex add-margin-b-10 grade-tags-container">
              <div class="grade-tag overflow-ellipsis">
                <span>{{$t('loc.unitPlannerWeeklyAgeGroup')}}:&nbsp;</span>
                <span class="font-bold" :title="unit.grade">{{ unit.grade }}</span>
              </div>
              <div class="grade-tag overflow-ellipsis lg-margin-left-8">
                <span class="font-bold" :title="unit.frameworkAbbreviation">{{ unit.frameworkAbbreviation }}</span>
              </div>
              <div class="grade-tag overflow-ellipsis lg-margin-left-8" v-if="showClassroomType">
                <span class="font-bold" :title="getClassroomTypeLabel()">{{ getClassroomTypeLabel() }}</span>
              </div>
            </div>
            <!-- 领域信息 -->
            <div class="standards-container">
              <el-skeleton :rows="1" animated :loading="loading && !unit.standardNames" class="lesson-standards">
                <template>
                  <div v-if="unit.standardNames" class="">{{$t('loc.unitPlannerStep1Standards')}}&nbsp;({{ unit.standardNames.length }})</div>
                  <div v-if="unit.standardNames" class="">
                  <span :title="unitStandardNames(unit.standardNames)">
                    {{ unitStandardNames(unit.standardNames) }}
                  </span>
                  </div>
                </template>
              </el-skeleton>
            </div>
            <!-- 周活动信息 -->
            <div class="currriculum-unit-info unit-info-stats">
              <div class="curriculum-info-item">
                <div class="currriculum-unit-info-num">{{ unit.weeksNum || 0 }}</div>
                <div class="currriculum-unit-info-title">{{ $t('loc.curriculum7') }}</div>
              </div>
              <el-divider direction="vertical" class="curriculum-info-item-divider"></el-divider>
              <div class="curriculum-info-item">
                <div class="currriculum-unit-info-num">{{ unit.activitiesNum || 0 }}</div>
                <div class="currriculum-unit-info-title">{{ $t('loc.curriculum8') }}</div>
              </div>
              <el-divider direction="vertical" class="curriculum-info-item-divider" v-if="unit.adapted"></el-divider>
              <div class="curriculum-info-item" v-if="unit.adapted">
                <div class="currriculum-unit-info-num">{{ unit.adaptedActivityCount || 0 }}</div>
                <div class="currriculum-unit-info-title">{{ $t('loc.unitAdaptedActivities') }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- tab  tab栏 及内容-->
        <over-view-tab
          @callAdaptUnitWeekPlan="callAdaptUnitWeekPlan"
          @callEditUnitWeekPlan="callEditUnitWeekPlan"
          :currentLangCode="currentLangCode"
          :unit="unit"
          :showApply="showApplyFeature"></over-view-tab>
        <!-- 展示 LessonEditor，当点击了生成内容之后，Editor 显示  -->
        <LessonEditor ref="lessonEditor"/>
        <AdaptUnitTips ref="adaptUnitRef"
                       :hiddenAdaptBtn="true"
                       :disabled="!unitUsageModel.adaptUnit"
                       :singleAdaptOrEditLesson="true"
                       :itemId="currentItemId"
                       :unitInfo="unit"
                       :adaptedGroupId="unit.groupId"
                       :adaptedCenterId="unit.adaptedCenterId"
                       @updateAdaptUnit="updateAdaptUnit"/>
      </div>
    </div>
    <!-- Chrome 插件提示弹窗 -->
    <ChromeExtensionDialog
      ref="chromeExtensionDialog"
      :unit="unit"
      @downloadPDF="downloadPDF"
      @downloadGoogleDocs="downloadGoogleDocs"
    />
    <!-- 移动端抽屉 -->
    <el-drawer
      :visible.sync="drawerVisible"
      direction="ttb"
      size="auto"
      v-if="isMobileView"
      custom-class="unit-actions-drawer"
    >
      <div class="drawer-content">
        <div class="drawer-item" v-if="!isCGCurriculumHistoryEnter">
          <LanguageToggle
            v-model="currentLangCode"
          />
        </div>
        <div class="drawer-item" v-if="isOpenAdaptUDLAndCLR && showApplyFeature && (!isCG || isCurriculumPlugin)">
          <AdaptUnitTips
            class="drawer-adapt-unit"
            :disabled="!unitUsageModel.adaptUnit"
            :isFromUnitDetail="true"
            :adapted="unit.adapted"
            :unitId="unit.id"
          />
        </div>
        <div class="drawer-item" v-if="showApplyFeature && !isCG && !isCurriculumPlugin">
          <!-- <UnitApply
            ref="drawerUnitApply"
            class="drawer-unit-apply"
            :unit="unit"
            :showSimpleUnitGuide="showSimpleUnitGuide"
            @callSkipUnitAdaptGuide="callSkipUnitAdaptGuide"
            @hideSimpleUnitGuide="hideSimpleUnitGuide"
            applyType="detail"
          /> -->
        </div>
        <div class="drawer-item" v-if="showApplyFeature && !isMagicUnit">
          <UnitDownload
            ref="drawerUnitDownLoad"
            class="drawer-unit-download"
            :unit="unit"
            :viewLesson="true"
            :isFromUnitDetail="true"
            :showDownloadResources="true"
            :showUnitDownload="true"
          />
        </div>
        <div class="drawer-item" v-if="!showApplyFeature && planItem">
          <el-button
            class="ai-btn drawer-button"
            @click="continueToGenerate()"
          >
            <template #icon>
              <i class="lg-icon lg-icon-generate"></i>
            </template>
            {{ $t('loc.editOrContinueUnitPlannerButton') }}
          </el-button>
        </div>
        <div class="drawer-item" v-if="!isCG && !isCurriculumPlugin">
          <el-button class="drawer-button" @click="manageClassRoomDemographics()">
            {{$t('loc.unitPlannerPlanItemManageClassroomDemographics')}}
          </el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 课程模板推荐弹窗 -->
    <lesson-template-recomend
      ref="LessonTemplateRecomendRef"
      @recommendLessonTemplate="recommendLessonTemplate"
      @cancelRecommendLessonTemplate="cancelRecommendLessonTemplate"
    />
  </div>
</template>

<script>
import CurriculumMediaViewer from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMediaViewer.vue'
import OverViewTab from './OverViewTab.vue'
import Lessons2 from '@/api/lessons2'
// import UnitApply from '@/views/modules/lesson2/unitPlanner/components/unitApply/UnitApply.vue'
import { isTeacher } from '@/utils/common'
import { mapState } from 'vuex'
import AdaptUnitTips from '@/views/modules/lesson2/unitPlanner/components/adaptUnits/AdaptUnitTips.vue'
import LessonEditor from '@/views/modules/lesson2/lessonBatchAdapter/LessonEditor.vue'
import LanguageToggle from '@/views/modules/lesson2/component/LanguageToggle.vue'
import UnitDownload from '@/views/modules/lesson2/lessonLibrary/components/UnitDownload.vue'
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton'
import ChromeExtensionDialog from './ChromeExtensionDialog.vue'
import LessonTemplateRecomend from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateRecomend.vue'

export default {
  name: 'UnitDetail',
  components: {
    AdaptUnitTips,
    // UnitApply,
    CurriculumMediaViewer,
    OverViewTab,
    LessonEditor,
    LanguageToggle,
    LessonDetailSkeleton,
    // eslint-disable-next-line vue/no-unused-components
    UnitDownload,
    LessonTemplateRecomend,
    ChromeExtensionDialog
  },
  props: {
  },
  data () {
    return {
      unit: null, // 单元信息,
      loading: false, // 加载状态
      paddingTop: 60,
      showUnitProgress80Tip: false, // 是否显示单元进度 80% 继续生成引导
      progress: 0, // 单元进度
      showSimpleUnitGuide: false, // 是否显示 Simple Unit 引导
      isAvailable: false, // 是否可用
      currentItemId: '', // 当前需要改编或编辑的 itemId
      currentPlanId: '', // 当前的周计划 ID
      operationRecordUserPopWidth: 450, // 操作记录用户信息弹窗宽度
      currentLangCode: '', // 当前内容的语言码
      isNeedDetectLanguage: false, // 是否是识别语言状态
      isMobileView: false, // 是否是移动端视图
      drawerVisible: false // 抽屉是否可见
    }
  },
  created () {
    // 发送单元预览埋点事件
    this.$analytics.sendEvent('cg_unit_preview')
    // 初始化 Unit 单元详情信息
    this.initUnitInfo(true)
    this.$bus.$on('closeAdaptUnitDialog', () => {
      // 如果是关闭改编单元弹窗，则重新获取单元详情
      if (this.needReloadLesson) {
        const fullPath = this.$route.fullPath
        this.$router.replace({ path: '/temporary' }, () => {
          this.$router.replace(fullPath)
        })
      }
    })
  },
  destroyed () {
    // 退出单元详情时清除 vuex 中的内容语言类型信息
    this.$store.commit('SET_ORIGINAL_LANGUAGE', '')
    this.$store.commit('SET_CURRENT_LANGUAGE', '')
    this.$store.commit('SET_LESSON_DOWNLOAD_FILE_LANG_CODE', '')
    this.$store.dispatch('setUnitAdapted', false) // 清空单元改编状态
    this.$store.dispatch('unit/setShowSurvey', false) // 清空满意度调查问卷显示状态
    window.removeEventListener('resize', this.checkMobileView)
  },
  watch: {
    unit: {
      deep: true,
      handler (val) {
        this.$nextTick(() => {
          if (!this.$route.params.weekNum) {
            document.querySelector('.page-top').scrollIntoView()
          } else {
            this.$route.params.weekNum = undefined
          }
        })
      }
    },
    // 监听当前语言码
    currentLangCode (val) {
      if (!this.isNeedDetectLanguage) {
        // 重新获取单元详情 （翻译后的）
        this.initUnitInfo(false, this.currentLangCode)
        // 设置 vuex 中内容的当前语言码
        this.$store.commit('SET_CURRENT_LANGUAGE', this.currentLangCode)
      } else {
        this.isNeedDetectLanguage = false
      }
    },
    showUnitAdaptGuide (value) {
      this.$nextTick(() => {
        this.showSimpleUnitGuide = value
      })
    },
    // 监听当前路由
    '$route': {
      deep: true,
      immediate: true,
      handler () {
        this.initializationUnitDetail()
        // 设置是否是 Curriculum Genie 课程设计
        let curriculumGenieDesignerRouteNames = ['unit-detail-cg-desinger']
        let isGenieCurriculumToUnitPlanThird = curriculumGenieDesignerRouteNames.includes(this.$route.name)
        this.$store.commit('curriculum/SET_GENIE_CURRICULUM_TO_UNIT_PLAN_THIRD', isGenieCurriculumToUnitPlanThird)
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      // 获取创建 unit 奖励状态
      this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
      // 添加移动端检测
      this.checkMobileView()
      window.addEventListener('resize', this.checkMobileView)
      if (this.$refs.chromeExtensionDialog && this.$route.query.isComplete && !this.isMobileView) {
        this.$refs.chromeExtensionDialog.show()
      }
    })
    // 监听 unit 改编数量
    this.$bus.$on('updateUnitAdaptedActivityCount', (val) => {
      if (val > 0) {
        this.unit.adapted = true
        this.unit.adaptedActivityCount = this.unit.adaptedActivityCount + val
      }
    });
  },
  provide () {
    return {
      isPublished: () => true
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      isCG: state => state.curriculum.isCG,
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      unitUsageModel: (state) => state.magicCurriculum.unitUsageModel, // unit 使用的功能模块
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      planItem: state => state.lesson.planItem,
      open: state => state.common.open,
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      unitAdaptGuide: state => state.unit.unitAdaptGuide,
      needReloadLesson: state => state.unit.needReloadLesson,
      contentOriginalLanguage: state => state.translate.originalContentLangCode, // 当前内容的源语言码
      profileOpenDropdown: state => state.common.profileOpenDropdown, // profile 的下拉框是否打开
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      currentPluginUser: state => state.cgAuth.user, // 当前插件用户
      showBanner: state => state.cgAuth.showBanner // 是否显示 Curriculum Genie 插件安装横幅
    }),
    isSharedToMagic: {
      get () {
        return this.$store.state.user.isSharedToMagic || localStorage.getItem('isSharedToMagic') === 'true'
      },
      set (value) {
        localStorage.setItem('isSharedToMagic', value)
        this.$store.dispatch('setIsSharedToMagicAction', value)
      }
    },
    // 是否 genie 的 curriculum 历史记录页面跳转
    isCGCurriculumHistoryEnter () {
      return this.$route.name === 'unit-detail-cg-desinger'
    },
    // 当前语言是否和内容的源语言相同
    isSameLanguage () {
      return this.currentLangCode === this.contentOriginalLanguage
    },
    unitStandardNames () {
      return function (standardNames) {
        return standardNames.join('; ')
      }
    },
    showUnitAdaptGuide: {
      get () {
        return this.guideFeatures && this.guideFeatures.showAdaptUnitAndApplyGuide
      },
      set (value) {
        const guideFeatures = {
          ...this.guideFeatures,
          showAdaptUnitAndApplyGuide: value
        }
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', guideFeatures)
      }
    },
    isOpenAdaptUDLAndCLR () {
      return this.open && this.open.adaptUDLAndCLROpen
    },
    // 处理单元的更新时间格式
    formatEditDate () {
      return function (unit) {
        if (!unit || !unit.updateAtUtc) {
          return ''
        }
        let date = this.$moment.utc(unit.updateAtUtc).local()
        return date.format('LL')
      }
    },
    // 显示应用功能
    showApplyFeature () {
      return this.progress === 100
    },
    // 是否是 magic unit
    isMagicUnit () {
      return this.isMC && this.unit && this.unit.isSharedToMagic == '1'
    },
    // 是否显示记录信息
    showActionRecord () {
      return this.unit.unitEditRecords && this.unit.unitEditRecords.length > 0
    },
    isK12Grade () {
      let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
      return k12Grades.includes(this.unit.grade)
    },
    showClassroomType () {
      return this.isK12Grade && !this.unit.adaptedType
    }
  },
  methods: {    
    // 获取课堂类型标签文本
    getClassroomTypeLabel() {
      if (!this.unit.classroomType) return this.$t('loc.classroomTypeInPerson')
        
        switch (this.unit.classroomType.toUpperCase()) {
          case 'IN_PERSON':
            return this.$t('loc.classroomTypeInPerson')
          case 'VIRTUAL':
            return this.$t('loc.classroomTypeVirtual')
          default:
            return this.unit.classroomType
        }
    },
    // 设置是否显示满意度调查问卷
    handleShowPostHogSurvey() {
      // 如果单元未完成或者是范例 unit，不显示问卷
      if (this.unit.progress < 100 || this.unit.exemplar) {
        return
      }
      // 判断14天内是否已经展示过调查问卷
      const key = 'lastSurveyDate' + this.currentUser.user_id
      const lastSurveyDate = localStorage.getItem(key)
      const fourteenDaysAgo = this.$moment().subtract(14, 'days')
      if (!lastSurveyDate || this.$moment(lastSurveyDate).isBefore(fourteenDaysAgo)) {
        // 如果从未显示过问卷或者上次显示问卷时间超过14天，则显示问卷
        this.$store.dispatch('unit/setShowSurvey', true)
      } else {
        this.$store.dispatch('unit/setShowSurvey', false)
      }
    },
    downloadPDF (fileType, unit) {
      this.downloadUnit = unit
      this.$nextTick(() => {
        this.$refs.unitDownLoad && this.$refs.unitDownLoad.downloadPDF(fileType)
      })
    },
    downloadGoogleDocs (unit) {
      this.downloadUnit = unit
      this.$nextTick(() => {
        this.$refs.unitDownLoad && this.$refs.unitDownLoad.downloadGoogleDocs()
      })
    },
    // 初始化 Unit 的数据
    initializationUnitDetail () {
      // 获取 params 参数
      const params = this.$route.params
      // 如果 params 不为空，并且 itemId 和 planId 都存在时，执行如下逻辑
      if (params && params.itemId && params.planId) {
        this.$nextTick(async () => {
          this.loading = true
          // 获取单元 ID
          const unitId = this.$route.query.unitId
          const data = await Lessons2.getUnitDetail({ unitId: unitId })
          this.unit = data
          this.handleShowPostHogSurvey()
          this.$store.dispatch('setUnitAdapted', true) // 设置单元改编状态
          // 设置当前单元的基本信息
          const baseInfo = {
            authorId: this.unit.curriculumAuthorId,
            exemplar: this.unit.exemplar,
            progress: this.unit.progress
          }
          await this.$store.dispatch('unit/setUnitBaseInfo', baseInfo)
          // loading 状态为 false
          this.loading = false
          // 获取参数
          const itemId = params.itemId
          this.currentPlanId = params.planId
          this.$refs.lessonEditor.currentPlanId = params.planId
          // 设置单个改编状态为 true
          this.$refs.lessonEditor.singleAdaptLesson = true
          // 赋值当前的周计划项 ID
          this.$refs.lessonEditor.currentItemId = itemId && itemId.toUpperCase()
          // 获取周计划课程列表
          this.$refs.lessonEditor.getPlanLessons()
        })
      } else {
        this.$nextTick(async () => {
          await this.initUnitInfo()
        })
      }
    },
    // 更新改编单元
    updateAdaptUnit () {
      this.$refs.lessonEditor.currentPlanId = this.currentPlanId
      // 设置单个改编状态为 true
      this.$refs.lessonEditor.singleAdaptLesson = true
      // 赋值当前的周计划项 ID
      this.$refs.lessonEditor.currentItemId = this.currentItemId && this.currentItemId.toUpperCase()
      // 获取周计划课程列表
      this.$refs.lessonEditor.getPlanLessons()
    },
    callAdaptUnitWeekPlan (item, isLesson) {
      this.currentPlanId = item.planId
      this.currentItemId = item.id.toUpperCase()
      this.$refs.adaptUnitRef.isLesson = isLesson
      this.$refs.adaptUnitRef.openAdaptUnitDialog()
    },
    callEditUnitWeekPlan (item) {
      this.$nextTick(() => {
        this.currentItemId = item.id.toUpperCase()
        this.$refs.lessonEditor.currentPlanId = item.planId
        this.$refs.lessonEditor.singleEditLesson = true
        this.$refs.lessonEditor.currentItemId = this.currentItemId
      })
    },
    // 是否是老师
    isTeacher,
    // 初始化单元信息
    async initUnitInfo (needDetectLanguage, langCode) {
      await this.$store.dispatch('unit/setUnitAdaptGuide', false)
      // 初始化当前单元的基本信息
      await this.$store.dispatch('unit/setUnitBaseInfo', null)
      // 初始化时先将 vuex 中存储的 planItem 置空
      this.$store.dispatch('setPlanItem', null)
      // 设置状态为加载中
      this.loading = true
      // 获取 unit 单元 ID
      const unitId = this.$route.query.unitId
      // 调用获取 Unit 详情信息接口
      const data = await Lessons2.getUnitDetail({
        unitId: unitId,
        ...(needDetectLanguage && { isDetect: needDetectLanguage }),
        ...((langCode && !this.isSameLanguage) && { langCode: langCode })
      }).catch(() => {
        this.loading = false
      })
      this.unit = data
      this.handleShowPostHogSurvey()
      // 设置当前单元的基本信息
      await this.$store.dispatch('magicCurriculum/setUnitInfo', data)
      this.$store.dispatch('setUnitAdapted', data.adapted) // 清空模板类型
      // 处理 PlanCenterType
      this.handlePlanCenterType()
      // 设置当前单元的基本信息
      const baseInfo = {
        authorId: this.unit.curriculumAuthorId,
        exemplar: this.unit.exemplar,
        progress: this.unit.progress
      }
      if (langCode && !this.isSameLanguage) {
        this.$store.commit('SET_CURRENT_LANGUAGE', langCode)
      }
      // 如果返回了语言码结果，则设置
      if (needDetectLanguage && this.unit.langCode && this.unit.langCode.trim() !== '') {
        // 设置内容源语言码
        this.$store.commit('SET_ORIGINAL_LANGUAGE', this.unit.langCode)
        // 设置内容的当前语言码
        this.$store.commit('SET_CURRENT_LANGUAGE', this.unit.langCode)
        // 设置当前语言码
        this.currentLangCode = this.unit.langCode
        this.isNeedDetectLanguage = true
      }
      await this.$store.dispatch('unit/setUnitBaseInfo', baseInfo)
      // 对获取的单元详情信息的操作类型和时间进行格式化
      if (this.unit && this.unit.unitEditRecords && this.unit.unitEditRecords.length > 0) {
        this.unit.unitEditRecords = this.unit.unitEditRecords.map(item => {
          item.updateAtUtc = this.getActionRecordTime(item.updateAtUtc)
          item.type = this.getActionRecordType(item.type)
          return item
        })
        this.unit.unitEditRecords[this.unit.unitEditRecords.length - 1].type = 'Created'
      }
      let width = this.unit.unitEditRecords >= 10 ? 55 : 35
      this.operationRecordUserPopWidth = this.$refs.actionRecordBtnSpan ? this.$refs.actionRecordBtnSpan.offsetWidth + width : this.operationRecordUserPopWidth
      this.progress = data.progress
      // 设置状态为加载完成
      this.loading = false
      this.getUnitGuide()
    },

    // 处理 PlanCenterType
    handlePlanCenterType () {
      // 默认为 PS
      let type = 'PS'
      let iTGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)']
      let psGrades = ['PS/PK (3-4)', 'TK (4-5)']
      let kToGrade2 = ['K (5-6)', 'Grade 1', 'Grade 2']
      // 如果单元存在年级信息
      if (this.unit && this.unit.grade) {
        // 判断年级信息设置 PlanCenterType
        type = kToGrade2.includes(this.unit.grade) ? 'K' : type
        type = iTGrades.includes(this.unit.grade) ? 'IT' : type
        type = psGrades.includes(this.unit.grade) ? 'PS' : type
      }
      this.$store.commit('SET_PLANCENTERTYPE', type)
    },
    // 格式化之后的操作类型
    getActionRecordType (type) {
      switch (type) {
        case 'CREATE':
          return 'Created'
        case 'UPDATE':
          return 'Updated'
        default :
          return ''
      }
    },
    // 格式化之后的时间
    getActionRecordTime (time) {
      let date = this.$moment.utc(time).local()
      return date.format('MMMM Do YYYY, hh:mm a')
    },
    // 获取 Unit 单元引导信息
    async getUnitGuide () {
      // 获取用户是否需要引导
      const showUnitProgress80Tip = localStorage.getItem(this.currentUser.user_id + 'SHOW_UNIT_PROGRESS_80_TIP')
      // 如果没有缓存，则请求接口
      if (!showUnitProgress80Tip) {
        this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
          if (result) {
            // 设置缓存
            localStorage.setItem(this.currentUser.user_id + 'SHOW_UNIT_PROGRESS_80_TIP', result.showUnitProgress80Tip)
            // 设置显示状态
            if (!this.showApplyFeature) {
              this.showUnitProgress80Tip = result.showUnitProgress80Tip
            }
          }
        })
      } else {
        this.$nextTick(() => {
          // 如果是范例数据，则显示范例引导
          if (!this.showApplyFeature) {
            this.showUnitProgress80Tip = JSON.parse(showUnitProgress80Tip)
          }
        })
      }
      this.$nextTick(() => {
        this.showSimpleUnitGuide = this.showUnitAdaptGuide
      })
    },
    // 编辑 Unit 单元信息
    async editUnitInfo () {
      var canEdit = await this.checkEditStatus()
      if (!canEdit) {
        return
      }
      // 点击编辑按钮的埋点
      this.$analytics.sendEvent('web_unit_detail_edit')
      // 清空已有单元信息
      this.$store.commit('curriculum/RESET_UNIT')
      // 跳转到单元详情页
      let params = {
        unitId: this.$route.query.unitId
      }
      if (this.isCGCurriculumHistoryEnter) {
        params['week'] = 1
      }
      let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isCGCurriculumHistoryEnter ? 'lesson-overview-cg-designer' : 'load-unit-cg' : 'loadUnit'
      if (this.unit.adaptedType) {
        routeName = 'load-unit-cg-adapt'
      }
      this.$router.push({
        name: routeName,
        params: params
      })
    },
    // 检查单元的编辑状态
    async checkEditStatus () {
      var res = await Lessons2.getUnitEditStatus(this.unit.id)
      if (res) {
        let userName = res.userName
        this.$message.error(userName + this.$t('loc.unitPlannerIsEditing'))
        return false
      } else {
        return true
      }
    },
    // 跳转至班级人口统计
    manageClassRoomDemographics () {
      // 路由参数
      let query = {}
      // 判断当前 Unit 是否已经被改编
      if (this.unit.adapted) {
        query = {
          centerId: this.unit.adaptedCenterId,
          groupId: this.unit.groupId
        }
      }
      // 跳转路由
      let { href } = this.$router.resolve({
        name: 'manageChildren',
        query: query
      })
      // 打开新标签页
      window.open(href, '_blank')
    },
    // 删除 Unit 单元
    async removeUnit () {
      var canEdit = await this.checkEditStatus()
      if (!canEdit) {
        return
      }
      // 点击删除按钮的埋点
      this.$analytics.sendEvent('web_unit_detail_delete') // todo
      // this.$t('loc.unitDeleteWarning', { name: this.unit.title })
      this.$confirm(this.$t('loc.unitDeleteWarning', { name: this.unit.title }), 'Confirmation', {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        dangerouslyUseHTMLString: true,
        customClass: 'lg-message-box',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        const params = {
          unitId: this.$route.query.unitId
        }
        Lessons2.deleteUnit(params).then(res => {
          if (res.success && res.id) {
            // 删除成功之后，跳转至 Unit 列表页
            if (this.isMC) {
              // 跳转到 Unit Planner 页面
              this.$router.push('/curriculum-genie/unit-planner')
            } else {
              this.$router.push({
                name: 'unitPlanner'
              })
            }
            this.$message({
              type: 'success',
              message: 'Unit was deleted successfully.'
            })
          }
        }).catch(err => {
          console.log(err)
          this.$message.error(err.message)
        })
      }).catch(() => {
      })
    },
    // Unit 操作权限
    operateAuth () {
      // 如果当前角色是老师，则需要判断当前的 Unit 是不是本人创建的
      // 同时判断当前老师是否具有创建权限
      if (this.unit && this.unit.exemplar) {
        return false
      }
      if (this.isTeacher()) {
        return this.currentUser.user_id.toLowerCase() === this.unit.curriculumAuthorId.toLowerCase() && this.open && this.open.createUnitPlannerOpen
      } else {
        return true
      }
    },
    // 点击我知道了
    hideUnitDetailGuide () {
      // 隐藏引导
      this.showUnitProgress80Tip = false
      localStorage.setItem(this.currentUser.user_id + 'SHOW_UNIT_PROGRESS_80_TIP', false)
      // 发起请求隐藏引导
      let result = { 'features': ['SHOW_UNIT_PROGRESS_80_TIP'] }
      this.$axios.post($api.urls().hideGuide, result).then()
    },
    // 隐藏 Simple Unit 引导
    hideSimpleUnitGuide (status) {
      this.showSimpleUnitGuide = status
      this.$store.dispatch('unit/setUnitAdaptGuide', true)
    },
    // 隐藏 Simple Unit 引导，只有第一步引导
    hideStep1SimpleUnitGuide (status) {
      this.showSimpleUnitGuide = status
    },
    callSkipUnitAdaptGuide () {
      this.showSimpleUnitGuide = false
      this.showUnitAdaptGuide = false
      this.$store.dispatch('unit/setUnitAdaptGuide', false)
      // 发起请求隐藏引导
      let result = { 'features': ['ADAPT_UNIT_AND_APPLY_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then()
    },
    // 继续生成
    async continueToGenerate () {
      // 检查是否可以编辑
      const canEdit = await this.checkEditStatus()
      if (!canEdit) {
        return
      }
      // 跳转到课程详情生成
      let goNextPage = () => {
        this.$store.commit('curriculum/RESET_UNIT')
        // 如果没有未生成课程的活动，给出提示..
        if (!this.planItem) {
          // 清空已有单元信息
          // 跳转到单元详情页
          this.$router.push({
            name: this.isCG ? 'load-unit-cg' : 'loadUnit',
            params: {
              unitId: this.unit.id
            }
          })
          return
        }
        let params = {
          unitId: this.$route.query.unitId,
          itemId: this.planItem.id
        }
        this.$router.push({
          name: this.isCG ? 'lesson-detail-cg' : 'lessonDetail',
          params: params
        })
      }
      // 获取单元是否展示使用课程模板
      let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
      let showLessonTemplateTip = this.eduProtocolsTemplateApplyOpen && this.unit && !this.unit.showLessonTemplateTip && k12Grades.includes(this.unit.grade)
      // 如果单元展示使用课程模板弹窗且跳转的非 center 活动，则打开弹窗，否则直接跳转到生成课程页面
      if (showLessonTemplateTip && !this.planItem.centerId) {
        // 跳转的时候同时隐藏 dialog
        this.showLessonDialogVisible = false
        // 如果有具体的活动项，则传递 itemId
        if (this.planItem && this.planItem.id) {
          this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.openDialog(goNextPage, this.planItem.id, this.planItem.planId)
        } else {
          this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.openDialog(goNextPage)
        }
      } else {
        goNextPage()
      }
    },
    // 不使用自动推荐模板
    async cancelRecommendLessonTemplate (callback) {
      await this.updateUnitUseLessonTemplateInfo(false)
      this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.closeDialog()
      callback && callback()
    },
    // 推荐课程模板
    async recommendLessonTemplate (callback, itemId, planId) {
      let params = {
        unitId: this.$route.query.unitId,
        planId: planId,
        itemId: itemId
      }
      await this.$axios.post($api.urls().recommendLessonTemplate, params)
        .then(async () => {
          // 遍历当前周计划活动项，将推荐的课程模板赋值给对应的活动项
          await this.updateUnitUseLessonTemplateInfo(true)
          this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.closeDialog()
          callback && callback()
        })
    },
    // 更新单元是否使用课程模板信息
    async updateUnitUseLessonTemplateInfo (useTemplate) {
      let params = {
        unitId: this.unit.id,
        useTemplate: useTemplate
      }
      await this.$axios.post($api.urls().updateUnitLessonTemplateInfo, params)
    },

    // 检测是否是移动端视图
    checkMobileView () {
      this.isMobileView = window.innerWidth <= 768
    },
    // 切换抽屉可见性
    toggleDrawer () {
      this.drawerVisible = !this.drawerVisible
    },
    handleLanguageChange (langCode) {
      this.currentLangCode = langCode
      this.isNeedDetectLanguage = false
      this.initUnitInfo(false, langCode)
    },
    handleAdaptUnit () {
      // Implementation of handleAdaptUnit method
    },
    handleDownloadCommand (command) {
      // Implementation of handleDownloadCommand method
    }
  }
}
</script>

<style lang="scss" scoped>
.unitDetailContent {
  background: #fff;
  width: 100%;
  padding: 0 20px 20px 20px;
}
.btn-hover {
  &:hover {
    box-shadow: 0 0 0 3px rgba(255, 127, 65, 0.4);
    border-radius: 4px;
  }
}
.grade-tag {
  background: #DDF2F3;
  border-radius: 27px;
  padding: 4px 12px;
  height: 40px;
  line-height: 33px;
  max-width: 300px;
}

.curriculum-name {
  font-size: 24px;
  font-weight: bold;
}

.curriculum-unit-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.curriculum-unit-name {
  font-size: 24px;
  color: #323338;
  font-weight: bold;
  word-break: break-word;
}

.currriculum-unit-info {
  height: 55px;
  width: 340px;
  background: #f5f6f8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  border-radius: 15px;
  padding: 10px;
  margin-top: 16px;

  .curriculum-info-item {
    width: 49.3%;
  }

  .curriculum-info-item-divider {
    height: 100%;
  }

  .currriculum-unit-info-num {
    color: #323338;
    font-weight: 600;
    font-size: 18px;
  }

  .currriculum-unit-info-title {
    color: #323338;
    font-size: 16px;
  }
}

.currriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 18px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}

.curriculum-info-value {
  padding: 0 0 0 16px;
  margin: 10px 0;
}

.unit-header {
  display: flex;
}
/deep/ .delete-icon-style .el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover {
  color: red;
}

.lesson-standards {
  display: inline-flex;
  min-height: 32px;
  line-height: 12px;
  &> :first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    color: #FFFFFF;
    background: #EA8985;
    padding: 5px 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 110px;
    white-space: nowrap;
  }
  &> :last-child {
    color: #FD8238;
    background: #FFEEE5;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 4px 5px;
    line-height: 22px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
  }
}
.operationRecordImg{
  vertical-align: middle;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  object-fit: cover
}
.operationRecordUser{
  margin-top: 16px;
  margin-bottom: 5px;
}
.no-wrap {
  white-space: nowrap;
}
.actionRecordBtn:focus {
  border-color: white;
  background-color: white;
}
.adapted-tag {
  color: var(--color-white) !important;
  background: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
}
.settings-style{
  padding: 8px 16px;
  line-height: 24px;
}
/deep/ .divider-custom {
  margin: 0px!important;
}
.delete-style {
  display: flex;
  justify-content: start;
}
.delete-style:hover {
  color: var(--color-danger);
}
.display-flex-end-relative {
  display: flex;
  justify-content: flex-end;
}
.language-toggle {
  position: absolute;
  top: 68px;
  right: 24px;
  z-index: 1001;
}

/* 移动端适配样式 */
@media screen and (max-width: 768px) {

  .lg-scrollbar-show {
    overflow: hidden;
  }

  .unit-detail-container {
    padding: 0 12px 12px !important;
    height: calc(100% - 48px) !important;
    margin: 0 !important;
  }

  #top-bar {
    flex-wrap: wrap;
    justify-content: center !important;
    margin-bottom: 8px !important;
  }

  .language-toggle {
    position: static;
    margin-bottom: 8px;
  }

  #unitDetailCover {
    flex-direction: column;
    margin: 16px 0 10px 0 !important;
  }

  #unitDetailCover > div:first-child {
    min-width: 100% !important;
    width: 100%;
    height: auto !important;
    margin-bottom: 12px;
  }

  .curriculum-unit-box {
    margin-left: 0 !important;
  }

  .curriculum-unit-name {
    font-size: 20px;
    justify-content: center;
    align-items: center;
  }

  .m-t-sm.grade-tags-container {
    flex-wrap: nowrap;
    overflow: hidden;
    gap: 8px;
  }

  .grade-tag {
    max-width: calc(50% - 4px);
    height: auto;
    padding: 4px 8px;
    line-height: 24px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .lesson-standards {
    display: flex;
    flex-direction: row;
    width: 100%;
    &> :first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      width: 110px;
      flex-shrink: 0;
    }
    &> :last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      flex: 1;
    }
  }

  .currriculum-unit-info {
    width: 100%;
    flex-wrap: nowrap;
    height: auto;
    padding: 8px;
    .curriculum-info-item {
      width: calc(50% - 1px);
      margin: 0;
    }
    .curriculum-info-item-divider {
      display: block;
      height: 24px;
    }
  }

  .unitDetailContent {
    padding: 0 12px 12px 12px;
  }

  .operationRecordUserPopWidth {
    max-width: 300px;
  }

  .no-wrap {
    white-space: normal;
    word-break: break-all;
  }

  /* 新增移动端按钮样式 */
  #top-bar .mobile-action-buttons {
    margin-left: auto;
    display: flex;
    gap: 8px;
    .mobile-edit-btn,
    .mobile-delete-btn,
    .mobile-more-btn {
      padding: 8px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .mobile-more-btn {
      width: 36px;
      padding: 0;
      border-radius: 4px;
      i {
        font-size: 18px;
      }
    }
  }
}

/* 修复小屏手机样式 */
@media screen and (max-width: 480px) {
  .btn-style {
    padding: 8px;
    font-size: 12px;
  }
}

/* 抽屉样式 */
.unit-actions-drawer {
  .drawer-content {
    padding: 16px;
    .drawer-item {
      margin-bottom: 16px;
      width: 100%;
      &:last-child {
        margin-bottom: 0;
      }
      .drawer-button {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
      }
      .drawer-adapt-unit,
      .drawer-unit-apply,
      .drawer-unit-download {
        width: 100%;
        margin: 0;
        /deep/ .el-button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
  /deep/ .el-drawer__header {
    margin-bottom: 0;
    padding: 16px;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid #e4e7ed;
  }
  /deep/ .el-drawer__body {
    padding: 0;
    overflow-y: auto;
  }
}

.unit-resources {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 16px;
  width: 100%;

  .resource-card {
    border-radius: 8px;
    padding: 16px;
    flex: 1;
    min-width: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .resource-title {
      color: #606266;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .resource-count {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .resource-icon {
      margin-bottom: 8px;
      width: 40px;
      height: 40px;
    }
  }
}

@media screen and (max-width: 768px) {
  .unit-resources {
    flex-direction: column;

    .resource-card {
      width: 100%;
      flex-direction: column;
      padding: 12px;
    }
  }

  // ... 其他移动端样式 ...
}
</style>
