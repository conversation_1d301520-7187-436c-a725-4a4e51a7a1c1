<template>
  <div v-if="unit">
    <div
      style="position: -webkit-sticky; position: sticky; top: -1px;z-index: 999;background: #FFFFFF;font-family: Inter;font-weight:bold;" id="OverViewTab">
      <el-tabs v-model="tabName" class="customer-tab" @tab-click="goNavList">
        <!-- DEI 页签增加 new tab 显示条件 -->
          <el-tab-pane v-for="(tab, index) in tabs" :name="tab.name" :key="index">
            <div slot="label" class="tab-pane-text">
              <el-dropdown v-if="tab.refName === 'setThreeRef' && (showResourcesItem('vocabularies') || showResourcesItem('quiz'))" placement="bottom-start" :offset="100"
                           @visible-change="(val)=>{resourceDropdownTabShow = val}" @click.stop>
                <div class="resource-dropdown-tab-text" :style="{color: resourceDropdownTabShow ? '#10B3B7' : ''}" @click.stop>
                  {{ tab.name }}
                  <i class="el-icon-arrow-up" v-if="resourceDropdownTabShow"></i>
                  <i class="el-icon-arrow-down" v-else></i>
                </div>
                <el-dropdown-menu slot="dropdown" class="resource-dropdown-tab-menu">
                  <el-dropdown-item @click.native="goResourceNavList('vocabularies')" v-if="showResourcesItem('vocabularies')">{{$t('loc.curriculum15')}}</el-dropdown-item>
                  <el-dropdown-item @click.native="goResourceNavList('quiz')" v-if="showResourcesItem('quiz')">{{$t('loc.lessonQuiz1')}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <span class="overflow-hidden-text-overflow-ellipsis" :title="tab.name" v-else>{{ tab.name }}
              <span v-if="!isMC && (tab.refName === 'deiRef' && showDEIBestPracticeNewTagGuide) || (tab.refName === 'teachingTipsRef' && showTeachingTipsForLearnerProfileNewTag)" class="new-tag">
                {{ $t('loc.new') }}
              </span>
            </span>
            </div>
          </el-tab-pane>
      </el-tabs>
    </div>
    <div style="width: fit-content;">
      <!--   Overview -->
      <div ref="setOneRef" v-if="getCustomModuleOverview || !isCustomModule">
        <div style="margin-bottom: -50px"></div>
        <div style="padding-top: 50px;">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.unitOverview') }}
            </div>
            <div class="curriculum-info-value space-pre-line" style="text-align: left;" v-if="getCustomModuleOverview" v-html="getCustomModuleOverview.content"></div>
            <div class="curriculum-info-value space-pre-line" style="text-align: left;" v-else v-html="unit.overview"></div>
          </el-row>
        </div>
      </div>
      <!-- Weekly Lesson Planning -->
      <div ref="setTwoRef">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-30"></div>
        <WeeklyInfo v-if="this.unit.activitiesNum !== 0"
                    :isOnlyView="isOnlyView"
                    :groupName="unit.groupName"
                    :unitId="unit.id" :unitTitle="unit.title" :unitNum="unit.number"
                    :plans="unit.plans" :isEditor="isEditor" @changeWeek="changeWeek"
                    :exemplar="unit.exemplar" :frameworkId="unit.frameworkId"
                    @callAdaptUnitWeekPlan="callAdaptUnitWeekPlan"
                    @callEditUnitWeekPlan="callEditUnitWeekPlan"
                    :showApply="showApply" :isFromUnitDetail="true"></WeeklyInfo>
      </div>

      <!-- Resources Section -->
      <div ref="setThreeRef" v-if="showUnitResources">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <el-row class="m-t-sm m-b-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.resources') }}
            </div>
          </el-row>
          <UnitResources
            :unitId="unit.id"
            :unit="unit"
            :ageGroup="unit.grade"
            ref="unitResourcesRef"
          />
        </div>
      </div>

      <template v-if="!isCustomModule">
      <!--  Unit Trajectory -->
      <div ref="setFourRef">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.trajectory') }}
            </div>
            <div class="curriculum-info-value space-pre-line" style="text-align: left;" v-html="unit.trajectory">
            </div>
          </el-row>
        </div>
      </div>
      <!-- Concepts -->
      <div ref="setFiveRef">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <!-- Concepts -->
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.concepts') }}
            </div>
            <div class="curriculum-info-value space-pre-line" style="text-align: left;" v-html="unit.concepts">
            </div>
          </el-row>
        </div>
      </div>
      <!--   Guiding Questions -->
      <div ref="setSevenRef">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{ $t('loc.unitaddress1') }}
            </div>
            <div class="curriculum-info-value unit-list-algin space-pre-line" v-html="unit.guidingQuestions">
            </div>
          </el-row>
        </div>
      </div>
      </template>
      <template v-else>
        <!--自定义模块-->
        <div v-for="(customFoundation, index) in customFoundationExceptOverview"
             :key="'customModule' + index"
             :ref="'customModule' + index">
          <div style="margin-top: -40px;"></div>
          <div class="add-padding-t-50">
            <el-row class="m-t-sm">
              <div class="curriculum-unit-info-tag">
                {{ customFoundation.name }}
              </div>
              <div class="curriculum-info-value unit-list-algin space-pre-line" v-html="customFoundation.content">
              </div>
            </el-row>
          </div>
        </div>
      </template>

      <!--   DEI Best Practice -->
      <div ref="deiRef" v-if="this.unit && this.unit.deiBestPractice && deiOpen">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50" ref="deiDisplay" :class="{'dei-box-hidden': deiHidden}">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag">
              {{$t('loc.unitPlannerEDI')}}
            </div>
            <!-- tab 切换按钮 teacher/admin -->
            <el-tabs v-model="activeUser" class="lg-tabs-mini h-full" style="margin-top: 20px;margin-left:16px">
              <el-tab-pane style="font-size: 14px!important;" :label="'For Teachers'" name="TEACHER"></el-tab-pane>
              <el-tab-pane style="font-size: 14px!important;" :label="'For Administrators'" name="ADMINISTRATORS"></el-tab-pane>
            </el-tabs>
            <div class="curriculum-info-value unit-list-algin" v-html="deiDetails"></div>
          </el-row>
        </div>
        <!-- 底部隐藏、显示按钮 -->
        <div v-if="deiOverHeight" class="text-center">
          <el-button v-show="deiHidden" type="text" @click="deiHidden = false">{{ $t('loc.quizShowAll') }}</el-button>
          <el-button v-show="!deiHidden" type="text" @click="deiHidden = true">{{ $t('loc.hide') }}</el-button>
        </div>
      </div>
      <!--   用户自定义信息   -->
      <div v-for="(customField, index) in customFields" :key="index" :ref="'customField' + index">
        <div style="margin-top: -40px;"></div>
        <div class="add-padding-t-50">
          <el-row class="m-t-sm">
            <div class="curriculum-unit-info-tag customer-field-name">
              <span :title="customField.fieldName">{{ formatCustomFieldName(customField.fieldName) }}</span>
            </div>
            <div class="curriculum-info-value unit-list-algin space-pre-line" v-html="customField.fieldValue"></div>
          </el-row>
        </div>
      </div>

      <!--   测评点汇总数据   -->
      <div ref="collectMeasureRef">
        <div style="margin-top: -40px" class="add-padding-t-50"></div>
        <collect-measure ref="collectMeasure"
                         :unitId="unit.id"
                         :unitTitle="unit.title"
                         :currentLangCode="currentLangCode"
                         :saveMeasureloading=!allWeeklyMeasureIdsUpdatesCompleted>
        </collect-measure>
      </div>

      <!--   Teaching Tips for Learner Profile -->
      <div ref="teachingTipsRef" v-if="this.unit && this.unit.rubrics && this.unit.rubrics.length > 0 && this.unit.progress > 40">
        <div style="margin-top: -40px;" class="add-padding-t-50"></div>
        <GraduatePortraitTable :unitId="unit.id" :currentLangCode="currentLangCode" @showGraduatePortraitTab="showGraduatePortraitTab"/>
      </div>

    </div>
  </div>
</template>

<script>
import WeeklyInfo from '@/views/modules/lesson2/lessonCurriculum/CurriculumUnitDetail/components/WeeklyInfo.vue'
import { mapState, mapGetters } from 'vuex'
import CollectMeasure from '../editor/CollectMeasure'
import UnitResources from './UnitResources.vue'
import GraduatePortraitTable from '../editor/GraduatePortraitTable.vue'
import tools from '@/utils/tools'
import {equalsIgnoreCase} from '@/utils/common'

export default {
  name: 'OverViewTab',
  props: {
    isEditor: {
      type: Boolean
    },
    unit: {
      type: Object
    },
    curriculumId: {
      type: String
    },
    domains: { // 框架信息
      type: Array
    },
    showApply: {
      type: Boolean,
      default: false
    },
    isOnlyView: { // 是否是仅预览
      type: Boolean,
      default: false
    },
    allWeeklyMeasureIdsUpdatesCompleted: { // 周计划测评点是否更新完毕
      type: Boolean,
      default: true
    },
    currentLangCode: { // 当前内容的语言码
      type: String,
      default: ''
    },
  },
  components: {
    WeeklyInfo,
    CollectMeasure,
    UnitResources,
    GraduatePortraitTable
  },
  data () {
    return {
      stickyOffset: 0,
      unitPlanners: [],
      unitMaterials: null,
      unitPlannersIsLoaded: false,
      unitMaterialsIsLoaded: false,
      tabs: [
        {
          name: this.$t('loc.lesson2TabName3'),
          refName: 'setTwoRef'
        },
      ],
      tabName: this.$t('loc.lesson2TabName3'),
      weekNum: 1,
      planId: '',
      showCoreMeasureOpen: false,
      showWeeklyInfo: true,
      showDEIBestPracticeNewTagGuide: false, // DEI 显示开关
      activeUser: 'TEACHER', // tab 标签定位
      deiHidden: false, // 是否隐藏内容
      deiOverHeight: false, // 是否超过高度
      observer: null, // 在 data 中定义 observer
      unitDetailCoverDom: null,
      teachingHidden: false, // 学习素养指南
      teachingOverHeight: false, // 是否超过高度
      guidingQuestionObserver: null, // 观察引导性问题元素的观察器
      resourceDropdownTabShow: false // 资源下拉框是否是弹出状态
    }
  },
  beforeDestroy() {
    if (this.isMC) {
      window.document.removeEventListener('mousewheel', this.handleScroll, true)
      window.document.removeEventListener('DOMMouseScroll', this.handleScroll, true)
      // 这里可以停止观察
      if (this.unitDetailCoverDom && this.observe) {
        this.observer.unobserve(document.querySelector('#unitDetailCover'));
        this.observer = null;
      }
    }
    this.hideLearnerTipsNewTag()
    // 销毁引导性问题观察器
    if (this.guidingQuestionObserver) {
      this.guidingQuestionObserver.disconnect();
    }
  },
  mounted () {
    let height = this.$refs.deiDisplay && this.$refs.deiDisplay.clientHeight
    this.deiOverHeight = height > 300
    this.deiHidden = this.deiOverHeight
    if (this.isMC || this.isCurriculumPlugin) {
      this.$nextTick(() => {
        let stickyElement = document.getElementById('OverViewTab')
        this.unitDetailCoverDom = document.querySelector('#unitDetailCover')
        this.stickyOffset = stickyElement.offsetTop // 获取元素初始的顶部偏移量
        window.document.addEventListener('mousewheel', this.handleScroll, true)
        window.document.addEventListener('DOMMouseScroll', this.handleScroll, true)
        if (this.unitDetailCoverDom) {
          // 创建 Intersection Observer 实例
          this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                if (stickyElement && stickyElement.classList) {
                  stickyElement.classList.remove('sticky-active')
                }
              }
            });
          }, {
            threshold: 0.1 // 触发回调的可见性阈值，0.1 表示当 10% 的元素可见时触发
          });
          // 开始观察目标元素
          this.observer.observe(this.unitDetailCoverDom);
        }
      })
    } else {
      // Magic 没有学习者素养
      let teachingHeight = this.$refs.teachingTipsRef && this.$refs.teachingTipsRef.clientHeight
      this.teachingOverHeight = teachingHeight > 300
      this.teachingHidden = this.teachingOverHeight
    }
  },
  created() {
    // 如果为仅预览则 tab 定位到 overview
    if (this.isOnlyView) {
      this.tabName = this.$t('loc.unitOverview')
    }
    // 初始化 tab
    this.initTabs()
    // 获取 new 标签显示状态
    this.getTabGuide()
    // 初始化引导性问题观察器
    this.initGuidingQuestionObserver()
  },
  computed: {
    ...mapState({
      isCG: state => state.curriculum.isCG,
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      deiOpen: state => (state.common.open && state.common.open.deiOpen) || false, // DEI 显示开关
      showSurvey: state => state.unit.showSurvey // 是否显示满意度调查问卷
    }),
    ...mapGetters(['lgIsMobile']),

    // 是否显示单元资源模块
    showUnitResources() {
      // 单元周活动数量不为 0 并且年龄段为 K12 年龄段或者开启了 DLL 功能
      return this.unit && this.unit.activitiesNum !== 0 && (this.open && this.open.dllopen || this.isCurriculumPlugin || tools.isK12AgeGroup(this.unit.grade))
    },

    // 是否显示单元资源模块
    showUnitResources() {
      // 单元周活动数量不为 0 并且年龄段为 K12 年龄段或者开启了 DLL 功能
      return this.unit && this.unit.activitiesNum !== 0 && ((this.open && this.open.dllopen) || this.isCG || tools.isK12AgeGroup(this.unit.grade))
    },

    // 是否显示学习素养指南的新标签
    showTeachingTipsForLearnerProfileNewTag() {
      return this.guideFeatures && this.guideFeatures.showTeachingTipsForLearnerProfileNewTag
    },

    // 单元课程信息
    unitCurriculum () {
      return {
        id: this.curriculumId,
        units: [this.unit]
      }
    },
    deiDetails () {
      // 添加数据
      let dei = JSON.parse(this.unit.deiBestPractice)
      if (this.activeUser === 'TEACHER' && dei) {
        return dei.forTeacher
      } else if (this.activeUser === 'ADMINISTRATORS' && dei) {
        return dei.forAdmin
      }
    },
    customFields () {
      return this.unit.customFields.filter(customField => (customField.fieldName && customField.fieldName !== '') || (customField.fieldValue && customField.fieldValue !== ''))
        .map(customField => {
          if (!customField.fieldName || customField.fieldName === '') {
            customField.fieldName = 'Section Name'
          }
          return customField
        })
    },
    // 是否自定义模块
    isCustomModule () {
      return this.unit.customFoundationInfos && this.unit.customFoundationInfos.length > 0
    },
    // 排除 'Unit Overview' 的自定义模块
    customFoundationExceptOverview () {
      return this.unit.customFoundationInfos.filter(customFoundationInfo => customFoundationInfo.name !== 'Unit Overview')
    },
    // 查找自定义的 Overview
    getCustomModuleOverview () {
      if (this.isCustomModule) {
        const result = this.unit.customFoundationInfos.find(item => item.name === 'Unit Overview')
        return result
      } else {
        return false
      }
    },
  },
  watch: {
    // 监听单元资源模块是否显示，重新初始化 tab 标签
    showUnitResources: {
      immediate: true,
      handler() {
        this.initTabs()
      }
    }
  },
  methods: {
    // 跳转资源响应位置并打开弹框
    goResourceNavList(tabName) {
      // 页面滚动到资源模块
      this.goNavList({name: this.$t('loc.resources')})
      this.tabName = this.$t('loc.resources')
      setTimeout(() => {
        const unitResourcesRef = this.$refs.unitResourcesRef
        if (unitResourcesRef) {
          // 打开资源对应弹框
          this.$refs.unitResourcesRef.getResourceDetail(tabName)
        }
      }, 500)
    },
    
    // 判断响应的资源按钮是否可用
    showResourcesItem(item) {
      // 判空
      if (!this.unit || !this.unit.resources || this.unit.resources.length === 0) {
        return false
      }
      // 获取资源
      const resource = this.unit.resources.find(resourceItem => resourceItem.type === item)
      // 如果资源存在
      if (resource) {
        return true
      }
      return false
    },
    
    // 初始化引导性问题观察器
    initGuidingQuestionObserver() {
      if (this.showSurvey) {
        this.$nextTick(() => {
          // 等待OverViewTab组件渲染完成
          setTimeout(() => {
            const guidingQuestionElement = this.$refs.setSevenRef
            if (guidingQuestionElement) {
              // 创建IntersectionObserver实例
              this.guidingQuestionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                if (entry.isIntersecting) {
                  const key = 'lastSurveyDate' + this.currentUser.user_id
                  localStorage.setItem(key, this.$moment().toISOString())
                  // 元素进入视口，显示问卷
                  const data = {
                    event: 'SHOW_POSTHOG_SURVEY',
                    unitId: this.unit.id
                  }
                  // 向父级发送显示弹窗消息
                  this.$bus.$emit('message', data)
                  this.$store.dispatch('unit/setShowSurvey', false)
                  // 只触发一次，之后取消观察
                  this.guidingQuestionObserver.unobserve(entry.target)
                }
              })
            }, {
              threshold: 0.3 // 当30%的元素可见时触发
            });
            // 开始观察引导性问题元素
            this.guidingQuestionObserver.observe(guidingQuestionElement)
            }
          }, 100); // 给予足够的时间让组件渲染
        })
      }
    },
    // 显示校训 tab
    showGraduatePortraitTab () {
      // 判断是否存在校训 tab
      const teachingTipsIndex = this.tabs.findIndex(tab => tab.refName === 'teachingTipsRef')
      if (teachingTipsIndex !== -1) {
        return
      }
      const teachingTips = {
        name: this.$t('loc.lessonPortraitTitle'),
        refName: 'teachingTipsRef'
      }
      // 插入到 collectMeasure 后面
      const collectMeasureIndex = this.tabs.findIndex(tab => tab.refName === 'collectMeasureRef')
      if (collectMeasureIndex !== -1) {
        this.tabs.splice(collectMeasureIndex + 1, 0, teachingTips)
      } else {
        this.tabs.push(teachingTips)
      }
    },
    handleScroll() {
      let stickyElement = document.getElementById('OverViewTab')
      let scrollElement = document.querySelector('.main-container .el-scrollbar__wrap') || document.querySelector('.el-container>.el-main')
      if (!scrollElement) {
        return
      }
      let scrollTop = scrollElement.scrollTop

      if (this.lgIsMobile) {
        scrollTop = window.pageYOffset || document.documentElement.scrollTop
      }
      // 判断滚动距离是否超过 sticky 元素的初始位置
      if (this.lgIsMobile) {
        if (scrollTop >= this.stickyOffset || scrollTop < 0) {
          if (stickyElement && stickyElement.classList) {
            stickyElement.classList.add('sticky-active')
          }
          let unitDetailContent = document.querySelector('.unitDetailContent')
          let elHeader = document.querySelector('.el-header')
          let elBreadcrumb = document.querySelector('.el-breadcrumb')
          if (unitDetailContent) {
            stickyElement.style = `width: ${unitDetailContent.offsetWidth - 10}px;top: 0px`
          }
        } else {
          if (stickyElement && stickyElement.classList) {
            stickyElement.classList.remove('sticky-active')
          }
        }
      } else {
        if ((scrollTop >= this.stickyOffset || scrollTop < 0) && !this.unit.adaptedType) {
          if (stickyElement && stickyElement.classList) {
            stickyElement.classList.add('sticky-active')
          }
          let unitDetailContent = document.querySelector('.unitDetailContent')
          let elHeader = document.querySelector('.el-header')
          let elBreadcrumb = document.querySelector('.el-breadcrumb')
          if (unitDetailContent) {
            stickyElement.style = `width: ${unitDetailContent.offsetWidth - 40}px;top: ${elHeader.offsetHeight + elBreadcrumb.offsetHeight}px`
          }
        } else {
          if (stickyElement && stickyElement.classList) {
            stickyElement.classList.remove('sticky-active')
          }
        }
      }
    },
    formatCustomFieldName (customFieldName) {
      if (customFieldName && customFieldName.length > 50) {
        return customFieldName.substring(0, 50) + '...'
      }
      return customFieldName
    },
    // 初始化 tab
    initTabs () {
      // 如果不是自定义模块 或则 是自定义模块但包含 'Unit Overview' ，把 'Unit Overview' 放到第一个
      if (!this.isCustomModule || (this.isCustomModule && this.unit.customFoundationInfos.map(customFoundationInfo => customFoundationInfo.name).includes('Unit Overview'))) {
        this.tabs = [
          {
            name: this.$t('loc.unitOverview'),
            refName: 'setOneRef'
          },
          {
            name: this.$t('loc.lesson2TabName3'),
            refName: 'setTwoRef'
          }
        ]
      }

      // 如果显示单元资源模块，则添加资源模块
      if (this.showUnitResources) {
        this.tabs.push({
          name: this.$t('loc.resources'),
          refName: 'setThreeRef'
        })
      }

      // 默认模块数据
      const defaultModel = [
        {
          name: this.$t('loc.trajectory'),
          refName: 'setFourRef'
        },
        {
          name: this.$t('loc.concepts'),
          refName: 'setFiveRef'
        },
        {
          name: this.$t('loc.unitaddress1'),
          refName: 'setSevenRef'
        }
      ]

      // 判断是否是自定义模块
      if (this.isCustomModule) {
        this.customFoundationExceptOverview
          .forEach((customFoundationInfo, index) => {
            this.tabs.push({
              name: customFoundationInfo.name,
              refName: 'customModule' + index
            })
          })
      } else {
        this.tabs.push(...defaultModel)
      }

      // 如果没有生成 dei 或者 deiOpen 未开启，不显示 dei tab
      if (this.unit && this.unit.deiBestPractice && this.deiOpen) {
        const dei = {
          name: this.$t('loc.unitPlannerEDI'),
          refName: 'deiRef'
        }
        this.tabs.push(dei)
      }

      // 添加用户自定义信息 tab
      if (this.customFields) {
        this.customFields.forEach((customField, index) => {
          this.tabs.push({
            name: customField.fieldName,
            refName: 'customField' + index
          })
        })
      }

      // 添加汇总测评点 tab
      this.tabs.push({
        name: this.$t('loc.assessmentDomainMeasureList'),
        refName: 'collectMeasureRef'
      })

      // 如果单元周活动数量为0，且为仅预览则不显示周计划 tab
      if (this.unit.activitiesNum === 0 && this.isOnlyView) {
        // 查找要删除的元素索引
        const index = this.tabs.findIndex(tab => tab.name === this.$t('loc.lesson2TabName3'))
        if (index !== -1) {
          // 根据索引删除元素
          this.tabs.splice(index, 1)
        }
      }
      // 默认选中第一个
      this.tabName = this.tabs[0].name
    },
    callAdaptUnitWeekPlan (item, isLesson) {
      this.$emit('callAdaptUnitWeekPlan', item, isLesson)
    },
    callEditUnitWeekPlan (item) {
      this.$emit('callEditUnitWeekPlan', item)
    },
    // 获取 new 标签显示状态
    getTabGuide () {
      let userId = this.$store.state.magicCurriculum.userId;
      if (this.currentUser) {
        userId = this.currentUser.user_id
      }
      const showDEIBestPracticeNewTagGuide = localStorage.getItem(userId + 'DEI_BEST_PRACTICE_NEW_TAG')
      // 如果没有缓存，则请求接口
      if (!showDEIBestPracticeNewTagGuide) {
        this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
          if (result) {
            // 设置缓存
            localStorage.setItem(userId + 'DEI_BEST_PRACTICE_NEW_TAG', result.showDEIBestPracticeNewTagGuide)
            // 设置显示状态
            this.showDEIBestPracticeNewTagGuide = result.showDEIBestPracticeNewTagGuide
          }
        })
      } else {
        this.$nextTick(() => {
          // 设置显示状态
          this.showDEIBestPracticeNewTagGuide = JSON.parse(showDEIBestPracticeNewTagGuide)
        })
      }
    },
    // 隐藏 new tab
    hideNewTag () {
      // 设置隐藏缓存
      this.showDEIBestPracticeNewTagGuide = false
      let userId = this.$store.state.magicCurriculum.userId;
      if (this.currentUser) {
        userId = this.currentUser.user_id
      }
      localStorage.setItem(userId + 'DEI_BEST_PRACTICE_NEW_TAG', false)
      // 发起请求隐藏 new 标签
      let result = { 'features': ['DEI_BEST_PRACTICE_NEW_TAG'] }
      this.$axios.post($api.urls().hideGuide, result).then()
    },
    // 隐藏学习者素养 new tab
    hideLearnerTipsNewTag () {
      if (this.showTeachingTipsForLearnerProfileNewTag) {
        // 设置隐藏缓存
        let result = { 'features': ['TEACHING_TIPS_FOR_LEARNER_PROFILE_NEW_TAG'] }
        this.$axios.post($api.urls().hideGuide, result).then(res=>{
          this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
          ...this.guideFeatures,
            showTeachingTipsForLearnerProfileNewTag: false
          })
        })
      }
    },
    // 页面锚点定位
    goNavList (tab) {
      const selectedTab = this.tabs.find(t => t.name === tab.name)
      if (selectedTab) {
        // 通过this.$refs找到对应的内容区域
        const targetElement = this.$refs[selectedTab.refName]
        const element = Array.isArray(targetElement) && targetElement.length > 0 ? targetElement[0] : targetElement
        if (element) {
          // 调用scrollIntoView方法，使对应的区域滚动到可见区域
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
          let selectedIndex = this.tabs.findIndex(t => t.name === tab.name)
          if (selectedIndex > 0) {
            // 异步触发一次滚动事件
            setTimeout(() => {
              this.handleScroll()
            },300)
          }
        }
      }
    },
    // 切换周计划
    changeWeek (val) {
      this.weekNum = val.week
      this.$nextTick(() => {
        if (this.unit && this.unit.plans) {
          this.unit.plans.forEach(item => {
            if (item.number == this.weekNum) {
              this.planId = item.planId
            }
          })
        }
        if (val.scroll) {
          this.goNavList({ index: '2' })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.sticky-active {
  position: fixed !important;
  width: 1110px;
  z-index: 100;
  background: #fff;
}
@media screen and (max-width: 767px) {
  .sticky-active {
    width: 100%;
    left: 15px;
    right: 0;
    padding: 0 10px;
  }
  
  // 移动端优化tab显示
  /deep/ .el-tabs__nav {
    display: flex;
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
    
    .el-tabs__item {
      flex-shrink: 0;
      padding: 0 10px !important;
      font-size: 14px;
    }
  }
  
  // 移动端优化内容显示
  div[style="width: fit-content;"] {
    width: 100% !important;
  }
  
  // 移动端图片显示优化
  /deep/ img, /deep/ video, /deep/ iframe {
    max-width: 100% !important;
    height: auto !important;
  }
  
  // 移动端链接优化
  /deep/ a {
    word-break: break-all !important;
  }
  
  .curriculum-unit-info-tag {
    max-width: 90%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  
  .curriculum-info-value {
    padding: 0 8px !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin: 10px 0 16px 0 !important;
  }
  
  // 修复DEI标签按钮在移动端的样式
  /deep/ .lg-tabs-mini {
    margin-left: 0 !important;
    
    .el-tabs__header {
      padding: 0 !important;
    }
    
    .el-tabs__nav {
      display: flex;
      width: 100%;
      
      .el-tabs__item {
        flex: 1;
        text-align: center;
        padding: 0 !important;
        font-size: 13px !important;
      }
    }
  }
  
  // 移动端DEI和其他模块按钮样式优化
  .dei-box-hidden, .teaching-box-hidden {
    max-height: 200px !important;
  }
  
  // 自定义字段名称在移动端的显示优化
  .customer-field-name {
    max-width: 100% !important;
  }
}

// 移动端更小屏幕的进一步优化
@media screen and (max-width: 480px) {
  /deep/ .customer-tab {
    height: auto !important;
    min-height: 40px;
  }
  
  .curriculum-unit-info-tag {
    font-size: 14px !important;
    line-height: 28px !important;
    height: auto !important;
    padding: 2px 12px !important;
  }
  
  .curriculum-info-value {
    font-size: 13px !important;
  }
}

/deep/ #unit-resource-info {
  padding-top: 40px;
}

/deep/ .customer-tab {
  width: 100%;
  height: 50px;
  padding: 4px;
}

/deep/ .el-tabs__header {
  margin: 0;
}

/deep/ .customer-tab {
  width: 100%;
  height: 50px;
  padding: 4px;
}

/deep/ .el-tabs--card > .el-tabs__header .el-tabs__item {
  color: #fff;
  transition: none !important;
}

/deep/ .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background: #FFF !important;
  color: #FCBA4C !important;
  font-weight: bold;
  border-radius: 4px !important;
  transition: none !important;
}

/deep/ .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
  padding-left: 20px;
}

/deep/ .el-tabs--top .el-tabs__item.is-top:last-child {
  padding-left: 20px;
  padding-right: 20px;
}

.curriculum-unit-info {
  height: 80px;
  width: 100%;
  background: #f5f6f8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  border-radius: 15px;
  padding: 10px;

  .curriculum-info-item {
    width: 49.3%;
  }

  .curriculum-info-item-divider {
    height: 100%;
  }

  .curriculum-unit-info-num {
    color: #111111;
    font-weight: 600;
    font-size: 24px;
  }

  .curriculum-unit-info-title {
    font-size: 14px;
  }
}

.curriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 16px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}

.curriculum-info-value {
  font-size: 16px;
  padding: 0 0 0 16px;
  margin: 12px 0 24px 0;
  color: #111c1c;
  //自动换行
  word-break: break-word;

  /deep/ & {
    h1, h2, h3, h4, h5, h6 {
      line-height: 1.42 !important;
    }
  }
}

.unit-list-algin {
  text-align: left;
}

.over-top {
  display: flex;
  justify-content: space-between;
}

.desc_font {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #323338;
}

.pdfBtn {
  text-align: right;
  padding: 10px;
}

.planner-materials-over-view-dialog {
  .materials-over-view-dialog {
    /deep/ .el-dialog__body {
      overflow-y: auto;
    }
  }

  /deep/ .el-dialog {
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 95%;
    display: flex;
    flex-direction: column;
  }

  /deep/ .el-dialog__body {
    color: #606266;
    word-break: break-all;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 24px;
    font-size: 14px;

    &::-webkit-scrollbar {
      /* 滚动条整体样式 */
      width: 8px; /* 高宽分别对应横竖滚动条的尺寸 */
      height: 8px;
    }

    &::-webkit-scrollbar-thumb {
      /* 滚动条里面小方块 */
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: RGBA(182, 182, 182, 0.45);
    }

    &::-webkit-scrollbar-track {
      /* 滚动条里面轨道 */
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      background: #ededed;
    }
  }

  /deep/ .el-dialog__title {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #323338;
  }

  /deep/ .el-dialog__header {
    text-align: left;
    padding: 24px 24px 0;
  }
}

.new-tag {
  background: var(--color-danger);
  color: var(--color-white);
  border-radius: 10px;
  padding: 2px 5px;
  font-size: 12px;
  margin-left: 5px;
}
.teaching-box-hidden {
  max-height: 290px;
  overflow: hidden;
}
.dei-box-hidden {
  max-height: 300px;
  overflow: hidden;
}
.overflow-hidden-text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.tab-pane-text {
  display: flex;
  max-width: 340x;
}
.customer-field-name {
  display: flex;
  max-width: 70%;
}


// 表格样式优化
table {
  display: block;
  width: 100% !important;
  overflow-x: auto;
  border-collapse: collapse;
  
  td, th {
    min-width: 100px;
    padding: 5px !important;
    font-size: 13px !important;
  }
}

// 列表样式优化
ul, ol {
  padding-left: 20px !important;
}

// 代码样式优化
pre, code {
  white-space: pre-wrap !important;
  word-break: break-all !important;
}

// 文本样式优化
p, li, div, span {
  max-width: 100% !important;
  word-break: break-word !important;
}

// 标题样式优化
h1 {
  font-size: 20px !important;
}

h2 {
  font-size: 18px !important;
}

h3, h4, h5, h6 {
  font-size: 16px !important;
}

// 嵌套内容间距优化
p, li, div {
  > img {
    margin: 8px 0 !important;
  }
}

.resource-dropdown-tab-text {
  color: #303133;
  font-size: 14px;
  
  i {
    vertical-align: text-bottom;
  }
}

.resource-dropdown-tab-text:hover {
  color: #10B3B7;
}

.resource-dropdown-tab-menu {
  width: 230px;
  padding: 12px !important;
  margin-left: -24px;
  
  .el-dropdown-menu__item {
    height: 40px;
    line-height: 40px;
    padding: 0 12px;
    color: #111C1C;
    font-size: 16px;
    border-radius: 4px;
  }
  
  .el-dropdown-menu__item:hover {
    color: #10B3B7;
    font-weight: 600;
    background-color: #DDF2F3;
  }
  
  .el-dropdown-menu__item.is-disabled {
    color: #bbb !important;
    cursor: not-allowed !important;
    //pointer-events: unset;
    
    &:hover {
      span {
        color: #bbb !important;
      }
    }
  }
  
}
</style>
