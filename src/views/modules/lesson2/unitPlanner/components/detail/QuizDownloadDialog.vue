<template>
  <div>
    <el-button :type="buttonType" :plain="isPlain"
      @click="handleDownload" :loading="loading"
      :icon="[buttonIcon, 'lg-margin-right-4']">{{ buttonText || $t('loc.download') }}</el-button>
    <el-dialog
      :title="$t('loc.selectFileToDownload')"
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="handleClose"
      custom-class="select-download-dialog"
      :close-on-click-modal="false"
      append-to-body>
      <div class="download-options">
        <!-- 带答案 -->
        <el-checkbox
          v-model="withAnswerKey"
          @change="handleAnswerKeyChange">
          {{ $t('loc.withAnswerKey') }}
        </el-checkbox>
        <!-- 不带答案 -->
        <el-checkbox
          v-model="withoutAnswerKey"
          @change="handleWithoutAnswerKeyChange">
          {{ $t('loc.withoutAnswerKey') }}
        </el-checkbox>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="loading" @click="handleCancel">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="handleDownload" :loading="loading">{{ $t('loc.download') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Lessons2 from '@/api/lessons2'
import { mapState } from 'vuex'
import tools from '@/utils/tools'

export default {
  name: 'QuizDownloadDialog',
  props: {
    buttonType: {
      type: String,
      default: 'primary'
    },
    isPlain: {
      type: Boolean,
      default: false
    },
    buttonIcon: {
      type: String
    },
    buttonText: {
      type: String
    },
    // 单元 ID
    unitId: {
      type: String
    },
    // 课程 ID
    lessonId: {
      type: String
    },
    // 文件名
    fileName: {
      type: String
    }
  },
  data() {
    return {
      dialogVisible: false, // 是否显示下载对话框
      withAnswerKey: true, // 是否带答案
      withoutAnswerKey: true, // 是否不带答案
      loading: false // 是否正在下载
    }
  },
  computed: {
    ...mapState({
      contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
      contentOriginalLanguage: state => state.translate.originalContentLangCode // 课程源语言类型
    }),
    // 内容的当前语言是否与源语言相同
    isSameLanguage () {
      return this.contentLanguage === this.contentOriginalLanguage
    },
  },
  methods: {
    // 打开下载对话框
    handleOpen() {
      this.dialogVisible = true
      this.withAnswerKey = true
      this.withoutAnswerKey = true
    },
    // 关闭下载对话框
    handleClose() {
      this.loading = false
      this.dialogVisible = false
    },
    // 取消下载
    handleCancel() {
      this.dialogVisible = false
    },
    // 带答案选项变化
    handleAnswerKeyChange(val) {
      if (!val && !this.withoutAnswerKey) {
        // 如果尝试取消选中，且另一个选项也未选中，则阻止取消
        this.withAnswerKey = true
        this.$message.warning(this.$t('loc.atLeastOneOption'))
      }
    },
    // 不带答案选项变化
    handleWithoutAnswerKeyChange(val) {
      if (!val && !this.withAnswerKey) {
        // 如果尝试取消选中，且另一个选项也未选中，则阻止取消
        this.withoutAnswerKey = true
        this.$message.warning(this.$t('loc.atLeastOneOption'))
      }
    },
    
    // 下载前确认框
    async downloadMessageConfirm(iconUrl, fullName) {
      // 下载确认框
      let end = true
      const downloadButton = 'loc.download'
      await this.$alert(
        ` <p class="download-quiz-message-confirm">
          <img src="${iconUrl}">
          <span class="overflow-ellipsis-two" title='${fullName}'>${fullName}</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t(downloadButton)
        }).then(() => {
        end = false
      })
      return end
    },
    
    // 下载
    async handleDownload() {
      // 下载确认框
      if (this.fileName && await this.downloadMessageConfirm(require('@/assets/img/file/zip.svg'), tools.removeInvalidFileChars(this.fileName))) {
        return
      }
      
      // 如果正在下载，则返回
      if (this.loading) return
      // 设置正在下载
      this.loading = true
      try {
        // 创建下载任务参数
        const params = {}
        if (this.unitId) {
          params.unitId = this.unitId
        }
        if (this.lessonId) {
          params.lessonId = this.lessonId
        }
        if (this.withAnswerKey && this.withoutAnswerKey) {
          // 如果两个选项都选中，则下载所有
          params.type = 'ALL'
        } else if (this.withAnswerKey) {
          // 如果只选中带答案选项，则下载带答案
          params.type = 'WITH_ANSWER'
        } else {
          // 如果只选中不带答案选项，则下载不带答案
          params.type = 'WITHOUT_ANSWER'
        }
        // 如果当前语言与源语言不相同，则添加语言码
        if (!this.isSameLanguage && this.contentLanguage) {
          params.langCode = this.contentLanguage
        }
        // 创建下载任务
        const res = await Lessons2.createDownloadQuizTask(params)
        // 如果返回了 taskId，则轮询获取任务状态
        if (res && res.id) {
          // 如果返回了 taskId，则轮询获取任务状态
          this.getDownloadQuizTask(res.id)
        } else if (res && res.url) {
          this.loading = false
          this.dialogVisible = false
          window.open(res.url, '_blank')
        } else {
          this.loading = false
        }
      } catch (error) {
        this.$message.error(error.message)
      }
    },
    // 获取下载任务
    async getDownloadQuizTask(taskId) {
      // 获取下载任务
      const res = await Lessons2.getDownloadQuizTask(taskId)
      // 如果返回了 url，则打开新窗口下载
      if (res && res.url) {
        this.loading = false
        window.open(res.url, '_blank')
        this.dialogVisible = false
      } else if (res && res.status === 'FAIL') {
        // 下载失败，提示错误信息
        this.loading = false
        this.dialogVisible = false
      } else {
        // 如果未返回 url，则轮询获取任务状态
        setTimeout(() => {
          this.getDownloadQuizTask(taskId)
        }, 3000)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.download-options {
  padding: 20px;
  display: flex;
  flex-direction: row;
  gap: 40px;
}

/deep/ .select-download-dialog {
  .el-dialog__body {
    padding: 0;
  }
}
</style>

<style lang="less">
.download-quiz-message-confirm {
  word-break: break-word;
  display: flex;
  align-items: center;
  
  img {
    margin-right: 10px;
    height: 42px !important;
    width: 42px !important;
  }
}
</style>