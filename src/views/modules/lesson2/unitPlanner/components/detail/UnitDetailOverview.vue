<template>
  <div style="height: calc(100% - 16px)" class="add-padding-t-4">
    <div style="padding:12px 16px;background-color: #fff;border-radius: 8px;" v-if="!unit">
      <lesson-detail-skeleton/>
    </div>
    <div class="unitDetailContent lg-scrollbar-hidden h-full" v-else>
      <!-- 单元封面 -->
      <div class="display-flex" >
        <div style="min-width: 346px;height: 195px;align-self: baseline;">
          <curriculum-media-viewer
            :url="(unit && unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].url : 'https://d2urtjxi3o4r5s.cloudfront.net/images/unitDefaultCoverK12.png'"
            :source="(unit && unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].source : ''"
            :type="(unit && unit.coverMedias && unit.coverMedias.length > 0) ? unit.coverMedias[0].type : ''"/>
        </div>
        <div v-if="this.unit.activitiesNum !== 0" class="m-l-md curriculum-unit-box">
          <!-- 单元名称 -->
          <div class="curriculum-unit-name">
            <span :title="unit.title">{{ unit.title }}</span>
          </div>
          <!-- 年龄段 -->
          <div class="m-t-sm display-flex">
            <div class="grade-tag overflow-ellipsis">
              <span>{{$t('loc.unitPlannerWeeklyAgeGroup')}}:&nbsp;</span>
              <span class="font-bold" :title="unit.grade">{{ unit.grade }}</span>
            </div>
            <div class="grade-tag overflow-ellipsis lg-margin-left-8">
              <span class="font-bold" :title="unit.frameworkAbbreviation">{{ unit.frameworkAbbreviation }}</span>
            </div>
            <div class="grade-tag overflow-ellipsis lg-margin-left-8" v-if="showClassroomType">
              <span class="font-bold" :title="classroomTypeLabel">{{ classroomTypeLabel }}</span>
            </div>
          </div>
          <!-- 创建人 -->
          <div style="margin-top: 20px">
          </div>
          <!-- 周活动信息 -->
          <div class="currriculum-unit-info">
            <div class="curriculum-info-item">
              <div class="currriculum-unit-info-num">{{ unit.weeksNum || 0 }}</div>
              <div class="currriculum-unit-info-title">{{ $t('loc.curriculum7') }}</div>
            </div>
            <el-divider direction="vertical" class="curriculum-info-item-divider"></el-divider>
            <div class="curriculum-info-item">
              <div class="currriculum-unit-info-num">{{ unit.activitiesNum || 0 }}</div>
              <div class="currriculum-unit-info-title">{{ $t('loc.curriculum8') }}</div>
            </div>
          </div>
        </div>
        <div v-if="this.unit.activitiesNum == 0" class="m-l-md curriculum-unit-box">
          <!-- 单元名称 -->
          <div class="curriculum-unit-name">
            <span :title="unit.title">{{ unit.title }}</span>
          </div>
          <!-- 年龄段 -->
          <div class="m-t-sm display-flex">
            <div class="grade-tag overflow-ellipsis">
              <span>{{$t('loc.unitPlannerWeeklyAgeGroup')}}:&nbsp;</span>
              <span class="font-bold" :title="unit.grade">{{ unit.grade }}</span>
            </div>
            <div class="grade-tag overflow-ellipsis lg-margin-left-8">
              <span class="font-bold" :title="unit.frameworkAbbreviation">{{ unit.frameworkAbbreviation }}</span>
            </div>
            <div class="grade-tag overflow-ellipsis lg-margin-left-8" v-if="showClassroomType">
              <span class="font-bold" :title="classroomTypeLabel">{{ classroomTypeLabel }}</span>
            </div>
          </div>
          <div class="lg-margin-t-b-16">

          </div>
          <div class="currriculum-unit-info" style="background-color:#FFFFFF">

          </div>
        </div>
      </div>
      <!-- tab栏 及内容（传入仅预览标志）-->
      <over-view-tab :unit="unit" :showApply="showApplyFeature" :allWeeklyMeasureIdsUpdatesCompleted="allWeeklyMeasureIdsUpdatesCompleted" :isOnlyView="true" ></over-view-tab>
    </div>
  </div>
</template>

<script>
import CurriculumMediaViewer from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMediaViewer.vue'
import OverViewTab from './OverViewTab.vue'
import Lessons2 from '@/api/lessons2'
import { isTeacher } from '@/utils/common'
import { mapState } from 'vuex'
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton'
import tools from '@/utils/tools'

export default {
  name: 'UnitDetailOverview',
  props: {
    unitId: {
      type: String
    },
    allWeeklyMeasureIdsUpdatesCompleted: {
      type: Boolean,
      default: true
    }
  },
  components: {
    CurriculumMediaViewer,
    OverViewTab,
    LessonDetailSkeleton
  },
  data () {
    return {
      unit: null, // 单元信息,
      loading: false, // 加载状态
      paddingTop: 60,
      showUnitProgress80Tip: false, // 是否显示单元进度 80% 继续生成引导
      progress: 0, // 单元进度
      showSimpleUnitGuide: false, // 是否显示 Simple Unit 引导
      isAvailable: false // 是否可用
    }
  },
  created () {
    // 初始化 Unit 单元详情信息
    this.initUnitInfo()
  },
  watch: {
    unit: {
      deep: true,
      handler (val) {
        this.$nextTick(() => {
          if (!this.$route.params.weekNum) {
            document.querySelector('.unitDetailContent').scrollIntoView()
          } else {
            this.$route.params.weekNum = undefined
          }
        })
      }
    }
  },
  provide () {
    return {
      isPublished: () => true
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      planItem: state => state.lesson.planItem,
      open: state => state.common.open
    }),
    // 处理单元的更新时间格式
    formatEditDate () {
      return function (unit) {
        if (!unit || !unit.updateAtUtc) {
          return ''
        }
        let date = this.$moment.utc(unit.updateAtUtc).local()
        return date.format('LL')
      }
    },
    // 显示应用功能
    showApplyFeature () {
      return this.progress === 100
    },
    showClassroomType () {
      return tools.isK12AgeGroup(this.unit.grade) && !this.unit.adaptedType
    },
    classroomTypeLabel () {
      if (!this.unit.classroomType) return this.$t('loc.classroomTypeInPerson')
      switch (this.unit.classroomType.toUpperCase()) {
        case 'IN_PERSON':
          return this.$t('loc.classroomTypeInPerson')
        case 'VIRTUAL':
          return this.$t('loc.classroomTypeVirtual')
        default:
          return this.unit.classroomType
      }
    }
  },
  methods: {
    // 是否是老师
    isTeacher,
    // 初始化单元信息
    async initUnitInfo () {
      // 设置状态为加载中
      this.loading = true
      // 获取 unit 单元 ID
      // 调用获取 Unit 详情信息接口
      const data = await Lessons2.getUnitDetail({ unitId: this.unitId })
      this.unit = data
      this.$store.dispatch('setUnitAdapted', data.adapted) // 清空模板类型
      // 处理 PlanCenterType
      this.handlePlanCenterType()
      this.progress = data.progress
      // 设置状态为加载完成
      this.loading = false
    },

    // 处理 PlanCenterType
    handlePlanCenterType () {
      // 默认为 PS
      let type = 'PS'
      let iTGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)']
      let psGrades = ['PS/PK (3-4)', 'TK (4-5)']
      let kToGrade2 = ['K (5-6)', 'Grade 1', 'Grade 2']
      // 如果单元存在年级信息
      if (this.unit && this.unit.grade) {
        // 判断年级信息设置 PlanCenterType
        type = iTGrades.includes(this.unit.grade) ? 'IT' : type
        type = psGrades.includes(this.unit.grade) ? 'PS' : type
        type = kToGrade2.includes(this.unit.grade) ? 'K' : type
      }
      this.$store.commit('SET_PLANCENTERTYPE', type)
    },

    // 检查单元的编辑状态
    async checkEditStatus () {
      var res = await Lessons2.getUnitEditStatus(this.unit.id)
      if (res) {
        let userName = res.userName
        this.$message.error(userName + this.$t('loc.unitPlannerIsEditing'))
        return false
      } else {
        return true
      }
    }
  },
  destroyed () {
    this.$store.dispatch('setUnitAdapted', false) // 清空单元改编状态
  }
}
</script>

<style lang="less" scoped>
@media screen and (max-width: 768px) {
  .unitDetailContent {
    > div {
      flex-direction: column;
      > div {
        margin-bottom: 10px;
      }
      > div:last-child {
        margin-left: 0;
      }
    }
  }
}

.unitDetailContent {
  background: #fff;
  width: 100%;
}
.grade-tag {
  background: #DDF2F3;
  border-radius: 27px;
  padding: 4px 12px;
  height: 40px;
  line-height: 33px;
  max-width: 300px;
}

.curriculum-name {
  font-size: 24px;
  font-weight: bold;
}

.curriculum-unit-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.curriculum-unit-name {
  font-size: 24px;
  color: #323338;
  font-weight: bold;
  //自动换行
  word-break: break-word;
  // display: -webkit-box;
  // overflow: hidden;
  // -webkit-line-clamp: 4;
  // -webkit-box-orient: inherit;
  //宽度80%
  // width: 100%;
}

.currriculum-unit-info {
  height: 65px;
  width: 360px;
  background: #f5f6f8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  border-radius: 15px;
  padding: 10px;

  .curriculum-info-item {
    width: 49.3%;
  }

  .curriculum-info-item-divider {
    height: 100%;
  }

  .currriculum-unit-info-num {
    color: #323338;
    font-weight: 600;
    font-size: 18px;
  }

  .currriculum-unit-info-title {
    color: #323338;
    font-size: 16px;
  }
}

.currriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 18px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}

.curriculum-info-value {
  padding: 0 0 0 16px;
  margin: 10px 0;
}

.unit-header {
  display: flex;
}
/deep/ .delete-icon-style .el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover {
    color: red;
}
/deep/ .media-viewer {
  border-radius: 8px!important;
}
</style>
