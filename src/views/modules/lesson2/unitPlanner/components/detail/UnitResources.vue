<template>
  <div>
    <el-row :gutter="20" class="resource-items" v-loading="loadingData">
      <el-col v-for="(i, index) in resources" :key="index" :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <div class="resource-item lg-pointer" :class="'rescouce-item-' + i.type" @click="getResourceDetail(i.type)">
          <div>{{ i.name }}</div>
          <div class="rescouce-num">{{ i.num }}</div>
          <div class="display-flex" style="height: 40px">
            <div>
              <el-button :class="'rescouce-more-' + i.type" round size="small" class="rescouce-more m-t-sm">
                {{ $t('loc.moreButten') }}
              </el-button>
            </div>
            <div class="rescouce-icon">
              <el-avatar size="large" shape="square" :src="i.icon"></el-avatar>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 词汇列表弹窗 -->
    <el-dialog
      :title="$t('loc.curriculum15')"
      :visible.sync="vocabularyDialogVisible"
      :close-on-click-modal="false"
      width="60%"
      append-to-body
      :before-close="handleVocabularyClose"
      custom-class="vocabulary-dialog">
      <div v-if="vocabularyList.length > 0">
        <div v-for="(week, weekIndex) in vocabularyList" :key="weekIndex" class="week-container">
          <!-- 周标题 -->
          <div class="week-header">
            {{ week.name }}
          </div>
          <!-- 表格内容 -->
          <div class="week-table">
            <div class="day-content" v-for="(day, dayIndex) in week.days" :key="dayIndex">
              <div class="day-row">
                <div class="day-title">{{ day.name }}</div>
                <div class="day-table">
                  <div v-for="(lesson, lessonIndex) in day.lessons" :key="lessonIndex" class="lesson-block">
                    <div class="lesson-title">{{ lesson.name }}</div>
                    <ul class="vocabulary-list">
                      <li v-for="(item, index) in lesson.items" :key="index" class="vocabulary-item">
                        {{ item }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <lg-empty-page v-else>
        <template slot="custom-content">
          <div style="height: 50px;"></div>
        </template>
      </lg-empty-page>
      <div v-if="vocabularyList.length > 0" slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="exportLoading" @click="exportToCSV" icon="lg-icon lg-icon-excel">&nbsp;{{ $t('loc.downloadCSV') }}</el-button>
      </div>
    </el-dialog>

    <!-- Formative Assessment 弹窗 -->
    <el-dialog
      :title="$t('loc.lessonQuiz1')"
      :visible.sync="quizListDialogVisible"
      :close-on-click-modal="false"
      width="80%"
      append-to-body
      :before-close="handleQuizListClose"
      custom-class="quiz-list-dialog">
      <div v-if="quizList.length > 0" class="quiz-list-dialog-body">
        <!-- 选择 Quiz 的列表 -->
        <el-collapse v-model="activeNames" class="quiz-menu lg-scrollbar">
          <el-collapse-item :name="weekIndex" v-for="(week, weekIndex) in quizList" :key="weekIndex">
            <template slot=title>
              <!-- 周计划名称 -->
              <div class="quiz-collapse-item-title" :title="week.name">
                {{ week.name }}
              </div>
            </template>
            <!-- 课程名选项 -->
            <div v-for="(day, dayIndex) in week.days" :key="dayIndex">
              <div v-for="(lesson, lessonIndex) in day.lessons" :title="lesson.name" :key="lessonIndex"
                   @click="handleDetailsClick(week.weekNum, day.name, lesson)" class="quiz-item-content"
                   :class="{'quiz-item-content-active': lesson.lessonId === currentLesson.lessonId}">
                <div class="quiz-item-content-name">{{ lesson.name }}</div>
                <div class="quiz-item-content-day">{{ day.name }}</div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      
        <div class="lesson-quiz-body lg-scrollbar position-relative">
          <div class="quiz-title">
            {{ currentLesson.name }}
          </div>
          <LessonQuiz
            v-if="currentLesson"
            v-model="currentLessonQuiz"
            :ageGroupName="ageGroup"
            :overflow-hidden="false"
            :edit="false"
            :showDownload="false"/>
        </div>
      </div>
      <lg-empty-page v-else>
        <template slot="custom-content">
          <div style="height: 50px;"></div>
        </template>
      </lg-empty-page>
      <div v-if="quizList.length > 0" slot="footer" class="quiz-dialog-footer">
        <el-tooltip :content="$t('loc.downloadAllTip')" placement="top" :open-delay="500" :disabled="lgIsMobile">
          <quiz-download-dialog :unitId="unitId" :buttonText="$t('loc.downloadAll')" :fileName="unit && unit.title ? 'Formative Assessment_' + unit.title  + '.zip' : ''"
                                button-icon="lg-icon lg-icon-download"/>
        </el-tooltip>
        <quiz-download-dialog :lessonId="currentLesson && currentLesson.lessonId" :fileName="currentLesson && currentLesson.content ? 'Formative Assessment_' + currentLesson.content  + '.zip' : ''"
                              :buttonText="$t('loc.downloadCurrent')" button-icon="lg-icon lg-icon-download"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import keyVocabularies from '@/assets/img/lesson2/curriculum/key_vocabularies.png'
import quiz from '@/assets/img/lesson2/curriculum/quiz.png'
import tools from '@/utils/tools'
import { mapState } from 'vuex'
import Lessons2 from '@/api/lessons2'
import LessonQuiz from '@/views/modules/lesson2/lessonLibrary/editor/LessonQuiz.vue'
import QuizDownloadDialog from './QuizDownloadDialog.vue'
import LgEmptyPage from '@/components/LgEmptyPage.vue'

  export default {
    name: 'UnitResources',
    components: {
      LessonQuiz,
      QuizDownloadDialog,
      LgEmptyPage
    },
    props: {
      unitId: {
        type: String,
        required: true
      },
      ageGroup: {
        type: String,
        required: true
      },
      unit: {
        type: Object,
        default: () => {}
      }
    },
    data () {
      return {
        loadingData: false, // 是否正在加载数据
        resources: [], // 资源列表
        vocabularyDialogVisible: false, // 词汇列表弹窗是否显示
        vocabularyList: [], // 词汇列表
        quizListDialogVisible: false, // 问卷列表弹窗是否显示
        quizList: [], // 问卷列表
        quizDetailTitle: '', // 问卷详情标题
        currentLesson: null, // 当前课程
        currentLessonQuiz: null, // 当前问卷
        formattedWeeks: [], // 存储格式化后的资源数据
        vocabularyCount: 0, // 词汇总数
        questionCount: 0, // 问卷总数
        exportLoading: false, // 添加导出loading状态
        activeNames: [] // quiz 选择列表的打开项
      }
    },
    watch: {
      // 监听单元 id, 初始化资源
      contentLanguage: {
        immediate: true,
        handler() {
          this.initResources()
        }
      },
      dllOpen: {
        immediate: true,
        handler() {
          this.initResourcNumber()
        }
      },
      unit: {
        deep: true,
        handler() {
          // 设置资源数据到单元
          this.$set(this.unit, 'resources', this.resources)
        }
      }
    },
    mounted () {
      // 初始化资源数量
      this.initResourcNumber()
    },
    computed: {
      ...mapState({
        open: state => state.common.open,
        isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
        isCG: state => state.curriculum.isCG,
        contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
        contentOriginalLanguage: state => state.translate.originalContentLangCode, // 课程源语言类型
        lgIsMobile: state => state.common.lgIsMobile, // 添加移动端状态
      }),
      // 内容的当前语言是否与源语言相同
      isSameLanguage () {
        return this.contentLanguage === this.contentOriginalLanguage
      },
      // 是否打开 dll
      dllOpen () {
        return this.isCurriculumPlugin || (this.open && this.open.dllopen) || this.isCG
      }
    },
    methods: {
      // 初始化资源数量
      initResourcNumber () {
        // 初始化资源列表
        let resources = []
        // 如果打开 dll，则添加词汇模块
        if (this.dllOpen) {
          resources.push({
            'name': this.$t('loc.curriculum15'),
            'type': 'vocabularies',
            'num': this.vocabularyCount,
            'icon': keyVocabularies
          })
        }
        // 只有 k12 或者 TK（4-5） 才显示 quiz 模块
        if (tools.isK12AgeGroup(this.ageGroup) || tools.isExtendedAgeGroup(this.ageGroup)) {
          resources.push({
            'name': this.$t('loc.lessonQuiz1'),
            'type': 'quiz',
            'num': this.questionCount,
            'icon': quiz
          })
        }
        this.resources = resources
        // 设置资源数据到单元
        this.$set(this.unit, 'resources', resources)
      },
      // 初始化资源数据
      async initResources() {
        try {
          // 设置加载中
          this.loadingData = true
          // 获取单元资源
          let params = {
            unitId: this.unitId
          }
          // 如果当前语言与源语言不相同，则添加语言码
          if (!this.isSameLanguage && this.contentLanguage) {
            params.langCode = this.contentLanguage
          }
          const res = await Lessons2.getUnitResources(params)
          // 设置加载完成
          this.loadingData = false
          // 如果返回了数据，则转换数据
          if (res && res.weeks) {
            // 转换后端数据为前端需要的格式
            this.formattedWeeks = res.weeks.map(week => ({
              name: `Week ${week.weekNum}: ${week.weekName}`,
              id: week.weekId,
              weekNum: week.weekNum,
              // 转换天数据
              days: week.days.map(day => ({
                name: this.getDayName(day.dayIndex),
                // 转换lesson数据
                lessons: day.lessons.map(lesson => ({
                  ...lesson,
                  name: lesson.lessonName,
                  content: lesson.lessonName,
                  items: lesson.vocabularies || [],
                  questions: lesson.questions || []
                }))
              }))
            }))

            // 更新统计数量
            this.vocabularyCount = res.vocabularyCount || 0
            this.questionCount = res.questionCount || 0
            // 初始化资源数量
            this.initResourcNumber()
          }
        } catch (error) {
          // 设置加载完成
          this.loadingData = false
          // 提示错误信息
          if (error.response && error.response.data && error.response.data.error_message) {
            this.$message.error(error.response.data.error_message)
          }
        }
      },
      // 获取资源详情
      getResourceDetail (type) {
        // 如果正在加载数据，则返回
        if (this.loadingData) return
        // 根据类型切换弹窗
        switch (type) {
          case 'vocabularies':
            // 如果词汇数量为0，则清空词汇列表
            if (this.vocabularyCount === 0) {
              this.vocabularyList = []
            } else {
              // 过滤掉没有词汇的lesson、day、week
              let vocabularyList = JSON.parse(JSON.stringify(this.formattedWeeks))
              vocabularyList.forEach(week => {
                week.days.forEach(day => {
                  day.lessons = day.lessons.filter(lesson => lesson.vocabularies.length > 0)
                })
                week.days = week.days.filter(day => day.lessons.length > 0)
              })
              vocabularyList = vocabularyList.filter(week => week.days.length > 0)
              this.vocabularyList = vocabularyList
            }
            this.vocabularyDialogVisible = true
            break
          case 'quiz':
            // 如果问卷数量为0，则清空问卷列表
            if (this.questionCount === 0){
              this.quizList = []
            } else {
              // 过滤掉没有问卷的lesson、day、week
              let quizList = JSON.parse(JSON.stringify(this.formattedWeeks))
              quizList.forEach(week => {
                week.days.forEach(day => {
                  day.lessons = day.lessons.filter(lesson => lesson.questions.length > 0)
                })
                week.days = week.days.filter(day => day.lessons.length > 0)
              })
              quizList = quizList.filter(week => week.days.length > 0)
              this.quizList = quizList
              // 默认选择第一个 quiz
              if (this.quizList.length > 0) {
                this.currentLesson = this.quizList[0].days[0].lessons[0]
                this.currentLessonQuiz = this.currentLesson.questions
              }
            }
            this.activeNames = this.quizList.map((_, index) => index);
            this.quizListDialogVisible = true
            break
        }
      },
      // 获取星期几的名称
      getDayName(dayIndex) {
        const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        return days[dayIndex - 1] || `Day ${dayIndex}`
      },
      // 关闭词汇列表弹窗回调
      handleVocabularyClose() {
        this.vocabularyDialogVisible = false
      },
      // 关闭问卷列表弹窗回调
      handleQuizListClose() {
        this.quizListDialogVisible = false
      },
      // 导出词汇 CSV
      async exportToCSV() {
        // 下载确认框
        if (await this.downloadMessageConfirm(require('@/assets/img/file/csv.svg'), tools.removeInvalidFileChars('Key Vocabularies ' + this.unit.title + '.csv'))) {
          return
        }
        
        // 如果正在导出，则返回
        if (this.exportLoading) return
        // 设置正在导出
        this.exportLoading = true
        try {
          // 导出词汇
          let params = {
            unitId: this.unitId
          }
          // 如果当前语言与源语言不相同，则添加语言码
          if (!this.isSameLanguage && this.contentLanguage) {
            params.langCode = this.contentLanguage
          }
          const res = await Lessons2.exportVocabularies(params)
          // 如果返回了 url，则打开新窗口下载
          if (res && res.url) {
            window.open(res.url, '_blank')
          }
        } catch (error) {
          // 提示错误信息
          this.$message.error(error.message)
        } finally {
          // 设置导出完成
          this.exportLoading = false
        }
      },
      // 处理 Details 按钮点击
      handleDetailsClick(weekNum, dayName, lesson) {
        // 设置问卷详情标题
        this.quizDetailTitle = `Week ${weekNum} - ${dayName}: ${lesson.name}`
        // 设置当前问卷
        this.currentLesson = lesson
        // 设置当前问卷的问卷
        this.currentLessonQuiz = lesson.questions
      },
  
      // 下载前确认框
      async downloadMessageConfirm(iconUrl, fullName) {
        // 下载确认框
        let end = true
        const downloadButton = 'loc.download'
        await this.$alert(
          ` <p class="download-message-confirm">
          <img src="${iconUrl}">
          <span class="overflow-ellipsis-two" title='${fullName}'>${fullName}</span>
        </p>`,
          this.$t('loc.lessons2DownloadFile'), {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t(downloadButton)
          }).then(() => {
          end = false
        })
        return end
      },
    }
  }
  </script>

  <style lang="less" scoped>
  .resource-items {
    color: #676879;
    font-size: 16px;
    display: flex;
    flex-wrap: wrap;
    row-gap: 20px
  }
  .resource-item {
    height: 100%;
    margin-bottom: 10px;
    padding: 24px;
    background: #F2F6FE;
    border-radius: 20px;
    position: relative;
  }
  .rescouce-item-vocabularies {
    background: #EFF9FF;
  }
  .rescouce-item-quiz {
    background: #FDF6ED;
  }
  .rescouce-num {
    height: 24px;
    font-size: 24px;
    color: #323338;
    font-weight: 500;
  }
  .rescouce-more {
    position: absolute;
    bottom: 24px;
    color:#FFF;
    font-size: 14px;
    font-weight: 500;
    border: none;
  }
  .rescouce-icon {
    background: #FFF;
    border-radius: 15px;
    padding: 5px;
    text-align: center;
    margin-bottom: 10px;
    right: 24px;
    bottom: 16px;
    position: absolute;
    height: 70px;
    width: 70px;
    /deep/ .el-avatar {
      background: #FFF;
    }
    /deep/ .el-avatar--large {
      width: 60px;
      height: 60px;
      line-height: 60px;
    }
  }
  .rescouce-more-vocabularies {
    background: #97D7FE;
  }
  .rescouce-more-quiz {
    background: #F9BB80;
  }

  /deep/ .el-col-8 {
    /**兼容safari 浏览器 */
    width: 33.3%;
  }

  .vocabulary-card {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);

    .vocabulary-image {
      width: 100%;
      height: 120px;
      border-radius: 4px;
      overflow: hidden;

      .el-image {
        width: 100%;
        height: 100%;
      }
    }

    .vocabulary-text {
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 500;
      color: #333;
      text-align: center;
      background: #f5f7fa;
      border-radius: 4px;
    }
  }

  .empty-text {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
  }

  /* 最后一周不需要设置margin-bottom */
  .week-container:not(:last-child) {
    margin-bottom: 24px;
  }

  .week-header {
    line-height: 34px;
    height: 34px;
    padding: 0 16px;
    font-size: 16px;
    font-weight: 500 !important;
    color: #ffffff;
    width: fit-content;
    background: #10b3b7;
    border-radius: 16px;
    margin-bottom: 10px;
  }

  .week-table {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .day-content {
    padding: 0;
    border-bottom: 1px solid #dcdfe6;

    &:last-child {
      border-bottom: none;
    }
  }

  .day-row {
    display: flex;
  }

  .day-title {
    width: 120px;
    padding: 12px 24px;
    background: #EFF9FF;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    border-right: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    justify-content: center;
    word-break: keep-all;
    white-space: normal;
    text-align: center;
  }

  .day-table {
    flex: 1;
    padding: 12px 12px;
  }

  .day-table-quiz {
    flex: 1;
    padding: 12px 12px;
    line-height: 24px;
  }


  .lesson-block {
    &:not(:last-child) {
      margin-bottom: 16px;
    }
  }

  .lesson-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }

  .vocabulary-list {
    margin: 0;
    padding: 0;
    list-style-type: disc;
    padding-left: 20px;

    .vocabulary-item {
      line-height: 1.8;
      font-size: 14px;
      position: relative;
      padding: 0;
      list-style-type: disc;

      &::before {
        display: none;
      }
    }
  }

  /deep/ .el-dialog {
    margin: 0!important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 95%;
    display: flex;
    flex-direction: column;
  }

  /deep/ .quiz-list-dialog {
    max-height: 80vh;
    .el-dialog__body {
      height: 100%;
      overflow-y: hidden !important;
      padding-right: 0 !important;
    }
  }
  
  /deep/ .quiz-list-dialog,
  /deep/ .vocabulary-dialog,
  /deep/ .quiz-dialog {
    .el-dialog__header {
      padding: 24px 24px 0;
    }

    .el-dialog__title {
      font-size: 20px;
    }

    .el-dialog__body {
      padding: 24px;
      padding-top: 12px;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 8px 24px 24px;
      text-align: right;
    }
  }

  .quiz-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    .action-buttons {
      display: flex;
      gap: 16px;
    }

    /deep/ .action-buttons .el-button--text {
      padding: 0 !important;
      height: 24px;
      line-height: 24px;
    }
  }
  
  .quiz-list-dialog-body {
    display: flex;
    align-items: flex-start;
  }

  .quiz-menu {
    width: 27% !important;
    max-height: 62vh;
    overflow: auto;
    border-bottom: none;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);
    border-radius: 8px;
    margin-right: 24px;
  
    /deep/ .el-collapse-item__header {
      padding: 12px 0 12px 12px;
      background: #FFF;
    }
  
    /deep/ .el-collapse-item__content {
      padding: 0;
      background: #F5F6F8;
      cursor: pointer;
    }
  }

  .quiz-item-content {
    padding: 12px 12px 12px 24px;
    color: #111C1C;
    font-size: 14px;
    line-height: 140%;
    display: flex;
  }

  .quiz-item-content:hover {
    font-weight: 600;
    color: #10B3B7;
    background: rgba(16, 179, 183, 0.10);
  }

  .quiz-item-content-active {
    padding: 12px 12px 12px 21px;
    font-weight: 600;
    color: #10B3B7;
    border-left: 3px solid #10B3B7;
    background: rgba(16, 179, 183, 0.10);
  }

  .quiz-item-content-name {
    width: 70%;
    padding-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .quiz-item-content-day {
    width: 30%;
    text-align: right;
    white-space: nowrap;
  }

  .lesson-quiz-body {
    width: 73% !important;
    height: 62vh;
    padding-right: 20px;
    overflow: auto;
  
    /deep/ .lessons2-lesson-resources {
      display: none;
    }
  
    /deep/ .lesson-quiz {
      margin: 0;
      padding: 32px 30px 8px;
    }
  }

  .quiz-title {
    width: 100%;
    color: #111C1C;
    margin-top: 16px;
    font-size: 18px;
    font-weight: 600;
    z-index: 999;
    text-align: center;
    position: absolute;
  }

  .quiz-collapse-item-title {
    max-width: 90%;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    color: var(--111-c-1-c, #111C1C);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .quiz-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }

  @media screen and (max-width: 768px) {
    .quiz-list-dialog {
      top: 30% !important;
      max-height: 70vh !important;
    }
  
    .quiz-list-dialog-body {
      max-height: 60vh !important;
      flex-direction: column
    }
  
    .quiz-menu {
      width: 94% !important;
      margin-bottom: 12px;
    }
  
    .lesson-quiz-body {
      width: 100% !important;
    }
  
    /deep/ .quiz-dialog-footer {
      display: block;
    
      .el-button {
        margin-top: 4px;
      }
    }
  
    .quiz-title {
      width: 85%;
      padding: 0 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  
  }
</style>
<style lang="less">
.download-message-confirm {
  word-break: break-word;
  display: flex;
  align-items: center;
  
  img {
    margin-right: 10px;
    height: 42px !important;
    width: 42px !important;
  }
}
</style>