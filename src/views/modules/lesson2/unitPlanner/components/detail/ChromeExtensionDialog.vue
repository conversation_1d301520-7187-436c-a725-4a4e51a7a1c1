<template>
  <el-dialog
    title="🎉Your Unit Planner is Complete and Ready to Use!"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="true"
    width="800px"
    height="352px"
    custom-class="chrome-extension-dialog">
    <div class="dialog-content">
      <div class="dialog-body">
        <p style="color: #111c1c">Download or export it now for easy access and sharing. Want a one-stop solution for all your teaching needs? Try our Curriculum Genie Chrome Plugin—a free extension packed with versatile instructional tools that support lesson planning, assessments, and more.</p>
        <div class="extension-info" style="background: linear-gradient(100deg, rgb(213, 242, 245) -16.68%, rgb(239, 234, 254) 89.59%, rgb(187, 107, 217) 142.72%);border-radius: 20px;">
          <img src="logo.png" alt="Curriculum Genie" class="logo">
          <div class="text-content">
            <div style="margin-right: 100px;">Download to unlock 2 units and unlimited access to powerful teaching tools!</div>
            <a href="https://curriculumgenie.learning-genie.com/" target="_blank" class="learn-more">Learn more</a>
          </div>
          <el-button
            type="primary"
            class="add-chrome-btn"
            @click="addToChrome">
            <img src="chrome-logo.png" alt="Chrome" style="width: 20px; height: 20px; margin-right: 8px;">
            Add to Chrome
          </el-button>
        </div>
      </div>
      <div class="dialog-footer display-flex justify-content-between align-items-center" style="padding: 0;line-height: 40px;">
        <div class="dont-show-again">
          <el-checkbox v-model="dontShowAgain" @change="handleDontShowChange">Don't show this again</el-checkbox>
        </div>
        <el-dropdown trigger="click">
          <el-button type="primary" plain>
            <i class="lg-icon lg-icon-download" style="margin-right: 4px"></i>
            Download/Export <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown" class="download-dropdown-menu">
            <!--PDF 下载按钮-->
            <el-dropdown-item @click.native="downloadPDF('pdf')">
              <i class="lg-icon lg-icon-pdf2" style="size: 24px;"></i>
              <span class="download-font-style add-margin-l-3">Download PDF</span>
            </el-dropdown-item>
            <!--Word 下载按钮-->
            <el-dropdown-item @click.native="downloadPDF('docx')">
              <i class="lg-icon lg-icon-word1" style="size: 24px;"></i>
              <span class="download-font-style add-margin-l-3">Download Word</span>
            </el-dropdown-item>
            <div class="divider-wrapper">
              <el-divider></el-divider>
            </div>
            <!--Google Docs 下载按钮-->
            <el-dropdown-item @click.native="downloadGoogleDocs()">
              <img class="drive-icon-size" style="width: 16px; height: 16px;"
                   src="@/assets/img/lesson2/assistant/google-drive-icon.png">
              <span class="download-font-style add-margin-l-8">Save to Google Drive</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ChromeExtensionDialog',
  props: {
    unit: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      dontShowAgain: false
    }
  },
  methods: {
    show() {
      // 检查是否需要显示弹窗
      const hideDialog = localStorage.getItem('hideExtensionDialog') === 'true'
      const isInstalled = localStorage.getItem('isInstalled') === 'true'
      this.dialogVisible = !hideDialog && !isInstalled
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleDontShowChange(val) {
      // 将用户选择保存到localStorage
      localStorage.setItem('hideExtensionDialog', val)
    },
    addToChrome() {
      window.open('https://chromewebstore.google.com/detail/curriculum-genie/jbhknkdnmmcipjcfjngiojllgbgolmao', '_blank')
    },
    // 下载谷歌docs文档
    downloadGoogleDocs () {
      this.handleClose()
      // 关闭下拉菜单
      this.$emit('downloadGoogleDocs', this.unit)
    },
    // 下载PDF文档
    downloadPDF (fileType) {
      this.handleClose()
      // 关闭下拉菜单
      this.$emit('downloadPDF', fileType, this.unit)
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .chrome-extension-dialog {
  .el-dialog__header .el-dialog__title {
    font-size: 20px!important;
  }
  .el-checkbox__label, .el-checkbox__input {
    font-size: 16px;
  }
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dialog-body {
    margin-bottom: 20px;
    p {
      margin-bottom: 20px;
      line-height: 1.6;
      color: #606266;
    }
  }

  .dialog-content {
    font-size: 16px;
  }

  .extension-info {
    display: flex;
    align-items: center;
    padding: 16px;
    margin: 16px 0;

    .logo {
      width: 48px;
      height: 48px;
      margin-right: 16px;
    }

    .text-content {
      flex: 1;
      margin-right: 16px;

      .learn-more {
        font-weight: bold;
        color: #10b3b7;
        text-decoration: none;
        margin-top: 8px;
        display: inline-block;
        text-decoration: underline;
        &:hover {
          text-decoration: underline;
        }
      }
    }

    .add-chrome-btn {
      white-space: nowrap;
    }
  }

  .dialog-footer {
    padding-top: 20px;
  }

  .download-dropdown-menu {
    .download-font-style {
      font-size: 14px;
      color: #333;
    }

    .add-margin-l-3 {
      margin-left: 12px;
    }

    .add-margin-l-8 {
      margin-left: 8px;
    }

    .drive-icon-size {
      vertical-align: middle;
    }
  }
  .el-button--primary.is-plain {
      background-color: #1cb7bb !important;
      border-color: #1cb7bb !important;
      color: #fff !important;

      &:hover, &:focus {
        background-color: #19a5a9 !important;
        border-color: #19a5a9 !important;
        color: #fff !important;
    }
  }
}
</style>
<style lang="scss">
.divider-wrapper .el-divider--horizontal {
    margin: 0;
}

</style>
