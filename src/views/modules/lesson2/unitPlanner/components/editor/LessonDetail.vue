<template>
  <div class="w-full">
    <el-form
        ref="lessonFormRef"
        label-position="top"
        label-width="100px"
        :model="lesson"
        :rules="lessonRules"
    >
      <!-- 课程详情 -->
      <el-card  shadow="never" class="w-full" body-style="height: 100%;" style="overflow: visible;">
        <div id="lesson-detail-header" class="display-flex justify-content-between">
          <!-- 课程名称 -->
          <div class="font-size-20 font-bold">
          </div>
        </div>
        <div class="display-flex m-b-sm">
          <!-- 封面 -->
          <el-form-item prop="cover">
            <div class="cover-container">
              <el-skeleton animated :loading="!!lesson.generateLessonCoverLoading" class="position-relative">
                <template slot="template">
                  <el-skeleton-item variant="image" style="width: 370px; height: 208px;"/>
                </template>
                <template>
                  <div class="cover-operation">
                    <!-- 重新生成 -->
                    <ReplaceCover :key="item.coverKeywords || lesson.coverKeywords" :searchKeywords="item.coverKeywords || lesson.coverKeywords" type="lesson" @generateCover="generateLessonCover" @setCoverInfo="setCoverInfo"/>
                    <el-button size="mini" round @click.stop="customPromptVisible = true"
                               v-show="promptManagement && !loading">
                      <i class="el-icon-edit-outline"></i>
                    </el-button>
                  </div>
                  <MediasUploader
                    ref="mediasUploader"
                    :maxFileCount="1"
                    :uploaderRatio="'16-9'"
                    :priviewRatio="'16-9'"
                    :priviewColSpan="24"
                    :uploaderTheme="'theme-2'"
                    :previewFiles="coverImages"
                    :currentLesson="lesson"
                    @change="updateImagesEvent"
                  >
                    <template slot="placeholder-default">
                    <i class="lg-icon lg-icon-picture text-muted"></i>
                    <div class="el-upload__text m-b-sm">
                      <div>
                          {{$t('loc.unitPlannerStep1DropImage')}}
                      </div>
                      </div>
                      <el-button type="primary" round size="medium">
                        <div class="display-flex justify-content align-items">
                          <i class="el-icon-upload2 font-size-20"></i>
                          <span>{{ $t('loc.upload') }}</span>
                        </div>
                      </el-button>
                    </template>
                  </MediasUploader>
                </template>
              </el-skeleton>
            </div>
          </el-form-item>
          <!-- 内容 -->
          <div class="flex-auto add-margin-l-24">
            <!-- 课程名称 -->
            <div class="font-size-20 font-bold m-b-sm">
              <span>{{ item.title.replace(/"/g, '') }}</span>
              <el-tag v-if="lesson.adaptedLesson" class="adapted-tag font-weight-400" size="mini">{{ $t('loc.adaptUnitPlanner25') }}</el-tag>
            </div>
            <!-- 年龄段 -->
            <div class="m-b-sm display-flex flex-wrap gap-8">
              <el-skeleton :rows="1" animated :loading="loading && !lesson.ageGroup" class="lesson-tag">
                <template>
                  <div v-if="lesson.ageGroup" >
                    <span>{{ lesson.ageGroup }}</span>
                  </div>
                </template>
              </el-skeleton>
              <div v-if="isCenterLesson" class="lesson-tag">
                <i class="lg-icon font-size-20 lg-margin-right-4" :class="iconMap.get(item.centerGroupName)"></i>
                <span>{{ item.centerGroupName }}</span>
              </div>
              <!-- 课堂类型标签 -->
              <div class="lesson-tag" v-if="showClassroomType">
                <span>{{ getClassroomTypeLabel(lesson.classroomType) }}</span>
              </div>
            </div>
            <!-- DRDP 测评点 -->
            <div class="m-b-sm">
              <el-skeleton :rows="1" animated :loading="loading && !lesson.measures" class="lesson-measure">
                <template>
                  <div v-if="lesson.measures" class="">{{$t('loc.standardsOrMeasures')}}</div>
                  <div v-if="lesson.measures" class="">
                    <span v-for="(measure, index) in lesson.measures" :key="index">
                      <el-tooltip placement="top" :open-delay="300"
                                  :disabled="!measureNameDescription(measure)">
                          <div slot="content" class="measure-content">{{ measureNameDescription(measure) }}</div>
                          <span>
                              {{ measure }}<span
                              v-if="lesson.measures.length > 1 && index <  lesson.measures.length - 1">;&nbsp;</span>
                          </span>
                      </el-tooltip>
                  </span>
                  </div>
                </template>
              </el-skeleton>
            </div>
            <el-row class="lesson-field-time add-margin-t-20">
              <!-- 准备时间 -->
              <el-col class="lesson-field-time-item">
                <span class="lesson-field-value add-margin-b-10">
                  {{ $t('loc.lesson2NewLessonFormLabelPrepareTime') }}
                </span>
                <el-skeleton :rows="1" animated :loading="loading && !lesson.prepareTime">
                  <template>
                    <el-select v-model="lesson.prepareTime" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                               class="input-select" clearable>
                      <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value"/>
                    </el-select>
                  </template>
                </el-skeleton>
              </el-col>
              <!-- 活动时长 -->
              <el-col class="lesson-field-time-item" :class="{'before-item' : lesson.prepareTime}">
                <span class="lesson-field-value add-margin-b-10">
                  {{ $t('loc.lesson2NewLessonFormLabelActivityTime') }}
                </span>
                <el-skeleton :rows="1" animated :loading="loading && !lesson.activityTime">
                  <template>
                    <el-select v-model="lesson.activityTime" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                               class="input-select" clearable>
                      <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value"/>
                    </el-select>
                  </template>
                </el-skeleton>
              </el-col>
              <!-- 课程模板 -->
              <el-col class="lesson-field-time-item" v-if="showLessonTemplate">
                <span class="lesson-field-value add-margin-b-10">
                  {{ $t('loc.eduprotocols9') }}
                </span>
                <el-skeleton :rows="1" animated :loading="loading && !lesson.activityTime">
                  <template>
                    <lesson-template-select-modal
                     v-model="item.lessonTemplateType"
                     :lessonId="lesson.id"
                     :loading="loading"
                     :saveLesson="saveLesson"
                     @change="updateLessonTemplate"
                     :lessonAges="[lesson.ageGroup]" />
                  </template>
                </el-skeleton>
              </el-col>
            </el-row>
          </div>
        </div>
        <!-- 课程标题 -->
        <el-form-item label="Title" prop="title" v-show="false">
          <el-skeleton :rows="1" animated :loading="loading && !item.title">
            <template>
              <el-input v-model="item.title"/>
            </template>
          </el-skeleton>
        </el-form-item>
        <!-- 目标 -->
        <el-form-item ref="objectives" :label="$t('loc.unitPlannerLessonObjectives')" prop="objectives">
          <el-skeleton :rows="1" animated :loading="loading && !lesson.objectives">
            <template>
              <!-- 当存在两种目标样式时显示选择界面 -->
              <div v-if="hasMultipleObjectiveStyles && !objectiveStyleSelected" class="objective-style-selector">
                <div class="objective-style-tip">
                  <span style="font-style: italic;">👇We've created two versions of your lesson objectives. Which one do you prefer?</span>
                </div>
                
                <!-- 左右布局的两个选项 -->
                <div class="objective-style-options-container">
                  <!-- Formal Description 样式选择 -->
                  <div class="objective-style-option">
                    <el-radio
                      :value="selectedObjectiveStyle"
                      label="formal"
                      @input="selectObjectiveStyle('formal')"
                      class="objective-style-radio">
                      <span class="objective-style-label">{{ 'Use This Style - Formal Description' }}</span>
                    </el-radio>
                    <div class="objective-style-content">
                      <el-input
                        v-model="lesson.formalDescription"
                        type="textarea"
                        :rows="5"
                        resize="none"
                        @focus="handleInputFocus('formal')"
                        @input="needConfirmLesson"
                        class="objective-preview-input"/>
                    </div>
                  </div>

                  <!-- Student Friendly 样式选择 -->
                  <div class="objective-style-option">
                    <el-radio
                      :value="selectedObjectiveStyle"
                      label="studentFriendly"
                      @input="selectObjectiveStyle('studentFriendly')"
                      class="objective-style-radio">
                      <span class="objective-style-label">{{ 'Use This Style - Student-Friendly' }}</span>
                    </el-radio>
                    <div class="objective-style-content">
                      <el-input
                        v-model="lesson.studentFriendly"
                        type="textarea"
                        :rows="5"
                        resize="none"
                        @focus="handleInputFocus('studentFriendly')"
                        @input="needConfirmLesson"
                        class="objective-preview-input"/>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 正常的输入框（当没有多种样式或已选择样式后显示） -->
              <el-input
                v-else
                v-model="lesson.objectives"
                @input="needConfirmLesson"
                @blur="validateLessonForm('objectives')"
                type="textarea"
                :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderObjectives')"
                :autosize="{ minRows: 4, maxRows: 10}"/>
            </template>
          </el-skeleton>
        </el-form-item>
        <!-- 材料 -->
        <el-form-item ref="materials" :label="$t('loc.unitPlannerLessonMaterials')" prop="materialFiles">
          <el-skeleton :rows="4" animated :loading="loading && !lesson.materialFiles">
            <template>
              <lesson-material-input v-model="lesson.materialFiles" ref="lessonMaterialInput" :key="'unit-planner-material-'+lesson.id"/>
            </template>
          </el-skeleton>
        </el-form-item>
        <!-- 关键词 -->
        <el-form-item ref="keyVocabularyWords" v-if="!isCenterLesson" :label="$t('loc.unitPlannerLessonDLLAndPhrases')" prop="keyVocabularyWords">
          <el-skeleton :rows="4" animated :loading="loading && !lesson.keyVocabularyWords">
            <template>
              <!-- <editor v-model="lesson.keyVocabularyWords" @input="needConfirmLesson"/> -->
              <el-input v-model="lesson.keyVocabularyWords" type="textarea" :autosize="{ minRows: 4, maxRows: 10}" @input="needConfirmLesson"
              :placeholder="$t('loc.unitPlannerLessonDLLAndPhrasesPlaceholder')"
              />
            </template>
          </el-skeleton>
        </el-form-item>
        <!-- 步骤 -->
        <div style="display: flex; flex-direction: column;" ref="implementationStepsRef">
          <div class="display-flex justify-content-between add-padding-b-4" style="align-items: center;">
            <el-form-item label="" style="margin-bottom: 0">
              <label class="el-form-item__label">
                <span class="required-star" style="color: #F56C6C;">*</span>
                {{ $t('loc.unitPlannerLessonImplementationSteps') }}
              </label>
            </el-form-item>
            <!-- 展示或隐藏实施步骤资源按钮 -->
            <div class="position-relative " v-if="!isCenterLesson">
              <!-- 添加 Resource Settings 组件 -->
              <ResourceSettings @regenerate="generateLessonSources(true)" :setupEventName="'cg_unit_lesson_resource'" />
              <div class="display-ib add-margin-l-10">
                <el-button @click="changeShowImpStepSource()" plain>
                  <template #icon>
                    <i class="lg-icon"
                      :class="{ 'lg-icon-eye': !showImpStepSource, 'lg-icon-eye-off': showImpStepSource }"
                      style="margin-right: 5px"></i>
                  </template>
                  <span v-if="!showImpStepSource">{{ $t('loc.unitPlannerShowSources') }}</span>
                  <span v-else>{{ $t('loc.unitPlannerHideSources') }}</span>
                </el-button>
                <span class="new-tag">Beta</span>
              </div>
            </div>
          </div>
          <div style="display: flex; flex-direction: row;">
            <!--没有资源/主动不显示资源/按钮置灰也就是生成时，都不显示实施步骤资源-->
            <div
              :style=" !showImpStepSource || isCenterLesson ? 'width:100%' :'width: 72%'">
              <el-form-item ref="implementationSteps" prop="implementationSteps">
                <el-skeleton :rows="4" animated :loading="loading && !lesson.implementationSteps || this.implementationStepsLoading">
                  <template>
                    <editor ref="editorRef" v-model="lesson.implementationSteps" @input="needConfirmLesson"
                            :hiddeImpStepSource="!showImpStepSource" :impStepModel="true"
                            :placeholder="$t('loc.unitPlannerLessonImplementationStepsPlaceholder')"
                            />
                    <!-- <el-input v-model="lesson.implementationSteps" type="textarea" :autosize="{ minRows: 4, maxRows: 20}" @input="needConfirmLesson" /> -->
                  </template>
                </el-skeleton>
              </el-form-item>
            </div>
            <!--实施步骤资源内容-->
            <!--按钮被禁用时，也就是重新生成时，无论是否有资源都不展示-->
            <div v-show="showImpStepSource && !isCenterLesson" :style="{ width: showImpStepSource ? '28%' : '0'}">
              <ImplementationStepSources
                :sourcePreview="false"
                :lesson=lesson
                :showImpStepSource=showImpStepSource
                parentComponentName="LessonDetail"
                @upLessonImpStepData="upLessonImpStepData"
                @before-save-resources="implementationStepsLoading = true"
                @save-resources-success="saveResourcesSuccess"
                @save-resources-failed="implementationStepsLoading = false"
                ref="implementationStepSources"
                style="margin-left: 10px;"
              />
            </div>
          </div>
        </div>
        <!-- 课程模板 -->
        <lesson-template-preview v-if="kToGrade12 && !isCenterLesson && (getLightAdaptModuleShow('eduProtocolsTemplatesFlag') || lesson.lessonTemplate)"
                                 ref="lessonTemplatePreviewRef"
                                 :templateType="item.lessonTemplateType"
                                 :lessonId="lesson.id"
                                 :lessonLoading="loading"
                                 :lessonName="item.title || lesson.name"
                                 :ageGroup="lesson.ageGroup"
                                 :lessonTemplate="lesson.lessonTemplate"
                                 :edit="true"
                                 :saveLesson="saveLesson"
                                 @updateGenerateCustomTemplateLoading="updateGenerateCustomTemplateLoading"
                                 @updateLessonTemplateInfo="updateLessonTemplateInfo" />
      </el-card>

      <!-- 课程幻灯片 -->
      <div v-if="(kToGrade12 || lectureSlidesExpandGrade) && !isCenterLesson && getLightAdaptModuleShow('lectureSlidesFlag')" class="w-full m-t-sm">
        <lesson-slides
          ref="lessonSlidesRef"
          :lesson-id="lesson.id"
          :lesson-name="item.title || lesson.name"
          :edit="true"
          :lesson-loading="loading"
          :activityType="lesson.activityType"
          @updateGenerateLessonSlidesLoading="updateGenerateLessonSlidesLoading">
        </lesson-slides>
      </div>

      <!-- 课程校训 -->
      <el-card shadow="never" v-if="(lessonLearnerProfiles && lessonLearnerProfiles.length > 0 || generatePortraitGraduateLoading) && getLightAdaptModuleShow('portraitOfGraduateFlag')" class="w-full m-t-sm portrait-graduate-card" body-style="height: 100%;">
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div>
            <div class="title">
              {{ $t('loc.lessonPortraitTitle') }} <span v-if="lesson.ageGroup"> ({{ lesson.ageGroup }})</span>
              <span class="new-tag">New</span>
            </div>
          </div>
          <div class="lesson-detail-item-action">
            <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
              <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                         @click="regeneratePortraitGraduate()"
                         :loading="(generatePortraitGraduateLoading || loading)">
              </el-button>
            </el-tooltip>
            <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
                :class="portraitGraduateCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
                @click="portraitGraduateCollapse = !portraitGraduateCollapse"></i>
          </div>
        </div>

        <el-collapse-transition>
          <div class="lg-padding-20" v-show="!portraitGraduateCollapse">
            <el-skeleton :rows="5" animated
                :loading="(generatePortraitGraduateLoading || loading) && lessonLearnerProfiles.filter(item => Object.keys(item).length > 0).length == 0">
              <template>
                <!-- 提示说明 -->
                <div v-if="!generatePortraitGraduateLoading && item.lessonTemplateType && filteredProfiles && filteredProfiles.length > 0" class="portrait-table-tip">
                  <i class="lg-icon lg-icon-prompting-message color-676879"></i>
                  <span style="margin-left: 6px;"> {{ getPortraitGraduateTip() }}</span>
                </div>
                <table class="learners-table portrait-table">
                  <thead>
                    <tr>
                      <th style="width: 20%;"> {{ $t('loc.lessonPortraitAttr')}} ({{ lessonLearnerProfiles.length }})</th>
                      <th v-if="hasSubRubrics" style="width: 20%"> {{ $t('loc.learnerProfileStandards') }} ({{ lessonLearnerProfiles.reduce((acc, profile) => acc + (profile.subRubrics || []).length, 0) }}) </th>
                      <th v-if="hasExpectations" style="width: 20%"> {{ $t('loc.learnerProfileExpectations') }} </th>
                      <th :style="hasSubRubrics && hasExpectation ? 'width: 40%' : hasSubRubrics || hasExpectation ? 'width: 60%' : 'width: 80%'" > {{ $t('loc.lessonPortraitAttrValue')}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <template v-for="(portrait, index) in lessonLearnerProfiles">
                      <template v-if="hasSubRubrics">
                        <tr v-for="(sub, subIdx) in (portrait.subRubrics || [])" :key="`sub-${index}-${subIdx}`">
                          <td v-if="hasSubRubrics && subIdx == 0" class="portrait-name" :rowspan="portrait.subRubrics.length">
                            <div class="name">{{ portrait.rubricsName }}</div>
                          </td>
                          <td class="portrait-name">
                            <div class="name">{{ sub.rubricsName }}</div>
                            <div class="description" v-if="sub.rubricsNameDesc">{{ sub.rubricsNameDesc }}</div>
                          </td>
                          <td v-if="hasExpectations" class="portrait-name">
                            <div class="description">{{ sub.rubricsExpectation }}</div>
                          </td>
                          <td class="portrait-evidence">
                            <el-input
                              v-model="sub.rubricsValue"
                              type="textarea"
                              :placeholder="$t('loc.lessonPortraitAttrValue')"
                              :autosize="{ minRows: 3, maxRows: 50}"
                              resize="none"
                              @blur="validateLessonForm(`portrait-${index}-sub-${subIdx}`)"
                            />
                          </td>
                        </tr>
                      </template>
                      <!-- 主 rubrics 行 -->
                      <tr v-else>
                        <td class="portrait-name">
                          <div class="name">{{ portrait.rubricsName }}</div>
                          <div class="description" v-if="portrait.rubricsNameDesc">{{ portrait.rubricsNameDesc }}</div>
                        </td>
                        <td v-if="hasExpectations" class="portrait-name">
                          <div class="description">{{ portrait.rubricsExpectation }}</div>
                        </td>
                        <td class="portrait-evidence">
                          <el-input
                            v-model="portrait.rubricsValue"
                            type="textarea"
                            :placeholder="$t('loc.lessonPortraitAttrValue')"
                            :autosize="{ minRows: 3, maxRows: 10}"
                            resize="none"
                            @blur="validateLessonForm(`portrait-` + index)"
                          />
                        </td>
                      </tr>
                    </template>
                  </tbody>
                </table>
              </template>
            </el-skeleton>
          </div>
        </el-collapse-transition>
      </el-card>
      <!-- Reading Passage -->
      <el-card ref="readingPassage" shadow="never" class="w-full m-t-sm udl-clr-card udl-table" body-style="height: 100%;">
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div class="lg-color-primary">
            <div class="font-size-20 font-bold">
              Reading Passage (Anchor Text）
            </div>
          </div>
          <div class="lesson-detail-item-action">
            <el-button @click="changeShowSource()" :loading="(generateCulturallyResponsiveInstructionLoading || loading)" plain>
                  <template #icon>
                    <i class="lg-icon" :class="{ 'lg-icon-eye': !showSource, 'lg-icon-eye-off': showSource }" style="margin-right: 5px"></i>
                  </template>
                  <span v-if="!showSource">{{ $t('loc.unitPlannerShowSources') }}</span>
                  <span v-else>{{ $t('loc.unitPlannerHideSources') }}</span>
                </el-button>
            <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
              <!-- 重新生成 -->
              <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                         @click="regenerateReadingPassage()"
                         :loading="(generateReadingPassageLoading || loading)">
              </el-button>
            </el-tooltip>
            <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
               :class="readingPassageCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
               @click="readingPassageCollapse = !readingPassageCollapse"></i>
          </div>
        </div>

        <el-collapse-transition>
          <template>
            <div class="lg-padding-20" v-show="!readingPassageCollapse">
              <el-skeleton :rows="4" animated
                           :loading="(generateReadingPassageLoading || loading) && !(lesson && lesson.readingPassage  && lesson.readingPassage.length > 0)">
                <reading-passage :value="lesson.readingPassage"/>
              </el-skeleton>
            </div>
          </template>
        </el-collapse-transition>
      </el-card>

      <!-- 标准教学指导 -->
      <el-card ref="teachingTipsForStandard" shadow="never" class="w-full m-t-sm udl-clr-card udl-table" v-if="!isCenterLesson && getLightAdaptModuleShow('teacherGuideFlag')"
               body-style="height: 100%;">
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div class="lg-color-primary">
            <div class="font-size-20 font-bold">
              {{ $t('loc.teachingTipsForStandards') }}
            </div>
            <div v-if="isCAPTKLF">
              California Preschool/Transitional Kindergarten Learning Foundations
            </div>
          </div>
          <div class="lesson-detail-item-action">
            <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
              <!-- 重新生成 -->
              <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                         @click="regenerateTeachingTips()"
                         :loading="(generateTeachingTipsForStandardLoading || loading)">
              </el-button>
            </el-tooltip>
            <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
               :class="teachingTipsCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
               @click="teachingTipsCollapse = !teachingTipsCollapse"></i>
          </div>
        </div>

        <el-collapse-transition>
          <template>
            <div class="lg-padding-20" v-show="!teachingTipsCollapse">
              <el-skeleton :rows="4" animated
                           :loading="(generateTeachingTipsForStandardLoading || loading) && !(lesson && lesson.teachingTips && lesson.teachingTips.length > 0)">
                <teaching-tips-standard :frameworkId="baseInfo.frameworkId"
                                        :domains="domains"
                                        :needAddMeasure="false"
                                        v-model="lesson.teachingTips"
                                        ref="teachingTipsStandard"/>
              </el-skeleton>
            </div>
          </template>
        </el-collapse-transition>
      </el-card>

      <!-- 典型行为 -->
      <el-card shadow="never" v-if="!isCenterLesson && !isK12Grade && getLightAdaptModuleShow('teacherGuideFlag')" class="w-full m-t-sm typical-behaviors-card" body-style="height: 100%;">
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div class="display-flex align-items">
            <img src="~@/assets/img/lesson2/unitPlanner/typical_behaviors.png" alt="" height="32">
            <div class="font-size-20 font-bold lg-margin-left-8" style="color: #2D9CDB">{{$t('loc.unitPlannerLessonTypicalBehaviorsTips')}}</div>
          </div>
          <div class="display-flex align-items">
            <div class="typical-behaviors-tab" v-if="isCAPTKLF">
              <typical-behaviors-tab v-model="typicalBehaviorsType"></typical-behaviors-tab>
            </div>
            <div class="lesson-detail-item-action">
              <!-- 重新生成 -->
              <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
                <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                          @click="regenerateTypicalBehaviors()"
                          :loading="(generateTypicalBehaviorsLoading || loading)">
                </el-button>
              </el-tooltip>
              <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
                :class="typicalBehaviorsCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
                @click="typicalBehaviorsCollapse = !typicalBehaviorsCollapse"></i>
            </div>
          </div>
        </div>
        <el-collapse-transition>
          <div class="lg-padding-20" v-show="!typicalBehaviorsCollapse">
            <el-skeleton :rows="5" animated
                         :loading="(generateTypicalBehaviorsLoading || loading) && lessonTypicalBehaviors.filter(item => Object.keys(item).length > 0).length == 0">
            </el-skeleton>
            <!-- <el-form
                ref="typicalBehaviorFormRef"
                label-position="top"
                label-width="100px"
                :rules="typicalBehaviorRules"
                v-if="lesson.typicalBehaviors"
                class="m-t-sm"
                v-loading="generateTypicalBehaviorsLoading && !lesson.typicalBehaviors"
            > -->
            <div v-if="!loading && !generateTypicalBehaviorsLoading && showUnMappedTip" class="lg-color-warning">
              <i class="el-icon-warning-outline"></i>
              {{ $t('loc.measureMapTip') }}
            </div>
            <!-- 步骤 -->
            <el-form-item :label="behavior ? measureName(behavior.measureAbbreviation, typicalBehaviorsType == 'PS') : ''" :prop="`behavior-` + index"
                          v-for="(behavior, index) in lessonTypicalBehaviors" :key="index" v-show="behavior && behavior.measureAbbreviation">
              <el-skeleton :rows="3" animated :loading="generateTypicalBehaviorsLoading && !behavior.typicalBehaviors">
                <template>
                  <div v-if="behavior && behavior.measureAbbreviation" style="margin-top: -15px;">
                    <!-- 测评点描述 -->
                    <div class="line-height-21 m-t-sm m-b-sm">{{ measureDescription(behavior.measureAbbreviation, typicalBehaviorsType == 'PS') }}</div>
                  </div>
                  <!-- <editor v-model="behavior.typicalBehaviors" @blur="validateLessonForm(`behavior-` + index)"/> -->
                  <el-input v-model="behavior.typicalBehaviors" type="textarea" :autosize="{ minRows: 2, maxRows: 10}"  @blur="validateLessonForm(`behavior-` + index)" />
                </template>
              </el-skeleton>
            </el-form-item>
            <!-- </el-form> -->
            <!-- 未生成重新生成提示 -->
            <div v-if="!loading && !generateTypicalBehaviorsLoading && lesson && lessonTypicalBehaviors.length == 0" class="text-center">
              <template v-if="typicalBehaviorsType=='PS' && allMeasureUnMapped">
                {{ $t('loc.measureMapEmptyTip') }}
              </template>
              <template v-else-if="typicalBehaviorsType=='PS' && !allMeasureUnMapped">
                {{ $t('loc.measureMapGenerateTip') }}
              </template>
              <template v-else>
                {{ $t('loc.unitPlannerRenegerateTip') }}
              </template>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>

      <!-- 差异教学内容 -->
      <!--UDL & CLR-->
      <el-card v-if="!isCenterLesson && (getLightAdaptModuleShow('teacherGuideFlag') || lesson.universalDesignForLearning || lesson.universalDesignForLearningGroup)" ref="universalDesignForLearning" shadow="never" class="w-full m-t-sm udl-clr-card udl-table m-b-sm"
               body-style="height: 100%;">
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div class="font-size-20 font-bold lg-color-primary">
            {{ $t('loc.unitPlannerLessonUDL') }}
          </div>
          <div class="lesson-detail-item-action">
            <!-- 重新生成 -->
            <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
              <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                         @click="regenerateUniversalDesignForLearning(item.adaptedLesson)"
                         :loading="(generateUniversalDesignForLearningLoading || loading)">
              </el-button>
            </el-tooltip>
            <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
               :class="udlCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
               @click="udlCollapse = !udlCollapse"></i>
          </div>
        </div>
        <div>
          <el-collapse-transition>
            <div>
              <el-skeleton :rows="5" animated
                           :style="{padding: udlLoading ? '24px' : '0'}"
                           :loading="udlLoading">
              </el-skeleton>
              <el-form-item prop="universalDesignForLearning" v-show="!isCenterLesson && !udlCollapse"
                            :key="'udl-' + lesson.ageGroup"
                            class="remove-padding-l-0">
                <UniversalDesignLearning
                  v-show="!udlLoading"
                  :universalDesignForLearning="lesson.universalDesignForLearning"
                  :mixedAgeDifferentiation="lesson.mixedAgeDifferentiations"
                  :showMixedAge="lesson.showMixedAge"
                  :universalDesignForLearningClassSpecial="lesson.universalDesignForLearningGroup ? lesson.universalDesignForLearningGroup: ''"
                  :universalDesignForLearningCopy="universalDesignForLearningCopy"
                  :universalDesignForLearningClassSpecialCopy="universalDesignForLearningClassSpecialCopy"
                  :lessonId="lesson.id"
                  :itemId="item.id"
                  :lessonStepIndex="0"
                  :isAdaptedLesson="lesson.adaptedLesson"
                  :lessonAgeGroup="lesson.ageGroup"
                  :isWeeklyPlanEdit="false"
                  :adaptUDLAndCLROpen="true"
                  :getNewLessonInfo="getNewLessonInfo"
                  @updateUniversalDesignForLearning="updateUniversalDesignForLearning"
                  @updateUniversalDesignForLearningClassSpecial="updateUniversalDesignForLearningClassSpecial"
                  @clearUniversalDesignForLearningClassSpecial="clearUniversalDesignForLearningClassSpecial"
                  @updateUdlLoading="updateUdlLoading"
                  :key="'unit-planner-udl-'+lesson.id"
                  class="udl-component"
                  ref="universalDesignLearning"/>
              </el-form-item>
            </div>
          </el-collapse-transition>
        </div>
      </el-card>
      <!-- CLR 文化差异 -->
      <el-card shadow="never" v-if="!isCenterLesson && (getLightAdaptModuleShow('teacherGuideFlag') || lesson.culturallyResponsiveInstruction || lesson.culturallyResponsiveInstructionGroup)" class="w-full m-t-sm udl-clr-card" body-style="height: 100%;">
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div class="font-size-20 font-bold lg-color-primary display-flex align-items" style="gap: 10px">
            <span>{{ $t('loc.unitPlannerCLR') }}</span>
          </div>
          <div class="display-flex align-items">
            <div class="display-flex align-items">
              <div class="position-relative add-margin-r-10">
                <!-- （CLR 没有做，暂时注释掉）添加 Resource Settings 组件 -->
                <!-- <ResourceSettings class="resource-settings-component"
                @regenerate="generateLessonSources(false)"
                /> -->
                <el-button @click="changeShowSource()" :loading="(generateCulturallyResponsiveInstructionLoading || loading)" plain>
                  <template #icon>
                    <i class="lg-icon" :class="{ 'lg-icon-eye': !showSource, 'lg-icon-eye-off': showSource }" style="margin-right: 5px"></i>
                  </template>
                  <span v-if="!showSource">{{ $t('loc.unitPlannerShowSources') }}</span>
                  <span v-else>{{ $t('loc.unitPlannerHideSources') }}</span>
                </el-button>
                <span class="new-tag">Beta</span>
              </div>
              <!-- 重新生成 -->
              <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
                <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                           @click="regenerateCulturallyResponsiveInstruction()"
                           :loading="(generateCulturallyResponsiveInstructionLoading || loading)">
                </el-button>
              </el-tooltip>
            </div>
            <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
               :class="clrCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
               @click="clrCollapse = !clrCollapse"></i>
          </div>
        </div>
        <el-collapse-transition>
          <div v-show="!clrCollapse">
            <!-- 内容 -->
            <CulturallyLinguisticallyResponsive
              class="unit-planner-clr"
              v-show="!clrLoading"
              :key="'unit-planner-clr-'+lesson.id"
              :ageGroupName="lesson.ageGroup"
              :getNewLessonInfo="getNewLessonInfo"
              :lessonId="lesson.id"
              :itemId="item.id"
              :isWeeklyPlanEdit="false"
              :isEdit="isUnitPlanner"
              :isUnitPlanner="isUnitPlanner"
              :step="stepForClr"
              :isAdaptedLesson="item.adaptedLesson"
              :adaptUDLAndCLROpen="item.adaptedLesson"
              :normalClrLoading="generateCulturallyResponsiveInstructionLoading"
              :showUnitLessonSource="showSource"
              ref="culturallyLinguisticallyResponsive"
              @updateLessonSource="updateClrLessonSource"
              @updateClrGroupLessonSource="updateClrGroupLessonSource"
              @regenerateNormalClr="regenerateCulturallyResponsiveInstruction(false)"
              @upNormalClrLoading="upNormalClrLoading"
              :culturallyResponsiveInstruction="lesson.culturallyResponsiveInstruction"
              :culturallyResponsiveInstructionGeneral="lesson.culturallyResponsiveInstructionGeneral"
              :culturallyResponsiveInstructionGroup="lesson.culturallyResponsiveInstructionGroup"/>

            <!-- 骨架屏覆盖层 -->
            <div v-if="clrLoading">
              <el-skeleton :rows="5"
                           animated
                           :loading="clrLoading"
                           :style="{padding: clrLoading ? '24px' : '0'}"
              />
            </div>
          </div>
        </el-collapse-transition>
      </el-card>

      <!-- Quiz -->
      <el-card shadow="never" v-if="!isCenterLesson && (isK12Grade || isExtendedAgeGroup) && getLightAdaptModuleShow('standardsAssessmentFlag')" class="w-full m-t-sm formative-quiz-card" body-style="height: 100%;">
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div class="display-flex align-items">
            <img src="~@/assets/img/lesson2/unitPlanner/formative_quiz.png" alt="" height="32">
            <div class="font-size-20 font-bold lg-margin-left-8 lg-color-chart-7">{{ $t('loc.lessonQuiz1') }}</div>
          </div>
          <!-- 题目难度标准 -->
          <div>
            <el-radio-group :value="isBloom" @input="changeQuizModel" :disabled="generateLessonQuizLoading || loading">
              <!-- Bloom -->
              <el-radio :label="true">
                <span>{{ $t('loc.LessonQuiz3') }}</span>
                <el-tooltip popper-class="tooltip-wrapper" class="item" effect="dark" :content="$t('loc.lessonQuizLevelBloomDesc')" placement="top">
                  <i class="lg-icon lg-icon-question lg-margin-left-4"></i>
                </el-tooltip>
              </el-radio>
              <!-- DOK -->
              <el-radio :label="false">
                <span>{{ $t('loc.lessonQuizLevelDOK') }}</span>
                <el-tooltip popper-class="tooltip-wrapper" class="item" effect="dark" :content="$t('loc.lessonQuizLevelDOKDesc')" placement="top">
                  <i class="lg-icon lg-icon-question lg-margin-left-4"></i>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
            <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
                :class="lessonQuizCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
                @click="lessonQuizCollapse = !lessonQuizCollapse"></i>
          </div>
        </div>
        <el-collapse-transition>
          <div class="lg-padding-20" v-show="!lessonQuizCollapse">
            <!-- 内容 -->
            <el-skeleton :rows="5" animated :loading="(generateLessonQuizLoading || loading) && !lesson.questions">
              <template>
                <el-form-item prop="lessonQuiz">
                  <div class="lesson-quiz-windows">
                    <LessonQuizWindow v-for="(question, index) in currentQuestions" :key="index + '' + quizRenderKey" :measures="lessonMeasures"
                                      :ageGroupName="lesson.ageGroup" @deleteQuizEditor="deleteQuizEditor" v-model="currentQuestions[index]"
                                      :questionIndex="index" class="lesson-quiz-window" :data-id="index" :isBloom="isBloom"
                                      :edit="true" :disableEditorDeleteIcon="currentQuestions && currentQuestions.length === 1" @regenerateQuizQuestion="regenerateQuizQuestion"/>
                  </div>
                </el-form-item>
                <el-button icon="el-icon-plus" @click="addQuizEditor" class="add-question-button">{{$t('loc.LessonQuiz6')}}</el-button>
              </template>
            </el-skeleton>
          </div>
        </el-collapse-transition>
      </el-card>
      <!-- 家庭活动 -->
      <el-card shadow="never" v-if="!isCenterLesson && isShowHomeActivity" class="w-full m-t-sm home-actiovities-card" body-style="height: 100%;">
        <!-- 家庭活动 -->
        <div slot="header" class="display-flex justify-content-between align-items">
          <!-- 标题 -->
          <div class="display-flex align-items">
            <img src="~@/assets/img/lesson2/unitPlanner/at_home_activities.png" alt="" height="32">
            <div class="font-size-20 font-bold lg-margin-left-8 lg-color-success">{{$t('loc.unitPlannerLessonAtHomeActivities')}}</div>
          </div>
          <div>
            <!-- 重新生成 -->
            <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top">
              <el-button icon="el-icon-refresh-right font-size-20" class="el-button-icon"
                         @click="regenerateHomeActivity()"
                         :loading="(generateHomeActivityLoading || loading)">
              </el-button>
            </el-tooltip>
            <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
                :class="homeActivitiesCollapse ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
                @click="homeActivitiesCollapse = !homeActivitiesCollapse"></i>
          </div>
        </div>
        <el-collapse-transition>
          <div class="lg-padding-20" v-show="!homeActivitiesCollapse">
            <!-- 内容 -->
            <el-skeleton :rows="5" animated :loading="(generateHomeActivityLoading || loading) && !lesson.homeActivity">
              <template>
                <el-form-item prop="homeActivity">
                  <!-- <editor v-model="lesson.homeActivity" @blur="validateLessonForm('homeActivity')"/> -->
                  <el-input
                    v-model="lesson.homeActivity"
                    type="textarea"
                    @blur="validateLessonForm('homeActivity')"
                    :placeholder="$t('loc.LessonHomeActivityPlaceholder')"
                    :autosize="{ minRows: 4, maxRows: 10}"/>
                </el-form-item>
              </template>
            </el-skeleton>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
    <!-- 分组提示弹窗 -->
    <el-dialog
        :close-on-click-modal="false"
        title="Confirmation"
        :visible.sync="adaptationConfirmVisible"
        :top="'60px'"
        :before-close="handleAdaptationConfirmClose"
        width="70%">
      <div class="m-b-sm">
        {{$t('loc.unitPlannerPersonalizePlanTitle')}}
      </div>
      <ChildList
          class="child-list"
          :groupId="adaptationGroupId"
          :editable="false"
          :tableHeight="'calc(100vh - 500px)'"
      ></ChildList>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
                <el-button @click="confirmAdaptation(false)">General Instructions Only</el-button>
                <el-button type="primary" @click="confirmAdaptation(true)">{{$t('loc.unitPlannerPersonalizePlanConfirm')}}</el-button>
            </span>
    </el-dialog>
    <!-- 选择测评点弹窗 -->
    <el-dialog
        :close-on-click-modal="false"
        custom-class="update-standards-and-regenerate-dialog"
        :title="$t('loc.unitPlannerUpdateStandardsRegenerate')"
        :visible.sync="regenerateLessonVisible"
        width="600px">
      <el-form
          ref="regenerateLessonFormRef"
          label-position="top"
          label-width="100px"
          :model="regenerateLesson"
          :rules="regenerateLessonRules"
      >
        <!-- 描述信息 -->
        <div class="m-b-sm">
          {{$t('loc.unitPlannerEasilyModify')}}
        </div>
        <!-- 框架 -->
        <el-form-item :label="$t('loc.framework')" prop="framework" v-show="false">
          <el-select v-model="regenerateLesson.frameworkId" filterable :placeholder="$t('loc.pleaseSelect')"
                     @change="changeFramework" class="w-full">
            <el-option
                v-for="framework in frameworks"
                :key="framework.id"
                :label="framework.name"
                :value="framework.id">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 测评点 -->
        <el-form-item :label="$t('loc.unitPlannerMeasuresStandards2')" prop="measures">
          <el-select v-model="regenerateLesson.measures" filterable multiple class="w-full update-lesson-measure-select"
                     :loading="loadingFrameworkLoading" :popper-append-to-body="false">
            <el-option-group
                v-for="domain in domains"
                :key="domain.id"
                :label="domain.name">
              <el-option
                  v-for="measure in domain.children"
                  v-show="!measure.iepmeasure"
                  :key="measure.id"
                  :label="measure.abbreviation"
                  :value="measure.abbreviation">
                <span v-if="measure.name && measure.abbreviation !== measure.name && measure.name !== null">{{ measure.abbreviation }}: {{ measure.name }}</span>
                <span v-else>{{ measure.abbreviation }}: {{ measure.description }}</span>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
      </el-form>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
          <el-button @click="regenerateLessonVisible = false">{{$t('loc.cancel')}}</el-button>
          <el-button type="primary" @click="updateLessonDetail(true)">{{$t('loc.unitPlannerConfirmRegenerate')}}</el-button>
      </span>
    </el-dialog>
    <!-- 自定义 Prompt -->
    <el-dialog
        :close-on-click-modal="false"
        title="Custom prompt"
        :visible.sync="customPromptVisible"
        width="60%">
      <el-input v-model="customPrompt" type="textarea" :autosize="{ minRows: 10, maxRows: 20}" class="r-2x"/>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="generateLessonCover(customPrompt)">{{$t('loc.unitPlannerStep3Confirm')}}</el-button>
            </span>
    </el-dialog>

    <!-- 选择目标样式弹框 -->
    <el-dialog
        :close-on-click-modal="false"
        title="Choose Your Objectives Style Before Editing"
        :visible.sync="styleSelectionDialogVisible"
        width="600px"
        class="no-body-top-padding"
        :show-close="true">
      <div class="style-selection-content">
        <p class="style-selection-tip">
          We've created two versions of your lesson objectives. Please select the one you prefer before making any edits.
        </p>
        
        <div class="style-selection-options">
          <!-- Formal Description 选项 -->
          <div class="style-selection-option"
               @click="confirmStyleSelection('formal')">
            <div class="style-option-title">Formal Description</div>
            <div class="style-option-preview">{{ lesson.formalDescription }}</div>
          </div>

          <!-- Student-Friendly 选项 -->
          <div class="style-selection-option"
               @click="confirmStyleSelection('studentFriendly')">
            <div class="style-option-title">Student-Friendly</div>
            <div class="style-option-preview">{{ lesson.studentFriendly }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import MediasUploader from '@/views/curriculum/components/meida/MediasUploader.vue'
import ChildList from '@/views/curriculum/roster/components/ChildList.vue'
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import FileUtils from '@/utils/file'
import { equalsIgnoreCase } from '@/utils/common'
import {
  UnitPlanCenterIconMap, UnitPlanCenterTags, UnitPlanCenterTagsI18n,
  UnitPlanITCenterIconMap,
  UnitPlanITCenterTags,
  UnitPlanITCenterTagsI18n, UnitPlanKCenterIconMap, UnitPlanKCenterTags, UnitPlanKCenterTagsI18n
} from '@/utils/constants'
import ResourceSettings from '@/views/modules/lesson2/lessonLibrary/editor/ResourceSettings.vue'
import { parseStreamData } from '@/utils/eventSource'
import regenerateIcon from '@/assets/img/lesson2/plan/regenerate.svg'
import CurriculumMediaViewer from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMediaViewer.vue'
import ClrEditComponent from '@/views/modules/lesson2/unitPlanner/components/editor/ClrEditComponent.vue'
import LessonQuizWindow from '@/views/modules/lesson2/lessonLibrary/editor/LessonQuizWindow.vue'
import Sortable from 'sortablejs/modular/sortable.core.esm.js'
import ImplementationStepSources
  from '@/views/modules/lesson2/unitPlanner/components/editor/ImplementationStepSources.vue'
import TeachingTipsStandard from '@/views/modules/lesson2/lessonLibrary/editor/TeachingTipsStandard'
import TypicalBehaviorsTab from '@/views/modules/lesson2/lessonLibrary/editor/TypicalBehaviorsTab.vue'
import { isBloomQuiz } from '@/utils/common'
import LessonTemplatePreview from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplatePreview'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import frameworkUtils from '@/utils/frameworkUtils'
import LessonMaterialInput from '@/views/modules/lesson2/lessonLibrary/editor/lessonMaterialInput/index'
import LessonSlides from '@/views/modules/lesson2/unitPlanner/components/lessonSlides'
import ReplaceCover from '@/views/modules/lesson2/unitPlanner/components/editor/ReplaceCover.vue'
import ResourceAddModal from '@/views/modules/lesson2/unitPlanner/components/editor/ResourceAddModal.vue'
import ResourceEditModal from '@/views/modules/lesson2/unitPlanner/components/editor/ResourceEditModal.vue'
import tools from '@/utils/tools'
import CulturallyLinguisticallyResponsive from '@/views/modules/lesson2/lessonLibrary/editor/CulturallyLinguisticallyResponsive'
import UniversalDesignLearning from '@/views/modules/lesson2/lessonLibrary/editor/UniversalDesignLearning'
import LessonApi from '@/api/lessons2'
import ReadingPassage from '@/views/modules/lesson2/component/ReadingPassage.vue'

export default {
  props: {
    item: {
      type: Object,
      default: () => {
      }
    },
    lesson: {
      type: Object,
      default: () => {
      }
    },
    // 是否可以编辑
    editable: {
      type: Boolean,
      default: false
    },
    // 典型行为加载状态
    generateTypicalBehaviorsLoading: {
      type: Boolean,
      default: false
    },
    // 生成差异教学加载状态
    generateUniversalDesignForLearningLoading: {
      type: Boolean,
      default: false
    },
    // 生成标准教学指导加载状态
    generateTeachingTipsForStandardLoading: {
      type: Boolean,
      default: false
    },
    // 生成学生画像加载状态
    generatePortraitGraduateLoading: {
      type: Boolean,
      default: false
    },
    // 生成文化差异加载状态
    generateCulturallyResponsiveInstructionLoading: {
      type: Boolean,
      default: false
    },
    // 生成差异教学加载状态
    generateUniversalDesignForLearningGroupLoading: {
      type: Boolean,
      default: false
    },
    // 生成文化差异加载状态
    generateCulturallyResponsiveInstructionGroupLoading: {
      type: Boolean,
      default: false
    },
    generateLessonQuizLoading: {
      type: Boolean,
      default: false
    },
    isK12Grade: {
      type: Boolean,
      default: false
    },
    // 生成家庭活动加载状态
    generateHomeActivityLoading: {
      type: Boolean,
      default: false
    },
    // 更新课程 Loading
    updateLessonLoading: {
      type: Boolean,
      default: false
    },
    // 生成 Loading
    loading: {
      type: Boolean,
      default: false
    },
    generateLessonSourceLoading: {
      type: Boolean,
      default: false
    },
    generatedLessonSources: {
      type: Boolean,
      default: false
    },
    // 映射的框架
    mappedDomains: {
      type: Array,
      default: () => []
    },
    // 保存课程方法
    saveLesson: {
      type: Function
    },
    // 是否从 Unit Planner 过来
    isUnitPlanner: {
      type: Boolean,
      default: false
    }
  },

  components: {
    CurriculumMediaViewer,
    Editor,
    MediasUploader,
    ChildList,
    ClrEditComponent,
    LessonQuizWindow,
    ImplementationStepSources,
    TeachingTipsStandard,
    LessonTemplatePreview,
    LessonTemplateSelectModal,
    TypicalBehaviorsTab,
    LessonMaterialInput,
    ResourceSettings,
    LessonSlides,
    ReplaceCover,
    ResourceAddModal,
    ResourceEditModal,
    CulturallyLinguisticallyResponsive,
    UniversalDesignLearning,
    ReadingPassage
  },

  computed: {
    ...mapState({
      unit: state => state.curriculum.unit,
      frameworks: state => state.curriculum.frameworks,
      promptManagement: state => state.curriculum.promptManagement, // Prompt 管理
      isCG: state => state.curriculum.isCG, // 是否是 CG 项目
      baseInfo: state => state.curriculum.unit.baseInfo,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      bloomQuizSetting: state => state.curriculum.bloomQuizSetting,
      currentUser: state => state.user.currentUser, // 当前用户
      showSurvey: state => state.unit.showSurvey, // 是否显示满意度调查问卷
      agencyLearnerProfile: state => state.lesson.agencyLearnerProfile
    }),
    // 年龄组是否是 K - Grade 12
    kToGrade12 () {
      if (this.baseInfo && this.baseInfo.grade) {
        return ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group'].includes(this.baseInfo.grade)
      }
      return false
    },
    lectureSlidesExpandGrade () {
      if (this.baseInfo && this.baseInfo.grade) {
        return ['TK (4-5)'].includes(this.baseInfo.grade)
      }
      return false
    },
    // 是否显示课程模板功能
    showLessonTemplate () {
      return this.eduProtocolsTemplateApplyOpen && this.kToGrade12 && !this.isCenterLesson
    },
    forLearnersWithIEPS () {
          return this.$t('loc.unitPlannerLessonForLearnersIEPs')
      },
    activityDescription () {
          return this.$t('loc.activityDescription')
      },
    forEnglishLanguageLearners () {
      if (this.kToGrade12) {
        return this.$t('loc.unitPlannerLessonForEnglishLanguageLearners')
      } else {
        return this.$t('loc.unitPlannerLessonForDualLanguageLearners')
      }
    },
    support () {
          return this.$t('loc.unitPlannerLessonSupport')
      },
    inclusiveLearning () {
          return this.$t('loc.unitPlannerCLRAndUDLInclusiveLearningGroups')
      },
    // 是否存在可用资源
    hasAvilableResources () {
      if (!this.lesson || !this.lesson.lessonClrAndSources) {
        return false
      }
      const sources = this.lesson.lessonClrAndSources.sources
      return sources && sources.filter(source => !source.hidden).length > 0
    },
    clrWithSubscript: {
      get () {
        return this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.clr
      },
      set (val) {
        this.$set(this.lesson.lessonClrAndSources, 'clr', val)
      }
    },
    isAppearShowSourceBtn () {
      // 判断资源列表是否存在，如果存在才显示 showSources 按钮
      if (this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.sources) {
        return this.lesson.lessonClrAndSources.sources.length > 0
      }
      return false
    },


    // 框架标准名称
    standardName () {
      // 当前单元使用的框架 ID
      let frameworkId = this.unit.baseInfo.frameworkId
      if (!this.frameworks || !frameworkId) {
        return ''
      }
      let standardName = ''
      // 遍历框架获取框架名称
      this.frameworks.forEach(framework => {
        if (frameworkId.toLowerCase() === framework.id.toLowerCase()) {
          standardName = framework.standardName
        }
      })
      return standardName
    },
    // 测评点名称
    measureName () {
      return function (abbr, isMapped) {
        if (!abbr) {
          return ''
        }
        // 没有框架信息，直接返回
        if (!this.domains || this.domains.length === 0) {
          return abbr
        }
        // 对应的测评点
        let measure = this.getMeasureByAbbr(this.domains, abbr, isMapped)
        // 没有找到测评点，则直接返回缩写
        if (!measure) {
          return abbr
        }
        if (abbr === measure.name) {
          return abbr
        }
        // 拼接缩写和名称
        if (measure.name) {
          return `${abbr}: ${measure.name}`
        }
        return `${abbr}`
      }
    },
    // 测评点描述
    measureDescription () {
      return function (abbr, isMapped) {
        if (!abbr) {
          return ''
        }
        // 没有框架信息，直接返回
        if (!this.domains || this.domains.length === 0) {
          return ''
        }
        // 对应的测评点
        let measure = this.getMeasureByAbbr(this.domains, abbr, isMapped)
        // 没有找到测评点，则直接返回
        if (!measure) {
          return ''
        }
        // 拼接缩写和名称
        return measure.description
      }
    },
    // 测评点名字加描述
    measureNameDescription () {
      return function (abbr, isMapped) {
        if (!abbr) {
          return ''
        }
        // 没有框架信息，直接返回
        if (!this.domains || this.domains.length === 0) {
          return ''
        }
        // 对应的测评点
        let measure = this.getMeasureByAbbr(this.domains, abbr, isMapped)
        // 没有找到测评点，则直接返回缩写
        if (!measure) {
          return ''
        }
        if (measure.description && measure.description.trim() !== '' &&
          measure.name && measure.name.trim() !== '') {
          // 拼接缩写和名称
          return `${measure.name}: ${measure.description}`
        } else if (measure.name && measure.name.trim() !== '') {
          return `${measure.name}`
        } else if (measure.description && measure.description.trim() !== '') {
          return `${measure.description}`
        } else {
          return ``
        }
      }
    },
    // 课程测评点信息
    lessonMeasures () {
      let measures = []
      // 从 domains 中找到对应的测评点
      this.lesson && this.lesson.measures.forEach(abbr => {
        // 根据测评点简写获取测评点详细信息
        let measure = this.getMeasureByAbbr(this.domains, abbr)
        // 如果当前有 question 且找到了对应测评点
        if (this.currentQuestions && measure) {
          for (let i = 0; i < this.currentQuestions.length; i++) {
            const question = this.currentQuestions[i]
            // 设置测评点信息
            if (measure && question.measureAbbreviation === measure.abbreviation) {
              question.measureName = measure.name
              question.measureId = measure.id
              break
            }
          }
        }
        if (measure) {
          measures.push(measure)
        }
      })
      return measures
    },
    // 班级名称
    adaptationGroupName () {
      if (!this.groups || this.groups.length === 0 || !this.lesson || !this.lesson.groupId) {
        return
      }
      let name = null
      // 遍历班级
      this.groups.forEach(group => {
        if (group && group.id && group.id === this.lesson.groupId) {
          name = group.name
        }
      })
      return name
    },
    // 获取指定属性的值
    getAttrValue () {
      return function (child, attrName) {
        // 不存在返回空
        if (!child || !attrName) {
          return ''
        }
        // 属性列表
        let attrs = child.attrs
        if (!attrs) {
          return ''
        }
        // 匹配到的属性值
        let matchValues = null
        // 遍历属性列表
        attrs.forEach(attr => {
          // 匹配属性名称
          if (attr && attr.name && attr.name.trim().toLowerCase() === attrName.trim().toLowerCase()) {
            // 属性值
            let attrValues = attr.values
            if (attrValues && attrValues.length > 0) {
              matchValues = attrValues
            }
          }
        })
        // 如果有属性值，以逗号分割
        if (matchValues) {
          return matchValues.join(', ')
        }
        // 没有值
        return ''
      }
    },
    // 获取语言和种族
    childRaceAndLanguage () {
      return function (child) {
        let race = this.getAttrValue(child, 'Race')
        let language = this.getAttrValue(child, 'Language')
        let result = ''
        if (race) {
          result += race
        }
        if (language) {
          result += ', ' + language
        }
        return result
      }
    },
    // 获取名称对应的小孩
    getChildByName () {
      return function (childName) {
        if (!this.children) {
          return null
        }
        let filterChildren = this.children.filter(child => child && child.displayName === childName)
        if (!filterChildren || filterChildren.length === 0) {
          return null
        }
        return filterChildren[0]
      }
    },
    // 是否是 IEP
    isIEPChildName () {
      return function (childName) {
        return childName && this.iepChildNames && this.iepChildNames.includes(childName.trim())
      }
    },
    // 是否是 ELD
    isELDChildName () {
      return function (childName) {
        return childName && this.eldChildNames && this.eldChildNames.includes(childName.trim())
      }
    },
    // 是否是 IEP
    isIEP () {
      return function (child) {
        if (!child) {
          return false
        }
        let iepValues = this.getAttrValue(child, 'IEP/IFSP')
        return iepValues && iepValues === 'Yes'
      }
    },
    // 是否是 ELD
    isELD () {
      return function (child) {
        if (!child) {
          return false
        }
        let values = this.getAttrValue(child, 'ELD')
        return values && values === 'Yes'
      }
    },
    // 是否是 CAPTKLF 课程
    isCAPTKLF () {
      return frameworkUtils.isCAPTKLF(this.unit.baseInfo.frameworkId)
    },
    // 课程显示的典型行为
    lessonTypicalBehaviors () {
      if (!this.lesson || !this.lesson.typicalBehaviors) {
        return []
      }
      if (this.typicalBehaviorsType === 'PTKLF') {
        return this.lesson.typicalBehaviors.filter(behavior => !behavior.mapped)
      } else {
        return this.lesson.typicalBehaviors.filter(behavior => behavior.mapped)
      }
    },
    // 课程显示的校训
    lessonLearnerProfiles () {
      if (!this.lesson || !this.lesson.learnerProfiles) {
        return []
      }
      return this.lesson.learnerProfiles
    },
    // 是否存在子校训
    hasSubRubrics () {
      return this.lessonLearnerProfiles.some(profile => profile.subRubrics && profile.subRubrics.length > 0)
    },
    // 是否存在期望
    hasExpectations () {
      if (this.hasSubRubrics) {
        return this.lessonLearnerProfiles.some(profile => profile.subRubrics.some(sub => sub.rubricsExpectation))
      } else {
        return this.lessonLearnerProfiles.some(profile => profile.rubricsExpectation)
      }
    },
    // 是否展示未映射提示
    showUnMappedTip () {
      // 映射上的典型行为数量
      let mappedCount = this.lesson.typicalBehaviors.filter(behavior => behavior.mapped)
      // 当前实施步骤的 optklf 测评点
      let ptklfMeasureIds = this.lessonMeasures.map(item => item.id)
      // 全部可以映射 psc 框架的 ptklf 测评点
      let canMappedPtklfMeasureIds = []
      // 遍历映射测评点收集所有的 ptklf 测评点
      this.mapFrameworkData.forEach(measure => {
        Object.keys(measure.mappedMeasures).forEach(key => {
          let measureIds = measure.mappedMeasures[key] && measure.mappedMeasures[key].map(x => x.id)
          canMappedPtklfMeasureIds = canMappedPtklfMeasureIds.concat(measureIds)
        })
      })
      // 部分测评点有映射
      return this.isCAPTKLF && !ptklfMeasureIds.every(item => canMappedPtklfMeasureIds.includes(item)) && this.typicalBehaviorsType === 'PS' && mappedCount.length > 0
    },
    // 是否是 Center 课程
    isCenterLesson () {
      return !!this.item.centerGroupName
    },
    // 是否展示家庭活动
    isShowHomeActivity () {
      var grade = this.baseInfo.grade
      // 需要显示家庭活动的年级
      let showHomeActivity = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)', 'K (5-6)', 'Grade 1', 'Grade 2']
      if (showHomeActivity.includes(grade)) {
        return true
      }
      return this.lesson && this.lesson.atHomeActivityTemp
    },

    // 是否全部测评点无映射
    allMeasureUnMapped () {
      // 当前实施步骤的 optklf 测评点
      let ptklfMeasureIds = this.lessonMeasures.map(item => item.id)
      // 全部可以映射 psc 框架的 ptklf 测评点
      let canMappedPtklfMeasureIds = []
      // 遍历映射测评点收集所有的 ptklf 测评点
      this.mapFrameworkData.forEach(measure => {
        Object.keys(measure.mappedMeasures).forEach(key => {
          let measureIds = measure.mappedMeasures[key] && measure.mappedMeasures[key].map(x => x.id)
          canMappedPtklfMeasureIds = canMappedPtklfMeasureIds.concat(measureIds)
        })
      })
      // 未匹配的测评点
      return ptklfMeasureIds.every(item => !canMappedPtklfMeasureIds.includes(item))
    },
    // 过滤掉在 item.rubrics 中的值
    filteredProfiles () {
      if (!this.lessonLearnerProfiles || !this.lessonLearnerProfiles.length) {
        return []
      }
      const result = []
      // item 包含的校训
      let allItemRubrics = this.hasSubRubrics ? this.item.rubrics.map(rubric => rubric.subStandards).flat() : this.item.rubrics
      // 课程包含的校训
      let allLessonRubrics = this.hasSubRubrics ? this.lessonLearnerProfiles.map(profile => profile.subRubrics).flat() : this.lessonLearnerProfiles
      // 找出 item 中不存在的校训
      allLessonRubrics.forEach(rubric => {
        if (!allItemRubrics.some(item => item.title === rubric.rubricsName)) {
          result.push(rubric)
        }
      })
      return result
    },
    // 是否为扩展年龄组（如TK 4-5）
    isExtendedAgeGroup () {
      if (this.lesson && this.lesson.ageGroup) {
        return tools.isExtendedAgeGroup(this.lesson.ageGroup)
      }
      return false
    },
    // 为 CLR 组件构造步骤对象
    stepForClr () {
      return {
        ageGroupName: this.lesson.ageGroup,
        ageGroupValue: this.lesson.ageGroup,
        culturallyResponsiveInstruction: this.lesson.culturallyResponsiveInstruction,
        culturallyResponsiveInstructionGeneral: this.lesson.culturallyResponsiveInstructionGeneral,
        culturallyResponsiveInstructionGroup: this.lesson.culturallyResponsiveInstructionGroup,
        lessonClrAndSources: this.lesson.lessonClrAndSources,
        universalDesignForLearning: this.lesson.universalDesignForLearning,
        universalDesignForLearningGroup: this.lesson.universalDesignForLearningGroup
      }
    },
    // clr loading
    clrLoading () {
      return (this.generateCulturallyResponsiveInstructionLoading || this.loading) && (!this.lesson.lessonClrAndSources || !this.lesson.lessonClrAndSources.clr || this.lesson.lessonClrAndSources.clr === '')
    },
    // udl loading
    udlLoading() {
      return (this.generateUniversalDesignForLearningLoading || this.loading) && (!this.lesson || (!this.lesson.universalDesignForLearning && !this.lesson.universalDesignForLearningGroup))
    },
    // 是否有多种目标样式
    hasMultipleObjectiveStyles() {
      return this.lesson &&
             this.lesson.formalDescription &&
             this.lesson.studentFriendly &&
             this.lesson.formalDescription.trim() !== '' &&
             this.lesson.studentFriendly.trim() !== ''
    },
    // 获取课堂类型标签文本
    getClassroomTypeLabel() {
      return function(classroomType) {
        if (!classroomType) return this.$t('loc.classroomTypeInPerson')
        
        switch (classroomType.toUpperCase()) {
          case 'IN_PERSON':
            return this.$t('loc.classroomTypeInPerson')
          case 'VIRTUAL':
            return this.$t('loc.classroomTypeVirtual')
          default:
            return classroomType
        }
      }
    },
    /**
     * 是否显示课堂类型
     * K 年级及以上展示，但活动类型为 Center/Station 时不展示
     */
    showClassroomType () {
      return !this.isCenterLesson && tools.isK12AgeGroup(this.lesson.ageGroup || this.baseInfo.grade) && !this.lesson.adaptedModuleSwitch && !this.unit.baseInfo.adaptedType
    },
  },
  data () {
    // 自定义校验函数 - 材料文件
    const validateMaterialFiles = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        // 检查描述是否存在
        if (!value.description || value.description.trim() === '') {
          callback(new Error(this.$t('loc.fieldReq')))
        } else {
          callback()
        }
      }
    }
    
    // 校验 UDL IEP 活动描述
    let validateUDLIepActivityDescription = (rule, value, callback) => {
      if (!this.lesson.learnersWithIEP.activityDescription || this.lesson.learnersWithIEP.activityDescription.trim().length === 0) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    // 校验 UDL IEP 支持
    let validateUDLIepSupport = (rule, value, callback) => {
      if (!this.lesson.learnersWithIEP.support || this.lesson.learnersWithIEP.support.trim().length === 0) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    // 校验 UDL ELD 活动描述
    let validateUDLEldActivityDescription = (rule, value, callback) => {
      if (!this.lesson.englishLanguageLearners.activityDescription || this.lesson.englishLanguageLearners.activityDescription.trim().length === 0) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    // 校验 UDL ELD 支持
    let validateUDLEldSupport = (rule, value, callback) => {
      if (!this.lesson.englishLanguageLearners.support || this.lesson.englishLanguageLearners.support.trim().length === 0) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    // 校验文化差异
    let validateCulturallyResponsiveInstruction = (rule, value, callback) => {
      if (!this.lesson.culturallyResponsiveInstruction || this.lesson.culturallyResponsiveInstruction.trim().length === 0) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    // 封面校验
    let validateLessonCover = (rule, value, callback) => {
      if (!this.loading && this.lesson && this.lesson.generateLessonCoverLoading != true && (!this.lesson.cover)) {
        callback(new Error(this.$t('loc.unitCoverCheckTip')))
      } else {
        callback()
      }
    }
    return {
      typicalBehaviorRules: {},
      lessonRules: {
        objectives: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
        ],
        materials: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
        ],
        materialFiles: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' },
          { validator: validateMaterialFiles, trigger: 'blur' },
          { validator: validateMaterialFiles, trigger: 'change' }
        ],
        // keyVocabularyWords: [
        //   { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
        //   { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
        // ],
        implementationSteps: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
        ]
        // iepActivityDescription: [
        //   { validator: validateUDLIepActivityDescription, trigger: 'change' },
        //   { validator: validateUDLIepActivityDescription, trigger: 'blur' }
        // ],
        // iepSupport: [
        //   { validator: validateUDLIepSupport, trigger: 'change' },
        //   { validator: validateUDLIepSupport, trigger: 'blur' }
        // ],
        // eldActivityDescription: [
        //   { validator: validateUDLEldActivityDescription, trigger: 'change' },
        //   { validator: validateUDLEldActivityDescription, trigger: 'blur' }
        // ],
        // eldSupport: [
        //   { validator: validateUDLEldSupport, trigger: 'change' },
        //   { validator: validateUDLEldSupport, trigger: 'blur' }
        // ],
        // culturallyResponsiveInstruction: [
        //   { validator: validateCulturallyResponsiveInstruction, trigger: 'change' },
        //   { validator: validateCulturallyResponsiveInstruction, trigger: 'blur' }
        // ],
        // homeActivity: [
        //   { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
        //   { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
        // ],
        // cover: [
        //   { validator: validateLessonCover, trigger: 'change' }
        // ]
      },
      regenerateLessonVisible: false, // 重新生成课程内容弹窗
      regenerateLesson: {}, // 重新生成课程内容
      regenerateLessonRules: {
        measures: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
        ]
      }, // 重新生成课程内容校验规则
      loadingFrameworkLoading: false, // 加载框架 loading
      domains: [], // 测评点
      confirmed: false, // 课程内容是否已经确认
      getCenterLoading: false, // 获取默认学校 Loading
      groups: [], // 班级列表
      adaptationConfirmVisible: false, // Adaptation 确认弹窗
      adaptationGroupId: null, // Adaptation 班级 ID
      rosterPath: this.$router.resolve({ name: 'roster' }).href, // Roster 页面路由
      listChildrenLoading: false, // 加载学生列表 Loading
      children: [], // 小孩列表
      iepChildNames: [], // IEP 小孩姓名列表
      eldChildNames: [], // ELD 小孩姓名列表
      adaptationConfirmType: null, // Adaptation 确认触发方法类型
      customPromptVisible: false,
      customPrompt: '',
      coverImages: [],
      iconMap: UnitPlanCenterIconMap, // Center 课程图标
      udlCollapse: false, // 差异化教学折叠
      teachingTipsCollapse: false, // 标准教学指导折叠
      readingPassageCollapse: false, // 阅读文章折叠
      udlClassCollapse: false, // 班级定制差异化教学折叠
      clrCollapse: false, // 差异化教学折叠
      clrClassCollapse: false, // 文化差异折叠
      typicalBehaviorsCollapse: false, // 典型行为折叠
      typicalBehaviorsType: 'PS', // 典型行为类型
      lessonQuizCollapse: false, // Formative Quiz 折叠
      homeActivitiesCollapse: false, // 家庭活动折叠
        // 班级定制化的 UDL 数据
        classUDL: {
            learnersWithIEPActivity: '',
            learnersWithIEPSupports: [],
            specificEnglishLanguageActivity: '',
          specificEnglishLanguageSupport: ''
        },
      showSource: false, // 是否显示资源
      currentDocumentId: '', // 当前选中的 domId
      copyLesson: null, // 拷贝的初始的课程信息
      lessonInit: false, // 是否第一次初始化课程内容
      currentQuestions: this.lesson.questions || [], // 当前课程的 quiz 题目列表
      quizRenderKey: 0, // quiz 渲染 key
      sortable: null, // quiz 拖拽实例
      lessonMeasuresInitFinish: false, // lessonMeasures 是否初始化数据完成
      times: null, // 可选时间
      showImpStepSource: true, // 主动控制是否显示实施步骤资源
      isBloom: null, // quiz 模型是否是 bloom
      mapFrameworkData: [], // 映射框架数据
      generatedTeachingTips: false, // 标准教学指导是否已经开始生成
      portraitGraduateCollapse: false, // Portrait of a Graduate 折叠
      showStepResource: false, // 是否显示实施步骤资源
      implementationStepsObserver: null, // 实施步骤问题观察器
      implementationStepsLoading: false, // 实施步骤加载状态
      isAppearShowImpStepSourceBtn: false, // 是否显示实施步骤资源按钮
      previousSubscriptCLRTags: [], // 存储上次解析的角标内容
      universalDesignForLearningCopy: null, // 记录 UDL 的初始数据
      universalDesignForLearningClassSpecialCopy: null, // 记录改编后的 UDL 的初始数据
      // objectives 样式选择相关
      selectedObjectiveStyle: '', // 选择的目标样式 ('formal' 或 'studentFriendly')
      objectiveStyleSelected: false, // 是否已经选择了样式
      styleSelectionDialogVisible: false, // 样式选择弹框是否可见
      dialogSelectedStyle: '', // 弹框中选择的样式
      pendingEditStyle: '', // 待编辑的样式类型
      readingPassage: null, // 阅读文章
      generateReadingPassageLoading: false // 生成阅读文章加载状态
    }
  },
  destroyed () {
    if (this.unit.id && this.copyLesson !== JSON.stringify(this.lesson)) {
      this.$store.commit('curriculum/SET_UNIT_HAS_UPDATED', true)
    }
    // 销毁实施步骤问题观察器
    if (this.implementationStepsObserver) {
      this.implementationStepsObserver.disconnect()
    }
  },
  watch: {
    // 监听课程内容变化
    lesson: {
      deep: true,
      handler (val) {
        if (!this.lessonInit) {
          this.copyLesson = JSON.stringify(val)
          this.lessonInit = true
        }
        if (val.questions) {
          this.currentQuestions = [...val.questions]
          // 因为当前课程的题目列表发生变化，需要重新初始化拖拽实例
          if (this.sortable) {
            this.sortable.destroy()
            this.sortable = null
          }
          // 初始化拖拽实例
          this.initSortable()
        }
      }
    },
    lessonMeasures: {
      deep: true,
      immediate: true,
      handler (val) {
        // 如果 lessonMeasures 不为空且有选择的测评点数据，且初始化完成标记为 false
        if (val && val.length !== 0 && !this.lessonMeasuresInitFinish) {
          // 标记初始化完成
          this.lessonMeasuresInitFinish = true
          // 如果当前没有 question，那么则初始化一个默认的 question
          if (this.lesson && this.lesson.questions && this.lesson.questions.length === 0 && !this.isCenterLesson && (this.isK12Grade || this.isExtendedAgeGroup)) {
            const measures = this.lessonMeasures
            const haveMeasures = measures && measures.length !== 0
            this.currentQuestions = [{
              type: 'Multiple Choice',
              sortIndex: 1,
              question: '',
              answer: '',
              level: this.isBloom ? 'Understand' : 'DOK2',
              measureName: haveMeasures ? measures[0].name : '',
              measureId: haveMeasures ? measures[0].id : '',
              measureAbbreviation: haveMeasures ? measures[0].abbreviation : ''
            }]
            // 更新问题列表
            this.$emit('updateQuestions', this.currentQuestions)
          }
        }
      }
    },
    // 监听课程信息变化,切换课程时滚动到顶部
    'lesson.id': {
      immediate: true,
      handler (val, oldVal) {
        if (val && oldVal) {
          if (val !== oldVal) {
            // 重置目标样式选择状态
            this.selectedObjectiveStyle = ''
            this.objectiveStyleSelected = false
            this.styleSelectionDialogVisible = false
            this.dialogSelectedStyle = ''
            this.pendingEditStyle = ''
          }
          let lessonHeader = document.getElementById('lesson-detail-header')
          if (lessonHeader) {
            setTimeout(() => {
              lessonHeader.scrollIntoView(true)
            }, 0)
          }
        }
      }
    },
    // 滚动到标准教学指导
    generateTeachingTipsForStandardLoading: {
      immediate: true,
      handler (val) {
        if (val) {
          this.generatedTeachingTips = true
          this.scrollIntoView('teachingTipsForStandard', 'start')
        }
      }
    },
    // 监听 UDL 内容变化，处理 UDL 内容
    'lesson.universalDesignForLearning': {
      immediate: true,
      handler (val) {
        this.processUniversalDesignFroLearning(val)
      }
    },
      // 监听班级定制化 UDL 内容变化，处理班级定制化 UDL 内容
      'lesson.universalDesignForLearningGroup': {
        immediate: true,
        handler (val) {
              this.processUniversalDesignFroLearningGroup(val)
        }
      },
    'lesson.objectives': {
      immediate: true,
      handler (val) {
        if (val) {
          this.scrollIntoView('objectives')
        }
      }
    },
    // 监听目标样式字段变化
    hasMultipleObjectiveStyles: {
      immediate: true,
      handler (hasMultiple) {
        if (!hasMultiple) {
          // 如果没有多种样式，重置选择状态
          this.selectedObjectiveStyle = ''
          this.objectiveStyleSelected = false
          this.styleSelectionDialogVisible = false
          this.dialogSelectedStyle = ''
          this.pendingEditStyle = ''
        }
      }
    },
    'lesson.materials': {
      immediate: true,
      handler (val) {
        // 同步更新材料中的 description
        if (this.$refs.lessonMaterialInput) {
          this.$refs.lessonMaterialInput.updateDescription(this.lesson.materials)
        }
        if (val) {
          this.scrollIntoView('materials')
        }
      }
    },
    'lesson.keyVocabularyWords': {
      immediate: true,
      handler (val) {
        if (val) {
          this.scrollIntoView('keyVocabularyWords')
        }
      }
    },
    'lesson.implementationSteps': {
      immediate: true,
      handler (val) {
        if (val !== undefined) {
          // 标准教学指导已经开始生成过后，不再监听实施步骤的滚动防止页面向上滚动
          if (!this.generatedTeachingTips) {
            this.scrollIntoView('implementationSteps')
          }
          const impStepAndSource = this.lesson.lessonImpStepAndSource
          if (impStepAndSource && impStepAndSource.sources && impStepAndSource.sources.length > 0) {
            this.isAppearShowImpStepSourceBtn = true
            this.$set(this.lesson, 'lessonImpStepAndSource', {
              ...this.lesson.lessonImpStepAndSource,
              impStepContent: val
            })
          }
        }
      }
    },
    'lesson.lessonClrAndSources': {
      immediate: true,
      deep: true,
      handler (value) {
        if (value && !value.clr && value.sources) {
          this.lesson.lessonClrAndSources.sources.forEach(source => {
            this.$set(source, 'hidden', true)
            this.currentDocumentId = ''
          })
        }
        // 判断资源数据是否存在
        if (value) {
          // 获取资源列表
          const sources = this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.sources
          // 获取带有角标的 clr 内容
          const clr = this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.clr
          // 如果不存在 clr，则直接返回
          if (!clr || !sources) {
            return
          }

          // 获取资源的角标
          const subscripts = sources.map(source => source.subscript)

          // 解析为DOM处理，提取角标内容
          const parser = new DOMParser()
          const doc = parser.parseFromString(clr, 'text/html')
          // 存放根据 imp-script 标签解出来的内容
          const currentTags = []
          // 遍历所有的 <imp-script> 标签
          doc.querySelectorAll('a').forEach(scriptTag => {
            currentTags.push(scriptTag.textContent)
          })

          // 判断 clr 中是否存在某个角标
          subscripts.forEach(subscript => {

            // 检查当前 tags 数组中是否包含当前 subscript
            const existsInCurrentTags = currentTags.some(tag => tag.includes(`[${subscript}]`))

            // 检查上一次 tags 数组中是否包含当前 subscript
            const existsInPreviousTags = this.previousSubscriptCLRTags.some(tag => tag.includes(`[${subscript}]`))


              this.lesson.lessonClrAndSources.sources.forEach(source => {
                if (source.subscript === subscript) {
                if (existsInCurrentTags) {
                  // 如果当前存在，显示资源
                  this.$set(source, 'hidden', false)
                } else if (!existsInCurrentTags && existsInPreviousTags) {
                  // 如果当前不存在，但上次存在，则隐藏
                  this.$set(source, 'hidden', true)
                } else if (!existsInCurrentTags && !existsInPreviousTags) {
                  // 如果当前和上次都不存在
                  if (source.noMatchSubscript) {
                    // 如果未匹配到关键词，说明自定义不需要隐藏
                  this.$set(source, 'hidden', false)
                  } else {
                    // 如果匹配到关键词，但不存在，需要隐藏
                    this.$set(source, 'hidden', true)
                }
                }
            }
          })
          })

          // 更新previousTags以便下次比较
          this.previousSubscriptCLRTags = [...currentTags]
        }
      }
    },
    // activityTime 变更赋值给 UnitReview
    'lesson.activityTime': {
      handler (newValue) {
        this.$emit('updateActivityTime', newValue) // 触发自定义事件通知父组件
      },
      immediate: true, // 在组件实例化时立即触发
      deep: true
    }
  },
  async created () {
      // 获取班级列表
      if (this.isCG || this.isCurriculumPlugin) {
          await this.getUserDefaultCenter()
      }
    // 初始化测评点信息
    if (!this.domains || this.domains.length === 0) {
      this.getFrameworkDomains(this.unit.baseInfo.frameworkId)
    }
    this.$store.dispatch('curriculum/getQuizBloomSetting')
    .then(() => {
      this.setStepQuizBloomSetting(this.currentQuestions)
    })
    // 课程内容是否确认
    this.confirmed = this.lesson && this.lesson.typicalBehaviors && this.lesson.typicalBehaviors.length > 0
    // 有班级 ID 则获取小孩信息
    if (this.lesson && this.lesson.groupId) {
      this.listChildren(this.lesson.groupId)
    }
    // 获取框架测评点映射
    this.getFrameworkMeasureMap()
    // 记录原始的 UDL 数据，当 IEP 模块不生成内容时回显原始通用内容
    this.initUdlCopyData()
  },
  mounted () {
    // 课程详情曝光事件埋点
    if (this.isCenterLesson) {
      this.$analytics.sendEvent('web_unit_create3_center_detail')
    } else {
      this.$analytics.sendEvent('web_unit_create3_group_detail')
    }
    // 初始化活动和准备时间可选值
    this.times = this.generateTimes()
    // 根据年龄组获取课程主题图标
    this.getCenterIconByGrade()
    if (this.isCAPTKLF) {
      this.typicalBehaviorsType = 'PS'
    } else {
      this.typicalBehaviorsType = 'PTKLF'
    }
    // 初始化实施步骤问题观察器
    this.initImplementationStepsObserver()
    // 监听事件总线
    this.$bus.$on('updateClrLessonSource', (data, itemId) => {
        // 确认是当前组件的事件
        if (itemId === this.item.id) {
          this.updateClrLessonSource(data)
        }
    })
    this.$store.dispatch('getAgencyLearnerProfile')
  },
  beforeDestroy() {
    this.$bus.$off('updateClrLessonSource')
  },
  methods: {
    equalsIgnoreCase,
    
    // 获取轻量导入改编时当前模块是否显示
    getLightAdaptModuleShow(module) {
      // 默认展示
      if(!this.unit || !this.unit.adaptedModuleSwitch || !module) {
        return true
      }
      return this.unit.adaptedModuleSwitch[module]
    },
    
    // 截断文本显示
    truncateText(text, wordLimit = 5) {
      if (!text) return ''
      
      // 移除 HTML 标签
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = text
      const cleanText = tempDiv.textContent || tempDiv.innerText || ''
      
      // 按单词分割
      const words = cleanText.trim().split(/\s+/)
      
      // 如果单词数量超过限制，截断并添加省略号
      if (words.length > wordLimit) {
        return words.slice(0, wordLimit).join(' ') + '...'
      }
      
      return cleanText
    },
    // 选择目标样式
    selectObjectiveStyle(style) {
      this.selectedObjectiveStyle = style
      this.objectiveStyleSelected = true
      let objectiveType = ''
      // 根据选择的样式设置 objectives 内容
      if (style === 'formal') {
        this.$set(this.lesson, 'objectives', this.lesson.formalDescription)
        objectiveType = 'formal_description'
        // 增加埋点
        this.$analytics.sendEvent('cg_unit_objective_style_use_fd')
      } else if (style === 'studentFriendly') {
        this.$set(this.lesson, 'objectives', this.lesson.studentFriendly)
        objectiveType = 'student_friendly'
        // 增加埋点
        this.$analytics.sendEvent('cg_unit_objective_style_use_sf')
      }
      // 设置 lessonObjectiveType
      this.$store.dispatch('curriculum/setLessonObjectiveType', objectiveType)
      // 将 formalDescription 和 studentFriendly 设置为空
      this.$set(this.lesson, 'formalDescription', '')
      this.$set(this.lesson, 'studentFriendly', '')
      // 调用接口，保存用户选择的 objective 类型
      LessonApi.setLessonObjectiveType(objectiveType)
      // 更新 lesson 内容
      this.$emit('updateLesson', false, false, false, false, null, true)
      // 触发课程内容确认
      this.needConfirmLesson()
      // 吐司提示
      this.$message.success('Great! We\'ve set your lesson objectives style.')
    },
    // 处理输入框获得焦点
    handleInputFocus(style) {
      // 如果已经选择了样式，允许编辑
      if (this.objectiveStyleSelected) {
        return
      }
      
      // 如果没有选择样式，显示选择弹框
      this.pendingEditStyle = style
      this.dialogSelectedStyle = ''
      this.styleSelectionDialogVisible = true
      
      // 移除输入框焦点
      this.$nextTick(() => {
        document.activeElement.blur()
      })
    },
    // 确认样式选择
    confirmStyleSelection(style) {
      // 如果传入了样式参数，使用参数；否则使用已选择的样式
      const selectedStyle = style || this.dialogSelectedStyle
      
      if (!selectedStyle) {
        return
      }
      
      // 设置选择的样式
      this.selectObjectiveStyle(selectedStyle)
      
      // 关闭弹框
      this.styleSelectionDialogVisible = false
      
      // 清理临时数据
      this.dialogSelectedStyle = ''
      this.pendingEditStyle = ''
      
      // 重新聚焦到对应的输入框
      this.$nextTick(() => {
        const targetClass = this.selectedObjectiveStyle === 'formal' ?
          '.objective-style-option:first-child .objective-preview-input' :
          '.objective-style-option:last-child .objective-preview-input'
        const targetInput = this.$el.querySelector(targetClass)
        if (targetInput) {
          targetInput.focus()
        }
      })
    },
    // 记录原始的 UDL 数据，当 IEP 模块不生成内容时回显原始通用内容
    initUdlCopyData() {
      if (this.lesson) {
        this.universalDesignForLearningCopy = this.lesson.universalDesignForLearning
        this.universalDesignForLearningClassSpecialCopy = this.lesson.universalDesignForLearningGroup
      }
    },
    // 初始化实施步骤问题观察器
    initImplementationStepsObserver() {
      if (this.showSurvey) {
        this.$nextTick(() => {
          // 等待OverViewTab组件渲染完成
          setTimeout(() => {
            const implementationStepsElement = this.$refs.implementationStepsRef
            if (implementationStepsElement) {
              // 创建IntersectionObserver实例
              this.implementationStepsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                if (entry.isIntersecting) {
                  const key = 'lastSurveyDate' + this.currentUser.user_id
                  localStorage.setItem(key, this.$moment().toISOString())
                  // 元素进入视口，显示问卷
                  const data = {
                    event: 'SHOW_POSTHOG_SURVEY',
                    unitId: this.unit.id
                  }
                  // 向父级发送显示弹窗消息
                  this.$bus.$emit('message', data)
                  this.$store.dispatch('unit/setShowSurvey', false)
                  // 只触发一次，之后取消观察
                  this.implementationStepsObserver.unobserve(entry.target)
                }
              })
            }, {
              threshold: 0.3 // 当30%的元素可见时触发
            });
            // 开始观察引导性问题元素
            this.implementationStepsObserver.observe(implementationStepsElement)
            }
          }, 100); // 给予足够的时间让组件渲染
        })
      }
    },
    // 生成课程幻灯片
    generateLessonSlides () {
      this.$nextTick(() => {
        this.$refs.lessonSlidesRef && this.$refs.lessonSlidesRef.generateSlides()
      })
    },
    // 更新课程 Slides 生成状态
    updateGenerateLessonSlidesLoading (loading) {
      this.$emit('updateGenerateLessonSlidesLoading', loading)
    },
    // 更新课程模板类型
    updateLessonTemplate (type) {
      this.item.lessonTemplateType = type
      this.$analytics.sendEvent('cg_unit_create_lesson_edualter')
      this.$emit('updateLessonTemplate', type)
    },
    // 更新自定义模板生成状态
    updateGenerateCustomTemplateLoading (val) {
      this.$emit('updateGenerateCustomTemplateLoading', val)
    },
    // 更新课程模版信息
    updateLessonTemplateInfo (lessonTemplate, templateId) {
      // 如果 lessonTemplate 不存在, 则清空课程模板
      if (!lessonTemplate) {
        if (this.lesson.lessonTemplate && equalsIgnoreCase(this.lesson.lessonTemplate.id, templateId)) {
          this.item.lessonTemplateType = null
          this.lesson.lessonTemplate = null
        }
      } else {
        // 更新课程模板信息
        this.lesson.lessonTemplate = lessonTemplate
      }
      this.$emit('updateLesson', false, false, false, false, null)
    },
    // 获取框架测评点映射
    getFrameworkMeasureMap () {
      this.$store.dispatch('getMapFrameworkData').then(res => {
        this.mapFrameworkData = res
      })
    },
    // 根据年龄组获取课程主题图标
    getCenterIconByGrade () {
      var grade = this.lesson.ageGroup || this.baseInfo.grade
      let iTGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)']
      let psGrades = ['PS/PK (3-4)', 'TK (4-5)']
      let kToGrade2 = ['K (5-6)', 'Grade 1', 'Grade 2']
      if (iTGrades.includes(grade)) {
        this.iconMap = UnitPlanITCenterIconMap
      } else if (psGrades.includes(grade)) {
        this.iconMap = UnitPlanCenterIconMap
      } else if (kToGrade2.includes(grade)) {
        this.iconMap = UnitPlanKCenterIconMap
      }
    },
    // 修改 quiz 模型
    changeQuizModel (val) {
      this.$confirm(this.$t('loc.lessonQuizLevelStandsChangeTip'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.unitPlannerConfirmRegenerate'),
        cancelButtonText: this.$t('loc.cancel'),
        closeOnClickModal: false
      }).then(() => {
        this.isBloom = val
        this.$store.dispatch('curriculum/setQuizBloomSetting', val)
        this.$emit('regenerateQuizQuestion')
      }).catch(() => {
        this.isBloom = !val
      })
    },
    isBloomQuiz,
    // 检查步骤中的测试是否 Bloom 测验
    setStepQuizBloomSetting (questions) {
      if (questions && questions.length > 0 && (!!questions[0].question || !!questions[0].answer)) {
        this.isBloom = this.isBloomQuiz(questions)
      } else {
        this.isBloom = this.bloomQuizSetting
      }
    },
    regenerateQuizQuestion (param) {
      // 删除掉 question 中展示重新生成按钮的标记属性
      this.currentQuestions && this.$delete(this.currentQuestions[param.questionIndex], 'showQuizRegenerateButton')
      // 获取 quiz 中测评点相同的所有问题并进行拼接
      let questionArray = []
      this.currentQuestions && this.currentQuestions.forEach(question => {
        if (question.measureAbbreviation === param.measureAbbreviation && question.question && question.question !== '') {
          questionArray.push(question.question)
        }
      })
      // 以换行符进行拼接
      const question = questionArray.join('\n')
      // 如果为空则设置为 null
      param.question = (question && question === '') ? null : question
      this.$emit('regenerateQuizQuestion', param)
    },
    initSortable () {
      const lessonQuizWindows = this.$el.querySelector('.lesson-quiz-windows')
      if (lessonQuizWindows) {
        const ops = {
          // 拖拽时过渡效果，0 的话没有过渡效果
          animation: 300,
          // 设置拖拽句柄，当用户鼠标在该位置上拖拽，就允许拖拽
          handle: '.quiz-editor-drag-handle',
          dataIdAttr: 'data-id',
          scroll: true,
          scrollSensitivity: 60,
          scrollSpeed: 10,
          bubbleScroll: true,
          forceFallback: true,
          onEnd: (evt) => {
            // 移动数组元素，为了保证每个 quiz 编辑窗的题目序号跟着拖拽位置而变化
            const dragFinishedArray = this.sortable.toArray()
            // 移动数组元素，为了保证每个 quiz 编辑窗的题目序号跟着拖拽位置而变化
            this.reorderInPlace(this.currentQuestions, dragFinishedArray)
            this.currentQuestions && this.currentQuestions.forEach((question, index) => {
              question.sortIndex = index + 1
            })
            this.$emit('updateQuestions', this.currentQuestions)
          }
        }
        this.sortable = Sortable.create(lessonQuizWindows, ops)
      }
    },
    // 根据 order 数组中的顺序对 arr 数组进行排序
    reorderInPlace (currentQuestions, dragFinishedArray) {
      // 创建一个访问标记数组
      const visited = new Array(currentQuestions.length).fill(false)
      for (let i = 0; i < currentQuestions.length; i++) {
        // 当前元素没有被访问过才走交换位置逻辑
        if (!visited[i]) {
          let currentIndex = i
          let nextIndex = dragFinishedArray[currentIndex]
          // 循环遍历没有访问过的位置数组
          while (!visited[nextIndex]) {
            // 交换数据数组中元素位置
            [currentQuestions[currentIndex], currentQuestions[nextIndex]] = [currentQuestions[nextIndex], currentQuestions[currentIndex]]
            // 添加访问标记
            visited[currentIndex] = true
            currentIndex = nextIndex
            // 获取下一次要处理的位置
            nextIndex = dragFinishedArray[currentIndex]
          }
          visited[currentIndex] = true
        }
      }
      return currentQuestions
    },
      // 获取默认学校信息
    async getUserDefaultCenter () {
          // 如果是课程插件，则不需要获取默认学校信息
          // if (this.isCurriculumPlugin) {
          //   return
          // }
          // 开启 Loading
          this.getCenterLoading = true
          // 调用接口
          return new Promise((resolve, reject) => {
              this.$axios.get($api.urls().getCGUserDefaultCenter).then((res) => {
                  // 班级列表
                  this.groups = res.groups
                  if (this.groups && this.groups.length > 0) {
                      this.adaptationGroupId = this.groups[0].id
                  }
                  // 停止 Loading
                  this.getCenterLoading = false
                  resolve()
              }).catch(error => {
                  // 停止 Loading
                  this.getCenterLoading = false
                  console.log(error)
                  this.$message.error(error.response.data.error_message)
                  reject(error)
              })
          })
      },
    changeClrContent (value) {
      this.$emit('updateCulturallyResponsiveInstruction', value)
    },
    addQuizEditor () {
      // 添加 quiz 编辑窗
      const measures = this.lessonMeasures
      const newQuizQuestion = {
        type: 'Multiple Choice',
        sortIndex: this.currentQuestions ? this.currentQuestions.length + 1 : 1,
        question: '',
        answer: '',
        showQuizRegenerateButton: false,
        level: this.isBloom ? 'Understand' : 'DOK2',
        measureName: measures ? measures[0].name : '',
        measureId: measures ? measures[0].id : '',
        measureAbbreviation: measures ? measures[0].abbreviation : ''
      }
      this.currentQuestions.push(newQuizQuestion)
      // 更新问题列表
      this.$emit('updateQuestions', this.currentQuestions)
    },
    deleteQuizEditor (questionIndex) {
      // 删除 quiz 编辑窗
      this.currentQuestions.splice(questionIndex, 1)
      this.currentQuestions.forEach((question, index) => {
        question.sortIndex = index + 1
      })
      // 更新问题列表
      this.$emit('updateQuestions', this.currentQuestions)
    },
    toRefView (event) {
      // 检查点击的是否是 A 标签
      if (event.target.tagName === 'A') {
        // 使用 getAttribute 方法获取 href 属性的值
        let hrefValue = event.target.getAttribute('href')
        if (hrefValue) {
          const hrefs = hrefValue.split('#')
          hrefValue = hrefs[1]
          this.currentDocumentId = hrefValue
          document.getElementById(hrefValue).scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }
    },
    // 更新带有角标的 CLR 内容
    updateClrWithSubscriptContent (content) {
      const lessonClrAndSources = this.lesson.lessonClrAndSources
      lessonClrAndSources.clr = content
      this.$emit('updateClrWithSubscript', lessonClrAndSources)
    },
    // 更新 CLR 资源生成的状态值
    updateGeneratedLessonSources () {
      this.$emit('updateGeneratedLessonSources')
    },
    // 滚动到元素位置
    scrollIntoView (refName, block='center') {
      if (this.loading) {
        this.$refs[refName] && this.$refs[refName].$el.scrollIntoView({ behavior: 'smooth', block: block })
      }
    },
    // 校验课程信息表单
    validateLessonInfo () {
      return new Promise((resolve, reject) => {
        this.$refs.lessonFormRef.validate((valid) => {
          if (valid) {
              resolve(true)
          } else {
            // 滚动到校验失败的表单项
            setTimeout(() => {
              let formItemsWithError = this.$el.querySelectorAll('.is-error')
              let firstItem = formItemsWithError[0]
              if (firstItem) {
                firstItem.scrollIntoView(true)
              }
            }, 0)
            resolve(false)
          }
        })
      })
    },
    // 校验课程信息表单项
    validateLessonForm (field) {
      this.$refs.lessonFormRef.validateField(field)
    },
    // 触发课程内容确认
    needConfirmLesson () {
      this.confirmed = false
    },
    // 重新生成课程弹窗
    updateLessonDetailDialog () {
      if (this.isCenterLesson) {
        this.$analytics.sendEvent('web_unit_create3_center_det_update')
      } else {
        this.$analytics.sendEvent('web_unit_create3_group_det_update')
      }
      this.regenerateLesson = {
        measures: this.lesson.measures,
        frameworkId: this.unit.baseInfo.frameworkId
      }
      this.getFrameworkDomains()
      this.regenerateLessonVisible = true
    },
    // 重新生成课程内容
    updateLessonDetail (updateStandards) {
      if (updateStandards) {
        if (this.isCenterLesson) {
          this.$analytics.sendEvent('web_unit_create3_center_det_update_con')
        } else {
          this.$analytics.sendEvent('web_unit_create3_group_det_update_con')
        }
      }
      this.$refs.regenerateLessonFormRef.validate((valid) => {
        if (valid) {
            this.classUDL = {
                learnersWithIEPActivity: '',
                learnersWithIEPSupports: [],
                specificEnglishLanguageActivity: '',
              specificEnglishLanguageSupport: ''
            }
          // 点击更新并重新生成时同样需要将 showSource 置为 false
          this.showSource = false
          this.$emit('regenerate', this.regenerateLesson.measures, this.regenerateLesson.frameworkId)
          this.regenerateLessonVisible = false
        }
      })
    },
    // 重新生成课程
    regenerateLessonDetail () {
      if (this.isCenterLesson) {
        this.$analytics.sendEvent('web_unit_create3_center_det_regenerate')
      } else {
        this.$analytics.sendEvent('web_unit_create3_group_det_regenerate')
      }
      // 如果重新生成 班级定制化的 UDL，那么就清空一下数据
      this.classUDL = {
          learnersWithIEPActivity: '',
          learnersWithIEPSupports: [],
          specificEnglishLanguageActivity: '',
        specificEnglishLanguageSupport: ''
      }
      // 点击重新生成课程，将 showSource 置为 false
      this.showSource = false
      this.$emit('regenerate')
    },
    // 更新实施步骤数据到 lesson
    upLessonImpStepData (lessonImpStepAndSource) {
      this.$set(this.lesson, 'lessonImpStepAndSource', lessonImpStepAndSource)
      this.$set(this.lesson, 'implementationSteps', lessonImpStepAndSource.impStepContent)
      this.$forceUpdate()
    },
    // 异步调用到实施步骤资源组件
    generateImplementationStepsSource (implementationStepContent, lessonId, showStepResource = false) {
      this.showImpStepSource = true
      this.showStepResource = showStepResource
      // 生成中
      this.$refs.implementationStepSources.generateResourcesByImpStep(implementationStepContent, lessonId)
    },
    // 调用到实施步骤资源组件显示资源
    showImplementationStepsSource () {
      if (this.$refs.implementationStepSources) {
        this.$refs.implementationStepSources.showSource = true
      }
    },
    // 重新生成典型行为
    regenerateTypicalBehaviors () {
      let mapped = this.typicalBehaviorsType === 'PS'
      this.$emit('regenerateTypicalBehaviors', mapped)
    },
    // 从新生成标准教学指导
    regenerateTeachingTips(){
      this.$emit('generateTeachingTips')
    },
    // 重新生成差异教学
    regenerateUniversalDesignForLearning (adaptation) {
      // 如果是改编的课程，调用改变重新生成
      if (adaptation) {
        // 清空 udl 数据
        this.lesson.universalDesignForLearningGroup = ''
        // 开始 Loading
        this.updateUdlLoading(true)
        this.generateUDLData()
        return
      }
        // 如果重新生成 班级定制化的 UDL，那么就清空一下数据
        this.classUDL = {
            learnersWithIEPActivity: '',
            learnersWithIEPSupports: [],
            specificEnglishLanguageActivity: '',
          specificEnglishLanguageSupport: ''
        }
      this.$emit('regenerateUniversalDesignForLearning', adaptation ? this.adaptationGroupId : null)
    },
    generateLessonSources (isLessonImplStep = false) {
      this.$emit('generateLessonSources', isLessonImplStep)
      // 发送埋点事件
      this.$analytics.sendEvent('cg_unit_lesson_resource_regenerate')
    },
    // 重新生成文化差异
    regenerateCulturallyResponsiveInstruction (adaptation) {
      if (this.item.adaptedLesson) {
        this.$refs.culturallyLinguisticallyResponsive.generateCLR(true)
        return
      }
      // 重新生成需要将 showSource 置为默认值
      this.showSource = false
      this.currentDocumentId = null
      this.$emit('regenerateCulturallyResponsiveInstruction', adaptation ? this.adaptationGroupId : null, 'regenerate')
    },
    upNormalClrLoading (val) {
      // 当重新生成时将数据置空，能够出现 loading
      if (val && this.lesson && this.lesson.lessonClrAndSources) {
        this.$set(this.lesson.lessonClrAndSources, 'clr', '')
      }
      this.$emit('upNormalClrLoading',val)
    },
    // 重新生成 quiz
    regenerateLessonQuiz () {
      this.$emit('regenerateLessonQuiz')
    },
    // 重新生成家庭活动
    regenerateHomeActivity () {
      this.$emit('regenerateHomeActivity')
    },
    // 更新课程内容
    updateLesson (adaptation) {
      let generateBehavior = !this.lesson.typicalBehaviors || this.lesson.typicalBehaviors.length === 0
      this.$emit('updateLesson', false, generateBehavior, false, false, adaptation ? this.adaptationGroupId : null)
      this.confirmed = true
    },
    // 切换框架，更新测评点
    changeFramework () {
      this.regenerateLesson.measures = []
      this.getFrameworkDomains()
    },
    getFrameworkDomains (frameworkId) {
      this.loadingFrameworkLoading = true
      // 获取路由参数 unitId
      const unitId = this.$route.params.unitId
      this.$store.dispatch('curriculum/getMeasuresContainTopAndBottom', {
        frameworkId: (frameworkId || this.regenerateLesson.frameworkId),
        unitId: unitId
      }).then(domains => {
        this.domains = domains
        this.loadingFrameworkLoading = false
      })
    },
    // 根据缩写获取测评点信息
    getMeasureByAbbr (domains, abbr, isMapped) {
      if (!abbr || !domains || domains.length === 0) {
        return null
      }
      // 遍历框架
      let allDomains = isMapped ? this.mappedDomains : this.domains
      for (let i = 0; i < allDomains.length; i++) {
        const domain = allDomains[i]
        // 遍历测评点
        for (let j = 0; j < domain.children.length; j++) {
          const measure = domain.children[j]
          if (measure.abbreviation && measure.abbreviation.trim() === abbr) {
            return measure
          }
        }
      }
      return null
    },
    // 获取小孩列表
    listChildren (groupId) {
      // 开始 Loading
      this.listChildrenLoading = true
      // 参数
      let params = {
        pageNum: 1,
        pageSize: 1000,
        sort: 'lastName',
        order: 'asc',
        groupId: groupId
      }
      // 调用接口
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().manageChildren, { params: params }).then(res => {
          this.children = res.results // 小孩列表
          // 遍历小孩
          if (this.children) {
            this.iepChildNames = []
            this.eldChildNames = []
            this.children.forEach(child => {
              if (this.isIEP(child)) {
                this.iepChildNames.push(child.displayName)
              }
              if (this.isELD(child)) {
                this.eldChildNames.push(child.displayName)
              }
            })
          }
          // 停止 Loading
          this.listChildrenLoading = false
            // 如果是 CG 项目，则需要重新解析下班级定制化的差异化教学内容
            if (this.isCG) {
                // 加载完小孩以后再解析一下班级定制化的差异化教学内容
                this.processUniversalDesignFroLearningGroup(this.lesson.universalDesignForLearningGroup)
            }
          resolve()
        }).catch(error => {
          // 停止 Loading
          this.listChildrenLoading = false
          console.log(error)
          this.$message.error(error.response.data.error_message)
          reject(error)
        })
      })
    },
    // 确认 Adaptation 选择
    confirmAdaptation (adaptation) {
      if (this.adaptationConfirmType === 'GENERATE_ALL') {
        this.updateLesson(adaptation)
      } else if (this.adaptationConfirmType === 'REGENERATE_UDL') {
        this.regenerateUniversalDesignForLearning(adaptation)
      } else if (this.adaptationConfirmType === 'REGENERATE_CLR') {
        this.regenerateCulturallyResponsiveInstruction(adaptation)
      }
      // 更新小孩信息
      if (adaptation && this.adaptationGroupId) {
        this.listChildren(this.adaptationGroupId)
      }
      // 改编课程事件
      this.$analytics.sendActivityEvent('RLP', 'ADAPT_LESSON')
      // 关闭提示弹窗
      this.adaptationConfirmVisible = false
    },
    // 显示弹窗
    showAdaptationConfirm (type) {
      this.adaptationConfirmType = type
      this.adaptationConfirmVisible = true
    },

    updateImagesEvent (files) {
      if (!files || files.length === 0) {
        this.$set(this.lesson, 'cover', null)
      } else if (files.length === 1 && files[0].url) {
        const cover = {
          id: files[0].id,
          url: files[0].url,
          source: files[0].source
        }
        this.$set(this.lesson, 'cover', cover)
      }
      // this.validateLessonForm('cover')
    },

      /**
       * 获取重新生成的图标
       */
      getRegenerateButtonIcon () {
          // 如果生成过展示重新生成图标
          return regenerateIcon
      },

    /**
     * 更新课程封面信息
     */
    updateLessonCover (cover) {
      if (!cover) {
        this.coverImages = []
      } else {
        this.coverImages = [cover]
      }
    },

    /**
     * 生成课程封面
     */
    generateLessonCover (prompt) {
      this.customPromptVisible = false
      this.$emit('generateCover', prompt)
    },

    setCoverInfo (cover) {
      this.$emit('setCoverInfo', cover)
    },

    /**
     * 解析差异化教学内容
     */
    processUniversalDesignFroLearning (val) {
      // 不同年龄解析模型不同， K 年龄以上用 For English Language Learners ， K 年龄以下用 For Dual Language Learners
      let universalDesignForLearning = parseStreamData(val, [
          { key: 'learnersWithIEP', name: 'For Learners with IEPs' },
          // 兼容历史数据
          { key: 'englishLanguageLearners', name: ['For Dual Language Learners', 'For English Language Learners'] }
        ])[0]


      if (universalDesignForLearning.learnersWithIEP !== undefined && universalDesignForLearning.learnersWithIEP !== '') {
          let learnersWithIEP = parseStreamData(universalDesignForLearning.learnersWithIEP, [
          { key: 'activityDescription', name: 'Activity Description' },
          { key: 'support', name: ['Support:', 'Support'] }
          ])[0]
          this.$set(this.lesson, 'learnersWithIEP', learnersWithIEP)
      } else {
          this.$set(this.lesson, 'learnersWithIEP', { 'activityDescription': '', 'support': '' })
      }
      if (universalDesignForLearning.englishLanguageLearners !== undefined && universalDesignForLearning.englishLanguageLearners !== '') {
          let englishLanguageLearners = parseStreamData(universalDesignForLearning.englishLanguageLearners, [
          { key: 'activityDescription', name: 'Activity Description' },
          { key: 'support', name: ['Support:', 'Support'] }
          ])[0]
          this.$set(this.lesson, 'englishLanguageLearners', englishLanguageLearners)
      } else {
          this.$set(this.lesson, 'englishLanguageLearners', { 'activityDescription': '', 'support': '' })
      }
    },

      /**
       * 解析班级定制化的差异化教学内容
       */
      processUniversalDesignFroLearningGroup (val) {
          // console.log("班级定制化的 UDL 数据", val)
          // 将 val 转化为对象
          let universalDesignForLearningGroup = parseStreamData(val, [
            { key: 'learnersWithIEP', name: ['For Learners with IEP', 'For Learners with IEPs'] },
            { key: 'englishLanguageLearners', name: 'For English Language Learners' }
          ])[0]
          // 解析 learnersWithIEP 数据
          if (universalDesignForLearningGroup.learnersWithIEP !== undefined && universalDesignForLearningGroup.learnersWithIEP !== '') {
              let learnersWithIEP = parseStreamData(universalDesignForLearningGroup.learnersWithIEP, [
                { key: 'activityDescription', name: 'Activity Description' },
                { key: 'support', name: 'Support' }
              ])[0]
              if (learnersWithIEP.activityDescription !== undefined && learnersWithIEP.activityDescription !== '') {
                  // console.log("learningIepActivity", learnersWithIEP.activityDescription)
                  // 将其设置进 班级 的 UDL 数据中
                  this.$set(this.classUDL, 'learnersWithIEPActivity', learnersWithIEP.activityDescription)
              }
              // 开始解析 support 数据
              if (learnersWithIEP.support !== undefined && learnersWithIEP.support !== '') {
                  // 解析其中的小孩及其解释的数据
                  let childIepdifferentiatedSupport = parseStreamData(learnersWithIEP.support, [
                    { key: 'childInfo', name: 'Child' }
                  ])
                  // console.log("解析出来的小孩信息", childIepdifferentiatedSupport)
                  // 如果 childIepdifferentiatedSupport 存在
                  if (childIepdifferentiatedSupport && childIepdifferentiatedSupport.length > 0) {
                      // 将 childIepdifferentiatedSupport 中的每一个元素进行处理
                      // childIepdifferentiatedSupport 中的每一个元素都是一个小孩的信息， 小孩名和描述信息使用 : 进行分割
                      // 存储生成的 learnersWithIEP 中的 support 数据
                      let classLearnersWithIEPSupports = []
                      childIepdifferentiatedSupport.forEach((item) => {
                          if (!item.childInfo) {
                            return
                          }
                          // 对其 childInfo 进行分割， 分为 childName 和 differentiatedSupport
                        let lineIndex = item.childInfo.indexOf('\n')
                          // 定义小孩名称和支持
                          let childName = ''
                          let differentiatedSupport = ''
                          // 如果没有找到换行符， 则直接将其作为小孩名称
                          if (lineIndex === -1) {
                              childName = item.childInfo
                          } else {
                              // 否则就进行分割
                            childName = item.childInfo.substring(0, lineIndex)
                            differentiatedSupport = item.childInfo.substring(lineIndex + 1)
                          }
                          // console.log("未解析前的小孩数据：", item.childInfo, lineIndex)
                          // console.log("解析出来的小孩名称", childName)
                          // console.log("解析出来的小孩支持", differentiatedSupport)
                          if (childName && childName !== '' && (differentiatedSupport !== null && differentiatedSupport !== undefined || differentiatedSupport === '')) {
                              if (this.children && this.children.length > 0) {
                                  // 判断这个小孩是否存在
                                  const findChild = this.children.find(child => child.displayName.trim().toLowerCase() === childName.trim().toLowerCase())
                                  // 如果存在才像其中添加数据
                                  if (findChild) {
                                      // 如果其不为空，则将其放入数组中以供展示
                                      classLearnersWithIEPSupports.push({
                                          childName: childName,
                                          differentiatedSupport: differentiatedSupport
                                      })
                                  }
                              }
                          }
                      })
                      // console.log("存储的 classLearnersWithIEPSupports：", classLearnersWithIEPSupports)
                      this.$set(this.classUDL, 'learnersWithIEPSupports', classLearnersWithIEPSupports)
                  }
              }
          }
          // 解析 englishLanguageLearners 数据
          if (universalDesignForLearningGroup.englishLanguageLearners !== undefined && universalDesignForLearningGroup.englishLanguageLearners !== '') {
              // 移除掉所有以 Note 开头的数据
              universalDesignForLearningGroup.englishLanguageLearners = universalDesignForLearningGroup.englishLanguageLearners.replace(/\(Note:[^)]*\)|Note:[^\n]*/g, '')
              let englishLanguageLearners = parseStreamData(universalDesignForLearningGroup.englishLanguageLearners, [
                { key: 'activityDescription', name: 'Activity Description' },
                { key: 'support', name: 'Support' }
              ])[0]
              if (englishLanguageLearners.activityDescription !== undefined && englishLanguageLearners.activityDescription !== '') {
                  this.$set(this.classUDL, 'specificEnglishLanguageActivity', englishLanguageLearners.activityDescription)
              }
              if (englishLanguageLearners.support !== undefined && englishLanguageLearners.support !== '') {
                  this.$set(this.classUDL, 'specificEnglishLanguageSupport', englishLanguageLearners.support)
              }
          }
          // console.log("解析的 UDl 班级定制化数据: ", this.classUDL)
      },

    // 改变是否可以显示实施步骤资源
    changeShowImpStepSource () {
      this.showImpStepSource = !this.showImpStepSource
      // 如果当前是要隐藏资源，则同时关闭引导弹窗
      if (!this.showImpStepSource) {
        // 调用 API 隐藏引导
        // 如果 ImplementationStepSources 组件引用存在，直接设置 showGuidePopup为false
        if (this.$refs.implementationStepSources) {
          this.$refs.implementationStepSources.showGuidePopup = false
        }
      }
    },
    // 改变是否可以显示资源
    changeShowSource () {
      this.showSource = !this.showSource
    },

      /**
       * 处理分组提示弹窗关闭事件
       */
      handleAdaptationConfirmClose () {
          // 那就只生成基础的 udl 和 clr
          if (this.adaptationConfirmType === 'GENERATE_ALL') {
              this.confirmAdaptation(false)
          }
          // 关闭提示弹窗
          this.adaptationConfirmVisible = false
      },
      /**
       * 生成准备时间和活动时间的选项
       */
      generateTimes () {
        let result = []
        let unit = 'mins'
        let unitValue = 'minutes'
        for (let value = 5; value < 65; value += 5) {
          result.push({
            name: `${value} ${unit}`,
            value: `${value} ${unitValue}`
          })
        }
        return result
      },
      // 重新生成 Portrait of a Graduate
      regeneratePortraitGraduate () {
        this.$emit('regeneratePortraitGraduate')
      },
      // 获取 Portrait of a Graduate 的提示内容
      getPortraitGraduateTip () {
        let templateNameStr = this.item.lessonTemplateType
        if (this.$refs.lessonTemplatePreviewRef) {
          templateNameStr = this.$refs.lessonTemplatePreviewRef.getTemplateNameByType(this.item.lessonTemplateType)
        }
        let portraitGraduateTip = this.$t('loc.lessonPortraitTip', { templateName: templateNameStr })
        // 使用 filteredProfiles 将每个属性添加到 portraitGraduateTip 中
        portraitGraduateTip += this.filteredProfiles
            .map(profile => profile.rubricsName)
            .join(', ') + '.'
        // 返回 portraitGraduateTip
        return portraitGraduateTip
      },

      // 生成 UDL 数据
      async generateUDLData() {
        await this.$refs.universalDesignLearning.syncGenerateUniversalDesign()
      },
      // 更新通用 UDL 数据
      updateUniversalDesignForLearning(generalUniversalDesignForLearning, lessonAgeGroup, mixedAgeGroup) {
        this.lesson.universalDesignForLearning = generalUniversalDesignForLearning;
        this.lesson.mixedAgeDifferentiations = mixedAgeGroup;
      },
      // 更新特定班级的 UDL 数据
      updateUniversalDesignForLearningClassSpecial(universalDesignForLearningClassSpecial, lessonAgeGroup, mixedAgeGroup) {
        this.$set(this.lesson, 'universalDesignForLearningGroup', universalDesignForLearningClassSpecial)
        this.$set(this.lesson, 'mixedAgeDifferentiations', mixedAgeGroup)
      },
      clearUniversalDesignForLearningClassSpecial() {
        this.lesson.universalDesignForLearningGroup = '';
      },
      // 更新 UDL 数据加载状态
      updateUdlLoading(val) {
        this.$emit('updateUdlLoading', val)
      },

    // 改编时 clr 数据同步
    updateClrLessonSource (lessonSources, ageGroupName) {
      this.$set(this.lesson, 'lessonClrAndSources', lessonSources)
      this.$forceUpdate()
    },
    // 改编时，clr group 数据同步
    updateClrGroupLessonSource (clrGroupData) {
      this.$set(this.lesson, 'culturallyResponsiveInstructionGroup', clrGroupData)
      this.$forceUpdate()
    },
    /**
     * UDL CLR 生成使用
     * @param ageGroupName
     */
    getNewLessonInfo (ageGroupName) {
      // 调用 UDL 的保存方法
      // this.fillUDLAndCLRData()
      // 返回新的 lesson 信息
      const lessonInfo = this.collectData()
      // 定义当前年龄组下面的 step
      let currentLessonStep = lessonInfo.steps.find(step => step.ageGroupName === ageGroupName)
      // 如果当前年龄组下面的 step 不存在，那么就返回空
      currentLessonStep = currentLessonStep || {}
      // 对 currentLessonStep 进行解包
      currentLessonStep = {
        ...currentLessonStep,
        universalDesignForLearningGroup: '',
        culturallyResponsiveInstructionGroup: '',
        homeActivity: ''
      }
      // 定义 dlls
      let dlls = lessonInfo.dlls || []
      // 仅仅传递需要的参数
      dlls = dlls.map(dll => {
        return {
          content: dll.content
        }
      })
      // 获取当前课程使用的测评点缩写
      let measureAbbrs = this.lesson.measures.reduce((prev, domain) => {
        // 获取得到所有的领域
        let domains = [domain]
        // 定义领域下面的实际测评点
        let measures = []
        // 如果领域是存在的
        while (domains.length) {
          // 获取第一个领域
          let temp = domains.pop()
          // 测评点无孩子节点
          if (!temp.children || !temp.children.length) {
            // 此时测评点是合理的
            measures.push(temp)
            continue
          }
          // 有孩子节点的为领域
          domains.push(...temp.children)
        }
        return prev.concat(measures.map(m => m.abbreviation))
      }, [])
      // 封装 newLessonInfo 的数据
      return {
        name: lessonInfo.name,
        objectives: lessonInfo.objectives,
        materials: lessonInfo.materials,
        dlls: dlls,
        frameworkId: lessonInfo.frameworkId,
        step: currentLessonStep,
        activityTime: lessonInfo.activityTime,
        prepareTime: lessonInfo.prepareTime,
        measureAbbrs: measureAbbrs,
        themes: this.lesson.themes,
        curAgeGroupName: ageGroupName,
        classroomType: lessonInfo.classroomType
      }
    },

    /**
     * 收集当前课程数据，适用于 unitPlanner 组件
     */
    collectData () {
      let copyLesson = JSON.parse(JSON.stringify(this.lesson))

      // 处理测评点 IDs
      let measureIds = copyLesson.measures.reduce((prev, domain) => {
        let domains = [domain]
        let measures = []
        while (domains.length) {
          let temp = domains.pop()
          // 测评点无孩子节点
          if (!temp.children || !temp.children.length) {
            measures.push(temp)
            continue
          }
          // 有孩子节点的为领域
          domains.push(...temp.children)
        }
        return prev.concat(measures.map(m => m.id))
      }, [])

      // 处理材料数据
      let materials = { descriptions: [] }
      if (copyLesson.materialFiles) {
        // 处理材料描述
        if (copyLesson.materials) {
          materials.descriptions.push(copyLesson.materials)
        }
        // 处理媒体文件
        if (copyLesson.materialFiles.media) {
          materials.mediaId = copyLesson.materialFiles.media.id
          materials.externalMediaUrlId = copyLesson.materialFiles.media.externalMediaUrlId
        }
        // 处理附件
        materials.attachmentMediaIds = (copyLesson.materialFiles.attachmentMedias || []).map(media => media.id)
      }

      // 处理时间格式：提取数字部分
      const extractTimeNumber = (timeString) => {
        if (!timeString) return null
        // 使用正则表达式提取数字部分
        const match = timeString.toString().match(/\d+/)
        return match ? parseInt(match[0]) : null
      }

      // 处理 CLR 资源数据（直接处理 lesson 对象，不是 steps 数组）
      if (copyLesson.lessonClrAndSources && copyLesson.lessonClrAndSources.sources) {
        // 遍历资源列表
        copyLesson.lessonClrAndSources.sources.forEach(source => {
          // 获取角标
          let subscript = source.subscript
          // 拼接需要搜索的文本
          const searchTxt = `<a href="#subscript${subscript}" class="text-primary">[${subscript}]</a>`
          // 如果该资源隐藏了，则保存的时候直接删除即可
          if (source.hidden) {
            copyLesson.lessonClrAndSources.clr = copyLesson.lessonClrAndSources.clr.replace(new RegExp(searchTxt, 'g'), '')
          }
        })
        copyLesson.lessonClrAndSources.sources = copyLesson.lessonClrAndSources.sources.filter(source => !source.hidden)

        // 如果 CLR 内容去除 html 为空时，则将 CLR 内容设置为空
        if (this.removeHtmlTag(copyLesson.culturallyResponsiveInstruction)) {
          copyLesson.culturallyResponsiveInstruction = ''
        }
        // 如果班级定制 CLR 内容去除 html 为空时，则将 CLR 内容设置为空
        if (this.removeHtmlTag(copyLesson.culturallyResponsiveInstructionGroup)) {
          copyLesson.culturallyResponsiveInstructionGroup = ''
        }
        // 判断带角标的 CLR 内容去除 html 标签后是否为空，则将资源 CLR 内容置为空
        if (this.removeHtmlTag(copyLesson.lessonClrAndSources.clr)) {
          copyLesson.lessonClrAndSources.clr = ''
        }
      }

      // 处理 quiz 数据
      if (copyLesson.questions && copyLesson.questions.length !== 0) {
        copyLesson.questions = copyLesson.questions
          .filter(question => (question.question && question.question.trim() !== '') || (question.answer && question.answer.trim() !== ''))
          .sort((a, b) => a.sortIndex - b.sortIndex)
        copyLesson.questions.forEach((question, index) => {
          question.sortIndex = index + 1
        })
      }

      // 处理实施步骤资源
      if (copyLesson.lessonImpStepAndSource) {
        this.impStepSourceHandle(copyLesson)
      }

      // 构造 step 对象（unitPlanner 中 lesson 没有 steps 数组，需要构造一个）
      const step = {
        ageGroupName: copyLesson.ageGroup,
        ageGroupValue: copyLesson.ageGroup,
        content: copyLesson.implementationSteps,
        universalDesignForLearning: copyLesson.universalDesignForLearning,
        universalDesignForLearningGroup: copyLesson.universalDesignForLearningGroup,
        culturallyResponsiveInstruction: copyLesson.culturallyResponsiveInstruction,
        culturallyResponsiveInstructionGeneral: copyLesson.culturallyResponsiveInstructionGeneral,
        culturallyResponsiveInstructionGroup: copyLesson.culturallyResponsiveInstructionGroup,
        homeActivity: copyLesson.homeActivity,
        mediaId: null, // unitPlanner 中步骤没有独立的媒体
        externalMediaUrlId: null,
        lessonStepGuides: copyLesson.typicalBehaviors || [],
        teachingTips: copyLesson.teachingTips || [],
        lessonClrAndSources: copyLesson.lessonClrAndSources,
        lessonImpStepAndSource: copyLesson.lessonImpStepAndSource,
        questions: copyLesson.questions || [],
        mixedAgeDifferentiations: copyLesson.mixedAgeDifferentiations,
        lessonScienceOfReadingModel: copyLesson.lessonScienceOfReadingModel,
        showMixedAge: copyLesson.showMixedAge || false,
        lessonTemplate: copyLesson.lessonTemplate
      }

      let lesson = {
        id: copyLesson.id,
        name: copyLesson.title || copyLesson.name,
        isAdaptedLesson: copyLesson.adaptedLesson || false,
        ages: [copyLesson.ageGroup], // unitPlanner 中通常只有一个年龄组
        ageGroupNames: [copyLesson.ageGroup],
        themeIds: (copyLesson.themes || []).map(item => item.id),
        prepareTime: extractTimeNumber(copyLesson.prepareTime), // 提取数字部分
        activityTime: extractTimeNumber(copyLesson.activityTime), // 提取数字部分
        activityType: copyLesson.activityType,
        activityTheme: copyLesson.activityTheme,
        frameworkId: this.unit.baseInfo.frameworkId,
        measureIds: measureIds,
        objectives: copyLesson.objectives ? [copyLesson.objectives] : [],
        materials: materials,
        steps: [step], // 构造单个 step 放入数组
        coverMediaIds: copyLesson.cover ? [copyLesson.cover.id] : [],
        coverExternalMediaUrlId: copyLesson.cover && copyLesson.cover.externalMediaUrlId,
        books: copyLesson.book ? [copyLesson.book] : [],
        videoBooks: copyLesson.videoBook ? [copyLesson.videoBook] : [],
        attachmentMediaIds: (copyLesson.attachments || []).map(media => media.id),
        dlls: (copyLesson.dlls || [])
          .filter(dll => {
            return dll.content && dll.content.trim() || dll.media
          })
          .map(dll => ({
            id: dll.id,
            title: dll.title,
            description: dll.description,
            content: dll.content,
            mediaIds: dll.media ? [dll.media.id] : [],
            languages: (dll.languages || []).map(item => ({ langCode: item.langCode, content: item.content }))
          })),
        recommendToAgency: false, // unitPlanner 中通常不需要推荐功能
        assistantAdaptInfo: copyLesson.assistantAdaptInfo,
        classroomType: copyLesson.classroomType
      }

      // 处理家庭活动（根据年龄组判断）
      let agesTip = ['0', '1', '2', '3', '4', '5', '1,2', 'Grade 1', 'Grade 2']
      let isLowGrade = agesTip.includes(copyLesson.ageGroup)

      if (!isLowGrade) {
        // 高年级数据
        let ageGroupValue = ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12']
        if (ageGroupValue.includes(copyLesson.ageGroup)) {
          lesson.steps[0].homeActivity = ''
        }
      }

      return lesson
    },

    /**
     * 处理实施步骤资源（需要添加这个辅助方法）
     */
    impStepSourceHandle (lessonData) {
      // 如果没有实施步骤资源，直接返回
      if (!lessonData.lessonImpStepAndSource || !lessonData.lessonImpStepAndSource.sources) {
        return
      }

      // 处理实施步骤资源的逻辑
      lessonData.lessonImpStepAndSource.sources.forEach(source => {
        if (source.hidden) {
          // 处理隐藏的资源
          source.hidden = null
        }
      })
    },

    /**
     * 移除 HTML 标签并判断是否为空（需要添加这个辅助方法）
     */
    removeHtmlTag (htmlString) {
      if (!htmlString) return true

      // 创建一个临时的 DOM 元素来解析 HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString

      // 获取纯文本内容
      const textContent = tempDiv.textContent || tempDiv.innerText || ''

      // 判断去除空白字符后是否为空
      return textContent.trim() === ''
    },

    /**
     * 填充 UDL 和 CLR 数据 - 适用于 unitPlanner 组件
     */
    fillUDLAndCLRData () {
      // 保存 CLR 内容
      if (this.$refs.culturallyLinguisticallyResponsive) {
        const clrComponent = this.$refs.culturallyLinguisticallyResponsive

        // 获取 CLR 组件中的值
        const culturallyResponsiveInstruction = clrComponent.getGeneralCLRValue
        // 获取 CLR 组件中的解析过的原始课程数据
        const culturallyResponsiveInstructionGeneral = clrComponent.culturallyResponsiveInstructionGeneral || clrComponent.generalValue
        const culturallyResponsiveInstructionGroup = clrComponent.getClassSpecificCLRValueWithChildren

        // 直接更新 lesson 对象（unitPlanner 中没有 steps 数组结构）
        if (culturallyResponsiveInstruction !== undefined) {
          this.$set(this.lesson, 'culturallyResponsiveInstruction', culturallyResponsiveInstruction)
        }
        if (culturallyResponsiveInstructionGroup !== undefined) {
          this.$set(this.lesson, 'culturallyResponsiveInstructionGroup', culturallyResponsiveInstructionGroup)
        }
        // 如果 CLR 的通用值不存在，或者为空时，将其设置为原始值
        if (!this.lesson.culturallyResponsiveInstructionGeneral || this.lesson.culturallyResponsiveInstructionGeneral === '') {
          this.$set(this.lesson, 'culturallyResponsiveInstructionGeneral', culturallyResponsiveInstructionGeneral)
        }
      }
    },

    // 资源保存成功 - 更新内容并关闭加载状态
    saveResourcesSuccess(content) {
      // 如果接收到了新的实施步骤内容，则更新
      if (content) {
        this.$set(this.lesson, 'implementationSteps', content);
      }
      // 关闭加载状态
      this.implementationStepsLoading = false;
    },
  }
}
</script>
<style>
  .tooltip-wrapper {
    max-width: 40vw;
  }
</style>
<style lang="less" scoped>

/deep/ .udl-table .el-form-item{
    margin-bottom: 0;
}

.regenerate {
    width: 32px!important;
    height: 32px!important;
    display: flex;
    align-content: center;
    justify-content: center;
    align-items: center;
}

.gap-8 {
  gap: 8px;
}

.lesson-tag {
  display: flex;
  align-items: center;
  padding: 0px 16px;
  width: fit-content;
  height: 32px;
  background: #FEF5E9;
  border: 1px solid #F49628;
  border-radius: 20px;
  color: #F49628;
  white-space: nowrap;
}

.lesson-measure {
  display: inline-flex;
  min-height: 32px;
  line-height: 12px;
  &> :first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    color: #FFFFFF;
    background: #EA8985;
    padding: 5px 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: normal;
  }
  &> :last-child {
    color: #FD8238;
    background: #FFEEE5;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 4px 5px;
    display: flex;
    flex-wrap: wrap;
    line-height: 22px;
    justify-content: flex-start;
    align-items: center;
  }
}
.lesson-field-time{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  align-items: stretch;
  .lesson-field-time-item {
    align-items: stretch;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    .lesson-field-value {
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }
    .el-select{
      width: 95%;
    }
  }
}

.child-group-list {
  flex-wrap: wrap;
}

.point {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.eld-color {
  color: #002EFE;
}

.iep-color {
  color: #F49628;
}

.eld-bg-color {
  background-color: #002EFE;
}

.iep-bg-color {
  background-color: #F49628;
}

.line-height-21 {
  line-height: 21px;
}

.child-tag {
  border-radius: 90px;
  background: #FFF;
  padding: 4px 10px;
}

.table-child-tag {
    border-radius: 90px;
    background-color: #f5f6f8;
    padding: 4px 10px;
}

::v-deep {
  .update-lesson-measure-select {
    .el-select-dropdown {
      width: 560px;
    }
  }
}

// 封面
.cover-container {
  width: 370px;
}

.lg-icon-picture {
  font-size: 100px;
  height: 50px;
  margin-top: 20px;
}

// 封面操作
/deep/ .cover-operation {
  position: absolute;
  left: 10px;
  top: 10px;
  z-index: 2;

  /deep/ button {
    opacity: 0.6;
  }
}

.child-list {
  height: calc(100vh - 330px);
}

.udl-clr-card {
  background: #E8F9FA;
  /deep/ .el-card__body {
    padding: 0;
    background: #FFF;
  }
}

.typical-behaviors-card {
  background: #F2F8FD;
  /deep/ .el-card__body {
    padding: 0;
    background: #FFF;
  }
}

.formative-quiz-card {
  background: #f0f8fa;
  /deep/ .el-card__body {
    padding: 0;
    background: #FFF;
  }
}

.home-actiovities-card {
  background: #F5FAF5;
  /deep/ .el-card__body {
    padding: 0;
    background: #FFF;
  }
}
.bg-ieps {
  // background: var(--color-warning);
  background: #FFF2E1;
}

.bg-elds {
  background: var(--color-page-background-white);
}

.border-ieps {
  tr,
  th,
  td {
    border: 1px solid #f49628;
  }
}

.border-elds {
  tr,
  th,
  td {
    border: 1px solid var(--color-border);
  }
}

.learners-table {
  table {
    border-collapse: collapse;
    width: 100%;
  }

  .activity-description {
    padding: 12px;
    color: var(--color-text-secondary);
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
    /deep/ .el-textarea__inner {
        border: 1px dashed transparent;
        border-radius: 4px;
        padding: 8px;
    }

    /deep/ .el-textarea__inner:focus,
    /deep/ .el-textarea__inner:hover {
        border: 1px dashed var(--color-info);
        border-radius: 4px;
        padding: 8px;
    }
}
.for-learners-with-IEPs {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: center;
    border-radius: 4px;
    width: 100%;
    margin-bottom: 20px;

    .learners-table {
        table {
            border-collapse: collapse;
            width: 100%;
        }

        .activity-description {
            display: flex;
            padding: 12px;
            word-break: keep-all;
            justify-content: center;
            align-items: center;
            align-self: center;
            color: var(--color-text-secondary);
            text-align: center;
            font-feature-settings: 'clig' off, 'liga' off;

            /* Semi Bold/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 150% */
        }

        .content-description {
            display: flex;
            width: 100%;
            justify-content: space-between;
            align-items: flex-start;
            flex: 1 0 0;
            color: var(--color-text-primary);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Regular/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }

        .child-content {
            display: flex;
            padding: 16px 20px;
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
            flex: 1 0 0;

            .child-description {
                display: flex;
                width: fit-content;
                align-items: center;
                align-self: center;
                background-color: var(--color-page-background-white);
                gap: 8px;
                padding: 4px 8px;
                height: 32px;
                border-radius: 90px;
                flex: 1 0 0;
            }
        }
    }
}

.for-english-language-learners {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: center;
    width: 100%;
    border-radius: 4px;

    .learners-table {
        table {
            border-collapse: collapse;
            width: 100%;
        }

        td {
            border: 1px solid var(--color-border);
        }

        .activity-description {
            display: flex;
            padding: 12px;
            word-break: keep-all;
            justify-content: center;
            align-items: center;
            align-self: center;
            color: var(--color-text-secondary);
            text-align: center;
            font-feature-settings: 'clig' off, 'liga' off;

            /* Semi Bold/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 150% */
        }

        .content-description {
            display: flex;
            width: 100%;
            justify-content: space-between;
            align-items: flex-start;
            flex: 1 0 0;
            color: var(--color-text-primary);
            font-feature-settings: 'clig' off, 'liga' off;

            /* Regular/16px */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }

        .child-content {
            display: flex;
            padding: 16px 20px;
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
            flex: 1 0 0;

            .child-description {
                display: flex;
                width: fit-content;
                align-items: center;
                align-self: center;
                background-color: var(--color-page-background-white);
                gap: 8px;
                padding: 4px 8px;
                height: 32px;
                border-radius: 90px;
                flex: 1 0 0;
            }
        }
    }
}
.domain-table {
    border-radius: 4px;
    border: 1px solid #f49628;
    width: 100%;
    border-collapse: collapse;
    color: #323338;

    tr,
    th,
    td {
        border: 1px solid #f49628;
    }

    th {
        height: 40px;
    }
}
.update-standards-and-regenerate-dialog {
  /deep/ & > .el-dialog__body {
    padding: 14px 20px;
  }
}
.clr-style {
  /deep/ .ql-toolbar {
    display: none!important;;
  }
  /deep/ .ql-toolbar.ql-snow + .ql-container.ql-snow {
    border-top: 1px solid #ccc;
    border-bottom: 0px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
  }
}
.show-clr {
  /deep/ .ql-toolbar {
    display: none!important;;
  }
  /deep/ .ql-toolbar.ql-snow + .ql-container.ql-snow {
    border-top: 1px solid #ccc;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}
.source-content {
  border: 1px solid #ccc;
  border-top: unset;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  min-height: 40px;
  padding: 0 15px 10px;
}
.source-item {
  display: flex;
  flex-direction: column;
  line-height: 20px;
  margin-bottom: 8px;
  border-radius: 8px;
  .source-label {
    width: fit-content;
    display: flex;
    gap: 8px;
  }
}
.clr-content {
  line-height: 24px;
  border: 1px solid #ccc;
  border-bottom: transparent;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  padding: 10px 15px;
}
/deep/ .media-viewer {
  border-radius: 8px!important;
}
.img-size{
  height: 152px;
  width: 128px;
}
.new-tag {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: -5px;
  top: -8px;
}

/deep/ .lesson-quiz-editor {
  .editor .ql-toolbar {
    border-radius: 4px 4px 0 0;
  }
  .editor .ql-container {
    border-radius: 0 0 4px 4px;
    max-height: 500px;
    overflow: auto;
  }
}
.add-question-button {
  width: 100%;
  border-style: dashed;
  border-width: 1px;
}
/deep/ .add-question-button > span {
  color: var(--color-primary);
}
/deep/ .add-question-button > i {
  color: var(--color-primary);
}
.lesson-quiz-window {
  margin-top: 16px;
  border-radius: 4px 4px 0 0;
}

/deep/ .typical-behaviors-tab {
  margin-right: 12px;
  z-index: 0;
  .lg-tabs-mini {
    position: relative!important;
  }
}

@media screen and (max-width: 768px) {
  .display-flex.m-b-sm {
    flex-direction: column;
  }

  .cover-container {
    width: 100% !important;
    margin-bottom: 16px;
  }

  .add-margin-l-24 {
    margin-left: 0 !important;
  }

  .lesson-field-time {
    flex-direction: column;
    .lesson-field-time-item {
      margin-bottom: 16px;
      .el-select {
        width: 100%;
      }
    }
  }

  .lesson-tag {
    margin-bottom: 8px;
  }

  .lesson-measure {
    margin-bottom: 8px;
  }

  /* 材料部分样式 */
  .lesson-material-input {
    display: flex;
    flex-direction: column;
  }

  .lesson-material-editor-textarea-top {
    width: 100% !important;
    margin-bottom: 16px;
  }

  .media-uploader-small {
    width: 100% !important;
  }

  /* 实施步骤资源样式 */
  div[style="display: flex; flex-direction: row;"] {
    flex-direction: column !important;
  }

  div[style*="width: 72%"] {
    width: 100% !important;
  }
}

.adapted-tag {
  color: var(--color-white) !important;
  background: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
  margin-left: 12px;
}

  .portrait-graduate-card {
    background: #EAF3D8;

    /deep/ .el-card__header {
      background: #EAF3D8;
      padding: 16px 20px;

      .title {
        font-size: 20px;
        font-weight: 600;
        color: #4CAF50;
        display: flex;
        align-items: center;
        gap: 8px;

        .new-tag {
          background: var(--f-56-c-6-c, #F56C6C);
          color: #FFFFFF;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 12px;
          font-weight: normal;
          position: relative;
          top: 0;
          right: 0;
        }
      }
    }

    /deep/ .el-card__body {
      padding: 0;
      background: #FFF;
    }

    .portrait-table-tip{
      color: var(--111-c-1-c, #111C1C);
      font-feature-settings: 'liga' off, 'clig' off;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }

    .portrait-table {
      border-collapse: collapse;
      width: 100%;
      table-layout: fixed;

      th, td {
        border: 1px solid #96C13D;
      }

      thead {
        background: #F2F6E8;
        tr {
          th {
            padding: 12px;
            font-weight: 600;
            font-size: 16px;
            line-height: 20px;
            text-align: left;
            color: #323338;
          }
        }
      }

      .portrait-name {
        padding: 12px;
        background: #FFFFFF;
        width: 30%;

        .name {
          color: #323338;
          font-weight: 600;
          font-size: 16px;
          line-height: 20px;
        }

        .description {
          color: #323338;
          font-size: 14px;
          line-height: 20px;
          margin-top: 8px;
        }
      }

      .portrait-evidence {
        padding: 12px;
        background: #FFFFFF;
        width: 70%;

        /deep/ .el-textarea__inner {
          border: 1px dashed transparent;
          border-radius: 4px;
          padding: 8px;
          font-size: 14px;
          line-height: 22px;
          font-family: Inter;
          color: var(--111-c-1-c, #111C1C);
          font-feature-settings: 'liga' off, 'clig' off;
          resize: none;
          &:focus,
          &:hover {
            border: 1px dashed var(--color-info);
          }
        }
      }
    }
  }

  .udl-component {
    border-radius: 0 !important;
    border: 0 !important;

    /deep/ .el-card__header {
      display: none !important;
    }

    /deep/ .feedback-group {
      display: none !important;
    }

    /deep/ .el-card__body {
      border-radius: 0 !important;
      border: 0 !important;
    }

    /deep/ .class-specific-instructions {
      padding: 16px 10px 0 10px !important;
    }

  }

.unit-planner-clr {
  /deep/ .edit-body-border {
    border: none !important;
  }
}

/* Objectives 样式选择器样式 */
.objective-style-selector {
  background: #ffffff;
}

.objective-style-tip {
  color: var(--111-c-1-c, #111C1C);
  font-feature-settings: 'liga' off, 'clig' off;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  display: flex;
  align-items: center;
}

.objective-style-options-container {
  display: flex;
  gap: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}

.objective-style-option {
  flex: 1;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
}

.objective-style-radio {
  padding: 6px 4px;
  margin: 0;
  width: 200px;
  display: block;
  
  /deep/ .el-radio__input.is-checked .el-radio__inner {
    border-color: #10b3b7;
    background: #10b3b7;
  }
  
  /deep/ .el-radio__input.is-checked + .el-radio__label {
    color: #10b3b7;
  }
  
  /deep/ .el-radio__label {
    font-weight: 600;
    font-size: 16px;
    color: #323338;
  }
}

.objective-style-label {
  font-weight: 600;
  font-size: 14px;
  color: #10b3b7;
}

.objective-style-content {
  padding: 10px 0px;
}

.objective-preview-input {
  /deep/ .el-textarea__inner {
    color: #323338;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
    border: 1px solid #e9ecef;
    background: #ffffff;
    
    &:focus {
      border-color: rgb(220, 223, 230);
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
    
    &:hover {
      border-color: rgb(220, 223, 230)
    }
  }
}


/* 样式选择弹框样式 */
.style-selection-content {
  padding: 8px 0;
}

.style-selection-tip {
  color: #111C1C;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  margin-top: 0;
}

.style-selection-options {
  display: flex;
  flex-direction: row;
  gap: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}

.style-selection-option {
  flex: 1;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 16px;
  width: 20px;
  
  &:hover {
    border-color: #10b3b7;
    background: #FAFAFA;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.15);
  }
}

@media screen and (max-width: 768px)  {
  .style-selection-option {
    width: 100%;
  }
}

.style-option-title {
  font-weight: 600;
  font-size: 16px;
  color: #10b3b7;
  margin-bottom: 12px;
  text-align: center;
}

.style-option-preview {
  color: #111C1C;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
  font-style: italic;
  min-height: 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/deep/ .no-body-top-padding {
  .el-dialog__body {
    padding: 6px 20px 20px 20px !important;
  }
}


</style>

<style>
.measure-content {
  width: 800px;
}

</style>