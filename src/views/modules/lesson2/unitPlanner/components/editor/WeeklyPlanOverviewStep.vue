<template>
    <div class="weekly-plan-overview-card">
        <el-row class="display-flex" :class="{'mobile-flex-column': isMobile}">
            <!-- 左侧操作 -->
            <el-col :span="8" class="operation-panel lg-scrollbar" v-show="promptDebugging" :class="{'mobile-full-width': isMobile}">
                <!-- Prompt 调试 -->
                <CollapseCard class="m-t-sm" :theme="'no-header'" :collapsible="false">
                    <!-- 标题 -->
                    <template slot="header">
                      <div class="display-flex justify-content-between">
                          <div class="text-success">
                              <span class="font-size-18 font-bold">Prompt Setting</span>
                          </div>
                          <div>
                              <PromptModelSelect />
                          </div>
                      </div>
                    </template>
                    <!-- 内容 -->
                    <PromptVersionEditor
                        ref="promptEditorRef"
                        :scenes="['PLAN_OVERVIEW']"
                        :showUpdatePromptBtn="true"
                        :showGenerateBtn="true"
                        :generateFun="generateWeeklyPlanOverviewStream"
                        @batchGenerateResultsSuccess="batchGenerateResultsSuccess"
                        @updatePromptId="updatePromptId"
                    />
                </CollapseCard>
            </el-col>
            <!-- 右侧内容 -->
            <el-col :span="promptDebugging ? 16 : 24" class="w-full" :class="[{ 'm-l-md': promptDebugging && !isMobile }, isMobile ? 'mobile-full-width' : '']">
                <!-- 切换按钮 -->
                <div class="lg-tabs-small m-t-sm"
                     v-show="promptDebugging && promptId && $refs.promptStatsResultRef && $refs.promptStatsResultRef.usageRecords.length > 0">
                    <el-tabs v-model="currentView">
                        <el-tab-pane :label="$t('loc.unitPlannerStep1WeeklyTheme')" name="RESULT"></el-tab-pane>
                        <el-tab-pane label="Statistics Validation" name="STATS_RESULT"></el-tab-pane>
                    </el-tabs>
                </div>
                <!-- 提示信息 -->
                <div class="display-flex justify-content-between lg-margin-bottom-16" :class="{'mobile-flex-column': isMobile}" v-if="!lightAdapt">
                    <div class="display-flex justify-content-between align-items w-full" :class="{'mobile-flex-column': isMobile}">
                      <div style="color: var(--color-success)" :class="{'mobile-margin': isMobile}">
                        <i class="lg-icon lg-icon-notification-off font-size-16 font-bold"></i>
                        <span class="font-size-16 lg-margin-left-8 font-bold">{{$t('loc.unitPlannerStep2PleaseReview')}}</span>
                      </div>
                      <div class="display-flex align-items" style="gap: 12px" :class="{'mobile-flex-column': isMobile, 'mobile-margin': isMobile}">
                        <!--feedback 的按钮所在-->
                        <!--反馈-->
                        <FeedbackForm :defaultFeedbackResult="defaultFeedbackResult"
                                      :feedbackStyle="setFeedbackStyle"
                                      :showFeedback="showFeedback"
                                      :showIcon="true"
                                      :showClose="false"
                                      :feedbackTitle="feedbackTitle"
                                      :feedbackSubmit="feedbackSubmit"
                                      :needFeedbackLevel="false"
                                      :feedbackInputPlaceholder="feedbackInputPlaceholder"
                                      :promptUsageRecordIds="promptUsageRecordIds"
                                      :showReminders="false"
                                      @clickFeedback="clickFeedback"
                                      />
                        <!-- 增加 preview 按钮 -->
                        <div :class="{'mobile-full-width': isMobile, 'mobile-margin': isMobile}">
                          <el-button class="m-t-xs" type="primary" plain size="medium" :disabled="generateWeeklyPlanOverviewLoading || singleGenerateLoading" @click="openPlannerPreviewDialog" :class="{'mobile-full-width': isMobile}">
                            <template #icon>
                              <i class="el-icon el-icon-view font-bold"></i>
                            </template>
                            {{ $t('loc.unitOverview') }}
                          </el-button>
                        </div>
                        <div :class="{'mobile-full-width': isMobile}">
                          <el-button class="m-t-xs" type="primary" plain size="medium" v-if="!isCurriculumPlugin"
                                     :loading="generateWeeklyPlanOverviewLoading" @click="regenerateWeeklyThemeAndOverview" :class="{'mobile-full-width': isMobile}">
                            <template #icon>
                              <i class="el-icon el-icon-refresh-right font-bold"></i>
                            </template>
                            {{$t('loc.unitPlannerStep2RegenerateAll')}}
                          </el-button>
                        </div>
                      </div>
                    </div>
                </div>
                <WeeklyPlanOverviewDetail
                    ref="weeklyPlanOverviewDetailRef"
                    v-show="currentView === 'RESULT'"
                    :weeklyPlans="weeklyPlans"
                    :domainsTreeData="domainsTreeData"
                    :allMeasures="allMeasures"
                    :rubricsOptions="rubricsOptions"
                    :editable="true"
                    :baseInfo="baseInfo"
                    :loading="generateWeeklyPlanOverviewLoading"
                    :saveAndGenerateLoading="saveAndGenerateLoading"
                    @regenerate="regenerateWeeklyPlanOverview"></WeeklyPlanOverviewDetail>
                <!-- 统计列表 -->
                <el-card v-show="currentView === 'STATS_RESULT' && promptDebugging">
                    <PromptStatsResult
                        ref="promptStatsResultRef"
                        :promptId="promptId"
                        @testCompleted="testCompleted"
                        @testIncomplete="testIncomplete"
                    />
                </el-card>
                <!-- 操作 -->
                <div class="mt-2 display-flex  bottom-btn-group" style="gap: 10px" :class="{'justify-content-between': !lightAdapt, 'justify-content': lightAdapt}">
                        <!-- 返回 -->
                        <el-button  v-if="!lightAdapt" class="" @click="back()" :disabled="saveAndGenerateLoading">{{$t('loc.unitPlannerStep2Back')}}</el-button>
                        <div>
                        <!-- 保存 -->
                        <el-button type="default" class="" @click="save()" :disabled="saveAndGenerateLoading">{{$t('loc.unitPlannerStep1Save')}}</el-button>
                        <!-- 下一步 -->
                        <el-button v-if="!lightAdapt"  type="primary" class="" @click="confirmAndNext()" :disabled="loading || saveAndGenerateLoading" >{{ confirmTitle }}</el-button>
                        <el-button v-if="lightAdapt" type="primary" class="" @click="openPlannerPreviewDialog">Preview</el-button>
                        </div>
                        <div></div>
                </div>
            </el-col>
        </el-row>
        <!-- 引入UnitOverview 组件 -->
        <div class="unit-planner-over-view-dialog">
          <el-dialog :visible="plannerPreviewDialogShow"
                          v-if="plannerPreviewDialogShow"
                          width="80%"
                          custom-class="loading-class"
                          :before-close="closePlannerPreviewDialog" :title="'Preview'">
            <!-- 仅展示核心测评点 switch end -->
            <UnitDetailOverview :unitId="this.unitId"
                                :allWeeklyMeasureIdsUpdatesCompleted="allWeeklyMeasureIdsUpdatesCompleted"
                                ref="unit-plan-overview"/>
            <!-- 操作 -->
            <span slot="footer" class="dialog-footer" style="text-align: right;">
              <el-button @click="closePlannerPreviewDialog">{{$t('loc.close')}}</el-button>
            </span>
          </el-dialog>
        </div>
    </div>
</template>

<script>
import { aiApiUrl, serverlessApiUrl } from '@/utils/setBaseUrl'
import { mapState } from 'vuex'
import { bus } from '@/utils/bus'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import WeeklyPlanOverviewDetail from './WeeklyPlanOverviewDetail.vue'
import PromptVersionEditor from '@/views/curriculum/prompt/components/editor/PromptVersionEditor.vue'
import PromptModelSelect from '@/views/curriculum/prompt/components/editor/PromptModelSelect.vue'
import CollapseCard from '@/views/curriculum/components/card/CollapseCard.vue'
import PromptStatsResult from '@/views/curriculum/prompt/components/editor/PromptStatsResult.vue'
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import Lessons2 from '@/api/lessons2'
import UnitDetailOverview from '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetailOverview.vue'
import LessonApi from '@/api/lessons2'
import tools from '@/utils/tools'
import {equalsIgnoreCase} from '@/utils/common'

export default {
    components: {
        FeedbackForm,
        WeeklyPlanOverviewDetail,
        PromptVersionEditor, // Prompt 编辑器
        PromptModelSelect, // Prompt 模型选择
        CollapseCard, // 折叠卡片
        PromptStatsResult, // Prompt 统计结果
        UnitDetailOverview
    },
    data () {
        return {
            weeklyPlanOverviewRules: {}, // 周计划概览信息校验规则
            generateWeeklyPlanOverviewLoading: false, // 生成周计划概览 Loading
            updateLoading: false, // 更新周计划概览 Loading
            retryCount: 0, // 重试次数
            weeklyPlansData: '', // 周计划数据
            currentView: 'RESULT', // 显示视图
            promptId: null, // Prompt ID
            promptUsageRecordIds: [], // 消息回调的 promptUsageRecordId，后续用于反馈
            defaultFeedbackResult: {
              feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
              feedBackResult: '',
              feedbackData: {
                feedback: undefined,
                feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
                feedbackLevel: [0, 0, 0, 0]
              }
            },
          leavePage: false, // 是否离开页面
          singleGenerateLoading: false, // 用于控制单个生成时是否显示下方操作按钮
          plannerPreviewDialogShow: false, // 单元预览页面显示条件
          domainsTreeData: [], // 测评点数据
          rubrics: [], // 校训数据
          needSysCloseDialog: false, // 是否需要系统主动关闭弹窗
          allWeeklyMeasureIdsUpdatesCompleted: true, // 所有周计划测评点更新完成
          isMobile: false, // 是否为移动端
        }
    },
    created () {
        // 初始化单元基本信息表单验证规则
        this.initUnitInfoFormRules()
        this.initPlanOverviewData()
        bus.$on('plan_overview:generateParams', (callback) => {
            callback({
                unitId: this.unitId
            })
        })
        // 检测设备类型
        this.checkDeviceType()
        // 监听窗口大小变化
        window.addEventListener('resize', this.checkDeviceType)
    },
    mounted () {
      // 进入第二步编辑步骤曝光事件埋点
      this.$analytics.sendEvent('web_unit_create2_exposure')
      if (this.frameworkId) {
        this.getMeasuresByFrameworkId(this.frameworkId)
      }
    },
    destroyed () {
      // 离开页面
      if (this.unitId) {
        this.leavePage = true
      }
      // 移除窗口大小变化监听
      window.removeEventListener('resize', this.checkDeviceType)
    },
    computed: {
        ...mapState({
            unitId: state => state.curriculum.unit.id,
            baseInfo: state => state.curriculum.unit.baseInfo,
            frameworkId: state => state.curriculum.unit.baseInfo.frameworkId,
            unitOverview: state => state.curriculum.unit.overview,
            gptSetting: state => state.curriculum.gptSetting,
            weekCount: state => state.curriculum.unit.baseInfo.weekCount,
            promptDebugging: state => state.curriculum.promptDebugging, // Prompt 调试
            unitInfo: state => state.curriculum.unit, // 单元基本信息
            isOldUnitData: state => state.curriculum.isOldUnitData, // 单元是否为旧数据
            batchTasks: state => state.unit.batchTasks, // 批量生成任务
            isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
            unitPlanDragItemCount: state => state.curriculum.unitPlanDragItemCount
        }),
        // 获取所有周计划的主题和概览信息
        weeklyPlans: {
            get () {
                return this.$store.state.curriculum.unit.weeklyPlans
            },
            set (value) {
                this.$store.commit('curriculum/SET_WEEKLY_PLANS', value)
            }
        },
        rubricsOptions () {
          return this.unitInfo.baseInfo.newRubrics
        },
        confirmTitle () {
          if (!this.baseInfo.progress || this.baseInfo.progress <= 40) {
            return this.$t('loc.unitPlannerStep2ConfirmNext')
          } else {
            return this.$t('loc.next')
          }
        },
        feedbackTitle () {
            return this.$t('loc.unitPlannerFeedback')
        },
        feedbackInputPlaceholder () {
            return this.$t('loc.unitPlannerFeedbackPlaceholder')
        },
        feedbackSubmit () {
            return this.$t('loc.unitPlannerFeedbackSubmit')
        },
        showFeedback () {
            // 判断 promptUsageRecordIds 中的每一个值，即对应的子数组都是存在值的
            return !this.loading && this.promptUsageRecordIds.length > 0
        },
        // 是否处于 loading 状态
        loading () {
            return this.generateWeeklyPlanOverviewLoading || this.updateLoading
        },
        // 当前框架下所有底层测评点
        allMeasures () {
          let measures = []
          this.getMeasuresBottom(this.domainsTreeData, measures)
          return measures
        },
        // 保存或生成中的 Loading
        saveAndGenerateLoading () {
          return this.loading || this.updateLoading || this.generateWeeklyPlanOverviewLoading || this.singleGenerateLoading
        },
        // 是否是轻量改编
        lightAdapt() {
          return equalsIgnoreCase(this.unitInfo.adaptedType, 'LIGHT')
        }
    },
    watch: {
        // 监听周计划列表变化
        unitId: {
            handler (val) {
                // 初始化周计划概览
                if (val) {
                    this.initPlanOverviewData()
                }
            }
        },
      // 监听周计划列表变化并获取测评点数据
      frameworkId: {
        handler (val) {
          // 初始化周计划概览
          if (val && this.domainsTreeData && this.domainsTreeData.length === 0) {
            this.getMeasuresByFrameworkId(val)
          }
        }
      },
      // 测评点数据变化构造详细测评点数据
      allMeasures: {
        handler (val) {
          if (val && this.domainsTreeData && this.domainsTreeData.length === 0) {
            this.getMeasuresByFrameworkId(val)
          }
          this.handlerWeekPlanStandards()
        }
      },

      unitPlanDragItemCount: {
        handler (val, oldVal) {
          if (val !== null) {
            if (!this.needSysCloseDialog) {
              return
            }
            if (val === 0) {
              this.needSysCloseDialog = false
              // 值等于0时主动关闭弹窗
              this.closePlannerPreviewDialog()
            }
          }
        },
        deep: true
      },
      '$route': {
          deep: true,
          immediate: true,
          handler() {
            // 获取当前路由名称
            if (this.$route) {
              const routeName = this.$route.name
              if (equalsIgnoreCase(routeName, 'edit-weekly-plan-overview-cg-adapt') && this.unitInfo && equalsIgnoreCase(this.unitInfo.adaptedType, 'LIGHT')) {
                this.lightAdapt = true
              }
            }
          }
      }
    },
    methods: {
      // 手动打开 loading
      openDialog () {
        // 获取弹窗内容区域的 DOM 对象
        const targetElement = this.$el.querySelector('.loading-class')
        let options = {
          text: 'Loading',
          target: targetElement
        }
        this.loadingInstance = this.$loading(options)
      },
        // 获取已经选择的测评点
        getSelectedMeasures (measureAbbs) {
          let measures = []
          if (measureAbbs) {
            // 筛选出选择的测评点
            let selectedMeasure = this.allMeasures
            if (!this.isOldUnitData && this.baseInfo.measureIds && this.baseInfo.measureIds.length > 0) {
              selectedMeasure = this.allMeasures.filter(measure => this.baseInfo.measureIds.includes(measure.id))
            }
            measureAbbs.forEach(measureAbb => {
              const measure = selectedMeasure.find(measure => measure.mappingAbbr === measureAbb || measure.abbreviation === measureAbb)
              if (measure) {
                measures.push(measure)
              }
            })
          }
          measures.sort((a, b) => a.sortIndex - b.sortIndex)
          return measures
        },

        // 获取底层测评点
        getMeasuresBottom (children, measuresBottom) {
          if (!children || children.length === 0) {
            return
          }
          children.forEach(v => {
            const childrenNext = v.children
            if (!childrenNext || childrenNext.length === 0) {
              measuresBottom.push(v)
            } else {
              this.getMeasuresBottom(childrenNext, measuresBottom)
            }
          })
        },

        // 获取框架下所有测评点信息
        async getMeasuresByFrameworkId (frameworkId) {
          // 使用 Vuex 中的 getFrameworkDomains 方法获取测评点数据
          try {
            const measures = await this.$store.dispatch('curriculum/getFrameworkDomains', {
              frameworkId: frameworkId,
              compress: false
            })
            this.domainsTreeData = measures
          } catch (error) {
            // 请求失败处理
            console.error(error)
          }
        },
        // 是否是旧数据 （Weekly Theme 添加测评点之前）
        checkOldUnitData (weeklyPlans) {
          if (!weeklyPlans || weeklyPlans.length === 0) return false

          const firstPlan = weeklyPlans[0]
          const hasOverview = firstPlan.overview && firstPlan.overview.trim() !== ''
          const noMeasureIds = firstPlan.measureIds && !firstPlan.measureIds.length

          // 如果有 overview 但是没测评点数据判定为旧数据
          return hasOverview && noMeasureIds
        },

        // 处理测评点数据，根据测评点 id 匹配测评点详细信息
        handlerWeekPlanStandards () {
          this.weeklyPlans.forEach((weeklyPlan) => {
            if (weeklyPlan.measureIds && weeklyPlan.measureIds.length > 0) {
              weeklyPlan.standards = this.allMeasures
                .filter(measure => weeklyPlan.measureIds.includes(measure.id))
                .sort((a, b) => a.sortIndex - b.sortIndex)
            }
          })
        },
        // 如果当前进度小于 40% 则先保存周计划测评点
        async saveWeeklyPlanStandards () {
          if (this.baseInfo.progress > 40) {
            return
          }
          this.allWeeklyMeasureIdsUpdatesCompleted = false

          // 收集所有的 measureIds
          const updates = this.weeklyPlans.map(weeklyPlan => {
            if (weeklyPlan.measureIds && weeklyPlan.measureIds.length > 0) {
              return {
                planId: weeklyPlan.id,
                measureIds: weeklyPlan.measureIds
              }
            }
            return null
          }).filter(Boolean) // 过滤掉 null 值

          if (updates.length > 0) {
            try {
              await LessonApi.updateWeeklyMeasureIds({ upWeeklyMeasureRequestList: updates })
            } catch (error) {
              // 处理错误
              console.error(error)
            }
          }
          // 最后允许继续处理
          this.allWeeklyMeasureIdsUpdatesCompleted = true
        },

      // 显示单元预览
        openPlannerPreviewDialog () {
          this.$analytics.sendEvent('cg_adapt_weekly_preview_clicked')
          this.plannerPreviewDialogShow = true
          // 设置全局 unitOverviewPreviewDrag 为 true
          this.$store.commit('curriculum/SET_UNIT_OVERVIEW_PREVIEW_DRAG', true)
          this.saveWeeklyPlanStandards()
        },
        // 关闭单元预览 -- step2 向 step3 时默认会重新查 item
        closePlannerPreviewDialog () {
          // 如果未处理完的 item 时，打开 loading
          if (this.unitPlanDragItemCount && this.unitPlanDragItemCount !== 0) {
            this.openDialog()
            this.needSysCloseDialog = true
            return
          }
          // 关闭 loading
          if (this.loadingInstance) {
            this.loadingInstance.close()
          }

          this.plannerPreviewDialogShow = false
          // 设置全局 unitOverviewPreviewDrag 为 false
          this.$store.commit('curriculum/SET_UNIT_OVERVIEW_PREVIEW_DRAG', false)
        },
        // 设置 feedback 的样式
        setFeedbackStyle () {
            return {
                zIndex: '2000'
            }
        },
        // 反馈按钮点击事件
        clickFeedback (isUP) {
          if (isUP) {
            this.$analytics.sendEvent('web_unit_create2_feedback_pos')
          } else {
            this.$analytics.sendEvent('web_unit_create2_feedback_neg')
          }
        },
        // 初始化周计划概览数据
        initPlanOverviewData () {
            // 单元内容未生成则跳过
            if (!this.unitId) {
                return
            }
            // 定义变量，用来存储周计划列表中是否存在只有一个属性或者存在空值的周计划
            let isOnlyHasOneAttributeOrExistEmpty = false
            this.weeklyPlans.forEach(weeklyPlan => {
              // 如果 weeklyPlan 对象中只包含 1 个字段，或者周计划的主题和概览存在空值时，则认为没有全部生成完成就离开页面
              if (Object.keys(weeklyPlan).length === 1 || !weeklyPlan.theme || weeklyPlan.theme.trim() === '' || !weeklyPlan.overview || weeklyPlan.overview.trim() === '') {
                isOnlyHasOneAttributeOrExistEmpty = true
              }
            })
            // 如果单元基础信息存在并且周计划为空，则获取周计划概览
            if (this.unitInfo.baseInfo && (!this.weeklyPlans || this.weeklyPlans.length === 0 || isOnlyHasOneAttributeOrExistEmpty)) {
              let params = {
                params: {
                  unitId: this.unitId
                }
              }
              // 调用接口获取周计划概览与主题数据
              this.$axios.get($api.urls().getPlanThemeAndOverview, params).then(res => {
                let weeklyPlans = res.weeklyPlans
                weeklyPlans.forEach(plan => {
                  plan.rubrics = plan.newRubrics
                })
                // 如果已经生成，则直接更新 Vuex 中的周计划数据, 否则生成周计划概览
                if (res.weeklyPlans && res.weeklyPlans.length > 0) {
                  this.$store.commit('curriculum/SET_WEEKLY_PLANS', weeklyPlans)
                  // 把周计划概览存入 sessionStorage
                  let overviews = this.weeklyPlans.map(x => ({ theme: x.theme, overview: x.overview, books: x.books }))
                  sessionStorage.setItem('PLAN_OVERVIEWS', JSON.stringify(overviews))
                  this.handlerWeekPlanStandards()
                  this.$store.commit('curriculum/SET_IS_OLD_UNIT',this.checkOldUnitData(weeklyPlans))
                } else {
                  // 生成周计划概览
                  this.generateWeeklyPlanOverviewStream(null, true)
                }
              })
            }
        },

        // 同步生成周计划概览
        generateWeeklyPlanOverview () {
            this.generateWeeklyPlanOverviewLoading = true
            let param = {
                baseInfo: this.baseInfo,
                unitOverview: this.unitOverview,
                gptSetting: this.gptSetting
            }
            this.$axios
                .post(aiApiUrl + '?action=generate_weekly_plan_overview', param).then((res) => {
                    this.$store.commit('curriculum/SET_WEEKLY_PLANS', res.result)
                    this.createWeekPlans()
                    this.generateWeeklyPlanOverviewLoading = false
                })
                .catch((error) => {
                    console.log(error)
                    let errorMsg = error.response.data.error
                    // 限制重试次数
                    if (errorMsg && (errorMsg.includes('Extra data:') || errorMsg.includes('Expecting ')) && this.retryCount < 1) {
                        this.retryCount = this.retryCount + 1
                        this.generateWeeklyPlanOverview()
                        return
                    }
                    this.generateWeeklyPlanOverviewLoading = false
                    this.$message.error(error.response.data.error_message)
                })
        },

        // 异步生成周计划概览
      async generateWeeklyPlanOverviewStream (callback, generate) {
          this.generateWeeklyPlanOverviewLoading = true
          // 清理 promptUsageRecordIds
          this.$set(this, 'promptUsageRecordIds', [])
          // 重置数据
          this.weeklyPlansData = ''
          // 根据数量生成周计划空对象
          let emptyPlans = []
          for (let i = 0; i < this.weekCount; i++) {
            let plan = {}
            if (this.weeklyPlans && this.weeklyPlans.length > i) {
              plan = this.weeklyPlans[i]
              plan.theme = null
              plan.overview = null
              plan.standards = null
              plan.books = null
              plan.rubrics = null
            } else {
              plan.week = i + 1
            }
            emptyPlans.push(plan)
          }
          this.$store.commit('curriculum/SET_WEEKLY_PLANS', emptyPlans)
          // 参数
          let params = {
            unitId: this.unitId
          }
          // 消息回调
          let messageCallback = (message) => {
            // 如果退出页面，则不再运行消息回调
            if (this.leavePage) {
              return
            }
            // 更新数据
            this.weeklyPlansData += message.data
            // 解析数据
            let parseKeys = [
              { key: 'week', name: 'Week' },
                { key: 'theme', name: 'Weekly Theme' },
                { key: 'overview', name: 'Weekly Overview' },
                { key: 'standards', name: 'Measures/Standards' },
                { key: 'books', name: 'Books' }
            ]
            if (this.baseInfo.newRubrics && this.baseInfo.newRubrics.length > 0) {
              parseKeys.push({ key: 'rubrics', name: ['Assigned Graduate Portrait Attributes', 'Learner Profile Focus'] })
            }
            // 当前 Unit 仅有一周的情况下，解析数据的 name 为 Weekly Overview
            let parsedData = parseStreamData(this.weeklyPlansData, parseKeys, this.weekCount)
            // 转换为新的周计划数组
            let newPlans = []
            for (let i = 0; i < this.weekCount; i++) {
              let plan = parsedData.length > i ? parsedData[i] : {}
              // 设置周信息
              plan.week = i + 1
              // 原有数据
              if (this.weeklyPlans && this.weeklyPlans.length > i) {
                plan.id = this.weeklyPlans[i].id
                // plan.items = this.weeklyPlans[i].items
                plan.activityCount = this.weeklyPlans[i].activityCount
              }
              if (plan.overview) {
                // 截断 '```' 之后的内容
                plan.overview = plan.overview.split('```')[0]
              }
              if (plan.books) {
                plan.books = plan.books.replace('None', '')
              }
              // 解析测评点数据
              if (plan.standards && plan.standards.trim() !== '') {
                const splitSign = plan.standards.includes(';') ? ';' : ','
                let measureAbbs = plan.standards.split(splitSign).map(item => item.trim())
                plan.standards = Object.values(this.getSelectedMeasures(measureAbbs))
              } else {
                plan.standards = []
              }
              // 解析校训数据
              if (plan.rubrics && plan.rubrics.trim() !== '') {
                // 解析并验证校训数据
                const validRubrics = tools.parseAndValidateRubrics(plan.rubrics, this.rubricsOptions);
                this.$set(plan, 'rubrics', validRubrics);
              } else {
                this.$set(plan, 'rubrics', []);
              }
              newPlans.push(plan)
              // 增加周计划课程默认值
              // plan.activityCount = 5
            }
            newPlans.forEach(plan => {
              this.$set(plan, 'theme', this.formatWeeklyTheme(plan.theme))
            })
            // 更新数据
            this.$store.commit('curriculum/SET_WEEKLY_PLANS', newPlans)
          }
          // 生成单元概览
        try {
          const res = await createEventSource($api.urls().generatePlanOverviewStream, params, messageCallback)

          if (this.leavePage) return

          this.weeklyPlans.forEach(weeklyPlan => {
            if (!weeklyPlan.theme || !weeklyPlan.overview) {
              this.$set(weeklyPlan, 'showDangerBorder', true)
            } else {
              this.$set(weeklyPlan, 'showDangerBorder', false)
            }
          })

          if (res && res.promptUsageRecordId) {
            this.promptUsageRecordIds.push(res.promptUsageRecordId)
          }
          // 数据保存或更新
          await this.createWeekPlans(true, generate)
          // 检查是否有未包全情况
          await this.checkMeasures()

          this.generateWeeklyPlanOverviewLoading = false
          if (callback && typeof callback === 'function') {
            callback()
          }

          this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
        } catch (error) {
          this.generateWeeklyPlanOverviewLoading = false
          this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          if (callback && typeof callback === 'function') {
            callback()
          }
        }
      },

      // 检查测评点是否包全
      async checkMeasures () {
        // 找出未包全测评点的单元
        // 单元包含的测评点
        let unitAllMeasures = this.unitInfo.baseInfo.measureIds
        // 单元周计划包含的测评点
        let unitMeasures = []
        this.weeklyPlans.forEach(weeklyPlan => {
          weeklyPlan.standards.forEach(measure => {
            unitMeasures.push(measure.id)
          })
        })
        // 未包全的测评点
        let diffMeasures = unitAllMeasures.filter(measure => !unitMeasures.includes(measure))
        // 如果存在未包全的测评点,则再次给周计划分配测评点
        if (diffMeasures.length === 0) {
          return Promise.resolve()
        }
        // 找出未包全的测评点的缩写
        let diffMeasureAbbrs = this.allMeasures.filter(measure => diffMeasures.includes(measure.id)).flatMap(measure => measure.abbreviation)
        // console.log(this.unitInfo + ' 存在未包全的测评点: ', diffMeasureAbbrs)

        let params = {
          unitId: this.unitId
        }
        await this.$axios.get(serverlessApiUrl + $api.urls().getAutoAdaptWeekPlanMeasures, { params: params }).then(res => {
          if (!res || !res.unitId || !res.weekPlans || res.weekPlans.length === 0) {
            return
          }
          // week map 构建
          const resWeekMap = res.weekPlans.reduce((acc, item) => {
            acc[item.planId] = item.measureIds
            return acc
          }, {})

          // 遍历单元下的周计划
          this.weeklyPlans.forEach((currentWeek, index) => {
            const resMeasureIds = resWeekMap[currentWeek.id.toUpperCase()]
            if (!resMeasureIds) {
              return
            }
            // 合并测评点和领域
            let measures = this.allMeasures.filter(x => resMeasureIds.includes(x.id))
            let measureInfos = [...new Set([...currentWeek.standards, ...measures])]
              .sort((a, b) => a.sortIndex - b.sortIndex)
            // 更新周计划的测评点和领域
            this.$set(currentWeek, 'standards', measureInfos)
          })
          // 更新周计划
          this.$store.commit('curriculum/SET_WEEKLY_PLANS', this.weeklyPlans)
          this.updateWeekPlans(false, false)
        }).catch(error => {
          console.error(error)
        })
      },

        // 重新生成全部
        regenerateWeeklyThemeAndOverview () {
          this.$confirm(this.$t('loc.unitPlannerStep1RegenerateConfirmation'), this.$t('loc.confirmation'), {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel')
          }).then(() => {
            // 旧数据重新生成后重置为新数据，展示测评点
            this.$store.commit('curriculum/SET_IS_OLD_UNIT',false)
            this.stopGenerateLessonTask()
            // 再次生成全部埋点
            this.$analytics.sendEvent('web_unit_create2_regenerate_all')
            this.generateWeeklyPlanOverviewStream(null, true)
          }).catch(() => {
            // 取消
          })
        },
        // 清除生成任务
        clearGenerateTask () {
          // 清除 vuex 中的任务列表
          this.$store.dispatch('unit/setBatchTasks', null)
          this.$store.dispatch('unit/setBatchId', '')
          this.$store.dispatch('unit/setAppearNotice', false)
        },
        // 停止执行生成课程任务
        stopGenerateLessonTask () {
          // 如果不存在任务列表，则直接返回
          if (!this.batchTasks) {
            return
          }
          // 如果是单个重新生成，则只需要删除当前课程的生成任务即可
          const pendingTasks = this.batchTasks.pendingTasks
          const processingTasks = this.batchTasks.processingTasks
          const failedTasks = this.batchTasks.failedTasks
          const completedTasks = this.batchTasks.completedTasks
          // 合并所有任务
          const allTasks = [...pendingTasks, ...processingTasks, ...failedTasks, ...completedTasks]
          const taskIds = allTasks.map(x => x.taskId).join(',')
          // 如果存在任务，则停止任务
          if (taskIds) {
            this.clearGenerateTask()
            Lessons2.stopGenerateTasks(taskIds)
          }
        },

        // 批量生成结果成功
        batchGenerateResultsSuccess (result) {
            // 更新测试记录 ID
            this.testId = result.testRecordId
            // 获取测试结果
            this.$refs.promptStatsResultRef.getTestResults(true)
            // 切换到测试列表页面
            this.currentView = 'STATS_RESULT'
        },

        // 测试完成
        testCompleted () {
            // 获取测试结果
            this.$refs.promptEditorRef.testCompleted()
        },

        // 测试未完成
        testIncomplete () {
            // 获取测试结果
            this.$refs.promptEditorRef.testIncomplete()
        },

        updatePromptId (promptId) {
            this.promptId = promptId
        },

        // 保存周计划概览
        save () {
          if (this.lightAdapt) {
            // 点击保存按钮埋点
          this.$analytics.sendEvent('cg_adapt_weekly_save_clicked')
          }
          // 点击保存按钮埋点
          this.$analytics.sendEvent('web_unit_create2_save')
          this.createWeekPlans()
        },

        // 去除 theme 前后的 ""
        formatWeeklyTheme (theme) {
          let reg = /^\"(.*)\"$/g
          let match = reg.exec(theme)
          let title = theme
          if (match) {
            title = match[1]
          }
          return title
        },

        // 保存周计划
      async createWeekPlans (clearPlanItems, generate = false) {
            // 如果已存在 ID，则更新数据
            if (this.weeklyPlans && this.weeklyPlans.length > 0 && this.weeklyPlans[0].id) {
              await this.updateWeekPlans(false, clearPlanItems, true, generate)
              return
            }
            this.updateLoading = true
            // 如果仅一周，将 unit 的 rubrics 信息更新到周计划中
            // if (this.baseInfo.rubrics && this.weekCount === 1) {
            //   this.weeklyPlans[0].rubrics = this.baseInfo.rubrics
            // }
            // 构造测评点 id 数据
            this.weeklyPlans.forEach(weeklyPlan => {
              if (weeklyPlan.standards) {
                weeklyPlan.measureIds = weeklyPlan.standards.map(item => item.id)
              }
              // 如果是单元导入深度改编设置原有课程数
              if (equalsIgnoreCase(this.unitInfo.adaptedType, 'DEEP') && !weeklyPlan.activityCount && this.unitInfo.weekLessonCountMap && this.unitInfo.weekLessonCountMap[weeklyPlan.week]) {
                weeklyPlan.activityCount = this.unitInfo.weekLessonCountMap[weeklyPlan.week]
              } else if (!weeklyPlan.activityCount) {
                weeklyPlan.activityCount = 5
              }
              if (!weeklyPlan.rubrics) {
                this.$set(weeklyPlan, 'newRubrics', [])
              } else {
                this.$set(weeklyPlan, 'newRubrics', weeklyPlan.rubrics)
              }
            })
          // 更新数据
          this.$store.commit('curriculum/SET_WEEKLY_PLANS', this.weeklyPlans)
          let weekCopy = JSON.parse(JSON.stringify(this.weeklyPlans))
          weekCopy.forEach(v => {
            v.rubrics = null;
          });

            // 参数
            let params = {
                plans: weekCopy,
                unitId: this.unitId,
                progress: 'WEEK_OVERVIEW_GENERATED'
            }
            // 请求接口
        await this.$axios.post($api.urls().createWeeklyPlans, params).then((res) => {
                let planIds = res.planIds
                // 确定完毕之后设置进度为 40
                this.baseInfo.progress = 40
                // 验证数量是否匹配
                if (planIds.length !== this.weeklyPlans.length) {
                    this.$message.error(this.$t('loc.unitPlannerStep3SavedUnsuccessfully'))
                    this.updateLoading = false
                    return
                }
                // 更新周计划 ID
                for (let i = 0; i < this.weeklyPlans.length; i++) {
                    this.$set(this.weeklyPlans[i], 'id', planIds[i])
                }
                this.updateLoading = false
                // 更新周计划成功事件
                this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
            }).catch(error => {
                this.updateLoading = false
                this.$message.error(error.response.data.error_message)
            })
        },

        // 更新周计划
      async updateWeekPlans (next, clearPlanItems, saveTip, generate) {
            // 如果仅一周，将 unit 的 rubrics 信息更新到周计划中
            // if (this.baseInfo.rubrics && this.weekCount === 1) {
            //   this.weeklyPlans[0].rubrics = this.baseInfo.rubrics
            // }
            // 是否有未生成的周计划，如果有则设置 isHasNoDataWeeklyPlan 为 true
            let isHasNoDataWeeklyPlan = false
            let week
            this.weeklyPlans.forEach((weeklyPlan, index) => {
              if (this.$refs.weeklyPlanOverviewDetailRef) {
                this.$refs.weeklyPlanOverviewDetailRef.validWeeklyThemeAndOverview(weeklyPlan, index)
              }
              if (this.isOldUnitData) {
                // 如果是旧数据不校验测评点数据
                if (!weeklyPlan.theme || weeklyPlan.theme === '' || !weeklyPlan.overview || weeklyPlan.overview === '') {
                  isHasNoDataWeeklyPlan = true
                  this.$set(weeklyPlan, 'showDangerBorder', true)
                  week = weeklyPlan.week
                }
              } else {
                if (!weeklyPlan.theme || weeklyPlan.theme === '' || !weeklyPlan.overview || weeklyPlan.overview === '' || !weeklyPlan.standards || weeklyPlan.standards.length === 0) {
                  isHasNoDataWeeklyPlan = true
                  this.$set(weeklyPlan, 'showDangerBorder', true)
                  week = weeklyPlan.week
                }
              }
              if (weeklyPlan.standards && weeklyPlan.standards.length > 0) {
                weeklyPlan.measureIds = weeklyPlan.standards.map(item => item.id)
              }
              if (!weeklyPlan.activityCount) {
                weeklyPlan.activityCount = 5
              }
              // 确保 rubrics 数据存在
              if (!weeklyPlan.rubrics) {
                this.$set(weeklyPlan, 'newRubrics', [])
              } else {
                this.$set(weeklyPlan, 'newRubrics', weeklyPlan.rubrics)
              }
            })
          // 更新数据
          this.$store.commit('curriculum/SET_WEEKLY_PLANS', this.weeklyPlans)
            // 判断 isHasNoDataWeeklyPlan 是否为 true，如果为 true 则提示用户第几周的周计划未生成
            if (isHasNoDataWeeklyPlan) {
              return
            }
            this.updateLoading = true

        let weekCopy = JSON.parse(JSON.stringify(this.weeklyPlans))
        weekCopy.forEach(v => {
          v.rubrics = null;
        });
            // 参数
            let params = {
                plans: weekCopy,
                unitId: this.unitId,
                clearPlanItems: clearPlanItems // 是否清空周计划项
            }
            // 更新
        await this.$axios.post($api.urls().updateWeeklyPlans, params).then((res) => {
                if (next) {
                  this.goNextPage()
                  return
                }
                if (saveTip && !generate) {
                  this.$message.success(this.$t('loc.unitPlannerStep3SavedSuccessfully'))
                }
                this.updateLoading = false
                // 更新周计划成功事件
                this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
            }).catch(error => {
                this.updateLoading = false
                console.log(error)
                this.$message.error(error.response.data.error_message)
            })
        },
        // 重新生成周计划主题和概览数据
        regenerateWeeklyPlanOverview (weeklyPlan) {
            this.singleGenerateLoading = true
            // 再次生成单个埋点
            this.$analytics.sendEvent('cg_unit_create_two_regenerate')
            this.$set(weeklyPlan, 'regenerateLoading', true)
            // 清理 promptUsageRecordIds
            this.$set(this, 'promptUsageRecordIds', [])
            // 周转换数字
            let week = parseInt(weeklyPlan.week)
            this.$set(weeklyPlan, 'generateLoading', true)
            weeklyPlan.retryCount = 0
            let param = {
                unitId: this.unitId,
                weekNumber: week
            }
            // 消息回调
            let data = ''
            let messageCallback = (message) => {
                // 如果退出页面，则不再运行消息回调
                if (this.leavePage) {
                  // 单个生成过程中退出当前页面，应该将 loading 效果设置为 false
                  this.$set(weeklyPlan, 'regenerateLoading', false)
                  return
                }
                // 更新数据
                data += message.data
                // 解析数据
                let parseKeys = [
                  { key: 'week', name: 'Week' },
                  { key: 'theme', name: 'Weekly Theme' },
                  { key: 'overview', name: 'Weekly Overview' },
                  { key: 'standards', name: 'Measures/Standards' },
                  { key: 'books', name: 'Books' }
                ]
                if (this.rubricsOptions && this.rubricsOptions.length > 0) {
                  parseKeys.push({ key: 'rubrics', name: ['Assigned Graduate Portrait Attributes', 'Learner Profile Focus'] })
                }
                // 当前 Unit 仅有一周的情况下，解析数据的 name 为 Weekly Overview
                let parsedData = parseStreamData(data, parseKeys, this.weekCount)
                if (parsedData.length > 1) {
                    // 转换为新的周计划数组
                    for (let i = 0; i < this.weekCount; i++) {
                        let plan = parsedData.length > i ? parsedData[i] : {}
                        if (plan.overview) {
                          // 截断 '```' 之后的内容
                          plan.overview = plan.overview.split('```')[0]
                        }
                        if (plan.books) {
                          plan.books = plan.books.replace('None', '')
                        }
                        // 解析校训数据
                        if (plan.rubrics && plan.rubrics.trim() !== '') {
                          // 解析并验证校训数据
                          const validRubrics = tools.parseAndValidateRubrics(plan.rubrics, this.rubricsOptions);
                          this.$set(plan, 'rubrics', validRubrics);
                        } else {
                          this.$set(plan, 'rubrics', []);
                        }
                        // 解析测评点数据
                        if (plan.standards && plan.standards.trim() !== '' && !this.isOldUnitData) {
                          const splitSign = plan.standards.includes(';') ? ';' : ','
                          let measureAbbs = plan.standards.split(splitSign).map(item => item.trim())
                          plan.standards = Object.values(this.getSelectedMeasures(measureAbbs))
                        } else {
                          plan.standards = []
                        }
                        // 设置周信息
                        if (plan.week) {
                            if (weeklyPlan.week == plan.week.trim()) {
                              if (this.lightAdapt) {
                                // 若果是轻量改编，则只更新主题和概览
                                this.$set(weeklyPlan, 'theme', plan.theme)
                                this.$set(weeklyPlan, 'overview', plan.overview)
                                this.$set(weeklyPlan, 'deleted', false)
                                this.$set(weeklyPlan, 'generateLoading', false)
                              } else {
                                this.$set(weeklyPlan, 'theme', plan.theme)
                                this.$set(weeklyPlan, 'overview', plan.overview)
                                this.$set(weeklyPlan, 'books', plan.books)
                                this.$set(weeklyPlan, 'standards', plan.standards)
                                this.$set(weeklyPlan, 'rubrics', plan.rubrics)
                                this.$set(weeklyPlan, 'deleted', false)
                                this.$set(weeklyPlan, 'generateLoading', false)
                              }
                            }
                        }
                    }
                } else {
                    if (parsedData[0].overview) {
                      // 截断 '```' 之后的内容
                      parsedData[0].overview = parsedData[0].overview.split('```')[0]
                    }
                    if (parsedData[0].books) {
                      parsedData[0].books = parsedData[0].books.replace('None', '')
                    }
                    // 解析校训数据
                    if (parsedData[0].rubrics && parsedData[0].rubrics.trim() !== '') {
                      // 解析并验证校训数据
                      const validRubrics = tools.parseAndValidateRubrics(parsedData[0].rubrics, this.rubricsOptions);
                      this.$set(parsedData[0], 'rubrics', validRubrics);
                    } else {
                      this.$set(parsedData[0], 'rubrics', []);
                    }
                    // 解析测评点数据
                    if (parsedData[0].standards && parsedData[0].standards.trim() !== '' && !this.isOldUnitData) {
                      const splitSign = parsedData[0].standards.includes(';') ? ';' : ','
                      let measureAbbs = parsedData[0].standards.split(splitSign).map(item => item.trim())
                      parsedData[0].standards = Object.values(this.getSelectedMeasures(measureAbbs))
                    } else {
                      parsedData[0].standards = []
                    }
                    if (this.lightAdapt) {
                      // 若果是轻量改编，则只更新主题和概览
                      this.$set(weeklyPlan, 'theme', parsedData[0].theme)
                      this.$set(weeklyPlan, 'overview', parsedData[0].overview)
                      this.$set(weeklyPlan, 'deleted', false)
                      this.$set(weeklyPlan, 'generateLoading', false)
                    } else {
                      this.$set(weeklyPlan, 'theme', parsedData[0].theme)
                      this.$set(weeklyPlan, 'overview', parsedData[0].overview)
                      this.$set(weeklyPlan, 'books', parsedData[0].books)
                      this.$set(weeklyPlan, 'standards', parsedData[0].standards)
                      this.$set(weeklyPlan, 'rubrics', parsedData[0].rubrics)
                      this.$set(weeklyPlan, 'deleted', false)
                      this.$set(weeklyPlan, 'generateLoading', false)
                    }
                }
                this.weeklyPlans.forEach(plan => {
                  this.$set(plan, 'theme', this.formatWeeklyTheme(plan.theme))
                })
                this.$store.commit('curriculum/SET_WEEKLY_PLANS', this.weeklyPlans)
            }
            createEventSource($api.urls().generateSinglePlanOverviewStream, param, messageCallback)
                .then((res) => {
                    // 离开页面不进行更新操作
                    if (this.leavePage) {
                      return
                    }
                    // 生成结束之后，重新判断是否存在未生成的周计划
                    this.weeklyPlans.forEach(weeklyPlan => {
                      if (!weeklyPlan.theme || weeklyPlan.theme === '' || !weeklyPlan.overview || weeklyPlan.overview === '') {
                        this.$set(weeklyPlan, 'showDangerBorder', true)
                      } else {
                        this.$set(weeklyPlan, 'showDangerBorder', false)
                      }
                    })
                    // 记录 promptUsageRecordId
                    if (res && res.promptUsageRecordId !== '') {
                        this.promptUsageRecordIds.push(res.promptUsageRecordId)
                    }
                    this.updateWeekPlans(false, false)
                    this.$set(weeklyPlan, 'regenerateLoading', false)
                    this.singleGenerateLoading = false
                })
                .catch(error => {
                    console.log(error)
                    let errorMsg = error.response.data.error
                    // 限制重试次数
                    if (errorMsg && (errorMsg.includes('Extra data:') || errorMsg.includes('Expecting ')) && weeklyPlan.retryCount < 1) {
                        weeklyPlan.retryCount = weeklyPlan.retryCount + 1
                        this.regenerateWeeklyPlanOverview()
                        return
                    }
                    this.$set(weeklyPlan, 'generateLoading', false)
                    this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                })
        },

        // 初始化单元基本信息表单验证规则
        initUnitInfoFormRules () {
            this.unitInfoRules = {
                name: [
                    { required: true, message: 'Please input name!', trigger: 'blur' },
                    { min: 1, max: 1000, message: 'Length should be 1 to 1000', trigger: 'blur' }
                ],
                grade: [
                    { required: true, message: 'Please input grade!', trigger: 'blur' },
                    { min: 1, max: 1000, message: 'Length should be 1 to 1000', trigger: 'blur' }
                ]
            }
        },

        // 返回
        back() {
          let routerName = ''
          if (this.unitInfo.adaptedType) {
            routerName = 'unit-overview-cg-adapt'
          } else if (this.$store.state.curriculum.isCG || this.isCurriculumPlugin) {
            routerName = 'unit-overview-cg'
          } else {
            routerName = 'unitOverview'
          }
          // 点击返回按钮埋点
          this.$analytics.sendEvent('web_unit_create2_back')
          this.$router.push({
            name: routerName,
            params: {
              unitId: this.unitId
            }
          })
        },
        // 确认并下一步
        confirmAndNext () {
          // 点击下一步按钮埋点
          this.$analytics.sendEvent('web_unit_create2_next')
          // 是否有未生成的周计划，如果有则设置 isHasNoDataWeeklyPlan 为 true
          let isHasNoDataWeeklyPlan = false
          let week
          this.weeklyPlans.forEach(weeklyPlan => {
            if (!weeklyPlan.theme || weeklyPlan.theme.trim() === '' || !weeklyPlan.overview || weeklyPlan.overview.trim() === '') {
              isHasNoDataWeeklyPlan = true
              this.$set(weeklyPlan, 'showDangerBorder', true)
              week = weeklyPlan.week
            }
          })
          // 判断 isHasNoDataWeeklyPlan 是否为 true，如果为 true 则提示用户第几周的周计划未生成
          if (isHasNoDataWeeklyPlan) {
            this.$message.error(this.$t('loc.unitPlannerStep1HaventGeneratedTheme', { week: week }))
            return
          }
          if (!this.baseInfo.progress || this.baseInfo.progress <= 40) {
            // 确认周计划
            this.weeklyPlans.forEach(weeklyPlan => {
              this.$set(weeklyPlan, 'confirmed', true)
            })
            this.$store.commit('curriculum/SET_BAE_INFO', this.baseInfo)
            this.updateWeekPlans(true)
          } else {
            this.updateWeekPlans(true)
          }
        },

        // 下一步
        next () {
            // 未完成的
            let uncompletedWeeklyPlans = this.weeklyPlans.filter(weeklyPlan => weeklyPlan.deleted)
            if (uncompletedWeeklyPlans.length > 0) {
                let uncompletedWeeks = uncompletedWeeklyPlans.map(weeklyPlan => 'Week ' + weeklyPlan.week).join(', ')
                let errorMsg = this.$t('loc.unitPlannerStep1HaventGeneratedTheme', { week: uncompletedWeeks })
                this.$message.error(errorMsg)
                return
            }
            // 未确认的
            let unconfirmedWeeklyPlans = this.weeklyPlans.filter(weeklyPlan => !weeklyPlan.confirmed)
            if (unconfirmedWeeklyPlans.length > 0) {
                this.$confirm(this.$t('loc.unitPlannerStep1ConfirmAll'), 'Confirmation', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel'
                }).then(() => {
                    // 确认剩余周计划
                    unconfirmedWeeklyPlans.forEach(weeklyPlan => {
                        this.$set(weeklyPlan, 'confirmed', true)
                    })
                    this.updateWeekPlans(true)
                }).catch(() => {})
                return
            }
            // 更新周计划信息
            this.updateWeekPlans(true)
        },
        // 继续下一步
        goNextPage () {
          this.checkAndExtractBooksFromUnitPlanOverviews()
          .then(() => {
            let routerName = ''
            if (this.unitInfo.adaptedType) {
              routerName = 'lesson-overview-cg-adapt'
            } else if (this.$store.state.curriculum.isCG || this.isCurriculumPlugin) {
              routerName = 'lesson-overview-cg'
            } else {
              routerName = 'lessonOverview'
            }
            this.$router.push({
                name: routerName,
                params: {
                    unitId: this.unitId,
                    week: 1
                }
            })
          }).catch(error => {
            this.$message.error(error.response.data.error_message)
          }).finally(() => {
            this.updateLoading = false
          })
        },

        // 提取单元计划概览中的书籍信息
        checkAndExtractBooksFromUnitPlanOverviews () {
          return new Promise((resolve, reject) => {
            let overviews = JSON.stringify(this.weeklyPlans.map(x => ({ theme: x.theme, overview: x.overview, books: x.books })))
            let originalOverviews = sessionStorage.getItem('PLAN_OVERVIEWS')
            // 如果进度为 40 或者后面又修改了周计划概览，则提取书籍信息
            if (this.baseInfo.progress == 40 || overviews !== originalOverviews) {
              this.$axios.get(serverlessApiUrl + $api.urls().extractBooksFromUnitPlanOverviews, {
                params: {
                  unitId: this.unitId
                }
              }).then(() => {
                sessionStorage.setItem('PLAN_OVERVIEWS', overviews)
                resolve()
              })
              .catch(error => {
                reject(error)
              })
            } else {
              resolve()
            }
          })
        },

        // 检测设备类型
        checkDeviceType() {
          this.isMobile = window.innerWidth <= 768
        }
    }
}
</script>

<style lang="less" scoped>
@media screen and (max-width: 768px) {
  .bottom-btn-group {
    > div > .el-button:last-child {
      margin-top: 10px !important;
      margin-left: 0 !important;
    }
  }
}

.operation-panel {
  margin-right: -10px;
  height: 100% !important;
}

.bottom-btn-group {
  position: sticky;
  bottom: 0;
  padding: 10px 0;
  background: var(--color-page-background-white);
}

.weekly-plan-overview-card {
    min-height: 80vh;
}
/deep/ .el-textarea__inner {
  resize: none;
}
::v-deep {
    .el-form-item__label {
        margin-bottom: 0;
        line-height: 22px;
        font-weight: 600;
    }
    .el-form-item__error {
        padding-left: 0;
    }
}
.unit-planner-over-view-dialog {
  /deep/ .el-dialog {
    margin: 0!important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 95%;
    display: flex;
    flex-direction: column;
  }
  /deep/ .el-dialog__body {
    overflow-y: auto;
    color: #606266;
    word-break: break-all;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 24px;
    font-size: 14px;
  }

  /deep/ .el-dialog__title {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #323338;
  }

  /deep/ .el-dialog__header {
    text-align: left;
    padding: 24px 24px 0;
  }
}
</style>
