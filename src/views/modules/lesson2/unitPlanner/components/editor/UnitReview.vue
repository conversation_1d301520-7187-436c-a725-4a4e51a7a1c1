<template>
    <div class="display-flex flex-auto unit-review-container flex-direction-col position-relative">
        <!-- 返回 -->
        <div class="display-flex justify-content">
          <div class="display-flex align-items lg-color-primary position-absolute lg-pointer" @click="back()" style="left: 0px" v-if="unit.adaptedType !== 'LIGHT'">
            <!-- <img src="~@/assets/img/curriculumGenie/back.svg" class="lg-pointer back-style" alt="back"> -->
            <i class="lg-icon lg-icon-back font-size-20"></i>
            <span class="lg-margin-left-8 font-size-16">{{$t('loc.unitPlannerLessonIdeas')}}</span>
          </div>
          <!-- 标题 -->
          <div class="display-flex justify-content font-bold font-size-18 text-default">Lesson Plan Details</div>
            <div class="position-absolute batch-generate lesson-detail-header-btn-group">
              <!--反馈  -->
              <FeedbackForm :defaultFeedbackResult="defaultFeedbackResult"
                            :showFeedback="showFeedback"
                            styleClass="left"
                            :showIcon="true"
                            :showClose="false"
                            :feedbackTitle="feedbackTitle"
                            :feedbackSubmit="feedbackSubmit"
                            :needFeedbackLevel="false"
                            :feedbackInputPlaceholder="feedbackInputPlaceholder"
                            :promptUsageRecordIds="promptUsageRecordIds"
                            :showReminders="false"
                            @submitFeedback="submitFeedback"
                            @clickFeedback="clickFeedback"/>
              <!-- 历史按钮 -->
              <el-button
                v-if="lesson && lesson.id && !loading"
                style="padding: 3px 6px!important;"
                class="add-margin-l-10"
                type="primary"
                size="medium"
                @click="openHistory"
                plain>
                <div class="btn-center">
                  <i style="font-size: 16px;" class="lg-icon lg-icon-history lg-color-primary"></i>
                  <span>{{ $t('loc.lessonVersionHistory') }}</span>
                </div>
              </el-button>
              <el-button
                type="primary"
                v-show="!showPromptCustomDetail && !adaptedUnitAllGenerated && unit.adaptedType !== 'LIGHT'"
                class="ai-btn add-margin-l-10"
                style="height: 36px;"
                :disabled="allGenerated || batchGenerateLessonLoading || generateLessonDetailLoading || loading || updateLoading"
                @click="batchGenerateLessons"
              >
                <template #icon>
                  <i class="lg-icon lg-icon-generate"></i>
                </template>
                Batch Generate
              </el-button>
            </div>
        </div>
        <div class="display-flex flex-auto unit-review-container add-margin-t-10">
          <!-- 左侧菜单 -->
          <div class="operation-panel lg-scrollbar operation-panel-normal" :style="promptDebugging ? 'width: 33%;' : ''">
            <!-- Prompt 调试 -->
            <CollapseCard class="m-b-sm" :theme="'no-header'" v-show="promptDebugging" :collapse="true">
                <!-- 标题 -->
                <template slot="header">
                    <div class="text-success">
                        <span class="font-size-18 font-bold">Prompt Setting</span>
                    </div>
                </template>
                <!-- 内容 -->
                <PromptVersionEditor
                    ref="promptEditorRef"
                    :scenes="['LESSON_DETAIL', 'TYPICAL_BEHAVIOR', 'UNIVERSAL_DESIGN_FOR_LEARNING', 'CULTURALLY_RESPONSIVE_INSTRUCTION', 'HOME_ACTIVITY']"
                    :showUpdatePromptBtn="true"
                    :showGenerateBtn="true"
                    :generateFun="generateContent"
                    @batchGenerateResultsSuccess="batchGenerateResultsSuccess"
                    @updatePromptId="updatePromptId"
                />
            </CollapseCard>
            <el-card class="menu-card add-margin-b-8" v-show="unit.adaptedType === 'LIGHT'" :class="{'menu-card-light': unit.adaptedType === 'LIGHT'}">
              <div class="light-overview">
                {{ $t('loc.unitOverview') }}
                <div v-if="generateUnitFoundationAndWeeklyThemeLoading">
                  <el-button type="primary" size="small" :loading="true" :disabled="true">{{ $t('loc.generating') }}</el-button>
                </div>
                <div v-else class="display-flex align-items">
                  <el-button type="text" :disabled="generateLessonDetailLoading || loading || updateLoading" @click="checkUnitOverview">
                    <i class="lg-icon lg-icon-edit font-size-20 add-margin-r-16" :class="{'lg-color-disabled': generateLessonDetailLoading || loading || updateLoading}"></i>
                  </el-button>
                  <el-button type="primary" size="small" @click="setPlannerPreviewDialogShow">{{ $t('loc.view') }}</el-button>
                </div>
              </div>
            </el-card>
            <!-- 课程列表 -->
            <el-card class="menu-card">
              <LessonMenu
                  ref="lessonMenuRef"
                  :unitInfo="unitInfo"
                  :weeklyPlans="weeklyPlans"
                  :disableSwitch="loading"
                  :batchGenerating="batchGenerating"
                  :completeAllLesson="completeAllLesson"
                  :isNext="isNext"
                  :isCenterLesson="isCenterLesson"
                  :currentGenerate="currentGenerate"
                  @changeLesson="changeLesson"
                  @keepOnSingleLessonGenerate="next()"
                  @changeNextLessonStatus="changeNextLessonStatus"
              />
            </el-card>
          </div>
          <!-- 详情 -->
          <div class="w-full m-l-md lesson-detail-container lg-scrollbar-show" v-show="lesson || generateLessonDetailLoading">
            <!-- 切换按钮 -->
            <div class="lg-tabs-small m-b-sm"
                 v-show="promptDebugging && promptId && $refs.promptStatsResultRef && $refs.promptStatsResultRef.usageRecords.length > 0">
              <el-tabs v-model="currentView">
                <el-tab-pane label="Lesson Plan Details" name="RESULT"></el-tab-pane>
                <el-tab-pane label="Statistics Validation" name="STATS_RESULT"></el-tab-pane>
              </el-tabs>
            </div>
            <!-- 统计列表 -->
            <el-card v-show="currentView === 'STATS_RESULT' && promptDebugging">
              <PromptStatsResult
                ref="promptStatsResultRef"
                :promptId="promptId"
                @testCompleted="testCompleted"
                @testIncomplete="testIncomplete"
              />
            </el-card>
            <!-- 单元详情 -->
            <!-- <UnitOverviewDetail v-if="!lesson && !weeklyPlan && currentView === 'RESULT'" :unitOverview="unit.overview" /> -->
            <!-- 周计划详情 -->
            <!-- <WeeklyPlanOverviewDetail v-if="weeklyPlan && currentView === 'RESULT'" :weeklyPlans="unit.weeklyPlans"></WeeklyPlanOverviewDetail> -->
            <PromptCustomDetail v-if="showPromptCustomDetail && currentView === 'RESULT'"
                                :lesson="lesson"
                                :promptId="promptId"
                                ref="promptCustomDetailRef"
                                :content="generateCustomerDetailContent"
                                @updatePromptId="updatePromptId"/>
            <!-- 课程详情 -->
            <LessonDetail v-else-if="lesson" :item="item" :lesson="lesson" :editable="true" ref="lessonDetailRef"
                          v-show="currentView === 'RESULT'"
                          @regenerateLessonQuiz="regenerateLessonQuiz"
                          @regenerate="regenerateLessonDetail"
                          @generateLessonSources="generateLessonSources"
                          @updateClrWithSubscript="updateClrWithSubscript"
                          @regenerateTypicalBehaviors="reGenerateTypicalBehaviorsStream"
                          @regeneratePortraitGraduate="regeneratePortraitGraduate"
                          @regenerateUniversalDesignForLearning="regenerateUniversalDesignForLearningStream"
                          @regenerateCulturallyResponsiveInstruction="regenerateCulturallyResponsiveInstructionStream"
                          @regenerateHomeActivity="reGenerateHomeActivityStream"
                          @generateCover="generateLessonCover"
                          @setCoverInfo="setLessonCoverInfo"
                          @updateLesson="updateLesson"
                          @updateGeneratedLessonSources="updateGeneratedLessonSources"
                          @updateQuestions="updateQuestions"
                          @regenerateQuizQuestion="regenerateQuizQuestion"
                          @generateTeachingTips="generateTeachingTips"
                          @updateLessonTemplate="updateLessonTemplate"
                          @updateGenerateCustomTemplateLoading="updateGenerateCustomTemplateLoading"
                          @updateGenerateLessonSlidesLoading="updateGenerateLessonSlidesLoading"
                          @updateActivityTime="updateActivityTime"
                          @updateUdlLoading="val => generateUniversalDesignForLearningLoading = val"
                          @upNormalClrLoading="upNormalClrLoading"
                          :isUnitPlanner="true"
                          :saveLesson="updateLesson"
                          :generateTypicalBehaviorsLoading="generateTypicalBehaviorsLoading"
                          :generateLessonSourceLoading="generateLessonSourceLoading"
                          :generatePortraitGraduateLoading="generatePortraitGraduateLoading"
                          :generateUniversalDesignForLearningLoading="generateUniversalDesignForLearningLoading"
                          :generateTeachingTipsForStandardLoading="generateTeachingTipsForStandardLoading"
                          :generateCulturallyResponsiveInstructionLoading="generateCulturallyResponsiveInstructionLoading"
                          :generateUniversalDesignForLearningGroupLoading="generateUniversalDesignForLearningGroupLoading"
                          :generateCulturallyResponsiveInstructionGroupLoading="generateCulturallyResponsiveInstructionGroupLoading"
                          :generateHomeActivityLoading="generateHomeActivityLoading"
                          :generateLessonQuizLoading="generateLessonQuizLoading"
                          :updateLessonLoading="updateLoading"
                          :generatedLessonSources="generatedLessonSources"
                          :loading="generateLessonDetailLoading"
                          :mappedDomains="mappedDomains"
                          :isK12Grade="isK12Grade"/>
            <div v-else v-loading="true" style="height:500px;"></div>
            <!--PromptCustomDetail  的操作栏-->
            <div v-show="showPromptCustomDetail && currentView === 'RESULT'" class="display-flex justify-content bg action-container" style="background-color: #f5f6f8">
              <el-button icon="el-icon-refresh-right" type="primary" plain
                         @click="regenerateCustomDetail"
                         :loading="generateCustomerDetail || loading || lesson && lesson.generateLessonCoverLoading">
                {{$t('loc.unitPlannerStep1Regenerate')}}</el-button>
                <!-- 保存 -->
              <el-button type="primary" class="" @click="savePromptCustomDetail"
                         :loading="generateCustomerDetail || loading || updateLoading">
                {{$t('loc.unitPlannerStep1Save')}}</el-button>
            </div>
            <!-- 操作 -->
            <div v-show="!showPromptCustomDetail" class="display-flex justify-content-between bg action-container desktop-actions" style="background-color: #f5f6f8">
              <div>
                <!-- 返回 -->
                <el-button class="" @click="back()" :disabled="loading" v-show="unit.adaptedType !== 'LIGHT'">{{$t('loc.back')}}</el-button>
              </div>
              <div>
                <!-- 保存 -->
                <el-button type="default" class="m-r-sm" @click="saveLesson(true)" :loading="generateLessonDetailLoading || loading || updateLoading">{{$t('loc.unitPlannerStep1Save')}}</el-button>
                <!-- 重新生成按钮 -->
                <el-popover
                  placement="top"
                  width="390"
                  v-model="showRedesignLessonGuide"
                  ref="settingGuide"
                  popper-class="adapt-lesson-popover"
                  trigger="manual">
                  <div class="text-white">
                    <!-- 引导文字 -->
                    <div class="lg-margin-bottom-24 word-break text-left">
                      <!-- 用户引导内容 -->
                      <span class="title-font-14">{{$t('loc.unitPlannerRedesignGuide1')}}</span>
                      <img src="~@/assets/img/lesson2/unitPlanner/redesign_lesson_with_idea_guide.png" class="lg-margin-top-12">
                    </div>
                    <div class="display-flex flex-justify-end gap-6 align-items">
                      <el-button type="text" @click="hideRedignGuide(false)">{{ $t('loc.unitPlannerRedesignGuide2') }}</el-button>
                      <el-button type="primary" @click="hideRedignGuide(true)">{{ $t('loc.unitPlannerRedesignGuide3') }}</el-button>
                    </div>
                  </div>
                  <el-button slot="reference"
                            class="ai-btn"
                            type="primary"
                            :disabled="generateLessonDetailLoading || loading || updateLoading"
                            v-show="!showPromptCustomDetail"
                            @click="openRedesignLessonDialog">
                    <template #icon>
                      <i class="lg-icon lg-icon-generate"></i>
                    </template>
                    {{ $t('loc.enhanceLesson') }}
                  </el-button>
                </el-popover>
                <!-- 批量生成课程按钮 -->
                <el-button
                    v-show="!adaptedUnitAllGenerated"
                    type="primary"
                    class="ai-btn add-margin-l-10"
                    :disabled="allGenerated || batchGenerateLessonLoading || generateLessonDetailLoading || loading || updateLoading"
                    @click="batchGenerateLessons"
                >
                    <template #icon>
                        <i class="lg-icon lg-icon-generate"></i>
                    </template>
                    Batch Generate
                </el-button>
                <!-- 下一个课程 -->
                <el-button type="primary" class="" @click="next()" :disabled="loading || (completeAllLesson && !hasNextUnconfirmedLesson)" v-show="hasNextUnconfirmedLesson && unit.adaptedType !== 'LIGHT'">{{$t('loc.unitPlannerLessonProceedPlan')}}</el-button>
                <!-- 轻量改编预览单元按钮 -->
                <el-button type="primary" class="" @click="jumpToUnitDetail" :disabled="generateLessonDetailLoading || loading || updateLoading" v-show="unit.adaptedType === 'LIGHT'">
                  <div class="display-flex align-items">
                    <div>{{$t('loc.unitPlannerPreviewUnitPlan')}}</div>
                    <i class="el-icon-arrow-right font-size-20"></i>
                  </div>
                </el-button>
                <!-- 进入 Center -->
                <el-button type="primary" class="" @click="toCenterActivityDetail()" :disabled="loading || (completeAllLesson && !hasNextUnconfirmedLesson)" v-show="showProcessToCenter">{{$t('loc.unitPlannerStep3GenerateCenterActivity')}}</el-button>
                <!-- 下一周 -->
                <el-button type="primary" class="" @click="nextWeek()" :disabled="loading" v-show="unconfirmedLessonOverviewWeeklyPlan && !hasNextUnconfirmedLesson && weeklyPlans.length > 1 && unit.adaptedType !== 'LIGHT'">{{$t('loc.unitPlannerStep3ProceedNextWeek')}}</el-button>
                <!-- 完成 -->
                <el-button type="primary" class="" @click="next(true, false, true)" :disabled="loading" v-if="completeAllLesson && !isCurriculumPlugin">{{$t('loc.unitPlannerStep3Publish')}}</el-button>
                <!-- 分享到 magic -->
                <el-button type="primary" v-if="!isCurriculumPlugin" class="ai-btn add-margin-l-10" @click="autoMagic" :disabled="loading || publishBtnDisabled" :style="computedAllowSubmit ? 'opacity: 0.5' : ''" v-show="completeAllLesson && isSharedToMagic">Submit Your Entry</el-button>
              </div>
              <div></div>
            </div>

            <!-- 移动端操作按钮 -->
            <div v-show="!showPromptCustomDetail" class="display-flex justify-content-between bg action-container mobile-actions" style="background-color: #f5f6f8">
              <div class="action-container-row">
                <!-- 返回 -->
                <el-button class="" @click="back()" :disabled="loading">{{$t('loc.back')}}</el-button>
                <!-- 保存 -->
                <el-button type="default" class="m-r-sm" @click="saveLesson(true)" :loading="generateLessonDetailLoading || loading || updateLoading">{{$t('loc.unitPlannerStep1Save')}}</el-button>
                <!-- 重新生成按钮 -->
                <el-button
                  icon="el-icon-refresh-right"
                  type="primary"
                  plain
                  :loading="generateLessonDetailLoading || loading || updateLoading"
                  @click="openRedesignLessonDialog">
                  {{$t('loc.enhanceLesson')}}
                </el-button>
              </div>
              <div class="action-container-row">
                <!-- 批量生成课程按钮 -->
                <el-button
                    type="primary"
                    class="ai-btn"
                    :disabled="allGenerated"
                    @click="batchGenerateLessons"
                    :loading="batchGenerateLessonLoading"
                >
                    <template #icon>
                        <i class="lg-icon lg-icon-generate"></i>
                    </template>
                    Batch Generate
                </el-button>
                <!-- 下一个课程 -->
                <el-button type="primary" class="" @click="next(false, true)" :disabled="loading || (completeAllLesson && !hasNextUnconfirmedLesson)" v-show="hasNextUnconfirmedLesson">{{ nextIsCenter() ? $t('loc.unitPlannerStep3GenerateCenterActivity') : $t('loc.unitPlannerLessonProceedPlan')}}</el-button>
                <!-- 下一周 -->
                <el-button type="primary" class="" @click="nextWeek()" :disabled="loading" v-show="unconfirmedLessonOverviewWeeklyPlan && !hasNextUnconfirmedLesson && weeklyPlans.length > 1">{{$t('loc.unitPlannerStep3ProceedNextWeek')}}</el-button>
                <!-- 完成 -->
                <el-button type="primary" class="" @click="next(true, false, true)" :disabled="loading" v-show="completeAllLesson">{{$t('loc.unitPlannerStep3Publish')}}</el-button>
                <!-- 分享到 magic -->
                <el-button type="primary" v-if="!isCurriculumPlugin" class="ai-btn" @click="autoMagic" :disabled="loading || publishBtnDisabled" :style="computedAllowSubmit ? 'opacity: 0.5' : ''" v-show="completeAllLesson && isSharedToMagic">Submit Your Entry</el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 回退警示弹窗 -->
        <el-dialog
          title="Confirmation"
          custom-class="unit-review-confirmation"
          :visible.sync="leaveVisible"
          width="35%"
          :before-close="handleClose">
          <span style="font-size:16px">Your content is currently being generated. You can exit this page without disrupting the process, and we will notify you when it's ready.</span>
          <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">{{ $t('loc.cancel') }}</el-button>
            <el-button type="danger" @click="confirmExit">Confirm & Exit</el-button>
          </span>
      </el-dialog>
      <!-- 选择测评点弹窗 -->
      <el-dialog
        :close-on-click-modal="false"
        custom-class="update-standards-and-regenerate-dialog"
        :title="$t('loc.enhanceLesson')"
        :visible.sync="regenerateLessonVisible"
        @close="selfCloseRedesignLessonDialog"
        z-index="1000"
        width="600px">
        <el-form
          label-position="top"
          label-width="100px"
          ref="regenerateLessonForm"
          :model="regenerateLesson"
          :rules="dynamicRegenerateLessonRules"
        >
          <!-- 描述信息 -->
          <div class="m-b-sm">
            {{$t('loc.unitPlannerEnhanceDescription')}}
          </div>
          <!-- 测评点 -->
          <el-form-item :label="$t('loc.standardsOrMeasures')" prop="measures">
            <el-select v-model="regenerateLesson.measures" filterable multiple class="w-full update-lesson-measure-select"
                       :multiple-limit="maxMeasureSelections"
                       :placeholder="$t('loc.pleaseSelect')"
                       :loading="loadingFrameworkLoading" :popper-append-to-body="false">
              <el-option-group
                  v-for="domain in domains"
                  :key="domain.id"
                  :label="domain.name">
                <el-option
                    v-for="measure in domain.children"
                    v-show="!measure.iepmeasure"
                    @mousedown.native.prevent="onOptionClick(measure)"
                    :key="measure.id"
                    :label="measure.abbreviation"
                    :value="measure.abbreviation">
                    <span v-if="measure.name && measure.abbreviation !== measure.name && measure.name !== null">{{ measure.abbreviation }}: {{ measure.name }}</span>
                    <span v-else>{{ measure.abbreviation }}: {{ measure.description }}</span>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <!-- 活动持续时间 -->
          <el-form-item :label="$t('loc.lesson2NewLessonFormLabelActivityTime')" prop="activityDuration">
            <el-select v-model="regenerateLesson.activityTime" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                       class="input-select w-full"  clearable>
              <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value"/>
            </el-select>
          </el-form-item>
          <!-- 重新设计的想法 -->
          <el-form-item :label="$t('loc.enhanceIdeas')" prop="redesignIdea">
            <el-input type="textarea" v-model="regenerateLesson.redesignIdea" :rows="4" :placeholder="$t('loc.unitPlannerRedesignIdeaPlaceholder')" :maxlength="2000" show-word-limit></el-input>
          </el-form-item>
        </el-form>
        <!-- 操作 -->
        <span slot="footer" class="dialog-footer">
        <el-button @click="closeRedesignLessonDialog">{{$t('loc.cancel')}}</el-button>
        <el-button type="primary" class="enhance-btn" @click="confirmRedesignLesson()">{{$t('loc.unitPlannerConfirmRegenerate')}}</el-button>
        </span>
      </el-dialog>
      <!-- 发布Magic成功提示弹窗 -->
      <MagicSuccessDialog :unit="editUnit" :visible.sync="magicSuccessVisible" @close="successClose"></MagicSuccessDialog>
      <!-- 单元完成弹窗 -->
      <UnitCompletedDialog v-if="!loading" :unit="() => unit" :visible.sync="showUnitCompletedDialog" @close="closeUnitCompletedDialog"></UnitCompletedDialog>
      <!-- 历史版本 -->
      <LessonHistoryDrawer ref="historyDrawer"
        :lessonId="lesson ? lesson.id : ''" :current-lesson="lesson"
        :visible.sync="showHistoryDrawer"
        @restore="handleRestoreHistory"
        @close="closeHistoryDrawer">
      </LessonHistoryDrawer>
      <!-- 课程完成后的邀请弹框 -->
      <UnitCompletedShared ref="unitCompletedInvitationDialogRef"/>

      <!-- 分享图片生成  -->
      <ImageGenerator class="imageGeneratorEmpty"
                      style="opacity: 0; position: fixed; top: 0; left: 0; z-index: -1; pointer-events: none;"
                      ref="imageGeneratorRef"
                      v-if="unit && unitCoverMediasUrl"
                      :unitDescription="unitDescription"
                      :unitTitle="unitTitle"
                      :unitCoverMediasUrl="unitCoverMediasUrl"
                      :unit="unit"/>
      
      <div class="unit-planner-over-view-dialog">
        <el-dialog :visible="plannerPreviewDialogShow" :width="isMobile ? '95%' : '80%'"
                   :before-close="()=>{plannerPreviewDialogShow = false}" :title="'Preview'" :close-on-click-modal="false"
                   custom-class="loading-class"
                   :close-on-press-escape="false"
                   :fullscreen="isMobile">
          <!-- 仅展示核心测评点 switch end -->
          <UnitDetailOverview :unitId="this.unit.id"
                              :isMobile="isMobile"
                              ref="unit-plan-overview"/>
          <!-- 操作 -->
          <span slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button type="primary" @click="checkUnitOverview" :class="{'mobile-full-width': isMobile}" :disabled="generateLessonDetailLoading || loading || updateLoading">
              <i class="lg-icon lg-icon-edit font-size-16 add-margin-r-4"></i>
              {{ $t('loc.unitPlannerEditUnitOverview') }}</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
</template>

<script>
import tools from '@/utils/tools'
import { mapState } from 'vuex'
import { aiApiUrl, platform, serverlessApiUrl } from '@/utils/setBaseUrl'
import LessonDetail from './LessonDetail.vue'
import WeeklyPlanOverviewDetail from './WeeklyPlanOverviewDetail.vue'
import LessonMenu from './LessonMenu.vue'
import { createEventSource, parseStreamData, removeUnexpectedCharacters } from '@/utils/eventSource'
import Lessons2 from '@/api/lessons2'
import LessonApi from '@/api/lessons2'
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import { equalsIgnoreCase, equalsNotIgnoreCase } from '@/utils/common'
import CollapseCard from '@/views/curriculum/components/card/CollapseCard.vue'
import PromptVersionEditor from '@/views/curriculum/prompt/components/editor/PromptVersionEditor.vue'
import PromptCustomDetail from '@/views/curriculum/prompt/components/editor/PromptCustomDetail.vue'
import PromptStatsResult from '@/views/curriculum/prompt/components/editor/PromptStatsResult.vue'
import { bus } from '@/utils/bus'
import MagicSuccessDialog from '@/views/magicCurriculum/components/btnGroup/MagicSuccessDialog.vue'
import frameworkUtils from '@/utils/frameworkUtils'
import constants from '@/utils/constants'
import LessonUtils from '@/utils/lessonUtils'
import UnitCompletedDialog from '@/views/modules/lesson2/unitPlanner/components/editor/UnitCompletedDialog.vue'
import UnitCompletedShared from '@/views/modules/lesson2/unitPlanner/components/editor/UnitCompletedShared.vue'
import LessonHistoryDrawer from '@/views/modules/lesson2/lessonLibrary/components/LessonHistoryDrawer.vue'
import ImageGenerator from '@/views/modules/lesson2/unitPlanner/ImageGenerator.vue'
import { maxMeasureSelections } from '@/utils/constants'
import UnitDetailOverview from '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetailOverview.vue'


export default {
    components: {
      UnitDetailOverview,
      PromptStatsResult,
      PromptVersionEditor,
      CollapseCard,
      PromptCustomDetail,
      LessonDetail,
      FeedbackForm,
      WeeklyPlanOverviewDetail,
      LessonMenu,
      MagicSuccessDialog,
      UnitCompletedDialog,
      LessonHistoryDrawer,
      UnitCompletedShared,
      ImageGenerator
    },
    data () {
        return {
            loadingFrameworkLoading: false, // 加载框架 Loading
            regenerateLessonVisible: false, // 重新生成课程弹窗
            showRedesignLessonGuide: false, // 显示重新设计课程引导
            regenerateLesson: {}, // 重新生成课程
            domains: [], // 领域
            weeklyPlan: null, // 当前选择的周计划
            item: null, // 当前选择的活动项
            lesson: null, // 当前选择的课程
            updateLoading: false, // 更新 Loading
            generateLessonDetailLoading: false,
            generateTypicalBehaviorsLoading: false,
            generateUniversalDesignForLearningLoading: false,
            generateTeachingTipsForStandardLoading: false,
            generatePortraitGraduateLoading: false,
            generateCulturallyResponsiveInstructionLoading: false,
            generateUniversalDesignForLearningGroupLoading: false,
            generateCulturallyResponsiveInstructionGroupLoading: false,
            generateHomeActivityLoading: false, // 生成家庭活动 Loading
            generateCustomerDetail: false, // 生成自定义详情
            generateCustomerDetailContent: '', // 生成自定义详情内容
            generateLessonQuizLoading: false, // 生成课程测验 Loading
            retryCount: 0, // 重试次数
            hasNextUnconfirmedLesson: false, // 是否有下一个未确认的课程
            allConfirmed: false, // 是否所有课程都已确认
            unconfirmedLessonOverviewWeeklyPlan: null, // 下一个未确认课程概览的周计划
            groupInstructionFormRules: {}, // 分组教学信息表单验证规则
            generateGroupInstructionLoading: false, // 生成分组教学信息 Loading
            completeAll: false, // 是否完成单元所有课程
            sharedMagic: false, // 是否分享到 Magic
            currentView: 'RESULT', // 显示视图
            promptId: null, // Prompt ID
            leavedPage: false, // 是否离开页面
            defaultFeedbackResult: {
                feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
                feedBackResult: '',
                feedbackData: {
                    feedback: undefined,
                    feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
                    feedbackLevel: [0, 0, 0, 0]
                }
            }, // 默认的反馈
          promptUsageRecordIdsMap: new Map(), // promptUsageRecordIds Map
          progressConstant: {
            sixtyPercent: 'WEEK_OVERVIEW_CONFIRMED',
            eightyPercent: 'LESSON_OVERVIEW_GENERATED'
          }, // 进度常量
          leaveVisible: false, // 离开提示框
          isNext: false, // 是否触发的是下一个活动项
          singleGenerate: false, // 是否单个生成课程
          showPromptCustomDetail: false, // 是否显示 Prompt Custom Detail
          isNotCurrentScene: ['UNIT_OVERVIEW', 'PLAN_OVERVIEW', 'LESSON_OVERVIEW', 'DEI_FOR_TEACHER', 'DEI_FOR_ADMINISTRATORS', 'DEI_FOR_ALL'], // 不能处理的事件
          generateLessonSourceLoading: false, // 生成课程来源 Loading
          generatedLessonSources: false, // 是否生成了 CLR 资源
          magicSuccessVisible: false , // 发布到 Magic 成功弹窗
          editUnit: {}, // 编辑的单元
          mappedMeasureMap: {}, // 映射测评点缩写映射
          mappedDomains: [], // 映射测评点
          googleSlideData: null, // Google Slide 数据
          originalLessonTemplate: null, // 原始课程模板
          lessonCopy: {}, // 课程副本
          generateCustomTemplateLoading: false, // 生成自定义模板 Loading
          generateLessonSlidesLoading: false, // 生成课程幻灯片 Loading
          times: null, // 可选时间
          redesignIdeaLoadLeave: false, // 重新设计课程 Loading 时离开了页面
          redesignIdeaLoading: false, // 重新设计课程 Loading
          maxMeasureSelections: maxMeasureSelections, // 测评点最大选择数量
          evaluationLessonDetailData: {}, // 评估课程详情数据
          needEvaluationLessonDetail: true, // 是否评估课程详情。默认为 true，同一个弹窗中只对比一次
          evaluationLessonDetailVisible: false, // 评估的课程详情的结果弹窗是否显示
          showUnitCompletedDialog: false, // 是否显示单元完成弹窗
          previousCompleteAllLesson: undefined, // 上一次的 completeAllLesson 状态
          showHistoryDrawer: false, // 历史记录抽屉可见性
          hasEnhanceLesson: false, // 是否已经增强课程
          isEnhanceLesson: false, // 是否是增强课程
          isFirstPublish: false, // 是否是第一次发布
          isUpdateCover: false, // 是否更新了封面
          needRecordVersion: false, // 是否需要记录版本
          lastSavedLesson: null, // 上次保存的课程数据，用于比较是否有未保存的更改
          hasBatchGenerated: false, // 是否已执行批量生成操作
          pendingNext: null, // 存储待执行的 next 函数
          objectivesTemp: [], // 存储 objectives 临时数据
          plannerPreviewDialogShow: false, // 单元 overview 预览弹窗显示状态
        }
    },
  created () {
    // 获取用户选择的 objective 类型
    this.$store.dispatch('curriculum/getLessonObjectiveType')
  },
  mounted () {
        // 初始化是否有下一个待生成课程按钮显示状态
        this.$nextTick(() => {
            this.hasNextUnconfirmedLesson = this.$refs.lessonMenuRef && (!!this.$refs.lessonMenuRef.getNextUnconfirmedLesson() || !!this.$refs.lessonMenuRef.getNextLesson())
        })
        this.$store.dispatch('magicCurriculum/checkCurrentUserToMagic')
      // 查询测评点数据
      if (this.unitInfo.frameworkId) {
        this.getFrameworkDomains(this.unitInfo.frameworkId)
      }
      // 如果是 CAPTKLF 框架，就获取映射测评点
      if (frameworkUtils.isCAPTKLF(this.unitInfo.frameworkId) || frameworkUtils.isMELS(this.unitInfo.frameworkId) || frameworkUtils.isILEARN(this.unitInfo.frameworkId)) {
        this.getMappedDomains()
      }
      // 初始化活动和准备时间可选值
      this.times = this.generateTimes()
    },
    destroyed () {
        this.leavedPage = true
        // 离开页面时关闭邀请弹窗
        if (this.$refs.unitCompletedInvitationDialogRef) {
          this.$refs.unitCompletedInvitationDialogRef.close()
        }
        this.$store.dispatch('unit/setShowSurvey', false) // 清空满意度调查问卷显示状态
    },
    computed: {
        ...mapState({
            unit: state => state.curriculum.unit,
            generateUnitFoundationAndWeeklyThemeLoading: state => state.curriculum.generateUnitFoundationAndWeeklyThemeLoading, // 单元生成 foundations 和主题的 loading 状态
            globalRegenerateLesson: state => state.lesson.regenerateLesson, // 重新生成课程数据
            allowPublicMagic: state => state.magicCurriculum.allowPublicMagic,
            publishedIds: state => state.magicCurriculum.publishedIds,
            baseInfo: state => state.curriculum.unit.baseInfo,
            isCG: state => state.curriculum.isCG, // 是否是 CG 项目
            gptSetting: state => state.curriculum.gptSetting,
            currentUser: state => state.user.currentUser,
            batchTasks: state => state.unit.batchTasks,
            batchId: state => state.unit.batchId, // 批量生成课程 ID
            convertMappingAbbrToDomainAbbrMap: state => state.curriculum.convertMappingAbbrToDomainAbbrMap, // 领域缩写转换
            isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
            promptDebugging: state => state.curriculum.promptDebugging, // Prompt 调试
            currentScene: state => state.curriculum.currentScene, // 当前场景
            systemTabs: state => state.curriculum.systemTabs, // 系统标签
            mediaUploaderUploadSuccessFile: state => state.unit.mediaUploaderUploadSuccessFile,
            quizQuestionAllTypes: state => state.curriculum.quizQuestionAllTypes,
            isGenieCurriculumToUnitPlanThird: state => state.curriculum.isGenieCurriculumToUnitPlanThird, // 是否由 genie 生成的 curriculum 进入的
            guideFeatures: state => state.common.guideFeatures, // 功能引导
            isMC: state => state.curriculum.isMC, // 是否是 MC
            eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
            needUpdateLessonTemplate: state => state.lesson.needUpdateLessonTemplate, // 单元是否已经适配
            magicAllowUnits: state => state.magicCurriculum.magicAllowUnits, // 允许创建的 unit 数量
            magicCreatedUnits: (state) => state.magicCurriculum.magicCreatedUnits, // 已创建 unit
            showSearchCover: state => state.unit.showSearchCover, // 是否显示搜索封面功能
            lessonObjectiveType: state => state.curriculum.lessonObjectiveType, // 用户选择的 objective 类型
            isMobile: state => state.common.lgIsMobile
        }),
        // 单元可选的校训选项
        rubricsOptions() {
          return this.baseInfo && this.baseInfo.newRubrics || []
        },
        // 导入改编课程也需要搜索封面
        showSearchCoverFeature () {
          return (this.showSearchCover && !!this.unitOverview.coverKeywords) || this.unit.adaptedType
        },
        // 是否是增强课程
        enhanceLessonfromUnitDetail () {
          // 如果已经增强课程，则直接返回 false
          if (this.hasEnhanceLesson) {
            return false
          }
          if (!this.loading && this.item && this.globalRegenerateLesson) {
            return true
          }
          return false
        },
        // 是否是 Magic Curriculum
        isSharedToMagic: {
          get() {
            return this.$store.state.user.isSharedToMagic || localStorage.getItem('isSharedToMagic') === 'true'
          },
          set (value) {
            localStorage.setItem('isSharedToMagic', value)
            this.$store.dispatch('setIsSharedToMagicAction', value)
          }
        },
        computedAllowSubmit () {
          return !this.allowPublicMagic && this.isMC && !(this.loading || this.publishBtnDisabled)
        },
        // 获取所有周计划的主题和概览信息
        weeklyPlans: {
          get () {
            return this.$store.state.curriculum.unit.weeklyPlans
          },
          set (value) {
          }
        },
        // 是否有区角周
        hasCornerWeek () {
          return this.weeklyPlans.some(item => item.planType === 'CORNER_WEEK')
        },
        unitInfo: {
            get () {
                return this.$store.state.curriculum.unit.baseInfo
            },
            set (value) {
                this.$store.commit('curriculum/SET_BAE_INFO', value)
            }
        },
        unitOverview: {
            get () {
                return this.$store.state.curriculum.unit.overview
            },
            set (value) {
                this.$store.commit('curriculum/SET_OVERVIEW', value)
            }
        },
        promptUsageRecordIds () {
          // 如果 lesson 存在，并且 lesson 的 Id 存在，之后将 lesson 的 Id 转换成大写
          let lessonId = this.lesson && this.lesson.id && this.lesson.id.trim().toUpperCase()
          // 如果 lessonId 是存在的
          if (lessonId) {
            // 从 map 中获取对应的 prompt record 的 Id 集合
            return this.promptUsageRecordIdsMap.get(lessonId) || []
          }
          // 没有 lessonId 的时候，返回空数组
          return []
        },
        publishBtnDisabled () {
            // 示例模板禁止点击
            if (this.unit.exemplar) {
                return true
            }
            // 当前 unit 状态，是否发布到 magic
            if (this.unit.isSharedToMagic) {
                return false
            }
            return false
        },
        feedbackTitle () {
            return this.$t('loc.unitPlannerFeedback')
        },
        feedbackInputPlaceholder () {
            return this.$t('loc.unitPlannerFeedbackPlaceholder')
        },
        feedbackSubmit () {
            return this.$t('loc.unitPlannerFeedbackSubmit')
        },
        showFeedback () {
            // 判断 promptUsageRecordIds 中的每一个值，即对应的子数组都是存在值的
            return !this.loading && this.promptUsageRecordIds.length > 0
        },
        // 是否是 K-12 年级
        isK12Grade () {
          let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
          return this.baseInfo.grade && k12Grades.indexOf(this.baseInfo.grade) > -1
        },
        // 是否是扩展年龄段（TK 4-5）
        isExtendedAgeGroup () {
          let extendedGrades = ['TK (4-5)']
          return this.baseInfo.grade && extendedGrades.indexOf(this.baseInfo.grade) > -1
        },
        lectureSlidesExpandGrade () {
          let k12Grades = ['TK (4-5)']
          return this.baseInfo.grade && k12Grades.indexOf(this.baseInfo.grade) > -1
        },
        // 是否有 Loading
        loading () {
            return this.generateLessonDetailLoading || this.generateTypicalBehaviorsLoading || this.generateUniversalDesignForLearningLoading || this.generatePortraitGraduateLoading || this.generateTeachingTipsForStandardLoading || this.generateCulturallyResponsiveInstructionLoading || this.generateHomeActivityLoading || this.generateLessonQuizLoading || this.batchGenerating || this.generateCustomTemplateLoading || this.generateLessonSlidesLoading
        },
        // currentGenerate 当前 item 是否在 loading
        currentGenerate () {
            return this.generateLessonDetailLoading || this.generateTypicalBehaviorsLoading || this.generateUniversalDesignForLearningLoading || this.generatePortraitGraduateLoading || this.generateTeachingTipsForStandardLoading || this.generateCulturallyResponsiveInstructionLoading || this.generateHomeActivityLoading || this.generateLessonQuizLoading || this.generateCulturallyResponsiveInstructionGroupLoading || this.updateLoading
        },
        // 批量生成课程的 Loading
        batchGenerating () {
          return this.batchGenerateProcessing || this.batchTasksProcessing
        },
        // 是否有正在处理的任务
        batchTasksProcessing () {
          // 如果 batchId 存在，但是 batchTasks 不存在，直接返回 100
          if (this.batchId && !this.batchTasks) {
            return true
          }
          // 如果 batchTasks 不存在，直接返回 false
          if (!this.batchId && !this.batchTasks) {
            return false
          }
          // 遍历 batchTasks，判断是否有正在处理的任务
          const pendingTasks = this.batchTasks.pendingTasks
          const processingTasks = this.batchTasks.processingTasks
          if (pendingTasks.length > 0 || processingTasks.length > 0) {
            return true
          } else {
            return false
          }
        },
        // 是否展示家庭活动
        isShowHomeActivity () {
          var grade = this.baseInfo.grade
          // 需要显示家庭活动的年级
          let showHomeActivity = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)', 'K (5-6)', 'Grade 1', 'Grade 2']
          if (showHomeActivity.includes(grade)) {
            return true
          }
          return this.lesson && this.lesson.atHomeActivityTemp
        },
        // 是否是 Center 课程
        isCenterLesson () {
          // 获取当前活动项的 ID
          const itemId = (this.item && this.item.id) || this.$route.params.itemId
          let isCenterLesson = false
          // 遍历周计划，判断当前活动项是否在周计划中
          for (let i = 0; i < this.weeklyPlans.length; i++) {
            // 获取周计划
            const weeklyPlan = this.weeklyPlans[i]
            // 获取周计划中的 centerItems
            const centerResults = weeklyPlan && weeklyPlan.centerItems.filter(centerItem => equalsIgnoreCase(centerItem.id, itemId))
            // 如果 centerResults 的长度大于 0，说明当前活动项在周计划中
            if (centerResults.length > 0) {
                isCenterLesson = true
            }
          }
          return isCenterLesson
        },
        // 当前课程所在周次
        currentLessonWeek () {
            return function (currentItem) {
                let weeklyPlan = this.weeklyPlans.find(x => {
                    let items = this.isCenterLesson ? x.centerItems : x.items
                    let item = items.find(item => item.id.toUpperCase() === currentItem.id.toUpperCase())
                    if (item) {
                        return x
                    }
                })
                return (weeklyPlan && weeklyPlan.week) || 1
            }
        },
        // 当前课程所在周次
        currentLessonWeeklyPlan () {
            return function (currentItem) {
                let weeklyPlan = this.weeklyPlans.find(x => {
                    let items = this.isCenterLesson ? x.centerItems : x.items
                    let item = items.find(item => item.id.toUpperCase() === currentItem.id.toUpperCase())
                    if (item) {
                        return x
                    }
                })
                return weeklyPlan
            }
        },
        // 已生成所有的课程
        completeAllLesson () {
          if (this.hasCornerWeek) {
            // 有区角周
            if (this.weeklyPlans.length === 0) {
              return undefined
            }
            let items = []
            this.weeklyPlans.forEach(x => {
              if (x.planType === 'CORNER_WEEK') {
                items = items.concat(x.centerItems)
              } else {
                items = items.concat(x.items)
              }
            })
            return items.every(item => !!item.lessonId)
          } else {
            // 没有区角周的旧逻辑
            if (this.weeklyPlans.length === 0 || !this.weeklyPlans.every(x => x.items.length > 0)) {
              return undefined
            }
            let items = []
            this.weeklyPlans.forEach(x => {
              items = items.concat(x.items)
              items = items.concat(x.centerItems)
            })
            return items.every(item => !!item.lessonId)
          }
        },
        // 进入 Center Activities
        showProcessToCenter () {
            // 定义一个遍历用来存放所有的 centerItems 是否已经全部完成
            let centersIsCompleted = false
            for (let i = 0; i < this.weeklyPlans.length; i++) {
                const weeklyPlan = this.weeklyPlans[i]
                const results = weeklyPlan && weeklyPlan.centerItems.filter(center => !center.lessonId)
                if (results.length > 0) {
                    centersIsCompleted = false
                    break
                } else {
                    centersIsCompleted = true
                }
            }
            return !this.hasNextUnconfirmedLesson && !this.completeAllLesson && !centersIsCompleted && this.$route.fullPath.indexOf('/lesson-detail/') !== -1
        },
        // 是否全部生成完成
        allGenerated () {
          let allItems = []
          let allCenterItems = []
          this.weeklyPlans.forEach(weeklyPlan => {
            const itemProcessing = weeklyPlan.items.filter(item => {
              return !item.lessonId
            })
            allItems = allItems.concat(itemProcessing)
            // 判断是否需要收集区角数据。如果当前单元有区角周，非区角周的区角数据（分配过去的）不需要统计。但是区角周下的区角数据要统计
            if (!this.hasCornerWeek || equalsIgnoreCase(weeklyPlan.planType, 'CORNER_WEEK')) {
              const centerItemProcessing = weeklyPlan.centerItems.filter(item => {
                return !item.lessonId
              })
              allCenterItems = allCenterItems.concat(centerItemProcessing)
            }
          })
          return allItems.length === 0 && allCenterItems.length === 0
        },
        batchGenerateProcessing () {
            // 如果不是批量生成，直接返回 false
            if (!this.batchId) {
              return false
            }
            // 是否有正在处理或待处理的活动项
            const processItems = this.weeklyPlans.flatMap(weeklyPlan => {
              const items = weeklyPlan.items
              const centerItems = weeklyPlan.centerItems
              return items.concat(centerItems)
            }).filter(item => {
              return item.processing
            })
            return processItems.length > 0
        },
        batchGenerateLessonLoading () {
          return this.generateLessonDetailLoading || this.generateHomeActivityLoading || this.generateLessonQuizLoading || this.generateUniversalDesignForLearningGroupLoading || this.generateCulturallyResponsiveInstructionGroupLoading || this.generatePortraitGraduateLoading || this.generateTeachingTipsForStandardLoading || this.generateUniversalDesignForLearningLoading || this.generateTypicalBehaviorsLoading || this.batchGenerateProcessing || this.batchTasksProcessing
        },
        // 评估课程详情的结果弹窗标题动态内容动态展示
        evaTitleInfo () {
          // 有测评点建议
          const measureSuggest = this.evaluationLessonDetailData.keepMeasure && this.evaluationLessonDetailData.keepMeasure.length > 0
          // 有活动时长建议
          const durationSuggest = this.evaluationLessonDetailData.duration
          if (measureSuggest && durationSuggest) {
            return this.$t('loc.evaluationLessonDetailTitleAll')
          } else if (measureSuggest && !durationSuggest) {
            return this.$t('loc.evaluationLessonDetailTitleMeasure')
          } else if (!measureSuggest && durationSuggest) {
            return this.$t('loc.evaluationLessonDetailTitleTime')
          } else {
            return this.$t('loc.evaluationLessonDetailTitleAll')
          }
        },
        /**
         * 课程概览编辑表单验证规则
         */
        dynamicRegenerateLessonRules () {
          // 清除之前的校验影响
          if (this.$refs.regenerateLessonForm) {
            this.$refs.regenerateLessonForm.clearValidate()
          }
          return {
            // center/station 不对测评点校验
            measures: this.isCenterLesson ? [] : [
              { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
            ]
          }
        },

        /**
         * 获取单元封面图片的 URL
         * @returns {string} 单元封面图片的 URL
         */
        unitCoverMediasUrl () {
          let unitImageUrl = 'https://s3.amazonaws.com/com.learning-genie.prod.us/unitDefaultCoverK12.png'
          // 获取单元封面图片的 URL
          if (this.unit && this.unit.coverMedias && this.unit.coverMedias.length > 0) {
            unitImageUrl = this.unit.coverMedias[0].url
          }
          if (this.unit && this.unit.overview && this.unit.overview.coverMedias && this.unit.overview.coverMedias.length > 0) {
            unitImageUrl = this.unit.overview.coverMedias[0].url
          }
          return unitImageUrl
        },
        // 单元标题
        unitTitle () {
          return this.unit.title || (this.unit.baseInfo && this.unit.baseInfo.title) || ''
        },
        // 单元描述
        unitDescription () {
          return this.unit.description || (this.unit.baseInfo && this.unit.baseInfo.description) || ''
        },
        // 是否是改编的单元并且课程全部已经生成
        adaptedUnitAllGenerated() {
          return this.unit && this.unit.adapted && this.allGenerated
        },
    },
    watch: {
        '$route': {
            deep: true,
            handler () {
                this.next()
            }
        },
        enhanceLessonfromUnitDetail: {
          immediate: true,
          handler(val) {
            if (val) {
                this.hasEnhanceLesson = true
                if (!this.globalRegenerateLesson) {
                  return
                }
                this.$nextTick(() => {
                  // 将 globalRegenerateLesson 中的值赋值给 regenerateLessonDetail
                  this.$set(this.regenerateLesson, 'measures', this.globalRegenerateLesson.originMeasure)
                  this.$set(this.regenerateLesson, 'activityTime', this.globalRegenerateLesson.activityTime)
                  this.$set(this.regenerateLesson, 'redesignIdea', this.globalRegenerateLesson.redesignIdea)
                  // 清理 vuex 中的数据
                  this.$store.dispatch('setRegenerateLesson', null)
                  this.regenerateLessonDetail(this.regenerateLesson.measures, this.regenerateLesson.frameworkId, this.regenerateLesson.redesignIdea.trim(), true)
                })
            }
          }
        },
        lesson: {
          deep: true,
          immediate: true,
          handler (val) {
            if (val) {
              this.updatePromptSourceList(val)
              //若 lesson 有值，则证明 weeklyplans 已经加载完毕
              // 如果路由中有 batchGenerate 参数, 则批量生成课程
              let batchGenerate = this.$route.params.batchGenerate
              let itemId = this.$route.params.itemId
              if (batchGenerate && !this.hasBatchGenerated && this.$refs.lessonMenuRef) {
                this.hasBatchGenerated = true
                this.$refs.lessonMenuRef.batchNewGenerateUnitLessons(itemId)
              }
            }
          }
        },
        // 监听当前活动项是否在 loading
        currentGenerate: {
            handler (val) {
              this.$set(this.item, 'processing', val)
            }
        },
        // 监听当前使用的 Prompt Scene
        currentScene: {
            handler (val) {
                this.showPromptCustomDetail = this.systemTabs.findIndex(item => equalsIgnoreCase(item, val)) < 0
                // 获取当前 scene 的名称
                let sceneName = this.currentScene || 'LESSON_DETAIL'
                // 如果不是当前场景，就不需要生成参数
                const isNotCurrentScene = this.isNotCurrentScene
                if (isNotCurrentScene.includes(sceneName)) {
                    return
                }
                // 将 scene 放入到 isNotCurrentScene
                this.isNotCurrentScene.push(sceneName)
                // 生成参数的事件名称
                sceneName = sceneName.trim().toLowerCase()
                const eventName = sceneName + ':generateParams'
                var planId = null
                if (this.currentLessonWeeklyPlan(this.item)) {
                  planId = this.currentLessonWeeklyPlan(this.item).id
                }
                // 响应事件
                bus.$on(eventName, (callback) => {
                    const lessonId = this.getCurrentLessonId()
                    callback({
                        unitId: this.unit.id,
                        itemId: this.item ? this.item.id : null,
                        lessonId: lessonId,
                        planId: planId
                    })
                })
            },
            immediate: true
        },
        // 监听 completeAllLesson 的变化
        completeAllLesson: {
          handler(newVal) {
            if (this.previousCompleteAllLesson === undefined) {
              this.previousCompleteAllLesson = newVal
              return
            }
            // 如果新值为 true 且之前为 false，则显示弹窗
            if (newVal && !this.previousCompleteAllLesson) {
              this.showUnitCompletedDialog = true
              // 关闭当前 unit 通知
              let params = {
                unitIds: [this.unit.id.toUpperCase()]
              }
              this.$axios.post($api.urls().setLessonNotificationStatus, params).then((res) => {
              })
              // 发送埋点事件
              this.$analytics.sendEvent('cg_unit_created_successfully')
              this.updateUnitMeta()
            }
          },
          immediate: true
        },
        mediaUploaderUploadSuccessFile (file) {
          // 如果上传成功的文件存在，就解析上传的文件
          this.parseMediaUploaderParam(file)
        },
        batchGenerateLessonLoading () {
          this.hasNextUnconfirmedLesson = this.$refs.lessonMenuRef && (!!this.$refs.lessonMenuRef.getNextUnconfirmedLesson() || !!this.$refs.lessonMenuRef.getNextLesson())
        },
        weeklyPlans: {
          deep: true,
          handler () {
            this.hasNextUnconfirmedLesson = this.$refs.lessonMenuRef && (!!this.$refs.lessonMenuRef.getNextUnconfirmedLesson() || !!this.$refs.lessonMenuRef.getNextLesson())
          }
        },
        // 监听离开提示框提醒埋点
        leaveVisible (val) {
            if (val) {
                this.$analytics.sendEvent('web_unit_create3_det_exit_pop')
            }
        },
        // 如果有框架 id 这查询 测评点数据
        'unitInfo.frameworkId' (frameworkId) {
            if (frameworkId) {
              this.getFrameworkDomains(frameworkId)
            }
        },

        // 监听课程完成弹窗的显示，如果课程弹框关闭则开启分享弹框
        async showUnitCompletedDialog(val) {
          if (val) {
            return
          }

          // 获取创建 unit 奖励状态
          await this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
          // 不能再获取奖励
          let unlockUnlimitedAccess = this.isCurriculumPlugin && this.magicAllowUnits >= 16
          if (!unlockUnlimitedAccess) {
            this.$refs.unitCompletedInvitationDialogRef.openAndAnalyse()
          }
        }
    },
    beforeRouteLeave (to, from, next) {
      // 单个生成课程的 Loading
      const singleGenerateLoading = this.generateLessonDetailLoading || this.generateHomeActivityLoading || this.generateLessonQuizLoading || this.generateUniversalDesignForLearningGroupLoading || this.generateCulturallyResponsiveInstructionGroupLoading || this.generateTeachingTipsForStandardLoading || this.generateUniversalDesignForLearningLoading || this.generateTypicalBehaviorsLoading
      // 批量生成课程的 Loading
      const batchGenerateLoading = this.batchTasksProcessing
      const showBatchGenerateLessonExitTip = JSON.parse(localStorage.getItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_EXIT_TIP'))
      //  是否单个生成课程
      this.singleGenerate = singleGenerateLoading && !batchGenerateLoading
      // 如果是单个生成，退出当前页面时，将单个生成课程的状态设置为 false，不显示进度弹窗
      if (this.singleGenerate) {
        this.$store.dispatch('unit/setSingleGenerate', false)
      }
      // 如果单个生成课程的 Loading 或者批量生成课程的 Loading 为 true，且 showBatchGenerateLessonExitTip 为 true
      if (showBatchGenerateLessonExitTip && (singleGenerateLoading || batchGenerateLoading)) {
        // 保存目标路由信息
        this.pendingNext = next
        this.leaveVisible = true
        next(false)
      } else {
        this.singleGenerateTask()
        next()
      }
    },
    methods: {
        jumpToUnitDetail() {
          // 点击跳转单元详情页
          this.$analytics.sendEvent('cg_adapt_unit_plan_detail_clicked')
          this.$router.push({name: 'unit-detail-cg', query: {unitId: this.unit.id}})
        },
        setPlannerPreviewDialogShow() {
          this.plannerPreviewDialogShow = true
          // 点击 Unit Overview 查看按钮
          this.$analytics.sendEvent('cg_adapt_unit_overview_view_clicked')
        },
        // 跳转到单元页面
        checkUnitOverview() {
          // 如果当前页面正在生成课程，则不跳转
          if (this.generateLessonDetailLoading || this.loading || this.updateLoading) {
            return
          }
           // 点击 Unit Overview 查看按钮
          this.$analytics.sendEvent('cg_adapt_unit_overview_edit_clicked')
          let unitId = ''
          if (this.unit && this.unit.id) {
            unitId = this.unit.id
          }
          let itemId = ''
          if (this.item && this.item.id) {
            itemId = this.item.id
          }
          var params = {
            unitId: unitId,
            itemId: itemId
          }
          this.$router.push({name: 'unit-overview-cg-adapt',params: params})
        },
        // 初始化对照数据
        getSharedAbTest () {
          const flag = this.$analytics.getFeatureFlag('cg-shared-social-media')
          if (!flag) {
            return
          }
          return flag === 'test'
        },
        // 下一个课程是否是区角课程
        nextIsCenter () {
          const next = this.$refs.lessonMenuRef && (this.$refs.lessonMenuRef.getNextUnconfirmedLesson() || this.$refs.lessonMenuRef.getNextLesson())
          return !!next && equalsIgnoreCase(next.activityType, 'Centers')
        },
        // 获取框架映射测评点数据
        getMappedDomains () {
          this.$axios.get($api.urls().getMeasuresContainTopAndBottom, { params: { frameworkId: constants.pscFrameworkId, unitId: '00000000-0000-0000-0000-000000000000' } }).then(res => {
            this.mappedDomains = res.measures
            let convertMappingAbbrToDomainAbbr = new Map()
            // 如果有映射值
            if (res.mappingAbbrToDomainAbbr) {
              // 将映射值转化为 map
              let entries = Object.entries(res.mappingAbbrToDomainAbbr)
              entries.forEach(([key, value]) => {
                convertMappingAbbrToDomainAbbr.set(key, value)
              })
            }
            this.mappedMeasureMap = convertMappingAbbrToDomainAbbr
          })
        },
        // 单个生成课程任务
        singleGenerateTask () {
          // 如果是单个生成的离开，则需要将当前的课程存入生成任务中
          if (this.singleGenerate) {
            this.$refs.lessonMenuRef.batchGenerateUnitLessons(true, false, this.item)
          }
        },
      // 获取当前周计划的 ID
        getCurrentLessonId () {
            // 定义 planId
            let lessonId = null
            // 定义重试次数
            let retryCount = 0
            // 如果当前的周计划存在，则返回当前的周计划 ID
            if (this.lesson) {
                lessonId = this.lesson.id
            } else {
                const getLessonId = (timeoutId) => {
                    clearTimeout(timeoutId)
                    // 如果当前的课程存在，则返回当前的课程 ID
                    if (this.lesson) {
                        return this.lesson.id
                    }
                    // 如果重试次数大于 10，则返回 null
                    if (retryCount > 10) {
                        return null
                    }
                    // 重试次数加 1
                    retryCount++
                    // 100 毫秒后再次获取课程 ID
                    return getLessonId(setTimeout(() => {}, 100))
                }
                lessonId = getLessonId(setTimeout(() => {}, 100))
            }
            return lessonId
        },
        // 更新课程 quiz
        updateQuestions (questions) {
          // 更新课程 quiz
          this.$set(this.lesson, 'questions', questions)
          // 更新 quizRenderKey
          this.$refs.lessonDetailRef.quizRenderKey++
        },
        // 重新生成课程 quiz
        regenerateQuizQuestion (param) {
          // 如果 param 存在，则重新生成课程单个 quiz
          if (param) {
            this.regenerateLessonQuiz(param)
          } else {
            // 如果 param 不存在，则重新生成课程全部 quiz
            this.generateLessonQuizStream(false, {})
          }
        },
        // 反馈提交的时间
        submitFeedback (feedbackAdditional) {
            if (feedbackAdditional && feedbackAdditional.promptUsageRecordIds && feedbackAdditional.promptUsageRecordIds.length > 0) {
                // 如果 feedback 提交了反馈，这里判断一下有没有可能反馈的 promptUsageRecordId 和当前的 lesson 的 promptUsageRecordId 不一致
                // 因为每一个 createEventSource 都会生成一个 promptUsageRecordId，所以这里需要判断一下
                // 如果不一样，就替换掉当前课程的 promptUsageRecordId
                let lessonId = this.lesson && this.lesson.id && this.lesson.id.trim().toUpperCase()
                if (lessonId) {
                    // 从 map 中获取对应的 prompt record 的 Id 集合
                    let promptUsageRecordIds = this.promptUsageRecordIdsMap.get(lessonId) || []
                    // 如果 promptUsageRecordIds 的长度大于 0，说明之前已经有 promptUsageRecordId 了
                    if (promptUsageRecordIds.length > 0) {
                        // 如果当前的 promptUsageRecordId 和之前的 promptUsageRecordId 不一样，就替换掉
                        if (promptUsageRecordIds[0] !== feedbackAdditional.promptUsageRecordIds[0]) {
                            this.promptUsageRecordIdsMap.set(lessonId, [feedbackAdditional.promptUsageRecordIds[0]])
                        }
                    } else {
                        // 如果 promptUsageRecordIds 的长度等于 0，说明之前没有 promptUsageRecordId，就直接设置
                        this.promptUsageRecordIdsMap.set(lessonId, [feedbackAdditional.promptUsageRecordIds[0]])
                    }
                }
            }
        },
        // 关闭弹窗
        handleClose () {
          this.leaveVisible = false
          this.hideUserGuide()
        },
        // 确认退出
        confirmExit () {
          this.singleGenerateTask()
          this.hideUserGuide()
          // 如果有保存的目标路由，跳转到目标路由
          if (this.pendingNext) {
            // 调用保存的 next 函数继续导航
            this.pendingNext()
            // 清空保存的路由
            this.pendingNext = null
          } else {
            // 如果没有保存的路由信息，使用回退（兼容其他场景）
            this.$router.back()
          }
        },
        // 反馈按钮点击事件
        clickFeedback (isUP) {
            if (isUP) {
                if (this.isCenterLesson) {
                    this.$analytics.sendEvent('web_unit_create3_center_det_feedback_p')
                } else {
                    this.$analytics.sendEvent('web_unit_create3_group_det_feedback_p')
                }
            } else {
                if (this.isCenterLesson) {
                    this.$analytics.sendEvent('web_unit_create3_center_det_feedback_n')
                } else {
                    this.$analytics.sendEvent('web_unit_create3_group_det_feedback_n')
                }
            }
        },
        // 设置 feedback 的样式
        setFeedbackStyle () {
            return {
                position: 'absolute',
                right: '0px',
                top: '-9px',
                zIndex: '2000'
            }
        },
        // 更新资源生成状态
        updateGeneratedLessonSources () {
            this.generatedLessonSources = false
        },
        // 生成内容
        generateContent (callback, scene) {
            if (scene === 'LESSON_DETAIL') {
                this.generateLessonDetailStream(callback)
            } else if (scene === 'TYPICAL_BEHAVIOR') {
                this.generateTypicalBehaviorsStream(callback)
            } else if (scene === 'UNIVERSAL_DESIGN_FOR_LEARNING') {
                this.generateUniversalDesignForLearningStream(callback)
            } else if (scene === 'CULTURALLY_RESPONSIVE_INSTRUCTION') {
                this.generateCulturallyResponsiveInstructionStream(callback)
            } else if (scene === 'HOME_ACTIVITY') {
                this.generateHomeActivityStream(callback)
            } else {
              // 如果是其他的情况，那么传递的是 scene 就调用通用的 scene 的方法
              this.generateContentByScene(this.promptId, callback)
            }
        },
        savePromptCustomDetail () {
            if (this.$refs.promptCustomDetailRef) {
                this.$refs.promptCustomDetailRef.savePromptCustomDetail()
            }
        },
        generateContentByScene (promptId, callback) {
            this.generateCustomerDetail = true
            // 重置数据
            this.generateCustomerDetailContent = ''
            // 生成单元概览参数
            let params = {
                promptId: promptId,
                unitId: this.unit.id,
                planId: this.currentLessonWeeklyPlan(this.item).id,
                itemId: this.item.id,
                lessonId: this.lesson.id
            }

            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                this.generateCustomerDetailContent += message.data
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateContentStream, null, messageCallback, 'POST', params)
                    .then((res) => {
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                            promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 生成结束
                        this.generateCustomerDetail = false
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        this.generateCustomerDetail = false
                        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
            })
        },
        // 重新生成课程详情
        regenerateCustomDetail () {
            // 调用 promptVersion 的生成方法
            if (this.$refs.promptEditorRef) {
                // 获取 promptEditorRef
                let promptEditorRef = this.$refs.promptEditorRef.$refs.promptEditorRef
                if (promptEditorRef) {
                    promptEditorRef.generateResult()
                }
            }
        },
        // 批量生成结果成功
        batchGenerateResultsSuccess (result) {
            // 更新测试记录 ID
            this.testId = result.testRecordId
            // 获取测试结果
            this.$refs.promptStatsResultRef.getTestResults(true)
            // 切换到测试列表页面
            this.currentView = 'STATS_RESULT'
        },
        // 更新 Prompt ID
        updatePromptId (promptId) {
            this.promptId = promptId
        },
        // 测试完成
        testCompleted () {
            // 获取测试结果
            this.$refs.promptEditorRef.testCompleted()
        },

        // 测试未完成
        testIncomplete () {
            // 获取测试结果
            this.$refs.promptEditorRef.testIncomplete()
        },
        // 切换课程或周计划
        changeLesson (item, lesson, weeklyPlan) {
            this.item = item
            this.lesson = lesson
            if (this.$refs.lessonDetailRef) {
              this.$refs.lessonDetailRef.oldLessonName = item.title
            }
            let cover = null
            if (lesson && (lesson.cover || (lesson.coverMedias && lesson.coverMedias.length > 0))) {
                cover = lesson.cover || lesson.coverMedias[0]
            }
            // 判断当前课程的资源信息列表是否存在，如果存在则默认首先展示资源
            if (this.lesson && this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.sources && this.lesson.lessonClrAndSources.sources.length > 0) {
              this.$refs.lessonDetailRef.showSource = true
            }
            // 判断当前课程实施步骤资源是否存在，进行展示
            if (this.lesson && this.lesson.lessonImpStepAndSource && this.lesson.lessonImpStepAndSource.sources && this.lesson.lessonImpStepAndSource.sources.length > 0) {
              this.$refs.lessonDetailRef.showImplementationStepsSource()
            }
            // 切换课程时需要将 generatedLessonSources 置为 true, 表示 CLR 内容发生了变化
            this.generatedLessonSources = true
            this.$nextTick(() => {
                if (lesson) {
                    this.$set(lesson, 'cover', cover)
                }
                this.$refs.lessonDetailRef && this.$refs.lessonDetailRef.updateLessonCover(cover)
                // 设置课程的 quiz 难度等级
                this.$refs.lessonDetailRef && this.$refs.lessonDetailRef.setStepQuizBloomSetting(this.lesson.questions)
                // 切换课程后重新计算是否有下一个待生成课程按钮显示状态
                this.hasNextUnconfirmedLesson = this.$refs.lessonMenuRef && (!!this.$refs.lessonMenuRef.getNextUnconfirmedLesson() || !!this.$refs.lessonMenuRef.getNextLesson())
            })
            if (this.lesson) {
              // 保存当前课程数据，用于后续比较是否有未保存的更改
              this.lastSavedLesson = JSON.parse(JSON.stringify(this.lesson))
            }
            this.weeklyPlan = weeklyPlan
            // 如果有课程 ID ，直接获取课程详情
            if (item.lessonId && !this.lesson) {
                this.getLessonDetail(item)
            } else if (item && !item.lessonId) {
                // 如果没有课程 ID，根据是否符合重新生成条件进行生成或者获取
                // 如果是 Center 课程且是第二周以后的课程，则获取 Center 课程
                if (this.isCenterLesson && this.currentLessonWeek(this.item) > 1) {
                    this.getCenterLesson(item)
                } else {
                    // 如果不是 Center 课程或者是第一周的 Center 课程，则生成课程
                    this.generateLessonDetailStream()
                    this.$set(this.item, 'processing', true)
                }
            }
              // 改编的课程不做任何处理
              if (lesson && !lesson.adaptedLesson) {
                let objectiveType = this.lessonObjectiveType
                if (lesson && lesson.formalDescription && objectiveType && equalsIgnoreCase(objectiveType, 'formal_description')) {
                  this.$set(this.lesson, 'objectives', lesson.formalDescription)
                  this.$set(this.lesson, 'formalDescription', '')
                  this.$set(this.lesson, 'studentFriendly', '')
                  // 更新 lesson 中的 objectives
                  this.updateLesson(null, null, null, null, null, true)
              }
              if (lesson && lesson.studentFriendly && objectiveType && equalsIgnoreCase(objectiveType, 'student_friendly')) {
                  this.$set(this.lesson, 'objectives', lesson.studentFriendly)
                  this.$set(this.lesson, 'formalDescription', '')
                  this.$set(this.lesson, 'studentFriendly', '')
                  // 更新 lesson 中的 objectives
                  this.updateLesson(null, null, null, null, null, true)
              }
            }
        },

        // 获取课程详情
        getLessonDetail (item) {
            let newTemplateType = item.lessonTemplateType
            this.generateLessonDetailLoading = true
            Lessons2.getCurriculumLessonDetail(item.lessonId)
            .then(res => {
                this.lesson = res
      
                // 保存原始 athome 数据
                this.$set(this.lesson, 'atHomeActivityTemp', res.homeActivity)
                // 保存标准教学指导数据
                this.$set(this.lesson, 'teachingTips', res.teachingTips)
                // 保存课程校训数据
                this.$set(this.lesson, 'learnerProfiles', res.learnerProfiles)
                // 提取媒体数据
                const materialFiles = {
                  description: res.materialFiles.descriptions && res.materialFiles.descriptions.join('\n'),
                  media: this.extractMedia(res.materialFiles.media),
                  externalMediaUrl: res.materialFiles.externalMediaUrl, // 媒体路径
                  externalMediaUrlId: res.materialFiles.externalMediaUrlId, // 媒体 Id
                  attachmentMedias: (res.materialFiles.attachmentMedias || []).map(item => this.extractMedia(item))
                }

                this.$set(this.lesson, 'materialFiles',materialFiles)
                // 如果 CLR 数据不为空时，执行如下逻辑
                if (this.lesson.culturallyResponsiveInstruction) {
                  this.lesson.culturallyResponsiveInstruction = this.lesson.culturallyResponsiveInstruction.replace(/\n/g, '<br>')
                  // 如果 CLR 资源不存在时，则需要将 CLR 数据设置到资源中, 并将 sources 置为空数组即可
                  if (!this.lesson.lessonClrAndSources) {
                    this.$set(this.lesson, 'lessonClrAndSources', {
                      clr: this.lesson.culturallyResponsiveInstruction,
                      sources: []
                    })
                  }
                }
                this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.sources.forEach(source => {
                  this.$set(source, 'hidden', false)
                })
                this.$set(item, 'lesson', res)
                // 改编的课程不做任何处理
              if (this.lesson && !this.lesson.adaptedLesson) {
                let objectiveType = this.lessonObjectiveType
                let objectivesList = this.lesson.objectivesList
                if (objectivesList && objectivesList.length > 0) {
                  if (objectivesList.length === 2) {
                    // 如果 objectiveType 不为空，则根据 objectiveType 赋值
                    if (objectiveType) {
                      if (equalsIgnoreCase(objectiveType, 'formal_description')) {
                        this.$set(this.lesson, 'objectives', objectivesList[0])
                        this.$set(this.lesson, 'formalDescription', '')
                        this.$set(this.lesson, 'studentFriendly', '')
                        // 更新 lesson 中的 objectives
                        this.updateLesson(null, null, null, null, null, true)
                      } else if (equalsIgnoreCase(objectiveType, 'student_friendly')) {
                        this.$set(this.lesson, 'objectives', objectivesList[1])
                        this.$set(this.lesson, 'formalDescription', '')
                        this.$set(this.lesson, 'studentFriendly', '')
                        // 更新 lesson 中的 objectives
                        this.updateLesson(null, null, null, null, null, true)
                      }
                    } else {
                      // 如果 objectiveType 为空，则根据 objectivesList 赋值
                      this.$set(this.lesson, 'formalDescription', objectivesList[0])
                      this.$set(this.lesson, 'studentFriendly', objectivesList[1])
                    }
                  } else {
                    this.$set(this.lesson, 'formalDescription', '')
                    this.$set(this.lesson, 'studentFriendly', '')
                  }
                }
              }
               

                // 将课程模板类型赋值给活动项
                this.$set(item, 'lessonTemplateType', res.templateType)
                this.$set(item.lesson, 'materialFiles', materialFiles)
                let cover = null
                if (res && res.coverMedias && res.coverMedias.length > 0) {
                    cover = res.coverMedias[0]
                }
                this.$nextTick(() => {
                    this.$set(this.lesson, 'cover', cover)
                    this.$refs.lessonDetailRef.updateLessonCover(cover)
                })
                // 拿到详情后判断是否有下一个未确认的课程
                this.hasNextUnconfirmedLesson = this.$refs.lessonMenuRef && (!!this.$refs.lessonMenuRef.getNextUnconfirmedLesson() || !!this.$refs.lessonMenuRef.getNextLesson())
                this.generateLessonDetailLoading = false
                this.$nextTick(() => {
                  // 如果存在资源列表，则默认展示资源信息
                  if (this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.sources && this.lesson.lessonClrAndSources.sources.length > 0) {
                    this.$refs.lessonDetailRef.showSource = true
                  }
                  // 保存一份课程数据作为备份
                  this.lessonCopy = JSON.parse(JSON.stringify(this.collectData()))
                  // 初次加载时保存一份课程数据，用于后续比较是否有未保存的更改
                  this.lastSavedLesson = JSON.parse(JSON.stringify(this.lesson))
                })
                // 判断是否需要更新课程模板，进行更新
                if (this.needUpdateLessonTemplate) {
                  // 清除更新课程模板状态
                  this.$store.dispatch('setNeedUpdateLessonTemplate', false)
                  // 更新课程模板
                  this.updateLessonTemplate(newTemplateType)
                } else {
                  this.$nextTick(() => {
                    // 显示引导
                    this.$bus.$emit('showEduProtocolsTemplateCustomGuide', this.lesson.id)
                  })
                }
            })
            .catch(error => {
                this.generateLessonDetailLoading = false
                this.$message.error(error.response.data.error_message)
            })
        },

        // 构建媒体对象
        extractMedia (media) {
          media = media || {}
          return {
            id: media.id,
            name: media.sourceFileName,
            url: media.url,
            size: media.size
          }
        },

        // 获取 Center 课程
        getCenterLesson (item) {
            this.generateLessonDetailLoading = true
            let lastWeeklyPlanId = this.weeklyPlans[0].id
            Lessons2.getCenterLessonDetail(lastWeeklyPlanId, item.id)
            .then(res => {
                if (res) {
                    this.lesson = res
                    this.$set(item, 'lesson', res)
                    let cover = null
                    if (res && res.coverMedias && res.coverMedias.length > 0) {
                        cover = res.coverMedias[0]
                    }
                    this.$nextTick(() => {
                        if (this.lesson) {
                            this.$set(this.lesson, 'cover', cover)
                        }
                        this.$refs.lessonDetailRef.updateLessonCover(cover)
                    })
                    this.generateLessonDetailLoading = false
                    this.createLesson()
                } else {
                    this.generateLessonDetailStream()
                }
            })
        },

        // 推荐课程模板
        async recommendLessonTemplate () {
          let params = {
            unitId: this.unit.id,
            planId: this.item.planId,
            itemId: this.item.id
          }
          await this.$axios.post($api.urls().recommendLessonTemplate, params)
          .then(res => {
            // 遍历当前周计划活动项，将推荐的课程模板赋值给对应的活动项
            let recommendItem = res.recommendLessonTemplateModelList.find(x => equalsIgnoreCase(x.itemId, this.item.id))
            if (recommendItem) {
              this.$set(this.item, 'lessonTemplateType', recommendItem.recommendedTemplate)
            }
          })
        },

        // 下个课程状态信息更新
        changeNextLessonStatus (hasNextUnconfirmedLesson, allConfirmed, unconfirmedLessonOverviewWeeklyPlan) {
            this.hasNextUnconfirmedLesson = hasNextUnconfirmedLesson
            this.allConfirmed = allConfirmed
            this.unconfirmedLessonOverviewWeeklyPlan = unconfirmedLessonOverviewWeeklyPlan
        },

        // 同步生成课程详情
        generateLessonDetail () {
            this.generateLessonDetailLoading = true
            let baseInfo = JSON.parse(JSON.stringify(this.unit.baseInfo))
            // 如果课程中有框架 ID ，则以课程中的框架 ID 为准
            if (this.lesson && this.lesson.frameworkId) {
                baseInfo.frameworkId = this.lesson.frameworkId
            }
            // 默认给个空值
            this.lesson.id = ''
            let params = {
                baseInfo: baseInfo,
                weeklyPlan: this.unit.weeklyPlans[0],
                lessonOverview: this.lesson,
                gptSetting: this.gptSetting
            }
            this.$axios
                .post(aiApiUrl + '?action=generate_lesson_detail', params).then((res) => {
                    for (let key in res.result) {
                        this.$set(this.lesson, key, res.result[key])
                    }
                    this.generateLessonDetailLoading = false
                    // 生成典型行为
                    this.generateTypicalBehaviors()
                })
                .catch((error) => {
                    let errorMsg = error.response.data.error
                    // 限制重试次数
                    if (errorMsg && (errorMsg.includes('Extra data:') || errorMsg.includes('Expecting ')) && this.retryCount < 1) {
                        this.retryCount = this.retryCount + 1
                        this.generateLessonDetail()
                        return
                    }
                    this.generateLessonDetailLoading = false
                    this.$message.error(error.response.data.error_message)
                })
        },

        // 异步生成课程详情
        async generateLessonDetailStream (callback, redesignIdea) {
            // 如果存在课程，提前保存，后面做切换模板使用
            if (this.lesson && this.lesson.id) {
              await this.createLesson()
            }
            // if (true) {
            //     this.$set(this.lesson, 'cover', {
            //         id: "5956af21-6e41-42a0-87f4-66b9ff566ab3",
            //         public_url: "https://s3.amazonaws.com//com.learning-genie.prod.us/ai/b8321eed-2a99-4640-bd85-b877c842e68c.png"
            //     })
            //     this.$refs.lessonDetailRef.updateLessonCover({
            //         id: "5956af21-6e41-42a0-87f4-66b9ff566ab3",
            //         public_url: "https://s3.amazonaws.com//com.learning-genie.prod.us/ai/b8321eed-2a99-4640-bd85-b877c842e68c.png"
            //     })
            //     return
            // }
            // 没有选中的活动则跳过
            if (!this.item) {
                return
            }
            // 如果需要推荐课程模板，先推荐课程模板
            if (this.eduProtocolsTemplateApplyOpen && this.baseInfo.useLessonTemplate && !this.item.lessonTemplateType && this.isK12Grade && !this.isCenterLesson) {
              await this.recommendLessonTemplate()
            }
            // 如果是单个生成课程
            if (!this.batchId) {
              this.$store.dispatch('unit/setSingleGenerate', true)
            }
            this.generateLessonDetailLoading = true
            this.$set(this.item, 'generateLessonDetailLoading', true)
            // 重置数据
            this.item.lessonData = ''
            // 根据数量生成周计划空对象
            let emptyLesson = {
              generateLessonCoverLoading: true,
              ...(this.lesson && {
                id: this.lesson.id,
                adaptedLesson: this.lesson.adaptedLesson
              })
            }
            let originalLesson = this.item.lesson && JSON.parse(JSON.stringify(this.item.lesson))

            let originalMaterialFiles = {}
            if (this.item.lesson && this.item.lesson.materialFiles) {
              // 保存旧媒体数据
              originalMaterialFiles = JSON.parse(JSON.stringify(this.item.lesson.materialFiles))
              // 移除 description
              originalMaterialFiles.description = null
            }
            // 留存原始课程模板数据
            if (this.item.lesson && this.item.lesson.lessonTemplate) {
              this.originalLessonTemplate = this.lesson.lessonTemplate
            }
            let failedTip = false
            this.$set(this.item, 'lesson', emptyLesson)
            // 课程内容是否发生变化，如果课程为空，则认为为发生变化，否则对比课程内容是否发生变化
            let lessonChanged = this.lesson && JSON.stringify(this.collectData()) !== JSON.stringify(this.lessonCopy) || false
            // 如果是增加改编想法重新生成课程或已修改课程,且课程模板为 BOOKA_KUCHA 类型，则需要判断课程中是否包含书籍
            if (this.item.lessonTemplateType == 'BOOKA_KUCHA' && this.lesson && this.lesson.id) {
              let params = {
                id: this.lesson.id
              }
              let result = await LessonApi.checkLessonContainBooks(params)
              if (!result.success) {
                this.$message.error(this.$t('loc.eduprotocols21'))
                this.$store.dispatch('unit/setSingleGenerate', false)
                this.lesson = originalLesson
                this.$set(this.item, 'lesson', originalLesson)
                this.generateLessonDetailLoading = false
                this.$set(this.item, 'generateLessonDetailLoading', false)
                return
              }
            }
            let duration = this.regenerateLesson && this.regenerateLesson.activityTime && typeof this.regenerateLesson.activityTime === 'string' ? Number(this.regenerateLesson.activityTime.split(' ')[0]) : typeof this.regenerateLesson.activityTime === 'number' ? this.regenerateLesson.activityTime : '';
            let batchGenerateLessonFlag = localStorage.getItem('batchGenerateLessonFlag')
            // 重置 batchGenerateLessonFlag 中的值
            localStorage.setItem('batchGenerateLessonFlag', false)
            // 参数
            let params = {
                unitId: this.unit.id,
                itemId: this.item.id,
                redesignIdea: redesignIdea,
                duration: duration,
                changeContent: lessonChanged,
                batchGenerateLessonFlag: batchGenerateLessonFlag
            }
            // 清空 Google Slide 数据
            this.googleSlideData = null
            // 消息回调
            let messageCallback = (message) => {
                if (this.leavedPage || failedTip) {
                    return
                }
                // 更新数据
                this.item.lessonData += message.data
                this.item.lessonData = this.item.lessonData.replace('```', '')
                // 如果数据中包含 yes，则说明输入的提示词不合适，提示用户重试
                if (this.item.lessonData.trim().toLocaleLowerCase().startsWith('yes') && !failedTip) {
                    this.$store.dispatch('unit/setSingleGenerate', false)
                    failedTip = true
                    this.$message.error(this.$t('loc.unableToProcessRequestAdjustDescription'))
                    this.lesson = originalLesson
                    this.$set(this.item, 'lesson', originalLesson)
                    this.generateLessonDetailLoading = false
                    this.$set(this.item, 'generateLessonDetailLoading', false)
                    return
                }
                // 解析课程数据 keys
                let parseKeys = this.getParseKeys(!!redesignIdea)
                // 解析课程数据
                let parsedData = parseStreamData(this.item.lessonData, parseKeys, 1)[0]
                if (this.isCenterLesson && !this.isEnhanceLesson) {
                  // 如果是 Center  非 Enhance 需要去除 activityTime
                  parsedData.activityTime = ''
                }
                // 解析 Google Slide 数据
                if (parsedData.moudle1 && this.item.lessonTemplateType && !redesignIdea) {
                  this.googleSlideData = this.parseGoogleSlideData(parsedData.moudle1, this.item.lessonTemplateType)
                }
                // 解析测评点
                let newLesson = parsedData
                // 判断是否需要解析 objectives
                if (newLesson.objectives) {
                  if (!this.guideFeatures) {
                    this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
                      if (result.showLessonObjectivesGuide) {
                        // 增加埋点
                        this.$analytics.sendEvent('cg_unit_objective_style_view')
                        let objectives = newLesson.objectives
                        // 首次需要解析 objectives
                        let newObjective = parseStreamData(objectives, [
                          { key: 'formalDescription', name: 'Formal Description:' },
                          { key: 'studentFriendly', name: 'Student-Friendly:' }
                        ])[0]

                        if (newObjective.formalDescription) {
                          this.$set(newLesson, 'formalDescription', newObjective.formalDescription)
                        }
                        if (newObjective.studentFriendly) {
                          this.$set(newLesson, 'studentFriendly', newObjective.studentFriendly)
                        }
                      }
                    })
                  } else {
                    if (this.guideFeatures && this.guideFeatures.showLessonObjectivesGuide) {
                      // 增加埋点
                      this.$analytics.sendEvent('cg_unit_objective_style_view')
                      let objectives = newLesson.objectives
                        // 首次需要解析 objectives
                        let newObjective = parseStreamData(objectives, [
                          { key: 'formalDescription', name: 'Formal Description:' },
                          { key: 'studentFriendly', name: 'Student-Friendly:' }
                        ])[0]
                        if (newObjective.formalDescription) {
                          this.$set(newLesson, 'formalDescription', newObjective.formalDescription)
                        }
                        if (newObjective.studentFriendly) {
                          this.$set(newLesson, 'studentFriendly', newObjective.studentFriendly)
                        }
                    }
                  }
                }
                
                // 年龄组匹配不上，则使用单元的年龄组
                if (newLesson.ageGroup && newLesson.ageGroup !== this.baseInfo.grade) {
                    newLesson.ageGroup = this.baseInfo.grade
                }
                if (newLesson.implementationSteps) {
                    // 只有 lesson 课程才需要过滤 implementationSteps
                    if (!this.isCenterLesson) {
                        // 由于更换模型，生成的 implementationSteps 会有多余的内容，需要过滤
                        // 由于更换模型，生成的 guidingQuestions 会有多余的内容，需要过滤
                        // 以两个 /n/n 为分隔符，获取最后一个分隔符之后的内容
                        let implementationStepsTemp = newLesson.implementationSteps.split('\n\n')
                        // 若 implementationStepsTemp 不为空且长度大于 1
                        if (implementationStepsTemp && implementationStepsTemp.length > 1) {
                            // 获取最后一个分隔符之后的内容
                            let lastImplementationStepsTemp = implementationStepsTemp[implementationStepsTemp.length - 1]
                            // 进行正则判断，判断是否以数字开头
                            let regex = /^(\d+).*/gm
                            // 使用正则表达式匹配保留语句
                            let matchedStatements = lastImplementationStepsTemp.match(regex)
                            // 再将匹配到的保留到 implementationStepsTemp 中
                            if (matchedStatements) {
                                // 将匹配后的内容拼接到 implementationStepsTemp 中
                                implementationStepsTemp[implementationStepsTemp.length - 1] = matchedStatements.join('\n')
                                // 将 implementationStepsTemp 拼接成字符串，赋值给 newLesson.implementationSteps
                                newLesson.implementationSteps = implementationStepsTemp.join('\n\n')
                            } else {
                                // 若没有匹配到，则将 implementationStepsTemp 除去最后一个元素，然后赋值给 newLesson.implementationSteps
                                newLesson.implementationSteps = implementationStepsTemp.slice(0, implementationStepsTemp.length - 1).join('\n')
                            }
                        }
                    } else {
                      // 如果是 Center 非 Enhance 需要去除 activityTime
                      if (!this.isEnhanceLesson) {
                        newLesson.activityTime = ''
                      }
                    }
                    newLesson.implementationSteps = newLesson.implementationSteps.replace(/\n/g, '<br>')
                    newLesson.implementationSteps = newLesson.implementationSteps.replace(/Dialogue:\s*"([^"]+)"/g, '<i>$1</i>')
                    newLesson.implementationSteps = newLesson.implementationSteps.replace(/Dialogue:\s*/g, '')
                }
                // if (newLesson.materials) {
                //     newLesson.materials = newLesson.materials.replace(/\n/g, '<br>')
                // }
                // if (newLesson.objectives) {
                //     newLesson.objectives = newLesson.objectives.replace(/\n/g, '<br>')
                // }
                // if (newLesson.keyVocabularyWords) {
                //     newLesson.keyVocabularyWords = newLesson.keyVocabularyWords.replace(/\n/g, '<br>')
                // }
                if (this.item && this.item.measures) {
                    // 设置值
                    this.$set(newLesson, 'measures', this.item.measures)
                } else if (newLesson.measuresString) {
                    let measures = newLesson.measuresString.split(';')
                    // 只有一个测评点时，尝试用逗号分隔
                    if (measures && measures.length == 1) {
                        measures = newLesson.measuresString.split(',')
                    }
                    // 去除空格
                    measures = measures.map(measure => measure.trim())
                        .map(measure => {
                            // 如果 convertMappingAbbrToDomainAbbrMap 存在并且长度大于 0，则使用 convertMappingAbbrToDomainAbbrMap 中的值
                            if (this.convertMappingAbbrToDomainAbbrMap && this.convertMappingAbbrToDomainAbbrMap.size > 0) {
                                return this.convertMappingAbbrToDomainAbbrMap.get(measure) || measure
                            }
                            return measure
                        })
                    // 设置值
                    this.$set(newLesson, 'measures', measures)
                }
                // 更新 ID
                if (this.lesson) {
                    newLesson.id = this.lesson.id
                    newLesson.cover = this.lesson.cover
                    newLesson.generateLessonCoverLoading = true
                    newLesson.adaptedLesson = this.lesson.adaptedLesson
                }

                // 赋值旧材料媒体数据以及 GPT 返回数据
                this.$set(newLesson, 'materialFiles', {
                  ...originalMaterialFiles,
                  description: newLesson.materials
                })
                // 设置 classroomType
                if (!newLesson.classroomType) {
                  newLesson.classroomType = this.baseInfo.classroomType || "IN_PERSON"
                }
                // 更新数据
                this.lesson = newLesson
                this.$set(this.item, 'lesson', newLesson)
                this.weeklyPlans.forEach(weeklyPlan => {
                    let items = this.isCenterLesson ? weeklyPlan.centerItems : weeklyPlan.items
                    let item = items.find(item => equalsIgnoreCase(item.id, this.item.id))
                    if (item) {
                        this.$set(item, 'lesson', newLesson)
                        this.$set(item, 'lessonId', newLesson.id)
                        // 更新课程标题
                        if (newLesson.title) {
                          this.$set(item, 'title', newLesson.title)
                        }
                    }
                })
              if (this.$refs.lessonDetailRef) {
                this.$refs.lessonDetailRef.oldLessonName = newLesson.title
              }
            }
            let url = this.isCenterLesson ? $api.urls().generateCenterLessonStream : $api.urls().generateLessonStream
            // 生成课程概览
            await createEventSource(url, null, messageCallback, 'POST', params)
                .then(async (res) => {
                    if (failedTip) {
                        return
                    }
                    // 如果不是批量生成课程，那么就是单个生成来到的这里，则设置 singleGenerate 为 false
                    if (!this.batchId && this.isCenterLesson) {
                      this.$store.dispatch('unit/setSingleGenerate', false)
                    }
                    // 记录 promptUsageRecordId
                    let promptUsageRecordIds = this.promptUsageRecordIds
                    if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                        promptUsageRecordIds = [res.promptUsageRecordId]
                    }
                    // 更新周计划项基础信息
                    await this.upItemBaseInfo()
                    // 如果离开页面，则不再执行
                    if (this.leavedPage) {
                        return
                    }
                    // 异步生成实施步骤资源   按钮要置灰，生成完后按钮再会高亮
                    if (!this.isCenterLesson) {
                      this.generateImplementationStepsSourceLessonDetail()
                    }
                    // 保存课程并生成典型行为
                    await this.createLesson(!this.isCenterLesson)
                    if (this.isCenterLesson) {
                      // 生成完成后调用 updateLesson 保存课程和版本
                      await this.updateLesson(null, null, null, null, null, true)
                    }
                    // 如果已有课程封面，不再生成
                    if (this.lesson.cover) {
                        this.$set(this.lesson, 'generateLessonCoverLoading', false)
                    } else {
                        // 首次生成 lesson 封面埋点
                        if (this.showSearchCoverFeature) {
                          this.$analytics.sendEvent('cg_unit_lesson_cover_generated_search')
                        } else {
                          this.$analytics.sendEvent('cg_unit_lesson_cover_generated_ai')
                        }
                        this.showSearchCoverFeature ? this.extractAndSearchLessonCover() : this.generateLessonCover()
                    }
                    // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                    if (this.lesson && this.lesson.id) {
                      // 定义 lessonId
                      let lessonId = this.lesson.id.trim().toUpperCase()
                      this.promptUsageRecordIdsMap = new Map()
                      this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                    }
                    // 如果是 CG 项目
                    if (this.isCG) {
                        // 则弹窗分组课程弹窗
                        // this.$nextTick(() => {
                        //     this.$refs.lessonDetailRef.showAdaptationConfirm('GENERATE_ALL')
                        // })
                    }
                    // 生成结束
                    if (this.isCenterLesson) {
                        this.generateLessonDetailLoading = false
                        // 生成结束之后，将当前课程的生成状态设置为 false
                        this.$set(this.item, 'processing', false)
                    }
                    // 如果存在 Google Slide 数据，则存储起来
                    if (this.googleSlideData) {
                      await this.createLessonGoogleSlide()
                    } else if (redesignIdea && this.item.lessonTemplateType) {
                      // 如果是 redesign 课程，且添加了 Idea 并存在课程模板，则调接口生成 Google Slide 数据
                      await this.generateLessonGoogleSlide()
                    } else {
                      // 清空 lessonTemplateType
                      this.$set(this.item, 'lessonTemplateType', null)
                    }
                    this.$set(this.item, 'generateLessonDetailLoading', false)
                    if (callback && typeof callback === 'function') {
                        callback()
                    }
                })
                .catch(error => {
                    // 如果不是批量生成课程，那么就是单个生成来到的这里，则设置 singleGenerate 为 false
                    if (!this.batchId) {
                      this.$store.dispatch('unit/setSingleGenerate', false)
                    }
                    // 生成出错
                    this.generateLessonDetailLoading = false
                    this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                    if (callback && typeof callback === 'function') {
                        callback()
                    }
                })
                this.$bus.$emit('unitClearResourceHistory', this.handleLessonGenerated)
        },

        // 生成课程 google slide 数据
        async generateLessonGoogleSlide () {
            // 参数
            let params = {
                unitId: this.unit.id,
                itemId: this.item.id,
                lessonId: this.lesson.id
            }
            this.item.lessonGoogleSlideData = ''
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                this.item.lessonGoogleSlideData += message.data
                // 解析 Google Slide 数据
                this.googleSlideData = this.parseGoogleSlideData(this.item.lessonGoogleSlideData, this.item.lessonTemplateType)
            }
            // 生成课程 google slide 数据
            createEventSource($api.urls().generateLessonTemplateStream, null, messageCallback, 'POST', params)
            .then(() => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                // 创建 google slide 数据
                this.createLessonGoogleSlide()
            })
        },

        // 创建 google slide 数据
        async createLessonGoogleSlide () {
          // 如果存在 Google Slide 数据，则存储起来
          if (this.googleSlideData) {
            let params = {
              type: this.item.lessonTemplateType,
              lessonId: this.item.lessonId,
              lessonName: this.lesson.title,
              ageGroup: this.lesson.ageGroup,
              lessonTemplates: this.googleSlideData,
              generateSlide: false
            }
            // 如果是 bookakucha 课程模板，但没有生成书，则提示用户生成失败，并把课程模板设置为原始课程模板
            if (this.googleSlideData.type == 'BOOKA_KUCHA' && this.googleSlideData.bookName == 'None') {
              this.$set(this.lesson, 'lessonTemplate', this.originalLessonTemplate)
              this.$message.warning(this.$t('loc.eduprotocols21'))
              return
            }
            // 调接口保存 Google Slide 数据
            await this.$axios.post($api.urls().createLessonGoogleSlide, params)
            .then(res => {
              // 回显 Google Slide 数据
              this.$set(this.lesson, 'lessonTemplate', res)
              // 提示生成成功
              this.$message.success(this.$t('loc.eduprotocols10'))
              // 更新课程
              this.createLesson()
            })
          }
        },

        // 获取解析 keys
        getParseKeys (hasRedesignIdea) {
          // 默认的解析 keys
          let parseKeys = [
              { key: 'title', name: 'Lesson Title' },
              { key: 'ageGroup', name: 'Age Group' },
              { key: 'prepareTime', name: 'Preparation Time' },
              { key: 'activityTime', name: 'Activity Duration' },
              { key: 'measuresString', name: 'Measures' },
              { key: 'objectives', name: ['Objectives', 'Objective'] },
              { key: 'materials', name: 'Materials' },
              { key: 'keyVocabularyWords', name: ['Key Vocabulary Words', 'Key Vocabulary Words & Child-Friendly Definitions', '- Key Vocabulary Words:'] },
              { key: 'implementationSteps', name: 'Implementation Steps' },
              { key: 'note', name: 'Note' }
          ]
          // 如果是 Center 课程，则需要更改解析 keys, 去除 keyVocabularyWords
          if (this.isCenterLesson) {
              parseKeys = [
                  { key: 'title', name: 'Lesson Title' },
                  { key: 'ageGroup', name: 'Age Group' },
                  { key: 'prepareTime', name: ['Preparation Time', 'Activity Preparation Time'] },
                  { key: 'activityTime', name: 'Activity Duration' },
                  { key: 'measuresString', name: ['Measures', 'DRDP Measures'] },
                  { key: 'objectives', name: ['Objectives', 'Objective'] },
                  { key: 'materials', name: 'Materials' },
                  { key: 'implementationSteps', name: 'Implementation Steps' },
                  { key: 'note', name: 'Note' }
              ]
          }
          // 如果使用了课程模板，则需要更改解析 keys，添加 module1 和 module2
          if (this.item.lessonTemplateType && !hasRedesignIdea) {
            parseKeys = [
              { key: 'moudle1', name: 'Module 1' },
              { key: 'moudle2', name: 'Module 2' },
              { key: 'title', name: 'Lesson Title' },
              { key: 'ageGroup', name: 'Age Group' },
              { key: 'prepareTime', name: 'Preparation Time' },
              { key: 'activityTime', name: 'Activity Duration' },
              { key: 'measuresString', name: 'Measures' },
              { key: 'objectives', name: ['Objectives', 'Objective'] },
              { key: 'materials', name: 'Materials' },
              { key: 'keyVocabularyWords', name: ['Key Vocabulary Words', 'Key Vocabulary Words & Child-Friendly Definitions', '- Key Vocabulary Words:'] },
              { key: 'implementationSteps', name: 'Implementation Steps' },
              { key: 'note', name: 'Note' }
          ]
          }
          return parseKeys
        },

        // 解析 Google Slide 数据
        parseGoogleSlideData (data, type) {
          let parseKeys = []
          let parsedData = {}
          switch (type.toUpperCase()) {
            case 'FRAYER_MODEL':
              // 解析 Google Slide 数据
              parseKeys = [
                { key: 'centralTerm', name: ['Central Term:', 'Central Idea'] },
                { key: 'dimension1', name: ['- Dimension 1:', '- Dimension 1', 'Dimension 1:', 'Dimension 1'] },
                { key: 'dimension2', name: ['- Dimension 2:', '- Dimension 2', 'Dimension 2:', 'Dimension 2'] },
                { key: 'dimension3', name: ['- Dimension 3:', '- Dimension 3', 'Dimension 3:', 'Dimension 3'] },
                { key: 'dimension4', name: ['- Dimension 4:', '- Dimension 4', 'Dimension 4:', 'Dimension 4'] }
              ]
              parsedData = parseStreamData(data, parseKeys)
              parsedData = {
                'frayerList': parsedData.map(parseModel => {
                  return {
                      centralTerm: parseModel.centralTerm, // 设置 centralTerm
                      dimension: [
                          parseModel.dimension1,
                          parseModel.dimension2,
                          parseModel.dimension3,
                          parseModel.dimension4
                      ] // 转换四个维度字段为 List<String>
                    }
                })
              }
              break
            case 'SKETCH_AND_TELL':
              parseKeys = [
                { key: 'keyword', name: ['Central Concept:', 'Central Concept'] }
              ]
              parsedData = parseStreamData(data, parseKeys)
              parsedData = { 'keywords': parsedData.map(x => x.keyword) }
              break
            case 'SKETCH_AND_TELL_O':
              parseKeys = [
                { key: 'centralTopic', name: ['Central Topic:', 'Central Topic'] },
                { key: 'task', name: ['Task:', 'Task'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              parsedData.circleCount = this.getSketchAndTellOCircleCount()
              break
            case 'BOOKA_KUCHA':
              parseKeys = [
                { key: 'bookName', name: ['Book name:', 'Book name'] },
                { key: 'theme', name: ['BookaKucha theme:', 'BookaKucha theme'] },
                { key: 'instruction', name: ['Instruction:', 'Instruction'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              break
            case 'THIN_SLIDE':
              parseKeys = [
                { key: 'keyTerm', name: ['Key Term:', 'Key Term'] }
              ]
              parsedData = parseStreamData(data, parseKeys)
              parsedData = { 'keyTerms': parsedData.map(x => x.keyTerm) }
              break
            case 'THIN_SLIDES_VARIATIONS':
              parseKeys = [
                { key: 'answer', name: ['Make a Thin Slide answering:', 'Make a Thin Slide answering'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              break
            case 'WICKED_HYDRA':
              parseKeys = [
                { key: 'templateContent', name: ['Template Content:', 'Template Content'] }
              ]
              parsedData = parseStreamData(data, parseKeys)[0]
              break
          }
          parsedData.type = type
          parsedData.lessonName = this.item.title
          return parsedData
        },
        getSketchAndTellOCircleCount () {
          // 获取当前年龄组
          let ageGroup = this.lesson.ageGroup
          let ageValue = tools.getAgeValue(ageGroup)
          // 1. 从 K 到 Grade 2 是 4 个圈
          if (ageValue >= 6 && ageValue < 9) {
            return 4
          }
          // 2. 从 Grade 3 到 Grade 6 是 6 个圈
          if (ageValue >= 9 && ageValue < 12) {
            return 6
          }
          // 3. Grade 7 及以上均为 8 个圈
          if (ageValue >= 12) {
            return 8
          }
        },
        // 更新周计划项基础信息
        async upItemBaseInfo () {
          const param = {
            items: [this.item]
          }
          try {
            await LessonApi.updatePlanItemsInfo(param)
          } catch (error) {
            // 处理错误
          }
        },

        // 异步调用到实施步骤资源组件
        generateImplementationStepsSourceLessonDetail (isLessonImplStep = false) {
          // 判断当前模块是否生成
          if(!this.getLightAdaptModuleGenerate('resourceUpgradeFlag')) {
            return
          }
          // 设置已有资源为
          this.$refs.lessonDetailRef.generateImplementationStepsSource(this.lesson.implementationSteps, this.lesson.id, isLessonImplStep)
        },

        // 生成课程封面
        generateLessonCover (prompt) {
            this.$set(this.lesson, 'generateLessonCoverLoading', true)
            // 请求参数
            let params = {
                prompt: prompt,
                lesson: {
                    ...this.lesson,
                    name: this.item.title
                }
            }
            this.$axios.post(serverlessApiUrl + $api.urls().generateLessonCover, params).then((res) => {
                if (this.leavedPage) {
                    return
                }
                if (this.isCenterLesson) {
                    this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
                    this.startRedesignLessonGuide()
                }
                let cover = {
                    id: res.id,
                    url: res.public_url
                }
                if (params.lesson.id == this.lesson.id) {
                    this.$set(this.lesson, 'cover', cover)
                    this.$nextTick(() => {
                        this.$set(this.lesson, 'cover', cover)
                        this.$refs.lessonDetailRef.updateLessonCover(cover)
                        this.$set(this.lesson, 'generateLessonCoverLoading', false)
                    })
                } else {
                    // this.$set(this.item, 'lesson', {
                    //     ...this.item.lesson,
                    //     cover: cover
                    // })
                    // 更新周计划中的课程封面
                    this.updateWeeklyPlansItemCover(params.lesson.id, cover)
                    this.$set(this.item, 'generateLessonCoverLoading', false)
                }
                // 保存课程
                this.createLesson()
                // 是否更新了封面
                this.isUpdateCover = true
            }).catch(error => {
                this.$set(this.lesson, 'generateLessonCoverLoading', false)
                this.$message.error(this.$t('loc.unitPlannerStep1CoverImageGenerationUnsuccessful'))
            })
        },

        // 提取并搜索课程封面
        extractAndSearchLessonCover () {
          let params = {
            itemId: this.item.id,
            unitId: this.unit.id,
            lessonId: this.lesson.id
          }
          this.$set(this.lesson, 'generateLessonCoverLoading', true)
          this.$axios.post($api.urls().extractAndSearchLessonCover, params)
          .then(res => {
            let cover = {
              id: res.id,
              url: res.public_url,
              source: res.source
            }
            this.$set(this.item, 'coverKeywords', res.keywords)
            this.$set(this.lesson, 'cover', cover)
            this.$refs.lessonDetailRef.updateLessonCover(cover)
            this.$set(this.lesson, 'generateLessonCoverLoading', false)
          })
          .catch(error => {
            this.$set(this.lesson, 'generateLessonCoverLoading', false)
          })
        },

        // 设置单元封面
        setLessonCoverInfo (cover) {
          let params = {
            keywords: this.item.coverKeywords || this.lesson.coverKeywords,
            lessonId: this.lesson.id,
            source: cover.source,
            url: cover.url
          }
          this.$set(this.lesson, 'generateLessonCoverLoading', true)
          this.$axios.post($api.urls().setCoverInfo, params)
          .then(res => {
            let cover = res
            this.$set(this.lesson, 'cover', cover)
            this.$refs.lessonDetailRef.updateLessonCover(cover)
            this.$set(this.lesson, 'generateLessonCoverLoading', false)
          })
          .catch(error => {
            this.$set(this.lesson, 'generateLessonCoverLoading', false)
          })
        },

        // 解析媒体上传参数
        parseMediaUploaderParam (params) {
          let file = params.file
          let currentLesson = params.currentLesson
          if (file && file.url) {
            // 更新课程
            const cover = {
              id: file.id,
              url: file.url
            }
            // 如果存在课程 ID，则更新
            if (currentLesson && currentLesson.id) {
              // 如果是当前的课程，则更新封面
              if (this.lesson && currentLesson.id === this.lesson.id) {
                // 如果页面没有切换，则更新自己
                this.$set(this.lesson, 'cover', cover)
              }
              // 如果页面切换了，则更新周计划中的课程封面
              this.updateWeeklyPlansItemCover(currentLesson.id, cover)
            }
          }
        },
        // 更新 weeklyPlans 中 item 里面的课程的封面
        updateWeeklyPlansItemCover (lessonId, cover) {
            // 获取要更新的课程
            let needUpdateLesson = null
            // 遍历 weeklyPlans
            this.weeklyPlans.forEach(weeklyPlan => {
                // 遍历 weeklyPlan 中的 items
                weeklyPlan.items.forEach(item => {
                    // 如果 item 的 lessonId 和 lessonId 相等，则更新封面
                    if (item.lessonId === lessonId) {
                        // 更新封面
                        item.lesson.cover = cover
                        // 更新课程
                        needUpdateLesson = {
                          lesson: item.lesson,
                          item: item
                        }
                    }
                })
            })
            //   更新数据
            if (needUpdateLesson) {
                this.updateLesson(false, false, false, needUpdateLesson, null)
            }
        },
        // 组装创建课程的数据
        collectData (needUpdateLesson) {
            let item = this.item
            let lesson = JSON.parse(JSON.stringify(this.lesson))
            if (needUpdateLesson) {
              item = needUpdateLesson.item
              lesson = needUpdateLesson.lesson
            }
            if (!lesson) {
              return
            }
            let culturallyResponsiveInstruction
            // 处理 CLR 资源的数据
            if (lesson.lessonClrAndSources) {
              lesson.lessonClrAndSources.sources = lesson.lessonClrAndSources && lesson.lessonClrAndSources.sources && lesson.lessonClrAndSources.sources.filter(source => !source.hidden)
              // 资源列表如果不为空的话，则遍历资源列表，将隐藏的资源删除
              if (lesson.lessonClrAndSources.sources) {
                // 遍历资源列表
                lesson.lessonClrAndSources.sources.forEach(source => {
                  // 获取角标
                  let subscript = source.subscript
                  // 拼接需要搜索的文本
                  const searchTxt = `<a href="#subscript${subscript}" class="text-primary">[${subscript}]</a>`
                  // 如果该资源隐藏了，则保存的时候直接删除即可
                  if (source.hidden) {
                    lesson.lessonClrAndSources.clr.replace(new RegExp(searchTxt, 'g'), '')
                  }
                })
              }
              // 如果 CLR 存在，则将更新的 CLR 的值更新一下
              if (lesson.lessonClrAndSources.clr) {
                culturallyResponsiveInstruction = lesson.lessonClrAndSources.clr.replace(/<br>/g, '\n')
              }
            }

            // 实施步骤资源数据处理
            this.impStepSourceHandle(lesson)

            // 过滤掉题干和答案都为空的 question，并按题号重新排序
            if ((this.isK12Grade || this.isExtendedAgeGroup) && lesson.questions && lesson.questions.length !== 0) {
              lesson.questions = lesson.questions
                .filter(question => (question.question && question.question.trim() !== '') || (question.answer && question.answer.trim() !== ''))
                .sort((a, b) => a.sortIndex - b.sortIndex)
              // 重新设置题号
              lesson.questions.forEach((question, index) => {
                question.sortIndex = index + 1
              })
            }

          // 转换材料数据
          const materialFiles = JSON.parse(JSON.stringify(lesson.materialFiles))
          if (materialFiles) {
            // 转换 description 到 descriptions
            materialFiles.descriptions = [materialFiles.description]
            if (materialFiles.attachmentMedias) {
              // 提取 attachmentMedias 的 id
              materialFiles.attachmentMediaIds = materialFiles.attachmentMedias.map(media => media.id)
            }
            if (materialFiles.media) {
              // 提取 media 的 id
              materialFiles.mediaId = materialFiles.media.id
              materialFiles.externalMediaUrlId = materialFiles.media.externalMediaUrlId
            }
            // 移除无用数据
            materialFiles.media = null
          }

          // 如果需要分享到 magic 需要发送请求
          if (this.sharedMagic) {
            let param = {
              'unitId': this.unit.id,
              'planId': [],
              'fileType': 'docx',
              'shardedToMagic': true,
              'project': platform
            }
            this.$axios.post($api.urls().createBatchGenerateTask, param)
          }

          return {
              id: lesson.id,
              planItemId: item.id,
              unitId: this.unit.id, // 单元 ID
              unitFinished: this.allGenerated, // 单元是否完成
              sharedToMagic: this.sharedMagic, // 是否共享到 magic
              name: item.title,
              ageGroup: lesson.ageGroup,
              ageGroupNames: lesson.ageGroup,
              prepareTime: lesson.prepareTime,
              activityTime: lesson.activityTime,
              activityType: item.activityType || null,
              activityTheme: item.centerGroupName || null,
              measures: lesson.measures,
              objectives: lesson.objectives,
              coverMediaIds: lesson.cover && [lesson.cover.id] || [],
              materials: materialFiles,
              learnerProfiles: lesson.learnerProfiles,
              classroomType: lesson.classroomType || 'IN_PERSON',
              steps: [{
                ageGroupName: lesson.ageGroup,
                ageGroupValue: lesson.ageGroup,
                content: lesson.implementationSteps,
                universalDesignForLearning: lesson.universalDesignForLearning,
                culturallyResponsiveInstruction: culturallyResponsiveInstruction || lesson.culturallyResponsiveInstruction,
                universalDesignForLearningGroup: lesson.universalDesignForLearningGroup,
                culturallyResponsiveInstructionGroup: lesson.culturallyResponsiveInstructionGroup,
                udlGroups: lesson.udlGroups,
                groupId: lesson.groupId,
                homeActivity: lesson.homeActivity, // 家庭活动
                lessonStepGuides: lesson.typicalBehaviors,
                lessonClrAndSources: lesson.lessonClrAndSources,
                questions: lesson.questions,
                lessonImpStepAndSource: lesson.lessonImpStepAndSource, // 实施步骤资源
                teachingTips: lesson.teachingTips && lesson.teachingTips.map(tip => {
                  return {
                    measureId: tip.measureId,
                    measureAbbreviation: tip.measureAbbreviation,
                    measureName: tip.measureName,
                    teachingTips: tip.teachingTips
                  }
                }),
                lessonTemplate: lesson.lessonTemplate,
                adaptedModuleSwitch: this.unit.adaptedModuleSwitch
              }],
              keyVocabularyWords: lesson.keyVocabularyWords,
              universalDesignForLearning: lesson.universalDesignForLearning,
              culturallyResponsiveInstruction: lesson.culturallyResponsiveInstruction,
              homeActivities: lesson.homeActivity, // 家庭活动
              frameworkId: JSON.parse(JSON.stringify(this.unit.baseInfo)).frameworkId,
              day: lesson.day,
              themeIds: [],
              confirmed: lesson.confirmed // 课程是否已确认
            }
        },

        // 判断实施步骤资源是否有隐藏的，有的话进行删除；资源不存在进行初始化
        impStepSourceHandle (lesson) {
          // center 类型课程不生成实施步骤资源
          if (this.isCenterLesson) {
            return
          }
          if (lesson.lessonImpStepAndSource) {
            lesson.lessonImpStepAndSource.impStepContent = lesson.implementationSteps
            // 资源列表如果不为空的话，则遍历资源列表，将隐藏的资源删除
            if (lesson.lessonImpStepAndSource.sources && lesson.lessonImpStepAndSource.sources.length > 0) {
              // 遍历资源列表
              lesson.lessonImpStepAndSource.sources.forEach(source => {
                // 获取角标
                let subscript = source.subscript
                // 构建正则表达式，匹配 <imp-script></imp-script> 标签及其内容
                const regex = new RegExp(`<imp-script[^>]*>.*?\\[${subscript}\\].*?</imp-script>`, 'gs')
                // 如果该资源隐藏了，则保存的时候直接删除即可
                if (source.hidden) {
                  lesson.lessonImpStepAndSource.impStepContent = lesson.lessonImpStepAndSource.impStepContent.replace(regex, '')
                }
              })
              // 判断后的数据给 content
              lesson.implementationSteps = lesson.lessonImpStepAndSource.impStepContent
              // 保留不隐藏的数据
              lesson.lessonImpStepAndSource.sources = lesson.lessonImpStepAndSource &&
                lesson.lessonImpStepAndSource.sources &&
                lesson.lessonImpStepAndSource.sources.filter(source => !source.hidden)
            }
          } else {
            // 提取所有被 [数字] 包裹的内容
            const subscriptRegex = /\[(\d+)\]/g

            // 找到所有匹配的数字
            let matches = lesson.implementationSteps ? [...lesson.implementationSteps.matchAll(subscriptRegex)] : []

            // 遍历匹配结果
            matches.forEach((match) => {
              let subscript = match[1] // 提取数字部分

              // 构建用于匹配 <imp-script> 的正则表达式
              const impScriptRegex = new RegExp(`<imp-script[^>]*>.*?\\[${subscript}\\].*?</imp-script>`, 'gs')

              // 替换匹配的 <imp-script> 标签内容为空
              lesson.implementationSteps = lesson.implementationSteps.replace(impScriptRegex, '')
            })
          }
        },

        // 校验课程信息
        async validateLessonInfo () {
            try {
                let result = await this.$refs.lessonDetailRef.validateLessonInfo()
                return result
            } catch (error) {
                return false
            }
        },

        // 保存课程
        async createLesson (generateBehavior, needRecordVersion = false) {
            // 如果是 CG 项目，则和 Center一样，不在此处生成典型行为后面的数据
            // if (this.isCG) {
            //     generateBehavior = false
            // }
            // 如果存在课程 ID，则更新
            if (this.lesson && this.lesson.id) {
                this.weeklyPlans.forEach(weeklyPlan => {
                  let items = this.isCenterLesson ? weeklyPlan.centerItems : weeklyPlan.items
                  let item = items.find(item => equalsIgnoreCase(item.id, this.item.id))
                  if (item) {
                    this.$set(item, 'lessonId', this.lesson.id)
                    this.$set(item, 'lesson', this.lesson)
                  }
                })
                // 如果 lesson 的 ID 不存在，则重新设置课程 ID
                if (this.lesson && !this.item.lessonId) {
                  this.$set(this.item, 'lessonId', this.lesson.id)
                }
                await this.updateLesson(false, generateBehavior, null, null, null, needRecordVersion)
                return
            }
            let params = this.collectData()
            // 判空
            if (!params) {
              return
            }
            await this.$axios.post($api.urls().createLesson, params).then((res) => {
                this.$set(this.lesson, 'id', res.id)
                this.$set(this.item, 'lessonId', res.id)
                if (generateBehavior) {
                  // 标记为第一次发布
                  this.isFirstPublish = true
                    this.generateLessonDetailStepTwo(res.id)
                } else {
                  this.singleGenerateLessonSetLessonId(res.id)
                  this.startRedesignLessonGuide()
                }
                // 更新课程成功事件
                this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
            }).catch(error => {
                this.$message.error(error.response.data.error_message)
            })
        },
        // 单个生成课程时设置课程的 lessonId
        singleGenerateLessonSetLessonId (lessonId) {
          this.weeklyPlans = this.weeklyPlans.map(weeklyPlan => {
            weeklyPlan = weeklyPlan.items.map(item => {
              if (equalsIgnoreCase(item.id, this.item.id)) {
                item.lessonId = lessonId
              }
            })
            weeklyPlan = weeklyPlan.centerItems && weeklyPlan.centerItems.map(item => {
              if (equalsIgnoreCase(item.id, this.item.id)) {
                item.lessonId = lessonId
              }
            })
            return weeklyPlan
          })
        },

        // 更新活动时间
        updateActivityTime (value) {
          // 接收到子组件的修改后更新父组件的 Activity
          this.$set(this.regenerateLesson,'activityTime',value)
        },

        // clr 资源生成状态
        upNormalClrLoading (val) {
          this.generateCulturallyResponsiveInstructionLoading = val
        },

        // 保存课程附属信息,延时 5s 保存，避免同时生成完成后频繁保存
        saveLessonSupplenments: tools.debounce(function (needRecordVersion = false) {
            this.createLesson(null, needRecordVersion)
        }, 5000),

        // 保存课程
        saveLesson (needRecordVersion = false) {
            if (equalsIgnoreCase(this.unit.adaptedType, 'LIGHT')) {
              this.$analytics.sendEvent('cg_adapt_lesson_detail_save_clicked')
            }
            if (this.isCenterLesson) {
                this.$analytics.sendEvent('web_unit_create3_center_det_save')
                // 更新课程成功事件
                this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
            } else {
                if (this.lesson.lessonClrAndSources) {
                  // 获取所有的非隐藏的资源列表
                  const sources = this.lesson.lessonClrAndSources.sources.filter(source => !source.hidden)
                  // 如果 CLR 资源内容与资源列表为空时，则需要展示 CLR 内容
                  if (!this.lesson.lessonClrAndSources.clr && sources.length === 0) {
                    this.$refs.lessonDetailRef.showSource = false
                  }
                }
                this.$analytics.sendEvent('web_unit_create3_group_det_save')
                // 更新课程成功事件
                this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
            }
            this.updateLesson(false, false, true, null, null, needRecordVersion)
        },
        // 更新课程
        async updateLesson (next, generateBehavior, showSaveTip, needUpdateLesson, groupId, needRecordVersion, additionalParams) {
            if (this.$refs.lessonDetailRef && !await this.validateLessonInfo() && !this.loading) {
                this.$message.error('Please fill in the required fields.')
                return
            }
            if ((!this.lesson || !this.item) && !needUpdateLesson) {
                return
            }
            this.objectivesTemp = []
            if (this.lesson && this.lesson.formalDescription && this.lesson.studentFriendly) {
              this.objectivesTemp.push(this.lesson.formalDescription)
              this.objectivesTemp.push(this.lesson.studentFriendly)
            } else {
              this.objectivesTemp = []
            }
            this.updateLoading = true
            let params = this.collectData()
            if (needUpdateLesson) {
              params = this.collectData(needUpdateLesson)
            }
            // 是否需要记录版本
            if (needRecordVersion || this.isUpdateCover || this.needRecordVersion) {
              this.isUpdateCover = false
              this.needRecordVersion = false
              if (this.isEnhanceLesson) {
                // 是否是增强课程
                this.isEnhanceLesson = false
                params.enhanceLesson = true
                params.needRecordVersion = true
              } else if (this.isFirstPublish) {
                // 是否首次发布课程
                this.isFirstPublish = false
                params.firstPublish = true
                params.needRecordVersion = true
              } else {
                // 生成课程变化描述
                const changeDescription = LessonUtils.generateLessonChangeDescription(this.lastSavedLesson, this.lesson)
                params.needRecordVersion = !!changeDescription
                params.description = changeDescription
              }
            }

            // 添加额外参数
            if (additionalParams) {
                Object.assign(params, additionalParams);
            }
            // 如果 objectivesTemp 不为空，则将 objectivesTemp 赋值给 objectives
            if (this.objectivesTemp.length > 0) {
              params.objectivesList = this.objectivesTemp
            }

            await this.$axios.post($api.urls().updateLesson, params).then((res) => {
                if (showSaveTip) {
                    this.$message.success(this.$t('loc.unitPlannerStep3SavedSuccessfully'))
                }
                this.updateLoading = false
                // 更新成功后，生成典型行为， center 课程不生成典型行为、UDL、CLR
                if (generateBehavior && !this.isCenterLesson) {
                    this.generateLessonDetailStepTwo(groupId, generateBehavior)
                } else {
                    this.startRedesignLessonGuide()
                }
                // 更新成功后，执行下一步
                if (next) {
                    this.next()
                }

                // 保存当前课程数据，用于后续比较是否有未保存的更改
                this.lastSavedLesson = JSON.parse(JSON.stringify(this.lesson))

                // magic 提交并发布成功 直接跳转 unit 列表页
                if (params.sharedToMagic && this.isMC) {
                  this.$store.dispatch('magicCurriculum/checkCurrentUserToMagic')
                  this.editUnit = {
                    id: this.$route.params.unitId
                  }
                  this.magicSuccessVisible = true
                }
            }).catch(error => {
                this.updateLoading = false
                this.$message.error(error.response.data.error_message)
            })
        },
        // 关闭发布成功弹窗回调
        successClose(){
          this.$router.push({
            path: '/curriculum-genie/unit-planner'
          })
        },

        // 生成课程幻灯片
        async generateLessonSlides() {
          // 判断当前模块是否生成
          if(!this.getLightAdaptModuleGenerate('lectureSlidesFlag')) {
            return
          }
          this.$refs.lessonDetailRef && this.$refs.lessonDetailRef.generateLessonSlides()
        },
      
        // 获取轻量导入改编时当前模块是否生成
        getLightAdaptModuleGenerate(module) {
          // 默认生成
          if(!this.unit || !this.unit.adaptedModuleSwitch || !module) {
            return true
          }
          return this.unit.adaptedModuleSwitch[module]
        },

        // 生成课程其余内容
        async generateLessonDetailStepTwo (lessonId, generateBehavior) {

            this.generateLessonDetailLoading = true
            try {
                await Promise.all([
                    this.isK12Grade || this.lectureSlidesExpandGrade ? this.generateLessonSlides() : Promise.resolve(), // 生成课程幻灯片
                    this.generateTeachingTips(), // 标准教学指导
                    this.generateLessonLearnerProfileStream(), // 生成毕业生核心素养画像
                    this.generateUniversalDesignForLearningStream(), // 生成 UDL
                    this.item.adaptedLesson ? this.$refs.lessonDetailRef.$refs.culturallyLinguisticallyResponsive.generateCLR(true) : this.generateCulturallyResponsiveInstructionStream(null, generateBehavior), // 生成文化教学内容
                    // k12 年龄端生成 quiz,否则生成典型行为
                    (this.isK12Grade && !this.isExtendedAgeGroup) ? this.generateLessonQuizStream(false, {}) :
                    this.isExtendedAgeGroup ? Promise.all([this.generateLessonQuizStream(false, {}),
                        frameworkUtils.isCAPTKLF(this.unitInfo.frameworkId) || frameworkUtils.isMELS(this.unitInfo.frameworkId) || frameworkUtils.isILEARN(this.unitInfo.frameworkId) ?
                        this.generateTypicalBehaviorsStream(null, true, true) : this.generateTypicalBehaviorsStream()]) :
                    frameworkUtils.isCAPTKLF(this.unitInfo.frameworkId) || frameworkUtils.isMELS(this.unitInfo.frameworkId) || frameworkUtils.isILEARN(this.unitInfo.frameworkId) ? this.generateTypicalBehaviorsStream(null, true, true) : this.generateTypicalBehaviorsStream(),
                    // 生成家庭活动
                    this.generateHomeActivityStream(true)
                ])
                this.singleGenerateLessonSetLessonId(lessonId)
                this.$set(this.item, 'processing', false)
                this.weeklyPlans.forEach(weeklyPlan => {
                  let items = this.isCenterLesson ? weeklyPlan.centerItems : weeklyPlan.items
                  let item = items.find(item => equalsIgnoreCase(item.id, this.item.id))
                  if (item) {
                    this.$set(item, 'lessonId', this.lesson.id)
                    this.$set(item, 'lesson', this.lesson)
                  }
                })
            } finally {
                // 单个课程生成结束之后，将单个生成课程的状态设置为 false
                this.$store.dispatch('unit/setSingleGenerate', false)
                this.$set(this.item, 'processing', false)
                // 生成完成后调用 updateLesson 保存课程和版本
                await this.updateLesson(null, null, null, null, null, true)
                this.generateLessonDetailLoading = false
                this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
                this.startRedesignLessonGuide()
                // 保存一份课程数据作为备份
                this.lessonCopy = JSON.parse(JSON.stringify(this.collectData()))
            }
        },

        // 更新周计划项
        updateCurrentItemMeasures (newMeasures) {
            if (!newMeasures || newMeasures.length === 0 || !this.item) {
                return
            }
            // 课程所在周计划
            let weeklyPlan = this.getWeeklyPlan(this.item)
            // 更新测评点
            this.$set(this.item, 'measures', newMeasures)
            // 更新本周测评点
            this.getWeeklyPlanAllMeasure(weeklyPlan, this.item.planId)
            // 参数
            let params = {
                items: [this.item],
                frameworkId: this.baseInfo.frameworkId,
                planId: weeklyPlan.id,
                unitId: this.unit.id // 单元 ID
            }
            // 重新判断当前 Unit 的生成进度
            params.progress = this.reJudgeUnitGenerateProgress()
            return new Promise((resolve, reject) => {
                this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
                    resolve()
                }).catch(error => {
                    this.$message.error(error.response.data.error_message)
                    reject(error)
                })
            })
        },
        // 更新本周测评点
        getWeeklyPlanAllMeasure (weeklyPlan, planId) {
          if (!weeklyPlan || !weeklyPlan.items || !planId) {
            return
          }
          // 收集 weeklyPlan.items 和 weeklyPlan.centerItems 中所有的 measures 的 abbreviation 到 Set 中
          const allPlanMeasureAbb = new Set(
            [
              ...weeklyPlan.items.flatMap(item => item.measures),
              ...weeklyPlan.centerItems.flatMap(item => item.measures)
            ]
          )

          // 查询出所有测评点
          let allMeasure = []
          frameworkUtils.getMeasuresBottom(this.domains, allMeasure)
          // 过滤出当前选择的测评点的 Id
          const allPlanMeasureIds = allMeasure
            .filter(measure => allPlanMeasureAbb.has(measure.abbreviation))
            .map(measure => measure.id)
          // 保存新选择的测评点数据
          const params = {
            upWeeklyMeasureRequestList: [{
              planId: planId,
              measureIds: allPlanMeasureIds
            }]
          }
          LessonApi.updateWeeklyMeasureIds(params).then()
        },
        // 重新判断当前 Unit 的生成进度
        reJudgeUnitGenerateProgress () {
          // 是否存在未确定的 Item
          let hasUnconfirmedItem = false
          // 是否存在未生成的 Item 的周次
          let hasUnGenerateItem = false
          // Unit 生成进度
          let progress = ''
          // 遍历所有周计划，如果有未确认的课程，则更新进度为 60%
          for (let i = 0; i < this.weeklyPlans.length; i++) {
            // 获取当前周计划
            let weeklyPlan = this.weeklyPlans[i]
            // 判断是否存在周次未生成大小组
            if (!weeklyPlan.items || weeklyPlan.items.length === 0) {
              hasUnGenerateItem = true
              break
            }
            // 如果周计划项存在，则判断是否有未确认的课程
            if (weeklyPlan.items && weeklyPlan.items.length > 0) {
              if (weeklyPlan.items.some(item => !item.confirmed)) {
                hasUnconfirmedItem = true
                break
              }
            }
            // 如果 centers 组周计划项存在，则判断是否有未确认的课程
            if (weeklyPlan.centerItems && weeklyPlan.centerItems.length > 0) {
              if (weeklyPlan.centerItems.some(item => !item.confirmed)) {
                hasUnconfirmedItem = true
                break
              }
            }
          }
          // 如果存在未确认的课程或未生成大小组的周次，则更新进度为 60%，否则不进行更新操作
          if (!this.baseInfo.progress || this.baseInfo.progress < 60 || hasUnconfirmedItem || hasUnGenerateItem) {
            progress = this.progressConstant.sixtyPercent
          } else if (!hasUnconfirmedItem) { // 如果不存在未确定的 Item，则更新进度为 80%
            progress = this.progressConstant.eightyPercent
          }
          return progress
        },

        // 获取课程项所属周计划
        getWeeklyPlan (item) {
            if (!item || !this.weeklyPlans || this.weeklyPlans.length === 0) {
                return
            }
            // 遍历周计划
            let weeklyPlan = null
            this.weeklyPlans.forEach(plan => {
                // 创建变量存储 items
                let items = plan.items
                // 如果是 Center 课程，则使用 centerItems
                if (this.isCenterLesson) {
                  items = plan.centerItems
                }
                // 遍历周计划项
                items.forEach(planItem => {
                    // 如果周计划项 ID 与课程项 ID 相同，则返回周计划
                    if (equalsIgnoreCase(planItem.id, item.id)) {
                        weeklyPlan = plan
                    }
                })
            })
            return weeklyPlan
        },

        // 隐藏重新生成课程引导
        hideRedignGuide (startAdapt) {
          this.showRedesignLessonGuide = false
          // 如果开始重新改编课程，打开弹窗
          if (startAdapt) {
            this.openRedesignLessonDialog()
          }
          this.guideFeatures.showRedesignLessonGuide = false
          let requestParam = { 'features': ['REDESIGN_LESSON_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, requestParam).then(() => {
            this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
            // 发送事件通知资源引导显示
            this.$bus.$emit('show-resources-guide')
          })
        },
          // 显示重新生成课程引导
      startRedesignLessonGuide() {
        if (!this.guideFeatures) {
          this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
            if (result.showRedesignLessonGuide) {
              this.setShowRedesignLessonGuide()
            } else {
              this.$bus.$emit('showEduProtocolsTemplateCustomGuide', this.lesson.id)
            }
          })
        }
        // 如果按钮当前处于禁用状态，则设置一个标记，等待按钮可用时再显示
        if (this.guideFeatures && this.guideFeatures.showRedesignLessonGuide) {
          this.setShowRedesignLessonGuide()
        } else {
          this.$bus.$emit('showEduProtocolsTemplateCustomGuide', this.lesson.id)
        }
      },
      setShowRedesignLessonGuide() {
        if (this.generateLessonDetailLoading || this.loading || this.updateLoading) {
          // 设置一个延迟检查，等待按钮可用时再显示引导
          this.$nextTick(() => {
            const checkButtonState = () => {
              if (!(this.generateLessonDetailLoading || this.loading || this.updateLoading)) {
                // 按钮可用，显示引导
                // if (!this.showUnitCompletedDialog) {
                  this.showRedesignLessonGuide = true;
                // }
              } else {
                // 按钮仍不可用，继续等待
                setTimeout(checkButtonState, 500);
              }
            };
            // 开始检查
            setTimeout(checkButtonState, 500);
          });
        } else {
          // 按钮当前可用，直接显示引导
          // if (!this.showUnitCompletedDialog) {
                this.showRedesignLessonGuide = true;
            // }
        }
      },

        // 打开重新生成课程详情
        openRedesignLessonDialog () {
          if (equalsIgnoreCase(this.unit.adaptedType, 'LIGHT')) {
              this.$analytics.sendEvent('cg_adapt_enhance_lesson_clicked')
          }
          this.regenerateLesson = {
            measures: this.lesson.measures,
            activityTime: this.regenerateLesson.activityTime,
            redesignIdea: ''
          }
          // 如果有引导，此时永久关闭
          this.closeRedesignLessonGuide()
          this.getFrameworkDomains(this.baseInfo.frameworkId)
          this.regenerateLessonVisible = true
          this.$analytics.sendEvent('cg_unit_create_lesson_update')
          // 如果有超过最大值限制的测评点，则截取
          if (this.regenerateLesson.measures && this.regenerateLesson.measures.length > this.maxMeasureSelections) {
            this.regenerateLesson.measures = this.regenerateLesson.measures.slice(0, this.maxMeasureSelections)
            this.$nextTick(() => {
              // 显示警告消息
              this.$message.warning(this.$t('loc.selectMeasureEight'));
            })
          }
        },

        // 点击 Enhance 时如果引导弹窗在主动关闭
        closeRedesignLessonGuide () {
          if (this.showRedesignLessonGuide) {
            this.hideRedignGuide(false)
          }
        },

        // 点击禁用选项时触发提示
        onOptionClick (measure) {
          // 如果没有最大值限制，直接返回
          if (!this.maxMeasureSelections) {
            return
          }
          // 判断是否超过最大值限制  &&  当前选项不在已选中的测评点中
          if (this.regenerateLesson.measures.length >= this.maxMeasureSelections && !this.regenerateLesson.measures.includes(measure.abbreviation)) {
            this.$message.warning(this.$t('loc.selectMeasureEight'))
          }
        },

        // 关闭重新生成课程详情
        closeRedesignLessonDialog () {
          // 关闭重新生成课程弹窗
          this.regenerateLessonVisible = false
          // 评估标识置为 true
          this.needEvaluationLessonDetail = true
          // 持续时间回退
          this.regenerateLesson.activityTime = this.lesson.activityTime
        },

        // 主动关闭重新生成课程详情
        selfCloseRedesignLessonDialog () {
          // 主动离开页面
          this.redesignIdeaLoadLeave = true
          // 关闭重新生成课程弹窗
          this.regenerateLessonVisible = false
          // 评估标识置为 true
          this.needEvaluationLessonDetail = true
        },

        /**
         * 取消使用建议
         */
        cancelSuggestion () {
          // 建议数据清空
          this.evaluationLessonDetailData = {}
          // 评估标识置为 false
          this.needEvaluationLessonDetail = false
          // 关闭建议弹窗
          this.evaluationLessonDetailVisible = false
          // 打开原弹窗
          this.regenerateLessonVisible = true
        },

        /**
         * 应用建议
         */
        applySuggestion () {
          // 应用提示
          this.$message({
            message: this.$t('loc.unitPlannerRecommendationApplied'),
            type: 'success',
            customClass: 'custom-message-class'
          })
          // 数据替换
          if (this.evaluationLessonDetailData.duration) {
            this.regenerateLesson.activityTime = this.evaluationLessonDetailData.duration + ' minutes' // 活动时间
          }
          if (this.evaluationLessonDetailData.keepMeasure && this.evaluationLessonDetailData.keepMeasure.length > 0) {
            this.regenerateLesson.measures = this.evaluationLessonDetailData.keepMeasure // 测评点
          }
          // 数据清空等操作
          this.cancelSuggestion()
        },

        // 确认并重新生成课程详情
        confirmRedesignLesson () {
          this.$refs.regenerateLessonForm.validate((valid) => {
            if (valid) {
              this.$bus.$emit('close-resources-guide')
              this.regenerateLessonVisible = false
              this.regenerateLessonDetail(this.regenerateLesson.measures, this.regenerateLesson.frameworkId, this.regenerateLesson.redesignIdea.trim(), true)
            }
          })
        },

        /**
         * 评估课程详情
         * @returns {Promise<boolean>} 是否评估成功 true 成功，false 失败
         */
        async evaluationLessonIdeas () {
          // 对比 measure 和 ActivityTime 是否有差异
          const oldMeasure = this.lesson.measures
          const newMeasure = this.regenerateLesson.measures
          const oldActivityTime = this.lesson.activityTime
          const newActivityTime = this.regenerateLesson.activityTime

          // 比较 measure 数组是否相同（逐个对比每个元素）
          const measureDifferent = oldMeasure.length !== newMeasure.length ||
            oldMeasure.some((m, index) => m !== newMeasure[index])

          // 比较 ActivityTime 是否不同
          const activityTimeDifferent = newActivityTime && !equalsNotIgnoreCase(oldActivityTime, newActivityTime)
          // 内容相同，无需后续评估
          if (!measureDifferent && !activityTimeDifferent) {
            return false
          }
          // 如果有修改，进行评估
          // 测评点不同时，需要传递测评点 id
          let measureIds = null
          let lessonMeasures = null
          if (measureDifferent) {
            // 根据测评点缩写过滤出测评点 id
            measureIds = this.regenerateLesson.measures.map(measure => {
              return this.getMeasureByAbbr(measure).id
            })

            lessonMeasures = this.regenerateLesson.measures
          }

          // 活动时间不同时
          let lessonActivityTime = null
          if (activityTimeDifferent) {
            lessonActivityTime = this.regenerateLesson.activityTime
          }

          // 参数构建
          const request = {
            lessonMeasureIds: measureIds, // 测评点 ID
            planId: this.lesson.planId, // 计划 ID
            lessonId: this.lesson.id, // 课程 ID
            lessonMeasures: lessonMeasures, // 测评点信息
            activityTime: lessonActivityTime, // 活动时间
            frameworkId: this.unit.baseInfo.frameworkId, // 框架 ID
            grade: this.baseInfo.grade // 年级
          }

          // 调用后台评估接口
          try {
            // 历史评估结果清空
            this.evaluationLessonDetailData = {}
            const res = await LessonApi.evaluationLessonDetail(request)
            if (!res || !res.lessonId) {
              // 评估结果无建议
              return false
            }
            // 评估成功
            // 评估结果
            this.evaluationLessonDetailData = res
            return true
          } catch (error) {
            // 评估失败
          }

          return false
        },

        // 获取单元下的领域测评点
        getFrameworkDomains (frameworkId) {
          this.loadingFrameworkLoading = true
          // 获取路由参数 unitId
          const unitId = this.$route.params.unitId
          this.$store.dispatch('curriculum/getMeasuresContainTopAndBottom',{ frameworkId: (frameworkId || this.regenerateLesson.frameworkId),unitId: unitId }).then(domains => {
            this.domains = domains
            this.loadingFrameworkLoading = false
          })
        },
        // 更新自定义模板生成状态
        updateGenerateCustomTemplateLoading (loading) {
          this.generateCustomTemplateLoading = loading
        },
        // 更新课程 Slides 生成状态
        updateGenerateLessonSlidesLoading (loading) {
          this.generateLessonSlidesLoading = loading
        },
        // 更新课程模板
        updateLessonTemplate (lessonTemplate) {
          this.$set(this.item, 'lessonTemplateType', lessonTemplate)
          let weeklyPlan = this.getWeeklyPlan(this.item)
          // 参数
          let params = {
              items: [this.item],
              frameworkId: this.baseInfo.frameworkId,
              planId: weeklyPlan.id,
              unitId: this.unit.id // 单元 ID
          }
          // 重新判断当前 Unit 的生成进度
          params.progress = this.reJudgeUnitGenerateProgress()
          this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
            this.needRecordVersion = true
            this.generateLessonDetailStream()
          }).catch(error => {
            this.$message.error(error.response.data.error_message)
          })
          this.$bus.$once('unitClearResourceHistory', this.handleLessonGenerated)
        },

        // 课程生成完成后的处理
        async handleLessonGenerated() {
          // 创建一个检查函数
          const checkAndClearHistory = async () => {
            if (!this.generateLessonDetailLoading) {
                await this.$axios.post($api.urls().clearResourceHistory, null, {
                  params: { lessonId: this.lesson.id }
                })
            } else {
              // 如果还在加载中，设置定时器继续检查
              setTimeout(checkAndClearHistory, 1000) // 每秒检查一次
            }
          }

          // 开始检查
          checkAndClearHistory()
        },
        // 重新生成课程详情
        async regenerateLessonDetail (newMeasures, frameworkId, redesignIdea, enhance = false) {
            // 重置重试次数
            this.retryCount = 0
            // 有新的测评点，则更新课程中的测评点
            if (newMeasures && newMeasures.length > 0) {
                // 开始 Loading
                this.generateLessonDetailLoading = true
                // 更新活动项中测评点
                try {
                    await this.updateCurrentItemMeasures(newMeasures)
                } catch (error) {
                    this.generateLessonDetailLoading = false
                }
            } else {
                // 重新生成课程详情 TODO 测试使用，更新测评点时不自动更新课程
                // this.generateLessonDetail()
            }
            // 重新生成课程详情
            this.generateLessonDetailStream(null, redesignIdea)
            // 如果是增强课程，则更新标记
            if (enhance) {
                this.isEnhanceLesson = true
            }
        },
        // 生成典型行为
        generateTypicalBehaviors () {
            if (!this.item || !this.lesson) {
                return
            }
            this.generateTypicalBehaviorsLoading = true
            let baseInfo = JSON.parse(JSON.stringify(this.unit.baseInfo))
            // 如果课程中有框架 ID ，则以课程中的框架 ID 为准
            if (this.lesson && this.lesson.frameworkId) {
                baseInfo.frameworkId = this.lesson.frameworkId
            }
            let params = {
                baseInfo: baseInfo,
                lesson: this.lesson,
                gptSetting: this.gptSetting
            }
            this.$axios
                .post(aiApiUrl + '?action=generate_typical_behaviors', params).then((res) => {
                    this.$set(this.lesson, 'typicalBehaviors', res.result)
                    this.createLesson()
                    this.generateTypicalBehaviorsLoading = false
                })
                .catch((error) => {
                    this.$set(this.lesson, 'typicalBehaviors', [])
                    this.generateTypicalBehaviorsLoading = false
                    this.$message.error(error.response.data.error_message)
                })
        },

        // 重新生成典型行为
        async reGenerateTypicalBehaviorsStream (isFrameworkMapped) {
            this.$analytics.sendEvent('web_unit_create3_group_det_regen_tb')
            try {
              await this.generateTypicalBehaviorsStream(null, isFrameworkMapped)
              this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
            } catch (e) {
            } finally {
            }
        },

        // 异步生成典型行为
        generateTypicalBehaviorsStream (callback, isFrameworkMapped, generateMapped) {
            // 判断当前模块是否生成
            if(!this.getLightAdaptModuleGenerate('teacherGuideFlag')) {
              return
            }
            this.generateTypicalBehaviorsLoading = true
            // 重置数据
            this.item.behaviorData = ''
            let emptyBehaviors = []
            if (this.lesson.measures) {
                for (let i = 0; i < this.lesson.measures.length; i++) {
                    emptyBehaviors.push({})
                }
            }
            if (isFrameworkMapped) {
              let original = (this.lesson.typicalBehaviors && this.lesson.typicalBehaviors.filter(item => !item.mapped)) || []
              let all = [...original, ...emptyBehaviors]
              this.$set(this.lesson, 'typicalBehaviors', all)
            } else {
              let original = (this.lesson.typicalBehaviors && this.lesson.typicalBehaviors.filter(item => item.mapped)) || []
              let all = [...original, ...emptyBehaviors]
              this.$set(this.lesson, 'typicalBehaviors', all)
            }
            // 获取 Item 测评点缩写列表
            let itemMeasure = null
            if (this.item && this.item.measures) {
                itemMeasure = this.item.measures
            }
            // 生成单元概览参数
            let params = {
                unitId: this.unit.id,
                planId: '',
                lessonId: this.lesson.id,
                frameworkMapped: isFrameworkMapped || false
            }
            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                // 更新数据
                this.item.behaviorData += message.data
                // 解析数据
                let parsedData = parseStreamData(this.item.behaviorData, [
                    { key: 'measureAbbreviation', name: 'Measure Abbreviation' },
                    { key: 'typicalBehaviors', name: 'Typical Behaviors' }
                ])
                // 如果解析的数据存在
                if (parsedData && parsedData.length > 0) {
                    // 循环遍历解析的数据
                    for (let i = 0; i < parsedData.length; i++) {
                        if (isFrameworkMapped) {
                          parsedData[i].mapped = true
                        }
                        // 如果存在 measureAbbreviation ，则替换换行符
                        if (parsedData[i].measureAbbreviation && !isFrameworkMapped) {
                          let measureAbbreviation = parsedData[i].measureAbbreviation
                          // 如果 measureAbbreviation 存在
                          if (measureAbbreviation) {
                            // 去除空格
                            measureAbbreviation = measureAbbreviation.trim()
                            // 如果 convertMappingAbbrToDomainAbbrMap 存在并且长度大于 0，则使用 convertMappingAbbrToDomainAbbrMap 中的值
                            if (this.convertMappingAbbrToDomainAbbrMap && this.convertMappingAbbrToDomainAbbrMap.size > 0) {
                              measureAbbreviation = this.convertMappingAbbrToDomainAbbrMap.get(measureAbbreviation) || measureAbbreviation
                            }
                          }
                          parsedData[i].measureAbbreviation = measureAbbreviation
                          // 如果缩写不在 itemMeasure 中，则过滤掉
                          if (itemMeasure && !itemMeasure.includes(measureAbbreviation)) {
                                parsedData[i] = {}
                            }
                        }
                        // 由于换模型，导致生成的内容有多余的内容，需要过滤
                        // 只处理 parsedData 的最后一个元素
                        if (i === parsedData.length - 1) {
                            if (parsedData[i].typicalBehaviors) {
                                // 以两个 /n/n 为分隔符，获取最后一个分隔符之后的内容
                                let typicalBehaviorsTemp = parsedData[i].typicalBehaviors.split('\n\n')
                                // 若 typicalBehaviorsTemp 不为空且长度大于 1
                                if (typicalBehaviorsTemp && typicalBehaviorsTemp.length > 1) {
                                    // 获取最后一个分隔符之后的内容
                                    let lastTypicalBehaviorsTemp = typicalBehaviorsTemp[typicalBehaviorsTemp.length - 1]
                                    // 进行正则判断，判断是否以数字或者 - 开头
                                    let regex = /^(-|\d+)\s.*/gm
                                    // 使用正则表达式匹配保留语句
                                    let matchedStatements = lastTypicalBehaviorsTemp.match(regex)
                                    // 再将匹配到的保留到 typicalBehaviorsTemp 中
                                    if (matchedStatements) {
                                        // 将匹配后的内容拼接到 typicalBehaviorsTemp 中
                                        typicalBehaviorsTemp[typicalBehaviorsTemp.length - 1] = matchedStatements.join('\n')
                                        // 将 typicalBehaviorsTemp 拼接成字符串，赋值给 parsedData[i].typicalBehaviors
                                        parsedData[i].typicalBehaviors = typicalBehaviorsTemp.join('\n')
                                    } else {
                                        // 若没有匹配到，则将 typicalBehaviorsTemp 除去最后一个元素，然后赋值给 parsedData[i].typicalBehaviors
                                        parsedData[i].typicalBehaviors = typicalBehaviorsTemp.slice(0, typicalBehaviorsTemp.length - 1).join('\n')
                                    }
                                }
                            }
                        }
                    }
                }
                // 更新数据
                if (isFrameworkMapped) {
                  let original = (this.lesson.typicalBehaviors && this.lesson.typicalBehaviors.filter(item => !item.mapped)) || []
                  let all = [...original, ...parsedData]
                  all = all.filter(item => Object.keys(item).length > 0)
                  this.$set(this.lesson, 'typicalBehaviors', all)
                } else {
                  let original = (this.lesson.typicalBehaviors && this.lesson.typicalBehaviors.filter(item => item.mapped)) || []
                  let all = [...original, ...parsedData]
                  all = all.filter(item => Object.keys(item).length > 0)
                  this.$set(this.lesson, 'typicalBehaviors', all)
                }
                // this.$set(this.lesson, 'typicalBehaviors', parsedData)
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateTypicalBehaviorsStream, params, messageCallback)
                    .then((res) => {
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                          promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 更新课程信息
                        this.saveLessonSupplenments(true)
                        // 生成结束
                        this.generateTypicalBehaviorsLoading = false
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        resolve()
                    })
                    .catch(error => {
                        this.generateTypicalBehaviorsLoading = false
                        // 生成出错
                        if (error.message && !error.message == '"NO_DATA"') {
                          this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        }
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
                    .finally(() => {
                        // 生成映射关系典型行为
                        if ((frameworkUtils.isCAPTKLF(this.unitInfo.frameworkId) || frameworkUtils.isMELS(this.unitInfo.frameworkId) || frameworkUtils.isILEARN(this.unitInfo.frameworkId)) && generateMapped) {
                          this.generateTypicalBehaviorsStream()
                        }
                    })
            })
        },

        /**
         * 生成标准教学指导
         */
        async generateTeachingTips () {
          // 判断当前模块是否生成
          if(!this.getLightAdaptModuleGenerate('teacherGuideFlag')) {
            return
          }
          // 清除旧数据数据
          this.$set(this.lesson, 'teachingTips', [])
          // 开始 Loading
          this.generateTeachingTipsForStandardLoading = true
          // 构建请求
          let params = this.buildGenerateTeachingTipsRequest()
          this.item.teachingTipsData = ''
          // 消息回调
          let messageCallback = (message) => {
            // 更新数据
            let teachingTipsData = (this.item.teachingTipsData || '') + message.data
            this.$set(this.item, 'teachingTipsData', teachingTipsData)
            // 解析数据
            let parsedData = parseStreamData(teachingTipsData, [
              { key: 'measureAbbreviation', name: 'Measure' },
              { key: 'teachingTips', name: 'Instructions' }
            ])
            this.handleTeachingTipsParsedData(parsedData)
          }
          // 发送请求
          return new Promise((resolve, reject) => {
            // 生成单元概览
            createEventSource($api.urls().generateTeachingTipsByLessonContentStream, {}, messageCallback, 'POST', params)
              .then((res) => {
                this.generateTeachingTipsForStandardLoading = false
                // 记录 promptUsageRecordId
                if (res && res.promptUsageRecordId !== '') {
                  this.promptUsageRecordIds.push(res.promptUsageRecordId)
                }
                resolve()
              })
              .catch(error => {
                this.generateTeachingTipsForStandardLoading = false
                // 调用请求失败处理
                this.requestErrorProcessing(error)
                reject(error)
              })
          })
        },
        // f封装标准教学指导请求数据
        buildGenerateTeachingTipsRequest () {
          let lesson = {
            name: this.item.title,
            measures: this.lesson.measures,
            ageGroup: this.lesson.ageGroup,
            frameworkId: this.baseInfo.frameworkId,
            objectives: this.lesson.objectives,
            materials: this.lesson.materials,
            prepareTime: this.lesson.prepareTime,
            activityTime: this.lesson.activityTime,
            keyVocabularyWords: this.lesson.keyVocabularyWords,
            implementationSteps: this.lesson.implementationSteps,
            classroomType: this.lesson.classroomType
          }
          return { 'lesson': lesson }
        },
        // 处理标准教学指导 GPT 返回数据
        handleTeachingTipsParsedData (teachingTipsForStandard) {
          // 更新标准教学指导内容
          if (teachingTipsForStandard && teachingTipsForStandard.length > 0) {
            let teachingTipsValue = [] // 所要展示的典型行为列表
            for (let teachingTip of teachingTipsForStandard) {
              if (teachingTip && teachingTip.measureAbbreviation && teachingTip.teachingTips) {
                let measureAbbreviation = teachingTip.measureAbbreviation // 获取测评点缩写
                let tips = teachingTip.teachingTips.trim() // 获取行为
                let teachingTipsObj = {} // 构建所要展示的典型行为列表中的对象
                teachingTipsObj.teachingTips = tips // 更新行为
                // 根据测评点缩写获取测评点详细信息
                const measure = this.getMeasureByAbbr(measureAbbreviation.trim())
                if (measure) {
                  teachingTipsObj.measureId = measure.id
                  teachingTipsObj.measureName = measure.name
                  teachingTipsObj.measureAbbreviation = measure.abbreviation
                  teachingTipsObj.core = measure.core
                  teachingTipsValue.push(teachingTipsObj)
                }
              }
            }
            // 更新数据
            this.$set(this.lesson, 'teachingTips', teachingTipsValue)
          }
        },

        /**
         * 重新生成课程校训信息
         */
        regeneratePortraitGraduate () {
          this.generateLessonLearnerProfileStream(true)
        },

        /**
         * 生成课程校训
         */
        async generateLessonLearnerProfileStream (needRecordVersion = false) {
          // 判断当前模块是否生成
          if(!this.getLightAdaptModuleGenerate('portraitOfGraduateFlag')) {
            return
          }
          // 如果当前单元没设置校训则不生成
          if (!(this.unit.baseInfo.newRubrics && this.unit.baseInfo.newRubrics.length > 0) || (!(this.item.rubrics && this.item.rubrics.length > 0) && !this.item.lessonTemplateType)) {
            return
          }
          // 开始 Loading
          this.generatePortraitGraduateLoading = true
          // 清除旧数据数据
          this.$set(this.lesson, 'learnerProfiles', [])
          // 构建请求
          let params = this.buildGenerateLessonLearnerProfileRequest()
          this.item.learnerProfilesData = ''
          // 消息回调
          let messageCallback = (message) => {
            // 更新数据
            let learnerProfilesData = (this.item.learnerProfilesData || '') + message.data
            this.$set(this.item, 'learnerProfilesData', learnerProfilesData)
            // 解析数据
            let parsedData = parseStreamData(learnerProfilesData, [
              { key: 'rubricsName', name: 'Attribute Name' },
              { key: 'rubricsValue', name: 'Instructions' },
            ])
            let tree = tools.handleLessonLearnerProfilesParsedData(parsedData, this.rubricsOptions)
            this.$set(this.lesson, 'learnerProfiles', tree)
          }
          // 发送请求
          return new Promise((resolve, reject) => {
            // 生成单元概览
            createEventSource($api.urls().generateLessonLearnerProfileStream, {}, messageCallback, 'POST', params)
              .then((res) => {
                // this.syncLessonLearnerProfileToItem()
                if (needRecordVersion) {
                  this.saveLessonSupplenments(true)
                }
                this.generatePortraitGraduateLoading = false
                // 记录 promptUsageRecordId
                if (res && res.promptUsageRecordId !== '') {
                  this.promptUsageRecordIds.push(res.promptUsageRecordId)
                }
                resolve()
              })
              .catch(error => {
                this.generatePortraitGraduateLoading = false
                // 调用请求失败处理
                reject(error)
              })
          })
        },

        // 封装生成课程校训请求数据
        buildGenerateLessonLearnerProfileRequest () {
          return {
            lessonId: this.item.lessonId,
            unitId: this.unit.id,
            planId: this.item.planId,
            planItemId: this.item.id,
            lessonTemplateName: this.item.lessonTemplateType,
            itemRubrics: this.item.rubrics && this.item.rubrics.length > 0 ? JSON.stringify(this.item.rubrics) : null,
            lesson: {
              ...this.buildGenerateTeachingTipsRequest().lesson // 使用展开运算符来提取 `lesson` 部分
            }
          }
        },

        // 根据缩写获取测评点信息
        getMeasureByAbbr (abbr) {
          if (!abbr || !this.domains || this.domains.length === 0) {
            return null
          }
          // 遍历框架
          for (let i = 0; i < this.domains.length; i++) {
            const domain = this.domains[i]
            // 遍历测评点
            for (let j = 0; j < domain.children.length; j++) {
              const measure = domain.children[j]
              if (measure.abbreviation && measure.abbreviation.trim() === abbr || measure.mappingAbbr && measure.mappingAbbr.trim() === abbr) {
                return measure
              }
            }
          }
          return null
        },

        // 重新生成 UDL
        async regenerateUniversalDesignForLearningStream (groupId) {
            this.$analytics.sendEvent('web_unit_create3_group_det_regen_udl')
            try {
                if (groupId) {
                    await this.generateUniversalDesignForLearningGroupStream(groupId) // 生成 UDL
                } else {
                    await this.generateUniversalDesignForLearningStream() // 生成 UDL
                }
            } finally {
                this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
            }
        },

        // 生成 UDL
        async generateUniversalDesignForLearningStream (callback) {
            // 如果是改编的课程，调用改变重新生成
            if (this.lesson.adaptedLesson) {
              await this.$refs.lessonDetailRef.generateUDLData()
              return
            }
            // 判断当前模块是否生成
            if(!this.getLightAdaptModuleGenerate('teacherGuideFlag')) {
              return
            }

            this.generateUniversalDesignForLearningLoading = true
            // 重置数据
            this.$set(this.lesson, 'universalDesignForLearning', null)
            if (this.lesson.learnersWithIEP) {
                this.$set(this.lesson, 'learnersWithIEP', null)
            }
            if (this.lesson.englishLanguageLearners) {
                this.$set(this.lesson, 'englishLanguageLearners', null)
            }
            // 生成单元概览参数
            let params = {
                unitId: this.unit.id,
                planId: '',
                lessonId: this.lesson.id
            }
            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                // 更新数据
                let data = (this.lesson.universalDesignForLearning || '') + message.data
                this.$set(this.lesson, 'universalDesignForLearning', data)
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateUniversalDesignForLearningStream, params, messageCallback)
                    .then((res) => {
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                          promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 更新课程信息
                        this.saveLessonSupplenments()
                        // 生成结束
                        this.generateUniversalDesignForLearningLoading = false
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        this.generateUniversalDesignForLearningLoading = false
                        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
            })
        },

        // 重新生成文化响应式教学
        async regenerateCulturallyResponsiveInstructionStream (groupId, regenerate) {
            this.$analytics.sendEvent('web_unit_create3_group_det_rege_clr')
            if (equalsIgnoreCase(regenerate, 'regenerate')) {
              this.generateLessonSourceLoading = true
            }
            try {
                if (groupId) {
                    await this.generateCulturallyResponsiveInstructionGroupStream(groupId, null, true) // 生成文化教学内容
                } else {
                    await this.generateCulturallyResponsiveInstructionStream(null, null, true) // 生成文化教学内容
                }
            } catch (e) {
            }

        },

        // 生成文化响应式教学
        async generateCulturallyResponsiveInstructionStream (callback, generateBehavior, needRecordVersion = false) {
            // 判断当前模块是否生成
            if(!this.getLightAdaptModuleGenerate('teacherGuideFlag')) {
              return
            }
            this.generateCulturallyResponsiveInstructionLoading = true
            // 重置数据
            this.$set(this.lesson, 'culturallyResponsiveInstruction', null)
            if (this.lesson && this.lesson.lessonClrAndSources) {
              this.$set(this.lesson.lessonClrAndSources, 'clr', '')
            }
            // 生成单元概览参数
            let params = {
                unitId: this.unit.id,
                planId: '',
                lessonId: this.lesson.id
            }
            let clrData = ''
            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                // 更新数据
                clrData += message.data
                // 移除无用的字符
                clrData = removeUnexpectedCharacters(clrData)
                let data = clrData.replace(/\n/g, '<br>').replace(/(<br>){2,}/g, '<br><br>')
                this.$set(this.lesson, 'culturallyResponsiveInstruction', clrData)
                this.$set(this.lesson, 'lessonClrAndSources', {
                  clr: data,
                  sources: this.lesson.lessonClrAndSources && this.lesson.lessonClrAndSources.sources
                })
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateCulturallyResponsiveInstructionStream, params, messageCallback)
                    .then(async (res) => {
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                          promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 更新课程信息
                        this.saveLessonSupplenments(needRecordVersion)
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        await this.generateLessonSourceByCLR(null, generateBehavior)
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        this.generateCulturallyResponsiveInstructionLoading = false
                        this.generateLessonSourceLoading = false
                        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
            })
        },
        generateLessonSources (isLessonImplStep = false) {
            if (isLessonImplStep) {
              this.generateImplementationStepsSourceLessonDetail(true)
            } else {
              // 更新课程信息，否则库里面的 CLR 会覆盖掉现有的资源
              this.generateLessonSourceByCLR(this.lesson.lessonClrAndSources)
            }
        },
        // 根据 CLR 生成资源内容
        async generateLessonSourceByCLR (lessonClrAndSources, generateBehavior = false) {
          // 判断当前模块是否生成
          if(!this.getLightAdaptModuleGenerate('resourceUpgradeFlag')) {
            // 生成结束
            this.finishCLRGeneration()
            return
          }
          // 记录是否是从 CLR 生成回调中调用的

          let culturallyResponsiveInstruction = this.lesson.culturallyResponsiveInstruction
          if (lessonClrAndSources) {
            culturallyResponsiveInstruction = lessonClrAndSources.clr
          }
          // CLR 生成结束之后，自动触发生成资源
          const params = {
            culturallyResponsiveInstructionGroup: culturallyResponsiveInstruction,
            lessonId: this.lesson.id,
            enhanceLesson: generateBehavior // 传递该参数到后端
          }
          // 根据 CLR 生成资源内容
          await this.$axios.post(serverlessApiUrl + $api.urls().generateLessonSource, params).then(res => {
            if (res.lessonClrAndSources) {
              res.lessonClrAndSources.sources.forEach(source => {
                // 初始化 hidden 属性为 false
                this.$set(source, 'hidden', false)
              })
            } else {
              res.lessonClrAndSources = {
                clr: this.lesson.culturallyResponsiveInstruction.replaceAll(/\n/g, '<br>'),
                sources: []
              }
            }
            // 如果资源存在时，则默认首先展示资源列表数据
            if (res.lessonClrAndSources.sources.length > 0) {
              this.$refs.lessonDetailRef.showSource = true
            } else {
              this.$refs.lessonDetailRef.showSource = false
            }
            // 更新当前课程的资源信息
            this.$set(this.lesson, 'lessonClrAndSources', res.lessonClrAndSources)
            // 更新课程信息
            this.saveLessonSupplenments()
            this.generatedLessonSources = true
            // 生成结束
            this.finishCLRGeneration()
            if (this.generateLessonSourceLoading) {
              this.$message.success(this.$t('loc.unitPlannerRegenerateSuf'))
            }
            this.generateLessonSourceLoading = false
          }).catch(() => {
            this.generateLessonSourceLoading = false
            this.generateCulturallyResponsiveInstructionLoading = false
            // this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          })
        },
        // UnitReview.vue 中
        finishCLRGeneration () {
          // 给数据同步留出时间
          setTimeout(() => {
            this.generateCulturallyResponsiveInstructionLoading = false
          }, 100) // 100ms 通常足够完成数据同步
        },
        updateClrWithSubscript (lessonClrAndSources) {
            this.$set(this.lesson, 'lessonClrAndSources', lessonClrAndSources)
        },
        // 生成 UDL
        async generateUniversalDesignForLearningGroupStream (groupId, callback) {
            this.generateUniversalDesignForLearningGroupLoading = true
            // 重置数据
            this.$set(this.lesson, 'universalDesignForLearningGroup', null)
            this.$set(this.lesson, 'universalDesignForLearningGroupData', null)
            // 重置小孩分组数据
            this.$set(this.lesson, 'udlGroups', [])
            // 更新班级 ID
            this.$set(this.lesson, 'groupId', groupId)
            // 生成单元概览参数
            let params = {
                unitId: this.unit.id,
                planId: '',
                lessonId: this.lesson.id,
                groupId: groupId
            }
            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                // 更新数据
                let data = (this.lesson.universalDesignForLearningGroupData || '') + message.data
                // 替换无用的符号
                data = removeUnexpectedCharacters(data)
                this.$set(this.lesson, 'universalDesignForLearningGroupData', data)
                // 解析数据
                let parsedData = parseStreamData(data, [
                    { key: 'groupInfo', name: 'Inclusive Learning Groups' },
                    { key: 'content', name: 'For Learners with IEP' }
                ])[0]
                if (parsedData && parsedData.groupInfo) {
                    // 按照行分割
                    let lines = parsedData.groupInfo.split('\n')
                    // 遍历行，分组
                    let groups = []
                    lines.forEach(line => {
                        if (!line || line.indexOf('Group') === -1 || line.indexOf(':') === -1) {
                            return
                        }
                        // 以冒号分割
                        let splitIndex = line.indexOf(':')
                        // 分组名称
                        let groupName = line.substring(0, splitIndex)
                        // 小孩名称列表
                        let childNamesStr = line.substring(splitIndex + 1, line.length)
                        // 分割小孩名称
                        let childNames = childNamesStr.split(',')
                        childNames = childNames.map(childName => childName.trim())
                        let group = {
                            groupName: groupName,
                            childNames: childNames
                        }
                        groups.push(group)
                    })
                    this.$set(this.lesson, 'udlGroups', groups)
                }
                if (parsedData && parsedData.content) {
                    this.$set(this.lesson, 'universalDesignForLearningGroup', 'For Learners with IEP ' + parsedData.content)
                }
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateUniversalDesignForLearningGroupStream, params, messageCallback)
                    .then((res) => {
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                            promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 更新课程信息
                        this.createLesson()
                        // 生成结束
                        this.generateUniversalDesignForLearningGroupLoading = false
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        this.generateUniversalDesignForLearningGroupLoading = false
                        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
            })
        },

        // 生成文化响应式教学
        async generateCulturallyResponsiveInstructionGroupStream (groupId, callback, needRecordVersion = false) {
            this.generateCulturallyResponsiveInstructionGroupLoading = true
            // 重置数据
            this.$set(this.lesson, 'culturallyResponsiveInstructionGroup', null)
            // 更新班级 ID
            this.$set(this.lesson, 'groupId', groupId)
            // 生成单元概览参数
            let params = {
                unitId: this.unit.id,
                planId: '',
                lessonId: this.lesson.id,
                groupId: groupId
            }
            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                // 更新数据
                let data = (this.lesson.culturallyResponsiveInstructionGroup || '') + message.data
                // 替换无用的符号
                data = removeUnexpectedCharacters(data)
                this.$set(this.lesson, 'culturallyResponsiveInstructionGroup', data)
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateCulturallyResponsiveInstructionGroupStream, params, messageCallback)
                    .then((res) => {
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                            promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 更新课程信息
                        this.createLesson(null, needRecordVersion)
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        this.generateLessonSourceByCLR()
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        this.generateCulturallyResponsiveInstructionGroupLoading = false
                        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
            })
        },

        // 重新生成测验
        async regenerateLessonQuiz (param) {
            await this.generateLessonQuizStream(true, param)
        },
        // 生成测验
        async generateLessonQuizStream (regenerate, { questionIndex, questionLevel, questionType, measureAbbreviation, question }, callback = () => {}) {
            // 判断当前模块是否生成
            if(!this.getLightAdaptModuleGenerate('standardsAssessmentFlag')) {
              return
            }
            // 声明临时 id 变量
            let tempId
            // 重置数据
            if (regenerate) {
              // 为当前题目临时生成一个 id
              tempId = tools.uuidv4()
              // 如果是单个重新生成
              const currentQuestion = this.lesson.questions[questionIndex]
              currentQuestion.hasOwnProperty('showQuizRegenerateButton') && this.$delete(currentQuestion, 'showQuizRegenerateButton')
              this.$set(currentQuestion, 'generateLoading', true)
              this.$set(currentQuestion, 'question', '')
              this.$set(currentQuestion, 'answer', '')
              // 临时设置 id
              this.$set(currentQuestion, 'tempId', tempId)
              this.$set(this.lesson.questions, questionIndex, currentQuestion)
            } else {
              // 如果是全部重新生成
              this.$set(this.lesson, 'questions', null)
              this.$set(this.lesson, 'generateLessonQuizLoading', true)
            }
            this.generateLessonQuizLoading = true
            // 生成单元概览参数
            let params = {
                unitId: this.unit.id,
                planId: '',
                lessonId: this.lesson.id,
                useBloomLevel: this.$refs.lessonDetailRef.isBloom, // 是否使用 Bloom 等级
                // 如果是重新生成则拼接对应参数
                ...(regenerate && { questionLevel, questionType, measureAbbreviation, previousQuestion: question })
            }
            // 由于 quiz 的数据是放入到页面展示的，但是可能数据没有返回完，直接修改数据会导致页面展示不全，所以需要一个临时变量
            let quizData = ''
            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                quizData += message.data
                // 解析返回的数据
                let parsedQuestions = parseStreamData(quizData, [
                  { key: 'type', name: 'Question Type' },
                  { key: 'level', name: ['Bloom\'s Taxonomy', 'DOK Level'] },
                  { key: 'measureAbbreviation', name: ['Measure Abbreviation', 'Measure'] },
                  { key: 'question', name: 'Question Content' },
                  { key: 'answer', name: ['Correct Answer', 'Example Answer', 'Expected Answer', 'Answer Guide', 'Correct Answers', 'Example Response', 'Correct Order'] }
                ])
                parsedQuestions = parsedQuestions.map(parsedQuestion => {
                  if (parsedQuestion.measureAbbreviation && this.convertMappingAbbrToDomainAbbrMap && this.convertMappingAbbrToDomainAbbrMap.size > 0) {
                    parsedQuestion.measureAbbreviation = this.convertMappingAbbrToDomainAbbrMap.get(parsedQuestion.measureAbbreviation) || parsedQuestion.measureAbbreviation
                  }
                  // 如果 level 存在，则去除空格
                  if (parsedQuestion.level) {
                    parsedQuestion.level = parsedQuestion.level.trim().replace(' ', '')
                  }
                  return parsedQuestion
                })
                // 如果是单个重新生成，那么则只设置重新生成的 question 内容
                if (regenerate) {
                  // 通过临时 id 查找目标问题
                  const targetQuestion = this.lesson.questions.find(q => q.tempId === tempId)
                  if (targetQuestion && parsedQuestions.length > 0) {
                    targetQuestion.answer = parsedQuestions[0].answer
                    targetQuestion.question = parsedQuestions[0].question
                    // 触发响应式更新
                    this.$forceUpdate()
                  }
                } else {
                  // 设置所有 question 内容
                  this.$set(this.lesson, 'questions', parsedQuestions)
                }
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource(regenerate ? $api.urls().regenerateLessonQuiz : $api.urls().generateLessonQuiz, {}, messageCallback, 'POST', params)
                    .then((res) => {
                        // 设置 quiz 中的题目序号和是否显示重新生成按钮的标记数组,题目序号从一开始
                        let currentSortIndex = 1
                        this.lesson && this.lesson.questions && (this.lesson.questions = this.lesson.questions.map(question => {
                          this.$set(question, 'sortIndex', currentSortIndex++)
                          return question
                        }))
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                            promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        if (regenerate) {
                          // 设置当前重新生成的 question 的加载状态 - 通过临时 id 查找
                          const targetQuestion = this.lesson.questions.find(q => q.tempId === tempId)
                          if (targetQuestion) {
                            this.$set(targetQuestion, 'generateLoading', false)
                            // 清理临时 id
                            this.$delete(targetQuestion, 'tempId')
                          }
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 更新课程信息
                        this.saveLessonSupplenments()
                        // 生成结束
                        this.generateLessonQuizLoading = false
                        this.$set(this.lesson, 'generateLessonQuizLoading', false)
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        this.generateLessonQuizLoading = false
                        this.$set(this.lesson, 'generateLessonQuizLoading', false)

                        // 如果是重新生成，需要清理临时 id
                        if (regenerate) {
                          const targetQuestion = this.lesson.questions.find(q => q.tempId === tempId)
                          if (targetQuestion) {
                            this.$set(targetQuestion, 'generateLoading', false)
                            // 清理临时 id
                            this.$delete(targetQuestion, 'tempId')
                          }
                        }

                        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
            })
        },

        // 重新生成家庭活动
        async reGenerateHomeActivityStream () {
            this.$analytics.sendEvent('web_unit_create3_group_det_regen_ah')
            try {
                    await this.generateHomeActivityStream(false)
            } finally {
                this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
            }
        },

        // 生成家庭活动
        async generateHomeActivityStream (flag, callback = () => {}) {
            // 只有全部生成新课程时，才用年龄段判断是否生成家庭活动
            if (flag) {
                // 判断是否需要生成家庭活动
                if (!this.isShowHomeActivity) {
                    return
                }
            }
            this.generateHomeActivityLoading = true
            // 重置数据
            this.$set(this.lesson, 'homeActivity', null)
            this.$set(this.lesson, 'generateHomeActivityLoading', true)
            // 生成单元概览参数
            let params = {
                unitId: this.unit.id,
                planId: '',
                lessonId: this.lesson.id
            }
            let homeActivityData = ''
            // 由于 homeActivityData 的数据是放入到页面展示的，但是可能数据没有返回完，直接修改数据会导致页面展示不全，所以需要一个临时变量
            let tempActivityData = ''
            // 消息回调`
            let messageCallback = (message) => {
                // 如果离开页面，则不再执行
                if (this.leavedPage) {
                    return
                }
                homeActivityData += message.data
                // 移除无用的符号
                homeActivityData = removeUnexpectedCharacters(homeActivityData)
                // 替换开头和结尾的星号
                homeActivityData = homeActivityData.replace(/^\*/g, '').replace(/\*$/g, '')
                // 替换 Activity Description 前多余的换行
                homeActivityData = homeActivityData.replace(/\n\nActivity Description/g, '\nActivity Description')
                // 为 tempActivityData 赋值
                tempActivityData = homeActivityData
                // 解析 homeActivity 数据
                let parseMersures = parseStreamData(homeActivityData, [
                  { key: 'activity ', name: 'Activity' },
                  { key: 'measuresString', name: 'Measures' },
                  { key: 'activityDescription', name: 'Activity Description' }
                ])
                if (parseMersures && parseMersures.length > 0) {
                  // 循环解析出来的数据
                  parseMersures.forEach((item, index) => {
                    // 如果 measureString 存在
                    // 解析测评点
                    if (item.measuresString) {
                      let measures = item.measuresString.split(';')
                      // 只有一个测评点时，尝试用逗号分隔
                      if (measures && measures.length == 1) {
                        measures = item.measuresString.split(',')
                      }
                      // 去除空格
                      measures = measures.map(measure => measure.trim())
                        .map(measure => {
                          // 如果 convertMappingAbbrToDomainAbbrMap 存在并且长度大于 0，则使用 convertMappingAbbrToDomainAbbrMap 中的值
                          if (this.convertMappingAbbrToDomainAbbrMap && this.convertMappingAbbrToDomainAbbrMap.size > 0) {
                            return this.convertMappingAbbrToDomainAbbrMap.get(measure) || measure
                          }
                          return measure
                        })
                      // 替换掉 tempActivityData 中的测评点
                      tempActivityData = tempActivityData.replace(item.measuresString, measures.join('; '))
                    }
                  })
                }
                // 更新数据
                // let data = homeActivityData.replace(/\n/g, '<br>')
                this.$set(this.lesson, 'homeActivity', tempActivityData)
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateHomeActivityStream, params, messageCallback)
                    .then((res) => {
                        // 记录 promptUsageRecordId
                        let promptUsageRecordIds = this.promptUsageRecordIds
                        if (res && res.promptUsageRecordId && res.promptUsageRecordId !== '') {
                            promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        // 将 promptUsageRecordIds 放到 promptUsageRecordIdsMap 中，key 为 this.lesson.id
                        if (this.lesson && this.lesson.id) {
                            let lessonId = this.lesson.id.trim().toUpperCase()
                            this.promptUsageRecordIdsMap.set(lessonId, promptUsageRecordIds)
                        }
                        // 如果离开页面，则不再执行
                        if (this.leavedPage) {
                            return
                        }
                        // 更新课程信息
                        this.saveLessonSupplenments()
                        // 生成结束
                        this.generateHomeActivityLoading = false
                        this.$set(this.lesson, 'generateHomeActivityLoading', false)
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        this.generateHomeActivityLoading = false
                        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        if (callback && typeof callback === 'function') {
                            callback()
                        }
                        reject(error)
                    })
            })
        },

        // 更新 promptSourceList
        updatePromptSourceList (val) {
            if (val) {
                // 定义一个资源数组
              let sourceValue = this.lesson.lessonClrAndSources
              if (sourceValue && sourceValue.sources && sourceValue.sources.length > 0) {
                  sourceValue = 'sources: ' + sourceValue.sources.map(source => {
                      return '- sourceKeyword: ' + source.sourceKeywords + '\nsourceLink: ' + source.sourceLink
                  }).join('\n\n')
              }
              const sources = [
                    { sourceStep: 4, sourceName: 'lesson-specific learning standards (with detailed info)', sourceValue: '-' },
                    { sourceStep: 4, sourceName: 'prepare time', sourceValue: this.lesson.prepareTime },
                    { sourceStep: 4, sourceName: 'activity duration', sourceValue: this.lesson.activityTime },
                    { sourceStep: 4, sourceName: 'objective', sourceValue: this.lesson.objectives },
                    { sourceStep: 4, sourceName: 'materials', sourceValue: this.lesson.materials },
                    { sourceStep: 4, sourceName: 'vocabulary words', sourceValue: this.lesson.keyVocabularyWords },
                    { sourceStep: 4, sourceName: 'implementation steps', sourceValue: this.lesson.implementationSteps },
                    { sourceStep: 4, sourceName: 'typical behaviors', sourceValue: this.lesson.typicalBehaviors },
                    { sourceStep: 4, sourceName: 'UDL strategies', sourceValue: this.lesson.universalDesignForLearning },
                    { sourceStep: 4, sourceName: 'CLR strategies', sourceValue: this.lesson.culturallyResponsiveInstruction },
                    { sourceStep: 4, sourceName: 'at-home activities', sourceValue: this.lesson.homeActivity },
                    { sourceStep: 4, sourceName: 'age tip', sourceValue: '-' },
                    { sourceStep: 4, sourceName: 'CLR sources', sourceValue: sourceValue }]

                // 更新数据
                this.$store.dispatch('curriculum/updatePromptSourcesList', {
                    step: 3,
                    sources: sources
                })
            }
        },
        autoMagic(){
          if (!this.allowPublicMagic && this.isMC) {
              this.$message.warning(this.$t('loc.limitOneMagicPublish'))
              return
            }
            this.$confirm(`<p>Are you sure you're ready to submit?</p>
                <p>Once you click 'Confirm', your submission is final, and no changes can be made.</p>
                <p>You only get one chance, so make it count!</p>`,'Confirmation',{
                confirmButtonText: 'Confirm',
                cancelButtonText: 'Cancel',
                dangerouslyUseHTMLString: true
              })
            .then(async() => {
                this.next(true, true)
            })
        },
        // 下一步
        async next (completeAll, sharedMagic, needRecordVersion = false) {
            if (completeAll) {
                this.$analytics.sendEvent('web_unit_create3_publish')
                this.$analytics.sendEvent('cg_unit_create_publish')
            } else {
                if (this.isCenterLesson) {
                    this.$analytics.sendEvent('web_unit_create3_center_det_next_lesson')
                } else {
                    this.$analytics.sendEvent('web_unit_create3_group_det_next_lesson')
                }
                // 获取是否需要提示批量生成课程弹窗
                const showBatchGenerateLessonTip = JSON.parse(localStorage.getItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP'))
                // 如果存在批量生成课程提示，则显示提示
                if (showBatchGenerateLessonTip) {
                  this.$refs.lessonMenuRef.batchGenerateVisible = true
                  this.isNext = true
                  return
                }
            }
            // 校验当前课程是否缺失信息
            if (!await this.validateLessonInfo()) {
                return
            }
            if (this.lesson) {
                this.$set(this.lesson, 'confirmed', true)
            }
            // 设置是否完成
            this.completeAll = completeAll
            // 设置分享
            this.sharedMagic = sharedMagic
            // 更新课程
            await this.updateLesson(null, null, null, null, null, needRecordVersion)
            // 下一个课程
            let nextLessonItem = this.$refs.lessonMenuRef.getNextLesson()
            let nextUnconfirmedLesson = this.$refs.lessonMenuRef.getNextUnconfirmedLesson()
            if (nextLessonItem) {
                let nextLesson = nextLessonItem.lesson
                // 切换为新的课程
                this.$refs.lessonMenuRef.changeLesson(nextLessonItem, nextLesson)
                return
            }
            // 检查后面是否还有课程
            if (nextUnconfirmedLesson) {
                this.$refs.lessonMenuRef.selectLesson(nextUnconfirmedLesson)
                return
            }
            // 所有课程都已完成--增加判断 Magic 环境快速发布, 不执行跳转
            if (completeAll && (!this.isMC || !sharedMagic)) {
                // this.$message.success('Published successfully!')
                // 发送点击完成单元埋点事件
                this.$analytics.sendEvent('cg_unit_create_lesson_complete')
                this.$router.push({
                    name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail',
                    query: {
                        unitId: this.unit.id,
                        isComplete: true
                    }
                })
            }
        },

        // 下一周
        nextWeek () {
            if (this.lesson) {
                this.$set(this.lesson, 'confirmed', true)
                // 更新课程
                this.updateLesson(null, null, null, null, null, true)
            }
            if (!this.unconfirmedLessonOverviewWeeklyPlan) {
                return
            }
            this.$router.push({
                name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-overview-cg-designer' : 'lesson-overview-cg' : 'lessonOverview',
                params: {
                    unitId: this.unit.id,
                    week: this.unconfirmedLessonOverviewWeeklyPlan.week
                }
            })
        },
        // 上一步
        back () {
            if (this.isCenterLesson) {
                this.$analytics.sendEvent('web_unit_create3_center_det_back')
            } else {
                this.$analytics.sendEvent('web_unit_create3_group_det_back')
            }
            let routerName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-overview-cg-designer' : 'lesson-overview-cg' : 'lessonOverview'
            if (this.unit.adaptedType) {
              routerName = 'lesson-overview-cg-adapt'
            }
            this.$router.push({
              name: routerName,
              params: {
                unitId: this.unit.id,
                week: 1
              }
            })
        },

        // 更新在三方平台显示的数据
        async updateUnitMeta () {
          if (!this.unit) return
          // 根据不同值走不同逻辑
          let variant = 'b'
          // 如果没有图片生成，直接返回
          if (!this.$refs.imageGeneratorRef) {
            return
          }
          const imageInfo = await this.$refs.imageGeneratorRef.uploadImage(false)
          if (!imageInfo) {
            return
          }
          let image = imageInfo.url
          const meta = {
            type: 'website',
            title: this.unitTitle,
            description: this.unitDescription,
            image: image,
            siteName: 'Learning Genie',
            variant: variant
          }
          // 发送消息到父窗口
          this.$bus.$emit('message', { event:'UPDATE_META', meta: meta })
        },
        // 隐藏中途退出提示弹窗
        hideUserGuide () {
          localStorage.setItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_EXIT_TIP', false)
          // 发起请求隐藏批量生成课程提示
          let result = { 'features': ['SHOW_BATCH_GENERATE_LESSON_EXIT_TIP'] }
          this.$axios.post($api.urls().hideGuide, result).then()
        },
        // 进入 Center Activity 详情
        toCenterActivityDetail () {
            // 跳转到 Center Activity 详情生成页埋点
            this.$analytics.sendEvent('web_unit_create3_group_det_center')
            let centerItem
            let isFindOne = false
            // 遍历当前 Unit 的周计划列表
            for (let i = 0; i < this.weeklyPlans.length; i++) {
                // 获取周计划下的 Centers 列表
                const centerItems = this.weeklyPlans[i].centerItems
                // 判断 CenterItems 是否存在并且存在元素
                if (centerItems && centerItems.length > 0) {
                    // 遍历 CenterItems 列表
                    for (let j = 0; j < centerItems.length; j++) {
                        // 获取具体的 Center 项
                        const center = centerItems[j]
                        // 如果当前的 center 是未确认的或者是确认过了但是没有生成课程的，则跳转到该 center 的详情页
                        if (!center.confirmed || (center.confirmed && !center.lessonId)) {
                            centerItem = center
                            isFindOne = true
                        }
                    }
                }
                // 判断是否找到符合要求的 center
                if (isFindOne) {
                    break
                }
            }
            // 判断当前 center 是否已经确认过了，如果已经确认过，则直接跳转至 Center 详情页即可
            if (centerItem.confirmed) {
              const routeParams = {
                  unitId: this.unit.id,
                  itemId: centerItem.id
              }
              this.$router.push({
                  name: 'centerLessonDetail',
                  params: routeParams
              })
            } else {
                // 设置当前的 Center 已经确认
                this.$set(centerItem, 'confirmed', true)
                const params = {
                    items: [centerItem],
                    frameworkId: this.unit.baseInfo.frameworkId,
                    planId: centerItem.planId,
                    clearItemLesson: true, // 清除课程信息
                    unitId: this.unit.id // 单元 ID
                }
                // 重新判断当前 Unit 的生成进度
                params.progress = this.reJudgeUnitGenerateProgress()
                // 发起请求更新当前 Center
                this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
                    const routeParams = {
                        unitId: this.unit.id,
                        itemId: centerItem.id
                    }
                    this.$router.push({
                        name: 'centerLessonDetail',
                        params: routeParams
                    })
                })
            }
        },
        batchGenerateLessons () {
            this.$bus.$emit('close-resources-guide')
            // 点击批量生成课程按钮埋点
            this.$analytics.sendEvent('cg_unit_create_lesson_batch')
            this.$refs.lessonMenuRef.batchGenerateUnitLessons(true)
        },
         /**
       * 生成准备时间和活动时间的选项
       */
      generateTimes () {
        let result = []
        let unit = 'mins'
        for (let value = 5; value < 65; value += 5) {
          result.push({
            name: `${value} ${unit}`,
            value: `${value}`
          })
        }
        return result
      },
      closeUnitCompletedDialog () {
        this.showUnitCompletedDialog = false
      },

      /**
       * 关闭历史记录抽屉
       */
      closeHistoryDrawer () {
        this.showHistoryDrawer = false
      },

      /**
       * 打开历史记录抽屉
       */
      async openHistory() {
        if (equalsIgnoreCase(this.unit.adaptedType, 'LIGHT')) {
          this.$analytics.sendEvent('cg_adapt_lesson_history_clicked')
        }
        // 正在保存中则跳过
        if (this.updateLoading) {
          return
        }
        // 检查是否有未保存的修改
        if (this.hasUnsavedChanges()) {
          try {
            // 执行保存操作并等待完成
            await this.updateLesson(false, false, true, null, null, true)
            // 保存完成后再打开历史抽屉
            this.openHistoryDrawer();
          } catch (error) {
            // 保存失败处理
            // this.$message.error(this.$t('loc.saveFailedCannotOpenHistory'));
          }
        } else {
          // 没有未保存的更改，直接打开历史抽屉
          this.openHistoryDrawer();
        }
      },

      /**
       * 实际打开历史抽屉的方法
       */
      openHistoryDrawer() {
        if (!this.lesson.id) {
          this.$message.error(this.$t('loc.cannotViewHistoryVersion'));
          return;
        }
        this.showHistoryDrawer = true;
      },

      /**
       * 检查是否有未保存的更改
       */
      hasUnsavedChanges() {
        // 如果没有保存过的课程数据，则认为有未保存的更改
        if (!this.lastSavedLesson) {
          return true
        }
        // 比较上一次保存的课程数据和当前课程数据是否相等
        return !LessonUtils.equalsLesson(this.lastSavedLesson, this.lesson)
      },

      /**
       * 处理恢复历史版本
       *
       * @param detail 历史版本数据
       * @param sourceVersionId 源版本ID
       */
      async handleRestoreHistory(detail, sourceVersionId) {
        // 1. 暂存原始数据，以便恢复失败时回退
        const originalLesson = JSON.parse(JSON.stringify(this.lesson))
        try {
          // 设置加载状态
          this.updateLoading = true

          // 2. 使用历史版本数据更新当前课程
          this.restoreHistoryVersionData(detail)

          // 3. 保存恢复的版本，添加sourceVersionId和描述
          await this.updateLesson(false, false, false, null, null, true, {
            sourceVersionId: sourceVersionId,
          })

          // 4. 更新UI和相关状态
          this.showHistoryDrawer = false

          // 触发恢复完成事件，通知子组件操作已完成
          this.$refs.historyDrawer.$emit('restore-complete')
        } catch (error) {
          // 处理恢复失败的情况
          this.$message.error(this.$t('loc.lessonRestoreFailed'));

          // 恢复原始数据
          this.lesson = JSON.parse(JSON.stringify(originalLesson));

          // 关闭加载状态
          this.updateLoading = false;
        }
      },

      /**
       * 恢复历史版本数据
       *
       * @param detail 历史版本数据
       */
      restoreHistoryVersionData(detail) {
        // 保留当前课程的ID
        const lessonId = this.lesson.id;

        // 设置课程ID
        this.$set(this.lesson, 'id', detail.id);
        // 设置课程名称
        this.$set(this.lesson, 'name', detail.name);
        // 设置封面
        this.$set(this.lesson, 'coverMedias', detail.coverMedias);
        // 更新页面上的封面
        if (detail.coverMedias && detail.coverMedias[0]) {
          let cover = {
            id: detail.coverMedias[0].id,
            url: detail.coverMedias[0].url,
            source: detail.coverMedias[0].source
          }
          this.$refs.lessonDetailRef && this.$refs.lessonDetailRef.updateLessonCover(cover)
        }

        // 设置准备时长，添加"minutes"后缀
        if (detail.prepareTime) {
          this.$set(this.lesson, 'prepareTime', detail.prepareTime + " minutes");
        }

        // 设置活动时长，添加"minutes"后缀
        if (detail.activityTime) {
          this.$set(this.lesson, 'activityTime', detail.activityTime + " minutes");
        }

        // 设置框架信息
        if (detail.framework) {
          this.$set(this.lesson, 'frameworkId', detail.framework.id);
        }

        // 设置测评点
        if (detail.measures && detail.measures.length) {
          const measureAbbreviations = [];
          for (const measure of detail.measures) {
            measureAbbreviations.push(measure.abbreviation);
          }
          this.$set(this.lesson, 'measures', measureAbbreviations);
        }

        // 设置课程目标
        if (detail.objectives && detail.objectives.length) {
          this.$set(this.lesson, 'objectives', detail.objectives.join("\n"));
        }

        // 设置课程材料
        if (detail.materials && detail.materials.descriptions) {
          const materialDescription = detail.materials.descriptions.join("\n")
          detail.materials.description = materialDescription
          this.$set(this.lesson, 'materials', materialDescription)
          this.$set(this.lesson, 'materialFiles', detail.materials)
        }

        // 设置活动类型
        this.$set(this.lesson, 'activityType', detail.activityType);
        // 设置活动主题
        this.$set(this.lesson, 'activityTheme', detail.activityTheme);

        // 设置DLL关键词
        if (detail.dlls && detail.dlls.length) {
          const keyWords = [];
          for (const dll of detail.dlls) {
            let keyword = dll.content;
            if (dll.description && dll.description.trim()) {
              keyword = keyword + ": " + dll.description;
            }
            keyWords.push(keyword);
          }
          this.$set(this.lesson, 'keyVocabularyWords', keyWords.join("\n"));
        }

        // 设置步骤信息
        if (detail.steps && detail.steps.length) {
          // 获取第一个步骤
          const step = detail.steps[0];

          // 设置步骤内容
          this.$set(this.lesson, 'implementationSteps', step.content);

          // 设置差异教学内容
          if (step.universalDesignForLearning && step.universalDesignForLearning.trim()) {
            this.$set(this.lesson, 'universalDesignForLearning', step.universalDesignForLearning);
          }

          // 设置文化差异内容
          if (step.culturallyResponsiveInstruction && step.culturallyResponsiveInstruction.trim()) {
            this.$set(this.lesson, 'culturallyResponsiveInstruction', step.culturallyResponsiveInstruction);
          }

          // 设置家庭活动
          if (step.homeActivity && step.homeActivity.trim()) {
            this.$set(this.lesson, 'homeActivity', step.homeActivity);
          }

          // 设置分组信息
          this.$set(this.lesson, 'universalDesignForLearningGroup', step.universalDesignForLearningGroup);
          this.$set(this.lesson, 'culturallyResponsiveInstructionGroup', step.culturallyResponsiveInstructionGroup);
          this.$set(this.lesson, 'udlGroups', step.udlGroups);
          this.$set(this.lesson, 'groupId', step.groupId);

          // 设置典型行为
          if (step.lessonStepGuides && step.lessonStepGuides.length) {
            const typicalBehaviors = [];
            for (const guide of step.lessonStepGuides) {
              typicalBehaviors.push({
                measureAbbreviation: guide.measureAbbreviation,
                typicalBehaviors: guide.typicalBehaviors,
                mapped: guide.mapped
              });
            }
            this.$set(this.lesson, 'typicalBehaviors', typicalBehaviors);
          }

          // 设置CLR资源
          if (step.lessonSource && step.lessonSource.trim()) {
            try {
              const lessonSourceResponse = JSON.parse(step.lessonSource);
              if (lessonSourceResponse) {
                this.$set(this.lesson, 'lessonClrAndSources', lessonSourceResponse);
              }
            } catch (e) {
              console.error('Failed to parse lessonSource:', e);
            }
          }

          // 设置实施步骤资源
          if (step.lessonImpStepAndSourceStr && step.lessonImpStepAndSourceStr.trim()) {
            try {
              const lessonImpStepAndSource = JSON.parse(step.lessonImpStepAndSourceStr);
              if (lessonImpStepAndSource) {
                this.$set(this.lesson, 'lessonImpStepAndSource', lessonImpStepAndSource);
              }
            } catch (e) {
              console.error('Failed to parse lessonImpStepAndSourceStr:', e);
            }
          }

          // 设置测试题
          this.$set(this.lesson, 'questions', step.questions);

          // 设置标准教学指导
          this.$set(this.lesson, 'teachingTips', step.teachingTips);

          // 设置课程模板
          this.$set(this.lesson, 'lessonTemplate', step.lessonTemplate);

          // 设置课程模板类型
          this.$set(this.item, 'lessonTemplateType', step.lessonTemplate ? step.lessonTemplate.templateType : null);

          // 设置课程模板类型
          if (step.lessonTemplate) {
            this.$set(this.lesson, 'templateType', step.lessonTemplate.templateType);
          }

          // 设置SOR模型
          if (step.lessonScienceOfReadingModel) {
            this.$set(this.lesson, 'lessonScienceOfReadingModel', step.lessonScienceOfReadingModel);
          }
        }

        // 设置校训
        this.$set(this.lesson, 'learnerProfiles', detail.learnerProfiles)

        // 设置年龄段
        if (detail.ages && detail.ages.length) {
          this.$set(this.lesson, 'ageGroup', detail.ages.join(", "));
        }

        // 设置课堂类型
        this.$set(this.lesson, 'classroomType', detail.classroomType || 'IN_PERSON');

        // 设置是否已确认
        this.$set(this.lesson, 'confirmed', detail.generateStatus && detail.generateStatus.toUpperCase() === "CONFIRMED");

        // 恢复原始ID
        this.$set(this.lesson, 'id', lessonId);
      }
    }
}
</script>

<style lang="less" scoped>
 .enhance-btn {
    background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
    border-color: none !important;
    border: 0 !important;
    color: #FFFFFF !important;
    font-size: 14px;
}
.el-button.el-button--primary.is-disabled.is-plain {
    background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
    color: #FFFFFF !important;
    border: none !important;
    cursor: not-allowed;
}
.operation-panel {
    margin-right: -10px;
    height: 100% !important;
}
.operation-panel-normal {
    width: 320px;
    flex-shrink: 0;
}
.unit-review-container {
    min-height: 0;
}
.lesson-detail-container {
    //margin-right: -20px;
    //padding-right: 20px;
}
.action-container {
    position: sticky;
    bottom: 0;
    padding: 10px 0;
    z-index: 999;
}
.group-instruction-menu-card {
    width: 400px;
    flex-shrink: 0;
}
ul, li {
    list-style: disc;
}
::v-deep {
    .el-form-item__label {
        margin-bottom: 0;
        line-height: 22px;
        font-weight: 600;
    }
    .el-form-item__error {
        padding-left: 0;
    }
    .menu-card {
      border-radius: 8px;
      overflow: hidden;
    }
    .menu-card .el-card__body {
        padding: 4px 0 0 0;
        background: linear-gradient(180deg, #F9F5FF 0%, #FFFFFF 5.95%);
    }
    .menu-card-light {
      -webkit-box-shadow: none;
      box-shadow: none;
    }
}
.back-style {
  width: 20px;
  height: 20px;
}
.batch-generate {
  right: 10px;
  top: -5px;
}
/deep/ .leave-confirm-dialog {

  & .el-dialog__body {
    padding: 10px 20px;
  }

  & .el-tag.el-tag--info {
    color: #676879;
  }
}
/deep/ .el-input__count {
  line-height: 20px !important;
}
/deep/ .update-lesson-measure-select {
 .el-select-dropdown {
    width: 560px;
  }
}
/deep/ .unit-review-confirmation {
  & .el-dialog__body {
    padding: 14px 20px;
    font-size: 16px;
  }
}

@media screen and (max-width: 768px) {
    .operation-panel-normal {
        width: 100%;
        margin-bottom: 16px;
    }
    .el-form-item__error {
        padding-left: 0;
    }
    .unit-review-container {
        flex-direction: column !important;
    }
    .menu-card .el-card__body {
        padding: 4px 0 0 0;
    }
    .lesson-detail-container {
        margin-left: 0 !important;
        margin-right: 0;
        padding-right: 0;
    }
    .menu-card {
        margin-right: 0;
    }

}

 .light-overview {
   color: var(--111-c-1-c, #111C1C);
   font-size: 16px;
   font-weight: 500;
   line-height: 26px;
   height: 54px;
   padding: 0 12px;
   display: flex;
   justify-content: space-between;
   align-items: center;

   i {
     color: #10B3B7;
     font-weight: 500;
     cursor: pointer;
   }
 }

 .unit-planner-over-view-dialog {
   /deep/ .el-dialog {
     margin: 0!important;
     position: absolute;
     top: 50%;
     left: 50%;
     transform: translate(-50%, -50%);
     height: 95%;
     display: flex;
     flex-direction: column;
   }
   /deep/ .el-dialog__body {
     overflow-y: auto;
     color: #606266;
     word-break: break-all;
     flex: 1;
     display: flex;
     flex-direction: column;
     padding: 16px 24px 0px 24px;
     font-size: 14px;
   }

   /deep/ .el-dialog__title {
     font-style: normal;
     font-weight: 600;
     font-size: 20px;
     line-height: 26px;
     color: #323338;
   }

   /deep/ .el-dialog__header {
     text-align: left;
     padding: 24px 24px 0;
   }

   /deep/ .el-dialog__footer {
     padding-top: 0px;
   }
 }
</style>
<style lang="less">
.el-popper.adapt-lesson-popover {
  background: var(--color-ai-assistant);
  color: var(--color-white);
  padding: 24px;
  border: none;
  z-index: 999 !important;

  &.el-popper[x-placement^=left] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=bottom] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }

  img {
    width: 100%;
  }

  .el-button {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
    color: var(--color-ai-assistant);
  }

  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
  }

  .el-button--text, .el-button--text:hover {
    color: #FFFFFF;
    background: transparent;
    border: none;
  }
}
.update-standards-and-regenerate-dialog {
  .el-dialog__body {
    padding: 24px;
  }
  .el-dialog__header {
    padding-bottom: 0px;
    .el-dialog__title {
      font-size: 20px!important;
    }
  }

  .el-dialog__footer {
    padding-top: 0px;
  }
}
.lg-color-disabled {
  pointer-events: none;
  cursor: not-allowed;
}
</style>
