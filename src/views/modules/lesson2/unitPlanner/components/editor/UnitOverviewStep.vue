<template>
  <div>
    <!-- 头部 -->
    <div class="display-flex justify-content-between align-items m-b-sm">
      <!-- 中间标题 -->
      <div class="flex-1 text-center">
        <!-- <span class="font-size-18 font-weight-semibold">{{ unitInfo.title }}</span> -->
      </div>
      <!-- 右侧操作 -->
      <div class="flex-1 text-right">
      </div>
    </div>
    <div ref="promptPopoverRef" style="margin-left: 2px">
      <el-row :class="{'mobile-flex-column': isMobile}">
        <!-- 左侧信息填写部分 -->
        <el-col v-show="isShowLeftPanel" id="unit-info-card-id" :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="unit-info-card" :class="{'mobile-full-width': isMobile}">
          <!-- 单元基本信息 -->
          <el-card id="unit-info-form-card" style="box-shadow: unset!important;" :style="{ 'max-height': formHeight }" class="lg-scrollbar-show" :class="{'mobile-margin': isMobile}">
            <UnitInfoForm
                @setGuide="setGuide"
                @validateAllowGenerate="validateAllowGenerate"
                ref="unitInfoFormRef"
                :showPromptPanel="isShowLeftPanel"
                @updateRubrics="updateSelectedRubrics"
            />
          </el-card>
          <!-- Prompt 调试 -->
          <CollapseCard class="m-t-sm" :theme="'no-header'" :collapsible="false" v-show="promptDebugging">
            <!-- 标题 -->
            <template slot="header">
              <div class="display-flex justify-content-between">
                  <div class="text-success">
                      <span class="font-size-18 font-bold">Prompt Setting</span>
                  </div>
                  <div>
                      <PromptModelSelect />
                  </div>
              </div>
            </template>
            <!-- 内容 -->
            <PromptVersionEditor
              ref="promptEditorRef"
              :scenes="['UNIT_OVERVIEW']"
              :showUpdatePromptBtn="true"
              :showGenerateBtn="true"
              :generateFun="generatePromptContent"
              @batchGenerateResultsSuccess="batchGenerateResultsSuccess"
              @updatePromptId="updatePromptId"
            />
          </CollapseCard>
          <el-popover
              :disabled="isLessonPlanNewUser"
              placement="top-start"
              width="400"
              trigger="manual"
              height="110"
              :visible-arrow="false"
              :offset="generateButtonPopoverOffset"
              v-model="showFirstGenerateGuide"
              ref="generateButtonPopoverRef"
              popper-class="generate-detail-lesson-guide">
              <div class="guide-content">
                <img :src="require('@/assets/img/unit/unit_first_generate.png')" class="guide-img">
                <i class="el-icon-close close-icon" @click="isCloseGenerateDetailGuide = true"></i>
              </div>
              <div slot="reference" class="m-t-sm sticky-bottom" v-show="!promptDebugging" :class="{'mobile-margin': isMobile}">
            <!-- 生成课程信息  v-if="!unitOverview.overview" -->
            <el-button type="primary"
                       v-if="!isLessonPlanNewUser"
                      class="w-full mt-2 generate-style"
                      ref="generateButton"
                      :loading="generateUnitOverviewLoading"
                      @click="generateUnitCustomOverviewValidate(false)"
                       :disabled="magicIsDisabledGenerate || generateUnitOverviewLoading">
              <template #icon>
                <i class="lg-icon lg-icon-generate font-bold"></i>
              </template>
              {{$t('loc.unitPlannerStep1Generate')}}
            </el-button>
          </div>
            </el-popover>

        </el-col>
        <el-col :xs="24" :sm="isShowLeftPanel ? 16 : 24" :md="isShowLeftPanel ? 16 : 24" :lg="isShowLeftPanel ? 16 : 24" :xl="isShowLeftPanel ? 16 : 24" :class="{'mobile-full-width': isMobile}">
          <div class="w-full">
            <!-- 单元概览 -->
            <el-card :class="[isMobile ? 'mobile-margin' : 'm-l-md']" style="box-shadow: unset!important;">
              <!-- 切换按钮 -->
              <div class="lg-tabs-small m-b-sm"
                  v-show="promptDebugging && promptId && $refs.promptStatsResultRef && $refs.promptStatsResultRef.usageRecords.length > 0">
                <el-tabs v-model="currentView">
                  <el-tab-pane :label="$t('loc.unitPlannerStep1UnitFoundations')" name="RESULT"></el-tab-pane>
                  <el-tab-pane label="Statistics Validation" name="STATS_RESULT"></el-tab-pane>
                </el-tabs>
              </div>
              <!--feedback 的按钮所在-->
                <!--反馈-->
                <FeedbackForm class="feedback-form"
                              :defaultFeedbackResult="defaultFeedbackResult"
                              :feedbackStyle="setFeedbackStyle"
                              :showFeedback="showFeedback"
                              :showIcon="true"
                              :showClose="false"
                              :feedbackTitle="feedbackTitle"
                              :feedbackSubmit="feedbackSubmit"
                              :needFeedbackLevel="false"
                              :feedbackInputPlaceholder="feedbackInputPlaceholder"
                              :promptUsageRecordIds="promptUsageRecordIds"
                              @clickFeedback="clickFeedback"
                              :showReminders="false"/>
              <!-- 空白页面 -->
              <EmptyCard v-if="!unitGenerated && currentView === 'RESULT' && isShowLeftPanel"
                         @start-now="generateUnitCustomOverviewValidate(false)"
                         :isFullScreen="false"
                         :showCard="false"
                         :generateUnitOverviewLoading="generateUnitOverviewLoading"
                         :magicIsDisabledGenerate="magicIsDisabledGenerate"
                         :aiTips="aiTips"
                         :isLessonPlanNewUser="isLessonPlanNewUser"
                         :shouldShowGuide="showFirstGenerateGuide && isLessonPlanNewUser"></EmptyCard>
              <!-- 概览详情 -->
              <UnitOverviewDetail v-else v-loading="false" v-show="currentView === 'RESULT'"
                                  ref="unitOverviewDetailRef"
                                  @regenerate="generateUnitOverviewValidate"
                                  @generateCover="generateUnitCover"
                                  @setCoverInfo="setUnitCoverInfo"
                                  @updateUnitOverview="updateUnitOverview"
                                  :loading="generateUnitOverviewLoading"
                                  :isCustomFoundation="isCustomFoundation"
                                  :customFoundationModule="customFoundationModule"
                                  :customFoundationData="customFoundationData"
                                  :unitInfo="curriculumUnit"
                                  :unitOverview="unitOverview" :editable="true"/>
              <!-- 统计列表 -->
              <PromptStatsResult
                  v-show="currentView === 'STATS_RESULT' && promptDebugging"
                  ref="promptStatsResultRef"
                  :promptId="promptId"
                  @testCompleted="testCompleted"
                  @testIncomplete="testIncomplete"
              />
            </el-card>
            <!-- 操作 -->
            <div class="display-flex justify-content bottom-btn-group" v-if="unitId && !aiTips && unitGenerated" :class="{'mobile-flex-column': isMobile}">
              <!-- 保存 -->
              <el-button type="default" class="font-weight-600 text-title-alt-color" plain @click="saveUnit()" :disabled="updateLoading || generateUnitOverviewLoading" :class="{'mobile-margin': isMobile}">{{$t('loc.unitPlannerStep1Save')}}</el-button>
              <!-- 重新生成 -->
              <el-button type='primary' class="enhance-btn" plain @click="openRedesignUnitDialog" :disabled="updateLoading || generateUnitOverviewLoading" :class="{'mobile-margin': isMobile}"><i class="lg-icon lg-icon-magic add-margin-r-4"></i>{{$t('loc.enhanceUnit')}}</el-button>
              <!-- 下一步 -->
              <el-button v-if="!lightAdapt" type="primary" @click="next()" :disabled="updateLoading || generateUnitOverviewLoading" :class="{'mobile-margin': isMobile}">{{$t('loc.unitPlannerStep1Next')}}</el-button>
              <el-button v-if="lightAdapt" type="primary"  @click="setPlannerPreviewDialogShow">Preview</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :title="$t('loc.enhanceUnit')"
      :close-on-click-modal="false"
      custom-class="regenerate-unit-dialog"
      :visible.sync="redesignUnitVisable"
      width="600px">
        <div>{{ $t('loc.regenerateUnitTip') }}</div>
        <div class="lg-margin-top-16 lg-maring-bottom-8 title-font-14 lg-margin-bottom-12">{{ $t('loc.enhanceIdeas') }}</div>
        <el-input
          type="textarea"
          :rows="5"
          :maxlength="2000"
          show-word-limit
          v-model="redesignIdea"
          :placeholder="$t('loc.regenerateUnitPlaceholder')">
        </el-input>
        <div slot="footer">
            <el-button plain @click="closeRedesignUnitDialog">{{ $t('loc.cancel') }}</el-button>
            <el-button class="enhance-btn" type="primary" @click="regenerateUnitOverview">{{ $t('loc.unitPlannerConfirmRegenerate') }}</el-button>
        </div>
    </el-dialog>

    <div class="custom-foundation">
      <el-dialog
        :title="$t('loc.confirmation')"
        :close-on-click-modal="false"
        :visible.sync="needTipCustomFoundationVisible"
        width="600px">
        <p>{{ $t('loc.unitPlannerUpFoundation1') }}</p>
        <p>{{ $t('loc.unitPlannerUpFoundation2') }}</p>
        <el-radio-group v-model="regCustomFoundationTipDialogOp" style="width: 100%;">
          <el-radio class="add-margin-t-12" :label="1">{{ this.$t('loc.unitPlannerKeepFoundation') }}</el-radio>
          <el-radio class="add-margin-t-12" :label="2">{{ this.$t('loc.unitPlannerNewModuleFoundation') }}</el-radio>
        </el-radio-group>
        <div slot="footer">
          <el-button plain @click="closeCurCustomFoundationTipDialog">{{ $t('loc.cancel') }}</el-button>
          <el-button type="primary" @click="regNewCustomFoundationTipDialog">{{ $t('loc.confirm') }}</el-button>
        </div>
      </el-dialog>
    </div>
    <div class="unit-planner-over-view-dialog">
        <el-dialog :visible="plannerPreviewDialogShow" :width="isMobile ? '95%' : '80%'"
                   :before-close="()=>{plannerPreviewDialogShow = false}" :title="'Preview'" :close-on-click-modal="false"
                   custom-class="loading-class"
                   :close-on-press-escape="false"
                   :fullscreen="isMobile">
          <!-- 仅展示核心测评点 switch end -->
          <UnitDetailOverview :unitId="unitId"
                              :isMobile="isMobile"
                              ref="unit-plan-overview"/>
         <!-- 操作 -->
          <span slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button @click="closePlannerPreviewDialog" :class="{'mobile-full-width': isMobile}">{{ $t('loc.close') }}</el-button>
          </span>
        </el-dialog>
      </div>

    <!-- Unit 生成时校验遮盖 -->
    <UnitStepOneLoadingDialog :dialogVisible.sync="unitStep1LoadingOpen"/>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { bus } from '@/utils/bus'
// 引导
import Driver from 'driver.js' // import driver.js
import 'driver.js/dist/driver.min.css' // import driver.js css
import steps from '@/assets/js/guide/createUnitPlannerPlugin'
import { aiApiUrl, serverlessApiUrl } from '@/utils/setBaseUrl'
import { equalsIgnoreCase } from '@/utils/common'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import tools from '@/utils/tools'
import Lessons2 from '@/api/lessons2'
import EmptyCard from './EmptyCard.vue'
import UnitOverviewDetail from './UnitOverviewDetail.vue'
import UnitInfoForm from './UnitInfoForm.vue'
import PromptVersionEditor from '@/views/curriculum/prompt/components/editor/PromptVersionEditor.vue'
import PromptModelSelect from '@/views/curriculum/prompt/components/editor/PromptModelSelect.vue'
import CollapseCard from '@/views/curriculum/components/card/CollapseCard.vue'
import PromptStatsResult from '@/views/curriculum/prompt/components/editor/PromptStatsResult.vue'
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import CurriculumApi from '@/api/lessons2/curriculum'
import { removeUnexpectedCharacters } from '@/utils/eventSource'
import { LESSON_PLAN_NEW_USER_UTC } from '../../../../../../utils/const'
import UnitStepOneLoadingDialog from './UnitStepOneLoadingDialog.vue'
import UnitDetailOverview from '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetailOverview.vue'

export default {
  name: 'UnitOverviewStep',
  components: {
    FeedbackForm,
    UnitInfoForm, // 单元基本信息卡片
    PromptVersionEditor, // Prompt 编辑器
    PromptModelSelect, // Prompt 模型选择
    EmptyCard,
    UnitOverviewDetail,
    CollapseCard, // 折叠卡片
    PromptStatsResult, // Prompt 统计结果
    UnitStepOneLoadingDialog,
    UnitDetailOverview
  },
  data () {
    return {
      showLeftPanel: true, // 控制左侧面板显示/隐藏
      unitNumber: '',
      unitGenerated: false, // 单元是否已经生成
      aiTips: '', // AI 提示信息
      unitOverviewRules: {}, // 单元概览校验规则
      generateUnitOverviewLoading: false, // 生成课程概览 Loading
      retryCount: 0, // 重试次数
      overviewData: '', // 课程概览数据
      updateLoading: false, // 更新 Loading
      evaluateUnitOverviewLoading: false, // 评估单元课程概览 Loading
      evaluateUnitOverviewResult: null, // 评估单元课程概览结果
      currentView: 'RESULT', // 显示视图
      promptId: null, // Prompt ID
      promptUsageRecordIds: [], // 消息回调的 promptUsageRecordId，后续用于反馈
      defaultFeedbackResult: {
        feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
        feedBackResult: '',
        feedbackData: {
          feedback: undefined,
          feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
          feedbackLevel: [0, 0, 0, 0]
        }
      },
      leavedPage: false, // 是否已经离开了页面
      isAllowGenerate: false, // 是否允许执行生成操作
      copyUnitOverview: {}, // 拷贝一份单元概览信息
      clientHeight: document.documentElement.clientHeight, // 浏览器高度
      cardHeight: 660, // 左侧表单所在的卡片高度，默认 660px
      promptHistory: {}, // Prompt 历史记录
      redesignUnitVisable: false, // 重新生成弹窗
      redesignIdea: '', // 重新生成理由
      hasLoadedUnit: false, // 是否已经加载过单元
      needTipCustomFoundationVisible: false, // 重新生成弹窗
      customFoundationGenerated: false, // 以自定义 Foundation 生成
      customFoundationModule: null, // 当前使用的自定义 Foundation 模板
      userNewFoundationModule: null, // 用户最新的自定义 Foundation 模板
      fixOverviewKeys: ['Unit Overview', 'Unit Trajectory', 'Concepts', 'Guiding Questions'], // 固定 Foundation 的模块名称
      customFoundationGenerateType: null, // null-固定模块生成 HISTORY_MODULE-单元历史数据模板 LATEST_MODULE-最新的模板
      regCustomFoundationTipDialogOp: 1, // 默认选中保持当前模块
      measureRecInfo: null, // 每次推荐结束后框架测评点的相关信息
      isMobile: false, // 是否是移动端
      isCloseGenerateDetailGuide: false, // 是否关闭生成详情引导
      generateButtonPopoverOffset: -200, // 生成按钮提醒框偏移量
      isComponentMounted: false, // 添加组件挂载状态标志
      hasCreatedUnit: true, // 是否已经创建过 Unit
      showFirstGenerateGuide: false, // 是否显示首次生成引导
      unitStep1LoadingOpen: false, // Unit 生成时校验遮盖
      plannerPreviewDialogShow: false, // 单元 overview 预览弹窗显示状态
    }
  },
  destroyed () {
    // 离开页面
    if (this.unitId) {
      this.leavedPage = true
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.checkDeviceType)
  },
  async created () {
    // 获取机构校训
    this.$store.dispatch('getAgencyLearnerProfile')
    bus.$on('unit_overview:generateParams', (callback) => {
      let unitId = this.defaultTestUnitId
      if (this.unitId) {
        unitId = this.unitId
      }
      callback({
        unitId: unitId
      })
    })
    // 检查 unitInfo 是否生成完，如果没有生成，则修改进度为 0
    if (!this.unitOverview || (!this.unitOverview.concepts && !this.unitOverview.guidingQuestions && !this.unitOverview.overview && !this.unitOverview.trajectory)) {
      this.unitInfo.progress = 10
    }
    if (this.unitInfo.progress >= 20) {
        this.showLeftPanel = false
    }
    // 检验是否允许生成
    this.validateAllowGenerate()

    // 如果路由中没有 unitId，默认调用一次
    if (!this.$route.params.unitId) {
      this.getUnitCustomFoundationModule()
    }

    // magic 环境判断是否可创建 unit
    if (this.isMC || this.isCurriculumPlugin) {
      try {
        const res = await this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
        // 统一转换大写
        let pathId = ((this.$route.params || {}).unitId || '').toUpperCase()
        // 需要额外处理可编辑的情况
        let isAllowCreated = res.success || (res.ids || []).includes(pathId)
        this.magicIsDisabledGenerate = isAllowCreated
        // 判断是否已经创建过 Unit
        this.hasCreatedUnit = res.ids && res.ids.length > 0
        if (!isAllowCreated && this.isMC) {
          this.$message.error('You have hit the maximum limit of 3 units for creation.')
        }
      } catch (error) {
      }
    }

    // 检测设备类型
    this.checkDeviceType()
    // 监听窗口大小变化
    window.addEventListener('resize', this.checkDeviceType)
  },
  async mounted () {
    // 进入第一步页面曝光事件埋点
    this.$analytics.sendEvent('web_unit_create1_exposure')
    // 组件加载完成之后拷贝一份单元信息
    this.$nextTick(() => {
      this.copyUnitOverview = JSON.parse(JSON.stringify(this.unitOverview))
      // 动态计算生成按钮弹框的偏移量
      this.calculateGenerateButtonPopoverOffset()
      this.isComponentMounted = true // 设置组件已挂载
    })
    window.onresize = () => {
      this.clientHeight = document.documentElement.clientHeight
    }
    // 从单元导入深度改编进来的直接开始生成
    if (this.$route.params && this.$route.params.generateUnit) {
        this.unitStep1LoadingOpen = true
        this.showLeftPanel = false
        // 生成单元课程概览
        await this.generateUnitOverviewStream()
    }
  },
  watch: {
    unitInfo: {
      deep: true,
      handler (baseInfo) {
        // 监听 Unit 表单对象，只有课程题目和描述填充之后才允许执行生成操作
        this.validateAllowGenerate()
      }
    },
    unitId: {
      handler (unitId) {
        // 设置扩展参数
        this.$store.dispatch('curriculum/setPromptExtensionParams', {
          unitId: unitId
        })
      },
      deep: true,
      immediate: true
    },
    unitOverview: {
      immediate: true,
      handler (unitOverview) {
        if (!this.isCustomFoundation) {
          this.unitGenerated = !!unitOverview.overview || !!unitOverview.trajectory || !!unitOverview.concepts || !!unitOverview.guidingQuestions || !unitOverview.coverMedias
        }
      }
    },
    customFoundationData: {
      immediate: true,
      handler (val) {
        if (this.customFoundationModule) {
          this.foundationUnitGenerated()
        }
      }
    },
    isCustomFoundation: {
      immediate: true,
      handler (val) {
        if (val) {
          this.foundationUnitGenerated()
        }
      }
    },
    curriculumUnit: {
      immediate: true,
      handler (newUnit) {
        if (newUnit && newUnit.id && !this.hasLoadedUnit) {
          this.hasLoadedUnit = true
          // 获取用户 Foundation 模板
          this.getUnitCustomFoundationModule()
          // 初始化测评点信息
          this.initInfoMeasureRecInfo()
        }
      }
    },
    clientHeight (newValue, oldValue) {
      if (newValue) {
        // 当浏览器视口的高度大于 850px 时，左侧表单的高度固定为 660px
        if (this.clientHeight > 850) {
          this.cardHeight = 660
        } else {
          // 当浏览器视口的高度小于 850px 时，左侧表单的高度为 660px 减去 850px 与当前视口高度的差值
          const height = 850 - this.clientHeight
          this.cardHeight = 560 - height
          // 当浏览器视口的高度小于 380 时，左侧表单的高度固定为 380px
          if (this.cardHeight < 380) {
            this.cardHeight = 380
          }
        }
      }
    },
    isShowLeftPanel: {
      immediate: true,
      handler(newVal) {
        // 当 isShowLeftPanel 变化时，发射事件通知父组件
        this.$emit('promptBtnVisibilityChange', newVal)
        this.$nextTick(() => {
          if (!newVal && this.$refs.unitInfoFormRef) {
            this.$emit('syncUnitInfoForm', this.$refs.unitInfoFormRef)
          }
        })
      }
    },
    generateUnitOverviewLoading: {
      immediate: true,
      handler (newVal) {
        this.$emit('promptLoadChange', newVal)
      }
    },
    // 检测显示生成详情引导的变化
    showCGUnitCreateGuide(newVal) {
      if (newVal) {
        // 获取 Unit Create Exemplar 分流结果
        let exemplarAbTestVariant = this.$analytics.getFeatureFlag('cg-unit-create-exemplar')
        // 现在默认是 test 组
        if (exemplarAbTestVariant == null) {
          exemplarAbTestVariant = 'test'
        }
        // 判断是否为 test 组
        let isExemplarTest = false
        if (exemplarAbTestVariant) {
          isExemplarTest = (exemplarAbTestVariant === 'test')
        }
        if (this.$refs.unitInfoFormRef) {
          if (!isExemplarTest) {
            // control组：自动填充exemplars
            // 生成按钮引导曝光
            this.$analytics.sendEvent('cg_unit_create_generate_guide')
            // 生成按钮引导显示
            this.showFirstGenerateGuide = true
            // 设置示例单元
            this.$refs.unitInfoFormRef.setExampleUnit(false)
          } else {
            // test组：高亮按钮并自动弹出 tooltip
            this.$refs.unitInfoFormRef.showExemplarsTip = true
            this.$refs.unitInfoFormRef.showExemplarsTipManual = true
            this.$analytics.sendEvent('cg_unit_create_exemplar_button_highlight')
            this.showFirstGenerateGuide = true
          }
        }
      } else {
        // 生成按钮引导隐藏
        this.showFirstGenerateGuide = false
        // 隐藏示例单元的 tooltip
        if (this.$refs.unitInfoFormRef) {
          this.$refs.unitInfoFormRef.showExemplarsTip = false
          this.$refs.unitInfoFormRef.showExemplarsTipManual = false
        }
      }
    }
  },
  computed: {
    ...mapState({
      agencyLearnerProfile: (state) => state.lesson.agencyLearnerProfile, // 当前机构校训
      currentUser: state => state.user.currentUser,
      unitId: state => state.curriculum.unit.id,
      gptSetting: state => state.curriculum.gptSetting,
      frameworks: state => state.curriculum.frameworks, // 框架列表
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      grades: state => state.curriculum.grades, // 年龄段列表
      promptDebugging: state => state.curriculum.promptDebugging, // Prompt 调试
      defaultTestUnitId: state => state.curriculum.defaultTestUnitId, // 默认测试单元 ID
      curriculumUnit: state => state.curriculum.unit, // 单元基本信息
      batchTasks: state => state.unit.batchTasks, // 批量生成任务
      isCG: state => state.curriculum.isCG, // 是否是 Curriculum Genie
      isMC: state => state.curriculum.isMC, // 是否是 MC
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 MC
      unitUsageModel: (state) => state.magicCurriculum.unitUsageModel, // unit 使用的功能模块
      magicCreatedUnits: state => state.magicCurriculum.magicCreatedUnits, // 已创建 unit
      magicAllowUnits: state => state.magicCurriculum.magicAllowUnits, // 允许创建的 unit 数量
      magicAllowCreateUnit: state => state.magicCurriculum.magicAllowCreateUnit, // 是否允许创建 unit
      showSearchCover: state => state.unit.showSearchCover // 是否显示搜索封面功能
    }),
    isShowLeftPanel () {
      // 计算结果
      return this.showLeftPanel && (!this.unitOverview || (!this.unitOverview.concepts && !this.unitOverview.guidingQuestions && !this.unitOverview.overview && !this.unitOverview.trajectory))
    },
    /**
     * 获取用户 id
     */
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    /**
     * 是否为新用户
     * @returns {boolean}
     */
    isLessonPlanNewUser () {
      return this.currentUser && this.currentUser.userInfo && tools.timeIsAfter(this.currentUser.userInfo.createdAtUtc, LESSON_PLAN_NEW_USER_UTC)
    },
    // 是否禁用
    magicIsDisabledGenerate: {
      get() {
        // 非 Magic 和 Curriculum Plugin 环境不需要判断
        if (!this.isMC && !this.isCurriculumPlugin) {
          return false
        }
        // 如果是插件平台
        const createdUnits = this.magicCreatedUnits
        if (this.isCurriculumPlugin && this.magicAllowUnits === 0 && createdUnits.length === 0) {
          return false
        } else if (this.isCurriculumPlugin && createdUnits.length >= this.magicAllowUnits) {
          return false
        }
        if (this.isCurriculumPlugin && this.magicAllowUnits === 2 && createdUnits.length >= this.magicAllowUnits) {
          return false
        }
        return !this.$store.state.magicCurriculum.magicAllowCreateUnit
      },
      set(value) {
        this.$store.commit('magicCurriculum/SET_MAGIC_ALLOW_CREATE_UNIT', value)
      }
    },
    unitInfo: {
      get () {
        return this.$store.state.curriculum.unit.baseInfo
      },
      set (value) {
        this.$store.commit('curriculum/SET_BAE_INFO', value)
      }
    },
    unitOverview: {
      get () {
        return this.$store.state.curriculum.unit.overview
      },
      set (value) {
        this.$store.commit('curriculum/SET_OVERVIEW', value)
        // 更新 store 中的 prompt source list 的信息
        this.updatePromptSourceList()
      }
    },

    /**
     * 是否是自定义 Foundation
     * @returns {boolean} true-是自定义 Foundation false-不是自定义 Foundation
     */
    isCustomFoundation () {
      // 非 FOLC 平台 为 false
      if (!this.isCG) {
        return false
      }

      // 数据本身就是根据自定义模板创建的 -- 有模板，且有数据
      if (this.customFoundationData && Object.keys(this.customFoundationData).length > 0 && this.customFoundationModule && this.customFoundationModule.length > 0) {
        return true
      }

      // 新生成且有模板为 true
      if ((!this.unitInfo || !this.unitOverview || !this.unitInfo.progress || this.unitInfo.progress < 20) && this.customFoundationModule && this.customFoundationModule.length > 0) {
        return true
      }

      // 原数据没有按自定义模板生成，此时如果主动按新模板生成且有模板
      if (this.customFoundationModule && this.customFoundationModule.length > 0 && this.customFoundationGenerated) {
        return true
      }

      return false // 默认情况
    },

    /**
     * 自定义 Foundation 模块生成的数据
     */
    customFoundationData: {
      get () {
        return this.$store.state.curriculum.unit.customFoundationData
      },
      set (value) {
        this.$store.commit('curriculum/SET_CUSTOM_FOUNDATION_DATA', value)
      }
    },

      feedbackTitle () {
          return this.$t('loc.unitPlannerFeedback')
      },
      feedbackInputPlaceholder () {
          return this.$t('loc.unitPlannerFeedbackPlaceholder')
      },
      feedbackSubmit () {
          return this.$t('loc.unitPlannerFeedbackSubmit')
      },
      showFeedback () {
          // 判断 promptUsageRecordIds 中的每一个值，即对应的子数组都是存在值的
          return !this.generateUnitOverviewLoading && !this.updateLoading && !this.evaluateUnitOverviewLoading && this.promptUsageRecordIds.length > 0
      },
    // 是否显示评估结果选项
    showEvaluateResult () {
      return this.evaluateUnitOverviewLoading || this.evaluateUnitOverviewResult
    },
    // 表单高度
    formHeight () {
      const path = this.$route.path
      // 是否显示步骤条
      let showSteps = path.includes('/weekly-plan-overview') || path.includes('/lesson-overview') || path.includes('/unit-overview')
      if (showSteps) {
        return `calc(100vh - 212px)`
      } else {
        return `calc(100vh - 178px)`
      }
    },
    // 是否显示 Unit 创建的引导
    showCGUnitCreateGuide: {
      get() {
        // 如果是 K12 Unit 平台，且没有关闭生成详情引导，且左侧面板显示，且没有创建过 Unit，且组件已挂载，则显示生成详情引导
        return this.isCurriculumPlugin && !this.isCloseGenerateDetailGuide && this.showLeftPanel && !this.hasCreatedUnit && this.isComponentMounted
      },
      set(value) {
      }
    },
    // 是否是轻量改编
    lightAdapt() {
      return equalsIgnoreCase(this.curriculumUnit.adaptedType, 'LIGHT')
    }
  },
  methods: {
    setPlannerPreviewDialogShow() {
      this.plannerPreviewDialogShow = true
      // 点击 Unit Overview 查看按钮
      this.$analytics.sendEvent('cg_adapt_foundation_preview_clicked')
    },
    // 关闭单元 overview 预览弹窗
    closePlannerPreviewDialog() {
      this.plannerPreviewDialogShow = false
    },
    // 检测设备类型
    checkDeviceType() {
      this.isMobile = window.innerWidth <= 768
      // 窗口大小变化时，等待 300ms 后更新 popper，解决窗口大小变化时，生成按钮弹框位置不正确的问题
      this.$nextTick(() => {
        if (this.$refs.generateButtonPopoverRef) {
          setTimeout(() => {
            this.$refs.generateButtonPopoverRef.updatePopper()
          }, 300)
        }
      })
    },

    // 从 UnitEditor 同步 UnitInfoForm 数据到当前组件
    syncUnitInfoForm(sourceUnitInfoForm) {
      if (sourceUnitInfoForm && this.$refs.unitInfoFormRef) {
          // 将源数据同步到当前组件的 unitInfo 中
          this.$refs.unitInfoFormRef.syncUnitInfoForm(sourceUnitInfoForm)
          // 如果需要更新表单验证状态，可以在同步后重新验证表单
          this.$nextTick(() => {
            this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validate();
          });
      }
    },

    // 获取 Unit 单元引导信息
    getOverviewStepGuide () {
      return new Promise((resolve, reject) => {
        // 获取用户是否需要引导
        const firstVisit = localStorage.getItem('CREATE_UNIT_GUIDE_FIRST_VISIT' + this.currentUserId)
        // 如果没有缓存，则请求接口
        if (!firstVisit) {
          this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
            if (result) {
              this.showCurriculumCreateUnitGuide = result.showCurriculumCreateUnitGuide
              resolve(result.showCurriculumCreateUnitGuide)
            }
          }).catch((err) => {
            reject(err)
          })
        } else {
          this.showCurriculumCreateUnitGuide = false
          resolve(false)
        }
      })
    },
    /**
     * 设置引导信息
     */
    async setGuide () {
      // 获取引导信息
      const firstVisit = await this.getOverviewStepGuide()
      // 如果引导过了，则不再显示。移动端暂时不显示引导
      //设置 k12 unit planner 引导 也不展示引导了，对用户来说，引导信息过多
      if (this.isCurriculumPlugin || !firstVisit || this.isMobile) {
        return
      }
      // 创建 style 元素
      const sheet = new CSSStyleSheet()
      // 插入规则
      sheet.insertRule(`
        .driver-popover-description {
          padding: 12px 0px 0px 15px;
        }
      `, 0)

      sheet.insertRule(`
        #effortless-unit-setup {
          display: flex;
          position: absolute;
          right: 7px;
          top: 13px;
        }
      `, 1)

      // 将样式表附加到文档
      document.adoptedStyleSheets = [...document.adoptedStyleSheets, sheet];
      // 添加一个需要仅在引导结束后移除的样式表
      let styleSheet;
      // 创建引导插件对象
      this.driver = new Driver({
        className: 'guide-style',
        opacity: 0.5, // 屏蔽罩透明度
        allowClose: false,
        closeBtnText: this.$t('loc.planGuideSkip'),
        nextBtnText: this.$t('loc.planGuideNext'),
        doneBtnText: this.$t('loc.planGuideDone'),
        onHighlightStarted: (element) => {
          // 高亮开始时执行的方法
          // 获取 unit-info-card-id 元素
          const unitInfoCard = document.getElementById('unit-info-card-id')
          // 将当前元素的 position 修改为 relative
          unitInfoCard.style.position = 'relative'
          // 获取 element 的 node
          const node = element.node
          if (node.id === 'description-assistant') {
            this.$analytics.sendEvent('cg_unit_create_onenext')
          } else if (node.id === 'standard-alignment') {
            this.$analytics.sendEvent('cg_unit_create_twonext')
          } else if (node.id === 'localized-curriculum') {
            this.$analytics.sendEvent('cg_unit_create_threenext')
          }
          // 如果元素的 ID 是 localized-curriculum，需要将 driver-close-btn 元素隐藏
          if (node.id === 'localized-curriculum') {
            // driver-close-btn 元素
            styleSheet = new CSSStyleSheet();
            sheet.insertRule(`
              .driver-close-btn {
                display: none !important;
              }
            `, 0);
            document.adoptedStyleSheets = [...document.adoptedStyleSheets, sheet];
          }
        },
        onDeselected: (element) => {
          // 取消选中元素
          document.adoptedStyleSheets = document.adoptedStyleSheets.filter(style => style !== styleSheet);
        },
        // 所有引导步骤执行后后执行的方法
        onReset: (element) => {
          // 获取 element 的 node
          const node = element.node
          // 按照不同的 ID 发送不同的事件
          if (node.id === 'effortless-unit-setup') {
            this.$analytics.sendEvent('cg_unit_create_oneskip')
          } else if (node.id === 'description-assistant') {
            this.$analytics.sendEvent('cg_unit_create_twoskip')
          } else if (node.id === 'standard-alignment') {
            this.$analytics.sendEvent('cg_unit_create_threeskip')
          } else if (node.id === 'localized-curriculum') {
            this.$analytics.sendEvent('cg_unit_create_gotit')
          }
          // 获取 unit-info-card-id 元素
          const unitInfoCard = document.getElementById('unit-info-card-id')
          // 将当前元素的 position 修改为 sticky
          unitInfoCard.style.position = 'sticky'
          // 如果引导结束，则设置引导过了
          // 设置缓存
          localStorage.setItem('CREATE_UNIT_GUIDE_FIRST_VISIT' + this.currentUserId, "true")
          // 隐藏引导过程
          let result = { 'features': ['PLUGIN_CREATE_UNIT_PLANNER_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then()
          // 解除禁用引导元素鼠标事件
          this.driver.steps.forEach(el => { el.node.style.pointerEvents = 'auto' })
          // 移除添加的样式表
          tools.dynamicRemoveStyleInDocument(sheet)
        }
      })
      this.$nextTick(() => {
        this.driver.defineSteps(steps)
        // 滚动页面到顶部
        this.scrollToTop()
        // 等待 200ms 后开始引导
        // 开始引导
        this.driver.start()
        // 禁用引导元素鼠标事件
        this.driver.steps.forEach(el => { el.node.style.pointerEvents = 'none' })
      })
    },

    /**
     * 滚动页面到顶部
     */
    scrollToTop () {
      let mainEles = document.getElementsByClassName('el-main')
      if (mainEles && mainEles.length > 0) {
        mainEles[0].scrollTop = 0
      }
      let leftCardEle = document.getElementById('unit-info-form-card')
      if (leftCardEle) {
        leftCardEle.scrollTop = 0
      }
    },

    initInfoMeasureRecInfo () {
      // 在单元初始化后填充
      this.measureRecInfo = {
        measureIds: this.unitInfo.measureIds,
        frameworkId: this.unitInfo.frameworkId,
        domainIds: this.unitInfo.domainIds,
        useDomain: this.unitInfo.useDomain
      }
    },
    // 自定义 Foundation 时 unitGenerated 更新
    foundationUnitGenerated () {
      if (!this.customFoundationModule || !this.customFoundationData || Object.keys(this.customFoundationData).length === 0) {
        return
      }
      // 生成成功判断，使用每个 key 进行判断，逻辑参考上面的判断
      const isAllValuesEmpty = this.customFoundationModule.every(
        (info) => {
          const value = this.customFoundationData[info.key]
          return value === null || value === undefined
        }
      )
      // 取反为 unitGenerated 值
      this.unitGenerated = !isAllValuesEmpty
    },
    // 动态获取单元基础信息标题,需要有 name、foundationId、sortIndex 字段
    async getUnitCustomFoundationModule () {
      // 获取当前单元数据
      if (this.curriculumUnit && this.curriculumUnit.customFoundationInfos && this.curriculumUnit.customFoundationInfos.length > 0) {
        // 从模块的完整信息中取出模块的基础信息
        this.customFoundationModule = this.curriculumUnit.customFoundationInfos.map(item => ({
          id: item.id,
          sortIndex: item.sortIndex,
          key: item.key,
          name: item.name,
          foundationId: item.foundationId
        }))
        this.customFoundationGenerated = true
        // 判断数据是否完整
        this.foundationUnitGenerated()
      }

      // 取用户最新的数据
      this.userNewFoundationModule = await this.getUserNewCustomFoundationModules()
      // 如果当前没有数据，直接赋值
      if (!this.customFoundationModule) {
        this.customFoundationModule = this.userNewFoundationModule
      }
    },

    /**
     * 获取用户最新的模板
     * @returns {Promise<*|null>}
     */
    async getUserNewCustomFoundationModules () {
      // 如果不是 FOLC 环境直接返回
      if (!this.isCG) {
        return null
      }
      try {
        // 查询用户最新的自定义 Foundation 模板
        const res = await CurriculumApi.getUserNewCustomFoundationModules()
        // 判断后返回实际数据
        if (res && res.data && res.data.length > 0) {
          return res.data
        }
      } catch (err) {
      }
      // 默认返回空数据
      return null
    },

    // 更新 prompt source list
    updatePromptSourceList () {
      // 定义一个资源数组
      const sources = [
        { sourceStep: 2, sourceName: 'unit overview', sourceValue: this.unitOverview.overview },
        { sourceStep: 2, sourceName: 'unit trajectory', sourceValue: this.unitOverview.trajectory },
        { sourceStep: 2, sourceName: 'concepts', sourceValue: this.unitOverview.concepts },
        { sourceStep: 2, sourceName: 'guiding questions', sourceValue: this.unitOverview.guidingQuestions }
      ]
      // 更新数据
      this.$store.dispatch('curriculum/updatePromptSourcesList', {
        step: 1,
        sources: sources
      })
    },
    // 设置 feedback 的样式
    setFeedbackStyle () {
      return {
        position: 'absolute',
        top: '-46px',
        right: '0px',
        zIndex: '2000'
      }
    },
    // 更新 Overview
    updateUnitOverview (newUnitOverview) {
      this.unitOverview = newUnitOverview
      this.unitInfo.title = newUnitOverview.title
    },
    validateAllowGenerate () {
      if (
        this.unitInfo.title && this.unitInfo.title.trim() &&
        this.unitInfo.description && this.unitInfo.description.trim() &&
        this.unitInfo.weekCount &&
        (
          (this.unitInfo.domainIds && this.unitInfo.domainIds.length !== 0 && !this.unitInfo.frameworkName.includes('DRDP')) ||
          this.unitInfo.frameworkName.includes('DRDP') ||
          !this.unitInfo.frameworkName.includes('K12 Standards')
        )
      ) {
        this.isAllowGenerate = true
      } else {
        this.isAllowGenerate = false
      }
    },
    // 反馈点击事件
    clickFeedback (isUP) {
      if (isUP) {
        this.$analytics.sendEvent('web_unit_create1_feedback_p')
      } else {
        this.$analytics.sendEvent('web_unit_create1_feedback_n')
      }
    },

    // 合并用户最新的自定义 Foundation 模板到当前的 customFoundationModule
    newCustomFoundationToCurCustomFoundation () {
      // 点左边的生成按钮时 customFoundationGenerated 为 true
      if (this.userNewFoundationModule && this.userNewFoundationModule.length > 0) {
        this.customFoundationGenerated = true
        if (this.customFoundationModule && this.customFoundationModule.length > 0) {
          // 定义一个 Map 来存储 customFoundationModule 的对象，键为 name
          const customModuleMap = new Map(
            this.customFoundationModule.map(item => [item.name, item])
          )

          // 遍历 userNewFoundationModule，进行合并逻辑
          const mergedModules = this.userNewFoundationModule.map(newItem => {
            // 如果 customModuleMap 中有相同 name，取 customFoundationModule 的对象
            if (customModuleMap.has(newItem.name)) {
              let olDItem = customModuleMap.get(newItem.name)
              // 以模板索引为准做更新
              olDItem.sortIndex = newItem.sortIndex
              // 以模板索引为准更新 key
              olDItem.key = 'customFoundation' + newItem.sortIndex
              return olDItem
            }
            // 否则取 userNewFoundationModule 的对象
            return newItem
          })
          // 更新 customFoundationModule
          this.customFoundationModule = mergedModules
        } else {
          // 如果 customFoundationModule 为空，则直接赋值 userNewFoundationModule
          this.customFoundationModule = this.userNewFoundationModule
        }
      } else {
        // 如果 userNewFoundationModule 为空，则直接赋值 customFoundationModule
        this.customFoundationModule = this.userNewFoundationModule
      }
    },
    // 兼容老数据，生成自定义 Foundation 数据
    generateUnitCustomOverviewValidate (regenerate) {
      // 点击生成按钮时，如果引导存在，则关闭引导
      if (this.showFirstGenerateGuide) {
        this.isCloseGenerateDetailGuide = true
      }
      // 生成按钮点击时，如果 exemplars 提示状态为手动控制，则重置 exemplars 提示状态，恢复为 hover 弹出
      if (this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.showExemplarsTipManual) {
        this.$refs.unitInfoFormRef.showExemplarsTip = false
        this.$refs.unitInfoFormRef.showExemplarsTipManual = false
      }
      // this.setGuide()
      this.$analytics.sendEvent('cg_unit_create_generate')
      // 如果是自定义 Foundation，则需要提示用户是否需要更新自定义 Foundation
      this.newCustomFoundationToCurCustomFoundation()
      // 确保同步最新的 unitInfo 数据
      this.$nextTick(() => {
        // 生成单元课程概览信息
        this.generateUnitOverviewValidate(regenerate)
      })

    },
    // 生成单元课程概览信息前置校验
    generateUnitOverviewValidate (regenerate) {
      if (regenerate) {
        // 点击再次生成按钮埋点
        this.$analytics.sendEvent('web_unit_create1_regenerate')
      }
      // 如果单元描述以及标题为空时，不允许执行生成操作
      // if (!this.isAllowGenerate) {
      //   return
      // }
      // 校验表单信息
      this.$refs.unitInfoFormRef.validate().then(() => {
        // 判断内容是否有变化
        this.createUnit()
        // 点击生成按钮埋点
        this.$analytics.sendEvent('web_unit_create1_generate')
        // 如果是重新生成，则停止生成课程任务
        this.stopGenerateLessonTask()
        this.$emit('promptPanelChange', false)
      }).catch(() => {
        // 未通过校验跳转到第一个错误的表单项
        setTimeout(() => {
          let formItemsWithError = this.$el.querySelectorAll('.is-error')
          // 过滤出可见的 dom
          formItemsWithError = tools.getVisibleDom(formItemsWithError)
          let firstItem = formItemsWithError[0]
          if (firstItem) {
            firstItem.dispatchEvent(new CustomEvent('validate-failed', {}))
            firstItem.scrollIntoView({ block: 'center' })
          }
        }, 0)
      })
    },
    // 停止执行生成课程任务
    stopGenerateLessonTask () {
      // 如果不存在任务列表，则直接返回
      if (!this.batchTasks) {
        return
      }
      // 如果是单个重新生成，则只需要删除当前课程的生成任务即可
      const pendingTasks = this.batchTasks.pendingTasks
      const processingTasks = this.batchTasks.processingTasks
      const failedTasks = this.batchTasks.failedTasks
      // 合并所有任务
      const allTasks = [...pendingTasks, ...processingTasks, ...failedTasks]
      let centerItemIds = []
      let itemIds = []
      this.weeklyPlans.forEach(weeklyPlan => {
        // 如果是 centers 活动，则需要删除 centers 活动的生成任务
        if (weeklyPlan.centerItems) {
          weeklyPlan.centerItems.forEach(centerItem => {
            centerItemIds.push(centerItem.id)
          })
        }
        // 如果是大小组活动，则需要删除大小组活动的生成任务
        if (weeklyPlan.items) {
          weeklyPlan.items.forEach(item => {
            itemIds.push(item.id)
          })
        }
      })
      const allItemIds = centerItemIds.concat(itemIds)
      const centerTasks = allTasks.filter(x => allItemIds.indexOf(x.itemId) !== -1)
      const taskIds = centerTasks.map(x => x.taskId).join(',')
      // 如果存在任务，则停止任务
      if (taskIds) {
        this.clearGenerateTask()
        Lessons2.stopGenerateTasks(taskIds)
      }
    },

    // 验证表单
    validateUnitInfoForm () {
      this.$emit('notifyValidate')
      return new Promise((resolve, reject) => {
        this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validate()
            .then(() => {
              resolve()
            })
            .catch(() => {
              reject()
            })
      })
    },

    // 获取地理位置信息
    getLocationInfo () {
      if (!this.unitInfo) {
        return {
          country: null,
          state: null,
          city: null
        }
      }
      // 国家信息
      let country = this.unitInfo.country
      // 州信息
      let state = this.unitInfo.state
      // 城市
      let city = this.unitInfo.city
      // 是否显示地理位置
      let useLocation = this.unitInfo.useLocation
      if (!useLocation) {
        city = null
      }
      return {
        country: country,
        state: state,
        city: city
      }
    },

    // 创建单元
    createUnit (noGenerate) {
      // 保存用户选择的内容
      let unitPrompt = this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.collectPromptHistoryData()
      if (this.isMC) {
        // 保存用户选择的年级
        localStorage.setItem('UNIT_GRADE' + this.currentUser.user_id, this.unitInfo.grade)
      }
      // 如果单元不是示例单元，则保存用户选择的内容
      if (!this.unitInfo.isExampleUnit) {
        localStorage.setItem('UNIT_INFO' + this.currentUser.user_id, JSON.stringify(unitPrompt))
      }
      if (this.$refs.unitInfoFormRef) {
        if (this.$refs.unitInfoFormRef.showClassroomSetup) {
          localStorage.setItem('UNIT_CLASSROOM_TYPE' + this.currentUser.user_id, this.unitInfo.classroomType || 'IN_PERSON')
        } else {
          this.unitInfo.classroomType = 'IN_PERSON'
        }
      }
      this.generateUnitOverviewLoading = true
      // 如果单元 ID 存在，则直接生成单元课程概览
      if (this.unitId) {
        return this.updateUnit(false, false, true)
      }
      return new Promise((resolve, reject) => {
        let locationInfo = this.getLocationInfo()
        let params = {
          number: 1,
          ...this.unitInfo,
          ...locationInfo
        }
        // 创建单元
        const unit = this.isCurriculumPlugin ? $api.urls().createPluginUnit : $api.urls().createUnit
        this.$axios
            .post(unit, params).then(async (res) => {
          // 单元 ID
          let unitId = this.isCurriculumPlugin ? res.ids[0] : res.id
          // 如果 success 是 false，則跳過
          if (!(!!res.success)) {
            this.generateUnitOverviewLoading = false
            // 弹出达到限制的提示
            this.$message.warning('You\'ve reached your unit creation limit.')
            // 如果是插件平台
            const createdUnits = res.ids || []
            const allowUnits = res.unitUsageModel.unitCount || 3
            if (this.isCurriculumPlugin && allowUnits === 0 && createdUnits.length >= 0) {
              const data = {
                event: 'GO_TO_SHARE_PAGE',
                type: 'RENEWAL',
                shared: false
              }
              this.$bus.$emit('message', data)
              return
            } else if (this.isCurriculumPlugin && allowUnits === 1 && createdUnits.length >= allowUnits) {
              const data = {
                event: 'GO_TO_SHARE_PAGE',
                type: 'INVITE_MORE',
                shared: false
              }
              this.$bus.$emit('message', data)
              this.$analytics.sendEvent('cg_unit_list_newunit_two')
              return
            }
            if (this.isCurriculumPlugin && allowUnits === 2 && createdUnits.length >= allowUnits) {
              const data = {
                event: 'GO_TO_SHARE_PAGE',
                type: 'UNLOCK_UNLIMITED_ACCESS',
                shared: true
              }
              this.$bus.$emit('message', data)
              this.$analytics.sendEvent('cg_unit_list_newunit_three')
              return
            }
            return
          }
          // 更新单元 ID
          this.$store.commit('curriculum/SET_UNIT_ID', unitId)
          let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-overview-cg' : 'unitOverview'
          if (this.unitInfo.adaptedType) {
            routeName = 'unit-overview-cg-adapt'
          }
          // 路由跳转到编辑页面
          this.$router.push({
            name: routeName,
            params: {
              unitId: unitId
            }
          })
          if (!noGenerate) {
            this.unitStep1LoadingOpen = this.isShowLeftPanel
            // 先进行测评点推荐
            await this.unitPlanerStep1RecommendMeasure()
            // 生成单元课程概览
            await this.generateUnitOverviewStream()
          }
          // 创建单元 prompt 历史记录
          this.createPromptHistory(true)
          // 创建单元成功事件
          this.$analytics.sendActivityEvent('UNIT_PLANNER', 'CREATE_UNIT')
          resolve()
        }).catch(error => {
          this.generateUnitOverviewLoading = false
          this.unitStep1LoadingOpen = false
          this.$message.error(error.response.data.error_message)
          reject()
        })
      })
    },

    // 校验单元详情信息
    async validateUnitDetail () {
      try {
        let result = await this.$refs.unitOverviewDetailRef.checkForm()
        return result
      } catch (error) {
        return false
      }
    },

    // 更新单元
    async updateUnit (next, clearWeekPlans, regenerate, showSaveTip) {
      if (!next) {
        // 点击保存按钮埋点
        this.$analytics.sendEvent('web_unit_create1_save')
      }
      // 校验单元基本信息
      try {
        await this.validateUnitInfoForm()
      } catch (error) {
        this.$message.error('Please fill in the required fields.')
        return
      }
      // 点击保存或者下一步时进行校验
      if (next || showSaveTip) {
        // 校验单元详情信息
        if (!await this.validateUnitDetail()) {
          this.$message.error('Please fill in the required fields.')
          return
        }
      }
      if (this.$refs.unitInfoFormRef && !this.$refs.unitInfoFormRef.showClassroomSetup) {
        this.unitInfo.classroomType = 'IN_PERSON'
      }
      // 拼接为 CurriculumUnitCustomFoundation 类的数据结构， 再将数组数据赋值给 unit 的 customFoundations 字段
      const customFoundations = this.customFoundationsData()
      return new Promise((resolve, reject) => {
        this.updateLoading = true
        let locationInfo = this.getLocationInfo()
        let params = {
          unitId: this.unitId,
          ...this.unitOverview,
          ...this.unitInfo,
          ...locationInfo,
          customFoundationAction: true, // 启用自定义 Foundation 行为判断
          customFoundations: customFoundations,
          clearWeekPlans: clearWeekPlans // 是否清空单元下的周计划信息
        }
        // 如果 Unit 的进度不存在，则设置为 INITIALIZED, 如果存在则不设置
        if (!this.curriculumUnit.baseInfo || !this.curriculumUnit.baseInfo.progress) {
          params.progress = 'INITIALIZED'
          this.unitInfo.progress = 20
        } else {
          params.progress = ''
        }
        this.$axios
            .post($api.urls().updateCurriculumUnit, params).then(async (res) => {
          // 如果是用户的操作则保存 prompt 历史记录
          if (next || clearWeekPlans || regenerate || showSaveTip) {
            this.createPromptHistory(true)
          }
          if (next) {
            // 点击 next 时才会推荐测评点，生成完后直接跳转到下一步
            this.unitPlanerStep1RecommendMeasure().then(() => {
              // 需要对 customFoundationInfos 做更新
              this.$store.commit('curriculum/SET_CUSTOM_FOUNDATION_INFO', this.customFoundationsData())
              this.updateLoading = false
              this.goNextPage()
            })
          }
          if (regenerate) {
            this.unitStep1LoadingOpen = this.isShowLeftPanel
            // 先进行测评点推荐
            await this.unitPlanerStep1RecommendMeasure()
            await this.generateUnitOverviewStream(regenerate)
          }
          if (!next) {
            this.updateLoading = false
          }
          if (showSaveTip) {
            this.$message.success(this.$t('loc.unitPlannerStep3SavedSuccessfully'))
          }
          // 更新单元成功事件
          this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
          resolve()
        }).catch(error => {
          this.updateLoading = false
          this.unitStep1LoadingOpen = false
          this.$message.error(error.response.data.error_message)
          reject()
        })
      })
    },

    // 组装 customFoundations
    customFoundationsData () {
      if (!this.isCustomFoundation || !this.customFoundationData || !this.customFoundationModule || this.customFoundationModule.length === 0) {
        return
      }
      // 根据模板基础信息和生成 Foundation 数据，组装实际保存的数据
      return this.customFoundationModule.map(module => ({
        id: module.id,
        sortIndex: module.sortIndex,
        name: module.name,
        key: module.key,
        foundationId: module.foundationId,
        content: this.customFoundationData[module.key] || '' // 根据 key 获取值，没有时设置为空字符串
      }))
    },

    // 同步生成单元概览
    generateUnitOverview () {
      this.generateUnitOverviewLoading = true
      let params = {
        baseInfo: this.unitInfo,
        gptSetting: this.gptSetting
      }
      this.$axios
          .post(aiApiUrl + '?action=generate_unit_overview', params).then((res) => {
        this.unitOverview = res.result
        this.unitOverview.title = this.unitInfo.title
        this.generateUnitOverviewLoading = false
      })
          .catch((error) => {
            let errorMsg = error.response.data.error
            // 重试
            if (errorMsg && (errorMsg.includes('Extra data:') || errorMsg.includes('Expecting ')) && this.retryCount < 1) {
              this.retryCount = this.retryCount + 1
              this.generateUnitOverview()
              return
            }
            this.generateUnitOverviewLoading = false
            this.$message.error(errorMsg)
          })
    },

    // 生成时，自定义模块信息确认
    generateCustomUnitOverviewInfo () {
      // 如果非自定义模块生成，清空自定义模块类型
      if (!this.isCustomFoundation) {
        this.customFoundationGenerateType = null
      }

      // 当生成时 isCustomFoundation=true 且 customFoundationGenerateType 未定义 赋值新模块
      // eslint-disable-next-line no-eq-null
      if (this.isCustomFoundation && this.customFoundationGenerateType == null) {
        this.customFoundationGenerateType = 'LATEST_MODULE'
      }
    },

    // 每次按最新的生成时，查最新的模板
    async generateUnitOverviewStreamNewFoundation () {
      // 如果是自定义 Foundation 且是按最新的模板生成 并且没有输入改编想法，查询最新模板
      if (this.customFoundationGenerateType && this.customFoundationGenerateType === 'LATEST_MODULE' && !(this.redesignIdea && this.redesignIdea.trim())) {
        // 获取用户最新的自定义 Foundation 模板
        this.userNewFoundationModule = await this.getUserNewCustomFoundationModules()
        // 模板合并
        this.newCustomFoundationToCurCustomFoundation()
      }
    },

    // 异步生成单元概览
    async generateUnitOverviewStream (regenerate) {
      // 生成时，自定义模块信息确认
      this.generateCustomUnitOverviewInfo()
      // 生成时，获取最新模板
      await this.generateUnitOverviewStreamNewFoundation()
      this.generateUnitOverviewLoading = true
        // 清理 promptUsageRecordIds
        this.$set(this, 'promptUsageRecordIds', [])
      // 重置数据
      this.overviewData = ''
      let orignalUnitOverview = this.unitOverview && JSON.parse(JSON.stringify(this.unitOverview))
      // 按新模块的话内容全部重新生成，所以不用考虑回滚时模块不一致
      let originalUnitFoundation = this.customFoundationData && JSON.parse(JSON.stringify(this.customFoundationData))
      this.unitOverview = {
        coverMedias: orignalUnitOverview.coverMedias
      }
      // 置空
      this.customFoundationData = {}
      let failedTip = false
      // 生成单元概览参数
      let params = {
        unitId: this.unitId,
        redesignIdea: this.redesignIdea && this.redesignIdea.trim(),
        customFoundationGenerateType: this.customFoundationGenerateType,
        measureIds: this.unitInfo.measureIds
      }
      // generateUnitDescriptionAnalysis 参数
      let generateUnitDescriptionAnalysisParams = {
        unitId: this.unitId
      }
      // 重置改编理由
      this.redesignIdea = ''
      // 生成单元描述分析
      await this.$axios.get(serverlessApiUrl + $api.urls().generateUnitDescriptionAnalysis, { params: generateUnitDescriptionAnalysisParams })
      // 设置单元名称
      this.unitOverview.title = this.unitInfo.title
      // 设置解析的 keys
      let keys = []
      // 根据是否是自定义模块分别进行处理
      if (this.isCustomFoundation) {
        // 到此处为新版自定义 Unit Foundation
        keys = this.customFoundationModule.map((info, index) => {
          return {
            key: info.key,
            name: info.name.replace(/[.*+?^=!:${}()|\[\]\/\\]/g, '\\$&') // 模块名中有特殊字符，转义正则特殊字符
          }
        })
      } else {
        // 固定模块关键字
        keys = [
          { key: 'overview', name: 'Unit Overview' },
          { key: 'trajectory', name: 'Unit Trajectory' },
          { key: 'concepts', name: 'Concepts' },
          { key: 'guidingQuestions', name: 'Guiding Questions' },
          { key: 'note', name: 'Note' }
        ]
      }
      let index = 0
      // 消息回调
      let messageCallback = (message) => {
        this.unitStep1LoadingOpen = false
        if (this.leavedPage) {
          return
        }
        // 更新数据
        this.overviewData += message.data
        this.overviewData = this.overviewData.replaceAll('```', '')
        const replaceMap = [
          { pattern: /\*\*Unit Overview\*\*/g, replacement: 'Unit Overview' },
          { pattern: /\*\*Unit Overview:\*\*/g, replacement: 'Unit Overview:' },
          { pattern: /\*\*Unit Trajectory\*\*/g, replacement: 'Unit Trajectory' },
          { pattern: /\*\*Unit Trajectory:\*\*/g, replacement: 'Unit Trajectory:' },
          { pattern: /\*\*Concepts\*\*/g, replacement: 'Concepts' },
          { pattern: /\*\*Concepts:\*\*/g, replacement: 'Concepts:' },
          { pattern: /\*\*Guiding Questions\*\*/g, replacement: 'Guiding Questions' },
          { pattern: /\*\*Guiding Questions:\*\*/g, replacement: 'Guiding Questions:' },
          { pattern: /\*\*Note\*\*/g, replacement: 'Note' },
          { pattern: /\*\*Note:\*\*/g, replacement: 'Note:' },
          { pattern: /\#\#\# Unit Overview/g, replacement: 'Unit Overview' },
          { pattern: /\#\#\# Unit Trajectory/g, replacement: 'Unit Trajectory' },
          { pattern: /\#\#\# Concepts/g, replacement: 'Concepts' },
          { pattern: /\#\#\# Guiding Questions/g, replacement: 'Guiding Questions' },
          { pattern: /\#\#\# Note/g, replacement: 'Note' }
        ];
        // 标题可能**，需要去除
        this.overviewData = replaceMap.reduce(
            (acc, { pattern, replacement }) => acc.replace(pattern, replacement),
            this.overviewData
          );
        // 如果数据中包含 yes，则说明输入的提示词不合适，提示用户重试
        if (equalsIgnoreCase('yes', this.overviewData.trim()) && !failedTip) {
            failedTip = true
            this.$message.error(this.$t('loc.unableToProcessRequestAdjustDescription2'))
            this.unitOverview = orignalUnitOverview
            this.customFoundationData = originalUnitFoundation
            this.updateLoading = false
            this.generateUnitOverviewLoading = false
            this.unitOverview.generateUnitCoverLoading = false
            return
        }
        // 解析数据
        let parsedData = {}
        // 根据是否是自定义模块分别进行处理
        if (!this.isCustomFoundation) {
          // 固定模块
          parsedData = parseStreamData(this.overviewData, keys, 1000, true)[0]
          if (parsedData.overview) {
            parsedData.overview = removeUnexpectedCharacters(
              parsedData.overview
              .trim()
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 转换粗体为 <strong>
              .replace(/\n/g, '<br>' )
            )
          }
          if (parsedData.trajectory) {
            parsedData.trajectory = removeUnexpectedCharacters(
              parsedData.trajectory
              .trim()
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 转换粗体为 <strong>
              .replace(/\n/g, '<br>')
            )
          }
          if (parsedData.concepts) {
            parsedData.concepts = removeUnexpectedCharacters(
              parsedData.concepts
              .trim()
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 转换粗体为 <strong>
              .replace(/\n/g, '<br>')
            )
          }
          if (parsedData.guidingQuestions) {
            // 由于更换模型，生成的 guidingQuestions 会有多余的内容，需要过滤
            // 以两个 /n/n 为分隔符，获取最后一个分隔符之后的内容
            let guidingQuestionsTemp = parsedData.guidingQuestions.split('\n\n')
            // 若 guidingQuestionsTemp 不为空且长度大于 1
            if (guidingQuestionsTemp && guidingQuestionsTemp.length > 1) {
              // 获取最后一个分隔符之后的内容
              let lastGuidingQuestionsTemp = guidingQuestionsTemp[guidingQuestionsTemp.length - 1]
              // 进行正则判断，判断是否以 - 或者 数字开头
              let regex = /^(-|\d+)\s.*\n/gm
              // 使用正则表达式匹配保留语句
              let matchedStatements = lastGuidingQuestionsTemp.match(regex)
              // 再将匹配到的保留到 guidingQuestionsTemp 中
              if (matchedStatements) {
                // 将匹配后的内容拼接到 guidingQuestionsTemp 中
                guidingQuestionsTemp[guidingQuestionsTemp.length - 1] = matchedStatements.join('\n')
                // 将 guidingQuestionsTemp 拼接成字符串，赋值给 parsedData.guidingQuestions
                parsedData.guidingQuestions = guidingQuestionsTemp.join('\n\n')
              } else {
                // 若没有匹配到，则将 guidingQuestionsTemp 除去最后一个元素，然后赋值给 parsedData.guidingQuestions
                parsedData.guidingQuestions = guidingQuestionsTemp.slice(0, guidingQuestionsTemp.length - 1).join('\n')
              }
            }
            parsedData.guidingQuestions = removeUnexpectedCharacters(
              parsedData.guidingQuestions
              .trim()
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 转换粗体为 <strong>
              .replace(/\n/g, '<br>')
            )
            // 截断 '```' 之后的内容
            parsedData.guidingQuestions = parsedData.guidingQuestions.split('```')[0]
            // 将 --- 替换为空
            parsedData.guidingQuestions = parsedData.guidingQuestions.replace(/---/g, '')
          }
          // 更新数据
          this.unitOverview = {
            ...this.unitOverview,
            ...parsedData
          }
        } else {
          // 自定义模块
          // 解析数据
          parsedData = parseStreamData(this.overviewData, keys)[0]
          // 换行
          keys.map((info, index) => {
            if (parsedData[info.key]) {
              parsedData[info.key] = parsedData[info.key].replace(/\n/g, '<br/>')
            }
          })
          // 只需有自定义 Foundation 数据
          this.customFoundationData = {
            ...parsedData
          }
        }
        // 如果生成成功了，收起左侧 Prompt 面板
        if (Object.keys(parsedData).length > 0 && this.showLeftPanel) {
          if (this.unitInfo.progress < 20) {
            this.unitInfo.progress = 20
          }
          this.showLeftPanel = false
        }
        // 如果已经生成内容但是没解析出数据，说明生成生成失败了，打开左侧 Prompt 面板
        if (this.overviewData.length > 20 && Object.keys(parsedData).length === 0 && !this.showLeftPanel && !this.isAdaptImportUnit) {
          this.showLeftPanel = true
        }
        // 更新数据
        this.unitOverview = {
          generateUnitCoverLoading: true,
          ...this.unitOverview,
          coverKeywords: orignalUnitOverview.coverKeywords
        }
        // 生成单元概览后，清空周计划数据
        this.$store.commit('curriculum/SET_WEEKLY_PLANS', [])
      }
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource($api.urls().generateUnitOverviewStream, null, messageCallback, 'POST', params)
        .then((res) => {
          this.customFoundationGenerateType = null
          if (this.leavedPage || failedTip) {
            return
          }
          // 判断是否生成了单元概览（单元）,如果没有生成则提示用户生成失败，并将 AI 提示信息展示出来
          let isAllValuesEmpty = false
          if (!this.isCustomFoundation) {
            // 如果是旧版数据，仍用下面判断
            if (!this.unitOverview || (!this.unitOverview.overview && !this.unitOverview.trajectory && !this.unitOverview.concepts && !this.unitOverview.guidingQuestions)) {
              isAllValuesEmpty = true
            }
          } else {
            // 自定义 Foundation 时，生成成功判断，使用每个 key 进行判断，逻辑参考上面的判断
            isAllValuesEmpty = this.customFoundationModule.every(
              (info) => {
                const value = this.customFoundationData[info.key]
                return value === null || value === undefined || value.trim() === ''
              }
            )
          }
          // 生成的数据，全部为空时，直接展示 AI 提示返回的数据
          if (isAllValuesEmpty) {
            this.generateUnitOverviewLoading = false
            this.unitOverview.generateUnitCoverLoading = false
            this.aiTips = this.overviewData
            this.unitGenerated = false
          } else {
            this.aiTips = ''
            this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
          }

          // 生成记录 ID
          let promptUsageRecordId = res ? res.promptUsageRecordId : null
          // 保存 promptUsageRecordId
          this.promptUsageRecordIds.push(promptUsageRecordId)
          // 更新单元信息，重新生成后清空周计划数据
          this.updateUnit()
          // 生成评价内容
          // this.evaluateUnitOverview(promptUsageRecordId)
          // 生成结束
          this.generateUnitOverviewLoading = false
          // 生成结束之后拷贝一份单元信息
          this.copyUnitOverview = JSON.parse(JSON.stringify(this.unitOverview))
          // 生成单元封面
          if (!regenerate) {
            // 首次生成 unit 封面埋点
            if (this.showSearchCover) {
              this.$analytics.sendEvent('cg_unit_cover_generated_search')
            } else {
              this.$analytics.sendEvent('cg_unit_cover_generated_ai')
            }
            this.generateUnitCover(this.showSearchCover)
          } else {
            this.unitOverview.generateUnitCoverLoading = false
          }
          resolve()
        })
        .catch(error => {
          // 生成出错
          this.generateUnitOverviewLoading = false
          this.unitStep1LoadingOpen = false
          this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          reject()
        })
      })
    },

    /**
     * 检查测评点推荐信息是否发生变化
     * @returns {boolean} false - 不允许推荐  true - 允许再次推荐
     */
    checkMeasureRecInfoChange () {
      // 没有推荐信息，首次推荐，允许推荐
      if (this.measureRecInfo === null) {
        return true
      }
      // 推荐信息不为空，但是推荐信息和当前信息不一致，允许推荐
      // 框架有变更
      if (this.measureRecInfo.frameworkId !== this.unitInfo.frameworkId) {
        return true
      }
      // 启用领域有变更
      if (this.measureRecInfo.useDomain !== this.unitInfo.useDomain) {
        return true
      }

      // 如果都选用领域推荐，判断领域是否有变更
      if (this.measureRecInfo.useDomain === true) {
        if (this.measureRecInfo.domainIds.length !== this.unitInfo.domainIds.length || this.measureRecInfo.domainIds.some((id, index) => id !== this.unitInfo.domainIds[index])) {
          return true
        }
      }

      // 启用领域和上次一一致，测评点比对
      if (this.measureRecInfo.measureIds.length !== this.unitInfo.measureIds.length || this.measureRecInfo.measureIds.some((id, index) => id !== this.unitInfo.measureIds[index])) {
        return true
      }

      return false
    },

    // unit planner step 1 根据领域推荐测评点
    async unitPlanerStep1RecommendMeasure () {
      // 推荐前进行判断
      if (!this.checkMeasureRecInfoChange()) {
        return Promise.resolve()
      }
      // 如果用户已选择测评点，则不再推荐
      if (this.unitInfo.useDomain !== true) {
        this.measureRecInfo = {
          measureIds: this.unitInfo.measureIds,
          frameworkId: this.unitInfo.frameworkId,
          useDomain: false
        }
        return Promise.resolve()
      }
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().unitPlanerStep1RecommendMeasure, {
          params: {
            unitId: this.unitId,
            // eslint-disable-next-line no-eq-null
            reGenerate: (this.unitInfo.progress == null || this.unitInfo.progress <= 20)
          }
        }).then(res => {
          if (!res || res.length === 0) {
            resolve()
            return
          }
          this.unitInfo.measureIds = res
          // 推荐信息记录
          this.measureRecInfo = {
            measureIds: this.unitInfo.measureIds,
            frameworkId: this.unitInfo.frameworkId,
            domainIds: this.unitInfo.domainIds,
            useDomain: true
          }
          this.$store.commit('curriculum/SET_BAE_INFO', this.unitInfo)
          resolve()
        }).catch(error => {
          resolve()
        })
      })
    },

    // 设置单元封面
    setUnitCoverInfo (cover) {
      let params = {
        keywords: this.unitOverview.coverKeywords,
        unitId: this.unitId,
        source: cover.source,
        url: cover.url
      }
      this.$set(this.unitOverview, 'generateUnitCoverLoading', true)
      this.$axios.post($api.urls().setCoverInfo, params)
      .then(res => {
        let cover = {
          ...res,
          type: 'UPLOAD'
        }
        this.$store.commit('curriculum/SET_UNIT_COVER', [cover])
        this.$refs.unitOverviewDetailRef && this.$refs.unitOverviewDetailRef.updateUnitCover(cover)
      })
      .catch(error => {
      })
      .finally(() => {
        this.$set(this.unitOverview, 'generateUnitCoverLoading', false)
        this.updateUnit(false, false, false, false)
      })
    },

    // 生成单元封面
    generateUnitCover (searchUnitCover = true) {
      this.$analytics.sendEvent('cg_unit_create_one_cover')
      this.$set(this.unitOverview, 'generateUnitCoverLoading', true)
      let generateLessonParam = {
        'unit': { ...this.unitOverview, 'id': this.unitId, 'description': this.unitInfo.description, 'grade': this.unitInfo.grade }
      }
      this.$axios.post(serverlessApiUrl + (searchUnitCover ? $api.urls().extractAndSearchUnitCover : $api.urls().generateUnitCover), generateLessonParam)
      .then(res => {
        let cover = {
          url: res.public_url,
          source: res.source,
          type: 'UPLOAD',
          id: res.id
        }
        // 设置封面关键词
        if (res.keywords) {
          this.$set(this.unitOverview, 'coverKeywords', res.keywords)
        }
        this.$store.commit('curriculum/SET_UNIT_COVER', [cover])
        this.$refs.unitOverviewDetailRef && this.$refs.unitOverviewDetailRef.updateUnitCover(cover)
        this.$set(this.unitOverview, 'generateUnitCoverLoading', false)
        this.updateUnit(false, false, false, false)
      })
      .catch(error => {
        this.$set(this.unitOverview, 'generateUnitCoverLoading', false)
        this.$message.error(this.$t('loc.unitPlannerStep1CoverImageGenerationUnsuccessful'))
      })
    },

    // 生成 Prompt 内容
    async generatePromptContent (callback) {
      try {
        // 验证表单
        try {
          await this.validateUnitInfoForm()
        } catch (error) {
          this.$message.error('Please fill in the required fields.')
          return
        }
        // 创建单元
        if (!this.unitId) {
          await this.createUnit(true)
        }
        // 生成单元概览
        await this.generateUnitOverviewStream()
      } finally {
        // 执行回调
        if (callback) {
          callback()
        }
      }
    },

    // 批量生成结果成功
    batchGenerateResultsSuccess (result) {
      // 更新测试记录 ID
      this.testId = result.testRecordId
      // 获取测试结果
      this.$refs.promptStatsResultRef.getTestResults(true)
      // 切换到测试列表页面
      this.currentView = 'STATS_RESULT'
    },

    // 测试完成
    testCompleted () {
      // 获取测试结果
      this.$refs.promptEditorRef.testCompleted()
    },

    // 测试未完成
    testIncomplete () {
      // 获取测试结果
      this.$refs.promptEditorRef.testIncomplete()
    },

    updatePromptId (promptId) {
      this.promptId = promptId
    },

    evaluateUnitOverview (promptUsageRecordId) {
      this.evaluateUnitOverviewLoading = true
      this.$axios.get($api.urls().evaluateUnitOverview, {
        params: {
          promptUsageRecordId: promptUsageRecordId
        }
      }).then((res) => {
        this.evaluateUnitOverviewResult = res.result
        this.evaluateUnitOverviewLoading = false
      }).catch(error => {
        this.evaluateUnitOverviewLoading = false
        this.$message.error(error.response.data.error_message)
      })
    },

    // 保存单元
    saveUnit () {
      this.$analytics.sendEvent('web_unit_create1_save')
      if (this.lightAdapt) {
        this.$analytics.sendEvent('cg_adapt_foundation_save_clicked')
      }
      this.updateUnit(false, false, false, true)
    },

    // 下一步
    next () {
      this.$analytics.sendEvent('web_unit_create1_next')
      this.$emit('promptPanelChange', false)
      this.updateUnit(true, false, false, false)
    },

    // 下一页
    goNextPage() {
      let routerName = ''
      if (this.curriculumUnit.adaptedType) {
        routerName = 'edit-weekly-plan-overview-cg-adapt'
      } else if (this.$store.state.curriculum.isCG || this.isCurriculumPlugin) {
        routerName = 'edit-weekly-plan-overview-cg'
      } else {
        routerName = 'weeklyPlanOverview'
      }
      this.$router.push({
        name: routerName,
        params: {
          unitId: this.unitId
        }
      })
    },

    // 返回单元历史记录页面
    goUnitList () {
      this.$router.push({
        name: 'unit-list'
      })
    },

    /**
     * 此次 Redesign 是否有模板变更
     * @returns {Promise<boolean>|boolean}
     */
    needTipCustomFoundationRegenerate () {
      // 仅 FOLC 平台才有此逻辑
      if (!this.isCG) {
        return false
      }
      // 原来是自定义模块生成的，现在还原了模块
      if (this.customFoundationModule && this.customFoundationModule.length > 0 && !this.userNewFoundationModule) {
        return true
      }
      // 判断是否有自定义模块数据
      if (!this.userNewFoundationModule || !this.customFoundationModule || this.customFoundationModule.length === 0) {
        return false
      }

      const moduleNames = this.customFoundationModule.map(module => module.name)
      // 如果当前是自定义模块
      if (this.isCustomFoundation) {
        // 数量不一致就未匹配
        if (this.customFoundationModule.length !== this.userNewFoundationModule.length) {
          return true
        }
        const newModuleNames = this.userNewFoundationModule.map(module => module.name)
        // 检查模块名称是否完全匹配
        const allNamesMatch = newModuleNames.every(key => moduleNames.includes(key))
        return !allNamesMatch
      } else {
        // 此时当前模块就是最新模块
        // 检查数量是否为 4
        if (this.customFoundationModule.length !== this.fixOverviewKeys.length) {
          return true
        }

        // 检查模块名称是否完全匹配
        const allNamesMatch = this.fixOverviewKeys.every(key => moduleNames.includes(key))
        return !allNamesMatch
      }
    },

    // 打开重新生成弹窗
    openRedesignUnitDialog () {
      if (this.lightAdapt) {
        this.$analytics.sendEvent('cg_adapt_foundation_enhance_clicked')
      }
      this.$analytics.sendEvent('cg_unit_create_one_update')
      // 获取最新的模板
      this.getUserNewCustomFoundationModules().then((res) => {
        if (res) {
          this.userNewFoundationModule = res
        }
        // 判断是否自定义模板有变更
        if (this.needTipCustomFoundationRegenerate()) {
          // 有模板变更
          this.needTipCustomFoundationVisible = true
        } else {
          // 没有模板变更，直接打开改编弹窗
          this.redesignIdea = ''
          this.redesignUnitVisable = true
          // 发送埋点事件
          this.$analytics.sendEvent('cg_unit_create_one_enhance_pop')
        }
      })
    },

    // 按当前模块生成，打开重新生成弹窗
    closeCurCustomFoundationTipDialog () {
      this.needTipCustomFoundationVisible = false
    },

    // 按新模块生成，打开重新生成弹窗
    regNewCustomFoundationTipDialog () {
      // 按现有模块生成
      if (this.regCustomFoundationTipDialogOp === 1) {
        // 只要选择按当前数据模板生成，不区分是否为  Redesign
        this.customFoundationGenerateType = 'HISTORY_MODULE'
        this.needTipCustomFoundationVisible = false
        this.redesignUnitVisable = true
      } else {
        this.customFoundationGenerated = true
        // 按新模板生成
        this.newCustomFoundationToCurCustomFoundation()
        this.needTipCustomFoundationVisible = false
        // 自定义模块重新生成按新模块生成
        this.customFoundationGenerateType = 'LATEST_MODULE'
        this.generateUnitOverviewValidate(true)
      }
    },

    // 关闭重新生成弹窗
    closeRedesignUnitDialog () {
      this.redesignUnitVisable = false
      this.redesignIdea = ''
    },

    // 重新生成单元概览
    regenerateUnitOverview () {
      this.redesignUnitVisable = false
      this.generateUnitOverviewValidate(true)
      // 发送埋点事件
      this.$analytics.sendEvent('cg_unit_create_one_enhance_pop_regenerate')
    },

    // 创建 prompt 历史
    createPromptHistory (checkChange) {
      // 当前 prompt 数据
      let historyData = this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.collectPromptHistoryData()
      // 原始 prompt 数据
      let originalData = sessionStorage.getItem('UNIT_PROMPT_HISTORY')
      // 如果使用自动分配测评点功能，则不对比测评点信息
      if (historyData.useDomain) {
        // 现有信息中的测评点信息
        delete historyData.measures
        // 确保 originalData 存在且为有效的 JSON 字符串
        try {
          originalData = JSON.parse(originalData)
          // 原始信息中的测评点信息
          delete originalData.measures
          // 转换为字符串
          originalData = JSON.stringify(originalData)
        } catch (e) {
          originalData = null
        }
      }
      // 如果没有变化，则不创建历史
      if (checkChange && historyData && originalData === JSON.stringify(historyData)) {
        return
      }
      // 保存历史记录
      if (historyData) {
        // 更新
        sessionStorage.setItem('UNIT_PROMPT_HISTORY', JSON.stringify(historyData))
        this.$axios.post($api.urls().createUnitPromptHistory, historyData)
      }
    },
    updateSelectedRubrics(selectedPortraits) {
      this.unitInfo.newRubrics = selectedPortraits
      this.$set(this.unitInfo, 'newRubrics', selectedPortraits)
    },
    // 动态计算生成按钮弹框的偏移量
    calculateGenerateButtonPopoverOffset() {
      if (this.$refs.generateButton) {
        const buttonWidth = this.$refs.generateButton.$el.offsetWidth;
        this.generateButtonPopoverOffset = -(buttonWidth * 0.85); // 设置为按钮宽度的 85%
      }
    }
  }
}
</script>

<style lang="less" scoped>
@media screen and (max-width: 768px) {
  .bottom-btn-group {
    .el-button {
      margin: 0 !important;
    }
  }
}

.unit-info-card {
  position: sticky;
  top: 0px;
  margin-bottom: 24px;
  /deep/ .el-card__body {
    padding: 16px;
  }
}

// 添加过渡动画
.el-col {
  transition: all 0.3s ease;
}
.bottom-btn-group {
  position: sticky;
  bottom: 0;
  padding: 10px 0;
  background: var(--color-page-background-white);
}

.sticky-bottom {
  position: sticky;
  bottom: 0px;
  z-index: 999;
  background-color: #fff;
  margin-left: 0px;
  width: 100%;
  border: unset;
  box-shadow: unset;
}

.enhance-btn {
    background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
    border-color: none !important;
    border: 0 !important;
    color: #FFFFFF !important;
    font-size: 14px;
}
.el-button.el-button--primary.is-disabled.is-plain {
  background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
  color: #FFFFFF !important;
  border: none !important;
  cursor: not-allowed;
}
/deep/ .regenerate-unit-dialog {
  z-index: 3000 !important;
  & .el-dialog__body {
    padding-top: 0px;
    padding-bottom: 16px;
  }
  & .el-input__count {
    line-height: 20px !important;
  }

  & .el-dialog__title {
    font-size: 20px !important;
  }
}
/deep/ .el-button--default.is-plain {
  color: #606266;
  font-weight: 600;
}
.custom-foundation /deep/ .el-dialog__wrapper {
  .el-dialog {
    width: 600px;
    margin: 0;
    position: absolute;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;

    .el-dialog__title {
      font-size: 20px !important;
    }

    .el-dialog__body {
      padding: 14px 24px 14px !important;
      font-size: 16px !important;
      font-weight: 400;
      line-height: 24px;
      .el-radio__label {
        font-size: 16px;
      }
    }
  }
}

.feedback-form {
  /* 默认样式 */
  display: flex;
  align-items: center;

  /* 移动端样式 */
  @media screen and (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
<style lang="less" scoped>
.generate-detail-lesson-guide {
  padding: 0px !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  animation: vertical-shake 1.2s cubic-bezier(0.36, 0, 0.64, 1) infinite;
  -webkit-animation: vertical-shake 1.2s cubic-bezier(0.36, 0, 0.64, 1) infinite;
  .guide-content {
    position: relative;
    -webkit-box-shadow: none;
    min-height: 110px;
    .guide-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .close-icon {
    position: absolute;
    top: 10px;
    right: 13px;
    font-size: 16px;
    cursor: pointer;
  }
}

.unit-planner-over-view-dialog {
   /deep/ .el-dialog {
     margin: 0!important;
     position: absolute;
     top: 50%;
     left: 50%;
     transform: translate(-50%, -50%);
     height: 95%;
     display: flex;
     flex-direction: column;
   }
   /deep/ .el-dialog__body {
     overflow-y: auto;
     color: #606266;
     word-break: break-all;
     flex: 1;
     display: flex;
     flex-direction: column;
     padding: 16px 24px 0px 24px;
     font-size: 14px;
   }

   /deep/ .el-dialog__title {
     font-style: normal;
     font-weight: 600;
     font-size: 20px;
     line-height: 26px;
     color: #323338;
   }

   /deep/ .el-dialog__header {
     text-align: left;
     padding: 24px 24px 0;
   }

   /deep/ .el-dialog__footer {
     padding-top: 0px;
   }
 }
</style>
