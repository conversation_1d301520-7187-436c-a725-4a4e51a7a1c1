<template>
  <div>
    <!-- 选择测评点弹窗 -->
    <el-dialog :close-on-click-modal="false"
      custom-class="unit-completed-dialog"
      :visible.sync="visible" @open="openDialog" width="600px"
      @close="handleCloseDialog"
      :show-close="false"
      :append-to-body="true">
      <template slot="title">
        <div class="header">
          <el-image style="width: 24px; height: 24px;" :src="require('@/assets/img/unit/title-gift.png')" />
          <h2 class="title">Your Unit Planner is Complete and Ready to Use!</h2>
          <i class="el-icon-close close-icon" @click="handleClose"></i>
        </div>
      </template>
    <div class="dialog-content">
      <div class="preview-section">
        <div class="preview-image">
          <!-- 这里放预览图片 -->
           <el-image :src="require('@/assets/img/unit/unit-completed-preview.png')" />
        </div>
      </div>

      <div class="description">
        <ul>
          <li><strong>Enhance & Refine:</strong> Adapt lessons like a pro for better alignment, with version history to track changes and revert anytime.</li>
          <li><strong>Download & Export:</strong> Instantly save or send your Unit Planner to Google Drive for seamless access and sharing.</li>
        </ul>
      </div>

      <div class="button-group">
        <UnitDownload class="btn-style btn-hover" style="height: 40px;"
                      ref="unitDownLoad"
                      :showBigButton="true"
                      :buttonColor="'#FFFFFF'"
                      :buttonBorder="'2px solid #DCDFE6'"
                      :buttonTextColor="'#111C1C'"
                      :downloadIconStyle="{fontSize: '22px'}"
                      :arrowIconColorStyle="{fontSize: '24px'}"
                      :getUnit="unit"
                      :viewLesson="true"
                      :isFromUnitDetail="true"
                      :showUnitDownload="true"/>
        <el-button type="primary" class="preview-btn" @click="handlePreview">
          Preview Unit Planner
        </el-button>
      </div>
    </div>
  </el-dialog>
  <!-- 礼花效果组件 -->
  <ConfettiEffect ref="confettiEffect" />
</div>
</template>

<script>
import UnitDownload from '@/views/modules/lesson2/lessonLibrary/components/UnitDownload.vue'
import ConfettiEffect from './ConfettiEffect.vue'

export default {
  name: 'UnitCompletedDialog',
  components: {
    UnitDownload,
    ConfettiEffect
  },
  props: {
    unit: {
      type: Function,
      required: true
    },
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
    }
  },
  methods: {
    openDialog() {
      // 打开弹窗时的处理
    },

    handleClose() {
      this.$emit('update:visible', false)
    },

    // 关闭弹窗
    handleCloseDialog() {
      // 关闭礼花效果
      if (this.$refs && this.$refs.confettiEffect) {
        this.$refs.confettiEffect.clearConfetti()
      }
    },

    handlePreview() {
      // 处理预览
      this.$router.push({
          name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail',
          query: {
              unitId: this.unit().id
          }
      })
    }
  },
  watch: {
    visible: {
      handler (val) {
        if (val) {
          this.$nextTick(() => {
            // 打开礼花效果
            if (this.$refs && this.$refs.confettiEffect) {
              this.$refs.confettiEffect.launchConfetti()
            }
          })
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .unit-completed-dialog {
  :deep(.el-dialog__header) {
    display: none;
  }

  .el-dialog__body {
    padding: 14px 20px 30px 20px;
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;

  .completed-icon {
    width: 40px;
    height: 40px;
  }

  .title {
    flex: 1;
    font-family: Inter;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.3;
    color: #111C1C;
    margin: 0;
  }

  .close-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #111C1C;
    cursor: pointer;
    line-height: 1;
  }
}

.preview-section {
  .preview-image {
    width: 100%;
    height: 180px;
    background: linear-gradient(90deg, #AADBFD 0%, #C6EDFD 50.48%, #A5C2FE 100%);
    border-radius: 8px;
    overflow: hidden;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 8px;

  ul {
    margin: 0;
    padding-left: 20px;
    list-style: disc !important;
    color: #111C1C;
  }

  li {
    margin: 0;
    list-style: disc !important;
    font-family: Inter;
    font-size: 16px;
    line-height: 1.5;
    color: #111C1C;
    padding-left: 8px;

    &::marker {
      color: #111C1C;
    }
  }

  strong {
    font-weight: 600;
  }
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  width: 100%;

  .download-btn {
    height: 40px;
    padding: 8px 12px;
    border-color: #DCDFE6;

    .el-icon-arrow-right {
      margin-left: 4px;
      color: #676879;
    }
  }

  .preview-btn {
    height: 40px;
    padding: 8px 12px;
    background-color: #10B3B7;
    border-color: rgba(16, 179, 183, 0.4);
    font-weight: 600;
  }
}
</style>