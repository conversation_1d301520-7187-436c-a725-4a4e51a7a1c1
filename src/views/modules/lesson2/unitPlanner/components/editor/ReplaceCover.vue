<template>
  <div>
    <el-tooltip :content="showSearchCoverFeature ? 'Replace' : 'Regenerate'" placement="top">
      <el-popover
        placement="right"
        width="250"
        :offset="20"
        :disabled="!showSearchCoverFeature"
        :visible-arrow="false"
        trigger="hover">
        <div class="display-flex flex-direction-col gap-8">
          <div class="title-font-14 lg-pointer" @click.stop="chooseFromSuggestions()">
            🔍 Choose from suggestions
          </div>
          <div class="title-font-14 lg-pointer" @click.stop="generateWithAI()">
            ✨ Generate with AI
          </div>
        </div>
        <el-button slot="reference" size="mini" type='primary' plain class="regenerate-btn" @click="showSearchCoverFeature ? () => {} : generateWithAI()" >
            <img :src="require('@/assets/img/lesson2/plan/regenerate.svg')" alt="">
        </el-button>
      </el-popover>
    </el-tooltip>
    <el-dialog
      v-if="showSearchCoverFeature"
      :visible.sync="suggestionsDialogVisible"
      :append-to-body="true"
      title="Choose from Suggestions"
      width="670px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="title-font-16">Pick a cover image from the suggested options below. </div>
      <div class="lg-margin-top-24">
        <div class="image-container lg-scrollbar-show"  v-loading="loading && images.length == 0" v-infinite-scroll="getSuggestions" :infinite-scroll-disabled="loading || page > 3" :infinite-scroll-distance="10">
          <div class="image-viewer lg-pointer" v-for="(image, index) in images" :key="index" @click.stop="selectImage(image)">
            <div class="image-viewer-container">
              <el-image :src="image.thumbnailUrl || image.url" :style="{height: '100%', width: '100%'}" lazy>
                <div slot="placeholder">
                  <el-skeleton :loading="true" animated>
                    <template slot="template">
                      <el-skeleton-item
                        variant="image"
                        style="width: 190px; height: 110px;"
                        />
                    </template>
                  </el-skeleton>
                </div>
              </el-image>
              <div class="image-source" :title="image.source">Image Source: {{ image.source }}</div>
            </div>
          </div>
          <div style="text-align: center; height: 50px; width: 100%;" v-if="loading && images.length > 0" v-loading="loading"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { equalsIgnoreCase } from '@/utils/common'
import LessonApi from '@/api/lessons2'
import { mapState } from 'vuex'
export default {
  name: 'ReplaceCover',
  props: {
    // 搜索关键词
    searchKeywords: {
      type: String,
      default: ''
    },
    // 单元ID
    type: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      suggestionsDialogVisible: false, // 建议对话框是否可见
      images: [], // 图片列表
      page: 1, // 页码
      pageSize: 10, // 每页大小
      loading: false // 加载状态
    }
  },
  computed: {
    ...mapState({
      showSearchCover: state => state.unit.showSearchCover // 是否显示搜索封面功能
    }),
    // 是否显示搜索封面功能
    showSearchCoverFeature() {
      return this.showSearchCover && !!this.searchKeywords
    }
  },
  methods: {
    // 从建议中选择封面
    chooseFromSuggestions() {
      // 选择搜索封面埋点
      if (this.type === 'unit') {
        this.$analytics.sendEvent('cg_unit_cover_replace_method_search')
      } else {
        this.$analytics.sendEvent('cg_unit_lesson_cover_replace_method_search')
      }
      this.suggestionsDialogVisible = true
      this.getSuggestions()
    },  
    // 使用AI生成封面
    generateWithAI() {
      // 使用AI生成封面埋点
      if (this.type === 'unit') {
        this.$analytics.sendEvent('cg_unit_cover_replace_method_ai')
      } else {
        this.$analytics.sendEvent('cg_unit_lesson_cover_replace_method_ai')
      }
      this.$emit('generateCover')
    },
    // 选择封面
    selectImage(image) {
      // 选择封面埋点
      if (this.type === 'unit') {
        this.$analytics.sendEvent('cg_unit_cover_replace_search_image_click')
      } else {
        this.$analytics.sendEvent('cg_unit_lesson_cover_replace_search_image_click')
      }
      this.$emit('setCoverInfo', image)
      this.suggestionsDialogVisible = false
    },
    // 获取建议图片
    async getSuggestions() {
      // 如果正在加载或图片数量达到30，则返回
      if (this.loading || this.page > 3) return
      try {
        // 设置加载状态
        this.loading = true
        // 搜索图片
        const res = await LessonApi.searchCover({
          keywords: this.searchKeywords,
          source: 'GOOGLE',
          pageNum: this.page,
          pageSize: this.pageSize
        })
        // 更新图片列表
        this.images = [...this.images, ...res.images]
        // 增加页码
        this.page++
      } catch (error) {
        this.$message.error('Failed to get suggestions')
      } finally {
        // 关闭加载状态
        this.loading = false
      }
    }
  }
}
</script>
<style lang="less" scoped>
.regenerate-btn {
  padding: 0 !important;
  width: 32px;
  height: 32px;
}

/deep/ .el-dialog__header {
  padding: 24px 24px 0;
  .el-dialog__title {
    font-size: 20px;
    font-weight: 600;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #111c1c;
  }
}

/deep/ .el-dialog__body {
  padding: 24px;
}

.image-viewer {
  aspect-ratio: 16 / 9;
  position: relative;
  padding-top: 0;
  width: calc((100% - 32px) / 3);
  border: 3px solid transparent;
  border-radius: 2px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.image-viewer:hover {
  border-color: #10b3b7;
}

.image-container {
  overflow: auto;
  height: 400px;  
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
  align-content: flex-start;
}

.image-viewer-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.image-viewer-container:hover .image-source {
  display: block;
}

.image-source {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #3A3F51B2;
  color: #fff;
  padding: 8px 10px;
  line-height: 16px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

@supports not (aspect-ratio: 16 / 9) {
  .image-viewer {
    height: 0;
    padding-top: 56.25%;
  }
  .image-viewer-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

::v-deep .el-image__inner {
  border-radius: 0 !important;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

</style>
