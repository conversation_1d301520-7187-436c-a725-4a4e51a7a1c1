<template>
  <div>
    <!-- 例子 -->
    <div v-if="unitProgress <= 20 && showHeadBtn" id="effortless-unit-setup" class="display-flex gap-10 align-items" style="float: right; margin-bottom: 5px;">
      <el-tooltip effect="dark" :content="$t('loc.unitPlannerExemplarsTip')" placement="top"  v-model="showExemplarsTip" :manual="showExemplarsTipManual">
        <el-button style="padding: 3px 6px!important;" :style="isCG && !isCurriculumPlugin ? 'margin-bottom: 6px' : ''" type="primary"
                   :class="{'width-36-height-36': !isCurriculumPlugin}"
                   size="medium"
                   :disabled="unitBaseInfoLoading || unitProgress > 20"
                   @click="handleExemplarsClick" :plain="!showExemplarsTipManual">
          <div class="btn-center">
            <i class="lg-icon lg-icon-light font-size-20"></i>
            <span v-if="isCurriculumPlugin">Exemplars {{ exampleIndex % examples.length + 1 }}/{{ examples.length }}</span>
          </div>
        </el-button>
      </el-tooltip>
      <!-- prompt 历史 -->
      <UnitPromptHistory v-if="unitProgress <= 20" class="unit-prompt-history" @usePromptHistory="usePromptHistory" :unitProgress="unitProgress" :showPromptPanel="showPromptPanel"/>
    </div>
    <!-- 从示例中选择一条 -->
    <el-form ref="unitInfoFormRef" label-position="top" label-width="100px" :model="unitInfo" :rules="unitInfoRules">
      <!-- 单元名称 -->
      <el-form-item :label="$t('loc.unitPlannerStep1UnitName')" prop="title" class="unit-title">
        <el-input v-model="unitInfo.title" :placeholder="$t('loc.unitPlannerStep1UnitName')" size="medium" />
      </el-form-item>
      <!-- 描述内容 -->
      <el-form-item :label="$t('loc.unitPlannerStep1UnitDescription')" prop="description" id="description-assistant" class="description">
        <el-input ref="unitDescriptionInputRef" class="unit-description-input" v-model="unitInfo.description" type="textarea" maxlength="4000"
                 :rows="4" :placeholder="$t('loc.unitPlannerStep1PleaseDescribeTheUnit')" @focus="unitDescriptionIsFocus = true" @blur="unitDescriptionIsFocus = false" @input="unitDescriptionInput"/>
        <!-- 清空键 -->
        <i class="el-icon-error unit-description-clear-icon" v-if="showClearUnitInfoDescription" @click="unitInfo.description = ''"/>
        <!-- 引导输入框 -->
        <UnitDescriptionAssistant :unitDescriptionIsFocus="unitDescriptionIsFocus" :showDesAndVoiceBtn="showDesAndVoiceBtn" @mergeImportInfo="mergeImportInfo"/>
      </el-form-item>
      <div class="display-flex flex-space-between">
        <!-- 年级 -->
        <el-form-item :label="$t('loc.unitPlannerStep1Grade')" prop="grade" class="m-r-xs flex-1">
          <el-select v-model="unitInfo.grade" filterable :placeholder="$t('loc.pleaseSelect')" size="medium"
            :disabled="unitProgress > 20"
            @change="handleGradeChange"
            ref="grade_select" @visible-change="selectVisible($event, 'grade_select')"
            class="w-full gradeSelectStyle"
            popper-class='unit-planer-unitInfoFormSelect'>
            <el-option v-for="(grade, index) in grades" :key="index" :label="grade.name"
                       :value="grade.value"></el-option>
          </el-select>
        </el-form-item>
        <!-- 周数 -->
        <el-form-item ref="weekCount" :label="$t('loc.unitPlannerStep1Weeks')" prop="weekCount" class="m-r-xs flex-1">
            <el-input v-model="unitInfo.weekCount" :min="unitWeekMin" :max="10" type="number" @change="validateUnitDomainMeasure" size="medium" :disabled="unitProgress > 20 || isMC"/>
        </el-form-item>
        <!-- 语言 -->
        <!-- 现在选项中只有英语，所以隐藏 -->
        <el-form-item  v-if="false" :label="$t('loc.unitPlannerStep1Language')" prop="language" class="flex-1">
          <el-select v-model="unitInfo.language" filterable :placeholder="$t('loc.pleaseSelect')" :disabled="unitProgress > 20" size="medium">
            <el-option v-for="language in languages" :key="language.code" :label="language.name" :value="language.name">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="display-flex flex-direction-col" id="standard-alignment">
        <!--unitProgress > 20 框架-->
        <el-form-item :label="$t('loc.unitPlannerStep1AssessmentFramework')" prop="framework"  class="w-full" v-if="unitProgress > 20 || isMC">
          <el-select v-model="unitInfo.frameworkName" class="w-full"  :disabled="unitProgress > 20 || isMC">
            <el-option :label="unitInfo.frameworkName" :value="unitInfo.frameworkName"></el-option>
          </el-select>
        </el-form-item>
        <!--unitProgress <= 20 框架-->
        <el-form-item :label="$t('loc.unitPlannerStep1AssessmentFramework')" prop="framework"  class="w-full"
                      v-show="!isMC"
                      v-if="unitProgress <= 20">
          <!-- 级联选择器，展示州和框架数据 -->
          <el-cascader
            size="medium"
            ref="stateStandardsCascader"
            v-model="selectedFrameworkData"
            :options="cascaderData"
            :props=cascaderProps
            :placeholder="$t('loc.pleaseSelect')"
            :show-all-levels="false"
            @change="handleCascaderChange"
            class="w-full"
            popper-class="public-lesson-assistantascader-class"
          />
        </el-form-item>
        <!-- 测评点 -->
        <el-form-item :label="$t('loc.unitPlannerStep1Standards2')" prop="domainIds" class="lg-margin-bottom-0 domain-label">
          <!-- 测评点选择方式 -->
          <el-tabs v-model="selectedDomainMode" @tab-click="changeUseDomain" :disabled="unitProgress > 20" class="lg-tabs-small lg-tabs-assistant-domain-tabs add-margin-b-8">
            <!-- 自动分配选项卡 -->
            <el-tab-pane name="auto" :disabled="unitProgress > 20 || shouldDisableAutoMode">
              <span slot="label" class="display-flex justify-content">
                {{ $t('loc.unitPlannerStep1MeasureAutoAdapt') }}
                <el-tooltip class="item" effect="dark" :content="$t('loc.unitPlannerStep1MeasureAutoAdaptTip')"
                  placement="top">
                  <i class="lg-icon lg-icon-question lg-margin-left-4"></i>
                </el-tooltip>
              </span>
            </el-tab-pane>
            <!-- 精准选择选项卡 -->
            <el-tab-pane name="specific" :disabled="unitProgress > 20">
            <span slot="label" class="display-flex justify-content">
              {{ $t('loc.unitPlannerStep1MeasureSpecified') }}
              <el-tooltip class="item" effect="dark" :content="$t('loc.unitPlannerStep1MeasureSpecifiedTip')"
                placement="top">
                <i class="lg-icon lg-icon-question lg-margin-left-4"></i>
              </el-tooltip>
            </span>
            </el-tab-pane>
          </el-tabs>
          <!-- 按领域自动分配 -->
          <el-select
            v-if="selectedDomainMode === 'auto'"
            ref="autoAdaptedSelect"
            v-model="selectedDomainIds"
            multiple
            class="w-full lesson-assistant-domain-select"
            :class="{'has-selected-subjects': selectedDomainIds.length > 0}"
            placeholder=""
            :loading="loadingDomainData"
            :disabled="unitProgress > 20 || shouldDisableAutoMode"
            @remove-tag="deleteDomain"
            popper-class="lesson-assistant-domain-select-dropdown">
              <!-- 当 domainSelected 为空时才显示 prefix -->
              <template slot="prefix" v-if="!selectedDomainIds.length">
                <i v-show="loadingDomainData" class="el-icon-loading font-size-20 lg-color-primary"></i>
                <i v-show="!loadingDomainData" class="lg-icon lg-icon-add1 lg-color-primary font-size-20"></i>
                <span class="font-weight-600">{{ $t('loc.selectSubjects') }}</span>
              </template>
              <template slot="prefix" v-if="selectedDomainIds.length > 0">
                <i class="lg-icon lg-icon-add1 lg-color-primary font-size-20"></i>
              </template>
            <el-option-group>
              <el-checkbox
                v-model="selectAll"
                @change="handleSelectAllChange"
                :indeterminate="selectAllIndeterminate"
                class="domain-select-all w-full">
                <span class="font-weight-600">{{ unitInfo.frameworkName }}</span>
              </el-checkbox>
            </el-option-group>
            <el-option
              v-for="item in unitInfo.allDomains"
              :key="item.value"
              :label="item.label"
              :value="item.value">
                <el-checkbox
                  :value="selectedDomainIds.includes(item.value)"
                  :label="item.label"
                  >
                </el-checkbox>
            </el-option>
          </el-select>
          <!-- 按测评选择 -->
          <subject-selector
            v-else
            ref="subjectSelector"
            :treeData="treeData"
            :loading="loadingDomainData"
            :disabled="unitProgress > 20"
            :showMaxItem="false"
            :maxItems="maxItems"
            :defaultProps="defaultProps"
            :unitProgress="unitProgress"
            @update:nonLeafNodes="handleNonLeafNodes"
            @update:selectedItems="handleUpdateSelectedItems"
            @update:domainNodes="handleDomainNodes">
          </subject-selector>
        </el-form-item>
      </div>
      
      <div :class="{'unit-import-adapt-form': inAdaptSelect}">
        <!-- 校训回显 -->
        <UnitInfoLearnerProfile v-if="!isMC" v-show="!inAdaptSelect" :disabled="unitProgress > 20"
                                :unitInfo="unitInfo"
                                @updateDisplayRubrics="updateDisplayRubrics"
                                @updateRubricsOptions="(val)=>{$emit('updateRubricsOptions', val)}"
                                :unitProgress="unitProgress" @updateRubrics="updateRubrics"
                                ref="UnitInfoLearnerProfile"></UnitInfoLearnerProfile>
        <!-- 单元导入页面下的校训回显 -->
        <div class="unit-import-adapt-form-pog" v-show="inAdaptSelect && isOlderThanK">
          <div class="unit-import-adapt-form-pog-content">
            <span>{{ $t('loc.lessonPortraitTitle') }}</span>
            <el-tooltip effect="dark" placement="top" :open-delay="500">
              <div slot="content" class="max-width-320">
                <div>{{ $t('loc.learnerProfileUseTip') }}</div>
                <!-- 若有选择的校训内容，显示 My settings -->
                <div v-if="hasProfile"
                     class="lg-pointer font-color-primary lg-underline"
                     @click="addAgency()">
                  My settings
                </div>
                <!-- 若当前年龄符合校训年龄且没有选择的校训内容，显示 Setup now -->
                <div v-else-if="!hasProfile"
                     class="lg-pointer font-color-primary lg-underline"
                     @click="addAgency()">
                  {{ $t('loc.learnerProfileSetupNow') }}
                </div>
              </div>
              <i class="lg-icon lg-icon-info"></i>
            </el-tooltip>
            <!-- 有校训的选择按钮 -->
            <div class="unit-import-adapt-form-pog-btn" @click="openSelectRubricsDialog"
                 v-if="hasProfile && displayRubrics.length === 0">
              <i class="el-icon-circle-plus-outline"></i>
              <span>{{ $t('loc.pleaseSelect') }}</span>
            </div>
            <!-- 无校训的设置按钮 -->
            <el-button type="primary" @click="openGenerateRubricsDialog" size="small" v-else-if="!hasProfile" class="add-margin-l-35">{{ $t('loc.learnerProfileSetupNow') }}</el-button>
          </div>
          <div class="unit-import-rubric" v-if="displayRubrics.length > 0"
               @click="openSelectRubricsDialog">
            <div class="unit-import-rubric-result">
              <div v-for="rubric in displayRubrics" class="unit-import-rubric-item" @click.stop>
                <span>{{ rubric.title }}</span>
                <i class="lg-icon lg-icon-close" @click.stop="removeRubrics(rubric.title)"></i>
              </div>
            </div>
            <i class="el-icon-circle-plus-outline"></i>
          </div>
        </div>
        
        <!-- Classroom Setup 部分 -->
        <el-form-item v-show="showClassroomSetup" prop="classroomType" class="classroom-setup-section">
          <div slot="label" class="flex-row-center">
            <span class="title-font-14">
              {{ $t('loc.classroomSetup') }}
            </span>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="max-width: 260px;">
                {{ $t('loc.classroomSetupTip') }}
              </div>
              <i class="lg-icon lg-icon-info lg-margin-left-4 color-icon lg-pointer font-400-important"></i>
            </el-tooltip>
          </div>
          <div class="classroom-setup-content">
            <el-radio-group :disabled="unitProgress > 20" v-model="unitInfo.classroomType" class="teaching-mode-options">
              <el-radio label="IN_PERSON" class="teaching-mode-radio" style="margin-right: 69px;">{{ $t('loc.classroomSetupInPerson') }}</el-radio>
              <el-radio label="VIRTUAL" class="teaching-mode-radio">{{ $t('loc.classroomSetupVirtual') }}</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
      
        <!-- 选择位置 -->
        <div class="location" id="localized-curriculum">
          <div>
            <span class="title-font-14 text-default lg-margin-right-12">
              {{ $t('loc.unitPlannerStep1CreateLocalizedCurriculum') }}
            </span>
          <el-switch
            style="height: 23px"
            v-model="unitInfo.useLocation"
            active-color="#10B3B7"
            inactive-color="#DCDFE6">
          </el-switch>
        </div>
        <el-tooltip :content=locationDescription placement="top" :open-delay="500">
          <button
            style="opacity: 1 !important;font-size: 20px"
            class="lg-icon lg-icon-location location-align"
            @click.prevent="editLocation">
              <span v-if="unitInfo.city && unitInfo.city !== '' " style="margin-left: 5px;font-family: Inter;">
              {{ unitInfo.city }}, {{ unitInfo.state }}, {{ unitInfo.country }}
            </span>
              <span v-else style="margin-left: 5px;font-family: Inter;">
              {{ unitInfo.state }}, {{ unitInfo.country }}
            </span>
              <i class="el-icon-arrow-right"></i>
            </button>
          </el-tooltip>
        </div>
        <!-- Advaced settings -->
        <resource-settings class="add-margin-t-12" :useTextBtn="inAdaptSelect" :useCollapse="true"
                           :setupEventName="'cg_unit_create_setup_resource'" ref="resourceSettings"/>
      </div>
      
      <!-- 选择位置弹框 -->
      <el-dialog title="Change Location" :visible.sync="cityDialogVisible" :append-to-body="true" width="600px" :close-on-click-modal="false"
                 custom-class="location-from-dialog">
        <el-form :model="location" label-position="top" label-width="100px">
          <el-form-item :label="$t('loc.unitPlannerStep1Country')">
            <el-select v-model="location.country" @change="changeCountryHandle">
              <el-option v-for="country in countries" :key="country" :label="country" :value="country">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('loc.unitPlannerStep1State')">
            <el-select
              @change="changeStateHandle"
              v-model="location.state">
              <el-option v-for="state in states" :key="state" :label="state" :value="state">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('loc.unitPlannerStep1City')">
            <el-select
              filterable
              v-model="location.city"
              clearable
              :placeholder="$t('loc.pleaseSelect')">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
              <el-option v-for="(city, index) in cities" :key="index" :label="city" :value="city">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cityDialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="cityDialogConfirm">Confirm</el-button>
        </div>
      </el-dialog>

      <!-- 请求帮助的 Dialog -->
      <el-dialog
        :title="$t('loc.StateStandardsRequest')"
        :visible.sync="helpDialogVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
        :modal-append-to-body="true"
        class="state-standards-help-dialog"
      >
      <el-form-item label="Your Request" required>
        <!-- 原有的详细内容输入框 -->
        <el-input
          type="textarea"
          v-model="helpContent"
          :rows="6"
          maxlength="2000"
          show-word-limit
          :placeholder="$t('loc.StateStandardsRequestDialogTip')"
        ></el-input>
      </el-form-item>
        <!-- 新增 State Standards URL 输入框 -->
        <el-form-item label="State Standards">
          <el-input
            v-model="stateStandards"
            placeholder="Please provide us with the specific URL."
          ></el-input>
        </el-form-item>
        <!-- 新增 Your State 输入框 -->
        <el-form-item label="Your State">
          <el-input
            v-model="state"
            placeholder="Your State"
          ></el-input>
        </el-form-item>
        <div slot="footer" class="dialog-footer">
          <el-button @click="helpDialogVisible = false">{{ $t('loc.cancel') }}</el-button>
          <el-button type="primary" @click="handleHelpSubmit" :loading="isSubmitting">{{ $t('loc.submit') }}</el-button>
        </div>
      </el-dialog>
    </el-form>
  </div>
</template>

<script>
import languages from '@/assets/data/languages.json'
import usStatesAndCities from '@/assets/data/usStatesAndCities.json'
import mexicoStatesAndCities from '@/assets/data/mexicoStatesAndCities.json'
import canadaStatesAndCities from '@/assets/data/canadaStatesAndCities.json'
import tools from '@/utils/tools'
import user from '@/store/modules/user'
import { domainMax3 } from '@/utils/constants'
import SubjectSelector from '@/views/modules/lesson2/lessonLibrary/assistant/SubjectSelector.vue'
import Vue from 'vue'
import { mapState } from 'vuex'
import HelpStandardsButton from '../../HelpStandardsButton.vue'
import UnitInfoLearnerProfile from '../learnerProfile/UnitInfoLearnerProfile.vue'
import UnitDescriptionAssistant from './UnitDescriptionAssistant.vue'
import { MagicAgeGroup } from '@/utils/constants';
import UnitPromptHistory from './UnitPromptHistory.vue'
import { FrameworkDomainsMapping, Grade4FrameworkDomainsMapping } from '@/utils/constants'
import { equalsIgnoreCase } from '@/utils/common'
import frameworkUtils from '@/utils/frameworkUtils'
import ResourceSettings from '@/views/modules/lesson2/lessonLibrary/editor/ResourceSettings.vue'
import _ from 'lodash'; // 确保已安装并导入 lodash

// 创建帮助按钮构造器
const HelpButtonConstructor = Vue.extend(HelpStandardsButton)

export default {
  name: 'UnitInfoForm',
  props: {
    showPromptPanel: {
      type: Boolean,
      default: false
    },
    initHistory: {
      type: Boolean,
      default: true
    },
    // 来自单元导入
    inAdaptSelect: {
      type: Boolean,
      default: false
    },
    // 展示单元描述和学生声音按钮
    showDesAndVoiceBtn: {
      type: Boolean,
      default: false
    },
    // 是否展示头部的例子和 Prompt 历史按钮
    showHeadBtn: {
      type: Boolean,
      default: true
    }
  },
  components: {
    UnitDescriptionAssistant,
    UnitPromptHistory,
    SubjectSelector,
    UnitInfoLearnerProfile,
    HelpStandardsButton,
    ResourceSettings
  },
  data () {
    return {
      examples: [
        {
          title: 'From Farm to Table',
          description: 'In this unit, children will discover the journey of food from farm to table. They will learn about different types of farms, the process of growing fruits and vegetables, and the basics of healthy eating. Interactive activities include planting seeds, understanding the roles of farmers, and exploring the variety of foods that make up our meals.',
          grade: 'Grade 4',
          weeks: 1
        },
        {
          title: 'Winter Holidays Around the World',
          description: 'This unit introduces children to the diverse celebrations of winter holidays across the globe. Through stories, crafts, and music, they will learn about traditions, customs, and foods associated with holidays such as Christmas, Hanukkah, Diwali, and Chinese New Year. This exploration fosters an understanding and appreciation of cultural diversity.',
          grade: 'Grade 4',
          weeks: 1
        },
        {
          title: 'Under the Sea Adventure',
          description: 'Children will embark on an underwater exploration in this unit, learning about the fascinating world of oceans and marine life. They will discover different ocean habitats, learn about a variety of sea creatures, and understand the importance of ocean conservation. Activities may include creating ocean-themed art, simulating ocean ecosystems, and storytelling about sea adventures.',
          grade: 'Grade 4',
          weeks: 1
        }
      ], // 示例单元列表
      selectAll: false,
      exampleIndex: 0, // 示例单元索引
      exemplarsClickCount: 0, // 点击计数
      unitInfoRules: {}, // 单元信息校验规则
      statesAndCities: usStatesAndCities, // 美国州和城市信息
      countries: ['United States', 'Mexico', 'Canada'], // 国家列表
      states: [], // 州列表
      usStates: [], // 美国州列表
      languages: languages, // 语言列表
      frameworks: [], // 框架列表
      frameworkNameData: [],
      frameworkName: null,
      unitDescriptionIsFocus: false, // 单元描述是否获取焦点
      unitInfoInit: false, // baseInfo 是否第一次更新
      frameworkData: [], // 级联数据数组，需填充具体数据
      selectedGrade: this.$store.state.curriculum.isMC ? 'TK (4-5)' : 'Grade 4', // 当前选中的年级
      selectedFrameworkData: [], // 当前选中的级联选项
      measures: [], // 测评点数据
      treeData: [], // 用于存储树形结构的数据
      selectedItems: [], // 选中的测评点
      nonLeafNodes: [],
      defaultProps: {
        children: 'children',
        label: 'abbreviation'
      },
      location: {
        country: 'United States',
        state: '',
        city: ''
      }, // 地区
      usePrompt: false, // 是否使用 prompt
      loadingDomainData: false, // 是否正在加载数据
      cityDialogVisible: false, // 控制选择地理位置的弹框是否弹出
      earlyAgeGroup: ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)'], // 'K (5-6)' 以下年龄范围
      selectedDomainMode: 'auto', // 选中的领域模式
      selectedDomainIds: [], // 选中的领域 ID 列表
      helpDialogVisible: false, // 控制帮助查找州标准的弹框是否弹出
      helpContent: '', // 帮助查找州标准的输入内容
      stateStandards: '', // 新增 State Standards URL 输入框的值
      state: '', // 新增 Your State 输入框的值
      isSubmitting: false, // 是否正在提交
      needSetDomainIds: false, // 标识是否需要设置 domainIds
      unitBaseInfoLoading: true, // 是否正在加载单元
      newRubrics: [],
      showExemplarsTip: false, // 是否显示示例单元提示
      showExemplarsTipManual: false, // 是否手动控制示例单元提示
      subjectsDomainsTabsCache: {}, // 领域选择模式缓存
      displayRubrics:[] // 选择的校训内容
    }
  },
  created () {
    // 初始化 classroomType，如果没有设置则默认为 IN_PERSON
    if (!this.unitInfo.classroomType) {
      this.unitInfo.classroomType = 'IN_PERSON'
    }
    
    if (this.unitInfo.domainIds && this.unitInfo.domainIds.length > 0) {
      this.selectedDomainIds = this.unitInfo.domainIds
    }
    // 获取当前用户默认校训
    if (!this.isMC) {
      // 需要先从 unitInfo 中获取 newRubrics 信息
      if (!(this.unitInfo && this.unitInfo.newRubrics && this.unitInfo.newRubrics.length > 0)) {
        if (this.agencyLearnerProfile && this.unitProgress < 20 && this.initHistory) {
          this.unitInfo.newRubrics = []
          this.$emit('updateRubrics', [])
        }
      } else {
        if (this.agencyLearnerProfile && this.unitProgress <= 20) {
          this.unitInfo.learnerProfileId = this.agencyLearnerProfile.id
          if (this.agencyLearnerProfile.newAgeGroupRubrics) {
            let rubrics = this.agencyLearnerProfile.newAgeGroupRubrics.find(x => x.ageGroup == this.unitInfo.grade)
            // 如果存在 rubrics 和 details
            if (rubrics && rubrics.details) {
              // 获取当前 unitInfo 中的 newRubrics
              const currentRubrics = this.unitInfo.newRubrics || []
              // 过滤出标题匹配的 rubrics
              const matchedRubrics = rubrics.details.filter(detail =>
                currentRubrics.some(current => current.title === detail.title)
              )
              // 更新 unitInfo 中的 newRubrics
              this.unitInfo.newRubrics = matchedRubrics
              // 更新 rubrics
              this.$emit('updateRubrics', matchedRubrics)
              this.newRubrics = matchedRubrics
            }
          }
        }
      }
    }
    // 初始化单元基本信息表单验证规则
    this.initUnitInfoFormRules(false)
    // 已有单元信息，不再初始化
    if (this.unitId && this.unitProgress > 20) {
      this.getAllFrameworkData().then(() => {
        let that = this
        // 通过框架 ID 找到对应的州
        this.frameworkData.forEach(countryData => {
          countryData.stateFrameworkInfo.forEach(stateData => {
            stateData.stateFrameworkInfo.frameworks.forEach(framework => {
              Object.values(framework.gradeFrameworkIdMap).forEach(value => {
                if (value === that.unitInfo.frameworkId) {
                  if (frameworkUtils.isCountryWithState(stateData.country)) {
                    that.selectedFrameworkData = [countryData.country, stateData.state, that.unitInfo.frameworkId]
                  } else {
                    that.selectedFrameworkData = [countryData.country, that.unitInfo.frameworkId]
                  }
                   if (this.initHistory) {
                    // 将初始化完成的 unit prompt 保存到 sessionStorage 中
                    sessionStorage.setItem('UNIT_PROMPT_HISTORY', JSON.stringify(that.collectPromptHistoryData()))
                  }
                }
              })
            })
          })
        })
      })
      // 已有单元信息,州信息也需要初始化
      this.getState()
      this.unitBaseInfoLoading = false
      return
    }
    // 初始化获取州信息
    this.getState().then(() => {
      // 初始化单元基本信息表单数据
      this.initUnitInfoFormData()
      // 初始化获取框架数据
      this.getAllFrameworkData().then(() => {
        if (this.inAdaptSelect) {
          return
        }
        // 如果有缓存数据，直接设置级联选择器的值
        if (!this.isMC && (localStorage.getItem('UNIT_INFO' + this.currentUser.user_id) || this.unitId)) {
          const ageGroup = this.grades.find(item => item.value === this.unitInfo.grade)
          this.selectedGrade = ageGroup.name
          // 设置级联选择器值
          this.setSelectedFrameworkDataByFrameworkIdAndGrade(this.unitInfo.frameworkId, this.unitInfo.grade)
          this.handleCascaderChange(this.selectedFrameworkData, true)
          this.$nextTick(() => {
            if (this.initHistory) {
            // 将初始化完成的 unit prompt 保存到 sessionStorage 中
              sessionStorage.setItem('UNIT_PROMPT_HISTORY', JSON.stringify(this.collectPromptHistoryData()))
            }
            // 初始化完成开始引导
            this.$emit('setGuide')
          })
          this.unitBaseInfoLoading = false
          return
        }
        // 设置默认级联选择器值
        if (this.unitInfo.grade) {
          this.handleGradeChange(this.unitInfo.grade)
          this.$nextTick(() => {
            if (this.initHistory) {
              // 将初始化完成的 unit prompt 保存到 sessionStorage 中
              sessionStorage.setItem('UNIT_PROMPT_HISTORY', JSON.stringify(this.collectPromptHistoryData()))
            }
            // 初始化完成开始引导
            this.$emit('setGuide')
          })
        }
        this.unitBaseInfoLoading = false
      })
    })
  },
  watch: {
    // 监听 treeData 的变化
    treeData: {
      handler: function () {
        this.checkAndExtractMeasures()
        this.$emit('notifySync')
      },
      immediate: true,
      deep: true
    },
    // 'unitInfo.domainIds': {
    //   handler: function () {
    //     this.selectedDomainMode = 'auto'
    //   },
    //   immediate: true,
    //   deep: true
    // },
    // 监听 unitInfo.measureIds 的变化
    'unitInfo.measureIds': {
      handler: function () {
        this.checkAndExtractMeasures()
      },
      immediate: true,
      deep: true
    },
    selectedItems (newVal) {
      if (!newVal || newVal.length === 0) {
        // 清空 domains 信息
        this.$refs.subjectSelector && this.$refs.subjectSelector.clearDomainNodes()
      } else {
        this.$refs.subjectSelector && this.$refs.subjectSelector.setSelectedItems(this.selectedItems)
      }
    },
    selectedGrade (newVal) {
      this.$emit('notifySync')
    },
    selectedFrameworkData (newVal) {
      this.$emit('notifySync')
    },
    'unitInfo.measures': {
      handler: function (newMeasures) {
        // 测评点赋值仅在第一次初始化执行
        if (this.unitInfoInit || (newMeasures && newMeasures.length === 0)) {
          return
        }
        let measures = newMeasures && newMeasures.length > 0 ? newMeasures : []
        this.handleUpdateSelectedItems(measures)
      },
      // 在定义的时候就会立即执行一次
      immediate: true,
      deep: true
    },
    'unitInfo.frameworkId': {
      handler(newVal) {
        if (this.usePrompt) {
          return
        }
        if (newVal) {
          this.subjectsDomainsTabsCache = {}
          // 框架 ID 变化时，清空已选测评点（领域）信息
          this.clearDomainSelected()
        }
      },
    },
    unitInfo (val) {
      if (!this.unitInfoInit) {
        this.copyUnitInfo = JSON.stringify(val)
        this.unitInfoInit = true
      }
    },
    selectedDomainIds (newVal, oldVal) {
      // 同步给 unitInfo.domainIds
      this.unitInfo.domainIds = newVal
      if (newVal.length > domainMax3 && this.isOlderThanK) {
        this.$message.warning(this.$t('loc.unitPlannerDomainMaxThree'))
        this.selectedDomainIds = oldVal
      }
      // 如果是增加 domain, 则需要判断是否可以增加
      if (newVal.length > oldVal.length) {
        this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
      }
      this.selectAll = (this.selectedDomainIds && this.selectedDomainIds.length >= domainMax3 && this.isOlderThanK)
      || (this.selectedDomainIds && this.selectedDomainIds.length >= this.unitInfo.allDomains.length && this.selectedDomainIds.length !== 0)
      this.unitInfo.domainIds = this.selectedDomainIds
      this.$emit('notifySync')
    },
    // 监听 useDomain 的变化
    'unitInfo.useDomain': {
      immediate: true,
      deep: true,
      handler: function (newUseDomain) {
        this.selectedDomainMode = newUseDomain ? 'auto' : 'specific'
      },
    },
    // 监听是否应该禁用自动分配模式
    shouldDisableAutoMode: {
      immediate: true,
      handler: function (shouldDisable) {
        // 如果需要禁用自动分配模式且当前是自动分配模式，则切换到精准选择模式
        if (shouldDisable && this.selectedDomainMode === 'auto') {
          this.selectedDomainMode = 'specific'
          this.unitInfo.useDomain = false
        }
      }
    },
    // 监听周数变化，触发校训校验
    'unitInfo.weekCount': {
      handler: function (newWeekCount, oldWeekCount) {
        // 如果周数发生变化，触发校训校验
        if (newWeekCount !== oldWeekCount && !this.isMC && this.unitProgress <= 20) {
          this.$nextTick(() => {
            this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('profile')
          })
        }
      }
    }
  },
  destroyed () {
    if (this.unitId && this.copyUnitInfo !== JSON.stringify(this.baseInfo)) {
      this.$store.commit('curriculum/SET_UNIT_HAS_UPDATED', true)
    }
    // 移除 Exemplars 按钮滚动监听
    const formCard = document.getElementById('unit-info-form-card')
    if (formCard) {
      formCard.removeEventListener('scroll', this.handleExemplarsScroll)
    }
  },
  computed: {
    ...mapState({
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 MC
      unitUsageModel: (state) => state.magicCurriculum.unitUsageModel, // unit 使用的功能模块
      agencyLearnerProfile: (state) => state.lesson.agencyLearnerProfile // 当前机构校训
    }),
    maxItems () {
      const weeks = this.unitInfo.weekCount
      if (weeks < 0) {
        return 0
      }
      return Math.min(weeks * 10, 30)
    },
    // 级联选择器的 props
    cascaderProps () {
      return {
        value: 'gradeFrameworkId',
        label: 'frameworkName',
        children: 'frameworks',
        disabled: 'isDisabled'
      }
    },
    // 级联选择器的数据
    cascaderData () {
      return tools.getFrameworkData(this.frameworkData, this.selectedGrade)
    },
    // 全局信息
    ...mapState({
      agencyLearnerProfile: (state) => state.lesson.agencyLearnerProfile, // 当前机构校训
      isCG: state => state.curriculum.isCG, // 是否是 CG
      isMC: state => state.curriculum.isMC, // 是否是 MC
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      mcDefaultFrameworkName: state => state.curriculum.mcDefaultFrameworkName, // MC 默认框架名称
      currentUser: state => state.user.currentUser, // 当前用户
      unitId: state => state.curriculum.unit.id, // 单元 ID
      grades: state => state.curriculum.isMC ? state.curriculum.grades.filter(v => MagicAgeGroup.includes(v.value)) : state.curriculum.grades, // 年龄段列表
      analyzeUnitContent: state => state.curriculum.analyzeUnitContent // 单元导入分析内容
    }),
    // 城市信息，根据选择的州更新
    cities () {
      if (this.location.state) {
        const state = this.statesAndCities.find(state => state.name === this.location.state)
        return (state !== null && state !== undefined) ? state.cities.slice().sort() : []
      } else {
        return []
      }
    },
    // 判断输入框是否有内容，从而决定是否显示清空键
    showClearUnitInfoDescription () {
      return this.unitInfo && this.unitInfo.description && this.unitInfo.description.length > 0
    },
    // 是否全选的半选状态
    selectAllIndeterminate () {
      // 只要选项中有一个是选中的状态，则半选状态为 true
      return this.selectedDomainIds && this.selectedDomainIds.length > 0 && !this.selectAll
    },
    // 单元基本信息
    unitInfo: {
      get () {
        return this.$store.state.curriculum.unit.baseInfo
      },
      set (value) {
        this.$store.commit('curriculum/SET_BAE_INFO', value)
        // 更新 store 中的 prompt source list 的信息
        this.updatePromptSourceList()
      }
    },
    unitProgress () {
      return this.unitInfo.progress || 0
    },
    // 地理位置信息
    locationDescription () {
      const { country, state, city } = this.unitInfo
      return `${city && city.trim() ? `${city}, ` : ''}${state}, ${country}`
    },
    // 获取当前选择的年龄是否为 'K (5-6)' 及以上
    isOlderThanK () {
      return !this.earlyAgeGroup.includes(this.unitInfo.grade)
    },
    // 判断是否应该禁用自动分配模式
    shouldDisableAutoMode () {
      // 如果框架名称是 Texas K12 Standards 且年级是 Grade 10、Grade 11、Grade 12，则禁用自动分配
      return this.unitInfo.frameworkName === 'Texas K12 Standards' &&
        ['Grade 10', 'Grade 11', 'Grade 12'].includes(this.unitInfo.grade)
    },
    // 检查是否显示 Classroom Setup 部分
    showClassroomSetup () {
      return this.isOlderThanK && !this.inAdaptSelect
    },
    // 单元最小周数，默认为 1 周。当单元导入改编时最小为导入的单元的周数，只能多不能少
    unitWeekMin() {
      if (!this.analyzeUnitContent) {
        return '1'
      }
      return this.analyzeUnitContent.totalWeek || '1'
    },
    // 是否有校训
    hasProfile() {
      return this.agencyLearnerProfile && this.agencyLearnerProfile.id
    }
  },
  mounted () {
    // // 组件挂载后添加窗口大小变化事件监听器
    // window.addEventListener('resize', this.handleResize)
    // // 初始时执行一次
    // this.handleResize()
    this.$nextTick(() => {
      // 给输入框补充滚动条的样式
      // 选取对应的输入框下的 textarea
      const unitDescriptionInput = this.$el && this.$el.querySelector('.unit-description-input > textarea')
      if (unitDescriptionInput) {
        // 添加滚动条的样式类
        unitDescriptionInput.classList.add('lg-scrollbar-show')
      }

      // 监听级联选择器的下拉面板显示/隐藏状态变化
      if (this.$refs.stateStandardsCascader) {
      this.$refs.stateStandardsCascader.$on('visible-change', (visible) => {
        if (visible) {
          // 使用 setTimeout 确保 DOM 已完全渲染
          setTimeout(() => {
            // 获取级联选择器下拉面板元素
            const panels = document.querySelectorAll('.public-lesson-assistantascader-class')
            for (const panel of panels) {
              if (panel) {
                // 检查并移除已存在的帮助按钮容器
                let existingContainer = panel.querySelector('.help-standards-container')
                if (existingContainer) {
                  existingContainer.remove()
                }

                // 创建新的帮助按钮容器
                const helpContainer = document.createElement('div')

                // 创建帮助按钮实例
                const helpButtonInstance = new HelpButtonConstructor({
                  parent: this,
                  propsData: {
                    onShowDialog: () => {
                      // 显示帮助对话框
                      this.helpDialogVisible = true
                    }
                  }
                })

                // 挂载帮助按钮实例并添加到容器
                helpButtonInstance.$mount()
                helpContainer.appendChild(helpButtonInstance.$el)

                // 将帮助按钮容器添加到面板中
                panel.appendChild(helpContainer)

                // 重新计算级联选择器位置
                this.$nextTick(() => {
                  // 触发级联选择器重新定位
                  this.$refs.stateStandardsCascader.updatePopper()
                })

                // 监听帮助按钮点击事件
                helpButtonInstance.$on('show-dialog', () => {
                  // 显示帮助对话框
                  this.helpDialogVisible = true
                  // 关闭级联选择器下拉面板
                  this.$refs.stateStandardsCascader.toggleDropDownVisible(false)
                })

                // 监听级联选择器关闭事件
                this.$refs.stateStandardsCascader.$once('visible-change', (visible) => {
                  if (!visible) {
                    // 销毁帮助按钮实例并移除容器
                    helpButtonInstance.$destroy()
                    helpContainer.remove()
                  }
                })
              }
            }
          }, 0)
        }
      })
    }
    })
    // 添加 Exemplars 按钮滚动监听
    const formCard = document.getElementById('unit-info-form-card')
    if (formCard) {
      formCard.addEventListener('scroll', this.handleExemplarsScroll)
    }
  },
  methods: {
    // 更新选择的校训数据
    updateDisplayRubrics(val){
      this.$set(this, 'displayRubrics', val)
    },
    syncUnitInfoForm(sourceUnitInfoForm) {
      // --- 同步原始类型数据 ---
      if (this.selectAll !== sourceUnitInfoForm.selectAll) {
        this.selectAll = sourceUnitInfoForm.selectAll;
      }
      if (this.exampleIndex !== sourceUnitInfoForm.exampleIndex) {
        this.exampleIndex = sourceUnitInfoForm.exampleIndex;
      }
      if (this.exemplarsClickCount !== sourceUnitInfoForm.exemplarsClickCount) {
        this.exemplarsClickCount = sourceUnitInfoForm.exemplarsClickCount;
      }
      if (this.selectedGrade !== sourceUnitInfoForm.selectedGrade) {
        this.selectedGrade = sourceUnitInfoForm.selectedGrade; // 这个有 watcher
      }
      if (this.frameworkName !== sourceUnitInfoForm.frameworkName) {
        this.frameworkName = sourceUnitInfoForm.frameworkName;
      }
      if (this.unitDescriptionIsFocus !== sourceUnitInfoForm.unitDescriptionIsFocus) {
        this.unitDescriptionIsFocus = sourceUnitInfoForm.unitDescriptionIsFocus;
      }
      // unitInfoInit 通常是内部状态，可能不需要从外部同步
      if (this.unitInfoInit !== sourceUnitInfoForm.unitInfoInit) {
        this.unitInfoInit = sourceUnitInfoForm.unitInfoInit;
      }
      if (this.usePrompt !== sourceUnitInfoForm.usePrompt) {
        this.usePrompt = sourceUnitInfoForm.usePrompt;
      }
      if (this.selectedDomainMode !== sourceUnitInfoForm.selectedDomainMode) {
        // 考虑是否直接赋值足够，或者需要调用 changeUseDomain
        this.selectedDomainMode = sourceUnitInfoForm.selectedDomainMode;
      }
      if (this.helpContent !== sourceUnitInfoForm.helpContent) {
        this.helpContent = sourceUnitInfoForm.helpContent;
      }
      if (this.stateStandards !== sourceUnitInfoForm.stateStandards) {
        this.stateStandards = sourceUnitInfoForm.stateStandards;
      }
      if (this.state !== sourceUnitInfoForm.state) {
        this.state = sourceUnitInfoForm.state;
      }
      if (this.needSetDomainIds !== sourceUnitInfoForm.needSetDomainIds) {
        this.needSetDomainIds = sourceUnitInfoForm.needSetDomainIds;
      }

      // --- 同步对象和数组（使用深比较） ---

      //      // --- 同步 Location 对象（逐个属性比较） ---
      // if (this.location.state !== sourceUnitInfoForm.location.state) {
      //   this.location.state = sourceUnitInfoForm.location.state;
      // }
      // if (this.location.city !== sourceUnitInfoForm.location.city) {
      //   this.location.city = sourceUnitInfoForm.location.city;
      // }
      // if (this.location.country !== sourceUnitInfoForm.location.country) {
      //   this.location.country = sourceUnitInfoForm.location.country;
      // }

      // 其他数组和复杂对象
      if (!_.isEqual(this.states, sourceUnitInfoForm.states)) {
        // 只有在内容不同时才创建新引用并赋值
        this.states = JSON.parse(JSON.stringify(sourceUnitInfoForm.states));
      }
      if (!_.isEqual(this.frameworks, sourceUnitInfoForm.frameworks)) {
        this.frameworks = JSON.parse(JSON.stringify(sourceUnitInfoForm.frameworks));
      }
      if (!_.isEqual(this.frameworkNameData, sourceUnitInfoForm.frameworkNameData)) {
        this.frameworkNameData = JSON.parse(JSON.stringify(sourceUnitInfoForm.frameworkNameData));
      }
      if (!_.isEqual(this.frameworkData, sourceUnitInfoForm.frameworkData)) {
        this.frameworkData = JSON.parse(JSON.stringify(sourceUnitInfoForm.frameworkData));
      }
      // selectedFrameworkData (有 watcher -> notifySync)
      if (!_.isEqual(this.selectedFrameworkData, sourceUnitInfoForm.selectedFrameworkData)) {
        this.selectedFrameworkData = JSON.parse(JSON.stringify(sourceUnitInfoForm.selectedFrameworkData));
      }
      if (!_.isEqual(this.measures, sourceUnitInfoForm.measures)) {
        this.measures = JSON.parse(JSON.stringify(sourceUnitInfoForm.measures));
      }
      // treeData (有 watcher -> notifySync)
      if (!_.isEqual(this.treeData, sourceUnitInfoForm.treeData)) {
        this.treeData = JSON.parse(JSON.stringify(sourceUnitInfoForm.treeData));
      }
      if (!_.isEqual(this.selectedItems, sourceUnitInfoForm.selectedItems)) {
        this.selectedItems = JSON.parse(JSON.stringify(sourceUnitInfoForm.selectedItems));
      }
      if (!_.isEqual(this.nonLeafNodes, sourceUnitInfoForm.nonLeafNodes)) {
        this.nonLeafNodes = JSON.parse(JSON.stringify(sourceUnitInfoForm.nonLeafNodes));
      }
      // selectedDomainIds (有 watcher -> notifySync)
      if (!_.isEqual(this.selectedDomainIds, sourceUnitInfoForm.selectedDomainIds)) {
        this.selectedDomainIds = JSON.parse(JSON.stringify(sourceUnitInfoForm.selectedDomainIds));
      }
      if (!_.isEqual(this.newRubrics, sourceUnitInfoForm.newRubrics)) {
        this.newRubrics = JSON.parse(JSON.stringify(sourceUnitInfoForm.newRubrics));
      }
    },
    equalsIgnoreCase,
    // 更新创建数据
    updateRubrics(value) {
      if (value && value !== -1 && value !== -2 && value !== -3) {
        this.unitInfo.learnerProfileId = this.agencyLearnerProfile.id
        this.$set(this.unitInfo, 'newRubrics', value.ageGroupRubrics)
        this.$emit('updateRubrics', this.unitInfo.newRubrics)
      } else {
        this.unitInfo.learnerProfileId = null
        this.unitInfo.newRubrics = null
      }
      // 触发校训校验
      this.$nextTick(() => {
        this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('profile')
      })
    },
    // 位置选择弹窗确认回调函数
    cityDialogConfirm () {
      this.unitInfo.country = this.location.country
      this.unitInfo.state = this.location.state
      this.unitInfo.city = this.location.city
      this.cityDialogVisible = false
    },
    // 将默认位置信息赋给当前组件内的位置变量
    baseInfoLocationToLocation () {
      this.location.country = this.unitInfo.country
      this.location.state = this.unitInfo.state
      this.location.city = this.unitInfo.city
      if (equalsIgnoreCase(this.location.country, 'United States')) {
        this.statesAndCities = usStatesAndCities
        this.states = this.usStates
      } else if (equalsIgnoreCase(this.location.country, 'Canada')) {
        this.statesAndCities = canadaStatesAndCities
        this.states = this.statesAndCities.map(state => state.name).sort()
      } else {
        this.statesAndCities = mexicoStatesAndCities
        this.states = this.statesAndCities.map(state => state.name).sort()
      }
    },
    // 打开本地化选择地理位置弹框
    editLocation () {
      this.baseInfoLocationToLocation()
      this.cityDialogVisible = true
    },
    // 切换国家
    changeCountryHandle (value) {
      // 如果选择的国家是美国，则更新州和城市信息
      if (equalsIgnoreCase(value, 'United States')) {
        this.statesAndCities = usStatesAndCities
        this.states = this.usStates
      } else if (equalsIgnoreCase(value, 'Canada')) {
        this.statesAndCities = canadaStatesAndCities
        this.states = this.statesAndCities.map(state => state.name).sort()
      } else {
        this.statesAndCities = mexicoStatesAndCities
        this.states = this.statesAndCities.map(state => state.name).sort()
      }
      this.location.state = this.states[0]
      this.changeStateHandle(this.states[0])
    },
    // 切换州信息的回调
    changeStateHandle (value) {
      const foundState = this.statesAndCities.find(state => state.name === value)
      let cities = foundState ? foundState.cities.sort() : []
      let userCity = this.currentUser.city
      if (userCity && cities.includes(userCity)) {
        this.location.city = userCity
      } else {
        this.location.city = ''
      }
    },
    // 更新 prompt source list
    updatePromptSourceList () {
      // 定义选中的 subjectsDomains
      let subjectsDomains
      if (this.unitInfo.domainIds && this.unitInfo.domainIds.length > 0) {
        subjectsDomains = this.unitInfo.domainIds.map(id => this.getLabel(id)).join(', ')
      }
      // 定义一个资源数组
      const sources = [
        { sourceStep: 1, sourceName: 'unit name', sourceValue: this.unitInfo.title },
        { sourceStep: 1, sourceName: 'unit description', sourceValue: this.unitInfo.description },
        { sourceStep: 1, sourceName: 'overall learning standards (with detailed info)', sourceValue: '-' },
        { sourceStep: 1, sourceName: 'grade', sourceValue: this.unitInfo.grade },
        { sourceStep: 1, sourceName: 'total number of week', sourceValue: this.unitInfo.weekCount },
        { sourceStep: 1, sourceName: 'learning standards', sourceValue: this.unitInfo.frameworkName },
        { sourceStep: 1, sourceName: 'subjects/domains', sourceValue: subjectsDomains },
        { sourceStep: 1, sourceName: 'country', sourceValue: this.unitInfo.country },
        { sourceStep: 1, sourceName: 'state', sourceValue: this.unitInfo.state },
        { sourceStep: 1, sourceName: 'city', sourceValue: this.unitInfo.city },
      ]
      // 更新数据
      this.$store.dispatch('curriculum/updatePromptSourcesList', {
        step: 1,
        sources: sources
      })
    },
    /**
     * 重置所有测评点和领域的选择状态
     */
     clearMeasureAndDomainSelections() {
      this.selectAll = false
      this.unitInfo.domainIds = []
      this.selectedDomainIds = []
      this.unitInfo.measureIds = []
      this.unitInfo.measures = []
      this.selectedItems = []
      this.$nextTick(() => {
        this.$refs.subjectSelector && this.$refs.subjectSelector.setSelectedItems(this.selectedItems)
      })
    },
    updateSubjectsDomainsTabsCache(tabName) {
      if (tabName === 'auto') {
        this.subjectsDomainsTabsCache['auto'] = {
          domainIds: this.unitInfo.domainIds, // 已选领域ID数组
        }
      } else {
        this.subjectsDomainsTabsCache['specific'] = {
          domainIds: this.unitInfo.domainIds, // 已选领域ID数组
          measureIds: this.unitInfo.measureIds, // 已选测评点ID数组
          measures: this.unitInfo.measures // 已选测评点
        }
      }
    },
    // 更换按照领域选测评点
    changeUseDomain (tab) {
      if (!equalsIgnoreCase(tab.name, 'auto') === this.unitInfo.useDomain) {
        this.unitInfo.useDomain = equalsIgnoreCase(tab.name, 'auto')
        // 在切换模式前,先将当前模式下的选择状态保存到缓存
        if (this.unitInfo.useDomain) {
          this.updateSubjectsDomainsTabsCache('specific')
        } else {
          this.updateSubjectsDomainsTabsCache('auto')
        }

        // 确保缓存对象存在
        if (!this.subjectsDomainsTabsCache['specific']) {
          this.subjectsDomainsTabsCache['specific'] = {}
        }
        if (!this.subjectsDomainsTabsCache['auto']) {
          this.subjectsDomainsTabsCache['auto'] = {}
        }
        this.clearMeasureAndDomainSelections()

        // 根据切换后的模式执行相应的处理逻辑
        if (this.unitInfo.useDomain) {
          // 自动模式处理逻辑
          const cachedDomains = this.subjectsDomainsTabsCache['auto'].domainIds
          if (cachedDomains && cachedDomains.length > 0) {
            this.unitInfo.domainIds = cachedDomains
            this.selectedDomainIds = cachedDomains
          }
        } else {
          // 手动模式处理逻辑
          const cache = this.subjectsDomainsTabsCache['specific']
          const domainIds = cache.domainIds
          const measureIds = cache.measureIds
          const measures = cache.measures

          if (domainIds && domainIds.length > 0 && measureIds && measureIds.length > 0) {
            this.unitInfo.domainIds = domainIds
            this.selectedDomainIds = domainIds
            this.unitInfo.measureIds = measureIds
            this.unitInfo.measures = measures
            this.selectedItems = measures
            this.$nextTick(() => {
              this.$refs.subjectSelector && this.$refs.subjectSelector.setSelectedItems(this.selectedItems)
            })
          }
        }
        this.selectAll = (this.selectedDomainIds && this.selectedDomainIds.length >= domainMax3 && this.isOlderThanK)
        // 清除表单中领域、测评点字段的校验状态
        this.$refs.fromRef && this.$refs.fromRef.clearValidate('domains');
      }
      // 如果没有选择测评点,则打开选择器弹窗
      this.showSelector(tab.name)
    },
    showSelector(tabName) {
      if (this.unitProgress > 20) {
        return
      }
      this.$nextTick(() => {
        if (equalsIgnoreCase(tabName, 'auto')) {
          // 自动展开 select
          if (this.$refs.autoAdaptedSelect && !this.shouldDisableAutoMode) {
            // 检查下拉菜单是否已经打开，如果没有打开才调用 toggleMenu
            if (!this.$refs.autoAdaptedSelect.visible) {
              this.$refs.autoAdaptedSelect.toggleMenu()
            }
          }
        } else {
          // 自动打开弹框
          if (this.$refs.subjectSelector) {
            this.$refs.subjectSelector.openShowModal()
          }
        }
      })
    },
      // 全选/取消全选
    handleSelectAllChange(checked) {
      // 如果当前已经达到限制，点击全选按钮应取消所有选择。否则按照顺序选择 3 个
      if (!checked) {
        this.selectedDomainIds = []
        this.unitInfo.domainIds = []
      } else {
        // 距离达到限制缺少的个数
        let lackCount = domainMax3 - this.selectedDomainIds.length
        // 按照顺序选择缺少的 domain
        for (let i = 0; i < this.treeData.length; i++) {
          // 如果当前学科未选择
          if (!this.selectedDomainIds.includes(this.unitInfo.allDomains[i].value)) {
            // 未选择则选择
            this.selectedDomainIds.push(this.unitInfo.allDomains[i].value)
            // 如果不存在缺少的则退出
            if (this.isOlderThanK) {
              if (--lackCount <= 0) {
                break
              }
            }
          }
        }
        this.unitInfo.domainIds = this.selectedDomainIds
      }
      this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
    },
    // 检查是否全选
    checkSelectAll () {
      // 如果所有选项都被选中，则全选按钮也被选中，否则不选中
      if (this.isOlderThanK) {
        this.selectAll = this.unitInfo.domainIds && this.unitInfo.domainIds.length >= domainMax3
      } else {
        this.selectAll = this.unitInfo.allDomains.length === this.unitInfo.domainIds.length
      }
    },
    // 选择领域
    selectedItem (value, item) {
      // 添加 domainIds 的时候，如果 domainIds 的长度大于等于 3 且年龄为 'K (5-6)' 及以上，则不能再添加
      if (this.unitInfo.domainIds && this.unitInfo.domainIds.length >= domainMax3 && value && this.isOlderThanK) {
        this.$message.warning(this.$t('loc.unitPlannerDomainMaxThree'))
        return
      }
      // 从 unitInfo 的 allDomains 中获取得到所有选中的元素
      this.unitInfo.domainIds = this.unitInfo.allDomains.filter(option => option.selected)
        .map(option => option.value)
      this.selectedDomainIds = this.unitInfo.domainIds
      this.unitInfo.measureIds = []
      this.unitInfo.measures = []
      // 检查是否全选
      this.checkSelectAll()

      this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
    },
    // 获取领域下所有测评点
    getAllDomainMeasures (domainId) {
      let measures = []
      function traverse (nodes) {
        nodes.forEach(node => {
          // 递归遍历子节点
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          } else {
            measures.push({
              id: node.id,
              abbreviation: node.abbreviation,
              name: node.name
            })
          }
        })
      }
      this.treeData.forEach(domain => {
        if (domain.id === domainId) {
          traverse(domain.children)
        }
      })
      return measures
    },
    // 切换框架信息
    changeDomainInfo () {
      // 如果还未生成内容，每次切换框架都要清空已选的领域
      // if (this.unitProgress < 20) {
        // this.clearDomainSelected()
      // }
      return this.getDomainInfo()
    },
    // 获取领域信息
    getDomainInfo (callback, params) {
      // 发送请求
      const selectedFrameworkId = this.unitInfo.frameworkId
      return this.$axios.get($api.urls().getDomainInfosByFrameworkId, {
        params: {
          frameworkId: selectedFrameworkId
        }
      })
        .then(response => {
          // 请求成功处理
          this.unitInfo.allDomains = response
            .filter(v => (v.abbreviation && v.abbreviation.trim() !== '') || (v.name && v.name.trim() !== '')) // 过滤出 abbreviation 或 name 不为空的项
            .map(item => ({
              value: item.id,
              label: item.name.trim() !== '' ? item.name.trim() : item.abbreviation.trim(), // 如果 name 为空，则使用 abbreviation；否则使用 name
              selected: false
            }))
          let deleteAll = false
          if (this.unitProgress > 0 || !this.initHistory) {
            deleteAll = true
          }
          this.selectMeasureOption()
          this.clearDomainSelected(deleteAll)
        })
        .catch(error => {
          this.unitInfo.allDomains = []
          // 请求失败处理
          console.error(error)
        }).finally(() => {
          if (callback) {
            callback(params)
          }
        })
    },
    // 设置 domain 已选项
    selectMeasureOption () {
      this.unitInfo.allDomains.forEach(option => {
        if (this.unitInfo.domainIds && this.unitInfo.domainIds.length > 0) {
          this.unitInfo.domainIds.find(domainId => domainId === option.value) ? this.$set(option, 'selected', true) : this.$set(option, 'selected', false)
        } else {
          this.$set(option, 'selected', false)
        }
      })
    },
    // 清空已选领域
    clearDomainSelected (deleteAll = false) {
      // 重置选择的领域
      if (!deleteAll) {
        this.selectAll = false // 全选按钮取消选中
        this.unitInfo.domainIds = [] // 先将数组清空
        this.selectedDomainIds = []
        this.unitInfo.domainIds.forEach((item, index) => {
          this.$set(this.unitInfo.domainIds, index, null) // 使用 $set 设置数组元素为 null
        })
        this.unitInfo.measureIds = []
        this.selectedItems = []
        this.$nextTick(() => {
          this.$refs.subjectSelector && this.$refs.subjectSelector.setSelectedItems(this.selectedItems)
        })
      }
    },
    // 验证领域和测评点
    validateUnitDomainMeasure () {
      // 如果没有选中的测评点，则不再更新
      if (this.selectedItems && this.selectedItems.length === 0) {
        return
      }
      this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
    },
    // 检查提取测评点
    checkAndExtractMeasures () {
      if (this.unitInfo.useDomain) {
        return
      }
      // 确保 treeData 和 unitInfo.measureIds 都有值
      if (this.treeData.length > 0 && this.unitInfo.measureIds.length > 0) {
        // 提取测评点
        this.extractedMeasures = this.extractMeasures(this.treeData, this.unitInfo.measureIds)
        this.selectedItems = this.extractedMeasures
        this.unitInfo.measures = this.selectedItems
        this.validateUnitDomainMeasure()
      }
    },
    // 提取测评点
    extractMeasures (treeData, measureIds) {
      const result = []
      // 递归遍历树形结构
      function traverse (nodes) {
        nodes.forEach(node => {
          if (measureIds.includes(node.id)) {
            result.push({
              id: node.id,
              abbreviation: node.abbreviation,
              name: node.name
            })
          }
          // 递归遍历子节点
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }
      traverse(treeData)
      return result
    },
    // 更新选中的 domains
    handleDomainNodes (domainNodes) { // 新增
      this.domainNodes = domainNodes
      // 将选中的 domains 赋值给 unitInfo.domainIds
      this.unitInfo.domainIds = domainNodes.map(item => item.id)
      this.selectedDomainIds = this.unitInfo.domainIds
    },
    // 更新选中的测评点
    handleUpdateSelectedItems (newSelectedItems, validate) {
      this.selectedItems = newSelectedItems
      // 将选中的测评点赋值给 unitInfo.measureIds
      this.unitInfo.measureIds = this.selectedItems.map(item => item.id)
      this.unitInfo.measures = this.selectedItems
      this.unitInfo.haveSelectMeasureId = true
      validate && this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
      this.$emit('validateAllowGenerate')
    },
    // 更新所有的测评点选项
    handleNonLeafNodes (nonLeafNodes) {
      this.nonLeafNodes = nonLeafNodes
      this.unitInfo.allDomains = this.nonLeafNodes
      // 验证领域和测评点
      this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
    },
    // 获取测评点的标签
    getLabel (value) {
      return value.abbreviation
    },
    // 获取领域的标签
    getDomainLabel (value) {
      if (!value) {
        return ''
      }
      let domain = this.unitInfo.allDomains.find(option => option.value === value)
      return domain && domain.label
    },
    // 删除学科
    deleteDomain (domainId) {
      if (this.unitProgress > 20) {
        return
      }
      // 更新 domainSelected 数组
      this.unitInfo.domainIds = this.unitInfo.domainIds.filter(item => item !== domainId)
      // 检查是否全选
      this.checkSelectAll()
      this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
    },
    // 删除选中的测评点
    deleteMeasure (value) {
      // unitProgress > 20 时不可删除
      if (this.unitProgress > 20) {
        return
      }
      // 在父组件中调用子组件的 removeSelectedItem 方法
      this.$refs.subjectSelector.removeSelectedItem(value)
      // 从 selectedItems 中删除选中的测评点
      this.selectedItems = this.selectedItems.filter(item => item.id !== value.id)

      // 将选中的测评点赋值给 unitInfo.measureIds
      this.unitInfo.measureIds = this.selectedItems.map(item => item.id)
      this.unitInfo.measures = this.selectedItems
      if (this.selectedItems.length === 0) {
        this.unitInfo.haveSelectMeasureId = false
      }
      // 验证领域和测评点

      this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.validateField('domainIds')
    },
    // 从后端获取测评点数据
    fetchMeasuresFromBackend (frameworkId) {
      this.loadingDomainData = true
      // 清空测评点信息
      this.treeData = []
      // 清空选中的项目
      this.selectedItems = []
      this.$nextTick(() => {
        this.$refs.subjectSelector && this.$refs.subjectSelector.setSelectedItems(this.selectedItems)
      })
      
      // 使用 Vuex 中的 getFrameworkDomains 方法获取测评点数据
      this.$store.dispatch('curriculum/getFrameworkDomains', {
        frameworkId: frameworkId,
        compress: false
      })
        .then(measures => {
          // 处理响应，将测评点数据存储在合适的属性中
          this.treeData = measures
          // 处理领域 ID
          this.processDomainIds()
        })
        .catch(error => {
          console.error('Error fetching measures:', error)
        })
        .finally(() => {
          this.loadingDomainData = false
        })
    },

    // 处理领域 ID
    processDomainIds () {
      // 如果是示例单元且有预设的 domainIds
      if (this.unitInfo.isExampleUnit && this.unitInfo.frameworkId && this.selectedDomainIds.length === 0) {
        const domainIds = Grade4FrameworkDomainsMapping[this.unitInfo.frameworkId.toUpperCase()]
        if (domainIds && domainIds.length > 0) {
          // 等待 allDomains 数据加载完成
          this.$nextTick(() => {
            // 找到对应的领域
            const domains = this.unitInfo.allDomains.filter(domain => domainIds.includes(domain.value))
            // 设置选中的领域
            if (domains && domains.length > 0) {
              this.selectedDomainIds = domains.map(domain => domain.value)
              this.unitInfo.domainIds = this.selectedDomainIds
            }
          })
        }
      } else if (this.unitInfo.domainIds && this.unitInfo.domainIds.length > 0) {
        // 如果已经有选中的领域，恢复选中状态
        this.$nextTick(() => {
          const domains = this.unitInfo.allDomains.filter(domain =>
            this.unitInfo.domainIds.includes(domain.value)
          )
          if (domains && domains.length > 0) {
            this.selectedDomainIds = domains.map(domain => domain.value)
            this.unitInfo.domainIds = this.selectedDomainIds
          }
        })
      }
    },

    /**
     * 获取全量框架数据
     */
    getAllFrameworkData () {
      // 如果是 mc 则只获取 California
      let params = {}
      let url = this.isMC ? $api.urls().getStateFrameworkInfoByState : $api.urls().getAllFrameworkInfoByState
      if (this.isMC) {
        params = {
          state: 'California',
          country: 'United States',
          frameworkName: this.mcDefaultFrameworkName
        }
      }
      // 发送请求获取 states 列表
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().getAllFrameworkInfoByCountry)
          .then(response => {
            // 处理返回的数据
            this.frameworkData = response
            resolve()
          })
          .catch(error => {
            console.error('Error while fetching getAllFrameworkInfoByState:', error)
            reject(error)
          })
      })
    },

    // 年级选择变化时的处理函数
    handleGradeChange (grade) {
      // 获取年龄组的 hasActivity
      const ageGroup = this.grades.find(item => item.value === grade)
      this.selectedGrade = ageGroup.name
      // 设置级联选择器点击时处理函数
      this.setDefaultSelections()
    },

    // 级联选择变化时的处理函数
    handleCascaderChange (value, init = false) {
      if (!value || value.length < 2) {
        return
      }
      // 提前清空测评点，避免下拉框内容闪动
      this.unitInfo.allDomains = []
      if (value.length == 2) {
        let countryData = this.frameworkData.find(x => x.country == value[0])
        let state = countryData.stateFrameworkInfo[0].state
        value = [value[0], state, value[1]]
      }
      if (!init && value[1] && !(equalsIgnoreCase(value[1], 'CCSS') || equalsIgnoreCase(value[1], 'Head Start') || equalsIgnoreCase(value[1], 'AERO'))) {
        this.unitInfo.country = value[0]
        this.unitInfo.state = value[1]
      }
      this.unitInfo.frameworkId = value[2]
      // 是否使用默认 US 的州和城市
      let useDefaultUS = false
      // 切换州以后后面的城市应该清空
      switch(this.unitInfo.country.toLowerCase()) {
        case 'united states':
          this.statesAndCities = usStatesAndCities
          break
        case 'mexico':
          this.statesAndCities = mexicoStatesAndCities
          break
        case 'canada':
          this.statesAndCities = canadaStatesAndCities
          break
        default:
          // 默认使用 US 的州和城市
          useDefaultUS = true
        this.statesAndCities = usStatesAndCities
      }
      let cities = []
      // 如果使用默认 US 的州和城市，则设置默认值
      if (useDefaultUS && (!init || this.unitInfo.country !== 'United States')) {
        this.unitInfo.country = 'United States'
        this.unitInfo.state = 'California'
      }
      const foundState = this.statesAndCities.find(state => state.name === this.unitInfo.state)
      cities = foundState ? foundState.cities.sort() : []
      if (!init) {
        let userCity = this.currentUser.city
        if (userCity && cities.includes(userCity)) {
          this.unitInfo.city = userCity
        } else {
          if (cities &&   cities.length > 0) {
            this.unitInfo.city = ''
          }
        }
      }
      // 遍历 cascaderData 来找到对应的 frameworkName
      let frameworkName = ''
      for (const countryData of this.frameworkData) {
        for (const stateData of countryData.stateFrameworkInfo) {
          for (const framework of stateData.stateFrameworkInfo.frameworks) {
            if (framework.gradeFrameworkIdMap[this.selectedGrade] === this.unitInfo.frameworkId) {
              frameworkName = framework.frameworkName
              break
            }
          }
          if (frameworkName) break
        }
      }
      this.unitInfo.frameworkName = frameworkName
      // 如果是 mg，并且框架名称不是 California Preschool/Transitional Kindergarten Learning Foundations，则给出提示
      if (this.isMC && this.unitInfo.frameworkName !== this.mcDefaultFrameworkName) {
        this.$message({
          message: this.$t('loc.unitPlannerStep1FrameworkWarning'),
          type: 'warning'
        })
      }
      // 调用 fetchMeasuresFromBackend 方法来获取测评点数据
      this.getDomainInfo(this.fetchMeasuresFromBackend, value[2])
      // 修改框架时清除测评点的校验提示
      this.$refs.unitInfoFormRef && this.$refs.unitInfoFormRef.clearValidate('domainIds')
      this.$emit('notifySync')
    },

    // 级联选择器点击时的处理函数
    setDefaultSelections () {
      // 设置默认年级
      const defaultGrade = this.selectedGrade
      let found = false

      // 获取默认州和默认年级对应的框架ID
      const defaultCountryFrameworkData = this.frameworkData.find(country => country.country === this.unitInfo.country)
      const defaultFrameworkData = defaultCountryFrameworkData && defaultCountryFrameworkData.stateFrameworkInfo.find(stateData => stateData.state === this.unitInfo.state)
      if (defaultFrameworkData) {
        // 如果框架可以找到，则设置选中的框架，如果是 MC 平台优先选择 California Preschool/Transitional Kindergarten Learning Foundations
        const findDefaultFrameworkFunc = this.isMC ? framework => {
          return framework.frameworkName &&
          framework.frameworkName.toUpperCase().trim() === this.mcDefaultFrameworkName.toUpperCase().trim() &&
          framework.gradeFrameworkIdMap[defaultGrade]
        } : framework => framework.gradeFrameworkIdMap[defaultGrade];
        let defaultFramework = defaultFrameworkData.stateFrameworkInfo.frameworks.find(findDefaultFrameworkFunc);
        // 解决冲突
        if (!this.isMC) {
          defaultFramework = defaultFrameworkData.stateFrameworkInfo.frameworks.find(framework => framework.frameworkName.indexOf('K12 Standards') > 0 && framework.gradeFrameworkIdMap[defaultGrade]) ||
          defaultFrameworkData.stateFrameworkInfo.frameworks.find(framework => framework.gradeFrameworkIdMap[defaultGrade])
        }
        if (defaultFramework) {
          if (frameworkUtils.isCountryWithState(defaultCountryFrameworkData.country)) {
            this.selectedFrameworkData = [defaultCountryFrameworkData.country, defaultFrameworkData.state, defaultFramework.gradeFrameworkIdMap[defaultGrade]]
          } else {
            this.selectedFrameworkData = [defaultCountryFrameworkData.country, defaultFramework.gradeFrameworkIdMap[defaultGrade]]
          }
          // 只在年龄切换时更新位置信息
          this.unitInfo.country = defaultCountryFrameworkData.country
          this.unitInfo.state = defaultFrameworkData.state
          this.unitInfo.frameworkId = defaultFramework.gradeFrameworkIdMap[defaultGrade]
          this.unitInfo.frameworkName = defaultFramework.frameworkName
          found = true
        }
      }

      // 如果在默认州中没有找到，则遍历所有州和框架
      if (!found) {
        for (const countryData of this.frameworkData) {
          for (const stateData of countryData.stateFrameworkInfo) {
            for (const framework of stateData.stateFrameworkInfo.frameworks) {
              if (framework.gradeFrameworkIdMap[defaultGrade]) {
                if (frameworkUtils.isCountryWithState(countryData.country)) {
                  this.selectedFrameworkData = [countryData.country, stateData.state, framework.gradeFrameworkIdMap[defaultGrade]]
                } else {
                  this.selectedFrameworkData = [countryData.country, framework.gradeFrameworkIdMap[defaultGrade]]
                }
                // 只在年龄切换时更新位置信息
                this.unitInfo.country = countryData.country
                this.unitInfo.state = stateData.state
                this.unitInfo.frameworkId = framework.gradeFrameworkIdMap[defaultGrade]
                this.unitInfo.frameworkName = framework.frameworkName
                found = true
                break
              }
            }
            if (found) break
          }
          if (found) break
        }
      }
      // 触发级联选择器的 change 事件来更新测评点数据
      this.$nextTick(() => {
        this.$refs.stateStandardsCascader && this.$refs.stateStandardsCascader.$emit('change', this.selectedFrameworkData)
      })
    },

    // 根据框架ID和年级设置级联选择器数据
    setSelectedFrameworkDataByFrameworkIdAndGrade (frameworkId, grade) {
      let found = false
      // 遍历框架数据
      for (const countryData of this.frameworkData) {
        // 遍历州数据
        for (const stateData of countryData.stateFrameworkInfo) {
          // 遍历州框架数据
          for (const framework of stateData.stateFrameworkInfo.frameworks) {
            // 如果框架ID和年级匹配
            if (equalsIgnoreCase(framework.gradeFrameworkIdMap[grade], frameworkId)) {
              // 如果国家是美国或加拿大，则设置级联选择器数据为国家和州和框架ID
              if (frameworkUtils.isCountryWithState(countryData.country)) {
                this.selectedFrameworkData = [countryData.country, stateData.state, framework.gradeFrameworkIdMap[grade]]
              } else {
                // 否则设置级联选择器数据为国家和框架ID
                this.selectedFrameworkData = [countryData.country, framework.gradeFrameworkIdMap[grade]]
              }
              // 找到匹配的框架后，跳出循环
              found = true
              break
            }
          }
          // 找到匹配的州后，跳出循环
          if (found) break
        }
        // 找到匹配的州后，跳出循环
        if (found) break
      }
    },
    // 让 unit description 滚动条滚动到底部
    unitDescriptionScrollToBottom () {
      // 获取 unit description 输入框下的 textarea 标签
      const unitDescriptionInput = this.$el.querySelector('.unit-description-input > textarea')
      if (unitDescriptionInput) {
        // 让滚动条滚动到底部
        unitDescriptionInput.scrollTop = unitDescriptionInput.scrollHeight
      }
    },
    // 合并导入 Unit Description 的回调
    mergeImportInfo (importInfo) {
      let unitInfo = JSON.parse(JSON.stringify(this.unitInfo))
      let description = unitInfo.description
      // 拼接 unit description
      const tempDesc = (description ? description + '\n' : '') + importInfo
      // 截取最多两千个字符
      unitInfo.description = tempDesc.substring(0, 4000)
      // 设置修改后的信息，必须要设置整个对象，因为 unitInfo 是计算属性
      this.unitInfo = unitInfo
      // 合并之后保证输入框仍然有焦点
      this.$refs.unitDescriptionInputRef.focus()
      // 滚动条滚动到底部
      this.$nextTick(() => {
        this.unitDescriptionScrollToBottom()
      })
    },
    // 当单元描述输入框内容变化
    unitDescriptionInput () {
      // 获取 unit description 输入框下的 textarea 标签
      const unitDescriptionInput = this.$el.querySelector('.unit-description-input > textarea')
      if (unitDescriptionInput) {
        // 获取清空键，当滚动条出现时，重新计算其位置
        const clearIcon = this.$el.querySelector('.unit-description-clear-icon')
        this.$nextTick(() => {
          let showUnitDescriptionScroll = unitDescriptionInput.scrollHeight > unitDescriptionInput.clientHeight && unitDescriptionInput.scrollTop > 0
          // 设置清空键的位置
          if (clearIcon) {
            clearIcon.style.right = showUnitDescriptionScroll ? '8px' : '4px'
          }
        })
      }
      // 让滚动条随着文本增加,滚动条滚动到底部
      this.unitDescriptionScrollToBottom()
    },

    getState () {
      // 发送请求获取 states 列表
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().getStates)
          .then(response => {
            // 处理返回的数据
            this.states = response
            this.usStates = response
            resolve()
          })
          .catch(error => {
            console.error('Error while fetching states:', error)
            reject(error)
          })
      })
    },

    // 设置示例单元
    setExampleUnit: tools.debounce(function(isShowToast = true) {
      // 点击样例按钮埋点
      this.$analytics.sendEvent('cg_unit_create_exemplars')
      
      // 检查是否是第一次点击当前样例
      const isFirstClick = !this.exemplarsClickCount || this.exemplarsClickCount === 0
      
      if (isFirstClick) {
        // 第一次点击：应用当前样例，显示"已经应用 1"
        this.exemplarsClickCount = 1
      } else {
        // 第二次点击：切换到下一个样例
        this.exemplarsClickCount++
        this.exampleIndex++
      }
      
      let index = this.exampleIndex % this.examples.length
      let example = this.examples[index]
      this.$set(this.unitInfo, 'title', example.title)
      this.$set(this.unitInfo, 'description', example.description)
      this.$set(this.unitInfo, 'weekCount', 1)
      this.$set(this.unitInfo, 'isExampleUnit', true)
      this.$set(this.unitInfo, 'grade', example.grade || this.unitInfo.grade)
      this.$set(this.unitInfo, 'useDomain', true)
      this.$set(this.unitInfo, 'classroomType', 'IN_PERSON')
      // 重新计算框架信息
      this.handleGradeChange(example.grade || this.unitInfo.grade)
      // 如果 frameworkId 存在，设置 domainIds
      if (this.unitInfo.frameworkId) {
        const domainIds = Grade4FrameworkDomainsMapping[this.unitInfo.frameworkId.toUpperCase()]
        if (domainIds && domainIds.length > 0
          && this.unitInfo.allDomains && this.unitInfo.allDomains.length > 0) {
          // 等待 allDomains 数据加载完成
          this.$nextTick(() => {
            // 找到对应的领域
            const domains = this.unitInfo.allDomains.filter(domain => domainIds.includes(domain.value))
            // 设置选中的领域
            if (domains && domains.length > 0) {
              this.selectedDomainIds = domains.map(domain => domain.value)
              this.unitInfo.domainIds = this.selectedDomainIds
            } else {
              this.needSetDomainIds = true
            }
          })
        } else {
          this.needSetDomainIds = true
        }
      } else {
        this.needSetDomainIds = true
      }
      if (isShowToast) {
        // 显示 toast 提示
        this.$message({
          message: this.$t('loc.exemplarToastMessage', {
            index: (this.exampleIndex % this.examples.length) + 1,
            total: this.examples.length
          }),
          type: 'success'
        })
      }
    }, 500),
    // 初始化单元基本信息表单数据
    initUnitInfoFormData () {
      // 州信息
      // this.states = this.statesAndCities.map(state => state.name).sort()
      // 已有单元信息，不再初始化
      if (this.unitId || this.inAdaptSelect) {
        return
      }
      // 获取用户选择的年级
      let grade = localStorage.getItem('UNIT_GRADE' + this.currentUser.user_id) || 'Grade 4'
      let classroomType = localStorage.getItem('UNIT_CLASSROOM_TYPE' + this.currentUser.user_id) || 'IN_PERSON'
      // 如果使用的是 MC 平台，则默认年级为 TK (4-5)
      if (this.isMC) {
        grade = 'TK (4-5)'
      }
      let unitInfo = JSON.parse(localStorage.getItem('UNIT_INFO' + this.currentUser.user_id) || '{}')
      // 默认值
      let info = this.unitInfo;
      if (!this.unitInfo.isExampleUnit) {
        this.unitInfo = {
          country: unitInfo.country || 'United States',
          state: unitInfo.state || 'California',
          city: '',
          grade: unitInfo.grade || 'Grade 4',
          weekCount: info.weekCount || 1,
          language: unitInfo.language || 'English',
          frameworkId: unitInfo.frameworkId || '4A5ABEDB-FAEC-4B1C-8221-E038C1CFB804',
          frameworkName: unitInfo.frameworkName || 'California K12 Standards',
          allDomains: [],
          useLocation: true,
          useDomain: true,
          showLessonTemplateTip: false, // 是否显示课程模板提示
          useLessonTemplate: false, // 是否使用课程模板
          title: "",
          description: "",
          isExampleUnit: false,
          newRubrics: this.newRubrics,
          classroomType: classroomType
        }
      } else {
        this.unitInfo = {
          country: 'United States',
          state: 'California',
          city: '',
          grade: 'Grade 4',
          weekCount: 1,
          language: 'English',
          frameworkId: '4A5ABEDB-FAEC-4B1C-8221-E038C1CFB804',
          frameworkName: 'California K12 Standards',
          allDomains: [],
          useLocation: true,
          useDomain: true,
          showLessonTemplateTip: false, // 是否显示课程模板提示
          useLessonTemplate: false, // 是否使用课程模板
          title: info.title || "",
          description: info.description || "",
          isExampleUnit: info.isExampleUnit || false,
          newRubrics: this.newRubrics,
          classroomType: classroomType
        }
      }
      if (this.isMC) {
        this.unitInfo = {
          country: 'United States',
          state: 'California',
          city: '',
          grade: grade,
          weekCount: 4,
          language: 'English',
          frameworkId: 'DA5D2340-6D56-41B9-A0B6-5F3962043E8E',
          frameworkName: 'DRDP2015-Kindergarten Fundamental View',
          allDomains: [],
          useLocation: true,
          useDomain: true,
          classroomType: classroomType
        }
        this.unitInfo.frameworkName = this.mcDefaultFrameworkName
        this.unitInfo.weekCount = 2
      }
      // 如果没有缓存的单元信息，则初始化地理位置信息
      if (!localStorage.getItem('UNIT_INFO' + this.currentUser.user_id)) {
        this.initLocationInfo()
      }
    },
    // 初始化单元基本信息表单验证规则
    initUnitInfoFormRules (showWarning = true) {
      const weekNum = 10
      // 周数校验规则
      let validateWeekCount = (rule, value, callback) => {
        if (!showWarning) {
          showWarning = true
          callback()
          return
        }
        if (!value) {
          callback(new Error('Please enter week count!'))
          return
        }
        // 如果输入的存在小数点，则直接返回错误，提示需要一个整数
        value = value.toString()
        if (value && value.indexOf('.') !== -1) {
          callback(new Error('Please enter an integer number!'))
        }
        // 转换为数字
        value = parseInt(value)
        if (!Number.isInteger(value)) {
          callback(new Error('Please enter an integer number!'))
        } else if (value < this.unitWeekMin || value > weekNum) {
          callback(new Error(`Please enter a number between ${this.unitWeekMin} and ${weekNum}!`))
        } else {
          callback()
        }
      }
      // 标题非空字符串校验
      let validateTitle = (rule, value, callback) => {
        if (!showWarning) {
          callback()
          return
        }
        if (!value || value.trim().length < 1) {
          callback(new Error('Please enter unit name!'))
        } else {
          callback()
        }
      }
      // 描述非空字符串校验
      let valideDescription = (rule, value, callback) => {
        if (!showWarning) {
          callback()
          return
        }
        if (!value || value.trim().length < 1) {
          callback(new Error('Please enter unit description!'))
        } else {
          callback()
        }
      }
      let validateStandardsBoValue = (rule, value, callback) => {
        if (!showWarning) {
          callback()
          return
        }
        callback()
      }
      // 验证框架是否选择
      let validateFramework = (rule, value, callback) => {
        if (!showWarning) {
          callback()
          return
        }
        // 如果框架名称或选中的级联框架数据不为空，则验证通过
        if (this.unitInfo.frameworkName || this.selectedFrameworkData) {
          callback() // 调用回调函数，表示验证通过
        }
      }
      // 验证领域是否选择
      let validateDomainIds = (rule, value, callback) => {
        if (!showWarning) {
          callback()
          return
        }
        // unitProgress > 20 时通过验证
        if (this.unitProgress > 20) {
          callback()
          return
        }
        // 如果是按照领域自动分配测评点，则只需判断是否选择了领域
        if (this.unitInfo.useDomain) {
          if (this.unitInfo.domainIds && this.unitInfo.domainIds.length > 0) {
            return callback()
          } else {
            return callback(new Error(this.$t('loc.plan20')))
          }
        }
        // 获取选中的领域
        let selectedItems = this.selectedItems
        let maxItems = this.maxItems // 获取计算属性 maxItems
        // 验证是否选择了领域
        if (!selectedItems || selectedItems.length === 0) {
          return callback(new Error(this.$t('loc.plan20')))
        }

        // 验证选择的数量是否超出限制
        if (selectedItems.length > maxItems) {
          if (maxItems === 10) {
            return callback(new Error(this.$t('loc.selectMeasureForOneWeek')))
          } else if (maxItems === 20) {
            return callback(new Error(this.$t('loc.selectMeasureForTwoWeeks')))
          }
        }

        // 通过验证
        callback()
      }
      
      // 校训数量校验规则
      let validateProfile = (rule, value, callback) => {
        if (!showWarning) {
          callback()
          return
        }
        // 如果进度大于20或者是MC平台，跳过校验
        if (this.unitProgress > 20 || this.isMC) {
          callback()
          return
        }
        
        // 计算展开后的校训实际数量（包括子标准）
        const calculateActualRubricsCount = (rubrics) => {
          if (!rubrics || !Array.isArray(rubrics)) {
            return 0
          }
          
          let count = 0
          rubrics.forEach(portrait => {
            if (!portrait || !portrait.title) {
              return
            }
            
            if (portrait.subStandards && Array.isArray(portrait.subStandards) && portrait.subStandards.length > 0) {
              // 如果有子标准，计算子标准数量
              portrait.subStandards.forEach(subStandard => {
                if (subStandard && subStandard.title) {
                  count++
                }
              })
            } else {
              // 如果没有子标准，计算主标准
              count++
            }
          })
          
          return count
        }
        
        // 获取展开后的校训实际数量
        const selectedCount = calculateActualRubricsCount(this.unitInfo.newRubrics)
        const maxCount = Math.min(parseInt(this.unitInfo.weekCount || 1) * 3, 9)
        
        // 如果校训数量超过限制
        if (selectedCount > maxCount) {
          let message = ''
          if (maxCount === 3) {
            message = this.$t('loc.learnerProfileSelectLimitFor1Week');
          } else if (maxCount === 6) {
            message = this.$t('loc.learnerProfileSelectLimitFor2Week');
          } else if (maxCount === 9) {
            message = this.$t('loc.learnerProfileSelectLimitFor3Week');
          }
          callback(new Error(message))
          return
        }
        
        // 通过验证
        callback()
      }

      // 表单校验规则
      this.unitInfoRules = {
        title: [
          { required: true, message: 'Please enter unit name!', trigger: 'blur' },
          { min: 1, max: 200, message: 'Length should be 1 to 200', trigger: 'blur' },
          { validator: validateTitle, trigger: 'change' },
          { validator: validateTitle, trigger: 'blur' }
        ],
        description: [
          { required: true, message: 'Please enter unit description!', trigger: 'blur' },
          { validator: valideDescription, trigger: 'change' },
          { validator: valideDescription, trigger: 'blur' }
        ],
        grade: [
          { required: true, message: 'Please select grade!', trigger: 'blur' }
        ],
        weekCount: [
          { required: true, message: 'Please enter the number of weeks!', trigger: 'blur' },
          { validator: validateWeekCount, trigger: ['change', 'blur'] }
        ],
        framework: [
          { required: true, validator: validateFramework, trigger: 'blur' }
        ],
        domainIds: [
          { required: true, validator: validateDomainIds, trigger: 'blur' }
        ],
        profile: [
          { validator: validateProfile, trigger: ['change', 'blur'] }
        ],
        state: [
          { required: true, message: 'Please select state!', trigger: 'blur' }
        ],
        standards1: [
          { validator: validateStandardsBoValue, trigger: ['blur', 'change'] }
        ]

      }
    },

    // 验证单元基本信息表单
    validate () {
      return new Promise((resolve, reject) => {
        this.$refs.unitInfoFormRef.validate((valid, obj) => {
          if (valid) {
            resolve()
          } else {
            Object.entries(obj).forEach(([key, value]) => {
              // 页面跳转到未通过验证的位置
              this.$nextTick(() => {
                if (this.$refs[key]) {
                  this.$refs[key].$el.scrollIntoView()
                }
              })
            })
            reject(obj)
          }
        })
      })
    },
    // 位置信息默认值
    initLocationInfo () {
      if (user.state.currentUser.country !== null && user.state.currentUser.state !== null && this.states.includes(user.state.currentUser.state)) {
        this.unitInfo.country = user.state.currentUser.country
        this.unitInfo.state = user.state.currentUser.state
        this.unitInfo.city = user.state.currentUser.city
      }
      // 如果是 mg 平台，则默认州为 California
      if (this.isMC) {
        this.unitInfo.state = 'California'
        this.unitInfo.country = 'United States'
      }
    },
    // 州选择后省略号
    selectVisible (e,refName) {
      e == false ? this.$refs[refName].blur() : ''
    },
    // 改变领域复选框宽度
    handleResize () {
      // 获取领域复选框外部容器的宽度
      let dropdown = document.querySelector('.el-dropdown')
      if (!dropdown) {
        setTimeout(() => {
          this.handleResize()
        }, 200)
        return
      }
      var containerWidth = dropdown.offsetWidth
      // 将外部容器的宽度应用到下拉框上
      if (containerWidth === 0) {
        setTimeout(() => {
          this.handleResize()
        }, 200)
      } else {
        let dropdownItem = this.$refs['dropdown-item']
        if (dropdownItem) {
          let element = dropdownItem.$el
          element.style.width = containerWidth + 'px'
        }
      }
    },

    // 使用历史记录
    usePromptHistory (historyData) {
      // 如果当前进度大于 20，则只更新 unit 名称和描述
      if (this.unitProgress > 20) {
        this.unitInfo.title = historyData.title
        this.unitInfo.description = historyData.description
        return
      }
      this.usePrompt = true
      let unitInfo = JSON.parse(JSON.stringify(this.unitInfo))
      unitInfo.title = historyData.title
      unitInfo.description = historyData.description
      unitInfo.grade = historyData.grade
      unitInfo.state = historyData.state
      unitInfo.country = historyData.country || 'United States'
      unitInfo.frameworkId = historyData.frameworkId
      unitInfo.frameworkName = historyData.frameworkName
      unitInfo.weekCount = historyData.weekCount
      unitInfo.classroomType = historyData.classroomType || 'IN_PERSON'
      if (this.isMC) {
        unitInfo.weekCount = 2
      }
      unitInfo.useDomain = historyData.useDomain
      unitInfo.measureIds = historyData.measures.map(measure => measure.id)
      // K 以上年龄截取前 3 个学科，限制学科数量为 3 个
      let spliceDomains = this.earlyAgeGroup.includes(historyData.grade) ? historyData.domains : historyData.domains.splice(0, 3)
      unitInfo.domainIds = spliceDomains.map(domain => domain.id)
      // 设置年级
      this.selectedGrade = historyData.grade
      // 设置框架
      this.setSelectedFrameworkDataByFrameworkIdAndGrade(historyData.frameworkId, historyData.grade)
      this.$store.commit('curriculum/SET_BAE_INFO', JSON.parse(JSON.stringify(unitInfo)))
      // 获取框架信息
      if (historyData.useDomain) {
        this.$axios.get($api.urls().getDomainInfosByFrameworkId, { params: { frameworkId: historyData.frameworkId } })
        .then(res => {
          unitInfo.allDomains = res.filter(v => (v.abbreviation && v.abbreviation.trim() !== '') || (v.name && v.name.trim() !== '')) // 过滤出 abbreviation 或 name 不为空的项
          .map(item => ({
            value: item.id,
            label: item.name.trim() !== '' ? item.name.trim() : item.abbreviation.trim(), // 如果 name 为空，则使用 abbreviation；否则使用 name
            selected: unitInfo.domainIds.includes(item.id)
          }))
          this.$store.commit('curriculum/SET_BAE_INFO', JSON.parse(JSON.stringify(unitInfo)))
          this.usePrompt = false
          this.selectedDomainIds = unitInfo.domainIds
          this.fetchMeasuresFromBackend(historyData.frameworkId)
        })
      } else {
        this.loadingDomainData = true
        this.$store.dispatch('curriculum/getFrameworkDomains', {
          frameworkId: historyData.frameworkId,
          compress: false
        })
        .then(measures => {
          return this.changeDomainInfo().then(() => Promise.resolve({ measures }))
        })
        .then(measures => {
            // 处理响应，将测评点数据存储在合适的属性中
            this.treeData = measures
            this.extractMeasures(this.treeData, unitInfo.measureIds).forEach(measure => {
              this.selectedItems.push(measure)
            })
            this.loadingDomainData = false
            this.$store.commit('curriculum/SET_BAE_INFO', JSON.parse(JSON.stringify(unitInfo)))
        })
        this.usePrompt = false
      }
    },

    // 收集历史记录数据
    collectPromptHistoryData () {
      // 获取历史记录
      let selectedDomains = this.unitInfo.allDomains && this.unitInfo.allDomains.filter(domain => this.unitInfo.domainIds.includes(domain.value)).map(domain => ({ id: domain.value, abbreviation: domain.label }))
      let selectedMeasures = []
      if (this.treeData && this.treeData.length > 0) {
        selectedMeasures = this.extractMeasures(this.treeData, this.unitInfo.measureIds).map(x => ({ id: x.id, abbreviation: x.abbreviation }))
      } else {
        selectedMeasures = this.unitInfo && this.unitInfo.measures.map(measure => ({ id: measure.id, abbreviation: measure.abbreviation }))
      }
      let country = this.selectedFrameworkData[0]
      let hasStateInFrameworkData = frameworkUtils.isCountryWithState(country)
    
      let historyData = {
        unitId: this.unitId,
        title: this.unitInfo.title,
        description: this.unitInfo.description,
        grade: this.selectedGrade,
        country: this.unitInfo.country,
        state: this.unitInfo.state,
        frameworkId: hasStateInFrameworkData ? this.selectedFrameworkData[2] : this.selectedFrameworkData[1],
        frameworkName: this.unitInfo.frameworkName,
        weekCount: Number(this.unitInfo.weekCount),
        useDomain: this.unitInfo.useDomain,
        measures: selectedMeasures,
        domains: selectedDomains,
        classroomType: this.showClassroomSetup ? this.unitInfo.classroomType : 'IN_PERSON'
      }
      this.unitInfo.classroomType = historyData.classroomType
      return historyData
    },
    // 提交州标准请求
    handleHelpSubmit () {
      // 如果内容为空，显示提示信息
      if (!this.helpContent || this.helpContent.trim() === '') {
        this.$message.warning('Please enter your request details.')
        return
      }
      // 设置提交状态为 true
      this.isSubmitting = true
      // 调用接口提交请求
      this.$axios.post($api.urls().submitStateStandardsRequest, {
        content: this.helpContent,
        stateStandards: this.stateStandards,
        state: this.state
      }).then(() => {
        this.helpDialogVisible = false
        this.helpContent = ''
        this.stateStandards = ''
        this.state = ''
        // 添加成功提示消息
        this.$message({
          message: 'Submission successful. We will process your request as soon as possible.',
          type: 'success'
        })
      }).catch(error => {
        if (error.response) {
          this.$message.error(error.response.data.error_message)
        } else {
          this.$message.error('Submission failed. Please try again later.')
        }
      }).finally(() => {
        // 设置提交状态为 false
        this.isSubmitting = false
      })
    },
    // Exemplars按钮点击事件，点击后重置 tooltip 状态为 hover 弹出
    handleExemplarsClick() {
      // 先重置手动控制状态，恢复为 hover 弹出
      this.showExemplarsTip = false
      this.showExemplarsTipManual = false
      // 再调用原有的示例单元填充逻辑
      this.setExampleUnit()
    },
    // 处理Exemplars按钮滚动事件
    handleExemplarsScroll(e) {
      // 如果手动控制状态为 true，则根据滚动位置显示 tooltip
      if (this.showExemplarsTipManual) {
        // 如果按钮在可视区域，则显示 tooltip
        this.showExemplarsTip = e.target.scrollTop > 52 ? false : true
      }
    },
  
    // 打开校训编辑弹框
    addAgency() {
      this.$nextTick(() => {
        if (this.$refs.UnitInfoLearnerProfile) {
          return this.$refs.UnitInfoLearnerProfile.addAgency()
        }
      })
    },
  
    // 打开校训选择弹框
    openSelectRubricsDialog() {
      this.$nextTick(() => {
        if (this.$refs.UnitInfoLearnerProfile && this.$refs.UnitInfoLearnerProfile.$refs.rubricsRowRef) {
          return this.$refs.UnitInfoLearnerProfile.$refs.rubricsRowRef.openDialog()
        }
      })
    },
    
    // 打开校训选择弹框
    openGenerateRubricsDialog() {
      this.$nextTick(() => {
        if (this.$refs.UnitInfoLearnerProfile && this.$refs.UnitInfoLearnerProfile.$refs.learnerProfilePopupRef) {
          return this.$refs.UnitInfoLearnerProfile.$refs.learnerProfilePopupRef.open()
        }
      })
    },
  
    // 删除选择的校训
    removeRubrics(title) {
      this.$nextTick(() => {
        const unitInfoLearnerProfile = this.$refs.UnitInfoLearnerProfile
        if (unitInfoLearnerProfile) {
          // 获取选择的校训
          const displayRubrics = unitInfoLearnerProfile.displayRubrics
          // 找到要删除的校训
          const delRubrics = displayRubrics.find(rubric => equalsIgnoreCase(rubric.title, title))
          // 删除校训
          if (delRubrics) {
            unitInfoLearnerProfile.removeDisplayItem(delRubrics)
          }
        }
      })
    },
  }
}
</script>
<style lang="less">
.lesson-assistant-domain-select-dropdown {
  padding: 0;

  /* 调整边距大小 */
  .el-select-dropdown__list {
    padding: 12px 0;
  }

  .el-select-group__wrap {
    padding-left: 16px;
  }

  .el-select-dropdown__item {
    padding-left: 26px;

    .el-checkbox {
      margin: 0;
    }
  }
}
</style>
<style lang="less" scoped>
.lesson-assistant-domain-select {
  /deep/.el-input__prefix {
    padding-left: 12px;
    color: #676879;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
    cursor: pointer;
    color: #10B3B7;
    justify-content: center;
  }
  /deep/.el-input__suffix {
    display: none;
  }
}
.has-selected-subjects {
  /deep/.el-input__prefix {
    padding-right: 12px;
    color: #676879;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
    cursor: pointer;
    color: #10B3B7;
    justify-content: flex-end;
    transition: all 0s;
  }
}
/deep/.lg-tabs-assistant-domain-tabs {
  .el-tabs__item{
    color: #111C1C !important;
    font-weight: 400 !important;
    width: 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-tabs__nav-scroll {
    width: 100%;
    .el-tabs__nav {
      width: 100%;
    }
  }

  // 禁用状态的样式
  .el-tabs__item.is-disabled {
    color: #C0C4CC !important;
    cursor: not-allowed !important;

    &:hover {
      color: #C0C4CC !important;
      background-color: transparent !important;
    }

    // 禁用状态下的图标和文字
    .lg-icon {
      color: #C0C4CC !important;
    }

    span {
      color: #C0C4CC !important;
    }
  }
}

.lg-profile-result {
  border: 1px solid #DCDFE6;
}

.location {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  > div {
    display: flex;
    align-items: center;
    flex-grow: 1;
  }

  .title-font-14 {
    white-space: nowrap; /* 确保文字一行展示 */
  }

  button {
    background: none;
    border: none;
    color: #10B3B7;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-left: auto; /* 将按钮推到最右边 */

    span {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }

  .el-icon-map-location {
    font-size: 20px; /* 设置图标的大小 */
  }
}
.location-align{
  display: flex;
  align-items: center;
}

@media (max-width: 1366px) {
  .location {
    display: block !important;
    button {
      margin-left: -6px;
      width: 100%;
    }
  }
}

/deep/ .location-from-dialog {
  .el-select {
    width: 100% !important;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-dialog__body {
    padding: 12px 20px !important;
  }

  .el-dialog__title {
    color: var(--color-text-primary);
    font-size: 20px;
  }
}
// 州下拉框样式
.stateSelectStyle{
   display: flex;
   /deep/.el-input__inner {
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 溢出隐藏 */
      text-overflow: ellipsis; /* 使用省略号 */
    }
}
.gradeSelectStyle{
  /deep/.el-input__inner {
      text-overflow: ellipsis; /* 使用省略号 */
    }
}
.frameworkSelectStyle{
  /deep/.el-input__inner {
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 溢出隐藏 */
      text-overflow: ellipsis; /* 使用省略号 */
    }
}
.bg-light.bg, .bg-light .bg{
  background-color:#F5F6F8;
}

.custom-button {
  width: 100%;
  background-color: white;
  border-color: #dcdfe6;
  color: #111C1C;
}

.custom-button:hover {
  background-color: white;
  border-color: #dcdfe6;
  color: #111C1C;
}
// .m-t-sm {
//   margin-top: -12px;
// }
.wrapper-sm{
  padding: 6px 8px 8px 6px;
}
.selectMeasure {
  font-size: 12px;
  margin: 2px 0px 0px 2px;
  padding: 7.5px;

  line-height: 1.1;
  border-radius: 4px;
  border: 1px solid #dbdbdb;
  background-color: #ffffff;
  font-weight: 400;

  display: inline-flex !important; /* 使用 Flexbox 布局 */
  align-items: center; /* 垂直居中对齐 */
}

.vertical-dropdown .el-checkbox-group {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.el-dropdown {
  width: 100%;
  // border: 1px solid #DCDFE6; /* 添加边框样式 */
  // border-radius: 4px; /* 添加圆角样式 */
}

.dropdown-button {
  color: #111C1C;
  width: 100%;
  font-weight: 400;
}

.full-width-button {
  width: 100%;
  color: #10B3B7;
  font-weight: 600;
  border: 1px solid #DCDFE6; /* 添加边框样式 */
  border-radius: 4px; /* 添加圆角样式 */
}

.full-width-button:hover,
.full-width-button:focus,
.full-width-button:active {
  /* 覆盖悬浮和点击时的背景颜色 */
  background-color: initial;
  /* 取消盒阴影 */
  box-shadow: none;
  /* 取消文字颜色变化 */
  color: initial;
}

.dropdown-checkbox {
  margin-left: 8px;
}

// 框架下拉框自动换行
.el-select-dropdown .el-select-dropdown__item {
  white-space: normal !important;
}

// 复选框每个选项换行
.option-text {
  white-space: normal !important;
}
// 调整复选框滚动条
.dropdown-checkbox-group{
  overflow-x: hidden;
  overflow-y: hidden;
  max-height: 200px;
}
// 调整复选框全选按钮
.dropdown-checkbox{
  white-space: normal !important;
  display: flex;
  align-items: top;
}
/deep/ .el-checkbox__input{
  padding-top:4px;
}

// 调整复选框每个选项
.dropdown-checkbox-item {
  margin-top: 10px;
  margin-left: 24px;

  display: flex;
  align-items: top;
}

// 设置禁选后颜色
.el-dropdown [disabled] {
  color: #32333866 !important;
  background-color: #EBEEF5;
  opacity: 1 !important;
}
/deep/ .el-button.is-loading:not(.state-standards-help-dialog .el-button) {
  background-color: transparent !important;
}

.el-dropdown-menu {
  white-space: normal !important;
  padding: 10px 20px 10px 20px; /* 调整边距大小 */
  max-height: 300px;
  overflow-x: hidden;
}

/* 当复选框被选中时，改变字体颜色为 #10B3B7 */
.custom-option {
  color: #10B3B7;
}

.divider-border {
  border-bottom: 1px solid #DCDFE6;
  padding-bottom: 9px;
  margin-bottom: -8px;
}

.indented-option {
  padding-left: 20px; /* 调整缩进的大小 */
}

/deep/ .unit-title > .el-form-item__label {
  line-height: 34px!important;
}

.description {
  margin-bottom: 0 !important;
  /deep/ .el-form-item__content {
    height: 145px;
  }

  /deep/ .el-form-item__content > .el-form-item__error {
    top: -38px;
    position: relative;
  }

  /deep/ .el-textarea .el-input__count {
    background: transparent !important;
    bottom: -7px !important;
    right: 24px !important;
  }
}

/deep/ .el-form--label-top .el-form-item__label {
  font-weight: 600;
  padding: 0;
  line-height: 24px;
}

/deep/ .domain-label .el-form-item__content {
  line-height: 32px;
}

/deep/ .el-form-item {
  margin-bottom: 12px;
}

/deep/ label {
  margin-bottom: 4px;
}
.unit-description-clear-icon {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  color: var(--color-info)
}
/deep/ .unit-description-input > textarea{
  padding-right: 20px;
  padding-bottom: 40px;
  resize: none;
  word-break: normal;
  // white-space: pre-wrap;
  // word-wrap: break-word;
}

.unit-prompt-history {
  display: inline-flex;
  justify-content: flex-end;
}

.width-36-height-36 {
  width: 36px;
  height: 36px;
}

.unit-import-adapt-form {
  width: 100%;
  padding: 20px;
  margin-top: 20px;
  border-radius: 8px;
  border: 1px solid var(--dcdfe-6, #DCDFE6);
  
  .location {
    margin-top: 0;
    justify-content: unset;
    
    > div {
      flex-grow: unset;
    }
  }
  
  .location-align {
    margin-left: 12px;
  }
  
  .resource-settings {
    margin-top: 20px;
  }
}

.unit-import-adapt-form-pog {
  color: var(--111-c-1-c, #111C1C);
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
  margin-bottom: 20px;
  
  i {
    font-size: 20px;
    color: #676879;
    margin-left: 4px;
    font-weight: 400;
    cursor: pointer;
  }
}

.unit-import-adapt-form-pog-content {
  display: flex;
  align-items: center;
}

.unit-import-adapt-form-pog-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--10-b-3-b-7, #10B3B7);
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
  width: 166px;
  height: 36px;
  padding: 9px 12px;
  margin-left: 38px;
  border-radius: 4px;
  border: 2px solid var(--10-b-3-b-7, #10B3B7);
  background: #FFF;
  cursor: pointer;
  
  i {
    color: #10B3B7;
    font-size: 20px;
    margin-right: 4px;
  }
}

.unit-import-rubric {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 8px;
  margin-top: 4px;
  border-radius: 4px;
  border: 1px solid var(--dcdfe-6, #DCDFE6);
  background: var(--ffffff, #FFF);
  cursor: pointer;
  
  i {
    font-size: 20px;
    color: #10B3B7;
  }
}

.unit-import-rubric-result {
  width: 95%;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.unit-import-rubric-item {
  display: flex;
  height: 29px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 2px;
  border-radius: 4px;
  background: #E8F9FA;
  color: var(--10-b-3-b-7, #10B3B7);
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  cursor: context-menu;
  
  i {
    font-size: 12px;
    color: #10B3B7;
  }
}
</style>
<style lang="less">
/* Classroom Setup 样式 */
.classroom-setup-section {
  margin-bottom: 16px;

  .classroom-setup-content {
    background: #F5F6F8;
    border-radius: 4px;
    padding: 6px 12px;

    .teaching-mode-options {
      display: flex;
      gap: 8px;
      align-items: center;
      flex-direction: row;
      flex-wrap: wrap;

      .teaching-mode-radio {
        margin: 0;
        line-height: 24px;

        /deep/ .el-radio__input {
          .el-radio__inner {
            width: 16px;
            height: 16px;
            border: 2px solid #DCDFE6;

            &::after {
              width: 6px;
              height: 6px;
            }
          }

          &.is-checked .el-radio__inner {
            border-color: #10B3B7;
            background-color: #10B3B7;
          }
        }

        &.is-checked /deep/ .el-radio__label {
          color: #10b3b7 !important;
        }
      }
    }
  }
}
.public-lesson-assistantascader-class {
  // 添加底部边框作为分割线
  .help-standards-container {
    border-top: 1px solid #DCDFE6;
    padding: 9px 16px;
  }
}
.state-standards-help-dialog {
  .el-dialog__header {
    padding: 24px 24px 0;
  }
  .el-dialog__title {
    font-size: 20px;
  }
  .el-dialog__body {
    padding: 8px 24px 24px 24px;

    .el-form-item {
      margin-bottom: 0px !important;
      
      .el-form-item__label {
        line-height: 1 !important;
        margin-top: 16px;
        padding-bottom: 8px;
        font-weight: 600;  // 加粗字体
        font-size: 16px;   // 增加字体大小
        color: #111C1C;
      }

      .el-input {
        width: 100%;
      }
    }
  }
  .el-dialog__footer {
    padding: 0 24px 24px;
  }
}
</style>

