<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="100%"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    custom-class="transparent-dialog full-screen-dialog"
    @click.stop>
    <div role="dialog" class="dialog-content">
      <div class="backdrop-blur"></div>
      <div class="content-wrapper">
        <img class="image-container" src="~@/assets/img/lesson2/unitPlanner/unit_loading.png"/>
        <div class="loader-container">
          <div class="loader"></div>
        </div>
        <div class="loading-text">Hold tight! The magic is happening...</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'UnitStep1LoadingDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
}
</script>

<style lang="less" scoped>
.dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* 使用视口高度而不是固定像素 */
  width: 100%;
  position: relative;
}

.backdrop-blur {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: -1;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  background-color: transparent;
  filter: none !important;
  position: relative;
}

.image-container {
  width: 170px;
  height: 178px;
  margin-bottom: 20px;
  filter: none !important;
  position: relative;
  z-index: 20;
}

.loader-container {
  margin-top: 10px;
  filter: none !important;
  position: relative;
  z-index: 20;
}

.loader {
  display: block;
  --height-of-loader: 12px;
  width: 600px;
  @media screen and (max-width: 768px) {
    width:320px !important;
  }
  height: var(--height-of-loader);
  border-radius: 15px;
  background-color: rgba(0, 0, 0, 0.2);
  position: relative;
}

.loader::before {
  content: "";
  position: absolute;
  background: linear-gradient(90deg, #AA89F2 0%, #10B3B7 100%);
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  border-radius: 15px;
  animation: moving 3s ease-in-out infinite;
}

@keyframes moving {
  50% {
    width: 100%;
  }

  100% {
    width: 0;
    right: 0;
    left: unset;
  }
}

/deep/ .transparent-dialog {
  background-color: white !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  opacity: 0.8;

  .el-dialog__header,
  .el-dialog__body {
    background-color: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
    filter: none !important;
  }

  .el-dialog__wrapper {
    background-color: transparent !important;
  }
}

/deep/ .el-dialog__wrapper {
  background-color: rgba(0, 0, 0, 0) !important;
}

/deep/ .el-dialog {
  background-color: white !important;
  opacity: 0.9;
  filter: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/deep/ .full-screen-dialog {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
  max-width: 100vw !important;

  .el-dialog {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

.loading-text {
  color: #676879;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px; /* 144.444% */
  margin-top: 20px;
  filter: none !important;
  position: relative;
  z-index: 20;
}
</style>