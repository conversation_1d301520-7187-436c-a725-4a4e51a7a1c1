<template>
    <div class="w-full" body-style="height: 100%;">
        <!-- 可编辑内容 -->
        <el-form
                ref="unitOverviewFormRef"
                label-position="top"
                label-width="100px"
                :model="currentModel"
                :rules="unitOverviewRules"
                :validate-on-rule-change="false"
                class="m-t-sm"
                v-if="editable"
            >
                <div class="display-flex justify-content-between align-items">
                  <div class="display-flex justify-content-between w-full">
                    <div class="display-flex w-full">
                      <!-- 封面 -->
                      <el-form-item prop="coverMedias">
                        <div class="cover-container">
                          <el-skeleton animated :loading="!!unitOverview.generateUnitCoverLoading || loading" class="position-relative">
                            <template slot="template">
                              <el-skeleton-item variant="image" style="width: 370px; height: 208px;"/>
                            </template>
                            <template>
                              <!-- 重新生成、替换按钮 -->
                              <div class="cover-operation">
                                <ReplaceCover :searchKeywords="unitOverview.coverKeywords" type="unit" @generateCover="generateUnitCover" @setCoverInfo="setCoverInfo" />
                              </div>
                              <div class="position-relative" style="width: 370px">
                                <MediasUploader
                                  ref="mediasUploader"
                                  :maxFileCount="1"
                                  :uploaderRatio="'16-9'"
                                  :priviewRatio="'16-9'"
                                  :priviewColSpan="24"
                                  :uploaderTheme="'theme-2'"
                                  :previewFiles="coverImages"
                                  @change="updateImagesEvent"
                                >
                                  <template slot="placeholder-default">
                                    <i class="lg-icon lg-icon-picture text-muted" style="font-size: 50px!important;"></i>
                                    <div class="el-upload__text m-b-sm">
                                      <div>
                                        {{$t('loc.unitPlannerStep1DropImage')}}
                                      </div>
                                      <div class="media-desc">
                                        ({{$t('loc.unitPlannerStep1Under20M')}})
                                      </div>
                                    </div>
                                    <el-button type="primary" round size="medium">
                                      <div class="display-flex justify-content align-items">
                                        <i class="el-icon-upload2 font-size-20"></i>
                                          <span>{{$t('loc.upload')}}</span>
                                      </div>
                                    </el-button>
                                  </template>
                                </MediasUploader>
                              </div>
                            </template>
                          </el-skeleton>
                        </div>
                      </el-form-item>
                      <!-- 单元名称 -->
                      <div class="font-size-20 font-bold lg-margin-left-20" v-if="!lightAdapt">
                        {{ unitOverview.title }}
                      </div>
                     <div v-if="lightAdapt" class="gap-6 lg-margin-left-20 w-full">
                      <div class="font-size-20 font-bold">
                        <el-input v-model="unitOverview.title" maxlength="90"  @blur="unitNameBlur" class="unit-name-input">
                        </el-input>                      </div>
                          <div class="age-group-framework-info">
                      <!-- 年龄组 -->
                          <div class="read-only-info-item text-no-wrap" v-if="unitInfo.grade">
                            {{ 'Age Group: ' + unitInfo.grade }}
                          </div>
                          <!-- 框架 -->
                          <div class="read-only-info-item text-no-wrap text-overflow-ellipsis" :title="unitInfo.frameworkName"> {{ unitInfo.frameworkName }} </div>
                        </div>
                        <div class="week-count-info">
                          <el-row class="lesson-field-time ipad_width"
                            v-if="unitInfo.weekCount || unitInfo.activityCount">
                            <!-- 准备时间 -->
                            <el-col class="lesson-field-time-item" v-if="unitInfo.weekCount">
                              <span class="lesson-field-value">
                               Weeks
                              </span>
                              <span class="lesson-field-label"> {{ unitInfo.weekCount }} 
                              </span>
                            </el-col>
                            <el-divider direction="vertical" class="divider-vertical"></el-divider>
                            <el-col class="lesson-field-time-item" :class="{'before-item' : unitInfo.weekCount}"
                                    v-if="unitInfo.activityCount">
                        
                              <span class="lesson-field-value">
                                 Activities
                              </span>
                              <span class="lesson-field-label">
                                {{ unitInfo.activityCount }}
                              </span>
                            </el-col>
                          </el-row>
                        </div>
                        </div> 
                    </div>
                  </div>
                </div>
                <!-- 旧版固定 Unit Foundation 模块 -->
                <!-- 当前只有 FOLC 需要启动新版处理 -->
                <div v-if="!isCustomFoundation">
                  <!-- Overview -->
                  <el-form-item :label="$t('loc.unitOverview')" prop="overview">
                    <el-skeleton :rows="4" animated :loading="loading && !unitOverview.overview">
                      <template>
                          <editor v-model="unitOverview.overview"  @blur="validateUnitForm('overview')" :placeholder="$t('loc.unitOverviewPlaceholder')" />
                        <!-- <el-input v-model="unitOverview.overview" type="textarea" :autosize="{ minRows: 2, maxRows: isMobile ? 10 : 4}" /> -->
                      </template>
                    </el-skeleton>
                  </el-form-item>
                  <!-- Unit Trajectory -->
                  <el-form-item :label="$t('loc.unitPlannerStep1UnitTrajectory')" prop="trajectory">
                    <el-skeleton :rows="4" animated :loading="loading && !unitOverview.trajectory">
                      <template>
                        <editor v-model="unitOverview.trajectory" @blur="validateUnitForm('trajectory')" :placeholder="$t('loc.unitTrajectoryPlaceholder')"/>
                        <!-- <el-input v-model="unitOverview.trajectory" type="textarea" :autosize="{ minRows: 4, maxRows: 20}" /> -->
                      </template>
                    </el-skeleton>
                  </el-form-item>
                  <!-- Concepts -->
                  <el-form-item :label="$t('loc.unitPlannerStep1Concepts')" prop="concepts">
                    <el-skeleton :rows="4" animated :loading="loading && !unitOverview.concepts">
                      <template>
                        <editor v-model="unitOverview.concepts" @blur="validateUnitForm('concepts')" :placeholder="$t('loc.unitConceptsPlaceholder')"/>
                        <!-- <el-input v-model="unitOverview.concepts" type="textarea" :autosize="{ minRows: 4, maxRows: 10}" /> -->
                      </template>
                    </el-skeleton>
                  </el-form-item>
                  <!-- Guiding Questions -->
                  <el-form-item :label="$t('loc.unitPlannerStep1GuidingQuestions')" prop="guidingQuestions">
                    <el-skeleton :rows="4" animated :loading="loading && !unitOverview.guidingQuestions">
                      <template>
                        <editor v-model="unitOverview.guidingQuestions" @blur="validateUnitForm('guidingQuestions')" :placeholder="$t('loc.unitGuidingQuestionsPlaceholder')"/>
                        <!-- <el-input v-model="unitOverview.guidingQuestions" type="textarea" :autosize="{ minRows: 4, maxRows: 10}" /> -->
                      </template>
                    </el-skeleton>
                  </el-form-item>
                </div>

                <!-- 新版自定义 Unit Foundation 模块 module.key是由 name${index} 拼接出来的数据 -->
                <div v-if="isCustomFoundation">
                  <el-form-item
                    v-for="(module, index) in customFoundationModule"
                    :key="index"
                    :label="module.name"
                    :prop="module.key"
                  >
                    <el-skeleton :rows="4" animated :loading="loading && (!customFoundationData || !customFoundationData[module.key])">
                      <template #default>
                        <editor
                          v-model="customFoundationData[module.key]"
                          @blur="validateUnitForm(module.key)"
                        />
                      </template>
                    </el-skeleton>
                  </el-form-item>
                </div>
        </el-form>
        <!-- 展示内容 -->
        <div v-else>
            <!-- Overview -->
            <div class="section-title">
                $t('loc.unitOverview')
            </div>
            <div class="" v-html="unitOverview.overview.replace(/\n/g, '<br/>')">
            </div>
            <!-- Unit Trajectory -->
            <div class="section-title">
                $t('loc.unitPlannerStep1UnitTrajectory')
            </div>
            <div class="" v-html="unitOverview.trajectory.replace(/\n/g, '<br/>')">
            </div>
            <!-- Concepts -->
            <div class="section-title">
                $t('loc.unitPlannerStep1Concepts')
            </div>
            <div class="" v-html="unitOverview.concepts.replace(/\n/g, '<br/>')">
            </div>
            <!-- Guiding Questions -->
            <div class="section-title">
                $t('loc.unitPlannerStep1GuidingQuestions')
            </div>
            <div class="" v-html="unitOverview.guidingQuestions.replace(/\n/g, '<br/>')">
            </div>
        </div>
    </div>
</template>

<script>
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import MediasUploader from '@/views/curriculum/components/meida/MediasUploader.vue'
import regenerateIcon from '@/assets/img/lesson2/plan/regenerate.svg'
import { mapState } from 'vuex'
import responsiveMixin from '@/mixins/responsive'
import {equalsIgnoreCase} from '@/utils/common'
import ReplaceCover from './ReplaceCover.vue'

export default {
  components: { MediasUploader, Editor, ReplaceCover },
  mixins: [responsiveMixin],
    props: {
        // 单元信息
        unitInfo: {
          type: Object,
          default: () => {}
        },
        // 单元概览
        unitOverview: {
            type: Object,
            default: () => {}
        },
        // 是否可编辑
        editable: {
            type: Boolean,
            default: false
        },
        // 是否加载中
        loading: {
            type: Boolean,
            default: false
        },
        // 是否自定义基础信息
        isCustomFoundation: {
          type: Boolean,
          default: false
        },
        // 自定义基础信息模块
        customFoundationModule: {
          type: Array,
          default: () => []
        },
        // 自定义 Foundation
        customFoundationData: {
          type: Object,
          default: () => ({})
        }
    },
    data () {
        return {
            unitOverviewRules: this.generateFixedRules(),
            coverImages: [],
            copyUnitOverview: null, // 初始的拷贝
            unitOverviewInit: false, // 是否初始化
            lightAdapt: false, // 轻量改编特殊处理
            oldUnitName: this.unitOverview.title || '' // 单元名字
        }
    },
    destroyed () {
      if (this.unitId && this.copyUnitOverview !== JSON.stringify(this.unitOverview)) {
        this.$store.commit('curriculum/SET_UNIT_HAS_UPDATED', true)
      }
    },
    created () { 
      // 轻量改编特殊处理
      this.initLightAdapt()
    },
    watch: {
      // 监听单元概览，设置封面信息
      unitOverview: {
        immediate: true,
        handler (val) {
          this.coverImages = val.coverMedias
          if (this.loading) {
            // 滚动到正在生成的单元概览项
            const activeElement = document.activeElement
            // 将滚动条滚动到当前活跃的元素位置
            activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
          }
          if (!this.unitOverviewInit) {
            this.copyUnitOverview = JSON.stringify(val)
            this.unitOverviewInit = true
          }
        }
      },
      // 监听 customFoundationModule 变化，动态生成 rules
      customFoundationModule: {
        immediate: true,
        handler (newModules) {
          // 如果自定义模块是空，还原固定模块校验逻辑
          if (!newModules) {
            // 还原固定模块校验逻辑
            this.unitOverviewRules = this.generateFixedRules()
            return
          }
          // 自定义模块校验逻辑
          this.generateDynamicRules(newModules)
        }
      }
    },
    computed: {
      ...mapState({
        unitId: state => state.curriculum.unit.id, // 单元 ID
      }),

      // 当前表单的 model
      currentModel () {
        // 根据条件动态返回绑定的对象
        return this.isCustomFoundation ? this.customFoundationData : this.unitOverview
      }
    },
    methods: {
        /**
       * 处理单元名字失去焦点事
       */
        unitNameBlur () {
          // 若当前单元名字为空，则设置为之前的单元名字
          if (!this.unitOverview.title || this.unitOverview.title.trim() === '') {
            this.unitOverview.title = this.oldUnitName
            return
          }
          // 若当前单元名字与之前的单元名字去除前后空格不一致，则更新单元名字
          if (this.unitOverview.title.trim() !== this.oldUnitName.trim()) {
            this.oldUnitName = this.unitOverview.title
            // 调用父类的 updateUnitOverview 方法更新单元名字
            this.$emit('updateUnitOverview', this.unitOverview)
          } else {
            this.unitOverview.title = this.oldUnitName
          }
        },
        // 轻量改编特殊处理
        initLightAdapt () {
           // 获取当前路由名称
           if (this.$route) {
            const routeName = this.$route.name
            if (equalsIgnoreCase(routeName, 'unit-overview-cg-adapt') && this.unitInfo && equalsIgnoreCase(this.unitInfo.adaptedType, 'LIGHT')) {
              this.lightAdapt = true
            }
          }
        },
        // 获取固定模块校验规则
        generateFixedRules () {
          return {
            overview: [{ required: true, message: 'Please input overview', trigger: 'blur' }],
            trajectory: [{ required: true, message: 'Please input unit trajectory', trigger: 'blur' }],
            concepts: [{ required: true, message: 'Please input concepts', trigger: 'blur' }],
            guidingQuestions: [{ required: true, message: 'Please input guiding questions', trigger: 'blur' }]
          }
        },

        // 获取重新生成的图标
        getRegenerateButtonIcon() {
            // 如果生成过展示重新生成图标
            return regenerateIcon
        },
        // 重新生成
        regenerate () {
          this.$emit('regenerate', true)
        },

        // 校验单元信息表单项
        validateUnitForm (field) {
          if (this.loading) {
            return
          }
          this.$refs.unitOverviewFormRef.validateField(field)
        },

        // 生成单元封面
        generateUnitCover () {
          this.$emit('generateCover', false)
        },

        // 设置单元封面
        setCoverInfo (cover) {
          this.$emit('setCoverInfo', cover)
        },

        // 更新单元封面
        updateUnitCover (cover) {
          if (!cover) {
            this.coverImages = []
            return
          }
          this.$store.commit('curriculum/SET_UNIT_COVER', [cover])
          this.coverImages = [cover]
        },

        // 封面更新事件
        updateImagesEvent (files) {
          if (!files || files.length === 0) {
            this.$set(this.unitOverview, 'coverMedias', [])
          } else {
            files[0].type = 'UPLOAD'
            this.$set(this.unitOverview, 'coverMedias', files)
          }
        },
        // 单元信息表单校验
        checkForm () {
          return new Promise((resolve, reject) => {
            this.$refs.unitOverviewFormRef.validate((valid) => {
            if (valid) {
                resolve(true)
            } else {
              // 滚动到校验失败的表单项
              setTimeout(() => {
                let formItemsWithError = this.$el.querySelectorAll('.is-error')
                let firstItem = formItemsWithError[0]
                if (firstItem) {
                  firstItem.scrollIntoView(true)
                }
              }, 0)
              resolve(false)
            }
          })
        })
      },
      // 动态生成校验规则
      generateDynamicRules (modules) {
        if (!this.isCustomFoundation) {
          // 固定模块时，还原规则
          this.unitOverviewRules = this.generateFixedRules()
          return
        }
        // 自定义模块规则
        this.unitOverviewRules = modules.reduce((acc, module) => {
          acc[module.key] = [
            { required: true, message: `Please input ${module.name}`, trigger: 'blur' }
          ]
          return acc
        }, {})
      }
    }
}
</script>

<style lang="less" scoped>

/deep/ .el-form--label-top .el-form-item__label{
    font-weight: 600;
    line-height: 24px;
    padding: 0;
}
// 封面
.cover-container {
  width: 370px;
}
.lg-icon-picture {
  font-size: 60px!important;
  height: 40px!important;
}
// 封面操作
/deep/ .cover-operation {
  position: absolute;
  left: 10px;
  top: 10px;
  z-index: 2;
  /deep/ button {
    opacity: 0.6;
  }
  .regenerate {
    width: 32px!important;
    height: 32px!important;
      display: flex;
      align-content: center;
      justify-content: center;
      align-items: center;
  }
}
.media-desc {
  margin-top: -15px;
}

.age-group-framework-info {
    display: flex;
    gap: 16px;
    width: 400px;
    height: 30px;
    margin-top: 20px;

    .read-only-info-item {
      border-radius: 90px;
      padding: 4px 16px;
      border: 1px solid #DDF2F3;
      background-color: #DDF2F3;
      font-weight: 600;
    }

    .text-no-wrap {
      white-space: nowrap;
    }

    .text-overflow-ellipsis {
      text-overflow: ellipsis;
      overflow: hidden;
    }

  }

.lesson-field-time {
  background-color: #F5F6F8;
  border-radius: 8px;
  display: flex;
  justify-content: space-around;
  height: 65px;

  .lesson-field-time-item {
    display: flex;
    flex-direction: column-reverse;
    padding: 20px 0px;
    gap: 0px;
    justify-content: center;
    align-items: center;
    vertical-align: middle;

    .lesson-field-value {
      font-size: 14px !important;
      color: #676879;
      height: 20px;
      line-height: 20px;
      font-weight: 400;
    }
    .lesson-field-label{
      font-size: 16px !important;
      color: #000000;
      height: 20px;
      line-height: 20px;
      font-weight: 600;
    }
  }

  & > :nth-child(3) {
    margin-left: 30px;
  }
}
.week-count-info {
  margin-top: 20px;
  width: 400px;
}
/deep/ .el-divider--vertical {
    height: 32px;
    color: #dcdfe6 !important;
    align-content: center;
    margin: 16px 8px
}

/deep/ .unit-name-input .el-input__inner {
  font-size: 20px !important;
}
</style>
