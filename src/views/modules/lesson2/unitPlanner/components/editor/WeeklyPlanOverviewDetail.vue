<template>
  <div>
    <el-card class="w-full m-b-sm" v-for="(weeklyPlan, index) in weeklyPlans" :key="index">
      <!-- 周 -->
      <div>
        <div class="display-flex justify-content-between lg-margin-bottom-16">
          <!-- 第几周主题 -->
          <div class="section-title font-size-16 font-bold week-theme display-flex align-items" style="gap: 8px">
            {{ $t('loc.unitPlannerStep2WeekX', { week: weeklyPlan.week }) }}
          </div>

          <!--居右-->
          <div class="display-flex justify-content-between  " style="gap: 16px">
            <div v-show="editable && !loading && !weeklyPlan.generateLoading && !isOldUnitData"
                 class="display-flex justify-content-between " style="gap: 16px;z-index:0">
              <div class="display-flex align-items">
                <span class="font-size-14" style="color: #111C1C;">{{$t('loc.weekLength')}}:</span>
              </div>
              <div class="input-number-with-suffix">
                <el-input-number v-model="weeklyPlan.activityCount"
                               class="input-number-class"
                               :step="1"
                               :min="1"
                               :max="getWeekActiveMax(weeklyPlan.week)"
                               placeholder="5"
                               style="text-align: left;"
                               :disabled="disableUpdate || (weeklyPlan.items && weeklyPlan.items.length > 0) ||
                                           loading || weeklyPlan.generateLoading || weeklyPlan.regenerateLoading"
                               :precision="0"/>
                <div class="input-suffix-text" :class="{'mobile-suffix-text': disableUpdate || (weeklyPlan.items && weeklyPlan.items.length > 0) ||
                                           loading || weeklyPlan.generateLoading || weeklyPlan.regenerateLoading}">{{ weeklyPlan.activityCount == 1 ? $t('loc.lessonTemp') : $t('loc.lessonsTemp') }}</div>
              </div>
            </div>
            <!-- 操作 -->
            <div v-show="editable && !loading && !weeklyPlan.generateLoading">
              <el-tooltip effect="dark" content="Regenerate" placement="top">
                <!-- 重新生成 -->
                <el-button type="primary" plain style="height: 40px;width: 40px;padding: 0 9px"
                           :loading="weeklyPlan.regenerateLoading || saveAndGenerateLoading"
                           @click="regenerate(weeklyPlan)">
                  <template #icon>
                    <i v-if="!weeklyPlan.regenerateLoading && !saveAndGenerateLoading"
                       class="el-icon-refresh-right font-bold"></i>
                  </template>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>

        <el-form ref="form" :model="weeklyPlan" :class="{'danger-border': weeklyPlan.showDangerBorder}">
          <el-form-item class="custom-item" prop="theme">
            <div slot="label">
              <span class="lg-color-danger">*&nbsp;</span>
              <span>{{ $t('loc.unitPlannerStep2Theme') }}</span>
            </div>
            <el-skeleton :rows="1" animated
                         :loading="(loading && !weeklyPlan.theme) || !!weeklyPlan.generateLoading">
              <el-input ref="weeklyTheme" v-model="weeklyPlan.theme" :placeholder="$t('loc.unitPlannerStep2ThemePlaceholder')"
                        @blur="validWeeklyThemeAndOverview(weeklyPlan, index)" maxlength="500"/>
            </el-skeleton>
          </el-form-item>

          <el-form-item class="overview-item"
                        :class="{ 'overview-danger-border': (weeklyPlan.noData && !loading)}"
                        prop="overview">
            <div slot="label">
              <span class="lg-color-danger">*&nbsp;</span>
              <span>{{ $t('loc.unitPlannerStep2Overview') }}</span>
            </div>
            <el-skeleton :rows="2" animated
                         :loading="(loading && !weeklyPlan.theme) || !!weeklyPlan.generateLoading">
              <el-input ref="weeklyOverview" v-model="weeklyPlan.overview" type="textarea" :placeholder="$t('loc.unitPlannerStep2OverviewPlaceholder')"
                        :autosize="{ minRows: 4, maxRows: 4}" maxlength="2000"
                        @blur="validWeeklyThemeAndOverview(weeklyPlan, index)"/>
            </el-skeleton>
          </el-form-item>

          <el-form-item :label="$t('loc.unitPlannerStep2Books')" class="book-item"
                        :class="{'border-bottom-radius': isOldUnitData}"
                        prop="books" v-if="isShowBooks(weeklyPlan)">
            <el-skeleton :rows="1" animated
                         :loading="(loading && !weeklyPlan.books) || !!weeklyPlan.generateLoading">
              <el-input ref="weeklyOverview" v-model="weeklyPlan.books" :placeholder="$t('loc.unitPlannerStep2BooksPlaceholder')"
                        :autosize="{ minRows: 2, maxRows: 2}" maxlength="2000"/>
            </el-skeleton>
          </el-form-item>

          <div v-if="!isOldUnitData">
            <el-form-item class="overview-standard-item"
                          :class="{ 'overview-danger-border': (weeklyPlan.noData && !loading), 'border-bottom-radius': !rubricsOptions || rubricsOptions.length == 0}"
                          prop="standards">
              <div slot="label">
                <span class="lg-color-danger">*&nbsp;</span>
                <span>{{ $t('loc.unitPlannerMeasuresStandards2') }}</span>
              </div>
              <el-skeleton :rows="1" animated
                           :loading="(loading && !weeklyPlan.theme) || !!weeklyPlan.generateLoading">
                <MeasureRow
                  ref="measureRow"
                  :domains="domainsTreeData"
                  :selectedMeasures=weeklyPlan.standards
                  popoverPlacement="bottom-start"
                  :disabled="disableUpdate"
                  :unitBaseInfo="baseInfo"
                  :standardsViewScrollbarHeight="100"
                  :needFilterMeasures="true"
                  :useDomain=baseInfo.useDomain
                  @updateSelectedMeasure="(selectedMeasureIds, selectDomainIds) => updateSelectedMeasure(selectedMeasureIds, selectDomainIds, index)">
                </MeasureRow>
              </el-skeleton>
            </el-form-item>

            <!-- 校训选择组件 -->
            <el-form-item v-if="rubricsOptions && rubricsOptions.length > 0" class="portrait-graduate-item" prop="rubrics">
              <div slot="label">
                <span>{{ $t('loc.unitPlannerStep2Rubrics') }}</span>
              </div>
              <el-skeleton :rows="2" animated
                           :loading="(loading && !weeklyPlan.theme) || !!weeklyPlan.generateLoading">
                <RubricsRow
                  :rubricsOptions="rubricsOptions"
                  :selectedRubrics="weeklyPlan.rubrics"
                  :disabled="disableUpdate"
                  @updateSelectedRubrics="(selectedRubrics) => updateSelectedRubrics(selectedRubrics, index)"
                />
              </el-skeleton>
            </el-form-item>

          </div>
        </el-form>
        <!-- tip -->
        <span v-if="weeklyPlan.showDangerBorder && !loading" class="add-margin-t-10 display-inline-block"
              style="color: var(--color-danger)">This field is required</span>
      </div>
    </el-card>
  </div>
</template>

<script>
import {mapState} from "vuex";
import MeasureRow from '@/views/curriculum/designer/components/MeasureRow'
import RubricsRow from '@/views/modules/lesson2/unitPlanner/components/learnerProfile/RubricsRow'
import { equalsIgnoreCase } from '@/utils/common'

export default {
    components: {
      MeasureRow,
      RubricsRow
    },
    props: {
        // 周计划
        weeklyPlans: {
            type: Array,
            default: () => []
        },
        // 周计划
        domainsTreeData: {
          type: Array,
          default: () => []
        },
        // 所有底层测评点
        allMeasures: {
          type: Array,
          default: () => []
        },
        // 是否可编辑
        editable: {
            type: Boolean,
            default: false
        },
        // 是否 Loading
        loading: {
            type: Boolean,
            default: false
        },
        // 保存或生成中的 Loading
        saveAndGenerateLoading: {
          type: Boolean,
          default: false
        },
          // 当前单元信息
        baseInfo: {
          type: Object,
          default: () => {
          }
        },
        // 校训选项
        rubricsOptions: {
          type: Array,
          default: () => []
        }
    },
  watch: {
        weeklyPlans: {
          handler (val, val2) {
            // 滚动到正在生成的周计划概览
            if (this.loading) {
              this.calculateDifferentIndex(val, val2)
              let themeIndex = this.calculateDifferentIndex(val, val2)
              if (themeIndex < 0) {
                return
              }
              this.$refs.weeklyTheme && this.$refs.weeklyTheme[themeIndex] && this.$refs.weeklyTheme[themeIndex].$el.scrollIntoView({ behavior: 'smooth', block: 'center' })
            }
            if (!this.weekPlansInit) {
              this.copyWeekPlan = JSON.stringify(val)
              this.weekPlansInit = true
            }
          },
          deep: true,
          immediate: true
        },
    },
    data () {
      return {
        currentWeeklyPlan: {}, // 正在操作的周计划
        weekPlansInit: false, // 周计划是否初始加载
        copyWeekPlan: null // 初始的周计划拷贝
      }
    },
    destroyed () {
      if (this.unitId && this.copyWeekPlan !== JSON.stringify(this.weeklyPlans)) {
        this.$store.commit('curriculum/SET_UNIT_HAS_UPDATED', true)
      }
    },
    computed: {
        ...mapState({
          unitId: state => state.curriculum.unit.id,
          unit: state => state.curriculum.unit,
          isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
          isOldUnitData: state => state.curriculum.isOldUnitData, // 单元是否为旧数据
        }),
        // 是否可以编辑
        canEdit () {
            return function (weeklyPlan) {
                return this.editable && weeklyPlan.confirmed && !weeklyPlan.edit
            }
        },
        // 是否可以删除
        canDelete () {
            return function (weeklyPlan) {
                return this.editable && !weeklyPlan.confirmed && !weeklyPlan.deleted
            }
        },
        // 是否可以重新生成
        canRegenerate () {
            return function (weeklyPlan) {
                return this.editable && !weeklyPlan.confirmed
            }
        },
        // 是否可以确认
        canConfirm () {
            return function (weeklyPlan) {
                return this.editable && !weeklyPlan.confirmed && !weeklyPlan.deleted
            }
        },
        // 超过 40 进度测评点不能再更新修改
        disableUpdate(){
          return this.baseInfo.progress > 40
        }
    },
    methods: {
        // 是否显示书籍
        isShowBooks(weeklyPlan) {
          if (this.unit && this.unit.adaptedType && equalsIgnoreCase(this.unit.adaptedType, 'LIGHT')) {
            return weeklyPlan && weeklyPlan.books && !equalsIgnoreCase(weeklyPlan.books, 'None')
          } else {
            return true
          }
          },
        // 获取当前周最大活动数限制
        getWeekActiveMax(week) {
          // 如果是单元导入深度改编设置原有课程数
          if (equalsIgnoreCase(this.unit.adaptedType, 'DEEP') && this.unit.weekLessonCountMap && this.unit.weekLessonCountMap[week]) {
            return this.unit.weekLessonCountMap[week]
          }
          return this.isCurriculumPlugin ? 5 : 10
        },
        // 计算两个数组第一个不同的位置
        calculateDifferentIndex (arr1, arr2) {
          let index
          for (let i = 0; i < arr1.length; i++) {
            if (JSON.stringify(arr1[i]) !== JSON.stringify(arr2[i])) {
              index = i
              break
            }
          }
          return index
        },
        // 更新测评点数据
        updateSelectedMeasure (selectedMeasureIds, selectDomainIds, index) {
          if(this.disableUpdate) {
            return
          }
          const updateMeasure = this.allMeasures.filter(measures => selectedMeasureIds.includes(measures.id))
            .sort((a, b) => a.sortIndex - b.sortIndex)
          this.weeklyPlans[index].standards = updateMeasure
          this.weeklyPlans[index].measureIds = updateMeasure.map(measure => measure.id)
          this.$store.commit('curriculum/SET_WEEKLY_PLANS', this.weeklyPlans)
        },
        // 更新校训数据
        updateSelectedRubrics (selectedRubrics, index) {
          if (this.disableUpdate) {
            return
          }
          // 更新校训数据
          this.$set(this.weeklyPlans[index], 'rubrics', selectedRubrics);
          this.$store.commit('curriculum/SET_WEEKLY_PLANS', this.weeklyPlans)
        },
        // 删除测评点
        deleteDomain (valueToRemove, index) {
          this.weeklyPlans[index].standards = this.weeklyPlans[index].standards.filter(item => item !== valueToRemove)
        },
        // 确认
        confirm (weeklyPlan, editIndex) {
            this.$set(weeklyPlan, 'confirmed', true)
            this.$set(weeklyPlan, 'edit', false)
            this.$set(weeklyPlan, 'editIndex', editIndex)
            this.$store.commit('curriculum/UPDATE_WEEKLY_PLAN', weeklyPlan)
        },
        // 编辑
        edit (weeklyPlan, editIndex) {
            this.$set(weeklyPlan, 'edit', true)
            this.$set(weeklyPlan, 'confirmed', false)
            this.$set(weeklyPlan, 'editIndex', editIndex)
            this.$store.commit('curriculum/UPDATE_WEEKLY_PLAN', weeklyPlan)
        },
        // 删除
        deleteWeeklyPlan (weeklyPlan, editIndex) {
            this.$confirm('Are you sure to delete this weekly plan?', 'Warning', {
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel'
            }).then(() => {
                // 清空数据
                this.$set(weeklyPlan, 'theme', undefined)
                this.$set(weeklyPlan, 'overview', undefined)
                this.$set(weeklyPlan, 'deleted', true)
                this.$set(weeklyPlan, 'editIndex', editIndex)
                this.$store.commit('curriculum/UPDATE_WEEKLY_PLAN', weeklyPlan)
            }).catch(() => {})
        },
        // 重新生成
        regenerate (weeklyPlan) {
            this.$emit('regenerate', weeklyPlan)
        },
        // 校验当前操作的周次的周主题和概览是否填写
        validWeeklyThemeAndOverview (weeklyPlan, index) {
          // 保存当前操作的周计划
          this.edit(weeklyPlan, index)
          // 如果周主题和概览存在未填写的情况，则显示红色边框
          if (this.isOldUnitData){
            // 如果是旧数据不校验测评点数据
            if (weeklyPlan.theme === '' || weeklyPlan.overview === '') {
              this.$set(weeklyPlan, 'showDangerBorder', true)
            } else {
              this.$set(weeklyPlan, 'showDangerBorder', false)
            }
          }else {
            if (weeklyPlan.theme === '' || weeklyPlan.overview === '' || !weeklyPlan.standards || weeklyPlan.standards.length === 0) {
              this.$set(weeklyPlan, 'showDangerBorder', true)
            } else {
              this.$set(weeklyPlan, 'showDangerBorder', false)
            }
          }
        }
    }
}
</script>

<style lang="less" scoped>
.deleted-tip {
    height: 100px;
}
.week-theme::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 18px;
  background: var(--color-primary);
}
/deep/ .el-form-item {
  display: flex!important;
  align-items: center!important;
}
/deep/ .el-form-item__content {
  width: 100% !important;
}

.custom-item {
  border: 1px solid var(--color-border) !important;
  border-top-left-radius: 8px!important;
  border-top-right-radius: 8px!important;
  margin-bottom: 0px!important;
  /deep/ .el-form-item__label {
    height: 54px!important;
    border-top-left-radius: 8px;
    border-right: 1px solid var(--color-border)!important;
    background: var(--color-table-background) !important;
    width: 121px !important;
    padding: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  /deep/ .el-form-item__content {
    padding: 0 16px!important;
    .theme-title-border {
      /deep/ .el-input__inner {
        border: 1px dashed #C0C4CC!important;
        padding: 5px 8px!important;
      }
    }
    .theme-no-border {
      /deep/ .el-input__inner {
        border: transparent!important;
        padding: 5px 8px!important;
      }
    }
  }
}
.book-item {
  border-left: 1px solid var(--color-border) !important;
  border-right: 1px solid var(--color-border) !important;
  border-bottom: 1px solid var(--color-border) !important;
  margin-bottom: 0px!important;
  /deep/ .el-form-item__label {
    height: 54px!important;
    border-right: 1px solid var(--color-border)!important;
    background: var(--color-table-background) !important;
    width: 121px !important;
    padding: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  /deep/ .el-form-item__content {
    padding: 0 16px!important;
    .theme-title-border {
      /deep/ .el-input__inner {
        border: 1px dashed #C0C4CC!important;
        padding: 5px 8px!important;
      }
    }
    .theme-no-border {
      /deep/ .el-input__inner {
        border: transparent!important;
        padding: 5px 8px!important;
      }
    }
  }
}
.book-item.border-bottom-radius{
  /deep/ .el-form-item__label {
    border-bottom-left-radius: 8px;
  }
}
.danger-border {
  border: 1px solid var(--color-danger) !important;
  border-radius: 8px !important;
}
.overview-item {
  border: 1px solid var(--color-border) !important;
  border-top: transparent!important;
  margin-bottom: 0px!important;
  /deep/ .el-form-item__label {
    width: 121px!important;
    height: 120px!important;
    border-right: 1px solid var(--color-border)!important;
    background: var(--color-table-background) !important;
    white-space: normal;
    width: 121px !important;
    display: -webkit-box;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 70px;
    padding: 0px;
  }
  /deep/ .el-form-item__content {
    padding: 0 16px!important;
  }
}
.border-bottom-radius{
  border-bottom-left-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
  /deep/ .el-form-item__label {
    border-bottom-left-radius: 8px !important;
  }
}
.overview-standard-item {
  border-left: 1px solid var(--color-border) !important;
  border-right: 1px solid var(--color-border) !important;
  border-bottom: 1px solid var(--color-border) !important;
  margin-bottom: 0px!important;
  height: 100% !important;
  /deep/ .el-form-item__label {
    border-bottom-left-radius: 0;
    width: 121px!important;
    height: 54px!important;
    border-right: 1px solid var(--color-border)!important;
    background: var(--color-table-background) !important;
    white-space: normal;
    display: -webkit-box;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 0;
  }
  /deep/ .el-form-item__content{
    padding: 0 16px!important;
    line-height: 14px;
    .measure {
      min-height: auto!important;
      padding: 8px!important;
    }
  }
}
.portrait-graduate-item {
  border-left: 1px solid var(--color-border) !important;
  border-right: 1px solid var(--color-border) !important;
  border-bottom: 1px solid var(--color-border) !important;
  border-bottom-left-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
  height: 100% !important;
  /deep/ .el-form-item__label {
    border-bottom-left-radius: 8px;
    width: 121px!important;
    height: 54px!important;
    border-right: 1px solid var(--color-border)!important;
    background: var(--color-table-background) !important;
    white-space: normal;
    display: -webkit-box;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 0;
  }
  /deep/ .el-form-item__content {
    line-height: 14px;
    padding: 0 16px!important;
  }
}
.standard-item-show{
  display: inline-block;
  background-color: #F5F6F8;
  margin-right: 6px;
  margin-top: 6px;
  padding: 0px 10px;
  height: 26px;
  line-height: 26px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  span{
    font-weight: 400;
    font-size: 14px;
  }
}
.overview-danger-border {
  border: 1px solid var(--color-danger) !important;
  border-top: transparent!important;
}

/deep/ .el-input__inner {
  border: 1px dashed transparent;
  border-radius: 4px;
  padding: 8px;
}

.input-number-class {
  /deep/ .el-input__inner {
    border-width: 1px;
    border-style: solid !important;
    border-color: #DCDFE6;
    border-radius: 4px;
    padding: 8px;
    padding-right: 70px !important;
    z-index: 99;
  }

  /deep/ .el-input__inner:hover {
    border-color: #10b3b7 !important; /* 鼠标悬停时的边框颜色 */
  }

  /deep/ .el-input__inner:focus {
    border-color: #10b3b7 !important; /* 聚焦时的边框颜色 */
  }
}

/deep/ .el-input__inner:focus,
/deep/ .el-input__inner:hover {
  border: 1px dashed var(--color-info);
  border-radius: 4px;
  padding: 8px;
}
/deep/ .el-textarea__inner {
  border: 1px dashed transparent;
  border-radius: 4px;
  padding: 8px;
}

/deep/ .el-textarea__inner:focus,
/deep/ .el-textarea__inner:hover {
  border: 1px dashed var(--color-info);
  border-radius: 4px;
  padding: 8px;
}
/deep/ .el-card.is-always-shadow {
  box-shadow: unset!important;
}

/* 校训选择组件样式 */
.portrait-select-container {
  padding: 16px;

  .portrait-search-input {
    margin-bottom: 16px;

    /deep/ .el-input__inner {
      border: 1px solid var(--color-border);
      &:hover, &:focus {
        border-color: var(--color-primary);
      }
    }
  }

  .portrait-options-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 8px;

    .portrait-option-item {
      display: block;
      margin-bottom: 12px;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background-color: var(--color-table-background);
      }

      .portrait-option-content {
        margin-left: 24px;

        .portrait-name {
          font-weight: 600;
          margin-bottom: 4px;
          color: var(--color-text-primary);
        }

        .portrait-description {
          font-size: 12px;
          color: var(--color-text-secondary);
          line-height: 1.4;
        }
      }

      /deep/ .el-checkbox__input {
        vertical-align: top;
        margin-top: 4px;
      }
    }
  }

  .portrait-limit-tip {
    font-size: 12px;
    color: var(--color-info);
    margin-top: 8px;
    text-align: right;
  }
}

/* 自定义滚动条样式 */
.portrait-options-list {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--color-border);
    border-radius: 3px;

    &:hover {
      background-color: var(--color-text-secondary);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.input-number-with-suffix {
  display: flex;
  align-items: center;
  max-width: 200px;
  position: relative;
  .suffix-text {
    margin-left: 5px;
    font-weight: 600;
    color: #111C1C;
  }
  :deep(.el-input-number) {
    .el-input__inner {
      text-align: left;
    }
  }
}

.input-suffix-text {
    position: absolute;
    right: 57px; /* 为右侧的加减按钮留出空间 */
    top: 51%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #111C1C;
    font-weight: 400;
    white-space: nowrap;
    pointer-events: none; /* 防止文本阻挡输入框交互 */
    z-index: 10;
  }

  .mobile-suffix-text {
    color: #32333866;
  }
</style>
