<template>
  <el-dialog
    title="Edit Resources"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :append-to-body="true"
    class="resource-edit-modal edit-resources-dialog"
    custom-class="edit-resources-custom-dialog"
  >
    <div class="remove-padding">
      <!-- 操作按钮 -->
      <div class="resource-actions">
        <el-button type="primary" icon="el-icon-plus" @click="addResource">{{ $t('loc.addResourceButton') }}</el-button>
        <el-button icon="el-icon-time" @click="showHistory">{{ $t('loc.resourceHistory') }}</el-button>
      </div>


      <div v-if="localResources.length === 0" class="no-version-selected">
                <img src="@/assets/img/lesson2/plan/no_record.png">
                <!-- <div class="">{{$t('loc.plan36')}}</div> -->
                 <div>No data found. Use the Add button (top left) to add a resource.</div>
              </div>
      <div v-else>
        <!-- 资源列表 -->
        <div class="resources-list-header">
          <h3>{{ $t('loc.currentResources') }}</h3>
        </div>
        <div class="resources-list">
          <div
            v-for="(source, index) in localResources"
            :key="index"
            class="resource-item"
            :class="{'hidden': source.hidden}"
          >
            <div class="resource-content">
              <div class="resource-title">
                <div class="resource-index">[{{source.subscript}}].</div>
                <div>
                  {{source.sourceKeywords || 'Custom Resource'}}
                  <span v-if="source.source">({{source.source}})</span>
                </div>
              </div>

              <!-- 根据资源类型显示不同内容 -->
              <div v-if="equalsIgnoreCase(source.type, 'File')" class="resource-file">
                <div class="file-info">
                  <div class="file-type-icon">
                    <img :src="getFileIconClass(source.fileName || '')" alt="File Icon">
                  </div>
                  <div class="file-details file-name">
                      {{ source.fileName }}
                  </div>

                  <!-- 下载按钮 -->
                  <!-- <div class="download-icon" @click="downloadFile(source)">
                      <i class="el-icon-download"></i>
                  </div> -->
                </div>
              </div>

              <div v-else class="resource-link">
                <a :href="source.sourceLink" target="_blank">{{source.sourceLink}}</a>
              </div>

              <curriculum-media-viewer v-if="source.videoLink" :url="source.videoLink"
                                      :coverMediaImg="source.cover" :preview="true"
                                      class="media-resource"/>
            </div>
            <div class="resource-actions">
              <el-button v-if="equalsIgnoreCase(source.type, 'File')" class="download-icon black-icon-btn" type="text" size="medium" icon="el-icon-download" @click="downloadFile(source)"></el-button>
              <el-button type="text" size="medium" icon="el-icon-edit" @click="editResource(index)" class="black-icon-btn"></el-button>
              <el-button type="text" size="medium" icon="el-icon-delete" @click="deleteResource(index)" class="black-icon-btn"></el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多按钮 -->
      <div v-if="hasMoreResources" class="load-more">
        <el-button type="text" @click="loadMore">
          <span v-if="!showAll">{{ $t('loc.viewMoreResources') }} <i class="el-icon-arrow-down"></i></span>
          <span v-else>{{ $t('loc.hideResources') }} <i class="el-icon-arrow-up"></i></span>
        </el-button>
      </div>

      <!-- 额外资源列表 -->
      <div v-if="hasMoreResources && showAll" class="additional-resources">
        <div v-for="(item, index) in additionalResources" :key="'add-'+index" class="additional-resource-item">
          <div class="additional-resource-info">
            <i class="el-icon-document"></i>
            <div class="additional-resource-text">
              <div>{{item.sourceKeywords || 'Custom Resource'}}</div>
              <div class="additional-resource-link">{{item.sourceLink}}</div>
            </div>
          </div>
          <el-button type="primary" icon="el-icon-plus" size="small" class="add-resource-btn" @click="addAdditionalResource(item)">{{ $t('loc.resourceAdded') }}</el-button>
        </div>
      </div>
    </div>

    <!-- 添加/编辑资源的弹窗 -->
    <resource-add-modal
      v-if="resourceModalVisible"
      :value="resourceModalVisible"
      :current-resource-count="currentFileNum"
      :is-edit-mode="isEditMode"
      :resource="isEditMode ? editingResource : null"
      @add-resources="handleAddResources"
      @update-resource="handleUpdateResource"
      @cancel="resourceModalVisible = false"
    />

    <!-- 历史记录弹窗 -->
    <el-dialog
      width="900px"
      :title="$t('loc.resourceHistoryVersion')"
      :visible.sync="historyVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      custom-class="resources-history-dialog"
    >
      <!-- 主体内容区域 -->
      <div class="history-container">
        <!-- 左侧版本列表区 -->
        <div class="version-history-panel">

          <!-- 版本列表骨架屏 -->
          <el-skeleton :loading="historyLoading" animated>
            <template slot="template">
              <div class="version-list-skeleton">
                <div style="padding-bottom: 18px;">
                  <el-skeleton-item variant="p" style="width: 40%" />
                  <el-skeleton-item variant="p" style="margin-left: 20px; width: 30%" />
                  <el-skeleton-item variant="text" style="width: 80%" />
                </div>
                <div v-for="i in 4" :key="i" style="padding-bottom: 18px;">
                  <el-skeleton-item variant="p" style="width: 40%" />
                  <el-skeleton-item variant="text" style="width: 80%" />
                </div>
              </div>
            </template>
            <template>
              <!-- 实际版本列表 -->
              <div class="version-list">
                <div
                  v-for="(version, vIndex) in displayVersions"
                  :key="vIndex"
                  class="version-item"
                  :class="{'current-version': version.isCurrent, 'active': selectedVersionIndex === getOriginalIndex(vIndex)}"
                  @click="selectVersion(getOriginalIndex(vIndex))"
                >
                  <div class="version-number">
                    Version {{ displayVersions.length - vIndex }}
                    <span v-if="version.isCurrent" class="current-tag">Current version</span>
                  </div>
                  <div class="version-date">{{ version.formattedDate }}</div>
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>

        <!-- 右侧资源列表区 -->
        <div class="version-resources-panel">
          <!-- 资源列表骨架屏 -->
          <el-skeleton :loading="historyLoading" animated :rows="5" style="padding: 20px;">
            <template slot="template">
              <div v-for="i in 3" :key="i" style="padding-bottom: 20px;">
                <el-skeleton-item variant="h3" style="width: 50%; margin-bottom: 10px;" />
                <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 5px;" />
                <el-skeleton-item variant="text" style="width: 60%;" />
              </div>
            </template>
            <template>
              <!-- 实际资源列表 -->
              <div v-if="selectedVersionIndex !== -1 && resourceVersions[selectedVersionIndex].resources && resourceVersions[selectedVersionIndex].resources.length > 0">
                <div v-for="(resource, rIndex) in resourceVersions[selectedVersionIndex].resources"  :class="{'hidden': resource.hidden}" :key="rIndex" class="version-resource-item">
                  <div class="resource-item-header">
                    <div class="resource-index">[{{ resource.subscript }}].</div>
                    <div class="resource-title">{{ resource.sourceKeywords || 'Custom Resource' }}</div>
                    <div v-if="resource.source" class="resource-source">({{ resource.source }})</div>
                  </div>

                  <!-- 根据资源类型显示不同内容 -->
                  <div v-if="equalsIgnoreCase(resource.type, 'File')" class="resource-file">
                    <div class="file-info">
                      <div class="file-type-icon">
                        <img :src="getFileIconClass(resource.fileName || '')" alt="File Icon">
                      </div>
                      <div class="file-details file-name">
                        {{ resource.fileName }}

                      </div>
                      <!-- 下载按钮 -->
                      <el-tooltip content="Download" placement="top" effect="dark">
                        <div class="download-icon" @click="downloadFile(resource)">
                            <i class="el-icon-download"></i>
                        </div>
                      </el-tooltip>
                    </div>
                  </div>

                  <div v-else class="resource-link">
                    <a :href="resource.sourceLink" target="_blank">{{resource.sourceLink}}</a>
                  </div>
                  <curriculum-media-viewer v-if="resource.videoLink" :url="resource.videoLink"
                                      :coverMediaImg="resource.cover" :preview="true"
                                      class="media-resource"/>
                  <!-- 如果是视频资源 -->
                  <!-- <div v-if="resource.videoLink" class="resource-video">
                    <div class="video-preview">
                      <img :src="resource.cover || 'https://via.placeholder.com/160x120'" alt="Video thumbnail">
                      <div class="play-icon">
                        <i class="el-icon-video-play"></i>
                      </div>
                    </div>
                  </div> -->

                  <!-- 如果是文件资源 -->
                  <div v-if="resource.isFile" class="resource-file-info">
                    <img v-if="resource.icon" :src="resource.icon" alt="File Icon" class="file-type-icon">
                    <span class="file-name">{{ resource.fileName }}</span>
                  </div>
                </div>
              </div>
              <div v-else class="no-version-selected" style="text-align: center;  color: #909399;padding-top: 30px;">
                <img src="@/assets/img/lesson2/plan/no_record.png">
                <!-- <div class="">{{$t('loc.plan36')}}</div> -->
                 <div>No data found. Use the Add button (top left) to add a resource.</div>
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="historyVisible = false" class="cancel-btn">Close</el-button>
        <el-button
          type="primary"
          @click="restoreVersion"
          :disabled="selectedVersionIndex === 0 || selectedVersionIndex === -1 || historyLoading"
          class="restore-btn"
        >
          {{ $t('loc.restoreResource') }}
        </el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import  FileUtils from '@/utils/file'
import { equalsIgnoreCase } from '@/utils/common'
import CurriculumMediaViewer from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMediaViewer.vue'
import ResourceAddModal from './ResourceAddModal.vue'

export default {
  name: 'ResourceEditModal',
  components: {
    ResourceAddModal,CurriculumMediaViewer
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    resources: {
      type: Array,
      default: () => []
    },
    moreResources: {
      type: Array,
      default: () => []
    },
    lessonId: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      resourceModalVisible: false,
      historyVisible: false,
      historyLoading: true, // 历史加载状态
      editingIndex: -1,
      editingResource: null, // 当前正在编辑的资源
      resourceForm: {
        sourceKeywords: '',
        sourceLink: ''
      },
      resourceHistory: [],
      showAll: false,
      localResources: [], // 本地资源数据，用于临时存储修改
      localCLRContent: '', // 本地内容数据，用于存储 CLR 记录回滚
      additionalResources: [], // 初始化为空数组
      rules: {
        sourceLink: [
          { required: true, message: 'Please enter resource URL', trigger: 'blur' },
          { validator: this.validateUrl, trigger: 'blur' }
        ],
        sourceKeywords: [
          { max: 200, message: this.$t('loc.titleCannotExceed200Characters'), trigger: 'blur' }
        ]
      },
      resourceVersions: [],
      selectedVersionIndex: -1,
      currentFileNum: 0, // 文件类型资源的数量
      isEditMode: false,
      subscript: 1,
      startSubscript: 1,
      isRestore: false,
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val) {
        // 打开弹窗时，复制资源数据到本地
        this.localResources = JSON.parse(JSON.stringify(this.resources || []))
        // 计算当前文件数量
        this.calculateCurrentFileNum()
        this.isRestore = false;

        // 处理额外资源数据，使用与 resources 相同的处理方式
        this.additionalResources = JSON.parse(JSON.stringify(this.moreResources || []))
              // 计算新资源的起始角标：找到当前资源列表中最大的角标，然后加1

        if (this.localResources.length > 0) {
          // 找出当前资源中最大的角标值
          const maxSubscript = Math.max(
            ...this.localResources.map(source => parseInt(source.subscript) || 0)
          );
          this.startSubscript = maxSubscript;
          this.subscript = this.startSubscript;
        }
      }
    },
    visible(val) {
      if (!val) {
        // 比较本地资源和原始资源是否有变化
        if (this.hasResourcesChanged()) {
          // 移除 additionalResources 中与 localResources 重复的资源
          this.removeAdditionalDuplicates();
          // 过滤掉隐藏的资源
          console.log('this.localResources', this.isRestore)
          this.localResources = this.localResources.filter(item => item.hidden !== true)
          if (equalsIgnoreCase(this.type, 'CLR') && this.localCLRContent) {
            this.$emit('save-resources', this.localResources, this.additionalResources, this.localCLRContent, this.isRestore);
          } else {
            this.$emit('save-resources', this.localResources, this.additionalResources, this.isRestore);
          }
        }
        this.$emit('close')
      }
    }
  },
  methods: {
    equalsIgnoreCase,
    // 比较本地资源和原始资源是否有变化
    hasResourcesChanged() {
      // 首先比较资源数量是否相同
      if (this.localResources.length !== this.resources.length ||
          this.additionalResources.length !== this.moreResources.length) {
        return true;
      }
      // 比较每个资源的关键属性
      for (let i = 0; i < this.localResources.length; i++) {
        const localResource = this.localResources[i];
        const originalResource = this.resources[i];

        // 比较资源链接和标题
        if (localResource.sourceLink !== originalResource.sourceLink ||
            localResource.sourceKeywords !== originalResource.sourceKeywords ||
            localResource.hidden !== originalResource.hidden) {
          return true;
        }
      }

      // 比较额外资源
      for (let i = 0; i < this.additionalResources.length; i++) {
        const localResource = this.additionalResources[i];
        const originalResource = this.moreResources[i];

        // 比较资源链接和标题
        if (localResource.sourceLink !== originalResource.sourceLink ||
            localResource.sourceKeywords !== originalResource.sourceKeywords) {
          return true;
        }
      }

      // 所有比较都通过，资源没有变化
      return false;
    },
    calculateCurrentFileNum() {
      this.currentFileNum = this.localResources.filter(item =>
        equalsIgnoreCase(item.type, 'File')
      ).length;
    },
    handleClose() {
      this.visible = false
    },
    addResource() {
      // 先确保之前的弹窗关闭并销毁
      this.resourceModalVisible = false
      this.$nextTick(() => {
        this.isEditMode = false
        this.editingResource = null
        this.resourceModalVisible = true
      })
    },
    editResource(index) {
      this.isEditMode = true
      this.editingIndex = index
      const resource = this.localResources[index]
      // 保存要编辑的资源
      this.editingResource = JSON.parse(JSON.stringify(resource))
      this.resourceModalVisible = true
    },
    deleteResource(index) {
      // 使用 Element UI 的 MessageBox 服务
      this.$msgbox({
        title: 'Confirmation',
        message: this.$t('loc.resourceDeleteConfirm'),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        cancelButtonClass: 'cancel-confirm-btn',
        distinguishCancelAndClose: true,
        customClass: 'delete-confirm-msgbox',
        confirmButtonClass: 'el-button--danger',
        beforeClose: (action, instance, done) => {
          if (equalsIgnoreCase(action, 'confirm')) {
            // 执行删除操作
            const updatedResources = [...this.localResources];

            // 检查是否是文件类型，如果是则减少 currentFileNum
            if (equalsIgnoreCase(updatedResources[index].type, 'File')) {
              this.currentFileNum--;
            }
            if(equalsIgnoreCase(updatedResources[index].subscript, this.subscript) && this.subscript > this.startSubscript) {
              this.subscript--;
            }

            updatedResources.splice(index, 1);

          // 删除后，重新计算 subscript 值
          if (updatedResources.length > 0) {
            // 找出剩余资源中的最大 subscript 值
            const maxSubscript = Math.max(
              ...updatedResources.map(resource => parseInt(resource.subscript) || 0)
            );

            // 如果当前 subscript 大于最大值+1，则调整为最大值+1
            if (this.subscript > maxSubscript && this.subscript > this.startSubscript) {
              this.subscript = Math.max(maxSubscript, this.startSubscript);
            }
          } else {
            // 如果没有剩余资源，则重置为起始值
            this.subscript = this.startSubscript;
          }
            // 更新本地资源
            this.localResources = updatedResources;

            this.$message({
              type: 'success',
              message: 'Deleted successfully!',
              customClass: 'top-message'
            });
          }
          done();
        }
      }).catch(() => {
        // 捕获取消操作，不做任何处理
      });
    },
    // 手动设置加载状态，用于重新加载数据
    setLoading() {
      this.historyLoading = true;
      // 模拟加载过程
      setTimeout(() => {
        this.historyLoading = false;
      }, 1500);
    },
    // 格式化日期时间的通用方法
    formatDateTime(date) {
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours();
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const ampm = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = hours % 12 || 0;
      // 当前版本使用不同的日期格式
       return `${month}/${day}/${year} ${formattedHours}:${minutes} ${ampm.toLowerCase()}`;

    },
    showHistory() {
      // 加载历史版本数据，从后端API获取
      this.selectedVersionIndex = -1; // 重置选中版本

      // 判断是否有必要的 ID
      if (!this.lessonId) {
        return;
      }

      // 构建请求参数
      const params = {
        lessonId: this.lessonId,
        type: this.type
      };

      // 显示加载状态和弹窗
      this.historyVisible = true;
      this.historyLoading = true; // 开始骨架屏加载
      this.resourceVersions = []; // 清空之前的版本记录

      // 创建当前版本记录
      const currentDate = new Date();
      const currentVersion = {
        id: 'current',
        date: currentDate,
        formattedDate: this.formatDateTime(currentDate),
        resources: JSON.parse(JSON.stringify(this.localResources)),
        isCurrent: true
      };

      // 调用 API 获取历史记录
      this.$axios.get($api.urls().getResourceHistory, { params })
        .then(response => {

          // 初始化版本列表，首先添加当前版本
          this.resourceVersions = [currentVersion];

          // 如果响应中有历史数据，则添加到版本列表中
          if (response && response.versions && response.versions.length > 0) {
            // 处理返回的历史记录数据
            const historyVersions = response.versions.map(version => {
              // 转换日期字符串为 Date 对象
              const date = new Date(version.createTime);


              return {
                id: version.id,
                date: date,
                formattedDate: this.formatDateTime(date),
                resources: version.sources,
                isCurrent: false, // 历史版本都不是当前版本
                content: version.content
              };
            });
            // 将历史版本添加到版本列表中
            this.resourceVersions = this.resourceVersions.concat(historyVersions);
          }

          // 选择当前版本
          this.selectedVersionIndex = 0;

          // 设置一个短暂的延迟，让骨架屏显示一会儿，提升用户体验
          setTimeout(() => {
            this.historyLoading = false; // 数据加载完成，关闭骨架屏
          }, 500);
        })
        .catch(error => {
          // 出错时至少显示当前版本
          this.resourceVersions = [currentVersion];
          this.selectedVersionIndex = 0;
          this.historyLoading = false; // 加载失败，关闭骨架屏
        });
    },
    loadMore() {
      this.showAll = !this.showAll
    },
    formatDate(date) {
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    },
    getDefaultCover() {
      return 'https://via.placeholder.com/160x120'
    },
    addAdditionalResource(item) {
      if (equalsIgnoreCase(item.sourceLink, '')) {
        this.$message.error('Please enter resource URL')
        return
      }

      // 检查本地资源列表中是否已存在相同的资源
      const isDuplicate = this.localResources.some(resource =>
        equalsIgnoreCase(resource.sourceLink, item.sourceLink) && equalsIgnoreCase(resource.sourceKeywords, item.sourceKeywords)
      )

      // 如果存在相同资源，显示提示并返回
      if (isDuplicate) {
        return
      }

      this.subscript++

      // 创建新资源对象
      const newResource = {
        subscript: this.subscript.toString(),
        sourceKeywords: item.sourceKeywords,
        sourceLink: item.sourceLink,
        source: item.source || "",
        videoLink: null,
        cover: null,
        hidden: false,
        type: item.type || "",
        noMatchSubscript: true
      };

      // 添加到本地资源列表
      this.localResources.push(newResource);

      // 从额外资源列表中移除
      const index = this.additionalResources.findIndex(
        r => equalsIgnoreCase(r.sourceLink, item.sourceLink)
      );
      if (index !== -1) {
        this.additionalResources.splice(index, 1);
      }

      // 如果没有更多额外资源，关闭显示
      if (this.additionalResources.length === 0) {
        this.showAll = false;
      }

      // 显示添加成功消息
      this.$message({
        type: 'success',
        message: this.$t('loc.resourceAddedSuccessfully')
      })
    },
    validateUrl(rule, value, callback) {
      if (!value) {
        callback(new Error('Please enter resource URL'))
        return
      }

      // 检查 URL 是否以 http 或 https 开头
      if (!value.startsWith('http://') && !value.startsWith('https://')) {
        callback(new Error('URL must start with http:// or https://'))
        return
      }

      // 使用正则表达式验证 URL 格式
      const urlPattern = /^(https?:\/\/)(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/

      if (!urlPattern.test(value)) {
        callback(new Error('Please enter a valid URL'))
        return
      }

      // 验证通过
      callback()
    },
    selectVersion(index) {
      this.selectedVersionIndex = index;
    },
    restoreVersion() {
      if (this.selectedVersionIndex <= 0 || this.selectedVersionIndex >= this.resourceVersions.length) {
        return;
      }

      // 恢复到选定版本
      const selectedVersion = this.resourceVersions[this.selectedVersionIndex];

      // 确认恢复操作
      this.$confirm(this.$t('loc.restoreResourceVersion'), 'Confirmation', {
        confirmButtonText: this.$t('loc.resourceConfirm'),
        cancelButtonText: this.$t('loc.resourceCancel'),
        width: '600px',
        confirmButtonClass: 'el-button--primary restore-confirm-btn'
      }).then(() => {
        // 用户确认恢复
        // 深拷贝选定版本的资源
        const restoredResources = JSON.parse(JSON.stringify(selectedVersion.resources));

        // 确保所有恢复的资源的 hidden 属性都设置为 false
        restoredResources.forEach(resource => {
          resource.hidden = false;
          resource.noMatchSubscript = true;
        });
        this.isRestore = true;
        // 更新本地资源
        this.localResources = restoredResources;
        this.localCLRContent = selectedVersion.content;
        // 添加当前版本到历史记录的头部
        this.resourceVersions.unshift({
          date: new Date(),
          resources: restoredResources,
          formattedDate: this.formatDateTime(new Date()),
          isCurrent: true
        });
        // 关闭历史弹窗
        this.historyVisible = false;

        this.$message({
          type: 'success',
          message: `Successfully restored to Version ${this.resourceVersions.length - this.selectedVersionIndex - 1}.`,
          customClass: 'top-message'
        });
      }).catch(() => {
        // 用户取消恢复，不做任何操作
      });
    },
    // 检查是否可以添加更多文件
    canAddMoreFiles() {
      return this.currentFileNum + this.addFileNum < this.maxFileLimit;
    },
    // 组件销毁时清理所有定时器
    beforeDestroy() {
      // 组件销毁时清理所有定时器
      this.clearAllProgressIntervals();
    },
    // 处理来自ResourceAddModal组件的添加资源事件
    handleAddResources(resources) {
      if (!resources || resources.length === 0) {
        return;
      }

      // 关闭添加资源弹窗
      this.resourceModalVisible = false;


      // 合并到本地资源列表
      const updatedResources = [...this.localResources];

      // 计算新资源的起始角标：找到当前资源列表中最大的角标，然后加1

      // 过滤掉重复的资源
      const newResources = resources.filter(newResource => {
        // 检查本地资源列表中是否已存在相同的资源
        const isDuplicate = updatedResources.some(existingResource =>
          equalsIgnoreCase(existingResource.sourceLink, newResource.sourceLink) && equalsIgnoreCase(existingResource.sourceKeywords, newResource.sourceKeywords)
        );

        // 如果存在重复，记录日志并返回 false（过滤掉）
        if (isDuplicate) {
          return false;
        }
        return true;
      });

      // 如果所有资源都是重复的，显示提示消息
      if (newResources.length === 0 && resources.length > 0) {

        return;
      }

      // 添加新资源，并设置正确的序号
      newResources.forEach(resource => {
        this.subscript++; // 下一个资源的角标递增
        const newResource = {
          ...resource,
          subscript: this.subscript.toString()
        };
        updatedResources.push(newResource);
      });

      // 更新本地资源列表
      this.localResources = updatedResources;

      // 如果是文件类型资源，更新文件计数
      const fileResources = newResources.filter(r => equalsIgnoreCase(r.type, 'File'));
      if (fileResources.length > 0) {
        this.currentFileNum += fileResources.length;
      }

      // 显示成功消息
      this.$message({
        type: 'success',
        message: this.$t('loc.resourceAddedSuccessfully')
      });
    },
    // 处理来自ResourceAddModal组件的更新资源事件
    handleUpdateResource(resources) {
      if (!resources || resources.length === 0) {
        return;
      }

      // 关闭添加资源弹窗
      this.resourceModalVisible = false;


      // 更新本地资源列表中的对应资源
      const updatedResources = [...this.localResources];
      const updatedResource = resources[0]; // 只会有一个资源

      // 如果原来的资源是文件类型，但现在不是，则减少文件计数
      if (equalsIgnoreCase(updatedResources[this.editingIndex].type, 'File') && equalsIgnoreCase(updatedResource.type, 'File')) {
        this.currentFileNum--;
      }

      // 如果原来的资源不是文件类型，但现在是，则增加文件计数
      if (equalsIgnoreCase(updatedResources[this.editingIndex].type, 'File') && equalsIgnoreCase(updatedResource.type, 'File')) {
        this.currentFileNum++;
      }

      // 更新资源，保留原有的subscript
      updatedResources[this.editingIndex] = {
        ...updatedResource,
        subscript: updatedResources[this.editingIndex].subscript
      };

      // 更新本地资源列表
      this.localResources = updatedResources;

      this.$message({
        type: 'success',
        message: this.$t('loc.resourceUpdatedSuccessfully')
      });

    },
    // 获取文件图标
    getFileIconClass(fileName) {
      return FileUtils.getFileType(fileName)
    },

    // 获取显示索引对应的原始索引
    getOriginalIndex(displayIndex) {
      return this.versionIndexMap[displayIndex] !== undefined ? this.versionIndexMap[displayIndex] : displayIndex;
    },
        // 下载文件
    downloadFile(source) {
      FileUtils.courseDownload(source.sourceLink, source.fileName)
    },
    // 移除 additionalResources 中与 localResources 重复的资源
    removeAdditionalDuplicates() {
      if (!this.additionalResources || !this.localResources) return;

      // 创建一个新的 additionalResources 数组，只包含不在 localResources 中的资源
      const filteredAdditionalResources = this.additionalResources.filter(additionalResource => {
        // 检查此额外资源是否存在于本地资源中
        const isDuplicate = this.localResources.some(localResource => {
          // 根据 sourceLink 和 sourceKeywords 比较是否为同一资源
          return equalsIgnoreCase(localResource.sourceLink, additionalResource.sourceLink);
        });

        // 返回 false 表示要过滤掉这个资源（它是重复的）
        return !isDuplicate;
      });

      // 更新 additionalResources
      this.additionalResources = filteredAdditionalResources;
    },
  },
  computed: {
    // 判断是否有额外资源可以显示
    hasMoreResources() {
      return this.additionalResources && this.additionalResources.length > 0;
    },
    // 处理显示版本列表逻辑
    displayVersions() {
      if (!this.resourceVersions || this.resourceVersions.length === 0) {
        return [];
      }

      // 最多显示20个版本
      const MAX_VERSIONS = 20;

      // 获取当前版本和历史版本
      const currentVersion = this.resourceVersions[0];
      const historyVersions = this.resourceVersions.slice(1);

      // 检查当前版本与最近一个历史版本是否一致
      let isCurrentMatchLatest = false;
      if (historyVersions.length > 0) {
        const latestHistoryVersion = historyVersions[0];

        // 过滤掉当前版本中hidden为true的资源
        const visibleCurrentResources = currentVersion.resources.filter(resource => !resource.hidden);

        // 比较资源数量是否相同
        if (visibleCurrentResources.length === latestHistoryVersion.resources.length) {
          // 如果资源数量相同，检查每个资源的内容是否一致
          isCurrentMatchLatest = visibleCurrentResources.every((resource, index) => {
            const historyResource = latestHistoryVersion.resources[index];
            return equalsIgnoreCase(resource.sourceLink, historyResource.sourceLink) &&
                   equalsIgnoreCase(resource.sourceKeywords, historyResource.sourceKeywords);
          });
        }
      }

      let result = [];

      // 如果当前版本与最近历史版本一致，则不显示最近历史版本
      if (isCurrentMatchLatest && historyVersions.length > 0) {
        result = [currentVersion, ...historyVersions.slice(1)];
      } else {
        result = [...this.resourceVersions];
      }

      // 如果不一致，则不显示最远的那个版本（当总数超过 MAX_VERSIONS 时）
      if (!isCurrentMatchLatest && result.length > MAX_VERSIONS) {
        result = result.slice(0, MAX_VERSIONS);
      } else if (result.length > MAX_VERSIONS) {
        // 如果一致，且版本总数超过 MAX_VERSIONS，则保留前 MAX_VERSIONS 个
        result = result.slice(0, MAX_VERSIONS);
      }

      return result;
    },
    // 保存原始索引与显示索引的映射关系
    versionIndexMap() {
      const map = {};
      if (this.displayVersions.length > 0) {
        this.displayVersions.forEach((version, displayIndex) => {
          const originalIndex = this.resourceVersions.findIndex(v =>
            v.id === version.id && v.formattedDate === version.formattedDate);
          map[displayIndex] = originalIndex;
        });
      }
      return map;
    }
  },
}
</script>

<style lang="less" scoped>
.media-resource {
  width: 280px;
  padding-left: 25px;
  height: 160px;
  margin-bottom: -15px;
}
/deep/ .delete-confirm-msgbox {
  z-index: 9999 !important;
}

/deep/ .delete-confirm-msgbox .el-button--primary {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
}

/deep/ .delete-confirm-msgbox .el-button--primary:hover,
/deep/ .delete-confirm-msgbox .el-button--primary:focus {
  background-color: #f78989 !important;
  border-color: #f78989 !important;
}

/deep/ .cancel-confirm-btn {
  margin-right: 10px;
}

/deep/ .edit-resources-custom-dialog {
  .el-dialog__body  {
    padding-top: 10px;
  }
  margin-top: 10vh !important;
  line-height: 14px;
  font-family: Inter;
}

.resource-edit-container {
  padding: 0;
}

.resource-actions {
  margin-bottom: 20px;
  display: flex;
  gap: 0;
}

.resources-list-header {
  margin-bottom: 15px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.download-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 4px;
  position: relative;
  padding-bottom: 10px;
  cursor: pointer;
  i {
    color: #333;
    font-size: 24px;
  }
}
.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  align-items: center;
  .resource-actions {
    margin-bottom: 0;
  }
}

.resource-content {
  flex: 1;
}


.resource-link{
    padding-left: 30px;
    padding-right: 20px;
  a {
    color: rgb(16, 179, 183);
    text-decoration: underline;
    word-break: break-all;
  }
}




.video-thumbnail {
  position: relative;
  width: 160px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      color: white;
      font-size: 20px;
    }
  }
}

.load-more {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.load-more button {
  width: 100%;
  background-color: #f5f5f5;
  border: 1px solid #e5e5e5;
  color: #333;
  height: 40px;
  border-radius: 4px;
  font-size: 14px;
}

.load-more button:hover {
  background-color: #e8e8e8;
}

.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
}

.history-date {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.history-resource {
  margin-bottom: 10px;
}

.history-link {
  margin-left: 20px;
  margin-top: 5px;

  a {
    color: #409EFF;
    text-decoration: underline;
  }
}

.no-history {
  text-align: center;
  color: #909399;
  padding: 30px 0;
}

/* 黑色图标按钮样式 */
/deep/ .black-icon-btn {
  color: #333 !important;
  font-size: 18px !important;
  padding: 10px !important;
}

/deep/ .black-icon-btn:hover {
  color: #000 !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/deep/ .black-icon-btn i {
  font-size: 20px !important;
}

.additional-resources {
  margin-top: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 20px;
}

.additional-resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.additional-resource-item:last-child {
  border-bottom: none;
}

.additional-resource-info {
  display: flex;
  align-items: flex-start;
}

.additional-resource-info i {
  color: #666;
  margin-top: 4px;
}

.additional-resource-text {
  margin-left: 10px;
  font-size: 14px;
  width: 95%;
}

.additional-resource-link {
  margin-top: 5px;
  font-size: 13px;
  color: rgb(16, 179, 183);
}

.add-resource-btn {
  padding: 7px 15px;
  font-size: 14px;
  border-radius: 4px;
  background-color: #10b3b7;
  border-color: #10b3b7;
}

.add-resource-btn:hover {
  background-color: #0d9b9f;
  border-color: #0d9b9f;
}

.confirm-content {
  font-size: 16px;
  padding: 10px 0;
  display: none;
}

.cancel-btn {
  margin-right: 10px;
  display: none;
}

.confirm-btn {
  background-color: #f56c6c;
  border-color: #f56c6c;
  display: none;
}

.confirm-btn:hover,
.confirm-btn:focus {
  background-color: #f78989;
  border-color: #f78989;
  display: none;
}

/deep/ .resource-edit-dialog {
  .el-dialog__body {
    padding: 0 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 0;
  }
}

.save-form-btn {
  background-color: #10b3b7;
  border-color: #10b3b7;
  padding: 10px 20px;
}

.save-form-btn:hover {
  background-color: #0d9b9f;
  border-color: #0d9b9f;
}

.cancel-form-btn {
  padding: 10px 20px;
}

/deep/ .el-form-item__label {
  font-weight: 600;
  padding-bottom: 8px;
}

/deep/ .el-form-item.is-required .el-form-item__label:before {
  color: #ff4949;
  content: "*";
  margin-right: 4px;
}

.upload-container {
  border: 2px dashed #10b3b7;
  border-radius: 6px;
  background-color: rgba(16, 179, 183, 0.05);
  padding: 0;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.upload-container.dragover {
  border-color: #10b3b7;
  background-color: rgba(16, 179, 183, 0.15);
  transform: scale(1.01);
  box-shadow: 0 0 10px rgba(16, 179, 183, 0.2);
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
  width: 100%;
}

.upload-icon {
  font-size: 30px;
  margin-bottom: 15px;
  color: #10b3b7;
  background-color: rgba(16, 179, 183, 0.1);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  text-align: center;
  margin-bottom: 15px;
}

.browse-btn {
  background-color: #10b3b7;
  border-color: #10b3b7;
  padding: 8px 15px;
  font-size: 14px;
}

.browse-btn:hover {
  background-color: #0d9b9f;
  border-color: #0d9b9f;
}

.file-list {
  margin-top: 15px;
}

.file-item {
  display: flex;
  align-items: flex-start;
  padding: 10px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #f9f9f9;
  position: relative;
}

.file-icon {
  margin-right: 10px;
  width: 25px;
  height: 50px;
  display: flex;
  align-items: center;
}

.file-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.add-file-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: calc(100% - 90px);
  padding-top: 6px;
  line-height: 10px;
}

.file-name {
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #333;
  width: 90%;
}

.file-progress-row {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 5px;
}

.file-progress {
  flex: 1;
}

.progress-text {
  margin-left: 10px;
  font-size: 12px;
  color: #606266;
  min-width: 40px;
  text-align: right;
}

.file-status {
  color: #67c23a;
}

.success-icon {
  font-size: 16px;
}

.file-actions {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
  color: #909399;
}

.file-actions i:hover {
  color: #f56c6c;
}

/deep/ .resource-edit-dialog .el-form-item {
  margin-bottom: 0px !important;
}

/deep/ .top-message {
  z-index: 9999 !important;
}

/deep/ .cancel-confirm-btn {
  margin-right: 10px;
}

.history-container {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.version-history-panel {
  flex: 0 0 240px;
  padding-left: 10px;
  overflow-y: auto;
  background-color: #fff;
}

.version-panel-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  padding: 15px;
  background-color: #fff;
}

.version-list {
  display: flex;
  flex-direction: column;
}

.version-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  cursor: pointer;
  background-color: #fff;
}

.version-item:hover:not(.active) {
  background-color: rgba(16, 179, 183, 0.05);
}

.version-item.active {
  background-color: #e2f8f8;
  position: relative;
}

.version-item.active.current-version::before,
.version-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #10b3b7;
  display: block !important;
  z-index: 2;
}

.version-item.current-version {
  position: relative;
}

.version-item.current-version:not(.active)::before {
  display: none;
}

.version-number {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
  margin-left: 5px;
  background: #DDF2F3;
  border: 1px solid #10B3B7;
  color: #10B3B7;
}

.version-date {
  font-size: 13px;
  color: #909399;
  margin-top: 5px;
}

.version-resources-panel {
  margin-right: 20px;
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #f5f7fa;
  margin-left: 16px;
  border-radius: 8px;
}

.no-version-selected {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding-top: 30px;
}

.version-resource-item {
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
  position: relative;
}


.resource-item-header {
  display: flex;
  flex-wrap: wrap;
}

.resource-index {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 5px;
  .resource-title {
    width: 90%;
  }
}

.resource-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 5px;
  margin-bottom: 5px;
  word-break: normal;
  display: flex;
}

.resource-source {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.resource-link {
  margin-top: 5px;
  margin-bottom: 10px;
}


.resource-video {
  margin-top: 10px;
  width: 180px;
  padding-left: 25px;
}

.video-preview {
  position: relative;
  width: 180px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #eee;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      color: white;
      font-size: 20px;
    }
  }
}

.resource-file-info {
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.file-type-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}


.restore-btn {
  background-color: #10b3b7;
  border-color: #10b3b7;
  padding: 8px 15px;
  font-size: 14px;
}

.restore-btn:hover {
  background-color: #0d9b9f;
  border-color: #0d9b9f;
}

/deep/ .restore-confirm-btn {
  background-color: #10b3b7 !important;
  border-color: #10b3b7 !important;
}

/deep/ .restore-confirm-btn:hover {
  background-color: #0d9b9f !important;
  border-color: #0d9b9f !important;
}

/deep/ .resources-history-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    background-color: #fff;
    border-radius: 8px 8px 0 0;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .el-dialog__body {
    padding: 0;
    height: 350px;
  }

  .el-dialog__footer {
    padding: 10px 20px 24px;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
  }
}


.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-direction: row;
  min-height: 42px;
}

.file-type-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-type-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.file-details {
  display: flex;
  flex-direction: column;
}



</style>