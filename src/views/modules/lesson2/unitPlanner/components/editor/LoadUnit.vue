<template>
  <div>
    <!-- 加载页 -->
    <div class="unit-loading" v-show="getUnitLoading" v-loading="getUnitLoading"></div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {equalsNotIgnoreCase, equalsIgnoreCase} from '@/utils/common'

export default {
  components: {},
  data () {
    return {
      getUnitLoading: true, // 获取单元详情 loading
      generateFlag: false, // 是否生成单元
      isDestroyed: false, // 组件是否已销毁
      adaptedType: null // 改编类型：LIGHT、DEEP
    }
  },
  created () {
    // 获取单元数据
    this.getUnit()
  },
  beforeDestroy () {
    // 标记组件即将销毁
    this.isDestroyed = true
  },
  computed: {
    ...mapState({
      unitId: state => state.curriculum.unit.id, // 单元 ID
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      isMC: state => state.curriculum.isMC, // 单元 ID
      currentUser: state => state.user.currentUser // 当前用户
    })
  },
  methods: {
    // 安全路由跳转 - 检查组件是否存在且使用 replace 避免历史记录
    safeRouterNavigate (routeConfig) {
      // 检查组件是否已销毁
      if (this.isDestroyed) {
        return false
      }
      
      // 检查 Vue 实例是否还存在
      if (!this.$router || !this.$router.replace) {
        return false
      }

      try {
        // 使用 replace 而不是 push，避免在浏览器历史记录中留下当前中间页面
        this.$router.replace(routeConfig)
        return true
      } catch (error) {
        return false
      }
    },

    // 加载单元数据
    getUnit () {
      // 获取路由参数 unitId
      const unitId = this.$route.params.unitId
      // 获取路由参数 generateFlag
      this.generateFlag = this.$route.params.generateFlag
      // 没有指定单元 ID 或者和当前已有 ID 相同则跳过
      if (!unitId || unitId === this.unitId) {
        this.$store.commit('curriculum/RESET_UNIT')
        // // 返回列表页
        // // 修复 Magic 平台返回路由错误
        // this.safeRouterNavigate({
        //   name: this.isMC ? 'unit-planner-cg' : 'unitPlanner'
        // })
        // return
      }
      // 请求参数
      let params = {
        params: {
          unitId: unitId,
          edit: !this.$route.params.goToOverview
        }
      }
      if (this.$route.params.goToOverview) {
        params.params.alwaysDetail = true
        params.params.needCheckLock = true
      }
      // 获取单元详情
      this.$axios
      .get($api.urls().getUnitInfo, params).then((res) => {
        // 检查组件是否仍然存在
        if (this.isDestroyed) {
          return
        }

        // 如果要去概览页并且进度为 100，则不显示锁定提示
        if (res.lockedData && !(this.$route.params.goToOverview && res.unit.progress === 100)) {
          let userName = res.lockedData.userName
          this.$message.error(userName + this.$t('loc.unitPlannerIsEditing'))
          this.safeRouterNavigate({
              name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-planner-cg' : 'unitPlanner'
          })
        }
        let unit = res.unit
        this.adaptedType = unit.adaptedType
        // 单元基本信息
        unit.baseInfo = {
          title: unit.title, // 标题
          number: unit.number, // 序号
          country: unit.country || 'United States', // 国家
          state: unit.state, // 状态
          city: unit.city, // 城市
          description: unit.description, // 描述
          frameworkId: unit.frameworkId, // 框架 ID
          frameworkName: unit.frameworkName, // 框架名称
          domainIds: unit.domainIds, // 领域 ID
          allDomains: unit.options, // 选项
          grade: unit.grade, // 年级
          language: unit.language, // 语言
          weekCount: unit.weekCount, // 周数
          progress: unit.progress, // 进度
          rubrics: unit.rubrics, // 校训
          newRubrics: unit.newRubrics, // 新校训
          oldGrade: unit.grade, // 旧年级
          useLocation: unit.useLocation, // 是否显示国家城市
          useDomain: unit.useDomain, // 是否按领域选择
          measureIds: unit.measureIds, // measure ID
          useLessonTemplate: unit.useLessonTemplate, // 是否使用课程模板
          showLessonTemplateTip: unit.showLessonTemplateTip, // 是否显示课程模板提示
          classroomType: unit.classroomType || 'IN_PERSON', // 课堂类型：IN_PERSON 或 VIRTUAL
          adaptedType: unit.adaptedType, // 改编类型
          adaptedModuleSwitch: unit.adaptedModuleSwitch, // 改编模块开关
          measures: unit.measures.map(measure => {
            return {
              id: measure.id,
              abbreviation: measure.abbreviation,
              name: measure.name
            };
          }) // measures 已选测评点
        }
        // 单元概览信息
        unit.overview = {
          title: unit.title, // 标题
          coverMedias: unit.coverMedias || [],
          coverKeywords: unit.coverKeywords, // 封面搜索关键词
          overview: unit.overview, // 概览
          concepts: unit.concepts, // 概念
          guidingQuestions: unit.guidingQuestions, // 指导问题
          trajectory: unit.trajectory // 轨迹
        }
        unit.customFoundationInfos = res.unit.customFoundationInfos
        unit.adaptedType = res.unit.adaptedType
        unit.adaptedModuleSwitch = res.unit.adaptedModuleSwitch
        // 构建 customFoundationData
        unit.customFoundationData = {}
        if (unit.customFoundationInfos && unit.customFoundationInfos.length > 0) {
          unit.customFoundationInfos.forEach((item, index) => {
            unit.customFoundationData[item.key] = item.content
          })
        }
        this.$store.commit('curriculum/SET_UNIT', unit)
        // 设置扩展参数
        this.$store.dispatch('curriculum/setPromptExtensionParams', {
          unitId: unitId
        })
        if (!this.generateFlag) {
          // 定位当前步骤
          this.locateStepV2(unit)
        } else {
          // 跳转到课程概览
          this.locateStepV3(unit)
        }
      }).catch(error => {
        // 检查组件是否仍然存在
        if (this.isDestroyed) {
          return
        }
        if (equalsNotIgnoreCase(error.response.data.error_message, "LESSON_NOT_FOUND")) {
          this.$message.error("This unit is no longer available. It may have been deleted.")
        } else {
          this.$message.error(error.response.data.error_message)
        }
        // 返回列表页
        this.safeRouterNavigate({
          name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-planner-cg' : 'unitPlanner'
        })
      })
    },
    // 获取第一个周的周计划数据并跳转到第一个符合的课程
    locateLessonDetail (unit) {
      this.$axios.get($api.urls().getUnitNavigationData, {
        params: {
          unitId: this.unitId
        }
      }).then((res) => {
        // 检查组件是否仍然存在
        if (this.isDestroyed) {
          return
        }

        // 如果没有获取到数据，则跳转到课程概览
        if (!res || !res.unitId || !res.weeklyPlans || res.weeklyPlans.length === 0) {
          this.goToLessonOverview(unit)
          return
        }
        // 获取 itemId：优先从 items 中获取，如果不存在则从 centerItems 中获取
        const weeklyPlan = res.weeklyPlans[0]
        let itemId = null
        if (weeklyPlan.items && weeklyPlan.items.length > 0) {
          itemId = weeklyPlan.items[0].id
        } else if (weeklyPlan.centerItems && weeklyPlan.centerItems.length > 0) {
          itemId = weeklyPlan.centerItems[0].id
        }
        // 如果没有 itemId，则跳转到课程概览
        if (!itemId) {
          this.goToLessonOverview(unit)
          return
        }
        // 设置是否显示满意度调查问卷
        // 判断14天内是否已经展示过调查问卷
        const key = 'lastSurveyDate' + this.currentUser.user_id
        const lastSurveyDate = localStorage.getItem(key)
        const fourteenDaysAgo = this.$moment().subtract(14, 'days')
        // 如果从未显示过问卷或者上次显示问卷时间超过14天，则显示问卷
        if (!lastSurveyDate || this.$moment(lastSurveyDate).isBefore(fourteenDaysAgo)) {
          this.$store.dispatch('unit/setShowSurvey', true)
        } else {
          this.$store.dispatch('unit/setShowSurvey', false)
        }
        let cgRouteName = 'lesson-detail-cg'
        if (this.adaptedType) {
          cgRouteName = 'lesson-detail-cg-adapt'
        }
        // 跳转详情页
        this.safeRouterNavigate({
          name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'lessonDetail',
          params: {
            itemId: itemId
          }
        })
      }).catch((error) => {
        // 检查组件是否仍然存在
        if (this.isDestroyed) {
          return
        }
        this.goToLessonOverview(unit)
      })
    },
    // 跳转到课程概览
    goToLessonOverview (unit) {
      this.safeRouterNavigate({
        name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'lesson-overview-cg' : 'lessonOverview',
        params: {
          unitId: this.unitId,
          week: unit.weekCount
        }
      })
    },
    // 根据进度定位当前步骤
    locateStepV2 (unit) {
        // 检查组件是否仍然存在
        if (this.isDestroyed) {
          return
        }
        if (equalsIgnoreCase(this.adaptedType, 'LIGHT')) {
          // 获取第一个周的周计划数据并跳转到第一个符合的课程
          this.locateLessonDetail(unit)
          return
        }
        let cgRouteName = ''
        // 获取进度
        const progress = unit.progress
        switch (progress) {
            case 0:
            case 20:
                cgRouteName = 'unit-overview-cg'
                if (equalsIgnoreCase(this.adaptedType, 'DEEP')) {
                  cgRouteName = 'unit-overview-cg-adapt'
                }
                this.safeRouterNavigate({
                    name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'unitOverview',
                    params: {
                        unitId: this.unitId
                    }
                })
                break
            case 40:
                cgRouteName = 'edit-weekly-plan-overview-cg'
                if (equalsIgnoreCase(this.adaptedType, 'DEEP')) {
                  cgRouteName = 'edit-weekly-plan-overview-cg-adapt'
                }
                this.safeRouterNavigate({
                    name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ?  cgRouteName : 'weeklyPlanOverview',
                    params: {
                        unitId: this.unitId
                    }
                })
                break
            case 60:
            case 80:
                cgRouteName = 'lesson-overview-cg'  
                if (equalsIgnoreCase(this.adaptedType, 'DEEP')) {
                  cgRouteName = 'lesson-overview-cg-adapt'
                }
                this.safeRouterNavigate({
                    name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'lessonOverview',
                    params: {
                        unitId: this.unitId,
                        week: 1
                    }
                })
                break
            case 100:
                // 如果是邮件链接进入的调整到单元详情页
                if (this.$route.params.completeToOverview || this.$route.params.goToOverview) {
                  this.safeRouterNavigate({
                    name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail',
                    query: {
                      unitId: this.unitId,
                    }
                  })
                  break
                }
                // 获取第一个周的周计划数据并跳转到第一个符合的课程
                this.locateLessonDetail(unit)
                break
        }
    },
    // 根据进度定位当前步骤的下一步
    locateStepV3(unit) {
      // 检查组件是否仍然存在
      if (this.isDestroyed) {
        return
      }

      // 获取进度
      const progress = unit.progress
      let cgRouteName = ''
      switch (progress) {
        case 0:
        case 20:
          // 判断  unit.overview  中的属性都不能为空，如果都不为空则跳转到周计划概览
          if (unit.overview.title && unit.overview.coverMedias && unit.overview.overview && unit.overview.concepts && unit.overview.guidingQuestions && unit.overview.trajectory) {
            cgRouteName = 'edit-weekly-plan-overview-cg'
            if (this.adaptedType) {
              cgRouteName = 'edit-weekly-plan-overview-cg-adapt'
            }
            this.safeRouterNavigate({
              name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'weeklyPlanOverview',
              params: {
                unitId: this.unitId
              }
            })
          } else {
            cgRouteName = 'unit-overview-cg'
            if (this.adaptedType) {
              cgRouteName = 'unit-overview-cg-adapt'
            }
            // 如果 unit.overview 中的属性为空，则跳转到单元概览
            this.safeRouterNavigate({
              name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'unitOverview',
              params: {
                unitId: this.unitId
              }
            })
          }
          break
        case 40:
          cgRouteName = 'lesson-overview-cg'
          if (this.adaptedType) {
            cgRouteName = 'lesson-overview-cg-adapt'
          }
          this.safeRouterNavigate({
            name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'lessonOverview',
            params: {
              unitId: this.unitId,
              week: 1
            }
          })
          break
        case 60:
        case 80:
          this.$axios.get($api.urls().getUnitNavigationData, {
            params: {
              unitId: this.unitId
            }
          }).then((res) => {
            // 检查组件是否仍然存在
            if (this.isDestroyed) {
              return
            }

            // 如果没有获取到数据，则跳转到课程概览
            if (!res || !res.unitId || !res.weeklyPlans || res.weeklyPlans.length === 0) {
              this.goToLessonOverview(unit)
              return
            }
            let itemId = null
            let batchGenerate = false
            // 获取当前单元所有周的周计划数据
            const weeklyPlans = res.weeklyPlans
            // 判断 weeklyPlans 中是否已经生成的有课程
            if (weeklyPlans && weeklyPlans.length > 0) {
              // 遍历 weeklyPlans 中的每一周，判断是否已经生成的有课程
              for (let i = 0; i < weeklyPlans.length; i++) {
                const weeklyPlan = weeklyPlans[i]
                if (weeklyPlan && weeklyPlan.items && weeklyPlan.items.length > 0) {
                  // 遍历 weeklyPlan.items 中的每一项，判断是否已经生成的有课程
                  for (let j = 0; j < weeklyPlan.items.length; j++) {
                    const item = weeklyPlan.items[j]
                    // 若有生成的课程详情，则批量生成其他课程
                    if (item.lessonId) {
                      batchGenerate = true
                    } else if (!itemId) {
                      itemId = item.id
                    }
                  }
                }
              }
              // 若没有找到 batchGenerate 为 true，则说明所有课程都没有生成。则单独生成第一个课程
              if (!batchGenerate) {
                cgRouteName = 'lesson-overview-cg'
                if (this.adaptedType) {
                  cgRouteName = 'lesson-overview-cg-adapt'
                }
                // 则说明所有课程都没有生成。则调到 3.1 页面再批量生成
                this.safeRouterNavigate({
                    name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'lessonOverview',
                    params: {
                      unitId: this.unitId,
                      week: 1,
                      batchGenerate: true
                    }
                  })
              } else {
                // 则说明有课程已经生成。则跳转到课程详情
                let params = {
                    unitId: this.unitId,
                    itemId: itemId,
                    batchGenerate: true
                  }
                  cgRouteName = 'lesson-detail-cg'
                  if (this.adaptedType) {
                    cgRouteName = 'lesson-detail-cg-adapt'
                  }
                  this.safeRouterNavigate({
                    name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? cgRouteName : 'lessonDetail',
                    params: params
                  })
              }
              
            }
          }).catch((error) => {
            // 检查组件是否仍然存在
            if (this.isDestroyed) {
              return
            }
            this.goToLessonOverview(unit)
          })

          break
      }
    }
  }
}
</script>

<style lang="less" scoped>
.unit-loading {
  width: 100%;
  height: calc(100vh - 100px);
}
</style>
