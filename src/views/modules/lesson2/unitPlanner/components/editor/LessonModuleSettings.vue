<template>
  <div @click="openDialog">
    <!-- 自定义内容插槽 -->
    <slot name="content">
    </slot>
    
    <!-- 设置弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="900px"
      append-to-body
      @close="handleClose"
      custom-class="lesson-module-dialog"
      :close-on-click-modal="false"
    >
      <!-- 描述内容 -->
      <div class="module-description">
        {{ description }}
      </div>

      <!-- 全选选项 -->
      <div>
        <el-checkbox 
          v-model="selectAll" 
          :indeterminate="isIndeterminate"
          @change="handleSelectAllChange"
          class="select-all-checkbox"
        >
          <span class="select-all-text">Select all</span>
        </el-checkbox>
      </div>

      <!-- 模块列表 - 两列网格布局 -->
      <div class="module-grid">
        <template v-if="loading">
          <div v-for="i in 6" :key="i" class="module-card skeleton">
            <el-skeleton-item variant="button" style="width: 20px; height: 20px;" />
            <div class="module-info">
              <el-skeleton-item variant="text" style="width: 150px; margin-top: 8px;" />
              <el-skeleton-item variant="text" style="width: 200px; margin-top: 8px;" />
            </div>
          </div>
        </template>
        <template v-else>
          <div
            v-for="item in internalModuleList" 
            :key="item.id" 
            class="module-card"
            :class="{ 'disabled-module': !item.available }"
            @click="handleCardClick(item)"
          >
            <!-- 模块头部 -->
            <div class="module-header">
              <el-checkbox
                v-model="item.enabled"
                :indeterminate="item.id === 'clr' && isCLRIndeterminate(item)"
                @change="handleModuleChange(item)"
                @click.stop
                :disabled="!item.available || item.id === 'lesson_plan'"
                class="module-checkbox"
              >
                <span class="module-name">{{ item.name }}</span>
              </el-checkbox>
              <i 
                class="el-icon-info-circle module-info-icon" 
                v-if="item.description"
                @click.stop="showModuleInfo(item)"
              ></i>
            </div>
            
            <!-- 模块描述 -->
            <div class="module-description-text" v-if="item.description">
              {{ item.description }}
            </div>
            
            <!-- CLR 特殊选项 -->
            <div v-if="item.id === 'clr'" class="clr-options" @click.stop>
              <el-checkbox-group v-model="item.clrOptions" @change="handleCLROptionsChange">
                <el-checkbox label="0-TK">0-TK</el-checkbox>
                <el-checkbox label="K-12">K-12</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </template>
      </div>

      <!-- 自定义底部插槽 -->
      <template slot="footer">
        <slot name="footer">
          <div class="dialog-footer">
            <el-button @click="handleCancel" class="cancel-button">Cancel</el-button>
            <el-button type="primary" @click="handleSave" class="save-button" :loading="saveLoading">
              Save
            </el-button>
          </div>
        </slot>
      </template>
    </el-dialog>

    <!-- 模块信息提示 -->
    <el-tooltip
      v-model="moduleInfoVisible"
      :content="currentModuleInfo"
      placement="top"
      effect="dark"
      :manual="true"
      popper-class="module-info-tooltip"
    >
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'LessonModuleSettings',
  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: 'Customize Your Lesson Plan Modules'
    },
    // 弹窗描述
    description: {
      type: String,
      default: 'Please select the supporting components you want to include with your lesson plan.'
    },
    // 模块列表
    moduleList: {
      type: Array,
      default: () => []
    },
    // 设置埋点名称
    setupEventName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      // 弹窗是否可见
      dialogVisible: false,
      // 是否在加载中
      loading: false,
      // 保存按钮 loading 状态
      saveLoading: false,
      // 全选状态
      selectAll: false,
      // 半选状态
      isIndeterminate: false,
      // 模块信息提示是否可见
      moduleInfoVisible: false,
      // 当前显示的模块信息
      currentModuleInfo: '',
      // 内部模块列表副本
      internalModuleList: []
    }
  },
  watch: {
    visible (val) {
      this.dialogVisible = val
      if (val) {
        this.openDialog()
      }
    },
    dialogVisible (val) {
      this.$emit('update:visible', val)
    },
    // 监听模块列表变化，更新全选状态
    internalModuleList: {
      handler () {
        this.updateSelectAllState()
      },
      deep: true
    }
  },
  methods: {
    // 打开弹窗
    async openDialog () {
      this.dialogVisible = true
      await this.getModuleSettings()
      // 发送埋点事件
      if (this.setupEventName) {
        this.$analytics.sendEvent(this.setupEventName)
      }
    },

    // 获取模块设置
    async getModuleSettings () {
      try {
        this.loading = true
        // 初始化内部模块列表
        this.internalModuleList = this.moduleList.length > 0 
          ? JSON.parse(JSON.stringify(this.moduleList))
          : this.getDefaultModuleList()
        
        // TODO: 调用 API 获取用户当前的模块设置
        // const res = await moduleApi.getUserModuleSettings()
        // this.updateModuleList(res)
      } catch (error) {
        this.$message.error('Failed to load module settings')
      } finally {
        this.loading = false
      }
    },

    // 获取默认模块列表
    getDefaultModuleList () {
      return [
        {
          id: 'lesson_plan',
          name: 'Lesson Plan',
          description: 'Core lesson plan components always included (e.g. objectives, materials, implementation steps, vocabulary words)',
          enabled: true,
          available: false,
          category: 'core'
        },
        {
          id: 'clr',
          name: 'Culturally and Linguistically Responsive Practice (CLR)',
          description: '',
          enabled: true,
          available: true,
          category: 'cultural',
          clrOptions: ['0-TK', 'K-12']
        },
        {
          id: 'teaching_tips',
          name: 'Teaching Tips for Standards',
          description: 'Available for Birth to Grade 12',
          enabled: true,
          available: true,
          category: 'support'
        },
        {
          id: 'reading_passage',
          name: 'Reading Passage',
          description: 'Available for K to Grade 12',
          enabled: true,
          available: true,
          category: 'resources'
        },
        {
          id: 'typical_behaviors',
          name: 'Typical Behaviors and Observation Tips',
          description: 'Available for Birth to TK children only',
          enabled: true,
          available: true,
          category: 'support'
        },
        {
          id: 'udl',
          name: 'Universal Design for Learning (UDL)',
          description: 'Available for Birth to Grade 12',
          enabled: true,
          available: true,
          category: 'support'
        },
        {
          id: 'teacher_slides',
          name: 'Teacher Slides',
          description: 'Available for TK to Grade 12',
          enabled: true,
          available: true,
          category: 'resources'
        },
        {
          id: 'home_activities',
          name: 'At-Home Activities',
          description: 'Currently available for Birth to Grade 2 children',
          enabled: true,
          available: true,
          category: 'activities'
        },
        {
          id: 'eduprotocols',
          name: 'Eduprotocols-based Student Activities',
          description: 'Available for K to Grade 12',
          enabled: true,
          available: true,
          category: 'activities'
        },
        {
          id: 'formative_assessment',
          name: 'Formative Assessment',
          description: 'Available for K to Grade 12',
          enabled: true,
          available: true,
          category: 'assessment'
        },
        {
          id: 'mixed_age',
          name: 'Mixed-age Differentiations',
          description: 'Currently available for PS/PK (3-4) children in mixed-age classrooms upon adaptation',
          enabled: true,
          available: true,
          category: 'differentiation'
        }
      ]
    },

    // 处理全选变化
    handleSelectAllChange (value) {
      this.internalModuleList.forEach(module => {
        if (module.available && module.id !== 'lesson_plan') {
          module.enabled = value
        }
        if (module.id === 'clr') {
          module.clrOptions = value ? ['0-TK', 'K-12'] : []
        }
      })
      this.$emit('module-change', this.getEnabledModuleIds())
    },

    // 处理单个模块变化
    handleModuleChange (module) {
      // 如果是 CLR 模块，根据子选项状态更新模块状态
      if (module.id === 'clr') {
        // 当手动点击 CLR 模块时，如果当前是半选状态，则全选所有子选项
        if (module.enabled) {
          // 如果启用，确保至少有一个子选项          
          module.clrOptions = ['0-TK', 'K-12']
        } else {
          // 如果禁用，清空所有子选项
          module.clrOptions = []
        }
      }
      this.$emit('module-change', this.getEnabledModuleIds())
    },

    // 处理 CLR 选项变化
    handleCLROptionsChange (value) {
      const clrModule = this.internalModuleList.find(module => module.id === 'clr')
      if (clrModule) {
        clrModule.clrOptions = value
        // 如果 CLR 选项被选中，则启用 CLR 模块
        clrModule.enabled = value.length > 0
        // 手动触发全选状态更新，确保状态正确
        this.$nextTick(() => {
          this.updateSelectAllState()
        })
      }
      this.$emit('module-change', this.getEnabledModuleIds())
    },

    // 判断 CLR 模块是否为半选状态
    isCLRIndeterminate (module) {
      if (module.id !== 'clr') return false
      const clrOptions = module.clrOptions || []
      return clrOptions.length > 0 && clrOptions.length < 2
    },

    // 更新全选状态
    updateSelectAllState () {
      const availableModules = this.internalModuleList.filter(module => module.available && module.id !== 'lesson_plan')
      
      // 计算有效启用的模块数量（考虑 CLR 模块的特殊情况）
      let effectiveEnabledCount = 0
      let hasPartialSelection = false
      
      availableModules.forEach(module => {
        if (module.id === 'clr') {
          // CLR 模块特殊处理：根据子选项数量判断状态
          const clrOptions = module.clrOptions || []
          if (clrOptions.length === 2) {
            // 两个子选项都选中，视为完全启用
            effectiveEnabledCount++
          } else if (clrOptions.length === 1) {
            // 只有一个子选项选中，视为部分选中
            hasPartialSelection = true
          }
          // 没有子选项选中时，不增加计数
        } else {
          // 其他模块正常处理
          if (module.enabled) {
            effectiveEnabledCount++
          }
        }
      })
      
      if (availableModules.length === 0) {
        this.selectAll = false
        this.isIndeterminate = false
      } else if (effectiveEnabledCount === 0 && !hasPartialSelection) {
        // 没有任何模块启用且没有部分选中
        this.selectAll = false
        this.isIndeterminate = false
      } else if (effectiveEnabledCount === availableModules.length) {
        // 所有模块都完全启用
        this.selectAll = true
        this.isIndeterminate = false
      } else {
        // 有部分模块启用或有部分选中状态
        this.selectAll = false
        this.isIndeterminate = true
      }
    },

    // 获取启用的模块 ID 列表
    getEnabledModuleIds () {
      return this.internalModuleList
        .filter(module => module.enabled)
        .map(module => module.id)
    },

    // 显示模块信息
    showModuleInfo (module) {
      this.currentModuleInfo = module.description
      this.moduleInfoVisible = true
      setTimeout(() => {
        this.moduleInfoVisible = false
      }, 3000)
    },

    // 保存设置
    async handleSave () {
      try {
        this.saveLoading = true
        const enabledModuleIds = this.getEnabledModuleIds()
        
        // 验证至少选择一个模块
        if (enabledModuleIds.length === 0) {
          this.$message.warning('Please select at least one module')
          return
        }

        // TODO: 调用 API 保存模块设置
        // await moduleApi.saveModuleSettings(enabledModuleIds)
        
        this.$emit('save', enabledModuleIds)
        this.dialogVisible = false
        this.$message.success('Module settings saved successfully')
      } catch (error) {
        this.$message.error('Failed to save module settings')
      } finally {
        this.saveLoading = false
      }
    },

    // 取消操作
    handleCancel () {
      this.$emit('cancel')
      this.dialogVisible = false
    },

    // 处理卡片点击事件
    handleCardClick (module) {
      // 如果模块不可用或是 lesson_plan，则不处理点击
      if (!module.available || module.id === 'lesson_plan') {
        return
      }
      
      // 切换模块的启用状态
      module.enabled = !module.enabled
      
      // 如果是 CLR 模块，需要特殊处理
      if (module.id === 'clr') {
        if (module.enabled) {
          // 如果启用，确保至少有一个子选项
          module.clrOptions = ['0-TK', 'K-12']
        } else {
          // 如果禁用，清空所有子选项
          module.clrOptions = []
        }
      }
      
      // 触发模块变化事件
      this.$emit('module-change', this.getEnabledModuleIds())
    },

    // 关闭弹窗
    handleClose () {
      this.dialogVisible = false
      this.internalModuleList = []
    }
  }
}
</script>

<style lang="scss" scoped>
.lesson-module-settings {
  display: inline-block;
}

/* 弹窗样式 */
/deep/.lesson-module-dialog {
  border-radius: 8px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);

  .el-dialog__header {
    padding: 24px;
    padding-bottom: 16px;
    border-bottom: none;

    .el-dialog__title {
      font-family: Inter, sans-serif;
      font-weight: 600;
      font-size: 20px;
      color: #111C1C;
    }
  }

  .el-dialog__body {
    padding: 0 24px 24px;
  }
}

.module-description {
  font-family: Inter, sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  color: #111C1C;
  margin-bottom: 20px;
}

.module-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 24px;

  .module-card {
    background-color: #F0F6FF;;
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
    min-height: 100px; // 确保卡片有足够的最小高度
    width: 400px;
    cursor: pointer; // 添加指针样式，表明可点击
    border: 1px solid transparent;

    &:hover {
      border-color: #10B3B7;
    }

    &.disabled-module {
      cursor: not-allowed; // 禁用状态显示不可点击  
    }

    &.disabled-module:hover {
      border-color: transparent !important;
    }

    &.skeleton {
      min-height: 120px;
    }

    .module-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 8px;
      gap: 8px;

      .module-checkbox {
        display: flex;
        flex: 1;
        min-width: 0; // 允许 flex 项目收缩
        
        .module-name {
          font-family: Inter, sans-serif;
          font-weight: 600;
          font-size: 14px;
          color: #111C1C;
          line-height: 1.4;
          word-wrap: break-word;
          white-space: normal;
          display: block;
          hyphens: auto; // 自动连字符
        }
      }

      .module-info-icon {
        color: #10B3B7;
        cursor: pointer;
        font-size: 16px;
        flex-shrink: 0;
        margin-top: 2px; // 稍微向下调整，与文本对齐
      }
    }

    .module-description-text {
      font-family: Inter, sans-serif;
      color: #606266;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .clr-options {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;

      /deep/ .el-checkbox-group {
        display: flex;
        gap: 16px;

        .el-checkbox {
          margin-right: 0;
          
          .el-checkbox__label {
            color: #606266;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-button {
    border: 1px solid #DCDFE6;
    color: #111C1C;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    height: 36px;
    background-color: #fff;
  }

  .save-button {
    background-color: #10B3B7;
    border: 1px solid #10B3B7;
    color: #FFFFFF;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    height: 36px;
  }
}

/* 模块信息提示样式 */
/deep/.module-info-tooltip {
  max-width: 300px;
  font-family: Inter, sans-serif;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .module-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
