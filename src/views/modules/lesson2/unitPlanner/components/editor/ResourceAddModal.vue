<template>
  <el-dialog
    width="800px"
    :title="isEditMode ? 'Edit Resource' : 'Add Resources'"
    :visible.sync="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="resource-form-dialog"
    @close="$emit('cancel')"
  >
    <el-form :model="newResourceForm" ref="addResourceForm" label-position="top" :rules="rules">
      <el-form-item :label="$t('loc.resourceTitle')" prop="sourceKeywords">
        <el-input
          v-model="newResourceForm.sourceKeywords"
          :placeholder="$t('loc.resourceTitlePlaceholder')"
          maxlength="200"
          show-word-limit
          class="font-size-16"
          @input="limitInputLength"
        ></el-input>
      </el-form-item>

      <el-form-item v-if="!isEditMode" :label="$t('loc.resourceType')">
        <el-radio-group v-model="newResourceForm.resourceType" @change="handleResourceTypeChange">
          <el-radio label="URL">{{ $t('loc.URL') }}</el-radio>
          <el-radio label="File">{{ $t('loc.File') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- URL资源输入 -->
      <el-form-item v-if="equalsIgnoreCase(newResourceForm.resourceType, 'URL')" label="Resource URL" prop="sourceLink" required>
        <el-input
          v-model="newResourceForm.sourceLink"
          :placeholder="$t('loc.resourceURLPlaceholder')">
          <i slot="prefix" class="el-icon-link btn-link"></i>
        </el-input>
      </el-form-item>

      <!-- 文件上传 -->
      <el-form-item v-if="equalsIgnoreCase(newResourceForm.resourceType, 'File')" label="Upload Files" required>
        <div
          class="upload-container"
          ref="uploadContainer"
          :class="{ 'dragover': isDragging }"
          @dragover.prevent="handleDragover"
          @dragleave.prevent="handleDragleave"
          @drop.prevent="handleDrop"
          @click="triggerFileInput"
        >
          <div class="upload-area">
            <div class="upload-icon">
              <i class="el-icon-upload"></i>
            </div>
            <div class="upload-text">
              {{ $t('loc.dropFilesHere') }}
            </div>
            <el-button type="primary" size="small" class="browse-btn" @click.stop="triggerFileInput">{{ $t('loc.browseFiles') }}</el-button>
            <input
              type="file"
              ref="fileInput"
              style="display: none"
              multiple
              accept=".xls,.xlsx,.doc,.docx,.ppt,.pptx,.pdf"
              @change="handleFileInputChange"
            />
          </div>
        </div>

        <!-- 上传文件列表 -->
        <div v-if="fileList.length > 0" class="file-list">
          <div v-for="(file, index) in fileList" :key="index" class="file-item">
            <div class="file-icon">
              <img :src="getFileIconClass(file.name)" alt="File Icon">
            </div>
            <div class="add-file-info">
              <div class="file-name-area">
                <div class="file-name">{{ file.name }}</div>
                <div v-if="file.percentage === 100" class="file-status" @click="removeFile(index)" @mouseenter="showDeleteIcon = index" @mouseleave="showDeleteIcon = -1">
                  <i class="font-size-16" :class="showDeleteIcon === index ? 'el-icon-close font-color-black' : 'el-icon-circle-check'"></i>
                </div>
              </div>
              <div v-if="file.percentage < 100" class="file-progress-row">
                <div class="flex-1 lg-pt-10">
                  <el-progress :stroke-width="3" :percentage="file.percentage" :show-text="false"></el-progress>
                </div>
                <div class="file-status" @mouseenter="showDeleteIcon = index" @mouseleave="showDeleteIcon = -1">
                  <i v-if="showDeleteIcon === index" class="el-icon-close l-h-0x font-size-16 font-color-black"  @click="removeFile(index)"></i>
                  <div v-else class="progress-text">{{ file.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" class="cancel-form-btn">{{ $t('loc.resourceAddCancel') }}</el-button>
      <el-button
        type="primary"
        @click="saveNewResource"
        class="save-form-btn"
        :disabled="isAddResourceButtonDisabled">{{ isEditMode ? 'Update Resource' : 'Add Resources' }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import FileUtils  from '@/utils/file'
import { equalsIgnoreCase } from '@/utils/common'

export default {
  name: 'ResourceAddModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    currentResourceCount: {
      type: Number,
      default: 0
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    resource: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: this.value,
      showDeleteIcon: -1, // 控制显示哪个文件的删除图标
      newResourceForm: {
        sourceKeywords: '',
        sourceLink: '',
        resourceType: 'URL'
      },
      rules: {
        sourceLink: [
          { validator: this.validateUrl, trigger: 'blur' }
        ],
        sourceKeywords: [
          { max: 200, message: this.$t('loc.titleCannotExceed200Characters'), trigger: 'blur' }
        ]
      },
      fileList: [],
      isDragging: false,
      uploadingFiles: false,
      progressIntervals: {}, // 存储进度条定时器
      maxPreUploadProgress: 80, // 上传前最大进度（三分之二位置）
      maxFileLimit: 1, // 最大允许的文件数量
      addFileNum: 0 // 临时上传但尚未保存的文件数量
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val && this.isEditMode && this.resource) {
        // 如果是编辑模式，则将传入的资源数据填充到表单中
        this.initEditMode()
      }
    },
    visible(val) {
      this.$emit('input', val)
      if (!val) {
        // 清空表单和文件列表
        this.resetForm()
      } else {
        // 打开弹窗时，确保重置表单验证状态
        this.$nextTick(() => {
          if (this.$refs.addResourceForm) {
            this.$refs.addResourceForm.clearValidate()
          }
        })
      }
    }
  },
  created() {
    this.initEditMode()
  },
  computed: {
    // 判断添加资源按钮是否应该禁用
    isAddResourceButtonDisabled() {

      // 如果选择的是文件类型，则检查是否有文件正在上传
      if (equalsIgnoreCase(this.newResourceForm.resourceType, 'File')) {

        // 检查是否所有文件都已上传完成（进度为100%）
        return this.fileList.some(file => file.percentage < 100);
      }

      return false;
    },
    // 当前剩余可上传文件数量
    remainingFileCount() {
      return this.maxFileLimit - this.currentResourceCount - this.addFileNum;
    }
  },
  methods: {
    equalsIgnoreCase,
    limitInputLength(val) {
      if (val && val.length > 200) {
        // 直接截断到200字符
        this.newResourceForm.sourceKeywords = val.slice(0, 200);
      }
    },
    closeDialog() {
      this.visible = false
      this.$emit('cancel')
    },
    resetForm() {
      this.newResourceForm = {
        sourceKeywords: '',
        sourceLink: '',
        resourceType: 'URL'
      }
      this.fileList = []
      this.clearAllProgressIntervals()
      this.addFileNum = 0

      // 重置表单验证状态
      if (this.$refs.addResourceForm) {
        this.$refs.addResourceForm.clearValidate()
      }
    },
    initEditMode() {
      if (!this.resource) return

      this.newResourceForm = {
        sourceKeywords: this.resource.sourceKeywords || '',
        sourceLink: this.resource.sourceLink || '',
        resourceType: equalsIgnoreCase(this.resource.type, 'File') ? 'File' : 'URL'
      }
      // 如果是文件类型，需要初始化文件列表
      if (equalsIgnoreCase(this.resource.type, 'File') && this.resource.fileName) {
        // 创建一个已上传完成的文件对象
        const fileItem = {
          name: this.resource.fileName,
          percentage: 100,
          status: 'success',
          url: this.resource.sourceLink,
        }
        this.fileList = [fileItem]
      }
    },
    saveNewResource() {
      // 根据资源类型进行不同的验证
      if (equalsIgnoreCase(this.newResourceForm.resourceType, 'URL')) {
        this.$refs.addResourceForm.validate((valid) => {
          if (valid) {
            this.handleSaveResource()
          }
        })
      } else {
        // 文件类型资源
        if (this.fileList.length === 0) {
          this.$message.error(this.$t('loc.pleaseUploadFile'))
          return
        }

        if (this.fileList.length > 1) {
          this.$message.error(this.$t('loc.uploadFileMaxNumberFour'))
          return
        }

        // 标题可选，但如果有值，需要验证长度
        if (this.newResourceForm.sourceKeywords && this.newResourceForm.sourceKeywords.length > 200) {
          return
        }

        this.handleSaveResource()
      }
    },
    handleSaveResource() {
      // 设置状态为正在保存
      this.uploadingFiles = true;

      const resources = []
      const sourceKeywords = this.newResourceForm.sourceKeywords.trim()
      if (equalsIgnoreCase(this.newResourceForm.resourceType, 'URL')) {
        // 判断是否修改了 url，如果修改了 url 则按照用户添加资源处理
        if (this.isEditMode && this.resource && this.resource.sourceLink !== this.newResourceForm.sourceLink) {
          this.resource.cover = '';
          this.resource.videoLink = '';
          this.resource.source = '';
        }
        // URL类型资源
        const newResource = {
          sourceKeywords: sourceKeywords,
          sourceLink: this.newResourceForm.sourceLink.trim(),
          source: this.resource && this.resource.source ? this.resource.source : '',
          videoLink: this.resource && this.resource.videoLink ? this.resource.videoLink : '',
          cover: this.resource && this.resource.cover ? this.resource.cover : '',
          hidden: false,
          type: this.isEditMode ? this.resource.type : 'URL',
          noMatchSubscript: true,
          // 如果是编辑模式，保留原有资源的ID和角标
          subscript: this.isEditMode && this.resource && this.resource.subscript ? this.resource.subscript : undefined
        }
        resources.push(newResource)
      } else {
        // 文件类型资源
        const successFiles = this.fileList.filter(file => equalsIgnoreCase(file.status, 'success') && file.url)

        // 检查是否有成功上传的文件
        if (successFiles.length === 0) {
          this.$message.error('No files were successfully uploaded. Please try again.')
          this.uploadingFiles = false; // 重置状态
          return;
        }

        // 在编辑模式下，我们只使用第一个文件
        const fileToUse = this.isEditMode ? [successFiles[0]] : successFiles;

        fileToUse.forEach(file => {
          const newResource = {
            sourceKeywords: sourceKeywords,
            sourceLink: file.url,
            source: this.resource && this.resource.source ? this.resource.source : '',
            fileName: file.name,
            videoLink: this.resource && this.resource.videoLink ? this.resource.videoLink : '',
            cover: this.resource && this.resource.cover ? this.resource.cover : '',
            hidden: false,
            type: this.isEditMode ? this.resource.type : 'File',
            noMatchSubscript: true,
            // 如果是编辑模式，保留原有资源的ID和角标
            subscript: this.isEditMode && this.resource && this.resource.subscript ? this.resource.subscript : undefined
          }
          resources.push(newResource)
        })
      }

      // 清理所有进度条定时器
      this.clearAllProgressIntervals();

      // 发送添加的资源给父组件
      this.$emit(this.isEditMode ? 'update-resource' : 'add-resources', resources)

      // 重置表单并关闭弹窗
      this.resetForm()
      this.uploadingFiles = false; // 重置状态
      this.visible = false
    },
    validateUrl(rule, value, callback) {
      if (!value  || value.trim() === '')  {
        callback(new Error(this.$t('loc.validURLError')))
        return
      }
      // 使用正则表达式验证 URL 格式
      const urlPattern = /^https?:\/\/([\w-]+\.)+[\w-]+(\/[^"<>]*)?/

      // 去除URL前后空格
      value = value.trim()

      if (!urlPattern.test(value)) {
        callback(new Error(this.$t('loc.validURLError')))
        return
      }

      // 验证通过
      callback()
    },
    handleResourceTypeChange(type) {
      // 切换资源类型时重置相关字段
      if (equalsIgnoreCase(type, 'URL')) {
        this.fileList = []
        if (this.isEditMode && this.resource) {
          this.newResourceForm.sourceLink = ''
        }
      } else {
        this.newResourceForm.sourceLink = ''
      }

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.addResourceForm) {
          this.$refs.addResourceForm.clearValidate()
        }
      })
    },
    handleFileInputChange(event) {
      const files = Array.from(event.target.files || [])

      // 在编辑模式下，如果已经有文件，则先清除之前的文件
      if (this.isEditMode && this.fileList.length > 0) {
        this.fileList = []
        this.addFileNum = 0
      }

      this.processFiles(files)
      // 清空 input，允许选择相同文件再次触发 change 事件
      this.$refs.fileInput.value = ''
    },
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
    // 删除文件方法
    removeFile(index) {
      if (index >= 0 && index < this.fileList.length) {
        const file = this.fileList[index]

        // 清除该文件的进度定时器
        if (this.progressIntervals[file.uid]) {
          clearInterval(this.progressIntervals[file.uid])
          delete this.progressIntervals[file.uid]
        }

        // 从文件列表中移除
        this.fileList.splice(index, 1)
        this.addFileNum--

        // 重置悬停状态
        this.showDeleteIcon = -1

        // 显示删除成功提示
        this.$message({
          type: 'success',
          message: this.$t('loc.removedSuccessfully')
        })
      }
    },

    processFiles(files) {
      if (!files) {
        return
      }

      // 检查文件总数 (当前保存的文件数 + 临时文件数 + 新选择的文件数)
      if (!this.isEditMode &&  this.addFileNum + files.length > this.maxFileLimit) {
        this.$message.error(this.$t('loc.uploadFileMaxNumberFour'))
        return
      }

      // 在编辑模式下，只处理第一个文件
      const filesToProcess = this.isEditMode ? [files[0]] : files;

      // 允许的文件扩展名
      const allowedExtensions = ['xls', 'xlsx', 'pdf', 'ppt', 'pptx', 'doc', 'docx']

      filesToProcess.forEach(file => {
        // 检查文件大小（10MB限制）
        if (file.size > 10 * 1024 * 1024) {
          this.$message.error(`Sorry, your upload file exceeds 10MB limit.`)
          return
        }


        // 检查文件类型
        const fileExt = file.name.split('.').pop().toLowerCase()
        if (!allowedExtensions.includes(fileExt)) {
          this.$message.error(this.$t('loc.uploadFileTip'))
          return
        }

        // 创建文件项
        const fileItem = {
          name: file.name,
          size: file.size,
          percentage: 0,
          status: 'uploading',
          uid: Date.now() + Math.random().toString(36).substring(2, 10),
          raw: file // 保存原始文件对象
        }

        this.fileList.push(fileItem)
        // 增加临时文件计数
        this.addFileNum++

        // 开始模拟进度
        this.startProgressSimulation(fileItem.uid)

        // 立即上传文件
        this.uploadSingleFile(fileItem)
      })
    },
    // 启动进度条模拟
    startProgressSimulation(uid) {
      // 清除可能存在的旧定时器
      if (this.progressIntervals[uid]) {
        clearInterval(this.progressIntervals[uid])
      }

      // 设置初始进度
      const index = this.fileList.findIndex(item => equalsIgnoreCase(item.uid, uid))
      if (index !== -1) {
        this.fileList[index].percentage = 0
      }

      // 初始阶段 - 随机增长到三分之二
      this.simulatePreUploadProgress(uid)
    },
    // 模拟上传前进度（随机增长到三分之二）
    simulatePreUploadProgress(uid) {
      this.progressIntervals[uid] = setInterval(() => {
        const index = this.fileList.findIndex(item => equalsIgnoreCase(item.uid, uid))
        if (index === -1) {
          clearInterval(this.progressIntervals[uid])
          return
        }

        const currentPercentage = this.fileList[index].percentage
        if (currentPercentage < this.maxPreUploadProgress) {
          // 剩余可增长空间
          const remainingProgress = this.maxPreUploadProgress - currentPercentage
          // 随机增量，接近目标时增量变小
          const randomIncrement = Math.random() * (remainingProgress / 8) + 0.5
          // 新进度
          const newPercentage = Math.min(this.maxPreUploadProgress, currentPercentage + randomIncrement)

          this.fileList[index].percentage = Math.round(newPercentage)
        } else {
          // 到达三分之二位置，暂停增长
          clearInterval(this.progressIntervals[uid])
        }
      }, 200)
    },
    // 模拟上传后进度（从三分之二到完成）
    simulatePostUploadProgress(uid) {
      if (this.progressIntervals[uid]) {
        clearInterval(this.progressIntervals[uid])
      }

      this.progressIntervals[uid] = setInterval(() => {
        const index = this.fileList.findIndex(item => equalsIgnoreCase(item.uid, uid))
        if (index === -1) {
          clearInterval(this.progressIntervals[uid])
          return
        }

        const currentPercentage = this.fileList[index].percentage
        if (currentPercentage < 100) {
          // 剩余可增长空间
          const remainingProgress = 100 - currentPercentage
          // 随机增量，比前段更快
          const randomIncrement = Math.random() * (remainingProgress / 3) + 1
          // 新进度
          const newPercentage = Math.min(100, currentPercentage + randomIncrement)

          this.fileList[index].percentage = Math.round(newPercentage)

          // 到达100%，清除定时器
          if (this.fileList[index].percentage >= 100) {
            clearInterval(this.progressIntervals[uid])
            // 触发状态更新，确保计算属性重新计算
            this.$forceUpdate()
          }
        } else {
          clearInterval(this.progressIntervals[uid])
        }
      }, 150) // 更快的更新频率
    },
    // 清除所有进度条定时器
    clearAllProgressIntervals() {
      Object.keys(this.progressIntervals).forEach(uid => {
        clearInterval(this.progressIntervals[uid])
      })
      this.progressIntervals = {}
    },
    uploadSingleFile(fileItem) {
      // 标记正在上传
      this.uploadingFiles = true;

      // 创建 FormData 对象
      const formData = new FormData()
      formData.append('file', fileItem.raw)

      // 获取文件类型
      const fileExt = fileItem.name.split('.').pop().toLowerCase()
      formData.append('type', fileExt) // 添加文件类型

      // 设置请求配置
      const configs = {
        headers: { 'Content-Type': 'multipart/form-data' }
      }

      // 使用axios发送请求
      this.$axios.post($api.urls().uploadFile, formData, configs)
        .then(response => {
          // 上传成功处理

          // 检查response是否有效
          if (!response || typeof response !== 'object') {
            this.uploadingFiles = false; // 重置上传状态
            return
          }

          // 根据axios的响应结构，数据应该在response.data中
          const res = response.data || response

          // 更新文件状态
          const index = this.fileList.findIndex(item => equalsIgnoreCase(item.uid, fileItem.uid))
          if (index !== -1) {
            this.fileList[index].status = 'success'

            // 提取URL和ID
            let fileUrl = ''
            let fileId = ''

            if (res && typeof res === 'object') {
              if (res.data && typeof res.data === 'object') {
                fileUrl = res.data.url || res.data.public_url || res.data.fileUrl || ''
                fileId = res.data.id || res.data.fileId || ''
              } else {
                fileUrl = res.url || res.public_url || res.fileUrl || ''
                fileId = res.id || res.fileId || ''
              }
            }

            this.fileList[index].url = fileUrl
            this.fileList[index].id = fileId

            // 如果获取到 URL，启动后半段进度模拟
            if (fileUrl) {
              this.simulatePostUploadProgress(fileItem.uid)
            } else {
              this.$message.error('Failed to get file URL from server response')
            }
          }

          // 检查是否所有文件都已上传完成
          const stillUploading = this.fileList.some(file => equalsIgnoreCase(file.status, 'uploading'));
          if (!stillUploading) {
            this.uploadingFiles = false; // 所有文件上传完成，重置状态
          }
        })
        .catch(error => {
          // 上传失败处理
          let errorMessage = 'Network error during upload'

          if (error.response && error.response.data) {
            errorMessage = error.response.data.error_message || error.response.data.message || errorMessage
          }

          // 清除该文件的进度定时器
          if (this.progressIntervals[fileItem.uid]) {
            clearInterval(this.progressIntervals[fileItem.uid])
            delete this.progressIntervals[fileItem.uid]
          }

          // 标记文件上传失败
          const index = this.fileList.findIndex(item => equalsIgnoreCase(item.uid, fileItem.uid))
          if (index !== -1) {
            this.fileList[index].status = 'error'
            this.fileList[index].percentage = 0 // 重置进度
          }



          // 检查是否所有文件都已上传完成
          const stillUploading = this.fileList.some(file => equalsIgnoreCase(file.status, 'uploading'));
          if (!stillUploading) {
            this.uploadingFiles = false; // 重置上传状态
          }
        })
    },
    getFileIconClass(fileName) {
      return FileUtils.getFileType(fileName)
    },
    handleDragover(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragging = true
    },
    handleDragleave(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragging = false
    },
    handleDrop(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragging = false

      const files = Array.from(e.dataTransfer.files || [])
      this.processFiles(files)
    }
  },
  // 组件销毁时清理所有定时器
  beforeDestroy() {
    this.clearAllProgressIntervals()
  }
}
</script>

<style lang="less" scoped>
/deep/ .resource-form-dialog {
  .el-dialog__body {
    padding: 0 20px;
    font-size: 16px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 0;
  }
  .el-form-item {
    margin-bottom: 0px !important;
    padding-bottom: 10px !important;
  }
  .el-form-item__label {
    font-weight: 600;
    padding-bottom: 0px;
    font-size: 16px;
    line-height: 24px;
  }
  .el-radio__label {
    font-size: 16px !important;
  }
  .font-size-16 .el-input__inner {
    padding-right: 70px;
  }
  .el-input__prefix {
    left: 12px;
    .btn-link:hover {
      color: inherit;
      text-decoration: none;
      border-color: none;
    }
  }
  margin-top: 5vh !important;
  color: #111c1c
}

.save-form-btn {
  background-color: #10b3b7;
  border-color: #10b3b7;
  padding: 10px 20px;
}

.save-form-btn:hover {
  background-color: #0d9b9f;
  border-color: #0d9b9f;
}

.cancel-form-btn {
  padding: 10px 20px;
}

/deep/ .el-form-item.is-required .el-form-item__label:before {
  color: #ff4949;
  content: "*";
  margin-right: 4px;
}

.upload-container {
  border: 2px dashed #10b3b7;
  border-radius: 6px;
  background-color: rgba(16, 179, 183, 0.05);
  padding: 0;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-container.dragover {
  border-color: #10b3b7;
  background-color: rgba(16, 179, 183, 0.15);
  transform: scale(1.01);
  box-shadow: 0 0 10px rgba(16, 179, 183, 0.2);
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
  width: 100%;
}

.upload-icon {
  font-size: 30px;
  margin-bottom: 15px;
  color: #10b3b7;
  background-color: rgba(16, 179, 183, 0.1);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-text {
  font-size: 16px;
  text-align: center;
  margin-bottom: 15px;
}

.browse-btn {
  background-color: #10b3b7;
  border-color: #10b3b7;
  padding: 8px 15px;
  font-size: 14px;
}

.browse-btn:hover {
  background-color: #0d9b9f;
  border-color: #0d9b9f;
}

.file-list {
  margin-top: 15px;
}

.file-item {
  display: flex;
  align-items: flex-start;
  padding: 3px 10px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #f9f9f9;
  position: relative;
}

.file-icon {
  margin-right: 10px;
  width: 25px;
  height: 50px;
  display: flex;
  align-items: center;
}

.file-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.add-file-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: calc(100% - 90px);
  padding-top: 18px;
  line-height: 10px;
}

.file-name-area {
  display: flex;
  justify-content: space-between;
}

.file-name {
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 90%;
}

.file-progress-row {
  display: flex;
  align-items: center;
  width: 100%;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  max-width: 34px;
  text-align: right;
}

.file-status {
  color: #67c23a;
  cursor: pointer;
  transition: all 0.3s;
}

.file-status:hover {
  transform: scale(1.1);
}


</style>