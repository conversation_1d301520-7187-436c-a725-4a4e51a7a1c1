<template>
    <!-- 礼花效果的组件 -->
    <div>
      <canvas ref="confettiCanvas" class="confetti-canvas"></canvas>
    </div>
  </template>
  
  <script>
  import confetti from 'canvas-confetti'
  
  export default {
    name: 'ConfettiEffect',
    data() {
      return {
        confettiInstance: null, // 礼花实例
        confettiInterval: null // 礼花定时器
      }
    },
    beforeDestroy() {
      // 组件销毁时清理礼花效果
      this.clearConfetti()
    },
    methods: {
      // 初始化礼花效果
      initConfetti() {
        const canvas = this.$refs.confettiCanvas
        if (canvas) {
          this.confettiInstance = confetti.create(canvas, {
            resize: true, // 自动适配窗口大小
            useWorker: true
          })
        }
      },
      
      // 启动礼花效果
      launchConfetti() {
        // 检查并重新初始化礼花实例
        if (!this.confettiInstance) {
          this.initConfetti()
        }
        
        if (!this.confettiInstance) {
          return
        }
        
        // 礼花效果持续时间
        const duration = 5 * 1000
        // 礼花效果结束时间
        const animationEnd = Date.now() + duration
        // 礼花效果默认配置
        const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 }
        // 礼花效果随机配置
        let x01 = this.randomInRange(0.1, 0.3)
        let x02 = this.randomInRange(0.7, 0.9)
        let tempConfetti = this.confettiInstance
        
        // 礼花效果定时器，每隔 250ms 发送一次礼花效果
        this.confettiInterval = setInterval(() => {
          // 礼花效果剩余时间
          const timeLeft = animationEnd - Date.now()
          if (timeLeft <= 0) {
            return clearInterval(this.confettiInterval)
          }
          // 礼花效果粒子数量
          const particleCount = 50 * (timeLeft / duration)
          // 礼花效果粒子位置
          tempConfetti({ ...defaults, particleCount, origin: { x: x01, y: Math.random() - 0.2 } })
          // 礼花效果粒子位置
          tempConfetti({ ...defaults, particleCount, origin: { x: x02, y: Math.random() - 0.2 } })
        }, 250)
      },
      
      // 清除礼花效果
      clearConfetti() {
        if (this.confettiInstance) {
          if (this.confettiInterval) {
            clearInterval(this.confettiInterval)
            this.confettiInterval = null
          }
          this.confettiInstance.reset()
          // 将实例置空，下次启动时重新初始化
          this.confettiInstance = null
        }
      },
      
      // 随机数生成
      randomInRange(min, max) {
        return Math.random() * (max - min) + min
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .confetti-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 9999; /* 高于 el-dialog 默认 z-index 即可 */
  }
  </style>