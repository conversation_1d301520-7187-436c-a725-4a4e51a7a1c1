<template>
  <div class="display-flex flex-direction-col lesson-overview-detail">
    <div class="preview-container" style="display: flex; justify-content: flex-end;margin-top:-51px;margin-bottom:15px">
      <!--用于放 preview 按钮-->
      <slot name="preview"></slot>
    </div>

    <el-card class="w-full lg-scrollbar small-large-group" style="box-shadow: unset!important;">
      <div id="lessonOverviewDetailHeader">
        <div class="week-theme font-size-16 font-bold lg-margin-bottom-16 display-flex">
          <span>{{$t('loc.unitPlannerStep1WeeklyTheme')}}</span>
          <i class="lg-icon lg-icon-arrow-up lg-margin-left-12 lg-pointer"
              :class="themeCollapsed ? 'lg-icon-arrow-up': 'lg-icon-arrow-down'"
              @click="themeCollapsed = !themeCollapsed"></i>
        </div>
        <el-collapse-transition>
          <div v-show="!themeCollapsed">
            <table class="week-table lg-margin-bottom-16">
              <tr>
                <td>{{$t('loc.unitPlannerStep2Theme')}}</td>
                <td>{{ currentWeeklyPlan && currentWeeklyPlan.theme }}</td>
              </tr>
              <tr>
                <td>{{$t('loc.unitPlannerStep2Overview')}}</td>
                <td> {{ currentWeeklyPlan && currentWeeklyPlan.overview}}</td>
              </tr>
              <tr>
                <td>{{$t('loc.standardsOrMeasures')}}</td>
                <td>
                  <!-- 周的所有测评点 -->
                  <div class="lg-margin-top-12">
                    <el-skeleton :rows="3" animated
                                 :loading="weeklyStandardsLoading">
                      <template>
                        <span v-for="(measure, index) in weeklyStandards" :key="index">
                          <el-tooltip popper-class="max-width-400" placement="top" :open-delay="300">
                            <div slot="content">{{ measureNameDescription(measure) }}</div>
                            <el-tag type="info" size="small" class="m-r-sm m-b-sm lg-pointer">
                              {{ measure }}
                            </el-tag>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-skeleton>
                  </div>
                </td>
              </tr>
              <tr v-if="(weeklyStandardsLoading && rubricsOptions.length > 0) || weeklyRubrics && weeklyRubrics.length > 0">
                <td>{{$t('loc.unitPlannerStep2Rubrics')}}</td>
                <td>
                  <!-- 周的所有校训 -->
                  <div class="lg-margin-top-12">
                    <el-skeleton :rows="1" animated :loading="weeklyStandardsLoading">
                      <template>
                        <span v-for="(rubric, index) in weeklyRubrics" :key="index">
                          <el-tooltip popper-class="max-width-400" placement="top" :open-delay="500">
                            <div slot="content" v-html="getDisplayRubrics(rubric)"></div>
                            <el-tag type="info" size="small" class="m-r-sm m-b-sm lg-pointer">
                              {{ rubric.title }}
                            </el-tag>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-skeleton>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </el-collapse-transition>
      </div>
      <!-- 每天的课程概览 -->
      <div class="display-flex justify-content-between align-items lg-margin-bottom-16">
        <span v-if="showSmallAndLargeGroupFlag"  class="week-theme font-size-16 font-bold">{{$t('loc.unitPlannerStep3LargeOrSmallGroup')}}</span>
        <span v-else  class="week-theme font-size-16 font-bold">{{$t('loc.lessons')}}</span>
        <div>
          <slot name="feedback"></slot>
          <!-- 重新生成 -->
          <el-tooltip effect="dark" content="Regenerate All" placement="top" :hide-after="1000">
            <el-button type="primary" plain style="width: 36px;height: 36px;padding: 10px;"
              icon="el-icon-refresh-right font-bold"
                       v-if="!isCurriculumPlugin"
              :loading="generateLessonIdeaLoading || loading" @click="regenerateAll(false)">
            </el-button>
          </el-tooltip>
          <!-- 确认全部 -->
          <!-- <el-button @click="confirmAll(false)" type="primary"
            icon="lg-icon lg-icon-check"
            :loading="generateLessonIdeaLoading || loading"
            :disabled="allLessonOverviewConfirmed || generatingLessonIdea || generatingCenterIdea">{{$t('loc.unitPlannerStep3ConfirmAll')}}</el-button> -->
        </div>
      </div>
      <el-row :gutter="24">
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-for="(overview, index) in lessonOverviews" :key="index">
          <!-- 每天的课程概览 -->
          <div ref="lessonOverview" :tabindex="index" class="lesson-overview-card lg-margin-bottom-24" @click="handleLessonIdeaCardClickEvent(overview)">
            <!-- 周几 -->
              <div class="title-font-20 text-center lg-padding-t-b-8 week-col" :class="`week-${index % 5 + 1}`">
                  <el-skeleton :rows="1" animated :loading="loading && !(overview.day || overview.activitySortNum)">
                      <template v-if="overview.activitySortNum">
                        {{$t('loc.lesson')}} {{ overview.activitySortNum }}
                      </template>
                      <template v-else>
                          {{ overview.day }}
                      </template>
                  </el-skeleton>
              </div>
              <!-- 课程详情 -->
              <div class="lg-padding-12 lesson-overview-box">
                  <div class="lesson-overview-content lg-scrollbar">
                      <!-- 课程标题 -->
                      <div>
                          <el-skeleton :rows="1" animated :loading="(loading && !overview.title) || !!overview.generateLoading">
                              <template>
                                  <div class="title-font-16">
                                    {{ overview.title }}
                                  </div>
                              </template>
                          </el-skeleton>
                      </div>
                      <div>
                          <!-- 测评点 -->
                          <div class="lg-margin-top-12">
                              <el-skeleton :rows="1" animated
                                  :loading="(loading && !overview.measures) || !!overview.generateLoading">
                                  <template>
                                    <span v-for="(measure, index) in overview.measures" :key="index">
                                        <el-tooltip popper-class="max-width-400" placement="top" :open-delay="300">
                                            <div slot="content">{{ measureNameDescription(measure) }}</div>
                                            <el-tag type="info" size="small" class="m-r-sm m-b-sm">
                                                {{ measure }}
                                            </el-tag>
                                        </el-tooltip>
                                    </span>
                                    <span v-for="(rubric, index) in flattenRubrics(overview.rubrics)" :key="index">
                                      <el-tooltip placement="top" popper-class="max-width-400" :open-delay="500">
                                        <div slot="content" v-html="getDisplayRubrics(rubric)"></div>
                                        <el-tag size="small" plain class="m-r-sm m-b-sm">
                                          {{ rubric.title }}
                                        </el-tag>
                                      </el-tooltip>
                                    </span>
                                  </template>
                              </el-skeleton>
                          </div>
                          <!-- 描述 -->
                          <div class="lg-margin-top-12">
                              <el-skeleton :rows="4" animated
                                  :loading="(loading && !overview.description) || !!overview.generateLoading">
                                  <template>
                                    <div class="lessson-overview-desc">{{ overview.description }}</div>
                                  </template>
                              </el-skeleton>
                          </div>
                      </div>
                  </div>
                  <!-- 课程类型和操作按钮 -->
                  <div class="display-flex justify-content-between align-items lg-margin-top-12">
                      <!-- 课程类型 -->
                      <div>
                          <el-skeleton :rows="1" animated
                              :loading="(loading && !overview.activityType) || !!overview.generateLoading"
                              v-if="showSmallAndLargeGroupFlag">
                              <template>
                                  <!-- <span>{{ overview.activityType }}</span> -->
                                  <el-dropdown @command="changeActivityType" :disabled="overview.generateLoading || loading || generatingLessonIdea || generatingCenterIdea">
                                      <span class="lg-pointer lg-color-primary" @click.stop>
                                          {{ groupTypeConvert(overview.activityType) }}<i class="el-icon-arrow-down el-icon--right"></i>
                                      </span>
                                      <el-dropdown-menu slot="dropdown">
                                          <el-dropdown-item :command="{overview:overview, flag:true}">{{$t('loc.unitPlannerStep3SmallGroup')}}</el-dropdown-item>
                                          <el-dropdown-item :command="{overview:overview, flag:false}">{{$t('loc.unitPlannerStep3LargeGroup')}}</el-dropdown-item>
                                      </el-dropdown-menu>
                                  </el-dropdown>
                              </template>
                          </el-skeleton>
                      </div>
                      <!-- 操作 -->
                      <div>
                          <div v-show="!loading && !overview.generateLoading && !overview.generating">
                              <!-- 编辑 -->
                              <el-tooltip effect="dark" :content="$t('loc.unitPlannerWeeklyEdit')" placement="top" :hide-after="1000">
                                <el-button class="icon-opt-button" type="text" @click.stop="edit(overview)" :disabled="generatingLessonIdea || generatingCenterIdea">
                                    <i class="lg-icon lg-icon-edit text-black-mon font-size-20"></i>
                                </el-button>
                              </el-tooltip>
                              <!-- 重新生成 -->
                              <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top" :popper-options="popperOptions" :hide-after="1000">
                                <el-button class="icon-opt-button" type="text" @click.stop="regenerateLessonOverview(overview, overview.day, index)" :disabled="generatingLessonIdea || generatingCenterIdea">
                                  <template #icon>
                                    <i class="el-icon-refresh-right text-black-mon font-size-20"></i>
                                  </template>
                                </el-button>
                              </el-tooltip>
                              <!-- 确认课程 idea -->
<!--                              <el-button type="primary" size="medium" v-show="!overview.confirmed" :disabled="generatingLessonIdea || generatingCenterIdea"-->
<!--                                  @click.stop="confirm(overview)">{{$t('loc.unitPlannerStep3Confirm')}}-->
<!--                              </el-button>-->
                              <!-- 生成详情 -->
                              <el-button type="primary"  size="medium" v-show="overview.confirmed" :disabled="generatingLessonIdea || generatingCenterIdea || batchGenerateLoading"
                                   :loading="batchGenerateLoading"
                                   @click.stop="overview.lessonId ? viewDetail(overview) : generateDetail(overview)"> {{$t('loc.viewLesson')}}
                        </el-button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
        </el-col>
      </el-row>
      <!-- Centers 活动概览 -->
      <div v-if="showCenters" class="w-full add-margin-b-16">
        <div v-show="!isMobile" class="display-flex justify-content-between align-items">
          <span class="week-theme font-size-16 font-bold">{{kToGrade2 ? $t('loc.unitPlannerStep3Stations') : $t('loc.unitPlannerStep3Centers')}}</span>
          <div>
            <!-- Centers 组设置 -->
            <el-button @click="openCenterSettingModal">
              <i class="lg-icon lg-icon-settings"></i>&nbsp;{{$t('loc.unitPlannerStep3Settings')}}</el-button>
            <el-button v-show="centerOverviews.length == 0" type="primary"
                       :disabled="loading || generateCenterOverviewDisabled || generatingLessonIdea || generatingCenterIdea || generateCenterOverviewLoading"
                       @click="openGenerateCenterModal">
              <i class="lg-icon lg-icon-generate"></i>{{ kToGrade2 ? $t('loc.unitPlannerStep3GenerateStationIdeas') : $t('loc.unitPlannerStep3GenerateCenterIdeas')}}</el-button>
            <!-- 重新生成 -->
            <el-tooltip effect="dark" content="Regenerate All" placement="top" :hide-after="1000">
              <el-button type="primary" v-show="centerOverviews.length > 0" plain
                         icon="el-icon-refresh-right font-bold"
                         v-if="!isCurriculumPlugin"
                         style="width: 36px;height: 36px;padding: 10px"
                         :loading="generateCenterOverviewLoading || generatingLessonIdea || generatingCenterIdea"
                         @click="regenerateAll(true)">
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div v-show="isMobile" class="display-flex justify-content-between align-items centers-header">
          <div class="display-flex align-items centers-title-wrap">
            <span class="week-theme font-size-16 font-bold">{{kToGrade2 ? $t('loc.unitPlannerStep3Stations') : $t('loc.unitPlannerStep3Centers')}}</span>
            <!-- Centers 组设置 -->
            <el-button class="centers-settings-btn" @click="openCenterSettingModal">
              <i class="lg-icon lg-icon-settings"></i>&nbsp;{{$t('loc.unitPlannerStep3Settings')}}</el-button>
          </div>
          <div class="display-flex align-items gap-2 centers-action-wrap">
            <el-button v-show="centerOverviews.length == 0" type="primary"
                       :disabled="loading || generateCenterOverviewDisabled || generatingLessonIdea || generatingCenterIdea || generateCenterOverviewLoading"
                       @click="openGenerateCenterModal">
              <i class="lg-icon lg-icon-generate"></i>{{ kToGrade2 ? $t('loc.unitPlannerStep3GenerateStationIdeas') : $t('loc.unitPlannerStep3GenerateCenterIdeas')}}</el-button>
            <!-- 重新生成 -->
            <el-tooltip effect="dark" content="Regenerate All" placement="top" :hide-after="1000">
              <el-button type="primary" v-show="centerOverviews.length > 0" plain
                         icon="el-icon-refresh-right font-bold"
                         v-if="!isCurriculumPlugin"
                         style="width: 36px;height: 36px;padding: 10px"
                         :loading="generateCenterOverviewLoading || generatingLessonIdea || generatingCenterIdea"
                         @click="regenerateAll(true)">
              </el-button>
            </el-tooltip>
          </div>
        </div>

        <div v-show="centerOverviews.length > 0" class="lg-padding-top-20">
          <el-row :gutter="24">
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-for="(overview, index) in centerOverviews" :key="index">
              <div ref="centerOverview" :tabindex="index" class="lesson-overview-card lg-margin-bottom-24" @click="handleLessonIdeaCardClickEvent(overview)">
                <!-- 周几 -->
                <div class="title-font-20 lg-padding-t-b-8 week-col center-col lg-padding-left-16">
                  <el-skeleton :rows="1" animated :loading="generateCenterOverviewLoading && !overview.centerGroupName">
                    <template>
                      <div>
                        <i class="lg-icon lg-padding-right-8 font-size-20" :class="iconMap.get(overview.centerGroupName)"></i>
                        <span>{{ overview.centerGroupName }}</span>
                      </div>
                    </template>
                  </el-skeleton>
                </div>
                <!-- 课程详情 -->
                <div class="lg-padding-12 lesson-overview-box">
                  <div class="lesson-overview-content lg-scrollbar">
                    <!-- 课程标题 -->
                    <div>
                      <el-skeleton :rows="1" animated :loading="(generateCenterOverviewLoading && !overview.title) || !!overview.generateLoading">
                        <template>
                          <div class="title-font-16">
                            {{ overview.title }}
                          </div>
                        </template>
                      </el-skeleton>
                    </div>
                    <div>
                      <!-- 测评点 -->
                      <div class="lg-margin-top-12">
                        <el-skeleton :rows="1" animated
                                     :loading="(generateCenterOverviewLoading && !overview.measures) || !!overview.generateLoading">
                          <template>
                                      <span v-for="(measure, index) in overview.measures" :key="index">
                                          <el-tooltip popper-class="max-width-400" placement="top" :open-delay="300">
                                              <div slot="content">{{ measureNameDescription(measure) }}</div>
                                              <el-tag type="info" size="small" class="m-r-sm m-b-sm">
                                                  {{ measure }}
                                              </el-tag>
                                          </el-tooltip>
                                      </span>
                          </template>
                        </el-skeleton>
                      </div>
                      <!-- 描述 -->
                      <div class="lg-margin-top-12">
                        <el-skeleton :rows="4" animated
                                     :loading="(generateCenterOverviewLoading && !overview.description) || !!overview.generateLoading">
                          <template>
                            <div class="lessson-overview-desc">{{ overview.description }}</div>
                          </template>
                        </el-skeleton>
                      </div>
                    </div>
                  </div>
                  <!-- 课程类型和操作按钮 -->
                  <div class="display-flex justify-content-end align-items lg-margin-top-12">
                    <!-- 操作 -->
                    <div>
                      <div v-show="!generateCenterOverviewLoading && !overview.generateLoading && !overview.generating">
                        <!-- 编辑 -->
                        <el-tooltip effect="dark" :content="$t('loc.unitPlannerWeeklyEdit')" placement="top" :hide-after="1000">
                          <el-button type="text" @click.stop="edit(overview)" :disabled="generatingLessonIdea || generatingCenterIdea">
                            <i class="lg-icon lg-icon-edit text-black-mon font-size-20"></i>
                          </el-button>
                        </el-tooltip>
                        <!-- 重新生成 -->
                        <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top" :hide-after="1000">
                          <el-button type="text" @click.stop="regenerateCenterOverview(overview)" :disabled="generatingLessonIdea || generatingCenterIdea">
                            <template #icon>
                              <i class="el-icon-refresh-right text-black-mon font-size-20"></i>
                            </template>
                          </el-button>
                        </el-tooltip>
                        <!-- 确认课程 idea -->
<!--                        <el-button type="primary" size="medium" v-show="!overview.confirmed" :disabled="generatingLessonIdea || generatingCenterIdea"-->
<!--                                   @click.stop="confirm(overview)">{{$t('loc.unitPlannerStep3Confirm')}}-->
<!--                        </el-button>-->
                        <!-- 生成详情 -->
                        <el-button type="primary"  size="medium" v-show="overview.confirmed" :disabled="generatingLessonIdea || generatingCenterIdea || batchGenerateLoading"
                                   :loading="batchGenerateLoading"
                                   @click.stop="overview.lessonId ? viewDetail(overview) : generateDetail(overview)"> {{$t('loc.viewLesson')}}
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
    <!-- 编辑课程概览弹窗 -->
    <el-dialog
      custom-class="edit-lesson-dialog"
      :close-on-click-modal="false"
      :title="$t('loc.confirmation')"
      :visible.sync="editLessonVisible"
      width="600px">
        <el-form
            ref="editLessonFormRef"
            label-position="top"
            label-width="100px"
            :model="editLesson"
            :rules="lessonRules"
            class="m-t-sm"
            v-if="editable"
        >
          <!-- 课程标题 -->
          <el-form-item :label="$t('loc.unitPlannerStep3ConfirmationLessonTitle')" prop="title">
            <el-input v-model="editLesson.title" maxlength="200"></el-input>
          </el-form-item>
          <!-- 课程类型 -->
          <el-form-item v-if="!editLesson.centerGroupName && showSmallAndLargeGroupFlag" :label="$t('loc.unitPlannerStep3ConfirmationActivityType')" prop="activityType">
            <el-select v-model="editLesson.activityType" class="w-full">
              <el-option :label="$t('loc.unitPlannerStep3SmallGroup')" :value="'Small Group'"></el-option>
              <el-option :label="$t('loc.unitPlannerStep3LargeGroup')" :value="'Large Group'"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="editLesson.centerGroupName" :label="$t('loc.unitPlannerStep3ConfirmationCenterName')" prop="centerGroupName">
            <el-input :readonly="true" v-model="editLesson.centerGroupName"></el-input>
          </el-form-item>
          <!-- 测评点 -->
          <el-form-item :label="$t('loc.unitPlannerStep3ConfirmationMeasures')" prop="measures">
            <el-select v-model="editLesson.measures" filterable multiple class="w-full update-lesson-measure-select"
                      @visible-change="handleVisibleChange"
                      :loading="loadingFrameworkLoading" :popper-append-to-body="false" :placeholder="$t('loc.pleaseSelect')" :multiple-limit="maxMeasureSelections">
              <!-- 虚拟列表渲染选项 -->
              <lg-virtual-list
                        ref="virtualMeasureList"
                        :data-sources="flattenedMeasures"
                        :data-component="virtualMeasureOptionComponent"
                        :extra-props="{onOptionClick: handleSelectMeasureClick}"
                        height="245px"
                        :data-key="'abbreviation'"
                        :estimate-size="34"
                    />
            </el-select>
          </el-form-item>
          <!-- 校训 -->
          <el-form-item v-if="!editLesson.centerId && rubricsOptions && rubricsOptions.length > 0" :label="$t('loc.unitPlannerStep2Rubrics')" prop="rubrics">
            <el-select v-model="editLesson.rubrics" filterable multiple class="w-full update-lesson-rubrics-select"
                      :popper-append-to-body="false" :placeholder="$t('loc.pleaseSelect')">
              <template v-for="(rubric, index) in rubricsOptions">
                <!-- 有子标准的校训使用 optgroup，只能选择子标准 -->
                <el-option-group v-if="rubric.subStandards && rubric.subStandards.length > 0" 
                                :key="`group-${index}`" 
                                :label="rubric.title">
                  <el-option
                    v-for="(subRubric, subIndex) in rubric.subStandards"
                    :key="`${index}-${subIndex}`"
                    :label="subRubric.title"
                    :value="subRubric.title">
                    <div class="rubric-option">
                      <span class="rubric-title">{{ subRubric.title }}</span>
                      <span v-if="subRubric.description" class="rubric-description">: {{ subRubric.description }}</span>
                    </div>
                  </el-option>
                </el-option-group>
                <!-- 没有子标准的校训直接显示，可以选择主标准 -->
                <el-option v-else
                  :key="index"
                  :label="rubric.title"
                  :value="rubric.title">
                  <div class="rubric-option">
                    <span class="rubric-title">{{ rubric.title }}</span>
                    <span v-if="rubric.description" class="rubric-description">: {{ rubric.description }}</span>
                  </div>
                </el-option>
              </template>
            </el-select>
          </el-form-item>
          <!-- 描述 -->
          <el-form-item :label="$t('loc.unitPlannerStep3ConfirmationActivityDescription')" prop="description">
            <el-input type="textarea" v-model="editLesson.description" maxlength="1000" :autosize="{ minRows: 2, maxRows: 6}"></el-input>
          </el-form-item>
        </el-form>
        <!-- 操作 -->
        <span slot="footer" class="dialog-footer">
                  <el-button @click="editLessonVisible = false">{{$t('loc.cancel')}}</el-button>
                  <el-button type="primary" @click="updateLesson">{{$t('loc.unitPlannerStep3Confirm')}}</el-button>
              </span>
    </el-dialog>
    <!-- Centers 组活动生成选择弹窗 -->
    <el-dialog
        class="centers-check-group-dialog"
      :close-on-click-modal="false"
      :title="kToGrade2 ? $t('loc.unitPlannerStep3SelectStations') : $t('loc.unitPlannerStep3SelectCenters')"
      :visible.sync="selectCentersVisible"
      :before-close="closeCenterModal"
      width="600px">
      <div>
        {{kToGrade2 ? $t('loc.unitPlannerStep3PleaseSelectActivitiesStations') : $t('loc.unitPlannerStep3PleaseSelectActivitiesCenters')}}
      </div>
      <div class="centers-check-group">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">{{$t('loc.unitPlannerStep3SelectAll')}}</el-checkbox>
        <el-row class="lg-margin-l-r-32">
          <el-checkbox-group v-model="checkedCenterTags"
                             :max="unitUsageModel.weeklyCenterCount"
                             @change="handleCheckedCentersChange">
            <el-col :span="12" v-for="(center, index) in centerTags" :key="index" class="m-b-sm">
              <el-checkbox :label="center" :key="center">{{getCenterTagsI18n(center)}}</el-checkbox>
            </el-col>
          </el-checkbox-group>
        </el-row>
      </div>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeCenterModal">{{$t('loc.cancel')}}</el-button>
        <el-button type="primary" @click="generateCentersOverView" :disabled="checkedCenterTags.length == 0">{{$t('loc.unitPlannerStep1Save')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="kToGrade2 ? $t('loc.unitPlannerStep3CenterRotationStationRotationSettings') : $t('loc.unitPlannerStep3CenterRotationCenterRotationSettings')"
      class="setting-dialog"
      :visible.sync="centerSettingVisible"
      width="800px"
      :before-close="closeCenterSettingModal">
      <div class="add-margin-b-20 font-size-16">
        {{kToGrade2 ? $t('loc.unitPlannerStep3StationRotationApproachEnsures') : $t('loc.unitPlannerStep3CenterRotationApproachEnsures')}}
      </div>
      <div v-loading="centerSettingLoading">
        <div>
          <el-radio v-model="centerSettingSystemType" :label="true">
            <span class="font-weight-600 font-size-16">{{$t('loc.unitPlannerStep3CenterRotationDefaultSettings')}}</span>
          </el-radio>
          <div v-if="kToGrade2" class="lg-padding-l-r-24 lg-margin-t-b-12 font-size-16">
            {{ $t('loc.unitPlannerStep3StationRotationTheSystemWillAutomatically') }}
          </div>
          <div v-else-if="!isShowCenterType" class="lg-padding-l-r-24 lg-margin-t-b-12 font-size-16">
            {{$t('loc.unitPlannerStep3CenterRotationTheSystemWillAutomatically')}}
          </div>
          <div v-else class="lg-padding-l-r-24 lg-margin-t-b-12 font-size-16">
            {{$t('loc.unitPlannerStep3CenterRotationTheSystemWillAutomaticallyTemp')}}
          </div>
        </div>
        <div class="lg-margin-top-20">
          <el-radio v-model="centerSettingSystemType" :label="false">
            <span class="font-weight-600 font-size-16">{{$t('loc.unitPlannerStep3CenterRotationCustomSettings')}}</span>
          </el-radio>
          <div v-show="!centerSettingSystemType">
            <div class="lg-padding-l-r-24 font-size-16">
              {{ kToGrade2 ? $t('loc.unitPlannerStep3CenterRotationPleaseSelectTheStations') : $t('loc.unitPlannerStep3CenterRotationPleaseSelectTheCenters')}}
            </div>
            <el-row class="lg-margin-top-12" :gutter="16">
              <el-col :xs="24" :sm="6" v-for="(center, index) in centerTags" :key="index">
                <div class="flex-column-center center-tag-box lg-pointer center-padding"
                    :class="centerTagChecked(center)"
                    @click="changeCustomCenterSetting(center)">
                  <i class="lg-icon font-size-24" :class="iconMap.get(center)"></i>
                  <span>{{ getCenterTagsI18n(center) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="centerSettingLoading" @click="closeCenterSettingModal">{{$t('loc.cancel')}}</el-button>
        <el-button :disabled="centerSettingLoading" type="primary" @click="saveCenterSetting">{{$t('loc.save')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
    UnitPlanCenterTags, UnitPlanCenterTagsI18n, UnitPlanCenterIconMap, UnitPlanITCenterTags, UnitPlanITCenterTagsI18n, UnitPlanITCenterIconMap,
    UnitPlanKCenterTags, UnitPlanKCenterTagsI18n, UnitPlanKCenterIconMap
} from '@/utils/constants'
import Lessons2 from '@/api/lessons2'
import LessonApi from '@/api/lessons2'
import MeasureOption from './LessonMeasureOption.vue'
import LgVirtualList from '@/components/LgVirtualList.vue'
import VirtualEnhanceLessonMeasureOption from '@/views/modules/lesson2/lessonLibrary/components/VirtualEnhanceLessonMeasureOption.vue'
import { maxMeasureSelections } from '@/utils/constants'
import tools from '@/utils/tools'
import { equalsIgnoreCase } from '@/utils/common'

export default {
  props: {
    // 当前周的大小组活动
    lessonOverviews: {
      type: Array,
      default: () => []
    },
    // 当前周的 centers 组活动
    centerOverviews: {
      type: Array,
      default: () => []
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: false
    },
    // 当前周大小组活动生成 loading 中
    loading: {
      type: Boolean,
      default: false
    },
    // 当前周 centers 组活动概览生成 loading 中
    generateCenterOverviewLoading: {
      type: Boolean,
      default: false
    },
    // 生成 centers 组活动概览按钮是否禁用
    generateCenterOverviewDisabled: {
      type: Boolean,
      default: false
    },
    // 当前周计划
    currentWeeklyPlan: {
      type: Object,
      default: () => {}
    },
    // 批量生成课程的 loading 状态
    batchGenerateLoading: {
      type: Boolean,
      default: false
    }
  },
  components: {
    MeasureOption, LgVirtualList, VirtualEnhanceLessonMeasureOption
  },
  data () {
    // 标题校验，不可为空
    let titleValidator = (rule, value, callback) => {
      if (!value || value.trim().length === 0) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    // 描述校验，不可为空
    let descriptionValidator = (rule, value, callback) => {
      if (!value || value.trim().length === 0) {
        callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    return {
      measureOption: MeasureOption,
      // 编辑弹窗是否显示
      editLessonVisible: false,
      // 当前编辑的课程
      editLesson: {},
      editLessonRef: {},
      loadingFrameworkLoading: false, // 加载框架 loading
      domains: [], // 框架测评点
      // 编辑课程的表单验证规则
      lessonRules: {
        title: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' },
          { validator: titleValidator, trigger: 'blur' }
        ],
        measures: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
        ],
        description: [
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'blur' },
          { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' },
          { validator: descriptionValidator, trigger: 'blur' }
        ]
      },
      themeCollapsed: false, // 主题是否折叠
      centerTags: UnitPlanCenterTags, // centers 组标签
      centerTagsI18n: UnitPlanCenterTagsI18n, // centers 组标签
      iconMap: UnitPlanCenterIconMap, // centers 组图标
      checkedCenterTags: UnitPlanCenterTags, // 选中的 centers 组
      checkedCenterTagsTemp: [], // 选中的 centers 组临时变量
      selectCentersVisible: false, // 选择 centers 弹窗是否显示
      checkAll: false, // 是否全选
      isIndeterminate: false, // 是否半选
      centerSettingVisible: false, // centers 组设置弹窗是否显示
      centerSettingLoading: false, // centers 组设置加载中
      centerSettingSystemType: true, // centers 组设置类型
      centerSettingCustomChanges: [], // centers 组设置自定义变化的 centers
      popperOptions: {
        offset: [0, 40] // 设置偏移量，例如 [水平偏移, 垂直偏移]
      },
      isShowCenterType: false, // 是否显示 center 类型
      progressConstant: {
        sixtyPercent: 'WEEK_OVERVIEW_CONFIRMED',
        eightyPercent: 'LESSON_OVERVIEW_GENERATED'
      }, // 进度常量
      copyLessonOverviews: null, // 拷贝的初始化 lessonOverviews
      lessonOverviewsInit: false, // lessonOverviews 是否初始化
      standardSet: new Set(), // 存储周计划的所有测评点，同时去除汇总的重复测评点
      isMobile: false, // 是否是移动端
      virtualMeasureOptionComponent: VirtualEnhanceLessonMeasureOption,
      maxMeasureSelections: maxMeasureSelections // 测评点限制个数为 3
    }
  },
  created () {
    // 检测设备类型
    this.checkDeviceType()
    // 监听窗口大小变化
    window.addEventListener('resize', this.checkDeviceType)
  },
  destroyed () {
    if (this.unit.id && this.copyLessonOverviews !== JSON.stringify(this.lessonOverviews)) {
      this.$store.commit('curriculum/SET_UNIT_HAS_UPDATED', true)
    }
  },
  mounted () {
    this.getFrameworkDomains(this.frameworkId)
    // this.$nextTick(() => {
    //   // 获取 centers 组设置
    //   this.getCentersSetting()
    // })
  },
  computed: {
    ...mapState({
      unit: state => state.curriculum.unit,
      unitUsageModel: (state) => state.magicCurriculum.unitUsageModel, // unit 使用的功能模块
      frameworkId: state => state.curriculum.unit.baseInfo.frameworkId,
      baseInfo: state => state.curriculum.unit.baseInfo,
      batchTasks: state => state.unit.batchTasks, // 任务列表
      showSmallAndLargeGroupFlag: state => state.curriculum.showSmallAndLargeGroupFlag, // 是否显示 Small Group 和 Large Group
      isGenieCurriculumToUnitPlanThird: state => state.curriculum.isGenieCurriculumToUnitPlanThird, // 是否由 genie 生成的 curriculum 进入的
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG // 是否使用课程模板功能
    }),
    // 当前周包含的所有测评点
    weeklyStandards () {
      // 清除当前存放的测评点
      this.standardSet.clear()
      const lessonOverviews = this.lessonOverviews
      // 汇总周里所有 item 包含的测评点
      lessonOverviews && lessonOverviews.forEach(item => {
        // 添加 item 中的测评点到 standardSet 中
        item.measures && item.measures.length > 0 && item.measures.forEach(measure => {
          this.standardSet.add(measure)
        })
      })

      // 汇总所有 center 课程包含的测评点
      const centerOverviews = this.centerOverviews
      centerOverviews && centerOverviews.forEach(item => {
        // 添加 item 中的测评点到 standardSet 中
        item.measures && item.measures.length > 0 && item.measures.forEach(measure => {
          this.standardSet.add(measure)
        })
      })
      // 测评点缩写集合转换为数组并排序
      return Array.from(this.standardSet).sort();
    },

    // 周概览测评点加载状态
    weeklyStandardsLoading () {
      // 判断课程的测评点加载状态
      if (this.lessonOverviews) {
        for (const overview of this.lessonOverviews) {
          if ((this.loading && !overview.measures) || !!overview.generateLoading) {
            return true
          }
        }
      }
      // 判断 center 课程的加载状态
      if (this.centerOverviews) {
        for (const overview of this.centerOverviews) {
          if ((this.generateCenterOverviewLoading && !overview.measures) || !!overview.generateLoading) {
            return true
          }
        }
      }
      return false
    },
    // 是否显示 centers 组生成及设置
    showCenters () {
      // 如果是线上教学，则不显示 centers 组生成及设置
      if (equalsIgnoreCase(this.baseInfo.classroomType, 'VIRTUAL')) {
        return false
      }
      // 如果已经生成了 centers 组活动，就不再隐藏 Center 设置及生成按钮了
      const hasCenterItems = this.weeklyPlans.some(x => x.centerItems && x.centerItems.length > 0)
      if (hasCenterItems) {
        return true
      }
      // 只有 0-5 岁的课程才生成 centers 组活动
      let showCenterGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)', 'K (5-6)', 'Grade 1', 'Grade 2']
      return showCenterGrades.includes(this.baseInfo.grade)
    },
    // 年龄组是否是 K-Grade 2
    kToGrade2 () {
      if (this.baseInfo && this.baseInfo.grade) {
        return ['K (5-6)', 'Grade 1', 'Grade 2'].includes(this.baseInfo.grade)
      }
      return false
    },
    // 年龄组是否是 K - Grade 12
    kToGrade12 () {
      if (this.baseInfo && this.baseInfo.grade) {
        return ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group'].includes(this.baseInfo.grade)
      }
      return false
    },
    // 是否需要批量推荐课程模板，全部活动都没有模板时走批量推荐流程
    needRecommendLessonTemplate () {
      return this.eduProtocolsTemplateApplyOpen && this.kToGrade12 && this.currentWeeklyPlan && this.currentWeeklyPlan.items && this.currentWeeklyPlan.items.every(item => !item.lessonTemplateType)
    },
    // 生成 centers 组活动按钮是否禁用
    domainTitle() {
      return function(domain) {
        let domainTitle = ""
        if (!domain) {
          return domainTitle
        }
        if (!!domain.abbreviation && !!domain.name) {
          domainTitle += domain.abbreviation + ': ' +  domain.name
        } else if(!!domain.abbreviation){
          domainTitle += domain.abbreviation
        } else if (!!domain.name) {
          domainTitle += domain.name
        }
        return domainTitle
      }
    },
    //  查找 centerTags 对应的多语言
    getCenterTagsI18n () {
        return (tag) => {
            let i18n = this.centerTagsI18n.find(item => item.label === tag)
            // 如果查找到了，就返回对应的国际化语言，否则返回空字符串
            if (i18n) {
                return i18n.i18n
            }
            return ''
        }
    },
    weeklyPlans: {
      get () {
        return this.$store.state.curriculum.unit.weeklyPlans
      },
      set (value) {
        this.$store.commit('curriculum/SET_WEEKLY_PLANS', value)
      }
    },
    // 所有的课程 idea 已确认
    allLessonOverviewConfirmed () {
      if (!this.lessonOverviews || this.lessonOverviews.length === 0) {
        return false
      }
      return this.lessonOverviews.filter(x => x.confirmed).length === this.lessonOverviews.length
    },
    // 所有的 center 课程 idea 已确认
    allCenterOverviewConfirmed () {
      if (!this.centerOverviews || this.centerOverviews.length === 0) {
        return false
      }
      return this.centerOverviews.filter(x => x.confirmed).length === this.centerOverviews.length
    },
    // 是否可以编辑
    canEdit () {
      return function (overview) {
        return this.editable && !overview.deleted
      }
    },
    // 是否可以删除
    canDelete () {
      return function (overview) {
        return this.editable && !overview.confirmed && !overview.deleted
      }
    },
    // 是否可以重新生成
    canRegenerate () {
      return function (overview) {
        return this.editable && !overview.confirmed
      }
    },
    // 是否可以确认
    canConfirm () {
      return function (overview) {
        return this.editable && !overview.confirmed && !overview.deleted
      }
    },
    // 内容换行符替换
    formatContentWrap () {
      return function (content) {
        if (!content) {
          return ''
        }
        return content.replace(/\n/g, '<br/>')
      }
    },
    // centers 组设置标签是否选中
    centerTagChecked () {
      return function (center) {
        if (this.centerSettingCustomChanges.includes(center)) {
          return 'active'
        }
        return 'color-676879'
      }
    },
    // 生成课程按钮 Loading 状态
    generateLessonIdeaLoading () {
      return this.lessonOverviews.some(x => x.generateLoading)
    },
    // 正在生成课程中
    generatingLessonIdea () {
      return this.lessonOverviews.some(x => x.generating) || (this.currentWeeklyPlan && this.currentWeeklyPlan.updateLoading)
    },
    // 正在生成 centers 活动中
    generatingCenterIdea () {
      return this.centerOverviews.some(x => x.generating) || this.generateCenterOverviewDisabled || (this.currentWeeklyPlan && this.currentWeeklyPlan.updateLoading)
    },
    // 当前周包含的所有校训
    weeklyRubrics() {
      // 创建一个 Map 用于去重，使用 title 作为 key
      const rubricsMap = new Map();

      // 汇总周里所有 item 包含的校训
      const lessonOverviews = this.lessonOverviews;
      lessonOverviews && lessonOverviews.forEach(item => {
        if (item.rubrics) {
          // 使用 flattenRubrics 方法处理树形结构
          const flattenedRubrics = this.flattenRubrics(item.rubrics);
          flattenedRubrics.forEach(rubric => {
            if (rubric.title) {
              rubricsMap.set(rubric.title, rubric);
            }
          });
        }
      });

      // 将 Map 转换为数组并返回
      return Array.from(rubricsMap.values());
    },
    // 单元可选的校训选项
    rubricsOptions() {
      return this.baseInfo && this.baseInfo.newRubrics || []
    },
    // 获取校训显示内容
    getDisplayRubrics() {
      return function(rubric) {
        if (!rubric || !rubric.title) {
          return '';
        }
        
        let content = rubric.title;
        if (rubric.description) {
          content += '<br/>' + rubric.description;
        }
        if (rubric.expectations) {
          content += '<br/><br/>Expectations<br/>' + rubric.expectations;
        }
        return content;
      }
    },
    // 将领域和测评点展平为一个数组
    flattenedMeasures() {
      if (!this.domains || this.domains.length === 0) return []

      // 将所有领域和测评点展平为一个数组
      const flattened = []

      this.domains.forEach(domain => {
        // 添加领域作为分组标题
        flattened.push({
          id: domain.id,
          name: domain.name || domain.abbreviation,
          abbreviation: domain.id,
          // 标记为领域
          isDomain: true
        })
        // 添加该领域下的所有测评点
        if (domain.children) {
          const measures = []
          this.getMeasuresBottom(domain.children, measures)
          measures.forEach(measure => {
            flattened.push({
              ...measure
            })
          })
        }
      })
      return flattened
    },
  },
  watch: {
    isMobile: {
      handler(val) {
        if (val) {
          console.log('isMobile', val)
        }
      }
    },
    // 监听 centers 组设置类型
    centerSettingSystemType (val) {
      if (val) {
        this.centerSettingCustomChanges = ['Art']
        // 点击自定义设置按钮埋点
        this.$analytics.sendEvent('web_unit_create3_center_setting_cust')
      }
    },
    // 监听 baseInfo.grade 变化，重新获取 centers 组
    'baseInfo.grade': {
      handler(val) {
        // 在 baseInfo.grade 变化时执行的逻辑
        // 需要根据年龄组展示不同的 centers 组
        this.getCentersByGrade(val);
      },
      immediate: true // 在定义的时候就会立即执行一次
    },
    // 初始化测评点信息
    frameworkId (val) {
      if (val) {
        this.getFrameworkDomains(val)
      }
    },
    // 监听课程概览生成, 滚动到生成的课程概览
    lessonOverviews: {
      deep: true,
      handler (val, oldVal) {
        if (this.loading) {
          let lessonOverviewIndex = this.calculateDifferentIndex(val, oldVal)
          if (!lessonOverviewIndex) {
            return
          }
          this.$refs.lessonOverview[lessonOverviewIndex].scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
        if (!this.lessonOverviewsInit) {
          this.copyLessonOverviews = JSON.stringify(val)
          this.lessonOverviewsInit = true
        }
      }
    },
    // 监听 centers 组概览生成, 滚动到生成的 centers 组概览
    centerOverviews: {
      deep: true,
      handler (val, oldVal) {
        if (this.generateCenterOverviewLoading) {
          let centerOverviewIndex = this.calculateDifferentIndex(val, oldVal)
          if (!centerOverviewIndex) {
            return
          }
          this.$refs.centerOverview[centerOverviewIndex].scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }
    },
    // 测评点变更后与周计划测评点对比
    weeklyStandards: {
      deep: true,
      handler (val, oldVal) {
        this.needUpdateWeeklyPlanStandards(val)
      }
    },
    // 监听周校训
    weeklyRubrics: {
      deep: true,
      handler (val, oldVal) {
        this.needUpdateWeeklyPlanRubrics(val)
      }
    }
  },
  methods: {
    // 测评点选择点击事件
    handleSelectMeasureClick(measure) {
      if (this.editLesson.measures && this.editLesson.measures.length >= this.maxMeasureSelections && !this.editLesson.measures.includes(measure.abbreviation)) {
        this.$nextTick(() => {
          // 显示警告消息
          this.$message.warning(this.$t('loc.selectMeasureEight'));
        })
      }
    },
    // 测评点下拉框可见变化时处理方法
    handleVisibleChange(visible) {
      if (visible && this.editLesson.measures[0]) {
        // 定位到第一个选中的
        this.$refs.virtualMeasureList && this.$refs.virtualMeasureList.scrollToItem(this.editLesson.measures[0])
      }
    },
    // 获取底层测评点
    getMeasuresBottom(children, measuresBottom) {
      if (!children || children.length === 0) {
        return
      }
      children.forEach(v => {
        const childrenNext = v.children
        if (
          !childrenNext || childrenNext.length === 0
        ) {
          measuresBottom.push(v)
        } else {
          this.getMeasuresBottom(childrenNext, measuresBottom)
        }
      })
    },
    // 检查并更新单元改编状态
    checkAndUpdateUnitAdaptedStatus () {
      // 改编单元
      if (!this.unit.adapted) {
        return
      }
      // 检查所有周计划下的课程是否都不是改编课程
      const allItemsNotAdapted = this.weeklyPlans.every(weeklyPlan => {
        // 检查普通课程项
        return weeklyPlan.items.every(item => !item.adaptedLesson)
      })
      // 如果所有课程都不是改编课程，则更新单元改编状态为 false
      if (allItemsNotAdapted) {
        this.updateUnitAdaptedStatus(false)
      }
    },

    // 更新单元改编状态
    async updateUnitAdaptedStatus (adaptedStatus) {
      try {
        const params = {
          unitId: this.unit.id,
          adapted: adaptedStatus
        }

        // 调用接口更新单元改编状态
        await this.$axios.post($api.urls().updateUnitBaseInfo, params)
        this.$set(this.unit, 'adapted', adaptedStatus)
      } catch (error) {
      }
    },

    // 检查并更新周计划改编状态
    checkAndUpdatePlanAdaptedStatus() {
      // 找出周计划下课程都不是改编但周计划为改编的周计划
      const allNotAdaptedPlanIds = this.weeklyPlans
        .filter(entry =>
          entry.adapted === true &&
          Array.isArray(entry.items) &&
          entry.items.every(item => !item.adaptedLesson)
        )
        .map(entry => entry.id);
      if (allNotAdaptedPlanIds.length > 0) {
        this.revertPlanAdapted(allNotAdaptedPlanIds)
      }
    },

    // 更新单元改编状态
    async revertPlanAdapted(planIds) {
      const params = {
        planIds: planIds,
        adapted: false
      }
      // 调用接口更新单元改编状态
      await this.$axios.post($api.urls().updatePlanAdapted, params)
      // 更新周计划状态
      this.weeklyPlans.forEach(plan => {
        if (planIds.includes(plan.id)) {
          plan.adapted = false
        }
      })
    },

    // 检测设备类型
    checkDeviceType() {
      this.isMobile = window.innerWidth <= 768
    },
    // 判断是否需要更新周计划校训
    async needUpdateWeeklyPlanRubrics (rubrics) {
      // 如果正在加载或生成中,不进行更新
      if (this.weeklyStandardsLoading || !this.currentWeeklyPlan || this.loading || this.generateCenterOverviewLoading || this.generatingLessonIdea || this.generatingCenterIdea) {
        return
      }
      // 获取当前周计划中的校训标题列表
      const currentRubricTitles = this.flattenRubrics(this.currentWeeklyPlan.rubrics).map(rubric => rubric.title)
      // 获取新的校训标题列表
      const newRubricTitles = rubrics.map(rubric => rubric.title)
      // 检查校训是否发生变化
      const isNotEqual = newRubricTitles.length !== currentRubricTitles.length ||
        newRubricTitles.some(title => !currentRubricTitles.includes(title)) ||
        currentRubricTitles.some(title => !newRubricTitles.includes(title))
      // 过滤校训
      const treeRubrics = tools.filterRubrics(newRubricTitles, this.rubricsOptions)
      // 如果校训发生变化,则更新周计划
      if (isNotEqual) {
        const params = {
          planId: this.currentWeeklyPlan.id,
          rubrics: treeRubrics
        }
        // 更新周计划校训
        try {
          const res = await LessonApi.updateWeeklyRubrics(params)
          if (res && res.success) {
            this.$set(this.currentWeeklyPlan, 'rubrics', rubrics)
          }
        } catch (error) {
          this.$message.error(error.message)
        }
      }
    },

    // 判断是否需要更新周计划测评点
    needUpdateWeeklyPlanStandards (val) {
      if (this.weeklyStandardsLoading || !this.currentWeeklyPlan || !this.currentWeeklyPlan.measureIds ||
        this.loading || this.generateCenterOverviewLoading || this.generatingLessonIdea || this.generatingCenterIdea) {
        return
      }
      if (!val) {
        val = this.weeklyStandards
      }
      // 周计划测评点为空时，基本为旧数据，不进行测评点同步
      if (!this.currentWeeklyPlan.measureIds || this.currentWeeklyPlan.measureIds.length === 0) {
        return
      }
      let measures = []
      let isNotEqual = false
      val.forEach(item => {
        const measure = this.getMeasureByAbbr(this.domains, item)
        if (measure) {
          measures.push(measure)
          if (!isNotEqual) {
            // 检查当前周计划的测评点ID列表中是否不包含当前测评点的ID
            // 如果返回true，表示当前测评点不在周计划中
            // 如果返回false，表示当前测评点已经在周计划中
            isNotEqual = this.currentWeeklyPlan.measureIds.indexOf(measure.id) === -1
          }
        }
      })
      // 如果测评点变更后与周计划测评点对比，不一致则更新周计划
      if (isNotEqual || val.length !== this.currentWeeklyPlan.measureIds.length) {
        this.updateWeeklyPlanStandards(measures)
      }
    },
    // 更新周计划测评点
    async updateWeeklyPlanStandards (measures) {
      // 开始更新测评点
      this.toggleWeeklyMeasureUpdates()
      const measureIds = measures.map(x => x.id)
      if (measureIds.length === 0) {
        return
      }
      const params = {
        upWeeklyMeasureRequestList: [{
          planId: this.currentWeeklyPlan.id,
          measureIds: measureIds
        }]
      }
      // 更新周计划测评点
      try {
        const res = await LessonApi.updateWeeklyMeasureIds(params)

        if (res && res.success) {
          this.$set(this.currentWeeklyPlan, 'measureIds', measureIds)
        }
      } catch (error) {
        console.error(error)
      }
      // 测评点更新结束
      this.toggleWeeklyMeasureUpdates()
    },
    // 对测评点进行排序
    sortedSelectMeasure (measureAbbs, measureA, measureB) {
      const indexA = measureAbbs.indexOf(measureA)
      const indexB = measureAbbs.indexOf(measureB)
      return indexA === -1
        ? (indexB === -1 ? 0 : 1)  // measureA 不在 measureAbbs 中，且 measureB 在则 measureA 在后
        : (indexB === -1 ? -1 : indexA - indexB) // measureB 不在 measureAbbs 中，或按 measureAbbs 顺序排序
    },
    // 计算两个数组第一个不同的位置
    calculateDifferentIndex (arr1, arr2) {
      let index
      for (let i = 0; i < arr1.length; i++) {
        if (JSON.stringify(arr1[i]) !== JSON.stringify(arr2[i])) {
          index = i
          break
        }
      }
      return index
    },
    // 滚动到顶部
    scrollToTop () {
      this.$nextTick(() => {
        document.getElementById('lessonOverviewDetailHeader').scrollIntoView({ behavior: 'smooth', block: 'center' })
      })
    },
    // 大小组多语言转化器
    groupTypeConvert (value) {
      if (value === 'Small Group') {
        return this.$t('loc.unitPlannerStep3SmallGroup')
      } else if (value === 'Large Group') {
        return this.$t('loc.unitPlannerStep3LargeGroup')
      } else {
        return value
      }
    },
    // 确认全部
    confirmAll (centerLesson) {
      // 确认全部按钮点击埋点
      this.$analytics.sendEvent('cg_unit_create_three_confirmall')
      // 清空数据
      if (centerLesson) {
        this.centerOverviews.forEach(overview => {
          this.$set(overview, 'confirmed', true)
        })
      } else {
        this.lessonOverviews.forEach(overview => {
          this.$set(overview, 'confirmed', true)
        })
      }
      this.$emit('update')
    },
    // 清除生成任务
    clearGenerateTask (single = false, isCenter = false, item) {
      // 如果不存在任务列表，则直接返回
      if (!this.batchTasks) {
        return
      }
      // 如果是单个重新生成，则只需要删除当前课程的生成任务即可
      let pendingTasks = this.batchTasks.pendingTasks
      let processingTasks = this.batchTasks.processingTasks
      let failedTasks = this.batchTasks.failedTasks
      const completedTasks = this.batchTasks.completedTasks
      // 如果没有需要处理的任务，则直接清除即可
      if (pendingTasks.length === 0 && processingTasks.length === 0 && failedTasks.length === 0) {
        this.$store.dispatch('unit/setBatchTasks', null)
        this.$store.dispatch('unit/setBatchId', '')
        this.$store.dispatch('unit/setAppearNotice', false)
        return
      }
      // 如果是单个重新生成，则只需要删除当前课程的生成任务即可
      if (single) {
        pendingTasks = pendingTasks.filter(x => x.itemId !== item.id)
        processingTasks = processingTasks.filter(x => x.itemId !== item.id)
        failedTasks = failedTasks.filter(x => x.itemId !== item.id)
        // 如果没有需要处理的任务，则直接清除即可
        if (pendingTasks.length === 0 && processingTasks.length === 0 && failedTasks.length === 0) {
          this.$store.dispatch('unit/setBatchTasks', null)
          this.$store.dispatch('unit/setBatchId', '')
          this.$store.dispatch('unit/setAppearNotice', false)
        } else {
          this.$store.dispatch('unit/setBatchTasks', {
            pendingTasks: pendingTasks,
            processingTasks: processingTasks,
            completedTasks: completedTasks,
            failedTasks: failedTasks
          })
        }
      } else {
        // 如果是重新生成 centers 活动，则需要删除 centers 活动的生成任务
        if (isCenter) {
          let centerItemIds = []
          this.weeklyPlans.forEach(weeklyPlan => {
            if (weeklyPlan.centerItems) {
              weeklyPlan.centerItems.forEach(centerItem => {
                centerItemIds.push(centerItem.id)
              })
            }
          })
          pendingTasks = pendingTasks.filter(x => centerItemIds.indexOf(x.itemId) === -1)
          processingTasks = processingTasks.filter(x => centerItemIds.indexOf(x.itemId) === -1)
          failedTasks = failedTasks.filter(x => centerItemIds.indexOf(x.itemId) === -1)
          // 如果没有需要处理的任务，则直接清除即可
          if (pendingTasks.length === 0 && processingTasks.length === 0 && failedTasks.length === 0) {
            this.$store.dispatch('unit/setBatchTasks', null)
            this.$store.dispatch('unit/setBatchId', '')
            this.$store.dispatch('unit/setAppearNotice', false)
          } else {
            this.$store.dispatch('unit/setBatchTasks', {
              pendingTasks: pendingTasks,
              processingTasks: processingTasks,
              completedTasks: completedTasks,
              failedTasks: failedTasks
            })
          }
        } else {
          // 如果是重新生成大小组活动，则需要删除大小组活动的生成任务
          let itemIds = []
          this.weeklyPlans.forEach(weeklyPlan => {
            if (weeklyPlan.items) {
              weeklyPlan.items.forEach(item => {
                itemIds.push(item.id)
              })
            }
          })
          pendingTasks = pendingTasks.filter(x => itemIds.indexOf(x.itemId) === -1)
          processingTasks = processingTasks.filter(x => itemIds.indexOf(x.itemId) === -1)
          failedTasks = failedTasks.filter(x => itemIds.indexOf(x.itemId) === -1)
          // 如果没有需要处理的任务，则直接清除即可
          if (pendingTasks.length === 0 && processingTasks.length === 0 && failedTasks.length === 0) {
            this.$store.dispatch('unit/setBatchTasks', null)
            this.$store.dispatch('unit/setBatchId', '')
            this.$store.dispatch('unit/setAppearNotice', false)
          } else {
            this.$store.dispatch('unit/setBatchTasks', {
              pendingTasks: pendingTasks,
              processingTasks: processingTasks,
              completedTasks: completedTasks,
              failedTasks: failedTasks
            })
          }
        }
      }
    },
    // 重新生成全部
    regenerateAll (centerLesson) {
      // 判断是否需要展示 Small Group 和 Large Group
      // 获取当前单元的年龄
      var grade = this.baseInfo.grade
      let showSmallAndLargeGroupGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)']
      // 判断该年龄是否包含在 showSmallAndLargeGroupGrades 中
      let showSmallAndLargeGroup = showSmallAndLargeGroupGrades.includes(grade)
      this.$store.commit('curriculum/SET_SHOW_SMALL_AND_LARGE_GROUP_FLAG', showSmallAndLargeGroup)
      if (centerLesson) {
        // 点击再次生成全部 center 按钮埋点
        this.$analytics.sendEvent('web_unit_create3_center_regene_all')
        this.$confirm(this.$t('loc.unitPlannerStep3Confirmation5'), this.$t('loc.confirmation'), {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel')
        }).then(() => {
          // 如果点击重新生成 centers 按钮，则停止 centers 活动生成任务
          this.stopGenerateLessonTask(false, true)
          this.clearGenerateTask(false, true)
          // 清空数据
          this.centerOverviews.forEach(overview => {
            this.$set(overview, 'title', undefined)
            this.$set(overview, 'deleted', true)
          })
          this.$emit('regenerateAllCenterOverviews', this.currentWeeklyPlan)
        }).catch(() => {
        })
      } else {
        // 点击再次生成全部按钮埋点
        this.$analytics.sendEvent('web_unit_create3_group_regene_all')
        this.$confirm(this.$t('loc.unitPlannerStep3Confirmation1'), this.$t('loc.confirmation'), {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel')
        }).then(() => {
          // 如果点击重新生成全部按钮，则停止大小组活动生成任务
          this.stopGenerateLessonTask(false, false)
          this.clearGenerateTask(false, false)
          // 清空数据
          this.lessonOverviews.forEach(overview => {
            this.$set(overview, 'title', undefined)
            this.$set(overview, 'deleted', true)
          })
          this.$emit('regenerateAllLessonOverviews', this.currentWeeklyPlan)
        }).catch(() => {
        })
      }
    },

    // 打开选择生成 Center 活动弹窗
    openGenerateCenterModal () {
      // 点击生成 centers 按钮埋点
      this.$analytics.sendEvent('web_unit_create3_generate_center')
      if (this.allLessonOverviewConfirmed) {
        this.selectCentersVisible = true
        // 选择 center 弹窗曝光事件
        this.$analytics.sendEvent('web_unit_create3_center_select_pop')
      } else {
        // 请先确认所有课程 idea 提示语
        let tip = this.kToGrade2 ? this.$t('loc.unitPlannerStep3StationRotationLargeGroupsAndSmallGroupsConfirmation') : this.$t('loc.unitPlannerStep3CenterRotationLargeGroupsAndSmallGroupsConfirmation')
        this.$confirm(tip, this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.unitPlannerStep3ConfirmAll'),
          cancelButtonText: this.$t('loc.cancel'),
          customClass: 'width-600-confirm-message-box'
        }).then(() => {
          this.lessonOverviews.forEach(overview => {
            this.$set(overview, 'confirmed', true)
          })
          this.$emit('update')
          this.selectCentersVisible = true
          // 选择 center 弹窗曝光事件
          this.$analytics.sendEvent('web_unit_create3_center_select_pop')
        }).catch(() => {
        })
      }
    },
    // 停止执行生成课程任务
    stopGenerateLessonTask (single = false, isCenter = false, item) {
      // 如果不存在任务列表，则直接返回
      if (!this.batchTasks) {
        return
      }
      // 如果是单个重新生成，则只需要删除当前课程的生成任务即可
      const pendingTasks = this.batchTasks.pendingTasks
      const processingTasks = this.batchTasks.processingTasks
      const completedTasks = this.batchTasks.completedTasks
      const failedTasks = this.batchTasks.failedTasks
      let taskIds = ''
      // 合并所有任务
      const allTasks = [...pendingTasks, ...processingTasks, ...completedTasks, ...failedTasks]
      if (single) {
        this.clearGenerateTask(true, false, item)
        // 如果是单个重新生成活动，则只需要删除当前活动的生成任务即可
        const task = allTasks.find(x => x.itemId === item.id)
        if (task) {
          taskIds = task.taskId
        }
      } else {
        let centerItemIds = []
        let itemIds = []
        this.weeklyPlans.forEach(weeklyPlan => {
          // 如果是 centers 活动，则需要删除 centers 活动的生成任务
          if (weeklyPlan.centerItems) {
            weeklyPlan.centerItems.forEach(centerItem => {
              centerItemIds.push(centerItem.id)
            })
          }
          // 如果是大小组活动，则需要删除大小组活动的生成任务
          if (weeklyPlan.items) {
            weeklyPlan.items.forEach(item => {
              itemIds.push(item.id)
            })
          }
        })
        if (isCenter) {
          // 如果是重新生成 centers 活动，则需要删除所有 centers 活动的生成任务
          const centerTasks = allTasks.filter(x => centerItemIds.indexOf(x.itemId) !== -1)
          taskIds = centerTasks.map(x => x.taskId).join(',')
        } else {
          // 如果是重新生成大小组活动，则需要删除所有大小组活动的生成任务
          const lessonTasks = allTasks.filter(x => itemIds.indexOf(x.itemId) !== -1)
          taskIds = lessonTasks.map(x => x.taskId).join(',')
        }
      }
      // 如果存在任务，则停止任务
      if (taskIds) {
        Lessons2.stopGenerateTasks(taskIds)
      }
    },
    // 关闭 centers 弹窗
    closeCenterModal () {
      this.checkedCenterTags = []
      this.selectCentersVisible = false
      this.checkAll = false
      this.isIndeterminate = false
    },

    // 生成 centers 活动概览
    generateCentersOverView () {
      // 生成 centers 活动概览按钮埋点
      this.$analytics.sendEvent('web_unit_create3_center_select_confirm')
      this.$emit('generateCentersOverView', this.currentWeeklyPlan, this.checkedCenterTags)
      this.closeCenterModal()
    },

    // 确保周计划测评点更新完成
    toggleWeeklyMeasureUpdates () {
      this.$emit('toggleWeeklyMeasureUpdates')
    },

    // 打开设置弹窗
    openCenterSettingModal () {
      // 点击 centers 组设置按钮埋点
      this.$analytics.sendEvent('web_unit_create3_center_setting')
      // 每次打开获取 centers 组设置
      this.getCentersSetting()
      this.centerSettingVisible = true
    },

    // 获取 centers 组设置
    getCentersSetting () {
      this.centerSettingLoading = true
      this.$store.dispatch('curriculum/getCentersSetting', this.kToGrade2).then((res) => {
        this.centerSettingLoading = false
        this.centerSettingSystemType = res.systemType
        this.centerSettingCustomChanges = JSON.parse(JSON.stringify(res.centerNames || []))
      })
      .catch(error => {
        console.log(error)
        this.centerSettingLoading = false
      })
    },
    // 根据年龄组获取 centers 组
    getCentersByGrade(val) {
      var grade = val || this.baseInfo.grade
      let iTGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)']
      let psGrades = ['PS/PK (3-4)', 'TK (4-5)']
      let kToGrade2 = ['K (5-6)', 'Grade 1', 'Grade 2']
      // 如果 grade 是 Infant (0-1) 或 Young Toddler (1-2), Toddler (2-3) 则显示 IT
      if (iTGrades.includes(grade)) {
        this.centerTags = UnitPlanITCenterTags
        this.centerTagsI18n = UnitPlanITCenterTagsI18n
        this.iconMap = UnitPlanITCenterIconMap
        this.checkedCenterTags = UnitPlanITCenterTags
        this.checkedCenterTagsTemp = UnitPlanITCenterTags
        this.isShowCenterType = true
      } else if (psGrades.includes(grade)) {
        this.centerTags = UnitPlanCenterTags
        this.centerTagsI18n = UnitPlanCenterTagsI18n
        this.checkedCenterTags = UnitPlanCenterTags
        this.checkedCenterTagsTemp = UnitPlanCenterTags
        this.iconMap = UnitPlanCenterIconMap
      } else if (kToGrade2.includes(grade)) {
        this.centerTags = UnitPlanKCenterTags
        this.centerTagsI18n = UnitPlanKCenterTagsI18n
        this.checkedCenterTags = UnitPlanKCenterTags
        this.checkedCenterTagsTemp = UnitPlanKCenterTags
        this.iconMap = UnitPlanKCenterIconMap
      }
      this.checkedCenterTags = []
    },

    // 保存 centers 组设置
    saveCenterSetting () {
      // 点击保存 centers 组设置按钮埋点
      this.$analytics.sendEvent('web_unit_create3_center_setting_save')
      if (!this.centerSettingSystemType && this.centerSettingCustomChanges.length == 0) {
        this.$message.error(this.$t('loc.unitPlannerStep3CenterRotationPleaseSelectTheRotateCenters'))
        return
      }
      this.centerSettingVisible = false
      // 调接口保存 centers 组设置
      let params = {
        systemType: this.centerSettingSystemType,
        centerNames: this.centerSettingCustomChanges
      }
      Lessons2.saveCentersActivityChangeSetting({
        ...params,
        isStation: this.kToGrade2
      })
      .then(() => {
        this.$message.success(this.$t('loc.unitPlannerStep3SavedSuccessfully'))
        if (this.kToGrade2) {
          this.$store.commit('curriculum/SET_STATIONS_SETTING', params)
        } else {
          this.$store.commit('curriculum/SET_CENTERS_SETTING', params)
        }
      })
    },

    // 修改 centers 组设置
    changeCustomCenterSetting (center) {
      if (this.centerSettingCustomChanges.includes(center)) {
        this.centerSettingCustomChanges.splice(this.centerSettingCustomChanges.indexOf(center), 1)
      } else {
        this.centerSettingCustomChanges.push(center)
      }
    },

    // 关闭 centers 组设置弹窗
    closeCenterSettingModal () {
      this.centerSettingVisible = false
    },

    // 确认
    confirm (overview) {
      this.$set(overview, 'confirmed', true)
      this.$set(overview, 'edit', false)
      this.$emit('update')
      // 点击确认按钮埋点
      this.$analytics.sendEvent('cg_unit_create_three_confirm')
    },

    // 处理点击课程 idea 卡片事件
    handleLessonIdeaCardClickEvent (overview) {
      // 生成中，不处理，直接返回
      if (overview.generateLoading || this.generateCenterOverviewLoading || this.loading || overview.generating || this.generatingLessonIdea || this.generatingCenterIdea) {
        return
      }
      // 未确认状态打开编辑弹窗
      if (!overview.confirmed) {
        this.edit(overview)
      } else {
        // 已确认过的，直接打开详情
        this.generateDetail(overview)
      }
    },

    // 生成详情
    generateDetail (overview) {
      // 防护检查：如果正在批量生成，则不执行
      if (this.batchGenerateLoading) {
        return
      }

      // 点击生成详情按钮埋点
      if (overview.centerId) {
        this.$analytics.sendEvent('web_unit_create3_center_generate_detail')
      } else {
        this.$analytics.sendEvent('cg_unit_create_three_lessonone')
      }
      // 如果点击单个重新生成按钮，则停止当前课程的生成任务
      this.stopGenerateLessonTask(true, false, overview)
      let params = {
        unitId: this.unit.id,
        itemId: overview.id
      }
      // 跳转到课程详情页面
      let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-detail-cg-designer' : 'lesson-detail-cg' : 'lessonDetail'
      if (this.unit.adaptedType) {
        routeName = 'lesson-detail-cg-adapt'
      }
      let goNextPage = () => {
        this.$router.push({
          name: routeName,
          params: params
        })
      }
      // 如果是课程活动（非 center station），需要推荐课程模板且未弹出过推荐弹窗，则弹出推荐课程模板弹窗
      if (!overview.centerId && !this.baseInfo.showLessonTemplateTip && this.needRecommendLessonTemplate) {
        this.$emit('showLessonTemplateRecomendDialog', goNextPage, overview.id)
      } else {
        goNextPage()
      }
    },

    // 查看课程详情
    viewDetail (overview) {
      // 防护检查：如果正在批量生成，则不执行
      if (this.batchGenerateLoading) {
        return
      }
      
      // 点击查看课程详情按钮
      if (overview.centerId) {
        this.$analytics.sendEvent('web_unit_create3_center_view')
      } else {
        this.$analytics.sendEvent('cg_unit_create_lesson_view')
      }
      let params = {
        unitId: this.unit.id,
        itemId: overview.id
      }
      let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-detail-cg-designer' : 'lesson-detail-cg' : 'lessonDetail'
      if (this.unit.adaptedType) {
        routeName = 'lesson-detail-cg-adapt'
      }
      this.$router.push({
        name: routeName,
        params: params
      })
    },

    // 编辑
    edit (overview) {
      // 点击编辑按钮埋点
      if (overview.centerId) {
        this.$analytics.sendEvent('web_unit_create3_center_edit')
      } else {
        this.$analytics.sendEvent('web_unit_create3_group_edit')
      }
      if (overview.lessonId || overview.lesson) {
        this.$confirm(this.$t('loc.unitPlannerStep3GeneratedModificationsOverwriting'), this.$t('loc.confirmation'), {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel')
        }).then(() => {
          this.editLesson = {
          ...JSON.parse(JSON.stringify(overview)),
          rubrics: this.rubricsOptions && this.rubricsOptions.length > 0
            ? this.flattenRubrics(overview.rubrics || []).map(rubric => rubric.title)
            : []
          }
          this.editLessonRef = overview
          // 获取测评点
          this.loadingFrameworkLoading = true
          // 获取路由参数 unitId
          const unitId = this.$route.params.unitId
          this.$store.dispatch('curriculum/getMeasuresContainTopAndBottom',  {frameworkId:this.unit.baseInfo.frameworkId, unitId:unitId}).then((domains) => {
            this.domains = domains
            this.loadingFrameworkLoading = false
          })
          this.editLessonVisible = true
          // 编辑课程弹窗曝光事件
          if (overview.centerId) {
              this.$analytics.sendEvent('web_unit_create3_center_ideas_pop')
          } else {
            this.$analytics.sendEvent('web_unit_create3_group_ideas_pop')
          }
          })
          .catch(() => {
          })
      } else {
        this.editLesson = {
          ...JSON.parse(JSON.stringify(overview)),
          rubrics: this.rubricsOptions && this.rubricsOptions.length > 0
            ? this.flattenRubrics(overview.rubrics || []).map(rubric => rubric.title)
            : []
        }
        this.editLessonRef = overview
        // 获取测评点
        this.loadingFrameworkLoading = true
        // 获取路由参数 unitId
        const unitId = this.$route.params.unitId
        this.$store.dispatch('curriculum/getMeasuresContainTopAndBottom',  {frameworkId:this.unit.baseInfo.frameworkId, unitId:unitId}).then((domains) => {
          this.domains = domains
          this.loadingFrameworkLoading = false
        })
        this.editLessonVisible = true
        // 编辑课程弹窗曝光事件
        if (overview.centerId) {
          this.$analytics.sendEvent('web_unit_create3_center_ideas_pop')
        } else {
          this.$analytics.sendEvent('web_unit_create3_group_ideas_pop')
        }
      }
    },

    // 更新课程
    updateLesson () {
      // 点击保存课程 idea 按钮埋点
      if (this.editLesson.centerId) {
        this.$analytics.sendEvent('web_unit_create3_center_edit_confirm')
      } else {
        this.$analytics.sendEvent('web_unit_create3_group_edit_confirm')
      }
      this.$refs.editLessonFormRef.validate((valid) => {
        if (valid) {
          this.$set(this.editLessonRef, 'title', this.editLesson.title)
          this.$set(this.editLessonRef, 'activityType', this.editLesson.activityType)
          this.$set(this.editLessonRef, 'measures', this.editLesson.measures)
          this.$set(this.editLessonRef, 'description', this.editLesson.description)
          this.$set(this.editLessonRef, 'adaptedLesson', null)
          this.$set(this.editLessonRef, 'lessonId', null)
          this.$set(this.editLessonRef, 'lesson', null)
          this.$set(this.editLessonRef, 'confirmed', true)
          // 检查并更新单元改编状态
          this.checkAndUpdateUnitAdaptedStatus()
          // 检查所有周计划下是否都是改编课程
          this.checkAndUpdatePlanAdaptedStatus()
          // 更新校训
          let rubrics = tools.filterRubrics(this.editLesson.rubrics, this.rubricsOptions)
          this.$set(this.editLessonRef, 'rubrics', rubrics)
          this.$set(this.editLessonRef, 'lessonTemplateType', null)
          // 更新课程概览
          let items = []
          items.push(this.editLessonRef)
          let params = {
            items: items,
            frameworkId: this.frameworkId,
            planId: this.editLessonRef.planId,
            clearItemLesson: true, // 清除课程信息
            unitId: this.unit.id // 单元 ID
          }
          // 重新判断当前 Unit 的生成进度
          params.progress = this.reJudgeUnitGenerateProgress()
          this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
            this.editLessonVisible = false
            // 编辑时停止当前课程的生成任务
            this.stopGenerateLessonTask(true, false, this.editLessonRef)
          }).catch(error => {
            this.editLessonVisible = false
            this.$message.error(error.message)
          })
        }
      })
    },

    // 重新判断当前 Unit 的生成进度
    reJudgeUnitGenerateProgress () {
      // 是否存在未确定的 Item
      let hasUnconfirmedItem = false
      // 是否存在未生成的 Item 的周次
      let hasUnGenerateItem = false
      // Unit 生成进度
      let progress = ''
      // 遍历所有周计划，如果有未确认的课程，则更新进度为 60%
      for (let i = 0; i < this.weeklyPlans.length; i++) {
        // 获取当前周计划
        let weeklyPlan = this.weeklyPlans[i]
        // 判断是否存在周次未生成大小组
        if (!weeklyPlan.items || weeklyPlan.items.length === 0) {
          hasUnGenerateItem = true
          break
        }
        // 如果周计划项存在，则判断是否有未确认的课程
        if (weeklyPlan.items && weeklyPlan.items.length > 0) {
          if (weeklyPlan.items.some(item => !item.confirmed)) {
            hasUnconfirmedItem = true
            break
          }
        }
        // 如果 centers 组周计划项存在，则判断是否有未确认的课程
        if (weeklyPlan.centerItems && weeklyPlan.centerItems.length > 0) {
          if (weeklyPlan.centerItems.some(item => !item.confirmed)) {
            hasUnconfirmedItem = true
            break
          }
        }
      }
      // 如果存在未确认的课程或未生成大小组的周次，则更新进度为 60%，否则不进行更新操作
      if (!this.baseInfo.progress || this.baseInfo.progress < 60 || hasUnconfirmedItem || hasUnGenerateItem) {
        progress = this.progressConstant.sixtyPercent
      } else if (!hasUnconfirmedItem) { // 如果不存在未确定的 Item，则更新进度为 80%
        progress = this.progressConstant.eightyPercent
      }
      return progress
    },

    // 改变课程类型
    changeActivityType (command) {
      // 拿到当前课程概览
      let overview = command.overview
      // 如果当前课程类型和要改变的课程类型一致，则直接返回
      let newActiveType = command.flag ? 'Small Group' : 'Large Group'
      if (newActiveType === overview.activityType) {
        return
      }
      // 如果已生成课程，给出提示
      if (overview.lessonId || overview.lesson) {
        this.$confirm(this.$t('loc.unitPlannerStep3GeneratedModificationsOverwriting'), this.$t('loc.confirmation'), {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel')
        }).then(() => {
          // 停止当前课程的生成任务
          this.stopGenerateLessonTask(true, false, overview)
          this.$set(overview, 'activityType', command.flag ? 'Small Group' : 'Large Group')
          // 清空课程
          this.$set(overview, 'lessonId', null)
          this.$set(overview, 'lesson', null)
          // 更新课程概览
          let items = []
          items.push(overview)
          let params = {
            items: items,
            frameworkId: this.frameworkId,
            planId: overview.planId,
            clearItemLesson: true, // 清除课程信息
            unitId: this.unit.id // 单元 ID
          }
          this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
          }).catch(error => {
            this.$message.error(error.message)
          })
        }).catch(() => {
        })
      } else {
        this.$set(overview, 'activityType', command.flag ? 'Small Group' : 'Large Group')
          // 清空课程
          this.$set(overview, 'lessonId', null)
          this.$set(overview, 'lesson', null)
          // 更新课程概览
          let items = []
          items.push(overview)
          let params = {
            items: items,
            frameworkId: this.frameworkId,
            planId: overview.planId,
            clearItemLesson: true, // 清除课程信息
            unitId: this.unit.id // 单元 ID
          }
          this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
          }).catch(error => {
            this.$message.error(error.message)
          })
      }
    },
    cancelUpdateLesson () {
      this.editLessonVisible = false
    },
    // 删除
    deletelesson (overview) {
      this.$confirm(this.$t('loc.unitPlannerStep3Confirmation6'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel')
      }).then(() => {
        // 清空数据
        this.$set(overview, 'title', undefined)
        this.$set(overview, 'deleted', true)
      }).catch(() => {
      })
    },
    // 重新生成
    regenerateLessonOverview (overview, day, index) {
      // 如果点击单个重新生成按钮，则停止当前课程的生成任务
      this.stopGenerateLessonTask(true, false, overview)
      this.$emit('regenerateLessonOverview', overview, day, index)
      this.$analytics.sendEvent('cg_unit_create_three_regenerate')
      // 检查并更新单元改编状态
      this.checkAndUpdateUnitAdaptedStatus()
      // 检查所有周计划下是否都是改编课程
      this.checkAndUpdatePlanAdaptedStatus()
    },
    regenerateCenterOverview (overview) {
      // 如果点击单个重新生成按钮，则停止当前课程的生成任务
      this.stopGenerateLessonTask(true, true, overview)
      this.$emit('regenerateCenterOverview', overview)

      this.$analytics.sendEvent('cg_unit_create_three_stationsideas')
    },
    getFrameworkDomains (frameworkId) {
      if (!frameworkId) {
        return
      }
      this.loadingFrameworkLoading = true
      // 获取路由参数 unitId
      const unitId = this.$route.params.unitId
      this.$store.dispatch('curriculum/getMeasuresContainTopAndBottom', {frameworkId:this.unit.baseInfo.frameworkId, unitId:unitId}).then(domains => {
        this.domains = domains
      })
    },
    // 测评点名字加描述
    measureNameDescription (abbr) {
      if (!abbr) {
        return ''
      }
      // 没有框架信息，直接返回
      if (!this.domains || this.domains.length === 0) {
        return ''
      }
      // 对应的测评点
      let measure = this.getMeasureByAbbr(this.domains, abbr)
      // 没有找到测评点，则直接返回缩写
      if (!measure) {
        return ''
      }
      // 拼接缩写和名称
      if (!measure.description || measure.description == 'null') {
        return `${measure.name}`
      }
      if (!measure.name || measure.name == 'null') {
        return `${measure.description}`
      }
      return `${measure.name}: ${measure.description}`
    },
    // 根据缩写获取测评点信息
    getMeasureByAbbr (domains, abbr) {
      if (!abbr || !domains || domains.length === 0) {
        return null
      }
      // 遍历框架
      for (let i = 0; i < this.domains.length; i++) {
        const domain = this.domains[i]
        // 遍历测评点
        for (let j = 0; j < domain.children.length; j++) {
          const measure = domain.children[j]
          if (measure.abbreviation && measure.abbreviation.trim() === abbr) {
            return measure
          }
        }
      }
      return null
    },
    handleCheckAllChange (val) {
        this.checkedCenterTags = val ? this.checkedCenterTagsTemp.slice(0, this.unitUsageModel.weeklyCenterCount) : []
        this.isIndeterminate = false
    },
    handleCheckedCentersChange (value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.centerTags.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.centerTags.length
    },

    // 扁平化校训
    flattenRubrics(rubrics) {
      if (!rubrics || !Array.isArray(rubrics)) {
        return [];
      }
      const result = [];
      rubrics.forEach(rubric => {
        // 递归添加子标准
        if (rubric.subStandards && rubric.subStandards.length > 0) {
          result.push(...this.flattenRubrics(rubric.subStandards));
        } else {
          // 添加主标准（没有子标准的情况）
          result.push(rubric);
        }
      });
      return result;
    }
  }
}
</script>

<style lang="less" scoped>

.icon-opt-button {
    height: 20px;
    padding: 0 0 0 0 !important;
}

.center-padding {
    text-align: center;
    padding: 0px 24px 0px 24px;
}

/deep/ .el-tag.el-tag--info {
    color: #676879;
}

.deleted-tip {
  height: 100px;
}

::v-deep {
  .update-lesson-measure-select {
    .el-select-dropdown {
      width: 560px;
    }
  }
}

.week-theme {
  border-left: 8px solid var(--color-primary);
  height: 18px;
  padding-left: 8px;
}

.week-table {
  white-space: pre-line;
  width: 100%;
  & tr, & td {
    border-collapse: collapse;
    border: 1px solid var(--color-border);
    padding: 8px 16px;
  }
  & td:first-child {
    background-color: var(--color-table-background);
    border-top-left-radius: 4px;
    font-weight: 600;
    text-align: center;
    width: 110px;
  }
}

.lesson-overview-card {
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
}

.lesson-overview-box {
  height: 276px;
}

.lesson-overview-content {
  height: 200px;
}

/deep/ .lessson-overview-desc {
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-line;
}

/* 周一至周五颜色 */
@week-1-color: #10B3B7;
@week-1-color-background: #10B3B733;
@week-2-color: #F2994A;
@week-2-color-background: #F2994A33;
@week-3-color: #67C23A;
@week-3-color-background: #67C23A33;
@week-4-color: #F56C6C;
@week-4-color-background: #F56C6C33;
@week-5-color: #9B51E0;
@week-5-color-background: #9B51E033;
@center-color: #86D0AB;

.week-col {
  position: relative;
}

.week-col:after {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  content: '';
  border-radius: 8px 8px 0 0;
  position: absolute;
  width: 100%;
  height: 100%;
}

.week-1 {
  color: @week-1-color;
}
.week-2 {
  color: @week-2-color;
}
.week-3 {
  color: @week-3-color;
}
.week-4 {
  color: @week-4-color;
}
.week-5 {
  color: @week-5-color;
}
.center-col {
  color: var(--color-white);
  background: @center-color;
  border-radius: 8px 8px 0 0;
}

.week-1:after {
  background: @week-1-color-background;
}

.week-2:after {
  background: @week-2-color-background;
}

.week-3:after {
  background: @week-3-color-background;
}

.week-4:after {
  background: @week-4-color-background;
}

.week-5:after {
  background: @week-5-color-background;
}

/deep/ .centers-check-group-dialog .el-dialog__body{
    padding: 10px 20px;
}
.centers-check-group {
  background: var(--color-page-background-white);
  padding: 10px;
    margin-top: 10px;
}

.center-activities {
  margin-top: 24px;
}

/deep/ .week-theme-and-lesson.el-card {
  height: calc(100vh - 270px);
}
/deep/ .week-theme-and-lesson-center.el-card {
  height: calc(100vh - 270px);
}
.center-tag-box {
  height: 100px;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  margin-bottom: 16px;
}

.center-tag-box.active {
  background: var(--color-primary);
  color: var(--color-white);
  border: 2px solid var(--color-primary)
}
.setting-dialog {
  /deep/ .el-dialog {
    .el-dialog__body {
      padding: 20px 20px 20px 20px!important;
    }
  }
}
.small-large-group {
  /deep/ .el-card__body {
    padding-bottom: 0px!important;
  }
}
/deep/ .el-card.is-always-shadow {
  box-shadow: unset!important;
  -webkit-box-shadow: unset!important;
}

/deep/ .edit-lesson-dialog {
    & .el-dialog__body {
        padding: 0 20px;
    }
}

.rubric-option {
  .rubric-title {
    font-weight: bold;
    margin-bottom: 4px;
    white-space: normal;
    word-break: break-word;
  }
  .rubric-description {
    font-size: 12px;
    white-space: normal;
    word-break: break-word;
  }
}

/deep/ .update-lesson-rubrics-select {

  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
    top: calc(50% - 6px);
  }

  .el-select-dropdown {
    width: 560px;
  }
}
  .centers-header {
    flex-direction: column;
    gap: 12px;

    .centers-title-wrap {
      flex-direction: row;
      justify-content: space-between;
      width: 100%;

      .week-theme {
        flex: 1;  // 让标题占据剩余空间，实现靠左
      }

      .centers-settings-btn {
        margin-left: 12px;
      }
    }

    .centers-action-wrap {
      width: 100%;
      justify-content: flex-end;
    }
  }
</style>
