<template>
  <div>
    <el-popover placement="bottom" width="500" :visible-arrow="true" v-model="showPopoverGuide"
      popper-class="unit-prompt-history-guide-popover" trigger="manual">
      <div class="text-white">
        <!-- 引导文字 -->
        <div class="word-break text-left">
          <!-- 用户引导内容 -->
          <span class="font-size-16 font-weight-400 line-height-24 display-inline-block add-margin-b-10">{{
            $t('loc.curriculumPromptHistoryGuideTip') }}</span>
        </div>
        <div class="display-flex flex-justify-end gap-6 align-items">
          <el-button type="primary" @click="hideGuide()">{{ $t('loc.gotIt') }}</el-button>
        </div>
      </div>
      <div slot="reference">
        <el-button type="primary" plain size="medium"
          class="display-flex align-items curriculum-button-padding"
          :disabled="unitProgress > 20"
          :class="{'curriculum-button-padding': isCurriculumPlugin}"
          ref="historyBtn"
          @click="openDrawer"
          icon="lg-icon lg-icon-history font-size-20">
          {{ $t('loc.curriculumPromptHistory') }}
        </el-button>
      </div>
    </el-popover>

    <el-drawer :title="drawerTitle" :direction="direction" :before-close="closeDrawer"
      :size="isMobile ? '95%' : '55%'" :append-to-body="true" :visible.sync="drawer">
      <div class="lg-margin-bottom-16">
        {{ drawerTip }}
      </div>
      <div class="lg-scrollbar" v-infinite-scroll="loadData" :infinite-scroll-disabled="!haveMore"
        style="height: calc(100vh - 96px)">
        <!-- Unit 类型展示 -->
        <template v-if="promptHistoryType === PROMPT_HISTORY_TYPE.UNIT_OVERVIEW">
          <div v-for="(item, index) in history" :key="index" class="prompt-history-item">
            <!-- 时间及操作按钮 -->
            <div class="display-flex flex-space-between">
            <div class="title-font-14 prompt-history-create-time">{{ $moment.utc(item.createAtUtc).local().format('MM/DD/YYYY, h:mm A') }}</div>
              <div class="display-flex align-items">
                <!-- 删除按钮 -->
                <el-tooltip effect="dark" :content="$t('loc.delete')" placement="top">
                <i class="delete-btn lg-icon lg-icon-delete lg-color-danger font-size-20" @click="deletePromptHistory(item)"></i>
                </el-tooltip>
              <el-button type="primary" size="small" @click="usePromptHistory(item)">{{ $t('loc.curriculumPromptHistoryUse') }}</el-button>
              </div>
            </div>
            <!-- 标题 -->
            <div class="title-font-14 lg-color-text-primary lg-margin-left-20 lg-margin-top-8">{{ item.title }}</div>
            <!-- 描述 -->
            <div class="lg-margin-left-20 lg-margin-top-8 space-pre-line">
              <TextViewer :text="item.description"></TextViewer>
            </div>
            <!-- 年龄组、周信息 -->
            <div class="display-flex lg-margin-left-20 lg-margin-top-16" style="gap: 8px;">
              <div class="prompt-history-grade-tag lg-margin-right-8">{{ item.grade }}</div>
              <div class="prompt-history-weeks-tag">Weeks: {{ item.weekCount }}</div>
              <!-- 课堂类型标签 -->
              <div class="prompt-history-teaching-mode-tag" v-if="showUnitClassroomType(item)">
                {{ getClassroomTypeLabel(item.classroomType) }}
              </div>
            </div>
            <!-- 框架信息 -->
            <div class="display-flex align-items lg-margin-left-20 lg-margin-top-8">
              <div>{{ item.frameworkName }}</div>
              <el-divider direction="vertical"></el-divider>
              <div>
                {{ item.useDomain ? $t('loc.unitPlannerStep1MeasureAutoAdapt') :
                $t('loc.unitPlannerStep1MeasureSpecified') }}
              </div>
            </div>
            <!-- 测评点、领域信息 -->
            <div class="lg-margin-left-20 lg-margin-top-8">
              <el-tag type="info" v-for="(measure, index) in item.useDomain ? item.domains : item.measures" :key="index"
                size="mini" class="lg-margin-right-8 lg-margin-bottom-8">{{ measure.abbreviation }}</el-tag>
            </div>
          </div>
        </template>
        <!-- Lesson 类型展示 -->
        <template v-if="promptHistoryType === PROMPT_HISTORY_TYPE.CREATE_LESSON">
          <div v-for="(item, index) in history" :key="index" class="prompt-history-item">
            <!-- 时间及操作按钮 -->
            <div class="display-flex flex-space-between">
              <div class="title-font-14 prompt-history-create-time">{{
                $moment.utc(item.createAtUtc).local().format('MM/DD/YYYY, h:mm A') }}</div>
              <div class="display-flex align-items">
                <!-- 删除按钮 -->
                <el-tooltip effect="dark" :content="$t('loc.delete')" placement="top">
                  <i class="delete-btn lg-icon lg-icon-delete lg-color-danger font-size-20"
                    @click="deletePromptHistory(item)"></i>
                </el-tooltip>
                <el-button type="primary" size="small" @click="usePromptHistory(item)">{{
                  $t('loc.curriculumPromptHistoryUse') }}</el-button>
              </div>
            </div>
            <!-- 活动描述 -->
            <div class="lg-margin-left-20 lg-margin-top-8 text-viewer-text-pre-wrap" v-if="item.activityDescription">
              <TextViewer :text="item.activityDescription" :isHtml="true"></TextViewer>
            </div>
            <!-- 年级信息和框架信息 -->
            <div class="display-flex lg-margin-left-20 lg-margin-top-16" style="gap: 8px;">
              <div class="prompt-history-grade-tag">{{ item.grade }}</div>
              <div class="prompt-history-weeks-tag">{{ item.frameworkName }}</div>
              <div class="prompt-history-weeks-tag" v-if="isWeeklyPlan && item.centerThemeName">{{ item.centerThemeName }}</div>
              <!-- 课堂类型标签 -->
              <div class="prompt-history-teaching-mode-tag" v-if="showLessonClassroomType(item)">
                {{ getClassroomTypeLabel(item.classroomType) }}
              </div>
            </div>
            <!-- 测评点、领域信息 -->
            <div class="lg-margin-left-20 lg-margin-top-8">
              <el-tag type="info" v-for="(measure, index) in item.useDomain ? item.domains : item.measures" :key="index"
                size="mini" class="lg-margin-right-8 lg-margin-bottom-8">{{ measure.abbreviation }}</el-tag>
            </div>
          </div>
        </template>
        <template v-if="promptHistoryType === PROMPT_HISTORY_TYPE.ADAPT_LESSON">
          <div v-for="(item, index) in history" :key="index" class="prompt-history-item">
            <!-- 时间及操作按钮 -->
            <div class="display-flex flex-space-between">
              <div class="title-font-14 prompt-history-create-time">{{
                $moment.utc(item.createAtUtc).local().format('MM/DD/YYYY, h:mm A') }}</div>
              <div class="display-flex align-items">
                <!-- 删除按钮 -->
                <el-tooltip effect="dark" :content="$t('loc.delete')" placement="top">
                  <i class="delete-btn lg-icon lg-icon-delete lg-color-danger font-size-20"
                    @click="deletePromptHistory(item)"></i>
                </el-tooltip>
                <el-button type="primary" size="small" @click="usePromptHistory(item)">{{
                  $t('loc.curriculumPromptHistoryUse') }}</el-button>
              </div>
            </div>
            <!-- 活动描述 -->
            <div class="lg-margin-left-20 lg-margin-top-8 text-viewer-text-pre-wrap" v-if="item.activityDescription">
              <div class="title-font-14 font-weight-600">Activity Description:</div>
              <TextViewer :text="item.activityDescription"></TextViewer>
            </div>
            <!-- 适配想法 -->
            <div class="lg-margin-left-20 lg-margin-top-8 text-viewer-text-pre-wrap" v-if="item.adaptationIdeas">
              <div class="title-font-14 font-weight-600">Personalization & Adaptation:</div>
              <TextViewer :text="item.adaptationIdeas"></TextViewer>
            </div>
            <!-- 年级信息和框架信息 -->
            <div class="display-flex lg-margin-left-20 lg-margin-top-16" style="gap: 8px;">
              <div class="prompt-history-grade-tag lg-margin-right-8">{{ item.grade }}</div>
              <div class="prompt-history-weeks-tag">{{ item.frameworkName }}</div>
              <div class="prompt-history-weeks-tag" v-if="isWeeklyPlan && item.centerThemeName">{{ item.centerThemeName }}</div>
              <!-- 课堂类型标签 -->
              <div class="prompt-history-teaching-mode-tag" v-if="showLessonClassroomType(item)">
                {{ getClassroomTypeLabel(item.classroomType) }}
              </div>
            </div>
            <!-- 测评点、领域信息 -->
            <div class="lg-margin-left-20 lg-margin-top-8">
              <el-tag type="info" v-for="(measure, index) in item.useDomain ? item.domains : item.measures" :key="index"
                size="mini" class="lg-margin-right-8 lg-margin-bottom-8">{{ measure.abbreviation }}</el-tag>
            </div>
          </div>
        </template>

        <div v-show="history.length == 0 && !loading">
          <LgEmptyPage :text="$t('loc.curriculumPromptHistoryEmptyTip')"/>
        </div>
        <div v-loading="loading" v-if="loading" style="height: 100px;"></div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import LgEmptyPage from '@/components/LgEmptyPage.vue'
import TextViewer from './TextViewer.vue'
import { mapState } from 'vuex'
import responsiveMixin from '@/mixins/responsive'
import tools from '@/utils/tools'

// 历史记录类型枚举
const PROMPT_HISTORY_TYPE = {
  UNIT_OVERVIEW: 'UNIT_OVERVIEW',
  CREATE_LESSON: 'CREATE_LESSON',
  ADAPT_LESSON: 'ADAPT_LESSON'
}

export default {
  name: 'UnitPromptHistory',
  components: {
    LgEmptyPage,
    TextViewer
  },
  mixins: [responsiveMixin],
  props: {
    showPromptPanel: {
      type: Boolean,
      default: false
    },
    promptHistoryType: {
      type: String,
      default: PROMPT_HISTORY_TYPE.UNIT_OVERVIEW,
      validator: function(value) {
        return Object.values(PROMPT_HISTORY_TYPE).includes(value)
      }
    },
    unitProgress: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      drawer: false, // 是否显示抽屉
      direction: 'rtl', // 抽屉方向
      history: [], // 历史记录
      loading: false, // 是否正在加载
      pageSize: 10, // 每页大小
      pageNum: 1, // 当前页
      haveMore: true, // 是否还有更多
      showGuide: false, // 是否显示引导
      total: 0, // 总数
      PROMPT_HISTORY_TYPE,
      hasPromptHistory: false // 是否存在单元历史记录
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.showGuide = this.guideFeatures && this.guideFeatures.showUnitPromptHistoryGuide && (this.promptHistoryType === 'UNIT_OVERVIEW' ||  !this.isCurriculumPlugin)
      // 如果显示引导，则获取单元历史记录,判断是否存在单元历史记录
      if (this.showGuide) {
        this.getUnitPromptHistory()
      }
    })
  },
  created () {
  },
  computed: {
    ...mapState({
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      guideFeatures: state => state.common.guideFeatures
    }),
    // 计算popover是否显示
    showPopoverGuide: {
      get() {
        return this.showPromptPanel && this.showGuide && this.hasPromptHistory;
      },
      set() {
      }
    },
    // 获取当前类型的配置
    config () {
      if (this.promptHistoryType === PROMPT_HISTORY_TYPE.CREATE_LESSON) {
        return {
          get: $api.urls().getCreateLessonPromptHistory,
          delete: $api.urls().deleteCreateLessonPromptHistory,
          title: this.$t('loc.lessonCreationPromptHistory'),
        }
      }
      if (this.promptHistoryType === PROMPT_HISTORY_TYPE.ADAPT_LESSON) {
        return {
          get: $api.urls().getAdaptLessonPromptHistory,
          delete: $api.urls().deleteAdaptLessonPromptHistory,
          title: this.$t('loc.lessonAdaptationPromptHistory'),
        }
      }
      // UNIT_OVERVIEW 类型
      return {
          get: $api.urls().getUnitPromptHistory,
          delete: $api.urls().deleteUnitPromptHistory,
          title: this.$t('loc.curriculumPromptHistory'),
        }
    },
    // 抽屉标题
    drawerTitle () {
      return this.config.title
    },
    // 抽屉提示
    drawerTip () {
      return this.$t('loc.curriculumPromptHistoryTip')
    },
    // 判断是不是在周计划中
    isWeeklyPlan () {
      return !!this.$route.path && this.$route.path.indexOf('weekly-lesson-planning') > -1
    },
  },
  methods: {
    loadData (clearAll = false) {
      if (this.loading || !this.haveMore || !this.drawer) {
        return
      }
      if (clearAll) {
        this.history = []
        this.pageNum = 1
      }
      this.loading = true
      this.$axios.get(this.config.get, {
        params: {
          pageSize: this.pageSize,
          pageNum: this.pageNum
        } 
      })
      .then(res => {
        this.history = this.history.concat(res.items)
        this.total = res.total
        this.pageNum++
        this.loading = false
        this.haveMore = this.history.length < this.total
      })
      .catch(() => {
        this.loading = false
        this.haveMore = false
      })
    },

    // 隐藏引导
    hideGuide () {
      this.showGuide = false
      const guideFeatures = this.guideFeatures
      guideFeatures.showUnitPromptHistoryGuide = false
      this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', guideFeatures)
      // 发起请求隐藏引导
      let result = { 'features': ['UNIT_PROMPT_HISTORY_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then()
    },

    // 打开抽屉
    openDrawer () {
      // 创建课程打开 prompt 历史埋点
      if (this.$route.name === 'AddLesson' && this.promptHistoryType === PROMPT_HISTORY_TYPE.CREATE_LESSON) {
        this.$analytics.sendEvent('cg_lesson_plan_click_cre_prompt')
      } else if (this.$route.name === 'AddLesson' && this.promptHistoryType === PROMPT_HISTORY_TYPE.ADAPT_LESSON) {
        this.$analytics.sendEvent('cg_lesson_plan_click_adp_prompt')
      } else if (this.promptHistoryType === PROMPT_HISTORY_TYPE.UNIT_OVERVIEW) {
        this.$analytics.sendEvent('cg_unit_create_history')
      }
      this.drawer = true
      this.loadData(true)
      // 重置按钮状态
      if (this.$refs.historyBtn) {
        // 使用 $el 访问按钮的 DOM 元素，然后调用原生的 blur 方法
        this.$refs.historyBtn.$el.blur();
      }
    },

    // 关闭抽屉
    closeDrawer () {
      this.drawer = false
      this.loading = false
      this.haveMore = true
    },

    // 使用历史记录
    usePromptHistory (item) {
      this.$emit('usePromptHistory', item)
      this.closeDrawer()
    },

    // 删除历史记录
    deletePromptHistory (item) {
      // 删除 prompt 历史埋点
      if (this.$route.name === 'AddLesson' && this.promptHistoryType === PROMPT_HISTORY_TYPE.CREATE_LESSON) {
        this.$analytics.sendEvent('cg_lesson_plan_click_cre_del')
      } else if (this.$route.name === 'AddLesson' && this.promptHistoryType === PROMPT_HISTORY_TYPE.ADAPT_LESSON) {
        this.$analytics.sendEvent('cg_lesson_plan_click_adp_del')
      }

      this.$confirm(this.$t('loc.curriculumPromptHistoryDeleteTip'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        // 确认删除 prompt 历史埋点
        if (this.$route.name === 'AddLesson' && this.promptHistoryType === PROMPT_HISTORY_TYPE.CREATE_LESSON) {
          this.$analytics.sendEvent('cg_lesson_plan_click_cre_del_confirm')
        } else if (this.$route.name === 'AddLesson' && this.promptHistoryType === PROMPT_HISTORY_TYPE.ADAPT_LESSON) {
          this.$analytics.sendEvent('cg_lesson_plan_click_adp_del_confirm')
        }
        this.$axios.post(this.config.delete, {}, {
          params: {
            id: item.id,
          }
        }).then(() => {
          this.haveMore = true
          this.loadData(true)
        })
      })
    },
    // 获取单元历史记录
    async getUnitPromptHistory() {
      // 使用 $api.urls().getUnitPromptHistory 获取接口地址
      const res = await this.$axios.get($api.urls().getUnitPromptHistory, {
        params: {
          pageSize: 1,
          pageNum: 1
        }
      })
      // 根据 total 判断是否有历史数据
      if (res.total <= 0) {
        this.hasPromptHistory = false
      } else {
        this.hasPromptHistory = true
      }
    },
    // 获取课堂类型标签文本
    getClassroomTypeLabel(classroomType) {
      if (!classroomType) return this.$t('loc.classroomTypeInPerson')
      
      switch (classroomType.toUpperCase()) {
        case 'IN_PERSON':
          return this.$t('loc.classroomTypeInPerson')
        case 'VIRTUAL':
          return this.$t('loc.classroomTypeVirtual')
        default:
          return classroomType
      }
    },
    showLessonClassroomType (item) {
      return !item.centerThemeName && tools.isK12AgeGroup(item.grade)
    },
    showUnitClassroomType (item) {
      return tools.isK12AgeGroup(item.grade)
    },
  }
}
</script>
<style scoped lang="less">
/deep/ .el-drawer__header {
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
  color: var(--color-text-primary);
  padding: 24px 24px 0;
  margin-bottom: 8px;
}

/deep/ .el-drawer__body {
  padding-left: 24px;
  overflow: hidden;
}

.prompt-history-item {
  margin-right: 24px;
  padding: 8px;
  border-bottom: 1px solid var(--color-border);
}

.prompt-history-item:hover {
  background-color: var(--color-table-header-background);
  /deep/.delete-btn {
    display: block;
  }
}

.prompt-history-create-time {
  color: var(--color-text-placeholder);
}

.prompt-history-create-time:before {
  content: '';
  height: 12px;
  width: 12px;
  display: inline-block;
  background-color: var(--color-primary);
  border-radius: 50%;
  margin-right: 6px;
}

.prompt-history-grade-tag, .prompt-history-weeks-tag, .prompt-history-teaching-mode-tag {
    background: #DDF2F3;
    border-radius: 27px;
    padding: 4px 10px;
}

.delete-btn {
  display: none;
  margin-right: 12px;
  cursor: pointer;
}

.curriculum-button-padding {
  padding: 0 6px !important;
}
</style>
<style lang="less">
.text-viewer-text-pre-wrap {
  .text-viewer-text {
    white-space: pre-wrap;
  }
}
.el-popper.unit-prompt-history-guide-popover {
  background: var(--color-ai-assistant);
  color: var(--color-white);
  padding: 24px;
  border: none;

  &.el-popper[x-placement^=left] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^=bottom] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }
  .el-button {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
    color: var(--color-ai-assistant);
  }

  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
  }
}
</style>