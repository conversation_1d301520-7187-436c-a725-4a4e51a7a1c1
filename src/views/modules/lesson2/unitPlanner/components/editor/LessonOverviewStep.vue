<template>
  <div class="display-flex add-margin-t-16 w-full lesson-overview-step" style="gap: 14px" :class="{'mobile-flex-column': isMobile}" >
    <!-- 左侧操作 -->
    <div class="display-flex flex-direction-col gap-16" :class="{'mobile-full-width': isMobile}">
      <!--DEI 以及周数据-->
      <div class="display-flex flex-direction-col" :class="{'mobile-full-width': isMobile}" style="position:sticky;top: 0px;">
        <!-- 切换周的标签 -->
        <div class="display-flex flex-direction-col lg-scrollbar" style="gap: 16px; position:sticky;top: 0px;" :style="{'max-height': (promptDebugging ? (leftHeight - 200) : leftHeight) +'px', 'width': promptDebugging && !isMobile ? '100%': isMobile ? '100%' : '290px'}">
          <div v-if="false && unitId && (unitInfo.learnerProfileId || unitInfo.baseInfo.learnerProfileId) || deiOpen" class="lg-card lg-pa-16" :style="{'width': promptDebugging && !isMobile ? '33vw': isMobile ? '100%' : '280px'}">
            <!-- DEI Best Practice 标签 -->
            <div v-if="deiOpen" class="lg-card-item font-size-16 font-bold display-flex justify-content-between align-items" :style="{'width': promptDebugging && !isMobile ? '33vw': isMobile ? '100%' : '100%'}"
            :class="[unitId && (unitInfo.learnerProfileId || unitInfo.baseInfo.learnerProfileId)?'add-padding-b-12':'', isMobile ? 'mobile-flex-column' : '']"
            >
              <div class="display-flex align-items" style="overflow: hidden;gap: 5px" :class="{'mobile-margin': isMobile}">
                <span>{{ $t('loc.unitPlannerEDI') }}</span>
                <el-tooltip
                  v-if="DEIBestPractice && !deiLoading"
                  effect="dark"
                  popper-class="myTooltip"
                  :content="$t('loc.unitPlannerDEI2')"
                  placement="top"
                >
                  <i class="lg-icon lg-icon-info font-size-20 line-height-20 font-weight-400"></i>
                </el-tooltip>

              </div>
              <div class="display-flex align-items" :class="{'mobile-margin': isMobile}">
                <el-tooltip
                  v-if="deiGenerateFailed && !deiLoading"
                  effect="dark"
                  :content="$t('loc.generateDEIFailed')"
                  placement="top"
                >
                  <i class="el-icon-error text-danger" style="margin-right:3px"></i>
                </el-tooltip>
                <el-button v-if="DEIBestPractice && !deiLoading" @click="showDEIDetails(false)" type="primary" size="medium" :class="{'mobile-full-width': isMobile}">
                  <span>{{ $t('loc.unitPlannerDEI9') }}</span>
                </el-button>
                <el-button v-if="deiLoading" type="primary" plain style="width: 36px;height: 36px;padding: 10px;"
                          icon="el-icon-refresh-right font-bold"
                          :disabled="true"
                          :loading="deiLoading">
                </el-button>
                <el-button v-if="!DEIBestPractice && !deiLoading" @click="showDEIDetails(true)" type="primary" size="medium" :class="{'mobile-full-width': isMobile}">
                  <span>Generate</span>
                </el-button>
              </div>
            </div>
            <template v-if="unitId && (unitInfo.learnerProfileId || unitInfo.baseInfo.learnerProfileId)">
              <el-divider v-if="deiOpen"></el-divider>
              <!-- 单元学习者素养培养指南 -->
              <TeachingTipsManager :DEIBestPractice="DEIBestPractice"></TeachingTipsManager>
            </template>
          </div>
          <div
            v-loading="weeklyPlansLoading"
            class="lg-pointer display-flex flex-direction-col justify-content position-relative lg-border-radius-8 lg-card lg-padding-20"
            :class="{ 'cursor-disabled': changeWeekDisabled }"
            v-for="(weeklyPlan, index) in weeklyPlans"
            :style="{ 'border': currentWeekPlanIndex === index ? '2px solid var(--color-primary)': '' , 'width': promptDebugging && !isMobile ? '33vw': isMobile ? '100%' : '280px'}"
            @click="changeWeek(index)"
            :key="index">
            <span class="display-b lg-color-text-placeholder font-size-14 font-bold">{{ $t('loc.unitPlannerStep2WeekX', {week: weeklyPlan.week}) }} <i v-if="changeWeekDisabled" class="el-icon-loading lg-margin-left-8 font-size-16"></i></span>
            <span class="display-b font-size-18 font-bold text-default week-theme" :title="weeklyPlan.theme">{{ weeklyPlan.theme }}</span>
            <span class="display-b add-margin-t-4 font-size-14 color-676879 display-flex align-items" style="gap: 10px">
              <span>{{$t('loc.lessons')}}: {{completedActivities(weeklyPlan)}} / {{activitiesTotal(weeklyPlan)}} </span>
              <el-progress type="circle" :percentage="completedProgress(weeklyPlan)" :color="colorSuccess" :stroke-width="4" :show-text="false" :width="20" status="success"></el-progress>
            </span>
          </div>
        </div>
      </div>
      <!--prompt setting-->
      <div class="operation-panel" v-show="promptDebugging">
        <!-- Prompt 调试 -->
        <CollapseCard :theme="'no-header'" :collapsible="true">
          <!-- 标题 -->
          <template slot="header">
            <div class="display-flex justify-content-between">
              <div class="text-success">
                <span class="font-size-18 font-bold">Prompt Setting</span>
              </div>
            </div>
          </template>
          <!-- 内容 -->
          <PromptVersionEditor
            ref="promptEditorRef"
            :scenes="['LESSON_OVERVIEW', 'DEI_FOR_TEACHER', 'DEI_FOR_ADMINISTRATORS', 'DEI_FOR_ALL']"
            :showUpdatePromptBtn="true"
            :needGetCustomPrompt="false"
            :canAddTab="false"
            :showGenerateBtn="true"
            :generateFun="generateAllLessonOverviews"
            @batchGenerateResultsSuccess="batchGenerateResultsSuccess"
            @updatePromptId="updatePromptId"
          />
        </CollapseCard>
      </div>
    </div>
    <div class="display-flex" style="width: calc(100% - 305px);right: 0px" :class="{'mobile-full-width': isMobile, 'mobile-width-100': isMobile}">
      <!-- 右侧内容 -->
      <div class="w-full" :class="{'m-l-md': promptDebugging && !isMobile, 'mobile-full-width': isMobile}">
        <!-- 切换按钮 -->
        <div class="lg-tabs-small m-b-sm"
             v-show="promptDebugging && promptId && $refs.promptStatsResultRef && $refs.promptStatsResultRef.usageRecords.length > 0">
          <el-tabs v-model="currentView">
            <el-tab-pane :label="$t('loc.unitPlannerStep1LessonPlanIdeas')" name="RESULT"></el-tab-pane>
            <el-tab-pane label="Statistics Validation" name="STATS_RESULT"></el-tab-pane>
          </el-tabs>
        </div>
        <!-- 课程概览 -->
        <LessonOverviewDetail
            ref="lessonOverviewRef"
            v-show="currentView === 'RESULT'"
            :lessonOverviews="lessonOverviews"
            :centerOverviews="centerOverviews"
            :editable="true"
            :loading="generateLessonOverviewLoading"
            :generateCenterOverviewLoading="generateCenterOverviewLoading"
            :generateCenterOverviewDisabled="generateCenterOverviewDisabled"
            :currentWeeklyPlan="currentWeeklyPlan"
            :isMobile="isMobile"
            :batchGenerateLoading="batchGenerateLoading"
            @regenerateLessonOverview="regenerateLessonOverview"
            @regenerateCenterOverview="regenerateCenterOverview"
            @update="updateAllPlanItems(currentWeeklyPlan)"
            @regenerateAllLessonOverviews="regenerateAllLessonOverviews"
            @regenerateAllCenterOverviews="regenerateAllCenterOverviews"
            @generateCentersOverView="generateCentersOverView"
            @toggleWeeklyMeasureUpdates="toggleWeeklyMeasureUpdates"
            @showLessonTemplateRecomendDialog="showLessonTemplateRecomendDialog"
            v-loading="!currentWeeklyPlan || (currentWeeklyPlan && currentWeeklyPlan.generateLoading)">
            <!-- 反馈 -->
            <template slot="feedback">
                <!-- <div class="feedback-slot w-full display-flex justify-content-end add-margin-b-10"> -->
                    <!--反馈-->
                    <FeedbackForm :defaultFeedbackResult="defaultFeedbackResult"
                                  :feedbackStyle="setFeedbackStyle"
                                  styleClass="left lg-margin-right-12"
                                  :showFeedback="showFeedback"
                                  :showIcon="true"
                                  :showClose="false"
                                  :feedbackTitle="feedbackTitle"
                                  :feedbackSubmit="feedbackSubmit"
                                  :needFeedbackLevel="false"
                                  :feedbackInputPlaceholder="feedbackInputPlaceholder"
                                  :promptUsageRecordIds="promptUsageRecordIds"
                                  :showReminders="false"
                                  @clickFeedback="clickFeedback"/>
                <!-- </div> -->
            </template>
            <template slot="preview">
              <el-button type="primary" class="lg-z-index-10"  plain size="medium" @click="openPlannerPreviewDialog"
                         :disabled="generateLessonOverviewLoading || generatingLessonIdea || generatingCenterIdea || deiLoading"
                         :class="{'mobile-full-width': isMobile}">
                <template #icon>
                  <i class="el-icon el-icon-view font-bold"></i>
                </template>
                {{ $t('loc.unitOverview') }}
              </el-button>
            </template>
        </LessonOverviewDetail>
        <!-- 统计列表 -->
        <el-card v-show="currentView === 'STATS_RESULT' && promptDebugging">
          <PromptStatsResult
            ref="promptStatsResultRef"
            :promptId="promptId"
            @testCompleted="testCompleted"
            @testIncomplete="testIncomplete"
          />
        </el-card>
        <!-- 操作 -->
        <div class="m-t-sm display-flex justify-content-between lesson-overview-bottom-btn-group"
             v-show="currentWeeklyPlan && !currentWeeklyPlan.generateLoading"
             :class="{'mobile-flex-column': isMobile}">
          <div :style="{ visibility: isGenieCurriculumToUnitPlanThird ? 'hidden' : 'visible' }" :class="{'mobile-margin': isMobile}" class="back-btn-group">
            <!-- 返回 -->
            <el-button class="" @click="back()" :disabled="changeWeekDisabled" :class="{'mobile-full-width': isMobile}">{{$t('loc.unitPlannerStep2Back')}}</el-button>
          </div>
          <div class="m-b-sm update-btn-group" :class="{'mobile-flex-column': isMobile, 'mobile-margin': isMobile}">
            <!-- 保存 -->
            <el-button type="default" class="save-btn" @click="updateAllPlanItems(currentWeeklyPlan, true)"
                       :disabled="changeWeekDisabled || currentWeeklyPlan && currentWeeklyPlan.updateLoading"
                       v-if="currentWeeklyPlan && currentWeeklyPlan.items && currentWeeklyPlan.items.length > 0 && currentWeeklyPlan.items[0].id"
                       :class="{'mobile-full-width': isMobile, 'mobile-margin': isMobile}">
              {{$t('loc.unitPlannerStep1Save')}}
            </el-button>
            <!-- 下一周 -->
            <el-button type="primary" plain class="" @click="changeWeek(currentWeekPlanIndex+1)" :disabled="changeWeekDisabled" v-show="!isLastWeek && !allWeekCompleted && weeklyPlans.length > 1"
                       :loading="currentWeeklyPlan && currentWeeklyPlan.updateLoading" :class="{'mobile-full-width': isMobile, 'mobile-margin': isMobile}">{{$t('loc.unitPlannerStep3ProceedNextWeek')}}
            </el-button>
            <!-- 生成课程详情 -->
            <el-button v-if="false" type="default" class="ai-btn" @click="next(true)" :disabled="currentWeeklyPlan && currentWeeklyPlan.updateLoading || changeWeekDisabled" v-show="!allWeekCompleted"
                       :class="{'mobile-full-width': isMobile, 'mobile-margin': isMobile}"><i class="lg-icon lg-icon-generate"></i>{{$t('loc.unitPlannerStep3GenerateDetailedLessonPlans')}}
            </el-button>
            <!-- 批量生成课程按钮 -->
            <el-popover
              placement="top"
              width="400"
              trigger="manual"
              height="110"
              :visible-arrow="false"
              v-model="showGenerateDetailGuideTemp"
              popper-class="generate-detail-lesson-guide_temp">
              <div class="guide-content">
                <img :src="require('@/assets/img/lesson2/curriculum/generateLessonDetailGuide.png')" class="guide-img">
                <i class="el-icon-close close-icon" @click="showGenerateDetailGuideTemp = false"></i>
              </div>
                <el-button
                        slot="reference"
                        type="primary"
                        class="add-margin-l-10"
                        :disabled="currentWeeklyPlan && currentWeeklyPlan.updateLoading || changeWeekDisabled"
                        :loading="batchGenerateLoading"
                        @click="batchGenerateLessons"
                    >
                         <i class="lg-icon lg-icon-generate lg-margin-right-12"></i>
                         {{$t('loc.viewMyLessons')}}
                        <i  class="el-icon-arrow-right lg-margin-left-12"></i>
                </el-button>
              </el-popover>

            <el-button v-if="allWeekCompleted && !isCurriculumPlugin" type="primary" @click="publishUnit" :class="{'mobile-full-width': isMobile, 'mobile-margin': isMobile}">{{$t('loc.unitPlannerStep3Publish')}}</el-button>

            <!-- 可以直接发布 Magic -->
            <el-button v-show="allWeekCompleted" :style="allowPublicMagic ? '' : 'opacity: 0.5'" class="ai-btn" type="primary" v-if="isMC" @click="publishMagic" :class="{'mobile-full-width': isMobile, 'mobile-margin': isMobile}"> Submit Your Entry </el-button>

          </div>
          <div></div>
        </div>
      </div>
    </div>
    <!-- 引入UnitOverview 组件 -->
    <div class="unit-planner-over-view-dialog">
      <el-dialog :visible="plannerPreviewDialogShow" v-if="plannerPreviewDialogShow" :width="isMobile ? '95%' : '80%'"
                 :before-close="closePlannerPreviewDialog" :title="'Preview'" :close-on-click-modal="false"
                 custom-class="loading-class"
                 :close-on-press-escape="false"
                 :fullscreen="isMobile">
        <!-- 仅展示核心测评点 switch end -->
      <UnitDetailOverview :unitId="this.unitId"
                          :allWeeklyMeasureIdsUpdatesCompleted="allWeeklyMeasureIdsUpdatesCompleted"
                          :isMobile="isMobile"
                          ref="unit-plan-overview"/>
        <!-- 操作 -->
        <span slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button @click="closePlannerPreviewDialog" :class="{'mobile-full-width': isMobile}">{{ $t('loc.close') }}</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 查看/编辑 DEI  -->
    <el-dialog
      custom-class="view-edit-lesson-dialog"
      :before-close="closeDEIDetails"
      :title="$t('loc.unitPlannerDEI2')"
      :close-on-click-modal="false"
      :visible.sync="DEIDetailsShow"
      :width="isMobile ? '95%' : '80%'"
      :fullscreen="isMobile"
    >
      <div style="display: flex; flex-direction: column; align-items: center;margin-top:-10px" :class="{'mobile-padding': isMobile}">
        <div style="display: flex; align-items: center; justify-content: space-between; width: 100%; margin-bottom: 10px;" :class="{'mobile-flex-column': isMobile}">
          <div></div>
          <div style="display: flex; align-items: center;" :class="{'mobile-full-width': isMobile}">
            <el-tabs v-model="activeUser" @tab-click="handleClick" class="lg-tabs lesson-tabs h-full" style="margin-right: auto;" :class="{'mobile-full-width': isMobile}">
              <el-tab-pane :label="$t('loc.unitPlannerDEI6')" name="TEACHER"></el-tab-pane>
              <el-tab-pane :label="$t('loc.unitPlannerDEI7')" name="ADMINISTRATORS"></el-tab-pane>
            </el-tabs>
          </div>
          <div :class="{'mobile-margin': isMobile}">
            <el-tooltip effect="dark" content="Regenerate" placement="left">
              <el-button
                type="primary"
                plain
                icon="el-icon-refresh-right font-bold"
                style="width: 36px; height: 36px; padding: 10px"
                :loading="deiLoading"
                @click="regenerateDeiBestPracticeByUnitStream(activeUser)"
                :class="{'mobile-full-width': isMobile}"
              ></el-button>
            </el-tooltip>
          </div>
        </div>
        <!-- 可编辑内容 -->
        <div style=" width: 100%;margin-bottom: -40px">
          <el-form
            ref="unitOverviewFormRef"
            label-position="top"
            class="m-t-sm"
            v-if="true"
          >
            <!-- DEI Best Practice -->
            <el-form-item>
              <el-skeleton :rows="8" animated :loading="deiLoading && (DEIBestPractice && !DEIBestPractice.forTeacher && activeUser == 'TEACHER' || DEIBestPractice &&!DEIBestPractice.forAdmin && activeUser == 'ADMINISTRATORS')">
                <template>
                  <div style="height: 400px; overflow-y: auto" class="lg-scrollbar-hidden dei-content">
                    <editor v-model="deiDetails"/>
                  </div>
                </template>
              </el-skeleton>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button @click="closeDEIDetails">{{$t('loc.cancel')}}</el-button>
        <el-button type="primary" @click="saveDEIToUnit" :disabled="deiLoading">{{ $t('loc.save') }}</el-button>
      </span>
    </el-dialog>


    <!-- 发布Magic成功提示弹窗 -->
    <MagicSuccessDialog :unit="editUnit" :visible.sync="magicSuccessVisible" @close="successClose"></MagicSuccessDialog>
    <lesson-template-recomend
      ref="LessonTemplateRecomendRef"
      @recommendLessonTemplate="recommendLessonTemplate"
      @cancelRecommendLessonTemplate="cancelRecommendLessonTemplate"
      />
  </div>
</template>

<script>
import {aiApiUrl, platform} from '@/utils/setBaseUrl'
import { mapState } from 'vuex'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import { UnitPlanCenterTags, UnitPlanITCenterTags, UnitPlanKCenterTags } from '@/utils/constants'
import Lessons2 from '@/api/lessons2'
import LessonOverviewDetail from './LessonOverviewDetail.vue'
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import UnitDetailOverview from '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetailOverview.vue'
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import CollapseCard from "@/views/curriculum/components/card/CollapseCard.vue";
import PromptVersionEditor from "@/views/curriculum/prompt/components/editor/PromptVersionEditor.vue";
import PromptModelSelect from "@/views/curriculum/prompt/components/editor/PromptModelSelect.vue";
import {bus} from "@/utils/bus";
import PromptStatsResult from "@/views/curriculum/prompt/components/editor/PromptStatsResult.vue";
import MagicSuccessDialog from '@/views/magicCurriculum/components/btnGroup/MagicSuccessDialog.vue'
import TeachingTipsManager from '../learnerProfile/TeachingTipsManager.vue'
import LessonTemplateRecomend from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateRecomend.vue'
import { equalsIgnoreCase } from '@/utils/common'
import tools from '@/utils/tools'

export default {
  components: {
    PromptStatsResult,
    PromptVersionEditor,
    PromptModelSelect,
    CollapseCard,
    FeedbackForm,
    LessonOverviewDetail,
    UnitDetailOverview,
    Editor,
    MagicSuccessDialog,
    TeachingTipsManager,
    LessonTemplateRecomend
  },
  data () {
    return {
      magicSuccessVisible: false , // 发布到 Magic 成功弹窗
      editUnit: {},
      TemplatelessonOverviewRules: {}, // 周计划概览信息校验规则
      currentWeekPlanIndex: 0, // 当前查看的周计划索引
      isMobile: false, // 是否是移动设备
      currentWeeklyPlan: null, // 当前查看的周计划
      updateLoading: false, // 更新 Loading
      lessonOverviewData: '', // 课程概览数据
      currentView: 'RESULT', // 显示视图
      centerTags: UnitPlanCenterTags, // centers 组活动标签
      promptId: null, // Prompt ID
      leftHeight: 300, // 左侧菜单高度
      progressConstant: {
        sixtyPercent: 'WEEK_OVERVIEW_CONFIRMED',
        eightyPercent: 'LESSON_OVERVIEW_GENERATED'
      }, // 进度常量
      weeklyPlansLoading: false,
        defaultFeedbackResult: {
            feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
            feedBackResult: '',
            feedbackData: {
                feedback: undefined,
                feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
                feedbackLevel: [0, 0, 0, 0]
            }
      }, // 默认的反馈
      promptUsageRecordIds: [], // 消息回调的 promptUsageRecordId，后续用于反馈
      leavedPage: false, // 是否离开页面
      plannerPreviewDialogShow: false, // planner Preview 模态框显示示标志
      DEIDetailsShow: false, // dei 模态框显示标志
      activeUser: 'TEACHER', // 当前 dei 角色
      DEIBestPractice: null, // dei 数据
      DEIBestPracticeCopy: null, // dei 数据副本
      deiLoading: false, // dei 生成中
      deiGenerateFailed: false, // dei 生成失败
      deiContentChanged: false, // dei 内容是否改动
      deiGenerateCount: 0,// dei 版本
      loadingInstance: null,// loading 实例
      needSysCloseDialog: false,// 是否需要系统主动关闭弹窗
      guideVisible: false, // 引导框状态
      foreverCloseGuide: false, // 永久关闭引导框
      showUnitPlanLessonPlanIdeaOverview: false, // 是否需要展示拖拽功能引导
      allWeeklyMeasureIdsUpdatesCompleted: true, // 确保周计划测评点更新完成
      teachingTipsForLearnerProfile: null, // 学习者素养
      showGenerateDetailGuide: false, // 生成课程详情引导
      showGenerateDetailGuideTemp: false, // 生成课程详情引导
      hasBatchGenerated: false, // 是否已执行批量生成操作
      batchGenerateLessonFlag: false, //  是否批量生成课程
      batchGenerateLoading: false // 批量生成课程按钮的 loading 状态
    }
  },
  created () {
    bus.$on('lesson_overview:generateParams', (callback) => {
      const planId = this.getCurrentPlanId()
      // eslint-disable-next-line standard/no-callback-literal
      callback({
        unitId: this.unitId,
        planId: planId
      })
    })
    // 获取引导状态
    this.getNeedShowUnitOverviewGuide()

    this.$store.dispatch('magicCurriculum/checkCurrentUserToMagic')
    this.$store.dispatch('magicCurriculum/checkCurrentUserCompleteUnit')

    // 检查设备类型
    this.checkDeviceType()
    // 监听窗口大小变化
    window.addEventListener('resize', this.checkDeviceType)
  },
  mounted () {
    // 页面切换时，将 vuex 中的 dei 数据设置到 data 中
    if (this.unitInfo.deiBestPractice) {
      this.DEIBestPractice = JSON.parse(this.unitInfo.deiBestPractice)
      this.DEIBestPracticeCopy = JSON.stringify(this.DEIBestPractice)
    } else {
      this.$nextTick(() => {
        if (this.baseInfo.progress <= 40) {
          // 调用 dei 生成方法
          this.generateDeiBestPracticeByUnitStream()
        }
      })
    }
    if (this.unitInfo.teachingTipsForLearnerProfile) {
      this.teachingTipsForLearnerProfile = this.unitInfo.teachingTipsForLearnerProfile
    }
    this.$nextTick(() => {
      this.initLessonOverviewData()
      // 处理 center 标签
      this.handleCenterTags()
    })
    this.calLeftMenuHeight()
    window.addEventListener('resize', this.calLeftMenuHeight)
    // 第三步曝光事件埋点
    this.$analytics.sendEvent('web_unit_create3_exposure')
  },
  computed: {
    ...mapState({
      deiOpen: state => (state.common.open && state.common.open.deiOpen) || false,
      unitId: state => state.curriculum.unit.id,
      baseInfo: state => state.curriculum.unit.baseInfo,
      unitOverview: state => state.curriculum.unit.overview,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin,
      gptSetting: state => state.curriculum.gptSetting,
      promptDebugging: state => state.curriculum.promptDebugging, // Prompt 调试
      unitInfo: state => state.curriculum.unit, // 单元基本信息
      convertMappingAbbrToDomainAbbrMap: state => state.curriculum.convertMappingAbbrToDomainAbbrMap, // 领域缩写转换
      batchId: state => state.unit.batchId, // 批量任务 ID
      showSmallAndLargeGroupFlag: state => state.curriculum.showSmallAndLargeGroupFlag, // 是否显示 Small Group 和 Large Group
      unit: state => state.curriculum.unit, // 单元详情
      frameworkMeasureAbbSet: state => state.curriculum.frameworkMeasureAbbSet, // 框架顶底测评点缩写集合
      isGenieCurriculumToUnitPlanThird: state => state.curriculum.isGenieCurriculumToUnitPlanThird, // 是否由 genie 生成的 curriculum 进入的
      unitOverviewPreviewDragPlanIds: state => state.curriculum.unit.unitOverviewPreviewDragPlanIds, // 全局 unitOverviewPreviewDrag 拖拽时需要刷新的周计划 Id
      unitPlanDragItemCount: state => state.curriculum.unitPlanDragItemCount,
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      allowPublicMagic: state => state.magicCurriculum.allowPublicMagic,
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      currentUser: (state) => state.user.currentUser, // 当前用户
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      isCompleteUnit: state => state.magicCurriculum.isCompleteUnit, // 是否完成 unit
    }),
    // 单元是否有校训
    hasRubrics () {
      return this.baseInfo && this.baseInfo.newRubrics && this.baseInfo.newRubrics.length > 0
    },
    // 年龄组是否是 K-Grade 2
    kToGrade2 () {
      if (this.baseInfo && this.baseInfo.grade) {
        return ['K (5-6)', 'Grade 1', 'Grade 2'].includes(this.baseInfo.grade)
      }
      return false
    },
    // 年龄组是否是 K - Grade 12
    kToGrade12 () {
      if (this.baseInfo && this.baseInfo.grade) {
        return ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group'].includes(this.baseInfo.grade)
      }
      return false
    },
    // 是否需要批量推荐课程模板，全部活动都没有模板时走批量推荐流程
    needRecommendLessonTemplate () {
      return this.eduProtocolsTemplateApplyOpen && this.kToGrade12 && this.currentWeeklyPlan && this.currentWeeklyPlan.items && this.currentWeeklyPlan.items.every(item => !item.lessonTemplateType)
    },
    deiDetails: {
      // 根据当前角色获取 dei 数据
      get () {
        if (this.DEIBestPractice && this.activeUser === 'TEACHER') {
          return this.DEIBestPractice.forTeacher
        } else if (this.DEIBestPractice && this.activeUser === 'ADMINISTRATORS') {
          return this.DEIBestPractice.forAdmin
        }
      },
      // 根据当前角色设置 dei 数据
      set (value) {
        if (this.DEIBestPractice && this.activeUser === 'TEACHER') {
          return this.DEIBestPractice.forTeacher = value
        } else if (this.DEIBestPractice && this.activeUser === 'ADMINISTRATORS') {
          return this.DEIBestPractice.forAdmin = value
        }
      }
    },
      feedbackTitle () {
          return this.$t('loc.unitPlannerFeedback')
      },
      feedbackInputPlaceholder () {
          return this.$t('loc.unitPlannerFeedbackPlaceholder')
      },
      feedbackSubmit () {
          return this.$t('loc.unitPlannerFeedbackSubmit')
      },
    showFeedback () {
        // 判断 promptUsageRecordIds 中的每一个值，即对应的子数组都是存在值的
        return !this.weeklyPlansLoading && !this.loading && !this.generateCenterOverviewLoading && !this.generateLessonOverviewLoading && this.promptUsageRecordIds.length > 0
    },
    // 第一周的 center 组活动
    lastWeeklyPlanId () {
      if (this.currentWeekPlanIndex > 0) {
        let unit = this.$store.state.curriculum.unit
        let weeklyPlans = unit.weeklyPlans
        return weeklyPlans[this.currentWeekPlanIndex - 1].id
      }
      return ''
    },
    weeklyPlans: {
      get () {
        return this.$store.state.curriculum.unit.weeklyPlans
      },
      set (value) {
        this.$store.commit('curriculum/SET_WEEKLY_PLANS', value)
      }
    },
    planIds: {
      get () {
        return this.$store.state.curriculum.planIds
      },
      set (value) {
        this.$store.commit('curriculum/SET_WEEKLY_PLANIDS', value)
      }
    },
    // 课程活动概览
    lessonOverviews () {
      if (!this.currentWeeklyPlan) {
        return []
      }
      let items = this.currentWeeklyPlan.items
      return items || []
    },
    // centers 组课程活动概览
    centerOverviews () {
      if (!this.currentWeeklyPlan) {
        return []
      }
      let items = this.currentWeeklyPlan.centerItems
      // items 按照 Art 、Science、Social Studies、Math、Language Arts 排序
      if (items && items.length > 0) {
        items.sort((a, b) => {
          let aIndex = this.centerTags.indexOf(a.centerGroupName)
          let bIndex = this.centerTags.indexOf(b.centerGroupName)
          if (aIndex === -1 || bIndex === -1) {
            return 0
          }
          return aIndex - bIndex
        })
      }
      return items || []
    },
    // 生成课程概览 Loading
    generateLessonOverviewLoading () {
      return this.currentWeeklyPlan && this.currentWeeklyPlan.generateLessonOverviewLoading || this.weeklyPlans.some(item => item.generateLessonOverviewLoading)
    },
    // 生成 centers 组活动概览 Loading
    generateCenterOverviewLoading () {
      return (this.currentWeeklyPlan && this.currentWeeklyPlan.generateCenterOverviewLoading) || this.centerOverviews.some(item => item.generateLoading && !item.generating) || this.weeklyPlans.some(item => item.generateLessonOverviewLoading)
    },
    // 生成 centers 组活动概览按钮是否禁用
    generateCenterOverviewDisabled () {
      // 如果有正在生成的 centers 组活动概览，则禁用生成按钮
      return this.weeklyPlans.some(item => item.generateCenterOverviewLoading) || this.weeklyPlans.some(item => item.generateLessonOverviewLoading)
    },
    loading () {
      let hasLoadingLesson = this.lessonOverviews.some(item => item.generateLoading)
      return this.currentWeeklyPlan && this.currentWeeklyPlan.generateLessonOverviewLoading || hasLoadingLesson || this.weeklyPlans.some(item => item.generateLessonOverviewLoading)
    },
    // 周内 idea 全部确认
    weekConfirmed () {
      return function (weeklyPlan) {
        if (weeklyPlan && weeklyPlan.items && weeklyPlan.items.length > 0) {
          return weeklyPlan.items && !weeklyPlan.items.some(item => !item.confirmed) && weeklyPlan.centerItems && !weeklyPlan.centerItems.some(item => !item.confirmed) && (weeklyPlan.items.some(item => !item.lessonId) || weeklyPlan.centerItems.some(item => !item.lessonId))
        }
        return false
      }
    },
    // 可以切换的最后一周的索引
    canSwitchLastWeekIndex () {
      let total = this.weeklyPlans.length - 1
      for (let i = total; i >= 0; i--) {
        if (this.weekConfirmed(this.weeklyPlans[i])) {
          return i + 1
        }
      }
      return 0
    },
    // 是否是最后一周
    isLastWeek () {
      return this.currentWeekPlanIndex && this.weeklyPlans && this.currentWeekPlanIndex === this.weeklyPlans.length - 1
    },
    // 周课程全部生成
    weekCompleted () {
      return function (weeklyPlan) {
        if (weeklyPlan && weeklyPlan.items && weeklyPlan.items.length > 0) {
          return weeklyPlan.items && !weeklyPlan.items.some(item => !item.lessonId) && !weeklyPlan.items.some(item => !item.confirmed) &&
          weeklyPlan.centerItems && !weeklyPlan.centerItems.some(item => !item.lessonId) && !weeklyPlan.centerItems.some(item => !item.confirmed)
        }
        return false
      }
    },
    // 所有周课程全部生成
    allWeekCompleted () {
      return this.weeklyPlans && this.weeklyPlans.every(weeklyPlan => this.weekCompleted(weeklyPlan))
    },
    // 正在生成课程中
    generatingLessonIdea () {
      return this.lessonOverviews.some(x => x.generating)
    },
    // 正在生成 centers 活动中
    generatingCenterIdea () {
      return this.centerOverviews.some(x => x.generating) || this.generateCenterOverviewDisabled
    },
    // 是否可以切换周
    changeWeekDisabled () {
      return this.loading || this.generateCenterOverviewLoading || this.generatingLessonIdea || this.generatingCenterIdea || this.generateCenterOverviewLoading || (this.currentWeeklyPlan && this.currentWeeklyPlan.updateLoading)
    },
    // 总活动数
    activitiesTotal () {
      return function (weekPlan) {
        const items = weekPlan && (weekPlan.items || [])
        const centerItems = weekPlan && (weekPlan.centerItems || [])
        if (items.length <= 0 && centerItems.length <= 0) {
          return 0
        }
        return items.length + centerItems.length
      }
    },
    // 已完成的活动数
    completedActivities () {
      return function (weekPlan) {
        const items = weekPlan && (weekPlan.items || [])
        if (items.length <= 0) {
          return 0
        }
        return weekPlan.items.filter(item => item.lessonId && item.confirmed).length + weekPlan.centerItems.filter(item => item.lessonId && item.confirmed).length
      }
    },
    // 完成的进度值
    completedProgress () {
      return function (weekPlan) {
        const items = weekPlan && (weekPlan.items || [])
        const centerItems = weekPlan && (weekPlan.centerItems || [])
        if (items.length <= 0 && centerItems.length <= 0) {
          return 0
        }
        return Math.round((items.filter(item => item.lessonId).length + centerItems.filter(item => item.lessonId).length) / (items.length + centerItems.length) * 100)
      }
    },
    // 成功的颜色
    colorSuccess () {
      return '#67C23A'
    }
  },
  destroyed () {
    // 组件销毁后标记离开页面
    this.$set(this.currentWeeklyPlan, 'generateLessonOverviewLoading', false)
    this.leavedPage = true
    // 拖拽时数据清空
    this.$store.commit('curriculum/CLEAR_UNIT_PLAN_DRAG_ITEM_COUNT')
    this.$store.commit('curriculum/CLEAR_UNIT_OVERVIEW_PREVIEW_DRAG_PLAN_IDS')
    this.$store.commit('curriculum/SET_UNIT_OVERVIEW_PREVIEW_DRAG', false)
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.checkDeviceType)
    window.removeEventListener('resize', this.calLeftMenuHeight)
  },
  watch: {
    // 监听 DEI 数据,如果还未生成 DEI，且是 Curriculum 生成的，则自动生成 DEI
    'unit.deiBestPractice' (val) {
      if (!val && this.isGenieCurriculumToUnitPlanThird) {
        // 调用 dei 生成方法
        this.generateDeiBestPracticeByUnitStream()
      }
    },
    // 当前周计划变化时，生成课程概览
    currentWeeklyPlan: {
      handler (val) {
        if (val) {
          if (val.updateLoading) {
            this.showGenerateDetailGuideTemp = this.showGenerateDetailGuide
          }
          this.$store.dispatch('curriculum/setCurrentWeekPlan', val)
          this.updatePromptSourceList(val)

          // 检查路由参数中是否有 batchGenerate 参数，并且还未执行过批量生成操作
          if (this.$route.params.batchGenerate && !this.hasBatchGenerated) {
            this.batchGenerateLessons()
            this.hasBatchGenerated = true // 标记已执行过批量生成
          }
        }
      },
      immediate: true
    },
    // 监听dei内容变化
    DEIBestPractice: {
      handler (newValue, oldValue) {
        // 处理新值和旧值，去除 <p></p> 和 <br> 标签
        const processedNewValue = this.removeTags(JSON.stringify(newValue))
        const processedOldValue = this.removeTags(this.DEIBestPracticeCopy)
        // 只有在 deiLoading 为 false 时才监听属性值的变化
        if (!this.deiLoading) {
          if (processedNewValue !== processedOldValue) {
            // 返回true或者执行其他操作
            this.deiContentChanged = true
          } else {
            // 返回false或者执行其他操作
            this.deiContentChanged = false
          }
        }
      },
      deep: true // 深度监听对象内部值的变化
    },
    // 刷新页面时，拿到 vux 中的 dei 数据进行初始赋值
    'unitInfo.deiBestPractice' (newValue, oldValue) {
      if (newValue) {
        // 更新 dei 和 deiCopy
        this.DEIBestPractice = JSON.parse(newValue)
        this.DEIBestPracticeCopy = JSON.stringify(this.DEIBestPractice)
      }
    },
    // 实时监听学习者素养数据
    'unitInfo.teachingTipsForLearnerProfile' (newValue, oldValue) {
      if (newValue) {
        // 更新
        this.teachingTipsForLearnerProfile = newValue
      }
    },
    // 监听单元 ID 初始化完成后，初始化课程概览数据
    unitId: {
      handler (val) {
        // 初始化选择的课程
        if (val) {
          this.initLessonOverviewData()
        }
      }
    },
    // currentWeekPlanIndex: {
    //   handler (val) {
    //     if (this.lessonOverviews && this.lessonOverviews.length == 0) {
    //       this.generateLessonOverviewStream(this.currentWeeklyPlan)
    //     }
    //   }
    // },
    $route: {
      handler (val, oldVal) {
        // 如果路由参数中包含 week，则切换到对应的周
        let currentWeek = this.$route.params.week
        // 如果正在生成课程概览，则跳回路由
        if (this.changeWeekDisabled || !currentWeek || parseInt(currentWeek) > this.weeklyPlans.length) {
          return
        }
        // 存在当前周，切换周次
        if (currentWeek) {
          this.currentWeekPlanIndex = parseInt(currentWeek) - 1
          this.currentWeeklyPlan = this.weeklyPlans[this.currentWeekPlanIndex]
          this.isShowSmallAndLargeGroup()
          if (this.lessonOverviews && this.lessonOverviews.length == 0) {
            this.generateLessonOverviewStream(this.currentWeeklyPlan)
          }
        }
      }
    },

    unitPlanDragItemCount: {
      handler (val, oldVal) {
        if (val !== null) {
          if (!this.needSysCloseDialog) {
            return
          }
          if (val === 0) {
            this.needSysCloseDialog = false
            // 值等于0时主动关闭弹窗
            this.closePlannerPreviewDialog()
          }
        }
      },
      deep: true
    }
  },
  methods: {
    // allWeeklyMeasureIdsUpdatesCompleted 取反
    toggleWeeklyMeasureUpdates () {
      this.allWeeklyMeasureIdsUpdatesCompleted = !this.allWeeklyMeasureIdsUpdatesCompleted
    },

    // 手动打开 loading
    openDialog () {
      // 获取弹窗内容区域的 DOM 对象
      const targetElement = this.$el.querySelector('.loading-class')
      let options = {
        text: 'Loading',
        target: targetElement
      }
      this.loadingInstance = this.$loading(options)
    },

    // 处理课程 center 标签
    handleCenterTags () {
      let iTGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)']
      let psGrades = ['PS/PK (3-4)', 'TK (4-5)']
      let kToGrade2 = ['K (5-6)', 'Grade 1', 'Grade 2']
      if (iTGrades.includes(this.baseInfo.grade)) {
        this.centerTags = UnitPlanITCenterTags
      } else if (psGrades.includes(this.baseInfo.grade)) {
        this.centerTags = UnitPlanCenterTags
      } else if (kToGrade2.includes(this.baseInfo.grade)) {
        this.centerTags = UnitPlanKCenterTags
      }
    },
    // 更新 promptSourceList
    updatePromptSourceList (val) {
      if (val) {
        // 定义一个资源数组
        const sources = [
          { sourceStep: 3, sourceName: 'weekly theme', sourceValue: val.theme },
          { sourceStep: 3, sourceName: 'weekly overview', sourceValue: val.overview },
          { sourceStep: 3, sourceName: 'week number', sourceValue: val.week }
        ]
        // 更新数据
        this.$store.dispatch('curriculum/updatePromptSourcesList', {
          step: 2,
          sources: sources
        })
      }
    },
    removeTags (str) {
      // 使用正则表达式去除 <p></p> 和 <br> 标签
      return str.replace(/<p>|<\/p>|<br>/gi, '')
    },
    updatePromptId (promptId) {
      this.promptId = promptId
    },
    isShowSmallAndLargeGroup () {
      // 获取当前单元的年龄
      var grade = this.baseInfo.grade.trim()
      let showSmallAndLargeGroupGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)']
      // 判断该年龄是否包含在 showSmallAndLargeGroupGrades 中
      let showSmallAndLargeGroup = showSmallAndLargeGroupGrades.includes(grade)
      // 不在该年龄中才需要判断是否是历史数据
      if (!showSmallAndLargeGroup) {
        if (this.currentWeeklyPlan) {
          // 获取当前周的课程概览
          let items = this.currentWeeklyPlan.items
          // 判断该周概览中的 activityType 是否包含 'Small Group' 或 'Large Group'
          if (items && items.length > 0) {
            let showSmallAndLargeGroupFlag = items.some(item => item.activityType === 'Small Group' || item.activityType === 'Large Group')
            this.$store.commit('curriculum/SET_SHOW_SMALL_AND_LARGE_GROUP_FLAG', showSmallAndLargeGroupFlag)
          } else {
            this.$store.commit('curriculum/SET_SHOW_SMALL_AND_LARGE_GROUP_FLAG', false)
          }
        } else {
          this.$store.commit('curriculum/SET_SHOW_SMALL_AND_LARGE_GROUP_FLAG', false)
        }
      } else {
        this.$store.commit('curriculum/SET_SHOW_SMALL_AND_LARGE_GROUP_FLAG', true)
      }
    },
    // 判断 centerItems 和 items 有没有值
    hasValues (weeklyPlans) {
      // 检查centerItems是否有值
      const hasCenterItems = weeklyPlans && Array.isArray(weeklyPlans.centerItems) && weeklyPlans.centerItems.length > 0

      // 检查items是否有值
      const hasItems = weeklyPlans && Array.isArray(weeklyPlans.items) && weeklyPlans.items.length > 0
      return hasCenterItems || hasItems
    },

    // 重新生成 DEI
    async regenerateDeiBestPracticeByUnitStream (role) {
        await this.generateDeiBestPracticeByUnitStream(role)
    },

    // 生成 DEI
    async generateDeiBestPracticeByUnitStream (type, callback) {
        // 如果 deiOpen 未开启，则不生成 DEI
        if (!this.deiOpen) {
          return
        }
        this.DEIBestPracticeCopy = JSON.stringify(this.DEIBestPractice)
        this.deiLoading = true
        // 重置数据
        if (type && type === 'TEACHER') {
          this.$set(this.DEIBestPractice, 'forTeacher', null)
        }
        if (type && type === 'ADMINISTRATORS') {
          this.$set(this.DEIBestPractice, 'forAdmin', null)
        }
        // 生成单元概览参数

        let params = {
            unitId: this.unitId,
            type: type
        }
        let data = ''
        let deiGenerateCount = this.deiGenerateCount
        // 消息回调`
        let messageCallback = (message) => {
            // 如果离开页面，则不再执行
            if (this.leavedPage || deiGenerateCount !== this.deiGenerateCount) {
              data = ''
              return
            }
            // 更新数据
            data += message.data
            // 解析数据
            let parsedData = parseStreamData(data, [
              { key: 'forTeacher', name: 'For Teachers:' },
              { key: 'forAdmin', name: 'For Administrators/Program Leaders:' }
            ])[0]
            // 初始化 this.DEIBestPractice 为一个空对象
            if (!this.DEIBestPractice) {
              this.DEIBestPractice = {}
            }
            // 需要加粗的固定的标题
            const boldSentences = ['1. Identify and Dismantle Implicit Bias', '2. Build a Love and Strengths-based, Equitable Early Education',
              '3. Collaborate with Families and Communities', 'Reflection Questions:']
            // 更新 DEI
            if (parsedData.forTeacher) {
              boldSentences.forEach(sentence => {
                // 添加粗体
                const bolderSentence = `<strong>${sentence}</strong>`
                // 判断老师 DEI 是否包含该句子，如果包含时则需要将标题替换成加粗的标题
                if (parsedData.forTeacher.includes(sentence)) {
                  parsedData.forTeacher = parsedData.forTeacher.replace(sentence, bolderSentence)
                }
              })
              this.$set(this.DEIBestPractice, 'forTeacher', parsedData.forTeacher.trim().replace(/\n/g, '<br>'))
            }
            if (parsedData.forAdmin) {
              boldSentences.forEach(sentence => {
                // 添加粗体
                const bolderSentence = `<strong>${sentence}</strong>`
                // 判断管理员 DEI 是否包含该句子，如果包含时则需要将标题替换成加粗的标题
                if (parsedData.forAdmin.includes(sentence)) {
                  parsedData.forAdmin = parsedData.forAdmin.replace(sentence, bolderSentence)
                }
              })
              this.$set(this.DEIBestPractice, 'forAdmin', parsedData.forAdmin.trim().replace(/\n/g, '<br>'))
            }
        }
        return new Promise((resolve, reject) => {
            // 生成单元概览
            createEventSource($api.urls().generateDeiBestPracticeByUnitStream,null, messageCallback,'POST',params)
                .then((res) => {
                    // 如果离开页面，则不再执行
                    if (this.leavedPage || deiGenerateCount !== this.deiGenerateCount) {
                        return
                    }
                    // 更新 vuex 中的 dei 信息
                    this.$store.commit('curriculum/SET_DEI_BEST_PRACTICE', JSON.stringify(this.DEIBestPractice))
                    // 更新 unit 中的 dei 信息
                    let params = {
                      unitId: this.unitId,
                      ...this.unitOverview,
                      ...this.baseInfo,
                      progress: this.getProgress(this.baseInfo.progress),
                      deiBestPractice: JSON.stringify(this.DEIBestPractice),
                      teachingTipsForLearnerProfile: this.teachingTipsForLearnerProfile
                    }
                    if (this.unitInfo.learnerProfileId) {
                      params = { ...params, learnerProfileId: this.unitInfo.learnerProfileId, rubrics: this.unitInfo.rubrics }
                    }
                    if (this.unitInfo.teachingTipsForLearnerProfile) {
                      params = { ...params, teachingTipsForLearnerProfile: this.unitInfo.teachingTipsForLearnerProfile }
                    }
                    // 判断 Unit 的 coverMedias 是否为空，如果不为空时将 coverMedia 的 type 设置为 UPLOAD
                    if (params.coverMedias && params.coverMedias.length > 0) {
                      params.coverMedias.forEach(media => {
                        media.type = 'UPLOAD'
                      })
                    }
                    this.$axios.post($api.urls().updateCurriculumUnit, params).then((res) => {
                        // 将改变标志设置为 false
                        this.deiContentChanged = false
                        // 更新 deiCopy（用于判断内容变化）
                        this.DEIBestPracticeCopy = JSON.stringify(this.DEIBestPractice)
                        // 成功标志
                        this.$message.success(this.$t('loc.unitPlannerDEI3'))
                    }).catch(error => {
                      console.log(error)
                      this.$message.error(error.message)
                    })

                    // 生成结束
                    this.deiLoading = false
                    if (callback && typeof callback === 'function') {
                        callback()
                    }
                    resolve()
                })
                .catch(error => {
                    // 生成出错
                    // loading 状态设置为 false
                    this.deiLoading = false
                    // 失败状态设置为 true
                    this.deiGenerateFailed = true
                    this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                    if (callback && typeof callback === 'function') {
                        callback()
                    }
                    reject(error)
                })
        })
    },
    // 判断 dei 内容是否为空
    isDEIContentEmpty (htmlString) {
      // 解析富文本 html 字符串
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlString, 'text/html')
      // 判断是否所有的子节点文本内容为空
      return Array.from(doc.body.childNodes).map(node => node.innerText).every(text => text.trim().length == 0)
    },
    // 更新 unit 中的 dei 信息
    saveDEIToUnit () {
      if (this.isDEIContentEmpty(this.DEIBestPractice.forAdmin) || this.isDEIContentEmpty(this.DEIBestPractice.forTeacher)) {
        this.$message.error(this.$t('loc.unitPlannerDEI8'))
        return false
      }
      let params = {
        unitId: this.unitId,
        ...this.unitOverview,
        ...this.baseInfo,
        progress: this.getProgress(this.baseInfo.progress),
        deiBestPractice: JSON.stringify(this.DEIBestPractice),
        teachingTipsForLearnerProfile: this.teachingTipsForLearnerProfile
      }
      if (this.unitInfo.learnerProfileId) {
        params = { ...params, learnerProfileId: this.unitInfo.learnerProfileId, rubrics: this.unitInfo.rubrics }
      }
      if (this.unitInfo.teachingTipsForLearnerProfile) {
        params = { ...params, teachingTipsForLearnerProfile: this.unitInfo.teachingTipsForLearnerProfile }
      }
      // 判断 Unit 的 coverMedias 是否为空，如果不为空时将 coverMedia 的 type 设置为 UPLOAD
      if (params.coverMedias && params.coverMedias.length > 0) {
        params.coverMedias.forEach(media => {
          media.type = 'UPLOAD'
        })
      }
      this.$axios.post($api.urls().updateCurriculumUnit, params).then((res) => {
          // 更新 deiCopy（用于判断内容变化）
          this.DEIBestPracticeCopy = JSON.stringify(this.DEIBestPractice)
          this.$message.success('Save successfully!')
          // 初始化 dei 改变标记
          this.deiContentChanged = false
          // 更新 vuex 中的 dei 信息
          this.$store.commit('curriculum/SET_DEI_BEST_PRACTICE', JSON.stringify(this.DEIBestPractice))
      }).catch(error => {
        this.$message.error(error.message)
      })
      return true
    },

    // 更新单元是否使用课程模板信息
    async updateUnitUseLessonTemplateInfo (useLessonTemplate) {
      let params = {
        unitId: this.unitId,
        ...this.unitOverview,
        ...this.baseInfo,
        progress: this.getProgress(this.baseInfo.progress),
        deiBestPractice: JSON.stringify(this.DEIBestPractice),
        teachingTipsForLearnerProfile: this.teachingTipsForLearnerProfile,
        showLessonTemplateTip: true,
        useLessonTemplate: useLessonTemplate
      }
      if (this.unitInfo.learnerProfileId) {
        params = { ...params, learnerProfileId: this.unitInfo.learnerProfileId, rubrics: this.unitInfo.rubrics }
      }
      if (this.unitInfo.teachingTipsForLearnerProfile) {
        params = { ...params, teachingTipsForLearnerProfile: this.unitInfo.teachingTipsForLearnerProfile }
      }
      await this.$axios.post($api.urls().updateCurriculumUnit, params)
      .then(() => {
        this.baseInfo.useLessonTemplate = useLessonTemplate
        this.baseInfo.showLessonTemplateTip = true
      })
    },
    // 获取当前周计划的 ID
    getCurrentPlanId () {
      // 定义 planId
      let planId = null
      // 定义重试次数
      let retryCount = 0
      // 如果当前的周计划存在，则返回当前的周计划 ID
      if (this.currentWeeklyPlan) {
        planId = this.currentWeeklyPlan.id
      } else {
        const getPlanId = (timeoutId) => {
          clearTimeout(timeoutId)
          // 如果当前的周计划不存在，则返回当前的周计划 ID
          if (this.weeklyPlans && this.weeklyPlans.length > 0) {
            return this.weeklyPlans[this.currentWeekPlanIndex].id
          }
          // 再次尝试获取 planId
          if (retryCount < 10) {
            retryCount++
            return getPlanId(setTimeout(() => {}, 100))
          } else {
            return null
          }
        }
        planId = getPlanId(setTimeout(() => {}, 100))
      }
      return planId
    },
    // tab点击事件
    handleClick (tab, event) {
      this.activeUser = tab.name
    },
    showDEIDetails (generate) {
      this.DEIDetailsShow = true
      this.$analytics.sendEvent('cg_unit_create_three_dei')
      // 点击生成按钮的话开始生成 DEI
      if (generate) {
        this.generateDeiBestPracticeByUnitStream()
      }
    },
    closeDEIDetails () {
      // 弹窗
      if (this.deiContentChanged) {
        this.$confirm(this.$t('loc.unitPlannerDEI4'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.save'),
          cancelButtonText: this.$t('loc.noSave')
        }).then(() => {
          // 更新 unit 中的 dei 信息
          if (!this.saveDEIToUnit()) {
             // 还原 dei 为更新前的状态
            this.DEIBestPractice = JSON.parse(this.DEIBestPracticeCopy)
          }
          // 将改变标志设置为 false
          this.deiContentChanged = false
          // 设置 dialog 为关闭状态
          this.DEIDetailsShow = false
        }).catch(() => {
          // 还原 dei 为更新前的状态
          this.DEIBestPractice = JSON.parse(this.DEIBestPracticeCopy)
          // 将改变标志设置为 false
          this.deiContentChanged = false
          // 设置 dialog 为关闭状态
          this.DEIDetailsShow = false
        })
      } else if (this.deiLoading) {
        this.$confirm(this.$t('loc.unitPlannerDEI5'), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel')
        }).then(() => {
          this.DEIDetailsShow = false
          this.deiGenerateCount++
          this.deiLoading = false
          this.DEIBestPractice = JSON.parse(this.DEIBestPracticeCopy)
        }).catch(() => {
        })
      } else {
        this.DEIDetailsShow = false
      }
      // tab 定位复原
      this.activeUser = 'TEACHER'
    },
    // 显示单元预览
    openPlannerPreviewDialog () {
      // 关闭引导
      this.hideGuideOperate()
      this.plannerPreviewDialogShow = true
      // 设置全局 unitOverviewPreviewDrag 为 true
      this.$store.commit('curriculum/SET_UNIT_OVERVIEW_PREVIEW_DRAG', true)
    },
    // 关闭单元预览-- 第三步时需要重新调用 item
    closePlannerPreviewDialog () {
      // 如果未处理完的 item 时，打开 loading
      if (this.unitPlanDragItemCount && this.unitPlanDragItemCount !== 0) {
        this.openDialog()
        this.needSysCloseDialog = true
        return
      }
      // 关闭 loading
      if (this.loadingInstance) {
        this.loadingInstance.close()
      }
      // 关闭 dialog
      this.plannerPreviewDialogShow = false
      // 设置全局 unitOverviewPreviewDrag 为 false
      this.$store.commit('curriculum/SET_UNIT_OVERVIEW_PREVIEW_DRAG', false)
      // 如果有拖拽进行刷新
      this.handlerUnitOverviewPreviewDrag()
    },

    // 处理 UnitOverviewPreviewDrag
    handlerUnitOverviewPreviewDrag () {
      // 判断是否有周记录 item 被拖拽，有的话进行刷新
      if (!this.unitOverviewPreviewDragPlanIds || this.unitOverviewPreviewDragPlanIds.size === 0) {
        return
      }
      // 需要 loading
      this.$set(this.currentWeeklyPlan, 'generateLessonOverviewLoading', true)
      // 构建 map
      let weeklyPlansMap = new Map()
      // 遍历 this.weeklyPlans 数组
      for (let i = 0; i < this.weeklyPlans.length; i++) {
        // 将每个 weeklyPlan 按照 id 存入 Map 中，id 作为键，整个 weeklyPlan 对象作为值
        weeklyPlansMap.set(this.weeklyPlans[i].id.toLowerCase(), this.weeklyPlans[i])
      }
      let promises = []
      this.unitOverviewPreviewDragPlanIds.forEach(id => {
        promises.push(this.getPlanItemsbyPlanId(id))
      })
      Promise.all(promises).then(res => {
        res.forEach(weeklyPlan => {
          let existingPlan = weeklyPlansMap.get(weeklyPlan.id.toLowerCase())
          if (!existingPlan) {
            return
          }
          this.$set(existingPlan, 'items', weeklyPlan.items)
          this.$set(existingPlan, 'centerItems', weeklyPlan.centerItems)
        })

        this.$set(this.currentWeeklyPlan, 'generateLessonOverviewLoading', false)
      })

      // 清空数据 unitOverviewPreviewDragPlanIds
      this.$store.commit('curriculum/CLEAR_UNIT_OVERVIEW_PREVIEW_DRAG_PLAN_IDS')
    },
    // 计算左侧菜单高度
    calLeftMenuHeight () {
      this.$nextTick(() => {
        this.leftHeight = document.documentElement.clientHeight - 114 - 60
      })
    },
    // 反馈按钮点击事件
    clickFeedback (isUP) {
      if (isUP) {
        this.$analytics.sendEvent('web_unit_create3_feedback_pos')
      } else {
        this.$analytics.sendEvent('web_unit_create3_feedback_neg')
      }
    },
    // 设置 feedback 的样式
    setFeedbackStyle () {
        return {
            zIndex: '2000'
        }
    },
    // 初始化课程概览数据
    initLessonOverviewData () {
      // 没有周计划数据，则不初始化
      if (!this.unitId) {
        return
      }
      // 如果 vuex 中存储的周计划列表为空，说明是从首页直接定位到这一步骤，则需要从接口中获取当前单元的周计划列表
      if (!this.weeklyPlans || this.weeklyPlans.length === 0) {
        this.weeklyPlansLoading = true
        let params = {
          params: {
            unitId: this.unitId
          }
        }
        // 调用接口获取周计划概览与主题数据
        this.$axios.get($api.urls().getPlanThemeAndOverview, params).then(res => {
          let weeklyPlans = res.weeklyPlans
          weeklyPlans.forEach(plan => {
            plan.rubrics = plan.newRubrics
          })
          this.weeklyPlansLoading = false
          // 如果已经生成，则直接更新 Vuex 中的周计划数据, 否则生成周计划概览
          if (res.weeklyPlans && res.weeklyPlans.length > 0) {
            this.$store.commit('curriculum/SET_WEEKLY_PLANS', weeklyPlans)
            // 保存缓存是否是旧数据
            this.$store.commit('curriculum/SET_IS_OLD_UNIT',this.checkOldUnitData(weeklyPlans))
            // 获取所有的周计划项
            this.getAllPlanItems()
          }
        })
      } else {
        // 获取所有的周计划项
        this.getAllPlanItems()
      }
    },

    // 是否是旧数据 （Weekly Theme 添加测评点之前）
    checkOldUnitData (weeklyPlans) {
      if (!weeklyPlans || weeklyPlans.length === 0) return false

      const firstPlan = weeklyPlans[0]
      const hasOverview = firstPlan.overview && firstPlan.overview.trim() !== ''
      const noMeasureIds = firstPlan.measureIds && !firstPlan.measureIds.length

      // 如果有 overview 但是没测评点数据判定为旧数据
      return hasOverview && noMeasureIds
    },

    // 获取所有的周计划项目
    getAllPlanItems () {
      let promises = []
      this.weeklyPlans.forEach(weeklyPlan => {
        promises.push(this.getPlanItemsbyPlanId(weeklyPlan.id))
      })
      Promise.all(promises)
      .then(async res => {
        res.forEach(weeklyPlan => {
          for (let i = 0; i < this.weeklyPlans.length; i++) {
            if (this.weeklyPlans[i].id.toLowerCase() === weeklyPlan.id.toLowerCase()) {
              this.$set(this.weeklyPlans[i], 'items', weeklyPlan.items)
              this.$set(this.weeklyPlans[i], 'centerItems', weeklyPlan.centerItems)
            }
          }
        })
        // 如果批量任务不存在，则初始化周计划项任务状态
        if (!this.batchId) {
          this.initWeeklyPlansProgressing()
        }
        let currentWeek = this.$route.params.week
        if (currentWeek) {
          this.currentWeekPlanIndex = parseInt(currentWeek) - 1
        }
        this.currentWeeklyPlan = this.weeklyPlans[this.currentWeekPlanIndex]
        this.isShowSmallAndLargeGroup()
        // 如果未获取到周计划项，则生成周计划概览
        // 并发生成周计划概览
          let generateLessonOverviewTasks = []
          this.weeklyPlans.forEach(weeklyPlan => {
            if (weeklyPlan.items.length === 0) {
              // 生成周计划概览
              generateLessonOverviewTasks.push(this.generateLessonOverviewStream(weeklyPlan))
            }
          })
          if (generateLessonOverviewTasks.length > 0) {
            Promise.all(generateLessonOverviewTasks)
            .then(() => {
              // 弹出预览课程拖拽引导框
              this.guideVisible = this.showUnitPlanLessonPlanIdeaOverview
              this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
            })
          }
      })
    },
    initWeeklyPlansProgressing () {
      this.weeklyPlans.forEach(weeklyPlan => {
        weeklyPlan.items.forEach(item => {
          this.$set(item, 'processing', false)
        })
        weeklyPlan.centerItems.forEach(item => {
          this.$set(item, 'processing', false)
        })
      })
    },
    async publishMagic () {
      // 当前用户是否可以发布
        if (!this.allowPublicMagic) {
          this.$message.warning(this.$t('loc.limitOneMagicPublish'))
          return
        }
        this.publishUnit(true)
    },
    autoMagic(){
      let unitId = this.$route.params.unitId
        this.$confirm(`<p>Are you sure you're ready to submit?</p>
            <p>Once you click 'Confirm', your submission is final, and no changes can be made.</p>
            <p>You only get one chance, so make it count!</p>`,'Confirmation',{
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            dangerouslyUseHTMLString: true
          })
        .then(async() => {
            await this.$axios.post($api.urls().shardUnitToMagic, {
              unitId,
              sharedToMagic: true
            })
            let param = {
              'unitId': unitId,
              'planId': [],
              'fileType': 'docx',
              'shardedToMagic': true,
              'project': platform
            }
            this.$axios.post($api.urls().createBatchGenerateTask, param)
            this.editUnit = {
              id: unitId
            }
            this.magicSuccessVisible = true
            // 请求成功后刷一次接口
            this.$store.dispatch('magicCurriculum/checkCurrentUserToMagic')

        })
        .catch((error) => {
          if (error.response.data.error_code === 'param_error') {
            this.$message.error(this.$t('loc.repeatEntryTip'))
          }else{
            this.$message.error(error.response.data.error_message)
          }
        })
    },
    successClose(){
      this.$router.push({
        path: '/curriculum-genie/unit-planner'
      })
    },
    //
    /**
     * 发布单元
     * @param isShareMagic 是否发布到 Magic
     */
    publishUnit (isShareMagic) {
      this.$analytics.sendEvent('web_unit_create3_publish')
      let params = {
        unitId: this.unitId,
        ...this.unitOverview,
        ...this.baseInfo,
        progress: 'COMPLETED'
      }
      // 判断 Unit 的 coverMedias 是否为空，如果不为空时将 coverMedia 的 type 设置为 UPLOAD
      if (params.coverMedias && params.coverMedias.length > 0) {
        params.coverMedias.forEach(media => {
          media.type = 'UPLOAD'
        })
      }
      if (this.unitInfo.learnerProfileId) {
        params = { ...params, learnerProfileId: this.unitInfo.learnerProfileId, rubrics: this.unitInfo.rubrics }
      }
      if (this.unitInfo.teachingTipsForLearnerProfile) {
        params = { ...params, teachingTipsForLearnerProfile: this.unitInfo.teachingTipsForLearnerProfile }
      }
      this.$axios.post($api.urls().updateCurriculumUnit, params).then((res) => {
        if (isShareMagic === true) {
            this.autoMagic()
        } else {
            // this.$message.success('Published successfully!')
            // 发送点击完成单元埋点事件
            this.$analytics.sendEvent('cg_unit_create_lesson_complete')
            let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-detail-cg' : 'unitDetail'  
            if (this.unitInfo.adaptedType) {
              routeName = 'unit-detail-cg-adapt'
            }
            this.$router.push({
                name: routeName,
                query: {
                    unitId: this.unitId
                }
            })
        }
      }).catch(error => {
        console.log(error)
        this.$message.error(error.message)
      })
    },

         // 获取进度值所代表的枚举值
    getProgress (progress) {
      switch (progress) {
        case 20:
          return 'INITIALIZED'
        case 40:
          return 'WEEK_OVERVIEW_GENERATED'
        case 60:
          return 'WEEK_OVERVIEW_CONFIRMED'
        case 80:
          return 'LESSON_OVERVIEW_GENERATED'
        default:
          return 'COMPLETED'
      }
    },

    // 通过周计划 ID 获取周计划项
    async getPlanItemsbyPlanId(planId) {
      return new Promise((resolve, reject) => {
        let params = {
          params: {
            planId: planId
          }
        }
        this.$axios.get($api.urls().getPlanActivities, params)
          .then(res => {
            resolve(res)
            this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
              if (result.showCreateLessonDetailGuide) {
                this.showGenerateDetailGuide = true
                // 关闭引导
                let result = { 'features': ['CREATE_LESSON_DETAIL_GUIDE'] }
                this.$axios.post($api.urls().hideGuide, result).then()
              } else {
                this.showGenerateDetailGuide = false
              }
            })
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 按照周生成课程概览
    // async generateLessonOverviewWeekly () {
    //   // 如果已经生成过，则不再生成
    //   if (this.weeklyPlans && this.weeklyPlans[0].items && this.weeklyPlans[0].items.length > 0) {
    //     return
    //   }
    //   // 为每周生成单元课程(串行生成)
    //   for (let i = 0; i < this.weeklyPlans.length; i++) {
    //     let weeklyPlan = this.weeklyPlans[i]
    //     await this.generateLessonOverviewStream(weeklyPlan)
    //   }
    // },
    deiBestPracticeByUnitStreamGenerate (role, callback) {
      this.DEIDetailsShow = true
      this.activeUser = role || 'TEACHER'
      this.generateDeiBestPracticeByUnitStream(role, callback)
    },

    // 生成所有课程概览
    generateAllLessonOverviews (callback, scene) {
      if (scene === 'DEI_FOR_TEACHER') {
        this.deiBestPracticeByUnitStreamGenerate('TEACHER', callback)
      } else if (scene === 'DEI_FOR_ADMINISTRATORS') {
        this.deiBestPracticeByUnitStreamGenerate('ADMINISTRATORS', callback)
      } else if (scene === 'DEI_FOR_ALL') {
        this.deiBestPracticeByUnitStreamGenerate('', callback)
      } else {
        // 如果是其他的情况，那么传递的是 scene 就调用通用的 scene 的方法
        this.generateLessonOverviewStream(this.currentWeeklyPlan, callback, true)
      }
    },

    regenerateAllLessonOverviews (weeklyPlan) {
      this.generateLessonOverviewStream(weeklyPlan, null, true)
    },

    regenerateAllCenterOverviews (weeklyPlan) {
      let planCenters = []
      this.centerOverviews.forEach(overview => {
        planCenters.push({ id: overview.centerId, name: overview.centerGroupName, itemId: overview.id })
      })
      this.generateCenterOverViewStream(weeklyPlan, planCenters, true)
    },

    // 创建 centers 分组
    generateCentersOverView (weekPlan, centerNames) {
      let param = {
        planId: this.currentWeeklyPlan.id,
        centerNames: centerNames
      }
      // 批量创建 centers 分组
      Lessons2.addPlanCenters(param).then(res => {
        // 生成 centers 组活动概览
        this.generateCenterOverViewStream(weekPlan, res.planCenters)
      }).catch(error => {
        console.log(error)
        this.$message.error(error.response.data.error_message)
      })
    },

    // 生成 centers 组活动概览
    async generateCenterOverViewStream (weeklyPlan, planCenters, regenerate, callback) {
      // 如果当前周为第二周及以后的周，先获取第一周的 centers 组活动概览
      let overviews = JSON.parse(JSON.stringify(this.centerOverviews))
      this.$set(weeklyPlan, 'generateCenterOverviewLoading', true)
      weeklyPlan.centersOverviewData = ''
      // 是否需要继续生成新的 centers 组活动概览标识
      let noGgenerateNewItems = false
      if (weeklyPlan.week > 1) {
        let getOverViewParams = {
          currentPlanId: weeklyPlan.id,
          week: weeklyPlan.week,
          lastWeeklyPlanId: this.lastWeeklyPlanId,
          isStation: this.kToGrade2
        }
        await Lessons2.getPlanCenterActivityOverviews(getOverViewParams)
        .then(res => {
          res.unchangedItems.forEach(x => {
            weeklyPlan.centersOverviewData += (this.kToGrade2 ? 'Station Name: ' : 'Center Name: ') + x.centerGroupName + '\n' +
                'Lesson Title: ' + x.title + '\n' +
                'Measures: ' + x.measuresString + '\n' +
                'Description: ' + x.description + '\n'
          })
          let emptyOverviews = [...res.unchangedItems, ...res.changedItems]
          planCenters.forEach(x => {
            emptyOverviews.forEach(overview => {
              if (x.name === overview.centerGroupName) {
                // 重新生成时，将原有的 id 赋值回去
                let currentCenterItem = overviews.find(y => y.centerId === x.id)
                overview.id = currentCenterItem && currentCenterItem.id
                overview.centerId = x.id
                overview.activityType = 'Centers'
                overview.confirmed = false
                this.$set(overview, 'planId', weeklyPlan.id)
              }
            })
          })
          this.$set(weeklyPlan, 'centerItems', emptyOverviews)
          overviews = emptyOverviews
          // 根据返回的变化的 center 组活动数量判断是否继续生成新的 centers 组活动概览
          if (res.changedItems.length == 0) {
            noGgenerateNewItems = true
          }
        })
      } else {
        let emptyOverviews = []
        for (let i = 0; i < planCenters.length; i++) {
          let overview = {}
          if (overviews && overviews.length > i) {
            overview = { id: overviews[i].id }
          }
          this.$set(overview, 'planId', weeklyPlan.id)
          emptyOverviews.push(overview)
        }
        this.$set(weeklyPlan, 'centerItems', emptyOverviews)
      }
      // 如果不需要继续生成新的 centers 组活动概览，则直接保存 centers 组活动概览
      if (noGgenerateNewItems) {
        if (regenerate) {
          this.updateCenterItems(weeklyPlan)
        } else {
          // 保存课程概览
          this.createCenterItems(weeklyPlan)
        }
        // 生成结束
        this.$set(weeklyPlan, 'generateCenterOverviewLoading', false)
        return
      }
      // 参数
      let params = {
        unitId: this.unitId,
        planId: weeklyPlan.id
      }
      // 消息回调
      let messageCallback = (message) => {
        if (this.leavedPage) {
          return
        }
        // 更新数据
        weeklyPlan.centersOverviewData += message.data
        // 去除多余的字符
        weeklyPlan.centersOverviewData = weeklyPlan.centersOverviewData.replace('```', '')
        // 解析数据
        let parsedData = parseStreamData(weeklyPlan.centersOverviewData, [
          { key: 'centerGroupName', name: ['Center Name', 'Station Name'] },
          { key: 'title', name: 'Lesson Title' },
          { key: 'measuresString', name: 'Measures' },
          { key: 'description', name: 'Description' }
        ], planCenters.length)
        let newOverviews = []
        for (let i = 0; i < planCenters.length; i++) {
          let overview = parsedData.length > i ? parsedData[i] : {}
          // 原有数据
          if (overviews && overviews.length > i) {
            overview.id = overviews[i].id
          }
          var center = planCenters.find(x => x.name === overview.centerGroupName)
          if (center) {
            overview.centerId = center.id
            overview.activityType = 'Centers'
          }
          // 解析测评点
          if (overview.measuresString) {
            let measures = this.handleGptMeasures(overview.measuresString)
            // 设置值
            this.$set(overview, 'measures', measures)
          }
          // lesson title 去除前后""
          if (overview.title) {
            let title = this.formatLessonTitle(overview.title)
            this.$set(overview, 'title', title)
          }
          this.$set(overview, 'planId', weeklyPlan.id)
          this.$set(overview, 'confirmed', true)
          newOverviews.push(overview)
        }
        // 更新数据
        this.$set(weeklyPlan, 'centerItems', newOverviews)
      }
      // 清理 promptUsageRecordIds
      this.$set(this, 'promptUsageRecordIds', [])
      // 生成课程概览
      createEventSource($api.urls().generateCenterOverviewStream, params, messageCallback)
        .then((res) => {
          if (this.leavedPage) {
            return
          }
          // 如果生成的 centers 组活动概览有空数据，则提示用户重新生成
          if (this.checkOverviews(weeklyPlan.centerItems)) {
            this.$set(weeklyPlan, 'generateCenterOverviewLoading', false)
            this.$set(weeklyPlan, 'centerItems', [])
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
            return
          }
          // 记录 promptUsageRecordId
          if (res && res.promptUsageRecordId !== '') {
              this.promptUsageRecordIds.push(res.promptUsageRecordId)
          }
          this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
          if (regenerate) {
            this.updateCenterItems(weeklyPlan)
          } else {
            // 保存课程概览
            this.createCenterItems(weeklyPlan)
          }
          // 生成结束
          this.$set(weeklyPlan, 'generateCenterOverviewLoading', false)
          if (callback && typeof callback === 'function') {
            callback()
          }

          // 生成结束后判断是否需要更新周计划测评点
          this.$refs.lessonOverviewRef.needUpdateWeeklyPlanStandards()
        })
        .catch(error => {
          // 生成出错
          this.$set(weeklyPlan, 'generateCenterOverviewLoading', false)
          this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          if (callback && typeof callback === 'function') {
            callback()
          }
        })
    },

    // 处理 GPT 返回的测评点内容
    handleGptMeasures (measuresString) {
      let measures = measuresString.split(';')
      // 只有一个测评点时，尝试用逗号分隔
      if (measures && measures.length === 1) {
        measures = measuresString.split(',')
      }
      // 去除空格
      measures = measures
        .map(measure => measure.trim())
        .map(measure => {
          // 如果 convertMappingAbbrToDomainAbbrMap 存在并且长度大于 0，则使用 convertMappingAbbrToDomainAbbrMap 中的值
          if (this.convertMappingAbbrToDomainAbbrMap && this.convertMappingAbbrToDomainAbbrMap.size > 0) {
            return this.convertMappingAbbrToDomainAbbrMap.get(measure) || measure
          }
          return measure
        })
        // 过滤掉为空的 convertedMeasure
        .filter(measure => {
          if (measure && this.frameworkMeasureAbbSet && this.frameworkMeasureAbbSet.size > 0) {
            let measureAbbreviation = measure.trim().toUpperCase()
            return this.frameworkMeasureAbbSet.has(measureAbbreviation)
          }
          return false
        })
      // 去重
      measures = [...new Set(measures)]
      return measures
    },

    // 校验动概览是否有空数据
    checkOverviews (items) {
      return items.some(x => !x.title && !x.measuresString && !x.description)
    },

    // 同步生成课程概览
    generateLessonOverview (weeklyPlan) {
      this.$set(weeklyPlan, 'generateLoading', true)
      let param = {
        baseInfo: this.baseInfo,
        weeklyPlan: weeklyPlan,
        gptSetting: this.gptSetting
      }
      this.$axios
          .post(aiApiUrl + '?action=generate_lesson_overview', param).then((res) => {
        this.$set(weeklyPlan, 'items', res.result.lessons)
        this.$set(weeklyPlan, 'generateLoading', false)
        this.createPlanItems(weeklyPlan)
        // this.$store.commit('curriculum/SET_WEEKLY_PLANS', this.weeklyPlans)
      })
          .catch((error) => {
            this.$set(weeklyPlan, 'generateLoading', false)
            console.log(error)
            this.$message.error(error.response.data.error_message)
          })
    },

    // 异步生成课程概览
    async generateLessonOverviewStream (weeklyPlan, callback, showTip) {
      this.$set(weeklyPlan, 'generateLessonOverviewLoading', true)

      // 周计划测评点数据
      const measureIds = weeklyPlan.measureIds
      // 默认旧数据是一周为五天
      let activityCount = 5
      let activityKey = 'day'
      let activityName = 'Day'
      // 新版提示词
      if (measureIds && measureIds.length > 0) {
        activityKey = 'activitySortNum'
        activityName = 'Activity Sort Num'
        activityCount = weeklyPlan.activityCount
      }

      // 重置数据
      weeklyPlan.lessonOverviewData = ''
      // 课程概览
      let overviews = weeklyPlan.items
      // 根据数量生成周计划空对象
      let emptyOverviews = []
      for (let i = 0; i < activityCount; i++) {
        let overview = {}
        if (overviews && overviews.length > i) {
          overview = { id: overviews[i].id }
        }
        emptyOverviews.push(overview)
      }
      this.$set(weeklyPlan, 'items', emptyOverviews)
      // 参数
      let params = {
        unitId: this.unitId,
        planId: weeklyPlan.id
      }
      // 消息回调
      let messageCallback = (message) => {
        if (this.leavedPage) {
          return
        }
        // 更新数据
        weeklyPlan.lessonOverviewData += message.data
        // 需要根据年龄选择不同的解析数据
        let parsedData = []
        var grade = this.baseInfo.grade.trim()
        // 不需要显示 Small Group 和 Large Group 的数据
        let showSmallAndLargeGroupGrades = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)']
        // 判断是否显示 Small Group 和 Large Group
        let activityFlag = showSmallAndLargeGroupGrades.includes(grade)
        if (this.isGenieCurriculumToUnitPlanThird) {
          if (activityFlag) {
            parsedData = parseStreamData(weeklyPlan.lessonOverviewData, [
              { key: 'activitySortNum', name: 'Activity Sort Num' },
              { key: 'title', name: ['Lesson Title', 'Title'] },
              { key: 'measuresString', name: 'Measures' },
              { key: 'description', name: ['Activity Description', 'Description'] },
              { key: 'activityType', name: 'Activity Type' }
            ], activityCount)
          } else {
            parsedData = parseStreamData(weeklyPlan.lessonOverviewData, [
              { key: 'activitySortNum', name: 'Activity Sort Num' },
              { key: 'title', name: ['Lesson Title', 'Title'] },
              { key: 'measuresString', name: 'Measures' },
              { key: 'description', name: ['Activity Description', 'Description'] },
              ...(this.hasRubrics && weeklyPlan.rubrics && weeklyPlan.rubrics.length > 0 ? [{ key: 'rubrics', name: 'Assigned Graduate Profile Attributes' }] : [])
            ], activityCount)
          }
        } else if (activityFlag) {
          // 解析数据
          parsedData = parseStreamData(weeklyPlan.lessonOverviewData, [
            { key: activityKey, name: activityName },
            { key: 'title', name: ['Lesson Title', 'Title'] },
            { key: 'measuresString', name: 'Measures' },
            { key: 'description', name: ['Activity Description', 'Description'] },
            { key: 'activityType', name: 'Activity Type' }
          ], activityCount)
        } else {
          parsedData = parseStreamData(weeklyPlan.lessonOverviewData, [
            { key: activityKey, name: activityName },
            { key: 'title', name: ['Lesson Title', 'Title'] },
            { key: 'measuresString', name: 'Measures' },
            { key: 'description', name: ['Activity Description', 'Description'] },
            ...(this.hasRubrics && weeklyPlan.rubrics && weeklyPlan.rubrics.length > 0 ? [{ key: 'rubrics', name: 'Assigned Graduate Profile Attributes' }] : [])
          ], activityCount)
        }
        // 转换为新的周计划数组
        let newOverviews = []
        for (let i = 0; i < activityCount; i++) {
          let overview = parsedData.length > i ? parsedData[i] : {}
          let overviews = weeklyPlan.items
          // 原有数据
          if (overviews && overviews.length > i) {
            overview.id = overviews[i].id
          }
          // 解析测评点
          if (overview.measuresString) {
            let measuresString = overview.measuresString.trim()
            this.$set(overview, 'measuresString', measuresString)
            let measures = this.handleGptMeasures(overview.measuresString)
            // 设置值
            this.$set(overview, 'measures', measures)
          }
          // lesson title 去除前后""
          if (overview.title) {
            let title = this.formatLessonTitle(overview.title)
            this.$set(overview, 'title', title)
          }
          // 活动类型去除空格
          if (overview.activityType) {
            // 校验活动类型，必须是 Small Group 或 Large Group
            let activityType = overview.activityType.trim()
            var validate = activityType == 'Small Group' || activityType == 'Large Group'
            if (!validate) {
              activityType = 'Small Group'
            }
            this.$set(overview, 'activityType', activityType)
          }
          // 解析 day
          if (overview.day) {
            // 正则替换掉后面的 Part 内容，例如 (Part 1) / (Part 2)
            let day = overview.day.replace(/\(Part \d\)/, '').trim()
            this.$set(overview, 'day', day)
          }
          // 描述
          if (overview.description) {
            let description = overview.description.trim()
            description = description.replace(/---/g, '')
            this.$set(overview, 'description', description)
          }
          // 活动序号
          if (overview.activitySortNum) {
            let activitySortNum = overview.activitySortNum.trim()
            this.$set(overview, 'activitySortNum', activitySortNum)
          }
          // 处理 rubrics
          if (overview.rubrics) {
            let rubrics = tools.parseAndValidateRubrics(overview.rubrics, weeklyPlan.rubrics)
            this.$set(overview, 'rubrics', rubrics)
          }
          this.$set(overview, 'planId', weeklyPlan.id)
          this.$set(overview, 'confirmed', true)
          newOverviews.push(overview)
        }
        // 更新数据,一次更新所有的 item
        this.$set(weeklyPlan, 'items', newOverviews)
      }
        // 清理 promptUsageRecordIds
        this.$set(this, 'promptUsageRecordIds', [])
      // 生成课程概览
      return new Promise((resolve, reject) => {
       createEventSource($api.urls().generateLessonOverviewStream, params, messageCallback)
          .then(async (res) => {
            if (this.leavedPage) {
              return
            }
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              await this.upPlanLessonOverviewItems(weeklyPlan, res.promptUsageRecordId)
            }

            // 如果生成的 centers 组活动概览有空数据，则提示用户重新生成
            if (this.checkOverviews(weeklyPlan.items)) {
              this.$set(weeklyPlan, 'generateLessonOverviewLoading', false)
              this.$set(weeklyPlan, 'items', [])
              this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
              return
            }
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
                this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            if (showTip) {
              this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
            }
            // 保存课程概览
            await this.createPlanItems(weeklyPlan, true)
            // 生成结束后判断是否需要更新周计划测评点
            this.$refs.lessonOverviewRef.needUpdateWeeklyPlanStandards()
            // 生成结束
            this.$set(weeklyPlan, 'generateLessonOverviewLoading', false)
            if (callback && typeof callback === 'function') {
              callback()
            }
            resolve(weeklyPlan)
          })
          .catch(error => {
            // 生成出错
            this.$set(weeklyPlan, 'generateLessonOverviewLoading', false)
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
            if (callback && typeof callback === 'function') {
              callback()
            }
            reject(error)
          })
      })
    },

    // 获取是否显示拖拽引导状态
    getNeedShowUnitOverviewGuide () {
      // 首先从缓存中获取状态值，如果本地缓存中不存在时再去调用接口
      const guideState = localStorage.getItem(this.currentUser.user_id + 'LESSON_PLAN_IDEA_OVERVIEW')
      // 如果缓存中状态值不存在，则调用接口获取状态值
      if (!guideState) {
        this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
          // 对状态值进行赋值
          this.showUnitPlanLessonPlanIdeaOverview = result.showUnitPlanLessonPlanIdeaOverview
          // 将接口中的值存到本地
          if (typeof this.showUnitPlanLessonPlanIdeaOverview === 'boolean') {
            localStorage.setItem(this.currentUser.user_id + 'LESSON_PLAN_IDEA_OVERVIEW', this.showUnitPlanLessonPlanIdeaOverview)
          }
        })
      } else {
        // 如果有值则直接赋值
        this.showUnitPlanLessonPlanIdeaOverview = JSON.parse(guideState)
      }
    },

    // 关闭引导
    hideGuideOperate () {
      this.guideVisible = false
      // 如果选择永久关闭
      if (this.foreverCloseGuide) {
        this.handleUnitOverviewGuide()
      }
    },

    // 发起请求，隐藏批量 Adapt 课程引导
    handleUnitOverviewGuide () {
      let result = { 'features': ['LESSON_PLAN_IDEA_OVERVIEW'] }
      this.$axios.post($api.urls().hideGuide, result).then()
      localStorage.setItem(this.currentUser.user_id + 'LESSON_PLAN_IDEA_OVERVIEW', false)
      this.showUnitPlanLessonPlanIdeaOverview = false
    },

    // 批量生成结果成功
    batchGenerateResultsSuccess (result) {
      // 更新测试记录 ID
      this.testId = result.testRecordId
      // 获取测试结果
      this.$refs.promptStatsResultRef.getTestResults(true)
      // 切换到测试列表页面
      this.currentView = 'STATS_RESULT'
    },

    // 二次分配处理
    async upPlanLessonOverviewItems (weeklyPlan, promptUsageRecordId) {
      let params = {
        params: {
          planId: weeklyPlan.id,
          unitId: this.unitId, // 单元 ID,
          promptUsageRecordId: promptUsageRecordId
        }
      }

      try {
        // 使用 await 等待异步请求完成
        const res = await this.$axios.get($api.urls().createLessonOverviewSecondAllocation, params)
        if (!res || res.length === 0) {
          return
        }
        let items = weeklyPlan.items
        if (!items) {
          return
        }
        // map 构建
        const resultMap = res.reduce((acc, item) => {
          acc[item.activitySortNum] = item.exMeasureAbbrList
          return acc
        }, {})
        // 遍历 items 增加新测评点
        items.forEach(item => {
          const exAbb = resultMap[item.activitySortNum]
          if (!exAbb) {
            return
          }
          exAbb.forEach(abb => {
            if (!item.measures.includes(abb)) {
              item.measures.push(abb)
            }
          })

          this.$set(item, 'measures', item.measures)
          this.$set(item, 'measuresString', item.measures.join(';'))
        })
        // 更新周计划 Items
        this.$set(weeklyPlan, 'items', items)
      } catch (error) {
        console.error('Error fetching lesson overview:', error)
      }
    },

    // 保存课程概览
    async createPlanItems (weeklyPlan, clearItemLesson) {
      // 周计划活动项
      let items = weeklyPlan.items
      // 如果存在 ID 则更新
      if (items && items.length > 0 && items[0].id) {
        this.updatePlanItems(weeklyPlan, null, clearItemLesson)
        return
      }
      let params = {
        items: items,
        frameworkId: this.baseInfo.frameworkId,
        planId: weeklyPlan.id,
        unitId: this.unitId // 单元 ID
      }
      // 更新进度
      this.baseInfo.progress = 60
      params.progress = this.progressConstant.sixtyPercent

      this.$set(weeklyPlan, 'updateLoading', true)
      await this.$axios.post($api.urls().createPlanItems, params).then((res) => {
        let itemIds = res.planItemIds
        // 验证数量是否匹配
        if (itemIds.length !== items.length) {
          this.$message.error(this.$t('loc.unitPlannerStep3SavedUnsuccessfully'))
          this.$set(weeklyPlan, 'updateLoading', false)
          return
        }
        // 更新周计划 ID
        for (let i = 0; i < items.length; i++) {
          this.$set(items[i], 'id', itemIds[i])
        }
        this.$set(weeklyPlan, 'updateLoading', false)
        // 更新课程概览成功事件
        this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
      }).catch(error => {
        this.$set(weeklyPlan, 'updateLoading', false)
        console.log(error)
        this.$message.error(error.response.data.error_message)
      })
    },

    // 更新课程概览
    updatePlanItems (weeklyPlan, next, clearItemLesson) {
      this.$set(weeklyPlan, 'updateLoading', true)
      let params = {
        items: [...weeklyPlan.items],
        frameworkId: this.baseInfo.frameworkId,
        planId: weeklyPlan.id,
        clearItemLesson: clearItemLesson, // 清除课程信息
        unitId: this.unitId // 单元 ID
      }
      params.progress = this.reJudgeUnitGenerateProgress()
      this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
        if (next) {
          next()
        }
        this.$set(weeklyPlan, 'updateLoading', false)
        // 更新课程概览成功事件
        this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
      }).catch(error => {
        console.log(error)
        this.$set(weeklyPlan, 'updateLoading', false)
        this.$message.error(error.response.data.error_message)
      })
    },

    // 测试完成
    testCompleted () {
      // 获取测试结果
      this.$refs.promptEditorRef.testCompleted()
    },

    // 测试未完成
    testIncomplete () {
      // 获取测试结果
      this.$refs.promptEditorRef.testIncomplete()
    },

    // 更新所有课程概览
    updateAllPlanItems (weeklyPlan, showSaveTip) {
      // 点击保存按钮埋点
      if (showSaveTip) {
        this.$analytics.sendEvent('web_unit_create3_save')
      }
      this.$set(weeklyPlan, 'updateLoading', true)
      let params = {
        items: [...weeklyPlan.items, ...weeklyPlan.centerItems],
        frameworkId: this.baseInfo.frameworkId,
        planId: weeklyPlan.id,
        unitId: this.unitId // 单元 ID
      }
      // 流式生成结束之后重新判断 Unit 的生成进度
      params.progress = this.reJudgeUnitGenerateProgress()
      this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
        this.$set(weeklyPlan, 'updateLoading', false)
        if (showSaveTip) {
          this.$message.success(this.$t('loc.unitPlannerStep3SavedSuccessfully'))
        }
        // 更新课程概览成功事件
        this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
      }).catch(error => {
        console.log(error)
        this.$set(weeklyPlan, 'updateLoading', false)
        this.$message.error(error.response.data.error_message)
      })
    },

    // 重新判断当前 Unit 的生成进度
    reJudgeUnitGenerateProgress () {
      // 如果是已经完成的单元，则不在更新单元状态
      if (this.allWeekCompleted) {
        return ''
      }
      // 是否存在未确定的 Item
      let hasUnconfirmedItem = false
      // 是否存在未生成的 Item 的周次
      let hasUnGenerateItem = false
      // Unit 生成进度
      let progress = ''
      // 遍历所有周计划，如果有未确认的课程，则更新进度为 60%
      for (let i = 0; i < this.weeklyPlans.length; i++) {
        // 获取当前周计划
        let weeklyPlan = this.weeklyPlans[i]
        // 判断是否存在周次未生成大小组
        if (!weeklyPlan.items || weeklyPlan.items.length === 0) {
          hasUnGenerateItem = true
          break
        }
        // 如果周计划项存在，则判断是否有未确认的课程
        if (weeklyPlan.items && weeklyPlan.items.length > 0) {
          if (weeklyPlan.items.some(item => !item.confirmed)) {
            hasUnconfirmedItem = true
            break
          }
        }
        // 如果 centers 组周计划项存在，则判断是否有未确认的课程
        if (weeklyPlan.centerItems && weeklyPlan.centerItems.length > 0) {
          if (weeklyPlan.centerItems.some(item => !item.confirmed)) {
            hasUnconfirmedItem = true
            break
          }
        }
      }
      // 如果存在未确认的课程或未生成大小组的周次，则更新进度为 60%，否则不进行更新操作
      if (!this.baseInfo.progress || this.baseInfo.progress < 60 || hasUnconfirmedItem || hasUnGenerateItem) {
        progress = this.progressConstant.sixtyPercent
      } else if (!hasUnconfirmedItem) { // 如果不存在未确定的 Item，则更新进度为 80%
        progress = this.progressConstant.eightyPercent
      }
      return progress
    },

    // 创建 center 组课活动
    createCenterItems (weeklyPlan) {
      // 周计划活动项
      let items = weeklyPlan.centerItems
      let params = {
        items: items,
        frameworkId: this.baseInfo.frameworkId,
        planId: weeklyPlan.id,
        unitId: this.unitId // 单元 ID
      }
      // 创建 Items 重新判断 Unit 的生成进度
      params.progress = this.reJudgeUnitGenerateProgress()
      this.$set(weeklyPlan, 'updateLoading', true)
      this.$axios.post($api.urls().createPlanItems, params).then((res) => {
        let itemIds = res.planItemIds
        // 验证数量是否匹配
        if (itemIds.length !== items.length) {
          this.$message.error(this.$t('loc.unitPlannerStep3SavedUnsuccessfully'))
          this.$set(weeklyPlan, 'updateLoading', false)
          return
        }
        // 更新周计划 ID
        for (let i = 0; i < items.length; i++) {
          this.$set(items[i], 'id', itemIds[i])
        }
        this.$set(weeklyPlan, 'updateLoading', false)
        // 更新课程概览成功事件
        this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
      }).catch(error => {
        this.$set(weeklyPlan, 'updateLoading', false)
        console.log(error)
        this.$message.error(error.response.data.error_message)
      })
    },

    // 更新 center 组课程概览
    updateCenterItems (weeklyPlan) {
      this.$set(weeklyPlan, 'updateLoading', true)
      let params = {
        items: weeklyPlan.centerItems,
        frameworkId: this.baseInfo.frameworkId,
        planId: weeklyPlan.id,
        clearItemLesson: true, // 清除课程信息
        unitId: this.unitId // 单元 ID
      }
      // 流式生成结束之后重新判断 Unit 的生成进度
      params.progress = this.reJudgeUnitGenerateProgress()
      this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
        this.$set(weeklyPlan, 'updateLoading', false)
        // 更新课程概览成功事件
        this.$analytics.sendActivityEvent('UNIT_PLANNER', 'UPDATE_UNIT')
      }).catch(error => {
        console.log(error)
        this.$set(weeklyPlan, 'updateLoading', false)
        this.$message.error(error.response.data.error_message)
      })
    },

    // 重新生成单元下活动信息
    regenerateLessonOverview (lesson, day, lessonIndex) {
      this.$set(lesson, 'generateLoading', true)
      this.$set(lesson, 'generating', true)
      this.$set(lesson, 'lessonId', null)
      this.$set(lesson, 'lesson', null)
      this.$set(lesson, 'description', null)
      this.$set(lesson, 'title', null)
      this.$set(lesson, 'measuresString', null)
      this.$set(lesson, 'measures', [])
      this.$set(lesson, 'lessonTemplateType', null)
      this.$set(lesson, 'adaptedLesson', null)
      let param = {
        unitId: this.unitId,
        planId: this.currentWeeklyPlan.id,
        dayOfWeek: lessonIndex + 1
      }
      let data = ''
      let items = []

      // 周计划测评点数据
      const measureIds = this.currentWeeklyPlan.measureIds
      // 默认旧数据是一周为五天
      let activityKey = 'day'
      let activityName = 'Day'
      // 新版提示词
      if (measureIds && measureIds.length > 0) {
        activityKey = 'activitySortNum'
        activityName = 'Activity Sort Num'
      }

      // 消息回调
      let messageCallback = (message) => {
        if (this.leavedPage) {
          return
        }
        // 更新数据
        data += message.data
        // 解析数据
        let parsedData = []
        if (this.isGenieCurriculumToUnitPlanThird) {
          if (this.showSmallAndLargeGroupFlag) {
            parsedData = parseStreamData(data, [
              { key: 'activitySortNum', name: 'Activity Sort Num' },
              { key: 'title', name: ['Lesson Title', 'Title'] },
              { key: 'measuresString', name: 'Measures' },
              { key: 'description', name: ['Activity Description', 'Description'] },
              { key: 'activityType', name: 'Activity Type' }
            ], 5)
          } else {
            parsedData = parseStreamData(data, [
              { key: 'activitySortNum', name: 'Activity Sort Num' },
              { key: 'title', name: ['Lesson Title', 'Title'] },
              { key: 'measuresString', name: 'Measures' },
              { key: 'description', name: ['Activity Description', 'Description'] }
            ], 5)
          }
        } else if (this.showSmallAndLargeGroupFlag) {
          parsedData = parseStreamData(data, [
            { key: activityKey, name: activityName },
            { key: 'title', name: ['Lesson Title', 'Title'] },
            { key: 'measuresString', name: 'Measures' },
            { key: 'description', name: ['Activity Description', 'Description'] },
            { key: 'activityType', name: 'Activity Type' }
          ], 5)
        } else {
          parsedData = parseStreamData(data, [
            { key: activityKey, name: activityName },
            { key: 'title', name: ['Lesson Title', 'Title'] },
            { key: 'measuresString', name: 'Measures' },
            { key: 'description', name: ['Activity Description', 'Description'] }
          ], 5)
        }

        for (let i = 0; i < 5; i++) {
          let overview = parsedData.length > i ? parsedData[i] : {}
          if (overview.activityType) {
            // 校验活动类型，必须是 Small Group 或 Large Group
            let activityType = overview.activityType.trim()
            var validate = activityType == 'Small Group' || activityType == 'Large Group'
            if (!validate) {
              if (this.showSmallAndLargeGroupFlag) {
                activityType = 'Small Group'
              } else {
                activityType = 'Activities'
              }
            }
            this.$set(overview, 'activityType', activityType)
          }
          if (overview.activitySortNum) {
            let activitySortNum = overview.activitySortNum.trim()
            this.$set(overview, 'activitySortNum', activitySortNum)
          }
          if (overview.day || overview.activitySortNum) {
            if (day == overview.day || lessonIndex + 1 == overview.activitySortNum) {
              // 解析测评点
              if (overview.measuresString) {
                let measures = this.handleGptMeasures(overview.measuresString)
                overview.measures = measures
              }
              for (let key in overview) {
                // 特殊处理字符串类型是属性，去除前后的空格
                if (typeof overview[key] === 'string') {
                  this.$set(lesson, key, overview[key].trim())
                } else {
                  this.$set(lesson, key, overview[key])
                }
                // 正则匹配，去除 title 前后的 ""
                if (key === 'title') {
                  let title = this.formatLessonTitle(lesson.title)
                  this.$set(lesson, 'title', title)
                }
              }
              this.$set(lesson, 'deleted', false)
              this.$set(lesson, 'generateLoading', false)
              this.$set(lesson, 'confirmed', false)
              this.$set(lesson, 'coverKeywords', null)
            }
          }
        }
      }
        // 清理 promptUsageRecordIds
        this.$set(this, 'promptUsageRecordIds', [])
      // 重新生成课程概览
      createEventSource($api.urls().generateSingleLessonOverviewStream, param, messageCallback)
          .then((res) => {
            if (this.leavedPage) {
              return
            }
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
                this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            // 提示生成成功
            this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
            this.$set(lesson, 'generating', false)
            items.push(lesson)
            let params = {
              items: items,
              frameworkId: this.baseInfo.frameworkId,
              planId: this.currentWeeklyPlan.id,
              clearItemLesson: true, // 清除课程信息
              unitId: this.unitId // 单元 ID
            }
            // 流式生成结束之后重新判断 Unit 的生成进度
            params.progress = this.reJudgeUnitGenerateProgress()
            this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
            }).catch(error => {
              console.log(error)
            })
            this.$set(lesson, 'confirmed', true)
            // 生成结束后判断是否需要更新周计划测评点
            this.$refs.lessonOverviewRef.needUpdateWeeklyPlanStandards()
          })
          .catch(error => {
            // 生成出错
            this.$set(lesson, 'generateLoading', false)
            console.log(error)
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          })
    },

    // 重新生成 center 组活动信息
    regenerateCenterOverview (lesson) {
      let centerName = lesson.centerGroupName
      let centerId = lesson.centerId
      this.$set(lesson, 'generateLoading', true)
      this.$set(lesson, 'generating', true)
      this.$set(lesson, 'lessonId', null)
      this.$set(lesson, 'lesson', null)
      this.$set(lesson, 'description', null)
      this.$set(lesson, 'title', null)
      this.$set(lesson, 'measuresString', null)
      this.$set(lesson, 'measures', [])
      let param = {
        unitId: this.unitId,
        planId: this.currentWeeklyPlan.id,
        centerId: centerId
      }
      let data = ''
      let items = []
      let messageCallback = (message) => {
        if (this.leavedPage) {
          return
        }
        // 更新数据
        data += message.data
        // 解析数据
        let parsedData = parseStreamData(data, [
          { key: 'centerGroupName', name: ['Center Name', 'Station Name'] },
          { key: 'title', name: 'Lesson Title' },
          { key: 'measuresString', name: 'Measures' },
          { key: 'description', name: 'Description' }
        ], 1)
        for (let i = 0; i < 1; i++) {
          let overview = parsedData.length > i ? parsedData[i] : {}
          if (overview.centerGroupName == centerName) {
            // 解析测评点
            if (overview.measuresString) {
              let measures = this.handleGptMeasures(overview.measuresString)
              overview.measures = measures
            }
            for (let key in overview) {
              // 特殊处理字符串类型是属性，去除前后的空格
              if (typeof overview[key] === 'string') {
                this.$set(lesson, key, overview[key].trim())
              } else {
                this.$set(lesson, key, overview[key])
              }
              // 正则匹配，去除 title 前后的 ""
              if (key === 'title') {
                let title = this.formatLessonTitle(lesson.title)
                this.$set(lesson, 'title', title)
              }
            }
            this.$set(lesson, 'deleted', false)
            this.$set(lesson, 'generateLoading', false)
            this.$set(lesson, 'confirmed', false)
          }
        }
      }
      // 生成课程概览
        // 清理 promptUsageRecordIds
        this.$set(this, 'promptUsageRecordIds', [])
      // 重新生成课程概览
      createEventSource($api.urls().generateSingleCenterOverviewStream, param, messageCallback)
      .then((res) => {
        if (this.leavedPage) {
          return
        }
        this.$set(lesson, 'generating', false)
        // 记录 promptUsageRecordId
        if (res && res.promptUsageRecordId !== '') {
            this.promptUsageRecordIds.push(res.promptUsageRecordId)
        }
        this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
        this.$set(lesson, 'generateLoading', false)
        items.push(lesson)
        let params = {
          items: items,
          frameworkId: this.baseInfo.frameworkId,
          planId: this.currentWeeklyPlan.id,
          clearItemLesson: true, // 清除课程信息
          unitId: this.unitId // 单元 ID
        }
        // 流式生成结束之后重新判断 Unit 的生成进度
        params.progress = this.reJudgeUnitGenerateProgress()
        this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
        }).catch(error => {
          console.log(error)
        })
        this.$set(lesson, 'confirmed', true)
        // 生成结束后判断是否需要更新周计划测评点
        this.$refs.lessonOverviewRef.needUpdateWeeklyPlanStandards()
      })
      .catch(error => {
        // 生成出错
        this.$set(lesson, 'generateLoading', false)
        console.log(error)
        this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
      })
    },

    // 去除 title 前后的 ""
    formatLessonTitle (lessonTitle) {
      let reg = /^\"(.*)\"$/g
      let match = reg.exec(lessonTitle)
      let title = lessonTitle
      if (match) {
        title = match[1]
      }
      return title
    },

    // 返回
    back () {
      // 返回上一步按钮埋点
      this.$analytics.sendEvent('web_unit_create3_back')
      let routerName = ''
      if (this.unitInfo.adaptedType) {
        routerName = 'edit-weekly-plan-overview-cg-adapt'
      } else if (this.$store.state.curriculum.isCG || this.isCurriculumPlugin) {
        routerName = 'edit-weekly-plan-overview-cg'
      } else {
        routerName = 'weeklyPlanOverview'
      }
      // 返回上一步
      this.$router.push({
        name: routerName,
        params: {
          unitId: this.unitId
        }
      })
    },

    // 下一步
    next (toLessonDetail) {
      // 未完成的
      let uncompletedLessons = this.lessonOverviews.filter(lesson => lesson.deleted)
      if (uncompletedLessons.length > 0) {
        let uncompletedDays = uncompletedLessons.map(lesson => lesson.day).join(', ')
        let errorMsg = 'You haven\'t generated the lesson for ' + uncompletedDays + '.'
        this.$message.error(errorMsg)
        return
      }
      // 当前周次未确认的
      let unconfirmedLessons = [...this.lessonOverviews.filter(lesson => !lesson.confirmed), ...this.centerOverviews.filter(lesson => !lesson.confirmed)]
      if (unconfirmedLessons.length > 0) {
          this.$confirm(this.$t('loc.unitPlannerStep3Confirmation4', { week: this.currentWeekPlanIndex + 1 }), this.$t('loc.confirmation'), {
            confirmButtonText: this.$t('loc.unitPlannerStep3Confirm'),
            cancelButtonText: this.$t('loc.cancel')
        }).then(() => {
          // 当前周下的活动项全部确认
          this.currentWeeklyPlan.items.forEach(item => {
            this.$set(item, 'confirmed', true)
          })
          this.currentWeeklyPlan.centerItems.forEach(item => {
            this.$set(item, 'confirmed', true)
          })
          this.updateAllPlanItems(this.currentWeeklyPlan)
          // 还有剩余的周
          if (this.currentWeekPlanIndex < this.weeklyPlans.length - 1 && !toLessonDetail) {
            this.currentWeekPlanIndex = this.currentWeekPlanIndex + 1
            this.updatePlanItems(this.currentWeeklyPlan, this.changeWeek, false)
            // 下一周埋点
            this.$analytics.sendEvent('web_unit_create3_next_week')
            return
          }
          this.updatePlanItems(this.currentWeeklyPlan, this.goNextPage, false)
          // 下一个课程埋点
          this.$analytics.sendEvent('cg_unit_create_three_lessonall')
        }).catch(() => {
        })
        return
      }
      // 还有剩余的周
      if (this.currentWeekPlanIndex < this.weeklyPlans.length - 1 && !toLessonDetail) {
        this.currentWeekPlanIndex = this.currentWeekPlanIndex + 1
        this.updatePlanItems(this.currentWeeklyPlan, this.changeWeek, false)
        return
      }
      // 最后一周，进入下一步
      this.updatePlanItems(this.currentWeeklyPlan, this.goNextPage, false)
    },

    // 进入课程详情页
    goNextPage () {
      // 找到第一个未生成课程的周计划项
      let uncompletedItem = null
      // 先从当前选中的周计划中找未生成课程的 item
      let items = [...this.currentWeeklyPlan.items, ...this.currentWeeklyPlan.centerItems]
      // 找到未生成课程的 item
      uncompletedItem = items.find(item => !item.lessonId)
      if (!uncompletedItem) {
        // 如果当前周没有未生成课程的 item，则从所有的周计划中找
        this.weeklyPlans.forEach(weeklyPlan => {
          if (!uncompletedItem) {
            let items = [...weeklyPlan.items, ...weeklyPlan.centerItems]
            // 找到未生成课程的 item
            uncompletedItem = items.find(item => !item.lessonId)
          }
        })
      }
      let lessonDetailRoute = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-detail-cg-designer' : 'lesson-detail-cg' : 'lessonDetail'
      if (this.unitInfo.adaptedType) {
        lessonDetailRoute = 'lesson-detail-cg-adapt'
      }
      let goNextPage = () => {
        // 进入下一步
        this.$router.push({
          name: lessonDetailRoute,
          params: {
            unitId: this.unitId,
            itemId: uncompletedItem && uncompletedItem.id
          }
        })
      }
      // 停止生成课程任务
      this.$refs.lessonOverviewRef.stopGenerateLessonTask(true, false, uncompletedItem)
      // 如果本单元未弹出过推荐模板且需要批量推荐，则弹出推荐课程模板弹窗或者推荐模板
      if (!this.baseInfo.showLessonTemplateTip && this.needRecommendLessonTemplate) {
        // 判断当前用户是否完整创建过 unit,如果完整创建过 unit,则不弹出推荐模板弹窗, 默认使用推荐模板
        if (!this.isCompleteUnit) {
          this.recommendLessonTemplate(goNextPage)
        } else {
          this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.openDialog(goNextPage)
        }
      } else if (this.baseInfo.useLessonTemplate && this.needRecommendLessonTemplate) {
        // 如果本单元已经弹出过推荐模板且需要批量推荐，则直接推荐模板
        this.recommendLessonTemplate(true, goNextPage)
      } else {
        goNextPage()
      }
    },

    // 显示推荐课程模板弹窗
    showLessonTemplateRecomendDialog (callback, itemId) {
      // 如果当前用户未完整创建过 unit，则不弹出推荐模板弹窗, 默认使用推荐模板
      if (!this.isCompleteUnit) {
        this.recommendLessonTemplate(callback, itemId)
      } else {
        this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.openDialog(callback, itemId)
      }
    },

    // 不使用自动推荐模板
    async cancelRecommendLessonTemplate (callback) {
      await this.updateUnitUseLessonTemplateInfo(false)
      callback && callback()
    },

    // 推荐课程模板
    async recommendLessonTemplate (callback, itemId) {
      let params = {
        unitId: this.unitId,
        planId: this.currentWeeklyPlan.id,
        itemId: itemId
      }
      await this.$axios.post($api.urls().recommendLessonTemplate, params)
      .then(async (res) => {
        // 遍历当前周计划活动项，将推荐的课程模板赋值给对应的活动项
        this.currentWeeklyPlan.items.forEach(item => {
          let recommendItem = res.recommendLessonTemplateModelList.find(x => equalsIgnoreCase(x.itemId, item.id))
          if (recommendItem) {
            this.$set(item, 'lessonTemplateType', recommendItem.recommendedTemplate)
          }
        })
        await this.updateUnitUseLessonTemplateInfo(true)
        callback && callback()
      })
    },

    // 切换周
    changeWeek (index) {
      // 正在生成 idea 时禁止切换周
      if (this.changeWeekDisabled) {
        return
      }
      if (index >= 0) {
        this.currentWeekPlanIndex = index
      }
      this.currentWeeklyPlan = this.weeklyPlans[this.currentWeekPlanIndex]
      let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-overview-cg-designer' : 'lesson-overview-cg' : 'lessonOverview'
      if (this.unitInfo.adaptedType) {  
        routeName = 'lesson-overview-cg-adapt'
      }
      // 更新路由
      this.$router.push({
        name: routeName,
        params: {
            unitId: this.unitId,
            week: this.currentWeekPlanIndex + 1
        }
      })
      // 切换周后，滚动到顶部
      this.$refs.lessonOverviewRef.scrollToTop()
    },

    // 更新当前周课程列表
    updateCurrentWeeklyPlanLessons () {
      if (!this.currentWeeklyPlan) {
        return []
      }
      this.lessonOverviews = this.currentWeeklyPlan.items
    },
    // 检查设备类型
    checkDeviceType() {
      this.isMobile = window.innerWidth <= 768
    },
    // 查看课程详情
    viewDetail(currentWeeklyPlan) {
      if (!currentWeeklyPlan || !currentWeeklyPlan.items || currentWeeklyPlan.items.length === 0) {
        return
      }
      // 遍历 currentWeeklyPlan 中的 items 找到第一个 itemId
      let itemId = null
      for (let i = 0; i < currentWeeklyPlan.items.length; i++) {
        const item = currentWeeklyPlan.items[i]
        if (item.lessonId) {
          itemId = item.id
          break
        }
      }
      if (!itemId) {
        return
      }

      let params = {
        unitId: this.unit.id,
        itemId: itemId
      }
      let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? this.isGenieCurriculumToUnitPlanThird ? 'lesson-detail-cg-designer' : 'lesson-detail-cg' : 'lessonDetail'
      if (this.unitInfo.adaptedType) {
        routeName = 'lesson-detail-cg-adapt'
      }
      this.$router.push({
        name: routeName,
        params: params
      })
    },

    batchGenerateLessons() {
      // 设置按钮 loading 状态
      this.batchGenerateLoading = true
      
      // 判断是否全部周次都生成过课程，如果全部周次都生成过课程，则跳转到课程详情页
      if (this.allWeekCompleted) {
        this.batchGenerateLoading = false
        this.viewDetail(this.currentWeeklyPlan)
        return
      }

      //
      // 关闭生成详情引导
      this.showGenerateDetailGuideTemp = false
      // 执行 unitPlannerStep3GenerateDetailedLessonPlans方 法的逻辑，但不进行路由跳转
      const uncompletedLessons = this.lessonOverviews.filter(lesson => lesson.deleted)
      if (uncompletedLessons.length > 0) {
        let uncompletedDays = uncompletedLessons.map(lesson => lesson.day).join(', ')
        let errorMsg = 'You haven\'t generated the lesson for ' + uncompletedDays + '.'
        this.$message.error(errorMsg)
        this.batchGenerateLoading = false
        return
      }

      // 当前周次未确认的
      const unconfirmedLessons = [...this.lessonOverviews.filter(lesson => !lesson.confirmed), ...this.centerOverviews.filter(lesson => !lesson.confirmed)]
      if (unconfirmedLessons.length > 0) {
        this.$confirm(this.$t('loc.unitPlannerStep3Confirmation4', { week: this.currentWeekPlanIndex + 1 }), this.$t('loc.confirmation'), {
          confirmButtonText: this.$t('loc.unitPlannerStep3Confirm'),
          cancelButtonText: this.$t('loc.cancel')
        }).then(() => {
          // 当前周下的活动项全部确认
          this.currentWeeklyPlan.items.forEach(item => {
            this.$set(item, 'confirmed', true)
          })
          this.currentWeeklyPlan.centerItems.forEach(item => {
            this.$set(item, 'confirmed', true)
          })
          this.updateAllPlanItems(this.currentWeeklyPlan)
          // 继续执行批量生成逻辑
          this.continueBatchGenerateLessons()
        }).catch(() => {
          // 用户取消，不执行后续操作
          this.batchGenerateLoading = false
        })
        return
      }

      // 直接执行批量生成逻辑
      this.continueBatchGenerateLessons()
    },

    // 继续执行批量生成逻辑的方法
    async continueBatchGenerateLessons() {
      let itemId = null
      let batchGenerate = false
      // 判断 weeklyPlans 中是否已经生成的有课程
      if (this.weeklyPlans && this.weeklyPlans.length > 0) {
        // 遍历 weeklyPlans 中的每一周，判断是否已经生成的有课程
        for (let i = 0; i < this.weeklyPlans.length; i++) {
          const weeklyPlan = this.weeklyPlans[i]
          // 若 batchGenerate 为 true，则跳出循环
          if (batchGenerate) {
            break
          }
          if (weeklyPlan && weeklyPlan.items && weeklyPlan.items.length > 0) {
            // 遍历 weeklyPlan.items 中的每一项，判断是否已经生成的有课程
            for (let j = 0; j < weeklyPlan.items.length; j++) {
              const item = weeklyPlan.items[j]
              //  找到第一个没有课程 ID 的 item
              if (!item.lessonId) {
                itemId = item.id
                batchGenerate = true
              }
              if (batchGenerate) {
                break
              }
            }
          }
        }
      }

      // 处理推荐模板逻辑
      const routeToLessonDetail = async () => {
        let params = {
          unitId: this.unitId,
          itemId: itemId,
        }
        let routeName = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'lesson-detail-cg' : 'lessonDetail'
        if (this.unitInfo.adaptedType) {
          routeName = 'lesson-detail-cg-adapt'
        }
        // 点击批量生成课程按钮埋点
        this.$analytics.sendEvent('cg_unit_create_lesson_batch')
        // 关闭批量引导弹框
        localStorage.setItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP', false)
        // 执行批量生成
        await this.batchGenerateUnitLessons(true, itemId)
        //将 this.batchGenerateLessonFlag 保存到 localStorage
        localStorage.setItem('batchGenerateLessonFlag', this.batchGenerateLessonFlag)
        this.$router.push({
          name: routeName,
          params: params
        })
      }

      // 停止生成课程任务
      this.$refs.lessonOverviewRef.stopGenerateLessonTask(true, false, { id: itemId })

      // 处理推荐模板逻辑 - 从goNextPage方法中复制的逻辑
      // 如果本单元未弹出过推荐模板且需要批量推荐，则弹出推荐课程模板弹窗或者推荐模板
      if (!this.baseInfo.showLessonTemplateTip && this.needRecommendLessonTemplate) {
        // 判断当前用户是否完整创建过 unit,如果完整创建过 unit,则不弹出推荐模板弹窗, 默认使用推荐模板
        if (!this.isCompleteUnit) {
          await this.recommendLessonTemplate(routeToLessonDetail)
        } else {
          // 打开模板推荐弹框，只在用户确认使用模板后才执行routeToLessonDetail回调
          this.$refs.LessonTemplateRecomendRef && this.$refs.LessonTemplateRecomendRef.openDialog(routeToLessonDetail)
        }
      } else if (this.baseInfo.useLessonTemplate && this.needRecommendLessonTemplate) {
        // 如果本单元已经弹出过推荐模板且需要批量推荐，则直接推荐模板
        await this.recommendLessonTemplate(routeToLessonDetail)
      } else {
        routeToLessonDetail()
      }
    },
    // 批量生成课程
    async batchGenerateUnitLessons(isBatch = true, itemId) {
      // 点击批量重置重试次数
      this.$store.dispatch('unit/setResetMaxRetry', 0)
      if (this.batchGenerateVisible) {
        this.hideGuide()
      }
      // 点击批量时先为 Vuex 中的 appearNotice 设置为 false
      this.$store.dispatch('unit/setAppearNotice', false)
      // 点击批量生成时先将 Vuex 中的数据清除
      this.$store.dispatch('unit/setBatchTasks', null)
      const result = this.unGenerateLessons()
      var items = [...new Set(result.items)]
      // items 中去除 itemId
      items = items.filter(item => !equalsIgnoreCase(item.itemId, itemId))
      // 判断 items 是否为空
      if (items.length === 0) {
        this.batchGenerateLoading = false
        return
      }
      const params = {
        unitId: this.unit.id,
        generateItems: items
      }
      // 判断是否是批量生成
      if (isBatch) {
        params.batch = true
      }
      this.batchGenerateLessonFlag = true
      // 添加 return 和 await，确保等待异步操作完成
      try {
        const res = await Lessons2.createGenerateLessonTasks(params)
        this.$store.dispatch('unit/setBatchId', res.id)
        this.batchGenerateLoading = false // 成功时停止 loading
        return res // 返回结果
      } catch (error) {
        this.unGenerateLessons()
        this.$store.dispatch('unit/setAppearNotice', true)
        this.batchGenerateLoading = false // 失败时停止 loading
        throw error // 重新抛出错误
      }
    },
    // 未生成课程的活动项
    unGenerateLessons (processing = false) {
      let items = []
      let existUnConfirmed = false
      this.weeklyPlans.forEach(weeklyPlan => {
        weeklyPlan.items.forEach(item => {
          // 如果已经生成过了，无需再次生成
          if (item.lessonId) {
            return
          }
          if (!item.confirmed) {
            existUnConfirmed = true
          }
          if (processing) {
            this.$set(item, 'processing', true)
          } else {
            this.$set(item, 'processing', false)
          }
          const itemVo = {
            planId: item.planId,
            itemId: item.id
          }
          items.push(itemVo)
        })
        weeklyPlan.centerItems.forEach(item => {
          // 如果已经生成过了，无需再次生成
          if (item.lessonId) {
            return
          }
          if (!item.confirmed) {
            existUnConfirmed = true
          }
          if (processing) {
            this.$set(item, 'processing', true)
          } else {
            this.$set(item, 'processing', false)
          }
          const itemVo = {
            planId: item.planId,
            itemId: item.id
          }
          items.push(itemVo)
        })
      })
      return {
        items: items,
        existUnConfirmed: existUnConfirmed
      }
    }
  }
}
</script>

<style lang="less" scoped>
.lg-z-index-10 {
  z-index: 10;
}

.unit-overview-guide{
}

.el-divider--horizontal {
  margin: 0;
}

.lg-card-item {
  gap: 5px;
}

.el-popper{
  background-color: #878BF9 !important;

}

::v-deep {
  .el-popover {
    padding: 0px !important;
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
  }

  .el-form-item__label {
    margin-bottom: 0;
    line-height: 22px;
    font-weight: 600;
  }

  .el-form-item__error {
    padding-left: 0;
  }

  .el-radio-group {
    .el-radio-button {
      background-color: #DDF2F3 !important;
      padding: 4px;
      box-sizing: border-box;
    }

    .el-radio-button__inner {
      padding: 6px 10px;
      background-color: #DDF2F3;
      color: #10b3b7;
      border: none;
      border-radius: 4px;
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: #10b3b7;
      color: #fff;
    }
  }
}
.process-style {
  border-width: 0 !important;
  height: 24px;
  line-height: 24px;
  border-radius: 0 4px;
  position: absolute;
  right: 0;
  top: 0;
}

.week-theme {
  height: 48px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.feedback-slot {
    height: 0;
    margin-bottom: 0;
    position: relative;
    top: -38px;
}

.lesson-overview-bottom-btn-group {
  position: sticky;
  bottom: 0;
  background: #f5f6f8;
  padding-top: 10px;
}

.feedback-left {
  margin-right: 10px;
}

.unit-planner-over-view-dialog {
  /deep/ .el-dialog {
    margin: 0!important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 95%;
    display: flex;
    flex-direction: column;
  }
  /deep/ .el-dialog__body {
    overflow-y: auto;
    color: #606266;
    word-break: break-all;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 24px 0px 24px;
    font-size: 14px;
  }

  /deep/ .el-dialog__title {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    color: #323338;
  }

  /deep/ .el-dialog__header {
    text-align: left;
    padding: 24px 24px 0;
  }

  /deep/ .el-dialog__footer {
    padding-top: 0px;
  }
}
.dei-icon {
  padding: 5px !important;
}
.dei-content {
  /deep/ .ql-container.ql-snow {
    height: 367px!important;
  }
}

::v-deep {
  .operation-panel {
    width: 33vw;
    flex-shrink: 0;
  }
}

/deep/ .el-dialog.view-edit-lesson-dialog .el-dialog__header .el-dialog__title {
  font-size: 20px !important;
}

.guide-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.guide-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.guide-description {
  color: white;
  font-size: 14px;
}

.ai-btn {
  margin-left: 15px;
}
</style>

<style lang="less">
.generate-detail-lesson-guide_temp {
  padding: 0px !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  animation: vertical-shake 0.8s ease-in-out infinite;
  -webkit-animation: vertical-shake 0.8s ease-in-out infinite;
  .guide-content {
    position: relative;
    -webkit-box-shadow: none;
    min-height: 110px;
    .guide-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .close-icon {
    position: absolute;
    top: 10px;
    right: 13px;
    font-size: 16px;
    cursor: pointer;
  }
}
</style>