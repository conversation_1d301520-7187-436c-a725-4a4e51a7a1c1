<template>
  <div class="wrapper-md bg-white">
    <!-- 切换标签 -->
    <div class="lg-tabs-small m-b-sm display-flex justify-content">
      <el-tabs v-model="currentUnitId">
        <el-tab-pane :label="u.name" :name="u.id" :disabled="u.disable" v-for="(u, index) in units"
                     :key="index"></el-tab-pane>
      </el-tabs>
    </div>
    <!-- 单元内容 -->
    <div v-loading="getUnitLoading" class="unit-doc">
      <!-- 单元标题 -->
      <h1>
        {{ unit.title }}
      </h1>
      <br/>
      <!-- 平均分数 -->
      <!-- <div class="" v-show="averageScore > 0">
          <div>
              <span class="font-bold font-size-16">Average Score</span>: {{ averageScore }}
          </div>
      </div>
      <br/> -->
      <div>
        <div class="font-bold font-size-16">Unit Overview</div>
        <div v-html="formatContent(unit.overview)"></div>
      </div>
      <br/>
      <div>
        <div class="font-bold font-size-16">Unit Trajectory</div>
        <div v-html="formatContent(unit.trajectory)"></div>
      </div>
      <br/>
      <div>
        <div class="font-bold font-size-16">Concepts</div>
        <div v-html="formatContent(unit.concepts)"></div>
      </div>
      <br/>
      <div>
        <div class="font-bold font-size-16">Guiding Questions</div>
        <div v-html="formatContent(unit.guidingQuestions)"></div>
      </div>
      <br/>
      <!-- 周计划 -->
      <div v-if="unit.weeklyPlans && unit.weeklyPlans.length > 0">
        <div v-for="(plan, index) in unit.weeklyPlans" :key="index">
          <h2>
            Week {{ index + 1 }}: {{ plan.theme }}
          </h2>
          <br/>
          <div>
            <div class="font-bold font-size-16">Weekly Overview</div>
            <div v-html="formatContent(plan.overview)"></div>
          </div>
          <br/>
          <!-- 课程 -->
          <div>
            <div v-for="(item, index) in plan.items" :key="index">
              <div v-if="item.lesson">
                <h3 v-if="item.centerGroupName">
                  {{ item.centerGroupName }}: {{ item.lesson.name }}
                </h3>
                <h3 v-else>
                  {{ item.day }}: {{ item.lesson.name }}
                </h3>
                <br/>
                <!-- 评分 -->
                <!-- <div class="display-flex align-items">
                    <div>
                        <span class="font-bold font-size-16">Score</span>: {{ item.lesson.score }}
                    </div>
                    <el-button class="m-l-sm" size="mini" @click="showEvaluationResult(item.lesson)">Evaluation Result</el-button>
                </div>
                <br/> -->
                <div>
                  <div>
                    <span class="font-bold font-size-16">Activity Type</span>: {{ item.activityType }}
                  </div>
                </div>
                <br/>
                <div>
                  <div>
                    <span class="font-bold font-size-16">Age Group</span>: {{ item.lesson.ageGroup }}
                  </div>
                </div>
                <br/>
                <div>
                  <div>
                    <span class="font-bold font-size-16">Activity Preparation Time</span>: {{ item.lesson.prepareTime }}
                  </div>
                </div>
                <br/>
                <div>
                  <div>
                    <span class="font-bold font-size-16">Activity Duration</span>: {{ item.lesson.activityTime }}
                  </div>
                </div>
                <br/>
                <div>
                  <div class="font-bold font-size-16">Measures</div>
                  <div>
                    <div v-for="(measure, index) in item.lesson.measures" :key="index">
                      {{ measureName(measure) }}
                    </div>
                  </div>
                </div>
                <br/>
                <template v-if="item.lesson.objectives">
                  <div>
                    <div class="font-bold font-size-16">Objectives</div>
                    <div v-html="formatContent(item.lesson.objectives)"></div>
                  </div>
                  <br/>
                </template>
                <template v-if="item.lesson.materials">
                  <div>
                    <div class="font-bold font-size-16">Materials</div>
                    <div v-html="formatContent(item.lesson.materials)"></div>
                  </div>
                  <br/>
                </template>
                <template v-if="item.lesson.keyVocabularyWords">
                  <div>
                    <div class="font-bold font-size-16">Key Vocabulary Words & Child-Friendly Definitions</div>
                    <div v-html="formatContent(item.lesson.keyVocabularyWords)"></div>
                  </div>
                  <br/>
                </template>
                <template v-if="item.lesson.implementationSteps">
                  <div>
                    <div class="font-bold font-size-16">Implementation Steps</div>
                    <div v-html="formatContent(item.lesson.implementationSteps)"></div>
                  </div>
                  <br/>
                </template>
                <template v-if="item.lesson.typicalBehaviors && item.lesson.typicalBehaviors.length > 0">
                  <div>
                    <div class="font-bold font-size-16">Typical Behaviors and Observation Tips</div>
                    <div>
                      <div v-for="(behavior, index) in item.lesson.typicalBehaviors" :key="index">
                        <div class="">{{ measureName(behavior.measureAbbreviation) }}</div>
                        <div v-html="formatContent(behavior.typicalBehaviors)"></div>
                        <br/>
                      </div>
                    </div>
                  </div>
                  <br/>
                </template>
                <template v-if="item.lesson.universalDesignForLearning">
                  <div>
                    <div class="font-bold font-size-16">Universal Design with Differentiated Learning</div>
                    <div v-html="formatContent(item.lesson.universalDesignForLearning)"></div>
                  </div>
                  <br/>
                </template>
                <!-- 分组课程差异化学习 -->
                <template v-if="item.lesson.universalDesignForLearningGroup">
                  <div>
                    <!-- 标题 -->
                    <div class="font-bold font-size-16">
                      Universal Design with Differentiated Learning
                      <!-- <span class="m-l-xs text-success">(Demo Class)</span> -->
                    </div>
                    <!-- 小孩分组 -->
                    <div class="bg m-b-sm lg-pa-10 m-t-sm"
                         v-show="false && item.lesson && item.lesson.udlGroups && item.lesson.udlGroups.length > 0">
                      <div class="display-flex justify-content-between">
                        <div class="font-bold">Inclusive Learning Groups</div>
                        <div class="display-flex">
                          <!-- ELD Children -->
                          <div class="display-flex align-items m-r-sm">
                            <div class="eld-bg-color point m-r-xs"></div>
                            <div>ELD Children</div>
                          </div>
                          <!-- IEP Children -->
                          <div class="display-flex align-items">
                            <div class="iep-bg-color point m-r-xs"></div>
                            <div>IEP Children</div>
                          </div>
                        </div>
                      </div>
                      <div v-for="(group, index) in item.lesson.udlGroups" :key="index"
                           class="display-flex align-items child-group">
                        <div class="font-bold m-t-sm m-r-sm flex-none">
                          {{ group.groupName }}:&nbsp;
                        </div>
                        <div class="display-flex align-items child-group-list">
                          <div v-for="(childName, index) in group.childNames" :key="index"
                               class="m-t-sm m-r-sm child-tag">
                            <span
                                :class="{'eld-color': isELDChildName(childName), 'iep-color': isIEPChildName(childName)}">{{
                                childName
                              }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-html="formatContent(item.lesson.universalDesignForLearningGroup)"></div>
                  </div>
                  <br/>
                </template>
                <template v-if="item.lesson.culturallyResponsiveInstruction">
                  <div>
                    <div class="font-bold font-size-16">Culturally and Linguistically Responsive Practice</div>
                    <div v-html="formatContent(item.lesson.culturallyResponsiveInstruction)"></div>
                  </div>
                  <br/>
                </template>
                <!-- 分组课程文化和语言反应实践 -->
                <template v-if="item.lesson.culturallyResponsiveInstructionGroup">
                  <div>
                    <div class="font-bold font-size-16">
                      Culturally and Linguistically Responsive Practice
                      <!-- <span class="m-l-xs text-success">(Demo Class)</span> -->
                    </div>
                    <!-- 小孩分组 -->
                    <div class="bg m-b-sm lg-pa-10 m-t-sm" v-show="false && children && children.length > 0">
                      <div class="display-flex justify-content-between">
                        <div class="font-bold">Races and Home Languages</div>
                        <div class="display-flex">
                        </div>
                      </div>
                      <div class="display-flex" style="flex-wrap: wrap;">
                        <div v-for="(child, index) in children" :key="index" class="child-tag m-r-sm m-t-sm">
                          <span>{{ child.displayName }} - {{ childRaceAndLanguage(child) }}</span>
                        </div>
                      </div>
                    </div>
                    <div v-html="formatContent(item.lesson.culturallyResponsiveInstructionGroup)"></div>
                  </div>
                  <br/>
                </template>
                <template v-if="item.lesson.homeActivity">
                  <div>
                    <div class="font-bold font-size-16">At-Home Activities</div>
                    <div v-html="formatContent(item.lesson.homeActivity)"></div>
                  </div>
                  <br/>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 评估结果弹窗 -->
    <el-dialog title="Evaluation Result" :visible.sync="evaluationResultVisible" width="80%"
               v-if="evaluationResultVisible">
      <div v-html="formatContent(evaluationResult)"></div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  data () {
    return {
      getUnitLoading: false, // 获取单元详情 loading
      loadingFrameworkLoading: false, // 获取框架 loading
      domains: [], // 测评点
      currentUnitId: '', // 当前单元 id
      units: [ // 单元
        { id: '346E8610-D2E3-43D6-BA87-E7E7D9A619D5', name: 'Teaching Pyramid' },
        { id: '9BEAC8DB-C94E-423C-AD42-43C39F04F085', name: 'All About Me' },
        { id: '830B9A9C-B301-43B6-A2A3-C00108F8DA95', name: 'My Community' },
        { id: '8849E778-FDE1-46DE-BA30-1151FEA2AF0A', name: 'Healthy Me, Healthy Us' },
        { id: '02931ED9-0606-415A-A7B6-336AF712E3CA', name: 'Imagination', disable: false },
        { id: 'DD4B88E5-EFE9-44DF-9B66-02C417FE286B', name: 'The Natural World' },
        { id: 'FC1AF4DA-BA7C-412A-BCFF-0456AD971283', name: 'We\'re Growing!' }
      ],
      evaluationResultVisible: false, // 评估结果弹窗
      evaluationResult: null, // 评估结果
      averageScore: 0, // 平均分
      listChildrenLoading: false, // 加载学生列表 Loading
      children: [], // 小孩列表
      iepChildNames: [], // IEP 小孩姓名列表
      eldChildNames: [] // ELD 小孩姓名列表
    }
  },

  created () {
    // 获取路由参数 unitId
    const unitId = this.$route.params.unitId
    if (unitId) {
      // 如果路由中有单元 ID，则查询该单元数据
      this.currentUnitId = unitId
    } else {
      // 路由中没有单元 ID，则默认取第一个单元
      this.currentUnitId = this.units[0].id
    }
  },

  watch: {
    currentUnitId (val) {
      if (val) {
        this.getUnit(val)
      }
    }
  },

  computed: {
    ...mapState({
      unit: state => state.curriculum.unit // 单元
    }),
    formatContent () {
      return (content) => {
        if (!content || typeof content !== 'string') {
          return ''
        }
        return content.replace(/\n/g, '<br>')
            .replace(/<img/g, '<img style="display: none;"')
      }
    },
    // 测评点名称
    measureName () {
      return function (abbr) {
        if (!abbr) {
          return ''
        }
        // 没有框架信息，直接返回
        if (!this.domains || this.domains.length === 0) {
          return abbr
        }
        // 对应的测评点
        let measure = this.getMeasureByAbbr(this.domains, abbr)
        // 没有找到测评点，则直接返回缩写
        if (!measure) {
          return abbr
        }
        // 拼接缩写和名称
        return `${abbr}: ${measure.name}`
      }
    },
    // 测评点描述
    measureDescription () {
      return function (abbr) {
        if (!abbr) {
          return ''
        }
        // 没有框架信息，直接返回
        if (!this.domains || this.domains.length === 0) {
          return ''
        }
        // 对应的测评点
        let measure = this.getMeasureByAbbr(this.domains, abbr)
        // 没有找到测评点，则直接返回
        if (!measure) {
          return ''
        }
        // 拼接缩写和名称
        return measure.description
      }
    },
    // 获取指定属性的值
    getAttrValue () {
      return function (child, attrName) {
        // 不存在返回空
        if (!child || !attrName) {
          return ''
        }
        // 属性列表
        let attrs = child.attrs
        if (!attrs) {
          return ''
        }
        // 匹配到的属性值
        let matchValues = null
        // 遍历属性列表
        attrs.forEach(attr => {
          // 匹配属性名称
          if (attr && attr.name && attr.name.trim().toLowerCase() === attrName.trim().toLowerCase()) {
            // 属性值
            let attrValues = attr.values
            if (attrValues && attrValues.length > 0) {
              matchValues = attrValues
            }
          }
        })
        // 如果有属性值，以逗号分割
        if (matchValues) {
          return matchValues.join(', ')
        }
        // 没有值
        return ''
      }
    },
    // 获取语言和种族
    childRaceAndLanguage () {
      return function (child) {
        let race = this.getAttrValue(child, 'Race')
        let language = this.getAttrValue(child, 'Language')
        let result = ''
        if (race) {
          result += race
        }
        if (language) {
          result += ', ' + language
        }
        return result
      }
    },
    // 获取名称对应的小孩
    getChildByName () {
      return function (childName) {
        if (!this.children) {
          return null
        }
        let filterChildren = this.children.filter(child => child && child.displayName === childName)
        if (!filterChildren || filterChildren.length === 0) {
          return null
        }
        return filterChildren[0]
      }
    },
    // 是否是 IEP
    isIEPChildName () {
      return function (childName) {
        return this.iepChildNames && this.iepChildNames.includes(childName)
      }
    },
    // 是否是 ELD
    isELDChildName () {
      return function (childName) {
        return this.eldChildNames && this.eldChildNames.includes(childName)
      }
    },
    // 是否是 IEP
    isIEP () {
      return function (child) {
        if (!child) {
          return false
        }
        let iepValues = this.getAttrValue(child, 'IEP/IFSP')
        return iepValues && iepValues === 'Yes'
      }
    },
    // 是否是 ELD
    isELD () {
      return function (child) {
        if (!child) {
          return false
        }
        let values = this.getAttrValue(child, 'ELD')
        return values && values === 'Yes'
      }
    }
  },

  methods: {
    // 加载单元数据
    getUnit () {
      // 获取路由参数 unitId
      const unitId = this.currentUnitId
      // 没有指定单元 ID 或者和当前已有 ID 相同则跳过
      if (!unitId || unitId === this.unit.id) {
        return
      }
      this.getUnitLoading = true
      // 请求参数
      let params = {
        params: {
          unitId: unitId,
          includeEvaluation: true
        }
      }
      // 重置单元
      this.$store.commit('curriculum/RESET_UNIT')
      // 获取单元详情
      this.$axios
          .get($api.urls().getUnit, params).then((res) => {
        let unit = res.unit
        // 单元基本信息
        unit.baseInfo = {
          title: unit.title, // 标题
          state: unit.state, // 状态
          city: unit.city, // 城市
          description: unit.description, // 描述
          frameworkId: unit.frameworkId, // 框架 ID
          grade: unit.grade, // 年级
          language: unit.language, // 语言
          weekCount: unit.weekCount // 周数
        }
        this.$store.commit('curriculum/SET_UNIT', unit)
        // 总分数
        let totalScore = 0
        // 课程总数
        let lessonCount = 0
        let frameworkId = null // 框架 ID
        let groupId = null // 班级 ID
        unit.weeklyPlans.forEach(p => {
          p.items.forEach(i => {
            if (!frameworkId && i.lesson && i.lesson.frameworkId) {
              frameworkId = i.lesson.frameworkId
            }
            if (i.lesson && i.lesson.score) {
              totalScore += i.lesson.score
              lessonCount++
            }
            // 分组课程班级 ID
            if (!groupId && i.lesson && i.lesson.groupId) {
              groupId = i.lesson.groupId
            }
          })
        })
        // 平均分
        this.averageScore = lessonCount > 0 ? (totalScore / lessonCount).toFixed(2) : 0
        this.getFrameworkDomains(frameworkId)
        // 获取班级小孩列表
        if (groupId) {
          this.listChildren(groupId)
        }
      }).catch(error => {
        this.getUnitLoading = false
        console.log(error)
        this.$message.error(error.response.data.error_message)
      })
    },

    // 获取框架测评点
    getFrameworkDomains (frameworkId) {
      this.loadingFrameworkLoading = true
      this.$store.dispatch('curriculum/getFrameworkDomains', frameworkId).then(domains => {
        this.domains = domains
        this.loadingFrameworkLoading = false
        this.getUnitLoading = false
      })
    },

    // 根据缩写获取测评点信息
    getMeasureByAbbr (domains, abbr) {
      if (!abbr || !domains || domains.length === 0) {
        return null
      }
      // 遍历框架
      for (let i = 0; i < this.domains.length; i++) {
        const domain = this.domains[i]
        // 遍历测评点
        for (let j = 0; j < domain.children.length; j++) {
          const measure = domain.children[j]
          if (measure.abbreviation === abbr) {
            return measure
          }
        }
      }
      return null
    },

    // 显示评估结果
    showEvaluationResult (lesson) {
      this.evaluationResult = lesson.evaluationResult
      this.evaluationResultVisible = true
    },

    // 获取小孩列表
    listChildren (groupId) {
      // 开始 Loading
      this.listChildrenLoading = true
      // 参数
      let params = {
        pageNum: 1,
        pageSize: 1000,
        sort: 'lastName',
        order: 'asc',
        groupId: groupId
      }
      // 调用接口
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().manageChildren, { params: params }).then(res => {
          this.children = res.results // 小孩列表
          // 遍历小孩
          if (this.children) {
            this.iepChildNames = []
            this.eldChildNames = []
            this.children.forEach(child => {
              if (this.isIEP(child)) {
                this.iepChildNames.push(child.displayName)
              }
              if (this.isELD(child)) {
                this.eldChildNames.push(child.displayName)
              }
            })
          }
          // 停止 Loading
          this.listChildrenLoading = false
          resolve()
        }).catch(error => {
          // 停止 Loading
          this.listChildrenLoading = false
          console.log(error)
          this.$message.error(error.response.data.error_message)
          reject(error)
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.unit-doc {
  min-height: 80vh;
  background-color: #FFF;
}

.child-tag {
  border-radius: 90px;
  background: #FFF;
  padding: 4px 10px;
}

.eld-color {
  color: #002EFE;
}

.iep-color {
  color: #F49628;
}

.eld-bg-color {
  background-color: #002EFE;
}

.iep-bg-color {
  background-color: #F49628;
}

.point {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
</style>
