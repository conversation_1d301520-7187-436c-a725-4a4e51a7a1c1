<template>
  <div class="unit-description-import-div">
    <div v-if="showDesAndVoiceBtn" class="display-flex gap-4 position-absolute" style="right: -10px">
      <div class="des-voice-btn" @click="showImportDialogFun('des')">✍️ {{ $t('loc.unitDescriptionAssistant') }}</div>
      <div class="des-voice-btn" @click="showImportDialogFun('voice')">👨‍🎓 {{ $t('loc.studentVoice') }}</div>
    </div>
    <!-- 开启引导输入弹窗的按钮 -->
    <el-button v-else @click="showImportDialogFun('')" size="mini" round :class="importGuideBtnClass" class="input-button-position">{{$t('loc.unitDescriptionAssistant')}}</el-button>
    <!-- 引导输入的弹窗 -->
    <el-dialog :append-to-body="true" :title="$t('loc.unitDescriptionGuideTitle')" :visible.sync="showImportDialog"
               @closed="importInfo = getDefaultImportInfo()" :close-on-click-modal="false" custom-class="import-info-dialog">
      <el-form :model="importInfo">
        <!-- 第一部分 -->
        <el-form-item :label="'1. ' + $t('loc.unitDescriptionGuideDescription1')" class="description-label" v-show="!showModelName || showModelName === 'des'">
          <el-input ref="importInfoDescription1" v-model="importInfo.description1" type="textarea" :autosize="{ minRows: 3, maxRows: 4}" maxlength="1000"
                    :placeholder="$t('loc.unitDescriptionGuidePlaceholder1')" />
        </el-form-item>
        <!-- 第二部分 -->
        <el-form-item class="description-label" v-show="!showModelName || showModelName === 'des'">
          <span slot="label">
            2. {{ $t('loc.unitDescriptionGuideDescription2') }}
            <el-tooltip effect="dark" :content="$t('loc.unitDescriptionGuideDescription2Tip')" placement="top">
              <i class="lg-icon lg-icon-question font-weight-400" v-show="showModelName !== 'des'"></i>
            </el-tooltip>
          </span>
          <div class="description2-tip" v-show="showModelName === 'des'">{{$t('loc.unitDescriptionGuideDescription2Tip')}}</div>
          <el-input ref="importInfoDescription2" v-model="importInfo.description2" type="textarea" :autosize="{ minRows: 3, maxRows: 4}" maxlength="1000"
                    :placeholder="$t('loc.unitDescriptionGuidePlaceholder2')" />
        </el-form-item>
        <!-- 第三部分 -->
        <el-form-item :label="(showModelName === 'voice' ? '1. ' : '3. ') + $t('loc.unitDescriptionGuideDescription3')" class="description-label" v-show="!showModelName || showModelName === 'voice'">
          <el-input ref="importInfoDescription3" v-model="importInfo.description3" type="textarea" :autosize="{ minRows: 3, maxRows: 4}" maxlength="1000"
                    :placeholder="$t('loc.unitDescriptionGuidePlaceholder3')" />
        </el-form-item>
        <!-- 第四部分 -->
        <el-form-item :label="(showModelName === 'voice' ? '2. ' : '4. ') + $t('loc.unitDescriptionGuideDescription4')" class="description-label" v-show="!showModelName || showModelName === 'voice'">
          <el-input ref="importInfoDescription4" v-model="importInfo.description4" type="textarea" :autosize="{ minRows: 3, maxRows: 4}" maxlength="1000"
                    :placeholder="$t('loc.unitDescriptionGuidePlaceholder4')" />
        </el-form-item>
        <el-form-item :label="'5. ' + $t('loc.unitDescriptionGuideDescription4')" class="description-label">
          <el-input ref="importInfoDescription4" v-model="importInfo.description5" type="textarea" :autosize="{ minRows: 3, maxRows: 4}" maxlength="1000"
                    :placeholder="$t('loc.unitDescriptionGuidePlaceholder4')" />
        </el-form-item>
      </el-form>
      <!-- 弹窗底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="showImportDialog = false">{{$t('loc.unitDescriptionGuideCancel')}}</el-button>
        <el-button type="primary" @click="importDialogConfirm" :disabled="showImportDialogConfirmBtn">{{$t('loc.unitDescriptionGuideConfirm')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { equalsIgnoreCase } from '@/utils/common'

export default {
  name: 'UnitDescriptionImport',
  props: {
    // unit description 输入框是否获得焦点
    unitDescriptionIsFocus: {
      type: Boolean,
      default: false
    },
    // 展示单元描述和学生声音按钮
    showDesAndVoiceBtn: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      importInfo: {
        description1: '',
        description2: '',
        description3: '',
        description4: '',
        description5: ''
      }, // 导入信息
      showImportDialog: false, // 是否显示弹窗
      showModelName: '', // 需要展示的模块的名称
    }
  },
  computed: {
    // 弹窗确认按钮是否禁用
    showImportDialogConfirmBtn () {
      const importInfo = this.importInfo
      for (let i = 1; i <= 4; i++) {
        // 判断导入信息是否为空
        if (importInfo['description' + i] && importInfo['description' + i].trim().length > 0) {
          return false
        }
      }
      return true
    },
    // 开启导入弹窗按钮的样式
    importGuideBtnClass () {
      return this.unitDescriptionIsFocus ? 'input-focus-button' : 'input-blur-button'
    }
  },
  methods: {
    // 打开弹窗触发的方法
    showImportDialogFun (model) {
      this.showModelName = model
      this.showImportDialog = true
      // 因为当前组件挂载完成时，dialog 组件还未挂载，所以需要当弹窗打开时，再给输入框补充滚动条的样式
      this.$nextTick(() => {
        // 给输入框补充滚动条的样式
        for (let i = 1; i <= 4; i++) {
          // 选取对应的输入框下的 textarea
          const description = this.$refs['importInfoDescription' + i]
          if (description && description.$el.querySelector('textarea')) {
            // 添加滚动条的样式类
            description.$el.querySelector('textarea').classList.add('lg-scrollbar-show')
          }
        }
      })
      // 点击学生声音埋点
      if (equalsIgnoreCase(model, 'voice')) {
        this.$analytics.sendEvent('cg_adapt_select_student_voice')
      }
    },
    // 导入弹窗确认，拼接导入信息合并进入 unit description 输入框
    importDialogConfirm () {
      const importInfo = this.importInfo
      let res = ''
      // 拼接导入信息
      for (let i = 1; i <= 4; i++) {
        if (importInfo['description' + i] && importInfo['description' + i].trim().length > 0) {
          // 添加标题和对应内容
          res += this.$t('loc.unitDescriptionGuideTitle' + i) + ':\n' + importInfo['description' + i].trim() + '\n'
        }
      }
      // 取出最后一个换行符
      res = res && res.length > 0 ? res.substring(0, res.length - 1) : res
      if (res && res.length > 0) {
        // 触发合并导入信息事件
        this.$emit('mergeImportInfo', res)
        // 清空导入信息
        this.importInfo = this.getDefaultImportInfo()
      }
      // 学生声音确定埋点
      if (equalsIgnoreCase(this.showModelName, 'voice')) {
        this.$analytics.sendEvent('cg_adapt_select_student_voice')
      }
      // 关闭弹窗
      this.showImportDialog = false
    },
    // 获取清空后的导入信息
    getDefaultImportInfo () {
      return {
        description1: '',
        description2: '',
        description3: '',
        description4: ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.unit-description-import-div {
  position: relative;
  left: 1%;
  width: 95%;
  bottom: 41px;
  background-color: var(--color-white);
  height: 40px;
}
.input-focus-button {
  border: none;
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}
.input-blur-button {
  border: none;
  background-color: var(--color-page-background-white);
  color: var(--color-text-placeholder);
}
.input-button-position {
  position: absolute;
  right: -8px;
  top: 4px;
}
.input-button-position:hover {
  background-color: var(--color-primary-light) !important;
  color: var(--color-primary) !important;
}
/deep/ .description-label {
  margin-bottom: 24px;
  .el-form-item__label {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 8px;
    text-align: left;
    width: 100%;
  }
  .el-form-item__content {
    margin-top: 24px;
  }
}
/deep/ .import-info-dialog > .el-dialog__header > .el-dialog__title{
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
}
/deep/ .import-info-dialog > .el-dialog__body {
  padding-top: 14px;
  padding-bottom: 0;
}
/deep/ .import-info-dialog > .el-dialog__footer {
  padding-top: 0;
}

.des-voice-btn {
  height: 32px;
  padding: 0 12px;
  gap: 8px;
  border-radius: 16px;
  background: var(--f-5-f-6-f-8, #F5F6F8);
  color: #676879 !important;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  line-height: 32px;
  cursor: pointer;
}

.des-voice-btn:hover {
  background-color: var(--color-primary-light) !important;
  color: var(--color-primary) !important;
}

.description2-tip {
  margin-bottom: 8px;
  color: #676879 !important;
  font-size: 16px;
  font-style: italic;
  font-weight: 400;
  line-height: 24px;
}

///deep/ .el-form-item__content:before{
//  display: none;
//}
</style>
