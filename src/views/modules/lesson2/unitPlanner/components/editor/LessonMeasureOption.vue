<template>
  <el-option-group
    :label="domainTitle(domain)">
    <!-- <span>{{ domain.abbreviation }}: {{ domain.name }}</span> -->
    <el-option
      v-for="measure in domain.children"
      v-show="!measure.iepmeasure"
      :key="measure.id"
      :label="measure.abbreviation"
      :value="measure.abbreviation">
      <span v-if="measure.abbreviation !== measure.name && !!measure.abbreviation && !!measure.name" :title="measure.abbreviation + ': ' + measure.name">{{
          measure.abbreviation
        }}: {{ measure.name }}</span>
      <span v-else-if="measure.abbreviation == measure.name" :title="measure.name">{{ measure.name }}</span>
      <span v-else :title="measure.abbreviation + ': ' + measure.description">{{ measure.abbreviation }}: {{ measure.description }}</span>
    </el-option>
  </el-option-group>
</template>

<script>
export default {
  name: 'LessonMeasureOption',
  props: {
    // 当前 item 的 index
    index: {
      type: Number
    },
    // 要遍历的元素
    source: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  computed: {
    // 生成 centers 组活动按钮是否禁用
    domainTitle () {
      return function (domain) {
        let domainTitle = ''
        if (!domain) {
          return domainTitle
        }
        if (!!domain.abbreviation && !!domain.name) {
          domainTitle += domain.abbreviation + ': ' + domain.name
        } else if (!!domain.abbreviation) {
          domainTitle += domain.abbreviation
        } else if (!!domain.name) {
          domainTitle += domain.name
        }
        return domainTitle
      }
    }
  },
  data () {
    return {
      // 当前元素的 domain
      domain: null
    }
  },
  watch: {
    source: {
      handler (val) {
        this.domain = val
      },
      immediate: true
    }
  }
}
</script>

<style scoped lang="less">

</style>