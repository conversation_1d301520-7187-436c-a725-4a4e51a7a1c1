<template>
  <div class="graduate-portrait" v-show="weekData.length > 0" v-loading="loading" :class="{'mobile-portrait': isMobile}">
    <div class="m-t-sm add-margin-b-20 display-flex align-items w-full" :class="{'mobile-header': isMobile}">
      <div class="curriculum-unit-info-tag" :class="{'mobile-tag': isMobile}">
        {{ $t('loc.lessonPortraitTitle') }}
      </div>
    </div>
    <!-- 表格容器 -->
    <div class="tables-container" :class="{'mobile-tables': isMobile}">
      <!-- 每周一个表格 -->
      <table v-for="weekItem in weekData" :key="weekItem.week" class="week-table" :class="{'mobile-table': isMobile}">
        <thead>
        <tr>
          <th v-if="hasParentProfile" class="parent-profile-cell">{{ $t('loc.learnerProfileAttributes') }} (Week {{ weekItem.week }})</th>
          <th class="child-profile-cell">{{ hasParentProfile ? $t('loc.learnerProfileStandards') : `${$t('loc.learnerProfileAttributes')} (Week ${weekItem.week})`}}</th>
          <th class="lesson-cell">{{ $t('loc.lsPlan') }}</th>
          <th class="tips-cell">{{ $t('loc.teachingTips') }}</th>
        </tr>
        </thead>
        <tbody>
        <!-- 遍历所有父级校训数据 -->
        <template v-for="(childProfiles, parentProfileName) in weekItem.parentLessonProfileMap">
          <!-- 遍历每个父级校训下的子校训 -->
          <template v-for="(lessons, profileName, childIdx) in childProfiles">
            <!-- 为每个课程创建独立的行 -->
            <tr v-for="(lesson, index) in lessons"
                :key="lesson.itemId">
              <!-- 父级校训，只在第一个子校训的第一个 lesson 显示，rowspan 合并 -->
              <td v-if="hasParentProfile && childIdx === 0 && index === 0"
                  :rowspan="Object.values(childProfiles).reduce((sum, arr) => sum + arr.length, 0)"
                  class="parent-profile-cell">
                {{ parentProfileName }}
              </td>
              <!-- 子级校训，只在第一个 lesson 显示，rowspan 合并 -->
              <td v-if="index === 0" :rowspan="lessons.length" class="child-profile-cell">
                <el-tooltip popper-class="max-width-400" placement="top" :open-delay="500">
                  <div slot="content" v-html="getDisplayProfileInfo(lessons[0])"></div>
                  <span class="lg-pointer">{{ profileName }} ({{ lessons.length }})</span>
                </el-tooltip>
              </td>
              <!-- 课程信息 -->
              <td class="lesson-cell">
                <div class="lesson-item">
                  <span class="lesson-title">{{ lesson.lessonTitle }}</span>
                </div>
              </td>
              <!-- 教学提示 -->
              <td class="tips-cell">
                <div class="tip-item"
                     :class="{ 'clickable': lesson.lessonId }"
                     @click="lesson.lessonId && handleLessonClick(lesson)">
                  {{ $t('loc.detailInfo') }}
                </div>
              </td>
            </tr>
          </template>
        </template>
        </tbody>
      </table>
    </div>

    <!--模态窗口展示详情页-->
    <lesson-detail-dialog v-if="showDetail" :lesson-id="lesson.lessonId" :show.sync="showDetail" :new-tab-to="newTabTo">
      <!--详情页-->
      <lesson-detail ref="lessonDetail" :is-from-library="true" :lessonId="lesson.lessonId" :isDialog="true" :itemId="lesson.itemId" :scrollToGraduate="true">
        <template slot="header-left" slot-scope="{lesson}">
          <lesson-read-count :count="lesson.readCount"/>
          <lesson-like :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
          <lesson-favorite :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/>
        </template>
        <!-- 课程详情页右侧菜单 -->
        <!-- 在编辑或者审批时,可以编辑或移除周计划中的课程 -->
        <template slot="header-right" slot-scope="{ lesson, mappedFrameworkId }">
          <div class="display-flex align-items" style="gap: 10px">
            <lesson-template-select-modal
              v-if="showLessonTemplate(lesson.ages, lesson.activityType) && !unitAdapted"
              :lessonAges="lesson.ages"
              buttonSize="small"
              :inDialog="true"
              :showGuidePopover="true"
              :lessonId="lesson.id"
              v-model="lesson.templateType"
              @change="changeTemplateType"
            />
          </div>
          <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name"
                           :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                           :lesson="lesson"
                           :viewLesson="true"/>
        </template>
      </lesson-detail>
    </lesson-detail-dialog>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import tools from '@/utils/tools'
import LessonLike from '../../../lessonLibrary/components/LessonLike.vue'
import LessonDetailDialog from '../../../lessonLibrary/components/LessonDetailDialog.vue'
import LessonDetail from '../../../lessonLibrary/components/LessonDetail.vue'
import LessonFavorite from '../../../lessonLibrary/components/LessonFavorite.vue'
import LessonReadCount from '../../../lessonLibrary/components/LessonReadCount.vue'
import LessonDownload from '@/views/modules/lesson2/lessonLibrary/components/LessonDownload.vue'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'

export default {
  name: 'GraduatePortraitTable',
  components: {
    LessonReadCount,
    LessonFavorite,
    LessonDetail,
    LessonDetailDialog,
    LessonLike,
    LessonDownload,
    LessonTemplateSelectModal
  },
  data () {
    return {
      loading: false, // 是否正在加载数据
      weekData: [], // 单元周课时校训数据
      showDetail: false, // 是否显示课程详情页
      lesson: {}, // 当前选中的课程
      showOperateBtn: false, // 是否显示操作按钮
    }
  },

  created () {
  },
  mounted () {
    this.getUnitWeekLessonProfile()
  },

  props: {
    unitId: {
      type: String,
      default: ''
    },
    // 当前内容的语言码
    currentLangCode: {
      type: String,
      default: ''
    },
  },

  computed: {
    ...mapState({
      unitAdapted: state => state.lesson.unitAdapted, // 单元是否已经适配
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      isMobile: state => state.common.lgIsMobile // 添加移动端状态
    }),

    hasEmptyWeeks () {
      return !this.weekData.some(week => week.week === 3 || week.week === 4)
    },

    /**
     * 是否有父级校训（parentLessonProfileName）
     */
    hasParentProfile() {
      // 只要有一个 parentLessonProfileName 存在且不为空字符串就认为有父级校训
      return this.weekData.some(week =>
        week.parentLessonProfileMap &&
        Object.keys(week.parentLessonProfileMap).some(parent => parent !== '')
      )
    },

    /**
     * 新标签页的路由名称
     */
    newTabTo () {
      let tab = 'PublicLessonDetail'
      // 如果在 Unit Planner 详情页，特殊处理
      if (this.$route.name === 'unitDetail') {
        tab = 'unitPlanLessonDetail'
      }
      return tab
    },

  },

  watch: {
    // 监听语言码，如果语言码变化重新获取测评点数据
    currentLangCode () {
      this.getUnitWeekLessonProfile()
    }
  },

  methods: {
    handleLessonClick (lesson) {
      if (lesson.lessonId) {
        this.lesson = lesson
        this.showDetail = true
      }
    },

    // 修改课程模板类型
    changeTemplateType (type) {
      // 如果是在 unitDetail 页面,直接清空当前课程和课程模板，再跳转到生成课程页面
      if (this.isFromUnitDetail) {
        this.updateLessonTemplateTypeAndRegenerate(type)
      } else {
        if (type) {
          this.$store.dispatch('setTemplateType', type)
          this.editLesson(true)
        }
      }
    },

    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    },

    // 获取校训显示内容，与 LessonOverviewDetail 实现方式一致
    getDisplayProfileInfo (lesson) {
      if (!lesson || !lesson.lessonProfileName) {
        return ''
      }
      
      let content = lesson.lessonProfileName
      if (lesson.lessonProfileDescription) {
        content += '<br/>' + lesson.lessonProfileDescription
      }
      if (lesson.lessonProfileExpectation) {
        content += '<br/><br/>Expectations<br/>' + lesson.lessonProfileExpectation
      }
      return content
    },

    // 获取单元周课时校训内容
    getUnitWeekLessonProfile () {
      this.loading = true
      let params = {
        params: {
          unitId: this.unitId,
          ...(this.currentLangCode !== '' && { langCode: this.currentLangCode })
        }
      }
      this.$axios.get($api.urls().getUnitWeekLessonProfile, params).then(res => {
        this.weekData = []
        // 有数据
        if (res && res.hasProfileWeekList && res.hasProfileWeekList.length > 0) {
          res.hasProfileWeekList.forEach(week => {
            // 先对校训列表进行排序
            const sortedProfiles = week.lessonProfileList.sort((a, b) => 
              a.lessonProfileName.localeCompare(b.lessonProfileName)
            )
            
            // 检查是否有父级校训数据
            const hasParentProfileData = sortedProfiles.some(lesson => lesson.parentLessonProfileName)
            
            let parentLessonProfileMap = {}
            
            if (hasParentProfileData) {
              // 有父级校训时，按照 parentLessonProfileName 分组，再按照 lessonProfileName 分组
              parentLessonProfileMap = sortedProfiles
                .reduce((acc, lesson) => {
                  const parentKey = lesson.parentLessonProfileName
                  
                  if (parentKey) {
                    if (!acc[parentKey]) {
                      acc[parentKey] = {}
                    }
                    
                    const childKey = lesson.lessonProfileName
                    if (!acc[parentKey][childKey]) {
                      acc[parentKey][childKey] = []
                    }
                    
                    acc[parentKey][childKey].push(lesson)
                  }
                  
                  return acc
                }, {})
            } else {
              // 没有父级校训时，直接按照 lessonProfileName 分组，包装成二级结构以保持一致性
              const lessonProfileMap = sortedProfiles
                .reduce((acc, lesson) => {
                  const key = lesson.lessonProfileName
                  
                  if (!acc[key]) {
                    acc[key] = []
                  }
                  
                  acc[key].push(lesson)
                  
                  return acc
                }, {})
              
              // 包装成二级结构：{ '': { lessonProfileName: [lessons] } }
              parentLessonProfileMap = { '': lessonProfileMap }
            }
            
            this.weekData.push({
              week: week.week,
              parentLessonProfileMap: parentLessonProfileMap
            })
          })
        }
        if (this.weekData.length > 0) {
          this.$emit('showGraduatePortraitTab')
        }
        this.loading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
.curriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 16px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}

.graduate-portrait {
  background: #FFFFFF;
  border-radius: 8px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #10B3B7;
    padding: 6px 12px;
    display: inline-block;
    border-radius: 16px;
  }

  .tables-container {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;

    .week-table {
      flex: 0 0 calc(49%);
      border-collapse: collapse;
      border: 1px solid #E6A23C;
      overflow: hidden;
      margin-bottom: 24px;
      margin-right: -1px;
      table-layout: fixed;
      width: 100%;
      align-self: flex-start;

      th, td {
        border: 1px solid #E6A23C;
        margin-right: -1px;
        margin-bottom: -1px;
        padding: 8px 16px;
        text-align: left;
        font-size: 14px;
        line-height: 20px;
        height: 36px;
        box-sizing: border-box;
      }

      th {
        background: #FEF5E9;
        font-weight: 600;
        color: #111C1C;
        height: 50px;
        box-sizing: border-box;
        word-break: normal;
      }

      td {
        vertical-align: middle;
        color: #111C1C;
        background: #FFFFFF;

        &[rowspan] {
          background: #FFFFFF;
          vertical-align: middle;
        }
      }

      .week-cell {
        word-break: normal;
        width: 30%;
      }

      .parent-profile-cell {
        word-break: normal;
        width: 25%;
      }

      .child-profile-cell {
        word-break: normal;
        width: 25%;
      }

      .lesson-cell {
        width: 40%;

        .lesson-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          min-height: 30px;
          box-sizing: border-box;

          .lesson-title {
            flex: 1;
            padding-right: 16px;
            line-height: 20px;
            word-break: normal;
          }
        }
      }

      .tips-cell {
        width: 20%;

        .tip-item {
          height: 20px;
          display: flex;
          align-items: center;
          color: #10B3B7;

          &.clickable {
            cursor: pointer;
          }
          &:not(.clickable) {
            cursor: not-allowed;
            opacity: 0.4;
          }
        }
      }

      tbody tr:last-child {
        td {
          border-bottom: none;
        }
      }

      thead tr th:last-child,
      tbody tr td:last-child {
        border-right: none;
      }
    }

    .week-table:nth-child(even) {
      margin-left: 20px;
    }
  }

  .no-content-tip {
    color: #111C1C;
    font-size: 14px;
    font-weight: 600;
    margin: 20px;
  }
}

/* 移动端样式 */
@media screen and (max-width: 768px) {
  .mobile-portrait {
    padding: 12px;
    .mobile-header {
      margin-bottom: 16px;
      .mobile-tag {
        font-size: 14px;
        height: 28px;
        line-height: 28px;
        padding: 0 12px;
      }
    }
    .mobile-tables {
      flex-direction: column;
      .mobile-table {
        flex: 1 1 100%;
        margin-right: 0;
        margin-bottom: 16px;
        th {
          height: 40px;
          padding: 4px 8px;
          font-size: 13px;
        }
        td {
          padding: 4px 8px;
          font-size: 13px;
          .lesson-item {
            min-height: 24px;
            .lesson-title {
              padding-right: 8px;
              line-height: 18px;
            }
          }
        }
        .week-cell {
          width: 25%;
        }
        .parent-profile-cell {
          width: 20%;
        }
        .child-profile-cell {
          width: 20%;
        }
        .lesson-cell {
          width: 40%;
        }
        .tips-cell {
          width: 15%;
          .tip-item {
            height: 18px;
            font-size: 13px;
          }
        }
      }
      .week-table:nth-child(even) {
        margin-left: 0;
      }
    }
  }
}
</style>