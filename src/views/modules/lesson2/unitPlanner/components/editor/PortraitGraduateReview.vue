<template>
  <el-card shadow="never" class="w-full m-t-sm portrait-graduate-card" body-style="height: 100%;">
    <!-- 卡片头部 -->
    <div slot="header" class="portrait-header display-flex justify-content-center align-items">
      <div class="title-container">
        <div class="left-gradient title-gradient"></div>
        <div class="title">
          {{ $t('loc.lessonPortraitTitle') }} ({{ ageGroup }})
        </div>
        <div class="right-gradient title-gradient"></div>
      </div>
    </div>

    <!-- 卡片内容区 -->
    <!-- <el-collapse-transition> -->
      <div class="lg-padding-20">
        <el-skeleton
          :loading="!lesson.learnerProfiles"
          :rows="5"
          animated>
          <template>
            <!-- 提示信息 -->
            <div v-if="showTip" class="portrait-table-tip">
              <i class="lg-icon lg-icon-info"></i>
              <span style="margin-left: 6px;"> {{ getPortraitGraduateTip() }}</span>
            </div>

            <!-- 数据表格容器 -->
            <div class="table-container">
              <table class="learners-table portrait-table">
                <thead>
                  <tr>
                    <th style="width: 20%;"> {{ $t('loc.lessonPortraitAttr')}} ({{ lesson.learnerProfiles.length }})</th>
                    <th v-if="hasSubRubrics" style="width: 20%"> {{ $t('loc.learnerProfileStandards') }} ({{ lesson.learnerProfiles.map(profile => profile.subRubrics.map(subRubric => subRubric.rubricsName)).flat().length }})</th>
                    <th v-if="hasExpectation" style="width: 20%"> {{ $t('loc.learnerProfileExpectations')}}</th>
                    <th :style="hasSubRubrics && hasExpectation ? 'width: 40%' : hasSubRubrics || hasExpectation ? 'width: 60%' : 'width: 80%'"> {{ $t('loc.lessonPortraitAttrValue')}}</th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="(portrait, index) in lesson.learnerProfiles">
                    <template v-if="hasSubRubrics">
                      <tr v-for="(sub, subIdx) in (portrait.subRubrics || [])" :key="`sub-${index}-${subIdx}`">
                        <td v-if="hasSubRubrics && subIdx == 0" class="portrait-name" :rowspan="portrait.subRubrics.length">
                          <div class="name">{{ portrait.rubricsName }}</div>
                        </td>
                        <td class="portrait-name">
                          <div class="name">{{ sub.rubricsName }}</div>
                          <div class="description" v-if="sub.rubricsNameDesc">{{ sub.rubricsNameDesc }}</div>
                        </td>
                        <td v-if="hasExpectation" class="portrait-name">
                          <div class="description" v-if="sub.rubricsExpectation">{{ sub.rubricsExpectation }}</div>
                        </td>
                        <td v-if="isEdit">
                          <el-input v-model="sub.rubricsValue" type="textarea" :placeholder="$t('loc.lessonPortraitAttrValue')" :autosize="{ minRows: 3, maxRows: 50}" resize="none"/>
                        </td>
                        <td v-else class="portrait-evidence">
                          <div class="textarea-wrapper">{{ formatText(sub.rubricsValue) }}</div>
                        </td>
                      </tr>
                    </template>
                    <!-- 主 rubrics 行 -->
                    <tr v-else>
                      <td class="portrait-name">
                        <div class="name">{{ portrait.rubricsName }}</div>
                        <div class="description" v-if="portrait.rubricsNameDesc">{{ portrait.rubricsNameDesc }}</div>
                      </td>
                      <td v-if="hasExpectation" class="portrait-name">
                          <div class="description" v-if="portrait.rubricsExpectation">{{ portrait.rubricsExpectation }}</div>
                        </td>
                      <td v-if="isEdit">
                        <el-input v-model="portrait.rubricsValue" type="textarea" :placeholder="$t('loc.lessonPortraitAttrValue')" :autosize="{ minRows: 3, maxRows: 50}" resize="none"/>
                      </td>
                      <td v-else class="portrait-evidence">
                        <div class="textarea-wrapper">{{ formatText(portrait.rubricsValue) }}</div>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </template>
        </el-skeleton>
      </div>
    <!-- </el-collapse-transition> -->
  </el-card>
</template>

<script>
import tools from '@/utils/tools'
import { mapState } from 'vuex'
export default {
  name: 'PortraitGraduateReview',

  props: {
    lesson: {
      type: Object,
      default: () => {
      }
    },
    lessonItem: {
      type: Object,
      default: () => {
      }
    },
    // 年龄组
    ageGroup: {
      type: String,
      default: ''
    },
    // 是否是编辑状态
    isEdit: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      lessonTemplate: {}, // 课程模板
    }
  },

  created () {
    this.lessonTemplate = this.lesson.steps[0].lessonTemplate
    this.$store.dispatch('getAgencyLearnerProfile')
  },

  computed: {
    ...mapState({
      agencyLearnerProfile: state => state.lesson.agencyLearnerProfile
    }),
    // 是否显示提示
    showTip () {
      return this.lessonTemplate && this.lessonTemplate.templateName && this.filteredProfiles.length > 0
    },

    // 过滤掉在 item.rubrics 中的值
    filteredProfiles () {
      if (!this.lessonItem || Object.keys(this.lessonItem).length === 0) {
        return []
      }
      const result = []
      // item 包含的校训
      let allItemRubrics = (this.hasSubRubrics ? this.lessonItem.rubrics && this.lessonItem.rubrics.map(rubric => rubric.subStandards).flat() : this.lessonItem.rubrics) || []
      // 课程包含的校训
      let allLessonRubrics = (this.hasSubRubrics ? this.lesson.learnerProfiles && this.lesson.learnerProfiles.map(profile => profile.subRubrics).flat() : this.lesson.learnerProfiles) || []
      // 找出 item 中不存在的校训
      allLessonRubrics.forEach(rubric => {
        if (!allItemRubrics.some(item => item.title === rubric.rubricsName)) {
          result.push(rubric.rubricsName)
        }
      })
      return result
    },

    // 内容完整的校训的个数
    learnerProfilesLength() {
      return this.lesson.learnerProfiles.filter(portrait => portrait && portrait.rubricsName && this.formatText(portrait.rubricsValue)).length
    },

    // 是否有子校训
    hasSubRubrics() {
      return this.lesson.learnerProfiles.some(profile => profile.subRubrics && profile.subRubrics.length > 0)
    },

    // 是否有期望
    hasExpectation() {
      if (this.hasSubRubrics) {
        return this.lesson.learnerProfiles.some(profile => profile.subRubrics.some(sub => sub.rubricsExpectation))
      } else {
        return this.lesson.learnerProfiles.some(profile => profile.rubricsExpectation)
      }
    }
  },

  methods: {
    // 格式化文本
    formatText (text) {
      if (!text) return ''
      return text.trim() // 移除文本开头和结尾的空白字符
    },

    // 获取 Portrait of a Graduate 的提示内容
    getPortraitGraduateTip () {
      let portraitGraduateTip = this.$t('loc.lessonPortraitTip', { templateName: this.lessonTemplate.templateName })
      // 使用 filteredProfiles 将每个属性添加到 portraitGraduateTip 中
      portraitGraduateTip += this.filteredProfiles
        .join(', ') + '.'
      // 返回 portraitGraduateTip
      return portraitGraduateTip
    }
  }
}
</script>

<style lang="less" scoped>
.portrait-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 16px 24px;
  align-self: center;
  background: #EAF3D8;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border-bottom: 1px solid var(--dcdfe-6, #DCDFE6);

  .title-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .title {
    color: #49AC18;
    font-feature-settings: 'liga' off, 'clig' off;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px;
  }
}

.left-gradient {
  background: linear-gradient(270deg, #49AC18 -1.16%, rgba(16, 179, 183, 0.00) 98.84%);
}

.right-gradient {
  background: linear-gradient(90deg, #49AC18 -1.16%, rgba(16, 179, 183, 0) 98.84%);
}

.title-gradient {
  width: 45px;
  height: 2px;
  border-radius: 1px;
}

.portrait-graduate-card {
  margin-top: 16px;
  border-radius: 20px;
  background: #FFF;
  border: 1px solid #96C13D;
  overflow: hidden;

  .portrait-table-tip {
    color: var(--111-c-1-c, #111C1C);
    font-feature-settings: 'liga' off, 'clig' off;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 16px;
  }

  .table-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid #96C13D;
      border-radius: 8px;
      pointer-events: none;
    }
  }

  .portrait-table {
    width: 100%;
    border-collapse: collapse;
    border: none;
    overflow: hidden;

    th, td {
      vertical-align: middle;  /* 让文本在单元格内垂直居中 */
      border: 1px solid #96C13D;
      padding: 12px;
      text-align: left;
    }

    th {
      background: #F2F6E8;
      font-weight: 600;
      font-size: 16px;
      border-bottom: 1px solid #96C13D;
    }

    .portrait-name {
      .name {
        font-weight: 600;
        font-size: 16px;
        line-height: 1.5;
      }

      .description {
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .textarea-wrapper {
      vertical-align: middle;  /* 让文本在单元格内垂直居中 */
      white-space:pre-line;  // 添加这行，保留换行符
      word-break: break-word;
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
      padding: 0;
    }

    td.portrait-evidence {
      padding: 0;
    }

    td.portrait-evidence .textarea-wrapper {
      padding: 12px;
    }
  }
}

/deep/ .el-textarea.el-input--large .el-textarea__inner {
  height: auto;
}

/deep/ .el-textarea__inner {
  border-color: transparent !important;
}

/deep/ .el-textarea__inner:hover {
  border: 1px dashed #c0c4cc !important;
}

/deep/ .el-textarea__inner:focus {
  border: 1px dashed #10b3b7 !important;
}

/deep/ .el-card__header {
  padding: 0 !important;
  border-bottom: none;
}

/deep/ .el-card__body {
  padding: 0;
}

.textarea-wrapper {
  word-break: break-word;
  font-size: 16px;
  line-height: 1.5;
}
</style>
