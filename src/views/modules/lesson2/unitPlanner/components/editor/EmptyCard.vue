<template>
    <el-card class="w-full lg-border-radius-8" body-style="height: 100%;" :shadow="showCard ? 'always' : 'never'"
             :style="{ 'height': searchTips ? 'calc(100vh - 170px)' : '',
             'min-height': isLessonPlanNewUser ? '661px' : '',
              'padding': isCurriculumPlugin ? '20px 66px 130px 66px' : ''}"
             :class="{'unit-empty': !searchTips || isCurriculumPlugin, 'display-flex': isCurriculumPlugin,
             'justify-content': isCurriculumPlugin,
             'no-border': !showCard, 'h-full': isFullScreen, 'margin-top': showAddUnitBtn ? '15px': ''}">
        <div class="w-full h-full display-flex align-items flex-direction-col"
             :class="{ 'justify-content': !isFullScreen }">
            <div class="text-center m-b-md m-t-md" v-if="!isCurriculumPlugin">
                <img src="@/assets/img/curriculumGenie/unit_empty.jpg" style="width: 400px" alt="">
                <div class="mt-2 font-size-22 font-bold" v-if="!aiTips">
                    <!-- 白板引导标题 -->
                    <slot name="title">
                        <template>{{ $t('loc.unitPlannerGuideRevolutionize') }}</template>
                    </slot>
                </div>
            </div>
            <div class="text-center m-b-md m-t-md" v-else>
              <iframe v-if="!isLessonPlanNewUser" src="https://www.youtube.com/embed/bfqpXMUvd_I" width="640" height="360" style="border-radius: 8px; border: none;" allowfullscreen/>
              <img v-if="isLessonPlanNewUser" src="~@/assets/img/lesson2/lessonPlan/new_user_unit_step1_empty.png" :style="isMobile ? { width: '85vw' } : { width: '640px', height: '250px' }" allowfullscreen/>
              <!-- <el-image :src="emptyCard" fit="contain"/> -->
                <div class="m-t-md font-size-22 font-bold" :style="isMobile ? {maxWidth: '85vw'} : {}" v-if="!aiTips">
                    <!-- 白板引导标题 -->
                    <slot name="title">
                        <template v-if="!isLessonPlanNewUser">Crafting Future-Ready Curriculum with AI</template>
                        <template v-if="isLessonPlanNewUser">Build a Brilliant Unit Plan in Just 8 Mins</template>
                    </slot>
                </div>
            </div>
            <template v-if="!isCurriculumPlugin">
                <span v-if="searchTips">{{ searchTips }}</span>
                <template v-if="aiTips">
                    <div v-html="aiTips"></div>
                </template>
                <template v-else>
                    <ul class="font-size-16 unit-empty-tip">
                        <!-- 引导内容显示 -->
                        <slot name="description">
                            <template>
                                <li>{{ $t('loc.unitPlannerGuideSimplifies') }}</li>
                                <li v-for="(item, index) in guidancePrompts" :key="index">{{ item }}</li>
                            </template>
                        </slot>
                    </ul>
                    <!-- 新建 Unit -->
                    <el-button v-if="showAddUnitBtn" type="primary" icon="el-icon-plus" class="ai-btn add-margin-t-20"
                               @click="newUnit">New Unit
                    </el-button>
                </template>
            </template>
            <template v-else>
              <template v-if="aiTips">
                <div v-html="aiTips"></div>
              </template>
              <div v-if="!isLessonPlanNewUser && !aiTips" class="font-size-16 unit-empty-tip text-center">Empower your unit planning with smart tools that save time and enhance impact, allowing
                you to focus on what truly matters: preparing your students for the future.</div>
              <div v-if="isLessonPlanNewUser" class="unit-empty-tip text-center" style="color: #676879;font-size: 14px;font-weight: 400;width: 85vw;">
                <div v-if="!aiTips">Empower your unit planning with magic that saves time and enhances impact, allowing you to focus on what truly matters: preparing your students for the future.</div>
                <el-popover
                  placement="top-start"
                  width="400"
                  trigger="manual"
                  height="110"
                  :visible-arrow="false"
                  :offset="-420"
                  v-model="showGuide"
                  ref="startButtonPopoverRef"
                  popper-class="generate-detail-lesson-guide">
                  <div class="guide-content">
                    <img :src="require('@/assets/img/unit/unit_first_start_now.png')" class="guide-img">
                    <i class="el-icon-close close-icon" @click="showGuide = false"></i>
                  </div>
                  <div slot="reference">
                    <el-button @click="exploring" type="primary" class="start-button"
                               :loading="generateUnitOverviewLoading"
                               :disabled="magicIsDisabledGenerate || generateUnitOverviewLoading">
                      <i class="lg-icon lg-icon-generate font-bold"
                         style="margin-right: 4px; width: 20px; height: 20px;"/>
                      <span class="start-button-text">Start Now</span>
                    </el-button>
                  </div>
                </el-popover>
              </div>
            </template>
        </div>
    </el-card>
</template>

<script>
import { isTeacher } from '@/utils/common'
import { mapState } from 'vuex'
// import emptyCard from '@/assets/img/curriculumPlugin/empty-card.svg'
import emptyCard from '@/assets/img/curriculumGenie/unit_empty.jpg'
import tools from '../../../../../../utils/tools'
import { LESSON_PLAN_NEW_USER_UTC } from '../../../../../../utils/const'

export default {
    props: {
        type: {
            type: String,
            default: () => 'UNIT_OVERVIEW'
        },
        aiTips: {
            type: String,
            default: ''
        },
        // 是否显示阴影
        showCard: {
            type: Boolean,
            default: () => true
        },
        showAddUnitBtn: {
            type: Boolean,
            default: () => false
        },
        isFullScreen: {
            type: Boolean,
            default: () => true
        },
        searchTips: {
          type: String,
          default: ''
        },
        // 是否显示引导
        shouldShowGuide: {
          type: Boolean,
          default: false
        },
        // 是否为新用户
        isLessonPlanNewUser: {
          type: Boolean,
          default: false
        },
        // 单元是否生成中
        generateUnitOverviewLoading: {
          type: Boolean,
          default: false
        },
        // 单元禁止生成
        magicIsDisabledGenerate: {
          type: Boolean,
          default: false
        }
    },
    data () {
      return {
        // 管理员角色页面给出的引导提示
        guidancePromptsForAdmin: [
          this.$t('loc.unitPlannerGuideSeamlesslyUnit'),
          this.$t('loc.unitPlannerEmpty1'),
          this.$t('loc.unitPlannerEmpty2')
        ],
        // 老师角色页面给出的引导提示
        guidancePromptsForTeacher: [
          this.$t('loc.unitPlannerPlanItemAdaptUDLAndCLRForMyClassGuideContent'),
          this.$t('loc.unitPlannerEmpty3')
        ],
        showGuide: false,
        isMobile: false // 是否是移动端
      }
    },
    computed: {
      guidancePrompts () {
        // 如果是老师角色，则返回老师角色的引导提示词，否则返回管理员角色的引导提示词
        if (this.isTeacher()) {
          return this.guidancePromptsForTeacher
        } else {
          return this.guidancePromptsForAdmin
        }
      },
      ...mapState({
        currentUser: state => state.user.currentUser,
        open: state => state.common.open,
        isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin
      }),
      // 单元创建权限
      allowCreateUnit () {
        return !this.isTeacher() || this.isTeacher() && this.open && this.open.createUnitPlannerOpen
      },
      emptyCard () {
        return emptyCard
      }
    },
    watch: {
      // 监听父组件传递的引导显示状态
      shouldShowGuide(newVal) {
        setTimeout(() => {
          this.showGuide = newVal
        }, 1000)
      }
    },
    mounted() {
      this.isMobile = window.innerWidth <= 768
    },
    methods: {
      // 是否是老师
      isTeacher,
      // 跳转到创建页面
      newUnit () {
        // 点击创建 Unit 单元的埋点
        this.$analytics.sendEvent('web_unit_click_create')
        // 如果是老师角色，且没有开通创建权限，则不允许创建 Unit, 并给出提示
         if (!this.allowCreateUnit) {
           this.$message.warning({
             showClose: true,
             message: this.$t('loc.unitPlannerYouCurrently')
           })
           return
         }
        // 清空已有单元信息
        this.$store.commit('curriculum/RESET_UNIT')
        this.$router.push({
          name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-creator-cg' : 'unitCreator'
        })
      },
      /**
       * 开始生成
       */
      exploring () {
        // 关闭引导
        this.showGuide = false
        // 触发开始事件
        this.$emit('start-now')
      }
    }
}
</script>

<style lang="less" scoped>
@media screen and (max-width:1599px) {
    .unit-empty-tip {
        max-width: 880px;
        padding: 0 50px;
    }
}
@media screen and (min-width:1600px) {
    .unit-empty-tip {
        max-width: 880px;
        padding: 0 100px;
    }
}
.unit-empty {
    // height: calc(100vh - 100px);
    min-height: 680px;
}
.start-button {
  border-radius: 4px;
  background: var(--10-b-3-b-7, #10B3B7);
  border: 0 !important;
  min-height: 50px;
  width: 100% !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
}

.start-button-text {
  color: var(--ffffff, #FFF);
  text-align: center;
  font-size: 17px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px; /* 144.444% */
}

ul, li {
    list-style: disc;
}
::v-deep {
    .no-border {
        border: none !important;
    }
}

/* 动画效果 */
@keyframes vertical-shake {
  0% { transform: translateY(0); }
  25% { transform: translateY(-5px); }
  50% { transform: translateY(0); }
  75% { transform: translateY(5px); }
  100% { transform: translateY(0); }
}
</style>

<style lang="less">
/* 引导弹框全局样式 */
.generate-detail-lesson-guide {
  /* 移动端样式 */
  @media screen and (max-width: 768px) {
    width:300px !important;
  }
  padding: 0px !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  animation: vertical-shake 1.2s cubic-bezier(0.36, 0, 0.64, 1) infinite;
  -webkit-animation: vertical-shake 1.2s cubic-bezier(0.36, 0, 0.64, 1) infinite;

  .guide-content {
    position: relative;
    -webkit-box-shadow: none;
    min-height: unset !important;

    .guide-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .close-icon {
    position: absolute;
    top: 10px;
    right: 10px !important;
    font-size: 16px;
    cursor: pointer;
  }
}
</style>
