<template>
  <div>
    <div class="m-t-sm add-margin-b-20 display-flex align-items w-full">
      <div class="curriculum-unit-info-tag">
        {{ $t('loc.assessmentDomainMeasureList') }}
      </div>
      <!-- 核心测评点开关 -->
      <div style="margin-left: auto" class="display-flex align-items" v-if="coreMeasureOpen">
        <el-switch
          v-model="onlyShowCoreMeasure"
          @change="showCoreMeasureChange"
          size="mini">
        </el-switch>
        <span class="core-measure">
                {{ $t('loc.collectShowCoreMeasureOnly') }}
              </span>
      </div>
    </div>

    <div ref="collectMeasureElRow" :class="{'unit-measure-max-height': collectMeasureHidden}">
      <div v-loading=allLoading>
        <el-row :gutter="0" type="flex" style="flex-wrap: wrap;" class="weeks">
          <el-col :span="24" style="border: 1px solid #FF7F41;min-height: 100px">
            <!--单元信息头部-->
            <div class="week-header">
              <span class="week-header-title">{{ $t('loc.collectUnit') }}: {{ getUnitTitle() }}</span>
              <span class="week-header-number">
              {{ $t('loc.subjectCount', { subjectCount: getDomainCount(collectMeasureData.unitAllMeasure) }) }}  &nbsp&nbsp
              {{ $t('loc.measureCount', { measureCount: getMeasureCount(collectMeasureData.unitAllMeasure) }) }}</span>
            </div>
            <!--单元信息测评点数据-->
            <div class="week-body">
              <div class="domain-measure" v-for="domain in collectMeasureData.unitAllMeasure.measures">
                <div class="domain-measure-name">{{ getDomainAbbAndName(domain) }}</div>
                <div v-for="measures in domain.children">
                  <el-tooltip class="item" effect="dark" :content=measures.description placement="top" :open-delay="400"
                              :enterable="false">
                    <div class="measure-item">
                      {{ measures.abbreviation }}
                      <span style="color: #F56C6C" v-if="measures.core">*</span>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </el-col>

          <!--各周测评点数据-->
          <el-col :span="12" class="week" v-for="weekData in collectMeasureData.weekListMeasure">
            <div class="week-header">
              <span class="week-header-title" style="width: 40%">{{ getWeekName(weekData.week) }}</span>
              <span class="week-header-number">
              {{ $t('loc.subjectCount', { subjectCount: getDomainCount(weekData) }) }}  &nbsp&nbsp
              {{ $t('loc.measureCount', { measureCount: getMeasureCount(weekData) }) }}</span>
            </div>

            <div class="week-body">
              <div class="domain-measure" v-for="domain in weekData.measures">
                <div v-for="measures in domain.children">
                  <el-tooltip class="item" effect="dark" :content=measures.description placement="top" :open-delay="400"
                              :enterable="false">
                    <div class="measure-item">
                      {{ measures.abbreviation }}
                      <span style="color: red" v-if="measures.core">*</span>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 底部隐藏、显示按钮 -->
    <div class="text-center" v-if="showHiddenButton">
      <el-button v-if="collectMeasureHidden" type="text" @click="collectMeasureHidden = false">
        {{ $t('loc.collectMeasureShowAll') }}
      </el-button>
      <el-button v-else type="text" @click="collectMeasureHidden = true">{{
          $t('loc.collectMeasureHide')
        }}
      </el-button>
    </div>
  </div>
</template>
<script>

import { mapState } from 'vuex'

export default {
  name: 'OverViewTab',
  props: {
    // 单元 Id
    unitId: {
      type: String
    },
    // 单元标题
    unitTitle: {
      type: String
    },
    // 更新测评点的 loading
    saveMeasureloading: {
      type: Boolean,
      default: false
    },
    // 当前内容的语言码
    currentLangCode: {
      type: String,
      default: ''
    },
  },
  components: {},
  data () {
    return {
      collectMeasureHidden: false,  // 表格是否折叠
      collectMeasureData: {
        unitAllMeasure: {
          measures: null
        }, // 单元总测评点数据
        weekListMeasure: [
          {
            measures: null
          }
        ] // 各州测评点数据
      }, // 测评点汇总数据
      onlyShowCoreMeasure: false, // 仅显示核心测评点开关
      collectMeasureDataCache: null, // 测评点汇总数据
      getUnitAllMeasureLoading: true, // 查询汇总数据 Loading
      coreMeasureOpen: false, // 是否开启核心测评点
      showHiddenButton: false, //折叠展开按钮是否展示
    }
  },
  created () {
  },
  mounted () {
    if (!this.saveMeasureloading) {
      this.getUnitAllMeasure()
    }
  },
  computed: {
    ...mapState({
      unit: state => state.curriculum.unit, // 单元信息
      weeklyPlans: state => state.curriculum.unit.weeklyPlans, // 单元每周数据
    }),
    allLoading () {
      return this.saveMeasureloading || this.getUnitAllMeasureLoading
    }
  },
  watch: {
    // 等待测评点保存成功后查询汇总测评点数据
    saveMeasureloading (val) {
      if (!val) {
        this.getUnitAllMeasure()
      }
    },
    // 监听语言码，如果语言码变化重新获取测评点数据
    currentLangCode () {
      this.getUnitAllMeasure()
    }

  },
  methods: {
    // 获取单元测评点数据
    getUnitAllMeasure () {
      this.getUnitAllMeasureLoading = true
      // 从缓存或组件传递获取 unitId
      let unitId = this.unit.id || this.unitId
      let params = {
        params: {
          unitId: unitId,
          ...(this.currentLangCode !== '' && { langCode: this.currentLangCode })
        }
      }
      this.$axios.get($api.urls().getUnitAllMeasure, params).then(res => {
        this.getUnitAllMeasureLoading = false
        this.collectMeasureData = res
        this.collectMeasureDataCache = JSON.parse(JSON.stringify(this.collectMeasureData))

        // 判断是否有核心测评点，是否显示核心测评点开关
        let filterCoreMeasure = JSON.parse(JSON.stringify(this.collectMeasureDataCache))
        this.handlerCoreMeasure(filterCoreMeasure)
        if (filterCoreMeasure && filterCoreMeasure.unitAllMeasure && filterCoreMeasure.unitAllMeasure.measures && filterCoreMeasure.unitAllMeasure.measures.length > 0) {
          this.coreMeasureOpen = true
        }

        // 如果高度大于 400px 进行折叠
        this.$nextTick(() => {
          let height = this.$refs.collectMeasureElRow && this.$refs.collectMeasureElRow.offsetHeight
          if (height > 400) {
            this.collectMeasureHidden = true
            this.showHiddenButton = true
          }
        })
      })
    },

    // 获取领域名和缩写
    getDomainAbbAndName (domain) {
      if (!domain) return ''
      const { abbreviation, name } = domain
      if (abbreviation && name) {
        return `${abbreviation}: ${name}`
      }
      return abbreviation || name || ''
    },

    // 获取领域个数
    getDomainCount (data) {
      if (data && data.measures) {
        return data.measures.filter(measure => measure.children.length > 0).length
      } else {
        return 0
      }
    },

    // 获取测评点个数
    getMeasureCount (data) {
      if (data && data.measures) {
        return data.measures.flatMap(domain => domain.children).length
      } else {
        return 0
      }
    },

    // 过滤出所有关键测评点数据
    handlerCoreMeasure (data) {
      data.weekListMeasure = data.weekListMeasure.map(item => ({
        ...item,
        measures: item.measures
          .map(measure => ({
            ...measure,
            children: (measure.children || []).filter(child => child.core === true) // 过滤出 core 为 true 的 children
          }))
      })).filter(item => (item.measures || []).length > 0)

      data.unitAllMeasure.measures = data.unitAllMeasure.measures.filter(measure => measure.children.length > 0) // 只保留有 children 的 measure
        .map(measure => ({
          ...measure,
          children: (measure.children || []).filter(child => child.core === true)
        }))
        .filter(measure => (measure.children || []).length > 0)
    },

    // 获取课程名
    getUnitTitle () {
      return this.unitTitle || this.unit.title
    },

    // 核心测评点开关状态变化
    showCoreMeasureChange (val) {
      if (val) {
        // 过滤出关键测评点数据
        this.handlerCoreMeasure(this.collectMeasureData)
      } else {
        this.collectMeasureData = JSON.parse(JSON.stringify(this.collectMeasureDataCache))
      }
    },
    // 获取周数
    getWeekName (number) {
      return number ? this.$t(`loc.week${number}`) : this.$t('loc.week1')
    }
  }
}
</script>

<style lang="less" scoped>
.week:nth-child(even) {
  border-left: 1px solid #FF7F41;
  border-right: 1px solid #FF7F41;
  border-bottom: 1px solid #FF7F41;
}

.week:nth-child(odd) {
  border-right: 1px solid #FF7F41;
  border-bottom: 1px solid #FF7F41;
}

.core-measure {
  margin-left: 8px;
  color: #111C1C;
}

.week-header {
  padding: 16px;
  border-bottom: 1px solid #FF7F41;
  background: #FCF6EC;;
}

.week-header-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  width: 70%;
  color: #111C1C;
  word-break: normal;
  display: inline-block;
}

.week-header-number {
  float: right;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #676879;
  display: inline-block;
}

.week-body {
  padding: 8px 16px 16px 8px;
  min-height: 56px;
}

.domain-measure {
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .domain-measure {
    width: 100%;
  }
}

.measure-item {
  line-height: 22px;
  background: #F5F6F8;
  margin-top: 8px;
  margin-left: 8px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  padding: 0 8px;
  font-weight: 400;
  font-size: 12px;
  color: #111C1C;
  cursor: default;
}

.domain-measure-name {
  width: 100%;
  font-weight: 600;
  font-size: 14px;
  margin: 8px 0 0 8px;
  color: #111C1C;
}

.curriculum-unit-info-tag {
  line-height: 34px;
  height: 34px;
  padding: 0px 16px;
  font-size: 16px;
  font-weight: 500 !important;
  color: #ffffff;
  width: fit-content;
  background: #10b3b7;
  border-radius: 16px;
}

.unit-measure-max-height {
  max-height: 400px !important;
  overflow: hidden;
}
</style>
