<template>
  <transition name="slide">
    <div class="unit-completed-shared" v-show="visible">
      <div class="dialog-content">
        <!-- 关闭按钮 -->
        <div class="close-btn" @click="closeAndAnalyse">
          <i class="lg-icon lg-icon-close font-size-24 color-676879"></i>
        </div>

        <div class="genie-image">
          <img src="@/assets/img/unit/unit-completed-shared.png">
        </div>

        <!-- 内容区域 -->
        <div class="content">
          <span class="title">🎉 Congrats! You've completed a new unit.</span>

          <p class="description">
            Share and Earn 2 More Units
          </p>

          <!-- 分享按钮 -->
          <div class="share-square" style="background:#D6F0F0;">
            <!-- Facebook -->
            <div class="share-btn lg-pointer" @click="shareToFacebook">
              <img src="@/assets/img/unit/fackbook-icon.png"/>
              <span>Facebook</span>
            </div>

            <!-- Instagram -->
            <div class="share-btn lg-pointer" @click="shareToTwitter">
              <img src="@/assets/img/unit/x-icon.png"/>
              <span>X (Twitter)</span>
            </div>
          </div>

        </div>
      </div>
    </div>
  </transition>

</template>


<script>
import {mapState} from "vuex";

export default {
  name: 'UnitCompletedShared',

  data() {
    return {
      visible: false, // 是否显示
    }
  },

  computed: {
    ...mapState({
      magicAllowUnits: state => state.magicCurriculum.magicAllowUnits, // 允许创建的 unit 数量
    }),
  },

  mounted() {
  },

  methods: {
    // 打开按钮
    openAndAnalyse() {
      this.$analytics.sendEvent('cg_unit_create3_share_card_exposure')
      this.visible = true
    },

    // 关闭按钮
    close() {
      this.visible = false
    },

    // 关闭按钮
    closeAndAnalyse() {
      this.$analytics.sendEvent('cg_unit_create3_share_card_close')
      this.visible = false
    },

    // 分享到 Facebook
    shareToFacebook() {
      this.$analytics.sendEvent('cg_unit_create3_share_card_click_fb')
      const data = {
        event: 'GO_TO_SHARE_PAGE',
        type: 'UNIT_COMPLETED_INVITE_SHARE_TO_FACEBOOK',
        shared: false
      }
      this.$bus.$emit('message', data)
      // 判断是否达到分享上限
      this.reachingLimit()
    },

    // 分享到 Twitter
    shareToTwitter() {
      this.$analytics.sendEvent('cg_unit_create3_share_card_click_x')
      const data = {
        event: 'GO_TO_SHARE_PAGE',
        type: 'UNIT_COMPLETED_INVITE_SHARE_TO_TWITTER',
        shared: false
      }
      this.$bus.$emit('message', data)
      // 判断是否达到分享上限
      this.reachingLimit()
    },

    // 判断分型是否达到上限，达到则关闭弹框
    async reachingLimit() {
      // 获取创建 unit 奖励状态
      await this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
      // 判断是否只能分享最后一次
      if (this.magicAllowUnits >= 16) {
        this.visible = false
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.unit-completed-shared {
  position: fixed;
  left: 20px;
  bottom: 75px;
  z-index: 999;

  .dialog-content {
    width: 445px;
    height: 200px;
    background: radial-gradient(86.66% 54.5% at 73.44% 37.8%, rgba(255, 215, 78, 0.06) 0%, rgba(255, 255, 255, 0.00) 100%), linear-gradient(169deg, rgba(255, 255, 255, 0.00) 37.36%, rgba(16, 179, 183, 0.12) 92.74%), linear-gradient(158deg, rgba(170, 137, 242, 0.20) 2.58%, rgba(255, 255, 255, 0.00) 59.46%), #FFF;
    border: 3px solid transparent;
    background-clip: padding-box;
    position: relative;
    border-radius: 12px;
    padding: 20px 20px 20px 5px;
    display: flex;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.20);

    &::before {
      content: '';
      position: absolute;
      top: -3px;
      right: -3px;
      bottom: -3px;
      left: -3px;
      z-index: -1;
      border-radius: 12px;
      background: linear-gradient(to right, #908FF5, #1FAECF);
    }
  }

  .close-btn {
    position: absolute;
    top: 0;
    right: 4px;
    cursor: pointer;
    color: #676879;

    &:hover {
      color: #333;
    }
  }

  .genie-image {
    img {
      height: 165px;
      margin-top: -10px;
    }
  }

  .content {
    width: 310px;
    margin-left: -30px;
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  .title {
    color: #000;
    font-size: 14px;
    font-weight: bold;
  }

  .description {
    font-size: 20px;
    font-weight: bold;
    line-height: 22px;
    text-align: center;
    padding: 8px 0;
    background: linear-gradient(91deg, #9969FF 6.35%, #0089FF 72.75%, #10B3B7 104.79%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.slide-enter-active {
  transition: transform 0.3s ease;
}

.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(-100%);
}

.share-square {
  display: flex;
  width: 235px;
  height: 110px;
  justify-content: center;
  align-items: center;
  gap: 30px;
  border-radius: 12px;
  border: 2px solid #FFF;
  background: #D6F0F0;

  img {
    width: 45px;
    height: auto;
  }

  span {
    padding-top: 4px;
    font-size: 14px;
    font-weight: 600;
    color: #111C1C;
    line-height: 22px;
  }

}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (max-width: 768px) {
  .genie-image {
    img {
      display: none;
    }
  }
  
  .dialog-content {
    width: 350px !important;
  }
  
  .unit-completed-shared {
    .content {
      margin-left: 0 !important;
    }
  }
}
</style>
