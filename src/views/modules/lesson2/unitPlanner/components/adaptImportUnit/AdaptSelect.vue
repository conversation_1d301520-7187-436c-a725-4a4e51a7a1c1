<template>
  <div class="adapt-select">
    <div class="adapt-select-body">
      <div class="box lg-scrollbar">
        <div class="box-header">
          <div class="box-header-title">
            <span>{{ $t('loc.adaptSelect') }}</span>
          </div>
          <div class="box-header-tip">
            <span>{{ $t('loc.adaptSelectTip') }}</span>
          </div>
        </div>
        <div class="box-content">
          <!-- 轻度改编 -->
          <div class="box-content-item" :class="{ 'box-content-item-active': adaptTypeSelected === 'lightAdapt' }"
               @click="handleAdaptTypeSelect('lightAdapt')">
            <div class="box-content-item-title">
              🌟 {{ $t('loc.adaptSelectLightAdapt') }}
            </div>
            <div class="box-content-item-tip">
              {{ $t('loc.adaptSelectLightAdaptTip') }}
            </div>
          </div>
          <!-- 深度改编 -->
          <div class="box-content-item" :class="{ 'box-content-item-active': adaptTypeSelected === 'deepRedesign' }"
               @click="handleAdaptTypeSelect('deepRedesign')">
            <div class="box-content-item-title">
              🔮 {{ $t('loc.adaptSelectDeepRedesign') }}
            </div>
            <div class="box-content-item-tip">
              {{ $t('loc.adaptSelectDeepRedesignTip') }}
            </div>
          </div>
        </div>
        
        <transition :name="!hasSelected ? 'fade-expand' : 'fade-out'">
          <!-- 如果选择轻度改编 -->
          <div v-show="adaptTypeSelected === 'lightAdapt'">
            <div class="position-relative add-padding-l-16">
              <div class="light-adapt-group-title">
                {{ $t('loc.adaptSelectLightAdaptGroup') }}
              </div>
              <!-- 轻度改编全选 -->
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"
                           class="light-adapt-checkbox-all">
                {{ $t('loc.selectAll') }}
              </el-checkbox>
              <!-- 轻度改编单选 -->
              <el-checkbox-group v-model="lightAdaptSelect" @change="handleCheckedChange">
                <div v-for="option in allowSelectModules" class="light-adapt-checkbox-item">
                  <el-checkbox :label="option.module" :key="option.moduleName" class="light-adapt-checkbox">
                    <div class="light-adapt-checkbox-content">{{ option.moduleName }}
                    </div>
                  </el-checkbox>
                  <el-tooltip class="item" effect="dark" :content="option.tip" placement="right" v-if="option.tip"
                              :open-delay="500"
                              popper-class="light-adapt-checkbox-content-tip">
                    <i class="lg-icon lg-icon-question add-margin-l-4"></i>
                  </el-tooltip>
                  
                  <el-tooltip effect="dark" placement="right" v-if="option.module === 'teacherGuideFlag'"
                              :open-delay="500">
                    <div slot="content" class="light-adapt-checkbox-teacher-content-tip">
                      {{ $t('loc.adaptSelectTeacherGuideTip') }}
                      <ul>
                        <li>{{ $t('loc.adaptSelectTeacherGuideTip1') }}</li>
                        <li>{{ $t('loc.adaptSelectTeacherGuideTip2') }}</li>
                        <li>{{ $t('loc.adaptSelectTeacherGuideTip3') }}</li>
                        <li>{{ $t('loc.adaptSelectTeacherGuideTip4') }}</li>
                      </ul>
                    </div>
                    <i class="lg-icon lg-icon-question add-margin-l-4"></i>
                  </el-tooltip>
                  
                  <el-tooltip effect="dark" placement="right" v-if="option.module === 'portraitOfGraduateFlag'"
                              :open-delay="500">
                    <div slot="content" class="light-adapt-checkbox-content-tip">
                      <div>{{ $t('loc.learnerProfileUseTip') }}</div>
                      <!-- 若有选择的校训内容，显示 My settings -->
                      <div v-if="rubricsOptions && rubricsOptions.length > 0"
                           class="lg-pointer font-color-primary lg-underline"
                           @click="addAgency()">
                        My settings
                      </div>
                      <!-- 若当前年龄符合校训年龄且没有选择的校训内容，显示 Setup now -->
                      <div v-else-if="!rubricsOptions || rubricsOptions.length === 0"
                           class="lg-pointer font-color-primary lg-underline"
                           @click="addAgency()">
                        {{ $t('loc.learnerProfileSetupNow') }}
                      </div>
                    </div>
                    <i class="lg-icon lg-icon-question add-margin-l-4"></i>
                  </el-tooltip>
                  
                  <div class="light-adapt-checkbox-tip"
                       :style="{'margin-bottom': option.module === 'portraitOfGraduateFlag' && selectedRubrics.length === 0 ? '65px' : '0'}">
                    {{ option.description }}
                  </div>
                  <!-- 轻度改编选择校训 -->
                  <div class="pog-select"
                       v-show="selectedRubrics.length > 0 && option.module === 'portraitOfGraduateFlag'"
                       @click="openRubricsDialog">
                    <div class="pog-select-body">
                      <div class="pog-select-item" v-for="rubric in displayRubrics" @click.stop>
                        {{ rubric.title }}
                        <i class="lg-icon lg-icon-close" @click.stop="removeRubrics(rubric.title)"></i>
                      </div>
                    </div>
                    <i class="el-icon-circle-plus-outline" @click="openRubricsDialog"></i>
                  </div>
                </div>
              </el-checkbox-group>
              
              <!-- 校训按钮 -->
              <div v-if="showRubricsSelect" class="rubrics-select">
                <div class="pog-no-select"
                     v-show="(!selectedRubrics || selectedRubrics.length === 0) && rubricsOptions && rubricsOptions.length > 0">
                  <i class="el-icon-circle-plus-outline"></i>
                  <RubricsRow ref="rubricsRowRef" :rubricsOptions="rubricsOptions" :showSelectedRubrics="true"
                              :disabled="false" :maxSelectCount="rubricsMax" :isLessonAssistant="false"
                              @updateSelectedRubrics="updateSelectedRubrics" @showPogTransformGuide="()=>{}"/>
                </div>
                <!-- 轻度改编无校训数据 -->
                <el-button type="primary" class="pog-setup-btn" size="small"
                           v-if="!rubricsOptions || rubricsOptions.length === 0"
                           @click="addAgency()">
                  {{ $t('loc.learnerProfileSetupNow') }}
                </el-button>
              </div>
            
            </div>
          </div>
        </transition>
        
        <transition :name="!hasSelected ? 'fade-expand' : 'fade-out'">
          <!-- 如果选择深度改编 -->
          <div v-show="adaptTypeSelected === 'deepRedesign'" class="unit-info-form-box">
            <unit-info-form ref="unitInfoFormRef"
                            :inAdaptSelect="true"
                            :showDesAndVoiceBtn="true"
                            :showHeadBtn="false"
                            :initHistory="false"
                            @updateRubricsOptions="updateRubricsOptions"></unit-info-form>
          </div>
        </transition>
        <div class="box-footer">
          <!-- 开始改编 -->
          <el-button type="primary" @click="handleNext" :loading="loading" class="w-full flex-center-center">
            <span class="flex-center-center">
              <span>{{ $t('loc.adaptSelectStartNow') }}</span>
              <i class="el-icon-arrow-right font-size-20 add-margin-l-4"></i>
            </span>
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 当前导入不能轻量改编弹框 -->
    <el-dialog title="提示" :visible.sync="unLightAdaptDialogVisible" width="600px" top="30vh"
               custom-class="un-light-adapt-dialog">
      <div slot="title" class="dialog-title">
        <span>{{ $t('loc.confirmation') }}</span>
      </div>
      <span v-html="$t('loc.adaptSelectUnLightAdapt')" class="font-size-16"></span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="unLightAdaptDialogVisible = false">
          <i class="lg-icon lg-icon-magic"></i>
          {{ $t('loc.adaptSelectUnLightAdaptBtn') }}</el-button>
      </span>
    </el-dialog>
    
    <!-- 生成 Loading 遮盖页 -->
    <UnitStepOneLoadingDialog :dialogVisible.sync="generateLoading"/>
    
    <!-- 学科测评点校验弹框 -->
    <CheckedDomainConformOfUnitDialog @deleteMeasure="deleteMeasure" @next="handleDeepRedesign"
                                      :checkedDomainConformOfUnitVisible.sync="checkedDomainConformOfUnitVisible"
                                      :checkedDomainConformOfUnitResult="checkedDomainConformOfUnitResult"/>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import RubricsRow from '@/views/modules/lesson2/unitPlanner/components/learnerProfile/RubricsRow.vue'
import UnitStepOneLoadingDialog from '../editor/UnitStepOneLoadingDialog.vue'
import UnitInfoForm from '../editor/UnitInfoForm.vue'
import CheckedDomainConformOfUnitDialog from './CheckedDomainConformOfUnitDialog.vue'
import tools from '@/utils/tools'
import Lessons2 from '@/api/lessons2'
import { equalsIgnoreCase } from '@/utils/common'
import { lightAdaptOptions } from '@/utils/constants'

export default {
  name: 'adaptSelect',
  components: {
    RubricsRow,
    UnitStepOneLoadingDialog,
    UnitInfoForm,
    CheckedDomainConformOfUnitDialog
  },
  data() {
    return {
      adaptTypeSelected: '', // 改编类型
      hasSelected: false,
      checkAll: false, // 全选
      isIndeterminate: false, // 半选
      lightAdaptSelect: [], // 轻度改编选择
      rubricsOptions: [], // 校训选项
      selectedRubrics: [], // 选择的校训
      loading: false, // 加载中
      generateLoading: false, // 生成加载中
      lightAdaptOptions: [], // 轻度改变显示的模块选项
      checkedDomainConformOfUnitByAI: false, // 当前单元学科测评点数据是否校验过
      checkedDomainConformOfUnitResult: {
        reserveMeasure: [], // 推荐保留的测评点
        removeMeasure: [], // 推荐删除的测评点
        rationale: '', // 推荐理由
      },
      checkedDomainConformOfUnitVisible: false, // 测评点校验弹框
      unLightAdaptDialogVisible: false // 当前导入不能轻量改编弹框状态，导入的只有单元时
    }
  },
  
  computed: {
    ...mapState({
      agencyLearnerProfile: state => state.lesson.agencyLearnerProfile,
      currentUserId: state => state.user.currentUser.user_id,
      baseInfo: state => state.curriculum.unit.baseInfo,
      analyzeUnitContent: state => state.curriculum.analyzeUnitContent,
      unit: state => state.curriculum.unit,
      plans: state => state.curriculum.unit.weeklyPlans,
    }),
    
    // 是否只有单元
    isOnlyUnit() {
      if (!this.analyzeUnitContent) {
        return false
      }
      return equalsIgnoreCase(this.analyzeUnitContent.enhanceMode, 'DEEP')
    },
    
    // 是否只有课程
    isOnlyLesson() {
      if (!this.analyzeUnitContent) {
        return false
      }
      return equalsIgnoreCase(this.analyzeUnitContent.enhanceMode, 'LIGHT')
    },
    
    // 校训可选择的最大数
    rubricsMax() {
      if (this.analyzeUnitContent && this.analyzeUnitContent.totalWeek && this.analyzeUnitContent.totalWeek !== 0) {
        return Math.min(this.analyzeUnitContent.totalWeek * 3, 9);
      }
      return 3
    },
    
    // 是否展示校训模块
    showRubricsSelect() {
      const item = this.allowSelectModules.find(item => equalsIgnoreCase(item.module, 'portraitOfGraduateFlag'))
      return !!item
    },
    
    // 当前年龄允许选择的模块
    allowSelectModules() {
      if (!this.analyzeUnitContent) {
        return []
      }
      // 获取当前年龄
      const ageValue = tools.getAgeValue(this.analyzeUnitContent.grade)
      // 过滤出所有当前年龄允许选择的模块
      return this.lightAdaptOptions.filter(item => ageValue >= tools.getAgeValue(item.minGrade) && ageValue <= tools.getAgeValue(item.maxGrade))
    },
    
    // 将树形结构的 selectedRubrics 转换为展开的平铺显示列表
    displayRubrics() {
      const result = []
      if (!this.selectedRubrics || !Array.isArray(this.selectedRubrics)) {
        return result
      }
      
      this.selectedRubrics.forEach(portrait => {
        // 添加防护性检查，确保 portrait 存在且有 title 属性
        if (!portrait || !portrait.title) {
          return // 跳过无效的数据项
        }
        
        if (portrait.subStandards && Array.isArray(portrait.subStandards) && portrait.subStandards.length > 0) {
          // 如果有子标准，展开显示子标准
          portrait.subStandards.forEach(subStandard => {
            // 确保子标准也有 title 属性
            if (subStandard && subStandard.title) {
              result.push({
                key: `${portrait.title}_${subStandard.title}`,
                title: subStandard.title,
                parentTitle: portrait.title,
                subStandard: subStandard,
                portrait: portrait,
                isSubStandard: true
              })
            }
          })
        } else {
          // 如果没有子标准，显示主标准
          result.push({
            key: portrait.title,
            title: portrait.title,
            portrait: portrait,
            isSubStandard: false
          })
        }
      })
      
      return result
    },
  },
  
  watch: {
    // 重新选择测评点后需要重新校验测评点
    'baseInfo.useDomain': {
      handler() {
        this.checkedDomainConformOfUnitByAI = false
      }
    },
    
    // 监听选择的模块
    lightAdaptSelect: {
      deep: true,
      handler(newVal, oldVal) {
        // 修改状态
        this.handleCheckedChange()
        // 埋点事件
        this.sendLightAdaptSelectEvent(newVal, oldVal)
      }
    }
  },
  
  async created() {
    // 如果导入的单元内容没有分析结果，则获取分析结果
    if (!this.analyzeUnitContent || this.analyzeUnitContent.analysisResultId) {
      await this.getAnalyzeContentById(this.$route.params.id)
    }
    // 初始化轻度改变选项
    this.initLightAdaptOptions()
    // 初始化回显数据
    this.initAdaptSelectData()
    // 获取校训数据
    await this.$store.dispatch('getAgencyLearnerProfile')
    // 进入增强模式页事件
    this.$analytics.sendEvent('cg_adapt_mode_page_entered')
  },
  
  mounted() {
  
  },
  
  methods: {
    // 初始化回显数据
    initAdaptSelectData() {
      if (this.isOnlyUnit) {
        this.adaptTypeSelected = 'deepRedesign'
        this.setAdaptHasSelected()
      }
      if (this.isOnlyLesson) {
        this.adaptTypeSelected = 'lightAdapt'
        this.setAdaptHasSelected()
      }
      // 初始化单元表单数据
      this.initUnitFormData()
    },
    
    // 初始化单元表单数据
    initUnitFormData() {
      if (!this.analyzeUnitContent) {
        return
      }
      const unitContent = this.analyzeUnitContent.unitContent || {} // 单元内容
      const frameworkData = this.analyzeUnitContent.frameworkData || [] // 框架数据
      this.$set(this.unit.baseInfo, 'title', unitContent.unitName) // 单元标题
      this.$set(this.unit.baseInfo, 'description', unitContent.unitOverview) // 单元描述
      this.$set(this.unit.baseInfo, 'weekCount', this.analyzeUnitContent.totalWeek || '1') // 周数
      this.$set(this.unit.baseInfo, 'grade', this.analyzeUnitContent.grade) // 年级
      this.$set(this.unit.baseInfo, 'frameworkId', this.analyzeUnitContent.frameworkId) // 框架 ID
      this.$set(this.unit.baseInfo, 'frameworkName', frameworkData.length > 3 ? frameworkData[3] : '') // 框架名称
      this.$set(this.unit.baseInfo, 'domainIds', this.analyzeUnitContent.domainIds) // 学科 ID
      this.$set(this.unit.baseInfo, 'useDomain', !this.analyzeUnitContent.measureIds || this.analyzeUnitContent.measureIds.length === 0) // 是否使用只选择学科
      this.$set(this.unit.baseInfo, 'useLocation', true) // 是否使用地理位置
      this.$set(this.unit.baseInfo, 'country', 'United States')
      this.$set(this.unit.baseInfo, 'state', 'California')
      
      this.$nextTick(() => {
        this.$refs.unitInfoFormRef.selectedGrade = this.analyzeUnitContent.grade // 年级
        this.$refs.unitInfoFormRef.selectedDomainIds = this.analyzeUnitContent.domainIds // 学科 ID
        this.$refs.unitInfoFormRef.selectedFrameworkData = frameworkData.slice(0, 3) // 框架数据
        if (this.unit.baseInfo.frameworkId) {
          // 根据框架 ID 获取学科信息
          this.$refs.unitInfoFormRef.getDomainInfo(this.$refs.unitInfoFormRef.fetchMeasuresFromBackend(this.unit.baseInfo.frameworkId), this.unit.baseInfo.frameworkId)
          this.$refs.unitInfoFormRef.initLocationInfo()
          this.unit.baseInfo.measureIds = this.analyzeUnitContent.measureIds // 测评点 ID
        }
      })
    },
    
    // 切换改编类型
    handleAdaptTypeSelect(type) {
      // 如果导入的单元内容只有单元，则弹出提示，不能轻量改编
      if (this.isOnlyUnit && equalsIgnoreCase(type, 'lightAdapt')) {
        this.unLightAdaptDialogVisible = true
        return
      }
      // 改编类型选择事件
      if (equalsIgnoreCase(type, 'lightAdapt')) {
        this.$analytics.sendEvent('cg_adapt_mode_light_selected')
      } else {
        this.$analytics.sendEvent('cg_adapt_mode_deep_selected')
      }
      this.setAdaptHasSelected()
      this.adaptTypeSelected = type
    },
    
    // 设置已经选择过改编类型
    setAdaptHasSelected() {
      setTimeout(() => {
        this.hasSelected = true
      }, 600)
    },
    
    async handleNext() {
      if (!await this.verifyAdapt()) {
        return
      }
      // 轻度改编
      if (equalsIgnoreCase(this.adaptTypeSelected, 'lightAdapt')) {
        this.handleLightAdapt()
        this.$analytics.sendEvent('cg_adapt_mode_start_clicked', { type: 'light' })
        return
      }
      
      // 深度改编
      if (equalsIgnoreCase(this.adaptTypeSelected, 'deepRedesign')) {
        await this.handleDeepRedesign()
        this.$analytics.sendEvent('cg_adapt_mode_start_clicked', { type: 'deep' })
      }
    },
    
    // 校验方法
    async verifyAdapt() {
      // 轻度改编
      if (equalsIgnoreCase(this.adaptTypeSelected, 'lightAdapt')) {
        // 轻度改编至少选择一个
        if (this.lightAdaptSelect.length === 0) {
          this.$message.warning(this.$t('loc.adaptSelectLightAdaptLeastOne'))
          return false
        }
        // 轻度改编如果选择校训模块了，必须选择校训
        if (this.lightAdaptSelect.includes('portraitOfGraduateFlag') && this.selectedRubrics.length === 0) {
          this.$message.warning(this.$t('loc.adaptSelectPogLeastOne'))
          return false
        }
        return true
      }
      // 深度改编
      if (equalsIgnoreCase(this.adaptTypeSelected, 'deepRedesign')) {
        let result = false
        // 校验表单信息
        await this.$refs.unitInfoFormRef.validate().then(() => {
          result = true
        }).catch(() => {
          // 未通过校验跳转到第一个错误的表单项
          setTimeout(() => {
            let formItemsWithError = this.$el.querySelectorAll('.is-error')
            // 过滤出可见的 dom
            formItemsWithError = tools.getVisibleDom(formItemsWithError)
            let firstItem = formItemsWithError[0]
            if (firstItem) {
              firstItem.dispatchEvent(new CustomEvent('validate-failed', {}))
              firstItem.scrollIntoView({ block: 'center' })
            }
          }, 0)
        })
        return result
      }
      // 未选择改编类型
      this.$message.warning(this.$t('loc.adaptSelectStartNowTip'))
      return false
    },
    
    // 轻度改编
    handleLightAdapt() {
      // 封装选择的参数
      const lightAdaptSwitch = {}
      // 遍历轻度改编选项，将选择的模块封装为对象
      this.lightAdaptOptions.map(item => {
        lightAdaptSwitch[item.module] = this.lightAdaptSelect.includes(item.module) ? true : false
      })
      
      const params = {
        analysisId: this.analyzeUnitContent.analysisResultId,
        newRubrics: lightAdaptSwitch.portraitOfGraduateFlag ? this.selectedRubrics : [],
        lightAdaptSwitch: lightAdaptSwitch
      }
      // 开始 Loading
      this.generateLoading = true
      this.$axios.post($api.urls().createAdaptedUnit, params).then(async res => {
        // 如果 success 是 false，則跳過
        if (!(!!res.success)) {
          this.generateLoading = false
          // 弹出达到限制的提示
          this.$message.warning(this.$t('loc.adaptSelectCreatLimit'))
          return
        }
        if (res.plans && res.plans.length > 0 && res.plans[0].items && res.plans[0].items.length > 0) {
          this.unit.adaptedType = 'LIGHT'
          this.unit.id = res.unitId
          this.unit.adaptedModuleSwitch = lightAdaptSwitch
          this.setLightOptionsCache()
          // 生成单元和周信息
          this.createUnitFoundationAndWeeklyTheme(res)
          // 创建课程批量生成任务
          await this.createGenerateLessonTasks(res)
        }
      })
    },
    
    // 设置浏览器缓存
    setLightOptionsCache() {
      // 如果上次选择所有，下次进来还默认选择所有
      const cacheData = this.checkAll ? 'all' : JSON.stringify(this.lightAdaptSelect)
      localStorage.setItem(this.currentUserId + '_Light_Adapt', cacheData)
    },
    
    // 创建课程批量生成任务
    async createGenerateLessonTasks(unitPlan) {
      if (!unitPlan || !unitPlan.plans || unitPlan.plans.length === 0) {
        return
      }
      // 遍历所有 plans
      let generateItems = []
      unitPlan.plans.forEach(plan => {
        // 遍历每个 plan 下的 items
        if (plan.items && plan.items.length > 0) {
          // 提取每个 item 的 itemId
          const planItem = plan.items.map(item => {
            return {
              itemId: item.itemId,
              planId: plan.planId
            }
          })
          generateItems = generateItems.concat(planItem)
        }
      })
      // 如果 itemIds 为空，则返回
      if (generateItems.length === 0) {
        return
      }
      // 去除第一个 id 并移除, 页面跳转到当前 item
      const firstItem = generateItems.shift()
      // 如果还有剩余的 itemId，则批量生成
      if (generateItems.length > 0) {
        const params = {
          unitId: this.unit.id,
          generateItems: generateItems,
          batch: true
        }
        // 添加 return 和 await，确保等待异步操作完成
        const res = await Lessons2.createGenerateLessonTasks(params)
        this.$store.dispatch('unit/setBatchId', res.id)
      }
      let params = {
        unitId: this.unit.id,
        itemId: firstItem.itemId
      }
      this.generateLoading = false
      // 跳转到课程详情页面
      this.$router.push({
        name: 'lesson-detail-cg-adapt',
        params: params
      })
    },
    
    // 生成单元和周信息
    createUnitFoundationAndWeeklyTheme(unitPlan) {
      if (!unitPlan || !unitPlan.plans || unitPlan.plans.length === 0) {
        return
      }
      // 获取所有周计划 id
      const weeklyPlanIds = unitPlan.plans.map(plan => plan.planId)
      const params = {
        unitId: this.unit.id,
        weeklyPlanIds: weeklyPlanIds
      }
      this.$store.commit('curriculum/SET_UNIT_FOUNDATION_GENERATE_LOADING', true)
      this.$axios.post($api.urls().createUnitFoundationAndWeeklyTheme, params).then(res => {
        this.$store.commit('curriculum/SET_UNIT_FOUNDATION_GENERATE_LOADING', false)
        // 发送事件，更新周计划数据
        this.$bus.$emit('getAdaptedUnitWeeklyPlans')
      })
    },
    
    // 深度改编
    async handleDeepRedesign() {
      // 检查单元内容和学科/测评点是否匹配
      if (!this.checkedDomainConformOfUnitByAI) {
        this.loading = true
        // 校验测评点是否合适
        const checkResult = await this.checkDomainConformOfUnitByAI()
        if (!checkResult) {
          this.loading = false
          return
        }
      }
      // 校验通过创建单元
      this.createUnit().then(() => {
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    
    // 创建单元
    createUnit() {
      return new Promise((resolve, reject) => {
        let locationInfo = this.getLocationInfo()
        let params = {
          number: 1,
          adaptedType: 'DEEP',
          analysisId: this.analyzeUnitContent.analysisResultId,
          ...this.baseInfo,
          ...locationInfo
        }
        // 创建单元
        this.$axios.post($api.urls().createPluginUnit, params).then(async(res) => {
          // 如果 success 是 false，則跳過
          if (!(!!res.success)) {
            // 弹出达到限制的提示
            this.$message.warning(this.$t('loc.adaptSelectCreatLimit'))
            reject()
            return
          }
          // 单元 ID
          let unitId = res.ids[0]
          this.unit.adaptedType = 'DEEP'
          this.unit.weekLessonCountMap = res.weekLessonCountMap
          // 更新单元 ID
          this.$store.commit('curriculum/SET_UNIT_ID', unitId)
          // 路由跳转到编辑页面
          this.$router.push({
            name: 'unit-overview-cg-adapt',
            params: {
              unitId: unitId,
              generateUnit: true
            }
          })
          resolve()
        }).catch(error => {
          this.$message.error(error.response.data.error_message)
          reject()
        })
      })
    },
    
    // 如果是自动选择测评点，检测单元内容和学科/测评点是否匹配，并推荐测评点。如果是手动选择测评点，则检测测评点是否合适，并推荐删除某些测评点
    checkDomainConformOfUnitByAI() {
      return new Promise((resolve, reject) => {
        const params = {
          title: this.baseInfo.title,
          description: this.baseInfo.description,
          week: this.baseInfo.weekCount,
          grade: this.baseInfo.grade,
          frameworkId: this.baseInfo.frameworkId,
          domainIds: this.baseInfo.domainIds,
          measureIds: this.baseInfo.measureIds,
          autoDomain: this.baseInfo.useDomain
        }
        // 创建单元
        this.$axios.post($api.urls().checkDomainConformOfUnitByAI, params).then((checkResult) => {
          // 记录当前单元已经被校验过
          this.checkedDomainConformOfUnitByAI = !this.baseInfo.useDomain
          // 如果是自动选择测评点且校验通过，则设置单元推荐的测评点列表
          if (this.baseInfo.useDomain && checkResult.pass) {
            this.baseInfo.measureIds = checkResult.measureIds
            resolve(true)
            return
          }
          // 如果是自动选择测评点且校验不通过，则弹出提示
          if (this.baseInfo.useDomain && !checkResult.pass) {
            // 根据不同学科个数弹出不同提示
            if (this.baseInfo.domainIds && this.baseInfo.domainIds.length > 1) {
              this.$message.error(this.$t('loc.unitPlannerDomainsNotGoodFit'))
            } else {
              this.$message.error(this.$t('loc.unitPlannerDomainNotGoodFit'))
            }
            resolve(false)
            return
          }
          // 如果是手动选择测评点且校验通过，则继续创建单元
          if (!this.baseInfo.useDomain && checkResult.pass) {
            resolve(true)
            return
          }
          // 如果是手动选择测评点且校验不通过，且没有推荐删除的测评点或推荐删除的测评点包括全部选择的测评点，说明全部测评点都不合适
          if (!this.baseInfo.useDomain && !checkResult.pass && (!checkResult.removeMeasureIds || checkResult.removeMeasureIds.length === 0 || checkResult.removeMeasureIds.length === this.unitInfo.measures.length)) {
            this.$message.error(this.$t('loc.unitPlannerMeasuresNotGoodFit'))
            resolve(false)
            return
          }
          // 如果是手动选择测评点且校验不通过，但有推荐删除的测评点，说明部分测评点不合适
          if (!this.baseInfo.useDomain && !checkResult.pass && checkResult.removeMeasureIds.length > 0) {
            // 过滤出推荐删除的测评点
            this.checkedDomainConformOfUnitResult.removeMeasure = this.baseInfo.measures.filter(measure => checkResult.removeMeasureIds.includes(measure.id))
            // 过滤出推荐保留的测评点
            this.checkedDomainConformOfUnitResult.reserveMeasure = this.baseInfo.measures.filter(measure => !checkResult.removeMeasureIds.includes(measure.id))
            // 推荐理由
            this.checkedDomainConformOfUnitResult.rationale = checkResult.rationale
            // 打开测评点推荐弹框
            this.checkedDomainConformOfUnitVisible = true
            resolve(false)
          }
          resolve(false)
        }).catch(error => {
          resolve(!this.baseInfo.useDomain)
        })
      })
    },
    
    // 获取地理位置信息
    getLocationInfo() {
      if (!this.baseInfo) {
        return {
          country: null,
          state: null,
          city: null
        }
      }
      // 国家信息
      let country = this.baseInfo.country
      // 州信息
      let state = this.baseInfo.state
      // 城市
      let city = this.baseInfo.city
      // 是否显示地理位置
      let useLocation = this.baseInfo.useLocation
      if (!useLocation) {
        city = null
      }
      return {
        country: country,
        state: state,
        city: city
      }
    },
    
    // 全选
    handleCheckAllChange() {
      this.lightAdaptSelect = this.lightAdaptSelect.length === this.allowSelectModules.length ? [] : this.allowSelectModules.map(item => item.module)
      this.isIndeterminate = false
      this.$analytics.sendEvent('cg_adapt_addon_select_all_clicked')
    },
    
    // 单个选中
    handleCheckedChange() {
      // 获取选中数量
      const checkedCount = this.lightAdaptSelect.length
      // 全选状态
      this.checkAll = checkedCount === this.allowSelectModules.length
      // 半选状态
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.allowSelectModules.length
    },
    
    // 更新选择的校训
    updateSelectedRubrics(selectedPortraits) {
      this.selectedRubrics = selectedPortraits
    },
    
    // 打开选择校训的弹框
    openRubricsDialog() {
      this.$nextTick(() => {
        const rubricsRowRef = this.$refs.rubricsRowRef
        if (rubricsRowRef && Array.isArray(this.$refs.rubricsRowRef)) {
          rubricsRowRef[0].openDialog()
        } else if (rubricsRowRef) {
          rubricsRowRef.openDialog()
        }
      })
    },
    
    // 删除表单测评点
    deleteMeasure(measure) {
      this.$nextTick(() => {
        const unitInfoFormRef = this.$refs.unitInfoFormRef
        if (unitInfoFormRef) {
          unitInfoFormRef.deleteMeasure(measure)
        }
      })
    },
    
    removeRubrics(title) {
      this.$nextTick(() => {
        const rubricsRowRef = this.$refs.rubricsRowRef
        if (rubricsRowRef && Array.isArray(this.$refs.rubricsRowRef)) {
          rubricsRowRef[0].removeRubrics(title)
        } else if (rubricsRowRef) {
          rubricsRowRef.removeRubrics(title)
        }
      })
    },
    
    // 获取导入的单元内容
    async getAnalyzeContentById(id) {
      const response = await Lessons2.getAnalyzeContentById(id)
      if (!response.success) {
        return
      }
      this.$store.commit('curriculum/SET_ANALYZE_UNIT_CONTENT', response)
    },
    
    // 更新校训选项
    updateRubricsOptions(rubricsOptions) {
      if (rubricsOptions && rubricsOptions.ageGroupRubrics) {
        this.rubricsOptions = rubricsOptions.ageGroupRubrics
      }
    },
    
    // 打开校训编辑弹框
    addAgency() {
      this.$nextTick(() => {
        if (this.$refs.unitInfoFormRef) {
          return this.$refs.unitInfoFormRef.addAgency()
        }
      })
      // 校训设置埋点
      if (!this.rubricsOptions || this.rubricsOptions.length === 0) {
        this.$analytics.sendEvent('cg_adapt_set_pog_clicked')
      }
    },
    
    // 初始化轻度改变选项
    async initLightAdaptOptions() {
      this.lightAdaptOptions = JSON.parse(JSON.stringify(lightAdaptOptions));
      // 获取轻量改编选项浏览器缓存
      const lightAdaptSelect = localStorage.getItem(this.currentUserId + '_Light_Adapt')
      let lightAdaptSelectArr = []
      // 如果是 all ，则全选
      if (lightAdaptSelect && equalsIgnoreCase(lightAdaptSelect, 'all')) {
        lightAdaptSelectArr = this.lightAdaptOptions.map(item => item.module)
      } else if (lightAdaptSelect) {
        // 如果有缓存数据则解析
        lightAdaptSelectArr = JSON.parse(lightAdaptSelect)
      } else {
        // 如果不存在浏览器缓存，则从后台获取
        const response = await this.$axios.get($api.urls().getLightAdaptOptions)
        if (response) {
          lightAdaptSelectArr = Object.keys(response).filter(key => response[key])
        } else {
          // 如果不存在缓存，默认全选
          lightAdaptSelectArr = this.lightAdaptOptions.map(item => item.module)
        }
      }
      // 获取当前选中的模块
      this.lightAdaptSelect = this.allowSelectModules.map(item => item.module).filter(item => lightAdaptSelectArr.includes(item))
    },
    
    // 轻量改编选择模块事件
    sendLightAdaptSelectEvent(newVal, oldVal) {
      if (newVal && oldVal && newVal.length > oldVal.length) {
        // 获取新增的元素
        const newModules = newVal.filter(item => !oldVal.includes(item))
        if (newModules.length === 0) {
          return
        }
        // 模块和事件映射
        const moduleEventMap = new Map([
          ['portraitOfGraduateFlag', 'cg_adapt_addon_select_pog'],
          ['teacherGuideFlag', 'cg_adapt_addon_select_teacher_guide'],
          ['standardsAssessmentFlag', 'cg_adapt_addon_select_quiz'],
          ['eduProtocolsTemplatesFlag', 'cg_adapt_addon_select_edu_template'],
          ['lectureSlidesFlag', 'cg_adapt_addon_select_teacher_slides'],
          ['resourceUpgradeFlag', 'cg_adapt_addon_select_resources']
        ])
        
        // 发送事件
        for (const newModule of newModules) {
          if (moduleEventMap.has(newModule)) {
            this.$analytics.sendEvent(moduleEventMap.get(newModule))
          }
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>

@media (max-width: 768px) {
  .box-content {
    flex-direction: column;
  }
  
  .rubrics-select {
    top: 155px !important;
  }
}

.adapt-select {
  display: flex;
  justify-content: center;
  height: auto;
}

.adapt-select-body {
  max-width: 860px;
  padding: 20px 0 20px 20px;
  height: unset;
  border-radius: 8px;
  background: #FFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);
}

.box {
  padding-right: 20px;
  max-height: calc(96vh - 155px);
  position: relative;
}

.box-header-title {
  color: #111C1C;
  font-size: 20px;
  font-weight: 700;
  line-height: 26px;
}

.box-header-tip {
  color: #676879;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-top: 8px;
}

.box-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.box-content-item {
  width: 420px;
  padding: 16px;
  margin-top: 20px;
  border-radius: 8px;
  background: #F5F6F8;
  cursor: pointer;
  border: 2px solid transparent;
}

.box-content-item:hover {
  background: #F0FEFF;
}

.box-content-item-active {
  border: 2px solid #10B3B7;
  background: #F0FEFF;
}

.box-content-item-title {
  color: #111C1C;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

.box-content-item-tip {
  color: #676879;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-top: 12px;
}

.box-footer {
  padding: 20px 3px 3px;
  position: sticky;
  bottom: 0;
  z-index: 99;
  background: #FFF;
}

.light-adapt-group-title {
  color: #676879;
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}

.light-adapt-checkbox-all {
  color: #111C1C;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  margin: 16px 0 0;
}

.light-adapt-checkbox {
  margin: 0;
}

.light-adapt-checkbox-item {
  margin-top: 16px;
}

.light-adapt-checkbox-content {
  color: #111C1C;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  
  i {
    font-weight: 400;
    color: #676879;
  }
}

.light-adapt-checkbox-tip {
  color: #676879;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-left: 24px;
}

.pog-no-select {
  display: flex;
  width: 166px;
  height: 36px;
  padding: 9px 12px;
  margin: 8px 0 0 24px;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: 2px solid #10B3B7;
  background: #FFF;
  cursor: pointer;

  /deep/ .select-area {
    padding: 0 !important;
    min-height: 25px !important;
  }

  /deep/ .placeholder-text {
    color: #10B3B7 !important;
    font-size: 14px;
    font-weight: 600;
  }

  /deep/ .rubrics-row {
    width: unset;
  }

  i {
    color: #10B3B7;
    font-size: 20px;
    margin-right: 4px;
  }
}

.pog-select {
  width: fit-content;
  display: flex;
  padding: 8px;
  margin: 8px 0 0 24px;
  align-items: center;
  align-content: center;
  gap: 4px;
  align-self: stretch;
  flex-wrap: wrap;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  background: #FFF;
  cursor: pointer;
  
  i {
    font-size: 20px;
    color: #10B3B7;
    cursor: pointer;
  }
}

.pog-select-body {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 96%;
}

.pog-select-item {
  display: flex;
  height: 29px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 2px;
  border-radius: 4px;
  background: #E8F9FA;
  color: #10B3B7;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  white-space: nowrap;
  cursor: context-menu;
  
  i {
    font-size: 12px;
    color: #10B3B7;
  }
}

.pog-setup-btn {
  margin: 10px 0 0 24px;
}

.unit-info-form-box {
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--dcdfe-6, #DCDFE6);
  background: #FFF;
  
  /deep/ .lesson-assistant-domain-select,
  /deep/ .lesson-assistant-subject-select {
    .el-tag {
      height: 29px;
      padding: 10px;
      gap: 2px;
      background-color: #E8F9FA;
      color: #10B3B7;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      
      i {
        color: #10B3B7;
        font-size: 14px;
        background-color: #E8F9FA;
      }
    }
  }
}

/deep/ .un-light-adapt-dialog {
  .el-dialog__header {
    padding: 24px;
  }
  
  .el-dialog__body {
    padding: 0 24px;
    
    color: #111C1C;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
  
  .el-dialog__footer {
    padding: 24px;
  }
}

.rubrics-select {
  position: absolute;
  top: 125px;
}

.fade-out-enter-active {
  transition: all 0.2s linear;
  overflow: hidden;
}

.fade-out-enter {
  opacity: 0;
}

.fade-out-enter-to {
  opacity: 1;
}

.fade-expand-enter-active, .fade-expand-leave-active {
  transition: all 0.5s linear;
  overflow: hidden;
}

.fade-expand-enter, .fade-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.fade-expand-enter-to, .fade-expand-leave {
  max-height: 800px;
  opacity: 1;
}

.light-adapt-checkbox-teacher-content-tip {
  ul {
    padding-left: 24px;
    margin: 8px 0;
  }
  
  li {
    list-style-type: disc;
    margin-bottom: 4px;
  }
}
</style>

<style>
.light-adapt-checkbox-content-tip {
  max-width: 700px;
}
</style>