<template>
  <!--是否同意删除 GPT 推荐删除的测评点弹框-->
  <el-dialog
    :title="$t('loc.unitPlannerStandardsDetected')"
    :visible.sync="checkedDomainConformOfUnitVisible"
    :append-to-body="true"
    @close="closeCheckedDomainDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="50%"
    custom-class="check-domain-dialog">
    <div>
      <span class="font-size-16 line-height-24 text-default">{{ $t('loc.unitPlannerMeasuresNotAlign') }}</span>
      <div class="w-full">
        <table border="1" class="w-full add-margin-t-20 checked-domain-table">
          <tbody>
          <tr>
            <td class="lg-padding-12 title-background text-default font-weight-600 text-center"
                style="width: 200px;border-right: 1px solid #DCDFE6;border-bottom: 1px solid #DCDFE6">
              {{ $t('loc.standardsOrMeasures') }}
            </td>
            <!--保留的测评点-->
            <td class="td-measure" style="border-bottom: 1px solid #DCDFE6">
              <div>
                <div class="flex-align-center flex-wrap">
                  <div v-for="measure in checkedDomainConformOfUnitResult.reserveMeasure" class="measure-check-tag"
                       style="">
                    {{ measure.abbreviation }}
                  </div>
                </div>
                <!--分割线-->
                <div class="remove-measure-split w-full">
                </div>
                <!--推荐删除的测评点-->
                <div class="flex-align-center flex-wrap">
                  <div class="font-size-14 text-default add-margin-t-6 add-margin-l-6 add-padding-r-4">
                    {{ $t('loc.recommendedForRemoval') }}
                  </div>
                  <div v-for="measure in checkedDomainConformOfUnitResult.removeMeasure" class="measure-check-tag">
                    <span style="opacity: 0.6">{{ measure.abbreviation }}</span>
                  </div>
                </div>
              </div>
            </td>
          </tr>
          <tr>
            <td class="lg-padding-12 title-background text-default font-weight-600 text-center"
                style="width: 200px;border-right: 1px solid #DCDFE6;">{{ $t('loc.detailInfo') }}
            </td>
            <!--推荐删除的理由-->
            <td class="lg-padding-16">{{ checkedDomainConformOfUnitResult.rationale }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <!--是否使用按钮-->
    <span slot="footer" class="dialog-footer">
          <el-button @click="handleRecommendMeasure(false)" plain>{{ $t('loc.skipAndGenerate') }}</el-button>
          <el-button type="primary" @click="handleRecommendMeasure(true)">{{ $t('loc.applyAndGenerate') }}</el-button>
        </span>
  </el-dialog>
</template>

<script>
import {mapState} from 'vuex'

export default {
  name: "CheckedDomainConformOfUnitDialog",
  props: {
    checkedDomainConformOfUnitResult: {
      type: Object,
      default: () => ({})
    },
    checkedDomainConformOfUnitVisible: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    ...mapState({
      unitInfo: state => state.curriculum.unit.baseInfo,
    }),
  },
  methods: {
    // 确认是否删除推荐删除的测评点
    async handleRecommendMeasure(isApply) {
      // 如果应用，则更新单元数据
      if (isApply) {
        // 修改选择的测评点 id
        this.unitInfo.measureIds = this.checkedDomainConformOfUnitResult.reserveMeasure.map(measure => measure.id)
        // 修改选择的测评点
        this.unitInfo.measures = this.unitInfo.measures.filter(measure => this.unitInfo.measureIds.includes(measure.id))
        // 更新表单的数据
        this.checkedDomainConformOfUnitResult.removeMeasure.forEach(measure => this.$emit('deleteMeasure', measure))
        // 关闭推荐弹框
        this.closeCheckedDomainDialog()
        this.$message.success(this.$t('loc.unitPlannerRecommendationApplied'))
      }
      // 关闭推荐弹框
      this.closeCheckedDomainDialog()
      this.$emit('next')
    },
    closeCheckedDomainDialog() {
      // 关闭推荐弹框
      this.$emit('update:checkedDomainConformOfUnitVisible', false)
    }
  },
}
</script>

<style lang="scss" scoped>
.checked-domain-table {
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  border-collapse: separate;
  border-spacing: 0;
  
  td {
    border: none;
  }
}

.title-background {
  background: rgba(235, 238, 245, 0.40);
}

.measure-check-tag {
  margin-top: 6px;
  margin-left: 6px;
  color: #111C1C;
  font-size: 12px;
  padding: 2px 8px;
  background-color: #F5F6F8;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
}

.td-measure {
  padding: 12px 16px 16px 10px;
}

.remove-measure-split {
  margin: 12px 0px 6px 0px;
  border-top: 1px dashed #DCDFE6;
}

/deep/ .check-domain-dialog {
  .el-dialog__header {
    padding: 24px;
  }
  
  & .el-dialog__title {
    font-size: 20px !important;
    font-weight: 600;
  }
  
  & .el-dialog__body {
    padding: 0 24px 14px 24px;
  }
  
  & .el-dialog__footer {
    padding: 10px 24px 24px;
  }
}

</style>