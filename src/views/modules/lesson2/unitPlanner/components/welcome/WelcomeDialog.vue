<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="560px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    @click.stop
    :before-close="handleClose">
    <div class="feature-reminder" role="dialog" aria-labelledby="tour-title">
      <!-- 根据类型显示视频或图片 -->
      <video
        v-if="welcomeDialogVariant === 'test'"
        class="feature-image"
        autoplay
        loop
        muted
        playsinline
        :src="unitPlannerWelcomeMp4Url"
        :poster="unitPlannerWelcomeVideoCover"
        >
      </video>
      <img v-else :src="pluginGuide" class="feature-image"/>
      <div class="content-wrapper">
        <div class="lg-margin-top-24 lg-margin-l-r-24 lg-color-text-primary">{{ displayText }}</div>
        <div class="button-container">
          <div class="button-wrapper">
            <el-button @click="startExploring" class="tour-button">
                <span class="button-text">Get Started</span>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {mapState} from 'vuex';
import pluginGuide from '@/assets/img/curriculumPlugin/plugin-guide.png'
export default {
  name: 'WelcomeDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    welcomeDialogVariant: {
      type: String,
      default: 'control'
    }
  },
  computed: {
    ...mapState({
      open: state => state.common.open,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      guideFeatures: state => state.common.guideFeatures // 功能引导
    }),
    pluginGuide () {
      return this.isCurriculumPlugin ? pluginGuide : ''
    },
    displayImage() {
      return this.welcomeDialogVariant === 'test' ? this.unitPlannerWelcomeImgUrl : this.pluginGuide
    },
    displayText() {
      return this.welcomeDialogVariant === 'test'
        ? this.$t('loc.unitPlannerWelcomeTipForTest')
        : this.$t('loc.unitPlannerWelcomeTipForControl')
    }
  },
  data () {
    return {
      unitPlannerWelcomeImgUrl: 'https://s3.us-east-1.amazonaws.com/com.learning-genie.cdn/cg/images/unit_planner_guide_0506.gif',
      unitPlannerWelcomeMp4Url: 'https://s3.us-east-1.amazonaws.com/com.learning-genie.cdn/cg/images/unit_planner_guide.mp4',
      unitPlannerWelcomeVideoCover: require('@/assets/img/curriculumPlugin/unit_planner_welcome_video_cover.png')
    }
  },
  mounted () {
  },
  watch: {
    dialogVisible (newVal) {
      if (newVal) {
        if (this.welcomeDialogVariant === 'test') {
          this.$analytics.sendEvent('cg_unit_welcome_unpack_b')
        } else {
          this.$analytics.sendEvent('cg_unit_welcome_unpack')
        }
      
      }
    }
  },
  methods: {
    handleClose () {
      // 清除本地存储
      event.stopPropagation()
      // 恢复默认值
      this.$emit('update:dialogVisible', false)
    },
    startExploring () {
      // 发送开始创建 unit 埋点事件
      this.$analytics.sendEvent('cg_unit_welcome_begin')
      this.$emit('update:dialogVisible', false)
      // 设置缓存
      localStorage.setItem('UNIT_PLANNER_GUIDE_FIRST_VISIT' + this.currentUserId, "true")
      // 隐藏引导过程
      let result = { 'features': ['PLUGIN_UNIT_PLANNER_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then(() => {
        // 更新 guideFeatures 中 store 中的值
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
              ...this.guideFeatures,
              showCurriculumUnitPlannerGuide: false
            })
      })
      // this.$emit('startExploring')
      // 清空已有单元信息
      this.$store.commit('curriculum/RESET_UNIT')
      // 跳转到创建页面
      this.$router.push({
        name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-creator-cg' : 'unitCreator'
      })
    }
  }
}
</script>

<style lang="less" scoped>
.feature-reminder {
  border-radius: 12px;
  display: flex;
  max-width: 560px;
  flex-direction: column;
  overflow: hidden;
  color: var(--ffffff, #fff);
  text-align: center;
  font: 600 16px Inter, sans-serif;
}

.hero-image {
  object-fit: contain;
  object-position: center;
  width: 100%;
  z-index: 10;
}

.content-wrapper {
  background-color: rgba(255, 255, 255, 1);
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: start;
}

.feature-image {
  object-fit: contain;
  object-position: center;
  width: 100%;
}

.button-container {
  background-color: rgba(255, 255, 255, 1);
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0 30px 0;
}

.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tour-button {
  background: var(--logo, linear-gradient(36deg, #10b3b7 15.6%, #aa89f2 87.76%)) !important;
  border-color: transparent !important;
  border: 0 !important;
  min-height: 45px;
  width: 159px !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.tour-button:hover {
  box-shadow: 0 0 0 3px rgba(45, 156, 219, 0.3) !important;
}
.button-text {
  font-feature-settings: "liga" off, "clig" off;
  padding: 0 4px;
  color: var(--ffffff, #fff);
}

/deep/ .el-dialog {
  border-radius: 16px;
  overflow: hidden;
}

/deep/ .el-dialog__header {
  display: none;
}

/deep/ .el-dialog__body {
  padding: 0px !important;
}

/deep/ .el-button {
  padding: 0;
  width: 100%;
  height: 100%;
}

@media (max-width: 991px) {
  .hero-image {
    max-width: 100%;
  }

  .content-wrapper {
    max-width: 100%;
    z-index: 9999;
  }

  .feature-image {
    max-width: 100%;
  }

  .button-container {
    max-width: 100%;
  }
}
</style>