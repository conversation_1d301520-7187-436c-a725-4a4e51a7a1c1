<template>
  <div>
    <!-- adapter 按钮及其引导-->
    <el-popover
      placement="bottom"
      width="500"
      :visible-arrow="true"
      v-model="showUnitAdaptGuide"
      ref="settingUnitTips"
      popper-class="adapt-UDL-and-CLR-guide-color-text"
      trigger="manual"
    >
      <div class="text-white">
        <template v-if="isAdmin">
          <div class="font-size-20 font-bold line-height-26 add-margin-b-12" v-if="!isCG">
            {{ $t('loc.personalization') }}
          </div>
          <div>
            <div
              class="learner-popover-info font-size-16 font-bold line-height-24 add-padding-l-24 add-margin-b-8 position-relative"
              v-if="!isCG"
            >
              {{ $t('loc.mixAgeGroup1') }}
            </div>
            <div
              class="learner-popover-desc font-size-16 font-weight-400 line-height-24 add-margin-l-24 add-margin-b-8 text-interrupt text-left"
              v-if="!isCG"
            >
              {{ $t('loc.mixAgeGroup6') }}
            </div>
            <div
              class="learner-popover-info font-bold line-height-24 add-margin-b-8 position-relative"
              :class="{
                'learner-info-cg font-size-20': isCG,
                'font-size-16 add-padding-l-24': !isCG,
              }"
            >
              {{ $t('loc.tailorTitle') }}
            </div>
            <div
              class="learner-popover-desc font-size-16 font-weight-400 line-height-24 text-interrupt text-left"
              :class="{
                'learner-desc-cg': isCG,
                'add-margin-l-24': !isCG,
              }"
            >
              {{ $t('loc.personalizationTip') }}
            </div>
          </div>
          <div class="flex-row-right w-full add-margin-t-24">
            <template v-if="isCG">
              <div
                class="learner-popover-btn text-center gap-8 bg-white border-radius-4 flex-row-center font-size-14 font-weight-400 line-height-22 lg-pointer add-margin-r-16"
                @click="hideGuideOperate(false)"
              >
                {{ $t('loc.mixAgeGroup4') }}
              </div>
              <div
                class="learner-popover-btn text-center gap-8 bg-white border-radius-4 flex-row-center font-size-14 font-weight-400 line-height-22 lg-pointer"
                @click="hideGuideOperate(true, 'learnerProfile')"
              >
                {{ $t('loc.setUpByGrant') }}
              </div>
            </template>
            <div
              class="learner-popover-btn text-center gap-8 bg-white border-radius-4 flex-row-center font-size-14 font-weight-400 line-height-22 lg-pointer"
              v-else
              @click="hideGuideOperate(false)"
            >
              {{ $t('loc.ikonw') }}
            </div>
          </div>
        </template>
        <template v-else>
          <!-- 老师角色 -->
          <!-- 标题 -->
          <div class="title-font-20 lg-margin-bottom-20">
            {{ $t('loc.mixAgeGroup1') }}
          </div>
          <!-- 引导文字 -->
          <div class="lg-margin-bottom-24 word-break text-left">
            <!-- 用户引导内容 -->
            <span
              class="font-size-16 font-weight-400 line-height-24 display-inline-block add-margin-b-10"
            >
              {{ $t('loc.mixAgeGroup6') }}
            </span>
            <img class="w-full" src="~@/assets/img/lesson2/unitPlanner/mixed_age_tips.png" alt="" />
          </div>
          <div class="display-flex flex-justify-end gap-6 align-items">
            <span v-if="showSteps" class="font-size-16">(2/2)</span>
            <el-button class="bg-ai">
              <span @click="hideGuideOperate(false)">{{ $t('loc.mixAgeGroup4') }}</span>
            </el-button>
            <el-button type="primary" @click="hideGuideOperate(true)">
              {{ $t('loc.mixAgeGroup5') }}
            </el-button>
          </div>
        </template>
      </div>
      <div slot="reference">
        <slot name="reference"></slot>
      </div>
    </el-popover>
  </div>
</template>
<script>

export default {
  name: 'SettingUnitTips',
  components: {},
  props: {
    // 是否隐藏改编按钮
    hiddenAdaptBtn: {
      type: Boolean,
      default: false
    },
    showUnitAdaptGuide: {
      type: Boolean,
      default: false
    },
    isAdmin: {
      type: Boolean,
      default: false
    },
    isCG: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showAdaptUDLAndCLRGuide: false, // 是否显示引导
      showSteps: false // 是否显示 step 的值
    }
  },
  methods: {
    // 隐藏引导
    hideGuideOperate(isLater, type) {
      this.$emit('hideAdaptGuideOperate', isLater, true, type)
    }
  }
}
</script>
<style lang="less" scoped>
.center-group-select {
  width: 180px;
}

@media screen and (max-width: 1366px) {
  .center-group-select {
    width: 165px;
  }

  /deep/.adapt-unit-dialog {
    margin-top: 5vh !important;
  }
}

/deep/.adapt-unit-dialog {
  .el-dialog__body {
    padding: 0 20px;
  }
}

/deep/.el-table > .table-header > .cell {
  word-break: break-word;
}

.custom-select {
  height: 40px;
  line-height: 40px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-white);
  color: var(--color-text-primary);
  &.active {
    border-color: var(--color-primary);
  }
}

.adapt-unit-list {
  max-height: 300px;
  padding-right: 12px;
}

.adapt-unit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px;
  border-radius: 4px;
  height: 38px;
  line-height: 38px;
  cursor: pointer;
  background: var(--color-white);
  color: var(--color-text-primary);
  &.selected {
    background: var(--color-primary-light) !important;
  }
}

.new-tag {
  background: var(--color-danger);
  color: var(--color-white);
  border-radius: 10px;
  padding: 2px 5px;
  font-size: 12px;
  margin-left: 5px;
}

.adapted-tag {
  background: var(--color-white) !important;
  color: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
}

.child-list {
  max-height: calc(100vh - 330px);
}

.child-list-open {
  height: calc(100vh - 392px);
}

.empty-child-list {
  height: calc(100vh - 450px);
}
</style>

<style lang="less">
.adapt-unit {
  background: var(--color-table-header-background);
  padding-right: 0px;
}

.el-popper.adapt-UDL-and-CLR-guide-color-text {
  background: var(--color-ai-assistant);
  color: #ffffff;
  padding: 24px;
  border: none;

  &.el-popper[x-placement^='left'] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^='right'] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^='bottom'] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^='bottom'] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^='top'] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }

  p {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
  }

  ul {
    padding-left: 24px;
    margin-bottom: 24px;

    li {
      list-style: disc;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
    }
  }

  .el-button {
    //color: var(--color-ai-assistant);
    //background: var(--color-white);
    //border-color: var(--color-ai-assistant);
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #dcdfe6);
    background: var(--ffffff, #fff);
    color: #676879;
  }

  .bg-ai {
    color: var(--ffffff) !important;
    background: var(--color-ai-assistant) !important;
  }

  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #dcdfe6);
    background: var(--ffffff, #fff);
  }
}
.learner-popover-info {
  &.learner-info-cg {
    padding-left: 0;
    &::before {
      display: none;
      padding-left: 0;
    }
  }
  &::before {
    position: absolute;
    content: '';
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 4px;
    background: #fff;
    top: 10px;
    left: 10px;
  }
}
.learner-popover-desc {
  &.learner-desc-cg {
    margin-left: 0;
  }
}
.learner-popover-btn {
  height: 40px;
  padding: 8px 12px;
  border: 2px solid var(--dcdfe-6, #dcdfe6);
}
</style>
