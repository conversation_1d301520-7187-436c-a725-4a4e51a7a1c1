<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="600px"
    :title="downloadType"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @click.stop
    :append-to-body="true"
    :before-close="handleClose">
    <div>
      <!-- 标题 -->
      <div class="flex justify-content items-center">
        <span class="text-base">{{ downloadTip }}
        <span v-if="percentage !== 100">{{ $t('loc.downloadClosing') }}</span>
        </span>
      </div>
      <div style="display: flex; justify-content: flex-start; margin-top: 8px">
        <span style="color:#E6A23C;">{{ $t('loc.downloadReceive')}}</span>
      </div>
      <div class="downloadBody">
        <!-- 下载进度 -->
        <div>
          <el-progress class="padding-progress" type="circle" v-if="percentage < 100" :percentage="percentage" :color="customColor" :text-color="textCustomColor"></el-progress>
          <el-progress class="custom-progress" type="circle" v-if="percentage === 100" :percentage="100" status="success" :color="customColor" :text-color="textCustomColor"></el-progress>
        </div>
        <div v-if="percentage < 100" class="text-base font-bold">{{ $t('loc.downloadBeing', {unitName: unitName}) }}</div>
        <div v-if="percentage === 100" class="text-base font-bold">{{ $t('loc.downloadSuccessfully', {unitName: unitName}) }}</div>
        <div v-if="percentage === 100 && downloadUrl !== '' " style="margin-top: 16px">
          <!-- 下载按钮 -->
          <el-button type="primary" size="medium" @click="downloadOpen">{{ downloadButton }}</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'UnitDownloadDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    downloadType: {
      type: String,
      default: 'Download Unit'
    },
    percentage: {
      type: Number,
      default: 0
    },
    // 单元是否使用课程模板
    useLessonTemplate: {
      type: Boolean,
      default: false
    },
    unitName: {
      type: String,
      default: ''
    },
    downloadUrl: {
      type: String,
      default: ''
    },
    batchId: {
      type: String,
      default: ''
    },
    downloadButton: {
      type: String,
      default: ''
    },
    // 添加 unit 属性
    grade: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser, // 当前用户
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      magicAllowUnits: state => state.magicCurriculum.magicAllowUnits, // 允许创建的 unit 数量
    }),
    // 用户 ID
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    isCG () {
      return this.$store.state.curriculum.isCG
    },
    // 下载提示
    downloadTip () {
      return this.downLoadTipDescription()
    }
  },
  data () {
    return {
      customColor: '#67C23A',
      textCustomColor: '#111C1C',
    }
  },
  methods: {
    downLoadTipDescription () {
      let grade1AndGrade2 = ['K (5-6)','Grade 1', 'Grade 2']
      let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
      if (k12Grades.indexOf(this.grade) <= -1) {
        return this.$t('loc.downloadIncludes')
      } else if (grade1AndGrade2.indexOf(this.grade) <= -1) {
        if (this.useLessonTemplate) {
          return this.$t('loc.downloadIncludes3')
        } else {
          return this.$t('loc.downloadIncludes5')
        }
      } else {
        if (this.useLessonTemplate) {
          return this.$t('loc.downloadIncludes2')
        } else {
          return this.$t('loc.downloadIncludes4')
        }
      }
    },
    handleClose () {
      // 清除本地存储
      event.stopPropagation()
      localStorage.removeItem('currentFileNotification')
      localStorage.removeItem('syncId')
      // 恢复默认值
      this.$emit('update:dialogVisible', false)
      // 显示分享弹窗
      this.openShareDialog()
      setTimeout(() => {
        this.$emit('update:percentage', 0)
      }, 500)
      if (this.percentage === 100 && this.downloadUrl !== '' && this.batchId) {
        // 修改当前通知状态
        let params = {
          batchId: this.batchId,
        }
        this.$axios.post($api.urls().setFileNotificationStatusByBatchId, params)
      }
    },
    downloadOpen () {
      event.stopPropagation()
      // 打开下载链接
      if (this.downloadButton === 'Download' || this.downloadButton === 'Descargar' || this.downloadButton === '下载') {
        window.location.href = this.downloadUrl
      } else {
        window.open(this.downloadUrl)
      }
      if (this.batchId) {
        let params = {
          batchId: this.batchId,
        }
        this.$axios.post($api.urls().setFileNotificationStatusByBatchId, params)
      }
      // 恢复默认值
      this.$emit('update:dialogVisible', false)
      // 显示分享弹窗
      this.openShareDialog()
      setTimeout(() => {
        this.$emit('update:percentage', 0)
      }, 1000)
    },
    stopEvent () {
      event.stopPropagation(); // 手动阻止冒泡
    },
    // 显示分享弹窗
    async openShareDialog () {
      // 获取创建 unit 奖励状态
      await this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
      // 不能再获取奖励
      let unlockUnlimitedAccess = this.isCurriculumPlugin && this.magicAllowUnits >= 18
      // 显示分享弹窗，每天显示一次
      const cacheKey = 'unitDownloadShareDialogLastShownDate' + this.currentUserId
      // 获取缓存显示弹窗日期
      const lastShownDate = localStorage.getItem(cacheKey)
      // 获取今天日期
      const today = this.$moment().format('YYYY-MM-DD')
      // 如果没有缓存或者缓存的日期不是今天，并且可以再获取奖励，则显示分享弹窗
      if ((!lastShownDate || lastShownDate !== today) && !unlockUnlimitedAccess) {
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'INVITE_MORE',
          source: 'UNIT_DOWNLOAD',
          shared: false
        }
        // 向父级发送显示弹窗消息
        this.$bus.$emit('message', data)
        // 缓存显示弹窗日期
        localStorage.setItem(cacheKey, today)
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-dialog__title {
  font-size: 20px;
}
/deep/ .el-dialog__header {
  padding: 24px 24px 24px 24px !important;
}
/deep/ .el-dialog__body {
  padding: 0px 24px 24px 24px !important;
}

.downloadBody {
  margin-top: 20px;
  text-align: center;
  background: #F5F6F8;
  padding: 20px;
}

.padding-progress {
  ::v-deep {
    .el-progress__text {
      color: #111C1C;
    }
  }
}

.custom-progress{
  ::v-deep {
    .el-progress__text {
      .el-icon-check{
        display: none;
      }
      .el-icon-check:before{
        //font-family: element-icons !important;
      }
    }
    .el-progress__text::before {
      content: ''; /* 清除默认图标 */
      display: block;
      background: url('../../../../../assets/img/icon/custom-success-icon.png') no-repeat center; /* 自定义成功图标 */
      background-size: contain;
      width: 3.5em;
      height: 3.5em;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

</style>
