<!-- HelpStandardsButton.vue -->
<template>
  <div class="help-standards-container">
    <!-- 帮助按钮 -->
    <el-button 
        type="primary" 
        plain
        size="mini"
        class="help-standards-btn"
        @click="handleClick"
    >
      {{ $t('loc.helpMeFindMyStateStandards') }}
    </el-button>

    <!-- 问号图标和 tooltip -->
    <el-tooltip
      effect="dark"
      placement="top"
      popper-class="max-width-300"
      :content="$t('loc.helpMeFindMyStateStandardsTip')"
    >
      <i class="lg-icon lg-icon-question font-size-18"></i>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'HelpStandardsButton',
  methods: {
    handleClick() {
      this.$emit('show-dialog')
    }
  }
}
</script>

<style lang="less" scoped>
.help-standards-container {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
}
</style> 