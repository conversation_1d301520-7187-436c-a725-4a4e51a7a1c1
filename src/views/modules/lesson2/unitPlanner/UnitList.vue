<template>
  <div class="unit-list-container" :class="{ 'unit-list-limit-width': isCG || isCurriculumPlugin }">
    <div ref="stickyHeader" class="new-unit-box lg-margin-top-24">
      <!-- 欢迎语 -->
      <div class="text-center">
        <div class="title-font-24">🎉 Welcome to Unit Planner！</div>
        <div class="title-font-16-regular lg-margin-top-16">Create a smarter, future-ready curriculum with AI. Start
          planning now!
        </div>
        <!-- 顶部 -->
        <el-button v-if="allowCreateUnit || isCG"
                   type="primary"
                   icon="el-icon-plus"
                   id="create-new-unit"
                   :loading="newUnitLoading"
                   style="width: 276px"
                   :class="[{ 'ai-btn': !allowCreateImportUnit }, 'lg-margin-top-16']"
                   @click="newUnit">
          {{ $t('loc.unitPlannerNewUnit') }}
        </el-button>
        <!-- 单元分析入口 -->
        <el-button v-if="allowCreateUnit && allowCreateImportUnit"
                   type="primary"
                   icon="lg-icon lg-icon-upload"
                   id="create-new-unit"
                   :loading="newUnitLoading"
                   style="width: 276px"
                   class="ai-btn lg-margin-top-16 position-relative"
                   @click="newAnalysisUnit">
          <span style="margin-left: 10px">Adapt Existing Unit</span>
          <div class="font-size-14 font-weight-600 height-20 line-height-20 position-absolute add-padding-lr-6"
               style="background-color: #F56C6C; border-radius: 10px; right: -9px; top: -5px">Beta
          </div>
        </el-button>
      </div>
    </div>
    <!-- 顶部 -->
    <!-- 模拟 sticky header 高度，防止内容抖动 -->
    <div v-if="isSticky" style="height: 72px;"></div>
    <div :class="{'sticky-header': isSticky, 'condition-content': !isSticky, 'sticky-header-banner': showBanner}">
      <div class="display-flex justify-content-between align-items flex-wrap gap-12"
           :class="isSticky ? 'unit-list-limit-width' : ''">
        <div :class="{
          'display-flex': isMC || isCurriculumPlugin
        }" class="justify-content align-items">
          <!-- 不允许创建 Unit，将单元总数位置显示到上方 -->
          <div v-if="!allowCreateUnit" class="font-bold lg-margin-t-b-12">{{ $t('loc.totalEntries') }}: {{
              total
            }}
          </div>
          <!-- 状态筛选框-->
          <!-- <div class="display-flex justify-content align-items w-full">
            <el-select v-if="allowCreateUnit || isCG" v-model="filterParams.status" placeholder=""
                       class="border-bold unit-filter-select">
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :disabled="item.disabled"
                :value="item.value">
              </el-option>
            </el-select>
          </div> -->
          <!-- 是否改编状态筛选 -->
          <!-- <el-select v-if="allowCreateUnit && !isCG && !isCurriculumPlugin " v-model="filterParams.adaptType" placeholder="" class="m-l-sm border-bold unit-filter-select">
            <el-option
              v-for="item in adaptStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select> -->
          <!-- 创建人筛选框 -->
          <!-- <el-select v-if="(allowCreateUnit || isCG) && !isMC && !isCurriculumPlugin" class="m-l-sm border-bold unit-filter-select" v-model="filterParams.createBy" placeholder="">
            <el-option
              v-for="item in createByList"
              :key="item.value"
              :label="item.label"
              :disabled="item.disabled"
              :value="item.value">
            </el-option>
          </el-select> -->
          <a
            :href="pptHref"
            @click="clickPPTGuide"
            class="flex-center-center display-flex justify-content align-items
            lg-pointer magic-question bg-white border-radius-4
            add-padding-l-12 add-padding-r-12 color-dll-title font-size-14"
            target="_blank"
            style="width: 100%;"
            v-if="isMC || isCurriculumPlugin"
          >
            <i class="lg-icon-question lg-icon add-margin-r-4 font-size-20"></i>
            <span>PPT Guide</span>
          </a>
          <div
            class="flex-center-center lg-pointer magic-question bg-white border-radius-4 add-margin-l-12 add-padding-l-12 add-padding-r-12 color-dll-title font-size-14"
            @click="showVideoGuide"
            v-if="isMC"
          >
            <i class="lg-icon-learning-media lg-icon add-margin-r-4 font-size-24"></i>
            <span>Video Guide</span>
          </div>
        </div>
        <!-- 右侧操作 -->
        <div class="btn-style-group display-flex align-items" style="gap: 10px">
          <template>
            <el-skeleton style="height: 20px" animated :rows="1" :loading="checkCreateUnitLoading">
              <template slot="template">
                <el-skeleton-item variant="text" style="width: 250px; height: 20px"/>
              </template>
              <template>
                <span v-if="isMC"> You've created {{ magicCreatedUnits.length }} out of your {{ magicAllowUnits }} units.</span>
                <span v-if="isCurriculumPlugin && magicAllowUnits > 0"> You've created {{ magicCreatedUnits.length }} out of {{
                    magicAllowUnits
                  }} units.
                  <a class="refer-link" @click="handleRefer">Refer & Earn</a>
                </span>
              </template>
            </el-skeleton>
          </template>
          <!-- 创建按钮显示增加条件：是老师且有创建权限才显示 -->
          <el-button v-if="(allowCreateUnit || isCG) && isSticky" type="primary" icon="el-icon-plus"
                     style="width: 160px;"
                     :class="[{ 'ai-btn': !allowCreateImportUnit }]"
                     @click="newUnit">{{ $t('loc.unitPlannerNewUnit') }}
          </el-button>
          <!-- 单元分析入口 -->
          <el-button v-if="allowCreateUnit && isSticky && allowCreateImportUnit"
                     type="primary"
                     icon="lg-icon lg-icon-upload"
                     id="create-new-unit"
                     :loading="newUnitLoading"
                     style="width: 200px"
                     class="ai-btn position-relative"
                     @click="newAnalysisUnit">
            <span style="margin-left: 8px">Adapt Existing Unit</span>
            <div class="font-size-14 font-weight-600 height-20 line-height-20 position-absolute add-padding-lr-6"
                 style="background-color: #F56C6C; border-radius: 10px; right: -9px; top: -5px">Beta
            </div>
          </el-button>
          <!-- Adapt 按钮需要开启功能后才会显示 -->
          <AdaptUnitTips ref="unitAdapt" @refreshUnits="refreshUnits"
                         v-if="initComponents"
                         v-show="false && (isCurriculumPlugin || (isOpenAdaptUDLAndCLR && !isCG))"
                         :disabled="!unitUsageModel.adaptUnit"
                         :showUnitAdaptGuide="showUnitAdaptGuide"
                         @hideAdaptGuideOperate="hideAdaptGuideOperate"/>
          <!-- 应用 Unit 到周计划弹窗组件 -->
          <!-- <UnitApply v-if="!isCG && !isCurriculumPlugin" ref="unitApply" applyType="home" :unit="currentUnit" @clearCurrentUnit="clearCurrentUnit"/> -->
          <SettingUnitTips ref="settingUnit"
                           v-if="(isOpenAdaptUDLAndCLR || (isCG && isAdmin)) && !isMC && !isSticky"
                           :isAdmin="isAdmin"
                           :isCG="isCG"
                           :showUnitAdaptGuide="showSettingUnitGuide"
                           @hideAdaptGuideOperate="hideSettingUnitGuideOperate"
          >
            <!-- 齿轮 + 三个展示的标题及其引导部分 -->
            <div slot="reference" v-if="(!isCG || isAdmin) && !isMC">
              <el-popover
                placement="bottom-end"
                trigger="click"
                popper-class="curriculum-popover"
                width="350"
              >
                <div class="display-flex flex-direction-col add-padding-t-8 add-padding-b-8"
                     style="align-items: baseline;">
                  <el-link class="settings-style"
                           v-if="!isCG && !isCurriculumPlugin"
                           :underline="false"
                           @click="manageClassRoomDemographics()">
                    {{ $t('loc.unitPlannerPlanItemManageClassroomDemographics') }}
                  </el-link>
                  <el-link class="settings-style"
                           :underline="false"
                           v-if="isAdmin && !isCG && !isCurriculumPlugin"
                           @click="managePermission()">
                    {{ $t('loc.managePermission') }}
                  </el-link>
                  <el-link class="settings-style"
                           :underline="false"
                           v-if="!isCG && !isCurriculumPlugin"
                           @click="showMixedAgeDifferentiation">{{ $t('loc.mixAgeGroup2') }}
                    <span v-show="showNewPointer" class="newPoint point-relative">{{ $t('loc.new') }}</span>
                  </el-link>
                  <!-- 校训配置 -->
                  <el-link class="settings-style"
                           :class="{'curriculum-button-padding': guideFeatures && guideFeatures.showManageLearnerProfileGuideNewTag}"
                           :underline="false" v-if="isAdmin" @click="manageLearnerProfile">{{ $t('loc.tailorTitle') }}
                    <span v-show="guideFeatures && guideFeatures.showManageLearnerProfileGuideNewTag"
                          class="newPoint point-relative">{{ $t('loc.new') }}</span>
                  </el-link>
                </div>
                <el-button slot="reference" style="padding: 0 10px" v-if="!isCG || isAdmin"
                           class="m-l-xs lg-icon lg-icon-settings" @click="handleSettings"></el-button>
              </el-popover>
            </div>
          </SettingUnitTips>
        </div>
      </div>
      <!-- Mixed-age Differentiation Settings -->
      <!--      <MixedAgeDifferentiationSettings ref="mixedAgeDifferentiation"></MixedAgeDifferentiationSettings>-->
      <!-- 允许创建 Unit，将单元总数位置不变 -->
      <div v-if="allowCreateUnit && !isCG && !isCurriculumPlugin"
           class="font-bold lg-margin-t-b-12 display-flex align-items justify-content-between">
        <span>{{ $t('loc.totalEntries') }}: {{ total }}</span>
        <el-input v-model="keywords"
                  :placeholder="this.$t('loc.UnitSearchUnitName')"
                  @input="autoSearch"
                  style="width: 200px"
                  @keyup.enter.native="handleSearch"
                  class="border-bold"
                  prefix-icon="el-icon-search"
        ></el-input>
        <!-- 创建按钮显示增加条件：是老师且有创建权限才显示 -->
        <!-- <el-button v-if="allowCreateUnit" type="primary" icon="el-icon-plus" class="ai-btn" @click="newUnit">{{$t('loc.unitPlannerNewUnit')}}</el-button> -->
        <!-- 应用 Unit 到周计划弹窗组件 -->
        <!-- <UnitApply ref="unitApply" applyType="home" v-show="!isCG"/> -->
      </div>
    </div>
    <!-- 单元列表 -->
    <div class="w-full" v-if="units && units.length !== 0">
      <div class="add-margin-t-16" ref="units" v-infinite-scroll="getUnits" :infinite-scroll-distance="10"
           :infinite-scroll-disabled="firstLoadDocument && isMC">
        <el-row :gutter="24">
          <BannerCard/>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="isMC || isCurriculumPlugin ? 8 : 6"
                  v-for="(unit, index) in units" :key="index">
            <UnitCard
              class="m-b-md unit-card"
              :unit="unit"
              @initAdaptComponent="initializeComponents"
              @applySingleUnit="applySingleUnit"
              @adaptSingleUnit="adaptSingleUnit"
              @refreshUnits="refreshUnits"
              @publishSuccess="publishSuccess"
              @downloadPDF="downloadPDF"
              @downloadGoogleDocs="downloadGoogleDocs">
            </UnitCard>
          </el-col>
        </el-row>
      </div>
    </div>
    <div v-if="units.length === 0 && !isFirstLoading" class="add-margin-t-24 h-full">
      <EmptyCard aiTips=" " :searchTips="$t('loc.plan36')"/>
    </div>
    <!-- Loading 列表 -->
    <div class="m-t-sm" v-if="isFirstLoading">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8" v-for="index in 8" :key="index">
          <UnitCard class="m-b-md" :loading="true" @initAdaptComponent="initializeComponents"/>
        </el-col>
      </el-row>
    </div>
    <!-- loading 数据 -->
    <div v-if="getUnitsLoading === 1 && (units && units.length > 0)">
      <div style="margin-top: 20px;font-size: 16px;text-align: center">
        <i class="el-icon-loading"></i> {{ $t('loc.lessons2LessonListLoading') }}
      </div>
    </div>
    <!-- 发布 Magic 成功提示弹窗 -->
    <!-- <MagicSuccessDialog :unit="editUnit" :visible.sync="magicSuccessVisible"></MagicSuccessDialog> -->
    <UnitDownload v-if="initComponents" :showUnitDownload="false" ref="unitDownLoad"
                  :unit="downloadUnit"></UnitDownload>
    <!-- 校训配置 -->
    <LearnerProfilePopup v-if="initComponents" ref="learnerProfilePopupRef"></LearnerProfilePopup>
    <!-- 欢迎弹窗 -->
    <WelcomeDialog v-if="isCurriculumPlugin"
                   @startExploring="startGuide"
                   :dialogVisible.sync="welcomeDialogOpen"
                   :welcomeDialogVariant="welcomeDialogVariant"></WelcomeDialog>
    <!-- 导入单元改编弹窗 -->
    <ImportUnitAdaptDialog v-if="isCurriculumPlugin"
                           :dialogVisible.sync="importUnitAdaptDialogOpen"
                           @closeDialog="closeImportUnitAdaptDialog"></ImportUnitAdaptDialog>
  </div>
</template>

<script>
import UnitCard from './components/list/UnitCard.vue'
// import UnitApply from './components/unitApply/UnitApply.vue'
import SettingUnitTips from '@/views/modules/lesson2/unitPlanner/components/settingUnit/SettingUnitTips.vue'
// import LearnerProfilePopup from '@/views/modules/lesson2/unitPlanner/components/learnerProfile/LearnerProfilePopup.vue'
// import AdaptUnitTips from '@/views/modules/lesson2/unitPlanner/components/adaptUnits/AdaptUnitTips.vue'
// eslint-disable-next-line import/no-duplicates
import { acrossRole, isTeacher } from '@/utils/common'
import defaultAvatar from '@/assets/img/healthCheck/child_avatar.jpg'
import EmptyCard from './components/editor/EmptyCard.vue'
import tools from '@/utils/tools'
import { mapState } from 'vuex'
// eslint-disable-next-line import/no-duplicates
// import MagicSuccessDialog from '@/views/magicCurriculum/components/btnGroup/MagicSuccessDialog.vue'
// import UnitDownload from '@/views/modules/lesson2/lessonLibrary/components/UnitDownload.vue'
// import MixedAgeDifferentiationSettings from '@/views/modules/lesson2/lessonPlan/components/MixedAgeDifferentiationSettings.vue';
import WelcomeDialog from '@/views/modules/lesson2/unitPlanner/components/welcome/WelcomeDialog.vue'
import ImportUnitAdaptDialog from '@/views/modules/lesson2/unitPlanner/components/ImportUnitAdaptDialog.vue'
// 引导
import Driver from 'driver.js' // import driver.js
import 'driver.js/dist/driver.min.css' // import driver.js css
import steps from '@/assets/js/guide/unitPlannerPlugin'
import { IMPORT_UNIT_ADAPT_NEW_USER_UTC } from '@/utils/const'
import BannerCard from './components/list/BannerCard.vue'

export default {
  components: {
    // MixedAgeDifferentiationSettings,
    WelcomeDialog,
    ImportUnitAdaptDialog,
    UnitDownload: () => import('@/views/modules/lesson2/lessonLibrary/components/UnitDownload.vue'),
    UnitCard,
    BannerCard,
    // UnitApply,
    EmptyCard,
    // MagicSuccessDialog,
    SettingUnitTips,
    AdaptUnitTips: () => import('@/views/modules/lesson2/unitPlanner/components/adaptUnits/AdaptUnitTips.vue'),
    LearnerProfilePopup: () => import('@/views/modules/lesson2/unitPlanner/components/learnerProfile/LearnerProfilePopup.vue'),
  },
  computed: {
    ...mapState({
      open: state => state.common.open,
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      currentUser: state => state.user.currentUser,
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      magicAllowCreateUnit: state => state.magicCurriculum.magicAllowCreateUnit, // 是否允许创建 unit
      unitUsageModel: (state) => state.magicCurriculum.unitUsageModel, // unit 使用的功能模块
      magicCreatedUnits: state => state.magicCurriculum.magicCreatedUnits, // 已创建 unit
      magicAllowUnits: state => state.magicCurriculum.magicAllowUnits, // 允许创建的 unit 数量
      checkCreateUnitLoading: state => state.magicCurriculum.checkCreateUnitLoading,
      showBanner: state => state.cgAuth.showBanner
    }),
    showNewPointer () {
      // eslint-disable-next-line no-unused-expressions
      this.triggerNewPointer
      const item = sessionStorage.getItem('showMixedAgeDifferentiation' + this.currentUserId)
      return !item
    },
    /**
     * PPT 引导链接
     */
    pptHref () {
      return this.isCurriculumPlugin ?
        'https://docs.google.com/presentation/d/1WGsxUfUjLAJnqSB63_MrumSV-obPA7xKcEhbNFDmJFE/edit?usp=sharing'
        : 'https://docs.google.com/presentation/d/1YqXol2Z47fTDHCL0BYXtXUIG9RgVdWUB/edit?usp=sharing&ouid=104481477209568284952&rtpof=true&sd=true'
    },
    /**
     * 获取用户 id
     */
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    isCG () {
      return this.$store.state.curriculum.isCG
    },
    // 单元创建权限控制
    allowCreateUnit () {
      return !this.isTeacher() || this.isTeacher() && this.open && this.open.createUnitPlannerOpen
    },
    // 可导入改编单元创建权限控制
    allowCreateImportUnit () {
      return this.open && this.open.cgImportUnitAdaptOpen
    },
    isAdmin () {
      // 判断当前用户的类型
      return !!acrossRole('agency_admin', 'agency_owner')
    },
    isOpenAdaptUDLAndCLR () {
      return this.open && this.open.adaptUDLAndCLROpen
    },
    showAdaptGuide () {
      return this.guideFeatures && this.guideFeatures.showAdaptUnitGuide
    },
    /**
     * 是否来自于 ipad
     */
    isFromIpad () {
      return tools.isComeFromIPad()
    },
    isShowAdaptUDLAndCLRGuide: {
      get () {
        return this.canAdapter && this.isOpenAdaptUDLAndCLR && this.showAdaptGuide
      },
      set (value) {
        // 如果功能引导信息存在，则更新
        if (this.guideFeatures) {
          this.guideFeatures.showAdaptUnitGuide = value
          this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
        }
      }
    },
    // 是否开启了 adapter 权限
    canAdapter () {
      if (!this.currentUser || this.isFromIpad) return false
      const { role2 = '' } = this.currentUser || {}
      let role = role2.toUpperCase()
      return role === 'COLLABORATOR' || role === 'TEACHING_ASSISTANT' || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
    },
    // 是否是导入单元改编弹窗新用户
    isImportUnitAdaptNewUser() {
      return this.currentUser && this.currentUser.userInfo && tools.timeIsAfter(this.currentUser.userInfo.createdAtUtc, IMPORT_UNIT_ADAPT_NEW_USER_UTC)
    },
  },
  data () {
    return {
      isSticky: false, // 是否固定
      magicSuccessVisible: false, // 发布成功弹窗
      welcomeDialogOpen: false, // 欢迎弹窗
      importUnitAdaptDialogOpen: false, // 导入单元改编弹窗
      welcomeDialogVariant: 'test', // 欢迎弹窗变体
      editUnit: {},
      // 过滤条件
      filterParams: {
        status: 'ALL',
        createBy: 'ALL',
        keyword: '',
        adaptType: 'ALL'
      },
      // 状态类型列表
      statusList: [{
        label: this.$t('loc.unitPlannerCompletionCondition'),
        value: 'ALL',
        disabled: false
      },
        {
          label: this.$t('loc.comple'),
          value: 'COMPLETED',
          disabled: false
        },
        {
          label: this.$t('loc.curriculum64'),
          value: 'DRAFT',
          disabled: false
        }],
      // 创建者类型列表
      createByList: [{
        label: this.$t('loc.UnitCreateByAnyone'),
        value: 'ALL',
        disabled: false
      },
        {
          label: this.$t('loc.UnitCreateByMe'),
          value: 'ME',
          disabled: false
        },
        {
          label: this.$t('loc.UnitCreateByOthers'),
          value: 'OTHERS',
          disabled: false
        }],
      newUnitLoading: false, // 创建新 Unit 加载状态
      // 改编状态
      adaptStatus: [
        {
          label: this.$t('loc.unitPlannerAdaptedCondition'),
          value: 'ALL'
        },
        {
          label: 'Adapted',
          value: 'ADAPTED'
        },
        {
          label: 'Unadapted',
          value: 'UNADAPTED'
        }
      ],
      triggerNewPointer: false, // 是否触发 New 的标志
      keywords: '', // 搜索关键字
      units: [], // 单元列表
      getUnitsLoading: 0, //  0 未加载，1 加载中，2 已加载
      pageSize: 9, // 每次加载的页大小
      pageNum: 1, // 已加载页
      ended: false, // 数据是否已全部加载
      endScroll: false, // 触发二次加载
      total: 0, // 数据总条数
      isFirstLoading: true, // 首次加载
      defaultAvatar, // 默认头像
      search: false, // 是否搜索
      currentUnit: null, // 当前操作的 Unit
      showUnitAdaptGuide: false, // 是否显示引导
      firstLoadDocument: true,
      downloadUnit: null, // 下载的 Unit
      showSettingUnitGuide: false, // 是否显示引导
      leavePage: false, // 标记是否已离开页面
      initComponents: false // 标记是否已初始化组件
    }
  },
  created () {
    // 获取机构校训
    this.$store.dispatch('getAgencyLearnerProfile')
    // 功能激活事件
    this.$analytics.sendActivityEvent('UNIT_PLANNER', 'ACTIVATE')
    // Magic 检测是否可创建 unit 、检测是否可发布 Magic
    if (this.isMC || this.isCurriculumPlugin) {
      this.$store.dispatch('magicCurriculum/checkCurrentUserToMagic')
    }

    // 添加消息监听器，用于接收外部网页发送的消息
    window.addEventListener('message', this.handleExternalMessage)
  },
  beforeDestroy () {
    // 设置页面已离开标记
    this.leavePage = true
    // 离开页面时，关闭右下角邀请弹窗
    this.closeUnitListShareDialog()
    // 组件销毁前移除消息监听器和滚动监听器
    window.removeEventListener('message', this.handleExternalMessage)
    document.removeEventListener('scroll', this.handleUnitListScroll, true)
  },
  mounted () {
    // 切换页面时访问 Unit Planner
    this.$nextTick(() => {
      this.initializeComponents()
      // CG 平台不显示改编单元
      if (this.isCG) {
        this.filterParams.adaptType = 'UNADAPTED'
      }
      if (this.isCurriculumPlugin) {
        this.filterParams.adaptType = 'ALL'
      }
      // 如果是老师角色且不允许创建，status 设置为已完成，进而触发下面的监视
      if (this.allowCreateUnit !== null && !this.allowCreateUnit) {
        this.filterParams.status = 'COMPLETED'
        this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')

      } else if (this.allowCreateUnit !== null && this.allowCreateUnit) {
        // 获取单元列表
        this.reloadUnits().then(() => {
          // 打开右下角邀请弹窗
          this.showUnitListShareDialog()
        }).catch(() => {
          this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
        })
      } else {
        // 默认也会进行打开右下角邀请弹窗
        this.showUnitListShareDialog()
      }
      // 添加滚动事件监听
      document.addEventListener('scroll', this.handleUnitListScroll, true)
    })
  },
  watch: {
    // 监听过滤条件，重新获取单元列表
    filterParams: {
      deep: true,
      handler () {
        // 如果是老师角色，当选择了 Draft 时，Create By Others 是不可选的
        if (this.isTeacher()) {
          // 如果选择了 Draft，Create By Others 是不可选的
          if (this.filterParams.status.toLowerCase() === this.statusList[2].value.toLowerCase()) {
            this.createByList[2].disabled = true
            // 创建人默认是 Create By Anyone
            // this.filterParams.createBy = this.createByList[0].value
          } else {
            // 否则，Create By Others 是可选的
            this.createByList[2].disabled = false
          }
          // 如果选择了 Create By Others，Draft 状态是不可选的
          if (this.filterParams.createBy.toLowerCase() === this.createByList[2].value.toLowerCase()) {
            this.statusList[2].disabled = true
            // this.filterParams.status = this.statusList[0].value
          } else {
            this.statusList[2].disabled = false
          }
        }
        this.ended = false
        this.getUnitsLoading = 0
        this.isFirstLoading = true
        // 如果是老师角色且不允许创建，并且未输入 keyword
        if (!this.allowCreateUnit && !this.keywords.trim()) {
          this.reloadUnits(false)
        } else {
          this.reloadUnits(true)
        }
        // Magic 检测是否可创建 unit 、检测是否可发布 Magic
        if (this.isMC || this.isCurriculumPlugin) {
          this.$store.dispatch('magicCurriculum/checkCurrentUserToMagic')
          this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
        }
      }
    },
    showSettingUnitGuide: {
      immediate: true,
      deep: true,
      handler (newValue, oldValue) {
        if (newValue) {
          // 处理首次渲染出现右下角
          setTimeout(() => {
            this.$refs.settingUnit &&
            this.$refs.settingUnit.$refs &&
            this.$refs.settingUnit.$refs.settingUnitTips &&
            this.$refs.settingUnit.$refs.settingUnitTips.updatePopper()
          }, 500)
        }
      }
    },
    guideFeatures: {
      deep: true,
      immediate: true,
      handler () {
        if (this.guideFeatures && !this.isCurriculumPlugin) {
          this.$nextTick(() => {
            this.showUnitAdaptGuide = this.isShowAdaptUDLAndCLRGuide
            // 如果当前已经显示了引导，则判断是否需要显示下一个引导
            if (!this.showUnitAdaptGuide && ((!this.isCG || this.isCurriculumPlugin) || this.isAdmin)) {
              this.hideSettingUnitGuideOperate()
            }
          })
        }
      }
    },
    // 监听搜索关键字变化，发送埋点
    'filterParams.keyword' (val) {
      if (val.trim()) {
        this.$analytics.sendEvent('web_unit_click_search')
      }
    },
    allowCreateUnit (newVal, oldVal) {
      // 初始化/刷新页面时查询 Unit Planner
      if (!newVal) {
        // 如果是老师角色，且关闭了创建单元权限，设置过滤条件为 COMPLETED
        this.filterParams.status = 'COMPLETED'
      } else {
        // 获取单元列表
        this.reloadUnits()
      }
    }
  },
  methods: {
    /**
     * 初始化组件
     */
    initializeComponents () {
      this.initComponents = true
    },
    // 点击设置按钮埋点
    handleSettings () {
      this.$analytics.sendEvent('cg_unit_list_setting')
    },
    clickPPTGuide () {
      this.$analytics.sendEvent('cg_unit_list_ppt')
    },
    showVideoGuide () {
      this.$store.commit('magicCurriculum/SET_SHOW_VIDEO_GUIDE', true)
    },
    downloadPDF (fileType, unit) {
      this.downloadUnit = unit
      this.$nextTick(() => {
        this.$refs.unitDownLoad && this.$refs.unitDownLoad.downloadPDF(fileType)
      })
    },
    downloadGoogleDocs (unit) {
      this.downloadUnit = unit
      this.$nextTick(() => {
        this.$refs.unitDownLoad && this.$refs.unitDownLoad.downloadGoogleDocs()
      })
    },
    // 是否是老师角色
    isTeacher,
    // shareLink,

    publishSuccess (unit) {
      this.editUnit = unit
      this.magicSuccessVisible = true
    },
    // 打开新增校训弹窗
    manageLearnerProfile () {
      this.$refs.learnerProfilePopupRef.open()
      this.closeLearnerProfileNew()
    },
    // 关闭校训的 new 字段
    closeLearnerProfileNew () {
      if (this.guideFeatures && this.guideFeatures.showManageLearnerProfileGuideNewTag) {
        let result = { 'features': ['MANAGE_LEARNER_PROFILE_GUIDE_NEW_TAG'] }
        this.$axios.post($api.urls().hideGuide, result).then((res) => {
          // 关闭校训的 new 字段埋点
          this.$analytics.sendEvent('cg_unit_list_setting_graduate_close')
          this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
            ...this.guideFeatures,
            showManageLearnerProfileGuideNewTag: false
          })
        })
      }
    },
    // 重新刷新列表页
    async refreshUnits (search) {
      // 删除操作，页面直接刷新
      // 将页面加载状态设置为初始值
      this.isFirstLoading = true
      this.ended = false
      this.getUnitsLoading = 0
      // 重新加载页面
      this.reloadUnits(search)
    },
    // 单元创建前的判断
    async isUnitCreatable () {
      if (!this.magicAllowCreateUnit && this.isMC) {
        this.$message.warning('You have hit the maximum limit of 3 units for creation.')
        return false
      }
      // 如果是插件平台
      const createdUnits = this.magicCreatedUnits
      // 判断是否有续费权限
      let renewalPermission = this.isCurriculumPlugin && this.magicAllowUnits === 0 && createdUnits.length >= 0
      let inviteMorePermission = this.isCurriculumPlugin && this.magicAllowUnits < 18 && createdUnits.length >= this.magicAllowUnits
      let unlockUnlimitedAccess = this.isCurriculumPlugin && this.magicAllowUnits >= 18 && createdUnits.length >= this.magicAllowUnits
      // 如果没有权限，则调用一次权限列表接口
      if (this.isMC && (renewalPermission || inviteMorePermission || unlockUnlimitedAccess)) {
        this.newUnitLoading = true
        await this.$store.dispatch('magicCurriculum/checkCurrentUserToMagic')
        renewalPermission = this.isCurriculumPlugin && this.magicAllowUnits === 0 && createdUnits.length >= 0
        inviteMorePermission = this.isCurriculumPlugin && this.magicAllowUnits < 18 && createdUnits.length >= this.magicAllowUnits
        unlockUnlimitedAccess = this.isCurriculumPlugin && this.magicAllowUnits >= 18 && createdUnits.length >= this.magicAllowUnits
        this.newUnitLoading = false
      } else if (this.isCurriculumPlugin && (renewalPermission || inviteMorePermission || unlockUnlimitedAccess)) {
        this.newUnitLoading = true
        await this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
        renewalPermission = this.isCurriculumPlugin && this.magicAllowUnits === 0 && createdUnits.length >= 0
        inviteMorePermission = this.isCurriculumPlugin && this.magicAllowUnits < 18 && createdUnits.length >= this.magicAllowUnits
        unlockUnlimitedAccess = this.isCurriculumPlugin && this.magicAllowUnits >= 18 && createdUnits.length >= this.magicAllowUnits
        this.newUnitLoading = false
      }
      if (renewalPermission) {
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'RENEWAL',
          shared: false
        }
        this.$bus.$emit('message', data)
        return false
      } else if (inviteMorePermission) {
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'INVITE_MORE',
          shared: false
        }
        this.$bus.$emit('message', data)
        this.$analytics.sendEvent('cg_unit_list_newunit_two')
        return false
      }
      if (unlockUnlimitedAccess) {
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'UNLOCK_UNLIMITED_ACCESS',
          shared: true
        }
        this.$bus.$emit('message', data)
        this.$analytics.sendEvent('cg_unit_list_newunit_three')
        return false
      }
      return true
    },
    // 跳转到创建页面
    async newUnit () {
      const canCreate = await this.isUnitCreatable()
      if (!canCreate) {
        return
      }
      // 判断 CreatedUnitNum，如果 CreatedUnitNum 为 0 则直接进入 New Unit
      // 点击创建 Unit 单元的埋点
      this.$analytics.sendEvent('cg_unit_list_newunit')
      // 清空已有单元信息
      this.$store.commit('curriculum/RESET_UNIT')
      this.$router.push({
        name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-creator-cg' : 'unitCreator'
      })
    },
    // 跳转到单元分析页
    async newAnalysisUnit () {
      const canCreate = await this.isUnitCreatable()
      if (!canCreate) {
        return
      }
      this.$analytics.sendEvent('cg_adapt_entry_clicked')
      this.$store.commit('curriculum/RESET_ANALYZE_UNIT_CONTENT')
      this.$router.push({
        name: 'import-unit-cg'
      })
    },
    // 隐藏改编引导
    hideAdaptGuideOperate (notShowNext) {
      this.showUnitAdaptGuide = false
      this.isShowAdaptUDLAndCLRGuide = false
      // 发起请求隐藏引导
      let result = { 'features': ['ADAPT_UNIT_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then()
      // 如果还存在其他引导，则显示其他引导
      if (!notShowNext) {
        this.$refs.settingUnit.showSteps = true
        // 关闭 this.guideFeatures 中的引导
        this.hideSettingUnitGuideOperate()
      } else {
        this.hideSettingUnitGuideOperateItem()
      }
    },
    hideSettingUnitGuideOperateItem () {
      // 关闭 this.guideFeatures 中的引导
      this.guideFeatures.showMixedAgeGroupGuide = false
      this.guideFeatures.showLearnerProfileGuide = false
      this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
      // 发起请求隐藏引导
      let result = { 'features': ['LEARNER_PROFILE_GUIDE', 'MIXED_AGE_GROUP_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then()
    },
    hideSettingUnitGuideOperate (notLater, hide, type) {
      // 防止重复触发
      if (typeof notLater !== 'boolean' && !this.showSettingUnitGuide && this.guideFeatures && (this.guideFeatures.showMixedAgeGroupGuide || (this.isAdmin && this.guideFeatures.showLearnerProfileGuide))) {
        // 显示混龄+校训引导
        this.showSettingUnitGuide = true
        this.hideSettingUnitGuideOperateItem()
      }
      if (hide) {
        this.showSettingUnitGuide = false
      }
      if (notLater) {
        if (type) {
          this.$refs.learnerProfilePopupRef &&
          this.$refs.learnerProfilePopupRef.open()
        } else {
          this.$refs.mixedAgeDifferentiation &&
          this.$refs.mixedAgeDifferentiation.showMixedAgeDifferentiation()
        }
      }
    },
    // 重新加载 Unit Planner 数据
    async reloadUnits (search = false) {
      this.isFirstLoading = true
      this.pageNum = 1
      this.units = []
      try {
        // 请求 Unit Planner 数据
        await this.getUnits(search)
      } catch (e) {
        this.isFirstLoading = false
      }
    },

    // 自动搜索 Unit Planner
    autoSearch: tools.debounce(function () {
      this.handleSearch()
    }, 2000),

    // 回车搜索 Unit Planner
    handleSearch () {
      this.filterParams.keyword = this.keywords.trim()
    },

    // 触底加载 Unit Planner 数据
    async getUnits (search) {

      this.search = search
      // 已加载完或正在加载，则不再加载
      if (this.ended || this.getUnitsLoading === 1 || this.firstLoadDocument) {
        this.firstLoadDocument = false
        return 0
      }
      this.getUnitsLoading = 1
      let units = this.units
      // 加载下一分页
      let page
      let error
      let el = this.$refs.units && this.$refs.units.$el || this.$refs.units
      if (units && units.length > 0) {
        this.endScroll = true
      }
      // 请求参数
      try {
        // 如果是第一次加载或者当前加载的数据小于总数据，则加载下一页
        if (this.isFirstLoading || this.units.length < this.total) {
          // 请求 Unit Planner 数据
          const pageNum = this.pageNum
          this.pageNum++
          page = await this.$axios.post($api.urls().listAgencyUnits, {
            ...this.filterParams,
            pageSize: this.pageSize,
            pageNum: pageNum
          })
          this.total = page.total
        } else {
          this.ended = true
        }
      } catch (e) {
        error = e
      }
      // 如果分页被重置，则丢弃本次家加载结果
      if (this.units !== units) {
        this.pageNum--
        return
      }
      this.getUnitsLoading = 2
      // 成功处理
      if (page) {
        // 下架之后返回的是字符串 '0'
        page.items.forEach(unit => {
          unit.isSharedToMagic && (unit.isSharedToMagic = Number(unit.isSharedToMagic))
        })
        // 拼接数据，按照 Id 去重
        this.units = units.concat(page.items.filter(item => !units.find(unit => unit.id === item.id)))
        if (!search) {
          this.$emit('getUnitList', this.units.length)
        }
        this.isFirstLoading = false
        // 获取引导信息
        const firstVisit = await this.getUnitListGuide()
        if (firstVisit) {
          // 定义 guide 对象
          this.setGuide(page)
          // 加载完毕，展示欢迎语弹窗
          this.showWelcomeDialog()
        }
        // 加载完毕，展示导入单元改编弹窗
        this.showImportUnitAdaptDialog()
        // this.$emit('getUnitList', this.units.length)
        this.total = page.total
        return page
      }
      // 失败处理
      this.ended = true
      error && error.message && this.$message.error(error && error.message)
    },
    // 展示欢迎语弹窗
    showWelcomeDialog () {
      // 获取引导信息
      const firstVisit = this.showCurriculumUnitPlannerGuide
      // 如果已经引导过了，则不再显示
      if (!firstVisit) {
        return
      }
      // 获取引导信息
      this.$nextTick(() => {
        this.welcomeDialogOpen = true
      })
    },
    // 展示导入单元改编弹窗
    showImportUnitAdaptDialog () {
      // 判断是否打开开关,若没有打开开关则直接返回
      if (!this.allowCreateImportUnit) {
        return
      }
      // 如果是新用户，则不展示导入单元改编弹窗
      if (this.isImportUnitAdaptNewUser) {
        return
      }
      // 若是老用户则判断是否已经展示过引导
      if (this.guideFeatures && this.guideFeatures.showImportUnitAdaptGuide) {
        // 新功能弹窗埋点
        this.$analytics.sendEvent('cg_adapt_intro_popup_shown')
        // 展示导入单元改编弹窗
        this.importUnitAdaptDialogOpen = true
        // 调接口隐藏引导
        let params = { features: ['IMPORT_UNIT_ADAPT_GUIDE'] }
        this.$axios.post($api.urls().hideGuide, params)
      }
      // 若 this.guideFeatures 不存在，则需要请求接口获取引导信息
      if (!this.guideFeatures) {
        this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
           if (result && result.showImportUnitAdaptGuide) {
              // 新功能弹窗埋点
              this.$analytics.sendEvent('cg_adapt_intro_popup_shown')
               // 展示导入单元改编弹窗
              this.importUnitAdaptDialogOpen = true
              // 调接口隐藏引导
              let params = { features: ['IMPORT_UNIT_ADAPT_GUIDE'] }
              this.$axios.post($api.urls().hideGuide, params)
           }
        })
      }
    },
    // 关闭导入单元改编弹窗
    closeImportUnitAdaptDialog() {
      this.importUnitAdaptDialogOpen = false
      this.$set(this.guideFeatures, 'showImportUnitAdaptGuide', false)
      // 调接口隐藏引导
      let params = { features: ['IMPORT_UNIT_ADAPT_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, params)
    },
    /**
     * 设置引导信息
     */
    setGuide (page) {
      // 获取引导信息
      const firstVisit = this.showCurriculumUnitPlannerGuide
      // 如果引导过了，或者没有 page 或者 page 没有 items 或者 items 为空，则不再显示
      if (this.isCurriculumPlugin || !firstVisit || !page || !page.items || page.items.length === 0) {
        return
      }
      // 定义插入样式表的位置
      const number = 0
      // 创建 style 元素
      const sheet = tools.dynamicAddStyleInDocument(`
            .driver-popover-description {
              padding: 12px 0px 0px 15px;
            }
          `, number)
      // 添加一个需要仅在引导结束后移除的样式表
      let styleSheet
      // 创建引导插件对象
      this.driver = new Driver({
        className: 'guide-style',
        opacity: 0.5, // 屏蔽罩透明度
        allowClose: false,
        closeBtnText: this.$t('loc.planGuideSkip'),
        nextBtnText: this.$t('loc.planGuideNext'),
        doneBtnText: this.$t('loc.planGuideDone'),
        onHighlightStarted: (element) => {
          // 获取 element 的 node
          const node = element.node
          // 分别按照不同的 ID 添加不同的埋点
          if (node.id === 'adapt-unit') {
            // 点击创建 Unit 单元的埋点
            this.$analytics.sendEvent('cg_unit_welcome_onenext')
          }
          // 如果元素的 ID 是 exemplar-unit，需要将 driver-close-btn 元素隐藏
          if (node.id === 'exemplar-unit') {
            // driver-close-btn 元素
            styleSheet = new CSSStyleSheet()
            sheet.insertRule(`
              .driver-close-btn {
                display: none !important;
              }
            `, 0)
            document.adoptedStyleSheets = [...document.adoptedStyleSheets, sheet]
            this.$analytics.sendEvent('cg_unit_welcome_twonext')
          }
        },
        onHighlighted: (element) => {
          // 高亮元素
          // 如果元素的 ID 是 create-new-unit 或者 adapt-unit，需要将 driver-highlighted-element-stage 元素的背景设置为透明
          const highlightedHTMLElement = document.querySelector('#driver-highlighted-element-stage')
          if (highlightedHTMLElement) {
            highlightedHTMLElement.style.background = 'transparent'
          }
        },
        onDeselected: (element) => {
          // 在元素要取消的时候触发
          // 取消选中元素
          document.adoptedStyleSheets = document.adoptedStyleSheets.filter(style => style !== styleSheet)
        },
        // 所有引导步骤执行后后执行的方法
        onReset: (element) => {
          // 获取 element 中的 ID，按照不同的 ID 点击的结束来发送不同的埋点
          const node = element.node
          const elementId = node.id
          if (elementId === 'create-new-unit') {
            // 点击创建 Unit 单元的埋点
            this.$analytics.sendEvent('cg_unit_welcome_oneskip')
          } else if (elementId === 'adapt-unit') {
            // 点击创建 Unit 单元的埋点
            this.$analytics.sendEvent('cg_unit_welcome_twoskip')
          } else if (elementId === 'exemplar-unit') {
            // 点击创建 Unit 单元的埋点
            this.$analytics.sendEvent('cg_unit_welcome_gotit')
          }
          // 如果引导结束，则设置引导过了
          // 设置缓存
          localStorage.setItem('UNIT_PLANNER_GUIDE_FIRST_VISIT' + this.currentUserId, 'true')
          // 隐藏引导过程
          let result = { 'features': ['PLUGIN_UNIT_PLANNER_GUIDE'] }
          this.$axios.post($api.urls().hideGuide, result).then()
          // 解除禁用引导元素鼠标事件
          this.driver.steps.forEach(el => {
            el.node.style.pointerEvents = 'auto'
          })
          // 移除添加的样式表
          tools.dynamicRemoveStyleInDocument(sheet)
        }
      })
    },
    // 获取 Unit 单元引导信息
    getUnitListGuide () {
      return new Promise((resolve, reject) => {
        // 获取用户是否需要引导
        const firstVisit = localStorage.getItem('UNIT_PLANNER_GUIDE_FIRST_VISIT' + this.currentUserId)
        // 如果没有缓存，则请求接口
        if (!firstVisit) {
          this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
            if (result) {
              this.showCurriculumUnitPlannerGuide = result.showCurriculumUnitPlannerGuide
              resolve(result.showCurriculumUnitPlannerGuide)
            }
          }).catch((err) => {
            reject(err)
          })
        } else {
          this.showCurriculumUnitPlannerGuide = false
          resolve(false)
        }
      })
    },
    /**
     * 开始引导
     */
    startGuide () {
      // 获取引导信息
      const firstVisit = this.showCurriculumUnitPlannerGuide
      // 如果引导过了，或者没有 page 或者 page 没有 items 或者 items 为空，则不再显示
      if (!this.isCurriculumPlugin || !firstVisit || !this.units || this.units.length === 0) {
        return
      }
      this.$nextTick(() => {
        this.driver.defineSteps(steps)
        // 开始引导
        this.driver.start()
        // 禁用引导元素鼠标事件
        this.driver.steps.forEach(el => {
          el.node.style.pointerEvents = 'none'
        })
      })
    },
    // 班级人口统计
    manageClassRoomDemographics () {
      // 跳转路由
      let { href } = this.$router.resolve({
        name: 'manageChildren',
        query: {}
      })
      // 打开新标签页
      window.open(href, '_blank')
    },
    // 权限管理
    managePermission () {
      this.$router.push({
        name: 'lessonsSetting'
      })
    },
    showMixedAgeDifferentiation () {
      this.$refs.mixedAgeDifferentiation.showMixedAgeDifferentiation()
      // New 是否显示
      this.triggerNewPointer = true
      // 添加 Unit Planner 设置的埋点
      // session storage 中存储一个数据
      sessionStorage.setItem('showMixedAgeDifferentiation' + this.currentUserId, 'true')
    },
    // 应用到周计划
    applySingleUnit (unit) {
      this.currentUnit = unit
      this.$nextTick(() => {
        this.$refs.unitApply.openDialog()
      })
    },
    adaptSingleUnit (unit) {
      this.$refs.unitAdapt.openAdaptUnitDialog(unit.id)
    },
    // 清除选择的 Unit
    clearCurrentUnit () {
      this.currentUnit = null
    },

    /**
     * 根据是否安装插件决定最大单元数
     * @returns {number} 最大单元数量
     */
    getMaxUnits () {
      // 统一按照 16 来判断
      return 16
    },

    /**
     * 已获得最大单元数量，则主动关闭右下角分享弹窗
     */
    maxSharedUnitsCloseDialog () {
      // 获取最大单元数量
      const maxUnits = this.getMaxUnits()
      // 如果此时达到最大单元数量，则主动关闭右下角分享弹窗
      if (this.magicAllowUnits >= maxUnits) {
        this.closeUnitListShareDialog()
      }
    },

    /**
     * 处理从外部网页接收到的消息
     * @param {MessageEvent} event - 消息事件对象
     */
    handleExternalMessage (event) {
      // 检查消息来源和数据格式
      if (event.data && typeof event.data === 'object') {
        // 如果消息类型是刷新单元数据
        if (event.data.type === 'REFRESH_UNIT_USAGE_DATA') {
          // 调用 checkCurrentUserCreateUnit 方法刷新单元数据
          this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit').then(() => {
            // 已获得最大单元数量，则主动关闭右下角分享弹窗
            this.maxSharedUnitsCloseDialog()
            // 可选：在刷新完成后向父窗口发送确认消息
            if (window.parent !== window) {
              window.parent.postMessage({ type: 'REFRESH_UNIT_USAGE_DATA_COMPLETED' }, '*')
            }
          })
        }
      }
    },
    // 处理滚动事件
    handleUnitListScroll () {
      // 找到 stickyHeader 元素的位置，如果距离顶部小于 140px，则固定
      if (this.$refs.stickyHeader) {
        const headerRect = this.$refs.stickyHeader.getBoundingClientRect()
        this.isSticky = headerRect.top <= -160 // 160px = 220px 欢迎语 - 60px 操作栏
      }
    },

    // 处理点击触发的分享页面
    handleRefer () {
      this.$analytics.sendEvent('cg_unit_list_click_refer&earn')
      // 发送消息到父窗口,触发分享页面
      const data = {
        event: 'GO_TO_SHARE_PAGE',
        type: 'INVITE_MORE',
        shared: false
      }
      this.$bus.$emit('message', data)
    },

    // 列表页的分享弹窗
    async showUnitListShareDialog () {
      // 获取创建 unit 奖励状态
      await this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
      // 判断是否有继续分享增加的权限
      if (!this.isCurriculumPlugin || this.magicCreatedUnits.length < 2 || this.magicAllowUnits >= this.getMaxUnits()) {
        return
      }
      // 延迟3秒显示分享弹窗
      setTimeout(() => {
        // 如果页面已经离开或没有权限打开弹窗，则不执行后续逻辑
        if (this.leavePage) {
          return
        }
        // 发送消息到父窗口,触发分享页面
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'UNIT_LIST_INVITE_MORE',
          shared: false
        }
        this.$bus.$emit('message', data)
      }, 3000)
    },

    // 关闭右下角分享弹窗
    closeUnitListShareDialog () {
      // 发送消息到父窗口,触发分享页面
      const data = {
        event: 'CLOSE_DIALOG',
        type: 'UNIT_LIST_INVITE_MORE',
        shared: false
      }
      this.$bus.$emit('message', data)
    },

  }
}
</script>

<style lang="less" scoped>
.unit-filter-select {
  width: 200px;
}

@media screen and (max-width: 1366px) {

  .unit-filter-select {
    width: 165px;
  }
}

.unit-list {
  max-width: 1200px;
  margin: 0 auto;
}

.btn-style-group {
  .el-button--primary:hover {
  }
}

.btn-style:hover {
  -webkit-box-shadow: 0 0 0 3px #10b3b766;
  box-shadow: 0 0 0 3px #10b3b766;
}

.unit-card {
  margin: 0 8px 24px !important;
}

.unit-list-limit-width {
  max-width: 1200px;
  margin: 0 auto;
}

.settings-style {
  padding: 8px 16px;
  line-height: 24px;
}

.condition-content {
  // position: sticky; // 修复 sticky 布局导致的 z-index 失效问题
  top: 0;
  z-index: 1;
  padding: 16px 0;
  // background: #f5f6f8
}

.sticky-header {
  width: 100%;
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 99;
  background: #FFF;
  transition: all 0.3s linear;
  padding: 8px 0;
}

.sticky-header-banner {
  top: 112px;
}

.magic-question {
  display: inline-flex;
  border: 2px solid #DCDFE6;
  width: auto;
  height: 40px;
  font-weight: 500;
}

.new-unit-box {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  border-radius: 8px;
  height: 200px;
  margin-top: 24px;
  transition: all 0.3s ease-in-out;

  .ai-btn {
    width: 160px;
  }
}

.unit-list-container {
  height: 100%;
  min-height: 100%;
}

.curriculum-button-padding {
  padding: 8px 3px !important;
}

.refer-link {
  font-weight: 700;
  color: #10B3B7;
  margin-left: 4px;
  text-decoration: none;
  cursor: pointer;

  &:hover {
    color: #10B3B7;
    text-decoration: none;
  }
}
</style>
