<template>
  <UnitListSkeleton v-if="loading" :loading="loading"></UnitListSkeleton>
  <div v-else :style="{ 'height': isShowEmptyCard ? 'calc(100% - 24px)' : '100%' }">
    <!-- 白板引导组件 -->
    <EmptyCard :showAddUnitBtn="true" v-if="isShowEmptyCard"/>
    <!-- Unit Planner 列表组件 -->
    <UnitList v-else @getUnitList="getUnitList"/>
  </div>
</template>

<script>
import UnitListSkeleton from '@/views/modules/lesson2/unitPlanner/UnitListSkeleton.vue';
import EmptyCard from './components/editor/EmptyCard.vue'
import UnitList from './UnitList.vue'
import {
  setLocale
} from '@/utils/i18n'
import { setPlatform } from '@/utils/setBaseUrl'
import {mapActions, mapState} from 'vuex'

export default {
  name: 'UnitLayout',
  components: { EmptyCard, UnitList, UnitListSkeleton },
  data () {
    return {
      // loading 状态
      loading: true,
      // callback 初始化状态
      callbackInit: false,
      isShowEmptyCard: false // 是否显示白板引导
    }
  },

  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser, // 当前用户信息
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
    }),
  },

  async created() {
    this.registerListener()
    // 设置先设置 header
    await this.setAuthHeader()
    // Unit 列表曝光埋点
    this.$analytics.sendEvent('web_unit_exposure')
  },

  methods: {
    getUnitList (size) {
      // 判断 Unit 列表是否为空, 如果为空，则显示白板引导，否则正常显示列表页
      if (size === 0) {
        // 白板引导曝光埋点
        this.$analytics.sendEvent('web_unit_white_board_exposure')
        this.isShowEmptyCard = true
      } else {
        this.isShowEmptyCard = false
      }
    },
    registerListener() {
      // 获取 this
      const that = this
      window.addEventListener('message', evt => {
        if (evt) {
          // 插件状态变化
          if (evt.data && evt.data.event === 'PLUGIN_STATUS_CHANGE') {
            localStorage.setItem('isInstalled', evt.data.isInstalled)
          }
          // 跳转 lesson-plan
          if (evt.data && evt.data.event === 'PAGE_CHANGE') {
            const path = evt.data.path
            console.log('evt.data', path)
            if (path === 'lesson-plan') {
              console.log('lesson-plan')
              that.$router.push({
                path: '/lessons/lesson-library/my-lessons'
              })
            }
            if (path === 'unit-planner') {
              console.log('unit-planner')
              that.$router.push({
                path: '/curriculum-genie/unit-planner'
              })
            }
          }
        }
      }, false)
    },
    ...mapActions([
      // 获取 event 信息
      'getEventNotifyAction',
    ]),
    async setAuthHeader() {
      // 获取 token
      const token = this.$route.query.t
      // 获取语言
      let language = this.$route.query.lang
      // 获取从那个地方嵌入的
      const agent = this.$route.query.agent
      // 获取当前项目
      const project = this.$route.query.project
      // 获取时区
      const timezone = this.$route.query.timezone
      // 如果没有 token，直接返回
      if (!token) {
        // loading 设置 false
        this.loading = false
        return
      }
      // 如果没有语言，则默认为 en-US
      if (!language) {
        language = 'en-US'
      }
      // 如果语言是 en，则转换为 en-US
      if (language === 'en') {
        language = 'en-US'
      }
      // 如果语言是 zh，则转换为 zh-CN
      if (language === 'zh') {
        language = 'zh-CN'
      }
      // 如果语言是 es，则转换为 es-ES
      if (language === 'es') {
        language = 'es-ES'
      }
      // 设置平台
      if (project) {
        setPlatform(project)
        await this.$store.dispatch('curriculum/setIsCurriculumPlugin', project)
      }
      // 设置语言
      setLocale(language)
      try {
        // 如果 callback 初始化状态为 true，则直接返回
        if (this.callbackInit) {
          return
        }
        // 检测第三方绑定 token 的有效性
        const tokenResponse = await this.checkThirdPartyToken(this.$route.query.code, token, this.$route.query.agent, timezone);
        // 如果没有返回结果则直接跳过
        if (!tokenResponse) {
          // loading 设置 false
          this.loading = false
          return
        }
        // callback 初始化状态设置为 true
        this.callbackInit = true
        // 保存用户信息
        await this.saveUserInfo(tokenResponse, agent)
        // 初始化埋点
        this.$analytics.initPostHog(this.currentUser)
        // 获取 event 信息
        await this.getEventInfo(project)
        // loading 设置 false
        this.loading = false
        // 添加埋点
        this.$analytics.sendEvent('cg_unit_unpack')
      } catch (e) {
        // loading 设置 false
        this.loading = false
      }
    },
    /**
     * 获取 event 信息
     * @param project 当前项目，如果不是 CURRICULUM-PLUGIN 则直接返回
     */
    getEventInfo(project) {
      if (project !== 'CURRICULUM-PLUGIN') {
        return
      }
      // 获取 event 信息
      this.getEventNotifyAction().then(res => {
        // 获取功能引导
        this.$store.dispatch('getUserNeedGuideFeaturesAction')
      })
    },
    // 检测第三方绑定token的有效性
    checkThirdPartyToken(code, token, type, timezone) {
      // 发送消息给父级页面
      this.$bus.$emit('message', 'LOADED')
      // 验证 curriculum plugin id-token 的有效性
      return this.$axios.get($api.urls().checkThirdLoginToken +
        '?code=' + code +
        '&token=' + token +
        '&agent=' + type +
        '&isLoggedIn=' + false +
        '&timezone=' + timezone || 'American/Los_Angeles'
      )
    },
    // 保存用户信息
    async saveUserInfo(res, type) {
      if (res && res.loginResponse) {
        // 存储 token、uid、centerid
        this.$store.commit('SET_TOKEN', res.loginResponse.token)
        this.$store.commit('SET_UID', res.loginResponse.user_id)
        this.$store.commit('SET_CENTERID', res.loginResponse.default_center_id)
        // 存储用户信息
        await this.$store.dispatch('setCurrentUserAction', res.loginResponse)
      }
      await this.$store.dispatch('magicCurriculum/setRefreshMagicData', true)
      if (this.updateLoading) {
        this.updateLoading()
      }
    }
  }

  ,
  watch: {
    // '$route': {
    //   handler(to) {
    //     // 监听路由变化
    //     if (to.path.includes('/curriculum-genie/unit-planner')) {
    //       // 发送消息给父级页面,需要切换到 unit planner
    //       this.$bus.$emit('message', {
    //         event: 'ROUTE_CHANGE',
    //         path: '/unit-planner'
    //       })
    //     } else if (to.path.includes('/lessons/lesson-library')) {
    //       // 发送消息给父级页面,需要切换到 lesson planner 
    //       this.$bus.$emit('message', {
    //         event: 'ROUTE_CHANGE', 
    //         path: '/lesson-plan'
    //       })
    //     }
    //   },
    //   immediate: true
    // }
  }
}
</script>

<style scoped lang="less">

</style>
