<template>
  <div>   
    <lesson-library v-if="tabName===tabNames[1]" v-bind="submoduleProps"/>    
  </div>
</template>
<script>
import { mapState } from 'vuex'
import {acrossRole, equalsIgnoreCase} from '@/utils/common'
import tools from '@/utils/tools'
import LessonLibrary from './lessonLibrary'

export default {
  name: 'Lesson2',
  props: ['submoduleName', 'submoduleProps'],
  components: {
    LessonLibrary    
  },
  data () {
    return {
      tabName: this.submoduleName || 'LessonLibrary',
      tabNames: ['Curriculum', 'LessonLibrary', 'DataReview', 'LessonPlan', 'LessonStandard']
    }
  },

  created () {
    // 进入到Lesson Library页面
    this.$analytics.sendEvent('web_lessonlibrary_exposure_enter')
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      lessonOpen () {
        return acrossRole(
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
          ) &&
          this.open && this.open.lesson2Open
      },
      planOpen () {
        return acrossRole(
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
          ) &&
          this.open && this.open.planOpen
      },
      curriculumOpen () {
        return acrossRole(
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
          ) &&
          this.open && this.open.curriculumOpen && this.open.planOpen
      }
    }),
    isTA () {
      let role = this.currentUser.role2
      return role && role.toLowerCase() === 'family_service'
    },
    isFromIpad () {
      return tools.isComeFromIPad()
    }
  },

  watch: {
    submoduleName () {
      this.tabName = this.submoduleName || 'LessonLibrary'
    },
    // 监听tab变化，进行路由跳转
    tabName (newVal, oldVal) {
      if (newVal === 'LessonLibrary') {
        this.LessonLibrary()
      } else if (newVal === 'DataReview') {
        this.dataReview()
      } else if (newVal === 'LessonPlan') {
        this.weekPlan()
      } else if (newVal === 'LessonStandard') {
        this.LessonStandard()
      } else if (newVal === 'Curriculum') {
        this.lessonCurriculum()
      }
    }
  },
  methods: {
    equalsIgnoreCase,
    LessonLibrary () {
      this.$router.push({ name: 'PublicLessonList' })
    },
    dataReview () {
      this.$router.push({ name: 'classOverview' })
    },
    weekPlan () {
      this.$router.push({ name: 'list-plan' })
    },
    LessonStandard () {
      this.$router.push({ name: 'Lesson-Standard' })
    },
    lessonCurriculum () {
      this.$router.push({ name: 'lessonCurriculum' })
    }
  }
}
</script>
<style lang="less" scoped>

  @media screen and (max-width:1199px) {
    .lesson2 /deep/ & {
      overflow: unset;
      scrollbar-gutter: stable;
      & > .el-tabs__header {
        height: 55px;
        line-height: 55px;
        margin-bottom: 0;
        font-weight: 500;

        .el-tabs__item {
          padding: 0 6px;
          height: 55px;
        }

        .el-button {
          padding: 8px 20px;
          font-size: 16px;
        }

        .el-button--text {
          color: #131313;
          padding-left: 15px;
          padding-right: 15px;
        }

        .el-tabs__nav-wrap::after {
          display: none;
        }

        .el-tabs__item.is-active {
          border-bottom-width: 0;
          color: white;
        }

        .el-tabs__nav {
          float: none;
          text-align: center;
        }
      }

      & > .el-tabs__content {
        height: calc(100% - 52px);

        & > :first-child, & > :nth-child(2), & > :last-child {
          height: 100%;
        }
      }
    }
  }
  @media screen and (min-width:1200px) {
    .lesson2 /deep/ & {
      overflow: unset;
      scrollbar-gutter: stable;

      & > .el-tabs__header {
        height: 55px;
        line-height: 55px;
        margin-bottom: 0;
        font-weight: 500;

        .el-tabs__item {
          height: 55px;
        }

        .el-button {
          padding: 8px 20px;
          font-size: 16px;
        }

        .el-button--text {
          color: #131313;
          padding-left: 15px;
          padding-right: 15px;
        }

        .el-tabs__nav-wrap::after {
          display: none;
        }

        .el-tabs__item.is-active {
          border-bottom-width: 0;
          color: white;
        }

        .el-tabs__nav {
          float: none;
          text-align: center;
        }
      }

      & > .el-tabs__content {
        height: calc(100% - 52px);

        & > :first-child, & > :nth-child(2), & > :last-child{
          height: 100%;
        }
      }
    }
  }

</style>
