<template>
  <router-view></router-view>
</template>

<script>
import awsS3Service from '@/utils/awsS3Service'
import { mapActions, mapState } from 'vuex'
import tools from '@/utils/tools'
import { equalsIgnoreCase } from '@/utils/common'

export default {
  name: 'LessonRouterView',

  computed: {
    ...mapState({
      open: state => state.common.open,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      currentPluginUser: state => state.cgAuth.user, // 当前插件用户
      currentUser: (state) => state.user.currentUser, // 当前用户
      magicCreatedUnits: state => state.magicCurriculum.magicCreatedUnits, // 已创建 unit
      magicAllowUnits: state => state.magicCurriculum.magicAllowUnits // 允许创建的 unit 数量
    })
  },

  mounted () {
    this.init()
    // 初始化监听
    this.initListener()
    // 获取当前插件用户信息
    this.getCurrentPluginUserInfo()
    // 初始化邮件进入平台携带参数
    this.initEmailSkipParam()
  },

  methods: {
    ...mapActions([
      'getEventNotifyAction'
    ]),
    // 初始化
    init () {
      // 视频调用初始化
      // 如果是插件，则跳过
      if (this.isCurriculumPlugin) {
        return
      }
      this.getEventNotifyAction().then(res => {
        const data = this.open
        // 初始化 s3Service
        if (data.awsIdentityPoolConfig) {
          awsS3Service.init(data.awsIdentityPoolConfig.bucketRegion, data.awsIdentityPoolConfig.bucketName, data.awsIdentityPoolConfig.identityPoolId)
        }
      })
    },

    /**
     * 初始化邮件进入平台携带参数
     */
    async initEmailSkipParam() {
      // 获取邮箱跳转到平台携带的参数
      let params = null
      // 获取路由参数
      const query = this.$route.query
      const queryStorage = localStorage.getItem('TO_CG_DATA')
      const queryData = queryStorage ? JSON.parse(queryStorage) : {}

      // 合并参数，优先使用路由参数，其次使用本地缓存
      const source = (query.userId && (query.toCreate || query.unitId)) ? query :
        (queryData.userId && (queryData.toCreate || queryData.unitId)) ? queryData : null
      if (source) {
        params = {
          toCreate: source.toCreate,
          unitId: source.unitId,
          userId: source.userId,
          p: source.p,
          time: Date.now()
        }
      }

      // 判断是否是当前用户，判断是否在 10 分钟内点击邮件链接进入的
      if (params && equalsIgnoreCase(params.userId, this.currentUser.user_id) && new Date().getTime() - params.time < 10 * 60 * 1000) {
        // 如果包含单元 ID 则跳转到单元编辑页面
        if (params.unitId) {
          if (params.p === '1' || !params.p) {
            this.$analytics.sendEvent('cg_unit_complete_email_click_finish')
          } else if (params.p === '2') {
            this.$analytics.sendEvent('cg_unit_complete_email_click_finish_v2')
          } else if (params.p === '3') {
            this.$analytics.sendEvent('cg_unit_complete_email_click_finish_v3')
          }
          await this.$router.push({
            name: 'load-unit-cg',
            params: {
              unitId: params.unitId,
              completeToOverview: true
            }
          })
        } else if (params.toCreate) {
          // 判断创建的课程是否达到限制
          await this.$store.dispatch('magicCurriculum/checkCurrentUserCreateUnit')
          if (this.magicCreatedUnits.length < this.magicAllowUnits) {
            if (params.p === '1' || !params.p) {
              this.$analytics.sendEvent('cg_unit_create_email_click_create')
            } else if (params.p === '2') {
              this.$analytics.sendEvent('cg_unit_create_email_click_create_v2')
            } else if (params.p === '3') {
              this.$analytics.sendEvent('cg_unit_create_email_click_create_v3')
            }
            // 跳转到单元创建页
            await this.$router.push({
              name: 'unit-creator-cg'
            })
          }
        }
        // 删除邮箱跳转到平台携带的参数
        localStorage.removeItem('TO_CG_DATA')
      }
    },

    // 获取当前插件用户信息
    getCurrentPluginUserInfo() {
      window.parent.postMessage({event: 'GET_CURRENT_PLUGIN_USER_INFO'}, '*')
    },

    // 处理消息事件
    handleMessage(event) {
      if (event && event.data) {
        // 事件类型
        const eventType = event.data.event
        const data = event.data.data
        switch (eventType) {
          case 'UPDATE_CG_PLUGIN_USER_INFO':
            // 是否初始化
            const isInit = event.data.isInit
            const userInfo = JSON.parse(data)
            // 更新用户昵称
            if (this.currentPluginUser && userInfo.user_name && this.currentPluginUser.user_name !== userInfo.user_name && !isInit) {
              this.updateDisplayName(userInfo.user_name)
            }
            // 保存插件用户信息
            this.$store.commit('SET_CURRENTUSER_PLUGIN', userInfo)
            break
          default:
            break
        }
      }
    },

    // 初始化监听
    initListener() {
      window.addEventListener('message', this.handleMessage)
    },

    // 更新用户昵称
    updateDisplayName: tools.debounce(function (displayName) {
      this.$axios.put($api.urls().updateDisplayName + '?displayName=' + displayName)
    }, 500),
  },

  beforeDestroy() {
    // 注销事件监听器
    window.removeEventListener('message', this.handleMessage)
  }
}
</script>
