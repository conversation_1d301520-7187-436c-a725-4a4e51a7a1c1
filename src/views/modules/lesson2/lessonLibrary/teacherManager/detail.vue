<template>
  <div class="scrollbar">
    <lesson-detail ref="lessonDetail" @mountedAfter="lessonDetailMountedAfter" :isFromLibrary="isFromLibrary" :lesson-id="lessonId">
      <template slot="header-left" slot-scope="{lesson}">
        <lesson-read-count :count="lesson.readCount"/>
        <!-- <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
        <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
      </template>

      <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
        <!--删除-->
        <i class="el-icon-delete" style="cursor: pointer" v-if="isAdmin() && isOwnerLesson(lesson)"
           @click="lessonDeleteClickHandler(lesson)"></i>
        <lesson-replicate :lesson-id="lesson.id"/>
        <!--编辑-->
        <el-button icon="el-icon-edit" size="small" type="primary" plain v-if="isAdmin() && isOwnerLesson(lesson)"
                   @click="lessonEditClickHandler(lesson)">
          {{ $t('loc.edit') }}
        </el-button>
        <lesson-template-select-modal
          v-if="showLessonTemplate(lesson.ages, lesson.activityType)"
          :lessonAges="lesson.ages"
          style="width: auto;"
          buttonSize="small"
          :inDialog="true"
          :showGuidePopover="true"
          v-model="lesson.templateType"
          :lessonId="lesson.id"
          :isMyLesson="isOwnerLesson(lesson)"
          redirectRoute="EditLesson"
        />
        <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                         :lesson="lesson"/>
      </template>
    </lesson-detail>
  </div>
</template>

<script>
// import LessonLike from '../components/LessonLike'
// import LessonFavorite from '../components/LessonFavorite'
import LessonDownload from '../components/LessonDownload'
import LessonDetail from '../components/LessonDetail'
import LessonReadCount from '../components/LessonReadCount'
import LessonReplicate from '../components/LessonReplicate'
import { mapState } from 'vuex'
import Api from "@/api/lessons2";
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import tools from '@/utils/tools'

export default {
  name: 'ManageTeacherLessonDetail',
  components: {
    LessonReadCount,
    LessonDetail,
    // LessonLike,
    // LessonFavorite,
    LessonDownload,
    LessonReplicate,
    LessonTemplateSelectModal
  },
  data () {
    return {
      isFromLibrary: true,
        isUseAdaptedUDLAndCLR: true, // 是否已经启用了 Adapt UDL and CLR
    }
  },
  props: ['lessonId'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      educator: state => (!state.common.open || state.common.open.educator), // 是否是 educator 机构
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
    }),
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    }
  },
  methods: {
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
      /**
       * 课程内容渲染后来自子组件的回调，用以判断是否使用了 Adapt UDL and CLR
       */
      lessonDetailMountedAfter() {
          if (this.$refs.lessonDetail) {
              // 判断是否启用了 UDL 和 CLR
              this.isUseAdaptedUDLAndCLR = this.$refs.lessonDetail.isUseAdaptedUDLAndCLR;
          }
      },
    // 编辑课程
    lessonEditClickHandler (lesson) {
      this.$analytics.sendEvent('web_lesson_library_my_less_eidt')
      // 进入编辑页面
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: lesson.id,
          type: 'Draft'
        }
      })
    },
    // 删除课程
    lessonDeleteClickHandler (lesson) {
      const h = this.$createElement
      // 弹出对话框
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.lessons2DeleteTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        // 触发回调函数
        beforeClose: (action, instance, done) => {
          // 如果点击 confirm
          if (action === 'confirm') {
            done()
            Api.deleteLesson(lesson.id).then(() => {
              if (this.educator) {
                this.$router.push({ name: 'TeacherLessonList' })
              }
            })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },
    // 判断是否是自己的课程
    isOwnerLesson (lesson) {
      return (lesson.createUserId === this.currentUser.user_id)
    },
  }
}
</script>

<style scoped lang="less">
.lesson-detail /deep/ & {
  background-color: #fff;
  padding: 0 50px;
  width: 1150px;
  margin: 24px auto;

  & > :first-child {
    line-height: 54px;
  }

  & > .lesson-detail__content {
    padding-bottom: 60px;
  }
}
</style>
