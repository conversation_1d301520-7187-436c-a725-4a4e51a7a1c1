<template>
  <div>
    <!--引入图标-->
    <link rel="stylesheet" type="text/css" href="//at.alicdn.com/t/font_2980060_f9r2y79jtww.css">

    <!--课程列表-->
    <lesson-list :params="params" :loader="getManageTeacherLessons" ref="lessonList" :key="params.userId">
      <template slot="header" slot-scope="{total}">
        <div style="display: flex;justify-content: space-between;align-items: center;">
          <!--课程类型-->
          <div>
            <slot name="header-left"/>
          </div>
          <div style="display: flex;align-items: center;gap: 24px;">
            <!-- 课程总数 -->
            <div>
              <span style="font-weight: bold">
                <span class="font-weight-400"> {{ $t('loc.totalEntries') }}: </span>{{ total }} 
              </span>
            </div>
            <div>
              <slot name="header-right" />
            </div>
          </div>
        </div>
      </template>
      <template slot-scope="{lesson}">
        <lesson-card :lesson="lesson" @click.native="lessonClickedHandler(lesson)">
        </lesson-card>
      </template>
      <template slot="footer" slot-scope="{empty}">
        <lesson-empty v-if="empty" :tip="$t('loc.lessons2NoResult')"/>
      </template>
    </lesson-list>

    <!--模态窗口展示详情页-->
    <lesson-detail-dialog new-tab-to="ManageTeacherLessonDetail" :lesson-id="lessonId" :show.sync="showDetail">
      <!--详情页-->
      <lesson-detail ref="lessonDetail" @mountedAfter="lessonDetailMountedAfter" :isFromLibrary="isFromLibrary" :lessonId="lessonId" :isDialog="true">
        <template slot="header-left" slot-scope="{lesson}">
          <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/>
          <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
          <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
        </template>
        <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
          <!--删除按钮-->
          <el-tooltip :content="$t('loc.lessons2DeleteLesson')"
            placement="top" effect="dark" v-if="isAdmin() && isOwnerLesson(lesson)">
            <i class="el-icon-delete" style="cursor: pointer" @click="lessonDeleteClickHandler(lesson)"></i>
          </el-tooltip>
          <!-- 已改编的课程或者管理员不显示推荐课程到机构课程按钮 -->
          <lesson-promote v-if="!params.isAdmin && !lesson.isAdaptedLesson" :lesson-id="lesson.id" :lesson-author-id="lesson.createUserId" @reloadLessons="reloadLessons"/>
          <lesson-replicate :lesson-id="lesson.id"/>
          <el-button icon="el-icon-edit" type="primary" size="small" plain
                     @click="lessonEditClickHandler(lesson)" v-if="isAdmin() && isOwnerLesson(lesson)">
            {{ $t('loc.edit') }}

          </el-button>
          <lesson-template-select-modal
            v-if="showLessonTemplate(lesson.ages, lesson.activityType)"
            :lessonAges="lesson.ages"
            style="width: auto;"
            buttonSize="small"
            :inDialog="true"
            :showGuidePopover="true"
            v-model="lesson.templateType"
            :lessonId="lesson.id"
            :isMyLesson="isOwnerLesson(lesson)"
            redirectRoute="EditLesson"
          />
          <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                           :lesson="lesson"/>
        </template>
      </lesson-detail>
    </lesson-detail-dialog>
  </div>
</template>
<script>
import LessonList from '../../component/lessonList'
import LessonCard from '@/views/modules/lesson2/lessonLibrary/components/LessonCard'
import Api from '../../../../../api/lessons2/index'
import NewTab from '../components/NewTab'
// import LessonReadCount from '../components/LessonReadCount'
// import LessonLike from '../components/LessonLike'
import LessonPromote from '../components/LessonPromote'
// import LessonFavorite from '../components/LessonFavorite'
import LessonDownload from '../components/LessonDownload'
import LessonReplicate from '../components/LessonReplicate'
import LessonDetail from '../components/LessonDetail'
import { mapState } from 'vuex'
import LessonDetailDialog from '../components/LessonDetailDialog'
import LessonEmpty from '../components/LessonEmpty'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import tools from '@/utils/tools'

export default {
  name: 'ManagedTeacherLessonList',
  components: {
    LessonDetailDialog,
    LessonDetail,
    LessonReplicate,
    LessonDownload,
    // LessonFavorite,
    // LessonLike,
    LessonPromote,
    // LessonReadCount,
    NewTab,
    LessonList,
    LessonCard,
    LessonEmpty,
    LessonTemplateSelectModal
  },
  props: ['params'],
  data () {
    return {
      showDetail: false, // 详情页弹窗的开关,
      lessonId: '', // 课程Id
      lesson: null, //  课程对象
      dialogKey: 0,
      isFromLibrary: true,
        isUseAdaptedUDLAndCLR: true, // 是否已经启用了 Adapt UDL and CLR
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
    }),
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    }
  },
  methods: {

    /**
     * 刷新课程列表
     */
    reloadLessons () {
      this.$refs.lessonList.reload()
    },

    // 判断是否是管理员
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    // 判断是否是自己的课程
    isOwnerLesson (lesson) {
      return (lesson.createUserId === this.currentUser.user_id)
    },
    // 查询教师端管理课程
    async getManageTeacherLessons (param) {
      // 如果没有传userId，直接返回
      if (!param.userId) {
        return {}
      }
      return Api.getManageTeacherLessons(param)
    },
    // 打开课程详情的窗口
    lessonClickedHandler (lesson) {
      window.lessonVideo && window.lessonVideo.pause()
      this.lessonId = lesson.id
      this.showDetail = true
      this.dialogKey++
    },
      /**
       * 课程内容渲染后来自子组件的回调，用以判断是否使用了 Adapt UDL and CLR
       */
      lessonDetailMountedAfter() {
          if (this.$refs.lessonDetail) {
              // 判断是否启用了 UDL 和 CLR
              this.isUseAdaptedUDLAndCLR = this.$refs.lessonDetail.isUseAdaptedUDLAndCLR;
          }
      },
    // 编辑课程
    lessonEditClickHandler (lesson) {
      this.$analytics.sendEvent('web_lesson_library_my_less_eidt')
      // 进入编辑页面
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: lesson.id,
          type: 'Draft'
        }
      })
    },
    // 删除课程
    lessonDeleteClickHandler (lesson) {
      const h = this.$createElement
      // 弹出对话框
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.lessons2DeleteTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        // 触发回调函数
        beforeClose: (action, instance, done) => {
          // 如果点击 confirm
          if (action === 'confirm') {
            done()
            Api.deleteLesson(lesson.id).then(() => {
              this.reloadLessons()
              this.showDetail = false
            })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },
  },
  beforeDestroy () {
    this.params.userId = null
  }
}
</script>
