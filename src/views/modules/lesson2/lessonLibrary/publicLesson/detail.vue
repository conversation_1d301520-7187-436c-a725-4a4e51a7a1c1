<template>
  <div class="scrollbar">
    <lesson-detail :isFromLibrary="isFromLibrary" :lesson-id="lessonId" :hideReflection=true :isDialog="false">
      <template slot="header-left" slot-scope="{lesson}">
        <el-button @click="this.$router.back();" v-if="false">
          {{ $t('loc.lessonLibraryTabName1') }}
        </el-button>
        <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/>
        <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
        <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
      </template>
      <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}" v-if="showOperation">
        <lesson-template-select-modal
          v-if="showLessonTemplate(lesson.ages, lesson.activityType)"
          :lessonAges="lesson.ages"
          style="width: auto;"
          buttonSize="small"
          :inDialog="true"
          :showGuidePopover="true"
          v-model="lesson.templateType"
          :lessonId="lesson.id"
          :isMyLesson="isOwnerLesson(lesson)"
          redirectRoute="EditLesson"
        />
        <lesson-replicate :lesson-id="lesson.id"/>
        <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                         :lesson="lesson"/>
      </template>
    </lesson-detail>
  </div>
</template>

<script>
// import LessonLike from '../components/LessonLike'
// import LessonFavorite from '../components/LessonFavorite'
import LessonDownload from '../components/LessonDownload'
import LessonDetail from '../components/LessonDetail'
// import LessonReadCount from '../components/LessonReadCount'
import { mapState } from 'vuex'
import tools from '@/utils/tools'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import LessonReplicate from '../components/LessonReplicate'

export default {
  name: 'PublicLessonDetail',
  components: {
    LessonReplicate,
    // LessonReadCount,
    LessonDetail,
    // LessonLike,
    // LessonFavorite,
    LessonDownload,
    LessonTemplateSelectModal
  },
  props: ['lessonId'],
  data () {
    return {
      showOperation: true,
      isFromLibrary: true
    }
  },
  created () {
    let from = this.$route.query.from
    if (from && from === 'NOTE') {
      this.showOperation = false
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
    }),
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    }
  },
  methods: {
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    // 判断是否是自己的课程
    isOwnerLesson (lesson) {
      return (lesson.createUserId === this.currentUser.user_id)
    }
  }
}
</script>

<style scoped lang="less">
.lesson-detail /deep/ & {
  background-color: #fff;
  padding: 0 50px;
  width: 1150px;
  margin: 24px auto;

  & > :first-child {
    height: 54px;
  }

  & > .lesson-detail__content {
    padding-bottom: 60px;
  }
}
</style>
