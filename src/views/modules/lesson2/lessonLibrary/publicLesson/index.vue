<template>
  <div>
    <!--课程列表-->
    <lesson-list :params="params" :loader="getPublicLessons" ref="lessonList">
      <template slot="header" slot-scope="{total}">
        <div style="display: flex;justify-content: space-between;align-items: center;">
        <!-- 过滤器 -->
        <!-- <filter-controller-button
          v-if="!isCurriculumPlugin"
          :show-filter="showFilter"
          :params="params"
          @toggle-filter="$emit('toggle-filter')"
        /> -->
        <div style="display: flex;align-items: center;gap: 24px;">
          <!-- 课程总数 -->
          <div>
            <span style="font-weight: bold"><span class="font-weight-400">{{ $t('loc.totalEntries') }}: </span>{{ total }}</span>
          </div>
          <div>
            <slot name="header-right"/>
          </div>
        </div>
      </div>
      </template>
      <template slot-scope="{lesson}">
        <lesson-item :lesson="lesson" @click.native="lessonClickedHandler(lesson)"/>
      </template>
      <template slot="footer" slot-scope="{empty}">
        <lesson-empty v-if="empty" :tip="$t('loc.lessons2NoResult')"/>
      </template>
    </lesson-list>

    <!--模态窗口展示详情页-->
    <lesson-detail-dialog :lesson-id="lessonId" :show.sync="showDetail" new-tab-to="PublicLessonDetail">
      <!--详情页-->
      <lesson-detail :isFromLibrary="isFromLibrary" :lessonId="lessonId" :hideReflection=true :isDialog="true">
        <template slot="header-left" slot-scope="{lesson}">
          <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/>
          <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"
                      @update:count="value=>handleUpdate(lesson,'likeCount',value)" />
          <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"
                      @update:count="value=>handleUpdate(lesson,'favoriteCount',value)" /> -->
        </template>

        <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
          <lesson-replicate :lesson-id="lesson.id"/>
          <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                           :lesson="lesson"/>

        </template>

        <!--课程详情弹窗提示-->
        <template slot="detail-tips">
          <lesson-detail-tips/>
        </template>
      </lesson-detail>
    </lesson-detail-dialog>
  </div>
</template>
<script>
import LessonItem from '@/views/modules/lesson2/lessonLibrary/components/LessonCard'
import { mapState } from 'vuex'
import Api from '../../../../../api/lessons2/index'
import LessonList from '../../component/lessonList'
// import FilterControllerButton from '../components/FilterControllerButton.vue'
import LessonDetail from '../components/LessonDetail'
import LessonDetailDialog from '../components/LessonDetailDialog'
import LessonDetailTips from '../components/LessonDetailTips'
import LessonDownload from '../components/LessonDownload'
import LessonEmpty from '../components/LessonEmpty'
// import LessonFavorite from '../components/LessonFavorite'
// import LessonLike from '../components/LessonLike'
// import LessonReadCount from '../components/LessonReadCount'
import LessonReplicate from '../components/LessonReplicate'

export default {
  name: 'PublicLessonList',
  components: {
    LessonEmpty,
    LessonDetailDialog,
    LessonDetailTips,
    LessonReplicate,
    LessonList,
    LessonItem,
    // LessonReadCount,
    LessonDetail,
    // LessonLike,
    // LessonFavorite,
    LessonDownload,
    // FilterControllerButton
  },
  props: ['params', 'orderOptions', 'currentTag', 'showFilter'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
    })
  },
  data () {
    return {
      showDetail: false, // 详情页弹窗的开关
      lessonId: '', // 弹框课程Id,
      cardLesson: null,
      isFromLibrary: true
    }
  },
  methods: {
    // 查询所有公共课程
    getPublicLessons (param) {
      return Api.getPublicLessons(param)
    },
    // 打开课程详情的窗口
    lessonClickedHandler (lesson) {
      if (this.currentTag === 'PublicLessonList') {
        this.$analytics.sendEvent('web_lesson_library_public_click_crad')
      }
      this.lessonId = lesson.id
      this.showDetail = true
      this.cardLesson = lesson
      window.lessonVideo && window.lessonVideo.pause()
    },
    handleUpdate (detailLesson, attr, value) {
      if (attr === 'likeCount') {
        Object.assign(this.cardLesson,value)
      }
      if (attr === 'favoriteCount') {
        Object.assign(this.cardLesson,value)
      }
    }
  }
}
</script>
