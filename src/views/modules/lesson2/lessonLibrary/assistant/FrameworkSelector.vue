<template>
  <el-form-item :prop="prop">
    <template #label v-if="showLabel">
      <label>
        <span class="required-star font-size-16 font-weight-600">* </span>
        <span class="font-size-16 font-weight-600">{{ label || $t('loc.unitPlannerStep1AssessmentFramework') }}</span>
      </label>
    </template>
    <!-- 级联选择器，展示州和框架数据 -->
    <el-cascader
      ref="cascaderRef"
      :key="selectedGrade"
      v-model="selectedValue"
      :options="cascaderData"
      :props="cascaderProps"
      :show-all-levels="showAllLevels"
      @change="handleChange"
      :class="cascaderClass"
      :loading="combinedLoading"
      :placeholder="computedPlaceholder"
      :style="cascaderStyle"
      :append-to-body="false"
      :popper-class="popperClass">
    </el-cascader>
  </el-form-item>
</template>

<script>
import tools from '@/utils/tools'
import { equalsIgnoreCase } from '@/utils/common'
import { mapState } from 'vuex'

export default {
  name: 'FrameworkSelector',

  props: {
    // 绑定值
    value: {
      type: Array,
      default: () => []
    },
    // 当前选中的年级
    selectedGrade: {
      type: String,
      required: false,
      default: ''
    },
    // 标签文本
    label: {
      type: String,
      default: ''
    },
    // 是否显示标签
    showLabel: {
      type: Boolean,
      default: true
    },
    // 表单字段名
    prop: {
      type: String,
      default: 'framework'
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: ''
    },
    // 是否显示完整路径
    showAllLevels: {
      type: Boolean,
      default: false
    },
    // 级联选择器样式类
    cascaderClass: {
      type: String,
      default: 'w-full'
    },
    // 级联选择器内联样式
    cascaderStyle: {
      type: [String, Object],
      default: 'line-height: unset;'
    },
    // 下拉框样式类
    popperClass: {
      type: String,
      default: 'framework-selector-cascader'
    },
    // 来源标识
    source: {
      type: String,
      default: ''
    },
    isDataBackFill: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      selectedValue: this.value ? [...this.value] : [], // 内部选中值，确保初始值安全
      frameworkData: [], // 框架数据
      frameworkLoading: false // 框架数据加载状态
    }
  },

  created () {
    // 组件创建时获取全量框架数据
    this.getAllFrameworkData()
  },

  computed: {
    // 映射用户状态
    ...mapState({
      currentUser: state => state.user.currentUser // 当前用户信息
    }),

    // 级联选择器的 props 配置
    cascaderProps () {
      return {
        value: 'gradeFrameworkId',
        label: 'frameworkName',
        children: 'frameworks',
        disabled: 'isDisabled'
      }
    },

    // 级联选择器的数据
    cascaderData () {
      if (!this.frameworkData || !Array.isArray(this.frameworkData) || this.frameworkData.length === 0) {
        return []
      }
      if (!this.selectedGrade) {
        return []
      }
      return tools.getFrameworkData(this.frameworkData, this.selectedGrade)
    },

    // 组合的加载状态
    combinedLoading () {
      return this.loading || this.frameworkLoading
    },

    // 是否禁用级联选择器
    isDisabled () {
      // 如果没有选择年级，则禁用
      return !this.selectedGrade || this.selectedGrade.trim() === ''
    },

    // 计算占位符文本
    computedPlaceholder () {
      return this.placeholder || this.$t('loc.lesson2NewLessonFormPlaceHolderSelect')
    }
  },

  watch: {
    // 监听外部值变化
    value: {
      handler (newValue) {
        // 避免无限循环，只在值真正不同时才更新
        if (JSON.stringify(newValue) !== JSON.stringify(this.selectedValue)) {
          this.selectedValue = [...newValue]
          // 框架名称鼠标悬浮显示
          this.updateTitle()
        }
      },
      deep: true
    },
    // 监听年级变化
    selectedGrade: {
      handler (newGrade, oldGrade) {
        if (this.isDataBackFill) {
          return
        }
        // 如果年级发生变化，清空当前选择
        if (newGrade !== oldGrade) {
          // 在下一个 tick 中应用默认选择
          this.$nextTick(() => {
            this.applyDefaultSelection()
            // 框架名称鼠标悬浮显示
            this.updateTitle()
          })
        }
      },
      immediate: false,
      deep: true
    }
  },
  mounted () {
    this.updateTitle()
    this.initCascaderEmpty()
  },
  methods: {
    // 鼠标悬浮框架名称显示
    updateTitle () {
      // 拿到 el-input 元素
      this.$nextTick(() => {
        const cascader = this.$refs.cascaderRef
        if (!cascader || !this.cascaderData) {
          return
        }

        const inputEl = cascader.$el.querySelector('.el-input__inner')
        if (!inputEl) {
          return
        }

        // 通过 getCheckedNodes 获取完整路径（单选时返回一个 path）
        let pathLabels = []
        if (typeof cascader.getCheckedNodes === 'function') {
          const checkedNodes = cascader.getCheckedNodes() // Element UI 2.x 支持
          if (checkedNodes.length > 0 && checkedNodes[0]) {
            pathLabels = checkedNodes[0].pathLabels || []
          }
        }
        if (pathLabels.length > 0) {
          const fullLabel = pathLabels[pathLabels.length - 1]
          inputEl.setAttribute('title', fullLabel)
        }
      })
    },

    // 获取默认州信息
    getDefaultState () {
      // 如果有已选的国家和州优先使用已选的
      if (this.selectedValue && this.selectedValue.length > 0) {
        if (this.selectedValue.length === 2) {
          // 先找到对应的国家
          let countryData = this.frameworkData.find(x => equalsIgnoreCase(x.country, this.selectedValue[0]))
          // 找到对应的国家下的第一个州
          let state = countryData.stateFrameworkInfo[0].state
          return {
            country: this.selectedValue[0],
            state: state
          }
        } else {
          return {
            country: this.selectedValue[0],
            state: this.selectedValue[1]
          }
        }
      }
      if (this.currentUser && this.currentUser.country && this.currentUser.state) {
        return {
          country: this.currentUser.country,
          state: this.currentUser.state
        }
      }
      return {
        country: 'United States',
        state: 'California'
      }
    },
    /**
     * 获取全量框架数据
     */
    getAllFrameworkData () {
      // 设置加载状态
      this.frameworkLoading = true
      // 向父组件发送加载状态变化事件
      this.$emit('loading-change', true)

      // 发送请求获取 states 列表
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().getAllFrameworkInfoByCountry)
          .then(response => {
            // 处理返回的数据
            this.frameworkData = response

            // 在数据加载完成后应用默认选择
            this.$nextTick(() => {
              this.applyDefaultSelection()
            })

            resolve()
          })
          .catch(error => {
            reject(error)
          })
          .finally(() => {
            // 无论成功失败都关闭加载状态
            this.frameworkLoading = false
            // 向父组件发送加载状态变化事件
            this.$emit('loading-change', false)
          })
      })
    },

    /**
     * 应用默认州选择
     */
    applyDefaultSelection () {
      // 如果已有选择或没有年级选择，则不处理
      if (!this.selectedGrade) {
        return
      }

      const defaultState = this.getDefaultState()
      if (!defaultState) {
        return
      }
      // 查找匹配的框架数据
      let found = false
      let findFramework = null
      // 获取默认州和默认年级对应的框架ID
      const defaultCountryData = this.frameworkData.find(countryData => countryData.country === defaultState.country)
      const defaultFrameworkData = defaultCountryData && defaultCountryData.stateFrameworkInfo.find(stateData => stateData.state === defaultState.state)
      if (defaultFrameworkData) {
        const defaultFramework = defaultFrameworkData.stateFrameworkInfo.frameworks.find(framework => framework.gradeFrameworkIdMap[this.selectedGrade])
        if (defaultFramework) {
          findFramework = defaultFramework
          if (tools.isTwoLevelCountry(defaultCountryData.country)) {
            this.selectedValue = [defaultCountryData.country, defaultFramework.gradeFrameworkIdMap[this.selectedGrade]]
          } else {
            this.selectedValue = [defaultCountryData.country, defaultFrameworkData.state, defaultFramework.gradeFrameworkIdMap[this.selectedGrade]]
          }
          found = true
        }
      }

      // 如果在默认州中没有找到，则遍历所有州和框架
      if (!found) {
        for (const countryData of this.frameworkData) {
          for (const stateData of countryData.stateFrameworkInfo) {
            for (const framework of stateData.stateFrameworkInfo.frameworks) {
              if (framework.gradeFrameworkIdMap[this.selectedGrade]) {
                if (tools.isTwoLevelCountry(defaultCountryData.country)) {
                  this.selectedValue = [countryData.country, framework.gradeFrameworkIdMap[this.selectedGrade]]
                } else {
                  this.selectedValue = [countryData.country, stateData.state, framework.gradeFrameworkIdMap[this.selectedGrade]]
                }
                findFramework = framework
                found = true
                break
              }
            }
            if (found) break
          }
        }
      }

      // 更新数据
      if (findFramework) {
        this.$emit('updateFrameworkName', findFramework.frameworkName)
      }
      this.$emit('input', this.selectedValue)
      this.$emit('change')
    },

    /**
     * 处理选择变化
     * @param {Array} value 选中的值数组
     */
    handleChange (value) {
      if (!value || value.length < 2) {
        return
      }
      // 如果回调参数长度为 2，说明是墨西哥，无州信息，需要特殊处理
      if (value.length === 2) {
        // 先找到对应的国家
        let countryData = this.frameworkData.find(x => equalsIgnoreCase(x.country, value[0]))
        // 找到对应的国家下的第一个州
        let state = countryData.stateFrameworkInfo[0].state
        // 将州信息和框架ID组合成新的数组
        value = [value[0], state, value[1]]
      }

      this.$emit('input', this.selectedValue)
      this.$emit('change')
      const frameworkName = this.findFrameworkName(value)
      this.$emit('updateFrameworkName', frameworkName)
      // 框架名称鼠标悬浮显示
      this.updateTitle()
    },

    /**
     * 查找框架名称
     * @param value 标准数据
     * @returns {String} 框架名称
     */
    findFrameworkName (value) {
      let frameworkName = ''

      const defaultCountryData = this.frameworkData.find(countryData => countryData.country === value[0])
      const defaultFrameworkData = defaultCountryData && defaultCountryData.stateFrameworkInfo.find(stateData => stateData.state === value[1])
      if (defaultFrameworkData) {
        for (const framework of defaultFrameworkData.stateFrameworkInfo.frameworks) {
          if (framework.gradeFrameworkIdMap[this.selectedGrade] === value[2]) {
            frameworkName = framework.frameworkName
            break
          }
        }
      }

      return frameworkName
    },
    
    // 初始级联选项为空时的提示
    initCascaderEmpty() {
      this.$nextTick(() => {
        if (!this.cascaderData || this.cascaderData.length === 0) {
          const frameworkSelectorCascader = document.querySelector('.framework-selector-cascader')
          // 设置宽度
          if (frameworkSelectorCascader) {
            frameworkSelectorCascader.style.width = '534px'
          }
          // 设置宽度
          const menu = frameworkSelectorCascader.querySelector('.el-cascader-menu')
          if (menu) {
            menu.style.width = '100%'
          }
          // 设置高度
          const menuWrap = frameworkSelectorCascader.querySelector('.el-cascader-menu__wrap')
          if (menuWrap) {
            menuWrap.style.height = '62px'
          }
          // 设置文本提示
          const textElement = frameworkSelectorCascader.querySelector('.el-cascader-menu__empty-text')
          if (textElement) {
            textElement.innerHTML = this.$t('loc.selectGradeFirst')
            textElement.style.color = '#323338'
          }
        }
      })
    }
  }
}
</script>

<style>
.framework-selector-cascader .el-cascader-menu__list {
  max-width: 600px;
}

.framework-selector-cascader .el-cascader-node__label {
  white-space: normal;
}

.framework-selector-cascader .el-cascader-node {
  height: unset;
  line-height: 20px;
  padding: 7px 30px 7px 20px;
}

.framework-selector-cascader .el-cascader-node:hover {
  background-color: #ddf2f3;
  color: #10b3b7;
}
</style>

<style scoped>
.required-star {
  color: #f56c6c;
  margin-right: 2px;
}
</style>