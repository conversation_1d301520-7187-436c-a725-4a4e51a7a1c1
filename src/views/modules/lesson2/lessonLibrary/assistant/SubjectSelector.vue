<template>
  <div>
    <!-- 测评点选择器 -->
    <el-select
      v-model="originalSelectedItems"
      multiple
      placeholder=""
      @click.native="openShowModal"
      @remove-tag="handleRemoveTag"
      popper-class="display-none"
      :disabled="unitProgress > 20"
      value-key="id"
      :class="{ 'has-selected-subjects': originalSelectedItems.length > 0 && !loading }"
      class="w-full lesson-assistant-subject-select">
        <!-- 当 domainSelected 为空时才显示 prefix -->
        <template slot="prefix" v-if="!originalSelectedItems.length || loading">
          <i v-show="loading" class="el-icon-loading lg-color-primary font-size-20"></i>
          <i v-show="!loading" class="lg-icon lg-icon-add1 lg-color-primary font-size-20"></i>
          <span class="font-weight-600">{{ $t('loc.selectMeasuresOrStandards2') }}</span>
        </template>
        <template slot="prefix" v-if="originalSelectedItems.length > 0 && !loading">
          <i class="lg-icon lg-icon-add1 lg-color-primary font-size-20"></i>
        </template>
        <el-option
          v-for="item in originalSelectedItems"
          :key="item.id"
          :label="item.abbreviation"
          :value="item">
        </el-option>
    </el-select>
    <el-dialog :visible.sync="showModal" width="60%"
               top="2vh"
               :append-to-body="true"
               :close-on-click-modal="false"
               :before-close="handleBeforeClose"
               class="measures-apply-dialog dialog-fullscreen-mobile">
               
      <div class="modal-header modal-header-mobile" style="height: calc(-264px + 100vh);">
        <h3 class="font-size-20 remove-margin-t-0 font-weight-600">{{ $t('loc.selectMeasuresOrStandards') }}</h3>
        <!--  搜索框  -->
        <div class="add-margin-t-8 search-input">
          <el-input
            class="searchInput"
            v-model="searchQuery"
            :placeholder="$t('loc.SearchStandardOrMeasures')"
            prefix-icon="el-icon-search"
            clearable>
          </el-input>
        </div>
        <!-- 树形结构 -->
        <div class="tree-container lg-scrollbar-show add-margin-t-16 tree-container-mobile" v-loading="loading">
          <el-tree
            :data="dataNodeList"
            :default-expanded-keys="defaultExpandedKeys"
            show-checkbox
            node-key="id"
            icon-class="tree-icon"
            :props="defaultProps"
            :empty-text="treeDataLoading ? ' ' : 'No data'"
            :render-content="renderContent"
            @check="handleCheck"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
            ref="tree">
          </el-tree>
        </div>
      </div>
      <!-- 选中的测评点 -->
      <div class="footer-container footer-container-mobile">
        <div v-if="selectedItems.length" class="bg bg-light m-t-sm wrapper-sm selected-items-container">
          <div class="flex-grow-1" style="max-width: 100%;">
            <div>
              <div class="selected-title">Selected:</div>
              <div v-for="item in selectedItems" class="showDom selectMeasure display-ib" :key="item.id">
                <span>{{ item.abbreviation }}</span>
                <i @click="removeSelectedItem(item)" class="flag el-icon-close red lg-pointer"
                   style="margin-left: 4px;"></i>
              </div>
              <span class="max-items" v-if="showMaxItem">(Max: {{ maxItems }})</span>
            </div>
          </div>
        </div>
        <!-- 确认和取消按钮 -->
        <div
          class="button-group mobile-button-group"
          style="display: flex; justify-content: flex-end; align-items: center; margin-top: 16px; margin-right: 5px">
          <el-button plain @click="cancelSelection">{{ $t('loc.cancel') }}</el-button>
          <el-button type="primary" @click="confirmSelection">{{ $t('loc.unitPlannerStep3Confirm') }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import frameworkUtils from '@/utils/frameworkUtils'
import { equalsIgnoreCase } from '@/utils/common'

/**
 * 判断是否是叶子节点
 * @param dataNode 节点
 * @returns 是否是叶子节点
 */
function isLeafNode (dataNode) {
  return dataNode && (!dataNode.children || dataNode.children.length === 0)
}

/**
 * 判断是否需要 select all 节点
 * @param dataNode 节点
 * @returns 是否需要 select all 节点
 */
function isSelectAllNeeded (dataNode) {
  return !isLeafNode(dataNode) && !dataNode.children.some(child => child.children.length)
}

/**
 * 转换节点列表
 * @param dataNodeList 节点列表
 * @returns 转换后的节点列表
 */
function convertDataNodeList (dataNodeList) {
  return dataNodeList.length === 0 ? [] : dataNodeList.map(convertDataNode)
}

/**
 * 转换节点, 添加 select all 节点
 * @param dataNode 节点
 * @returns 转换后的节点
 */
function convertDataNode (dataNode) {
  return isLeafNode(dataNode)
    ? dataNode
    : {
      ...dataNode,
      children: isSelectAllNeeded(dataNode) ? [
        { id: '#' + dataNode.id, name: 'Select all', virtual: true },
        ...convertDataNodeList(dataNode.children)
      ] : convertDataNodeList(dataNode.children)
    }
}

/**
 * 查找节点
 * @param dataNodeList 节点列表
 * @param id 节点 id
 * @returns 节点
 */
function findNodeById(dataNodeList, id) {
  for (let dataNode of dataNodeList) {
    if (dataNode.id === id) return dataNode
    if (!isLeafNode(dataNode)) {
      const result = findNodeById(dataNode.children, id)
      if (result) return result
    }
  }
  return null
}

/**
 * 遍历所有节点, 执行回调函数
 * @param dataNodeList 节点列表
 * @param fn 回调函数
 */
function applyToAllNodes (dataNodeList, fn) {
  for (let dataNode of dataNodeList) {
    fn(dataNode)
    if (!isLeafNode(dataNode)) {
      applyToAllNodes(dataNode.children, fn)
    }
  }
}

/**
 * 判断所有子节点是否都选中
 * @param treeNode tree 组件内部节点
 */
function isAllChecked (treeNode) {
  return treeNode.childNodes.length !== 0 && treeNode.childNodes.every(child => child.checked)
}

/**
 * 判断是否存在选中的子节点
 * @param treeNode tree 组件内部节点
 */
function isAnyChecked (treeNode) {
  return treeNode.childNodes.length !== 0 && treeNode.childNodes.some(child => child.checked)
}

/**
 * 判断是否存在 select all 节点
 * @param dataNode 节点
 */
function hasSelectAll (dataNode) {
  return !isLeafNode(dataNode) && dataNode.children[0].virtual
}


export default {
  name: 'SubjectSelector',

  props: {
    treeData: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    defaultProps: {
      type: Object,
      required: true
    },
    maxItems: {
      type: Number,
      required: true
    },
    unitProgress: {
      type: Number,
      required: true
    },
    // 是否显示最大可选数量
    showMaxItem: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showModal: false, // 是否显示弹窗
      searchQuery: '',
      lastTreeData: [],
      expandedState: {}, // 用于存储节点的展开状态
      selectedItems: [], // 用于存储选中的测评点
      lastCheckNodes: [], // 上一次保存的所有的节点
      lastHalfCheckNodes: [], // 上一次保存的半选中的节点
      originalSelectedItems: [], // 用于保存原始选中数据
      treeDataLoading: false,
      domainNodes: [], // 用于存储 domain 节点
      hasHistoryData: false, // 是否有回显的历史测评点数据，例如保存课程草稿后再次进入
      isMobile: false, // 添加移动端判断变量
    }
  },
  inject: ['changeElFormIndex'],
  watch: {
    // 监听 searchQuery 变化
    searchQuery(newValue, oldValue) {
      if(this.$refs.tree) {
        if (oldValue === '') {
          // 记录当前节点的展开状态
          this.recordNodeExpansionState()
        }

        if (newValue === '') {
          // 恢复所有节点的展开状态
          this.restoreNodeExpansionState()
        }

        // 在 tree 组件状态更改之前记录
        this.$refs.tree.filter(newValue);
        this.syncChecked()
      }
    },
    // 监听树形数据的变化，清空选中的数据
    treeData: {
      handler(newValue, oldValue) {
        // 如果有历史回显数据，则不再清空
        if (!this.hasHistoryData) {
          // 收集所有测评点
          let allMeasure = []
          // 递归获取所有底层测评点
          frameworkUtils.getMeasuresBottom(newValue, allMeasure)
          // 获取测评点的 Id
          const allMeasureIds = allMeasure.map(measure => measure.id)
          // 过滤掉没有在选中学科的测评点
          this.selectedItems = this.selectedItems.filter(item => allMeasureIds.includes(item.id))
        }
        // 如果新的 treeData 为空，则设置 treeDataLoading 为 true，否则为 false
        this.treeDataLoading = newValue.length === 0;
      },
      deep: true
    },
    showModal(newValue) {
      if (!this.changeElFormIndex) return
      if (newValue) {
        this.changeElFormIndex(-1);
      } else {
        setTimeout(() => {
          this.changeElFormIndex(1);
        }, 200); // 等待弹窗关闭动画结束
      }
    },
  },
  computed: {

    /**
     * 数据节点列表, 添加 select all 节点
     */
    dataNodeList() {
      return convertDataNodeList(this.treeData)
    },

    /**
     * 剩余可选择数量
     */
    remainingChoices() {
      return this.maxItems - this.selectedItems.length
    },

    /**
     * 默认展开第一个节点
     */
    defaultExpandedKeys() {
      if (this.dataNodeList.length === 0) {
        return []
      }
      const expandedKeys = []
      const helper = (dataNode) => {
        if (isLeafNode(dataNode)) {
          return
        }
        expandedKeys.push(dataNode.id)
        helper(dataNode.children[0])
      }
      helper(this.dataNodeList[0])
      return expandedKeys
    }
  },

  methods: {
    /**
     * 记录所有节点的展开状态
     */
    recordNodeExpansionState() {
      this.expandedState = {}
      applyToAllNodes(this.dataNodeList, dataNode => {
        if (isLeafNode(dataNode)) {
          return
        }
        const treeNode = this.$refs.tree.getNode(dataNode.id)
        this.expandedState[dataNode.id] = treeNode.expanded
      })
    },

    /**
     * 恢复所有节点的展开状态
     */
    restoreNodeExpansionState() {
      applyToAllNodes(this.dataNodeList, dataNode => {
        if (isLeafNode(dataNode)) {
          return
        }
        const treeNode = this.$refs.tree.getNode(dataNode.id)
        treeNode.expanded = this.expandedState[dataNode.id]
      })
    },

    /**
     * 判断两个节点是否相同
     * @param n1
     * @param n2
     */
    isSameNode(n1, n2) {
      return equalsIgnoreCase(n1.id, n2.id)
    },

    /**
     * 同步选中状态
     */
    syncChecked() {
      this.$refs.tree.getHalfCheckedNodes()
        .filter(hasSelectAll)
        .map(dataNode => this.$refs.tree.getNode(dataNode.id))
        .forEach(({ childNodes: [ treeNode ] }) => {
          // 构造一个虚拟的父节点 (去除掉 select all 节点和被过滤掉的, 页面上能看到的样子)
          const parentTreeNode = { ...treeNode.parent, childNodes: treeNode.parent.childNodes.slice(1).filter(treeNode => this.isNotFilteredNode(treeNode.data)) }
          if (isAllChecked(parentTreeNode)) {
            // 所有子节点都选中, 设置 select all 节点为选中状态, 取消半选状态
            this.$refs.tree.setChecked(treeNode.data.id, true)
            treeNode.indeterminate = false
            treeNode.data.indeterminate = false
          } else if (isAnyChecked(parentTreeNode)) {
            // 半选
            // 样式会变
            treeNode.indeterminate = true
            // 控制 select all 选中时的逻辑
            treeNode.data.indeterminate = true
          } else {
            // 取消选中
            this.$refs.tree.setChecked(treeNode.data.id, false)
          }
        })
    },
    // 获取所有非叶子节点的 name 和 id 以及勾选状态
    getNonLeafNodes(treeData) {
      let result = [];
      const traverse = (nodes) => {
        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            const selected = this.$refs.tree.getCheckedKeys().includes(node.id);
            result.push({
              label: node.name || node.abbreviation,
              value: node.id,
              selected: selected
            });
            traverse(node.children);
          }
        });
      };
      traverse(treeData);
      return result;
    },

    /**
     * 过滤节点
     * @param value
     * @param dataNode
     * @param treeNode
     */
    filterNode(value, dataNode, treeNode) {
      // select all 节点, 判断父节点的所有子节点是否有符合条件的节点
      return dataNode.virtual
        ? treeNode.parent.childNodes
          .map(child => child.data)
          .some(childDataNode => this.doFilterNode(value, childDataNode))
        : this.doFilterNode(value, dataNode)
    },
    doFilterNode(value, data) {
      // 仅输入空格,不会影响节点的展开状态
      if (!value || !value.trim()) return true;
      const lowerValue = value.toLowerCase().trim();
      return (data.abbreviation && data.abbreviation.toLowerCase().indexOf(lowerValue) !== -1) ||
        (data.name && data.name.toLowerCase().indexOf(lowerValue) !== -1) ||
        (data.description && data.description.toLowerCase().indexOf(lowerValue) !== -1);
    },
    // 清空父节点
    clearDomainNodes() {
      this.domainNodes = [];
    },
    getCheckedKeys() {
      return this.selectedItems.map(item => item.id);
    },
    // 查找第一个叶子节点的路径
    findFirstLeafPath(treeData) {
      let path = [];
      const findPath = (node, currentPath) => {
        currentPath.push(node.id);
        // 如果是叶子节点，保存路径
        if (!node.children || node.children.length === 0) {
          path = currentPath.slice();
          return;
        }
        // 递归查找子节点
        for (let child of node.children) {
          findPath(child, currentPath);
          if (path.length > 0) break;
        }
        // 回溯
        currentPath.pop();
      };
      // 从根节点开始查找
      findPath(treeData[0], []);
      return path;
    },
    // 打开弹窗
    openShowModal() {
      if (this.loading || this.unitProgress > 20) {
        return
      }
      this.showModal = true;
      this.selectedItems = [...this.originalSelectedItems];  // 设置当前选中的
      const checkedKeys = this.getCheckedKeys();
      this.$nextTick(() => {
        if(this.$refs.tree) {
          this.$refs.tree.setCheckedKeys(checkedKeys);
          this.syncChecked()
        }
      });
    },

    /**
     * 是否是没有被过滤掉的节点
     * @param dataNode
     */
    isNotFilteredNode(dataNode) {
      return this.doFilterNode(this.searchQuery, dataNode)
    },

    /**
     * 点击节点
     * @param dataNode
     * @param treeNode
     */
    handleNodeClick(dataNode, treeNode) {
      this.delegateCheck(dataNode, !treeNode.checked)
    },

    /**
     * 点击复选框
     * @param dataNode
     */
    handleCheck(dataNode) {
      const treeNode = this.$refs.tree.getNode(dataNode.id)
      this.delegateCheck(dataNode, treeNode.checked)
    },

    /**
     * 代理选中, 统一处理选中状态
     * @param dataNode
     * @param checked
     */
    delegateCheck(dataNode, checked) {
      if (!isLeafNode(dataNode)) {
        return
      }

      if (dataNode.indeterminate) {
        // 对于 select all 没有可以选择的就取消选择
        checked =  this.remainingChoices < 1 ? false : true
        dataNode.indeterminate = false
      }

      this.doCheck(dataNode, checked)
      this.syncChecked()
    },

    /**
     * 处理选中
     * @param dataNode
     * @param checked
     */
    doCheck(dataNode, checked) {
      this.$refs.tree.setChecked(dataNode.id, checked);
      // 如果选中且剩余可选择数量小于 1 则取消选中
      if (checked && this.remainingChoices < 1) {
        this.$refs.tree.setChecked(dataNode.id, !checked);
        this.showLimitWarning();
        return
      }

      // select all 节点
      if (dataNode.virtual) {
        // 获取所有子节点 (去除掉 select all 节点和被过滤掉的)
        const dataNodes = this.$refs.tree.getNode(dataNode.id.slice(1))
          .childNodes.slice(1).map(child => child.data)
          .filter(this.isNotFilteredNode)

        if (checked) {
          // 选中剩余可选择的节点
          dataNodes.filter(dataNode => !this.$refs.tree.getNode(dataNode.id).checked)
            .slice(0, this.remainingChoices)
            .forEach(dataNode => this.$refs.tree.setChecked(dataNode.id, checked))
        } else {
          // 取消选中所有子节点
          dataNodes.forEach(dataNode => this.$refs.tree.setChecked(dataNode.id, checked))
        }
      }

      // 获取所有选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes().filter(n => !n.virtual)
      this.selectedItems = checkedNodes.filter(isLeafNode)
        .map(node => ({
          id: node.id,
          abbreviation: node.abbreviation,
          name: node.name
        }));
    },
    // doCheck(node) {
    //   let currentCheck = false
    //   // 先判断一下当前节点是不是上一次选中的
    //   if (this.lastCheckNodes.some(n => this.isSameNode(n, node)) && this.lastHalfCheckNodes.every(n => !this.isSameNode(n, node))) {
    //     currentCheck = true
    //   }
    //   const checkedNodes = this.$refs.tree.getCheckedNodes().filter(n => !n.virtual)

    //   // 获取这次新选中的叶子节点
    //   const newCheckedLeafNodes = checkedNodes.filter(n =>
    //     (!n.children || n.children.length === 0) &&
    //     !this.selectedItems.some(item => this.isSameNode(item, n))
    //   );
    //   // 计算还可以选中的叶子节点数
    //   const remainingCount = this.maxItems - this.selectedItems.length;
    //   // 如果上一次选中的节点是选中状态，那么此时应该都取消
    //   if (currentCheck) {
    //     // 设置当前节点状态为 false
    //     this.$refs.tree.setChecked(node.id, false, true);
    //     // 清空选中的测评点
    //     this.clearSelectItems(node);
    //   } else {
    //     if (newCheckedLeafNodes.length > remainingCount) {
    //       // 只取前 remainingCount 个叶子节点
    //       let nodesToCheck = []
    //       // 如果还有待选中的节点
    //       if (remainingCount > 0) {
    //         nodesToCheck = newCheckedLeafNodes.slice(0, remainingCount)
    //       }

    //       // 如果新选中的叶子节点数大于 1 且剩余可选中的叶子节点数为 0 且没有待选中的节点 则取消所有选中
    //       if (remainingCount === 0 && nodesToCheck.length === 0) {
    //         this.$refs.tree.setChecked(node.id, false, true);
    //         this.updateSelectItems(this.$refs.tree.getCheckedNodes());
    //         if (this.selectedItems.length !== 0) {
    //           this.showLimitWarning(node);
    //         }
    //         return;
    //       }
    //       // 取消所有新的且不在之前权重的叶子节点的选中状态
    //       newCheckedLeafNodes.forEach(n => {
    //         if (!nodesToCheck.some(item => this.isSameNode(item, n))) {
    //           this.$refs.tree.setChecked(n.id, false, true);
    //         }
    //       });
    //       // 只选中不在已选中的叶子节点和之前选中的叶子节点
    //       nodesToCheck.forEach(n => {
    //         if (!this.selectedItems.some(item => this.isSameNode(item, n))) {
    //           this.$refs.tree.setChecked(n.id, true, true);
    //         }
    //       });
    //       this.showLimitWarning(node);
    //     } else {
    //       // 只选中不在已选中的叶子节点
    //       newCheckedLeafNodes.forEach(n => {
    //         if (!this.selectedItems.some(item => this.isSameNode(item, n))) {
    //           this.$refs.tree.setChecked(n.id, true, true);
    //         }
    //       });
    //     }
    //     this.updateSelectItems(this.$refs.tree.getCheckedNodes());
    //   }
    //   // this.updateDomainNodes();


    //   // 同步选中状态
    //   this.syncChecked()
    // },
    // 更新 DomainNodes
    updateDomainNodes () {
      // 清空当前的 domainNodes 数组
      this.domainNodes = []
      // 获取树形结构中所有选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes()
      // 拿到第一层有选中关系的所有领域节点
      const firstLevelCheckedNodes = this.treeData.filter(node => this.isNodeCheckedOrHasCheckedChildren(node, checkedNodes))
      // 将领域节点的 id 和 name 保存到 domainNodes 中
      this.domainNodes = firstLevelCheckedNodes.map(node => ({
        id: node.id,
        name: node.name || node.abbreviation
      }))
      // 触发事件，将更新后的 domainNodes 传递给父组件
      this.$emit('update:domainNodes', this.domainNodes);
    },

    // 判断节点是否被选中或者有选中的子节点
    isNodeCheckedOrHasCheckedChildren (node, checkedNodes) {
      if (checkedNodes.some(checkedNode => this.isSameNode(checkedNode, node))) {
        return true
      }
      if (node.children && node.children.length > 0) {
        return node.children.some(child => this.isNodeCheckedOrHasCheckedChildren(child, checkedNodes));
      }
      return false
    },
    // 显示选中数量警告
    showLimitWarning(node) {
      let messageKey;
      if (this.maxItems === 10) {
        messageKey = 'loc.selectMeasureForOneWeek';
      } else if (this.maxItems === 20) {
        messageKey = 'loc.selectMeasureForTwoWeeks';
      } else {
        messageKey = 'loc.selectMeasureForMoreWeeks';
      }
      if (this.maxItems === 8) {
        messageKey = 'loc.selectMeasureEight'
      }
      // 展示最大选中数量警告后不再显示 toast 提示
      this.$message({
        message: this.$t(messageKey),
        type: 'warning'
      })
      // if (node.message === 1) {
      //   this.clearSelectItems(node);
      //   node.message = 0;
      // } else {
      //   node.message = 1;
      // }
    },
    getAllNodes(node, allNodes) {
      // 将当前节点加入结果数组
      allNodes.push(node);

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          this.getAllNodes(child, allNodes);
        });
      }
    },
    // 清空选中的测评点
    clearSelectItems(checkedNodes) {
      // 保存当前选中的节点
      this.lastCheckNodes = this.$refs.tree.getCheckedNodes(false, true);
      // 保存上一次半选节点
      this.lastHalfCheckNodes = this.$refs.tree.getHalfCheckedNodes()
      let children = []
      // 获取当前节点及其所有的子节点
      this.getAllNodes(checkedNodes, children)
      let needClearItems = children.filter(n => !n.children || n.children.length === 0)
        .map(node => ({
          id: node.id,
          abbreviation: node.abbreviation,
          name: node.name
        }));
      // 从 selectedItems 中移除 needClearItems 中的节点
      this.selectedItems = this.selectedItems.filter(item => !needClearItems.some(needClearItem => needClearItem.id === item.id));
      // 更新 DomainNodes
      // this.updateDomainNodes();
    },
    // 更新选中的测评点
    updateSelectItems(checkedNodes) {
      // 保存当前选中的节点
      this.lastCheckNodes = this.$refs.tree.getCheckedNodes(false, true);
      // 保存上一次半选节点
      this.lastHalfCheckNodes = this.$refs.tree.getHalfCheckedNodes()
      if (this.searchQuery !== '') {
        let currentSelectedNodes = checkedNodes.filter(n => !n.children || n.children.length === 0)
          .map(node => ({
            id: node.id,
            abbreviation: node.abbreviation,
            name: node.name
          }));
        // 添加 currentSelectedNodes 中有，但是不存在 selectedItems 中的节点
        this.selectedItems.push(...currentSelectedNodes.filter(item => !this.selectedItems.some(selectedItem => selectedItem.id === item.id)));
      } else {
        this.selectedItems = checkedNodes.filter(n => !n.children || n.children.length === 0)
          .map(node => ({
            id: node.id,
            abbreviation: node.abbreviation,
            name: node.name
          }));
      }
    },
    // 移除选中的测评点
    removeSelectedItem(item) {
      this.selectedItems = this.selectedItems.filter(selectedItem => !this.isSameNode(selectedItem, item));
      const node = this.$refs.tree && this.$refs.tree.getNode(item.id);
      if (node) {
        node.setChecked(false, true);
        // console.log("removeSelectedItem")
        // this.updateDomainNodes();
      }
      this.syncChecked()
    },
    // 渲染树形节点内容
    renderContent(h, {node, data}) {
      // name 样式
      const nameStyle = {
        color: '#111C1C',
        fontSize: '14px',
      };
      // description 样式
      const descriptionStyle = {
        color: '#999',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        maxWidth: 'calc(100% - 40px)' // 留出其他元素的空间，例如树形节点的展开/折叠按钮
      };
      // 如果同时有名字和缩写，展示缩写：名字
      if (data.name && data.abbreviation) {
        // 如果名字和缩写相同，只显示名字
        if (data.name === data.abbreviation) {
          return h('span', {style: nameStyle}, data.name);
        }
        return h('span', [
          h('span', {style: nameStyle}, data.abbreviation),
          h('span', {style: nameStyle}, ': '),
          h('span', {style: nameStyle}, data.name)
        ]);
      }
      // 如果没有描述，展示名字或缩写
      if (!data.description || data.name) {
        return h('span', {style: descriptionStyle}, [
          h('span', {style: nameStyle}, data.name || data.abbreviation)
        ]);
      }
      // 如果不同时有名字和缩写，展示缩写和描述
      return h('el-tooltip', {
        props: {
          effect: 'light',
          content: data.description,
          placement: 'top',
          openDelay: 500
        }
      }, [
        h('span', {style: descriptionStyle}, [
          h('span', {style: nameStyle}, data.abbreviation),
          h('span', {style: nameStyle}, ': '),
          h('span', {style: descriptionStyle}, data.description)
        ])
      ]);
    },

    // 确认选中
    confirmSelection() {
      // 检查是否超过最大选中数量
      if (this.selectedItems.length > this.maxItems) {
        this.showLimitWarning();
        return;
      }
      // 通知父组件更新选中的数据
      this.originalSelectedItems = [...this.selectedItems]
      this.$emit('update:selectedItems', this.selectedItems, true);
      this.updateDomainNodes();
      // 关闭弹窗
      this.showModal = false;
      // 1 秒后 清空搜索关键字
      setTimeout(() => {
        this.searchQuery = '';
      }, 1000);
    },
    // 取消选中
    cancelSelection() {
      // 恢复原始选中数据
      this.selectedItems = [...this.originalSelectedItems];
      this.$refs.tree.setCheckedKeys(this.originalSelectedItems.map(item => item.id));
      // 关闭弹窗
      this.showModal = false;
      // 1 秒后 清空搜索关键字
      setTimeout(() => {
        this.searchQuery = '';
      }, 1000);
    },
    // 关闭弹窗前的处理
    handleBeforeClose(done) {
      // 恢复原始选中数据
      this.selectedItems = [...this.originalSelectedItems];
      this.$refs.tree.setCheckedKeys(this.originalSelectedItems.map(item => item.id));
      // 关闭弹窗
      done();
      // 1 秒后 清空搜索关键字
      setTimeout(() => {
        this.searchQuery = '';
      }, 1000);
    },
    // 截断文本
    truncate(text, length) {
      if (text.length <= length) {
        return text;
      } else {
        return text.substring(0, length) + '...';
      }
    },
    // 设置需要回显的测评点数据
    setSelectedItems (selectedItems) {
      this.originalSelectedItems = [...selectedItems]
      this.selectedItems = [...selectedItems]
      this.hasHistoryData = true
    },
    /**
     * 移除标签
     */
    handleRemoveTag(tag) {
      // 从选中项中移除
      this.originalSelectedItems = this.originalSelectedItems.filter(item => !this.isSameNode(item, tag));
      this.selectedItems = [...this.originalSelectedItems]
      // 更新树节点状态
      const node = this.$refs.tree && this.$refs.tree.getNode(tag.id);
      if (node) {
        node.setChecked(false, true);
        this.updateDomainNodes();
      }
      // 通知父组件更新数据
      this.$emit('update:selectedItems', this.selectedItems, true);
    },
    // 检查是否为移动端
    checkIsMobile() {
      this.isMobile = window.innerWidth <= 768
    },
  },
  mounted() {
    // 初始化移动端判断
    this.checkIsMobile()
    // 监听窗口大小变化
    window.addEventListener('resize', this.checkIsMobile)
  },
  beforeDestroy() {
    // 移除监听器
    window.removeEventListener('resize', this.checkIsMobile)
  },
}
</script>

<style lang="scss" scoped>
.lesson-assistant-subject-select {
  /deep/.el-input__prefix {
    padding-left: 12px;
    color: #676879;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
    cursor: pointer;
    color: #10B3B7;
    justify-content: center;
  }
  /deep/.el-input__suffix {
    display: none;
  }
}

.has-selected-subjects {
  /deep/.el-input__prefix {
    padding-right: 12px;
    color: #676879;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
    cursor: pointer;
    color: #10B3B7;
    justify-content: flex-end;
    transition: all 0s;
  }
}

.measures-apply-dialog {
  margin-top: 1vh
}
/deep/ .el-dialog__header {
  padding: 0 ! important;
}

/deep/ .el-tree-node__content {
  height: 30px
}

.el-button + .el-button {
  margin-left: 12px;
}

.full-width-button {
  width: 100%;
  color: #10B3B7 !important;
  font-weight: 600 !important;
  border: 1px solid #DCDFE6; /* 添加边框样式 */
  border-radius: 4px; /* 添加圆角样式 */
}

.full-width-button:hover,
.full-width-button:focus,
.full-width-button:active {
  /* 覆盖悬浮和点击时的背景颜色 */
  background-color: initial;
  /* 取消盒阴影 */
  box-shadow: none;
  /* 取消文字颜色变化 */
  color: initial;
}

.modal-header {
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-bottom: 0;

  /deep/ .el-input__validateIcon {
    display: none;
  }

}

.tree-container {
  width: 100%;

  /deep/ .el-tree .el-tree-node .el-tree-node__content {
    display: flex;
    align-items: center;

    .el-tree-node__expand-icon {
      // display: flex;
      // align-items: center;
      width: 16px;
      height: 16px;
      padding: 0;
      margin: 0 8px;
      position: relative;
      // font-size: 20px !important;
      border-radius: 4px;
      overflow: hidden;
      transition: transform 0.3s;

      &.expanded {
        transform: rotate(0deg);
      }

      // 非叶子节点不显示 checkbox
      &:not(.is-leaf) + .el-checkbox {
        display: none;
      }

      // 叶子节点不显示内容
      &.is-leaf {
        cursor: default;
        opacity: 0;
      }
    }

    .tree-icon {
      // 不允许收缩
      flex-shrink: 0;

      &.expanded::before {
        content: "-";
      }

      &::before {
        content: "+";
        font-weight: bold;
        color: #D9D9D9;
        background-color: #111C1C;
        position: absolute;
        top: -2px;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }
    }


    .el-checkbox {
      margin-right: 8px;
      margin-top: 2px;

      .el-checkbox__inner {
        width: 16px;
        height: 16px;

        &::after {
          top: 1px;
          left: 5px;
        }
      }
    }
  }
}

.footer-container {
  background-color: #fff;
  position: relative;
  bottom: 0;
  z-index: 1000;
}

.bg-light.bg, .bg-light .bg{
  background-color:#F5F6F8;
}

.flex-grow-1 {
  flex-grow: 1;
  width: 100%;
  max-width: 100%; /* 确保子元素的最大宽度 */
  overflow: hidden; /* 如果内容超出，隐藏溢出部分 */
}

.search-input {
  margin-top: 10px;
  width: 100%;
}

.modal-content {
  padding: 20px;
  position: initial !important;
  background-color: transparent !important;
  border: none !important;
  background-clip: padding-box !important;
  box-shadow: none !important;
}
/deep/ .el-dialog--center .el-dialog__body {
  text-align: initial;
  padding: 25px 25px 24px;
}

/deep/ .el-dialog__body {
  padding: 24px 20px 0;
}

.modal-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.selected-items {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #F5F6F8;
}

.selectMeasure {
  font-size: 12px;
  margin: 2px;
  padding: 7.5px;
  line-height: 1.1;
  border-radius: 4px;
  border: 1px solid #dbdbdb;
  background-color: #ffffff;
  display: inline-flex;
  align-items: center;

  .el-icon-close {
    margin-left: 4px;
    cursor: pointer;
    color: #999;

    &:hover {
      color: #f56c6c;
    }
  }
}

.selected-title {
  font-weight: bold;
  text-align: left;
  margin-bottom: 5px;
}

.max-items {
  font-weight: normal;
  color: #999;
  margin-left: 10px;
}

.button-group {
  margin-left: auto; /* 将剩余空间推到右侧 */
}
.is-disabled,
.is-disabled:hover,
.is-disabled:focus,
.is-disabled:active {
  color: #32333866 !important;
  background-color: #EBEEF5;
  opacity: 1 !important;
}
.button-green {
  color: #10B3B7;
  font-weight: 600;
}
/deep/ .el-dialog__body {
  padding: 24px;
}

/* 移动端适配样式 */
@media screen and (max-width: 768px) {
  .dialog-fullscreen-mobile {
    /deep/ .el-dialog {
      width: 100% !important;
      height: 100vh !important;
      margin: 0 !important;
      max-height: 100vh !important;
      display: flex;
      flex-direction: column;
      border-radius: 0;
      
      .el-dialog__body {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 80px;
        padding-top: 16px;
        position: relative;
      }
      
      .el-dialog__header {
        margin-top: 0;
        padding: 16px !important;
      }
    }
  }
  
  .tree-container-mobile {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding-bottom: 80px;
  }
  
  .footer-container-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 12px 16px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 2000;
  }
  
  .mobile-button-group {
    width: 100%;
    display: flex;
    justify-content: space-between !important;
    gap: 12px;
    margin: 0 !important;
    
    .el-button {
      flex: 1;
      margin: 0 !important;
    }
  }
  
  .selected-items-container {
    margin-bottom: 0px;
  }
  
  .modal-header-mobile {
    padding: 0 16px;
  }
}
</style>
