<template>
  <!-- 按领域自动分配 -->
  <el-select
    ref="domainSelect"
    v-model="selectedValue"
    multiple
    class="w-full lesson-assistant-domain-select"
    style="width: 100%"
    :placeholder="$t('loc.pleaseSelect')"
    :loading="getDomainLoading"
    @remove-tag="deleteDomain"
    popper-class="lesson-assistant-domain-select-dropdown">
    <el-option-group>
      <el-checkbox
        v-model="domainSelectAll"
        @change="handleSelectAllChange"
        :indeterminate="selectAllIndeterminate"
        class="domain-select-all w-full">
        <span class="font-weight-600">{{ frameworkName }}</span>
      </el-checkbox>
    </el-option-group>
    <el-option
      v-for="item in subjectOptions"
      :key="item.id"
      :label="item.name"
      :value="item.id">
      <el-checkbox
        style="pointer-events: none;"
        :value="selectedValue.includes(item.id)"
        :label="item.name"
      >
      </el-checkbox>
    </el-option>
    <template slot="empty" v-if="!getDomainLoading">
      <div class="flex-center-center font-size-14 color-323338 height-48">{{$t('loc.selectStateStandardsFirst')}}</div>
    </template>
  </el-select>
</template>
<script>

import { domainMax3 } from '../../../../../utils/constants'
import tools from '@/utils/tools'

export default {
  name: 'SimpleSubjectSelector',

  props: {
    // 绑定值
    value: {
      type: Array,
      default: () => []
    },
    frameworkName: {
      type: String,
      default: ''
    },
    selfSubjectOptions: {
      type: Array,
      default: () => []
    },
    ageName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      subjectOptions: this.selfSubjectOptions && this.selfSubjectOptions.length > 0 ? this.selfSubjectOptions : [],
      selectedValue: this.value ? [...this.value] : [], // 内部选中值，确保初始值安全
      domainSelectAll: false,
      getDomainLoading: false
    }
  },
  computed: {
    // 选择学科按钮是否全选的半选状态
    selectAllIndeterminate () {
      // 只要选项中有一个是选中的状态，则半选状态为 true
      return this.selectedValue && this.selectedValue.length > 0 && !this.domainSelectAll
    },
    // 获取当前选择的年龄是否为 'K (5-6)' 及以上
    isOlderThanK () {
      // 获取当前选中的年级的详细数据，获取其中的 value 值
      if (!this.ageName) {
        return true
      }
      // 获取年龄值
      const ageValue = tools.getAgeValue(this.ageName)
      // 判断年龄是否为 'K (5-6)' 及以上
      return ageValue ? ageValue >= 6 : false
    },
  },
  watch: {
    // 监听外部值变化
    value: {
      handler (newValue) {
        // 避免无限循环，只在值真正不同时才更新
        if (JSON.stringify(newValue) !== JSON.stringify(this.selectedValue)) {
          this.selectedValue = [...newValue]
        }
      },
      deep: true
    },
    // 监听当前选中的学科
    selectedValue: {
      deep: true,
      handler (newDomains, oldDomains) {
        if (newDomains && newDomains.length > domainMax3 && this.isOlderThanK) {
          this.$message.warning(this.$t('loc.unitPlannerDomainMaxThree'))
          this.selectedValue = oldDomains
          return
        }

        this.$emit('input', this.selectedValue)

        // 修改学科全选状态
        this.domainSelectAll = (this.subjectOptions && this.selectedValue.length >= domainMax3 && this.isOlderThanK) ||
          (this.subjectOptions && this.selectedValue.length >= this.subjectOptions.length && this.selectedValue.length !== 0)
      }
    }
  },
  methods: {

    /**
     * 从后端获取测评点数据, 并处理测评点数据
     *
     * @param frameworkId 框架ID
     */
    async fetchMeasuresFromBackend (frameworkId) {
      this.getDomainLoading = true
      this.selectedValue = []
      this.subjectOptions = []
      try {
        const measures = await this.$store.dispatch('curriculum/getFrameworkDomains', {
          frameworkId,
          compress: false
        })
        // 有时候会回显一次之前框架的，再出来当前框架的
        await this.$nextTick() // 确保空数据先渲染再更新

        this.subjectOptions = measures
      } catch (error) {
      } finally {
        this.$nextTick(() => {
          this.getDomainLoading = false
        })
      }
    },

    // 删除学科
    deleteDomain (domainId) {
      // 更新 domainSelected 数组
      this.selectedValue = this.selectedValue.filter(item => item !== domainId)
      if (this.selectedValue.length === 0) {
        this.domainSelectAll = false
      }
    },

    // 全选/取消全选
    handleSelectAllChange (checked) {
      // 如果当前已经达到限制，点击全选按钮应取消所有选择。否则按照顺序选择 3 个
      if (!checked) {
        this.selectedValue = []
      } else {
        // 距离达到限制缺少的个数
        let lackCount = domainMax3 - this.selectedValue.length
        // 按照顺序选择缺少的 domain
        for (let i = 0; i < this.subjectOptions.length; i++) {
          // 如果当前学科未选择
          if (!this.selectedValue.includes(this.subjectOptions[i].id)) {
            // 未选择则选择
            this.selectedValue.push(this.subjectOptions[i].id)
            // 如果不存在缺少的则退出
            if (this.isOlderThanK) {
              if (--lackCount <= 0) {
                break
              }
            }
          }
        }
      }
    }
  }
}
</script>


<style scoped lang="less">

</style>