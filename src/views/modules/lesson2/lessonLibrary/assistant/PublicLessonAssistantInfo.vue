<template>
  <el-form ref="fromRef" :model="lessonAssistantInfo" class="public-lesson-assistant-info">
    <div class="w-full">
      <div class="display-flex w-full" style="justify-content: space-between;gap: 6px;">
        <!--年龄组-->
        <el-form-item prop="ageGroup">
          <template #label>
            <label class="el-form-item__label">
              <span class="required-star font-size-14">*</span>
              <span class="font-size-14">{{ $t('loc.unitPlannerStep1Grade') }}</span>
            </label>
          </template>
          <el-select v-model="lessonAssistantInfo.ageGroup" class="w-full" @click.native="handleAgeGroupClick"
            :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')" @change="handleGradeChange">
            <el-option v-for="item in ageGroups" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>

      <!-- Center 活动类型 -->
      <el-form-item prop="centerThemeName"
        v-show="equalsIgnoreCase(lessonAssistantInfo.activityType, 'CENTER') || equalsIgnoreCase(lessonAssistantInfo.activityType, 'STATION')">
        <template #label>
          <label class="el-form-item__label">
            <span class="font-size-14">{{ getCenterOrStationLable }}</span>
          </label>
        </template>
        <el-select v-model="lessonAssistantInfo.centerThemeName" class="input-select w-full"
          :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')">
          <el-option v-for="center in centerTags" :key="center" :label="center" :value="center" />
        </el-select>
      </el-form-item>

        <!--框架-->
        <el-form-item prop="framework">
          <template #label>
            <label class="el-form-item__label">
              <span class="required-star font-size-14">*</span>
              <span class="font-size-14">{{ $t('loc.unitPlannerStep1AssessmentFramework') }}</span>
            </label>
          </template>
          <!-- 级联选择器，展示州和框架数据 -->
          <el-cascader @click.native="handleFrameworkClick" ref="stateStandardsCascader" v-model="selectedFrameworkData" :options="cascaderData"
            :props=cascaderProps :show-all-levels="false" @change="handleCascaderChange" class="w-full"
            :loading="frameworkLoading || loadFrameworkLoading"
            style="line-height: unset;"
            popper-class="public-lesson-assistantascader-class"></el-cascader>
        </el-form-item>

        <!-- 活动类型，目前被隐藏-->
        <el-form-item v-show="false" prop="activityType" class="">
          <template #label>
            <label class="el-form-item__label">
              <span class="required-star font-size-14">*</span>
              <span class="font-size-14">{{ $t('loc.unitPlannerStep3ConfirmationActivityType') }}</span>
            </label>
          </template>
          <!-- 引导步骤2 popover -->
          <el-popover placement="right" width="370" v-model="assistantToolbarGuide.showStep2"
            ref="assistantToolbarGuide2" popper-class="assistant-toolbar-guide-color-text" trigger="manual">
            <div class="text-white">
              <!-- 标题 -->
              <div class="title-font-20 lg-margin-bottom-12">{{ assistantToolbarGuide.title }}</div>
              <!-- 引导文字 -->
              <div class="lg-margin-bottom-24 word-break text-left">{{ assistantToolbarGuide.content }}</div>
              <div class="flex-space-between">
                <!-- 跳过 -->
                <div class="display-flex w-full justify-content-between align-items">
                  <!-- 步骤数 -->
                  <el-link :underline="false">({{ assistantToolbarGuide.step }}/3)</el-link>
                  <div class="display-flex flex-justify-end gap-6 align-items">
                    <!--上一步-->
                    <el-button type="primary" class="btn-back" @click="startGuide(1)">{{
                      $t('loc.assistantToolbarGuide10') }}</el-button>
                    <!-- 下一步 -->
                    <el-button type="primary" @click="startGuide(3)">{{
                      $t('loc.assistantToolbarGuide4')
                      }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            <el-select ref="activityTypeSelect" slot="reference" v-model="lessonAssistantInfo.activityType"
              class="input-select" :key="lessonAssistantInfo.activityType"
              :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')">
              <el-option v-for="type in activityTypes" :key="type.typeId" :label="type.typeName" :value="type.typeValue"
                :disabled="type.disabled" />
            </el-select>
          </el-popover>
        </el-form-item>
      </div>

      <!-- 选择学科 -->
      <el-form-item prop="domains" :rules="domainsRules">
        <template #label>
          <label class="el-form-item__label" style="width: 100%;">
            <div class="display-flex align-items justify-content-between">
              <div>
                <span class="required-star font-size-14">*</span>
                <span class="font-size-14">{{ $t('loc.unitPlannerStep1Standards') }}</span>
              </div>
              <!-- 测评点选择方式 -->
              <el-tabs v-model="selectedDomainMode" @tab-click="handleDomainModeChange" class="lg-tabs-small lg-tabs-assistant-domain-tabs"
                style="font-weight: 400;">
                <!-- 自动分配选项卡 -->
                <el-tab-pane name="auto" :disabled="shouldDisableAutoMode">
                  <span slot="label" class="display-flex">
                    {{ $t('loc.unitPlannerStep1MeasureAutoAdapt') }}
                    <el-tooltip class="item" effect="dark" :content="$t('loc.unitPlannerStep1MeasureAutoAdaptTip')"
                      placement="top">
                      <i v-show="source !== 'editPromptPopover'" class="lg-icon lg-icon-question lg-margin-left-4"></i>
                    </el-tooltip>
                  </span>
                </el-tab-pane>
                <!-- 精准选择选项卡 -->
                <el-tab-pane name="specific">
                  <span slot="label" class="display-flex">
                    {{ $t('loc.unitPlannerStep1MeasureSpecified') }}
                    <el-tooltip class="item" effect="dark" :content="$t('loc.unitPlannerStep1MeasureSpecifiedTip')"
                      placement="top">
                      <i v-show="source !== 'editPromptPopover'" class="lg-icon lg-icon-question lg-margin-left-4"></i>
                    </el-tooltip>
                  </span>
                </el-tab-pane>
              </el-tabs>
            </div>
          </label>
        </template>

        <!-- 按领域自动分配 -->
        <el-select
          v-if="equalsIgnoreCase(selectedDomainMode, 'auto')"
          ref="domainSelect"
          v-model="domainSelected"
          multiple
          :class="['w-full lesson-assistant-domain-select', {'has-selected-domain': domainSelected.length > 0}]"
          placeholder=""
          :loading="getMeasuresLoading"
          @remove-tag="deleteDomain"
          popper-class="lesson-assistant-domain-select-dropdown">
            <!-- 当 domainSelected 为空时才显示 prefix -->
            <template slot="prefix" v-if="!domainSelected.length">
              <i v-show="getMeasuresLoading" class="el-icon-loading font-size-20 lg-color-primary"></i>
              <i v-show="!getMeasuresLoading" class="lg-icon lg-icon-add1 lg-color-primary font-size-20"></i>
              <span class="font-weight-600"> {{ $t('loc.selectSubjects') }} </span>
            </template>
            <!-- 当有选中内容时显示 + icon -->
            <template slot="prefix" v-if="domainSelected.length > 0">
              <i class="lg-icon lg-icon-add1 lg-color-primary font-size-20"></i>
            </template>
          <el-option-group>
            <el-checkbox
              v-model="domainSelectAll"
              @change="handleSelectAllChange"
              :indeterminate="selectAllIndeterminate"
              class="domain-select-all w-full">
              <span class="font-weight-600">{{ lessonAssistantInfo.frameworkName }}</span>
            </el-checkbox>
          </el-option-group>
          <el-option
            v-for="item in treeData"
            :key="item.id"
            :label="item.name"
            :value="item.id">
              <el-checkbox
                :value="domainSelected.includes(item.id)"
                :label="item.name"
                >
              </el-checkbox>
          </el-option>
        </el-select>
        <!-- 按测评选择 -->
        <subject-selector
          v-if="equalsIgnoreCase(selectedDomainMode, 'specific')"
          ref="subjectSelector"
          :treeData="treeData"
          :unitProgress="10"
          :defaultProps="defaultProps"
          :loading="frameworkLoading || loadFrameworkLoading || getMeasuresLoading"
          :maxItems="8"
          :class="{'has-value': selectedItems && selectedItems.length > 0}"
          class="subject-selector"
          @update:selectedItems="handleUpdateSelectedItems"
          @update:nonLeafNodes="handleNonLeafNodes"
          @update:domainNodes="handleDomainNodes">
        </subject-selector>
      </el-form-item>
    </div>
  </el-form>
</template>
<script>

import { assistantToolState } from '@/mixins/assistantToolStateManager';
import {
  domainMax3,
  PublicLessonAssistantInfoActivityType,
  PublicLessonAssistantInfoAgeGroup,
  UnitPlanCenterTags,
  UnitPlanITCenterTags,
  UnitPlanKCenterTags,
  WeeklyPlanAssistantInfoAgeGroup,
} from '@/utils/constants';
import frameworkUtils from '@/utils/frameworkUtils';
import tools from '@/utils/tools';
import { mapState } from "vuex";
import SubjectSelector from './SubjectSelector.vue';
import { equalsIgnoreCase } from '@/utils/common'

export default {
  mixins: [assistantToolState],
    components: {
      SubjectSelector
    },
    name: "PublicLessonAssistantInfo",
    props: {
        defaultCountry: {
          type: String,
          required: true
        },
        defaultState: {
          type: String,
          required: true,
        },
        states: {
          type: Array,
          required: true,
        },
        // 待解析的课程信息
        lessonAssistantInfo: {
            type: Object,
            required: true
        },

        // 框架列表
        frameworks: {
            type: Array,
            default: () => []
        },

        // 加载框架的 loading
        loadFrameworkLoading: {
            type: Boolean,
            required: true
        },


        // 启动自动测评点建议
        enableAutomaticMeasure: {
            type: Boolean,
            required: true
        },
      // 课程助手工具栏指南
      assistantToolbarGuide: {
          type: Object,
          required: true
      },
      // 启动指南
      startGuideFunc: {
          type: Function,
          required: true
      },
      // 是否在 Weekly Planning 模块引用
      inWeeklyPlannDialog: {
        type: Boolean,
        default: () => false
      },
      // 在 Weekly Planning 模块中已经默认选择的框架
      selectedFramework: {
        type: Object,
        default: () => null
      },
      // 来自助手的历史测评点
      hisMeasureIds: {
        type: Array,
        required: false,
        default: () => null
      },
      // 恢复 AssistantTool 状态 loading
      restoreAssistantToolStateLoading: {
        type: Boolean,
        required: false,
        default: () => false
      },
      // 来源
      source: {
        type: String,
        default: () => null
      },
      // 来源
      assistantToolState: {
        type: Object,
        default: () => null
      }
    },

  data() {
    return {
      enableAutomaticMeasureBak: this.enableAutomaticMeasure,
      ageGroups: [...PublicLessonAssistantInfoAgeGroup].reverse(),
      weeklyPlanAgeGroup: WeeklyPlanAssistantInfoAgeGroup,
      activityTypes: PublicLessonAssistantInfoActivityType,
      centerTags: UnitPlanCenterTags, // centers 组标签
      selectedGrade: 'TK (4-5)', // 当前选中的年级 是年级数据的 name，非年级 id
      selectedFrameworkData: [], // 当前选中的级联选项
      measures: [], // 测评点数据
      treeData: [], // 用于存储树形结构的数据
      treeDataFrameworkId : null, // 用于存储树形结构的数据对应的框架 ID
      selectedItems: [], // 选中的测评点
      nonLeafNodes: [], // 非叶子节点 (Domains)
      defaultProps: {
        children: 'children',
        label: 'abbreviation'
      },
      domainsRules: [
        { required: true, validator: this.domainsValidator, trigger: 'blur' }
      ], // 学科校验规则
      domainSelectAll: false, // 学科全选状态
      domainSelected: [], // 选择的学科数据
      getMeasuresLoading: false, // 查询测评点 loading
      preSelectedDomainMode: 'auto', // 预选学科选择模式：auto-自动分配，specific-精准选择
      selectedDomainMode: 'auto', // 学科选择模式：auto-自动分配，specific-精准选择
      domainMax3: domainMax3, // 学科选择最大限制
      frameworkLoading: false, // 添加框架加载状态
      subjectsDomainsTabsCache: {
        'auto': {},
        'specific': {}
      }, // 学科或测评点 Tab 缓存,用于切换学科选择模式时回显
    }
  },

    created() {
      // 如果是正在恢复助手状态，则不需要初始化
      if (this.assistantToolState && this.assistantToolState.lessonAssistantInfo) {
        return
      }
      const needLessonWelcomeGuide = this.currentUser.needLessonWelcomeGuide
      if (needLessonWelcomeGuide) {
        return
      }
      this.init()
    },
    mounted () {
      // 学科选择数据初始化
      this.domainSelected = this.lessonAssistantInfo.domains || []
    },

    watch:{
      // 监听年龄组的变化
      'lessonAssistantInfo.ageGroup': {
        handler: function (newGroup) {
          if (!this.inWeeklyPlannDialog) {
            this.ageGroupsChange(newGroup)
          } else {
            this.weeklyPlanAgeGroupsChange(newGroup)
          }
          // 需要根据年龄组展示不同的 centers 组
          this.getCentersByAgeGroup(newGroup, this.lessonAssistantInfo.centerThemeName)

          // 更新 selectedGrade 值，确保下拉框显示正确的选中状态
          const ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.value, newGroup))
          if (ageGroup) {
            this.selectedGrade = ageGroup.name
          }
        },
        deep: true
      },
      // 监听活动类型的变化
      'lessonAssistantInfo.activityType': {
        handler: function (activityType) {
          this.$emit('changeActivityType', activityType)
        },
        // 在定义的时候就会立即执行一次
        immediate: true,
        deep: true
      },
      // 监听当前选中的学科
      'domainSelected': {
        deep: true,
        handler (newDomains, oldDomains) {
          if (newDomains && newDomains.length > domainMax3 && this.isOlderThanK) {
            this.$message.warning(this.$t('loc.unitPlannerDomainMaxThree'))
            this.domainSelected = oldDomains
          }
          // 如果删除了学科
          if (newDomains && oldDomains && newDomains.length < oldDomains.length) {
            // 获取删除的学科
            const deletedItems = oldDomains.filter(item => !newDomains.includes(item))
            // 获取删除的学科及 测评点数据
            const deletedDomain = this.treeData.filter(item => deletedItems.includes(item.id))
            // 获取该学科下最底层测评点
            let deletedDomainMeasure = []
            // 递归获取所有底层测评点
            frameworkUtils.getMeasuresBottom(deletedDomain, deletedDomainMeasure)
            // 获取所有最底层测评点 Id
            let deletedDomainMeasureIds = deletedDomainMeasure.map(item => item.id)
            // 删除选择的该学科下的测评点
            this.$set(this, 'selectedItems', this.selectedItems.filter(item => !deletedDomainMeasureIds.includes(item.id)))
            // 将选中的测评点赋值给 lessonAssistantInfo.measureIds
            this.lessonAssistantInfo.measureIds = this.selectedItems.map(item => item.id)
          } else if (newDomains && oldDomains && newDomains.length > oldDomains.length) {
            this.validateDomains()
          }

          // 修改学科全选状态
          this.domainSelectAll = (this.domainSelected && this.domainSelected.length >= domainMax3 && this.isOlderThanK)
            || (this.domainSelected && this.domainSelected.length >= this.treeData.length && this.domainSelected.length !== 0)
          // 赋值给 lessonAssistantInfo.domains
          this.$set(this.lessonAssistantInfo, 'domains',  this.domainSelected)
        }
      },
      // 监听 useDomain 的变化
      'lessonAssistantInfo.useDomain': {
        immediate: true,
        deep: true,
        handler: function (newUseDomain) {
          this.selectedDomainMode = newUseDomain ? 'auto' : 'specific'
        },
      },
      // 如果有 measureIds 的变化, 如果有值，则重新设置学科选择模式
      'lessonAssistantInfo.measureIds': {
        immediate: true,
        deep: true,
        handler: function (newMeasureIds) {
          if (newMeasureIds && newMeasureIds.length > 0) {
            this.lessonAssistantInfo.haveSelectMeasureId = true
          } else {
            this.lessonAssistantInfo.haveSelectMeasureId = false
          }
          // 同步 this.subjectsDomainsTabsCache 中的值
          if (equalsIgnoreCase(this.selectedDomainMode, 'auto')) {
            if (!this.subjectsDomainsTabsCache['auto']) {
              this.subjectsDomainsTabsCache['auto'] = {}
            }
            this.subjectsDomainsTabsCache['auto'].measureIds = newMeasureIds
          } else {
            if (!this.subjectsDomainsTabsCache['specific']) {
              this.subjectsDomainsTabsCache['specific'] = {}
            }
            this.subjectsDomainsTabsCache['specific'].measureIds = newMeasureIds
          }
        }
      },
      // 如果有 domains 的变化, 如果有值，则重新设置学科选择模式
      'lessonAssistantInfo.domains': {
        immediate: true,
        deep: true,
        handler: function (newDomains) {
          // 初始化 this.subjectsDomainsTabsCache 中的值
          if (!this.subjectsDomainsTabsCache['auto']) {
            this.subjectsDomainsTabsCache['auto'] = {}
          }
          if (!this.subjectsDomainsTabsCache['specific']) {
            this.subjectsDomainsTabsCache['specific'] = {}
          }
          // 同步 this.subjectsDomainsTabsCache 中的值
          if (equalsIgnoreCase(this.selectedDomainMode, 'auto')) {
            this.subjectsDomainsTabsCache['auto'].domains = newDomains
          } else {
            this.subjectsDomainsTabsCache['specific'].domains = newDomains
          }
        }
      },
      // 监听 selectedDomainMode 的变化
      'selectedDomainMode': {
        immediate: true,
        handler: function (newSelectedDomainMode) {
          this.preSelectedDomainMode = newSelectedDomainMode
        }
      },
      // 监听是否应该禁用自动分配模式
      shouldDisableAutoMode: {
        immediate: true,
        handler: function (shouldDisable) {
          // 如果需要禁用自动分配模式且当前是自动分配模式，则切换到精准选择模式
          if (shouldDisable && this.selectedDomainMode === 'auto') {
            this.selectedDomainMode = 'specific'
            this.lessonAssistantInfo.useDomain = false
          }
        }
      }
    },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      isMC: state => state.curriculum.isMC, // 是否是 MC
    }),
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    // 级联选择器的 props
    cascaderProps () {
      return {
        value: 'gradeFrameworkId',
        label: 'frameworkName',
        children: 'frameworks',
        disabled: 'isDisabled'
      }
    },
     // 级联选择器的数据
    cascaderData () {
      return tools.getFrameworkData(this.frameworkData, this.selectedGrade)
    },
    // Center 或 Station 标签名
    getCenterOrStationLable(){
      return equalsIgnoreCase(this.lessonAssistantInfo.activityType, 'CENTER') ? 'Center' : 'Station'
    },
    // 根据所选学科过滤出当前学科下的测评点
    domainTreeData () {
      // 如果是 K (5-6) 及以上，则只显示当前学科下的测评点
      if (this.isOlderThanK && this.domainSelected && this.domainSelected.length > 0) {
        return this.treeData.filter(item => this.domainSelected.includes(item.id))
      } else {
        return this.treeData
      }
    },
    // 获取当前选择的年龄是否为 'K (5-6)' 及以上
    isOlderThanK () {
      // 获取当前选中的年级的详细数据，获取其中的 value 值
      const age = this.ageGroups.find(item => equalsIgnoreCase(item.name, this.lessonAssistantInfo.ageGroup) || equalsIgnoreCase(item.value, this.lessonAssistantInfo.ageGroup))
      if (!age) {
        return false
      }
      // 获取年龄值
      const ageValue = tools.getAgeValue(age.name)
      // 判断年龄是否为 'K (5-6)' 及以上
      return ageValue ? ageValue >= 6 : false
    },
    // 选择学科按钮是否全选的半选状态
    selectAllIndeterminate () {
      // 只要选项中有一个是选中的状态，则半选状态为 true
      return this.domainSelected && this.domainSelected.length > 0 && !this.domainSelectAll
    },

    /**
     * 获取助手工具栏状态
     */
     frameworkData: {
      get() {
        return this.localState.frameworkData;
      },
      set(value) {
        this.updateFrameworkData(value);
      }
    },

    /**
     * 是否正在应用提示中
     */
    applyPrompting: {
      get() {
        return this.localState.applyPrompting;
      },
      set(value) {
        this.updateApplyPrompting(value);
      }
    },

    // 判断是否应该禁用自动分配模式
    shouldDisableAutoMode () {
      // 如果框架名称是 Texas K12 Standards 且年级是 Grade 10、Grade 11、Grade 12，则禁用自动分配
      return this.lessonAssistantInfo.frameworkName === 'Texas K12 Standards' &&
        ['Grade 10', 'Grade 11', 'Grade 12'].includes(this.lessonAssistantInfo.ageGroup)
    }
  },

      methods: {
        handleAgeGroupClick() {
          if (this.$route.name === 'AddLesson' && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
            // 创建课程第一步的年龄组点击埋点
            this.$analytics.sendEvent('cg_lesson_plan_cre_select_grade')
          }
        },
        handleFrameworkClick() {
          if (this.$route.name === 'AddLesson' && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
            // 创建课程第一步的框架点击埋点
            this.$analytics.sendEvent('cg_lesson_plan_cre_select_standard')
          }
        },
        /**
         * 不区分大小写比较两个字符串是否相等
         */
        equalsIgnoreCase,
        init () {
          if (this.lessonAssistantInfo.isHistoryData) {
            this.applyLessonAssistantInfo(this.lessonAssistantInfo)
            return
          }
          // 初始化 ActivityType
          this.initActivityType()
          if (!this.inWeeklyPlannDialog) {
            // 初始化 selectedDomainMode 值
            this.selectedDomainMode = this.lessonAssistantInfo.useDomain ? 'auto' : 'specific'
            let selectedGrade = this.getAgeGroupName(this.lessonAssistantInfo.ageGroup)
            if (selectedGrade) {
              this.selectedGrade = selectedGrade
            }
            // 初始化获取框架数据
            this.getAllFrameworkData().then(() => {

              //   // // 不是历史数据，判断本地是否有缓存，如果本地有缓存，则使用缓存数据
              //   // let lessonAssistantInfo = JSON.parse(localStorage.getItem('LESSON_ASSISTANT_INFO' + this.currentUserId) || '{}')
              //   // if (lessonAssistantInfo.state && lessonAssistantInfo.frameworkId && lessonAssistantInfo.ageGroup) {
              //   //   // 非公共数据
              //   //   this.selectedGrade = lessonAssistantInfo.ageGroup
              //   //   // 本地缓存数据回填
              //   //   this.commonHistoryDataFill(lessonAssistantInfo, lessonAssistantInfo.ageGroupValue)
              //   //   return
              //   // }
              // }
              // 设置默认级联选择器值
              this.setDefaultSelections(this.lessonAssistantInfo.frameworkState, this.lessonAssistantInfo.frameworkId)
              // 设置活动类型可选项
              let ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.name, this.selectedGrade))
              this.activityTypes.forEach(item => {
                this.$set(item, 'disabled', !ageGroup.hasActivity.includes(item.typeId))
              })
            })
          } else {
            // 初始化获取框架数据
            this.getAllFrameworkData().then(() => {
              this.weeklyPlanAgeGroupsChange(this.selectedFramework.grade)
              // 需要根据年龄组展示不同的 centers 组
              this.getCentersByAgeGroup(this.selectedFramework.grade)
              // 获取年龄组的详细信息
              let ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.value, this.selectedFramework.grade))
              if (ageGroup) {
                // 周计划年龄赋值到当前选择的年龄
                this.selectedGrade = ageGroup.name
              }
              // 初始化 selectedDomainMode 值
              this.selectedDomainMode = this.lessonAssistantInfo.useDomain ? 'auto' : 'specific'
              // 获取测评点数据
              this.setDefaultSelections(this.lessonAssistantInfo.frameworkState, this.lessonAssistantInfo.frameworkId)
            })
          }
        },

        getAgeGroupName(ageGroupValue) {
          if (!ageGroupValue) {
            return ''
          }
          let ageGroupName = ''
          this.ageGroups.forEach(ageGroup => {
            if (ageGroup.value && equalsIgnoreCase(ageGroup.value.trim(), ageGroupValue.trim())) {
              ageGroupName = ageGroup.name
            }
          })
          return ageGroupName
        },

        // 新增：应用课程助手信息
        applyLessonAssistantInfo(lessonAssistantInfo) {
          // 使用计算属性设置 applyPrompting 状态
          this.applyPrompting = true
          // 如果 frameworkData 有内容，直接处理
          if (this.frameworkData && this.frameworkData.length > 0) {
            this.handleApplyLessonAssistantInfo(lessonAssistantInfo)
          } else {
            // 如果没有内容，先获取 frameworkData 再处理
            this.getAllFrameworkData().then(() => {
              this.handleApplyLessonAssistantInfo(lessonAssistantInfo)
            })
          }
        },

        // 处理应用课程助手信息的具体逻辑
        handleApplyLessonAssistantInfo(lessonAssistantInfo) {

          // 设置 useDomain 值（是否按领域自动分配）
          this.lessonAssistantInfo.useDomain = lessonAssistantInfo.useDomain
          // 设置 useDomain 值（是否按领域自动分配）
          this.selectedDomainMode = lessonAssistantInfo.useDomain ? 'auto' : 'specific';

          // 设置年龄组信息
          if (lessonAssistantInfo.ageGroup) {
            // 查找对应的年龄组
            const ageGroup = this.ageGroups.find(
              item => equalsIgnoreCase(item.name, lessonAssistantInfo.ageGroup) || equalsIgnoreCase(item.value, lessonAssistantInfo.ageGroup)
            )
            if (ageGroup) {
              // 设置年龄组值（使用 value）
              this.lessonAssistantInfo.ageGroup = ageGroup.value
              // 保存年龄组名称，用于显示
              this.lessonAssistantInfo.ageGroupName = ageGroup.name
              this.selectedGrade = ageGroup.name
            }
          }
          // 设置 activityType，如果 activityType 为空，则初始化 activityType
          if (!lessonAssistantInfo.activityType) {
            this.initActivityType()
          } else { // 如果 activityType 不为空，则直接赋值
            this.lessonAssistantInfo.activityType = lessonAssistantInfo.activityType
          }

          // 设置 center 主题
          this.lessonAssistantInfo.centerThemeName = lessonAssistantInfo.centerThemeName || ''
          this.getCentersByAgeGroup(this.lessonAssistantInfo.ageGroup, lessonAssistantInfo.centerThemeName)
          // 通过框架 ID 找到对应的框架信息
          this.getDefFrameworkById(lessonAssistantInfo.frameworkId)

          let domainIds = lessonAssistantInfo.domains
          let measureIds = lessonAssistantInfo.measureIds
          // 设置 domains 和 measures - 先清空再设置，避免重复
          this.clearMeasureAndDomainSelections()
          // 获取测评点数据
          this.fetchMeasuresFromBackend(this.lessonAssistantInfo.frameworkId, domainIds, measureIds)
        },

        // 新增：统一处理 Activity Type 的设置
        initActivityType() {
          let grade = this.lessonAssistantInfo.ageGroup
          const ageGroup = this.ageGroups.find(
            item => equalsIgnoreCase(item.name, grade) || equalsIgnoreCase(item.value, grade)
          )

          if (!this.inWeeklyPlannDialog) {
            this.lessonAssistantInfo.centerThemeName = ''
            // 如果年龄组为 K 以上的，则使用 activityType 为 TEACHER_LED_ACTIVITY 否则为 LARGE_GROUP
            if (ageGroup && ['0', '1', '2', '3', '4'].includes(ageGroup.value)) {
              this.lessonAssistantInfo.activityType = 'LARGE_GROUP'
            } else if (!this.inWeeklyPlannDialog) {
              this.lessonAssistantInfo.activityType = 'TEACHER_LED_ACTIVITY'
            }
          }

          if (this.inWeeklyPlannDialog) {
            this.weeklyPlanAgeGroupsChange(ageGroup.value)
            // // Station 满足的年龄范围
            // const stationGrades = ['5', 'Grade 1', 'Grade 2']
            // let defaultActivityType = null
            // // 如果从 Center/Station 进入，且年龄为 K - Grede 2 ，则默认选择 Station
            // if (stationGrades.includes(ageGroup.value)) {
            //   defaultActivityType = this.activityTypes.find(item => equalsIgnoreCase(item.typeValue, 'STATION'))
            // } else {
            //   // 修改 activityType 为 disable 为 false 的默认的第一个值
            //   defaultActivityType = this.activityTypes.find(item => !item.disabled)
            // }
            // if (defaultActivityType) {
            //   this.lessonAssistantInfo.activityType = defaultActivityType.typeValue
            // }
          }

          if (!this.inWeeklyPlannDialog) {
            this.lessonAssistantInfo.centerThemeName = ''
          }
          // 如果 Activity Type 不是 CENTER 和 STATION 时，直接将 centerThemeName 设置为空
          if (equalsIgnoreCase(this.lessonAssistantInfo.activityType, 'CENTER') || equalsIgnoreCase(this.lessonAssistantInfo.activityType, 'STATION')) {
            this.lessonAssistantInfo.centerThemeName = ''
          }
        },
        // 全选/取消全选
        handleSelectAllChange(checked) {
          // 如果当前已经达到限制，点击全选按钮应取消所有选择。否则按照顺序选择 3 个
          if (!checked) {
            this.domainSelected = []
          } else {
            // 距离达到限制缺少的个数
            let lackCount = domainMax3 - this.domainSelected.length
            // 按照顺序选择缺少的 domain
            for (let i = 0; i < this.treeData.length; i++) {
              // 如果当前学科未选择
              if (!this.domainSelected.includes(this.treeData[i].id)) {
                // 未选择则选择
                this.domainSelected.push(this.treeData[i].id)
                // 如果不存在缺少的则退出
                if (this.isOlderThanK) {
                  if (--lackCount <= 0) {
                    break
                  }
                }
              }
            }
          }
          this.validateDomains()
        },
        // 删除学科
        deleteDomain (domainId) {
          // 更新 domainSelected 数组
          this.domainSelected = this.domainSelected.filter(item => item !== domainId)
          this.validateDomains()
        },
        // 校验学科/领域自定义方法
        domainsValidator (rule, value, callback) {
          // 如果是按照领域自动分配测评点，则检查 domainSelected 是否有值
          if (equalsIgnoreCase(this.selectedDomainMode, 'auto')) {
            if (!this.domainSelected || this.domainSelected.length === 0) {
              callback(this.$t('loc.fieldReq'))
            } else {
              callback()
            }
          } else {
            // 如果是精准选择，则检查 selectedItems 是否有值
            if (!this.selectedItems || this.selectedItems.length === 0) {
              callback(this.$t('loc.fieldReq'))
            } else {
              callback()
            }
          }
        },
        // 手动触发校验学科/领域
        validateDomains () {
          this.$refs.fromRef && this.$refs.fromRef.validateField('domains')
        },
        // 在周计划时当年龄组发生变化的时候
        weeklyPlanAgeGroupsChange (value) {
          // 获取活动类型的详细信息
          let activityType = this.weeklyPlanAgeGroup.find(item => equalsIgnoreCase(item.typeName, this.selectedFramework.categoryName))
          if (!activityType) {
            activityType = this.weeklyPlanAgeGroup.find(item => equalsIgnoreCase(item.typeName, 'Activities'))
          }

          // 获取年龄组的详细信息
          let ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.value, value))
          if (!ageGroup) {
            ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.name, 'TK (4-5)'))
          }

          // 周计划的活动类型和年龄可选择的活动类型的交集
          const intersection = activityType.hasActivity.filter(value => ageGroup.hasActivity.includes(value))
          // 过滤可选择的活动类型
          this.activityTypes.forEach(item => {
            this.$set(item, 'disabled', !intersection.includes(item.typeId))
          })

          // Station 满足的年龄范围
          const stationGrades = ['5', 'Grade 1', 'Grade 2']
          let defaultActivityType = null
          // 如果从 Center/Station 进入，且年龄为 K - Grede 2 ，则默认选择 Station
          if (equalsIgnoreCase(activityType.typeValue, 'CENTER') && stationGrades.includes(ageGroup.value)) {
            defaultActivityType = this.activityTypes.find(item => equalsIgnoreCase(item.typeValue, 'STATION'))
          } else {
            // 修改 activityType 为 disable 为 false 的默认的第一个值
            defaultActivityType = this.activityTypes.find(item => !item.disabled)
          }
          if (defaultActivityType) {
            this.lessonAssistantInfo.activityType = defaultActivityType.typeValue
          }
        },
        // 更新选中的测评点
        handleUpdateSelectedItems(newSelectedItems) {
          this.selectedItems = newSelectedItems;
          // 将选中的测评点赋值给 lessonAssistantInfo.measureIds
          this.lessonAssistantInfo.measureIds = this.selectedItems.map(item => item.id);
          this.lessonAssistantInfo.haveSelectMeasureId = true
          // 验证领域和测评点
          this.validateDomains()
        },
        // 对于获取的历史助手数据，进行处理
        handleHistoryAssistantData () {
          if (this.lessonAssistantInfo.isHistoryData) {
            // 历史表单缓存数据回填
            this.selectedGrade = this.lessonAssistantInfo.ageGroupName
            this.commonHistoryDataFill(this.lessonAssistantInfo, this.lessonAssistantInfo.ageGroup)
            return true
          } else {
            return false
          }
        },
        // 本地缓缓存和历史表单数据公用的回填数据
        commonHistoryDataFill (lessonAssistantInfo, ageGroupVal) {
          // center 主题进行指定初始化
          const defCenterTheme = lessonAssistantInfo.centerThemeName
          if (defCenterTheme) {
            this.getCentersByAgeGroup(ageGroupVal)
            // 还原默认主题
            lessonAssistantInfo.centerThemeName = defCenterTheme
            // 对于本地缓存的数据，需要特殊再对this.lessonAssistantInfo.centerThemeName进行赋值
            this.lessonAssistantInfo.centerThemeName = defCenterTheme
          }
          // 选中框架还原
          this.getDefFrameworkById(lessonAssistantInfo.frameworkId)
          lessonAssistantInfo.frameworkId = this.selectedFrameworkData[1]
          // 设置活动类型可选项
          let ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.name, this.selectedGrade))
          this.activityTypes.forEach(item => {
            this.$set(item, 'disabled', !ageGroup.hasActivity.includes(item.typeId))
          })
          // 获取测评点数据
          this.fetchMeasuresFromBackend(lessonAssistantInfo.frameworkId)
        },

        // 判断当前州下的框架是否存在，不存在取默认值
        getDefFrameworkById (frameworkId) {
          let haveStateFramework = false
          let frameworkCountry = ''
          let frameworkState = ''
          let frameworkName = ''
          for (const countryData of this.frameworkData) {
            for (const stateData of countryData.stateFrameworkInfo) {
              for (const framework of stateData.stateFrameworkInfo.frameworks) {
                if (equalsIgnoreCase(framework.gradeFrameworkIdMap[this.selectedGrade], frameworkId)) {
                  haveStateFramework = true
                  frameworkCountry = countryData.country
                  frameworkState = stateData.state
                  frameworkName = framework.frameworkName
                  break
                }
              }
              if (haveStateFramework) break // 退出外层循环
            }
            if (haveStateFramework) break // 退出外层循环
          }
          // 如果没有合适的框架，说明数据来自于周计划，根据年龄查找适合的框架
          if (!haveStateFramework) {
            this.findFrameworkByAge()
          } else {
            // 特殊处理墨西哥和 IB，不填充州信息
            if (frameworkUtils.isCountryWithState(frameworkCountry)) {
              this.selectedFrameworkData = [frameworkCountry, frameworkState, frameworkId]
            } else {
              this.selectedFrameworkData = [frameworkCountry, frameworkId]
            }
            this.lessonAssistantInfo.frameworkName = frameworkName
            this.lessonAssistantInfo.frameworkId = frameworkId
            this.lessonAssistantInfo.frameworkState = frameworkState
          }
        },

        // 根据年龄查找适合的框架
        findFrameworkByAge () {
          // 设置默认年级
          const defaultGrade = this.selectedGrade
          let found = false
          let frameworkName = ''

          // 获取默认州和默认年级对应的框架ID
          const defaultCountryData = this.frameworkData.find(countryData => countryData.country === this.defaultCountry)
          const defaultFrameworkData = defaultCountryData && defaultCountryData.stateFrameworkInfo.find(stateData => stateData.state === this.defaultState)
          if (defaultFrameworkData) {
            const defaultFramework = defaultFrameworkData.stateFrameworkInfo.frameworks.find(framework => framework.frameworkName.indexOf('K12 Standards') > 0 && framework.gradeFrameworkIdMap[defaultGrade]) ||
              defaultFrameworkData.stateFrameworkInfo.frameworks.find(framework => framework.gradeFrameworkIdMap[defaultGrade])
            if (defaultFramework) {
              // 特殊处理墨西哥和 IB，不填充州信息
              if (frameworkUtils.isCountryWithState(defaultCountryData.country)) {
                this.selectedFrameworkData = [defaultCountryData.country, defaultFrameworkData.state, defaultFramework.gradeFrameworkIdMap[defaultGrade]]
              } else {
                this.selectedFrameworkData = [defaultCountryData.country, defaultFramework.gradeFrameworkIdMap[defaultGrade]]
              }
              found = true
            }
          }

          // 如果在默认州中没有找到，则遍历所有州和框架
          if (!found) {
            for (const countryData of this.frameworkData) {
              for (const stateData of countryData.stateFrameworkInfo) {
                for (const framework of stateData.stateFrameworkInfo.frameworks) {
                  if (framework.gradeFrameworkIdMap[defaultGrade]) {
                    // 特殊处理墨西哥和 IB，不填充州信息
                    if (frameworkUtils.isCountryWithState(countryData.country)) {
                      this.selectedFrameworkData = [countryData.country, stateData.state, framework.gradeFrameworkIdMap[defaultGrade]]
                    } else {
                      this.selectedFrameworkData = [countryData.country, framework.gradeFrameworkIdMap[defaultGrade]]
                    }
                    frameworkName = framework.frameworkName
                    found = true
                    break
                  }
                }
                if (found) break
              }
            }
          }
          this.lessonAssistantInfo.frameworkName = frameworkName
          this.lessonAssistantInfo.frameworkId = this.selectedFrameworkData[1]
          this.lessonAssistantInfo.frameworkState = this.selectedFrameworkData[0]
        },
        // 删除选中的测评点
        deleteMeasure(value) {
          // 在父组件中调用子组件的 removeSelectedItem 方法
          this.$refs.subjectSelector.removeSelectedItem(value);
          // 从 selectedItems 中删除选中的测评点
          this.selectedItems = this.selectedItems.filter(item => item.id !== value.id);
          // 将选中的测评点赋值给 lessonAssistantInfo.measureIds
          this.lessonAssistantInfo.measureIds = this.selectedItems.map(item => item.id);
          if (this.selectedItems.length === 0) {
            this.lessonAssistantInfo.haveSelectMeasureId = false
          }
          // 验证领域和测评点
          this.validateDomains()
        },
        // 处理领域和测评点的选中状态
        handleDomainsAndMeasuresSelection(frameworkId, treeData, domainIds, measureIds) {
          this.$nextTick(() => {
            this.treeDataFrameworkId = frameworkId
            this.treeData = treeData
            domainIds = domainIds || []
            measureIds = measureIds || []

            // 更新 domains 选中状态
            // 过滤出有效的 domainIds
            const validDomainIds = domainIds.filter(id =>
              this.treeData.some(domain => equalsIgnoreCase(domain.id, id))
            );

            // 更新 domainSelected 数组
            this.domainSelected = validDomainIds;
            this.lessonAssistantInfo.domains = validDomainIds

            // 更新 domainSelectAll 状态
            this.domainSelectAll = (validDomainIds.length >= domainMax3 && this.isOlderThanK)
              || (validDomainIds.length >= this.treeData.length && validDomainIds.length !== 0);

            // 处理 measures 的选中状态
            // 获取所有有效的 measures
            let validMeasures = []
            frameworkUtils.collectMeasures(validMeasures, this.treeData);

            // 根据 measureIds 找到对应的有效 measure 对象
            const selectedMeasures = validMeasures.filter(measure =>
              measureIds.includes(measure.id)
            );

            // 更新 selectedItems
            this.selectedItems = selectedMeasures;
            if (this.$refs.subjectSelector) {
              // 如果有 SubjectSelector 组件引用，更新其选中项
              this.$refs.subjectSelector.setSelectedItems(this.selectedItems)
            }
            // 设置测评点信息
            this.lessonAssistantInfo.measureIds = selectedMeasures.map(measure => measure.id)
            this.lessonAssistantInfo.haveSelectMeasureId = this.lessonAssistantInfo.measureIds.length > 0
            // 使用计算属性设置 applyPrompting 状态
            this.applyPrompting = false
          })
        },

        /**
         * 重置所有测评点和领域的选择状态
         * 包括:
         * 1. 清空领域全选状态
         * 2. 清空已选择的领域列表
         * 3. 清空已选择的测评点列表
         * 4. 重置测评点选择器组件状态
         * 5. 重置测评点选中标记
         */
        clearMeasureAndDomainSelections() {
          // 重置领域全选状态
          this.domainSelectAll = false
          // 清空已选择的领域列表
          this.domainSelected = []
          // 清空已选择的测评点列表
          this.selectedItems = []
          // 如果存在测评点选择器组件,重置其状态
          if (this.$refs.subjectSelector) {
            this.$refs.subjectSelector.setSelectedItems([])
          }
          // 重置测评点选中标记
          this.lessonAssistantInfo.haveSelectMeasureId = false
        },

        /**
         * 从后端获取测评点数据, 并处理测评点数据
         *
         * @param frameworkId 框架ID
         * @param domainIds 领域ID
         * @param measureIds 测评点ID
         */
        fetchMeasuresFromBackend(frameworkId, domainIds, measureIds) {
          // 显示加载状态
          this.getMeasuresLoading = true
          if (frameworkId && equalsIgnoreCase(frameworkId, this.treeDataFrameworkId) && this.treeData && this.treeData.length > 0) {
            this.getMeasuresLoading = false
            if (domainIds && domainIds.length > 0 || measureIds && measureIds.length > 0) {
              this.handleDomainsAndMeasuresSelection(frameworkId, this.treeData, domainIds, measureIds)
            }
            this.applyPrompting = false
            return
          }
          this.treeDataFrameworkId = null
          // 清空测评点数据
          this.treeData = [];
          // 重置测评点信息
          this.clearMeasureAndDomainSelections()
          // 使用 Vuex 中的 getFrameworkDomains 方法获取测评点数据
          this.$store.dispatch('curriculum/getFrameworkDomains', { 
            frameworkId: frameworkId,
            compress: false
          })
            .then(measures => {
              // 处理响应，将测评点数据存储在合适的属性中
              this.treeDataFrameworkId = frameworkId
              this.treeData = measures;
              // 结束加载状态
              this.getMeasuresLoading = false
              if (domainIds && domainIds.length > 0 || measureIds && measureIds.length > 0) {
                this.handleDomainsAndMeasuresSelection(frameworkId, this.treeData, domainIds, measureIds)
              }
              // 清除表单校验状态
              this.$refs.fromRef && this.$refs.fromRef.clearValidate('domains');
            })
            .catch(error => {
              // 结束加载状态
              this.getMeasuresLoading = false
            }).finally(() => {
              // 使用计算属性设置 applyPrompting 状态
              this.applyPrompting = false
            });
        },
        // 判断是否有回填测评点数据，有的话回填
        hisMeasureIdsFill () {
          if (this.hisMeasureIds && this.hisMeasureIds.length > 0 && this.treeData.length > 0) {
            let measures = []
            frameworkUtils.collectMeasures(measures, this.treeData)
            measures.forEach(item => {
              if (this.hisMeasureIds.includes(item.id)) {
                this.selectedItems.push(item)
              }
            })
            // 回填测评点树形结构选择弹框数据
            this.$refs.subjectSelector.setSelectedItems(this.selectedItems)
          }
        },
      startGuide (step) {
        // this.startGuideFunc(step)
      },
      // 修改：年龄组变化的处理方法
      ageGroupsChange(value) {
        // 获取年龄组的 hasActivity
        const ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.value, value))
        // 如果年龄组存在
        if (ageGroup) {
          // 如果年龄组有 hasActivity
          if (ageGroup.hasActivity) {
            // 设置活动类型可选项
            this.activityTypes.forEach(item => {
              this.$set(item, 'disabled', !ageGroup.hasActivity.includes(item.typeId))
            })
          }
        }

        this.initActivityType()
        // this.$nextTick(() => {
        //   const activityType = this.$refs.activityTypeSelect;
        //   if (activityType) {
        //     activityType.visible = true; // 打开下拉菜单
        //     setTimeout(() => {
        //       activityType.visible = false; // 关闭下拉菜单
        //     }, 1); // 设置延迟以确保效果
        //   }
        // });
      },
      // 根据年龄组获取 centers 组，使用的是年龄组的 ID ，非 name
      getCentersByAgeGroup(val, centerThemeName) {
        var grade = val || this.lessonAssistantInfo.ageGroup
        // 获取 centerThemeName
        const centerThemeNameResult = this.getCenterThemeNameByAgeGroup(grade, centerThemeName)
        this.centerTags = centerThemeNameResult.centerTags
        this.lessonAssistantInfo.centerThemeName = centerThemeNameResult.centerThemeName

        // 如果 Activity Type 不是 CENTER 和 STATION 时，直接将 centerThemeName 设置为空
        if (this.lessonAssistantInfo.activityType !== 'CENTER' && this.lessonAssistantInfo.activityType !== 'STATION') {
          this.lessonAssistantInfo.centerThemeName = ''
        }
      },

      // 根据年龄组和 centerThemeName 获取对应的 centerThemeName
      getCenterThemeNameByAgeGroup(grade, centerThemeName) {
        const iTGrades = ['0', '1', '2']
        const psTkGrades = ['3', '4']
        let result = {
          centerTags: [],
          centerThemeName: ''
        }

        // 如果 grade 是 Infant (0-1) 或 Young Toddler (1-2), Toddler (2-3) 则显示 IT
        if (iTGrades.includes(grade)) {
          result.centerTags = UnitPlanITCenterTags
          // 如果传入了 centerThemeName 且在 centerTags 中存在，则使用传入的值
          result.centerThemeName = centerThemeName && UnitPlanITCenterTags.some(tag => equalsIgnoreCase(tag, centerThemeName))
            ? centerThemeName
            : UnitPlanITCenterTags[0]
        } else if (psTkGrades.includes(grade)) {
          result.centerTags = UnitPlanCenterTags
          // 如果传入了 centerThemeName 且在 centerTags 中存在，则使用传入的值
          result.centerThemeName = centerThemeName && UnitPlanCenterTags.some(tag => equalsIgnoreCase(tag, centerThemeName))
            ? centerThemeName
            : UnitPlanCenterTags[0]
        } else {
          // Station 情况
          result.centerTags = UnitPlanKCenterTags
          // 如果传入了 centerThemeName 且在 centerTags 中存在，则使用传入的值
          result.centerThemeName = centerThemeName && UnitPlanKCenterTags.some(tag => equalsIgnoreCase(tag, centerThemeName))
            ? centerThemeName
            : UnitPlanKCenterTags[0]
        }
        return result
      },

        /**
         * 更新是否开启 ai 测评点推荐的值
         * 注释：此方法可能已不再使用，仅作为事件传递使用
         */
        updateEnableAutomaticMeasure(newValue){
            // console.log("按钮更新了：",newValue)
            this.$emit('updateEnableAutomaticMeasure',newValue)
        },

         /**
         * 获取全量框架数据
         */
         getAllFrameworkData () {
          // 设置加载状态
          this.frameworkLoading = true
          // 发送请求获取 states 列表
          return new Promise((resolve, reject) => {
            this.$axios.get($api.urls().getAllFrameworkInfoByCountry)
              .then(response => {
                // 处理返回的数据
                this.frameworkData = response
                resolve()
              })
              .catch(error => {
                console.error('Error while fetching getAllFrameworkInfoByState:', error)
                reject(error)
              })
              .finally(() => {
                // 无论成功失败都关闭加载状态
                this.frameworkLoading = false
              })
          })
      },

      // 年级选择变化时的处理函数
      handleGradeChange(grade) {
        // 获取年龄组的 hasActivity
        const ageGroup = this.ageGroups.find(item => equalsIgnoreCase(item.value, grade))
        this.selectedGrade = ageGroup.name;
        // 清空选中的学科
        this.domainSelected = []
        this.setDefaultSelections();
      },
       // 级联选择变化时的处理函数
      handleCascaderChange (value) {
        if (!value || value.length < 2) {
          return
        }
        // 如果回调参数长度为 2，说明是墨西哥，无州信息，需要特殊处理
        if (value.length == 2) {
          // 先找到对应的国家
          let countryData = this.frameworkData.find(x => equalsIgnoreCase(x.country, value[0]))
          // 找到对应的国家下的第一个州
          let state = countryData.stateFrameworkInfo[0].state
          // 将州信息和框架ID组合成新的数组
          value = [value[0], state, value[1]]
        }
        // 特殊处理 IB
        if (equalsIgnoreCase(value[0], 'IB')) {
          this.lessonAssistantInfo.country = 'United States'
          this.lessonAssistantInfo.state = 'California'
          this.lessonAssistantInfo.frameworkState = 'California'
        } else {
          //  如果 value[1] 是 CCSS 或 Head Start 则不设置 state
          if (!equalsIgnoreCase(value[1], 'CCSS') && !equalsIgnoreCase(value[1], 'Head Start') && !equalsIgnoreCase(value[1], 'AERO')) {
            this.lessonAssistantInfo.country = value[0]
            this.lessonAssistantInfo.state = value[1]
          }
          this.lessonAssistantInfo.frameworkState = value[1]
        }
        this.$emit('updateLocation', this.lessonAssistantInfo.country, this.lessonAssistantInfo.state, this.lessonAssistantInfo.city)
        this.lessonAssistantInfo.frameworkId = value[2]
        // 清空选中的学科
        this.domainSelected = []
        this.lessonAssistantInfo.domains = []
        // 遍历 cascaderData 来找到对应的 frameworkName
        let frameworkName = ''
        for (const countryData of this.frameworkData) {
          for (const stateData of countryData.stateFrameworkInfo) {
            for (const framework of stateData.stateFrameworkInfo.frameworks) {
              if (framework.gradeFrameworkIdMap[this.selectedGrade] === this.lessonAssistantInfo.frameworkId) {
                frameworkName = framework.frameworkName
                break
              }
            }
            if (frameworkName) break
          }
        }
        this.lessonAssistantInfo.frameworkName = frameworkName
        // 调用 fetchMeasuresFromBackend 方法来获取测评点数据
        this.fetchMeasuresFromBackend(value[2])
        // 清除表单校验状态
        this.$refs.fromRef && this.$refs.fromRef.clearValidate('domains');
      },

      // 级联选择器点击时的处理函数
      setDefaultSelections(frameworkState, frameworkId) {
        this.getDefFrameworkById(frameworkId)
        // 美国和加拿大包含州信息，其他国家不包含
        if (frameworkUtils.isCountryWithState(this.selectedFrameworkData[0])) {
          if (!equalsIgnoreCase(this.selectedFrameworkData[1], 'CCSS') && !equalsIgnoreCase(this.selectedFrameworkData[1], 'Head Start') && !equalsIgnoreCase(this.selectedFrameworkData[1], 'AERO')) {
            this.lessonAssistantInfo.country = this.selectedFrameworkData[0]
            this.lessonAssistantInfo.state = this.selectedFrameworkData[1]
          }
          this.lessonAssistantInfo.frameworkState = this.selectedFrameworkData[1]
          this.lessonAssistantInfo.frameworkId = this.selectedFrameworkData[2]
        } else {
          this.lessonAssistantInfo.country = this.selectedFrameworkData[0]
          let countryData = this.frameworkData.find(x => equalsIgnoreCase(x.country, this.selectedFrameworkData[0]))
          let state = countryData.stateFrameworkInfo[0].state
          this.lessonAssistantInfo.state = state
          this.lessonAssistantInfo.frameworkState = state
          this.lessonAssistantInfo.frameworkId = this.selectedFrameworkData[1]
        }
        // 重置测评点选中状态
        this.clearMeasureAndDomainSelections()
        // 处理级联选择器点击时的处理函数
        this.handleCascaderChange(this.selectedFrameworkData)

        // 清除表单校验状态
        this.$nextTick(() => {
            this.$refs.fromRef && this.$refs.fromRef.clearValidate('domains');
        });
      },
        /**
         * 处理领域选择模式切换
         * 在自动模式(auto)和手动模式(specific)之间切换时:
         * 1. 缓存当前模式下的选择状态,以便切换回来时恢复
         * 2. 清空当前选择
         * 3. 根据切换后的模式,恢复之前缓存的选择或触发新的选择操作
         *
         * @param {Object} tab - 切换的标签页对象
         */
        handleDomainModeChange(tab) {
          let selectedDomainMode = tab.name
          // 根据标签页名称设置领域选择模式
          // auto模式: useDomain = true, 系统自动推荐测评点
          // specific模式: useDomain = false, 用户手动选择测评点
          this.lessonAssistantInfo.useDomain = equalsIgnoreCase(selectedDomainMode, 'auto')
          if (!equalsIgnoreCase(selectedDomainMode, this.preSelectedDomainMode)) {
            if (this.$route.name === 'AddLesson' && equalsIgnoreCase(selectedDomainMode, 'auto') && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
              // 创建课程第一步的自动模式点击埋点
              this.$analytics.sendEvent('cg_lesson_plan_cre_select_auto')
            } else if (this.$route.name === 'AddLesson' && equalsIgnoreCase(selectedDomainMode, 'specific') && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
              // 创建课程第一步的手动模式点击埋点
              this.$analytics.sendEvent('cg_lesson_plan_cre_select_specific')
            }

            // 在切换模式前,先将当前模式下的选择状态保存到缓存
            if (equalsIgnoreCase(selectedDomainMode, 'auto')) {
              // 当切换到自动模式时,保存手动模式下已选择的领域和测评点
              this.subjectsDomainsTabsCache['specific'] = {
                domains: this.lessonAssistantInfo.domains, // 已选领域ID数组
                measureIds: this.lessonAssistantInfo.measureIds // 已选测评点ID数组
              }
            } else {
              // 当切换到手动模式时,保存自动模式下已选择的领域
              this.subjectsDomainsTabsCache['auto'] = {
                domains: this.lessonAssistantInfo.domains // 已选领域ID数组
              }
            }

            // 清空当前的领域和测评点选择
            this.clearMeasureAndDomainSelections()

            // 确保缓存对象存在
            if (!this.subjectsDomainsTabsCache['specific']) {
              this.subjectsDomainsTabsCache['specific'] = {}
            }
            if (!this.subjectsDomainsTabsCache['auto']) {
              this.subjectsDomainsTabsCache['auto'] = {}
            }

            // 清除表单中领域、测评点字段的校验状态
            this.$refs.fromRef && this.$refs.fromRef.clearValidate('domains');
            // 根据切换后的模式执行相应的处理逻辑
            if (equalsIgnoreCase(selectedDomainMode, 'auto')) {
              // 自动模式处理逻辑
              const cachedDomains = this.subjectsDomainsTabsCache['auto'].domains
              if (cachedDomains && cachedDomains.length > 0) {
                // 如果缓存中存在之前的领域选择,则恢复这些选择
                this.fetchMeasuresFromBackend(this.lessonAssistantInfo.frameworkId, cachedDomains)
              }
            } else {
              // 手动模式处理逻辑
              const cache = this.subjectsDomainsTabsCache['specific']
              const domainIds = cache.domains
              const measureIds = cache.measureIds

              if (domainIds && domainIds.length > 0 && measureIds && measureIds.length > 0) {
                // 如果缓存中同时存在领域和测评点的选择,则恢复这些选择
                this.fetchMeasuresFromBackend(this.lessonAssistantInfo.frameworkId, domainIds, measureIds)
              }
            }
          }
          // 尝试打开下拉框
          this.showSelector(selectedDomainMode)
        },
    // 处理非叶子节点数据
    handleNonLeafNodes (nonLeafNodes) {
      // // 保存非叶子节点数据
      // this.nonLeafNodes = nonLeafNodes
      // // 触发表单验证，确保领域和测评点选择符合要求
      // this.validateDomains()
    },
    // 处理领域节点数据
    handleDomainNodes (domainNodes) {
      // 保存领域节点数据
      this.domainNodes = domainNodes
      // 将选中的领域节点 ID 赋值给 domainSelected 数组
      this.domainSelected = domainNodes.map(item => item.id)
    },
    showSelector(tabName) {
      if (equalsIgnoreCase(tabName, 'auto')) {
        // 自动展开 select
        this.$nextTick(() => {
          if (this.$refs.domainSelect) {
            // 检查下拉菜单是否已经打开，如果没有打开才调用 toggleMenu
            if (!this.$refs.domainSelect.visible) {
              this.$refs.domainSelect.toggleMenu()
            }
          }
        })
      } else {
        // 自动打开弹框
        this.$nextTick(() => {
          if (this.$refs.subjectSelector) {
            this.$refs.subjectSelector.openShowModal()
          }
        })
      }
    }
  },
}
</script>
<style>
.public-lesson-assistantascader-class .el-cascader-menu__list{
  max-width: 600px;
}
.public-lesson-assistantascader-class .el-cascader-node__label{
  white-space: normal;
}
.public-lesson-assistantascader-class .el-cascader-node{
  height: unset;
  line-height: 20px;
  padding: 7px 30px 7px 20px;
}

.public-lesson-assistantascader-class .el-cascader-node:hover {
  background-color: #ddf2f3;
  color:  #10b3b7;
}
</style>
<style lang="less">
.lesson-assistant-domain-select-dropdown {
  padding: 0;

  /* 调整边距大小 */
  .el-select-dropdown__list {
    padding: 12px 0;
  }

  .el-select-group__wrap {
    padding-left: 16px;
  }

  .el-select-dropdown__item {
    padding-left: 26px;

    .el-checkbox {
      margin: 0;
    }
  }
}
</style>
<style scoped lang="less">
@media (max-width: 768px) {
  .public-lesson-assistant-info {
    .el-form-item {
      width: 100%;
      margin-bottom: 12px;
    }
    .el-select {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
    .subject-selector {
      width: 100%;
    }
    .lesson-assistant-domain-select {
      width: 100%;
    }
    .display-flex {
      flex-direction: column;
      align-items: flex-start;
    }
    .gap-6 {
      gap: 5px;
    }
    .el-button {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}
/deep/.lg-tabs-assistant-domain-tabs {
  .el-tabs__item{
    color: #111C1C !important;
    font-weight: 400 !important;
  }

  // 禁用状态的样式
  .el-tabs__item.is-disabled {
    color: #C0C4CC !important;
    cursor: not-allowed !important;

    &:hover {
      color: #C0C4CC !important;
      background-color: transparent !important;
    }

    // 禁用状态下的图标和文字
    .lg-icon {
      color: #C0C4CC !important;
    }

    span {
      color: #C0C4CC !important;
    }
  }
}
.lesson-assistant-domain-select {
  /deep/.el-input__prefix {
    padding-left: 12px;
    color: #676879;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
    cursor: pointer;
    color: #10B3B7;
    justify-content: center;
  }
  /deep/.el-input__suffix {
    display: none;
  }
}
.has-selected-domain {
  /deep/.el-input__prefix {
    padding-right: 12px;
    color: #676879;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
    cursor: pointer;
    color: #10B3B7;
    justify-content: flex-end;
    transition: all 0s;
  }
}
.el-form-item {
    width: 100%;
}
.required-star {
  color: #f56c6c;
  margin-right: 2px;
}
.bold-label {
  font-weight: 600;
    font-size: 16px;
    color: #111c1c;
    line-height: 22px;
    margin-bottom: 20px;
}
.selected-items {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #F5F6F8;
}
.selectMeasure {
  font-size: 12px;
  margin: 4px 0px 0px 4px;
  padding: 7.5px;
  line-height: 1.1;
  border-radius: 4px;
  border: 1px solid #dbdbdb;
  background-color: #ffffff;
  font-weight: 400;
  display: inline-flex !important; /* 使用 Flexbox 布局 */
  align-items: center; /* 垂直居中对齐 */
}
.selected-title {
  font-weight: bold;
  text-align: left;
  margin-bottom: 5px;
}
.max-items {
  font-weight: normal;
  color: #999;
  margin-left: 10px;
}

/deep/ .el-form-item__error {
  margin-top: 4px;
}

.selected-domain-button {
  width: 100%;
  color: #111C1C;
  font-weight: 400;
  border: 1px solid #DCDFE6; /* 添加边框样式 */
  border-radius: 4px; /* 添加圆角样式 */
}

.selected-domain-button:hover,
.selected-domain-button:focus,
.selected-domain-button:active {
  /* 覆盖悬浮和点击时的背景颜色 */
  background-color: initial;
  /* 取消盒阴影 */
  box-shadow: none;
  /* 取消文字颜色变化 */
  color: initial;
}

.vertical-dropdown .el-checkbox-group {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.dropdown-checkbox {
  margin-left: 8px;
}

// 框架下拉框自动换行
.el-select-dropdown .el-select-dropdown__item {
  white-space: normal !important;
}

// 复选框每个选项换行
.option-text {
  white-space: normal !important;
}

// 调整复选框滚动条
.dropdown-checkbox-group {
  overflow-x: hidden;
  overflow-y: hidden;
  max-height: 200px;
}

// 调整复选框全选按钮
.dropdown-checkbox {
  white-space: normal !important;
  display: flex;
  align-items: top;
}

/deep/ .el-checkbox__input {
  padding-top: 4px;
}

// 调整复选框每个选项
.dropdown-checkbox-item {
  margin-top: 10px;
  margin-left: 24px;

  display: flex;
  align-items: top;
}

// 设置禁选后颜色
.el-dropdown [disabled] {
  color: #32333866 !important;
  background-color: #EBEEF5;
  opacity: 1 !important;
}

/deep/ .el-button.is-loading {
  background-color: transparent !important;
}

.el-dropdown-menu {
  white-space: normal !important;
  padding: 10px 20px 10px 20px; /* 调整边距大小 */
  max-height: 300px;
  overflow-x: hidden;
}

</style>