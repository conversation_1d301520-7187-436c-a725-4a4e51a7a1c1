<template>
  <!-- ai 助手内容主体 -->
  <div class="display-flex flex-direction-col h-full card-area assistant-container">
    <!-- Tab组件放在内容主体上方 -->
    <lg-tabs
      v-show="source !== 'editPromptPopover'"
      class="w-full text-center"
      style="display: flex; justify-content: center;"
      v-model="activeTab"
      :tabs="assistantToolTabs"
      @change="handleTabChange"></lg-tabs>

    <el-form
      class="form-area w-full lg-scrollbar-show assistant-form"
      ref="lessonAssistantFormRef"
      label-position="top"
      label-width="100px"
      :model="lessonAssistantInfo"
      :rules="showAdaptationIdeas ? adaptLessonAssistantRules : createLessonAssistantRules"
      style="padding-bottom: 16px;padding-top: 12px;"
      :validate-on-rule-change="false"
    >
      <!-- 课程内容输入框 -->
      <div class="w-full">
        <el-form-item :label="$t('loc.activityDescription')" prop="lessonContent" label-width="100%" class="custom-form-item">
          <template slot="label">
            <div class="custom-label">
              <div class="left-label ">
                <span class="required-star">*</span>
                <span class="font-size-14">{{ $t('loc.activityDescription') }}</span>
              </div>
              <!-- Exemplars 按钮 -->
              <div class="display-flex gap-10 align-items" v-if="source !== 'editPromptPopover'">
                <el-tooltip effect="dark" :content="$t('loc.unitPlannerExemplarsTip')" :placement="isMobile ? 'left' : 'right'"  v-model="showExemplarsTip" :manual="showExemplarsTipManual" ref="exemplarsTooltip" popper-class="assistant-exemplars-tooltip">
                  <el-button style="padding: 3px 6px!important;"
                              type="primary"
                              class="width-36-height-36 lg-button-animation exemplar-btn"
                             :class="{ 'hover-active': showExemplarsTip && showExemplarsTipManual }"
                             size="medium"
                              @click="handleExamplarsClick" plain>
                    <div class="btn-center">
                      <i class="lg-icon lg-icon-light font-size-20 lg-color-primary"></i>
                      <span>Exemplars {{ currentSampleIndex + 1 }}/{{ samplesContents[activeTab].length }}</span>
                    </div>
                  </el-button>
                </el-tooltip>
                <!-- prompt 历史 -->
                <UnitPromptHistory class="unit-prompt-history lg-button-animation" @usePromptHistory="usePromptHistory" :promptHistoryType="equalsIgnoreCase(activeTab, 'create') ? 'CREATE_LESSON' : 'ADAPT_LESSON'" />
              </div>
            </div>
          </template>
          <div>
            <!-- 引导步骤1 popover -->
            <el-popover
              placement="right"
              width="370"
              v-model="assistantToolbarGuide.showStep1"
              popper-class="assistant-toolbar-guide-color-text"
              ref="assistantToolbarGuide1"
              trigger="manual">
              <div class="text-white">
                <!-- 标题 -->
                <div class="title-font-20 lg-margin-bottom-12">{{ assistantToolbarGuide.title }}</div>
                <!-- 引导文字 -->
                <div class="lg-margin-bottom-24 word-break text-left">
                  <!-- 用户引导内容 -->
                  <ul>
                    <li v-for="(content, index) in assistantToolbarGuide.ulContent" :key="index">{{ content }}</li>
                  </ul>
                </div>
                <div class="flex-space-between">
                  <!-- 跳过 -->
                  <div class="display-flex w-full justify-content-between align-items">
                    <!-- 步骤数 -->
                    <el-link :underline="false">({{ assistantToolbarGuide.step }}/3)</el-link>
                    <!-- 下一步 -->
                    <el-button type="primary" @click="startGuide(2)">{{ $t('loc.assistantToolbarGuide4') }}</el-button>
                  </div>
                </div>
              </div>
              <el-input
                ref="textOptionInput"
                slot="reference"
                v-model="lessonAssistantInfo.lessonContent"
                type="textarea"
                :autosize="{ minRows: 6, maxRows: 10}"
                maxlength="5000"
                show-word-limit
                :placeholder="showAdaptationIdeas ? $t('loc.activityDescriptionPlaceholder2') : $t('loc.activityDescriptionPlaceholder1')"
              />
            </el-popover>
          </div>
        </el-form-item>
        <el-form-item label-width="100%" class="custom-form-item adapt-item" v-show="showAdaptationIdeas" prop="adaptationContent">
          <template slot="label">
            <div class="custom-label">
              <div class="left-label">
                <span class="required-star">*</span>
                <span class="font-size-14">{{ $t('loc.PersonalizationAdaptation') }}</span>
              </div>
            </div>
          </template>
          <el-input
            type="textarea"
            maxlength="2000"
            show-word-limit
            :autosize="{ minRows: 3, maxRows: 7}"
            :placeholder="$t('loc.adaptationIdeasPlaceholder')"
            v-model="lessonAssistantInfo.adaptationContent">
          </el-input>
        </el-form-item>
      </div>
      <PublicLessonAssistantInfo
        :lessonAssistantInfo="lessonAssistantInfo"
        :restoreAssistantToolStateLoading="restoreAssistantToolStateLoading"
        :states="states"
        :defaultCountry="lessonAssistantInfo.country"
        :defaultState="lessonAssistantInfo.state"
        :assistantToolState="assistantToolState"
        ref="publicLessonAssistantInfo"
        :frameworks="frameworks"
        :domains="domains"
        @changeActivityType="changeActivityType"
        :hisMeasureIds="lessonAssistantInfo.measureIds"
        :assistantToolbarGuide="assistantToolbarGuide"
        :startGuideFunc="startGuide"
        :loadFrameworkLoading="loadFrameworkLoading"
        :enableAutomaticMeasure="enableAutomaticMeasure"
        :inWeeklyPlannDialog="inWeeklyPlannDialog"
        :selectedFramework="selectedFramework"
        @updateLocation="updateLocation"
        @getFrameworkDomains="getFrameworkDomains"
        @updateEnableAutomaticMeasure="updateEnableAutomaticMeasure"
        :source="source"
      ></PublicLessonAssistantInfo>
      <UnitInfoLearnerProfile
       :unitInfo="learnerProfileLessonInfo"
       @updateRubrics="updateRubrics"
       @updateRubricsOptions="updateRubricsOptions"
      ></UnitInfoLearnerProfile>
      
      <!-- Classroom Setup 部分 -->
      <el-form-item v-show="showClassroomSetup" prop="classroomType" class="classroom-setup-section">
        <div slot="label" class="flex-row-center">
          <span class="title-font-14">
            {{ $t('loc.classroomSetup') }}
          </span>
          <el-tooltip effect="dark" placement="top">
            <div slot="content" style="max-width: 260px;">
              {{ $t('loc.classroomSetupTip') }}
            </div>
            <i class="lg-icon lg-icon-info lg-margin-left-4 color-icon lg-pointer font-400-important"></i>
          </el-tooltip>
        </div>
        <div class="classroom-setup-content">
          <el-radio-group v-model="lessonAssistantInfo.classroomType" class="teaching-mode-options">
            <el-radio label="IN_PERSON" class="teaching-mode-radio" style="margin-right: 69px;">{{ $t('loc.classroomSetupInPerson') }}</el-radio>
            <el-radio label="VIRTUAL" class="teaching-mode-radio">{{ $t('loc.classroomSetupVirtual') }}</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>
      
      <!-- 本地化课程 -->
      <div class="location">
        <div>
                <span class="title-font-14 lg-margin-right-8">
                  {{ $t('loc.unitPlannerStep1CreateLocalizedCurriculum') }}
                </span>
          <el-switch
            style="height: 23px"
            @click.native="handleLocationClick"
            v-model="lessonAssistantInfo.useLocation"
            active-color="#10B3B7"
            inactive-color="#DCDFE6">
          </el-switch>
        </div>
        <el-tooltip :content="locationDescription" placement="top">
          <button
            style="font-size: 20px"
            class="lg-icon lg-icon-location location-align  flex-center-center"
            @click.prevent="editLocation">
                  <span v-if="lessonAssistantInfo.city && lessonAssistantInfo.city !== '' " style="margin-left: 5px;font-family: Inter;">
                    {{ lessonAssistantInfo.city }}, {{ lessonAssistantInfo.state }}, {{ lessonAssistantInfo.country }}
                  </span>
            <span v-else style="margin-left: 5px;font-family: Inter;">
                    {{ lessonAssistantInfo.state }}, {{ lessonAssistantInfo.country }}
                  </span>
          </button>
        </el-tooltip>
      </div>
      <!-- 选择位置弹框 -->
      <el-dialog title="Change Location" :visible.sync="cityDialogVisible" :append-to-body="true" width="600px" :close-on-click-modal="false"
                  custom-class="location-from-dialog">
        <el-form :model="location" label-position="top" label-width="100px">
          <el-form-item :label="$t('loc.unitPlannerStep1Country')">
            <el-select v-model="location.country" @change="changeCountry">
              <el-option v-for="country in countries" :key="country" :label="country" :value="country">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('loc.unitPlannerStep1State')">
            <el-select
              @change="changeState"
              v-model="location.state">
              <el-option v-for="(state, index) in states" :key="index + state" :label="state" :value="state">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('loc.unitPlannerStep1City')">
            <el-select
              v-model="location.city"
              filterable
              clearable
              :placeholder="$t('loc.pleaseSelect')">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
              <el-option v-for="(city, index) in cities" :key="index + city" :label="city" :value="city">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cityDialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="cityDialogConfirm">Confirm</el-button>
        </div>
      </el-dialog>
    </el-form>
    <!--操作-->
    <div class="display-flex flex-justify-center btn-area w-full">
      <!-- 引导步骤3 popover -->
      <el-popover
        placement="right"
        width="440"
        v-model="assistantToolbarGuide.showStep3"
        ref="assistantToolbarGuide3"
        popper-class="assistant-toolbar-guide-color-text"
        trigger="manual">
        <div class="text-white">
          <!-- 标题 -->
          <div class="title-font-20 lg-margin-bottom-12">{{ assistantToolbarGuide.title }}</div>
          <!-- 引导文字 -->
          <!-- 引导文字 -->
          <div class="lg-margin-bottom-24 word-break text-left">
            <!-- 用户引导内容 -->
            <ul>
              <li v-for="(content, index) in assistantToolbarGuide.ulContent" :key="index">{{ content }}</li>
            </ul>
          </div>
          <div class="flex-space-between">
            <!-- 跳过 -->
            <div class="display-flex w-full justify-content-between align-items">
              <!-- 步骤数 -->
              <el-link :underline="false">({{ assistantToolbarGuide.step }}/3)</el-link>
              <div class="display-flex flex-justify-end gap-6 align-items">
                <!--上一步-->
                <el-button type="primary" class="btn-back" @click="startGuide(2)">{{ $t('loc.assistantToolbarGuide10') }}</el-button>
                <!-- 下一步 -->
                <el-button type="primary" @click="endGuide">{{ $t('loc.assistantToolbarGuide11') }}</el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 生成课程按钮 -->
        <el-button slot="reference" class="ai-btn w-full lg-button-animation" @click="convertLessonConfirm"
                   :loading="convertLessonLoading">
              <span class="w-full">
              <img class="lg-padding-right-8" :src="getGenerateButtonIcon" alt="">
              {{ getGenerateButtonName }}
              </span>
        </el-button>
      </el-popover>
    </div>
  </div>
</template>

<script>
import usStatesAndCities from '@/assets/data/usStatesAndCities.json'
import mexicoStatesAndCities from '@/assets/data/mexicoStatesAndCities.json'
import canadaStatesAndCities from '@/assets/data/canadaStatesAndCities.json'
import generateDefaultIcon from '@/assets/img/lesson2/assistant/generate.png'
import regenerateIcon from '@/assets/img/lesson2/assistant/regenerate.png'
import LgTabs from '@/components/LgTabs'
import { assistantToolState } from '@/mixins/assistantToolStateManager'
import user from '@/store/modules/user'
import { equalsIgnoreCase } from '@/utils/common'
import { PublicLessonAssistantInfoActivityType } from '@/utils/constants'
import { createEventSource, parseStreamData, removeUnexpectedCharacters } from '@/utils/eventSource'
import fileUtil from '@/utils/file'
import frameworkUtils from '@/utils/frameworkUtils'
import tools from "@/utils/tools"
import PublicLessonAssistantInfo from '@/views/modules/lesson2/lessonLibrary/assistant/PublicLessonAssistantInfo.vue'
import UnitPromptHistory from '@/views/modules/lesson2/unitPlanner/components/editor/UnitPromptHistory.vue'
import UnitInfoLearnerProfile from '@/views/modules/lesson2/unitPlanner/components/learnerProfile/UnitInfoLearnerProfile.vue'
import { mapState } from 'vuex'
import Lesson2 from '../../../../../api/lessons2'

export default {
  name: 'AssistantTool',
  mixins: [assistantToolState],
  components: {
    PublicLessonAssistantInfo,
    LgTabs,
    UnitPromptHistory,
    UnitInfoLearnerProfile
  },

  props: {
    // 年龄段列表
    ageGroups: {
      type: Array,
      default: () => []
    },
    // 是否是 unit 课程
    isFromUnitLesson: {
      type: Boolean,
      default: () => false
    },
    // 框架列表
    frameworks: {
      type: Array,
      default: () => []
    },

    // 是否全屏
    aiFullScreenFlag: {
      type: Boolean,
      default: () => false
    },

    // 是否展示ai入口
    aiToolEntryShow: {
      type: Boolean,
      default: () => false
    },

    // 课程封面loading
    lessonCoverLoading: {
      type: Boolean,
      required: true
    },

    // 是否在 Weekly Planning 模块引用
    inWeeklyPlannDialog: {
      type: Boolean,
      default: () => false
    },

    // 在 Weekly Planning 模块中已经默认选择的框架
    selectedFramework: {
      type: Object,
      default: () => null
    },

    // 课程
    lesson: {
      type: Object,
      default: function () {
        return null
      }
    },

    // 保存课程方法
    saveLesson: {
      type: Function
    },

    // 保存课程草稿方法
    saveDraft: {
      type: Function
    },

    // 来源
    source: {
      type: String,
      default: ''
    },

    // 是否是添加课程
    isAddLesson: {
      type: Boolean,
      default: () => false
    },

    // 是否是批量编辑（用来判断在保存时是否走自动保存的逻辑）
    batchEdit: {
      type: Boolean,
      default: () => false
    },

    // 轻量导入改编时当前模块是否显示
    adaptedModuleSwitch: {
      type: Object,
      default: () => {}
    }
  },

  data () {
    return {
      // 课程助手信息
      lessonAssistantInfo: {
        lessonContent: '',
        adaptationContent: '',
        ageGroup: 'Grade 3',
        activityType: 'LARGE_GROUP',
        frameworkId: 'D8520C73-D47A-49E4-A01F-66BC4EAD7BCB',
        frameworkName: 'California Preschool/Transitional Kindergarten Learning Foundations',
        centerThemeName: '',
        domains: [],
        measureIds: [],
        haveSelectMeasureId: false,
        state: 'California',
        useLocation: true,
        country: 'United States',
        city: '',
        isHistoryData: false,
        ageGroupName: '',
        useDomain: true, // 是否使用自动分配
        frameworkState: 'California', // 选择的框架对应的州
        classroomType: 'IN_PERSON' // 课堂类型：IN_PERSON 或 VIRTUAL
      },
      // 选中的课程信息
      lessonAssistantSelectedInfo: {
        lessonContent: '',
        adaptationContent: '',
        ageGroup: 'Grade 3',
        activityType: 'LARGE_GROUP',
        frameworkId: 'D8520C73-D47A-49E4-A01F-66BC4EAD7BCB',
        frameworkName: 'California Preschool/Transitional Kindergarten Learning Foundations',
        centerThemeName: '',
        domains: [],
        measureIds: [],
        haveSelectMeasureId: false,
        state: 'California',
        useLocation: true,
        country: 'United States',
        city: '',
        frameworkState: 'California', // 选择的框架对应的州
        useDomain: true, // 是否使用自动分配
        classroomType: 'IN_PERSON' // 课堂类型：IN_PERSON 或 VIRTUAL
      },
      // 位置选择相关数据
      cityDialogVisible: false,
      countries: ['United States', 'Mexico', 'Canada'],
      location: {
        country: 'United States',
        state: 'California',
        city: ''
      },
      statesAndCities: usStatesAndCities, // 美国州和城市信息
      states: [], // 美国州列表
      usStates: [], // 美国州列表
      createLessonAssistantRules: {
        lessonContent: [
          {
            validator: (rule, value, callback) => {
              if (this.restoreAssistantToolStateLoading) {
                callback()
                return
              }

              if (!value || value.trim() === '') {
                callback(new Error(this.$t('loc.fieldReq')))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      }, // 课程助手表单校验规则
      adaptLessonAssistantRules: {
        lessonContent: [
          {
            validator: (rule, value, callback) => {
              if (this.restoreAssistantToolStateLoading) {
                callback()
                return
              }

              if (!value || value.trim() === '') {
                callback(new Error(this.$t('loc.fieldReq')))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        adaptationContent: [
          {
            validator: (rule, value, callback) => {
              if (this.restoreAssistantToolStateLoading) {
                callback()
                return
              }

              if (!value || value.trim() === '') {
                callback(new Error(this.$t('loc.fieldReq')))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      }, // 课程助手表单校验规则
      domains: [], // 测评点列表
      loadFrameworkLoading: false, // 加载测评点 Loading
      generateMeasuresLoading: false, // 生成测评点 Loading
      convertedLessonContent: '', // 转换后的课程内容
      generateLessonTemplateLoading: false, // 生成课程模板 Loading
      generateGoogleSlideDataLoading: false, // 生成 Google Slide 数据 Loading
      lessonStepTwo: {
        universalDesignForLearning: '', // 课程步骤中差异化教学内容
        culturallyResponsiveInstruction: '', // 课程步骤中文化响应式教学内容
        teachingTips: [], // 标准教学指导列表
        teachingTipsData: '', // 标准教学指导数据
        typicalBehaviors: [], // 典型行为列表
        typicalBehaviorsData: '', // 典型行为数据
        lessonCover: null, // 课程封面
        homeActivity: ''
      },
      tooltipVisible: false, // 提示框是否显示
      // generatedLesson: false, // 是否生成过课程
      fileContentVisible: false, // 是否文件内容
      textContentVisible: true, // 是否文本主题内容
      fileUploadSuccess: false, // 文件是否上传成功
      uploading: false, // 文件正在上传标志
      uploadProgress: 0, // 文件上传进度
      uploadFileList: [], // 上传的文件列表，仅限制上传一个
      curFile: {
        name: '',
        fileSize: 0,
        url: ''
      }, // 当前上传的文件
      showFilePreview: false, // 文件缩略图标志
      API_KEY: 'AIzaSyB3v3Q0TDi4Rnf3ZUaw9QO-xCgVc3wAJbs', // apiKey
      CLIENT_ID: '688203293456-m7uf83u0uh7q1g7s4qlhi4tilqkmlq11.apps.googleusercontent.com', // clientId
      scope: 'https://www.googleapis.com/auth/drive.metadata.readonly', // 作用域
      oauthToken: null, // 登录验证token
      googleDriveTokenClient: null, // google drive token客户端
      googleDriveAccessToken: null, // google drive通行token
      pickerInited: false, // 选择器是否已经初始化
      gisInited: false, // gis是否已经初始化
      currentTagName: this.submoduleName || 'Manual Input', // 当前模块名称
      submodules: ['Manual Input', 'File Upload'], // 全部模块名称
      enableAutomaticMeasure: true, // 启动自动测评点建议
      // convertLessonLoading: false, // 正在生成课程的loading
      // lessonStepTwoGenerated: false, // 课程生成第二步完毕
      promptUsageRecordIds: [], // 标识着本次生成课程的所有promptUsageRecordId
      assistantToolbarGuide: {
        showStep1: false,
        showStep2: false,
        showStep3: false,
        step: 1,
        title: this.$t('loc.assistantToolbarGuide1'),
        content: this.$t('loc.assistantToolbarGuide2'),
        ulContent: [this.$t('loc.assistantToolbarGuide2'), this.$t('loc.assistantToolbarGuide3')]
      },// 引导信息
      samplesContents: {
        create: [
          {
            activityDescription: 'Start with a read-aloud from \'From Seed to Plant\' by Gail Gibbons to introduce plant life cycles. Discuss the stages from seed to mature plant. Then, have students plant seeds in small pots and predict growth stages. Conclude with a discussion on the steps and cause-effect relationships in the life cycle.',
            grade: 'Grade 3',
            frameworkId: "D8520C73-D47A-49E4-A01F-66BC4EAD7BCB",
            frameworkName: "California K12 Standards",
            domains: [
              {
                abbreviation: "English Language Arts",
                id: "6FC6FE8A-0546-4CA5-A074-A334308E8AEA"
              },
              {
                abbreviation: "Next Generation Science Standards (NGSS)",
                id: "78231C25-8158-462C-86BD-F4BBEFD6B869"
              }
            ]
          },
          {
            activityDescription: 'Explore basic concepts of renewable energy, focusing on solar and wind power. Use simple experiments or activities, like making a small solar-powered toy or a paper windmill, to help students understand how these energy sources work.',
            grade: 'Grade 3',
            frameworkId: "D8520C73-D47A-49E4-A01F-66BC4EAD7BCB",
            frameworkName: "California K12 Standards",
            domains: [
              {
                abbreviation: "English Language Arts",
                id: "6FC6FE8A-0546-4CA5-A074-A334308E8AEA"
              },
              {
                abbreviation: "Next Generation Science Standards (NGSS)",
                id: "78231C25-8158-462C-86BD-F4BBEFD6B869"
              }
            ]
          }
        ],
        adapt: [
          {
            activityDescription: "Lesson Title: Water Cycle Wonders\n\n" +
              "Objectives\n" +
              "Children will explore the stages of the water cycle and develop an understanding of how water moves through the environment. They will practice collaborative discussion skills, enhance their vocabulary by clarifying new terms, and apply their observations to represent data visually.\n\n" +
              "Implementation Steps\n" +
              "1. Start the lesson indoors by reading \"The Water Cycle\" by Helen Frost. Use the book's illustrations to introduce key vocabulary words, emphasizing their meanings with child-friendly explanations.\n" +
              "2. Engage students in a group discussion to identify and explain the main stages of the water cycle: evaporation﻿, condensation, and precipitation. Encourage them to use the new vocabulary words during the discussion.\n" +
              "3. Transition outdoors and have students gather around a table with the experiment materials. Explain that they will simulate the water cycle using a clear container, water, and plastic wrap.\n" +
              "4. Pour water into the clear container and place small stones inside to mimic land. Cover the top with plastic wrap tightly, securing it with a rubber band if necessary.\n" +
              "5. Place the container in a sunny spot and ask students to observe what happens over time. Encourage them to notice any changes on the plastic wrap and discuss what it represents in the water cycle.\n" +
              "6. While waiting for observations, have students draw simple diagrams of the water cycle in their notebooks, labeling each stage correctly with the new vocabulary.\n" +
              "7. After observations, gather students for a reflective discussion. Ask open-ended questions like:\n" +
              " - What did you notice happening inside the container?\n" +
              " - How does this experiment show the stages of the water cycle?\n" +
              " - Why do you think it's important to understand how the water cycle works?\n" +
              "8. Conclude by having students represent their observations and understanding of the water cycle on chart paper. Encourage them to use drawings and words to explain the process, and share their findings with the class.",
            adaptationIdeas: "Replace the activities in the above lesson plan with a role-play. Organize a role-play where each student represents a different part of the water cycle (e.g., sun, cloud, raindrop).",
            grade: "Grade 3",
            frameworkId: "D8520C73-D47A-49E4-A01F-66BC4EAD7BCB",
            frameworkName: "California K12 Standards",
            domains: [
              {
                abbreviation: "English Language Arts",
                id: "6FC6FE8A-0546-4CA5-A074-A334308E8AEA"
              },
              {
                abbreviation: "Next Generation Science Standards (NGSS)",
                id: "78231C25-8158-462C-86BD-F4BBEFD6B869"
              }
            ]
          },
          {
            activityDescription: "Lesson Title: Earth's Journey\n\n" +
              "Objectives\n" +
              "Children will explore Earth's movement and position in the Solar System. They will practice listening skills by identifying main ideas and details from a read-aloud and demonstrate understanding by creating a labeled diagram of Earth's orbit, highlighting its tilt and its effects on the seasons.\n\n" +
              "Implementation Steps\n" +
              "1. Begin by gathering students in a comfortable reading area. Introduce the theme of Earth's place in the Solar System and explain that they will be listening to a story that helps explore this theme.\n" +
              "2. Read aloud \"The Magic School Bus Lost in the Solar System\" by Joanna Cole. Pause periodically to ask open-ended questions, such as:\n" +
              " - What makes Earth different from other planets?\n" +
              " - Why is Earth's tilt﻿ important?\n" +
              "3. Encourage students to listen carefully for interesting facts about Earth's characteristics and its journey through space.\n" +
              "4. After the read-aloud, transition to a creative activity. Provide each student with a large sheet of paper or poster board. Instruct them to draw and label a diagram of Earth's orbit around the Sun, emphasizing Earth's tilt and its effect on the seasons.\n" +
              "5. Circulate the room to offer specific feedback and support, asking questions like:\n" +
              " - How does the Earth's tilt affect the seasons?\n" +
              " - Can you show me where Earth is in its orbit during different seasons?\n" +
              "6. Once students have completed their diagrams, invite them to present their work to the class. Encourage them to describe Earth's journey and how its position and tilt affect climate and seasons.\n" +
              "7. Conclude the lesson with a group discussion. Ask students to share what they found most interesting about Earth's journey through space and how this knowledge helps us understand our planet better.\n" +
              "8. Close by summarizing the key points discussed and encourage students to continue exploring other planets and celestial bodies in the Solar System for further learning.",
            adaptationIdeas: "Differentiate this lesson for diverse learners by providing multiple options for students to demonstrate their understanding, such as building a model, writing, or presenting, in addition to or instead of just drawing a diagram.",
            grade: "Grade 3",
            frameworkId: "D8520C73-D47A-49E4-A01F-66BC4EAD7BCB",
            frameworkName: "California K12 Standards",
            domains: [
              {
                abbreviation: "English Language Arts",
                id: "6FC6FE8A-0546-4CA5-A074-A334308E8AEA"
              },
              {
                abbreviation: "Next Generation Science Standards (NGSS)",
                id: "78231C25-8158-462C-86BD-F4BBEFD6B869"
              }
            ]
          }
        ]
      }, // 课程样例内容
      googleSlideData: null, // google slide数据
      activeTab: 'create',
      showAdaptationIdeas: false,
      currentSampleIndex: 0, // 当前选中的 sample 索引
      assistantToolTabs: [{
        label: this.$t('loc.createLesson'),
        name: 'create'
      },
        {
          label: this.$t('loc.adaptLesson'),
          name: 'adapt'
        }],
      // 新增：Create Lesson 标签页的状态
      createAssistantToolState: null,
      // 新增：Adapt Lesson 标签页的状态
      adaptAssistantToolState: null,
      restoreAssistantToolStateLoading: false, // 恢复 AssistantTool 状态 loading
      showExemplarsTip: false, // 是否显示示例单元提示
      showExemplarsTipManual: false, // 是否手动控制示例单元提示
      isMobile: false, // 是否是移动端
      exemplarsClickCount: 0 // 点击计数
    }
  },

  mounted() {
    // 检查是否需要显示 Exemplars 提示
    if (this.$route.query.showExemplarsTip === 'true') {
      this.showExemplarsTipAfterDelay()
    }

    // 添加滚动事件监听
    window.addEventListener('scroll', this.updateTooltipPosition, true)
    this.isMobile = window.innerWidth <= 768
  },

  beforeDestroy() {
    // 移除滚动事件监听
    window.removeEventListener('scroll', this.updateTooltipPosition, true)
  },

  created () {
    this.$store.dispatch('getAgencyLearnerProfile')
    // 初始化标签页状态
    this.activeTab = 'create' // 默认选中创建标签
    this.showAdaptationIdeas = false // 默认不显示适配建议
    this.initLessonAssistantInfo() // 初始化课程助手信息
    try {
      this.gapiLoaded()
      this.gisLoaded()
      this.startGuide()
    } catch (error) {
      // 忽略错误
    }

    // 检查并恢复助手状态
    if (this.assistantToolState && this.assistantToolState.lessonAssistantInfo) {
      this.restoreAssistantToolState(this.assistantToolState)
      this.getState()
      return
    }

    // 检查并恢复助手历史数据
    const historyInfo = this.historyLessonAssistantInfo()
    if (historyInfo) {
      // 非周计划对话框时,更新子分类中心
      if (!this.inWeeklyPlannDialog) {
        this.$emit('upSubclassCenter', this.lessonAssistantInfo.activityType)
      }
      this.getState()
      return
    }

    // 处理新用户欢迎引导
    const needLessonWelcomeGuide = this.currentUser.needLessonWelcomeGuide
    if (needLessonWelcomeGuide) {
      // 设置 adapt 样例数据
      const adaptSample = this.samplesContents['adapt'][0]
      this.lessonAssistantInfo.lessonContent = adaptSample.activityDescription
      this.lessonAssistantInfo.adaptationContent = adaptSample.adaptationIdeas
      this.lessonAssistantInfo.frameworkId = adaptSample.frameworkId
      this.lessonAssistantInfo.frameworkName = adaptSample.frameworkName
      this.lessonAssistantInfo.frameworkState = adaptSample.frameworkState
      this.lessonAssistantInfo.domains = adaptSample.domains && adaptSample.domains.map(item => item.id) || []
      this.lessonAssistantInfo.measureIds = adaptSample.measures && adaptSample.measures.map(item => item.id) || []
      this.lessonAssistantInfo.ageGroup = adaptSample.grade
      this.lessonAssistantInfo.useDomain = !(adaptSample.measures && adaptSample.measures.length > 0)
      this.lessonAssistantInfo.classroomType = 'IN_PERSON'
      // 保存 adapt 样例数据，下次切换到 Adapt 标签页时，恢复 adapt 样例数据
      this.adaptAssistantToolState = {
        lessonAssistantInfo: JSON.parse(JSON.stringify(this.lessonAssistantInfo)),
        // 保存测评点树形数据
        treeData: [],
        treeDataFrameworkId: null,
        activeTab: 'adapt',
        subjectsDomainsTabsCache: {}
      }
      // 保存 create 样例数据
      const createSample = this.samplesContents['create'][0]
      this.lessonAssistantInfo.lessonContent = createSample.activityDescription
      this.lessonAssistantInfo.adaptationContent = createSample.adaptationIdeas
      this.lessonAssistantInfo.frameworkId = createSample.frameworkId
      this.lessonAssistantInfo.frameworkName = createSample.frameworkName
      this.lessonAssistantInfo.frameworkState = createSample.frameworkState
      this.lessonAssistantInfo.domains = createSample.domains && createSample.domains.map(item => item.id) || []
      this.lessonAssistantInfo.measureIds = createSample.measures && createSample.measures.map(item => item.id) || []
      this.lessonAssistantInfo.ageGroup = createSample.grade
      this.lessonAssistantInfo.useDomain = !(createSample.measures && createSample.measures.length > 0)
      this.lessonAssistantInfo.classroomType = 'IN_PERSON'
      this.createAssistantToolState = {
        lessonAssistantInfo: JSON.parse(JSON.stringify(this.lessonAssistantInfo)),
        // 保存测评点树形数据
        treeData: [],
        treeDataFrameworkId: null,
        activeTab: 'create',
        subjectsDomainsTabsCache: {}
      }
      this.autoApplyNextSample(false) // 自动应用 Create 示例
      this.getState()
      return
    }
    this.getState().then(() => {
      this.initLocationInfo() // 初始化地理位置信息
    })
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      bloomQuizSetting: state => state.curriculum.bloomQuizSetting
    }),
    learnerProfileLessonInfo () {
      let ageGroupName = this.getAgeGroupName(this.lessonAssistantInfo.ageGroup)
      if (this.lessonAssistantInfo.learnerProfiles && this.lessonAssistantInfo.learnerProfiles.length > 0) {
        return {
          grade: ageGroupName,
          isLessonAssistant: true,
          newRubrics: this.lessonAssistantInfo.learnerProfiles
        }
      }
      return {
        grade: ageGroupName,
        isLessonAssistant: true
      }
    },
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    /**
     * 是否填充完毕
     * 判断条件:
     * text 下内容不为空 || file 下文件上传成功
     * ageGroup 不为空
     * frameworkId 不为空
     * activityType 不为空，并且当 activityType 为 CENTER 时，centerThemeName不为空
     */
    fillOver () {
      // 必填项不能为空
      const { ageGroup, frameworkId, activityType, centerThemeName, domains, measureIds } = this.lessonAssistantInfo

      // 检查是否选择了 domains 或 measures
      const hasDomainsOrMeasures = (domains && domains.length > 0) || (measureIds && measureIds.length > 0)

      let publicFillOver = ageGroup && frameworkId &&
        ((equalsIgnoreCase(activityType, 'CENTER') || equalsIgnoreCase(activityType, 'STATION')) ? centerThemeName : activityType) &&
        hasDomainsOrMeasures

      // 判断 lessonContent 去除空格和换行后是否有内容
      const hasContent = this.lessonAssistantInfo.lessonContent && this.lessonAssistantInfo.lessonContent.trim().replace(/\s+/g, '').length > 0
      if (this.showAdaptationIdeas) {
        // 判断 adaptContent 去除空格和换行后是否有内容
        const hasAdaptContent = this.lessonAssistantInfo.adaptationContent && this.lessonAssistantInfo.adaptationContent.trim().replace(/\s+/g, '').length > 0
        return hasContent && publicFillOver && hasAdaptContent
      } else {
        return hasContent && publicFillOver
      }
    },
    /**
     * 获取生成按钮的名称：Generate、Generating Measures、Generating Lesson Plan、Regenerate
     */
    getGenerateButtonName () {
      // 正在生成测评点
      if (this.generateMeasuresLoading) {
        return 'Generating Measures'
      }
      // 正在生成课程信息
      if (this.convertLessonLoading) {
        return 'Generating Lesson Plan'
      }
      // 已经生成过课程信息了
      if (this.generatedLesson) {
        return 'Regenerate'
      }
      // 都没有则返回默认的
      return 'Generate'
    },

    /**
     * 获取生成按钮的图标
     */
    getGenerateButtonIcon () {
      if (this.generateMeasuresLoading || this.convertLessonLoading) {
        return ''
      }
      if (this.generatedLesson) {
        return regenerateIcon
      }
      return generateDefaultIcon
    },
    // 城市信息，根据选择的州更新
    cities () {
      // 如果是在弹窗中，使用 location.state
      if (this.cityDialogVisible && this.location.state) {
        const state = this.statesAndCities.find(state => equalsIgnoreCase(state.name, this.location.state))
        return (state !== null && state !== undefined) ? state.cities.slice().sort() : []
      }
      // 如果是在表单中，使用 lessonAssistantInfo.state
      if (this.lessonAssistantInfo.state) {
        const state = this.statesAndCities.find(state => equalsIgnoreCase(state.name, this.lessonAssistantInfo.state))
        return (state !== null && state !== undefined) ? state.cities.slice().sort() : []
      }
      return [];
    },
    // 地理位置信息
    locationDescription () {
      const { country, state, city } = this.lessonAssistantInfo
      return `${city && city.trim() ? `${city}, ` : ''}${state}, ${country}`
    },
    /**
     * 获取转换课程的 loading
     */
    convertLessonLoading: {
      get() {
        return this.localState.convertLessonLoading;
      },
      set(value) {
        this.updateConvertLessonLoading1(value);
      }
    },

    /**
     * 获取是否生成过课程
     */
    generatedLesson: {
      get() {
        return this.localState.generatedLesson;
      },
      set(value) {
        this.updateGeneratedLesson(value);
      }
    },

    /**
     * lessonStepTwoGenerated 数据需要在组件同步
     */
    lessonStepTwoGenerated: {
      get () {
        return this.localState.lessonStepTwoGenerated
      },
      set (value) {
        this.updateLessonStepTwoGeneratedSyncNotify(value)
      }
    },

    // slides 流式状态
    generateLessonSlidesStreamLoading: {
      get () {
        return this.localState.generateLessonSlidesStreamLoading
      },
      set (value) {
        this.updateGenerateLessonSlidesStreamLoadingNotify(value)
      }
    },

    /**
     * 获取助手工具栏状态
     */
    assistantToolState: {
      get() {
        return this.localState.assistantToolState;
      },
      set(value) {
        this.updateAssistantToolState(value);
      }
    },
    
    /**
     * 是否显示 Classroom Setup 部分
     * K 年级及以上展示，但活动类型为 Center/Station 时不展示
     */
    showClassroomSetup () {
      return this.checkIsKAndAboveGrade(this.lessonAssistantInfo.ageGroup) && !this.checkIsCenterOrStation(this.lessonAssistantInfo.activityType) && !this.adaptedModuleSwitch
    }
  },

  watch: {
    activeTab: {
      handler(newVal) {
        // 创建课程第一步的改编课程曝光埋点
        if (this.$route.name === 'AddLesson' && equalsIgnoreCase(newVal, 'adapt') && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
          this.$analytics.sendEvent('cg_lesson_plan_adapt_exposure')
        } else if (this.$route.name === 'AddLesson' && equalsIgnoreCase(newVal, 'create') && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
          // 创建课程曝光埋点
          this.$analytics.sendEvent('cg_lesson_plan_cre_exposure')
        }
      },
      immediate: true,
    }
  },

  methods: {

    /**
     * 更新校训
     */
    updateRubrics(value) {
      this.lessonAssistantInfo.learnerProfiles = value.ageGroupRubrics
    },

    /**
     * 更新校训选项
     */
    updateRubricsOptions(value) {
      this.lessonAssistantInfo.rubricsOptions = value && value.ageGroupRubrics || []
    },

    /**
     * 更新tooltip位置
     */
    updateTooltipPosition() {
      if (this.showExemplarsTip && this.$refs.exemplarsTooltip) {
        const tooltip = this.$refs.exemplarsTooltip

        // 方法1：直接调用tooltip的updatePopper方法
        if (tooltip.updatePopper) {
          tooltip.updatePopper()
        }

        // 方法2：检查是否有popperJS实例
        if (tooltip.popperJS && typeof tooltip.popperJS.update === 'function') {
          tooltip.popperJS.update()
        }

        // 方法3：如果有$refs.popper
        if (tooltip.$refs && tooltip.$refs.popper && tooltip.$refs.popper.updatePopper) {
          tooltip.$refs.popper.updatePopper()
        }
      }
    },

    /**
     * 延迟显示 Exemplars 提示框
     */
    showExemplarsTipAfterDelay () {
      // 等待组件完全渲染后再显示提示
      this.$nextTick(() => {
        // 增加延迟时间，确保页面滚动和布局稳定后再显示提示框
        setTimeout(() => {
          this.showExemplarsTipManual = true
          this.showExemplarsTip = true

          // 清除 URL 中的查询参数，避免刷新页面时重复显示
          this.$router.replace({
            name: this.$route.name,
              params: this.$route.params
            })
        }, 1000) // 延迟1秒显示，确保用户注意到
      })
    },

    /**
     * 点击位置信息
     */
    handleLocationClick () {
      // 创建课程第一步的点击位置信息曝光埋点
      if (this.$route.name === 'AddLesson' && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_local')
      }
    },
    /**
     * 点击示例
     */
    handleExamplarsClick () {
      this.showExemplarsTip = false
      this.showExemplarsTipManual = false
      // 创建课程第一步的点击示例曝光埋点
      if (this.$route.name === 'AddLesson' && equalsIgnoreCase(this.activeTab, 'create') && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
        this.$analytics.sendEvent('cg_lesson_plan_click_cre_exemp')
      } else if (this.$route.name === 'AddLesson' && equalsIgnoreCase(this.activeTab, 'adapt') && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
        this.$analytics.sendEvent('cg_lesson_plan_click_adp_exemp')
      }
      this.autoApplyNextSample()
    },
    equalsIgnoreCase,
    showTooltip () {
      this.tooltipVisible = true
    },
    hideTooltip () {
      this.tooltipVisible = false
    },
    // 助手表单校验
    async checkAssistantForm () {
      return validateForms()
    },
    // 回填助手历史数据
    historyLessonAssistantInfo () {
      if (!this.lesson || !this.lesson.assistantAdaptInfo) {
        // console.log('没有历史记录')
        return false
      }
      // console.log('有历史记录', JSON.stringify(this.lesson.assistantAdaptInfo))
      let lessonAssistantInfoHis = JSON.parse(this.lesson.assistantAdaptInfo)
      this.lessonAssistantInfo = {
        lessonContent: lessonAssistantInfoHis.lessonContent,
        adaptationContent: lessonAssistantInfoHis.adaptationContent || '',
        ageGroup: lessonAssistantInfoHis.ageGroup || 'Grade 3',
        activityType: lessonAssistantInfoHis.activityType || 'LARGE_GROUP',
        frameworkId: lessonAssistantInfoHis.frameworkId || 'D8520C73-D47A-49E4-A01F-66BC4EAD7BCB',
        frameworkName: lessonAssistantInfoHis.frameworkName || 'California Preschool/Transitional Kindergarten Learning Foundations',
        centerThemeName: lessonAssistantInfoHis.centerThemeName || '',
        measureIds: lessonAssistantInfoHis.measureIds || [],
        domains: lessonAssistantInfoHis.domains || [],
        haveSelectMeasureId: lessonAssistantInfoHis.haveSelectMeasureId,
        state: lessonAssistantInfoHis.state || 'California',
        useLocation: lessonAssistantInfoHis.useLocation,
        country: lessonAssistantInfoHis.country || 'United States',
        city: lessonAssistantInfoHis.city || '',
        learnerProfiles: lessonAssistantInfoHis.learnerProfiles || [],
        rubricsOptions: lessonAssistantInfoHis.rubricsOptions || [],
        useDomain: lessonAssistantInfoHis.useDomain || !(lessonAssistantInfoHis.measureIds && lessonAssistantInfoHis.measureIds.length > 0),
        classroomType: lessonAssistantInfoHis.classroomType || 'IN_PERSON',
        isHistoryData: true, // 表示现在数据为历史回填数据
      }
      if (this.lessonAssistantInfo.useDomain) {
        this.lessonAssistantInfo.measureIds = []
        this.lessonAssistantInfo.haveSelectMeasureId = false
      }
      // 框架对应的州
      if (lessonAssistantInfoHis.frameworkState) {
        this.lessonAssistantInfo.frameworkState = lessonAssistantInfoHis.frameworkState
      } else {
        this.lessonAssistantInfo.frameworkState = lessonAssistantInfoHis.state || 'California'
      }
      this.activeTab = this.lessonAssistantInfo.adaptationContent ? 'adapt' : 'create'
      // 设置已经生成过课程
      this.generatedLesson = true
      return true
    },
    // 初始化课程助手信息
    initLessonAssistantInfo () {
      // 在周计划中 AI 助手不读取缓存
      if (this.inWeeklyPlannDialog) {
        this.initWeekPlanAssistantInfo()
        return
      }

      let lessonAssistantInfo = this.lessonAssistantInfo

      // 初始化基本信息
      this.lessonAssistantInfo = {
        lessonContent: '',
        adaptationContent: '',
        ageGroup: lessonAssistantInfo.ageGroupValue || 'Grade 3',
        activityType: lessonAssistantInfo.activityType || 'LARGE_GROUP',
        frameworkId: lessonAssistantInfo.frameworkId || 'D8520C73-D47A-49E4-A01F-66BC4EAD7BCB',
        frameworkName: lessonAssistantInfo.frameworkName || 'California Preschool/Transitional Kindergarten Learning Foundations',
        centerThemeName: '',
        domains: lessonAssistantInfo.domains || [],
        measureIds: [],
        haveSelectMeasureId: false,
        learnerProfiles: lessonAssistantInfo.learnerProfiles || [],
        rubricsOptions: lessonAssistantInfo.rubricsOptions || [],
        state: lessonAssistantInfo.state || 'California',
        useLocation: true,
        country: 'United States',
        city: '',
        useDomain: lessonAssistantInfo.useDomain || true,
        frameworkState: lessonAssistantInfo.frameworkState || lessonAssistantInfo.state || 'California',
        classroomType: lessonAssistantInfo.classroomType || 'IN_PERSON'
      }

      // 获取历史数据
      let localLessonAssistantInfo = JSON.parse(localStorage.getItem('LESSON_ASSISTANT_INFO' + this.currentUserId))

      // 回填历史数据
      if (localLessonAssistantInfo) {
        // 需要回填上次用户选择的框架 ID, 年级
        Object.assign(this.lessonAssistantInfo, {
          frameworkId: localLessonAssistantInfo.frameworkId || 'D8520C73-D47A-49E4-A01F-66BC4EAD7BCB',
          frameworkName: localLessonAssistantInfo.frameworkName || 'California Preschool/Transitional Kindergarten Learning Foundations',
          frameworkState: localLessonAssistantInfo.frameworkState || 'California',
          ageGroup: localLessonAssistantInfo.ageGroup || 'Grade 3',
          state: localLessonAssistantInfo.state || 'California',
          classroomType: localLessonAssistantInfo.classroomType || 'IN_PERSON'
        })
      }
    },
    // 在周计划中 AI 助手初始化
    initWeekPlanAssistantInfo () {
      let localLessonAssistantInfo = JSON.parse(localStorage.getItem('LESSON_ASSISTANT_INFO' + this.currentUserId))
      let classroomType = (localLessonAssistantInfo && localLessonAssistantInfo.classroomType) ? localLessonAssistantInfo.classroomType : 'IN_PERSON'
      this.lessonAssistantInfo = {
        lessonContent: '',
        adaptationContent: '',
        ageGroup: this.selectedFramework.grade,
        activityType: 'LARGE_GROUP',
        frameworkId: this.selectedFramework.frameworkId,
        centerThemeName: '',
        measureIds: [],
        haveSelectMeasureId: false,
        state: 'California',
        useLocation: true,
        country: 'United States',
        city: '',
        frameworkState: 'California',
        useDomain: true, // 是否使用自动分配
        learnerProfiles: [],
        rubricsOptions: [],
        classroomType: classroomType
      }
    },
    /**
     * 更新位置的值
     */
    updateLocation (country, state, city) {
      let stateCities = []
      // 特殊处理墨西哥，无州信息，需要特殊处理
      let useDefaultUS = false
      switch (country.toLowerCase()) {
        case 'united states':
          this.statesAndCities = usStatesAndCities
          this.states = this.usStates
          break
        case 'mexico':
          this.statesAndCities = mexicoStatesAndCities
          this.states = mexicoStatesAndCities.map(item => item.name).sort()
          break
        case 'canada':
          this.statesAndCities = canadaStatesAndCities
          this.states = canadaStatesAndCities.map(item => item.name).sort()
          break
        default:
          // 默认使用 US 的州和城市
          useDefaultUS = true
          this.statesAndCities = usStatesAndCities
          this.states = this.usStates
      }
      if (useDefaultUS) {
        stateCities = this.statesAndCities.find(item => equalsIgnoreCase(item.name, 'California')).cities.sort()
      } else {
        stateCities = this.statesAndCities.find(item => equalsIgnoreCase(item.name, state)).cities.sort()
      }
      let userCity = city || this.currentUser.city
      if (userCity && stateCities.includes(userCity)) {
        this.lessonAssistantInfo.city = userCity
        this.location.city = userCity
      } else {
        this.lessonAssistantInfo.city = ''
        this.location.city = ''
      }
    },
    /**
     * 修改国家
     */
    changeCountry (country) {
      this.lessonAssistantInfo.country = country
      // 特殊处理墨西哥，无州信息，需要特殊处理
      switch(country.toLowerCase()) {
        case 'united states':
          this.statesAndCities = usStatesAndCities
          this.states = this.usStates
          break
        case 'mexico':
          this.statesAndCities = mexicoStatesAndCities
          this.states = mexicoStatesAndCities.map(item => item.name).sort()
          break
        case 'canada':
          this.statesAndCities = canadaStatesAndCities
          this.states = canadaStatesAndCities.map(item => item.name).sort()
          break
        default:
          this.statesAndCities = usStatesAndCities
          this.states = this.usStates
      }

      // 设置州和城市
      this.lessonAssistantInfo.state = this.states[0]
      // 找到对应州的城市列表并排序
      let stateCities = this.statesAndCities.find(item => equalsIgnoreCase(item.name, this.states[0])).cities.sort()
      let userCity = this.currentUser.city
      if (userCity && stateCities.includes(userCity)) {
        this.lessonAssistantInfo.city = userCity
      } else {
        this.lessonAssistantInfo.city = ''
      }
      this.location.country = this.lessonAssistantInfo.country
      this.location.state = this.lessonAssistantInfo.state
      this.location.city = this.lessonAssistantInfo.city
    },
    // 切换州以后后面的城市应该清空
    changeState (state) {
      let stateCities = this.statesAndCities.find(item => equalsIgnoreCase(item.name, state)).cities.sort()
      let userCity = this.currentUser.city
      if (userCity && stateCities.includes(userCity)) {
        this.lessonAssistantInfo.city = userCity
      } else {
        this.lessonAssistantInfo.city = ''
      }
      this.location.city = this.lessonAssistantInfo.city
    },
    // 位置信息默认值
    initLocationInfo () {
      // 如果是历史回填数据，不需要设置默认值
      if (this.lessonAssistantInfo.isHistoryData || (!this.inWeeklyPlannDialog && JSON.parse(localStorage.getItem('LESSON_ASSISTANT_INFO' + this.currentUserId)))) {
        return
      }
      if (user.state.currentUser.country !== null && user.state.currentUser.state !== null && this.states.includes(user.state.currentUser.state)) {
        this.lessonAssistantInfo.country = user.state.currentUser.country
        this.lessonAssistantInfo.state = user.state.currentUser.state
        this.location.country = user.state.currentUser.country
        this.location.state = user.state.currentUser.state
        this.location.city = user.state.currentUser.city
      }
    },
    /**
     * 获取州信息
     */
    getState () {
      // 发送请求获取 states 列表
      return new Promise((resolve, reject) => {
        this.$axios.get($api.urls().getStates)
          .then(response => {
            // 处理返回的数据
            this.usStates = response
            this.states = response
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 州选择后省略号
    selectVisible (e,refName) {
      e == false ? this.$refs[refName].blur() : ''
    },
    /**
     * 结束引导
     */
    endGuide (hide) {
    },
    /**
     * 开始引导
     */
    startGuide (step) {

    },
    /**
     * 年龄段名称发生变化
     */
    changeActivityType (activityType) {
      this.$emit('changeActivityType', activityType)
    },
    /**
     * 获取框架测评点信息
     */
    getFrameworkDomains (frameworkId) {
      // 没有传入框架 ID，则查询当前选择框架的测评点
      if (!frameworkId) {
        frameworkId = this.lessonAssistantInfo.frameworkId
      }
      // 开始 Loading
      this.loadFrameworkLoading = true
      // 获取测评点
      this.$store.dispatch('curriculum/getFrameworkDomains', frameworkId).then(domains => {
        this.domains = domains
        this.loadFrameworkLoading = false
      }).finally(() => {
        this.loadFrameworkLoading = false
      })
    },

    /**
     * 请求失败统一关闭 loading 处理
     */
    requestErrorProcessing (error) {
      // 关闭全部的 loading
      this.closeAllLoading()
      if (!error.message) {
        this.$message.error('Internal server error.')
      } else if (error.message !== '"NO_DATA"') {
        this.$message.error(error.message)
      }
    },

    /**
     * 关闭全部的 loading
     */
    closeAllLoading () {
      // 关闭生成测评点的 loading
      this.generateMeasuresLoading = false
      // 关闭生成课程的 loading
      this.convertLessonLoading = false
      // 关闭生成课程模板的 loading
      this.generateLessonTemplateLoading = false
      // 关闭生成 Google Slide 数据的 loading
      this.generateGoogleSlideDataLoading = false
      // 关闭生成课程幻灯片的 loading
      this.generateLessonSlidesStreamLoading = false
      // 关闭加载框架的 loading
      this.updateConvertLessonLoading(false)
      // 关闭生成图片的 loading
      this.updateLessonCoverLoading(false)
    },

    /**
     * 生成测评点
     */
    generateMeasures () {
      // 开始 Loading
      // this.generateMeasuresLoading = true
      // 参数
      let params = {
        frameworkId: this.lessonAssistantSelectedInfo.frameworkId,
        lessonContent: this.lessonAssistantSelectedInfo.lessonContent,
        domainIds: this.lessonAssistantSelectedInfo.domains
      }
      // 发送请求
      return new Promise(async (resolve, reject) => {
        await this.$axios.post($api.urls().generateMeasuresByLesson, params).then((res) => {
          // 求用户自己选择的和ai生成的测评点并集，注意将原始数组转换为小写形式然后求并集
          this.lessonAssistantSelectedInfo.measureIds = Array.from(new Set([...this.lessonAssistantSelectedInfo.measureIds, ...res.measureIds].map(id => id.toUpperCase())))
          resolve()
        }).catch(error => {
          // 调用请求失败处理
          this.requestErrorProcessing(error)
          reject(error)
        })
      })
    },

    // 验证所有表单
    async validateForms () {
      // 验证主表单
      const mainFormValid = await new Promise((resolve) => {
        this.$refs.lessonAssistantFormRef.validate((valid) => {
          resolve(valid)
        })
      })

      let subFormValid = false
      // 验证 PublicLessonAssistantInfo 组件中的领域和测评点
      const publicLessonAssistantInfo = this.$refs.publicLessonAssistantInfo
      if (publicLessonAssistantInfo) {
        await publicLessonAssistantInfo.$refs.fromRef.validate(valid => {
          subFormValid = valid
        })
        if (!subFormValid) {
          return false
        }
      }
      return mainFormValid && subFormValid
    },

    async convertLessonConfirm () {
      // 关闭引导
      this.showExemplarsTip = false
      this.showExemplarsTipManual = false
      if (this.$route.name === 'AddLesson' && !this.generatedLesson) {
        // 添加课程时生成课程埋点
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_generate')
      } else if (this.$route.name === 'AddLesson' && this.generatedLesson) {
        // 重新生成课程埋点
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_regenerate')
      }

      // 验证所有表单
      const valid = await this.validateForms()
      if (!valid) {
        return
      }

      this.convertedLessonContent = ''
      // 如果已经生成过给出确认提示
      if (this.generatedLesson) {
        this.$confirm('Are you sure you want to regenerate this lesson plan, which will overwrite the previous one.', 'Confirmtion', {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel'
        }).then(() => {
          // 先校验课程，再生成
          this.checkCurriculumLessonContentByAI(true)
        }).catch(() => {})
      } else {
        // 先校验课程，再生成
        this.checkCurriculumLessonContentByAI(true)
      }
    },

    /**
     * 清空课程
     */
    clearLesson () {
      // 清空父组件中课程相关的内容
      this.$emit('clearLesson')
      this.$emit('generaterdLessoncomplete', false)
      this.$emit('setGenerateUniversalState', false)
      // 清空本组件中课程相关的内容
      this.lessonStepTwo = {
        universalDesignForLearning: '',
        culturallyResponsiveInstruction: '',
        typicalBehaviors: [],
        typicalBehaviorsData: '',
        lessonCover: (this.lesson && this.lesson.cover) || null,
        homeActivity: '',
        questions: ''
      }
    },

    // 判断内容是否有效
    checkCurriculumLessonContentByAI (needSavePrompt) {
      // 开始 Loading
      this.convertLessonLoading = true
      // 参数
      let params = {
        lessonContent: this.lessonAssistantInfo.lessonContent,
        // 州
        state: this.lessonAssistantInfo.state,
        // 年龄组
        ageGroup: this.lessonAssistantInfo.ageGroup,
        // Adaptation Ideas 内容
        adaptationContent: this.lessonAssistantInfo.adaptationContent
      }
      // 先设置为未完成状态
      this.$emit('generaterdLessoncomplete', false)
      // 发送请求
      return new Promise(async (resolve, reject) => {
        await this.$axios.post($api.urls().checkLessonContentByAI, params).then((res) => {
          // 如果校验通过，开始生成课程 （书校验移除）
          if (res.result && !res.wrongBooks) {
            // 更新课程转换的 loading 为 true
            // this.updateConvertLessonLoading(true)
            // 在校验通过时才切换显示
            // this.$emit('switchAiToolShow')
            // 清空课程
            // this.clearLesson()
            // 开始生成
            this.convertLessonStream(res.correctBooks, res.wrongBooks, null, null, null, null, needSavePrompt)
          } else if (res.result && res.wrongBooks) {
            // 如果校验通过但书校验未通过，弹出对话框提示是否继续生成，如果继续生成，则开始生成课程
            this.$confirm(this.$t('loc.generaetLessonDetailAdjustBookTip', { books: res.wrongBooks }), this.$t('loc.confirmation'), {
              confirmButtonText: this.$t('loc.generaetLessonDetailAdjustBookContinue'),
              cancelButtonText: this.$t('loc.generaetLessonDetailAdjustBookEdit'),
              customClass: 'model-width-700',
              closeOnClickModal: false,
              closeOnPressEscape: false
            }).then(() => {
              // 更新课程转换的 loading 为 true
              // this.updateConvertLessonLoading(true)
              // 清空课程
              // this.clearLesson()
              // 开始生成
              this.convertLessonStream(res.correctBooks, res.wrongBooks, null, null, null, null, needSavePrompt)
            }).catch(() => {
              // 更新 loading 为 false
              this.convertLessonLoading = false
              // 更新课程转换的 loading 为 false
              this.updateConvertLessonLoading(false)
            })
          } else {
            // 不符合置为 true
            this.convertLessonLoading = false
            this.$emit('generaterdLessoncomplete', false)
            // 更新 loading 为 false
            this.convertLessonLoading = false
            // 判断 adaptationCheckExecuted 并显示相应的错误信息
            if (res.adaptationCheckExecuted) {
              this.$message.error(this.$t('loc.unableToProcessRequestAdjustLessonPlan'))
            } else {
              this.$message.error(this.$t('loc.unableToProcessRequestAdjustDescription'))
            }
          }
          resolve()
        }).catch(error => {
          // 调用请求失败处理
          this.requestErrorProcessing(error)
          reject(error)
        })
      })
    },

    // redesign 时课程数据回填
    async redesignLessonFill (copyLesson) {
      // 框架没有变更取历史测评点，否则进行推荐
      if (equalsIgnoreCase(copyLesson.framework.frameworkId, this.lessonAssistantSelectedInfo.frameworkId) && copyLesson.measures && copyLesson.measures.length > 0) {
        let copyMeasureIds = []
        frameworkUtils.getMeasuresBottomIds(copyLesson.measures, copyMeasureIds)
        this.lessonAssistantSelectedInfo.measureIds = copyMeasureIds
      } else {
        // 没有选中测评点，进行推荐
        if (!this.lessonAssistantSelectedInfo.haveSelectMeasureId) {
          // 推荐测评点
          await this.generateMeasures()
        }
      }
    },

    // 增加想法重新生成课程，不依赖于 AI 助手数据
    async generalConvertLessonStream (redesignData, redesignLessonInfo, copyLesson, isAdaptedLesson) {
      // 获取框架领域数据
      this.getFrameworkDomains(this.lesson.framework.frameworkId)
      // 将课程数据赋值到 lessonAssistantSelectedInfo 中，便于后续参数获取，并复用第二步每个模块生成方法
      this.setlessonAssistantSelected()
      // 清空课程
      this.clearLesson()
      // 生成时上次数据清空
      this.convertedLessonContent = ''
      // 通过 Update and Regenerate 按钮点击生成时也需要将按钮置为Loading
      this.convertLessonLoading = true
      // 更新课程转换的 loading 为 true
      this.updateConvertLessonLoading(true)
      let failedTip = null
      // 获取测评点
      let measureIds = []
      frameworkUtils.getMeasuresBottomIds(redesignData.measures, measureIds)
      // 请求参数
      let params = {
        measureIds: measureIds,
        frameworkId: this.lessonAssistantSelectedInfo.frameworkId,
        redesignLessonInfo: redesignLessonInfo,
        redesignIdea: redesignData.redesignIdea,
        ageGroup: this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup),
        enhanceActivityDuration: redesignData.activityTime,
        activityDuration: copyLesson.activityTime, // 设置原课程的活动时间
        activityType: this.lessonAssistantSelectedInfo.activityType, // 设置原课程的活动类型
        centerThemeName: this.lessonAssistantSelectedInfo.centerThemeName, // 设置原课程的主题
        rubrics: this.lessonAssistantSelectedInfo.learnerProfiles, // 校训
        useLocation: false, // 设置原课程的地点
        classroomType: this.lessonAssistantSelectedInfo.classroomType
      }
      // 初始化只调用一次的标志
      let updateCalled = false
      // 消息回调
      let messageCallback = (message) => {
        // 更新数据
        this.convertedLessonContent += message.data
        if (this.convertedLessonContent.length < 10) {
          // 如果数据中包含 yes，则说明输入的提示词不合适，提示用户重试
          const template = this.convertedLessonContent
            .replace(/[`]/g, '') // 去除反引号
            .trim() // 去除首尾空格
          const result = template.length >= 3 ? template.slice(0, 3) : template
          if (equalsIgnoreCase('yes', result) && !failedTip) {
            failedTip = true
            this.$message.error(this.$t('loc.unableToProcessEnhanceAdjustLessonPlan'))
            // 恢复旧课程数据
            this.$emit('updateLessonInfo', copyLesson)
            // 更新课程转换的 loading 为 false
            this.updateConvertLessonLoading(false)
            // 更新课程封面的 loading 为 false
            this.updateLessonCoverLoading(false)
            this.$emit('setGenerateUniversalState', true)
            return
          }
        }
        // 解析课程数据
        let parsedData = this.parseLessonContentData(this.convertedLessonContent)
        parsedData.ageGroup = this.lessonAssistantSelectedInfo.ageGroup
        // 更新数据
        this.$emit('updateLessonContent', parsedData)
        // 当生成活动时间后填充框架和测评点, 只需调用一次
        if (parsedData.activityTime && !updateCalled) {
          this.$emit('updateLessonFrameworkAndMeasures', this.lessonAssistantSelectedInfo.frameworkId, measureIds, this.lessonAssistantSelectedInfo.classroomType)
          updateCalled = true
        }
      }
      // 更新课程测评点
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource($api.urls().generateCurriculumLessonPlanStream, {}, messageCallback, 'POST', params)
          .then(async (res) => {
            // 回填课程材料数据
            this.setLessonMaterialData(copyLesson)
            // 如果是根据 redesignIdea 重新生成的失败
            if (failedTip) {
              this.convertLessonLoading = false
              // 避免测评点弹窗弹开
              this.$emit('updateLessonMeasureSelectNotShow')
              return
            }
            // 只有在生成时才会改编 index 页的 center 属性
            this.$emit('upSubclassCenter', this.lessonAssistantSelectedInfo.activityType)

            if (this.lessonAssistantSelectedInfo.activityType !== 'CENTER' && this.lessonAssistantSelectedInfo.activityType !== 'STATION') {
              // 触发实施步骤资源生成
              this.$emit('generateImpStepResourceAssistant')
              // 批量翻译 DLL 词汇
              this.$emit('batchTranslateDllVocabularies')
            }
            // 生成课程模板
            this.generateLessonTemplate(copyLesson)
            // 开始生成课程步骤的第二部分：差异化教学内容、文化响应式教学内容、典型行为
            await this.generateLessonStepTwo(false, isAdaptedLesson, copyLesson)
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            resolve()
          })
          .catch(error => {
            // 恢复旧课程数据
            this.$emit('updateLessonInfo', copyLesson)
            // 更新课程转换的 loading 为 false
            this.updateConvertLessonLoading(false)
            // 更新课程封面的 loading 为 false
            this.updateLessonCoverLoading(false)
            this.$emit('setGenerateUniversalState', true)
            // 调用请求失败处理
            this.requestErrorProcessing(error)
            reject(error)
          })
      })
    },

    // 生成课程 google slide 数据
    async generateLessonTemplate (lesson) {
      let lessonTemplate = lesson.templateType
      if (!lessonTemplate) {
        return
      }
      // 需要判断 eduProtocolsTemplatesFlag 是否为 true,如果不为 true,则不生成课程模版数据
      if (this.adaptedModuleSwitch && !this.adaptedModuleSwitch.eduProtocolsTemplatesFlag) {
        return
      }

      try {
        this.generateLessonTemplateLoading = true

        await this.saveDraft(true)

        let params = {
          lessonId: lesson.id,
          lessonTemplateType: lessonTemplate,
          onlyGenerateTemplate: true
        }

        let convertedLessonContent = ''

        let messageCallback = (message) => {
          convertedLessonContent += message.data
          convertedLessonContent = convertedLessonContent.replace('```', '')
          this.googleSlideData = this.parseGoogleSlideData(
            convertedLessonContent,
            lessonTemplate,
            lesson.steps[0] && lesson.steps[0].ageGroupName
          )
        }

        // 等待 createEventSource 完成
        await createEventSource(
          $api.urls().generateAILessonTemplateStream,
          null,
          messageCallback,
          'POST',
          params
        )

        // 等待 generateGoogleSlideData 完成
        lesson.lessonTemplateType = lessonTemplate

        await this.generateGoogleSlideData(lesson)
      } catch (error) {
      } finally {
        this.generateLessonTemplateLoading = false
        this.$nextTick(() => {
          this.generaterdLessoncomplete()
        })
      }
    },

    // 赋值增加想法重新生成课程所需数据
    setlessonAssistantSelected () {
      // 获取课程类型
      const activityType = this.lesson.activityType ? this.lesson.activityType : 'LARGE_GROUP'
      // 获取课程类型对象
      const activityTypeObj = PublicLessonAssistantInfoActivityType.find(item => equalsIgnoreCase(item.typeValue, activityType) || equalsIgnoreCase(item.typeName, activityType))
      // 赋值课程类型
      if (activityTypeObj) {
        this.lessonAssistantSelectedInfo.activityType = activityTypeObj.typeValue
      } else {
        this.lessonAssistantSelectedInfo.activityType = 'LARGE_GROUP'
      }
      // 赋值框架数据
      this.lessonAssistantSelectedInfo.frameworkId = this.lesson.framework && this.lesson.framework.frameworkId
      // 赋值年龄组
      this.lessonAssistantSelectedInfo.ageGroup = this.lesson.ages && this.lesson.ages.length > 0 ? this.lesson.ages[0].value : 'Grade 1'
      // 获取测评点
      let measures = []
      frameworkUtils.getMeasuresBottom(this.lesson.measures, measures)
      // 赋值测评点
      this.lessonAssistantSelectedInfo.measureIds = measures.map(measure => measure.id)
      
      // 使用封装的方法检查年级和活动类型
      const isKAndAbove = this.checkIsKAndAboveGrade(this.lessonAssistantSelectedInfo.ageGroup)
      const isCenterOrStation = this.checkIsCenterOrStation(this.lessonAssistantSelectedInfo.activityType)      
      // 根据条件设置课堂类型
      this.lessonAssistantSelectedInfo.classroomType = (isKAndAbove && !isCenterOrStation) ? this.lesson.classroomType : 'IN_PERSON'
      
      // 根据活动类型设置中心主题
      if (!isCenterOrStation) {
        // 如果不是 CENTER 和 STATION，直接将 centerThemeName 设置为空
        this.lessonAssistantSelectedInfo.centerThemeName = ''
      } else if (!this.lesson.activityTheme) { // 如果 activityType 是 CENTER 或 STATION 时，如果 activityTheme 为空，则设置一个默认值
        const centerThemeNameResult = this.$refs.publicLessonAssistantInfo && this.$refs.publicLessonAssistantInfo.getCenterThemeNameByAgeGroup(this.lessonAssistantSelectedInfo.ageGroup)
        this.lessonAssistantSelectedInfo.centerThemeName = centerThemeNameResult.centerThemeName || 'Art'
      } else { // 如果 activityType 是 CENTER 或 STATION 时，并且 activityTheme 不为空，则设置 activityTheme
        this.lessonAssistantSelectedInfo.centerThemeName = this.lesson.activityTheme
      }
      this.lessonAssistantSelectedInfo.rubricsOptions = this.lessonAssistantInfo.rubricsOptions
      this.lessonAssistantSelectedInfo.learnerProfiles = this.lessonAssistantInfo.learnerProfiles

    },

      /**
       * 检查年级是否为 K 及以上
       * @param {String} ageGroup - 年龄组
       * @return {Boolean} - 是否为 K 及以上年级
       */
      checkIsKAndAboveGrade(ageGroup) {
        const kAndAboveGrades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
        const currentGrade = this.getAgeGroupName(ageGroup)
        return kAndAboveGrades.includes(currentGrade)
      },
      
      /**
       * 检查活动类型是否为 Center 或 Station
       * @param {String} activityType - 活动类型
       * @return {Boolean} - 是否为 Center 或 Station
       */
      checkIsCenterOrStation(activityType) {
        return this.equalsIgnoreCase(activityType, 'CENTER') || 
               this.equalsIgnoreCase(activityType, 'STATION')
      },

      
    // 回填课程材料数据
    setLessonMaterialData (copyLesson) {
      // 获取课程材料数据
      let material = JSON.parse(JSON.stringify(copyLesson.material))
      if (this.lesson.material && this.lesson.material.description) {
        // 设置新生成的描述信息
        material.description = this.lesson.material.description
      }
      this.lesson.material = material
      // 回填实施步骤附件
      if (this.lesson.steps && this.lesson.steps[0] && copyLesson.steps && copyLesson.steps[0]) {
        this.lesson.steps[0].media = copyLesson.steps[0].media
      }
    },

    // 清除课程数据，如果是改编课程，保留改编的数据
    clearLessonExceptUdlClr (exceptUdlClr = false) {
      this.lesson.objective = ''
      this.lesson.material = {}
      this.lesson.dlls = []
      // 清除课程第二部分数据
      let step = this.lesson.steps && this.lesson.steps[0]
      // 如果不存在直接返回
      if (!step) {
        return
      }
      step.content = ''
      step.teachingTips = []
      step.lessonStepGuides = []
      step.questions = []
      step.homeActivity = ''
      step.lessonImpStepAndSource = {}
      // 判断是否需要保留 UDL CLR
      if (!exceptUdlClr) {
        step.culturallyResponsiveInstruction = ''
        step.culturallyResponsiveInstructionGeneral = ''
        step.lessonClrAndSources = {}
        step.lessonSource = ''
        step.universalDesignForLearning = ''
        step.universalDesignForLearningGroup = ''
      }
    },

    /**
     * 转换课程内容
     */
    async convertLessonStream (correctBooks, wrongBooks, redesignIdea, copyLesson, redesignLessonInfo, lessonTemplate, needSavePrompt, isAdaptedLesson=false) {
      // 在重新生成之前自动保存当前课程
      if (this.lesson && this.lesson.id && this.saveLesson && typeof this.saveLesson === 'function' && !this.batchEdit && !lessonTemplate) {
        // 保存课程
        await this.saveLesson()
      }
      // 生成课程时同步助手工具栏状态
      this.saveAssistantToolState()
      // 设置助手选中的信息
      this.fillLessonAssistantSelectedInfo()
      if (needSavePrompt) {
        // 助手数据记录到课程，第一次保存草稿时需要
        this.saveAssistantInfoToLesson()
        // 记住用户本次选择
        localStorage.setItem('LESSON_ASSISTANT_INFO' + this.currentUserId, JSON.stringify(this.lessonAssistantInfo))
        // 创建历史记录
        this.createPromptHistory()
      }
      // 没有课程ID，则先保存课程
      if (this.saveDraft && typeof this.saveDraft === 'function' && (!this.lesson || !this.lesson.id)) {
        // 保存草稿，并设置临时草稿 ID（传入true参数）
        let lesson = await this.saveDraft(true)
        // 如果没有 ID 并且是添加课程路径，将课程 ID 加入路由参数 params
        if (!this.$route.params.lessonId && (this.$route.name === 'AddLesson' || this.$route.name === 'AddLessonPlan')) {
          // 将新创建的课程 ID 添加到路由参数中
          this.$router.push({
            name: this.$route.name,
            params: {
              lessonId: lesson.id
            }
          })
        }
      }
      this.convertLessonLoading = true
      // 更新课程转换的 loading 为 true
      this.updateConvertLessonLoading(true)
      // 生成时上次数据清空
      this.convertedLessonContent = ''
      // 清空课程
      this.clearLesson()
      if (needSavePrompt) {
        // 清空课程之后，助手数据再次同步到课程
        this.saveAssistantInfoToLesson()
      }
      // 如果 lessonTemplate 存在, 则设置课程模板
      if (lessonTemplate) {
        this.lesson.templateType = lessonTemplate
      }
      // 在用户确认继续生成时才切换显示
      this.$emit('switchAiToolShow')
      // 更新课程封面的 loading 为 true
      this.updateLessonCoverLoading(true)
      if (lessonTemplate) {
        // 如果是使用课程模版生成课程，框架和测评点直接取课程模版的框架和测评点
        this.lessonAssistantSelectedInfo.frameworkId = copyLesson.framework.frameworkId
        this.lessonAssistantInfo.frameworkId = copyLesson.framework.frameworkId
      }
      // 设置框架缓存
      this.setFrameCache()
      // 清除 promptRecordId
      this.promptUsageRecordIds = []
      let failedTip = null
      // 不选测评点时，生成测评点 ,对于使用 redesignIdea 重新生成时候，框架一致的话直接取旧测评点
      if (!lessonTemplate) {
        if (redesignLessonInfo && redesignIdea && copyLesson) {
          // 标识是否输入优化的提示词不合适
          failedTip = false
          await this.redesignLessonFill(copyLesson)
        } else if (!this.lessonAssistantSelectedInfo.haveSelectMeasureId) {
          await this.generateMeasures()
        }
      } else {
        // 使用课程模版生成课程时，设置课程选择的测评点
        let copyMeasureIds = []
        frameworkUtils.getMeasuresBottomIds(copyLesson.measures, copyMeasureIds)
        this.lessonAssistantSelectedInfo.measureIds = copyMeasureIds
        this.getFrameworkDomains(copyLesson.framework.frameworkId)
      }

      // 进入第二步
      this.$emit('updateAddLessonStep', 2)

      // 请求参数
      let params = {
        frameworkId: this.lessonAssistantSelectedInfo.frameworkId,
        frameworkName: this.lessonAssistantSelectedInfo.frameworkName,
        lessonContent: this.lessonAssistantSelectedInfo.lessonContent,
        activityType: this.lessonAssistantSelectedInfo.activityType,
        ageGroup: this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup),
        measureIds: this.lessonAssistantSelectedInfo.measureIds,
        centerThemeName: this.lessonAssistantSelectedInfo.centerThemeName,
        country: this.lessonAssistantSelectedInfo.country,
        state: this.lessonAssistantSelectedInfo.state,
        city: this.lessonAssistantSelectedInfo.city,
        adaptationContent: this.lessonAssistantSelectedInfo.adaptationContent,
        useLocation: this.lessonAssistantSelectedInfo.useLocation,
        correctBooks: correctBooks,
        wrongBooks: wrongBooks,
        redesignLessonInfo: redesignLessonInfo,
        redesignIdea: redesignIdea,
        rubrics: this.lessonAssistantSelectedInfo.learnerProfiles, // 校训
        classroomType: this.lessonAssistantSelectedInfo.classroomType // 课堂类型
      }
      // 初始化只调用一次的标志
      let updateCalled = false
      // 如果 lessonTemplate 为空或者有 redesignIdea 并且课程模版不为空 ，则用之前的逻辑生成课程
      if (!lessonTemplate || (redesignIdea && lessonTemplate)) {
        // 消息回调
        let messageCallback = (message) => {
          // 更新数据
          this.convertedLessonContent += message.data
          if (!failedTip && this.convertedLessonContent.length < 10) {
            // 如果数据中包含 yes，则说明输入的提示词不合适，提示用户重试
            const template = this.convertedLessonContent
              .replace(/[`]/g, '') // 去除反引号
              .trim() // 去除首尾空格
            const result = template.length >= 3 ? template.slice(0, 3) : template
            if (equalsIgnoreCase('yes', result) && !failedTip) {
              failedTip = true
                // 根据 generatedLesson 状态决定处理方式
              if (this.generatedLesson || !this.isAddLesson) {
                // 如果已经生成过课程，使用原来的通知形式
                this.$message.error(this.$t('loc.unableToProcessRequestAdjustLessonPlan'))
                // 恢复旧课程数据
                this.$emit('updateLessonInfo', copyLesson)
                // 更新课程转换的 loading 为 false
                this.updateConvertLessonLoading(false)
                // 更新课程封面的 loading 为 false
                this.updateLessonCoverLoading(false)
                this.$emit('setGenerateUniversalState', true)
                this.closeAllLoading()
              } else {
                // 如果未生成过课程，弹出确认弹窗
                this.$confirm(this.$t('loc.unableToProcessRequestAdjustLessonPlan'), this.$t('loc.confirmation'), {
                  confirmButtonText: 'Confirm'
                }).then(() => {
                }).catch(() => {
                }).finally(() => {
                  // 恢复旧课程数据
                  this.$emit('updateLessonInfo', copyLesson)
                  // 更新课程转换的 loading 为 false
                  this.updateConvertLessonLoading(false)
                  // 更新课程封面的 loading 为 false
                  this.updateLessonCoverLoading(false)
                  this.$emit('setGenerateUniversalState', true)
                  this.closeAllLoading()
                  this.$emit('updateAddLessonStep', 1)
                })
              }
              return
            }
          }
          // 解析课程数据
          let parsedData = this.parseLessonContentData(this.convertedLessonContent)
          parsedData.ageGroup = this.lessonAssistantSelectedInfo.ageGroup
          // 更新数据
          this.$emit('updateLessonContent', parsedData)
          // 当生成活动时间后填充框架和测评点, 只需调用一次
          if (parsedData.activityTime && !updateCalled) {
            this.$emit('updateLessonFrameworkAndMeasures', this.lessonAssistantSelectedInfo.frameworkId, this.lessonAssistantSelectedInfo.measureIds, this.lessonAssistantSelectedInfo.classroomType)
            updateCalled = true
          }
        }
        // 更新课程测评点
        return new Promise((resolve, reject) => {
          // 生成单元概览
          createEventSource($api.urls().generateCurriculumLessonPlanStream, {}, messageCallback, 'POST', params)
            .then(async (res) => {
              // 如果是根据 redesignIdea 重新生成的失败
              if (failedTip) {
                this.convertLessonLoading = false
                // 避免测评点弹窗弹开
                this.$emit('updateLessonMeasureSelectNotShow')
                return
              }
              // 只有在生成时才会改编 index 页的 center 属性
              this.$emit('upSubclassCenter', this.lessonAssistantSelectedInfo.activityType)

              if (this.lessonAssistantSelectedInfo.activityType !== 'CENTER' && this.lessonAssistantSelectedInfo.activityType !== 'STATION') {
                // 触发实施步骤资源生成
                this.$emit('generateImpStepResourceAssistant')
                // 批量翻译 DLL 词汇
                this.$emit('batchTranslateDllVocabularies')
              }
              // 开始生成课程步骤的第二部分：差异化教学内容、文化响应式教学内容、典型行为
              await this.generateLessonStepTwo(true, isAdaptedLesson)
              // 记录 promptUsageRecordId
              if (res && res.promptUsageRecordId !== '') {
                this.promptUsageRecordIds.push(res.promptUsageRecordId)
              }
              resolve()
            })
            .catch(error => {
              // 调用请求失败处理
              this.requestErrorProcessing(error)
              reject(error)
            })
        })
      } else {
        // console.log('convertLessonStream - 使用课程模版生成课程')
        // 设置课程模版类型
        copyLesson.lessonTemplateType = lessonTemplate
        let params = {
          'lessonId': copyLesson.id,
          'lessonTemplateType': lessonTemplate
        }
        this.convertedLessonContent = ''
        let messageCallback = (message) => {
          // 更新数据
          this.convertedLessonContent += message.data
          this.convertedLessonContent = this.convertedLessonContent.replace('```', '')
          // 解析数据
          let parseKeys = this.getParseKeys()
          let parsedData = parseStreamData(this.convertedLessonContent, parseKeys, 1)[0]
          if (parsedData.moudle1) {
            this.googleSlideData = this.parseGoogleSlideData(parsedData.moudle1, lessonTemplate, parsedData.ageGroup)
          }
          // 赋值年龄信息
          this.lessonAssistantSelectedInfo.ageGroup = this.getAgeGroupValue(parsedData.ageGroup)
          // 更新数据
          this.$emit('updateLessonContent', parsedData)
          // 当生成活动时间后填充框架和测评点, 只需调用一次
          if (parsedData.activityTime && !updateCalled) {
            this.$emit('updateLessonFrameworkAndMeasures', this.lessonAssistantSelectedInfo.frameworkId, this.lessonAssistantSelectedInfo.measureIds, this.lessonAssistantSelectedInfo.classroomType)
            updateCalled = true
          }
        }
        // 更新课程测评点
        return new Promise((resolve, reject) => {
          // 生成单元概览
          createEventSource($api.urls().generateAILessonTemplateStream, {}, messageCallback, 'POST', params)
            .then(async (res) => {
              if (this.lesson && this.lesson.id && lessonTemplate) {
                // 生成 Google Slide 数据
                this.generateGoogleSlideData(copyLesson)
              }
              // 只有在生成时才会改编 index 页的 center 属性
              this.$emit('upSubclassCenter', this.lessonAssistantSelectedInfo.activityType)
              if (this.lessonAssistantSelectedInfo.activityType !== 'CENTER' && this.lessonAssistantSelectedInfo.activityType !== 'STATION') {
                // 触发实施步骤资源生成
                this.$emit('generateImpStepResourceAssistant')
                // 批量翻译 DLL 词汇
                this.$emit('batchTranslateDllVocabularies')
              }
              // 开始生成课程步骤的第二部分：差异化教学内容、文化响应式教学内容、典型行为
              await this.generateLessonStepTwo(true, isAdaptedLesson)
              // 记录 promptUsageRecordId
              if (res && res.promptUsageRecordId !== '') {
                this.promptUsageRecordIds.push(res.promptUsageRecordId)
              }
              resolve()
            })
            .catch(error => {
              // 调用请求失败处理
              this.requestErrorProcessing(error)
              reject(error)
            })
        })
      }
    },
    // 获取解析 keys
    getParseKeys () {
      let parseKeys = [
        { key: 'moudle1', name: ['Module 1', 'Module 1:'] },
        { key: 'moudle2', name: ['Module 2', 'Module 2:'] },
        { key: 'name', name: ['Lesson Title', 'Lesson Title:'] },
        { key: 'ageGroup', name: ['Age Group', 'Age Group:'] },
        { key: 'prepareTime', name: ['Preparation Time', 'Preparation Time:'] },
        { key: 'activityTime', name: ['Activity Duration', 'Activity Duration:'] },
        { key: 'measuresString', name: ['Measures', 'Measures:'] },
        { key: 'objectives', name: ['Objectives', 'Objective'] },
        { key: 'materials', name: 'Materials' },
        { key: 'keyVocabularyWords', name: ['Key Vocabulary Words', 'Key Vocabulary Words & Child-Friendly Definitions', '- Key Vocabulary Words:'] },
        { key: 'implementationSteps', name: 'Implementation Steps' }
      ]
      return parseKeys
    },

    // 解析 Google Slide 数据
    parseGoogleSlideData (data, type, ageGroup) {
      let parseKeys = []
      let parsedData = {}
      switch (type.toUpperCase()) {
        case 'FRAYER_MODEL':
          // 解析 Google Slide 数据
          parseKeys = [
            { key: 'centralTerm', name: ['Central Term:', 'Central Idea'] },
            { key: 'dimension1', name: ['- Dimension 1:', '- Dimension 1', 'Dimension 1:', 'Dimension 1'] },
            { key: 'dimension2', name: ['- Dimension 2:', '- Dimension 2', 'Dimension 2:', 'Dimension 2'] },
            { key: 'dimension3', name: ['- Dimension 3:', '- Dimension 3', 'Dimension 3:', 'Dimension 3'] },
            { key: 'dimension4', name: ['- Dimension 4:', '- Dimension 4', 'Dimension 4:', 'Dimension 4'] }
          ]
          parsedData = parseStreamData(data, parseKeys)
          parsedData = {
            'frayerList': parsedData.map(parseModel => {
              return {
                centralTerm: parseModel.centralTerm, // 设置 centralTerm
                dimension: [
                  parseModel.dimension1,
                  parseModel.dimension2,
                  parseModel.dimension3,
                  parseModel.dimension4
                ] // 转换四个维度字段为 List<String>
              }
            })
          }
          break
        case 'SKETCH_AND_TELL':
          parseKeys = [
            { key: 'keyword', name: ['Central Concept:', 'Central Concept'] }
          ]
          parsedData = parseStreamData(data, parseKeys)
          parsedData = { 'keywords': parsedData.map(x => x.keyword) }
          break
        case 'SKETCH_AND_TELL_O':
          parseKeys = [
            { key: 'centralTopic', name: ['Central Topic:', 'Central Topic'] },
            { key: 'task', name: ['Task:', 'Task'] }
          ]
          parsedData = parseStreamData(data, parseKeys)[0]
          parsedData.circleCount = this.getSketchAndTellOCircleCount(ageGroup)
          break
        case 'BOOKA_KUCHA':
          parseKeys = [
            { key: 'bookName', name: ['Book name:', 'Book name'] },
            { key: 'theme', name: ['BookaKucha theme:', 'BookaKucha theme'] },
            { key: 'instruction', name: ['Instruction:', 'Instruction'] }
          ]
          parsedData = parseStreamData(data, parseKeys)[0]
          break
        case 'THIN_SLIDE':
          parseKeys = [
            { key: 'keyTerm', name: ['Key Term:', 'Key Term'] }
          ]
          parsedData = parseStreamData(data, parseKeys)
          parsedData = { 'keyTerms': parsedData.map(x => x.keyTerm) }
          break
        case 'THIN_SLIDES_VARIATIONS':
          parseKeys = [
            { key: 'answer', name: ['Make a Thin Slide answering:', 'Make a Thin Slide answering'] }
          ]
          parsedData = parseStreamData(data, parseKeys)[0]
          break
        case 'WICKED_HYDRA':
          parseKeys = [
            { key: 'templateContent', name: ['Template Content:', 'Template Content'] }
          ]
          parsedData = parseStreamData(data, parseKeys)[0]
          break
      }
      parsedData.type = type
      parsedData.lessonName = this.lesson.name
      return parsedData
    },
    /**
     * 获取 SketchAndTellO 圈数
     */
    getSketchAndTellOCircleCount (ageGroup) {
      let ageValue = tools.getAgeValue(ageGroup)
      // 1. 从 K 到 Grade 2 是 4 个圈
      if (ageValue >= 6 && ageValue < 9) {
        return 4
      }
      // 2. 从 Grade 3 到 Grade 6 是 6 个圈
      if (ageValue >= 9 && ageValue < 12) {
        return 6
      }
      // 3. Grade 7 及以上均为 8 个圈
      if (ageValue >= 12) {
        return 8
      }
    },
    /**
     * 生产 Google Slide 数据
     */
    async generateGoogleSlideData (lesson) {
      // 如果存在 Google Slide 数据，则存储起来
      if (this.googleSlideData) {
        try {
          // 开始 loading
          this.generateGoogleSlideDataLoading = true
          let params = {
            type: lesson.lessonTemplateType,
            lessonId: this.lesson.id,
            lessonName: lesson.name,
            ageGroup: this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup),
            lessonTemplates: this.googleSlideData,
            generateSlide: false
          }
          const res = await this.$axios.post($api.urls().createLessonGoogleSlide, params)
          this.$emit('setStepsLessonTemplates', res)
        } finally {
          // 结束 loading
          this.generateGoogleSlideDataLoading = false
          // 检查课程生成是否完毕
          this.$nextTick(() => {
            this.generaterdLessoncomplete()
          })
        }
      }
    },
    // 保存助手的表单数据
    saveAssistantInfoToLesson () {
      let historyAssistantInfoInfo = {
        frameworkId: this.lessonAssistantSelectedInfo.frameworkId,
        frameworkName: this.lessonAssistantSelectedInfo.frameworkName,
        lessonContent: this.lessonAssistantSelectedInfo.lessonContent,
        activityType: this.lessonAssistantSelectedInfo.activityType,
        ageGroup: this.lessonAssistantSelectedInfo.ageGroup,
        measureIds: this.lessonAssistantSelectedInfo.measureIds,
        centerThemeName: this.lessonAssistantSelectedInfo.centerThemeName,
        domains: this.lessonAssistantSelectedInfo.domains,
        learnerProfiles: this.lessonAssistantSelectedInfo.learnerProfiles || [],
        rubricsOptions: this.lessonAssistantSelectedInfo.rubricsOptions || [],
        country: this.lessonAssistantSelectedInfo.country,
        state: this.lessonAssistantSelectedInfo.state,
        city: this.lessonAssistantSelectedInfo.city,
        adaptationContent: this.lessonAssistantSelectedInfo.adaptationContent,
        useLocation: this.lessonAssistantSelectedInfo.useLocation,
        haveSelectMeasureId: this.lessonAssistantSelectedInfo.haveSelectMeasureId,
        frameworkState: this.lessonAssistantSelectedInfo.frameworkState,
        useDomain: this.lessonAssistantSelectedInfo.useDomain,
        classroomType: this.showClassroomSetup ? this.lessonAssistantSelectedInfo.classroomType : 'IN_PERSON'
      }
      this.lessonAssistantInfo.classroomType = historyAssistantInfoInfo.classroomType
      // 助手数据记录到课程
      this.$emit('syncAssistantInfoToLesson', historyAssistantInfoInfo)
    },
    // 填充助手选中的信息
    fillLessonAssistantSelectedInfo () {
      if (this.lessonAssistantInfo.frameworkId !== this.lessonAssistantSelectedInfo.frameworkId) {
        this.getFrameworkDomains(this.lessonAssistantInfo.frameworkId)
      }
      const initialFrameworkId = this.lessonAssistantInfo.frameworkId
      const initialFrameworkName = this.lessonAssistantInfo.frameworkName
      const initialLessonContent = this.lessonAssistantInfo.lessonContent
      const initialActivityType = this.lessonAssistantInfo.activityType
      const initialCenterThemeName = this.lessonAssistantInfo.centerThemeName
      const initialAgeGroupValue = this.lessonAssistantInfo.ageGroup
      const domains = this.lessonAssistantInfo.domains
      const initialMeasureIds = this.lessonAssistantInfo.measureIds
      const state = this.lessonAssistantInfo.state
      const initialCity = this.lessonAssistantInfo.city
      const initialAdaptationContent = this.lessonAssistantInfo.adaptationContent
      const initialUseDomain = this.lessonAssistantInfo.useDomain
      var initialLearnerProfiles = this.lessonAssistantInfo.learnerProfiles || []
      var initialRubricsOptions = this.lessonAssistantInfo.rubricsOptions || []
      // 如果是 unit 课程，课程助手中没有校训值，从课程本身的校训中获取
      if (this.isFromUnitLesson && initialLearnerProfiles.length === 0 && this.lesson.learnerProfiles.length > 0) {
        initialLearnerProfiles = this.lesson.learnerProfiles.map(profile => {
          return {
            title: profile.rubricsName,
            description: profile.rubricsNameDesc,
            expectations: profile.rubricsExpectation,
            subStandards: profile.subRubrics ? profile.subRubrics.map(subRubric => {
              return {
                title: subRubric.rubricsName,
                description: subRubric.rubricsNameDesc,
                expectations: subRubric.rubricsExpectation
              }
            }) : []
          }
        })
        initialRubricsOptions = initialLearnerProfiles
      }
      this.lessonAssistantSelectedInfo = {
        lessonContent: initialLessonContent,
        adaptationContent: initialAdaptationContent,
        ageGroup: initialAgeGroupValue,
        activityType: initialActivityType,
        frameworkId: initialFrameworkId,
        frameworkName: initialFrameworkName,
        centerThemeName: initialCenterThemeName,
        domains: domains,
        measureIds: initialMeasureIds,
        haveSelectMeasureId: this.lessonAssistantInfo.haveSelectMeasureId,
        state: state,
        useLocation: this.lessonAssistantInfo.useLocation,
        country: this.lessonAssistantInfo.country,
        city: initialCity,
        frameworkState: this.lessonAssistantInfo.frameworkState,
        useDomain: initialUseDomain,
        learnerProfiles: initialLearnerProfiles,
        rubricsOptions: initialRubricsOptions,
        classroomType: this.showClassroomSetup ? this.lessonAssistantInfo.classroomType : 'IN_PERSON'
      }
      this.lessonAssistantInfo.classroomType = this.lessonAssistantSelectedInfo.classroomType
    },

    /**
     * 设置框架缓存
     */
    setFrameCache () {
      // 获取当前用户的缓存键
      var storageKey = 'lessonFrameworks' + this.currentUserId

      // 从缓存中获取已存在的框架列表
      var existingFrameworks = JSON.parse(sessionStorage.getItem(storageKey)) || []

      // 只保留前四个框架，删除其余的框架
      if (existingFrameworks.length > 4) {
        existingFrameworks = existingFrameworks.slice(0, 4)
      }
      // 检查新的框架ID是否已经存在于缓存中
      var frameworkIdExists = existingFrameworks.some(function (framework) {
        return framework.frameworkId === this.lessonAssistantSelectedInfo.frameworkId
      }, this)

      // 如果frameworkId已经存在，则不添加新的框架
      if (!frameworkIdExists) {
        // 创建新的框架对象，设置frameworkUrl为空
        var newFramework = {
          frameworkId: this.lessonAssistantSelectedInfo.frameworkId,
          frameworkName: this.lessonAssistantSelectedInfo.frameworkName,
          frameworkUrl: '',
          drdp: false
        }
        // 将新的框架对象追加到已有的框架列表中
        existingFrameworks.push(newFramework)
        this.$store.commit('curriculum/SET_REFRESH_EXT_FRAMEWORK', true)

        // 将更新后的框架列表保存到缓存中
        sessionStorage.setItem(storageKey, JSON.stringify(existingFrameworks))
      }
    },

    /**
     * 解析课程数据为 map
     */
    parseLessonContentData (lessonContent) {
      let keyMapping = [
        { key: 'name', name: 'Lesson Title' },
        { key: 'ageGroup', name: 'Age Group' },
        { key: 'prepareTime', name: 'Preparation Time' },
        { key: 'activityTime', name: 'Activity Duration' },
        { key: 'measuresString', name: 'Measures' },
        { key: 'objectives', name: ['Objectives', 'Objective'] },
        { key: 'materials', name: 'Materials' },
        {
          key: 'keyVocabularyWords',
          name: ['Key Vocabulary Words', 'Key Vocabulary Words & Child-Friendly Definitions', '- Key Vocabulary Words:']
        },
        { key: 'implementationSteps', name: ['Implementation Steps', 'a. Intro to Centers'] }
      ]
      if (equalsIgnoreCase(this.lessonAssistantSelectedInfo.activityType, 'CENTER') || equalsIgnoreCase(this.lessonAssistantSelectedInfo.activityType, 'STATION')) {
        keyMapping = [
          { key: 'name', name: 'Lesson Title' },
          { key: 'ageGroup', name: 'Age Group' },
          { key: 'prepareTime', name: ['Preparation Time', 'Activity Preparation Time'] },
          { key: 'activityTime', name: 'Activity Duration' },
          { key: 'measuresString', name: 'Measures' },
          { key: 'objectives', name: ['Objectives', 'Objective'] },
          { key: 'materials', name: 'Materials' },
          { key: 'implementationSteps', name: ['Implementation Steps', 'a. Intro to Centers'] }
        ]
      }
      // 解析数据
      return parseStreamData(lessonContent, keyMapping, 1)[0]
    },

    /**
     * 课程生成完毕
     */
    generaterdLessoncomplete () {
      // 检查所有生成任务是否完成：封面生成、课程步骤二、课程模板生成、Google Slide 数据生成、课程幻灯片生成
      if (!this.lessonCoverLoading && this.lessonStepTwoGenerated && !this.generateLessonTemplateLoading && !this.generateGoogleSlideDataLoading && !this.generateLessonSlidesStreamLoading) {
        // 关闭全部的 loading
        this.closeAllLoading()
        // 已经生成过课程了
        this.generatedLesson = true
        // 当课程生成完毕的时候进行回调
        this.$emit('generaterdLessoncomplete', true)
        this.$emit('getPromptUsageRecordIds', this.promptUsageRecordIds)
        this.$bus.$emit('showEduProtocolsTemplateCustomGuide', this.lesson.id)
        // 提示生成完成
        // 只有在添加课程页面才关闭所有消息提示
        if (this.$route.path.includes('/lessons/lesson-library/add-lesson')) {
          this.$message.closeAll()
        }
        this.$message.success(this.$t('loc.unitPlannerStep3GeneratedSuccessfully'))
      } else {
        // 优化为结构化的日志输出
        // console.log('课程生成完成状态检查:', {
        //   lessonCoverLoading: this.lessonCoverLoading,
        //   lessonStepTwoGenerated: this.lessonStepTwoGenerated,
        //   generateLessonTemplateLoading: this.generateLessonTemplateLoading,
        //   generateGoogleSlideDataLoading: this.generateGoogleSlideDataLoading, // 补全变量名
        //   generateLessonSlidesStreamLoading: this.generateLessonSlidesStreamLoading
        // })
      }
    },

    /**
     * 构建生成课程步骤第二步的请求体
     */
    buildGenerateLessonStepTwoRequest () {
      // 解析课程内容数据
      let parsedData = this.parseLessonContentData(this.convertedLessonContent)
      // 获取所有测评点
      let allMeasures = []
      // 获取所有已选择的测评点
      frameworkUtils.getMeasuresBottom(this.lesson.measures, allMeasures)
      // 获取所有已选择的测评点缩写
      const measures = allMeasures.map(child => child.abbreviation)
      let lesson = {
        id: this.lesson.id,
        name: parsedData.name,
        measures: measures,
        ageGroup: this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup),
        frameworkId: this.lessonAssistantSelectedInfo.frameworkId,
        objectives: parsedData.objectives,
        materials: parsedData.materials,
        prepareTime: parsedData.prepareTime,
        activityTime: parsedData.activityTime,
        keyVocabularyWords: parsedData.keyVocabularyWords,
        implementationSteps: parsedData.implementationSteps,
        classroomType: this.lessonAssistantSelectedInfo.classroomType
      }
      return { 'lesson': lesson }
    },

    /**
     * 生成差异化教学内容
     */
    generateUniversalDesign () {
      // 构建请求
      let params = this.buildGenerateLessonStepTwoRequest()
      // 消息回调
      let messageCallback = (message) => {
        // 更新数据
        let universalDesignForLearning = (this.lessonStepTwo.universalDesignForLearning || '') + message.data
        this.$set(this.lessonStepTwo, 'universalDesignForLearning', universalDesignForLearning)
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'universalDesignForLearning')
      }
      // 发送请求
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource($api.urls().generateUniversalDesignForLearningByLessonContentStream, {}, messageCallback, 'POST', params)
          .then((res) => {
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            // 生成结束
            resolve()
          })
          .catch(error => {
            // 调用请求失败处理
            this.requestErrorProcessing(error)
            reject(error)
          })
      })
    },

    /**
     * 生成文化响应式教学内容
     */
    async generateCulturallyResponsiveInstruction () {
      // 构建请求
      let params = this.buildGenerateLessonStepTwoRequest()
      // 消息回调
      let messageCallback = (message) => {
        let lessonClrAndSourcesNewClr = {}
        this.lessonStepTwo.culturallyResponsiveInstruction = (this.lessonStepTwo.culturallyResponsiveInstruction || '') + message.data
        // 移除无用的符号
        this.lessonStepTwo.culturallyResponsiveInstruction = removeUnexpectedCharacters(this.lessonStepTwo.culturallyResponsiveInstruction)
        lessonClrAndSourcesNewClr = { clr: this.lessonStepTwo.culturallyResponsiveInstruction, sources: [] }
        this.$set(this.lessonStepTwo, 'lessonClrAndSources', lessonClrAndSourcesNewClr)
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'culturallyResponsiveInstruction')
      }
      // 发送请求
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource($api.urls().generateCulturallyResponsiveInstructionByLessonContentStream, {}, messageCallback, 'POST', params)
          .then(async (res) => {
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            await new Promise((resolve) => {
              this.$emit('curriculumGenerateLessonSourceByCLR', resolve)
            })
            // 获取 CLR 资源

            // 发送停止标志
            let lessonClrAndSourcesNewClrOver = {
              clr: this.lessonStepTwo.culturallyResponsiveInstruction,
              sources: [],
              over: true
            }
            this.$set(this.lessonStepTwo, 'lessonClrAndSources', lessonClrAndSourcesNewClrOver)
            this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'culturallyResponsiveInstruction')
            resolve()
          })
          .catch(error => {
            // 调用请求失败处理
            this.requestErrorProcessing(error)
            reject(error)
          })
      })
    },

    /**
     * 生成校训
     */
    async generateLearnerProfile (itemRubrics) {
      let params = this.buildGenerateLessonStepTwoRequest()
      params.itemRubrics = JSON.stringify(itemRubrics)
      params.lessonId = this.lesson.id
      let learnerProfilesData = ''
      let messageCallback = (message) => {
        learnerProfilesData += message.data
        // 解析数据
        let parsedData = parseStreamData(learnerProfilesData, [
          { key: 'rubricsName', name: 'Attribute Name' },
          { key: 'rubricsValue', name: 'Instructions' },
        ])
        let tree = tools.handleLessonLearnerProfilesParsedData(parsedData, this.lessonAssistantSelectedInfo.rubricsOptions)
        this.$set(this.lessonStepTwo, 'learnerProfiles', tree)
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'learnerProfiles')
      }
      return new Promise((resolve, reject) => {
        createEventSource($api.urls().generateLessonLearnerProfileStream, {}, messageCallback, 'POST', params)
          .then((res) => {
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    /**
     * 生成课程典型行为
     */
    async generateTypicalBehaviors (isFrameworkMapped) {
      this.lessonStepTwo.typicalBehaviorsData = ''
      // 先重置成空数据
      let emptyBehaviors = []
      if (this.lessonAssistantSelectedInfo.measureIds) {
        for (let i = 0; i < this.lessonAssistantSelectedInfo.measureIds.length; i++) {
          emptyBehaviors.push({})
        }
      }
      if (isFrameworkMapped) {
        let original = (this.lessonStepTwo.typicalBehaviors && this.lessonStepTwo.typicalBehaviors.filter(item => !item.mapped)) || []
        let all = [...original, ...emptyBehaviors]
        this.$set(this.lessonStepTwo, 'typicalBehaviors', all)
      } else {
        let original = (this.lessonStepTwo.typicalBehaviors && this.lessonStepTwo.typicalBehaviors.filter(item => item.mapped)) || []
        let all = [...original, ...emptyBehaviors]
        this.$set(this.lessonStepTwo, 'typicalBehaviors', all)
      }
      // 构建请求
      let params = this.buildGenerateLessonStepTwoRequest()
      if (isFrameworkMapped) {
        params.frameworkMapped = true
      }
      // 消息回调
      let messageCallback = (message) => {
        // 更新数据
        let typicalBehaviorsData = (this.lessonStepTwo.typicalBehaviorsData || '') + message.data
        this.$set(this.lessonStepTwo, 'typicalBehaviorsData', typicalBehaviorsData)
        // 解析数据
        let parsedData = parseStreamData(typicalBehaviorsData, [
          { key: 'measureAbbreviation', name: 'Measure Abbreviation' },
          { key: 'behaviors', name: 'Typical Behaviors' }
        ])
        for (let i = 0; i < parsedData.length; i++) {
          if (isFrameworkMapped) {
            parsedData[i].mapped = true
          }
        }
        // 更新数据
        if (isFrameworkMapped) {
          let original = (this.lessonStepTwo.typicalBehaviors && this.lessonStepTwo.typicalBehaviors.filter(item => !item.mapped)) || []
          let all = [...original, ...parsedData]
          all = all.filter(item => Object.keys(item).length > 0)
          this.$set(this.lessonStepTwo, 'typicalBehaviors', all)
        } else {
          let original = (this.lessonStepTwo.typicalBehaviors && this.lessonStepTwo.typicalBehaviors.filter(item => item.mapped)) || []
          let all = [...original, ...parsedData]
          all = all.filter(item => Object.keys(item).length > 0)
          this.$set(this.lessonStepTwo, 'typicalBehaviors', all)
        }
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'typicalBehaviors')
      }
      // 发送请求
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource($api.urls().generateTypicalBehaviorsByLessonContentStream, {}, messageCallback, 'POST', params)
          .then((res) => {
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            resolve()
          })
          .catch(error => {
            if (error.message && error.message == '"NO_DATA"') {
              resolve()
            }
            // 调用请求失败处理
            this.requestErrorProcessing(error)
            reject(error)
          })
      })
    },

    /**
     * 生成标准教学指导
     */
    async generateTeachingTips () {
      // 先重置成空数据
      let emptyTeachingTips = []
      if (this.lessonAssistantSelectedInfo.measureIds) {
        for (let i = 0; i < this.lessonAssistantSelectedInfo.measureIds.length; i++) {
          emptyTeachingTips.push({})
        }
      }
      this.$set(this.lessonStepTwo, 'teachingTips', emptyTeachingTips)
      // 构建请求
      let params = this.buildGenerateLessonStepTwoRequest()
      // 消息回调
      let messageCallback = (message) => {
        // 更新数据
        let teachingTipsData = (this.lessonStepTwo.teachingTipsData || '') + message.data
        this.$set(this.lessonStepTwo, 'teachingTipsData', teachingTipsData)
        // 解析数据
        let parsedData = parseStreamData(teachingTipsData, [
          { key: 'measureAbbreviation', name: 'Measure' },
          { key: 'teachingTips', name: 'Instructions' }
        ])
        // 更新数据
        if (this.lessonStepTwo.teachingTips && this.lessonStepTwo.teachingTips.length > 0 && parsedData && parsedData.length > 0) {
          for (let i = 0; i < parsedData.length; i++) {
            this.$set(this.lessonStepTwo.teachingTips, i, parsedData[i])
          }
        } else {
          this.$set(this.lessonStepTwo, 'teachingTips', parsedData)
        }
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'teachingTips')
      }
      // 发送请求
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource($api.urls().generateTeachingTipsByLessonContentStream, {}, messageCallback, 'POST', params)
          .then((res) => {
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              this.promptUsageRecordIds.push(res.promptUsageRecordId)
            }
            resolve()
          })
          .catch(error => {
            // 调用请求失败处理
            this.requestErrorProcessing(error)
            reject(error)
          })
      })
    },

    // 生成家庭活动
    async generateCurriculumHomeActivity () {
      // 高年级数据
      let ageGroupValue = ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12']
      if (ageGroupValue.includes(this.lessonAssistantSelectedInfo.ageGroup)) {
        return
      }
      let params = this.buildGenerateLessonStepTwoRequest()
      // 消息回调`
      let homeActivityData = ''
      let tempActivityData = ''

      let messageCallback = (message) => {
        // 更新数据
        homeActivityData += message.data
        // 移除无用的符号
        homeActivityData = removeUnexpectedCharacters(homeActivityData)
        // 为 tempActivityData 赋值
        tempActivityData = homeActivityData
        // 解析 homeActivity 数据
        let parseMersures = parseStreamData(homeActivityData, [
          { key: 'activity ', name: 'Activity' },
          { key: 'measuresString', name: 'Measures' },
          { key: 'activityDescription', name: 'Activity Description' }
        ])
        if (parseMersures && parseMersures.length > 0) {
          // 循环解析出来的数据
          parseMersures.forEach((item, index) => {
            // 如果 measureString 存在
            // 解析测评点
            if (item.measuresString) {
              let measures = item.measuresString.split(';')
              // 只有一个测评点时，尝试用逗号分隔
              if (measures && measures.length === 1) {
                measures = item.measuresString.split(',')
              }
              // 去除空格
              measures = measures.map(measure => measure.trim())
                .map(measure => {
                  // 如果 convertMappingAbbrToDomainAbbrMap 存在并且长度大于 0，则使用 convertMappingAbbrToDomainAbbrMap 中的值
                  if (this.convertMappingAbbrToDomainAbbrMap && this.convertMappingAbbrToDomainAbbrMap.size > 0) {
                    return this.convertMappingAbbrToDomainAbbrMap.get(measure) || measure
                  }
                  return measure
                })
              // 替换掉 tempActivityData 中的测评点
              tempActivityData = tempActivityData.replace(item.measuresString, measures.join('; '))
            }
          })
        }
        this.$set(this.lessonStepTwo, 'homeActivity', tempActivityData)
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'homeActivity')
      }
      return new Promise((resolve, reject) => {
        // 生成单元概览
        createEventSource($api.urls().generateCurriculumHomeActivity, {},messageCallback,'POST',params)
          .then(() => {
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 生成 quiz
    async generateQuiz () {
      // 获取课程数据
      let params = this.buildGenerateLessonStepTwoRequest()
      params.useBloomLevel = this.bloomQuizSetting
      let quizData = ''
      // 消息回调
      let messageCallback = (message) => {
        quizData += message.data
        // 加粗标题、替换换行符
        // quizData = quizData.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/\n/g, '<br>')
        let parsedQuestions = parseStreamData(quizData, [
          { key: 'type', name: 'Question Type' },
          { key: 'level', name: ['Bloom\'s Taxonomy', 'DOK Level'] },
          { key: 'measureAbbreviation', name: ['Measure Abbreviation', 'Measure'] },
          { key: 'question', name: 'Question Content' },
          { key: 'answer', name: ['Correct Answer', 'Example Answer', 'Expected Answer', 'Answer Guide', 'Correct Answers', 'Example Response', 'Correct Order'] }
        ])
        if (parsedQuestions && parsedQuestions[0] && ((parsedQuestions[0].question && parsedQuestions[0].question.trim() !== '') || (parsedQuestions[0].answer && parsedQuestions[0].answer.trim() !== ''))) {
          parsedQuestions = parsedQuestions.map(parsedQuestion => {
            // 匹配测评点缩写
            if (parsedQuestion.measureAbbreviation && this.convertMappingAbbrToDomainAbbrMap && this.convertMappingAbbrToDomainAbbrMap.size > 0) {
              parsedQuestion.measureAbbreviation = this.convertMappingAbbrToDomainAbbrMap.get(parsedQuestion.measureAbbreviation) || parsedQuestion.measureAbbreviation
            }
            // 去除空格
            if (parsedQuestion.level) {
              parsedQuestion.level = parsedQuestion.level.trim().replace(' ', '')
            }
            return parsedQuestion
          })
          this.$set(this.lessonStepTwo, 'questions', parsedQuestions)
        }
        // 只更新 questions 字段
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'questions')
      }
      // 发送请求
      return new Promise((resolve, reject) => {
        createEventSource($api.urls().generateLessonQuiz, {}, messageCallback, 'POST', params)
          .then(() => {
            // 设置 quiz 中的题目序号和是否显示重新生成按钮的标记数组,题目序号从一开始
            let currentSortIndex = 1
            this.lessonStepTwo && this.lessonStepTwo.questions && (this.lessonStepTwo.questions = this.lessonStepTwo.questions.map(question => {
              this.$set(question, 'sortIndex', currentSortIndex++)
              return question
            }))
            // console.log(this.lessonStepTwo)
            // console.log(this.lessonStepTwo.questions)
            this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'questions')
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    /**
     * 生成课程图片
     */
    generateLessonCover () {
      // 已经有课程封面就不再生成
      if (this.lessonStepTwo.lessonCover) {
        // 更新封面数据
        this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'lessonCover')
        this.updateLessonCoverLoading(false)
        // 课程生成完毕
        this.$nextTick(() => {
          this.generaterdLessoncomplete()
        })
        return
      }
      let params = this.buildGenerateLessonStepTwoRequest()
      Lesson2.extractAndSearchLessonCover(params)
        .then(res => {
          this.$set(this.lessonStepTwo, 'lessonCover', res)
          // 将图片的 Prompt record Id 保存
          this.promptUsageRecordIds.push(res.promptRecordId)
          // 更新封面数据
          this.$emit('updateLessonStepTwo', this.lessonStepTwo, 'lessonCover')
          // 暂停封面的 loading
          this.updateLessonCoverLoading(false)
          // 课程生成完毕
          this.$nextTick(() => {
            this.generaterdLessoncomplete()
          })
        })
        .catch(error => {
          // 课程封面加载失败只需关闭生成封面的 loading
          this.updateLessonCoverLoading(false)
          // 捕获异常并处理
          if (!error.message) {
            this.$message.error('Internal server error.')
          } else {
            this.$message.error(error.message)
          }
          // 课程生成完毕
          this.$nextTick(() => {
            this.generaterdLessoncomplete()
          })
        })
    },

    getAgeGroupName (ageGroupValue) {
      if (!ageGroupValue || !this.ageGroups) {
        return ''
      }
      let ageGroupName = ''
      this.ageGroups.forEach(ageGroup => {
        if (ageGroup.value && ageGroup.value.trim().toLowerCase() === ageGroupValue.trim().toLowerCase()) {
          ageGroupName = ageGroup.name
        }
      })
      return ageGroupName
    },

    // 获取年龄组的值
    getAgeGroupValue (ageGroupName) {
      if (!ageGroupName || !this.ageGroups) {
        return ''
      }
      let ageGroupValue = ''
      this.ageGroups.forEach(ageGroup => {
        if (ageGroup.value && ageGroup.name.trim().toLowerCase() === ageGroupName.trim().toLowerCase()) {
          ageGroupValue = ageGroup.value
        }
      })
      return ageGroupValue
    },

    /**
     * 全屏展示
     */
    fullScreen () {
      this.$emit('aiFullScreen')
    },

    /**
     * 进入ai助手
     */
    switchAiToolShow () {
      this.$emit('switchAiToolShow')
    },

    // /**
    //  * 点击举例中的确认按钮
    //  * 会自动覆盖文本框中内容
    //  */
    // samplesConfirm () {
    //     // 如果当前文字是被选择的，则进行赋值
    //     for (let i = 0; i < this.samplesContents.length; i++) {
    //         if (this.samplesContents[i].selected) {
    //             this.lessonAssistantInfo.lessonContent = this.samplesContents[i].text
    //         }
    //     }
    //     // console.log("展示的年龄段等信息: ", this.lessonAssistantInfo)
    // },

    // /**
    //  * 选中样例中的文字
    //  */
    // selectSampleContent (index) {
    //     for (let i = 0; i < this.samplesContents.length; i++) {
    //         this.samplesContents[i].selected = index === i
    //     }
    // },

    /**
     * 选择了文件上传模式
     */
    selectFileOption () {
      this.fileContentVisible = true
      this.textContentVisible = false
    },

    /**
     * 选择了文本输入模式
     */
    selectTextOption () {
      this.textContentVisible = true
      this.fileContentVisible = false
      // 点击文本输入框时的聚焦操作
      this.$nextTick(() => {
        this.$refs.textOptionInput.focus()
      })
    },

    /**
     * 文件上传的回调钩子
     */
    beforeUpload (file) {
      var fileName = file.name
      // 允许上传的文件类型
      var passTypes = ['.doc', '.docx', '.pdf', '.txt']
      // 是否通过标志
      var pass = false
      for (var passType of passTypes) {
        if (fileName.endsWith(passType)) {
          pass = true
          break
        }
      }
      // 文件类型不通过则不通过并提示
      if (!pass) {
        // this.$message.error('仅支持上传 .doc, .docx, .pdf, .txt的文件类型')
        return false
      }
      // 校验文件大小，目前先假定3MB
      var size = file.size
      if (size > 5 * 1024 * 1024) {
        // this.$message.error('文件过大')
        return false
      }
      this.curFile.name = fileName
      this.curFile.fileSize = this.convertSize(size)
    },

    convertSize (size) {
      var fileSize = '0B'
      if (size < 1024) {
        fileSize = size + 'B'
      } else if (size < 1024 * 1024) {
        fileSize = (size / 1024).toFixed(2) + 'KB'
      } else {
        fileSize = (size / 1024 / 1024).toFixed(2) + 'M'
      }
      return fileSize
    },

    /**
     * 上传文件请求
     */
    fileUpload (content) {
      this.selectFileOption()
      this.uploading = true
      // 进度条默认为0
      this.uploadProgress = 0
      let file = content.file
      let param = new FormData()
      // 通过append向form对象添加数据
      param.append('file', file)
      // FormData私有类对象，访问不到，可以通过get判断值是否传进去
      var that = this
      const configs = {
        headers: { 'Content-Type': 'multipart/form-data' },
        // 文件上传进度的回调钩子
        onUploadProgress: function (progressEvent) {
          // 处理上传进度的逻辑
          var progress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
          // console.log(that.uploadFileList)
          that.uploadProgress = progress
        }
      }
      this.$axios
        .post('/medias/fileUpload?privateFile=true', param, configs)
        .then((res) => {
          content.onSuccess(res)
        })
        .catch((error) => {
          content.onError(error)
        })
    },

    /**
     * 文件上传成功的回调函数
     */
    fileUploadOnSuccess (res, file, fileList) {
      this.curFile.url = res.public_url
      this.fileUploadSuccess = true
    },

    /**
     * 文件上传失败的回调函数
     */
    fileUploadOnError (err, file, fileList) {
      if (err) {
        this.$message.error(err.message)
      } else {
        // this.$message.error('网络异常，请重新尝试')
      }
      this.uploadFileList = []
      this.uploading = false
      this.curFile = {}
    },

    /**
     * 删除当前文件
     */
    deleteCurFile () {
      // 再次显示文件上传
      this.uploading = false
      this.uploadFileList = []
      this.fileUploadSuccess = false
      this.curFile = {}
    },

    /**
     * 获取文件类型，以显示对应的图标
     * @param fileName 文件名
     * @returns {*} 文件类型对应的图标地址
     */
    getFileType (fileName) {
      return fileUtil.getFileType(fileName)
    },

    /**
     * Callback after api.js is loaded.
     */
    gapiLoaded () {
      gapi.load('client:picker', this.initializePicker)
    },

    /**
     * Callback after the API client is loaded. Loads the
     * discovery doc to initialize the API.
     */
    initializePicker () {
      gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest')
      this.pickerInited = true
      this.maybeEnableButtons()
    },

    /**
     * Callback after Google Identity Services are loaded.
     */
    gisLoaded () {
      this.googleDriveTokenClient = google.accounts.oauth2.initTokenClient({
        client_id: this.CLIENT_ID,
        scope: this.scope,
        callback: ''
      })
      this.gisInited = true
      this.maybeEnableButtons()
    },

    /**
     * Enables user interaction after all libraries are loaded.
     */
    maybeEnableButtons () {
      if (this.pickerInited && this.gisInited) {
      }
    },

    /**
     *  点击 google drive 图标触发的操作
     *  1. 登录
     *  2. 选择文件
     *  3. 完成回调
     */
    handleGoogleDriveAuthClick () {
      this.googleDriveTokenClient.callback = async (response) => {
        if (response.error !== undefined) {
          throw (response)
        }
        this.googleDriveAccessToken = response.access_token
        // console.log(this.googleDriveAccessToken)
        // 构建文件选择器
        await this.createPicker()
      }

      if (this.googleDriveAccessToken === null) {
        // Prompt the user to select a Google Account and ask for consent to share their data
        // when establishing a new session.
        this.googleDriveTokenClient.requestAccessToken({ prompt: 'consent' })
      } else {
        // Skip display of account chooser and consent dialog for an existing session.
        this.googleDriveTokenClient.requestAccessToken({ prompt: '' })
      }
    },

    /**
     *  退出登录，清除 token
     */
    handleSignOutClick () {
      if (this.googleDriveAccessToken) {
        this.googleDriveAccessToken = null
        google.accounts.oauth2.revoke(this.googleDriveAccessToken)
      }
    },

    /**
     *  构建 google drive 文件选择器
     */
    createPicker () {
      // 设置可见的文件类型
      const view = new google.picker.View(google.picker.ViewId.DOCS)
      // view.setMimeTypes('image/png,image/jpeg,image/jpg');
      view.setMimeTypes('application/vnd.openxmlformats-officedocument.wordprocessingml.document,' +
        'application/msword,' +
        'application/pdf,' +
        'text/plain')
      // 构建文件选择器
      const picker = new google.picker.PickerBuilder()
        .enableFeature(google.picker.Feature.NAV_HIDDEN)
        .enableFeature(google.picker.Feature.MULTISELECT_ENABLED)
        .setDeveloperKey(this.API_KEY)
        // .setAppId(APP_ID)
        // 设置 token
        .setOAuthToken(this.googleDriveAccessToken)
        .addView(view)
        .addView(new google.picker.DocsUploadView())
        // 选择后的回调函数
        .setCallback(this.pickerCallback)
        .build()
      picker.setVisible(true)
    },

    /**
     * 选择文件后的回调函数
     * @param {object} data - 选择的文件的数据
     */
    async pickerCallback (data) {
      // 如果行为是选择，则执行操作
      if (data.action === google.picker.Action.PICKED) {
        this.selectFileOption()
        this.uploading = true
        // 进度条默认为0
        this.uploadProgress = 0
        var doc = data[google.picker.Response.DOCUMENTS][0]
        var url = doc[google.picker.Document.URL]
        this.curFile.name = doc.name
        this.curFile.fileSize = this.convertSize(doc.sizeBytes)
        this.curFile.url = doc.url
        this.uploadProgress = 100
        this.fileUploadSuccess = true
      }
    },

    /**
     * 切换选择的类型，文本 or 文件
     */
    switchType (tag, event) {
      if (tag.name === this.submodules[0]) {
        this.selectTextOption()
      } else if (tag.name === this.submodules[1]) {
        this.selectFileOption()
      }
    },

    /**
     * 更新课程封面 loading 的值
     */
    updateLessonCoverLoading (newValue) {
      this.$emit('updateLessonCoverLoading', newValue)
    },
    /**
     * 更新课程生成 loading 的值
     */
    updateConvertLessonLoading (newValue) {
      this.$emit('updateConvertLessonLoading', newValue)
    },
    /**
     * 更新是否开启 ai 测评点推荐的值
     */
    updateEnableAutomaticMeasure (newValue) {
      this.enableAutomaticMeasure = newValue !== null && newValue !== undefined ? newValue : true
    },
    // 处理 Tab 切换
    handleTabChange(tab) {
      // 保存当前标签页状态
      this.saveAssistantToolState(equalsIgnoreCase(tab.name, 'create') ? 'adapt' : 'create')
      // 更新当前标签页
      this.activeTab = tab.name
      this.showAdaptationIdeas = equalsIgnoreCase(this.activeTab, 'adapt')

      // 恢复对应标签页状态
      this.restoreAssistantToolStateByTabName(tab.name)
      // 重新验证表单
      this.$nextTick(() => {
        this.$refs.lessonAssistantFormRef && this.$refs.lessonAssistantFormRef.clearValidate()
        this.$refs.publicLessonAssistantInfo.$refs.fromRef && this.$refs.publicLessonAssistantInfo.$refs.fromRef.clearValidate()
      })
    },
    // 保存 AssistantTool 状态
    saveAssistantToolState(tabName) {
      // 如果 tabName 为空，则使用当前的 tabName
      if (!tabName) {
        tabName = this.activeTab
      }
      let assistantToolState = {
        lessonAssistantInfo: JSON.parse(JSON.stringify(this.lessonAssistantInfo)),
        // 保存测评点树形数据
        treeData: JSON.parse(JSON.stringify(this.$refs.publicLessonAssistantInfo.treeData)) || [],
        treeDataFrameworkId: this.$refs.publicLessonAssistantInfo.treeDataFrameworkId || null,
        activeTab: tabName || this.activeTab,
        subjectsDomainsTabsCache: JSON.parse(JSON.stringify(this.$refs.publicLessonAssistantInfo.subjectsDomainsTabsCache)) || {},
        currentSampleIndex: this.currentSampleIndex,
        exemplarsClickCount: this.exemplarsClickCount
      }
      if (equalsIgnoreCase(tabName, 'create')) {
        this.createAssistantToolState = assistantToolState
      } else if (equalsIgnoreCase(tabName, 'adapt')) {
        this.adaptAssistantToolState = assistantToolState
      }
      this.assistantToolState = assistantToolState
      return assistantToolState
    },
    restoreAssistantToolStateByTabName(tabName) {
      // 如果 tabName 为空，则使用当前的 tabName
      if (!tabName) {
        tabName = this.activeTab
      }
      if (equalsIgnoreCase(tabName, 'create')) {
        this.restoreAssistantToolState(this.createAssistantToolState)
      } else if (equalsIgnoreCase(tabName, 'adapt')) {
        this.restoreAssistantToolState(this.adaptAssistantToolState)
      }
    },
    // 恢复 AssistantTool 状态
    restoreAssistantToolState(assistantToolState) {
      // console.log("恢复 AssistantTool 状态")
      // 设置恢复中的状态
      this.restoreAssistantToolStateLoading = true
      if (!assistantToolState || !assistantToolState.lessonAssistantInfo) {
        this.currentSampleIndex = 0
        this.exemplarsClickCount = 0
        // 如果没有要恢复的 lessonAssistantInfo，则重新初始化
        this.initLocationInfo()
        // 初始化课程助手信息
        this.initLessonAssistantInfo()
        this.$nextTick(() => {
          this.$refs.publicLessonAssistantInfo.subjectsDomainsTabsCache = {}
          this.$refs.publicLessonAssistantInfo.setDefaultSelections(this.lessonAssistantInfo.frameworkState, this.lessonAssistantInfo.frameworkId)
        })
        this.restoreAssistantToolStateLoading = false
        return
      }
      if (assistantToolState.activeTab) {
        this.activeTab = assistantToolState.activeTab
        this.showAdaptationIdeas = equalsIgnoreCase(this.activeTab, 'adapt')
      }
      this.currentSampleIndex = assistantToolState.currentSampleIndex || 0
      this.exemplarsClickCount = assistantToolState.exemplarsClickCount || 0
      // 回填 subjectsDomainsTabsCache 数据
      this.$nextTick(() => {
        this.$refs.publicLessonAssistantInfo.subjectsDomainsTabsCache = assistantToolState.subjectsDomainsTabsCache || {}
      })
      // 回填 lessonAssistantInfo
      this.lessonAssistantInfo = assistantToolState.lessonAssistantInfo
      this.applyLessonAssistantInfo(assistantToolState.lessonAssistantInfo, assistantToolState.treeDataFrameworkId, assistantToolState.treeData)
      this.restoreAssistantToolStateLoading = false
    },
    /**
     * 自动应用下一个 Sample 内容
     */
    autoApplyNextSample (isShowToast = true) {
      // 获取当前标签页对应的样例数组
      const currentSamples = this.samplesContents[this.activeTab]
      
      // 检查是否是第一次点击当前样例
      const isFirstClick = !this.exemplarsClickCount || this.exemplarsClickCount === 0
      
      if (isFirstClick) {
        // 第一次点击：应用当前样例，显示"已经应用 1"
        this.exemplarsClickCount = 1
      } else {
        // 第二次点击：切换到下一个样例
        this.exemplarsClickCount++
        this.currentSampleIndex = (this.currentSampleIndex + 1) % currentSamples.length
      }
      
      // 获取当前样例
      const currentSample = currentSamples[this.currentSampleIndex]

      let sampleData = {
        lessonContent: currentSample.activityDescription,
        adaptationContent: currentSample.adaptationIdeas,
        frameworkId: currentSample.frameworkId,
        frameworkName: currentSample.frameworkName,
        frameworkState: currentSample.frameworkState,
        domains: currentSample.domains && currentSample.domains.map(item => item.id) || [],
        measureIds: currentSample.measures && currentSample.measures.map(item => item.id) || [],
        ageGroup: currentSample.grade,
        useDomain: !(currentSample.measures && currentSample.measures.length > 0),
        classroomType: 'IN_PERSON'
      }
      // 使用 applyLessonAssistantInfo 方法应用样例数据
      this.applyLessonAssistantInfo(sampleData)
      if (isShowToast) {
        // 显示 toast 提示
        this.$message({
          message: this.$t('loc.exemplarToastMessage', { 
            index: this.currentSampleIndex + 1, 
            total: currentSamples.length 
          }),
          type: 'success'
        })
      }
    },
    /**
     * 编辑位置信息
     */
    editLocation () {
      // 创建课程第一步的位置信息埋点
      if (this.$route.name === 'AddLesson' && !equalsIgnoreCase(this.source, 'editPromptPopover')) {
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_changeloca')
      }
      // 将当前位置信息赋值给 location 对象
      this.location = {
        country: this.lessonAssistantInfo.country || 'United States',
        state: this.lessonAssistantInfo.state || '',
        city: this.lessonAssistantInfo.city || ''
      }
      this.cityDialogVisible = true
    },
    /**
     * 确认位置信息
     */
    cityDialogConfirm () {
      this.lessonAssistantInfo.country = this.location.country
      this.lessonAssistantInfo.state = this.location.state
      this.lessonAssistantInfo.city = this.location.city
      this.cityDialogVisible = false
    },
    // 收集历史记录数据
    collectPromptHistoryData() {
      // 获取选中的领域和测评点数据
      let selectedDomains = this.$refs.publicLessonAssistantInfo.treeData && this.$refs.publicLessonAssistantInfo.treeData
        .filter(domain => this.lessonAssistantInfo.domains.includes(domain.id))
        .map(domain => ({ id: domain.id, abbreviation: domain.name }))
      let measures = []
      frameworkUtils.collectMeasures(measures, this.$refs.publicLessonAssistantInfo.treeData)
      let selectedMeasures = measures && measures
        .filter(measure => this.lessonAssistantInfo.measureIds.includes(measure.id))
        .map(measure => ({ id: measure.id, abbreviation: measure.abbreviation }))
      if (!this.showClassroomSetup) {
        this.lessonAssistantInfo.classroomType = 'IN_PERSON'
      }
      // 构建历史数据结构
      let historyData = {
        // 基本信息
        lessonId: this.lesson && this.lesson.id, // 课程 ID - 保留，重新生成时会有值
        activityDescription: this.lessonAssistantInfo.lessonContent, // 活动描述
        activityType: this.lessonAssistantInfo.activityType || 'LARGE_GROUP',
        // 框架相关
        frameworkId: this.lessonAssistantInfo.frameworkId, // 框架 ID
        frameworkName: this.lessonAssistantInfo.frameworkName, // 框架名称
        frameworkState: this.lessonAssistantInfo.frameworkState, // 框架名称
        // 年龄组和测评点
        grade: this.getAgeGroupName(this.lessonAssistantInfo.ageGroup), // 年级
        useDomain: this.lessonAssistantInfo.useDomain, // 是否使用领域
        domains: selectedDomains, // 领域
        measures: selectedMeasures, // 测评点
        // 地理位置
        country: this.lessonAssistantInfo.country, // 国家
        state: this.lessonAssistantInfo.state, // 州
        city: this.lessonAssistantInfo.city, // 城市
        useLocation: this.lessonAssistantInfo.useLocation, // 是否使用地理位置
        classroomType: this.lessonAssistantInfo.classroomType || 'IN_PERSON' // 课堂类型
      }
      if (equalsIgnoreCase(this.activeTab, 'adapt')) {
        historyData.adaptationIdeas = this.lessonAssistantInfo.adaptationContent
      }
      if (this.inWeeklyPlannDialog) {
        historyData.centerThemeName = this.lessonAssistantInfo.centerThemeName
      }
      return historyData
    },

    /**
     * 生成课程步骤的第二步：差异化教学内容、文化响应式教学内容、典型行为
     */
    async generateLessonStepTwo (generateLessonCover = true, isAdaptedLesson = false, copyLesson = null) {
      this.lessonStepTwoGenerated = false
      // 判断是否要生成封面
      if (generateLessonCover) {
        // 生成课程封面
        this.generateLessonCover()
      }
      // 保存草稿，保证后续生成的内容是根据第一步的内容继续生成的
      if (this.saveDraft && typeof this.saveDraft === 'function') {
        // 保存草稿
        await this.saveDraft(true)
      }
      // 生成课程幻灯片
      this.$emit('generateSlides')

      try {
        // 当活动类型为 CENTER 时 DLL 和 Family Resources 的内容不生成
        if (this.lessonAssistantSelectedInfo.activityType !== 'CENTER' && this.lessonAssistantSelectedInfo.activityType !== 'STATION') {
          this.$emit('scrollToActive', 'teachingTipsStandardTitleRef')
          // 构建并行任务数组
          const parallelTasks = []
          if ((this.adaptedModuleSwitch && this.adaptedModuleSwitch.teacherGuideFlag) || !this.adaptedModuleSwitch) {
            // 1. 生成标准教学指导
            parallelTasks.push(this.generateTeachingTips())
          }
          // 根据年龄段生成典型行为或者测验
          let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
          // 2. 根据年龄段生成典型行为或者测验 如果设置轻量导入改编时当前模块是否显示，且 teacherGuideFlag 为 false，则不生成典型行为
          if ((!k12Grades.includes(this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup))) && ((this.adaptedModuleSwitch && this.adaptedModuleSwitch.teacherGuideFlag) || !this.adaptedModuleSwitch)) {            // this.$emit('scrollToActive', 'domainsTableEditor')
            // 其他年级的生成典型行为
            if (frameworkUtils.isCAPTKLF(this.lessonAssistantSelectedInfo.frameworkId) ||
              frameworkUtils.isILEARN(this.lessonAssistantSelectedInfo.frameworkId) ||
              frameworkUtils.isMELS(this.lessonAssistantSelectedInfo.frameworkId)) {
              // 需要串行执行两个 generateTypicalBehaviors 方法
              parallelTasks.push(async () => {
                await this.generateTypicalBehaviors(true)
                await this.generateTypicalBehaviors()
              })
            } else {
              parallelTasks.push(this.generateTypicalBehaviors())
            }
          }
          if ((this.adaptedModuleSwitch && this.adaptedModuleSwitch.teacherGuideFlag) || !this.adaptedModuleSwitch) {
            // 3. 改编课程不再生成 UDL CLR
            if (!isAdaptedLesson) {
              // 生成差异化教学内容
              // this.$emit('scrollToActive', 'universalDesignLearning')
              parallelTasks.push(this.generateUniversalDesign())
              // 生成文化响应式教学内容
              // this.$emit('scrollToActive', 'culturallyLinguisticallyResponsive')
              parallelTasks.push(this.generateCulturallyResponsiveInstruction())
            } else {
              // 如果设置轻量导入改编时当前模块是否显示，且 teacherGuideFlag 为 false，则不生成 UDL 和 CLR 数据
              if (this.adaptedModuleSwitch && !this.adaptedModuleSwitch.teacherGuideFlag) {
                return
              }
              // 异步生成 UDL 和 CLR 数据
              parallelTasks.push(new Promise((resolve, reject) => {
                this.$emit('syncGenerateUniversalDesignAndCLRData', {
                  onSuccess: resolve,
                  onError: reject
                }, copyLesson)
              }))
            }
          }
         

          // 4. k-12年级的生成测验
          if (k12Grades.includes(this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup)) || tools.isExtendedAgeGroup(this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup))) {
             // 如果设置轻量导入改编时当前模块是否显示，且 standardsAssessmentFlag 为 false，则不生成 quiz
             if ((this.adaptedModuleSwitch && this.adaptedModuleSwitch.standardsAssessmentFlag) || !this.adaptedModuleSwitch) {
               // this.$emit('scrollToActive', 'lessonQuiz')
              parallelTasks.push(this.generateQuiz())
            }
          }

          // 5. 生成 homeActivity
          // this.$emit('scrollToActive', 'homeActivity')
          parallelTasks.push(this.generateCurriculumHomeActivity())

          // 6. 生成校训
          if (k12Grades.includes(this.getAgeGroupName(this.lessonAssistantSelectedInfo.ageGroup)) && this.lessonAssistantSelectedInfo.learnerProfiles && this.lessonAssistantSelectedInfo.learnerProfiles.length > 0) {
            if ((this.adaptedModuleSwitch && this.adaptedModuleSwitch.portraitOfGraduateFlag) || !this.adaptedModuleSwitch) {
              parallelTasks.push(this.generateLearnerProfile(this.lessonAssistantSelectedInfo.learnerProfiles))
          }
          }

          // 并行执行所有任务
          await Promise.all(parallelTasks.map(task => typeof task === 'function' ? task() : task))
        }
      } finally {
        this.lessonStepTwoGenerated = true
        // 课程生成完毕检查
        this.$nextTick(() => {
          this.generaterdLessoncomplete()
        })
      }
    },

    // 创建历史记录
    createPromptHistory() {
      // 收集历史数据
      let historyData = this.collectPromptHistoryData()
      // 根据类型获取对应的 storage key
      const storageKey = equalsIgnoreCase(this.activeTab, 'create') ?
        'CREATE_LESSON_PROMPT_HISTORY' : 'ADAPT_LESSON_PROMPT_HISTORY'

      // 获取原始 prompt 数据
      let originalData = JSON.parse(sessionStorage.getItem(storageKey + this.currentUserId))

      if (originalData) {
        // 定义需要比较的字段
        let fieldsToCompare = [
          'activityDescription',
          'adaptationIdeas',
          'activityType',
          'centerThemeName',
          'frameworkId',
          'frameworkName',
          'frameworkState',
          'domains',
          'grade',
          'classroomType'
        ]

        // 如果不使用自动分配测评点功能,则需要比较测评点信息
        if (!historyData.useDomain) {
          fieldsToCompare.push('measures')
        }

        // 创建仅包含需要比较字段的对象
        const compareOriginal = {}
        const compareHistory = {}

        fieldsToCompare.forEach(field => {
          if (originalData[field]) {
            compareOriginal[field] = originalData[field]
          }
          if (historyData[field]) {
            compareHistory[field] = historyData[field]
          }
        })
        // 如果没有变化,则不创建历史
        if (JSON.stringify(compareOriginal) === JSON.stringify(compareHistory)) {
          return
        }
      }
      // 保存历史记录
      if (historyData) {
        // 更新 sessionStorage
        sessionStorage.setItem(storageKey + this.currentUserId, JSON.stringify(historyData))
        if (equalsIgnoreCase(this.activeTab, 'create')) {
          this.$axios.post($api.urls().createCreateLessonPromptHistory, historyData)
        } else {
          this.$axios.post($api.urls().createAdaptLessonPromptHistory, historyData)
        }
      }
    },

    // 使用历史记录
    usePromptHistory(historyData) {
      if (this.$route.name === 'AddLesson' && equalsIgnoreCase(this.activeTab, 'create')) {
        // 创建课程点击使用历史记录曝光埋点
        this.$analytics.sendEvent('cg_lesson_plan_click_cre_use')
      } else if (this.$route.name === 'AddLesson' && equalsIgnoreCase(this.activeTab, 'adapt')) {
        // 改编课程的点击使用历史记录曝光埋点
        this.$analytics.sendEvent('cg_lesson_plan_click_adp_use')
      }
      // 将 historyData 处理成 lessonAssistantInfo
      let lessonAssistantInfo = {
        lessonContent: historyData.activityDescription,
        adaptationContent: historyData.adaptationIdeas,
        activityType: historyData.activityType,
        centerThemeName: historyData.centerThemeName,
        frameworkId: historyData.frameworkId,
        frameworkName: historyData.frameworkName,
        frameworkState: historyData.frameworkState,
        domains: historyData.domains && historyData.domains.map(item => item.id) || [],
        measureIds: historyData.measures && historyData.measures.map(item => item.id) || [],
        ageGroup: this.getAgeGroupValue(historyData.grade),
        useDomain: historyData.useDomain || !(historyData.measures && historyData.measures.length > 0),
        classroomType: historyData.classroomType || 'IN_PERSON'
      }
      this.applyLessonAssistantInfo(lessonAssistantInfo)
    },
    applyLessonAssistantInfo(lessonAssistantInfo, treeDataFrameworkId, treeData) {
      // console.log("应用课程助手信息 1")
      // 回填历史数据
      this.lessonAssistantInfo.lessonContent = lessonAssistantInfo.lessonContent || ''

      // 根据当前标签页设置 adaptationContent
      this.lessonAssistantInfo.adaptationContent = lessonAssistantInfo.adaptationContent || ''
      this.lessonAssistantInfo.classroomType = lessonAssistantInfo.classroomType || 'IN_PERSON'
      this.$nextTick(() => {
        if (treeDataFrameworkId && treeData && treeData.length > 0) {
          this.$refs.publicLessonAssistantInfo.treeDataFrameworkId = treeDataFrameworkId
          this.$refs.publicLessonAssistantInfo.treeData = treeData
        }
        // 将数据传递给 publicLessonAssistantInfo 组件
        this.$refs.publicLessonAssistantInfo.applyLessonAssistantInfo(lessonAssistantInfo)
      })
    }
  },
}
</script>

<style lang="less" scoped>
/deep/ .el-form-item__error {
  margin-top: 4px;
}

/deep/ .el-textarea .el-textarea__inner {
  cursor: text !important;
}

/deep/ .el-input__count {
  line-height: 20px !important;
  right: 23px !important;
  bottom: 2px !important;
}
// 州下拉框样式
.stateSelectStyle{
  display: flex;
  /deep/.el-input__inner {
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 溢出隐藏 */
    text-overflow: ellipsis; /* 使用省略号 */
  }
}
.select-ellipsis-style{
  /deep/.el-input__inner {
    text-overflow: ellipsis; /* 使用省略号 */
  }
}
.custom-button {
  color: grey; /* 默认颜色为灰色 */
}
.custom-button:hover {
  color: #10b3b7; /* 鼠标悬浮时的颜色 */
}
.custom-title {
  color: #303133;
  font-size: 16px;
  line-height: 1;
  font-weight: bold;
}
/deep/ .el-form-item {
  width: 100%;
  margin-bottom: 16px!important;
}

/deep/ .btn-area span{
  width: 100%!important;
}
.flex-justify-center {
  justify-content: center;
  margin-left: 0px;
}

.ai-btn, .ai-btn:hover, .ai-btn:focus {
  border-color: none !important;
  border: 0 !important;
}

.custom-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-label {
  justify-content: flex-start;
}

/deep/ .custom-form-item .el-form-item__label {
  width: 100%;
}
/deep/ .adapt-item .el-form-item__label{
  font-size: 14px !important;
}

/deep/ .el-form-item__label {
  font-weight: 600;
  font-size: 16px;
  color: #111c1c;
  line-height: 22px;
  margin-bottom: 0;
}

.flex-container {
  display: flex;
  justify-content: flex-end;
}

.left-icon-contain {
  display: flex;
  justify-content: center;
  align-items: center;
}

.take-back-icon {
  background-color: #DCDFEC;
  border-radius: 0 8px;
}

.el-icon-arrow-left-icon {
  font-size: 20px;
  width: 33px;
  height: 37px;
}

.el-icon-arrow-right-icon {
  font-size: 20px;
  width: 25px;
  height: 25px;
}

.el-icon-document-add-icon {
  font-size: 20px;
}

.el-icon-full-screen-icon {
  font-size: 25px;
  width: 30px;
  align-items: center;
}

.enter-icon-div {
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  background-color: #DCDFEC;
}

.enter-icon {
  padding: 0 0 0 3px;
  background: #2E61D8;
  color: transparent;
  -webkit-background-clip: text;
}

.flex-element-center {
  display: flex;
  justify-content: center !important;
  align-items: center !important;
}

.ai-assistant-text-style {
  font-size: 18px;
  line-height: 19px;
  color: transparent;
  -webkit-background-clip: text;
  background-image: linear-gradient(269.9deg, #2D9CDB 4.02%, #8B63FF 55.18%);
  font-weight: 600;
  letter-spacing: 0;
  text-align: left;
}

.enter-ai-style {
  box-shadow: 0 4px 16px 0 #0000001A;
  border-radius: 8px;
  //background-image: url("../../../../../assets/img/lesson2/assistant/aiAssistantEnter.png");
  background-image: url("~@/assets/img/lesson2/assistant/aiAssistantEnter.png");
  background-size: cover;
  background-repeat: no-repeat;
}

.file-upload-div {
  border-radius: 8px;
  border: 1.6px dashed var(--10-b-3-b-7, #10B3B7);
  background: rgba(221, 242, 243, 0.40);
}

.drive-icon {
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--dcdfe-6, #DCDFE6);
  background: #FFF;
}

.drive-icon-size {
  width: 22px;
  height: 18px;
}

/deep/ .el-form-item__content {
  line-height: unset !important;
}

/deep/ .ai-assistant-tab {

  border-radius: 4px;
  background: #ffffff;
  border: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;

  .el-tabs__header {
    border: none !important;
    margin: 0 !important;
  }
  .el-tabs__nav {
    padding: 6px !important;
    border-radius: 4px !important;
    gap: 6px;
    display: inline-flex;
    border: none !important;
    width: 100% !important;
    background: var(--2-ebeef-5, #EBEEF5);
    justify-content: center;
    align-items: center;
  }
  .el-tabs__item.is-active {
    background: #ffffff !important;
    color: var(--10-b-3-b-7, #10B3B7);
    font-weight: 600;
    border-radius: 4px;
  }
  .el-tabs__item {
    color: var(--111-c-1-c, #111C1C);
    font-weight: 400;
    font-size: 14px;
    height: 32px !important;
    line-height: 22px !important;
    padding: 0 12px !important;
    border-width: 0 !important;
    width: 50% !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-tabs__content {
    padding: 15px 0 !important;
  }
}

.unselected-content /deep/ .el-textarea .el-textarea__inner {
  border-color: transparent !important;
  /* 隐藏边框 */
  background: linear-gradient(0deg, #FAFAFA, #FAFAFA) !important;
  resize: none;
  /* 禁止拖动 */
}

/deep/ .el-textarea .el-textarea__inner {
  white-space: normal;
  /* 允许文本换行 */
  word-break: break-word;
  /* 在需要时换行，但保持单词的完整性 */
  cursor: pointer;
}

/deep/ .generate-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

/deep/ .el-popper.assistant-toolbar-guide-color-text {
  left: unset !important;
  right: 10px !important;
}

/deep/.el-form--label-top .el-form-item__label {
  padding: 0 0 4px 0;
}

/deep/.el-input__inner {
  height: 36px;
}

/deep/.el-input__suffix {
  height: unset !important;
  top: -2px;
}

.form-area {
  overflow-y: auto; // 垂直方向可滚动
  overflow-x: hidden; // 水平方向隐藏

  .el-form {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); // 使用贝塞尔曲线让过渡更平滑
    opacity: 1; // 默认不透明
  }

  .el-form-item {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); // 使用贝塞尔曲线让过渡更平滑
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1); // 使用更平滑的动画
    will-change: transform, opacity; // 优化动画性能
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0); // 使用 translate3d 开启 GPU 加速
    visibility: hidden; // 初始隐藏
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    visibility: visible; // 显示元素
  }
}
.location {
  display: flex;
  align-items: center;
  gap: 16px;

  .title-font-14 {
    white-space: nowrap; /* 确保文字一行展示 */
  }

  button {
    background: none;
    border: none;
    color: #10B3B7;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    span {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }

  .el-icon-map-location {
    font-size: 20px; /* 设置图标的大小 */
  }
}
.required-star {
  color: #f56c6c;
  margin-right: 2px;
}

/deep/ .location-from-dialog {
  .el-select {
    width: 100% !important;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-dialog__body {
    padding: 12px 20px !important;
  }
}

@media (max-width: 768px) {
  .assistant-form {
    .el-form-item {
      width: 100%;
      margin-bottom: 12px;
    }
    .el-input {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
    .el-dialog {
      width: 100% !important;
    }
    .display-flex {
      flex-direction: column;
      align-items: flex-start;
    }
    .gap-10 {
      gap: 5px;
    }
    .el-button {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}

.exemplar-btn.hover-active {
  background-color: var(--color-primary) !important;
  color: #fff !important;
}

.exemplar-btn:hover {
  background-color: var(--color-primary) !important;
  color: #fff !important;
}

.exemplar-btn.hover-active .lg-color-primary,
.exemplar-btn.hover-active span,
.exemplar-btn:hover .lg-color-primary,
.exemplar-btn:hover span {
  color: #fff !important;
}

/* Classroom Setup 样式 */
.classroom-setup-section {
  margin-bottom: 16px;

  .classroom-setup-content {
    background: #F5F6F8;
    border-radius: 4px;
    padding: 6px 12px;

    .teaching-mode-options {
      display: flex;
      gap: 8px;
      align-items: center;
      flex-direction: row;
      flex-wrap: wrap;

      .teaching-mode-radio {
        margin: 0;
        line-height: 24px;

        /deep/ .el-radio__input {
          .el-radio__inner {
            width: 16px;
            height: 16px;
            border: 2px solid #DCDFE6;
            
            &::after {
              width: 6px;
              height: 6px;
            }
          }
          &.is-checked .el-radio__inner {
            border-color: #10B3B7;
            background-color: #10B3B7;
          }
        }
        
        &.is-checked /deep/ .el-radio__label {
          color: #10b3b7!important;
        }
      }
    }
  }
}
</style>

<style lang="less">

@media (max-width: 768px) {
  .assistant-exemplars-tooltip {
    max-width: 180px !important;
  }
}

.gap-6 {
  gap: 6px;
}
/* assistant tool bar Note 引导弹框样式 */
.el-popper.assistant-toolbar-guide-color-text {
  background: var(--color-ai-assistant);
  color: #FFFFFF;
  padding: 24px;
  border: none;
  &.el-popper[x-placement^=left] .popper__arrow::after{
    border-left-color: var(--color-ai-assistant);
  }
  &.el-popper[x-placement^=right] .popper__arrow::after{
    border-right-color: var(--color-ai-assistant);
  }
  &.el-popper[x-placement^=bottom] .popper__arrow::after{
    border-bottom-color: var(--color-ai-assistant);
  }
  &.el-popper[x-placement^=top] .popper__arrow::after{
    border-top-color: var(--color-ai-assistant);
  }
  p {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
  }
  ul {
    padding-left: 24px;
    margin-bottom: 24px;
    li {
      list-style: disc;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
    }
  }
  .el-button {
    color: var(--color-ai-assistant);
    background: var(--color-white);
    border-color: var(--color-ai-assistant);
  }
  .btn-back {
    border-color: var(--color-border);
    color: var(--color-white);
    background: var(--color-ai-assistant);
  }
  .el-link{
    color: var(--color-white);
    display: block;
  }
  .el-link:hover {
    color: var(--color-white);
  }
  .el-button:hover {
    color: var(--color-ai-assistant);
    background: var(--color-white);
    border-color: var(--color-ai-assistant);
  }
}
</style>
