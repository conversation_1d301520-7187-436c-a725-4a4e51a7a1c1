<template>
  <div style="position: relative">
    <!--机构课程列表-->
    <lesson-list :params="params" :loader="getAgencyLessons" ref="lessonList" :show-header-even-empty="isAdmin()">
      <template slot="header" slot-scope="{total}">
        <div style="display: flex;justify-content: space-between;align-items: center;">
          <!-- 过滤器 -->
          <!-- <filter-controller-button v-if="!isCurriculumPlugin" :show-filter="showFilter" :params="params"
            @toggle-filter="$emit('toggle-filter')" /> -->
          <div style="display: flex;align-items: center;gap: 24px;">
            <!-- 课程总数 -->
            <div>
              <span style="font-weight: bold"><span class="font-weight-400">{{ $t('loc.totalEntries') }}: </span>{{
                total }}</span>
            </div>
            <div>
              <slot name="header-right" />
            </div>
          </div>
        </div>
      </template>
      <template slot-scope="{lesson}">
        <lesson-card :lesson="lesson" @click.native="lessonClickedHandler(lesson)" />
      </template>
      <template slot="footer" slot-scope="{empty}">
        <lesson-empty v-if="empty" :tip="$t('loc.lessons2NoResult')" />
      </template>
    </lesson-list>

    <!--模态窗口展示详情页-->
    <lesson-detail-dialog new-tab-to="AgencyLessonDetail" :show.sync="showDetail" :lesson-id="lessonId" :dsiable-esc-close="showHistoryDrawer">
      <!--详情页-->
      <lesson-detail ref="lessonDetail" @mountedAfter="lessonDetailMountedAfter" :isFromLibrary="isFromLibrary"
        :lessonId="lessonId" :isDialog="true">
        <template slot="header-left" slot-scope="{lesson}">
          <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount" />
          <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"
            @update:count="value=>handleUpdate(lesson,'likeCount',value)" />
          <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"
            @update:count="value=>handleUpdate(lesson,'favoriteCount',value)" /> -->
        </template>
        <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
          <!--删除按钮-->
          <el-tooltip :content="$t('loc.lessons2DeleteLesson')"
            placement="top" effect="dark" v-if="isAdmin() && isOwnerLesson(lesson)">
            <i class="el-icon-delete" style="cursor: pointer" @click="lessonDeleteClickHandler(lesson)"></i>
          </el-tooltip>

          <lesson-promote v-if="lesson.type !== 'AGENCY' && !isUseAdaptedUDLAndCLR" :lesson-id="lesson.id"
            :lesson-author-id="lesson.createUserId" @reloadLessons="reloadLessons" />
          <!-- 历史版本按钮 -->
          <el-button v-if="lesson.status !== 'DRAFT' && lessonId && isAdmin() && isOwnerLesson(lesson)" style="height: 32px;padding: 3px 6px!important;"
            type="primary" size="medium" @click="openHistory" plain>
            <div class="btn-center">
              <i style="font-size: 16px;" class="lg-icon lg-icon-history lg-color-primary"></i>
              <span>{{ $t('loc.lessonVersionHistory') }}</span>
            </div>
          </el-button>
          <lesson-replicate :lesson-id="lesson.id" style="margin-left: 0;" />
          <el-button icon="el-icon-edit" size="small" plain class="remove-margin-l-0" type="primary"
            @click="lessonEditClickHandler(lesson)" v-if="isAdmin() && isOwnerLesson(lesson)">
            {{ $t('loc.edit') }}
          </el-button>
          <lesson-template-select-modal v-if="showLessonTemplate(lesson.ages, lesson.activityType)"
            :lessonAges="lesson.ages" style="width: auto;" buttonSize="small" :inDialog="true" :showGuidePopover="true"
            v-model="lesson.templateType" :lessonId="lesson.id" :isMyLesson="isOwnerLesson(lesson)"
            redirectRoute="EditLesson" />
          <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId"
            :lesson-framework-id="lesson.framework.id" :lesson="lesson"/>
        </template>
        <!--课程详情弹窗提示-->
        <template slot="detail-tips" slot-scope="{lesson}">
          <lesson-detail-tips v-if="(isAdmin() && lesson.type !== 'AGENCY') || !isAdmin()" />
        </template>
      </lesson-detail>
      <!-- 课程历史抽屉 -->
      <lesson-history-drawer ref="historyDrawer" :visible.sync="showHistoryDrawer" :lesson-id="lessonId"
        :is-from-library="isFromLibrary" :key="lessonId" />
    </lesson-detail-dialog>
  </div>
</template>
<script>
import tools from '@/utils/tools'
import LessonCardOperationIcon from '@/views/modules/lesson2/lessonLibrary/components/LessonCardOperationIcon'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import { mapState } from 'vuex'
import Api from '../../../../../api/lessons2/index'
import LessonList from '../../component/lessonList'
// import FilterControllerButton from '../components/FilterControllerButton.vue'
import LessonCard from '../components/LessonCard'
import LessonDetail from '../components/LessonDetail'
import LessonDetailDialog from '../components/LessonDetailDialog'
import LessonDetailTips from '../components/LessonDetailTips'
import LessonDownload from '../components/LessonDownload'
import LessonEmpty from '../components/LessonEmpty'
// import LessonFavorite from '../components/LessonFavorite'
// import LessonLike from '../components/LessonLike'
import LessonPromote from '../components/LessonPromote'
// import LessonReadCount from '../components/LessonReadCount'
import LessonReplicate from '../components/LessonReplicate'
import NewTab from '../components/NewTab'
import RecycleLesson from '../Recycle/RecycleLesson'
import LessonHistoryDrawer from '@/views/modules/lesson2/lessonLibrary/components/LessonHistoryDrawer.vue'

export default {
  name: 'AgencyLessonList',
  components: {
    LessonCardOperationIcon,
    LessonCard,
    LessonDetailDialog,
    LessonDetailTips,
    RecycleLesson,
    LessonReplicate,
    LessonDownload,
    // LessonFavorite,
    // LessonLike,
    // LessonReadCount,
    NewTab,
    LessonDetail,
    LessonList,
    LessonPromote,
    LessonEmpty,
    LessonTemplateSelectModal,
    // FilterControllerButton,
    LessonHistoryDrawer
  },
  props: [
    'params',
    'showFilter'
  ],
  data () {
    return {
      showDetail: false, // 详情页弹窗的开关
      lessonId: null, // 详情课程Id
      reset: 0,
      isDelete: false,
      cardLesson: null,
      isFromLibrary: true,
      isUseAdaptedUDLAndCLR: true, // 是否已经启用了 Adapt UDL and CLR
      showHistoryDrawer: false // 历史版本抽屉的开关
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
    }),
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    }
  },
  watch: {
    showDetail () {
      if (!this.showDetail && this.isDelete) {
        this.$refs.lessonList.reload()
        this.isDelete = false
      }
    },
  },
  methods: {
    /**
     * 课程内容渲染后来自子组件的回调，用以判断是否使用了 Adapt UDL and CLR
     */
    lessonDetailMountedAfter () {
      if (this.$refs.lessonDetail) {
        // 判断是否启用了 UDL 和 CLR
        this.isUseAdaptedUDLAndCLR = this.$refs.lessonDetail.isUseAdaptedUDLAndCLR;
      }
    },
    /**
     * 刷新课程列表
     */
    reloadLessons () {
      this.$refs.lessonList.reload()
    },
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    // 判断是否是自己的课程
    isOwnerLesson (lesson) {
      return (lesson.createUserId === this.currentUser.user_id)
    },
    // 查询所有机构课程
    getAgencyLessons (param) {
      return Api.getAgencyLessons(param)
    },
    getMyLessonDraft (param) {
      return Api.getMyLessons({ ...param, status: 'DRAFT' })
    },
    // 打开课程详情的窗口
    lessonClickedHandler (lesson) {
      window.lessonVideo && window.lessonVideo.pause()
      this.lessonId = lesson.id
      this.cardLesson = lesson
      this.showDetail = true
    },
    // 编辑课程
    lessonEditClickHandler (lesson) {
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: lesson.id,
          type: 'Draft'
        }
      })
    },
    // 删除课程
    lessonDeleteClickHandler (lesson) {
      const h = this.$createElement
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.lessons2DeleteTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        beforeClose: (action, instance, done) => {
          if (instance.confirmButtonLoading) {
            return
          }
          if (action === 'confirm') {
            done()
            Api.deleteLesson(lesson.id).then(() => {
              this.showDetail = false
              if (this.submodule === this.submodules[1]) {
                this.$refs.manageDraftLessonList.reload()
              } else {
                this.isDelete = true
              }
            })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },
    // 回收站关闭后
    afterRecycleClosedHandler (restored) {
      if (this.submodule === this.submodules[1]) {
        restored && this.$refs.manageDraftLessonList.reload()
      } else {
        restored && this.$refs.lessonList.reload()
      }
    },
    handleUpdate (detailLesson, attr, value) {
      if (attr === 'likeCount') {
        Object.assign(this.cardLesson, value)
      }
      if (attr === 'favoriteCount') {
        Object.assign(this.cardLesson, value)
      }
    },
    // 打开历史抽屉
    openHistory () {
      if (!this.lessonId) {
        this.$message.error('Unable to view history version')
        return
      }
      this.showHistoryDrawer = true
    },
  }
}
</script>
<style lang="less" scoped>
.manage-lesson-tabs /deep/ & {

  & > :nth-child(1), & > :nth-child(2), & > :nth-child(3) {
    margin-left: 0;
  }
}
</style>
