<template>
  <div class="scrollbar">
    <lesson-detail ref="lessonDetail" @mountedAfter="lessonDetailMountedAfter" :isFromLibrary="isFromLibrary" :lesson-id="lessonId" :isDialog="false">
      <template slot="header-left" slot-scope="{lesson}">
        <el-button @click="this.$router.back();" v-if="false">
          {{ $t('loc.lessonLibraryTabName1') }}
        </el-button>
        <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/>
        <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
        <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
      </template>

      <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
        <!--删除-->
        <el-tooltip :content="$t('loc.lessons2DeleteLesson')" v-if="isAdmin() && isOwnerLesson(lesson)"
          placement="top" effect="dark">
          <i class="el-icon-delete" style="cursor: pointer" @click="lessonDeleteClickHandler(lesson)"></i>
        </el-tooltip>
        <!--推荐-->
        <lesson-promote v-if="lesson.type !== 'AGENCY' && !isUseAdaptedUDLAndCLR" :lesson-id="lesson.id" :lesson-author-id="lesson.createUserId"/>
        <!--复制-->
        <lesson-replicate :lesson-id="lesson.id"/>
        <!--编辑-->
        <el-button icon="el-icon-edit" size="small" v-if="isAdmin() && isOwnerLesson(lesson)" plain type="primary"
                   @click="lessonEditClickHandler(lesson)" class="remove-margin-l-0">
          {{ $t('loc.edit') }}
        </el-button>
        <lesson-template-select-modal
          v-if="showLessonTemplate(lesson.ages, lesson.activityType)"
          :lessonAges="lesson.ages"
          style="width: auto;"
          buttonSize="small"
          :inDialog="true"
          :showGuidePopover="true"
          v-model="lesson.templateType"
          :lessonId="lesson.id"
          :isMyLesson="isOwnerLesson(lesson)"
          redirectRoute="EditLesson"
        />
        <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                         :lesson="lesson"/>
      </template>

    </lesson-detail>
  </div>
</template>

<script>
// import LessonLike from '../components/LessonLike'
// import LessonFavorite from '../components/LessonFavorite'
import LessonDownload from '../components/LessonDownload'
import LessonDetail from '../components/LessonDetail'
// import LessonReadCount from '../components/LessonReadCount'
import LessonPromote from '../components/LessonPromote'
import { mapState } from 'vuex'
import LessonReplicate from '../components/LessonReplicate'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import Api from '../../../../../api/lessons2'
import tools from '@/utils/tools'

export default {
  name: 'AgencyLessonDetail',
  components: {
    LessonReplicate,
    // LessonReadCount,
    LessonDetail,
    // LessonLike,
    // LessonFavorite,
    LessonDownload,
    LessonPromote,
    LessonTemplateSelectModal
  },
  data () {
    return {
      isFromLibrary: true,
        isUseAdaptedUDLAndCLR: true, // 是否已经启用了 Adapt UDL and CLR
    }
  },
  props: ['lessonId'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      educator: state => (!state.common.open || state.common.open.educator), // 是否是 educator 机构
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
    }),
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    }
  },
  methods: {
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    // 判断是否是自己的课程
    isOwnerLesson (lesson) {
      return (lesson.createUserId === this.currentUser.user_id)
    },
    lessonEditClickHandler (lesson) {
      this.$analytics.sendEvent('web_lesson_library_my_less_eidt')
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: lesson.id,
          type: 'Draft'
        }
      })
    },
    lessonDeleteClickHandler (lesson) {
      const h = this.$createElement
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.lessons2DeleteTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            done()
            Api.deleteLesson(lesson.id).then(() => {
              if (this.educator) {
                this.$router.push({ name: 'TeacherLessonList' })
              }
            })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },
      /**
       * 课程内容渲染后来自子组件的回调，用以判断是否使用了 Adapt UDL and CLR
       */
      lessonDetailMountedAfter() {
          if (this.$refs.lessonDetail) {
              // 判断是否启用了 UDL 和 CLR
              this.isUseAdaptedUDLAndCLR = this.$refs.lessonDetail.isUseAdaptedUDLAndCLR;
          }
      },
  }
}
</script>

<style scoped lang="less">
@media screen and (max-width:1199px) {
  .lesson-detail /deep/ & {
    background-color: #fff;
    padding: 0 30px;
    width: 800px;
    margin: 18px auto;

    & > :first-child {
      height: 54px;
    }

    & > .lesson-detail__content {
      padding-bottom: 60px;
    }

    .the-end {
      margin-bottom: 50px;
    }
  }
}
@media screen and (min-width:1200px) {
  .lesson-detail /deep/ & {
    background-color: #fff;
    padding: 0 50px;
    width: 1150px;
    margin: 24px auto;

    & > :first-child {
      height: 54px;
    }

    & > .lesson-detail__content {
      padding-bottom: 60px;
    }

    .the-end {
      margin-bottom: 50px;
    }
  }
}
</style>
