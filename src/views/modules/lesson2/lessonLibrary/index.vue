<template>
  <div ref="container" class="container">
  <div style="max-width: 1200px; margin: 0 auto;">
    <!-- 欢迎区域 -->
    <welcome-header ref="welcomeHeader" @create-lesson="newLessonButtonClickHandler('header')" class="welcome-section" />
    <div id="mark"></div>
    <!-- 标签页和搜索框容器 -->
    <div v-if="!isCurriculumPlugin" class="tabs-search-container" style="margin-right: -30px; padding-right: 30px; /* 盖住课程列表的阴影 */">
      <!-- 标签页 -->
      <el-tabs :style="{'opacity': isCurriculumPlugin ? '0' : '1'}" v-model="currentTagName" @tab-click="tabsClickedHandler" class="lg-tabs lesson-tabs">
        <!-- Public Lessons -->
        <el-tab-pane v-if="!isCurriculumPlugin" :label="$t('loc.lessonLibraryTabName1')" :name="submodules[0]"></el-tab-pane>
        <!-- Agency-wide Lessons -->
        <el-tab-pane :label="$t('loc.lessonLibraryTabName2')" :name="submodules[1]" v-if="isAgencyUser && !isCurriculumPlugin"></el-tab-pane>
        <!-- My Lessons -->
        <el-tab-pane :label="$t('loc.lessonLibraryTabName3')" :name="submodules[2]"></el-tab-pane>
        <!-- Lessons Management -->
        <el-tab-pane :label="$t('loc.lessonLibraryTabName4')" :name="submodules[3]" v-if="!isCurriculumPlugin && isAgencyUser && isAdmin()"></el-tab-pane>
      </el-tabs>

      <!-- 搜索框 - 放在标签页右侧 -->
      <div v-if="!isCurriculumPlugin || currentTagName !== 'TeacherLessonList'" class="search-box-container search-box">
        <div class="display-flex align-items-center">
        <!-- 小屏关键词搜索 -->
          <i ref="searchBack" class="hidden-lg-and-up-lesson lg-icon lg-icon-arrow-left lg-pointer font-size-24 lg-margin-right-16" style="display: none;"></i>
          <el-input class="border-bold hidden-lg-and-up-lesson"
                    ref="searchBox"
                    style="border-radius: 100px;display:none;-webkit-user-select: auto!important;-khtml-user-select: auto!important;-moz-user-select: auto!important;-ms-user-select: auto!important; -o-user-select: auto!important;user-select: auto!important;"
                    v-model="keyword"
                    @input="autoSearch"
                    @blur="blurSearch"
                    :placeholder="$t('loc.lessons2LessonListSearchPlaceholder')"
                    @keyup.enter.native="keyUpHandler">
            <i
                class="el-icon-search el-input__icon lg-pointer"
                slot="prefix">
            </i>
          </el-input>
        </div>
 
        <!--大屏关键词搜索-->
        <el-input class="hidden-md-and-down-lesson border-bold"
                  style="border-radius: 100px;-webkit-user-select: auto!important;-khtml-user-select: auto!important;-moz-user-select: auto!important;-ms-user-select: auto!important; -o-user-select: auto!important;user-select: auto!important;"
                  v-model="keyword"
                  @input="autoSearch"
                  :placeholder="$t('loc.lessons2LessonListSearchPlaceholder')"
                  @keyup.enter.native="keyUpHandler">
          <i
              class="el-icon-search el-input__icon lg-pointer"
              slot="prefix">
          </i>
        </el-input>
        <!-- 小屏关键词搜索聚焦按钮 -->
        <el-button class="search-btn hidden-lg-and-up-lesson" icon="el-icon-search" @click="focusSearch"></el-button>
      </div>

      <el-button
        v-if="!isCurriculumPlugin || currentTagName !== 'TeacherLessonList'"
        :style="isShowCreateLessonButton ? '' : 'padding: 0; width: 0;'"
        class="create-lesson-button ai-btn"
        type="primary"
        @click="newLessonButtonClickHandler('button')">
        <i class="el-icon-plus"></i>
        <span class="create-lesson-button-text"> {{ $t('loc.createLessonBtn') }} </span>
      </el-button>
    </div>

    <div class="content-area">
      <!--课程筛选条件-->
      <div v-if="!isCurriculumPlugin" class="filter-column" :class="{'filter-hidden': shouldHideFilter}">
        <!-- lesson filter -->
        <!--v-show="[0,1,2].includes(submodules.indexOf(currentTagName))"-->
        <!-- <lesson-filter ref="lessonFilter"
                      :ages.sync="params.ages"
                      :mappedFrameworkId.sync="params.mappedFrameworkId"
                      :themeIds.sync="params.themeIds"
                      :domainIds.sync="params.domainIds"
                      :mapped-measure-ids.sync="params.otherMeasureIds"
                      :mappedDrdpFrameworkIds.sync="params.mappedDrdpFrameworkIds"
                      v-show="!isCurriculumPlugin && [0,1,2].includes(submodules.indexOf(currentTagName))"/> -->
        <!-- teacher filter -->
        <!-- <teacher-filter :user-id.sync="manageParams.userId"
          :is-admin.sync="manageParams.isAdmin"
          v-if="currentTagName === submodules[3]"
        /> -->
      </div>

      <!-- 课程列表 -->
      <el-col :class="{'full-width': shouldHideFilter}" class="content-column">
        <!-- Public Lessons -->
        <public-lesson :params="params"
          :currentTag="currentTagName"
          v-if="currentTagName === submodules[0]"
          :showFilter="!shouldHideFilter"
          @toggle-filter="toggleFilter">
          <!--课程排序-->
          <div slot="header-right">
            <span style="margin-right: 4px">{{ $t('loc.sortBy') }}:</span>
            <el-select class="border-bold" size="medium" style="width: 135px" v-model="params.orderKey" placeholder="Update time">
              <el-option v-for="item in orderOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </div>
        </public-lesson>
        
        <!-- Agency-wide Lessons -->
        <agency-wide-lesson :params="params"
          v-if="currentTagName === submodules[1]"
          v-bind="submoduleProps"
          :showFilter="!shouldHideFilter"
          @toggle-filter="toggleFilter">
          <!--课程排序-->
          <div slot="header-right">
            <span style="margin-right: 4px">{{ $t('loc.sortBy') }}:</span>
            <el-select class="border-bold" size="medium" style="width: 135px" v-model="params.orderKey" placeholder="Update time">
              <el-option v-for="item in orderOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </div>
        </agency-wide-lesson>
        
        <!-- My Lessons -->
        <teacher-lesson v-if="currentTagName === submodules[2]"
          :params.sync="params"
          :showFilter="!shouldHideFilter"
          :keyword="keyword"
          :isShowCreateLessonButton="isShowCreateLessonButton"
          :isCurriculumPlugin="isCurriculumPlugin"
          v-bind="submoduleProps"
          @toggle-filter="toggleFilter"
          @search-input="handleTeacherLessonSearch"
          @search-keyup="keyUpHandler"
          @search-blur="blurSearch"
          @search-focus="focusSearch"
          @update-create-total="handleTotalUpdate">
          <!--课程排序-->
          <div v-show="!isCurriculumPlugin" slot="header-right">
            <span style="margin-right: 4px">{{ $t('loc.sortBy') }}:</span>
            <el-select size="medium" style="width: 135px" v-model="params.orderKey" placeholder="Update time" class="lesson-sort-class border-bold">
              <el-option v-for="item in orderOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </div>
        </teacher-lesson>
        
        <!-- Lessons Management -->
        <managed-teacher-lesson-list v-if="currentTagName === submodules[3] && manageParams.userId" :params="manageParams" :="submoduleProps">
          <!-- 课程类型 -->
          <div slot="header-left">
            <el-select class="border-bold" size="medium"  v-model="manageParams.isAdaptedLesson"   placeholder="Class-specific-Lessons">
              <el-option  v-for="item in classTypeOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </div>
          <!--课程排序-->
          <div slot="header-right">
            <span style="margin-right: 4px">{{ $t('loc.sortBy') }}:</span>
            <el-select class="border-bold" style="width: 135px" size="medium"  v-model="manageParams.orderKey"   placeholder="Update time">
              <el-option  v-for="item in orderOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </div>
        </managed-teacher-lesson-list>
      </el-col>
    </div>
  </div>
  <!-- 欢迎弹窗 -->
  <CreateLessonPlanDialog v-if="isCurriculumPlugin"
                          :dialogVisible.sync="createLessonPlanDialogOpen"/>
  <!-- 欢迎弹窗 -->
  <LessonPlanToUnitDialog v-if="isCurriculumPlugin"
                          :dialogVisible.sync="lessonPlanToUnitDialogOpen"/>
</div>
</template>
<script>

import { isTeacher } from '@/utils/common'
import tools from '@/utils/tools'
import { mapState } from 'vuex'
// import LessonFilter from '../component/lessonFilter'
// import TeacherFilter from '../component/TeacherFilter'
import AgencyWideLesson from './agencyLesson'
import WelcomeHeader from './components/WelcomeHeader.vue'
import PublicLesson from './publicLesson'
import TeacherLesson from './teacherLesson'
import ManagedTeacherLessonList from './teacherManager'
import CreateLessonPlanDialog from './guideDialog/CreateLessonPlanDialog.vue'
import LessonPlanToUnitDialog from './guideDialog/LessonPlanToUnitDialog.vue'
import { LESSON_PLAN_NEW_USER_UTC } from '../../../../utils/const'

let markOffsetTop = null

export default {
  name: 'LessonLibrary',
  components: {
    ManagedTeacherLessonList,
    // TeacherFilter,
    // LessonFilter,
    PublicLesson,
    AgencyWideLesson,
    TeacherLesson,
    WelcomeHeader,
    CreateLessonPlanDialog,
    LessonPlanToUnitDialog
  },
  props: [
    'submoduleName',
    'submoduleProps'
  ],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      open: state => state.common.open
    }),
    /**
     * 获取用户 id
     */
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    /**
     * 是否为新用户
     * @returns {boolean}
     */
    isLessonPlanNewUser () {
      return this.currentUser && this.currentUser.userInfo && tools.timeIsAfter(this.currentUser.userInfo.createdAtUtc, LESSON_PLAN_NEW_USER_UTC)
    },
    tabs () {
      let tabs = [
        {
          name: 'PublicLessonList',
          label: this.$t('loc.lessonLibraryTabName1')
        },
        {
          name: 'AgencyLessonList',
          label: this.$t('loc.lessonLibraryTabName2')
        },
        {
          name: 'TeacherLessonList',
          label: this.$t('loc.lessonLibraryTabName3')
        },
        {
          name: 'ManagedTeacherLessonList',
          label: this.$t('loc.lessonLibraryTabName4')
        }
      ]
      return tabs
    },
    isAgencyUser () {
      return this.open && !this.open.educator
    },
  },
  mounted () {
    // 课程库曝光埋点
    this.$analytics.sendEvent('cg_lesson_plan_exposure')
    // 当滚动条高度大于欢迎区域高度时，显示创建课程按钮, !! 不要频繁访问计算属性
    markOffsetTop = this.$el.querySelector('#mark').offsetTop
    this.$el.addEventListener('scroll', e => {
      this.isShowCreateLessonButton = e.target.scrollTop >= markOffsetTop
    })
    // 获取引导信息
    this.getCreateLessonPlanGuide()
  },
  data () {
    return {
      isShowCreateLessonButton: false,
      params: {
        //  过滤条件--其他测评点
        otherMeasureIds: [],
        // 过滤条件--映射测评点对应的 DRDP 框架的 ID
        mappedDrdpFrameworkIds: [],
        // 过滤条件--年龄段
        ages: [],
        // 过滤条件--领域
        domainIds: [],
        // 过滤条件--主题
        themeIds: [],
        // 搜索条件
        keyword: '',
        // 当前映射的框架
        mappedFrameworkId: '',
        orderKey: 'UPDATE_TIME',
        // 课程类型筛选条件
        isAdaptedLesson: ''
      },
      // 教师过滤条件
      manageParams: {
        userId: null,
        isAdmin: false,
        keyword: '',
        orderKey: 'UPDATE_TIME',
        isAdaptedLesson:  ''
      },
      // 搜索条件
      keyword: '',
      orderOptions: [
        {
          value: 'UPDATE_TIME',
          label: this.$t('loc.UpdateTime')
        }
      ],
      classTypeOptions: [
        {
          value: '',
          label: this.$t('loc.allLessonType')
        },
        {
          value: true,
          label: this.$t('loc.adaptedLessonType')
        },
        {
          value: false,
          label: this.$t('loc.generalLessonType')
        }
      ],
      currentTagName: this.submoduleName || 'TeacherLessonList',
      submodules: ['PublicLessonList', 'AgencyLessonList', 'TeacherLessonList', 'ManagedTeacherLessonList'],
      shouldHideFilter: false,
      createCurrentTotal: 0, // 添加当前课程总数
      createLessonPlanDialogOpen: false, // 引导创建 Lesson Plan 弹窗
      lessonPlanToUnitDialogOpen: false // 引导 Lesson Plan 跳转 Unit 弹窗
    }
  },
  watch: {
    'params.keyword': function () {
      this.keyword = this.params.keyword
      this.manageParams.keyword = this.params.keyword
    },
    // 监听路由变化，自动切换到对应的 tab
    '$route.name': {
      immediate: true,
      handler(newRouteName) {
        // 如果新路由名称在 submodules 数组中存在，则切换到对应的 tab
        if (this.submodules.includes(newRouteName)) {
          this.currentTagName = newRouteName
        }
      }
    },
    /**
     * 监听 createCurrentTotal 变化
     */
    createCurrentTotal: {
      handler (newTotal) {
        // 只有在 TeacherLesson 标签页且课程总数为1时才调用
        if (newTotal === 2 && this.currentTagName === this.submodules[2]) {
          this.getLessonPlanToUnitGuide()
        }
      }
    }
  },
  methods: {
    /**
     * 处理子组件传递的 total 更新
     */
    handleTotalUpdate (total) {
      this.createCurrentTotal = total
    },
    // 获取创建 Lesson Plan 引导信息
    getCreateLessonPlanGuide () {
      if (!this.isLessonPlanNewUser) {
        return
      }
      return new Promise((resolve, reject) => {
        // 获取用户是否需要引导
        const firstVisit = localStorage.getItem('CREATE_LESSON_PLAN_GUIDE_FIRST_VISIT' + this.currentUserId)
        // 如果没有缓存，则请求接口
        if (!firstVisit) {
          this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
            if (result) {
              this.$analytics.sendEvent('cg_lesson_welcome_unpack')
              this.createLessonPlanDialogOpen = result.showCurriculumCreateLessonPlanGuide
              resolve(result.showCurriculumCreateLessonPlanGuide)
            }
          }).catch((err) => {
            reject(err)
          })
        } else {
          this.createLessonPlanDialogOpen = false
          resolve(false)
        }
      })
    },

    // 获取跳转 Unit 引导信息
    getLessonPlanToUnitGuide () {
      if (!this.isLessonPlanNewUser) {
        return
      }
      return new Promise((resolve, reject) => {
        // 先判断单元引导有没有触发
        const unitFirstVisit = localStorage.getItem('UNIT_PLANNER_GUIDE_FIRST_VISIT' + this.currentUserId)
        // 如果单元引导已经触发不再触发后续处理
        if (unitFirstVisit) {
          resolve(false)
          return
        }
        // 获取用户是否需要引导
        const firstVisit = localStorage.getItem('LESSON_PLAN_TO_UNIT_GUIDE_FIRST_VISIT' + this.currentUserId)
        // 如果没有缓存，则请求接口
        if (!firstVisit) {
          this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
            if (result) {
              // 单元引导已触发不再处理
              if (!result.showCurriculumUnitPlannerGuide) {
                resolve(false)
                return
              }
              this.$analytics.sendEvent('cg_lesson_unit_pop_shown')
              this.lessonPlanToUnitDialogOpen = result.showCurriculumLessonPlanToUnitGuide
              this.lessonPlanToUnitDialogOpen = true
              resolve(result.showCurriculumLessonPlanToUnitGuide)
            }
          }).catch((err) => {
            reject(err)
          })
        } else {
          this.lessonPlanToUnitDialogOpen = false
          resolve(false)
        }
      })
    },

    /**
     * 滚动到 tab 顶部
     */
    scrollToTabTop() {
      // markOffsetTop，这个值就是粘性定位开始生效的位置
      // 使用 container 的 scrollTo 方法实现平滑滚动
      if (this.$refs.container.scrollTop > markOffsetTop) {
        this.$refs.container.scrollTo({
          top: markOffsetTop + 1,
          behavior: 'instant'
        })
      }
    },
    tabsClickedHandler () {
      this.scrollToTabTop()
      this.keyword = ''
      this.params.orderKey = 'UPDATE_TIME'
      this.manageParams.orderKey = 'UPDATE_TIME'
      this.params.keyword = this.keyword
      if (this.currentTagName === 'ManagedTeacherLessonList') {
        // 清空 params
        this.params = {
          ages: [],
          domainIds: [],
          themeIds: [],
          otherMeasureIds: [],
          mappedDrdpFrameworkIds: [],
          keyword: '',
          orderKey: 'UPDATE_TIME'
        }
        // 重置 lessonFilter 组件的状态
        if (this.$refs.lessonFilter) {
          // 调用 lessonFilter 组件的重置方法
          this.$refs.lessonFilter && this.$refs.lessonFilter.resetFilter()
        }
        this.shouldHideFilter = false
      }
      if (this.currentTagName === 'PublicLessonList') {
        this.$analytics.sendEvent('web_lesson_library_public_exposure')
      } else if (this.currentTagName === 'AgencyLessonList') {
        this.$analytics.sendEvent('web_lesson_library_agency_exposure')
      } else if (this.currentTagName === 'ManagedTeacherLessonList') {
        this.$analytics.sendEvent('web_lesson_library_mgt_exposure')
      } else if (this.currentTagName === 'TeacherLessonList') {
        this.$analytics.sendEvent('web_lesson_library_my_less_exposure')
      }
      this.$router.push({ name: this.currentTagName })
    },
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    // 关键字更新自动搜索课程
    autoSearch: tools.debounce(function () {
      if (this.currentTagName === 'PublicLessonList') {
        this.$analytics.sendEvent('web_lesson_library_public_search')
      } else if (this.currentTagName === 'AgencyLessonList') {
        this.$analytics.sendEvent('web_lesson_library_agency_search')
      } else if (this.currentTagName === 'TeacherLessonList') {
        this.$analytics.sendEvent('web_lesson_library_my_less_search')
      }
      this.changeKeyword()
    }, 2000),
    changeKeyword () {
      this.params.keyword = this.keyword.trim()
      this.params.orderKey = ''
    },
    keyUpHandler () {
      this.scrollToTabTop()
      this.changeKeyword()
    },
    newLessonButtonClickHandler (type) {
      // 创建课程点击埋点
      if (type === 'header') {
        // 顶部 header
        this.$analytics.sendEvent('cg_lesson_plan_create')
      }
      if (isTeacher()) {
        this.$analytics.sendEvent('web_lesson_library_my_less_add')
      } else {
        this.$analytics.sendEvent('web_lesson_library_agency_add')
      }
      this.$router.push({ name: 'AddLesson' })
    },
    closeKeyboard () {
      if (tools.isComeFromIPad()) {

      }
    },
    // 判断是否是 ipad 端
    isiPad () {
      return tools.isComeFromIPad()
    },
    // 搜索框聚焦
    focusSearch () {
      // 显示返回按钮
      this.$refs.searchBack.style.display = 'block'
      // 显示搜索框
      this.$refs.searchBox.$el.style.display = 'block'
      // 设置搜索框宽度 100%
      this.$refs.searchBox.$el.style.width = '100%'
      // 聚焦
      this.$refs.searchBox.focus()
      // 隐藏头部 tab
      let headerTab = document.querySelector('.lesson-tabs')
   
      // 设置宽度 0
      headerTab.style.width = '0'
      if (headerTab && headerTab.querySelector('.el-tabs__header')) {
        headerTab.querySelector('.el-tabs__header').style.visibility = 'hidden'
      }
    },
    // 搜索框失焦
    blurSearch () {
      // 隐藏返回按钮
      this.$refs.searchBack.style.display = 'none'
      // 隐藏搜索框
      this.$refs.searchBox.$el.style.display = 'none'
      // 设置搜索框宽度 auto
      this.$refs.searchBox.$el.style.width = 'auto'
      // 显示头部 tab
      let headerTab = document.querySelector('.lesson-tabs')
      if (headerTab && headerTab.querySelector('.el-tabs__header')) {
        headerTab.querySelector('.el-tabs__header').style.visibility = 'visible'
      }

      // 设置宽度 100%
      headerTab.style.width = '100%'
    },
    toggleFilter() {
      this.shouldHideFilter = !this.shouldHideFilter
    },
    handleTeacherLessonSearch(value) {
      this.keyword = value
      this.autoSearch()
    }
  }
}
</script>

<style scoped lang="less">
.hidden-md-and-down-lesson {
  /deep/.el-input__inner {
    border-radius: 100px;
  }
}

.hidden-lg-and-up-lesson {
  /deep/.el-input__inner {
    border-radius: 100px;
  }
}

@media only screen and (max-width: 980px) {
  .hidden-md-and-down-lesson {
    display: none!important;
  }

  .tabs-search-container {
    .lesson-tabs {
      flex: unset !important;
    }
  }
}

@media only screen and (min-width: 980px) {
  .hidden-lg-and-up-lesson {
      display: none!important;
  }
  .hidden-md-and-down-lesson {
    display: block!important;
  }
}

.container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 0px 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

.welcome-section {
  margin-bottom: 25px;
  width: 100%;
  transition: all 0.3s ease;
}

/* 内容区域 */
.content-area {
  display: flex;
  margin-top: 14px;
}

/* 保留原有样式但隐藏 */
.lesson-library-header {
  width: auto;
  position: absolute;
  top: 5px;
  right: 10px;
  display: flex;
  align-items: center;
  justify-content: end;
  flex-flow: row-reverse;
}

/deep/.search-box:focus-within > .search-btn {
  display: none;
}

.search-box:focus-within + .lg-tabs {
  display: none;
}

.tabs-search-container {
  position: sticky;
  top: -1px;
  z-index: 100;
  background-color: #f7f7f7;
  padding: 10px 0;

  display: flex;
  align-items: center;
  gap: 10px;

  .lesson-tabs {
    overflow-x: auto;
    overflow-y: hidden;
    flex: 2;
  }

  .search-box-container {
    flex: 1;
    width: 100%;
    text-align: end;
  }

}

@media screen and (max-width:1199px) {
  /deep/.lg-tabs .el-tabs__item {
    padding: 0 8px !important;
    font-size: 14px !important;
  }

  .create-lesson-button.ai-btn {
    width: 50px;
  }

  .create-lesson-button-text {
    display: none;
  }

  /*
  .search-box-container {
    position: absolute;
    right: 10px;
    top: 5px;
    padding-left: 0;
    max-width: 180px;
  }
    */

}

.filter-column {
  transition: all 0.3s ease;
  padding-right: 16px;
  width: 216px;
}

.filter-hidden {
  width: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  opacity: 0;
  height: 0;
}

.content-column {
  transition: all 0.3s ease;
  border-radius: 8px;
  flex: 1;
}

.full-width {
  width: 100% !important;
}

.create-lesson-button {
  overflow: hidden;
  transition: all 0.8s ease;
  width: 200px;
}

</style>
