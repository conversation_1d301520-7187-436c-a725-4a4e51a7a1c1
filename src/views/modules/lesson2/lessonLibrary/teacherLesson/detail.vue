<template>
  <div class="scrollbar">
    <lesson-detail ref="lessonDetail" @mountedAfter="lessonDetailMountedAfter" :isFromLibrary="isFromLibrary" :lesson-id="lessonId">
      <template slot="header-left" slot-scope="{lesson}">
        <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/>
        <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
        <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
      </template>
      <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
        <!--删除-->
        <el-tooltip :content="$t('loc.lessons2DeleteLesson')"
          placement="top" effect="dark">
          <i class="el-icon-delete" style="cursor: pointer" @click="lessonDeleteClickHandler(lesson)"></i>
        </el-tooltip>
        <!--推荐-->
        <lesson-promote v-if=!isUseAdaptedUDLAndCLR :lesson-id="lesson.id" :lesson-author-id="lesson.createUserId"/>
        <!--复制-->
        <lesson-replicate :lesson-id="lesson.id"/>
        <!--编辑-->
        <el-button size="small" type="primary" plain icon="el-icon-edit" @click="lessonEditClickHandler(lesson)">
          {{ $t('loc.edit') }}
        </el-button>
        <lesson-template-select-modal
          v-if="showLessonTemplate(lesson.ages, lesson.activityType)"
          :lessonAges="lesson.ages"
          style="width: auto;"
          buttonSize="small"
          :inDialog="true"
          :showGuidePopover="true"
          v-model="lesson.templateType"
          :lessonId="lesson.id"
          :isMyLesson="isOwnerLesson(lesson)"
          redirectRoute="EditLesson"
        />
          <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                           :lesson="lesson"/>
      </template>
    </lesson-detail>
  </div>
</template>

<script>
// import LessonLike from '../components/LessonLike'
// import LessonFavorite from '../components/LessonFavorite'
import LessonDownload from '../components/LessonDownload'
import LessonDetail from '../components/LessonDetail'
// import LessonReadCount from '../components/LessonReadCount'
import { mapState } from 'vuex'
import LessonReplicate from '../components/LessonReplicate'
import Api from '../../../../../api/lessons2'
import LessonPromote from '../components/LessonPromote'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import tools from '@/utils/tools'

export default {
  name: 'PublicLessonDetail',
  components: {
    LessonReplicate,
    // LessonReadCount,
    LessonDetail,
    // LessonLike,
    // LessonFavorite,
    LessonPromote,
    LessonDownload,
    LessonTemplateSelectModal
  },
  props: ['lessonId'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
    }),
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    },
  },
  data () {
    return {
      agencyStatus: 'ppp', // 推荐状态 PROMOTED | NONE
      id: this.lessonId,
      isFromLibrary: true,
        isUseAdaptedUDLAndCLR: true, // 是否已经启用了 Adapt UDL and CLR
    }
  },
  created () {
    Api.getLessonRecommendStatus(this.lessonId).then(res => {
      this.agencyStatus = res.agencyStatus
    })
  },
  methods: {
    // 判断是否是自己的课程
    isOwnerLesson (lesson) {
      return (lesson.createUserId === this.currentUser.user_id)
    },
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    lessonClickedHandler (lesson) {
      this.lessonId = lesson.id
      this.showDetail = true
    },
    lessonEditClickHandler (lesson) {
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: lesson.id,
          type: 'Draft'
        }
      })
    },
    lessonDeleteClickHandler (lesson) {
      // 提示信息
      let tipsContent = this.agencyStatus === 'PROMOTED' ? this.$t('loc.lessons2DeleteStatusAgencyTips') : this.$t('loc.lessons2DeleteTips')
      const h = this.$createElement
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, tipsContent)
        ]),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // 删除课程
            Api.deleteLesson(lesson.id).then(res => {
              done()
              this.$message({
                type: 'success',
                message: 'Lesson was deleted successfully!'
              })
              this.$router.push({ name: 'Create' })
            })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },
      /**
       * 课程内容渲染后来自子组件的回调，用以判断是否使用了 Adapt UDL and CLR
       */
      lessonDetailMountedAfter() {
          if (this.$refs.lessonDetail) {
              // 判断是否启用了 UDL 和 CLR
              this.isUseAdaptedUDLAndCLR = this.$refs.lessonDetail.isUseAdaptedUDLAndCLR;
          }
      },
  }
}
</script>

<style scoped lang="less">
.lesson-detail /deep/ & {
  background-color: #fff;
  padding: 0 50px;
  width: 1150px;
  margin: 24px auto;

  & > :first-child {
    height: 54px;
  }

  & > .lesson-detail__content {
    padding-bottom: 60px;
  }
}
</style>
