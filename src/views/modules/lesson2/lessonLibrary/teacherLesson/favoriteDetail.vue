<template>
  <div class="scrollbar">
    <lesson-detail :isFromLibrary="isFromLibrary" :lesson-id="lessonId">
      <template slot="header-left" slot-scope="{lesson}">
        <el-button @click="this.$router.back();" v-if="false">
          {{ $t('loc.lessonLibraryTabName1') }}
        </el-button>
        <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount"/>
        <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/>
        <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
      </template>

      <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
        <lesson-replicate :lesson-id="lesson.id"/>
          <lesson-download :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id"
                           :lesson="lesson"/>
      </template>
    </lesson-detail>
  </div>
</template>

<script>
// import LessonLike from '../components/LessonLike'
// import LessonFavorite from '../components/LessonFavorite'
import LessonDownload from '../components/LessonDownload'
import LessonDetail from '../components/LessonDetail'
// import LessonReadCount from '../components/LessonReadCount'
import { mapState } from 'vuex'
import LessonReplicate from '../components/LessonReplicate'

export default {
  name: 'FavoriteLessonDetail',
  components: {
    LessonReplicate,
    // LessonReadCount,
    LessonDetail,
    // LessonLike,
    // LessonFavorite,
    LessonDownload
  },
  data () {
    return {
      isFromLibrary: true
    }
  },
  props: ['lessonId'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin
    })
  },
  methods: {
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    }
  }
}
</script>

<style scoped lang="less">
.lesson-detail /deep/ & {
  background-color: #fff;
  padding: 0 50px;
  width: 1150px;
  margin: 24px auto;

  & > :first-child {
    height: 54px;
  }

  & > .lesson-detail__content {
    padding-bottom: 60px;
  }
}
</style>
