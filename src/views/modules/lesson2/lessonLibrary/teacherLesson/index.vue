<template>
  <div>
    <!-- 课程列表 -->
    <lesson-list ref="lessonList" :loader="displayLessonsLoader" :params="params" class="lesson-list-container"  @update:total="onTotalUpdate">
      <!-- 头部 -->
      <template slot="header" slot-scope="{total}">
        <div class="sticky-header" ref="stickyHeader">
          <div style="display: flex;justify-content: space-between;align-items: center;">
            <div style="display: flex;align-items: center;gap: 10px;">
              <!-- 过滤器 -->
              <!-- <filter-controller-button
                v-if="!isCurriculumPlugin"
                :show-filter="showFilter"
                :params="params"
                @toggle-filter="$emit('toggle-filter')"
              /> -->
              <el-tabs class="lg-tabs-small" v-model="submodule" @tab-click="checkTag">
                <el-tab-pane :label="$t('loc.lessons2TeacherLessonTagName1')" :name="submodules[0]"></el-tab-pane>
                <el-tab-pane v-if="!isAdmin()" :label="$t('loc.lessons2TeacherLessonTagName2')" :name="submodules[1]"></el-tab-pane>
                <el-tab-pane :label="$t('loc.lessons2TeacherLessonTagName3')" :name="submodules[2]"></el-tab-pane>
              </el-tabs>
              <el-tooltip :content="$t('loc.lessonTailorTitle')" placement="top">
                <el-button style="padding: 0 10px" class="lg-icon lg-icon-settings" @click="manageLearnerProfile"></el-button>
              </el-tooltip>
              <!-- 回收站视图 -->
              <recycle-lesson v-if="false" style="display: inline-block;margin-left: 10px" @afterClosed="lessonRecycledHandler" />
            </div>
            <div style="display: flex;align-items: center;gap: 24px;">
              <!-- 课程总数 -->
              <div>
                <span style="font-weight: bold"><span class="font-weight-400">{{ $t('loc.totalEntries') }}: </span>{{
                  total }}</span>
              </div>
              <!-- 搜索框 -->
              <div v-if="isCurriculumPlugin" class="search-box-container search-box">
                <div class="display-flex align-items-center">
                  <!-- 小屏关键词搜索 -->
                  <i ref="searchBack" class="hidden-lg-and-up-lesson lg-icon lg-icon-arrow-left lg-pointer font-size-24 lg-margin-right-16" style="display: none;"></i>
                  <el-input class="border-bold hidden-lg-and-up-lesson"
                           ref="searchBox"
                           style="border-radius: 100px;display:none;-webkit-user-select: auto!important;-khtml-user-select: auto!important;-moz-user-select: auto!important;-ms-user-select: auto!important; -o-user-select: auto!important;user-select: auto!important;"
                           :value="keyword"
                           @focus="handleSearchFocus"
                           @input="handleSearch"
                           @blur="$emit('search-blur')"
                           :placeholder="$t('loc.lessons2LessonListSearchPlaceholder')"
                           @keyup.enter.native="$emit('search-keyup')">
                    <i class="el-icon-search el-input__icon lg-pointer" slot="prefix"></i>
                  </el-input>
                </div>

                <!--大屏关键词搜索-->
                <el-input class="hidden-md-and-down-lesson border-bold"
                         style="border-radius: 100px;-webkit-user-select: auto!important;-khtml-user-select: auto!important;-moz-user-select: auto!important;-ms-user-select: auto!important; -o-user-select: auto!important;user-select: auto!important;"
                         :value="keyword"
                         @focus="handleSearchFocus"
                         @input="handleSearch"
                         :placeholder="$t('loc.lessons2LessonListSearchPlaceholder')"
                         @keyup.enter.native="$emit('search-keyup')">
                  <i class="el-icon-search el-input__icon lg-pointer" slot="prefix"></i>
                </el-input>
                <!-- 小屏关键词搜索聚焦按钮 -->
                <el-button class="search-btn hidden-lg-and-up-lesson" icon="el-icon-search" @click="$emit('search-focus')"></el-button>
              </div>
              <!-- 创建课程按钮 -->
              <el-button
                v-if="isShowCreateLessonButton"
                :style="isShowCreateLessonButton ? '' : 'padding: 0; width: 0;'"
                class="create-lesson-button ai-btn"
                type="primary"
                @click="newLessonButtonClickHandler('button')">
                <i class="el-icon-plus"></i>
                <span class="create-lesson-button-text"> {{ $t('loc.createLessonBtn') }} </span>
              </el-button>
              <div v-if="$slots['header-right'] && !isCurriculumPlugin">
                <slot name="header-right" />
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 课程列表内容 -->
      <template slot-scope="{lesson}">
        <!-- 我的课程 -->
        <lesson-card v-if="submodule === submodules[0]" :lesson="lesson" @click.native="lessonClickedHandler(lesson)">
          <!--判断课程是否推荐到机构课程-->
          <span v-if="lesson.agency" slot="lesson-promoted-agency" style="display: inline-block;vertical-align: middle">
            <el-tooltip class="item" effect="dark" :content="$t('loc.lessons2SharedToAgency')" placement="top">
<!--              <img src="../../../../../assets/img/lesson2/ic-remove-favorite.png" style="vertical-align: text-top;" />-->
            </el-tooltip>
          </span>
        </lesson-card>

        <!-- 收藏课程 -->
        <lesson-card v-if="submodule === submodules[1]" :lesson="lesson" @click.native="lessonClickedHandler(lesson)" @favorite="lessonFavoriteHandler">
          <template slot="mask">
            <lesson-item-deleted-mask :lesson="lesson" type="favorite" @removed="favoriteLessonRemovedHandler" />
          </template>
        </lesson-card>

        <!-- 草稿课程 -->
        <lesson-card v-if="submodule === submodules[2]" :lesson="lesson" @click.native="lessonClickedHandler(lesson)" :is-draft="true">
          <el-dropdown slot="lesson-card-corner-right-top" style="margin-right: 10px;" @click.native.stop="handleDropdownClick" trigger="click">
            <lesson-card-operation-icon class="el-dropdown-link" />
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="el-icon-edit" @click.native.stop="lessonEditClickHandler(lesson)">
                {{ $t('loc.edit') }}
              </el-dropdown-item>
              <el-dropdown-item icon="el-icon-delete" @click.native.stop="lessonDeleteClickHandler(lesson)">
                {{ $t('loc.delete') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </lesson-card>
      </template>

      <!-- 底部内容 -->
      <template slot="footer" slot-scope="{empty}">
        <!-- 我的课程为空底部 -->
        <create-lesson-empty v-if="showCreateLessonEmpty(empty)" @add-new-lesson="newLessonButtonClickHandler('empty')" />
        <!-- my lesson 存在过滤条件/草稿/收藏底部 -->
        <lesson-empty v-else-if="empty" :tip="$t('loc.lessons2NoResult')"/>
      </template>
    </lesson-list>

    <!--模态窗口展示详情页-->
    <lesson-detail-dialog :new-tab-to="submodule === submodules[0] ? 'MyLessonDetail' : 'FavoriteLessonDetail'"
      :show.sync="showDetail" :lesson-id="lessonId" :dsiable-esc-close="showHistoryDrawer">
      <!--详情页-->
      <lesson-detail ref="lessonDetail" @mountedAfter="lessonDetailMountedAfter" :isFromLibrary="isFromLibrary"
        :lessonId="lessonId" :submodule="submodules[1]" :isDialog="true">
        <template slot="header-left" slot-scope="{lesson}"
          v-if="submodule === submodules[0] || submodule === submodules[1]">
          <!-- <lesson-read-count v-if="!isCurriculumPlugin" :count="lesson.readCount" />
          <lesson-like v-if="!isCurriculumPlugin" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"
            @update:count="value=>handleUpdate(lesson,'likeCount',value)" />
          <lesson-favorite v-if="!isCurriculumPlugin" :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"
            @update:count="value=>handleUpdate(lesson,'favoriteCount',value)" /> -->
        </template>
        <template slot="header-right" slot-scope="{lesson,mappedFrameworkId}">
          <!--删除按钮-->
          <el-tooltip :content="$t('loc.lessons2DeleteLesson')"
            placement="top" effect="dark" v-if="submodule === submodules[0] && lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM' " >
            <i class="el-icon-delete" style="cursor: pointer" @click="lessonDeleteClickHandler(lesson)"></i>
          </el-tooltip>
          <!-- 推荐机构课程库按钮 -->
          <lesson-promote :lesson-id="lesson.id" :lesson-author-id="lesson.createUserId"
            v-if="submodule === submodules[0] && !isUseAdaptedUDLAndCLR && lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM'" @reloadLessons="reloadLessons" />
          <!-- 历史版本按钮 -->
          <el-button v-if="lesson.status !== 'DRAFT' && lessonId && lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM'" style="height: 32px;padding: 3px 6px!important;"
            type="primary" size="medium" @click="openHistory" plain>
            <div class="btn-center">
              <i style="font-size: 16px;" class="lg-icon lg-icon-history lg-color-primary"></i>
              <span>{{ $t('loc.lessonVersionHistory') }}</span>
            </div>
          </el-button>
          <!-- 复制课程按钮 -->
          <lesson-replicate :lesson-id="lesson.id" :submodule="submodule" style="margin-left: 0;" />
          <!-- 编辑按钮 -->
          <el-button icon="el-icon-edit" plain type="primary" @click="lessonEditClickHandler(lesson)" size="small"
            class="edit-button" v-if="submodule === submodules[0] && lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM'">
            {{ $t('loc.edit') }}
          </el-button>
          <lesson-template-select-modal v-if="showLessonTemplate(lesson.ages, lesson.activityType) && lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM'"
            :lessonAges="lesson.ages" style="width: auto;" buttonSize="small" :inDialog="true"
            :showGuidePopover="true" v-model="lesson.templateType" :lessonId="lesson.id"
            :isMyLesson="isOwnerLesson(lesson)" redirectRoute="EditLesson" />
          <!-- adapt 按钮 -->
          <adapt-lesson-button v-if="lesson && lesson.status !== 'DRAFT'" ref="adaptLessonButton" :adapt-lesson-id="lesson.id" @lessonAdapt="handleLessonAdapt" :is-lesson-detail="true" />
          <lesson-download @click.native="handleDownloadClick" :lesson-id="lesson.id" :lesson-name="lesson.name" :submodule="submodule"
            :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id" :lesson="lesson" />
        </template>
        <template slot-scope="{lesson}" v-if="submodule === submodules[2]">
          <lesson-info :lesson="lesson" />
        </template>
      </lesson-detail>
      <div slot="footer" v-if="submodule === submodules[2]" style="text-align: right">
        <i class="el-icon-delete" style="cursor: pointer" @click="lessonDeleteClickHandler(lesson)"></i>
        <el-button type="primary" icon="el-icon-edit" @click="lessonEditClickHandler(lesson)" size="medium">
          {{ $t('loc.edit') }}
        </el-button>
      </div>
      <!-- 课程历史抽屉 -->
      <lesson-history-drawer ref="historyDrawer" :visible.sync="showHistoryDrawer" :lesson-id="lessonId"
        :is-from-library="isFromLibrary" :key="lessonId" />
    </lesson-detail-dialog>
    <!-- 校训弹窗 -->
    <learner-profile-popup v-if="initComponents" :is-lesson-assistant="true" ref="learnerProfilePopupRef"/>
  </div>
</template>
<script>
import tools from '@/utils/tools'
import LessonCard from '@/views/modules/lesson2/lessonLibrary/components/LessonCard'
import LessonCardOperationIcon from '@/views/modules/lesson2/lessonLibrary/components/LessonCardOperationIcon'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import { mapState } from 'vuex'
import Api from '../../../../../api/lessons2/index'
import LessonList from '../../component/lessonList'
import CreateLessonEmpty from '../components/CreateLessonEmpty'
// import FilterControllerButton from '../components/FilterControllerButton.vue'
import LessonDetail from '../components/LessonDetail'
import LessonDetailDialog from '../components/LessonDetailDialog'
import LessonDownload from '../components/LessonDownload'
import LessonEmpty from '../components/LessonEmpty'
// import LessonFavorite from '../components/LessonFavorite'
import LessonInfo from '../components/LessonInfo'
import LessonItemDeletedMask from '../components/LessonItemDeletedMask'
// import LessonLike from '../components/LessonLike'
import LessonPromote from '../components/LessonPromote'
// import LessonReadCount from '../components/LessonReadCount'
import LessonReplicate from '../components/LessonReplicate'
import mappedMeasureExplanation from '../components/MappedMeasureExplanation'
import NewTab from '../components/NewTab'
import RecycleLesson from '../Recycle/RecycleLesson'
import LessonHistoryDrawer from '@/views/modules/lesson2/lessonLibrary/components/LessonHistoryDrawer.vue'
import AdaptLessonButton from '@/views/modules/lesson2/lessonPlan/components/AdaptLessonButton.vue'

export default {
  name: 'TeacherLesson',
  props: [
    'params',
    'submoduleName',
    'submoduleProps',
    'showFilter',
    'keyword',
    'isShowCreateLessonButton'
  ],
  components: {
    mappedMeasureExplanation,
    LessonCardOperationIcon,
    LessonDetailDialog,
    LessonItemDeletedMask,
    LessonInfo,
    LessonPromote,
    NewTab,
    LessonReplicate,
    // LessonFavorite,
    // LessonLike,
    // LessonReadCount,
    LessonDetail,
    RecycleLesson,
    LessonList,
    LessonCard,
    LessonEmpty,
    LessonDownload,
    LessonTemplateSelectModal,
    CreateLessonEmpty,
    // FilterControllerButton,
    LessonHistoryDrawer,
    AdaptLessonButton,
    LearnerProfilePopup: () => import('@/views/modules/lesson2/unitPlanner/components/learnerProfile/LearnerProfilePopup.vue')
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser, // 当前用户
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
    }),
    // 是否显示课程模板功能
    showLessonTemplate () {
      return function (ages, activityType) {
        return tools.showLessonTemplate(ages, activityType, this.eduProtocolsTemplateApplyOpen)
      }
    },
    displayLessonsLoader () {
      if (this.submodule === this.submodules[0]) {
        return this.getMyLessons
      } else if (this.submodule === this.submodules[1]) {
        return this.getMyFavoriteLessons
      } else if (this.submodule === this.submodules[2]) {
        return this.getMyLessonDraft
      }
    }
  },
  data () {
    return {
      submodule: this.submoduleName || 'Create',
      lessonId: '',
      submodules: ['Create', 'Favorite', 'Draft'],
      lesson: null,
      showDetail: false,
      isDelete: false,
      agencyStatus: '',
      reset: 0,
      lessonShareable: !this.submoduleName || this.submoduleName === 'Create',
      cardLesson: null,
      isFromLibrary: true,
      isUseAdaptedUDLAndCLR: true, // 用于判断是否使用过 UDL 和 CLR
      isFilterActive: false, // 控制过滤器显示状态
      isFirstVisit: true, // 添加标记，用于记录是否是第一次访问
      showHistoryDrawer: false, // 控制历史版本抽屉显示状态
      initComponents: false // 控制校训弹窗显示状态
    }
  },
  provide () {
    return {
      lessonShareable: this.lessonShareable
    }
  },
  watch: {
    showDetail () {
      if (!this.showDetail) {
        // 我创建的课程
        if (this.submodules[0] === this.submodule && this.isDelete) {
          this.$refs.lessonList.reload()
          this.isDelete = false
        }
      }
    },
    submoduleName (newValue) {
      this.submodule = this.submoduleName || 'Create'
      this.lessonShareable = this.submodule === 'Create'
      this.reset = new Date().getTime()
      if (newValue === 'Draft') {
        // 草稿页面曝光埋点
        this.$analytics.sendEvent('cg_lesson_plan_draft_exposure')
      }
    }
  },
  methods: {
    // 打开新增校训弹窗
    manageLearnerProfile() {
      this.$refs.learnerProfilePopupRef && this.$refs.learnerProfilePopupRef.open()
    },
    /**
     * 接收课程列表组件传递的总数并向父组件传递
     */
    onTotalUpdate (total) {
      if (this.submodule === this.submodules[0]) {
        this.$emit('update-create-total', total)
      }
    },
    /**
     * 点击下载
     */
    handleDownloadClick() {
      // 我的课程下载点击埋点
      this.$analytics.sendEvent('cg_lesson_plan_my_creation_click_down')
    },
    /**
     * 点击草稿课程卡片右上角三个点
     */
    handleDropdownClick() {
      // 草稿课程卡片右上角三个点点击埋点
      this.$analytics.sendEvent('cg_lesson_plan_draft_more')
    },
    /**
     * 搜索框聚焦
     */
    handleSearchFocus() {
      // 搜索框点击埋点
      if (this.submodule === this.submodules[0]) {
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_search')
      } else if (this.submodule === this.submodules[2]) {
        this.$analytics.sendEvent('cg_lesson_plan_draft_search')
      }
    },
    handleDraftClick() {
      this.checkTag(this.submodules[2])
    },

    // 判断是否是自己的课程
    isOwnerLesson (lesson) {
      return (lesson.createUserId === this.currentUser.user_id)
    },

    /**
     * 刷新课程列表
     */
    reloadLessons () {
      this.$refs.lessonList.reload()
    },

    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },

    checkTag ({ name: tagName }) {
      if (this.submodule === this.submodules[1]) {
        this.$analytics.sendEvent('web_lesson_library_my_less_favorite')
      } else if (this.submodule === this.submodules[2]) {
        this.$analytics.sendEvent('web_lesson_library_my_less_click_draft')
      }
      this.submodule = tagName
      this.params.keyword = ''
      this.params.orderKey = 'UPDATE_TIME'
      this.$emit('update:params', this.params)
      this.$router.push({ name: this.submodule })
    },
    closeThis () {
      this.showDetail = !this.showDetail
    },
    getMyLessons (param) {
      return Api.getMyLessons({ ...param, status: 'PUBLISHED', isAdaptedLesson: '' })
    },
    getMyLessonDraft (param) {
      return Api.getMyLessons({ ...param, status: 'DRAFT', isAdaptedLesson: '' })
    },
    getMyFavoriteLessons (param) {
      return Api.getMyFavoriteLessons({ ...param, isAdaptedLesson: '' })
    },
    lessonClickedHandler (lesson) {
      // 课程卡片点击埋点
      if (this.submodule === this.submodules[0]) {
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_click_crad')
      } else if (this.submodule === this.submodules[2]) {
        this.$analytics.sendEvent('cg_lesson_plan_draft_card')
      }


      this.$analytics.sendEvent('web_lesson_library_my_less_click_crad')
      window.lessonVideo && window.lessonVideo.pause()
      if (this.submodule === this.submodules[2]) {
        this.$router.push({
          name: 'EditLesson',
          params: {
            lessonId: lesson.id,
            type: 'Draft'
          }
        })
      } else {
        this.showDetail = true
        this.cardLesson = lesson
        this.lessonId = lesson.id
        this.lesson = lesson
      }
    },
    lessonEditClickHandler (lesson) {
      // 编辑课程按钮点击埋点
      if (this.submodule === this.submodules[0]) {
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_eidt')
      } else if (this.submodule === this.submodules[2]) {
        this.$analytics.sendEvent('cg_lesson_plan_draft_edit')
      }
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: lesson.id,
          type: 'Draft'
        }
      })
    },
    // adapt 弹框设置完成后进入 Lesson 编辑页面
    handleLessonAdapt() {
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: this.lessonId,
          type: 'Draft',
          adaptLesson: true
        }
      })
    },
    lessonDeleteClickHandler (lesson) {
      // 删除课程按钮点击埋点
      if (this.submodule === this.submodules[0]) {
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_click_delete')
      } else if (this.submodule === this.submodules[2]) {
        this.$analytics.sendEvent('cg_lesson_plan_draft_del')
      }
      this.$analytics.sendEvent('web_lesson_library_my_less_click_delete')
      // 获取课程推荐状态
      Api.getLessonRecommendStatus(lesson.id).then(res => {
        this.agencyStatus = res.agencyStatus
        // 提示信息
        let tipsContent = this.agencyStatus === 'PROMOTED' ? this.$t('loc.lessons2DeleteStatusAgencyTips') : this.$t('loc.lessons2DeleteTips')
        const h = this.$createElement
        this.$msgbox({
          title: 'Confirmation',
          message: h('p', null, [
            h('span', null, tipsContent)
          ]),
          showCancelButton: true,
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          confirmButtonClass: 'el-button--danger',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              if (instance.confirmButtonLoading) {
                return
              }
              instance.confirmButtonLoading = true
              // 删除课程
              Api.deleteLesson(lesson.id).then(() => {
                done()
                this.$message({
                  type: 'success',
                  message: 'Lesson was deleted successfully!'
                })
                this.$refs.lessonList.reload()
                this.showDetail = false
                this.isDelete = true
              }).finally(() => {
                setTimeout(() => {
                  instance.confirmButtonLoading = false
                }, 500)
              })
            } else {
              done()
            }
          }
        })
      })
    },
    lessonFavoriteHandler (favorite) {
      if (!favorite) {
        this.$refs.favoriteLessonList.reload()
      }
    },
    lessonRecycledHandler (restored) {
      restored && this.$refs.lessonList.reload()
    },
    favoriteLessonRemovedHandler () {
      this.$refs.favoriteLessonList.reload()
    },
    handleUpdate (detailLesson, attr, value) {
      if (attr === 'likeCount') {
        Object.assign(this.cardLesson,value)
      }
      if (attr === 'favoriteCount') {
        Object.assign(this.cardLesson,value)
      }
    },
      /**
       * 课程内容渲染后来自子组件的回调，用以判断是否使用了 Adapt UDL and CLR
       */
      lessonDetailMountedAfter() {
          if (this.$refs.lessonDetail) {
              // 判断是否启用了 UDL 和 CLR
              this.isUseAdaptedUDLAndCLR = this.$refs.lessonDetail.isUseAdaptedUDLAndCLR;
          }
      },
    /**
     * 新建课程
     */
    newLessonButtonClickHandler(type) {
      // 创建课程点击埋点
      if (type === 'button') {
        // 搜索框旁的按钮
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_create')
      }
      this.$analytics.sendEvent('web_lesson_library_my_less_add')
      this.$router.push({ name: 'AddLesson' })
    },
    showCreateLessonEmpty (empty) {
      // 不为空/不是 my lesson
      if (!empty || this.submodule !== this.submodules[0]) {
        return false
      }

      // 有过滤条件
      for (const k in this.params) {
        if (k !== 'orderKey' && this.params[k] && this.params[k].length > 0) {
          return false
        }
      }

      // 只在第一次访问时关闭过滤器
      if (this.isFirstVisit && this.showFilter) {
        this.$emit('toggle-filter')
        this.isFirstVisit = false
      }

      return true
    },
    handleSearch(value) {
      this.$emit('search-input', value)
    },
    // 打开历史抽屉
    openHistory () {
      if (!this.lessonId) {
        this.$message.error('Unable to view history version')
        return
      }
      this.showHistoryDrawer = true
    }
  },
  mounted() {
    // 组件挂载后，设置 isFilterActive 的初始值为 false（与父组件中 shouldHideFilter: true 对应）
    this.isFilterActive = false
    this.initComponents = true
  },
}
</script>
<style scoped lang="less">
.teacher-lesson-tabs /deep/ & {
  line-height: 50px;

  & > :nth-child(1), & > :nth-child(2), & > :nth-child(3) {
    margin-left: 0;
  }
}

.lesson-card-operations {
  font-weight: bold;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.8);
}

.el-button-sec-tab {
  background-color: rgba(220, 223, 230, 100);
  color: rgba(103, 104, 121, 100);
  border: none;
  border-radius: 4px !important;
}

.el-button-group-sec-tab {
  display: inline-flex;
  border-radius: 4px !important;
  padding: 4px !important;
  background-color: rgba(220, 223, 230, 100);
}
.edit-button{
  margin-left: 0;
  font-size: 14px;
  font-weight: 600;
  color: #10b3b7;
}

/deep/ .lesson-detail-header-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: end;
}

/* 新增的按钮样式 */
.button-group {
  display: flex;
  gap: 10px;
  height: 42px;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

.hidden-md-and-down-lesson {
  /deep/.el-input__inner {
    border-radius: 100px;
  }
}

.hidden-lg-and-up-lesson {
  /deep/.el-input__inner {
    border-radius: 100px;
  }
}

@media only screen and (max-width: 980px) {
  .hidden-md-and-down-lesson {
    display: none!important;
  }
}

@media only screen and (min-width: 980px) {
  .hidden-lg-and-up-lesson {
    display: none!important;
  }
  .hidden-md-and-down-lesson {
    display: block!important;
  }
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  margin-left: -10px;
  margin-right: -10px;
  background-color: #f5f6f8;
  padding: 10px;
  margin-bottom: 10px;
  transition: all 0.3s ease;

  &.is-sticky {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.create-lesson-button {
  overflow: hidden;
  transition: all 0.8s ease;
  width: 200px;

  @media screen and (max-width:1199px) {
    width: 50px;

    .create-lesson-button-text {
      display: none;
    }
  }
}
.search-box-container {
    flex: 1;
    width: 100%;
    min-width: 270px;
    text-align: end;
  }
.lesson-list-container {
  height: 100%;
}
</style>
