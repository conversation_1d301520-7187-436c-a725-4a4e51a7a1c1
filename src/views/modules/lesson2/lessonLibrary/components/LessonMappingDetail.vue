<template>
  <div style="padding-left: 30px;" class="lesson-detail-ccss">
    <div v-if="(showCoreMeasure && measures.length === 0)">{{ $t('loc.noCoreMeasureTip') }}</div>
    <div v-else>
      <el-row v-for="(domainAndMapped,domainIndex) in domainMappedMeasuresList" :key="domainIndex" class="add-padding-t-10">
        <!-- 显示domain全称 -->
        <el-row style="margin-bottom: 5px;margin-left: -10px">
          <el-col v-if="domainAndMapped.mappedInfo.length !== 0" v-html="domainAndMappedDomainInfoDisplayText(domainAndMapped)">
          </el-col>
          <!-- <el-col v-else>
            当前没有核心测评点，请关闭核心测评点开关，查看全部测评点
          </el-col> -->
        </el-row>
        <!-- DRDP 显示区域 -->
        <el-row style="width: 48%;float: left"
                v-for="(otherMeasure,index) in domainAndMapped.mappedInfo"
                :key="otherMeasure.id"
                :class="{marginRight: index%2===0}">
          <!--存在映射关系-->
          <div v-if="otherMeasure.measures.length > 0">
            <div class="lesson-detail-ccss-box">
              <!-- DRDP 名称及简称 -->
              <el-row class="lesson-detail-ccss-title" style="margin-bottom: 5px">
                <el-col v-html="measureDisplayText(otherMeasure.drdpMeasure)">
                  <span style="font-size:16px">{{ otherMeasure.drdpMeasure.abbreviation }}</span>
                  <span style="font-size:16px"> {{ otherMeasure.drdpMeasure.name }}</span>
                  <span v-show="coreMeasure[otherMeasure.drdpMeasure.name]" style="color: red">*</span>
                </el-col>
              </el-row>
              <el-row v-if="lesson.mappedMeasures && mappedFrameworkId">
                <!-- 映射关系区域 -->
                <el-col>
                  <div class="lesson-info-detail-box" :style="{ 'overflow': otherMeasure.show ? 'visible' : 'hidden', 'height': otherMeasure.show ? 'fit-content' : '29px' }">
                    <div v-for="mappedMeasure in otherMeasure.measures" :key="mappedMeasure.id"
                        class="lesson-detail-ccss-tag" style="display: flex">
                      <el-tooltip :open-delay="Number(300)" effect="dark" placement="top">
                        <div slot="content" style="width: 200px;font-size: 14px;white-space: pre-wrap;">{{ mappedMeasure.description }}</div>
                        <lesson-mapped-measure :measure="mappedMeasure"/>
                      </el-tooltip>
                    </div>
                  </div>
                  <!-- 显示更多或更少区域 -->
                  <div class="lesson-detail-ccss-load" v-if="otherMeasure.measures.length > defaultCount">
                    <el-link :underline="false" type="primary" @click="showMore(domainIndex,index)"
                            class="fixed-show-more-or-less">
                      {{ domainMappedMeasuresList[domainIndex].mappedInfo[index].show ? $t('loc.lessons2ShowLess') : $t('loc.lessons2ShowMore') }}
                      <el-icon class="el-icon-arrow-up"
                              v-if="domainMappedMeasuresList[domainIndex].mappedInfo[index].show"></el-icon>
                      <el-icon class="el-icon-arrow-down" v-else></el-icon>
                    </el-link>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          <!--无映射关系-->
          <div v-else>
            <!-- DRDP 名称及简称 -->
            <el-row class="lesson-detail-ccss-title">
              <el-col v-html="measureDisplayText(otherMeasure.drdpMeasure)">
                <span style="font-size:16px">{{ otherMeasure.drdpMeasure.abbreviation }}</span>
                <span style="font-size:16px"> {{ otherMeasure.drdpMeasure.name }}</span>
                <span v-show="coreMeasure[otherMeasure.drdpMeasure.name]" style="color: red">*</span>
              </el-col>
            </el-row>
          </div>
        </el-row>
      </el-row>
    </div>
  </div>
</template>
<script>
import LessonMappedMeasure from '@/views/modules/lesson2/lessonLibrary/components/LessonMappedMeasure'
import LessonMappingBase from '@/views/modules/lesson2/lessonLibrary/components/LessonMappingBase'

export default {
  name: 'LessonMappingDetail',
  mixins: [LessonMappingBase],
  components: { LessonMappedMeasure },
  props: ['lesson', 'mappedFrameworkId', 'plfMeasuresList', 'showCoreMeasure'],
  computed: {
    // 显示 domain 全称
    domainAndMappedDomainInfoDisplayText() {
      return (domainAndMapped) => {
        // domainAndMapped 为空
        if (!domainAndMapped) {
          return ''
        }
        // 获取 domainInfo
        let domainInfo = domainAndMapped.domainInfo
        if (!domainInfo) {
          return ''
        }
        // 获取 domainInfo 缩写和名称
        let abbreviation = domainInfo.abbreviation
        let name = domainInfo.name
        // 如果全称不存在，那么 name 就重新赋值为 description
        if (!name || name.trim() === '') {
          name = domainInfo.description
        }
        // domainInfo 显示文本
        let domainInfoText = ''
        // 如果缩写和名称都存在，那么就展示缩写和名称
        if (abbreviation && name && abbreviation.trim() !== '' && name.trim() !== '') {
          // 如果 name 和 abbr 相等，那么只展示一个
          if (abbreviation.trim() === name.trim()) {
            domainInfoText = abbreviation
          } else {
            domainInfoText = `${abbreviation}-${name}`
          }
        } else if (abbreviation) {
          // 如果缩写存在，那么就展示缩写
          domainInfoText = abbreviation
        } else if (name) {
          // 如果名称存在，那么就展示名称
          domainInfoText = name
        }
        // 返回 domainInfo 显示文本
        return  `<span class="lesson-field-value font-bold"> ${domainInfoText} </span>`
      }
    },
    // 测评点显示文本
    measureDisplayText () {
      return (measure) => {
        // 测评点为空
        if (!measure) {
          return ''
        }
        // 获取测评点缩写和测评点名称
        let abbreviation = measure.abbreviation
        let name = measure.name
        // 如果全称不存在，那么 name 就重新赋值为 description
        if (!name || name.trim() === '') {
          name = measure.description
        }
        // 测评点显示文本
        let measureText = ''
        // 测评点缩写和名称都存在
        if (abbreviation && name && abbreviation.trim() !== '' && name.trim() !== '') {
          // 测评点显示文本为缩写-名称
          measureText = `${abbreviation}-${name}`
        } else if (abbreviation) {
          // 测评点显示文本为缩写
          measureText = abbreviation
        } else if (name) {
          // 测评点显示文本为名称
          measureText = name
        }
        if (measureText !== '') {
          measureText = `<span style="font-size:16px">${measureText}</span>`
        }
        // 如果是核心测评点，添加核心测评点标识
        if (this.coreMeasure[name]) {
          measureText = `${measureText} <span style="color: red">*</span>`
        }
        // 返回测评点显示文本
        return measureText
      }
    }
  }
}
</script>
