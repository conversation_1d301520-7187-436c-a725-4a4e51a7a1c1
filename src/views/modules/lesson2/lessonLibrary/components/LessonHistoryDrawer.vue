<template>
  <div class="lesson-history-drawer" :style="{ zIndex: isFromLibrary ? 10000 : 1000 }" v-if="visible"
   :class="{ 'show': visible, 'from-library': isFromLibrary}">
    <div class="drawer-mask" :style="{ zIndex: isFromLibrary ? 10001 : 1001 }"></div>
    <div class="drawer-content" :style="{ zIndex: isFromLibrary ? 10002 : 1002 }" v-loading="restoreLoading" :element-loading-text="$t('loc.lessonRestoreLoadingText')">
      <div class="drawer-header">
        <div class="drawer-title">{{ $t('loc.lessonPlanHistory') }}</div>
        <div class="drawer-close" @click="handleClose">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <lesson-empty v-if="historyList.length === 0 && !isLoadingMore" :tip="$t('loc.noLessonHistory')"/>
      <div class="drawer-body" v-else>
        <div class="history-container" :class="{'collapsed': !showHistoryList}">
          <!-- 左侧课程详情 -->
          <div class="lesson-detail-container" ref="lessonDetailContainerRef">
            <div class="lesson-detail-content">
              <!-- 课程详情组件 -->
              <lesson-detail
                :versionId="selectedHistoryId"
                :isDialog="true"
                :isFromLibrary="true"
                :key="selectedHistoryId"
              />
            </div>
          </div>

          <!-- 控制历史列表显示的按钮 -->
          <div v-show="!restoreLoading" class="toggle-history-btn" :style="{ zIndex: isFromLibrary ? 10003 : 2003 }" @click="toggleHistoryList">
            <i :class="showHistoryList ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"></i>
          </div>

          <!-- 右侧历史列表 -->
          <div class="history-list-container">
            <div class="history-list-title">
              <span>{{ $t('loc.lessonVersionHistory') }}</span>
            </div>
            <div ref="historyListRef" class="history-list" infinite-scroll-immediate="true" v-infinite-scroll="loadMoreHistory" infinite-scroll-distance="10" v-loading="isLoadingMore && page === 1">
              <template>
                <lesson-history-item
                  v-for="(item, index) in historyList"
                  :key="index"
                  :history-item="item"
                  :is-current="index === 0"
                  :is-active="selectedHistoryId === item.id"
                  @restore="handleRestore(item)"
                  @click="handleSelectHistory(item)"
                />
                <!-- 加载更多提示 -->
                <div v-if="isLoadingMore && page !== 1" class="loading-more-container" v-loading="isLoadingMore">
                </div>
                <div v-else-if="!hasMore && historyList.length > 10" class="no-more">
                  <span>{{ $t('loc.noMoreLessonHistory') }}</span>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LessonHistoryItem from './LessonHistoryItem.vue'
import LessonDetail from './LessonDetail.vue'
import Lesson2 from '@/api/lessons2'
import LessonEmpty from './LessonEmpty.vue'

export default {
  name: 'LessonHistoryDrawer',
  components: {
    LessonHistoryItem,
    LessonDetail,
    LessonEmpty
  },
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 课程 ID
    lessonId: {
      type: String,
      default: ''
    },
    // 是否是从课程库中的课程卡片预览弹窗进入
    isFromLibrary: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      historyList: [],
      selectedHistoryId: null,
      page: 1,
      pageSize: 10,
      hasMore: true,
      isLoadingMore: false,
      restoreLoading: false,
      showHistoryList: true
    }
  },
  watch: {
    // 监听 visible 的变化
    visible(val) {
        if (val) {
          // 初始化所有信息
          this.resetAllState()
          this.loadMoreHistory()
      }
    }
  },
  methods: {
    // 切换历史版本列表显示隐藏的状态
    toggleHistoryList() {
      this.showHistoryList = !this.showHistoryList;
    },
    // 关闭历史版本抽屉
    handleClose() {
      if(this.restoreLoading) {
        return
      }
      this.$emit('update:visible', false)
    },
    // 恢复历史版本
    handleRestore(item) {
      // 显示确认弹窗
      this.$confirm(`${this.$t('loc.lessonRestoreDesc')} ${this.$moment.utc(item.date).local().format('MM/DD/YYYY, h:mm A')}.`, this.$t('loc.lessonRestoreDialogTitle'), {
        confirmButtonText: this.$t('loc.lessonRestore'),
        cancelButtonText: this.$t('loc.cancel')
      }).then(() => {
        if (this.$route.name === 'AddLesson') {
          // 创建课程恢复历史版本点击埋点
          this.$analytics.sendEvent('cg_lesson_plan_cre_click_pop_res')
        } else if (this.$route.name === 'EditLesson') {
          // 编辑课程恢复历史版本点击埋点
          this.$analytics.sendEvent('cg_lesson_plan_edit_click_pop_res')
        }
        // 如果是预览弹窗中进行 restore，则跳转编辑页
        if (this.isFromLibrary) {
          // 跳转到编辑页面携带 versionId
          this.$router.push({
            name: 'EditLesson',
            params: {
              lessonId: this.lessonId
            },
            query: {
              versionId: item.id
            }
          })
          return
        }
        // 设置恢复加载状态
        this.restoreLoading = true

        // 调用API获取历史版本的课程详情
        Lesson2.getLessonVersionDetail(item.id)
          .then(data => {
            try {
              // 创建一个回调函数，在恢复操作完成后调用
              const onRestoreComplete = () => {
                // 显示成功消息
                this.$message.success(this.$t('loc.lessonRestoreSuccess'))
                this.selectedHistoryId = null
                // 关闭加载状态
                this.restoreLoading = false
                // 关闭抽屉
                this.handleClose()
              }
              // 监听一次性事件
              this.$once('restore-complete', onRestoreComplete)
              // 触发恢复事件，并传递版本ID
              this.$emit('restore', data, item.id)
            } catch (error) {
              // 关闭加载状态
              this.restoreLoading = false
            }
          })
          .catch(error => {
            this.restoreLoading = false
          })
      }).catch(() => {
        // 用户取消恢复操作
      })
    },
    // 选择历史版本
    handleSelectHistory(item) {
      if (this.lessonId === item.id) {
        return;
      }
      // 设置选中的历史记录ID
      this.selectedHistoryId = item.id
      // 滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.lessonDetailContainerRef) {
          this.$refs.lessonDetailContainerRef.scrollTop = 0
        }
      })
    },
    // 加载更多历史版本
    loadMoreHistory() {
      // 如果没有更多历史版本、正在加载更多历史版本、历史版本抽屉未打开，则返回
      if (!this.hasMore || this.isLoadingMore || !this.visible) return
      // 设置加载更多历史版本的状态
      this.isLoadingMore = true
      // 如果 lessonId 为空，则无法加载更多历史版本
      if (!this.lessonId) {
        this.isLoadingMore = false
        return
      }
      // 调用API获取更多历史版本
      Lesson2.getLessonVersions(this.lessonId, this.page, this.pageSize)
        .then(async data => {
          // 处理返回的数据
          let newItems = data.versions.map(item => {
            return {
              id: item.versionId,
              date: item.versionTime,
              isCurrent: this.page === 1 && data.versions.indexOf(item) === 0,
              description: item.description?item.description.updateDetails || '' : '',
              sourceVersionTime: item.description?item.description.sourceVersionTime : ''
            }
          })
          // 过滤掉没有时间和描述的记录
          newItems = newItems.filter(item => item.date && item.description)
          // 添加到列表中
          this.historyList = [...this.historyList, ...newItems]
          // 默认选中第一个历史记录（当前版本）
          if (this.historyList.length > 0 && !this.selectedHistoryId && this.lessonId) {
            // 否则加载第一个历史记录
            this.handleSelectHistory(this.historyList[0])
          }
          // 更新分页信息
          this.page += 1
          this.hasMore = data.hasMore
          // 当 histtoryRef 高度没有出现滚动条时，继续加载
          // await nextTick()
          // const historyListRef = this.$refs.historyListRef
          // 解决首次加载历史版本时，历史列表高度没有出现滚动条，导致无法加载更多历史版本的问题
          // if (historyListRef.scrollHeight <= historyListRef.clientHeight && this.hasMore) {
          //   this.isLoadingMore = false
          //   this.loadMoreHistory()
          // }
        })
        .catch(error => {
        }).finally(() => {
          this.isLoadingMore = false
        })
    },
    // 重置所有状态
    resetAllState() {
      // 完全重置所有状态
      this.selectedHistoryId = null
      this.historyList = []
      // 重置分页状态
      this.page = 1
      this.hasMore = true
      this.isLoadingMore = false
    },
  }
}
</script>

<style lang="scss" scoped>
/deep/ .lg-box-shadow {
  box-shadow: none;
}
.lesson-history-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;

  // 加载更多容器样式
  .loading-more-container {
    height: 60px;
    margin: 10px 0;
  }

  /deep/ .el-loading-text {
    color: #111C1C;
    font-size: 16px;
  }

  // 添加历史列表切换按钮样式
  .history-container {
    display: flex;
    height: 100%;
    overflow: hidden;
    position: relative;

    &.collapsed {
      .history-list-container {
        width: 0;
        min-width: 0;
        border-left: none;
        padding: 0;
        margin: 0;
        opacity: 0;
      }

      .toggle-history-btn {
        right: 0;
        border-radius: 0 0 0 4px;
        border-right: 1px solid #ebeef5;
        border-left: 1px solid #ebeef5;
      }

      .lesson-detail-container {
        padding: 50px 50px 20px 50px;
      }
    }
  }

  .toggle-history-btn {
    position: absolute;
    right: 360px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 60px;
    background-color: #f5f7fa;
    border: 1px solid #ebeef5;
    border-right: none;
    border-radius: 4px 0 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #ddf2f3;

      i {
        color: var(--color-primary);
      }
    }

    i {
      font-size: 16px;
      color: #606266;
      transition: all 0.3s ease;
    }
  }

  .drawer-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }

  .drawer-content {
    position: absolute;
    bottom: -100%;
    left: 0;
    width: 100%;
    height: calc(100vh - 40px);
    background-color: #fff;
    transition: bottom 0.3s;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
  }

  &.show {
    pointer-events: auto;

    .drawer-mask {
      opacity: 1;
      pointer-events: auto;
    }

    .drawer-content {
      bottom: 0;
    }
  }

  .drawer-header {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .drawer-title {
      font-size: 20px;
      font-weight: 600;
      color: #111C1C;
    }

    .drawer-close {
      cursor: pointer;
      font-size: 20px;
      color: #909399;

      &:hover {
        color: var(--color-primary);
      }
    }
  }

  .drawer-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .lesson-detail-container {
    flex: 1;
    overflow-y: auto;
    padding: 0px 10px 10px 10px;
    background-color: #fff;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 4px;
        transition: all 0.3s ease;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #c0c4cc;
      }

    .lesson-detail-content {
      background-color: #fff;
      border-radius: 8px;
    }
  }

  .history-list-container {
    width: 360px;
    min-width: 320px;
    border-left: 1px solid #ebeef5;
    display: flex;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    overflow: hidden;
    opacity: 1;

    .history-list-title {
      padding: 16px;
      font-size: 18px;
      font-weight: 600;
      color: #111C1C;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .history-list {
      flex: 1;
      overflow-y: auto;
      padding: 12px;
      display: flex;
      flex-direction: column;
      gap: 0px;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 4px;
        transition: all 0.3s ease;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #c0c4cc;
      }

      .no-more {
        color: #c0c4cc;
        padding: 12px 0;
        font-size: 13px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .toggle-history-btn {
      display: none;
    }

    .history-container {
      flex-direction: column-reverse;

      &.collapsed {
        .history-list-container {
          max-height: 0;
          padding: 0;
        }

        .lesson-detail-container {
          padding: 20px;
        }
      }
    }

    .drawer-header {
      .drawer-close {
        display: none;
      }
    }
  }
}
</style>
<style lang="scss">

</style>
