<template>
  <div style="padding: 0 60px;background-color: #fff" v-if="!lesson || lessonDetailDataLoading">
    <lesson-detail-skeleton/>
  </div>
  <div v-else class="lesson-detail lesson-detail-iPad lesson-detail-web" :key="lessonId">
    <div class="lesson-curriculum-name" v-if="curriculumName">
      {{curriculumName}}
    </div>
    <!--    如果是不隐藏面包屑且不是弹窗预览课程详情    -->
    <LanguageToggle v-if="!isDialog && !currentPageNoBreadCrumb && !isLessonReflection" class="language-toggle-not-dialog" v-model="currentContentLangCode" v-show="!profileOpenDropdown" :originalLangCode="originalLangCode"/>
    <!-- 详情页头部操作区-->
    <div style="margin: 1px 0px 24px 0px;">
      <div class="lesson-detail-header-group">
        <!--    如果是不隐藏面包屑且是弹窗预览课程详情,且非 unit planner 第二和第三步的单元预览中的课程预览    -->
        <LanguageToggle v-if="isDialog && (!currentPageNoBreadCrumb || $route.name === 'lessonCurriculum')
                        && ($route.name !== 'lessonOverview' && $route.name !== 'weeklyPlanOverview')
                        && ($route.name !== 'lesson-overview-cg' && $route.name !== 'edit-weekly-plan-overview-cg')
                        && ($route.name !== 'lesson-overview-cg-designer') && !isLessonHistoryPreview"
                        :class="curriculumName ? 'language-toggle-temp' : 'language-toggle'" class="remove-margin-l-0" :isLessonColor="true" v-model="currentContentLangCode" :originalLangCode="originalLangCode" :curriculumName="curriculumName"/>
        <div class="lesson-detail-header remove-margin-l-0">
          <div class="lesson-detail-header-left">
            <!--      是弹窗其没有面包屑且非 curriculum 的课程预览      -->
            <LanguageToggle v-if="isDialog && currentPageNoBreadCrumb && $route.name !== 'lessonCurriculum'" :isLessonColor="true" v-model="currentContentLangCode" :originalLangCode="originalLangCode"/>
            <slot name="header-left" :lesson="lesson"/>
          </div>
          <div class="lesson-detail-header-right mobile-header-right" :class="{ 'lesson-reflection-style': isLessonReflection }">
            <!--    如果是隐藏面包屑且不是弹窗预览课程详的情况    -->
            <LanguageToggle v-if="(!isDialog && currentPageNoBreadCrumb) || isLessonReflection" :isLessonColor="true" v-model="currentContentLangCode" :originalLangCode="originalLangCode"/>
            <slot ref="headerRight" name="header-right" :lesson="lesson" :mappedFrameworkId="mappedFrameworkId"/>
          </div>
        </div>
      </div>
    </div>
    <div class="lesson-detail__content">
      <slot :lesson="lesson">
        <!--详情页的内容主体-->
        <lesson-info-new v-if="isFromLibrary" :isLessonHistoryPreview="isLessonHistoryPreview" :lesson="lesson" :lessonItem="lessonItem" :scrollToGraduate="scrollToGraduate" :planPreview="planPreview" @getMappedFrameworkId="getMappedFrameworkId">
          <slot name="lesson-operation" :lesson="lesson" slot="lesson-operation"/>
        </lesson-info-new>
        <lesson-info v-if="!isFromLibrary" :lesson="lesson" :planPreview="planPreview" @getMappedFrameworkId="getMappedFrameworkId">
          <slot name="lesson-operation" :lesson="lesson" slot="lesson-operation"/>
        </lesson-info>
        <!-- 课程反思 -->
        <reflection-detail-list :excludePlanId="planId" :lessonId="lesson.id" :subList=true :showHeader=true v-if="!hideReflection"></reflection-detail-list>
        <!--评论区-->
        <lesson-comment v-if="!isLessonHistoryPreview" :lesson-id="lesson.id" :lesson-create-user-id="lesson.createUserId" :disabled="disabled"/>
      </slot>
    </div>
    <!--课程详情弹窗提示-->
    <div>
      <slot name="detail-tips" :lesson="lesson"></slot>
    </div>
  </div>
</template>

<script>
import Api from '@/api/lessons2/index'
import LessonInfo from './LessonInfo'
import LessonComment from './LessonComment'
import Loading from '../../component/loading'
import ReflectionDetailList from '../../lessonPlan/components/ReflectionDetailList'
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton'
import tools from '@/utils/tools'
import LessonInfoNew from '@/views/modules/lesson2/lessonLibrary/components/LessonInfoNew'
import LanguageToggle from '@/views/modules/lesson2/component/LanguageToggle.vue'
import { mapState } from 'vuex'

export default {
  name: 'LessonDetail',
  components: {
    LessonInfoNew,
    Loading,
    LessonComment,
    LessonInfo,
    ReflectionDetailList,
    LessonDetailSkeleton,
    LanguageToggle
  },
  props: ['lessonId', 'disabled', 'planId', 'planPreview', 'hideReflection', 'isFromLibrary', 'submodule', 'isDialog', 'itemId','scrollToGraduate', 'versionId'],
  data () {
    return {
      lesson: null,
      mappedFrameworkId: true,
        mountedAfterFlag: false,
      currentContentLangCode: '', // 当前内容语言类型
      isNeedDetectLanguage: false, // 是否是识别语言状态
      lessonDetailDataLoading: false, // 课程详情加载状态
      isNoLastLevel: false, // 是否没有上一级，指的是在课程库或者周计划进入的课程预览
      previousLangCode: '', // 上一次的内容语言类型
      originalLangCode: '', // 源语言类型
      lessonItem: {} // 课程 Item
    }
  },
    created () {
      // 初始化为 false
      this.mountedAfterFlag = false
      // 如果当前内容源语言类型为空，说明是在课程库或者周计划进入的课程预览
      if (this.contentOriginalLanguage === '' && !this.versionId && this.lessonId) {
        this.isNoLastLevel = true
        // 识别获取内容的源语言类型
        this.getLessonDetail(true)
      } else {
        // 设置当前内容语言类型
        this.currentContentLangCode = this.contentLangCode
      }
      // 设置切换语言类型前的语言类型
      this.previousLangCode = this.currentContentLangCode
      // 获取课程 Item
      this.getItemByItemId()
    },
  beforeDestroy () {
    if (this.isNoLastLevel) {
      // 如果是在课程库或者周计划进入的课程预览，那么退出时清除 vuex 中的语言类型数据
      this.$store.commit('SET_ORIGINAL_LANGUAGE', '')
      this.$store.commit('SET_CURRENT_LANGUAGE', '')
    }
    this.$store.commit('SET_LESSON_DOWNLOAD_FILE_LANG_CODE', '')
    this.$store.commit('SET_LESSON_DOWNLOAD_FILE_ORIGINAL_LANG_CODE', '')
    // 清除当前课程详情的源语言码
    this.originalLangCode = ''
  },
  watch: {
      lessonId: {
        immediate: true,
        handler () {
          // 添加浏览量
          if(!this.versionId && this.lessonId) {
            Api.increaseLessonViewCount(this.lessonId)
          }
        }
      },
      currentContentLangCode (val) {
        // 如果是非识别语言操作导致的语言码变更，则进行翻译请求
        if (!this.isNeedDetectLanguage && !this.versionId && this.lessonId) {
          this.getLessonDetail(false, val, true)
        } else {
          this.isNeedDetectLanguage = false
        }
      },
      versionId: {
        immediate: true,
        handler (newVal, oldVal) {
          if (newVal) {
            this.getLessonVersionDetail()
          }
        }
      }
  },
    computed: {
        ...mapState({
          contentLangCode: state => state.translate.currentContentLangCode, // 当前内容语言类型
          contentOriginalLanguage: state => state.translate.originalContentLangCode, // 当前内容源语言类型
          profileOpenDropdown: state => state.common.profileOpenDropdown, // profile 的下拉框是否打开
          curriculumName: state => state.lesson.curriculumName, // curriculum 名称
        }),
        // 内容的当前语言是否与源语言相同
        isSameLanguage () {
          return this.currentContentLangCode === this.contentOriginalLanguage
        },
        // 当前是否不存在面包屑
        currentPageNoBreadCrumb () {
          return this.$route.meta.hideBreadCrumb
        },
        isUseAdaptedUDLAndCLR() {
            // 在mountedAfter之前由于没数据，默认有 UDL 和 CLR 以免用户误操作
            if (!this.mountedAfterFlag) {
                return true;
            }
            if (this.lesson) {
                if (this.lesson.steps) {
                    return this.lesson.steps.some(item => {
                        return !!item.culturallyResponsiveInstructionGroup || !!item.universalDesignForLearningGroup
                    })
                }
            }
            return false
        },
        // 判断是否反思课程
        isLessonReflection () {
          return this.$route.name === 'lesson-reflection'
        },
        // 是否是课程历史预览
        isLessonHistoryPreview () {
          return this.versionId? true : false
        }
    },
  methods: {
    // 根据课程查询 Item
    getItemByItemId() {
      if (!this.itemId || this.itemId === '') {
        return
      }
      let params = {
        params: {
          itemId: this.itemId
        }
      }
      this.$axios.get($api.urls().getItemById, params).then(res => {
        // 有数据
        if (res) {
          this.lessonItem = res
          // 如果有校训数据
          if (this.lessonItem.rubrics) {
            this.lessonItem.rubrics = JSON.parse(this.lessonItem.rubrics)
          }
        }
        this.loading = false
      })
    },
    getMappedFrameworkId (flag) {
      this.mappedFrameworkId = flag
    },
    // 获取课程详情
    getLessonDetail (needDetectLanguage, langCode = null, detectAndTranslate = false) {
      if (!this.lessonId) {
        return
      }
      const params = {
        lessonId: this.lessonId,
        ...((needDetectLanguage || detectAndTranslate) && { isDetect: needDetectLanguage || detectAndTranslate }),
        ...(((langCode && !this.isSameLanguage) || detectAndTranslate) && { langCode })
      }
      this.lessonDetailDataLoading = true
      // 获取课程详情
      Api.getLessonDetail(params).then((res) => {
        this.lesson = res
        // 如果需要识别语言类型且返回的语言码不为空
        if ((needDetectLanguage || detectAndTranslate) && res.langCode && res.langCode.trim() !== '') {
          // 设置当前内容的语言类型
          this.currentContentLangCode = res.langCode
          // 设置当前内容的源语言类型
          this.originalLangCode = res.langCode
          if (this.isNoLastLevel) {
            // 设置当前内容的源语言类型
            this.$store.commit('SET_ORIGINAL_LANGUAGE', this.currentContentLangCode)
            // 设置当前语言类型
            this.$store.commit('SET_CURRENT_LANGUAGE', this.currentContentLangCode)
          }
          if (detectAndTranslate) {
            this.$store.commit('SET_LESSON_DOWNLOAD_FILE_ORIGINAL_LANG_CODE', res.langCode)
            this.$store.commit('SET_LESSON_DOWNLOAD_FILE_LANG_CODE', res.langCode)
          }
          // 设置当前为识别语言状态
          this.isNeedDetectLanguage = !detectAndTranslate
        }
        // 如果传入了语言码
        if (langCode && langCode !== '') {
          // 设置当前内容语言类型
          this.currentContentLangCode = langCode
          // 设置下载文件的语言类型
          this.$store.commit('SET_LESSON_DOWNLOAD_FILE_LANG_CODE', langCode)
        }
        // 设置切换语言类型前的语言类型
        this.previousLangCode = this.currentContentLangCode
        this.$nextTick(() => {
          this.mountedAfter()
        })
        this.lessonDetailDataLoading = false
      }).catch(() => {
        // 如果翻译语言失败，提示失败
        if (langCode && !this.isSameLanguage) {
          this.$message.error(this.$t('loc.lessonDetailTranslateFailed'))
        }
        // 还原语言按钮的语言类型为切换语言失败前的语言类型
        this.currentContentLangCode = this.previousLangCode
        this.lessonDetailDataLoading = false
      })
    },
    isComeFromIpad () {
      return tools.isComeFromIPad()
    },
      /**
       * mounted 之后的钩子，在 课程加载完之后调用
       */
      mountedAfter() {
          // 将标志设置为 true
          this.mountedAfterFlag = true
          // 向外发送事件 mountedAfter
          this.$emit('mountedAfter')
      },
    getLessonVersionDetail() {
      // 如果没有 versionId，则不执行
      if (!this.versionId) {
        return
      }

      // 显示加载状态
      this.lessonDetailDataLoading = true

      // 调用API获取历史版本的课程详情
      Api.getLessonVersionDetail(this.versionId)
        .then(data => {
          this.lesson = data
          this.lessonDetailDataLoading = false
        })
        .catch(error => {
          this.lessonDetailDataLoading = false
          this.$message.error('Failed to get lesson version detail.')
        })
    }
  },
    mounted () {
      if (this.lesson) {
          this.$nextTick(() => {
              this.mountedAfter()
          })
      }
    },
}
</script>
<style scoped lang="less">
@media only screen and (max-width:1199px){
  .lesson-detail-iPad{
    padding: 0 25px;
  }
}
.language-toggle {
  position: absolute;
  top: -40px;
  margin-left: 0;
}
.language-toggle-temp {
  position: absolute;
  top: -48px;
  margin-left: 0;
  right: 90px;
}
@media only screen and (min-width:1200px){
  .lesson-detail-web{
    padding: 0 60px;
  }
}
.v-modal /deep/ &  {
  /* position: fixed; */
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000;
}
.lesson-detail /deep/ & {
  /* 头部按钮区 */

  & > :first-child {
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > div {
      display: flex;
      align-items: center;

      &:first-child{
        & > * {
          margin-right: 20px;
          margin-left: 0;
        }
      }
      &:last-child{
        & > * {
          margin-right: 0;
          margin-left: 10px;
        }
      }
    }
  }
}
.language-toggle-not-dialog {
  position: absolute;
  top: 67px;
  right: 28px;
  z-index: 1001;
}
.lesson-detail-header-group {
  display: flex;
  width: 100%;
  position: relative;
}
.lesson-detail-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.lesson-detail-header-left {
  display: flex;
  gap: 16px;
}
.lesson-detail-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}
.lesson-reflection-style {
  position: absolute;
  right: 0;
  top: -40px;
}
.lesson-curriculum-name {
  position: absolute;
  top: 18px;
  margin-left: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111c1c;
}

@media only screen and (max-width: 768px) {
  .mobile-header-right {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;

    & > * {
      margin-left: 0 !important;
      width: 100%;

      // 让按钮占满整行
      :deep(.el-button) {
        width: 100%;
        margin-left: 0;
      }

      // 图标按钮保持原有大小
      :deep(.el-icon-delete),
      :deep(.el-tooltip__trigger) {
        width: auto;
      }
    }
  }
}
</style>
