<template>
  <!--收藏-->
  <el-tooltip effect="dark" placement="top" :hide-after="1000">
    <span slot="content">
      {{ userFavorite ? $t('loc.lessons2FavoriteTips') : $t('loc.lessons2LessonFavoriteTipInfo') }}
    </span>
    <span>
      <el-button type="text" @click.stop="addFavorite($event)" :disabled="isAdmin" style="padding: 0;font-weight: 400;">
        <icon-alibaba :size="size" :class="[`icon-alibaba-favorite-${userFavorite ? 'on' : 'off'}`]"/>
        <span :class="textClass">{{ favoriteCount }}</span>
      </el-button>
    </span>
  </el-tooltip>
</template>
<script>
import Api from '../../../../../api/lessons2'
import {mapState} from "vuex";
import IconAlibaba from "@/views/modules/lesson2/lessonLibrary/components/IconAlibaba";

export default {
  name: 'LessonFavorite',
  components: {IconAlibaba},
  props: [
    'count', // 收藏数
    'favorite', // 当前用户是否已收藏
    'type', // 样式类型
    'lessonId', // 课程 ID
    'size', // 大小
  ],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    }),
    textClass() {
      let tc = ['iconfont-text'];
      if (this.size) {
        tc.push('iconfont-text' + this.size);
      }
      return tc;
    },
    isAdmin() {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    }
  },
  data() {
    return {
      userFavorite: this.favorite,
      favoriteCount: this.count,
      // 防止重复操作
      loading: false
    }
  },
  watch: {
    favoriteCount: function () {
      this.$emit('update:count', { favoriteCount: this.favoriteCount, favorite: this.userFavorite })
    },
    favorite(value){
      this.userFavorite = value
    },
    count(value){
      this.favoriteCount = value
    }
  },
  methods: {
    // 收藏
    addFavorite() {
      if (this.loading || this.isAdmin) {
        return;
      }
      this.loading = true;
      if (this.userFavorite) {
        Api.cancelFavorite(this.lessonId)
          .then(() => {
            this.favoriteCount--;
            this.userFavorite = false;
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        Api.favorite(this.lessonId)
          .then(() => {
            this.favoriteCount++;
            this.userFavorite = true;
          })
          .finally(() => {
            this.loading = false;
          });
      }
    }
  }
}
</script>
<style scoped lang="less">
.iconfont-text {
  font-size: 15px;
  color: #999;
  margin-left: 4px;
}

.iconfont-text-mini {
  font-size: 13px;
}
</style>