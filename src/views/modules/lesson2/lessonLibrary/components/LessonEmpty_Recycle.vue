<template>
  <div>
    <div class="lesson-empty hidden-md-and-down h-full">
      <img class="web_size" src="../../component/assets/img/empty.jpg">
      <div>{{ tip }}</div>
    </div>
    <div style="height: 369px;" class="lesson-empty hidden-lg-and-up h-full">
      <img class="ipad_size" src="../../component/assets/img/empty.jpg">
      <div>{{ tip }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LessonEmpty_Recycle',
  props: ['tip']
}
</script>
<style scoped>
@media screen and (max-width:1199px) {
  .lesson-empty {
    height: 560px;
    text-align: center;
    background-color: #fff;
    display: flex;
    flex-flow: nowrap column;
    align-items: center;
    justify-items: center;
    justify-content: center;
  }
  .ipad_size{
    width: 55%;
    height: 55%;
  }
}
@media screen and (min-width:1200px) {
  .lesson-empty {
    height: 470px;
    text-align: center;
    background-color: #fff;
    display: flex;
    flex-flow: nowrap column;
    align-items: center;
    justify-items: center;
    justify-content: center;
  }
  .web_size {
    width: 355px;
    height: 220px;
  }
}

</style>
