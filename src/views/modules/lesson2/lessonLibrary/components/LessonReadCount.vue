<template>
  <!--浏览量-->
  <el-tooltip class="item" effect="dark" placement="top">
    <span slot="content">{{$t('loc.views')}}</span>
    <span>
      <el-button type="text" style="cursor: default;padding: 0;font-weight: 400;">
        <icon-alibaba class="icon-alibaba-view"/>
        <span class="iconfont-value">{{ count || 0 }}</span>
      </el-button>
    </span>
  </el-tooltip>
</template>
<script>
import IconAlibaba from "@/views/modules/lesson2/lessonLibrary/components/IconAlibaba";

export default {
  name: 'LessonReadCount',
  components: {IconAlibaba},
  props: ['count']
}
</script>
<style scoped lang="less">
.icon-alibaba-view {
  position: relative;
  top: 3px;
  line-height: 16px;
}

.iconfont-value {
  font-size: 15px;
  margin-left: 4px;
  color: #999;
}
</style>