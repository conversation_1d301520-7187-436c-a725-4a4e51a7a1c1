<template>
  <div>
    <div class="lesson-empty hidden-md-and-down lg-border-radius-8 lg-box-shadow">
      <img class="web_size" src="../../component/assets/img/empty.jpg">
      <div>{{ tip }}</div>
    </div>
    <div class="lesson-empty hidden-lg-and-up">
      <img class="ipad_size" src="../../component/assets/img/empty.jpg">
      <div>{{ tip }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LessonEmpty',
  props: ['tip']
}
</script>
<style scoped>
  @media screen and (max-width:1199px) {
    .lesson-empty {
      height: 560px;
      text-align: center;
      background-color: #fff;
      display: flex;
      flex-flow: nowrap column;
      align-items: center;
      justify-items: center;
      justify-content: center;
    }
    .ipad_size {
      height: 200px;
    }
  }
  @media screen and (min-width:1200px) {
    .lesson-empty {
      height: 350px;
      text-align: center;
      background-color: #fff;
      display: flex;
      flex-flow: nowrap column;
      align-items: center;
      justify-items: center;
      justify-content: center;
    }
    .web_size {
      height: 200px;
    }
  }

</style>
