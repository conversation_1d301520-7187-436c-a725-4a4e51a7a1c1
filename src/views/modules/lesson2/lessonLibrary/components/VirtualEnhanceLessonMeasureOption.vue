<template>
  <!-- 如果是领域（带有 children 属性），则渲染为 option-group -->
  <el-option-group v-if="isDomain" :label="measure.name" :key="'domain-' + measure.id"></el-option-group>
  <!-- 如果是单个测评点，则直接渲染为 option -->
  <el-option v-else
    :key="measure.id"
    :label="measure.abbreviation"
    :value="measure.abbreviation"
    @mousedown.native.prevent="optionClick(measure)">
    <span v-if="measure.abbreviation !== measure.name && measure.name">
      {{ measure.abbreviation }}: {{ measure.name }}
    </span>
    <span :title="measure.description" v-else>
      {{ measure.abbreviation }}: {{ measure.description }}
    </span>
  </el-option>
</template>

<script>
export default {
  name: 'VirtualEnhanceLessonMeasureOption',
  props: {
    // 当前 item 的 index，由 virtual-list 提供
    index: {
      type: Number
    },
    // 当前数据项，由 virtual-list 提供
    source: {
      type: Object,
      default () {
        return {}
      }
    },
    // 点击选项后的回调
    onOptionClick: {
      type: Function,
      default: () => {
      },
    }
  },
  data () {
    return {
      measure: null
    }
  },
  watch: {
    source: {
      handler (val) {
        if (val) {
          this.measure = val
        }
      },
      immediate: true
    }
  },
  computed: {
    // 判断当前是否为领域
    isDomain() {
      return this.measure && this.measure.isDomain
    }
  },
  methods: {
    // 处理选项点击事件
    optionClick(measure) {
      // 触发选项点击事件
      this.onOptionClick(measure)
    }
  }
}
</script>

<style scoped lang="scss">
</style>