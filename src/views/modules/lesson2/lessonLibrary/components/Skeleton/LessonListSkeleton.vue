<template>
  <div style="padding: 5px;background-color: #fff">
    <el-skeleton animated>
      <template slot="template">
        <el-skeleton-item variant="image"  style="height: 240px"/>
        <div style="padding-top: 14px;">
          <el-skeleton-item variant="p" style="width: 50%" />
          <div
            style="display: flex; align-items: center; justify-items: space-between;">
            <el-skeleton-item variant="text" style="margin-right: 16px;" />
            <el-skeleton-item variant="text" style="width: 30%;" />
          </div>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script>
export default {
name: "LessonListSkeleton"
}
</script>

<style scoped>

</style>