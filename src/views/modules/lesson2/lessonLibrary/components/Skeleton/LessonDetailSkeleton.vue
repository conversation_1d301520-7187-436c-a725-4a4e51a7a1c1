<template>
  <el-skeleton animated style="margin-top: 30px">
    <template slot="template">
      <el-row type="flex">
        <!-- 课程封面 -->
        <el-col :span="14">
          <el-skeleton-item variant="image"  style="height: 340px"/>
        </el-col>
        <el-col :span="10" style="display: flex; flex-flow: column nowrap; justify-content: center;">
          <div style="display: flex;align-items: center;flex-flow: column nowrap">
            <el-skeleton-item variant="text"  style="width: 60%;margin-bottom: 30px;"/>
            <el-skeleton-item variant="text"  style="width: 20%;margin-bottom: 30px"/>
            <el-skeleton-item variant="text"  style="width: 35%;margin-bottom: 30px"/>
            <el-skeleton-item variant="text"  style="width: 20%;margin-bottom: 30px"/>
          </div>
          <div>
            <el-skeleton-item variant="text"  style="width: 15%;margin-bottom: 30px;margin-left: 50px"/>
            <el-skeleton-item variant="text"  style="width: 25%;margin-bottom: 30px;margin-left: 10px" />
          </div>
          <div style="display: flex">
            <el-skeleton-item variant="text"  style="width: 40%;margin-bottom: 30px;margin-left: 50px"/>
            <el-skeleton-item variant="text"  style="width: 40%;margin-bottom: 30px;margin-left: 50px"/>
          </div>
          <div>
            <el-skeleton-item variant="text"  style="width: 15%;margin-bottom: 30px;margin-left: 50px"/>
            <el-skeleton-item variant="text"  style="width: 25%;margin-bottom: 30px;margin-left: 10px" />
            <el-skeleton-item variant="text"  style="width: 25%;margin-bottom: 30px;margin-left: 10px" />
          </div>

        </el-col>
      </el-row>
      <div style="margin-top: 10px">
        <el-skeleton-item variant="p" style="width: 40%;margin-right: 50px" />
        <el-skeleton-item variant="p" style="width: 30%" />
      </div>
      <!--测评点-->
        <div style="margin: 10px 0px">
          <el-skeleton-item variant="p" style="width: 10%" />
        </div>
        <div style="margin-left: 30px">
          <el-skeleton-item variant="p" style="width: 35%;margin-right: 15%;margin-bottom: 10px" v-for="i in 7" :key="i"/>
        </div>
      <!--课程目标-->
      <div style="margin-bottom: 10px">
        <el-skeleton-item variant="p" style="width: 10%" />
      </div>
      <div style="margin-left: 30px;display: flex;flex-flow: column nowrap">
        <el-skeleton-item variant="p" style="width: 20%;margin-bottom: 10px" />
        <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
        <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
        <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
        <el-skeleton-item variant="p" style="width: 40%;margin-bottom: 10px" />
      </div>
      <!--材料-->
      <div style="margin-bottom: 10px">
        <el-skeleton-item variant="p" style="width: 10%" />
      </div>
      <div style="margin-left: 30px;">
        <el-row >
          <el-col :span="17" style="flex-flow: column nowrap;display: flex">
            <el-skeleton-item variant="p" style="width: 20%;margin-bottom: 10px" />
            <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
            <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
            <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
            <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
            <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
            <el-skeleton-item variant="p" style="width: 40%;margin-bottom: 10px" />
          </el-col>
          <el-col :span="7">
            <el-skeleton-item variant="image"  style="height: 200px"/>
          </el-col>
        </el-row>

      </div>
      <!--步骤-->
      <div style="margin-bottom: 10px">
        <el-skeleton-item variant="p" style="width: 10%" />
      </div>
      <div>
        <div style="margin-left: 30px;display: flex;flex-flow: column nowrap;margin-bottom: 10px">
          <el-skeleton-item variant="p" style="width: 25%;margin-bottom: 10px" />
        </div>
        <div style="margin-left: 60px;margin-bottom: 10px" >
          <el-row>
            <el-col :span="17" style="flex-flow: column nowrap;display: flex">
              <el-skeleton-item variant="p" style="width: 30%;margin-bottom: 10px" />
              <el-skeleton-item variant="p" style="width: 80%;margin-bottom: 10px" />
              <el-skeleton-item variant="p" style="width: 80%;margin-bottom: 10px" />
              <el-skeleton-item variant="p" style="width: 80%;margin-bottom: 10px" />
              <el-skeleton-item variant="p" style="width: 80%;margin-bottom: 10px" />
              <el-skeleton-item variant="p" style="width: 80%;margin-bottom: 10px" />
              <el-skeleton-item variant="p" style="width: 60%;margin-bottom: 10px" />
            </el-col>
            <el-col :span="7">
              <el-skeleton-item variant="image"  style="height: 200px"/>
            </el-col>
          </el-row>
        </div>
      </div>
    </template>
  </el-skeleton>
</template>

<script>
export default {
name: 'LessonDetailSkeleton'
}
</script>

<style scoped>

</style>
