<template>
  <el-skeleton animated style="margin-left: 15px;background-color: #fff">
    <template slot="template">
      <div style="display: flex; align-items: center; justify-items: space-between;" v-for="i in rows" :key="i">
        <el-skeleton-item style="width: 5%;margin: 10px 10px 10px 0" />
        <el-skeleton-item style="width: 90%;margin: 10px 0"/>
      </div>
    </template>
  </el-skeleton>
</template>

<script>
export default {
  name: 'LessonFilterSkeleton',
  props: {
    rows:{
      type: Number,
      default: 3
    }
  }
}
</script>

<style scoped>

</style>