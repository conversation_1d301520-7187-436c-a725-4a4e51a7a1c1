<template>
  <el-skeleton animated>
    <template slot="template">
      <el-row class="recycle_list_item" v-for="i in 5" :key="i">
        <el-col :span="4" style="cursor: pointer">
          <el-skeleton-item variant="image" style="height: 50px"/>
        </el-col>
        <el-col :span="16" class="el-col-16" >

          <el-skeleton-item variant="h3"  style="width: 60%;margin-left: 6px;"/>
          <el-skeleton-item variant="h3"  style="width: 80%;margin-left: 6px;margin-top: 9px"/>
        </el-col>
        <el-col :span="4">
          <div
            style="display: flex; align-items: center; justify-items: space-between;">
            <el-skeleton-item variant="h3" style="width: 40px;margin-top: 18px" />
            <el-skeleton-item variant="h3" style="width: 40px;margin-top: 18px;margin-left: 10px" />
          </div>
        </el-col>
      </el-row>
    </template>
  </el-skeleton>
</template>

<script>
export default {
  name: 'RecycleLessonSkeleton'
}
</script>

<style scoped>
.recycle_list_item {
  margin: 10px 0 20px 14px;
}
</style>