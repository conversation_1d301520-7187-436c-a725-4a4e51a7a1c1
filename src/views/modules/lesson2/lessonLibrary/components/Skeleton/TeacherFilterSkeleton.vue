<template>
  <div style="background-color: #fff;padding: 15px 15px">
    <el-skeleton animated>
      <template slot="template">
        <div v-for="i in 4" style="margin-bottom: 5px">
          <el-skeleton-item  style="width: 100%;height:50px;"/>
          <div style="display: flex; align-items: center; justify-items: space-between;" v-for="i in rows" :key="i">
            <el-skeleton-item style="width: 5%;margin: 10px 10px 10px 0" />
            <el-skeleton-item style="width: 93%;margin: 10px 0"/>
          </div>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script>
export default {
  name: 'TeacherFilterSkeleton',
  props: {
    rows: {
      type: Number,
      default: 5
    }
  }
}
</script>

<style scoped>

</style>