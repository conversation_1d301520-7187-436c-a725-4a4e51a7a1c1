<template>
  <div class="welcome-header">
    <div class="welcome-content">
      <div class="welcome-title"> 🎉&nbsp;{{ $t('loc.lessonWelcomeTitle') }} </div>
      <div class="welcome-description"> {{ $t('loc.lessonWelcomeDescription') }} </div>
      <el-button
        class="create-lesson-button ai-btn lg-button-animation"
        @click="createLessonHandler">
        <i class="el-icon-plus"></i>
        {{ $t('loc.createLessonBtn') }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WelcomeHeader',
  methods: {
    createLessonHandler() {
      this.$emit('create-lesson');
    }
  }
}
</script>

<style scoped lang="less">
.welcome-header {
  width: 100%;
  background-color: #fff;
  // border: 1px solid #e6e6e6;
  border-radius: 8px;
  padding: 32px;
  margin-bottom: 20px;
  // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 200px;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.welcome-title {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
}

.welcome-description {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px
}

.create-lesson-button {
  border-radius: 6px;

  i {
    margin-right: 8px;
    font-weight: 600;
    font-size: 14px;
  }
}

@media screen and (max-width: 768px) {
  .welcome-header {
    padding: 20px 15px;
    border-radius: 6px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .welcome-description {
    font-size: 14px;
  }

  .create-lesson-button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;

    i {
      margin-right: 6px;
      font-size: 12px;
    }
  }
}
</style>