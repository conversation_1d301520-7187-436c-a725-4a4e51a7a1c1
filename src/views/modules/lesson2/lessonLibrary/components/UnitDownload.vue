<template>
  <div>
    <div v-show="showUnitDownload">
      <el-dropdown trigger="click" @command="handleCommand"
      :disabled="downloadLoading || disabled" style="height: 40px;"
                   @click.stop="(event) => event.stopPropagation()"
      :style="{ opacity:  (isCurriculumPlugin && disabled) ? '0.5' : '', background: buttonColor || '#FF7F41', border: buttonColor ? buttonBorder : 'none' }">
    <span class="el-dropdown-link" style="display: flex; align-items: center "
          @click.stop="(event) => event.stopPropagation()"
          :style="{ fontSize: showBigButton ? '14px' : '14px', color: buttonTextColor || '#FFFFFF' }">
      <i class="lg-icon lg-icon-download"  :style="{ fontSize: showBigButton ? '16px' : '14px', ...downloadIconStyle }" style="margin-right: 4px" v-show="!downloadLoading"></i>
      <i class="el-icon-loading" style="font-size: 14px;" v-show="downloadLoading"></i>
      {{ $t('loc.export') }}
      <i class="el-icon-arrow-down el-icon--right" :style="{ ...arrowIconColorStyle }"></i>
    </span>

      <el-dropdown-menu slot="dropdown">
        <span class="dropdown-menu-tip">{{ $t('loc.fullDownloadResources') }}</span>

        <el-dropdown-item command="pdf">
          <img src="~@/assets/img/file/pdf.svg" alt="" class="item-icon-img">
          <span class="font-style">{{ $t('loc.exportAsPDF') }}</span>
        </el-dropdown-item>

        <el-dropdown-item command="docx">
          <img src="~@/assets/img/file/word.svg" alt="" class="item-icon-img">
          <span class="font-style">{{ $t('loc.exportAsWord') }}</span>
        </el-dropdown-item>

        <el-dropdown-item command="googleDriver">
          <img src="~@/assets/img/file/google_drive.svg" class="item-icon-img">
          <span class="font-style">{{ $t('loc.lessons2SaveDrive') }}</span>
        </el-dropdown-item>
  
        <template v-if="showDownloadResources && (showResourcesItem('vocabularies') || showResourcesItem('quiz'))">
          <el-divider></el-divider>
    
          <span class="dropdown-menu-tip">{{ $t('loc.teachingResources') }}</span>
          <el-dropdown-item command="vocabularies" :disabled="!ableResourcesItem('vocabularies')" class="position-relative" v-if="showResourcesItem('vocabularies')">
            <img src="~@/assets/img/file/csv.svg" alt="" class="item-icon-img">
            <span class="font-style">{{ $t('loc.keyVocabulariesDownload') }}</span>
            <div class="w-full h-full position-absolute" @click.stop v-if="!ableResourcesItem('vocabularies')"></div>
          </el-dropdown-item>
    
          <el-dropdown-item command="quiz" :disabled="!ableResourcesItem('quiz')" class="position-relative" v-if="showResourcesItem('quiz')">
            <img src="~@/assets/img/file/zip.svg" alt="" class="item-icon-img">
            <span class="font-style">{{ $t('loc.formativeAssessmentDownload') }}</span>
            <div class="w-full h-full position-absolute" @click.stop v-if="!ableResourcesItem('quiz')"></div>
          </el-dropdown-item>
        </template>
        
      </el-dropdown-menu>
    </el-dropdown>
   </div>
    <!-- <el-button type="primary" @click.stop="logoutGoogleAuth()" size="mini">退出按钮</el-button> -->
    <UnitDownloadDialog :dialogVisible.sync="showDownloadDialog" :downloadType.sync="downloadType" :percentage.sync="percentage" :unitName="currentUnit ? currentUnit.title ? currentUnit.title : currentUnit.overview.title : ''" :useLessonTemplate="currentUnit && getUseLessonTemplate" :downloadUrl="downloadUrl" :batchId="batchId" :grade="currentUnit && currentUnit.grade" :downloadButton = "downloadButton" @click.native.stop />
    <!-- <UnitDownloadDialog :dialogVisible.sync="showDownloadDialog" :downloadType.sync="downloadType" :percentage.sync="percentage" :unitName="unit ? unit.title : ''" :useLessonTemplate="unit && unit.useLessonTemplate" :downloadUrl="downloadUrl" :batchId="batchId" :downloadButton = "downloadButton" @click.native.stop /> -->
  </div>
</template>
<script>
import Api from '../../../../../api/lessons2'
import { mapState } from 'vuex'
import tools from '../../../../../utils/tools'
import {isAuthenticated, isTeacher} from '@/utils/common'
import { platform } from '@/utils/setBaseUrl'
import credentials from '../../../../../components/googleAuth/credentials.json'
import UnitDownloadDialog from '@/views/modules/lesson2/unitPlanner/components/UnitDownloadDialog.vue'
import Lessons2 from '@/api/lessons2'

export default {
  name: 'UnitDownload',
  props: [
    'unit', // 课程 ID
    'getUnit',
    'disabled', // 是否禁用
    'unitName', // 课程名
    'mappedFrameworkId',
    'submodule',
    'unitFrameworkId',
    'viewLesson',
    'showUnitDownload',
    'showBigButton',
    'isFromUnitDetail',
    'buttonColor',
    'buttonBorder',
    'buttonTextColor',
    'downloadIconStyle',
    'arrowIconColorStyle',
    'showDownloadResources'
  ],
  components: {
    UnitDownloadDialog
  },
  data () {
    return {
      downloadLoading: false, // 下载Loading
      showDownloadDrive: true, // Drive下载是否展示
      showSaving: false,// 如果是PDF 或 Word 下载,使用Downloading.如果Google下载,使用 Saving

      // 防止重复操作
      loading: false,
      pdfLoading: false,
      userDownload: false,

      // Client ID 即 OAuth 2.0 客户端 ID
      CLIENT_ID: credentials.web.client_id, // clientId
      scope: credentials.web.scope, // 作用域，这里面 documents 就是 docs
      googleDriveTokenClient: null, // google drive token客户端
      googleAuthCode: null, // google drive通行token
      pickerInited: false, // 选择器是否已经初始化
      gisInited: false, // gis是否已经初始化
      needAuth: false, // 是否需要授权
      showDownloadDialog: false,
      downloadType: 'Download Unit',
      percentage: 0,
      maxRetryCount: 0,
      downloadUrl: '',
      currentBatchId: [], // 当前批次 ID
      timeoutMap: new Map(),
      unitIdMap: new Map(),
      notify: null,
      isSubmenuLeft: false,
      maxCallCount: 0,
      batchId: '',
      downloadButton: '',
      syncId: '',
      leavedPage: false // 是否离开页面
    }
  },

  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser, // 当前用户
      lessonInfoMappedFramework: state => state.lesson.lessonInfoMappedFramework, // 课程详情映射框架
      showClrSource: state => state.lesson.showClrSource, // 是否显示 CLR 资源
      lessonDownloadFileLangCode: state => state.lesson.lessonDownloadFileLangCode, // 课程详情下载文件对应的语言类型的语言码
      contentOriginalLanguage: state => state.translate.originalContentLangCode, // 内容的源语言码
      lessonDownloadFileOriginalLangCode: state => state.lesson.lessonDownloadFileOriginalLangCode, // 课程详情下载文件对应的语言类型的源语言码
      unitUsageModel: (state) => state.magicCurriculum.unitUsageModel, // unit 使用的功能模块
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      showImpStepSourceMap: state => state.lesson.showImpStepSourceMap, // 下载时是否显示实施步骤资源列表
      contentLanguage: state => state.translate.currentContentLangCode, // 内容的当前语言码
    }),
    // 内容的当前语言是否与源语言相同
    isSameKeyVocabulariesLanguage() {
      return this.contentLanguage === this.contentOriginalLanguage
    },
    getUseLessonTemplate() {
      if (this.currentUnit && this.currentUnit.useLessonTemplate) {
        return this.currentUnit.useLessonTemplate;
      }
      return this.currentUnit && this.currentUnit.baseInfo && this.currentUnit.baseInfo.useLessonTemplate
    },
    // 获取当前的 unit 数据
    currentUnit() {
      return this.unit || (this.getUnit && this.getUnit())
    },
    // 是否是相同语言
    isSameLanguage () {
      if (this.lessonDownloadFileOriginalLangCode && this.lessonDownloadFileOriginalLangCode !== '') {
        return this.lessonDownloadFileLangCode === this.lessonDownloadFileOriginalLangCode
      }
      return this.lessonDownloadFileLangCode === this.contentOriginalLanguage
    },
    // 是否显示下载资源提示
    showDownloadResourcesTip() {
      // TK 及以上才显示
      if (this.currentUnit && this.currentUnit.grade && tools.getAgeValue(this.currentUnit.grade) >= 5) {
        return true
      }
      return false
    },
  },

  created () {
    // 如果是ipad，不显示Drive下载
    if (tools.isComeFromIPad()) {
      this.showDownloadDrive = false
    }
    if (this.isFromUnitDetail) {
      this.$analytics.sendEvent('cg_unit_preview_download')
    }
    this.gapiLoaded()
    this.gisLoaded()
    // todo 发送接口进行内容请求
    this.needAuth = true
    this.syncId = tools.uuidv4()
  },

  methods: {
    handleCommand(command) {
      if (command === 'pdf') {
        if (this.isFromUnitDetail) {
          this.$analytics.sendEvent('cg_unit_preview_download_pdf')
        }
        this.downloadPDF('pdf', true)
      } else if (command === 'docx') {
        if (this.isFromUnitDetail) {
          this.$analytics.sendEvent('cg_unit_preview_download_word')
        }
        this.downloadPDF('docx', true)
      } else if (command === 'googleDriver') {
        if (this.isFromUnitDetail) {
          this.$analytics.sendEvent('cg_unit_preview_download_export')
        }
        this.downloadGoogleDocs()
      } else if (command === 'vocabularies') {
        // 下载 dll 词汇
        this.downloadKeyVocabularies()
      } else if (command === 'quiz') {
        // 下载 quiz
        this.downloadUnitQuiz()
      }
      // 阻止事件冒泡
      event.stopPropagation()
    },
  
    // 导出词汇
    async downloadKeyVocabularies() {
      // 下载确认框
      if (await this.downloadMessageConfirm(require('@/assets/img/file/csv.svg'), tools.removeInvalidFileChars('Key Vocabularies ' + this.currentUnit.title + '.csv'))) {
        return
      }
      this.downloadLoading = true
      let params = {
        unitId: this.currentUnit.id
      }
      // 如果当前语言与源语言不相同，则添加语言码
      if (!this.isSameKeyVocabulariesLanguage && this.contentLanguage) {
        params.langCode = this.contentLanguage
      }
      const res = await Lessons2.exportVocabularies(params)
      // 如果返回了 url 则下载
      if (res && res.url) {
        window.location.href = res.url
      }
      this.downloadLoading = false
    },
  
    // 下载 quiz
    async downloadUnitQuiz() {
      // 下载确认框
      if (await this.downloadMessageConfirm(require('@/assets/img/file/zip.svg'), tools.removeInvalidFileChars('Formative Assessment_' + this.currentUnit.title + '.zip'))) {
        return
      }
      this.downloadLoading = true
      try {
        // 创建下载任务参数
        const params = {type: 'ALL'}
        if (this.currentUnit.id) {
          params.unitId = this.currentUnit.id
        }
        // 如果当前语言与源语言不相同，则添加语言码
        if (!this.isSameKeyVocabulariesLanguage && this.contentLanguage) {
          params.langCode = this.contentLanguage
        }
        // 创建下载任务
        const res = await Lessons2.createDownloadQuizTask(params)
        // 如果返回了 taskId，则轮询获取任务状态
        if (res && res.id) {
          // 如果返回了 taskId，则轮询获取任务状态
          await this.getDownloadQuizTask(res.id)
        } else if (res && res.url) {
          this.downloadLoading = false
          window.location.href = res.url
        } else {
          this.downloadLoading = false
        }
      } catch (error) {
        this.downloadLoading = false
        this.$message.error(error.message)
      }
    },
  
    // 获取下载任务
    async getDownloadQuizTask(taskId) {
      if (this.leavedPage) {
        return
      }
      // 获取下载任务
      const res = await Lessons2.getDownloadQuizTask(taskId)
      // 如果返回了 url，则打开新窗口下载
      if (res && res.url) {
        this.downloadLoading = false
        window.location.href = res.url
      } else if (res && res.status === 'FAIL') {
        // 下载失败，提示错误信息
        this.downloadLoading = false
      } else {
        // 如果未返回 url，则轮询获取任务状态
        setTimeout(() => {
          this.getDownloadQuizTask(taskId)
        }, 3000)
      }
    },
  
    // 判断相应的资源按钮是否可用
    ableResourcesItem(item) {
      // 获取资源
      const resource = this.showResourcesItem(item)
      // 如果资源存在且数量大于0
      return resource && resource.num > 0
    },
  
    // 判断是否显示资源按钮
    showResourcesItem(item) {
      // 判空
      if (!this.unit || !this.unit.resources || this.unit.resources.length === 0) {
        return false
      }
      // 获取资源
      return this.unit.resources.find(resourceItem => resourceItem.type === item)
    },
    
    // 课程其他资源下载前确认框
    async downloadMessageConfirm(iconUrl, fullName) {
      // 下载确认框
      let end = true
      const downloadButton = 'loc.download'
      await this.$alert(
        ` <p class="download-unit-message-confirm">
          <img src="${iconUrl}">
          <span class="overflow-ellipsis-two" title='${fullName}'>${fullName}</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t(downloadButton)
        }).then(() => {
        end = false
      })
      return end
    },
    
    // 下载前检测
    checkLogin() {
      // 如果登录则继续执行
      if (isAuthenticated()) {
        return true
      } else {
        // 如果未登录则弹出登录框
        this.$bus.$emit('checkMagicLogin', 'Sign in')
        return false
      }
    },
    // 下载 PDF
    downloadPDF (fileType, showExport = false) {
      // 如果未登录则返回
      if (!this.checkLogin()) {
        return
      }
      localStorage.setItem('syncId', this.syncId)
      this.downloadButton = this.$t('loc.download')
      if (fileType === 'pdf') {
        // 设置下载弹框的标题
        this.downloadType = showExport ? this.$t('loc.exportAsPDF') : this.$t('loc.downloadPDF')
        if (!this.unitUsageModel.downloadPdf && this.isCurriculumPlugin && !this.currentUnit.exemplar) {
          const data = {
            event: 'GO_TO_SHARE_PAGE',
            type: 'UNLOCK_DOWNLOAD'
          }
          this.$bus.$emit('message', data)
          this.$analytics.sendEvent('cg_unit_list_newunit_download')
          return
        }
      } else {
        // 设置下载弹框的标题
        this.downloadType = showExport ? this.$t('loc.exportAsWord') : this.$t('loc.downloadWord')
        if (!this.unitUsageModel.downloadWord && this.isCurriculumPlugin && !this.currentUnit.exemplar) {
          const data = {
            event: 'GO_TO_SHARE_PAGE',
            type: 'UNLOCK_DOWNLOAD'
          }
          this.$bus.$emit('message', data)
          this.$analytics.sendEvent('cg_unit_list_newunit_download')
          return
        }
      }
      this.showDownloadDialog = true
      // Api.generateUnitFoundationPDF(this.unit.id).then(res => {
      // }).catch(error => {
      //   this.$message.error(error.message)
      // })
      // this.getFileGenerateProgress('1234456789')
      // 初始化
      const fileTimeOut = setTimeout(() => {
        this.percentage = 10
      }, 500)
      this.downloadUrl = ''
      let param = {
        'unitId': this.currentUnit.id,
        'planId': [],
        'fileType': fileType,
        'project': platform
      }
      this.$axios.post($api.urls().createBatchGenerateTask, param)
        .then((res) => {
          let batchId = res.id
          this.batchId = batchId
          if (batchId && this.showDownloadDialog) {
            batchId = batchId.toUpperCase()
            localStorage.setItem('currentFileNotification', batchId)
          }
          // 缓存中可能有当前单元的 url, 直接使用
          if (res.message) {
              this.downloadUrl = res.message
              this.percentage = 100
            clearTimeout(fileTimeOut)
          } else {
            this.currentBatchId.push(batchId)
            // 根据 batchId 轮询获取下载的进度
            this.getFileGenerateProgress(batchId, fileType)
          }
        }).catch(error => {
          console.log(error)
        this.$message.error('Network error. Please try again.')
      })
    },
    // 轮询查询 PDF 生成状态
    getFileGenerateProgress (batchId, fileType) {
      let requestData = {
        'batchId': batchId,
        'jobType': 'UNIT_RESOURCE'
      }
      this.$axios.get($api.urls().getFileGenerateProgress, {params: requestData}).then((data) => {
        // 获取下当前 batchId. 若当前 batchId 与最新的 batchId 不一致，则不进行操作
        let batchIdTemp = localStorage.getItem('currentFileNotification')
        if (data.progress !== 100 && data.progress > this.percentage && batchIdTemp && batchIdTemp === batchId.toUpperCase()) {
          this.percentage = data.progress
        }
        if (data.downloadUrl) {
          if (batchIdTemp && batchIdTemp === batchId.toUpperCase()) {
            this.downloadUrl = data.downloadUrl
            this.percentage = 100
          }
        } else {
          setTimeout(() => {
            this.getFileGenerateProgress(batchId, fileType)
          }, 3000)
        }
      }).catch(error => {
        this.$message.error('Network error. Please try again.')
      })
    },
    // 获取文件名 "文件名_yyyy-MM-dd_HH-mm.docx" 格式
    getFileName() {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = ('0' + (currentDate.getMonth() + 1)).slice(-2);
      const day = ('0' + currentDate.getDate()).slice(-2);
      const hours = ('0' + currentDate.getHours()).slice(-2);
      const minutes = ('0' + currentDate.getMinutes()).slice(-2);

      // 定义文件名称,只能是字母和数字还有一些规定的符号
      const regex = /^[a-zA-Z0-9,-]+$/;
      // 如果文件名不符合规定，就将所有不符合的字符替换成 _
      let modifiedLessonName = this.lessonName.trim();
      //  如果不符合规定就替换成 _
      const replacedStr = modifiedLessonName.replace(/./g, (char) => {
        return regex.test(char) ? char : '_';
      });
      return replacedStr + '_' + `${year}-${month}-${day}_${hours}-${minutes}`;
    },
    // 下载谷歌docs文档
    downloadGoogleDocs () {
      // 如果未登录则返回
      if (!this.checkLogin()) {
        return
      }
      // 如果没有权限则返回
      if (this.isCurriculumPlugin && !this.unitUsageModel.exportToGoogleDrive && !this.currentUnit.exemplar) {
        const data = {
          event: 'GO_TO_SHARE_PAGE',
          type: 'UNLOCK_DOWNLOAD'
        }
        this.$bus.$emit('message', data)
        this.$analytics.sendEvent('cg_unit_list_newunit_download')
        return
      }
      if (this.needAuth) {
        this.userDownload = true
        // 判断是否需要认证
        this.authGoogleDocs()
      } else {
        this.showDownloadDialog = true
        localStorage.setItem('syncId', this.syncId)
        this.downloadButton = this.$t('loc.downloadViewGoogleDrive')
        this.downloadType = this.$t('loc.downloadGoogleDrive')
        let fileType = 'google'
        // 初始化
        const fileTimeOut = setTimeout(() => {
          this.percentage = 10
        }, 500)
        this.downloadUrl = ''
        let param = {
          'unitId': this.currentUnit.id,
          'planId': [],
          'fileType': fileType,
          'project': platform
        }
        this.$axios.post($api.urls().createBatchGenerateTask, param)
          .then((res) => {
            let batchId = res.id
            this.batchId = batchId
            if (batchId && this.showDownloadDialog) {
              batchId = batchId.toUpperCase()
              localStorage.setItem('currentFileNotification', batchId)
            }
            // 缓存中可能有当前单元的 url, 直接使用
            if (!batchId && res.message) {
                this.downloadUrl = res.message
                this.percentage = 100
              clearTimeout(fileTimeOut)
            } else {
              this.currentBatchId.push(batchId)
              // 根据 batchId 轮询获取下载的进度
              this.getFileGenerateProgress(batchId, fileType)
            }
          }).catch(error => {
          this.$message.error('Network error. Please try again.')
          this.percentage = 10
          this.downloadUrl = ''
          this.showDownloadDialog = false
          this.userDownload = true
        }).finally(() => {
          if (!this.userDownload) {
            this.downloadLoading = false
          }
        });
      }
    },

    // 如果ipad下载，则使用邮件发送
    ComeFromIPad(downloadFileUrl, fileName) {
      let sendEmailRequest = {
        'emailTemplate': 'lesson_library_detail',
        'downloadFileUrl': downloadFileUrl,
        'fileName': fileName,
        'courseName': this.lessonName
      }
      this.$axios.post($api.urls().sendFileDownloadEmail, sendEmailRequest)
        .then(() => {
          this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
            confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
            showCancelButton: false
          })
        }).catch(error => {
        this.$message.error(error.message)
      })
    },

    // 退出谷歌登录
    logoutGoogleAuth() {
      const credentialsRequest = {
        userId: this.currentUser.user_id,
        scope: this.scope,
        scopeKey: "drivefile"
      }
      this.$axios.post("/lessons2/lessons/logoutGoogleAuth", credentialsRequest)
        .then(data => {
          this.needAuth = true
        })
    },

    /**
     * 判断是否需要谷歌认证
     */
    authGoogleDocs() {
      if (this.needAuth) {
        this.handlerGoogleAuth()
      }
      // todo 请求后台接口创建 google docs
    },
    /**
     * 在 api.js 加载之后，这里进行加载对应的操作的 api
     */
    gapiLoaded() {
      gapi.load('client:picker', this.initializePicker)
    },

    /**
     * 等待 api 加载完成之后，这里进行加载 rest 的服务
     */
    initializePicker() {
      gapi.client.load('https:// www.googleapis.com/discovery/v1/apis/drive/v3/rest')
      this.pickerInited = true
      this.checkGoogleAuth()
    },

    /**
     * Google Identity Services 加载完成之后
     */
    gisLoaded() {
      this.googleDriveTokenClient = google.accounts.oauth2.initCodeClient({
        client_id: this.CLIENT_ID,
        scope: this.scope,
        ux_mode: 'popup',
        callback: '',
      })
      this.gisInited = true
      this.checkGoogleAuth()
    },

    /**
     * 如果同时加载完毕之后，这里应该调用后台的一个接口用于接收前台传递的 token
     */
    checkGoogleAuth(mustBeLoggedIn = false) {
      if (this.pickerInited && this.gisInited) {
        // 封装 token 和 scope 来构建凭证信息
        console.log(this.googleAuthCode)
        const credentialsRequest = {
          authCode: this.googleAuthCode,
          scope: this.scope,
          userId: this.currentUser.user_id,
          scopeKey: "drivefile",
          onlyCheck: ""
        }
        // 接口调用，传递 token
        this.$axios.post($api.urls().checkGoogleAuth, credentialsRequest).then(data => {
          this.needAuth = !data.success
          if (data.success && this.userDownload) {
            this.downloadGoogleDocs()
          }
          console.log(this.needAuth)
        })

      } else if (this.pickerInited && mustBeLoggedIn) {
        // 如果 gisInited 是 false，那么就等待 gisInited 加载完成
        this.gisLoaded()
      } else if (mustBeLoggedIn) {
        // 如果 pickerInited 是 false，那么就等待 initializePicker 加载完成
        this.initializePicker()
      }
    },

    /**
     *  点击 google drive 图标触发的操作
     *  1. 登录
     *  2. 选择文件
     *  3. 完成回调
     */
    handlerGoogleAuth() {
      this.googleDriveTokenClient.callback = async (response) => {
        if (response.error !== undefined) {
          throw (response)
        }
        console.log(response)
        this.googleAuthCode = response.code
        // 校验 scope 是否是规定的 scope
        if (response.scope !== this.scope) {
          // 再一次发送请求
          this.googleDriveTokenClient.requestCode()
        }
        // 保存对应的 token
        this.checkGoogleAuth(true)
      }

      if (this.googleAuthCode === null) {
        // Prompt the user to select a Google Account and ask for consent to share their data
        // when establishing a new session.
        this.googleDriveTokenClient.requestCode()
      } else {
        // Skip display of account chooser and consent dialog for an existing session.
        this.googleDriveTokenClient.requestCode()
      }
    },

    /**
     *  退出登录，清除 token
     */
    handleSignOutClick() {
      if (this.googleAuthCode) {
        this.googleAuthCode = null
        google.accounts.oauth2.revoke(this.googleAuthCode)
      }
    }

  },

  beforeDestroy() {
    this.leavedPage = true
    this.timeout && clearTimeout(this.timeout)
  }
}
</script>

<style scoped lang="less">
.notification_error {
  z-index: 2000;
  width: initial;
  align-items: center;
  background-color: #FEF0F0;
}
// 设置下拉框样式
.el-dropdown {
  height: 32px;
  padding: 0 10px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000;
}

// 设置下拉弹出框选项样式
.el-popper {
  padding: 8px;
}

.el-dropdown-menu {
  border-radius: 8px;
}

// 设置选项样式
.el-dropdown-menu__item {
  padding: 8px 5px;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;

  &>i {
    margin-right: 0;
  }
}

.el-dropdown-menu__item.is-disabled {
  color: #bbb;
  cursor: not-allowed !important;
  pointer-events: unset;
}

// 下拉框里横线样式
.el-divider--horizontal {
  width: 264;
  height: 1px;
  margin: 6px 0px;
  gap: 10px;
}

.font-style {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  margin-left: 8px;
}

.dropdown-menu-tip {
  padding-left: 6px;
  color: var(--676879, #676879);
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

.item-icon-img {
  height: 24px !important;
  width: 19px !important;
}

@font-face {
  font-family: 'iconfont';
  /* Project id 2980060 */
  src: url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.woff2?t=1638515281075') format('woff2'),
  url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.woff?t=1638515281075') format('woff'),
  url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.ttf?t=1638515281075') format('truetype');
}
</style>

<style lang="less">
.download-unit-message-confirm {
  word-break: break-word;
  display: flex;
  align-items: center;
  
  img {
    margin-right: 10px;
    height: 42px !important;
    width: 42px !important;
  }
}
</style>
