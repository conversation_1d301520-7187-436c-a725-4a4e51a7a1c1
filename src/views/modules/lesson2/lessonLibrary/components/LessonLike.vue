<template>
  <!--点赞-->
  <el-tooltip class="item" effect="dark" placement="top" :hide-after="1000">
    <span slot="content">{{ userLiked ? $t('loc.lessons2LikedTips') : $t('loc.lessons2LessonLikeTipInfo') }}</span>
    <span>
      <el-button type="text" @click.stop="like" style="padding: 0;font-weight: 400;">
        <icon-alibaba :size="size" :class="[`icon-alibaba-like-${userLiked ? 'on' : 'off'}`]"/>
        <span :class="textClass">{{ likeCount }}</span>
      </el-button>
    </span>
  </el-tooltip>
</template>
<script>
import Api from '../../../../../api/lessons2'
import IconAlibaba from '@/views/modules/lesson2/lessonLibrary/components/IconAlibaba'

export default {
  name: 'LessonLike',
  components: { IconAlibaba },
  props: [
    'count', // 点赞数
    'liked', // 当前用户是否已点赞
    'type', // 样式类型
    'lessonId', // 课程 ID
    'size'
  ],
  computed: {
    textClass () {
      let tc = ['iconfont-text']
      if (this.size) {
        tc.push('iconfont-text' + this.size)
      }
      return tc
    },
  },
  data () {
    return {
      userLiked: this.liked,
      likeCount: this.count,
      // 防止重复操作
      loading: false
    }
  },
  watch: {
    likeCount: function () {
      this.$emit('update:count', { likeCount: this.likeCount, liked: this.userLiked })
    },
    liked(value){
      this.userLiked = value
    },
    count(value){
      this.likeCount = value
    }
  },
  methods: {
    like () {
      if (this.loading) {
        return
      }
      this.loading = true
      if (this.userLiked) {
        Api.cancelLike(this.lessonId)
          .then(() => {
            this.likeCount--
            this.userLiked = false
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        Api.like(this.lessonId)
          .then(() => {
            this.likeCount++
            this.userLiked = true
          })
          .finally(() => {
            this.loading = false
          })
      }
    }
  }
}
</script>
<style scoped lang="less">
.iconfont-text {
  font-size: 15px;
  color: #999;
  margin-left: 4px;
}

.iconfont-text-mini {
  font-size: 13px;
}

</style>