<template>
  <div ref="appendToBody" class="position-relative">
    <el-dropdown trigger="click" :disabled="downloadLoading" class="magicLessonDownLoad">
    <span class="el-dropdown-link">
      <i class="el-icon-download" style="font-size: 14px;" v-show="!downloadLoading"></i>
      <i class="el-icon-loading" style="font-size: 14px;" v-show="downloadLoading"></i>
      {{ $t('loc.lessons2DownloadOrExport') }}
      <i class="el-icon-arrow-down el-icon--right"></i>
    </span>

      <el-dropdown-menu slot="dropdown" :append-to-body="false" ref="dropMenu">

        <el-dropdown-item @click.native="downloadPDFTemp">
          <i class="lg-icon lg-icon-pdf2" style="size: 24px;">
          </i>
          <span class="font-style" v-if="!unitId">{{ $t('loc.lessons2DownloadPDF') }}</span>
          <span class="font-style" v-else>Download Unit</span>
        </el-dropdown-item>

        <!-- <el-dropdown-item @click.native="downloadDocs">
          <i class="lg-icon lg-icon-word1" style="size: 24px;">
          </i>
          <span class="font-style">{{ $t('loc.lessons2DownloadWord') }}</span>
        </el-dropdown-item> -->

        <el-divider v-if="showDownloadDrive"></el-divider>

        <el-dropdown-item @click.native="downloadGoogleDocsTemp">
          <img class="drive-icon-size" style=" width: 16px; height: 16px;"
               src="@/assets/img/lesson2/assistant/google-drive-icon.png">
          <span class="font-style">Save to Google Drive</span>
        </el-dropdown-item>
        <!-- <el-dropdown-item @click.native="downloadGooglePDF">
          <img class="drive-icon-size" style=" width: 16px; height: 16px;"
            src="@/assets/img/lesson2/assistant/google-drive-icon.png">
          <span class="font-style">Save to Google Drive(PDF)</span>
        </el-dropdown-item> -->

      </el-dropdown-menu>
    </el-dropdown>
  </div>
  <!-- <el-button type="primary" @click.stop="logoutGoogleAuth()" size="mini">退出按钮</el-button> -->

</template>
<script>
import Api from '../../../../../api/lessons2'
import { mapState } from 'vuex'
import tools from '../../../../../utils/tools'
import {isAuthenticated, isTeacher} from '@/utils/common'
import credentials from '../../../../../components/googleAuth/credentials.json'

export default {
  name: 'LessonDownload',
  props: [
    'lessonId', // 课程 ID
    'lessonName', // 课程名
    'mappedFrameworkId',
    'submodule',
    'lessonFrameworkId',
    'viewLesson',
    'unitId', // 单元 ID
    'unitName', // 单元名
  ],
  data () {
    return {
      downloadLoading: false, // 下载Loading
      showDownloadDrive: true, // Drive下载是否展示
      showSaving: false,// 如果是PDF 或 Word 下载,使用Downloading.如果Google下载,使用 Saving

      // 防止重复操作
      loading: false,
      pdfLoading: false,
      userDownload: false,

      // Client ID 即 OAuth 2.0 客户端 ID
      CLIENT_ID: credentials.web.client_id, // clientId
      scope: credentials.web.scope, // 作用域，这里面 documents 就是 docs
      googleDriveTokenClient: null, // google drive token客户端
      googleAuthCode: null, // google drive通行token
      pickerInited: false, // 选择器是否已经初始化
      gisInited: false, // gis是否已经初始化
      needAuth: false, // 是否需要授权
    }
  },

  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser, // 当前用户
      lessonInfoMappedFramework: state => state.lesson.lessonInfoMappedFramework, // 课程详情映射框架
      showClrSource: state => state.lesson.showClrSource, // 是否显示 CLR 资源
      unitDownloadInfoMap: state => state.magicCurriculum.unitDownloadInfoMap, // 单元下载信息
      lessonDownloadInfoMap: state => state.magicCurriculum.lessonDownloadInfoMap, // 课程下载信息
      userEmail: state => state.magicCurriculum.userEmail, // 用户邮箱
    })
  },

  created () {
    // 如果是ipad，不显示Drive下载
    if (tools.isComeFromIPad()) {
      this.showDownloadDrive = false
    }
    this.gapiLoaded()
    this.gisLoaded()
    // todo 发送接口进行内容请求
    this.needAuth = true
  },
  mounted() {
    this.$refs.appendToBody.appendChild(
      this.$refs.dropMenu.popperElm
    )
  },

  methods: {
    // 下载 PDF
    downloadPDF () {
      if (this.viewLesson) {
        this.$analytics.sendEvent('web_unit_detail_download_pdf')
      } else {
        if (isTeacher() && this.submodule !== 'Favorite') {
          this.$analytics.sendEvent('web_lesson_library_my_less_pdf')
        } else if (isTeacher() && this.submodule === 'Favorite') {
          this.$analytics.sendEvent('web_lesson_library_my_less_favorite_pdf')
        } else if (this.submodule === 'Draft') {
          this.$analytics.sendEvent('web_lesson_library_my_less_draft_pdf')
        } else {
          this.$analytics.sendEvent('web_lesson_library_mgt_pdf')
        }
      }
      if (this.downloadLoading) {
        return
      }
      const pdfURL = require('../../../../../assets/img/file/pdf.png')
      const name = this.lessonName.trim().replaceAll(/\s+/g, '_')
      // 缓存键
      const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
      let showDescription = false
      // 将值放入缓存中
      if (localStorage.getItem(cacheKey)) {
        showDescription = JSON.parse(localStorage.getItem(cacheKey))
      }
      this.$alert(
        `<p class="word_keep-all lesson-message-box-pdf-confirm">
          <img src="${pdfURL}">
          <span class="text-ellipsis" title='${name}.pdf'>${name}.pdf</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download')
      }).then(() => {
        this.downloadLoading = true
        this.pdfLoading = true
        const cacheShowCoreKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
        let onlyShowCore = true
        if (localStorage.getItem(cacheShowCoreKey)) {
          onlyShowCore = JSON.parse(localStorage.getItem(cacheShowCoreKey))
        }

        // 如果是课程详情中点击 PDF 下载，优先使用课程详情中的映射框架
        let requestMappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
        Api.generateLessonPDF(this.lessonId, requestMappedFrameworkId, showDescription, onlyShowCore, this.showClrSource).then(res => {
          this.getPDFList()
        }).catch(res => {
          this.downloadLoading = false
          this.pdfLoading = false
        })
      })
      // 课程详情中点击PDF下载
      this.$analytics.sendEvent('web_lessondetails_click_pdfdownload')
      this.$analytics.sendActivityEvent('RLP', 'DOWNLOAD_LESSON')
    },
    // 下载前检测
    checkLogin() {
      // 如果登录则继续执行
      if (isAuthenticated()) {
        return true
      } else {
        // 如果未登录则弹出登录框
        this.$bus.$emit('checkMagicLogin', 'Sign in')
        return false
      }
    },

    downloadPDFTemp() {
      // 如果未登录则返回
      if (!this.checkLogin()) {
        return
      }
      let downloadInfo = []
      let type = ''
      let name = ''
      // 将 unitDownloadInfoMap 和 lessonDownloadInfoMap 合并
      if (this.unitDownloadInfoMap) {
        downloadInfo = downloadInfo.concat(this.unitDownloadInfoMap)
      }
      if (this.lessonDownloadInfoMap) {
        downloadInfo = downloadInfo.concat(this.lessonDownloadInfoMap)
      }
      // 根据 unitId 或 lessonId 获取下载信息
      let downloadInfoItem = downloadInfo.find(item => {
        return (this.unitId && item.unitId && item.unitId.trim().toUpperCase() === this.unitId.trim().toUpperCase())
            || (this.lessonId && item.lessonId && item.lessonId.trim().toUpperCase() === this.lessonId.trim().toUpperCase())
      });
      console.log('this.currentUser1111111111111', this.currentUser)
      console.log('this.userEmail1111111111111', this.userEmail)
      // 判断 unitId 或 lessonId 是否存在
      if (this.unitId) {
        type = 'Unit'
        name = this.unitName
      } else if (this.lessonId) {
        type = 'Lesson'
        name = this.lessonName
      }
      let event = {
        'loadType': type,
        'downType': this.unitId ? 'Zip' : 'PDF',
        'boardId': '7214113094',
        'userEmail': this.userEmail,
        'lessonName': name
      }
      // 给 lambad 发送请求
      this.$axios.post('https://wlixo35bz4viue62dwgeqcmrfm0ecdvi.lambda-url.us-west-1.on.aws/?key=b2a5d0b5-4146-424a-9c52-fd8749186f38', { event: event }).then(data => {

      }).catch(error => {
      })
      // 返回 downloadInfoItem 中 downloadInfo 的 pdfPath 值
      window.open(downloadInfoItem.downloadInfo.pdfPath)
      return downloadInfoItem ? downloadInfoItem.downloadInfo.pdfPath : ''
    },
    downloadGoogleDocsTemp() {
      // 如果未登录则返回
      if (!this.checkLogin()) {
        return
      }
      console.log('this.unitId111111111111111111', this.unitId)
      console.log('this.lessonId111111111111111111', this.lessonId)
      let downloadInfo = []
      let type = ''
      let name = ''
      // 将 unitDownloadInfoMap 和 lessonDownloadInfoMap 合并
      if (this.unitDownloadInfoMap) {
        downloadInfo = downloadInfo.concat(this.unitDownloadInfoMap)
      }
      if (this.lessonDownloadInfoMap) {
        downloadInfo = downloadInfo.concat(this.lessonDownloadInfoMap)
      }
      console.log('downloadInfo1111111111111', downloadInfo)
      // 根据 unitId 或 lessonId 获取下载信息
      // let downloadInfoItem = downloadInfo.find(item => item.unitId === this.unitId || item.lessonId === this.lessonId)
      let downloadInfoItem = downloadInfo.find(item => {
        return (this.unitId && item.unitId && item.unitId.trim().toUpperCase() === this.unitId.trim().toUpperCase())
            || (this.lessonId && item.lessonId && item.lessonId.trim().toUpperCase() === this.lessonId.trim().toUpperCase())
      });
      // 判断 unitId 或 lessonId 是否存在
      if (this.unitId) {
        type = 'Unit'
        name = this.unitName
      } else if (this.lessonId) {
        type = 'Lesson'
        name = this.lessonName
      }
      let event = {
        'loadType': type,
        'downType': 'Google Drive',
        'boardId': '7214113094',
        'userEmail': this.userEmail,
        'lessonName': name
      }
      // 给 lambad 发送请求
      this.$axios.post('https://wlixo35bz4viue62dwgeqcmrfm0ecdvi.lambda-url.us-west-1.on.aws/?key=b2a5d0b5-4146-424a-9c52-fd8749186f38', { event: event }).then(data => {

      }).catch(error => {
      })
      window.open(downloadInfoItem.downloadInfo.googleDrivePath)
      // 返回 downloadInfoItem 中 downloadInfo 的 googleDrivePath 值
      return downloadInfoItem ? downloadInfoItem.downloadInfo.googleDrivePath : ''
    },

    // 轮询查询 PDF 生成状态
    getPDFList () {
      let requestData = { type: 'LESSON_DETAIL' }
      this.$axios.get($api.urls().pdfList, { params: requestData }).then(data => {
        let dataIsCreating = false
        if (data[0].status !== 'SUCCEED' && data[0].status !== 'FAILED') {
          dataIsCreating = true
        } else {
          this.pdfLoading = false
          this.downloadLoading = false
          if (data[0].pdfUrl) {
            if (tools.isComeFromIPad()) {
              this.ComeFromIPad(data[0].pdfUrl, data[0].pdfName)
            } else {
              window.location.href = data[0].pdfUrl
            }
          }
          clearTimeout(this.timeout)
        }
        if (dataIsCreating) {
          this.timeout = setTimeout(() => {
            this.getPDFList()
          }, 3000)
        }
      }).catch(error => {
        this.pdfLoading = false
        this.downloadLoading = false
        if (error.response.data) {
          this.$message.error('error')
        }
        clearTimeout(this.timeout)
      })
    },

    // 获取文件名 "文件名_yyyy-MM-dd_HH-mm.docx" 格式
    getFileName() {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = ('0' + (currentDate.getMonth() + 1)).slice(-2);
      const day = ('0' + currentDate.getDate()).slice(-2);
      const hours = ('0' + currentDate.getHours()).slice(-2);
      const minutes = ('0' + currentDate.getMinutes()).slice(-2);

      // 定义文件名称,只能是字母和数字还有一些规定的符号
      const regex = /^[a-zA-Z0-9,-]+$/;
      // 如果文件名不符合规定，就将所有不符合的字符替换成 _
      let modifiedLessonName = this.lessonName.trim();
      //  如果不符合规定就替换成 _
      const replacedStr = modifiedLessonName.replace(/./g, (char) => {
        return regex.test(char) ? char : '_';
      });
      return replacedStr + '_' + `${year}-${month}-${day}_${hours}-${minutes}`;
    },

    // 下载docs文档
    downloadDocs() {
      if (this.viewLesson) {
        this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_pop_do')
      }
      const name = this.getFileName();
      const iconUrl = require('../../../../../assets/img/file/docx.png')
      const title = 'loc.lessons2DownloadFile'
      const downloadButton = 'loc.download'
      this.$alert(
        ` <p class="lesson-message-box-pdf-confirm">
          <img src="${iconUrl}">
          <span>${name}.docx</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t(downloadButton)
      }).then(() => {
        let requestData = {
          lessonId: this.lessonId,
          scopeKey: "drivefile",
          mappedFrameworkId: "",
          downloadType: "",
          fileName: this.getFileName()
        }
        this.downloadLoading = true
        requestData.mappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
        requestData.downloadType = "default"
        requestData.showClrSource = this.showClrSource
        this.$axios.get($api.urls().generateLessonDetailDoc, { params: requestData }).then(data => {
          // 判断是否是ipad登录
          if (tools.isComeFromIPad()) {
            this.ComeFromIPad(data.url, data.dataStr)
          } else {
            window.location.href = data.url
          }
          if (this.viewLesson) {
            this.$analytics.sendEvent('web_unit_detail_download_word')
          }
          // 下载课程事件
          this.$analytics.sendActivityEvent('RLP', 'DOWNLOAD_LESSON')
        }).finally(() => {
          this.downloadLoading = false
        });
      })
    },

    // 下载谷歌docs文档
    downloadGoogleDocs() {
      if (this.viewLesson) {
        this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_pop_do')
      }
      let requestData = {
        lessonId: this.lessonId,
        scopeKey: "drivefile",
        mappedFrameworkId: "",
        downloadType: "",
        fileName: this.getFileName()
      }
      requestData.mappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
      if (this.needAuth) {
        this.userDownload = true
        // 判断是否需要认证
        this.authGoogleDocs()
      } else {
        requestData.downloadType = "google_drive"
        requestData.showClrSource = this.showClrSource
        this.downloadLoading = true
        this.$axios.get($api.urls().generateLessonDetailDoc, { params: requestData }).then(data => {
          this.userDownload = false

          // 下载成功弹出跳转页面
          const title = 'loc.lessons2SuccessfullyExported'
          const downloadButton = 'loc.lessons2GoDriveButton'
          this.$alert(
            `<p class="word_keep-all lesson-message-box-pdf-confirm">
              <span>${this.$t('loc.lessons2SuccessfullyTitle')}</span>
            </p>`,
            this.$t(title), {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t(downloadButton)
          }).then(() => {
            if (this.viewLesson) {
              this.$analytics.sendEvent('web_unit_detail_download_google')
            }
            window.open(data.url);
          })
          // 下载课程事件
          this.$analytics.sendActivityEvent('RLP', 'DOWNLOAD_LESSON')
        }).catch(error => {
          let errorCode = error.response.data.error_code
          if (errorCode === 'unauthorized') {
            this.userDownload = true
            // 判断是否需要认证
            // this.checkGoogleAuth()
          }
        })
          .finally(() => {
            if (!this.userDownload) {
              this.downloadLoading = false
            }
          });
      }
    },

    // 下载谷歌 PDF
    downloadGooglePDF() {
      if (this.viewLesson) {
        this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_pop_do')
      }
      // 缓存键
      const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
      let showDescription = false
      // 将值放入缓存中
      if (localStorage.getItem(cacheKey)) {
        showDescription = JSON.parse(localStorage.getItem(cacheKey))
      }
      let requestData = {
        lessonId: this.lessonId,
        scopeKey: "drivefile",
        mappedFrameworkId: "",
        downloadType: "",
        fileName: this.getFileName()
      }
      requestData.mappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
      if (this.needAuth) {
        this.userDownload = true
        // 判断是否需要认证
        this.authGoogleDocs()
      } else {
        requestData.downloadType = "google_drive"
        requestData.showClrSource = this.showClrSource
        this.downloadLoading = true
        let onlyShowCore = true
        const cacheShowCoreKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
        if (localStorage.getItem(cacheShowCoreKey)) {
          onlyShowCore = JSON.parse(localStorage.getItem(cacheShowCoreKey))
        }
          // 如果是课程详情中点击 PDF 下载，优先使用课程详情中的映射框架
        let requestMappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
        Api.generateLessonPDF(this.lessonId, requestMappedFrameworkId, showDescription, onlyShowCore, this.showClrSource).then(res => {
          this.getPDFList()
          this.userDownload = false

          // 下载成功弹出跳转页面
          const title = 'loc.lessons2SuccessfullyExported'
          const downloadButton = 'loc.lessons2GoDriveButton'
          this.$alert(
            `<p class="word_keep-all lesson-message-box-pdf-confirm">
              <span>${this.$t('loc.lessons2SuccessfullyTitle')}</span>
            </p>`,
            this.$t(title), {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t(downloadButton)
          }).then(() => {
            if (this.viewLesson) {
              this.$analytics.sendEvent('web_unit_detail_download_google')
            }
            window.open(data.url);
          })
          // 下载课程事件
          this.$analytics.sendActivityEvent('RLP', 'DOWNLOAD_LESSON')
        }).catch(error => {
          let errorCode = error.response.data.error_code
          if (errorCode === 'unauthorized') {
            this.userDownload = true
            // 判断是否需要认证
            // this.checkGoogleAuth()
          }
        })
          .finally(() => {
            if (!this.userDownload) {
              this.downloadLoading = false
            }
          });
      }
    },



    // 如果ipad下载，则使用邮件发送
    ComeFromIPad(downloadFileUrl, fileName) {
      let sendEmailRequest = {
        'emailTemplate': 'lesson_library_detail',
        'downloadFileUrl': downloadFileUrl,
        'fileName': fileName,
        'courseName': this.lessonName
      }
      this.$axios.post($api.urls().sendFileDownloadEmail, sendEmailRequest)
        .then(() => {
          this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
            confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
            showCancelButton: false
          })
        }).catch(error => {
          this.$message.error(error.message)
        })
    },

    // 退出谷歌登录
    logoutGoogleAuth() {
      const credentialsRequest = {
        userId: this.currentUser.user_id,
        scope: this.scope,
        scopeKey: "drivefile"
      }
      this.$axios.post("/lessons2/lessons/logoutGoogleAuth", credentialsRequest)
        .then(data => {
          this.needAuth = true
        })
    },

    /**
     * 判断是否需要谷歌认证
     */
    authGoogleDocs() {
      if (this.needAuth) {
        this.handlerGoogleAuth()
      }
      // todo 请求后台接口创建 google docs
    },
    /**
     * 在 api.js 加载之后，这里进行加载对应的操作的 api
     */
    gapiLoaded() {
      gapi.load('client:picker', this.initializePicker)
    },

    /**
     * 等待 api 加载完成之后，这里进行加载 rest 的服务
     */
    initializePicker() {
      gapi.client.load('https:// www.googleapis.com/discovery/v1/apis/drive/v3/rest')
      this.pickerInited = true
      // this.checkGoogleAuth()
    },

    /**
     * Google Identity Services 加载完成之后
     */
    gisLoaded() {
      this.googleDriveTokenClient = google.accounts.oauth2.initCodeClient({
        client_id: this.CLIENT_ID,
        scope: this.scope,
        ux_mode: 'popup',
        callback: '',
      })
      this.gisInited = true
      // this.checkGoogleAuth()
    },

    /**
     * 如果同时加载完毕之后，这里应该调用后台的一个接口用于接收前台传递的 token
     */
    // checkGoogleAuth(mustBeLoggedIn = false) {
    //   if (this.pickerInited && this.gisInited) {
    //     // 封装 token 和 scope 来构建凭证信息
    //     console.log(this.googleAuthCode)
    //     const credentialsRequest = {
    //       authCode: this.googleAuthCode,
    //       scope: this.scope,
    //       userId: this.currentUser.user_id,
    //       scopeKey: "drivefile",
    //       onlyCheck: ""
    //     }
    //     // 接口调用，传递 token
    //     this.$axios.post($api.urls().checkGoogleAuth, credentialsRequest).then(data => {
    //       this.needAuth = !data.success
    //       if (data.success && this.userDownload) {
    //         this.downloadGoogleDocs()
    //       }
    //       console.log(this.needAuth)
    //     })

    //   } else if (this.pickerInited && mustBeLoggedIn) {
    //     // 如果 gisInited 是 false，那么就等待 gisInited 加载完成
    //     this.gisLoaded()
    //   } else if (mustBeLoggedIn) {
    //     // 如果 pickerInited 是 false，那么就等待 initializePicker 加载完成
    //     this.initializePicker()
    //   }
    // },

    /**
     *  点击 google drive 图标触发的操作
     *  1. 登录
     *  2. 选择文件
     *  3. 完成回调
     */
    handlerGoogleAuth() {
      this.googleDriveTokenClient.callback = async (response) => {
        if (response.error !== undefined) {
          throw (response)
        }
        console.log(response)
        this.googleAuthCode = response.code
        // 校验 scope 是否是规定的 scope
        if (response.scope !== this.scope) {
          // 再一次发送请求
          this.googleDriveTokenClient.requestCode()
        }
        // 保存对应的 token
        // this.checkGoogleAuth(true)
      }

      if (this.googleAuthCode === null) {
        // Prompt the user to select a Google Account and ask for consent to share their data
        // when establishing a new session.
        this.googleDriveTokenClient.requestCode()
      } else {
        // Skip display of account chooser and consent dialog for an existing session.
        this.googleDriveTokenClient.requestCode()
      }
    },

    /**
     *  退出登录，清除 token
     */
    handleSignOutClick() {
      if (this.googleAuthCode) {
        this.googleAuthCode = null
        google.accounts.oauth2.revoke(this.googleAuthCode)
      }
    }

  },

  beforeDestroy() {
    this.timeout && clearTimeout(this.timeout)
  }
}
</script>
<style>
.lesson-message-box-pdf-confirm {
  word-break: break-all;
  display: flex;
  align-items: center;

  &>img {
    margin-right: 10px;
  }
}
</style>

<style scoped lang="less">
// 设置下拉框样式
.el-dropdown {
  height: 40px;
  padding: 0 10px;
  border-radius: 4px;
  border: 2px solid #dcdfe6;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffff;
}

// 设置下拉弹出框选项样式
.el-popper {
  padding: 8px;
  width: 280px;
}

// 设置选项样式
.el-dropdown-menu__item {
  padding: 0 5px;

  &>i {
    margin-right: 0;
  }
}

// 下拉框里横线样式
.el-divider--horizontal {
  width: 264;
  height: 1px;
  margin: 6px 0px;
  gap: 10px;
}
.magicLessonDownLoad {
  background: #FF7F41;
  border: none;
}

.magicLessonDownLoad .el-dropdown {
  border: none;
}

.font-style {
  font-size: 15px;
  color: #000;
  font-weight: 400;
  line-height: 22px;
  margin-left: 8px;
}

@font-face {
  font-family: 'iconfont';
  /* Project id 2980060 */
  src: url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.woff2?t=1638515281075') format('woff2'),
    url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.woff?t=1638515281075') format('woff'),
    url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.ttf?t=1638515281075') format('truetype');
}
</style>
