<template>
  <div class="book">
    <div class="color-323338" style="font-weight: bold;font-size: 18px;margin-bottom: 5px;">
      <span class="title-book font-size-16">{{ $t('loc.lessons2LessonBook') }}</span>
    </div>
    <a @click=" openBookUrl(bookLink)" >
      <lesson-media-viewer :url="thumbnailUrl" type="image"/>
    </a>
    <a @click="openBookUrl(bookLink)" >
      <div class="lesson-share-media-info-title color-323338" :title="title"
           style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
        {{ title }}
      </div>
    </a>

    <div class="lesson-share-media-info-author color-676879" v-if="authorName">
      {{ $t('loc.lessons2LessonDetailToAuthorName') }} {{ authorName }}
    </div>
  </div>
</template>

<script>
import LessonMediaViewer from "@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer";
import tools from '@/utils/tools'
export default {
  name: 'LessonShareBook',
  components: { LessonMediaViewer },
  props: ['book'],
  computed: {
    thumbnailUrl () {
      // eslint-disable-next-line standard/object-curly-even-spacing
      let { volumeInfo = {} } = this.book || {}
      let { imageLinks = {}} = volumeInfo;
      let {thumbnail} = imageLinks;
      return thumbnail;
    },
    title() {
      let {volumeInfo = {} } = this.book || {};
      let {title} = volumeInfo;
      return title;
    },
    authorName() {
      let {volumeInfo = {}} = this.book || {};
      let {authors = []} = volumeInfo;
      let [authorName] = authors;
      return authorName
    },
    bookId() {
      return this.book.id;
    },

    /**
     * 书的链接
     */
    bookLink () {
      let { volumeInfo = {} } = this.book || {}
      let { infoLink } = volumeInfo
      return infoLink
    }
  },
  methods:{
    openBookUrl (url) {
      tools.openUrlWithWebAndIPad(url)
    }
  }
}
</script>

<style scoped>
.title-book {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.7);
  padding: 4px 12px;
  text-align: center;
}
.book {
  width: 240px;
  display: flex;
  flex-flow: column;
  justify-content: center;
  gap: 5px;
  font-size: 14px;
}

.lesson-share-media-info-title {
  text-align: center;
  color: #2A343A;
  font-weight: 500;
}

.lesson-share-media-info-author {
  text-align: center;
  color: rgba(52, 62, 67, 0.73);
  font-weight: 400;
}
</style>