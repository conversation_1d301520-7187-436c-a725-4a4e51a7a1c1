<template>
  <div class="attachment-list">
    <div class="color-323338 title-attachment font-size-16"> {{ $t('loc.lessons2LessonAttachments') }}</div>
    <div style="display: flex;gap: 29px; flex-wrap: wrap;">
      <attachment-item v-for="attachment in files" :file="attachment" :key="attachment.id"/>
    </div>
  </div>
</template>
<script>

import AttachmentItem from '@/views/modules/lesson2/lessonLibrary/components/LessonShare/AttachmentItem'

export default {
  name: 'AttachmentList',
  components: {
    AttachmentItem
  },
  props: {
    files: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  created () {
  }
}
</script>

<style scoped lang="less">
.title-attachment {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.7);
  padding: 4px 12px;
  text-align: center;
  width: 130px;
}
.attachment-list {
  & > :first-child {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 10px;
  }
}
</style>