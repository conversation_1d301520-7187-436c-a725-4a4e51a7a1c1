<template>
  <div class="lesson-share-info">
    <div style="font-size: 24px;" class="lesson-title-container">
      <img src="@/assets/img/lesson2/lessons2-lesson-resources2.png"
           class="lessons2-lesson-resources"  :alt="$t('loc.lessons2LessonResources')"/>
      <div class="flex-center-center lessons2-lesson-resources-info">
        <span>{{ $t('loc.lessons2LessonResources') }}</span>
      </div>
    </div>
    <!-- 家庭活动 -->
    <template v-for="(step, index) in lesson.steps">
      <div :key="index" v-show="step.homeActivity && step.homeActivity.length > 0">
        <div class="font-size-16 font-bold m-t-sm m-b-sm">
          <div class="title-family">{{ $t('loc.unitPlannerLessonAtHomeActivities') }}</div>
          <span v-show="lesson.steps.length > 1">&nbsp;({{ step.ageGroupName }})</span>
        </div>
        <div class="font-size-16 text-black-mon ql-editor remove-padding m-b-sm" v-html="formatContetWrap(step.homeActivity)"></div>
      </div>
    </template>
    <div style="display: flex;gap:29px; margin-top: 10px;">
      <book :book="book" v-if="book"/>
      <video-book :video="video" :book="book" :playable="true" v-if="video && book"/>
    </div>
    <attachment-list :files="attachments" v-if="attachments && attachments.length"/>
    <slot name="share"/>
  </div>
</template>
<script>
import Book from '@/views/modules/lesson2/lessonLibrary/components/LessonShare/Book'
import VideoBook from '@/views/modules/lesson2/lessonLibrary/components/LessonShare/VideoBook'
import AttachmentList from '@/views/modules/lesson2/lessonLibrary/components/LessonShare/AttachmentList'

export default {
  name: 'Info',
  components: {
    Book,
    VideoBook,
    AttachmentList
  },
  props: {
    lesson: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  computed: {
    attachments () {
      return this.lesson.attachmentMedias
    },
    book () {
      return this.lesson.books && this.lesson.books[0]
    },
    video () {
      return this.lesson.videoBooks && this.lesson.videoBooks[0]
    },
    // 内容换行符替换
    formatContetWrap () {
      return function (content) {
        if (!content) {
          return ''
        }
        return content.replace(/\n/g, '<br/>')
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.title-family {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.7);
  width: 180px;
  padding: 4px 12px;
  text-align: center;
}
.lesson-share-info {
  position: relative;
  margin: 60px auto 0;
  border-radius: 4px;
  background: #D9F0CE;
  padding: 48px 34px 30px;

  .lessons2-lesson-resources {
    position: relative;
    top: 19px;
  }
  .lessons2-lesson-resources-info {
    width: 467px;
    position: relative;
    bottom: 39px;
    color: #FFFFFF;
  }

  & > :first-child {
    width: 467px;
    height: 58px;
    position: absolute;
    left: calc(50% - 233.5px);
    top: -29px;
    font-size: 16px;
    text-align: center;
    line-height: 58px;
  }

  & > .attachment-list {
    margin-top: 31px;
  }
}

@media screen and (max-width: 768px) {
  .lesson-share-info {
    .lesson-title-container {
      width: 100% !important;
      font-size: 18px !important;
      .lessons2-lesson-resources {
        width: 100%;
        height: auto;
      }
      .lessons2-lesson-resources-info {
        width: 100%;
        bottom: 30px;
        font-size: 16px;
      }
    }
    & > :first-child {
      width: 90%;
      left: 5%;
      height: auto;
      padding: 10px 0;
    }
  }

  .title-family {
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.7);
    width: 180px;
    padding: 4px 12px;
    text-align: center;
  }
}
</style>
