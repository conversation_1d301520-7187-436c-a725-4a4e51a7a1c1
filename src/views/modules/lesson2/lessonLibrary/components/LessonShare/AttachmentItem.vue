<template>
  <div class="attachment-item" style="border-radius: 6px;background: #F4F7FA;padding: 4px 12px">
    <img class="lesson-share-resource-img" :src="fileIcon" alt="">
    <div class="lesson-share-resource-title color-323338" :title="file.sourceFileName">
      {{ file.sourceFileName }}
    </div>
    <span class="lesson-share-resource-button" style="color: #019EA1; font-weight: 500;margin-left: 150px;">
      <a @click="openAttachmentUrl(file)" target="_blank">
        {{ $t('loc.download') }}
      </a>
    </span>
  </div>
</template>

<script>
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
import tools from '@/utils/tools'

export default {
  name: 'AttachmentItem',
  props: ['file'],
  computed: {
    fileIcon() {
      let name = this.file.sourceFileName;
      let extension = name.substring(name.lastIndexOf('.') + 1)
      return this.fileIcons[extension] || file
    }
  },
  data() {
    return {
      fileIcons: {doc, docx, pdf, ppt, pptx, xls, xlsx, file}
    }
  },
  methods: {
    openAttachmentUrl (file) {
      if (tools.isComeFromIPad()) {
        var fileSize = tools.calFilesize(file.size)
        let requestData = {
          'emailTemplate': 'lesson_library',
          'downloadFileUrl': file.url,
          'fileSize': fileSize,
          'fileName': file.sourceFileName,
          'courseName': this.lesson.name
        }
        this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
        .then(() => {
          this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
            confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
            showCancelButton: false
          })
        }).catch(error => {
          this.$message.error(error.message)
        })
      } else {
        window.open(file.url, '_blank')
      }
    }
  }
}
</script>

<style scoped lang="less">
.attachment-item {
  display: flex;
  grid-template-areas: "a b"
                       "a c";
  align-content: space-between;
  justify-content: space-between;
  align-items: center;
  justify-items: center;
  gap: 5px;

  & > :first-child {
    grid-area: a;
  }
}

.lesson-share-resource-title {
  font-size: 14px;
  text-align: left;
  color: rgba(52, 62, 67, 0.73);
  font-family: inherit;
  font-weight: 400;
  line-height: 35px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-left: 10px;
}

.lesson-share-resource-img {
  display: inline-block;
  width: 32px;
  height: 32px;
}
</style>