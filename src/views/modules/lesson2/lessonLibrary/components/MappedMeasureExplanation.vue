<template>
  <div v-if="mappedMeasureList && mappedMeasureList.length > 0 && !isPlf">
    <h2 style="margin-bottom: 20px;font-size: 18px;font-weight: bolder;margin-top: 25px">Standards</h2>
    <div v-for="(measure ,index) in mappedMeasureList" v-show="index < defaultNum">
      <LessonMappedMeasure :measure="measure" class="mappedMeasureTag"/>
      <p style="margin-bottom: 15px;font-size: 16px;" v-html="formatDescription(measure.description)"></p>
    </div>
    <!--加载更多按钮-->
    <div v-if="!more" style="text-align: center">
      <el-button @click="showMore" size="medium" type="text">{{ tip }}</el-button>
    </div>
  </div>
</template>
<script>
import LessonMappedMeasure from './LessonMappedMeasure'
import constants from '@/utils/constants'

export default {
  name: 'MappedMeasureExplanation',
  components: {
    LessonMappedMeasure
  },
  props: {
    lesson: {
      type: Object,
      default: function () {
        return null
      }
    },
    mappedFrameworkId: {
      type: String,
      default: ''
    }
  },
  data: function () {
    return {
      more: false,
      mappedMeasureList: [], // 需要展示的其它测评点
      isShow: true,
      tip: '',
      defaultNum: 6, // 显示的个数
      isPlf: false
    }
  },
  created () {
    // 获取 DRDP 映射的所有需要展示的其它测评点
    this.getMeasureList()
  },
  watch: {
    mappedFrameworkId: {
      immediate: true,
      handler: function () {
        this.getMeasureList()
        this.isPlf = this.mappedFrameworkId === constants.caPlfFrameworkId
      }
    }
  },
  methods: {
    // 是否需要显示更多按钮
    showAll () {
      this.tip = this.$t('loc.lessons2ShowAll')
      this.more = this.mappedMeasureList.length <= 6 && this.mappedMeasureList.length >= 0
    },
    // 收集课程 DRDP 映射的所有 CCSS 对象
    getMeasureList () {
      // 恢复到初始状态
      this.isShow = true
      this.defaultNum = 6
      let measures = this.lesson.measures || []
      let mappedMeasures = this.lesson.mappedMeasures || {}
      let list = []
      let processed = []
      // 遍历 drdp
      measures && measures.forEach(m => {
        let mappedObj = mappedMeasures && mappedMeasures.find(mapped => {
          return mapped.measureId === m.id
        })
        let mappedMap = mappedObj && mappedObj.mappedMeasures
        mappedMap && mappedMap[this.mappedFrameworkId] && mappedMap[this.mappedFrameworkId].forEach(item => {
          if (!processed.includes(item.id)) {
            list.push(item)
            processed.push(item.id)
          }
        })
      })
      list.sort((a, b) => a.sortIndex - b.sortIndex)
      this.mappedMeasureList = list
      this.showAll()
    },
    // 加载更多
    showMore () {
      this.isShow = !this.isShow
      this.defaultNum = this.isShow ? 6 : this.mappedMeasureList.length
      this.tip = this.isShow ? this.$t('loc.lessons2ShowAll') : this.$t('loc.lessons2Hide')
    },
    // 格式化测评点描述
    formatDescription (description) {
      return description.split('\n').join('<br/>')
    }
  }
}
</script>

<style scoped lang="less">
.mappedMeasureTag {
  /deep/ & > :last-child {
    max-width: unset;
  }

  /deep/ .abbreviationHidden {
    max-width: unset;
  }
}
</style>