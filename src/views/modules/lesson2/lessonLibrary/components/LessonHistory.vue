<template>
  <div id="didi">
    <div>
      <!--按钮-->
      <el-button @click="showAll = !showAll" class="show-more">
        <div class="font">{{ word }}</div>
      </el-button>
      <div class="block">
        <el-timeline>
          <el-timeline-item reverse="true"
                            v-for="(tab, index) in showList.history" :key="index"
                            :icon="c"
                            :color="index === 0? color : null"
                            size="normal"
                            :timestamp="tab.updateLessonTime">
            <el-col>
              <el-row>
                <span v-if="index===0 ">Current Version </span>
                <span v-else-if="index!==0 && index!==tabs.length -1 ">Modified Version </span>
                <span v-else> Original Version </span>
              </el-row>
              <el-row :span="24">
                <!--老师的头像-->
                <el-avatar :src="tab.teacherAvatarURL" style="height:24px;width:24px;"></el-avatar>
                <!--老师的名字-->
                <span style="font-size: 14px">{{ tab.teacherName }}</span> <br>
              </el-row>
            </el-col>
          </el-timeline-item>
        </el-timeline>
      </div>


    </div>
  </div>

</template>


<script>

import index from '../../../../../api/lessons2'


export default {

  name: "LessonHistory",
  props: {
    lessonId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      c: "https://img0.baidu.com/it/u=1685599145,4249304692&fm=15&fmt=auto",
      color: '#0bbd87',
      tabs: [
        {
          teacherName: "Jim Stven",
          updateLessonTime: "Nov.16, 2021 05:45 pm",
          teacherURL: "http://qqpublic.qpic.cn/qq_public/0/0-2392567353-7B198D09663E9337CF220E3F89E31045/0?fmt=jpg&size=46&h=562&w=900&ppv=1.jpg",
          status: "Current Version",
          color: '#0bbd87',
          size: 'large',
          content: '支持使用图标',
        },
        {
          teacherName: "Alan Jack",
          updateLessonTime: "Nov.16, 2021 06:45 pm",
          teacherURL: "http://qqpublic.qpic.cn/qq_public/0/0-2392567353-7B198D09663E9337CF220E3F89E31045/0?fmt=jpg&size=46&h=562&w=900&ppv=1.jpg",
          status: "Modified Version1",

        },
        {
          teacherName: "Jams Curry",
          updateLessonTime: "Nov.16, 2021 08:45 pm",
          teacherURL: "http://qqpublic.qpic.cn/qq_public/0/0-2392567353-7B198D09663E9337CF220E3F89E31045/0?fmt=jpg&size=46&h=562&w=900&ppv=1.jpg",
          status: "Original Version"
        }
      ],
      history: [],
      showAll: false,
      activeName: 0,
    }
  },
  created() {
    //this.getUpdateHistory();
  },
  methods: {
  },
  computed: {
    showList() {
      if (this.showAll === false) {   // 当数据不需要完全显示的时候
        var show = [];            // 定义一个空数组
        return show;　　　　       // 返回当前数组
      } else {
        return this.history;
      }
    },
    word() {
      if (this.showAll === false) { // 对文字进行处理
        return 'Click to view modification history'
      } else {
        return 'Click to collect modification history'
      }
    }
  },
  beforeMount() {
    // 获取更新记录详情：
    index.getUpdateHistory(this.lessonId)
      .then(response => {
        this.history = response;
      }).catch(error => {
    })
  },


}

</script>

<style>
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

/* 隐藏上方表格多余部分 */
.el-table_1_column_8 .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 0px solid #ebeef5
}

.font {

  font-weight: normal;
  width: 196px;
  height: 13px;
  font-size: 14px;
  text-align: left;
  color: #777777;
}

.show-more {
  width: 250px;
  height: 36px;
  border-radius: 3px;
  background: rgba(238, 238, 238, 1)
}
</style>