<template>
  <el-dropdown trigger="click" :disabled="disabled || downloadLoading" style="background: #FF7F41"
               :style="isMC ? {height: '40px'} : {height: '32px'}">
    <span class="el-dropdown-link"  style="color: #FFFFFF;">
      <i class="lg-icon lg-icon-download" style="font-size: 14px;" v-show="!downloadLoading"></i>
      <i class="el-icon-loading" style="font-size: 14px;" v-show="downloadLoading"></i>
      {{ $t('loc.export') }}
      <i class="el-icon-arrow-down el-icon--right"></i>
    </span>

    <el-dropdown-menu slot="dropdown">

      <!-- Full Lesson Kit 部分标题 -->
      <el-dropdown-item disabled class="section-title" v-if="showTeachResource && !isCenterLesson">
        <span class="section-title-text">{{ $t('loc.fullLessonExport') }}</span>
      </el-dropdown-item>

      <el-dropdown-item @click.native="downloadFullLesson('pdf')">
        <img src="~@/assets/img/file/pdf.svg" alt="" class="item-icon-img">
        <span class="font-style">{{ $t('loc.exportAsPDF') }}</span>
      </el-dropdown-item>

      <el-dropdown-item @click.native="downloadFullLesson('docx')">
        <img src="~@/assets/img/file/word.svg" alt="" class="item-icon-img">
        <span class="font-style">{{ $t('loc.exportAsWord') }}</span>
      </el-dropdown-item>

      <el-dropdown-item @click.native="downloadFullLesson('google')" v-if="showDownloadDrive">
        <img src="~@/assets/img/file/google_drive.svg" alt="" class="item-icon-img">
        <span class="font-style">{{ $t('loc.lessons2SaveDrive') }}</span>
      </el-dropdown-item>

      <div v-if="showTeachResource && !isCenterLesson">
        <el-divider></el-divider>

        <!-- Teaching Resources 部分标题 -->
        <el-dropdown-item disabled class="section-title">
          <span class="section-title-text">{{ $t('loc.teachingResources') }}</span>
        </el-dropdown-item>

        <!-- Lecture Slides -->
        <el-dropdown-item v-if="hasSlidesResource" @click.native="downloadLectureSlides">
          <img src="~@/assets/img/file/ppt.svg" alt="" class="item-icon-img">
          <span class="font-style">{{ $t('loc.lessonSlide9') + ' (.pptx)' }}</span>
        </el-dropdown-item>
        <div v-else class="disabled-dropdown-item">
          <img src="~@/assets/img/file/ppt.svg" alt="" class="item-icon-img">
          <span class="font-style">{{ $t('loc.lessonSlide9') + ' (.pptx)' }}</span>
        </div>

        <!-- Student Activities -->
        <template v-if="showTemplateResource">
          <el-dropdown-item v-if="hasTemplateResource" @click.native="downloadStudentActivities">
            <img src="~@/assets/img/file/ppt.svg" alt="" class="item-icon-img">
            <span class="font-style">{{ $t('loc.eduprotocols11') + ' (.pptx)' }}</span>
          </el-dropdown-item>
          <div v-else class="disabled-dropdown-item">
            <img src="~@/assets/img/file/ppt.svg" alt="" class="item-icon-img">
            <span class="font-style">{{ $t('loc.eduprotocols11') + ' (.pptx)' }}</span>
          </div>
        </template>

        <!-- Formative Assessment -->
        <el-dropdown-item v-if="hasQuizResource" @click.native="downloadFormativeAssessment">
          <img src="~@/assets/img/file/zip.svg" alt="" class="item-icon-img">
          <span class="font-style">{{ $t('loc.formativeAssessmentDownload') }}</span>
        </el-dropdown-item>
        <div v-else class="disabled-dropdown-item">
          <img src="~@/assets/img/file/zip.svg" alt="" class="item-icon-img">
          <span class="font-style">{{ $t('loc.formativeAssessmentDownload') }}</span>
        </div>
      </div>

    </el-dropdown-menu>
  </el-dropdown>
  <!-- <el-button type="primary" @click.stop="logoutGoogleAuth()" size="mini">退出按钮</el-button> -->

</template>
<script>
import Api from '../../../../../api/lessons2'
import { mapState } from 'vuex'
import tools from '../../../../../utils/tools'
import { isAuthenticated, isTeacher } from '@/utils/common'
import credentials from '../../../../../components/googleAuth/credentials.json'
import Lessons2 from '../../../../../api/lessons2'
import { platform } from '../../../../../utils/setBaseUrl'
import lessonUtils from '../../../../../utils/lessonUtils'
import { equalsIgnoreCase } from '../../../../../utils/common'
import { LessonTemplates } from '../../../../../utils/constants'

export default {
  name: 'LessonDownload',
  props: [
    'lessonId', // 课程 ID
    'lessonName', // 课程名
    'mappedFrameworkId',
    'submodule',
    'lessonFrameworkId',
    'viewLesson',
    'preview', // 是否是预览
    'disabled',
    'lesson' // 课程内容
  ],
  data () {
    return {
      downloadLoading: false, // 下载Loading
      showDownloadDrive: true, // Drive下载是否展示
      showSaving: false,// 如果是PDF 或 Word 下载,使用Downloading.如果Google下载,使用 Saving

      // 防止重复操作
      loading: false,
      pdfLoading: false,
      userDownload: false,

      // Client ID 即 OAuth 2.0 客户端 ID
      CLIENT_ID: credentials.web.client_id, // clientId
      scope: credentials.web.scope, // 作用域，这里面 documents 就是 docs
      googleDriveTokenClient: null, // google drive token客户端
      googleAuthCode: null, // google drive通行token
      pickerInited: false, // 选择器是否已经初始化
      gisInited: false, // gis是否已经初始化
      needAuth: false, // 是否需要授权
      leavedPage: false, // 是否离开页面
      models: LessonTemplates, // 模板数据

      // 文件类型配置
      fileTypeConfig: {
        pptx: {
          icon: require('@/assets/img/file/ppt.svg'),
          extension: '.pptx'
        },
        zip: {
          icon: require('@/assets/img/file/zip.svg'),
          extension: '.zip'
        }
      },

    }
  },

  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser, // 当前用户
      lessonInfoMappedFramework: state => state.lesson.lessonInfoMappedFramework, // 课程详情映射框架
      showClrSource: state => state.lesson.showClrSource, // 是否显示 CLR 资源
      showMappedTypicalBehaviors: state => state.lesson.showMappedTypicalBehaviors, // 是否显示映射的典型行为
      lessonDownloadFileLangCode: state => state.lesson.lessonDownloadFileLangCode, // 课程详情下载文件对应的语言类型的语言码
      contentOriginalLanguage: state => state.translate.originalContentLangCode, // 内容的源语言码
      lessonDownloadFileOriginalLangCode: state => state.lesson.lessonDownloadFileOriginalLangCode, // 课程详情下载文件对应的语言类型的源语言码
      showImpStepSourceMap: state => state.lesson.showImpStepSourceMap, // 下载时是否显示实施步骤资源列表
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      lessonResourceStatus: state => state.lesson.lessonResourceStatus, // 课程资源状态
    }),
    // 是否是相同语言
    isSameLanguage () {
      if (this.lessonDownloadFileOriginalLangCode && this.lessonDownloadFileOriginalLangCode !== '') {
        return this.lessonDownloadFileLangCode === this.lessonDownloadFileOriginalLangCode
      }
      return this.lessonDownloadFileLangCode === this.contentOriginalLanguage
    },

    // 有教师资源
    showTeachResource () {
      return this.grade && this.grade !== '' && (tools.isK12AgeGroup(this.grade) || equalsIgnoreCase(this.grade.toLocaleLowerCase(), 'TK (4-5)'))
    },

    // 显示模板资源
    showTemplateResource  () {
      return this.grade && this.grade !== '' && (tools.isK12AgeGroup(this.grade))
    },
    
    // 从 Vuex 获取各种资源状态
    hasSlidesResource () {
      return this.lessonResourceStatus.slidesResourceUrl && this.lessonResourceStatus.slidesResourceUrl !== ''
    },
    hasTemplateResource () {
      return this.lessonResourceStatus.templateResourceUrls && this.lessonResourceStatus.templateResourceUrls.length > 0
    },
    hasQuizResource () {
      return this.lessonResourceStatus.hasQuizResource
    },

    // 年龄
    grade () {
      return this.lesson.steps[0] && this.lesson.steps[0].ageGroupName
    },
    // 是否是 center 课程
    isCenterLesson () {
      return lessonUtils.isCenterLesson(this.lesson.activityType)
    },
    // 资源类型配置
    resourceTypes () {
      return {
        LECTURE_SLIDES: {
          type: 'pptx',
          prefix: '',
          suffix: ''
        },
        STUDENT_ACTIVITIES_SINGLE: {
          type: 'pptx',
          prefix: this.getTemplateNameByType(this.lesson.templateType) + '_',
          suffix: ''
        },
        STUDENT_ACTIVITIES_MULTIPLE: {
          type: 'zip',
          prefix: 'Student-ready Activities_',
          suffix: ''
        },
        FORMATIVE_ASSESSMENT: {
          type: 'zip',
          prefix: 'Formative Assessment_',
          suffix: ''
        }
      }
    }
  },

  created () {
    // 如果是ipad，不显示Drive下载
    if (tools.isComeFromIPad()) {
      this.showDownloadDrive = false
    }
    this.gapiLoaded()
    this.gisLoaded()
    // todo 发送接口进行内容请求
    this.needAuth = true
  },

  methods: {
    // 根据模板类型获取模板名称
    getTemplateNameByType (type) {
      if (!type) return ''
      const template = this.models.find(model => model.type === type)
      return template ? template.name : type
    },
    // 下载前检测
    checkLogin () {
      // 如果登录则继续执行
      if (isAuthenticated()) {
        return true
      } else {
        // 如果未登录则弹出登录框
        this.$bus.$emit('checkMagicLogin', 'Sign in')
        return false
      }
    },

    // 下载前处理
    async beforeDownload() {
        // 检查是否有父组件监听此事件
        const hasListeners = this.$listeners && this.$listeners.beforeDownload;
        if (hasListeners) {
          // 触发 beforeDownload 事件，并真正等待父组件处理完成
          await new Promise((resolve, reject) => {
            this.$emit('beforeDownload', { success: resolve, error: reject });
          });
        }
    },

    // 下载 PDF
    downloadPDF () {
      // 如果未登录则返回
      if (!this.checkLogin()) {
        return
      }

      if (this.$route.name === 'AddLesson') {
        // 创建课程下载 pdf 点击埋点
        this.$analytics.sendEvent(this.preview ? 'cg_lesson_plan_cre_pre_click_d_pdf' : 'cg_lesson_plan_cre_click_down_pdf')
      } else if (this.$route.name === 'EditLesson') {
        // 编辑课程下载 pdf 点击埋点
        this.$analytics.sendEvent(this.preview ? 'cg_lesson_plan_edit_pre_click_d_pdf' : 'cg_lesson_plan_edit_click_down_pdf')
      } else if (this.$route.name === 'TeacherLessonList' && this.submodule === 'Create') {
        // 我的课程下载 pdf 点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_click_d_pdf')
      }

      if (this.viewLesson) {
        this.$analytics.sendEvent('web_unit_detail_download_pdf')
      } else {
        if (isTeacher() && this.submodule !== 'Favorite') {
          this.$analytics.sendEvent('web_lesson_library_my_less_pdf')
        } else if (isTeacher() && this.submodule === 'Favorite') {
          this.$analytics.sendEvent('web_lesson_library_my_less_favorite_pdf')
        } else if (this.submodule === 'Draft') {
          this.$analytics.sendEvent('web_lesson_library_my_less_draft_pdf')
        } else {
          this.$analytics.sendEvent('web_lesson_library_mgt_pdf')
        }
      }
      if (this.downloadLoading) {
        return
      }
      const pdfURL = require('../../../../../assets/img/file/pdf.png')
      let name = this.getFileName(false)
      // 如果 Lesson Name 存在，则设置名称
      if (this.lessonName && this.lessonName.trim() !== '') {
        name = this.lessonName.trim().replaceAll(/\s+/g, '_')
      }
      // 缓存键
      const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
      let showDescription = false
      // 将值放入缓存中
      if (localStorage.getItem(cacheKey)) {
        try {
          showDescription = JSON.parse(localStorage.getItem(cacheKey))
        } catch (e) {
          showDescription = false
        }
      }
      this.$alert(
        `<p class="lesson-message-box-pdf-confirm">
          <img src="${pdfURL}">
          <span class="overflow-ellipsis-two" title='${name}.pdf'>${name}.pdf</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('loc.download')
      }).then(async () => {
        this.downloadLoading = true
        this.pdfLoading = true
        
        try {
          // 等待 beforeDownload 完成
          await this.beforeDownload()
          
          const cacheShowCoreKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
          let onlyShowCore = true
          if (localStorage.getItem(cacheShowCoreKey)) {
            try {
              onlyShowCore = JSON.parse(localStorage.getItem(cacheShowCoreKey))
            } catch (e) {
              onlyShowCore = true
            }
          }

          // 如果是课程详情中点击 PDF 下载，优先使用课程详情中的映射框架
          let requestMappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
          
          // 开始生成 PDF
          await Api.generateLessonPDF(
            this.lessonId,
            requestMappedFrameworkId,
            showDescription,
            onlyShowCore,
            this.showClrSource,
            (this.isSameLanguage ? null : this.lessonDownloadFileLangCode),
            this.showImpStepSourceMap,
            this.showMappedTypicalBehaviors
          )
          
          // 生成PDF成功后，获取PDF列表
          this.getPDFList()
        } catch (error) {
          this.downloadLoading = false
          this.pdfLoading = false
        }
      })
      // 课程详情中点击PDF下载
      this.$analytics.sendEvent('web_lessondetails_click_pdfdownload')
      this.$analytics.sendActivityEvent('RLP', 'DOWNLOAD_LESSON')
    },

    // 轮询查询 PDF 生成状态
    getPDFList () {
      let requestData = { type: 'LESSON_DETAIL' }
      this.$axios.get($api.urls().pdfList, { params: requestData }).then(data => {
        let dataIsCreating = false
        if (data[0].status !== 'SUCCEED' && data[0].status !== 'FAILED') {
          dataIsCreating = true
        } else {
          this.pdfLoading = false
          this.downloadLoading = false
          if (data[0].pdfUrl) {
            if (tools.isComeFromIPad()) {
              this.ComeFromIPad(data[0].pdfUrl, data[0].pdfName)
            } else {
              window.location.href = data[0].pdfUrl
            }
          }
          // 触发 afterDownload 事件
          this.$emit('afterDownload')
          clearTimeout(this.timeout)
        }
        if (dataIsCreating) {
          this.timeout = setTimeout(() => {
            this.getPDFList()
          }, 3000)
        }
      }).catch(error => {
        this.pdfLoading = false
        this.downloadLoading = false
        if (error.response.data) {
          this.$message.error('error')
        }
        // 触发 afterDownload 事件
        this.$emit('afterDownload')
        clearTimeout(this.timeout)
      })
    },

    // 获取文件名 "文件名_yyyy-MM-dd_HH-mm.docx" 格式
    getFileName (includesTime = true) {
      // 定义文件名称,只能是字母和数字还有一些规定的符号
      // 如果文件名不符合规定，就将所有不符合的字符替换成 _
      let modifiedLessonName = (this.lessonName && this.lessonName.trim()) || 'Unnamed_Lesson'
      // 替换非法字符为 '-'
      const replacedStr = tools.removeInvalidFileChars(modifiedLessonName)
      if (includesTime) {
        const currentDate = new Date()
        const year = currentDate.getFullYear()
        const month = ('0' + (currentDate.getMonth() + 1)).slice(-2)
        const day = ('0' + currentDate.getDate()).slice(-2)
        const hours = ('0' + currentDate.getHours()).slice(-2)
        const minutes = ('0' + currentDate.getMinutes()).slice(-2)
        return replacedStr + '_' + `${year}-${month}-${day}_${hours}-${minutes}`
      }
      return replacedStr
    },

    // 统一的文件名构建方法
    buildFileName (prefix = '', suffix = '', extension = '') {
      let name = (this.lessonName && this.lessonName.trim()) || 'Unnamed_Lesson'
      return prefix + tools.removeInvalidFileChars(name) + suffix + extension
    },

    // 显示资源下载确认对话框
    async showDownloadConfirmDialog (resourceType, prefix = '', suffix = '') {
      const config = this.fileTypeConfig[resourceType]
      if (!config) {
        return true
      }

      const fileName = this.buildFileName(prefix, suffix, config.extension)
      return this.otherResourceDialog(config.icon, fileName)
    },

    // 通用的资源下载确认方法
    async confirmResourceDownload (resourceKey) {
      const resource = this.resourceTypes[resourceKey]
      if (!resource) {
        return true
      }

      return this.showDownloadConfirmDialog(resource.type, resource.prefix, resource.suffix)
    },

    // 下载docs文档
    downloadDocs () {
      // 如果未登录则返回
      if (!this.checkLogin()) {
        return
      }

      if (this.$route.name === 'AddLesson') {
        // 创建课程下载 word 点击埋点
        this.$analytics.sendEvent(this.preview ? 'cg_lesson_plan_cre_pre_click_d_word' : 'cg_lesson_plan_cre_click_down_word')
      } else if (this.$route.name === 'EditLesson') {
        // 编辑课程下载 word 点击埋点
        this.$analytics.sendEvent(this.preview ? 'cg_lesson_plan_edit_pre_click_d_word' : 'cg_lesson_plan_edit_click_down_word')
      } else if (this.$route.name === 'TeacherLessonList') {
        // 我的课程下载 word 点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_click_d_word')
      }


      if (this.viewLesson) {
        this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_pop_do')
      }
      const name = this.getFileName();
      const iconUrl = require('../../../../../assets/img/file/docx.png')
      const title = 'loc.lessons2DownloadFile'
      const downloadButton = 'loc.download'
      this.$alert(
        ` <p class="lesson-message-box-pdf-confirm">
          <img src="${iconUrl}">
          <span class="overflow-ellipsis-two" title='${name}.docx'>${name}.docx</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t(downloadButton)
      }).then(async () => {
        this.downloadLoading = true
        
        try {
          // 等待 beforeDownload 完成
          await this.beforeDownload()
          
          let requestData = {
            lessonId: this.lessonId,
            scopeKey: "drivefile",
            mappedFrameworkId: "",
            downloadType: "",
            fileName: this.getFileName(),
            ...((this.lessonDownloadFileLangCode && !this.isSameLanguage) && { langCode: this.lessonDownloadFileLangCode })
          }
          
          requestData.mappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
          requestData.downloadType = "default"
          requestData.showClrSource = this.showClrSource
          requestData.showMappedTypicalBehaviors = this.showMappedTypicalBehaviors
          requestData.showImpStepSourceMap = this.showImpStepSourceMap
          
          const data = await this.$axios.get($api.urls().generateLessonDetailDoc, { params: requestData })
          
          // 判断是否是ipad登录
          if (tools.isComeFromIPad()) {
            this.ComeFromIPad(data.url, this.getFileName() + '.docx')
          } else {
            window.location.href = data.url
          }
          // 触发 afterDownload 事件
          this.$emit('afterDownload')
          
          if (this.viewLesson) {
            this.$analytics.sendEvent('web_unit_detail_download_word')
          }
          // 下载课程事件
          this.$analytics.sendActivityEvent('RLP', 'DOWNLOAD_LESSON')
        } catch (error) {
          this.$message.error(error)
        } finally {
          // 触发 afterDownload 事件
          this.$emit('afterDownload')
          this.downloadLoading = false
        }
      })
    },

    // 下载谷歌docs文档
    async downloadGoogleDocs() {
      // 如果未登录则返回
      if (!this.checkLogin()) {
        return
      }

      if (this.$route.name === 'AddLesson') {
        // 创建课程下载 google docs 点击埋点
        this.$analytics.sendEvent(this.preview ? 'cg_lesson_plan_cre_pre_click_d_gog' : 'cg_lesson_plan_cre_click_down_gog')
      } else if (this.$route.name === 'EditLesson') {
        // 编辑课程下载 google docs 点击埋点
        this.$analytics.sendEvent(this.preview ? 'cg_lesson_plan_edit_pre_click_d_gog' : 'cg_lesson_plan_edit_click_down_gog')
      } else if (this.$route.name === 'TeacherLessonList') {
        // 我的课程下载 google docs 点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_click_d_gog')
      }

      if (this.viewLesson) {
        this.$analytics.sendEvent('web_weekly_plan_view_click_lesson_pop_do')
      }
      
      let requestData = {
        lessonId: this.lessonId,
        scopeKey: "drivefile",
        mappedFrameworkId: "",
        downloadType: "",
        fileName: this.getFileName(),
        ...((this.lessonDownloadFileLangCode && !this.isSameLanguage) && { langCode: this.lessonDownloadFileLangCode })
      }
      
      requestData.mappedFrameworkId = this.lessonInfoMappedFramework || this.mappedFrameworkId || this.lessonFrameworkId;
      
      if (this.needAuth) {
        this.userDownload = true
        // 判断是否需要认证
        this.authGoogleDocs()
      } else {
          this.downloadLoading = true
          try {
            // 等待 beforeDownload 完成
            await this.beforeDownload()
            
            requestData.downloadType = "google_drive"
            requestData.showClrSource = this.showClrSource
            requestData.showMappedTypicalBehaviors = this.showMappedTypicalBehaviors
            requestData.showImpStepSourceMap = this.showImpStepSourceMap
            
            const data = await this.$axios.get($api.urls().generateLessonDetailDoc, { params: requestData })
            
            this.userDownload = false

            // 下载成功弹出跳转页面
            const title = 'loc.lessons2SuccessfullyExported'
            const downloadButton = 'loc.lessons2GoDriveButton'
            this.$alert(
              `<p class="word_keep-all lesson-message-box-pdf-confirm">
                <span>${this.$t('loc.lessons2SuccessfullyTitle')}</span>
              </p>`,
              this.$t(title), {
              dangerouslyUseHTMLString: true,
              confirmButtonText: this.$t(downloadButton)
            }).then(() => {
              if (this.viewLesson) {
                this.$analytics.sendEvent('web_unit_detail_download_google')
              }
              window.open(data.url);
            })
            // 触发 afterDownload 事件
            this.$emit('afterDownload')

            // 下载课程事件
            this.$analytics.sendActivityEvent('RLP', 'DOWNLOAD_LESSON')
          } catch (error) {
            // console.error('Google文档生成失败:', error)
            
            if (error.response && error.response.data && error.response.data.error_code === 'unauthorized') {
              this.userDownload = true
              // 判断是否需要认证
              this.checkGoogleAuth()
            }
          } finally {
            // 触发 afterDownload 事件
            this.$emit('afterDownload')
            if (!this.userDownload) {
              this.downloadLoading = false
            }
          }
      }
    },

    // 如果ipad下载，则使用邮件发送
    ComeFromIPad(downloadFileUrl, fileName) {
      let sendEmailRequest = {
        'emailTemplate': 'lesson_library_detail',
        'downloadFileUrl': downloadFileUrl,
        'fileName': fileName,
        'courseName': this.lessonName
      }
      this.$axios.post($api.urls().sendFileDownloadEmail, sendEmailRequest)
        .then(() => {
          this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
            confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
            showCancelButton: false
          })
        }).catch(error => {
          this.$message.error(error.message)
        })
    },

    // 退出谷歌登录
    logoutGoogleAuth() {
      const credentialsRequest = {
        userId: this.currentUser.user_id,
        scope: this.scope,
        scopeKey: "drivefile"
      }
      this.$axios.post("/lessons2/lessons/logoutGoogleAuth", credentialsRequest)
        .then(data => {
          this.needAuth = true
        })
    },

    /**
     * 判断是否需要谷歌认证
     */
    authGoogleDocs() {
      if (this.needAuth) {
        this.handlerGoogleAuth()
      }
      // todo 请求后台接口创建 google docs
    },
    /**
     * 在 api.js 加载之后，这里进行加载对应的操作的 api
     */
    gapiLoaded() {
      gapi.load('client:picker', this.initializePicker)
    },

    /**
     * 等待 api 加载完成之后，这里进行加载 rest 的服务
     */
    initializePicker() {
      gapi.client.load('https:// www.googleapis.com/discovery/v1/apis/drive/v3/rest')
      this.pickerInited = true
      this.checkGoogleAuth()
    },

    /**
     * Google Identity Services 加载完成之后
     */
    gisLoaded() {
      this.googleDriveTokenClient = google.accounts.oauth2.initCodeClient({
        client_id: this.CLIENT_ID,
        scope: this.scope,
        ux_mode: 'popup',
        callback: '',
      })
      this.gisInited = true
      this.checkGoogleAuth()
    },

    /**
     * 如果同时加载完毕之后，这里应该调用后台的一个接口用于接收前台传递的 token
     */
    checkGoogleAuth(mustBeLoggedIn = false) {
      if (this.pickerInited && this.gisInited) {
        // 封装 token 和 scope 来构建凭证信息
        // console.log(this.googleAuthCode)
        const credentialsRequest = {
          authCode: this.googleAuthCode,
          scope: this.scope,
          userId: this.currentUser.user_id,
          scopeKey: "drivefile",
          onlyCheck: ""
        }
        // 接口调用，传递 token
        this.$axios.post($api.urls().checkGoogleAuth, credentialsRequest).then(data => {
          this.needAuth = !data.success
          if (data.success && this.userDownload) {
            this.downloadFullLesson('google')
          }
          // console.log(this.needAuth)
        })

      } else if (this.pickerInited && mustBeLoggedIn) {
        // 如果 gisInited 是 false，那么就等待 gisInited 加载完成
        this.gisLoaded()
      } else if (mustBeLoggedIn) {
        // 如果 pickerInited 是 false，那么就等待 initializePicker 加载完成
        this.initializePicker()
      }
    },

    /**
     *  点击 google drive 图标触发的操作
     *  1. 登录
     *  2. 选择文件
     *  3. 完成回调
     */
    handlerGoogleAuth() {
      this.googleDriveTokenClient.callback = async (response) => {
        if (response.error !== undefined) {
          throw (response)
        }
        this.googleAuthCode = response.code
        // 校验 scope 是否是规定的 scope
        if (response.scope !== this.scope) {
          // 再一次发送请求
          this.googleDriveTokenClient.requestCode()
        }
        // 保存对应的 token
        this.checkGoogleAuth(true)
      }

      if (this.googleAuthCode === null) {
        // Prompt the user to select a Google Account and ask for consent to share their data
        // when establishing a new session.
        this.googleDriveTokenClient.requestCode()
      } else {
        // Skip display of account chooser and consent dialog for an existing session.
        this.googleDriveTokenClient.requestCode()
      }
    },

    /**
     *  退出登录，清除 token
     */
    handleSignOutClick() {
      if (this.googleAuthCode) {
        this.googleAuthCode = null
        google.accounts.oauth2.revoke(this.googleAuthCode)
      }
    },

    // 下载演讲幻灯片
    async downloadLectureSlides () {
      // 如果未登录则返回
      if (!this.checkLogin() || this.downloadLoading) {
        return
      }
      // 下载确认框
      if (await this.confirmResourceDownload('LECTURE_SLIDES')) {
        return
      }
      this.downloadLoading = true
      try {
        if (equalsIgnoreCase(this.lessonResourceStatus.slidesResourceUrl, 'NO_PRESENTATION_ID')) {
          // 生成后下载
          const res = await Lessons2.getLessonSlidesDownloadModel(this.lessonId)
          if (res && res.slidesUrl) {
            this.downloadFileViaAnchorTag(res.slidesUrl + '/export/pptx')
          } else {
            this.$message.error(this.$t('loc.downloadUnsuccessful'))
          }
          this.downloadLoading = false
        } else {
          this.downloadFileViaAnchorTag(this.lessonResourceStatus.slidesResourceUrl)
        }
      } catch (error) {
        this.$message.error(this.$t('loc.downloadUnsuccessful'))
        this.downloadLoading = false
      }
    },

    // 下载学生活动材料 模板
    async downloadStudentActivities () {
      // 如果未登录则返回
      if (!this.checkLogin() || this.downloadLoading) {
        return
      }
      // 下载确认框
      const oneSize = this.lessonResourceStatus.templateResourceUrls.length === 1
      const resourceType = oneSize ? 'STUDENT_ACTIVITIES_SINGLE' : 'STUDENT_ACTIVITIES_MULTIPLE'
      if (await this.confirmResourceDownload(resourceType)) {
        return
      }

      this.downloadLoading = true
      try {
        if (oneSize) {
          // 单个文件直接下载
          if (equalsIgnoreCase(this.lessonResourceStatus.templateResourceUrls[0], 'NO_PRESENTATION_ID')) {
            // 生成后下载
            const res = await Lessons2.getLessonTemplateDownloadModels(this.lessonId)
            if (res && res[0] && res[0].templateUrl) {
              this.downloadFileViaAnchorTag(res[0].templateUrl + '/export/pptx')
            } else {
              this.$message.error(this.$t('loc.downloadUnsuccessful'))
            }
            this.downloadLoading = false
          } else {
            this.downloadFileViaAnchorTag(this.lessonResourceStatus.templateResourceUrls[0])
          }
        } else {
          // 多个文件打包下载，直接调用模板的校验方法
          const res = await Lessons2.createLessonTemplatesDownload(this.lessonId)
          // 如果返回了 taskId，则轮询获取任务状态
          if (res && res.id) {
            // 如果返回了 taskId，则轮询获取任务状态
            this.getSingleDownloadTask(res.id)
          } else if (res && res.url) {
            this.downloadFileViaAnchorTag(res.url)
          } else {
            this.downloadLoading = false
          }
        }
      } catch (error) {
        this.$message.error(this.$t('loc.downloadUnsuccessful'))
        this.downloadLoading = false
      }
    },

    // 下载形成性评估材料 quiz
    async downloadFormativeAssessment () {
      // 如果未登录则返回
      if (!this.checkLogin() || this.downloadLoading) {
        return
      }
      // 下载确认框
      if (await this.confirmResourceDownload('FORMATIVE_ASSESSMENT')) {
        return
      }
      // 设置正在下载
      this.downloadLoading = true
      try {
        // 创建下载任务参数
        const params = {}
        params.lessonId = this.lessonId
        params.type = 'ALL'
        // 如果当前语言与源语言不相同，则添加语言码
        if (!this.isSameLanguage && this.lessonDownloadFileLangCode) {
          params.langCode = this.lessonDownloadFileLangCode
        }
        // 创建下载任务
        const res = await Lessons2.createDownloadQuizTask(params)
        // 如果返回了 taskId，则轮询获取任务状态
        if (res && res.id) {
          // 如果返回了 taskId，则轮询获取任务状态
          this.getSingleDownloadTask(res.id)
        } else if (res && res.url) {
          this.downloadFileViaAnchorTag(res.url)
        } else {
          this.downloadLoading = false
        }
      } catch (error) {
        this.$message.error(this.$t('loc.downloadUnsuccessful'))
        this.downloadLoading = false
      }
    },

    // 获取下载任务
    async getSingleDownloadTask (taskId) {
      if (this.leavedPage) {
        return
      }
      // 获取下载任务
      const res = await Lessons2.getDownloadQuizTask(taskId)
      // 如果返回了 url，则打开新窗口下载
      if (res && res.url) {
        this.downloadFileViaAnchorTag(res.url)
      } else if (res && res.status === 'FAIL') {
        // 下载失败，提示错误信息
        this.$message.error(this.$t('loc.downloadUnsuccessful'))
        this.downloadLoading = false
      } else {
        // 如果未返回 url，则轮询获取任务状态
        setTimeout(() => {
          this.getSingleDownloadTask(taskId)
        }, 3000)
      }
    },

    // 创建 A 标签进行直接下载
    downloadFileViaAnchorTag (url) {
      const link = document.createElement('a')
      link.href = url
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      this.downloadLoading = false
    },

    // 兼容没有资源的课程，调用原下载处理
    noResourceLessonHandle (fileType) {
      if (fileType === 'google') {
        this.downloadGoogleDocs()
      } else if (fileType === 'docx') {
        this.downloadDocs()
      } else if (fileType === 'pdf') {
        this.downloadPDF()
      }
    },

    // 下载课程资源
    async downloadFullLesson (fileType) {
      // 兼容没有资源的课程，调用原下载处理
      if (!this.showTeachResource || this.isCenterLesson) {
        this.noResourceLessonHandle(fileType)
        return
      }

      // 如果未登录则返回
      if (!this.checkLogin() || this.downloadLoading) {
        return
      }
      this.userDownload = true
      if (this.needAuth && fileType === 'google') {
        // 判断是否需要认证
        this.authGoogleDocs()
        return
      }
      // 下载确认框
      if (await this.fullResourceDialog(fileType)) {
        return
      }

      this.downloadLoading = true
      let params = {
        'lessonId': this.lessonId,
        'fileType': fileType,
        'project': platform
      }
      // 如果当前语言与源语言不相同，则添加语言码
      if (!this.isSameLanguage && this.lessonDownloadFileLangCode) {
        params.langCode = this.lessonDownloadFileLangCode
      }
      this.$axios.post($api.urls().createBatchGenerateLessonTask, params)
        .then((res) => {
          let batchId = res.id
          if (res.message) {
            this.fullResourceViaAnchorTag(res.message, fileType)
          } else {
            if (batchId) {
              // 根据 batchId 轮询获取下载的进度
              this.getLessonFileGenerateProgress(batchId, fileType)
            } else {
              this.$message.error(this.$t('loc.downloadUnsuccessful'))
              this.downloadLoading = false
            }
          }
        }).catch(error => {
        this.$message.error(this.$t('loc.downloadUnsuccessful'))
        this.downloadLoading = false
      })
    },

    // 课程完整资源下载前确认框
    async fullResourceDialog (fileType) {
      // 下载确认框
      let end = true
      if (fileType === 'google') {
        return false
      }
      const name = this.getFileName()
      const iconUrl = require('@/assets/img/file/zip.svg')
      const downloadButton = 'loc.download'
      await this.$alert(
        ` <p class="lesson-message-box-pdf-confirm-zip">
          <img src="${iconUrl}">
          <span class="overflow-ellipsis-two" title='${name}.zip'>${name}.zip</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t(downloadButton)
        }).then(() => {
        end = false
      })

      return end
    },

    // 课程其他资源下载前确认框
    async otherResourceDialog (iconUrl, fullName) {
      // 下载确认框
      let end = true
      const downloadButton = 'loc.download'
      await this.$alert(
        ` <p class="lesson-message-box-pdf-confirm-zip">
          <img src="${iconUrl}">
          <span class="overflow-ellipsis-two" title='${fullName}'>${fullName}</span>
        </p>`,
        this.$t('loc.lessons2DownloadFile'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t(downloadButton)
        }).then(() => {
        end = false
      })

      return end
    },

    // 轮询查询课程资源生成状态
    getLessonFileGenerateProgress (batchId, fileType) {
      if (this.leavedPage) {
        return
      }
      let requestData = {
        'batchId': batchId,
        'jobType': 'LESSON_RESOURCE'
      }
      this.$axios.get($api.urls().getFileGenerateProgress, { params: requestData }).then((data) => {
        // 资源是否完成准备
        if (data.downloadUrl) {
            // 直接下载
            this.fullResourceViaAnchorTag(data.downloadUrl, fileType)
        } else {
          setTimeout(() => {
            this.getLessonFileGenerateProgress(batchId, fileType)
          }, 3000)
        }
      }).catch(error => {
        this.$message.error(this.$t('loc.downloadUnsuccessful'))
        this.downloadLoading = false
      })
    },

    /**
     * 课程完整资源下载后打开
     * @param url 资源地址
     * @param fileType 文件类型
     */
    fullResourceViaAnchorTag (url, fileType) {
      // 直接下载
      if (fileType === 'google') {
        // 下载成功弹出跳转页面
        const title = 'loc.lessons2SuccessfullyExported'
        const downloadButton = 'loc.lessons2GoDriveButton'
        this.$alert(
          `<p class="word_keep-all lesson-message-box-pdf-confirm">
                <span>${this.$t('loc.lessons2SuccessfullyTitle')}</span>
              </p>`,
          this.$t(title), {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t(downloadButton)
          }).then(() => {
          window.open(url)
          this.downloadLoading = false
        })
      } else {
        this.downloadFileViaAnchorTag(url)
      }
    }

  },

  beforeDestroy() {
    this.leavedPage = true
    this.timeout && clearTimeout(this.timeout)
  }
}
</script>
<style>
.lesson-message-box-pdf-confirm {
  word-break: break-word;
  display: flex;
  align-items: center;
}

.lesson-message-box-pdf-confirm > img {
  margin-right: 10px;
}

.lesson-message-box-pdf-confirm-zip {
  word-break: break-word;
  display: flex;
  align-items: center;
}

.lesson-message-box-pdf-confirm-zip > img {
  margin-right: 10px;
  height: 42px !important;
  width: 42px !important;
}
</style>

<style scoped lang="less">
// 设置下拉框样式
.el-dropdown {
  height: 32px;
  padding: 0 10px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000;

  &:hover {
    box-shadow: 0 0 0 3px rgba(255, 127, 65, 0.4);
    border-radius: 4px;
  }
}

// 设置下拉弹出框选项样式
.el-popper {
  padding: 8px;
  width: 320px;
}

// 禁用状态样式 - 自定义禁用项样式
.disabled-dropdown-item {
  padding: 0 5px;
  cursor: not-allowed !important;
  background-color: transparent !important;
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 36px;
  min-height: 36px;

    // 禁用文字选择
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  // 图标置灰
  i {
    opacity: 0.3 !important;
    color: #C0C4CC !important;
  }

  // 图标置灰：支持 <img> 标签
  img,
  .item-icon-img {
    opacity: 0.3 !important;
    color: #C0C4CC !important;
  }

  // 文字置灰
  .font-style {
    color: #C0C4CC !important;
    opacity: 0.5 !important;
  }

  // 禁用悬停效果
  &:hover {
    background-color: transparent !important;
    cursor: not-allowed !important;

    .font-style {
      color: #C0C4CC !important;
    }
  }
}

// 设置选项样式
.el-dropdown-menu__item {
  padding: 0 5px;

  &>i {
    margin-right: 0;
  }

  &:hover {
    span {
      color: #10B3B7 !important;
    }
  }

  // 分组标题样式
  &.section-title {
    padding: 0 12px 0 6px;
    color: #8C8C8C !important;
    font-size: 12px;
    font-weight: 500;
    cursor: default;
    background-color: transparent !important;

    &:hover {
      background-color: transparent !important;

      .section-title-text {
        color: #8C8C8C !important;
      }
    }

    .section-title-text {
      color: #8C8C8C;
      font-size: 12px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 下拉框里横线样式
.el-divider--horizontal {
  width: 304px;
  height: 1px;
  margin: 6px 0px;
  gap: 10px;
}

.font-style {
  font-size: 15px;
  color: #000;
  font-weight: 400;
  line-height: 22px;
  margin-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-icon-img {
  height: 16px !important;
  width: 19px !important;
}

@font-face {
  font-family: 'iconfont';
  /* Project id 2980060 */
  src: url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.woff2?t=1638515281075') format('woff2'),
  url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.woff?t=1638515281075') format('woff'),
  url('// at.alicdn.com/t/font_2980060_12gyhi5dajl.ttf?t=1638515281075') format('truetype');
}
</style>
