<template>
  <el-card class="el-card lg-box-shadow-hover lg-border-radius-8 lesson-card-header">
    <!-- 封面 -->
    <lesson-media-viewer :url="coverURL" :isAdaptedLesson="lesson.adaptedLesson" :unitTitle="lesson.unitTitle" class="lg-border-top-left-radius-8 lg-border-top-right-radius-8"/>
    <!-- adapt 图片按钮 -->
    <adapt-lesson-button v-if="!isDraft"
                      :is-first-lesson="lesson.isFirstLesson"
                      :adapt-lesson-id="lesson.id"
                      :is-adapt-icon="true"
                      @lessonAdapt="handleLessonAdapt"
    ></adapt-lesson-button>
    <!-- 课程名称 -->
    <div class="lesson-name" :title="lesson.name">
      <span>
      <slot name="lesson-promoted-agency"></slot>
      {{ lesson.name }}
    </span>
    </div>
    <!-- 课程年龄组 -->
    <div class="lesson-age-group-framework" style="display: flex; gap: 5px; margin: 0 12px;">
      <el-tag v-for="age in parse(lesson.ageGroupNames)" :key="age" type="info" size="mini">
        {{ age }}
      </el-tag>

      <!-- 课程框架 -->
      <el-tag v-if="lesson.frameworkName" :title="lesson.frameworkName" class="overflow-ellipsis" type="info" size="mini">
        {{ lesson.frameworkName }}
      </el-tag>
    </div>
    <!-- 课程测评点 -->
    <div class="lesson-measure">
      <el-tag v-for="measure in measures" :key="measure" type="success" size="small">
        {{ measure }}
      </el-tag>
    </div>
    <!-- 分隔线-->
    <el-divider/>
    <div class="lesson-card-footer">
      <!-- 作者头像 -->
      <el-avatar v-if="lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM'" :src="currentPluginUser.avatar_url || lesson.authorAvatar || avatarURL" size="small" shape="circle" class="lesson-avatar"/>
      <el-avatar v-if="lesson.type === 'CURRICULUM-PLUGIN_SYSTEM'" :src="lesson.authorAvatar || avatarURL" size="small" shape="circle" class="lesson-avatar"/>
      <!-- 作者姓名 -->
      <span v-if="lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM'" class="author-name" style="padding-left: 5px;" :title="currentPluginUser.user_name || currentPluginUser.email || lesson.authorName">
        {{ currentPluginUser.user_name || currentPluginUser.email || lesson.authorName || formatUserName }}
      </span>
      <span v-if="lesson.type === 'CURRICULUM-PLUGIN_SYSTEM'" class="author-name" style="padding-left: 5px;" :title="lesson.authorName">
        {{ lesson.authorName }}
      </span>
      <!-- 课程点赞数 -->
      <!-- <lesson-like v-if="!this.isDraft && !isCurriculumPlugin"
                   :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"/> -->
      <!-- 课程收藏数 -->
      <!-- <lesson-favorite v-if="!this.isDraft && !isCurriculumPlugin" style="margin-left: 3px;"
                       :count="lesson.favoriteCount" :lesson-id="lesson.id" :favorite="lesson.favorite"/> -->
    </div>

    <div style="position: absolute;top: 0;left: 0;width: 100%;">
      <!-- 左上角内容：草稿标记 -->
      <div style="float:left">
        <slot name="lesson-card-corner-left-top"/>
      </div>
      <!-- 右上角内容：操作 -->
      <div style="float:right;">
        <slot name="lesson-card-corner-right-top"/>
      </div>
    </div>
    <slot name="mask" :lesson="lesson"/>
  </el-card>
</template>
<script>
import { mapState } from 'vuex'
import constants from '../../../../../utils/constants'
// import LessonFavorite from '../../lessonLibrary/components/LessonFavorite'
// import LessonLike from '../../lessonLibrary/components/LessonLike'
import LessonMediaViewer from '../../lessonLibrary/components/LessonMediaViewer'
import AdaptLessonButton from '../../lessonPlan/components/AdaptLessonButton'

export default {
  name: 'LessonCard',
  components: {
    LessonMediaViewer,
    AdaptLessonButton
    // LessonLike,
    // LessonFavorite
  },
  props: ['lesson', 'isDraft'],
  computed: {
    // 测评点排序
    measures () {
      let measures = this.lesson.measure || []
      if (measures && measures.length > 0) {
        measures.sort((a, b) => {
            return a.toLowerCase().localeCompare(b.toLowerCase())
        })
      }
      return measures
    },
    // 封面媒体地址，取最后一个
    coverURL () {
      // 课程默认封面：
      if (!this.lesson.coverMediaUrls || (this.lesson.coverMediaUrls.length === 0 && (!this.lesson.coverExternalMediaUrl || this.lesson.coverExternalMediaUrl.length === 0))) {
        return constants.lessonDefaultCoverURL
      }
      return this.lesson.coverMediaUrls && this.lesson.coverMediaUrls[this.lesson.coverMediaUrls.length - 1]
    },
    ...mapState({
      currentUser: state => state.user.currentUser,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      currentPluginUser: state => state.cgAuth.user // 当前插件用户
    })
  },
  data () {
    return {
      avatarURL: constants.userAvatarURL
    }
  },
  methods: {
    parse (ageGroupNames) {
      if (ageGroupNames) {
        return JSON.parse(ageGroupNames)
      } else {
        return ''
      }
    },
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    getType (url) {
      if (!url) {
        return 'unknown'
      }
      url = url.toLowerCase()
      if (url.match('.+\\.(png|jpg|jpeg)')) {
        return 'image'
      }
      if (url.match('.+\\.mp4')) {
        return 'video'
      }
      return 'image'
    },
    formatAgeGroup (lesson) {
      if (!lesson.ages || !lesson.ages.length) {
        return []
      }
      let ages = []
      let textAges = []
      for (let age of lesson.ages) {
        for (let v of age.split(',')) {
          let numberValue = Number(v)
          if (Number.isNaN(numberValue)) {
            textAges.push(v)
          } else {
            ages.push(numberValue)
          }
        }
      }
      ages.sort((a, b) => a - b)
      let result = []
      let start, end
      let appender = this.$t('loc.yearsOld')
      ages.forEach(age => {
        // 初次遍历，初始化start和end
        if (start === undefined) {
          start = end = age
          // 顺序年龄，进行合并
        } else if (age === end + 1) {
          end++
          // 年龄有差值
        } else if (age > end + 1) {
          result.push(`${start}-${end + 1} ${appender}`)
          start = end = age
        }
      })
      if (start !== undefined) {
        result.push(`${start}-${end + 1} ${appender}`)
      }
      result = result.concat(...textAges)
      return result
    },
    // adapt 弹框设置完成后进入 Lesson 编辑页面
    handleLessonAdapt() {
      this.$router.push({
        name: 'EditLesson',
        params: {
          lessonId: this.lesson.id,
          type: 'Draft',
          adaptLesson: true
        }
      })
    }
  },
  filters: {
    formatUserName (name) {
      if (name && name.indexOf('Ms.') === 0) {
        let separator = 'Ms.'
        if (name.replace(separator, '').charAt(0) !== ' ') {
          name = separator + ' ' + name.replace(separator, '')
        }
      } else if (name && name.indexOf('Mr.') === 0) {
        let separator = 'Mr.'
        if (name.replace(separator, '').charAt(0) !== ' ') {
          name = separator + ' ' + name.replace(separator, '')
        }
      }
      return name
    }
  }
}
</script>
<style scoped lang="less">
.lesson-card-header {
  position: relative;
}
.lesson-card-header:hover /deep/ .unit-adapt-btn{
  visibility: visible;
}
.el-card {
  background-color: #fff;
  position: relative;
  border: none;
  overflow: inherit;

  &:hover {
    cursor: pointer;
  }
}

.lesson-framework-name .el-tag {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-card /deep/ .el-card__body {
  padding: 0;

  & > .lesson-name {
    //padding-left: 5px;
    //height: 32px;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    //line-height: 32px;
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
    padding: 0 12px;
  }

  & > .lesson-age-group-framework {
    line-height: 21px;
    min-height: 21px;
    height: 21px;
    overflow: hidden;

    & > * {
      margin-right: 5px;
    }
  }

  & > .lesson-measure {
    line-height: 40px;
    min-height: 40px;
    height: 40px;
    overflow: hidden;
    padding-left: 12px;
    padding-right: 7px;

    & > * {
      margin-right: 5px;
      height: 22px;
    }

  }

  & > .el-divider {
    //margin: 0 2px;
    width: calc(100% - 24px);
    margin: 0 12px;
    background-color: rgba(216, 216, 216, 0.4);
  }

  & > .lesson-card-footer {
    display: grid;
    grid-template-columns: max-content 1fr max-content max-content;
    align-items: center;
    height: 40px;
    padding: 0 12px;
    gap: 5px;

    & > :nth-child(2) {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    & > * {
      margin-left: 0;
    }
  }

  .lesson-avatar {
    img {
      width: 100%;
    }
  }
}
</style>
