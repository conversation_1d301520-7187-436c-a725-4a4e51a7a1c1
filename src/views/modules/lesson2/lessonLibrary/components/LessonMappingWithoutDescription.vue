<template>
  <div style="padding-top: 10px; padding-left: 16px;" class="lesson-detail-ccss">
    <el-row v-if="mappedFrameworkId !== 0 && !lesson.preview"
            :gutter="24" class="remove-margin-r">
      <div v-if="measures.length === 0 && showCoreMeasure" style="margin-left: 10px">
        {{ $t('loc.noCoreMeasureTip') }}
      </div>
      <div v-else>
        <div v-for="(domainAndMapped,domainIndex) in domainMappedMeasuresList" :key="domainIndex">
          <!-- 简写 -->
          <el-col class="lesson-detail-drdp-mapping"
                  :span="2">
            <div style="left: 1px" class="lesson-field-value font-bold fit-width"
                :class="titleOffset(domainAndMapped)" v-if="domainAndMapped.mappedInfo.length !== 0">
              <span v-show="domainAndMapped.domainInfo.abbreviation">{{ domainAndMapped.domainInfo.abbreviation }}
                <!--如果分隔符后面的内容区域不存在，那么也不需要展示分隔符-->
                <span v-show="domainAndMapped.mappedInfo.length !== 0">{{ mappedFrameworkId === 0 || lesson.preview ? ':&nbsp' : '' }}</span>
              </span>
            </div>
          </el-col>
          <!-- DRDP 显示区域, 不来自于预览的时候 -->
          <div v-if="mappedFrameworkId !== 0 && !lesson.preview">
            <el-row :gutter="24">
              <el-col v-for="(otherMeasure,index) in domainAndMapped.mappedInfo"
                      :key="otherMeasure.id"
                      :offset="(index % 2 === 0 && index !== 0) ? 2 : 0"
                      :span="11">
                <div>
                  <div :class="otherMeasure.measures.length !== 0 ? 'lesson-detail-ccss-box':'lesson-detail-no-mapping-ccss-box'" class="ipad-adapter">
                    <el-row v-if="lesson.mappedMeasures && mappedFrameworkId" class="add-padding-t-5">
                      <!--有无映射都要显示关系-->
                      <!-- drdp 名称-->
                      <!-- 没有映射关系需要增加偏移-->
                      <el-col :span="4"
                              v-if="otherMeasure.measures.length !== 0"
                              class="fit-width add-margin-r-5">
                        <span class="fit-width lg-color-text-primary font-size-16 measure-spacing">{{ otherMeasure.drdpMeasure.abbreviation }}
                          <span v-show="coreMeasure[otherMeasure.drdpMeasure.name]" style="color: red">*</span>
<!--                          <span>:</span>-->
                        </span>
                      </el-col>
                      <!-- 如果第一个就没有映射关系，但是第一个前面存在标题，这个时候不需要偏移。没有映射关系需要增加偏移 -->
                      <!--如果上一个是有测评点的，并且上一个测评点是偶数，那么它本身就会存在两个便宜量，那么这里也是不需要再增加偏移量了-->
                      <el-col :span="19"
                              v-else
                              :style="lastNoMappingMeasure(index, domainAndMapped)"
                              class="fit-width add-margin-r-4">
                        <span class="fit-width lg-color-text-primary font-size-16 measure-spacing">{{ otherMeasure.drdpMeasure.abbreviation }}</span>
                        <span v-show="coreMeasure[otherMeasure.drdpMeasure.name]" style="color: red">*</span>
                      </el-col>
                      <!--存在映射关系 thymeleaf-->
                      <!-- 映射关系区域 -->
                      <el-col :span="20" class="display-flex" v-if="otherMeasure.measures.length > 0">
                        <!-- 如果数据量过长，让 tag 换行 -->
                        <!--                      ipad-->
                        <div class="lesson-info-detail-box hidden-md-and-down" :style="{ 'overflow': otherMeasure.show ? 'visible' : 'hidden', 'height': otherMeasure.show ? 'fit-content' : '29px' }">
                          <div v-for="mappedMeasure in otherMeasure.measures" :key="mappedMeasure.id"
                              class="lesson-detail-ccss-tag">
                            <el-tooltip :open-delay="Number(300)" effect="dark" placement="top">
                              <div slot="content" style="width: 200px;font-size: 14px;white-space: pre-wrap;">{{ mappedMeasure.description }}</div>
                              <lesson-mapped-measure :measure="mappedMeasure"/>
                            </el-tooltip>
                          </div>
                        </div>
                        <!--                      web-->
                        <div class="lesson-info-detail-box hidden-lg-and-up" :style="{ 'overflow': otherMeasure.show ? 'visible' : 'hidden', 'height': otherMeasure.show ? 'fit-content' : '29px' }">
                          <div v-for="mappedMeasure in otherMeasure.measures" :key="mappedMeasure.id"
                              class="lesson-detail-ccss-tag">
                            <el-tooltip :open-delay="Number(300)" effect="dark" placement="top">
                              <div slot="content" style="width: 200px;font-size: 14px;white-space: pre-wrap;">{{ mappedMeasure.description }}</div>
                              <lesson-mapped-measure :measure="mappedMeasure"/>
                            </el-tooltip>
                          </div>
                        </div>
                        <!-- 显示更多或更少区域 -->
                        <div class="lesson-detail-ccss-load" v-if="computerShow(otherMeasure, domainIndex, index)">
                          <el-link :underline="false" type="primary" @click="showMore(domainIndex,index)"
                                  class="fixed-show-more-or-less">
                            {{ domainMappedMeasuresList[domainIndex].mappedInfo[index].show ? $t('loc.lessons2ShowLess') : $t('loc.lessons2ShowMore') }}
                            <i class="lg-icon lg-icon-arrow-up"
                                    v-if="domainMappedMeasuresList[domainIndex].mappedInfo[index].show"></i>
                            <i class="lg-icon lg-icon-arrow-down" v-else></i>
                          </el-link>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </el-row>
    <el-row v-if="mappedFrameworkId === 0 || lesson.preview"
            :gutter="24" class="remove-margin-r flex-warp">
      <!-- 简写 -->
      <div v-if="measures.length === 0 && showCoreMeasure">
        {{ $t('loc.noCoreMeasureTip') }}
      </div>
      <div style="width: 100%" v-else>
        <el-col v-for="(domainAndMapped,domainIndex) in domainMappedMeasuresList" :key="domainIndex"
                :class="domainAndMapped.mappedInfo.length !== 0 ? 'lesson-detail-no-drdp-mapping':''"
                :span="12">
          <div class="lesson-field-value font-bold fit-width text-nowrap"
              :class="titleOffset(domainAndMapped)"  v-if="domainAndMapped.mappedInfo.length !== 0">
            <!--如果缩写存在，那么才展示缩写和分隔符-->
            <span v-show="domainAndMapped.domainInfo.abbreviation">{{ domainAndMapped.domainInfo.abbreviation }}
              <!--如果分隔符后面的内容区域不存在，那么也不需要展示分隔符-->
              <span v-show="domainAndMapped.mappedInfo.length !== 0">{{ mappedFrameworkId === 0 || lesson.preview ? ':&nbsp' : '' }}</span>
            </span>
          </div>
          <!--如果 mappedFrameworkId 是 0 ，说明关闭了按钮，那么就应该展示为一行 -->
          <div class="lesson-field-value add-padding-r-20" v-if="domainAndMapped.mappedInfo.length !== 0" v-html="mappedInfoDrdpMeasureAbbreviation(domainAndMapped)"></div>
        </el-col>
      </div>
    </el-row>
  </div>
</template>
<script>
import LessonMappedMeasure from '@/views/modules/lesson2/lessonLibrary/components/LessonMappedMeasure'
import LessonMappingBase from '@/views/modules/lesson2/lessonLibrary/components/LessonMappingBase'

export default {
  name: 'LessonMappingWithoutDescription',
  mixins: [LessonMappingBase],
  components: { LessonMappedMeasure },
  props: ['lesson', 'mappedFrameworkId', 'plfMeasuresList', 'showCoreMeasure'],
  methods: {
    computerShow (otherMeasure, domainIndex, index) {
      return otherMeasure.measures.length > this.defaultCount
    },
    titleOffset (domainAndMapped) {
      if (this.mappedFrameworkId !== 0 && !this.lesson.preview) {
        if (this.allNullOtherMeasure(domainAndMapped)) {
          return 'line-height-80'
        } else {
          return 'relative-top-5'
        }
      }
    },
    allNullOtherMeasure (domainAndMapped) {
      let result = false
      domainAndMapped.mappedInfo.forEach(item => {
        if (item.measures.length > 0) {
          result = true
        }
      })
      return result
    },
    lastNoMappingMeasure (index, domainAndMapped) {
      let mappedInfosLength = domainAndMapped.mappedInfo.length
      let style = 'margin-bottom: 15px'
      if (index === domainAndMapped.mappedInfo.length - 1) {
        return style
      }
      // 如果是偶数，那么最后两个应该添加，如果是奇数，那么最后一个增加
      if (mappedInfosLength % 2 === 0 && index === domainAndMapped.mappedInfo.length - 2) {
        return style
      }
    },
    showPlaceholder (otherMeasure) {
      // 数量是偶数, 并且show more 打开的时候可能会遮住 show more，那么这个时候负责占位
      // 判断最后一个需要展示的元素的长度是不是过长
      // 如果没有打开 show more，那么不需要占位
      if (!otherMeasure.show) {
        return false
        // 如果打开 show more，那么并且数量是偶数,那么这个时候负责占位
      } else if (otherMeasure.measures.length !== 2 && otherMeasure.measures.length % 2 === 0) {
        return true
        // 如果打开 show more，不满足上一步,但是字符过长,两个可能在一行,这个时候也需要展示占位符
      } else if (otherMeasure.measures.length > 2 &&
        otherMeasure.measures[otherMeasure.measures.length - 1].abbreviation.length +
        otherMeasure.measures[otherMeasure.measures.length - 2].abbreviation.length > 30) {
        return true
      }
    }
  }
}
</script>
