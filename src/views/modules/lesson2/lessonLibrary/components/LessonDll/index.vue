<template>
  <div class="lesson-dll" style="margin: 60px 0" v-if="lesson && lesson.dlls && lesson.dlls.length > 0">
    <div style="font-size: 24px;" >
      <img src="@/assets/img/lesson2/lessons2-lesson-resources.png"
           class="lessons2-lesson-resources"  :alt="$t('loc.lessons2LessonDLLTitle')"/>
      <div class="lessons2-lesson-resources-dll lessons2-lesson-resources-info">
        <span>{{ $t('loc.lessons2LessonDLLTitle') }}</span>
<!--        <dll-tips/>-->
      </div>
    </div>
    <!-- DLL 模块的标题-->
    <div style="margin: 10px 24px" class="display-flex justify-content-between">
      <span class="color-323338 text-bolder font-size-16">{{ lesson.name }} <span class="color-676879">({{ dlls.length }})</span></span>
      <el-button plain style="height: 36px" @click="openDLLDemoDialog()">
        <div class="display-flex align-items" style="gap: 5px">
          <span>{{ $t('loc.dll3') }}</span>
          <i class="lg-icon lg-icon-arrow-right font-size-20"></i>
        </div>
      </el-button>
    </div>
    <!-- DLL 列表展示 -->
    <lesson-detail-dll-list :dlls="dlls" @toDllPreview="openDLLDemoDialog"/>
    <!-- DLL 演示的弹框 -->
    <DLLHomeworkDemoDialog ref='dllHomeworkDemoDialog'></DLLHomeworkDemoDialog>
  </div>
</template>

<script>
import LessonDetailDllList from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/LessonDetailDllList'
import DllTips from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/DllTips'
import DLLHomeworkDemoDialog from '@/views/dll/DLLHomeworkDemoDialog.vue'

export default {
  name: 'LessonDetailDll',
  props: ['lesson'],
  components: {
    DLLHomeworkDemoDialog,
    LessonDetailDllList,
    DllTips
  },
  data () {
    return {
      dlls: []
    }
  },
  watch: {
    'lesson.dlls': {
      immediate: true,
      handler (value) {
        this.dlls = (value || []).map(dll => {
          this.$set(dll, 'voiceLoading', false)
          dll.title = this.lesson.name
          dll.mediaModels = dll.medias
          dll.contentModels = dll.languages
          dll.contentModels.forEach(content => {
            this.$set(content, 'voiceLoading', false)
          })
          return {
            ...dll
          }
        })
      }
    }
  },
  methods: {
    // 打开 DLL 演示的弹框
    openDLLDemoDialog (index) {
      // 如果没有传入 index，则默认为第一个
      if (index === undefined) {
        index = 0
      }
      // 判断是否有 DLL 内容
      if (this.dlls.length === 0) {
        this.$message.error(this.$t('loc.plan36'))
        return
      }
      this.$refs.dllHomeworkDemoDialog && this.$refs.dllHomeworkDemoDialog.show(this.dlls, index,this.lesson.name)
    }
  }
}
</script>

<style scoped lang="less">
.lesson-dll {
  position: relative;
  margin: 27.5px auto 0;
  border-radius: 4px;
  background: #FFE8B9;
  padding: 48px 0 30px;
  .lessons2-lesson-resources {
    position: relative;
    top: 19px;
  }
  .lessons2-lesson-resources-dll {
    display: flex;
    justify-content: center;
    gap: 6px;
  }
  .lessons2-lesson-resources-info {
    position: relative;
    bottom: 39px;
    width: 467px;
    color: #FFFFFF;
  }
  & > :first-child {
    width: 467px;
    height: 58px;
    position: absolute;
    left: calc(50% - 233.5px);
    top: -29px;
    font-size: 16px;
    text-align: center;
    line-height: 58px;
  }
}

</style>