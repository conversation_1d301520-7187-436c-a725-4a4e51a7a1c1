<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="mediaSpan" v-if="mediaURL">
        <lesson-media-viewer :url="mediaURL"/>
      </el-col>
      <el-col :span="mediaURL ? translationListSpan : 24">
        <div class="dll-item-header">
          <span class="color-676879">{{ $t('loc.lessons2LessonDLLAndTitle') }}</span>
          <div>
            <slot name="edit"></slot>
            <dll-speaker class="video-play" :content="dll.content" :lang-code="langCode"/>
          </div>
        </div>
        <div>
          <div class="display-flex flex-direction-col">
            <span class="color-323338 text-bolder" style="font-size: 16px; white-space: pre-wrap">{{ dll.content }}</span>
            <span class="color-676879" style="font-size: 14px; white-space: pre-wrap">{{ dll.description }}</span>
          </div>
          <!--翻译列表折叠框-->
          <translation-collapse :languages="dll.languages" :langs="languages"/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import DllSpeaker from '@/views/modules/lesson2/lessonLibrary/editor/dll/DllSpeaker'
import TranslationCollapse from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/TranslationCollapse'
import LessonMediaViewer from '@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer'

export default {
  name: 'DllItem',
  components: {
    DllSpeaker,
    TranslationCollapse,
    LessonMediaViewer
  },
  props: {
    dll: {
      type: Object
    },
    mediaSpan: {
      type: Number,
      default: 7
    },
    translationListSpan:{
      type: Number,
      default: 17
    },
    languages: Array
  },
  data () {
    return {}
  },
  computed:{
    mediaURL() { // dll 图片 URL
      return this.dll && this.dll.medias && this.dll.medias[0] && this.dll.medias[0].mediaUrl;
    },
    langCode(){ // dll 原语言代码
      if (this.dll.langCode) {
        return this.dll.langCode;
      }
      if (!this.dll.language || !this.languages) {
        return 'en';
      }
      let language = this.languages.find(({code = '', ttsCode=''}) => {
        return [code, ttsCode].some(item => item === this.dll.language);
      });
      return language && language.code || 'en';
    },
  }
}
</script>

<style lang="less" scoped>
.dll-item-header {
  display: flex;
  justify-content: space-between;
  height: 25px;
  & > :first-child {
    font-size: 16px;
    color: #999;
  }

  & > div{
    display: flex;
    align-items: center;
  }
}

</style>