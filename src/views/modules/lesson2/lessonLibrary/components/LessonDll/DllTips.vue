<template>
  <el-tooltip effect="light" placement="right" popper-class="atooltip" style="font-size: 16px; line-height: 60px;">
      <i class="el-icon-question"/>
      <div :style="'width:'+tipWidth()+'px'" slot="content" style="padding: 24px">
        <div style="font-size: 16px;font-weight: bold;">
          {{$t('loc.DLLVocabularyAndPhrases')}}
        </div>
        <div style="font-size: 14px;margin-top: 10px">{{$t('loc.lessons2LessonDLLSettingHelp1')}}</div>
        <div style="font-size: 14px;margin-top: 10px">{{$t('loc.DLLHelpTwo')}}</div>
        <el-row :gutter="10" style="margin-top: 10px">
          <el-col :span="12">
            <img :width="tipWidth()/2 - 10" src="../../../../../../assets/img/lesson2/icon_dll_list.png" alt=""/>
          </el-col>
          <el-col :span="12">
            <img :width="tipWidth()/2 - 10" src="../../../../../../assets/img/lesson2/icon_dll_add.png" alt=""/>
          </el-col>
        </el-row>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'DllTips' ,
  methods:{
    tipWidth() {
      let clientWidth = document.body.clientWidth;
      if (clientWidth >1199){
        return 520;
      }else {
        return 347;
      }
    }
  }
}
</script>

<style>
.atooltip {
  padding: 0 !important;
  background: white !important;
  border-color: white !important;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}

.atooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow {
  border-top-color: white;
}

.atooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow:after {
  border-top-color: white;
}

.atooltip.el-tooltip__popper.is-light[x-placement^=bottom] .popper__arrow {
  border-bottom-color: white;
}
.el-tooltip__popper.is-light[x-placement^=bottom] .popper__arrow:after {
  border-bottom-color: white;
}

.atooltip.el-tooltip__popper[x-placement^="right"] .popper__arrow {
  border-right-color: white;
}

.atooltip.el-tooltip__popper[x-placement^="right"] .popper__arrow:after {
  border-right-color: white;
}

.atooltip.el-tooltip__popper[x-placement^="left"] .popper__arrow {
  border-left-color: white;
}

.atooltip.el-tooltip__popper[x-placement^="left"] .popper__arrow:after {
  border-left-color: white;
}
</style>
