<template>
  <div>
    <!--翻译列表折叠框-->
    <div class="selected-header" @click="isFold = !isFold" v-if="languages && languages.length > 0">
      <span>{{ $t('loc.selectedLanguages') }}: {{ languages && languages.length }}</span>
      <el-icon :class="[`el-icon-arrow-${isFold ? 'down' : 'right'}`]"></el-icon>
    </div>
    <!--翻译列表-->
    <div v-if="isFold" class="selected-content" :style="{background: backgroundColor}">
      <div v-for="(language,index) in languages">
        <div class="translate-item">
          <div>
            <span>{{ language.lang }}</span><span>{{ ` (${getLanguageName(language)})`}}</span>
            <dll-speaker class="video-play" :content="language.content" :lang-code="language.langCode"/>
          </div>
          <span style="white-space: pre-wrap">{{ language.content }}</span>
        </div>
        <el-divider class="lesson-dll-divider" v-if="!(index === languages.length - 1)"/>
      </div>
    </div>
  </div>
</template>

<script>
import DllSpeaker from '@/views/modules/lesson2/lessonLibrary/editor/dll/DllSpeaker'

export default {
  name: 'TranslationCollapse',
  components: {
    DllSpeaker
  },
  props: {
    languages: {
      type: Array,
      default: function () {
        return []
      }
    },
    backgroundColor: { // 翻译列表背景色
      type: String,
      default: '#f8f8f8'
    },
    langs:{ // 语言信息
      type:Array,
      default(){
        return [];
      }
    }
  },
  data() {
    return {
      openName: ["translation"],
      isFold: false // 是否折叠
    }
  },
  methods:{
    getLanguageName(translation) {
      if (translation.lang_en) {
        return translation.lang_en;
      }
      if (!this.langs) {
        return '';
      }
      let language = this.langs.find(item => item.originalName === translation.lang || item.name === translation.lang);
      return language && language.name || '';
    }
  }
}
</script>

<style lang="less" scoped>

.lesson-dll-divider {
  margin: 0;
  background-color: #e4e7ed80;
}

// 折叠头部及折叠列表样式
.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  cursor: pointer;
  font-size: 16px;

  & > :first-child {
    color: #10b3b7
  }

  & > :last-child {
    color: #999
  }
}

.selected-content {
  font-size: 16px;
}

.translate-item {
  padding: 10px 10px;

  & > :first-child {
    & > :first-child {
      font-weight: bolder;
      font-size: 16px;
    }

    & > :nth-child(2) {
      font-size: 16px;
    }
  }

  & > :last-child {
    font-size: 16px;
  }
}

.video-play {
  float: right;
  vertical-align: middle;
}
</style>