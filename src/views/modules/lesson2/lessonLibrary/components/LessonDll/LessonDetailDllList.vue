<template>
  <div class="dll-body">
    <div class="dll-list">
      <div style="height: 188px;" v-for="(item, index) in initMaxLimit" @click="toDllPreview(index)" :key="index" class="dll-item border-radius-4 lg-pointer">
        <div class="dll-cover-container add-padding-lr-10" v-if="!item.medias || item.medias.length === 0" :title="item.content">
          <div class="content">{{ item.content }}</div>
        </div>
        <img v-if="item.medias && item.medias.length > 0" :src="item.medias && item.medias[0] && item.medias[0].mediaUrl" alt="" class="w-full dll-cover">
        <div style="height: 54px;line-height: 54px;text-align: center; background: var(--color-page-background-white);" class="w-full">
          <div class="font-size-16 font-weight-600 valouries-content add-padding-lr-10" :title="item.content">{{ item.content }}</div>
        </div>
      </div>
      <div class="display-flex justify-content w-full" v-if="isAppear && dlls.length > 8">
        <span class="text-primary lg-pointer font-weight-600" @click="showMore()">{{ $t('loc.dll1') }}</span>
      </div>
      <div class="display-flex justify-content w-full" v-if="!isAppear && dlls.length > 8">
        <span class="text-primary lg-pointer font-weight-600" @click="showLess()">{{ $t('loc.dll2') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import LessonMediaViewer from '@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer'
import DllSpeaker from '@/views/modules/lesson2/lessonLibrary/editor/dll/DllSpeaker'
import TranslationCollapse from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/TranslationCollapse'
import DllItem from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/DllItem'

export default {
  name: 'DllDetailItem',
  props: ['dlls'],
  components: {
    DllItem,
    LessonMediaViewer,
    DllSpeaker,
    TranslationCollapse
  },
  data () {
    return {
      initMaxLimit: [], // 初始 DLL 最大限制
      isAppear: true // 是否显示查看更多
    }
  },
  mounted () {
    // 如果 DLL 数量小于等于 8，则显示全部，且隐藏查看更多
    if (this.dlls.length <= 8) {
      this.initMaxLimit = this.dlls
      this.isAppear = false
      return
    }
    // 如果 DLL 数量大于 8，则只显示 8 个，否则显示全部
    this.initMaxLimit = this.dlls.length > 8 ? this.dlls.slice(0, 8) : this.dlls
  },
  methods: {
    unfold (dll, index) {
      this.$set(this.dlls, index, { ...dll, isUnfold: !dll.isUnfold })
    },
    // 显示更多
    showMore () {
      // 如果点击查看更多，则显示全部
      this.initMaxLimit = this.dlls
      // 隐藏查看更多
      this.isAppear = false
    },
    // 收起
    showLess () {
      this.initMaxLimit = this.dlls.slice(0, 8)
      this.isAppear = true
    },
    // 跳转 DLL 演示
    toDllPreview (index) {
      this.$emit('toDllPreview', index)
    }
  }
}
</script>

<style scoped lang="less">

.dll-detail-body /deep/ & {
  padding: 0 34px;

  .selected-header {
    background-color: #f8f8f8;
    padding: 5px 10px;
    border-radius: 4px;
  }
}
.dll-body {
  padding: 10px 24px;
}
.dll-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .dll-item {
    margin-right: 24px;
    width: calc((100% - 75px) / 4);
    margin-bottom: 20px;
    .dll-cover {
      object-fit: cover;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      height: 134px;
    }
    .dll-cover-container {
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      height: 134px;
      background-color: var(--color-white);
      font-size: 24px;
      color: var(--color-text-primary);
      font-weight: bolder;
      line-height: 134px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px;
      box-sizing: border-box;
      overflow: hidden;
      text-align: center;
      position: relative;
      .content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5em;
        max-height: 4.5em;
      }
    }
    .valouries-content {
      white-space: pre;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .dll-item:nth-of-type(4n+0) {
    margin-right: 0;
  }
}
</style>