<template>
  <div class="dll-content-play">
    <span :class="[`${play?'voice-playing':'show-play-icon'}`]" style="margin-right: 3px"></span>
    <span>{{title}}</span>
  </div>
</template>

<script>
export default {
  name: 'VoicePlay',
  props: {
    title:{           // 播放按钮标题
      type: String,
      default: 'Play'
    },
    play:{
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped lang="less">
.dll-content-play {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #10B3B7;
  text-align: center;
  cursor: pointer;
}
</style>