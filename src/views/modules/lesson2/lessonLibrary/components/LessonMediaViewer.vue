<template>
  <div class="media-viewer">
    <div style="position: relative;padding-top: 56.25%; height:100%">
      <div class="media-viewer__inner">
        <!--如果用户上传的是图片资源或图片链接-->
        <el-image v-if="(!failed && _type === 'image') || (!failed &&_type === 'externalMediaImg')" :src="url"
                  :fit="fit|| 'cover'" @error="imageLoadError"/>
        <!-- 添加特殊课程标签 -->
        <span v-if="isAdaptedLesson" class="special-tag">
            {{ $t('loc.adaptedLesson') }}
        </span>
        <!--如果用户上传的是视频资源-->
        <video v-if="!failed && _type === 'video'" :src="url" controls :style="{objectFit: fit||'cover'}"
               ref="videoElement"
               @play="handlePlay($event);" @click.stop @error="videoLoadError()" disablePictureInPicture/>
        <!--如果用户上传的是视频链接-->
        <iframe class="iframeStyle"
                :src="url"
                width="640"
                height="360"
                allowfullscreen
                v-if="!failed && _type === 'externalMedia'">
        </iframe>
        <div class="media-viewer__error" v-if="failed" style="background-color: #e7e7e7">
          <el-image style="width: 35%" :src="errorImageURL" :fit="fit || 'cover'"/>
          <span>{{ $t('loc.lesson2FailedLoadingMedia') }}</span>
        </div>
      </div>
      <!-- 来自 Unit 的标签 -->
      <div v-if="unitTitle" class="media-viewer__unit">
        <svg class="unit-icon lg-icon" style="flex: none;" width="16" height="16" aria-hidden="true">
            <use xlink:href="#lg-icon-a-unitpaln"></use>
        </svg>
        <span class="overflow-ellipsis" :title="unitTitle">{{ $t('loc.fromUnit') + ' ' + unitTitle }}</span>
      </div>

      <div v-if="source" class="media-viewer__source">
        Image Source: {{ source }}
      </div>
    </div>
  </div>
</template>

<script>
import errorImage from '@/assets/img/lesson2/image.png'
import errorVideoImage from '@/assets/img/lesson2/Video.png'

export default {
  name: 'LessonMediaViewer',
  props: [
    'url',
    'fit',
    'type', // image 图片、video 视频
    'failedType', // 加载失败时，显示图片类型：image 图片、video 视频,默认根据type设置
    'source', // 来源
    'isAdaptedLesson', // 是否显示特殊课程标签，true 显示特殊课程标签， false 不显示特殊课程标签
    'unitTitle' // 单元标题，如果有值则显示"From Unit: xxx"标签
  ],
  computed: {
    _type () {
      if (!this.url) {
        return 'unknown'
      }
      if (this.type) {
        return this.type
      }
      return /.+(\.jpeg|\.png|\.jpg|\.webp)/i.test(this.url) && 'image' || // 以资源的形式上传的图片
        /.+(\.mp4|\.mov)/i.test(this.url) && 'video' || // 以资源的形式上传的视频
        /^https:\/\/i.vimeocdn.com\/video\/.*/.test(this.url) && 'externalMediaImg' ||
        /^https:.*/.test(this.url) && 'externalMedia' || // 以链接的形式上传的视频
        'unknown' // 未知
    }
  },
  data () {
    return {
      failed: false,
      errorImageURL: null
    }
  },
  watch: {
    url () {
      this.failed = false
    }
  },
  beforeDestroy () {
    if (this.$refs.videoElement) {
      this.$refs.videoElement.pause()
      this.$refs.videoElement.remove()
    }
  },
  methods: {
    handlePlay (evt) {
      // 暂停其他播放
      let previous = window.lessonVideo
      if (previous && previous !== evt.target) {
        previous.pause()
      }
      window.lessonVideo = evt.target
    },
    imageLoadError () {
      this.failed = true
      this.errorImageURL = this.getErrorImageURL(errorImage)
    },
    videoLoadError () {
      this.failed = true
      this.errorImageURL = this.getErrorImageURL(errorVideoImage)
    },
    getErrorImageURL (defaultValue) {
      if (!this.failedType) {
        return defaultValue
      }
      if (this.failedType === 'image') {
        return errorImage
      }
      return errorVideoImage
    }
  }
}
</script>

<style scoped lang="less">
.media-viewer {
  overflow: hidden;
}

.media-viewer__inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  & > * {
    height: 100%;
    width: 100%;
  }

  .media-viewer__error {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-flow: column;
    background-color: #f2f2f2;
    color: #999;
  }

  .iframeStyle {
    z-index: 1;
    opacity: 1;
    border: 0px;
  }

  .special-tag {
    position: absolute;
    top: 0px; /* 调整垂直位置 */
    left: 0px; /* 调整水平位置 */
    width: fit-content; /* 设置宽度 */
    height: auto; /* 设置高度 */
    //line-height: 24px; /* 设置行高 */
    text-align: center; /* 文字居中 */
    border-radius: 0 0 8px 0; /* 设置边框半径 */
    padding: 6px 8px; /* 设置内边距 */
    background-color: #878BF9; /* 设置背景颜色 */
    color: #ffffff; /* 设置文字颜色 */
    font-size: 12px; /* 设置字体大小 */
    font-family: 'Inter', sans-serif; /* 设置字体为Inter */
    font-weight: 600; /* 设置字体粗细 */
  }
}

.media-viewer:hover .media-viewer__source {
  display: block;
}

.media-viewer__source {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #3A3F51B2;
  color: #fff;
  padding: 8px 10px;
  line-height: 16px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.media-viewer__unit {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #3A3F51B2;
  color: #fff;
  padding: 8px 10px;
  line-height: 16px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.unit-icon {
  display: flex;
  align-items: center;
}
</style>
