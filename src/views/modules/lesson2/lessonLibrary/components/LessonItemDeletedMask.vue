<template>
  <div v-if="show" @click.stop="maskClickedHandler">
    <lesson-item-mask class="lesson-item-delete-mask">
      <div style="width: 195px;margin: 0 auto 18px;text-align: center;">
        {{ $t('loc.lessons2DeleteLessonTips') }}
      </div>
      <el-button type="info" @click="removeLessonHandler" size="medium">
        {{ $t('loc.remove') }}
      </el-button>
    </lesson-item-mask>
  </div>
</template>

<script>
import Mask from "../../component/mask";
import Api from "../../../../../api/lessons2";

export default {
  name: "LessonItemDeletedMask",
  components: {LessonItemMask: Mask},
  props: ['lesson', 'favorite'],
  computed: {
    show() {
      return this.lesson && (this.lesson.deleted || this.lesson.inactive);
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    removeLessonHandler() {
      if (this.loading) {
        return;
      }
      Api.cancelFavorite(this.lesson.id)
        .then(() => {
          this.$emit("removed", this.lesson);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    maskClickedHandler(evt) {
      evt.stopPropagation();
    }
  }
}
</script>

<style scoped lang="less">
.lesson-item-delete-mask /deep/ & {
  display: flex;
  flex-flow: column;
  justify-content: center;
  cursor: auto;
  align-items: center;

  & > :first-child {
    color: #fff;
  }
}
</style>