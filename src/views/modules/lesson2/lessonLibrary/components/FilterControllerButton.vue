<template>
  <!-- 过滤器按钮容器 -->
  <el-button
    plain
    :type="showFilter ? 'primary' : 'normal'"
    :style="showFilter ? 'background: #DDF2F3;' : ''"
    @click="handleClick"
    class="filter-controller-button"
  >
    <!-- 过滤器图标和文本 -->
    <i class="lg-icon lg-icon-Filter"></i>
    <span>{{ $t('loc.filters') }}</span><span v-show="activeFiltersCount > 0">&nbsp;({{ activeFiltersCount }})</span>
  </el-button>
</template>

<script>

const ignoreKeys = ['orderKey', 'keyword', 'mappedDrdpFrameworkIds']

export default {
  name: 'FilterControllerButton',
  
  // 组件属性定义
  props: {
    // 控制过滤器显示状态
    showFilter: {
      type: Boolean,
      default: false
    },
    // 过滤参数对象
    params: {
      type: Object,
      default: {}
    }
  },

  computed: {
    // 计算激活的过滤条件数量
    activeFiltersCount() {
      if (!this.params) return 0;
      
      let count = 0;
      // 遍历过滤参数
      for (const key in this.params) {
        // 排除 ignoreKeys 和空值
        if (!ignoreKeys.includes(key) &&
            this.params[key] &&
            Array.isArray(this.params[key]) &&
            this.params[key].length > 0) {
          count += this.params[key].length
        }
      }
      return count;
    }
  },

  // 组件方法
  methods: {
    // 点击按钮时触发事件
    handleClick() {
      this.$emit('toggle-filter')
    }
  }
}
</script>

<style scoped lang="less">
</style>