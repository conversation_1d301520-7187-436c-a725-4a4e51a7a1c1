<template>
  <div v-show="showDetail">
    <div v-if="isShowWeb">
      <el-dialog :visible.sync="showDetail" :show-close="false"
                 width="1150px"  top="30px" bottom="2px"
                 append-to-body
                 :close-on-press-escape="!dsiableEscClose"
                 class="el-dialog-lesson-detail scrollbar" ref="dialog" >
        <div slot="title" v-if="newTabTo && showNewTab()">
          <!-- <new-tab v-if="!isCG && !isCurriculumPlugin" :to="{name:newTabTo,params:{lessonId:lessonId}}"/> -->
        </div>
        <div v-if="downloadLesson" class="preview-text">
          {{ $t('loc.preview') }}
        </div>
        <div v-if="downloadLesson" class="download-btn-container">
          <lesson-download
            :preview="true"
            @click.native="handleDownloadClick"
            class="download-btn"
            :lesson-id="lessonId"
            :lesson-name="lessonName"
            :lesson="lesson"
            :lesson-framework-id="lessonFrameworkId"
            @beforeDownload="handleBeforeDownload"
          />
        </div>
        <slot v-if="showDetail"/>
        <button
          type="button"
          class="el-dialog__headerbtn"
          aria-label="Close"
          @click="handleClose">
          <i class="el-dialog__close el-icon el-icon-close"></i>
        </button>
      </el-dialog>
    </div>
    <div v-if="!isShowWeb">
      <el-dialog :visible.sync="showDetail" :show-close="false" :close-on-press-escape="!dsiableEscClose"
                 width="800px"  top="30px" bottom="2px"
                 class="el-dialog-lesson-detail scrollbar" ref="dialog">
        <!-- iPad 显示课程详情时不显示 New Tab ， 保持边距一致即可-->
        <div slot="title">
        </div>
        <div v-if="downloadLesson" class="preview-text">
          {{ $t('loc.preview') }}
        </div>
        <div v-if="downloadLesson" class="download-btn-container">
          <lesson-download
            :lesson-id="lessonId"
            :lesson-name="lessonName"
            :lesson-framework-id="lessonFrameworkId"
            :lesson="lesson"
            @beforeDownload="handleBeforeDownload"
          />
        </div>
        <slot v-if="showDetail"/>
        <button
          type="button"
          class="el-dialog__headerbtn"
          aria-label="Close"
          @click="handleClose">
          <i class="el-dialog__close el-icon el-icon-close"></i>
        </button>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// import NewTab from './NewTab'
import tools from '@/utils/tools'
import { mapState } from 'vuex'
import LessonDownload from './LessonDownload'

export default {
  name: 'LessonDetailDialog',
  props: ['show', 'lessonId', 'newTabTo', 'downloadLesson', 'lessonName', 'lessonFrameworkId', 'showHeader', 'dsiableEscClose', 'lesson'],
  components: {
    // NewTab,
    LessonDownload
  },
  data () {
    return {
      isShowWeb: true,
      showDetail: this.show,
      fullWidth: 0,
      // 确保每次展示，都重新创建子组件
      key: 0
    }
  },
  computed: {
    ...mapState({
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      isCG: state => state.curriculum.isCG // 是否是 Curriculum Genie 平台
    })
  },
  watch: {
    show (value) {
      value && this.key++
      this.fullWidth = document.documentElement.clientWidth
      // 页面宽度小于1200px时，不显示地图
      if (this.fullWidth < 1200) {
        this.isShowWeb = false
      } else {
        this.isShowWeb = true
      }
      this.showDetail = value
      value && this.$nextTick(() => {
        if (!this.$refs.dialog) {
          return
        }
        this.$refs.dialog.$el.scrollTop = 0
      })
    },
    showDetail (value) {
      this.$emit('update:show', value)
    }

  },
  methods: {
    handleDownloadClick() {
      if (this.$route.name === 'AddLesson') {
        // 创建课程预览下载点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_cre_pre_click_down')
      } else if (this.$route.name === 'EditLesson') {
        // 编辑课程预览下载点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_edit_pre_click_down')
      }
    },
    handleClose () {
      this.showDetail = false
    },
    showNewTab () {
      return !tools.isComeFromIPad()
    },
    // 处理下载前的事件
    handleBeforeDownload(callbacks) {
      this.$emit('beforeDownload', callbacks)
    }
  }
}
</script>

<style scoped lang="less">
@media only screen and (max-width:1199px){
  .el-dialog__headerbtn-iPad{
    width: 40px;
    height: 40px;
    font-size: 25px;
  }
}
@media only screen and (min-width:1200px){
  .el-dialog__headerbtn-web{
    width: 55px;
    height: 55px;
    font-size: 40px;
  }
}
.el-dialog-lesson-detail /deep/ & {

  & > .el-dialog {
    & > .el-dialog__header {
      padding: 0;

      & > :first-child {
        height: 56px;
        line-height: 56px;
        font-size: 16px;
        text-align: right;
        margin-right: 60px;
      }
    }

    & > .el-dialog__body {
      padding: 0 0 30px;
    }
  }
}
.el-dialog__headerbtn {
  position: fixed;
  width: 55px;
  height: 55px;
  border-radius: 0 0 0 100%;
  background-color: rgba(0, 0, 0, 0.55);
  top: 0;
  right: 8px;
  font-size: 40px;
  color: #fff;
  z-index: 9999;

  & > .el-dialog__close {
    position: relative;
    top: -5px;
    right: -5px;
    color: #fff;
  }
}

.download-btn-container {
  position: absolute;
  top: 24px;
  right: 30px;
}

/deep/ .download-btn {
  height: 36px;
}

.preview-text {
  position: absolute;
  top: 16px;
  left: 30px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
</style>
