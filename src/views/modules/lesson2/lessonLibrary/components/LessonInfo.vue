<template>
  <div>
    <div v-if="lesson" class="lesson-info">
      <el-row type="flex" class="">
        <!-- 课程封面 -->
        <el-col :span="12">
          <lesson-media-viewer :url="coverURL"/>
        </el-col>
        <el-col :span="12" style="display: flex; flex-flow: column nowrap; justify-content: center;">
          <!-- 课程名 -->
          <div class="lesson-name" :title="lesson.name" :style="{color:!lesson.name && '#C0C4CC'}">
            {{ lesson.name || 'Lesson Name' }}
          </div>
          <!--作者和时间应该放在一行-->
          <el-row class="lesson-author-date-row">
            <!-- 最新更新时间 -->
            <div class="lesson-field-value" >
              {{ $moment(lesson.updateTime).format('MMM DD, YYYY') }}
            </div>
            <!-- 作者名 -->
            <div class="lesson-author-name" :title="lesson.authorName">
              {{ $t('loc.lessons2LessonDetailToAuthorName') }} {{ lesson.authorName | formatUserName}}
            </div>
          </el-row>
          <!-- 分隔线 -->
          <el-divider class="split-line"/>
          <el-col :span="24" class="lesson-field-label-age-topic">
            <!-- 年龄组 -->
            <div class="lesson-field-label lesson-age-field-label">
              <el-col class="lesson-age-field-value radius radius-30"
                      :span="5"
                      v-if="lesson.ages && lesson.ages.length > 0"
                      v-for="(age) in lesson.ages"
                      :key="age">
                {{ age }}
              </el-col>
              <!-- 课堂类型标签 -->
              <el-col class="lesson-age-field-value radius radius-30"
                      :span="5"
                      v-if="showClassroomType">
                {{ getClassroomTypeLabel(lesson.classroomType) }}
              </el-col>
              <!-- 课程主题 -->
              <el-col class="lesson-topic-field-value radius radius-30"
                      v-for="theme in lesson.themes"
                      v-if="lesson.themes && lesson.themes.length > 0"
                      :span="5"
                      :key="theme.id">
                {{ theme.name }}
              </el-col>
            </div>

          </el-col>
          <el-row class="lesson-field-time ipad_width add-padding-t-10" v-if="lesson.prepareTime || lesson.activityTime">
            <!-- 准备时间 -->
            <el-col class="lesson-field-time-item" v-if="lesson.prepareTime">
              <span class="lesson-field-value">
                {{ $t('loc.lesson2NewLessonFormLabelPrepareTime') }}
              </span>
              <span class="lesson-field-label">
                {{ lesson.prepareTime }} mins
              </span>
            </el-col>
            <!--            :class="{'before-item' : lesson.prepareTime}"-->
            <el-col class="lesson-field-time-item" :class="{'before-item' : lesson.prepareTime}"
                    v-if="lesson.activityTime">
              <!--              <span v-if="planPreview"></span>-->
              <!--               活动时间 -->
              <span class="lesson-field-value">
                {{ $t('loc.lesson2NewLessonFormLabelActivityTime') }}
              </span>
              <span class="lesson-field-label">
                {{ lesson.activityTime }} mins
              </span>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div>
        <!-- 框架 -->
        <div class="lesson-field-label">
          <h1 class="lesson-field-label lesson-title-label" v-if="lesson.framework">
            {{ $t('loc.lesson2NewLessonFormLabelFramework') }}
          </h1>
        </div>
        <div class="lesson-content-value">
          <span class="lesson-field-value">
            {{ lesson.framework && lesson.framework.name }}
          </span>
        </div>
      </div>
      <!--  测评点 与 测评点映射  -->
      <div>
        <div class="lesson-field-label fit-height" v-if="lesson.measures && lesson.measures.length > 0">
          <div class="lesson-field-label core-measure-field">
            <h1 class="lesson-title-label" >
              {{ $t('loc.measures') }}
            </h1>
            <!-- 仅展示核心测评点 switch start -->
            <div class="core-measure" v-show="showCoreMeasureOpen || hasCoreMeasure">
              <span class="lesson-switch">{{ $t('loc.showCoreMeasureOnly') }}</span>
              <el-switch
                @change="changeShowCoreMeasure"
                v-model="showCoreMeasure">
              </el-switch>
            </div>
            <!-- 仅展示核心测评点 switch end -->
          </div>
          <!--          web端-->
          <div style="margin-left: -5px;" class="lesson-content-value flex-align-justify add-padding-t-8 hidden-md-and-down">
            <!--增加按钮-->
            <div class="min-width-270">
              <span class="lesson-switch">{{ $t('loc.ShowDomainOrMeasureDescription') }}</span>
              <el-switch
                v-model="showDomainDescription"
                @change="changeShowDomainDescription"
                unselectable="on" onselectstart="return false;"
                style="-moz-user-select:none;-webkit-user-select:none;">
              </el-switch>
            </div>
            <div v-if="lessonMappedFramework.length === 1 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <span class="lesson-switch">{{
                  getFrameworkMappedTitle(lessonMappedFramework[0])
                }}</span>
              <el-switch
                v-model="mappedFrameworkId"
                :active-value="lessonMappedFramework[0]"
                :inactive-value="defaultNoMappedFrameworkId"
                unselectable="on" onselectstart="return false;"
                style="-moz-user-select:none;-webkit-user-select:none;">
              </el-switch>
            </div>
            <div v-if="lessonMappedFramework.length === 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0"
                 class="flex-column-center height-36" :style="{'width' : planPreview ? 'min-content' : 'fit-content'}">
              <el-radio-group v-model="mappedFrameworkId">
                <el-radio v-for="frameworkId in lessonMappedFramework" :label="frameworkId" :key="frameworkId"
                          @click.native="switchMappedFramework">
                  {{ getFrameworkMappedTitle(frameworkId) }}
                </el-radio>
              </el-radio-group>
            </div>
            <div style="font-weight: normal;"
                 v-if="lessonMappedFramework.length > 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <span style="margin-right: 10px;font-weight: normal">{{ $t('loc.lessons2AdminLessonsSettingMappingTitle') }}</span>
              <span>
            <el-select v-model="mappedFrameworkId"
                       :title="getFrameworkMappedTitle(mappedFrameworkId)"
                       clearable placeholder="Please select" size="small">
                <el-option
                  v-for="frameworkId in lessonMappedFramework"
                  :key="frameworkId"
                  :label="getFrameworkMappedTitle(frameworkId)"
                  :value="frameworkId">
                </el-option>
            </el-select>
          </span>
            </div>
          </div>
          <!--          ipad端-->
          <div style="" class="display-flex flex-direction-row flex-justify-start hidden-lg-and-up">
            <!--增加按钮-->
            <div style="padding-left: 0px;min-width: 300px;" class="display-flex flex-direction-row justify-content-start">
              <div>
                <span class="lesson-switch">{{ $t('loc.ShowDomainOrMeasureDescription') }}</span>
              </div>
              <div style="margin-left: 4px;">
                <el-switch
                  v-model="showDomainDescription"
                  @change="changeShowDomainDescription"
                  unselectable="on" onselectstart="return false;"
                  style="-moz-user-select:none;-webkit-user-select:none;">
                </el-switch>
              </div>
            </div>
            <div class="display-flex flex-direction-row justify-content-start" v-if="lessonMappedFramework.length === 1 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <div>
                <span class="lesson-switch">{{
                    getFrameworkMappedTitle(lessonMappedFramework[0])
                  }}</span>
              </div>
              <div style="margin-left: 4px;">
                <el-switch
                  v-model="mappedFrameworkId"
                  :active-value="lessonMappedFramework[0]"
                  :inactive-value="defaultNoMappedFrameworkId"
                  unselectable="on" onselectstart="return false;"
                  style="-moz-user-select:none;-webkit-user-select:none;">
                </el-switch>
              </div>
            </div>
            <div v-if="lessonMappedFramework.length === 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0"
                 class="flex-column-center height-36" :style="{'width' : planPreview ? 'min-content' : 'fit-content'}">
              <el-radio-group class="display-flex flex-direction-col" v-model="mappedFrameworkId">
                <el-radio v-for="frameworkId in lessonMappedFramework" :label="frameworkId" :key="frameworkId"
                          @click.native="switchMappedFramework">
                  {{ getFrameworkMappedTitle(frameworkId) }}
                </el-radio>
              </el-radio-group>
            </div>
            <div style="font-weight: normal;"
                 v-if="lessonMappedFramework.length > 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <span style="margin-right: 10px;font-weight: normal">{{ $t('loc.lessons2AdminLessonsSettingMappingTitle') }}</span>
              <span>
            <el-select v-model="mappedFrameworkId"
                       :title="getFrameworkMappedTitle(mappedFrameworkId)"
                       clearable placeholder="Please select" size="small">
                <el-option
                  v-for="frameworkId in lessonMappedFramework"
                  :key="frameworkId"
                  :label="getFrameworkMappedTitle(frameworkId)"
                  :value="frameworkId">
                </el-option>
            </el-select>
          </span>
            </div>
          </div>
        </div>
        <lesson-mapping-detail v-if="showDomainDescription" :plf-measures-list="plfMeasuresList" :showCoreMeasure="showCoreMeasure" :lesson="lesson"
                               :mapped-framework-id="mappedFrameworkId" :key="mappedFrameworkId"/>
        <lesson-mapping-without-description v-else :plf-measures-list="plfMeasuresList" :showCoreMeasure="showCoreMeasure" :lesson="lesson"
                                            :mapped-framework-id="mappedFrameworkId ? mappedFrameworkId : defaultNoMappedFrameworkId" :key="mappedFrameworkId"/>
      </div>
      <!--  课程目标  -->
      <div class="lesson-field-label" v-if="lesson.objectives && lesson.objectives.length">
        <h1 class="lesson-title-label" >
          {{ $t('loc.lesson2NewLessonFormLabelObjectives') }}
        </h1>
      </div>
      <div class="lesson-content-value">
        <div v-for="objective in lesson.objectives" class="lesson-field-value">
          <span v-html="getObjective(objective)"></span><br>
        </div>
      </div>
      <!--  课程材料  -->
      <div class="lesson-field-label"
           v-if="lesson.materials && lesson.materials.descriptions && lesson.materials.descriptions[0] || materialMediaUrl">
        <h1 class="lesson-title-label" >
          {{ $t('loc.lesson2NewLessonFormLabelMaterials') }}
        </h1>
      </div>
      <div class="lesson-content-value">
        <el-row>
          <el-col :span="materialMediaUrl ? 17 : 24">
            <div v-for="material in lesson.materials && lesson.materials.descriptions">
              <span class="lesson-field-value" v-html="getMaterial(material)"></span>
            </div>
            <div class="lesson-material-attachment-item" v-for="attachment in lesson.materials &&lesson.materials.attachmentMedias">
              <img class="lesson-material-attachment-item-img" :src="fileIcon(attachment)" alt="">
              <div class="lesson-material-attachment-item-title" :title="attachment.sourceFileName">
                <span style="max-width: 240px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{ attachment.sourceFileName.substring(0, attachment.sourceFileName.lastIndexOf('.')).trim() }}</span>
                <span>.{{ attachment.sourceFileName.substring(attachment.sourceFileName.lastIndexOf('.') + 1) }}</span>
                <span>({{ formatSize(attachment.size) }})</span>
              </div>
              <a style="margin-left: auto;margin-right: 5px;" @click="downloadMaterialAttachFile(attachment)">
                <el-button icon="el-icon-download" size="medium" type="text">
                  {{ $t('loc.download') }}
                </el-button>
              </a>
            </div><br>
          </el-col>
          <el-col :span="7" v-if="materialMediaUrl" style="float: right">
            <lesson-media-viewer :url="materialMediaUrl"/>
          </el-col>
        </el-row>
      </div>
      <!-- 课程步骤 -->
      <div class="lesson-field-label" v-if="!stepsEmpty">
        <h1 class="lesson-title-label" >
          {{ $t('loc.lesson2NewLessonFormLabelSteps') }}
        </h1>
      </div>
      <div class="lesson-content-value">
        <div v-for="(step, index) in lesson.steps" :key="index">
          <div class="lesson-field-label" v-if="!isStepEmpty(step) || !isStepGuidesEmpty(step)">
            {{ $t('loc.lessons2LessonDetailGuide') }} {{ step.ageGroupName }}
          </div>
          <template v-if="!isStepEmpty(step)">
            <el-row>
              <el-col class="lesson-field-value ql-editor remove-padding" v-html="step.content"
                      :span="step.media && step.media.url || step.externalMediaUrl ? 17:24"/>
              <el-col :span="7" v-if="step.media && step.media.url" style="float: right"> <!-- 如果有图片或音频则显示图片或音频 -->
                <lesson-media-viewer :url="step.media && step.media.url"/>
              </el-col>
              <!-- 外部视频时显示外部视频 -->
              <el-col :span="7" v-else-if="step.externalMediaUrl" style="float: right">
                <lesson-media-viewer :url="step.externalMediaUrl"/>
              </el-col>
            </el-row>
          </template>
          <template v-if="!isStepGuidesEmpty(step)">
            <div class="lesson-field-label add-margin-t-10">
              {{ $t('loc.typicalBehaviors') }}
            </div>
            <DomainsTableOverview class="add-margin-b-24" v-if="!isStepGuidesEmpty(step)" :domains="domains" :unitAddressDomains="filterCoreMeasure(step.lessonStepGuides).filter(item => item.typicalBehaviors || item.examples)"></DomainsTableOverview>
          </template>
        </div>
      </div>
      <dll-detail :lesson="lesson" v-if="dllOpen"/>
      <!-- 课程分享 -->
      <lesson-share :lesson="lesson" v-if="hasShareResource"/>
    </div>
    <!-- 课程详情中映射的测评点的解释 -->
    <MappedMeasureExplanation :lesson="lesson" :mapped-framework-id="mappedFrameworkId" v-if="lesson.mappedMeasures && mappedFrameworkId"/>
  </div>
</template>

<script>
import 'quill/dist/quill.snow.css'
import constants from '../../../../../utils/constants'
import LessonMediaViewer from './LessonMediaViewer'
import lessonShare from './LessonShare'
import LessonMappingDetail from '@/views/modules/lesson2/lessonLibrary/components/LessonMappingDetail'
import LessonMappingWithoutDescription from '@/views/modules/lesson2/lessonLibrary/components/LessonMappingWithoutDescription'
import MappedMeasureExplanation from './MappedMeasureExplanation'
import { mapState } from 'vuex'
import { MappedStateFramework } from '@/utils/constants'
import Api from '@/api/lessons2/index'
import DllDetail from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/index'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
import AppUtil from '../../../../../utils/app'
import tools from '@/utils/tools'
import lessonUtils from '@/utils/lessonUtils'
import DomainsTableOverview from '@/views/modules/lesson2/lessonLibrary/editor/DomainsTableOverview'

export default {
  name: 'LessonInfoNew',
  components: {
    LessonMediaViewer,
    lessonShare,
    LessonMappingDetail,
    LessonMappingWithoutDescription,
    MappedMeasureExplanation,
    DllDetail,
    DomainsTableOverview
  },
  props: {
    lesson: {
      type: Object,
      default: function () {
        return null
      }
    },
    planPreview: {
      type: Boolean
    },
    domains: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      showOtherMeasures: !!this.lesson.mappedMeasures,
      mappedFrameworkId: 0,
      defaultNoMappedFrameworkId: 0, // 如果单选没有选中的时候，多选没有选中的时候的 默认 mappedFrameworkId
      recordMappedFrameworkId: '', // 记录当前映射的框架
      plfMeasuresList: [],
      showDomainDescription: false, // 是否打开了展示按钮详细描述
      showCoreMeasure: false, // 是否打开仅展示核心测评点 默认为true
      fileIcons: { doc, docx, pdf, ppt, pptx, xls, xlsx, file },
      showCoreMeasureOpen: this.lesson.showCoreMeasureOpen // 是否展示仅展示核心测评点开关
    }
  },
  computed: {
    coverURL () {
      // 课程默认封面
      if (this.lesson.coverMedias.length === 0 && (!this.lesson.coverExternalMediaUrl || this.lesson.coverExternalMediaUrl.length === 0)) {
        return constants.lessonDefaultCoverURL
      }
      // 如果有课程封面，展示课程封面
      if (this.lesson.coverMedias.length !== 0) {
        let medias = this.lesson && this.lesson.coverMedias || []
        let media = medias[medias.length - 1]
        return media && media.url || media.externalMediaUrl // 如果有媒体的路径就用媒体的，没有媒体的路径就用外部链接，外部链接用于预览的时候显示
      } else {
        // 如果没有课程封面，展示外部媒体视频
        return this.lesson && this.lesson.coverExternalMediaUrl
      }
    },
    materialMediaUrl () {
      return this.lesson.materials && this.lesson.materials.media && this.lesson.materials.media.url || this.lesson.materials && this.lesson.materials.externalMediaUrl
    },
    hasShareResource () {
      let { books, videoBooks, attachmentMedias } = this.lesson || {}
      return books && books.length || videoBooks && videoBooks.length || attachmentMedias && attachmentMedias.length
    },
    stepsEmpty () {
      return !this.lesson.steps || // 没有步骤
        !this.lesson.steps.find(step => !this.isStepEmpty(step) || !this.isStepGuidesEmpty(step)) // 有步骤，步骤是空的 或者 有步骤，步骤没有例子或典型行为
    },
    ...mapState({
      lessonMappedFramework: function (state) {
        let lessonMappedFrameworkList = state.common.open.lessonMappedFramework.split(',')
        lessonMappedFrameworkList = lessonMappedFrameworkList.filter(frameworkId => {
          return frameworkId !== ''
        })
        let frameworkId = sessionStorage.getItem('mappedFrameworkId')
        // 如果这个课程选择的框架是用户选择开放的映射框架，才修改 mappedFrameworkId
        if (this.lesson.mappedMeasures && this.lesson.mappedMeasures.length > 0 && lessonMappedFrameworkList.length > 0) {
          if (frameworkId) {
            this.mappedFrameworkId = frameworkId
          } else {
            this.mappedFrameworkId = lessonMappedFrameworkList && lessonMappedFrameworkList[0]
          }
        }
        return lessonMappedFrameworkList
      },
      open: state => state.common.open,
      currentUser: (state) => state.user.currentUser // 当前用户
    }),
    dllOpen () {
      return this.open && this.open.dllopen
    },
    hasCoreMeasure () {
      let hasCoreMeasure = false
      this.domains.forEach(domain => {
        domain.children.forEach(measure => {
          if (measure.core) {
            hasCoreMeasure = true
          }
        })
      })
      return hasCoreMeasure
    },
    /**
      * 是否显示 Classroom Type 部分
      */
    showClassroomType () {
      return this.lesson && lessonUtils.showClassroomType(this.lesson.ages, this.lesson.activityType) && !this.lesson.adaptedModuleSwitch
    },
  },
  watch: {
    mappedFrameworkId: {
      immediate: true,
      handler: function () {
        this.$emit('getMappedFrameworkId', this.mappedFrameworkId)
      }
    }
  },
  methods: {
    switchMappedFramework () {
      if (this.mappedFrameworkId === this.recordMappedFrameworkId) {
        this.mappedFrameworkId = this.defaultNoMappedFrameworkId
      }
      this.recordMappedFrameworkId = this.mappedFrameworkId
    },
    // 每当按钮的值发生变化的时候，将按钮的值存入缓存中， 如果缓存中存在数据的时候，将缓存中的数据放入到 showDomainDescription中
    changeShowDomainDescription (value) {
      // 缓存键
      const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
      // 将值放入缓存中
      localStorage.setItem(cacheKey, value)
    },
    isStepEmpty (step) {
      return !step ||
        !step.content &&
        (!step.media || !step.media.url) &&
        !step.externalMediaUrl  // !step.externalMediaUrl 只有链接时不返回空
    },
    isStepGuidesEmpty (step) {
      return !step || !step.lessonStepGuides ||
        !step.lessonStepGuides.some(item => item.examples || item.typicalBehaviors)
    },
    // 判断格式为视频还是书
    isImg (url) {
      let reg = /.+(\.jpeg|\.png|\.jpg)/i
      return reg.test(url)
    },
    getMaterial (material) {
      if (tools.isComeFromIPad()) {
        // APP H5页面跳转浏览器
        $('a.textarea-item-a, a.lesson-material-card-link').on('click', function ($this) {
          AppUtil.appOpenUrlByBrowser($(this).attr('href'))
          return false
        })
      }
      // H5页面渲染
      return material.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ') + ' </a>'.replace('href')
    },
    getObjective (objective) {
      return objective.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ')
    },
    getFrameworkMappedTitle (frameworkId) {
      let framework = MappedStateFramework.find(item => item.id === frameworkId)
      if (!framework) {
        return ''
      }
      return this.lessonMappedFramework.length > 2 ? framework.name : `${framework.name}`
    },
    fileIcon (attachment) {
      let name = attachment.sourceFileName
      let extension = name && name.substring(name.lastIndexOf('.') + 1)
      return this.fileIcons[extension] || file
    },
    formatSize (size) {
      let value = size
      let suffix = 'B'
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'KB'
      }
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'MB'
      }
      return `${value}${suffix}`
    },
    // 获取课堂类型标签文本
    getClassroomTypeLabel (classroomType) {
      if (!classroomType) return this.$t('loc.classroomTypeInPerson')
      
      switch (classroomType.toUpperCase()) {
        case 'IN_PERSON':
          return this.$t('loc.classroomTypeInPerson')
        case 'VIRTUAL':
          return this.$t('loc.classroomTypeVirtual')
        default:
          return classroomType
      }
    },
    // 下载课程材料附件
    downloadMaterialAttachFile (file) {
      if (tools.isComeFromIPad()) {
        var fileSize = tools.calFilesize(file.size)
        let requestData = {
          'emailTemplate': 'lesson_library_material_attach',
          'downloadFileUrl': file.url,
          'fileName': file.sourceFileName,
          'fileSize': fileSize,
          'courseName': this.lesson.name
        }
        this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
          .then(() => {
            this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
              confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
              showCancelButton: false
            })
          }).catch(error => {
          this.$message.error(error.message)
        })
      } else {
        window.location.href = file.url
      }
    },
    changeShowCoreMeasure () {
      // 缓存键
      let cacheKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
      localStorage.setItem(cacheKey, this.showCoreMeasure)
      this.$bus.$emit('onlyShowMeasure', this.showCoreMeasure)
      if (this.showCoreMeasure) {
        this.$analytics.sendEvent('web_lesson_open_core_measure')
      } else {
        this.$analytics.sendEvent('web_lesson_close_core_measure')
      }
    },
    filterCoreMeasure (lessonStepGuides) {
      if (this.showCoreMeasure) {
        lessonStepGuides = lessonStepGuides.filter(item => item.core)
      }
      return lessonStepGuides
    },
    // quiz 资源状态同步
    quizHasResource () {
      if (!this.lesson || !this.lesson.steps || this.lesson.steps.length === 0) {
        this.$store.dispatch('setHasQuizResource', false)
        return
      }
      if (this.lesson.steps[0].questions && this.lesson.steps[0].questions.length > 0) {
        this.$store.dispatch('setHasQuizResource', true)
      } else {
        this.$store.dispatch('setHasQuizResource', false)
      }
    }
  },
  created () {
    Api.getMeasures(constants.caPlfFrameworkId).then(res => {
      this.plfMeasuresList = res.measures || []
    })
    // 同步 quiz 资源状态
    this.quizHasResource()
  },
  mounted () {
    // 开始阶段为 SHOW_DOMAIN_DESCRIPTION 赋值
    const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
    if (localStorage.getItem(cacheKey)) {
      this.showDomainDescription = JSON.parse(localStorage.getItem(cacheKey))
    } else {
      this.showDomainDescription = false
    }
    if (this.lesson.showCoreMeasureOpen) {
      // 开始阶段为 SHOW_CORE_MEASURE 赋值
      const cacheShowCoreKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
      if (localStorage.getItem(cacheShowCoreKey)) {
        this.showCoreMeasure = JSON.parse(localStorage.getItem(cacheShowCoreKey))
      } else {
        this.showCoreMeasure = this.lesson.showCoreMeasureOpen
      }
    } else {
      this.showCoreMeasure = this.lesson.showCoreMeasureOpen
    }
  }
}
</script>
<style scoped lang="less">
@media screen and (max-width:1199px) {
  .before-item ::before {
    content: "";
    position: absolute;
    display: block;
    width: 1px;
    top: 15px;
    bottom: 15px;
    left: 145px;
    background: #DCDFE6;
  }

  .lesson-field-time {
    margin: 0px 40px;
    background-color: #F5F6F8;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;

    .lesson-field-time-item {
      display: flex;
      flex-direction: column-reverse;
      padding: 30px 0px;
      gap: 0px;
      justify-content: center;
      align-items: center;
      vertical-align: middle;
      margin-top: -20px;
      .lesson-field-value, .lesson-field-label {
        height: 20px;
        line-height: 20px;
      }
      .lesson-field-value{
        margin-left: 5px;
      }
    }
  }
}
@media screen and (min-width: 1200px) {
  .before-item ::before {
    content: "";
    position: absolute;
    display: block;
    width: 1px;
    top: 15px;
    bottom: 15px;
    left: 210px;
    background: #DCDFE6;
  }

}
.lesson-name {
  text-align: center;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
  font-size: 26px;
  color: #333;
  font-weight: bold;
}

/* 分割线 */
.split-line {
  width: 20%;
  height: 3px;
  background: rgba(16, 179, 183, 1);
  margin: 30px auto 30px;
}

/* 描述列表标签 */
.lesson-field-label {
  font-size: 16px;
  line-height: 34px;
  height: 34px;
  font-weight: 600;
  color: #333333;
}

/* 描述列表标签值 */
.lesson-field-value {
  font-size: 16px;
  line-height: 25px;
  color: #111c1c;
}

.lesson-author-date-row {
  display: flex;
  justify-content: center;
  font-size: 16px;
  line-height: 25px;
  color: #676879;
  margin-top: 20px;
}

.lesson-author-date-row .lesson-field-value {
  margin: 0px 5px;
  font-size: 15px;
  //line-height: 25px;
}

.lesson-author-name {
  margin: 0px 5px;
  max-width: 150px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lesson-field-label-age-topic {
  display: flex;
  margin-bottom: 15px;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
  height: fit-content;
  padding-left: 20px;

  .lesson-age-field-label {
    display: flex;
    justify-content: center;
    gap: 10px;
    font-size: 14px;
    flex-wrap: wrap;
    height: fit-content;
  }
}

.lesson-age-field-value {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 16px;
  gap: 4px;
  width: fit-content;
  height: 32px;
  background: #FEF5E9;
  border: 1px solid #F49628;
  border-radius: 20px;
  flex: none;
  order: 0;
  flex-grow: 0;
  color: #F49628;
}

.lesson-topic-field-value {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 16px;
  gap: 4px;
  width: fit-content;
  height: 32px;
  background: #FFEFE5;
  border: 1px solid #FF6000;
  border-radius: 20px;
  flex: none;
  color: #FF6000;
  order: 1;
  flex-grow: 0;
}

/* 描述列表主题 */
.lesson-descriptions-theme-value {
  line-height: 32px;
  font-size: 16px;
  border-radius: 2px;
  background: #eee;
  color: #777;
  padding: 2% 5%;
  margin-right: 5px;
  display: revert;
}

/*作者标签值*/
.lesson-author-tag-value {
  font-size: 20px;
  color: #333333;
  line-height: 20px;
}

.lesson-detail-option-button {
  width: 80px;
  height: 32px;
  border-radius: 2px;
  line-height: 12px;
  color: #999999;
  font-size: 12px;
  padding: 0;
}

.lesson-field-time {
  margin: 0px 40px;
  background-color: #F5F6F8;
  border-radius: 8px;
  display: flex;
  justify-content: space-around;

  .lesson-field-time-item {
    display: flex;
    flex-direction: column-reverse;
    padding: 20px 0px;
    gap: 0px;
    justify-content: center;
    align-items: center;
    vertical-align: middle;

    .lesson-field-value, .lesson-field-label {
      height: 20px;
      line-height: 20px;
    }
  }

  & > :nth-child(3) {
    margin-left: 30px;
  }
}

.fit-height {
  height: fit-content;
  /* 仅展示核心测评点 start */
  .core-measure-field {
    position: relative!important;
  }
  .core-measure {
    position: absolute;
    right: 0px;
    top: 0px;
    font-size: 15px;
    font-weight: 400;
    span {
      margin-right: 5px;
    }
  }

  /* 仅展示核心测评点 end */
}

.lesson-title-label {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 16px;
  font-size: 18px;
  font-weight: 500 !important;
  color: #ffffff;
  gap: 10px;
  margin-bottom: 0px;
  width: fit-content;
  height: 32px;
  background: #10B3B7;
  border-radius: 16px;
}

.add-padding-t-8 {
  padding-top: 8px !important;
}

.lesson-content-value {
  padding-left: 20px;
  padding-top: 16px;

  .lesson-field-label {
    color: #10B3B7;
  }


  .lesson-field-value {
    color: #111c1c;
  }

  .min-width-270 {
    min-width: 270px;
  }

  .lesson-switch {
    font-size: 15px;
    font-weight: normal;
    margin-right: 5px
  }

  ::v-deep .el-radio__label {
    font-size: 15px !important;
  }

  ::v-deep label.el-radio {
    height: 19px;
    line-height: 19px;
    margin-bottom: 0px;
  }

  ::v-deep .el-radio-group {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
    line-height: 20px;
    vertical-align: middle;
    font-size: 0;
  }
}

.el-tag {
  margin-right: 5px;
  font-size: 15px;
}

.marginRight {
  margin-right: 4%;
}

/deep/ .el-icon-circle-close {
  padding: 2px 0;
}

// 下面是课程材料描述的展示样式
/deep/ .textarea-item.p {
  padding: 0 15px 0 0 !important;
  margin: 0 !important;
  color: #323338;
  line-height: 25px !important;
  -webkit-user-modify: read-only !important;
}

/deep/ .textarea-item-a, textarea-item-a:hover, textarea-item-a:focus {
  word-break: break-all !important;
  color: #10B3B7;
  cursor: pointer !important;
}

/deep/ .lesson-material-card {
  margin-top: 6px !important;
  margin-bottom: 10px !important;
  line-height: 18px !important;
}

/deep/ .lesson-material-card-link {
  display: flex !important;
  flex-direction: row !important;
  width: 450px !important;
  align-items: center !important;
  height: 90px !important;
  background-color: #fff !important;
  margin: 0 !important;
  border-radius: 5px !important;
  gap: 10px !important;
}

/deep/ .lesson-material-card-link .lesson-material-card-link-icon {
  max-width: calc(107px) !important;
  min-width: 90px !important;
  height: 90px !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  object-fit: contain !important;
}

/deep/ .lesson-material-card-link .lesson-material-card-link-text {
  display: flex !important;
  flex-direction: column !important;
  align-content: flex-start !important;
  gap: 10px !important;
  flex: 1 !important;
  padding-right: 10px !important;
  max-width: 430px;
}

/deep/ .lesson-material-card-site {
  width: max-content !important;
  position: relative !important;
  border: 1px solid #c0c4cc !important;
  border-radius: 5px !important;
  background-color: #fff !important;
}

/deep/ .editor-card-link-describe {
  color: #606266 !important;
  font-size: 14px;
  font-weight: bold !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2 !important;
}

/deep/ .editor-card-link-origin {
  color: #606266 !important;
  font-size: 16px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/deep/ .lesson-material-card-img {
  position: relative !important;
  width: 192px !important;
  height: 108px !important;
}

/deep/ .lesson-material-card-img img {
  width: 192px !important;
  height: 108px !important;
}

/deep/ .lesson-material-attachment-item {
  display: flex !important;
  background-color: #f2f2f2 !important;
  align-items: center !important;
  width: 452px !important;
  min-height: 35px !important;
  border-radius: 5px !important;
  margin-top: 5px !important;
}

/deep/ .lesson-material-attachment-item-title {
  line-height: 16px !important;
  display: grid !important;
  grid-template-columns: 1fr max-content max-content !important;
}

/deep/ .lesson-material-attachment-item-img {
  width: 20px !important;
  margin-left: 5px !important;
  margin-right: 5px !important;
}

/deep/ .lesson-material-attachment-item .el-button span {
  margin-left: 0 !important;
}

/deep/ .lesson-material-attachment-item .el-button {
  padding: 0 !important;
}

/deep/ .lesson-material-card-video-play {
  width: 40px !important;
  height: 40px !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  margin: auto !important;
}

/deep/ .lesson-material-card-close {
  display: none !important;
}
</style>
