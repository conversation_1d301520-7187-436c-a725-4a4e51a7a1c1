<template>
  <el-button plain size="small" @click="download" style="margin-right: 0" >
    <i class="el-icon-loading" v-if="this.pdfLoading"></i>
    <i class="iconfont" style="font-size: 14px;">&#xefb8;</i>
    <span style="margin-left: 5px">
        {{ $t('loc.lessons2PDF') }}
      </span>
  </el-button>
</template>
<script>
import Api from '../../../../../api/lessons2'
import { mapState } from 'vuex'
import tools from '../../../../../utils/tools'
import { isTeacher } from '@/utils/common'
import pdf from '@/assets/img/file/pdf.png'
export default {
  name: 'LessonPdf',
  props: [
    'lessonId', // 课程 ID
    'lessonName', // 课程名
    'mappedFrameworkId',
    'submodule'
  ],
  data () {
    return {
      // 防止重复操作
      loading: false,
      pdfLoading: false
    }
  },
  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser, // 当前用户
      showClrSource: state => state.lesson.showClrSource, // 是否显示 CLR 资源
      showMappedTypicalBehaviors: state => state.lesson.showMappedTypicalBehaviors // 是否显示映射后的典型行为
    })
  },
  methods: {
    // 下载 PDF
    download () {
      if (isTeacher() && this.submodule !== 'Favorite') {
        this.$analytics.sendEvent('web_lesson_library_my_less_pdf')
      } else if (isTeacher() && this.submodule === 'Favorite') {
        this.$analytics.sendEvent('web_lesson_library_my_less_favorite_pdf')
      } else if (this.submodule === 'Draft') {
        this.$analytics.sendEvent('web_lesson_library_my_less_draft_pdf')
      } else {
        this.$analytics.sendEvent('web_lesson_library_mgt_pdf')
      }
      if (this.pdfLoading) {
        return
      }
      // 缓存键
      const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
      let showDescription = false
      // 将值放入缓存中
      if (localStorage.getItem(cacheKey)) {
        showDescription = JSON.parse(localStorage.getItem(cacheKey))
      }
      this.pdfLoading = true
      const cacheShowCoreKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
      let onlyShowCore = true
      if (localStorage.getItem(cacheShowCoreKey)) {
        onlyShowCore = JSON.parse(localStorage.getItem(cacheShowCoreKey))
      }
      Api.generateLessonPDF(this.lessonId, this.mappedFrameworkId, showDescription, onlyShowCore, this.showClrSource, this.showMappedTypicalBehaviors).then(res => {
        this.getPDFList()
      }).catch(res => {
        this.pdfLoading = false
      })
      // 课程详情中点击PDF下载
      this.$analytics.sendEvent('web_lessondetails_click_pdfdownload')
    },
    // 轮询查询 PDF 生成状态
    getPDFList () {
      let requestData = { type: 'LESSON_DETAIL' }
      this.$axios.get($api.urls().pdfList, { params: requestData }).then(data => {
        let dataIsCreating = false
        if (data[0].status !== 'SUCCEED' && data[0].status !== 'FAILED') {
          dataIsCreating = true
        } else {
          this.pdfLoading = false
          if (data[0].pdfUrl) {
            if (tools.isComeFromIPad()) {
              let sendEmailRequest = {
                'emailTemplate': 'lesson_library_detail',
                'downloadFileUrl': data[0].pdfUrl,
                'fileName': data[0].pdfName,
                'courseName': this.lessonName
              }
              this.$axios.post($api.urls().sendFileDownloadEmail, sendEmailRequest)
              .then(() => {
                this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
                  confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
                  showCancelButton: false
                })
              }).catch(error => {
                this.$message.error(error.message)
              })
            } else {
              this.$alert(
                '<div class="display-flex" style="gap: 10px"><img style="height: 30px; margin: 5px"  alt="" src="' +
                pdf +
                '"> <span class="word-break">' +
                data[0].pdfName + '</span></div>',
                'Download File',
                {
                  dangerouslyUseHTMLString: true,
                  confirmButtonText: this.$t('loc.download'),
                  customClass: 'el-message-box-font',
                  callback: (action) => {
                    if (action === 'confirm') {
                      const eleLink = document.createElement('a')
                      // eleLink.download = data[0].pdfName
                      eleLink.style.display = 'none'
                      // 字符内容转变成blob地址
                      // const blob = new window.Blob([data[0].pdfUrl])
                      eleLink.href = data[0].pdfUrl
                      // 触发点击
                      document.body.appendChild(eleLink)
                      eleLink.click()
                      // 然后移除
                      document.body.removeChild(eleLink)
                    }
                  }
                }
              )
              // window.location.href = data[0].pdfUrl
            }
          }
          clearTimeout(this.timeout)
        }
        if (dataIsCreating) {
          this.timeout = setTimeout(() => {
            this.getPDFList()
          }, 3000)
        }
      }).catch(error => {
        this.pdfLoading = false
        if (error.response.data) {
          this.$message.error('error')
        }
        clearTimeout(this.timeout)
      })
    }
  },
  beforeDestroy () {
    this.timeout && clearTimeout(this.timeout)
  }
}
</script>
<style lang="less">
.lesson-message-box-pdf-confirm {
  word-break: break-word;
  display: flex;
  align-items: center;

  & > img {
  margin-right: 10px;
  }
}
</style>
<style scoped lang="less">
@font-face {
  font-family: 'iconfont';  /* Project id 2980060 */
  src: url('//at.alicdn.com/t/font_2980060_12gyhi5dajl.woff2?t=1638515281075') format('woff2'),
  url('//at.alicdn.com/t/font_2980060_12gyhi5dajl.woff?t=1638515281075') format('woff'),
  url('//at.alicdn.com/t/font_2980060_12gyhi5dajl.ttf?t=1638515281075') format('truetype');
}
</style>
