<template>
  <div id="app">

    <div class="title-first" style="padding-bottom: 28px">Original and all versions of this lesson</div>

    <div class="all">

      <el-card class="all" :body-style="{ padding: '0px' }">
        <el-col style="padding-top: 22px;">
          <!--上面的行-->
          <el-row>
            <el-col :span="22">
              <div class="titleLesson">Original Lesson</div>
              <el-row type="flex" justify="start" style="padding-left: 20px">
                <el-col :span="21">
                  <el-card :body-style="{padding:'0px'}" class="whole" shadow="always">
                    <el-row>
                      <el-col :span="8">
                        <img :src="avatar" class="cover">
                      </el-col>
                      <!-- 右边的文字-->
                      <el-col :span="16">
                        <el-row>
                          <el-col :span="24" :offset="2" style="position: relative;top: 7px;">
                            <!--课程标题-->
                            <span class="subjectLesson">contract Jama To Go</span>
                          </el-col>
                        </el-row>

                        <el-row type="flex" justify="space-around" style="padding-top: 20px;">

                          <el-col :span="5" class="authorAvatar" :offset="1">
                            <!--作者头像-->
                            <el-avatar :size="24" shape="circle" :src="avatar"></el-avatar>
                          </el-col>
                          <el-col :span="9">
                            <!--作者姓名-->
                            <span class="authorName">Curry Mappers</span>
                          </el-col>
                          <el-col :span="10">
                            <!--对比按钮-->
                            <el-button class="contractButton" @click="lessonContract()">
                              <span class="buttonFont">Lesson Comparison</span>
                            </el-button>
                          </el-col>
                        </el-row>
                      </el-col>

                    </el-row>
                  </el-card>
                </el-col>

              </el-row>


            </el-col>
          </el-row>

          <!--分割线-->

          <!--下面的行-->
          <el-row type="flex" justify="center">
            <el-col :span="24">
              <div class="titleLesson" style=" padding-top: 20px;">Other Versions</div>
              <el-row :gutter="0">
                <el-col :span="8" v-for="(lesson, index) in copyLessonList.linkedLessons" :key="index"
                        style="padding-left: 19px">
                  <el-card :body-style="{padding:'0px'}" class="whole" shadow="always">
                    <el-row>
                      <el-col :span="8">
                        <!--封面-->
                        <img :src="lesson.lessonCoverAvatarURL[0]" class="cover">
                      </el-col>

                      <!-- 右边的文字-->
                      <el-col :span="16">
                        <el-row>
                          <el-col :span="24" :offset="2" style="position: relative;top: 7px;">
                            <!--课程标题-->
                            <span class="subjectLesson">{{ lesson.lessonName }}</span>
                          </el-col>
                        </el-row>

                        <el-row type="flex" justify="space-around" style="padding-top: 20px;">
                          <!--                            4-->
                          <el-col :span="7" class="authorAvatar" :offset="1">
                            <!--作者头像-->
                            <el-avatar :size="24" shape="circle"
                                       :src="lesson.authorAvatarURL"></el-avatar>
                          </el-col>
                          <el-col :span="7">
                            <!--作者姓名-->
                            <span class="authorName">{{ lesson.authorName }}</span>
                          </el-col>
                          <el-col :span="10">
                            <!--对比按钮-->
                            <el-button class="contractButton" @click="lessonContract()">
                              <span class="buttonFont">Lesson Comparison</span>
                            </el-button>
                          </el-col>
                        </el-row>

                      </el-col>

                    </el-row>

                  </el-card>
                </el-col>

              </el-row>
              <!--查看更多按钮-->
              <el-row v-if="copyLessonList.length > 3" style="text-align: center; position: relative; top: 35px;" class="show-more">
                <el-button type="text" @click="" style="padding: 9px;">Show More</el-button>
              </el-row>


            </el-col>
          </el-row>
        </el-col>

      </el-card>


    </div>

  </div>


</template>

<script>

import copy from '../../../../../api/lessons2'

export default {

  name: "LessonVersion",
  props: {
    lessonId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      copyLessonList: [] // 复制本课程的课程集合
    }
  },
  created() {
    this.getListCopyLessonByLessonId()
  },
  methods: {
    getListCopyLessonByLessonId() {
      if (this.lessonId != null) {
        copy.getCopyLessons(this.lessonId)
          .then(response => {
            this.copyLessonList = response
          })
          .catch(response => {
          })
      }

    },
    lessonContract() {
    },
  }

}
</script>

<style>

.all {
  width: 1200px;
  height: 343px;
  background: rgba(255, 255, 255, 1);
}

.whole {
  height: 72px;
  width: 360px;
}

.cover {
  width: 128px;
  height: 72px;
  border-radius: 0;
  background: rgba(230, 230, 230, 1)
}

.authorAvatar {
  width: 24px;
  height: 24px;
}

.subjectLesson {
  color: #333333;
  font-weight: normal;
  width: 187px;
  height: 13px;
  font-size: 14px;
  text-align: left
}

.authorName {
  font-weight: normal;
  width: 73px;
  height: 9px;
  font-size: 12px;
  text-align: left
}

.contractButton {
  padding: 0;
  width: 100%;
  height: 24px;
  border-radius: 4px;
  background: #FFBA00;
}

.buttonFont {
  color: #FFFFFF;
  font-weight: normal;
  width: 36px;
  height: 7px;
  font-size: 10px;
  text-align: left;
}

.titleLesson {
  padding-left: 20px;
  color: #333333;
  font-size: 16px;
  padding-bottom: 15px;
}

.title-first {
  color: #333333;
  font-weight: normal;
  width: 300px;
  height: 14px;
  font-size: 16px;
  text-align: left
}

</style>