<template>
  <i class="icon-alibaba" :class="clazz"/>
</template>

<script>
export default {
  name: "IconAlibaba",
  props: ['size'],
  computed: {
    clazz() {
      let cz = [];
      if (this.size) {
        cz.push('icon-alibaba--' + this.size)
      }
      return cz;
    }
  },
  data() {
    return {}
  }
}
</script>

<style scoped lang="less">
@font-face {
  font-family: 'iconfont';  /* Project id 2980060 */
  src: url('//at.alicdn.com/t/font_2980060_3uchugmjui.woff2?t=1645012430564') format('woff2'),
  url('//at.alicdn.com/t/font_2980060_3uchugmjui.woff?t=1645012430564') format('woff'),
  url('//at.alicdn.com/t/font_2980060_3uchugmjui.ttf?t=1645012430564') format('truetype');
}

.icon-alibaba::before {
  font-family: "iconfont", sans-serif;
  font-size: 18px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-alibaba--mini::before {
  font-size: 18px;
}

/**收藏图标*/
.icon-alibaba-favorite-on::before {
  color: #f0832f;
  content: '\efb7'
}

.icon-alibaba-favorite-off::before {
  color: #f0832f;
  content: '\efb6'
}

/**点赞图标*/
.icon-alibaba-like-on::before {
  color: #f84624;
  content: '\efb5'
}

.icon-alibaba-like-off::before {
  color: #f84624;
  content: '\efb4'
}

/**浏览量图标*/
.icon-alibaba-view::before {
  color: #707070;
  content: '\efb9';
  font-size: 23px;
}
.icon-alibaba-view-mini::before {
  color: #707070;
  content: '\efb9';
  font-size: 18px;
}
/** 麦克风图标*/
.icon-alibaba-microphone::before{
  color:red;
  content: '\e65f';
  font-size: 22px;
}
</style>