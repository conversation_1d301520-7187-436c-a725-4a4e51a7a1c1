<template>
  <!--点赞-->
  <el-tooltip class="item" effect="dark" placement="top" :hide-after="1000">
    <span slot="content">{{ userLiked ? $t('loc.lessons2LikedTips') : $t('loc.lessons2LessonLikeTipInfo') }}</span>
    <span>
      <el-button type="text"  @click.stop="like" style="padding: 0;font-weight: 400;">
        <!-- <svg xmlns="http://www.w3.org/2000/svg"  width="16" height="12" viewBox="0 0 14 11" fill >
          <path xmlns="http://www.w3.org/2000/svg" d="M6.99997 11L7.05699 10.9377C7.15731 10.9191 7.22134 10.8525 7.24954 10.7896C7.25716 10.7726 7.26465 10.7509 7.2687 10.7253C7.81567 10.2933 10.2965 8.81582 11.8456 7.9207C13.3589 7.05715 14.1904 5.38621 13.9629 3.66439C13.7169 1.805 12.2375 0.311105 10.365 0.031819C9.31394 -0.124975 8.17933 0.301572 6.99997 1.29989C5.82037 0.301458 4.68587 -0.125561 3.63493 0.031819C1.76164 0.311349 0.28213 1.8057 0.0369797 3.66592C-0.190208 5.38927 0.643081 7.06055 2.1596 7.92377C3.7061 8.81737 6.18466 10.2936 6.73127 10.7252C6.73532 10.7509 6.74281 10.7726 6.75043 10.7895C6.77851 10.8524 6.84254 10.9189 6.94286 10.9377L6.99997 11Z" fill="#717281"/>
        </svg> -->
        <icon-alibaba :size="size" :class="[`icon-alibaba-like-${userLiked ? 'on' : 'off'}`]"/>
        <span :class="textClass">{{ likeCount }}</span>
      </el-button>
    </span>
  </el-tooltip>
</template>
<script>
import Api from '../../../../../api/lessons2'
import IconAlibaba from '@/views/modules/lesson2/lessonLibrary/components/IconAlibaba'

export default {
  name: 'LessonLike',
  components: { IconAlibaba },
  props: [
    'count', // 点赞数
    'liked', // 当前用户是否已点赞
    'type', // 样式类型
    'lessonId', // 课程 ID
    'size'
  ],
  computed: {
    textClass () {
      let tc = ['iconfont-text']
      if (this.size) {
        tc.push('iconfont-text' + this.size)
      }
      return tc
    },
  },
  data () {
    return {
      userLiked: this.liked,
      likeCount: this.count,
      // 防止重复操作
      loading: false
    }
  },
  watch: {
    likeCount: function () {
      this.$emit('update:count', { likeCount: this.likeCount, liked: this.userLiked })
    },
    liked(value){
      this.userLiked = value
    },
    count(value){
      this.likeCount = value
    }
  },
  methods: {
    like () {
      this.likeCount++
    }
  }
}
</script>
<style scoped lang="less">
.iconfont-text {
  font-size: 15px;
  color: #999;
  margin-left: 4px;
}

.iconfont-text-mini {
  font-size: 13px;
}
</style>