<template>
    <!-- 选择测评点弹窗 -->
    <el-dialog :close-on-click-modal="false"
        custom-class="update-standards-and-regenerate-dialog" :title="$t('loc.enhanceLesson')"
        :visible.sync="dialogVisible" @open="openDialog" @close="handleClose" width="600px"
        :append-to-body="true">
        <el-form label-position="top" label-width="100px" ref="regenerateLessonForm" :model="regenerateLesson"
            :rules="regenerateLessonRules">
            <!-- 描述信息 -->
            <div class="m-b-sm">
                {{ $t('loc.enhanceLessonPlanTip') }}
            </div>
            <!-- 测评点 -->
            <el-form-item :label="$t('loc.standardsOrMeasures')" prop="measures">
                <el-select v-model="regenerateLesson.measures" filterable multiple
                    class="w-full update-lesson-measure-select" :multiple-limit="maxMeasureSelections"
                    :placeholder="$t('loc.pleaseSelect')" :loading="loadingFrameworkLoading" :disabled="loadingFrameworkLoading"
                    :popper-append-to-body="false" @remove-tag="handleRemoveTag" :key="selectKey"
                    @visible-change="handleVisibleChange"
                    >
                    <!-- 虚拟列表渲染选项 -->
                    <lg-virtual-list 
                        ref="virtualMeasureList"
                        :data-sources="flattenedMeasures"
                        :data-component="virtualMeasureOptionComponent"
                        :extra-props="{ onOptionClick: onOptionClick }"
                        height="245px"
                        :data-key="'abbreviation'"
                        :estimate-size="32"
                        class="virtual-measure-list"
                    />
                </el-select>
            </el-form-item>
            <!-- 活动持续时间 -->
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelActivityTime')" prop="activityTime">
                <el-select v-model="regenerateLesson.activityTime"
                    :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')" class="input-select w-full"
                    clearable>
                    <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
            </el-form-item>
            <!-- 重新设计的想法 -->
            <el-form-item :label="$t('loc.enhancementIdeas')" prop="redesignIdea" style="margin-bottom: 0">
                <el-input type="textarea" v-model="regenerateLesson.redesignIdea" :rows="4"
                    :placeholder="$t('loc.enhanceIdeasPlaceholder')" :maxlength="2000" show-word-limit
                    @input="redesignIdeaChange"></el-input>
            </el-form-item>
        </el-form>
        <!-- 操作 -->
        <span slot="footer" class="dialog-footer">
            <el-button plain @click="closeRedesignLessonDialog">{{ $t('loc.cancel') }}</el-button>
            <el-button class="ai-btn" type="primary" @click="confirmRedesignLesson">
                <template #icon>
                    <i class="lg-icon lg-icon-generate"></i>
                </template>
                {{ $t('loc.unitPlannerConfirmRegenerate') }}
            </el-button>
        </span>
    </el-dialog>
</template>

<script>
import Lesson2 from '@/api/lessons2'
import LessonUtils from '@/utils/lessonUtils'
import VirtuVirtualEnhanceLessonMeasureOption from './VirtualEnhanceLessonMeasureOption.vue'

export default {
    name: 'LessonEnhanceDialog',
    components: {
        VirtuVirtualEnhanceLessonMeasureOption
    },
    props: {
        // 弹框状态
        dialogVisible: {
            type: Boolean,
            default: false
        },
        // 课程测评点
        lessonMeasures: {
            type: Array,
            default: () => []
        },
        // 课程活动时间(允许接收String或Number类型)
        lessonActivityTime: {
            type: [String, Number],
            default: ''
        },
        // 领域列表
        domains: {
            type: Array,
            default: () => []
        },
        // 框架 ID(如果指定框架，domains 取框架下的测评点)
        frameworkId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 时间选项
            times: [],
            // 重新生成的表单数据
            regenerateLesson: {
                measures: [],
                activityTime: '',
                redesignIdea: ''
            },
            // 测评点选择最大数量
            maxMeasureSelections: 8,
            // 框架加载状态
            loadingFrameworkLoading: false,
            // 表单校验规则
            regenerateLessonRules: {
                measures: [
                    { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
                ],
                // redesignIdea: [
                //     { required: true, message: this.$t('loc.fieldReq'), trigger: 'change' }
                // ]
            },
            lessonMeasuresData: [],
            selectKey: 0,
            virtualMeasureOptionComponent: VirtuVirtualEnhanceLessonMeasureOption // 虚拟列表的选项组件
        }
    },
    computed: {
        // 将领域和测评点展平为一个数组，用于虚拟列表
        flattenedMeasures() {
            if (!this.domains || this.domains.length === 0) return []

            // 将所有领域和测评点展平为一个数组
            const flattened = []

            this.domains.forEach(domain => {
                // 添加领域作为分组标题
                flattened.push({
                    id: domain.id,
                    name: domain.name,
                    abbreviation: domain.id,
                    // 标记为领域
                    isDomain: true
                })
                // 添加该领域下的所有测评点
                if (domain.children && domain.children.length) {
                    domain.children.forEach(measure => {
                        flattened.push({
                            ...measure,
                        })
                    })
                }
            })
            return flattened
        },
    },
    created() {
        let times = LessonUtils.generateTime()
        // 将 times 的每一项的 value 处理为数字
        this.times = times.map(item => ({
            ...item,
            value: parseInt(item.value.match(/\d+/)[0], 10)
        }));
    },
    methods: {
        handleVisibleChange(visible) {
            if (visible && this.regenerateLesson.measures[0]) {
                // 定位到第一个选中的
                this.$refs.virtualMeasureList.scrollToItem(this.regenerateLesson.measures[0])
            }
        },
        // 打开弹框的回调
        async openDialog() {
            this.lessonMeasuresData = JSON.parse(JSON.stringify(this.lessonMeasures))
            // 限制测评点个数，只保留前 8 个测评点
            this.limitChildrenToEight();
            // 如果指定框架，则获取框架下的测评点
            if (this.frameworkId) {
              // 设置加载状态为 true
              this.loadingFrameworkLoading = true;
              let response = await Lesson2.getMeasures(this.frameworkId)
              this.domains = response.measures
              // 无论成功失败都将加载状态设为 false
              this.loadingFrameworkLoading = false;
            }
            // 初始化表单数据
            this.regenerateLesson.measures = this.getLastLevelAbbreviations(this.lessonMeasuresData) || []
            this.regenerateLesson.activityTime = this.lessonActivityTime
            this.regenerateLesson.redesignIdea = ''

            // 恢复表单效验状态
            this.$nextTick(() => {
                if (this.$refs.regenerateLessonForm) {
                    this.$refs.regenerateLessonForm.clearValidate()
                }
                this.selectKey++;
            })
        },

        // 限制测评点个数，只保留前 8 个测评点
        limitChildrenToEight() {
          if (this.lessonMeasuresData && Array.isArray(this.lessonMeasuresData)) {
            // 收集所有子测评点
            let allChildren = [];
            this.lessonMeasuresData.forEach(domain => {
              if (domain.children && Array.isArray(domain.children)) {
                allChildren = allChildren.concat(domain.children);
              }
            });
            // 如果传进来的测评点是一维结构直接截取
            if (allChildren.length === 0) {
              if (this.lessonMeasuresData.length > this.maxMeasureSelections) {
                this.lessonMeasuresData = this.lessonMeasuresData.slice(0, this.maxMeasureSelections)
                this.$nextTick(() => {
                  // 显示警告消息
                  this.$message.warning(this.$t('loc.selectMeasureEight'));
                })
              }
              return
            }
            if (allChildren.length > this.maxMeasureSelections) {
              this.$nextTick(() => {
                // 显示警告消息
                this.$message.warning(this.$t('loc.selectMeasureEight'));
              })
            }

            // 只保留前 8 个子测评点
            allChildren = allChildren.slice(0, this.maxMeasureSelections);

            // 重新构建 lessonMeasuresData，只包含有子测评点的领域
            let newLessonMeasuresData = [];
            allChildren.forEach(child => {
              // 找到该子测评点所属的领域
              const parentDomain = this.lessonMeasuresData.find(domain =>
                domain.children && domain.children.some(c => c.id === child.id)
              );

              if (parentDomain) {
                // 检查该领域是否已经添加到新数组中
                let domainInNewData = newLessonMeasuresData.find(d => d.id === parentDomain.id);

                if (!domainInNewData) {
                  // 如果该领域还未添加，则添加一个新的领域对象，并初始化 children 数组
                  domainInNewData = {
                    ...parentDomain,
                    children: []
                  };
                  newLessonMeasuresData.push(domainInNewData);
                }

                // 将当前子测评点添加到对应领域的 children 中
                domainInNewData.children.push(child);
              }
            });

            // 更新 lessonMeasuresData
            this.lessonMeasuresData = newLessonMeasuresData;
          }
        },

        // 测评点选项点击事件
        onOptionClick(measure, domain) {
            if (!domain) {
                // 根据 measure 的 id 获取 domain
                domain = this.domains.find(item => item.children.some(child => child.id === measure.id));
                // 两层以上的框架，存在 domains 和 lessonMeasuresData 的数据初始化层级结构不一致导致取消不掉测评点，暂时以测评点 ID 为准在 lessonMeasuresData 中找 domain
                let lessonMeasuresDataDomain = this.lessonMeasuresData.find(item => item.children.some(child => child.id === measure.id))
                if (lessonMeasuresDataDomain) {
                    domain = lessonMeasuresDataDomain
                }
            }
            // 判断当前点击的测评点是否已经在选中列表中
            const isSelected = this.regenerateLesson.measures.includes(measure.abbreviation);

            // 如果当前测评点已经选中，则需要取消选中
            if (isSelected) {
                // 从选中的数据结构中移除此测评点
                const parentMeasure = this.lessonMeasuresData.find(item => item.id === domain.id);
                if (parentMeasure) {
                    // 从父项的children中删除当前measure
                    const index = parentMeasure.children.findIndex(item => item.id === measure.id);
                    if (index !== -1) {
                        parentMeasure.children.splice(index, 1);
                    }

                    // 如果父项没有其他子项了，从lessonMeasuresData中删除该父项
                    if (parentMeasure.children.length === 0) {
                        const parentIndex = this.lessonMeasuresData.findIndex(item => item.id === domain.id);
                        if (parentIndex !== -1) {
                            this.lessonMeasuresData.splice(parentIndex, 1);
                        }
                    }
                }
                return;
            }

            // 判断是否超过最大值限制 && 当前选项不在已选中的测评点中
            if (this.regenerateLesson.measures.length >= this.maxMeasureSelections) {
                // 显示警告消息
                this.$message.warning(this.$t('loc.selectMeasureEight'));
                return;
            }

            // 添加测评点逻辑
            // 1. 判断当前选项的父项是否在已选中的测评点中
            const parentMeasure = this.lessonMeasuresData.find(item => item.id === domain.id);

            // 2. 如果存在则在已选中的测评点的父项的孩子节点中添加 measure
            if (parentMeasure) {
                // 检查measure是否已存在，避免重复添加
                const exists = parentMeasure.children.some(item => item.id === measure.id);
                if (!exists) {
                    parentMeasure.children.push(measure);
                }
            }
            // 3. 如果不存在则添加父项到已选中的测评点，同时父项的孩子节点为 measure
            else {
                this.lessonMeasuresData.push({
                    id: domain.id,
                    name: domain.name,
                    abbreviation: domain.abbreviation,
                    description: domain.description,
                    core: domain.core,
                    sortIndex: domain.sortIndex,
                    iepmeasure: domain.iepmeasure,
                    mappingAbbr: domain.mappingAbbr,
                    children: [measure]
                });
            }

        },
        // 通过标签移除测评点
        handleRemoveTag(tag) {
            // 从 lessonMeasuresData 中移除对应的测评点
            for (let i = 0; i < this.lessonMeasuresData.length; i++) {
                const domain = this.lessonMeasuresData[i];
                if (domain.children && domain.children.length > 0) {
                    // 查找并移除匹配的子测评点
                    const childIndex = domain.children.findIndex(child => child.abbreviation === tag);
                    if (childIndex !== -1) {
                        // 找到匹配的子测评点，从数组中移除
                        domain.children.splice(childIndex, 1);

                        // 如果该领域下已没有任何子测评点，也移除该领域
                        if (domain.children.length === 0) {
                            this.lessonMeasuresData.splice(i, 1);
                        }

                        break;
                    }
                }
            }
        },

        // 关闭弹框
        closeRedesignLessonDialog() {
            // 关闭弹框
            this.$emit('update:dialogVisible', false)
        },

        // 确认重新生成
        confirmRedesignLesson() {
            // 先进行表单验证
            this.$refs.regenerateLessonForm.validate((valid) => {
                if (valid) {
                    const data = {
                        originMeasure: this.regenerateLesson.measures,
                        measures: this.lessonMeasuresData,
                        activityTime: this.regenerateLesson.activityTime,
                        redesignIdea: this.regenerateLesson.redesignIdea
                    }
                    // 验证通过，发送确认事件
                    this.$emit('confirmRedesignLesson', data)
                } else {
                    // 验证失败，不做任何操作
                    return false;
                }
            });
        },

        // 输入内容变化
        redesignIdeaChange(value) {
            this.regenerateLesson.redesignIdea = value;
            this.$emit('input', value)
        },

        // 关闭弹框前置操作
        handleClose() {
            // 关闭弹框
            this.$emit('update:dialogVisible', false)
        },
        // 获取测评点的 Abbreviations 数组
        getLastLevelAbbreviations(measures) {
            let abbreviations = [];
            // 如果测评点不存在或者不是数组，则返回空数组
            if (!measures || !Array.isArray(measures)) {
                return abbreviations;
            }

            measures.forEach(measure => {
                // 如果测评点 ID 存在
                if (measure.id) {
                    // 如果测评点没有子测评点，则将测评点 Abbreviations 添加到数组中
                    if (!measure.children || measure.children.length === 0) {
                        abbreviations.push(measure.abbreviation);
                    } else if (measure.children && measure.children.length > 0) {
                        // 如果测评点有子测评点，则递归获取子测评点的 Abbreviations
                        abbreviations = abbreviations.concat(this.getLastLevelAbbreviations(measure.children));
                    }
                }
            });
            // 返回测评点的 Abbreviations 数组
            return abbreviations;
        },
    }
}
</script>

<style lang="scss" scoped>
/deep/ .update-standards-and-regenerate-dialog {
    height: unset !important;
    margin-top: 15vh !important;
    background-color: #fff !important;

    .el-dialog__header {
        padding: 24px;
    }

    .el-dialog__body {
        padding: 0 24px 16px;
    }

    .el-dialog__footer {
        padding: 8px 24px 24px;
    }

    .el-input__count {
        line-height: 20px !important;
    }

    .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
    }

    .el-form-item__label {
        font-weight: 600;
        font-size: 16px;
        color: #111c1c;
        line-height: 22px;
    }

    .el-form-item {
      margin-bottom: 24px;
      display: block !important;
    }
}

.m-b-sm {
    margin-bottom: 16px;
}

.w-full {
    width: 100%;
}

.required-star {
    color: #F56C6C;
    margin-right: 4px;
    vertical-align: middle;
}

.update-lesson-measure-select {
    /deep/ .el-select__tags {
        max-height: 80px;
        overflow-y: auto;
    }
}

.el-form {
    /deep/ .el-form-item {
        margin-bottom: 20px;
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;

    .el-button {
        padding: 10px 20px;
        margin-left: 10px;
    }
}

/deep/ .el-textarea__inner {
    padding: 10px 12px;
    font-family: inherit;
}

/deep/ .el-select-dropdown__item {
    padding-right: 40px !important;
    padding-left: 20px !important;
    padding-top: 6px !important;
    padding-bottom: 6px !important;
    line-height: 20px !important;
    height: auto !important;
    word-break: break-word !important;
    flex-wrap: wrap !important;
}

/deep/ .el-select-dropdown__item.selected {
    color: #409EFF;
    font-weight: bold;
}

/deep/ .el-option-group__title {
    padding: 10px 15px;
    font-size: 14px;
    font-weight: bold;
    color: #606266;
    background-color: #f5f7fa;
}

// 控制选项宽度
/deep/ .el-select-dropdown {
    width: 0;
    padding-bottom: 8px;
}

</style>