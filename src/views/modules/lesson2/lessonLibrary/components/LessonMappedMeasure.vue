<template>
  <div class="lesson-mapped-measure ipad_width" style="cursor:default;display: flex">
    <el-tag
      type="info"
      effect="light"
      color="rgb(245, 245, 245)"
      size="small"
      :hit="true">
      {{ measure.applicableFramework }}
    </el-tag>
    <el-tag
      type="info"
      effect="light"
      color="#FFF"
      size="small"
      :hit="true">
      <div v-if="measure.applicableFramework !== 'CA-PLF'">
        {{ measureAbbreviation }}
      </div>
      <div v-else>
        <div class="abbreviationHidden" :title="measureAbbreviation" :style="{'max-width': !showDetail ? '160px' : ''}">{{ measureAbbreviation }}</div>
        &nbsp;<span style="float: right;">{{ measure.abbreviation.substring(measure.abbreviation.length-3,measure.abbreviation.length) }}</span>
      </div>
    </el-tag>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'LessonMappedMeasure',
  props: ['measure'],
  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser // 当前用户
    }),
    showDetail () {
      // 从缓存中获取按钮的值，根据不同情况采用不同的分行
      const CACHE_KEY = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
      let showDetail = localStorage.getItem(CACHE_KEY)
      if (showDetail) {
        showDetail = JSON.parse(localStorage.getItem('SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id))
      } else {
        showDetail = false
      }
      return showDetail
    },
    measureAbbreviation () {
      if (this.measure.applicableFramework === 'CA-PLF') {
        return this.measure.abbreviation.substring(0,this.measure.abbreviation.length - 3)
      } else {
        return this.measure.abbreviation
      }
    }
  }
}
</script>

<style scoped lang="less">
@media only screen and (max-width:1199px){
  //ipad
  .lesson-mapped-measure {
    & > :first-child {
      border-radius: 4px 0 0 4px;
      border-color: #e5dddd;
      border-style: solid none solid solid;
      color: #333;
      font-size: 14px;
      float: left;
    }

    & > :last-child {
      border-color: #e5dddd;
      border-radius: 0 4px 4px 0;
      border-style: solid solid solid none;
      color: #333;
      font-size: 14px;
      max-width: 306px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .abbreviationHidden {
    display: inline-block;
    max-width: 303px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .ipad_width{
    max-width: 300px;
  }
}
@media only screen and (min-width:1200px){
  //web
  .lesson-mapped-measure {
    & > :first-child {
      border-radius: 4px 0 0 4px;
      border-color: #e5dddd;
      border-style: solid none solid solid;
      color: #333;
      font-size: 14px;
      float: left;
    }

    & > :last-child {
      border-color: #e5dddd;
      border-radius: 0 4px 4px 0;
      border-style: solid solid solid none;
      color: #333;
      font-size: 14px;
      max-width: 306px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .abbreviationHidden {
    display: inline-block;
    max-width: 303px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
