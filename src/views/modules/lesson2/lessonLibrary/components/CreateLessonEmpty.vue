<template>
  <div class="create-lesson-empty" @click="handleClick">
    <div class="content-wrapper">
      <div class="icon-wrapper ai-btn">
        <i class="el-icon-plus"></i>
      </div>
      <h3 class="title">{{ $t('loc.createLessonBtn') }}</h3>
      <p class="description">{{ $t('loc.lessonWelcomeDescription') }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CreateLessonEmpty',
  methods: {
    handleClick() {
      // 触发点击事件，由父组件处理
      this.$emit('add-new-lesson')
    }
  }
}
</script>

<style scoped lang="less">
.create-lesson-empty {
  width: 100%;
  height: 360px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px dashed #00c6d7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 20px;
  grid-column: 1 / -1; /* 确保在网格布局中占据整行 */

  &:hover {
    border-color: #00a8b7;
    background-color: #f5fdff;
  }

  .content-wrapper {
    text-align: center;
    max-width: 400px;
  }

  .icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #8a7aef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 10px rgba(138, 122, 239, 0.2);

    i {
      font-size: 30px;
      color: white;
    }
  }

  &:hover .icon-wrapper {
    transform: scale(1.05);
  }

  .title {
    font-size: 20px;
    color: #00c6d7;
    margin-bottom: 10px;
    font-weight: 500;
  }

  .description {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
    margin: 0 auto;
    max-width: 300px;
  }
}

@media screen and (max-width: 768px) {
  .create-lesson-empty {
    min-height: 250px;
    
    .icon-wrapper {
      width: 50px;
      height: 50px;
      
      i {
        font-size: 24px;
      }
    }
    
    .title {
      font-size: 18px;
    }
    
    .description {
      font-size: 13px;
    }
  }
}
</style> 