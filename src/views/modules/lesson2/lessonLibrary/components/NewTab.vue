<template>
  <el-button type="text" icon="el-icon-copy-document" @click="clickHandler" style="color:#999;">
    {{ $t('loc.lessons2LessonDetailNewTab') }}
  </el-button>
</template>
<script>
export default {
  name: 'NewTab',
  props: ['to'],
  methods: {
    clickHandler() {
      // 在新窗口打开
      let router = this.$router.resolve(this.to);
      window.open(router.href, '_blank')
    }
  }
}
</script>