<template>
  <!-- 历史版本列表项 -->
  <div
    class="lesson-history-item"
    :class="{ 'current': isCurrent, 'active': isActive }"
    @click="$emit('click', historyItem)"
    ref="historyItem"
  >
    <!-- 历史版本头部 -->
    <div class="item-header">

      <div class="item-status">
        <span class="status-dot" v-if="isCurrent"></span>
        <div class="item-date">{{ historyItem.date ? $moment.utc(historyItem.date).local().format('MM/DD/YYYY, h:mm A') : '' }}</div>
      </div>
      <div class="item-status" v-if="isCurrent">
        <el-col class="lesson-topic-field-value radius radius-30">
                {{ $t('loc.lessonCurrentVersion') }}
              </el-col>
      </div>
    </div>
    <!-- 历史版本内容 -->
    <div class="item-content">
      <div class="content-wrapper" ref="contentWrapper">
          <div v-if="historyItem.sourceVersionTime" class="restore-details">Restore from: {{ $moment.utc(historyItem.sourceVersionTime).local().format('MM/DD/YYYY, h:mm A') }}</div>
        <el-tooltip
          v-if="historyItem.description"
          transition="el-fade-in-linear"
          :content="versionDescription"
          placement="top"
          :open-delay="300"
          popper-class="max-width-600 font-size-14"
        >
          <div class="item-description overflow-ellipsis-two" ref="description">{{ versionDescription }}</div>
        </el-tooltip>
      </div>
    </div>
    <!-- 历史版本底部 -->
    <div class="item-footer">
      <el-button
        v-if="!isCurrent"
        type="primary"
        size="small"
        class="restore-button"
        @click.stop="handleRestoreClick"
      >
        {{ $t('loc.lessonRestore') }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LessonHistoryItem',
  props: {
    historyItem: {
      type: Object,
      required: true
    },
    isCurrent: {
      type: Boolean,
      default: false
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    };
  },
  mounted() {
    this.$nextTick(() => {
    });
  },
  beforeDestroy() {
  },
  updated() {
    this.$nextTick(() => {
    });
  },
  computed: {
    versionDescription () {
      if (!this.historyItem || !this.historyItem.description) {
        return ''
      }
      return this.historyItem.description
        .replace('Agency Owner ', '')
        .replace('enhanced the lesson plan using AI', 'enhanced the lesson plan')
        .replace(/^.*?(?=\supdated\b)/, 'You')
        .replace(/^.*?(?=\screated\b)/, 'You')
        .trim()
    }
  },
  methods: {
    handleRestoreClick () {
      if (this.$route.name === 'AddLesson') {
        // 创建课程恢复历史版本点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_restore')
      } else if (this.$route.name === 'EditLesson') {
        // 编辑课程恢复历史版本点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_edit_click_restore')
      }
      this.$emit('restore', this.historyItem)
    }
  }
}
</script>

<style lang="scss" scoped>
.lesson-history-item {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: 1px solid #ebeef5;
  margin-bottom: 12px;
  will-change: transform, box-shadow;
  max-height: 168px;
  display: flex;
  flex-direction: column;

  &:hover {
    background-color: #f5f7fa;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &.current:not(.active) {
    border: 1px solid #d9ecff;
  }

  &.active {
    // background-color: #deedfc;
    background-color: #DDF2F3;
    border: 1px solid #b3d8ff;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    transform: translateY(-3px);
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    flex-shrink: 0;

    .item-status {
      display: flex;
      align-items: center;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #10B3B7;
        margin-right: 6px;
      }

      .status-text {
        margin-right: 8px;
        font-size: 14px;
        color: #10B3B7;
        font-weight: 600;
        text-shadow: 0 0 1px rgba(16, 179, 183, 0.2);
      }
    }

    .item-date {
      font-size: 15px;
      color: #303133;
      font-weight: 500;
    }
  }

  .item-content {
    margin-bottom: 4px;
    flex-grow: 1;
    overflow: hidden;
    position: relative;

    .content-wrapper {
      overflow: hidden;
      transition: all 0.3s ease;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 24px;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
    }

    .restore-details {
      font-size: 13px;
      color: #606266;
      font-weight: normal;
      margin-bottom: 8px;
      white-space: normal;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-height: 1.5;
      transition: all 0.2s ease;
      padding: 0;
      border-radius: 0;
      font-style: italic;
    }

    .item-description {
      padding: 0;
      font-size: 14px;
      color: #303133;
      opacity: 0.85;
      line-height: 1.6;
      white-space: normal;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      transition: all 0.2s ease;
    }

    .empty-content {
      display: flex;
      align-items: center;
      color: #909399;
      font-size: 13px;

      i {
        margin-right: 4px;
      }
    }
  }

  .item-footer {
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    margin-top: auto;
    position: relative;

    .restore-button {
      padding: 6px 12px;
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
.lesson-topic-field-value {
  font-weight: 600;
  font-size: 12px;
  padding: 0 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: fit-content;
  background: #DDF2F3;
  border: 1px solid #10B3B7;
  border-radius: 20px;
  flex: none;
  color: #10B3B7;
  flex-grow: 0;
}
</style>