<template>
  <el-dialog class="lesson-detail-tips"
             title
             :visible.sync="dialogVisible"
             width="580px"
             top="calc(50vh - 100px)"
             append-to-body
             :show-close="false">
    <span slot="title" class="font-size-20">{{ $t('loc.Tips') }}</span>
    <div class="font-size-16 text-left">
      {{ $t('loc.lessons2LessonDetailTipInfo') }}
    </div>
    <div slot="footer" style="text-align: right;">
      <el-button type="primary" class="lesson-tip-button" @click="close()" size="medium">
        {{ $t('loc.lessons2Okay') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import Api from '../../../../../../src/api/lessons2/index'

export default {
  name: 'LessonDetailTips',
  components: {},
  data() {
    return {
      dialogVisible: false, // 提示窗口开关
    }
  },
  methods: {
    close() {
      this.dialogVisible = false
    }
  },
  created() {
    // 判断用户是否点击过课程提示
    Api.getTeacherDialog().then(res => {
      if (!res.click) {
        this.dialogVisible = true;
        Api.addTipRecords().then(res => {
        })
      }
    })
  }
}
</script>

<style scoped lang="less">
.lesson-detail-tips /deep/ & {
  .el-dialog__header {
    padding: 26px 24px 22px;
    font-size: 16px;
  }

  .el-dialog__body {
    padding: 0 24px;
    line-height: 20px;
    font-size: 14px;
    color: #333;
    text-align: justify;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}

.lesson-tip-button {
  background: rgba(45, 179, 184, 1)
}
</style>