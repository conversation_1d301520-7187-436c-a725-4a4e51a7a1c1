<template>
  <div v-if="added + originalCount > 0">
    <!-- 折叠子评论 -->
    <div v-if="comments.length" style="color:#28B3B7;font-size:14px;margin-top: -10px;">
      <el-button v-if="!folded" type="text" @click="folded = !folded">
        <i class="el-icon-caret-top"></i>
        <span>{{ $t('loc.lesson2CommentHidePrefix') }} </span>
        <span>{{ comments.length !== 1 ? comments.length : '' }}</span>
        <span> {{ comments.length !== 1 ? $t('loc.lesson2CommentSSuffix') : $t('loc.lesson2CommentSuffix') }}</span>
      </el-button>
      <el-button v-else type="text" @click="folded = !folded">
        <i class="el-icon-caret-bottom"></i>
        <span>{{ $t('loc.lesson2CommentViewTitle') }} </span>
        <span>{{ comments.length !== 1 ? comments.length : '' }}</span>
        <span> {{ comments.length !== 1 ? $t('loc.lesson2CommentSSuffix') : $t('loc.lesson2CommentSuffix') }}</span>
      </el-button>
    </div>
    <!--子评论内容-->
    <comment-item v-for="comment in comments" :key="comment.id" v-if="!folded"
                  :lesson-create-user-id="lessonCreateUserId" :comment="comment" :root="root" :lesson-id="lessonId"
                  @reply="$emit('reply')" @delete="handleDelete" :disabled="disabled"/>
    <pager :loading="loading" :more="!noMore" v-if="!noMore && !folded" @click="loadPage"/>
  </div>
</template>

<script>
import Api from '../../../../../../api/lessons2'
import CommentEditor from "./Editor";
import {mapState} from "vuex";
import CommentItem from "./Item";
import Pager from "./Pager";

export default {
  name: 'SubCommentList',
  components: {
    Pager,
    CommentItem,
    CommentEditor
  },
  props: [
    'lessonId',
    'lessonCreateUserId',
    'root',
    'disabled'
  ],
  data() {
    return {
      comments: [], // 存放获取的评论列表
      pageSize: 5, // 页大小
      pageNum: 1, // 分页信息
      total: 0,
      noMore: false, // 加载完成
      folded: false, // 折叠评论
      loading: false, // 加载中
      added: 0,// 手动添加的评论数
      descendants: [...this.root.descendants],// 最新评论列表
      originalCount: 0, // 根节点原本存储的评论数
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    })
  },
  watch: {
    'root.descendants': {
      deep: true,
      handler() {
        // 最新评论
        let last = this.descendants[0];
        // 新添加的评论数，新添加的评论都在最前
        let addedCount = last ? this.root.descendants.indexOf(last) : this.root.descendants.length;
        let addedComments = this.root.descendants.slice(0, addedCount);
        this.descendants.unshift(...addedComments);
        // 手动添加评论的，放入已加载评论列表中
        this.comments.unshift(...addedComments)
        this.added += addedComments.length;
        this.total += addedComments.length;
      }
    }
  },
  methods: {
    // 分页加载子评论列表
    loadPage() {
      if (this.loading || this.noMore) {
        return;
      }
      this.loading = true;
      // 第一页直接从根节点的最新评论列表中加载
      if (this.pageNum === 1) {
        this.pageNum++;
        this.total = this.root.descendants.length;
        this.comments = this.comments.concat(this.root.descendants.slice(this.added, this.added + this.pageSize));
        this.noMore = this.total <= this.comments.length;
        this.loading = false;
        return;
      }
      // 后续页从数据库中加载
      let page = this.getPage(this.added + (this.pageNum - 1) * this.pageSize, this.pageSize);
      Api.getSubComments(page.pageSize, page.pageNum, this.root.id)
        .then((response) => {
          this.pageNum++;
          this.total = response.total;
          this.comments = this.comments.concat(...response.records.slice(page.from, page.to));
          this.noMore = this.total <= this.comments.length;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    reload() {
      this.comments = [];
      this.pageNum = 1;
      this.total = 0;
      this.noMore = false;
      this.folded = false;
      this.loadPage();
    },
    // 重新计算分页数据 offset 已加载数，size页大小
    getPage(offset, size) {
      let from, to, pageSize, pageNum;
      for (pageSize = size; pageSize < 2 * size; pageSize++) {
        if (offset % pageSize + size <= pageSize) {
          break;
        }
      }
      from = offset % pageSize;
      to = from + size;
      pageNum = (offset - from) / pageSize + 1
      return {from, to, pageNum, pageSize}
    },
    handleDelete(...args) {
      let [comment, deleteType] = args;
      if (deleteType === 'COMPLETE') {
        let index = this.comments.indexOf(comment);
        if (index > -1) {
          this.comments.splice(index, 1);
          this.added--;
          this.total--;
        }
      }
      this.$emit('delete');
    }
  },
  created() {
    this.originalCount = (this.root.descendants || []).length;
    // 进行评论的初始化显示
    this.loadPage();
  },
}
</script>

<style scoped lang="less">
// 评论总数显示
.lesson-comment-total-title {
  font-size: 14px;
}

// 评论区提示标题
.lesson-comment-tips-title {
  font-size: 18px;
}

// 评论输入框 （初始状态）
.lesson-comment-input /deep/ .el-input__inner {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #dadde0;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
}

// 有内容
.lesson-comment-input-true /deep/ .el-input__inner {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #333;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
}

// 回复区域 （初始状态）
.lesson-comment-reply-box {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #dadde0;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
  position: relative;
}

// 回复区域 （有内容）
.lesson-comment-reply-box-true {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #333;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
  position: relative;
}

// 回复输入框区域
.lesson-comment-reply-input /deep/ .el-input__inner {
  border: none;
  background: rgba(221, 221, 221, 0);
  padding: 0%;
}

// 底部间隔
.lesson-comment-margin-bottom {
  margin-bottom: 10px;
}

// 顶部间隔
.lesson-comment-margin-top {
  margin-bottom: 30px;
}

// 昵称
.lesson-comment-nickname {
  color: #999999;
  font-size: 14px;
  text-align: left
}

// 评论发布时间
.lesson-comment-create-time {
  font-size: 14px;
  margin: 0px 10px 0px 30px;
  color: #999999;
  display: inline-block;
}

// 作者标签
.author-tag {
  display: inline-block;
  margin: 0px 0px 10px 10px;
  width: 60px;
  height: 20px;
  font-size: 12px;
  color: #FFFFFF;
  border-radius: 10px;
  background: #FFBA00;
  text-align: center;
  line-height: 20px;
}

// 回复按钮
.lesson-comment-reply-button {
  font-size: 12px;
  color: #28B3B7;
  background: rgba(40, 179, 183, 0.2);
  border-radius: 2px;
  padding: 5px 10px
}

// 结尾提示语
.lesson-comment-end-tips {
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 评论内容
.lesson-comment-content {
  font-size: 14px;
  display: inline;
  line-height: 24px;
  color: #333333;
}

//  提示语样式
/deep/ .el-textarea__inner::-moz-placeholder {
  color: #777;
}

/deep/ .el-textarea__inner::-webkit-input-placeholder {
  color: #777;
}

.lesson-comment-delete-content {
  width: 240px;
  font-size: 14px;
  // border-radius: 12.5px;
  // background-color: #ddd;
  padding: 4px 35px 4px 0px;
  color: #777;
  line-height: 25px;
}

.lesson-comment-delete-content-box {
  height: 35px;
}

.lesson-comment-split-line /deep/ .el-divider {
  margin: 10px 0px;
}

.lesson-comment-tag-space {
  padding-right: 5px;
}

.lesson-comment-show-more {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #EEEEEE;
}

.split-line {
  width: 50px;
  height: 1px;
  background: #999;
  display: inline-block;
  margin: 9px;
}
</style>
