<template>
  <div @click.native.stop="stopClickHandler">
    <div v-if="more" style="text-align: center">
      <el-button size="medium " type="text" :icon="loading && 'el-icon-loading' || ''"
                 @click="!loading && $emit('click')">
        {{ $t('loc.lessons2CommentShowMore') }}
      </el-button>
    </div>
     <el-divider v-else>{{ $t('loc.lessons2CommentEndTitle') }}</el-divider>
  </div>
</template>
<script>
export default {
  name: 'Pager',
  props: [
    // 是否还有更多
    'more',
    // 加载中
    'loading'
  ],
  methods: {
    stopClickHandler(evt) {

    }
  }
}
</script>
<style scoped lang="less">

</style>
