<!-- 发表评论 -->
<template>
  <div class="lesson-comment-editor">
    <el-avatar size="medium" :src="currentPluginUser.avatar_url || currentUser.userInfo.avatarUrl" shape="circle" class="avatar"/>
    <div>
      <div v-if="replyTo">
        <span>{{ currentUser.userName | formatUserName }}</span>
        <span v-if="currentUser.userInfo.id === lessonCreateUserId" class="author-tag">
            {{ $t('loc.lesson2CommentLessonAuthorTag') }}
          </span>
      </div>
      <div :class="['lesson-comment-editor-input',{hasText:showOperation}]">
          <span style="height:100%;flex: none;padding: 5px 0 5px 15px;" v-if="replyTo">
            @{{ replyTo.userName | formatUserName }}:
          </span>
        <el-input type="textarea" autosize :placeholder="replyTo ? $t('loc.lessons2AddReply') : $t('loc.lesson2CommentInputTips')"
                  v-model="comment.content" ref="lesson-comment-editor-inner" @focus="handleFocus(true)"/>
      </div>
      <!--发表按钮及取消按钮-->
      <div v-show="showOperation" class="lesson-comment-editor-operation">
        <el-button  @click="cancelCommentHandler"  plain>
          {{ $t('loc.cancel') }}
        </el-button>
        <el-button @click="submitCommentHandler" type='primary' :disabled="commentLoading" >
          {{ replyTo ? $t('loc.lessons2Reply') : $t('loc.comment') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
import {mapState} from "vuex";
import Api from "../../../../../../api/lessons2";

export default {
  name: 'CommentEditor',
  props: ['lessonId', 'lessonCreateUserId', 'replyTo'],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      currentPluginUser: state => state.cgAuth.user // 当前插件用户
    }),
  },
  data() {
    return {
      comment: {
        content: ''
      },
      showOperation: false,
      commentLoading: false // 课程评论请求中
    }
  },
  methods: {
    submitCommentHandler() {
      // 设置课程评论请求中，防止重复点击导致重复评论
      this.commentLoading = true
      Api.commentLesson(this.comment.content, this.lessonId, this.replyTo && this.replyTo.id)
        .then((res) => {
          this.commentLoading = false
          this.$emit("comment", 'success', {
            id: res.id,
            content: this.comment.content,
            userId: this.currentUser.userInfo.id,
            userName: this.currentUser.userInfo.firstName + ' ' + this.currentUser.userInfo.lastName,
            userAvatarURL: this.currentUser.userInfo.avatarUrl,
            createAtUtc: new Date().getTime() + new Date().getTimezoneOffset() * 60000,
            deleted: false,
            replyTo: this.replyTo,
            descendants: []
          });
          this.comment.content = '';
          this.showOperation = false;
        })
        // 发表\回复失败
        .catch(error => {
          this.commentLoading = false
          this.$emit("comment", 'fail', {replyTo: this.replyTo, error})
        })
    },
    cancelCommentHandler() {
      this.$emit('comment', 'cancel', {replyTo: this.replyTo});
      this.comment.content = '';
      this.showOperation = false;
    },
    focus() {
      let ref = this.$refs['lesson-comment-editor-inner'];
      ref && ref.focus();
    },
    handleFocus(focus) {
      this.showOperation = focus;
    }
  }
}
</script>
<style scoped lang="less">
.lesson-comment-editor {
  color: #999999;
  font-size: 14px;
  display: flex;
  align-items: flex-start;

  & > :first-child {
    flex: none;
    margin: 8px 16px 0 0;
  }

  & > :last-child {
    flex: auto;
    margin-top: 3px;
  }
}

.avatar {
  img{
    width: 100% !important;

  }
}

.lesson-comment-editor-input {
  border-radius: 24px;
  resize: none;
  display: flex;
  width: 100%;
  border: 1px solid #dadde0;
  padding: 8px 5px;

  & /deep/ .el-textarea__inner {
    background-color: transparent;
    padding: 5px 15px 5px 15px;
    border: none;
    resize: none;

    &::placeholder {
      color: #C0C4CC;
    }
  }

  &:hover {
    border-color: #10B3B7;
  }

  &.hasText {
    border-color: #10B3B7;
  }
}

// 作者标签
.author-tag {
  display: inline-block;
  margin: 0 0 10px 10px;
  width: 60px;
  height: 20px;
  font-size: 12px;
  color: #FFFFFF;
  border-radius: 10px;
  background: #FFBA00;
  text-align: center;
  line-height: 20px;
}

.lesson-comment-editor-operation {
  text-align: right;
  margin-top: 13px;

  & > * {
    border: none;
  }

  & > :last-child {
    margin-left: 20px;
    padding: 6px 9px;
  }
}
</style>