<template>
  <comment-item :root="root" :comment="root" :lesson-create-user-id="lessonCreateUserId" @reply="$emit('reply')"
                @delete="handleDelete" :lesson-id="lessonId" :disabled="disabled">
    <sub-comment-list :root="root" :lesson-create-user-id="lessonCreateUserId" :lesson-id="lessonId"
                      @reply="$emit('reply')" @delete="handleDelete" :disabled="disabled"/>
  </comment-item>
</template>
<script>
import CommentEditor from "./Editor";
import SubCommentList from "./SubList";
import CommentItem from "./Item";

export default {
  name: 'CommentRootItem',
  components: {
    CommentItem,
    SubCommentList,
    CommentEditor
  },
  props: [
    // 根评论
    'root',
    // 课程作者用户 ID
    'lessonCreateUserId',
    // 课程 ID
    'lessonId',
    // 是否禁用
    'disabled'
  ],
  methods:{
    handleDelete(...args){
      this.$emit('delete', ...args)
    }
  }
}
</script>