<template>
  <div class="lesson-comment-item">
    <el-avatar size="medium" :src="currentPluginUser.avatar_url || userAvatarURL" shape="circle" class="avatar"></el-avatar>
    <div>
      <div>
        <!-- 评论人名称 -->
        <span class="title-font-14">
          {{ currentUser.userName | comment.userName | formatUserName }}
        </span>
        <!-- 课程作者标识 -->
        <el-tag v-if="showAuthorTag" effect="dark" size="mini" type="warning" class="el-tag">
          {{ $t('loc.lesson2CommentLessonAuthorTag') }}
        </el-tag>
      </div>
      <div class="lesson-comment-item-content">
        <!-- 已删除 -->
        <span class="title-font-16-regular" v-if="deleted">
          {{ content }}
        </span>
        <!-- 未删除 -->
        <div v-else>
          <span v-if="comment.replyTo">
            @{{ comment.replyTo.userName | formatUserName }}:
          </span>
          <!-- 评论内容 -->
          <span class="title-font-16-regular" v-html="content">
          </span>
          <!-- 回复按钮 -->
          <el-button v-if="showReply && !disabled" size="mini" round @click="showEditor = true;"
                     class="lesson-comment-item-reply-btn">
            {{ $t('loc.lesson2CommentReplyComment') }}
          </el-button>
          <!-- 删除按钮 -->
          <el-button v-if="showDelete && !disabled"
                     class="lg-margin-left-20"
                     type="text" size="mini" round @click="deleteComment">
            {{ $t('loc.delete') }}
          </el-button>
          <diV>
            <!-- 创建时间 -->
            <span class="lg-color-info">
              {{ createTime }}
            </span>
          </div>
        </div>
      </div>
      <!-- 评论输入框 -->
      <comment-editor :lesson-id="lessonId" :lesson-create-user-id="lessonCreateUserId" :reply-to="comment"
                      @comment="commentHandler" v-if="showEditor && !disabled" ref="lesson-comment-item-editor"/>
      <slot/>
    </div>
  </div>
</template>
<script>
import {mapState} from "vuex";
import CommentEditor from "./Editor";
import Api from "../../../../../../api/lessons2";
import Constants from "../../../../../../utils/constants";

export default {
  name: 'CommentItem',
  components: {
    CommentEditor
  },
  props: [
    // 根评论
    'root',
    // 当前评论
    'comment',
    // 课程作者用户 ID
    'lessonCreateUserId',
    // 课程 ID
    'lessonId',
    // 是否禁用
    'disabled'
  ],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      currentPluginUser: state => state.cgAuth.user // 当前插件用户
    }),
    // 评论时间
    createTime() {
      return this.$moment(this.comment.createAtUtc)
        .format("MMM DD, YYYY");
    },
    // 是否展示删除按钮，仅评论人自己可以删除自己的评论
    showDelete() {
      return this.currentUser.userInfo.id === this.comment.userId;
    },
    // 作者评论标识，评论人为课程作者
    showAuthorTag() {
      return this.comment.userId === this.lessonCreateUserId;
    },
    // 是否展示回复按钮，不能自己回复自己的评论
    showReply() {
      return this.currentUser.userInfo.id !== this.comment.userId;
    },
    // 评论人头像
    userAvatarURL() {
      return this.comment.userAvatarURL || Constants.userAvatarURL;
    },
    // 评论删除标识
    deleted: {
      get() {
        return this.comment.deleted
      },
      set(value) {
        this.comment.deleted = value;
      }
    },
    // 评论内容
    content: {
      get() {
        this.comment.content = this.comment.content.trim()
        return this.comment.content.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ')
      },
      set(value) {
        this.comment.content = value;
      }
    }
  },
  data() {
    return {
      showEditor: false
    }
  },
  watch: {
    showEditor(value) {
      value && this.$refs['lesson-comment-item-editor'] && this.$refs['lesson-comment-item-editor'].focus();
    }
  },
  methods: {
    // 编辑器监听函数：success、fail、cancel
    commentHandler(type, data) {
      this.showEditor = false;
      if (type === 'success') {
        this.root.descendants.unshift(data);
        this.$emit('reply', data)
      }
    },
    // 删除评论
    deleteComment() {
      const h = this.$createElement
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.commentDeleteTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        confirmButtonClass: 'el-button--danger',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            Api.deleteComment(this.comment.id)
              .then(({type}) => {
                done()
                this.deleted = true;
                this.content = this.$t('loc.commentDeleteShow');
                this.$emit('delete', this.comment, type)
              })
          } else {
            done()
          }
        }
      }).then(action => {
      })

    },
  }
}
</script>
<style scoped lang="less">
.lesson-comment-item {
  display: flex;
  align-items: flex-start;

  & > :first-child {
    flex: none;
    margin-right: 16px;
  }

  & > :last-child {
    flex: auto;
    margin-top: 3px;

    & > .lesson-comment-item-content {
      margin: 8px 0;
    }
  }
}

.avatar {
  img {
    width: 100% !important;

  }
}

.el-tag {
  line-height: 18px;
  margin-left: 10px;
}

.lesson-comment-item-reply-btn {
  margin-left: 20px;
  color: #28B3B7;
  border: none;
  border-radius: 2px;
  font-size: 12px;
  background-color: rgba(40, 179, 183, 0.2);
  padding: 6px 9px;
}
</style>