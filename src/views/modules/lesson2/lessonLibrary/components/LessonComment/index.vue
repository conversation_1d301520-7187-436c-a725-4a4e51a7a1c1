<!-- 课程评论 -->
<template>
  <div class="lesson-comment">
    <!-- 评论标题及统计区域 -->
    <el-row style="display: flex;justify-content: space-between" type="flex" class="lesson-comment-title">
      <el-col :span="12" class="lesson-comment-tips-title">
        {{ $t('loc.lesson2CommentTitle') }}:
      </el-col>
      <el-col :span="12" class="lesson-comment-total-title hidden-lg-and-up" :offset="12" >
        <span class="lesson-comment-nickname lesson-comment-tag-space">
          {{ $t('loc.lesson2CommentTotalTitle') }}:&nbsp{{ total }}
        </span>
<!--        <span>{{ total }}</span>-->
      </el-col>
      <el-col :span="12" class="lesson-comment-total-title hidden-md-and-down" :offset="16" >
        <span class="lesson-comment-nickname lesson-comment-tag-space">
          {{ $t('loc.lesson2CommentTotalTitle') }}:&nbsp{{ total }}
        </span>
        <!--        <span>{{ total }}</span>-->
      </el-col>
    </el-row>

    <!--发表评论-->
    <comment-editor :lesson-id="lessonId" :lesson-create-user-id="lessonCreateUserId"
                    @comment="commentHandler" class="lesson-comment-editor" v-if="!disabled"/>

    <!--评论显示区域-->
    <div v-for="(comment,index) in roots" :key="comment.id">
      <!-- 根评论 -->
      <comment-root-item :root="comment" :lesson-create-user-id="lessonCreateUserId" :lesson-id="lessonId"
                         @reply="total++" @delete="handleDelete" :disabled="disabled"/>

      <!-- 分隔线 -->
      <div v-if="noMore && index === roots.length - 1 && !onePage" class="the-end">
        <el-divider>
           <span>
              {{ $t('loc.lessons2CommentEndTitle') }}
           </span>
        </el-divider>
      </div>
      <el-divider v-else class="el-divider"></el-divider>

    </div>
    <pager :loading="loading" :more="!noMore" v-if="!noMore" @click="loadPage"/>
  </div>
</template>

<script>
import Api from '../../../../../../api/lessons2'
import CommentEditor from './Editor'
import Pager from './Pager'
import CommentRootItem from './RootItem'

export default {
  name: 'LessonComment',
  components: {
    CommentRootItem,
    Pager,
    CommentEditor
  },
  props: [
    'lessonId',
    'lessonCreateUserId',
    'disabled'
  ],
  data () {
    return {
      roots: [], // 根评论列表
      total: 0, // 评论总数
      rootCommentTotal: 0, // 根评论总数
      pageSize: 10, // 页大小
      pageNum: 1, // 分页信息
      noMore: false, // 加载完成
      loading: false, // 根评论加载中
      added: 0,// 加载后手动添加、删除的根评论数量（删除时，该值为负数）
      onePage: false
    }
  },
  methods: {
    // 编辑器监听函数：success、fail、cancel
    commentHandler (type, data) {
      if (type === 'success') {
        this.roots.unshift(data)
        this.added++
        this.total++
        this.rootCommentTotal++
      }
    },
    // 分页加载根评论列表
    loadPage () {
      if (this.loading) {
        return
      }
      this.loading = true
      let page = this.getPage(this.added + (this.pageNum - 1) * this.pageSize, this.pageSize)
      Api.getComments(page.pageSize, page.pageNum, this.lessonId)
        .then((response) => {
          if (this.pageNum) {
            this.pageNum++
          }
          // 加载成功，进行页面数据渲染
          this.total = response.total
          this.rootCommentTotal = response.roots.total
          this.roots = this.roots.concat(...response.roots.records.slice(page.from, page.to))
          this.noMore = this.rootCommentTotal <= this.roots.length
          if (this.pageNum === 2 && this.noMore) {
            this.onePage = true
          }
        })
        .finally(() => this.loading = false)
    },
    // 重新计算分页数据 offset 已加载数，size页大小
    getPage (offset, size) {
      let from, to, pageSize, pageNum
      for (pageSize = size; pageSize < 2 * size; pageSize++) {
        if (offset % pageSize + size <= pageSize) {
          break
        }
      }
      from = offset % pageSize
      to = from + size
      pageNum = (offset - from) / pageSize + 1
      return { from, to, pageNum, pageSize }
    },
    handleDelete (...args) {
      this.total--
      let [comment, deleteType] = args
      if (deleteType === 'COMPLETE') {
        if (this.roots.includes(comment)) {
          this.roots.splice(this.roots.indexOf(comment), 1)
          this.added--
        }
      }
    }
  },
  created () {
    // 进行评论的初始化显示
    this.loadPage()
  }
}
</script>

<style scoped lang="less">
.el-divider /deep/ {
  margin: 10px 0 10px 56px;
  width: calc(100% - 56px);

  & .el-divider__text {
    color: #C0C4CC;
  }
}

.the-end {
  width: 250px;
  margin: 0 auto
}

.lesson-comment {
  & > .lesson-comment-title {
    margin: 18px 0;
  }

  & > .lesson-comment-editor {
    margin-bottom: 21px;
  }
}

// 评论总数显示
.lesson-comment-total-title {
  font-size: 14px;
}

// 评论区提示标题
.lesson-comment-tips-title {
  font-size: 18px;
  font-weight: bold;
}

// 评论输入框 （初始状态）
.lesson-comment-input /deep/ .el-input__inner {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #dadde0;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
}

// 有内容
.lesson-comment-input-true /deep/ .el-input__inner {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #333;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
}

// 回复区域 （初始状态）
.lesson-comment-reply-box {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #dadde0;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
  position: relative;
}

// 回复区域 （有内容）
.lesson-comment-reply-box-true {
  overflow-y: hidden;
  border-radius: 24px;
  background: rgba(221, 221, 221, 0);
  resize: none;
  display: block;
  width: 100%;
  padding: 0px 24px;
  border: 1px solid #333;
  height: 48px;
  font-size: 14px;
  line-height: 48px;
  position: relative;
}

// 回复输入框区域
.lesson-comment-reply-input /deep/ .el-input__inner {
  border: none;
  background: rgba(221, 221, 221, 0);
  padding: 0%;
}

// 底部间隔
.lesson-comment-margin-bottom {
  margin-bottom: 10px;
}

// 顶部间隔
.lesson-comment-margin-top {
  margin-bottom: 30px;
}

// 昵称
.lesson-comment-nickname {
  color: #999999;
  font-size: 14px;
  text-align: left;
  float: right;
}

// 结尾提示语
.lesson-comment-end-tips {
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 评论内容
.lesson-comment-content {
  font-size: 14px;
  display: inline;
  line-height: 24px;
  color: #333333;
}

//  提示语样式
/deep/ .el-textarea__inner::-moz-placeholder {
  color: #777;
}

/deep/ .el-textarea__inner::-webkit-input-placeholder {
  color: #777;
}

.lesson-comment-delete-content {
  width: 240px;
  font-size: 14px;
  // border-radius: 12.5px;
  // background-color: #ddd;
  padding: 4px 35px 4px 0px;
  color: #777;
  line-height: 25px;
}

.lesson-comment-tag-space {
  padding-right: 1px;
}

.lesson-comment-show-more {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #EEEEEE;
}

.split-line {
  width: 50px;
  height: 1px;
  background: #999;
  display: inline-block;
  margin: 9px;
}
</style>
