<template>
  <div>
    <div v-if="lesson" class="lesson-info">
      <el-row type="flex" class="lesson-info-header" :class="{'mobile-layout': lgIsMobile}">
        <!-- 课程封面 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="lesson-info-header-media-viewer">
          <lesson-media-viewer :url="coverURL" :source="coverSource"/>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="lesson-info-header-content"
                :class="{'mobile-content': lgIsMobile, 'have-lesson-from-unit-tag': showFromUnit}"
                style="display: flex; flex-flow: column nowrap; justify-content: center;">
          <!-- 课程名 -->
          <div class="lesson-name add-padding-lr-6" style="width: 80%; margin: 0 auto" :title="lesson.name" :style="{color:!lesson.name && '#C0C4CC'}">
            <div class="lesson-name-content">
              <span class="lesson-name-text">{{ lesson.name || 'Unnamed Lesson' }}</span>
              <!--  -->
              <el-tag v-if="lesson.isAdaptedLesson" class="adapted-tag font-weight-400 content-vertical-middle"
                      size="small">{{ $t('loc.adaptUnitPlanner25') }}
              </el-tag>
            </div>
          </div>
          <!--作者和时间应该放在一行-->
          <el-row class="lesson-author-date-row">
            <!-- 最新更新时间 -->
            <div class="lesson-field-value">
              {{ $moment(lesson.updateTime).format('MMM DD, YYYY') }}
            </div>
            <!-- 作者名 -->
            <div class="lesson-author-name" :title="lesson.authorName || currentPluginUser.user_name || currentPluginUser.email || formatUserName" :style="isMC ? 'max-width: 66%;' :''">
              {{ $t('loc.lessons2LessonDetailToAuthorName') }} {{lesson.authorName || currentPluginUser.user_name || currentPluginUser.email || formatUserName}}
            </div>
          </el-row>
          <!-- 来自 Unit 的标签 -->
          <el-tooltip v-if="showFromUnit" popper-class="max-width-400" content="This lesson is linked to a Unit. Changes will sync with the original Unit plan." placement="top">
            <div class="lesson-from-unit-tag" @click="goToUnitDetail">
              <svg class="unit-icon lg-icon" style="flex: none;" width="16" height="16" aria-hidden="true">
                  <use xlink:href="#lg-icon-a-unitpaln"></use>
              </svg>
              <span class="unit-title" :title="lesson.unitTitle">{{ $t('loc.fromUnit') + ' ' + lesson.unitTitle }}</span>
              <i class="lg-icon lg-icon-arrow-right"></i>
            </div>
          </el-tooltip>
          <!-- 分隔线 -->
          <el-divider class="split-line"/>
          <el-col :span="24" class="lesson-field-label-age-topic">
            <!-- 年龄组 -->
            <div class="lesson-field-label lesson-age-field-label">
              <el-col class="framework-abbreviation-value radius radius-30"
                      :span="5"
                      v-if="frameworkAbbreviation">
                {{ frameworkAbbreviation }}
              </el-col>
              <el-col class="lesson-age-field-value radius radius-30"
                      :span="5"
                      v-if="lesson.ages && lesson.ages.length > 0"
                      v-for="(age) in lesson.ages"
                      :key="age">
                {{ formatAge(age) }}
              </el-col>
              <!-- 课堂类型标签 -->
              <el-col class="lesson-age-field-value radius radius-30"
                      v-if="showClassroomType"
                      :span="5">
                {{ getClassroomTypeLabel(lesson.classroomType) }}
              </el-col>
              <!-- 课程活动类型 -->
              <el-col class="lesson-topic-field-value radius radius-30"
                      :span="5"
                      v-if="lesson.activityType && !getNoActivityTheme().includes(lesson.activityType)">
                {{ lesson.activityTheme ? lesson.activityTheme + ' ' + lesson.activityType : lesson.activityType }}
              </el-col>
              <!-- 课程主题 -->
              <el-col class="lesson-topic-field-value radius radius-30"
                      v-for="theme in lesson.themes"
                      v-if="lesson.themes && lesson.themes.length > 0"
                      :span="5"
                      :key="theme.id">
                {{ theme.name }}
              </el-col>
            </div>

          </el-col>
          <el-row class="lesson-field-time ipad_width add-padding-t-10"
                  v-if="lesson.prepareTime || lesson.activityTime">
            <!-- 准备时间 -->
            <el-col class="lesson-field-time-item" v-if="lesson.prepareTime">
              <span class="lesson-field-value">
                {{ $t('loc.lesson2NewLessonFormLabelPrepareTime') }}
              </span>
              <span class="lesson-field-label">
                {{ lesson.prepareTime }} mins
              </span>
            </el-col>
            <!--            :class="{'before-item' : lesson.prepareTime}"-->
            <el-col class="lesson-field-time-item" :class="{'before-item' : lesson.prepareTime}"
                    v-if="lesson.activityTime">
              <!--              <span v-if="planPreview"></span>-->
              <!--               活动时间 -->
              <span class="lesson-field-value">
                {{ $t('loc.lesson2NewLessonFormLabelActivityTime') }}
              </span>
              <span class="lesson-field-label">
                {{ lesson.activityTime }} mins
              </span>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div>
        <!-- 框架 -->
        <div class="lesson-field-label">
          <h1 class="lesson-field-label lesson-title-label" v-if="lesson.framework">
            {{ $t('loc.lesson2NewLessonFormLabelFramework') }}
          </h1>
        </div>
        <div class="lesson-content-value">
          <span class="lesson-field-value">
            {{ lesson.framework && lesson.framework.name }}
          </span>
        </div>
      </div>
      <!--  测评点 与 测评点映射  -->
      <div>
        <div class="lesson-field-label fit-height" v-if="lesson.measures && lesson.measures.length > 0">
          <div class="lesson-field-label core-measure-field">
            <h1 class="lesson-title-label">
              {{ $t('loc.standards') }}
            </h1>
            <!-- 仅展示核心测评点 switch start -->
            <div class="core-measure" v-show="showCoreMeasureOpen || hasCoreMeasure">
              <span class="lesson-switch">{{ $t('loc.showCoreMeasureOnly') }}</span>
              <el-switch
                @change="changeShowCoreMeasure"
                v-model="showCoreMeasure">
              </el-switch>
            </div>
            <!-- 仅展示核心测评点 switch end -->
          </div>
          <!--          web端-->
          <div style="margin-left: -5px;"
               class="lesson-content-value flex-align-justify add-padding-t-8 hidden-md-and-down">
            <!--增加按钮-->
            <div class="min-width-270">
              <span class="lesson-switch">{{ $t('loc.showStandardDescription') }}</span>
              <el-switch
                v-model="showDomainDescription"
                @change="changeShowDomainDescription"
                unselectable="on" onselectstart="return false;"
                style="-moz-user-select:none;-webkit-user-select:none;">
              </el-switch>
            </div>
            <div v-if="lessonMappedFramework.length === 1 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <span class="lesson-switch">{{
                  getFrameworkMappedTitle(lessonMappedFramework[0])
                }}</span>
              <el-switch
                v-model="mappedFrameworkId"
                :active-value="lessonMappedFramework[0]"
                :inactive-value="defaultNoMappedFrameworkId"
                unselectable="on" onselectstart="return false;"
                style="-moz-user-select:none;-webkit-user-select:none;">
              </el-switch>
            </div>
            <div style="margin-left: auto"
                 v-if="lessonMappedFramework.length === 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0"
                 class="flex-column-center height-36" :style="{'width' : planPreview ? 'min-content' : 'min-content'}">
              <el-radio-group v-model="mappedFrameworkId">
                <el-radio v-for="frameworkId in lessonMappedFramework" :label="frameworkId" :key="frameworkId"
                          @click.native="switchMappedFramework">
                  {{ getFrameworkMappedTitle(frameworkId) }}
                </el-radio>
              </el-radio-group>
            </div>
            <div style="font-weight: normal;"
                 v-if="lessonMappedFramework.length > 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <span style="margin-right: 10px;font-weight: normal">{{
                  $t('loc.lessons2AdminLessonsSettingMappingTitle')
                }}</span>
              <span>
            <el-select v-model="mappedFrameworkId"
                       :title="getFrameworkMappedTitle(mappedFrameworkId)"
                       clearable placeholder="Please select" size="small">
                <el-option
                  v-for="frameworkId in lessonMappedFramework"
                  :key="frameworkId"
                  :label="getFrameworkMappedTitle(frameworkId)"
                  :value="frameworkId">
                </el-option>
            </el-select>
          </span>
            </div>
          </div>
          <!--          ipad端-->
          <div style="" class="display-flex flex-direction-row flex-justify-start hidden-lg-and-up">
            <!--增加按钮-->
            <div style="padding-left: 0px;min-width: 300px;"
                 class="display-flex flex-direction-row justify-content-start">
              <div>
                <span class="lesson-switch">{{ $t('loc.ShowDomainOrMeasureDescription') }}</span>
              </div>
              <div style="margin-left: 4px;">
                <el-switch
                  v-model="showDomainDescription"
                  @change="changeShowDomainDescription"
                  unselectable="on" onselectstart="return false;"
                  style="-moz-user-select:none;-webkit-user-select:none;">
                </el-switch>
              </div>
            </div>
            <div class="display-flex flex-direction-row justify-content-start"
                 v-if="lessonMappedFramework.length === 1 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <div>
                <span class="lesson-switch">{{
                    getFrameworkMappedTitle(lessonMappedFramework[0])
                  }}</span>
              </div>
              <div style="margin-left: 4px;">
                <el-switch
                  v-model="mappedFrameworkId"
                  :active-value="lessonMappedFramework[0]"
                  :inactive-value="defaultNoMappedFrameworkId"
                  unselectable="on" onselectstart="return false;"
                  style="-moz-user-select:none;-webkit-user-select:none;">
                </el-switch>
              </div>
            </div>
            <div v-if="lessonMappedFramework.length === 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0"
                 class="flex-column-center height-36" :style="{'width' : planPreview ? 'min-content' : 'fit-content'}">
              <el-radio-group class="display-flex flex-direction-col" v-model="mappedFrameworkId">
                <el-radio v-for="frameworkId in lessonMappedFramework" :label="frameworkId" :key="frameworkId"
                          @click.native="switchMappedFramework">
                  {{ getFrameworkMappedTitle(frameworkId) }}
                </el-radio>
              </el-radio-group>
            </div>
            <div style="font-weight: normal;"
                 v-if="lessonMappedFramework.length > 2 && lesson.mappedMeasures && lesson.mappedMeasures.length > 0">
              <span style="margin-right: 10px;font-weight: normal">{{
                  $t('loc.lessons2AdminLessonsSettingMappingTitle')
                }}</span>
              <span>
            <el-select v-model="mappedFrameworkId"
                       :title="getFrameworkMappedTitle(mappedFrameworkId)"
                       clearable placeholder="Please select" size="small">
                <el-option
                  v-for="frameworkId in lessonMappedFramework"
                  :key="frameworkId"
                  :label="getFrameworkMappedTitle(frameworkId)"
                  :value="frameworkId">
                </el-option>
            </el-select>
          </span>
            </div>
          </div>
        </div>
        <lesson-mapping-detail v-if="showDomainDescription" :plf-measures-list="plfMeasuresList"
                               :showCoreMeasure="showCoreMeasure" :lesson="lesson"
                               :mapped-framework-id="mappedFrameworkId" :key="mappedFrameworkId"/>
        <lesson-mapping-without-description v-else :plf-measures-list="plfMeasuresList"
                                            :showCoreMeasure="showCoreMeasure" :lesson="lesson"
                                            :mapped-framework-id="mappedFrameworkId ? mappedFrameworkId : defaultNoMappedFrameworkId"
                                            :key="mappedFrameworkId"/>
      </div>
      <!--  课程目标  -->
      <div class="lesson-field-label" v-if="lesson.objectives && lesson.objectives.length">
        <h1 class="lesson-title-label">
          {{ $t('loc.lesson2NewLessonFormLabelObjectives') }}
        </h1>
      </div>
      <div class="lesson-content-value">
        <div v-for="objective in lesson.objectives" class="lesson-field-value">
          <span v-html="getObjective(objective)"></span><br>
        </div>
      </div>
      <!--  课程材料  -->
      <div class="lesson-field-label"
           v-if="shouldDisplayLessonFieldLabel">
        <h1 class="lesson-title-label">
          {{ $t('loc.lesson2NewLessonFormLabelMaterials') }}
        </h1>
      </div>
      <div class="lesson-content-value">
        <el-row>
          <el-col :span="materialMediaUrl ? 17 : 24" style="padding-right: 24px">
            <div v-for="material in lesson.materials && lesson.materials.descriptions">
              <span class="lesson-field-value" v-html="getMaterial(material)"></span>
            </div>
            <div class="lesson-material-attachment-item"
                 v-for="attachment in lesson.materials &&lesson.materials.attachmentMedias">
              <img class="lesson-material-attachment-item-img" :src="fileIcon(attachment)" alt="">
              <div class="lesson-material-attachment-item-title" :title="attachment.sourceFileName">
                <span style="max-width: 240px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{
                    attachment.sourceFileName.substring(0, attachment.sourceFileName.lastIndexOf('.')).trim()
                  }}</span>
                <span>.{{ attachment.sourceFileName.substring(attachment.sourceFileName.lastIndexOf('.') + 1) }}</span>
                <span>({{ formatSize(attachment.size) }})</span>
              </div>
              <a style="margin-left: auto;margin-right: 5px;" @click="downloadMaterialAttachFile(attachment)">
                <el-button icon="el-icon-download" size="medium" type="text">
                  {{ $t('loc.download') }}
                </el-button>
              </a>
            </div>
            <br>
          </el-col>
          <el-col :span="7" v-if="materialMediaUrl" style="float: right">
            <lesson-media-viewer :url="materialMediaUrl"/>
          </el-col>
        </el-row>
      </div>
      <!-- 课程步骤 -->
      <div class="lesson-field-label" v-if="!stepsEmpty">
        <h1 class="lesson-title-label">
          {{ $t('loc.lesson2NewLessonFormLabelSteps') }}
        </h1>
      </div>
      <div class="">
        <div v-for="(step, index) in lesson.steps" :key="index">
          <div
            v-if="!isStepEmpty(step) || !isStepGuidesEmpty(step) || !isTeachingTipsEmpty(step) || universalDesignLearningNotEmpty(step.universalDesignForLearning) || existClrModel(step) || !isQuizEmpty(step)"
               class="display-flex justify-content-between add-padding-b-4 add-margin-t-12"
               style="background: rgb(232, 249, 250); height: 60px;align-items: center">
            <div class="lesson-field-label " style="color: #10B3B7;padding-left: 20px;">
              <span class="font-color-primary">{{ $t('loc.lessons2LessonDetailGuide') }} {{ step.ageGroupName }}</span>
            </div>
            <!--实施步骤资源显示按钮-->
            <div class="position-relative add-margin-r-16 add-margin-t-4"
                 v-if="haveImplStepResource(step)">
              <el-button @click="changeShowImpStepSource(step)" plain>
                <template #icon>
                  <i class="lg-icon"
                     :class="{ 'lg-icon-eye': !step.showImpStepSource, 'lg-icon-eye-off': step.showImpStepSource }"
                     style="margin-right: 5px"></i>
                </template>
                <span v-if="!step.showImpStepSource">{{ $t('loc.unitPlannerShowSources') }}</span>
                <span v-else>{{ $t('loc.unitPlannerHideSources') }}</span>
              </el-button>
              <span class="new-tag">Beta</span>
            </div>
          </div>
          <template v-if="!isStepEmpty(step) || !isStepLessonScienceOfReadingModelEmpty(step)" style="display: flex; flex-direction: row;">
            <el-row
              :class="{'lesson-content-value' : true,'view-hide-imp-step-subscript': step ? !step.showImpStepSource : false}" :gutter="10">
              <el-col class="lesson-field-value ql-editor remove-padding lesson-content-imp-step"
                      v-html="step.content"
                      :xs="24"
                      style="padding-right: 11px !important;"
                      :sm="step.media && step.media.url || step.externalMediaUrl || (haveImplStepResource(step) && step.showImpStepSource) ? 17:24"/>
              <el-col :xs="24" :sm="7" style="display: flex; flex-direction: column;">
                <!-- 如果有图片或音频则显示图片或音频 -->
                <el-col :xs="24" :sm="7" v-if="step.media && step.media.url" style="width: 100%; float: right; margin-bottom: 10px">
                  <lesson-media-viewer :url="step.media && step.media.url"/>
                </el-col>
                <!-- 外部视频时显示外部视频 -->
                <el-col :xs="24" :sm="7" v-else-if="step.externalMediaUrl" style="width: 100%; float: right; margin-bottom: 10px">
                  <lesson-media-viewer :url="step.externalMediaUrl"/>
                </el-col>
                <el-col :xs="24" :sm="7"  v-if="haveImplStepResource(step) && step.showImpStepSource"
                        style="width: 100%; float: right">
                  <ImplementationStepSources
                    :step=step
                    :showImpStepSource=step.showImpStepSource
                    :sourcePreview="true"
                    ref="implementationStepSources"
                  />
                </el-col>
              </el-col>
            </el-row>
            <div class="lg-margin-top-24">
              <!-- 课程模板 -->
              <lesson-template-preview v-if="kToGrade12(step.ageGroupName) && isNotCenter && !isLessonHistoryPreview"
                :lessonTemplate="step.lessonTemplate"
                :lessonId="lesson.id"
                :lessonName="lesson.name"
                :ageGroup="step.ageGroupName"
                :templateType="step.lessonTemplate && step.lessonTemplate.templateType" />
            </div>
            <!-- 幻灯片 -->
            <div class="lg-margin-top-24" v-if="(kToGrade12(step.ageGroupName) || lectureSlidesExpandGrade(step.ageGroupName)) && isNotCenter && !isLessonHistoryPreview">
              <lesson-slides :edit="false" :lessonId="lesson.id" :lessonName="lesson.name" :canEdit="canEditSlides(lesson)" :activityType="lesson.activityType"/>
            </div>
            <!--SOR-->
            <div v-if="showScienceOfReadingModel(step)" class="lg-margin-top-24">
              <!--SOR 预览的标题-->
              <div class="display-flex justify-content-between add-padding-b-4 add-margin-b-12 align-items height-60"
                style="background: rgb(232, 249, 250);">
                <div class="lesson-field-label add-padding-l-20" style="color: var(--color-primary);">
                  <span class="font-color-primary">Science of Reading Activities </span>
                </div>
              </div>
              <!-- SOR 组件预览 -->
              <ScienceOfReading
                ref="scienceOfReading"
                :lessonId="lesson.id"
                :preview="lesson.preview"
                :lessonName="lesson.name"
                :ageGroup="step.ageGroupName"
                :lessonScienceOfReadingModel="step.lessonScienceOfReadingModel"
                :edit="false"></ScienceOfReading>
            </div>
          </template>

          <!-- 毕业生画像 -->
          <template>
            <div v-if="lesson.learnerProfiles && lesson.learnerProfiles.length > 0 && !allLearnerProfilesIsEmpty" class="add-margin-t-36">
              <PortraitGraduateReview ref="portraitGraduateReview" :lesson="lesson"  :lessonItem="lessonItem" :ageGroup="step.ageGroupName"/>
            </div>
          </template>

          <!-- 标准教学指导 -->
          <template>

            <div class="lesson-field-label display-flex align-items justify-content add-margin-b-10 add-margin-t-36" v-if="!isTeachingTipsEmpty(step)">
              <div class="left-gradient title-gradient lg-margin-right-16"></div>
              <span class="font-size-18 font-color-primary">{{ $t('loc.teachingTipsForStandards') }}</span>
              <div class="right-gradient title-gradient lg-margin-left-16"></div>
            </div>

            <teaching-tips-standard :key="'teaching-tips-'+step.ageGroupName"
                                    v-if="!isTeachingTipsEmpty(step)"
                                    :domains="domains"
                                    :isOverview="true"
                                    :frameworkId="lesson.framework.id"
                                    :showCoreMeasure="showCoreMeasure"
                                    v-model="step.teachingTips"
                                    ref="teachingTipsStandard"/>
            <template v-if="!isStepGuidesEmpty(step)">
                <div class="lesson-field-label display-flex align-items justify-content add-margin-t-32 add-margin-b-10 position-relative">
                    <div class="left-gradient title-gradient lg-margin-right-16"></div>
                    <span class="font-size-18 font-color-primary">{{ $t('loc.typicalBehaviors') }}</span>
                    <div class="right-gradient title-gradient lg-margin-left-16"></div>
                    <typical-behaviors-tab v-if="isMappedFramework"
                                        :frameworkId="() => lesson.framework.id"
                                        v-model="typicalBehaviorsType"></typical-behaviors-tab>
                </div>
                <DomainsTableOverview class="add-margin-b-24" v-if="!isStepGuidesEmpty(step)"
                :domains="domains" :frameworkId="lesson.framework.id"
                :unitAddressDomains="filterCoreMeasure(step.lessonStepGuides).filter(item => item.typicalBehaviors || item.examples)"
                :allMeasureUnMapped="allMeasureUnMapped(step.lessonStepGuides)"
                :isMapped="typicalBehaviorsType == 'PS'"
                ></DomainsTableOverview>
            </template>
            <!--如果是非改编，那么必须含有 generally 的数据-->
            <!--如果是改编的，那么如果 group 不为空，那么 group 必须存在，如果 group 为空，则必须要有 generally 的数据-->
            <div v-show="showUniversalDesignLearning(step)">
              <div class="lesson-field-label add-margin-t-10 title-difference">
                <div class="display-flex align-items justify-content">
                  <div class="left-gradient title-gradient lg-margin-right-16"></div>
                  <span class="font-size-18 font-color-primary">{{ $t('loc.unitPlannerLessonUDL') }}</span>
                  <div class="right-gradient title-gradient lg-margin-left-16"></div>
                </div>
              </div>
              <UniversalDesignLearning :universalDesignForLearning="step.universalDesignForLearning"
                                       :universalDesignForLearningClassSpecial="step.universalDesignForLearningGroup ? step.universalDesignForLearningGroup: ''"
                                       :mixedAgeDifferentiation="step.mixedAgeDifferentiations"
                                       :showMixedAge="step.showMixedAge"
                                       :lessonId="lesson.id"
                                       :lessonStepIndex="index"
                                       :lessonAgeGroup="step.ageGroupName"
                                       :isNotEdit="true"
                                       :isWeeklyPlanEdit="isWeeklyPlanEdit()"
                                       :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                                       ref="universalDesignLearning"
                                       :selectedGroupId="selectedGroupId"></UniversalDesignLearning>
            </div>
          </template>
          <!-- 文化教学 -->
          <template v-if="existClrModel(step)">
<!--            <div class="lesson-field-label add-margin-t-10 title-difference">-->
<!--                <div class="display-flex align-items justify-content">-->
<!--                  <div class="left-gradient title-gradient lg-margin-right-16"></div>-->
<!--                  <span class="font-size-18 font-color-primary">Culturally and Linguistically Responsive Practice</span>-->
<!--                  <div class="right-gradient title-gradient lg-margin-left-16"></div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="lesson-field-value ql-editor remove-padding content-difference" v-html="formatContetWrap(step.culturallyResponsiveInstruction)"></div>-->

            <CulturallyLinguisticallyResponsive :ageGroupName="step.ageGroupName"
                                                :lessonId="lesson.id"
                                                :isEdit="false"
                                                :step="step"
                                                class="culturally-header"
                                                :isWeeklyPlanEdit="isWeeklyPlanEdit()"
                                                :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                                                ref="culturallyLinguisticallyResponsive"
                                                :culturallyResponsiveInstruction="step.culturallyResponsiveInstruction"
                                                :culturallyResponsiveInstructionGroup="step.culturallyResponsiveInstructionGroup"/>
          </template>
          <LessonQuiz v-model="lesson.steps[index].questions" :lessonId="lesson.id" :showDownload="!isLessonHistoryPreview" :ageGroupName="step.ageGroupName" :domains="domains" v-if="!isQuizEmpty(step)" :edit="false" />
        </div>
      </div>
      <dll-detail :lesson="lesson" v-if="dllOpen"/>
      <!-- 课程分享 -->
      <lesson-share :lesson="lesson" v-if="hasShareResource"/>
    </div>
    <!-- 课程详情中映射的测评点的解释 -->
    <MappedMeasureExplanation :lesson="lesson" :mapped-framework-id="mappedFrameworkId"
                              v-if="lesson.mappedMeasures && mappedFrameworkId"/>
  </div>
</template>

<script>
import 'quill/dist/quill.snow.css'
import constants from '../../../../../utils/constants'
import { NoActivityTheme } from '../../../../../utils/constants'
import LessonMediaViewer from './LessonMediaViewer'
import lessonShare from './LessonShare'
import LessonMappingDetail from '@/views/modules/lesson2/lessonLibrary/components/LessonMappingDetail'
import LessonMappingWithoutDescription
  from '@/views/modules/lesson2/lessonLibrary/components/LessonMappingWithoutDescription'
import MappedMeasureExplanation from './MappedMeasureExplanation'
import {mapState} from 'vuex'
import {MappedStateFramework} from '@/utils/constants'
import Api from '@/api/lessons2/index'
import DllDetail from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/index'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
import AppUtil from '../../../../../utils/app'
import tools from '@/utils/tools'
import DomainsTableOverview from '@/views/modules/lesson2/lessonLibrary/editor/DomainsTableOverview'
import CulturallyLinguisticallyResponsive
  from '@/views/modules/lesson2/lessonLibrary/editor/CulturallyLinguisticallyResponsive.vue'
import UniversalDesignLearning from '@/views/modules/lesson2/lessonLibrary/editor/UniversalDesignLearning.vue'
import LessonQuiz from '@/views/modules/lesson2/lessonLibrary/editor/LessonQuiz.vue'
import ImplementationStepSources
  from '@/views/modules/lesson2/unitPlanner/components/editor/ImplementationStepSources.vue'
import TeachingTipsStandard from '@/views/modules/lesson2/lessonLibrary/editor/TeachingTipsStandard'
import TypicalBehaviorsTab from '@/views/modules/lesson2/lessonLibrary/editor/TypicalBehaviorsTab.vue'
import LessonTemplatePreview from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplatePreview'
import { isBloomQuiz } from '@/utils/common'
import { parseStreamData } from '@/utils/eventSource'
import frameworkUtils from '@/utils/frameworkUtils'
import LessonSlides from '@/views/modules/lesson2/unitPlanner/components/lessonSlides/index.vue'
import { equalsIgnoreCase } from '../../../../../utils/common'
import PortraitGraduateReview from '../../unitPlanner/components/editor/PortraitGraduateReview.vue'
import ScienceOfReading from '@/views/modules/lesson2/lessonLibrary/editor/ScienceOfReading'
import lessonUtils from '@/utils/lessonUtils'
export default {
  name: 'LessonInfoNew',
  components: {
    ImplementationStepSources,
    UniversalDesignLearning,
    CulturallyLinguisticallyResponsive,
    LessonMediaViewer,
    ScienceOfReading,
    lessonShare,
    LessonMappingDetail,
    LessonMappingWithoutDescription,
    MappedMeasureExplanation,
    DllDetail,
    DomainsTableOverview,
    LessonQuiz,
    TeachingTipsStandard,
    TypicalBehaviorsTab,
    LessonTemplatePreview,
    LessonSlides,
    PortraitGraduateReview
  },
  props: {
    lesson: {
      type: Object,
      default: function () {
        return null
      }
    },
    selectedGroupId: {
      type: String,
      default: function () {
        return ''
      }
    },
    planPreview: {
      type: Boolean
    },
    domains: {
      type: Array,
      default: function () {
        return []
      }
    },
    lessonItem: {
      type: Object,
      default: () => {
      }
    },
    scrollToGraduate: {
      type: Boolean,
      default: false
    },
    // 是否是课程历史预览
    isLessonHistoryPreview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showOtherMeasures: !!this.lesson.mappedMeasures,
      mappedFrameworkId: 0,
      defaultNoMappedFrameworkId: 0, // 如果单选没有选中的时候，多选没有选中的时候的 默认 mappedFrameworkId
      recordMappedFrameworkId: '', // 记录当前映射的框架
      plfMeasuresList: [],
      showDomainDescription: false, // 是否打开了展示按钮详细描述
      showCoreMeasure: false, // 是否打开仅展示核心测评点 默认为true
      fileIcons: {doc, docx, pdf, ppt, pptx, xls, xlsx, file},
      showCoreMeasureOpen: this.lesson.showCoreMeasureOpen, // 是否展示仅展示核心测评点开关
      typicalBehaviorsType: 'PS', // 典型行为类型
      isMappedFramework: false, // 是否是CAPTKLF框架课程
      mapFrameworkData: [] // 映射框架数据
    }
  },
  computed: {
    // 是否显示 SOR 资源
    showScienceOfReadingModel () {
      return function (step) {
        // 如果是小于 Grade 2 的年龄段，且显示 Science of Reading 模型，或者是预览状态且有 Science of Reading 内容，则返回 true，否则返回 false
        return this.ltGrade2(step.ageGroupName) && (step.showScienceOfReadingModel || (
          this.lesson.preview && this.hasScienceOfReadingContent(step.lessonScienceOfReadingModel)
        )) && !this.unitDetail && this.isNotCenter && !this.isCG
      }
    },
    // 判断是否是 K12 年龄段
    kToGrade12 () {
      return function (age) {
        return ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group'].includes(age)
      }
    },
    lectureSlidesExpandGrade () {
      return function (age) {
        return ['TK (4-5)'].includes(age)
      }
    },
    // 小于 Grade 2 的年龄
    ltGrade2 () {
      return function (ageGroupName) {
        let k12Grades = ['PS/PK (3-4)', 'TK (4-5)', 'K (5-6)', 'Grade 1', 'Grade 2']
        return tools.isInCurrentAgeGroup(ageGroupName, k12Grades)
      }
    },
    isNotCenter () {
      return this.lesson.subclassCenter !== 'CENTER' && this.lesson.subclassCenter !== 'STATION'
    },
    // 是否有 Science of Reading 内容
    hasScienceOfReadingContent () {
      return function (lessonScienceOfReadingModel) {
        // 如果 lessonScienceOfReadingModel 不存在，返回 false
        if (!lessonScienceOfReadingModel) {
          return false
        }
        // 如果 activityTitle 有值并且不是空串，
        // 或者 measureIds 不为 null，是一个数组，并且数组长度大于 0，
        // 或者 content 有值并且不是空串，则返回 true，否则返回 false
        return (lessonScienceOfReadingModel.activityTitle && lessonScienceOfReadingModel.activityTitle !== '') ||
          (lessonScienceOfReadingModel.measureIds && lessonScienceOfReadingModel.measureIds.length > 0) ||
          (lessonScienceOfReadingModel.content && lessonScienceOfReadingModel.content !== '')
      }
    },
    // unit 详情改编课程时显示操作栏
    unitDetail() {
      return equalsIgnoreCase(this.$route.name, 'unitDetail')
    },
    // 是否显示框架描述
    shouldDisplayLessonFieldLabel() {
      return (this.lesson.materials && this.lesson.materials.descriptions && this.lesson.materials.descriptions[0])
        || this.materialMediaUrl
        || (this.lesson.materials && this.lesson.materials.attachmentMedias && this.lesson.materials.attachmentMedias[0] && this.lesson.materials.attachmentMedias[0].sourceFileName);
    },
    showUniversalDesignLearning() {
      // 如果是非改编，那么必须含有 generally 的数据
      // 如果是改编的，那么如果 group 不为空，那么 group 必须存在，如果 group 为空，则必顶要有 generally 的数据
      return function (step) {
        return (!this.lesson.isAdaptedLesson && this.universalDesignLearningNotEmpty(step.universalDesignForLearning)) ||
          (this.lesson.isAdaptedLesson && step.universalDesignForLearningGroup && step.universalDesignForLearningGroup !== '' &&
            this.universalDesignLearningGroupNotEmpty(step.universalDesignForLearningGroup)) ||
          (this.lesson.isAdaptedLesson && (!step.universalDesignForLearningGroup || step.universalDesignForLearningGroup === '') &&
            this.universalDesignLearningNotEmpty(step.universalDesignForLearning)) ||
          (this.lesson.isAdaptedLesson && step.mixedAgeDifferentiations && step.mixedAgeDifferentiations !== '')
      }
    },
    coverURL () {
      // 课程默认封面
      if (this.lesson.coverMedias.length === 0 && (!this.lesson.coverExternalMediaUrl || this.lesson.coverExternalMediaUrl.length === 0)) {
        return constants.lessonDefaultCoverURL
      }
      // 如果有课程封面，展示课程封面
      if (this.lesson.coverMedias.length !== 0) {
        let medias = this.lesson && this.lesson.coverMedias || []
        let media = medias[medias.length - 1]
        return media && media.url || media.externalMediaUrl // 如果有媒体的路径就用媒体的，没有媒体的路径就用外部链接，外部链接用于预览的时候显示
      } else {
        // 如果没有课程封面，展示外部媒体视频
        return this.lesson && this.lesson.coverExternalMediaUrl
      }
    },
    coverSource () {
      if (this.lesson.coverMedias.length !== 0) {
        let medias = this.lesson && this.lesson.coverMedias
        let media = medias[0]
        return media && media.source
      }
      return null
    },
    // 内容换行符替换
    formatContetWrap() {
      return function (content) {
        if (!content) {
          return ''
        }
        return content.replace(/\n/g, '<br/>')
      }
    },
    materialMediaUrl() {
      return this.lesson.materials && this.lesson.materials.media && this.lesson.materials.media.url || this.lesson.materials && this.lesson.materials.externalMediaUrl
    },
    hasShareResource() {
      let {books, videoBooks, attachmentMedias, steps} = this.lesson || {}
      let hasHomeActivity = false
      // 遍历步骤
      if (steps && steps.length > 0) {
        steps.forEach(step => {
          if (step.homeActivity && step.homeActivity.length > 0) {
            hasHomeActivity = true
          }
        })
      }
      return books && books.length || videoBooks && videoBooks.length || attachmentMedias && attachmentMedias.length || hasHomeActivity
    },
    stepsEmpty() {
      return !this.lesson.steps || // 没有步骤
        !this.lesson.steps.find(step => !this.isStepEmpty(step) || !this.isStepGuidesEmpty(step) || !this.isStepLessonScienceOfReadingModelEmpty(step)) // 有步骤，步骤是空的 或者 有步骤，步骤没有例子或典型行为
    },
    filterMappedFramework() {
      return function (lessonMappedFrameworkList, ages) {
        return lessonMappedFrameworkList.filter(frameworkId => {
          // 从映射中获取框架
          const curFramework = MappedStateFramework.find(item => item.id === frameworkId)
          // 如果框架存在，判断框架是否包含年龄组
          if (curFramework) {
            const grades = curFramework.grades
            // grades 是一个 Map，如果为空则返回 true，如果不为空，则需要进一步处理
            if (grades.size === 0) {
              return true
            }
            return ages.some(age => grades.get(age) !== undefined)
          }
          // 如果框架不存在，返回 false
          return false
        })
      }
    },
    ...mapState({
      isMC: state => state.curriculum.isMC, // 是否是 Magic Curriculum 平台
      lessonMappedFramework: function (state) {
        let lessonMappedFrameworkList = []
        // 进行判空处理，如果没有值，就不进行切割
        if (state.common.open) {
          lessonMappedFrameworkList = state.common.open.lessonMappedFramework.split(',')
        }
        lessonMappedFrameworkList = lessonMappedFrameworkList.filter(frameworkId => {
          return frameworkId !== ''
        })
        // 过滤掉不符合年龄组的框架
        lessonMappedFrameworkList = this.filterMappedFramework(lessonMappedFrameworkList, this.lesson.ages)
        let frameworkId = sessionStorage.getItem('mappedFrameworkId')
        // 如果这个课程选择的框架是用户选择开放的映射框架，才修改 mappedFrameworkId
        if (this.lesson.mappedMeasures && this.lesson.mappedMeasures.length > 0 && lessonMappedFrameworkList.length > 0) {
          if (frameworkId && lessonMappedFrameworkList.findIndex(item => item === frameworkId) !== -1) {
            this.mappedFrameworkId = frameworkId
          } else {
            this.mappedFrameworkId = lessonMappedFrameworkList && lessonMappedFrameworkList[0]
          }
        }
        return lessonMappedFrameworkList
      },
      open: state => state.common.open,
      isCG: state => state.curriculum.isCG, // 是否是 Curriculum Genie
      currentUser: (state) => state.user.currentUser, // 当前用户
      showImpStepSourceMap: state => state.lesson.showImpStepSourceMap, // 下载时是否显示实施步骤资源列表
      frameworkAbbreviation: state => state.lesson.frameworkAbbreviation, // 框架缩写
      lgIsMobile: state => state.common.lgIsMobile, // 添加移动端状态
      currentPluginUser: state => state.cgAuth.user, // 当前插件用户
      unitBaseInfo: state => state.unit.unitBaseInfo // 单元作者 ID
    }),
    // UDL 开关
    adaptUDLAndCLROpen() {
      return this.open && this.open.adaptUDLAndCLROpen
    },
    dllOpen () {
      return this.open && this.open.dllopen
    },
    hasCoreMeasure() {
      let hasCoreMeasure = false
      if (!this.domains) {
        return hasCoreMeasure
      }
      this.domains.forEach(domain => {
        domain.children.forEach(measure => {
          if (measure.core) {
            hasCoreMeasure = true
          }
        })
      })
      return hasCoreMeasure
    },
    // 判断所有校训数据是否为空
    allLearnerProfilesIsEmpty() {
      if (!this.lesson.learnerProfiles) {
        return true
      }
      let hasSubRubrics = this.lesson.learnerProfiles.some(profile => profile.subRubrics && profile.subRubrics.length > 0)
      let rubricsValues = []
      if (hasSubRubrics) {
        rubricsValues = this.lesson.learnerProfiles.map(profile => profile.subRubrics.map(subRubric => subRubric.rubricsValue)).flat()
      } else {
        rubricsValues = this.lesson.learnerProfiles.map(profile => profile.rubricsValue).flat()
      }
      return rubricsValues.every(value => !(!value ? '' : value.trim()));
    },
    showFromUnit() {
      return this.$route.path.startsWith('/lessons') && !(this.unitBaseInfo && this.unitBaseInfo.exemplar) && this.lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM' && this.lesson.unitId && !this.isLessonHistoryPreview
    },
    /**
      * 是否显示 Classroom Type 部分
      */
    showClassroomType () {
      return this.lesson && lessonUtils.showClassroomType(this.lesson.ages, this.lesson.activityType) && !this.lesson.adaptedModuleSwitch
    },
  },
  watch: {
    mappedFrameworkId: {
      immediate: true,
      handler: function (val) {
        this.$emit('getMappedFrameworkId', this.mappedFrameworkId)
        // 将数据保存进入 vuex
        this.$store.dispatch('setLessonInfoMappedFramework', val)
      }
    },
    'lesson.framework.id': {
      immediate: true,
      handler: function (val) {
        Api.getMeasures(val).then(res => {
          this.domains = res.measures || []
        })
      }
    }
  },
  methods: {
    // 判断是否是自己的课程
    canEditSlides (lesson) {
      // 自己和 agency_owner 或者 agency_admin 可以编辑 slides（范例除外）
      return (['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase()) || equalsIgnoreCase(lesson.createUserId, this.currentUser.user_id))
       && !(this.unitBaseInfo && this.unitBaseInfo.exemplar) && this.lesson.type !== 'CURRICULUM-PLUGIN_SYSTEM'
    },
    // 获取没有主题的活动类型
    getNoActivityTheme () {
      return NoActivityTheme
    },
    // 修改 PS/PK 的年龄组
    formatAge (age) {
      if (this.$route.name == 'curriculumUnitDetail' && age && equalsIgnoreCase(age, 'PS/PK (3-4)')) {
        return 'Preschool (3-5)'
      } else {
        return age
      }
    },
    // 初始化实施步骤资源是否显示
    initStepShowImpStepSource () {
      if (this.lesson.steps && this.lesson.steps.length > 0) {
        const showImpStepSourceMap = {}
        this.lesson.steps.forEach(step => {
          if (step.lessonImpStepAndSource && step.lessonImpStepAndSource.sources && step.lessonImpStepAndSource.sources.length > 0) {
            this.$set(step, 'showImpStepSource', true)
            showImpStepSourceMap[step.ageGroupValue] = true
          } else {
            this.$set(step, 'showImpStepSource', false)
            showImpStepSourceMap[step.ageGroupValue] = false
          }
        })

        // 存入全局变量下载时使用
        this.$store.dispatch('setShowImpStepSourceMap', showImpStepSourceMap)
      }
    },
    // 改变是否可以显示实施步骤资源
    changeShowImpStepSource (step) {
      this.$set(step, 'showImpStepSource', !step.showImpStepSource)
      // 更新当前 step 的 showImpStepSource 状态
      this.showImpStepSourceMap[step.ageGroupValue] = step.showImpStepSource
      // 存入全局变量下载时使用
      this.$store.dispatch('setShowImpStepSourceMap', this.showImpStepSourceMap)
      // 如果当前是要隐藏资源，则同时关闭引导弹窗
      if (!this.showImpStepSource) {
        // 调用 API 隐藏引导
        // 如果 ImplementationStepSources 组件引用存在，直接设置 showGuidePopup 为 false
        if (this.$refs.implementationStepSources) {
          this.$refs.implementationStepSources.showGuidePopup = false
        }
      }
    },
    isBloomQuiz,
    // 判断是不是在周计划编辑页面
    isWeeklyPlanEdit() {
      return !!this.$route.path && this.$route.path.indexOf('weekly-lesson-planning') > -1
    },
    switchMappedFramework() {
      if (this.mappedFrameworkId === this.recordMappedFrameworkId) {
        this.mappedFrameworkId = this.defaultNoMappedFrameworkId
      }
      this.recordMappedFrameworkId = this.mappedFrameworkId
    },
    // 每当按钮的值发生变化的时候，将按钮的值存入缓存中， 如果缓存中存在数据的时候，将缓存中的数据放入到 showDomainDescription中
    changeShowDomainDescription(value) {
      // 缓存键
      const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
      // 将值放入缓存中
      localStorage.setItem(cacheKey, value)
    },
    isStepEmpty(step) {
      return !step ||
        !step.content &&
        (!step.media || !step.media.url) &&
        !step.externalMediaUrl // !step.externalMediaUrl 只有链接时不返回空
    },
    isStepGuidesEmpty(step) {
      return !step || !step.lessonStepGuides ||
        !step.lessonStepGuides.some(item => item.examples || item.typicalBehaviors)
    },
    isStepLessonScienceOfReadingModelEmpty (step) {
      return !step || !step.lessonScienceOfReadingModel
    },
    // 判断是否有实施步骤资源 -- 预览时资源显示判断隐藏资源也算无资源
    haveImplStepResource (step) {
      return step && step.lessonImpStepAndSource && step.lessonImpStepAndSource.sources && step.lessonImpStepAndSource.sources.filter(source => !source.hidden).length > 0
    },
    // 判断标准教学指导是否为空
    isTeachingTipsEmpty (step) {
      return !step || !step.teachingTips || !step.teachingTips.some(item => item.teachingTips && item.teachingTips.trim() !== "");
    },
    // 判断 quiz 内容是否为空
    isQuizEmpty(step) {
      let quizIsEmpty = true
      // 判断 step 中是否有 quiz
      if (step && step.questions && step.questions.length > 0) {
        let quizQuestionHaveData = false
        // 判断 quiz 中的 question 是否有问题和答案
        for (let i = 0; i < step.questions.length; i++) {
          const question = step.questions[i]
          if ((question.question && question.question.trim() !== '') || (question.answer && question.answer.trim() !== '')) {
            // 如果有问题或者答案，那么则应该展示 quiz
            quizQuestionHaveData = true
            break
          }
        }
        quizIsEmpty = !quizQuestionHaveData
      }
      // 返回 quiz 是否为空
      return quizIsEmpty
    },
    // 判断格式为视频还是书
    isImg(url) {
      let reg = /.+(\.jpeg|\.png|\.jpg)/i
      return reg.test(url)
    },
    getMaterial(material) {
      if (tools.isComeFromIPad()) {
        // APP H5页面跳转浏览器
        $('a.textarea-item-a, a.lesson-material-card-link').on('click', function ($this) {
          AppUtil.appOpenUrlByBrowser($(this).attr('href'))
          return false
        })
      }
      // H5页面渲染
      return material.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ') + ' </a>'.replace('href')
    },
    getObjective(objective) {
      return objective.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ')
    },
    getFrameworkMappedTitle(frameworkId) {
      let framework = MappedStateFramework.find(item => item.id === frameworkId)
      if (!framework) {
        return ''
      }
      return this.lessonMappedFramework.length > 2 ? framework.name : `${framework.name}`
    },
    fileIcon(attachment) {
      let name = attachment.sourceFileName
      let extension = name && name.substring(name.lastIndexOf('.') + 1)
      return this.fileIcons[extension] || file
    },
    formatSize(size) {
      let value = size
      let suffix = 'B'
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'KB'
      }
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'MB'
      }
      return `${value}${suffix}`
    },
    // 下载课程材料附件
    downloadMaterialAttachFile(file) {
      if (tools.isComeFromIPad()) {
        var fileSize = tools.calFilesize(file.size)
        let requestData = {
          'emailTemplate': 'lesson_library_material_attach',
          'downloadFileUrl': file.url,
          'fileName': file.sourceFileName,
          'fileSize': fileSize,
          'courseName': this.lesson.name
        }
        this.$axios.post($api.urls().sendFileDownloadEmail, requestData)
          .then(() => {
            this.$confirm($i18n.t('loc.fileLinkSendToEmail'), $i18n.t('loc.fileDownloadTip'), {
              confirmButtonText: $i18n.t('loc.fileDownloadConfirm'),
              showCancelButton: false
            })
          }).catch(error => {
          this.$message.error(error.message)
        })
      } else {
        window.open(file.url, '_blank')
      }
    },
    changeShowCoreMeasure() {
      // 缓存键
      let cacheKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
      localStorage.setItem(cacheKey, this.showCoreMeasure)
      this.$bus.$emit('onlyShowMeasure', this.showCoreMeasure)
      if (this.showCoreMeasure) {
        this.$analytics.sendEvent('web_lesson_open_core_measure')
      } else {
        this.$analytics.sendEvent('web_lesson_close_core_measure')
      }
    },
    filterCoreMeasure(lessonStepGuides) {
      if (this.showCoreMeasure) {
        lessonStepGuides = lessonStepGuides.filter(item => item.core)
      }
      // 按类型过滤典型行为
      if (this.typicalBehaviorsType === 'PS') {
        lessonStepGuides = lessonStepGuides.filter(item => item.mapped)
      } else {
        lessonStepGuides = lessonStepGuides.filter(item => !item.mapped)
      }
      return lessonStepGuides
    },
    // 是否全部测评点无映射
    allMeasureUnMapped (lessonStepGuides) {
      // 当前实施步骤的 optklf 测评点
      let ptklfMeasureIds = lessonStepGuides && lessonStepGuides.filter(item => !item.mapped || item.typicalBehaviors || item.examples).map(item => item.measureId)
      // 全部可以映射 psc 框架的 ptklf 测评点
      let canMappedPtklfMeasureIds = []
      //
      this.mapFrameworkData.forEach(measure => {
        Object.keys(measure.mappedMeasures).forEach(key => {
          let measureIds = measure.mappedMeasures[key] && measure.mappedMeasures[key].map(x => x.id)
          canMappedPtklfMeasureIds = canMappedPtklfMeasureIds.concat(measureIds)
        })
      })
      // 未匹配的测评点
      return ptklfMeasureIds.every(item => !canMappedPtklfMeasureIds.includes(item))
    },

    // 获取框架测评点映射
    getFrameworkMeasureMap () {
      this.$store.dispatch('getMapFrameworkData', this.lesson.framework.id).then(res => {
        this.mapFrameworkData = res
      })
    },

    /**
     * UniversalDesignForLearningGroup 是否有数据
     * @param data
     */
    universalDesignLearningGroupNotEmpty(data) {
      // 如果数据为空，直接返回 false
      if (data === null || data === undefined || data === '') {
        return false
      }
      try {
        // 解析数据
        const parse = JSON.parse(data)
        // 如果解析后的数据长度大于 0，返回 true
        return parse && parse.length > 0
      } catch (e) {
        // 如果解析失败，返回 false
        return false
      }
    },
    // 判断 UniversalDesign 模块数据是否为空
    universalDesignLearningNotEmpty (data) {
      if (data === null || data === undefined || data === '') {
        return
      }
      data = data.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '')
      // 根据年龄段解析模型不同
      let universalDesignForLearning = parseStreamData(data, [
          { key: 'learnersWithIEP', name: 'For Learners with IEPs' },
          { key: 'englishLanguageLearners', name: ['For Dual Language Learners', 'For English Language Learners'] }
        ])[0]
      let learnersWithIEP = ''
      if (universalDesignForLearning.learnersWithIEP !== undefined && universalDesignForLearning.learnersWithIEP !== '') {
        learnersWithIEP = parseStreamData(universalDesignForLearning.learnersWithIEP, [
          { key: 'activityDescription', name: 'Activity Description' },
          { key: 'support', name: 'Support' }
        ])[0]
        if (learnersWithIEP.activityDescription !== undefined && learnersWithIEP.activityDescription !== '') {
          learnersWithIEP.activityDescription = learnersWithIEP.activityDescription.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()}
        if (learnersWithIEP.support !== undefined && learnersWithIEP.support !== '') {
          learnersWithIEP.support = learnersWithIEP.support.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()
        }
      }

      let englishLanguageLearners = ''
      if (universalDesignForLearning.englishLanguageLearners !== undefined && universalDesignForLearning.englishLanguageLearners !== '') {
        englishLanguageLearners = parseStreamData(universalDesignForLearning.englishLanguageLearners, [
          { key: 'activityDescription', name: 'Activity Description' },
          { key: 'support', name: 'Support' }
        ])[0]
        if (englishLanguageLearners.activityDescription !== undefined && englishLanguageLearners.activityDescription !== '') {
          englishLanguageLearners.activityDescription = englishLanguageLearners.activityDescription.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()
        }
        if (englishLanguageLearners.support !== undefined && englishLanguageLearners.support !== '') {
          englishLanguageLearners.support = englishLanguageLearners.support.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()
        }
      }

      if ((learnersWithIEP.activityDescription && learnersWithIEP.support) || (englishLanguageLearners.activityDescription && englishLanguageLearners.support)) {
        return true
      }
      return false
    },

    // 预览时判断 clr 是否有数据，从而展示头部归属哪个年龄
    existClrModel (step) {
      return !!step.culturallyResponsiveInstruction || !!step.culturallyResponsiveInstructionGroup || (step.lessonClrAndSources && step.lessonClrAndSources.clr)
    },

    // 滚动到毕业素养
    scrollToGraduateReview () {
      if (this.scrollToGraduate && this.$refs.portraitGraduateReview) {
        this.$nextTick(() => {
          this.$refs.portraitGraduateReview[0].$el.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        })
      }
    },
    /**
     * 跳转到 Unit 详情页
     */
    goToUnitDetail() {
      if (this.lesson && this.lesson.unitId) {
        this.$router.push({
          name: 'load-unit-cg',
          params: {
            unitId: this.lesson.unitId,
            completeToOverview: true,
            goToOverview: true
          }
        })
      }
    },
    // quiz 资源状态同步
    quizHasResource () {
      if (!this.lesson || !this.lesson.steps || this.lesson.steps.length === 0) {
        this.$store.dispatch('setHasQuizResource', false)
        return
      }
      if (this.lesson.steps[0].questions && this.lesson.steps[0].questions.length > 0) {
        this.$store.dispatch('setHasQuizResource', true)
      } else {
        this.$store.dispatch('setHasQuizResource', false)
      }
    },
    // 获取课堂类型标签文本
    getClassroomTypeLabel (classroomType) {
      if (!classroomType) return this.$t('loc.classroomTypeInPerson')
      
      switch (classroomType.toUpperCase()) {
        case 'IN_PERSON':
          return this.$t('loc.classroomTypeInPerson')
        case 'VIRTUAL':
          return this.$t('loc.classroomTypeVirtual')
        default:
          return classroomType
      }
    }

  },
  created() {
    Api.getMeasures(constants.caPlfFrameworkId).then(res => {
      this.plfMeasuresList = res.measures || []
    })
    // 同步 quiz 资源状态
    this.quizHasResource()
    // 初始化 step 中 控制参数  showImpStepSource
    this.initStepShowImpStepSource()
    // 获取框架测评点映射
    this.getFrameworkMeasureMap()
  },

  mounted () {
    // 开始阶段为 SHOW_DOMAIN_DESCRIPTION 赋值
    const cacheKey = 'SHOW_DOMAIN_DESCRIPTION_' + this.currentUser.user_id
    if (localStorage.getItem(cacheKey)) {
      this.showDomainDescription = JSON.parse(localStorage.getItem(cacheKey))
    } else {
      this.showDomainDescription = false
    }
    if (this.lesson.showCoreMeasureOpen) {
      // 开始阶段为 SHOW_CORE_MEASURE 赋值
      const cacheShowCoreKey = 'SHOW_CORE_MEASURE_' + this.currentUser.user_id
      if (localStorage.getItem(cacheShowCoreKey)) {
        this.showCoreMeasure = JSON.parse(localStorage.getItem(cacheShowCoreKey))
      } else {
        this.showCoreMeasure = this.lesson.showCoreMeasureOpen
      }
    } else {
      this.showCoreMeasure = false
    }
    // 判断是否是 CAPTKLF 框架
    this.isMappedFramework = frameworkUtils.isCAPTKLF(this.lesson.framework.id) || frameworkUtils.isILEARN(this.lesson.framework.id) || frameworkUtils.isMELS(this.lesson.framework.id)
    // 如果是 CAPTKLF 框架，那么默认显示 PS 框架
    if (this.isMappedFramework) {
      this.typicalBehaviorsType = 'PS'
      this.$store.dispatch('setShowMappedTypicalBehaviors', true)
    } else {
      this.typicalBehaviorsType = 'CA-PTKLF'
      this.$store.dispatch('setShowMappedTypicalBehaviors', false)
    }

    // 控制 UniversalDesign 模块是否展示
    this.$nextTick(() => {
      if (this.$refs.universalDesignLearning) {
        // 判断 UniversalDesignLearning 是否为空
        this.showUniversalDesign = this.$refs.universalDesignLearning[0].generalLearningIepIsEmpty()
        // 判断是不是在周计划编辑页面
        if (this.isWeeklyPlanEdit()) {
          this.showUniversalDesign = false
        }
        // 如果 UniversalDesignLearning 高度小于20px 则 showUniversalDesign 为 false
        if (this.$refs.universalDesignLearning[0].$el.clientHeight < 20) {
          this.showUniversalDesign = true
        }
      }
    })

    // 初始化 step 中 控制参数  showImpStepSource
    this.initStepShowImpStepSource()

    // 滚动到毕业素养
    this.scrollToGraduateReview()
  }
}
</script>
<style scoped lang="less">
@media screen and (max-width:768px) {
  .lesson-field-time {
    .lesson-field-time-item {
      margin-top: -7px;
    }
  }
  .lesson-author-date-row, .lesson-from-unit-tag {
      flex-direction: row !important;
  }

  .split-line {
    margin: 10px auto 10px !important;
  }
}

@media screen and (min-width:768px) and (max-width:1199px)  {
  .before-item {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      display: block;
      width: 1px;
      top: 15px;
      bottom: 15px;
      left: 0;
      margin-left: -0.5px;
      background: #DCDFE6;
    }
  }

  .lesson-field-time {
    margin: 0px 40px;
    background-color: #F5F6F8;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;

    .lesson-field-time-item {
      display: flex;
      flex-direction: column-reverse;
      padding: 30px 0px;
      gap: 0px;
      justify-content: center;
      align-items: center;
      vertical-align: middle;
      margin-top: -20px;
      position: relative;

      .lesson-field-value, .lesson-field-label {
        height: 20px;
        line-height: 20px;
      }

      .lesson-field-value {
        margin-left: 5px;
      }
    }
  }
}

@media screen and (min-width: 1200px) {
  .before-item {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      display: block;
      width: 1px;
      top: 15px;
      bottom: 15px;
      left: 0;
      margin-left: -0.5px;
      background: #DCDFE6;
    }
  }
}

.lesson-name {
  text-align: center;
  width: 100%;
  font-size: 26px;
  color: #333;
  font-weight: bold;

  .lesson-name-content {
    display: flex;
    flex-direction: column; // 改为垂直布局
    justify-content: center;
    align-items: center;
    gap: 10px; // 标签与文本之间的间距
    min-height: 60px; // 确保有足够高度显示内容
  }

  .lesson-name-text {
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
    line-height: 1.2; // 改善行高
  }
}

/* 分割线 */
.split-line {
  width: 20%;
  height: 3px;
  background: rgba(16, 179, 183, 1);
  margin: 30px auto 30px;
}

/* 描述列表标签 */
.lesson-field-label {
  font-size: 16px;
  line-height: 34px;
  height: 34px;
  font-weight: 600;
  color: #333333;
}

/* 描述列表标签值 */
.lesson-field-value {
  font-size: 16px;
  line-height: 25px;
  color: #111c1c;
  word-break: break-word;
}

.lesson-author-date-row {
  display: flex;
  justify-content: center;
  font-size: 16px;
  line-height: 25px;
  color: #676879;
  margin-top: 20px;
}

.lesson-author-date-row .lesson-field-value {
  margin: 0px 5px;
  font-size: 15px;
  //line-height: 25px;
}
.have-lesson-from-unit-tag {
  .lesson-author-date-row {
    margin-top: 12px;
  }
  .split-line {
    height: 0 !important;
    margin: 6px auto 6px !important;
  }
}
.lesson-from-unit-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 100px;
  padding: 8px 12px;
  margin: 12px auto;
  margin-bottom: 0;
  background-color: #F3EEFF;
  max-width: 80%;
  cursor: pointer;
  transition: background-color 0.3s ease;
  &:hover {
    background-color: #E9DCFF;
    transition: background-color 0.3s ease;
  }
  .unit-title {
    font-size: 14px;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.lesson-author-name {
  margin: 0px 5px;
  max-width: 150px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lesson-field-label-age-topic {
  display: flex;
  margin-bottom: 15px;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
  height: fit-content;
  padding-left: 20px;

  .lesson-age-field-label {
    display: flex;
    justify-content: center;
    gap: 10px;
    font-size: 14px;
    flex-wrap: wrap;
    height: fit-content;
  }
}
.framework-abbreviation-value {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 16px;
  gap: 4px;
  width: fit-content;
  height: 32px;
  background: #FEE9E9;
  border: 1px solid #F56C6C;
  border-radius: 20px;
  flex: none;
  order: 0;
  flex-grow: 0;
  color: #F56C6C;
}

.lesson-age-field-value {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 16px;
  gap: 4px;
  width: fit-content;
  height: 32px;
  background: #FEF5E9;
  border: 1px solid #F49628;
  border-radius: 20px;
  flex: none;
  order: 0;
  flex-grow: 0;
  color: #F49628;
}

.lesson-topic-field-value {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 16px;
  gap: 4px;
  width: fit-content;
  height: 32px;
  background: #FFEFE5;
  border: 1px solid #FF6000;
  border-radius: 20px;
  flex: none;
  color: #FF6000;
  order: 1;
  flex-grow: 0;
}

/* 描述列表主题 */
.lesson-descriptions-theme-value {
  line-height: 32px;
  font-size: 16px;
  border-radius: 2px;
  background: #eee;
  color: #777;
  padding: 2% 5%;
  margin-right: 5px;
  display: revert;
}

/*作者标签值*/
.lesson-author-tag-value {
  font-size: 20px;
  color: #333333;
  line-height: 20px;
}

.lesson-detail-option-button {
  width: 80px;
  height: 32px;
  border-radius: 2px;
  line-height: 12px;
  color: #999999;
  font-size: 12px;
  padding: 0;
}

.lesson-field-time {
  margin: 0px 40px;
  background-color: #F5F6F8;
  border-radius: 8px;
  display: flex;
  justify-content: space-around;

  .lesson-field-time-item {
    display: flex;
    flex-direction: column-reverse;
    padding: 20px 0px;
    gap: 0px;
    justify-content: center;
    align-items: center;
    vertical-align: middle;

    .lesson-field-value, .lesson-field-label {
      height: 20px;
      line-height: 20px;
    }
  }

  & > :nth-child(3) {
    margin-left: 30px;
  }
}

.fit-height {
  height: fit-content;
  /* 仅展示核心测评点 start */
  .core-measure-field {
    position: relative!important;
  }
  .core-measure {
    position: absolute;
    right: 0px;
    top: 0px;
    font-size: 15px;
    font-weight: 400;
    span {
      margin-right: 5px;
    }
  }

  /* 仅展示核心测评点 end */
}

.lesson-title-label {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 16px;
  font-size: 18px;
  font-weight: 500 !important;
  color: #ffffff;
  gap: 10px;
  margin-bottom: 0px;
  width: fit-content;
  height: 32px;
  background: #10B3B7;
  border-radius: 16px;
}

.add-padding-t-8 {
  padding-top: 8px !important;
}

.lesson-content-value {
  padding-left: 20px;
  padding-top: 16px;

  .lesson-field-label {
    color: #10B3B7;
  }

  .lesson-field-value {
    color: #111c1c;
  }

  .min-width-270 {
    min-width: 270px;
  }

  .lesson-switch {
    font-size: 15px;
    font-weight: normal;
    margin-right: 5px
  }

  ::v-deep .el-radio__label {
    font-size: 15px !important;
  }

  ::v-deep label.el-radio {
    height: 19px;
    line-height: 19px;
    margin-bottom: 0px;
  }

  ::v-deep .el-radio-group {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
    line-height: 20px;
    vertical-align: middle;
    font-size: 0;
  }
}

.el-tag {
  margin-right: 5px;
  font-size: 15px;
}

.marginRight {
  margin-right: 4%;
}

/deep/ .el-icon-circle-close {
  padding: 2px 0;
}

// 实施步骤资源 beta 样式
.new-tag {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: -5px;
  top: -8px;
}

.lesson-content-imp-step {
}

.view-hide-imp-step-subscript /deep/ & {
  //.lesson-content-imp-step span[style*="color: rgb(16, 179, 183)"] {
  //  display: none;
  //}

  .lesson-content-imp-step imp-script {
    display: none;
  }
}

// 下面是课程材料描述的展示样式
/deep/ .textarea-item.p {
  padding: 0 15px 0 0 !important;
  margin: 0 !important;
  color: #323338;
  line-height: 25px !important;
  -webkit-user-modify: read-only !important;
}

/deep/ .textarea-item-a, textarea-item-a:hover, textarea-item-a:focus {
  word-break: break-all !important;
  color: #10B3B7;
  cursor: pointer !important;
}

/deep/ .lesson-material-card {
  margin-top: 6px !important;
  margin-bottom: 10px !important;
  line-height: 18px !important;
}

/deep/ .lesson-material-card-link {
  display: flex !important;
  flex-direction: row !important;
  width: 450px !important;
  align-items: center !important;
  height: 90px !important;
  background-color: #fff !important;
  margin: 0 !important;
  border-radius: 5px !important;
  gap: 10px !important;
}

/deep/ .lesson-material-card-link .lesson-material-card-link-icon {
  max-width: calc(107px) !important;
  min-width: 90px !important;
  height: 90px !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  object-fit: contain !important;
}

/deep/ .lesson-material-card-link .lesson-material-card-link-text {
  display: flex !important;
  flex-direction: column !important;
  align-content: flex-start !important;
  gap: 10px !important;
  flex: 1 !important;
  padding-right: 10px !important;
  max-width: 430px;
}

/deep/ .lesson-material-card-site {
  width: max-content !important;
  position: relative !important;
  border: 1px solid #c0c4cc !important;
  border-radius: 5px !important;
  background-color: #fff !important;
}

/deep/ .editor-card-link-describe {
  color: #606266 !important;
  font-size: 14px;
  font-weight: bold !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2 !important;
}

/deep/ .editor-card-link-origin {
  color: #606266 !important;
  font-size: 16px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/deep/ .lesson-material-card-img {
  position: relative !important;
  width: 192px !important;
  height: 108px !important;
}

/deep/ .lesson-material-card-img img {
  width: 192px !important;
  height: 108px !important;
}

/deep/ .lesson-material-attachment-item {
  display: flex !important;
  background-color: #f2f2f2 !important;
  align-items: center !important;
  width: 452px !important;
  min-height: 35px !important;
  border-radius: 5px !important;
  margin-top: 5px !important;
}

/deep/ .lesson-material-attachment-item-title {
  line-height: 16px !important;
  display: grid !important;
  grid-template-columns: 1fr max-content max-content !important;
}

/deep/ .lesson-material-attachment-item-img {
  width: 20px !important;
  margin-left: 5px !important;
  margin-right: 5px !important;
}

/deep/ .lesson-material-attachment-item .el-button span {
  margin-left: 0 !important;
}

/deep/ .lesson-material-attachment-item .el-button {
  padding: 0 !important;
}

/deep/ .lesson-material-card-video-play {
  width: 40px !important;
  height: 40px !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  margin: auto !important;
}

/deep/ .lesson-material-card-close {
  display: none !important;
}

/* 差异化教学样式优化 */
.title-difference {
  border-top-left-radius : 24px;
  border-top-right-radius : 24px;
  border: 1px solid rgba(16, 179, 183, 0.40);
  background: rgba(16, 179, 183, 0.10);
  padding: 8px 0px;
  height: 64px!important;
  margin-top: 36px;
  text-align: center;
}

.content-difference {
  padding: 24px;
  border-radius: 20px;
  border: 1px solid rgba(16, 179, 183, 0.40);
  background: #fff;
  margin-top: -14px;
}
.title-gradient {
  width: 45px;
  height: 2px;
  border-radius: 1px;

}
.left-gradient {
  background: linear-gradient(270deg, #10B3B7 -1.16%, rgba(16, 179, 183, 0.00) 98.84%);
}
.right-gradient {
  background: linear-gradient(90deg, #10B3B7 -1.16%, rgba(16, 179, 183, 0) 98.84%)
}
.adapted-tag {
  color: var(--color-white) !important;
  background: var(--color-ai-assistant) !important;
  border: 1px solid var(--color-ai-assistant) !important;
  font-size: 12px;
  margin-left: 10px;
}

/* 移动端样式 */
.mobile-layout {
  flex-direction: column !important;

  .lesson-info-header-content {
    margin-top: 20px;
  }
}

.mobile-content {
  .lesson-field-time {
    margin: 0;
    flex-direction: row;
    flex-wrap: wrap;

    .lesson-author-date-row {
      align-items: center;
      gap: 10px;
      flex-direction: row !important;
    }

    .lesson-field-time-item {
      width: 50%;
      padding: 15px 0;
      border-bottom: 0px solid #DCDFE6;

      &:last-child {
        border-bottom: 0px solid #DCDFE6;
      }

      &::before {
        display: none;
      }

      &:nth-child(odd) {
        border-right: 1px solid #DCDFE6;
      }
    }
  }

  .lesson-field-label-age-topic {
    padding: 0;
    margin-top: 0px;
    margin-bottom: 10px;
  }

  .lesson-author-date-row {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .lesson-name {
    margin-top: 20px;
  }
}
</style>
