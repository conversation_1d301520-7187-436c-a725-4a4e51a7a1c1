<template>
  <el-button class="replicate-button" size="small" plain type="primary" @click="copyLesson" icon="el-icon-document-copy">
    {{ $t('loc.lessons2LessonReplicate') }}
  </el-button>
</template>
<script>
import Api from '../../../../../api/lessons2'
import { isTeacher } from '@/utils/common'

export default {
  name: 'LessonReplicate',
  props: [
    'lessonId', // 课程 ID
    'submodule'
  ],
  data () {
    return {
      // 防止重复操作
      loading: false
    }
  },
  methods: {
    // 复制课程
    copyLesson () {
      if (isTeacher() && this.submodule !== 'Favorite') {
        this.$analytics.sendEvent('web_lesson_library_my_less_replicate')
      } else if (isTeacher() && this.submodule === 'Favorite') {
        this.$analytics.sendEvent('web_lesson_library_my_less_favorite_repl')
      } else if (this.submodule === 'Draft') {
        this.$analytics.sendEvent('web_lesson_library_my_less_draft_repl')
      } else {
        this.$analytics.sendEvent('web_lesson_library_mgt_replicate')
      }

      if (this.submodule === 'Create') {
        // mylesson 复制课程点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_my_creation_replicate')
      }

      if (this.loading) {
        return
      }
      this.loading = true
      let replicatePathName = ''
      if (this.$route.path.includes('curriculum-genie')) {
        replicatePathName = 'LessonPlanReplicate'
      } else {
        replicatePathName = 'Replicate'
      }
      Api.copyLesson(this.lessonId)
        .then((response) => {
          this.$router.push({
            name: replicatePathName,
            params: {
              lessonId: response.id
            }
          })
        })
        .finally(() => {
          this.loading = false
        })
      // 课程详情中点击复制
      this.$analytics.sendEvent('web_lessondetails_click_replicate')
    }
  }
}
</script>
<style scoped lang="less">

</style>
