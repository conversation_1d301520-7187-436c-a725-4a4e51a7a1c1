<template>
  <div v-if="showPromoteButton">
    <!-- 推荐到机构课程按钮 -->
    <el-button @click="promoteLesson"
               size="small"
               v-if="agencyStatus === 'NONE' && (isAdmin() || agencyRight || isAuthor)">
      <i class="iconfont" style="margin-right: 5px;font-size: 14px;">&#xe601;</i>
      <span>{{ $t('loc.lessons2LessonPromote') }}</span>
    </el-button>
    <!-- 取消推荐到机构课程按钮 -->
    <el-tooltip :content="$t('loc.lessons2LessonPromotionRemove')" 
      placement="top" effect="dark" v-if="agencyStatus === 'PROMOTED' && (isAdmin() || isAuthor)">
        <el-button @click="removeFromAgencyWideLesson"
                size="small"  type="danger" plain
                >
        <i class="iconfont" style="margin-right: 5px;font-size: 14px;">&#xe626;</i>
        <span style="">{{ $t('loc.remove') }}</span>
      </el-button>
    </el-tooltip>
  </div>
</template>
<script>
import Api from '../../../../../api/lessons2'
import { mapState } from 'vuex'
import { isTeacher } from '@/utils/common'

export default {
  name: 'LessonPromote',
  props: [
    'lessonAuthorId', // 课程作者 ID
    'lessonId',// 课程 ID
    'removePromote'
  ],
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      recommendToAgencyOpen: state => state.common.open && state.common.open.recommendLessonToAgencyOpen,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
    }),
    // 是否是课程作者
    isAuthor () {
      return this.currentUser.user_id && this.lessonAuthorId && this.currentUser.user_id.toLowerCase() === this.lessonAuthorId.toLowerCase()
    },
    // 是否显示推荐按钮(管理员或开启推荐权限的老师才显示)
    showPromoteButton () {
      return !this.isCurriculumPlugin && this.open && !this.open.educator && (this.isAdmin() || (this.recommendToAgencyOpen && !this.isAdmin()))
    }
  },
  data () {
    return {
      agencyRight: null,
      agencyStatus: null,
      // 防止重复操作
      loading: false
    }
  },
  created () {
    Api.getLessonRecommendStatus(this.lessonId).then(res => {
      this.agencyRight = res.agencyRight
      this.agencyStatus = res.agencyStatus
    })
  },
  methods: {
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    // 推荐课程
    promoteLesson () {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_lesson_library_my_less_add_agency')
      } else {
        this.$analytics.sendEvent('web_lesson_library_mgt_add_agency')
      }

      if (this.loading) {
        return
      }
      this.loading = true
      Api.promoteLesson(this.lessonId)
        .then(res => {
          this.agencyStatus = 'PROMOTED'
          this.$message({
            type: 'success',
            message: 'This lesson was added to "Agency-wide Lessons" successfully!'
          })
          // 推荐课程后刷新课程列表
          this.$emit('reloadLessons')
        })
        .catch(error => {
        }).finally(() => {
        this.loading = false
      })
    },
    // 移除课程推荐
    removeFromAgencyWideLesson () {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_lesson_library_my_less_remove_agency')
      } else {
        this.$analytics.sendEvent('web_lesson_library_mgt_remove_agency')
      }
      if (this.loading) {
        return
      }
      this.loading = true
      Api.cancelPromoteToAgency(this.lessonId)
        .then(res => {
          this.agencyStatus = 'NONE'
          this.$message({
            type: 'success',
            message: this.$t('loc.lessons2RemoveSuccessTips')
          })
          // 取消推荐向外发送事件
          this.$emit('removePromote', true)
          // 取消推荐后刷新课程列表
          this.$emit('reloadLessons')
        })
        .catch(error => {
        }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style scoped lang="less">
.lesson-promote-button {
  color: #999;
  font-size: 15px;
  margin-right: 10px;
}

@font-face {
  font-family: 'iconfont';  /* Project id 2980060 */
  src: url('//at.alicdn.com/t/font_2980060_p1hqq1l2ari.woff2?t=1645011952328') format('woff2'),
  url('//at.alicdn.com/t/font_2980060_p1hqq1l2ari.woff?t=1645011952328') format('woff'),
  url('//at.alicdn.com/t/font_2980060_p1hqq1l2ari.ttf?t=1645011952328') format('truetype');
}

.lesson-promote-item {
  display: inline-block;
}
</style>
