<template>
  <el-tooltip class="dll-setting-tips" effect="light" placement="right"
              popper-class="el-tooltip__popper-lesson-plan-dll-setting">
    <div slot="content" class="dll-tips">
      <div>
        <span>{{ $t('loc.selectYourLanguage') }}</span>
      </div>
      <div class="font-size-16 font-family-ssp m-b-xs" style="display: flex">
        <div>{{ $t('loc.DLLSettingTipInfo1') }}</div>
      </div>
      <el-image :src="dllImage" style="width: 98%"></el-image>
    </div>
    <i class="el-icon-question" style="line-height: 41px;color: #969696"></i>
  </el-tooltip>
</template>

<script>
import LessonMediaViewer from '@/views/modules/lesson2/lessonLibrary/components/LessonMediaViewer'
import DllSettingTipImage from '@/assets/img/lesson2/plan/dll_setting_tip.jpg'

export default {
  name: 'DllTipsQuestion',
  components: { LessonMediaViewer },
  data () {
    return {
      dllImage: DllSettingTipImage
    }
  }
}
</script>

<style lang="less">
.el-tooltip__popper-lesson-plan-dll-setting {
  border-color: #dcdfe6 !important;

  &.is-light[x-placement^=right] .popper__arrow {
    border-right-color: #dcdfe6;
  }
}
</style>
<style scoped lang="less">
.dll-setting-tips /deep/ .el-tooltip__popper.is-light {
  margin-left: 45px;
}

.dll-tips {
  width: 340px;
  padding: 5px;

  & > :first-child {
    margin: 5px 0;

    & > :first-child {
      font-size: 18px;
      font-family: Source Sans Pro;
      color: #000;
      font-weight: 500;
    }
  }

  & > :nth-child(2) {
    color: #555
  }

  & > :nth-child(3) {
    color: #555
  }
}

.font-family-ssp {
  font-family: "Source Sans Pro";
}
</style>
