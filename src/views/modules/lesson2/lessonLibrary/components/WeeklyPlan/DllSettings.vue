<template>
  <div>
    <el-button icon="lg-icon lg-icon-settings" @click="openSettingDialog">
    </el-button>
    <!-- 设置弹窗 -->
    <dll-dialog :week-plan-setting-open.sync="settingVisible" :languages="languages" :send-open="sendOpen"
                :lang-codes="langCodes" :group-id="currentGroupId" :key="currentGroupId" @syncData="syncData" v-if="settingVisible"/>
  </div>

</template>

<script>
import DllDialog from '@/views/modules/lesson2/lessonLibrary/components/WeeklyPlan/DllSettingDialog'
import DllApi from '@/api/dll/DLL'

export default {
  name: 'DllSettings',
  components: {
    DllDialog
  },
  inject: ['dllContext'],
  data () {
    return {
      showStatus: true, // 是否显示过设置弹窗
      sendOpen: false, // 只分享 DLL 小孩的开关
      langCodes: [], // 选择的语言
      languages: [], // 可选语言
      settingVisible: false, // 设置弹窗是否显示
      currentGroupId: '' // 当前班级编号
    }
  },
  watch: {
    'dllContext.groupId': {
      immediate: true,
      handler (value) {
        // 有值再进行查询，防止数据还未加载完就查询导致接口报错
        if (value) {
          this.currentGroupId = value
          // 切换班级，课程 ID 重置，避免切换班级选择相同的课程不触发监听
          this.$set(this.dllContext,'lessonId',null)
          // 获取该班级是否显示过设置弹窗
          DllApi.getDLLSettingShowStatus(value).then(res => {
            this.showStatus = res.showStatus
          })
          // 获取课程设置的配置信息
          DllApi.getGroupLanguageList(value).then(res => {
            let unSelectList = res.unSelectList || []
            let selectList = res.selectList || []
            this.languages = selectList.concat(unSelectList)
            let langCodeList = selectList && selectList.map(lang => {
              return lang.code
            }) || []
            this.langCodes = langCodeList
          })
          DllApi.GetGroupDLLShareOnlyDLLChildResponse(value).then(res => {
            this.sendOpen = res.onlyDLLChild
          })
        }
      }
    },
    'dllContext.lessonId': {
      handler (value) {
        // 若未显示过设置弹窗，查询课程是否为DLL课程
        if (value && !this.showStatus) {
          this.isDllLesson(value)
        }
      }
    },
    langCodes: {
      handler (value) {
        this.dllContext && (this.dllContext.langCodes = value)
      }
    },
    sendOpen (value) {
      this.dllContext && (this.dllContext.onlyDllChildOpen = value)
    }
  },
  methods: {
    syncData (data) {
      if (data) {
        this.sendOpen = data.sendOpen
        this.languages = data.languages
        this.langCodes = data.langCodes
        this.settingVisible = data.weekPlanSettingOpen
      }
    },
    isDllLesson (lessonId) {
      DllApi.getSubjects(lessonId).then(res => {
        if (res && res.length > 0) {
          // 为 DLL 课程,显示弹窗,添加显示记录
          this.settingVisible = true
          DllApi.closeGroupDllSettingTips(this.currentGroupId)
          this.showStatus = !this.showStatus
        }
      })
    },
    openSettingDialog () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_setting')
      this.settingVisible = true
      if (!this.showStatus) {
        // 没有显示过,添加显示记录
        DllApi.closeGroupDllSettingTips(this.currentGroupId)
        this.showStatus = !this.showStatus
      }
    }
  },
  created () {

  }
}
</script>

<style scoped>

</style>
