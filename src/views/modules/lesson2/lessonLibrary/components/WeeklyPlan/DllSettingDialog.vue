<template>
  <el-dialog
    :append-to-body="true"
    :visible.sync="weekPlanSettingOpen"
    class="dll-tips-dialog"
    width="60%">
    <span slot="title">{{ $t('loc.DLLSettingTitle') }}</span>
    <div class="font-size-16 lg-color-text-primary font-weight-normal">{{ $t('loc.DLLSettingInfo1') }}</div>
    <div class="dll-tips-form">
      <el-form :model="dllForm" ref="settingForm" class="dll-setting-form">
        <el-form-item
          prop="langCodes"
          :label="$t('loc.selectLanguage')"
          label-width="150"
          :rules="[{ required: true, message: $t('loc.plan20')}]">
          <dll-language-select style="width: 70%" :languages="languages" v-model="dllForm.langCodes"/>
          <dll-tips-question style="float: right"/>
        </el-form-item>
        <el-form-item
          prop="sendOpen"
          :label="$t('loc.onlySendToDLLParent')">
          <el-switch :inactive-value="false" :active-value="true" v-model="dllForm.sendOpen"></el-switch>
        </el-form-item>
      </el-form>
    </div>
    <div class="font-size-16 lg-color-text-secondary font-weight-normal">{{ $t('loc.DLLSettingInfo2') }}</div>
    <span slot="footer" class="dialog-footer dll-tips-dialog__footer">
      <el-button  @click="weekPlanSettingOpen = false">{{ $t('loc.close') }}</el-button>
      <el-button  type="primary" @click="saveSetting">{{ $t('loc.save') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Api from '@/api/dll/DLL'
import DllTipsQuestion from './DllTipsQuestion'
import DllLanguageSelect from '@/views/modules/lesson2/lessonLibrary/editor/dll/DllLanguageSelect'

export default {
  name: 'DllTips',
  components: {
    DllTipsQuestion,
    DllLanguageSelect
  },
  props: ['weekPlanSettingOpen', 'languages', 'langCodes', 'sendOpen', 'groupId'],
  data () {
    return {
      dllForm: {
        sendOpen: false,
        langCodes: [] // 选择的语言
      }
    }
  },
  watch: {
    weekPlanSettingOpen: {
      handler: function (val) {
        if (val) {
          this.initFromData()
        }
        this.$emit('update:weekPlanSettingOpen',this.weekPlanSettingOpen)
      }
    }
  },
  methods: {
    saveSetting () {
      this.$analytics.sendEvent('web_weekly_plan_edit_click_lang_save')
      this.$refs['settingForm'].validate((valid) => {
        if (valid) {
          let langCodes = this.dllForm.langCodes && this.dllForm.langCodes
            .map((language, index) => {
              return {
                langCode: language,
                sortIndex: index
              }
            })
          let dLLGroupLanguage = { groupId: this.groupId, languageLists: langCodes }
          Api.setGroupLanguage(dLLGroupLanguage)
          Api.setGroupDLLShareOnlyDLLChild(this.groupId, this.dllForm.sendOpen)
          // 同步到外部数据
          this.$emit('syncData',{
            languages: this.languages,
            langCodes: this.dllForm.langCodes,
            sendOpen: this.dllForm.sendOpen
          })
        }
      })
    },
    initFromData () {
      this.dllForm.sendOpen = this.sendOpen
      this.dllForm.langCodes = this.langCodes
      this.languageList = this.languages
    }
  },
  created () {
    this.initFromData()
  }
}
</script>

<style scoped lang="less">
@media only screen and (max-width:1199px){
  //ipad
  .dll-tips-dialog /deep/ .el-dialog__body {
    padding: 10px 20px;
  }

  .dll-tips-dialog /deep/ .el-dialog {
    border-radius: 4px;
  }

  .dll-tips-checkbox /deep/ .el-checkbox__label {
    font-size: 12px;
  }

  .dll-tips-dialog {
    & > :first-child {
      color: #111c1c;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .dialog-footer {
    margin-top: 20px;
  }

  .dll-tips-form {
    background-color: #fafafa;
    padding: 10px 10px 0px;
    margin: 10px 0 0px;
    border-radius: 3px;
  }

  .dll-setting-form {
    & /deep/ .el-form-item {
      margin-bottom: 10px;
    }

    & /deep/ .el-form-item__label {
      font-size: 16px;
      color: #111c1c;
    }
  }
}
@media only screen and (min-width:1200px){
  //web
  .dll-tips-dialog /deep/ .el-dialog__body {
    padding: 0 20px;
  }

  .dll-tips-dialog /deep/ .el-dialog {
    border-radius: 4px;
  }

  .dll-tips-checkbox /deep/ .el-checkbox__label {
    font-size: 12px;
  }

  .dll-tips-dialog {
    & > :first-child {
      color: #111c1c;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .dialog-footer {
    margin-top: 20px;
  }

  .dll-tips-form {
    background-color: #fafafa;
    padding: 10px 10px 0px;
    margin: 10px 0 0px;
    border-radius: 3px;
  }

  .dll-setting-form {
    & /deep/ .el-form-item {
      margin-bottom: 10px;
    }

    & /deep/ .el-form-item__label {
      font-size: 16px;
      color: #111c1c;
    }
  }
}
</style>
