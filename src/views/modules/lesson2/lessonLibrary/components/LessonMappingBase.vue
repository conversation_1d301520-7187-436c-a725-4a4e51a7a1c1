<script>
import LessonMappedMeasure from '@/views/modules/lesson2/lessonLibrary/components/LessonMappedMeasure'
import constants from '../../../../../utils/constants'
import { MappedStateFramework } from '@/utils/constants'
import { mapState } from 'vuex'

export default {
  name: 'LessonMappingBase',
  components: { LessonMappedMeasure },
  props: ['lesson', 'mappedFrameworkId', 'plfMeasuresList', 'showCoreMeasure'],
  data () {
    return {
      domainMappedMeasuresList: [], // DRDP 领域、测评点与映射测评点
      // measureShow: false, // 记录 show more 是否被点击过
      // measureIndex: 0, // 记录点击了 show more 的那个 col 的 index
      caPlfFrameworkId: constants.caPlfFrameworkId,
      coreMeasure: {}, // 核心测评点映射对象
      cachedomainMeasure: [], // 暂存核心测评点数据
      measures: []
    }
  },
  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser // 当前用户
    }),
    defaultCount () {
      if (this.mappedFrameworkId === this.caPlfFrameworkId) {
        return 1
      } else if (this.mappedFrameworkId === constants.illinoisFrameworkId) {
        return 3
      }
      return 2
    }
  },
  watch: {
    showCoreMeasure () {
      this.mappedCoreMeasure()
    }
  },
  created: function () {
      let mappedMeasuresList = [] // 存储映射测评点信息
      // 装载数据
      this.lesson.measures.forEach(m => {
        let mappedMeasure = this.lesson.mappedMeasures && this.lesson.mappedMeasures.find(measure => {
          return measure.measureId === m.id
        }) || null

        // 一个 DRDP 测评点对应的 所有映射的测评点
        let oneDRDPMappedMeasureList = mappedMeasure && mappedMeasure.mappedMeasures[this.mappedFrameworkId] || []
        // 若为 plf 则映射测评点的顶级领域，设置测评点的领域名称属性
        if (this.mappedFrameworkId === constants.caPlfFrameworkId) {
          oneDRDPMappedMeasureList = oneDRDPMappedMeasureList.map(measureObj => {
            this.plfMeasuresList.forEach(measure => {
              measure.children && measure.children.forEach(subMeasure => {
                subMeasure.children && subMeasure.children.forEach(m => {
                  if (m.id === measureObj.id) {
                    measureObj.domainName = measure.name
                  }
                })
              })
            })
            return measureObj
          })
        }
        mappedMeasuresList.push({
          measures: oneDRDPMappedMeasureList || [],
          show: false,
          showSize: oneDRDPMappedMeasureList.length > this.defaultCount ? this.defaultShowSize : oneDRDPMappedMeasureList.length,
          drdpMeasure: { ...m }
        })
      })
    // 排序 （有映射的排在一起，无映射的排在一起）
      let exist = []
      let notExist = []
      mappedMeasuresList.forEach(m => {
        if (m.measures.length > 0) {
          exist.push(m)
        } else {
          notExist.push(m)
        }
      })
        mappedMeasuresList = exist.concat(notExist)
      // 构建 DRDP 领域、测评点与映射的测评点信息
        this.lesson.domains.forEach((domain,index) => this.domainMappedMeasuresList.push({
          domainInfo: domain,
          mappedInfo: mappedMeasuresList.filter(item => {
              if (!item.drdpMeasure.parentId || item.drdpMeasure.parentId === domain.id) {
                return item
              }
            }
          )
        }))
      this.cachedomainMeasure = this.domainMappedMeasuresList
      this.mappedCoreMeasure()
  },
  methods: {
    mappedCoreMeasure () {
      const data = {}
      this.domainMappedMeasuresList.forEach(item => {
        const domainAndMapped = item.mappedInfo
        domainAndMapped.forEach(measure => {
          data[measure.drdpMeasure.name] = measure.drdpMeasure.core
        })
      })
      this.coreMeasure = data
      const cacheData = JSON.stringify(this.cachedomainMeasure)
      if (!this.showCoreMeasure) {
        this.domainMappedMeasuresList = this.cachedomainMeasure
         return
      }
      this.filterCore(cacheData)
    },
    // 过滤核心测评点
    filterCore (data) {
      data = JSON.parse(data)
      data.map(item => {
        item.mappedInfo = item.mappedInfo.filter(val => {
          return val.drdpMeasure.core
        })
        return item.mappedInfo
      })
      this.domainMappedMeasuresList = data
      this.isShowHaventNotice(data)
    },
    // 显示是否存在核心测评点
    isShowHaventNotice (data) {
      this.measures = []
      data.forEach(item => {
        item.mappedInfo.forEach(val => {
          if (val.drdpMeasure.core === true) {
            this.measures.push(val)
          }
        })
      })
    },
    mappedInfoDrdpMeasureAbbreviation (domainAndMapped) {
      let info = ''
      let mappedInfos = domainAndMapped.mappedInfo
      // 如果没有 mappedInfo 数据， 那么就直接返回一个空字符串
      if (!mappedInfos) {
        return info
      }
      // 如果 length === 0，那么也返回一个字符串
      if (mappedInfos.length === 0) {
        return info
      }
      mappedInfos.forEach((mappedInfo) => {
        // 如果存在 drdpMeasure，那么就拼接
        if (mappedInfo.drdpMeasure) {
          info += '; '
          info += mappedInfo.drdpMeasure.abbreviation
        }
        if (this.coreMeasure[mappedInfo.drdpMeasure.name]) {
          info += `<span style="color: red">*</span>`
        }
      })
      return info.substring(1)
    },
    showMore (domainIndex, index) {
      let measure = this.domainMappedMeasuresList[domainIndex].mappedInfo[index]
      measure.show = !measure.show
      this.$set(this.domainMappedMeasuresList[domainIndex].mappedInfo, index, measure)
    },
    // 格式化测评点描述
    formatDescription (description) {
      return description.split('\n').join('<br/>')
    }
  }
}
</script>

<style lang="less" scoped>

@media only screen and (max-width: 1200px){
  .ipad-adapter {
    margin-left: 30px!important;
  }
}

@media only screen and (max-width:1199px){
  //ipad
  .fit-width {
    width: fit-content;
    height: 24px;
    line-height: 24px;
  }
  .lesson-detail-ccss {
    .lesson-detail-no-drdp-mapping {
      display: flex;
      margin-bottom: 5px;
      // flex-wrap: nowrap;
      padding-left: 16px !important;
      text-align: start;
      vertical-align: middle;
      align-items: flex-start;
    }
    .lesson-detail-drdp-mapping {
      margin-bottom: 5px;
      margin-left: -10px;
      padding: 0px !important;
      //display: flex;
      //flex-direction: row-reverse;
    }

    // 每一个测评点区域
    & > * {
      & .lesson-detail-ccss-box {
        background-color: #F8F8F8;
        padding: 5px 10px;
        border-radius: 5px;
        margin-bottom: 10px;
      }
      & .lesson-detail-no-mapping-ccss-box {

      }
    }
  }

  .lesson-detail-ccss-tag {
    margin: 0 5px 5px 0;
    float: left;
  }
  // 负责占位
  .lesson-detail-ccss-tag-placeholder{
    width: 165px;
    height: 29px;
    margin: 0 5px 5px 0;
    float: left;
  }
  .lesson-info-detail-box {
    //display: flex;
    //flex-wrap: wrap;
    max-width: 570px;
    height: 24px;
    overflow: hidden;
  }
  .lesson-detail-ccss-load {
    min-width: 35px;
    flex-shrink: 0;
    width: fit-content;
    height: 30px;
    line-height: 25px;
  }
  .fixed-show-more-or-less {
    position: absolute;
    bottom: -7px;
    right: 3px;
  }

  .marginRight {
    margin-right: 4%;
  }

  .lesson-detail-ccss-title {

    & > :first-child {
      & > :first-child {
        font-size: 16px;
        color: #000;
      }

      & > :last-child {
        font-size: 16px;
        line-height: 25px;
      }
    }
  }

  /* 描述列表标签值 */
  .lesson-field-value {
    font-size: 16px;
    line-height: 25px;
    display: inline;
    // width: 100px !important;;
  }
  // 左右上下居中对齐
  .line-height-80 {
    position: relative;
    top: 10px;
    align-content: end;
    text-align: end;
    align-self: end;
  }
  .relative-top-5 {
    position: relative;
    top: 5px;
    align-content: end;
    text-align: end;
    align-self: end;
  }
  .flex-warp {
    display: flex;
    flex-wrap: wrap;;
  }
}

@media only screen and (min-width:1200px){
  //web
  .fit-width {
    width: fit-content;
    height: 24px;
    line-height: 24px;
  }
  .lesson-detail-ccss {
    .lesson-detail-no-drdp-mapping {
      display: flex;
      margin-bottom: 5px;
      // flex-wrap: nowrap;
      padding-left: 16px !important;
      text-align: start;
      vertical-align: middle;
      align-items: flex-start;
    }
    .lesson-detail-drdp-mapping {
      margin-bottom: 5px;
      margin-left: -10px;
      padding: 0px !important;
      display: flex;
      flex-direction: row-reverse;
    }

    // 每一个测评点区域
    & > * {
      & .lesson-detail-ccss-box {
        background-color: #F8F8F8;
        padding: 5px 10px;
        border-radius: 5px;
        margin-bottom: 10px;
      }
      & .lesson-detail-no-mapping-ccss-box {

      }
    }
  }

  .lesson-detail-ccss-tag {
    margin: 0 5px 5px 0;
    float: left;
  }
  // 负责占位
  .lesson-detail-ccss-tag-placeholder{
    width: 165px;
    height: 29px;
    margin: 0 5px 5px 0;
    float: left;
  }
  .lesson-info-detail-box {
    display: flex;
    flex-wrap: wrap;
    max-width: 370px;
    height: 24px;
    overflow: hidden;
  }
  .lesson-detail-ccss-load {
    min-width: 75px;
    flex-shrink: 0;
    width: fit-content;
    height: 30px;
    line-height: 25px;
  }
  .fixed-show-more-or-less {
    position: absolute;
    bottom: 6px;
    right: 5px;
  }

  .marginRight {
    margin-right: 4%;
  }

  .lesson-detail-ccss-title {

    & > :first-child {
      & > :first-child {
        font-size: 16px;
        color: #000;
      }

      & > :last-child {
        font-size: 16px;
        line-height: 25px;
      }
    }
  }

  /* 描述列表标签值 */
  .lesson-field-value {
    font-size: 16px;
    line-height: 25px;
    display: inline;
    // width: 95px!important;
  }
  // 左右上下居中对齐
  .line-height-80 {
    position: relative;
    top: 10px;
    align-content: end;
    text-align: end;
    align-self: end;
  }
  .relative-top-5 {
    position: relative;
    top: 5px;
    align-content: end;
    text-align: end;
    align-self: end;
  }
  .flex-warp {
    display: flex;
    flex-wrap: wrap;;
  }
}
</style>
