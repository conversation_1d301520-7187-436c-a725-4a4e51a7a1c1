<template>
  <div>
    <table class="domain-table">
      <thead style="background: #FFF2E1;">
        <tr class="font-size-16">
          <th style="width:30%; text-align:left; padding-left: 15px; font-weight: 600;">
            <span v-if="isMapped" style="color: #f49628">DRDP</span>
            <span v-else style="color: #f49628">{{abbr}}</span>
            {{ $t('loc.standardsOrMeasures') }} {{unitAddressDomains.filter(item => item.measureId && (item.mapped && isMapped) || (!isMapped && !item.mapped)).length > 0 ? '('+unitAddressDomains.filter(item => item.measureId && (item.mapped && isMapped) || (!isMapped && !item.mapped)).length+')':''}}</th>
          <th style="width:70%; text-align:left; padding-left: 15px; font-weight: 600;">{{ $t('loc.typicalBehaviors') }}</th>
          <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in unitAddressDomains"
            :key="index"
            v-show="(item.mapped && isMapped) || (!isMapped && !item.mapped)"
            class="table-row">
          <td @mouseenter="handleCellEnter($event, index)"
              @mouseleave="handleCellLeave($event, index)">
            <el-select v-model="item.measureId"
                       ref="selectMeasure"
                       placeholder="Please select"
                       @focus="focusMeasure($event)"
                       @click.native="clickMeasure($event)"
                       @blur="blurMeasure($event, 'input-'+item.measureId, index)"
                       @visible-change="(visible) => selectMeasureVisibleHandler(visible, index, item.measureId)"
                       @change="(id)=> {
                          item.measureAbbreviation = getMeasureInfoById(item.measureId).abbreviation
                          item.measureName = getMeasureInfoById(item.measureId).name
                          closeInput(index)
                       }"
                       class="measure-input"
                       popper-class="domains-table-editor-measure-select"
                       :class="item.measureId?'haveMeasure':'noMeasure'">

              <!-- 单独渲染选中的测评点 -->
              <el-option v-show="false" :key="item.measureId" :value="item.measureId" :label="item.measureAbbreviation">
              </el-option>
              
              <lg-virtual-list
                :ref="`domainsTableEditor-virtualList-${index}`"
                :data-sources="flattenedMeasures"
                :data-component="virtualMeasureOption"
                :extra-props="{ isOptionDisabled: isMeasureDisabled }"
                height="245px"
              />
            </el-select>
            <div v-if="item.measureId"
                 :id="'guide-'+item.measureId"
                 class="teacher-guide-measures">
              <span class="font-size-16 font-bold"> {{item.measureAbbreviation}}</span>
              <span style="color:red">{{ isCoreMeasure(item.measureId) ? '*' : '' }}</span>
              <br>
              <span class="font-size-14" v-html="measureOrDescription(item)"></span>
            </div>
          </td>
          <td class="position-relative">
            <el-input type="textarea"
                      class="font-size-14 textarea-hover-padding"
                      :placeholder="$t('loc.lessonTypicalPlaceholder')"
                      maxlength="5000"
                      v-model="item.typicalBehaviors"
                      @input="inputTypical($event, item)"
                      @change="checkTypical($event, item)"
                      :ref="`rubrics-${index}`"
                      autosize>
            </el-input>
            <div class="del-button">
              <el-button type="text"
                         icon="el-icon-close"
                         style="color: red"
                         @click="delRow(index)"></el-button>
            </div>
          </td>
        </tr>
        <tr align="center"
            cols>
          <td colspan="3">
            <el-button
              v-if="!isMapped || (isMapped && !allMeasureUnMapped)"
              icon="el-icon-plus"
              type="text"
              @click="addUnitDomains"
            >
              {{$t('loc.viewsample3')}}
            </el-button>

            <div v-else-if="isMapped && allMeasureUnMapped" >
              <span>{{ $t('loc.domainMappingMap6', {abbr: abbr}) }}</span>
            </div>

          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import constants from '@/utils/constants'
import frameworkUtils from '@/utils/frameworkUtils'
import LgVirtualList from '@/components/LgVirtualList.vue'
import VirtualMeasureOption from './VirtualMeasureOption.vue'

export default {
  name: 'DomainsTableEditor',
  components: {
    LgVirtualList,
    VirtualMeasureOption
  },
  props: [
    'value', // 教师指南测评点内容
    'domains', // 框架领域
    'isMapped', // 典型行为类型
    'allMeasureUnMapped', // 全部测评点无映射
    'frameworkId' // 选择的框架 ID
  ],
  data () {
    return {
      unitAddressDomains: [{ typicalBehaviors: '', measureId: '', measureName: '', measureAbbreviation: '', mapped: this.isMapped }],
      measures: null,
      measuresMap: new Map(),
      hidden: new WeakMap(),
      typicalBehaviorData: [],
      num: 1,
      virtualMeasureOption: VirtualMeasureOption,
      activeSelect: -1, // 记录已经打开的下拉框
      cellHoverStates: [] // 记录每个单元格的鼠标悬停状态
    }
  },
  created () {
    this.unitAddressDomains = this.value || [{ typicalBehaviors: '', measureId: '', measureName: '', measureAbbreviation: '', mapped: this.isMapped }]
    // 初始化单元格悬停状态数组
    this.initCellHoverStates()
  },
  watch: {
    value: {
      deep: true,
      handler (value) {
        this.unitAddressDomains = value || [{ typicalBehaviors: '', measureId: '', measureName: '', measureAbbreviation: '', mapped: this.isMapped }]
        this.$nextTick(() => {
          this.init()
        })
      }
    },
    unitAddressDomains: {
      deep: true,
      handler () {
        this.$emit('input', this.unitAddressDomains)
        this.$nextTick(() => {
          this.init()
        })
      }
    },
    domains: {
      deep: true,
      immediate: true,
      handler (value) {
        if (!this.unitAddressDomains) {
          return
        }
        let measures = []
        this.collectMeasures(measures)

        this.measures = measures
        // 切换框架，保留重复的测评点
        this.unitAddressDomains.forEach(item => {
          // 取两者交集
          let measure = measures.find(measure => measure.id === item.measureId)
          if (measure) {
            item.measureAbbreviation = measure.abbreviation
            item.measureName = measure.name
            item.measureDescription = measure.description
          }
        })
        this.$nextTick(() => {
          this.init()
        })
      }
    }
  },
  computed: {
    abbr () {
      // 获取框架的缩写
      return frameworkUtils.getFrameworkAbbr(this.frameworkId)
    },
    // 将领域和测评点展平为一个数组
    flattenedMeasures() {
      if (!this.domains || this.domains.length === 0) return []
      
      // 将所有领域和测评点展平为一个数组
      const flattened = []
      
      this.domains.forEach(domain => {
        // 添加领域作为分组标题
        flattened.push({
          id: domain.id,
          name: domain.name,
          abbreviation: domain.abbreviation,
          // 标记为领域
          isDomain: true
        })
        // 添加该领域下的所有测评点
        if (domain.children) {
          const measures = []
          this.getMeasuresBottom(domain.children, measures)
          measures.forEach(measure => {
            flattened.push({
              ...measure
            })
          })
        }
      })
      return flattened
    },
    allDomainChildren() {
      return function(domain) {
        // 如果领域不存在或者领域没有子节点
        if (!domain || !domain.children) {
          return []
        }
        // 定义 measures
        let measures = []
        // 获取底层测评点
        this.getMeasuresBottom(domain.children, measures)
        // 返回 measures
        return measures
      }
    },
    // 获取测评点信息
    measureOrDescription() {
      return (item) => {
        // 定义测评点文本
        let measureText = ''
        // 如果 item 不存在
        if (!item) {
          return measureText
        }
        // 如果 measureName 存在
        if (item.measureName) {
          measureText = item.measureName
        } else if (item.measureDescription) {
          // 如果 measureAbbreviation 存在
          measureText = item.measureDescription
        }
        if ((!measureText || measureText.trim() === '') && this.measuresMap[item.measureId] && this.measuresMap[item.measureId].description) {
          measureText = this.measuresMap[item.measureId].description
        }
        return measureText
      }
    },
    // 根据测评 ID 获取测评点信息
    getMeasureInfoById () {
      return function (id) {
        if (!this.measures || this.measures.length <= 0) {
          return null
        }
        return this.measures.find(item => item.id == id)
      }
    },
    // 根据领域、选择的测评点 IDs 判断该领域是否可选择
    isOptionalDomain () {
      return function (domain, measureId) {
        if (!measureId) {
          return true
        }
        // return domain.children.find(item => measureId == item.id)
        return true
      }
    },
    selectedMeasureIds () {
      let selectedMeasureIds = []
      this.unitAddressDomains.forEach(item => {
        selectedMeasureIds.push(item.measureId)
      })
      return selectedMeasureIds
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    // 初始化单元格悬停状态数组
    initCellHoverStates() {
      this.cellHoverStates = this.unitAddressDomains ? Array(this.unitAddressDomains.length).fill(false) : []
    },
    // 判断测评点是否禁用
    isMeasureDisabled(measureId) {
      // 如果测评点已经被选中，则禁用
      return this.selectedMeasureIds.includes(measureId);
    },
    // 从 domains 中收集所有的测评点数据
    collectMeasures (measures) {
      // 如果 domains 不为空
      if (this.domains) {
        this.domains.forEach(item => {
          if (item.children) {
            this.getMeasuresBottom(item.children, measures)
          }
        })
      }
      this.convertMeasuresToMap(measures)
      return measures
    },

    // 将测评点转换为 Map
    convertMeasuresToMap(measures) {
      this.measuresMap = measures.reduce((map, obj) => {
        map[obj.id] = obj;
        return map;
      }, {});
    },

    // 获取底层测评点
    getMeasuresBottom (children, measuresBottom) {
      if (!children || children.length === 0) {
        return
      }
      children.forEach(v => {
        const childrenNext = v.children
        if (
          !childrenNext || childrenNext.length === 0
        ) {
          measuresBottom.push(v)
        } else {
          this.getMeasuresBottom(childrenNext, measuresBottom)
        }
      })
    },
    clickMeasure (event) {
      let node = event.target.parentNode.parentNode
      node.className += ' selectInput'
    },
    checkTypical (value, item) {
      if (value && value.trim() === '') {
        item.typicalBehaviors = ''
      }
    },
    checkExamples (value, item) {
      if (value && value.trim() === '') {
        item.examples = ''
      }
    },
    init () {
      let inputs = document.getElementsByClassName('measure-input haveMeasure')
      for (let i = 0; i < inputs.length; i++) {
        inputs[i].style.display = 'none'
      }

      let inputs2 = document.getElementsByClassName('measure-input noMeasure')
      for (let i = 0; i < inputs2.length; i++) {
        inputs2[i].style.display = 'block'
      }
      let inputs3 = document.getElementsByClassName('teacher-guide-measures')
      for (let i = 0; i < inputs3.length; i++) {
        inputs3[i].style.display = 'block'
      }
      this.typicalBehaviorData = []
      // 切换框架，保留重复的测评点
      this.unitAddressDomains.forEach(item => {
        this.typicalBehaviorData.push(item.typicalBehaviors)
      })
      this.$nextTick(() => {
        let selectMeasures = this.$refs['selectMeasure']
        if (selectMeasures) {
          for (const measure of selectMeasures) {
            measure.blur()
          }
        }
        setTimeout(() => {
          this.typicalBehaviorData.forEach((_, index) => {
            if (this.$refs[`rubrics-${index}`]) {
              const textarea = this.$refs[`rubrics-${index}`][0].$el.querySelector('textarea')
              if (textarea) {
                textarea.style.height = 'auto' // 清除之前的高度
                textarea.style.height = textarea.scrollHeight + 'px' // 调整为内容高度
              }
            }
          })
        })
        this.initCellHoverStates() // 重新初始化单元格悬停状态
      })
    },
    closeInput (index) {
      this.$nextTick(() => {
        let selectMeasures = this.$refs['selectMeasure']
        if (selectMeasures.length > index && selectMeasures[index]) {
          let el = selectMeasures[index].$el
          el.className = el.className.replace('selectInput', '')
        }
        this.init()
      })
    },
    focusMeasure (event) {
      // let inputs = document.getElementsByClassName('selectInput')
      // for (let i = 0; i < inputs.length; i++) {
      //   inputs[i].className = inputs[i].className.replace('selectInput', '')
      // }
      let node = event.target.parentNode.parentNode
      node.className += ' selectInput'
    },
    blurMeasure (event, name, index) {
      // 如果下拉框存在，则不进行处理
      if (this.$refs['selectMeasure'] && this.$refs['selectMeasure'][index] && this.$refs['selectMeasure'][index].visible) {
        return
      }
      let node = event.target.parentNode.parentNode
      node.className = node.className.replace('selectInput', '')

      this.$nextTick(() => {
        if (node.parentNode.querySelector('.measure-input.haveMeasure')) {
          node.parentNode.querySelector('.measure-input.haveMeasure').style.display = 'none'
        }
        if (node.parentNode.querySelector('.measure-input.noMeasure')) {
          node.parentNode.querySelector('.measure-input.noMeasure').style.display = 'block'
        }
        if (node.parentNode.querySelector('.teacher-guide-measures')) {
          node.parentNode.querySelector('.teacher-guide-measures').style.display = 'block'
        }
      })
    },
    handleCellEnter (event, index) {
      this.cellHoverStates[index] = true // 设置对应单元格的鼠标进入状态
      // 显示学校输入框
      event.target.style.height = `${event.target.offsetHeight}px`
      if (event.target.querySelector('.measure-input.haveMeasure')) {
        event.target.querySelector('.measure-input.haveMeasure').style.display = 'block'
      }
      if (event.target.querySelector('.measure-input.noMeasure')) {
        event.target.querySelector('.measure-input.noMeasure').style.display = 'block'
      }
      if (event.target.querySelector('.teacher-guide-measures')) {
        event.target.querySelector('.teacher-guide-measures').style.display = 'none'
      }
    },

    /** 鼠标移出cell */
    handleCellLeave (event, index) {
      this.cellHoverStates[index] = false // 设置对应单元格的鼠标离开状态
      // 隐藏学校班级的输入框
      if (event.target.querySelector('.selectInput')) {
        return
      }
      event.target.style.height = 'auto'
      if (event.target.querySelector('.measure-input.haveMeasure')) {
        event.target.querySelector('.measure-input.haveMeasure').style.display = 'none'
      }
      if (event.target.querySelector('.measure-input.noMeasure')) {
        event.target.querySelector('.measure-input.noMeasure').style.display = 'block'
      }
      if (event.target.querySelector('.teacher-guide-measures')) {
        event.target.querySelector('.teacher-guide-measures').style.display = 'block'
      }
    },
    inputTypical (value, item) {
      if (!this.frameworkId) {
        item.typicalBehaviors = ''
        this.$message.error('Please select the framework first')
      }
    },
    selectMeasureVisibleHandler (visible, index, measureId) {
      // 记录正在打开的下拉框的 index
      this.activeSelect = visible ? index : -1
      
      if (!this.frameworkId && visible) {
        this.$message.error('Please select the framework first')
        return
      }
      
      // 当下拉框关闭且鼠标不在对应单元格内时，恢复显示状态
      if (!visible && !this.cellHoverStates[index]) {
        this.$nextTick(() => {
          const selectRef = this.$refs['selectMeasure'] && this.$refs['selectMeasure'][index]
          // 当下拉框关闭时，实现和 handleCellLeave 一样的效果
          if (selectRef) {
            const tdElement = selectRef.$el.closest('td')
            if (tdElement) {
              tdElement.style.height = 'auto'
              // 隐藏学校班级的输入框
              if (tdElement.querySelector('.measure-input.haveMeasure')) {
                tdElement.querySelector('.measure-input.haveMeasure').style.display = 'none'
              }
              if (tdElement.querySelector('.measure-input.noMeasure')) {
                tdElement.querySelector('.measure-input.noMeasure').style.display = 'block'
              }
              if (tdElement.querySelector('.teacher-guide-measures')) {
                tdElement.querySelector('.teacher-guide-measures').style.display = 'block'
              }
            }
          }
        })
      }
      // 如果下拉框打开，滚动到选中项
      if (visible && measureId) {
        this.$nextTick(() => {
          const virtualList = this.$refs[`domainsTableEditor-virtualList-${index}`] && this.$refs[`domainsTableEditor-virtualList-${index}`][0]
          if (virtualList) {
            virtualList.scrollToItem(measureId)
          }
        })
      }
    },
    addUnitDomains () {
      this.unitAddressDomains.push({
        typicalBehaviors: '',
        measureId: '',
        measureName: '',
        measureAbbreviation: '',
        mapped: this.isMapped
      })
    },
    delRow (index) {
      if (this.unitAddressDomains.length > 1) {
        this.unitAddressDomains.splice(index, 1)
      } else {
        this.unitAddressDomains = [{
          typicalBehaviors: '',
          measureId: '',
          measureName: '',
          measureAbbreviation: '',
          mapped: this.isMapped
        }]
        this.$nextTick(() => {
          this.init()
        })
      }
    },
    isCoreMeasure (measureId) {
      let isCore = false
      if (!this.domains || this.isMapped) {
        return isCore
      }
     // 直接从 measuresMap 中获取测评点信息
     if (this.measuresMap && this.measuresMap[measureId]) {
        return !!this.measuresMap[measureId].core;
      }
      return false;
    },

  }
}
</script>
<style lang="less"> 
.domains-table-editor-measure-select {
  width: 300px;
}
</style>
<style lang="less" scoped>
.teacher-guide-measures {
  padding: 10px 15px;
  line-height: 20px;
}
.domain-table {
  border-radius: 4px;
  border: 1px solid #f49628;
  width: 100%;
  border-collapse: collapse;
  color: #323338;

  tr,
  th,
  td {
    border: 1px solid #f49628;
  }
  th {
    height: 40px;
  }
}

.is-error {
  .domain-table {
    border: 1px solid red;

    tr,
    th,
    td {
      border: 1px solid red;
    }
  }
}

.table-row {
  position: relative;
}

.table-row:hover {
  .del-button {
    visibility: visible;
  }
}

.del-button {
  visibility: hidden;
  position: absolute;
  right: -20px;
  top: 0;
  color: red;
  height: -webkit-fill-available;
  align-items: center;
  display: flex;
}

/deep/.el-select {
  margin: 10px;
}

/deep/.el-input__inner {
  border-style: dashed;
}

/deep/.el-textarea__inner {
  border-color: transparent;
  resize: none;
  overflow: hidden;
}
/deep/.el-textarea__inner:hover {
  border: 1px dashed #c0c4cc;
}

/deep/.el-textarea__inner:focus {
  border: 1px dashed #10b3b7;
}
.textarea-hover-padding {
  padding: 10px 15px;
}
.textarea-hover-padding:hover, .textarea-hover-padding:focus-within{
  padding: 10px 15px;
}
</style>
