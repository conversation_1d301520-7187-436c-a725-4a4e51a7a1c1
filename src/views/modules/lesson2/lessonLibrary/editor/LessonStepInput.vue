<template>
  <!--  <div :class="simple ? '' : 'lesson-step-input'" >-->
  <div style="display: flex; flex-direction: column; margin-left: 2px;">

    <!--头部-->
    <div class="display-flex justify-content-between " style="background: rgb(232, 249, 250); height: 58px;align-items: center">
      <div>
          <label class="el-form-item__label" style="margin-left: 16px;padding: 0 0 0 0 !important;">
            <span class="color-text-danger">*</span>
            {{ $t('loc.unitPlannerLessonImplementationSteps') }} {{ value.ageGroupName }}
          </label>
      </div>
      <!-- 展示或隐藏实施步骤资源按钮 -->
      <div class="position-relative " style="margin-right: 16px"           >
        <!-- 添加 Resource Settings 组件 -->
        <ResourceSettings
              :setupEventName="'cg_lesson_plan_cre_click_resource'"
              @regenerate="generateImpStepsSource"
              />
        <div class="display-ib add-margin-l-10">
          <el-button @click="changeShowImpStepSource()" plain>
            <template #icon>
              <i class="lg-icon"
                :class="{ 'lg-icon-eye': !showImpStepSource, 'lg-icon-eye-off': showImpStepSource }"
                style="margin-right: 5px"></i>
            </template>
            <span v-if="!showImpStepSource">{{ $t('loc.unitPlannerShowSources') }}</span>
            <span v-else>{{ $t('loc.unitPlannerHideSources') }}</span>
          </el-button>
          <span class="new-tag">Beta</span>
        </div>
      </div>
    </div>
    <!--编辑部分-->
    <div class="lesson-step-content-container" :style="{display: !noMarginLeft ? 'flex' : 'block',
                  flexDirection: 'row',
                  marginTop:'8px'}">
      <div :style=" !showImpStepSource ? 'width:100%' :'width: 72%'" class="editor-container">
        <el-skeleton :rows="5" animated :loading="implementationStepsLoading">
          <template>
            <div class="editor-wrapper">
              <editor
                class="lesson-step-editor"
                v-model="content"
                @blur="handleBlur"
                :hiddeImpStepSource="!showImpStepSource"
                :impStepModel="true"
                :placeholder="`${$t('loc.lesson2NewLessonFormPlaceHolderSteps')} ${value.ageGroupName}`"
              />
            </div>
          </template>
        </el-skeleton>
      </div>

      <div class="lesson-step-media-container" :style=" !noMarginLeft ? 'margin-left: 33px;    width: 36%;':'margin-left: 0'">
    <!-- 快速添加课程不显示图片上传组件  showUrlBtn="true" 目的是 是否要显示 URL 按钮-->
    <media-uploader showUrlBtn="true" typeDistinguish="stepMedia" v-if="!simple" v-model="media"
                    :tips="$t('loc.lesson2NewLessonStepMediaUploaderTips')"
                    class="media-uploader-small" :validate-event="false">
      <div slot="tips" style="margin-left:35px">
        <ul style="text-align: left;">
          <li class="media-uploader-tips-li" style="list-style:disc;width: 310px;font-size: 12px;">{{$t('loc.coverExternalMediaUploadSize')}}</li>
          <li class="media-uploader-tips-li" style="list-style:disc;width: 310px;font-size: 12px;">{{$t('loc.materialAndStepDescription')}}</li>
        </ul>
      </div>
    </media-uploader>
        <!--实施步骤资源内容-->
        <!--按钮被禁用时，也就是重新生成时，无论是否有资源都不展示-->
        <div v-if="showImpStepSource">
          <ImplementationStepSources
            :sourcePreview="sourcePreview"
            :step=value
            :showImpStepSource=showImpStepSource
            :lessonId=this.lessonId
            parentComponentName="LessonStepInput"
            :append-to-body="true"
            @upStepImpStepSource="upStepImpStepSource"
            @before-save-resources="beforeSaveResources"
            @save-resources-success="saveResourcesSuccess"
            @save-resources-failed="saveResourcesFailed"
            ref="implementationStepSources"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import Editor from '@/views/modules/lesson2/component/editor'
import MediaUploader from '@/views/modules/lesson2/component/mediaUploader'
import emitter from 'element-ui/src/mixins/emitter'
import ImplementationStepSources
  from '@/views/modules/lesson2/unitPlanner/components/editor/ImplementationStepSources.vue'
import ResourceSettings from '@/views/modules/lesson2/lessonLibrary/editor/ResourceSettings.vue'
export default {
  name: 'LessonStepInput',
  components: {
    ImplementationStepSources,
    MediaUploader,
    ResourceSettings,
    Editor
  },
  mixins: [emitter],
  props: [
    'simple', // 是否是快速添加课程
    'value', // step 数据
    'validateEvent',
    'sourcePreview',
    'noMarginLeft', // 控制 margin-left
    'lessonId' // 课程ID
  ],
  computed: {
    _validateEvent() {
      return this.validateEvent || this.validateEvent === undefined;
    },
  },
  data() {
    return {
      content: this.value.content,
      media: this.value.media,
      showImpStepSource: true, // 主动控制是否显示实施步骤资源
      implementationStepsLoading: false, // 添加资源保存加载状态
      mediaContainerHeight: 0 // 存储媒体容器高度
    }
  },
  watch: {
    'value.content': {
      handler(value) {
        this.content = value
      },
      deep: true
    },
    value(value) {
      value = value || {};
      this.content = value.content;
      this.media = value.media;
    },
    content (value) {
      const impStepAndSource = this.value.lessonImpStepAndSource
      if (impStepAndSource && impStepAndSource.sources && impStepAndSource.sources.length > 0) {
        this.value.lessonImpStepAndSource.impStepContent = value
      }

      // input 为默认事件
      this.$emit('input', { ...this.value, content: value, media: this.media })
      if (this._validateEvent) {
        this.dispatch('ElFormItem', 'el.form.change', [value])
      }
    },
    media(value) {
      this.$emit('input', {...this.value, description: this.description, media: value});
    },
  },
  created() {
    if (this.value.externalMediaUrl && this.value.externalMediaUrlId){
      this.media.externalMediaUrl=this.value.externalMediaUrl
      this.media.externalMediaUrlId=this.value.externalMediaUrlId
    }

    // 初始化 CSS 变量
    document.documentElement.style.setProperty('--editor-max-height', '400px');

    // 添加窗口大小变化监听，以便动态调整高度
    window.addEventListener('resize', this.getMediaContainerHeight);
  },
  mounted() {
    // 组件挂载后获取媒体容器高度
    this.getMediaContainerHeight();
  },

  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.getMediaContainerHeight);
  },
  methods: {
    updateContent (value) {
      this.content = value
    },
    // 改变是否可以显示实施步骤资源
    changeShowImpStepSource () {
      this.showImpStepSource = !this.showImpStepSource
    },

    // 更新 step 中的实施步骤资源
    upStepImpStepSource (lessonImpStepAndSource) {
      // 更新课程信息
      this.$emit('updateLessonImpStepAndSource', lessonImpStepAndSource, this.value.ageGroupName)
      this.$forceUpdate()
    },

    // 异步调用到实施步骤资源组件
    generateImpStepsSource (lessonId) {
      this.showImpStepSource = true
      this.$refs.implementationStepSources.generateResourcesByImpStep(this.content, lessonId)
      // 发送埋点事件
      this.$analytics.sendEvent('cg_lesson_plan_cre_click_resource_reg')
    },

    handleBlur () {
      if (this._validateEvent) {
        this.dispatch('ElFormItem', 'el.form.change', [this.content]);
      }
    },

    // 资源保存前 - 显示加载状态
    beforeSaveResources() {
      // 设置实施步骤为加载状态
      this.implementationStepsLoading = true;
      this.getMediaContainerHeight();
      // 添加一个简单的超时保护，确保加载状态不会永久停留
      setTimeout(() => {
        if (this.implementationStepsLoading) {
          this.implementationStepsLoading = false;
        }
      }, 10000); // 10秒后自动关闭加载状态
    },

    // 资源保存成功 - 更新内容并关闭加载状态
    saveResourcesSuccess(content) {
      // 如果接收到了新的实施步骤内容，则更新
      if (content) {
        this.updateContent(content);
      }
      // 关闭加载状态
      this.implementationStepsLoading = false;
    },

    // 资源保存失败 - 关闭加载状态
    saveResourcesFailed() {
      // 关闭加载状态
      this.implementationStepsLoading = false;
    },

    // 获取 lesson-step-media-container 高度
    getMediaContainerHeight() {
      this.$nextTick(() => {
        const mediaContainer = document.querySelector('.lesson-step-media-container');
        if (mediaContainer) {
          const height = mediaContainer.offsetHeight;
          // 当高度大于 400px 时，动态设置编辑器容器的最大高度
          if (height > 400) {
            // 获取所有需要设置高度的 ql-container 元素
            const qlContainers = document.querySelectorAll('.lesson-step-editor .editor .ql-container');
            if (qlContainers.length > 0) {
              qlContainers.forEach(container => {
                container.style.maxHeight = `${height}px`;
                container.style.overflowY = 'auto';
              });
            }
            // 同时更新 CSS 变量，以便 CSS 选择器也能使用这个高度
            document.documentElement.style.setProperty('--editor-max-height', `${height}px`);
          }
        }
      });
    }
  }
}
</script>
<style scoped>
@media screen and (max-width:1199px) {
  /deep/ .media-uploader-small .media-uploader{
    width: 340px;
    height: 190px;
  }
  /deep/ .media-uploader-small .media-uploader-select{
    width: 330px;
    height: 190px;
  }
}

@media screen and (max-width: 768px) {
  .lesson-step-content-container {
    display: flex;
    flex-direction: column;
  }

  .lesson-step-media-container {
    margin-left: 0;
    margin-top: 15px;
  }
}

@media screen and (min-width: 769px) {
  .lesson-step-content-container {
    display: flex;
    flex-direction: row;
  }

  .lesson-step-media-container {
    margin-left: 33px;
  }
}

/* 使用多种选择器组合确保样式能被应用 */
/deep/ .lesson-step-editor .editor .ql-container {
  max-height: var(--editor-max-height, 370px) !important;
  overflow-y: auto !important;
  min-height: 400px !important;
}

.lesson-step-input {
  display: grid;
  grid-template-columns: 1fr auto;
  /* gap: 33px; */
}

.media-uploader-small {
  /* width: 340px; */
  height: 185px;
  margin-bottom: 15px;
  /* margin-left: -10px; */
}

.new-tag {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: 1px;
  top: -7px;
}
</style>