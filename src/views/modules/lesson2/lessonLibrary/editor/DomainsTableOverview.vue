<template>
  <div>
    <table class="domain-table"
           v-if="unitAddressDomains && unitAddressDomains.length > 0">
      <thead style="background: #FFF2E1;">
        <tr class="font-size-16" align="left">
          <th class="text-bolder" style="width:30%; text-align:left; padding-left: 15px;">
            <span v-if="isMapped" style="color: #f49628">DRDP-PS</span>
            {{ $t('loc.standardsOrMeasures') }} ({{unitAddressDomains.length}})</th>
          <th class="text-bolder" style="width:70%; text-align:left; padding-left: 15px;">{{ $t('loc.typicalBehaviors') }}</th>
          <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
        </tr>
      </thead>
      <tbody>
        <template v-for="(item, i) in unitAddressDomains" >
          <tr :key="i"
              class="table-row">
            <td style="padding: 12px 8px;">
              <div class="teacher-guide-measures">
              <span class="font-size-16 font-bold">{{item.measureAbbreviation}} </span>
              <span style="color:red">{{ item.core?'*':'' }}</span>
              <br>
              <span class="font-size-16" v-html="measureOrDescription(item)">{{item.measureName}}</span>
            </div>
            </td>
            <td style="padding: 12px 8px;">
              <div class="guide-text font-size-16">{{item.typicalBehaviors}}</div>
            </td>
            <!-- <td style="padding: 12px 8px;">
              <div class="guide-text font-size-16">{{item.examples}}</div>
            </td> -->
          </tr>
        </template>
      </tbody>
    </table>
    <div v-else-if="isMapped && allMeasureUnMapped" class="lg-margin-top-16 text-center font-size-16 color-676879">
      {{ $t('loc.measureMapEmptyTip') }}
    </div>
    <div v-else-if="isMapped && !allMeasureUnMapped" class="lg-margin-top-16 text-center font-size-16 color-676879">
      {{ $t('loc.measureMapAddTip') }}
    </div>
    <div v-else class="lg-margin-top-16 text-center font-size-16 color-676879">
      {{ $t('loc.noCoreMeasureTip') }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'DomainsTableOverview',
  props: ['unitAddressDomains', 'domains', 'frameworkId', 'isMapped', 'allMeasureUnMapped'],
  data () {
    return {
      measures: [] // 当前框架下领域所有测评点信息
    }
  },
  watch: {
    domains: {
      deep: true,
      immediate: true,
      handler (value) {
        let measures = []
        this.collectMeasures(measures)
        // 切换框架，保留重复的测评点
        this.unitAddressDomains.forEach(item => {
          // 取两者交集
          let measure = measures.find(measure => measure.id === item.measureId)
          if (measure) {
            item.measureAbbreviation = measure.abbreviation
            item.measureName = measure.name
            item.measureDescription = measure.description
          }
        })
      }
    }
  },
  computed: {
    measureOrDescription() {
      return (item) => {
        let measureText = ''
        if (!item) {
          return measureText
        }
        if (item.measureName) {
          measureText = item.measureName
        } else if (item.measureDescription) {
          measureText = item.measureDescription
        }
        return measureText
      }
    }
  },
  methods: {
    // 从 domains 中收集所有的测评点数据
    collectMeasures (measures) {
      // 如果 domains 不为空
      if (this.domains) {
        this.domains.forEach(item => {
          if (item.children) {
            this.getMeasuresBottom(item.children, measures)
          }
        })
      }
      return measures
    },
    // 获取底层测评点
    getMeasuresBottom (children, measuresBottom) {
      if (!children || children.length === 0) {
        return
      }
      children.forEach(v => {
        const childrenNext = v.children
        if (
          !childrenNext || childrenNext.length === 0
        ) {
          measuresBottom.push(v)
        } else {
          this.getMeasuresBottom(childrenNext, measuresBottom)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.guide-text {
  white-space: pre-line;
}

.domain-table {
  border-radius: 4px;
  border: 1px solid #f49628;
  width: 100%;
  border-collapse: collapse;
  color: #323338;

  tr,
  th,
  td {
    border: 1px solid #f49628;
  }
  th {
    height: 40px;
  }

  td {
    padding-left: 5px;
  }
}

.table-row {
  position: relative;
}

.table-row:hover {
  .del-button {
    visibility: visible;
  }
}

.del-button {
  visibility: hidden;
  position: absolute;
  right: -20px;
  top: 0;
  color: red;
  height: -webkit-fill-available;
  align-items: center;
  display: flex;
}

/deep/.el-select {
  margin: 10px;
}

/deep/.el-input__inner {
  border-style: dashed;
}

/deep/.el-textarea__inner {
  border: none;
  resize: none;
}
</style>
