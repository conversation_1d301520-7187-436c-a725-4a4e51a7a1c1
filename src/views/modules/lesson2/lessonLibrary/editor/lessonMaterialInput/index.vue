<template>
  <div :class="simple ? '' : 'lesson-material-input'" class="lesson-responsive-container">
    <div :class="simple? 'lesson-material-editor-textarea-top-simple' : 'lesson-material-editor-textarea-top'" :style="editorTopStyle" class="lesson-editor-content">
      <lesson-material-editor-textarea @focus="fillMaterialInfo" v-model="description" :simple="simple"
                                       :validate-event="_validateEvent"
                                       :not-show-url-card="notShowUrlCard"
                                       :selfPlaceholder="selfPlaceholder"
                                       ref="editorTextareaRef"
                                       @style-change="styleChangeTextarea"/>
      <div v-if="!simple" style="margin: 0 15px;">
        <div class="lesson-material-attachment-item" v-for="file in fileList && fileList" :key="file.id">
          <el-image class="lesson-material-attachment-item-img el-upload-list__item-thumbnail"
                    v-if="file.status !== 'uploading'"
                    :src="file.icon" alt=""/>
          <a class="hidden-md-and-down" :href="file.url" target="_blank" :title="file.name"
             style="line-height: 16px;display: grid;grid-template-columns: 1fr max-content;color: #606266;">
            <span style="max-width: 290px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
              {{ file.name.substring(0, file.name.lastIndexOf('.')).trim() }}</span>
            <span>.{{ file.name.substring(file.name.lastIndexOf('.') + 1) }}</span>
          </a>
          <a class="hidden-lg-and-up" :href="file.url" :title="file.name"
             style="line-height: 16px;display: grid;grid-template-columns: 1fr max-content;color: #606266;">
            <span style="max-width: 290px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
              {{ file.name.substring(0, file.name.lastIndexOf('.')).trim() }}</span>
            <span>.{{ file.name.substring(file.name.lastIndexOf('.') + 1) }}</span>
          </a>
          <span style="margin-left: 5px;width: 95px">({{ formatSize(file.size) }})</span>
          <i class="el-icon-close" @click="handleFileDeleteClick(file)"></i>
        </div>
        <div style="cursor: initial;">
          <el-divider></el-divider>
          <el-upload action :accept="_accept" :limit="fileLimit" :file-list="fileList"
                     refs="uploader" :show-file-list="false"
                     :http-request="upload" :on-success="handleSuccess" :on-error="handleError"
                     :before-upload="beforeUploadHandler">
            <el-button type="text" icon="el-icon-paperclip" slot="trigger"  @click="handleUploadClick">
              {{ $t('loc.attachFile') }}
            </el-button>
            <i class="el-icon-loading" v-if="this.fileUploadLoading"></i>
          </el-upload>
        </div>
      </div>
    </div>
    <!-- 快速添加课程不显示图片上传组件  showUrlBtn="true" 目的是 是否要显示 URL 按钮-->
    <media-uploader showUrlBtn="true" typeDistinguish="materMedia" v-if="!simple" v-model="media"
                    class="media-uploader-small media-uploader-mobile" :validate-event="false">
      <div slot="tips" style="margin-left: 20px" class="media-uploader-tips">
        <ul style="text-align: left;">
          <li class="media-uploader-tips-li" style="list-style:disc;width: 310px;font-size: small;">{{$t('loc.coverExternalMediaUploadSize')}}</li>
          <li class="media-uploader-tips-li" style="list-style:disc;width: 310px;font-size: small;">{{$t('loc.materialAndStepDescription')}}</li>
        </ul>
      </div>
    </media-uploader>
  </div>
</template>

<script>
import MediaUploader from '@/views/modules/lesson2/component/mediaUploader'
import lessonMaterialEditorTextarea from '@/views/modules/lesson2/lessonLibrary/editor/lessonMaterialInput/lessonMaterialEditorTextarea'
import emitter from 'element-ui/src/mixins/emitter'
import fileUtils from '@/utils/file'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'

export default {
  name: 'LessonMaterialInput',
  components: {
    MediaUploader,
    lessonMaterialEditorTextarea
  },
  mixins: [emitter],
  props: [
    'simple', // 是否是快速添加课程
    'value',
    'validateEvent',
    'notShowUrlCard',
    'selfPlaceholder'
  ],
  computed: {
    _validateEvent () {
      return this.validateEvent || this.validateEvent === undefined
    },
    _accept () {
      return '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx'
    },
    fileLimit () {
      return 4
    }
  },
  data () {
    return {
      media: this.value && this.value.media,
      description: this.value && this.value.description,
      editorTopStyle: '',
      // 附件属性
      fileList: [],
      fileIcon: { doc, docx, pdf, ppt, pptx, xls, xlsx },
      files: this.value && this.value.attachmentMedias || [], // 附件文件 ID
      fileUploadLoading: false // 文件上传
    }
  },
  watch: {
    value (value) {
      value = value || {}
      this.description = value.description
      this.media = value.media
      if (value.attachmentMedias) {
        value.attachmentMedias.forEach(file => {
          file.icon = this.fileIcon[file.name.substring(file.name.lastIndexOf('.') + 1)]
        })
        if (!this.contains(this.fileList, value.attachmentMedias)) {
          this.fileList = this.value.attachmentMedias.slice()
        }
        if (!this.arrayEquals(this.files, this.value.attachmentMedias)) {
          this.files = this.value.attachmentMedias
        }
      }
    },
    description (value) {
      this.$emit('input', { description: value, media: this.media, attachmentMedias: this.files })
      if (this._validateEvent) {
        this.dispatch('ElFormItem', 'el.form.change', value)
      }
    },
    media (value) {
      this.$emit('input', { description: this.description, media: value, attachmentMedias: this.files })
    },
    files (value) {
      this.$emit('input', { description: this.description, media: this.media, attachmentMedias: value })
    }
  },
  methods: {
    /**
     * 更新描述信息
     */
    updateDescription (value) {
      this.$refs.editorTextareaRef.dataInitialize()
      this.description = value
    },

    styleChangeTextarea (value) {
      this.editorTopStyle = value
    },
    fillMaterialInfo () {
      this.$analytics.sendEvent('web_lesson_library_add_materials')
    },
    handleFileDeleteClick (file) {
      this.fileList = this.fileList.filter(item => item.id !== file.id)
      this.files = this.files.filter(item => item.id !== file.id)
    },
    beforeUploadHandler (file) {
      // 文件类型校验
      let type = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
      if (!this._accept.split(',').includes(type)) {
        this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'))
        return false
      }
      // 文件大小校验，10MB
      if (file.size / 1000 / 1000 > 10) {
        this.$message.error(this.$t('loc.lessons2FileUploadTips'))
        return false
      }
      return true
    },
    upload (option) {
      this.fileUploadLoading = true
      return fileUtils.uploadFile(option.file,
        {
          privateFile: false,
          processEventHandler: e => option.onProgress(e),
          annexType: 'annex'
        })
    },
    handleSuccess (response, file) {
      this.fileUploadLoading = false
      file.icon = this.fileIcon[file.name.substring(file.name.lastIndexOf('.') + 1)]
      if (!response || !response.id) {
        file.status = 'failed'
        return
      }
      file.id = response.id
      file.url = response.public_url
      this.fileList = [...this.fileList, file]
      this.files = [...this.files, { id: response.id, name: file.name, url: response.public_url, size: file.size }]
    },
    handleError (err) {
      this.fileUploadLoading = false
      let message = err && err.error_message
      message && this.$message.error(message)
    },
    formatSize (size) {
      let value = size
      let suffix = 'B'
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'KB'
      }
      if (value > 1000) {
        value = (Number(value) / Number(1000)).toFixed(2)
        suffix = 'MB'
      }
      return `${value}${suffix}`
    },
    // 判断文件是否相同
    fileEquals (a, b) {
      a = a || {}
      b = b || {}
      return a.id === b.id
    },
    // 判断文件列表的包含关系
    contains (a, b) {
      a = a || []
      b = b || []
      for (let temp of b) {
        if (!a.find(item => this.fileEquals(a, b))) {
          return false
        }
      }
      return true
    },
    // 判断文件列表的相等关系
    arrayEquals (a, b) {
      return this.contains(a, b) && this.contains(b, a)
    },
    // 重置
    reset () {
      this.fileList = []
      this.files = []
    },
    // 清空内容
    clearData () {
      this.fileList = []
      this.files = []
      this.description = []
      this.media = {}
      this.$refs.editorTextareaRef.clearValue()
    },
    // 编辑的时候展示填写的内容
    showMaterialsDialog () {
      if (this.$refs.editorTextareaRef) {
        this.$refs.editorTextareaRef.dataInitialize()
      }
    },
    /**
     * 处理上传按钮点击事件
     * 检查文件数量限制，达到限制时显示提示并阻止上传
     */
    handleUploadClick (event) {
      // 检查是否有文件正在上传中
      if (this.fileUploadLoading) {
        event.stopPropagation()
        event.preventDefault()
        return false
      }
      // 检查当前文件数量是否已达到限制
      if (this.fileList.length >= this.fileLimit) {
        // 阻止默认的文件选择行为
        event.stopPropagation()
        event.preventDefault()

        // 显示提示信息
        this.$message.warning('Upload limit reached. You can upload up to 4 files.')
        return false
      }
      // 如果未达到限制，允许正常的上传流程
    },
  },
  created () {
    if (this.value && this.value.attachmentMedias) {
      this.value.attachmentMedias.forEach(file => {
        file.icon = this.fileIcon[file.name.substring(file.name.lastIndexOf('.') + 1)]
      })
      this.fileList = this.value.attachmentMedias || []
    }
    if (this.value && this.value.externalMediaUrl && this.value.externalMediaUrlId) {
      this.media.externalMediaUrl = this.value.externalMediaUrl
      this.media.externalMediaUrlId = this.value.externalMediaUrlId
    }
  }
}
</script>

<style lang="less" scoped>
.media-uploader-small {
  width: 340px;
  height: 185px;
  margin-left: -10px;
}

.is-error .lesson-material-editor-textarea-top {
  border-color: #f56c6c !important;
}

.is-error .lesson-material-editor-textarea-top-simple {
  border-color: #f56c6c !important;
}

.lesson-material-editor-textarea-top-simple {
  border-radius: 3px;
  border: 1px solid #dcdfe6;
}

.lesson-material-input {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 33px;
}

.lesson-material-editor-textarea-top {
  display: grid;
  height: 100%;
  grid-template-rows: 1fr max-content;
  border: 1px solid;
  border-radius: 3px;
}

.lesson-material-attachment-item {
  display: flex;
  background-color: #f2f2f2;
  color: #606266;
  align-items: center;
  width: 452px;
  min-height: 35px;
  border-radius: 5px;
  margin-bottom: 5px;
  cursor: initial;
}

.lesson-material-attachment-item-img {
  width: 20px;
  margin-left: 5px;
  margin-right: 5px;
  height: 25px;
}

/deep/ .el-divider {
  height: 1px;
  width: auto;
  margin: 0;
}

/deep/ .lesson-material-attachment-item .el-icon-close {
  margin-left: auto;
  margin-right: 10px;
  top: 11px;
  cursor: pointer;
}

.media-uploader-small {
  width: 340px;
  height: 185px;
  margin-left: -10px;
}

/* 移动端适配样式 */
@media screen and (max-width: 768px) {
  .lesson-responsive-container {
    display: grid;
    grid-template-columns: 1fr !important;
    grid-template-rows: auto auto;
    gap: 20px !important;
  }
  .lesson-editor-content {
    width: 100%;
  }
  .lesson-material-attachment-item {
    width: 100% !important;
    max-width: 100%;
  }
  .media-uploader-mobile {
    width: 100% !important;
    max-width: 100%;
    margin-left: 0 !important;
  }

  .media-uploader-small {
    width: 340px;
    height: fit-content;
    margin-left: -10px;
  }
}
</style>
