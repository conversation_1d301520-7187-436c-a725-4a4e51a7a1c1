<template>
  <div class="lesson-quiz">
    <span slot="label">
      <img src="@/assets/img/lesson2/lessons2-lesson-resources3.png"
           class="lessons2-lesson-resources"  :alt="$t('loc.lessonQuiz1')"/>
      <div class="flex-center-center lessons2-lesson-resources-info">
        <span style="font-weight: 600;">{{ $t('loc.lessonQuiz1') }}</span>
      </div>
    </span>
    <div class="lg-margin-top-20">
      <div class="quiz-download-btn"
      :style="{'top': lgIsMobile ? '36px' : '20px'}"
       style="position: absolute;right: 34px;" v-if="!edit && showDownload">
        <quiz-download-dialog button-type="default" :is-plain="true" :lessonId="lessonId" button-icon="lg-icon lg-icon-download"/>
      </div>
      <el-radio-group v-model="isBloom" v-if="edit" @input="handleBloomChange" class="quiz-radio-group">
        <!-- Bloom -->
        <el-radio :label="true">
          <span>{{ $t('loc.LessonQuiz3') }}</span>
          <el-tooltip popper-class="tooltip-wrapper" class="item" effect="dark" :content="$t('loc.lessonQuizLevelBloomDesc')" placement="top">
            <i class="lg-icon lg-icon-question lg-margin-left-4"></i>
          </el-tooltip>
        </el-radio>
        <!-- DOK -->
        <el-radio :label="false">
          <span>{{ $t('loc.lessonQuizLevelDOK') }}</span>
          <el-tooltip popper-class="tooltip-wrapper" class="item" effect="dark" :content="$t('loc.lessonQuizLevelDOKDesc')" placement="top">
            <i class="lg-icon lg-icon-question lg-margin-left-4"></i>
          </el-tooltip>
        </el-radio>
      </el-radio-group>
      <div class="lesson-quiz-editor-container" ref="display" :class="{'lesson-quiz-box-hidden': hidden}">
        <LessonQuizWindow v-for="(question, index) in currentQuestions" :key="index + '' + quizRenderKey" :domains="domains" :measures="measures" :showGenerateButton="showGenerateButton"
                          :ageGroupName="ageGroupName" @deleteQuizEditor="deleteQuizEditor" @regenerateQuizQuestion="regenerateQuizQuestion" v-model="currentQuestions[index]"
                          :questionIndex="index" class="lesson-quiz-window" :isBloom="isBloom"
                          :edit="edit" :disableEditorDeleteIcon="currentQuestions && currentQuestions.length === 1" :data-id="index"
                          :generateLessonLoading="generateLessonLoading"/>
      </div>
      <el-button icon="el-icon-plus" class="add-question-button" v-if="edit" @click="addQuizEditor">{{$t('loc.LessonQuiz6')}}</el-button>
      <!-- 底部隐藏、显示按钮 -->
      <div v-if="overHeight && !edit" class="text-center">
        <el-button v-show="hidden" type="text" @click="hidden = false">{{ $t('loc.quizShowAll') }}</el-button>
        <el-button v-show="!hidden" type="text" @click="hidden = true">{{ $t('loc.hide') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import LessonQuizWindow from '@/views/modules/lesson2/lessonLibrary/editor/LessonQuizWindow.vue'
import QuizDownloadDialog from '@/views/modules/lesson2/unitPlanner/components/detail/QuizDownloadDialog.vue'
import Sortable from 'sortablejs/modular/sortable.core.esm.js'
import { isBloomQuiz } from '@/utils/common'
import { createEventSource, parseStreamData } from '@/utils/eventSource'
import { mapState } from 'vuex'
import tools from '@/utils/tools'
export default {
  name: 'LessonQuiz',
  components: {
    LessonQuizWindow,
    QuizDownloadDialog
  },
  props: {
    // quiz 问题列表
    value: {
      type: Array,
      default: () => []
    },
    // 编辑模式
    edit: {
      type: Boolean,
      default: true
    },
    // 当前年龄组
    ageGroupName: {
      type: String,
      default: ''
    },
    // 框架下的所有测评点
    domains: {
      type: Array,
      default: () => []
    },
    measures: {
      type: Array,
      default: () => []
    },
    showAiAssistant: {
      type: Boolean,
      default: false
    },
    lessonId: {
      type: String
    },
    overflowHidden: {
      type: Boolean,
      default: true
    },
    showDownload: {
      type: Boolean,
      default: true
    },
    showGenerateButton: {
      type: Boolean,
      default: false
    },
    // 课程生成状态
    generateLessonLoading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isBloom: true, // 是否是 Bloom
      quizRenderKey: 0, // quiz 渲染 key
      hidden: false, // 是否隐藏内容
      overHeight: false, // 是否超过高度
      sortable: null, // 拖拽实例
      domainsInitFinished: false, // domains 是否加载完成
      currentQuestions: this.value || [], // 当前问题列表
      leavedPage: false, // 是否离开页面
      quizRegenerateLoading: false // Quiz 重新生成的 loading 状态
    }
  },
  computed: {
    ...mapState({
      lgIsMobile: state => state.common.lgIsMobile, // 添加移动端状态
      bloomQuizSetting: state => state.curriculum.bloomQuizSetting
    }),
    allMeasures() {
      let measures = []
      if (this.domains && this.domains.length !== 0) {
        // 收集框架下的所有测评点信息
        this.collectMeasures(measures)
      }
      return measures
    }
  },
  async created () {
    this.setStepQuizBloomSetting(this.currentQuestions)
    // 如果是编辑模式
    if (this.edit) {
      // 如果当前没有 question
      if (this.currentQuestions.length === 0) {
        // 设置一个默认的 question
        this.currentQuestions = [{
          type: 'Multiple Choice',
          sortIndex: 1,
          question: '',
          answer: '',
          level: this.isBloom ? 'Understand' : 'DOK2',
          measureName: '',
          measureId: '',
          measureAbbreviation: ''
        }]
      }
    } else {
      // 非编辑模式下删除所有题干和答案都为空的 question
      if (this.currentQuestions.length !== 0) {
        this.currentQuestions = this.currentQuestions.filter(item => item.question !== '' || item.answer !== '')
      }
    }
  },
  watch: {
    value: {
      deep: true,
      handler (val) {
        if (val && val.length !== 0) {
          this.currentQuestions = val
        } else if (val && val.length === 0) {
          let measures = this.allMeasures
          const haveMeasures = measures && measures.length !== 0
          this.currentQuestions = [{
            type: 'Multiple Choice',
            sortIndex: 1,
            question: '',
            answer: '',
            level: this.isBloom ? 'Understand' : 'DOK2',
            measureName: haveMeasures ? measures[0].name : '',
            measureId: haveMeasures ? measures[0].id : '',
            measureAbbreviation: haveMeasures ? measures[0].abbreviation : ''
          }]
        }
      }
    },
    currentQuestions: {
      deep: true,
      handler (val) {
        // 生成中是从外部传递过来所以不需要再次传递到外部
        if (this.generateLessonLoading) {
          return
        }
        this.updateQuestions(val)
      }
    },
    generateLessonLoading: {
      handler (newVal, oldVal) {
        if (!newVal && oldVal && this.currentQuestions && this.currentQuestions.length !== 0) {
          this.updateQuestions(this.currentQuestions)
        }
      }
    },
    // 监听 domains 变化
    domains: {
      deep: true,
      immediate: true,
      handler (value) {
        // 如果当前没有 question
        if (!this.currentQuestions) {
          return
        }
        let measures = this.allMeasures
        // 如果是编辑模式
        if (this.edit) {
          // 如果能收集到框架下的测评点信息且存在 question
          if (measures.length !== 0 && this.currentQuestions && this.currentQuestions.length !== 0) {
            // 遍历所有 question ，给每个 question 设置 measureName、measureId、measureAbbreviation （如果不存在测评点信息则默认为框架下所有测评点中的第一个）
            this.currentQuestions = this.currentQuestions.map(question => {
              // 寻找框架下对应的测评点信息
              const measure = measures.find(measure => measure.id === question.measureId)
              // 设置 measureName、measureId、measureAbbreviation
              question.measureName = measure ? measure.name : measures[0].name
              question.measureId = measure ? measure.id : measures[0].id
              question.measureAbbreviation = measure ? measure.abbreviation : measures[0].abbreviation
              return question
            })
          }
          // 如果当前没有 question 且 domains 数据加载完毕
          if (value && !this.domainsInitFinished && this.currentQuestions.length === 0) {
            // 设置一个默认的 question
            this.currentQuestions = [{
              type: 'Multiple Choice',
              sortIndex: 1,
              question: '',
              answer: '',
              level: this.isBloom ? 'Understand' : 'DOK2',
              measureName: measures ? measures[0].name : '',
              measureId: measures ? measures[0].id : '',
              measureAbbreviation: measures ? measures[0].abbreviation : ''
            }]
            this.domainsInitFinished = true
          }
        }
        // 切换框架，保留重复的测评点信息
        this.currentQuestions.forEach(item => {
          // 取两者交集
          let measure = measures.find(measure => measure.id === item.measureId)
          if (measure) {
            item.measureAbbreviation = measure.abbreviation
            item.measureName = measure.name
          }
        })
      }
    }
  },
  mounted () {
    // 预览模式下，判断是否超过高度，如果超过高度则隐藏
    if (!this.edit && this.overflowHidden) {
      let height = this.$refs.display && this.$refs.display.clientHeight
      this.overHeight = height > 500
      this.hidden = this.overHeight
    } else {
      // 编辑模式下才创建拖拽实例
      this.$nextTick(() => {
        const lessonQuizEditorContainer = this.$el.querySelector('.lesson-quiz-editor-container')
        if (lessonQuizEditorContainer && !this.sortable) {
          const ops = {
            // 拖拽时过渡效果，0 的话没有过渡效果
            animation: 300,
            // 设置拖拽句柄，当用户鼠标在该位置上拖拽，就允许拖拽
            handle: '.quiz-editor-drag-handle',
            dataIdAttr: 'data-id',
            scroll: true,
            scrollSensitivity: 60,
            scrollSpeed: 10,
            bubbleScroll: true,
            forceFallback: true,
            onEnd: (evt) => {
              const dragFinishedArray = this.sortable.toArray()
              // 移动数组元素，为了保证每个 quiz 编辑窗的题目序号跟着拖拽位置而变化
              dragFinishedArray.forEach((cur, index) => {
                this.currentQuestions[cur].sortIndex = index + 1
              })
              // 更新当前问题列表，因为拖拽之后改变了数据
              this.updateQuestions(this.currentQuestions.sort((a, b) => a.sortIndex - b.sortIndex))
              // 渲染 key 自增，拖拽后进行重新渲染，保证显示为拖拽后的顺序
              this.quizRenderKey++
            }
          }
          this.sortable = Sortable.create(lessonQuizEditorContainer, ops)
        }
      })
    }
  },
  beforeDestroy () {
    // 标记已离开页面，防止异步操作继续执行
    this.leavedPage = true
  },
  methods: {
    isBloomQuiz,
    // 检查步骤中的测试是否 Bloom 测验
    setStepQuizBloomSetting (questions) {
      // 如果没有 question 或者 question 为空，则默认为 Bloom 测验
      if (questions && questions.length > 0 && (!!questions[0].question || !!questions[0].answer)) {
        this.isBloom = this.isBloomQuiz(questions)
      } else {
        this.isBloom = this.bloomQuizSetting
      }
    },
    handleBloomChange (value) {
      this.isBloom = value
      this.$store.dispatch('curriculum/setQuizBloomSetting', value)
    },
    // 从 domains 中收集所有的测评点数据
    collectMeasures (measures) {
      // 如果 domains 不为空
      if (this.domains) {
        this.domains.forEach(item => {
          if (item.children) {
            this.getMeasuresBottom(item.children, measures)
          }
        })
      }
      return measures
    },
    // 获取测评点数据
    getMeasuresBottom (children, measuresBottom) {
      if (!children || children.length === 0) {
        return
      }
      children.forEach(v => {
        const childrenNext = v.children

        if (
          !childrenNext || childrenNext.length === 0
        ) {
          // 如果当前已经是最底层，那么直接放入 measuresBottom
          measuresBottom.push(v)
        } else {
          // 如果不是最底层，递归获取
          this.getMeasuresBottom(childrenNext, measuresBottom)
        }
      })
    },
    // 更新 questions
    updateQuestions (questions) {
      this.$emit('input', this.currentQuestions)
    },
    addQuizEditor () {
      // 添加 quiz 编辑窗
      if (!this.domains) {
        // 如果没有选择框架
        this.$message.error(this.$t('loc.LessonQuiz10'))
        return
      }
      let measures = []
      // 获取框架下所有测评点数据
      this.domains && (measures = this.domains.flatMap(domain => domain.children) || [])
      // 添加一个默认的 question
      const newQuizQuestion = {
        type: 'Multiple Choice',
        sortIndex: this.currentQuestions ? this.currentQuestions.length + 1 : 1,
        question: '',
        showQuizRegenerateButton: false,
        answer: '',
        level: this.isBloom ? 'Understand' : 'DOK2',
        measureName: measures ? measures[0].name : '',
        measureId: measures ? measures[0].id : '',
        measureAbbreviation: measures ? measures[0].abbreviation : ''
      }
      this.currentQuestions.push(newQuizQuestion)
    },
    deleteQuizEditor (questionIndex) {
      // 删除 quiz 编辑窗
      this.currentQuestions.splice(questionIndex, 1)
      // 重新设置题号
      this.currentQuestions.forEach((question, index) => {
        question.sortIndex = index + 1
      })
      // 渲染 key 自增，删除后进行重新渲染，保证显示为删除后的顺序
      this.quizRenderKey++
    },
    // 重新生成单个 quiz 题目
    async regenerateQuizQuestion ({ questionIndex, questionLevel, questionType, measureAbbreviation, question }, callback = () => {}) {
      // 设置全局 loading 状态
      this.quizRegenerateLoading = true
      this.$emit('update:quizRegenerateLoading', true)
      
      // 为当前题目临时生成一个 id
      const tempId = tools.uuidv4()
      // 重置数据
      const currentQuestion = this.currentQuestions[questionIndex]
      currentQuestion.hasOwnProperty('showQuizRegenerateButton') && this.$delete(currentQuestion, 'showQuizRegenerateButton')
      this.$set(currentQuestion, 'generateLoading', true)
      this.$set(currentQuestion, 'question', '')
      this.$set(currentQuestion, 'answer', '')
      // 临时设置 id
      this.$set(currentQuestion, 'tempId', tempId)
      this.$set(this.currentQuestions, questionIndex, currentQuestion)
      
      // 生成参数
      let params = {
        lessonId: this.lessonId,
        useBloomLevel: this.isBloom, // 是否使用 Bloom 等级
        questionLevel,
        questionType,
        measureAbbreviation,
        previousQuestion: question
      }
      
      // 临时变量存储流式数据
      let quizData = ''
      
      // 消息回调
      let messageCallback = (message) => {
        // 如果离开页面，则不再执行
        if (this.leavedPage) {
          return
        }
        quizData += message.data
        
        // 解析返回的数据
        let parsedQuestions = parseStreamData(quizData, [
          { key: 'type', name: 'Question Type' },
          { key: 'level', name: ['Bloom\'s Taxonomy', 'DOK Level'] },
          { key: 'measureAbbreviation', name: ['Measure Abbreviation', 'Measure'] },
          { key: 'question', name: 'Question Content' },
          { key: 'answer', name: ['Correct Answer', 'Example Answer', 'Expected Answer', 'Answer Guide', 'Correct Answers', 'Example Response', 'Correct Order'] }
        ])
        
        parsedQuestions = parsedQuestions.map(parsedQuestion => {
          // 如果 level 存在，则去除空格
          if (parsedQuestion.level) {
            parsedQuestion.level = parsedQuestion.level.trim().replace(' ', '')
          }
          return parsedQuestion
        })
        
        // 如果是单个重新生成，通过临时 id 查找目标问题
        const targetQuestion = this.currentQuestions.find(q => q.tempId === tempId)
        if (targetQuestion && parsedQuestions.length > 0) {
          targetQuestion.answer = parsedQuestions[0].answer
          targetQuestion.question = parsedQuestions[0].question
          // 触发响应式更新
          this.$forceUpdate()
        }
      }
      
      return new Promise((resolve, reject) => {
        // 生成 API 调用
        createEventSource($api.urls().regenerateLessonQuiz, {}, messageCallback, 'POST', params)
          .then((res) => {
            // 设置题目序号
            let currentSortIndex = 1
            this.currentQuestions && (this.currentQuestions = this.currentQuestions.map(question => {
              this.$set(question, 'sortIndex', currentSortIndex++)
              return question
            }))
            
            // 如果离开页面，则不再执行
            if (this.leavedPage) {
              return
            }
            
            // 设置当前重新生成的 question 的加载状态 - 通过临时 id 查找
            const targetQuestion = this.currentQuestions.find(q => q.tempId === tempId)
            if (targetQuestion) {
              this.$set(targetQuestion, 'generateLoading', false)
              // 清理临时 id
              this.$delete(targetQuestion, 'tempId')
            }
            
            // 重置全局 loading 状态
            this.quizRegenerateLoading = false
            this.$emit('update:quizRegenerateLoading', false)
            
            if (callback && typeof callback === 'function') {
              callback()
            }
            resolve()
          })
          .catch(error => {
            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
            
            // 设置加载状态 - 通过临时 id 查找
            const targetQuestion = this.currentQuestions.find(q => q.tempId === tempId)
            if (targetQuestion) {
              this.$set(targetQuestion, 'generateLoading', false)
              // 清理临时 id
              this.$delete(targetQuestion, 'tempId')
            }
            
            // 重置全局 loading 状态
            this.quizRegenerateLoading = false
            this.$emit('update:quizRegenerateLoading', false)
            
            if (callback && typeof callback === 'function') {
              callback()
            }
            reject(error)
          })
      })
    }
  }
}
</script>
<style>
.tooltip-wrapper {
  max-width: 50vw;
}
</style>
<style scoped lang="less">
/* quiz 模块背景色 */
.lesson-quiz-box {
  background-color: var(--color-white);
}
/* quiz 标题 */
.lessons2-lesson-resources-info {
  position: relative;
  bottom: 39px;
  width: 467px;
  gap: 10px;
  color: var(--color-white);
  font-weight: normal;
}
/* 编辑框样式 */
/deep/ .lesson-quiz-editor {
  .editor .ql-toolbar {
    border-radius: 4px 4px 0 0;
  }
  .editor .ql-container {
    border-radius: 0 0 4px 4px;
    max-height: 500px;
    overflow: auto;
  }
}
.lesson-quiz-box-hidden {
  max-height: 500px;
  overflow: hidden;
}
@media screen and (max-width:1599px) {
  .lesson-quiz {
    position: relative;
    width: 100%;
    margin: 27px auto 0;
    border-radius: 4px;
    background: #F2F2F2;
    padding: 48px 34px 8px;
    & > :first-child {
      position: absolute;
      display: block;
      width: 467px;
      height: 58px;
      border-radius: 4px;
      margin: 0 auto;
      padding: 0;
      top: -29px;
      left: calc(50% - 233.5px);
      text-align: center;
      line-height: 58px;
      color: var(--color-white);
      font-size: 24px;
    }
  }
  .lesson-quiz {
    position: relative;
    margin: 27.5px auto 0;
    border-radius: 4px;
    background: var(--color-blue-light);
    padding: 48px 30px 8px;
    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
  }
}

/* 移动端样式适配 */
@media screen and (max-width: 768px) {
  .lesson-quiz {
    padding: 40px 16px 8px !important;
    & > :first-child {
      width: 90% !important;
      height: 48px !important;
      left: 5% !important;
      font-size: 18px !important;
      .lessons2-lesson-resources {
        width: 100%;
        height: auto;
      }
      .lessons2-lesson-resources-info {
        width: 100% !important;
        bottom: 34px !important;
        font-size: 16px;
      }
    }
    .quiz-download-btn {
      position: absolute;
      right: 16px;
      top: 60px;
      z-index: 1;
    }
    .quiz-radio-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 40px;
      .el-radio {
        margin-right: 0;
        margin-left: 0;
        height: auto;
        line-height: 1.5;
        white-space: normal;
        span {
          line-height: 1.5;
        }
      }
    }
  }
}

.add-question-button {
  width: 100%;
  border-style: dashed;
  border-width: 1px;
  margin-top: 16px;
}
/deep/ .add-question-button > span {
  color: var(--color-primary);
}
/deep/ .add-question-button > i {
  color: var(--color-primary);
}
@media screen and (min-width:1600px) {
  .lesson-quiz {
    position: relative;
    // width: 1100px;
    margin: 27px auto 0;
    border-radius: 4px;
    background: #F2F2F2;
    padding: 48px 34px 8px;
    & > :first-child {
      position: absolute;
      display: block;
      width: 467px;
      height: 58px;
      border-radius: 4px;
      margin: 0 auto;
      padding: 0;
      top: -29px;
      left: calc(50% - 233.5px);
      text-align: center;
      line-height: 58px;
      color: var(--color-white);
      font-size: 24px;
    }
  }
  .lesson-quiz {
    position: relative;
    margin: 24px auto 0;
    border-radius: 4px;
    background: var(--color-blue-light);
    padding: 48px 34px 8px;
    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
  }
}
.lesson-quiz-window {
  margin-top: 16px;
  border-radius: 4px 4px 0 0;
}
</style>
