<template>
    <div v-if="isNotEdit" class="lesson-field-value ql-editor remove-padding content-difference">
        <div class="UDL-body">
            <!--负责切换的 tab -->
            <div class="display-flex add-padding-t-16 justify-content align-items flex-element-center w-full">
                <!--如果开启了分组，那么这里就会显示出现-->
                <div class="w-full" v-show="universalDesignForLearningClassSpecial !== '' || teacherGroups.length > 0">
                    <div class="class-specific-instructions lg-padding-16">
                        <!--可以拖拽的小孩分组  group -->
                        <div v-if="!isCurriculumPlugin" class="inclusive-learning-groups">
                            <div class="content-title">
                                <div v-show="teacherGroups.length > 1" class="add-width-5 teacher-info">
                                    <div :style="{'max-width': (100 / teacherGroups.length) + '%'}"
                                         v-for="(teacher) in teacherGroups"
                                         @click="selectTeacher(teacher)"
                                         class="group-child"
                                         :class="{'selected-teacher': teacher.teacherId === currentTeacherId}"
                                         :key="teacher.teacherId">
                                        <el-avatar
                                            :size="24"
                                            style="min-width: 24px"
                                            :src="teacher.teacherAvatar"
                                        ></el-avatar>
                                        <div class="add-width-fit-content overflow-ellipsis">
                                            {{ teacher.teacherName | formatUserName }}
                                        </div>
                                    </div>
                                </div>
                                <div v-show="teacherGroups.length === 1" class="title-text"
                                     v-html="inclusiveLearning"></div>
                                <div class="add-width-48 title-tag">
                                    <div class="display-flex justify-content-end align-items gap-12">
                                        <div class="add-width-fit-content display-flex justify-content gap-8 align-items">
                                            <span class="differentiation-title">{{ $t('loc.mixAgeGroup20') }}</span>
                                            <span>
                                                <el-switch
                                                    v-model="enableShowIEPOrELD"
                                                    @change="changeShowIEPOrELD"
                                                    unselectable="on" onselectstart="return false;"
                                                    style="-moz-user-select:none;-webkit-user-select:none;">
                                                </el-switch>
                                            </span>
                                        </div>
                                        <el-popover style="padding-right: 7px;" class="display-flex justify-content-end"
                                                    :append-to-body="false" placement="bottom-end" width="200"
                                                    trigger="click" ref="switchPopoverRef">
                                            <div class="flex-space-between">
                                                <div class="flex-row-center white-space">
                                                    <span class="popover-dot-eld"></span>
                                                    <span style="margin-left:5px;">{{
                                                            $t('loc.unitPlannerCLRAndUDLELDChildren')
                                                        }}</span>
                                                </div>
                                                <el-switch
                                                    class="defineSwitch"
                                                    v-model="showEldOpen"
                                                    @change="showEldChange"
                                                    :active-value=true
                                                    :inactive-value=false>
                                                </el-switch>
                                            </div>
                                            <div class="flex-space-between" style="padding-top: 10px;">
                                                <div class="flex-row-center white-space bottom-tip">
                                                    <span class="popover-dot-iep"></span>
                                                    <span style="margin-left:5px;">{{
                                                            $t('loc.unitPlannerCLRAndUDLIEPChildren')
                                                        }}</span>
                                                </div>
                                                <el-switch
                                                    class="defineSwitch"
                                                    v-model="showIepOpen"
                                                    @change="showIepChange"
                                                    :active-value=true
                                                    :inactive-value=false>
                                                </el-switch>
                                            </div>
                                            <el-button plain size="medium" slot="reference"><i
                                                class="lg-icon lg-icon-settings font-size-16 lg-margin-right-4"></i>{{
                                                    $t('loc.settingType')
                                                }}
                                            </el-button>
                                        </el-popover>
                                    </div>
                                </div>
                            </div>
                            <div class="content-content">
                                <div v-show="currentTeacherId === teacher.teacherId"
                                     class="teacher-group" v-for="(teacher) in teacherGroups"
                                     :key="teacher.teacherId">
                                    <!-- 分组暂时隐藏 -->
                                    <template v-if="showAIGroup">
                                        <div v-for="(group, index) in teacher.groupData" :key="group.id"
                                             :data-category-id="group.id"
                                             :class="{'remove-remove-padding-t-0': index === 0}"
                                             :data-category-info="getCategoryInfo(group.id)" class="content-group">
                                            <div class="group-title" v-html="group.label + ':'"></div>
                                            <div ref="dragHandle" class="group-child"
                                                 v-show="!enableShowIEPOrELD || child.iep || child.eld"
                                                 :data-drag-info="dragInfo(group, child)"
                                                 v-for="(child) in group.children" :key="child.id">
                                                <!-- 使用 ref 指定拖拽的手柄 -->
                                                <el-avatar
                                                    :size="24"
                                                    style="min-width: 24px"
                                                    class="drag-handle"
                                                    :src="child.avatarUrl"
                                                ></el-avatar>
                                                <div v-if="!child.iep && !child.eld">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                </div>
                                                <div v-else-if="child.iep && !child.eld"
                                                     :style="{ 'color': showIepOpen ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen"> ({{
                                                            child.symptom
                                                        }})</span>
                                                </div>
                                                <div v-else-if="!child.iep && child.eld"
                                                     :style="{ 'color': showEldOpen ? 'var(--color-eld)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        <span> (</span>
                                                        <span v-for="(item, index) in child.languages" :key="index">
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        </span>
                                                        <span>)</span>
                                                    </span>
                                                </div>
                                                <div v-else
                                                     :style="{ 'color': showIepOpen && showEldOpen ? '#BB6BD9' : (showEldOpen && !showIepOpen) ? 'var(--color-eld)' : (!showEldOpen && showIepOpen) ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen">
                                                    <span> ({{ child.symptom }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">; </span>
                                                    <span v-if="!showEldOpen"> )</span>
                                                    </span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && !showIepOpen && showEldOpen"> (</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        <span v-for="(item, index) in child.languages" :key="index">
                                                        <span v-if="index === 0 && !child.symptom"> (</span>
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        <span v-else>)</span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <!-- 所有小孩分组 -->
                                    <template v-else>
                                        <div class="teacher-children">
                                            <div ref="dragHandle" class="group-child"
                                                 v-show="!enableShowIEPOrELD || child.iep || child.eld"
                                                 v-for="(child) in teacher.children" :key="child.id">
                                                <!-- 使用 ref 指定拖拽的手柄 -->
                                                <el-avatar
                                                    :size="24"
                                                    style="min-width: 24px"
                                                    class="drag-handle"
                                                    :src="child.avatarUrl"
                                                ></el-avatar>
                                                <div v-if="!child.iep && !child.eld">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                </div>
                                                <div v-else-if="child.iep && !child.eld"
                                                     :style="{ 'color': showIepOpen ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen"> ({{
                                                            child.symptom
                                                        }})</span>
                                                </div>
                                                <div v-else-if="!child.iep && child.eld"
                                                     :style="{ 'color': showEldOpen ? 'var(--color-eld)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        <span> (</span>
                                                        <span v-for="(item, index) in child.languages" :key="index">
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        </span>
                                                        <span>)</span>
                                                    </span>
                                                </div>
                                                <div v-else
                                                     :style="{ 'color': showIepOpen && showEldOpen ? '#BB6BD9' : (showEldOpen && !showIepOpen) ? 'var(--color-eld)' : (!showEldOpen && showIepOpen) ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen">
                                                    <span> ({{ child.symptom }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">; </span>
                                                    <span v-if="!showEldOpen"> )</span>
                                                    </span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && !showIepOpen && showEldOpen"> (</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        <span v-for="(item, index) in child.languages" :key="index">
                                                        <span v-if="index === 0 && !child.symptom"> (</span>
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        <span v-else>)</span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                            <div class="bottom-tip">
                                <div class="title-tag"
                                     style="justify-content: flex-start!important;margin: 0px 15px 12px">
                                    <div class="display-flex flex-justify-start tag-css align-items" v-if="showEldOpen">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10"
                                                 viewBox="0 0 10 10" fill="none">
                                                <circle cx="5" cy="5" r="5" fill="#002EFE"/>
                                            </svg>
                                        </div>
                                        <div class="font-size-14 color-676879">
                                            <span>{{ $t('loc.unitPlannerCLRAndUDLELDChildren') }}</span>
                                        </div>
                                    </div>
                                    <div class="display-flex flex-justify-start tag-css align-items" v-if="showIepOpen">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10"
                                                 viewBox="0 0 10 10" fill="none">
                                                <circle cx="5" cy="5" r="5" fill="#FF7F41"/>
                                            </svg>
                                        </div>
                                        <div class="font-size-14 color-676879">
                                            <span>{{ $t('loc.unitPlannerCLRAndUDLIEPChildren') }}</span>
                                        </div>
                                    </div>
                                    <div class="display-flex flex-justify-start tag-css align-items"
                                         v-if="showIepOpen && showEldOpen">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10"
                                                 viewBox="0 0 10 10" fill="none">
                                                <circle cx="5" cy="5" r="5" fill="#BB6BD9"/>
                                            </svg>
                                        </div>
                                        <div class="font-size-14 color-676879">
                                            <span>{{ $t('loc.unitPlannerCLRAndUDLIEPAndELDChildren') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--For Learners with IEPs -->
                        <div class="for-learners-with-IEPs" v-show="showLearningIep">
                            <table class="learners-table domain-table"
                                   v-for="(teacher) in teacherGroups"
                                   :key="teacher.teacherId"
                                   v-show="currentTeacherId === teacher.teacherId && teacher.learningIepActivity && teacher.learningIepActivity  !== ''">
                                <thead style="background: #FFF2E1;">
                                <tr class="font-size-16 table-row">
                                    <!--th 扩展为两列-->
                                    <th colspan="2" style="text-align:center; padding-left: 15px; font-weight: 600;"
                                        v-html="forLearnersWithIEPS"></th>
                                    <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="table-row">
                                    <td style="background: #FFF2E1;">
                                        <div class="activity-description" v-html="activityDescription"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description">
                                            <div v-html="formatContetWrap(teacher.learningIepActivity)"
                                                 class="textarea-hover-padding"
                                            >
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                                <tr class="table-row">
                                    <td style="background: #FFF2E1;">
                                        <div class="activity-description" v-html="support"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description font-size-16 remove-padding">
                                            <div
                                                class="textarea-hover-padding remove-padding font-size-16"
                                            >
                                                <div class="el-textarea__inner" style="min-height: 33px; border: none"
                                                     v-html="teacher.teacherChildrenContent"></div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        <!--For English Language Learners -->
                        <div class="for-english-language-learners" v-show="showEnglishLanguage">
                            <table class="learners-table domain-table"
                                   v-for="(teacher) in teacherGroups"
                                   :key="teacher.teacherId"
                                   v-show="currentTeacherId === teacher.teacherId
                                            && teacher.classSpecificEnglishLanguageActivity && teacher.classSpecificEnglishLanguageActivity  !== ''
                                            && teacher.classSpecificEnglishLanguageSupport && teacher.classSpecificEnglishLanguageSupport != ''"
                                   style="border: 1px solid var(--color-border)">
                                <thead
                                    style="background: var(--color-page-background-white); border: 1px solid var(--color-border)">
                                <tr class="font-size-16 table-row" style="border: 1px solid var(--color-border)">
                                    <!--th 扩展为两列-->
                                    <th colspan="2"
                                        style="text-align:center; padding-left: 15px; font-weight: 600; border: 1px solid var(--color-border)"
                                        v-html="forEnglishLanguageLearners"></th>
                                    <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="table-row">
                                    <td style="background: var(--color-page-background-white);">
                                        <div class="activity-description" v-html="activityDescription"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description">
                                            <div
                                                v-html="formatContetWrap(teacher.classSpecificEnglishLanguageActivity)"
                                                class="textarea-hover-padding"
                                            >
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                                <tr class="table-row">
                                    <td style="background: var(--color-page-background-white);">
                                        <div class="activity-description" v-html="support"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description font-size-16">
                                            <div
                                                v-html="formatContetWrap(teacher.classSpecificEnglishLanguageSupport)"
                                                class="textarea-hover-padding"
                                            >
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="w-full" v-if="(showMixedAge || checkShowMixedAge) && (universalDesignForLearningClassSpecial !== '' || teacherGroups.length > 0)">
                          <!-- 混合年龄组   -->
                          <MixedAgeDifferentiation
                                                   :mixedAgeDifferentiation="mixedAgeDifferentiation"
                                                   :loading="generateUniversalDesignLoading !== 0"
                                                   :lessonId="lessonId"
                                                   :itemId="itemId"
                                                   :lessonStepIndex="lessonStepIndex"
                                                   :changeAgeGroups="changeAgeGroups"
                                                   :isNotEdit="isNotEdit"
                                                   :isAdaptedLesson="isAdaptedLesson"
                                                   :lessonAgeGroup="lessonAgeGroup"
                                                   :isWeeklyPlanEdit="isWeeklyPlanEdit"
                                                   :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                                                   :groupId="groupId"
                                                   ref="mixedAgeDifferentiation"
                                                   :getNewLessonInfo="getNewLessonInfo"
                                                   @updateMixedAgeDifferentiation="updateMixedAgeDifferentiation"/>
                        </div>
                    </div>
                </div>
                <!--如果没有开启分组-->
                <div class="w-full class-specific-instructions lg-padding-16" v-show="universalDesignForLearningClassSpecial === '' && teacherGroups.length <= 0 &&
                            (showGeneralLearningIep || showGeneralEnglishLanguage || showGeneralMixedAge)">
                    <!--For Learners with IEPs -->
                    <div
                        v-show="showGeneralLearningIep"
                        class="for-learners-with-IEPs">
                        <table class="learners-table domain-table">
                            <thead style="background: #FFF2E1;">
                            <tr class="font-size-16 table-row">
                                <!--th 扩展为两列-->
                                <th colspan="2" style="text-align:center; padding-left: 15px; font-weight: 600;"
                                    v-html="forLearnersWithIEPS"></th>
                                <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="table-row">
                                <td style="background: #FFF2E1;">
                                    <div class="activity-description" v-html="activityDescription"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description">
                                        <div v-html="formatContetWrap(generalLearningIepActivity)"
                                             class="textarea-hover-padding"
                                        >
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                            <tr class="table-row">
                                <td style="background: #FFF2E1;">
                                    <div class="activity-description" v-html="support"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description font-size-16">
                                        <div v-html="formatContetWrap(generalLearningIepSupport)"
                                             class="textarea-hover-padding"
                                        >
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <!--For English Language Learners -->
                    <div
                        v-show="showGeneralEnglishLanguage"
                        class="for-english-language-learners">
                        <table class="learners-table domain-table" style="border: 1px solid var(--color-border)">
                            <thead
                                style="background: var(--color-page-background-white); border: 1px solid var(--color-border)">
                            <tr class="font-size-16 table-row" style="border: 1px solid var(--color-border)">
                                <!--th 扩展为两列-->
                                <th colspan="2"
                                    style="text-align:center; padding-left: 15px; font-weight: 600; border: 1px solid var(--color-border)"
                                    v-html="forEnglishLanguageLearners"></th>
                                <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="table-row">
                                <td style="background: var(--color-page-background-white);">
                                    <div class="activity-description" v-html="activityDescription"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description">
                                        <div v-html="formatContetWrap(generalEnglishLanguageActivity)"
                                             class="textarea-hover-padding"
                                        >
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                            <tr class="table-row">
                                <td style="background: var(--color-page-background-white);">
                                    <div class="activity-description" v-html="support"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description font-size-16">
                                        <div v-html="formatContetWrap(generalEnglishLanguageSupport)"
                                             class="textarea-hover-padding"
                                        >
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                   <div class="w-full" v-if="(showMixedAge || checkShowMixedAge) && universalDesignForLearningClassSpecial === '' && teacherGroups.length <= 0 &&
                            (showGeneralLearningIep || showGeneralEnglishLanguage || showGeneralMixedAge)">
                      <!-- 混合年龄组   -->
                      <MixedAgeDifferentiation
                        :mixedAgeDifferentiation="mixedAgeDifferentiation"
                        :loading="generateUniversalDesignLoading !== 0"
                        :lessonId="lessonId"
                        :itemId="itemId"
                        :lessonStepIndex="lessonStepIndex"
                        :changeAgeGroups="changeAgeGroups"
                        :isNotEdit="isNotEdit"
                        :isAdaptedLesson="isAdaptedLesson"
                        :lessonAgeGroup="lessonAgeGroup"
                        :isWeeklyPlanEdit="isWeeklyPlanEdit"
                        :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                        :groupId="groupId"
                        ref="mixedAgeDifferentiation"
                        :getNewLessonInfo="getNewLessonInfo"
                        @updateMixedAgeDifferentiation="updateMixedAgeDifferentiation"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--在编辑状态下-->
    <el-card v-else shadow="never" class="border-radius-14" :body-style="bodyStyle">
        <div slot="header" class="UDL-header">
            <div class="header-title">{{ $t('loc.unitPlannerLessonUDL') }}</div>
            <div class="display-flex gap-12"
                 v-show="currentTagName === submodules[1] && adaptUDLAndCLROpen && (isWeeklyPlanEdit || unitDetail)">
                <!--Teacher Groups 按钮-->
                <div
                    v-show="showModuleButton && !isCurriculumPlugin">
                    <el-button :loading="generateUniversalDesignLoading !== 0" class="header-button"
                               icon="lg-icon lg-icon-settings" plain @click="showAssignTeacherGroups()">
                        <span class="font-size-14 font-weight-400">Teacher Groups</span>
                    </el-button>
                </div>
                <!--manager children-->
                <div
                    v-show="showModuleButton && !isCurriculumPlugin">
                    <el-button :loading="generateUniversalDesignLoading !== 0" class="header-button" plain
                               @click="manageClassRoomDemographics()">
                        <span class="font-size-14 font-weight-400">Manage Classroom Demographics</span>
                    </el-button>
                </div>
                <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top"
                            :hide-after="1000">
                    <el-button v-show="generatedUDL || isAdaptedLesson" class="regenerated-btn header-button"
                               :loading="generateUniversalDesignLoading !== 0"
                               @click="generateUniversalDesign(true)">
                        <img src="~@/assets/img/lesson2/plan/regenerate.svg" alt="">
                    </el-button>
                </el-tooltip>
            </div>
        </div>
        <div class="UDL-body">
            <!--负责切换的 tab -->
            <div class="display-flex justify-content align-items flex-element-center w-full">
                <!--如果开启了分组，那么这里就会显示出现-->
                <div class="w-full"
                     v-show="(universalDesignForLearningClassSpecial !== '' || teacherGroups.length > 0)">
                    <!--如果没有错误就展示当前数据-->
                    <div class="class-specific-instructions">
                        <!--可以拖拽的小孩分组  group -->
                        <div v-if="!isCurriculumPlugin" class="inclusive-learning-groups">
                            <div class="content-title">
                                <div v-show="teacherGroups.length > 1" class="teacher-info add-width-5">
                                    <div :style="{'max-width': (100 / teacherGroups.length) + '%'}"
                                         v-for="(teacher) in teacherGroups"
                                         @click="selectTeacher(teacher)"
                                         class="group-child"
                                         :class="{'selected-teacher': teacher.teacherId === currentTeacherId}"
                                         :key="teacher.teacherId">
                                        <el-avatar
                                            :size="24"
                                            style="min-width: 24px"
                                            :src="teacher.teacherAvatar"
                                        ></el-avatar>
                                        <div class="add-width-fit-content overflow-ellipsis"
                                             :title="teacher.teacherName | formatUserName">
                                            {{ teacher.teacherName | formatUserName }}
                                        </div>
                                    </div>
                                </div>
                                <div v-show="teacherGroups.length === 1" class="title-text"
                                     v-html="inclusiveLearning"></div>
                                <div class="add-width-48 title-tag">
                                    <div class="display-flex justify-content-end align-items gap-12">
                                        <div class="add-width-fit-content display-flex justify-content gap-8 align-items">
                                            <span class="differentiation-title">{{ $t('loc.mixAgeGroup20') }}</span>
                                            <span>
                                                <el-switch
                                                  v-model="enableShowIEPOrELD"
                                                  @change="changeShowIEPOrELD"
                                                  unselectable="on" onselectstart="return false;"
                                                  style="-moz-user-select:none;-webkit-user-select:none;">
                                                </el-switch>
                                            </span>
                                        </div>
                                        <el-popover style="padding-right: 7px;" class="display-flex justify-content-end"
                                                    :append-to-body="false" placement="bottom-end" width="200"
                                                    trigger="click" ref="switchPopoverRef">
                                            <div class="flex-space-between">
                                                <div class="flex-row-center white-space">
                                                    <span class="popover-dot-eld"></span>
                                                    <span style="margin-left:5px;">{{
                                                            $t('loc.unitPlannerCLRAndUDLELDChildren')
                                                        }}</span>
                                                </div>
                                                <el-switch
                                                    class="defineSwitch"
                                                    v-model="showEldOpen"
                                                    @change="showEldChange"
                                                    :active-value=true
                                                    :inactive-value=false>
                                                </el-switch>
                                            </div>
                                            <div class="flex-space-between" style="padding-top: 10px;">
                                                <div class="flex-row-center white-space bottom-tip">
                                                    <span class="popover-dot-iep"></span>
                                                    <span style="margin-left:5px;">{{
                                                            $t('loc.unitPlannerCLRAndUDLIEPChildren')
                                                        }}</span>
                                                </div>
                                                <el-switch
                                                    class="defineSwitch"
                                                    v-model="showIepOpen"
                                                    @change="showIepChange"
                                                    :active-value=true
                                                    :inactive-value=false>
                                                </el-switch>
                                            </div>
                                            <el-button plain size="medium" slot="reference"><i
                                                class="lg-icon lg-icon-settings font-size-16 lg-margin-right-4"></i>{{
                                                    $t('loc.settingType')
                                                }}
                                            </el-button>
                                        </el-popover>
                                    </div>
                                </div>
                            </div>
                            <div class="content-content">
                                <div v-show="currentTeacherId === teacher.teacherId"
                                     class="teacher-group" v-for="(teacher) in teacherGroups"
                                     :key="teacher.teacherId">

                                    <template v-if="showAIGroup">
                                        <div v-for="(group, index) in teacher.groupData" :key="group.id"
                                             :data-category-id="group.id"
                                             :class="{'remove-remove-padding-t-0': index === 0}"
                                             :data-category-info="getCategoryInfo(group.id)" class="content-group">
                                            <div class="group-title" v-html="group.label + ':'"></div>
                                            <div ref="dragHandle" class="group-child"
                                                 :data-drag-info="dragInfo(group, child)"
                                                 v-show="!enableShowIEPOrELD || child.iep || child.eld"
                                                 v-for="(child) in group.children" :key="child.id">
                                                <!-- 使用 ref 指定拖拽的手柄 -->
                                                <el-avatar
                                                    :size="24"
                                                    style="min-width: 24px"
                                                    class="drag-handle"
                                                    :src="child.avatarUrl"
                                                ></el-avatar>
                                                <div  v-if="!child.iep && !child.eld">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                </div>
                                                <div v-else-if="child.iep && !child.eld"
                                                     :style="{ 'color': showIepOpen ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen"> ({{
                                                            child.symptom
                                                        }})</span>
                                                </div>
                                                <div v-else-if="!child.iep && child.eld"
                                                     :style="{ 'color': showEldOpen ? 'var(--color-eld)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        (<span v-for="(item, index) in child.languages" :key="index">
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        </span>)
                                                    </span>
                                                </div>
                                                <div v-else
                                                     :style="{ 'color': showIepOpen && showEldOpen ? '#BB6BD9' : (showEldOpen && !showIepOpen) ? 'var(--color-eld)' : (!showEldOpen && showIepOpen) ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen">
                                                    <span> ({{ child.symptom }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">; </span>
                                                    <span v-if="!showEldOpen">)</span>
                                                    </span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && !showIepOpen && showEldOpen"> (</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        <span v-for="(item, index) in child.languages" :key="index">
                                                        <span v-if="index === 0 && !child.symptom"> (</span>
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        <span v-else>)</span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div class="teacher-children">
                                            <div ref="dragHandle" v-for="(child, index) in teacher.children"
                                                 v-show="!enableShowIEPOrELD || child.iep || child.eld"
                                                 :key="index" class="group-child">
                                                <!-- 使用 ref 指定拖拽的手柄 -->
                                                <el-avatar
                                                    :size="24"
                                                    style="min-width: 24px"
                                                    class="drag-handle"
                                                    :src="child.avatarUrl"
                                                ></el-avatar>
                                                <div v-if="!child.iep && !child.eld">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                </div>
                                                <div v-else-if="child.iep && !child.eld"
                                                     :style="{ 'color': showIepOpen ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen"> ({{
                                                            child.symptom
                                                        }})</span>
                                                </div>
                                                <div v-else-if="!child.iep && child.eld"
                                                     :style="{ 'color': showEldOpen ? 'var(--color-eld)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        (<span v-for="(item, index) in child.languages" :key="index">
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        </span>)
                                                    </span>
                                                </div>
                                                <div v-else
                                                     :style="{ 'color': showIepOpen && showEldOpen ? '#BB6BD9' : (showEldOpen && !showIepOpen) ? 'var(--color-eld)' : (!showEldOpen && showIepOpen) ? 'var(--color-iep)' : '' }">
                                                    <span :style="{ 'font-weight': getFontWeight(child) }">{{
                                                            child.label
                                                        }}</span>
                                                    <span v-if="child.symptom && showIepOpen">
                                                    <span> ({{ child.symptom }}</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">; </span>
                                                    <span v-if="!showEldOpen">)</span>
                                                    </span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && !showIepOpen && showEldOpen"> (</span>
                                                    <span
                                                        v-if="child.languages && child.languages.length > 0 && showEldOpen">
                                                        <span v-for="(item, index) in child.languages" :key="index">
                                                        <span v-if="index === 0 && !child.symptom"> (</span>
                                                        <span>{{ item }}</span>
                                                        <span v-if="index !== child.languages.length - 1">, </span>
                                                        <span v-else>)</span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                                <div class="display-flex align-items justify-content-between w-full bottom-tip">
                                    <span v-show="showAIGroup" class="display-inline-block w-full">{{
                                            $t('loc.unitPlannerUDLDragTitle')
                                        }}</span>
                                    <!--标签-->
                                    <div class="title-tag w-full">
                                        <div class="display-flex flex-justify-start tag-css align-items"
                                             v-if="showEldOpen">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10"
                                                     viewBox="0 0 10 10" fill="none">
                                                    <circle cx="5" cy="5" r="5" fill="#002EFE"/>
                                                </svg>
                                            </div>
                                            <div>
                                                {{ $t('loc.unitPlannerCLRAndUDLELDChildren') }}
                                            </div>
                                        </div>
                                        <div class="display-flex flex-justify-start tag-css align-items"
                                             v-if="showIepOpen">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10"
                                                     viewBox="0 0 10 10" fill="none">
                                                    <circle cx="5" cy="5" r="5" fill="#FF7F41"/>
                                                </svg>
                                            </div>
                                            <div>{{ $t('loc.unitPlannerCLRAndUDLIEPChildren') }}</div>
                                        </div>
                                        <div class="display-flex flex-justify-start tag-css align-items"
                                             v-if="showIepOpen && showEldOpen">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10"
                                                     viewBox="0 0 10 10" fill="none">
                                                    <circle cx="5" cy="5" r="5" fill="#BB6BD9"/>
                                                </svg>
                                            </div>
                                            <div>{{ $t('loc.unitPlannerCLRAndUDLIEPAndELDChildren') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--For Learners with IEPs -->
                        <div v-show="hasIEP" class="for-learners-with-IEPs">
                            <table class="learners-table domain-table"
                                   v-for="(teacher) in teacherGroups"
                                   :key="teacher.teacherId"
                                   v-show="currentTeacherId === teacher.teacherId">
                                <thead style="background: #FFF2E1;">
                                <tr class="font-size-16 table-row">
                                    <!--th 扩展为两列-->
                                    <th colspan="2" style="text-align:center; padding-left: 15px; font-weight: 600;"
                                        v-html="forLearnersWithIEPS"></th>
                                    <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="table-row">
                                    <td style="background: #FFF2E1;">
                                        <div class="activity-description" v-html="activityDescription"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description">
                                            <el-skeleton :rows="3" animated
                                                         class="textarea-hover-padding-skeleton w-full"
                                                         :loading="generateUniversalDesignLoading !== 0 && !(teacher.learningIepActivity && teacher.learningIepActivity  !== '')">
                                                <template>
                                                    <el-input
                                                        ref="autosizeInput"
                                                        type="textarea"
                                                        placeholder="Describe how the activity can be adapted to meet the needs of IEP children here."
                                                        autosize
                                                        @input="updateUniversalDesignForLearningGroup"
                                                        v-model="teacher.learningIepActivity">
                                                    </el-input>
                                                </template>
                                            </el-skeleton>
                                        </div>
                                    </td>
                                </tr>
                                <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                                <tr class="table-row">
                                    <td style="background: #FFF2E1;">
                                        <div class="activity-description" v-html="support"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description font-size-14 remove-padding">
                                            <el-skeleton :rows="3" animated
                                                         class="textarea-hover-padding-skeleton w-full"
                                                         :loading="generateUniversalDesignLoading !== 0 && teacherChildrenData(teacher) === ''">
                                                <template>
                                                    <div
                                                        :id="'teacherChildrenData' + teacher.teacherId"
                                                        class="el-textarea font-size-14 lg-no-toolbar-editor teacherChildrenContent"
                                                        type="textarea"
                                                        autosize>
                                                        <!--<div class="el-textarea__inner"-->
                                                        <!--     style="min-height: 33px; "-->
                                                        <!--     v-html="teacherChildrenData(teacher)"></div>-->
                                                        <editor v-model="teacher.teacherChildrenContent"
                                                                @updateUniversalDesignForLearningGroup="updateUniversalDesignForLearningGroup"
                                                                @input="iepChildrenContentChange"
                                                                placeholder="Specify any support strategies, resources, or materials to aid in the learning of IEP children."/>
                                                    </div>
                                                </template>
                                            </el-skeleton>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        <!--For English Language Learners -->
                        <div v-show="hasELD" class="for-english-language-learners">
                            <table class="learners-table domain-table"
                                   v-for="(teacher) in teacherGroups"
                                   :key="teacher.teacherId"
                                   v-show="currentTeacherId === teacher.teacherId"
                                   style="border: 1px solid var(--color-border)">
                                <thead
                                    style="background: var(--color-page-background-white); border: 1px solid var(--color-border)">
                                <tr class="font-size-16 table-row" style="border: 1px solid var(--color-border)">
                                    <!--th 扩展为两列-->
                                    <th colspan="2"
                                        style="text-align:center; padding-left: 15px; font-weight: 600; border: 1px solid var(--color-border)"
                                        v-html="forEnglishLanguageLearners"></th>
                                    <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="table-row">
                                    <td style="background: var(--color-page-background-white);">
                                        <div class="activity-description" v-html="activityDescription"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description">
                                            <el-skeleton :rows="3" animated
                                                         class="textarea-hover-padding-skeleton w-full"
                                                         :loading="generateUniversalDesignLoading !== 0 && !(teacher.classSpecificEnglishLanguageActivity && teacher.classSpecificEnglishLanguageActivity  !== '')">
                                                <template>
                                                    <el-input
                                                        ref="autosizeInput"
                                                        type="textarea"
                                                        autosize
                                                        @input="updateUniversalDesignForLearningGroup"
                                                        placeholder="Describe how the activity can be adapted to meet the needs of ELD children here."
                                                        v-model="teacher.classSpecificEnglishLanguageActivity">
                                                    </el-input>
                                                </template>
                                            </el-skeleton>
                                        </div>
                                    </td>
                                </tr>
                                <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                                <tr class="table-row">
                                    <td style="background: var(--color-page-background-white);">
                                        <div class="activity-description" v-html="support"></div>
                                    </td>
                                    <td class="w-full">
                                        <div class="content-description font-size-14">
                                            <el-skeleton :rows="3" animated
                                                         class="textarea-hover-padding-skeleton w-full"
                                                         :loading="generateUniversalDesignLoading !== 0 && !(teacher.classSpecificEnglishLanguageSupport && teacher.classSpecificEnglishLanguageSupport  !== '')">
                                                <template>
                                                    <el-input
                                                        ref="autosizeInput"
                                                        type="textarea"
                                                        placeholder="Specify any support strategies, resources, or materials to aid in the learning of ELD children."
                                                        autosize
                                                        @input="updateUniversalDesignForLearningGroup"
                                                        v-model="teacher.classSpecificEnglishLanguageSupport">
                                                    </el-input>
                                                </template>
                                            </el-skeleton>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--空数据提示-->
                        <div v-show="currentTeacherErrorTitle !== ''" class="w-full display-flex justify-content">
                            <div style="text-align: center; color:#111c1c;margin-top:12px;">{{
                                currentTeacherErrorTitle
                              }}
                            </div>
                        </div>
                        <div class="w-full" v-if="(showMixedAge || checkShowMixedAge) && (universalDesignForLearningClassSpecial !== '' || teacherGroups.length > 0)">
                          <!-- 混合年龄组   -->
                          <MixedAgeDifferentiation
                            :mixedAgeDifferentiation="mixedAgeDifferentiation"
                            :lessonId="lessonId"
                            :loading="generateUniversalDesignLoading !== 0"
                            :itemId="itemId"
                            :lessonStepIndex="lessonStepIndex"
                            :changeAgeGroups="changeAgeGroups"
                            :isNotEdit="isNotEdit"
                            :isAdaptedLesson="isAdaptedLesson"
                            :lessonAgeGroup="lessonAgeGroup"
                            :isWeeklyPlanEdit="isWeeklyPlanEdit"
                            :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                            :groupId="groupId"
                            ref="mixedAgeDifferentiation"
                            :getNewLessonInfo="getNewLessonInfo"
                            @updateMixedAgeDifferentiation="updateMixedAgeDifferentiation"/>
                        </div>
                        <!--每一个老师都应该有对应的 feedback-->
                        <FeedbackForm :defaultFeedbackResult="defaultFeedbackResult"
                                      :feedbackStyle="setFeedbackStyle"
                                      :showFeedback="showFeedback"
                                      :showIcon="true"
                                      :showClose="false"
                                      :feedbackTitle="feedbackTitle"
                                      :feedbackSubmit="feedbackSubmit"
                                      :needFeedbackLevel="false"
                                      :feedbackInputPlaceholder="feedbackInputPlaceholder"
                                      :promptUsageRecordIds="promptUsageRecordIds"
                                      :showReminders="false"
                                      @clickFeedback="clickFeedback"/>
                    </div>
                </div>
                <!--如果没有开启分组-->
                <div
                    v-show="universalDesignForLearningClassSpecial === '' && teacherGroups.length <= 0"
                    class="class-specific-instructions">
                    <!--For Learners with IEPs -->
                    <div class="for-learners-with-IEPs">
                        <table class="learners-table domain-table">
                            <thead style="background: #FFF2E1;">
                            <tr class="font-size-16 table-row">
                                <!--th 扩展为两列-->
                                <th colspan="2" style="text-align:center; padding-left: 15px; font-weight: 600;"
                                    v-html="forLearnersWithIEPS"></th>
                                <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="table-row">
                                <td style="background: #FFF2E1;">
                                    <div class="activity-description" v-html="activityDescription"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description">
                                        <el-input v-model="generalLearningIepActivity"
                                                  class="textarea-hover-padding"
                                                  placeholder="Describe how the activity can be adapted to meet the needs of IEP children here."
                                                  :autosize="{ minRows: 3}" resize="none" type="textarea"
                                                  @input="updateUniversalDesignForLearningGroup"
                                                  :maxlength="10000"/>
                                    </div>
                                </td>
                            </tr>
                            <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                            <tr class="table-row">
                                <td style="background: #FFF2E1;">
                                    <div class="activity-description" v-html="support"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description font-size-14">
                                        <el-input v-model="generalLearningIepSupport"
                                                  class="textarea-hover-padding"
                                                  placeholder="Specify any support strategies, resources, or materials to aid in the learning of IEP children."
                                                  :autosize="{ minRows: 3}" resize="none" type="textarea"
                                                  @input="updateUniversalDesignForLearningGroup"
                                                  :maxlength="10000"/>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <!--For English Language Learners -->
                    <div class="for-english-language-learners">
                        <table class="learners-table domain-table" style="border: 1px solid var(--color-border)">
                            <thead
                                style="background: var(--color-page-background-white); border: 1px solid var(--color-border)">
                            <tr class="font-size-16 table-row" style="border: 1px solid var(--color-border)">
                                <!--th 扩展为两列-->
                                <th colspan="2"
                                    style="text-align:center; padding-left: 15px; font-weight: 600; border: 1px solid var(--color-border)"
                                    v-html="forEnglishLanguageLearners"></th>
                                <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="table-row">
                                <td style="background: var(--color-page-background-white);">
                                    <div class="activity-description" v-html="activityDescription"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description">
                                        <el-input v-model="generalEnglishLanguageActivity"
                                                  class="textarea-hover-padding"
                                                  placeholder="Describe how the activity can be adapted to meet the needs of ELD children here."
                                                  @input="updateUniversalDesignForLearningGroup"
                                                  :autosize="{ minRows: 3}" resize="none" type="textarea"
                                                  :maxlength="10000"/>
                                    </div>
                                </td>
                            </tr>
                            <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                            <tr class="table-row">
                                <td style="background: var(--color-page-background-white);">
                                    <div class="activity-description" v-html="support"></div>
                                </td>
                                <td class="w-full">
                                    <div class="content-description font-size-14">
                                        <el-input v-model="generalEnglishLanguageSupport"
                                                  class="textarea-hover-padding"
                                                  placeholder="Specify any support strategies, resources, or materials to aid in the learning of ELD children."
                                                  :autosize="{ minRows: 3}" resize="none" type="textarea"
                                                  @input="updateUniversalDesignForLearningGroup"
                                                  :maxlength="10000"/>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="w-full" v-if="(showMixedAge || checkShowMixedAge) && universalDesignForLearningClassSpecial === '' && teacherGroups.length <= 0">
                      <!-- 混合年龄组   -->
                      <MixedAgeDifferentiation
                        :mixedAgeDifferentiation="mixedAgeDifferentiation"
                        :loading="generateUniversalDesignLoading !== 0"
                        :lessonId="lessonId"
                        :itemId="itemId"
                        :lessonStepIndex="lessonStepIndex"
                        :changeAgeGroups="changeAgeGroups"
                        :isNotEdit="isNotEdit"
                        :isAdaptedLesson="isAdaptedLesson"
                        :lessonAgeGroup="lessonAgeGroup"
                        :isWeeklyPlanEdit="isWeeklyPlanEdit"
                        :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                        :groupId="groupId"
                        ref="mixedAgeDifferentiation"
                        :getNewLessonInfo="getNewLessonInfo"
                        @updateMixedAgeDifferentiation="updateMixedAgeDifferentiation"/>
                    </div>
                </div>
            </div>
        </div>
        <!--Assign Teacher Groups-->
        <AssignTeacherGroups ref="assignTeacherGroups"></AssignTeacherGroups>
    </el-card>
</template>

<script>
import regenerateIcon from '@/assets/img/lesson2/plan/regenerate.svg'
import generateDefaultIcon from '@/assets/img/lesson2/assistant/generate.png'
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import AssignTeacherGroups from '@/views/modules/lesson2/lessonPlan/components/AssignTeacherGroups'
import MixedAgeDifferentiation from '@/views/modules/lesson2/lessonLibrary/editor/MixedAgeDifferentiation'
import Sortable from 'sortablejs/modular/sortable.core.esm.js'
import {mapState} from 'vuex'
import {createEventSource, parseStreamData, removeUnexpectedCharacters} from '@/utils/eventSource'
import {formatUserName} from '@/filters'
import LessonApi from '@/api/lessons2'
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import tools from '@/utils/tools';

export default {
    name: 'UniversalDesignLearning',
    components: {Editor, FeedbackForm, AssignTeacherGroups, MixedAgeDifferentiation},
    props: {
        universalDesignForLearning: {
            type: String,
            default: ''
        },
        showMixedAge: {
            type: Boolean,
            default: false
        },
        mixedAgeDifferentiation: {
            type: String,
            default: ''
        },
        universalDesignForLearningClassSpecial: {
            type: String,
            default: ''
        }, // 课程中的分类的 UDL 数据
        isNotEdit: {
            type: Boolean,
            default: false
        },
        lessonId: {
            type: String,
            default: ''
        },
        // 当前展示的课程的 itemId
        itemId: {
            type: String,
            default: ''
        },
        // 当年龄组发生改变的时候
        changeAgeGroups: {
            type: Boolean,
            default: false
        },
        lessonStepIndex: {
            type: Number,
            default: 0
        },
        lessonAgeGroup: {
            type: String,
            default: ''
        },
        isWeeklyPlanEdit: {
            type: Boolean,
            default: true
        },
        adaptUDLAndCLROpen: {
            type: Boolean,
            default: false
        },
        getNewLessonInfo: {
            type: Function,
            default: () => {
            }
        },
        isAdaptedLesson: {
            type: Boolean,
            default: false
        },
        universalDesignForLearningCopy: {
          type: String,
          default: ''
        },
        universalDesignForLearningClassSpecialCopy: {
          type: String,
          default: ''
        },
    },
    data() {
        return {
            currentTagName: '', // 当前模块名称
            currentTeacherId: '', // 当前老师的 ID
            generalLearningIepActivity: '', // 通用的 IEP 学习活动
            generalLearningIepSupport: '', // 通用的 IEP 支持
            generalEnglishLanguageActivity: '', // 通用的英语语言活动
            generalEnglishLanguageSupport: '', // 通用的英语语言支持
            checkShowMixedAge: false, // 通过后台的检测是否可以显示混合年龄组
            clearMixAgeData: false, // 是否清除混合年龄组的数据，如果用户关闭了按钮，同时点击生成，那么此时需要清除混合年龄组的数据
            lesson: {
                universalDesignForLearning: [], // 每一个老师要生成的 UDL 分组内容
                universalDesignForLearningStream: [] // 每一个老师要生成的 UDL IEP 等内容
            }, // 生成的课程信息
            generateUniversalDesignLoading: 0, // 生成 UDL 数据的按钮的 loading 状态
            generatedUDL: false, // 生成 UDL 数据的完成
            teacherGroups: [], // 老师的分组信息
            showEldOpen: true, // 是否展示 eld 的信息
            showIepOpen: true, // 是否展示 iep 的信息
            defaultFeedbackResult: {
                feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
                feedBackResult: '',
                feedbackData: {
                    feedback: undefined,
                    feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
                    feedbackLevel: [0, 0, 0, 0]
                }
            }, // 默认的反馈
            enableShowIEPOrELD: true, // 是否仅显示 IEP 或者 ELD 的小孩
            promptUsageRecordIds: [], // 消息回调的 promptUsageRecordId，后续用于反馈
            generateUniversalDesignForLearningLoading: [], // 为了保证每一个老师的生成 UDL 数据的 loading 状态，不要一个老师生成多份数据
            selectGroupId: undefined, // 选择的班级 ID
            triggerErrorTitle: false, // 触发错误提示
            triggerShowMixedAge: false, // 触发显示混合年龄组
            groupErrorMap: new Map(), // 老师分组的错误信息
            leavePage: false, // 是否离开页面
            toAdapt: false, // 当前是否是去改编
        }
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser,
            hasIEPChild: state => state.lesson.hasIEPChild,
            hasELDChild: state => state.lesson.hasELDChild,
            isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
            showAIGroup: state => state.lesson.showAIGroup // 是否展示 AI 分组
        }),
        // 当前老师的错误提示
        currentTeacherErrorTitle() {
          // eslint-disable-next-line no-unused-expressions
            this.triggerErrorTitle
            // 如果 currentTeacherId 是存在的，并且 groupErrorMap 也是存在的，那么就返回对应的错误信息
            if (this.currentTeacherId && this.groupErrorMap) {
                return this.groupErrorMap.get(this.currentTeacherId) || ''
            }
            return ''
        },
        // unit 详情改编课程时显示操作栏
        unitDetail() {
            return this.$route.name === 'unitDetail' || (this.$route.name === 'unit-detail-cg' && this.isCurriculumPlugin)
        },
        // 是否展示对应的功能模块的按钮
        showModuleButton() {
            // 如果老师分组的长度大于 1 并且当前模块名称是第二个模块，并且开启了 UDL 和 CLR 并且生成了 UDL 数据
            return this.currentTagName === this.submodules[1] && this.adaptUDLAndCLROpen && (this.generatedUDL || this.isAdaptedLesson)
        },
        // 全部模块名称
        submodules() {
            return [this.$t('loc.unitPlannerCLRAndUDLGeneralInstructions'), this.$t('loc.unitPlannerCLRAndUDLClass-specificInstructions')]
        },
        feedbackTitle() {
            return this.$t('loc.unitPlannerFeedback')
        },
        feedbackInputPlaceholder() {
            return this.$t('loc.unitPlannerFeedbackPlaceholder')
        },
        feedbackSubmit() {
            return this.$t('loc.unitPlannerFeedbackSubmit')
        },
        bodyStyle() {
            return {
                display: 'flex',
                padding: '16px 16px 8px 16px',
                flexDirection: 'column',
                alignItems: 'flex-start',
                marginTop: '-16px',
                alignSelf: 'stretch',
                borderRadius: '12px',
                border: '1px solid var(--dcdfe-6, #DCDFE6)',
                background: '#FFF'
            }
        },
        // 拼接普通的 UDL 数据
        generalUniversalDesignForLearning() {
            // 获取 generalLearningIepActivity
            const generalLearningIepActivity = this.generalLearningIepActivity ? this.generalLearningIepActivity.replace('Support', 'TEMP_SUPPORT') : this.generalLearningIepActivity
            // 获取 generalLearningIepSupport
            const generalLearningIepSupport = this.generalLearningIepSupport
            // 获取 generalEnglishLanguageActivity
            const generalEnglishLanguageActivity = this.generalEnglishLanguageActivity ? this.generalEnglishLanguageActivity.replace('Support', 'TEMP_SUPPORT') : this.generalEnglishLanguageActivity
            // 获取 generalEnglishLanguageSupport
            const generalEnglishLanguageSupport = this.generalEnglishLanguageSupport
            // 任意一个值不为空的时候拼接数据
            if (!!generalLearningIepActivity || !!generalLearningIepSupport || !!generalEnglishLanguageActivity || !!generalEnglishLanguageSupport) {
                if (this.kToGrade12) {
                     return `For Learners with IEPs \n\nActivity Description: ${generalLearningIepActivity} \n\nSupport: ${generalLearningIepSupport} \n\nFor English Language Learners: \n\nActivity Description: ${generalEnglishLanguageActivity} \n\nSupport: ${generalEnglishLanguageSupport}`
                } else {
                    return `For Learners with IEPs \n\nActivity Description: ${generalLearningIepActivity} \n\nSupport: ${generalLearningIepSupport} \n\nFor Dual Language Learners: \n\nActivity Description: ${generalEnglishLanguageActivity} \n\nSupport: ${generalEnglishLanguageSupport}`
                }
            }
            return ''
        },
      /**
       * 判断是否展示 IEP 数据
       * @returns {boolean} 是否展示 IEP 数据
       */
        showLearningIep() {
            return this.teacherGroups && this.teacherGroups.length > 0 && this.teacherGroups.some(teacher => teacher.learningIepActivity && teacher.learningIepActivity  !== '')
        },
        /**
         * 判断是否展示英语语言数据
         * @returns {boolean}
         */
        showEnglishLanguage() {
            return this.teacherGroups && this.teacherGroups.length > 0 && this.teacherGroups.some(teacher => teacher.classSpecificEnglishLanguageActivity && teacher.classSpecificEnglishLanguageActivity !== ''
              && teacher.classSpecificEnglishLanguageSupport && teacher.classSpecificEnglishLanguageSupport != '')
        },
        // 判断解析的数据是否存在数据
        showGeneralLearningIep() {
            return this.generalLearningIepActivity && this.generalLearningIepActivity !== '' && this.generalLearningIepSupport && this.generalLearningIepSupport !== ''
        },
        // 判断解析的数据是否存在数据
        showGeneralEnglishLanguage() {
            return this.generalEnglishLanguageActivity && this.generalEnglishLanguageActivity !== '' && this.generalEnglishLanguageSupport && this.generalEnglishLanguageSupport !== ''
        },
        // 判断是否有混合年龄组数据
        showGeneralMixedAge() {
          // eslint-disable-next-line no-unused-expressions
            this.triggerShowMixedAge
            const differentiation = this.$refs.mixedAgeDifferentiation;
            if (differentiation) {
              return differentiation.mixedAgeDifferentiations && differentiation.mixedAgeDifferentiations.length > 0 && differentiation.mixedAgeDifferentiations.some(item => item.activity !== '' || item.support !== '')
            }
            return true
        },
        currentUserId() {
            if (!this.currentUser) {
                return ''
            }
            return this.currentUser.user_id
        },
        // 判断是否拥有 IEP 小孩，如果没有明确表明没有 IEP 小孩，那么就返回 true
        hasIEP() {
            if (typeof this.hasIEPChild === 'undefined') {
                return true
            }
            // 拥有 IEP 小孩并且错误是没有的情况下，才返回 true
            return this.hasIEPChild && this.currentTeacherErrorTitle === ''
        },
        // 判断是否拥有 ELD 小孩，如果没有明确表明没有 ELD 小孩，那么就返回 true
        hasELD() {
            if (typeof this.hasELDChild === 'undefined') {
                return true
            }
            // 拥有 ELD 小孩并且错误是没有的情况下，才返回 true
            return this.hasELDChild && this.currentTeacherErrorTitle === ''
        },
        /**
         * 判断是否是老师
         */
        canAdapter() {
            const {role2 = ''} = this.currentUser || {}
            let role = role2.toUpperCase()
            return role === 'COLLABORATOR' || role === 'TEACHING_ASSISTANT' || role === 'TEACHER' || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
        },
        // 内容换行符替换
        formatContetWrap() {
            return function (content) {
                if (!content) {
                    return ''
                }
                return content.replace(/\n/g, '<br/>')
            }
        },
        groupId: {
            get() {
                if (this.selectGroupId && this.selectGroupId !== '') {
                    return this.selectGroupId
                }
                // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
                const selectedGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
                if (selectedGroupId) {
                    return selectedGroupId
                }
            },
            set(val) {
                this.selectGroupId = val
            }
        },
        centerId() {
            // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
            const selectedCenterId = sessionStorage.getItem('selectedCenterId' + this.currentUserId)
            if (selectedCenterId) {
                return selectedCenterId
            }
        },
        forLearnersWithIEPS() {
            return this.$t('loc.unitPlannerLessonForLearnersIEPs')
        },
        activityDescription() {
            return this.$t('loc.activityDescription')
        },
        forEnglishLanguageLearners() {
            if (this.kToGrade12) {
                return this.$t('loc.unitPlannerLessonForEnglishLanguageLearners')
            } else {
                return this.$t('loc.unitPlannerLessonForDualLanguageLearners')
            }
        },
        support() {
            return this.$t('loc.unitPlannerLessonSupport')
        },
        inclusiveLearning() {
            return this.showAIGroup ? this.$t('loc.unitPlannerCLRAndUDLInclusiveLearningGroups') : this.$t('loc.unitPlannerCLRAndUDLGroupInfo')
        },
        showFeedback() {
            // 判断 promptUsageRecordIds 中的每一个值，即对应的子数组都是存在值的，同时要保证没有错误的时候才展示反馈
            return this.generatedUDL && this.generateUniversalDesignLoading === 0 && this.promptUsageRecordIds.length > 0 && this.currentTeacherErrorTitle === ''
        },
        // 年龄组是否是 K - Grade 12
        kToGrade12 () {
            if (this.lessonAgeGroup) {
                return ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'].includes(this.lessonAgeGroup)
            }
            return false
        }
    },
    beforeDestroy() {
      // 当组件被销毁的时候清理掉 New
      if (this.mixedAgeGroup() !== '') {
        // 当组件被销毁的时候清理掉 New
        sessionStorage.setItem('mixedAgeDifferentiation' + this.currentUserId, 'true');
      }
    },
    destroyed () {
      this.leavePage = true
    },
    methods: {
        // 当年龄组数据发生变化的时候
        updateMixedAgeDifferentiation() {
           this.triggerShowMixedAge = !this.triggerShowMixedAge
        },
        mixedAgeGroup() {
            // 定义函数
            const mixedAgeDifferentiations = this.$refs.mixedAgeDifferentiation && this.$refs.mixedAgeDifferentiation.mixedAgeDifferentiations;
            if (mixedAgeDifferentiations && mixedAgeDifferentiations.length > 0
                && mixedAgeDifferentiations.some(item => item.ageGroup !== '' || item.activity !== '' || item.support !== '')) {
                return JSON.stringify(mixedAgeDifferentiations);
            }
            return '';
        },
        // UDL eld 内容修改触发的回调
        iepChildrenContentChange() {
            this.teacherGroups.forEach(teacher => {
                // 如果老师的 ID 和当前老师的 ID 相同，那么就更新老师的数据
                if (teacher.teacherId === this.currentTeacherId) {
                    // 如果内容被清空，把小孩信息也清空
                    if (!teacher.teacherChildrenContent) {
                        teacher.iepChildren = []
                    }
                }
            })
        },
        // UDL 数据修改触发的回调
        updateUniversalDesignForLearningGroup() {
            // UDL 数据修改，向上一级组件发送修改后的数据，确保导入年龄组的信息的 UDL 是改变之后的
            if (this.teacherGroups && this.teacherGroups.length > 0) {
                this.$emit('updateUniversalDesignForLearningClassSpecial', JSON.stringify(this.teacherGroups), this.lessonAgeGroup, this.mixedAgeGroup())
            } else {
                this.$emit('updateUniversalDesignForLearning', this.generalUniversalDesignForLearning, this.lessonAgeGroup, this.mixedAgeGroup())
            }
        },
        // 获取小孩字体样式
        getFontWeight(child) {
            // 获取当前小孩是否是 eld 或 iep 小孩
            const isIepOrEld = child.iep || child.eld
            // 如果不是 iep 或 eld 小孩，那么就不加粗
            if ((!isIepOrEld || (!this.showIepOpen && !this.showEldOpen)) && !(this.showIepOpen || this.showEldOpen)) {
                return '400' // 不加粗
            } else if (isIepOrEld) {
                if (this.showIepOpen && child.iep) {
                    return '600' // 加粗，IEP开关打开且小孩具有IEP属性
                } else if (this.showEldOpen && child.eld) {
                    return '600' // 加粗，ELD开关打开且小孩具有ELD属性
                } else {
                    return '400' // 不加粗，其他情况
                }
            } else {
                return '400' // 不加粗，其他情况
            }
        },
        handleScroll() {
            // 延时 3ms 重新计算 popover 位置
            setTimeout(() => {
                this.$refs.switchPopoverRef && this.$refs.switchPopoverRef.updatePopper()
            }, 300)
        },
        // 记录每组中的 IEP 和 ELD 的生成状态
        updateTeacherIEPAndELDStatus() {
            // 在 this.teacherGroups 中遍历每一个老师，如果老师的 learningIepActivity 或者 iepChildren 不为空，
            // 那么就为老师设置 hasIEPData 属性为 true,
            // 否则设置为 false；如果老师的 classSpecificEnglishLanguageActivity 或者 classSpecificEnglishLanguageSupport
            // 就设置  hasELDData 属性为 true,否则设置为 false
            this.teacherGroups.forEach(teacher => {
                // 使用 $set 设置
                teacher.hasIEPData = teacher.learningIepActivity || teacher.iepChildren.length > 0
                teacher.hasELDData = teacher.classSpecificEnglishLanguageActivity || teacher.classSpecificEnglishLanguageSupport
            })
        },
        // 改变 iep 值
        showIepChange(val) {
            this.showIepOpen = val
        },
        // 改变 eld 值
        showEldChange(val) {
            this.showEldOpen = val
        },
        /**
        * 改变仅仅展示 IEP 或者 ELD 的小孩
        */
        changeShowIEPOrELD(val) {
          this.enableShowIEPOrELD = val
          if (val) {
            sessionStorage.removeItem('enableShowIEPOrELD' + this.currentUserId)
          } else {
            sessionStorage.setItem('enableShowIEPOrELD' + this.currentUserId, val)
          }
        },
        // 格式化用户的名称
        formatUserName() {
            return formatUserName
        },
        // 获取当前老师下面的所有 IEP 的小孩，这些小孩是要展示在 iep support 下面的
        teacherChildrenData(teacher) {
            // 定义默认返回值
            // 如果没有 iep 小孩，或者 iep 小孩是空的
            if (!teacher.iepChildren || teacher.iepChildren.length === 0) {
                return ''
            }
            // 将 teacher 中的所有的 iepChildren 收集起来
            // 小孩名字左右两边为加粗并且 font-size 为 14px，小孩名之后添加 : 与小孩的 description 拼接在一起，
            // 每个孩子之间使用 <br> 换行,如果对应的 child.description，那么这一行就不需要添加了
            const iepChildElements = teacher.iepChildren && teacher.iepChildren.map((child, index) => {
                // 如果 child.description 存在，那么就返回对应的小孩名和小孩描述
                if (child && child.description) {
                    // 创建一个 div 标签，设置类名为 child-container
                    const childContainer = document.createElement('div')
                    childContainer.className = 'child-container'

                    // 创建一个 span 标签，设置类名为 child-label，设置样式为 font-weight: 600; font-size: 14px;
                    const labelSpan = document.createElement('strong')
                    labelSpan.className = 'child-label'
                    labelSpan.style.fontWeight = '600'
                    // 设置小孩名
                    labelSpan.innerText = this.isCurriculumPlugin ? `` : `${child.label}: `
                    // 设置 data-child-id 属性
                    labelSpan.setAttribute('data-child-id', child.id) // 添加 data-child-id 属性

                    // 创建一个 span 标签，设置类名为 child-description，设置样式为 font-size: 14px;
                    const descriptionSpan = document.createElement('span')
                    descriptionSpan.className = 'child-description'
                    descriptionSpan.innerText = child.description

                    // 将 labelSpan 和 descriptionSpan 放入 childContainer 中，并返回出去
                    childContainer.appendChild(labelSpan)
                    childContainer.appendChild(descriptionSpan)
                    return childContainer
                }
                return null
            })

            // 定义 iep 结果 iepChildElements 的元素拼接 <br>
            const iepChildData = iepChildElements && iepChildElements.map(child => {
                return child ? child.outerHTML : ''
            }).join('')

            // 为 iepChildData 添加外层组件 并添加类 el-textarea__inner 和 样式 min-height: 33px; height: 75px;
            if (iepChildData) {
                // 为 teacherChildrenContent 赋值
                teacher.teacherChildrenContent = iepChildData
                return iepChildData
            }
            // 如果 iepChildData 不存在，就直接返回空串
            return ''
        },
        // 当前老师的小孩信息被修改的时候，通过反向解析字符串将对应的 teacher 下的 iepChildren 中的数据进行修改
        changeTeacherChildrenData(teacher) {
            // 如果 teacher 不存在，就直接返回
            if (!teacher || !teacher.iepChildren || teacher.iepChildren.length === 0) {
                return
            }
            // 拼接 'teacherChildrenData' + teacher.teacherId，获取指定的 div
            const el = document.getElementById('teacherChildrenData' + teacher.teacherId)
            // 获取其内部的元素，内部的元素 `<div class="el-textarea__inner" style="min-height: 33px; ">${iepChildData}</div>` 以此拼接，获取对应的 iepChildData
            const iepChildData = el && el.querySelector('.el-textarea__inner')

            // 如果 iepChildData 不存在就直接返回
            if (!iepChildData) {
                return
            }
            // 如果 iepChildData.innerHTML 是空的
            if (!iepChildData.innerHTML) {
                teacher.iepChildren = []
            }
            // 获取 iepChildData 的所有的 子元素
            const lines = iepChildData.children
            if (teacher.iepChildren.length === 0 || !lines || lines.length === 0) {
                return
            }
            // 定义收集小孩的数组
            const newIepChildren = []
            // 循环遍历所有的子元素
            for (let i = 0; i < lines.length; i++) {
                // 处理每个子节点
                const lineContainer = lines[i]
                if (lineContainer.innerHTML === '') {
                    continue
                }
                // 获取所有的小孩名
                const labelSpan = lineContainer.querySelector('span.child-label')
                // 获取对应的小孩描述
                const descriptionSpan = lineContainer.querySelector('span.child-description')

                const description = descriptionSpan ? descriptionSpan.innerText : ''

                // 从 labelSpan 中获取 data-child-id 属性
                const id = labelSpan ? labelSpan.getAttribute('data-child-id') : ''
                // 使用小孩 id 来从 teacher 的 iepChildren 中进行查找，去替换对应的 description
                const child = teacher.iepChildren.filter(child => child && child.id).find(child => child.id === id)
                // 如果 child 存在，那么就将对应的 description 赋值给 child.description
                if (child) {
                    child.description = description
                }
                // 将 child 返回
                newIepChildren.push(child)
            }
            // 将用户编辑的数据转化为 iepChildren
            teacher.iepChildren = newIepChildren
        },
        // 获取班级的信息
        getGroupTeams(needRequest) {
            if (this.groupId) {
                // 从 sessionStorage 中获取 enableGroupTeams，如果 enableGroupTeams 是 true，那么就获取班级的信息
                let enableGroupTeams = sessionStorage.getItem('enableGroupTeams' + this.groupId)
                // 如果开启了分组，那么就获取班级的信息
                if (enableGroupTeams) {
                    enableGroupTeams = JSON.parse(enableGroupTeams)
                }
                // 获取班级的信息
                this.$axios.get($api.urls().getGroupTeams + '?groupId=' + this.groupId + '&enableGroupTeams=' + enableGroupTeams)
                    .then((res) => {
                        if (res) {
                            this.$nextTick(() => {
                                // 如果 res 是存在的，那么就将 res 的值赋值给 groupTeams
                                this.processGroupTermData(res, needRequest)
                            })
                        }
                    })
                    .catch((error) => {
                        this.groupTeams = {}
                    })
            }
        },
        // 获取班级的信息
        async syncGetGroupTeams (needRequest, errorCallback) {
          if (this.groupId) {
            // 从 sessionStorage 中获取 enableGroupTeams，如果 enableGroupTeams 是 true，那么就获取班级的信息
            let enableGroupTeams = sessionStorage.getItem('enableGroupTeams' + this.groupId)
            // 如果开启了分组，那么就获取班级的信息
            if (enableGroupTeams) {
              enableGroupTeams = JSON.parse(enableGroupTeams)
            }
            // 获取班级的信息
            await this.$axios.get($api.urls().getGroupTeams + '?groupId=' + this.groupId + '&enableGroupTeams=' + enableGroupTeams)
              .then(async (res) => {
                if (res) {
                  await this.processGroupTermData(res, needRequest, errorCallback)
                }
                return Promise.resolve()
              })
              .catch((error) => {
                this.groupTeams = {}
                return Promise.resolve()
              })
          }
        },
        // 当关闭弹窗的时候
        closeAssignTeacherGroups(saveAssignGroup) {
            // 如果 saveAssignGroup 为 true，说明修改了分组，这个时候重新获取分组信息
            if (saveAssignGroup && this.adaptUDLAndCLROpen && this.canAdapter && this.isWeeklyPlanEdit) {
                // 获取分组信息
                this.getGroupTeams(true)
            }
        },
        // 处理请求得到的数据
        async processGroupTermData(groupTerm, needRequest, errorCallback) {
            // 处理 groupTerm 数据
            const groupData = groupTerm.teacherList.map(teacher => {
                return {
                    teacherId: teacher.id,
                    teacherName: teacher.display_name,
                    teacherAvatar: teacher.avatar_url,
                    iepChildren: [], // 这个老师所管理的所有的 iep 小孩数据
                    children: groupTerm.enrollmentTeamModelList
                        .filter(child => child.teacherId === teacher.id)
                        .map(child => {
                            return {
                                id: child.enrollmentId,
                                label: child.displayName,
                                avatarUrl: child.avatarUrl,
                                iep: child.iep,
                                eld: child.eld,
                                description: '',
                                languages: child.languages,
                                symptom: child.symptom
                            }
                        }), // 这个老师所管理的所有的小孩数据
                    groupData: [{
                        id: '00000000-0000-0000-0000-000000000000', // 一开始只有一个分组，所以这里的 id 都是一样的
                        label: 'Group 1',
                        children: groupTerm.enrollmentTeamModelList
                            .filter(child => child.teacherId === teacher.id)
                            .map(child => {
                                return {
                                    id: child.enrollmentId,
                                    label: child.displayName,
                                    avatarUrl: child.avatarUrl,
                                    iep: child.iep,
                                    eld: child.eld,
                                    description: '',
                                    languages: child.languages,
                                    symptom: child.symptom
                                }
                            }) // gpt 返回的小孩的数据
                    }]
                }
            })
            // 从 groupData 中去除那些没有小孩的老师
            const filterGroupData = groupData.filter(teacher => teacher.children.length > 0)
            // 如果 filterGroupData 的长度为 0，那么就不需要进行后续的操作了
            if (filterGroupData.length === 0) {
                this.$message.error(this.$t('loc.batchAdaptLesson17'))
                // 关闭按钮的 loading 状态
                this.$set(this, 'generateUniversalDesignLoading', 0)
                this.$bus.$emit('generateUniversalDesignForLearningCompleted', this.itemId)
                return
            }
            // 将 res 的值赋值给 groupTeams
            this.$set(this, 'teacherGroups', filterGroupData)
            this.$forceUpdate()
            // 判断 filterGroupData 中是否存在 this.currentUserId 对应的教师，如果有 那么就设置 this.currentTeacherId 为 this.currentUserId
            const findTeacher = filterGroupData.find(teacher => teacher.teacherId === this.currentUserId)
            // 如果 findTeacher 存在
            if (findTeacher) {
                this.currentTeacherId = findTeacher.teacherId
            } else {
                // 获取 groupData 中的第一个老师的 Id 作为 currentTeacherId
                this.currentTeacherId = filterGroupData[0].teacherId
            }
            // 如果不需要请求，就不要发送 gpt 请求
            if (!needRequest) {
                return
            }
            // 初始化数据，获取班级中的小孩进行分组
            // 多个分组记录进行处理
            const promises = []
            // 设置按钮为 load 状态
            this.$set(this, 'generateUniversalDesignLoading', this.teacherGroups.length)
            // 发送事件，udl 开始生成数据
            this.$bus.$emit('generateUniversalDesignForLearningStart', this.itemId)
            let newLessonInfo = this.getNewLessonInfo(this.lessonAgeGroup);
            // 开始生成 UDL 数据，此时清理掉原有的普通的内容
            // this.$set(this, 'generalLearningIepActivity', '')
            // this.$set(this, 'generalLearningIepSupport', '')
            // this.$set(this, 'generalEnglishLanguageActivity', '')
            // this.$set(this, 'generalEnglishLanguageSupport', '')
            // 循环每一个老师生成 UDL 数据
            for (let i = 0; i < this.teacherGroups.length; i++) {
                // 获取每一个老师
                const teacher = this.teacherGroups[i]
                // 初始化 teacher 的状态
                teacher.hasIEPData = false
                teacher.hasELDData = false
                // 内容生成清除之前的生成内容
                this.$set(teacher, 'learningIepActivity', '')
                this.$set(teacher, 'iepChildren', [])
                this.$set(teacher, 'classSpecificEnglishLanguageActivity', '')
                this.$set(teacher, 'classSpecificEnglishLanguageSupport', '')
                // 获取小孩 Ids
                const childIds = teacher.children.map(child => child.id)
                // 将老师的 Id 传递给 generateUniversalDesignForLearningGroupStreamGroup
                try {
                    // todo 跳过分组逻辑、直接生成 UDL 内容
                    // const promptUsageRecordId = await this.generateUniversalDesignForLearningGroupStreamGroup(i, teacher.teacherId, childIds)
                    // if (promptUsageRecordId && promptUsageRecordId !== '') {
                   await this.generateUniversalDesignForLearningStream(i, teacher.teacherId, null, childIds, newLessonInfo, errorCallback)
                    // }
                } catch (e) {
                    // 设置按钮为非 load 状态
                    let generateUniversalDesignLoading = this.generateUniversalDesignLoading
                    generateUniversalDesignLoading = generateUniversalDesignLoading - 1
                    if (generateUniversalDesignLoading >= 0) {
                        this.$set(this, 'generateUniversalDesignLoading', generateUniversalDesignLoading)
                    } else {
                        this.$set(this, 'generateUniversalDesignLoading', 0)
                        // 此时生成任务完成，发送事件
                        this.$bus.$emit('generateUniversalDesignForLearningCompleted', this.itemId)
                    }
                    this.$set(this, 'generatedUDL', true)
                }
            }
            // 初始化拖拽
            this.initDrag()
            // 更新对应的老师状态
            this.updateTeacherIEPAndELDStatus()
            // 此时生成任务完成，发送事件
            this.$bus.$emit('generateUniversalDesignForLearningCompleted', this.itemId)
        },
        // 清理混合年龄的数据
        clearMixedAgeDifferentiation(index) {
          // 如果 index 不是 0，那么就直接返回
          if (index !== 0) {
            return
          }
          this.$refs.mixedAgeDifferentiation && this.$refs.mixedAgeDifferentiation.clearOldData()
        },
        // 生成混合年龄组的数据
        async generateLessonMixedAgeDifferentiation(index, newLessonInfo) {
            // 如果 index 不是 0，那么就直接返回
            if (index !== 0) {
                return
            }
            //  等待下一次渲染
            await new Promise(resolve => {
                this.$nextTick(() => {
                    setTimeout(() => {
                        resolve()
                    }, 50)
                })
            })
            if (this.checkShowMixedAge) {
                this.clearMixAgeData = false
                // 生成 mixed age differentiation 数据
                this.$refs.mixedAgeDifferentiation && await this.$refs.mixedAgeDifferentiation.generateLessonMixedAgeGroupStream({ lessonId: this.lessonId,
                  groupId: this.groupId,
                  curAgeGroup: this.lessonAgeGroup,
                  lessonInfo: newLessonInfo})
                // 获取 mixedAgeDifferentiations
                this.clearMixAgeData = !(this.$refs.mixedAgeDifferentiation && this.$refs.mixedAgeDifferentiation.showMixedAgeTable)
            } else {
                // 如果开关没有开启，那么就清空 mixedAgeDifferentiations
                this.clearMixAgeData = true
                // 如果开关没有开启，那么就清空 mixedAgeDifferentiations
                this.$refs.mixedAgeDifferentiation && this.$refs.mixedAgeDifferentiation.clearOldData()
            }
        },
        // 生成 UDL 和 CLR 数据
        generateUniversalDesignAndCLRData() {
            // 向父组件发送事件，点击了按钮需要生成 UDL和 CLR 数据
            this.$emit('generateUniversalDesignAndCLRData', this.lessonAgeGroup)
            // 添加个性化课程适应 UDL 处埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_lesson_udl_adapt')
        },
        // 生成改编 UDL 数据
        generateUniversalDesign(isRegenerateUDL, toAdapt = false) {
            this.toAdapt = toAdapt
            // 设置按钮为 load 状态
            this.$set(this, 'generateUniversalDesignLoading', 1)
            // 首先获取 groupTeam 数据
            this.getGroupTeams(true)
            if (isRegenerateUDL) {
                // 再次生成 UDL  处埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_udl_regenerate')
            }
        },
        // 同步生成 UDL 数据
        async syncGenerateUniversalDesign(errorCallback) {
          // 设置按钮为 load 状态
          this.$set(this, 'generateUniversalDesignLoading', 1)
          // 首先获取 groupTeam 数据
          await this.syncGetGroupTeams(true, errorCallback)
        },
        // 解析插件中的 UDL 数据
        processPluginUniversalDesignFroLearningUniversalStream(val, teacherId) {
          // 找到对应的老师
          const findTeacher = this.teacherGroups.find(item => item.teacherId === teacherId)
          // 不同年龄解析模型不同
          let universalDesignForLearning = null
            if (!this.kToGrade12) {
                universalDesignForLearning = parseStreamData(val, [
                    { key: 'learnersWithIEP', name: ['For Learners with IEP', 'For Learners with IEPs'] },
                    { key: 'englishLanguageLearners', name: ['For Dual Language Learners', 'For ELD Children'] }
                ])[0]
            } else {
                // 将 val 转化为对象
                universalDesignForLearning = parseStreamData(val, [
                    { key: 'learnersWithIEP', name: ['For Learners with IEP', 'For Learners with IEPs'] },
                    { key: 'englishLanguageLearners', name: ['For English Language Learners', 'For ELD Children'] }
                ])[0]
            }
          if (universalDesignForLearning.learnersWithIEP !== undefined && universalDesignForLearning.learnersWithIEP !== '') {
            let learnersWithIEP = parseStreamData(universalDesignForLearning.learnersWithIEP, [
              {key: 'activityDescription', name: 'Activity Description'},
              {key: 'support', name: 'Support:'}
            ])[0]
            if (learnersWithIEP.activityDescription !== undefined && learnersWithIEP.activityDescription !== '') {
              this.$set(findTeacher, 'learningIepActivity', learnersWithIEP.activityDescription)
            }
            if (learnersWithIEP.support !== undefined && learnersWithIEP.support !== '') {
              // 定义返回的结果集
              const iepChild = []

              // 定义结果
              const iepChildItem = {
                id: '',
                description: learnersWithIEP.support
              }
              // 将小孩放入到 iepChild
              iepChild.push(iepChildItem)
              // 将 iepChild 放入到 findTeacher 中
              this.$set(findTeacher, 'iepChildren', iepChild)
              // iep 小孩发生了变化，这个时候调用 teacherChildrenData
              this.teacherChildrenData(findTeacher)
            }
          }
          if (universalDesignForLearning.englishLanguageLearners !== undefined && universalDesignForLearning.englishLanguageLearners !== '') {
            // 移除掉所有以 Note 开头的数据
            universalDesignForLearning.englishLanguageLearners = universalDesignForLearning.englishLanguageLearners.replace(/\(Note:[^)]*\)|Note:[^\n]*/g, '')
            let englishLanguageLearners = parseStreamData(universalDesignForLearning.englishLanguageLearners, [
              {key: 'activityDescription', name: 'Activity Description'},
              {key: 'support', name: 'Support:'}
            ])[0]
            if (englishLanguageLearners.activityDescription !== undefined && englishLanguageLearners.activityDescription !== '') {
              this.$set(findTeacher, 'classSpecificEnglishLanguageActivity', englishLanguageLearners.activityDescription)
            }
            if (englishLanguageLearners.support !== undefined && englishLanguageLearners.support !== '') {
              this.$set(findTeacher, 'classSpecificEnglishLanguageSupport', englishLanguageLearners.support)
            }
          }
        },
        // 处理第二步骤的 UDL 的数据生成
        processUniversalDesignFroLearningUniversalStream(val, teacherId) {
            // 找到对应的老师
            const findTeacher = this.teacherGroups.find(item => item.teacherId === teacherId)
            // 不同年龄解析模型不同, K 年龄以上用 For English Language Learners ， K 年龄以下用 For Dual Language Learners
            let universalDesignForLearning = parseStreamData(val, [
                    { key: 'learnersWithIEP', name: ['For Learners with IEP', 'For Learners with IEPs'] },
                    { key: 'englishLanguageLearners', name: ['For Dual Language Learners', 'For English Language Learners', 'For ELD Children'] }
                ])[0]
            if (universalDesignForLearning.learnersWithIEP !== undefined && universalDesignForLearning.learnersWithIEP !== '') {
                let learnersWithIEP = parseStreamData(universalDesignForLearning.learnersWithIEP, [
                    {key: 'activityDescription', name: 'Activity Description'},
                    {key: 'support', name: 'Support'}
                ])[0]
                if (learnersWithIEP.activityDescription !== undefined && learnersWithIEP.activityDescription !== '') {
                    this.$set(findTeacher, 'learningIepActivity', learnersWithIEP.activityDescription)
                }
                if (learnersWithIEP.support !== undefined && learnersWithIEP.support !== '') {
                    let childIepdifferentiatedSupport = parseStreamData(learnersWithIEP.support, [
                        {key: 'childName', name: 'Child Name'},
                        {key: 'relation', name: 'Eligibility'},
                        {key: 'differentiatedSupport', name: 'Differentiated Support'}
                    ])
                    // 如果 childIepdifferentiatedSupport 存在
                    if (childIepdifferentiatedSupport && childIepdifferentiatedSupport.length > 0) {
                        // 定义返回的结果集
                        const iepChild = []
                        // 将 childIepdifferentiatedSupport 中的每一个元素进行处理
                        // childIepdifferentiatedSupport 中的每一个元素都是一个小孩的信息， 小孩名和描述信息使用 : 进行分割
                        childIepdifferentiatedSupport.forEach((item) => {
                            if (item.childName && item.childName !== '' && item.differentiatedSupport && item.differentiatedSupport !== '') {
                                // 将 itemArr 中的第一个元素去除前后空格然后全部转化为小写
                                const childName = item.childName.trim().toLowerCase()
                                // 将 itemArr 中的第二个元素去除前后空格
                                const childDescription = item.differentiatedSupport.trim()
                                // 将 findTeacher 中每一个分组中的所有的小孩都收集起来
                                let allChildren = findTeacher.children
                                // 利用 childGroups 在小孩中进行查找小孩的名称相等的数据
                                // 找到对应的小孩
                                const findChild = allChildren.find(child => child.label.trim().toLowerCase() === childName)
                                // 如果 findChild 找到了
                                if (findChild) {
                                    // 定义结果
                                    const iepChildItem = {
                                        id: findChild.id,
                                        label: findChild.label,
                                        avatarUrl: findChild.avatarUrl,
                                        iep: findChild.iep,
                                        eld: findChild.eld,
                                        description: childDescription
                                    }
                                    // 将小孩放入到 iepChild
                                    iepChild.push(iepChildItem)
                                }
                            }
                        })
                        // 将 iepChild 放入到 findTeacher 中
                        this.$set(findTeacher, 'iepChildren', iepChild)
                        // iep 小孩发生了变化，这个时候调用 teacherChildrenData
                        this.teacherChildrenData(findTeacher)
                    }
                }
            }
            if (universalDesignForLearning.englishLanguageLearners !== undefined && universalDesignForLearning.englishLanguageLearners !== '') {
                // 移除掉所有以 Note 开头的数据
                universalDesignForLearning.englishLanguageLearners = universalDesignForLearning.englishLanguageLearners.replace(/\(Note:[^)]*\)|Note:[^\n]*/g, '')
                let englishLanguageLearners = parseStreamData(universalDesignForLearning.englishLanguageLearners, [
                    {key: 'activityDescription', name: 'Activity Description'},
                    {key: 'support', name: 'Support'}
                ])[0]
                if (englishLanguageLearners.activityDescription !== undefined && englishLanguageLearners.activityDescription !== '') {
                    this.$set(findTeacher, 'classSpecificEnglishLanguageActivity', englishLanguageLearners.activityDescription)
                }
                if (englishLanguageLearners.support !== undefined && englishLanguageLearners.support !== '') {
                    this.$set(findTeacher, 'classSpecificEnglishLanguageSupport', englishLanguageLearners.support)
                }
            }
        },
        // 处理第一步的 UDL 的数据生成
        processUniversalDesignFroLearningStream(val, teacherId) {
            // 找到对应的老师
            const findTeacher = this.teacherGroups.find(item => item.teacherId === teacherId)
            // 打印 findTeacher
            // 将 val 转化为对象
            let universalDesignForLearning = parseStreamData(val, [
                {key: 'groupInfo', name: 'Inclusive Learning Groups'},
                {key: 'learnersWithIEP', name: 'For Learners with IEPs'},
                {key: 'englishLanguageLearners', name: 'For English Language Learners'}
            ])[0]
            if (universalDesignForLearning.groupInfo !== undefined && universalDesignForLearning.groupInfo !== '') {
                // 移除掉所有以 Note 开头的数据
                universalDesignForLearning.groupInfo = universalDesignForLearning.groupInfo.replace(/\(Note:[^)]*\)|Note:[^\n]*/g, '')
                let groupInfo = parseStreamData(universalDesignForLearning.groupInfo, [
                    {key: 'childGroups', name: ['Group Name', 'Group Name:']},
                    {key: 'childList', name: ['Child List', 'Child List:']}
                ])
                // 构造所有的分组
                const groupData = []
                // 计算组数
                let groupIndex = 0
                // 循环所有的 childGroups
                for (let groupKey in groupInfo) {
                    let childGroups = groupInfo[groupKey].childList
                    // 判断是不是 undefined
                    if (childGroups && childGroups !== '' && !childGroups.startsWith('None')) {
                        // childGroups 按照 ， 分割
                        childGroups = childGroups.split(',')
                        // 将 childGroups 中的数据去除前后空格然后全部转化为小写
                        childGroups = childGroups.map(child => child.trim().toLowerCase())
                        // 将 findTeacher 中每一个分组中的所有的小孩都收集起来
                        let allChildren = findTeacher.children
                        // 利用 childGroups 在小孩中进行查找小孩的名称相等的数据
                        let findChildren = allChildren.filter(child => childGroups.includes(child.label.trim().toLowerCase()))
                        // 如果 findChildren 的小孩数目为 0，那么就不需要进行后续的操作了
                        if (findChildren.length === 0) {
                            continue
                        }
                        // 构造 groupData
                        const groupDataItem = {
                            id: 'Group ' + (groupIndex + 1), // 拼接 groupId
                            label: 'Group ' + (groupIndex + 1),
                            children: findChildren
                        }
                        // 将 group 对应的 index 添加
                        groupIndex++
                        // 添加到 groupData 中
                        groupData.push(groupDataItem)
                    }
                }
                // 将 groupData 添加到 findTeacher 中
                this.$set(findTeacher, 'groupData', groupData)
            }
        },
        // 生成 UDL
        async generateUniversalDesignForLearningGroupStreamGroup(index, teacherId, childIds) {
            // 如果正在生成，那么此时就不在允许点击
            if (this.generateUniversalDesignForLearningLoading[index]) {
                return
            }
            this.generateUniversalDesignForLearningLoading[index] = true
            // 重置数据， universalDesignForLearning 是一个数组，要修改 universalDesignForLearning[teacherId] 的值
            this.$set(this.lesson.universalDesignForLearning, teacherId, null)
            // 从 sessionStorage 中获取 enableGroupTeams，如果 enableGroupTeams 是 true，那么就获取班级的信息
            let enableGroupTeams = sessionStorage.getItem('enableGroupTeams' + this.groupId)
            // 如果开启了分组，那么就获取班级的信息
            if (enableGroupTeams) {
                enableGroupTeams = JSON.parse(enableGroupTeams)
            }
            // 生成单元概览参数
            let params = {
                lessonId: this.lessonId,
                groupId: this.groupId,
                teacherId: teacherId || this.currentTeacherId,
                enrollmentIds: childIds,
                enableGroupTeams: enableGroupTeams ? 'true' : 'false',
                lessonInfo: this.getNewLessonInfo(this.lessonAgeGroup)
            }
            // 消息回调`
            let messageCallback = (message) => {
                // 更新数据
                let data = (this.lesson.universalDesignForLearning[teacherId] || '') + message.data
                data = data.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '')
                this.processUniversalDesignFroLearningStream(data, teacherId)
                this.$set(this.lesson.universalDesignForLearning, teacherId, data)
            }
            // 生成单元概览
            const promptUsageRecordId = await createEventSource($api.urls().lessonGenerateUniversalDesignForLearningGroupStream, null, messageCallback, 'POST', params)
                .then((res) => {
                    // 生成结束
                    this.generateUniversalDesignForLearningLoading[index] = false
                    // 记录 promptUsageRecordId
                    if (res && res.promptUsageRecordId !== '') {
                        // 使用 $set this.promptUsageRecordIds.push(res.promptUsageRecordId)
                        this.$set(this, 'promptUsageRecordIds', [res.promptUsageRecordId])
                        return res.promptUsageRecordId
                    }
                })
                .catch(error => {
                    // 设置加载状态为 0
                    this.$set(this, 'generateUniversalDesignLoading', 0)
                    // 标识需要用户重新点击生成
                    this.$set(this, 'generatedUDL', true)
                    // 生成出错
                    this.generateUniversalDesignForLearningLoading[index] = false
                    this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                    return ''
                })
            return promptUsageRecordId
        },
        // 这里是获取 UDL 数据的第二步骤，这里需要上一步的执行结果，所以收集了对应的 promptUsageRecordId
        async generateUniversalDesignForLearningStream(index, teacherId, promptUsageRecordId, childIds, newLessonInfo, errorCallback) {

            // 重置数据
            this.$set(this.lesson.universalDesignForLearningStream, teacherId, null)
            // 重置数据之后清理混合年龄组数据
            this.clearMixedAgeDifferentiation(index)
            // 从 sessionStorage 中获取 enableGroupTeams，如果 enableGroupTeams 是 true，那么就获取班级的信息
            let enableGroupTeams = sessionStorage.getItem('enableGroupTeams' + this.groupId)
            // 如果开启了分组，那么就获取班级的信息
            if (enableGroupTeams) {
                enableGroupTeams = JSON.parse(enableGroupTeams)
            }
            // 生成单元概览参数
            let params = {
                lessonId: this.lessonId,
                groupId: this.groupId,
                teacherId: teacherId || this.currentTeacherId,
                promptUsageId: promptUsageRecordId,
                enableGroupTeams: enableGroupTeams ? 'true' : 'false',
                enrollmentIds: childIds,
                lessonInfo: newLessonInfo,
                enhance: !this.toAdapt
            }
            // 创建一个仅仅执行一次的函数
            // 定义一个需要等待的 promise
            let awaitPromise = null;
            // 消息回调`
            let messageCallback = (message) => {
                if (this.leavePage) {
                  return
                }
                this.$emit('updateUniversalDesignForLearningClassSpecial', '[]', this.lessonAgeGroup, this.mixedAgeGroup())
                // 更新数据
                let data = (this.lesson.universalDesignForLearningStream[teacherId] || '') + message.data
                data = data.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '')
                data = removeUnexpectedCharacters(data)
                if (this.isCurriculumPlugin) {
                  this.processPluginUniversalDesignFroLearningUniversalStream(data, teacherId)
                } else {
                  this.processUniversalDesignFroLearningUniversalStream(data, teacherId)
                }
                this.$set(this.lesson.universalDesignForLearningStream, teacherId, data)
            }
            // 生成单元概览
            await createEventSource($api.urls().universalDesignForLearningStream, null, messageCallback, 'POST', params)
                .then(res => this.processSuccessStream(res, index, awaitPromise, newLessonInfo))
                .catch(error => this.processErrorStream(error, index, newLessonInfo, teacherId, errorCallback))
                .finally(() => {
                  // 重置改编状态
                  this.toAdapt = false
                })
        },
        // 处理 UDL 数据生成成功
        async processSuccessStream(res, index, awaitPromise, newLessonInfo) {
            if (this.leavePage) {
              return
            }
            // 补充 IEP 数据
            this.provideLearningIepData()
            this.$emit('updateUdlLoading',false)
            // 更新数据父元素中的数据
            this.updateUniversalDesignForLearningGroup()
            // 生成结束
            this.generateUniversalDesignForLearningLoading[index] = false
            // 当 UDL 获取得到数据的时候，调用一次 generateLessonMixedStream
            if (!awaitPromise) {
                awaitPromise = this.generateLessonMixedAgeDifferentiation(index, newLessonInfo)
            }
            await awaitPromise
            // 记录 promptUsageRecordId
            if (res && res.promptUsageRecordId !== '') {
              this.$set(this, 'promptUsageRecordIds', [res.promptUsageRecordId])
              // 设置按钮为非 load 状态
              let generateUniversalDesignLoading = this.generateUniversalDesignLoading
              generateUniversalDesignLoading = generateUniversalDesignLoading - 1
              if (generateUniversalDesignLoading >= 0) {
                this.$set(this, 'generateUniversalDesignLoading', generateUniversalDesignLoading)
              } else {
                this.$set(this, 'generateUniversalDesignLoading', 0)
              }
              this.$set(this, 'generatedUDL', true)
            }
        },
        // 处理 UDL 数据生成失败
        async processErrorStream(error, index, newLessonInfo, teacherId, errorCallback) {
            // 生成出错
            this.generateUniversalDesignForLearningLoading[index] = false
            // 如果发生了错误，那么就是由于参数异常或者老师没有 IEP 和 ELD 小孩导致的，不再输出错误信息
            // this.$message.error(error.message)
            // 设置按钮为非 load 状态
            let generateUniversalDesignLoading = this.generateUniversalDesignLoading
            generateUniversalDesignLoading = generateUniversalDesignLoading - 1
            if (generateUniversalDesignLoading >= 0) {
              this.$set(this, 'generateUniversalDesignLoading', generateUniversalDesignLoading)
            } else {
              this.$set(this, 'generateUniversalDesignLoading', 0)
            }
            this.$set(this, 'generatedUDL', true)
            // 如果 error 不是 NO_DATA，就说明是 error 的时候 reject 来的，这个时候需要展示错误提示，否则不展示
            if (error.message && error.message !== '"NO_DATA"') {
              this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
              if (errorCallback) {
                errorCallback()
              }
            } else {
              // 如果是空串，那么就是没有 IEP 和 ELD 小孩导致的，不再输出错误信息
              // this.groupErrorMap.set(teacherId, this.$t('loc.unitPlannerUDLError'))
              this.$message.warning(this.$t('loc.unitPlannerUDLError'))
              // 清理旧数据
              this.clearTeacherData()
              if (errorCallback) {
                errorCallback()
              }
              // 如果抛出了错误，则直接生成混合年龄组的数据
              await this.generateLessonMixedAgeDifferentiation(index, newLessonInfo)
            }
        },
        // 清理老师的数据
        clearTeacherData() {
            // 更改 trigger 的值，让计算属性重新计算
            this.triggerErrorTitle = !this.triggerErrorTitle
            // 如果存在多个分组，那么此时可能仅有一个分组的数据咩有，那么此时不需要清理数据
            if (this.teacherGroups.length > 1) {
                return
            }
            // 清理 universalDesignForLearningClassSpecial 和 teacherGroups
            this.$emit('clearUniversalDesignForLearningClassSpecial', this.lessonAgeGroup)
            this.$set(this, 'universalDesignForLearningClassSpecial', [])
            this.$set(this, 'teacherGroups', [])
        },
        // 处理 adaptUDLAndCLROpen 的结果
        processadaptUDLAndCLROpen(adaptUDLAndCLROpen) {
            // 获取 this.universalDesignForLearningClassSpecial.length > 0 的结果，如果存在分组数据，那么就显示分组数据，否则就显示通用数据
            if (adaptUDLAndCLROpen || this.teacherGroups.length > 0) {
                this.currentTagName = this.submodules[1]
            } else {
                this.currentTagName = this.submodules[0]
            }
        },
        // 打开 assignTeacherGroups 的弹窗
        showAssignTeacherGroups() {
            // 添加 Unit Planner 设置的埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_udl_click_atg')
            LessonApi.getGroupAdaptSetting(this.groupId).then(res => {
                this.$refs.assignTeacherGroups.enableTecherGroups = res.teacherGroupEnabled
                this.$refs.assignTeacherGroups.showAssignTeacherGroups(this.groupId, '')
            })
        },
        // 管理小孩
        manageClassRoomDemographics() {
            if (!this.canAdapter) {
                window.open('/#/manage_children')
            } else {
                // 跳转路由
                let {href} = this.$router.resolve({
                    name: 'manageChildren',
                    query: {
                        centerId: this.centerId,
                        groupId: this.groupId
                    }
                })
                // 打开新标签页
                window.open(href, '_blank')
            }
            // 添加 Unit Planner 设置的埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_click_manage_demogra')
        },
        // 点击老师对应的标签选择了老师，所以将老师设置为当前老师
        selectTeacher(teacher) {
            this.currentTeacherId = teacher.teacherId
            // 由于 teacher 发生了变化，这个时候修改一下 input 的 size
            this.handleResize()
        },
        // 处理 resize 事件，需要将所有设置了 autosize 的 input 元素进行重新计算高度
        handleResize() {
            // 渲染一下页面
            this.$nextTick(() => {
                if (this.$refs.autosizeInput && this.$refs.autosizeInput.length >= 1) {
                    this.$refs.autosizeInput.forEach((item) => {
                        item.resizeTextarea()
                    })
                }
            })
            this.$nextTick(() => {
                // general 中的 input 要重新计算一下高度
                if (!this.isNotEdit) {
                    // 重新计算一下高度
                    this.$refs.generalLearningIepActivity && this.$refs.generalLearningIepActivity.resizeTextarea()
                    this.$refs.generalLearningIepSupport && this.$refs.generalLearningIepSupport.resizeTextarea()
                    this.$refs.generalEnglishLanguageActivity && this.$refs.generalEnglishLanguageActivity.resizeTextarea()
                    this.$refs.generalEnglishLanguageSupport && this.$refs.generalEnglishLanguageSupport.resizeTextarea()
                }
            })
        },
        // 定义一个函数用于封装需要拖动的元素的信息
        dragInfo(group, child) {
            // 将 group 信息和 child 信息都封装起来
            return JSON.stringify({
                id: child.id,
                label: child.label,
                avatarUrl: child.avatarUrl,
                groupId: group.id,
                iep: child.iep,
                eld: child.eld,
                sortNum: group.children.findIndex(item => item.id === child.id)
            })
        },
        // 获取当前需要拖动的元素所属于哪个老师的分组
        getCategoryInfo(groupId) {
            const findTeacher = this.teacherGroups.find(item => item.teacherId === this.currentTeacherId)
            // 将 group 信息封装起来
            return JSON.stringify({
                id: groupId,
                index: findTeacher.groupData.findIndex(item => item.id === groupId)
            })
        },
        // 初始化拖动的功能
        initDrag() {
            // 首先判断是不是在编辑状态
            if (this.isNotEdit) {
                return
            }
            // 拿到所有的拖拽分组
            var categories = document.querySelectorAll('.content-group')
            // 遍历所有的拖拽分组，创建拖拽实例
            var _this = this
            var tip = this.$t('loc.unitPlannerUDLDragErrorTitle')
            for (var dragCategory of categories) {
                // 如果已经创建拖拽实例，跳过
                if (dragCategory.sort) {
                    continue
                }
                dragCategory.putTip = true
                // 创建拖拽实例
                dragCategory.sort = Sortable.create(dragCategory, {
                    group: {
                        name: 'shared',
                        // 拖拽到目标分组时，判断是否可以放置
                        put: function (to, from, el) {
                            // from 是拖拽的分组, to 是目标分组, el 是拖拽的元素
                            // from.el 就是拖拽的分组元素, to.el 就是目标分组元素, el 就是拖拽的元素
                            // 如果要拖动的位置和当前位置是一样的, 或者移动到的分级和当前分级是一样的,那么就不需要拖拽,仅仅允许将 child 移动到 group 中,不允许移动到其他的地方
                            let canPut = to.el !== from.el || el.parentElement !== from.el
                            // group-title 是分组的标题,不允许拖动
                            canPut = canPut && el.className !== 'group-title'
                            // 拿到原分组的信息
                            var fromCategory = JSON.parse(from.el.dataset.categoryInfo)
                            // 拿到拖拽项目的信息
                            var dragInfo = JSON.parse(el.dataset.dragInfo)

                            // 从原分组中移除 child
                            // 从 _this 中找到原分组
                            const findTeacher = _this.teacherGroups.find(item => item.teacherId === _this.currentTeacherId)
                            let sourceCategory = findTeacher.groupData.find((item) => item.id === fromCategory.id)
                            let sourceCategoryChild = sourceCategory.children.filter(child => child.id !== dragInfo.id)

                            // 如果 sourceCategory 或者 targetCategory 的 children 为空,则取消这次拖拽
                            if (sourceCategoryChild.length === 0) {
                                canPut = false
                            }
                            // 如果不需要拖动
                            if (dragCategory.putTip && !canPut) {
                                dragCategory.putTip = false
                                _this.$message({
                                    message: tip,
                                    type: 'error',
                                    duration: 2000,
                                    onClose: function () {
                                        dragCategory.putTip = true
                                    },
                                })
                            }
                            return canPut
                        },
                    },
                    handle: '.group-child',
                    animation: 150,
                    scroll: true,
                    scrollSensitivity: 50,
                    scrollSpeed: 10,
                    bubbleScroll: true,
                    forceFallback: true,
                    onEnd: (evt) => {
                        // 拖拽结束，更新数据
                        // 拿到拖拽的目标分组和原分组
                        var from = evt.from
                        var to = evt.to
                        // 拿到拖拽的项目
                        var item = evt.item
                        // 输入对应的值

                        // 如果目标分组和原分组不一致，则更新数据
                        // 拖拽结束后重新排列 group-title 和 group-child 的顺序
                        const contentGroups = document.querySelectorAll('.content-group')
                        contentGroups.forEach((contentGroup) => {
                            const groupTitle = contentGroup.querySelector('.group-title')
                            const groupChildren = Array.from(contentGroup.querySelectorAll('.group-child'))

                            // 将 group-title 移到第一个位置
                            contentGroup.insertBefore(groupTitle, contentGroup.firstChild)

                            // 将 group-child 按照 sortNum 排序
                            groupChildren.sort((a, b) => {
                                const sortNumA = parseInt(JSON.parse(a.dataset.dragInfo).sortNum)
                                const sortNumB = parseInt(JSON.parse(b.dataset.dragInfo).sortNum)
                                return sortNumA - sortNumB
                            })

                            // 将排好序的 group-child 依次添加到 contentGroup 中
                            groupChildren.forEach((child) => {
                                contentGroup.appendChild(child)
                            })
                        })

                        if (from !== to) {
                            // 拿到原分组的信息
                            var fromCategory = JSON.parse(from.dataset.categoryInfo)
                            // 拿到目标分组的信息
                            var toCategory = JSON.parse(to.dataset.categoryInfo)
                            // 拿到拖拽项目的信息
                            var dragInfo = JSON.parse(item.dataset.dragInfo)

                            // 从原分组中移除 child
                            // 找到当前的老师
                            const findTeacher = _this.teacherGroups.find(item => item.teacherId === _this.currentTeacherId)
                            const findTeacherIndex = _this.teacherGroups.findIndex(item => item.teacherId === _this.currentTeacherId)
                            // 从 _this 中找到原分组
                            let sourceCategory = findTeacher.groupData.find((item) => item.id === fromCategory.id)
                            let targetCategory = findTeacher.groupData.find((item) => item.id === toCategory.id)
                            sourceCategory.children = sourceCategory.children.filter(child => child.id !== dragInfo.id)

                            // 将 child 添加到目标分组
                            targetCategory.children.push({
                                id: dragInfo.id,
                                label: dragInfo.label,
                                avatarUrl: dragInfo.avatarUrl,
                                iep: dragInfo.iep,
                                eld: dragInfo.eld,
                                // 其他属性...
                            })

                            // 更新 Vue 数据，为 teacherGroups 中 teacherId 为 currentTeacherId 的 groupData 的 fromCategory.index 和 toCategory.index 进行赋值, 使用 this.$set 进行赋值
                            this.$set(this.teacherGroups[findTeacherIndex].groupData, fromCategory.index, sourceCategory)
                            this.$set(this.teacherGroups[findTeacherIndex].groupData, toCategory.index, targetCategory)

                            // 拖拽结束
                            // 添加 Unit Planner 设置的埋点
                            this.$analytics.sendEvent('web_weekly_plan_edit_lesson_udl_drag')
                        }
                    },
                })
            }
        },
        // 设置 feedback 的样式
        setFeedbackStyle() {
            return {
                float: 'right',
                zIndex: '20',
                position: 'relative',
                top: '-8px'
            }
        },
        // 当 普通的 和 课程特定的 之间切换的时候
        switchType(tag, event) {
            if (tag.name === this.submodules[0]) {
                this.currentTagName = tag.name
                // 切换通用 UDL 处埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_udl_general')
            } else if (tag.name === this.submodules[1]) {
                this.currentTagName = tag.name
                // 切换班级 UDL 处埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_udl_class')
            }
            this.handleResize()
        },
        /**
         * 获取生成按钮的图标
         */
        getGenerateButtonIcon() {
            if (this.generatedUDL) {
                return regenerateIcon
            }
            return generateDefaultIcon
        },
        /**
         * 获取生成按钮的名称：Generate、Generating Measures、Generating Lesson Plan、Regenerate
         */
        getGenerateButtonName() {
            // 已经生成过课程信息了
            if (this.generatedLesson) {
                return 'Regenerate'
            }
        },
        // 处理普通的 Universal Design for Learning 数据
        processUniversalDesignFroLearning(val, getObject = false) {
            if (val === null || val === undefined || val === '') {
                return
            }
            val = val.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '')
            const objData = {}
            // 根据年龄段解析模型不同， K 年龄以上用 For English Language Learners ， K 年龄以下用 For Dual Language Learners
            let universalDesignForLearning = parseStreamData(val, [
                    { key: 'learnersWithIEP', name: 'For Learners with IEPs' },
                    // 兼容历史数据
                    { key: 'englishLanguageLearners', name: ['For Dual Language Learners', 'For English Language Learners'] }
                ])[0]
            if (universalDesignForLearning.learnersWithIEP !== undefined && universalDesignForLearning.learnersWithIEP !== '') {
                let learnersWithIEP = parseStreamData(universalDesignForLearning.learnersWithIEP, [
                    {key: 'activityDescription', name: 'Activity Description'},
                    {key: 'support', name: 'Support:'}
                ])[0]
                if (learnersWithIEP.activityDescription !== undefined && learnersWithIEP.activityDescription !== '') {
                    learnersWithIEP.activityDescription = learnersWithIEP.activityDescription.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()
                    objData.generalLearningIepActivity = learnersWithIEP.activityDescription.replace('TEMP_SUPPORT', 'Support')
                }
                if (learnersWithIEP.support !== undefined && learnersWithIEP.support !== '') {
                    learnersWithIEP.support = learnersWithIEP.support.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()
                    objData.generalLearningIepSupport = learnersWithIEP.support
                }
            }
            if (universalDesignForLearning.englishLanguageLearners !== undefined && universalDesignForLearning.englishLanguageLearners !== '') {
                let englishLanguageLearners = parseStreamData(universalDesignForLearning.englishLanguageLearners, [
                    {key: 'activityDescription', name: 'Activity Description'},
                    {key: 'support', name: 'Support:'}
                ])[0]
                if (englishLanguageLearners.activityDescription !== undefined && englishLanguageLearners.activityDescription !== '') {
                    englishLanguageLearners.activityDescription = englishLanguageLearners.activityDescription.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()
                    objData.generalEnglishLanguageActivity = englishLanguageLearners.activityDescription.replace('TEMP_SUPPORT','Support')
                }
                if (englishLanguageLearners.support !== undefined && englishLanguageLearners.support !== '') {
                    englishLanguageLearners.support = englishLanguageLearners.support.replace(/^"|"$|(^"|"$)/g, '').replace(/```.*$/, '').trim()
                    objData.generalEnglishLanguageSupport = englishLanguageLearners.support
                }
            }
            // 如果需要返回对象直接返回，否则就设置到对应的变量中
            if (getObject) {
              return objData
            } else {
              this.$set(this, 'generalLearningIepActivity', objData.generalLearningIepActivity || '')
              this.$set(this, 'generalLearningIepSupport', objData.generalLearningIepSupport || '')
              this.$set(this, 'generalEnglishLanguageActivity', objData.generalEnglishLanguageActivity || '')
              this.$set(this, 'generalEnglishLanguageSupport', objData.generalEnglishLanguageSupport || '')
            }
        },
        // 校验 universalDesignForLearningClassSpecial 的值
        validatedUniversalDesignForLearningClassSpecial() {
            if (this.universalDesignForLearningClassSpecial && this.universalDesignForLearningClassSpecial !== '') {
                // 使用响应式设置 teacherGroups
                this.$set(this, 'teacherGroups', JSON.parse(this.universalDesignForLearningClassSpecial))
                // 获取 groupData 中的找到 this.currentUserId 对应的老师，如果没有找到就默认第一个老师
                const findTeacher = this.teacherGroups.find(teacher => teacher.teacherId === this.currentUserId)
                if (findTeacher) {
                    this.currentTeacherId = findTeacher.teacherId
                } else {
                    if (this.teacherGroups.length !== 0) {
                        this.currentTeacherId = this.teacherGroups[0].teacherId
                    }
                }
                // 处理对应的老师状态
                this.updateTeacherIEPAndELDStatus()
                // 处理老师的 teacherChildrenContent
                this.teacherGroups.forEach(teacher => {
                    // 如果 teacherChildrenContent 不存在
                    if (!teacher.teacherChildrenContent) {
                        // 调用方法为 teacherChildrenContent 赋值
                        this.teacherChildrenData(teacher)
                    }
                })
                // 处理 Universal Design for Learning 数据完成之后，resize 一下
                this.handleResize()
            }
        },
        // 判断 autosizeInput 元素是否可以被看见
        observeElementVisibility() {
            // 获取 autosizeInput 元素
            const autosizeInputs = this.$refs.autosizeInput

            // 如果元素存在，并且数量大于等于 1
            if (autosizeInputs && autosizeInputs.length > 1) {
                // 遍历循环 autosizeInputs
                autosizeInputs.forEach(autosizeInput => {
                    // 获取挂载的元素
                    const inputElement = autosizeInput.$el
                    // 定义 DOM 检测器
                    const observer = new IntersectionObserver(entries => {
                        // 输入 entries
                        // 如果元素可见
                        if (entries[0].isIntersecting) {
                            // 执行 resizeTextArea 方法
                            autosizeInput.resizeTextarea()

                            // 可以选择取消观察，如果只想执行一次
                            observer.disconnect()
                        }
                    })

                    // 开始观察元素
                    observer.observe(inputElement)
                })
            }
        },
        /**
         * 点击反馈的埋点
         */
        clickFeedback(isUp) {
            if (isUp) {
                // UDL 正反馈的埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_udl_feedback_pos')
            } else {
                // UDL 负反馈的埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_udl_feedback_neg')
            }
        },
        // 初始化 showMixedAge
        initShowMixedAge() {
            // 如果 showMixedAge 存在
            if (!this.isNotEdit) {
                let request = {
                  grade: this.lessonAgeGroup,
                  groupId: this.groupId
                }
                // 将 showMixedAge 转化为布尔值
                this.$axios.post($api.urls().getShowMixedAgeGroupData, request).then(res => {
                        if (res.success) {
                            this.checkShowMixedAge = true
                        }
                  })
            }
        },
        // 初始化 enableShowIEPOrELD
        initEnableShowIEPOrELD() {
          const item = sessionStorage.getItem('enableShowIEPOrELD' + this.currentUserId);
          this.enableShowIEPOrELD = !item;
        },
        // 如果 IEP 没生成内容，将原始数据赋值给新生成内容
        provideLearningIepData() {
          if (this.teacherGroups && this.teacherGroups.length > 0 && this.teacherGroups[0]) {
            // UDL 对应的 4 条数据
            let learningIepActivity = ''
            let teacherChildrenContent = ''
            let englishLanguageActivity = ''
            let englishLanguageSupport = ''
            // 判断原始数据的获取的方式
            if (this.universalDesignForLearningCopy) {
              const universalDesignForLearningCopyObj = this.processUniversalDesignFroLearning(this.universalDesignForLearningCopy, true)
              // 赋值原始数据
              learningIepActivity = universalDesignForLearningCopyObj.generalLearningIepActivity
              teacherChildrenContent = universalDesignForLearningCopyObj.generalLearningIepSupport
              englishLanguageActivity = universalDesignForLearningCopyObj.generalEnglishLanguageActivity
              englishLanguageSupport = universalDesignForLearningCopyObj.generalEnglishLanguageSupport
            } else if (this.universalDesignForLearningClassSpecialCopy) {
              const universalDesignForLearningClassSpecialCopyObj = JSON.parse(this.universalDesignForLearningClassSpecialCopy)
              if (universalDesignForLearningClassSpecialCopyObj.length > 0 && universalDesignForLearningClassSpecialCopyObj[0]) {
                // 赋值原始数据
                learningIepActivity = universalDesignForLearningClassSpecialCopyObj[0].learningIepActivity
                teacherChildrenContent = universalDesignForLearningClassSpecialCopyObj[0].teacherChildrenContent
                englishLanguageActivity = universalDesignForLearningClassSpecialCopyObj[0].classSpecificEnglishLanguageActivity
                englishLanguageSupport = universalDesignForLearningClassSpecialCopyObj[0].classSpecificEnglishLanguageSupport
              }
            }
            const teacherGroup = this.teacherGroups[0]
            // 为没生成的模块赋值
            if (!teacherGroup.learningIepActivity && !teacherGroup.teacherChildrenContent) {
              teacherGroup.learningIepActivity = learningIepActivity
              teacherGroup.hasIEPData = learningIepActivity
              teacherGroup.teacherChildrenContent = teacherChildrenContent
            }
            if (!teacherGroup.classSpecificEnglishLanguageActivity && !teacherGroup.classSpecificEnglishLanguageSupport) {
              teacherGroup.classSpecificEnglishLanguageActivity = englishLanguageActivity
              teacherGroup.classSpecificEnglishLanguageSupport = englishLanguageSupport
            }
            // 更新数据父元素中的数据
            this.updateUniversalDesignForLearningGroup()
          }
        }
    },
    mounted() {
        // 后续 adaptUDLAndCLROpen 处理结果
        this.processadaptUDLAndCLROpen(this.adaptUDLAndCLROpen)
        // item 挂在完成后初始化拖拽逐渐
        this.$nextTick(() => {
            if (this.currentTeacherId && this.currentTeacherId !== '') {
                this.initDrag()
            }
        })
        // 监听窗口大小变化
        // 在组件挂载后注册resize事件监听器
        window.addEventListener('resize', this.handleResize)
        // 检测元素可见性
        this.observeElementVisibility()
        // UDL mounted 完成之后，发送事件
        if (!this.changeAgeGroups) {
            this.$bus.$emit('lessonLibraryUDLMounted', this.itemId)
        }
        // 监听页面滚动
        window.document.addEventListener('mousewheel', this.handleScroll, true) || window.document.addEventListener('DOMMouseScroll', this.handleScroll, true)
        // 初始化 showMixedAge
        this.initShowMixedAge()
        this.initEnableShowIEPOrELD()
    },
    watch: {
        universalDesignForLearning: {
            handler(val) {
                // 处理 Universal Design for Learning 数据
                this.processUniversalDesignFroLearning(val)
                // 处理 Universal Design for Learning 数据完成之后，resize 一下
                this.handleResize()
            },
            immediate: true,
            deep: true
        }
    },
    async created() {
        // 监听事件
        this.$bus.$on('updateSelectedGroupId', () => {
            this.groupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
        })
        // 获取班级的信息
        await this.validatedUniversalDesignForLearningClassSpecial()
    }
}
</script>

<style lang="less" scoped>
.UDL-header {
    display: flex;
    padding: 0px 0px;
    align-items: center;
    gap: 20px;
    width: 100%;
    height: 40px;
    align-self: center;
    color: var(--color-primary);

    .regenerated-btn, .regenerated-btn:hover, .regenerated-btn:focus {
        border-radius: 4px;
        border: 2px solid var(--10-b-3-b-7, #10B3B7);
        background: var(--ffffff, #FFF);
        display: flex;
        padding: 8px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }

    .regenerated-btn:active {
        opacity: 0.5 !important;
    }

    .header-title {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 12px;
        width: 100%;
        flex-grow: 1;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 137.5% */
    }

    .header-button {
        display: flex;
        height: 40px;
        padding: 8px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        width: fit-content;
    }

    .ai-btn, .ai-btn:hover, .ai-btn:focus {
        background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
        border-color: none !important;
        border: 0 !important;
        color: #FFFFFF;

    }

    .ai-btn:active {
        opacity: 0.5 !important;
    }
}

.teacher-children {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.group-child {
    display: flex;
    min-height: 32px;
    height: fit-content;
    line-height: normal;
    padding: 4px 8px;
    align-items: center;
    gap: 8px;
    border-radius: 90px;
    background: #FFF;
    cursor: pointer;
    border: 1px solid var(--color-primary);
}

.UDL-body {
    width: 100%;

    .class-specific-instructions {
        display: flex;
        padding: 16px 10px 24px 10px;
        width: 100%;
        flex-direction: column;
        align-items: flex-end;
        gap: 16px;
        align-self: center;

        .inclusive-learning-groups {
            border-radius: 4px;
            background: var(--color-page-background-white);
            width: 100%;

            .content-title {
                display: flex;
                padding: 16px;
                align-items: center;
                justify-content: space-between;
                gap: 12px;
                max-height: 60px;
                align-self: center;
                border-bottom: 1px solid #C0C4CC;
                background: var(--color-page-background-white);

                .teacher-info {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    gap: 10px;
                    align-self: center;

                    .width-85 {
                        width: 85%;
                    }

                    .group-child {
                        display: flex;
                        min-height: 32px;
                        height: fit-content;
                        line-height: normal;
                        padding: 4px 8px;
                        align-items: center;
                        gap: 8px;
                        border-radius: 90px;
                        background: #FFF;
                        cursor: pointer;
                        border: 1px solid var(--color-primary);
                    }

                    .selected-teacher {
                        color: var(--color-white);
                        background: var(--color-primary);
                    }
                }

                .title-text {
                    color: #000;
                    font-feature-settings: 'clig' off, 'liga' off;
                    font-family: Inter;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 22px; /* 157.143% */
                }
            }

            .content-content {
                display: flex;
                padding: 16px 16px 12px 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                align-self: stretch;

                .teacher-group {
                    width: 100%;
                }

                .content-group {
                    display: flex;
                    align-items: center;
                    align-content: center;
                    gap: 14px;
                    align-self: stretch;
                    flex-wrap: wrap;
                    padding: 10px 0;
                    border-bottom: 1px solid var(--color-border);

                    .group-title {
                        color: #000;
                        font-feature-settings: 'clig' off, 'liga' off;
                        font-family: Inter;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 22px; /* 157.143% */
                    }

                    .group-child {
                        display: flex;
                        min-height: 32px;
                        height: fit-content;
                        line-height: normal;
                        padding: 4px 8px;
                        align-items: center;
                        gap: 8px;
                        border-radius: 90px;
                        background: #FFF;
                        cursor: pointer;
                    }
                }
            }
        }

        .for-learners-with-IEPs {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            align-self: center;
            border-radius: 4px;
            width: 100%;

            .learners-table {
                table {
                    border-collapse: collapse;
                    width: 100%;
                }

                .activity-description {
                    display: flex;
                    padding: 12px;
                    word-break: keep-all;
                    justify-content: center;
                    align-items: center;
                    align-self: center;
                    color: var(--color-text-secondary);
                    text-align: center;
                    font-feature-settings: 'clig' off, 'liga' off;

                    /* Semi Bold/16px */
                    font-family: Inter;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 150% */
                }

                .content-description {
                    display: flex;
                    width: 100%;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex: 1 0 0;
                    color: var(--color-text-primary);
                    font-feature-settings: 'clig' off, 'liga' off;

                    /* Regular/16px */
                    font-family: Inter;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 24px; /* 150% */
                }

                .child-content {
                    display: flex;
                    padding: 16px 20px;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                    flex: 1 0 0;

                    .child-description {
                        display: flex;
                        width: fit-content;
                        align-items: center;
                        align-self: center;
                        background-color: var(--color-page-background-white);
                        gap: 8px;
                        padding: 4px 8px;
                        height: 32px;
                        border-radius: 90px;
                        flex: 1 0 0;
                    }
                }
            }
        }

        .for-english-language-learners {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            align-self: center;
            width: 100%;
            border-radius: 4px;

            .learners-table {
                table {
                    border-collapse: collapse;
                    width: 100%;
                }

                td {
                    border: 1px solid var(--color-border);
                }

                .activity-description {
                    display: flex;
                    padding: 12px;
                    word-break: keep-all;
                    justify-content: center;
                    align-items: center;
                    align-self: center;
                    color: var(--color-text-secondary);
                    text-align: center;
                    font-feature-settings: 'clig' off, 'liga' off;

                    /* Semi Bold/16px */
                    font-family: Inter;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 150% */
                }

                .content-description {
                    display: flex;
                    width: 100%;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex: 1 0 0;
                    color: var(--color-text-primary);
                    font-feature-settings: 'clig' off, 'liga' off;

                    /* Regular/16px */
                    font-family: Inter;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 24px; /* 150% */
                }

                .child-content {
                    display: flex;
                    padding: 16px 20px;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                    flex: 1 0 0;

                    .child-description {
                        display: flex;
                        width: fit-content;
                        align-items: center;
                        align-self: center;
                        background-color: var(--color-page-background-white);
                        gap: 8px;
                        padding: 4px 8px;
                        height: 32px;
                        border-radius: 90px;
                        flex: 1 0 0;
                    }
                }
            }
        }
    }
}

.domain-table {
    border-radius: 4px;
    border: 1px solid #f49628;
    width: 100%;
    border-collapse: collapse;
    color: #323338;

    tr,
    th,
    td {
        border: 1px solid #f49628;
    }

    th {
        height: 40px;
    }
}

.is-error {
    .domain-table {
        border: 1px solid red;

        tr,
        th,
        td {
            border: 1px solid red;
        }
    }
}

.table-row {
    position: relative;
}

.table-row:hover {
    .del-button {
        visibility: visible;
    }
}

.del-button {
    visibility: hidden;
    position: absolute;
    right: -20px;
    top: 0;
    color: red;
    height: -webkit-fill-available;
    align-items: center;
    display: flex;
}

/deep/ .el-select {
    margin: 10px;
}

/deep/ .el-input__inner {
    border-style: dashed;
}

/deep/ .el-textarea__inner {
    border-color: transparent;
    resize: none;
    overflow: hidden;
}

/deep/ .el-textarea__inner:hover {
    border: 1px dashed #c0c4cc;
}

/deep/ .el-textarea__inner:focus {
    border: 1px dashed #10b3b7;
}

.textarea-hover-padding-skeleton {
    & > :first-child {
        padding: 10px 15px;

        [contenteditable="true"] {
            outline: none;
        }

        // iep support 使用 editor 组件，这里对其进行样式修改
        .teacherChildrenContent /deep/ .quill-editor {
            line-height: normal;
            height: 100%;

            h1, h2, h3, h4, h5, h6 {
                line-height: 1.42 !important;
            }

            // 隐藏功能栏
            .ql-toolbar {
                display: none !important;
            }

            .ql-container {
                font-size: 14px !important;
                border: 1px dashed transparent !important;
                resize: none !important;
                overflow: hidden !important;
                border-radius: 4px !important;
            }

            .ql-editor {
                border: 1px dashed transparent !important;
            }

            .ql-editor:hover {
                border: 1px dashed var(--color-inner-dashed-border) !important;
            }

            .ql-editor:focus {
                border: 1px dashed var(--color-primary) !important;
            }

            .ql-editor {
                overflow-y: auto;
                min-height: 33px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
                &.ql-blank:before {
                    font-style: normal;
                }
            }

            /* 修改富文本编辑器的placeholder的默认颜色：字体 */

            .ql-editor[data-placeholder]:before {
                /* 此处设置 placeholder 样式 */
                font-style: normal;
                font-size: 14px;
            }
        }
    }
}

.textarea-hover-padding {
    padding: 10px 15px;
}

.textarea-hover-padding:hover, .textarea-hover-padding:focus-within {
    padding: 10px 15px;
}

.remove-remove-padding-t-0 {
    padding-top: 0px !important;
}

/deep/ .el-card__header {
    display: -ms-flexbox;
    display: flex;
    padding-top: 16px;
    padding-bottom: 32px;
    padding-left: 24px;
    padding-right: 24px;
    margin-bottom: 0px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%;
    align-self: center;
    gap: 16px;
    background: #E8F9FA;
}

/deep/ .el-tree-node__children {
    display: flex;
    flex-wrap: nowrap; /* 防止换行 */
}

/deep/ .ai-assistant-tab {

    border-radius: 4px;
    background: #ffffff;
    border: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    .el-tabs__header {
        border: none !important;
        margin: 0 !important;
        width: fit-content;
    }

    .el-tabs__nav {
        padding: 6px !important;
        border-radius: 4px !important;
        gap: 6px;
        display: inline-flex;
        border: none !important;
        width: 100% !important;
        background: var(--2-ebeef-5, #EBEEF5);
        justify-content: center;
        align-items: center;
    }

    .el-tabs__item.is-active {
        background: #ffffff !important;
        color: var(--10-b-3-b-7, #10B3B7);
        font-weight: 600;
        border-radius: 4px;
    }

    .el-tabs__item {
        color: var(--111-c-1-c, #111C1C);
        font-weight: 400;
        font-size: 14px;
        height: 32px !important;
        line-height: 22px !important;
        padding: 0 12px !important;
        border-width: 0 !important;
        width: fit-content !important;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .el-tabs__content {
        padding: 15px 0 0 0 !important;
        width: 100%;
    }
}

.remove-padding {
    padding: 0px !important;
}

/* 描述列表标签值 */
.lesson-field-value {
    font-size: 16px;
    line-height: 25px;
    color: #111c1c;
}

.content-difference {
    padding: 24px;
    border-radius: 20px;
    border: 1px solid rgba(16, 179, 183, 0.40);
    background: #fff;
    margin-top: -14px;
}

.differentiation-title {
  //styleName: Regular/14px;
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  text-align: left;
}

.bottom-tip {
    max-height: 26px;

    .title-tag {
        display: flex;
        justify-content: flex-end;
        align-items: flex-start;
        gap: 22px;
        .dot-eld {
            width: 16px;
            height: 16px;
            background: #2D9CDB;
            border-radius: 50%;
        }

        .dot-iep {
            width: 16px;
            height: 16px;
            background: #FF7F41;
            border-radius: 50%;
        }

        .tag-css {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 9px;
        }
    }
}

.popover-dot-eld {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: #002EFE;
    border-radius: 50%;
}

.popover-dot-iep {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: #FF7F41;
    border-radius: 50%;
}

// iep support 使用 editor 组件，这里对其进行样式修改
.teacherChildrenContent /deep/ .quill-editor {
    line-height: normal;
    height: 100%;

    h1, h2, h3, h4, h5, h6 {
        line-height: 1.42 !important;
    }

    // 隐藏功能栏
    .ql-toolbar {
        display: none !important;
    }

    .ql-container {
        font-size: 14px !important;
        border: 1px dashed transparent !important;
        resize: none !important;
        overflow: hidden !important;
        border-radius: 4px !important;
    }

    .ql-editor {
        border: 1px dashed transparent !important;
    }

    .ql-editor:hover {
        border: 1px dashed var(--color-inner-dashed-border) !important;
    }

    .ql-editor:focus {
        border: 1px dashed var(--color-primary) !important;
    }

    .ql-editor {
        overflow-y: auto;
        min-height: 33px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
        &.ql-blank:before {
            font-style: normal;
        }
    }

    /* 修改富文本编辑器的placeholder的默认颜色：字体 */

    .ql-editor[data-placeholder]:before {
        /* 此处设置 placeholder 样式 */
        font-style: normal;
        font-size: 14px;
    }

    .ql-editor ol, .ql-editor ul {
        padding-left: 0px !important;
    }

    .ql-editor ol li:not(.ql-direction-rtl), .ql-editor ul li:not(.ql-direction-rtl) {
        padding-left: 0px !important;
    }
}
</style>