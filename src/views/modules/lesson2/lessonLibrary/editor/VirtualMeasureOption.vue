<template>
  <!-- 如果是领域（带有 children 属性），则渲染为 option-group -->
  <el-option-group v-if="isDomain" :label="measure.abbreviation || measure.name" :key="'domain-' + measure.id"></el-option-group>
  <!-- 如果是单个测评点，则直接渲染为 option -->
  <el-option v-else
    :key="measure.id"
    :label="measure.abbreviation"
    :value="measure.id"
    :disabled="isOptionDisabled(measure.id)">
    <span>{{measure.abbreviation}}<span v-if="measure.core" style="color: red;"> *</span></span>
  </el-option>
</template>

<script>
export default {
  name: 'VirtualMeasureOption',
  props: {
    // 当前 item 的 index，由 virtual-list 提供
    index: {
      type: Number
    },
    // 当前数据项，由 virtual-list 提供
    source: {
      type: Object,
      default () {
        return {}
      }
    },
    isOptionDisabled: {
      type: Function,
      default: () => {
      },
    }
  },
  data () {
    return {
      measure: null
    }
  },
  watch: {
    source: {
      handler (val) {
        if (val) {
          this.measure = val
        }
      },
      immediate: true
    }
  },
  computed: {
    // 判断当前是否为领域
    isDomain() {
      return this.measure && this.measure.isDomain
    }
  }
}
</script>

<style scoped lang="less">
</style>