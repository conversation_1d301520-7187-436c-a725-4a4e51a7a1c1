<template>
  <div :class="['new-lesson', 'scrollbar-new', { 'lg-padding-l-r-8': showAiAssistant, 'mobile-new-lesson': lgIsMobile }]">
    <el-row v-if="isFromIpad" class="header">
      <router-link to>
        <img style="height: 33px;padding-top: 15px;" src="@/assets/img/us/back_home.png" @click="$router.back()">
      </router-link>
      <span>{{ title }}</span>
    </el-row>

    <!-- 添加欢迎弹窗组件 -->
    <welcome-dialog :visible="showWelcomeDialog" @close="closeWelcomeDialog"></welcome-dialog>

    <div v-show="showAssistantTool && addLessonStep === 1" class="assistant-container" v-if="isAddLesson" :style="{ height: lgIsMobile ?'calc(-100px + 100vh)' : inWeeklyPlannDialog ? 'calc(-230px + 100vh)' : 'calc(-230px + 100vh)' }">
      <!-- 标题部分 -->
      <div style="border-radius: 4px; padding: 8px; margin-bottom: 10px; text-align: center;padding-bottom: 12px;">
        <div style="display: flex; align-items: center; justify-content: center;">
            <i class="lg-icon lg-icon-magic font-size-24 add-margin-r-8"
              style="color: #8B63FF"/>
            <span style="background: linear-gradient(270deg, #2D9CDB 49.87%, #8B63FF 90.23%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-size: 24px; font-weight: bold;">Lesson Plan</span>
        </div>
        <div style="margin-top: 5px; text-align: center; font-size: 16px;">
          {{ $t('loc.lessonWelcomeDescription') }}
        </div>
      </div>
      <el-card class="h-full assistant-tool">
        <!-- 添加 AssistantTool 组件 -->
        <assistant-tool
          ref="assistantTool"
          :isFromUnitLesson="isFromUnitLesson"
          :ageGroups="ageGroup"
          :frameworks="frameworks"
          :aiFullScreenFlag="aiFullScreenFlag"
          :aiToolEntryShow="aiToolEntryShow"
          :lessonInfoHasChanged="lessonInfoHasChanged"
          :lessonCoverLoading="lessonCoverLoading"
          :inWeeklyPlannDialog="inWeeklyPlannDialog"
          :selectedFramework="selectedFramework"
          :lesson="lesson"
          :saveDraft="saveDraft"
          :saveLesson="saveLesson"
          :isAddLesson="isAddLesson"
          :batchEdit="batchEdit"
          @updateAddLessonStep="updateAddLessonStep"
          @updateLessonContent="updateLessonAssistantContent"
          @setStepsLessonTemplates="setStepsLessonTemplates"
          @updateLessonInfo="updateLessonInfo"
          @updateLessonMeasureSelectNotShow="updateLessonMeasureSelectNotShow"
          @syncAssistantInfoToLesson="syncAssistantInfoToLesson"
          @generateImpStepResourceAssistant="generateImpStepResourceAssistant"
          @updateLessonFrameworkAndMeasures="updateLessonFrameworkAndMeasures"
          @clearLesson="clearLesson"
          @aiFullScreen="aiFullScreen"
          @updateLessonCoverLoading="updateLessonCoverLoading"
          @updateConvertLessonLoading="updateConvertLessonLoading"
          @generaterdLessoncomplete="generaterdLessoncomplete"
          @getPromptUsageRecordIds="getPromptUsageRecordIds"
          @updateLessonInfoHasChanged="updateLessonInfoHasChanged"
          @updateLessonStepTwo="updateLessonStepTwo"
          @curriculumGenerateLessonSourceByCLR="curriculumGenerateLessonSourceByCLR"
          @setGenerateUniversalState="setGenerateUniversalState"
          @scrollToActive="scrollToActive"
          @batchTranslateDllVocabularies="batchTranslateDllVocabularies"
          @upSubclassCenter="upSubclassCenter"
          @generateSlides="generateSlides"
          @changeActivityType="changeActivityType"
        ></assistant-tool>
      </el-card>
    </div>

    <div class="content lg-border-radius-8 display-flex justify-content">
      <!-- <FeedbackForm :defaultFeedbackResult="defaultFeedbackResult"
                    v-if="!convertLessonLoading"
                    :feedbackStyle="setFeedbackStyle"
                    :showFeedback="generaterdLessoncompleted"
                    :showIcon="true"
                    :showClose="false"
                    :feedbackTitle="feedbackTitle"
                    :feedbackSubmit="feedbackSubmit"
                    :needFeedbackLevel="false"
                    :feedbackInputPlaceholder="feedbackInputPlaceholder"
                    :promptUsageRecordIds="promptUsageRecordIds"
                    :showReminders="false"
                    @submitFeedback="submitFeedback"
                    :style="computedFeedbackStyle"/> -->

      <div style="width: 100%;" class="lesson-editor-container" v-if="!aiFullScreenFlag && (!showAssistantTool || !isAddLesson || (addLessonStep === 2 && isAddLesson)) && loading === 2" :class="{'in-dialog': inDialog, 'mobile-lesson-editor-container': lgIsMobile}">
        <!-- 顶部固定状态栏 -->
        <div class="fixed-status-bar fixed-status-bar-width" v-show="!batchEdit" :class="{'mobile-fixed-status-bar': lgIsMobile}">
          <!-- 状态提示 -->
          <div class="status-indicator" :class="{'mobile-status-indicator': lgIsMobile}">
            <!-- 状态标题 -->
            <!-- 三种状态：正在保存、正在生成、已保存 -->
            <div class="status-item" v-if="convertLessonLoading" :class="{'mobile-status-item': lgIsMobile}">
              <span class="status-icon" :class="{'mobile-status-icon': lgIsMobile}">
                <i class="el-icon-refresh"></i>
              </span>
              <span class="status-time" :class="{'mobile-status-time': lgIsMobile}"> {{ $t('loc.generating') }} </span>
            </div>

            <div class="status-item" v-else-if="saving === 1 || saving === 2" :class="{'mobile-status-item': lgIsMobile}">
              <span class="status-icon" :class="{'mobile-status-icon': lgIsMobile}">
                <i class="el-icon-refresh"></i>
              </span>
              <span class="status-time" :class="{'mobile-status-time': lgIsMobile}"> {{ $t('loc.saving') }} </span>
            </div>

            <div class="status-item" v-else :class="{'mobile-status-item': lgIsMobile}">
              <span class="status-icon" :class="{'mobile-status-icon': lgIsMobile}">
                <i class="el-icon-success"></i>
              </span>
              <span class="status-time" :class="{'mobile-status-time': lgIsMobile}"> {{ $t('loc.lastSaved') }}&nbsp;{{ lastPublishTime ? $moment.utc(lastPublishTime).local().format('MM/DD/YYYY, h:mm A') : '' }}</span>
            </div>
            <!-- 反馈按钮区域 -->
            <FeedbackForm :defaultFeedbackResult="defaultFeedbackResult"
                    class="feedback-button"
                    :feedbackStyle="setFeedbackStyle"
                    :showFeedback="generaterdLessoncompleted"
                    :showIcon="true"
                    :showClose="false"
                    :feedbackTitle="feedbackTitle"
                    :feedbackSubmit="feedbackSubmit"
                    :needFeedbackLevel="false"
                    :feedbackInputPlaceholder="feedbackInputPlaceholder"
                    :promptUsageRecordIds="promptUsageRecordIds"
                    :showReminders="false"
                    @submitFeedback="submitFeedback"
                    :style="computedFeedbackStyle"
                    style="margin-left: 16px;"
                    :class="{'mobile-feedback-button': lgIsMobile}"
                    />

            <!-- 右侧按钮区域 -->
            <div class="right-buttons" :class="{'mobile-right-buttons': lgIsMobile}">
              <!-- 历史按钮 -->
              <el-button slot="reference"
                  v-if="!isActionDisabledWhileGenerating && lesson.status !== 'DRAFT' && lesson.id && publishAfterGenerate"
                  style="padding: 3px 6px!important;"
                  type="primary"
                  size="medium"
                  @click="openHistory"
                  plain>
                  <div class="btn-center">
                    <i style="font-size: 16px;" class="lg-icon lg-icon-history lg-color-primary"></i>
                    <span>{{ $t('loc.lessonVersionHistory') }}</span>
                  </div>
              </el-button>
              <!-- Edit Prompt 按钮 -->
              <el-popover
                v-show="isAddLesson && !convertLessonLoading"
                :visible-arrow="false"
                placement="bottom"
                popper-class="edit-prompt-popover"
                v-model="editPromptVisible"
                trigger="manual">
                <el-button slot="reference"
                  style="padding: 3px 6px!important;"
                  type="primary"
                  size="medium"
                  @click="clickEditPromptPopover"
                  plain>
                  <div class="btn-center">
                    <i class="lg-icon lg-icon-edit lg-color-primary"></i>
                    <span>{{ $t('loc.editPrompt') }}</span>
                  </div>
                </el-button>
                <!-- 编辑 prompt 弹窗头部 -->
                <div class="display-flex justify-content-between align-items">
                  <div class="font-size-20 font-weight-600 lg-color-text-primary">
                    Prompt
                  </div>
                  <div>
                    <i class="lg-icon lg-icon-close lg-pointer font-size-24"
                        @click="clickEditPromptPopover"
                      ></i>
                  </div>
                </div>
                <!-- 将 AssistantTool 组件嵌入到弹出框中 -->
                <assistant-tool
                  ref="assistantToolEditPrompt"
                  class="assistant-tool-edit-prompt"
                  :ageGroups="ageGroup"
                  :isFromUnitLesson="isFromUnitLesson"
                  :frameworks="frameworks"
                  :aiToolEntryShow="false"
                  :lessonInfoHasChanged="lessonInfoHasChanged"
                  :lessonCoverLoading="lessonCoverLoading"
                  :inWeeklyPlannDialog="inWeeklyPlannDialog"
                  :selectedFramework="selectedFramework"
                  :lesson="lesson"
                  :saveDraft="saveDraft"
                  :saveLesson="saveLesson"
                  :source="'editPromptPopover'"
                  :isAddLesson="isAddLesson"
                  :batchEdit="batchEdit"
                  :adaptedModuleSwitch="adaptedModuleSwitch"
                  @switchAiToolShow="switchAiToolShow"
                  @updateAddLessonStep="updateAddLessonStep"
                  @updateLessonContent="updateLessonAssistantContent"
                  @setStepsLessonTemplates="setStepsLessonTemplates"
                  @updateLessonInfo="updateLessonInfo"
                  @updateLessonMeasureSelectNotShow="updateLessonMeasureSelectNotShow"
                  @syncAssistantInfoToLesson="syncAssistantInfoToLesson"
                  @generateImpStepResourceAssistant="generateImpStepResourceAssistant"
                  @updateLessonFrameworkAndMeasures="updateLessonFrameworkAndMeasures"
                  @clearLesson="clearLesson"
                  @updateLessonCoverLoading="updateLessonCoverLoading"
                  @updateConvertLessonLoading="updateConvertLessonLoading"
                  @generaterdLessoncomplete="generaterdLessoncomplete"
                  @getPromptUsageRecordIds="getPromptUsageRecordIds"
                  @updateLessonInfoHasChanged="updateLessonInfoHasChanged"
                  @updateLessonStepTwo="updateLessonStepTwo"
                  @curriculumGenerateLessonSourceByCLR="curriculumGenerateLessonSourceByCLR"
                  @setGenerateUniversalState="setGenerateUniversalState"
                  @scrollToActive="scrollToActive"
                  @batchTranslateDllVocabularies="batchTranslateDllVocabularies"
                  @upSubclassCenter="upSubclassCenter"
                  @changeActivityType="changeActivityType"
                  @generateSlides="generateSlides"
                  @syncGenerateUniversalDesignAndCLRData="syncGenerateUniversalDesignAndCLRData"
                ></assistant-tool>
              </el-popover>

              <!-- 下载/导出按钮 -->
              <lesson-download style="height: 36px;"
                v-if="!isActionDisabledWhileGenerating && lesson.id"
                @click.native="handleDownloadClick"
                :lesson-id="lesson.id"
                :lesson-name="lesson.name"
                :lesson="lesson"
                :lesson-framework-id="lesson && lesson.framework && lesson.framework.frameworkId"
                :disabled="convertLessonLoading"
                @beforeDownload="handleBeforeDownload"
                @afterDownload="handleAfterDownload"
              />
            </div>
          </div>
        </div>

        <div class="lg-scrollbar-show form-container" :class="{'mobile-form-container': lgIsMobile, 'form-container-with-banner': showBanner}">
          <el-form ref="form" v-if="showLessonForm" :model="lesson" :rules="rules" label-position="top" class="lesson-form" :class="{'in-dialog': inDialog, 'mobile-lesson-form': lgIsMobile, 'has-from-unit-tag': isFromUnitLesson}" :style="elFromZindexClass" style="margin: 0 auto;">
          <!-- 来自 Unit 的标签 -->
          <div v-if="isFromUnitLesson" class="lesson-from-unit-container display-flex align-items">
            <!-- <el-tooltip popper-class="max-width-400" content="This lesson is linked to a Unit. Changes will sync with the original Unit plan." placement="top"> -->
              <div class="lesson-from-unit-tag" @click="goToUnitDetail">
                <svg class="unit-icon lg-icon" style="flex: none;" width="16" height="16" aria-hidden="true">
                    <use xlink:href="#lg-icon-a-unitpaln"></use>
                </svg>
                <span class="unit-title" :title="fromUnit.title">{{ $t('loc.fromUnit') + ' ' + fromUnit.title }}</span>
                <i class="lg-icon lg-icon-arrow-right"></i>
              </div>
            <!-- </el-tooltip> -->
            <div class="font-size-14 font-weight-400" style="color: #676879;">{{ $t('loc.fromUnitSyncUnitPlanTip') }}</div>
          </div>

        <!--封面-->
          <el-form-item prop="cover" class="lesson-form-cover" :class="{'mobile-lesson-form-cover': lgIsMobile}">
            <div v-if="lessonCoverLoading">
              <el-skeleton :loading="lessonCoverLoading" animated>
                <template slot="template">
                  <el-skeleton-item variant="image" class="lesson-cover-el-skeleton-item" />
                </template>
              </el-skeleton>
            </div>
            <media-uploader v-if="!lessonCoverLoading" :showUrlBtn="false" typeDistinguish="coverMedia" v-model="lesson.cover" class="media-uploader-lesson-cover" :tips="$t('loc.lesson2NewLessonFormPlaceHolderCover')">
              <div slot="tips" class="media-uploader-tips">
                <ul style="text-align: left; padding: 0 32px;">
                  <li style="list-style: disc;">{{ $t('loc.coverExternalMediaUploadSize') }}</li>
                  <li style="list-style: disc;">{{ $t('loc.curriculum105') }}</li>
                  <li style="list-style: disc;">{{ $t('loc.curriculum106') }}</li>
                </ul>
              </div>
            </media-uploader>
          </el-form-item>
        <div style="align-items: end;padding-right: 10px;" class="hidden-lg-and-up display-flex flex-direction-col" :class="{'mobile-hidden-lg-and-up': lgIsMobile}">
          <div>
            <!--课程名称-->
            <el-form-item v-show="!isFromUnitLesson" :label="$t('loc.lesson2NewLessonFormLabelLessonName')" prop="name" class="lesson-form-name is-required" :style="formItemLongStyle">
              <el-input v-model="lesson.name" @change="updateLessonName" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderLessonName')"
                        auto-complete="off" type="text" maxlength="200"/>
            </el-form-item>
            <div v-show="isFromUnitLesson" class="font-size-20 font-bold m-b-sm">
              <span>{{ lesson.name }}</span>
            </div>
          </div>

          <template v-if="isAddLesson || isFromUnitLesson">
            <div class="read-only-info" style="width: 100%;">
              <div class="age-group-framework-info">
                <!-- 年龄组 -->
                <div class="read-only-info-item text-no-wrap" v-for="readOnlyAgeGroup in readOnlyAgeGroup" :key="readOnlyAgeGroup">
                  {{ readOnlyAgeGroup }}
                </div>
                <!-- 框架 -->
                <div class="read-only-info-item text-no-wrap text-overflow-ellipsis" :title="readOnlyFramework"> {{ readOnlyFramework }} </div>
                <!-- 课堂类型标签 -->
                <div v-if="lesson.classroomType && showClassroomType" class="read-only-info-item text-no-wrap">
                  {{ getClassroomTypeLabel(lesson.classroomType) }}
                </div>
              </div>

              <!-- 测评点 -->
              <div class="measures-info">
                <div class="measures-info-title">Standards</div>
                <div class="measures-info-content">{{ readOnlyMeasures }}</div>
              </div>
            </div>
          </template>
          <template v-else>
          <div>
            <!--年龄组-->
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelAgeGroup')" prop="ages" class="lesson-form-age is-required" :style="formItemLongStyle">
              <el-select v-model="ageValues" @change="changeAgeValues($event,false, true ,true)"
                         multiple
                         :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                         :class="['input-select', {'single-age-group': ageValues && ageValues.length === 1}]">
                <el-option v-for="item in ageGroup" :key="item.value" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>
          </div>
          </template>
          <div>
            <!--准备时间-->
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelPrepareTime')"
                          class="lesson-form-prepare" :style="formItemShortStyle">
              <el-select v-model="lesson.prepareTime" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                         class="input-select" clearable>
                <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>
            <!--活动时间-->
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelActivityTime')" class="lesson-form-activity" :style="formItemShortStyle">
              <el-select v-model="lesson.activityTime" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                         class="input-select" clearable>
                <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="hidden-md-and-down" :class="{'mobile-hidden-md-and-down': lgIsMobile}">
          <!--课程名称-->
          <el-form-item v-show="!isFromUnitLesson" :label="$t('loc.lesson2NewLessonFormLabelLessonName')" prop="name" class="lesson-form-name is-required" :style="formItemLongStyle">
            <el-input v-model="lesson.name" @change="updateLessonName" @focus="fillLessonName" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderLessonName')"
                      auto-complete="off" type="text" maxlength="200"/>
          </el-form-item>
          <div v-show="isFromUnitLesson" class="lesson-form-name font-size-20 font-bold add-margin-b-24">
              <span>{{ lesson.name }}</span>
          </div>

          <template v-if="isAddLesson || isFromUnitLesson">
            <div class="read-only-info">
              <div class="age-group-framework-info">
                <!-- 年龄组 -->
                <div class="read-only-info-item text-no-wrap" v-for="readOnlyAgeGroup in readOnlyAgeGroup" :key="readOnlyAgeGroup">
                  {{ readOnlyAgeGroup }}
                </div>
                <!-- 框架 -->
                <div class="read-only-info-item text-no-wrap text-overflow-ellipsis" :title="readOnlyFramework"> {{ readOnlyFramework }} </div>
                <!-- 课堂类型标签 -->
                <div v-if="lesson.classroomType && showClassroomType" class="read-only-info-item text-no-wrap">
                  {{ getClassroomTypeLabel(lesson.classroomType) }}
                </div>
              </div>

              <!-- 测评点 -->
              <div class="measures-info">
                <div class="measures-info-title">Standards</div>
                <div class="measures-info-content">{{ readOnlyMeasures }}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <!--年龄组-->
            <el-form-item :label="$t('loc.lesson2NewLessonFormLabelAgeGroup')" prop="ages" class="lesson-form-age is-required" :style="formItemLongStyle">
              <el-select v-model="ageValues" @change="changeAgeValues($event,false, true, true)"
                        multiple
                        ref="ageGroupSelect"
                        :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                        :class="['input-select', {'single-age-group': ageValues && ageValues.length === 1}]">
                <el-option v-for="item in ageGroup" :key="item.value" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>
          </template>
          <!--准备时间-->
          <el-form-item :label="$t('loc.lesson2NewLessonFormLabelPrepareTime')"
                        class="lesson-form-prepare" :style="formItemShortStyle">
            <el-select v-model="lesson.prepareTime" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                       class="input-select" clearable>
              <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value"/>
            </el-select>
          </el-form-item>
          <!--活动时间-->
          <el-form-item :label="$t('loc.lesson2NewLessonFormLabelActivityTime')" class="lesson-form-activity" :style="formItemShortStyle">
            <el-select v-model="lesson.activityTime" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                       class="input-select" clearable>
              <el-option v-for="item in times" :key="item.value" :label="item.name" :value="item.value"/>
            </el-select>
          </el-form-item>
        </div>
        <!--课程模板-->
        <el-form-item v-if="lesson.id && isNotCenter && eduProtocolsTemplateApplyOpen && getLightAdaptModuleShow('eduProtocolsTemplatesFlag')" :label="$t('loc.eduprotocols9')" class="lesson-form-prepare" :style="formItemLongStyle">
          <lesson-template-select-modal v-model="lesson.templateType" :lessonId="lesson.id" :loading="convertLessonLoading" :lessonAges="lesson.ages.map(x => x.name)" :saveLesson="saveLesson" @change="updateLessonTemplate" />
        </el-form-item>
        <template v-if="!isAddLesson && !isFromUnitLesson">
          <!--主题-->
          <el-form-item :label="$t('loc.lesson2NewLessonFormLabelTheme')" prop="themes"
                        :class="lesson.id && isNotCenter && eduProtocolsTemplateApplyOpen ? 'lesson-form-activity' : 'lesson-form-theme' "
                        :style="[formItemLongStyle,lessonThemeMarginStyle]">
            <el-select ref="lessonTheme" v-model="themeIds" multiple :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')"
                      class="input-select" @change="themeChangeHandler" filterable allow-create>
              <el-option v-for="item in themes" :key="item.id" :label="item.name" :value="item.id">
                <div class="display-flex justify-content-between flex-align-center">
                  <span>{{ item.name }}</span>
                  <i v-if="item.custom" class="el-icon-close" @click.stop="deleteLessonTheme(item.id)"></i>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <!--框架-->
          <el-form-item :label="$t('loc.lesson2NewLessonFormLabelFramework')" prop="framework"
                  class="lesson-form-framework is-required">
            <el-select v-model="frameworkId" class="input-select"
                        @change="changeFramework($event, false)"
                      :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderSelect')">
              <el-option v-for="item in frameworks" :key="item.frameworkId" :label="item.frameworkName" v-show="!item.hidden"
                        :value="item.frameworkId"/>
            </el-select>
          </el-form-item>
          <!--领域、测评点-->
          <el-form-item :label="$t('loc.standardsOrMeasures')" prop="measures" class="lesson-form-domain is-required"
              v-if="lesson.framework">
            <lesson-measure-select v-model="lesson.measures" @change="changeMeasures" :beforeChange="beforeChangeMeasures" :domains="domains" :lessonMeasureSelectNotShow="lessonMeasureSelectNotShow" class="input-select" ref="lessonMeasureSelect" :loading="loadingDomains"/>
          </el-form-item>
          <lesson-measure-label class="lg-margin-bottom-24" @change="changeMeasures" :beforeChange="beforeChangeMeasures" v-model="lesson.measures" />
        </template>
        <!--课程目标-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelObjectives')" prop="objective" ref="lessonObjective"
                      class="lesson-form-objective is-required">
          <el-input v-model="lesson.objective" @focus="fillObjective" :placeholder="$t('loc.lesson2NewLessonFormPlaceHolderObjectives')"
                    :autosize="{ minRows: 3}" resize="none" type="textarea" :maxlength="10000"/>
        </el-form-item>
        <!--课程材料-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelMaterials')" prop="material"
                      class="lesson-form-material is-required">
          <lesson-material-input v-model="lesson.material" ref="lessonMaterialInput"/>
        </el-form-item>
        <!--课程步骤-->
        <el-form-item :label="$t('loc.lesson2NewLessonFormLabelSteps')"
                      class="lesson-form-step"
                      v-if="lesson.steps.length"
                      :show-message="false">
          <template v-for="(step,index) in lesson.steps">
            <el-form-item :prop="`steps.${index}`"
                          class="step-left"
                          :style="{'margin-bottom' : showLessonTemplate(step, index) ? '24px': ''}"
                          :key="'step-content' + step.ageGroupName + index"
                          :rules="stepRules">
              <lesson-step-input class="lesson-step-input"
                                 :sourcePreview="false"
                                 :lessonId="lesson.id"
                                 v-show="quillFocus"
                                 @updateLessonImpStepAndSource="updateLessonImpStepAndSource"
                                 v-model="lesson.steps[index]"
                                 ref="lessonStepInput">
              </lesson-step-input>
            </el-form-item>
            <div v-if="k12Grade(step.ageGroupName) && isNotCenter && getLightAdaptModuleShow('eduProtocolsTemplatesFlag')" :key="step.ageGroupName">
              <lesson-template-preview
                ref="lessonTemplatePreviewRef"
                :templateType="lesson.templateType"
                :lessonId="lesson.id"
                :lessonLoading="convertLessonLoading"
                :lessonName="lesson.name"
                :ageGroup="step.ageGroupName"
                :lessonTemplate="lesson.steps[index].lessonTemplate"
                :edit="true"
                @updateLessonTemplateInfo="updateLessonTemplateInfo"
                @updateGenerateCustomTemplateLoading="updateGenerateLessonContentLoading"></lesson-template-preview>
            </div>
            <div v-if="(k12Grade(step.ageGroupName) || lectureSlidesExpandGrade(step.ageGroupName)) && isNotCenter && getLightAdaptModuleShow('lectureSlidesFlag')" :key="'lesson-slides-'+step.ageGroupName">
              <lesson-slides
                ref="lessonSlidesRef"
                class="lg-margin-top-24"
                :edit="true"
                :lessonId="lesson.id"
                :lessonName="lesson.name"
                :lessonLoading="convertLessonLoading"
                :activityType="lesson.activityType"
                @updateGenerateLessonSlidesLoading="updateGenerateLessonContentLoading"
                @updateGenerateLessonSlidesStreamLoading="updateGenerateLessonSlidesStreamLoading"
                ></lesson-slides>
            </div>
            <!-- 毕业生画像 -->
            <div v-if="k12Grade(step.ageGroupName) && isNotCenter && lesson.learnerProfiles && lesson.learnerProfiles.length > 0 && getLightAdaptModuleShow('portraitOfGraduateFlag')" :key="`portrait-${step.ageGroupName}`">
              <PortraitGraduateReview ref="portraitGraduateReview" :lesson="lesson" :ageGroup="step.ageGroupName" :isEdit="true"/>
            </div>
            <!--SOR 组件-->
            <!-- && step.showScienceOfReadingModel-->
            <div v-if="showScienceOfReadingModel(step)" :key="step.ageGroupName"
              :class="{ 'add-margin-t-10' :k12Grade(step.ageGroupName) && isNotCenter}">
              <ScienceOfReading
                ref="scienceOfReading"
                :lessonId="lesson.id"
                :lessonScienceOfReadingModel="step.lessonScienceOfReadingModel"
                :lessonName="lesson.name"
                :ageGroup="step.ageGroupName"
                @updateLessonScienceOfReadingModel="updateLessonScienceOfReadingModel"
                :edit="true"></ScienceOfReading>
            </div>
            <!-- 标准教学指导内容 -->
            <el-form-item
              v-if="getLightAdaptModuleShow('teacherGuideFlag')"
              ref="teachingTipsStandardTitleRef"
              v-show="isNotCenter"
              :prop="`steps.${index}.lessonStepGuides`"
              :key="'teaching-'+step.ageGroupName"
              class="remove-padding-l-0"
            >
              <div class="lesson-field-label display-flex align-items justify-content add-margin-b-10 add-margin-t-24" v-if="getLightAdaptModuleShow('teacherGuideFlag')">
                <div class="left-gradient title-gradient lg-margin-right-16"></div>
                <span class="font-size-18 font-color-primary">{{ $t('loc.teachingTipsForStandards') }}</span>
                <div class="right-gradient title-gradient lg-margin-left-16"></div>
              </div>
              <teaching-tips-standard :key="'teaching-tips-'+step.ageGroupName"
                                      :frameworkId="frameworkId"
                                      :domains="domains"
                                      v-model="lesson.steps[index].teachingTips"
                                      v-if="getLightAdaptModuleShow('teacherGuideFlag')"
                                      ref="teachingTipsStandard"/>
            </el-form-item>
             <!-- 教师指南填写 -->
             <el-form-item v-if="(!k12Grade(step.ageGroupName) || k12Grade(step.ageGroupName) && lesson.steps && lesson.steps[index].lessonStepGuides.length > 0) && isNotCenter && getLightAdaptModuleShow('teacherGuideFlag')"
                          :prop="`steps.${index}.lessonStepGuides`"
                          :key="'guide-'+step.ageGroupName"
                          class="remove-padding-l-0"
                          >
                <div class="lesson-field-label display-flex align-items justify-content add-margin-b-10" v-if="getLightAdaptModuleShow('teacherGuideFlag')">
                    <div class="left-gradient title-gradient lg-margin-right-16"></div>
                    <span class="font-size-18 font-color-primary">{{ $t('loc.typicalBehaviors') }}</span>
                    <div class="right-gradient title-gradient lg-margin-left-16"></div>
                    <typical-behaviors-tab v-if="isMappedFramework && getLightAdaptModuleShow('teacherGuideFlag')"
                                           :frameworkId="() => frameworkId"
                                           v-model="typicalBehaviorsType"></typical-behaviors-tab>
                </div>
              <domains-table-editor :key="'guide-editor-'+step.ageGroupName" :frameworkId="frameworkId"
                                    :domains="typicalBehaviorsType == 'PS' ? mappedDomains : domains"
                                    :isMapped="typicalBehaviorsType == 'PS'"
                                    :allMeasureUnMapped="allMeasureUnMapped"
                                    v-model="lesson.steps[index].lessonStepGuides" ref="domainsTableEditor"
                                    v-if="getLightAdaptModuleShow('teacherGuideFlag')">
              </domains-table-editor>
            </el-form-item>
            <!-- 差异教学内容 -->
            <!--UDL & CLR-->
            <el-form-item prop="universalDesignForLearning"  v-show="isAdaptedLesson || isNotCenter || step.universalDesignForLearningGroup || step.universalDesignForLearning || step.mixedAgeDifferentiations || adaptLoading"
              :key="'udl-' + step.ageGroupName"
              class="remove-padding-l-0">
              <UniversalDesignLearning :universalDesignForLearning="step.universalDesignForLearning"
                                        :mixedAgeDifferentiation="step.mixedAgeDifferentiations"
                                       :showMixedAge="step.showMixedAge"
                                       :universalDesignForLearningClassSpecial="step.universalDesignForLearningGroup ? step.universalDesignForLearningGroup: ''"
                                       :universalDesignForLearningCopy="universalDesignForLearningCopy"
                                       :universalDesignForLearningClassSpecialCopy="universalDesignForLearningClassSpecialCopy"
                                       :lessonId="lesson.id"
                                       :itemId="itemId"
                                       :lessonStepIndex="index"
                                       :changeAgeGroups="changeAgeGroups"
                                       :isAdaptedLesson="isAdaptedLesson"
                                       :lessonAgeGroup="step.ageGroupName"
                                       :isWeeklyPlanEdit="isWeeklyPlanEdit()"
                                       :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                                       ref="universalDesignLearning"
                                       :getNewLessonInfo="getNewLessonInfo"
                                       @updateUniversalDesignForLearning="updateUniversalDesignForLearning"
                                       @updateUniversalDesignForLearningClassSpecial="updateUniversalDesignForLearningClassSpecial"
                                       @generateUniversalDesignAndCLRData="updateGenerateUniversalDesignAndCLRData"
                                       @clearUniversalDesignForLearningClassSpecial="clearUniversalDesignForLearningClassSpecial"
                                       :selectedGroupId="selectedGroupId"
                                       v-show="getLightAdaptModuleShow('teacherGuideFlag') || step.universalDesignForLearningGroup || step.universalDesignForLearning || step.mixedAgeDifferentiations"></UniversalDesignLearning>
            </el-form-item>
            <!-- 文化内容 -->
            <el-form-item prop="culturallyResponsiveInstruction" v-show="isAdaptedLesson || isNotCenter || step.culturallyResponsiveInstruction || step.culturallyResponsiveInstructionGroup || adaptLoading"
              :key="'clrp-' + step.ageGroupName"
              class="remove-padding-l-0">
                <CulturallyLinguisticallyResponsive :selectedGroupId="selectedGroupId"
                                                    :ageGroupName="step.ageGroupName"
                                                    :lessonId="lesson.id"
                                                    :itemId="itemId"
                                                    :changeAgeGroups="changeAgeGroups"
                                                    :isCurriculum="isCurriculum()"
                                                    :isWeeklyPlanEdit="isWeeklyPlanEdit()"
                                                    :isEdit="true"
                                                    :step="step"
                                                    :isAdaptedLesson="isAdaptedLesson"
                                                    :adaptUDLAndCLROpen="adaptUDLAndCLROpen"
                                                    ref="culturallyLinguisticallyResponsive"
                                                    :getNewLessonInfo="getNewLessonInfo"
                                                    @updateLessonSource="updateLessonSource"
                                                    @clearCulturallyResponsiveInstructionGroup="clearCulturallyResponsiveInstructionGroup"
                                                    @generateUDLAndCLRData="updateGenerateUniversalDesignAndCLRData"
                                                    :culturallyResponsiveInstruction="step.culturallyResponsiveInstruction"
                                                    :culturallyResponsiveInstructionGeneral="step.culturallyResponsiveInstructionGeneral"
                                                    :culturallyResponsiveInstructionGroup="step.culturallyResponsiveInstructionGroup"
                                                    v-show="getLightAdaptModuleShow('teacherGuideFlag') || step.culturallyResponsiveInstruction || step.culturallyResponsiveInstructionGroup || (step.lessonClrAndSources && step.lessonClrAndSources.clr)"/>
<!--                <el-input v-model="step.culturallyResponsiveInstruction"
                :placeholder="'Culturally and Linguistically Responsive Practice'"
                :autosize="{ minRows: 3}" resize="none" type="textarea" :maxlength="10000"/>-->
            </el-form-item>
            <!-- quiz -->
            <el-form-item v-if="(k12Grade(step.ageGroupName) || lectureSlidesExpandGrade(step.ageGroupName)) && isNotCenter && getLightAdaptModuleShow('standardsAssessmentFlag')" :key="'quiz-' + step.ageGroupName"
              class="remove-padding-l-0">
              <LessonQuiz
                  v-model="lesson.steps[index].questions"
                  v-if="getLightAdaptModuleShow('standardsAssessmentFlag')"
                  :ageGroupName="step.ageGroupName"
                  ref="lessonQuiz"
                  :showGenerateButton="isAddLesson && !convertLessonLoading"
                  :lessonId="lesson.id"
                  :edit="true"
                  :showDownload="!!(!(!generaterdLessoncompleted && convertLessonLoading) && lesson.id)"
                  :generateLessonLoading="convertLessonLoading"
                  :domains="domains"
                  @update:quizRegenerateLoading="handleQuizRegenerateLoading"/>
            </el-form-item>
          </template>
        </el-form-item>
        <!-- DLL -->
        <el-form-item class="lesson-dll" v-if="dllOpen && isNotCenter">
          <span slot="label">
             <img src="@/assets/img/lesson2/lessons2-lesson-resources.png"
                  class="lessons2-lesson-resources"  :alt="$t('loc.lessons2LessonDLLTitle')"/>
            <div class="flex-center-center lessons2-lesson-resources-info">
              <span style="font-weight: 600;">{{ $t('loc.lessons2LessonDLLTitle') }}</span>
<!--              <dll-tips/>-->
            </div>
          </span>
          <lesson-dll-input class="add-margin-t-20" v-model="lesson.dlls" :title="lesson.name" ref="dlls" @reopenDllData="dealDllData"/>
        </el-form-item>
        <!--与家长分享-->
        <el-form-item class="lesson-share" v-show="isNotCenter">
          <span slot="label">
            <img src="@/assets/img/lesson2/lessons2-lesson-resources2.png"
                 class="lessons2-lesson-resources"  :alt="$t('loc.lessons2LessonResources')"/>
            <div class="flex-center-center lessons2-lesson-resources-info">
               <span style="font-weight: 600;">{{ $t('loc.lessons2LessonResources') }}</span>
            </div>
          </span>
          <!-- 家庭活动 -->
          <template v-for="step in lesson.steps" >
            <div v-show="isShowAtHome">
              <el-form-item :label="'At-Home Activities' + (lesson.steps.length > 1 ? ' (' + step.ageGroupName + ')' : '')" prop="homeActivity"
                :key="'homeActivity-' + step.ageGroupName"
                class="w-full" ref="homeActivity">
                <el-input v-model="step.homeActivity"
                  :placeholder="'At-Home Activities' + (lesson.steps.length > 1 ? ' (' + step.ageGroupName + ')' : '')"
                  :autosize="{ minRows: 3}" resize="none" type="textarea" :maxlength="10000"/>
              </el-form-item>
            </div>
          </template>
          <el-form-item :label="$t('loc.lessons2LessonBook')" class="lesson-form-book">
            <book-select v-model="lesson.book"/>
          </el-form-item>
          <el-form-item :label="$t('loc.lessons2LessonVideo')" class="lesson-form-video">
            <video-book-select v-model="lesson.videoBook" :book="lesson.book"/>
          </el-form-item>
          <el-form-item :label="$t('loc.lessons2LessonAttachments')" class="lesson-form-attachment">
            <attachment-uploader v-model="lesson.attachments"/>
          </el-form-item>
        </el-form-item>
        <!-- 操作 -->
        <!-- 如果是在编辑课程弹窗里，不显示操作按钮 -->
        <div v-if="!inDialog" class="new-lesson-operation align-items" :class="{'mobile-new-lesson-operation': lgIsMobile}">
          <el-checkbox v-if="showRecommendCheckbox && !inCurriculumGenie && (generaterdLessoncompleted || !convertLessonLoading)" v-model="recommendToAgency" style="position: absolute;left: 0" @change="handleRecommendToAgencyChange">{{ $t('loc.saveToAgencyWideLesson') }}</el-checkbox>
          <!-- <el-button @click="submitDraft" plain :loading="saving === 2" type="primary" v-if="!inWeeklyPlannDialog"
                     :disabled="!generaterdLessoncompleted &&  convertLessonLoading">
            {{ $t('loc.plan_saveDraft') }}
          </el-button> -->
          <el-button plain :disabled="isActionDisabledWhileGenerating" type="primary" :class="{'add-margin-l-20':inWeeklyPlannDialog}" @click="handleClickPreview">
            <template #icon>
              <i class="add-margin-r-4 lg-icon lg-icon-eye"></i>
            </template>
            {{ $t('loc.preview') }}
          </el-button>
          <!-- 更新和重新生成按钮 -->
          <el-popover
            placement="top"
            :width="lgIsMobile ? 300 : 390"
            :append-to-body="false"
            popper-class="enhance-lesson-popover"
            v-model="showEnhanceLessonPlanGuide"
            ref="settingGuide"
            trigger="manual">
            <div class="text-white">
              <!-- 引导文字 -->
              <div class="lg-margin-bottom-24 word-break text-left">
                <!-- 用户引导内容 -->
                <span class="title-font-14">
                  Enhance & Refine: Adapt lessons like a pro for better alignment, with version history to track changes and revert anytime.
                </span>
                <img src="~@/assets/img/lesson2/lessonPlan/lesson_enhance.png" class="lg-margin-top-12">
              </div>
              <div class="display-flex flex-justify-end gap-6 align-items">
                <el-button type="text" @click="handelEnhanceGuide(false)">{{ $t('loc.unitPlannerRedesignGuide2') }}</el-button>
                <el-button type="primary" @click="handelEnhanceGuide(true)">Try Now</el-button>
              </div>
            </div>
            <!-- 由于 adapt 之后 enhance会把 adapt 的数据丢失，所以暂时把 enhance 按钮隐藏 -->
            <el-button slot="reference"
                       class="ai-btn"
                       :disabled="isActionDisabledWhileGenerating"
                       @click="openRedesignLessonDialog">
              <i class="lg-icon lg-icon-generate font-bold" style="margin-right: 4px; width: 20px; height: 20px;"/>
              {{ $t('loc.enhanceLesson') }}
            </el-button>
          </el-popover>
          <el-button :disabled="isActionDisabledWhileGenerating" type="primary" @click="handlePublishClick"
                     :loading="saving === 1" v-if="isNeedManualPublish">
            {{ $t('loc.lessons2Save') }}
          </el-button>
          <!-- adapt 按钮显示，在编辑界面并且不是草稿状态-->
          <adapt-lesson-button v-if="lesson && lesson.status !== 'DRAFT' && !isAddLesson"
                               @lessonAdapt="handleLessonAdapt"
                               :adapt-lesson-id="lesson.id"
                               :disabled-adapt-tooltip="true"
                               :disabled-adapt-btn="isActionDisabledWhileGenerating"/>
        </div>
          </el-form>
        </div>
    </div>

    <!-- 更新和重新生成课程弹窗 -->
    <LessonEnhanceDialog
                          :dialogVisible.sync="regenerateLessonVisible"
                          :lessonMeasures="lesson.measures"
                          :lessonActivityTime="lesson.activityTime"
                          :domains="domains"
                          @confirmRedesignLesson="confirmRedesignLesson">
    </LessonEnhanceDialog>

    <!--加载中-->
    <div v-if="loading===1 && !(isAddLesson && addLessonStep === 1)" class="content">
      <lesson-detail-skeleton/>
    </div>

    <lesson-detail-dialog
      :show.sync="showDetail"
      :downloadLesson="true"
      :lessonId="lesson && lesson.id"
      :lessonName="lesson && lesson.name"
      :lessonFrameworkId="lesson && lesson.framework && lesson.framework.frameworkId"
      :lesson="lesson"
      @beforeDownload="handleBeforeDownload">
      <div style="padding: 0 30px;">
        <lesson-info-new :lesson="previewLesson" :domains="domains"/>
      </div>
    </lesson-detail-dialog>
    <!--年龄组确认删除弹框-->
    <el-dialog :visible.sync="showAgeConfirm" class="confirm-dialog" width="450px" style="margin-top: 15vh;" :append-to-body="true">
      <div slot="title" style="height: 15px;font-size: 20px">{{ $t('loc.confirmation') }}</div>
      <span class="font-size-16">{{ ageDeleteTips }}</span>
      <div slot="footer">
        <el-button @click="closeAgeConfirmDialog" class="text-base" style="padding: 10px 15px;">{{
            $t('loc.cancel')
          }}
        </el-button>
        <el-button type="danger" @click="ageConfirmDialog" class="text-base m-l-md"
                   style="padding: 10px 15px;">{{ $t('loc.confirm') }}
        </el-button>
      </div>
    </el-dialog>
<!--    导入最近删除年龄组课程信息确认框-->
    <el-dialog  :visible.sync="showImportLatelyAgeGroupInfo" class="confirm-dialog" width="450px" style="margin-top: 15vh;" :append-to-body="true" :close-on-click-modal="false">
      <div slot="title" style="height: 15px;font-size: 20px">{{ $t('loc.confirmation') }}</div>
      <span class="font-size-16">{{ $t('loc.lessonImportAgeGroupTip') }}</span>
      <div slot="footer">
        <el-button @click="closeImportLatelyAgeGroupInfoDialog" class="text-base" style="padding: 10px 15px;">{{
            $t('loc.cancel')
          }}
        </el-button>
        <el-button type="primary" @click="importLatelyAgeGroupInfoConfirmDialog" class="text-base m-l-md"
                   style="padding: 10px 15px;">{{ $t('loc.confirm') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 课程历史抽屉 -->
    <lesson-history-drawer
      ref="historyDrawer"
      :visible.sync="showHistoryDrawer"
      :lesson-id="lesson.id"
      @restore="handleRestoreHistory"
    />
    <!-- 历史版本回滚 loading 使系统已有的 -->
  <!--  展示 PersonalizePlan-->
  <PersonalizePlan @updateGenerateUniversalDesignAndCLRData="generateUniversalDesignAndCLRData"
                   :itemIds="[itemId]"
                   :lessonIds="[lesson.id]"
                   ref="personalizePlan"></PersonalizePlan>
    </div>
  </div>
</template>
<script>
import DLLApi from '@/api/dll/DLL'
import { AssistantToolStateManager } from '@/mixins/assistantToolStateManager'
import { equalsIgnoreCase, getRecommendToAgencyKey,extractTextFromHtml } from '@/utils/common'
import constants, { PublicLessonAssistantInfoActivityType } from '@/utils/constants'
import frameworkUtils from '@/utils/frameworkUtils.js'
import lessonUtils from '@/utils/lessonUtils'
import tools from '@/utils/tools'
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import AssistantTool from '@/views/modules/lesson2/lessonLibrary/assistant/AssistantToolbar'
import LessonDetailDialog from '@/views/modules/lesson2/lessonLibrary/components/LessonDetailDialog'
import DllTips from '@/views/modules/lesson2/lessonLibrary/components/LessonDll/DllTips'
import LessonDownload from '@/views/modules/lesson2/lessonLibrary/components/LessonDownload.vue'
import LessonEnhanceDialog from '@/views/modules/lesson2/lessonLibrary/components/LessonEnhanceDialog.vue'
import LessonHistoryDrawer from '@/views/modules/lesson2/lessonLibrary/components/LessonHistoryDrawer.vue'
import LessonInfoNew from '@/views/modules/lesson2/lessonLibrary/components/LessonInfoNew'
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton'
import CulturallyLinguisticallyResponsive from '@/views/modules/lesson2/lessonLibrary/editor/CulturallyLinguisticallyResponsive'
import LessonDllInput from '@/views/modules/lesson2/lessonLibrary/editor/dll'
import LessonMaterialInput from '@/views/modules/lesson2/lessonLibrary/editor/lessonMaterialInput/index'
import LessonMeasureLabel from '@/views/modules/lesson2/lessonLibrary/editor/LessonMeasureLabel'
import LessonMeasureSelect from '@/views/modules/lesson2/lessonLibrary/editor/LessonMeasureSelect'
import LessonQuiz from '@/views/modules/lesson2/lessonLibrary/editor/LessonQuiz.vue'
import LessonStepInput from '@/views/modules/lesson2/lessonLibrary/editor/LessonStepInput'
import ScienceOfReading from '@/views/modules/lesson2/lessonLibrary/editor/ScienceOfReading'
import UniversalDesignLearning from '@/views/modules/lesson2/lessonLibrary/editor/UniversalDesignLearning'
import WelcomeDialog from '@/views/modules/lesson2/lessonLibrary/editor/WelcomeDialog.vue'
import PersonalizePlan from '@/views/modules/lesson2/lessonPlan/components/PersonalizePlan'
import LessonTemplatePreview from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplatePreview'
import LessonTemplateSelectModal from '@/views/modules/lesson2/unitPlanner/components/eduprotocols/LessonTemplateSelectModal'
import LessonSlides from '@/views/modules/lesson2/unitPlanner/components/lessonSlides/index.vue'
import { mapState } from 'vuex'
import Lesson2 from '../../../../../api/lessons2'
import AppUtil from '../../../../../utils/app'
import AttachmentUploader from '../../component/attachmentUploader'
import BookSelect from '../../component/BookSelect'
import Loading from '../../component/loading'
import MediaUploader from '../../component/mediaUploader'
import VideoBookSelect from '../../component/VideoBookSelect'
import DomainsTableEditor from './DomainsTableEditor'
import TeachingTipsStandard from './TeachingTipsStandard'
import TypicalBehaviorsTab from './TypicalBehaviorsTab.vue'
import EditorTools from '@/utils/lessonMaterialEditorTools'
import LessonApi from '@/api/lessons2'
import AdaptLessonButton from '@/views/modules/lesson2/lessonPlan/components/AdaptLessonButton'
import PortraitGraduateReview from '@/views/modules/lesson2/unitPlanner/components/editor/PortraitGraduateReview.vue'
export default {
  name: 'NewLesson',
  components: {
    FeedbackForm,
    DllTips,
    LessonDllInput,
    LessonInfoNew,
    LessonMeasureLabel,
    LessonMeasureSelect,
    LessonStepInput,
    LessonMaterialInput,
    UniversalDesignLearning,
    ScienceOfReading,
    CulturallyLinguisticallyResponsive,
    LessonQuiz,
    Loading,
    PersonalizePlan,
    MediaUploader,
    BookSelect,
    VideoBookSelect,
    AttachmentUploader,
    LessonDetailDialog,
    LessonDetailSkeleton,
    DomainsTableEditor,
    TeachingTipsStandard,
    TypicalBehaviorsTab,
    LessonTemplatePreview,
    LessonTemplateSelectModal,
    LessonEnhanceDialog,
    WelcomeDialog,
    AssistantTool,
    LessonDownload,
    LessonHistoryDrawer,
    LessonSlides,
    AdaptLessonButton,
    PortraitGraduateReview
  },
  props: ['lessonId', 'type', 'inDialog', 'selectedGroupId', 'itemId', 'batchEdit', 'singleEditLesson', 'planStatus', 'isAdaptedLesson', 'showAiAssistant', 'unitPlannerBatchAdapt', 'singleAdaptLesson', 'aiAssistantDllOpen', 'centerName', 'weeklyEditLesson', 'adaptLoading', 'inWeeklyPlannDialog', 'selectedFramework', 'showAssistantTool', 'adaptLesson'],
  updated: function () {
    this.lessonEditOpenUrlWithWebAndIPad()
  },
  beforeRouteLeave (to, from, next) {
    // 如果存在临时草稿 ID，清理它
    this.cleanupTempDraft()
    
    this.handlePageChange()
    // 获取当前用户的缓存键
    let storageKey = 'lessonFrameworks' + this.currentUserId

    // 从 sessionStorage 中获取缓存的框架
    let cachedFrameworks = sessionStorage.getItem(storageKey)

    // 如果缓存中存在数据
    if (cachedFrameworks) {
      // 解析缓存数据
      let frameworks = JSON.parse(cachedFrameworks)

      // 只保留前四个框架
      if (frameworks.length > 4) {
        frameworks = frameworks.slice(0, 4)
        // 更新缓存
        sessionStorage.setItem(storageKey, JSON.stringify(frameworks))
      }
    }

    next()
  },
  computed: {
    ...mapState({
      unitPlannerFrameworks: state => state.curriculum.frameworks,
      refreshExtFramework: state => state.curriculum.refreshExtFramework,
      currentUser: state => state.user.currentUser,
      open: state => state.common.open,
      enableMixedAgeGroup: state => state.lesson.enableMixedAgeGroup,
      bloomQuizSetting: state => state.curriculum.bloomQuizSetting, // quiz bloom 设置
      templateType: state => state.lesson.templateType, // 课程模版类型
      eduProtocolsTemplateApplyOpen: state => (state.common.open && state.common.open.eduProtocolsTemplateApplyOpen) || state.curriculum.isCG, // 是否使用课程模板功能
      isCG: state => state.curriculum.isCG, // 是否是 Curriculum Genie
      lgIsMobile: state => state.common.lgIsMobile, // 添加移动端状态
      showBanner: state => state.cgAuth.showBanner, // 是否显示 Curriculum Genie 插件安装横幅
      guideFeatures: state => state.common.guideFeatures, // 功能引导
    }),
    lessonData () {
      // 如果课程生成完成，则返回课程数据
      if (this.generaterdLessoncompleted) {
        return JSON.parse(JSON.stringify(this.lesson))
      }
      return {}
    },
    // // 监听数据，判断 Update and Regenerate 课程按钮是否可用
    // updateAndRegenerateButton () {
    //   // 判断是否有 assistantAdaptInfo 且长度大于 10
    //   const hasValidAssistantInfo = this.lesson.assistantAdaptInfo && this.lesson.assistantAdaptInfo.length > 10

    //   // 如果是从周计划进入，直接返回判断结果
    //   if (this.inWeeklyPlannDialog || this.showAiAssistant) {
    //     return hasValidAssistantInfo
    //   }

    //   // 否则根据是否显示 AI 助手决定
    //   return false
    // },
    isOpenAdaptUDLAndCLR () {
      return this.open && this.open.adaptUDLAndCLROpen
    },
    dllOpen () {
      return (this.open && this.open.dllopen) || this.aiAssistantDllOpen
    },
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    title () {
      return this.isAddLesson && this.steaddLessonStep === 1 ? '' : (this.type === 'Draft' ? this.$t('loc.lessons2EditLessonTitle') : this.$t('loc.lesson2NewLessonTitle'))
    },
    customTheme () {
      return this.checkCustomTheme()
    },
    isFromIpad () {
      return tools.isComeFromIPad()
    },
    /**
     * 监听页面内数据是否变化的对象
     */
    listenChange () {
      const { lesson, ageValues, themeIds, frameworkId, previewLesson, domains } = this
      return { lesson, ageValues, themeIds, frameworkId, previewLesson, domains }
    },
    // 是否开启了推荐到机构课程开关
    recommendToAgencyOpen () {
      return this.open && this.open.recommendLessonToAgencyOpen && !this.open.educator
    },
    // 是否显示 SOR 模块
    showScienceOfReadingModel() {
      return function (step) {
        // 只有在数据库中设置 step.showScienceOfReadingModel 为 true 才可以看得见 SOR 模块
        return this.ltGrade2(step.ageGroupName) && this.isNotCenter && !this.unitDetail && !this.isCG && step.showScienceOfReadingModel
      }
    },
    // 是否是 K-12 年级
    k12Grade () {
      return function (ageGroupName) {
        let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
        return k12Grades.includes(ageGroupName)
      }
    },
    lectureSlidesExpandGrade () {
      return function (ageGroupName) {
        return ['TK (4-5)'].includes(ageGroupName)
      }
    },
    // 小于 Grade 2 的年龄
    ltGrade2 () {
      return function (ageGroupName) {
        let k12Grades = ['PS/PK (3-4)', 'TK (4-5)', 'K (5-6)', 'Grade 1', 'Grade 2']
        return tools.isInCurrentAgeGroup(ageGroupName, k12Grades)
      }
    },
    // unit 详情改编课程时显示操作栏
    unitDetail() {
      return equalsIgnoreCase(this.$route.name, 'unitDetail')
    },
    elFromZindexClass () {
      if (this.windowWidth <= 1515 && this.showAiAssistant) {
        return {
          // zIndex: this.elFormIndex,
          width: '914px'
        }
      }
      return {
        // zIndex: this.elFormIndex
      }
    },
    // 当打开 ai 助手时，需要根据屏幕宽度调整 ai 助手宽度
    assistantToolbarWidthStyle () {
      if (this.aiToolEntryShow) {
        return { width: '162px' }
      }
      // 当打开ai 助手时且屏幕小于1515px
      if (this.windowWidth <= 1515 && this.showAiAssistant) {
        return { width: '375px' }
      }
      return { width: '412px' }
    },
    // 当打开ai 助手时且屏幕小于1515px，需要动态调整下拉框宽度
    formItemLongStyle () {
      if (this.windowWidth <= 1515 && this.showAiAssistant) {
        return { width: '280px' }
      }
    },
    formItemShortStyle () {
      if (this.windowWidth <= 1515 && this.showAiAssistant) {
        return { width: '130px' }
      }
    },
    // 课程主题选择框样式
    lessonThemeMarginStyle () {
      // 如果课程模板选择框不显示，则不设置样式
      if (!(this.lesson.id && this.isNotCenter && this.eduProtocolsTemplateApplyOpen)){
        return
      }
      if (this.windowWidth <= 1515 && this.showAiAssistant) {
        return { marginLeft: '0px', marginRight: '20px' }
      } else {
        return { marginLeft: '20px', marginRight: '0px' }
      }
    },
    // 判断是否在 curriculum-genie 页面
    inCurriculumGenie () {
      return this.inCurriculumGeniePage()
    },
    feedbackTitle () {
      return this.$t('loc.unitPlannerFeedback')
    },
    feedbackInputPlaceholder () {
      return this.$t('loc.unitPlannerFeedbackPlaceholder')
    },
    feedbackSubmit () {
      return this.$t('loc.unitPlannerFeedbackSubmit')
    },
    // 是否为 center 课程
    isNotCenter () {
      // 只有周计划或 FOLC 使用 AI 生成课程时才做判断
      if (this.inWeeklyPlannDialog || this.showAiAssistant || this.showAssistantTool) {
        return !this.centerName && this.subclassCenter !== 'CENTER' && this.subclassCenter !== 'STATION'
      }
      return !this.lesson.activityType || (this.lesson.activityType && this.lesson.activityType !== 'Center' && this.lesson.activityType !== 'Station')
    },
    /**
     * 是否显示课程模板
     * @returns {function(*, *): *}
     */
    showLessonTemplate() {
      return function (step, index) {
        return (this.k12Grade(step.ageGroupName) || this.lectureSlidesExpandGrade(step.ageGroupName)) && this.isNotCenter && this.eduProtocolsTemplateApplyOpen
      }
    },
    // 年龄组删除时的提示
    ageDeleteTips () {
      if (this.lesson && this.lesson.assistantAdaptInfo && this.lesson.assistantAdaptInfo.includes('"activityType":"CENTER"')) {
        return this.$t('loc.lesson2NewLessonFormLabelCenterAgeGroupDeleteTips')
      }
      return this.$t('loc.lesson2NewLessonFormLabelAgeGroupDeleteTips')
    },
    readOnlyAgeGroup () {
      return this.ageGroup && this.ageGroup.filter(item => this.ageValues.includes(item.value)).map(item => item.name) || []
    },
    readOnlyFramework () {
      const framework = this.frameworks && this.frameworks.filter(item => item.frameworkId === this.frameworkId)[0] || {}
      return framework ? framework.frameworkName : ''
    },
    readOnlyMeasures () {
      return this.lesson.measures && this.lesson.measures.map(item => item.children).flat().map(item => item.abbreviation || item.name).join('; ') || ''
    },
    // 是否需要手动发布
    isNeedManualPublish () {
      return !this.publishAfterGenerate
    },
    // 测评点映射表（性能优化 - 缓存）
    measureMap () {
      const map = new Map()
      if (this.domains) {
        for (let domain of this.domains) {
          if (domain.children) {
            for (let measure of domain.children) {
              if (measure.abbreviation) {
                map.set('abbreviation-' + measure.abbreviation.trim(), measure)
              }
              if (measure.mappingAbbr) {
                map.set('mappingAbbr-' + measure.mappingAbbr.trim(), measure)
              }
            }
          }
        }
      }
      return map
    },
    // 映射域的测评点映射表（性能优化 - 缓存）
    mappedMeasureMap () {
      const map = new Map()
      if (this.mappedDomains) {
        for (let domain of this.mappedDomains) {
          if (domain.children) {
            for (let measure of domain.children) {
              if (measure.abbreviation) {
                map.set('abbreviation-' + measure.abbreviation.trim(), measure)
              }
              if (measure.mappingAbbr) {
                map.set('mappingAbbr-' + measure.mappingAbbr.trim(), measure)
              }
            }
          }
        }
      }
      return map
    },
    // 是否全部测评点无映射
    allMeasureUnMapped () {
      // 当前实施步骤的 optklf 测评点
      let ptklfMeasureIds = (this.lesson && this.lesson.measures) ? this.lesson.measures.flatMap(item => item.children).map(item => item.id) : []
      // 全部可以映射 psc 框架的 ptklf 测评点
      let canMappedPtklfMeasureIds = []
      // 遍历映射测评点收集所有的 ptklf 测评点
      this.mapFrameworkData.forEach(measure => {
        Object.keys(measure.mappedMeasures).forEach(key => {
          let measureIds = measure.mappedMeasures[key] && measure.mappedMeasures[key].map(x => x.id)
          canMappedPtklfMeasureIds = canMappedPtklfMeasureIds.concat(measureIds)
        })
      })
      // 未匹配的测评点
      return ptklfMeasureIds.every(item => !canMappedPtklfMeasureIds.includes(item))
    },
    // 生成时是否禁用操作
    isActionDisabledWhileGenerating() {
      // 课程生成未完成
      let lessonNotReady = !this.generaterdLessoncompleted && this.convertLessonLoading;
      // UDL 和 CLR 生成未完成
      let udlClrNotReady = this.udlAndClrGenerateHasListener &&
                           (!this.generateUniversalDesignForLearningCompleted || !this.generateCLRCompleted);
      return lessonNotReady || udlClrNotReady;
    },
    // 是否显示改编课程引导
    showEnhanceLessonPlanGuide () {
      const showGuide = this.isNewLesson && this.guideFeatures && this.guideFeatures.showCurriculumLessonPlanEnhanceGuide && (this.generaterdLessoncompleted || !this.convertLessonLoading) && this.isAddLesson
      if (showGuide) {
        this.$analytics.sendEvent('cg_lesson_enhance_guide_shown')
      }
      return showGuide
    },
    // 是否可以进行自动保存
    canAutoSave() {
      return !this.convertLessonLoading &&
             this.saving === 0 &&
             !this.quizRegenerateLoading &&
             this.lastPublishTime &&
             this.changesCounter > 0
    },
    // 是否显示来自 Unit
    isFromUnitLesson () {
      return !!(this.fromUnit && this.fromUnit.id)
    },
    /**
      * 是否显示 Classroom Type 部分
      */
    showClassroomType () {
      return this.lesson && lessonUtils.showClassroomType(this.lesson.ages.map(x => x.name), this.lesson.activityType) && !this.lesson.adaptedModuleSwitch
    },
  },
  data () {
    let _this = this
    // 封面字段的联合校验
    let checkCover = (rule,value,callback) => {
      // 若没有图片、视频和外部链接，则进行提示
      if (!value || ((value && !value.externalMediaUrl && !value.externalMediaUrl && !value.id))) {
        return callback(new Error(this.$t('loc.fieldReq')))
      } else {
        callback()
      }
    }
    return {
      lesson: { // 课程信息
        id: null, // 课程 ID
        cover: null, // 课程封面，对象，id、name、url
        name: null, // 课程名称
        ages: [], // 年龄组，对象数组，name、value
        prepareTime: null, // 准备时长
        activityTime: null, // 活动时长
        activityType: null, // 活动类型
        activityTheme: null,// 活动主题
        themes: [], // 课程主题，对象数组，id、name
        framework: null, // 框架，对象，frameworkId、frameworkName
        measures: [], // 领域和测评点，对象数组，id、name、abbreviation、children
        objective: null, // 课程目标
        material: null, // 课程材料，对象，media、description、attachmentMedias (附件，对象数组)
        steps: [], // 课程步骤，对象数组，content、media、ageGroup
        book: null, // 书，对象
        videoBook: null, // 视频书，对象
        attachments: [], // 附件，对象数组
        dlls: [],// DLL，对象数组：id、title、content、media、languages（对象数组：langCode、content、lang）
        coverExternalMediaUrlId: null, // 封面外部链接视频id
        assistantAdaptInfo: null, // AI 助手信息
        classroomType: 'IN_PERSON' // 课堂类型：IN_PERSON 或 VIRTUAL
      },
      showDetail: false,
      previewLesson: {},

      showAgeConfirm: false, // 年龄组删除--是否展示确认提示弹框
      ageValuesBeforeDelete: [], // 确认删除前的年龄组
      ageValues: [], // 当前选择的年龄组值，字符串数组
      oldAgeValues: [], // 更新之前年龄组值，字符串数组
      stepsCache: [], // 课程步骤缓存，对象数组
      ageGroup: null, // 当前登录人地区年龄组
      changeAgeGroups: false, // 是否是发生了年龄组改变
      initLessonData: null, // 初始化结束的课程数据副本
      times: null, // 可选时间
      themeIds: [], // 当前选择的主题，字符串数组
      themes: null, // 可选主题
      frameworkId: null, // 当前选择的框架 ID
      cacheStepSorForm: null, // 缓存的 SOR 表单数据
      oldFrameworkId: null, // 上一次选择的框架 ID
      frameworks: null, // 可选框架
      domains: null, // 可选领域和测评点
      saving: 0, // 保存状态,1 正在发布、2正在保存草稿
      changesCounter: 0, // 变更计数器
      autoSavingTimer: null, // 自动保存计时器
      loading: 0, // 课程信息加载状态,0未加载，1加载中，2已加载
      adaptUDLAndCLROpen: false, // 是否开启 UDL 和 CLR
      languages: null, // 语言信息
      langCodes: [], // 选择的语言
      isShowAtHome: true, // 是否显示家庭活动
      isSelectMultiple: false, // 是否多选
      // 验证规则
      rules: {
        // cover: [{
          // type: 'object', message: this.$t('loc.fieldReq'), required: true,
          // fields: {
          //   id: { message: this.$t('loc.fieldReq'), required: true, whitespace: true }
          // }
          // validator: checkCover,type: 'object'
        // }],
        name: [{ message: this.$t('loc.fieldReq'),required: true, whitespace: true }],
        ages: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        // themes: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        framework: [{ type: 'object', message: this.$t('loc.fieldReq'), required: true }],
        measures: [{ type: 'array', message: this.$t('loc.fieldReq'), required: true }],
        objective: [{ message: this.$t('loc.fieldReq'), required: true, whitespace: true }],
        material: [{
          type: 'object',
          message: this.$t('loc.fieldReq'),
          required: true,
          fields: {
            description: { message: this.$t('loc.fieldReq'), required: true, whitespace: true }
          }
        }]
      },
      stepRules: {
        type: 'object',
        required: true,
        fields: {
          content: { message: this.$t('loc.fieldReq'), required: true, transform: value => this.innerText(value) }
        }
      },
      toResetMeasureIds: [], // 要重置的测评点 ID 列表
      oldMeasureIds: [], // 更新前的旧测评点
      removeMeasureIds: [], // 移除的测评点 id 数组
      addMeasureIds: [], // 添加的测评点 id 数组
      haveGuides: false, // 是否有教师指导内容
      aiFullScreenFlag: false, // 是否全屏
      aiToolEntryShow: false, // 是否展示ai入口
      lessonInfoHasChanged: false, // 页面内数据是否有变化
      rateDialogVisible: false, // 是否弹出评分弹出框
      ageAppropriatenessRate: 0, // age appropriateness 评分
      lessonCoverLoading: false, // 正在生成课程封面的loading
      convertLessonLoading: false, // 正在生成课程的loading
      promptUsageRecordIds: [], // 消息回调的 promptUsageRecordId，后续用于反馈
      generaterdLessoncompleted: false, // 表示课程是否生成完毕
      redesignLessonButton: false, // 在生成时课程内容被清空用计算属性判断的话会导致按钮没了再出现
      defaultFeedbackResult: {
        feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
        feedBackResult: '',
        feedbackData: {
          feedback: '',
          feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
          feedbackLevel: [0, 0, 0, 0]
        }
      },
      recommendToAgency: false, // 推荐到机构课程库复选框值
      showRecommendCheckbox: false, // 是否显示推荐课程的复选框
      shouldGenerateUniversalDesignAndCLRData: false, // 是否开始生成 UDL 和 CLR 数据
      // showAiAssistant: false, // 是否展示助手，默认关闭
      currentAgeGroupName: undefined, // 当前年龄组名称
      elFormIndex: 1,
      currentRemoveAgeGroup: '', // 当前删除的年龄组
      latelyRemoveAgeGroupAndStepMap: null, // 最后删除的年龄组以及其 step , key : ageValue , value : step
      showImportLatelyAgeGroupInfo: false, // 是否显示要导入最近删除课程信息弹窗
      currentAddAgeValue: null, // 当前要添加的年龄组的 val
      currentAgeGroupStepsPart: [
        'content',
        'universalDesignForLearning',
        'lessonClrAndSources',
        'homeActivity',
        'lessonStepGuides'
      ], // 存储需要判断内容是否有更改的字段属性
      computedFeedbackStyle: {
        right: '0px',
        top: '0px',
        position: 'fixed'
      },
      universalDesignBeginGenerate: false,
      windowWidth: 0,// 屏幕宽度
      subclassCenter: null, // 子组件是否有 center 属性
      regenerateLessonVisible: false, // 重新生成课程内容弹窗
      hasSaveDraft: false, // 当前组件活跃时调用了保存草稿
      beforeLesson: null,
      lessonMeasureSelectNotShow: null, // 控制课程数据还原时组件不被弹开
      quillFocus: true, // quill 会有文本聚焦的问题，增加参数不进行聚焦
      skipFrameworkWatch: false, // 是否跳过框架的 watch
      mappedDomains: [], // 映射的领域
      mapFrameworkData: [], // 映射的框架数据
      typicalBehaviorsType: 'PS', // 典型行为类型
      generatingCustomTemplate: false, // 是否正在生成自定义模板
      showWelcomeDialog: false,
      showHistoryDrawer: false, // 是否显示历史抽屉
      lastPublishTime: null, // 记录 Lesson 上次保存发布的时间，初始设为1970年，确保第一次能正常保存
      autoSaveDebounceTimer: null, // 防抖定时器
      forceAutoSaveTimer: null, // 强制自动保存定时器
      autoSaveWaitTime: 10000, // 自动保存等待时间（10秒）
      debounceWaitTime: 3000, // 防抖等待时间（3秒）
      forceSaveInterval: 120000, // 强制保存间隔（2分钟）
      sourceLesson: { // 课程信息
        id: null, // 课程 ID
        cover: null, // 课程封面，对象，id、name、url
        name: null, // 课程名称
        ages: [], // 年龄组，对象数组，name、value
        prepareTime: null, // 准备时长
        activityTime: null, // 活动时长
        activityType: null, // 活动类型
        activityTheme: null,// 活动主题
        themes: [], // 课程主题，对象数组，id、name
        framework: null, // 框架，对象，frameworkId、frameworkName
        measures: [], // 领域和测评点，对象数组，id、name、abbreviation、children
        objective: null, // 课程目标
        material: null, // 课程材料，对象，media、description、attachmentMedias (附件，对象数组)
        steps: [], // 课程步骤，对象数组，content、media、ageGroup
        book: null, // 书，对象
        videoBook: null, // 视频书，对象
        attachments: [], // 附件，对象数组
        dlls: [],// DLL，对象数组：id、title、content、media、languages（对象数组：langCode、content、lang）
        coverExternalMediaUrlId: null, // 封面外部链接视频id
        assistantAdaptInfo: null, // AI 助手信息
        classroomType: 'IN_PERSON' // 课堂类型：IN_PERSON 或 VIRTUAL
      },
      editPromptVisible: false, // 是否显示编辑提示弹窗
      isAddLesson: false, // 是否是添加课程
      addLessonStep: 1, // 添加课程步骤 1 填写 Prompt 2 提交生成
      generatingLessonContent: false, // 是否正在生成自定义模板
      isMappedFramework: false, // 是否是映射框架
      scrolledRef:[],
      showLessonForm: true, // 是否显示课程表单
      isEnhanceLesson: false, // 是否是增强课程
      isDownloading: false, // 是否正在下载
      isGeneratingSlides: false, // 是否正在单独点击生成 slides
      publishAfterGenerate: false, // 生成结束后是否需要发布
      publishAfterGenerateSendEvent: true, // 首次发布埋点记录
      isNewLesson: true, // 是否是旧课程重新进入页面
      udlAndClrGenerateHasListener: false, // UDL 和 CLR 生成是否有监听
      generateUniversalDesignForLearningCompleted: true, // UDL 生成是否完成
      generateCLRCompleted: true, // CLR 生成是否完成
      needRecordAdaptInfo: false, // 是否需要保存改编信息
      universalDesignForLearningCopy: null, // 记录 UDL 的初始数据
      universalDesignForLearningClassSpecialCopy: null, // 记录改编后的 UDL 的初始数据
      loadingDomains: false, // 是否正在加载测评点
      lessonAdaptGroupId: this.adaptGroupId, // 课程 adapt 的班级 ID
      lessonHasApplyWeekPlan: this.isApplyWeeklyPlan, // 课程是否已经应用到周计划中
      quizRegenerateLoading: false, // Quiz 重新生成的 loading 状态
      fromUnit: null, // 来自 Unit 信息
      tempDraftLessonId: null, // 临时草稿 ID，用于在页面离开时清理未完成的生成
      isSetObjectiveType: false, // 是否设置 objective 类型
      adaptedModuleSwitch: null // 轻量导入改编时当前模块是否显示
    }
  },
    mounted () {
    // 更新校验规则
    this.updateRules()
    // 课程编辑页面滚动到表单
    this.$nextTick(() => {
        // this.scrollToForm(1)
    })
    this.setFeedbackStyle()

    this.windowWidth = window.innerWidth
    // 监听屏幕大小变化使布局自适应
    window.addEventListener('resize', this.handleResize)
    window.addEventListener('resize', this.setFeedbackStyle)
    // 添加页面关闭前的事件监听
    window.addEventListener('beforeunload', this.handleBeforeUnload)
    // 检查是否打开欢迎弹窗
    this.checkWelcomeDialog()
    // 添加 Crtl + s 事件监听
    window.addEventListener("keydown", this.handleSaveShortcut);
    this.checkWelcomeDialog()
    },

    created: async function () {
    this.loading = 1
    // 如果初始化时没有 lessonId 或者是路由名为 addLesson，则表示是添加课程
    if (!this.lessonId || this.$route.name === 'AddLesson' || this.$route.name === 'AddLessonPlan') {
      this.isAddLesson = true
      // 添加课程，生成后直接发布
      this.publishAfterGenerate = true
    }
    this.times = this.generateTime()
    this.getEventNotify()
    this.$store.dispatch('curriculum/getQuizBloomSetting')
    // 使用 await 等待，因为后续的 initLesson 需要设置的值，所以这些值应该提前被加载
    await this.loadAgeGroup()
    await this.loadThemes()
    await this.loadFrameworks()
    await this.listLanguages()
    // 编辑模式，加载课程详情
    if (this.lessonId) {
      // 编辑课程时不再记录埋点
      this.publishAfterGenerateSendEvent = false
      this.isNewLesson = false
      // 如果有 lessonId 则表示是编辑模式，则需要设置 addLessonStep 为 2
      this.addLessonStep = 2
      await this.initLesson()
      // 记录原始的 UDL 数据，当 IEP 模块不生成内容时回显原始通用内容
      this.initUdlCopyData()
      // 如果当前组件直接使用没有传过来 isAdaptedLesson 从课程中拿
      if (this.isAdaptedLesson === undefined) {
        this.isAdaptedLesson = this.lesson.isAdaptedLesson
      }
      // 课程初始状态是发布状态，则生成后直接发布
      if (this.lesson.status === 'PUBLISHED') {
        this.publishAfterGenerate = true
      }
      // 获取扩展框架信息
      await this.loadExtendFramework()
      this.lesson.framework && this.loadMeasures(this.lesson.framework)
      // 课程有内容的话，默认可以保存
      this.generaterdLessoncompleted = true
      // 向 teachingTips 补充测评点描述
      this.addDescToTeachingTips(this.lesson.steps)
      // 确保数据更新完成
      await this.$nextTick()
      this.changesCounter = 0
      this.sourceLesson = JSON.parse(JSON.stringify(this.lesson))
    }
    // 设置是否显示推荐课程的复选框以及默认值
    this.handleRecommendToAgency()

    // 设置强制自动保存定时器
    if (!this.batchEdit) {
      this.forceAutoSaveTimer = setInterval(() => {
        this.checkForceAutoSave()
      }, this.forceSaveInterval) // 每两分钟检查一次是否需要强制保存
    }


    // 注册 watch
    this.$watch('lessonData', (val, oldVal) => {
      if (!this.generaterdLessoncompleted) {
        return
      }
      if (!this.checkLessonChange(this.sourceLesson, val)) {
        if (this.autoSaveDebounceTimer) {
          clearTimeout(this.autoSaveDebounceTimer)
        }
        this.changesCounter = 0
        return
      }
      this.changesCounter++
      // 如果是在批量编辑中，则不进行自动保存
      if (this.batchEdit) {
        return
      }
      this.autoSaveLesson()
    }, { deep: true, immediate: true })
    this.$watch('frameworkId', (value) => {
      if (this.skipFrameworkWatch) {
        this.skipFrameworkWatch = false
        return
      }
      let frameworks = this.frameworks || []
      let framework = frameworks.find(item => value === item.frameworkId)
      // this.oldMeasureIds = []
      this.lesson.framework = framework
      this.lesson.measures = []
      // 修改框架 初始化步骤的教师指导
      this.lesson.steps.forEach(step => {
        this.initStepGuide(step)
        this.initTeachingTips(step)
        this.initStepQuiz(step)
      })
      this.loadMeasures(framework)
    })
    this.$watch('ageValues', () => {
      this.oldAgeValues = this.ageValues // 缓存修改前的年龄组
    })
    this.$nextTick(() => {
      // 等待组件加载完成，发送 lessonLoaded 事件
      this.$emit('lessonLoaded', this.lessonId)
      // loading = 2 的时候表示加载完成， 此时保存一个 lesson 的初始值
      // 收集数据保存课程副本
      this.fillUDLAndCLRData()
      this.initLessonData = JSON.stringify(this.collectData())
      // if (!this.batchEdit && !this.inWeeklyPlannDialog) {
      //   this.autoSavingTimer = setInterval(() => {
      //     if (this.changesCounter && !this.saving && this.loading === 2 && this.lesson.name && this.lesson.name.trim()) {
      //       let count = this.changesCounter
      //       this.changesCounter = 0
      //       this.saveDraft().catch(() => this.changesCounter += count)
      //     }
      //   }, 60 * 1000)
      // }
      // 如果 templateType 不为空，则需要生成模版内容
      if (this.templateType) {
        this.updateLessonTemplate(this.templateType) // 生成模版内容
      }
      setTimeout(() => {
        this.$bus.$emit('showEduProtocolsTemplateCustomGuide', this.lesson.id)
      }, 1000);
    })
    // 保存页面初始化时数据
    this.beforeLesson = this.collectData()
    // 根据路由传参 query 判断是否要回滚历史
    if (this.$route.query.versionId) {
      // 开启历史回滚 loading
      // 获取历史版本详情
      Lesson2.getLessonVersionDetail(this.$route.query.versionId)
        .then(async data => {
          await this.$nextTick()
          // 回滚历史版本
          await this.handleRestoreHistory(data, this.$route.query.versionId)
          this.$message.success(this.$t('loc.lessonRestoreSuccess'))
        }).finally(() => {
          // 清除路由传递的 versionId
          const query = { ...this.$route.query }
          delete query.versionId
          this.$router.replace({ query })
          // 关闭历史回滚 loading
          this.loading = 2
        })
    } else {
      this.loading = 2
    }
  },
  beforeDestroy () {
    this.autoSavingTimer && clearInterval(this.autoSavingTimer)
    // 清理所有定时器
    this.autoSaveDebounceTimer && clearTimeout(this.autoSaveDebounceTimer)
    this.forceAutoSaveTimer && clearInterval(this.forceAutoSaveTimer)
    window.removeEventListener('resize', this.handleResize)
    window.removeEventListener('resize', this.setFeedbackStyle)
    // 移除页面关闭前的事件监听
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
    // 移除 Ctrl + s 事件监听
    window.removeEventListener("keydown", this.handleSaveShortcut);
    // 销毁监听 UDL 和 CLR 的生成完成事件
    this.$bus.$off('generateUniversalDesignForLearningCompleted')
    this.$bus.$off('generateCLRCompleted')
    this.$bus.$off('generateUniversalDesignForLearningStart')
    this.$bus.$off('generateCLRStart')
    // 销毁监听 UDL 和 CLR 的加载完成事件
    this.$bus.$off('lessonLibraryUDLMounted')
    this.$bus.$off('lessonLibraryCLRMounted')
    this.$bus.$off('clearResourceHistory')
  },
  watch: {
    // 监听课程 convertLessonLoading 变化
    convertLessonLoading (newValue) {
      // 更新父组件的生成状态
      this.$emit('updateGenerateStatus', newValue)
      // 如果 convertLessonLoading 为 true 且不是生成自定义模板，则滚动到表单
      if (newValue && !this.generatingLessonContent) {
        this.scrollToForm(1)
      }
      this.updateRules()
    },
    refreshExtFramework: {
      immediate: true,
      deep: true,
      handler (val) {
        if (val) {
          this.loadFrameworks()
          this.$store.commit('curriculum/SET_REFRESH_EXT_FRAMEWORK', false)
        }
      }
    },
    /**
     * 监听页面内展示的数据有无变化
     */
    listenChange: {
      handler (newValue, oldValue) {
        this.lessonInfoHasChanged = true
        // 执行其他操作
      },
      deep: true
    },

    // 'lesson.steps': {
    //   immediate: true,
    //   deep: true,
    //   handler (val) {
    //     console.log("---------",val)
    //     this.$nextTick(() => {
    //       // 滚动到正在生成的单元概览项
    //       if (!this.universalDesignBeginGenerate && this.$refs.assistantToolbar && this.$refs.assistantToolbar.convertLessonLoading){
    //         const activeElement = document.activeElement
    //         if (activeElement.tagName.toLowerCase() !== 'button') {
    //           // 将滚动条滚动到当前活跃的元素位置
    //           activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    //         }
    //       }
    //     })
    //   }
    // }
  },
  provide() {
    return {
      assistantToolStateManager: new AssistantToolStateManager(),
      changeElFormIndex: this.changeElFormIndex
    }
  },

  methods: {
    // 获取课堂类型标签文本
    getClassroomTypeLabel (classroomType) {
      if (!classroomType) return this.$t('loc.classroomTypeInPerson')
      
      switch (classroomType.toUpperCase()) {
        case 'IN_PERSON':
          return this.$t('loc.classroomTypeInPerson')
        case 'VIRTUAL':
          return this.$t('loc.classroomTypeVirtual')
        default:
          return classroomType
      }
    },
    // 获取轻量导入改编时当前模块是否显示
    getLightAdaptModuleShow(module) {
      // 默认展示
      if (!this.adaptedModuleSwitch || !module) {
        return true
      }
      return this.adaptedModuleSwitch[module]
    },
    // 记录原始的 UDL 数据，当 IEP 模块不生成内容时回显原始通用内容
    initUdlCopyData() {
      if (this.lesson && this.lesson.steps && this.lesson.steps[0]) {
        this.universalDesignForLearningCopy = this.lesson.steps[0].universalDesignForLearning
        this.universalDesignForLearningClassSpecialCopy = this.lesson.steps[0].universalDesignForLearningGroup ? this.lesson.steps[0].universalDesignForLearningGroup : ''
      }
    },
    /**
     * 处理 Enhance 引导弹框
     */
    handelEnhanceGuide(value) {
      if (value) {
        this.$analytics.sendEvent('cg_lesson_enhance_guide_try_click')
        // 打开 Enhance 弹框
        this.openRedesignLessonDialog()
      }
      this.hideEnhanceGuide()
    },

    /**
     * 关闭 Enhance 引导
     */
    hideEnhanceGuide() {
      if (this.showEnhanceLessonPlanGuide) {
        this.$analytics.sendEvent('cg_lesson_enhance_guide_later_click')
        // 隐藏引导
        this.guideFeatures.showCurriculumLessonPlanEnhanceGuide = false
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
        this.$bus.$emit('show-resources-guide')
        // 永久隐藏引导
        this.$axios.post($api.urls().hideGuide, {'features': ['LESSON_PLAN_ENHANCE_GUIDE']}).then()
      }
    },

    /**
     * 点击下载
     */
    handleDownloadClick() {
      if (this.$route.name === 'AddLesson') {
        // 创建课程下载点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_download')
      } else if (this.$route.name === 'EditLesson') {
        // 编辑课程下载点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_edit_click_download')
      }
    },
    /**
     * 点击发布
     */
    handlePublishClick () {
      // 编辑课程发布点击埋点
      this.$analytics.sendEvent('cg_lesson_plan_edit_public')
      this.publishLesson(null, null, true, true)
    },
    // 生成课程幻灯片
    generateSlides () {
      this.$refs.lessonSlidesRef && this.$refs.lessonSlidesRef[0] && this.$refs.lessonSlidesRef[0].generateSlides()
    },
    // 更新课程模板类型
    async updateLessonTemplate (type) {
      await this.saveDraft()
      this.$nextTick(() => {
        this.convertLessonLoading = true
        // 隐藏 quill
        this.quillFocus = false
        // 旧的课程数据
        let copyLesson = JSON.parse(JSON.stringify(this.lesson))
        let assistantTool = this.getCurrentAssistantTool()
        // 开始生成课程内容
        assistantTool.convertLessonStream(null, null, null, copyLesson, null, type, null, this.isAdaptedLesson)
        this.$store.dispatch('setTemplateType', '') // 清空模板类型
      })
      this.$bus.$once('clearResourceHistory', (lessonId) => {
        this.handleLessonGenerated(lessonId)
      })
    },
    // 更新课程模版信息
    updateLessonTemplateInfo (lessonTemplate, templateId) {
      // 如果 lessonTemplate 存在, 则设置课程模板
      if (lessonTemplate) {
        this.setStepsLessonTemplates(lessonTemplate)
      } else {
        // 清空课程模板
        if (this.lesson.steps[0] && this.lesson.steps[0].lessonTemplate && equalsIgnoreCase(templateId, this.lesson.steps[0].lessonTemplate.id)) {
          this.$set(this.lesson, 'templateType', null)
          this.$set(this.lesson.steps[0], 'lessonTemplate', null)
        }
        // 缓存的实施步骤中的课程模板也清空
        this.stepsCache.forEach(step => {
          if (step.lessonTemplate && equalsIgnoreCase(templateId, step.lessonTemplate.id)) {
            step.lessonTemplate = null
          }
        })
        this.saveLesson()
      }
    },
    // 关闭课程模板引导
    closeCustomTemplateGuide () {
      this.$refs.lessonTemplatePreviewRef && this.$refs.lessonTemplatePreviewRef[0] && this.$refs.lessonTemplatePreviewRef[0].closeCustomTemplateGuide()
    },
    // 更新课程内容生成状态
    updateGenerateLessonContentLoading(loading) {
      let assistantTool = this.getCurrentAssistantTool()
      // slides 开始生成 loading 但 convertLessonLoading 为false 时，代表时单独点击 slides 进行生成
      if (loading && !assistantTool.convertLessonLoading) {
        this.isGeneratingSlides = true
      }
      // 生成课程内容完成之前，slides 生成完成直接返回；单独点击 slides 开始生成但 generatedLesson 为false 时，要调整 loading 状态
      if ((assistantTool.lessonCoverLoading || !assistantTool.lessonStepTwoGenerated) && !this.isGeneratingSlides) {
        return
      }
      // 更新生成课程内容状态
      this.generatingLessonContent = loading
      // 如果生成完成状态，根据 loading 赋值
      this.generaterdLessoncompleted = !loading
      // 更新课程生成 loading
      this.convertLessonLoading = loading
      // 更新课程生成 loading
      this.$emit('updateGeneratingLessonContent', loading)
      // 更新 ai 课程表单生成按钮 loading
      assistantTool && (assistantTool.convertLessonLoading = loading)
      if (!loading) {
        this.isGeneratingSlides = false
      }
    },

    // slides 流式生成状态同步
    updateGenerateLessonSlidesStreamLoading (val) {
      // 如果是单独生成的 slides 不做状态同步
      if (this.isGeneratingSlides) {
        return
      }
      let assistantTool = this.getCurrentAssistantTool()
      if (assistantTool) {
        assistantTool.generateLessonSlidesStreamLoading = val
        // 如果是生成结束，尝试调用结束标识
        if (!val) {
          assistantTool.generaterdLessoncomplete()
        }
      }
    },

    // 向 teachingTips 补充测评点描述
    addDescToTeachingTips (step) {
      if (Array.isArray(step)) {
        let measures = []
        frameworkUtils.collectMeasures(measures, this.lesson.measures)
        step.forEach(singleStep => {
          if (singleStep && singleStep.teachingTips && singleStep.teachingTips.length > 0) {
            singleStep.teachingTips.forEach(tip => {
              if (tip.measureId) {
                let measure = measures.find(me => me.id === tip.measureId)
                if (measure) {
                  tip.measureDescription = measure.description
                }
              }
            })
          }
        })
      }
    },
    // 当 activityType 改变时，更新课程类型
    changeActivityType (val) {
      this.subclassCenter = val
    },
    // 更新 subclassCenter
    upSubclassCenter (val) {
      this.subclassCenter = val
      // 还原课程模板
      this.recoverStepSorContent()
    },
    // 判断课程内容是否更新
    isUpdateLesson () {
      // 如果正在初始化，返回未更新
      if (this.loading === 1) {
        return false
      }
      return this.changesCounter > 0
      // 移除不需要校验的字段
      // const nowLesson = this.removeNotCheckData(this.collectData())
      // const beforeLesson = this.removeNotCheckData(this.beforeLesson)
      // return nowLesson !== beforeLesson
    },
    // 移除不需要校验的字段
    removeNotCheckData (data) {
      const dataNew = JSON.parse(JSON.stringify(data))
      dataNew.recommendToAgency = null
      return JSON.stringify(dataNew)
    },
    // 更新校验规则
    updateRules () {
      this.rules.name[0].required = !this.convertLessonLoading
      this.rules.framework[0].required = !this.convertLessonLoading
      this.rules.ages[0].required = !this.convertLessonLoading
      this.rules.objective[0].required = !this.convertLessonLoading
      this.rules.material[0].required = !this.convertLessonLoading
      this.rules.material[0].fields.description.required = !this.convertLessonLoading
    },
    // 课程编辑页面滚动到表单
    scrollToForm (count) {
      // 检查表单引用是否存在
      if (this.$refs.form && this.$refs.form.$el) {
        // 如果表单引用存在，滚动到该元素
        this.$refs.form.$el.scrollIntoView()
      } else if (count <= 30) {
        // 如果计数小于等于30，则递归调用自身
        setTimeout(() => {
          // 增加计数并递归调用scrollToForm
          this.scrollToForm(count + 1)
        }, 150) // 延时150毫秒执行
      }
    },
    changeElFormIndex (newValue) {
      this.elFormIndex = newValue
    },
    // 修改点赞按钮位置
    setFeedbackStyle () {
      this.$nextTick(() => {
        let assistantTool = this.getCurrentAssistantTool()
        if (assistantTool) {
          const formElement = assistantTool.$el
          const rect = formElement.getBoundingClientRect()
          if (this.inWeeklyPlannDialog) {
            this.computedFeedbackStyle = {
              right: `${rect.left}px`,
              top: `${rect.top - 41}px`,
              // position: 'fixed'
            }
          } else {
            this.computedFeedbackStyle = {
              right: `${rect.left + 20}px`,
              top: `${rect.top - 40}px`,
              // position: 'fixed'
            }
          }
        }
      })
    },
    // 监听屏幕大小变化使布局自适应
    handleResize () {
      this.windowWidth = window.innerWidth
    },
      // 定义 UDL 和 CLR 的 loading
      generateDataLoading () {
          let currentAgeGroupName = this.currentAgeGroupName || ''
          if (this.lesson && this.lesson.steps && this.lesson.steps.length > 0) {
              // 如果 currentAgeGroupName 为空
              if (currentAgeGroupName === null || currentAgeGroupName === undefined || currentAgeGroupName === '') {
                  // 那么就将 currentAgeGroupName 设置为 lesson.steps[0].ageGroupName
                  currentAgeGroupName = this.lesson.steps[0].ageGroupName
              }
          }
          // 设置 loading 状态
          let loading = false
          this.$refs.universalDesignLearning.forEach(item => {
              if (currentAgeGroupName === item.lessonAgeGroup) {
                  // UDL 使用 group team 进行数据的请求
                  loading = loading || item.generateUniversalDesignLoading
              }
          })
          // 这里负责请求 CLR 数据
          this.$refs.culturallyLinguisticallyResponsive.forEach(item => {
              if (currentAgeGroupName === item.ageGroupName) {
                  // CLR 使用 group team 进行数据的请求
                  loading = loading || item.generateClassSpecificValueLoading
              }
          })
          return loading
      },
      // 判断是不是在周计划编辑页面
      isWeeklyPlanEdit () {
        if (this.unitPlannerBatchAdapt) {
          return true
        }
          return !!this.$route.path &&
            this.$route.path.indexOf('weekly-lesson-planning') > -1 &&
            (this.$route.name.indexOf('edit-plan') > -1 || this.planStatus === 'B_PENDING' || this.$route.name.indexOf('view-plan') !== -1 || this.inWeeklyPlannDialog)
      },
      // 是否是 Curriculum
      isCurriculum () {
        return equalsIgnoreCase(this.$route.name, 'curriculumEdit') || equalsIgnoreCase(this.$route.name, 'curriculumUnitDetail')
      },
      /**
       * 获取 event notify 中的信息
       */
      getEventNotify () {
          // 如果 fromIpad 直接将 adaptUDLAndCLROpen 设置为 false
          if (this.isFromIpad) {
              this.adaptUDLAndCLROpen = false
              return
          }
          this.adaptUDLAndCLROpen = this.isOpenAdaptUDLAndCLR
      },
    submitFeedback (res) {
    },
    // 从 AI 助手调过来的实施步骤资源生成
    generateImpStepResourceAssistant () {
      // 触发实施步骤资源生成
      this.$refs.lessonStepInput[0].generateImpStepsSource(this.lesson.id)
    },
    /**
     * 传过来的是完成课程内容，进行恢复
     */
    updateLessonInfo (oldLesson) {
      if (!oldLesson) {
        this.lesson = {
          id: this.lesson.id, // 课程 ID
          cover: (this.lesson && this.lesson.cover) || null, // 课程封面，对象，id、name、url
          name: null, // 课程名称
          ages: [], // 年龄组，对象数组，name、value
          prepareTime: null, // 准备时长
          activityTime: null, // 活动时长
          themes: [], // 课程主题，对象数组，id、name
          framework: this.inWeeklyPlannDialog ? this.lesson.framework : null, // 框架，对象，frameworkId、frameworkName。 在 WeekPLan 中有默认框架，不需要修改
          measures: [], // 领域和测评点，对象数组，id、name、abbreviation、children
          objective: null, // 课程目标
          material: null, // 课程材料，对象，media、description、attachmentMedias (附件，对象数组)
          steps: [], // 课程步骤，对象数组，content、media、ageGroup
          learnerProfiles: [], // 毕业生画像，对象数组，rubricsName、rubricsValue、subRubrics（对象数组，rubricsName、rubricsValue、rubricsNameDesc）
          book: null, // 书，对象
          videoBook: null, // 视频书，对象
          attachments: [], // 附件，对象数组
          dlls: [],// DLL，对象数组：id、title、content、media、languages（对象数组：langCode、content、lang）
          coverExternalMediaUrlId: null // 封面外部链接视频id
        }
        return
      }
      this.lesson = oldLesson
      this.$refs.lessonMaterialInput.updateDescription(this.lesson.materials)
      // 重新生成改编课程按钮关闭主动控制
      this.redesignLessonButton = false
      // 测评点恢复
      let copyMeasureIds = []
      frameworkUtils.getMeasuresBottomIds(this.lesson.measures, copyMeasureIds)
      this.toResetMeasureIds = copyMeasureIds
      this.skipFrameworkWatch = true
      // 框架 ID 恢复
      this.frameworkId = this.lesson.framework.frameworkId
    },
    // 修改课程名称
    updateLessonName () {
      this.$emit('updateLessonName', this.lesson.id, this.lesson.name)
    },
    /**
     * 传过来的是完成课程内容，进行恢复
     */
    updateLessonMeasureSelectNotShow () {
      // 向测评点组件设置 show = false ，避免组件弹开
      this.lessonMeasureSelectNotShow = false
      this.quillFocus = true
    },
    /**
     * 传过来的是完成课程内容，进行恢复
     */
    syncAssistantInfoToLesson (assistantAdaptInfo) {
      // 活动类型
      this.$set(this.lesson, 'activityType', this.getTypeNameByValue(assistantAdaptInfo.activityType))
      // 活动主题
      if (this.getRealActivityTheme(assistantAdaptInfo.activityType)) {
        this.$set(this.lesson, 'activityTheme', assistantAdaptInfo.centerThemeName)
      } else {
        this.$set(this.lesson, 'activityTheme', null)
      }
      this.$set(this.lesson, 'assistantAdaptInfo', JSON.stringify(assistantAdaptInfo))
      this.$set(this.lesson, 'classroomType', assistantAdaptInfo.classroomType)
    },
    /**
     * 根据传过来的 val 查 name
     */
    getTypeNameByValue (typeValue) {
      const activity = PublicLessonAssistantInfoActivityType.find(item => item.typeValue === typeValue)
      return activity ? activity.typeName : null // 如果找不到匹配项，则返回 null
    },
    /**
     * 判断是否应该有主题
     */
    getRealActivityTheme (typeValue) {
      const validValues = [
        PublicLessonAssistantInfoActivityType[2].typeValue,
        PublicLessonAssistantInfoActivityType[4].typeValue
      ]

      return validValues.includes(typeValue)
    },
    /**
     * 设置课程模版信息
     */
    setStepsLessonTemplates (lessonTemplate) {
      if (lessonTemplate) {
        this.$set(this.lesson, 'templateType', lessonTemplate.templateType)
        this.$set(this.lesson.steps[0], 'lessonTemplate', lessonTemplate)
      }
    },
    /**
     * 更新课程助手修改的内容
     */
    updateLessonAssistantContent (newLesson) {
      this.$emit('updateAdaptedStatus')
      // 课程名称
      if (newLesson.name && !this.lesson.objective) {
        this.lessonMeasureSelectNotShow = false
        // 防止在 redesignLesson 时，quillFocus 为 false
        this.quillFocus = true
        this.$set(this.lesson, 'name', newLesson.name)
      }
      // 年龄段
      if (this.lesson.name && newLesson.ageGroup && !this.lesson.prepareTime) {
        // 处理年龄段,找到对应的 value
        let ageValue = this.ageGroup.find(x => x.name == newLesson.ageGroup)
        // 设置年龄段为标准的年龄段值
        newLesson.ageGroup = ageValue ? ageValue.value : newLesson.ageGroup
        this.ageValues = [newLesson.ageGroup]
        this.changeAgeValues([newLesson.ageGroup], true, false)
      }
      if (newLesson.ageGroup) {
        this.$set(this.lesson, 'ageGroup', newLesson.ageGroup)
      }
      // 准备时间
      if (this.ageGroup.length > 0 && newLesson.prepareTime && !this.lesson.objective) {
        this.$set(this.lesson, 'prepareTime', this.extractActivityTime(newLesson.prepareTime))
      }
      // 活动时间
      if (this.lesson.prepareTime && newLesson.activityTime && !this.lesson.objective) {
        this.$set(this.lesson, 'activityTime', this.extractActivityTime(newLesson.activityTime))
      }
      // // 主题，暂时固定
      // if (this.lesson.activityTime) {
      //   this.$set(this.themeIds, 0, '0A94DF23-D926-9DA9-1A2A-74A3C2F0EA42')
      // }
      if (this.themes) {
        this.lesson.themes = this.themes.filter(item => this.themeIds.includes(item.id))
      }
      // 课程目标
      if (this.lesson.themes && newLesson.objectives && (!this.lesson.material || !this.lesson.material.description)) {
        this.$set(this.lesson, 'objective', newLesson.objectives)
        this.scrollToActive("lessonObjective")
      }
      // 课程材料
      if (this.lesson.objective && newLesson.materials) {
        this.$refs.lessonMaterialInput.updateDescription(newLesson.materials)
      }
      // 关键词汇
      if (this.lesson.material && newLesson.keyVocabularyWords) {
        let dlls = this.lesson.dlls
        let words = newLesson.keyVocabularyWords.split('\n')
        for (let i = 0; i < words.length; i++) {
          let word = words[i]
          if (!word || word.trim() === '') {
            continue
          }
          // 如果有冒号，则根据冒号进行分割
          let wordsAndDescription = word.split(/[:：]/)
          // 获取关键词
          let content = wordsAndDescription[0]
          // 检查字符串并处理
          if (!!content && content.startsWith('-')) {
            content = content.replace('-', '').trim()
          }
          let description = ''
          // 判断数组的长度是否大于 1，如果是大于 1 则说明存在描述
          if (wordsAndDescription.length > 1) {
            // 获取关键词描述
            description = wordsAndDescription[1].trim()
          }
          // 封装 dll 数据
          let dll = {
            content: content,
            description: description
          }
          // 不存在则新增
          if (i >= dlls.length) {
            this.lesson.dlls.push(dll)
          } else {
            this.$set(this.lesson.dlls, i, dll)
          }
        }
      }
      // 实施步骤
      if (newLesson.implementationSteps) {
        this.scrollToActive("lessonStepInput")
        let implementationStepContent = newLesson.implementationSteps.replace(/\n/g, '<br/>')
        implementationStepContent = implementationStepContent.replace(/Dialogue:\s*"([^"]+)"/g, '<i>$1</i>')
        implementationStepContent = implementationStepContent.replace(/Dialogue:\s*/g, '')
        // this.$set(this.lesson.steps[0], 'content', implementationStepContent)
        this.$refs.lessonStepInput[0].updateContent(implementationStepContent)
      }
      // this.$set(this.lesson, '', newLesson.)
      // 由于还未生成完毕，将是否更改改为 false
      this.lessonInfoHasChanged = false
    },

    /**
     * 更新课程步骤中内容的第二步，更新差异化教学内容、文化响应式教学内容、典型行为
     * @param {Object} newLessonStep - 新的课程步骤数据
     * @param {String} specificField - 可选，指定只更新某个字段，用于性能优化
     */
    updateLessonStepTwo (newLessonStep, specificField = null) {
      // 更新课程封面
      if (!this.lesson.cover && newLessonStep.lessonCover && newLessonStep.lessonCover.id && (equalsIgnoreCase(specificField, 'lessonCover') || !specificField)) {
        this.$set(this.lesson, 'cover', {})
        this.$set(this.lesson.cover, 'id', newLessonStep.lessonCover.id)
        this.$set(this.lesson.cover, 'url', newLessonStep.lessonCover.public_url)
        this.$set(this.lesson.cover, 'name', newLessonStep.lessonCover.fileName)
        this.$set(this.lesson.cover, 'size', newLessonStep.lessonCover.size)
        this.$set(this.lesson.cover, 'source', newLessonStep.lessonCover.source)
      }

      // 更新课程步骤中的其他信息
      if (this.lesson && this.lesson.steps.length > 0) {
        // 更新差异化教学内容
        if (newLessonStep.universalDesignForLearning && (equalsIgnoreCase(specificField, 'universalDesignForLearning') || !specificField)) {
          this.$set(this.lesson.steps[0], 'universalDesignForLearning', newLessonStep.universalDesignForLearning)
          this.$set(this.lesson.steps[0], 'universalDesignForLearningGroup', newLessonStep.universalDesignForLearningGroup)
        }
        // 更新文化响应式教学内容
        if (newLessonStep.culturallyResponsiveInstruction && (equalsIgnoreCase(specificField, 'culturallyResponsiveInstruction') || !specificField)) {
          this.$set(this.lesson.steps[0], 'culturallyResponsiveInstruction', newLessonStep.culturallyResponsiveInstruction)
          this.$set(this.lesson.steps[0], 'culturallyResponsiveInstructionGroup', newLessonStep.culturallyResponsiveInstructionGroup)
          if (newLessonStep.lessonClrAndSources) {
            // clr资源存在时，不在做clr更新(ai助手的clr资源为触发资源生成得来，newLessonStep不存在资源内容)
            if (!this.lesson.steps[0].lessonClrAndSources || !this.lesson.steps[0].lessonClrAndSources.sources || this.lesson.steps[0].lessonClrAndSources.sources.length <= 0) {
              this.$set(this.lesson.steps[0], 'lessonClrAndSources', newLessonStep.lessonClrAndSources)
            }
            if (this.$refs.culturallyLinguisticallyResponsive[0]) {
              this.$refs.culturallyLinguisticallyResponsive[0].generateClassSpecificValueLoading = true
              this.$refs.culturallyLinguisticallyResponsive[0].generalValue = newLessonStep.culturallyResponsiveInstruction
            }

            if (newLessonStep.lessonClrAndSources.over) {
              this.$refs.culturallyLinguisticallyResponsive[0].generateClassSpecificValueLoading = false
            }
          }
        }
        // 更新典型行为内容
        if (newLessonStep.typicalBehaviors && newLessonStep.typicalBehaviors.length > 0 && (equalsIgnoreCase(specificField, 'typicalBehaviors') || !specificField)) {
          let typicalBehaviorsValue = [] // 所要展示的典型行为列表
          for (let typicalBehavior of newLessonStep.typicalBehaviors) {
            if (typicalBehavior && typicalBehavior.measureAbbreviation) {
              let measureAbbreviation = typicalBehavior.measureAbbreviation // 获取测评点缩写
              let behaviors = typicalBehavior.behaviors // 获取行为
              let mapped = typicalBehavior.mapped || false // 是否映射
              let typicalBehaviorsObj = { mapped: mapped } // 构建所要展示的典型行为列表中的对象
              typicalBehaviorsObj.typicalBehaviors = behaviors // 更新行为
              // 使用映射表快速查找测评点（性能优化）
              const mapToUse = mapped ? this.mappedMeasureMap : this.measureMap
              const measure = mapToUse.get('abbreviation-' + measureAbbreviation.trim())
              if (measure) {
                typicalBehaviorsObj.measureId = measure.id
                typicalBehaviorsObj.measureName = measure.name
                typicalBehaviorsObj.measureAbbreviation = measure.abbreviation
                typicalBehaviorsValue.push(typicalBehaviorsObj)
              }
            }
          }
          // 更新数据
          this.$set(this.lesson.steps[0], 'lessonStepGuides', typicalBehaviorsValue)
        }

        // 更新标准教学指导内容
        if (newLessonStep.teachingTips && newLessonStep.teachingTips.length > 0 && (equalsIgnoreCase(specificField, 'teachingTips') || !specificField)) {
          let teachingTipsValue = [] // 所要展示的教学指导内容列表
          for (let teachingTip of newLessonStep.teachingTips) {
            if (teachingTip && teachingTip.measureAbbreviation) {
              let measureAbbreviation = teachingTip.measureAbbreviation // 获取测评点缩写
              let tips = teachingTip.teachingTips // 获取行为
              let teachingTipsObj = {} // 构建所要展示的教学指导内容列表中的对象
              teachingTipsObj.teachingTips = tips // 更新行为

              // 使用映射表快速查找测评点（性能优化）
              let measure = this.measureMap.get('abbreviation-' + measureAbbreviation.trim())
              if (!measure) {
                measure = this.measureMap.get('mappingAbbr-' + measureAbbreviation.trim())
              }
              if (measure) {
                teachingTipsObj.measureId = measure.id
                teachingTipsObj.measureName = measure.name
                teachingTipsObj.measureAbbreviation = measure.abbreviation
                teachingTipsObj.core = measure.core
                teachingTipsValue.push(teachingTipsObj)
              }
            }
          }
          // 更新数据
          this.$set(this.lesson.steps[0], 'teachingTips', teachingTipsValue)
        }

        // 更新家庭活动
        if (newLessonStep.homeActivity && newLessonStep.homeActivity.length > 0 && (equalsIgnoreCase(specificField, 'homeActivity') || !specificField)) {
          this.$set(this.lesson.steps[0], 'homeActivity', newLessonStep.homeActivity)
        }
        // 更新测验
        if (newLessonStep.questions && (equalsIgnoreCase(specificField, 'questions') || !specificField)) {
          let questions = []
          // 循环测试题，设置测评点信息
          for (let question of newLessonStep.questions) {
            // 根据测评点缩写找到对应的测评点
            let measureAbbreviation = question.measureAbbreviation
            if (question && measureAbbreviation) {
              // 使用映射表快速查找测评点（性能优化）
              let measure = this.measureMap.get('abbreviation-' + measureAbbreviation.trim())
              if (!measure) {
                measure = this.measureMap.get('mappingAbbr-' + measureAbbreviation.trim())
              }
              if (measure) {
                question.measureId = measure.id
                question.measureName = measure.name
                question.measureAbbreviation = measure.abbreviation
                questions.push(question)
              }
            }
          }
          // 更新数据
          this.$set(this.lesson.steps[0], 'questions', questions)
        }
        // 更新校训
        if (newLessonStep.learnerProfiles && newLessonStep.learnerProfiles.length > 0 && (equalsIgnoreCase(specificField, 'learnerProfiles') || !specificField)) {
          this.$set(this.lesson, 'learnerProfiles', newLessonStep.learnerProfiles)
        }
      }
      // 由于还未生成完毕，将是否更改改为 false
      this.lessonInfoHasChanged = false
    },

    // curriculum生成 CLR 资源
    async curriculumGenerateLessonSourceByCLR (resolve) {
      await this.$refs.culturallyLinguisticallyResponsive[0].generateLessonSourceByCLR(false, resolve)
    },

    /**
     * 更新课程框架和测评点
     */
    updateLessonFrameworkAndMeasures (frameworkId, measureIds, classroomType) {
      // 重新生成改编课程按钮关闭主动控制
      this.redesignLessonButton = false
      if (this.frameworkId && this.frameworkId.toLowerCase() === frameworkId.toLowerCase()) {
        this.setSelectedMeasures(measureIds)
      } else {
        this.frameworkId = frameworkId
        this.toResetMeasureIds = measureIds
        this.changeFramework(frameworkId, false)
      }
      this.oldMeasureIds = measureIds
      if (this.lesson) {
        this.lesson.classroomType = classroomType
      }
    },

    /**
     * 设置要选中的测评点
     */
    setSelectedMeasures (measureIds) {
      if (!this.domains || this.domains.length === 0 || !measureIds || measureIds.length === 0) {
        return
      }
      this.toResetMeasureIds = [] // 清空要重置的测评点
      let selectedMeasures = []
      for (let domain of this.domains) {
        let children = domain.children || []
        let measures = children.filter(measure => measureIds.includes(measure.id))
        if (measures.length) {
          selectedMeasures.push({ ...domain, children: measures })
        }
      }
      this.$set(this.lesson, 'measures', selectedMeasures)
    },

    /**
     * 提取
     */
    extractActivityTime (time, lengthLimit) {
      // 没有参数直接返回
      if (!time) {
        return
      }
      // 匹配数字
      time = time.match(/\d+/)[0]
      // 超过限制长度则截取
      if (lengthLimit && time.length > lengthLimit) {
        time = time.substring(0, lengthLimit)
      }
      return parseInt(time)
    },

    /**
     * 清空课程
     */
    clearLesson () {
      // 缓存步骤内容
      this.cacheStepSorContent()
      this.lesson = { // 课程信息
        id: this.lesson.id, // 课程 ID
        cover: (this.lesson && this.lesson.cover) || null, // 课程封面，对象，id、name、url
        name: null, // 课程名称
        ages: [], // 年龄组，对象数组，name、value
        prepareTime: null, // 准备时长
        activityTime: null, // 活动时长
        themes: [], // 课程主题，对象数组，id、name
        framework: this.inWeeklyPlannDialog ? this.lesson.framework : null, // 框架，对象，frameworkId、frameworkName。 在 WeekPLan 中有默认框架，不需要修改
        measures: [], // 领域和测评点，对象数组，id、name、abbreviation、children
        objective: null, // 课程目标
        material: null, // 课程材料，对象，media、description、attachmentMedias (附件，对象数组)
        steps: [], // 课程步骤，对象数组，content、media、ageGroup
        learnerProfiles: [], // 毕业生画像，对象数组，rubricsName、rubricsValue、subRubrics（对象数组，rubricsName、rubricsValue、rubricsNameDesc）
        book: null, // 书，对象
        videoBook: null, // 视频书，对象
        attachments: [], // 附件，对象数组
        dlls: [],// DLL，对象数组：id、title、content、media、languages（对象数组：langCode、content、lang）
        coverExternalMediaUrlId: null, // 封面外部链接视频id
        status: this.lesson.status // 课程状态
      }
      // 清掉 step 缓存 -- 不清空的话，生成时给年级赋值会触发缓存赋值
      this.stepsCache = []
      // 置空，这样在未改变框架重新生成时让监听器监听数据变更。 在 WeekPLan 中有默认框架，不需要修改
      if (!this.inWeeklyPlannDialog) {
        this.frameworkId = null
      }
      this.scrolledRef = []
    },
    // 缓存步骤内容
    cacheStepSorContent() {
      // 缓存步骤内容
      this.cacheStepSorForm = this.lesson.steps.map(step => {
        return {
          ageGroupName: step.ageGroupName,
          lessonScienceOfReadingModel: step.lessonScienceOfReadingModel
        }
      })
    },
    // 恢复步骤内容
    recoverStepSorContent() {
      // 如果没有缓存，则不恢复
      if (!this.cacheStepSorForm || this.cacheStepSorForm.length === 0) {
        return
      }
      // 恢复步骤内容
      this.lesson.steps.forEach(step => {
        let cacheStep = this.cacheStepSorForm.find(cacheStep => cacheStep.ageGroupName === step.ageGroupName)
        if (cacheStep) {
          step.lessonScienceOfReadingModel = cacheStep.lessonScienceOfReadingModel
        }
      })
    },
      /**
       * 是否有 UDL 和 CLR 的数据
       * @returns {boolean}
       */
      hasUDLOrCLRData () {
          // 判断 lesson 中所有的 step 是否有 universalDesignForLearningGroup 和 culturallyResponsiveInstructionGroup
          // 如果有，它们的内容是否是空串
          if (this.lesson && this.lesson.steps) {
              return this.lesson.steps.some(item => {
                  return !!item.culturallyResponsiveInstructionGroup || !!item.universalDesignForLearningGroup
              })
          }
          return false
      },

    /**
     * 设置是否显示推荐课程的复选框以及默认值
     */
    handleRecommendToAgency () {
      this.$nextTick(() => {
        // 只有老师角色并且开通了推荐到机构课程库的开关才显示, 并且只有草稿课程或新创建课程才显示，已发布的课程不在显示
        let hasUDLOrCLRData = this.hasUDLOrCLRData()
        this.showRecommendCheckbox = !this.isAdmin() && this.recommendToAgencyOpen && ((this.lessonId && this.lesson.status === 'DRAFT') || this.isAddLesson) && !hasUDLOrCLRData
        // 如果显示推荐课程的复选框，则设置默认值
        if (this.showRecommendCheckbox) {
          // 如果课程 ID 存在，则调用接口获取推荐状态
          if (this.lessonId) {
            Lesson2.getLessonRecommendStatus(this.lessonId).then(res => {
              this.recommendToAgency = equalsIgnoreCase(res.agencyStatus,'PROMOTED')
            })
            return
          }
          // 默认值从 localStorage 中获取，如果没有则默认为 true，（勾选上）
          var defaultValue = localStorage.getItem(getRecommendToAgencyKey())
          this.recommendToAgency = defaultValue !== 'false'
        }
      })
    },
    async beforeChangeMeasures (domains) {
      // 弹窗确认
      this.newMeasureIds = domains.flatMap(domain => domain.children).map(item => item.id)
      // 删除的测评点
      this.removeMeasureIds = this.oldMeasureIds.filter(item => !this.newMeasureIds.includes(item))
      // 如果有框架映射，则将对应的映射测评点的 id 也加入到删除的测评点中
      if (this.mapFrameworkData && this.mapFrameworkData.length > 0) {
        // 遍历测评点，找到映射的测评点
        this.mapFrameworkData.forEach(measure =>
          measure.mappedMeasures && Object.values(measure.mappedMeasures).forEach(mappedList =>
            mappedList.forEach(item => {
              // 如果有映射关系，且删除的测评点中没有映射的测评点，则加入到删除的测评点中
              if (this.removeMeasureIds.includes(item.id) && !this.newMeasureIds.includes(measure.id)) {
                this.removeMeasureIds.push(measure.id)
              }
            }
          ))
        )
      }
      // 如果没有删除的测评点，则查找新增的测评点
      if (!this.removeMeasureIds || this.removeMeasureIds.length === 0) {
        // 新增的测评点
        this.addMeasureIds = this.newMeasureIds.filter(item => !this.oldMeasureIds.includes(item))
        return true
      }
      let confirm = this.lesson.steps.some(step => {
        // 有内容的典型行为 & 标准教学指导测评点
        let typicalHaveContentIds = step.lessonStepGuides.filter(item => item.typicalBehaviors).map(item => item.measureId)
        let teachingHaveContentIds = step.teachingTips.filter(item => item.teachingTips).map(item => item.measureId)
        let combinedContentIds = [...new Set([...typicalHaveContentIds, ...teachingHaveContentIds])]
        // 如果移除的测评点有内容，则弹弹框
        if (this.removeMeasureIds.every(element => combinedContentIds.includes(element))) {
          return true
        }
        return false
      })
      if (confirm) {
        let remove = await this.$confirm(this.$t('loc.lessonDeleteMeasure'), this.$t('loc.cfm'), {
          confirmButtonText: this.$t('loc.confirm'),
          cancelButtonText: this.$t('loc.cancel'),
          showClose: true,
          customClass: 'confirm-dialog',
          confirmButtonClass: 'el-button--danger'
        }).then(() => {
          return true
        }).catch(() => {
          return false
        })
        return remove
      } else {
        return true
      }
    },
    // 课程目标获取焦点
    fillObjective () {
      this.$analytics.sendEvent('web_lesson_library_add_objectives')
    },
    fillLessonName () {
      this.$analytics.sendEvent('web_lesson_library_add_lesson_name')
    },
    changeMeasures () {
      this.$analytics.sendEvent('web_lesson_library_add_select_measures')
      this.lesson.steps.forEach(step => {
        // 如果没有移除测评点，则为新增
        if (!this.removeMeasureIds || this.removeMeasureIds.length === 0) {
          // 典型行为新增测评点
          if (!this.k12Grade(step.ageGroupName)) {
            this.addStepItemMeasure(step.lessonStepGuides, 'typicalBehaviors')
            step.lessonStepGuides = step.lessonStepGuides.filter(item => item.measureId !== '' || item.typicalBehaviors !== '')
          }
          // 标准教学指导新增测评点
          this.addStepItemMeasure(step.teachingTips, 'teachingTips')
          step.teachingTips = step.teachingTips.filter(item => item.measureId !== '' || item.teachingTips !== '')
          return
        }
        // 移除典型行为相应测评点
        step.lessonStepGuides = step.lessonStepGuides.filter(teachingTip => !this.removeMeasureIds.includes(teachingTip.measureId))
        // 如果数据为空添加一个空数据
        if (step.lessonStepGuides.length === 0 && !this.k12Grade(step.ageGroupName)) {
          step.lessonStepGuides.push({ typicalBehaviors: '',
            measureId: '' ,
            measureName: '',
            measureAbbreviation: ''
          })
        }
        // 移除标准教学指导相应测评点
        step.teachingTips = step.teachingTips.filter(teachingTip => !this.removeMeasureIds.includes(teachingTip.measureId))
        if (step.teachingTips.length === 0) {
          step.teachingTips.push({
            teachingTips: '',
            measureId: '',
            measureName: '',
            measureAbbreviation: ''
          })
        }
      })
      // 保存修改的测评点id
      this.oldMeasureIds = this.lesson.measures.flatMap(domain => domain.children).map(item => item.id)
    },
    // 获取课程 quiz 的 bloom 设置
    getQuizBloomSetting () {
      return this.bloomQuizSetting ? 'Understand' : 'DOK2'
    },
    initStepQuiz (step) {
      // 如果不是 K - 12 年纪，则不需要初始化 quiz
      if (!this.k12Grade(step.ageGroupName)) {
        return
      }
      let cache = step.questions
      if (!cache) {
        cache = []
      }
      step.questions = []
      // 当前 quiz 的题号（从 1 开始）
      let currentSortIndex = 1
      this.lesson.measures.flatMap(domain => domain.children).forEach(measure => {
        // 如果缓存中存在数据的时候，将缓存中的数据放入到新的步骤中
        let cacheItem = cache.find(item => item.measureId === measure.id)
        if (cacheItem) {
          step.questions.push(cacheItem)
          return
        }
        // 放入新的 question , 设置好默认值，测评点信息
        step.questions.push({
          type: 'Multiple Choice',
          sortIndex: currentSortIndex++,
          question: '',
          answer: '',
          level: this.getQuizBloomSetting(),
          measureId: measure.id ,
          measureName: measure.name,
          measureAbbreviation: measure.abbreviation
        })
      })
      // 当 questions 为空时，添加一个默认的 question
      if (step.questions.length === 0) {
        step.questions.push({
          type: 'Multiple Choice',
          sortIndex: 1,
          question: '',
          answer: '',
          level: this.getQuizBloomSetting(),
          measureName: '',
          measureId: '',
          measureAbbreviation: ''
        })
      }
    },
    initStepGuide (step) {
      // 初始化步骤的教师指导
      let cache = step.lessonStepGuides
      if (!cache) {
        cache = []
      }
      step.lessonStepGuides = []
      // 如果是 K-12 年级，则不需要初始典型行为
      if (this.k12Grade(step.ageGroupName)) {
        return
      }
      this.lesson.measures.flatMap(domain => domain.children).forEach(measure => {
        // 如果缓存中存在数据的时候，将缓存中的数据放入到新的步骤中
        let cacheItem = cache.find(item => item.measureId === measure.id)
        if (cacheItem) {
          step.lessonStepGuides.push(cacheItem)
          return
        }
        step.lessonStepGuides.push({ typicalBehaviors: '',
          measureId: measure.id ,
          measureName: measure.name,
          measureAbbreviation: measure.abbreviation
        })
      })
      if (step.lessonStepGuides.length === 0) {
         step.lessonStepGuides.push({ typicalBehaviors: '',
          measureId: '' ,
          measureName: '',
          measureAbbreviation: ''
        })
      }
    },
    // 初始化标准教学指导内容
    initTeachingTips (step) {
      let cache = step.teachingTips
      if (!cache) {
        cache = []
      }
      step.teachingTips = []

      this.lesson.measures.flatMap(domain => domain.children).forEach(measure => {
        // 如果缓存中存在数据的时候，将缓存中的数据放入到新的步骤中
        let cacheItem = cache.find(item => item.measureId === measure.id)
        if (cacheItem) {
          step.teachingTips.push(cacheItem)
          return
        }
        step.teachingTips.push({
          teachingTips: '',
          measureId: measure.id,
          measureName: measure.name,
          measureAbbreviation: measure.abbreviation
        })
      })
      if (step.teachingTips.length === 0) {
        step.teachingTips.push({
          teachingTips: '',
          measureId: '',
          measureName: '',
          measureAbbreviation: ''
        })
      }
    },

    // 课程测评点影响 典型行为 & 标准教学指导 新增测评点
    addStepItemMeasure (stepItem, stepItemName) {
      // 查找新增测评点的 id
      let newMeasures = this.lesson.measures.flatMap(domain => domain.children).filter(measure => this.addMeasureIds.includes(measure.id))
      const stepItemMeasureIds = stepItem.map(item => item.measureId)
      // 如果有映射的测评点，则将映射的测评点也加入到新增的测评点中
      if (this.mapFrameworkData && this.mapFrameworkData.length > 0 && stepItemName === 'typicalBehaviors') {
        // 遍历测评点，找到映射的测评点
        this.mapFrameworkData.forEach(measure =>
          measure.mappedMeasures && Object.values(measure.mappedMeasures).forEach(mappedList =>
            mappedList.forEach(item => {
              // 如果有映射关系，且新增的测评点中没有映射的测评点，则加入到新增的测评点中
              if (this.addMeasureIds.includes(item.id) && !stepItemMeasureIds.includes(measure.id)) {
                stepItem.push({
                  stepItemName: '',
                  measureId: measure.id,
                  measureName: measure.name,
                  measureAbbreviation: measure.abbreviation,
                  core: measure.core,
                  mapped: true
                })
              }
            }
          ))
        )
      }
      newMeasures.forEach(measure => {
        // 如果已经存在该测评点，则跳过
        if (stepItemMeasureIds.includes(measure.id)) {
          return
        }
        stepItem.push({
          stepItemName: '',
          measureId: measure.id,
          measureName: measure.name,
          measureAbbreviation: measure.abbreviation,
          core: measure.core
        })
      })
    },

    handleClickPreview () {
      if (this.$route.name === 'AddLesson') {
        // 创建课程预览点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_preview')
      } else if (this.$route.name === 'EditLesson') {
        // 编辑课程预览点击埋点
        this.$analytics.sendEvent('cg_lesson_plan_edit_click_preview')
      }

      this.$analytics.sendEvent('web_lesson_library_add_perview')
      // 更新 UDL 和 CLR 的数据
      this.fillUDLAndCLRData()
      this.previewLesson = this.previewToLesson()
      this.showDetail = true
    },
    isAdmin () {
      return ['agency_owner', 'agency_admin'].includes(this.currentUser.role2.toLowerCase())
    },
    // 生成准备时间和活动时间的选项
    generateTime () {
      let result = []
      let unit = 'mins'
      for (let value = 5; value < 65; value += 5) {
        result.push({
          name: `${value} ${unit}`,
          value: value
        })
      }
      return result
    },
    async loadAgeGroup () {
      // 先判断 sessionStorage 缓存中是否有对应的值
      let ageGroup = sessionStorage.getItem('lessonAgeGroup' + this.currentUser.default_agency_state)
      // 如果缓存中存在，则直接返回
      if (ageGroup) {
        // 设置 ageGroup
        this.ageGroup = JSON.parse(ageGroup)
        return JSON.parse(ageGroup)
      }
      // 如果缓存中没有，则获取后台数据
      let response = await Lesson2.getAgeGroups(this.currentUser.default_agency_state)
      // 设置 ageGroup
      this.ageGroup = response.ageGroups
      // 缓存中放入 ageGroup
      sessionStorage.setItem('lessonAgeGroup' + this.currentUser.default_agency_state, JSON.stringify(response.ageGroups))
      return response.ageGroups
    },
    async loadThemes () {
      // 先判断 sessionStorage 缓存中是否有对应的值
      let themes = sessionStorage.getItem('lessonThemes' + this.currentUserId)
      // 如果缓存中存在，则直接返回
      if (themes) {
        // 设置 themes
        this.themes = JSON.parse(themes)
        return JSON.parse(themes)
      }
      // 如果缓存中没有，则获取后台数据
      let response = await Lesson2.getThemes(true)
      this.themes = response.themes
      // 缓存中放入 themes
      sessionStorage.setItem('lessonThemes' + this.currentUserId, JSON.stringify(response.themes))
      return response.themes
    },
    // 获取扩展的框架 ID
    async loadExtendFramework () {
      // 构建要获取的扩展框架的 ID 列表
      let extendFrameworkIds = []
      // 如果当前的 this.lesson.framework 的 frameworkId 不在 this.frameworks 中，那么就再次获取扩展的框架信息
      if (this.lesson.framework &&
        this.lesson.framework.frameworkId) {
        let extendFramework = this.frameworks.find(item => item.frameworkId === this.lesson.framework.frameworkId)
        // 如果扩展框架存在
        if (extendFramework) {
          return
        }
        // 将要请求的框架 ID 放入扩展的框架 ID 列表中
        extendFrameworkIds.push(this.lesson.framework.frameworkId)
      }
      // 构建请求框架的数组
      let request = {
        extendFrameworkIds: extendFrameworkIds
      }
      // 如果缓存中没有，则获取后台数据
      let response = await Lesson2.getExtendFrameworks(request)
      // 如果 response 中没有数据
      if (!response) {
        return this.frameworks
      }
      // 如果 response 中没有框架
      if (!response.frameworks || response.frameworks.length === 0) {
        return this.frameworks
      }
      // 如果框架存在
      if (this.frameworks) {
        // 将扩展的框架添加进入原有的框架中
        this.frameworks.push(...response.frameworks)
      } else {
        // 否则将扩展的框架直接赋值给 framework
        this.frameworks = response.frameworks
      }
      return this.frameworks
    },
    async loadFrameworks () {
      // 先判断 sessionStorage 缓存中是否有对应的值
      let frameworks = sessionStorage.getItem('lessonFrameworks' + this.currentUserId)
      // 如果缓存中存在，则直接返回
      if (frameworks) {
        // 设置 themes
        this.frameworks = JSON.parse(frameworks)
        // 截取默认框架缓存
        sessionStorage.setItem('lessonFrameworks' + this.currentUserId, JSON.stringify(this.frameworks.slice(0, 4)))
        // 检查是否有框架名称为空的数据
        const hasEmptyName = this.frameworks.some(framework => !framework.frameworkName || framework.frameworkName.trim() === '')
        if (!hasEmptyName) {
          return JSON.parse(frameworks)
        }
      }
      // 如果缓存中没有，则获取后台数据
      let response = await Lesson2.getFrameworks()
      this.frameworks = response.frameworks
      // 缓存中放入 themes
      sessionStorage.setItem('lessonFrameworks' + this.currentUserId, JSON.stringify(response.frameworks))
      return response.frameworks
    },
    // 在 WeekPlan 中加载框架数据
    async weekPlanLoadFrameworks () {
      // 选择框架下拉框数据
      this.frameworks = [this.selectedFramework]
      // 选择框架下拉框已选择数据，用于回显
      this.frameworkId = this.selectedFramework.frameworkId
      // 本课程选择的框架
      this.lesson.framework = this.selectedFramework
      this.lesson.framework.frameworkId = this.selectedFramework.frameworkId
      // 加载测评点数据
      await this.loadMeasures(this.lesson.framework)
      return this.selectedFramework
    },
    // 处理框架数据，将框架的所有叶子节点提取到一级 domain 中
    handleFrameworkData(unCompressMeasures) {
      // 遍历每个 framework
      return unCompressMeasures.map(domain => {
            // 创建一级领域对象
            const newDomain = {
              ...domain,
              children: []
            }

            // 使用迭代而不是递归来收集叶子节点
            const queue = [...domain.children]
            let head = 0 // 使用指针追踪队列头部
            
            while (head < queue.length) {
              const node = queue[head] // O(1) 访问队列头部
              head++ // 移动指针到下一个位置
              
              if (node.children && node.children.length > 0) {
                // 将子节点加入队列尾部继续处理
                queue.push(...node.children)
              } else {
                // 叶子节点直接加入结果中
                newDomain.children.push(node)
              }
            }

            return newDomain
          })
    },
    async loadMeasures (framework) {
      if (!framework || !framework.frameworkId) {
        return
      }
      this.loadingDomains = true
      // 使用 Vuex 中的 getFrameworkDomains 方法获取测评点数据
      let unCompressMeasures = await this.$store.dispatch('curriculum/getFrameworkDomains', {
        frameworkId: framework.frameworkId,
        compress: false
      })
      const measures = this.handleFrameworkData(unCompressMeasures)

      this.loadingDomains = false
      this.domains = measures
      // 如果有要重置的测评点，则在获取完框架下测评点后重置
      if (this.toResetMeasureIds && this.toResetMeasureIds.length > 0) {
        this.setSelectedMeasures(this.toResetMeasureIds)
      }
      // 判断是否是 CA-PTKLF, 如果是则获取映射的测评点
      if (frameworkUtils.isCAPTKLF(framework.frameworkId) || frameworkUtils.isILEARN(framework.frameworkId) || frameworkUtils.isMELS(framework.frameworkId)) {
        this.isMappedFramework = true
        this.typicalBehaviorsType = 'PS'
        Lesson2.getMeasures(constants.pscFrameworkId).then(mappingResponse => {
          this.mappedDomains = mappingResponse.measures
        })
        this.$store.dispatch('getMapFrameworkData', this.lesson.framework.frameworkId).then(res => {
          this.mapFrameworkData = res
        })
      } else {
        this.isMappedFramework = false
        this.typicalBehaviorsType = 'CA-PTKLF'
      }
      return measures
    },
    async listLanguages () {
      // 先判断 sessionStorage 缓存中是否有对应的值
      let languagesCache = sessionStorage.getItem('lessonLanguages' + this.currentUserId)
      // 如果缓存中存在，则直接返回
      if (languagesCache) {
        // 设置 themes
        this.languages = JSON.parse(languagesCache).languages
        this.langCodes = JSON.parse(languagesCache).used
        return JSON.parse(languagesCache)
      }
      // 如果缓存中没有，则获取后台数据
      let response = await DLLApi.listLanguages()
      let { languages, used } = response
      this.languages = languages
      this.langCodes = used
      // 缓存中放入 themes
      sessionStorage.setItem('lessonLanguages' + this.currentUserId, JSON.stringify(response))
      return response
    },
    // 主题名检查
    checkCustomTheme () {
      this.$refs.lessonTheme.query = this.$refs.lessonTheme.query.substring(0, 50)
      return this.$refs.lessonTheme.query
    },
    // 去除 html 标签后，内容是否是空的
    removeHtmlTag (content) {
      // 如果内容不存在时，则说明也是空的
      if (!content) {
        return true
      }
      // 移除HTML标签
      let strippedInput = content.replace(/(<([^>]+)>)/ig,'')
      // 移除特殊字符，包括 &nbsp;
      strippedInput = strippedInput.replace(/&[^\s]*;/g, '')
      // 移除多余的空格
      strippedInput = strippedInput.replace(/\s+/g, ' ').trim()
      return strippedInput.trim() === ''
    },
    // 修改课程主题
    async themeChangeHandler (values) {
      this.$analytics.sendEvent('web_lesson_library_add_select_theme')
      values = values || []
      let themes = this.themes || []
      // 所有的主题 ID
      let themeIds = themes.map(x => x.id)
      // 新增加的主题
      let newThemes = values.filter(x => !themeIds.includes(x))
      // 已选择的主题
      let currentThemes = values.filter(x => themeIds.includes(x)) || []
      // 如果有新主题，调接口添加主题
      if (newThemes.length > 0) {
        let themeName = newThemes[0].substring(0, 50)
        // 生成主题 ID
        let id = tools.uuidv4()
        // 将新增的主题 ID 添加到当前主题列表
        currentThemes.push(id)
        // 调接口添加主题
        await Lesson2.addLessonTheme({
          id: id,
          name: themeName
        })
        .then(res => {
          // 将生成的主题添加到主题列表
          this.themes.push({
            id: id,
            name: themeName,
            custom: true
          })
        })
        .catch(err => {
          // 如果添加主题失败，从当前主题列表中删除
          currentThemes = currentThemes.filter(item => item !== id)
          this.$message.error(err.message)
        })
      }
      // 更新课程主题
      this.$nextTick(() => {
        this.themeIds = currentThemes
      })
      this.lesson.themes = this.themes.filter(item => currentThemes.includes(item.id))
    },
    // 删除课程主题
    deleteLessonTheme (id) {
      // 从课程主题列表中删除
      this.lesson.themes = this.lesson.themes.filter(item => item.id !== id)
      this.themeIds = this.themeIds.filter(item => item !== id)
      // 从主题列表中删除
      this.themes = this.themes.filter(item => item.id !== id)
      let params = {
        id: id
      }
      // 调接口删除主题
      Lesson2.deleteLessonTheme(params)
    },
    previewToLesson () {
      let measures = []
      if (this.lesson.measures && this.lesson.measures.length > 0) {
        this.lesson.measures.forEach(m => {
          m.children.forEach(children => {
            measures.push({
              id: children.id,
              abbreviation: children.abbreviation,
              name: children.name,
              parentId: m.id,
              core: children.core,
              description: children.description
            })
          })
        })
      }
      // 框架下的核心测评点
      let frameworkCoreMeasures = []
      this.domains && this.domains.forEach(d => d.children.forEach(m => m.core && frameworkCoreMeasures.push(m.id)))
      // 为lessonStepGuides添加core属性
      this.lesson.steps.forEach(step => {
        step.lessonStepGuides.forEach(guide => {
          guide.core = false
          if (frameworkCoreMeasures.indexOf(guide.measureId) > -1) {
            guide.core = true
          }
        })
      })
      let steps = this.lesson.steps && JSON.parse(JSON.stringify(this.lesson.steps)) || []
      // 若年龄中含有低年级的数据，则不处理家庭活动，若年龄中只有高年级的数据，则将家庭活动置为空
      let agesTip = ['0', '1', '2', '3', '4', '5', '1,2', 'Grade 1', 'Grade 2']
      // 只要 lesson 中的 ages 中有一个值在 agesTip 中，则不处理家庭活动
      let isLowGrade = this.lesson.ages.some(age => agesTip.includes(age.value))
      if (isLowGrade) {
      } else {
        // 高年级数据
        let ageGroupValue = ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12']
        // 遍历 lessonData.steps，判断 ageeGroupValue 的值是否是高年级，如果是，则将 homeActivity 设置为空
        steps.forEach(step => {
          if (ageGroupValue.includes(step.ageGroupValue)) {
            step.homeActivity = ''
          }
        })
      }

      steps.forEach(step => {
        // media 有响应数据才进行赋值操作
        step.externalMediaUrl = step.media && step.media.externalMediaUrl
        step.externalMediaUrlId = step.media && step.media.externalMediaUrlId
        // 预览时判断 content 内容去除 html 标签后是否有内容
        if (this.removeHtmlTag(step.content)) {
          this.$set(step, 'content', '')
        }
        // 预览时判断 homeActivity 内容去除 html 标签后是否有内容
        if (this.removeHtmlTag(step.homeActivity)) {
          this.$set(step, 'homeActivity', '')
        }
        // 预览时判断 CLR 内容去除 html 标签后是否有内容
        if (this.removeHtmlTag(step.culturallyResponsiveInstruction)) {
          this.$set(step, 'culturallyResponsiveInstruction', '')
        }
        // 预览时判断 CLR 内容去除 html 标签后是否有内容
        if (this.removeHtmlTag(step.culturallyResponsiveInstructionGroup)) {
          this.$set(step, 'culturallyResponsiveInstructionGroup', '')
        }
        // 判断资源 CLR 是否存在
        if (step.lessonClrAndSources && step.lessonClrAndSources.clr) {
          // 去除 html 标签后是否是否有内容，如果内容不存在时，则将资源 CLR 置为空
          if (this.removeHtmlTag(step.lessonClrAndSources.clr)) {
            this.$set(step.lessonClrAndSources, 'clr', '')
          }
        }
        // 过滤掉空的 question
        if (step.questions) {
          // 如果问题或者答案不为空
          step.questions = step.questions.filter(question => (question.question && question.question.trim()) !== '' || (question.answer && question.answer.trim() !== ''))
          // 根据题号重新排序
          step.questions.sort((a, b) => a.sortIndex - b.sortIndex)
          // 重新设置题号
          step.questions.forEach((question, index) => {
            question.sortIndex = index + 1
          })
        }
      })
      if (this.lesson.material && this.lesson.material.description) {
        let nodes = this.lesson.material.description && new DOMParser().parseFromString(this.lesson.material.description, 'text/html')
        // 处理课程材料描述的富文本结构：关闭 Dom 的可编辑状态
        nodes = this.lessonMaterialDescriptionFormat(nodes)
        this.lesson.material.description = nodes.body.innerHTML
      }
      return {
        id: this.lesson.id,
        // 传递一个值表示来自与预览
        preview: true,
        showCoreMeasureOpen: frameworkCoreMeasures.length > 0,
        isAdaptedLesson: this.lesson.isAdaptedLesson || this.isAdaptedLesson,
        name: this.lesson.name,
        prepareTime: this.lesson.prepareTime,
        activityTime: this.lesson.activityTime,
        activityType: this.lesson.activityType,
        subclassCenter: this.subclassCenter,
        activityTheme: this.lesson.activityTheme,
        coverMedias: this.lesson.cover && [this.lesson.cover] || [],
        ages: this.lesson.ages && this.lesson.ages.map(age => age.name),
        ageValues: this.lesson.ages && this.lesson.ages.map(age => age.value),
        classroomType: this.lesson.classroomType,
        framework: this.lesson.framework && {
          id: this.lesson.framework.frameworkId,
          name: this.lesson.framework.frameworkName
        },
        domains: this.lesson.measures && this.lesson.measures.map(item => {
          return {
            id: item.id,
            name: item.name,
            abbreviation: item.abbreviation
          }
        }),
        updateTime: new Date().getTime(),
        themes: this.lesson.themes,
        measures: measures,
        objectives: this.lesson.objective && [this.lesson.objective],
        materials: this.lesson.material && {
          descriptions: this.lesson.material && this.lesson.material.description && [this.lesson.material.description],
          media: this.lesson.material && this.lesson.material.media,
          externalMediaUrl: this.lesson.material && this.lesson.material.media && this.lesson.material.media.externalMediaUrl, // 增添外部链接URL
          attachmentMedias: this.lesson.material.attachmentMedias && this.lesson.material.attachmentMedias.map(attachment => {
            return {
              id: attachment.id,
              size: attachment.size,
              sourceFileName: attachment.name,
              url: attachment.url
            }
          })
        },
        steps: steps,
        books: this.lesson.book && [this.lesson.book],
        learnerProfiles: this.lesson.learnerProfiles || [],
        videoBooks: this.lesson.videoBook && [this.lesson.videoBook],
        attachmentMedias: this.lesson.attachments && this.lesson.attachments.map(attachment => {
          return {
            id: attachment.id,
            size: attachment.size,
            sourceFileName: attachment.name,
            url: attachment.url
          }
        }),
        authorName: this.currentUser.display_name || this.lesson.authorName,
        dlls: (this.lesson.dlls || []).filter(dll => {
          // // 判断是否有翻译列表,没找到为 undefined、找到为翻译对象
          // let language = dll.languages.find(language=>language.content && language.content.trim())
          // return (dll.title && dll.title.trim()) && (dll.content && dll.content.trim()) && language
          return dll.content && dll.content.trim()
        }).map(dll => ({
          id: dll.id,
          title: dll.title,
          content: dll.content,
          medias: dll.media && [{ ...dll.media, mediaUrl: dll.media.url }] || [],
          languages: (dll.languages || [])
            .map(item => {
              let language = this.languages.find(lang => lang.code === item.langCode)
              let result = { ...item }
              if (language) {
                Object.assign(result, language)
                result.lang_en = language.name
                result.lang = language.originalName
              }
              return result
            })
        }))
      }
    },
    collectData () {
      let copyLesson = JSON.parse(JSON.stringify(this.lesson))
      let measureIds = copyLesson.measures.reduce((prev, domain) => {
        let domains = [domain]
        let measures = []
        while (domains.length) {
          let temp = domains.pop()
          // 测评点无孩子节点
          if (!temp.children || !temp.children.length) {
            measures.push(temp)
            continue
          }
          // 有孩子节点的为领域
          domains.push(...temp.children)
        }
        return prev.concat(measures.map(m => m.id))
      }, [])
      let materials = { descriptions: [] }
      if (copyLesson.material) {
        copyLesson.material.media && (materials.mediaId = copyLesson.material.media.id)
        // 添加外部媒体资源链接
        copyLesson.material.media && (materials.externalMediaUrlId = copyLesson.material.media.externalMediaUrlId)
        if (copyLesson.material.description) {
          let nodes = copyLesson.material.description && new DOMParser().parseFromString(copyLesson.material.description, 'text/html')
          // 处理富文本结构：关闭 Dom 的可编辑状态
          nodes = this.lessonMaterialDescriptionFormat(nodes)
          copyLesson.material.description = nodes.body.innerHTML
          materials.descriptions.push(copyLesson.material.description)
        }
        materials.attachmentMediaIds = copyLesson.material.attachmentMedias.map(media => media.id)
      }
      // 处理 CLR 资源数据
      copyLesson.steps.forEach(step => {
        // 处理 quiz
        if (step.questions && step.questions.length !== 0) {
          step.questions = step.questions
            .filter(question => (question.question && question.question.trim() !== '') || (question.answer && question.answer.trim() !== ''))
            .sort((a, b) => a.sortIndex - b.sortIndex)
          step.questions.forEach((question, index) => {
            question.sortIndex = index + 1
          })
        }
        // 实施步骤资源处理
        this.impStepSourceHandle(step)
        // 如果资源内容不存在，则直接返回即可
        if (!step.lessonClrAndSources || !step.lessonClrAndSources.sources) {
          return
        }
        // 遍历资源列表
        step.lessonClrAndSources.sources.forEach(source => {
          // 获取角标
          let subscript = source.subscript
          // 拼接需要搜索的文本
          const searchTxt = `<a href="#subscript${subscript}" class="text-primary">[${subscript}]</a>`
          // 如果该资源隐藏了，则保存的时候直接删除即可
          if (source.hidden) {
            step.lessonClrAndSources.clr.replace(new RegExp(searchTxt, 'g'), '')
          }
        })
        step.lessonClrAndSources.sources = step.lessonClrAndSources.sources.filter(source => !source.hidden)
        // 如果 CLR 内容去除 html 为空时，则将 CLR 内容设置为空
        if (this.removeHtmlTag(step.culturallyResponsiveInstruction)) {
          this.$set(step, 'culturallyResponsiveInstruction', '')
        }
        // 如果班级定制 CLR 内容去除 html 为空时，则将 CLR 内容设置为空
        if (this.removeHtmlTag(step.culturallyResponsiveInstructionGroup)) {
          this.$set(step, 'culturallyResponsiveInstructionGroup', '')
        }
        // 判断带角标的 CLR 内容去除 html 标签后是否为空，则将资源 CLR 内容置为空
        if (this.removeHtmlTag(step.lessonClrAndSources.clr)) {
          step.lessonClrAndSources.clr = ''
        }
      })
      let lesson = {
        id: copyLesson.id,
        name: copyLesson.name,
        isAdaptedLesson: copyLesson.isAdaptedLesson,
        ages: copyLesson.ages.map(item => item.value),
        ageGroupNames: copyLesson.ages.map(item => item.name),
        themeIds: copyLesson.themes.map(item => item.id),
        prepareTime: copyLesson.prepareTime,
        activityTime: copyLesson.activityTime,
        activityType: copyLesson.activityType,
        activityTheme: copyLesson.activityTheme,
        frameworkId: copyLesson.framework && copyLesson.framework.frameworkId,
        measureIds: measureIds,
        objectives: copyLesson.objective && [copyLesson.objective] || [],
        materials: materials,
        steps: copyLesson.steps.map(step => ({
          ageGroupName: step.ageGroupName,
          ageGroupValue: step.ageGroupValue,
          content: step.content,
          universalDesignForLearning: step.universalDesignForLearning, // 差异化教学
          universalDesignForLearningGroup: step.universalDesignForLearningGroup, // 差异化教学
          culturallyResponsiveInstruction: step.culturallyResponsiveInstruction, // 文化教学内容
          culturallyResponsiveInstructionGeneral: step.culturallyResponsiveInstructionGeneral, // 文化教学内容
          culturallyResponsiveInstructionGroup: step.culturallyResponsiveInstructionGroup, // 文化教学内容
          homeActivity: step.homeActivity, // 家庭活动
          mediaId: step.media && step.media.id,
          // 添加 externalMediaId
          externalMediaUrlId: step.media && step.media.externalMediaUrlId,
          lessonStepGuides: step.lessonStepGuides,
          teachingTips: step.teachingTips,
          lessonClrAndSources: step.lessonClrAndSources,
          lessonImpStepAndSource: step.lessonImpStepAndSource,
          questions: step.questions,
          mixedAgeDifferentiations: step.mixedAgeDifferentiations,
          lessonScienceOfReadingModel: step.lessonScienceOfReadingModel,
          showMixedAge: this.enableMixedAgeGroup || step.showMixedAge,
          lessonTemplate: step.lessonTemplate,
          lessonImpStepAndSourceStr: step.lessonImpStepAndSourceStr,
          adaptedModuleSwitch: this.adaptedModuleSwitch || null // 轻量导入改编时当前模块是否显示
        })),
        learnerProfiles: copyLesson.learnerProfiles || [],
        coverMediaIds: copyLesson.cover && [copyLesson.cover.id],
        coverExternalMediaUrlId: copyLesson.cover && copyLesson.cover.externalMediaUrlId, // 增加封面字段
        books: copyLesson.book && [copyLesson.book] || [],
        videoBooks: copyLesson.videoBook && [copyLesson.videoBook] || [],
        attachmentMediaIds: copyLesson.attachments.map(media => media.id),
        dlls: copyLesson.dlls
          .filter(dll => {
            return dll.content && dll.content.trim() || dll.media
          })
          .map(dll => ({
            id: dll.id,
            title: dll.title,
            description: dll.description,
            content: dll.content,
            mediaIds: dll.media && [dll.media.id] || [],
            languages: (dll.languages || []).map(item => ({ langCode: item.langCode, content: item.content }))
          })),
        recommendToAgency: this.showRecommendCheckbox ? this.recommendToAgency : false,
        assistantAdaptInfo: copyLesson.assistantAdaptInfo,
        classroomType: copyLesson.classroomType || 'IN_PERSON'
      }
      // 若年龄中含有低年级的数据，则不处理家庭活动，若年龄中只有高年级的数据，则将家庭活动置为空
      let agesTip = ['0', '1', '2', '3', '4', '5', '1,2', 'Grade 1', 'Grade 2']
      // 只要 lesson 中的 ages 中有一个值在 agesTip 中，则不处理家庭活动
      let isLowGrade = lesson.ages.some(age => agesTip.includes(age))
      if (isLowGrade) {
        return lesson
      } else {
        // 高年级数据
        let ageGroupValue = ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12']
        // 遍历 lessonData.steps，判断 ageeGroupValue 的值是否是高年级，如果是，则将 homeActivity 设置为空
        lesson.steps.forEach(step => {
          if (ageGroupValue.includes(step.ageGroupValue)) {
            step.homeActivity = ''
          }
        })
        this.$store.dispatch('setEnableMixedAgeGroup', false)
        return lesson
      }
    },

    // 判断实施步骤资源是否有隐藏的，有的话进行删除；资源不存在进行初始化
    impStepSourceHandle (step) {
      if (!step.lessonImpStepAndSource) {
        // 提取所有被 [数字] 包裹的内容
        const subscriptRegex = /\[(\d+)\]/g

        // 找到所有匹配的数字
        let matches = step.content ? [...step.content.matchAll(subscriptRegex)] : []

        // 遍历匹配结果
        matches.forEach((match) => {
          let subscript = match[1] // 提取数字部分

          // 构建用于匹配 <imp-script> 的正则表达式
          const impScriptRegex = new RegExp(`<imp-script[^>]*>.*?\\[${subscript}\\].*?</imp-script>`, 'gs')

          // 替换匹配的 <imp-script> 标签内容为空
          step.content = step.content.replace(impScriptRegex, '')
        })
        return
      }
      step.lessonImpStepAndSource.impStepContent = step.content
      // 资源列表如果不为空的话，则遍历资源列表，将隐藏的资源删除
      if (step.lessonImpStepAndSource.sources && step.lessonImpStepAndSource.sources.length > 0) {
        // 遍历资源列表
        step.lessonImpStepAndSource.sources.forEach(source => {
          // 获取角标
          let subscript = source.subscript
          // 构建正则表达式，匹配 <imp-script></imp-script> 标签及其内容
          const regex = new RegExp(`<imp-script[^>]*>.*?\\[${subscript}\\].*?</imp-script>`, 'gs')
          // 如果该资源隐藏了，则保存的时候直接删除即可
          if (source.hidden) {
            step.lessonImpStepAndSource.impStepContent = step.lessonImpStepAndSource.impStepContent.replace(regex, '')
          }
        })
        // 判断后的数据给 content
        step.content = step.lessonImpStepAndSource.impStepContent
        step.lessonImpStepAndSource.sources = step.lessonImpStepAndSource && step.lessonImpStepAndSource.sources &&
          step.lessonImpStepAndSource.sources.filter(source => !source.hidden)
      }
    },

    /**
     * 获取周计划中使用的课程信息
     */
    getWeekPlanLessonDetail () {
      let measures = this.lesson.measures.reduce((prev, domain) => {
        let domains = [domain]
        let measures = []
        while (domains.length) {
          let temp = domains.pop()
          // 测评点无孩子节点
          if (!temp.children || !temp.children.length) {
            measures.push(temp)
            continue
          }
          // 有孩子节点的为领域
          domains.push(...temp.children)
        }
        return prev.concat(measures.map(m => m.abbreviation))
      }, [])
      let isAdaptedLesson = !!this.lesson.steps.find(x => x.universalDesignForLearningGroup || x.culturallyResponsiveInstructionGroup)
      return {
        id: this.lesson.id,
        name: this.lesson.name,
        coverMediaUrls: (this.lesson.cover && [this.lesson.cover.url]) || [],
        measure: measures,
        authorId: this.currentUser.user_id,
        isAdaptedLesson: isAdaptedLesson
      }
    },
    // 校验课程中的数据
    async validateLesson () {
      // 调用 UDL 的保存方法
      this.fillUDLAndCLRData()
      // 表单校验，失败返回
      try {
        // 校验主表单
        let isMainFormValid = true;
        // 校验 SOR 表单
        const scienceOfReading = this.$refs.scienceOfReading;
        let isSorFormValid = true;
        if (scienceOfReading && scienceOfReading.length > 0) {
          for (const sor of scienceOfReading) {
            try {
              await sor.$refs['sorForm'].validate();
            } catch (sorErr) {
              isSorFormValid = false;
              // 标记 SOR 表单错误
              sor.$refs['sorForm'].$el.classList.add('is-error');
            }
          }
        }

        // 如果主表单或 SOR 表单校验失败
        if (!isMainFormValid || !isSorFormValid) {
          setTimeout(() => {
            let formItemsWithError = this.$el.querySelectorAll('.is-error');
            // 过滤出可见的 DOM
            formItemsWithError = tools.getVisibleDom(formItemsWithError);
            let firstItem = formItemsWithError[0];
            if (firstItem) {
              firstItem.dispatchEvent(new CustomEvent('validate-failed', {}));
              firstItem.scrollIntoView({ block: 'center' });
            }
          }, 0);
          return false
        }
      } catch (err) {
      }
      return true
    },
    async publishLesson (planId, sourceVersionId, validateLesson = false, needRedirect = false) {
      this.fillUDLAndCLRData()
      // 课程库中的发布课程埋点
      if (!this.inDialog) {
        this.$analytics.sendEvent('web_lesson_library_add_public')
      }
      // 表单校验，失败返回
      try {
        // 校验主表单
        let isMainFormValid = true;
        if (validateLesson) {
          try {
            await this.$refs['form'].validate();
          } catch (mainFormErr) {
            isMainFormValid = false;
          }
        }
        // 校验 SOR 表单
        const scienceOfReading = this.$refs.scienceOfReading;
        let isSorFormValid = true;
        if (scienceOfReading && scienceOfReading.length > 0) {
          for (const sor of scienceOfReading) {
            try {
              await sor.$refs['sorForm'].validate();
            } catch (sorErr) {
              isSorFormValid = false;
              // 标记 SOR 表单错误
              sor.$refs['sorForm'].$el.classList.add('is-error');
            }
          }
        }

        // 如果主表单或 SOR 表单校验失败
        if (!isMainFormValid || !isSorFormValid) {
          setTimeout(() => {
            let formItemsWithError = this.$el.querySelectorAll('.is-error');
            // 过滤出可见的 DOM
            formItemsWithError = tools.getVisibleDom(formItemsWithError);
            let firstItem = formItemsWithError[0];
            if (firstItem) {
              firstItem.dispatchEvent(new CustomEvent('validate-failed', {}));
              firstItem.scrollIntoView({ block: 'center' });
            }
          }, 0);
          return Promise.resolve(); // 确保返回一个已解决的 Promise
        }
      } catch (err) {
        return Promise.reject(err); // 返回一个被拒绝的 Promise
      }
      if (this.saving === 1 || this.saving === 2) {
        return Promise.resolve(); // 确保返回一个已解决的 Promise
      }
      // 发布课程
      this.saving = 1
      // // 是否是草稿发布
      // let isDraft = false;
      // if (this.lesson.status === 'DRAFT') {
      //   isDraft = this.isNeedManualPublish
      // }
      // 记录原来 changesCounter 的值
      const originalChangesCounter = this.changesCounter
      // 记录原来 lesson 的值
      const originalLesson = JSON.parse(JSON.stringify(this.lesson))
      try {
        let lessonData = this.collectData()
         // isSetObjectiveType 说明用户没有设置过 objective 类型,并且当前课程不是改编课程,并且不是复制课程
        if (this.isSetObjectiveType && !this.isAdaptedLesson && equalsIgnoreCase(this.lesson.copySourceLessonId, '00000000-0000-0000-0000-000000000000')) {
          // 调用接口，保存用户选择的 objective 类型
          LessonApi.setLessonObjectiveType("formal_description")
        }
        // 从路由中获取单元 ID
        const unitId = this.$route.query.unitId
        // 判断单元 ID 是否存在
        if (unitId && this.singleAdaptLesson) {
          lessonData.unitId = unitId
        }
        if (this.inCurriculumGeniePage()) {
          lessonData.recommendToAgency = false
        }
        // 增加课程的 itemId 和 planId
        if (this.itemId && planId) {
          lessonData.itemId = this.itemId
          lessonData.planId = planId
        }
        if (sourceVersionId) {
          lessonData.sourceVersionId = sourceVersionId
        }
        // 设置描述信息
        lessonData.description = this.generateChangeDescription(this.sourceLesson, this.lesson);
        // 设置是否是增强课程
        lessonData.enhanceLesson = this.isEnhanceLesson
        // 设置需要记住该版本
        lessonData.needRecordVersion = true
        // 设置需要记录改编信息
        lessonData.needRecordAdaptInfo = this.needRecordAdaptInfo
        // 记录一次后置该变量为 false，避免重复记录
        this.needRecordAdaptInfo = false
        // 等待数据都更新完成
        await this.$nextTick()
        this.sourceLesson = JSON.parse(JSON.stringify(this.lesson))
        this.changesCounter = 0
        const lessonInfo = await Lesson2.publishLesson(lessonData).then(res => {
          this.$bus.$emit('clearResourceHistory', res.lessonId)
          return res
        })
        // 发布成功后清空临时草稿 ID
        this.clearTempDraftLessonId()
        // 如果有草稿后的发布，进行 ID 同步
        if (this.hasSaveDraft) {
          // 发布完课程后，重新初始化 lesson
          await this.initLesson(true)
          this.hasSaveDraft = false
        }
        this.sourceLesson.id = lessonInfo.lessonId
        this.lesson.id = lessonInfo.lessonId
        this.lesson.status = 'PUBLISHED'
        this.sourceLesson.status = 'PUBLISHED'
        this.lastPublishTime = this.$moment.utc(lessonInfo.publishTime).local().toDate()
          // 课程发布完成之后修改 initLessonData
          this.initLessonData = JSON.stringify(this.collectData())
          if (lessonInfo && lessonInfo.lessonId && this.inDialog) {
              this.$emit('updateWeeklyLessonId', {
                  oldLessonId: this.lessonId,
                  newLessonId: lessonInfo.lessonId
              })
              // 如果当前的课程 Id 并不是 publish 的课程 Id，那么就是草稿，这个时候更新草稿的内容
              if (this.lesson.id !== lessonInfo.lessonId) {
                  await Lesson2.saveLessonDraft(lessonData)
              }

            // 周计划中的课程发布成功后，更新 PlanItem 数据
            if (lessonData.coverMediaIds && lessonData.coverMediaIds.length > 0) {
              this.$bus.$emit('updateLessonItemBusOn', { itemId: this.itemId, mediaId: lessonData.coverMediaIds[0].toUpperCase() })
            } else {
              this.$bus.$emit('updateLessonItemBusOn', { itemId: this.itemId, mediaId: null })
            }
          }
        if (this.inWeeklyPlannDialog) {
            // 如果在周计划中，封装周计划保存需的数据
            let LessonDetail = {}
            LessonDetail['id'] = lessonInfo.lessonId
            LessonDetail['name'] = this.lesson.name
            // 封装测评点缩写数组
            let allMeasures = []
            let measures = []
            frameworkUtils.getMeasuresBottom(this.lesson.measures,allMeasures)
            allMeasures.forEach(measure => {
              measures.push(measure.abbreviation)
            })
            LessonDetail['coverMediaUrls'] = [this.lesson.cover && this.lesson.cover.url]
            LessonDetail['measure'] = measures
            LessonDetail['authorId'] = this.currentUser.user_id
            // 传值设置项目课程为新加的课程
            this.$emit('callSelectLesson', LessonDetail)
            // this.$emit('closeLessonGenerateDialog')
        }
          // 如果不是在编辑弹窗中发布课程，跳转到课程列表
        if (!this.inDialog && !this.inWeeklyPlannDialog) {
          // 如果是草稿发布，则跳转到课程列表
          if (this.inCurriculumGeniePage() && needRedirect) {
            this.$router.push({
              name: 'lesson-plan-List'
            })
          } else if (this.$route.name == 'EditLesson' && needRedirect) {
            this.$router.push({
              name: 'TeacherLessonList'
            })
          } else if (equalsIgnoreCase(this.$route.name,'Replicate')) {
            this.$router.push({
              name: this.isAdmin() && (this.open.educator && 'TeacherLessonList') || 'TeacherLessonList'
            })
          } else {
            if (needRedirect) {
              this.$router.push({
              name: this.isAdmin() && (this.open.educator && 'TeacherLessonList' || 'ManagedTeacherLessonList') || 'TeacherCreateLesson'
            })
            }
          }
        }
        // 发布成功后，更新本地推荐到机构课程库的值
        if (this.showRecommendCheckbox) {
          localStorage.setItem(getRecommendToAgencyKey(), this.recommendToAgency)
        }
        // 只有在非批量编辑并且是草稿发布的时候才会出现这个弹窗
        if (!this.batchEdit && needRedirect) {
            // 如果勾选了推荐到机构课程库复选框，则提示用户成功发布课程并推荐到机构课程库
            if (!this.isAdmin() && lessonData.recommendToAgency) {
                this.$message({
                    message: this.$t('loc.lessonPublishAndRecommendAgency'),
                    type: 'success'
                })
            } else {
                this.$message({
                    message: this.$t('loc.lesson2Publish'),
                    type: 'success'
                })
            }
        }
        return this.getWeekPlanLessonDetail()
      } catch (err) {
        // 还原 changesCounter
        this.changesCounter = originalChangesCounter
        // 还原 sourceLesson
        this.sourceLesson = JSON.parse(JSON.stringify(originalLesson))
        // 发布失败,进行提示
        this.$message({ type: 'error', message: err.message || this.$t('loc.fieldReq') })
      } finally {
        this.saving = 0
      }
    },
      /**
       * 填充 UDL 和 CLR 数据
       */
      fillUDLAndCLRData () {
          // 如果没有年龄组，那么就没有 UDL 和 CLR，就不用走下面的流程
          if (!(this.ageValues && this.ageValues.length > 0)) {
              return
          }
          // 调用 UDL 的保存方法
          this.$refs.universalDesignLearning && this.$refs.universalDesignLearning.forEach(item => {
              // 获取 lessonAgeGroup
              const lessonAgeGroup = item.lessonAgeGroup
              // 从 lesson.steps 中找到和 lessonAgeGroup 相同的 ageGroupName 的 step
              const findStep = this.lesson.steps.find(item => item.ageGroupName === lessonAgeGroup)
              // 获取 generalUniversalDesignForLearning
              const generalUniversalDesignForLearning = item.generalUniversalDesignForLearning
              const mixedAgeGroup = item.mixedAgeGroup()
              // 使用 $set 修改 findStep 中的 universalDesignForLearning
              this.$set(findStep, 'universalDesignForLearning', generalUniversalDesignForLearning)
              // 获取 teacherGroups
              const teacherGroups = item.teacherGroups
              // 如果 teacherGroups 有值，则将 teacherGroups 的值赋值给 findStep 中的 universalDesignForLearningGroup
              if (teacherGroups && teacherGroups.length > 0) {
                  // 更新 teacherGroups
                  teacherGroups.forEach(teacher => item.changeTeacherChildrenData(teacher))
                  this.$set(findStep, 'universalDesignForLearningGroup', JSON.stringify(teacherGroups))
              } else {
                  this.$set(findStep, 'universalDesignForLearningGroup', '')
              }
              // 如果 mixedAgeGroup 有值，则将 mixedAgeGroup 的值赋值给 findStep 中的 universalDesignForLearningGroup
              if (mixedAgeGroup && mixedAgeGroup !== '') {
                this.$set(findStep, 'mixedAgeDifferentiations', mixedAgeGroup)
              }
              if (item.clearMixAgeData) {
                this.$set(findStep, 'mixedAgeDifferentiations', '')
              }
          })
          // 保存 CLR 内容
          this.$refs.culturallyLinguisticallyResponsive && this.$refs.culturallyLinguisticallyResponsive.forEach(item => {
              // 获取 CLR 组件中的值
              const culturallyResponsiveInstruction = item.getGeneralCLRValue
              // 获取 CLR 组件中的解析过得原始的课程数据
              const culturallyResponsiveInstructionGeneral = item.culturallyResponsiveInstructionGeneral || item.generalValue
              const culturallyResponsiveInstructionGroup = item.getClassSpecificCLRValueWithChildren
              const ageGroupName = item.ageGroupName
              // 从 lesson.steps 中找到和 lessonAgeGroup 相同的 ageGroupName 的 step
              const findStep = this.lesson.steps.find(item => item.ageGroupName === ageGroupName)
              if (findStep) {
                  // 如果找到了就进行更改
                  this.$set(findStep, 'culturallyResponsiveInstruction', culturallyResponsiveInstruction)
                  this.$set(findStep, 'culturallyResponsiveInstructionGroup', culturallyResponsiveInstructionGroup)
                  // 如果 CLR 的值不存在，或者 CLR 的值为空时，将 CLR 的值设置为 CLR 的原始值
                  if (!findStep.culturallyResponsiveInstructionGeneral || findStep.culturallyResponsiveInstructionGeneral === '') {
                    this.$set(findStep, 'culturallyResponsiveInstructionGeneral', culturallyResponsiveInstructionGeneral)
                  }
              }
          })
      },
    async saveDraft (setTempDraftId = false) {
      if (this.saving === 1 || this.saving === 2) {
        return
      }
      this.fillUDLAndCLRData()
      this.saving = 2
      try {
        this.changesCounter = 0
        this.sourceLesson = JSON.parse(JSON.stringify(this.lesson))
        let draft = await Lesson2.saveLessonDraft(this.collectData())
        this.hasSaveDraft = true
        this.sourceLesson.id = draft.id
        this.lesson.id = draft.id
        this.lastPublishTime = this.$moment.utc(draft.updateAtUtc).local().toDate()
        
        // 如果需要设置临时草稿 ID（用于 AI 生成过程）
        if (setTempDraftId) {
          this.setTempDraftLessonId(draft.id)
        }
        
        let newSteps = JSON.parse(draft.steps)
        if (newSteps) {
          this.lesson.steps.forEach(step => {
            let newStep = newSteps.find(newStep => newStep.ageGroupName === step.ageGroupName)
            // 只有在后端返回的 lessonTemplate 有值时才覆盖前端的值，避免数据丢失
            if (newStep && newStep.lessonTemplate) {
              step.lessonTemplate = newStep.lessonTemplate
            }
          })
          this.sourceLesson.steps.forEach(step => {
            let newStep = newSteps.find(newStep => newStep.ageGroupName === step.ageGroupName)
            // 只有在后端返回的 lessonTemplate 有值时才覆盖前端的值，避免数据丢失
            if (newStep && newStep.lessonTemplate) {
              step.lessonTemplate = newStep.lessonTemplate
            }
          })
        }
        return draft
      } catch (err) {
        throw err
      } finally {
        this.saving = 0
      }
    },
    async submitDraft () {
      this.$analytics.sendEvent('web_lesson_library_add_save_draft')
      if (!this.lesson.name || !this.lesson.name.trim()) {
        this.$message.error(this.$t('loc.lesson2LessonNameRequired'))
        return
      }
      try {
        await this.saveDraft()
        this.$message.success(this.$t('loc.lesson2LessonSave'))
        if (this.$route.path.includes('curriculum-genie')) {
          this.$router.push({
            name: 'lesson-plan-List'
          })
        } else {
          if (this.isAdmin()) {
            this.$router.push({
              name: 'ManageDraft'
            })
          } else {
            this.$router.push({
              name: 'TeacherDraftLesson'
            })
          }
        }
      } catch (err) {
        this.$message({ type: 'error', message: this.$t('loc.fieldReq') })
      }
    },
    extractMedia (media) {
      media = media || {}
      return {
        id: media.id,
        name: media.sourceFileName,
        url: media.url,
        size: media.size,
        source: media.source
      }
    },
    // 取两个对象数组的交集
    arrIntersection (arr1, arr2) {
      let intersection = []
      for (let i = 0; i < arr1.length; i++) {
        for (let j = 0; j < arr2.length; j++) {
          if (arr1[i].name === arr2[j].ageGroupName) {
            intersection.push(arr2[j])
          }
        }
      }
      return intersection
    },
    async initLesson (refresh = false) {
      // 查询课程详情
      let lessonId = this.lessonId || this.lesson.id
      let detail = await Lesson2.getLessonLastDraftDetail(lessonId) || await Lesson2.getLessonDetail({ lessonId: lessonId })
      this.fromUnit ={
        title: detail.unitTitle || null,
        id: detail.unitId || null
      }
      // 如果是编辑现有课程，设置lastPublishTime为当前时间
      this.lastPublishTime = this.$moment.utc(detail.updateTime).local().toDate()
      this.handleLessonDetail(detail)
      this.quizHasResource()
      // 将初始化的年龄组和课程步骤加入缓存
      this.oldAgeValues = this.ageValues
      this.stepsCache = this.lesson.steps
      this.oldFrameworkId = this.frameworkId
      this.oldMeasureIds = this.lesson.measures.flatMap(domain => domain.children).map(item => item.id)
      if (refresh) {
        return
      }
      // 如果课程加载完毕了，那么就使用 this.$bus.$emit 向外发送一个挂在完成的事件
      this.$bus.$emit('lessonLibraryEditorIndexMounted', this.hasUDLOrCLRData())
      // 发送课程请求完毕的时间
      this.$nextTick(() => {
          this.$bus.$emit('lessonDetailCompleted', this.itemId)
      })
      // 如果是要 adapt 课程，那么就设置监听 UDL 和 CLR 的加载完成事件
      if (this.adaptLesson) {
        // 如果当前课程已经 adapted，就不用复制了
        if (!this.lesson.isAdaptedLesson && !this.isAdaptedLesson) {
           // 复制课程
          await this.copyAdaptLesson(this.lesson)
        }
        // 设置监听 UDL 和 CLR 的生成完成事件
        this.setUDLAndCLRGenerateCompletedListener()
        // 设置监听 UDL 和 CLR 的加载完成事件
        this.setUDLAndCLRLoadCompletedListener()
      }
    },
    handleLessonDetail (detail){
      // 初始化编辑器信息
      let steps = detail.steps || []
      // 课程主题
      let themes = detail.themes || []
      // 先判断当前用户的年龄组是否包含
      // let intersection =  this.arrIntersection(this.ageGroup, steps)
      // if (intersection.length === 0) {
      //   this.ageValues = []
      //   steps = []
      // } else {
      //   this.ageValues = intersection.map(item => item.ageGroupValue)
      //   steps = intersection
      // }
      this.ageValues = steps.map(item => item.ageGroupValue)
      // 判断是否是多选，若课程年龄组有多个，则为多选
      if (this.ageValues && this.ageValues.length > 1) {
        this.isSelectMultiple = true
      }
      // 课程下所有的家庭活动
      let homeActivity = steps.map(item => item.homeActivity)
      // 只要年龄组的值包含 1 2 3 4 5 6 7 其中任意一个就显示家庭活动
      let values = detail.ageValues
      // 判断是否是多选，若课程年龄组有多个，则为多选
      if (values.includes('0') || values.includes('1') || values.includes('2') || values.includes('3') || values.includes('4') || values.includes('5') || values.includes('1,2') || values.includes('Grade 1') || values.includes('Grade 2')) {
        this.isShowAtHome = true
      } else {
        // homeActivity 有值，则 isShowAtHome 为 true，目前为了兼容老数据，只要有值就显示
        if (homeActivity.some(item => item)) {
          this.isShowAtHome = true
        } else {
          this.isShowAtHome = false
        }
      }
      // 课程的所有主题 ID
      let themeIds = themes.map(item => item.id)
      // 可选的所有课程主题
      let allThemeIds = this.themes.map(item => item.id)
      // 将现有的主题 ID 与可选的主题 ID 进行交集，得出现在课程的主题 ID（将已删除的主题 ID去除掉）
      this.themeIds = allThemeIds.filter(id => themeIds.includes(id))
      detail.framework && (this.frameworkId = detail.framework.id)
      if (this.frameworkId) {
        this.$nextTick(() => {
          // 判断框架是否在框架列表中，如果不在框架列表，则从 unit planner 框架列表中获取
          let framework = this.frameworks.find(item => item.frameworkId.toLowerCase() === this.frameworkId.toLowerCase())
          if (!framework) {
            let lessonFramework = this.unitPlannerFrameworks.find(item => item.id.toLowerCase() === this.frameworkId.toLowerCase())
            if (lessonFramework) {
              // 将框架添加到框架列表中，但并不显示此框架
              let framework = {
                frameworkId: this.frameworkId,
                frameworkName: lessonFramework.name,
                hidden: true
              }
              this.frameworks.push(framework)
            }
          }
        })
      }
      // 处理 CLR 资源数据, 初始化资源是不隐藏的，后续用户手动删除角标时会重新设置 hidden 值
      detail.steps.forEach(step => {
        // 判断 CLR 资源u你是否存在并且资源列表是否存在，如果存在才会初始化 hidden 值
        if (step.lessonClrAndSources && step.lessonClrAndSources.sources) {
          step.lessonClrAndSources.sources.forEach(source => {
            this.$set(source, 'hidden', false)
          })
        }
      })
      this.lesson = {
        id: detail.id,
        name: detail.name,
        cover: detail.coverMedias && detail.coverMedias.length > 0 && this.extractMedia((detail.coverMedias || []).slice(-1)[0]) || this.coverExternalMedia(detail),
        ages: steps.map(step => ({ name: step.ageGroupName, value: step.ageGroupValue })),
        prepareTime: detail.prepareTime,
        activityTime: detail.activityTime,
        isAdaptedLesson: detail.isAdaptedLesson,
        templateType: detail.templateType, // 模板类型
        themes: themes,
        authorName: detail.authorName, // 设置 authorName
        framework: detail.framework && { frameworkId: detail.framework.id, frameworkName: detail.framework.name },
        measures: this.buildDomainTree(detail.domains, detail.measures),
        objective: detail.objectives && detail.objectives.join('\n'),
        copySourceLessonId: detail.copySourceLessonId, // 复制的源课程 ID
        material: detail.materials &&
          {
            description: detail.materials.descriptions && detail.materials.descriptions.join('\n'),
            media: this.extractMedia(detail.materials.media),
            externalMediaUrl: detail.materials.externalMediaUrl, // 媒体路径
            externalMediaUrlId: detail.materials.externalMediaUrlId, // 媒体 Id
            attachmentMedias: (detail.materials.attachmentMedias || []).map(item => this.extractMedia(item))
          },
        steps: (detail.steps || [])
          .map(step => ({
            content: step.content,
            universalDesignForLearning: step.universalDesignForLearning, // 差异化教学
            universalDesignForLearningGroup: step.universalDesignForLearningGroup, // 差异化教学
            culturallyResponsiveInstruction: step.culturallyResponsiveInstruction, // 文化教学内容
            culturallyResponsiveInstructionGeneral: step.culturallyResponsiveInstructionGeneral, // 文化教学内容
            culturallyResponsiveInstructionGroup: step.culturallyResponsiveInstructionGroup, // 文化教学内容
            homeActivity: step.homeActivity, // 家庭活动
            media: this.extractMedia(step.media),
            ageGroupName: step.ageGroupName,
            ageGroupValue: step.ageGroupValue,
            externalMediaUrl: step.externalMediaUrl, // 媒体路径
            externalMediaUrlId: step.externalMediaUrlId, // 媒体 Id detail.materials.externalMediaUrlId
            lessonStepGuides: step.lessonStepGuides,
            teachingTips: step.teachingTips,
            lessonClrAndSources: step.lessonClrAndSources,
            lessonImpStepAndSourceStr: step.lessonImpStepAndSourceStr,
            lessonImpStepAndSource: step.lessonImpStepAndSource,
            lessonSource: step.lessonSource,
            questions: step.questions,
            mixedAgeDifferentiations: step.mixedAgeDifferentiations,
            lessonScienceOfReadingModel: step.lessonScienceOfReadingModel,
            showMixedAge: this.enableMixedAgeGroup || step.showMixedAge,
            lessonTemplate: step.lessonTemplate,
            adaptedModuleSwitch: step.adaptedModuleSwitch || {} // 轻量导入改编时当前模块是否显示
          })),
        learnerProfiles: detail.learnerProfiles || [],
        book: detail.books && detail.books[0],
        videoBook: detail.videoBooks && detail.videoBooks[0],
        attachments: (detail.attachmentMedias || []).map(item => this.extractMedia(item)),
        dlls: this.dealDllData(detail.dlls),
        status: detail.status,
        assistantAdaptInfo: detail.assistantAdaptInfo,
        activityType: detail.activityType,
        activityTheme: detail.activityTheme,
        classroomType: detail.classroomType || "IN_PERSON"
      }
      // 判断 detail 中的 objectives 长度是否是 2
      if (detail.objectives && detail.objectives.length === 2) {
        this.isSetObjectiveType = true
      }
      // 设置 adaptedModuleSwitch
      if (detail && detail.adaptedModuleSwitch) {
        this.adaptedModuleSwitch = detail.adaptedModuleSwitch
      }
    },
    // 处理 DLL 数据
    dealDllData (dlls) {
      if (!dlls || dlls.length === 0) {
        return []
      }
      return dlls.map(dll => {
        dll.media = dll.medias && dll.medias[0] && { id: dll.medias[0].mediaId, url: dll.medias[0].mediaUrl, name: dll.medias[0].fileName }
        dll.languages = (dll.languages || [])
        this.$set(dll, 'voiceLoading', false)
        dll.title = this.title
        dll.mediaModels = [{
          ...dll.media,
          mediaUrl: dll.media && dll.media.url
        }]
        dll.description = dll.description || ''
        this.$set(dll, 'contentModels', dll.languages)
        dll.contentModels.forEach(content => {
          this.$set(content, 'voiceLoading', false)
        })
        return {
          ...dll
        }
      })
    },
    // quiz 资源状态同步
    quizHasResource () {
      if (!this.lesson || !this.lesson.steps || this.lesson.steps.length === 0) {
        this.$store.dispatch('setHasQuizResource', false)
        return
      }
      if (this.lesson.steps[0].questions && this.lesson.steps[0].questions.length > 0) {
        this.$store.dispatch('setHasQuizResource', true)
      } else {
        this.$store.dispatch('setHasQuizResource', false)
      }
    },
    batchTranslateDllVocabularies () {
      // 构造需要翻译的 DLL 词汇
      const dllContentList = this.lesson.dlls.map(dll => dll.content)
      const request = {
        dllContentList: dllContentList,
        langCodes: this.langCodes
      }
      // 多个 DLL 词汇批量翻译
      this.$axios.post($api.urls().batchTranslate, request).then((response) => {
        const dlltranslateMap = response.dlltranslateMap || {}
        // Dll 翻译后的新数据
        this.lesson.dlls = this.lesson.dlls.map(dll => ({
          ...dll,
          languages: (dlltranslateMap[dll.content] || []).map(translate => ({
            ...translate,
            lang: translate.name,
            lang_en: translate.originalName
          }))
        }))
        // 处理 DLL 数据
        this.dealDllData(this.lesson.dlls)
      })
    },
    // 判断是否发生过改变
    hasUpdated () {
      // 解析 this.initLessonData，数据不存在不用更新
      if (!this.initLessonData) {
        return false
      }
      // 获取当前课程的结果
      this.fillUDLAndCLRData()
      let currentLesson = this.collectData()
      let initLesson = JSON.parse(this.initLessonData)
      // 将 currentLesson 中的 steps 中的 lessonGuides 下面的 measure 的 Description 赋值给 initLesson 中的 steps 中的 lessonGuides 下面的 measure 的 Description
      currentLesson.steps.forEach(step => {
        let initStep = initLesson.steps.find(item => item.ageGroupValue === step.ageGroupValue)
        if (initStep) {
          step.lessonStepGuides.forEach(guide => {
            let initGuide = initStep.lessonStepGuides.find(item => item.measureId === guide.measureId)
            if (initGuide) {
              initGuide.measureDescription = guide.measureDescription
            }
          })
        }
      })
      const currentLessonData = JSON.stringify(currentLesson)
      delete initLesson.unitId
      // 判断当前课程是否和 initLessonData 可成是一样的
      return currentLessonData !== JSON.stringify(initLesson)
    },
    // 封面外部视频链接
    coverExternalMedia (detail) {
      return {
        externalMediaUrl: detail.coverExternalMediaUrl,
        externalMediaUrlId: detail.coverExternalMediaUrlId
      }
    },

    innerText (html) {
      let htmlDivElement = document.createElement('div')
      htmlDivElement.innerHTML = html || ''
      return htmlDivElement.innerText.trim()
    },
    buildDomainTree (domains, measures) {
      domains = domains || []
      measures = measures || []
      return domains.map(domain => {
        return {
          id: domain.id,
          name: domain.name,
          abbreviation: domain.abbreviation,
          children: measures
            .filter(item => item.parentId === domain.id)
            .map(item => ({ id: item.id, name: item.name, abbreviation: item.abbreviation, children: [], core: item.core, description: item.description }))
        }
      })
    },
    changeFramework (value, flag) {
      this.$analytics.sendEvent('web_lesson_library_add_select_framework')
      if (!flag) {
        let confirm = this.lesson.steps.some(step => {
          // 有内容的测评点,如果有典型行为或者 quiz
          return step.lessonStepGuides.filter(item => item.typicalBehaviors).length > 0 || (step.questions && step.questions
            .some(item => (item.question && item.question.trim() !== '') || (item.answer && item.answer.trim() !== '')))
        })
        if (confirm) {
          this.$confirm(this.$t('loc.lessonChangeFramework'), this.$t('loc.cfm'), {
            confirmButtonText: this.$t('loc.confirm'),
            cancelButtonText: this.$t('loc.cancel'),
            showClose: true,
            customClass: 'confirm-dialog',
            confirmButtonClass: 'el-button--danger'
          }).then(() => {
            this.changeFramework(value, true)
          })
          this.frameworkId = this.oldFrameworkId
          return false
        }
      }
      this.oldFrameworkId = this.frameworkId
      this.oldMeasureIds = []
      this.frameworkId = value
    },
    changeAgeValues(values, ageDeleteConfirmState, canShowImportLatelyAgeGroupInfoDialog = true, leastOne) {
      this.$analytics.sendEvent('web_lesson_library_add_select_age_group');

      // 确保 values 只包含一个值
      if (values.length > 1) {
        values = [values[values.length - 1]];
      }

      // 当年龄组改变时，要更新年龄组与步骤缓存
      this.lesson.steps.forEach(step => {
        let index = this.stepsCache.findIndex(item => step.ageGroupName === item.ageGroupName)
        if (index > -1) {
          this.stepsCache.splice(index, 1, step)
        }
      })
      // 只要年龄组的值包含 1 2 3 4 5 6 7 其中任意一个就显示家庭活动
      if (values.includes('0') || values.includes('1') || values.includes('2') || values.includes('3') || values.includes('4') || values.includes('5') || values.includes('1,2') || values.includes('Grade 1') || values.includes('Grade 2')) {
        this.isShowAtHome = true
      } else {
        this.isShowAtHome = false
      }

      // 判断是否是删除动作
      let delAction = false
      // 添加的年龄组值
      let addAgeVal = null

      // 如果 values 为空，则说明删除了所有的年龄组
      if (values.length === 0) {
        delAction = true;
      } else if (this.oldAgeValues && this.oldAgeValues.length > 0 && values[0] !== (this.oldAgeValues[0] || null)) {
        // 否则是切换了年龄组
        delAction = true;
        addAgeVal = values[0];
      } else {
        // 否则是添加了年龄组
        addAgeVal = values[0];
      }

      // 如果是删除
      if (delAction) {
        // 如果只有一个年龄组，则不允许删除
        if (leastOne && values.length === 0 && this.oldAgeValues && this.oldAgeValues.length === 1) {
          this.ageValues = this.oldAgeValues; // 恢复选择状态
          return;
        }
        // 如果是删除，那么需要判断删除的年龄组是否有内容
        let deletedAge = this.oldAgeValues[0];
        // 找到删除的年龄组的 steps 对应的 index
        let index = this.lesson.steps.findIndex(item => item.ageGroupValue === deletedAge)
        // 获取删除的步骤
        let deletedStep = this.lesson.steps[index]

        // 未确认删除 并且 步骤内容不为空时
        let isEmptyContent = true;
        // 判断是否有典型行为
        if (deletedStep.content === null) {
          this.ageValues = values; // 更改选中年龄组的值为删除后状态
        } else {
          isEmptyContent = ((deletedStep.content === undefined || deletedStep.content.length <= 0) && (deletedStep.media === undefined || deletedStep.media === null || deletedStep.media.id === undefined || deletedStep.media.id === null));
        }
        let haveGuides = deletedStep.lessonStepGuides.filter(item => item.typicalBehaviors).length > 0;

        // 如果未确认删除 并且 步骤内容不为空时
        if ((!ageDeleteConfirmState && !isEmptyContent) ||
          (!ageDeleteConfirmState && haveGuides)) {
          this.ageValuesBeforeDelete = this.ageValues // 缓存删除后的年龄组值
          this.ageValues = this.oldAgeValues // 保持年龄组值的选中状态
          this.haveGuides = haveGuides
          // 用删除后的年龄组重新构建显示
          this.changeAgeValues(this.ageValuesBeforeDelete, true, canShowImportLatelyAgeGroupInfoDialog)
          return
        } else { // 确认删除了
          this.ageValues = values // 更改选中年龄组的值为删除后状态
          // 如果在选择的年龄组内找不到，则说明将该年龄组删除，则需要判断该年龄组对应的数据是否存在
          // 如果数据存在，则需要将该年龄组对应的数据保存一份，否则无需保存
          let idx = this.lesson.steps.findIndex(item => item.ageGroupValue === deletedAge)
          let val = idx === -1 ? null : this.lesson.steps[idx]
          // 判断数据是否不为空且其中的 UDL CLR 等数据不为空
          let dataExit = (val !== null && !this.checkSpecificFields(val, this.currentAgeGroupStepsPart))
          // 不为空才会更新最近删除的年龄组，否则保持原来的值
          val = dataExit ? JSON.parse(JSON.stringify(val)) : val
          this.currentRemoveAgeGroup = dataExit ? deletedAge : this.currentRemoveAgeGroup
          if (this.ageValues.indexOf(deletedAge) === -1 && dataExit) {
            // 每次保存都需要用一个新的 map ，如果在同一个 map 是修改那么会让数据失去响应性
            this.latelyRemoveAgeGroupAndStepMap = new Map([[this.currentRemoveAgeGroup, val]])
          }
        }
      }

      values = values || []
      let ageGroup = this.ageGroup || [] // 当前用户的全部年龄组
      let ages = ageGroup.filter(item => values.includes(item.value))
      this.lesson.ages = ages // 更新后的用户的全部年龄组
      // age group 发生了变化，这个时候不在进行 mounted 的事件发送
      this.changeAgeGroups = true
      let temp = JSON.parse(JSON.stringify(this.lesson.steps))

      // 取缓存时缓存不存在 则添加默认值，存在直接取出缓存值
      this.lesson.steps = ages.map(age => {
        let value = this.stepsCache.find(item => item.ageGroupName === age.name)
        // 如果缓存不存在或者缓存存在但是如 UDL CLR 等这些数据为空
        if (!value || this.checkSpecificFields(value, this.currentAgeGroupStepsPart)) {
          // 添加默认值
          value = {
            ageGroupName: age.name,
            ageGroupValue: age.value
          }
          // 如果最近删除的年龄组的课程信息不为空
          if (addAgeVal && addAgeVal === age.value && this.latelyRemoveAgeGroupAndStepMap && this.latelyRemoveAgeGroupAndStepMap.size > 0) {
            this.currentAddAgeValue = age.value
            // 如果最近删除的年龄组和当前添加的年龄组相同
            if (this.currentRemoveAgeGroup === age.value) {
              // 导入最近删除的年龄组的课程信息
              this.importLatelyAgeGroupInfo()
            } else {
              // 关闭下拉框
              this.$refs.ageGroupSelect.blur()
              // 判断移除的年龄组是否包含数据
              if (this.hasOldData() && canShowImportLatelyAgeGroupInfoDialog) {
                // 开启弹窗
                this.showImportLatelyAgeGroupInfo = true
              }
            }
          }
          this.stepsCache.push(value)
          this.initStepGuide(value)
          this.initTeachingTips(value)
        }
        return value
      })

      this.lesson.templateType = (this.lesson.steps[0] && this.lesson.steps[0].lessonTemplate && this.lesson.steps[0].lessonTemplate.templateType) || null
    },
    // 是否存在旧数据
    hasOldData() {
      // 获取最近删除的年龄组的课程信息
      let removedAgeGroupData = this.latelyRemoveAgeGroupAndStepMap.get(this.currentRemoveAgeGroup)
      // 为了防止修改原有的属性的值，这里需要深拷贝一份数据
      removedAgeGroupData = JSON.parse(JSON.stringify(removedAgeGroupData))
      // 判断之前先移除部分字段
      const needClearProp = ['ageGroupName', 'ageGroupValue']
      // 讲这些字段设置为空
      if (Array.isArray(needClearProp) && removedAgeGroupData) {
        needClearProp.forEach(prop => {
          if (removedAgeGroupData.hasOwnProperty(prop)) {
            removedAgeGroupData[prop] = null;
          }
        });
      }
      // 需要特殊处理的属性，将 key 对应的属性的子属性设置为空
      const specialFields = new Map([
        ['questions', ['level', 'type', 'sortIndex']]
      ])
      // 假设 specialFields 是一个 Map 对象
      if (specialFields && typeof specialFields.forEach === 'function') {
        specialFields.forEach((value, key) => {
          // 检查 value 是否是数组且不为空
          if (Array.isArray(value) && value.length > 0) {
            // 检查 removedAgeGroupData 是否有 key 对应的属性，并且该属性是数组且不为空
            if (removedAgeGroupData && Array.isArray(removedAgeGroupData[key]) && removedAgeGroupData[key].length > 0) {
              value.forEach(item => {
                removedAgeGroupData[key].forEach(question => {
                  // 检查 question 对象是否有 item 对应的属性
                  if (question && typeof question === 'object' && item in question) {
                    question[item] = null;
                  }
                });
              });
            }
          }
        });
      }
      // 判断一个对象是不是空的
      function isObjectEmpty(obj) {
        // 如果是基本类型，直接判断是否为空
        if (typeof obj !== 'object' || obj === null) {
          return obj === undefined || obj === null || obj === '';
        }

        // 如果是数组
        if (Array.isArray(obj)) {
          if (obj.length === 0) {
            return true;
          }
          // 如果是数组，其中有且仅有一个元素的时候，这个元素必须存在一个属性是有数据的
          if (obj.length === 1) {
            const singleElement = obj[0];
            if (typeof singleElement === 'object' && singleElement !== null) {
              for (let key in singleElement) {
                if (singleElement.hasOwnProperty(key) && !isObjectEmpty(singleElement[key])) {
                  return false;
                }
              }
              return true;
            }
            return isObjectEmpty(singleElement);
          }
          // 数组长度大于 1，只要有一个元素不为空，数组就不为空
          for (let i = 0; i < obj.length; i++) {
            if (!isObjectEmpty(obj[i])) {
              return false;
            }
          }
          return true;
        }

        // 遍历对象的所有属性
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            // 递归检查属性值
            if (!isObjectEmpty(obj[key])) {
              return false;
            }
          }
        }

        return true;
      }

      // 判断是否存在数据
      return removedAgeGroupData && !isObjectEmpty(removedAgeGroupData)
    },
    // 检查对象中指定的字段是否为空
    checkSpecificFields (obj, fields) {
      for (let i = 0; i < fields.length; i++) {
        let field = fields[i]
        if (obj.hasOwnProperty(field)) {
          const value = obj[field]
          // 判断值是否为空
          if (!this.fieldIsEmpty(value)) {
            // 如果是 lessonStepGuides 对象需要特殊判断
            if (field === 'lessonStepGuides') {
              for (let i = 0; i < value.length; i++) {
                if (value[i].hasOwnProperty('typicalBehaviors') && !this.fieldIsEmpty(value['typicalBehaviors'])) {
                  return false
                }
              }
            } else {
              return false
            }
          }
        }
      }
      return true
    },
    // 判断属性是否为空
    fieldIsEmpty (value) {
      return value === null || value === undefined || value === '' ||
        (typeof value === 'string' && value.trim() === '') ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
    },
    // 更新课程 SOR 的数据
    updateLessonScienceOfReadingModel (lessonScienceOfReadingModel, lessonAgeGroup) {
      let idx = this.getByAgeGroupName(lessonAgeGroup)
      if (idx !== -1) {
        this.$set(this.lesson.steps[idx], 'lessonScienceOfReadingModel', lessonScienceOfReadingModel)
      }
    },
    // 更改 generalUniversalDesignForLearning 的回调,设置 UDL
    updateUniversalDesignForLearning (generalUniversalDesignForLearning, lessonAgeGroup, mixedAgeGroup) {
      let idx = this.getByAgeGroupName(lessonAgeGroup)
      if (idx !== -1) {
        if (!this.lesson.steps[idx].universalDesignForLearning) {
          // 当该属性不存在时使用 this.$set 为对象添加属性和属性值，能让 vue 正确检测到属性值变化并更新值
          this.$set(this.lesson.steps[idx], 'universalDesignForLearning', generalUniversalDesignForLearning)
        } else {
          this.lesson.steps[idx].universalDesignForLearning = generalUniversalDesignForLearning
        }
        this.$set(this.lesson.steps[idx], 'mixedAgeDifferentiations', mixedAgeGroup)
      }
    },
    // 更改 universalDesignForLearningClassSpecial 的回调,设置 UDL
    updateUniversalDesignForLearningClassSpecial (universalDesignForLearningClassSpecial, lessonAgeGroup, mixedAgeGroup) {
      let idx = this.getByAgeGroupName(lessonAgeGroup)
      if (idx !== -1) {
        if (!this.lesson.steps[idx].universalDesignForLearningGroup) {
          // 当该属性不存在时使用 this.$set 为对象添加属性和属性值，能让 vue 正确检测到属性值变化并更新值
          this.$set(this.lesson.steps[idx], 'universalDesignForLearningGroup', universalDesignForLearningClassSpecial)
        } else {
          this.lesson.steps[idx].universalDesignForLearningGroup = universalDesignForLearningClassSpecial
        }
        this.$set(this.lesson.steps[idx], 'mixedAgeDifferentiations', mixedAgeGroup)
      }
    },
    // 根据 ageGroupName 查找对应的 step 的下标
    getByAgeGroupName (lessonAgeGroup) {
      for (let i = 0; i < this.lesson.steps.length; i++) {
        if (this.lesson.steps[i].ageGroupName === lessonAgeGroup) {
          return i
        }
      }
      return -1
    },
    ageConfirmDialog () {
      // 确认修改年龄组
      this.showAgeConfirm = false
      // 用删除后的年龄组重新构建显示
      this.changeAgeValues(this.ageValuesBeforeDelete, true)
    },
    // 导入最近删除年龄组课程信息的确认按钮触发方法
    importLatelyAgeGroupInfoConfirmDialog () {
      // 确认导入最近删除年龄组课程信息
      this.showImportLatelyAgeGroupInfo = false
      this.importLatelyAgeGroupInfo()
    },
    // 导入最近删除年龄组课程信息
    importLatelyAgeGroupInfo () {
      // 如果有能够导入的数据
      if (this.currentAddAgeValue) {
        // 查找当前 lesson 中的对应的该 step
        let idx = this.lesson.steps.findIndex(item => item.ageGroupValue === this.currentAddAgeValue)
        // 判断是否有 universalDesignForLearningGroup 有则保存
        let hasUniversalDesignForLearningGroup = false
        let universalDesignForLearningGroup = null
        // 如果存在
        if (idx !== -1) {
          // 将最近删除年龄组课程信息放入该 step
          let ageName = this.lesson.steps[idx].ageGroupName
          let ageValue = this.lesson.steps[idx].ageGroupValue
          this.lesson.steps[idx] = JSON.parse(JSON.stringify(this.latelyRemoveAgeGroupAndStepMap.get(this.currentRemoveAgeGroup)))
          this.lesson.steps[idx].ageGroupName = ageName
          this.lesson.steps[idx].ageGroupValue = ageValue
          if (this.lesson.steps[idx].universalDesignForLearningGroup && this.lesson.steps[idx].universalDesignForLearningGroup.length > 0) {
            hasUniversalDesignForLearningGroup = true
            universalDesignForLearningGroup = this.lesson.steps[idx].universalDesignForLearningGroup
          }
          // 如果新的年龄组是 k-12 则需要判断 quiz 是否存在，如果 quiz 为空，初始化一个 question
          if (this.k12Grade(ageName)) {
            if (this.lesson.steps[idx].lessonStepGuides && this.lesson.steps[idx].lessonStepGuides.length !== 0) {
              this.lesson.steps[idx].lessonStepGuides = []
            }
            if (!this.lesson.steps[idx].questions || this.lesson.steps[idx].questions.length === 0) {
              // 如果导入的年龄组下的课程信息中的 quiz 为空，那么放入一个默认的 quiz 问题卡片，测评点为当前框架下的第一个测评点
              const defaultMeasure = this.domains ? this.domains.flatMap(domain => domain.children)[0] : null
              this.$set(this.lesson.steps[idx], 'questions', [{
                type: 'Multiple Choice',
                sortIndex: 1,
                question: '',
                answer: '',
                level: this.getQuizBloomSetting(),
                measureName: defaultMeasure ? defaultMeasure.name : '',
                measureId: defaultMeasure ? defaultMeasure.id : '',
                measureAbbreviation: defaultMeasure ? defaultMeasure.abbreviation : ''
              }])
            }
          } else {
            // 如果是没有 quiz 的年龄组且之前年龄组存在 quiz 内容，那么将之前年龄组的 quiz 内容清空
            if (this.lesson.steps[idx].questions && this.lesson.steps[idx].questions.length !== 0) {
              this.lesson.steps[idx].questions = []
            }
          }
          // Thin Slide 模版只有3年级及以上才能使用，如果年龄组不是3年级及以上则清空模版信息
          let grade3To12 = ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
          if (this.lesson.steps[idx].lessonTemplate && this.lesson.steps[idx].lessonTemplate.templateType == 'THIN_SLIDE' && !tools.arrayContainsIgnoreCase(grade3To12, this.lesson.steps[idx].ageGroupName)) {
            this.lesson.steps[idx].lessonTemplate = null
            this.lesson.templateType = null
          }
          this.lesson.templateType = (this.lesson.steps[0] && this.lesson.steps[0].lessonTemplate && this.lesson.steps[0].lessonTemplate.templateType) || null
        }
        let currentAddAgeGroup = this.ageGroup.find(item => item.value === this.currentAddAgeValue)
        // 重新渲染 udl 数据
        if (this.$refs.universalDesignLearning) {
          let arr = this.$refs.universalDesignLearning
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].lessonAgeGroup === currentAddAgeGroup.name) {
              if (hasUniversalDesignForLearningGroup) {
                this.$set(arr[i], 'universalDesignForLearningClassSpecial', universalDesignForLearningGroup)
              }
              arr[i].validatedUniversalDesignForLearningClassSpecial()
              break
            }
          }
        }
        if (this.$refs.culturallyLinguisticallyResponsive) {
          let arr = this.$refs.culturallyLinguisticallyResponsive
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].lessonAgeGroup === currentAddAgeGroup.name) {
              arr[i].showSource = true
              break
            }
          }
        }
        // 重新渲染实施步骤资源数据
        if (this.$refs.lessonStepInput && this.lesson.steps[idx]) {
          let arr = this.$refs.lessonStepInput
          let lessonStep = this.lesson.steps[idx] // 提取步骤缓存中的值

          // 判断是否存在 lessonImpStepAndSource 和 sources
          if (lessonStep.lessonImpStepAndSource && lessonStep.lessonImpStepAndSource.sources && lessonStep.lessonImpStepAndSource.sources.length > 0) {
            for (let i = 0; i < arr.length; i++) {
              // 显式检查 arr[i] 和 arr[i].$refs 是否存在
              if (arr[i] && arr[i].$refs && arr[i].$refs.implementationStepSources) {
                arr[i].$refs.implementationStepSources.showSource = true
              }
            }
          }
        }

        // 重新计算 quiz 的 bloom 设置
        if (this.$refs.lessonQuiz) {
          let arr = this.$refs.lessonQuiz
          for (let i = 0; i < arr.length; i++) {
            arr[i].setStepQuizBloomSetting(this.lesson.steps[i].questions)
          }
        }
        // 如果导入年龄组不是当前添加年龄组且没有缓存，则无需展示该成功信息
        let val = this.stepsCache.findIndex(item => item.ageGroupValue === this.currentAddAgeValue)
        if (val && this.currentRemoveAgeGroup !== this.currentAddAgeValue) {
          // 成功的提示信息
          this.$message.success(this.$t('loc.importAgeGroupInfoNotification'))
        }
        this.currentAddAgeValue = null
      }
    },
    closeAgeConfirmDialog () {
      this.showAgeConfirm = false
    },
    closeImportLatelyAgeGroupInfoDialog () {
      this.showImportLatelyAgeGroupInfo = false
    },
    lessonMaterialDescriptionFormat (nodes) {
      if (nodes) {
        let deleteNode = []
        let listDomes = nodes.querySelectorAll('.textarea-item')
        listDomes.forEach(item => {
          item.setAttribute('contenteditable', 'false')
          // 移除加载中的 card
          if (item.className.indexOf('lesson-material-card') > -1 && item.getAttribute('isLoading') == 'true') {
            deleteNode.push(item.parentElement)
          }
        })
        deleteNode.forEach(item => {
          item.parentNode.replaceChild(document.createTextNode('\r\n'), item)
          item.remove()
        })
      }
      return nodes
    },
    lessonEditOpenUrlWithWebAndIPad () {
      if (tools.isComeFromIPad()) {
        $('a.textarea-item-a, a.lesson-material-card-link').on('click', function ($this) {
          AppUtil.appOpenUrlByBrowser($(this).attr('href'))
          return false
        })

        $('.lesson-material-attachment-item').children('a[href]').on('click', function ($this) {
          AppUtil.appOpenUrlByBrowser($(this).attr('href'))
          return false
        })
      }
    },

    /**
     * 全屏开关
     */
    aiFullScreen () {
      this.aiFullScreenFlag = !this.aiFullScreenFlag
    },

    /**
     * 切换助手展示样式
     */
    switchAiToolShow () {
      this.aiToolEntryShow = !this.aiToolEntryShow

      // 如果是从 AssistantTool 组件切换过来的（即开始生成过程）
      // 则隐藏整个 AssistantTool 组件的内容区域
      if (this.convertLessonLoading && this.showAssistantTool) {
        this.closeEditPromptPopover()
        // 不改变 showAssistantTool 的值，只是通过条件渲染隐藏内容
        // 强制更新视图
        this.$forceUpdate()
      }
    },

    /**
     * 反馈弹窗关闭前操作
     */
    rateDialogClose () {
    },

    /**
     * 更新课程转换loading
     */
    updateLessonCoverLoading (newValue) {
      this.lessonCoverLoading = newValue !== null && newValue !== undefined ? newValue : false
    },

    /**
     * 更新课程生成 loading 的值
     */
    updateConvertLessonLoading (newValue) {
      this.convertLessonLoading = newValue !== null && newValue !== undefined ? newValue : false
    },

    /**
     * 更新添加步骤的步骤
     */
    updateAddLessonStep (newValue) {
      this.addLessonStep = newValue
    },

    /**
     * 更新数据是否有变更
     */
    updateLessonInfoHasChanged (newValue) {
      this.lessonInfoHasChanged = newValue !== null && newValue !== undefined ? newValue : false
    },
    // 课程生成完毕的回调通知
    generaterdLessoncomplete (param) {
      if (param) {
        this.setFeedbackStyle()
      }
      this.generaterdLessoncompleted = param

      // 如果课程已经生成完成，则自动发布
      if (param) {
        // 批量编辑页面, 不需要自动保存
        if (this.batchEdit) {
          return
        }
        if (this.publishAfterGenerate) {
          if (this.publishAfterGenerateSendEvent) {
            // 发布后记录埋点
            this.$analytics.sendEvent('cg_lesson_created_successfully')
            this.publishAfterGenerateSendEvent = false
          }
          this.publishLesson()
        } else {
          this.saveDraft()
        }
      } else {
      }
    },
    // 课程生成成功之后的 recordId
    getPromptUsageRecordIds (recordId) {
      // 如果传递过来的 recordId 是存在的，则将其添加到 promptUsageRecordIds 中
      if (recordId) {
        let tempPromptUsageRecordIds = []
        // 将 recordId 放在数组的第一个位置
        tempPromptUsageRecordIds.push(...recordId)
        this.promptUsageRecordIds = tempPromptUsageRecordIds
      }
    },
      updateCulturallyLinguisticallyResponsiveContent (index, content) {
        this.lesson.steps[index].culturallyResponsiveInstruction = content
      },
    /**
     * 清理差异化教学班级特殊数据
     * @param ageGroupName 年龄组名称
     */
    clearUniversalDesignForLearningClassSpecial (ageGroupName) {
      this.lesson.steps.forEach(step => {
        if (step.ageGroupName === ageGroupName) {
          this.$set(step, 'universalDesignForLearningGroup', '')
        }
      })
    },
    /**
     * 清理文化教学内容班级特殊数据
     * @param ageGroupName
     */
    clearCulturallyResponsiveInstructionGroup (ageGroupName) {
      this.lesson.steps.forEach(step => {
        if (step.ageGroupName === ageGroupName) {
          this.$set(step, 'culturallyResponsiveInstructionGroup', '')
        }
      })
    },
      /**
       * 准备开始生成 UDL 和 CLR 数据
       */
    updateGenerateUniversalDesignAndCLRData (ageGroupName) {
          // 如果传递了 ageGroupName，那么就将 currentAgeGroupName 设置为 ageGroupName
          if (ageGroupName) {
              this.currentAgeGroupName = ageGroupName
          }
        this.$refs.personalizePlan.showPersonalizePlan()
    },
      updateLessonSource (lessonSources, ageGroupName) {
          this.lesson.steps.forEach(step => {
              // 判断当前步骤的年龄组与发生改变的年龄组是否相等，如果一致才会更新 CLR 资源数据
              if (equalsIgnoreCase(step.ageGroupName, ageGroupName)) {
                step.lessonClrAndSources = lessonSources
                this.$set(step, 'lessonClrAndSources', lessonSources)
                // 为 step 设置一个标志位，表示进行了班级 CLR 生成
                this.$set(step, 'classCLRGenerated', true)
                this.$forceUpdate()
              }
          })
      },
      // 更新实施步骤资源
      updateLessonImpStepAndSource (lessonImpStepAndSource, ageGroupName) {
        this.lesson.steps.forEach(step => {
          // 判断当前步骤的年龄组与发生改变的年龄组是否相等，如果一致才会更新 CLR 资源数据
          if (!equalsIgnoreCase(step.ageGroupName, ageGroupName)) {
            return
          }
          this.$set(step, 'lessonImpStepAndSource', lessonImpStepAndSource)
          this.$set(step, 'content', lessonImpStepAndSource.impStepContent)
          this.$forceUpdate()
        })
      },

      getNewLessonInfo (ageGroupName) {
          // 调用 UDL 的保存方法
          this.fillUDLAndCLRData()
          // 返回新的 lesson 信息
          const lessonInfo = this.collectData()
          // 定义当前年龄组下面的 step
          let currentLessonStep = lessonInfo.steps.find(step => step.ageGroupName === ageGroupName)
          // 如果当前年龄组下面的 step 不存在，那么就返回空
          currentLessonStep = currentLessonStep || {}
          // 对 currentLessonStep 进行解包
          currentLessonStep = {
              ...currentLessonStep,
              universalDesignForLearningGroup: '',
              culturallyResponsiveInstructionGroup: '',
              homeActivity: ''
          }
          // 定义 dlls
          let dlls = lessonInfo.dlls || []
          // 仅仅传递需要的参数
          dlls = dlls.map(dll => {
              return {
                  content: dll.content
              }
          })
          // 获取当前课程使用的测评点缩写
          let measureAbbrs = this.lesson.measures.reduce((prev, domain) => {
              // 获取得到所有的领域
              let domains = [domain]
              // 定义领域下面的实际测评点
              let measures = []
              // 如果领域是存在的
              while (domains.length) {
                  // 获取第一个领域
                  let temp = domains.pop()
                  // 测评点无孩子节点
                  if (!temp.children || !temp.children.length) {
                      // 此时测评点是合理的
                      measures.push(temp)
                      continue
                  }
                  // 有孩子节点的为领域
                  domains.push(...temp.children)
              }
              return prev.concat(measures.map(m => m.abbreviation))
          }, [])
          // 封装 newLessonInfo 的数据
          return {
              name: lessonInfo.name,
              objectives: lessonInfo.objectives,
              materials: lessonInfo.materials,
              dlls: dlls,
              frameworkId: lessonInfo.frameworkId,
              step: currentLessonStep,
              activityTime: this.lesson.activityTime,
              prepareTime: this.lesson.prepareTime,
              measureAbbrs: measureAbbrs,
              themes: this.lesson.themes,
              curAgeGroupName: ageGroupName,
              classroomType: lessonInfo.classroomType
          }
      },
      // 移动到对应的 UDL 或者 CLR 中
      scrollIntoUDLOrCLR () {
          // 设置滑动标记，如果为 false，那就是没有滑动过
          let scrollIntoViewFlag = false
          // 将滚轮滚动到 universalDesignLearning 这个地方
          if (this.$refs.universalDesignLearning && this.$refs.universalDesignLearning.length > 0) {
              this.$nextTick(() => {
                this.$refs.universalDesignLearning[0].$el.scrollIntoView({
                  behavior: 'smooth'
                })
                scrollIntoViewFlag = true
              })
          }
          // 如果没有滑动过就将滚轮滚动到 culturallyLinguisticallyResponsive 这个地方
          if (this.$refs.culturallyLinguisticallyResponsive && this.$refs.culturallyLinguisticallyResponsive.length > 0) {
             this.$nextTick(() => {
               if (!scrollIntoViewFlag) {
                 this.$refs.culturallyLinguisticallyResponsive[0].$el.scrollIntoView({
                   behavior: 'smooth'
                 })
               }
             })
          }
      },
      // 更新 UDL 和 CLR 的状态
      updateUDLAndCLRGenerateStatus () {
          // 获取当前正在加载的 ageGroup
          let currentAgeGroupName = this.currentAgeGroupName || ''
          // 如果 lesson 存在并且 lesson.steps 存在并且 lesson.steps.length 大于 0
          if (this.lesson && this.lesson.steps && this.lesson.steps.length > 0) {
              // 如果 currentAgeGroupName 为空
              if (currentAgeGroupName === null || currentAgeGroupName === undefined || currentAgeGroupName === '') {
                  // 那么就将 currentAgeGroupName 设置为 lesson.steps[0].ageGroupName
                  currentAgeGroupName = this.lesson.steps[0].ageGroupName
              }
          }
          // 修改对应的 generate 状态
          this.$refs.universalDesignLearning.forEach(item => {
              if (currentAgeGroupName === item.lessonAgeGroup) {
                  // UDL 使用 group team 进行数据的请求
                  item.generatedUDL = true
              }
          })
          // 修改对应的 generate 状态
          this.$refs.culturallyLinguisticallyResponsive.forEach(item => {
              if (currentAgeGroupName === item.ageGroupName) {
                  // CLR 使用 group team 进行数据的请求
                  item.generatedCLR = true
              }
          })
      },

    // 打开重新生成课程详情
    async openRedesignLessonDialog () {
      // 重新生成按钮点击埋点
      if (this.$route.name === 'AddLesson') {
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_enhance')
      } else if (this.$route.name === 'EditLesson') {
        this.$analytics.sendEvent('cg_lesson_plan_edit_click_enhance')
      }
      // 隐藏引导
      this.hideEnhanceGuide()

      // 如果是在课程库使用重新生成需要校验表单必填项
        const result = await this.checkFromValidate()
        if (!result) {
          return
        }
      // }
      this.regenerateLessonVisible = true
    },

    // 关闭重新生成课程详情
    closeRedesignLessonDialog () {
      this.regenerateLessonVisible = false
    },

    // 校验表单必填项
    async checkFromValidate () {
      // 表单校验，失败返回
      try {
        // 校验主表单
        let isMainFormValid = true;
        try {
          await this.$refs['form'].validate();
        } catch (mainFormErr) {
          isMainFormValid = false;
        }
        // 校验 SOR 表单
        const scienceOfReading = this.$refs.scienceOfReading;
        let isSorFormValid = true;
        if (scienceOfReading && scienceOfReading.length > 0) {
          for (const sor of scienceOfReading) {
            try {
              await sor.$refs['sorForm'].validate();
            } catch (sorErr) {
              isSorFormValid = false;
              // 标记 SOR 表单错误
              sor.$refs['sorForm'].$el.classList.add('is-error');
            }
          }
        }

        // 如果主表单或 SOR 表单校验失败
        if (!isMainFormValid || !isSorFormValid) {
          setTimeout(() => {
            let formItemsWithError = this.$el.querySelectorAll('.is-error');
            // 过滤出可见的 DOM
            formItemsWithError = tools.getVisibleDom(formItemsWithError);
            let firstItem = formItemsWithError[0];
            if (firstItem) {
              firstItem.dispatchEvent(new CustomEvent('validate-failed', {}));
              firstItem.scrollIntoView({ block: 'center' });
            }
          }, 0);
          return false
        }
      } catch (err) {
      }
      return true
    },

    // 重新生成课程，不依赖于 AI 助手数据
    async generalConfirmRedesignLesson (redesignData) {
      this.closeEditPromptPopover()
      if (!this.batchEdit) {
        // 非批量编辑页面执行自动保存
        await this.saveLesson()
      }
      // 根据课程信息拼接 redesignIdea 课程数据
      let redesignLesson = JSON.parse(JSON.stringify(this.lesson))
      redesignLesson = lessonUtils.redesignLesson(redesignLesson)
      // 旧的课程数据
      let copyLesson = JSON.parse(JSON.stringify(this.lesson))
      // 调用重新生成方法
      let assistantTool = this.getCurrentAssistantTool()
      this.isEnhanceLesson = true
      await assistantTool.generalConvertLessonStream(redesignData, lessonUtils.redesignLessonToInfo(redesignLesson), copyLesson, this.isAdaptedLesson)
      this.isEnhanceLesson = false
    },

    /**
     * 获取当前的 assistantTool
     */
    getCurrentAssistantTool () {
      let assistantTool = this.$refs.assistantTool
      if (this.$refs.assistantToolEditPrompt) {
        assistantTool = this.$refs.assistantToolEditPrompt
      }
      return assistantTool
    },
    // 确认并重新生成课程详情
    async confirmRedesignLesson (data) {
      // 关闭弹窗
      this.regenerateLessonVisible = false
      // 开始生成课程的 Loading
      this.generaterdLessoncompleted = false
      // 重新生成课程
      await this.generalConfirmRedesignLesson(data)
      // 结束生成课程的 Loading
      // this.generaterdLessoncompleted = true
    },

    // 同步生成 UDL 和 CLR 改编数据
    async syncGenerateUniversalDesignAndCLRData (callbacks, copyLesson) {
      try {
        // 生成 UDL 和 CLR 的数据时，切换模版按钮不可用
        // this.convertLessonLoading = true
        this.scrollIntoUDLOrCLR()
        // 获取当前年龄组名称
        const currentAgeGroupName = this.getCurrentAgeGroupName()
        // 并行处理 UDL 和 CLR 数据生成
        await Promise.all([
          this.generateUDLData(currentAgeGroupName, copyLesson),
          this.generateCLRData(currentAgeGroupName)
        ])
        // 处理完成后调用成功回调
        if (callbacks && callbacks.onSuccess) {
          callbacks.onSuccess()
        }
      } catch (error) {
        // 处理失败后调用错误回调
        if (callbacks && callbacks.onError) {
          callbacks.onError(error)
        }
      } finally {
        // 确保加载状态正确重置
        // this.convertLessonLoading = false
      }
    },

    // 提取获取年龄组名称的逻辑
    getCurrentAgeGroupName () {
      let currentAgeGroupName = this.currentAgeGroupName || ''
      // 如果 currentAgeGroupName 为空，使用第一个 step 的 ageGroupName
      if (!currentAgeGroupName && this.lesson && this.lesson.steps && this.lesson.steps.length > 0) {
        currentAgeGroupName = this.lesson.steps[0].ageGroupName
      }
      return currentAgeGroupName
    },

    // 生成 UDL 数据
    async generateUDLData (currentAgeGroupName, copyLesson) {
      const udlRefs = this.$refs.universalDesignLearning || []
      const udlPromises = []

      for (const item of udlRefs) {
        if (currentAgeGroupName === item.lessonAgeGroup) {
          // UDL 使用 group team 进行数据的请求
          udlPromises.push(item.syncGenerateUniversalDesign(() => {
            // 如果当前是改编课程，当生成 UDL 内容失败时进行回填操作
            if (item.lessonStepIndex !== undefined && this.isAdaptedLesson) {
              const step = this.lesson.steps[item.lessonStepIndex]
              if (step) {
                step.universalDesignForLearning = copyLesson.steps[item.lessonStepIndex].universalDesignForLearning
                step.showMixedAge = copyLesson.steps[item.lessonStepIndex].showMixedAge
                step.universalDesignForLearningGroup = copyLesson.steps[item.lessonStepIndex].universalDesignForLearningGroup
              }
            }
          }))
        }
      }
      // 并行执行所有 UDL 请求
      await Promise.all(udlPromises)
    },

    // 生成 CLR 数据
    async generateCLRData (currentAgeGroupName) {
      const clrRefs = this.$refs.culturallyLinguisticallyResponsive || []
      const clrPromises = []

      for (const item of clrRefs) {
        if (currentAgeGroupName === item.ageGroupName) {
          // CLR 使用 group team 进行数据的请求
          const result = item.generateCLR()
          // 如果 generateCLR 返回 Promise，则添加到数组中
          if (result && typeof result.then === 'function') {
            clrPromises.push(result)
          }
        }
      }

      // 如果有异步的 CLR 操作，等待其完成
      if (clrPromises.length > 0) {
        await Promise.all(clrPromises)
      }
    },

      // 生成 UDL 和 CLR 的数据
      // fromPersonalizePlan: 是否来自于个性化计划
      generateUniversalDesignAndCLRData () {
          //   判断是否开启了对应的功能
          if (!this.adaptUDLAndCLROpen) {
              return
          }
          // 生成 UDL 和 CLR 的数据时，切换模版按钮不可用
          this.convertLessonLoading = true
          // 需要保存改编信息
          this.needRecordAdaptInfo = true
          // 如果是编辑课程，则不需要锚点定位到 UDL 和 CLR
          if (!this.singleEditLesson) {
            this.scrollIntoUDLOrCLR()
          }
          // 如果没有分组并且 shouldGenerateUniversalDesignAndCLRData 为 false，那么就不需要生成 UDL 和 CLR 的数据
          // 生成之前将 showRecommendCheckbox 置为 false
          this.showRecommendCheckbox = false
          // 通知父组件已经有数据了
          this.$bus.$emit('lessonLibraryEditorIndexMounted', true)
          let currentAgeGroupName = this.currentAgeGroupName || ''
          if (this.lesson && this.lesson.steps && this.lesson.steps.length > 0) {
              // 如果 currentAgeGroupName 为空
              if (currentAgeGroupName === null || currentAgeGroupName === undefined || currentAgeGroupName === '') {
                  // 那么就将 currentAgeGroupName 设置为 lesson.steps[0].ageGroupName
                  currentAgeGroupName = this.lesson.steps[0].ageGroupName
              }
          }
          // 这里负责请求 UDL 数据
          this.$refs.universalDesignLearning.forEach(item => {
              if (currentAgeGroupName === item.lessonAgeGroup) {
                  // UDL 使用 group team 进行数据的请求
                  item.generateUniversalDesign(false, true)
              }
          })
          // 这里负责请求 CLR 数据
          this.$refs.culturallyLinguisticallyResponsive.forEach(item => {
              if (currentAgeGroupName === item.ageGroupName) {
                  // CLR 使用 group team 进行数据的请求
                  item.generateCLR(false, true)
              }
          })
      },
    // 异步调用到实施步骤资源组件
    generateImplementationStepsSource (implementationStepContent, lessonId) {
      this.$refs.implementationStepSources.generateResourcesByImpStep(implementationStepContent, lessonId)
    },

    inCurriculumGeniePage () {
      return this.$route.path.includes('curriculum-genie')
    },
    // 根据组件 ref 把页面滚动到该 ref 顶部
    scrollToActive(activeRef) {
      this.universalDesignBeginGenerate = true;
      this.$nextTick(() => {
        const refElement = this.$refs[activeRef];
        const targetElement = this.getTargetElement(refElement);
        // 如果该 ref 存在且未滚动过，则执行滚动
        if (targetElement && !this.scrolledRef.includes(activeRef)) {
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
          this.scrolledRef.push(activeRef);
        }
      })
    },
    // 判断获取到的 ref 类型
    getTargetElement(refElement) {
      if (Array.isArray(refElement) && refElement.length > 0) {
        return refElement[0].$el;
      }
      if (refElement && refElement.$el) {
        return refElement.$el;
      }
      return null;
    },
    setGenerateUniversalState (val) {
      this.universalDesignBeginGenerate = false
    },
    /**
     * 检查是否打开欢迎弹窗
     */
    checkWelcomeDialog () {
      // 检查页面路由是否是新增 lesson
      const path = this.$route.path
      if (path === '/lessons/lesson-library/add-lesson' && this.currentUser.needLessonWelcomeGuide) {
        this.showWelcomeDialog = true
      }
    },
    /**
     * 关闭欢迎对话框
     */
    closeWelcomeDialog() {
      this.showWelcomeDialog = false
      this.currentUser.needLessonWelcomeGuide = false
      this.$store.dispatch('setCurrentUserAction', this.currentUser)
      // 调用关闭欢迎引导的接口
      this.$axios.post($api.urls().closeLessonPlanningWelcomeGuide)
    },
    openHistory () {
      // 历史版本点击埋点
      if (this.$route.name === 'AddLesson') {
        this.$analytics.sendEvent('cg_lesson_plan_cre_click_version')
      } else if (this.$route.name === 'EditLesson') {
        this.$analytics.sendEvent('cg_lesson_plan_edit_click_version')
      }

      // 检查是否有未保存的修改
      if (this.changesCounter) {
        // 执行保存发布
        this.publishLesson().then(() => {
          // 保存完成后再打开历史抽屉
          this.openHistoryDrawer()
        }).catch(() => {
          // 如果保存失败，仍然打开历史抽屉
          this.$message.error('Unable to open lesson plan history.')
        })
      } else if (this.saving !== 0) {
        // 正在保存中，显示提示并等待保存完成后再打开历史抽屉
        this.$message.warning(this.$t('loc.saving'))
      } else if (this.saving == 0) {
        this.openHistoryDrawer()
      } else {
        this.$message.info('Unknown error, unable to open history list')
      }
    },

    // 打开历史抽屉的实际方法
    openHistoryDrawer() {
      if (!this.lesson.id) {
        this.$message.error('Unable to view history version')
        return
      }
      this.closeEditPromptPopover()
      // 历史版本曝光埋点
      if (this.$route.name === 'AddLesson') {
        this.$analytics.sendEvent('cg_lesson_plan_cre_version_exposure')
      } else if (this.$route.name === 'EditLesson') {
        this.$analytics.sendEvent('cg_lesson_plan_edit_version_exposure')
      }
      this.showHistoryDrawer = true
    },
    async handleRestoreHistory(detail, sourceVersionId) {
      try {
        // 解决测评点组件弹开的问题
        this.lessonMeasureSelectNotShow = true
        this.showLessonForm = false
        // 处理恢复历史版本的逻辑
        this.clearLesson()
        this.skipFrameworkWatch = true
        this.handleLessonDetail(detail)
        // 获取扩展框架信息
        await this.loadExtendFramework()
        this.lesson.framework && this.loadMeasures(this.lesson.framework)
        // 向 teachingTips 补充测评点描述
        this.addDescToTeachingTips(this.lesson.steps)
        // 重新显示表单和组件
        this.showLessonForm = true
        // 确保数据更新完成
        await this.$nextTick()
        await this.publishLesson(null, sourceVersionId)
        // 确保表单和组件重新渲染完成
        await this.$nextTick()
        // 课程页面回到最上方
        this.$refs.form.$el.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } catch (error) {
      } finally {
        // 恢复测评点组件的显示
        this.lessonMeasureSelectNotShow = false
        // 触发恢复完成事件，通知子组件操作已完成
        this.$refs.historyDrawer.$emit('restore-complete')
        // 确保表单重新显示
        this.showLessonForm = true
      }
    },

    // 自动保存课程
    autoSaveLesson () {
      // 自动保存课程
      // 清除之前的防抖定时器
      if (this.autoSaveDebounceTimer) {
        clearTimeout(this.autoSaveDebounceTimer)
      }

      // 设置新的防抖定时器
      this.autoSaveDebounceTimer = setTimeout(() => {
        // 用户停止输入 debounceWaitTime 后，检查是否可以保存
        this.checkAndAutoSave()
      }, this.debounceWaitTime)
    },
    // 检查并执行自动保存
    checkAndAutoSave() {
      // 如果不满足自动保存条件，不执行自动保存
      if (!this.canAutoSave) {
        return
      }
      // 获取当前时间
      const now = new Date()

      // 计算时间差
      const publishTimeDiff = now.getTime() - this.lastPublishTime.getTime()
      // 只有当距离上次保存发布时间大于 autoSaveWaitTime，才执行自动保存发布
      if (publishTimeDiff > this.autoSaveWaitTime) {
        this.saveLesson()
      } else {
        if (this.autoSaveDebounceTimer) {
          clearTimeout(this.autoSaveDebounceTimer)
        }
        // 如果没到 autoSaveWaitTime 的时间间隔，则在 autoSaveWaitTime - publishTimeDiff 的时间间隔后检查是否可以保存
        this.autoSaveDebounceTimer = setTimeout(() => {
           this.checkAndAutoSave()
        }, this.autoSaveWaitTime - publishTimeDiff)
      }
    },
    // 检查是否需要强制自动保存（长时间未保存但有修改的情况）
    checkForceAutoSave() {
      // 如果不满足强制保存条件，不执行强制保存
      if (!this.canAutoSave) {
        return
      }

      // 获取当前时间
      const now = new Date()

      const publishTimeDiff = now.getTime() - this.lastPublishTime.getTime()

      // 如果距离上次保存时间超过设定的强制保存间隔，且有未保存的修改
      if (publishTimeDiff > this.forceSaveInterval && this.changesCounter) {
        this.saveLesson()
      }
    },

    // 处理页面关闭前的事件
    handleBeforeUnload (e) {
      // 如果存在临时草稿 ID，清理它
      this.cleanupTempDraft()
      
      const result = this.handlePageChange()
      if (!result || this.isDownloading) {
        return
      }
      e.preventDefault()
      // 兼容IE8和Firefox 4之前的版本
      e.returnValue = ''
      // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
      return ''
    },
    // 处理 Ctrl + s 事件
    handleSaveShortcut(e) {
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault(); // 阻止默认的保存行为
        this.handlePageChange()
      }
    },
    // 页面改变时，处理保存草稿或发布课程
    handlePageChange() {
    // 批量保存页面（不开启自动保存功能的页面）、添加页面生成未完成、没有修改时不需要弹窗
      if (this.batchEdit || (this.isAddLesson && !this.generaterdLessoncompleted) || (this.changesCounter == 0 && this.saving == 0)) {
        return false
      }
      this.saveLesson()
      return true
    },
    // 处理下载前的事件
    async handleBeforeDownload(callbacks) {
      try {
        // 调用 saveLesson 方法
        await this.saveLesson()
        this.isDownloading = true
        callbacks.success();
      } catch (error) {
        callbacks.error(error);
      }
    },
    handleAfterDownload() {
      this.isDownloading = false
    },
    // 清理临时草稿
    cleanupTempDraft() {
      if (this.tempDraftLessonId) {
        Lesson2.deleteLesson(this.tempDraftLessonId)
        // 清空临时草稿ID
        this.tempDraftLessonId = null
      }
    },
    // 设置临时草稿 ID
    setTempDraftLessonId(lessonId) {
      if (!this.isNeedManualPublish) {
        this.tempDraftLessonId = lessonId
      }
    },
    // 清空临时草稿 ID（在发布成功后调用）
    clearTempDraftLessonId() {
      this.tempDraftLessonId = null
    },
    // 检查 lesson 是否发生了变化
    checkLessonChange(sourceLesson, lesson) {
      // 处理无关字段
      lesson = this.handleIgnoreFields(lesson)
      sourceLesson = this.handleIgnoreFields(sourceLesson)

      // 比较处理后的 sourceLesson 和 lesson 是否相等
      return JSON.stringify(sourceLesson) !== JSON.stringify(lesson);
    },
    // 处理不需要参与比较的字段
    handleIgnoreFields(lesson) {
      let lessonCopy = JSON.parse(JSON.stringify(lesson))
      lessonCopy.authorName = null
      // ID 统一转大写
      if (lessonCopy.id) {
        lessonCopy.id = lessonCopy.id.toUpperCase()
      }
      // 将一些与 lesson 比较无关字段进行特殊处理，都置为 null，解决 lesson 自动保存时，changesCounter 不准确的问题
      if (lessonCopy.steps) {
        if (lessonCopy.steps.classCLRGenerated) {
          lessonCopy.steps.classCLRGenerated = null
        }
        lessonCopy.steps.forEach(step => {
          if (step.lessonStepGuides) {
            step.lessonStepGuides.forEach(guide => {
              guide.measureDescription = null;
              guide.core = null;
              guide.mapped = null;
              guide.sortIndex = null;
            });
          }

          if (step.teachingTips) {
            step.teachingTips.forEach(tip => {
              tip.core = null;
              tip.measureDescription = null;
              tip.measureName = null;
            });
          }

          if (step.lessonImpStepAndSource && step.lessonImpStepAndSource.sources) {
            step.lessonImpStepAndSource.sources.forEach(source => {
              source.hidden = null;
            });
          }
          // 解决富文本中不同 HTML 标签造成的 lesson 是否变化发生误判的问题
          if (step.culturallyResponsiveInstruction) {
            step.culturallyResponsiveInstruction = extractTextFromHtml(step.culturallyResponsiveInstruction)
          }
          if (step.culturallyResponsiveInstructionGroup) {
            step.culturallyResponsiveInstructionGroup = extractTextFromHtml(step.culturallyResponsiveInstructionGroup)
          }
          if (step.culturallyResponsiveInstructionGeneral) {
            step.culturallyResponsiveInstructionGeneral = extractTextFromHtml(step.culturallyResponsiveInstructionGeneral)
          }
        });
      }
      // 处理附件中不需要参与比较的字段
      if (lessonCopy.attachments) {
        lessonCopy.attachments.forEach(attachment => {
          attachment.icon = null;
          attachment.uid = null;
          attachment.status = null;
        });
      }
       // 处理 material 中文本字段
      if (lessonCopy.material) {
        // 解析出原始文本内容进行比较
        lessonCopy.material.description = EditorTools.parseValue(lessonCopy.material.description);
        if (lessonCopy.material.attachmentMedias) {
          lessonCopy.material.attachmentMedias.forEach(attachment => {
            attachment.icon = null;
            attachment.uid = null;
            attachment.status = null;
          });
        }
      }
      // 处理 dll 中的字段
      if (lessonCopy.dlls) {
        lessonCopy.dlls.forEach(dll => {
          // 只保留需要比较的字段
          const languages = dll.languages ? dll.languages.map(lang => ({
            content: lang.content,
            langCode: lang.langCode
          })) : [];

          // 重置dll对象，只保留需要比较的字段
          Object.keys(dll).forEach(key => {
            if (!['content', 'description', 'sortIndex', 'mediaModels'].includes(key)) {
              delete dll[key];
            }
          });

          // 重新赋值languages
          dll.languages = languages;
        });
      }

      if (lessonCopy.framework) {
        lessonCopy.framework.frameworkName = null;
        lessonCopy.framework.frameworkUrl = null;
        lessonCopy.framework.drdp = null;
      }

      // measures 只比较最底层测评点的 ID
      if (lessonCopy.measures) {
        // 递归收集最底层 measure 的 ID
        const collectMeasureIds = (measure) => {
          if (!measure.children || measure.children.length === 0) {
            return [measure.id]
          }
          return measure.children.reduce((ids, child) => {
            return ids.concat(collectMeasureIds(child))
          }, [])
        }

        // 收集所有最底层 measure 的 ID 并重新赋值
        lessonCopy.measures = lessonCopy.measures.reduce((ids, measure) => {
          return ids.concat(collectMeasureIds(measure))
        }, [])
      }
      // lesson 中所有为 null 的属性（需要递归查看），移除掉
      this.removeNullProperties(lessonCopy)
      return lessonCopy;
    },
    /**
     * 点击编辑提示弹窗
     */
    clickEditPromptPopover() {
      this.editPromptVisible = !this.editPromptVisible

      if (this.$route.name === 'AddLesson' && this.editPromptVisible) {
        // 编辑 prompt 曝光埋点
        this.$analytics.sendEvent('cg_lesson_plan_cre_editprompt_exposure')
      }

      this.$nextTick(() => {
        // 打开弹窗时，保存 AssistanTool 中的状态
        if (this.editPromptVisible) {
          this.getCurrentAssistantTool().saveAssistantToolState()
        } else { // 关闭弹窗时，恢复 AssistanTool 中的状态
          this.getCurrentAssistantTool().restoreAssistantToolStateByTabName()
        }
      })
    },
    // 关闭编辑 Prompt 弹窗
    closeEditPromptPopover() {
      // 如果弹窗从打开状态变成关闭状态，则恢复 AssistantTool 中的状态
      if (this.editPromptVisible) {
          this.$nextTick(() => {
          this.getCurrentAssistantTool().restoreAssistantToolStateByTabName()
        })
      }
      this.editPromptVisible = false
    },
    generateChangeDescription(sourceLesson, lesson){
      // 处理无关字段
      let lessonCopy = this.handleIgnoreFields(lesson)
      let sourceLessonCopy = this.handleIgnoreFields(sourceLesson)

      // 定义 lesson 中字段与修改描述中模块名称的对应关系
      const fieldDisplayMap = {
        'cover': 'Lesson Cover',
        'name': 'Lesson Name',
        'ages': 'Age Group',
        'prepareTime': 'Prepare Time',
        'activityTime': 'Activity Time',
        'templateType': 'Lesson Template',
        'themes': 'Theme/Topic',
        'framework': 'Framework',
        'measures': 'Measures',
        'objective': 'Objective',
        'material': 'Materials',
        'book': 'Family Resources',
        'videoBook': 'Family Resources',
        'attachments': 'Family Resources',
        'dlls': 'DLL Vocabulary and Phrases',
        'learnerProfiles': 'Portrait of a Graduate'
      }

      // 定义 lesson.steps 中字段与修改描述中模块名称的对应关系
      const stepsFieldMap = {
        'content': 'Implementation Steps/Guides',
        'media': 'Implementation Steps/Guides',
        'lessonScienceOfReadingModel': 'Implementation Steps/Guides',
        'teachingTips': 'Teaching Tips for Standards',
        'universalDesignForLearning': 'Universal Design for Differentiated Learning',
        'universalDesignForLearningGroup': 'Universal Design for Differentiated Learning',
        'mixedAgeDifferentiations': 'Universal Design for Differentiated Learning',
        'culturallyResponsiveInstruction': 'Culturally and Linguistically Responsive Practice',
        'culturallyResponsiveInstructionGroup': 'Culturally and Linguistically Responsive Practice',
        'culturallyResponsiveInstructionGeneral': 'Culturally and Linguistically Responsive Practice',
        'lessonStepGuides': 'Typical Behaviors and Observation Tips',
        'homeActivity': 'Family Resources',
        'questions': 'Formative Assessment',
        'lessonImpStepAndSource': 'Implementation Steps/Guides',
        "lessonClrAndSources": "Culturally and Linguistically Responsive Practice"
      }

      // 创建一个映射，记录每个业务名称对应的字段变化状态
      const displayNameChanges = {}
      let changes = []

      // 初始化所有业务名称的变化状态为false
      const allDisplayNames = [...new Set([...Object.values(fieldDisplayMap), ...Object.values(stepsFieldMap)])]
      allDisplayNames.forEach(displayName => {
        displayNameChanges[displayName] = false
      })

      // 检查普通字段
      Object.entries(fieldDisplayMap).forEach(([field, displayName]) => {
        const hasChanged = JSON.stringify(lessonCopy[field]) !== JSON.stringify(sourceLessonCopy[field])
        if (hasChanged) {
          displayNameChanges[displayName] = true
        }
      })

      // 检查steps中的字段
      if (lessonCopy.steps && sourceLessonCopy.steps) {
        // 遍历当前lesson的steps
        lessonCopy.steps.forEach(step => {
          // 根据ageGroupName和ageGroupValue找到对应的sourceStep
          const sourceStep = sourceLessonCopy.steps.find(s =>
            s.ageGroupName === step.ageGroupName &&
            s.ageGroupValue === step.ageGroupValue
          )

          if (!sourceStep) return // 如果找不到对应的step，跳过

          // 检查每个字段是否有变化
          Object.entries(stepsFieldMap).forEach(([field, displayName]) => {
            if (JSON.stringify(step[field]) !== JSON.stringify(sourceStep[field])) {
              displayNameChanges[displayName] = true
            }
          })
        })
      }



      // 更新changes数组
      Object.entries(displayNameChanges).forEach(([displayName, hasChanged]) => {
        if (hasChanged) {
          if (!changes.includes(displayName)) {
            changes.push(displayName)
          }
        } else {
          const index = changes.indexOf(displayName)
          if (index !== -1) {
            changes.splice(index, 1)
          }
        }
      })
      // 对 changes 去重
      changes = [...new Set(changes)]
      // 使用 , 拼接为字符串
      let description
      if (changes.length > 1) {
        description = changes.slice(0, -1).join(", ") + " and " + changes[changes.length - 1];
      } else if (changes.length === 1) {
        description = changes[0].toString(); // 如果只有一个元素，直接使用它
      } else {
        description = "lesson"; // 如果数组为空，返回 lesson
      }
      return description
    },
    // 移除 lesson 中所有为 null 的属性（需要递归查看）
    removeNullProperties(lesson) {
      Object.keys(lesson).forEach(key => {
        if (lesson[key] === null || lesson[key] === undefined || lesson[key] === '') {
          delete lesson[key];
        } else if (typeof lesson[key] === 'object' && lesson[key] !== null) {
          this.removeNullProperties(lesson[key])
        }
      })
    },
    // 保存 lesson
    async saveLesson () {
      // 如果没有任何变化并且是发布状态，则不保存 （首次生成过程中课程为 DRAFT 状态，需要调用 saveDraft）
      if (!this.changesCounter) {
        return
      }
      // 如果 lesson 是草稿，则保存草稿
      if (equalsIgnoreCase(this.lesson.status, 'DRAFT') || this.isNeedManualPublish) {
        await this.saveDraft()
      } else {
        await this.publishLesson()
      }
    },
    // 处理推荐到机构课程库的状态变化
    async handleRecommendToAgencyChange() {
      // 添加页面 && 课程 ID 存在 && 课程状态是发布状态 && 非批量编辑页面（开启了自动保存的页面）时，点击直接修改状态
      if (this.isAddLesson && this.lessonId && this.lesson.status === 'PUBLISHED' && !this.batchEdit) {
        try {
          if (this.recommendToAgency) {
            // 推荐课程
            await Lesson2.promoteLesson(this.lessonId)
            this.$message({
              type: 'success',
              message: 'This lesson was added to "Agency-wide Lessons" successfully!'
            })
          } else {
            // 取消推荐
            await Lesson2.cancelPromoteToAgency(this.lessonId)
            this.$message({
              type: 'success',
              message: this.$t('loc.lessons2RemoveSuccessTips')
            })
          }
          // 接口调用成功后保存到本地存储
          localStorage.setItem(getRecommendToAgencyKey(), this.recommendToAgency)
        } catch (error) {
          console.error('Failed to update lesson promotion status', error)
        }
      }
    },
    // 拷贝适应性课程
    async copyAdaptLesson(lesson) {
      // 如果 item 存在
      if (lesson && lesson.id) {
        // 如果不是我的课程，先复制课程副本，再加载
        const newLesson = await LessonApi.copyAdaptLesson(lesson.id)
        if (newLesson && newLesson.id) {
          // 保存复制的课程
          this.$set(lesson, 'oldLessonId', lesson.id)
          // 并为对应的 item 进行更新
          this.$set(lesson, 'id', newLesson.id)
          this.$set(lesson, 'isCopyLesson', true)
          this.$set(lesson, 'isAdaptedLesson', true)
          this.isAdaptedLesson = true
          this.lessonId = newLesson.id
          // 替换当前路由参数中的 lessonId
          this.$router.push({
            params: { ...this.$route.params, lessonId: this.lessonId.toUpperCase() }
          })
          // 清空 fromUnit
          this.fromUnit = null
        }
      }
    },
    // 设置监听 UDL 和 CLR 的加载完成方法
    setUDLAndCLRLoadCompletedListener() {
      /// 定义一个布尔值表示是否可以开始正常的生成数据了
      let lessonLibraryUDLMounted = false
      let lessonLibraryCLRMounted = false
      // 定义一个变量表示只生成一次数据
      let generateData = false
      // 监听 lessonLibraryUDLMounted 和 lessonLibraryCLRMounted 事件，如果加载完成，就继续加载下面的课程
      this.$bus.$on('lessonLibraryUDLMounted', () => {
        lessonLibraryUDLMounted = true
        // 如果 lessonDetailCompleted 和 lessonLibraryUDLMounted 和 lessonLibraryCLRMounted 都为 true，那么就说明第一个课程应该开始生成了
        if (lessonLibraryUDLMounted && lessonLibraryCLRMounted && !generateData) {
          this.generateUniversalDesignAndCLRData()
          // 设置为 true，表示已经生成过数据了
          generateData = true
        }
      })
      // 监听 lessonLibraryCLRMounted 事件，如果加载完成，就继续加载下面的课程
      this.$bus.$on('lessonLibraryCLRMounted', () => {
        lessonLibraryCLRMounted = true
        // 如果 lessonDetailCompleted 和 lessonLibraryUDLMounted 和 lessonLibraryCLRMounted 都为 true，那么就说明第一个课程应该开始生成了
        if (lessonLibraryUDLMounted && lessonLibraryCLRMounted && !generateData) {
          this.generateUniversalDesignAndCLRData()
          // 设置为 true，表示已经生成过数据了
          generateData = true
        }
      })
    },
    // 设置监听 UDL 和 CLR 的生成完成事件
    setUDLAndCLRGenerateCompletedListener() {
      // 设置 UDL 和 CLR 生成是否有监听
      this.udlAndClrGenerateHasListener = true
      // 监听 UDL 生成完成事件
      this.$bus.$on('generateUniversalDesignForLearningCompleted', () => {
        this.generateUniversalDesignForLearningCompleted = true
        if (this.isEnhanceLesson) {
          return
        }
        // 如果 CLR 也生成完成，则提示
        if (this.generateCLRCompleted) {
          this.$message.success(this.$t('loc.batchAdaptLesson19'))
          // 展示 lesson 保存时间信息
          this.convertLessonLoading = false
          // 立即保存课程
          this.saveLesson()
        }
      })
      // 监听 CLR 生成完成事件
      this.$bus.$on('generateCLRCompleted', () => {
        this.generateCLRCompleted = true
        if (this.isEnhanceLesson) {
          return
        }
        // 如果 UDL 也生成完成，则提示
        if (this.generateUniversalDesignForLearningCompleted) {
          this.$message.success(this.$t('loc.batchAdaptLesson19'))
          // 展示 lesson 保存时间信息
          this.convertLessonLoading = false
          // 立即保存课程
          this.saveLesson()
        }
      })
      // 监听 generateUniversalDesignForLearningStart 事件
      this.$bus.$on('generateUniversalDesignForLearningStart', () => {
        this.generateUniversalDesignForLearningCompleted = false
      })
      // 监听 generateCLRStart 事件
      this.$bus.$on('generateCLRStart', () => {
        this.generateCLRCompleted = false
      })
    },
    // 处理 adapt 设置弹框选定数据之后的逻辑
    async handleLessonAdapt() {
      // 如果当前课程不是 adapted，则需要复制课程，并恢复课程
      if (!this.lesson.isAdaptedLesson && !this.isAdaptedLesson) {
        // 复制课程
        await this.copyAdaptLesson(this.lesson)
      }
      // 判断 UDL 和 CLR 生成是否有监听
      if (!this.udlAndClrGenerateHasListener) {
        // 设置生成 UDL 和 CLR 完成监听
        this.setUDLAndCLRGenerateCompletedListener()
      }
      // 生成 UDL 和 CLR 数据
      this.generateUniversalDesignAndCLRData()
    },
    // 课程生成完成后的处理
    async handleLessonGenerated(lessonId) {
        await this.$axios.post($api.urls().clearResourceHistory, null, {
          params: { lessonId: lessonId }
        })
    },
    // 处理 Quiz 重新生成的 loading 状态
    handleQuizRegenerateLoading(loading) {
      this.quizRegenerateLoading = loading
    },
    /**
     * 跳转到 Unit 详情页
     */
     goToUnitDetail() {
      if (this.fromUnit && this.fromUnit.id) {
        this.$router.push({
          name: 'load-unit-cg',
          params: {
            unitId: this.fromUnit.id,
            completeToOverview: true,
            goToOverview: true
          }
        })
      }
    }
  }

}
</script>
<style lang="scss">
.edit-prompt-popover {
  padding: 12px 18px;
  width: 480px;
}
.assistant-tool-edit-prompt {
  max-height: calc(100vh - 220px);
  min-height: 220px;
  height: auto;
}
</style>
<style lang="scss" scoped>
.lesson-editor-container {
  /deep/ .clr-content {
    border: 0;
  }
  /deep/ .class-specific-instructions {
    padding: 0;
  }
}
/* 单个年龄组时隐藏删除按钮 */
.single-age-group {
  ::v-deep .el-tag__close {
    display: none !important;
  }
}
.lesson-from-unit-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 100px;
  padding: 8px 12px;
  background-color: #F3EEFF;
  cursor: pointer;
  width: fit-content;
  transition: background-color 0.3s ease;
  &:hover {
    background-color: #E9DCFF;
    transition: background-color 0.3s ease;
  }
  .unit-title {
    font-size: 14px;
    font-weight: 400;
    max-width: 220px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/deep/ .media-uploader-lesson-cover .media-uploader{
  border-radius: 8px;
  .el-upload-dragger {
      border: none;
      background: #F2F2F2;
  }
  .el-upload-dragger.is-dragover {
      border: 0.2em dashed var(--10-b-3-b-7, #10B3B7);
  }
  .media-viewer {
    border-radius: 8px;
  }
}

.lesson-from-unit-container {
  gap: 10px;
  margin-bottom: -8px;
}

.assistant-container {
  min-height: 400px;
}
// 添加媒体查询，处理不同高度屏幕的适配
@media (min-height: 854px) {
  .assistant-container {
    height: auto !important; // 高屏幕下自适应高度
    color: #111c1c;
  }
}
/deep/ .assistant-tool.el-card {
  border-radius: 8px;
}
.assistant-tool {
  border-radius: 8px;
  /deep/ .el-card__body {
  height: 100%;
  padding: 0;
}
  /deep/ .form-area {
    padding: 0 24px;
  }
  /deep/ .card-area {
    padding: 16px 0px;
  }
  /deep/ .btn-area {
    padding: 0 24px;
  }
}

.assistant-tool {
  margin: 0 auto;
}

/* 标准屏幕适配 */
@media screen and (min-width: 1366px) {
  .assistant-tool {
    /* 标准屏幕下的宽度和高度 */
    width: 760px;
  }
}

/* 小屏幕适配 */
@media screen and (min-width: 1200px) and (max-width: 1366px) {
  .assistant-tool {
    /* 小屏幕下的宽度和高度 */
    width: 700px;
  }
}

@media screen and (min-width:768px) and (max-width:1600px) {
    .new-lesson /deep/ {
    font-size: 14px;
    color: #333333;
    //overflow: auto;

    .el-form-item__label {
      font-weight: 600;
      font-size: 16px;
      color: #111c1c;
      line-height: 22px;
      margin-bottom: 0;
    }

    .el-form-item {
      margin-bottom: 24px;
      display: inline-block;
    }
  }

  .header {
    text-align: center;
    height: 50px;
    padding: 0 35px;
    line-height: 50px;
    color: #111C1C;
    background-color: #e4eaec;

    :first-child {
      float: left;
    }

    :last-child {
      font-size: 18px;
      color: #111C1C;
    }
  }
  .content {
    margin: 0;
    min-width: 1100px;

    form {
      background-color: #fff;
      width: 1060px;
      padding: 20px 20px 0px 20px;
    }

    .fixed-status-bar-width {
      width: 1060px;
    }

    .assistant-toolbar {
      width: 350px;
      max-height: 900px;
      // overflow: auto;
      position: sticky;
      top: 0;
    }

  }

  .input-select {
    width: 100%;
  }
  .has-from-unit-tag {
    .lesson-form-name {
      margin-top: 20px;
    }
  }
  .el-form-item {
    display: inline-block;

    &.lesson-form-cover {
      float: left;
      margin-right: 40px;
      margin-top: 20px;
      .lesson-cover-el-skeleton-item {
        height: 310px;
        width: 540px;
      }
    }

    &.lesson-form-name, &.lesson-form-age, &.lesson-form-theme{
      width: 440px;
    }

    &.lesson-form-prepare, &.lesson-form-activity {
      width: 210px;
      /deep/ .lesson-template-select-modal {
        position: relative;
        bottom: 3px;
      }
    }

    &.lesson-form-activity {
      margin-left: 20px;
    }

    &.lesson-form-framework ,&.lesson-form-domain {
      width: 495px;
      margin-bottom: 20px;
    }

    &.lesson-form-framework {
      margin-right: 30px;
    }

    &.lesson-form-objective, &.lesson-form-material, &.lesson-form-step {
      width: 100%;
    }

    &.lesson-form-step {
      margin-bottom: 0;

      .el-form-item {
        padding-left: 15px;
        width: 100%;
      }
    }

    &.lesson-form-book, &.lesson-form-video {
      width: 240px;
    }

    &.lesson-form-video {
      vertical-align: top;
      margin-left: 34px;
    }

    &.lesson-form-attachment {
      width: 100%;
    }
  }

  .lesson-share, .lesson-dll {
    position: relative;
    width: 100%;
    margin: 27px auto 0;
    border-radius: 4px;
    background: #F2F2F2;
    padding: 48px 34px 30px;

    & > :first-child {
      position: absolute;
      display: block;
      width: 467px;
      height: 58px;
      border-radius: 4px;
      margin: 0 auto;
      padding: 0;
      top: -29px;
      left: calc(50% - 233.5px);
      text-align: center;
      line-height: 58px;
      color: #ffffff;
      font-size: 24px;
    }
  }

  .lesson-dll {
    position: relative;
    margin: 27.5px auto 0;
    border-radius: 4px;
    background: #FFE8B9;
    padding: 48px 30px 30px;
    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
  }
  .lessons2-lesson-resources-info {
    position: relative;
    bottom: 39px;
    width: 467px;
    gap: 10px;
    color: #FFFFFF;
    font-weight: normal;
  }
  .lesson-share {
    position: relative;
    margin: 24px auto 0;
    border-radius: 4px;
    background: #D9F0CE;
    padding: 48px 34px 30px;

    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
  }

  .new-lesson-operation {
    gap: 16px;
    margin-right: -20px;
    display: flex;
    justify-content: center;
    position: sticky;
    bottom: 0 !important;
    padding: 16px 0;
    background-color: white;
    z-index: 200;
  }

  .el-form-item /deep/ .el-form-item__error {
    padding-left: 0;
    padding-top: 5px;
  }

  .el-button--primary.is-disabled, .el-button--primary.is-disabled:active, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:hover {
    color: #fff !important;
    background-color: #88d9db !important;
    border-color: #88d9db !important;
  }

  ::v-deep .confirm-dialog .el-dialog__body {
    padding: 20px 20px;
  }

  /deep/ .quill-editor.editor {
    word-break: break-word;
    display: flex;
    flex-direction: column;
  }

  // .el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  //   bottom: 10px;
  // }

    /deep/ .lesson-step-input .editor .ql-container{
        max-height: 400px;
    }

  /deep/ .step-left {
    padding-left: 0 !important;
  }
}

@media screen and (min-width:1600px) {
  .new-lesson /deep/ {
    font-size: 14px;
    color: #333333;
    //overflow: auto;

    .el-form-item__label {
      font-weight: 600;
      font-size: 16px;
      color: #111c1c;
      line-height: 22px;
      margin-bottom: 0;
    }

    .el-form-item {
      margin-bottom: 24px;
      display: inline-block;
    }
  }

  .header {
    text-align: center;
    height: 50px;
    padding: 0 35px;
    line-height: 50px;
    color: #111C1C;
    background-color: #e4eaec;

    :first-child {
      float: left;
    }

    :last-child {
      font-size: 18px;
      color: #111C1C;
    }
  }

  .content {
    margin: 0;
    min-width: 1200px;

    form {
      width: 1200px;
      background-color: #fff;
      padding: 50px 50px 0 50px;
    }

    .has-from-unit-tag {
      padding-top: 24px;
    }

    .fixed-status-bar-width {
      width: 1200px;
    }

    .assistant-toolbar {
      width: 400px;
      position: sticky;
      top: 0;
    }
    .assistant-toolbar-full{
        width: calc(100vw - 300px);
        height: calc(115vh - 283px);
        // overflow-y: auto;
        position: sticky;
        top: 0;
    }
  }

  .input-select {
    width: 100%;
  }
  .lesson-from-unit-container {
    margin-bottom: 12px;
  }
  .el-form-item {
    display: inline-block;

    &.lesson-form-cover {
      width: 640px;
      height: 340px;
      float: left;
      margin-right: 20px;
        .lesson-cover-el-skeleton-item {
            height: 380px;
            width: 640px;
        }
    }

    &.lesson-form-name, &.lesson-form-age, &.lesson-form-theme {
      width: 439px;
    }

    &.lesson-form-prepare, &.lesson-form-activity {
      width: 205px;
      /deep/ .lesson-template-select-modal {
        position: relative;
        bottom: 3px;
      }
    }

    &.lesson-form-activity {
      margin-left: 29px;
    }

    &.lesson-form-framework, &.lesson-form-domain {
      width: 510px;
      margin-bottom: 20px;
    }

    &.lesson-form-framework {
      margin-right: 79px;
    }

    &.lesson-form-material, &.lesson-form-step {
      width: 1100px;
    }

    &.lesson-form-objective{
      width: 1100px;
      margin-top: 20px;
    }

    &.lesson-form-step {
      margin-bottom: 0;

      .el-form-item {
        padding-left: 15px;
        width: 100%;
      }
    }

    &.lesson-form-book, &.lesson-form-video {
      width: 240px;
    }

    &.lesson-form-video {
      vertical-align: top;
      margin-left: 34px;
    }

    &.lesson-form-attachment {
      width: 100%;

    }
  }

  .lesson-share, .lesson-dll {
    position: relative;
    width: 1100px;
    margin: 27px auto 0;
    border-radius: 4px;
    background: #F2F2F2;
    padding: 48px 34px 30px;

    & > :first-child {
      position: absolute;
      display: block;
      width: 467px;
      height: 58px;
      border-radius: 4px;
      margin: 0 auto;
      padding: 0;
      top: -29px;
      left: calc(50% - 233.5px);
      text-align: center;
      line-height: 58px;
      color: #ffffff;
      font-size: 24px;
    }
  }

  .lesson-dll {
    position: relative;
    margin: 27.5px auto 0;
    border-radius: 4px;
    background: #FFE8B9;
    padding: 48px 30px 30px;
    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
  }
  .lessons2-lesson-resources-info {
    position: relative;
    bottom: 39px;
    width: 467px;
    gap: 10px;
    color: #FFFFFF;
    font-weight: normal;
  }
  .lesson-share {
    position: relative;
    margin: 24px auto 0;
    border-radius: 4px;
    background: #D9F0CE;
    padding: 48px 34px 30px;

    .lessons2-lesson-resources {
      position: relative;
      top: 19px;
    }
  }

  .new-lesson-operation {
    display: flex;
    justify-content: center;
    gap: 40px;
    position: sticky;
    bottom: -1px;
    padding: 16px 0;
    background-color: white;
    z-index: 200;
  }

  .el-form-item /deep/ .el-form-item__error {
    padding-left: 0;
    padding-top: 5px;
  }

  .el-button--primary.is-disabled, .el-button--primary.is-disabled:active, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:hover {
    color: #fff !important;
    background-color: #88d9db !important;
    border-color: #88d9db !important;
  }

  ::v-deep .confirm-dialog .el-dialog__body {
    padding: 20px 20px;
  }

  /deep/ .quill-editor.editor {
    word-break: break-word;
  }
  /deep/ .media-uploader-lesson-cover {
    width: 620px;
    height: 348px;
  }
  /deep/ .media-uploader-lesson-cover .media-uploader-select {
    width: 620px;
    height: 348px;
    margin-top: 0;
  }
  /deep/ .media-uploader-lesson-cover .media-uploader {
    width: 620px;
    height: 348px;
  }
  /deep/ .media-uploader-lesson-cover .media-uploader-selected {
    width: 620px;
    height: 348px;
  }
  // .el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  //   bottom: 10px;
  // }

  /deep/ .step-left {
    padding-left: 0 !important;
  }

  /deep/ .lesson-step-input .editor .ql-container{
    max-height: 400px;
  }
}

@media (max-height: 1050px) {
  .content {
    .assistant-toolbar {
      height: calc(100vh - 140px);
    }
  }
}

/deep/ .el-popper.enhance-lesson-popover {
  background: var(--color-ai-assistant) !important;
  color: var(--color-white);
  padding: 24px;
  border: none;
  border-radius: 8px;

  &.el-popper[x-placement^=left] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }

  img {
    width: 100%;
  }

  .el-button {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
    color: var(--color-ai-assistant);
  }

  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #DCDFE6);
    background: var(--ffffff, #FFF);
  }

  .el-button--text, .el-button--text:hover {
    color: #FFFFFF;
    background: transparent;
    border: none;
  }
}

/* 箭头基本样式 */
/deep/ .el-popover.el-popper.enhance-lesson-popover .popper__arrow {
  bottom: -6px !important;
  top: auto !important;
  border-top-color: var(--color-ai-assistant) !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-width: 0 !important;
  left: 50% !important;
  margin-left: 0 !important;
  transform: translateX(-50%) !important;
}

/* 箭头内部样式 */
/deep/  .el-popover.el-popper.enhance-lesson-popover .popper__arrow::after {
  border-top-color: var(--color-ai-assistant) !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-width: 0 !important;
}

.lesson-field-label {
    font-size: 16px;
    line-height: 34px;
    height: 34px;
    font-weight: 600;
    color: #333333;
}

.left-gradient {
    background: linear-gradient(270deg, #10B3B7 -1.16%, rgba(16, 179, 183, 0.00) 98.84%);
}

.right-gradient {
    background: linear-gradient(90deg, #10B3B7 -1.16%, rgba(16, 179, 183, 0) 98.84%)
}

.title-gradient {
    width: 45px;
    height: 2px;
    border-radius: 1px;

}

.m-r-sm-24{
  margin-right: 24px;
}

.lesson-step-input {
  /deep/ .ql-toolbar.ql-snow {
    border: 1px solid rgb(220, 223, 230);
    border-bottom: none;
  }
  /deep/ .ql-container.ql-snow {
    border: 1px solid rgb(220, 223, 230);
    border-top: none;
  }
  /deep/ .editor .ql-editor.ql-blank:before  {
    font-size: 14px;
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 4px 0px 4px 0px;
  justify-content: flex-start;
}

.right-buttons {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 12px;
}

.status-item {
  margin-top: 12px;
  display: flex;
  align-items: center;
  color: #303133;
}

.status-icon {
  margin-right: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.status-icon .el-icon-success {
  color: #67c23a;
  font-size: 18px;
}

.status-icon .el-icon-refresh {
  color: #909399;
  font-size: 18px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.status-text {
  margin-right: 10px;
  font-size: 16px;
  font-weight: 600;
}

.status-time {
  color: #909399;
  font-size: 14px;
}

.status-title {
  font-size: 14px;
  color: #606266;
  margin-right: 15px;
  font-weight: 500;
}

.fixed-status-bar {
  margin: 0 auto;
  margin-bottom: 8px;
  position: sticky;
  top: 0;
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.additional-content-area {
  margin-top: 10px;
  min-height: 10px;
}

.edit-prompt-button {
  background-color: #FFFFFF;
  border: 3px solid #10B3B7;
  border-radius: 4px;
  padding: 0 10px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.edit-prompt-button:hover {
  box-shadow: 0 0 2px 3px rgba(16, 179, 183, 0.5);
}

.download-export-button {
  background: rgb(255, 127, 65);
  border-color: rgb(255, 127, 65);
  height: 36px;
  display: flex;
  align-items: center;
}

.download-export-button:hover {
  background: rgb(255, 127, 65);
  border-color: rgb(255, 127, 65);
}

.download-export-button i {
  margin-right: 5px;
}

.download-export-button i:last-child {
  margin-left: 5px;
  margin-right: 0;
}

.read-only-info {

  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 24px;

  .age-group-framework-info {
    display: flex;
    gap: 16px;

    .read-only-info-item {
      border-radius: 90px;
      padding: 4px 16px;
      color: #FF910E;
      border: 1px solid #FF910E;
      background-color: #FFF5E9;
      font-weight: 600;
    }

    .text-no-wrap {
      white-space: nowrap;
    }

    .text-overflow-ellipsis {
      text-overflow: ellipsis;
      overflow: hidden;
    }

  }

  .measures-info {
    display: flex;

    .measures-info-title {
      padding: 4px 10px;
      border-radius: 4px 0px 0px 4px;
      background: #FD8480;
      color: #FFF;
      font-weight: 600;
    }

    .measures-info-content {
      padding: 4px 10px;
      border-radius: 0px 4px 4px 0px;
      background: #FFEEE5;
      color: #FF9682;
      font-weight: 600;
    }
  }
  .feedback-button {
  display: inline-flex;
  align-items: center;
}

}

.form-container {
  height: calc(-190px + 100vh);
}

.form-container-with-banner {
  height: calc(-242px + 100vh);
}

/* 超小屏幕适配 */
@media screen and (min-width: 768px) and (max-width: 1200px) {
  .assistant-tool {
    /* 超小屏幕下的宽度和高度 */
    width: 600px;
  }

  .el-form-item {
    display: inline-block;
    width: 100%;

    &.lesson-form-cover {
      float: left;
      margin-right: 40px;
      margin-top: 20px;
      .lesson-cover-el-skeleton-item {
        height: 310px;
        width: 540px;
      }
    }

    &.lesson-form-name, &.lesson-form-age, &.lesson-form-theme{
      width: calc(100vw - 65px) !important;
    }

    &.lesson-form-prepare, &.lesson-form-activity {
      width: 210px;
      /deep/ .lesson-template-select-modal {
        position: relative;
        bottom: 3px;
      }
    }

    &.lesson-form-activity {
      margin-left: 20px;
    }

    &.lesson-form-framework ,&.lesson-form-domain {
      width: 495px;
      margin-bottom: 20px;
    }

    &.lesson-form-framework {
      margin-right: 30px;
    }

    &.lesson-form-objective, &.lesson-form-material, &.lesson-form-step {
      width: 100%;
    }

    &.lesson-form-step {
      margin-bottom: 0;

      .el-form-item {
        padding-left: 15px;
        width: 100%;
      }
    }

    &.lesson-form-book, &.lesson-form-video {
      width: 240px;
    }

    &.lesson-form-video {
      vertical-align: top;
      margin-left: 34px;
    }

    &.lesson-form-attachment {
      width: 100%;
    }
  }

}

// 移动端适配
@media screen and (max-width: 768px) {
  .assistant-tool {
    width: 100vw;
  }
  .mobile-new-lesson {
    padding: 0;
  }

  .mobile-lesson-editor-container {
    padding: 10px;
  }

  .mobile-fixed-status-bar {
    position: relative;
    padding: 8px;
    margin-bottom: 10px;
  }

  .mobile-status-indicator {
    flex-direction: column;
    align-items: flex-start;
  }

  .mobile-status-item {
    margin-bottom: 8px;
  }

  .mobile-status-icon {
    font-size: 14px;
  }

  .mobile-status-time {
    font-size: 12px;
  }

  .mobile-feedback-button {
    margin-left: 0 !important;
    margin-top: 8px;
  }

  .mobile-right-buttons {
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-top: 10px;
  }

  .mobile-form-container {
    padding: 10px;
  }

  .mobile-lesson-form {
    width: 100% !important;
  }

  .mobile-lesson-form-cover {
    margin-bottom: 15px;
  }

  .mobile-hidden-lg-and-up {
    width: 100%;
    align-items: baseline !important;
  }

  .mobile-hidden-md-and-down {
    display: none;
  }

  .mobile-new-lesson-operation {
    flex-direction: column;
    align-items: stretch;
  }
  .mobile-new-lesson-operation .el-button {
    margin-bottom: 10px;
  }
  .content {
    min-width: 100vw !important;
    width: 100vw !important;
    max-width: 100vw !important;

    form {
      background-color: #fff;
      min-width: 100% !important;
      width: 100% !important;
      max-width: 100% !important;
      padding: 20px 20px 0px 20px;
      overflow-x: hidden;
    }

    .fixed-status-bar-width {
      min-width: 100% !important;
      width: 100% !important;
      max-width: 100% !important;
    }
  }

  .el-form-item {
    &.lesson-form-activity {
      margin-left: 0px !important;
    }
    &.lesson-form-name, &.lesson-form-age, &.lesson-form-theme, &.lesson-form-activity{
      width: 100% !important;
    }
  }
}

/* 优化滚动性能 */
.lg-scrollbar-show {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  overflow-y: auto;
  /* 减少重绘 */
  will-change: scroll-position;
  /* 使用 GPU 加速滚动 */
  -webkit-overflow-scrolling: touch;
}

/* 生成状态下的特殊优化 */
.lesson-editor-container.generating {
  /* 减少不必要的重排 */
  contain: layout style paint;
}

</style>
