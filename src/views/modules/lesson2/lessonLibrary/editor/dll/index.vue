<template>
  <div class="lesson-dll-input">
    <el-card class="box-card remove-border" shadow="never">
      <div slot="header" class="display-flex justify-content-between dll-header" v-if="dlls.length > 0">
        <span class="font-bold font-size-16 text-default">{{ title }} <span class="color-676879">({{ dlls.length }})</span> </span>
        <div class="display-flex" style="gap: 12px">
          <el-button plain style="height: 36px;" @click="toPreviewDll()">
            <div class="display-flex align-items" style="gap: 5px">
              <span>{{ $t('loc.dll3') }}</span>
              <i class="lg-icon lg-icon-arrow-right font-size-20"></i>
            </div>
          </el-button>
          <el-button class='el-button-warning-dark' @click="handleClickAdd" style="height: 36px">{{ $t('loc.dll4') }}</el-button>
        </div>
      </div>
      <div class="dll-list" v-if="dlls.length > 0">
        <div style="height: 188px;" v-for="(item, index) in initMaxLimit" :key="index" class="dll-item border-radius-4" @mouseenter="mouseEnterItem(item)" @mouseleave="mouseLeaveItem(item)">
          <img v-if="item.content && item.media && item.media.url" :src="item.media && item.media.url" alt="" class="w-full dll-cover lg-pointer" @click="toPreviewDll(index)">
          <div class="dll-cover-container lg-pointer" v-else @click="toPreviewDll(index)">
            <img :src="emptyMediaUrl" style="width: 90px;height: 60px" alt="" class="w-full dll-cover lg-pointer">
          </div>
          <div class="w-full display-flex justify-content-between bg-color-F5F6F8 add-padding-l-10 add-padding-r-10 bottom-content">
            <span class="font-size-16 font-weight-600 vocabularies-content" :class="{ 'vocabularies-body': item.edit }" :title="item.content">{{ item.content }}</span>
            <div v-show="item.edit">
              <i class="lg-icon lg-icon-edit font-size-20 display-inline-block add-margin-r-10 lg-pointer edit-operate" @click="editVocabularies(item, index)"></i>
              <i class="lg-icon lg-icon-delete font-size-20 lg-pointer remove-operate" @click="handleClickDelete(item, index)"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="display-flex justify-content w-full" v-if="isAppear && dlls.length > 8">
        <span class="text-primary lg-pointer font-weight-600" @click="showMore()">{{ $t('loc.dll1') }}</span>
      </div>
      <div class="display-flex justify-content w-full" v-if="!isAppear && dlls.length > 8">
        <span class="text-primary lg-pointer font-weight-600" @click="showLess()">{{ $t('loc.dll2') }}</span>
      </div>
      <div class="w-full bg-white border-radius-6" style="height: 400px;" v-if="dlls.length === 0">
        <LgEmptyPage>
          <template slot="custom-content">
            <el-button class='el-button-warning-dark add-margin-t-20' style="height: 36px" @click="handleClickAdd">{{ $t('loc.dll4') }}</el-button>
          </template>
        </LgEmptyPage>
      </div>
      <el-dialog
        :title="operateType === 'add' ? $t('loc.dll4') : $t('loc.dll6')"
        :append-to-body="true"
        :visible="addKeyVocabulariesDialogVisible"
        :close-on-click-modal="false"
        width="35%"
        custom-class="add-dll-dialog-class"
        :before-close="handleClose">
        <div>
          <div ref="dllTitleInput" class="add-padding-t-5">
            <el-form ref="ruleForm" v-if="ruleForm" :model="ruleForm" class="form-add-dll" label-position="top" :rules="rules">
              <el-form-item :label="$t('loc.addDLLContentTitle')" prop="content">
                <div class="display-flex align-items">
                  <el-input
                    class="content-input flex-1"
                    type="textarea"
                    resize="none"
                    v-model="ruleForm.content"
                    maxlength="200"
                    show-word-limit
                    :placeholder="$t('loc.addDLLContentTip')"></el-input>
                  <!-- 选择照片的控件 -->
                  <el-upload
                    class="add-margin-l-10 add-margin-b-0 add-dll-upload"
                    :class="uploadHavePlus ? 'add-dll-upload-plus' : 'add-dll-upload-no-plus'"
                    v-if="fileType !== 'mp4' && fileType !== 'aac'"
                    v-loading="fileUploadLoading"
                    list-type="picture-card"
                    action
                    :http-request="(file) => fileUpload(file, attach)"
                    :on-success="
                      (res, file, fileList) => {
                        uploadSuccess(res, file, fileList, attach)
                      }
                    "
                    :on-change="
                      (file, fileList) => {
                        upAttachChange(file, fileList, attach)
                      }
                    "
                    :on-remove="
                      (file, fileList) => {
                        handleRemove(file, attach)
                      }
                    "
                    accept=".jpg,.png,.jpeg"
                    :limit="1"
                    :file-list="attach.medias"
                    :before-upload="beforeAvatarUpload"
                  >
                    <i slot="default" class="el-icon-picture-outline"></i>
                    <div slot="file" slot-scope="{ file }">
                      <img class="el-upload-list__item-thumbnail"
                           style="object-fit: cover;"
                           :src="file.url">
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview"
                              @click="handlePictureCardPreview(file)">
                          <i class="el-icon-search"></i>
                        </span>
                        <span
                          class="el-upload-list__item-delete"
                          @click="handleRemove(file, attach)">
                            <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </div>
              </el-form-item>

              <el-form-item :label="$t('loc.dll7')" prop="description" style="margin-bottom: 5px!important;">
                <el-input
                  type="textarea"
                  v-model="ruleForm.description"
                  :autosize="{ minRows: 4, maxRows: 5}"
                  class="content-input"
                  resize="none"
                  maxlength="200"
                  show-word-limit
                  :placeholder="$t('loc.dll5')">
                </el-input>
              </el-form-item>

              <el-form-item prop="selectedLanguageNum">
                <div class="w-full display-flex add-margin-t-10" style="gap: 10px">
                  <DllLanguageSelect class="flex-1" v-model="langCodes" :languages="languages" v-if="languages"/>
                  <el-button type="primary" :disabled="ruleForm.content.trim() === ''" :loading="translating" @click="translateVocabularies()">{{ $t('loc.translate') }}</el-button>
                </div>
              </el-form-item>
            </el-form>
            <!-- 翻译结果列表 -->
            <dll-translation v-for="(item, index) in translations" :key="item.langCode" :translation="item" class="dll-translate"
                             :ref="`translation${index}`" @change="$emit('change')"/>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">{{ $t('loc.cancel') }}</el-button>
          <el-button type="primary" :disabled="disabledOperate" @click="handleAddKeyVocabularies">{{ $t('loc.save') }}</el-button>
        </span>
      </el-dialog>
    </el-card>
    <!-- DLL 演示的弹框 -->
    <DLLHomeworkDemoDialog ref='dllHomeworkDemoDialog'></DLLHomeworkDemoDialog>
    <!-- 查看大图的弹框 -->
    <el-dialog :visible.sync="dialogVisible" :append-to-body="true">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <!-- 编辑提示对话框 -->
    <el-dialog :title="$t('loc.confirmation')" custom-class="show-edit-tip" :visible.sync="editTipVisibleDialog"
               :lock-scroll="false" width="35%" :close-on-click-modal="false" :close-on-press-escape="false" :append-to-body="true">
      <span class="dll-text-title-alt-color font-size-14">{{ $t('loc.editTip') }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="editTipVisibleDialog = false">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="submitAddKeyVocabularies">{{ $t('loc.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { deepEqual } from '@/utils/common'
import DLLHomeworkDemoDialog from '@/views/dll/DLLHomeworkDemoDialog.vue'
import LgEmptyPage from '@/components/LgEmptyPage.vue'
import configBaseUrl from '@/utils/setBaseUrl'
import axios from 'axios'
import store from '@/store'
import emptyMediaPic from '@/views/modules/lesson2/component/assets/img/media.png'
import DllLanguageSelect from '@/views/modules/lesson2/lessonLibrary/editor/dll/DllLanguageSelect'
import DllTranslation from '@/views/modules/lesson2/lessonLibrary/editor/dll/DllTranslation'
import DLLApi from '@/api/dll/DLL'

export default {
  name: 'LessonDllInput',
  components: { DLLHomeworkDemoDialog, LgEmptyPage, DllLanguageSelect, DllTranslation },
  props: [
    'title',
    'value'
  ],
  data () {
    return {
      dlls: [{}], // 课程 DLL
      needTranslation: [], // 需要翻译，对象数组：id(dll 唯一标识：0,1,2),reason(需要翻译的原因：content、language)
      edit: false,
      addKeyVocabulariesDialogVisible: false, // 添加关键词弹窗标识
      ruleForm: {
        title: this.title, // DLL 的标题
        content: '', // DLL 的内容
        languages: [], // DLL 的选择语言
        media: null, // DLL 的图片
        edit: false,
        description: '' // DLL 关键词描述
      }, // 关键词表单信息
      // 输入的内容的校验，包括是否填写和字数限制
      rules: {
        // DLL的内容
        content: [
          {
            required: true,
            message: this.$t('loc.fieldIsRequired'),
            trigger: 'blur'
          },
          {
            max: 200,
            message: this.$t('loc.limitInputNum2000'),
            trigger: 'blur'
          }
        ]
      },
      uploadHavePlus: true, // 上传图片含有plus的class
      fileUploadLoading: false, // 文件上传 loading
      attach: { medias: [], mediakeys: [] }, // 上传图片的文件数组和图片id的数组
      fileType: 'image', // 文件类型，用于编辑
      operateType: 'add', // 操作类型
      emptyMediaUrl: emptyMediaPic,
      currentIndex: 0, // 当前操作的 DLL 的下标索引
      languages: null, // 可选语言
      langCodes: [], // 选择的语言
      translating: false, // 是否正在翻译中
      translations: [], // 翻译结果
      initMaxLimit: [], // 初始 DLL 最大限制
      isAppear: true, // 是否显示 show More
      dialogImageUrl: '', //  查看大图的url
      dialogVisible: false, // 查看大图弹框的标识,
      disabledOperate: false, // 禁止操作
      editTipVisibleDialog: false,
      translateOfContent: true, // 翻译和单词是否对应，有可能改变单词了但没重新翻译
      ruleForCmontentRecord:''
    }
  },
  async mounted () {
    let { languages, used } = await this.listLanguages()
    this.languages = languages
    this.langCodes = used
    this.initDlls()
  },
  watch: {
    dlls: {
      deep: true,
      handler (value) {
        if (!deepEqual(value, this.value)) {
          this.$emit('input', value)
        }
      }
    },
    value: {
      immediate: true,
      handler (value) {
        if (!deepEqual(value, this.dlls)) {
          value = value || []
          value.forEach((dll, index) => dll.sortIndex = index)
          this.dlls = JSON.parse(JSON.stringify(value))
          this.needTranslation = this.needTranslation ? this.needTranslation.splice(0, this.needTranslation.length) : []
          // value 数据的变化添加到渲染数组中
          this.initDlls()
        }
      }
    },
    'ruleForm.content' () {
      // 监测单词和翻译的对应状态
      this.translateOfContent = this.ruleForCmontentRecord===this.ruleForm.content
    }
  },
  methods: {
    initDlls () {
      // 如果 DLL 数量小于等于 8，则显示全部，且隐藏查看更多
      if (this.dlls.length <= 8) {
        this.initMaxLimit = this.dlls
        this.isAppear = false
        return
      }
      // 如果是显示 show More 的时候再切割
      if (this.isAppear) {
        // 如果 DLL 数量大于 8，则只显示 8 个，否则显示全部
        this.initMaxLimit = this.dlls.length > 8 ? this.dlls.slice(0, 8) : this.dlls
      }
    },
    // 获取可选语言
    async listLanguages () {
      return DLLApi.listLanguages()
    },
    // 预览 Dll 词汇
    toPreviewDll (index = 0) {
      // 判断是否有 DLL 内容
      if (this.dlls.length === 0) {
        this.$message.error(this.$t('loc.plan36'))
        return
      }
      this.$refs.dllHomeworkDemoDialog && this.$refs.dllHomeworkDemoDialog.show(this.dlls, index,this.title)
    },
    // 添加关键词
    handleClickAdd () {
      // 操作类型为添加
      this.operateType = 'add'
      this.disabledOperate = false
      // 重置表单校验
      this.resetForm()
      // 显示弹窗
      this.addKeyVocabulariesDialogVisible = true
      // 重置图片信息
      this.attach = { medias: [], mediakeys: [] }
      // 翻译结果列表
      this.translations = []
    },
    handleClickDelete (dll, index) {
      const h = this.$createElement
      this.$msgbox({
        title: this.$t('loc.confirmation'),
        message: h('p', null, [
          h('span', null, this.$t('loc.dll8'))
        ]),
        showCancelButton: true,
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        confirmButtonClass: 'el-button--danger',
        cancelButtonClass: 'is-plain',
        customClass: 'remove-vocabularies',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            this.dlls.splice(index, 1)
            this.initDlls()
            done()
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },
    // 编辑关键词
    editVocabularies (item, index) {
      this.ruleForCmontentRecord=item.content
      this.currentIndex = index
      this.operateType = 'edit'
      this.disabledOperate = false
      this.addKeyVocabulariesDialogVisible = true
      this.ruleForm = {
        ...item
      }
      // 如果关键词图片存在时不显示添加图片按钮
      if (item.media) {
        this.uploadHavePlus = false
      }
      this.attach = {
        medias: item.media ? [item.media] : [],
        mediakeys: item.media ? [item.media.id] : []
      }
      this.translations = item.languages.map(language => {
        return {
          ...language,
          originalName: language.content,
          name: language.content
        }
      })
    },
    mouseEnterItem (item) {
      this.$set(item, 'edit', true)
    },
    mouseLeaveItem (item) {
      this.$set(item, 'edit', false)
    },
    // 上传照片
    fileUpload (content, question) {
      // 上传图片，把上传的站位图隐藏
      this.uploadHavePlus = false
      // 展示上传图片的进度条
      this.fileUploadLoading = true
      let file = content.file
      let param = new FormData()
      // 通过append向form对象添加数据
      // param.append('file', content.file)
      param.append('file', file)
      param.append('type', 'jpg') // 这里要添加传递文件的类型，图片传jpg,视频传mp4，录音文件传aac
      // FormData私有类对象，访问不到，可以通过get判断值是否传进去
      const configs = {
        baseURL: configBaseUrl,
        headers: {
            'Content-Type': 'multipart/form-data',
            'X-UID': store.state.user.uid
          }
      }
      axios.post($api.urls().uploadFile, param, configs)
        .then(res => {
          this.fileUploadLoading = false
          content.onSuccess(res.data, question)
        }).catch(error => {
        if (error.response) {
          content.onError()
          this.$message.error(error.response.data.error_message)
          this.fileUploadLoading = false
          // 上传图片失败，把上传的站位图显式出来
          this.uploadHavePlus = true
        }
      })
    },
    // 图片上传到服务器成功后
    uploadSuccess (res, file, fileList, question) {
      let medias = question.medias || []
      file.fileKey = res.id // 文件id
      medias.push(file)
      question.medias = medias
      // key
      let mediaKeys = question.mediaKeys || []
      mediaKeys.push(res.id)
      question.mediaKeys = mediaKeys
      // 修改只能选择一种图片
      if (medias.length >= 1) {
        this.uploadHavePlus = false
      }
    },
    upAttachChange (file, fileList, question) {
      this.hideAttachUploadEdit = fileList.length >= 1
    },
    // 删除图片的操作
    handleRemove (file, question) {
      let medias = []
      let mediaKeys = []
      for (let media of question.medias) {
        if (media.fileKey !== file.fileKey) {
          medias.push(media)
          mediaKeys.push(media.fileKey)
        }
      }
      question.medias = medias
      question.mediaKeys = mediaKeys
      // 修改只能选择一种图片
      if (medias.length < 1) {
        this.uploadHavePlus = true
      }
    },
    // 显示更多
    showMore () {
      // 如果点击查看更多，则显示全部
      this.initMaxLimit = this.dlls
      // 隐藏查看更多
      this.isAppear = false
    },
    // 收起
    showLess () {
      this.initMaxLimit = this.dlls.slice(0, 8)
      this.isAppear = true
    },
    // 查看大图的操作
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 上传图片之前的判断
    beforeAvatarUpload (file) {
      let supportImgFormat = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/JPG', 'image/JPEG', 'image/PNG', 'image/GIF']
      let isSupportImgFormat = supportImgFormat.indexOf(file.type) > -1
      let isLt10M = file.size / 1024 / 1024 < 10
      if (!isSupportImgFormat) {
        this.$message.error(this.$t('loc.lessons2UnsupportedFileFormat'))
      }
      if (!isLt10M) {
        this.$message.error(this.$t('loc.dllImageUploadTips'))
      }
      return isSupportImgFormat && isLt10M
    },
    handleClose () {
      // 清除校验
      this.$refs['ruleForm'].resetFields()
      // 清除表单数据
      this.attach.medias = []
      this.attach.mediaKeys = []
      // 关闭弹窗
      this.addKeyVocabulariesDialogVisible = false
      this.uploadHavePlus = true
    },
    submitAddKeyVocabularies(){
      // 关闭弹框
      this.editTipVisibleDialog = false
      // 重置单词和翻译状态
      this.translateOfContent = true
      const that = this
      const response = that.attach.medias && that.attach.medias.length > 0 && (that.attach.medias[0].response || that.attach.medias[0])
      // 封装表单数据
      const newDll = {
        ...that.ruleForm,
        languages: that.translations.map(translate => {
          return {
            ...translate,
            lang: translate.name,
            lang_en: translate.originalName
          }
        }),
        media: response ? {
          ...response,
          url: response.public_url || response.url
        } : {},
        medias: response ? [
          {
            ...response,
            mediaId: response.id,
            mediaUrl: response.public_url || response.url,
            fileName: response.fileName || response.name
          }
        ] : []
      }
      // 判断是添加还是编辑, 如果是编辑，则将对应的数据替换
      if (that.operateType === 'edit') {
        that.dlls.splice(that.currentIndex, 1, newDll)
      } else {
        // 禁用操作按钮
        this.disabledOperate = true
        // 如果是添加，则直接操作
        that.dlls.push(newDll)
      }
      // 触发父组件的回调
      this.$emit('reopenDllData', that.dlls)
      // 重置索引下标
      that.currentIndex = 0
      // 关闭弹窗
      that.addKeyVocabulariesDialogVisible = false
      that.attach.medias = []
      that.attach.mediaKeys = []
      that.uploadHavePlus = true
    },
    handleAddKeyVocabularies () {
      // 校验表单
      this.$refs.ruleForm.validate(valid => {
        // 判断表单是否能够校验通过
        if (valid && this.translations.length !== 0) {
          if (!this.translateOfContent) {
            // 验证不通过，打开弹框
            this.editTipVisibleDialog = true
            return
          }
          this.submitAddKeyVocabularies()
        } else {
          this.$message.error(this.$t('loc.noTranslateByAdd'))
        }
      })
    },
    // 翻译关键词
    translateVocabularies () {
      this.translating = true
      DLLApi.translate(this.ruleForm.content, this.langCodes)
        .then(data => {
          this.translations = data
          let prev = this.needTranslation.find(item => item.id === this.dll.sortIndex && item.reason === 'content')
          if (prev) {
            this.needTranslation.splice(this.needTranslation.indexOf(prev), 1)
          }
          // 重置单词翻译对应状态
          this.translateOfContent = true
          // 重新记录单词
          this.ruleForCmontentRecord=this.ruleForm.content
        })
        .catch(error => {
          this.$message.error(error.message)
        })
        .finally(() => {
          this.translating = false
        })
    },
    resetForm () {
      this.ruleForm = {
        title: this.title, // DLL 的标题
        content: '', // DLL 的内容
        languages: [], // DLL 的选择语言
        media: null, // DLL 的图片
        edit: false,
        description: '' // DLL 关键词描述
      }
    }

  }
}
</script>

<style scoped lang="less">
.bottom-content {
  height: 54px;
  line-height: 54px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.dll-translate {
  margin-bottom: 10px;
  /deep/ .el-card__header {
    padding: 0px 13px!important;
  }
  /deep/ .el-card__body {
    padding-top: 0px!important;
  }
}
/deep/ .el-textarea .el-input__count {
  line-height: 20px!important;
  position: absolute;
  right: 5px;
  display: flex;
  width: 98%;
  justify-content: end;
  background: var(--color-white) !important;
  bottom: 1px;
}
.el-card {
  background: unset!important;
}
.el-card /deep/ & {
  & > .el-card__header {
    // background-color: #FCBA4C;
    padding: 0px;
    min-height: 40px;
    border-bottom: unset!important;
  }

  & > .el-card__body {
    line-height: initial;
    padding: 20px 0 0 0;
  }

  .el-form-item {
    margin-bottom: 0 !important;

    & > .el-form-item__label {
      display: grid;
      align-items: center;
      grid-template-areas: "a b c";
      grid-template-columns: max-content 1fr max-content;

      .el-link {
        grid-area: c;
        padding: 0;
        color: #909399;
        justify-self: end;
      }
    }
  }

  .dll-input-item {
    display: grid;
    grid-template-columns: max-content 1fr;
    gap: 20px;
  }
}
.dll-header {
  margin-top: 3px;
  padding-right: 3px;
}
.dll-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .dll-item {
    margin-right: 24px;
    width: calc((100% - 75px) / 4);
    margin-bottom: 20px;
    .dll-cover {
      border-radius: 4px;
      height: 134px;
      object-fit: cover;
      object-fit: cover;
    }
    .dll-cover-container {
      border-radius: 4px;
      height: 134px;
      background-color: #E7E7E7;
      font-size: 24px;
      color: var(--color-text-primary);
      font-weight: bolder;
      line-height: 134px;
      text-align: center;
    }
  }
  .dll-item:nth-of-type(4n+0) {
    margin-right: 0;
  }
}
/deep/ .el-dialog__body {
  overflow: auto;
  height: calc(100% - 130px);
}
.title-input {
  font-size: 14px;
  color: #303133;
}
.content-input {
  font-size: 14px;
  color: #303133;
}

.translation-div {
  border: #eeeeee solid 1px;
  border-radius: 4px;
}

.show-language-div {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background: #F4F7FA;
  align-items: center;
  padding: 8px;
}

.show-language-title {
  color: #303133;
  font-weight: Bold;
  font-size: 16px;
}

.show-language-play {
  font-size: 14px;
  color: #10B3B7;
  text-align: center;
}

.translation-input {
  color: #606266 !important;
  font-size: 14px !important;
}
.edit-operate:hover {
  color: var(--color-primary);
}
.remove-operate:hover {
  color: var(--color-danger);
}
/deep/ .el-textarea {
  textarea {
    padding-bottom: 30px;
  }
}
</style>
<style>
.add-dll-dialog-class {
  margin-bottom: 0 !important;
}

.add-dll-dialog-class .el-dialog__footer {
  padding: 0px 14px 24px;
}

.add-dll-dialog-class .el-dialog__header {
  font-size: 20px;
  padding: 22px 24px 12px 14px;
}

.add-dll-dialog-class .el-dialog__header .el-dialog__title {
  font-size: 20px !important;
  color: #111c1c !important;
}

.add-dll-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}

.add-dll-dialog-class .el-dialog__body {
  padding: 0 14px 20px !important;
  max-height: 605px;
  overflow-y: auto;
}
.select-language-input .el-input__inner {
  color: #10B3B7;
  font-size: 14px;
  cursor: pointer;
}

.form-add-dll .el-form-item {
  margin-bottom: 15px !important;
}

.form-add-dll .el-form-item .el-form-item__label {
  color: #111c1c !important;
  font-size: 16px !important;
  line-height: 25px !important;
  font-weight: 600;
  font-family: 'Inter';
  padding: 0;
}

.form-add-dll .el-form-item .el-form-item__error {
  padding-top: 0px;
  padding-left: 0 !important;
}

.translation-input .el-textarea__inner {
  border: none;
}

.content-input .el-textarea__inner {
  min-height: 90px !important;
  overflow-x: hidden;
}
/*将 dll 弹窗内滚动条的宽度设置为0px，以达到视觉隐藏的效果*/
.add-dll-dialog-class ::-webkit-scrollbar {
  width: 0px;
}

/*定义滚动条轨道 内阴影+圆角*/
.add-dll-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.add-dll-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}

.add-dll-upload .el-upload-list__item-thumbnail {
  width: 90px;
  height: 90px;
}

.add-dll-upload .el-upload-list__item {
  height: 90px;
  width: 90px;
  /*去掉加载图片的动画*/
  transition: none !important;
  display: inline-flex;
  margin: 0 !important;
}

.add-dll-upload-plus .el-upload--picture-card {
  height: 90px;
  width: 90px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.add-dll-upload-no-plus .el-upload--picture-card {
  height: 90px;
  width: 90px;
  display: none;
}

.show-edit-tip .el-dialog__body {
  padding: 10px 20px !important;;
}

.div-choose-date .el-radio-group label {
  margin-bottom: 0px !important;
}

.lg-pa-down-0 {
  padding-bottom: 0px;
  height: 52px;
}
.gap-8 {
  gap: 8px;
}
.hr-color-border-height {
  background-color: #dcdfe6;
  height: 1px;
  border: none;
}
.bg-f5f6f8 {
  background-color: #F5F6F8;
}
.vocabularies-body {
  max-width: 75%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.vocabularies-content {
  white-space: pre;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
<style>
.add-dll-dialog-class {
  margin-bottom: 0 !important;
}

.add-dll-dialog-class .el-dialog__footer {
  padding: 10px 14px 24px;
}

.add-dll-dialog-class .el-dialog__header {
  font-size: 20px;
  padding: 22px 24px 12px 14px;
}

.add-dll-dialog-class .el-dialog__header .el-dialog__title {
  font-size: 20px !important;
  color: #111c1c !important;
}

.add-dll-dialog-class .el-dialog__header .el-dialog__headerbtn {
  top: 17px;
}

.add-dll-dialog-class .el-dialog__body {
  padding: 0 14px 20px !important;
  max-height: 605px;
  overflow-y: auto;
}
/*将 dll 弹窗内滚动条的宽度设置为0px，以达到视觉隐藏的效果*/
.add-dll-dialog-class ::-webkit-scrollbar {
  width: 0px;
}

/*定义滚动条轨道 内阴影+圆角*/
.add-dll-dialog-class ::-webkit-scrollbar-track {
  background-color: white;
}

/*定义滑块 内阴影+圆角*/
.add-dll-dialog-class ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: rgba(0, 0, 0, 0.1);
}
.remove-vocabularies {
  position: absolute!important;
  left: 50%!important;
  top: 20%!important;
  transform: translate(-50%)!important;
}
</style>
