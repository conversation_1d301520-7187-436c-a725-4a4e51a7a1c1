<template>
  <el-card shadow="never">
    <template slot="header">
      <span>
        <span style="font-weight: bolder">{{ translation.originalName }}</span>
        <span class="font-normal m-l-xs">({{ translation.name }})</span>
      </span>
      <dll-speaker :content="content" :lang-code="translation.langCode"/>
    </template>
    <el-input v-model="content" type="textarea" maxlength="2000" :autosize="{minRows:2}"
              :class="['text-textarea',{'has-error':error}]"/>
    <div style="color: #f56c6c;" v-if="error">{{ error }}</div>
  </el-card>
</template>

<script>
import DllSpeaker from "@/views/modules/lesson2/lessonLibrary/editor/dll/DllSpeaker";

export default {
  name: "DllTranslation",
  components: {
    DllSpeaker
  },
  props: [
    'translation'
  ],
  data() {
    return {
      content: '',
      error: null,
      changed: false,
    }
  },
  watch: {
    'translation.content': {
      immediate: true,
      handler(value) {
        this.content = value;
      }
    },
    content(value) {
      if (value && value.trim()) {
        this.error = null;
      } else {
        this.error = this.$t('loc.fieldReq');
      }
      if (!this.equalIgnoreEndSpace(this.translation.content, value)) {
        this.changed = true;
        this.$emit('change');
      }
      this.translation.content = value;
    }
  },
  methods: {
    validate() {
      if (!this.content || !this.content.trim()) {
        this.error = this.$t('loc.fieldReq');
        return {status: false}
      }
      return {status: true}
    },
    equalIgnoreEndSpace(value1, value2) {
      value1 = value1 || '';
      value2 = value2 || '';
      if (value1 === value2) {
        return true;
      }
      return value1.trim() === value2.trim();
    }
  }
}
</script>

<style scoped lang="less">
.el-card /deep/ & {
  border: none;
  border-radius: unset;
  background-color: transparent;

  & > .el-card__header {
    border: 1px solid #ebeef5;
    border-bottom: none;
    padding: 0 15px;
    background-color: #fafdfe;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 40px;
    border-radius: 4px 4px 0 0;
  }

  & > .el-card__body {
    padding: 0;
    line-height: initial;
  }
}

.text-textarea /deep/ .el-textarea__inner {
  border-color: #ebeef5;
  border-radius: 0 0 4px 4px;
}

.has-error /deep/ .el-textarea__inner {
  border: 1px solid #f56c6c;
}
</style>