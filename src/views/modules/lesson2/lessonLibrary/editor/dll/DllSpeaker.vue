<template>
  <el-button type="text" @click="clickHandler">
    <template v-if="playStatus===0">
      <span class="show-play-icon" style="display: inline-block;margin-right: 3px"/>
      <span>{{ $t('loc.play') }}</span>
    </template>
    <i v-if="playStatus === 1" class="el-icon-loading"/>
    <template v-if="playStatus===2">
      <span class="voice-playing" style="display: inline-block;margin-right: 3px;"/>
      <span>{{ $t('loc.play') }}</span>
    </template>
  </el-button>
</template>

<script>
import DLLApi from "@/api/dll/DLL";
export default {
  name: "DllSpeaker",
  props: [
    'langCode',
    'content'
  ],
  data() {
    return {
      playStatus: 0, // 播放状态：0 正常，1生成音频资源中，2播放中
      audio: null, // 播放器
      url: null, // 音频资源 URL
      destroyed: false
    }
  },
  watch: {
    langCode() {
      this.reset();
    },
    content() {
      this.reset();
    }
  },
  beforeDestroy() {
    this.audio && this.audio.pause();
    this.audio = null;
    this.destroyed = true;
  },
  methods: {
    // 重置播放器
    reset() {
      this.audio && this.audio.pause();
      this.audio = null;
      this.url = null;
      this.playStatus = 0;
    },
    // 语音按钮事件处理
    async clickHandler() {
      let {langCode, content} = this;
      if(!content || !content.trim()){
        this.$message.error("Translations cannot be empty");
        return;
      }
      let mediaId = ++window.lessonVideoId;
      // 未生成音频资源则请求后端生成
      if (!this.url) {
        try {
          this.playStatus = 1;
          let response = await DLLApi.tts(content, langCode);
          let {voiceUrl} = response;
          this.url = voiceUrl;
        } catch (error) {
          error.message && this.$message.error(error.message);
          return;
        } finally {
          this.playStatus = 0;
        }
      }
      // 未播放则开始播放
      if (this.playStatus === 0 && !this.destroyed) {
        let previous = window.lessonVideo;
        if (previous) {
          previous.pause();
        }
        // 不考虑 seek，所以始终重新创建 audio
        this.audio = window.lessonVideo = new Audio(this.url);
        this.audio.addEventListener('canplay', () => {
          this.audio.play();
        });
        this.audio.addEventListener('play', () => {
          this.playStatus = 2;
        });
        this.audio.addEventListener('pause', () => {
          this.playStatus = 0;
        })
        return;
      }
      // 播放中则停止播放
      if (this.playStatus === 2) {
        this.audio.pause();
      }
    },
    destroy(){
      this.audio.pause();

    }
  }
}
</script>

<style scoped lang="less">
.el-button /deep/ & {
  padding: 0;
  & > span:last-child {
    display: inline-flex;
    align-items: center;
  }
}
</style>