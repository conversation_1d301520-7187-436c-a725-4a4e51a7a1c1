<template>
  <div class="dll-language-select">
    <el-input suffix-icon="el-icon-arrow-down" readonly :placeholder="$t('loc.lessons2LessonDLLLanguageSelect')"
              @click.native="clickInputHandler"
              class="w-full select-language-input" v-model="valueShowed">
    </el-input>
    <!-- DLL 中选择语言的弹框 -->
    <el-dialog custom-class="div-select-language"
               append-to-body
               :title="$t('loc.selectYourLanguage')" width="550px"
               :visible.sync="showDialog" :close-on-click-modal=false :lock-scroll="false" top="8vh">
      <div class="scrollbar-new">
        <!-- 搜索框 -->
        <div style="padding: 20px 20px">
          <el-input class="w-full el-check-hidden" :placeholder="$t('loc.searchLanguage')" suffix-icon="el-icon-search"
                    v-model="keyword"/>
        </div>
        <!-- 已选择语言 -->
        <template v-if="selected.length">
          <div class="theme-background line-height-28 height-28" style="padding-left: 20px;">
            {{ $t('loc.selectedLanguages') }}
          </div>
          <transition-group name="drag" class="selected-list dll-text-title-color font-size-16" tag="ul">
            <li v-for="(item,index) in selected" :key="item.code" draggable="true" class="dll-language-item"
                @dragenter.prevent="dragenter($event, index)" @dragover.prevent @dragstart="dragIndex = index">
              <img src="@/assets/img/dll/icon_subtraction.png" class="lg-pointer" alt="" style="grid-area: a;"
                   @click.stop="unselect(item,index)"/>
              <span class="lg-pointer" style="grid-area: b" @click.stop="unselect(item,index)">
                {{ item.name }}
              </span>
              <span class="lg-pointer" style="grid-area: d" @click.stop="unselect(item,index)">
                {{ item.originalName }}
              </span>
              <img src="@/assets/img/dll/menu.png" alt="" style="cursor: move;grid-area: c"/>
            </li>
          </transition-group>
        </template>
        <!-- 未选语言 -->
        <template v-if="unselected.length">
          <div class="theme-background line-height-28 height-28" style="padding-left: 20px;" v-if="selected.length">
            {{ $t('loc.otherLanguages') }}
          </div>
          <div v-for="(item,index) in unselected" :key="item.code" class="display-flex flex-direction-col">
            <div @click.stop="select(item,index)" class="lg-pointer dll-language-item dll-text-title-color font-size-16">
              <img src="@/assets/img/dll/icon_add.png" alt="" style="grid-area: a;">
              <div style="grid-area: b">{{ item.name }}</div>
              <div style="grid-area: d">{{ item.originalName }}</div>
            </div>
            <el-divider class="m-n" style="background-color: #eee;"/>
          </div>
        </template>
      </div>
      <span slot="footer" class="close-footer">
        <el-button @click="showDialog = false" plain>{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="confirm()" :disabled="!selected.length">{{ $t('loc.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DLLApi from '@/api/dll/DLL'

export default {
  name: 'DllLanguageSelect',
  props: [
    'value', // 已选语言，字符串数组：langCode
    'languages'// 可选语言，对象数组：code 、name、originalName、ttsCode、ttsVoice
  ],
  computed: {
    valueShowed () {
      if (!this.value || !this.value.length) {
        return ''
      }
      return this.$t('loc.selectedLanguageNum', { num: this.value.length })
    }
  },
  data () {
    return {
      showDialog: false, // 是否展示语言弹框
      keyword: '', // 语言搜索
      selected: [], // 已经选择的语言集合
      unselected: [], // 未选择的语言集合
      dragIndex: '' // 要拖拽源对象的位置
    }
  },
  watch: {
    keyword (value) {
      value = value.trim()
      this.unselected = (this.languages || [])
        .filter(item => !this.selected.includes(item) && this.matchKeyword(item, value))
    },
    value: {
      immediate: true,
      handler (value) {
        value = value || []
        let selected = value.map(code => this.languages.find(language => language.code === code))
        let unselected = this.languages.filter(item => !value.includes(item.code) && this.matchKeyword(item, this.keyword))
        if (!this._arrayEquals(selected, this.selected)) {
          this.selected = selected
        }
        if (!this._arrayEquals(unselected, this.unselected)) {
          this.unselected = unselected
        }
      }
    }
  },
  methods: {
    clickInputHandler () {
      let value = this.value || []
      this.selected = value.map(code => this.languages.find(language => language.code === code))
      this.keyword = ''
      this.unselected = this.languages.filter(item => !value.includes(item.code))
      this.showDialog = true
    },
    // 语言搜索匹配算法
    matchKeyword (language, keyword) {
      if (!keyword) {
        return true
      }
      keyword = keyword.toLowerCase()
      let { name = '', originalName = '' } = language || {}
      return !![name, originalName].find(item => item.toLowerCase().includes(keyword))
    },
    // 已选语言拖拽排序
    dragenter (e, index) {
      // 避免源对象触发自身的dragenter事件
      if (this.dragIndex !== index) {
        const source = this.selected[this.dragIndex]
        // 1.先删除要拖拽位置的元素
        this.selected.splice(this.dragIndex, 1)
        // 2.在要插入的位置放置原拖拽的元素
        this.selected.splice(index, 0, source)
        // 排序变化后目标对象的索引变成源对象的索引
        this.dragIndex = index
      }
    },
    // 选中某个语言
    select (item, index) {
      this.selected.push(item)
      this.unselected.splice(index, 1)
    },
    // 取消选中某个语言
    unselect (item, index) {
      this.unselected.push(item)
      this.selected.splice(index, 1)
    },
    // 确认选择的语言
    confirm () {
      if (this.selected.length > 10) {
        this.$message.error(this.$t('loc.limitSelectLanguageNum').toString())
        return
      }
      let langCodes = this.selected.map(item => item.code)
      if (!this._arrayEquals(langCodes, this.value)) {
        this.$emit('input', langCodes)
        DLLApi.setLastUsedLanguages(langCodes.join(','))
      }
      this.showDialog = false
    },
    _arrayEquals (arr1, arr2) {
      arr1 = arr1 || []
      arr2 = arr2 || []
      return arr1.length === arr2.length && !arr1.find((item, index) => item !== arr2[index])
    }
  }
}
</script>

<style lang="less" scoped>
.selected-list {
  list-style: none;

  .drag-move {
    transition: transform .3s;
  }
}

.close-footer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.height-28 {
  height: 28px;
}

/deep/ .div-select-language .el-dialog__header {
  border-bottom: 1px solid #EEEEEE;
  padding: 10px 20px;

  & > .el-dialog__headerbtn {
    top: 10px;
  }
}

/deep/ .div-select-language .el-dialog__footer {
  border-top: 1px solid #EEEEEE;
  padding: 10px 20px;
}

/deep/ .div-select-language .el-dialog__body {
  padding: 0;
}

/deep/ .select-language-input .el-input__inner {
  color: #10B3B7;
  font-size: 14px;
}

.scrollbar-new {
  max-height: 420px;
  min-height: 200px;
  overflow-y: auto;
}

.dll-language-item {
  display: grid;
  grid-template-areas: 'a b c'
                       'a d c';
  grid-template-columns: max-content 1fr max-content;
  align-items: center;
  padding: 10px 16px;
  column-gap: 8px;
  line-height: 1.42857143;
}

.dll-language-select {
  display: inline-block;
}

.el-check-hidden /deep/ .el-input__validateIcon{
  display: none;
}
</style>
