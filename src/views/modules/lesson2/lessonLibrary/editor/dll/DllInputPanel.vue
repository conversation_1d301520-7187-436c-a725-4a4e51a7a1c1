<template>
  <div class="dll-input-panel">
    <!-- DLL 图片-->
    <media-uploader :tips="$t('loc.lessons2SupportImage')" accept=".png,.jpg,.jpeg" :showUrlBtn="false" v-model="dll.media"
                    @change="$emit('change')"
                    class="media-uploader-small"/>
    <div style="display: grid;gap: 10px;">
      <!-- DLL 内容-->
      <el-form-item :label="$t('loc.lessons2LessonDLLAndTitle')" :error="error" ref="content">
        <el-input
          type="textarea"
          :autosize="{minRows:5}"
          v-model="dll.content"
          maxlength="200"
          show-word-limit
          :placeholder="$t('loc.addDLLContentTip')"
          @change="handleContentChange"/>
      </el-form-item>
      <el-form-item :label="$t('loc.dll7')" :error="error" ref="content">
        <el-input
          type="textarea"
          :autosize="{minRows:5}"
          v-model="dll.description"
          :placeholder="$t('loc.dll5')"
          maxlength="200"
          show-word-limit
          @change="handleContentChange"/>
      </el-form-item>
      <!-- 翻译按钮 -->
      <el-button type="primary" :loading="translating" :disabled="!dll.content || !dll.content.trim()"
                 @click="translate" style="place-self: end right;">
        {{ $t('loc.translate') }}
      </el-button>
      <!-- 翻译结果列表 -->
      <dll-translation v-for="(item,index) in translations" :key="item.langCode" :translation="item"
                       :ref="`translation${index}`" @change="$emit('change')"/>
    </div>
  </div>
</template>

<script>
import MediaUploader from "@/views/modules/lesson2/component/mediaUploader";
import DLLApi from "@/api/dll/DLL";
import DllTranslation from "@/views/modules/lesson2/lessonLibrary/editor/dll/DllTranslation";
import {deepEqual} from "@/utils/common";

export default {
  name: "DllInputPanel",
  components: {
    DllTranslation,
    MediaUploader
  },
  props: [
    'languages', // 语言信息
    'langCodes', // DLL 语言
    'value', // DLL
  ],
  inject: {
    validators: {
      from: 'validators',
      default: []
    },
    needTranslation: {
      from: 'needTranslation',
      default: []
    },
  },
  data() {
    return {
      dll: {
        media: null,
        content: null,
        languages: null,
        description: ''
      },
      translating: false,// 是否翻译中
      translations: [], // 翻译结果
      error: null
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(value) {
        if (deepEqual(this.dll, value)) {
          return;
        }
        value = value || {};
        let {languages = []} = this.dll = JSON.parse(JSON.stringify(value));
        this.translations = languages.map(item => {
          let translation = {...item};
          let language = this.languages.find(temp => temp.code === translation.langCode);
          if (language) {
            Object.assign(translation, language);
          }
          return translation;
        });
      }
    },
    dll: {
      deep: true,
      handler(value) {
        if (!deepEqual(value, this.value)) {
          this.$emit('input', value);
        }
      }
    },
    translations: {
      deep: true,
      handler(value) {
        value = value || [];
        this.dll.languages = value.map(item => ({langCode: item.langCode, content: item.content}))
      }
    }
  },
  created() {
    this.validators.push(this.validate);
  },
  methods: {
    translate() {
      this.translating = true;
      DLLApi.translate(this.dll.content, this.langCodes)
        .then(data => {
          this.translations = data;
          let prev = this.needTranslation.find(item => item.id === this.dll.sortIndex && item.reason === 'content');
          if (prev) {
            this.needTranslation.splice(this.needTranslation.indexOf(prev), 1);
          }
        })
        .catch(error => {
          this.$message.error(error.message)
        })
        .finally(() => {
          this.translating = false
        })
    },
    validate() {
      if (!this.dll.content || !this.dll.content.trim()) {
        return {status: true, dll: this.value}
      }
      for (const translation of this.translations) {
        let ref = `translation${this.translations.indexOf(translation)}`;
        let instance = this.$refs[ref];
        if (Array.isArray(instance)) {
          instance = instance[0];
        }
        let {validate} = instance || {};
        let {status = true} = validate && validate() || {};
        if (!status) {
          return {status: false, dll: this.value, el: instance.$el}
        }
      }
      return {status: true, dll: this.value}
    },
    handleContentChange(newContent, oldContent) {
      if (this.dll.content && this.dll.content.trim()) {
        this.error = null;
      }
      let languages = this.value.languages || [];
      if (languages.length) {
        let prev = this.needTranslation.find(item => item.id === this.dll.sortIndex && item.reason === 'content');
        if (!prev) {
          this.needTranslation.push({id: this.dll.sortIndex, reason: 'content'})
        }
      }
      !this.equalIgnoreEndSpace(newContent, oldContent) && this.$emit('change')
    },
    equalIgnoreEndSpace(value1, value2) {
      value1 = value1 || '';
      value2 = value2 || '';
      if (value1 === value2) {
        return true;
      }
      return value1.trim() === value2.trim();
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-textarea .el-input__count {
  background: transparent!important;
  line-height: 20px!important;
}
.el-form-item {
  margin-bottom: 0 !important;
  width: 100%;
  line-height: initial;
}

.dll-input-panel {
  gap: 20px;
  display: grid;
  grid-template-columns: max-content 1fr;
  background: #f4f6f8;
}

.media-uploader-small {
  background: #e7e7e7;
}
</style>