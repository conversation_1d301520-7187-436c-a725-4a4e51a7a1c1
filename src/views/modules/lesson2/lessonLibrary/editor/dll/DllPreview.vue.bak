<template>
  <div class="dll-preview">
    <div style="justify-content: flex-end;gap:10px;" class="m-t-md flex-align-center">
      <span>{{ $t('loc.selectLanguage') }}:</span>
      <dll-language-select v-model="langCodes" :languages="languages" v-if="languages"/>
      <el-button type="primary" @click="handlePreview">{{ $t('loc.lessons2LessonDLLTranslate') }}</el-button>
    </div>
    <el-dialog :title="`${$t('loc.lessons2LessonDLLTitle')}`"
               :visible.sync="showDialog"
               :width="dialogWidth"
               top="8vh"
               :append-to-body="true"
               :modal-append-to-body="false"
               :close-on-click-modal="false"
               :before-close="handleBeforeClose"
               @close="handleClose">
      <dll-input-panel v-model="dlls[0]" :languages="languages" v-if="dlls.length ===1" :lang-codes="langCodes"
                       class="scrollbar-new" @change="handleChange"/>
      <el-tabs v-model="tabName" v-else :before-leave="handleLeaveTab">
        <el-tab-pane v-for="(dll,index) in dlls" :label="`${name} ${index+1}`" :name="`${name} ${index+1}`">
          <el-button :type="tabName===`${name} ${index+1}`?'primary':''" slot="label">
            <span>{{ `${name} ${index + 1}` }}</span>
          </el-button>
          <dll-input-panel v-model="dlls[index]" :languages="languages" :lang-codes="langCodes"
                           class="scrollbar-new" v-if="tabName === `${name} ${index+1}`"
                           @change="handleChange"/>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" style="text-align: right">
        <el-button @click="handleClickCancel" plain>{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" @click="confirm()" :loading="loading">{{ $t('loc.save') }}</el-button>
      </div>
    </el-dialog>
    <!--确认离开弹框-->
    <el-dialog :visible.sync="showConfirmLeave"
               width="450px"
               top="15vh"
               append-to-body
               :close-on-click-modal="false"
               @close="handleCloseConfirm">
      <div slot="title" style="height: 15px;font-size: 20px">{{ $t('loc.confirmation') }}</div>
      <span class="font-size-16">{{ $t('loc.lessons2LessonDLLLeaveConfirmation') }}</span>
      <div slot="footer">
        <el-button @click="confirmCloseModel = 'leave without save'; showConfirmLeave = false"
                   class="text-base" style="padding: 10px 15px;">
          {{$t('loc.exitWithoutSaving')}}
        </el-button>
        <el-button type="primary"
                   @click="confirmCloseModel = 'save and leave'; showConfirmLeave = false;"
                   class="text-base m-l-md"
                   style="padding: 10px 15px;">
          {{ $t('loc.saveAndExit') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {deepEqual} from "@/utils/common";
import DllInputPanel from "@/views/modules/lesson2/lessonLibrary/editor/dll/DllInputPanel";
import DLLApi from "@/api/dll/DLL";
import DllLanguageSelect from "@/views/modules/lesson2/lessonLibrary/editor/dll/DllLanguageSelect";

export default {
  name: "DllPreview",
  components: {
    DllLanguageSelect,
    DllInputPanel
  },
  props: [
    'value',
    'title',
    'needTranslation'
  ],
  computed: {
    name() {
      return this.$t('loc.lessons2LessonDLLTabTitle');
    }
  },
  data() {
    return {
      langCodes: [], // 选择的语言
      languages: null, // 可选语言
      showDialog: false, // 是否展示预览
      dlls: [],
      previewedDlls: [],
      tabName: null,
      validators: [],
      loading: false,
      innerNeedTranslation: [],
      closeModel: 'other',
      changed: false,
      showConfirmLeave: false,
      confirmResolver: null,
      confirmCloseModel: 'other',
      dialogWidth: '1030px', //可视宽度是否大于iPad
    }
  },
  provide() {
    return {
      validators: this.validators, // input panel 校验器
      needTranslation: this.innerNeedTranslation
    }
  },
  async created() {
    let {languages, used} = await this.listLanguages();
    this.languages = languages;
    this.langCodes = used;
    this.$watch('value', value => {
      if (!deepEqual(value, this.dlls)) {
        this.dlls = JSON.parse(JSON.stringify(value));
        // this.langCodes = this.getLangCodes(this.dlls, used);
        this.tabName = `${this.name} 1`;
        this.changed = false;
      }
    }, {immediate: true, deep: true})
    this.$watch('langCodes', value => {
      let langCodes = this.getLangCodes(this.dlls);
      let needTranslation = this.needTranslation.find(item => item.reason === 'language');
      let dllLanguageOK = deepEqual(langCodes, value);
      if (!dllLanguageOK && !needTranslation) {
        this.needTranslation.push({reason: 'language'});
      } else if (dllLanguageOK && needTranslation) {
        this.needTranslation.splice(this.needTranslation.indexOf(needTranslation), 1);
      }
    });
    let width = document.body.clientWidth;
    if (width < 1199){
      this.dialogWidth = '850px'
    }
  },
  methods: {
    getLangCodes(dlls, used) {
      let langCodes = [];
      for (const dll of dlls) {
        let temp = dll.languages && dll.languages.map(lang => lang.langCode) || [];
        temp.length > langCodes.length && (langCodes = temp);
      }
      return langCodes.length && langCodes || used || [];
    },
    async handlePreview() {
      if (!this.langCodes.length) {
        this.$message.error(this.$t('loc.lessons2SelectLanguageTips').toString());
        return;
      }
      // 对未翻译的 DLL 进行翻译
      this.loading = true;
      await this.translate()
      this.tabName = `${this.name} 1`;
      this.loading = false;
      this.needTranslation.splice(0, this.needTranslation.length);
      this.innerNeedTranslation.splice(0, this.innerNeedTranslation.length);
      this.$emit('input', JSON.parse(JSON.stringify(this.dlls)));
      this.previewedDlls = JSON.parse(JSON.stringify(this.dlls));
      this.closeModel = 'other'
      this.showDialog = true;
      this.changed = false;
    },
    async listLanguages() {
      return DLLApi.listLanguages();
    },
    confirm() {
      if (!this.validate()) {
        return;
      }
      let dlls = JSON.parse(JSON.stringify(this.dlls));
      dlls.forEach(dll => {
        if (!dll.content || !dll.content.trim()) {
          dll.languages = [];
        }
      })
      this.$emit('input', dlls);
      if (this.innerNeedTranslation.length) {
        this.needTranslation.push(...this.innerNeedTranslation);
      }
      this.closeModel = 'saved'
      this.showDialog = false;
    },
    validate() {
      for (const validator of this.validators) {
        let {status, dll, el} = validator();
        if (!status) {
          this.dlls.length > 1 && (this.tabName = `${this.name} ${this.dlls.indexOf(dll) + 1}`)
          el && this.$nextTick(() => {
            if (!this.isInViewPort(el)) {
              el.scrollIntoView(true);
            }
          })
          return false;
        }
      }
      return true;
    },
    isInViewPort(element) {
      if (!element) {
        return false;
      }
      const viewWidth = window.innerWidth || document.documentElement.clientWidth;
      const viewHeight = window.innerHeight || document.documentElement.clientHeight;
      const {
        top,
        right,
        bottom,
        left,
      } = element.getBoundingClientRect();

      return (
        top >= 0 &&
        left >= 0 &&
        right <= viewWidth &&
        bottom <= viewHeight
      );
    },
    async translate() {
      let {dlls, langCodes} = this;
      dlls = JSON.parse(JSON.stringify(dlls));
      let contents = {};// 需要翻译的内容
      for (const dll of dlls) {
        if (!dll.content || !dll.content.trim()) { // 忽略无内容的 DLL
          dll.languages = [];
          continue;
        }
        let needTranslation = this.needTranslation.find(item => item.id === dll.sortIndex && item.reason === 'content');
        if (needTranslation) {// 原文被修改，则翻译所有语言
          dll.languages = [];
        }
        let languages = [];
        for (const langCode of langCodes) {
          let language = dll.languages && dll.languages.find(item => item.langCode === langCode) || {langCode};
          languages.push(language);
          if (!language.content || !language.content.trim()) { // 该语言未被翻译
            let langArray = contents[dll.content] = contents[dll.content] || [];
            langArray.push(langCode);
          }
        }
        dll.languages = languages;
      }
      // 进行翻译
      let translations = {};
      let keys = Object.keys(contents);
      let translationList = [];
      for (const content of keys) {
        translationList.push({content, list: DLLApi.translate(content, contents[content])});
      }
      for (const {content, list} of translationList) {
        (await list).forEach(item => {
          translations[content] = translations[content] || {};
          translations[content][item.langCode] = item;
        })
      }
      // 应用翻译结果
      if (keys.length) {
        dlls.forEach(dll => {
          let map = translations[dll.content] || {};
          dll.languages.filter(language => !language.content || !language.content.trim())
            .forEach(language => {
              let {content} = map[language.langCode] || {};
              language.content = content;
            })
        })
      }
      this.dlls = dlls;
      return dlls;
    },
    async handleBeforeClose(done) {
      // 无修改或者保存后，直接关闭弹窗
      if(!this.changed || this.closeModel === 'saved'){
        done();
        return;
      }
      // 弹出确认弹窗
      this.confirmCloseModel = 'other';
      this.showConfirmLeave = true;
      let mode = await new Promise(resolve => {
        this.confirmResolver = resolve;
      });
      // 直接关闭确认弹窗，返回编辑
      if (mode === 'other') {
        this.showDialog = true;
      }else if (mode === 'save and leave') { // 保存，保存失败返回编辑，成功则关闭
        if (!this.validate()) {
          this.showDialog = true;
          return;
        }
        let dlls = JSON.parse(JSON.stringify(this.dlls));
        dlls.forEach(dll => {
          if (!dll.content || !dll.content.trim()) {
            dll.languages = [];
          }
        })
        this.$emit('input', dlls);
        if (this.innerNeedTranslation.length) {
          this.needTranslation.push(...this.innerNeedTranslation);
        }
        done()
      }else if (mode === 'leave without save') { // 不保存
        done();
      }
    },
    handleClose() {
      window.lessonVideo && window.lessonVideo.pause();
    },
    handleLeaveTab() {
      window.lessonVideo && window.lessonVideo.pause();
      return true;
    },
    handleClickCancel() {
      this.closeModel = 'cancel';
      this.handleBeforeClose(()=>{
        this.showDialog = false;
      })
    },
    handleChange() {
      this.changed = true;
    },
    handleCloseConfirm(){
      this.confirmResolver && this.confirmResolver(this.confirmCloseModel);
    }
  }
}
</script>

<style scoped lang="less">
.dll-input-panel {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 16vh - 134px);
}

.dll-preview /deep/ & {
  & > .el-dialog__wrapper > .el-dialog {
    & > .el-dialog__header {
      padding: 20px;
      line-height: initial;
    }

    & > .el-dialog__body {
      padding: 0 !important;

      & > .el-tabs {
        & > .el-tabs__header {
          padding: 0 20px;
          margin-bottom: 0;

          .el-tabs__nav-wrap::after {
            background-color: transparent;
          }

          .el-tabs__item {
            padding: 0 !important;
            border-bottom: none;
          }

          .el-tabs__item:not(:nth-child(2)) {
            .el-button {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
          }

          .el-tabs__item:not(:last-child) {
            .el-button {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }
          }
        }

        & > .el-tabs__content {
          .dll-input-panel {
            max-height: calc(100vh - 16vh - 134px - 41px);
          }
        }
      }
    }
  }
}
</style>