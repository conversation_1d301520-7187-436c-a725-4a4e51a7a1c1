<template>
  <div class="lg-tabs-mini" style="position: absolute; right: 0;">
    <el-tabs v-model="activeName" @tab-click="handleChange">
      <el-tab-pane name="PS">
        <span slot="label">
          DRDP-PS
          <el-tooltip effect="dark" placement="top">
            <span slot="content" v-html="$t('loc.typicalBehaviorsPSTip')"></span>
            <i class="lg-icon lg-icon-question font-weight-400"></i>
          </el-tooltip>
        </span>
      </el-tab-pane>
      <el-tab-pane name="PTKLF">
        <span slot="label">
          CA-PTKLF
        </span>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  name: 'TypicalBehaviorsTab',
  props: {
    value: {
      type: String,
      default: 'PS'
    }
  },
  watch: {
    value (val) {
      this.activeName = val
    }
  },
  data () {
    return {
      activeName: this.value
    }
  },
  methods: {
    handleChange (tab) {
      this.$emit('input', tab.name)
      this.$store.dispatch('setShowMappedTypicalBehaviors', tab.name === 'PS')
    }
  }
}
</script>
