<template>
  <div>
    <!--  如果是编辑模式  -->
    <div v-if="edit" class="quiz-window-container quiz-window-container-edit quiz-edit-font-style"
         @mouseover="quizEditorContainerMouseOverHandle"
         @mouseleave="quizEditorContainerMouseLeaveHandle">
      <!--   头部操作栏   -->
      <div class="quiz-header-group">
        <div class="m-l quiz-editor-drag-handle">
          <el-tooltip effect="dark" :content="$t('loc.LessonQuiz12')" placement="top" class="item">
            <el-button class="quiz-drag-button" type="text" size="mini"><i class="lg-icon lg-icon-reorder quiz-drag-icon"></i></el-button>
          </el-tooltip>
        </div>
        <!--    题目类型    -->
        <div class="white-space m-l flex-align-center">
          <div class="text-bolder">{{$t('loc.LessonQuiz2')}}:&nbsp;</div>
          <el-select v-model="value.type" :placeholder="$t('loc.plan111')"
                     @change="quizQuestionTypeChangeHandle"
                     @visible-change="quizQuestionTypeVisibleChangeHandle">
            <el-option
                v-for="questionType in questionTypes"
                :key="questionType.key"
                :disabled="questionType.disabled"
                :label="$t('loc.' + questionType.label)"
                :value="questionType.key">
            </el-option>
          </el-select>
        </div>
        <!--    题目 level    -->
        <div class="white-space m-l flex-align-center">
          <div class="text-bolder">{{ isBloom ? $t('loc.LessonQuiz3') : $t('loc.lessonQuizLevelDOKAbbr')}}:&nbsp;</div>
          <el-select v-model="value.level" :placeholder="$t('loc.plan111')"
                     @visible-change="quizQuestionLevelVisibleChangeHandle"
                     @change="quizQuestionLevelChangeHandle">
            <el-option
                v-for="quizQuestionLevel in quizQuestionLevels"
                :key="quizQuestionLevel.key"
                :disabled="quizQuestionLevel.disabled"
                :label="$t('loc.' + quizQuestionLevel.val)"
                :value="quizQuestionLevel.key">
              <div style="display: flex;flex-direction: column;">
                <div class="quiz-question-level-item">{{$t('loc.' + quizQuestionLevel.val)}}</div>
                <div class="quiz-question-level-description">{{$t('loc.' + quizQuestionLevel.val + 'Desc')}}</div>
              </div>
            </el-option>
          </el-select>
        </div>
        <!--    测评点    -->
        <div class="white-space m-l flex-align-center">
          <div class="text-bolder">{{$t('loc.standardOrMeasure')}}:&nbsp;</div>
          <!--     如果是 unit planner 生成部分，那么只会传递可选的测评点列表     -->
          <el-select v-if="isHaveMeasures && !this.showAiAssistant" v-model="value.measureId" :placeholder="$t('loc.plan111')" @change="measureChangeHandle" popper-class="quiz-question-measure-select">
            <el-option
                v-for="measure in measures"
                :key="measure.id"
                :label="measure.abbreviation"
                :value="measure.id">
                <span>{{measure.abbreviation}}<span v-if="isCoreMeasure(measure.id)" style="color: red;"> *</span></span>
            </el-option>
          </el-select>
          <!--     如果是课程编辑界面，则会传递框架下的所有测评点     -->
          <el-select v-else 
                    v-model="value.measureId" 
                    :placeholder="$t('loc.plan111')" 
                    @change="domainsChangeHandle" 
                    @focus="quizSelectDomainsFocusHandle"
                    @visible-change="handleSelectVisibleChange" 
                    popper-class="quiz-question-measure-select"
                    >
            <!-- 单独渲染选中的测评点 -->
            <el-option v-show="false" :key="value.measureId" :value="value.measureId" :label="value.measureAbbreviation">
            </el-option>
            <lg-virtual-list 
              ref="virtualList"
              :data-sources="flattenedMeasures"
              :data-component="virtualMeasureOption"
              height="245px"
            />
          </el-select>
        </div>
        <!-- 如果是 unit planner 的课程生成界面，则显示重新生成按钮或者生成按钮 -->
        <div v-if="isHaveMeasures || showGenerateButton" class="m-l  quiz-editor-generate-button">
          <el-tooltip v-if="showQuizRegenerateButton" effect="dark" :content="$t('loc.LessonQuiz5')" placement="top" :hide-after="1000">
            <el-button type="primary" plain icon="el-icon-refresh-right font-bold" @click="regenerateQuizQuestion()" :loading="generateQuestionLoading()">
            </el-button>
          </el-tooltip>
          <!-- 生成 -->
          <el-tooltip v-else effect="dark" :content="$t('loc.gen')" placement="top" :hide-after="1000">
            <el-button icon="lg-icon lg-icon-generate" @click="regenerateQuizQuestion()" class="quiz-generate-button" :loading="generateQuestionLoading()">
              {{$t('loc.gen')}}
            </el-button>
          </el-tooltip>
        </div>
        <!-- 删除当前编辑窗 -->
        <div :style="{ visibility: (showEditorDeleteIcon ? 'visible' : 'hidden') }" class="editor-delete-icon m-l">
          <el-tooltip effect="dark" :content="$t('loc.LessonQuiz11')" placement="top" class="item">
            <el-button class="quiz-delete-button" type="text" size="mini" @click="deleteQuizEditor" style="padding: 0 !important;" :disabled="disableEditorDeleteIcon">
              <i class="el-icon-delete"></i>
            </el-button>
          </el-tooltip>
        </div>
      </div>
      <!--   问题输入框   -->
      <el-skeleton :rows="5" animated :loading="generateQuestionLoading() && !value.question" class="quiz-editor-question">
        <template>
          <div v-show="showEditorQuestionNumber" class="quiz-editor-question-number" v-html="sortIndex"></div>
          <el-input v-model="value.question"
                    :placeholder="$t('loc.LessonQuiz7')"
                    type="textarea"
                    class="quiz-editor-question-input"
                    @focus="quizQuestionFocusHandle"
                    @blur="quizQuestionInputBlur"
                    :autosize="{ minRows: 1, maxRows: 7}"/>
        </template>
      </el-skeleton>
      <p class="quiz-window-answer-placeholder">{{$t('loc.LessonQuiz9')}}:&nbsp;</p>
      <el-skeleton :rows="2" animated :loading="generateQuestionLoading() && !value.answer" class="quiz-editor-answer line-height-16">
        <template>
          <!--   答案输入框   -->
          <el-input v-model="value.answer"
                    :placeholder="$t('loc.LessonQuiz8')"
                    class="quiz-editor-answer-input"
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 5}"/>
        </template>
      </el-skeleton>
    </div>
    <!--  如果是预览模式  -->
    <div v-else class="quiz-window-container quiz-preview-font-style">
      <!--   头部信息展示栏   -->
      <div class="quiz-header-group flex-align-center">
        <div class="quiz-preview-question-type">{{getQuestionTypeName(value.type)}}</div>
        <div><el-divider direction="vertical" class="quiz-window-divider"></el-divider></div>
        <div class="quiz-preview-question-level">{{getQuestionLevelName(value.level)}}</div>
        <div><el-divider direction="vertical" class="quiz-window-divider"></el-divider></div>
        <div><el-tag size="small" type="info" class="quiz-preview-measure">{{value.measureAbbreviation}}<span v-if="isCoreMeasure(value.measureId)" style="color: red;"> *</span></el-tag></div>
      </div>
      <!--   问题   -->
      <div class="quiz-preview-question">
        <div v-show="showEditorQuestionNumber" class="quiz-preview-question-number" v-html="sortIndex"></div>
        <div v-html="showQuizQuestionPreviewQuestion"></div>
      </div>
      <p class="quiz-window-answer-placeholder">{{$t('loc.LessonQuiz9')}}:&nbsp;</p>
      <!--   答案   -->
      <div class="quiz-preview-answer" v-html="showQuizAnswerPreviewQuestion"></div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import LgVirtualList from '@/components/LgVirtualList.vue'
import VirtualMeasureOption from './VirtualMeasureOption.vue'

export default {
  name: 'LessonQuizWindow',
  components: {
    VirtualMeasureOption,
    LgVirtualList
  },
  props: {
    // 测评点列表
    measures: {
      type: Array,
      default: () => []
    },
    // 年龄组
    ageGroupName: {
      type: String,
      default: ''
    },
    // 当前 quiz 的 question 实体内容
    value: {
      type: Object,
      default: () => {}
    },
    // 当前 question 在 questions 数组中的索引
    questionIndex: {
      type: Number,
      default: 0
    },
    // 是否为编辑模式
    edit: {
      type: Boolean,
      default: true
    },
    // 是否禁用删除键
    disableEditorDeleteIcon: {
      type: Boolean,
      default: true
    },
    // 当前框架下的所有测评点
    domains: {
      type: Array,
      default: () => []
    },
    showAiAssistant: {
      type: Boolean,
      default: false
    },
    // 是否是 boolom taxonomy
    isBloom: {
      type: Boolean,
      default: null
    },
    showGenerateButton: {
      type: Boolean,
      default: false
    },
    // 新增：课程生成状态
    generateLessonLoading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      questionTypes: [], // 问题类型
      quizQuestionLevels: [], // quiz 题目难度
      showEditorDeleteIcon: false, // 是否显示删除图标
      showEditorQuestionNumber: true, // 是否显示题号
      virtualMeasureOption: VirtualMeasureOption,
    }
  },
  created () {
    // 获取可选的问题类型
    this.getLessonQuizQuestionTypes(this.ageGroupName)
    // 获取题目难度
    // this.quizQuestionLevels = this.quizBloomTaxonomyAllTypes
  },
  mounted () {
    // 初始化测评点相关信息
    if (this.showAiAssistant) {
      this.setMeasureByAbbreviation(this.value.measureAbbreviation)
    } else {
      if (this.isHaveMeasures) {
        // 如果是展示范围内的测评点
        this.setMeasureById(this.value.measureId)
      } else if (this.domains && Object.keys(this.domains).length !== 0) {
        // 如果是展示框架下的测评点
        this.setDomainById(this.value.measureId)
      }
    }
    if (this.edit) {
      this.$nextTick(() => {
        // 给问题输入框和答案输入框添加滚动条样式
        const quizQuestionInputTextArea = this.$el.querySelector('.quiz-editor-question-input > textarea')
        const quizAnswerInputTextArea = this.$el.querySelector('.quiz-editor-answer-input > textarea')
        if (quizQuestionInputTextArea) {
          // 给问题输入框添加滚动条样式
          quizQuestionInputTextArea.classList.add('lg-scrollbar-show')
        }
        if (quizAnswerInputTextArea) {
          // 给答案输入框添加滚动条样式
          quizAnswerInputTextArea.classList.add('lg-scrollbar-show')
        }
      })
    }
  },
  computed: {
    ...mapState({
      quizQuestionAllTypes: state => state.curriculum.quizQuestionAllTypes, // 所有的 quiz 题目类型
      quizBloomTaxonomyAllTypes: state => state.curriculum.quizBloomTaxonomyAllTypes, // 所有的 quiz 难度类型
      quizDOKAllTypes: state => state.curriculum.quizDOKAllTypes // 所有的 quiz DOK 类型
    }),
    // 将领域和测评点展平为一个数组
    flattenedMeasures() {
      if (!this.domains || this.domains.length === 0) return []
      
      // 将所有领域和测评点展平为一个数组
      const flattened = []
      
      this.domains.forEach(domain => {
        // 添加领域作为分组标题
        flattened.push({
          id: domain.id,
          name: domain.name,
          abbreviation: domain.abbreviation,
          // 标记为领域
          isDomain: true
        })
        // 添加该领域下的所有测评点
        if (domain.children && domain.children.length) {
          domain.children.forEach(measure => {
            flattened.push({
              ...measure,
            })
          })
        }
      })
      return flattened
    },
    // 是否显示重新生成按钮
    showQuizRegenerateButton () {
      return !this.value.hasOwnProperty('showQuizRegenerateButton')
    },
    // 判断传入的是否为当前课程课程的测评点
    isHaveMeasures () {
      return this.measures && Object.keys(this.measures).length !== 0
    },
    // quiz 是否正在加载
    generateQuestionLoading () {
      // 必须要返回函数，不然无法接收到 generateLoading 的变化
      return function () {
        return this.value.hasOwnProperty('generateLoading') && this.value.generateLoading
      }
    },
    // 问题预览
    showQuizQuestionPreviewQuestion () {
      const value = this.value
      return value.question ? value.question.replaceAll('\n', '<br/>') : ''
    },
    // 题号
    sortIndex () {
      return (this.value && this.value.sortIndex && this.value.sortIndex !== '') ? (this.value.sortIndex + '.&nbsp;') : ''
    },
    // 答案预览
    showQuizAnswerPreviewQuestion () {
        const value = this.value
        return value.answer ? value.answer.replaceAll('\n', '<br/>') : ''
    },
    // 获取题目等级名称
    getQuestionLevelName () {
      return function (level) {
        let translateKey = this.quizQuestionLevels.find(item => item.key === level) && this.quizQuestionLevels.find(item => item.key === level).val
        return translateKey ? this.$t('loc.' + translateKey) : level
      }
    },
    // 获取题目类型名称
    getQuestionTypeName () {
      return function (type) {
        let translateKey = this.questionTypes.find(item => item.key === type) && this.questionTypes.find(item => item.key === type).label
        return translateKey ? this.$t('loc.' + translateKey) : type
      }
    },
    // 判断是否为核心测评点
    isCoreMeasure () {
      return function (measureId) {
        if (this.measures && this.measures.length > 0) {
          return this.measures.find(measure => measure.id === measureId) && this.measures.find(measure => measure.id === measureId).core
        }
        if (this.domains && this.domains.length > 0) {
          let measures = this.domains.flatMap(domain => domain.children)
          return measures.find(measure => measure.id === measureId) && measures.find(measure => measure.id === measureId).core
        }
        return false
      }
    }
  },
  watch: {
    // 监听 question 内容变化
    value: {
      deep: true,
      handler (val) {
        // 生成中是从外部传递过来所以不需要再次传递到外部
        if (this.generateLessonLoading) {
          return
        }
        this.$emit('input', val)
      }
    },
    'value.measureAbbreviation': {
      immediate: true,
      deep: true,
      handler (val) {
        if (this.generateLessonLoading) {
          return
        }
        this.setMeasureByAbbreviation(val)
      }
    },
    generateLessonLoading: {
      handler (newVal, oldVal) {
        if (!newVal && oldVal && this.value) {
          this.value.measureAbbreviation && this.setMeasureByAbbreviation(this.value.measureAbbreviation)
          this.$emit('input', this.value)
        }
      }
    },
    isBloom: {
      immediate: true,
      handler (val) {
        if (val === null) {
          return
        }
        if (val) {
          this.quizQuestionLevels = this.quizBloomTaxonomyAllTypes
        } else {
          this.quizQuestionLevels = this.quizDOKAllTypes
        }
        // 获取可选问题等级
        this.getLessonQuizSelectableQuestionLevels({ ageGroupName: this.ageGroupName, type: this.value.type })
      }
    }
  },
  methods: {
    // 题目类型下拉框更改选项回调
    quizQuestionTypeChangeHandle (val) {
      // 获取可选题目 level
      this.getLessonQuizSelectableQuestionLevels({ ageGroupName: this.ageGroupName, type: val })
    },
    // 题目类型下拉框出现回调
    quizQuestionTypeVisibleChangeHandle (visible) {
      if (visible) {
        // 获取可选题目 level 并设置当前选项为第一个可选 level
        this.getLessonQuizSelectableQuestionLevels({ ageGroupName: this.ageGroupName, type: this.value.type })
      }
    },
    // 题目难度下拉框出现回调
    quizQuestionLevelVisibleChangeHandle (visible) {
      if (visible) {
        // 获取可选题目 level
        this.getLessonQuizQuestionLevels({ ageGroupName: this.ageGroupName, type: this.value.type })
      }
    },
    // 题目难度下拉框更改选项回调
    quizQuestionLevelChangeHandle (val) {
      // 获取可选题目 level
      this.getLessonQuizQuestionLevels({ ageGroupName: this.ageGroupName, type: val })
    },
    // 测评点下拉框选项更改的回调
    setMeasureByAbbreviation (val) {
      const measure = this.measures.find(measure => measure.abbreviation === val)
      if (measure) {
        this.value.measureName = measure.name
        this.value.measureId = measure.id
      }
    },
    measureChangeHandle (value) {
      this.setMeasureById(value)
    },
    // 课程库情况的测评点下拉框选项更改的回调
    domainsChangeHandle (value) {
      this.setDomainById(value)
    },
    // 根据测评点 id 设置测评点的其他信息
    setMeasureById (value) {
      if (this.measures) {
        const measure = this.measures.find(measure => measure.id === value)
        if (measure) {
          this.value.measureAbbreviation = measure.abbreviation
          this.value.measureName = measure.name
        }
      }
    },
    // 课程库情况的根据测评点 id 设置测评点的其他信息
    setDomainById (value) {
      if (this.domains) {
        const measure = this.domains.flatMap(domain => domain.children).find(measure => measure.id === value)
        if (measure) {
          this.value.measureAbbreviation = measure.abbreviation
          this.value.measureName = measure.name
        }
      }
    },
    // （重新）生成 question
    regenerateQuizQuestion () {
      const { level, type, measureAbbreviation, question } = this.value
      // 获取当前题目等级类型请求枚举值
      const questionType = this.questionTypes.find(item => item.key === type)
      this.$emit('regenerateQuizQuestion', {
        questionIndex: this.questionIndex,
        questionLevel: level,
        // 默认为多选题枚举
        questionType: questionType ? questionType.value : 'MULTIPLE_CHOICE',
        measureAbbreviation: measureAbbreviation,
        // 如果之前问题不存在则传递 null
        question: (question && question !== '') ? question : null
      })
    },
    // 鼠标进入编辑区域不显示删除图标
    quizEditorContainerMouseOverHandle () {
      this.showEditorDeleteIcon = true
    },
    // 鼠标移出编辑区域不显示删除图标
    quizEditorContainerMouseLeaveHandle () {
      this.showEditorDeleteIcon = false
    },
    // 获取可选问题等级, 并设置其是否禁用
    getLessonQuizQuestionLevels (ageGroupName) {
      this.$store.dispatch('curriculum/getLessonQuizQuestionLevels', { ageGroupNameAndType: ageGroupName, isBloom: this.isBloom })
          .then(questionLevels => {
            // 设置问题难度选项是否可选
            this.quizQuestionLevels = this.quizQuestionLevels.map(item => ({
              ...item,
              disabled: !questionLevels.find(questionLevel => questionLevel === item.key)
            }))
          })
    },
    // 获取可选问题等级并设置当前选择的问题等级为可选问题的等级的第一项
    getLessonQuizSelectableQuestionLevels (ageGroupName) {
      this.$store.dispatch('curriculum/getLessonQuizQuestionLevels', { ageGroupNameAndType: ageGroupName, isBloom: this.isBloom })
        .then(questionLevels => {
          if (questionLevels) {
            const questionLevel = questionLevels.find(questionLevel => questionLevel === this.value.level)
            if (!questionLevel || questionLevel === '') {
              this.value.level = questionLevels[0]
            }
          }
        })
    },
    // 获取可选问题类型
    getLessonQuizQuestionTypes (ageGroupName) {
      this.$store.dispatch('curriculum/getLessonQuizQuestionTypes', ageGroupName)
          .then(questionTypes => {
            this.questionTypes = questionTypes
          })
    },
    // 删除当前的 quiz window
    deleteQuizEditor () {
      this.$emit('deleteQuizEditor', this.value.sortIndex - 1)
    },
    // 如果输入框获取焦点
    quizQuestionFocusHandle () {
      // 失去题号
      this.showEditorQuestionNumber = false
    },
    // 如果输入框失去焦点
    quizQuestionInputBlur () {
      // 显示题号
      this.showEditorQuestionNumber = true
    },
    quizSelectDomainsFocusHandle () {
      if (!this.domains) {
        // 如果没有测评点
        this.$message.error(this.$t('loc.LessonQuiz10'))
        return
      }
      
      // 滚动逻辑已经移到 handleSelectVisibleChange 中处理
    },

    // 处理下拉列表可见性变化
    handleSelectVisibleChange(visible) {
     if (visible && this.value.measureId) {
       this.$nextTick(() => {
         const virtualList = this.$refs.virtualList
         if (virtualList) {
           virtualList.scrollToItem(this.value.measureId)
         }
       })
     }
   }
  }
}
</script>
<style scoped lang="less">
// 下拉框内容可以换行
.el-select-dropdown__item {
    min-height: 34px;
    padding-top: 8px;
    padding-bottom: 8px;
    height: auto;
    line-height: normal;
    > span {
        white-space: normal;
    }
}
@media screen and (max-width: 768px) {
  .quiz-editor-question {
    margin-top: 16px;
    margin-left: 16px;
    width: 95%;
    display: flex;
  }
  .quiz-editor-answer {
    margin-left: 16px;
    width: 95%;
    margin-bottom: 16px;
  }
}

@media screen and (min-width: 768px) {
  .quiz-editor-question {
    margin-top: 16px;
    margin-left: 16px;
    width: 98%;
    display: flex;
  }
  .quiz-editor-answer {
    margin-left: 16px;
    width: 98%;
    margin-bottom: 16px;
  }
}
.quiz-window-container {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  word-break: break-word;
  background-color: var(--color-border-lighter);
}
.quiz-window-container-edit {
  border: 1px solid var(--color-border);
  background-color: var(--color-white) !important;
}
.quiz-window-container-edit:hover {
  border-color: var(--color-primary);
}
.quiz-header-group {
  display: flex;
  width: 98%;
  margin-top: 10px;
  height: 36px;
}

/deep/ .quiz-editor-question-input > textarea {
  padding: 0;
  border: none;
  resize: none;
}
/deep/ .quiz-editor-answer-input > textarea {
  padding: 0;
  border: none;
  resize: none;
}
.quiz-drag-button {
  display: flex;
  justify-content: center;
  align-items: center;
}
.quiz-delete-button {
  height: 20px;
  width: 20px;
  color: var(--color-text-placeholder);
}
/deep/ .quiz-drag-button > span {
  margin-top: 12px;
}
.editor-delete-icon {
  display: flex;
  align-items: center;
}
/deep/ .quiz-editor-generate-button > button {
  padding: 0 !important;
  width: 36px;
  height: 36px;
}
/deep/ .el-select > .el-input > input {
  height: 36px;
}
.quiz-window-answer-placeholder {
  margin-left: 16px;
  height: 24px;
  line-height: 24px;
  margin-top: 16px;
  font-weight: 600;
  color: var(--color-text-placeholder);
}
.quiz-preview-question-type {
  font-weight: 600;
  color: var(--color-text-placeholder);
  margin-left: 16px;
}
.quiz-preview-question-level {
  color: var(--color-text-placeholder);
  font-weight: 600;
}
.quiz-preview-font-style {
  line-height: 24px;
  font-size: 16px;
}
.quiz-edit-font-style {
  line-height: 22px;
  font-size: 14px;
}
.quiz-window-divider {
  height: 16px;
}
.quiz-preview-question {
  margin-left: 16px;
  width: 98%;
  margin-top: 8px;
  display: flex;
}
.quiz-preview-answer {
  margin-left: 16px;
  margin-bottom: 16px;
  width: 98%;
}
.quiz-editor-question-number {
  height: 22px;
  text-wrap: nowrap;
}
.quiz-preview-question-number {
  height: 24px;
  white-space: nowrap;
}
.quiz-preview-measure {
  width: 100%;
  white-space: nowrap;
  color: var(--color-text-secondary);
  text-overflow: ellipsis;
}
.el-icon-delete {
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.quiz-drag-icon {
  color: var(--color-text-placeholder);
  font-size: 24px !important;
}
.quiz-question-level-item {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
}
.quiz-question-level-description {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}
.quiz-generate-button {
  border-radius: 4px;
  border: 2px solid var(--color-primary);
  width:120px !important;
  height: 36px !important;
}
/deep/ .quiz-generate-button > span {
  color: var(--color-primary);
}
/deep/ .quiz-generate-button > i {
  color: var(--color-primary);
}
</style>
<style lang="less">
.quiz-question-measure-select {
  width: 300px;
}
</style>
