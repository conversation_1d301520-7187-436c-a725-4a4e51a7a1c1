<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :show-close="true"
    width="920px"
    top="10vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="welcome-dialog"
    @close="closeDialog"
  >
    <div class="welcome-content">
      <div class="welcome-header">
        <div class="header-content">
          <div class="title-row">
            <span class="emoji">🎉</span>
            <h2>{{ $t('loc.lessonWelcomeTitle') }}</h2>
          </div>
          <p>{{ $t('loc.lessonWelcomeDescription') }}</p>
        </div>
      </div>
      <div class="welcome-video">
        <div
        class="video-container">
            <iframe 
            id="welcomeVideo"
            v-if="dialogVisible"
            class="rounded-video"
            style="width: 100%; height: 100%; position: relative; z-index: 10;"
            src="https://www.youtube.com/embed/Z5i_2C9LskQ?si=u3mzcQZd2AX-rEqf&autoplay=1&mute=1&loop=1&modestbranding=1&controls=1&rel=0&hd=1"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen>
          </iframe>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'WelcomeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: this.visible
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    }
  },
  methods: {
    closeDialog() {
      this.$emit('close')
      // 清除首次登录标记
    }
  }
}
</script>

<style lang="less" scoped>
.welcome-dialog {
  border-radius: 12px;
  overflow: hidden;
}
/deep/ .el-dialog__header {
  padding: 0;
}

/deep/.el-dialog__headerbtn {
  top: 16px;
  right: 16px;
  z-index: 10;
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
  color: #000000;
  font-size: 20px;
  font-weight: bold;
}

/deep/ .el-dialog__body {
  padding: 0px 30px 20px 30px;
}

.welcome-content {
  padding: 0;
  text-align: center;
}

.welcome-header {
  padding: 20px 20px 30px;
  background-color: #fff;
  position: relative;
}

.header-content {
  max-width: 600px;
  margin: 0 auto;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.emoji {
  font-size: 32px;
  margin-right: 10px;
}

.welcome-header h2 {
  font-size: 28px;
  margin: 0;
  color: #333;
  font-weight: bold;
  display: inline-block;
}

.welcome-header p {
  font-size: 18px;
  color: #111c1c;
  margin: 10px 0 0;
  line-height: 1.5;
}

.welcome-video {
  margin: 0;
  padding: 0;
}

.video-container {
  position: relative;
  width: 860px;
  height: 484px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  overflow: hidden;
}

.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  opacity: 0.15;
  z-index: 1;
}

.rounded-video {
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  z-index: 5;
}
</style> 