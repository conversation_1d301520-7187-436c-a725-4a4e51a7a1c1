<template>
  <div v-if="newLessonResourceOpen" class="resource-settings position-relative"
  :style="{'width': useCollapse ? '100%' : 'fit-content'}">
    <!-- 设置按钮 -->
    <template v-if="useTextBtn">
      <div class="display-flex align-items lg-pointer" @click="openDialog">
        <span class="text-default font-size-14 font-weight-600">Manage Resources ({{ resourceCount }}) </span>
        <i class="lg-icon lg-icon-arrow-right add-margin-l-16 font-size-20 color-676879"></i>
      </div>
    </template>
    <template v-else-if="!useCollapse">
      <el-button plain @click="openDialog">
      <template #icon>
        <i class="el-icon el-icon-setting" style="margin-right: 5px"></i>
      </template>
      <span>Manage Resources</span>
    </el-button>
    <!-- <span class="new-tag">Beta</span> -->
    </template>
    <template v-else>
      <el-collapse accordion class="resource-collapse" @change="handleCollapseChange">
      <el-collapse-item name="advanced-settings">
        <template slot="title">
          <div class="collapse-title-container">
            <img src="@/assets/img/icons/settings-icon.svg" class="settings-icon" />
            <span>Advanced settings</span>
          </div>
        </template>
        <div @click="openDialog" class="collapse-content">
          <div>Manage Lesson Resource ({{resourceCount}})</div>
          <i class="el-icon-arrow-right"></i>
        </div>
        <LessonModuleSetting class="collapse-content2">
          <template slot="content">
            <div >Manage Lesson Modules</div>
            <i class="el-icon-arrow-right"></i>
          </template>
        </LessonModuleSetting>
        <div id="collapse-content-advanced-setting"></div>
      </el-collapse-item>
    </el-collapse>
    </template>

    <!-- 设置弹窗 -->
    <el-dialog
      :title="$t('loc.manageResources')"
      :visible.sync="dialogVisible"
      width="800px"
      append-to-body
      @close="handleClose"
      custom-class="resource-dialog"
      :close-on-click-modal="false"
    >
      <div class="resource-description">
        {{$t('loc.manageResourcesDescription')}}
      </div>

      <div class="resource-list lg-scrollbar-show">
        <template v-if="loading">
          <div v-for="i in 7" :key="i" class="resource-item">
            <el-skeleton-item variant="button" style="width: 40px; height: 20px;" />
            <div class="resource-info">
              <el-skeleton-item variant="text" style="width: 200px; margin-top: 8px;" />
              <el-skeleton-item variant="text" style="width: 300px; margin-top: 8px;" />
              <el-skeleton-item variant="text" style="width: 250px; margin-top: 8px;" />
            </div>
          </div>
        </template>
        <template v-else>
          <div v-for="item in resourceList" :key="item.id" class="resource-item">
            <el-switch
              @change="handleSwitchChange(item)"
              v-model="item.enabled"
              active-color="#10B3B7"
            />
            <div class="resource-info">
              <div class="resource-name">{{ item.name }}</div>
              <div class="resource-description" v-if="item.description">{{ item.description }}</div>
              <div class="resource-url" v-if="item.apiEndpoint">
                <el-link type="primary" :href="item.apiEndpoint" target="_blank">{{ item.apiEndpoint }}</el-link>
              </div>
            </div>
          </div>
        </template>
      </div>

      <div class="dialog-footer">
        <el-button @click="showSuggestDialog" class="cancel-button">{{$t('loc.suggestNewResource')}}</el-button>
        <el-button type="primary" v-if="!useCollapse" @click="regenerateResources" class="save-button" :loading="regenerateLoading">{{ $t('loc.regenerate') }}</el-button>
      </div>
    </el-dialog>

    <!-- 建议资源弹窗 -->
    <el-dialog
      title="Suggest a new resource"
      :visible.sync="suggestDialogVisible"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      custom-class="suggest-dialog"
    >
      <el-form v-if="suggestDialogVisible" :model="suggestForm" ref="suggestForm" :rules="suggestRules">
        <el-form-item label="Resource URL" prop="resourceURL" required>
          <el-input
            v-model="suggestForm.resourceURL"
            placeholder="Add your own Resource URL"
          />
        </el-form-item>
        <el-form-item label="Reason for Recommending" prop="reasonForRecommending">
          <el-input
            type="textarea"
            v-model="suggestForm.reasonForRecommending"
            placeholder="Please explain why you are recommending this site."
            :rows="8"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="suggestDialogVisible = false" class="cancel-btn">Cancel</el-button>
        <el-button type="primary" @click="submitSuggest" class="submit-btn" :loading="suggestLoading">Submit</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import resourceApi from '@/api/lessons2/resource'
import LessonModuleSetting from '@/views/modules/lesson2/unitPlanner/components/editor/LessonModuleSettings.vue'

export default {
  name: 'ResourceSettings',
  components: {
    LessonModuleSetting
  },
  props: {
    // 是否使用折叠面板样式
    useCollapse: {
      type: Boolean,
      default: false
    },
    // 设置埋点名称
    setupEventName: {
      type: String,
      default: ''
    },
    // 使用文字按钮样式
    useTextBtn: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 资源管理弹窗是否可视
      dialogVisible: false,
      // 建议弹窗是否可视
      suggestDialogVisible: false,
      // 资源列表
      resourceList: [],
      // 是否在加载中
      loading: false,
      // 在按钮呈现 collapse 的时，是否为展开状态
      isExpanded: false,
      // 资源中开启的数目
      resourceCount: 0,
      // 建议弹窗按钮加载效果
      suggestLoading: false,
      // 重新生成按钮 loading 状态
      regenerateLoading: false,
      // 建议的 Form
      suggestForm: {
        // URL 链接
        resourceURL: '',
        // 推荐的理由
        reasonForRecommending: ''
      },
      // Form 校验规则
      suggestRules: {
        // URL 是必填的
        resourceURL: [
          {required: true, message: 'Resource URL is required', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              try {
                // 验证 URL
                new URL(value);
                callback();
              } catch (e) {
                callback(new Error('Please enter a valid URL'));
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  async created () {
    await this.getResourceSettings()
  },
  computed: {
    ...mapState({
      open: state => state.common.open
    }),
    // 新课程资源搜索开关
    newLessonResourceOpen() {
      if (!this.open) {
        return false
      }
      return this.open.newLessonResourceOpen
    }
  },
  methods: {
    // 切换展开/折叠状态
    toggleExpand () {
      this.isExpanded = !this.isExpanded
      if (this.isExpanded) {
        this.openDialog()
      }
    },

    // 打开设置弹窗
    async openDialog() {
      this.dialogVisible = true;
      await this.getResourceSettings();
      // 发送埋点事件
      if (this.setupEventName) {
        this.$analytics.sendEvent(this.setupEventName)
      }
    },

    // 获取资源设置
    async getResourceSettings () {
      try {
        this.loading = true
        const res = await resourceApi.getUserLessonResourceSettings()
        this.resourceList = res
        this.resourceCount = this.resourceList.filter(item => item.enabled).length
      } catch (error) {
        // '获取资源设置失败'
        this.$message.error(this.$t('loc.getResoureSettingsFailed'));
      } finally {
        this.loading = false
      }
    },

    // 处理开关切换
    async handleSwitchChange(switchItem) {
      try {
        const enabledCount = this.resourceList.filter(item => item.enabled).length;

        // 如果尝试关闭最后一个启用的资源
        if (enabledCount === 0) {
          this.$message.warning(this.$t('loc.leastResoure'));
          // 找到当前被关闭的资源并重新启用它
          const lastDisabledItem = this.resourceList.find(item => switchItem.id === item.id);
          if (lastDisabledItem) {
            lastDisabledItem.enabled = true;
          }
          return;
        }

        const enabledIds = this.resourceList
          .filter(item => item.enabled)
          .map(item => item.id);

        await resourceApi.saveUserResourceSettings(enabledIds);
        this.resourceCount = enabledCount;
      } catch (error) {
        // '保存资源设置失败'
        this.$message.error(this.$t('loc.saveResourceSettingsFailed'));
      }
    },
    // 保存设置
    async regenerateResources () {
      try {
        this.regenerateLoading = true;
        const enabledIds = this.resourceList
          .filter(item => item.enabled)
          .map(item => item.id)

        await resourceApi.saveUserResourceSettings(enabledIds)
        this.$emit('regenerate')
        this.dialogVisible = false
      } catch (error) {
      } finally {
        this.regenerateLoading = false;
      }
    },

    // 显示建议资源弹窗
    showSuggestDialog() {
      this.suggestDialogVisible = true;
      this.suggestForm = {
        resourceURL: '',
        reasonForRecommending: ''
      };
      // 发送埋点事件
      this.$analytics.sendEvent('cg_unit_create_setup_resource_new')
    },

    // 提交建议
    async submitSuggest() {
      if (this.suggestLoading) {
        return
      }
      try {
        await this.$refs.suggestForm.validate();
        this.suggestLoading = true;
        await resourceApi.suggestNewResource(this.suggestForm);
        this.$message.success('Submission successful. We will process your request as soon as possible.');
        this.suggestDialogVisible = false;
      } catch (error) {
        // 表单验证失败时不显示错误消息，因为 validate 会处理
        // 如果是 API 错误，可以在这里添加 this.$message.error
      } finally {
        this.suggestLoading = false;
        // 发送埋点事件
        this.$analytics.sendEvent('cg_unit_create_setup_resource_submit')
      }
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.isExpanded = false;
      this.resourceList = [];
    },

    // 处理折叠面板变化
    handleCollapseChange (activeName) {
      // 仅当 'advanced-settings' 面板被激活（展开）时执行
      if (activeName === 'advanced-settings') {
        this.$nextTick(() => {
            const element = document.getElementById('collapse-content-advanced-setting')
            if (element) {
              // 获取折叠面板的容器元素
              const collapseContent = element.closest('.el-collapse-item__wrap')
              if (collapseContent) {
              const handleTransitionEnd = () => {
                element.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
                collapseContent.removeEventListener('transitionend', handleTransitionEnd)
              }
              collapseContent.addEventListener('transitionend', handleTransitionEnd)
            }
          }
        })
        }
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-settings {
  display: inline-block;
}

.resource-collapse {
  border: none;
  width: 100%;

  /deep/ .el-collapse-item__header {
    background-color: transparent;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    font-family: Inter, sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.57;
    color: #111C1C;
    padding: 0;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.6);

    &:hover {
      background-color: rgba(255, 255, 255, 0.8);
    }
  }

  /deep/ .el-collapse-item__header.is-active {
    border: 1px solid #10B3B7;
  }

  /deep/ .el-collapse-item__content {
    padding: 12px;
    background-color: #F5F5F5;
    border-radius: 8px;
    margin-top: 10px;
  }

  /deep/ .el-collapse-item__wrap {
    border-bottom: none !important;
  }
}

.collapse-title-container {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 16px;
  width: 100%;
}

.collapse-content {
  padding: 0;
  background: #FFFFFF;
  border: 1px solid #DCDFE6;
  padding-top: 9px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #111C1C;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  padding-right: 12px;
  padding-bottom: 9px;
  padding-left: 12px;
  gap: 4px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-width: 1px;
}

.collapse-content2 {
  padding: 0;
  background: #FFFFFF;
  border: 1px solid #DCDFE6;
  padding-top: 9px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #111C1C;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  padding-right: 12px;
  padding-bottom: 9px;
  padding-left: 12px;
  gap: 4px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-width: 1px;
  border-top: none;
}

.settings-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.dropdown-icon {
  width: 12px;
  height: 12px;
  margin-left: 4px;

  &.rotated {
    transform: rotate(180deg);
  }
}

.resource-button-container {
  position: relative;
  display: inline-block;
}

.resource-button {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 40px;
  padding: 0 16px;
  border-radius: 4px;
  font-family: Inter, sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.57;
  color: #111C1C;

  &.expanded {
    border-color: #10B3B7;
    background-color: rgba(255, 255, 255, 0.6);

    .expand-icon {
      color: #676879;
    }
  }

  .expand-icon {
    font-size: 12px;
    margin-left: 4px;
  }
}

/* 弹窗样式 */
/deep/.resource-dialog {
  border-radius: 8px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);

  .el-dialog__header {
    padding: 24px;
    padding-bottom: 16px;
    border-bottom: none;

    .el-dialog__title {
      font-family: Inter, sans-serif;
      font-weight: 600;
      font-size: 20px;
      color: #111C1C;
    }
  }

  .el-dialog__body {
    padding: 0 24px 24px;
  }
}

.resource-description {
  font-family: Inter, sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  color: #111C1C;
  margin-bottom: 16px;
}

.resource-list {
  max-height: 420px;
  overflow-y: auto;
  margin-bottom: 24px;

  .resource-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px 8px 12px;
    border-radius: 4px;
    background-color: #fff;

    &:hover {
      background-color: #f5f7fa;
    }

    .resource-info {
      flex: 1;
      font-family: Inter, sans-serif;
      color: #111C1C;

      .resource-name {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.57;
      }

      .resource-description {
        font-size: 14px;
        font-weight: 400;
        line-height: 1.57;
        margin-bottom: 0;
        color: #606266;
      }

      .resource-url {
        font-size: 14px;
        font-weight: 400;
        line-height: 1.57;
        color: #10B3B7;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-button {
    border: 2px solid #10B3B7;
    color: #10B3B7;
    border-radius: 4px;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.57;
    padding: 8px 12px;
    height: 40px;
  }

  .save-button {
    background-color: #10B3B7;
    border: 2px solid rgba(16, 179, 183, 0.4);
    color: #FFFFFF;
    border-radius: 4px;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.57;
    padding: 8px 12px;
    height: 40px;
  }

  .suggest-button {
    color: #10B3B7;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.57;
    margin-left: auto;
  }
}

.new-tag {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: -5px;
  top: -8px;
}
.new-tag-preview {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: 18px;
  top: -7px;
}

/deep/.suggest-dialog {
  border-radius: 8px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);

  .el-dialog__header {
    padding: 24px;
    padding-bottom: 0;

    .el-dialog__title {
      font-family: Inter, sans-serif;
      font-weight: 600;
      font-size: 20px;
      color: #111C1C;
    }
  }

  .el-dialog__body {
    padding: 0 24px 24px;
  }

  .dialog-footer {
    margin-top: 22px;
  }
}

/deep/ .suggest-dialog {
  .el-form-item__label {
    font-family: Inter, sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 40px;
    margin-bottom: -3px;
    margin-top: 20px;
    color: #111C1C;
  }

  .el-form-item {
    margin-bottom: 0px;
  }

  .el-input__inner, .el-textarea__inner {
    border-color: #DCDFE6;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: Inter, sans-serif;
    font-size: 16px;

    &::placeholder {
      color: #676879;
    }
  }

  .cancel-btn {
    height: 40px;
    border-radius: 4px;
    border: 2px solid #DCDFE6;
    padding: 8px 12px;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: #111C1C;
  }

  .submit-btn {
    height: 40px;
    border-radius: 4px;
    background-color: #10B3B7;
    border: 2px solid rgba(16, 179, 183, 0.4);
    padding: 8px 12px;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #FFFFFF;
  }
}
</style>
