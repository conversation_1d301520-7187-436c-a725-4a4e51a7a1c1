<template>
  <div style="display: flex;flex-flow: column;gap:10px;">
    <div v-for="domain in value"
         class="tag-group">
      <span class="tag-group__title" v-show="domain.abbreviation">{{ domain.abbreviation }}:</span>
      <el-tag v-for="measure in domain.children"
              closable
              effect="dark"
              :key="measure.id"
              @close="remove(domain,measure)"
              size="small"
              class="tag-measure"
              :disable-transitions="true">
        {{measureText(measure)}}
      </el-tag>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LessonMeasureLabel',
  props: [
    'value',
    'beforeChange'
  ],
  computed: {
    // measure 的文本信息
    measureText() {
      return function(measure) {
        // 如果 measure 为空
        if (!measure) {
          return ''
        }
        // 定义测评点文本
        let measureText = ''
        // 获取测评点缩写和测评点名称
        let abbreviation = measure.abbreviation
        let name = measure.name
        // 如果缩写和名称都存在，那么就展示缩写和名称
        if (abbreviation && name && abbreviation.trim() !== '' && name.trim() !== '') {
          measureText = `${abbreviation}-${name}`
        } else if (abbreviation) {
          // 如果缩写存在，那么就展示缩写
          measureText = abbreviation
        } else if (name) {
          // 如果名称存在，那么就展示名称
          measureText = name
        }
        // 返回测评点文本
        return measureText
      }
    }
  },
  methods: {
    remove (domain, measure) {
      domain = { ...domain, children: domain.children.filter(item => item.id !== measure.id) }
      let domains = []
      for (const value of this.value) {
        if (value.id !== domain.id) {
          domains.push({ ...value, children: value.children.slice() })
        } else if (domain.children.length) {
          domains.push(domain)
        }
      }
      if (this.beforeChange instanceof Function) {
        this.beforeChange(domains).then((flag) => {
          if (flag) {
            this.$emit('input', domains)
            this.$emit('change', domains)
          }
        })
      } else {
        this.$emit('input', domains)
        this.$emit('change', domains)
      }
    }
  }
}
</script>

<style scoped lang="less">
.tag-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;

  .tag-group__title {
    color: #39b2b8;
    font-weight: bold;
  }
  
  .tag-measure{
    display: block;
    white-space: normal;
    word-wrap: break-word;
    height: auto;
  }
}
</style>
