<template>
  <div>
    <el-popover
      placement="bottom-start"
      ref="measureRef"
      :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }"
      :visible-arrow="false"
      popper-class="popover-default"
      v-model="show">
      <el-button slot="reference" icon="el-icon-plus" plain style="width: 100%; box-shadow: none; border-width: 1px;">
        {{ $t('loc.lesson2NewLessonFormPlaceHolderSelect') }}
      </el-button>
      <div>
        <!-- 使用 vue-easy-tree 替换 el-tree，支持虚拟滚动 -->
        <vue-easy-tree
          v-loading="loading"
          ref="tree"
          node-key="id"
          height="360px"
          :data="domains"
          :props="treeProps"
          show-checkbox
          :default-checked-keys="flattenMeasureIds(value)"
          :render-content="renderContent"
          @node-click="handleNodeClick"
          @check="handleCheck"
          :check-strictly="true"
          :default-expand-all="shouldExpandAll"
          empty-text="No data"
          :itemSize="34"
          class="lg-scrollbar-show lg-scrollbar-small lesson-measure-select-popover word_keep-all text-left line-height-22">
        </vue-easy-tree>
      </div>
    </el-popover>
  </div>
</template>

<script>
import emitter from 'element-ui/src/mixins/emitter'

export default {
  name: 'LessonMeasureSelect',
  componentName: 'ElFormItem',// 自定义属性，用于接收复选框校验事件并阻止派发
  props: {
    value: {
      type: Array,
      default () {
        return []
      }
    },
    domains: {
      type: Array,
      default () {
        return []
      }
    },
    validateEvent: {
      type: Boolean,
      default: true
    },
    beforeChange: {
      type: Function
    },
    lessonMeasureSelectNotShow: {
      type: Boolean,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  mixins: [ emitter ],
  data () {
    return {
      show: false,
      // 缓存已计算的测评点文本
      measureTextCache: new Map(),
      // 缓存父节点的叶子节点列表
      leafChildrenCache: new Map(),
      // tree props 配置
      treeProps: {
        label: 'abbreviation', // 显示字段
        children: 'children'  // 子节点字段
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      handler () {
        this.updateCheckedKeys()
      }
    },
    // 清理缓存当 domains 变化时
    domains: {
      handler () {
        this.measureTextCache.clear()
        this.leafChildrenCache.clear() // 清理叶子节点缓存
        this.$nextTick(() => {
          this.updateCheckedKeys()
        })
      }
    },
    show: {
      handler () {
        if (this.show) {
          this.updateCheckedKeys()
        }
      }
    }
  },
  computed: {
    flattenedMeasureIds() {
      return this.flattenMeasureIds(this.domains)
    },
    // 根据 domains 的数量决定是否默认展开所有节点
    shouldExpandAll() {
      // 当 domains 数量少于等于 3 个时，默认展开所有节点
      if (this.domains) { 
        return this.domains.length <= 3
      }
      return false
    }
  },
  methods: {
    /**
     * 处理节点选中事件
     * @param {Object} data - 当前节点数据
     * @param {Object} treeNode - 树节点对象
     */
    handleCheck(data, treeNode) {
      const tree = this.$refs.tree
      if (!tree) {
        return
      }
      
      // 由于 check-strictly="true"，现在只需要检查是否超过限制
      if (!data.children || data.children.length === 0) {
        // 叶子节点被点击
        this.handleLeafNodeCheck(data, treeNode)
      } else {
        // 父节点被点击，手动处理父子联动
        this.handleParentNodeCheck(data, treeNode)
      }
    },

    /**
     * 处理叶子节点选中逻辑
     * @param {Object} data - 叶子节点数据
     * @param {Object} treeNode - 树节点对象
     */
    handleLeafNodeCheck(data, treeNode) {
      // 获取当前实际选中的叶子节点数量
      const currentLeafCount = this.getCurrentLeafNodeCount()
      
      if (currentLeafCount > 20) {
        this.$message.warning(this.$t('loc.selectMeasureTwenty'))
        // 恢复该节点的状态
        const isCurrentlyChecked = treeNode.checkedKeys.includes(data.id)
        this.$refs.tree.setChecked(data.id, !isCurrentlyChecked, false)
        return
      }
      
      // 父节点状态会在 handleSelect 中通过 value 变化自动更新
      this.handleSelect()
    },

    /**
     * 处理父节点选中逻辑
     * @param {Object} data - 父节点数据
     * @param {Object} treeNode - 树节点对象
     */
    handleParentNodeCheck(data, treeNode) {
      const tree = this.$refs.tree
      const isParentChecked = treeNode.checkedKeys.includes(data.id)
      
      // 获取该父节点下的所有叶子节点
      const leafChildren = this.getLeafChildren(data)
      
      // 获取当前已选中的叶子节点总数
      const currentTotalLeafCount = this.getCurrentLeafNodeCount()
      if (isParentChecked) {
        // 父节点被选中 - 尝试选中所有子节点
        this.selectAllChildrenIfPossible(data, leafChildren, currentTotalLeafCount)
      } else {
        // 父节点被取消选中 - 取消选中所有子节点
        this.unselectAllChildren(data, leafChildren)
      }
      
      // 父节点状态会在 handleSelect 中通过 value 变化自动更新
      this.handleSelect()
    },

    /**
     * 选中所有子节点（如果可能）
     * @param {Object} parentData - 父节点数据
     * @param {Array} leafChildren - 叶子节点列表
     * @param {Number} currentTotalLeafCount - 当前已选中的叶子节点总数
     */
    selectAllChildrenIfPossible(parentData, leafChildren, currentTotalLeafCount) {
      const tree = this.$refs.tree
      const currentCheckedKeys = tree.getCheckedKeys() || []
      
      // 获取未选中的子节点
      const uncheckedChildren = leafChildren.filter(child => !currentCheckedKeys.includes(child.id))
      
      // 获取父节点在 nodesMap 中的引用
      const parentNodeKey = parentData.id.toString()
      const store = tree.store
      if (!store || !store.nodesMap || !store.nodesMap[parentNodeKey]) {
        return
      }
      
      const parentNodeRef = store.nodesMap[parentNodeKey]
      
      // 检查是否会超过限制
      if (currentTotalLeafCount + uncheckedChildren.length > 20) {
        // 超过限制，只选中部分节点
        const remainingSlots = 20 - currentTotalLeafCount
        if (remainingSlots <= 0) {
          this.unselectAllChildren(parentData, leafChildren)
          parentNodeRef.indeterminate = false;
          tree.setChecked(parentData.id, false, false);
          // 如果取消的只有父节点自己则提示
          const checkedChildren = leafChildren.filter(child => currentCheckedKeys.includes(child.id))
          if (checkedChildren.length === 0) {
            this.$message.warning(this.$t('loc.selectMeasureTwenty'))
          }
        } else {
          const nodesToCheck = uncheckedChildren.slice(0, remainingSlots)
          nodesToCheck.forEach(leaf => {
            tree.setChecked(leaf.id, true, false)
          })
        }
      } else {
        // 可以全部选中
        uncheckedChildren.forEach(leaf => {
          tree.setChecked(leaf.id, true, false)
        })
      }
    },

    /**
     * 取消选中所有子节点
     * @param {Object} parentData - 父节点数据
     * @param {Array} leafChildren - 叶子节点列表
     */
    unselectAllChildren(parentData, leafChildren) {
      const tree = this.$refs.tree
      const currentCheckedKeys = tree.getCheckedKeys() || []
      
      // 获取当前选中的子节点
      const checkedChildren = leafChildren.filter(child => currentCheckedKeys.includes(child.id))
      
      // 取消选中所有子节点
      checkedChildren.forEach(leaf => {
        tree.setChecked(leaf.id, false, false)
      })
    },

    /**
     * 获取当前实际选中的叶子节点数量
     * @returns {Number} 选中的叶子节点数量
     */
    getCurrentLeafNodeCount() {
      const tree = this.$refs.tree
      if (!tree) return 0
      
      const checkedKeys = tree.getCheckedKeys() || []
      let leafCount = 0
      
      // 遍历所有选中的节点
      for (const key of checkedKeys) {
        // 如果选中的节点是叶子节点(在 flattenedMeasureIds 中存在)则计数
        if (this.flattenedMeasureIds.includes(key)) {
          leafCount++
        }
      }
      
      return leafCount
    },

         /**
      * 获取父节点下的所有叶子节点（按显示顺序）
      * @param {Object} parentNode - 父节点
      * @returns {Array} 叶子节点列表
      */
     getLeafChildren(parentNode) {
       if (!parentNode) return []
       
       // 使用节点ID作为缓存键
       const cacheKey = parentNode.id
       
       // 检查缓存中是否已有结果
       if (this.leafChildrenCache.has(cacheKey)) {
         return this.leafChildrenCache.get(cacheKey)
       }
       
       const leafNodes = []
       
       // 使用非递归方式遍历，避免调用栈过深
       const stack = [parentNode]
       const processedNodes = new Set() // 防止循环引用
       
       while (stack.length > 0) {
         const node = stack.pop()
         
         if (!node || processedNodes.has(node.id)) continue
         processedNodes.add(node.id)
         
         if (!node.children || node.children.length === 0) {
           // 叶子节点
           leafNodes.push(node)
         } else {
           // 将子节点按照反序入栈，以保持原始显示顺序
           for (let i = node.children.length - 1; i >= 0; i--) {
             stack.push(node.children[i])
           }
         }
       }
       
       // 缓存结果
       this.leafChildrenCache.set(cacheKey, leafNodes)
       
       return leafNodes
     },

    /**
     * 处理节点点击事件
     * @param {Object} data - 节点数据
     * @param {Object} node - 节点对象
     * @param {Object} treeNode - 树节点对象
     */
    handleNodeClick(data, node, treeNode) {
      // 如果是叶子节点（没有 children），则切换勾选状态
      if (!data.children || data.children.length === 0) {
        const tree = this.$refs.tree;
        const isChecked = tree.getCheckedKeys().includes(data.id);
        if (isChecked) {
          tree.setChecked(data.id, false); // 取消选中
        } else {
          // 检查选中后是否会超过限制
          const currentLeafCount = this.getCurrentLeafNodeCount()
          if (currentLeafCount >= 20) {
            this.$message.warning(this.$t('loc.selectMeasureTwenty'))
            return
          }
          tree.setChecked(data.id, true); // 勾选
        }
        this.handleSelect()
      }
    },

    /**
     * 更新选中状态
     */
    updateCheckedKeys() {
      this.$nextTick(() => {
        if (!this.$refs.tree) {
          return
        }
        // 获取当前选中的节点 ID
        const currentCheckedKeys = this.$refs.tree.getCheckedKeys();
        // 获取新的选中节点 ID
        const newCheckedKeys = this.flattenMeasureIds(this.value);
        
        // 找出需要取消选中的节点
        const needUncheck = currentCheckedKeys.filter(id => !newCheckedKeys.includes(id));
        // 找出需要新选中的节点 
        const needCheck = newCheckedKeys.filter(id => !currentCheckedKeys.includes(id));

        // 只更新变化的节点
        needUncheck.forEach(id => {
          this.$refs.tree.setChecked(id, false, false);
        });
        needCheck.forEach(id => {
          this.$refs.tree.setChecked(id, true, false);
        });
        
        // 更新所有父节点状态
        this.$nextTick(() => {
          this.updateAllParentNodesStatus();
        });
      })
    },
    
    /**
     * 更新所有父节点状态
     */
    updateAllParentNodesStatus() {
      // 遍历所有 domains（父节点）
      for (const domain of this.domains) {
        const tree = this.$refs.tree;
        const checkedKeys = tree.getCheckedKeys() || [];
        const leafChildren = this.getLeafChildren(domain);
        
        // 计算选中的子节点数量
        const checkedChildrenCount = leafChildren.filter(child => checkedKeys.includes(child.id)).length;
        
        // 获取父节点在 nodesMap 中的引用
        const parentNodeKey = domain.id.toString();
        const store = tree.store;
        if (!store || !store.nodesMap || !store.nodesMap[parentNodeKey]) {
          continue;
        }
        
        const parentNode = store.nodesMap[parentNodeKey];
        
        if (checkedChildrenCount === 0) {
          // 没有子节点被选中，父节点不选中且不半选
          parentNode.checked && tree.setChecked(domain.id, false, false);
          parentNode.indeterminate = false;
        } else if (checkedChildrenCount === leafChildren.length) {
          // 所有子节点都被选中，父节点选中且不半选
          !parentNode.checked && tree.setChecked(domain.id, true, false);
          parentNode.indeterminate = false;
        } else {
          // 部分子节点被选中，父节点不选中但设置半选状态
          parentNode.checked && tree.setChecked(domain.id, false, false);
          parentNode.indeterminate = true;
        }
      }
    },
    
    /**
     * 获取测评点显示文本
     * @param {Object} measure - 测评点对象
     * @returns {String} 测评点显示文本
     */
    measureDisplayText (measure) {
      if (!measure) {
        return ''
      }
      
      // 使用缓存
      const cacheKey = `${measure.id}-${measure.abbreviation}-${measure.isCore}`
      if (this.measureTextCache.has(cacheKey)) {
        return this.measureTextCache.get(cacheKey)
      }
      
      // 获取测评点缩写和测评点名称
      let abbreviation = measure.abbreviation
      let name = measure.name
      // 如果全称不存在，那么 name 就重新赋值为 description
      if (!name || name.trim() === '') {
        name = measure.description
      }
      // 测评点显示文本
      let measureText = ''
      // 测评点缩写和名称都存在
      if (abbreviation && name && abbreviation.trim() !== '' && name.trim() !== '') {
        // 测评点显示文本为缩写-名称
        measureText = `${abbreviation}-${name}`
      } else if (abbreviation) {
        // 测评点显示文本为缩写
        measureText = abbreviation
      } else if (name) {
        // 测评点显示文本为名称
        measureText = name
      }
      
      // 缓存结果
      this.measureTextCache.set(cacheKey, measureText)
      return measureText
    },
    
    /**
     * 扁平化测评点ID
     * @param {Array} value - 测评点数组
     * @returns {Array} 扁平化后的测评点ID数组
     */
    flattenMeasureIds (value) {
      const measureIds = []
      const stack = [...value]
      
      while (stack.length > 0) {
        const item = stack.pop()
        if (!item) continue
        
        // 测评点无孩子节点
        if (!item.children || !item.children.length) {
          measureIds.push(item.id)
        } else {
          // 有孩子节点的为领域
          stack.push(...item.children)
        }
      }
      
      return measureIds
    },
    
    /**
     * 计算叶子节点数量
     * @param {Array} value - 节点数组
     * @returns {Number} 叶子节点数量
     */
    countLeafNodes (value) {
      let count = 0
      const stack = [...value]
      
      while (stack.length > 0) {
        const item = stack.pop()
        if (!item) continue
        
        // 测评点无孩子节点（叶子节点）
        if (!item.children || !item.children.length) {
          count++
        } else {
          // 有孩子节点的为领域，继续遍历
          stack.push(...item.children)
        }
      }
      
      return count
    },
    
    /**
     * 自定义渲染函数
     * @param {Function} h - 渲染函数
     * @param {Object} param1 - 渲染参数
     * @param {Object} param1.node - 节点对象
     * @param {Object} param1.data - 节点数据
     * @returns {VNode} 渲染节点
     */
    renderContent (h, { node, data }) {
      const isDomain = data.children && data.children.length > 0
      const isCore = data.isCore
      const text = data.displayText || this.measureDisplayText(data)
      
      const className = isDomain ? 'is-domain text-ellipsis' : 'text-ellipsis'
      
      if (isCore) {
        return h('span', { 
          class: className,
          attrs: {
            title: text // 添加 title 属性，鼠标悬浮时显示完整文字
          } 
        }, [
          text,
          h('span', { 
            style: { 
              color: 'red', 
              marginLeft: '4px',
              fontWeight: 'bold'
            } 
          }, ' *')
        ])
      }

      return h('span', {
        class: className,
        attrs: {
          title: text // 添加 title 属性，鼠标悬浮时显示完整文字
        }
      }, text)
    },
    
    /**
     * 根据测评点 ID 构建树结构
     * @param {Array} measureIds - 测评点ID数组
     * @returns {Array} 树结构数组
     */
    buildTree (measureIds) {
      const measureIdSet = new Set(measureIds)
      const result = []
      
      for (const domain of this.domains) {
        const children = domain.children || []
        const measures = children.filter(measure => measureIdSet.has(measure.id))
        if (measures.length) {
          result.push({ ...domain, children: measures })
        }
      }
      
      return result
    },
    
    compare (a, b) {
      return a.id - b.id
    },
    
    measureEqual (a, b) {
      return !a && !b ||
        a && b && a.id === b.id
    },
    
    arrayEqual (a, b, callback) {
      if (!a && !b) {
        return true
      }
      if (!a || !b) {
        return false
      }
      a = a.slice()
      a.sort(this.compare)
      b = b.slice()
      b.sort(this.compare)
      for (let index = 0; index < a.length; index++) {
        if (!this.measureEqual(a[index], b[index])) {
          return false
        }
        if (callback) {
          callback(a[index], b[index])
        }
      }
      return a.length === b.length
    },
    
    treeEquals (tree1, tree2) {
      let trees1 = [tree1]
      let trees2 = [tree2]
      let callback = (a, b) => {
        trees1.push(a.children)
        trees2.push(b.children)
      }
      while (trees1.length) {
        if (!this.arrayEqual(trees1.pop(), trees2.pop(), callback)) {
          return false
        }
      }
      return true
    },
    
    handleCancel () {
      this.show = false
      if (this.validateEvent) {
        var parent = this.$parent || this.$root
        var name = parent.$options.componentName
        this.dispatch('ElFormItem', 'el.form.change', [this.value])
      }
    },
    
    handleSelect () {
      if (this.lessonMeasureSelectNotShow) {
        return
      }
      
      let tree = this.$refs.tree
      if (!tree) {
        return
      }
      
      let checkedKeys = tree.getCheckedKeys() || []
      let halfCheckedKeys = tree.getHalfCheckedKeys ? tree.getHalfCheckedKeys() : []
    
      let current = this.buildTree([...checkedKeys, ...halfCheckedKeys])
      
      // 检查叶子节点数量限制
      const leafCount = this.countLeafNodes(current)
      if (leafCount > 20) {
        this.$message.warning(this.$t('loc.selectMeasureTwenty'))
        // 恢复到之前的选中状态
        this.$nextTick(() => {
          this.updateCheckedKeys()
        })
        current = this.value
      }
      if (!this.treeEquals(this.value, current)) {
        if (this.beforeChange && this.beforeChange instanceof Function) {
          this.beforeChange(current).then((flag) => {
            if (flag) {
              this.$emit('input', current)
              this.$emit('change', current)
              if (this.validateEvent) {
                this.dispatch('ElFormItem', 'el.form.change', [current])
              }
            } else {
              this.$emit('input', JSON.parse(JSON.stringify(this.value)))
            }
          })
        } else {
          this.$emit('input', current)
          this.$emit('change', current)
          if (this.validateEvent) {
            this.dispatch('ElFormItem', 'el.form.change', [current])
            }
          }
        }
      }
  }
}
</script>
<style lang="less">
.lesson-measure-select-popover-container {
  padding: 15px 15px 0;
}

.lesson-measure-select-popover {
  max-height: 400px;
  overflow-y: auto;
  width: 102%;
  /** 加宽一点，防止滚动条盖住过长的文字 */
  max-width: 469px !important;

  .vue-recycle-scroller::-webkit-scrollbar {
    height: 8px;
    width: 5px;
  }
  .vue-recycle-scroller::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #c5c7d0;
  }
  .is-domain {
    font-weight: bold;
  }

  /deep/.el-tree-node__content>label.el-checkbox {
    margin-bottom: 0px !important;
  }

  /deep/ .el-tree-node__content {
    height: auto;
    padding: 5px 0px;
    white-space: normal !important;
    display: flex;
    align-self: center;
    align-items: flex-start;
  }
  .el-tree-node__content>label.el-checkbox {
    margin-bottom: 0px !important;
  }
}

@media screen and (max-width:1599px) {
  .popover-default.el-popover{
    min-width: 495px!important;
  }
}
@media screen and (min-width:1600px) {
  .popover-default.el-popover{
    min-width: 510px!important;
  }
}
</style>
<style lang="less" scoped>

</style>
