<template>
    <el-card :body-style="bodyStyle" style="border: none;" shadow="never">
        <div slot="header" class="w-full">
            <!-- 编辑状态下的头部 -->
            <div class="CLR-header" v-show="isEdit && !isUnitPlanner">
                <div class="header-title">
                  <span class="display-flex align-items" style="gap: 10px">
                    <span>{{ $t('loc.unitPlannerCLR') }}</span>
                  </span>
                </div>
                <div v-show="currentTagName === submodules[1] && isEdit && (adaptUDLAndCLROpen || adaptUDLAndCLRFeatureOpen) && (isWeeklyPlanEdit || isCurriculum || showSourcesBtn || isCurriculumPlugin)" class="display-flex align-items" style="gap: 8px">
                   <!-- 添加 Resource Settings 组件 -->
                   <!-- <ResourceSettings  @regenerate="generateLessonSourceByCLR(true)" /> -->
                    <div class="position-relative add-margin-r-10 add-margin-l-10">

                      <!-- 展示或隐藏 Source -->
                      <el-button @click="changeShowSource()" v-if="hasResources || !removeHtmlTag(lessonClrAndSources.clr)" :loading="generateClassSpecificValueLoading || normalClrLoading" plain>
                        <template #icon>
                          <i class="lg-icon" :class="{ 'lg-icon-eye': !showSource, 'lg-icon-eye-off': showSource }" style="margin-right: 5px"></i>
                        </template>
                        <span v-if="!showSource">{{ $t('loc.unitPlannerShowSources') }}</span>
                        <span v-else>{{ $t('loc.unitPlannerHideSources') }}</span>
                      </el-button>
                      <span v-if="hasResources || !removeHtmlTag(lessonClrAndSources.clr)" class="new-tag">Beta</span>
                    </div>
                    <el-tooltip effect="dark" :content="$t('loc.unitPlannerStep1Regenerate')" placement="top"
                                :hide-after="1000">
                      <el-button v-if="showRegenerateClr" :loading="generateClassSpecificValueLoading || normalClrLoading"
                                 class="regenerated-btn" @click="generateCLR(true)" type="primary" plain>
                        <template #icon>
                          <i class="el-icon-refresh-right font-bold font-size-20"></i>
                        </template>
                      </el-button>
                    </el-tooltip>
                </div>
            </div>
            <!-- 非编辑状态下的头部 -->
            <div v-show="!isEdit && !isUnitPlanner" class="lesson-field-label add-margin-t-10 title-difference">
                <div class="display-flex align-items position-relative clr-header-no-edit"
                :class="hasResources ? 'justify-content-around' : 'justify-content'"
                >
                    <!--空盒子占位置，为了让第二个盒子放在中间位置-->
                    <div class="padding-box" style="width: 145px;height: 40px;" v-if="hasResources"></div>
                    <div class="display-flex align-items justify-content position-relative">
                      <div class="left-gradient title-gradient lg-margin-right-16"></div>
                      <span class="font-size-18 font-color-primary display-flex align-items" style="gap: 10px">
                      <span>{{ $t('loc.unitPlannerCLR') }}</span>
                    </span>
                      <div class="right-gradient title-gradient lg-margin-left-16"></div>
                    </div>
                    <!-- 展示或隐藏 Source -->
                    <el-button @click="changeShowSource()" class="position-relative" v-if="hasResources" plain>
                      <template #icon>
                        <i class="lg-icon" :class="{ 'lg-icon-eye': !showSource, 'lg-icon-eye-off': showSource }" style="margin-right: 5px"></i>
                      </template>
                      <span v-if="!showSource">{{ $t('loc.unitPlannerShowSources') }}</span>
                      <span v-else>{{ $t('loc.unitPlannerHideSources') }}</span>
                      <span v-if="hasResources" class="new-tag-preview">Beta</span>
                    </el-button>
                </div>
            </div>
        </div>
        <div class="CLR-body" :class="isEdit ? 'edit-body-border' : 'preview-body-border'">
            <div class="display-flex justify-content align-items flex-element-center w-full">
                <!--负责切换的 tab -->
                <div v-show="isShowTab" class="w-full">
                    <div class="class-specific-instructions" :style="{ fontSize: isEdit ? '14px' : '16px' }">
                        <div class="source-body">
                          <el-skeleton :rows="5" animated :loading="updateCLRSourceLoading">
                            <ClrEditComponent :showSource="showSource"
                                              v-model="lessonClrAndSources.clr"
                                              :generatedLessonSources="generatedLessonSources"
                                              :loading="generateClassSpecificValueLoading || normalClrLoading"
                                              :canEdit="isEdit" @scrollView="toRefView"/>
                          </el-skeleton>
                          <div v-if="showSource && (!removeHtmlTag(lessonClrAndSources.clr) || hasResources)" class="source-content"
                            :style="[
                              isEdit ? {} : {border: 'none'},
                              updateCLRSourceLoading ? {'border-top': '1px solid #ccc', 'border-radius': '8px'} : {}
                            ]">
                            <div style="display: flex;justify-content: space-between;">
                              <span class="font-size-16 font-weight-600">{{ $t('loc.unitPlannerSources') }}:</span>
                              <!-- 添加资源管理按钮 -->
                              <el-button
                                v-if="isEdit"
                                type="primary"
                                size="medium"
                                :icon="hasAvilableResources ? 'el-icon-edit' : 'el-icon-plus'"
                                style="margin-left: 10px;"
                                :disabled="generateClassSpecificValueLoading || updateCLRSourceLoading"
                                @click="hasAvilableResources ? openResourceEditModal() : openResourceAddModal()"
                              >
                                {{ hasAvilableResources ? $t('loc.editResourceButton') : $t('loc.addResourceButton') }}
                              </el-button>
                            </div>
                            <!-- 资源内容区域 -->
                            <div class="sources" v-if="hasAvilableResources">
                              <div :id="'subscript' + source.subscript"
                                   v-for="source in lessonClrAndSources.sources.filter(s => !s.hidden)" :key="source.subscript"
                                   :style="{ 'background': (currentDocumentId === '#subscript' + source.subscript) ? '#f5f5f5' : '', 'padding': source.hidden ? '0px 10px' : equalsIgnoreCase(source.type, 'File') ? '0 10px' : '5px 10px 10px' }"
                                   class="source-item">
                                <span class="source-label">
                                  <!-- 根据是否为文件类型选择不同的展示方式 -->
                                  <template v-if="source.type === 'File'">
                                    <!-- 文件类型资源 -->
                                    <div class="file-resource-container">
                                      <div class="file-download-card" :style="{ backgroundColor: !isEdit ? 'transparent':'#f2f2f2'}">
                                        <!-- 角标和标题 -->
                                        <div class="file-title-area">
                                          <span class="source-number">[{{ source.subscript }}]. </span>
                                          <span>{{ source.sourceKeywords || "Custom Resource" }}<span v-if="!!source.source"> ({{ source.source }})</span></span>
                                        </div>

                                        <div class="file-info-area">
                                          <!-- 文件类型图标 -->
                                          <div class="file-type-icon">
                                            <img v-if="source.icon" :src="source.icon" alt="File Icon"/>
                                            <img v-else :src="getFileIconClass(source.fileName || source.sourceLink.split('/').pop())" alt="File Icon">
                                          </div>

                                          <!-- 文件名称 -->
                                          <div class="file-name" :style="{ color: !isEdit ? '#676879' : '#333' }">
                                            {{ source.fileName || source.sourceLink }}
                                          </div>
                                          <!-- 下载按钮 -->
                                          <el-tooltip content="Download" placement="top" effect="dark">
                                            <div class="download-button" @click="downloadFile(source)">
                                              <div class="download-icon-container">
                                                <i class="el-icon-download"></i>
                                              </div>
                                            </div>
                                          </el-tooltip>
                                        </div>
                                      </div>
                                    </div>
                                  </template>

                                  <template v-else>
                                    <!-- 普通资源 -->
                                    <div style="display: flex; gap: 8px">
                                      <span class="source-number">[{{ source.subscript }}]. </span>
                                      <span class="display-flex" style="flex-direction: column;gap: 5px">
                                        <span>{{ source.sourceKeywords || "Custom Resource" }}<span v-if="!!source.source"> ({{ source.source }})</span></span>
                                        <a :href="source.sourceLink" class="text-primary" target="_blank"
                                           style="text-decoration: underline; word-break: break-all">{{ source.sourceLink }}</a>
                                        <curriculum-media-viewer v-if="source.videoLink" :url="source.videoLink"
                                                               :coverMediaImg="source.cover" :preview="true"/>
                                      </span>
                                    </div>
                                  </template>
                                </span>
                              </div>
                            </div>
                            <div v-else style="line-height: 20px">
                              <span class="color-676879">{{ $t('loc.unitPlannerNotHasSources') }}</span>
                            </div>
                          </div>
                        </div>
                    </div>
                    <!-- 反馈 -->
                    <div v-show="showFeedback && !isUnitPlanner" class="class-specific-instructions" :style="{ fontSize: isEdit ? '14px' : '16px' }"
                         style="padding-top: 0;padding-bottom: 8px; align-items: flex-end">
                        <FeedbackForm :defaultFeedbackResult="defaultFeedbackResult"
                                      :feedbackStyle="setFeedbackStyle"
                                      :showFeedback="showFeedback"
                                      :showIcon="true"
                                      :showClose="false"
                                      :feedbackTitle="feedbackTitle"
                                      :feedbackSubmit="feedbackSubmit"
                                      :needFeedbackLevel="false"
                                      :feedbackInputPlaceholder="feedbackInputPlaceholder"
                                      :promptUsageRecordIds="promptUsageRecordIds"
                                      :showReminders="false"
                                      @clickFeedback="clickFeedback"/>
                    </div>
                </div>
                <!-- 普通模式下只展示普通的 CLR -->
                <div class="w-full" v-show="!isShowTab">
                    <div class="class-specific-instructions" :style="{ fontSize: isEdit ? '14px' : '16px' }">
                      <ClrEditComponent v-if="isEdit && !classSpecificValue"
                                        :showSource="showSource"
                                        :generatedLessonSources="generatedLessonSources"
                                        @updateGeneratedLessonSources="updateGeneratedLessonSources"
                                        v-model="lessonClrAndSources.clr"
                                        :canEdit="isEdit" @scrollView="toRefView"/>
                      <ClrEditComponent v-if="!isEdit && !classSpecificValue && !showSource"
                                        :showSource="showSource"
                                        :generatedLessonSources="generatedLessonSources"
                                        style="border: transparent!important;"
                                        v-model="lessonClrAndSources.clr"
                                        :canEdit="isEdit" @scrollView="toRefView"/>
                      <div class="source-body" v-show="showSource">
                        <ClrEditComponent v-if="!isEdit" style="border: transparent!important;" :showSource="showSource" v-model="lessonClrAndSources.clr" :canEdit="isEdit" @scrollView="toRefView"/>
                        <div v-if="showSource" :class="isEdit ? 'source-content' : ''">
                          <span class="font-size-16 font-weight-600">{{ $t('loc.unitPlannerSources') }}:</span>
                          <!-- 资源内容区域 -->
                          <div class="sources" v-if="hasAvilableResources">
                            <div :id="'subscript' + source.subscript"
                                 :style="{ 'background': (currentDocumentId === '#subscript' + source.subscript) ? '#f5f5f5' : '', 'padding': source.hidden ? '0px 10px' : equalsIgnoreCase(source.type, 'File') ? '0 10px' : '5px 10px 10px' }"
                                 class="source-item" v-for="(source) in lessonClrAndSources.sources"
                                 :key="source.subscript">
                                  <span class="source-label" v-if="!source.hidden">
                                    <span class="source-number">[{{ source.subscript }}]. </span>
                                    <span class="display-flex" style="flex-direction: column;gap: 5px">
                                      <span>{{ source.sourceKeywords }}<span v-if="!!source.source"> ({{ source.source }})</span></span>
                                      <a :href="source.sourceLink" class="text-primary" target="_blank" style="text-decoration: underline">{{ source.sourceLink }}</a>
                                      <curriculum-media-viewer v-if="source.videoLink" :url="source.videoLink" :coverMediaImg="source.cover" :preview="true"/>
                                    </span>
                                  </span>
                            </div>
                          </div>
                          <div v-else style="line-height: 20px">
                            <span class="color-676879">{{ $t('loc.unitPlannerNotHasSources') }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                </div>
            </div>
        </div>
      <AssignTeacherGroups ref="assignTeacherGroups"></AssignTeacherGroups>

      <!-- 资源编辑弹窗 -->
      <resource-edit-modal
        v-model="resourceEditVisible"
        :resources="lessonClrAndSources.sources || []"
        :more-resources="lessonClrAndSources.moreSources || []"
        :lesson-id="lessonId"
        type="CLR"
        @save-resources="saveResources"
        @close="onResourceEditClose"
      />

      <!-- 添加资源弹窗 -->
      <resource-add-modal
        v-model="resourceAddVisible"
        :current-resource-count="currentFileNum"
        @add-resources="handleAddResources"
        @close="onResourceAddClose"
      />
    </el-card>
</template>

<script>
import AssignTeacherGroups from '@/views/modules/lesson2/lessonPlan/components/AssignTeacherGroups.vue'
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import regenerateIcon from '@/assets/img/lesson2/plan/regenerate.svg'
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import emitter from 'element-ui/src/mixins/emitter'
import LgTabs from '@/components/LgTabs.vue'
import defaultAvatar from '@/assets/img/healthCheck/child_avatar.jpg'
import { mapState } from 'vuex'
import { createEventSource, removeUnexpectedCharacters } from '@/utils/eventSource'
import generateDefaultIcon from '@/assets/img/lesson2/assistant/generate.png'
import ClrEditComponent from '@/views/modules/lesson2/unitPlanner/components/editor/ClrEditComponent.vue'
import CurriculumMediaViewer from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMediaViewer.vue'
import ResourceSettings from './ResourceSettings.vue'
import ResourceAddModal from '@/views/modules/lesson2/unitPlanner/components/editor/ResourceAddModal.vue'
import ResourceEditModal from '@/views/modules/lesson2/unitPlanner/components/editor/ResourceEditModal.vue'
import { serverlessApiUrl } from '@/utils/setBaseUrl'
import { equalsIgnoreCase } from '@/utils/common'
import fileUtil from '@/utils/file'
export default {
    name: 'CulturallyLinguisticallyResponsive',
    components: {
      CurriculumMediaViewer,
      ClrEditComponent,
      LgTabs,
      Editor,
      FeedbackForm,
      AssignTeacherGroups,
      ResourceSettings,
      ResourceAddModal,
      ResourceEditModal
    },
    mixins: [emitter],
    props: {
        // 文化响应式教学内容
        culturallyResponsiveInstruction: {
            type: String,
            default: () => ''
        },
        // 最原始的文化响应式教学内容，如果它存在，那么就说明不是第一次了，这个值不要修改
        culturallyResponsiveInstructionGeneral: {
          type: String,
          default: () => ''
        },
        // 文化响应式教学 班级定制 内容
        culturallyResponsiveInstructionGroup: {
            type: String,
            default: () => ''
        },

        // 选择的班级 id
        selectedGroupId: {
            type: String,
            default: () => null
        },
        // 当年龄组发生改变的时候
        changeAgeGroups: {
            type: Boolean,
            default: false
        },
        // 年龄组名称
        ageGroupName: {
            type: String,
            default: ''
        },
        // 课程 id
        lessonId: {
            type: String,
            default: ''
        },
        // 当前展示的课程的 itemId
        itemId: {
            type: String,
            default: ''
        },
        // 是否是编辑状态
        isEdit: {
            type: Boolean,
            default: false
        },
        adaptUDLAndCLROpen: {
            type: Boolean,
            default: false
        },
        isWeeklyPlanEdit: {
            type: Boolean,
            default: true
        },
        getNewLessonInfo: {
            type: Function,
            default: () => {
            }
        },
        // 是否 Adapted
        isAdaptedLesson: {
          type: Boolean,
          default: false
        },
        step: {
            type: Object,
            default: null
        },
        isCurriculum: {
            type: Boolean,
            default: false
        },
        isUnitPlanner: {
          type: Boolean,
          default: false
        },
        normalClrLoading: {
          type: Boolean,
          default: false
        },
        showUnitLessonSource: {
          type: Boolean,
          default: undefined
        }
    },
    destroyed () {
      this.leavePage = true
    },
    created () {
        // 默认的 tab 展示
        this.currentTagName = this.submodules[1]
        // 进行赋值操作
        // 普通 CLR 赋值
        if (this.culturallyResponsiveInstruction) {
            this.generalValue = this.culturallyResponsiveInstruction.replace(/\n/g, '<br>')
            this.generalValueTab = this.culturallyResponsiveInstruction.replace(/\n/g, '<br>')
        }
        // 班级定制的 CLR 赋值
        if (this.culturallyResponsiveInstructionGroup) {
            // 解析里面的数据，包含 class 数据 以及小孩数据
            let culturallyResponsiveInstructionGroupWithChildren = JSON.parse(this.culturallyResponsiveInstructionGroup)
            if (culturallyResponsiveInstructionGroupWithChildren) {
                // 解析里面的 culturallyResponsiveInstructionGroup 数据
                if (culturallyResponsiveInstructionGroupWithChildren.culturallyResponsiveInstructionGroup) {
                    this.classSpecificValue = culturallyResponsiveInstructionGroupWithChildren.culturallyResponsiveInstructionGroup
                    if (this.classSpecificValue) {
                      this.classSpecificValue = this.classSpecificValue.replace(/\n/g, '<br>')
                    }
                    this.originalClassSpecificValue = this.classSpecificValue
                }
                // 解析里面的 children 数据
                if (culturallyResponsiveInstructionGroupWithChildren.children) {
                    // 非编辑状态下才会进行赋值操作
                    if (!this.isEdit) {
                        this.children = culturallyResponsiveInstructionGroupWithChildren.children
                    }
                }
            }
        }
        if (!this.step.lessonClrAndSources) {
          if (!this.classSpecificValue) {
            this.$set(this.step, 'lessonClrAndSources', {
              clr: this.generalValue,
              sources: this.step.lessonClrAndSources && this.step.lessonClrAndSources.sources.forEach(source => {
                this.$set(source, 'hidden', false)
              })
            })
          } else {
            this.$set(this.step, 'lessonClrAndSources', {
              clr: this.classSpecificValue,
              sources: this.step.lessonClrAndSources && this.step.lessonClrAndSources.sources.forEach(source => {
                this.$set(source, 'hidden', false)
              })
            })
          }
        } else {
          this.step.lessonClrAndSources && this.step.lessonClrAndSources.sources && this.step.lessonClrAndSources.sources.forEach(source => {
            this.$set(source, 'hidden', false)
          })
        }
        this.$nextTick(() => {
          // 判断资源信息列表是否存在，如果存在，则默认首先展示资源内容
          if (this.step.lessonClrAndSources && this.step.lessonClrAndSources.sources && this.step.lessonClrAndSources.sources.length > 0) {
            this.showSource = true
            // 初始化是否展示资源状态
            this.$store.dispatch('setShowClrSource', true)
          } else {
            this.showSource = false
            // 初始化是否展示资源状态
            this.$store.dispatch('setShowClrSource', false)
          }

          // 计算当前文件数量
          this.calculateCurrentFileNum();
        })
    },
    data () {
        return {
            currentTagName: '', // 当前模块名称
            defaultFeedbackResult: {
                feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
                feedBackResult: '',
                feedbackData: {
                    feedback: undefined,
                    feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
                    feedbackLevel: [0, 0, 0, 0]
                }
            }, // 默认的反馈
            promptUsageRecordIds: [], // 消息回调的 promptUsageRecordId，后续用于反馈
            defaultAvatar: defaultAvatar, // 用户默认头像
            children: [], // 全部的小孩
            getChildrenLoading: false, // 获取的小孩的 loading
            generalValue: '', // 普通的值
            generalValueTab: '', // 普通的值的赋值值
            classSpecificValue: '', // 班级定制的 CLR 的值
            originalClassSpecificValue: '', // 班级定制的原始的 CLR 的值
            classSpecificValueBak: '', // 班级定制的 CLR 的值的临时值，以免重复的 <p> 标签
            generateClassSpecificValueLoading: false, // 生成班级定制的 CLR 的 loading
            generatedCLR: false, // 是否生成过了 班级定制的 CLR
            showSource: false, // 是否显示资源
            currentDocumentId: '', // 当前的 domId
            generatedLessonSources: false, // 是否生成过 lessonSources
            resourceAddVisible: false, // 控制资源添加弹窗显示
            resourceEditVisible: false, // 控制资源编辑弹窗显示
            currentFileNum: 0, // 当前文件数量
            updateCLRSourceLoading: false, // 更新 CLR 资源 loading
            previousSubscriptCLRTags: [], // 上一次CLR中的角标标签
            leavePage: false
        }
    },
    methods: {
      equalsIgnoreCase,
      // 处理角标与资源的关联
      handleSubscriptsAndResources(value) {
        // 判断资源数据是否存在
        if (value) {
          // 获取资源列表
          const sources = value.sources;
          // 获取带有角标的 clr 内容
          const clr = value.clr;
          // 如果不存在 clr，则直接返回
          if (!clr || !sources || sources.length === 0) {
            return;
          }

          // 获取资源的角标
          const subscripts = sources.map(source => source.subscript);

          // 解析为 DOM 处理，提取角标内容
          const parser = new DOMParser();
          const doc = parser.parseFromString(clr, 'text/html');
          // 存放根据 a 标签解出来的内容
          const currentTags = [];
          // 遍历所有的 <a> 标签
          doc.querySelectorAll('a').forEach(scriptTag => {
            currentTags.push(scriptTag.textContent);
          });

          // 判断 clr 中是否存在某个角标
          subscripts.forEach(subscript => {
            // 检查当前 tags 数组中是否包含当前 subscript
            const existsInCurrentTags = currentTags.some(tag => tag.includes(`[${subscript}]`));

            // 检查上一次 tags 数组中是否包含当前 subscript
            const existsInPreviousTags = this.previousSubscriptCLRTags.some(tag => tag.includes(`[${subscript}]`));
            // 找到对应角标的资源
            const source = sources.find(src => src.subscript === subscript);
            if (source) {
              if (existsInCurrentTags) {
                // 如果当前存在，显示资源
                this.$set(source, 'hidden', false);
              } else if (!existsInCurrentTags && existsInPreviousTags) {
                // 如果当前不存在，但上次存在，则隐藏
                this.$set(source, 'hidden', true);
              } else if (!existsInCurrentTags && !existsInPreviousTags) {
                // 如果当前和上次都不存在
                if (source.noMatchSubscript) {
                  // 如果未匹配到关键词，说明自定义不需要隐藏
                  this.$set(source, 'hidden', false);
                } else {
                  // 如果匹配到关键词，但不存在，需要隐藏
                  this.$set(source, 'hidden', true);
                }
              }
            }
          });

          // 更新 previousTags 以便下次比较
          this.previousSubscriptCLRTags = [...currentTags];
        }
      },

      // 更新 lessonSources 的状态值
      updateGeneratedLessonSources () {
        this.generatedLessonSources = false
      },
        /**
         * 设置 feedback 的样式
         */
        setFeedbackStyle () {
            return {
                float: 'right',
                zIndex: '20'
            }
        },

        /**
         * 点击反馈的埋点
         */
        clickFeedback (isUp) {
            if (isUp) {
                // CLR 正反馈的埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_clr_feedback_pos')
            } else {
                // CLR 负反馈的埋点
                this.$analytics.sendEvent('web_weekly_plan_edit_lesson_clr_feedback_pos')
            }
        },
        // 去除 html 标签后，内容是否是空的
        removeHtmlTag (content) {
          // 如果内容不存在时，则说明也是空的
          if (!content) {
            return true
          }
          // 移除HTML标签
          let strippedInput = content.replace(/(<([^>]+)>)/ig,'')
          // 移除特殊字符，包括 &nbsp;
          strippedInput = strippedInput.replace(/&[^\s]*;/g, '')
          // 移除多余的空格
          strippedInput = strippedInput.replace(/\s+/g, ' ').trim()
          return strippedInput.trim() === ''
        },
        /**
         * 获取生成按钮的图标
         */
        getGenerateButtonIcon () {
            // 如果生成过展示重新生成图标
            if (this.generatedCLR) {
                return regenerateIcon
            }
            // 否则展示普通图标
            return generateDefaultIcon
        },

        /**
         * 获取生成按钮的名称
         */
        getGenerateButtonName () {
            return 'Adapt UDL and CLR for My Class'
        },

        /**
         * 更新班级定制的 CLR 内容
         */
        updateClassSpecificValue (newValue) {
          if (newValue) {
            this.classSpecificValue = newValue
            const newClr = newValue.replace(/\n/g, '<br>')
            let data = {
              clr: newClr,
              sources: this.step.lessonClrAndSources && this.step.lessonClrAndSources.sources
            }
            if (this.isUnitPlanner) {
              // 使用事件总线而不是 $emit
              this.$bus.$emit('updateClrLessonSource', data, this.itemId)
            } else {
              this.$set(this.step, 'lessonClrAndSources', data)
            }
          }
        },

        /**
         * 生成 UDL 和 CLR 数据
         */
        generateUDLAndCLRData () {
            // 向父组件发送事件，点击了按钮需要生成 UDL和 CLR 数据
            this.$emit('generateUDLAndCLRData', this.ageGroupName)
            // 添加个性化课程适应 CLR 处埋点
            this.$analytics.sendEvent('web_weekly_plan_edit_lesson_clr_adapt')
        },

        // 处理 resize 事件，需要将所有设置了 autosize 的 input 元素进行重新计算高度
        handleResize () {
            this.$nextTick(() => {
                // general 中的 input 要重新计算一下高度
                // 重新计算一下高度
                this.$refs.editorClassSpecificValueTab && this.$refs.editorClassSpecificValueTab.resizeTextarea()
                this.$refs.editorGeneralValue && this.$refs.editorGeneralValue.resizeTextarea()
            })
        },
        // 判断 autosizeInput 元素是否可以被看见
        observeElementVisibility () {
            // 获取 autosizeInput 元素
            const autosizeInputs = []
            if (this.$refs.editorClassSpecificValueTab) {
                autosizeInputs.push(this.$refs.editorClassSpecificValueTab)
            }
            if (this.$refs.editorGeneralValue) {
                autosizeInputs.push(this.$refs.editorGeneralValue)
            }
            // 如果元素存在，并且数量大于等于 1
            // 遍历循环 autosizeInputs
            autosizeInputs.forEach(autosizeInput => {
                // 获取挂载的元素
                const inputElement = autosizeInput.$el
                // 定义 DOM 检测器
                const observer = new IntersectionObserver(entries => {
                    // 输入 entries
                    // 如果元素可见
                    if (entries[0].isIntersecting) {
                        // 执行 resizeTextArea 方法
                        autosizeInput.resizeTextarea()
                        // 可以选择取消观察，如果只想执行一次
                        observer.disconnect()
                    }
                })
                // 开始观察元素
                observer.observe(inputElement)
            })
        },
        // 改编资源显示状态
        changeShowSource () {
          this.showSource = !this.showSource
          this.currentDocumentId = ''
          this.$store.dispatch('setShowClrSource', this.showSource)
        },
        // 滚动到指定的资源处
        toRefView (event) {
          // 检查点击的是否是 A 标签
          if (event.target.tagName === 'A') {
            event.preventDefault()
            // 使用 getAttribute 方法获取 href 属性的值
            let hrefValue = event.target.getAttribute('href')
            // 获取父类 source-body 元素，确保下面查询到的需要滚动到的地方是当前页面的
            const sourceBody = event.target.closest('.source-body');
            if (hrefValue && sourceBody) {
              this.currentDocumentId = hrefValue
              // 找到父类 source-body 元素下的需要滚动到的元素
              const element = sourceBody.querySelector(hrefValue);
              if (element) {
                element.scrollIntoView({behavior: 'smooth', block: 'center'})
              }
            }
          }
        },
        /**
         * 生成班级定制的 CLR 内容
         */
        async generateCLR (isRegenerateCLR, toAdapt = false) {
            this.showSource = false
            this.currentDocumentId = null
            if (!this.isShowTab) {
                // 如果是重新生成 CLR且没展示 tab 的时候，那么需要将原始的 CLR 信息赋值给 tab 的值
                this.generalValueTab = this.generalValue
            }
            // 设置开始的 loading
            this.generateClassSpecificValueLoading = true
            if (this.isUnitPlanner) {
              this.$emit('upNormalClrLoading', true)
            }
            // 发送事件，clr 开始生成数据
            this.$bus.$emit('generateCLRStart', this.itemId)
            // 重置数据
            this.classSpecificValue = ''
            this.classSpecificValueBak = ''
            // 开始生成 CLR 数据，此时清理掉原有的普通的内容
          // 构建参数
          let newLessonInfo = this.getNewLessonInfo(this.ageGroupName);
          this.generalValue = ''
          this.generalValueTab = ''
          let params = {
                lessonId: this.lessonId,
                unitId: '',
                planId: '',
                groupId: this.groupId,
                lessonInfo: newLessonInfo,
                enhance: !toAdapt
            }
            // 消息回调
            let messageCallback = (message) => {
                if (this.leavePage) {
                  return
                }
                // 更新数据
                let data = (this.classSpecificValueBak || '') + message.data
                // 去除首尾的引号
                data = data.replace(/^"|"$/g, '')
                data = removeUnexpectedCharacters(data)
                // 复制到中间变量
                this.classSpecificValueBak = data
                // 更新班级定制的 CLR 内容
                this.updateClassSpecificValue(data)
            }
            return new Promise((resolve, reject) => {
                // 生成单元概览
                createEventSource($api.urls().generateCulturallyResponsiveInstructionGroupStream, null, messageCallback, 'POST', params)
                    .then(async (res) => {
                        // 生成结束
                        // this.generateClassSpecificValueLoading = false
                        // 表示已经生成过了
                        this.generatedCLR = true
                        if (res && res.promptUsageRecordId !== '') {
                            this.promptUsageRecordIds = [res.promptUsageRecordId]
                        }
                        if (this.leavePage) {
                          return
                        }
                        // 如果是 Unit Planner 且生成结果，将 group 进行同步
                        if (this.isUnitPlanner) {
                          this.$emit('updateClrGroupLessonSource', this.getClassSpecificCLRValueWithChildren)
                        }
                        await this.generateLessonSourceByCLR(true)
                        resolve()
                    })
                    .catch(error => {
                        // 生成出错
                        // 关闭 loading
                        this.generateClassSpecificValueLoading = false
                        // 表示已经生成过了
                        this.generatedCLR = true
                        if (this.isUnitPlanner) {
                          this.$emit('upNormalClrLoading', false)
                        }
                        if (this.leavePage) {
                          return
                        }
                        // 如果 error 不是 NO_DATA，就说明是 error 的时候 reject 来的，这个时候需要展示错误提示，否则不展示
                        if (error.message && error.message !== '"NO_DATA"') {
                            this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
                        } else {
                            // 如果是空串，那么就是没有 IEP 和 ELD 小孩导致的，不再输出错误信息
                            // 清理旧数据
                            this.clearTeacherData()
                        }
                        reject(error)
                        // 此时生成任务完成，发送事件
                        this.$bus.$emit('generateCLRCompleted', this.itemId)
                    })
                if (isRegenerateCLR) {
                    // 再次生成 CLR 处埋点
                    this.$analytics.sendEvent('web_weekly_plan_edit_lesson_clr_regenerate')
                }
            })
        },
        clearTeacherData() {
            // 清理 universalDesignForLearningClassSpecial 和 teacherGroups
            this.$emit('clearCulturallyResponsiveInstructionGroup', this.ageGroupName)
            this.$set(this, 'classSpecificValue', '')
        },
        // 根据 CLR 生成资源内容
        async generateLessonSourceByCLR (needCallback = false, resolve) {
          this.generateLessonSourceLoading = true
          let culturallyResponsiveInstructionGroup = ''
          if (this.classSpecificValue) {
            culturallyResponsiveInstructionGroup = this.classSpecificValue
          } else {
            culturallyResponsiveInstructionGroup = this.generalValue
          }
          // CLR 生成结束之后，自动触发生成资源
          const params = {
            culturallyResponsiveInstructionGroup: culturallyResponsiveInstructionGroup,
            lessonId: this.lessonId,
            enhanceLesson: true
          }
          // 根据 CLR 生成资源内容
          await this.$axios.post(serverlessApiUrl + $api.urls().generateLessonSource, params).then(res => {
            if (!res.lessonClrAndSources) {
              // 如果生成失败了，则将 CLR 的值赋值给 CLR 资源
              res.lessonClrAndSources = {
                clr: this.classSpecificValue ? this.classSpecificValue : this.generalValue,
                sources: []
              }
            }
            // 如果资源信息存在，则默认首先展示资源信息内容
            if (res.lessonClrAndSources.sources && res.lessonClrAndSources.sources.length > 0) {
              this.showSource = true
              // 初始化是否展示资源状态
              this.$store.dispatch('setShowClrSource', true)
            } else {
              this.showSource = false
              // 初始化是否展示资源状态
              this.$store.dispatch('setShowClrSource', false)
            }
            res.lessonClrAndSources.sources.forEach(source => {
              // 初始化 hidden 属性为 false
              this.$set(source, 'hidden', false)
            })
            if (this.leavePage) {
              return
            }
            this.$forceUpdate()
            this.generatedLessonSources = true
            // 更新课程信息
            if (this.isUnitPlanner) {
              // 使用事件总线而不是 $emit
              this.$bus.$emit('updateClrLessonSource', res.lessonClrAndSources, this.itemId)
            } else {
              this.$emit('updateLessonSource', res.lessonClrAndSources, this.step.ageGroupName)
            }
            // 生成结束
            this.generateClassSpecificValueLoading = false
            this.generateLessonSourceLoading = false
            if (this.isUnitPlanner) {
              this.$emit('upNormalClrLoading', false)
            }
            // 此时生成任务完成，发送事件
            needCallback && this.$bus.$emit('generateCLRCompleted', this.itemId)
          }).catch(() => {
            this.generateClassSpecificValueLoading = false
            this.generateLessonSourceLoading = false
            if (this.isUnitPlanner) {
              this.$emit('upNormalClrLoading', false)
            }
            // 此时生成任务完成，发送事件
            needCallback && this.$bus.$emit('generateCLRCompleted', this.itemId)
          }).finally(() => {
            resolve && resolve()
          })
        },
        getFileIconClass(fileName) {
          return fileUtil.getFileType(fileName)
        },
        downloadFile(source) {
          fileUtil.courseDownload(source.sourceLink, source.fileName)
        },
        // 打开资源添加弹窗
        openResourceAddModal() {
          this.resourceAddVisible = true;
        },

        // 打开资源编辑弹窗
        openResourceEditModal() {
          this.resourceEditVisible = true;
        },

        // 处理添加资源的结果
        handleAddResources(resources) {
          if (!resources || resources.length === 0) {
            return;
          }

          // 创建或更新资源数组
          const currentSources = this.lessonClrAndSources.sources || [];
          const updatedResources = [...currentSources];

          // 计算新资源的起始角标
          let nextSubscript = 1; // 默认从1开始
          if (updatedResources.length > 0) {
            const maxSubscript = Math.max(
              ...updatedResources.map(source => parseInt(source.subscript) || 0)
            );
            nextSubscript = maxSubscript + 1;
          }

          // 添加新资源，并设置正确的序号
          resources.forEach(resource => {
            const newResource = {
              ...resource,
              subscript: nextSubscript.toString(),
              hidden: false
            };
            updatedResources.push(newResource);
            nextSubscript++;
          });

          // 关闭添加资源弹窗
          this.resourceAddVisible = false;

          // 调用后端API保存资源
          this.saveResourcesToBackend(updatedResources, this.lessonClrAndSources.moreSources || []);
        },

        // 处理保存资源的结果
        saveResources(resources, moreResources, content, isRestore) {
          // 关闭编辑资源弹窗
          this.resourceEditVisible = false;
          // 调用后端API保存资源
          this.saveResourcesToBackend(resources, moreResources || [], content, isRestore);
        },

        // 保存资源到后端
        saveResourcesToBackend(resources, moreResources, content, isRestore) {
          // 先更新本地资源列表，提高用户体验
          const localUpdatedData = {
            ...this.lessonClrAndSources,
            sources: resources,
            moreSources: moreResources
          };

          // 先本地更新资源，但不更新内容
          this.$set(this.step, 'lessonClrAndSources', localUpdatedData);

          // 计算当前文件数量
          this.calculateCurrentFileNum();

          // 显示加载状态 - 骨架屏
          this.updateCLRSourceLoading = true;

          // 使用课程ID
          const effectiveLessonId = this.lessonId;
          if (!effectiveLessonId) {
            this.$message.error('Missing lesson ID, cannot save resources');
            this.updateCLRSourceLoading = false; // 关闭骨架屏
            return;
          }

          // 构建请求数据
          const requestData = {
            lessonId: effectiveLessonId,
            type: 'CLR',
            lessonResource: {
              content: content || this.lessonClrAndSources.clr || this.step.culturallyResponsiveInstruction,
              sources: resources.filter(resource => !resource.hidden), // 过滤掉 hidden 为 true 的资源
              moreSources: moreResources
            },
            rollback: isRestore
          };

          // 调用API保存资源
          this.$axios.post($api.urls().updateResource, requestData)
            .then(response => {

              // 如果响应中包含更新后的资源数据，则用响应数据更新内容
              if (response && response.lessonResource) {
                const responseData = response.lessonResource;

                // 更新数据 - 此时更新内容
                const updatedData = {
                  clr: responseData.content || this.lessonClrAndSources.clr,
                  sources: responseData.sources || resources,
                  moreSources: responseData.moreSources || moreResources
                };

                // 更新本地数据
                this.$set(this.step, 'lessonClrAndSources', updatedData);

                // 同步到父组件
                this.$emit('updateLessonSource', updatedData, this.step.ageGroupName);
              }

            })
            .catch(error => {

              // 显示错误提示
              this.$message.error('Failed to save resources, please try again later');
            })
            .finally(() => {
              // 无论成功还是失败，都关闭骨架屏
              this.updateCLRSourceLoading = false;
            });
        },

        // 资源弹窗关闭处理
        onResourceAddClose() {
          this.resourceAddVisible = false;
        },

        onResourceEditClose() {
          this.resourceEditVisible = false;
        },

        // 计算当前文件数量
        calculateCurrentFileNum() {
          if (this.lessonClrAndSources && this.lessonClrAndSources.sources) {
            this.currentFileNum = this.lessonClrAndSources.sources.filter(item => equalsIgnoreCase(item.type, 'File')).length;
          } else {
            this.currentFileNum = 0;
          }
        },
    },
    computed: {
      ...mapState({
        open: state => state.common.open,
        isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin
      }),
        /**
         * el-body 的样式
         */
        bodyStyle () {
            return {
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                alignSelf: 'stretch',
                background: '#FFF',
                padding: '0px',
                margin: '0px',
                border: 'none',
                bottom: '0px'
            }
        },
        adaptUDLAndCLRFeatureOpen () {
          return this.open && this.open.adaptUDLAndCLROpen
        },
        /**
         * 判断是否展示 tab
         */
        isShowTab () {
            return (!!this.originalClassSpecificValue || !!this.classSpecificValue || !!this.generalValue) || this.generateClassSpecificValueLoading
        },
        // 是否是显示 CLR 资源按钮
        showSourcesBtn () {
          return equalsIgnoreCase(this.$route.name, 'Replicate') ||
            equalsIgnoreCase(this.$route.name, 'edit-template') ||
            equalsIgnoreCase(this.$route.name, 'EditLesson') ||
            equalsIgnoreCase(this.$route.name, 'AddLesson') ||
            equalsIgnoreCase(this.$route.name, 'unitDetail') ||
            (equalsIgnoreCase(this.$route.name, 'unit-detail-cg') && this.isCurriculumPlugin) ||
            equalsIgnoreCase(this.$route.name, 'EditLessonPlan') ||
            equalsIgnoreCase(this.$route.name, 'AddLessonPlan')
        },
        showRegenerateClr () {
          // 如果是能够生成 CLR 的，并且已经生成过了，那么就展示重新生成按钮
          if (this.canAdapter && (this.generatedCLR || this.isAdaptedLesson)) {
            // 如果当前是课程插件，并且不是 unit-detail-cg 页面，那么就不展示重新生成按钮
            if (this.isCurriculum || (this.showSourcesBtn && this.$route.name !== 'unitDetail' && (!equalsIgnoreCase(this.$route.name, 'unit-detail-cg') && this.isCurriculumPlugin))) {
              return false
            }
            return true
          } else if (this.isCurriculum || (this.showSourcesBtn && this.$route.name !== 'unitDetail' && (!equalsIgnoreCase(this.$route.name, 'unit-detail-cg') && this.isCurriculumPlugin))) {
            return false
          }
          // 默认返回
          return true
        },
        /**
         * 资源详情
         */
        lessonClrAndSources: {
          get () {
            if (!this.step || !this.step.lessonClrAndSources) {
              return { clr: '', sources: [] }
            }
            return this.step && this.step.lessonClrAndSources
          },
          set (value) {
            this.$emit('updateLessonSource', value, this.step.ageGroupName)
          }
        },

        // 是否存在可用资源
        hasAvilableResources () {
          // 如果资源不存在则返回 false
          if (!this.lessonClrAndSources) {
            return false
          }
          // 获取资源列表
          let sources = this.lessonClrAndSources.sources
          // 如果资源列表不存在则返回 false
          if (!sources) {
            return false
          }
          return sources.filter(source => !source.hidden).length > 0
        },
        // 是否有 CLR 资源
        hasResources () {
          // 如果资源不存在则返回 false
          if (!this.lessonClrAndSources) {
            return false
          }
          // 获取资源列表
          const sources = this.lessonClrAndSources.sources
          // 如果资源列表不存在则返回 false
          if (!sources || sources.length === 0) {
            return false
          }
          return sources.length > 0
        },

        /**
         * 补充的多语言信息
         */
        submodules () {
            return [this.$t('loc.unitPlannerCLRAndUDLGeneralInstructions'), this.$t('loc.unitPlannerCLRAndUDLClass-specificInstructions')]
        },
        feedbackTitle () {
            return this.$t('loc.unitPlannerFeedback')
        },
        feedbackInputPlaceholder () {
            return this.$t('loc.unitPlannerFeedbackPlaceholder')
        },
        feedbackSubmit () {
            return this.$t('loc.unitPlannerFeedbackSubmit')
        },

        /**
         * 当前用户
         */
        ...mapState({
            currentUser: state => state.user.currentUser
        }),

        /**
         * 获取用户 id
         */
        currentUserId () {
            if (!this.currentUser) {
                return ''
            }
            return this.currentUser.user_id
        },

        /**
         * 获取班级 id
         */
        groupId () {
            if (this.isCurriculumPlugin) {
              return 'CurriculumPlugin'
            }
            // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
            const selectedGroupId = sessionStorage.getItem('selectedGroupId' + this.currentUserId)
            if (selectedGroupId) {
                return selectedGroupId
            }
        },
        /**
         * 获取学校 Id
         */
        centerId () {
          // 判断 sessionStorage 中是否存在 selectedGroupId，如果 'selectedGroupId' + this.currentUserId 对应的值是存在的，那么就将它返回出
          const selectedCenterId = sessionStorage.getItem('selectedCenterId' + this.currentUserId)
          if (selectedCenterId) {
            return selectedCenterId
          }
        },

        /**
         * 判断是否显示 feedback
         */
        showFeedback () {
            // 判断 promptUsageRecordIds 中的每一个值，即对应的子数组都是存在值的
            return this.isEdit && this.generatedCLR && !this.generateClassSpecificValueLoading && this.promptUsageRecordIds.length > 0
        },

        /**
         * 判断是否是老师
         */
        canAdapter () {
            if (!this.currentUser) return false
            const { role2 = '' } = this.currentUser || {}
            let role = role2.toUpperCase()
            return role === 'COLLABORATOR' || role === 'TEACHING_ASSISTANT' || role2.toUpperCase() === 'AGENCY_ADMIN' || role2.toUpperCase() === 'SITE_ADMIN' || role2.toUpperCase() === 'AGENCY_OWNER'
        },

        /**
         * 获取普通的 CLR 信息
         */
        getGeneralCLRValue () {
            let clr = this.step.lessonClrAndSources && this.step.lessonClrAndSources.clr
            // 如果 CLR 存在，则需要将 br 标签替换为换行符
            if (clr) {
              // 去除 HTML 标签判断是否是空字符串
              if (this.removeHtmlTag(clr)) {
                return ''
              }
              clr = clr.replace(/<br>/g, '\n')
            }
            // 判断是否展示 tab 而获取不同的值
            if (this.isShowTab) {
                // 如果展示 tab，则使用 tab 中绑定的值
                return this.generalValueTab
            } else {
                // 否则获取普通的 CLR 信息
              if (clr) {
                return clr
              } else {
                return this.generalValue
              }
            }
        },

        /**
         * 附带学生信息的班级定制化 CLR 信息
         */
        getClassSpecificCLRValueWithChildren () {
            // 获取 CLR
            let clr = this.step.lessonClrAndSources && this.step.lessonClrAndSources.clr
            // 将 班级定制的 CLR 以及 对应的小孩构建成 对象
            if (!this.classSpecificValue || !clr) {
                return ''
            }
            // 如果 CLR 存在，则进行替换
            if (clr) {
              // 去除 HTML 标签判断是否是空字符串
              if (this.removeHtmlTag(clr)) {
                return ''
              }
              clr = clr.replace(/<br>/g, '\n')
            }
            let classSpecificValueWithChildrenData = {
                culturallyResponsiveInstructionGroup: clr,
                children: this.children
            }
            // 将其转化为 json 串进行保存。
            return JSON.stringify(classSpecificValueWithChildrenData)
        }
    },
    mounted () {
        // 监听窗口大小变化
        // 在组件挂载后注册resize事件监听器
        window.addEventListener('resize', this.handleResize)
        // 检测元素可见性
        this.observeElementVisibility()
        // CLR mounted 完成之后，发送事件
        if (!this.changeAgeGroups) {
            this.$bus.$emit('lessonLibraryCLRMounted', this.itemId)
        }
    },
    watch: {
        // 监测父组件传过来的 culturallyResponsiveInstruction 的变化，并对本组件内的相应对象进行赋值
        culturallyResponsiveInstruction: {
            handler (val) {
                if (val) {
                    this.generalValue = val
                    this.generalValueTab = val
                }
            },
            immediate: true
        },
        lessonClrAndSources: {
          deep: true,
          immediate: true,
          handler (value) {
            if (!value || !value.clr) {
              this.$set(this.step, 'culturallyResponsiveInstruction', '')
            }
            if (value && !value.clr && value.sources) {
              this.lessonClrAndSources.sources.forEach(source => {
                this.$set(source, 'hidden', true)
                this.currentDocumentId = ''
              })
            }

            // 处理角标与资源的关联
            this.handleSubscriptsAndResources(value);
          }
        },
      showUnitLessonSource: {
        handler (value) {
          if (!this.isUnitPlanner) {
            return
          }
          if (this.showSource !== value) {
            this.changeShowSource()
          }
        }
      }
    }
}
</script>

<style lang="less" scoped>

.CLR-header {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 16px 24px;
    align-self: center;
    color: var(--color-primary);
    background: #E8F9FA;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border: 1px solid var(--dcdfe-6, #DCDFE6);

    .header-title {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        width: 100%;
        flex-grow: 1;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 137.5% */
        height: 60px;
    }

    .regenerated-btn, .regenerated-btn:hover, .regenerated-btn:focus {
        border-radius: 4px;
        border: 2px solid var(--10-b-3-b-7, #10B3B7);
        background: var(--ffffff, #FFF);
        display: flex;
        padding: 8px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }

    .regenerated-btn:active {
        opacity: 0.5 !important;
    }

    .ai-btn, .ai-btn:hover, .ai-btn:focus {
        background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
        border-color: none !important;
        border: 0 !important;
        color: #FFFFFF;

    }

    .ai-btn:active {
        opacity: 0.5 !important;
    }

}

.CLR-body {

    width: 100%;
    border-radius: 20px;
    background: #fff;
    margin-top: -14px;
    padding: 8px;

    .class-specific-instructions {
        display: flex;
        padding: 16px;
        width: 100%;
        flex-direction: column;
        // gap: 16px;
        align-self: center;
        font-size: 16px;
    }
}

.preview-body-border {
    border: 1px solid rgba(16, 179, 183, 0.4);
}

.edit-body-border {
    border: 1px solid #DCDFE6;
}

/deep/ .clr-module-tabs {

    box-shadow: none !important;
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    padding: 16px 0 0 0;
    border: none;
    background: #FFF;

    .el-tabs__header {
        border: none !important;
        margin: 0 !important;
        width: fit-content;
        padding: 0;
    }

    .el-tabs__nav {
        padding: 4px !important;
        border-radius: 4px !important;
        gap: 6px;
        display: inline-flex;
        border: none !important;
        width: 100% !important;
        background: var(--2-ebeef-5, #EBEEF5);
        justify-content: center;
        align-items: center;
    }

    .el-tabs__item.is-active {
        background: #ffffff !important;
        color: var(--10-b-3-b-7, #10B3B7);
        font-weight: 600;
        border-radius: 4px;
    }

    .el-tabs__item {
        color: var(--111-c-1-c, #111C1C);
        font-weight: 400;
        font-size: 14px;
        height: 32px !important;
        line-height: 22px !important;
        padding: 0 12px !important;
        border-width: 0 !important;
        width: fit-content !important;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .el-tabs__content {
        padding: 0 !important;
        width: 100%;
    }

    .race-language-div {
        margin: 16px 16px 0 16px;
        display: flex;
        padding: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        align-self: stretch;
        background: var(--color-page-background-white);
    }

    .race-language-title {
        display: flex;
        width: 100%;
        align-items: flex-start;
    }

    .race-language-title-content {
        color: #000;
        font-feature-settings: 'clig' off, 'liga' off;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px; /* 157.143% */
    }

    .race-language-child {
        display: flex;
        width: 100%;
        align-items: flex-start;
        align-content: flex-start;
        gap: 8px;
        flex-wrap: wrap;
    }

    .race-language-child-content {
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        border-radius: 90px;
        padding: 0 8px;
        background: #FFF;
        line-height: 32px;
    }


    .ql-indent-1 {
        padding-left: 2em !important;
    }
}

/deep/ .el-card__header {
    padding: 0 !important;
}

.view-html-content {
    border: 1px solid #ccc;
    padding: 10px 16px;
}

.left-gradient {
    background: linear-gradient(270deg, #10B3B7 -1.16%, rgba(16, 179, 183, 0.00) 98.84%);
}

.right-gradient {
    background: linear-gradient(90deg, #10B3B7 -1.16%, rgba(16, 179, 183, 0) 98.84%)
}

.title-gradient {
    width: 45px;
    height: 2px;
    border-radius: 1px;
}

/* 差异化教学样式优化 */
.title-difference {
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    border: 1px solid rgba(16, 179, 183, 0.40);
    background: rgba(16, 179, 183, 0.10);
    padding: 8px 0;
    height: 64px !important;
    margin-top: 36px;
    text-align: center;
}

/* 描述列表标签 */
.lesson-field-label {
    font-size: 16px;
    line-height: 34px;
    height: 34px;
    font-weight: 600;
    color: #10B3B7;
    width: 100%;
}

/deep/ .el-card {
    border: none !important;
}

/deep/ .ql-container {
    height: 100%;
    overflow-y: auto;
}
.source-content {
  border: 1px solid #ccc;
  border-top: unset;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  min-height: 40px;
  padding: 0 15px 10px;

}
.source-item {
  display: flex;
  flex-direction: column;
  line-height: 20px;
  margin-bottom: 8px;
  border-radius: 8px;
  .source-label {
    width: fit-content;
    display: flex;
    gap: 8px;
  }
  .source-number {
    word-break: normal;
  }
}
.clr-content {
  line-height: 24px;
  border: 1px solid #ccc;
  border-bottom: transparent;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  padding: 10px 15px;
  white-space: pre-line;
}
/deep/ .media-viewer {
  border-radius: 8px!important;
}
.clr-style {
  /deep/ .ql-toolbar {
    display: none!important;;
  }
  /deep/ .ql-toolbar.ql-snow + .ql-container.ql-snow {
    border-top: 1px solid #ccc;
    border-bottom: 0px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
  }
}
.show-clr {
  /deep/ .ql-toolbar {
    display: none!important;;
  }
  /deep/ .ql-toolbar.ql-snow + .ql-container.ql-snow {
    border-top: 1px solid #ccc;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}
.img-size{
  height: 152px;
  width: 128px;
}
/deep/ .media-viewer {
  width: 280px!important;
}
.new-tag {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: -5px;
  top: -8px;
}
.new-tag-preview {
  padding: 2px 5px;
  border-radius: 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  background: var(--color-danger);
  color: var(--color-white);
  position: absolute;
  right: -4px;
  top: -7px;
}
.justify-content-around {
  -webkit-justify-content: space-around;
  -moz-justify-content: space-around;
  justify-content: space-around;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.resource-settings-component {
  display: inline-block;
  margin-right: 10px;
}

/deep/ .el-card__header {
    padding: 0 !important;
}

.resource-button {
  height: 32px;
  width: 114px;
  padding: 0;
}

/* 文件资源容器样式 */
.file-resource-container {
  margin: 5px 0 5px -10px;
}

.file-download-card {
  display: flex;
  border-radius: 6px;
  padding: 10px;
  width: 32vw;
  flex-direction: column !important;
}

.file-title-area {
  margin-bottom: 8px;
}

.file-info-area {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.file-type-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-type-icon img {
  height: 28px;
  width: auto;
}

.file-name {
  font-size: 14px;
  word-break: break-all;
  flex: 1;
  width: 90%;
}

.download-button {
  cursor: pointer;
}

.download-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.download-icon-container i {
  color: #10b3b7;
  font-size: 20px;
}
</style>
