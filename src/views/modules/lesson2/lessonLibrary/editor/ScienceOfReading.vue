<template>
  <div v-if="edit" class="social-of-reading">
    <div class="sor-title">{{ $t('loc.scienceOfReading1') }}</div>
    <el-form class="display-flex flex-direction-col"
             style="width: 100%; padding: 0px 0px 0px 0px !important;"
             :rules="rules" ref="sorForm"
             label-position="top" label-width="80px" :model="sorForm">
      <div class="w-full display-flex gap-24">
        <el-form-item class="add-width-5" :label="$t('loc.scienceOfReading2')" prop="activityTitle">
          <el-input v-model="sorForm.activityTitle"
                    :maxlength="100"
                    @input="updateLessonScienceOfReadingActivityTitle"
                    :placeholder="$t('loc.scienceOfReading3')">>
          </el-input>
        </el-form-item>
        <el-form-item class="add-width-5" :label="$t('loc.scienceOfReading4')" prop="measureIds">
          <!--测评点选项-->
          <el-select
            v-model="sorForm.measureIds"
            multiple
            :placeholder="$t('loc.scienceOfReading5')"
            @change="updateLessonScienceOfReadingMeasures"
            style="width: 100%"
          >
            <el-option
              v-for="item in scienceOfReadingMeasures.children"
              :key="item.id"
              :label="item.abbreviation"
              :value="item.id"
              @mouseenter.native="isHover = item.id"
              @mouseleave.native="isHover = null"> <!-- 绑定到父元素 -->
              <span :style="getSpanStyle(item, false)">
        {{ item.abbreviation }}
    </span>
              <span v-if="!!item.abbreviation && !!item.name" :style="getSpanStyle(item, false)">
        -
    </span>
              <span :style="getSpanStyle(item, true)">
        {{ item.name }}
    </span>
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <el-form-item class="w-full editor-sor" prop="content">
        <editor v-model="sorForm.content" @blur="handleBlur"
                @input="updateLessonScienceOfReadingContent"
                :placeholder="$t('loc.scienceOfReading6')"/>
      </el-form-item>
    </el-form>
  </div>
  <!--预览的情况-->
  <div v-else>
    <div class="social-of-reading-preview">
      <div class="preview-header">
        <div style="flex: 1 1 0">
          <span class="header-title">{{ $t('loc.scienceOfReading2') }}: </span>
          <span class="header-content">{{ sorForm.activityTitle }}</span>
        </div>
        <div style="flex: 1 1 0; cursor: default;" class="display-flex align-items flex-wrap gap-8">
          <span class="header-title">{{ $t('loc.scienceOfReading4') }}:</span>
          <el-tooltip v-for="measure in measureList"
                      :key="measure.id" :open-delay="Number(300)" effect="dark" placement="top">
            <div slot="content" style="width: 300px;font-size: 14px;white-space: pre-wrap;">
              <span>{{ measure.abbreviation }}</span>
              <span v-if="!!measure.abbreviation && !!measure.name"> - </span>
              <span style="font-size: 14px">{{ measure.name }}</span><br/>
              <span>Description: {{ measure.description }}</span>
            </div>
            <el-tag type="info" size="small">
              {{ measure.abbreviation }}
            </el-tag>
          </el-tooltip>
        </div>
      </div>
      <div class="preview-description">
        <!--使用 auto 的 overflow-->
        <div style="flex: 1 1 0; overflow: auto; font-size: 16px" v-html="sorForm.content">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Editor from '@/views/modules/lesson2/component/editor'
import {ScienceOfReadingMeasures} from '@/utils/constants'

export default {
  name: 'ScienceOfReading',
  components: {
    Editor,
  },
  props: {
    // 课程 ID
    lessonId: {
      type: String,
      default: ''
    },
    // 课程名称
    lessonName: {
      type: String,
      default: ''
    },
    // 课程年级
    ageGroup: {
      type: String,
      default: ''
    },
    // 是否为编辑状态
    edit: {
      type: Boolean,
      default: false
    },
    // 是否为预览状态
    preview: {
      type: Boolean,
      default: false
    },
    // SOR 表单数据
    lessonScienceOfReadingModel: {
      type: Object,
      default: () => {
        return {
          activityTitle: '',
          measureIds: [],
          content: '',
          measureList: []
        }
      }
    }
  },
  data() {
    return {
      // SOR 表单
      sorForm: {
        // 活动标题
        activityTitle: '',
        // 测评点 ID 集合
        measureIds: [],
        // 活动描述
        content: '',
        // 测评点集合
        measureList: []
      },
      isHover: null, // 当前悬浮的项的 id
      // SOR 表单校验规则
      rules: null,
      // SOR 活动标准选项
      scienceOfReadingMeasures: ScienceOfReadingMeasures
    }
  },
  computed: {
    // 测评点列表
    measureList() {
      // 如果是预览状态，则直接返回 measureIds 中的测评点
      if (this.preview) {
        return this.scienceOfReadingMeasures.children.filter(item => this.sorForm.measureIds.includes(item.id))
      } else {
        // 过滤掉不在 measureIds 中的测评点
        const dbMeasure = this.sorForm.measureList.filter(item => this.sorForm.measureIds.includes(item.id))
        // 如果测评点列表为空，则从测评点选项中获取
        if (dbMeasure.length === 0) {
          return this.scienceOfReadingMeasures.children.filter(item => this.sorForm.measureIds.includes(item.id))
        }
        return [...dbMeasure] // 返回新的数组引用
      }
    }
  },
  created() {
    // 初始化 SOR 表单校验规则
    this.initSorRules()
  },
  methods: {
    handleBlur() {
      this.$emit('update', this.sorForm)
    },
    // 获取 HTML 中的数据，考虑 img 标签
    innerText(html) {
      // 如果没有值，则返回空字符串
      if (!html) return false
      // 创建一个 div 元素
      const htmlDivElement = document.createElement('div')
      // 将 HTML 字符串设置为 div 的 innerHTML
      htmlDivElement.innerHTML = html
      // 检查是否有 img 标签
      const hasImg = htmlDivElement.getElementsByTagName('img').length > 0
      // 获取 div 的 innerText
      const text = htmlDivElement.innerText.trim()
      // 返回是否有 img 标签或者文本内容
      return hasImg || text
    },
    // 初始化 SOR 表单校验规则
    initSorRules() {
      // 定义多语言
      const language = this.$t('loc.scienceOfReading7')
      // 触发方式
      let triggerConfig = 'manual'
      // 表单的校验规则是，要么三者都填写，要么三者都不填写
      this.rules = {
        activityTitle: [
          {
            validator: (rule, value, callback) => {
              const standardsValue = this.sorForm.measureIds; // 获取 measureIds 的值
              const descriptionValue = this.sorForm.content; // 获取 content 的值

              // 判断 measureIds 是否为空（数组长度是否为 0）
              const isStandardsEmpty = !standardsValue || standardsValue.length === 0;

              // 如果三者都为空，或者三者都不为空，则通过校验
              if (
                (!value && isStandardsEmpty && !descriptionValue) ||
                (value && !isStandardsEmpty && descriptionValue)
              ) {
                callback();
              } else {
                // 判断是否是当前字段触发的校验
                if (!value) {
                  callback(new Error(language));
                } else {
                  callback(); // 如果不是当前字段触发的校验，不弹出错误
                }
              }
            },
            trigger: triggerConfig, // 禁用默认的 trigger
          },
        ],
        measureIds: [
          {
            validator: (rule, value, callback) => {
              const activityTitleValue = this.sorForm.activityTitle; // 获取 activityTitle 的值
              const descriptionValue = this.sorForm.content; // 获取 content 的值

              // 判断 measureIds 是否为空（数组长度是否为 0）
              const isStandardsEmpty = !value || value.length === 0;

              // 如果三者都为空，或者三者都不为空，则通过校验
              if (
                (isStandardsEmpty && !activityTitleValue && !descriptionValue) ||
                (!isStandardsEmpty && activityTitleValue && descriptionValue)
              ) {
                callback();
              } else {
                // 判断是否是当前字段触发的校验
                if (isStandardsEmpty) {
                  callback(new Error(language));
                } else {
                  callback(); // 如果不是当前字段触发的校验，不弹出错误
                }
              }
            },
            trigger: triggerConfig, // 禁用默认的 trigger
          },
        ],
        content: [
          {
            validator: (rule, value, callback) => {
              const activityTitleValue = this.sorForm.activityTitle; // 获取 activityTitle 的值
              const standardsValue = this.sorForm.measureIds; // 获取 measureIds 的值

              // 判断 measureIds 是否为空（数组长度是否为 0）
              const isStandardsEmpty = !standardsValue || standardsValue.length === 0;
              const innerText = this.innerText(value)
              // 如果三者都为空，或者三者都不为空，则通过校验
              if (
                (!innerText && !activityTitleValue && isStandardsEmpty) ||
                (innerText && activityTitleValue && !isStandardsEmpty)
              ) {
                callback();
              } else {
                // 判断是否是当前字段触发的校验
                if (!innerText) {
                  callback(new Error(language));
                } else {
                  callback(); // 如果不是当前字段触发的校验，不弹出错误
                }
              }
            },
            trigger: triggerConfig, // 禁用默认的 trigger
          },
        ],
      };
      this.$forceUpdate()
    },
    // 更新 SOR 活动标题
    updateLessonScienceOfReadingActivityTitle(value) {
      // 如果 value 全是空格，则清空
      if (value && value.trim() === '') {
        this.sorForm.activityTitle = ''
      } else if (value) {
        // 更新 SOR 表单数据
        this.updateLessonScienceOfReadingModel()
      }
      // 校验活动标题
      this.$refs.sorForm.validateField('activityTitle')
      // 若测评点和 SOR 活动描述都为空，则也需要校验，去除错误提示
      if (!value && this.sorForm.measureIds.length === 0 && !this.sorForm.content) {
        this.$refs.sorForm.validateField('measureIds')
        this.$refs.sorForm.validateField('content')
      }
    },
    // 更新 SOR 测评点
    updateLessonScienceOfReadingMeasures() {
      this.$refs.sorForm.validateField('measureIds')
      // 更新 SOR 表单数据
      this.updateLessonScienceOfReadingModel()
      // 若标题和 SOR 活动描述都为空，则也需要校验，去除错误提示
      if (!this.sorForm.activityTitle && this.sorForm.measureIds.length === 0 && !this.sorForm.content) {
        this.$refs.sorForm.validateField('activityTitle')
        this.$refs.sorForm.validateField('content')
      }
    },
    // 更新 SOR 活动描述
    updateLessonScienceOfReadingContent(contentValue) {
      // 校验活动描述
      if (contentValue && contentValue.trim() === '') {
        this.sorForm.content = ''
      }
      this.$refs.sorForm.validateField('content')
      // 若标题和测评点都为空，则也需要校验，去除错误提示
      if (!this.sorForm.activityTitle && this.sorForm.measureIds.length === 0 && !this.sorForm.content) {
        this.$refs.sorForm.validateField('activityTitle')
        this.$refs.sorForm.validateField('measureIds')
      }
      // 更新 SOR 表单数据
      this.updateLessonScienceOfReadingModel()
    },
    // 更新 SOR 表单数据
    updateLessonScienceOfReadingModel() {
      // 校验方式修改，如果输入一个内容，另外两个框自动校验
      this.$emit('updateLessonScienceOfReadingModel', this.sorForm, this.ageGroup)
    },
    getSpanStyle(item, isName = false) {
      const isActive = this.sorForm.measureIds.includes(item.id) || this.isHover === item.id;
      return {
        color: isActive ? '#10b3b7' : (isName ? 'var(--color-text-placeholder)' : 'var(--color-text-primary)')
      };
    }
  },
  watch: {
    lessonScienceOfReadingModel: {
      handler(val) {
        // 如果有值，则更新表单数据
        if (val) {
          this.sorForm = val
        } else {
          // 否则清空表单数据
          this.sorForm = {
            activityTitle: '',
            measureIds: [],
            content: '',
            measureList: []
          }
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.social-of-reading {
  align-items: flex-start;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.social-of-reading .sor-title {
  color: var(--color-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
}

.social-of-reading-preview {
  width: 100%;
  padding-left: 20px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
  display: inline-flex;

  .preview-header {
    align-self: stretch;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 4px;
    display: inline-flex;

    .header-title {
      color: #111C1C;
      font-size: 16px;
      font-family: Inter;
      font-weight: 600;
      line-height: 24px;
      word-wrap: break-word;
    }

    .header-content {
      color: #111C1C;
      font-size: 16px;
      font-family: Inter;
      font-weight: 400;
      line-height: 24px;
      word-wrap: break-word;
    }
  }

  .preview-description {
    align-self: stretch;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    display: inline-flex;
  }
}


/deep/ .el-tag.el-tag--info {
    color:#111c1c;
}


@media screen and (min-width: 1600px) {
  .social-of-reading /deep/ {
    .el-form-item {
      margin-bottom: 18px !important;
      display: inline-block;
    }
  }
}

/deep/ .el-form-item__content {
  line-height: initial !important;
}

/deep/ .el-form-item__label {
  font-size: 14px !important;
}

/deep/ .el-form--label-top .el-form-item__label {
  padding: 0 0 4px 0 !important;
}

.selected-option {
  color: #10b3b7 !important; /* 强制设置选中的项颜色 */
}

@media screen and (min-width: 1600px) {

  /deep/ .content {
    margin: 0 0 20px 0;
    min-width: 1147px;

    form {
      width: 1115px;
      background-color: #fff;
      padding: 0px 0px 0px 0px !important;
    }
  }
}

.editor-sor {
  /deep/ .ql-toolbar.ql-snow {
    border: 1px solid rgb(220, 223, 230);
    border-bottom: none;
  }
  /deep/ .ql-container.ql-snow {
    border: 1px solid rgb(220, 223, 230);
    border-top: none;
  }
  /deep/ .editor .ql-editor.ql-blank:before  {
    font-size: 14px;
  }
}

</style>
