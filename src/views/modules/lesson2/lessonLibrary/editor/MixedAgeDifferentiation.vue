<template>
  <div v-if="isNotEdit" class="w-full lesson-field-value ql-editor remove-padding content-difference">
    <div class="mixed-age-body">
      <!--负责切换的 tab -->
      <div class="display-flex add-padding-t-16 add-padding-b-16 justify-content align-items flex-element-center w-full">
        <!--如果没有开启分组-->
        <div class="w-full class-specific-instructions lg-padding-16">
          <!--For Learners with IEPs -->
          <div
            class="mixed-age-differentiations">
            <table v-show="showMixedAgeTable" class="learners-table domain-table">
              <thead style="background: #96C13D33;">
              <tr class="font-size-16 table-row">
                <!--th 扩展为两列-->
                <th colspan="3" style="text-align:center; padding-left: 15px; font-weight: 600;">
                  <div class="display-flex justify-content align-items gap-8">
                    <div>
                      <span ref="newPoint" v-show="showNewPointer" class="newPoint point-relative">{{ $t('loc.new') }}</span>
                      <span>{{ $t('loc.mixAgeGroup1') }}</span>
                    </div>
                    <el-tooltip effect="dark"
                                class="display-flex align-items"
                                popper-class="max-width-300 font-size-14"
                                :content="unitDetail ? $t('loc.mixAgeGroup16') : $t('loc.mixAgeGroup17')"
                                placement="bottom">
                      <div class="lg-pointer">
                        <i class="lg-icon lg-icon-question font-size-18 font-normal" style="color: var(--color-text-primary)"></i>
                      </div>
                    </el-tooltip>
                  </div>
                </th>
              </tr>
              </thead>
              <tbody>
              <template v-for="ageDifferentiation in mixedAgeDifferentiations">
                <tr :key="ageDifferentiation.ageGroup" class="table-row">
                  <td rowspan="2" style="background: #96C13D33;">
                    <div class="activity-description">{{ageDifferentiation.ageGroup}}</div>
                  </td>
                  <td style="background: #96C13D33;">
                    <div class="activity-description" v-html="activityDescription"></div>
                  </td>
                  <td class="w-full">
                    <div class="content-description">
                      <div
                           class="textarea-hover-padding"
                      >
                        <span class="differentiation-content" v-html="formatContentWrap(ageDifferentiation.activity)"></span>
                      </div>
                    </div>
                  </td>
                </tr>
                <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                <tr :key="ageDifferentiation.ageGroup" class="table-row">
                  <td style="background: #96C13D33;">
                    <div class="activity-description" v-html="support"></div>
                  </td>
                  <td class="w-full">
                    <div class="content-description font-size-16">
                      <div
                           class="textarea-hover-padding"
                      >
                        <span class="differentiation-content" v-html="formatContentWrap(ageDifferentiation.support)"></span>
                      </div>
                    </div>
                  </td>
                </tr>
              </template>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--在编辑状态下-->
  <div v-else  class="w-full border-radius-14">
    <div class="mixed-age-body">
      <!--负责切换的 tab -->
      <div class="display-flex justify-content align-items flex-element-center w-full">
        <!--如果没有开启分组-->
        <div
          class="class-specific-instructions">
          <!--For Learners with IEPs -->
          <div class="mixed-age-differentiations">
            <table v-show="showMixedAgeTable" class="learners-table domain-table">
              <thead style="background: #96C13D33;">
              <tr class="font-size-16 table-row">
                <!--th 扩展为两列-->
                <th colspan="3" style="text-align:center; padding-left: 15px; font-weight: 600;">
                  <div class="display-flex justify-content align-items gap-8">
                    <div>
                      <span ref="newPoint" v-show="showNewPointer" class="newPoint point-relative">{{ $t('loc.new') }}</span>
                      <span>{{ $t('loc.mixAgeGroup1') }}</span>
                    </div>
                    <el-tooltip effect="dark"
                                class="display-flex align-items"
                                popper-class="max-width-300 font-size-14"
                                :content="unitDetail ? $t('loc.mixAgeGroup16') : $t('loc.mixAgeGroup17')"
                                placement="bottom">
                      <div class="lg-pointer">
                        <i class="lg-icon lg-icon-question font-size-18 font-normal" style="color: var(--color-text-primary)"></i>
                      </div>
                    </el-tooltip>
                    <!--<el-button class="regenerated-btn header-button"-->
                    <!--           @click="generateLessonMixedAgeGroupStream(null)">-->
                    <!--  <img src="~@/assets/img/lesson2/plan/regenerate.svg" alt="">-->
                    <!--</el-button>-->
                  </div>
                </th>
                <!-- <th style="width:45%; text-align:left; padding-left: 15px;">{{ $t('loc.example') }}</th> -->
              </tr>
              </thead>
              <tbody>
              <template v-for="ageDifferentiation in mixedAgeDifferentiations">
                <tr :key="ageDifferentiation.ageGroup" class="table-row">
                  <td rowspan="2" style="background: #96C13D33;">
                    <div class="activity-description">
                      <el-skeleton :rows="3" animated
                                   :loading="(generateMixedAgeLoading || loading) && (!ageDifferentiation.ageGroup || ageDifferentiation.ageGroup === '')">
                        <template slot="template">
                          <el-skeleton-item variant="text"  style="width: 60px;margin-bottom: 30px;"/>
                        </template>
                        <template slot="default">
                          <div style="width: 60px;margin-bottom: 30px;">{{ageDifferentiation.ageGroup}}</div>
                        </template>
                      </el-skeleton>
                    </div>
                  </td>
                  <td style="background: #96C13D33;">
                    <div class="activity-description" v-html="activityDescription"></div>
                  </td>
                  <td class="w-full">
                    <div class="content-description">
                      <el-skeleton :rows="3" animated
                                   class="textarea-hover-padding-skeleton w-full"
                                   :loading="(generateMixedAgeLoading || loading) && (!ageDifferentiation.activity || ageDifferentiation.activity === '')">
                        <template>
                          <el-input
                            ref="autosizeInput"
                            type="textarea"
                            :placeholder="$t('loc.mixAgeGroup18')"
                            autosize
                            v-model="ageDifferentiation.activity">
                          </el-input>
                        </template>
                      </el-skeleton>
                    </div>
                  </td>
                </tr>
                <!--接下来是多个小孩的循环,第一列统一都是 Support,这一列要进行合并单元格.第二列需要循环 children-->
                <tr :key="ageDifferentiation.ageGroup" class="table-row">
                  <td style="background: #96C13D33;">
                    <div class="activity-description" v-html="support"></div>
                  </td>
                  <td class="w-full">
                    <div class="content-description font-size-14 remove-padding">
                      <el-skeleton :rows="3" animated
                                   class="textarea-hover-padding-skeleton w-full"
                                   :loading="(generateMixedAgeLoading || loading) && (!ageDifferentiation.support || ageDifferentiation.support === '')">
                        <template>
                          <div
                            class="el-textarea font-size-14 lg-no-toolbar-editor teacherChildrenContent"
                            type="textarea"
                            autosize>
                            <editor v-model="ageDifferentiation.support"
                                    :placeholder="$t('loc.mixAgeGroup19')"/>
                          </div>
                        </template>
                      </el-skeleton>
                    </div>
                  </td>
                </tr>
              </template>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FeedbackForm from '@/views/curriculum/components/FeedbackForm.vue'
import {mapState} from 'vuex'
import {createEventSource, parseStreamData} from '@/utils/eventSource'
import LessonApi from '@/api/lessons2'
import Editor from '@/views/modules/lesson2/component/editor/index.vue'

export default {
  name: 'MixedAgeDifferentiation',
  components: {Editor, FeedbackForm},
  props: {
    // 混合年龄组的数据
    mixedAgeDifferentiation: {
      type: String,
      default: ''
    },
    isNotEdit: {
      type: Boolean,
      default: false
    },
    lessonId: {
      type: String,
      default: ''
    },
    groupId: {
      type: String,
      default: ''
    },
    // 当前展示的课程的 itemId
    itemId: {
      type: String,
      default: ''
    },
    // 当年龄组发生改变的时候
    changeAgeGroups: {
      type: Boolean,
      default: false
    },
    lessonStepIndex: {
      type: Number,
      default: 0
    },
    lessonAgeGroup: {
      type: String,
      default: ''
    },
    isWeeklyPlanEdit: {
      type: Boolean,
      default: true
    },
    adaptUDLAndCLROpen: {
      type: Boolean,
      default: false
    },
    getNewLessonInfo: {
      type: Function,
      default: () => {
      }
    },
    loading: {
      type: Boolean,
      default: false
    },
    isAdaptedLesson: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 初始化混合年龄组的数据
      mixedAgeDifferentiations: [
        {
          ageGroup: '',
          activity: '',
          support: ''
        }
      ], // 混合年龄组的数据，通过解析 mixedAgeDifferentiation 得到
      generateMixedAgeLoading: false, // 生成混合年龄组数据的按钮的 loading 状态
      originalMixedAgeDifferentiation: '', // 原始的混合年龄组数据
      defaultFeedbackResult: {
        feedbackId: '00000000-0000-0000-0000-000000000000', // 反馈 ID,
        feedBackResult: '',
        feedbackData: {
          feedback: undefined,
          feedbackLevelLabel: ['Age appropriateness', 'Flexibility in adhering to the original lesson plan ', 'Alignment with standards', 'Ease of use and clarity of instructions'],
          feedbackLevel: [0, 0, 0, 0]
        }
      }, // 默认的反馈
      promptUsageRecordIds: [], // 消息回调的 promptUsageRecordId，后续用于反馈
      showNewPointer: true
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
    }),
    feedbackTitle() {
      return this.$t('loc.unitPlannerFeedback')
    },
    feedbackInputPlaceholder() {
      return this.$t('loc.unitPlannerFeedbackPlaceholder')
    },
    feedbackSubmit() {
      return this.$t('loc.unitPlannerFeedbackSubmit')
    },
    // 拼接普通的 UDL 数据
    getMixedAgeDifferentiationData() {
      return JSON.stringify(this.mixedAgeDifferentiations)
    },
    // unit 详情改编课程时显示操作栏
    unitDetail() {
      return this.$route.name === 'unitDetail' || this.$route.name === 'unitPlanner'
    },
    currentUserId() {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    },
    // 内容换行符替换
    formatContentWrap() {
      return function (content) {
        if (!content) {
          return ''
        }
        // 将 html 标签与标签之间的任意字符替换为空
        return content.trim().replace(/\n/g, '').replace(/\>\s+\</g, '><')
      }
    },
    activityDescription() {
      return this.$t('loc.activityDescription')
    },
    support() {
      return this.$t('loc.unitPlannerLessonSupport')
    },
    // 是否展示混合年龄组的表格
    showMixedAgeTable() {
      if (this.isNotEdit) {
        return this.mixedAgeDifferentiations && this.mixedAgeDifferentiations.length > 0
          && this.mixedAgeDifferentiations.some(item => item.activity !== '' || item.support !== '')
      }
      // 如果 mixedAgeDifferentiations 存在，并且长度大于 0，并且其中的 ageGroup 不为空
      return this.mixedAgeDifferentiations && this.mixedAgeDifferentiations.length > 0
        && this.mixedAgeDifferentiations.some(item => item.ageGroup !== '' || item.activity !== '' || item.support !== '')
    }
  },
  methods: {
    /**
     * 初始化显示 New
     */
    initShowNewPointer() {
      const item = sessionStorage.getItem('mixedAgeDifferentiation' + this.currentUserId);
      this.showNewPointer = !item;
    },
    // 判断 autosizeInput 元素是否可以被看见
    observeElementVisibility() {
      // 获取 autosizeInput 元素
      const autosizeInputs = this.$refs.autosizeInput

      // 如果元素存在，并且数量大于等于 1
      if (autosizeInputs && autosizeInputs.length > 1) {
        // 遍历循环 autosizeInputs
        autosizeInputs.forEach(autosizeInput => {
          // 获取挂载的元素
          const inputElement = autosizeInput.$el
          // 定义 DOM 检测器
          const observer = new IntersectionObserver(entries => {
            // 输入 entries
            // 如果元素可见
            if (entries[0].isIntersecting) {
              // 执行 resizeTextArea 方法
              autosizeInput.resizeTextarea()

              // 可以选择取消观察，如果只想执行一次
              observer.disconnect()
            }
          })

          // 开始观察元素
          observer.observe(inputElement)
        })
      }
    },
    // 生成课程的混合年龄组数据
    generateLessonMixedAgeGroupStream(params) {
      // 如果正在生成，那么此时就不在允许点击
      if (this.generateMixedAgeLoading) {
        return
      }
      this.generateMixedAgeLoading = true
      // 清空混龄数据
      this.clearOldData()
      // 重置数据 originalMixedAgeDifferentiation
      this.$set(this, 'originalMixedAgeDifferentiation', null)
      // 生成单元概览参数
      if (!params) {
        params = {
          lessonId: this.lessonId,
          groupId: this.groupId,
          curAgeGroup: this.lessonAgeGroup,
          lessonInfo: this.getNewLessonInfo(this.lessonAgeGroup)
        }
      }
      // 消息回调`
      let messageCallback = (message) => {
        // 更新数据
        let data = (this.originalMixedAgeDifferentiation || '') + message.data
        data = data.replace(/```.*$/, '')
        this.processMixedAgeGroupStream(data)
        this.$set(this, 'originalMixedAgeDifferentiation', data)
      }
      // 生成单元概览
      return createEventSource($api.urls().generateLessonMixedAgeGroupStream, null, messageCallback, 'POST', params)
        .then((res) => {
          // 生成结束
          this.generateMixedAgeLoading = false
          // 记录 promptUsageRecordId
          if (res && res.promptUsageRecordId !== '') {
            // 使用 $set this.promptUsageRecordIds.push(res.promptUsageRecordId)
            this.$set(this, 'promptUsageRecordIds', [res.promptUsageRecordId])
            return res.promptUsageRecordId
          }
        })
        .catch(error => {
          // 设置加载状态为 0
          this.$set(this, 'generateMixedAgeLoading', false)
          // 生成出错
          this.$message.error(this.$t('loc.generateActionPlanErrorMessage'))
          return ''
        })
    },
    /**
     * 处理混合年龄组的数据
     * @param originalData
     */
    processMixedAgeGroupStream(originalData) {
      // 将 val 转化为对象
      let mixedAgeGroups = parseStreamData(originalData, [
        {key: 'ageGroup', name: ['Adapted age group:', 'Adapted age group']},
        {key: 'activity', name: ['Activity Description:', 'Activity Description']},
        {key: 'support', name: ['Support:', 'Support']}
      ])
      // 如果 mixedAgeGroups 存在，并且长度大于 0
      if (mixedAgeGroups && mixedAgeGroups.length > 0) {
        // mixedAgeGroups 中的 ageGroup 需要去除 :
        mixedAgeGroups.forEach((item) => {
          if (item.ageGroup && item.ageGroup.trim() !== '') {
            item.ageGroup = item.ageGroup.replace(/:/g, '')
          }
        })
        // 设置 mixedAgeDifferentiations
        this.$set(this, 'mixedAgeDifferentiations', mixedAgeGroups)
      }
    },
    /**
     * 处理 resize 事件，需要将所有设置了 autosize 的 input 元素进行重新计算高度
     */
    handleResize() {
      // 渲染一下页面
      this.$nextTick(() => {
        if (this.$refs.autosizeInput && this.$refs.autosizeInput.length >= 1) {
          this.$refs.autosizeInput.forEach((item) => {
            item.resizeTextarea()
          })
        }
      })
      this.$nextTick(() => {
        // general 中的 input 要重新计算一下高度
        if (!this.isNotEdit) {
          // 重新计算一下高度
          this.$refs.generalLearningIepActivity && this.$refs.generalLearningIepActivity.resizeTextarea()
          this.$refs.generalLearningIepSupport && this.$refs.generalLearningIepSupport.resizeTextarea()
          this.$refs.generalEnglishLanguageActivity && this.$refs.generalEnglishLanguageActivity.resizeTextarea()
          this.$refs.generalEnglishLanguageSupport && this.$refs.generalEnglishLanguageSupport.resizeTextarea()
        }
      })
    },
    // 清理旧数据
    clearOldData() {
      this.$set(this, 'mixedAgeDifferentiations', [
        {
          ageGroup: '',
          activity: '',
          support: ''
        }
      ])
    },
    /**
     * 处理混合年龄组的数据
     */
    processMixedAgeDifferentiations(mixedAgeDifferentiations) {
      // 如果 mixedAgeDifferentiations 不存在，那么就直接返回
      if (!mixedAgeDifferentiations || mixedAgeDifferentiations.trim() === '') {
        return
      }
      // 解析 mixedAgeDifferentiations
      try {
        this.mixedAgeDifferentiations = JSON.parse(mixedAgeDifferentiations)
        this.$emit('updateMixedAgeDifferentiation', mixedAgeDifferentiations)
      } catch (e) {
        // 如果解析失败，那么就直接返回
        this.mixedAgeDifferentiations = []
        this.$emit('updateMixedAgeDifferentiation', mixedAgeDifferentiations)
      }
    }
  },
  mounted() {
    // 初始化显示 New
    this.initShowNewPointer()
    // 监听窗口大小变化
    // 在组件挂载后注册resize事件监听器
    window.addEventListener('resize', this.handleResize)
    // 检测元素可见性
    this.observeElementVisibility()
    // 混合年龄组 mounted 完成之后，发送事件
    if (!this.changeAgeGroups) {
      this.$bus.$emit('lessonLibraryUDLMounted', this.itemId)
    }
    this.$nextTick(() => {
      // 处理混合年龄组的数据
      this.processMixedAgeDifferentiations(this.mixedAgeDifferentiation)
    })
    // 监听页面滚动
    window.document.addEventListener('mousewheel', this.handleScroll, true) || window.document.addEventListener('DOMMouseScroll', this.handleScroll, true)
  },
  watch: {
    mixedAgeDifferentiation: {
      handler(val) {
        this.$nextTick(() => {
          this.processMixedAgeDifferentiations(val)
          // 处理 Universal Design for Learning 数据完成之后，resize 一下
          this.handleResize()
        })
      },
      immediate: true,
      deep: true
    }
  }
}

</script>

<style lang="less" scoped>
.mixed-age-body {
  width: 100%;

  .class-specific-instructions {
    display: flex;
    padding: 0px !important;
    width: 100%;
    flex-direction: column;
    align-items: flex-end;
    gap: 16px;
    align-self: center;

    .mixed-age-differentiations {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      align-self: center;
      border-radius: 4px;
      width: 100%;

      .learners-table {
        table {
          border-collapse: collapse;
          width: 100%;
        }

        .point-relative {
          position: relative;
          bottom: 2px;
          right: 5px;
        }
        .activity-description {
          display: flex;
          padding: 12px;
          word-break: keep-all;
          justify-content: center;
          align-items: center;
          align-self: center;
          color: var(--color-text-secondary);
          text-align: center;
          font-feature-settings: 'clig' off, 'liga' off;

          /* Semi Bold/16px */
          font-family: Inter;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px; /* 150% */
        }

        .content-description {
          display: flex;
          width: 100%;
          justify-content: space-between;
          align-items: flex-start;
          flex: 1 0 0;
          color: var(--color-text-primary);
          font-feature-settings: 'clig' off, 'liga' off;

          /* Regular/16px */
          font-family: Inter;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 150% */
        }

        .child-content {
          display: flex;
          padding: 16px 20px;
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
          flex: 1 0 0;

          .child-description {
            display: flex;
            width: fit-content;
            align-items: center;
            align-self: center;
            background-color: var(--color-page-background-white);
            gap: 8px;
            padding: 4px 8px;
            height: 32px;
            border-radius: 90px;
            flex: 1 0 0;
          }
        }
      }
    }
  }
}

.domain-table {
  border-radius: 4px;
  border: 1px solid #96C13D;
  width: 100%;
  border-collapse: collapse;
  color: #323338;

  tr,
  th,
  td {
    border: 1px solid #96C13D;
  }

  th {
    height: 40px;
  }
}

.is-error {
  .domain-table {
    border: 1px solid red;

    tr,
    th,
    td {
      border: 1px solid red;
    }
  }
}

.table-row {
  position: relative;
}


/deep/ .el-select {
  margin: 10px;
}

/deep/ .el-input__inner {
  border-style: dashed;
}

/deep/ .el-textarea__inner {
  border-color: transparent;
  resize: none;
  overflow: hidden;
}

/deep/ .el-textarea__inner:hover {
  border: 1px dashed #c0c4cc;
}

/deep/ .el-textarea__inner:focus {
  border: 1px dashed #10b3b7;
}

.textarea-hover-padding-skeleton {
  & > :first-child {
    padding: 10px 15px;

    [contenteditable="true"] {
      outline: none;
    }

    // iep support 使用 editor 组件，这里对其进行样式修改
    .teacherChildrenContent /deep/ .quill-editor {
      line-height: normal;
      height: 100%;

      h1, h2, h3, h4, h5, h6 {
        line-height: 1.42 !important;
      }

      // 隐藏功能栏
      .ql-toolbar {
        display: none !important;
      }

      .ql-container {
        font-size: 14px !important;
        border: 1px dashed transparent !important;
        resize: none !important;
        overflow: hidden !important;
        border-radius: 4px !important;
      }

      .ql-editor {
        border: 1px dashed transparent !important;
      }

      .ql-editor:hover {
        border: 1px dashed var(--color-inner-dashed-border) !important;
      }

      .ql-editor:focus {
        border: 1px dashed var(--color-primary) !important;
      }

      .ql-editor {
        overflow-y: auto;
        min-height: 33px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
        &.ql-blank:before {
          font-style: normal;
        }
      }

      /* 修改富文本编辑器的placeholder的默认颜色：字体 */

      .ql-editor[data-placeholder]:before {
        /* 此处设置 placeholder 样式 */
        font-style: normal;
        font-size: 14px;
      }

      .ql-editor ol, .ql-editor ul {
        padding-left: 0px !important;
      }

      .ql-editor ol li:not(.ql-direction-rtl), .ql-editor ul li:not(.ql-direction-rtl) {
        padding-left: 0px !important;
      }
    }
  }
}

.textarea-hover-padding {
  padding: 10px 15px;
  & {
    .differentiation-content {
      padding-left: 0px !important;
      height: fit-content;
      display: block;
      margin: 0 !important;
      line-height: 1.5 !important; /* 可根据需要调整行间距 */
    }

    .differentiation-content p, .differentiation-content li {
      padding: 0;
      margin: 0;
    }
  }
}

.textarea-hover-padding:hover, .textarea-hover-padding:focus-within {
  padding: 10px 15px;
}

.remove-remove-padding-t-0 {
  padding-top: 0px !important;
}

/deep/ .el-card__header {
  display: -ms-flexbox;
  display: flex;
  padding-top: 16px;
  padding-bottom: 32px;
  padding-left: 24px;
  padding-right: 24px;
  margin-bottom: 0px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  align-self: center;
  gap: 16px;
  background: #E8F9FA;
}

/deep/ .el-tree-node__children {
  display: flex;
  flex-wrap: nowrap; /* 防止换行 */
}
.remove-padding {
  padding: 0px !important;
}

/* 描述列表标签值 */
.lesson-field-value {
  font-size: 16px;
  line-height: 25px;
  color: #111c1c;
}

.content-difference {
  padding: 24px;
  border-radius: 20px;
  border: 1px solid var(--color-white) !important;
  background: #fff;
  margin-top: -14px;
}


// iep support 使用 editor 组件，这里对其进行样式修改
.teacherChildrenContent /deep/ .quill-editor {
  line-height: normal;
  height: 100%;

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.42 !important;
  }

  // 隐藏功能栏
  .ql-toolbar {
    display: none !important;
  }

  .ql-container {
    font-size: 14px !important;
    border: 1px dashed transparent !important;
    resize: none !important;
    overflow: hidden !important;
    border-radius: 4px !important;
  }

  .ql-editor {
    border: 1px dashed transparent !important;
  }

  .ql-editor:hover {
    border: 1px dashed var(--color-inner-dashed-border) !important;
  }

  .ql-editor:focus {
    border: 1px dashed var(--color-primary) !important;
  }

  .ql-editor {
    overflow-y: auto;
    min-height: 33px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
    &.ql-blank:before {
      font-style: normal;
    }
  }

  /* 修改富文本编辑器的placeholder的默认颜色：字体 */

  .ql-editor[data-placeholder]:before {
    /* 此处设置 placeholder 样式 */
    font-style: normal;
    font-size: 14px;
  }

  .ql-editor ol, .ql-editor ul {
    padding-left: 0px !important;
  }

  .ql-editor ol li:not(.ql-direction-rtl), .ql-editor ul li:not(.ql-direction-rtl) {
    padding-left: 0px !important;
  }
}
</style>
