<template>
  <div class="lesson-recycle">
    <!-- 回收站按钮 -->
    <div style="cursor: pointer;"  @click="recycleButtonClickHandler">
      <i class="lg-icon lg-icon-delete"></i>
      <span> {{ $t('loc.lessons2RecycleDialogTitle') }} </span>
    </div>
    <!-- 回收站列表页面 -->
    <el-dialog :title="$t('loc.lessons2RecycleDialogTitle')" :visible.sync="showList" v-if="showList"
               width="650px" class="recycle_list" append-to-body>
      <div class="scrollbar" v-infinite-scroll="load" style="max-height:404px;overflow-y: auto;"
           v-if="loading === 0 || loading===1 || loading===2 && items && items.length > 0" :key="listKey">
        <div v-for="item in items" style="border-bottom: 1px solid #f3f1f1;">
          <el-row class="recycle_list_item">
            <el-col :span="4" style="cursor: pointer" @click.native="intoDetailPage(item)">
              <lesson-media-viewer :url="getLast(item.coverMediaUrls)"/>
            </el-col>
            <el-col :span="16" class="el-col-16">
              <div class="lessonName" :title="item.name">
                <span @click="intoDetailPage(item)" style="cursor: pointer;">{{ item.name }}</span>
              </div>
              <div class="authorMessage">
                <el-avatar :src="get(item.authorAvatar)" size="small"/>
                <span>{{ item.authorName }}</span>
              </div>
            </el-col>
            <el-col :span="4">
              <el-tooltip class="item" effect="dark" :content="$t('loc.lessons2PermanentlyDeleteButtonTips')"
                          placement="top">
                <el-button type="text" icon="el-icon-delete"
                           @click.stop="removeById(item.id)" class="remove_lesson"/>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :content="$t('loc.lessons2RestoreTips')" placement="top">
                <el-button type="text" @click.stop="restoreById(item.id)" class="restore_lesson">
                  <img src="../../../../../assets/img/lesson2/Restore.png" style="width: 16px;height: 14px" alt=""/>
                </el-button>
              </el-tooltip>
            </el-col>
          </el-row>
        </div>
        <div v-if="loading===1" style="max-height:404px;overflow-y: auto;">
          <RecycleLessonSkeleton></RecycleLessonSkeleton>
        </div>
      </div>
      <lesson-empty_-recycle v-if="this.loading===2 && (!items || items.length === 0)" :tip="$t('loc.lessons2NoResult')" style="height: 400px;"/>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showList = false">Close</el-button>
      </div>
    </el-dialog>
    <!--课程详情弹窗-->
    <lesson-detail-dialog :show.sync="showDetail" :lesson-id="detailLessonId">
      <lesson-detail :lessonId="detailLessonId" :disabled="true" :isFromLibrary="true" :isDialog="true" />
    </lesson-detail-dialog>

  </div>
</template>
<script>
import { isTeacher } from "@/utils/common"
import LessonDetailDialog from '@/views/modules/lesson2/lessonLibrary/components/LessonDetailDialog'
import Api from '../../../../../api/lessons2'
import constants from '../../../../../utils/constants'
import Loading from '../../component/loading'
import LessonDetail from '../components/LessonDetail'
import LessonEmpty_Recycle from '../components/LessonEmpty_Recycle'
import LessonMediaViewer from '../components/LessonMediaViewer'
import RecycleLessonSkeleton from '../components/Skeleton/RecycleLessonSkeleton'
export default {
  name: 'RecycleLesson',
  components: {
    LessonMediaViewer,
    Loading,
    LessonEmpty_Recycle,
    LessonDetail,
    LessonDetailDialog,
    RecycleLessonSkeleton
  },
  data () {
    return {
      showList: false, // 课程列表--是否展示列表弹框
      showDetail: false, // 详情--是否展示弹框
      detailLessonId: false, // 详情--课程 ID
      restored: 0, // 通过恢复操作恢复的课程数
      pageSize: 5, // 每次加载的页大小
      pageNum: 0, // 已加载页
      items: [],// 已加载的数据
      total: 0, // 数据总条数
      loading: 0,// 0 未加载，1 加载中，2 已加载
      ended: false, // 数据是否已全部加载
      listKey: 0
    }
  },
  watch: {
    showList () {
      if (!this.showList) {
        this.$emit('afterClosed', this.restored)
      }
    }
  },
  methods: {
    recycleButtonClickHandler () {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_lesson_library_my_less_click_deleted')
      } else {
        this.$analytics.sendEvent('web_lesson_library_agency_click_deleted')
      }
      this.restored = 0
      this.pageNum = 0
      this.items = []
      this.total = 0
      this.loading = 0
      this.ended = false
      this.showList = true
      this.listKey++
      window.lessonVideo && window.lessonVideo.pause()
    },
    load () {
      // 已加载完或正在加载，则不再加载
      if (this.ended || this.loading === 1) {
        return
      }
      // 加载下一分页
      this.loading = 1
      this.pageNum++
      Api.getRecoverableLesson(this.pageSize, this.pageNum).then(
        page => {
          this.items = this.items.concat(...page.items)
          this.total = page.total
          this.ended = this.total <= this.items.length
        })
        .catch(error => {
          this.ended = true
          let message = error && error.response && error.response.data && error.response.data.error_message
          message && this.$message.error(message)
        })
        .finally(() => {
          this.loading = 2
        })
    },
    // 默认头像
    get (str) {
      if (str === null) {
        return constants.userAvatarURL
      }
      return str
    },
    getLast (arr) {
      if (arr.length === 0) {
        return constants.lessonDefaultCoverURL
      }
      // return arr && arr[arr.length - 1]
       return arr && arr[0]
    },
    getType (url) {
      if (!url) {
        return 'unknown'
      }
      url = url.toLowerCase()
      if (url.match('.+\\.(png|jpg|jpeg)')) {
        return 'image'
      }
      if (url.match('.+\\.mp4')) {
        return 'video'
      }
      return 'image'
    },
    intoDetailPage (lesson) {
      this.detailLessonId = lesson.id
      this.showDetail = true
      window.lessonVideo && window.lessonVideo.pause()
    },

    removeById (lessonId) {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_lesson_library_my_less_perm_deleted')
      } else {
        this.$analytics.sendEvent('web_lesson_library_agency_perm_deleted')
      }
      const h = this.$createElement
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.lessons2PermanentlyDeleteTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        cancelButtonClass: 'is-plain',
        confirmButtonClass: 'el-button--danger',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            if (instance.confirmButtonLoading) {
              return
            }
            instance.confirmButtonLoading = true
            Api.deleteLessonPermanently(lessonId)
              .then(() => {
                  done()
                  this.$message.success(this.$t('loc.lessons2PermanentlyDeleteSuccessTips'))
                  this.recycleButtonClickHandler()
                }
              )
              .finally(() => {
                setTimeout(() => {
                  instance.confirmButtonLoading = false
                }, 500)
              })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },

    restoreById (lessonId) {
      if (isTeacher()) {
        this.$analytics.sendEvent('web_lesson_library_my_lesson_click_restore')
      } else {
        this.$analytics.sendEvent('web_lesson_library_agency_click_restore')
      }
      const h = this.$createElement
      this.$msgbox({
        title: 'Confirmation',
        message: h('p', null, [
          h('span', null, this.$t('loc.lessons2RestoreLessonTips'))
        ]),
        showCancelButton: true,
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        confirmButtonClass: 'el-button--primary',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            if (instance.confirmButtonLoading) {
              return
            }
            instance.confirmButtonLoading = true
            Api.recoverLesson(lessonId).then(
              () => {
                this.$message.success(this.$t('loc.lessons2RestoreSuccessTips'))
                this.recycleButtonClickHandler()
                this.restored++
              }
            ).finally(() => {
              done()
              setTimeout(() => {
                instance.confirmButtonLoading = false
              }, 500)
            })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    }
  }
}
</script>
<style scoped lang="less">
.lesson-recycle /deep/ & {
  display: inline-block;

  & > :first-child {
    border: none;
  }
}

.lesson_media {
  width: 95.75px;
  height: 50px;
}

//弹框头部
.recycle_list /deep/ .el-dialog__header {
  padding: 15px 14px 13px;
  border-bottom: 2px solid #eeeeee;

  .el-dialog__title {
    font-size: 18px;
    color: #4b4b4b;
  }
}

.recycle_list /deep/ .el-dialog {
  position: relative;
  margin: 0 auto 0;
  background: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  padding: 2px 2px 2px 2px;
}

//弹框体
.recycle_list /deep/ .el-dialog__body {
  padding: 0;
  border-bottom: 1px solid #eeeeee;

  .el-icon-delete:before {
    color: red;
  }

  .remove_lesson {
    margin: 0 10px;
    line-height: 33px;
  }

  .restore_lesson {
    margin: 0 20px;
  }
}

//弹框底部
.recycle_list /deep/ .el-dialog__footer {
  padding: 13px 12px 19px 0;
}

//作者头像和名
.authorMessage {
  display: flex;
  align-items: center;

  span {
    margin-left: 8px;
    font-size: 14px;
    color: #909399;
  }
}

.lessonName {
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
  padding-left: 6px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.recycle_list /deep/ .el-col {
  height: 50px;
}

//列表间距
.recycle_list_item {
  margin: 10px 0 20px 14px;
}

.lesson-detail-dialog /deep/ & {
  .el-dialog__headerbtn > .el-dialog__close {
    position: relative;
    top: -9px;
    right: -5px;
    font-size: 33px;
  }

  .lesson-field-time {

    & > :nth-child(3) {
      margin-left: 30px;
    }
  }
}
</style>
