<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    @click.stop
    :before-close="handleClose">
    <div class="feature-reminder" role="dialog" aria-labelledby="tour-title">
      <img src="~@/assets/img/lesson2/lessonPlan/create_lesson_plan_dialog.png" class="feature-image"/>
      <div class="content-wrapper">
        <div class="lg-margin-top-24 lg-margin-l-r-24 content1">
          ✨ Abracadabra! Lesson plans in a flash! ✨
        </div>
        <div class="lg-margin-top-24 content2">
          Just toss in a simple thought, our magic Genie will whip up a fantastic,
          detailed lesson plan in just 2 mins! Perfectly tailored for your amazing students.
        </div>

        <div class="button-container">
          <div class="button-wrapper">
            <el-button @click="startExploring" class="tour-button">
              <span class="button-text">⚡️ Create Magic Now</span>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import pluginGuide from '@/assets/img/curriculumPlugin/plugin-guide.png'

export default {
  name: 'CreateLessonPlanDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      guideFeatures: state => state.common.guideFeatures,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin // 是否是 Curriculum Plugin 平台
    }),
    /**
     * 获取用户 id
     */
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    }
  },
  data () {
    return {
    }
  },
  methods: {
    handleClose () {
      // 恢复默认值
      this.$emit('update:dialogVisible', false)
    },
    startExploring () {
      // 发送开始创建 unit 埋点事件
      this.$analytics.sendEvent('cg_lesson_welcome_begin')
      this.$emit('update:dialogVisible', false)
      // 设置缓存
      localStorage.setItem('CREATE_LESSON_PLAN_GUIDE_FIRST_VISIT' + this.currentUserId, 'true')
      // 隐藏引导过程
      let result = { 'features': ['CREATE_LESSON_PLAN_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then(() => {
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
              ...this.guideFeatures,
              showCurriculumCreateLessonPlanGuide: false
            })
      })
      // 跳转到创建页面
      this.$router.push({
        name: 'AddLesson',
        query: { showExemplarsTip: 'true' }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.feature-reminder {
  border-radius: 12px;
  display: flex;
  max-width: 650px;
  flex-direction: column;
  overflow: hidden;
  color: var(--ffffff, #fff);
  text-align: center;
  font: 600 16px Inter, sans-serif;
}

.hero-image {
  object-fit: contain;
  object-position: center;
  width: 100%;
  z-index: 10;
}

.content1 {
  color: var(--111-c-1-c, #111C1C);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 28px; /* 116.667% */
}

.content2 {
  color: var(--111-c-1-c, #111C1C);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  margin-left: 40px;
  margin-right: 40px;
}

.content-wrapper {
  background-color: rgba(255, 255, 255, 1);
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: start;
}

.feature-image {
  object-fit: contain;
  object-position: center;
  width: 100%;
}

.button-container {
  background-color: rgba(255, 255, 255, 1);
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0 30px 0;
}

.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tour-button {
  background: var(--logo, linear-gradient(271deg,#2d9cdb .32%,#878bf9 67.59%,#bb6bd9 142.72%)) !important;
  border-color: transparent !important;
  border: 0 !important;
  min-height: 50px;
  width: 230px !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tour-button:hover {
  box-shadow: 0 0 0 3px rgba(45, 156, 219, 0.3) !important;
}

.button-text {
  font-size: 16px;
  font-feature-settings: "liga" off, "clig" off;
  padding: 0 4px;
  color: var(--ffffff, #fff);
}

/deep/ .el-dialog {
  border-radius: 16px;
  overflow: hidden;
}

/deep/ .el-dialog__header {
  display: none;
}

/deep/ .el-dialog__body {
  padding: 0px !important;
}

/deep/ .el-button {
  padding: 0;
  width: 100%;
  height: 100%;
}

@media (max-width: 991px) {
  .hero-image {
    max-width: 100%;
  }

  .content-wrapper {
    max-width: 100%;
    z-index: 9999;
  }

  .feature-image {
    max-width: 100%;
  }

  .button-container {
    max-width: 100%;
  }
}
</style>