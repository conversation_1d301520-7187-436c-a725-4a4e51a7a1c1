<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    custom-class="lesson-plan-to-unit-dialog"
    @click.stop
    :before-close="handleClose">
    <div class="feature-reminder" role="dialog" aria-labelledby="tour-title">
      <img src="~@/assets/img/lesson2/lessonPlan/lessonPlanToUnitDialogImgUrl.png" class="feature-image"/>
      <div class="content-wrapper">
        <div class="lg-margin-top-24 lg-margin-l-r-24 content-title">
          🎉 Brilliant Lesson! You've got the magic touch!
        </div>
        <div class="lg-margin-top-24 content-font">
          Now for something even more exciting: building your very own <strong>Unit Plan!</strong>
          And guess what? It's a snap to create. Get ready for more WOWs!
        </div>

        <div class="button-container">
          <div class="button-wrapper">
            <el-button @click="startExploring" class="tour-button-create">
              <span class="button-text-create">Start My Exciting Unit</span>
            </el-button>
            <el-button @click="laterExploring" class="tour-button-later">
              <span class="button-text-later">Later</span>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'LessonPlanToUnitDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      guideFeatures: state => state.common.guideFeatures // 功能引导
    }),
    /**
     * 获取用户 id
     */
    currentUserId () {
      if (!this.currentUser) {
        return ''
      }
      return this.currentUser.user_id
    }
  },
  data () {
    return {
    }
  },
  methods: {
    handleClose () {
      // 恢复默认值
      this.$emit('update:dialogVisible', false)
    },

    /**
     * 跳转 Unit
     */
    startExploring () {
      // 发送开始创建 unit 埋点事件
      this.$analytics.sendEvent('cg_lesson_unit_pop_start_click')
      this.$emit('update:dialogVisible', false)
      // 设置缓存
      localStorage.setItem('LESSON_PLAN_TO_UNIT_GUIDE_FIRST_VISIT' + this.currentUserId, 'true')
      localStorage.setItem('UNIT_PLANNER_GUIDE_FIRST_VISIT' + this.currentUserId, 'true')
      // 隐藏引导过程
      let result = { 'features': ['LESSON_PLAN_TO_UNIT_GUIDE','PLUGIN_UNIT_PLANNER_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then(() => {
        // 更新 guideFeatures 中 store 中的值
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
              ...this.guideFeatures,
              showCurriculumLessonPlanToUnitGuide: false,
              showCurriculumUnitPlannerGuide: false
            })
      })

      // 清空已有单元信息
      this.$store.commit('curriculum/RESET_UNIT')
      // 跳转到创建页面
      this.$router.push({
        name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-creator-cg' : 'unitCreator'
      })
    },

    /**
     * later 处理
     */
    laterExploring () {
      this.$analytics.sendEvent('cg_lesson_unit_pop_later_click')
      this.$emit('update:dialogVisible', false)
      // 设置缓存
      localStorage.setItem('LESSON_PLAN_TO_UNIT_GUIDE_FIRST_VISIT' + this.currentUserId, 'true')
      // 隐藏引导过程
      let result = { 'features': ['LESSON_PLAN_TO_UNIT_GUIDE'] }
      this.$axios.post($api.urls().hideGuide, result).then(() => {
        // 更新 guideFeatures 中 store 中的值
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
              ...this.guideFeatures,
              showCurriculumLessonPlanToUnitGuide: false
            })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.feature-reminder {
  border-radius: 12px;
  border: 3px solid #AA89F2;
  background: radial-gradient(43.51% 51.51% at 51.72% 16.3%, rgba(255, 215, 78, 0.10) 0%, rgba(255, 255, 255, 0.00) 100%), linear-gradient(176deg, rgba(170, 137, 242, 0.30) 0.15%, rgba(16, 179, 183, 0.03) 31.5%), #FFF;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.10);
  display: flex;
  max-width: 650px;
  flex-direction: column;
  overflow: visible;
  color: var(--ffffff, #fff);
  text-align: center;
  font: 600 16px Inter, sans-serif;
  // 添加相对定位作为定位参考
  position: relative;
}

.hero-image {
  object-fit: contain;
  object-position: center;
  width: 100%;
  z-index: 10;
}

.content-title {
  color: var(--111-c-1-c, #111C1C);
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px; /* 116.667% */
}

.content-font {
  color: var(--111-c-1-c, #111C1C);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  margin-left: 40px;
  margin-right: 40px;
}

.content-wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: start;
  // 添加负上边距来消除图片移动造成的空白
  margin-top: -130px;
  // 关键修改：添加圆角匹配父容器
  border-radius: 12px;
}

.feature-image {
  width: 195px;
  height: 216px;
  // 使用 transform 移动图片但不影响文档流
  transform: translateY(-100px);
  position: relative;
  z-index: 10;
  // 添加水平居中
  margin-left: auto;
  margin-right: auto;
}

.button-container {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0 30px 0;
  border-radius: 12px;
}

.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  // 添加垂直排列
  flex-direction: column;
  // 添加按钮之间的间距
  gap: 8px;
}


.tour-button-later {
  border-color: transparent !important;
  border: 0 !important;
  min-height: 20px;
  width: 195px !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

// 添加悬停状态样式
.tour-button-later:hover {
  background: transparent !important; // 保持透明背景
}

.tour-button-create {
  border-radius: 4px;
  background: var(--10-b-3-b-7, #10B3B7);
  border: 0 !important;
  min-height: 50px;
  width: 225px !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.button-text-create {
  color: var(--ffffff, #FFF);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
}

.button-text-later {
  color: var(--676879, #676879);
  text-align: center;
  font-feature-settings: 'liga' off, 'clig' off;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

/deep/ .el-dialog {
  border-radius: 16px;
  overflow: visible;
}

/deep/ .el-dialog__header {
  display: none;
}

/deep/ .el-dialog__body {
  padding: 0px !important;
  overflow-y: visible !important;
}

/deep/ .el-button {
  padding: 0;
  width: 100%;
  height: 100%;
}

@media (max-width: 991px) {
  .hero-image {
    max-width: 100%;
  }

  .content-wrapper {
    max-width: 100%;
    z-index: 9999;
  }

  .feature-image {
    max-width: 100%;
  }

  .button-container {
    max-width: 100%;
  }
}

/deep/ .lesson-plan-to-unit-dialog {
  margin-top: 30vh!important;
}
</style>