<template>
  <div class="share-card">
    <div @click="shareLink('Facebook', unit)">
      <img src="@/assets/img/magicCurriculum/card-facebook.png" alt="" />
      <p>Facebook</p>
    </div>
    <div @click="shareLink('Twitter', unit)">
      <img src="@/assets/img/magicCurriculum/card-X.png" alt="" />
      <p>Twitter</p>
    </div>
    <div @click="shareLink('CopyLink', unit)">
      <img src="@/assets/img/magicCurriculum/card-link.png" alt="" />
      <p>Copy link</p>
    </div>
  </div>
</template>

<script>
import { shareLink } from '@/utils/common'

export default {
  props: {
    unit: {
      type: Object,
      required: true
    }
  },
  methods: {
    shareLink
  }
}
</script>

<style lang="scss" scoped>
.share-card {
  display: flex;
  gap: 20px;
  text-align: center;
  justify-content: center;
  & > div {
    flex: 1;
  }
  p {
    color: #111c1c;
    font-size: 12px;
    display: block;
    margin-top: 10px;
  }
  & > div {
    cursor: pointer;
  }
  img {
    width: 48px;
    height: 48px;
  }
}
</style>
