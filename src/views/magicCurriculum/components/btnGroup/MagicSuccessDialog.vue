<template>
  <!-- 成功提示弹窗 -->
  <el-dialog
    title=""
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="visible"
    :width="'528px'"
    custom-class="lg-success-dialog"
  >
    <div class="success-title">Submission successful!</div>
    <div class="success-info">
      Your entry has been published. Start gathering votes and rise to the top of the leaderboard.
      Share your Unit Planner with others on:
    </div>
    <SharePopover :unit="unit"></SharePopover>
    <span class="lg-icon lg-icon-error success-close" @click="close"></span>
  </el-dialog>
</template>

<script>
import { shareLink } from '@/utils/common'
import SharePopover from './SharePopover'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    unit: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    SharePopover
  },
  data() {
    return {
      internalVisible: this.visible // 使用一个内部数据来控制弹窗显示
    }
  },
  watch: {
    visible(newVal) {
      this.internalVisible = newVal // 监听 prop 的变化，更新内部数据
    },
    internalVisible(newVal) {
      this.$emit('update:visible', newVal) // 当内部数据改变时，通知父组件
    }
  },
  methods: {
    shareLink,
    close() {
      this.visible = false
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep{ 
  .lg-success-dialog{
    // background: url(~@/assets/img/magicCurriculum/dialog-ad.png) no-repeat center top;
    background-color: transparent;
    // background-size: 261px 116px;
    box-shadow: none;
    // height: 300px;
  }
  .success-title{
    color: #111C1C;
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 130% */
  }
  .success-info{
    color: #111C1C;
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .el-dialog__body{
    position: relative;
    border-radius: 12px;
    &::before{
      content: "";
      height: 116px;
      width: 261px;
      display: block;
      position: absolute;
      top: -80px;
      left: calc(50% - 130px);
      background: url(~@/assets/img/magicCurriculum/dialog-ad.png) no-repeat center top;
      background-color: transparent;
      background-size: 261px 116px;
      box-shadow: none;
    }
    padding-top: 50px !important;
    padding-bottom: 20px !important;
    background: #fff;
    .share-card {
      gap: 12px;
      margin-top: 16px;
      & > div{
        flex: none;
      }
      p{
        display: none;
      }
    }
    .success-close{
      cursor: pointer;
      position: absolute;
      left: 248px;
      bottom: -48px;
      font-size: 32px;
      color: rgba(245, 246, 248, 1);
    }
  }
}
</style>
