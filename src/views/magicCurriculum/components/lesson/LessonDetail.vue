<template>
  <div ref="mainContainer" style="height: calc(100% - 64px)" class="add-padding-t-4 main-container" >
    <div style="padding: 0 30px;padding-top: 10px;background-color: #fff" class="lg-border-radius-8" v-if="!lesson">
    <lesson-detail-skeleton/>
    </div>
    <div v-else class="unitDetailContent   lg-border-radius-8 lg-box-shadow lg-scrollbar-show h-full" >
      <div class="lessonHeader">
        <div>
          <!-- <lesson-love v-if="lesson" :count="lesson.likeCount" :lesson-id="lesson.id" :liked="lesson.liked"
            @update:count="value => handleUpdate(lesson, 'likeCount', value)" />
          <lesson-read-count style="margin-left: 12px;" v-if="lesson" :count="lesson.readCount" /> -->
        </div>
        <!-- 下载按钮 -->
        <div>
          <MagicLessonDownLoad v-if="lesson && isUnitIdInUnitIds() " :lesson-id="lesson.id" :lesson-name="lesson.name"
            :mapped-framework-id="mappedFrameworkId" :lesson-framework-id="lesson.framework.id" :viewLesson="true">
          </MagicLessonDownLoad>
          <LessonDownload v-else :lesson-id="lesson.id" :lesson-name="lesson.name" :mapped-framework-id="mappedFrameworkId"
            :lesson-framework-id="lesson.framework.id" :viewLesson="true" :lesson="lesson">
          </LessonDownload>
        </div>
      </div>
      <div class="lesson-detail__content">
        <slot :lesson="lesson">
          <!--详情页的内容主体-->
          <lesson-info-new v-if="isFromLibrary && lesson" :lesson="lesson" :planPreview="planPreview"
            @getMappedFrameworkId="getMappedFrameworkId" :isMagicLesson=true>
            <slot name="lesson-operation" :lesson="lesson" slot="lesson-operation" />
          </lesson-info-new>
          <lesson-info v-if="!isFromLibrary" :lesson="lesson" :planPreview="planPreview"
            @getMappedFrameworkId="getMappedFrameworkId">
            <slot name="lesson-operation" :lesson="lesson" slot="lesson-operation" />
          </lesson-info>
          <!-- 课程反思 -->
          <reflection-detail-list :excludePlanId="planId" :lessonId="lesson.id" :subList=true :showHeader=true
            v-if="!hideReflection && lesson"></reflection-detail-list>
          <!--评论区-->
          <!-- <lesson-comment :lesson-id="lesson.id" :lesson-create-user-id="lesson.createUserId" :disabled="disabled" /> -->
        </slot>
      </div>
      <!--课程详情弹窗提示-->
      <div>
        <slot name="detail-tips" :lesson="lesson"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import Api from '@/api/lessons2/index'
import LessonInfo from '@/views/modules/lesson2/lessonLibrary/components/LessonInfo'
import LessonComment from '@/views/modules/lesson2/lessonLibrary/components/LessonComment'
import ReflectionDetailList from '@/views/modules/lesson2/lessonPlan/components/ReflectionDetailList'
import LessonDetailSkeleton from '@/views/modules/lesson2/lessonLibrary/components/Skeleton/LessonDetailSkeleton'
import CurriculumMediaViewer from '@/views/modules/lesson2/lessonCurriculum/components/CurriculumMediaViewer.vue'
import tools from '@/utils/tools'
import LessonInfoNew from '@/views/modules/lesson2/lessonLibrary/components/LessonInfoNew'
import Editor from '@/views/modules/lesson2/component/editor/index.vue'
import MediasUploader from '@/views/curriculum/components/meida/MediasUploader.vue'
import ChildList from '@/views/curriculum/roster/components/ChildList.vue'
import ClrEditComponent from '@/views/modules/lesson2/unitPlanner/components/editor/ClrEditComponent.vue'
import LessonQuizWindow from '@/views/modules/lesson2/lessonLibrary/editor/LessonQuizWindow.vue'
import MagicLessonDownLoad from '@/views/modules/lesson2/lessonLibrary/components/MagicLessonDownLoad.vue'
import LessonReadCount from '@/views/modules/lesson2/lessonLibrary/components/LessonReadCount.vue';
import LessonLike from '@/views/modules/lesson2/lessonLibrary/components/LessonLike'
import LessonLove from '@/views/modules/lesson2/lessonLibrary/components/LessonLove'
import LessonDownload from '@/views/modules/lesson2/lessonLibrary/components/LessonDownload.vue'
import {mapState} from "vuex";
import { POPULAR_UNIT_LIST } from '@/store/modules/const'

export default {
  name: 'LessonDetail',
  components: {
    LessonInfoNew,
    LessonComment,
    LessonInfo,
    ReflectionDetailList,
    LessonDetailSkeleton,
    CurriculumMediaViewer,
    Editor,
    MediasUploader,
    ChildList,
    ClrEditComponent,
    LessonQuizWindow,
    MagicLessonDownLoad,
    LessonReadCount,
    LessonLike,
    LessonLove,
    LessonDownload
  },
  props: {
    // lessonId: {
    //   type: String,
    //   default: ''
    // },
    // 是否可以编辑
    disabled: {
      type: Boolean,
      default: false
    },
    // 典型行为加载状态
    planId: {
      type: String,
      default: ''
    },
    planPreview: {
      type: Boolean,
      default: false
    },
    hideReflection: {
      type: Boolean,
      default: false
    },
    isFromLibrary: {
      type: Boolean,
      default: true
    },
    // 生成文化差异加载状态
    submodule: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      lesson: null,
      mappedFrameworkId: true,
      mountedAfterFlag: false,
      previousPath: '',
      loading: false,
      unitIds: POPULAR_UNIT_LIST.items.map(v => v.id)
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.fetchData(to.params.lessonId)
    next()
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.previousPath = from.fullPath; // 获取上一步的路由路径
    });
  },
  created() {
  // 初始化为 true
  this.loading = true
  this.mountedAfterFlag = false
  let _this = this
  const params = _this.$route.params
  // 如果 params 不为空，并且 itemId 和 planId 都存在时，执行如下逻辑
  if (params && params.lessonId) {
    this.lessonId = params.lessonId
    Api.increaseLessonViewCount(this.lessonId).then(() => {
      // 获取课程详情
      Api.getLessonDetail({lessonId: this.lessonId}).then((res) => {

        if (this.isMC && (res.steps || []).length) {
          res.steps.forEach(v => {
            v.showImpStepSource = true
          })
        }
        _this.lesson = res
        _this.loading = false // 在这里将 loading 设置为 false
        _this.$nextTick(() => {
          _this.mountedAfter()
        })
      }).catch(() => {
        this.loading = false // 处理错误时也需要将 loading 设置为 false
      })
    }).catch(() => {
      this.loading = false
    })
  } else {
    this.loading = false
  }
},
  watch: {
    // lessonId: {
    //   immediate: true,
    //     handler() {
    //         // 添加浏览量
    //         Api.increaseLessonViewCount(this.lessonId).then(() => {
    //             let _this = this
    //             // 获取课程详情
    //             Api.getLessonDetail(this.lessonId).then((res) => {
    //                 _this.lesson = res
    //                 _this.$nextTick(() => {
    //                     _this.mountedAfter()
    //                 })
    //             })
    //         })
    //     }
    // }
  },
  computed: {
    ...mapState({
      unitInfo: state => state.magicCurriculum.unitInfo,
      isMC: state => state.curriculum.isMC, // 是否是 MC
     }),

    isUseAdaptedUDLAndCLR() {
      // 在mountedAfter之前由于没数据，默认有 UDL 和 CLR 以免用户误操作
      if (!this.mountedAfterFlag) {
        return true;
      }
      if (this.lesson) {
        if (this.lesson.steps) {
          return this.lesson.steps.some(item => {
            return !!item.culturallyResponsiveInstructionGroup || !!item.universalDesignForLearningGroup
          })
        }
      }
      return false
    },
  },
  methods: {
    // 判断 unitId 是否在 unitIds 中
    isUnitIdInUnitIds () {
      return this.unitIds.includes(this.unitInfo.id)
    },
    getMappedFrameworkId(flag) {
      this.mappedFrameworkId = flag
    },
    isComeFromIpad() {
      return tools.isComeFromIPad()
    },
    goBack() {
      this.$router.go(-1)
    },
    /**
     * mounted 之后的钩子，在 课程加载完之后调用
     */
    mountedAfter() {
      // 将标志设置为 true
      this.mountedAfterFlag = true
      // 向外发送事件 mountedAfter
      // this.$emit('mountedAfter')
    }
  },
  mounted() {
    if (this.lesson) {
      this.$nextTick(() => {
        this.mountedAfter()
      })
    }
    document.getElementsByClassName('main-menu')[0].scrollIntoView()
  },
}
</script>
<style scoped lang="less">
@media only screen and (max-width:1199px) {
  .lesson-detail-iPad {
    padding: 0 25px;
  }
}

@media only screen and (min-width:1200px) {
  .lesson-detail-web {
    padding: 0 60px;
  }
}

.v-modal /deep/ & {
  /* position: fixed; */
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000;
}

.magicLessonDown {
  width: 210px;
  height: 40px;
  margin-right: auto;
  /* 将元素推到左侧 */
  padding-left: 16px;
  /* 与右边的距离 */
}

.main-container {
  padding: 0 calc((100vw - 1150px) / 2);
  height: 100%;
  overflow-y: auto;
  background: #F0F2F5 /* 从 #E1F8FF 渐变到 #B0E0F8 */
}
.unitDetailContent {
  background: #fff;
  width: 100%;
  padding: 0 20px 20px 20px;
  margin-bottom: 24px;
}

.lessonHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 16px;
}
.lesson-detail__content {
  margin-top: 16px;
}


.lesson-detail /deep/ & {
  /* 头部按钮区 */

  &> :first-child {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &>div {
      display: flex;
      align-items: center;

      &:first-child {
        &>* {
          margin-right: 20px;
          margin-left: 0;
        }
      }

      &:last-child {
        &>* {
          margin-right: 0;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
