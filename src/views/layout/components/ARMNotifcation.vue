<template>
  <div style="text-align: center ;    word-break: break-word !important ;">
    <div><img src="@/assets/img/us/learning.png" /></div>
    <div style="text-align: center; padding: 20px; font-size: 1.5rem">
      The Analytical
      Reporting Module in Learning Genie has been officially retired (phased
      out) since <span class="font-bold">06/26/2021</span>, please note that all
      your data are <span style="font-weight: bold">safely saved</span>. If you
      have any question or needs, please don't hesitate to contact us at any
      time for support. The new DRDP data module aligned with CDE vendor
      Licensing agreement will be online in the month of
      <span style="font-weight: bold">Aug</span>. Thank you for your continuous
      support and understanding!
    </div>
    <div>
      <el-button  @click="close()">
        I got it.
      </el-button>
      <el-button type="primary" @click="goArchive()" :loading="goArchiveLoading">
        Download Child Progress Report
      </el-button>
      <el-button type="primary" @click="toAnalyticalReport()">
        Download other DRDP reports, I need support.
      </el-button
      >
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCurrentUser } from '@/utils/common'

export default {
  props: {
    close: {
      type: Function,
    },
  },
  data () {
    return {
      goArchiveLoading: false
    }
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser()
    })
  },
  methods: {
    toAnalyticalReport() {
      this.$axios.get($api.urls().sendHelpEmail).then((data) => {
        this.close();
        this.$alert(
          "Your request on the DRDP Data has been sent to Learning Genie team.  A team member will contact you on specific needs to serve you.",
          "Tips",
          {
            confirmButtonText: this.$t("loc.ok"),
            callback: (action) => {},
          }
        );
      });
    },
    goArchive () {
      let tipContent = 'Hover over a child on "Admin Settings" > "Manage Children" page and click the "Archives" folder, you can download the child\'s Domain Score Report, which is the same with Child Progress Report. Are you sure you want to go to the "Manage Children" page now?'
      let goText = 'Yes, go to the "Manage Children" page.'
      if (this.currentUser.role == 'COLLABORATOR') {
        tipContent = 'Click the "Download Archieves" button on child\'s portfolio page, you can download child\'s Domain Score Report, which is the same with Child Progress Report. Are you sure you want to go to child\'s portfolio page now?'
        goText = 'Yes, go to child\'s portfolio page.'
      }
      this.$alert(
        tipContent,
        "Tips",
        {
          confirmButtonText: goText,
          customClass: 'arm-to-archive-modal',
          callback: (action) => {
            if (action !== 'cancel') {
              this.goArchiveLoading = true
              if (this.currentUser.role === 'COLLABORATOR') {
                this.$axios.get($api.urls(this.currentUser.user_id).getCenterIdNameByUserId).then((data) => {
                  if (data && data.length > 0) {
                    const centerId = data[0].id
                    this.$axios.get($api.urls(undefined, centerId).getCenterById).then((data) => {
                      if (data && data.groups && data.groups.length > 0) {
                        const firstGroup = data.groups[0]
                        const groupId = firstGroup.id
                        if (firstGroup.enrollments && firstGroup.enrollments.length > 0) {
                          const childId = firstGroup.enrollments[0].id
                          window.location.href = '/#/centers/' + centerId + '/groups/' + groupId + '/students/' + childId + '/portfolios'
                        }
                      }
                    });
                  }
                });
              } else {
                window.location.href = '/#/manage_children'
              }
            }
          },
          showCancelButton: true
        }
      );
    }
  },
};
</script>

<style>
</style>