<template>
  <div>
    <el-dialog
      :visible.sync="isShowAdavter"
      width="59.5%"
      :custom-class="!isShowIconAdavter?'showAdvter' : 'showAdvter img-rotate-animation'"
      :show-close="false"
      :before-close="beforeClose()"
      >
      <div v-show="autoOpenStatus && isPlayying" class="noMoreTips"  @mouseenter="isShowBtn()">
        <!--<span @click="isChecked()"><em v-if="isCheck" class="el-icon-check"></em></span>-->
        <span class="showAgain" @click="isChecked()">
          <img v-if="isCheck" src="@/assets/img/advertise/check.png" alt="">
          <img v-if="!isCheck" src="@/assets/img/advertise/no_check.png" alt="">
        </span>
        <!-- <i class="font-size-16">{{$t('loc.noShowAgn')}}</i> -->
        <i class="font-size-16">Don’t show again</i>
      </div>
      <button v-show="isPlayying" type="button" @click="closeAdvter()" @mouseenter="isShowBtn()"  @mouseleave="isHidenBtn()" class="el-dialog__headerbtn"><img src="@/assets/img/advertise/close.png" alt=""></button>
      <div class="videoBox" @mouseenter="isShowBtn()" @mouseleave="isHidenBtn()">
        <!--<div class="videoPlay"  @click="playVideo()">-->
          <img @click="playVideo()" class="advertise-video" src="@/assets/img/advertise/video.png" alt="">
        <!--</div>-->
        <!--<video id="advterVideo" width="100%" controls="true" controlslist="nodownload">-->
            <!--<source :src="videoUrl" type="video/mp4">-->
        <!--</video>-->
        <video id="my-video" class="video-js videoHeight" controls preload="auto" width="100%">
          <source :src="videoUrl" type="video/mp4">
        </video>
      </div>
      <div @mouseenter="isShowBtn()"  @mouseleave="isHidenBtn()" @click="wantMore()" :class="isPlayying?'font-size-16 wantBtn' : 'font-size-16 wantBtn oapcity0'"><span>{{adText}}</span> <span > I want to learn more <img src="@/assets/img/advertise/video_btn.png" alt=""></span></div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import tools from '@/utils/tools'
// import videoJs from 'video.js'
export default {
  data () {
    return {
      isCheck: false,
      isAnimation: false,
      autoOpenStatus: true,
      videoData: null,
      videoUrl: '',
      adText: '',
      isPlayying: true,
      isCloseShow: true,
      isHouhu: true
    }
  },
  computed: {
    ...mapState({
      isShowAdavter: state => state.common.isShowAdavter, // 显示广告弹框
      isShowIconAdavter: state => state.common.isShowIconAdavter
    })
  },
  created () {
    if (!tools.localItem('advertiseData')) {
      return
    }
    if (tools.localItem('advertiseData')) {
      let advertiseData = JSON.parse(tools.localItem('advertiseData'))
      this.videoData = advertiseData
      if (this.videoData.mediaUrls) {
        this.videoUrl = this.videoData.mediaUrls[0]
      }
      this.adText = this.videoData.adText
      this.autoOpenStatus = this.videoData.autoOpenStatus
      var ua = navigator.userAgent.toLowerCase()
      if (ua.indexOf('firefox') != -1) {
        this.isHouhu = false
      }
    }
  },
  methods: {
    closeAdvter () {
      let myPlayer = document.getElementById('my-video_html5_api')
      if (!myPlayer.paused) {
        myPlayer.pause()
        $('.advertise-video').show()
        this.isPlayying = true
      }
      this.$store.dispatch('setShowIconAdvterAction',true)
      $('.advertise-video').css('display','block')
      let that = this
      setTimeout(function () {
        $('.showAdvter').css('display','none')
        that.$store.dispatch('setShowAdvterAction',false)
        that.$store.dispatch('setShowIconAdvterAction0',true)
      },300)
      if (this.isCheck) {
        this.$axios({
          url: $api.urls().adAutoOpenSwitch + '/' + this.videoData.id,
          method: 'put'
        }).then(res => {
          this.videoData.autoOpenStatus = false
          this.autoOpenStatus = false
          if (this.videoData) {
            tools.localItem('advertiseData',JSON.stringify(this.videoData))
          }
        }).catch(error => {
          this.$message.error(error.response.data.error_message)
        })
      }
    },
    wantMore () {
      //        发送邮件
      this.isCheck = true
      this.videoData.autoOpenStatus = false
      tools.localItem('advertiseData',JSON.stringify(this.videoData))
      this.autoOpenStatus = false
      this.closeAdvter()
      this.$confirm('We will reach out to you by email soon!' , '', {
        showClose: true,
        closeOnClickModal: false,
        showCancelButton: false,
        showConfirmButton: false,
        center: true,
        customClass: 'wantMoreDialog'
      }).then(() => {

      })
      this.$axios({
        url: $api.urls().sendTryRemindEmail + '/' + this.videoData.id,
        method: 'post'
      }).then(res => {
      })
    },
    beforeClose () {

    },
    isShowBtn () {
      this.isPlayying = true
    },
    isHidenBtn () {
      if (!this.isCloseShow) {
        this.isPlayying = false
      } else {
        this.isPlayying = true
      }
    },
    isChecked () {
      this.isCheck = !this.isCheck
    },
    playVideo () {
      // var myPlayer = videoJs('my-video')
      // if (myPlayer) {
      //   if (myPlayer.paused) {
      //     myPlayer.play()
      //     $('.advertise-video').hide()
      //     this.isCloseShow = false
      //   } else {
      //     myPlayer.pause()
      //     $('.advertise-video').show()
      //     this.isCloseShow = true
      //   }
      // }
    }
  },
  watch: {
    isShowAdavter: {
      deep: true,
      handler: function (newVal,oldVal) {
        if (newVal) {
          let that = this
          setTimeout(function () {
            // video.js  初始化video
            let video = document.getElementById('my-video_html5_api')
            if (video) {
              video.addEventListener('play',function () {
                $('.advertise-video').hide()
                that.isCloseShow = false
              })
              video.addEventListener('pause',function () {
                $('.advertise-video').show()
                that.isCloseShow = true
                that.isPlayying = true
              })
            }
            // videoJs('my-video').ready(function () {

            // })
          },800)
        }
      }
    }
  }
}
</script>
<style lang="less">
  .showAdvter{
    background: #000;
    margin-top: 60px !important;
    border-radius: 5px;
    box-shadow: 0 0px 0px 0 rgba(0,0,0,.1);
    -webkit-box-shadow: 0 0px 0px 0 rgba(0,0,0,.1);
    .el-dialog__header,.el-dialog__body,.el-dialog__footer{
      padding:0;
    }
    .el-dialog__body{
      border-radius: 5px;
      overflow:hidden;
    }
    .el-dialog__headerbtn .el-dialog__close {
        color: #fff;
    }
    video {
      display: block;
    }
    .el-dialog__headerbtn{
      cursor: pointer;
      z-index: 999;
      font-size: 22px;
    }
  }
  .wantBtn{
    width: 100%;
    border: 0;
    padding: 6px 10px;
    text-align: center;
    cursor: pointer;
    // background-color: #13B6B4;
    background: url('../../../assets/img/advertise/video_bg.png') repeat 0 0;
    color:#fff;
    border-radius: 0;
    span{
      display: inline-block;
    }
    span:last-child{
      margin-left:15px;
      padding: 3px 10px;
      border-radius: 20px;
      background-color:#fff;
      color:#FD8900;
      cursor: pointer;
    }
    img{
      margin-left:5px;
    }
  }
  .oapcity0{
     display: none;
  }
  .noMoreTips{
    position: absolute;
    top: -25px;
    right: 0;
    color: #fff;
    height:30px;
    i{
      display: inline-block;
      font-style: normal;
      vertical-align:top;
    }
  }

  .showAgain{
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    vertical-align:top;
    cursor: pointer;
    text-align: center;
    span{
      border: 1px solid #fefefecc;
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-top: 3px;
    }
  }
.img-rotate-animation
{
  animation:mymove 500ms infinite;
  -webkit-animation:mymove 500ms infinite;
  animation-iteration-count: 1;
}
@keyframes mymove
{
    0% {
      transform: scale(1)
    }
    100% {
        transform: scale(0);
    }
}
@-webkit-keyframes mymove /*Safari and Chrome*/
{
  0% {transform: scale(1)}
  100% {
      transform: scale(0);
  }
}
.videoBox{
  position: relative;
  height:auto;
}
.videoPlay{
  position: absolute;
  width: 100%;
  height: 84%;
  z-index: 2;
}
.advertise-video{
    position: absolute;
    left: 50%;
    top: 53%;
    transform: translate(-50%, -50%);
    max-width: 50%;
    z-index: 999;
    cursor: pointer;
    text-align: center;
}

.wantMoreDialog{
  background: url('../../../assets/img/advertise/advter_dialog_img.png') no-repeat 0 0;
  width: 537px;
  height: 244px;
  border: 0;
  box-shadow: 0 0px 0px 0 rgba(0,0,0,.1);
  -webkit-box-shadow: 0 0px 0px 0 rgba(0,0,0,.1);
  vertical-align: initial;
  .el-message-box__message {
    margin: 60px 40px;
    width: 100%;
  }
  .el-message-box__headerbtn {
    top: 76px;
    right: 29px;
    z-index: 888;
  }
}
  .video-js .vjs-big-play-button{
    display: none!important;
  }
  .videoHeight{
    min-height: 300px;
  }
  .my-video-dimensions{
    width: 100% !important;
    height:auto !important;
  }
  .video-js .vjs-tech{
    position: static !important;
  }
  .vjs-button>.vjs-icon-placeholder:before {
    line-height: 2 !important;
  }
  .video-js .vjs-control-bar{
    height:35px !important;
  }
  .video-js .vjs-time-control{
    line-height: 35px !important;
  }
  .video-js .vjs-volume-bar {
    margin: 16px .45em !important;
}
</style>
