<template>
  <header ref="getHeader"
          class="layout-header navbar-fixed-top"
          id="guide-app-header-navbar">
    <!-- <768px -->
    <div class="navbar-header bg-white-only">
      <button class="pull-right visible-xs-block"
              data-target=".navbar-collapse"
              @click="targetCollapse">
        <i class="glyphicon glyphicon-cog"></i>
      </button>
    </div>
    <!-- <768px -->

    <!-- navbar collapse -->
    <div class="collapse navbar-collapse box-shadow font-size-16 bg-white-only padder m-l-none"
         :class="{ show: isShow }">
      <div style="display: flex">
        <div id="navLogo">
          <!-- link and dropdown -->
          <div class="navbar-brand font-size-18">
            <a :href="navUrl.home">
              <img v-if="!isChina"
                   src="@/assets/img/us/genie_cloud.png"
                   alt="." />
              <img v-else
                   src="@/assets/img/cn/genie_cloud.png"
                   alt="." />
            </a>
            <!-- grantee权限 -->
            <ul class="nav navbar-nav navbar-right m-l-sm"
                v-if="
                currentType == 'grantee' || currentType == 'special_education'
              ">
              <li class="dropdown">
                <a data-toggle="dropdown"
                   role="button"
                   aria-haspopup="true"
                   aria-expanded="false"
                   href="javascript:;"
                   class="dropdown-toggle clear text-ellipsis"
                   style="max-width:200px"
                   :title="selectedAgency.name">
                  <b class="caret"
                     style="float: right; margin-top: 10px;"></b>
                  <span class="hidden-sm hidden-md">{{
                    selectedAgency.name
                  }}</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-left"
                    style="max-width:200px">
                  <li v-for="(agency, index) in agencies"
                      :key="index"
                      class="dropdown-submenu"
                      :title="agency.name"
                      :class="{
                      active:
                        agency.id == selectedAgency.id ||
                        agency.name == selectedAgency.type,
                    }">
                    <template v-if="
                        agency.otherPaidAgencies ||
                        agency.paid ||
                        agency.permission == '*'
                      ">
                      <a class="text-ellipsis"
                         @click="selectAgency(agency, true)">
                        <span>{{ agency.name }}</span>
                        <i class="fa fa-angle-right pull-right"
                           v-if="
                            agency.otherPaidAgencies &&
                            agency.otherPaidAgencies.length &&
                            currentTopic == 'analysis'
                          "
                           style="margin-top: 2px"></i>
                      </a>
                      <ul class="dropdown-menu"
                          style="max-height: 300px; overflow-y: auto"
                          v-if="
                          agency.otherPaidAgencies &&
                          agency.otherPaidAgencies.length &&
                          currentTopic == 'analysis'
                        ">
                        <li v-for="(other, index) in agency.otherPaidAgencies"
                            :key="index"
                            :class="{ active: other.id == selectedAgency.id }">
                          <a href="javascript:;"
                             @click="selectAgency(other, true, 'Others')">
                            <span ng-bind="other.otherName"></span>
                          </a>
                        </li>
                      </ul>
                    </template>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
        <!-- nabar right -->

        <!-- 正常显示 -->
        <div style="flex: 1">
          <!-- nav navbar-nav -->
          <ul v-show="!isSFTP"
              style="white-space: nowrap"
              class="headerType nav navbar-nav hidden-sm"
              id="topNavBar">
            <li>
              <a id="navHome"
                 :href="navUrl.home">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span :style="currentUser.role.toLowerCase() === 'parent' && $route.path.includes('parent') ? 'color: #10b3b7;' : ''">{{ $t("loc.home") }}</span>
              </a>
              <div class="header-tabActiveBottom"
                   :style="currentUser.role.toLowerCase() === 'parent' && $route.path.includes('parent') ? 'display: block;' : ''"></div>
            </li>
            <!--            parentChatOpen &&  判断家长是否开启了这个功能 parentChatOpen && -->
            <li v-if="parentChatOpen && currentUser.role.toLowerCase() == 'parent'">
              <a id="navWebChat"
                 @click="openWebChat()">{{ $t("loc.webChat")
                }}<span v-show="webChatCount"
                      class="notify"></span></a>
            </li>
            <!-- <li

              :class="
                $route.path.indexOf('attendance-review') > 0 ? 'tab-active' : ''
              "
            >
              <router-link id="healthViewOpen1" to="/datahub/DataHubMenu">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>Data Hub</span>
              </router-link>
              <div class="header-tabActiveBottom"></div>
            </li> -->
            <!-- <li>
              <a :href="navUrl.datahub">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span> Data Hub </span>
              </a>
              <div class="header-tabActiveBottom"></div>
            </li> -->
            <!-- dashboard 新版导航栏 start-->
            <li v-if="
                (assessmentOpen || engagementOpen || analyReportOpen || currentUser.dashboard_engagement_v2_open)
              "
                :class="{ 'text-primary': $route.path.includes('dashboard') }">
              <a id="navDashboard"
                 class="dropdown-toggle"
                 data-toggle="dropdown"
                 role="button"
                 aria-haspopup="true"
                 aria-expanded="false">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.dataHub") }}</span>
                <b class="caret"></b>
              </a>
              <div :class="{
                  'header-tabActiveBottom': $route.path.includes('dashboard'),
                }"></div>
              <ul class="dropdown-menu">
                <li v-if="assessmentOpen">
                  <a :href="navUrl.assessment">{{ $t("loc.assPro") }}</a>
                </li>
                <li v-if="engagementOpen || currentUser.dashboard_engagement_v2_open">
                  <template v-if="currentUser.dashboard_engagement_v2_open">
                    <router-link to="/dashboard/engagement"
                                 id="navEngagement">{{
                    $t("loc.parEnga")
                  }}</router-link>
                  </template>
                  <template v-else>
                    <a :href="navUrl.engagement">
                      <span>{{ $t("loc.parEnga") }}</span>
                    </a>
                  </template>
                </li>
                <li v-if="analyReportOpen">
                  <router-link to="/datahub/DRDPReport/Menu/ParentProgressReport">{{
                    $t("loc.drdpReport")
                  }}</router-link>
                </li>
              </ul>
            </li>
            <!-- 新版导航栏 end -->
            <!-- dashboard 旧版导航栏 statr -->
            <!--v-if="dashboardOpen && !currentUser.dashboard_engagement_v2_open && !analyReportOpen" -->
            <li v-if="dashboardOpen && !currentUser.dashboard_engagement_v2_open && !analyReportOpen">
              <a id="navDashboard"
                 :href="navUrl.dashboard">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.dashboard") }}</span>
              </a>
              <div class="header-tabActiveBottom"></div>
            </li>
            <!-- 旧版导航栏 end -->
            <li v-if=" noteReviewOpen">
              <a class="dropdown-toggle clear"
                 data-toggle="dropdown"
                 role="button"
                 aria-haspopup="true"
                 aria-expanded="false">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.hearPortfolio") }}</span>
                <b class="caret"></b>
              </a>
              <div class="header-tabActiveBottom"></div>
              <ul class="dropdown-menu">
                <li v-if="currentUser.oldARMOpen">
                  <a id="naletM"
                     @click="openAnalyticalReport">{{
                    $t("loc.analyRep")
                  }}</a>
                </li>
                <li v-if="noteReviewOpen">
                  <a id="navNotesReview"
                     :href="navUrl.noteReview">{{
                    $t("loc.noteReview")
                  }}</a>
                </li>
              </ul>
            </li>
            <li v-if="
                messageOpen ||
                approvalOpen ||
                webChatOpen ||
                eventOpen ||
                inkind
              "
                :class="{ 'text-primary': $route.path.includes('inkindreview')
                  || $route.path.includes('messages') }">
              <a class="dropdown-toggle clear"
                 data-toggle="dropdown"
                 role="button"
                 aria-haspopup="true"
                 aria-expanded="false">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.sendMessg") }}</span>
                <b class="caret"></b>
                <span v-show="
                    eventNotify ||
                    webChatCount ||
                    inKindNotify ||
                    approvalQueueRedTip||
                    surveyNotify
                  "
                      class="notify"></span>
              </a>
              <div :class="{
                  'header-tabActiveBottom': $route.path.includes('inkindreview')
                  || $route.path.includes('messages'),
                }"></div>
              <ul class="dropdown-menu">
                <li v-if="messageOpen">
                  <router-link :to="{name:'SendSchoolMessage'}">{{ $t("loc.schoolMessage") }}</router-link>
                </li>
                <li v-if="approvalOpen">
                  <a id="navApproveQueue"
                     :href="navUrl.approval">{{ $t("loc.approvalQueue")
                    }}<span v-show="approvalQueueRedTip"
                          class="notify"></span></a>
                </li>
                <li v-if="webChatOpen">
                  <a id="navWebChat"
                     @click="openWebChat()">{{ $t("loc.webChat")
                    }}<span v-show="webChatCount"
                          class="notify"></span></a>
                </li>
                <li v-if="eventOpen">
                  <a id="navEvents"
                     :href="navUrl.events">{{ $t("loc.events")
                    }}<span v-show="eventNotify"
                          class="notify"></span></a>
                </li>
                <li v-if="inkind">
                  <router-link :to="'/inkindreview/inkindselect'"
                               v-if="!isChina">{{ $t("loc.inkindReport")
                    }}<span v-show="inKindNotify"
                          class="notify"></span>
                    <span class="newPoint"
                          v-show="inkindNewStatue">new</span>
                  </router-link>
                </li>
                <li v-if="surveyOpen"
                    :class="$route.path.indexOf('survey') > 0 ? 'tab-active' : ''">
                  <router-link id="surveyViewOpen"
                               to="/survey">
                    <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                    <span>{{ $t("loc.survey") }}</span>
                    <span v-show="surveyNotify"
                          class="notify"></span>
                    <el-tag size="mini"
                            effect="plain"
                            type="danger"
                            class="pull-right">Beta</el-tag>
                  </router-link>
                  <!-- <div class="header-tabActiveBottom"></div> -->
                </li>
              </ul>
            </li>
            <li v-if="lessonOpen">
              <a id="navLessons"
                 :href="navUrl.lessons">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.lessons") }}</span>
              </a>
              <div class="header-tabActiveBottom"></div>
            </li>
            <li v-if="healthCheckOpen"
                :class="
                $route.path.indexOf('attendance-review') > 0 ? 'tab-active' : ''
              ">
              <router-link id="healthViewOpen1"
                           to="/attendance-review/centerList">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.attenDance") }}</span>
              </router-link>
              <div class="header-tabActiveBottom"></div>
            </li>
            <li v-if="healthViewOpen"
                :class="
                $route.path.indexOf('healthstats') > 0 ? 'tab-active' : ''
              ">
              <router-link id="healthViewOpen"
                           to="/healthstats/child">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.healthStatistics") }}</span>
              </router-link>
              <div class="header-tabActiveBottom"></div>
            </li>
            <li v-if="lesson2Open || planOpen"
                :class=" $route.path.indexOf('lessons') > 0 ? 'tab-active' : '' ">
              <router-link :to="{name: lesson2RouteName}">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>{{ $t("loc.menuLesson2") }}</span>
              </router-link>
              <div class="header-tabActiveBottom"></div>
            </li>
            <li v-if="lesson2Open || planOpen"
                :class=" $route.path.indexOf('curriculum-genie') > 0 ? 'tab-active' : '' ">
              <router-link :to="{name: 'unit-list'}">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span>Curriculum Genie</span>
              </router-link>
              <div class="header-tabActiveBottom"></div>
            </li>
            <!--dll 教练入口-->
            <!--            <li v-if="dllCoachOpen && currentUser.role.toLowerCase() !== 'parent'"-->
            <!-- 暂时默认关闭dll review  -->
            <li v-if="false"
                :class=" $route.path.indexOf('dllCoach') > 0 ? 'tab-active' : '' ">
              <router-link to="/dllCoach">
                <i class="fa fa-fw fa-plus visible-xs-inline-block"></i>
                <span :style="{'color':$route.path.indexOf('dllCoach') > 0 ? '#10B3B7' : 'inherit'}">{{$t('loc.dllReview')}}</span>
              </router-link>
              <div class="header-tabActiveBottom"></div>
            </li>
          </ul>
        </div>
        <!-- ≥768px显示 -->
        <ul class="nav navbar navbar-right pull-right visible-sm">
          <li class="dropdown"
              style="height: 50px; padding-top: 4px">
            <a href="javascript:;"
               class="dropdown-toggle clear"
               data-toggle="dropdown"
               role="button"
               aria-haspopup="true"
               aria-expanded="false">
              <i class="glyphicon glyphicon-align-justify"></i>
            </a>
            <ul class="dropdown-menu navbar-right-ul">
              <li>
                <a :href="navUrl.home">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.home") }}</span>
                </a>
              </li>
              <!-- Assessment Progress -->
              <li v-if="assessmentOpen">
                <a :href="navUrl.assessment">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.assPro") }}</span>
                </a>
              </li>
              <!-- Family Engagement -->
              <li v-if="engagementOpen || currentUser.dashboard_engagement_v2_open">
                <template v-if="currentUser.dashboard_engagement_v2_open">
                  <router-link to="/dashboard/engagement"
                               id="navEngagement">
                    <i class="fa fa-fw fa-plus"></i>{{
                    $t("loc.parEnga")
                  }}
                  </router-link>
                </template>
                <a :href="navUrl.engagement">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.parEnga") }}</span>
                </a>
              </li>
              <!-- DRDP-Report -->
              <li v-if="analyReportOpen">
                <router-link to="/datahub/DRDPReport/Menu/ParentProgressReport">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{$t("loc.drdpReport")}}</span>
                </router-link>
              </li>
              <!-- Analytical Reports 老 ARM 已关闭 -->
              <!-- <li v-if="analyReportOpen">
                <a @click="openAnalyticalReport">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.analyRep") }}</span>
                </a>
              </li> -->
              <!-- Notes Review -->
              <li v-if="noteReviewOpen">
                <a :href="navUrl.noteReview">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.noteReview") }}</span>
                </a>
              </li>
              <!-- School Message -->
              <li v-if="messageOpen">
                <router-link :to="{name:'SendSchoolMessage'}">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.schoolMessage") }}</span>
                </router-link>
              </li>
              <!-- Approval Queue -->
              <li v-if="approvalOpen">
                <a :href="navUrl.approval">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.approvalQueue") }}</span>
                </a>
              </li>
              <!-- Chat -->
              <li v-if="webChatOpen">
                <a @click="openWebChat()">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.webChat") }}</span>
                </a>
              </li>
              <!-- Events -->
              <li v-if="eventOpen">
                <a :href="navUrl.events">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.events") }}</span>
                </a>
              </li>
              <!-- In-Kind Report -->
              <li v-if="inkind">
                <router-link to="/inkindreview/inkindselect"
                             v-if="!isChina">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.inkindReport") }}</span>
                </router-link>
              </li>
              <!-- Survey -->
              <li v-if="surveyOpen">
                <router-link to="/survey">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.survey") }}</span>
                </router-link>
              </li>
              <!-- Lessons -->
              <li v-if="lessonOpen">
                <a :href="navUrl.lessons">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.lessons") }}</span>
                </a>
              </li>
              <!-- Attendance -->
              <li v-if="healthCheckOpen"
                  :class="
                  navUrl.attendance.indexOf($route.path) > 0
                    ? 'router-link-active'
                    : ''
                ">
                <router-link to="/attendance-review/centerList">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.attenDance") }}</span>
                </router-link>
              </li>
              <!-- Daily Health Card Stats -->
              <li v-if="healthViewOpen"
                  :class="
                  navUrl.healthStats.indexOf($route.path) > 0
                    ? 'router-link-active'
                    : ''
                ">
                <router-link to="/healthstats/child">
                  <i class="fa fa-fw fa-plus"></i>
                  <span>{{ $t("loc.healthStatistics") }}</span>
                </router-link>
              </li>
              <!--dll coach-->
              <li v-if="dllCoachOpen && currentUser.role.toLowerCase() !== 'parent'"
                  :class="
                  navUrl.dllCoach.indexOf($route.path) > 0
                    ? 'router-link-active'
                    : ''
                ">
                <router-link to="/dllCoach">
                  <i class="fa fa-fw fa-plus"></i>
                  <span :style="{'color':navUrl.dllCoach.indexOf($route.path) > 0
                    ? '#10B3B7' : 'inherit'}">{{$t('loc.dllReview')}}</span>
                </router-link>
              </li>
            </ul>
          </li>
        </ul>
        <div style="">
          <ul class="nav navbar-nav navbar-right"
              style="margin-right: 0">
            <li v-show="advertiseShow"
                style="padding: 13px 10px 0 0; cursor: pointer"
                :class="isShowIconAdavter0 ? 'animation-advertise' : ''"
                @click="openAdvert()">
              <img src="@/assets/img/us/advertise.png"
                   alt="" />
            </li>
            <!-- 培训课程 -->
            <li v-if="courseOpen"
                style="height: 50px"
                :class="$route.path.indexOf('/courses/') > -1 && $route.path.indexOf('admin') === -1 ? 'tab-active' : ''">
              <el-tooltip :content="$t('loc.lgCourse')"
                          placement="bottom"
                          :open-delay=300>
                <a @click="openCourse">
                  <i class="visible-xs-inline-block"></i>
                  <img src="@/assets/img/us/course_logo.png"
                       style="width: 18px;">
                </a>
              </el-tooltip>
            </li>
            <li style="height: 50px">
              <!-- $t('loc.knowledgeBase') -->
              <el-tooltip :content="$t('loc.helpAndResources')"
                          :open-delay=300
                          placement="bottom">
                <a v-if="!isChina"
                   class="clear pull-left noticeIcon h-full dropdown-toggle"
                   role="button"
                   data-toggle="dropdown"
                   @click="knowledgeClick()"
                   target="_blank"
                   aria-haspopup="true"
                   aria-expanded="false">
                  <img src="@/assets/img/us/knowledge_base.png"
                       height="18">
                </a>
              </el-tooltip>

              <ul class="dropdown-menu w pull-none-xs"
                  style="left: 0px">
                <li>
                  <el-tooltip 
                      :content="$t('loc.helpAndResources')"
                      :open-delay=300
                      placement="bottom">
                      <a 
                        :href="knowledgeBaseUrl"
                        target="_blank"
                        @click="knowledgeLibraryClick()"
                        id="navMyProfile">
                        {{ $t("loc.knowledgeBase") }}
                      </a>
                  </el-tooltip>
                </li>
                <!--                  跳转链接 -->
                <li>
                  <a href="https://www.youtube.com/c/LearningGenie"
                     @click="knowledgeVideoGuide()"
                     target="_blank">
                    {{
                      $t("loc.VideoGuide")
                    }}
                  </a>
                </li>
                <li>
                  <!--                  跳转链接 -->
                  <a href="javascript:;"
                     id="parent-contact-us"
                     v-show="false"
                     @click="contactUs()">
                    {{ $t("loc.ContactUs") }}
                  </a>
                </li>
              </ul>
            </li>
            <li v-if="teacherOpen"
                class="dropdown"
                style="height: 50px">
              <el-tooltip :content="$t('loc.notific')"
                          :open-delay=300
                          placement="bottom">
                <a href="javascript:;"
                   v-if="!isChina"
                   class="dropdown-toggle clear noticeIcon h-full"
                   @click="getNotifications()"
                   data-toggle="dropdown"
                   role="button"
                   aria-haspopup="true"
                   aria-expanded="false">
                  <img src="@/assets/img/us/notification.png"
                       style="width: 16px;">
                  <div v-if="unreadCount > 0"
                       style="
                      height: 5px;
                      width: 5px;
                      position: absolute;
                      top: 16px;
                      right: 17px;
                      background: #fa6666;
                      border-radius: 5px;
                    "></div>
                </a>
              </el-tooltip>
              <!-- 通知消息 -->
              <ul class="dropdown-menu notificationTab"
                  style="position: fixed; top:0; right: -2px; padding-top: 0;">
                <li class="clear-float font-size-20 text-white">
                  <span class="pull-left m-l-md font-bold">{{
                    $t("loc.nftions")
                  }}</span>
                  <i class="fa fa-times lg-pointer pull-right m-r-xs padder-md"
                     style="line-height: 70px; padding-right: 10px"></i>
                </li>
                <div style="
                    max-height: 200px;
                    overflow-y: auto;
                    overflow-x: hidden;
                  ">
                  <template v-if="releaseNotifacations && releaseNotifacations.length">
                    <li style="background: #fff"
                        :key="index"
                        v-for="(updateInfo, index) in releaseNotifacations">
                      <div class="clearfix font-size-16"
                           style="padding: 10px 20px 10px 10px">
                        <div style="width: 15px; float: left; margin-top: 15px">
                          <div v-show="updateInfo.isRead == false"
                               style="
                              height: 6px;
                              width: 6px;
                              background: #fa6666;
                              border-radius: 5px;
                            "></div>
                        </div>
                        <span class="pull-left thumb-sm avatar m-r">
                          <img :src="updateInfo.iconUrl"
                               alt="..." />
                        </span>
                        <span class="clear">
                          <span style="display: inline-block; max-width: 260px"
                                class="text-success font-size-16 overflow-ellipsis">{{ updateInfo.subject }}</span>
                          <span class="pull-right text-muted font-size-14">{{
                            updateInfo.strReceiveAtUtc
                          }}</span>
                          <small class="clear font-size-14">
                            <div class="overflow-ellipsis pull-left"
                                 style="max-width: 240px">
                              <span>{{ updateInfo.summary }}</span>
                            </div>
                            <a :href="'/#/updateMessage/' + updateInfo.id"
                               class="text-primary m-l-sm pull-left">
                              <small class="font-size-14">{{
                                $t("loc.readMore")
                              }}</small>
                            </a>
                          </small>
                        </span>
                      </div>
                    </li>
                  </template>
                  <li v-if="notificationLoading"
                      class="text-center m-t-md m-b-md">
                    <img src="@/assets/img/notification/timg.gif"
                         style="height: 60px"
                         alt="" />
                  </li>
                </div>
                <div v-if="releaseNotifacations && releaseNotifacations.length"
                     style="height: 1px; background: #e8e7e7; overflow: hidden"></div>
                <li v-if="releaseNotifacations && releaseNotifacations.length"
                    class="text-center m-t-sm m-b-sm">
                  <span class="lg-pointer text-primary font-size-16"><a href="/#/updateMessage/more">{{
                      $t("loc.allNotifis")
                    }}</a></span>
                </li>
                <li v-show="
                    (!releaseNotifacations || !releaseNotifacations.length) &&
                    !notificationLoading &&
                    noNotific
                  "
                    class="text-center m-t-md m-b-md font-size-16">
                  <span>{{ $t("loc.noNotific") }}</span>
                </li>
              </ul>
            </li>
            <li v-if="adminOpen"
                style="height: 50px"
                :class="
                $route.path.indexOf('custom_portfolio_tags') > 0 || $route.path.indexOf('admin/courses') > 0 ? 'tab-active' : ''
                ">
              <el-tooltip :content="$t('loc.admSet')"
                          :open-delay=300
                          placement="bottom">
                <a :href="navUrl.adminSeting">
                  <i class=" visible-xs-inline-block"></i>
                  <img src="@/assets/img/us/admin_setting.png"
                       style="width: 18px;">
                </a>
              </el-tooltip>
            </li>
            <li class="dropdown">
              <a class="pull-left dropdown-toggle clear userInfo"
                 id="navAccount"
                 data-toggle="dropdown"
                 role="button"
                 aria-haspopup="true"
                 aria-expanded="false">
                <div class="hidden-sm hidden-md display-flex align-items">
                  <span class="display-flex justify-content flex-direction-col"
                        style="max-width:150px">
                    <div class="text-ellipsis"
                         :title="userDisplayName">{{ userDisplayName }}</div>
                    <div class="font-size-14 text-muted text-ellipsis"
                         :title="userRole"
                         v-show="userRole">{{ userRole }}</div>
                  </span>
                  <b class="caret m-l-xs"></b>
                </div>
                <span v-if="userInfo !== null && userInfo !== undefined && userInfo.avatarUrl !== null && userInfo.avatarUrl !== undefined"
                      class="thumb-sm avatar pull-right m-t-n-sm m-b-n-sm m-l-sm m-r">
                  <img :src="userInfo.avatarUrl" />
                </span>
              </a>
              <ul class="dropdown-menu w">
                <li>
                  <a :href="navUrl.myProfile"
                     id="navMyProfile">{{
                    $t("loc.myProFile")
                  }}</a>
                  <!-- <router-link to="/edit-profile"
                               id="navMyProfile">{{
                    $t("loc.myProFile")
                  }}</router-link> -->
                </li>
                <li v-if="adminOpen">
                  <a :href="navUrl.adminSeting"
                     id="navAdminSettings">
                    <span>{{ $t("loc.admSet") }}</span>
                    <!-- <span v-show="periodGroupNotify || healthCardPoint"
                          class="headerEvent"></span> -->
                  </a>
                </li>
                <li class="dropdown-submenu pull-left dropdown">
                  <a style="overflow: hidden"
                     id="language">
                    <span class="pull-left">{{ $t("loc.language") }}</span>
                    <i class="fa fa-angle-right pull-right"
                       style="margin-top: 2px"></i>
                  </a>
                  <ul class="dropdown-menu language">
                    <li :class="{ active: selectedLanguage == 'en-US' }"
                        v-if="!isChina">
                      <a style="color: #000 !important"
                         @click="changeLanguage('en-US')">English</a>
                    </li>
                    <!-- <li :class="{ active: selectedLanguage == 'zh-US' }"
                        v-if="isChina">
                      <a style="color: #000 !important"
                         @click="changeLanguage('zh-US')">English</a>
                    </li> -->
                    <li :class="{ active: selectedLanguage == 'pt-BR' }"
                        v-if="!isChina">
                      <a style="color: #000 !important"
                         @click="changeLanguage('pt-BR')">Português do Brasil</a>
                    </li>
                    <li :class="{ active: selectedLanguage == 'es-ES' }"
                        v-if="!isChina">
                      <a style="color: #000 !important"
                         @click="changeLanguage('es-ES')">Español</a>
                    </li>
                    <li :class="{ active: selectedLanguage == 'zh-CN' }">
                      <a style="color: #000 !important"
                         @click="changeLanguage('zh-CN')">简体中文</a>
                    </li>
                  </ul>
                </li>
                <li v-if="!isChina">
                    <a target="_blank" class="flex-space-between" style="display:flex" href="https://www.learning-genie.com/help_29/"
                      >{{ $t("loc.help") }}
                    <i class="lg-icon lg-icon-new-page font-size-14"></i>
                    </a
                    >
                </li>
                <li class="divider"></li>
                <li>
                  <a @click="signOut()"
                     id="navLogout">{{
                    $t("loc.signOut")
                  }}</a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <!-- end 正常显示 -->
        <!-- / link and dropdown -->
      </div>
      <!--页脚聊天信息提示-->
      <div class="msgTips"
           v-show="messagesHasMsg"
           @click="openWebChat()">
        <img :src="messages.avatar_url"
             alt=""
             class="msgTipsImg" />
        <span class="msgTipsUser">
          {{ messages.display_name }}
        </span>
        <i class="msgTipsNum">1</i>
      </div>
    </div>
    <el-dialog width="900px"
               title="Analytical Reporting Module Retirement Notification"
               :visible.sync="ARMNotifcationVisible"
               append-to-body>
      <ARMNotifcation :close="()=>{ARMNotifcationVisible = false}" />
    </el-dialog>
  </header>
</template>

<script>
import tools from '@/utils/tools'
import { setLocale } from '@/utils/i18n'
import { toSignOut } from '@/utils/autoSignOut'
import { mapState, mapActions } from 'vuex'
import { acrossRole, getCurrentUser, getSelectedAgency } from '@/utils/common'
// import videoJs from 'video.js'
import { bus } from '@/utils/bus'
import ARMNotifcation from './ARMNotifcation'
import awsS3Service from '@/utils/awsS3Service'
import { LAST_NOTIFY_TIME } from '../../../utils/const'
export default {
  name: 'LayoutHeader',
  props: {
    headerHeight: {
      type: Function,
      default: () => { }
    },
    headerType: {
      type: String,
      default: ''
    }
  },
  components: {
    ARMNotifcation
  },
  data () {
    return {
      currentNav: '', // 当前选中导航
      isShow: false, // 小屏是否显示列表
      selectedLanguage: '', // 当前语言
      selectedAgency: {}, // 当前机构
      notificationLoading: false, // 通知loading
      releaseNotifacations: null, // 通知
      periodGroupNotify: false,
      noNotific: false, // 没有通知
      // unreadCount: false, // 是否有未读消息
      // eventNotify: false, // 是否有未读消息
      isNotSelfAgency: false, // 是否是自己的机构
      inkind: false, // 是否显示inkind
      inkindNewStatue: false, // inkind 是否显示new
      advtdialogVisible: false, //
      advertiseShow: false, // 控制广告弹框是否显示
      approvalQueueRedTip: false, // 控制approvalQueue红点显示
      // healthOpen: false, // 是否有健康卡权限
      // healthViewOpen: false, // 是否有健康卡统计查看权限
      // healthCardPoint: false, // 健康卡设置红点显示
      parentChatOpen: false, //
      isSFTP: false,
      ARMNotifcationVisible: false,
      navUrl: { // 当前导航链接
        home: '',
        dashboard: '',
        analysis: '/#/analysis/measure/detail',
        lessons: '/#/lessons/public/list',
        messages: '/#/notification/email',
        events: '/#/notification/event',
        approval: '/#/approval',
        noteReview: '/#/notesReview/notesList//',
        myProfile: '/#/parent/edit-profile',
        adminSeting: '/#/import_center/select',
        assessment: '/#/dashboard/assessment',
        engagement: '/#/dashboard/engagement',
        // inKind: '/#/inkindreview/in-kind-table/' + true,
        healthStats: '/#/healthstats/child',
        attendance: '/#/attendance-review/centerList',
        datahub: '/#/datahub/ChildProgressReport',
        dllCoach: '/#/dllCoach' // dll教练的路径
      }
    }
  },
  async created () {
    if ('class_set_period_sftp'.includes(this.$route.name)) {
      this.isSFTP = true
    }
    if (!this.currentUser) {
      this.$store.dispatch('getUserInfoAction')
    }
    this.navUrl.home = `/#/centers/${this.currentUser.default_center_id}/groups`
    this.navUrl.myProfile = `/#/centers/${this.currentUser.default_center_id}/editProfile`

    if (this.currentUser && this.currentUser.role.toLowerCase() == 'parent') {
      this.navUrl.home = `/#/parent`
      this.parentChatOpen = this.currentUser.webChatOpen
      this.navUrl.myProfile = '/v2/#/edit-profile'
    }

    this.navUrl.dashboard = this.currentUser.assessmentProgressOpen ? '/#/dashboard/assessment' : '/#/dashboard/engagement'
    this.inkindHeaderOpen()
    this.setApprovalQueueRedTip()
    this.getNotification()
    if (tools.localItem('advertiseData')) {
      let advertiseData = tools.localItem('advertiseData')
      advertiseData = JSON.parse(advertiseData || '{}')
      if (advertiseData.showStatus) {
        this.advertiseShow = advertiseData.showStatus
      }
    }
    this.periodGroupNotify = JSON.parse(
      localStorage.getItem('periodGroupNotify')
    )
    // // 获取 In-Kind 设置
    // let data = await this.$axios.get($api.urls().getRatifySetting)
    // this.$store.dispatch('setRatifyModeAction', data.ratifiedMode)
    // this.$store.dispatch('setSiteAdminThirdPartyApprovalOpen', data.siteAdminThirdPartyApprovalOpen)
  },
  mounted () {
    this.init()
    let role = {
      assessmentOpen: this.assessmentOpen,
      engagementOpen: this.engagementOpen,
      analyReportOpen: this.analyReportOpen,
      lessonOpen: this.lessonOpen,
      messageOpen: this.messageOpen,
      dashboardMessageOpen: this.currentUser.message_open,
      approvalOpen: this.approvalOpen,
      noteReviewOpen: this.noteReviewOpen,
      webChatOpen: this.webChatOpen,
      eventOpen: this.eventOpen,
      adminOpen: this.adminOpen
    }
    this.$store.dispatch('setCurrentRoleAction', role)
    const that = this
    window.onresize = () => {
      that.headerHeight(this.height - 50)
    }
    bus.$on('activeHeaderTab', () => {
      this.currentNav = $router.currentRoute.path
    })
  },
  updated () {
    this.headerHeight(this.height - 50)
  },
  methods: {
    // 点击知识库下拉菜单
    knowledgeClick () {
      this.$analytics.sendEvent('web_knowledgebase_click_icon')
    },
    // 点击知识库链接
    knowledgeLibraryClick () {
      this.$analytics.sendEvent('web_knowledgebase_click_klb')
    },
    // 点击video 的链接
    knowledgeVideoGuide () {
      this.$analytics.sendEvent('web_knowledgebase_click_videoguide')
    },
    // 点击联系我们
    contactUs () {
      if (window.zE && window.zE.show) {
        window.zE.show()
        window.zE.activate()
      }
      this.$analytics.sendEvent('web_knowledgebase_click_help')
    },
    refresh () {
      this.$store.dispatch('addSignVersion') // 签到
      this.$store.dispatch('addHealthCardVersion') // 健康检查卡
      this.$store.dispatch('addHealthCheckCardVersion') // 视觉健康检查卡

      setTimeout(() => {
        this.refresh()
      }, 5 * 1000)
    },
    // 打开培训视频
    openCourse () {
      this.$router.push({
        name: 'videoList'
      })
    },
    openAnalyticalReport () {
      if (this.analyReportOpen && !this.currentUser.oldARMOpen) {
        this.ARMNotifcationVisible = true
      } else {
        location.href = '/#/analysis/measure/detail'
      }
    },
    ...mapActions([
      'getUnReadCountAction',
      'getEventNotifyAction',
      'getAgencyTipsAction',
      'setRefreshSignReview',
      'addSignVersion', // 签到
      'addHealthCardVersion', // 健康检查卡
      'addHealthCheckCardVersion', // 视觉健康检查卡
      'getSurveyNotifyAction'
    ]),
    myAcrossRole (...values) {
      return acrossRole(...values)
    },
    // 初始化
    init () {
      // 初始化lang
      let language = tools.localItem('NG_TRANSLATE_LANG_KEY') // 语言
      let currentUser = this.currentUser // 当前用户
      let currentType = this.currentType // 当前用户类型
      this.selectedLanguage = language
      // 存储selectedAgency,data,初始化
      if (currentType == 'grantee' || currentType == 'special_education') {
        let defaultAgency = this.getDefaultAgency(
          currentUser.default_agency_id
        )
        this.selectedAgency = this.granteeSelectedAgency ? this.granteeSelectedAgency : defaultAgency
        this.selectAgency(this.selectedAgency)
      }
      // 获取当前通知是否存在未读消息
      this.getUnReadCountAction()
      // 获取event通知
      // if (this.currentUser.eventOpen) {
      //   this.getEventNotifyAction()
      // }
      this.getAgencyTipsAction()
      this.getEventNotifyAction().then(res => {
        const data = this.open
        // 初始化s3Service
        if (data.awsIdentityPoolConfig) {
          awsS3Service.init(data.awsIdentityPoolConfig.bucketRegion, data.awsIdentityPoolConfig.bucketName, data.awsIdentityPoolConfig.identityPoolId)
        }
      })

      // 获取是否有待填写问卷红点
      this.getSurveyNotifyAction()
      // 改变导航选中状态
      this.currentNav = $router.currentRoute.path
    },
    // 切换语言
    changeLanguage (lang) {
      this.$store
        .dispatch('setLanguageAction', lang)
        .then((res) => {
          setLocale(lang)
          this.selectedLanguage = lang
          this.$router.go(0)
        })
        .catch((err) => {
          this.$message.error(err)
        })
    },
    // 切换<750时的下拉框
    targetCollapse () {
      this.isShow = !this.isShow
    },
    // getHealthCardPoint () {
    //     this.healthOpen = this.open.agencyOpenHealthCheck
    //     if (this.healthOpen) {
    //       this.healthViewOpen = this.open.healthStatistics && this.open.haveGroup
    //       this.healthCardPoint = this.open.notifyHealthCheckFunction
    //     }
    // },
    // grantee切换机构
    selectAgency (agency, reset, type) {
      if (!this.currentUser) {
        return ''
      }
      // let currentUser = this.currentUser
      if (
        !agency ||
        (this.selectedAgency && this.selectedAgency.id == agency.id)
      ) {
        return
      }
      if (type) {
        agency.type = type
      }
      if (agency.self && this.currentUser.type.toLowerCase() == 'grantee') {
        this.isNotSelfAgency = false
      } else {
        this.isNotSelfAgency = true
      }
      this.selectedAgency = agency
      sessionStorage.setItem('selectedAgency', JSON.stringify(agency))
      if (reset) {
        window.location.href = '/#/centers/' + agency.id + '/groups'
        // if (this.$router.fullPath == '/home/<USER>') {
        //   this.$router.go(0)
        // } else {
        //   this.$router.push({ path: '/home/<USER>' })
        // }
      }
    },
    // 获取默认机构
    getDefaultAgency (agencyId) {
      if (!this.currentUser) {
        return ''
      }
      let defaultAgency
      let currentUser = this.currentUser
      if (agencyId) {
        if (currentUser.default_agency_id) {
          for (let ii = 0; ii < this.agencies.length; ii++) {
            let el = this.agencies[ii]
            if (el.id == agencyId) {
              defaultAgency = el
              return defaultAgency
            }
          }
        }
        if (!defaultAgency) {
          defaultAgency = this.agencies[0]
        }
        return defaultAgency
      }
      return defaultAgency
    },
    // 退出
    signOut () {
      // 弹出框提示
      this.$confirm(this.$t('loc.areYouSignOut'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        // type: 'warning',
        customClass: 'lg-modal-warning'
      })
        .then(() => {
          // success
          toSignOut()
        })
        .catch((error) => {
          return error
        })
    },
    // 获取通知
    getNotifications () {
      // 获取store所有信息
      this.releaseNotifacations = this.$store.getters.notifications
      // 开启loading
      this.notificationLoading = true
      // 请求接口
      this.$axios
        .get($api.urls().releasenote, { params: { releaseNoteNum: '' } })
        .then((data) => {
          if (data.length <= 0) {
            this.noNotific = true
          }
          // 多语言日期本地化
          data.forEach((notification) => {
            let monthDay = this.$d(
              new Date(notification.strReceiveAtUtc),
              'monthDay'
            )
            notification.strReceiveAtUtc = monthDay
          })
          // 更新用户信息
          this.$store.dispatch('setNotificationsAction', data)
          if (!this.currentUser.releaseReadingState) {
            this.currentUser.releaseReadingState = true
            this.$store.dispatch('setCurrentUserAction', this.currentUser)
          }
          this.releaseNotifacations = data
          // 关闭loading
          this.notificationLoading = false
        })
        .catch((error) => {
          this.notificationLoading = false
        })
      // 进入到通知列表
      this.$analytics.sendEvent('web_notifications_exposure_enter')
    },
    openWebChat () {
      if (!this.currentUser.commInfo) {
        this.$message.error(this.$t('loc.noChatGroup'))
        return
      }
      localStorage.setItem('isContChat', false)
      let env = process.env.VUE_APP_CURRENTMODE
      let url = '/v2/#/webChat'
      if (env === 'local') {
        url = '/#/webChat'
      }
      window.open(url, 'chat')
    },
    inkindHeaderOpen () {
    //   let date = new Date()
    //   let today =
    //     date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
    //   // 登录后调取inkind红点接口，判断是否显示入口
    //   $axios
    //     .get($api.urls().point + '?isNew=true' + '&date=' + today)
    //     .then((data) => {
    //       localStorage.setItem('inkindStatus', JSON.stringify(data))
    //       localStorage.setItem(
    //         'inkindUserStatus',
    //         JSON.stringify(data.inkindUserStatus)
    //       )
    //       this.inkind = data.inkindDisplayStatus
    //       this.inkindNewStatue = data.inkindUserStatus.newStatus
    //       this.$store.dispatch('setInkindNewStatue', this.inkindNewStatue)
    //     })
    },
    // 广告弹框
    openAdvert () {
      // $('.showAdvter').css('display', 'block')
      // this.$store.dispatch('setShowAdvterAction', true)
      // this.$store.dispatch('setShowIconAdvterAction', false)
      // this.$store.dispatch('setShowIconAdvterAction0', false)
      // setTimeout(function () {
      //   let video0 = videoJs('my-video')
      //   let video = document.getElementById('my-video')
      //   if (video) {
      //     let myVideo = document.getElementById('my-video_html5_api')
      //     myVideo.load()
      //     video0.currentTime(0)
      //     $('.vjs-play-progress').css('width', '0')
      //   }
      // }, 0)
    },
    setApprovalQueueRedTip () {
      if (
        acrossRole('site_admin', 'agency_admin', 'agency_owner') &&
        this.currentUser.approval_open &&
        this.currentUser.reportOpen
      ) {
        this.$axios({
          url: $api.urls().approvalReadTip,
          method: 'get'
        })
          .then((res) => {
            this.approvalQueueRedTip = res.approvalNotify
          })
          .catch((error) => {
            this.approvalQueueRedTip = false
            this.$message.error(error.response.data.error_message)
          })
      } else {
        this.approvalQueueRedTip = false
      }
    },

    // 调取轮询接口
    getNotification () {
      let cunrrentUser = getCurrentUser()
      let fromH5 = false
      if (cunrrentUser && cunrrentUser.source && cunrrentUser.source === 'H5') {
        fromH5 = true
      }
      if (!fromH5) {
        window.setInterval(() => {
          setTimeout(this.pollingInterface, 0)
        }, 30000)
      }
    },

    pollingInterface () {
      let params = {
        type: this.executeType,
        userId: this.currentUser.user_id
      }
      this.$axios({
        url: $api.urls().notification,
        params: params,
        method: 'get'
      })
        .then((res) => {
          switch (params.type) {
            case 'ATTENDANCE':
              this.setRefreshSignReview(res.refreshSignReview)
              break
          }
          tools.localItem(LAST_NOTIFY_TIME, JSON.stringify(new Date().getTime()))
        })
        .catch((error) => {
          this.$message.error(error.response.data.error_message)
        })
    },
    // 录制周计划中时，阻止用户离开页面
    handlePlanRecording (e) {
      e = e || window.event
      e.preventDefault() ? e.preventDefault() : (e.returnValue = false)
      e.stopPropagation() ? e.stopPropagation() : (e.cancelBubble = true)
      this.$message.warning(this.$t('loc.plan145'))
    }
  },
  watch: {

    noClassReloadingTitle () {
      this.init()
    },
    // 监听录制周计划状态
    planRecording (val) {
      let header = document.getElementById('guide-app-header-navbar')
      if (val) {
        header.addEventListener('click', this.handlePlanRecording, true)
      } else {
        header.removeEventListener('click', this.handlePlanRecording, true)
      }
    }
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser(), // 当前用户
      granteeSelectedAgency: state => getSelectedAgency(), // grantee 当前选中的机构
      userInfo: state => state.user.currentUser.userInfo, // 当前用户简要信息
      webChatCount: state => state.chat.webChatCount, // 是否存在未读信息（环信）
      messages: state => state.chat.messages, // 未读提示信息
      messagesHasMsg: state => state.chat.messagesHasMsg, // 是否显示该未读提示信息
      unreadCount: state => state.common.unreadCount, // 铃铛的未读提示
      eventNotify: state => state.common.eventNotify, // event未读提示
      inKindNotify: state => state.common.inkindNotify,
      surveyNotify: state => state.common.surveyNotify, // survey红点
      isShowIconAdavter0: state => state.common.isShowIconAdavter0,
      open: state => state.common.open,
      executeType: state => state.common.executeType,
      noClassReloadingTitle: state => state.common.noClassReloadingTitle, // 健康卡设置班级为空时刷新标题头 防止未及时刷新可以误入出席功能
      planRecording: state => state.lesson.planRecording // 是否录制周计划中
    }),
    userDisplayName () {
      let displayName = this.currentUser.display_name
      if (!displayName) {
        return ''
      }
      let output
      let personTypes = [
        { typeName: 'Miss.' },
        { typeName: 'Miss' },
        { typeName: 'Ms.' },
        { typeName: 'Mr.' },
        { typeName: 'Mrs.' }
      ]
      personTypes.forEach((item) => {
        let key = item.typeName
        if (displayName.indexOf(key) !== -1) {
          output = displayName.substr(key.length)
        }
      })
      return output || displayName
    },
    // eslint-disable-next-line vue/return-in-computed-property
    knowledgeBaseUrl () {
      // 根据不同的角色跳转到不同的链接
      let roleKnowledgeUrl = {
        'admin': 'https://learninggenie.zendesk.com/hc/en-us/categories/200229834-For-Admins-Directors',
        'parent': 'https://learninggenie.zendesk.com/hc/en-us/categories/200256284-For-Parents',
        'teacher': 'https://learninggenie.zendesk.com/hc/en-us/categories/200254244-For-Teachers'
      }
      if (!this.currentUser) {
        return undefined
      }
      // 角色
      let role = this.currentUser.role2
      if (!role) {
        return undefined
      }
      // 如果是有角色的，那么对角色进行判断，admin跳转到admin知识库，老师跳转到老师的知识库
      role = role.toLowerCase()
      switch (role) {
        case 'collaborator':
          return roleKnowledgeUrl.teacher
        case 'site_admin':
          return roleKnowledgeUrl.admin
        case 'agency_admin':
          return roleKnowledgeUrl.admin
        case 'agency_owner':
          return roleKnowledgeUrl.admin
        case 'parent':
          return roleKnowledgeUrl.parent
        case 'family_service':
          return roleKnowledgeUrl.teacher
        case 'teaching_assistant':
          return roleKnowledgeUrl.teacher
      }
    },
    // 当前用户角色
    userRole () {
      if (!this.currentUser) {
        return undefined
      }
      // 角色
      let role = this.currentUser.role2
      if (!role) {
        return undefined
      }
      // 类型
      let type = this.currentUser.type
      if (type && type.toLowerCase() === 'grantee') {
        return this.$t('loc.gteAdm')
      }
      if (type && type.toLowerCase() === 'special_education') {
        return this.$t('loc.speList')
      }
      if (role.toLowerCase() === 'parent') {
        return undefined
      }
      // 转换角色名称
      return tools.formatRole(role)
    },
    height () {
      this.headerHeight()
      return this.$refs.getHeader ? this.$refs.getHeader.offsetHeight : 50
    },
    isChina () {
      return this.$store.getters.isChina
    },
    currentType () {
      if (!this.currentUser) {
        return ''
      }
      let type = this.currentUser.type
      return type.toLowerCase()
    },
    agencies () {
      if (!this.currentUser) {
        return []
      }
      let agencies = this.currentUser.agencies
      return agencies
    },
    notDefaultCenterId () {
      let agency = sessionStorage.getItem('selectedAgency')
      agency = JSON.parse(agency)
      if (agency && !agency.permission) {
        return true
      } else if (agency && agency.permission) {
        return false
      }
      if (!this.currentUser.default_center_id) {
        if (
          this.currentUser.role2.toLowerCase() != 'parent' &&
          this.currentUser.type.toLowerCase() != 'grantee'
        ) {
          return true
        }
      }
    },
    currentRole () {
      if (!this.currentUser) {
        return ''
      }
      let role = this.currentUser.role
      return role.toLowerCase()
    },
    // 旧版 dashboard 显示
    dashboardOpen () {
      if (
        acrossRole(
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        (this.currentUser.assessmentProgressOpen ||
          this.currentUser.dashboard_engagement_v1_open)
      ) {
        return true
      } else {
        return false
      }
    },
    // 新版 dashboard 显示
    assessmentOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.assessmentProgressOpen
      ) {
        return true
      } else {
        return false
      }
    },
    engagementOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.dashboard_engagement_v1_open
      ) {
        return true
      } else {
        return false
      }
    },
    analyReportOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.paid &&
        this.currentUser.tempDrdp
      ) {
        return true
      } else {
        return false
      }
    },
    lessonOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.lessonOpen
      ) {
        return true
      } else {
        return false
      }
    },
    messageOpen () {
      if (
        acrossRole('collaborator', 'family_service', 'site_admin', 'agency_admin', 'agency_owner') &&
        !this.isNotSelfAgency &&
        this.currentUser.message_open
      ) {
        return true
      } else {
        return false
      }
    },
    approvalOpen () {
      if (
        acrossRole('site_admin', 'agency_admin', 'agency_owner') &&
        this.currentUser.approval_open &&
        this.currentUser.reportOpen
      ) {
        return true
      } else {
        return false
      }
    },
    noteReviewOpen () {
      if (
        acrossRole('site_admin', 'agency_admin', 'agency_owner') &&
        this.currentUser.note_review_open
      ) {
        return true
      } else {
        return false
      }
    },
    webChatOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.webChatOpen &&
        !this.isChina &&
        !this.currentUser.academy_open
      ) {
        return true
      } else {
        return false
      }
    },
    eventOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        !this.isChina &&
        this.currentUser.eventOpen
      ) {
        return true
      } else {
        return false
      }
    },
    adminOpen () {
      if (
        acrossRole('agency_owner', 'agency_admin', 'site_admin') &&
        !this.notDefaultCenterId
      ) {
        return true
      } else {
        return false
      }
    },
    // 以为 消息通知老师也应该有，所以这里应该为老师增加上权限
    teacherOpen () {
      if (
        acrossRole('agency_owner', 'agency_admin', 'site_admin', 'collaborator', 'family_service', 'teaching_assistant') &&
        !this.notDefaultCenterId
      ) {
        return true
      } else {
        return false
      }
    },
    healthViewOpen () {
      if (!this.open) {
        return false
      }
      return (
        this.open.agencyOpenHealthCheck &&
        this.open.healthStatistics &&
        this.open.haveGroup
      )
    },
    lesson2Open () {
      return acrossRole(
        'teaching_assistant',
        'collaborator',
        'site_admin',
        'agency_admin',
        'agency_owner'
      ) &&
        this.open && this.open.lesson2Open
    },
    // 周计划开关
    planOpen () {
      return acrossRole(
        'teaching_assistant',
        'collaborator',
        'site_admin',
        'agency_admin',
        'agency_owner'
      ) &&
        this.open && this.open.planOpen
    },
    // 系列课程开关
    curriculumOpen () {
      return acrossRole(
        'teaching_assistant',
        'collaborator',
        'site_admin',
        'agency_admin',
        'agency_owner'
      ) &&
        this.open && this.open.curriculumOpen
    },
    healthCardPoint () {
      if (!this.open) {
        return false
      }
      return (
        this.open.agencyOpenHealthCheck && this.open.notifyHealthCheckFunction
      )
    },
    healthCheckOpen () {
      if (!this.open) {
        return false
      }
      return this.open.agencyOpenCheck && this.open.haveGroup
    },
    surveyOpen () {
      return this.currentUser.surveyOpen
    },
    // 判断dll教练是否开启
    dllCoachOpen () {
      return this.open && this.open.dllopen
    },
    // 教学课程开关
    courseOpen () {
      return !acrossRole('parent') && this.open && this.open.trainingCertificateOpen
    },
    // 课程跳转路由
    lesson2RouteName () {
      if (this.planOpen && this.curriculumOpen) {
        return 'lessonCurriculumView'
      }
      return 'PublicLessonList'
    }
  }
}

$(document).ready(function (e) {
  if (window.location.hash) {
    let hash
    if (window.location.hash.includes('?')) {
      hash = window.location.hash.substring(
        window.location.hash.indexOf('#') + 0,
        window.location.hash.indexOf('?')
      )
    } else {
      hash = window.location.hash
    }
    if (
      hash == '#DRS' ||
      hash == '#DRP' ||
      hash == '#DFFI' ||
      hash == '#DCI' ||
      hash == '#DCP' ||
      hash == '#DRP' ||
      hash == '#DRMA' ||
      hash == '#EICS' ||
      hash == '#ORG'
    ) {
      $(hash + 'Content')
        .addClass('pageOn')
        .removeClass('pageOff')
    } else {
      $('#homeContent').addClass('pageOn').removeClass('pageOff')
    }
  } else {
    $('#homeContent').addClass('pageOn').removeClass('pageOff')
  }
  $(window).on('popstate', function () {
    let hash
    if (window.location.hash.includes('?')) {
      hash = window.location.hash.substring(
        window.location.hash.indexOf('#') + 0,
        window.location.hash.indexOf('?')
      )
    } else {
      hash = window.location.hash
    }
    if (
      hash == '#DRS' ||
      hash == '#DRP' ||
      hash == '#DFFI' ||
      hash == '#DCI' ||
      hash == '#DCP' ||
      hash == '#DRP' ||
      hash == '#DRMA' ||
      hash == '#EICS' ||
      hash == '#ORG'
    ) {
      $(this).navigate({ target: $(hash + 'Content') })
      if (window.location.hash.includes('?')) {
      } else {
        location.href = location.href + '?'
      }
    } else {
      $(this).navigate({ target: $('#homeContent') })
    }
  })
})
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.headerType {
  li {
    display: inline;
  }
}
.layout-header {
  .collapse {
    margin: 0;
    ul {
      li {
        position: relative;
        .header-tabActiveBottom {
          display: none;
        }
      }
      .text-primary {
        .header-tabActiveBottom {
          display: block;
        }
      }
      .dropdown-menu {
        .router-link-active {
          background-color: #edf1f2;
        }
      }
      // .router-link-active{
      //   color: #10B3B7;
      //   .header-tabActiveBottom{
      //     display: block
      //   }
      // }
      // &.navbar-right-ul{
      //   .router-link-active{
      //     background-color: #97C23D;
      //     color: #e9f0db;
      //   }
      // }
    }
    .navbar-brand {
      float: left;
      line-height: 49px;
      font-weight: normal;
      img {
        max-height: 36px;
        cursor: pointer;
      }
    }
    .navbar-right {
      .noticeIcon {
        position: relative;
        padding-bottom: 10px;
      }
      .userInfo {
        padding: 0 0 0 15px !important;
        height: 50px !important;
        display: flex;
        align-items: center;
        span {
          img {
            height: 40px;
          }
          font {
            vertical-align: inherit;
            padding-right: 5px;
          }
        }
      }
      .dropdown-submenu.pull-left {
        float: none !important;
        .dropdown-menu.language {
          left: -170px;
          width: 140px;
        }
      }
    }
    // .dropdown-menu-side{
    //   list-style: none;
    //   background: #fdfbfb;
    //   padding: 0;
    //   color: #111C1C;
    //   li{
    //     a{
    //       display: inline-block;
    //       width: 100%;
    //       padding: 5px 15px;
    //       color: #111C1C;
    //     }
    //   }
    // }
  }
}
.notificationTab {
  width: 452px;
  position: fixed;
  top: 0;
  right: -2px;
  padding-top: 0;
  li.clear-float {
    background: url('../../../assets/img/notification/topbg.jpg') no-repeat;
    background-size: 100% 100%;
    height: 70px;
    line-height: 70px;
  }
}
.notify {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgb(250, 102, 102);
  display: inline-block;
  position: relative;
  top: -5px;
  margin: 0 1px;
}
.add-padding-l-36 {
  padding-left: 36px;
}

.animation-advertise {
  animation: advertiseChange 1s infinite;
  -webkit-animation: advertiseChange 1s infinite;
  animation-iteration-count: 1;
}
@keyframes advertiseChange {
  0% {
    transform: translate(-300px, 250px);
  }
  100% {
    transform: translate(0, 0);
  }
}
.tab-active {
  border-bottom: 3px solid #10b3b7;
}
.headerType.nav.navbar-nav.hidden-sm > li > a {
  padding: 15px 10px;
}
</style>
