<template>
  <div class="layout">
    <div class="app-header navbar" v-show="(!$store.getters.viewSource || $store.getters.viewSource !== 'H5') && !v2">
      <LayoutHeader ref="header" :headerHeight="(data) =>{paddingTop = data}" />
    </div>
    <router-view
      :style="{ 'padding-top': paddingTop + 'px' }"
      class="h-full wfull-overflow-hidden"
    ></router-view>
    <AdvterDialog />
  </div>
</template>
<script>
// import LgLoading from '@/components/LgLoading'
import LayoutHeader from './components/LayoutHeader'
import AdvterDialog from './components/AdvterDialog'
export default {
  name: 'Layout',
  components: {
    // LgLoading,
    LayoutHeader,
    AdvterDialog
  },
  data () {
    return {
      paddingTop: 0
    }
  },
  computed: {
    v2 () {
      return this.$route.meta && this.$route.meta.v2
    }
  },
  created () {
  }
}
</script>
