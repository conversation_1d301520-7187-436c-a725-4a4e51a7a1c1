<template>
<div>
  <!-- 包含不再询问的确认弹框 -->
  <el-dialog :title="$t('loc.confirmation')"
             class="ask-again-confirm-dialog"
             :modal-append-to-body='false'
             :visible.sync="dialogVisible"
             width="30%">
    <span> {{ content }}</span>
    <span slot="footer"
          class="dialog-footer display-flex align-items justify-content-between">
        <el-checkbox v-model="askAgainCheck">{{ $t('loc.plan96') }}</el-checkbox>
        <span>
           <el-button @click="closeDialog">{{$t('loc.cancel')}}</el-button>
           <el-button type="primary" :loading="confirmLoading" @click="confirmDialog">{{$t('loc.confirm')}}</el-button>
        </span>
    </span>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'AskAgainConfirmDialog',
  data () {
    return {
      dialogVisible: false,
      askAgainCheck: false,
      confirmLoading: false
    }
  },
  props: {
    // 提示的内容
    content: {
      type: String,
      default: ''
    },
    // 不再询问的的请求参数内容
    hideGuideFeature: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    closeDialog () {
      this.dialogVisible = false
    },
    openDialog () {
      this.dialogVisible = true
    },
    confirmDialog () {
      if (this.askAgainCheck) {
        this.confirmLoading = true
        let result = { 'features': this.hideGuideFeature }
        this.$axios.post($api.urls().hideGuide, result).then(data => {
          this.confirmLoading = false
          this.$emit('confirm', 'hideGuideSuccess')
          this.dialogVisible = false
        }).catch(() => {
          this.confirmLoading = false
          this.$emit('confirm','')
          this.dialogVisible = false
        })
      } else {
        this.$emit('confirm', '')
        this.dialogVisible = false
      }
    }
  }
}
</script>

<style scoped>
.ask-again-confirm-dialog {
  .el-dialog__header {
    padding: 20px 24px 5px 24px;
  }
  .el-dialog__body {
    padding: 12px 24px;
  }
  .el-dialog__footer {
    padding: 10px 24px 20px;
  }
}
</style>