<template>
  <div>

    <CurriculumGenieHeader/>
    <!-- 面包屑 -->
    <lg-bread-crumb ref="breadCrumb" v-if="!($route.meta.hideBreadCrumb || $route.path.indexOf('/survey/') !== -1 || $route.path.indexOf('/courses/') !== -1 || $route.path.indexOf('/admin/') !== -1)
    || $route.path.indexOf('/mhs-setting') !== -1
    || $route.path.indexOf('/export-sftp') !== -1
    || $route.path.indexOf('/cl-setting') !== -1
    || $route.path.indexOf('/admin/import') !== -1
    || $route.fullPath.indexOf('/admin/import-error-handle') !== -1"
    v-show="!isLessonPlannHome"
    :style="showBanner ? 'margin-top: 112px;' : 'margin-top: 60px;'"
                    :levelList="levelList">
    </lg-bread-crumb>
    <ask-again-confirm-dialog ref="askAgainConfirmDialogRef"
                              v-if="(currentType === 'grantee' || currentType === 'special_education' || isSpecialTeacher) && agencies && agencies.length > 1"
                              :content="switchAgencyTip"
                              :hide-guide-feature="['HOME_SWITCH_AGENCY_CONFIRM_DIALOG']"
                              @confirm="confirmSwitchAgency">
    </ask-again-confirm-dialog>
  </div>
</template>

<script>
import tools from '@/utils/tools'
import { setLocale } from '@/utils/i18n'
import { localEnv } from '@/utils/setBaseUrl'
import { toSignOut } from '@/utils/autoSignOut'
import { mapState, mapActions } from 'vuex'
import CurriculumGenieHeader from '@/views/layout/v2/components/CurriculumGenieHeader.vue'
import {
  acrossRole,
  getCurrentUser,
  getSelectedAgency,
  getSelectedSite,
  getSelectedClass,
  setSelectedClass,
  setSelectedChild,
  getHomeToPortfolioKey
} from '@/utils/common'
// import videoJs from 'video.js'
import { bus } from '@/utils/bus'
import awsS3Service from '@/utils/awsS3Service'

import LgBreadCrumb from '@/components/LgBreadCrumb'
import AskAgainConfirmDialog from '@/views/layout/v2/components/AskAgainConfirmDialog'
import { LAST_NOTIFY_TIME } from '@/utils/const'
import { isEmbedded, isAuthenticated } from '@/utils/common'

export default {
  name: 'LayoutHeader',
  components: {
    AskAgainConfirmDialog,
    LgBreadCrumb,
    CurriculumGenieHeader
  },
  props: {
    headerHeight: {
      type: Function,
      default: () => { }
    }
  },
  data () {
    return {
      tempPage: {}, // 临时页面名称
      currentNav: '', // 当前选中导航
      isShow: false, // 小屏是否显示列表
      selectedLanguage: '', // 当前语言
      selectedAgency: {}, // 当前机构
      notificationLoading: false, // 通知loading
      releaseNotifacations: null, // 通知
      periodGroupNotify: false,
      noNotific: false, // 没有通知
      // unreadCount: false, // 是否有未读消息
      // eventNotify: false, // 是否有未读消息
      isNotSelfAgency: false, // 是否是自己的机构
      inkind: false, // 是否显示inkind
      inkindNewStatue: false, // inkind 是否显示new
      advtdialogVisible: false, //
      advertiseShow: false, // 控制广告弹框是否显示
      approvalQueueRedTip: false, // 控制approvalQueue红点显示
      parentChatOpen: false, //
      isSFTP: false,
      ARMNotifcationVisible: false,
      navUrl: { // 当前导航链接
        home: '',
        dashboard: '',
        analysis: '/#/analysis/measure/detail',
        lessons: '/#/lessons/public/list',
        messages: '/#/notification/email',
        events: '/#/notification/event',
        approval: '/#/approval',
        noteReview: '/#/notesReview/notesList//',
        myProfile: '/#/parent/edit-profile',
        adminSeting: '/#/import_center/select',
        manageChildren: '/#/manage_children',
        assessment: '/#/dashboard/assessment',
        engagement: '/#/dashboard/engagement',
        // inKind: '/#/inkindreview/in-kind-table/' + true,
        healthStats: '/#/healthstats/child',
        attendance: '/#/attendance-review/centerList',
        datahub: '/#/datahub/ChildProgressReport',
        dllCoach: '/#/dllCoach' // dll教练的路径
      },
      selectedClassName: '', // 选中的班级名称
      selectedClassId: '', // 选中的班级id
      classesInfo: [], // 班级列表
      activeSubmenu: '', // 选中的子菜单
      lessonNotifications: {},// 生成课程通知列表
      lessonGenerateNotificationEntities: [], // 生成课程通知实体列表
      fileNotifications: {}, // 生成文件通知列表
      fileGenerateNotificationEntities: [], // 生成文件通知实体列表
      currentNotification: {}, // 当前通知
      fileCurrentNotification: {}, // 当前通知
      switchAgencyTip: '', // 切换机构提示
      tempSelectedAgencyInfo: {}, // 临时选中机构信息
      adminSettingHref: '#', // admin 设置的链接
      profileObserver: null, // 对于 Profile 组件的监视器
      notify: new Map(), // 通知
      currentBatchIds: [], // 当前批次id
      notifyInterval: null, // 通知间隔
      currentFileNotification: '' // 当前文件通知
    }
  },
  async created () {
    // 接受重新设置页面名称
    this.$bus.$on('setPageName', page => {
      this.tempPage = page
    })
    if ('class_set_period_sftp'.includes(this.$route.name)) {
      this.isSFTP = true
    }
    if (!this.currentUser) {
      this.$store.dispatch('getUserInfoAction')
    }
    this.navUrl.home = `/#/centers/${this.currentUser.default_center_id}/groups`
    this.navUrl.myProfile = `/#/centers/${this.currentUser.default_center_id}/editProfile`

    if (this.currentUser && this.currentUser.role.toLowerCase() == 'parent') {
      this.navUrl.home = `/#/parent`
      this.parentChatOpen = this.currentUser.webChatOpen
      this.navUrl.myProfile = '/v2/#/edit-profile'
    }

    this.navUrl.dashboard = this.currentUser.assessmentProgressOpen ? '/#/dashboard/assessment' : '/#/dashboard/engagement'
    this.inkindHeaderOpen()
    this.setApprovalQueueRedTip()
    this.getNotification()
    if (tools.localItem('advertiseData')) {
      let advertiseData = tools.localItem('advertiseData')
      advertiseData = JSON.parse(advertiseData || '{}')
      if (advertiseData.showStatus) {
        this.advertiseShow = advertiseData.showStatus
      }
    }
    this.periodGroupNotify = JSON.parse(
      localStorage.getItem('periodGroupNotify')
    )
    // // 获取 In-Kind 设置
    // let data = await this.$axios.get($api.urls().getRatifySetting)
    // this.$store.dispatch('setRatifyModeAction', data.ratifiedMode)
    // this.$store.dispatch('setSiteAdminThirdPartyApprovalOpen', data.siteAdminThirdPartyApprovalOpen)
  },
  mounted () {
    this.init()
    let role = {
      assessmentOpen: this.assessmentOpen,
      engagementOpen: this.engagementOpen,
      analyReportOpen: this.analyReportOpen,
      lessonOpen: this.lessonOpen,
      messageOpen: this.messageOpen,
      dashboardMessageOpen: this.currentUser.message_open,
      approvalOpen: this.approvalOpen,
      noteReviewOpen: this.noteReviewOpen,
      webChatOpen: this.webChatOpen,
      eventOpen: this.eventOpen,
      adminOpen: this.adminOpen
    }
    this.$store.dispatch('setCurrentRoleAction', role)
    const that = this
    window.onresize = () => {
      that.headerHeight(this.height - 50)
    }
    bus.$on('activeHeaderTab', () => {
      this.currentNav = $router.currentRoute.path
    })
    this.$nextTick(() => {
      const targetNode = document.querySelector('#profileNavAccount-lg')
      if (targetNode) {
        // 创建监视器
        this.profileObserver = new MutationObserver((mutationsList) => {
          mutationsList.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
              // 如果当前是下拉框展示状态
              if (targetNode.classList.contains('open')) {
                this.$store.commit('SET_PROFILE_OPEN_DROPDOWN', true)
              } else {
                this.$store.commit('SET_PROFILE_OPEN_DROPDOWN', false)
              }
            }
          })
        })
        // 开启监视
        this.profileObserver.observe(targetNode, {
          attributes: true, // 开启属性监听
          attributeFilter: ['class'] // 监听的属性
        })
      }
    })
  },
  beforeDestroy () {
    // 在组件销毁前断开 MutationObserver
    if (this.profileObserver) {
      this.profileObserver.disconnect()
      this.profileObserver = null
    }
  },
  updated () {
    this.headerHeight(this.height - 50)
  },
  methods: {
    // 跳转到帮助页面
    toHelp () {
      const routeName = this.$route.name
      if (routeName === 'dll') {
        window.open('https://learninggenie.zendesk.com/hc/en-us/articles/17257972846100-DLL-Dual-Language-Learner-Module-Introduction', '_blank')
      } else {
        window.open('https://learninggenie.zendesk.com/hc/en-us/articles/17106108263956-Infant-Sleep-Check-Web-Portal-', '_blank')
      }
    },
    submenuMouseEnter (menuName) {
      this.activeSubmenu = menuName || ''
    },
    // 点击知识库下拉菜单
    knowledgeClick () {
      this.$analytics.sendEvent('web_knowledgebase_click_icon')
    },
    // 点击知识库链接
    knowledgeLibraryClick () {
      this.$analytics.sendEvent('web_knowledgebase_click_klb')
    },
    // 点击video 的链接
    knowledgeVideoGuide () {
      this.$analytics.sendEvent('web_knowledgebase_click_videoguide')
    },
    // 点击联系我们
    contactUs () {
      if (window.zE && window.zE.show) {
        window.zE.show()
        window.zE.activate()
      }
      this.$analytics.sendEvent('web_knowledgebase_click_help')
    },
    refresh () {
      this.$store.dispatch('addSignVersion') // 签到
      this.$store.dispatch('addHealthCardVersion') // 健康检查卡
      this.$store.dispatch('addHealthCheckCardVersion') // 视觉健康检查卡

      setTimeout(() => {
        this.refresh()
      }, 5 * 1000)
    },
    // 打开培训视频
    openCourse () {
      this.$router.push({
        name: 'videoList'
      })
    },
    ...mapActions([
      'getUnReadCountAction',
      'getEventNotifyAction',
      'getAgencyTipsAction',
      'setRefreshSignReview',
      'addSignVersion', // 签到
      'addHealthCardVersion', // 健康检查卡
      'addHealthCheckCardVersion', // 视觉健康检查卡
      'getSurveyNotifyAction'
    ]),
    myAcrossRole (...values) {
      return acrossRole(...values)
    },
    // 初始化
    init () {
      // 初始化lang
      let language = tools.localItem('NG_TRANSLATE_LANG_KEY') // 语言
      let currentUser = this.currentUser // 当前用户
      let currentType = this.currentType // 当前用户类型
      this.selectedLanguage = language
      // 存储selectedAgency,data,初始化
      if (currentType == 'grantee' || currentType == 'special_education' || this.isSpecialTeacher) {
        let defaultAgency = this.getDefaultAgency(
          currentUser.default_agency_id
        )
        this.selectedAgency = this.granteeSelectedAgency ? this.granteeSelectedAgency : defaultAgency
        // 判断当前机构是否是自己的机构
        if (this.selectedAgency.self) {
          this.isNotSelfAgency = false
        } else {
          this.isNotSelfAgency = true
        }
        this.selectAgency(this.selectedAgency)
      }
      // 获取当前通知是否存在未读消息
      this.getUnReadCountAction()
      // 获取event通知
      // if (this.currentUser.eventOpen) {
      //   this.getEventNotifyAction()
      // }
      this.getAgencyTipsAction()
      this.getEventNotifyAction().then(res => {
        const data = this.open
        // 初始化s3Service
        if (data.awsIdentityPoolConfig) {
          awsS3Service.init(data.awsIdentityPoolConfig.bucketRegion, data.awsIdentityPoolConfig.bucketName, data.awsIdentityPoolConfig.identityPoolId)
        }
        // 获取功能引导
        this.$store.dispatch('getUserNeedGuideFeaturesAction')
      })
      // 获取是否有待填写问卷红点
      this.getSurveyNotifyAction()
      // 改变导航选中状态
      this.currentNav = $router.currentRoute.path
    },
    // 切换语言
    changeLanguage (lang) {
      this.$store
        .dispatch('setLanguageAction', lang)
        .then((res) => {
          setLocale(lang)
          this.selectedLanguage = lang
          this.submenuMouseEnter('')
          this.$router.go(0)
        })
        .catch((err) => {
          this.$message.error(err)
        })
    },
    // 切换<750时的下拉框
    targetCollapse () {
      this.isShow = !this.isShow
    },
    // getHealthCardPoint () {
    //     this.healthOpen = this.open.agencyOpenHealthCheck
    //     if (this.healthOpen) {
    //       this.healthViewOpen = this.open.healthStatistics && this.open.haveGroup
    //       this.healthCardPoint = this.open.notifyHealthCheckFunction
    //     }
    // },
    // grantee切换机构
    selectAgency (agency, reset, type) {
      if (!this.currentUser) {
        return ''
      }
      // let currentUser = this.currentUser
      if (
        !agency ||
        (this.selectedAgency && this.selectedAgency.id == agency.id)
      ) {
        return
      }
      if (type) {
        agency.type = type
      }
      if (agency.self && (this.currentUser.type.toLowerCase() == 'grantee' || this.isSpecialTeacher)) {
        this.isNotSelfAgency = false
      } else {
        this.isNotSelfAgency = true
      }
      this.selectedAgency = agency
      // 切换机构设置标识
      sessionStorage.setItem('SWITCH_AGENCY', JSON.stringify(true))
      sessionStorage.setItem('selectedAgency', JSON.stringify(agency))
      if (reset) {
        window.location.href = '/#/centers/' + agency.id + '/groups'
        // if (this.$router.fullPath == '/home/<USER>') {
        //   this.$router.go(0)
        // } else {
        //   this.$router.push({ path: '/home/<USER>' })
        // }
      }
    },
    // 点击切换机构
    clickSelectAgency (agency, reset, type) {
      if (this.switchAgencyNeedConfirm) {
         this.tempSelectedAgencyInfo = {
           'agency': agency,
           'reset': reset,
           'type': type
         }
        this.switchAgencyTip = this.$t('loc.switchAgencyTip',{ originalAgencyName: this.granteeSelectedAgency.name, replacedAgencyName: agency.name })
        this.$refs.askAgainConfirmDialogRef.openDialog()
      } else {
        if (type) {
          this.selectAgency(agency, reset, type)
        } else {
          this.selectAgency(agency, reset)
        }
      }
    },
    // 获取默认机构
    getDefaultAgency (agencyId) {
      if (!this.currentUser) {
        return ''
      }
      let defaultAgency
      let currentUser = this.currentUser
      if (agencyId) {
        if (currentUser.default_agency_id) {
          for (let ii = 0; ii < this.agencies.length; ii++) {
            let el = this.agencies[ii]
            if (el.id == agencyId) {
              defaultAgency = el
              return defaultAgency
            }
          }
        }
        if (!defaultAgency) {
          defaultAgency = this.agencies[0]
        }
        return defaultAgency
      }
      return defaultAgency
    },
    // 退出
    signOut () {
      // 弹出框提示
      this.$confirm(this.$t('loc.areYouSignOut'), this.$t('loc.confirmation'), {
        confirmButtonText: this.$t('loc.confirm'),
        cancelButtonText: this.$t('loc.cancel'),
        // type: 'warning',
        customClass: 'lg-modal-warning'
      })
        .then(() => {
          // 触发 lgAppSignOut 传递退出事件
          bus.$emit('lgAppSignOut')
          // success
          toSignOut()
        })
        .catch((error) => {
          return error
        })
    },
    // 获取通知
    getNotifications () {
      // 获取store所有信息
      this.releaseNotifacations = this.$store.getters.notifications
      // 开启loading
      this.notificationLoading = true
      // 请求接口
      this.$axios
        .get($api.urls().releasenote, { params: { releaseNoteNum: '' } })
        .then((data) => {
          if (data.length <= 0) {
            this.noNotific = true
          }
          // 多语言日期本地化
          data.forEach((notification) => {
            let monthDay = this.$d(
              new Date(notification.strReceiveAtUtc),
              'monthDay'
            )
            notification.strReceiveAtUtc = monthDay
          })
          // 更新用户信息
          this.$store.dispatch('setNotificationsAction', data)
          if (!this.currentUser.releaseReadingState) {
            this.currentUser.releaseReadingState = true
            this.$store.dispatch('setCurrentUserAction', this.currentUser)
          }
          this.releaseNotifacations = data
          // 关闭loading
          this.notificationLoading = false
        })
        .catch((error) => {
          this.notificationLoading = false
        })
      // 进入到通知列表
      this.$analytics.sendEvent('web_notifications_exposure_enter')
    },
    openWebChat () {
      if (!this.currentUser.commInfo) {
        this.$message.error(this.$t('loc.noChatGroup'))
        return
      }
      localStorage.setItem('isContChat', false)
      let env = process.env.VUE_APP_CURRENTMODE
      let url = '/v2/#/webChat'
      if (env === 'local') {
        url = '/#/webChat'
      }
      // 点击 Chat 图标添加埋点
      this.$analytics.sendEvent('web_home_click_chat_icon')
      window.open(url, 'chat')
    },
    inkindHeaderOpen () {
      // let date = new Date()
      // let today =
      //   date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
      // // 登录后调取inkind红点接口，判断是否显示入口
      // $axios
      //   .get($api.urls().point + '?isNew=true' + '&date=' + today)
      //   .then((data) => {
      //     localStorage.setItem('inkindStatus', JSON.stringify(data))
      //     localStorage.setItem(
      //       'inkindUserStatus',
      //       JSON.stringify(data.inkindUserStatus)
      //     )
      //     this.inkind = data.inkindDisplayStatus
      //     this.inkindNewStatue = data.inkindUserStatus.newStatus
      //     this.$store.dispatch('setInkindNewStatue', this.inkindNewStatue)
      //   })
    },
    // 广告弹框
    openAdvert () {
      // $('.showAdvter').css('display', 'block')
      // this.$store.dispatch('setShowAdvterAction', true)
      // this.$store.dispatch('setShowIconAdvterAction', false)
      // this.$store.dispatch('setShowIconAdvterAction0', false)
      // setTimeout(function () {
      //   let video0 = videoJs('my-video')
      //   let video = document.getElementById('my-video')
      //   if (video) {
      //     let myVideo = document.getElementById('my-video_html5_api')
      //     myVideo.load()
      //     video0.currentTime(0)
      //     $('.vjs-play-progress').css('width', '0')
      //   }
      // }, 0)
    },
    setApprovalQueueRedTip () {
      if (
        acrossRole('site_admin', 'agency_admin', 'agency_owner') &&
        this.currentUser.approval_open &&
        this.currentUser.reportOpen
      ) {
        this.$axios({
          url: $api.urls().approvalReadTip,
          method: 'get'
        })
          .then((res) => {
            this.approvalQueueRedTip = res.approvalNotify
          })
          .catch((error) => {
            this.approvalQueueRedTip = false
            this.$message.error(error.response.data.error_message)
          })
      } else {
        this.approvalQueueRedTip = false
      }
    },
  // 轮询接口，维持登录
  getNotification() {
    // 每 30 秒触发一次
    this.notifyInterval = window.setInterval(() => {
      setTimeout(this.getNotificationRequest, 0)
    }, 30000)
  },

    // 请求轮询接口
    getNotificationRequest () {
      if (!isAuthenticated()) {
        clearInterval(this.notifyInterval)
        return
      }
      // 请求参数
      let currentUser = this.currentUser
      // 如果当前用户不存在，则重新获取
      if (!currentUser) {
        currentUser = getCurrentUser()
      }
      // 如果当前用户不存在，则直接返回
      if (!currentUser) {
        return
      }
      let params = {
            userId: currentUser.user_id
        }
        // 请求接口
        this.$axios({
            url: $api.urls().notification,
            params: params,
            method: 'get'
        }).then((res) => {
            this.FileNotification(res)
            tools.localItem(LAST_NOTIFY_TIME, JSON.stringify(new Date().getTime()))
        }).catch((error) => {
            // 退出登录，停止轮询
            if (this.notifyInterval && error && error.response && error.response.status === 401) {
                clearInterval(this.notifyInterval)
            }
        })
    },
    
    // 调取轮询接口
    // getNotification () {
    //   let cunrrentUser = getCurrentUser()
    //   let fromH5 = false
    //   if (cunrrentUser && cunrrentUser.source && cunrrentUser.source === 'H5') {
    //     fromH5 = true
    //   }
    //   if (!fromH5) {
    //     window.setInterval(() => {
    //       setTimeout(this.pollingInterface, 0)
    //     }, 30000)
    //   }
    // },

    pollingInterface () {
      let params = {
        type: this.executeType,
        userId: this.currentUser.user_id,
        keepLogin: !!this.currentUser.keepLogin
      }
      this.$axios({
        url: $api.urls().notification,
        params: params,
        method: 'get'
      })
        .then((res) => {
          switch (params.type) {
            case 'ATTENDANCE':
              this.setRefreshSignReview(res.refreshSignReview)
              break
          }
          // 在 Angular 项目中，/notification 请求成功会记录时间，页面刷新时如果超过十分钟就退出登录，
          tools.localItem(LAST_NOTIFY_TIME, JSON.stringify(new Date().getTime()))
          this.lessonNotification(res)
          this.FileNotification(res)
        })
        .catch((error) => {
          this.$message.error(error.response.data.error_message)
        })
    },
    sleep (ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },
    // 课程通知方法
    lessonNotification (res) {
      // 判断 lessonGenerateNotificationEntities 是否为空 为空则直接返回
      if (!res.lessonGenerateNotificationEntities) {
        return
      }
      // 判断 lessonGenerateNotificationEntities 是否为空 为空则直接返回
      if (res.lessonGenerateNotificationEntities.length === 0) {
        return
      }
      let notificationIds = []
      // 获取路由，判断是否是在生成课程页面，若是则不弹出通知，直接返回
      if (this.$route.name === 'lessonDetail' || this.$route.name === 'lesson-detail-cg') {
        // 关闭通知
        this.$notify.closeAll()
        // 若是在生成课程页面，需要修改通知消息的状态,获取所有的通知 ID
        res.lessonGenerateNotificationEntities.forEach((item) => {
          notificationIds.push(item.id)
        })
        let params = {
          notificationIds: notificationIds
        }
        this.$axios.post($api.urls().setLessonNotificationStatus, params).then((res) => {
        })
        return
      }
      if (res.lessonGenerateNotificationEntities.length === 1) {
        // 若是只有一个通知，则判断是否和现在展示的通知一样，一样则不弹出通知，直接返回
        if (this.lessonNotifications.id === res.lessonGenerateNotificationEntities[0].id) {
          return
        } else {
          // 关闭通知
          this.$notify.closeAll()
        }
      } else {
        // 关闭通知
        this.$notify.closeAll()
      }
      if (res.lessonGenerateNotificationEntities.length > 1) {
        // 关闭通知
        this.$notify.closeAll()
      }
      this.lessonGenerateNotificationEntities = res.lessonGenerateNotificationEntities
      // 发送通知
      this.loopWithDelay()
    },
    // 发送通知
    async loopWithDelay () {
      // 遍历 lessonGenerateNotificationEntities 生成通知
      for (let i = 0; i < this.lessonGenerateNotificationEntities.length; i++) {
        // 等待 2 秒
        await this.sleep(2000)
        let notificationIds = []
        let notification = this.lessonGenerateNotificationEntities[i]
        let content = ''
        // 判断类型
        if (notification.type.toLowerCase() === 'Lesson'.toLowerCase()) {
          // 判断是否有多个课程
          if (notification.successCount > 1) {
            content = this.$t('loc.MultipleLessonNotification', { count: notification.successCount })
          } else {
            content = this.$t('loc.SingeLessonNotification', { count: notification.successCount })
          }
        } else {
          content = this.$t('loc.unitNotification')
        }
        notificationIds.push(notification.id)
        let params = {
          notificationIds: notificationIds
        }
        // 判断是否是最后一个
        if (i !== this.lessonGenerateNotificationEntities.length - 1) {
          // 弹出通知埋点
          this.$analytics.sendEvent('web_unit_det_generated_pop')
          const h = this.$createElement
          this.$notify.success({
            dangerouslyUseHTMLString: true,
            message:
              h('div', { style: { backgroundColor: '#F0F9EB' } }, [
                h('p', null, [
                  h('span', null, content),
                  h('el-button', {
                    props: {
                      type: 'primary',
                      size: 'mini'
                    },
                    style: {
                      marginLeft: '5px' // 为按钮添加左侧间距
                    },
                    on: { click: () => this.clickBtn(notification) }
                  }, 'View')
                ])
              ]),
            customClass: 'notification_right',
            onClose: () => {
              this.$axios.post($api.urls().setLessonNotificationStatus, params).then((res) => {
              })
            }
          })
        } else {
          // 保存一下最后一个通知的信息
          this.lessonNotifications = notification
          const h = this.$createElement
          this.currentNotification = this.$notify.success({
            dangerouslyUseHTMLString: true,
            message:
              h('div', { style: { backgroundColor: '#F0F9EB' } }, [
                h('p', null, [
                  h('span', null, content),
                  h('el-button', {
                    props: {
                      type: 'primary',
                      size: 'mini'
                    },
                    style: {
                      marginLeft: '5px' // 为按钮添加左侧间距
                    },
                    on: { click: () => this.clickBtn(notification) }
                  }, 'View')
                ])
              ]),
            customClass: 'notification_right',
            duration: 0,
            onClose: () => {
              // 关闭通知埋点
              this.$analytics.sendEvent('web_unit_det_generated_pop_close')
              this.$axios.post($api.urls().setLessonNotificationStatus, params).then((res) => {
                // 通知关闭后，将通知信息清空
                this.lessonNotifications = {}
              })
            }
          })
        }
      }
    },
    // 文件通知方法
    FileNotification (res) {
      // 判断 fileGenerateNotificationEntities 是否为空 为空则直接返回
      if (!res.fileGenerateNotificationEntities) {
        return
      }
      // 判断 fileGenerateNotificationEntities 是否为空 为空则直接返回
      if (res.fileGenerateNotificationEntities.length === 0) {
        return
      }
      this.fileGenerateNotificationEntities = res.fileGenerateNotificationEntities
      // 发送通知
      this.flieLoopWithDelay()
    },

    // 发送通知
    async flieLoopWithDelay () {
      // 遍历 fileGenerateNotificationEntities 生成通知
      for (let i = 0; i < this.fileGenerateNotificationEntities.length; i++) {
        // 等待 1 秒
        await this.sleep(1000)
        let notification = this.fileGenerateNotificationEntities[i]
        this.currentFileNotification = localStorage.getItem('currentFileNotification')
        // 若是当前通知已经弹出，则不再弹出
        if (this.currentFileNotification) {
          let batchId = notification.batchId
          if (batchId === this.currentFileNotification) {
            continue
          }
        }
        let type = notification.type
        let content = ''
        var download = $i18n.t('loc.download')
        if (type && type === 'GOOGLE') {
          content = $i18n.t('loc.downloadSuccessfullyGoogle', { unitName: notification.unitName })
          download = $i18n.t('loc.downloadViewGoogleDrive')
        } else {
          content = $i18n.t('loc.downloadViewWord', { unitName: notification.unitName })
        }

        const h = this.$createElement
        this.notify.set(notification.batchId, this.$notify({
          dangerouslyUseHTMLString: true,
          message: h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              backgroundColor: '#F0F9EB',
              borderRadius: '4px',
              width: '600px',
              overflow: 'hidden',
            },
          }, [
            // 自定义成功图标
            h('div', {
              class: ['flex-align-center', 'add-margin-r-12']
            }, [
              h('i', {
                class: ['lg-icon', 'lg-icon-approve', 'lg-color-success'],
                style: { fontSize: '32px' }
              })
            ]),
            // 文字内容
            h('div', {
              style: {
                color: '#111C1C',
                fontWeight: '600',
                width: '400px',
                wordBreak: 'normal',
                overflowWrap: 'break-word',
                textAlign: 'left'
              }
            }, content),
            // 按钮
            h('div', {
              style: { marginLeft: '12px' }
            }, [
              h('el-button', {
                props: {
                  type: 'primary',
                  size: 'mini'
                },
                on: {
                  click: () => this.clickFileBtn(notification.downloadUrl, type, notification.batchId)
                }
              }, download)
            ])
          ]),
          customClass: 'notification_top',
          duration: 0, // 通知常显
          offset: 60, // 通知距离顶部的初始偏移量
          onClose: () => {
          }
        }))
      }
    },
    clickFileBtn (url, type, batchId) {
      // 打开下载链接
      if (type && type === 'GOOGLE') {
        window.open(url)
      } else {
        window.location.href = url
      }
      // 关闭通知
      if (this.notify) {
       if (this.notify.has(batchId)) {
          this.notify.get(batchId).close()
          this.notify.delete(batchId)
       }
      }
    },
    // 点击通知按钮，回调处理
    clickBtn (notification) {
      // 点击查看按钮埋点
      this.$analytics.sendEvent('web_unit_det_generated_pop_view')
      // 清除 weekPlan
      this.$store.commit('curriculum/RESET_UNIT')
      // 拼接地址，跳转到课程生成页面
      // window.location.href = '/v2/#/lessons/unit-planner/unit-editor/' + notification.unitId + '/lesson-detail/' + notification.planItemId
      // 跳转到单元详情页面
      this.$router.push({
          name: 'unit-detail-cg',
          query: {
              unitId: notification.unitId,
          }
      })
      // 关闭通知
      this.$notify.closeAll()
    },
    // 录制周计划中时，阻止用户离开页面
    handlePlanRecording (e) {
      e = e || window.event
      e.preventDefault() ? e.preventDefault() : (e.returnValue = false)
      e.stopPropagation() ? e.stopPropagation() : (e.cancelBubble = true)
      this.$message.warning(this.$t('loc.plan145'))
    },
    getClassesByCenterId () {
      this.$axios.get($api.urls(undefined, getSelectedSite()).getCenterById).then((data) => {
        this.classesInfo = []
        if (data && data.groups && data.groups.length > 0) {
          if (!getSelectedClass()) {
            // 如果本地没有groupId,设置有小孩的第一个班级
            let tempGroups = data.groups.filter(group => group.enrollments && group.enrollments.length > 0)
            setSelectedClass(tempGroups && tempGroups.length > 0 ? tempGroups[0].id : '')
            setSelectedChild(tempGroups && tempGroups.length > 0 ? tempGroups[0].enrollments[0].id : '')
          }
          data.groups.forEach(group => {
            this.classesInfo.push({
              name: group.name,
              id: group.id,
              isInactive: group.isInactive,
              isNoChild: !group.enrollments || group.enrollments.length === 0,
              isUnderThree: this.checkGroupAge(group),
              enrollments: group.enrollments
            })
            if (group.id === getSelectedClass()) {
              this.selectedClassName = group.name
            }
          })
        }
      })
    },
    handleClassCommand (command) {
      if (command.isNoChild) {
        this.$message.info('class no child')
      } else if (command.isInactive) {
        this.$message.info('class is inactive')
      } else {
        // 设置选择的班级
        this.selectedClassName = command.name
        setSelectedClass(command.id)
        // 设置班级第一个位置的学生
        let tempChildId = command.enrollments[0].id
        setSelectedChild(tempChildId)
        this.$bus.$emit('switchClass',{ classId: command.id,childId: tempChildId })
      }
    },
    checkGroupAge (res) {
      if (res && res.stage_id && res.stage_id.length > 0) {
        if (res.stage_id.toLowerCase() === '72516154-3b50-e411-837d-02dbfc8648ce' ||
          res.stage_id.toLowerCase() === '73516154-3b50-e411-837d-02dbfc8648ce' ||
          res.stage_id.toLowerCase() === '74516154-3b50-e411-837d-02dbfc8648ce' ||
          res.stage_id.toLowerCase() === '81516154-3b50-e411-837d-02dbfc8648ce'
        ) {
          return true
        } else {
          return false
        }
      }
      return false
    },
    // 确认切换机构
    confirmSwitchAgency (status) {
      if (status) {
       this.switchAgencyNeedConfirm = false
      }
      if (this.tempSelectedAgencyInfo && this.tempSelectedAgencyInfo.agency) {
        if (this.tempSelectedAgencyInfo.type) {
          this.selectAgency(this.tempSelectedAgencyInfo.agency, this.tempSelectedAgencyInfo.reset, this.tempSelectedAgencyInfo.type)
        } else {
          this.selectAgency(this.tempSelectedAgencyInfo.agency, this.tempSelectedAgencyInfo.reset)
        }
      }
    },
    // 点击 Admin Settings 跳转到设置页面
    navClickAdminSettings (event) {
      // 阻止默认行为以防止导航
      event.preventDefault()
      if (this.isNotSelfAgency) {
        this.$message.warning(this.$t('loc.onlyMainAgencyCanSetting'))
      } else {
        this.adminSettingHref = this.importSyncOpen ? this.navUrl.adminSeting : this.navUrl.manageChildren
        // 模拟点击导航
        window.location.href = this.adminSettingHref
      }
    },
    isEmbedded,
  },
  watch: {
    // 监听路由变化，清除页面名称缓存
    $route: {
      immediate: false,
      handler () {
        if (Object.keys(this.tempPage).length > 0 && this.tempPage.routeName !== this.$route.name) {
          this.tempPage = {}
        }
        // 调用子组件方法获取存储到本地的周计划功能引导状态值
        this.$refs.breadCrumb && this.$refs.breadCrumb.isNeedWeeklyPlanGuide()
        // 将进入睡眠检查和 DLL 的方式更新到 vuex 中进行存储
        this.$store.commit('SET_SKIP_TYPE', sessionStorage.getItem(getHomeToPortfolioKey()))
        // 判断路由，若是在生成课程页面，则关闭通知
        if (this.$route.name === 'lessonDetail') {
          this.$notify.closeAll()
        }
        // 判断路由，若是 Home 页进入的 DLL 页，则需要将 skipType 更新到 vuex 中进行存储
        if (this.$route.name === 'dllTeacherList') {
          this.$store.commit('SET_SKIP_TYPE', 'HOME')
        }
      }
    },
    noClassReloadingTitle () {
      this.init()
    },
    // 监听录制周计划状态
    planRecording (val) {
      let header = document.getElementById('guide-app-header-navbar')
      if (val) {
        header.addEventListener('click', this.handlePlanRecording, true)
      } else {
        header.removeEventListener('click', this.handlePlanRecording, true)
      }
    },
    showClassSwitch: {
      handler (newVal) {
        // 请求学校的班级信息
        if (newVal) {
          this.getClassesByCenterId()
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      currentUser: state => getCurrentUser(), // 当前用户
      granteeSelectedAgency: state => getSelectedAgency(), // grantee 当前选中的机构
      userInfo: state => state.user.currentUser.userInfo, // 当前用户简要信息
      webChatCount: state => state.chat.webChatCount, // 是否存在未读信息（环信）
      messages: state => state.chat.messages, // 未读提示信息
      messagesHasMsg: state => state.chat.messagesHasMsg, // 是否显示该未读提示信息
      unreadCount: state => state.common.unreadCount, // 铃铛的未读提示
      eventNotify: state => state.common.eventNotify, // event未读提示
      inKindNotify: state => state.common.inkindNotify,
      surveyNotify: state => state.common.surveyNotify, // survey红点
      isShowIconAdavter0: state => state.common.isShowIconAdavter0,
      open: state => state.common.open,
      stateReportOpen: state => state.common.open && state.common.open.stateReportOpen, // 州报告开关
      executeType: state => state.common.executeType,
      noClassReloadingTitle: state => state.common.noClassReloadingTitle, // 健康卡设置班级为空时刷新标题头 防止未及时刷新可以误入出席功能
      planRecording: state => state.lesson.planRecording, // 是否录制周计划中
      skipType: state => state.common.skipType, // 获取是从首页点击班级卡片进入睡眠检查和 DLL 还是从侧边栏进入睡眠检查和 DLL 的访问跳转类型
      guideFeatures: state => state.common.guideFeatures, // 功能引导
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      showBanner: state => state.cgAuth.showBanner // 是否显示 Curriculum Genie 横幅
    }),
    // 是否是 Lesson Planner 首页
    isLessonPlannHome() {
      return this.$route.fullPath.indexOf('/lessons/lesson-library/my-lessons') > -1
    },
    userDisplayName () {
      let displayName = this.currentUser.display_name
      if (!displayName) {
        return ''
      }
      let output
      let personTypes = [
        { typeName: 'Miss.' },
        { typeName: 'Miss' },
        { typeName: 'Ms.' },
        { typeName: 'Mr.' },
        { typeName: 'Mrs.' }
      ]
      personTypes.forEach((item) => {
        let key = item.typeName
        if (displayName.indexOf(key) !== -1) {
          output = displayName.substr(key.length)
        }
      })
      return output || displayName
    },
    // eslint-disable-next-line vue/return-in-computed-property
    knowledgeBaseUrl () {
      // 根据不同的角色跳转到不同的链接
      let roleKnowledgeUrl = {
        'admin': 'https://learninggenie.zendesk.com/hc/en-us/categories/200229834-For-Admins-Directors',
        'parent': 'https://learninggenie.zendesk.com/hc/en-us/categories/200256284-For-Parents',
        'teacher': 'https://learninggenie.zendesk.com/hc/en-us/categories/200254244-For-Teachers'
      }
      if (!this.currentUser) {
        return undefined
      }
      // 角色
      let role = this.currentUser.role2
      if (!role) {
        return undefined
      }
      // 如果是有角色的，那么对角色进行判断，admin跳转到admin知识库，老师跳转到老师的知识库
      role = role.toLowerCase()
      switch (role) {
        case 'collaborator':
          return roleKnowledgeUrl.teacher
        case 'site_admin':
          return roleKnowledgeUrl.admin
        case 'agency_admin':
          return roleKnowledgeUrl.admin
        case 'agency_owner':
          return roleKnowledgeUrl.admin
        case 'parent':
          return roleKnowledgeUrl.parent
        case 'family_service':
          return roleKnowledgeUrl.teacher
        case 'teaching_assistant':
          return roleKnowledgeUrl.teacher
      }
    },
    // 健康卡菜单是否显示
    healthStatisticsOpen () {
      if (!this.open) {
        return false
      }
      return this.open.agencyOpenHealthCheck && this.open.healthStatistics && this.open.haveGroup && this.open.existDHCForm
    },
    // 签到菜单是否显示
    healthOpen () {
      if (!this.open) {
        return false
      }
      return this.open.agencyOpenCheck && this.open.haveGroup
    },
    // 签到签退功能是否开启
    signInOutOpen () {
      if (!this.open) {
        return false
      }
      return this.open.manualReview && this.open.openCheck
    },
    importSyncOpen () {
      if (!this.open || !acrossRole('site_admin')) {
        return true
      }
      return this.open.importSyncOpen
    },
    // 当前用户角色
    userRole () {
      if (!this.currentUser) {
        return undefined
      }
      // 角色
      let role = this.currentUser.role2
      if (!role) {
        return undefined
      }
      // 类型
      let type = this.currentUser.type
      if (type && type.toLowerCase() === 'grantee') {
        return this.$t('loc.gteAdm')
      }
      if (type && type.toLowerCase() === 'special_education') {
        return this.$t('loc.speList')
      }
      if (role.toLowerCase() === 'parent') {
        return undefined
      }
      // 转换角色名称
      return tools.formatRole(role)
    },
    height () {
      this.headerHeight()
      return this.$refs.getHeader ? this.$refs.getHeader.offsetHeight : 50
    },
    isChina () {
      return this.$store.getters.isChina
    },
    currentType () {
      if (!this.currentUser) {
        return ''
      }
      let type = this.currentUser.type
      return type.toLowerCase()
    },
    agencies () {
      if (!this.currentUser) {
        return []
      }
      let agencies = this.currentUser.agencies
      return agencies
    },
    notDefaultCenterId () {
      let agency = sessionStorage.getItem('selectedAgency')
      agency = JSON.parse(agency)
      if (agency && !agency.permission) {
        return true
      } else if (agency && agency.permission) {
        return false
      }
      if (!this.currentUser.default_center_id) {
        if (
          this.currentUser.role2.toLowerCase() != 'parent' &&
          this.currentUser.type.toLowerCase() != 'grantee'
        ) {
          return true
        }
      }
    },
    currentRole () {
      if (!this.currentUser) {
        return ''
      }
      let role = this.currentUser.role
      return role.toLowerCase()
    },
    // 旧版 dashboard 显示
    dashboardOpen () {
      if (
        acrossRole(
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        (this.currentUser.assessmentProgressOpen ||
          this.currentUser.dashboard_engagement_v1_open)
      ) {
        return true
      } else {
        return false
      }
    },
    // 新版 dashboard 显示
    assessmentOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.assessmentProgressOpen
      ) {
        return true
      } else {
        return false
      }
    },
    engagementOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.dashboard_engagement_v1_open
      ) {
        return true
      } else {
        return false
      }
    },
    analyReportOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.paid &&
        this.currentUser.tempDrdp
      ) {
        return true
      } else {
        return false
      }
    },
    lessonOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.lessonOpen
      ) {
        return true
      } else {
        return false
      }
    },
    messageOpen () {
      if (
        acrossRole('collaborator', 'family_service', 'site_admin', 'agency_admin', 'agency_owner') &&
        !this.isNotSelfAgency &&
        this.currentUser.message_open
      ) {
        return true
      } else {
        return false
      }
    },
    approvalOpen () {
      if (
        acrossRole('site_admin', 'agency_admin', 'agency_owner') &&
        this.currentUser.approval_open &&
        this.currentUser.reportOpen
      ) {
        return true
      } else {
        return false
      }
    },
    noteReviewOpen () {
      if (
        acrossRole('site_admin', 'agency_admin', 'agency_owner') &&
        this.currentUser.note_review_open
      ) {
        return true
      } else {
        return false
      }
    },
    webChatOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        this.currentUser.webChatOpen &&
        !this.isChina &&
        !this.currentUser.academy_open
      ) {
        return true
      } else {
        return false
      }
    },
    eventOpen () {
      if (
        acrossRole(
          'family_service',
          'teaching_assistant',
          'collaborator',
          'site_admin',
          'agency_admin',
          'agency_owner'
        ) &&
        !this.isChina &&
        this.currentUser.eventOpen
      ) {
        return true
      } else {
        return false
      }
    },
    adminOpen () {
      if (
        acrossRole('agency_owner', 'agency_admin', 'site_admin') &&
        !this.notDefaultCenterId
      ) {
        return true
      } else {
        return false
      }
    },
    // 以为 消息通知老师也应该有，所以这里应该为老师增加上权限
    teacherOpen () {
      if (
        acrossRole('agency_owner', 'agency_admin', 'site_admin', 'collaborator', 'family_service', 'teaching_assistant') &&
        !this.notDefaultCenterId
      ) {
        return true
      } else {
        return false
      }
    },
    healthViewOpen () {
      if (!this.open) {
        return false
      }
      return (
        this.open.agencyOpenHealthCheck &&
        this.open.healthStatistics &&
        this.open.haveGroup
      )
    },
    lesson2Open () {
      return acrossRole(
        'teaching_assistant',
        'collaborator',
        'site_admin',
        'agency_admin',
        'agency_owner'
      ) &&
        this.open && this.open.lesson2Open
    },
    // 周计划开关
    planOpen () {
      return acrossRole(
        'teaching_assistant',
        'collaborator',
        'site_admin',
        'agency_admin',
        'agency_owner'
      ) &&
        this.open && this.open.planOpen
    },
    // 系列课程开关
    curriculumOpen () {
      return acrossRole(
        'teaching_assistant',
        'collaborator',
        'site_admin',
        'agency_admin',
        'agency_owner'
      ) &&
        this.open && this.open.curriculumOpen
    },
    healthCardPoint () {
      if (!this.open) {
        return false
      }
      return (
        this.open.agencyOpenHealthCheck && this.open.notifyHealthCheckFunction
      )
    },
    healthCheckOpen () {
      if (!this.open) {
        return false
      }
      return this.open.agencyOpenCheck && this.open.haveGroup
    },
    surveyOpen () {
      return this.currentUser.surveyOpen
    },
    // 判断dll教练是否开启
    dllCoachOpen () {
      return this.open && this.open.dllopen
    },
    // 培训课程开关
    courseOpen () {
      return !acrossRole('parent') && this.open && this.open.trainingCertificateOpen
    },
    // 培训课程限时免费标签
    trainingModuleFreeTag () {
      return this.open && this.open.trainingModuleFreeTag
    },
    // 课程跳转路由
    lesson2RouteName () {
      if (this.planOpen && this.curriculumOpen) {
        return 'lessonCurriculumView'
      }
      return 'TeacherLessonList'
    },
    // 页面名称
    pageName () {
      if (Object.keys(this.tempPage).length > 0) {
        return this.tempPage.name
      }
      if (this.$route.fullPath.indexOf('detail?type=book') > -1) {
        return this.$t('loc.boo')
      } else if (this.$route.fullPath.indexOf('detail?type=learningMedia') > -1) {
        return this.$t('loc.learnMedia')
      }
      return this.$route.meta.pageName || ''
    },
    // 面包屑
    levelList () {
      let levelList = []
      this.$route.matched.filter(item => item.meta.breadcrumb).forEach(item => {
        levelList.push({
          name: item.name,
          meta: item.meta,
          path: item.path
        })
      })
      levelList = JSON.parse(JSON.stringify(levelList))
      // 如果有重新设置的页面名称，就替换掉
      if (Object.keys(this.tempPage).length > 0) {
        levelList.forEach(item => {
          if (item.name === this.tempPage.routeName) {
            item.meta.breadcrumb = this.tempPage.name
            // 如果重新设置了样式，则替换成新的样式
            if (this.tempPage.style) {
              item.meta.style = this.tempPage.style
            }
          }
        })
      }
      // 如果签到签退和健康检查的功能全部开启则重新赋值面包屑
      if ((this.$route.fullPath.indexOf('attendance-review/') !== -1 || this.$route.fullPath.indexOf('healthstats/child') !== -1) && this.healthOpen && this.signInOutOpen && this.healthStatisticsOpen) {
        levelList[0].meta.breadcrumb = this.$t('loc.attendanceDHC')
      }
      if (this.$route.fullPath.indexOf('healthstats/child') !== -1 && !this.healthOpen && !this.signInOutOpen && this.healthStatisticsOpen) {
        return []
      }
      // 如果当前页面是班级签到详情页面，则则面包屑中添加一个二级面包屑，如果有更好的方法可以干掉这块代码
      let groupDetail = levelList.find(item => item.name === 'attendance-review-groupDetail' ||
        item.name === 'supplement' ||
        item.name === 'batchFillInVHC' ||
        item.name === 'attendance-review-searchChild')
      let isUpdateAttendance = levelList.find(item => item.name === 'supplement')
      let isFillInVHC = levelList.find(item => item.name === 'batchFillInVHC')
      if (groupDetail) {
        levelList.splice(1, 0,{
          path: '/attendance-review/centerList',
          meta: {
            breadcrumb: this.$t('loc.attendanceReview'),
            pageName: this.$t('loc.attendanceReview')
          }
        })
        if (isUpdateAttendance || isFillInVHC) {
          const { groupName } = this.$route.params
          if (groupName) {
            levelList.splice(2, 0, {
              path: '/attendance-review/groupDetail',
              meta: {
                breadcrumb: groupName,
                pageName: groupName
              }
            })
          }
        }

        levelList[0].path = (localEnv !== 'local' ? '/v2' : '') + '/#/attendance-review/centerList'
      }

      groupDetail = levelList.find(item => item.name === 'attendance-signIn-searchChild')
      if (groupDetail) {
        levelList.splice(1, 0, {
          path: '/attendance-review/centerList/review',
          meta: {
            breadcrumb: this.$t('loc.signinOrOutReview')
          }
        })

        levelList[0].path = (localEnv !== 'local' ? '/v2' : '') + '/#/attendance-review/centerList/review'
      }

      groupDetail = levelList.find(item => item.name === 'signBatchFillInVHC')
      if (groupDetail) {
        levelList.splice(1, 0, {
          path: '/attendance-review/centerList/review',
          meta: {
            breadcrumb: this.$t('loc.signinOrOutReview')
          }
        })

        levelList[0].path = (localEnv !== 'local' ? '/v2' : '') + '/#/attendance-review/centerList/review'
      }

      levelList = levelList.map(level => {
        if (level.path === '/dllteacher' || level.path === '/infantSleepCheck') {
          let centerId = getSelectedSite()
          level.path = `/#/centers/${centerId || this.currentUser.default_center_id}/groups`
        }
        if (level.path === '/dllBookDetails' || level.path === '/dllBookNewNotice') {
          let centerId = this.$route.params.centerId
          level.path = `/#/centers/${centerId || this.currentUser.default_center_id}/groups`
        }
        return level
      })
      // 判断是否是 dll book 详情页，如果是，则面包屑中添加一个二级面包屑
      let dllBookDetail = levelList.find(item => item.name === 'dllBookDetails')
      if (dllBookDetail) {
        // 获取 dll book 详情页的参数
        let centerId = this.$route.params.centerId
        let groupId = this.$route.params.groupId
        let studentId = this.$route.params.studentId
        levelList.splice(1, 0, {
          path: `/#/centers/${centerId}/groups/${groupId}/students/${studentId}/reports`,
          meta: {
            breadcrumb: this.$t('loc.report')
          }
        })
      }
      // 判断是否是 dll book 评论详情页，如果是，则面包屑中添加一个二级面包屑
      let dllBookCommentDetail = levelList.find(item => item.name === 'dllBookCommentDetails')
      if (dllBookCommentDetail) {
        // 获取 dll book 详情页的参数
        let centerId = this.$route.params.centerId
        let groupId = this.$route.params.groupId
        let studentId = this.$route.params.studentId
        levelList.splice(1, 0, {
          path: `/#/centers/${centerId}/groups/${groupId}/students/${studentId}/reports`,
          meta: {
            breadcrumb: this.$t('loc.report')
          }
        })
      }
      let inkindIgnore = levelList.find(item => item.name === 'in-kind-ignore-table')
      if (inkindIgnore) {
        levelList[0].path = '/inkindreview/inkindselect/approve'
        levelList.splice(1, 0, {
          path: '/inkindreview/inkindselect/approve',
          name: 'in-kind-approve',
          meta: {
            pageName: $i18n.t('loc.inKindApproval'),
            breadcrumb: $i18n.t('loc.inKindApproval'),
            activeMenu: 'in-kind-approve'
          }
        })
      }

      if (levelList.length > 0 && levelList[0].path === '/admin/import') {
        levelList[0].path = this.navUrl['adminSeting']
      }

      // 如果同时开通 DRDP 和 State Report, 则在面包屑中添加一个二级面包屑
      if (this.stateReportOpen && this.analyReportOpen && this.$route.path.indexOf('/assessment-report') > -1) {
        levelList.splice(1, 0, {
          path: this.$route.path,
          name: this.$route.name,
          meta: {
            pageName: $i18n.t('loc.assessmentReport'),
            breadcrumb: $i18n.t('loc.assessmentReport')
          }
        })
      }

      if (levelList.length > 0 && levelList[0].path === '/admin/export-sftp') {
        levelList[0].path = this.navUrl['adminSeting']
      }

      return levelList
    },
    showClassSwitch () {
      return this.$route.name && this.$route.name === 'dllTeacherList'
    },
    // 是否是特殊教师
    isSpecialTeacher () {
      return this.currentUser && this.currentUser.specialTeacher
    },
    // 切换机构是否需要确认
    switchAgencyNeedConfirm: {
      get () {
        return this.guideFeatures && this.guideFeatures.showSwitchAgencyConfirmDialog
      },
      set (value) {
        // 如果功能引导信息存在，则更新
        if (this.guideFeatures) {
          this.guideFeatures.showSwitchAgencyConfirmDialog = value
          this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', this.guideFeatures)
        }
      }
    }
  }
}
</script>
<style lang="less">
/* 通知消息弹窗 */
.notification_right {
  top: 80px !important;
  z-index: 2000;
  width: initial;
  align-items: center;
  background-color: #F0F9EB;
}
</style>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.lg-icon-gray{
  color: #666;
}
.headerType {
  li {
    display: inline;
  }
}
.navbar-nav>li>a {
  background: transparent;
  border-radius: 4px;
  padding: 10px 10px;
  margin: 10px 2px;
  height: 44px;
}

.nav > li > a:hover, .nav > li > a:focus {
  background-color: #ddf2f3;
}
.layout-header {
  height: 60px;
  .collapse {
    padding: 0;
    margin: 0;
    ul {
      li {
        position: relative;
        .header-tabActiveBottom {
          display: none;
        }
      }
      .text-primary {
        .header-tabActiveBottom {
          display: block;
        }
      }
      .dropdown-menu {
        z-index: 9999;
        .router-link-active {
          background-color: #edf1f2;
        }
      }
    }
    .navbar-brand {
      float: left;
      line-height: 49px;
      font-weight: normal;
      img {
        max-height: 36px;
        cursor: pointer;
      }
    }
    .navbar-right {
      .noticeIcon {
        position: relative;
        padding-bottom: 10px;
      }
      .lg-icon{
        font-size: 24px;
      }
      .userInfo {
        padding: 0px !important;
        display: flex;
        align-items: center;
        background: transparent !important;
        span {
          img {
            height: 36px;
            width: 36px;
          }
          font {
            vertical-align: inherit;
            padding-right: 5px;
          }
        }
      }
      .dropdown-submenu > .dropdown-menu {
        display: block;
      }
      .dropdown-submenu:hover > a,
      .dropdown-submenu:focus > a {
        background-color: #ddf2f3;
        color: #111C1C;
      }
      .dropdown-submenu.pull-left {
        float: none !important;
        .dropdown-menu.language {
          left: -174px;
          width: 160px;
          border-radius: 8px;
          top: 10px;
          padding-top: 8px;
          padding-bottom: 8px;
          border: 0;
          box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
        }
        .dropdown-menu.language li {
          height: 40px;
          padding: 0 4px !important;
          a {
            height: 40px;
            line-height: 40px;
            padding: 0 8px !important;
            border-radius: 4px;
          }
        }
      }
    }
  }
}
.notificationTab {
  width: 452px !important;
  position: fixed !important;
  top: 60px !important;
  right: -2px !important;
  padding-top: 0 !important;
  li.clear-float {
    background: url('../../../../assets/img/notification/topbg.jpg') no-repeat;
    background-size: 100% 100%;
    height: 70px;
    line-height: 70px;
  }
}
.notify {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgb(250, 102, 102);
  display: inline-block;
  position: relative;
  top: -5px;
  margin: 0 1px;
}

.animation-advertise {
  animation: advertiseChange 1s infinite;
  -webkit-animation: advertiseChange 1s infinite;
  animation-iteration-count: 1;
}
@keyframes advertiseChange {
  0% {
    transform: translate(-300px, 250px);
  }
  100% {
    transform: translate(0, 0);
  }
}
.tab-active a {
  background: #10b3b7 !important;
  color: #fff !important;
}
.tab-active i {
  color: #fff !important;
}
.headerType.nav.navbar-nav.hidden-sm > li > a {
  padding: 15px 10px;
}
/deep/.navbar-nav>li>.dropdown-menu {
  width: 240px;
  padding: 0px !important;
  border-radius: 8px !important;
  border-width: 0;
}
/deep/.navbar-nav>li>.dropdown-menu>li {
  height:48px;
  line-height: 48px;
}
/deep/.navbar-nav>li>.dropdown-menu>li>a {
  height:48px;
  padding: 16px;
  display: flex;
  align-items: center;
}
/deep/.navbar-nav>li>.dropdown-menu>li>a span {
  color: #111C1C;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.dropdown-menu>li>a {
  color: #111C1C;
}
/deep/ .dropdown-menu .divider {
  height: 1px !important;
  margin: 0px !important;
}
.profile {
  display: flex;
  align-items: center;
  padding: 16px;
  height: 80px;
  border-radius: 8px 8px 0 0;
  background: url('../../../../assets/img/menu/header_profile_background.png') no-repeat;
  background-size: 100% 100%;
  background-color: #10B3B7;
}

.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  background-color: #ddf2f3 !important;
}

.dropdown-menu > li:last-child > a:hover {
  border-radius: 0 0 8px 8px !important;
}

.dropdown-menu > .active > a {
  font-weight: 600;
  background-color: #fff !important;
}

.dropdown-menu>div>a:hover, .dropdown-menu>div>a:focus,  .dropdown-menu>.active>a:focus, .dropdown-menu>div>.active {
  color: #10b3b7 !important;
  border-radius: 8px;
}
.dropdown-menu>.active>a:hover,
.dropdown-menu>.active>a {
  color: #10b3b7 !important;
}
.class-dropdown-menu {
   max-height: 300px;
   overflow-y: auto;
 }
 /* 班级切换图标 */
.lg-header-icon:after {
  margin-left: 8px;
  font-family: 'lg-icon'!important;
  content: "\e62c" !important;
  border-radius: 12px;
  border: 1px solid #dcdfe6;
  padding: 0px 5px;
}
 /* 班级切换图标激活状态 */
.lg-header-icon:hover, .lg-header-icon~.class-dropdown-menu:hover,.lg-header-icon-active {
  color: var(--color-primary);
  &::after {
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
  }
}
.layout-header .lg-icon{
  vertical-align: middle;
}
.profile-top-li >.dropdown-menu >li {
  .lg-icon {
    margin-right: 8px;
    margin-left: 4px;
    &.lg-icon-new-page {
      margin-left: 8px;
    }
  }
}

/deep/ .el-badge__content {
  padding: 0 4px !important;
}

/* 铃铛通知弹窗 没有通知消息时文字显示位置 */
.empty-notify {
  position: absolute!important;
  top: 60px;
  left: 50%;
  transform: translate(-50%);
}

/* 文字滚动动画 */
@keyframes scroll {
    0% {
        transform: translateY(0);
    }
    25% {
        transform: translateY(-24px);
    }
    50% {
        transform: translateY(-24px);
    }
    75% {
        transform: translateY(-48px);
    }
    100% {
        transform: translateY(-48px);
    }
}

/* 滚动容器样式 */
.scrolling-box {
  overflow: hidden;
  height: 24px;
  line-height: 24px;
}

/* 文字滚动样式 */
.scrolling-text {
  animation: scroll 4s infinite;
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  line-height: 24px;
}

/* 限时免费标签 */
.free-tag {
  width: 100px;
  background: var(--color-danger);
  color: var(--color-white);
  display: inline-block;
  vertical-align: middle;
  border-radius: 5px;
  position: relative;
  text-align: center;
}

/* 标签小三角 */
.free-tag::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 0;
  width: 0;
  height: 0;
  border-top: 12px solid transparent;
  border-bottom: 12px solid transparent;
  border-right: 15px solid var(--color-danger);
}
.notification_top {
  z-index: 2000;
  width: initial;
  align-items: center;
  background-color: #F0F9EB;
  ::v-deep {
    .el-notification__group {
      margin: 0px;
    }
  }
}

</style>
