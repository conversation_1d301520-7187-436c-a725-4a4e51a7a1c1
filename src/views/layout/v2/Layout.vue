<template>
  <el-container>
    <!-- <SideMenu v-if="!isCurriculumPlugin" v-show="(!$store.getters.viewSource || $store.getters.viewSource !== 'H5')" /> -->
    <el-container>
      <el-header v-show="(!$store.getters.viewSource || $store.getters.viewSource !== 'H5' || isCurriculumPlugin)"
                 :height="headerHeight() + 'px'">
        <LayoutHeader/>
      </el-header>
      <el-main id="router-scroll"
               :style="{margin: ( $route.fullPath.indexOf('/engagement') > -1 || $route.fullPath.indexOf('/lesson-library') > -1 || $route.fullPath.indexOf('/child-development-insights') > -1 || $route.fullPath.indexOf('/weekly-lesson-planning') > -1) ? '0px 0px 24px 0px' : $route.fullPath.indexOf('/lessons/unit-planner') > -1 ? '0px 24px' : '', height: 'calc(100% - ' + headerHeight() + 'px)'}"
               :class="[{'lg-no-margin' : noPadding},{'lg-scrollbar-hidden' : noScrollbar && !showScrollbar },{'scrollbar-hidden' : !noScrollbar && !showScrollbar},{'lg-scrollbar-show' : showScrollbar}]">
        <router-view class="h-full"/>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
import LayoutHeader from './components/LayoutHeader'
// import SideMenu from './components/SideMenu'
import { mapState } from 'vuex'
import { getSkipTypeValue } from '@/utils/common'
import tools from '@/utils/tools'
export default {
  name: 'Layout2',
  components: { LayoutHeader },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      open: state => state.common.open,
      showBanner: state => state.cgAuth.showBanner
    }),
    // 是否是 Lesson Planner 首页
    isLessonPlannHome() {
      return this.$route.fullPath.indexOf('/lessons/lesson-library/my-lessons') > -1
    },
    // 不设置padding的页面
    noPadding () {
      let filter = ['in-kind-select', 'in-kind-goals', 'in-kind-approve', 'in-kind-assignment', 'surveyList']
      if (filter.find(item => item === this.$route.name)) {
        return true
      }
      // if (this.$route.name === 'teacherShadow') {
      //   return true
      // }
      if (this.$route.name === 'view-plan' && this.$route.query.shareId) {
        return true
      }
      if (this.$route.name === 'attendance-review-centerList' ||
        this.$route.name === 'attendance-review-groupDetail' ||
        this.$route.name === 'attendance-centerList-review') {
        return true
      }
      if (this.$route.name === 'healthstats-child') {
        return true
      }
      return false
    },
    // 最外层div不设置滚动条
    noScrollbar () {
      if (this.$route.fullPath.indexOf('/DRDPReport') > -1) {
        return false
      }
      return true
    },
    showScrollbar () {
      if (this.$route.fullPath.indexOf('/inkindreview/inkindselect') > -1) {
        return true
      }
      if (this.$route.fullPath.indexOf('healthstats/child') > -1) {
        return true
      }
      return false
    },

    // 健康卡菜单是否显示
    healthStatisticsOpen () {
      if (!this.open) {
        return false
      }
      return this.open.agencyOpenHealthCheck && this.open.healthStatistics && this.open.haveGroup && this.open.existDHCForm
    },

    // 签到菜单是否显示
    healthOpen () {
      if (!this.open) {
        return false
      }
      return this.open.agencyOpenCheck && this.open.haveGroup
    },
    signInOutOpen () {
      if (!this.open) {
        return false
      }
      return this.open.manualReview && this.open.openCheck
    }
  },
  created() {
    // 设置当前是 Curriculum Genie
    this.$store.dispatch('curriculum/setIsCG', true)
  },
  methods: {
    // el-header 默认高度为60px,加上面包屑后为114px
    headerHeight () {
      // 获取是否从首页进入 Portfolio 后进入到睡眠检查
      let skipType = getSkipTypeValue()
      const isFirstVisit = JSON.parse(localStorage.getItem(this.currentUser.user_id + 'WEEKLY_PLAN_GUIDE'))
      let headerHeight = 60
      if (isFirstVisit && this.$route.fullPath === '/lessons/weekly-lesson-planning/planner-list' || this.isLessonPlannHome) {
        headerHeight = 84
      } else if (skipType && (skipType.toUpperCase() === 'INFANT_SLEEP' || (skipType.toUpperCase() === 'DLL' && this.$route.fullPath === '/dll/dllList'))) {
        headerHeight = 60
      } else if (this.$store.getters.viewSource && this.$store.getters.viewSource == 'H5' && !this.isCurriculumPlugin && !this.isLessonPlannHome) {
        headerHeight = 0
      } else if (this.$route.meta.hideBreadCrumb || this.$route.path.indexOf('/admin/') == 0) {
        headerHeight = 60
      } else if (this.$route.fullPath.indexOf('healthstats/child') !== -1 && !this.healthOpen && !this.signInOutOpen && this.healthStatisticsOpen) {
        headerHeight = 60
      } else if (tools.isComeFromIPad() && !this.isCurriculumPlugin) {
        headerHeight = 16
      } else {
        if (this.isCurriculumPlugin) {
          headerHeight = 114
        } else {
          headerHeight = 114
        }
      }
      return headerHeight  + (this.showBanner ? 52 : 0)
    }
  },
  watch: {
    $route: {
      handler () {
        let router = document.getElementById('router-scroll')
        if (router) {
          router.scrollTop = 0
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style scoped>
.el-container {
  background: #f5f6f8;
  width: 100%;
}
.el-header {
  padding: 0;
}
.el-main {
  margin: 0px 24px 24px 24px;
  padding: 0 !important;
}
.scrollbar-hidden {
  overflow: hidden;
}

@media screen and (max-width: 768px) {
  .el-main {
    margin: 0 !important;
  }
}
</style>
