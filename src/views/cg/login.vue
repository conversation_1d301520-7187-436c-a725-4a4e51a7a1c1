<template>
  <el-container class="tailwind-utilities" style="background-color: #fff;">
    <el-aside width="50%" style="overflow: hidden;background: linear-gradient(9.95deg, rgba(16, 179, 183, 0.15) -42.48%, rgba(170, 137, 242, 0.15) 117.34%);" class="aside">

      <!-- 头部图片 -->
      <div class="w-full h-full flex items-center justify-center" style="margin-left: auto; max-width: 800px; ">        
        <img src="@/assets/cg/images/auth/login-home.png" style="height: 85%;width: 85%;max-width: 500px;max-height: 900px ;
        object-fit: contain;" />
      </div>

    </el-aside>
    <el-container>
      <!-- 右边 -->
      <div class="w-full flex items-start flex-col items-center justify-center" style="max-width: 800px;height: 100vh;">
        <div class="w-[500px] flex flex-col items-center justify-center" style="max-width: 100%;">
          <!-- 头部操作 -->
          <div class="flex w-[300px] justify-between items-center pb-0">
            <!-- 返回 -->
            <el-button v-if="step !== 'login-options'" class="cursor-pointer" @click="handleBack">
              <i class="el-icon-arrow-left"></i>
            </el-button>
          </div>
          <!-- 根据当前步骤显示不同组件 -->
          <div class="p-6 pb-16" v-if="visible">
            <login-options v-if="step === 'login-options'" :loginLoading="loginLoading" :isLoginPage="true"
              @close="close" @emailLogin="handleEmailLogin" @loginSuccess="loginSuccess" />
            <!-- 账号密码登录 -->
            <password-login v-if="step === 'password-login'" :isLoginPage="true" :initialEmail="email"
              @forgotPassword="handleForgotPassword" @updateEmail="email = $event" @close="close"
              @back="step = 'login-options'" @loginSuccess="loginSuccess" />
            <!-- 邮箱验证 -->
            <verify-email v-if="step === 'verify-email'" :isLoginPage="true" :initialEmail="email"
              @verified="handleEmailVerified" @back="step = 'login-options'" />
            <!-- 设置密码 -->
            <set-password v-if="step === 'set-password'" :isLoginPage="true" :initialEmail="email" @close="close"
              @signIn="step = 'password-login'" @back="step = 'login-options'" />
            <!-- 忘记密码 -->
            <forget-password v-if="step === 'forget-password'" :isLoginPage="true" :initialEmail="email"
              @updateEmail="email = $event" @back="step = 'login-options'" @close="close" />
          </div>
        </div>
        <!-- 浏览器警告组件 -->
        <BrowserWarning/>
      </div>
    </el-container>
  </el-container>

</template>

<script>
  import { useInvitationApi } from '@/api/cg/invitation'
  import { mapState, mapActions } from 'vuex'
  import { setLocale } from '@/utils/i18n'
  import { setPlatform } from '@/utils/setBaseUrl'
  import tools from '@/utils/tools'
  import { LESSON_PLAN_NEW_USER_UTC } from '../../utils/const'
  import BrowserWarning from '@/components/BrowserWarning.vue'

  // import { useAuthApi } from '@/api/cg/auth'
  export default {
    name: 'CGLogin',
    props: {
      // 登录成功回调
      onLoginSuccess: {
        type: Function,
        default: null
      },
      loginLoading: {
        type: Boolean,
        default: false
      }
    },

    components: {
      LoginOptions: () => import('@/components/cg/auth/login-options'),
      PasswordLogin: () => import('@/components/cg/auth/password-login'),
      VerifyEmail: () => import('@/components/cg/auth/verify-email'),
      SetPassword: () => import('@/components/cg/auth/set-password'),
      ForgetPassword: () => import('@/components/cg/auth/forget-password'),
      BrowserWarning
    },

    data() {
      return {
        visible: true,
        step: 'login-options', // login-options | password-login | verify-email | set-password | forget-password
        email: '',
      }
    },

    computed: {
      ...mapState({
        currentUser: state => state.cgAuth.user,
        hasPlugin: state => state.cgAuth.hasPlugin,
        showHeaderMask: state => state.cgAuth.showHeaderMask
      })
    },

    mounted() {
      // 进入登录弹窗埋点
      this.$analytics.sendEvent('lg_cg_login_autoshow')
      this.$analytics.sendEvent('lg_k12_login_exposure')
      // 进入登录页面 A/B Test 埋点
      this.handleLoginExposure()
    },
    methods: {
      ...mapActions('cgAuth', [
        'clearAuth',
        'getEventNotifyAction'
      ]),
      open() {
        this.visible = true
        this.step = 'login-options'
        this.email = ''
        // 进入登录页面埋点
        this.$analytics.sendEvent('cg_web_login_exposure')
      },

      close() {
        this.visible = false
        this.step = 'login-options'
        this.email = ''
      },

      handleBack() {
        // 如果是忘记密码页面，则返回登录
        if (this.step === 'forget-password') {
          this.step = 'password-login'
        } else {
          this.step = 'login-options'
        }
      },

      handleClose() {
        this.visible = false
        this.step = 'login-options'
        this.email = ''
      },

      /**
       * 邮箱登录
       */
      async handleEmailLogin(email, is_exist) {
        this.email = email
        // 邮箱存在，下一步密码登录
        // 邮箱不存在，下一步邮箱验证
        this.step = is_exist ? 'password-login' : 'verify-email'
        // 进入登录或验证邮箱页面埋点
        const eventName = is_exist ? 'lg_cg_login_email_exposure' : 'lg_cg_login_emailverification_exposure'
        this.$analytics.sendEvent(eventName)
      },

      handleForgotPassword() {
        this.step = 'forget-password'
        // 点击忘记密码埋点
        this.$analytics.sendEvent('lg_cg_login_forgot')
      },

      handleEmailVerified() {
        this.step = 'set-password'
        // 点击验证邮箱埋点
        this.$analytics.sendEvent('lg_cg_login_signup_exposure')
      },

      /**
       * 登录成功
       */
      loginSuccess() {
        // 如果有回调则执行回调，否则跳转到 Unit Planner 页面
        if (this.onLoginSuccess) {
          this.$analytics.sendEvent('lg_cg_login_google')
          this.onLoginSuccess()
        } else {
          this.$analytics.sendEvent('lg_cg_login_google')
          // 如果当前时间大于 LESSON_PLAN_NEW_USER_UTC，则跳转到 Lesson Plan 页面
          if (this.currentUser && tools.timeIsAfter(this.currentUser.created_at_utc, LESSON_PLAN_NEW_USER_UTC)) {
            this.$router.push('/lessons')
          } else {
            this.$router.push('/curriculum-genie/unit-planner')
          }
        }
        // 登录成功埋点
        this.$analytics.sendEvent('lg_k12_login_completelogin')
      },

      /**
      * 从路由中获取参数
      */
      async handleLoginExposure() {
        let invitationCode = this.$route.query.c
        let templateId = this.$route.query.t
        let invitationFrom = this.$route.query.f
        let variant = this.$route.query.v
        if (invitationCode && templateId) {
          localStorage.setItem('invitationCode', invitationCode)
          localStorage.setItem('templateId', templateId)
          localStorage.setItem('invitationFrom', invitationFrom)
        }
        // 如果不是通过邀请链接进来，则不记录数据
        if (!invitationCode || !variant) {
          return
        }
        // 记录数据
        let data = {
          invitation_from: invitationFrom,
        }
        // 查询邀请人信息
        const invitationInfo = await this.getInvitationInfoByInvitationCode(invitationCode)
        if (invitationInfo) {
          data.invitation_user_email = invitationInfo.email
          data.invitation_user_id = invitationInfo.user_id
        }
        if (variant && variant === 'b') {
          // 进入登录页面埋点 variant b
          this.$analytics.sendEvent('lg_k12_login_exposure_from_b', data)
        } else {
          // 进入登录页面埋点 variant a
          this.$analytics.sendEvent('lg_k12_login_exposure_from_a', data)
        }
      },

      /* 获取 URL 中的邀请信息
       */
      async getInvitationInfoByInvitationCode(invitationCode) {
        // 还是没有邀请码，结束并跳转到首页
        if (!invitationCode) {
          return
        }
        // 获取邀请人信息
        const { getInvitationInfo } = useInvitationApi()
        try {
          return await getInvitationInfo({ invitation_code: invitationCode })
        } catch (error) {
          return null
        }
      }
    },
    watch: {
      currentUser: {
        handler() {
          /// 如果当前用户已登录，则直接跳转到 Unit Planner 页面
          if (this.currentUser) {
            this.loginSuccess()
          }
        },
        immediate: true
      }
    }
  }
</script>

<style lang="scss" scoped>
  /deep/ .el-button--primary {
    height: 48px !important;
    border-radius: 8px;
  }

  /* 添加响应式样式 */
  @media (max-width: 768px) {
    .login-dialog {
      padding: 0 16px;
    }
    .aside {
      display: none;
    }
  }

  .login-dialog {
    .el-dialog {
      border-radius: 20px;
      padding: 0;
    }
  }
</style>