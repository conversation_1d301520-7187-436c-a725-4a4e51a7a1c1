import store from '@/store'
import { MessageBox } from 'element-ui'
import { toSignOut } from '@/utils/autoSignOut'
import { pathName } from '@/utils/setBaseUrl'

let currentUser = store.getters.currentUser ? store.getters.currentUser : ''

// 链接webIM
export const openChatInfo = function (params) {
  var conn = new WebIM.connection({
    isMultiLoginSessions: WebIM.config.isMultiLoginSessions,
    https: typeof WebIM.config.https === 'boolean' ? WebIM.config.https : location.protocol === 'https:',
    url: WebIM.config.xmppURL,
    heartBeatWait: WebIM.config.heartBeatWait,
    autoReconnectNumMax: WebIM.config.autoReconnectNumMax,
    autoReconnectInterval: WebIM.config.autoReconnectInterval,
    apiUrl: WebIM.config.apiURL,
    isAutoLogin: true
  })
  if (!currentUser) {
    return
  }
  if (currentUser.commInfo) {
    conn.listen(params)
    var options = {
      apiUrl: WebIM.config.apiURL,
      user: currentUser.commInfo.username,
      pwd: currentUser.commInfo.password,
      appKey: currentUser.commKey
    }
    // 当前用户开启chat功能
    if (currentUser.webChatOpen) {
      // 当前用户没有开启学院模式
      if (!currentUser.academy_open) {
        conn.open(options)
      }
    }
    return conn
  }
}
// 获取最近聊天列表
export const getChatList = function (chatList) {
  var chatLists = []
  for (var i = 0; i < chatList.length; i++) {
    chatLists.push(chatList[i])
  }
  return chatLists
}
// 主界面右下角显示提示
export const addTip = function (message) {
  isNewInfo()
  addChatInfoTranslate(message)
  let msg = message.ext.childBean
  if (typeof (msg) == 'object') {
    store.dispatch('setMessages', msg)
  } else if (typeof (msg) == 'string') {
    msg = eval('(' + msg + ')')
    store.dispatch('setMessages', msg)
  }
  store.dispatch('setMessagesHasMsg', true)
  // store.dispatch('setWebChatCount', true)
  setTimeout(function () {
    store.dispatch('setMessagesHasMsg', false)
  }, 6000)
}
// 获取用法是否开启翻译
export const getTranslate = function (userId) {
  this.$axios.get($api.urls(userId).imTranslationOpen)
    .then(data => {
      if (data.status == 'false') {
        store.dispatch('setIsTranslate', false)
      } else {
        store.dispatch('setIsTranslate', true)
      }
    })
}
// 对接收到的信息做翻译处理
export const addChatInfoTranslate = function (message) {
  // 当前本地语言
  var currentLanguage = localStorage.getItem('NG_TRANSLATE_LANG_KEY')
  var language = currentLanguage.substring(0, 2)
  // 环信语言
  var sendLanguage = message.ext.language.substring(0, 2)
  // 翻译开关是否开启
  if (store.state.chat.isTranslate) {
    // 是否存在信息
    if (message.data || message.ext.ObservationText) {
      if (sendLanguage != language) {
        var originalsData
        if (message.data) {
          originalsData = message.data
        }
        if (message.ext.ObservationText) {
          originalsData = message.ext.ObservationText
        }
        var requestData = {
          originals: [originalsData],
          lang: language
        }
        this.$axios.post($api.urls().translate,requestData)
          .then(data => {
            if (data.translations[0] != message.data) {
              message.isTranslate = true
              message.havTranslate = true
              message.translations = data.translations[0]
              addChatInfo(message)
            } else {
              addChatInfo(message)
            }
          })
        /* this.isTranslate.post({}, requestData, function (data) {
            if(data.translations[0] != message.data){
                message.isTranslate =true;
                message.havTranslate = true;
                message.translations = data.translations[0];
                addChatInfo(message)
            }else{
                addChatInfo(message);
            }
        }, function (error) {
            alertService.error(error.data.error_message, true);
        }); */
      } else {
        addChatInfo(message)
      }
    } else {
      addChatInfo(message)
    }
  } else {
    addChatInfo(message)
  }
}
// 比较接收到两条信息时间的间隔
export const compareTime = function (time1, time2) {
  if (!time2) {
    store.dispatch('setPreviousTime', time1)
    return time1.split(' ')[1]
  }
  var t1 = new Date(time1)
  var t2 = new Date(time2)
  var time = t1 - t2
  if (time > 60000) {
    store.dispatch('setPreviousTime', time1)
    return time1.split(' ')[1]
  } else {
    return false
  }
}
// 添加新消息
export const addChatInfo = function (message) {
  var chatListUer = 'chat' + currentUser.userInfo.id
  var chatList = JSON.parse(localStorage.getItem(chatListUer))
  if (chatList) {
    var list = getChatList(chatList)
  }
  var chatData = JSON.parse(localStorage.getItem('chatInfo'))
  if (chatData) {
    var chatInfo = chatData
  }
  // var myDate = moment().local().format('YYYY-MM-DD HH:mm');
  // var myDate
  // myDate = compareTime(myDate,store.state.chat.previousTime)
  // myDate = $i18n.d(new Date(myDate), 'ymdhm')
  // if (myDate) {
  //   message.receiveTime = myDate
  // }
  var same = 0; var flag = false
  if (list) {
    for (var i = 0; i < list.length; i++) {
      if (message.to == list[i].chatGroupId) {
        same = i
        flag = true
        break
      }
    }
  } else {
    list = []
  }
  var info = message.ext.childBean
  var emApnsExt = message.ext.em_apns_ext
  if (typeof info != 'object') {
    var isString1 = (typeof (info) == 'string')
    if (isString1) {
      info = JSON.parse(info)
      info.chatGroupId = emApnsExt.chatGroupId
    }
  }
  info.avatarUrl = info.avatar_url
  info.disPlayName = info.display_name
  if (flag) {
    var count = list[same].newsCount
    if (count) {
      count++
      info.newsCount = count
    } else {
      info.newsCount = 1
    }
    list.splice(i,1)
    list.unshift(info)
  } else {
    info.newsCount = 1
    list.unshift(info)
  }
  for (var x = 0; x < list.length; x++) {
    if (message.to == list[x].chatGroupId) {
      // var time = this.$moment().local().format('H:mm')
      var time = $i18n.d(new Date(), 'hmm')
      list[x].receiveTime = time
      if (message.msg) {
        list[x].txt = message.msg
        list[x].showTxt = true
      } else if (message.file) {
        list[x].tip = 'Picture'
        list[x].showTxt = false
      } else if (message.data) {
        if (message.isTranslate) {
          list[x].txt = message.translations
          list[x].showTxt = true
        } else {
          list[x].txt = message.data
          list[x].showTxt = true
        }
      } else if (message.url) {
        if (message.ext.ObservationText) {
          if (message.isTranslate) {
            list[x].txt = message.translations
            list[x].showTxt = true
          } else {
            list[x].txt = message.ext.ObservationText
            list[x].showTxt = true
          }
        } else {
          if (message.filename == 'image') {
            list[x].tip = 'Picture'
          }
          if (message.filename == 'audio') {
            list[x].tip = 'audio'
          }
          if (message.filename == 'video.mp4') {
            list[x].tip = 'video'
          }
          list[x].showTxt = false
        }
      }
    }
  }
  localStorage.setItem(chatListUer,JSON.stringify(list))
  message.sendSuccess = true
  var same1; var flag1 = false
  if (chatInfo) {
    for (let i = 0; i < chatInfo.length; i++) {
      if (message.to == chatInfo[i].id) {
        same1 = i
        flag1 = true
        break
      }
    }
  } else {
    chatInfo = []
  }
  if (flag1) {
    var isNew = true
    chatInfo[same1].data.forEach(function (info) {
      if (info.id == message.id) {
        isNew = false
      }
    })
    if (isNew) {
      chatInfo[i].data.push(message)
    }
  } else {
    info = {
      id: message.to,
      data: [message]
    }
    chatInfo.push(info)
  }
  localStorage.setItem('chatInfo',JSON.stringify(chatInfo))
}
// 判断是否有未读/新消息
export const isNewInfo = function () {
  var chatListUer = 'chat' + currentUser.userInfo.id
  var tip = false
  var chatList = JSON.parse(localStorage.getItem(chatListUer))
  if (chatList) {
    var list = getChatList(chatList)
  }
  var unreadMessageCount = 0
  if (list) {
    for (var i = 0; i < list.length; i++) {
      if (list[i].newsCount >= 1) {
        unreadMessageCount += list[i].newsCount
        tip = true
      }
    }
  }
  if (tip) {
    store.dispatch('setWebChatCount', unreadMessageCount)
  } else {
    store.dispatch('setWebChatCount', 0)
  }
}
// 主页面登录环信
export const isOpenChat = function () {
  openChatInfo({
    onOpened: function () { // 连接成功回调
      // 当前环信在主页面登录
      console.log('success')
      localStorage.setItem('chatIsHome', true)
      getTranslate(currentUser.user_id)
      store.dispatch('setIsContChat', true)
      isNewInfo()
    },
    onClosed: function () {
      // console.log('out')
      store.dispatch('setIsContChat', false)
      $axios.get($api.urls().loginTime).then(data => {
        if (localStorage.getItem('lastLoginUtcTime') != data.lastLoginUtcTime) {
          var isFirstOpenTip = localStorage.getItem('isFirstOpenTip')
          if (isFirstOpenTip == 'true') {
            if (localStorage.getItem('isFirstOpenTip') == 'true') {
              localStorage.setItem('isFirstOpenTip', false)
              // 用户异地登录被顶掉
              MessageBox.alert($i18n.t('loc.isLoginOutTip'), $i18n.t('loc.confirm'), {
                confirmButtonText: $i18n.t('loc.confirm'),
                // type: 'warning',
                customClass: 'lg-modal-warning'
              }).then(() => {
                $axios.post($api.urls().logout).then(res => {
                  // webChat 页面是否存在
                  let isContact = localStorage.getItem('isContChat')
                  if (isContact == 'false') {
                    let url = `${pathName}/#/webChat`
                    let close = window.open(url, 'chat')
                    if (close) {
                      close.close()
                    }
                  }
                  localStorage.setItem('isFirstShowErr', false)
                  // 清空退出
                  toSignOut()
                })
              })
            }
          }
        }
      })
    }, // 连接关闭回调
    onTextMessage: function (message) {
      addTip(message)
    }, // 收到文本消息
    onPictureMessage: function (message) {
      message.filename = 'image'
      addTip(message)
    }, // 收到图片消息
    onAudioMessage: function (message) {
      message.filename = 'audio'
      addTip(message)
    }, // 收到音频消息
    onVideoMessage: function (message) {
      message.filename = 'video.mp4'
      addTip(message)
    }// 收到视频消息
  })
}
