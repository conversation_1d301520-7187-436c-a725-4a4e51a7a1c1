/* eslint-disable */
import store from '@/store'
import tools from '@/utils/tools'
let currentUser = store.getters.currentUser ? store.getters.currentUser : ''
class ratePeriod {
  isOwner (role) {
    return role.toLowerCase() == 'agency_owner' ? true : false
  }

  isAgencyAdmin (role) {
    if (role.toLowerCase() == 'agency_admin' || role.toLowerCase() == 'site_admin') {
      return true
    } else {
      return false
    }
  }
  
  isSiteAdmin (role) {
    if (role.toLowerCase() == 'site_admin') {
      return true
    } else {
      return false
    }
  }

  // 返回当前缓存的数据
  getCurrentRatingPeriodData () {
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    if (!currentYear) {
      return false
    }
    if (!currentUser) {
      currentUser =  this.getCurrent()
    }
    let type = this.getType()
    let dateKey = 'setRatingPeriod-' + currentYear.value + '-' + currentUser.user_id + type
    let ratingPeriodData = JSON.parse(localStorage.getItem(dateKey))
    if (ratingPeriodData) {
      return ratingPeriodData
    } else {
      return false
    }
  }

  // 返回当前缓存选择周期组的数据
  setActivePeriod (index) {
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    if (!currentYear) {
      return false
    }
    if (!currentUser) {
      currentUser =  this.getCurrent()
    }
    let type = this.getType()
    let dateKey = 'setRatingPeriod-' + currentYear.value + '-' + currentUser.user_id + type
    let ratingPeriodData = JSON.parse(localStorage.getItem(dateKey))

    if (ratingPeriodData.periodGroup && ratingPeriodData.periodGroup.length > 0) {
      for (let i in ratingPeriodData.periodGroup) {
        ratingPeriodData.periodGroup[i].select = false
      }
      ratingPeriodData.periodGroup[index].select = true
    }
    localStorage.setItem(dateKey, JSON.stringify(ratingPeriodData));
    if (ratingPeriodData) {
      return ratingPeriodData
    } else {
      return false
    }
  }


  // 返回当前选中的周期组数据
  getActivePeriodGroup () {
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    if (!currentYear) {
      return false
    }
    if (!currentUser) {
      currentUser =  this.getCurrent()
    }
    let type = this.getType()
    let dateKey = 'setRatingPeriod-' + currentYear.value + '-' + currentUser.user_id + type
    let ratingPeriodData = JSON.parse(localStorage.getItem(dateKey))
    if (ratingPeriodData) {
      let activePeriodGroup = {}
      if (ratingPeriodData.periodGroup && ratingPeriodData.periodGroup.length > 0) {
        for (let i in ratingPeriodData.periodGroup) {
          if (ratingPeriodData.periodGroup[i].select) {
            activePeriodGroup = ratingPeriodData.periodGroup[i]
            activePeriodGroup.index = parseInt(i) + 1
          }
        }
      }
      if (activePeriodGroup) {
        return activePeriodGroup
      } else {
        return false
      }
    } else {
      return false
    }
  }

  // 更新当前选择周期组的数据
  updateActivePeriodData (periodData, index) {
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    if (!currentYear) {
      return false
    }
    if (!currentUser) {
      currentUser =  this.getCurrent()
    }
    let type = this.getType()
    let dateKey = 'setRatingPeriod-' + currentYear.value + '-' + currentUser.user_id + type
    let ratingPeriodData = JSON.parse(localStorage.getItem(dateKey))
    if (ratingPeriodData) {
      if (ratingPeriodData.periodGroup && ratingPeriodData.periodGroup.length > 0) {
        let hasErr = true
        if (index) {
          hasErr = false
          ratingPeriodData.periodGroup[index] = periodData
        } else {
          for (let i in ratingPeriodData.periodGroup) {
            if (ratingPeriodData.periodGroup[i].name == periodData.name) {
              hasErr = false
              ratingPeriodData.periodGroup[i] = periodData
            }
          }
        }
        if (!hasErr) {
          localStorage.setItem(dateKey, JSON.stringify(ratingPeriodData));
          return true
        } else {
          return false
        }
      }
    } else {
      return false
    }
  }

  getCurrent () {
    if (tools.localItem('currentUser')) {
      return JSON.parse(tools.localItem('currentUser') || '{}')
    }
  }

  // 返回当前缓存数据的KEY
  getCacheKey () {
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    if (!currentYear) {
      return false
    }
    if (!currentUser) {
      currentUser =  this.getCurrent()
    }
    let type = this.getType()
    let dateKey = 'setRatingPeriod-' + currentYear.value + '-' + currentUser.user_id + type
    return dateKey
  }

  setCacheCenterKey = function () {
    if (!currentUser) {
      currentUser =  this.getCurrent()
    }
    let dateKey = 'cacheCenter' + currentUser.user_id
    return dateKey
  }
  
  setType(type){
    let dateKey = 'GuideType'
    try {
      localStorage.setItem(dateKey, JSON.stringify(type));
    } catch (error) {
      localStorage.setItem(dateKey, JSON.stringify(''));
    }
  }

  getType(){
    let dateKey = 'GuideType'
    if (JSON.parse(localStorage.getItem(dateKey))) {
      return JSON.parse(localStorage.getItem(dateKey))
    } else {
      return ''
    }
  }

  setCacheViewKey = function () {
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    if (!currentYear) {
      return false
    }
    if (!currentUser) {
      currentUser =  this.getCurrent()
    }
    let type = this.getType()
    let dateKey = 'cacheView' + currentYear.value + currentUser.user_id + type
    return dateKey
  }

  // 获取当前设置的学年
  getPeriodYear () {
    if (JSON.parse(localStorage.getItem('setPeriodYear'))) {
      return JSON.parse(localStorage.getItem('setPeriodYear'))
    } else {
      return false
    }
  }

  removeReviewIndex (){
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    let dateKey = 'ReviewIndex_' + currentYear.value + currentUser.user_id
    localStorage.removeItem(dateKey)
  }

  getReviewIndex(){
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    let dateKey = 'ReviewIndex_' + currentYear.value + currentUser.user_id
    if (JSON.parse(localStorage.getItem(dateKey))) {
      return JSON.parse(localStorage.getItem(dateKey))
    } else {
      return 0
    }
  }
  setReviewIndex(index){
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    let dateKey = 'ReviewIndex_' + currentYear.value + currentUser.user_id
    try {
      index = parseInt(index)
      localStorage.setItem(dateKey, JSON.stringify(index));
    } catch (error) {
      localStorage.setItem(dateKey, JSON.stringify(0));
    }
  }

  removeReviewPeriodIds (){
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    let dateKey = 'ReviewPeriodIds_' + currentYear.value + currentUser.user_id
    localStorage.removeItem(dateKey)
  }

  getReviewPeriodIds(){
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    let dateKey = 'ReviewPeriodIds_' + currentYear.value + currentUser.user_id
    if (JSON.parse(localStorage.getItem(dateKey))) {
      return JSON.parse(localStorage.getItem(dateKey))
    } else {
      return []
    }
  }
  setReviewPeriodIds(ids){
    let currentYear = JSON.parse(localStorage.getItem('setPeriodYear'))
    let dateKey = 'ReviewPeriodIds_' + currentYear.value + currentUser.user_id
    try {
      localStorage.setItem(dateKey, JSON.stringify(ids));
    } catch (error) {
      localStorage.setItem(dateKey, JSON.stringify([]));
    }
  }
}
export default new ratePeriod()
