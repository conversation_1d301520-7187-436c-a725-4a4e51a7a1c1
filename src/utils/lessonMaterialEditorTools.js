// 目前为课程材料描述中的自定义行式文本编辑器服务
export default {
  /**
   * 判断网站 icon 是否为链接
   * @param {String} url Website url
   * @returns {Boolean}
   */
  isHttp_icon (url) {
    return /^(https?:)?\/\//g.test(url)
  },
  /**
   * 判断字符串后缀是否为图片格式
   * @param url
   * @return {boolean}
   */
  isImg (url) {
    let reg = /.+(\.jpeg|\.png|\.jpg)/i
    return reg.test(url)
  },
  /**
   * 获取大部分 YouTube 网站链接上的视频 ID
   * @param url
   * @returns {null|*}
   * 2022
   */
  getYouTubeID (url) {
    const youtube_regex = /^.*(youtu\.be\/|vi?\/|u\/\w\/|embed\/|\?vi?=|\&vi?=)([^#\&\?]*).*/
    const parsed = url.match(youtube_regex)
    if (parsed && parsed[2] && url.includes('youtu')) {
      return parsed[2]
    }
    return null
  },
  /**
   * 获取大部分 vimeo 网站链接上的视频 ID
   * @param url
   * @returns {null|*}
   * 2022
   */
  getVimeoID (url) {
    let parsed = url.match(/(?:www\.|player\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^\/]*)\/videos\/|album\/(?:\d+)\/video\/|video\/|)(\d+)(?:[a-zA-Z0-9_\-]+)?/i)
    if (parsed && parsed[1]) {
      return parsed[1]
    }
    return null
  },
  /**
   * 获取 Google Book 网站链接上的 BooK ID
   * @param url
   * @returns {null|*}
   * 2022
   */
  getGoogleBookID (url) {
    if (!new RegExp('^https://books.google.com/').test(url)) {
      return null
    }
    const link = new URL(url)
    return link.searchParams.get('id')
  },
  /**
   * 获取原始的文本内容
   * @param {string} 被组件处理后的 html 内容
   * @returns {string} 解析后的原始文本内容
   */
  parseValue(html) {
    let result
    // 创建一个临时的 DOM 元素
    const tempDom = document.createElement('div')
    tempDom.innerHTML = html
    // 查找包含原始内容的 div
    const contentDiv = tempDom.querySelector('.textarea-item.p')
    // 提取文本内容
    if (contentDiv) {
      // 获取文本内容
      result = contentDiv.innerText
    } else {
      result = tempDom.innerText
    }
    return result
  }
}