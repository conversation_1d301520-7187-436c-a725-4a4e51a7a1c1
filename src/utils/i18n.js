import Vue from 'vue'
import VueI18n from 'vue-i18n'
import enLocale from 'element-ui/lib/locale/lang/en'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
import esLocale from 'element-ui/lib/locale/lang/es'
import ptLocale from 'element-ui/lib/locale/lang/pt-br'
import tools from './tools'

// 注入灵魂
Vue.use(VueI18n)

const dateTimeFormats = {
  'en-US': {
    monthDay: {
      month: 'long', day: '2-digit'
    },
    short: {
      year: 'numeric', month: 'short', day: 'numeric'
    },
    long: {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      weekday: 'short',
      hour: 'numeric',
      minute: 'numeric'
    },
    ymdhm: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    },
    hmm: {
      hour: 'numeric',
      minute: '2-digit'
    }
  },
  'zh-CN': {
    monthDay: {
      month: 'long', day: '2-digit'
    },
    short: {
      year: 'numeric', month: 'short', day: 'numeric'
    },
    long: {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      weekday: 'short',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    },
    ymdhm: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    },
    hmm: {
      hour: 'numeric',
      minute: '2-digit'
    }
  },
  'pt-BR': {
    monthDay: {
      month: 'long', day: '2-digit'
    },
    short: {
      year: 'numeric', month: 'short', day: 'numeric'
    },
    long: {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      weekday: 'short',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    },
    ymdhm: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    },
    hmm: {
      hour: 'numeric',
      minute: '2-digit'
    }
  },
  'es-ES': {
    monthDay: {
      month: 'long', day: '2-digit'
    },
    short: {
      year: 'numeric', month: 'short', day: 'numeric'
    },
    long: {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      weekday: 'short',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    },
    ymdhm: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    },
    hmm: {
      hour: 'numeric',
      minute: '2-digit'
    }
  }
}

// 获取当前locals文件夹下json
let loadLocaleMessages = (function () {
  const locales = require.context('../locales', true, /[A-Za-z0-9-_,\s]+\.json$/i)
  const messages = {}
  locales.keys().forEach(key => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i)
    if (matched && matched.length > 1) {
      const locale = matched[1]
      messages[locale] = locales(key)
    }
  })
  return messages
})()

// 自定义 ElementUI 组件文字
if (enLocale && enLocale.el && enLocale.el.pagination) {
  enLocale.el.pagination.total = 'Total entries: {total}'
}

// 融合loadLocaleMessages和ElementUI的语言包,加上loc前缀，避免与element冲突
let localeMessages = {
  'pt-BR': {
    loc: loadLocaleMessages['pt-BR'],
    ...ptLocale
  },
  'en-US': {
    loc: loadLocaleMessages['en-US'],
    ...enLocale
  },
  'zh-CN': {
    loc: loadLocaleMessages['zh-CN'],
    ...zhLocale
  },
  'es-ES': {
    loc: loadLocaleMessages['es-ES'],
    ...esLocale
  }
}

// 默认保存的语言
let language = process.env.VUE_APP_I18N_LOCALE
// 失效时的默认语言
let FALLBACK_LOCALE = process.env.VUE_APP_I18N_FALLBACK_LOCALE

// 初始化VueI18n
const i18n = new VueI18n({
  locale: language || 'en-US',
  fallbackLocale: FALLBACK_LOCALE || 'en-US',
  messages: localeMessages,
  dateTimeFormats
})

// 暴露接口设置语言
export const setLocale = lang => {
  if (lang === undefined) {
    // 初始化获取localStorage
    lang = tools.localItem('NG_TRANSLATE_LANG_KEY')
    // 设置无效语言时，返回默认en-US
    if (localeMessages[lang] === undefined) {
      lang = language
    }
  }
  tools.localItem('NG_TRANSLATE_LANG_KEY',lang)

  Object.keys(localeMessages).forEach(lang => {
    document.getElementsByTagName('html')[0].classList.remove(`lang-${lang}`)
  })
  document.getElementsByTagName('html')[0].classList.add(`lang-${lang}`)
  document.getElementsByTagName('html')[0].setAttribute('lang', lang)

  Vue.config.lang = lang
  i18n.locale = lang
}

// 初始化调用
setLocale()
// 当想要在非Vue的应用中调用时，通过$i18n.t
window.$i18n = i18n

export default i18n
