let AppUtil = {
  appOpenUrlByBrowser: (data) => {
    let dic = { url: data }
    try {
      window.webkit.messageHandlers.appOpenUrlByBrowser.postMessage(dic)
    } catch (ignore) {
      // continue regardless of error
    }
    try {
      window.android.appOpenUrlByBrowser(data)
    } catch (err) {
      // continue regardless of error
    }
    try {
      window.parent.postMessage('APP_OPEN_URL_BY_BROWSER', data)
    } catch (err) {
      // continue regardless of error
    }
  }

}

export default AppUtil
