import store from '@/store'

class history {
  mounted () {
    if (window.history && window.history.pushState) {
        // 向历史记录中插入了当前页
        window.history.pushState(null, null, document.URL)
        console.log('添加返回监听', document.URL)
        window.addEventListener('popstate', this.goBack, false)
    }
  }

  destroyed () {
    // console.log('销毁返回监听', document.URL)
    window.removeEventListener('popstate', this.goBack, false)
  }

  goBack () {
        // console.log('点击了浏览器的返回按钮', document.URL)
        window.history.pushState(null, null, document.URL)
  }

  getSetting () {
    let data = localStorage.getItem('IN_KIND_SETTING_' + store.state.user.uid)
    data = JSON.parse(data)
    return data
  }

  setSetting (data) {
    localStorage.setItem(
      'IN_KIND_SETTING_' + store.state.user.uid,
      JSON.stringify(data)
    )
  }
  setActivityType (activityId) {
    localStorage.setItem('IN_KIND_GROUP_SETTING_ACTIVITY_TYPE_' + store.state.user.uid, activityId)
  }

  getGroupSetting () {
    let activityId = localStorage.getItem('IN_KIND_GROUP_SETTING_ACTIVITY_TYPE_' + store.state.user.uid)
    let data = localStorage.getItem('IN_KIND_GROUP_SETTING_' + store.state.user.uid + '_' + activityId)
    data = JSON.parse(data)
    return data
  }

  setGroupSetting (data) {
    let activityId = localStorage.getItem('IN_KIND_GROUP_SETTING_ACTIVITY_TYPE_' + store.state.user.uid)
    localStorage.setItem(
      'IN_KIND_GROUP_SETTING_' + store.state.user.uid + '_' + activityId,
      JSON.stringify(data)
    )
  }

  setGroupRoute (name) {
    let activityId = localStorage.getItem('IN_KIND_GROUP_SETTING_ACTIVITY_TYPE_' + store.state.user.uid)
    localStorage.setItem('IN_KIND_GROUP_ROUTE_' + store.state.user.uid + '_' + activityId, name)
  }

  getGroupRoute () {
    let activityId = localStorage.getItem('IN_KIND_GROUP_SETTING_ACTIVITY_TYPE_' + store.state.user.uid)
    return localStorage.getItem('IN_KIND_GROUP_ROUTE_' + store.state.user.uid + '_' + activityId)
  }

  clearGroupSetting () {
    let activityId = localStorage.getItem('IN_KIND_GROUP_SETTING_ACTIVITY_TYPE_' + store.state.user.uid)
    localStorage.removeItem('IN_KIND_GROUP_ROUTE_' + store.state.user.uid + '_' + activityId)
    localStorage.removeItem('IN_KIND_GROUP_SETTING_' + store.state.user.uid + '_' + activityId)
    localStorage.removeItem('IN_KIND_GROUP_SETTING_ACTIVITY_TYPE_' + store.state.user.uid)
  }

  setRoute (name) {
    localStorage.setItem('IN_KIND_ROUTE_' + store.state.user.uid, name)
  }

  isSame (str1,str2) {
    let s1 = str1.replace(/\s+/g,' ')
    let s2 = str2.replace(/\s+/g,' ')
    return s1 == s2
  }
  getRateRange(){
    return { max: 300, min: 0}
  }
  getMaxLength (type) {
    let sourceName = 50
    let domainName = 60
    let domainAbbreviation = 8
    let activityDescription = 1500
    
    if (type == 'sourceName') {
      return sourceName
    } else if (type == 'domainName') {
      return domainName
    } else if (type == 'domainAbbreviation') {
      return domainAbbreviation
    } else if (type == 'activityDescription') {
      return activityDescription
    } else if (type == '') {

    }
  }
}
export default new history()
// export const getSetting = () => {
//   let data = localStorage.getItem('IN_KIND_SETTING_' + store.state.user.uid)
//   data = JSON.parse(data)
//   console.log(data)
//   return data
// }
