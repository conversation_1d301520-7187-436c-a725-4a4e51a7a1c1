import store from '@/store'
import axios from '@/utils/axios'
import tools from './tools'
/**
 * 调用翻译接口，获取翻译数据
 *
 * @param {*} originals 要翻译文本内容
 * @param {*} language  语言
 * @returns 翻译数据 Map
 */
export async function translateByAPI (originals, language) {
  // 创建请求数据
  let requestData = {
    originals: originals,
    lang: language,
    format: 'text'
  }
  // 调用翻译接口, 获取翻译后的结果
  let result = await axios.post($api.urls().translate, requestData)
  // 创建保存翻译数据的 Map
  let translateResult = new Map()
  // 遍历翻译数据, 保存到 Map 中
  for (let i = 0; i < originals.length; i++) {
    // 原始文本内容
    const original = originals[i]
    // 翻译后的文本内容
    const translate = result.translations[i]
    // 保存到 Map 中
    translateResult.set(original, translate)
  }
  return translateResult // 返回翻译数据
}

/**
 * 获取翻译数据
 *
 * @param {*} originals 要翻译文本内容
 * @returns 翻译数据
 */
export async function getTranslateResult (originals) {
  if (!originals) {
    return new Map()
  }
  // 获取用户当前语言，来当做目标语言
  let targetLanguage = store.state.user.language
  // 创建保存翻译数据的 Map
  let translateResult = new Map()
  // 创建保存未翻译的数组
  let untranslatedOriginals = []
  // 遍历原始文本内容, 判断是否已经翻译过
  for (const original of originals) {
    // 如果有翻译过, 则从 store 中获取翻译数据
    if (store.getters.translateResult[targetLanguage] && store.getters.translateResult[targetLanguage].has(original)) {
      translateResult.set(original, store.getters.translateResult[targetLanguage].get(original))
    } else { // 否则, 添加到未翻译数组中
      untranslatedOriginals.push(original)
    }
  }
  // 如果有未翻译的内容, 则调用翻译接口, 获取翻译数据
  if (untranslatedOriginals.length > 0) {
    // 调用翻译接口, 获取翻译后的结果
    let result = await translateByAPI(untranslatedOriginals, targetLanguage)
    // 遍历结果, 保存到 store 中
    result.forEach((value, key) => {
      // 保存到 store 中
      store.commit('ADD_TRANSLATE_RESULT', [targetLanguage, key, value])
      // 保存到翻译结果中
      translateResult.set(key, value)
    })
  }
  // 返回翻译结果
  return translateResult
}

/**
  * 翻译文本内容
  *
  * @param {*} result 翻译结果
  * @param {*} original 翻译前的文本内容
  * @returns 翻译后的文本内容
  */
export function translateText (result, original) {
  let translateOpen = tools.localItem('translateOpen') == 'true' || false
  // 如果翻译开关打开, 则返回翻译后的文本内容
  if (translateOpen && result instanceof Map) {
    // 如果翻译数据中有翻译后的文本内容, 则返回翻译后的文本内容
    return result.get(original) || original
  }
  return original
}
