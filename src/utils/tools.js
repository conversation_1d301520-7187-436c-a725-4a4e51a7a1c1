import axios from 'axios'
import { pathName } from './setBaseUrl'
import AppUtil from './app'
import { Message } from 'element-ui'
import moment from 'moment'
import { equalsIgnoreCase } from "./common"
class Tools {
  /**
   * 本地localStorage数据存储或读取
   * @param {string} key
   * @param {string} value
   * @returns
   * @memberof Tools
   */
  localItem (key, value) {
    // 如果 key 为空，则返回 null
    if (key === null || key === undefined) {
      return null
    }
    if (arguments.length === 1) {
      return localStorage.getItem(key)
    } else {
      return localStorage.setItem(key, value)
    }
  }

  /**
   * 函数只执行一次
   * @param fn
   * @returns {(function(...[*]): (*|undefined))|*}
   */
  once(fn) {
    let executed = false;
    return function (...args) {
      if (!executed) {
        executed = true;
        return fn.apply(this, args);
      }
    };
  }
  /**
   * 删除本地localStorage数据
   * @param {string} key
   * @returns
   * @memberof Tools
   */
  removeLocalItem (key) {
    if (key) {
      return localStorage.removeItem(key)
    }
    return localStorage.removeItem()
  }
  /**
   * 本地sessionStorage数据存储或读取
   * @param {string} key
   * @param {string} value
   * @returns
   * @memberof Tools
   */
  sessionItem (key, value) {
    if (arguments.length === 1) {
      return sessionStorage.getItem(key)
    } else {
      return sessionStorage.setItem(key, value)
    }
  }
  /**
   * 删除本地sessionStorage数据
   * @param {string} key
   * @returns
   * @memberof Tools
   */
  removeSessionItem (key) {
    if (key) {
      return sessionStorage.removeItem(key)
    }
    return sessionStorage.removeItem()
  }
  /**
   * 设置cookie
   * @param {string} name
   * @param {string} value
   * @param {string} expires
   * @param {string} domain
   * @param {string} path
   * @param {string} secure
   * @memberof Tools
   */
  setCookie (name, value, expires, domain, secure) {
    let cookieText = ''
    cookieText += encodeURIComponent(name) + '=' + encodeURIComponent(value)
    if (expires instanceof Date) {
      cookieText += '; expires=' + expires.toGMTString()
    }
    // if (path) {
    cookieText += '; path=' + '/'
    // }
    if (domain) {
      cookieText += '; domain=' + domain
    }
    if (secure) {
      cookieText += '; secure'
    }
    document.cookie = cookieText
  }
  /**
   * 获取cookie
   * @param {string} name
   * @returns
   * @memberof Tools
   */
  getCookie (name) {
    let cookieName = encodeURIComponent(name) + '='
    let cookieStart = document.cookie.indexOf(cookieName)
    let cookieValue = ''
    if (cookieStart > -1) {
      let cookieEnd = document.cookie.indexOf(';', cookieStart)
      if (cookieEnd == -1) {
        cookieEnd = document.cookie.length
      }
      cookieValue = decodeURIComponent(document.cookie.substring(cookieStart + cookieName.length, cookieEnd))
    }
    return cookieValue
  }
  /**
   * 删除cookie
   * @param {string} name
   * @param {string} path
   * @param {string} domain
   * @memberof Tools
   */
  deletCookie (name, path = "/", domain) {
    this.setCookie(name, '', '-1', domain, secure)
  }
  /**
   * 去抖动延迟执行方法
   * @param {function} fn 回调函数
   * @param {long} delay 延迟时间
   */
  debounce (fn, delay) {
    var timeoutID = null
    return function () {
      clearTimeout(timeoutID)
      var args = arguments
      var that = this
      timeoutID = setTimeout(function () {
        fn.apply(that, args)
      }, delay)
    }
  }

  /**
   * 生成 UUID
   * @returns UUID V4
   */
  uuidv4 () {
    return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
      (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    )
  }

  setHelpBtnVisiable (visiable) {
    // 隐藏帮助按钮
    let helpEle = document.getElementById('launcher')
    if (helpEle) {
      helpEle.style.display = visiable ? 'block' : 'none'
    }
    // 隐藏帮助按钮关闭按钮
    let closeEle = document.getElementById('top-layer')
    if (closeEle) {
      closeEle.style.display = visiable ? 'block' : 'none'
    }
  }

  // 格式化角色显示
  formatRole (role) {
    if (!role) {
      return null
    }
    role = role.toLowerCase()
    switch (role) {
      case 'collaborator':
        return $i18n.t('loc.teacherRole')
      case 'site_admin':
        return $i18n.t('loc.siteAdmin')
      case 'agency_admin':
        return $i18n.t('loc.agenAdmin')
      case 'agency_owner':
        return $i18n.t('loc.agencyOwner')
      case 'parent':
        return $i18n.t('loc.parent')
      case 'family_service':
        return $i18n.t('loc.familyService')
      case 'teaching_assistant':
        return $i18n.t('loc.teachingAssistant')
    }
  }

  getDRDPRatingColor (level) {
    switch (level) {
      case 'Discovering Language':
        return '#e4e7ee'
      case 'Discovering English':
        return '#cbd0df'
      case 'Exploring English':
        return '#b5bdd3'
      case 'Developing English':
        return '#a0adc9'
      case 'Building English':
        return '#8d9dbf'
      case 'Integrating English':
        return '#7a8eb6'
      case 'Discovering Spanish':
        return '#cbd0df'
      case 'Exploring Spanish':
        return '#b5bdd3'
      case 'Developing Spanish':
        return '#a0adc9'
      case 'Building Spanish':
        return '#8d9dbf'
      case 'Integrating Spanish':
        return '#7a8eb6'
      case 'Integrating Earlier':
        return '#e7e1ef'
      case 'Integrating Middle':
        return '#ccc0dc'
      case 'Integrating Later':
        return '#b6aac7'
      case 'Building Earlier':
        return '#dce5ee'
      case 'Building Middle':
        return '#b9ccdd'
      case 'Building Later':
        return '#8baac7'
      case 'Exploring Earlier':
        return '#dceedf'
      case 'Exploring Middle':
        return '#b9dec1'
      case 'Exploring Later':
        return '#8bc797'
      case 'Responding Earlier':
        return '#f9e5bb'
      case 'Responding Later':
        return '#f6d38f'
    }
    return ''
  }

  download (url, fileName) {
    url = url.replace(/\\/g, '/')
    axios({
      method: 'get',
      url: url,
      headers: { 'Origin': pathName },
      responseType: 'blob'
    }).then(data => {
      this.saveAs(data.data, fileName)
    }).catch(error => {
      console.log(error)
      var link = document.createElement('a')
      link.target = '_blank'
      link.href = url
      link.download = fileName
      link.click()
      link.remove()
    })
    // const xhr = new XMLHttpRequest()
    // xhr.open('GET', url, true)
    // xhr.responseType = 'blob'
    // xhr.setRequestHeader('Origin', 'http://127.0.0.1:8080/')
    // // xhr.setRequestHeader('Origin', '127.')
    // xhr.onload = () => {
    //   if (xhr.status === 200) {
    //     // 获取文件blob数据并保存
    //     this.saveAs(xhr.response, fileName)
    //   }
    // }
    // xhr.send()
  }

  saveAs (data, name) {
    var urlObject = window.URL || window.webkitURL || window
    var blob = new Blob([data])
    var link = document.createElement('a')
    link.target = '_blank'
    link.href = urlObject.createObjectURL(blob)
    link.download = name
    link.click()
    link.remove()
  }

  // 判断日期是否为当天,传入格式为YYYY-MM-DD
  isToday (inDate) {
    // let nowDate = new Date()
    // // 由于时区的原因，需要加上时间，要不然会自动转换为本地时间，导致判断错误
    // let checkDate = new Date(inDate + ' 00:00:00')
    // 判断是否为当天
    let nowDate = moment().format('YYYY-MM-DD')
    return moment(inDate).isSame(nowDate, 'day')
  }
  isNotEmpty (str) {
    return str && str.length > 0
  }
  // 访问来源是否来自iPad
  isComeFromIPad () {
    // 判断用户终端类型
    if (/(iPad)/i.test(navigator.userAgent)) {
      return true
    }
    return false
  }
  // 判断是否是谷歌浏览器
  isChrome () {
    return typeof window.chrome !== 'undefined'
  }
  // 判断是否是Safari浏览器
  isSafari () {
    return /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)
  }
  // 判断是否是Firefox浏览器
  isFirefox () {
    return /Firefox/.test(navigator.userAgent)
  }
  // 判断是否是MacOS
  isMacOS () {
    return /Mac OS X/.test(navigator.userAgent)
  }
// 判断是否是不支持的内嵌浏览器 (Facebook, Wechat)
  isInUnsupportedWebView () {
    return /FBAN|FBAV|Wechat/i.test(navigator.userAgent);
  }
  // 打开 url
  openUrlWithWebAndIPad (url) {
    if (this.isComeFromIPad()) {
      AppUtil.appOpenUrlByBrowser(url)
    } else {
      window.open(url, '_blank')
    }
  }
  // 计算转换文件大小
  calFilesize (size) {
    if (!size) {
      return '0B'
    }
    var num = 1024.00 // byte
    if (size < num) {
      return size + 'B'
    }
    if (size < Math.pow(num, 2)) {
      return (size / num).toFixed(2) + 'KB' // kb
    }
    if (size < Math.pow(num, 3)) {
      return (size / Math.pow(num, 2)).toFixed(2) + 'MB' // M
    }
    if (size < Math.pow(num, 4)) {
      return (size / Math.pow(num, 3)).toFixed(2) + 'GB' // G
    }
    return (size / Math.pow(num, 4)).toFixed(2) + 'TB' // T
  }
  // 计算星期几
  calWeekDay (num) {
    if (!num) {
      return ''
    }
    switch (num) {
      case 1:
        return $i18n.t('loc.Monday')
      case 2:
        return $i18n.t('loc.Tuesday')
      case 3:
        return $i18n.t('loc.Wednesday')
      case 4:
        return $i18n.t('loc.Thursday')
      case 5:
        return $i18n.t('loc.Friday')
    }
  }

  // 对象转换为 URL 参数
  objToUrlParams (obj) {
    if (!obj) {
      return ''
    }
    let params = ''
    for (let key in obj) {
      params += key + '=' + obj[key] + '&'
    }
    return '?' + params.substring(0, params.length - 1)
  }

  // 访问来源是否来自 inKind 三方填写界面
  isInKindDonate () {
    // 判断用户终端类型
    if (window && window.location && window.location.href && window.location.href.indexOf('inKindSubmission') > -1) {
      return true
    }
    return false
  }
  // 获取两个日期之间的所有年月
  getYearAndMonth (start, end) {
    let result = []
    let newResult = []
    let starts = start.split('-')
    let ends = end.split('-')
    let staYear = parseInt(starts[0])
    let staMon = parseInt(starts[1])
    let endYear = parseInt(ends[0])
    let endMon = parseInt(ends[1])
    while (staYear <= endYear) {
      if (staYear === endYear) {
        while (staMon < endMon) {
          staMon++
          result.push({ year: staYear, month: staMon })
        }
        staYear++
      } else {
        staMon++
        if (staMon > 12) {
          staMon = 1
          staYear++
        }
        result.push({ year: staYear, month: staMon })
      }
    }
    for (let i = 0; i < result.length; i++) {
      let year = result[i].year
      let monthinit = result[i].month
      let month = monthinit
      // 补'0'操作
      if (monthinit < 10) {
           month = '0' + monthinit
      } else {
           month = monthinit + ''
      }
      let ym = year + '-' + month + '-01'
      newResult.push(ym)
    }
    newResult.unshift(starts[0] + '-' + starts[1] + '-01')
    return newResult
  }
  checkEmailForTest (email) {
    if (email && email.length > 0) {
      let tempEmail = email.toLowerCase()
      return tempEmail.endsWith('@learning-genie-test.com') || tempEmail.endsWith('@learninggenie.com') ||
      tempEmail.endsWith('@learning-genie.test') || tempEmail.endsWith('@123.com') || tempEmail.endsWith('@chacuo.net') ||
      tempEmail.endsWith('@qq.com') || tempEmail.endsWith('@163.com') || tempEmail.endsWith('@126.com')
    }
    return false
  }
  /**
   * 获取可视的 dom 元素
   */
  getVisibleDom (elements) {
    let results = []
    elements.forEach(element => {
      // 假定元素默认可见
      let isVisible = true
      // 检查元素及其父级元素的 display 样式
      let currentElement = element
      while (currentElement) {
        const computedStyle = getComputedStyle(currentElement)
        if (computedStyle.display === 'none') {
          isVisible = false
          break
        }
        currentElement = currentElement.parentElement
      }
      // 如果元素可见，则添加到结果中
      if (isVisible) {
        results.push(element)
      }
    })
    return results
  }

  /**
   * 将 base64 转换为 Blob
   * @param {String} base64 base64
   * @returns {Blob} Blob
   */
  base64ToBlob(base64) {
    const splitBase64 = base64.split(',')
    const byteString = atob(splitBase64[1]);
    const mimeString = splitBase64[0].split(':')[1].split(';')[0];
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const uint8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteString.length; i++) {
      uint8Array[i] = byteString.charCodeAt(i);
    }
    return new Blob([arrayBuffer], {type: mimeString});
  }

  async copyText(content) {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(content)
      } else {
        // Fallback method for copying text to clipboard
        const textarea = document.createElement('textarea')
        textarea.value = content
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('copy')
        document.body.removeChild(textarea)
      }
      Message({
        message: 'Copied successfully.',
        type: 'success'
      })
    } catch (err) {
      console.error('Failed to copy: ', err)
    }
  }
  
  /**
   * 获取年龄段的值
   */
  getAgeValue (ageGroup) {
    switch (ageGroup) {
      case 'Infant (0-1)':
        return 1
      case 'Young Toddler (1-2)':
        return 2
      case 'Toddler (1-3)':
      case 'Toddler (2-3)':
        return 3
      case 'PS/PK (3-4)':
        return 4
      case 'TK (4-5)':
        return 5
      case 'K (5-6)':
        return 6
      case 'Grade 1':
        return 7
      case 'Grade 2':
        return 8
      case 'Grade 3 (8-9)':
      case 'Grade 3':
        return 9
      case 'Grade 4 (9-10)':
      case 'Grade 4':
        return 10
      case 'Grade 5 (10-11)':
      case 'Grade 5':
        return 11
      case 'Grade 6 (11-12)':
      case 'Grade 6':
      case 'Mixed age group':
      case 'Mixed age group (most of them are in Grade 6)': // 混龄组
        return 12
      case 'Grade 7 (12-13)':
      case 'Grade 7':
        return 13
      case 'Grade 8 (13-14)':
      case 'Grade 8':
        return 14
      case 'Grade 9 (14-15)':
      case 'Grade 9':
        return 15
      case 'Grade 10 (15-16)':
      case 'Grade 10':
        return 16
      case 'Grade 11 (16-17)':
      case 'Grade 11':
        return 17
      case 'Grade 12 (17-18)':
      case 'Grade 12':
        return 18
      default:
        return 0 // 表示无效的年龄段名称
    }
  }

/**
   * 获取年龄段的值
   */
getAgeValueTemp (ageGroup) {
  switch (ageGroup) {
    case 'Infant (0-1)':
      return 1
    case 'Young Toddler (1-2)':
      return 2
    case 'Toddler (1-3)':
    case 'Toddler (2-3)':
      return 3
    case 'PS/PK (3-4)':
      return 4
    case 'TK (4-5)':
      return 5
    case 'K-Grade 2':
    case 'K (5-6)':
      return 6
    case 'Grade 1':
      return 7
    case 'Grade 2':
      return 8
    case 'Grades 3-5':
    case 'Grade 3 (8-9)':
    case 'Grade 3':
      return 9
    case 'Grade 4 (9-10)':
    case 'Grade 4':
      return 10
    case 'Grade 5 (10-11)':
    case 'Grade 5':
      return 11
    case 'Grades 6-8':
    case 'Grade 6 (11-12)':
    case 'Grade 6':
      return 12
    case 'Grade 7 (12-13)':
    case 'Grade 7':
      return 13
    case 'Grade 8 (13-14)':
    case 'Grade 8':
      return 14
    case 'Grade 9 (14-15)':
    case 'Grade 9':
      return 15
    case 'Grade 10 (15-16)':
    case 'Grade 10':
      return 16
    case 'Grade 11 (16-17)':
    case 'Grade 11':
      return 17
    case 'Grades 9-12':
    case 'Grade 12 (17-18)':
    case 'Grade 12':
      return 18
    case 'Mixed age group':
    case 'Mixed age group (most of them are in Grade 6)': // 混龄组
      return 0
    default:
      return 0 // 表示无效的年龄段名称
  }
}

  /**
   * 获取年龄所在的年龄段
   */
  getAgeStageByAgeGroup (ageGroup) {
    switch (ageGroup) {
      case 'Infant (0-1)':
      case 'Young Toddler (1-2)':
      case 'Toddler (1-3)':
      case 'Toddler (2-3)':
      case 'PS/PK (3-4)':
      case 'TK (4-5)':
        return null;
      case 'K (5-6)':
      case 'Grade 1':
      case 'Grade 2':
        return 'K-Grade 2'
      case 'Grade 3 (8-9)':
      case 'Grade 3':
      case 'Grade 4 (9-10)':
      case 'Grade 4':
      case 'Grade 5 (10-11)':
      case 'Grade 5':
        return 'Grades 3-5'
      case 'Grade 6 (11-12)':
      case 'Grade 6':
      case 'Mixed age group':
      case 'Mixed age group (most of them are in Grade 6)': // 混龄组
      case 'Grade 7 (12-13)':
      case 'Grade 7':
      case 'Grade 8 (13-14)':
      case 'Grade 8':
        return 'Grades 6-8'
      case 'Grade 9 (14-15)':
      case 'Grade 9':
      case 'Grade 10 (15-16)':
      case 'Grade 10':
      case 'Grade 11 (16-17)':
      case 'Grade 11':
      case 'Grades 9-12':
      case 'Grade 12 (17-18)':
      case 'Grade 12':
        return 'Grades 9-12'
      default:
        return null
    }
  }

  /**
   * 处理流式返回的校训数据，组装树形结构
   * @param {*} lessonLearnerProfile 校训数据
   * @param {*} rubricsOptions 校训选项
   * @returns 校训数据
   */
  handleLessonLearnerProfilesParsedData (lessonLearnerProfile, rubricsOptions) {
    // 构建 rubricsName -> rubricsValue 的映射，便于快速查找
    const valueMap = new Map();
    (lessonLearnerProfile || []).forEach(profile => {
      if (profile && profile.rubricsName) {
        valueMap.set(profile.rubricsName, profile.rubricsValue ? profile.rubricsValue.trim() : '');
      }
    });

    // 遍历 rubricsOptions，组装树形结构
    return (rubricsOptions || []).map(main => {
      // 主 rubrics 节点
      const node = {
        rubricsName: main.title,
        rubricsNameDesc: main.description,
        rubricsExpectation: main.expectations,
        rubricsValue: valueMap.get(main.title) || '',
        subRubrics: []
      };
      // 子标准
      if (Array.isArray(main.subStandards) && main.subStandards.length > 0) {
        node.subRubrics = main.subStandards.map(sub => ({
          rubricsName: sub.title,
          rubricsNameDesc: sub.description,
          rubricsExpectation: sub.expectations,
          rubricsValue: valueMap.get(sub.title) || '',
          subRubrics: []
        })).filter(sub => sub.rubricsValue); // 过滤掉空的子标准
      }
      return node;
    }).filter(node => {
      // 过滤掉完全空的节点（主节点和所有子节点都没有值）
      const hasMainValue = node.rubricsValue;
      const hasSubValues = node.subRubrics && node.subRubrics.length > 0;
      return hasMainValue || hasSubValues;
    });
  }

  /**
   * 是否在当前年龄段
   * @param ageValue 年龄值
   * @param currentAgeGroup 当前年龄段
   */
  isInCurrentAgeGroup = (ageValue, currentAgeGroup) => {
    // 输入检查：如果年龄值为空，或者当前年龄段为空数组，则直接返回 false
    if (typeof ageValue !== 'string' || !Array.isArray(currentAgeGroup) || currentAgeGroup.length === 0) {
      return false
    }
    // 使用 Array.prototype.some 方法检查 ageValue 是否在 currentAgeGroup 中（不区分大小写）
    return currentAgeGroup.some((group) => equalsIgnoreCase(ageValue, group))
  }

  /**
   * 数组是否包含指定字符串（不分区大小写)
   * @param {*} strArray 字符串数组
   * @param {*} str 字符串
   * @returns 是否包含字符串
   */
  arrayContainsIgnoreCase (stringArr, str) {
    if (stringArr && stringArr.length > 0 && str) {
      // 转换为小写
      let lowerStr = str.toLowerCase()
      for (let i = 0; i < stringArr.length; i++) {
        // 转换为小写后比较，如果相等则包含
        if (stringArr[i].toLowerCase() === lowerStr) {
          return true
        }
      }
    }
    return false
  }

  /**
   * 判断是否符合显示课程模板的条件
   * @param {*} ages 年龄组
   * @param {*} activityType 活动类型
   * @param {*} eduProtocolsTemplateApplyOpen 是否开启课程模板功能
   * @returns 是否符合显示课程模板的条件
   */
  showLessonTemplate (ages, activityType, eduProtocolsTemplateApplyOpen) {
    // 只有一个年龄段，且年龄段为 K-12 之间的年级，且活动类型不是 Center 或 Station，且开启了课程模板功能才能显示课程模板
    if (ages && ages.length == 1) {
      let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
      let centerActivityTypes = ['Center', 'Station']
      return ages.some(age => this.arrayContainsIgnoreCase(k12Grades, age)) && !this.arrayContainsIgnoreCase(centerActivityTypes, activityType) && eduProtocolsTemplateApplyOpen
    }
    return false
  }

  /**
   * 通过注册演示表单，动态的添加样式
   * @param cssText 具体的 css 样式
   * @param number 样式的编号
   */
  dynamicAddStyleInDocument(cssText, number) {
    const styleSheet = new CSSStyleSheet()
    styleSheet.insertRule(cssText, number)
    // 获取页面中的 style 元素
    document.adoptedStyleSheets = [...document.adoptedStyleSheets, styleSheet]
    return styleSheet
  }

  /**
   * 通过注册演示表单，动态的删除样式
   * @param sheet 样式表
   */
  dynamicRemoveStyleInDocument(sheet) {
    // 如果没有样式表，则直接返回
    if (!sheet) {
      return
    }
    // 删除指定编号的样式
    document.adoptedStyleSheets = document.adoptedStyleSheets.filter(s => s !== sheet)
  }

  /**
   * 获取框架数据
   * @param {*} frameworkData 框架数据
   * @param {*} selectedGrade 选中的年级
   * @returns 框架数据
   */
  getFrameworkData (frameworkData, selectedGrade) {
    let res = frameworkData.map(country => {
      return {
        gradeFrameworkId: country.country,
        frameworkName: country.country,
        frameworks: country.stateFrameworkInfo.map(stateData => {
          return {
            gradeFrameworkId: stateData.state,
            frameworkName: stateData.state,
            frameworks: stateData.stateFrameworkInfo.frameworks.map(framework => {
              return {
                frameworkName: framework.frameworkName,
                gradeFrameworkId: framework.gradeFrameworkIdMap[selectedGrade],
                isDisabled: !framework.gradeFrameworkIdMap[selectedGrade]
              }
            }).sort((a, b) => a.isDisabled - b.isDisabled)// 将可选的选项放在前面
          }
        })
      }
    })
    // 特殊处理墨西和 IB
    res.forEach(item => {
      if (item.gradeFrameworkId.toLowerCase() === 'Mexico'.toLowerCase() || item.gradeFrameworkId.toLowerCase() === 'IB'.toLowerCase()) {
        item.frameworks = item.frameworks[0].frameworks
      }
    })
    return res
  }

  /**
   * 是否为两个层级的国家
   * @param country
   */
  isTwoLevelCountry (country) {
    if (!country) {
      return false
    }
    return country.toLowerCase() === 'Mexico'.toLowerCase() || country.toLowerCase() === 'IB'.toLowerCase()
  }

  /*
   * 是否为 K12 年龄段
   * @param {string} ageGroup 年龄段
   * @returns {boolean} 是否为 K12 年龄段
   */
  isK12AgeGroup (ageGroup) {
    let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
    return this.arrayContainsIgnoreCase(k12Grades, ageGroup)
  }
    /**
   * 新增 TK（4-5）扩展方法，后续如果再新加年级，可以直接添加里面
   * @param {string} ageGroup 年龄段
   * @returns {boolean} 是否为扩展的年龄段
   */
    isExtendedAgeGroup (ageGroup) {
      let extendedGrades = ['TK (4-5)']
      return this.arrayContainsIgnoreCase(extendedGrades, ageGroup)
    }

  /**
   * 解析并验证校训数据
   * @param {*} content 校训文本数据
   * @param {*} rubricsOptions 校训选项
   * @returns 校训数据
   */
  parseAndValidateRubrics(content, rubricsOptions) {
    if (!content) {
      return [];
    }
    
    // 解析校训文本数据
    const details = [];
    const lines = content.split('\n');
    for (let line of lines) {
      line = line.trim();
      if (!line) continue;
      
      const match = line.match(/^\d+\.\s*(.*)/);
      if (match) {
        const attributeName = match[1].trim();
        details.push(attributeName);
      }
    }
    
    // 如果没有解析出数据或没有校训选项，直接返回
    if (!details.length || !rubricsOptions) {
      return [];
    }
    return this.filterRubrics(details, rubricsOptions)
  }

  /**
   * 将树形结构转换为字符串
   * @param {*} tree 树形结构
   * @returns 字符串
   */
  filterRubrics(rubricsTitleArray, rubricsOptions) {
    // 将标题转换为需要保留的集合
    const titlesToKeep = new Set(rubricsTitleArray);

    // 剪枝函数
    const prune = (node) => {
      // 检查当前节点是否匹配
      const isCurrentMatch = titlesToKeep.has(node.title);
      
      // 处理子标准
      if (node.subStandards && node.subStandards.length > 0) {
        const prunedSubs = node.subStandards
          .map(prune)
          .filter(sub => sub !== null);
        
        // 如果有保留的子标准，或者当前节点匹配，则保留
        if (prunedSubs.length > 0 || isCurrentMatch) {
          return {
            ...node,
            subStandards: prunedSubs
          };
        }
      }
      
      // 叶子节点或没有匹配的子标准
      return isCurrentMatch ? { ...node } : null;
    };

    // 对每个主标准进行剪枝
    return rubricsOptions
      .map(prune)
      .filter(rubric => rubric !== null);
  }

  /**
   * 比较两个时间的大小
   * @param {string|Date} time1 - 第一个时间
   * @param {string|Date} time2 - 第二个时间
   * @returns {number} 返回值：1表示time1>time2，-1表示time1<time2，0表示相等，null表示比较失败
   */
  compareTime (time1, time2) {
    try {
      // Safari 只识别带 T 的 ISO 格式
      if (typeof time1 === 'string' && time1 !== '' && !time1.includes('T')) {
        time1 = time1.trim().replace(/\s+/, 'T')
      }
      if (typeof time2 === 'string' && time2 !== '' && !time2.includes('T')) {
        time2 = time2.trim().replace(/\s+/, 'T')
      }
      const date1 = new Date(time1)
      const date2 = new Date(time2)

      // 验证时间是否有效
      if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
        return null
      }

      if (date1.getTime() > date2.getTime()) {
        return 1
      } else if (date1.getTime() < date2.getTime()) {
        return -1
      } else {
        return 0
      }
    } catch (error) {
      return null
    }
  }

  /**
   * 检查第一个时间是否大于第二个时间
   * @param {string|Date} time1 - 第一个时间
   * @param {string|Date} time2 - 第二个时间
   * @returns {boolean} 是否大于
   */
  timeIsAfter (time1, time2) {
    return this.compareTime(time1, time2) === 1
  }
  
  /**
   * 移除文件名中的非法字符
   * @param fileName 文件名
   * @returns {*} 处理后的文件名
   */
  removeInvalidFileChars(fileName) {
    if (!fileName) {
      return ''
    }
    // 替换非法字符为 '-'
    return fileName.replace(/[\/'"?:<>*|%#\\]/g, '-');
  }
}
export default new Tools()
