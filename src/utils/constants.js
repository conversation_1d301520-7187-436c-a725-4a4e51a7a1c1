export default {
    // 默认课程封面地址
    lessonDefaultCoverURL: 'https://s3.amazonaws.com//com.learning-genie.prod.us/b45752e9-d3ca-434c-b756-6bcf049f52c7.jpg',
    // 默认用户头像地址
    userAvatarURL: 'https://d2urtjxi3o4r5s.cloudfront.net/images/user_avatar_2023.png?v=1',
    ccssFrameworkId: 'A5CAA685-637A-EC11-9C21-4CCC6ACF6129',
    caPlfFrameworkId: '6A9D1DAC-8F93-EC11-9C22-4CCC6ACF6129',
    caPtklfFrameworkId: 'F9E1415E-CFC0-4BC8-9698-7249A303584D',
    caPtklfFrameworkIdForPS: 'F9E1415E-CFC0-4BC8-9698-7249A303584D',
    caPtklfFrameworkIdForTK: 'FBAA410A-B6F1-45F1-9309-FAACFD36C36C',
    pscFrameworkId: '********-BDCE-E411-AF66-02C72B94B99B',
    // 新增的框架
    frameworkMelsInfantId: "C32820B1-2600-4ED9-AF2C-94A010F8B5AE",
    frameworkMelsYoungToddlerId: "8FBCEF2E-563D-418B-94FC-7116EDF538A7",
    frameworkMelsToddlerId: "EF339B7B-2728-4966-A3D1-B5ABADFCD1A0",
    frameworkMelsPsPkId: "B063C849-0B61-49E9-B127-BCD2C615695E",
    frameworkMelsTkId: "6281E4C7-2F88-493C-99A2-A39155999578",
    frameworkIllinoisPsPkId: "69F21AA6-BA81-45A4-9D0B-7BA76475997E",
    frameworkIllinoisTkId: "DD5594C4-ADD4-47F4-B43F-2AA8FA0CA520",
    hsElofFrameworkId: 'A169DBFA-CD98-46AC-B337-35607CC1EB29',
    illinoisFrameworkId: '70AA5F98-C09A-4D6E-8CC7-CBEF848CCC12',
    existPSAndITTemplate: ['A169DBFA-CD98-46AC-B337-35607CC1EB29', '51544A61-4074-438A-90DB-82E5B02FD661', '2F2AE2AC-4783-4593-97C6-BF8CFB56DAF7', '0D30E1D5-3DA7-49B3-906A-816B984BD02E'],
    ousdFrameworkId: 'D47854DF-F042-9DDF-0985-EBE90008733F', // OUSD Framework ID
    // 框架缩写映射，框架 ID 必须大写！
    lessonFrameworkAbbrMap: new Map([
      ["F9E1415E-CFC0-4BC8-9698-7249A303584D", "CA-PTKLF"],
      ["FBAA410A-B6F1-45F1-9309-FAACFD36C36C", "CA-PTKLF"],
      ["69F21AA6-BA81-45A4-9D0B-7BA76475997E", "IELDS"],
      ["DD5594C4-ADD4-47F4-B43F-2AA8FA0CA520", "IELDS"],
      ["C32820B1-2600-4ED9-AF2C-94A010F8B5AE", "MELS"],
      ["8FBCEF2E-563D-418B-94FC-7116EDF538A7", "MELS"],
      ["EF339B7B-2728-4966-A3D1-B5ABADFCD1A0", "MELS"],
      ["B063C849-0B61-49E9-B127-BCD2C615695E", "MELS"],
      ["6281E4C7-2F88-493C-99A2-A39155999578", "MELS"],
    ]),
    ousdFrameworkCmsDomainName: 'CMS.TK' // OUSD Framework CMS Domain Name
}
export const MappedStateFramework = [
    {
        id: '6A9D1DAC-8F93-EC11-9C22-4CCC6ACF6129',
        name: 'California Preschool Learning Foundations',
        abbreviation: 'CA-PLF',
        grades: new Map(),
        stateName: 'California'
    },
    // 添加 PT-KLF 框架
    {
        id: 'F9E1415E-CFC0-4BC8-9698-7249A303584D',
        name: 'California Preschool/Transitional Kindergarten Learning Foundations',
        abbreviation: 'CA-PTKLF',
        stateName: 'California',
        grades: new Map([
            ['PS/PK (3-4)', 'F9E1415E-CFC0-4BC8-9698-7249A303584D'],
            ['TK (4-5)', 'FBAA410A-B6F1-45F1-9309-FAACFD36C36C']
        ])
    },
    {
        id: 'A5CAA685-637A-EC11-9C21-4CCC6ACF6129',
        name: 'Common Core State Standards',
        abbreviation: 'CCSS',
        grades: new Map(),
        stateName: 'California'
    },
    {
        id: '0D30E1D5-3DA7-49B3-906A-816B984BD02E',
        name: 'Pennsylvania Learning Standards for Early Childhood',
        abbreviation: 'Pennsylvania Learning Standards for Early Childhood',
        grades: new Map(),
        stateName: 'Pennsylvania'
    },
    {
        id: 'DB45D751-02BA-4DED-A2DD-9358A7F38F51',
        name: 'Texas Prekindergarten Guidelines',
        abbreviation: 'Texas Prekindergarten Guidelines',
        grades: new Map(),
        stateName: 'Texas'
    },
    {
        id: '51544A61-4074-438A-90DB-82E5B02FD661',
        name: 'Florida Early Learning and Developmental Standards',
        abbreviation: 'Florida Early Learning and Developmental Standards',
        grades: new Map(),
        stateName: 'Florida'
    },
    {
        id: '2F2AE2AC-4783-4593-97C6-BF8CFB56DAF7',
        name: 'Ohio\'s Early Learning and Development Standards',
        abbreviation: 'Ohio\'s Early Learning and Development Standards',
        grades: new Map(),
        stateName: 'Ohio'
    },
    {
    "id": "B063C849-0B61-49E9-B127-BCD2C615695E",
    "abbreviation": "MELS",
    "name": "Missouri Early Learning Standards",
    "stateName": "Missouri",
      "grades": new Map([
        ["Young Toddler (1-2)", "8FBCEF2E-563D-418B-94FC-7116EDF538A7"],
        ["Infant (0-1)", "C32820B1-2600-4ED9-AF2C-94A010F8B5AE"],
        ["TK (4-5)", "6281E4C7-2F88-493C-99A2-A39155999578"],
        ["Toddler (2-3)", "EF339B7B-2728-4966-A3D1-B5ABADFCD1A0"],
        ["PS/PK (3-4)", "B063C849-0B61-49E9-B127-BCD2C615695E"]
      ])
    },
    {
      "id": "69F21AA6-BA81-45A4-9D0B-7BA76475997E",
      "abbreviation": "Illinois Early Learning and Development Standards",
      "name": "Illinois Early Learning and Development Standards",
      "stateName": "Illinois",
      "grades": new Map([
        ["PS/PK (3-4)", "69F21AA6-BA81-45A4-9D0B-7BA76475997E"],
        ["TK (4-5)", "DD5594C4-ADD4-47F4-B43F-2AA8FA0CA520"]
      ])
    },
    {
      id: 'A169DBFA-CD98-46AC-B337-35607CC1EB29',
      name: 'Head Start Early Learning Outcomes Framework',
      abbreviation: 'HS-ELOF',
      grades: new Map(),
      // stateName: '' 代表每一个州都存在该框架
      stateName: ''
    }
]

export const MappedStates = [
    {
        "id": "1",
        "name": "California",
        "abbreviation": "CA"
    },
    {
        "id": "2",
        "name": "Florida",
        "abbreviation": "FL"
    },
    {
        "id": "3",
        "name": "Georgia",
        "abbreviation": "GA"
    },
    {
        "id": "4",
        "name": "Illinois",
        "abbreviation": "IO"
    },
    {
        "id": "5",
        "name": "Indiana",
        "abbreviation": "IN"
    },
    {
        "id": "6",
        "name": "Louisiana",
        "abbreviation": "LA"
    },
    {
        "id": "7",
        "name": "Massachusetts",
        "abbreviation": "MA"
    },
    {
        "id": "8",
        "name": "Michigan",
        "abbreviation": "MI"
    },
    {
        "id": "9",
        "name": "Minnesota",
        "abbreviation": "MN"
    },
    {
        "id": "10",
        "name": "Missouri",
        "abbreviation": "MO"
    },
    {
        "id": "11",
        "name": "New Mexico",
        "abbreviation": "NM"
    },
    {
        "id": "12",
        "name": "New York",
        "abbreviation": "NY"
    },
    {
        "id": "13",
        "name": "North Carolina",
        "abbreviation": "NC"
    },
    {
        "id": "14",
        "name": "Ohio",
        "abbreviation": "OH"
    },
    {
        "id": "15",
        "name": "Oklahoma",
        "abbreviation": "OK"
    },
    {
        "id": "16",
        "name": "Pennsylvania",
        "abbreviation": "PA"
    },
    {
        "id": "17",
        "name": "Tennessee",
        "abbreviation": "TN"
    },
    {
        "id": "18",
        "name": "Texas",
        "abbreviation": "TX"
    },
    {
        "id": "19",
        "name": "Wisconsin",
        "abbreviation": "WI"
    }
]
export const SchoolReadinessAgeGroups = [
    {
        name: '0 to 8 months',
        frameworkId: 'E163164F-BDCE-E411-AF66-02C72B94B99B',
        frameworkName: 'DRDP2015-INFANT-TODDLER Comprehensive View'
    },
    {
        name: '9 to 18 months',
        frameworkId: 'E163164F-BDCE-E411-AF66-02C72B94B99B',
        frameworkName: 'DRDP2015-INFANT-TODDLER Comprehensive View'
    },
    {
        name: '19 to 36 months',
        frameworkId: 'E163164F-BDCE-E411-AF66-02C72B94B99B',
        frameworkName: 'DRDP2015-INFANT-TODDLER Comprehensive View'
    },
    {
        name: '3 years old',
        frameworkId: '********-BDCE-E411-AF66-02C72B94B99B',
        frameworkName: 'DRDP2015-PRESCHOOL Comprehensive view'
    },
    {
        name: '4 and 5 years old',
        frameworkId: '********-BDCE-E411-AF66-02C72B94B99B',
        frameworkName: 'DRDP2015-PRESCHOOL Comprehensive view'
    }
]
// 合并年龄组对排序索引的映射
export const mergedViewIndexMap = new Map([
  ["9 to 36 months",2.5],
  ["3 to 5 years old",5],
])
// 合并年龄组对颜色的映射
export const mergedViewColorMap = new Map([
  ["9 to 36 months", "#EAF6FF"],
  ["3 to 5 years old", "#FFFCF3"],
]);
// 合并年龄组的周期栏对颜色的映射
export const mergedViewColorMapForPeriod = new Map([
  ["9 to 36 months", "#D4EBFD"],
  ["3 to 5 years old", "#FFF9E9"],
]);
// 合并年龄组对注释的映射
export const mergedViewCommentMap = new Map([
  ["9 to 36 months", 'loc.mergedAgeGroup1'],
  ["3 to 5 years old",'loc.mergedAgeGroup2'],
])
// 合并年龄组对其包含年龄段的映射
export const mergedViewMap = new Map([
  ["9 to 36 months", ["9 to 18 months", "19 to 36 months"]],
  ["3 to 5 years old", ["3 years old", "4 and 5 years old"]],
]);
export const planCenterTags = [
  {
    label: 'loc.curriculum84',
    options: [
      {
        value: 'Art',
        label: 'Art',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Blocks',
        label: 'Blocks',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Sensory Table',
        label: 'Sensory Table',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Math',
        label: 'Math',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Dramatic Play',
        label: 'Dramatic Play',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Library & Listening',
        label: 'Library & Listening',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Science & Engineering',
        label: 'Science & Engineering',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Writing & Drawing',
        label: 'Writing & Drawing',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Outdoor',
        label: 'Outdoor',
        disabled: false,
        isDefault: true
      }
    ]
}
]

// IT Center Tags
export const planITCenterTags = [
  {
    label: 'loc.curriculum84',
    options: [
      {
        value: 'Art',
        label: 'Art',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Blocks',
        label: 'Blocks',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Dramatic Play',
        label: 'Dramatic Play',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Fine Motor Area',
        label: 'Fine Motor Area',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Music and Movement',
        label: 'Music and Movement',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Library/Quiet Area',
        label: 'Library/Quiet Area',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Nature Science/Discovery Area',
        label: 'Nature Science/Discovery Area',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Math',
        label: 'Math',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Sand and/or Water Area',
        label: 'Sand and/or Water Area',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Outdoor',
        label: 'Outdoor',
        disabled: false,
        isDefault: true
      }
    ]
  }
]

// K-Grade2 Center Tags
export const planKCenterTags = [
  {
    label: 'loc.curriculum84',
    options: [
      {
        value: 'Art',
        label: 'Art',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Building',
        label: 'Building',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Math',
        label: 'Math',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Drama',
        label: 'Drama',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Library',
        label: 'Library',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Science & Engineering',
        label: 'Science & Engineering',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Writing & Drawing',
        label: 'Writing & Drawing',
        disabled: false,
        isDefault: true
      },
      {
        value: 'Outdoor',
        label: 'Outdoor',
        disabled: false,
        isDefault: true
      }
    ]
  }
]

// Unit Planner 周计划 Centers 组活动可选类型
export const UnitPlanCenterTags = [
    'Art',
    'Blocks',
    'Sensory Table',
    'Math',
    'Dramatic Play',
    'Library & Listening',
    'Science & Engineering',
    'Writing & Drawing',
    'Outdoor'
]

// Unit Planner 周计划下 IT Centers 组活动可选类型
export const UnitPlanITCenterTags = [
    'Art',
    'Dramatic Play',
    'Fine Motor Area',
    'Music and Movement',
    'Library/Quiet Area',
    'Nature Science/Discovery Area',
    'Math',
    'Blocks',
    'Sand and/or Water Area',
    'Outdoor'
]

// Unit Planner 周计划下 K-Grade2 Centers 组活动可选类型
export const UnitPlanKCenterTags = [
  'Art',
  'Building',
  'Math',
  'Drama',
  'Library',
  'Science & Engineering',
  'Writing & Drawing',
  'Outdoor'
]

// Unit Planner 周计划下 K-Grade2 Centers 组活动可选类型
export const UnitPlanKCenterTagsI18n = [
  { label: 'Art', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationArt') },
  { label: 'Building', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationBuilding') },
  { label: 'Math', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationMath') },
  { label: 'Drama', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationDrama') },
  { label: 'Library', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationLibrary') },
  { label: 'Science & Engineering', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationScienceEngineering') },
  { label: 'Writing & Drawing', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationWritingDrawing') },
  { label: 'Outdoor', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationOutdoor') }
]

// Unit Planner 周计划 Centers 组活动类型国际化
export const UnitPlanCenterTagsI18n = [
    { label: 'Art', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationArt') },
    { label: 'Blocks', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationBlocks') },
    { label: 'Sensory Table', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationSensoryTable') },
    { label: 'Math', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationMath') },
    { label: 'Dramatic Play', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationDramaticPlay') },
    { label: 'Library & Listening', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationLibraryListening') },
    { label: 'Science & Engineering', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationScienceEngineering') },
    { label: 'Writing & Drawing', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationWritingDrawing') },
    { label: 'Outdoor', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationOutdoor') }
]

// Unit Planner 周计划 IT Centers 组活动类型国际化
export const UnitPlanITCenterTagsI18n = [
    { label: 'Art', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationArt') },
    { label: 'Blocks', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationBlocks') },
    { label: 'Dramatic Play', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationDramaticPlay') },
    { label: 'Fine Motor Area', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationFineMotorArea') },
    { label: 'Music and Movement', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationMusicMovement') },
    { label: 'Library/Quiet Area', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationLibraryQuietArea') },
    { label: 'Nature Science/Discovery Area', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationNatureScienceDiscoveryArea') },
    { label: 'Math', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationMath') },
    { label: 'Sand and/or Water Area', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationSandWaterArea') },
    { label: 'Outdoor', i18n: $i18n.t('loc.unitPlannerStep3CenterRotationOutdoor') }
]

// Unit Planner 周计划 Centers 组活动类型图标 Map
export const UnitPlanCenterIconMap = new Map([
    ['Art', 'lg-icon-c-art'],
    ['Blocks', 'lg-icon-c-blocks'],
    ['Sensory Table', 'lg-icon-c-sensory'],
    ['Math', 'lg-icon-c-math'],
    ['Dramatic Play', 'lg-icon-c-dramatic'],
    ['Library & Listening', 'lg-icon-c-library'],
    ['Science & Engineering', 'lg-icon-c-science'],
    ['Writing & Drawing', 'lg-icon-c-writing'],
    ['Outdoor', 'lg-icon-c-outdoor']
])

// Unit Planner 周计划 IT Centers 组活动类型图标 Map
export const UnitPlanITCenterIconMap = new Map([
    ['Art', 'lg-icon-c-art'],
    ['Blocks', 'lg-icon-c-blocks'],
    ['Dramatic Play', 'lg-icon-c-dramatic'],
    ['Fine Motor Area', 'lg-icon-c-sensory'],
    ['Math', 'lg-icon-c-math'],
    ['Library/Quiet Area', 'lg-icon-c-library'],
    ['Music and Movement', 'lg-icon-c-science'],
    ['Nature Science/Discovery Area', 'lg-icon-c-writing'],
    ['Sand and/or Water Area', 'lg-icon-c-sand'],
    ['Outdoor', 'lg-icon-c-outdoor']
])

// Unit Planner 周计划 K-Grade2 Centers 组活动类型图标 Map
export const UnitPlanKCenterIconMap = new Map([
  ['Art', 'lg-icon-c-art'],
  ['Building', 'lg-icon-c-blocks'],
  ['Math', 'lg-icon-c-math'],
  ['Drama','lg-icon-c-dramatic'],
  ['Library','lg-icon-c-library'],
  ['Science & Engineering', 'lg-icon-c-science'],
  ['Writing & Drawing', 'lg-icon-c-writing'],
  ['Outdoor', 'lg-icon-c-outdoor']
])

export const RatingLevelColorMap = new Map([
  // DRDP
  ['Discovering Language', '#e4e7ee'],
  ['Discovering English', '#cbd0df'],
  ['Exploring English', '#b5bdd3'],
  ['Developing English', '#a0adc9'],
  ['Building English', '#8d9dbf'],
  ['Integrating English', '#7a8eb6'],

  ['Discovering Spanish', '#cbd0df'],
  ['Exploring Spanish', '#b5bdd3'],
  ['Developing Spanish', '#a0adc9'],
  ['Building Spanish', '#8d9dbf'],
  ['Integrating Spanish', '#7a8eb6'],

  [$i18n.t('loc.IntegratingEarlier'), '#e7e1ef'],
  ['Integrating Earlier', '#e7e1ef'],
  [$i18n.t('loc.IntegratingMiddle'), '#ccc0dc'],
  ['Integrating Middle', '#ccc0dc'],
  [$i18n.t('loc.IntegratingLater'), '#b6aac7'],
  ['IntegratingLater', '#b6aac7'],

  [$i18n.t('loc.BuildingEarlier'), '#dce5ee'],
  ['Building Earlier', '#dce5ee'],
  [$i18n.t('loc.BuildingMiddle'), '#b9ccdd'],
  ['Building Middle', '#b9ccdd'],
  [$i18n.t('loc.BuildingLater'), '#8baac7'],
  ['BuildingLater', '#8baac7'],

  [$i18n.t('loc.ExploringEarlier'), '#dceedf'],
  ['Exploring Earlier', '#dceedf'],
  [$i18n.t('loc.ExploringMiddle'), '#b9dec1'],
  ['Exploring Middle', '#b9dec1'],
  [$i18n.t('loc.ExploringLater'), '#8bc797'],
  ['Exploring Later', '#8bc797'],

  [$i18n.t('loc.RespondingEarlier'), '#f9e5bb'],
  ['Responding Earlier', '#f9e5bb'],
  [$i18n.t('loc.RespondingLater'), '#f6d38f'],
  ['Responding Later', '#f6d38f'],

  ['Developing', '#b9ccdd'],
  ['Understanding', '#8baac7'],
  ['Integrating', '#e7e1ef'],
  ['Expanding', '#ccc0dc'],
  ['Connecting', '#b6aac7'],
  ['Not Yet', '#dce5ee'],
  ['Not Rated', '#dce5ee'],
  ['1 - Early Infancy', '#feedcf'],
  ['2 - Late Infancy', '#f8e4a5'],
  ['3 - Early Toddler', '#f0f7e4'],
  ['4 - Middle Toddler', '#e2f5ea'],
  ['5 - Late Toddler', '#c3eced'],
  ['6 - Early Preschool', '#c5ddf4'],
  ['7 - Middle Preschool', '#e1e2fd'],
  ['8 - Late Preschool', '#eedaf5'],
  ['9 - Kindergarten', '#e4c4f0'],
  ['NY - Not Yet', '#f1f1f1'],
  ['UR', '#e6e9f1'],

  // DRDP2010 IT
  ['Responding with Reflexes', '#FCF1D2'],
  ['Expanding Responses', '#F8E4A5'],
  ['Acting with Purpose', '#F6D982'],
  ['Discovering Ideas', '#E2F5EA'],
  ['Developing Ideas', '#C5ECD5'],
  ['Moving with Reflexes', '#DCF6F9'],
  ['Connecting Ideas', '#C3ECED'],
  ['Combining Simple Movements', '#ABD7F1'],
  ['Coordinating Simple Movements', '#E1E2FD'],
  ['Coordinating Complex Movements', '#EEDAF5'],
  ['Exploring Complex Movements', '#EEDAF5'],
  ['Making Complex Movements', '#E4C4F0'],
  ['Expanding Complex Movements', '#A9C0E4'],

  // DRDP2010 PS
  ['Building', '#F8E4A5'],
  ['Not yet at first level', '#C3ECED'],
  ['English is the only language spoken in child’s home', '#BCD3F7'],
  ['Unable to Rate', '#e6e9f1']
])

// Other 框架特殊评分等级颜色
export const otherFrameworkSpecifyRatingLevelColorMap = new Map([
  // IK_PS_ELD
  ['DL - Discovering Language', '#dae9ff'],
  ['DCE - Discovering English', '#bcd3f7'],
  ['EE - Exploring English', '#a9c04e'],
  ['DVE - Developing English', '#98afd3'],
  ['BE - Building English', '#889fc3'],
  ['IE- Integrating English', '#788fb3'],
  ['NA - Not Applicable', '#6b81a3'],

  // IL_K
  ['1 - Early Preschool', '#feedcf'],
  ['2 - Preschool', '#f8e4a5'],
  ['3 - Kindergarten Entry', '#f0f7e4'],
  ['4 - Middle Kindergarten', '#e2f5ea'],
  ['5 - Late Kindergarten', '#c3eced'],
  ['6 - First Grade', '#c5ddf4'],
  ['UR - Unable to rate', '#e6e9f1'],

  // First Note Curriculum
  ['Not Observed', '#feedcf'],
  ['Emerging', '#f0f7e4'],
  ['Satisfactory', '#c3eced'],
  ['Proficient', '#e1e2fd'],
  ['Yes', '#f1f1f1'],
  ['No', '#e6e9f1'],

  // Australia_EYLF
  ['Exploring', '#c3eced'],
  ['Extending', '#e1e2fd'],

  // Grade Curricular
  ['Conquistado', '#FEEDCF'],
  ['Conquistando', '#F0F7E4'],
  ['A Conquistar', '#C3ECED'],

  // New Mexico Essential Indicators
  ['Rubric 2', '#FCF1D2'],
  ['Rubric 3', '#F8E4A5'],
  ['Rubric 4', '#F6D982'],
  ['Rubric 5', '#F0F7E4'],
  ['Rubric 6', '#DBF3E5'],
  ['Rubric 7', '#C5ECD5'],
  ['EI # 6.1 (Conversational Ability)', '#DCF6F9'],
  ['EI #17.4 (Family, Community Culture)', '#CBE6F6'],
  ['EI #20.1 (Plays and Interacts)', '#ABD7F1'],
  ['EI #20.2 (Social Problem Solving)', '#E1E2FD'],
  ['EI #25.3 (Role Plays)', '#EEDAF5'],
  ['EI #27.1 (Focus)', '#E4C4F0']
])

export const otherFrameworkRatingLevelColorMap = new Map([
  [0, '#e1e2fd'],
  [1, '#dcf6f9'],
  [2, '#f0f7e4'],
  [3, '#feedcf'],
  [4, '#f1f1f1'],
  [5, '#feedcf'],
  [6, '#e6e9f1'],
  [7, '#d3eff0'],
  [8, '#bcd8d9'],
  [9, '#f0e6d7'],
  [10, '#d9cfc0'],
  [11, '#c6bcad'],
  [12, '#b5ab9c'],
  [13, '#a59b8c']
])

// Demo Class 班级 ID 和默认小孩 ID
export const DefaultDemoClassGroupIdAndChildId = {
  childId: '49941195-45AE-443B-9528-36B7AC846C3C',
  groupId: 'DEMO-CLASS'
}

// Magic Age Grade
export const MagicAgeGroup = [
'PS/PK (3-4)',
'TK (4-5)'
]
// Public Lesson AssistantInfo 中的 ageGroup
export const PublicLessonAssistantInfoAgeGroup = [
  {
    'name': 'Infant (0-1)',
    'value': '0',
    'hasActivity': [1, 2, 3]
  },
  {
    'name': 'Young Toddler (1-2)',
    'value': '1',
    'hasActivity': [1, 2, 3]
  },
  {
    'name': 'Toddler (2-3)',
    'value': '2',
    'hasActivity': [1, 2, 3]
  },
  // {
  //   'name': 'Toddler (1-3)',
  //   'value': '1,2',
  //   'hasActivity': [1, 2, 3]
  // },
  {
    'name': 'PS/PK (3-4)',
    'value': '3',
    'hasActivity': [1, 2, 3]
  },
  {
    'name': 'TK (4-5)',
    'value': '4',
    'hasActivity': [1, 2, 3]
  },
  {
    'name': 'K (5-6)',
    'value': '5',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 1',
    'value': 'Grade 1',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 2',
    'value': 'Grade 2',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 3',
    'value': 'Grade 3',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 4',
    'value': 'Grade 4',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 5',
    'value': 'Grade 5',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 6',
    'value': 'Grade 6',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 7',
    'value': 'Grade 7',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 8',
    'value': 'Grade 8',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 9',
    'value': 'Grade 9',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 10',
    'value': 'Grade 10',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 11',
    'value': 'Grade 11',
    'hasActivity': [4]
  },
  {
    'name': 'Grade 12',
    'value': 'Grade 12',
    'hasActivity': [4]
  },
  // {
  //   'name': 'Mixed age group',
  //   'value': 'Mixed age group',
  //   'hasActivity': [4]
  // }
]

// Public Lesson AssistantInfo Centers 组活动可选类型
export const PublicLessonAssistantInfoCenters = [
  { disabled: false, activityId: '1', activityName: 'Art', activityValue: 'Art' },
  { disabled: false, activityId: '2', activityName: 'Blocks', activityValue: 'Blocks' },
  { disabled: false, activityId: '3', activityName: 'Sensory Table', activityValue: 'Sensory Table' },
  { disabled: false, activityId: '4', activityName: 'Math', activityValue: 'Math' },
  { disabled: false, activityId: '5', activityName: 'Dramatic Play', activityValue: 'Dramatic Play' },
  { disabled: false, activityId: '6', activityName: 'Library & Listening', activityValue: 'Library & Listening' },
  { disabled: false, activityId: '7', activityName: 'Science & Engineering', activityValue: 'Science & Engineering' },
  { disabled: false, activityId: '8', activityName: 'Writing & Drawing', activityValue: 'Writing & Drawing' }
]

export const WeeklyPlanAssistantInfoAgeGroup = [
  { typeName: 'Large Group', typeValue: 'LARGE_GROUP', hasActivity: [1, 4] },
  { typeName: 'Small Group', typeValue: 'SMALL_GROUP', hasActivity: [2, 4] },
  { typeName: 'Activities', typeValue: 'ACTIVITY', hasActivity: [1, 2, 4] },
  { typeName: 'Center', typeValue: 'CENTER', hasActivity: [3, 4, 5] },
  { typeName: 'Customized', typeValue: 'CUSTOMIZED', hasActivity: [1, 4] }
]

export const PublicLessonAssistantInfoActivityType = [
  { disabled: false, typeId: 1, typeName: 'Large Group', typeValue: 'LARGE_GROUP' },
  { disabled: false, typeId: 2, typeName: 'Small Group', typeValue: 'SMALL_GROUP' },
  { disabled: false, typeId: 3, typeName: 'Center', typeValue: 'CENTER' },
  { disabled: false, typeId: 4, typeName: 'Regular Activity', typeValue: 'TEACHER_LED_ACTIVITY' },
  { disabled: false, typeId: 5, typeName: 'Station', typeValue: 'STATION' },
]

// 定义常见的免费邮箱域名，限制这些邮箱注册
export const LIMIT_EMAIL_LIST = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'qq.com','163.com']

// 注册可用角色
export const REGISTER_ROLES = [
  {
    value: 'UPK Teachers and Coaches (including PreK and TK)',
    label: 'UPK Teachers and Coaches (including PreK and TK)'
  },
  {
    value: 'CSPP Teachers and Coaches',
    label: 'CSPP Teachers and Coaches'
  },
  {
    value: 'Early Childhood Administrators',
    label: 'Early Childhood Administrators'
  },
  {
    value: 'Instructional Coordinators',
    label: 'Instructional Coordinators'
  },
  {
    value: 'Special Education Teachers',
    label: 'Special Education Teachers'
  },
  {
    value: 'Curriculum Specialists',
    label: 'Curriculum Specialists'
  }
]
// 没有主题的活动类型
export const NoActivityTheme = ['Teacher-led Activity', 'Large Group', 'Small Group', 'Regular Activity']

// 课程模板信息
export const LessonTemplates = [
  {
      type: 'FRAYER_MODEL',
      name: $i18n.t('loc.frayerModel'),
      modelDescription: $i18n.t('loc.frayerModelTitleDesc'),
      description: $i18n.t('loc.frayerModelDesc'),
      previewTitle: $i18n.t('loc.frayerModelPreviewTitle'),
      previewDescription: $i18n.t('loc.frayerModelTitleDesc'),
      img: require('@/assets/img/lesson2/unitPlanner/frayer_model.png')
  },
  {
      type: 'SKETCH_AND_TELL',
      name: $i18n.t('loc.sketchAndTell'),
      modelDescription: $i18n.t('loc.sketchAndTellTitleDesc'),
      description: $i18n.t('loc.sketchAndTellDesc'),
      previewTitle: $i18n.t('loc.sketchAndTellPreviewTitle'),
      previewDescription: $i18n.t('loc.sketchAndTellTitleDesc'),
      img: require('@/assets/img/lesson2/unitPlanner/sketch_and_tell.png')
  },
  {
      type: 'SKETCH_AND_TELL_O',
      name: $i18n.t('loc.sketchAndTellO'),
      modelDescription: $i18n.t('loc.sketchAndTellOTitleDesc'),
      description: $i18n.t('loc.sketchAndTellODesc'),
      previewTitle: $i18n.t('loc.sketchAndTellOPreviewTitle'),
      previewDescription: $i18n.t('loc.sketchAndTellOTitleDesc'),
      img: require('@/assets/img/lesson2/unitPlanner/sketch_and_tell_o.png')
  },
  {
      type: 'BOOKA_KUCHA',
      name: $i18n.t('loc.bookaKucha'),
      modelDescription: $i18n.t('loc.bookaKuchaTitleDesc'),
      description: $i18n.t('loc.bookaKuchaDesc'),
      previewTitle: $i18n.t('loc.bookaKuchaPreviewTitle'),
      previewDescription: $i18n.t('loc.bookaKuchaTitleDesc'),
      img: require('@/assets/img/lesson2/unitPlanner/bookakucha.png')
  },
  {
      type: 'THIN_SLIDE',
      name: $i18n.t('loc.thinSlide'),
      modelDescription: $i18n.t('loc.thinSlideTitleDesc'),
      description: $i18n.t('loc.thinSlideDesc'),
      previewTitle: $i18n.t('loc.thinSlidePreviewTitle'),
      previewDescription: $i18n.t('loc.thinSlideTitleDesc'),
      img: require('@/assets/img/lesson2/unitPlanner/thin_slide.png')
  },
  {
      type: 'THIN_SLIDES_VARIATIONS',
      name: $i18n.t('loc.thinSlideV2'),
      modelDescription: $i18n.t('loc.thinSlideV2TitleDesc'),
      description: $i18n.t('loc.thinSlideV2Desc'),
      previewTitle: $i18n.t('loc.thinSlideV2PreviewTitle'),
      previewDescription: $i18n.t('loc.thinSlideV2TitleDesc'),
      img: require('@/assets/img/lesson2/unitPlanner/thin_slide_v2.png')
  },
  {
    type: 'WICKED_HYDRA',
    name: $i18n.t('loc.wickedHydra'),
    modelDescription: $i18n.t('loc.wickedHydraTitleDesc'),
    description: $i18n.t('loc.wickedHydraDesc'),
    previewTitle: $i18n.t('loc.wickedHydraPreviewTitle'),
    previewDescription: $i18n.t('loc.wickedHydraTitleDesc'),
    img: require('@/assets/img/lesson2/unitPlanner/wicked_hydra.png')
  },
  {
    type: 'THICK_SLIDE',
    name: $i18n.t('loc.thickSlide'),
    modelDescription: $i18n.t('loc.thickSlideTitleDesc'),
    description: $i18n.t('loc.thickSlideDesc'),
    previewTitle: $i18n.t('loc.thickSlidePreviewTitle'),
    previewDescription: $i18n.t('loc.thickSlideTitleDesc'),
    img: require('@/assets/img/lesson2/unitPlanner/thick_slide.png'),
    disabled: true
  },
  {
    type: 'CYBER_SANDWICH',
    name: $i18n.t('loc.cyberSandwich'),
    modelDescription: $i18n.t('loc.cyberSandwichTitleDesc'),
    description: $i18n.t('loc.cyberSandwichDesc'),
    previewTitle: $i18n.t('loc.cyberSandwichPreviewTitle'),
    previewDescription: $i18n.t('loc.cyberSandwichTitleDesc'),
    img: require('@/assets/img/lesson2/unitPlanner/cyber_sandwich.png'),
    disabled: true
  }
]

// 单元导入的样例单元
export const exampleImportUnit = {
  name: 'Example: Life Cycle of Butterflies.pdf',
  id: '783888F0-0E16-4CF9-BE34-F7AD345E0E95',
  url: 'https://s3.us-east-1.amazonaws.com/com.learning-genie.prod.us/Exemplar - Life Cycle of Butterflies.pdf'
}

// 限制选择学科个数为 3
export const domainMax3 = 3

// 测评点限制个数为 8
export const maxMeasureSelections = 8

// 顶层设计限制最大单元数 12 个
export const curriculumUnitMax12 = 12

// 顶层设计限制用户自定义 Foundation 列个数 10 个
export const customFoundationMax10 = 10
// SOR 可以选择的测评点
export const  ScienceOfReadingMeasures = 		{
  "id": "07BAE110-FEA7-45CB-8446-87B288A155A7",
  "name": "Foundational Language Development",
  "abbreviation": "FLD",
  "children": [
    {
      "id": "E7B548D5-89A3-4A7C-B813-779C44D2674D",
      "name": "Understanding and Using Vocabulary",
      "abbreviation": "FLD 1.1",
      "description": "Understand and use an increasing variety of words for objects, actions, and attributes experienced in everyday life, such as through play, conversations, or stories.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7000,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "18A89096-B3D9-42E0-B9B1-B9C9C3EA867E",
      "name": "Understanding and Using Words for Categories",
      "abbreviation": "FLD 1.2",
      "description": "Understand and use increasingly specific vocabulary to describe categories and the relationships within them.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7100,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "A50D8411-C1C8-438A-A93C-711A0187BDA5",
      "name": "Understanding and Using Size and Location Words",
      "abbreviation": "FLD 1.3",
      "description": "Understand and use increasingly specific words to describe and compare the size and location of objects (such as “longer” and “between”).",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7200,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "23E3991A-A05A-41BD-AAFB-F441033C6E3E",
      "name": "Using Grammatical Features and Sentence Structure",
      "abbreviation": "FLD 1.4",
      "description": "Use both common and less common word forms and sentence forms to express complex thoughts and ideas.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7300,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "57BACEDB-19B3-4B72-9D0D-5C3CB1C4C44B",
      "name": "Asking Questions",
      "abbreviation": "FLD 1.5",
      "description": "Use questions and follow-up questions to seek information and to clarify and confirm understanding.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7400,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "5518BB0C-4C15-40D3-8BCC-4A94A6283A21",
      "name": "Constructing Narratives",
      "abbreviation": "FLD 1.6",
      "description": "Use language to construct real or fictional extended narratives that have several details or a plotline.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7500,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "06F73200-1C8B-4E67-A455-718691A2CC72",
      "name": "Sharing Explanations and Opinions",
      "abbreviation": "FLD 1.7",
      "description": "Share detailed descriptions, opinions, and explanations.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7600,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "95E0DDDD-05E5-4EA8-8825-4F894FCDD310",
      "name": "Participating in Conversations",
      "abbreviation": "FLD 1.8",
      "description": "Participate in increasingly long and complex back-and-forth conversations with adults and peers. Respond on topic across several turns in the conversation.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7700,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "A8E75380-05C4-46A7-BB4D-978A5D3AC33A",
      "name": "Isolating Initial Sounds",
      "abbreviation": "FLD 2.1",
      "description": "Isolate and pronounce the first sound of a word, with adult support or the support of pictures or objects.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 7900,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "4EBA9783-BBC7-4758-B1CC-9946E3111AB5",
      "name": "Recognizing and Blending Sounds",
      "abbreviation": "FLD 2.2",
      "description": "When presented with syllables and individual sounds, blend them into words in speech with adult support or the support of pictures or objects.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8000,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "27A8146F-FE11-455C-B860-0BCAE72A917F",
      "name": "Participating in Rhyming and Wordplay",
      "abbreviation": "FLD 2.3",
      "description": "Produce rhyming sounds or words. Rhymes may be imperfect and can be real or nonsense words.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8100,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "3687B651-0BFE-46CB-BBF2-86DB7103E573",
      "name": "Identifying Letters",
      "abbreviation": "FLD 2.4",
      "description": "Match many letter names to their printed form. If learning the alphabet in English, Spanish, or other languages using a similar alphabet, such as Tagalog, match most (about 15 to 20) uppercase letter names and approximately half (about 12 to 16) of the lowercase letter names to their printed form.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8200,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "D12E1B08-0FB0-4FE2-85B9-325C81AD494A",
      "name": "Learning Letter–Sound Correspondence",
      "abbreviation": "FLD 2.5",
      "description": "Accurately identify or produce sounds associated with several letters or common characters with adult support. If learning the alphabet in English, Spanish, or other languages using a similar alphabet, such as Tagalog, accurately identify or produce sounds associated with about half of the letters.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8300,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "7BBC2560-8043-403E-9065-8657CD799A63",
      "name": "Understanding the Concept of Print",
      "abbreviation": "FLD 2.6",
      "description": "Identify the meaning of a few instances of familiar print in the environment.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8400,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "75C90330-5F2B-4D03-95C3-4AE530039729",
      "name": "Understanding Print Conventions",
      "abbreviation": "FLD 2.7",
      "description": "Display increasingly sophisticated book- handling behaviors and knowledge of print conventions, such as turning pages one at a time and understanding the direction and orientation of print.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8500,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "34CCDAC1-CD27-4E9F-9825-969F996D50D9",
      "name": "Demonstrating Interest in Literacy Activities",
      "abbreviation": "FLD 3.1",
      "description": "Demonstrate interest in and engagement with literacy and literacy-related activities for progressively extended periods of time and with increasing independence.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8700,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "010EB476-B055-4D7A-95A4-6E6761A3C1CE",
      "name": "Understanding Stories",
      "abbreviation": "FLD 3.2",
      "description": "Demonstrate understanding of details in a story, including knowledge of characters, events, and ordering of events, and use their increased understanding of story structure to predict what might come next when asked.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8800,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "35BBF7FA-63B9-46D8-A11C-AA009173ECD3",
      "name": "Understanding Informational Text",
      "abbreviation": "FLD 3.3",
      "description": "Demonstrate deeper understanding of informational text using their abilities to make connections to previous knowledge, make inferences, and ask questions.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 8900,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "2997D99A-8C7D-4A69-8A1B-5A64B4596075",
      "name": "Developing Fine Motor Skills in Writing",
      "abbreviation": "FLD 4.1",
      "description": "Adjust grasp and body position for increased control in drawing and writing.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 9100,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "066953F6-4583-4A83-954C-B5EB1FB7D218",
      "name": "Writing to Represent Sounds",
      "abbreviation": "FLD 4.2",
      "description": "Write, with adult support, a few recognizable letters that are intended to represent their corresponding sounds.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 9200,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "AF5B97CC-0B99-4501-9AD1-90BE6CCA413F",
      "name": "Dictating Thoughts and Ideas to Be Conveyed in Writing",
      "abbreviation": "FLD 4.3",
      "description": "Demonstrate interest in conveying extended thoughts and ideas in writing, engaging the help of an adult.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 9300,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "A610BDB8-20AB-45B5-9A0D-ED7A800AC3D3",
      "name": "Writing to Represent Words or Ideas",
      "abbreviation": "FLD 4.4",
      "description": "Write a few recognizable letters or characters to represent words or ideas.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 9400,
      "children": [],
      "iepmeasure": false
    },
    {
      "id": "4BCED063-64D8-4959-A9CF-FFC3EFD9C0E7",
      "name": "Writing Own Name",
      "abbreviation": "FLD 4.5",
      "description": "Write own name nearly correctly.",
      "core": false,
      "mappingAbbr": null,
      "sortIndex": 9500,
      "children": [],
      "iepmeasure": false
    }
  ],
  "iepmeasure": false
}

// 定义所有可选的年龄组
export const LessonAgeGroup = [
  {
    "name": "Infant (0-1)",
    "value": "0"
  },
  {
    "name": "Young Toddler (1-2)",
    "value": "1"
  },
  {
    "name": "Toddler (2-3)",
    "value": "2"
  },
  {
    "name": "Toddler (1-3)",
    "value": "1,2"
  },
  {
    "name": "PS/PK (3-4)",
    "value": "3"
  },
  {
    "name": "TK (4-5)",
    "value": "4"
  },
  {
    "name": "K (5-6)",
    "value": "5"
  },
  {
    "name": "Grade 1",
    "value": "Grade 1"
  },
  {
    "name": "Grade 2",
    "value": "Grade 2"
  },
  {
    "name": "Grade 3",
    "value": "Grade 3"
  },
  {
    "name": "Grade 4",
    "value": "Grade 4"
  },
  {
    "name": "Grade 5",
    "value": "Grade 5"
  },
  {
    "name": "Grade 6",
    "value": "Grade 6"
  },
  {
    "name": "Grade 7",
    "value": "Grade 7"
  },
  {
    "name": "Grade 8",
    "value": "Grade 8"
  },
  {
    "name": "Grade 9",
    "value": "Grade 9"
  },
  {
    "name": "Grade 10",
    "value": "Grade 10"
  },
  {
    "name": "Grade 11",
    "value": "Grade 11"
  },
  {
    "name": "Grade 12",
    "value": "Grade 12"
  },
  {
    "name": "Mixed age group",
    "value": "Mixed age group"
  }
]

// 框架和领域映射
export const FrameworkDomainsMapping = {
  "4A5ABEDB-FAEC-4B1C-8221-E038C1CFB804": [
    "E34EF77A-3370-481E-B1AA-BB83A5DC2926",
    "D22D89D8-E273-4F21-A003-50E76611113B"
  ],
  "2FD3C078-E74E-44AB-AB22-90DBD5D3A7FC": [
    "B8DFBE01-5859-4888-8759-FEE2A9CB6917",
    "5F4B179B-5F50-4E03-9017-C4477BE88B95"
  ],
  "224FD5BA-2D60-448E-A7F3-E1D9500B789F": [
    "ABE1D24D-3CF6-41CD-A5DD-12DAC9BCC4A9",
    "4CFAE70B-BB5F-443B-8818-767B13191C05"
  ],
  "B7FF07AB-064E-4A83-BC94-3EEA2EFD928A": [
    "9CA53496-78BB-4E9D-A50B-209C56E0EBD1",
    "56B17033-D93F-4D28-8958-92E005CD8D91"
  ],
  "8F6EDC79-6355-4A72-9935-088F80DF03DA": [
    "4262AEA8-F17C-49A4-8697-38F32B7B35AE",
    "64050AD6-86C3-447F-BDDF-89CEDBB8BC88"
  ],
  "2CA4D07A-D6AD-46B0-BFB6-87B465A06C61": [
    "C3D0661B-9897-44F1-8ECB-2B92A73C76B9",
    "514D60CF-4C6A-470D-9DA7-CF8D89D94F17"
  ],
  "3DCF8549-D64E-42C6-977A-1AFED9F241E6": [
    "434FA238-04B9-47DC-BA9F-9F58269FE5B6",
    "61E33C05-3D3C-442A-9C4F-1D13A1C0ABF4"
  ],
  "4A8A9734-075C-463B-AC4C-CD0D728CAF8A": [
    "8ECCFAA7-D4EC-452E-91B1-6C7DB5D24899",
    "1216CC07-B62F-48CE-B35C-F8DDD01A5861"
  ],
  "29E2958A-6774-4C87-AC50-FFAC581A4B00": [
    "34F4C41E-0D84-40A2-A36A-DA4BDDE62CC1",
    "B82E8FE5-9023-4A0B-A64F-DA18501AD105"
  ],
  "BE344BF5-AE0F-4FE8-9399-AD47F16B6258": [
    "D76E9BCE-4371-49EF-AC8A-7DC03D0D171B",
    "6ECB59AB-20DC-49F1-9BCD-B87B25B202A5"
  ],
  "3C292E49-D274-43BE-AE1B-F4D8B71466B0": [
    "0A6A74A0-4FFF-4964-9BEE-D13BEA0F29BD",
    "FAD2D8B2-5A98-4B5A-8DD3-C3083F84BC5E"
  ],
  "FBB37218-78C3-4643-93A1-3F2134DED65A": [
    "F6144ABA-90BA-46D6-A668-645C60FB7D8D",
    "4DC18386-B8D8-4BB5-A355-14B8D9BAB38D"
  ],
  "BAE27635-AC1C-47FC-AA18-5B647FD551A0": [
    "6A059960-1381-41D9-A32E-41186536A08D",
    "DA3AFC3A-8B8B-4BDD-82ED-CE01AE6C4971"
  ],
  "A474ED6B-0C12-40CD-90B6-BC116FA1884F": [
    "7B5944DD-8E0E-464D-9EED-E08B8565247E",
    "595C6898-C09E-4238-AC17-543B5D716ABE"
  ],
  "175F73A1-2BDE-403F-B7E9-6997E2486EAE": [
    "2D2C3DEE-0EC4-4AAE-8045-408DEDFD34A8",
    "ECF76527-7928-462A-B7AB-EC95873CDF45"
  ],
  "75382B48-1945-4ECF-8CBB-31CE7EB7BBEE": [
    "3EBF884F-B189-4AB6-99FF-3BAD72C8B22C",
    "DD64FB99-F814-45EE-A1D7-FC0C30D3ADF2"
  ],
  "A2E0FF5A-485E-4CDA-B9E9-3BA0A179183A": [
    "23C6F2C9-C8EC-4C1A-8201-A559FD416A72",
    "F08472A7-313E-4F98-AE05-ADDA9F5275D4"
  ],
  "9050281F-8F3A-483F-ADDD-EE773E0C6353": [
    "50AD002B-CB8C-4A15-9376-B1B49FDF68E0",
    "06575422-519C-4438-B357-068FC5556BC9"
  ],
  "C77842F9-9ADD-4CAE-8272-962E4272B936": [
    "DE4AB71C-6348-4BE4-A45D-C477452CFA68",
    "4BA2C5FA-C5AA-4147-B1D4-6B54FEC657E5"
  ],
  "E8A05505-6303-4BA8-8936-0F6F80D1F87E": [
    "08E919E8-429B-4908-ACF7-D144C8EDBBC2",
    "336FE946-8BEC-42F3-8C11-A9A22EE11BFD"
  ],
  "640F3F0B-0048-44CC-8F28-85BEFEBE016A": [
    "1F15C338-DE4B-4271-922B-8D5C1D7F69DD",
    "8B7C44B1-0FAE-43DC-AB4D-DA2AE8A59769"
  ],
  "28D0B808-BFA4-4F91-B54B-D5068D355FF1": [
    "50FFCD40-90F1-46F6-8732-487BD320A534",
    "C090FCF6-8D0E-42BE-B373-E6B251963C21"
  ],
  "CFE1E112-EABF-48AB-8114-6099F6D2559A": [
    "B6FC43B9-34F1-46ED-A41A-AC07CE4627F7",
    "32808EF7-85A3-4F41-B6EA-8B0E89EE5F48"
  ],
  "47D3188E-9402-415C-9CA5-D63057DD53CE": [
    "0191A6CB-6343-4A35-B8EF-0632F6826B79",
    "44DE1ECD-BF79-42BC-A1D4-EF4DFF3FEB21"
  ],
  "5B88AD7B-1C35-433D-88DC-E10270B37AB4": [
    "04B0512B-A9BD-48D3-863B-0A1D8301783B",
    "232C2823-350A-4501-90C9-11B58E0959CA"
  ],
  "6C91ABF7-6852-4EBA-B1F0-A6A4CC7D0A3F": [
    "B89EA2A9-3F9D-400D-A564-0C4683A80CFF",
    "4F1C7913-813D-43C5-987F-8E5E371B43F6"
  ],
  "60F42A83-4370-4D4A-9126-A93CACF761D3": [
    "35F994C2-9967-476C-80CB-A658FC1A856F",
    "E73CABF6-9040-4605-8717-8AC9AD1FEB75"
  ],
  "03B2F705-FEAC-4F0D-8DE6-468BF583A933": [
    "157A94F1-2E18-44DD-9DE6-DDF97EC2D645",
    "DB356257-795E-426D-9266-1A06D4F1EA98"
  ],
  "6D7EABEF-BAC3-438B-A560-86C8A8F176B3": [
    "3CE11732-D549-4052-B733-47D0D3F0514E",
    "68ADA695-71F1-481C-A731-B34E1EC5D8A6"
  ],
  "C88EF2A1-29A2-432C-97B8-ED50D8C5350B": [
    "F564FAE9-0386-4832-A8EF-73FD254A8ADC",
    "B7ACC703-BED4-4A4B-8007-2C380B135030"
  ],
  "64FD4449-1BF2-4F42-B778-E3CAEB1783C5": [
    "977D47B8-E8CE-42E8-AF60-5B3E73A68D52",
    "44CC94CF-5BDB-4E8C-92B6-8FDF27A34812"
  ],
  "876E5C76-87F9-4781-A460-65DDCF371CAE": [
    "CFAAA0CD-24B4-404E-BCB1-CE6BB123A90A",
    "46E526B8-0345-4743-95F0-8D5CDFDDBD2F"
  ],
  "83ECDE27-2F92-45D5-B8F7-F61FD091E9F8": [
    "C50BD109-D8E9-419F-AC78-4DDCCDDFD805",
    "D681F6A8-F6C0-450C-9434-057DCD4FC6D0"
  ],
  "5F2E34D3-310B-45CF-8444-176FACE3E130": [
    "3A5DCB0F-BB47-4B47-B74E-318DA5EA0CF0",
    "5CD92D12-1E54-4329-BE0B-9AAF47610EF7"
  ],
  "309F2F33-B5D1-4806-837C-0BA98D83CBEB": [
    "6748D780-7852-4CDE-A654-DD76A0D72A37",
    "B938E41C-6A5D-4C0C-8B54-E557EDFCA163"
  ],
  "8EE4C55C-543B-414D-AC7C-7D7AE4376395": [
    "E31D8D5A-70BF-4C3E-8888-818776EBBD86",
    "2A76E05B-E328-4F91-A97A-25280DAADDCA"
  ],
  "800142FB-98E1-4A74-AA4C-80075383F3A6": [
    "33BAB57B-5A14-4255-B3CC-8A07713FFF09",
    "A31281B6-C1BA-4A59-ADA5-1F87D0C1EEFF"
  ],
  "34D99844-CD2E-4DDA-855B-9D6C84E93FCD": [
    "0771285C-22E4-422B-9828-C6D73A9554B3",
    "E202262D-577D-4646-87EB-4445BE436268"
  ],
  "064E6CD7-DEAA-4C2F-9097-ABAE37F803FE": [
    "107296FF-B405-40AB-B4B6-C6994D6CB083",
    "B299847F-B0FA-4924-B901-E6870F36D323"
  ],
  "B7C2F53E-9D25-4982-9ACF-1E91FFD3C344": [
    "3F2E4784-B58B-4ACB-A683-00E894CF5884",
    "E45557B1-0991-4CAF-BEAF-1E36B68BE2FF"
  ],
  "CCA29466-8217-4048-84EA-7712C5E03C8F": [
    "B9595379-4318-43CB-8F2E-C5137552B11F",
    "C04AF48B-CE43-40BB-8899-40E0704F05F0"
  ],
  "FC359BB5-AD1C-4748-AF18-B3EEFCE775E3": [
    "CB40D0C6-B952-4548-812A-09C11D568CBD",
    "E4C6C344-01D1-49B6-A488-7DE4B4559F6F"
  ],
  "356B307B-5D1F-402B-827A-ABEF9C5CECCC": [
    "3E95E145-5BEE-465B-9FD6-A12A973A4B94",
    "28D9492F-533F-498C-A3EC-024DCF7BDA29"
  ],
  "24EE40DE-BD06-456C-AEF8-E581BDFE0EDA": [
    "FDB387C8-8EB3-4E08-9F0D-05A19591A1A5",
    "EABDD68F-0A9F-4F97-8DF6-98D3755BCFB7"
  ],
  "6B795BCD-E9EB-4B1F-8F93-C11B9302D771": [
    "3AD75117-73C8-4921-B513-B653DAFB6274",
    "374DDA72-4D02-44AF-8394-BB658E7AF816"
  ],
  "D0DC2B91-78D5-4C8F-971E-DF77E6DC57D0": [
    "48F3A706-3B6C-4A5E-86C9-BD64C09C1197",
    "BA98CF3D-9A19-45FA-BBD6-1350937A7704"
  ],
  "53F502BB-E0EA-4AA0-90F7-A89BAD063D24": [
    "06F33330-6004-43CF-9BE6-C0760EB240F2",
    "6FF9CF65-C195-42E1-8503-AE4DC129286E"
  ],
  "98BAE5B2-6502-4E26-82BA-0B58A9F46FC3": [
    "C2AF543D-EF0A-4C08-936B-A615A4BB12DB",
    "BEAA7896-B6FD-4CC4-86F4-03A2D09CCB30"
  ]
}
export const Grade4FrameworkDomainsMapping = {
  // 以下是 Grade 4 不同框架的 domainIds, Grade 4 需要展示 English Language Arts; 和 Social Sciences这两个领域
  'E35546D0-6A84-4A8F-8BEA-2DFFD6E21A8A': ['FA7669A1-7C17-4D34-BB89-11B42BB9E46B', 'B2D924A9-8A90-4FBA-8045-F3729B6FCD1A'],
  'F5DE5E5D-17D8-4A11-9859-FC9F8B246C65': ['550D4E3F-513C-4000-BE82-19D0AAA34FCB', '867E3416-6D29-4075-AB0E-6C3BA24CB4FA'],
  '955BD784-EF00-4E5A-A0E8-4431DCA79DCD': ['89A665BD-1015-4C14-8C2A-341ADDCAC498', 'F3123D69-34E9-4F99-9D6D-326612EA6139'],
  '5FB66C21-AEE6-4FBE-B177-70B49B57B0D6': ['BEFE7658-75A8-49A8-B9CE-7CFA5E9BA3EC', 'BF127DA4-F4B9-4301-BDA8-5D72D2D18F48'],
  '45C14E30-C0D6-49B3-AFEB-D5D66C5794CE': ['CB41E558-2575-4F9B-8074-5FF324A19B6A', 'F7393109-10EA-4F07-AAD8-2E44BC1C918C'],
  'A3540F88-80AC-4B17-86F8-82895DBBC934': ['1753914A-FFBF-45E5-9438-4830D9792789', '34EB188C-D15D-4679-8BB9-028AED972257'],
  'CD251534-D134-4EC7-B1D0-57774E94324F': ['E055C04A-E666-4CCF-BE75-37A2C49B9810', '6B8F7909-297C-4D05-A9D5-EA4A2FA7F875'],
  'FFC65D2D-7FC9-43A9-9DF8-6B1F87133B9A': ['32BCD1A1-C385-4FC2-9543-05EDF1292D81', 'D19FFE0F-ABF6-4BFE-8171-7ECB6C667BD2'],
  'BAF5DEC3-4B9A-4F5D-9CFC-6788B4DF52C4': ['41051C89-D92C-40EF-A620-9880F3326E5E', '24DACE04-474A-42F5-A4F1-59678BF4B71B'],
  '02F70C57-9792-463A-82B5-BE5D9631FA6C': ['6ECEC855-C803-4CBE-89AD-459070F7F68C', '8E9ADE88-31A6-47F6-A729-D3C29FBE147D'],
  'CD606F9A-BC72-4DAC-A21A-C728AE63B220': ['E954B4AA-A0AF-499F-B68C-5C932C6A8413', '389162BF-0E09-4EB5-A1C3-03C4393C8E1E'],
  '35FDA3BC-9D63-4EB2-A67A-BA710092BD1A': ['8FFC9251-0456-49A0-82D8-D4C000146534', '73AF9F72-FCCB-4224-A8A9-F4C771933F70'],
  'E563F378-E6C1-4928-A5C6-32AD9A16D767': ['F4ED3D4C-1282-4F6B-AB59-469DD9AD74EB', '2BADE64B-6B4E-4997-A0D8-2D9BAF4F9E9E'],
  '2043D2AE-F88D-4034-AF04-5909D1319455': ['30B21259-35B7-49DF-81AC-5A95FE902D4B', '6A424BC1-2763-4298-8A59-D61BA6283F7A'],
  'DDF64C8C-0C61-4105-8D01-C82D8FB84A39': ['AE2D06FA-4214-414C-B8EF-4CCE179375E2', '1EAC6346-3E72-455C-B01C-5850643B929B'],
  '9947A01B-3B47-48D3-A3CA-509BEF957515': ['7F93D2A7-F3AF-407F-B5FE-724A1FCC3B50', 'EE3E4675-6CFD-4213-9C17-0C3FD58F9740'],
  'B361C7FE-9356-4C9A-A7E6-58F31F91D121': ['E1307813-915C-436D-82EF-47FEEA23B5FC', 'E8412E1B-843D-4D5B-8C37-022FCDBADF7D'],
  '20C56ECF-47CF-4CA5-ACBE-42F3B457FA47': ['94891E98-3195-43F7-A323-5D1D314C7E91', 'B8DEA678-11D5-4260-99B0-E5ACE5FDEE65'],
  '6F8BB7FE-8234-4E57-89E5-5D996060996E': ['0573D5EB-E267-4CBC-8B48-2AFEC4CFD7D8', 'E14581B0-C47A-4CC0-AC76-45DBEF064BF2'],
  '99C9E482-B90D-4DF0-966B-0C1AFFE1041D': ['AC1365DD-3CF8-485A-9895-654A89589CAC', '64169559-0C0A-48BC-ABB8-AD43FA64E43F'],
  'F778CC4F-330F-4B5E-9649-0097CC7E1020': ['CFC88E77-11D8-4CA6-85EC-A1D3EE8A3E9C', '7E9A8501-049C-40A7-B0D5-3B4E39123BA5'],
  '6E6D8A83-2C1F-4F87-9F9C-45408105D956': ['BE276E9D-B035-4E51-AF00-753B4EEC8F38', '7D563DAD-A123-438C-80E5-61EBE255D38E'],
  'D6D32E34-C799-44AE-9FB2-9B431212C4DC': ['A157D90B-2D04-481A-BE9D-251F02BD4F73', 'EDA580E3-EA18-4BF1-B4FA-72583D0BD632'],
  'D4E7C42C-BDC3-43B5-ACF5-46F65F9A5A03': ['90F6FD88-4802-4AF7-8AE7-65AD6D46E5D1', 'D491AC13-1B2C-4BB0-B759-8262C85970CC'],
  '8155342B-8291-4419-A1EE-57E18C848945': ['F241612D-8354-4670-802B-1BBF4F8FB4A7', 'E7CFD18F-DEDF-432D-9124-5B6B212F34B7'],
  '62FA4AF9-7D21-4129-A3AC-B6ED113B8C7E': ['DC8AA288-3078-46FA-98D9-D2FF5EA9E1E7', 'FBE2703A-05AB-40F4-83EC-5E62F9D5D64B'],
  '85F00FD8-2D18-4EA9-81EF-9A564632D1D7': ['7436876D-F778-486F-972F-6E8DEE24AE1F', '78BA439F-21E3-4997-BE5C-B4422B6929F6'],
  'CEDCB7B7-E5D4-46CC-9CEB-FC2FCE2CF28D': ['B4EAAAA6-DD35-4B34-86B0-D18460F7F8D3', '0299B3F3-B574-4137-B7B9-49BD95F3BF1A'],
  '839F64F4-B1A3-49B2-99E0-ACD836AAD721': ['E70877B0-2B3B-487E-A4A7-316931C21FF9', '6C1F16BA-C1E7-40FC-A0CF-D6530D129BE7'],
  'AECEF226-E5A4-45E1-8FFE-9C871A20FA4D': ['0352AF22-17C5-4085-B21A-4841A8AA4132', '7DA5A14E-D9FE-4AA3-A8B7-426152C0CBF9'],
  '9FCF396D-05DF-4287-B9C6-6831EA0458E3': ['ADE296C7-66DC-48B3-A67E-EA54FB273145', '759A6C53-D604-4C4B-910F-898BB7FA52A0'],
  'D906C000-E5D4-4136-AAC6-7BE7E7CA0393': ['0C64D3CD-685F-4CD8-90D2-4FE34EA979BC', '634D87EC-D490-4BA5-8F99-83A6281E1B22'],
  'E4906E60-E527-486A-9773-9088D75EF79B': ['774CF89E-D491-4D7F-B9C7-4FE663B79B1A', '17552C1B-59E0-41C7-B6BD-E6059B05B666'],
  '4235E7F7-B866-4C1E-831F-D13DBFD74FC2': ['DDF29ED8-104B-4007-8103-5DC8AEC6FF72', '673B59AB-EC52-4578-97AD-5AD8C36D92C6'],
  '43577C2D-DBE0-4806-B804-64AE601E2EBC': ['D8446AE4-2BBC-4307-8FFB-7418CEA492B3', 'EF0C5970-FB0D-481A-A54F-AF4964D83CB6'],
  '19FD74E1-28F8-460B-9CCD-047015DA15F1': ['C4229348-223F-40CB-A947-B2CF5AC61374', '85575572-1EC0-4D35-8763-4130F8ACED47'],
  'DF65D67F-8421-43C8-B439-E611FD1EDA96': ['C468E46F-2F9C-4533-A731-DD708518E2B7', 'E9F28FC0-5314-4B3E-A939-D1154229B10E'],
  'DA265A60-CA7D-49F0-9FD9-194F9CF42E7F': ['2EBD0845-055D-460B-A88A-8CBA6487C218', 'E940D91C-CFB1-4451-BE9D-4646257D3F70'],
  '96359D04-323F-4BE7-AA9B-BB741A95A369': ['AD04630E-3AB1-4EA6-A2A9-9721BA61CFAE', '2A02AEC3-2DB8-4593-B2D9-F5836019F6F6'],
  '5E14F570-A540-44B9-9EE4-829815F63139': ['5B1019FB-B19E-42C4-B0F1-2F38058E4F14', '420B7F70-DF84-48D9-8ACE-58699A3AD49E'],
  '641A2A23-7C5C-4E30-AA1F-64F72DE7FBA3': ['3B86829B-68B2-480A-827D-5AA861F02084', 'DE289E32-9DA7-4C83-B79A-6D3D4CCE52DF'],
  'A45828EF-61C5-4C04-B7AD-5240FC5F815E': ['79C9E553-4742-483F-A3A5-2D963EED3E9D', 'F88D7734-BA6E-45D1-A6B8-76E4C2A23095'],
  '15B8C83C-8986-4E05-BC76-0CB6E6568BB6': ['2EF9819A-B227-406A-A986-F77671E3932D', '8AC0DC41-754B-440F-A174-7E0B891077B9'],
  '0D59C506-B9CD-454D-9BDB-0AA4009799BD': ['5B6B07CC-526C-4AA0-9FE2-CFB76442DECC', '4634EAA2-CA1E-41F4-B2FC-93550107A1BC'],
  '18A04EAC-C8A1-45AB-857D-408988684299': ['C3A5AE1F-21DF-46F4-8730-002ED14EA12F', 'B852B1FE-F38A-4FBC-A9E1-F9DF9ECBE34E'],
  'DDBEE140-5EBF-4B08-8C9C-55FE138DD86C': ['53D0CF7C-78C7-41C2-B044-E65FD797BC14', '6EF98F96-832F-4B93-9127-79D367587083'],
  '8E731A92-B8C0-41C9-A357-3C787E2AC284': ['2EBB1E68-9DA2-408E-BA02-4330730AB8EB', '1521986A-1DA7-44B1-9411-BB22E736CF21'],
  '6E85D6CC-FC11-41C4-BBC0-8251F8248598': ['4523E50F-0311-4358-BBF6-609CCD6093E8', '5A4F157E-B15B-4734-87F4-81F361D35745'],
  '1B291C91-6828-4E09-9EB7-913501101EC8': ['5297F129-558A-44F6-98F8-BE0F17BC4080', '788E7BB2-4F86-4A37-B13C-4CA11A271402'],
  '96176A36-4352-47C2-8341-A06D426BEED1': ['9BEF762A-E3CB-497B-9C38-AA8DD6E35C27', 'D3C0FE4E-4763-42D9-85D8-F21596BCA59F'],
  'C80EDD2D-5985-4744-A9B8-52BFA6FBEBDD': ['F85DEA4D-B2B8-4416-A549-A4A4546CA887', 'A6A8BFD5-99F3-45EC-BD05-5071DA6AB6CB']
}

// 单元导入轻量改编模块
export const lightAdaptOptions = [
  {
    module: 'portraitOfGraduateFlag', // 模块键值，向后台请求时使用
    moduleName: $i18n.t('loc.adaptSelectPog'), // 模块名
    description: $i18n.t('loc.adaptSelectPogDes'),// 模块解释
    minGrade: 'K (5-6)', // 模块支持的最小年龄
    maxGrade: 'Grade 12' // 模块支持的最大年龄
  },
  {
    module: 'teacherGuideFlag',
    moduleName: $i18n.t('loc.adaptSelectTeacherGuide'),
    description: $i18n.t('loc.adaptSelectTeacherGuideDes'),
    minGrade: 'Infant (0-1)',
    maxGrade: 'Grade 12'
  },
  {
    module: 'standardsAssessmentFlag',
    moduleName: $i18n.t('loc.adaptSelectAssessment'),
    description: $i18n.t('loc.adaptSelectAssessmentDes'),
    minGrade: 'TK (4-5)',
    maxGrade: 'Grade 12'
  },
  {
    module: 'eduProtocolsTemplatesFlag',
    moduleName: $i18n.t('loc.adaptSelectEduProtocols'),
    description: $i18n.t('loc.adaptSelectEduProtocolsDes'),
    minGrade: 'K (5-6)',
    maxGrade: 'Grade 12'
  },
  {
    module: 'lectureSlidesFlag',
    moduleName: $i18n.t('loc.adaptSelectSlides'),
    description: $i18n.t('loc.adaptSelectSlidesDes'),
    minGrade: 'TK (4-5)',
    maxGrade: 'Grade 12'
  },
  {
    module: 'resourceUpgradeFlag',
    moduleName: $i18n.t('loc.adaptSelectResourceUpgrade'),
    description: $i18n.t('loc.adaptSelectResourceUpgradeDes'),
    minGrade: 'Infant (0-1)',
    maxGrade: 'Grade 12'
  }
]
