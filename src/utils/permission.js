/*
  登录许可权限
*/
import router from '@/router'
import store from '@/store'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'// progress bar style
import { isAuthenticated, getCurrentUser } from '@/utils/common' // getToken from cookie

import {getPlatform, pathName} from './setBaseUrl'
import { platform } from './setBaseUrl'
import tools from './tools'
import { LESSON_PLAN_NEW_USER_UTC } from './const'
// 定义白名单
const whiteMap = new Map([
  // magic-curriculum 平台
  ['MAGIC-CURRICULUM', ['magicHome','magicContest','magicLessonDetail','magicUnitDetail','observation-label','observation-score-select']],
  // curriculum-plugin 平台
  ['CURRICULUM-PLUGIN', [/*'unit-planner-cg','unit-planner'*/]]
])
// 判断cn/us
const isChina = store.getters.isChina

NProgress.configure({ showSpinner: false })// NProgress Configuration

/**
 * 保存从邮箱跳转到平台的参数
 * @param {Object} query - 路由查询参数
 */
const saveToCGParams = (query) => {
  if (Object.keys(query).length > 0) {
    // 值存到浏览器缓存， 登录后再读取
    localStorage.setItem('TO_CG_DATA', JSON.stringify(query))
  }
}

router.beforeEach(function (to, from, next) {
  NProgress.start() // start progress bar
  if (to.matched.some(record => record.meta.verifySession) && isAuthenticated()) {
    // 如果当前时间大于 LESSON_PLAN_NEW_USER_UTC，则跳转到 Lesson Plan 页面
    const curUser = getCurrentUser()
    if (curUser.userInfo && tools.timeIsAfter(curUser.userInfo.createdAtUtc, LESSON_PLAN_NEW_USER_UTC)) {
      next({ path: '/lessons' })
    } else {
      next({ path: '/curriculum-genie/unit-planner' })
    }
    NProgress.done()
    return
  }
  if (to.matched.some(record => record.meta.requireAuth)) {
    // 路由元信息requireAuth:true则不做登录校验
    next()
    store.dispatch('setShowIconAdvterAction',false)
    store.dispatch('setShowIconAdvterAction0',false)
  } else {
    let fromH5 = false
    if (to && to.query && to.query.source && to.query.source === 'H5') {
      fromH5 = true
    }
    // 如果是 Curriculum Genie 平台，对登录页和默认跳转页面进行修改
    if (platform && platform === 'CURRICULUM-GENIE') {
      if (isAuthenticated()) {
        // 打开根路径，跳转到单元列表
        if (to.path == '/' || to.path == '/admin/custom_portfolio_tags') {
          // 如果当前时间大于 LESSON_PLAN_NEW_USER_UTC，则跳转到 Lesson Plan 页面
          const curUser = getCurrentUser()
          if (curUser.userInfo && tools.timeIsAfter(curUser.userInfo.createdAtUtc, LESSON_PLAN_NEW_USER_UTC)) {
            next({ path: '/lessons' })
          } else {
            next({ path: '/curriculum-genie/unit-planner' })
          }
        } else {
          // 其他路径，直接跳转
          next()
        }
      } else {
        // 如果未登录，则跳转到登录页面
        next({ path: '/curriculum-genie/login' })
      }
      NProgress.done()
      document.title = 'Curriculum Genie'
      return
    }
    // 如果是 magic curriculum 平台，对登录页和默认跳转页面进行修改
    // todo 后续看怎么修改
    if (platform && (platform === 'MAGIC-CURRICULUM' || platform === 'CURRICULUM-PLUGIN')) {
      // 已登录或者白名单，正常跳转
      const whiteList = whiteMap.get(platform);
      if (isAuthenticated() || whiteList.includes(to.name)) {
          // 其他路径，直接跳转
          next()
      } else {
          // 保存链接携带数据
          saveToCGParams(to.query)
          // 如果未登录，则跳转到 Contest 首页
          next({ path: platform === 'CURRICULUM-PLUGIN' ? '/login' : '/magic-curriculum/contest' })
      }
      document.title = 'Curriculum Genie'
      NProgress.done()
      return
    }
    if (isAuthenticated() || fromH5) { // 判断用户
      if (to.path == '/login') {

        next({ path: '/' })
        NProgress.done()
      } else {
        if (to.meta.init) {
          let iconSrc = !isChina ? '/favicon.ico' : '/favicon.plg.ico'
          let title = !isChina ? 'Learning Genie' : 'PLG - 幼儿评估发展系统'
          document.getElementById('faviconIcon').setAttribute('href', iconSrc)
          document.title = title
        }
        if (to.meta.title) {
          // 如果设置标题，拦截后设置标题
          document.title = to.meta.title
        } else {
          next()
        }
      }
    } else {
      // next(`/login?redirect=${to.path}`) // 否则全部重定向到登录页   本地的
      // 线上的
      next()
      window.location.href = pathName + '/#/login'
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
