'use strict'

import store from '@/store'
import axios from 'axios'
import configBaseUrl from './setBaseUrl'
import {platform} from './setBaseUrl'
import tools from './tools'
import { cgApiUrl } from './setBaseUrl'
import { toSignOut } from '@/utils/autoSignOut'

let config = {
  baseURL: configBaseUrl,
  method: 'get',
  // 请求头信息
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  // 参数
  data: {},
  // 设置超时时间
  timeout: 200000,
  // Check cross-site Access-Control
  withCredentials: false,
  // 返回数据类型
  responseType: 'json'
}

const _axios = axios.create(config)
const _cgAxios = axios.create(config)

const addInterceptors = (axiosInstance, isCG) => {
  axiosInstance.interceptors.request.use(
    function (config) {
      // 根据平台设置不同的 BaseURL
      config.baseURL = isCG ? cgApiUrl : configBaseUrl
      // 带上 token , 可以结合 vuex 或者重 localStorage
      if (store.getters.currentUser && !isCG) {
        config.headers['X-Center-Id'] = store.state.user.centerId
        config.headers['X-LG-Language'] = tools.localItem('NG_TRANSLATE_LANG_KEY')
        config.headers['X-LG-Platform'] = 'web'
        config.headers['X-UID'] = store.state.user.uid
        config.headers['X-LG-Token'] = store.state.user.token
        config.headers['X-LG-TimezoneOffset'] = -(new Date().getTimezoneOffset() / 60)
      }
      // 只要 platform 不为空，就带上 platform
      if (platform && platform !== '${platform}' && !isCG) {
        config.headers['X-Project'] = platform
        config.headers['X-UID'] = store.state.user.uid ? store.state.user.uid : store.state.magicCurriculum.userId
      }
      // 如果是 Curriculum Genie，更新认证信息
      if (isCG) {
        // 从 cookie 获取 token
        const token = tools.getCookie('cg_session')
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`
        }
      }
      return config
    },
    function (error) {
      // 请求错误时做些事(接口错误、超时等)
      return Promise.reject(error)
    }
  )
  
  // Add a response interceptor
  axiosInstance.interceptors.response.use(
    function (response) {
      let data
      // IE9 时 response.data 是 undefined，因此需要使用 response.request.responseText (Stringify 后的字符串)
      if (response.data === undefined || response.data === '') {
        data = JSON.parse(response.request.responseText)
      } else {
        data = response.data
      }
      return data
    },
    function (err) {
      if (err && err.response) {
        switch (err.response.status) {
          case 400:
            err.message = $i18n.t('loc.badRequest') // '请求错误'
            break
  
          case 401:
            err.message = $i18n.t('loc.logonFailure') // '用户错误'
            // 跳转到当前路径的登录界面
            window.parent.postMessage('COME_BACK_TO_LOGIN', '*')
            toSignOut()
            break
  
          case 403:
            err.message = $i18n.t('loc.accessDenied') // '拒绝访问'
            break
  
          case 404:
            err.message = $i18n.t('loc.requestAddressError') + ':' + err.response.config.url // `请求地址出错: ${err.response.config.url}`
            break
  
          case 408:
            err.message = $i18n.t('loc.requestTimeout') // '请求超时'
            break
  
          case 500:
            err.message = $i18n.t('loc.serverInternalError') // '服务器内部错误'
            break
  
          case 501:
            err.message = $i18n.t('loc.serviceNotImplemented') // '服务未实现'
            break
  
          case 502:
            err.message = $i18n.t('loc.badGateway') // '网关错误'
            break
  
          case 503:
            err.message = $i18n.t('loc.serviceUnavailable') // '服务不可用'
            break
  
          case 504:
            err.message = $i18n.t('loc.gatewayTimeout') // '网关超时'
            break
  
          case 505:
            err.message = $i18n.t('loc.httpNotSupport') // 'HTTP版本不受支持'
            break
  
          default:
        }
      }
      store.dispatch('setIsLoadingAction', false)
      if (err.response) {
        if (err.response.data) {
          err.message = err.response.data.error_message
        }
      }
      return Promise.reject(err)
    }
  )
}

// 应用拦截器
addInterceptors(_axios, false)
addInterceptors(_cgAxios, true)

window.$axios = _axios
export default _axios

window.$cgAxios = _cgAxios
export { _cgAxios }
