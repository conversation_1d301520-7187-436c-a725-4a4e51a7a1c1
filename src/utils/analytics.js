import { localEnv, sendActivityEventUrl } from '@/utils/setBaseUrl'
import axios from 'axios'
import moment from 'moment'
import { equalsIgnoreCase, getCurrentUser } from '@/utils/common'
import tools from './tools'
import { datadogRum } from '@datadog/browser-rum'

// 调查问卷 ID
const surveyIdMap = {
  'PCG_CSAT_NPS': '0196b3a4-2ed7-0000-080d-cbc8b5e8557a',
  'ADAPT_IMPORT_UNIT': '01985e36-4390-0000-709c-e5f94742011d',
}

// 数据分析
export default {
  // 设置用户信息
  setUserProfile (user) {
    try {
      var agencyId = user.default_agency_id // 机构 ID
      var userId = user.user_id // 用户 ID
      var userRole = user.role2 // 用户角色

      // 设置 Google Analytics 用户信息
      gtag('config', 'G-9LSY1Q2T3S', {
        'user_id': userId
      })
      gtag('set', 'user_properties', {
        'agency_id': agencyId,
        'user_id': userId,
        'role': userRole
      })
    } catch (e) {
      console.log('analytics init error!', e)
    }
  },

  // 初始化 Heap
  initHeap (user) {
    try {
      // 没有用户信息则返回
      if (!user) {
        return
      }
      // 已经初始化过则返回
      if (window.heap) {
        return
      }

      var agencyId = user.default_agency_id // 机构 ID
      var agencyName = user.default_agency_name // 机构名称
      var userId = user.user_id // 用户 ID
      var userName = user.display_name // 用户名称
      var userRole = user.role2 // 用户角色
      var userEmail = user.email // 用户邮箱

      // 根据邮箱后缀过滤测试账号
      if (userEmail) {
          var tempUserEmail = userEmail.toLowerCase().trim()
          // 如果是测试邮箱后缀则跳过
          if (tempUserEmail.endsWith('@learning-genie.com') || tempUserEmail.endsWith('@learning-genie-test.com')
              || tempUserEmail.endsWith('@163.com') || tempUserEmail.endsWith('@qq.com')) {
              return
          }
      }

      // APP ID
      var appId
      if (localEnv && localEnv.toLowerCase() === 'prod') {
        appId = '282680219' // 生产环境 APP ID
      } else if (localEnv && localEnv.toLowerCase() === 'test') {
        appId = '898895882' // 测试环境 APP ID
      } else {
        // 其他环境不处理
        return
      }
      // 加载 Heap
      window.heap=window.heap||[],heap.load=function(e,t){window.heap.appid=e,window.heap.config=t=t||{};var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.src="https://cdn.heapanalytics.com/js/heap-"+e+".js";var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(r,a);for(var n=function(e){return function(){heap.push([e].concat(Array.prototype.slice.call(arguments,0)))}},p=["addEventProperties","addUserProperties","clearEventProperties","identify","resetIdentity","removeEventProperty","setEventProperties","track","unsetEventProperty"],o=0;o<p.length;o++)heap[p[o]]=n(p[o])};
      heap.load(appId);
      // 设置 Heap 用户信息
      heap.identify(userEmail);
      heap.addUserProperties({
        'user_id': userId,
        'user_name': userName,
        'user_role': userRole,
        'agency_id': agencyId,
        'agency_name': agencyName
      });
    } catch (e) {
      console.log('Init heap error!', e)
    }
  },

  // 初始化 PostHog
  initPostHog (user) {
    try {
      // 没有用户信息则返回
      if (!user) {
        return
      }
      // 已经初始化过则返回
      // if (window.posthog) {
      //   return
      // }

      let apiKey = ''
      if (localEnv && localEnv.toLowerCase().includes('prod')) {
        apiKey = 'phc_h6jjapPCV2g85hambnsNiB1Au1Z62UsylgiWVnRWgvA'
      } else if (localEnv && (localEnv.toLowerCase().includes('test') || localEnv.toLowerCase().includes('stage'))) {
        apiKey = 'phc_h6jjapPCV2g85hambnsNiB1Au1Z62UsylgiWVnRWgvA'
      } else {
        // 其他环境不处理
        return
      }

      var agencyId = user.default_agency_id // 机构 ID
      var agencyName = user.default_agency_name // 机构名称
      var userId = user.user_id // 用户 ID
      var userName = user.display_name // 用户名称
      var userRole = user.role2 // 用户角色
      var userEmail = user.email // 用户邮箱

      // 根据邮箱后缀过滤测试账号
      if (userEmail) {
        var tempUserEmail = userEmail.toLowerCase().trim()
        // 如果是测试邮箱后缀则跳过
        if (tempUserEmail.endsWith('@learning-genie-test.com')
            || tempUserEmail.endsWith('@163.com') || tempUserEmail.endsWith('@qq.com')) {
            return
        }
      }

      // 初始化 PostHog 用户信息
      posthog.identify(
        userEmail, // Required. Replace 'distinct_id' with your user's unique identifier
        { email: userEmail, userId: userId, userName: userName, role: userRole, agencyId: agencyId, agencyName: agencyName }  // $set, optional
      )
    } catch (e) {
      console.log('Init postHog error!', e)
    }
  },

  initDatadogRum () {
    try {
      // 环境
      const env = localEnv.replace('magic-curriculum-', '')
      // 初始化 Datadog RUM
      datadogRum.init({
          applicationId: '2681772a-1133-46a4-8662-1aacd8a53e67',
          clientToken: 'pubda35dfa3c9c10a42da56e17886d27358',
          site: 'datadoghq.com',
          service: 'cg-rum',
          env: env,
          // version: '1.0.0',
          sessionSampleRate: 100,
          sessionReplaySampleRate: 100,
          defaultPrivacyLevel: 'mask-user-input',
          trackViewsManually: true
      })
    } catch (e) {
      console.log('Init datadogRum error!', e)
    }
  },

  // 手动追踪 Vue 路由变化
  trackRouteChange (viewName) {
    try {
      // 开始追踪路由变化
      datadogRum.startView({
        name: viewName || 'Unknown Route'
      })
    } catch (e) {
      console.log('Track route change error!', e)
    }
  },

  setDatadogRumUser (user) {
    if (!user) {
      return
    }
    try {
      // 设置用户信息
      var agencyId = user.default_agency_id // 机构 ID
      var agencyName = user.default_agency_name // 机构名称
      var userId = user.user_id // 用户 ID
      var userName = user.display_name // 用户名称
      var userRole = user.role2 // 用户角色
      var userEmail = user.email // 用户邮箱
      datadogRum.setUser({
        id: userId,
        email: userEmail,
        name: userName,
        role: userRole,
        agencyId: agencyId,
        agencyName: agencyName
      })
    } catch (e) {
      console.log('Set datadogRum user error!', e)
    }
  },

  // 获取 PostHog 指定 flag
  getFeatureFlag (name) {
    try {
      if (!window.posthog) {
        return null
      }
      return window.posthog.getFeatureFlag(name)
    } catch (e) {
      return null
    }
  },

  // 获取 PostHog 指定 flag 异步
  getFeatureFlagAsync (name) {
    return new Promise((resolve, reject) => {
      try {
        if (!posthog) {
          resolve(null)
          return
        }
        // 使用 onFeatureFlags 确保特性标志已加载
        posthog.onFeatureFlags(function(flags) {
          resolve(posthog.getFeatureFlag(name))
        })
      } catch (e) {
        resolve(null)
      }
    })
  },

  // 发送事件
  sendEvent (name, params) {
    try {
      let userInfo = getCurrentUser()
      if (userInfo && tools.checkEmailForTest(userInfo.email)) {
        return
      }
      gtag('event', name, params)
      // 发送 PostHog 事件
      posthog.capture(name, params)
    } catch (e) {
      console.log('analytics event error!', e)
    }
  },

  /**
   * 根据 Id 获取 Posthog 调查问卷
   *
   * @param key 调查问卷匿名键值
   * @param forceReload 是否强制重新刷新缓存
   * @returns 调查问卷
   */
  async getPosthogSurvey(key, forceReload) {
    const surveyId = surveyIdMap[key]
    if (!surveyId) return null
    try {
      return new Promise((resolve) => {
        posthog.getSurveys((surveys) => {
          let survey = null
          if (surveys && surveys.length > 0) {
            survey = surveys.find(survey => equalsIgnoreCase(survey.id, surveyId))
          }
          resolve(survey)
        }, forceReload)
      })
    } catch (error) {
      console.error('Failed to get Posthog survey:', error)
      return null
    }
  },

  /**
   * 显示 Posthog 调查问卷
   * @param surveyId 调查问卷 ID
   */
  showPosthogSurvey(surveyId) {
    try {
      posthog.capture("survey shown", {
          $survey_id: surveyId
      })
    } catch (error) {
      console.error('Failed to show Posthog survey:', error)
    }
  },

  /**
   * 隐藏 Posthog 调查问卷
   * @param surveyId 调查问卷 ID
   */
  dismissPosthogSurvey(surveyId) {
    try {
      posthog.capture("survey dismissed", {
          $survey_id: surveyId
      })
    } catch (error) {
      console.error('Failed to dismiss Posthog survey:', error)
    }
  },

  /**
   * 发送 Posthog 调查问卷请求
   * @param data 请求数据
   */
  sendPosthogSurvey(data) {
    try {
      posthog.capture('survey sent', data)
    } catch (error) {
      console.error('Failed to send Posthog survey:', error)
    }
  },

  // 发送事件
  sendCRMEvent (name, centerId, groupId) {
    try {
      let userInfo = getCurrentUser()
      if (userInfo == undefined) {
        return
      }
      let url = analyticsCRMEventUrl
      let date = moment(new Date()).format('YYYYMMDD')
      axios.post(url, {
          'agency_id': userInfo.default_agency_id,
          'user_id': userInfo.user_id,
          'center_id': centerId,
          'group_id': groupId,
          'event_name': name,
          'event_date': date,
          'role': userInfo.role2
      })
    } catch (e) {
      console.log('analytics crm event error!', e)
    }
  },

  // 发送活跃事件
  sendActivityEvent (module, event, eventCount, childIds, classIds, centerIds) {
    try {
      let userInfo = getCurrentUser()
      if (userInfo == undefined) {
        return
      }
      axios.post(sendActivityEventUrl + '/sendActivityEvent', {
          'agency_id': userInfo.default_agency_id,
          'user_id': userInfo.user_id,
          'center_ids': centerIds,
          'class_ids': classIds,
          'child_ids': childIds,
          'module': module,
          'event': event,
          'event_count': eventCount,
          'user_role': userInfo.role2,
          'timezone_offset': -(new Date().getTimezoneOffset() / 60),
      })
    } catch (e) {
      console.log('send activity event error!', e)
    }
  }
}
