/**
 * 简化的路由预加载器
 * 提供基本的路由预加载功能
 */

class RoutePreloader {
  constructor() {
    this.preloadedRoutes = new Set()
    
    /**
     * 路由组件映射
     */
    this.routeChunkMap = {
      'lessons-editor': () => import(
        /* webpackChunkName: "lessons-editor" */
        '@/views/modules/lesson2/lessonLibrary/editor'
      ),
      'unit-editor': () => import(
        /* webpackChunkName: "unit-editor" */
        '@/views/modules/lesson2/unitPlanner/UnitEditor.vue'
      ),
      'unit-detail-cg': () => import(
        /* webpackChunkName: "unit-detail" */
        '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetail.vue'
      ),
      'unit-creator-cg': () => import(
        /* webpackChunkName: "unit-creator" */
        '@/views/modules/lesson2/unitPlanner/components/editor/UnitOverviewStep.vue'
      ),
      'load-unit': () => import(
        /* webpackChunkName: "curriculum" */
        '@/views/modules/lesson2/unitPlanner/components/editor/LoadUnit.vue'
      )
    }

    /**
     * 路由预测映射 - 根据当前路由预测用户可能访问的路由
     */
    this.routePredictions = {
      'cg-login': ['unit-creator-cg', 'lessons-editor'],
      'unit-planner-cg': ['unit-creator-cg', 'unit-editor', 'load-unit', 'unit-detail-cg'],
      'TeacherLessonList': ['lessons-editor'],
      'unit-planner': ['unit-editor', 'load-unit', 'unit-detail-cg'],
      'lesson-library': ['lessons-editor']
    }
  }

  /**
   * 预加载单个路由
   * @param {string} routeName - 路由名称
   */
  async preload(routeName) {
    if (this.preloadedRoutes.has(routeName)) {
      return // 已经预加载过了
    }

    const loadRoute = this.routeChunkMap[routeName]
    if (!loadRoute) {
      // console.warn(`⚠️ Route not found: ${routeName}`)
      return
    }

    try {
      await loadRoute()
      this.preloadedRoutes.add(routeName)
      // console.log(`✅ Preloaded route: ${routeName}`)
    } catch (error) {
      // console.warn(`⚠️ Failed to preload route: ${routeName}`, error)
    }
  }

  /**
   * 批量预加载路由
   * @param {Array} routeNames - 路由名称数组
   */
  async preloadMultiple(routeNames) {
    const promises = routeNames.map(routeName => this.preload(routeName))
    await Promise.allSettled(promises)
  }

  /**
   * 根据当前路由名称预加载相关路由
   * @param {string} currentRouteName - 当前路由名称
   * @param {number} [delay] - 延迟时间（毫秒）
   */
  preloadByRoute(currentRouteName, delay) {
    // console.log('currentRouteName', currentRouteName)
    const relatedRoutes = this.routePredictions[currentRouteName]
    
    if (!relatedRoutes || relatedRoutes.length === 0) {
      // console.log(`📝 No related routes found for: ${currentRouteName}`)
      return
    }

    // console.log(`🔄 Preloading routes for ${currentRouteName}:`, relatedRoutes)
    
    if (delay) {
      setTimeout(() => {
        this.preloadMultiple(relatedRoutes)
      }, delay)
    } else {
      this.preloadMultiple(relatedRoutes)
    }
  }

  /**
   * 检查路由是否已预加载
   * @param {string} routeName - 路由名称
   */
  isPreloaded(routeName) {
    return this.preloadedRoutes.has(routeName)
  }

  /**
   * 清理预加载状态
   */
  clear() {
    this.preloadedRoutes.clear()
  }
}

// 创建全局实例
const routePreloader = new RoutePreloader()

export default routePreloader 