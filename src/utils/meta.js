// 随机的描述列表
const descriptions = [
  'Just created a unit plan with Curriculum Genie’s Unit Planner. ⚡Fast, simple, and hassle-free! If you’re looking to streamline your planning, it could be a helpful tool to explore.\n',
]

// 随机的图片列表
const images = [
  'https://s3.us-east-1.amazonaws.com/com.learning-genie.cdn/cg/images/social_share_01.png',
  'https://s3.us-east-1.amazonaws.com/com.learning-genie.cdn/cg/images/social_share_02.png',
]

/**
 * 随机选择一个描述
 */
export const getRandomDescription = () => {
  return descriptions[Math.floor(Math.random() * descriptions.length)]
}

/**
 * 随机选择一个图片
 */
export const getRandomImage = () => {
  return images[Math.floor(Math.random() * images.length)]
}

export const defaultMeta = {
  twitter: {
    card: 'summary_large_image',
    site: '@LearningGenie',
    title: 'Curriculum Genie',
    image: getRandomImage(),
  },
  // Facebook Open Graph
  og: {
    type: 'website',
    title: 'Curriculum Genie',
    image: getRandomImage(),
    siteName: 'Curriculum Genie',
  },
  // LinkedIn 特定的 meta（大部分会使用 og 标签）
  linkedin: {
    title: 'Curriculum Genie',
    image: getRandomImage(),
  }
}

export const getTwitterMeta = (options) => {
  const meta = { ...defaultMeta.twitter, ...options }
  
  return [
    { name: 'twitter:card', content: meta.card },
    { name: 'twitter:site', content: meta.site },
    { name: 'twitter:title', content: meta.title },
    { name: 'twitter:description', content: getRandomDescription() },
    { name: 'twitter:image', content: meta.image },
  ]
}

export const getOpenGraphMeta = (options) => {
  const meta = { ...defaultMeta.og, ...options }
  
  return [
    { property: 'og:type', content: meta.type },
    { property: 'og:title', content: meta.title },
    { property: 'og:description', content: getRandomDescription() },
    { property: 'og:image', content: meta.image },
    // { property: 'og:url', content: meta.url },
    { property: 'og:site_name', content: meta.siteName },
  ]
}

export const getLinkedInMeta = (options) => {
  const meta = { ...defaultMeta.linkedin, ...options }
  
  return [
    { property: 'linkedin:title', content: meta.title },
    { property: 'linkedin:description', content: getRandomDescription() },
    { property: 'linkedin:image', content: meta.image },
  ]
}

/**
 * 获取iframe预加载的link标签
 * @param url iframe的URL
 * @returns link标签对象
 */
export const getIframePreloadMeta = (url, as = 'document') => {
  try {
    if (!url) {
      return []
    }
    
    return [
      { rel: 'preload', href: url, as }
    ]
  } catch (error) {
    return []
  }
}

/**
 * 获取DNS预解析的link标签
 * @param domains 需要预解析的域名数组
 * @returns link标签对象数组
 */
export const getDNSPrefetchMeta = (domains) => {
  try {
    if (!domains || domains.length === 0) {
      return []
    }
    
    return domains.map(domain => ({ 
      rel: 'dns-prefetch', 
      href: domain 
    }))
  } catch (error) {
    return []
  }
}

/**
 * 获取预连接的link标签
 * @param urls 需要预连接的URL数组
 * @param crossOrigin 是否添加crossorigin属性
 * @returns link标签对象数组
 */
export const getPreconnectMeta = (urls, crossOrigin = true) => {
  try {
    if (!urls || urls.length === 0) {
      return []
    }
    
    return urls.map(url => ({ 
      rel: 'preconnect', 
      href: url,
      ...(crossOrigin ? { crossOrigin: 'anonymous' } : {})
    }))
  } catch (error) {
    return []
  }
}

// 获取所有社交媒体的 meta 标签
export const getAllSocialMeta = (options) => {
  return [
    ...getTwitterMeta(options.twitter),
    ...getOpenGraphMeta(options.og),
    ...getLinkedInMeta(options.linkedin),
  ]
} 
