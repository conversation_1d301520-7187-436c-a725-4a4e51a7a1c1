import i18n from './i18n' // 引入 i18n 实例

// 页面无操作 25 分钟, 出现一个超时未操作弹窗
export const INACTIVITY_TIME_OUT = 25 * 60 * 1000

// 弹窗出现后不操作自动退出时间
export const TO_SIGN_OUT_NUM = 5 * 60 * 1000

export const LAST_NOTIFY_TIME = 'lastNotifyTime' // 上次 notification 接口请求时间
export const LAST_OPERATION_TIME = 'lastOperationTime' // 上次界面操作时间
// 本地存储用户信息 key
export const USER_DATA_KEY = 'current'
// AES 随机密钥 key
export const LG_SECRET_KEY = 'lgSecretKey'
// notification 接口轮询周期
export const LOOP_TIME = 30 * 1000
// lesson plan 新用户判定时间，以 UTC 时间为准
// export const LESSON_PLAN_NEW_USER_UTC = '2025-06-05 08:00:00'
export const LESSON_PLAN_NEW_USER_UTC = '9999-06-05 08:00:00'
// eduprotocols 新用户判定时间，以 UTC 时间为准
export const EDU_PROTOCOLS_NEW_USER_UTC = '2025-06-23 08:00:00'
// 导入单元改编弹窗新用户判定时间，以 UTC 时间为准
export const IMPORT_UNIT_ADAPT_NEW_USER_UTC = '2025-07-31 08:00:00'

// 校训可选年龄段
export const TAILOR_UNIT_AGE_GROUPS = [
  {
    ageGroup: 'Grade 12',
    label: i18n.t('loc.grade_12')
  },
  {
    ageGroup: 'Grade 11',
    label: i18n.t('loc.grade_11')
  },
  {
    ageGroup: 'Grade 10',
    label: i18n.t('loc.grade_10')
  },
  {
    ageGroup: 'Grade 9',
    label: i18n.t('loc.grade_9')
  },
  {
    ageGroup: 'Grade 8',
    label: i18n.t('loc.grade_8')
  },
  {
    ageGroup: 'Grade 7',
    label: i18n.t('loc.grade_7')
  },
  {
    ageGroup: 'Grade 6',
    label: i18n.t('loc.grade_6')
  },
  {
    ageGroup: 'Grade 5',
    label: i18n.t('loc.grade_5')
  },
  {
    ageGroup: 'Grade 4',
    label: i18n.t('loc.grade_4')
  },
  {
    ageGroup: 'Grade 3',
    label: i18n.t('loc.grade_3')
  },
  {
    ageGroup: 'Grade 2',
    label: i18n.t('loc.grade_2')
  },
  {
    ageGroup: 'Grade 1',
    label: i18n.t('loc.grade_1')
  },
  {
    ageGroup: 'K (5-6)',
    label: i18n.t('loc.k_5_6')
  },
  {
    ageGroup: 'Mixed age group',
    label: i18n.t('loc.mixed_age_group')
  }
]

// 校训混合年龄段
export const TAILOR_UNIT_MIXED_AGE_GROUPS = [
  {
    ageGroup: 'Grades 9-12',
    label: 'Grades 9-12'
  },
  {
    ageGroup: 'Grades 6-8',
    label: 'Grades 6-8'
  },
  {
    ageGroup: 'Grades 3-5',
    label: 'Grades 3-5'
  },
  {
    ageGroup: 'K-Grade 2',
    label: 'K-Grade 2'
  }
]

export const IN_KIND_GOALS_ACTIVITY_COLORS = {
  'at-home': '#4aa3df',
  'volunteer': '#8fc64d',
  'mileage': '#e7c04c',
  'donations': '#f58989'
}
/**
 * 活动类型与比例的映射
 * @type {Array<Object>}
 * @property {string} label - 活动类型的本地化标签
 * @property {string} key - result 对象中对应的键名
 * @property {string} id - 唯一标识符（备用）
 */
export const IN_KIND_ACTIVITY_RATE_MAPPING = [
  {
    label: i18n.t('loc.athomeActivites'),
    key: 'atHomeActivityRate',
    id: 'AT_HOME'
  },
  {
    label: i18n.t('loc.volunteerActivites'),
    key: 'volunteerActivityRate',
    id: 'VOLUNTEER'
  },
  {
    label: i18n.t('loc.mileage'),
    key: 'mileageActivityRate',
    id: 'MILEAGE'
  },
  {
    label: i18n.t('loc.donations'),
    key: 'donationActivityRate',
    id: 'DONATION'
  }
]

// 定义常量
export const GRANT_ACTION_CONTINUE = 'continue_donation' // 继续捐赠
export const GRANT_ACTION_REALLOCATE = 'reallocate_donation' // 重新分配捐赠
