import tools from '@/utils/tools'
import store from '@/store'
import moment from 'moment'
import axios from '@/utils/axios'
import CryptoJS from 'crypto-js'
import { LG_SECRET_KEY, USER_DATA_KEY } from './const'

/* get currentAgencies */
export const getCurrentAgencies = () => {
  let agency = (getCurrentUser() || {}).agencies
  return agency
    ? {
        agencies: agency
      }
    : ''
}

/* 获得当前选中的机构（多用于 Grantee 角色） */
export const getSelectedAgency = () => {
  let agency = sessionStorage.getItem('selectedAgency')
  return agency ? JSON.parse(agency) : ''
}
/* 设置当前选中的机构（多用于 Grantee 角色） */
export const setSelectedAgency = (newSelectAgency) => {
  let agency = JSON.stringify(newSelectAgency)
  return sessionStorage.setItem('selectedAgency', agency)
}
const getScretKey = () => {
  return localStorage.getItem(LG_SECRET_KEY)
}
// 加密函数
const encryptData = (data) => {
  // 解密密钥
  let secretKey = getScretKey()
  try {
    // 使用 CryptoJS 对数据进行 AES 加密
    const encrypted = CryptoJS.AES.encrypt(data, secretKey).toString() // 获取加密后的密文字符串
    return encrypted
  } catch (error) {
    // 如果加密失败，返回原始数据
    return data
  }
}

// 解密函数
const decryptData = (data) => {
  // 解密密钥
  let secretKey = getScretKey()
  try {
    let bytes = CryptoJS.AES.decrypt(data, secretKey) // 解密后的字节
    let decrypted = bytes.toString(CryptoJS.enc.Utf8) // 将字节转换成 UTF-8 字符串
    return decrypted
  } catch (error) {
    // 如果解密失败，返回原始数据
    return data
  }
}
// 生成密钥
export const generateKey = () => {
  // 生成一个 16 字节的随机密钥（128 位）
  try {
      var scretKey = CryptoJS.lib.WordArray.random(16)
      localStorage.setItem(LG_SECRET_KEY, scretKey.toString())
  } catch (error) {

  }
}
/* get currentUser from localStorage */
export const getCurrentUser = () => {
  let user = ''
  var currentUser = tools.localItem(USER_DATA_KEY) || ''
  if (currentUser) {
    try {
      let decryptUserData = decryptData(currentUser)
      user = JSON.parse(decryptUserData)
    } catch (e) {
      // 解密失败尝试直接使用原始数据
      try {
        user = JSON.parse(currentUser)
      } catch (e) {
        return null
      }
    }
    if (user && user.userInfo && user.userInfo.lastName) {
      user.userInfo.displayName = user.userInfo.lastName
    }
  }
  return user
}

export const isTeacher = () => {
  const role = store.state.user.currentUser.role2
  var isTeacher = (role.toUpperCase() !== 'AGENCY_ADMIN' && role.toUpperCase() !== 'SITE_ADMIN' && role.toUpperCase() !== 'AGENCY_OWNER')
  return isTeacher
}
/* set currentUser */
export const setCurrentUser = (newUser) => {
  if (!newUser) {
    tools.removeLocalItem('currentUser')
    tools.removeLocalItem(USER_DATA_KEY)
    tools.removeSessionItem('currentAgencies')
    tools.removeLocalItem('currentUserInfo')
    tools.removeLocalItem(LG_SECRET_KEY)
    return
  }
  let user = Object.assign({}, newUser)
  user.group_stages = []
  localStorage.setItem(USER_DATA_KEY, encryptData(JSON.stringify(user)))
}
/* delete currentUser */
export const removeCurrentUser = () => {
  return tools.removeLocalItem(USER_DATA_KEY)
}
/* the currentUser is login or not */
export const isAuthenticated = () => {
  if (getCurrentUser() && getCurrentUser() != 'null' 
      && localStorage.getItem('cg_user') 
      && localStorage.getItem('cg_user') != 'null'
      && tools.getCookie('cg_session')
      && tools.getCookie('cg_session') !== ''
    ) {
    return true
  }
  return false
}
/* get http status for ablum */
export const getHttpStatus = () => {
  try {
    return tools.localItem('httpStatus')
  } catch (e) {
    return ''
  }
}
/* set http status for ablum */
export const setHttpStatus = (stateData) => {
  var HA = getHttpStatus(); var state
  if (HA) {
    state = 'hs' + (stateData.portal ? stateData.portal : HA.substr(2, 1)) + (stateData.album ? stateData.album : HA.substr(3, 1))
  } else {
    state = 'hs' + (stateData.portal ? stateData.portal : '0') + (stateData.album ? stateData.album : '0')
  }
  tools.localItem('httpStatus', state)
}
/* across role */
export const acrossRole = (...values) => {
  if (!values.length) {
    return false
  }
  if (!isAuthenticated()) {
    return
  }
  let role = getCurrentUser().role2.toUpperCase()
  for (let val of values) {
    if (val.toUpperCase() === role) {
      return true
    }
  }
}

export const dateFormat = () => {
  let language = store.state.user.language
  let format = 'MM/dd/yyyy'
  switch (language) {
    case 'zh-CN':
      format = 'yyyy/MM/dd'
      break
    default:
      break
  }
  return format
}

export const toDecimal = (value) => {
  return Math.round(value * 100) / 100
}

export const getFileSize = (size) => {
  var result = ''
  size = parseInt(size)
  if (isNaN(size)) {
    result = 0 + 'K'
  } else if (size < 1024 * 500) {
    result = toDecimal(size / 1024) + 'K'
  } else {
    result = toDecimal(size / (1024 * 1024)) + 'M'
  }
  return result
}

export const minPeriodDays = 15
export const maxPeriodDays = 300
// from underscore isEqual
export const deepEqual = (a, b) => {
  let isFunction, has, eq, deepEq
  isFunction = function (obj) {
    return typeof obj === 'function' || false
  }
  has = function (obj, path) {
    return obj != null && Object.prototype.hasOwnProperty.call(obj, path)
  }
  eq = function (a, b, aStack, bStack) {
    // Identical objects are equal. `0 === -0`, but they aren't identical.
    // See the [Harmony `egal` proposal](http://wiki.ecmascript.org/doku.php?id=harmony:egal).
    if (a === b) return a !== 0 || 1 / a === 1 / b
    // `null` or `undefined` only equal to itself (strict comparison).
    if (a == null || b == null) return false
    // `NaN`s are equivalent, but non-reflexive.
    if (a !== a) return b !== b
    // Exhaust primitive checks
    let type = typeof a
    if (type !== 'function' && type !== 'object' && typeof b != 'object') return false
    return deepEq(a, b, aStack, bStack)
  }

  // Internal recursive comparison function for `isEqual`.
  deepEq = function (a, b, aStack, bStack) {
    // Compare `[[Class]]` names.
    let className = Object.prototype.toString.call(a)
    if (className !== Object.prototype.toString.call(b)) return false
    switch (className) {
      // Strings, numbers, regular expressions, dates, and booleans are compared by value.
      case '[object RegExp]':
      // RegExps are coerced to strings for comparison (Note: '' + /a/i === '/a/i')
      case '[object String]':
        // Primitives and their corresponding object wrappers are equivalent; thus, `"5"` is
        // equivalent to `new String("5")`.
        return '' + a === '' + b
      case '[object Number]':
        // `NaN`s are equivalent, but non-reflexive.
        // Object(NaN) is equivalent to NaN.
        if (+a !== +a) return +b !== +b
        // An `egal` comparison is performed for other numeric values.
        return +a === 0 ? 1 / +a === 1 / b : +a === +b
      case '[object Date]':
      case '[object Boolean]':
        // Coerce dates and booleans to numeric primitive values. Dates are compared by their
        // millisecond representations. Note that invalid dates with millisecond representations
        // of `NaN` are not equivalent.
        return +a === +b
      case '[object Symbol]':
        return Symbol.prototype.valueOf.call(a) === Symbol.prototype.valueOf.call(b)
    }

    let areArrays = className === '[object Array]'
    if (!areArrays) {
      if (typeof a != 'object' || typeof b != 'object') return false

      // Objects with different constructors are not equivalent, but `Object`s or `Array`s
      // from different frames are.
      let aCtor = a.constructor; let bCtor = b.constructor
      if (aCtor !== bCtor && !(isFunction(aCtor) && aCtor instanceof aCtor &&
        isFunction(bCtor) && bCtor instanceof bCtor) &&
        ('constructor' in a && 'constructor' in b)) {
        return false
      }
    }
    // Assume equality for cyclic structures. The algorithm for detecting cyclic
    // structures is adapted from ES 5.1 section 15.12.3, abstract operation `JO`.

    // Initializing stack of traversed objects.
    // It's done here since we only need them for objects and arrays comparison.
    aStack = aStack || []
    bStack = bStack || []
    let length = aStack.length
    while (length--) {
      // Linear search. Performance is inversely proportional to the number of
      // unique nested structures.
      if (aStack[length] === a) return bStack[length] === b
    }

    // Add the first object to the stack of traversed objects.
    aStack.push(a)
    bStack.push(b)

    // Recursively compare objects and arrays.
    if (areArrays) {
      // Compare array lengths to determine if a deep comparison is necessary.
      length = a.length
      if (length !== b.length) return false
      // Deep compare the contents, ignoring non-numeric properties.
      while (length--) {
        if (!eq(a[length], b[length], aStack, bStack)) return false
      }
    } else {
      // Deep compare objects.
      let keys = Object.keys(a); let key
      length = keys.length
      // Ensure that both objects contain the same number of properties before comparing deep equality.
      if (Object.keys(b).length !== length) return false
      while (length--) {
        // Deep compare each member
        key = keys[length]
        if (!(has(b, key) && eq(a[key], b[key], aStack, bStack))) return false
      }
    }
    // Remove the first object from the stack of traversed objects.
    aStack.pop()
    bStack.pop()
    return true
  }

  // Perform a deep comparison to check if two objects are equal.
  return eq(a, b)
}
export const timeFormatWithoutSeconds = () => {
  let language = store.state.user.language
  let format = 'hh:mm A'
  switch (language) {
    case 'zh-CN':
      format = 'HH:mm'
      break
    default:
      break
  }
  return format
}
export const filterUrl = (content) => {
    if (content) {
      let reg = /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi
      content = content.replace(reg, (match) => {
        let href = match
        if (match.indexOf('http') === -1) {
          href = 'http://' + match
        }
        return "<a class='inkind-link' style='color: #10b3b7;' target=\"_blank\" href=\"" + href + '">' + match + '</a>'
      })
    }
    return content
}

// 生成视频 s3桶Key
export const generateVideoKey = () => {
  let t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'

let randomString = ''
  for (let i = 0; i < 6; i++) randomString += t.charAt(Math.floor(Math.random() * t.length))
  return 'videos/' + moment().utc().format('YYYYMMDDHHmmss') + '-' + randomString
}

/* 获得当前选中的学校 */
export const getSelectedSite = () => {
  // 从本地获取进入睡眠检查或 DLL 的方式
  let skipType = getSkipTypeValue()
  let site
  // 从 home 点击班级进入睡眠检查或 DLL
  if (skipType.toUpperCase() === 'HOME') {
    site = sessionStorage.getItem('selectedSite')
  }
  // 从侧边栏进入 DLL ，从本地获取保存的学校 ID
  if (skipType.toUpperCase() === 'DLL') {
    site = JSON.stringify(localStorage.getItem(getDllSelectedCenterIdKey()))
  }
  // 从侧边栏进入睡眠检查，从本地获取保存的学校 ID
  if (skipType.toUpperCase() === 'INFANT_SLEEP') {
    site = JSON.stringify(localStorage.getItem(getSleepCheckSelectedCenterIdKey()))
  }
  return site ? JSON.parse(site) : ''
}

/* 设置当前选中的学校 */
export const setSelectedSite = (newSelectSite) => {
  let site = JSON.stringify(newSelectSite)
  return sessionStorage.setItem('selectedSite', site)
}

/* 获得当前选中的班级 */
export const getSelectedClass = () => {
  // 从本地获取进入睡眠检查或 DLL 的方式
  let skipType = getSkipTypeValue()
  let tempClass
  // 从 home 点击班级进入睡眠检查或 DLL
  if (skipType.toUpperCase() === 'HOME') {
    tempClass = sessionStorage.getItem('selectedClass')
  }
  // 从侧边栏进入 DLL ，从本地获取保存的班级 ID
  if (skipType.toUpperCase() === 'DLL') {
    tempClass = JSON.stringify(localStorage.getItem(getDllSelectedGroupIdKey()))
  }
  // 从侧边栏进入睡眠检查，从本地获取保存的班级 ID
  if (skipType.toUpperCase() === 'INFANT_SLEEP') {
    tempClass = JSON.stringify(localStorage.getItem(getSleepCheckSelectedGroupIdKey()))
  }
  return tempClass ? JSON.parse(tempClass) : ''
}

/* 设置当前选中的班级 */
export const setSelectedClass = (newSelectClass) => {
  let tempClass = JSON.stringify(newSelectClass)
  return sessionStorage.setItem('selectedClass', tempClass)
}

/* 获得当前选中的班级 */
export const getSelectedChild = () => {
  let tempChild = sessionStorage.getItem('selectedChild')
  return tempChild ? JSON.parse(tempChild) : ''
}

/* 设置当前选中的班级 */
export const setSelectedChild = (newSelectChild) => {
  let tempChild = JSON.stringify(newSelectChild)
  return sessionStorage.setItem('selectedChild', tempChild)
}

/* 是否推荐到机构课程库的 Key */
export const getRecommendToAgencyKey = () => {
  return store.state.user.currentUser.user_id + 'RECOMMEND_TO_AGENCY'
}

/* Drop-off Note 功能引导步骤 Key */
export const getDropOffNoteGuideStepKey = () => {
  return store.state.user.currentUser.user_id + '_DROP_OFF_NOTE_GUIDE_STEP'
}

/* 选中的学校 ID 对应的 Key */
export const getSelectedCenterIdKey = () => {
  return store.state.user.currentUser.user_id + '_SELECTED_CENTER_ID'
}

/* 选中的班级 ID 对应的 Key */
export const getSelectedGroupIdKey = () => {
  return store.state.user.currentUser.user_id + '_SELECTED_GROUP_ID'
}

/* 选中的小孩 ID 对应的 Key */
export const getSelectedChildIdKey = () => {
  return store.state.user.currentUser.user_id + '_SELECTED_CHILD_ID'
}

/* 当前用户下所有（除去 Demo Class 以及 离校班级）的学校 对应的 Key */
export const getAllCentersUnderCurrentUserKey = () => {
  return store.state.user.currentUser.user_id + '_ALL_CENTERS_UNDER_CURRENT_USER'
}

/* 保存当前用户下所有的学校数据 对应的 Key */
export const getCacheCentersKey = () => {
  return store.state.user.currentUser.user_id + '_CACHE_CENTERS'
}

/* 选中的符合睡眠检查的班级 ID 对应的 Key */
export const getSleepCheckSelectedGroupIdKey = () => {
  return store.state.user.currentUser.user_id + '_SLEEP_CHECK_SELECTED_GROUP_ID'
}

/* 选中的符合睡眠检查的学校 ID 对应的 Key */
export const getSleepCheckSelectedCenterIdKey = () => {
  return store.state.user.currentUser.user_id + '_SLEEP_CHECK_SELECTED_CENTER_ID'
}

/* 选中的符合 DLL 的学校 ID 对应的 Key */
export const getDllSelectedCenterIdKey = () => {
  return store.state.user.currentUser.user_id + '_DLL_SELECTED_CENTER_ID'
}

/* 选中的符合 DLL 的班级 ID 对应的 Key */
export const getDllSelectedGroupIdKey = () => {
  return store.state.user.currentUser.user_id + '_DLL_SELECTED_GROUP_ID'
}

/* 获取进入 Portfolio, Engagement, DLL, 睡眠检查 的进入方式 */
export const getHomeToPortfolioKey = () => {
  return 'HOME_TO_PORTFOLIO'
}

/* 获取学校数据 */
export const getCentersData = () => {
  return new Promise((resolve, reject) => {
    // 获取当前机构 ID
    let agencyId = store.state.user.currentUser.default_agency_id
    // 获取当前用户 ID
    let userId = store.state.user.currentUser.user_id
    // 当前用户是否是老师角色
    const isTeacherRole = isTeacher()
    axios.get($api.urls(userId).centersAndGroups, { params: { agencyId: agencyId, includeRelatedAgency: isTeacherRole } }).then(res => {
      // 遍历所有的班级，去除学校下的离校班级或没有小孩的班级
      res = filterEmptyCenterGroup(res)
      // 移除学校和班级中没有用到的字段
      removeUselessAttributes(res)
      // 如果上次操作的班级失效或未找到，设置默认的观察测评学校班级
      checkDefaultGroupAndCenter(res)
      // 返回学校数据
      resolve(res)
    }, err => {
      reject(err)
    })
  })
}

/* 班级是否能睡眠检查(0 - 3 岁班级) */
export const checkGroupCanSleepCheck = (group) => {
  if (group && group.id !== 'DEMO-CLASS') {
    const underThreeStageIds = ['72516154-3b50-e411-837d-02dbfc8648ce', '73516154-3b50-e411-837d-02dbfc8648ce', '74516154-3b50-e411-837d-02dbfc8648ce', '81516154-3b50-e411-837d-02dbfc8648ce']
    return underThreeStageIds.indexOf(group.stage_id.toLowerCase()) > -1 && group.childCount > 0
  }
  return false
}

/* 过滤掉空学校以及学校中没有小孩的班级 (班级列表为空且如果是离校班级同样去除) */
const filterEmptyCenterGroup = (centers) => {
  let cacheCenters = []
  for (let i = 0; i < centers.length; i++) {
    let tempGroups = []
    for (let j = 0; j < centers[i].groups.length; j++) {
      if (!centers[i].groups[j].inactive && centers[i].groups[j].childCount !== 0) {
        tempGroups.push(centers[i].groups[j])
      }
    }
    // 如果 tempGroups 不为空则需要赋值
    if (tempGroups.length !== 0) {
      centers[i].groups = tempGroups
      cacheCenters.push(centers[i])
    }
  }
  return cacheCenters
}

/* 判断当前用户是否是新用户或没有创建班级的用户，将结果缓存到本地，用来判断在 DLL 页面如果是没有班级，或者是有班级但是所有的班级都是空班级时显示空页面 */
export const determineIfAllClassesHaveNoChildren = (centers) => {
  for (let i = 0; i < centers.length; i++) {
    for (let j = 0; j < centers[i].groups.length; j++) {
      if (centers[i].groups[j].inactive || centers[i].groups[j].childCount <= 0 || centers[i].groups[j].id === 'DEMO-CLASS') {
        centers[i].groups.splice(j, 1)
      }
    }
    if (centers[i].groups.length > 0) {
      return false
    }
  }
  return true
}

/* 检查是否有默认的学校和班级 */
const checkDefaultGroupAndCenter = (centers) => {
  // 获取当前选中的学校 ID
  const selectedCenterId = localStorage.getItem(getSelectedCenterIdKey())
  // 获取当前选中的班级 ID
  const selectedGroupId = localStorage.getItem(getSelectedGroupIdKey())
  // 获取当前选中的符合睡眠检查的学校 ID
  const sleepCheckSelectedGroupId = localStorage.getItem(getSleepCheckSelectedGroupIdKey())
  // 获取当前选中的符合睡眠检查的班级 ID
  const sleepCheckSelectedCenterId = localStorage.getItem(getSleepCheckSelectedCenterIdKey())
  // 选中的学校班级是否存在
  let defaultExisting = false
  // 选中的符合睡眠检查的学校班级是否存在
  let sleepCheckExisting = false
  // 循环判断缓存中的学校和班级是否存在, 如果缓存中没有则进行赋值默认学校与班级
  centers.forEach(center => {
    center.groups.forEach(group => {
      // 判断已选中的学校班级是否存在，存在时为 true 不需要设置默认选中的学校班级以及小孩 ID, 不存在时为 false ,需要去设置默认的学校班级小孩 ID
      if (selectedGroupId && selectedCenterId && group.id.toLowerCase() === selectedGroupId.toLowerCase() && center.id.toLowerCase() === selectedCenterId.toLowerCase()) {
        defaultExisting = true
      }
      // 判断已选中的学校班级是否存在，存在时为 true 不需要设置默认选中的符合睡眠检查的学校班级以及小孩 ID, 不存在时为 false ,需要去设置默认的符合睡眠检查的学校班级小孩 ID
      if (sleepCheckSelectedGroupId && sleepCheckSelectedCenterId && checkGroupCanSleepCheck(group) && group.id.toLowerCase() === sleepCheckSelectedGroupId.toLowerCase() &&
          center.id.toLowerCase() === sleepCheckSelectedCenterId.toLowerCase()) {
        sleepCheckExisting = true
      }
    })
  })
  // 如果观察测评和家园互动的班级不存在，设置默认的学校和班级
  if (!defaultExisting && centers && centers.length !== 0 && centers[0].groups.length !== 0) {
    localStorage.setItem(getSelectedCenterIdKey(), centers[0].id)
    localStorage.setItem(getSelectedGroupIdKey(), centers[0].groups[0].id)
    // 设置 DLL 条件筛选参数的默认值
    for (let i = 0; i < centers.length; i++) {
      if (centers[i].groups.length > 0) {
        localStorage.setItem(getDllSelectedCenterIdKey(), centers[i].id)
        localStorage.setItem(getDllSelectedGroupIdKey(), centers[i].groups[0].id)
        break
      }
    }
  }
  // 睡眠检查班级不存在，设置默认的睡眠检查学校和班级
  if (!sleepCheckExisting && centers && centers.length !== 0) {
    let updated = false
    centers.forEach(center => {
      center.groups.forEach(group => {
        if (!updated && checkGroupCanSleepCheck(group)) {
          updated = true
          localStorage.setItem(getSleepCheckSelectedCenterIdKey(), center.id)
          localStorage.setItem(getSleepCheckSelectedGroupIdKey(), group.id)
        }
      })
    })
  }
}

/* 移除学校班级中无用的字段 */
const removeUselessAttributes = (centers) => {
  // 学校对象中需要保留的字段
  const reservedCenterAttributes = ['id', 'name', 'groups', 'noClass']
  // 班级对象中需要保留的字段
  const reservedGroupAttributes = ['id', 'name', 'childCount', 'inactive', 'noChild', 'stage_id']
  for (let i = 0; i < centers.length; i++) {
    // 去除学校中无用的属性字段
    for (let centerKey in centers[i]) {
      if (reservedCenterAttributes.indexOf(centerKey) === -1) {
        delete centers[i][centerKey]
      }
    }
    for (let j = 0; j < centers[i].groups.length; j++) {
      for (let groupKey in centers[i].groups[j]) {
        if (reservedGroupAttributes.indexOf(groupKey) === -1) {
          delete centers[i].groups[j][groupKey]
        }
      }
    }
  }
}

/* 获取进入睡眠检查或 DLL 的方式，如果不存在时根据路由为进入方式 skipType 赋值 */
export const getSkipTypeValue = () => {
  let skipType = sessionStorage.getItem(getHomeToPortfolioKey())
  // 如果获取进入睡眠检查或 DLL 的方式为空时，则根据路由来对 skipType 进行赋值
  if (!skipType) {
    let path = window.location.href
    if (path.indexOf('/infantSleep/infantSleepCheckList') !== -1) {
      skipType = 'INFANT_SLEEP'
    }
    if (path.indexOf('/dll/dllList') !== -1) {
      skipType = 'DLL'
    }
    if (path.indexOf('/infantSleepCheck/infantSleepCheckList') !== -1 || path.indexOf('/dllteacher/dllList') !== -1) {
      skipType = 'HOME'
    }
  }
  return skipType
}

/* 将数据存储到 SessionStorage 中并设置过期时间 */
export const setSessionStorageWithExpireTime = (key, value, expireTime) => {
  // 获取当前时间的时间戳
  const now = new Date().getTime()
  // 将当前时间的时间戳和要缓存的数据封装成一个对象
  const payload = {
    value: value,
    expireTime: now + expireTime * 1000
  }
  // 对封装的对象进行存储
  sessionStorage.setItem(key, JSON.stringify(payload))
}

/* 从缓存中获取学校数据，并检查是否过期 */
export const getSessionStorageAndCheckIsExpired = (key) => {
  // 从本地获取带有过期时间的缓存数据
  const payload = sessionStorage.getItem(key)
  // 如果缓存的数据不存在时，返回 null
  if (!payload) {
    return null
  }
  // 解析缓存数据
  const item = JSON.parse(payload)
  // 获取当前时间的时间戳
  const now = new Date().getTime()
  // 判断如果缓存的时间已经过期，则删除本地缓存，返回 null
  if (item.expireTime && (now > item.expireTime)) {
    sessionStorage.removeItem(key)
    return null
  }
  return item.value
}
/* AssistantTool bar 功能引导步骤 Key */
export const getAssistantToolbarGuideStepKey = () => {
  return store.state.user.currentUser.user_id + '_ASSISTANT_TOOLBAR_GUIDE_STEP'
}

/**
 * 不区分大小写比较两个字符串是否相等
 *
 * @param source 来源
 * @param target 目标
 */
export const equalsIgnoreCase = (source, target) => {
  // 输入检查
  if (source === null || target === null) {
    return false
  }
  // 类型检查
  if (typeof source !== 'string' || typeof target !== 'string') {
    return false
  }
  // 不区分大小写比较
  return source.toLocaleLowerCase() === target.toLocaleLowerCase()
}

/**
 * 区分大小写比较两个字符串是否相等
 *
 * @param source 来源
 * @param target 目标
 * @returns {boolean} true 相等，false 不相等
 */
export const equalsNotIgnoreCase = (source, target) => {
  // 输入检查
  if (source === null || target === null) {
    return false
  }
  // 类型检查
  if (typeof source !== 'string' || typeof target !== 'string') {
    return false
  }
  // 区分大小写比较
  return source === target
}

/**
 * 是否是 Bloom Quiz
 *
 * @param questions  问题列表
 */
export const isBloomQuiz = (questions) => {
  if (questions && questions.length > 0 && (questions[0].question || questions[0].answer)) {
    return questions[0].level.indexOf('DOK') == -1
  }
  return true
}
/**
 * magic 分享到 Facebook、推特、复制链接
 * @param {*} type 分享网站
 * @param {*} unit unit 详情
 */
export const shareLink = (type, unit) => {
  let detailUrl = '/magic-curriculum/unit-detail?unitId=' + unit.id
  switch (type) {
    case 'Facebook':
      window.open(`https://www.facebook.com/sharer/sharer.php?u=${location.origin}${detailUrl}`)
      break
    case 'Twitter':
      window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(`🚀 I submitted my unit for the PTKLF Magicurriculum Contest! Please like it! ❤️ Every vote counts!

Teachers can join too! Use the AI Unit Planner and win prizes! 🎉

Let’s bring magic to the classroom! ✨

👉 Vote: [${location.origin}${detailUrl}]
`)}`)
      break
    default:
      tools.copyText(location.origin + '/#' + detailUrl)
      break
  }
}


/**
 * 判断页面是否是嵌入式的
 */
export const isEmbedded = () => {
  return localStorage.getItem('embedded') === '1'
}

/**
  * 从富文本内容中提取纯文本
  * @param {string} html - HTML字符串
  * @returns {string} 提取的纯文本
  */
export const extractTextFromHtml = (html) => {
  // 空值处理
  if (!html) return ''
  // 创建临时DOM元素
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html;

  // 递归遍历节点
  const extractText = (node) => {
    let result = '';

    // 处理不同类型的节点（如果不能满足需求，可以扩展 case 增加对其它标签的处理）
    switch (node.nodeType) {
      case Node.TEXT_NODE:
        // 文本节点直接获取内容
        result += node.textContent;
        break;

      case Node.ELEMENT_NODE:
        // 元素节点需要特殊处理
        switch (node.nodeName.toLowerCase()) {
          case 'br':
            result += '\n';
            break;
          case 'p':
          case 'div':
            // 段落和div后添加换行
            if (result && !result.endsWith('\n')) {
              result += '\n';
            }
            break;
          case 'li':
            // 列表项前添加 - 
            result += '- ';
            break;
          case 'a':
            // 处理链接文本
            result += node.textContent;
            if (node.href) {
              result += ` (${node.href})`;
            }
            break;
          case 'img':
            // 处理图片的 alt 文本和 src
            const alt = node.getAttribute('alt') || '';
            const src = node.getAttribute('src') || '';
            if (alt && src) {
              result += `[Image: ${src} (${alt})]`;
            } else if (src) {
              result += `[Image: ${src}]`;
            } else if (alt) {
              result += `[Image: ${alt}]`;
            } else {
              result += '[Image]';
            }
            break;
        }

        // 递归处理子节点
        for (const childNode of node.childNodes) {
          result += extractText(childNode);
        }

        // 段落和div后确保有换行
        if ((node.nodeName.toLowerCase() === 'p' || node.nodeName.toLowerCase() === 'div') 
            && !result.endsWith('\n')) {
          result += '\n';
        }
        break;
    }

    return result;
  }

  // 开始解析
  let result = extractText(tempDiv);
  
  // 清理结果中的 BOM 字符
  result = result.replace(/\ufeff/g, '');

  return result;
}
