import Quill from 'quill'

// 定义自定义 Blot
const Embed = Quill.import('blots/embed')

export class ImpScriptBlot extends Embed {
  static formats (node) {
    // 从节点中提取 style 属性
    return node.getAttribute('style') || ''
  }

  // 创建标签
  static create (value) {
    let node = document.createElement('imp-script')
    if (typeof value === 'string') {
      node.setAttribute('contenteditable', false)
      node.innerHTML = value
    }
    return node
  }

  static value (node) {
    return node.innerHTML
  }

  // 从 DOM 节点中读取样式属性
  // update (domNode, oldValue) {
  //   const currentStyle = domNode.getAttribute('style')
  //   if (currentStyle !== oldValue) {
  //     domNode.setAttribute('style', oldValue)
  //   }
  // }

  // 自定义方法：将格式应用到 DOM 节点
  // format (name, value)
  //   if (name === 'style') {
  //     this.domNode.setAttribute('style', value) // 使用 this.node 替代 this.domNode
  //   } else {
  //     super.format(name, value)
  //   }
  // }
}

// 定义标签名
ImpScriptBlot.blotName = 'impScript'
ImpScriptBlot.tagName = 'imp-script'

export default ImpScriptBlot
