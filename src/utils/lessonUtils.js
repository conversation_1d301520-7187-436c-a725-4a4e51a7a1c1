import frameworkUtils from '@/utils/frameworkUtils'
import tools from '@/utils/tools'
import Api from '@/api/lessons2'
import LessonApi from '@/api/lessons2'
import { Message } from 'element-ui'
import { extractTextFromHtml } from '@/utils/common'
import EditorTools from '@/utils/lessonMaterialEditorTools'
export default {
  // 从课程数据中收集课程数据
  redesignLessonToInfo (mainLessonInfo) {
    if (!mainLessonInfo) {
      return ''
    }
    return 'Lesson Title: ' + mainLessonInfo.name + '\n' +
      'Activity Preparation Time: ' + mainLessonInfo.prepareTime + ' minutes' + '\n' +
      'Activity Duration: ' + mainLessonInfo.activityTime + ' minutes' + '\n' +
      'Measures: ' + mainLessonInfo.measureInfo + '\n\n' +
      'Objectives\n' + this.clearHtmlTag(mainLessonInfo.objectives) + '\n\n' +
      'Materials\n' + this.clearHtmlTag(mainLessonInfo.materials) + '\n\n' +
      'Key Vocabulary Words & Child-Friendly Definitions\n' + this.clearHtmlTag(mainLessonInfo.keyVocabularyWords) + '\n\n' +
      'Implementation Steps\n' + this.clearHtmlTag(mainLessonInfo.implementationSteps)
  },

  // 根据课程信息拼接 redesignIdea 课程数据，周计划课程没有自动保存，必须从前端拿课程数据
  redesignLesson (lesson) {
    if (!lesson) {
      return null
    }
    let redesignLesson = {}
    redesignLesson.name = lesson.name
    redesignLesson.prepareTime = lesson.prepareTime
    redesignLesson.activityTime = lesson.activityTime
    redesignLesson.objectives = lesson.objective
    redesignLesson.materials = lesson.material ? lesson.material.description : ''
    redesignLesson.implementationSteps = lesson.steps[0].content
    // 词汇
    if (lesson.dlls && lesson.dlls.length > 0) {
      const keyWords = lesson.dlls.map(dll => {
        // 获取主体内容
        let keyword = dll.content

        // 如果 description 存在，将其拼接到 content 后面
        if (dll.description && dll.description.trim()) {
          keyword = `${keyword}: ${dll.description}`
        }

        return keyword
      })

      // 使用换行符拼接所有关键词，并赋值给 keyVocabularyWords
      redesignLesson.keyVocabularyWords = keyWords.join('\n')
    }
    // 取测评点数据
    if (lesson.measures && lesson.measures.length > 0) {
      let tempMeasures = []
      frameworkUtils.collectMeasures(tempMeasures, lesson.measures)
      // 将每个对象的 name 属性拼接成一个字符串，以逗号分隔
      redesignLesson.measureInfo = tempMeasures
        .map(measure => measure.abbreviation || measure.mappingAbbr)
        .join(', ')
    }
    return redesignLesson
  },

  // js 移除 html 格式
  clearHtmlTag (content) {
    // 检查内容是否为空
    if (!content || content.trim() === '') {
      return ''
    }

    // 解析 HTML 并创建 DOM 结构
    const parser = new DOMParser()
    const doc = parser.parseFromString(content, 'text/html')

    // 禁用格式化输出（模拟 Jsoup 的 prettyPrint）
    // 不需要专门设置，在 JS 中解析不会自动格式化

    // 替换 <p> 标签前添加换行符
    doc.querySelectorAll('p').forEach(p => {
      p.insertAdjacentText('beforebegin', '\n')
    })

    // 获取纯文本内容
    let textContent = doc.body.textContent || ''

    // 去除前后空格并替换换行符
    return textContent.replace(/\\n/g, '\n').trim()
  },

  /**
   * 周计划复制课程
   *
   * @param item 被复制的课程
   * @param planCenter Center 数据
   * @param lessonCount 当天课程数量
   * @param copyLessonLoadingDay 哪天需要 Loading
   * @param inLessonDialog 是否是在弹框点击的复制
   * @param callback 方法回调
   */
  copyLesson (item, planCenter, lessonCount, copyLessonLoadingDay, inLessonDialog, callback) {
    // 如果已经 5 节课，弹出提示
    if (lessonCount >= 5) {
      Message.error($i18n.t('loc.plan151'))
      callback()
      return
    }

    // 开始本天 Loading
    this.updateCopyLessonLoadingDay('push', item.centerId ? item.centerId : item.dayOfWeek, copyLessonLoadingDay)
    // 深拷贝当前周计划 item 数据
    let copyItem = JSON.parse(JSON.stringify(item))
    // 修改周计划 item Id
    copyItem.id = tools.uuidv4()

    // 如果有课程 Id ，则为课程数据，需要先复制课程数据
    if (item.lessonId) {
      Api.copyLesson(item.lessonId, true).then((res) => {
        // 修改当前周计划 item 的课程 Id
        copyItem.lessonId = res.id
        this.createCopyItem(item, copyItem, planCenter, lessonCount, copyLessonLoadingDay, inLessonDialog, callback)
      }).catch((err) => {
        // 结束复制课程的 Loading
        this.updateCopyLessonLoadingDay('del', item.centerId ? item.centerId : item.dayOfWeek, copyLessonLoadingDay)
        callback()
      })
    } else {
      // 如果不包含课程 Id ，则为链接课程，直接创建周计划 item
      this.createCopyItem(item, copyItem, planCenter, lessonCount, copyLessonLoadingDay, inLessonDialog, callback)
    }
  },

  /**
   * 创建并更新周计划 item
   *
   * @param item 被复制的课程
   * @param copyItem 复制出的课程
   * @param planCenter Center 数据
   * @param lessonCount 当天课程数量
   * @param copyLessonLoadingDay 哪天正在 Loading
   * @param inLessonDialog 是否是在弹框点击的复制
   * @param callback 方法回调
   */
  createCopyItem (item, copyItem, planCenter, lessonCount, copyLessonLoadingDay, inLessonDialog, callback) {
    // 获取周计划复制课程请求参数
    let params = {
      id: copyItem.id,
      name: copyItem.name,
      categoryId: copyItem.categoryId,
      planId: copyItem.planId,
      lessonId: copyItem.lessonId,
      link: copyItem.link ? copyItem.link.trim() : null,
      dayOfWeek: copyItem.dayOfWeek,
      childIds: copyItem.childIds,
      measureIds: copyItem.measureIds,
      frameworkId: copyItem.frameworkId,
      sortNum: 0,
      centerId: planCenter ? planCenter.center.id : null
    }
    // 创建新的周计划 item
    LessonApi.createItem(params).then(response => {
      // 修改排序，后台会对其他周计划 item 排序
      params.sortNum = copyItem.sortNum > lessonCount ? lessonCount : copyItem.sortNum
      LessonApi.updateItem(params).finally(() => {
        Message.success(inLessonDialog ? $i18n.t('loc.lessonCopySuccessNeedBack') : $i18n.t('loc.lessonCopySuccess'))
        // 结束复制课程的 Loading
        this.updateCopyLessonLoadingDay('del', item.centerId ? item.centerId : item.dayOfWeek, copyLessonLoadingDay)
        callback(copyItem)
      })
    }).catch(() => {
      // 结束复制课程的 Loading
      this.updateCopyLessonLoadingDay('del', item.centerId ? item.centerId : item.dayOfWeek, copyLessonLoadingDay)
      callback()
    })
  },

  // 更新需要 Loading 的天数
  updateCopyLessonLoadingDay (handle, data, copyLessonLoadingDay) {
    if (handle === 'push') {
      copyLessonLoadingDay.push(data)
    } else {
      // 查找需要删除元素的下标
      const index = copyLessonLoadingDay.indexOf(data)
      if (index > -1) {
        copyLessonLoadingDay.splice(index, 1)
      }
    }
  },

  // 更新周计划活动数组，在中间插入新课程，并滚动到该位置
  updateCategory (copyItem, beforeId, category) {
    // 在当天课程中，需要插入位置后的课程向后推一个
    category.filter(item => item.dayOfWeek === copyItem.dayOfWeek && item.sortNum > copyItem.sortNum)
      .forEach(item => {
        item.sortNum += 1
      })
    // 找到哪个课程后面需要插入元素
    const index = category.findIndex(item => item.id === beforeId)
    copyItem.sortNum += 1
    // 在该课程后面插入元素
    category.splice(index + 1, 0, copyItem)
  },

  /**
   * 生成课程的准备时间和活动时间的选项
   */
  generateTime () {
    let result = []
    let unit = 'mins'
    let unitValue = 'minutes'
    for (let value = 5; value < 65; value += 5) {
      result.push({
        name: `${value} ${unit}`,
        value: `${value} ${unitValue}`
      })
    }
    return result
  },

  /**
   * 标准化文本以消除格式差异
   *
   * @param text 需要标准化的文本
   * @returns 标准化后的文本
   */
  normalizeText(text) {
    if (!text || typeof text !== 'string') {
      return text
    }

    return text
      // 将多个连续的空格替换为单个空格
      .replace(/\s+/g, ' ')
      // 去除换行符前后的空格
      .replace(/\s*\n\s*/g, '\n')
      // 去除行首和行尾的空格
      .replace(/^\s+|\s+$/gm, '')
      // 去除整个文本前后的空白
      .trim()
  },

  /**
   * 清除与 lesson 比较无关的字段
   * 
   * @param lesson 课程数据
   * @returns 
   */
  clearLessonCompareIgnoreFields(lesson) {
    if (!lesson) {
      return null
    }
    let lessonCopy = JSON.parse(JSON.stringify(lesson))
    // ID 统一转大写
    if (lessonCopy.id) {
      lessonCopy.id = lessonCopy.id.toUpperCase()
    }
    // 封面列表
    if (lessonCopy.coverMedias) {
      lessonCopy.coverMedias.forEach(cover => {
        cover.icon = null;
        cover.uid = null;
        cover.status = null;
      });
    }
    // 封面对象
    if (lessonCopy.cover) {
      lessonCopy.cover.sourceFileName = null;
      lessonCopy.cover.size = null;
      lessonCopy.cover.fileType = null;
      lessonCopy.cover.coverUrl = null;
    }
    // 将一些与 lesson 比较无关字段进行特殊处理，都置为 null，解决 lesson 自动保存时，changesCounter 不准确的问题
    if (lessonCopy.steps) {
      if (lessonCopy.steps.classCLRGenerated) {
        lessonCopy.steps.classCLRGenerated = null
      }
      lessonCopy.steps.forEach(step => {
        if (step.lessonStepGuides) {
          step.lessonStepGuides.forEach(guide => {
            guide.measureDescription = null;
            guide.core = null;
            guide.mapped = null;
            guide.sortIndex = null;
          });
        }

        if (step.teachingTips) {
          step.teachingTips.forEach(tip => {
            tip.core = null;
            tip.measureDescription = null;
            tip.measureName = null;
          });
        }

        if (step.lessonImpStepAndSource && step.lessonImpStepAndSource.sources) {
          step.lessonImpStepAndSource.sources.forEach(source => {
            source.hidden = null;
          });
        }
        // 解决富文本中不同 HTML 标签造成的 lesson 是否变化发生误判的问题
        if (step.culturallyResponsiveInstruction) {
          step.culturallyResponsiveInstruction = extractTextFromHtml(step.culturallyResponsiveInstruction)
        }
        if (step.culturallyResponsiveInstructionGroup) {
          step.culturallyResponsiveInstructionGroup = extractTextFromHtml(step.culturallyResponsiveInstructionGroup)
        }
        if (step.culturallyResponsiveInstructionGeneral) {
          step.culturallyResponsiveInstructionGeneral = extractTextFromHtml(step.culturallyResponsiveInstructionGeneral)
        }
      });
    }
    // 处理附件中不需要参与比较的字段
    if (lessonCopy.attachments) {
      lessonCopy.attachments.forEach(attachment => {
        attachment.icon = null;
        attachment.uid = null;
        attachment.status = null;
      });
    }
    // 处理 material 中文本字段
    if (lessonCopy.material) {
      // 解析出原始文本内容进行比较
      lessonCopy.material.description = EditorTools.parseValue(lessonCopy.material.description);
      if (lessonCopy.material.attachmentMedias) {
        lessonCopy.material.attachmentMedias.forEach(attachment => {
          attachment.icon = null;
          attachment.uid = null;
          attachment.status = null;
        });
      }
    }
    // 处理 dll 中的字段
    if (lessonCopy.dlls) {
      lessonCopy.dlls.forEach(dll => {
        // 只保留需要比较的字段
        const languages = dll.languages ? dll.languages.map(lang => ({
          content: lang.content,
          langCode: lang.langCode
        })) : [];

        // 重置 dll 对象，只保留需要比较的字段
        Object.keys(dll).forEach(key => {
          if (!['content', 'description', 'sortIndex', 'mediaModels'].includes(key)) {
            delete dll[key];
          }
        });

        // 重新赋值languages
        dll.languages = languages;
      });
    }

    if (lessonCopy.framework) {
      lessonCopy.framework.frameworkName = null;
      lessonCopy.framework.frameworkUrl = null;
      lessonCopy.framework.drdp = null;
    }

    // measures 只比较最底层测评点的 ID
    if (lessonCopy.measures) {
      // 递归收集最底层 measure 的 ID
      const collectMeasureIds = (measure) => {
        if (!measure.children || measure.children.length === 0) {
          return [measure.id]
        }
        return measure.children.reduce((ids, child) => {
          return ids.concat(collectMeasureIds(child))
        }, [])
      }
      
      // 收集所有最底层 measure 的 ID 并重新赋值
      lessonCopy.measures = lessonCopy.measures.reduce((ids, measure) => {
        return ids.concat(collectMeasureIds(measure))
      }, [])
    }

    // 处理教学指导
    if (lessonCopy.teachingTips) {
      lessonCopy.teachingTips.forEach(tip => {
        tip.core = null;
        tip.measureDescription = null;
      });
    }
    // 处理实施步骤生成资源中的富文本
    if (lessonCopy.implementationSteps) {
      lessonCopy.implementationSteps = this.normalizeText(extractTextFromHtml(lessonCopy.implementationSteps))
    }
    if (lessonCopy.lessonImpStepAndSource && lessonCopy.lessonImpStepAndSource.impStepContent) {
      lessonCopy.lessonImpStepAndSource.impStepContent = this.normalizeText(extractTextFromHtml(lessonCopy.lessonImpStepAndSource.impStepContent))
    }

    // lesson 中所有为 null 的属性移除掉
    this.removeNullProperties(lessonCopy)
    return lessonCopy;
  },

  /**
   * 移除 lesson 中所有为 null 的属性
   * 
   * @param lesson 课程数据
   */
  removeNullProperties(lesson) {
    Object.keys(lesson).forEach(key => {
      if (lesson[key] === null || lesson[key] === undefined || lesson[key] === '') {
        delete lesson[key];
      } else if (typeof lesson[key] === 'object' && lesson[key] !== null) {
        this.removeNullProperties(lesson[key])
      }
    })
  },

  /**
   * 比较两个课程数据是否相等
   * 
   * @param lesson1 课程数据1
   * @param lesson2 课程数据2
   * @returns 是否相等
   */
  equalsLesson(lesson1, lesson2) {
    // 先深拷贝，避免影响源数据
    let lesson1Copy = JSON.parse(JSON.stringify(lesson1))
    // 当前课程数据
    let lesson2Copy = JSON.parse(JSON.stringify(lesson2))
    
    // 处理无关字段
    lesson1Copy = this.clearLessonCompareIgnoreFields(lesson1Copy)
    lesson2Copy = this.clearLessonCompareIgnoreFields(lesson2Copy)

    // 比较处理后的 sourceLesson 和 lesson 是否相等
    return JSON.stringify(lesson1Copy) === JSON.stringify(lesson2Copy)
  },

  /**
   * 生成课程变化描述
   * 
   * @param sourceLesson 源课程数据
   * @param lesson 当前课程数据
   * @returns 变化描述
   */
  generateLessonChangeDescription(sourceLesson, lesson) {
    // 处理无关字段
    let lessonCopy = this.clearLessonCompareIgnoreFields(lesson)
    let sourceLessonCopy = this.clearLessonCompareIgnoreFields(sourceLesson)
    // 定义 lesson 中字段与修改描述中模块名称的对应关系
    const fieldMappings = {
      'cover.url': 'Lesson Cover',
      'name': 'Lesson Name',
      'title': 'Lesson Name',
      'ageGroup': 'Age Group',
      'prepareTime': 'Prepare Time',
      'activityTime': 'Activity Time',
      'lessonTemplate.templateType': 'Lesson Template',
      'themes': 'Theme/Topic',
      'framework': 'Framework',
      'measuresString': 'Measures',
      'objectives': 'Objective',
      'materials': 'Materials',
      'materialFiles': 'Materials',
      'book': 'Family Resources',
      'videoBook': 'Family Resources',
      'attachments': 'Family Resources',
      'keyVocabularyWords': 'DLL Vocabulary and Phrases',
      'implementationSteps': 'Implementation Steps/Guides',
      'lessonImpStepAndSource': 'Implementation Steps/Guides',
      'teachingTips': 'Teaching Tips for Standards',
      'universalDesignForLearning': 'Universal Design for Differentiated Learning',
      'universalDesignForLearningGroup': 'Universal Design for Differentiated Learning',
      'mixedAgeDifferentiations': 'Universal Design for Differentiated Learning',
      'culturallyResponsiveInstruction': 'Culturally and Linguistically Responsive Practice',
      'culturallyResponsiveInstructionGroup': 'Culturally and Linguistically Responsive Practice',
      'culturallyResponsiveInstructionGeneral': 'Culturally and Linguistically Responsive Practice',
      'lessonClrAndSources': 'Culturally and Linguistically Responsive Practice',
      'lessonTypicalBehaviors': 'Typical Behaviors and Observation Tips',
      'lessonStepGuides': 'Typical Behaviors and Observation Tips',
      'homeActivity': 'Family Resources',
      'questions': 'Formative Assessment',
      'learnerProfiles': 'Portrait of a Graduate',
    }

    // 用于记录每个业务模块的变化状态
    const displayNameChanges = {}

    // 初始化所有业务模块的变化状态为 false
    const allDisplayNames = [...new Set(Object.values(fieldMappings))]
    allDisplayNames.forEach(displayName => {
      displayNameChanges[displayName] = false
    })

    // 比较字段值是否相等的函数
    const compareValues = (value1, value2) => {
      return JSON.stringify(value1) !== JSON.stringify(value2)
    }

    // 获取对象中指定路径的值
    const getValueByPath = (obj, path) => {
      return path.split('.').reduce((current, key) => {
        return current ? current[key] : undefined
      }, obj)
    }

    // 检查每个字段的变化
    Object.entries(fieldMappings).forEach(([fieldPath, displayName]) => {
      const sourceValue = getValueByPath(sourceLessonCopy, fieldPath)
      const currentValue = getValueByPath(lessonCopy, fieldPath)
      
      if (compareValues(sourceValue, currentValue)) {
        displayNameChanges[displayName] = true
      }
    })

    // 收集发生变化的模块名称
    const changes = Object.entries(displayNameChanges)
      .filter(([_, hasChanged]) => hasChanged)
      .map(([displayName]) => displayName)

    // 生成描述文本
    let description
    if (changes.length > 1) {
      description = changes.slice(0, -1).join(", ") + " and " + changes[changes.length - 1]
    } else if (changes.length === 1) {
      description = changes[0]
    }
    return description
  },

  /**
   * 是否是 center 课程
   * @param activityType 活动类型
   */
  isCenterLesson (activityType) {
    if (!activityType || activityType === '') {
      return false
    }
    const lowerCaseActivityType = activityType.toLocaleLowerCase()
    return lowerCaseActivityType === 'center' || lowerCaseActivityType === 'station'
  },

  /**
   * 判断是否符合显示课堂类型部分
   * 
   * @param {*} ages 年龄组
   * @param {*} activityType 活动类型
   * @returns 是否符合显示课堂类型部分
   */
  showClassroomType(ages, activityType) {
    // 年龄段为 K-12 之间的年级，且活动类型不是 Center 或 Station，才能展示 Classroom Type 部分
    let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
    return ages.some(age => tools.arrayContainsIgnoreCase(k12Grades, age)) && !this.isCenterLesson(activityType)
  }
}
