
var awsS3Service = {}

// 初始化s3配置
function init (bucketRegion,bucketName,identityPoolId) {
  // 如果已经初始化过，则返回
  if (awsS3Service.s3) {
    return
  }
  // 如果配置不完整，直接返回
  if (!bucketRegion || !bucketName || !identityPoolId) {
    console.log("error")
    return
  }
  // 设置认证信息
  window.AWS.config.update({
    region: bucketRegion,
    credentials: new window.AWS.CognitoIdentityCredentials({
      IdentityPoolId: identityPoolId
    })
  })
  // 创建s3对象
  var s3 = new window.AWS.S3({
    apiVersion: '2006-03-01',
    params: {Bucket: bucketName}
  })
  awsS3Service.s3 = s3
}

awsS3Service.init = init
export default awsS3Service