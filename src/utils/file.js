/*
* 文件操作
* */
import configBaseUrl from '@/utils/setBaseUrl'
import axios from '@/utils/axios'
import store from '@/store'
import doc from '@/assets/img/file/doc.png'
import docx from '@/assets/img/file/docx.png'
import pdf from '@/assets/img/file/pdf.png'
import ppt from '@/assets/img/file/ppt.png'
import pptx from '@/assets/img/file/pptx.png'
import xls from '@/assets/img/file/xls.png'
import xlsx from '@/assets/img/file/xlsx.png'
import file from '@/assets/img/mediaForm/file.png'
import zip from '@/assets/img/file/zip.png'
import awsS3Service from "@/utils/awsS3Service";
import {generateVideoKey} from '@/utils/common'

export default {
  // 图片压缩
  compressImage(file, width, height) {
    // todo
    return file
  },
  // 视频压缩
  compressVideo(file, width, height) {
    // todo
    return file
  },
  // 上传文件
  uploadFile(file, {privateFile, processEventHandler, annexType, videoSnapshot}) {
    let formData = new FormData()
    formData.append('file', file)
    videoSnapshot && formData.append('videoSnapshot', videoSnapshot, file.name.substring(0, file.name.lastIndexOf('.')) + '.png')
    return axios.post($api.urls().uploadFile, formData, {
      baseURL: configBaseUrl,
      headers: {'Content-Type': 'multipart/form-data'},
      params: {
        privateFile: !!privateFile,
        fileName: file.name,
        annexType,
        type: file.name.substring(file.name.lastIndexOf('.') + 1)
      },
      onUploadProgress: evt => {
        processEventHandler && processEventHandler(evt)
      }
    })
  },
  // 文件直传：直接将文件上传到云存储
  async uploadFileDirectly(file, privateFile, feature) {
    // 获取临时上传链接
    let preSignedResponse = await axios.post($api.urls().getPreSignedURL, {
      privateFile: privateFile,
      feature: feature,
      fileName: file.name,
      type: file.name.substring(file.name.lastIndexOf('.') + 1),
      contentType: file.type,
      size: file.size,
      agencyId: store.state.user.default_agency_id
    })
    // 上传
    let url = new URL(preSignedResponse.uploadUrl)
    return new Promise((resolve, reject) => {
      axios.put(url.toString(), file, {headers: {'content-type': file.type}}).then(result => {
        resolve(preSignedResponse)
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 上传视频至s3
  // param1: 视频文件
  // param2: 视频上传过程回调函数
  // param3: 视频上传结束回调函数
  uploadToS3(file, {onUploadProgress, completeHandler}) {
    const key = generateVideoKey() + '.mp4'
    const params = {Key: key, Body: file}
    const options = {partSize: 5 * 1024 * 1024, queueSize: 5}
    return awsS3Service.s3.upload(params, options,
      (err, data) => completeHandler(err, data)
    ).on('httpUploadProgress', evt => onUploadProgress(evt))
  },
  // 触发上传文件弹框
  getFile(accept) {
    return new Promise((resolve, reject) => {
      let inputElement = document.createElement('input')
      inputElement.type = 'file'
      accept && (inputElement.accept = accept)
      inputElement.addEventListener('change', event => {
        resolve(inputElement.files[0])
      })
      inputElement.click()
    })
  },
  // 判断文件类型--图片
  isImage(url) {
    return /^.+\.(jpg|png|jpeg|gif)$/i.test(url)
  },
  /**
   * 判断图片类型是否有效
   * @param fileType 文件类型
   * @returns {boolean} 图片类型是否有效
   */
  isValidImageType(fileType) {
    const allowedTypes = ["image/jpg", "image/jpeg", "image/png", "image/gif"];
    const lowercaseType = fileType.toLowerCase();
    return allowedTypes.includes(lowercaseType);
  },
  /**
   * 判断是否为 base64 图片的 src
   * @param src 图片 src
   * @returns {boolean}   是否为 base64 图片
   */
  isBase64ImageSrc(src) {
    return /^data:image\/[a-z]+;base64,/.test(src);
  },
  // 判断文件类型--视频
  isVideo(url) {
    return /^.+\.(mp4)$/i.test(url)
  },
  // 解析视频第一帧
  async getFirstFrame(file, videoElement, canvasElement) {
    // 视频校验，非视频返回 null
    if (!file || !videoElement || !canvasElement) {
      return
    }
    let isVideo = /video\/.+/i.test(file.type)
    if (!isVideo) {
      return
    }
    return new Promise((resolve, reject) => {
      let processor = {}
      let info = {}
      processor.doLoad = async function () {
        this.url = URL.createObjectURL(file)
        this.video = videoElement
        // 设置自动播放
        this.video.autoplay = true
        // 设置静音
        this.video.muted = true
        this.video.setAttribute('playsinline', '')
        // 设置内联播放，避免在 IOS 中会劫持播放器，自动弹出播放界面
        this.video.setAttribute('webkit-playsinline', '')
        this.canvas = canvasElement
        this.context = this.canvas.getContext('2d')
        this.video.addEventListener('loadedmetadata', (evt) => {
          this.height = evt.target.videoHeight
          this.width = evt.target.videoWidth
          info.duration = evt.target.duration
          info.height = evt.target.videoHeight
          info.width = evt.target.videoWidth
          this.canvas.height = this.height
          this.canvas.width = this.width
          this.video.addEventListener('canplay', (evt) => {
            if (info.duration > 1) {
              this.video.currentTime = 1
            } else {
              this.video.currentTime = info.duration * 0.25
            }
          }, false)
        }, false)
        this.video.addEventListener('seeked', (evt) => {
          this.context.drawImage(this.video, 0, 0, this.width, this.height)
          const imageBase64 = this.canvas.toDataURL('image/png')
          info.thumbNail = imageBase64
          // 销毁 video 元素
          this.video.pause() // 暂停视频播放
          this.video.removeAttribute('src') // 移除视频源
          this.video.load() // 重新加载视频
          resolve(info)
        }, false)
        this.video.src = this.url
      }
      processor.doLoad()
    })
  },
  // 判断文件类型，下载提示框展示对应的图标
  getFileType(fileName) {
    if (fileName.endsWith('doc')) {
      return doc
    }
    if (fileName.endsWith('docx')) {
      return docx
    }
    if (fileName.endsWith('pdf')) {
      return pdf
    }
    if (fileName.endsWith('ppt')) {
      return ppt
    }
    if (fileName.endsWith('pptx')) {
      return pptx
    }
    if (fileName.endsWith('xls')) {
      return xls
    }
    if (fileName.endsWith('xlsx')) {
      return xlsx
    }
    if (fileName.endsWith('zip')) {
      return zip
    }
    return file
  },
  /**
   * 下载文件：系统生成的 PDF 的下载。。。
   * @param type 要下载的文件类型
   * @param retryInterval 重试间隔
   * @param retryTimes 重试次数
   */
  async download(type, retryInterval = 3000, retryTimes = 10) {
    async function getURL(type) {
      let [{status,pdfUrl = ''}] = await axios.get($api.urls().pdfList, {params: {type}});
      if(status !== 'SUCCEED' && status !== 'FAILED'){
        return;
      }
      return pdfUrl;
    }

    async function awaitFor(time) {
      return new Promise((resolve)=>{
        setTimeout(() => {
          resolve();
        }, time);
      })
    }

    let url = await getURL(type);
    while (url === undefined && retryTimes-- > 0) {
      await awaitFor(retryInterval);
      url = await getURL(type);
    }
    window.location.href = url;
  },
  // 填充视频信息 缩略图（第一帧）base64编码，视频分辨率
  async getVideoInfo(file, videoHelper) {
    // 获取第一帧图片二进制文件
    let videoInfo = await this.getFirstFrame(file, videoHelper.querySelector('video'), videoHelper.querySelector('canvas'));
    file.duration = videoInfo.duration
    file.base64Str = videoInfo.thumbNail
    file.width = videoInfo.width
    file.height = videoInfo.height
  },
  
  courseDownload (url, filename) {
    this.getBlob(url, (blob) => {
      this.saveAs(blob, filename)
    })
  },

  getBlob (url, cb) {
    var xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    xhr.onload = function () {
      if (xhr.status === 200) {
        cb(xhr.response)
      }
    }
    xhr.send()
  },

  saveAs (blob, filename) {
    if (window.navigator.msSaveOrOpenBlob) {
      navigator.msSaveBlob(blob, filename)
    } else {
      var link = document.createElement('a')
      var body = document.querySelector('body')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      // fix Firefox
      link.style.display = 'none'
      body.appendChild(link)
      link.click()
      body.removeChild(link)
      window.URL.revokeObjectURL(link.href)
    }
  }
}
