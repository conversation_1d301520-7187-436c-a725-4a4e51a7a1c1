import { localEnv } from './setBaseUrl'
import store from '@/store'
import { acrossRole } from "@/utils/common"

let currentUser = store.getters.currentUser ? store.getters.currentUser : ''
let source = acrossRole('parent') ? 'web-parent' : 'web'

const h5BaseUrlEnv = {
    local: 'http://localhost:8081/',
    dev: 'http://dev.h5.lejingling.cn:9000/',
    test: 'https://test.d3fmqy08svk1x9.amplifyapp.com/',
    stage: 'https://stage.d3fmqy08svk1x9.amplifyapp.com/',
    release: 'http://com.learning-genie.h5.s3-website-us-west-1.amazonaws.com/',
    prod: 'https://h5.learning-genie-api.com/'
};

/**
 * 获取 h5 页面的 url
 * @param path {string} 页面路径
 * @param params {Object} 参数
 * @returns {string} url
 */
export function getH5Url(path, params) {
    let paramString = ''
    if (currentUser) {
        params = params || {}
        params['env'] = localEnv
        params['source'] = source
        params['t'] = currentUser.token
        params['uid'] = currentUser.user_id
        if (!params['role']) {
            params['role'] = currentUser.role2.toUpperCase()
        }
        // 构建参数字符串
        paramString = Object.keys(params).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&');
    }
    return `${h5BaseUrl}${path}?${paramString}`
}

export let h5BaseUrl = h5BaseUrlEnv[localEnv]
