import Vue from 'vue'
import i18n from './i18n'
import {
  <PERSON><PERSON>, Aside, Breadcrumb, BreadcrumbItem, Button, ButtonGroup, Card,
  Cascader, Carousel,CarouselItem, Checkbox, CheckboxGroup, Col,
  Collapse, CollapseItem, Container, DatePicker, Dialog, Dropdown,
  DropdownItem, DropdownMenu, Footer, Form, FormItem, Header, Input,
  InputNumber, Main, Menu, MenuItem, Option, OptionGroup, Pagination,
  Popover, Progress, Radio, RadioButton, RadioGroup, Row, Select,
  Step, Steps, Switch, Table, TableColumn, Tabs, TabPane, Tag,
  Timeline, TimelineItem, Tooltip, Tree, Upload, Divider, Link,
  Image, Icon, Backtop, InfiniteScroll, Avatar, Drawer, Skeleton,
  SkeletonItem, Result, Loading, MessageBox, Notification, Message,
} from 'element-ui';
import ElementLocale from 'element-ui/lib/locale'
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition';

Vue.use(Alert).use(Aside).use(Breadcrumb).use(BreadcrumbItem).use(Button).use(ButtonGroup).use(Card)
  .use(Cascader).use(Carousel).use(CarouselItem).use(Checkbox).use(CheckboxGroup).use(Col)
  .use(Collapse).use(CollapseItem).use(Container).use(DatePicker).use(Dialog).use(Dropdown)
  .use(DropdownItem).use(DropdownMenu).use(Footer).use(Form).use(FormItem).use(Header).use(Input)
  .use(InputNumber).use(Main).use(Menu).use(MenuItem).use(Option).use(OptionGroup).use(Pagination)
  .use(Popover).use(Progress).use(Radio).use(RadioButton).use(RadioGroup).use(Row).use(Select)
  .use(Step).use(Steps).use(Switch).use(Table).use(TableColumn).use(Tabs).use(TabPane).use(Tag)
  .use(Timeline).use(TimelineItem).use(Tooltip).use(Tree).use(Upload).use(Divider).use(Link)
  .use(Image).use(Icon).use(Backtop).use(InfiniteScroll).use(Avatar).use(Drawer).use(Skeleton)
  .use(SkeletonItem).use(Result)

Vue.use(Loading.directive);

Vue.component(CollapseTransition.name, CollapseTransition)

Vue.prototype.$loading = Loading.service;
Vue.prototype.$msgbox = MessageBox;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$confirm = MessageBox.confirm;
Vue.prototype.$prompt = MessageBox.prompt;
Vue.prototype.$notify = Notification;
Vue.prototype.$message = Message;

ElementLocale.i18n((key, value) => i18n.t(key, value))

// /** Aside Component */
// export class Aside extends ElAside {}

// /** Autocomplete Component */
// export class Autocomplete extends ElAutocomplete {}

// /** Bagde Component */
// export class Badge extends ElBadge {}

// /** Checkbox Button Component */
// export class CheckboxButton extends ElCheckboxButton {}

// /** Color Picker Component */
// export class ColorPicker extends ElColorPicker {}

// /** Menu Item Group Component */
// export class MenuItemGroup extends ElMenuItemGroup {}

// /** Rate Component */
// export class Rate extends ElRate {}

// /** Slider Component */
// export class Slider extends ElSlider {}

// /** TimePicker Component */
// export class TimePicker extends ElTimePicker {}

// /** TimeSelect Component */
// export class TimeSelect extends ElTimeSelect {}

// /** Transfer Component */
// export class Transfer extends ElTransfer {}

// /** Calendar Component */
// export class Calendar extends ElCalendar {}

// /** PageHeader Component */
// export class PageHeader extends ElPageHeader {}

// /** Popconfirm Component */
// export class Popconfirm extends ElPopconfirm {}

// /** CascaderPanel Component */
// export class CascaderPanel extends ElCascaderPanel {}

// /** Empty Component */
// export class Empty extends ElEmpty {}

// /** Spinner Component */
// export class Spinner extends ElSpinner {}

// /** Description Component */
// export class Descriptions extends ElDescriptions {}

// /** Description Item Component */
// export class DescriptionsItem extends ElDescriptionsItem {}
