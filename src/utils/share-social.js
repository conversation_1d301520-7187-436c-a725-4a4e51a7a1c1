import { getRandomDescription } from '@/utils/meta'

/**
 * 分享到社交平台
 * @param type 社交平台类型
 * @param link 分享链接
 * @param content 分享内容
 */
export const getShareSocialLink = (type, link, content) => {
  // 类型转换为大写
  const typeUpper = type.toUpperCase()
  // 只对内容进行编码，链接使用 encodeURI
  const linkEncoded = encodeURIComponent(link)
  // 获取随即的描述
  const description = getRandomDescription()
  const contentEncoded = encodeURIComponent(description)
  // 根据类型获取对应的链接
  switch (typeUpper) {
    case 'FACEBOOK':
      return `https://www.facebook.com/sharer/sharer.php?u=${linkEncoded}&t=${contentEncoded}`
    case 'X':
      const xContent = contentEncoded ? `${contentEncoded} ${linkEncoded}` : linkEncoded
      return `https://x.com/intent/tweet?text=${xContent}`
    case 'LINKEDIN':
      return `https://www.linkedin.com/shareArticle?mini=true&url=${linkEncoded}`
    default:
      return ''
  }
}
