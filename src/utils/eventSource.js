import { fetchEventSource } from '\@microsoft/fetch-event-source'
import { serverlessApiUrl, platform } from './setBaseUrl'
import store from '@/store'
import tools from './tools'

export const createEventSource = (path, params, _onmessage, method, body) => {
    // 默认为 GET 请求
    method = method || 'GET'
    // body 序列化
    if (body) {
        body = JSON.stringify(body)
    }
    let retryCount = 0 // 重试次数
    const maxRetryCount = 3 // 最大重试次数
    // 错误类型，用于判断是否需要重试
    class RetriableError extends Error { }
    class FatalError extends Error { }
    return new Promise((resolve, reject) => {
        // 返回参数信息
        let resultParams = null
        let headers = {
            'Content-Type': 'application/json', // 数据格式
            'X-LG-Language': tools.localItem('NG_TRANSLATE_LANG_KEY'), // 语言
            'X-LG-Platform': 'web', // 平台
            'X-UID': store.state.user.uid, // 用户 ID
            'X-LG-Token': store.state.user.token, // token
            'X-LG-TimezoneOffset': -(new Date().getTimezoneOffset() / 60), // 时区
            'X-LG-Unit-Version': store.state.curriculum.promptDebugging ? '2.0' : '2.0', // 临时属性，旧版本特殊处理
        }
        // 请求头添加平台信息
        if (platform && platform === 'CURRICULUM-GENIE') {
            headers['X-Project'] = 'FOLC'
        } else if (platform && platform === 'CURRICULUM-PLUGIN') {
            headers['X-Project'] = 'CURRICULUM-PLUGIN'
        }
        // 发起请求
        fetchEventSource(serverlessApiUrl + path + tools.objToUrlParams(params), {
            method: method,
            headers: headers,
            body: body,
            openWhenHidden: true, // 允许后台运行
            onopen(response) {
                // 返回数据类型
                let contentType = response && response.headers ? response.headers.get('content-type') : null
                // 是否为 text/event-stream 类型
                let contentTypeError = !contentType || (contentType.toLowerCase() !== 'text/event-stream' && contentType.toLowerCase() !== 'text/event-stream;charset=utf-8')
                if (response.ok && !contentTypeError) {
                    // 没有错误
                    return
                } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
                    throw new FatalError()
                } else {
                    // 其他错误，需要重试
                    throw new RetriableError()
                }
            },
            onmessage(message) {
                if (message.event === 'ERROR') {
                    // 服务器错误，不需要重试
                    // 将重试次数设置为最大值
                    retryCount = maxRetryCount + 1
                    throw new FatalError(message.data)
                }
                // 参数类型
                if (message.event === 'PARAM') {
                    resultParams = JSON.parse(message.data)
                    return
                }
                if (message && message.data) {
                    message.data = message.data.substring(1, message.data.length).replace(/\\n/g, '\n')
                    _onmessage(message)
                }
            },
            onclose() {
                // 连接关闭
                console.log('event source closed.')
                resolve(resultParams)
            },
            onerror(error) {
                // 所有错误都再重试 3 次
                console.log('event source error: ', error)
                // 增加重试次数
                retryCount++
                if (retryCount > maxRetryCount) {
                    // 超过最大重试次数，不再重试
                    reject(error)
                    throw error
                }
                // 重试间隔指数增长，间隔 = 重试次数 * 1000 ms
                return retryCount * 1000
            }
        })
    })
}

// 清理解析后字符串中包含的不需要显示的信息
// 为 clearParsedValue 创建模块级别的正则表达式缓存
const CLEAR_PARSED_VALUE_REGEX_CACHE = new Map()
const CLEAR_PARSED_VALUE_REGEX = (subName) => {
    if (!CLEAR_PARSED_VALUE_REGEX_CACHE.has(subName)) {
        CLEAR_PARSED_VALUE_REGEX_CACHE.set(subName, new RegExp('(^|\n)[0-9\.\s]*' + subName + '[:\s]*$'))
    }
    return CLEAR_PARSED_VALUE_REGEX_CACHE.get(subName)
}

const clearParsedValue = (value, keys) => {
    // 遍历键/值
    for (let i = 0; i < keys.length; i++) {
        let keyData = keys[i]
        // 属性对应的名称
        let names = Array.isArray(keyData.name) ? keyData.name : [keyData.name]
        // 遍历属性名称，从第一个字符到完整的名称
        for (let x = 0; x < names.length; x++) {
            let name = names[x]
            // 是否结束
            let isEnd = false
            for (let j = 1; j < name.length; j++) {
                // 截取字符串
                let subName = name.substring(0, j)
                // 使用缓存函数获取正则表达式
                let reg = CLEAR_PARSED_VALUE_REGEX(subName)
                // 通过正则，找到符合名称规则的字符串
                let nameStringResults = value.match(reg)
                // 没有找到，则跳过
                if (!nameStringResults || nameStringResults.length === 0) {
                    continue
                }
                let nameString = nameStringResults[0]
                // 替换字符串，结束循环
                let tempStr = value.replace(nameString, '')
                if (tempStr.length !== 0) {
                    // 如果替换后不为空，则更新值
                    value = tempStr
                }
                isEnd = true
                break
            }
            // 找到符合条件的就结束
            if (isEnd) {
                break
            }
        }
    }
    // 去掉开头和结尾的换行符
    value = value.trim().replace(/^\n+|\n+$/g, '')
    // 去掉结尾的 ```
    value = value.replace(/\n+```$/g, '')
    return value
}

const ASTERISK_REGEX = /\*\*/g // 匹配星号
// 创建正则表达式缓存
const NAME_PATTERN_REGEX_CACHE = new Map()
const NAME_PATTERN_REGEX = (name) => {
    if (!NAME_PATTERN_REGEX_CACHE.has(name)) {
        NAME_PATTERN_REGEX_CACHE.set(name, new RegExp('(^|\n)[0-9\.\s]*' + name + '[:\s]*'))
    }
    return NAME_PATTERN_REGEX_CACHE.get(name)
}

// 根据指定的键/值解析流数据为对象
const parseStreamObjectData = (data, keys, isRemove) => {
    // 结果
    let result = {}
    if (!isRemove) {
        data = data.replace(ASTERISK_REGEX, '')
    }
    // 截取后的数据
    let subData = data
    // 遍历键/值
    for (let i = 0; i < keys.length; i++) {
        let keyData = keys[i]
        // 属性的键
        let key = keyData.key
        // 属性对应的名称
        let name = keyData.name
        // 如果 name 是数组，对名称进行拼接
        if (Array.isArray(name)) {
            name = '(' + name.join('|') + ')'
        }
        // 通过正则，找到符合名称规则的字符串
        let nameStringResults = subData.match(NAME_PATTERN_REGEX(name))
        // 没有找到，则跳过
        if (!nameStringResults || nameStringResults.length === 0) {
            continue
        }
        let nameString = nameStringResults[0]
        // 找到第一次出现的索引位置
        let nameIndex = subData.indexOf(nameString)
        // 未找到，则跳过
        if (nameIndex === -1) {
            continue
        }
        // 截取字符串
        subData = subData.substring(nameIndex + nameString.length + 1, subData.length)
        // 下一个属性
        let nextKeyData = null
        // 已经是最后一个，则取第一个，处理数组的情况
        if (i === keys.length - 1) {
            nextKeyData = keys[0]
        } else {
            nextKeyData = keys[i + 1]
        }
        // 下一个属性的名称
        let nextName = nextKeyData.name
        // 如果 name 是数组，对名称进行拼接
        if (Array.isArray(nextName)) {
            nextName = '(' + nextName.join('|') + ')'
        }
        // 通过正则，找到符合名称规则的字符串
        let nextNameStringResults = subData.match(NAME_PATTERN_REGEX(nextName))
        // 没有找到，则跳过
        if (!nextNameStringResults || nextNameStringResults.length === 0) {
            // 设置值
            result[key] = clearParsedValue(subData, keys)
            continue
        }
        let nextNameString = nextNameStringResults[0]
        // 找到第一次出现的索引位置
        let nextNameIndex = subData.indexOf(nextNameString)
        // 未找到，则跳过
        if (nextNameIndex === -1) {
            // 设置值
            result[key] = clearParsedValue(subData, keys)
            continue
        }
        // 截取字符串
        let valueString = subData.substring(0, nextNameIndex)
        // 设置值
        result[key] = clearParsedValue(valueString, keys)
        // 截取字符串
        subData = subData.substring(nextNameIndex, subData.length)
    }
    // 返回结果和剩余的字符串
    return [result, subData]
}

// 根据指定的键/值解析流数据
export const parseStreamData = (data, keys, maxCount, isRemove) => {
    // 空数据返回空对对象列表
    if (!data || !keys) {
        return [{}]
    }
    // 结果集合
    let results = []
    // 最大遍历次数，如果未指定，默认为 1000 防止死循环
    if (!maxCount) {
        maxCount = 1000
    }
    if (!isRemove) {
        // 移除 Markdown 语法内容后再解析
        data = removeUnexpectedCharacters(data)
    }
    // 对象索引
    let objectIndex = 0
    // 解析数据
    while (objectIndex < maxCount) {
        // 解析对象数据
        let [result, subData] = parseStreamObjectData(data, keys, isRemove)
        // 没有解析出数据，则跳出循环
        if (!result || Object.keys(result).length === 0) {
            break
        }
        // 添加到结果集合
        results.push(result)
        // 数据解析结束
        if (!subData || '' === subData.trim()) {
            break
        }
        // 更新数据
        data = subData
        // 增加索引
        objectIndex++
    }
    return results.length > 0 ? results : [{}]
}

/**
 * 移除无用的符号
 */
export const removeUnexpectedCharacters = (content) => {
    // 没有内容，直接返回
    if (!content) {
        return content
    }
    // 移除 Markdown 语法
    let result = content.replace(/##### /g, '').replace(/#### /g, '').replace(/### /g, '').replace(/## /g, '').replace(/\*\*/g, '')
    // 按照分割符号 ``` 拆分
    let splitData = result.split('```')
    // 如果包含两个 ```, 则取中间的内容
    if (splitData.length === 3) {
        // 如果包含两个 ```, 则取中间的内容
        if (splitData[1] && splitData[1].trim() != '') {
            result = splitData[1]
        }
    }
    // 移除分割符号
    result = result.replace(/```\n/g, '').replace(/```/g, '')
    // 返回结果
    return result
}
