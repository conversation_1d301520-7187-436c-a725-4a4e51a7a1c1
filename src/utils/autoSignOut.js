import tools from '@/utils/tools'
import { isAuthenticated, getCurrentUser, setHttpStatus, getHttpStatus } from '@/utils/common'
import { MessageBox } from 'element-ui'
import store from '@/store'
import { pathName, platform } from './setBaseUrl'
import { AUTO_RENEWAL_TIME, INACTIVITY_TIME_OUT, LAST_NOTIFY_TIME, LAST_OPERATION_TIME, TO_SIGN_OUT_NUM } from './const'
// import { logOut } from '@/utils/applozic'
// import { logout } from '@/utils/quickblox'
import NProgress from "nprogress";

// 清除用户登录状态
function clearCurrentUser () {
  tools.removeSessionItem('CurrentUserName')
  tools.removeSessionItem('selectedAgency')
  tools.removeLocalItem('defaultCenterId')
  tools.removeSessionItem('CurrentUserPassword')
  tools.removeLocalItem(LAST_OPERATION_TIME)
  tools.removeLocalItem('currentUser')
  tools.removeLocalItem('currentUserInfo')
  tools.removeLocalItem('currentAgencies')
  tools.removeSessionItem('currentAgencies')
  tools.removeLocalItem(LAST_NOTIFY_TIME)
  tools.removeLocalItem('loginExpireTime')
  tools.removeLocalItem('sessionExpireTime')
}
// 清楚导入缓存
function clearImportInfo () {
  tools.removeSessionItem('ImportData')
  tools.removeSessionItem('ImportIndex')
  tools.removeSessionItem('ImportName')
  tools.removeSessionItem('ImportSuccessData')
  tools.removeSessionItem('ImportSkipData')
  tools.removeLocalItem('mergeSuccessSign')
}
// 退出
export const toSignOut = () => {
  // if (platform === 'CURRICULUM-PLUGIN') {
  //   return
  // }
  // 如果ipad，退出时直接返回
  if (tools.isComeFromIPad() || tools.isInKindDonate()) {
    return
  }
  tools.sessionItem('USER_SIGN_OUT', true)
  clearImportInfo()
  clearCurrentUser()
  store.dispatch('cgAuth/clearAuth', { sync: true })

  localStorage.removeItem('setNewYearTipOpen')
  // 跳转到当前路径的登录界面
  // $router.push('/login') //本地
  if (platform && platform === 'CURRICULUM-GENIE') {
    window.location.href = window.location.origin + '/#/curriculum-genie/login'
  } else if (platform === 'MAGIC-CURRICULUM') {
    location.reload();
  } else {
    window.location.href = pathName + '/app/index.html#/login' // 线上
  }
  // logout()
  store.dispatch('setCurrentUserAction', null)
  // 关闭所有弹出层，初始化自动退出变量
  MessageBox.close()
  init()
}
// 重置自动退出时间
export const resetTimeout = () => {
  if (!assertRefresh()) {
    setCacheTime()
    return
  }
  setHttpStatus({ portal: '1' })
  assertUserTimeout()
  setCacheTime()
}
// 获取当前时间
function getCurrentTime () {
  return new Date().getTime()
}
// 设置当前隐藏时间
function setCacheTime () {
  var timestamp = new Date().getTime()
  tools.localItem(LAST_OPERATION_TIME, timestamp)
}
// 获取当前隐藏时间
function getCacheTime () {
  return tools.localItem(LAST_OPERATION_TIME)
}
// 判断是否登录、是否记住密码登录、是否弹窗提示剩余时间
function assertRefresh () {
  if (!isAuthenticated || !isAuthenticated()) {
    return false
  }
  if (platform === 'CURRICULUM-PLUGIN') {
    return false
  }
  if (getCurrentUser().rememberMe) {
    return false
  }
  if (isAlertWarnInfo) {
    return false
  }
  // 判断如果是 inkind 三方捐赠界面，不弹框
  if (tools.isInKindDonate()) {
    return false
  }
  return true
}

function handleViewByJudgeTime () {
  if (!assertRefresh()) {
    assertUserTimeout()
    return
  }
  cacheTime = getCacheTime()
  currentTime = getCurrentTime()
  if (cacheTime) {
    cacheTime = parseInt(cacheTime)
    if ((warnTimerNum + cacheTime) <= currentTime && (warnTimerNum + cacheTime + toSignOutNum) > currentTime) {
      setHttpStatus({ portal: '2' })
      var hs = getHttpStatus()
      if (hs) {
        if (hs.indexOf('1') != -1) {
          assertUserTimeout()
          return
        }
      }
      signOutTimer = setTimeout(toSignOut, toSignOutNum)
      isAlertWarnInfo = true
      MessageBox.alert($i18n.t('loc.plsSaveEdit'), $i18n.t('loc.confirmation'), {
        confirmButtonText: $i18n.t('loc.ok'),
        // type: 'warning',
        customClass: 'lg-modal-warning'
      }).then(() => {
        isAlertWarnInfo = false
        assertUserTimeout()
      })
    } else if ((warnTimerNum + cacheTime + toSignOutNum) <= currentTime) {
      toSignOut()
    } else {
      assertUserTimeout()
    }
  } else {
    setCacheTime()
    assertUserTimeout()
  }
}

function assertUserTimeout () {
  if (timer) {
    clearTimeout(timer)
  }
  if (signOutTimer) {
    clearTimeout(signOutTimer)
  }
  timer = setTimeout(handleViewByJudgeTime, 1000 * 60 * 2)
}

// 用户操作时触发重新设置当前隐藏时间
$(document).on('keypress mousedown', function () {
  if (!assertRefresh()) {
    setCacheTime()
    return
  }
  setHttpStatus({ portal: '1' })
  assertUserTimeout()
  setCacheTime()
})

// 续期 session 时间
export const renewalSessionTime = ()=> {
  var currentTime = Date.now(); // 获取当前时间戳
  localStorage.setItem('sessionExpireTime', (currentTime + AUTO_RENEWAL_TIME).toString());
}

let timer = null // 时间戳
let signOutTimer = null // 退出时间
let warnTimerNum = INACTIVITY_TIME_OUT // 提醒时间
let toSignOutNum = TO_SIGN_OUT_NUM // 自动退出时间
let isAlertWarnInfo = false // 警告是否弹出
let cacheTime, currentTime // 隐藏时间，当前时间

function init () {
  timer = null // 时间戳
  signOutTimer = null // 退出时间
  isAlertWarnInfo = false // 警告是否弹出

  cacheTime = getCacheTime()
  currentTime = getCurrentTime()

  if (cacheTime) {
    cacheTime = parseInt(cacheTime)
    if ((warnTimerNum + cacheTime + toSignOutNum) <= currentTime && assertRefresh()) {
      toSignOut()
    } else {
      assertUserTimeout()
    }
  } else {
    setCacheTime()
    assertUserTimeout()
  }
  // 如果ipad，设置定时器，每分钟刷新一次自动退出时间
  if (tools.isComeFromIPad()) {
    setInterval(() => {
      resetTimeout()
    }, 1000 * 60 * 1)
  }
}

init()
