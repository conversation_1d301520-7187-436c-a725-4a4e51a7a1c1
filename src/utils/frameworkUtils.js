import constants from '@/utils/constants'
import { equalsIgnoreCase } from '@/utils/common'

export default {
  // 从 domains 中收集所有的测评点数据
  collectMeasures (measures, domains) {
    // 如果 domains 不为空
    if (domains) {
      domains.forEach(item => {
        if (item.children) {
          this.getMeasuresBottom(item.children, measures)
        }
      })
      this.convertMeasuresToMap(measures)
    }
    return measures
  },
  // 将测评点转换为 Map
  convertMeasuresToMap (measures) {
    this.measuresMap = measures.reduce((map, obj) => {
      map[obj.id] = obj
      return map
    }, {})
  },
  // 获取底层测评点
  getMeasuresBottom (children, measuresBottom) {
    if (!children || children.length === 0) {
      return
    }
    children.forEach(v => {
      const childrenNext = v.children
      if (!childrenNext || childrenNext.length === 0) {
        measuresBottom.push(v)
      } else {
        this.getMeasuresBottom(childrenNext, measuresBottom)
      }
    })
  },

  // 获取底层测评点的 ID 数组
  getMeasuresBottomIds (children, measuresBottom) {
    if (!children || children.length === 0) {
      return
    }
    children.forEach(v => {
      const childrenNext = v.children
      if (!childrenNext || childrenNext.length === 0) {
        measuresBottom.push(v.id)
      } else {
        this.getMeasuresBottomIds(childrenNext, measuresBottom)
      }
    })
  },

  // 判断是否是 CA-PTKLF 框架
  isCAPTKLF (frameworkId) {
    return frameworkId &&
      (frameworkId.toUpperCase() === constants.caPtklfFrameworkIdForPS || frameworkId.toUpperCase() === constants.caPtklfFrameworkIdForTK)
  },

  /**
   * 获取框架缩写
   * @param frameworkId 框架 ID
   */
  getFrameworkAbbr (frameworkId) {
    // 如果框架 ID 不为空，全部转化为大写
    frameworkId = frameworkId ? frameworkId.toUpperCase() : ''
    // Map<框架 ID, 框架缩写>
    // 框架缩写映射
    const frameworkAbbrMap = constants.lessonFrameworkAbbrMap

    return frameworkAbbrMap.get(frameworkId)
  },

  /**
   * 判断是否是映射的框架
   * @param frameworkId 框架 ID
   * @returns {boolean} 是否是映射的框架
   */
  isMELS (frameworkId) {
    const frameworkIds = [
      constants.frameworkMelsInfantId,
      constants.frameworkMelsYoungToddlerId,
      constants.frameworkMelsToddlerId,
      constants.frameworkMelsPsPkId,
      constants.frameworkMelsTkId
    ]
    return frameworkId && frameworkIds.some(id => id.toLowerCase() === frameworkId.toLowerCase())
  },

  /**
   * 判断是否是映射的框架
   * @param frameworkId 框架 ID
   * @returns {boolean} 是否是映射的框架
   */
  isILEARN (frameworkId) {
    const frameworkIds = [
      constants.frameworkIllinoisPsPkId,
      constants.frameworkIllinoisTkId
    ]
    return frameworkId && frameworkIds.some(id => id.toLowerCase() === frameworkId.toLowerCase())
  },

  /**
   * 根据缩写获取测评点信息
   * @param domains 领域信息
   * @param abbr 测评点缩写
   * @returns {{abbreviation}|*|null} 测评点信息
   */
  getMeasureByAbbr (domains, abbr) {
    if (!abbr || !domains || domains.length === 0) {
      return null
    }
    // 遍历框架
    for (let i = 0; i < domains.length; i++) {
      const domain = domains[i]
      // 遍历测评点
      for (let j = 0; j < domain.children.length; j++) {
        const measure = domain.children[j]
        if (measure.abbreviation && measure.abbreviation.trim() === abbr) {
          return measure
        }
      }
    }
    return null
  },

  /**
   * 判断国家是否需要包含州信息（美国和加拿大需要包含州信息）
   * @param country 国家名称
   * @returns {boolean} 是否需要包含州信息
   */
  isCountryWithState (country) {
    return equalsIgnoreCase(country, 'United States') || equalsIgnoreCase(country, 'Canada')
  }

}
