
class StatsUtil {
  // 获取班级 ID 列表参数，如果选择的学校下所有班级，则返回空数组
  getRequestGroupIds (selectedCenters, selectedGroupIds) {
    let groupIds = []
    // 判断是否选择所有班级，如果选择部分班级，则按照班级统计
    if (selectedCenters && selectedCenters.length > 0 && selectedGroupIds && selectedGroupIds.length > 0) {
      let allGroups = []
      // 获取选中学校下所有班级
      selectedCenters.forEach(c => {
        c.groups.forEach(g => {
          if (g.id !== 'all_groups') {
            allGroups.push(g)
          }
        })
      })
      // 如果选中的班级和所有班级数量不等，则按照班级统计
      if (selectedGroupIds.length !== allGroups.length) {
        groupIds = selectedGroupIds
      } else if (selectedGroupIds.length === 1) {
        // 如果学校中只有一个班级，则按小孩统计
        groupIds = selectedGroupIds
      }
    }
    return groupIds
  }
}

export default new StatsUtil()