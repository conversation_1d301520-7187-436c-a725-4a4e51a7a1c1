import store from '@/store'
import axios from 'axios'

export const oldKey = 'mpfj2' // Web端标识

// 回调函数
const events = {
  'onConnectFailed': function (resp) {
    console.log('onConnectFailed',resp)
  },
  'onConnect': function (resp) {
    localStorage.setItem('chatIsHome', true)
    store.dispatch('setIsContChat', true)
  },
  'onMessageDelivered': function (resp) {
    console.log('onMessageDelivered',resp)
  },
  'onMessageRead': function (resp) {
    // called when a message is read by the receiver
    console.log('onMessageRead',resp)
  },
  'onMessageDeleted': function (resp) {
    console.log('onMessageDeleted',resp)
  },
  'onConversationDeleted': function (resp) {
    console.log('onConversationDeleted',resp)
  },
  'onUserConnect': function (resp) {
    console.log('onUserConnect',resp)
  },
  'onUserDisconnect': function (resp) {
    console.log('onUserDisconnect',resp)
  },
  'onConversationReadFromOtherSource': function (resp) {
    console.log('onConversationReadFromOtherSource',resp)
  },
  'onConversationRead': function (resp) {
    console.log('onConversationRead',resp)
    // called when the conversation thread is read by the receiver
  },
  'onMessageReceived': function (resp) {
    console.log('onMessageReceived',resp)
    let message = resp.message
    if (message.metadata && message.metadata.category && message.metadata.category == 'HIDDEN') {
      console.log(message.message)
      switch (message.message) {
        case 'HEALTH_CARD':
          store.dispatch('addSignVersion')
          break
        case 'ADD_HEALTH_CARD':
          store.dispatch('addHealthCardVersion')
          break
        case 'HEALTH_CHECK_CARD':
          store.dispatch('addHealthCheckCardVersion') // 视觉健康检查卡
          break
      }
      return
    }
    // called when a new message is received

    if (message.contentType !== 4 && message.contentType !== 10 && message.contentType !== 103) {
      store.dispatch('setMessages', {
        avatar_url: message.metadata.avatarUrl,
        display_name: message.metadata.disPlayName
      })
      store.dispatch('setMessagesHasMsg', true)
      // store.dispatch('setWebChatCount', true)
      setTimeout(function () {
        store.dispatch('setMessagesHasMsg', false)
      }, 6000)
    }
  },
  'onMessageSentUpdate': function (resp) {
    console.log('onMessageSentUpdate',resp)
  },
  'onMessageSent': function (resp) {
    console.log('onMessageSent',resp)
  },
  'onUserBlocked': function (resp) {
    console.log('onUserBlocked',resp)
  },
  'onUserUnblocked': function (resp) {
    console.log('onUserUnblocked',resp)
  },
  'onUserActivated': function (resp) {
    console.log('onUserActivated',resp)
  },
  'onUserDeactivated': function (resp) {
    console.log('onUserDeactivated',resp)
  },
  'connectToSocket': function (resp) {
    console.log('connectToSocket',resp)
  },
  'onMessage': function (resp) {
    console.log('onMessage',resp)
  },
  'onTypingStatus': function (resp) {
    console.log('onTypingStatus',resp)
  }
}

export const applozicUserInfo = function () {
  let currentUser = store.getters.currentUser ? store.getters.currentUser : ''
  if (!currentUser) {
    return
  }
  if (currentUser.commInfo) {
    if (currentUser.webChatOpen) {
      // 当前用户没有开启学院模式
      if (!currentUser.academy_open) {
        return currentUser.commInfo
      }
    }
  }
  return false
}
// 登录Applozic
export const login = function (initEvent) {
  let info = applozicUserInfo()
  if (!info) {
    return Promise.reject('No User Info')
  }
  let href = window.location.href
  // 聊天页面必须加载正确的回调事件
  if (href.indexOf('webChat') > 0 && !initEvent) {
    return
  }
  return new Promise(function (resolve, reject) {
    Applozic.ALApiService.login(
      {
        data: {
          baseUrl: 'https://apps.applozic.com',
          alUser:
            {
              userId: info.username,
              password: info.password,
              appVersionCode: 111,
              applicationId: info.appKey,
              fileupload: 'awsS3Server'
            }
        },
        success: function (response) {
          if (!response.authToken) reject('Wrong Applozic password,Please refresh later.')
          // 初始化  This method initializes socket connection
          window.Applozic.ALSocket.init(info.appKey, response, initEvent || events)
          if (response.totalUnreadCount > 0) {
            store.dispatch('setWebChatCount', response.totalUnreadCount)
          } else {
            store.dispatch('setWebChatCount', 0)
          }
          resolve(response)
        },
        error: function (error) {
          console.log(error)
          reject()
        }
      }
    )
  })
}

// 登出Applozic
export const logOut = function () {
  console.log('Applozic logOut')
  window.Applozic.AlCustomService.logout()
  let isContChat = localStorage.getItem('isContChat')
  if (isContChat === 'false') {
    let env = process.env.VUE_APP_CURRENTMODE
    let url = '/v2/#/webChat'
    if (env === 'local') {
      url = '/#/webChat'
    }
    let open = window.open(url, 'chat')
    open.close()
  }
}

// 发送群组消息
export const sendMessage = function (groupId, msg, file, metadata) {
  if (!file) {
    return new Promise((resolve, reject) => {
      Applozic.ALApiService.sendMessage({
        data: {
          message: {
            'type': 5,
            'contentType': 0,
            'message': msg,
            'groupId': groupId,
            'metadata': metadata,
            'key': oldKey,
            'source': 1
          }
        },
        success: function (response) {
          resolve(response)
        },
        error: function () {
          reject()
        }
      })
    })
  } else {
    let message = {
      'type': 5,
      'message': msg,
      'groupId': groupId,
      'metadata': metadata,
      'key': oldKey,
      'source': 1
    }
    return new Promise((resolve, reject) => {
      window.Applozic.ALApiService.sendAttachment({
        data: {
          file: file,
          messagePxy: message
        },
        success: function (result) {
          resolve(result)
        },
        error: function () {
          reject()
        }
      })
    })
  }
}

// 发送广播消息
export const sendBroadcastMessage = function (groupIds, msg, fileMeta, metadata, userId) {
  let info = applozicUserInfo()
  if (!info) {
    return Promise.reject('No User Info')
  }
  let body = {
    clientGroupIds: groupIds,
    messageObject: {
      metadata: metadata
    }
  }
  let headers = {
    'content-type': 'application/json',
    'application-key': info.appKey,
    'authorization': 'Basic Ym90OkxlYXJuaW5nMjAyMCE=',
    'of-user-id': userId
  }
  if (!fileMeta) {
    body.messageObject.message = msg
  } else {
    body.messageObject.fileMeta = fileMeta
  }
  return axios.post('https://apps.applozic.com/rest/ws/message/sendall', body, {
    headers: headers
  })
}

// 获取用户的最近消息列表
export const getMessages = function () {
  return new Promise(function (resolve, reject) {
    Applozic.ALApiService.getMessages({
      data:
        {
          startIndex: 0,
          mainPageSize: 600
        },
      success: function (response) {
        let data = response.data
        let groupFeeds = data.groupFeeds
        let currentApplozicUserId = applozicUserInfo().username
        let activeGroup = []
        for (let i = 0; i < groupFeeds.length; i++) {
          let groupFeed = groupFeeds[i]
          let removedMembersId = groupFeed.removedMembersId
          let metadata = groupFeed.metadata
          if (!metadata) {
            if (removedMembersId.indexOf(currentApplozicUserId) < 0) {
              activeGroup.push(groupFeed)
            }
          } else {
            if (removedMembersId.indexOf(currentApplozicUserId) < 0 && !(metadata.hasOwnProperty('isDelete') && metadata.isDelete === 'true')) {
              activeGroup.push(groupFeed)
            }
          }
        }
        response.data.groupFeeds = activeGroup

        resolve(response)
      },
      error: function () {
        reject()
      }
    })
  })
}

// 拉取群组的消息 endTime为毫秒值
export const getMessagesByGroupId = function (groupId, endTime) {
  let data = {
    startIndex: 0,
    groupId: groupId,
    pageSize: 50
  }
  if (endTime) {
    data.endTime = endTime
  }
  return new Promise((resolve, reject) => {
    Applozic.ALApiService.getMessages({
      data: data,
      success: function (response) {
        if (response.status === 'success') {
          if (response.data.message.length > 0) {
            let filter = response.data.message.filter(m => m.metadata && !(m.metadata.hasOwnProperty('show') && m.metadata.show))
            let filter2 = filter.filter(m => {
              return m.contentType != 10
            })
            if (filter2.length > 0) {
              response.data.message = filter2
            }
          }
          resolve(response)
        } else {
          reject()
        }
      },
      error: function () {
        reject()
      }
    })
  })
}

export const getFileUrl = function (blobKey, authToken) {
  return axios.get('https://apps.applozic.com/rest/ws/file/url', {
    params: {
      key: blobKey
    },
    headers: {
      'X-Authorization': authToken
    }
  })
}
