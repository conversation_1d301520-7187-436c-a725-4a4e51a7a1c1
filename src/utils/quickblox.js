// // import QuickBlox from 'quickblox/quickblox.min'
// import QuickBlox from 'quickblox/quickblox'
// import store from '@/store'
// import { localEnv } from '@/utils/setBaseUrl' // 配置文件
// import fileUtil from '@/utils/file'
// import axios from '@/utils/axios' // 上传 S3
// var showLog = false
// var isLogin = false
// // 私聊类型常量定义为 3
// var privateChatType = 3
// var CONFIG = {
//   // 流处理防止消息丢失
//   streamManagement: {
//     enable: true
//   },
//   // 日志输出 产品环境不输出日志信息
//   debug: { mode: localEnv == 'local' ? 0 : 0 },
//   on: {
//     // 会话中断事件
//     sessionExpired: []
//   },
//   pingTimeout: 1,
//   pingLocalhostTimeInterval: 5,
//   chatReconnectionTimeInterval: 3,
//   callBackInterval: 5
// }
// var sendMessageResolve = new Map()
// var APPLICATION_ID = null
// var AUTH_KEY = null
// var AUTH_SECRET = null
// var ACCOUNT_KEY = null
// // 初始化连接
// export const init = function () {
//   let currentUser = store.getters.currentUser ? store.getters.currentUser : ''
//   if (!currentUser || !currentUser.commInfo) {
//     return false
//   }
//   APPLICATION_ID = parseInt(currentUser.commInfo.applicationId)
//   AUTH_KEY = currentUser.commInfo.authKey
//   AUTH_SECRET = currentUser.commInfo.authSecret
//   ACCOUNT_KEY = currentUser.commInfo.accountKey
//   log('初始化QB', APPLICATION_ID, AUTH_KEY, AUTH_SECRET, ACCOUNT_KEY)
//   // 产品环境使用本地端点
//   if (currentUser.commInfo.apiServer && currentUser.commInfo.imServer) {
//     ACCOUNT_KEY = ''
//     CONFIG.endpoints = {
//       api: currentUser.commInfo.apiServer.replace(/^https?:\/\//, ''),
//       chat: currentUser.commInfo.imServer.replace(/^https?:\/\//, '')
//     }
//   }
//   if (ACCOUNT_KEY) {
//     QuickBlox.init(APPLICATION_ID, AUTH_KEY, AUTH_SECRET, ACCOUNT_KEY, CONFIG)
//   } else {
//     QuickBlox.init(APPLICATION_ID, AUTH_KEY, AUTH_SECRET, CONFIG)
//   }
// }
// // 注册事件需要在初始化后
// init()

// function log (...array) {
//   if (showLog) {
//     console.log('LGLog', array)
//   }
// }
// // 同时发生多个请求时，只有一个请求成功，其他请求延迟执行
// var flag = false
// var timerList = []
// // 启动定时器
// setInterval(() => {
//   timerList.forEach(item => {
//     item.exe()
//   })
// }, 1000)
// // 接口报错重试机制
// async function retry (error, exe, params, methodName, dialogJid, time) {
//   if (flag) {
//     if (!time) {
//       let time = new Date().getTime()
//       timerList.push({ id: time, exe: () => retry(error, exe, params, methodName, dialogJid, time) })
//     }
//     return
//   } else {
//     flag = true
//     if (time) {
//       timerList = timerList.filter(item => item.id != time)
//     }
//   }
//   if (error) {
//     axios.get('/test', {
//       params: { methodName: methodName, errorCode: error.code, errorMessage: JSON.stringify(error) }
//     })
//     log('error', error, methodName, dialogJid)
//     if (error.code == 401 || error.code == 404 || error.code == 503 || error.code == 500) {
//       await login()
//       if (dialogJid) {
//         await Promise.race([joinRoom(dialogJid), new Promise((resolve, reject) => {
//           setTimeout(() => resolve(false), 1000 * 3)
//         })
//         ])
//       }
//       if (exe) {
//         exe(params)
//       }
//     } else if (error.code == 422) {
//       log('422 error', error)
//       if (error.detail && error.detail instanceof Array && error.detail.includes('No one can receive the message')) {
//         log('push 422 error', error)
//         flag = false
//         return
//       }
//       if (methodName.indexOf('events.create') > -1) {
//         log('push 422 error', error)
//         flag = false
//         return
//       }
//       // 422错误重新登录
//       await login()
//       if (dialogJid) {
//         await Promise.race([joinRoom(dialogJid), new Promise((resolve, reject) => {
//           setTimeout(() => resolve(false), 1000 * 3)
//         })
//         ])
//       }
//       if (exe) {
//         exe(params)
//       }
//     } else if (error.name === 'ChatNotConnectedError') {
//       await login()
//       if (dialogJid) {
//         await Promise.race([joinRoom(dialogJid), new Promise((resolve, reject) => {
//           setTimeout(() => resolve(false), 1000 * 3)
//         })
//         ])
//       }
//       if (exe) {
//         exe(params)
//       }
//     } else {
//       log('其他错误', error)
//       if (exe) {
//         exe(params)
//       }
//     }
//   }
//   flag = false
// }
// // 获取登录信息
// export const getUserInfo = function () {
//   let currentUser = store.getters.currentUser ? store.getters.currentUser : ''
//   if (!currentUser) {
//     return false
//   }
//   if (currentUser.commInfo) {
//     if (currentUser.webChatOpen) {
//       // 当前用户没有开启学院模式
//       if (!currentUser.academy_open) {
//         // return currentUser.commInfo
//         return { login: currentUser.commInfo.username, password: currentUser.commInfo.password }
//       }
//     }
//   }
//   return false
// }
// function onSentMessageCallback (messageLost, messageSent) {
//   log('发送消息监听', messageLost, messageSent)
//   if (messageLost) {
//     // 发送失败尝试重试
//     let key = messageLost.id
//     if (sendMessageResolve.has(key)) {
//       let model = sendMessageResolve.get(key)
//       // init()
//       // QuickBlox.chat.disconnect()
//       // model.lostFunction(model.lostParam)
//       sendMessageResolve.delete(key)
//       retry({ code: 401 }, model.lostFunction, model.lostParam, 'SentMessageRetry', messageLost.extension.dialog_id)
//     }
//     axios.get('/test', {
//       params: { methodName: 'onSentMessageLost', errorCode: 500, errorMessage: JSON.stringify({ messageLost: messageLost, messageSent: messageSent }) }
//     })
//   } else {
//     // 发送成功触发通知
//     let key = messageSent.id
//     if (sendMessageResolve.has(key)) {
//       let model = sendMessageResolve.get(key)
//       model.function(model.param)
//       sendMessageResolve.delete(key)
//     }
//   }
// }

// function onJoinOccupant (dialogId, userId) {
//   // log('接收有人进群事件', dialogId, userId)
// }
// function onKickOccupant (dialogId, userId) {
//   store.dispatch('addChatListVersion')
//   // log('离群事件', dialogId, userId)
// }
// function onSystemMessage (receivedMessage) {
//   log('接收系统消息事件', receivedMessage)
//   let userId = store.getters.login ? store.getters.login.user_id : ''
//   if (receivedMessage && receivedMessage.extension) {
//     switch (receivedMessage.extension.type) {
//       case 'NOTIFY':
//         if (receivedMessage.userId != userId) {
//           switch (receivedMessage.extension.pushType) {
//             case 'HEALTH_CARD':
//               store.dispatch('addSignVersion', receivedMessage.extension.childIds) // 签到
//               break
//             case 'ADD_HEALTH_CARD':
//               store.dispatch('addHealthCardVersion', receivedMessage.extension.childIds) // 健康检查卡
//               break
//             case 'HEALTH_CHECK_CARD':
//               store.dispatch('addHealthCheckCardVersion', receivedMessage.extension.childIds) // 视觉健康检查卡
//               break
//           }
//         }
//         break
//       case 'RECALL':
//         store.dispatch('recallMessage', receivedMessage)
//         log('撤回消息', receivedMessage)
//         break
//       case 'MESSAGE':
//         if (receivedMessage.userId != userId) {
//           log('新消息提醒', receivedMessage)
//           store.dispatch('setMessages', {
//             avatar_url: receivedMessage.extension.user_icon_url,
//             display_name: receivedMessage.extension.user_name
//           })
//           store.dispatch('setMessagesHasMsg', true)
//           // 未读红点显示
//           // store.dispatch('setWebChatCount', true)
//           setTimeout(function () {
//             store.dispatch('setMessagesHasMsg', false)
//           }, 6000)
//         }
//         break
//     }
//   }
// }

// // 监听接收消息事件
// function onMessageError (messageId, error) {
//   log('消息失败事件', messageId, error)
//   let sendMessageFailIds = store.state.chat.sendMessageFailIds
//   // 去重更新数组
//   !sendMessageFailIds.includes(messageId) && sendMessageFailIds.push(messageId)
//   store.commit('SET_SEND_MESSAGE_FAIL_IDS', store.state.chat.sendMessageFailIds)
//   // 获取到新消息
//   error.messageId = messageId
//   store.dispatch('sendMessageError', error)
//   axios.get('/test', {
//     params: { methodName: 'onMessageError', errorCode: 200, errorMessage: JSON.stringify({ messageId: messageId, error: error }) }
//   })
//   // 触发聊天列表静默刷新
//   // store.dispatch('addChatListVersion')
//   // 新消息提醒
// }

// // 监听接收消息事件
// function onMessage (userId, message) {
//   log('接收消息事件', userId, message)
//   // 获取到新消息
//   message.sender_id = userId
//   store.dispatch('addNewMessage', message)
//   // 触发聊天列表静默刷新
//   // store.dispatch('addChatListVersion')
//   // 新消息提醒
// }
// function onDisconnectedListener () {
//   log('断开连接事件')
//   joinRoomMap = new Map()
//   axios.get('/test', {
//     params: { methodName: 'onDisconnectedListener', errorCode: 200 }
//   })
// }

// function onReconnectListener () {
//   log('重新连接事件')
//   joinRoomMap = new Map()
//   axios.get('/test', {
//     params: { methodName: 'onReconnectListener', errorCode: 200 }
//   })
//   // connect()
// }
// // // 登录聊天服务器
// // export const login = function (flag) {
// //   if (isLogin && !flag && store.getters && store.getters.login) {
// //     return Promise.resolve(store.getters.login)
// //   }
// //   isLogin = true
// //   // 获取用户信息
// //   let info = getUserInfo()
// //   if (!info) {
// //     // 没有用户信息
// //     isLogin = false
// //     return Promise.reject(new Error('No User Info'))
// //   }
// //   // 调取登录
// //   return new Promise((resolve, reject) => {
// //     // 登录 QuickBlox
// //     QuickBlox.createSession(info, function (error, result) {
// //       if (error) {
// //         isLogin = false
// //         reject(error)
// //       } else {
// //         store.dispatch('setLogin', result)
// //         connect().then(x => {
// //           isLogin = false
// //           resolve(result)
// //         }).catch(e => {
// //           isLogin = false
// //           resolve(result)
// //         })
// //       }
// //     })
// //     setTimeout(() => {
// //       if (isLogin) {
// //         isLogin = false
// //       }
// //     }, 1000 * 10)
// //   })
// // }

// async function QBCreateSession () {
//   return new Promise(function (resolve, reject) {
//     QuickBlox.createSession(function (error, result) {
//       if (error) {
//         reject(error)
//       } else {
//         resolve(result)
//       }
//     })
//   })
// }

// async function loginUser (info) {
//   log('loginUser', info)
//   return new Promise(function (resolve, reject) {
//     QuickBlox.login(info, function (error, result) {
//       if (error) {
//         reject(error)
//       } else {
//         resolve(result)
//       }
//     })
//   })
// }

// async function chatConnect (userId, password) {
//   log('chatConnect', userId, password)
//   return new Promise(function (resolve, reject) {
//     QuickBlox.chat.connect({
//       jid: QuickBlox.chat.helpers.getUserJid(userId, APPLICATION_ID),
//       password: password
//     }, function (error, result) {
//       if (error) {
//         reject(error)
//       } else {
//         resolve(result)
//       }
//     })
//   })
// }

// function setListeners () {
//   QuickBlox.chat.onSystemMessageListener = onSystemMessage
//   QuickBlox.chat.onMessageListener = onMessage
//   QuickBlox.chat.onMessageErrorListener = onMessageError
//   QuickBlox.chat.onDisconnectedListener = onDisconnectedListener
//   QuickBlox.chat.onReconnectListener = onReconnectListener
//   QuickBlox.chat.onJoinOccupant = onJoinOccupant
//   QuickBlox.chat.onKickOccupant = onKickOccupant
//   QuickBlox.chat.onSentMessageCallback = onSentMessageCallback
// }

// async function connect () {
//   // 停止重连
//   stopReconnecting()
//   // 登录
//   try {
//     var isLogin = await login()
//   } catch (e) {
//     // 初始化QB
//     init()
//     // 再次登录
//     isLogin = await login()
//   }
//   // 设置监听
//   setListeners()

//   // 登入失败退出
//   if (!isLogin) {
//     // let env = process.env.VUE_APP_CURRENTMODE
//     // let url = '/v2/#/webChat'
//     // if (env === 'local') {
//     //   url = '/#/webChat'
//     // }
//     // window.open(url, 'chat').close()
//   }
//   return Promise.resolve()
// }
// // 重连
// var timer = null

// function reconnecting (interval) {
//   log('重连', interval)
//   timer = setInterval(connect, interval)
// }

// function stopReconnecting () {
//   clearInterval(timer)
// }

// export const QBLogin = async function (info) {
//   // 创建 Session
//   var session = await QBCreateSession()
//   // 登录
//   var userData = await loginUser(info)
//   session.user_id = userData.id
//   store.dispatch('setLogin', session)
//   // 连接聊天服务器
//   await chatConnect(userData.id, info.password).then(function () {
//     isLogin = true
//   })
//   reconnecting(1800000)

//   return Promise.resolve(session)
// }

// // 登录聊天服务器
// export const login = async function (flag) {
//   // 获取用户信息
//   let info = getUserInfo()
//   if (!info) {
//     // 没有用户信息
//     return false
//   }
//   // 有用户信息进行 QB 登录
//   let login = await QBLogin(info)
//   setListeners()
//   return login
// }

// // 判断是否存在会话，如果没有则重新登录
// export const isSession = function (isConnect, i = 0) {
//   return new Promise(function (resolve, reject) {
//     try {
//       QuickBlox.getSession(function (error, session) {
//         if (error) {
//           log('Session invalid', error)
//           login().then(() => {
//             if (isConnect) {
//               unreadCount()
//             }
//             resolve()
//           }).catch(e => {
//             log('login invalid', e)
//             reject(e)
//           })
//         } else {
//           log('Session exist', session, isConnect)
//           if (isConnect) {
//             unreadCount()
//           }
//           resolve()
//         }
//       })
//     } catch (error) {
//       log('Session invalid', error)
//       reject(error)
//     }
//   })
// }

// // 注销 QuickBlox
// export const logout = function () {
//   return new Promise((resolve, reject) => {
//     if (QuickBlox.logout) {
//       QuickBlox.destroySession(function (error) {
//         if (error) {
//           reject(error)
//         } else {
//           let env = process.env.VUE_APP_CURRENTMODE
//           let url = '/v2/#/webChat'
//           if (env === 'local') {
//             url = '/#/webChat'
//           }
//           window.open(url, 'chat').close()
//           resolve({})
//         }
//       })
//     }
//   })
// }
// // 注销 QuickBlox
// export const unreadCount = function () {
//   QuickBlox.chat.message.unreadCount({
//   }, function (error, result) {
//     log('未读消息数', result, error)
//     if (error) {
//     } else {
//       if (result.total > 0) {
//         store.dispatch('setWebChatCount', result.total)
//       } else {
//         store.dispatch('setWebChatCount', result.total)
//       }
//     }
//   })
// }
// // export const connect = function () {
// //   if (store.getters && store.getters.login) {
// //     var userId = store.getters.login.user_id
// //     var password = store.getters.login.token
// //     var params = { userId, password }
// //     let info = getUserInfo()
// //     if (!info) {
// //       // 没有用户信息
// //       isLogin = false
// //       return Promise.reject(new Error('No User Info'))
// //     }
// //     return new Promise((resolve, reject) => {
// //       QuickBlox.login(info, function (error, result) {
// //         log('登录聊天服务器', result, error)
// //         if (error) {
// //           // retry(error, reject, error, 'QuickBlox.login')
// //         }
// //         log('连接中', params)
// //         QuickBlox.chat.connect(params, function (error, contactList) {
// //           log('连接聊天服务器', contactList, error)
// //           QuickBlox.chat.onSystemMessageListener = onSystemMessage
// //           QuickBlox.chat.onMessageListener = onMessage
// //           QuickBlox.chat.onMessageErrorListener = onMessageError
// //           QuickBlox.chat.onDisconnectedListener = onDisconnectedListener
// //           QuickBlox.chat.onReconnectListener = onReconnectListener
// //           QuickBlox.chat.onJoinOccupant = onJoinOccupant
// //           QuickBlox.chat.onKickOccupant = onKickOccupant
// //           QuickBlox.chat.onSentMessageCallback = onSentMessageCallback
// //           if (error) {
// //             retry(error, reject, error, 'QuickBlox.chat.connect')
// //           } else {
// //             QuickBlox.chat.message.unreadCount({
// //             }, function (error, result) {
// //               log('未读消息数', result, error)
// //               if (error) {
// //               } else {
// //                 if (result.total > 0) {
// //                   store.dispatch('setWebChatCount', true)
// //                 } else {
// //                   store.dispatch('setWebChatCount', false)
// //                 }
// //               }
// //             })
// //             resolve(contactList)
// //           }
// //         })
// //       })
// //     })
// //   } else {
// //     throw new Error('Not Login')
// //   }
// // }
// var joinRoomMap = new Map()
// // 连接聊天房间
// export const joinRoom = function (dialogId, i = 0) {
//   return new Promise((resolve, reject) => {
//     if (joinRoomMap.has(dialogId) && joinRoomMap.get(dialogId) > new Date().getTime() - 1000 * 60 * 10) {
//       log('连接已存在', joinRoomMap.get(dialogId) - (new Date().getTime() - 1000 * 60 * 10))
//       resolve(true)
//       return
//     }
//     var dialogJid = QuickBlox.chat.helpers.getRoomJidFromDialogId(dialogId)
//     try {
//       QuickBlox.chat.muc.join(dialogJid, function (error, result) {
//         if (error) {
//           for (var i = 0; i < error.childNodes.length; i++) {
//             var elItem = error.childNodes.item(i)
//             if (elItem.tagName === 'error') {
//               joinRoomMap.delete(dialogId)
//               log('连接聊天房间失败', error)
//               axios.get('/test', {
//                 params: { methodName: 'joinRoom', errorCode: error.code, errorMessage: JSON.stringify(error) }
//               })
//               resolve(false)
//             }
//           }
//         } else {
//           log('连接聊天房间', result, error)
//         }
//         joinRoomMap.set(dialogId, new Date().getTime())
//         resolve(true)
//       })
//     } catch (e) {
//       if (e.name === 'ChatNotConnectedError') {
//         // not connected to chat
//       }
//       joinRoomMap.delete(dialogId)
//       log('连接聊天房间失败', e)
//       axios.get('/test', {
//         params: { methodName: 'joinRoom', errorCode: e.code, errorMessage: JSON.stringify(e) }
//       })
//       resolve(false)
//     }
//   })
// }

// // 获取聊天群组信息
// export const getGroupInfo = function (dialogId) {
//   let params = {
//     _id: dialogId,
//     limit: 1
//   }
//   return new Promise((resolve, reject) => {
//     QuickBlox.chat.dialog.list(params, function (error, dialogs) {
//       if (error) {
//         retry(error, reject, error, 'QuickBlox.chat.dialog.list', dialogId)
//       } else {
//         log('聊天群组信息', dialogs)
//         if (dialogs.items && dialogs.items.length > 0) {
//           let dialog = dialogs.items[0]
//           dialog.occupants_ids = dialog.occupants_ids.filter(x => dialog.user_id != x)
//           resolve(dialogs.items[0])
//         } else {
//           reject(new Error('Not Have Dialogs'))
//         }
//       }
//     })
//   })
// }
// // 获取聊天列表
// export const getMessages = function () {
//   let params = {
//     type: {
//       in: [2, 3]
//     },
//     sort_desc: 'last_message_date_sent', // 按照最后一次发送消息时间降序排列
//     limit: 200
//   }
//   return new Promise((resolve, reject) => {
//     QuickBlox.chat.dialog.list(params, function (error, dialogs) {
//       if (error) {
//         retry(error, reject, error, 'QuickBlox.chat.dialog.list')
//       } else {
//         let newItems = []
//         for (const dialog of dialogs.items) {
//           dialog.occupants_ids = dialog.occupants_ids.filter(x => dialog.user_id != x)
//           if (dialog.data && !dialog.data.deleted) {
//             newItems.push(dialog)
//           }
//         }
//         dialogs.items = newItems
//         resolve(dialogs)
//       }
//     })
//   })
// }

// // 获取聊天记录
// export const getMessagesByGroupId = function (dialogId, endTime) {
//   var params = {
//     markAsRead: true,
//     chat_dialog_id: dialogId,
//     sort_desc: 'date_sent',
//     limit: 50,
//     skip: 0
//   }
//   if (endTime) {
//     params.date_sent = {
//       lte: endTime
//     }
//   }
//   return new Promise((resolve, reject) => {
//     QuickBlox.chat.message.list(params, function (error, messages) {
//       if (error) {
//         retry(error, reject, error, 'QuickBlox.chat.message.list', dialogId)
//       } else {
//         messages.items.reverse()
//         log('聊天记录', messages, endTime)
//         resolve(messages)
//       }
//     })
//   })
// }

// // 获取文件内容的私有Url
// export const getFileUrl = function (fileId, isPublic) {
//   let fileUrl = ''
//   if (isPublic) {
//     fileUrl = QuickBlox.content.publicUrl(fileId)
//   } else {
//     fileUrl = QuickBlox.content.privateUrl(fileId)
//   }
//   log('文件路径', fileUrl)
//   return fileUrl
// }
// function getAttachmentType (type, result) {
//   let attachmentType = ''
//   switch (type) {
//     case 'Image':
//       attachmentType = 'image'
//       break
//     case 'Video':
//       // 区分封面，和视频result.type
//       attachmentType = 'video'
//       if (result && result.content_type.indexOf('image')) {
//         attachmentType = 'image'
//       }
//       break
//     case 'Audio':
//       attachmentType = 'audio'
//       break
//   }
//   return attachmentType
// }

// export const uploadAllFile = async function (files, type) {
//   let resultList = []
//   if (files && files.length > 0) {
//     resultList = await uploadFiles(files)
//   }
//   let extension = {}
//   if (resultList.length > 0) {
//     extension.content_type = getAttachmentType(type)
//     extension.attachments = resultList.map(result => {
//       if (result.error) {
//         console.log('上传文件失败', result)
//         return result
//       }
//       return {
//         id: result.uid,
//         lgId: result.lgId,
//         content_type: result.content_type,
//         url: result.url,
//         size: result.size,
//         name: result.name,
//         type: getAttachmentType(type, result)
//       }
//     })
//   }
//   return extension
// }
// // 向群组发送消息
// export const sendMessage = async function (dialog, dialogId, msg, metadata, isRetry, retryMessage) {
//   let message = {}
//   if (isRetry && retryMessage && retryMessage.id) {
//     // 如果是重试，判断该消息是否在消息列表中，如果在则不再发送该条消息
//     let haveMessage = await checkMessageInList(dialogId, retryMessage.id)
//     if (haveMessage) {
//       return new Promise((resolve, reject) => {
//         resolve(retryMessage)
//       })
//     }
//     // 不在消息列表中，赋值重发消息
//     message = retryMessage
//   } else {
//     message = {
//       // dialog && dialog.type === 3 聊天类型为 3 时，走私聊的逻辑
//       type: dialog && dialog.type === privateChatType ? 'chat' : 'groupchat',
//       body: msg,
//       extension: {
//         ...metadata,
//         save_to_history: 1,
//         dialog_id: dialogId
//       },
//       markable: 1
//     }
//   }
//   return new Promise((resolve, reject) => {
//     var dialogJid = dialog && dialog.type === privateChatType ? dialog.opponentId : QuickBlox.chat.helpers.getRoomJidFromDialogId(dialogId)
//     try {
//       message.id = QuickBlox.chat.send(dialogJid, message)
//     } catch (error) {
//       log('发送消息失败', error)
//       if (error.name === 'ChatNotConnectedError') {
//         // not connected to chat
//       }
//       if (isRetry) {
//         reject(error)
//         return
//       }
//       log('重试发送消息', error)
//       retry(error, () => {
//         sendMessage(dialog, dialogId, msg, metadata, true, message).then(message => {
//           resolve(message)
//         }).catch(error => {
//           reject(error)
//         })
//       }, error, 'QuickBlox.chat.send', dialogId)
//       return
//     }
//     log('消息发送中', dialogJid, message)
//     if (CONFIG.streamManagement.enable) {
//       if (isRetry) {
//         sendMessageResolve.set(message.id, {
//           'function': resolve,
//           'param': message,
//           'lostFunction': reject,
//           'lostParam': message
//         })
//       } else {
//         sendMessageResolve.set(message.id, {
//           'function': resolve,
//           'param': message,
//           'lostFunction': () => {
//             sendMessage(dialog, dialogId, msg, metadata, true, message).then(newMessage => {
//               resolve(newMessage)
//             }).catch(error => {
//               reject(error)
//             })
//           },
//           'lostParam': ''
//         })
//       }
//     } else {
//       resolve(message)
//     }
//   })
// }

// // 上传单个文件
// export const uploadFile = function (file, i = 0) {
//   return new Promise((resolve, reject) => {
//     fileUtil.uploadFileDirectly(file, false, 'IM').then(result => {
//       log('上传文件成功', file, result)
//       resolve(result)
//     }).catch(error => {
//       if (error) {
//         if (i < 3) {
//           log('上传文件失败', file, error)
//           resolve({ error: error })
//         } else {
//           // 重试上传
//           log('重试上传', file, i)
//           resolve(uploadFile(file, ++i))
//         }
//       }
//     })
//   })
//   // var fileParams = {
//   //   name: file.name,
//   //   file: file,
//   //   type: file.type,
//   //   size: file.size,
//   //   public: false
//   // }
//   // return new Promise((resolve, reject) => {
//   //   QuickBlox.content.createAndUpload(fileParams, function (error, result) {
//   //     if (error) {
//   //       if (i < 3) {
//   //         log('上传文件失败', file, error)
//   //         resolve({ error: error })
//   //       } else {
//   //         // 重试上传
//   //         log('重试上传', file, i)
//   //         resolve(uploadFile(file, ++i))
//   //       }
//   //     } else {
//   //       log('上传文件成功', file, result)
//   //       resolve(result)
//   //     }
//   //   })
//   // })
// }

// // 上传文件列表
// export const uploadFiles = async function (files) {
//   // 上传文件列表
//   let resultList = []
//   for (const file of files) {
//     let result = await uploadFile(file)
//     if (result.error) {
//       console.log('上传文件失败', result)
//       throw result.error
//     }
//     // S3 结构转换
//     result = {
//       lgId: result.id,
//       content_type: file.type,
//       url: result.public_url,
//       size: file.size,
//       name: file.name
//     }
//     resultList.push(result)
//   }
//   log('上传文件列表', files, resultList)
//   return resultList
// }

// // 向用户发送系统消息
// export const sendSystemMessage = function (userIds, msg, extension) {
//   var message = {
//     body: msg,
//     extension: extension
//   }
//   log('准备发送系统消息', userIds, message)
//   for (const userId of userIds) {
//     try {
//       message.id = QuickBlox.chat.sendSystemMessage(userId, message)
//       log('发送系统消息', userId, message)
//       // or message.id = QB.chat.sendSystemMessage(opponentId, message) if message ID is needed
//     } catch (e) {
//       log('发送系统错误', e)
//       if (e.name === 'ChatNotConnectedError') {
//         // not connected to chat
//       }
//     }
//   }
// }

// // 更新已读
// export const updateMessage = function (messageId, dialogId, i = 0) {
//   if (i > 2) {
//     log('更新已读重试结束')
//     return
//   }
//   var updateParams = {
//     chat_dialog_id: dialogId,
//     read: 1
//   }
//   // messageId 为空则更新整个群组
//   QuickBlox.chat.message.update('', updateParams, function (error) {
//     retry(error, () => { updateMessage(messageId, dialogId, ++i) }, '', 'updateMessage', dialogId)
//     log('更新已读', messageId, updateParams, error)
//   })
// }

// export const recallMessage = function (message, dialogId) {
//   if (message.msg.startsWith('LG_RECALL_')) {
//     return
//   }
//   var updateParams = {
//     chat_dialog_id: dialogId,
//     message: 'LG_RECALL_' + message.msg
//   }
//   return new Promise((resolve, reject) => {
//     QuickBlox.chat.message.update(message.key, updateParams, function (error) {
//       // if error occurred - error will be returned
//       if (error) {
//         retry(error, reject, error, 'recallMessage')
//       } else {
//         resolve()
//       }
//     })
//   })
// }
// export const pushNotifications = function (message, userIds, data, isNewPush, i = 0) {
//   if (i > 2) {
//     log('推送重试结束')
//     return
//   }
//   var payload = JSON.stringify({
//     message: message,
//     ios_voip: '1',
//     ios_badge: '1',
//     ios_sound: '1',
//     VOIPCall: '1',
//     ...data
//   })
//   if (userIds.length == 0) {
//     return
//   }
//   var pushParameters = {
//     notification_type: 'push',
//     user: { ids: userIds }, // recipients.
//     environment: ['prod', 'release'].includes(localEnv) ? 'production' : 'development', // environment, can be 'production'.
//     message: QuickBlox.pushnotifications.base64Encode(payload)
//   }
//   if (!isNewPush) {
//     pushParameters.push_type = 'gcm'
//   }
//   log('发送推送', message, pushParameters)
//   QuickBlox.pushnotifications.events.create(pushParameters, function (error, result) {
//     if (error) {
//       retry(error, () => { pushNotifications(message, userIds, data, isNewPush, ++i) }, '', 'QuickBlox.pushnotifications.events.create')
//       log('Push Notification is sent error.', error)
//     } else {
//       // success
//       log('Push Notification is sent.')
//     }
//   })
// }

// /**
//  * 检查消息是否在消息列表中
//  *
//  * @param groupId 群组 ID
//  * @param messageId 消息 ID
//  * @returns { Promise<boolean> } 是否存在
//  */
// export async function checkMessageInList (groupId, messageId) {
//   let params = {
//     markAsRead: true,
//     chat_dialog_id: groupId,
//     sort_desc: 'date_sent',
//     limit: 10,
//     skip: 0
//   }
//   try {
//     let messages = await new Promise((resolve, reject) => {
//       QuickBlox.chat.message.list(params, function (error, messages) {
//         if (error) {
//           resolve(error)
//         } else {
//           messages.items.reverse()
//           resolve(messages)
//         }
//       })
//     })
//     if (messages && messages.items && messages.items.length > 0) {
//       let message = messages.items.find(item => item._id.toUpperCase() === messageId.toUpperCase())
//       return !!message
//     } else {
//       return false
//     }
//   } catch (error) {
//     return false
//   }
// }
