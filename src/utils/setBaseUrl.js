// 当前环境
const region = 'us' // process.env.VUE_APP_REGION
const env = 'magic-curriculum-' + 'prod' // process.env.VUE_APP_CURRENTMODE
// const env = 'stage' // process.env.VUE_APP_CURRENTMODE
let configBaseUrl = ''
// 当前URL 协议+域名+接口
export let pathName = ''

let domains = {
  us: 'learning-genie.com',
  cn: 'learninggenie.cn'
}

export let domain = domains[region]

// 处理stage环境中307错误
// if (region && region === 'us' && env && env === 'stage') {
//   // domain = domain.replace('learning-genie', 'learning-genie-api')
// }

// 判断当前环境,默认为test环境
switch (env) {
  case 'local':
    pathName = `https://magiccuriculum.learning-genie.com`
    configBaseUrl = `https://api2.${domain}/api/v1` // 这里是dev环境url
    // configBaseUrl = `http://stage.api2.learning-genie-api.com/api/v1` // 这里stage环境url
    // configBaseUrl = `https://test.api2.${domain}/api/v1` // 这里是test环境url
    // configBaseUrl = `http://localhost:8090/api/v1`
    break
  case 'dev':
    pathName = `http://dev.web.lejingling.cn:9000`
    configBaseUrl = `http://dev.api2.lejingling.cn:9000/api/v1` // 这里是dev环境url
    break
  case 'test':
    pathName = `https://test.web.${domain}`
    configBaseUrl = `https://test.api2.${domain}/api/v1` // 这里是test环境url
    break
  case 'stage':
    pathName = `https://stage.web.${domain}`
    configBaseUrl = `https://stage.api2.${domain}/api/v1` // 这里stage环境url
    break
  case 'release':
    pathName = `http://release.web.${domain}`
    configBaseUrl = `https://release.api.${domain}/api/v1` // 这里release环境url
    break
  case 'prod':
    pathName = `https://web.${domain}`
    configBaseUrl = `https://api2.${domain}/api/v1` // 生产环境url
    break
  case 'magic-curriculum-prod':
    pathName = `https://curriculumgenie.learning-genie.com`
    configBaseUrl = `https://api2.${domain}/api/v1` // 生产环境url
    break
  case 'magic-curriculum-test':
    pathName = `https://test.curriculumgenie.learning-genie.com`
    configBaseUrl = `https://test.api2.${domain}/api/v1` // 生产环境url
    break
  case 'magic-curriculum-stage':
    pathName = `https://stage.curriculumgenie.learning-genie.com`
    configBaseUrl = `https://stage.api2.${domain}/api/v1` // 生产环境url
    break
  case 'magic-curriculum-dev':
    pathName = `https://dev.curriculumgenie.learning-genie.com`
    configBaseUrl = `https://test.api2.${domain}/api/v1` // 生产环境url
    break
}

var sendActivityEventUrlEnv = {
  local: 'https://9kxg27n817.execute-api.us-west-1.amazonaws.com/',
  dev: 'https://9kxg27n817.execute-api.us-west-1.amazonaws.com/',
  stage: 'https://erb6eqlmi8.execute-api.us-west-1.amazonaws.com/',
  test: 'https://9kxg27n817.execute-api.us-west-1.amazonaws.com/',
  prod: 'https://shz9oixjh6.execute-api.us-west-1.amazonaws.com/',
  'magic-curriculum-stage': 'https://erb6eqlmi8.execute-api.us-west-1.amazonaws.com/',
  'magic-curriculum-test': 'https://9kxg27n817.execute-api.us-west-1.amazonaws.com/',
  'magic-curriculum-prod': 'https://shz9oixjh6.execute-api.us-west-1.amazonaws.com/'
}

var aiApiUrlEnv = {
  local: 'https://qek5ky43uogjoln4lsxooi5v2y0tgwku.lambda-url.us-west-1.on.aws/',
  dev: 'https://qek5ky43uogjoln4lsxooi5v2y0tgwku.lambda-url.us-west-1.on.aws/',
  test: 'https://qek5ky43uogjoln4lsxooi5v2y0tgwku.lambda-url.us-west-1.on.aws/',
  stage: 'https://qek5ky43uogjoln4lsxooi5v2y0tgwku.lambda-url.us-west-1.on.aws/',
  release: 'https://qek5ky43uogjoln4lsxooi5v2y0tgwku.lambda-url.us-west-1.on.aws/',
  prod: 'https://fnbjldysdn24lvl32joamslfxa0fkdim.lambda-url.us-west-1.on.aws/',
  'magic-curriculum-prod': 'https://fnbjldysdn24lvl32joamslfxa0fkdim.lambda-url.us-west-1.on.aws/',
  'magic-curriculum-test': 'https://qek5ky43uogjoln4lsxooi5v2y0tgwku.lambda-url.us-west-1.on.aws/',
  'magic-curriculum-stage': 'https://qek5ky43uogjoln4lsxooi5v2y0tgwku.lambda-url.us-west-1.on.aws/'
}

// AI 课程流式接口地址
var serverlessApiUrlEnv = {
  local: 'https://e34txt3gaz7oxao2ffpsv24fwe0qbtxm.lambda-url.us-west-1.on.aws/api/v1',
  dev: 'https://e34txt3gaz7oxao2ffpsv24fwe0qbtxm.lambda-url.us-west-1.on.aws/api/v1',
  test: 'https://e34txt3gaz7oxao2ffpsv24fwe0qbtxm.lambda-url.us-west-1.on.aws/api/v1',
  stage: 'https://n7b5xtjklrkcwlvetsrp4zotsu0jcifr.lambda-url.us-west-1.on.aws/api/v1',
  release: 'https://x6df6uzao4idgdjpxkeiniae6y0tmmcr.lambda-url.us-west-1.on.aws/api/v1',
  prod: 'https://x6df6uzao4idgdjpxkeiniae6y0tmmcr.lambda-url.us-west-1.on.aws/api/v1',
  'magic-curriculum-prod':
    'https://x6df6uzao4idgdjpxkeiniae6y0tmmcr.lambda-url.us-west-1.on.aws/api/v1',
  'magic-curriculum-test':
    'https://e34txt3gaz7oxao2ffpsv24fwe0qbtxm.lambda-url.us-west-1.on.aws/api/v1',
  'magic-curriculum-dev':
    'https://e34txt3gaz7oxao2ffpsv24fwe0qbtxm.lambda-url.us-west-1.on.aws/api/v1',
  'magic-curriculum-stage': 'https://n7b5xtjklrkcwlvetsrp4zotsu0jcifr.lambda-url.us-west-1.on.aws/api/v1'
}

export let serverlessApiUrl = serverlessApiUrlEnv[env]

// Curriculum Genie API 接口地址
var cgApiUrlEnv = {
  'magic-curriculum-dev': 'http://localhost:8000/api/v1',
  'magic-curriculum-test': 'https://goj32evevfbqqyegdxjhncxtae0dfwzh.lambda-url.us-west-1.on.aws/api/v1',
  'magic-curriculum-stage': 'https://z4eq2e7k4zmy5vjlnfb5gvsmr40xtmpp.lambda-url.us-west-1.on.aws/api/v1',
  'magic-curriculum-prod': 'https://cdgpb2gdisk3q3t6s4fb2aoz2q0lhlnv.lambda-url.us-west-1.on.aws/api/v1'
}

export let cgApiUrl = cgApiUrlEnv[env]

// 平台 LEARNING-GENIE, CURRICULUM-GENIE, MAGIC-CURRICULUM
export let platform = '${platform}'

export let setPlatform = function (newPlatForm) {
  platform = newPlatForm
}

let url = window.location.href
// 解析 url 中的 project 参数
let urlParams = new URLSearchParams(url)
const project = urlParams.get('project')
// 如果 url 中有 project 参数，使用 project 参数作为 platform
if (project) {
    platform = project
} else {
    // 默认为 CURRICULUM-PLUGIN
    platform = 'CURRICULUM-PLUGIN'
}

export let getPlatform = function () {
  return platform
}

export const platformRedirectMap = {
  'LEARNING-GENIE': '/admin',
  'CURRICULUM-GENIE': '/curriculum-genie',
  'MAGIC-CURRICULUM': '/magic-curriculum/contest'
}

// ClassLink Client ID
let classLinkClientIdTemp = 'c168984399302294ed5c15f6505818c42112bfc480bb'
// 测试环境 ClassLink Client ID
if (env && env !== 'prod') {
  classLinkClientIdTemp = 'c1689843993022a2d18947e4024495ae461820b95db7'
}

// Google Client ID
let googleClientIdTemp = '432944120765-1lr3sshgf5ktaudprrns4cq0dfi924s0.apps.googleusercontent.com'

export let aiApiUrl = aiApiUrlEnv[env]
export let sendActivityEventUrl = sendActivityEventUrlEnv[env]
export let localEnv = env
export let classLinkClientId = classLinkClientIdTemp
export let googleClientId = googleClientIdTemp
export default configBaseUrl
export let cleverClientId = '1baa5c15e169ec1999e7'
