// rules校验方法

//是否为合法IP地址
export function validateIP(rule, value,callback) {
    if(value==''||value==undefined||value==null){
      callback();
    }else {
      const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
      if ((!reg.test(value)) && value != '') {
        callback(new Error('请输入正确的IP地址'));
      } else {
        callback();
      }
    }
}
//是否为手机号码或者固话
export function validatePhoneTwo(rule, value, callback) {
    const reg = /^((0\d{2,3}-\d{7,8})|(1[34578]\d{9}))$/;;
    if (value == '' || value == undefined || value == null) {
      callback();
    } else {
      if ((!reg.test(value)) && value != '') {
        callback(new Error('请输入正确的电话号码或者固话号码'));
      } else {
        callback();
      }
    }
}
//是否为邮箱
export function validateEMail(rule, value,callback) {
    const reg =/^([a-zA-Z0-9]+[-_\.]?)+@[a-zA-Z0-9]+\.[a-z]+$/;
    if(value==''||value==undefined||value==null){
      callback();
    }else{
      if (!reg.test(value)){
        callback(new Error('请输入正确的邮箱'));
      } else {
        callback();
      }
    }
}
//是否为合法的url
export function validateURL(url) {
    const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
    return urlregex.test(url);
}
//验证内容是否包含英文数字以及下划线
export function isPassword(rule, value, callback) {
    const reg =/^[_a-zA-Z0-9]+$/;
    if(value==''||value==undefined||value==null){
      callback();
    } else {
      if (!reg.test(value)){
        callback(new Error('仅由英文字母，数字以及下划线组成'));
      } else {
        callback();
      }
    }
}
//验证数字输入框最大数值
export function checkMaxVal(rule, value,callback) {
    if (value < 0 || value > 最大值) {
      callback(new Error('请输入[0,最大值]之间的数字'));
    } else {
      callback();
    }
}
//密码校验
export const validatePsdReg = (rule, value, callback) => {
    if (!value) {
      return callback(new Error('Password cannot be empty.'))
    }
    if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/.test(value)) {
      callback(new Error('Password should be 8-20 characters long and must contain both letters and numbers.'))
    } else {
      callback()
    }
}
