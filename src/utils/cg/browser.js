/**
 * 浏览器工具类
 */
class BrowserUtil {
  /**
   * 检测是否为 Firefox 浏览器
   */
  static isFirefox() {
    if (typeof navigator === 'undefined') return false;
    return navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
  }

  /**
   * 检测是否为 Chrome 浏览器
   */
  static isChrome() {
    if (typeof navigator === 'undefined') return false;
    return navigator.userAgent.toLowerCase().indexOf('chrome') > -1 && !this.isEdge();
  }

  /**
   * 检测是否为 Edge 浏览器
   */
  static isEdge() {
    if (typeof navigator === 'undefined') return false;
    return navigator.userAgent.toLowerCase().indexOf('edg') > -1;
  }

  /**
   * 检测是否为支持的浏览器(Chrome 或 Edge)
   */
  static isSupportedBrowser() {
    return this.isChrome() || this.isEdge();
  }
}

export default BrowserUtil; 