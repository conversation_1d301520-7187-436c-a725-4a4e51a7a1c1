import Vue from 'vue'
import store from './store' // 状态管理器
import App from './App.vue' // 主应用
import router from './router' // 路由
import i18n from './utils/i18n' // 多语言
import '@/utils/element.js' // elementUi
import '@/utils/permission.js' // 路由权限设置
import { localEnv } from '@/utils/setBaseUrl' // 配置文件
import '@/utils/autoSignOut.js' // 自动退出功能
import analytics from '@/utils/analytics' // 数据分析
import '@/assets/css/theme.css' // 主题 CSS
import '@/assets/css/element-theme-v2.less' // 自定义主题 CSS
import '@/assets/css/common.css' // 公共css
import '@/assets/css/custom.less' // 自定义elementUi样式
import '@/assets/intro/intro.js' // 自定义elementUi样式
import '@/assets/intro/introjs.css' // 自定义elementUi样式
import '@/assets/font/Inter/inter.css'
// import '@/assets/css/video-js.min.css'
import '@/directive/clickOut'
import '@/directive/filterLink'
import '@/directive/experience'
import '@/directive/autofitText'
import '@/api' // API 的 url 配置
import axios from '@/utils/axios'
import { _cgAxios } from '@/utils/axios'
import * as filters from './filters' // 全局过滤
// import VCharts from 'v-charts'
// import 'v-charts/lib/style.css'
// import VueClipboard from 'vue-clipboard2'
import audio from 'vue-mobile-audio'
// 注册 vue-easy-tree
import VueEasyTree from '@wchbrad/vue-easy-tree'
import LgVirtualList from '@/components/LgVirtualList.vue'
import moment from 'moment'
import print from 'print-js'
import 'print-js/dist/print.css'// 导入打印的功能
import Sortable, { AutoScroll } from 'sortablejs/modular/sortable.core.esm'
import qs from 'qs'
// 导入 CG 相关的 Tailwind CSS 样式
import '@/assets/cg/css/tailwind-utilities.scss'
import '@/assets/icon/iconfont.css';
import '@/assets/icon/iconfont.js'
Vue.prototype.$print = print
Vue.prototype.$moment = moment
Vue.prototype.$axios = axios
Vue.prototype.$cgAxios = _cgAxios
Vue.prototype.$analytics = analytics // 数据分析
Vue.prototype.$qs = qs // 查询参数格式化
// Vue.use(VCharts)
// Vue.use(VueClipboard)
Vue.use(audio)
Vue.use(VueEasyTree)
Vue.component('LgVirtualList', LgVirtualList)
Sortable.mount(new AutoScroll())
// Vue.config.devtools = true
// register global utility filters.
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})
Vue.config.productionTip = false
Vue.config.devtools = true

Vue.prototype.$bus=new Vue()
new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')
