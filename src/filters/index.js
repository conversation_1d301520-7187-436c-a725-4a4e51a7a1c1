/* 三位数一断 */
export function numFormat (value) {
  if (!value) return '0'
  let intPart = Number(value) | 0 // 获取整数部分
  var re = /[0-9]+(\.[0-9]+)*/
  let decimal = String(value).match(re,'$1') // 获取小数部分
  let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
  return intPartFormat + (decimal[1] ? decimal[1] : '')
}
/* 三位数一断 */
export function valueFormat (value, fixed) {
  if (!value) return '0'
  let intPart = Number(value) | 0 // 获取整数部分

  value = Math.round(value * 100) / 100
  var re = /[0-9]+(\.[0-9]+)*/
  if (!fixed) {
    fixed = 2
  }
  let decimal = value.toFixed(fixed).match(re,'$1') // 获取小数部分
  let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
  return intPartFormat + (decimal[1] ? decimal[1] : '')
}

export function fixedFormat (value, fixed) {
  if (!value) return '0'
  let intPart = Number(value) | 0 // 获取整数部分

  value = Math.round(value * 100) / 100
  var re = /[0-9]+(\.[0-9]+)*/
  let decimal = value.toFixed(fixed).match(re,'$1') // 获取小数部分
  let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
  return intPartFormat + (decimal[1] ? decimal[1] : '')
}

export function floatFormat (value, fixed) {
  if (!fixed) {
    fixed = 2
  }
  return Math.round(+value + 'e' + fixed) / Math.pow(10, fixed)
}

export function moneyFormat (value, fixed) {
  if (!value) return '0'
  let intPart = Number(value) | 0 // 获取整数部分
  var re = /[0-9]+(\.[0-9]+)*/
  let decimal = String(value).match(re,'$1') // 获取小数部分
  let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
  // 如果 fixed 为 0, 那么就不需要小数部分
  if (fixed === 0) {
    return intPartFormat
  }
  return intPartFormat + (decimal[1] ? decimal[1].padEnd(fixed + 1,'0') : '.'.padEnd(fixed + 1,'0'))
}

export const fileSize = (size) => {
  if (!size) return ''
  if (size < pow1024(1)) return size + ' B'
  if (size < pow1024(2)) return (size / pow1024(1)).toFixed(2) + ' KB'
  if (size < pow1024(3)) return (size / pow1024(2)).toFixed(2) + ' MB'
  if (size < pow1024(4)) return (size / pow1024(3)).toFixed(2) + ' GB'
  return (size / pow1024(4)).toFixed(2) + ' TB'
}

/**
 * 格式化名字，Ms.Hello => Ms. Hello
 * Mr.Hello World => Mr. Hello World
 * Learning Genie => Learning Genie
 * @param name
 * @returns {string}
 */
export function formatUserName (name) {
  if (name) {
    if (name.indexOf('Ms.') === 0) {
      let separator = 'Ms.'
      if (name.replace(separator, '').charAt(0) !== ' ') {
        name = separator + ' ' + name.replace(separator, '')
      }
    } else if (name.indexOf('Mr.') === 0) {
      let separator = 'Mr.'
      if (name.replace(separator, '').charAt(0) !== ' ') {
        name = separator + ' ' + name.replace(separator, '')
      }
    }
  }
  return name
}

/**
 * 格式化关系名字，father => Father
 * mother => Mother
 * @param name
 * @returns 返回格式化后的关系名字
 */
export function formatRelationshipName (name) {
  if (name) {
    if (name.toLowerCase() === 'father') {
      return 'Father Figure'
    }
    if (name.toLowerCase() === 'mother') {
      return 'Mother Figure'
    }
    return name
  }
  return ''
}

/**
 * 在日期字符串中的指定符号后插入换行符
 * 11/11/2024 /  =>  11/11/<wbr>2024
 * 该函数用于处理日期字符串，确保在特定符号后添加换行符（<wbr>）
 *
 * @param {string} dateStr 日期字符串
 * @param {string} symbol 需要在其后插入换行符的符号
 * @returns {string} 处理后的日期字符串
 */
export function insertLineBreakAfterSymbol (dateStr, symbol) {
  if (!dateStr || !symbol) return ''
  // 将日期字符串中的指定符号替换为带有换行标签的符号
  const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // 转义特殊字符
  const regex = new RegExp(escapedSymbol, 'g')
  return dateStr.replace(regex, symbol + '<wbr>')
}

// 求次幂
function pow1024 (num) {
  return Math.pow(1024, num)
}
