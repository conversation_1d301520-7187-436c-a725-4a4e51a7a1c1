import Vue from 'vue'
import user from '@/store/modules/user'

Vue.directive('experience', {
    inserted: function (el,binding,vnode) {

      if (binding.value && binding.value.length > 0) {
        if (user.state.currentUser.email) {
          if (!binding.value.map(x => x.replace('.bak','')).includes(user.state.currentUser.email.replace('.bak',''))) {
            el.style.display = 'none'
          }
        }
      }
    }
  })
