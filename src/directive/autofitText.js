import Vue from 'vue'

// 防抖函数定义
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 改进的自适应字体大小函数
function autoFit(el) {
  try {
    // 基本检查
    if (!el || !el.parentNode || el.offsetWidth === 0 || el.offsetHeight === 0) {
      return;
    }

    // 获取文本内容
    const textContent = el.textContent || el.innerText || '';
    if (!textContent.trim()) {
      return;
    }

    // 获取容器尺寸
    const computedStyle = window.getComputedStyle(el);
    const containerWidth = el.clientWidth;
    const containerHeight = el.clientHeight;
    
    // 排除 padding
    const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0;
    const paddingRight = parseFloat(computedStyle.paddingRight) || 0;
    const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
    
    const availableWidth = containerWidth - paddingLeft - paddingRight;
    const availableHeight = containerHeight - paddingTop - paddingBottom;
    
    if (availableWidth <= 0 || availableHeight <= 0) {
      return;
    }
    
    // 创建测量函数 - 保持原有的 white-space 设置
    function measureTextSize(fontSize) {
      // 临时设置字体大小
      const originalFontSize = el.style.fontSize;
      el.style.fontSize = fontSize + 'px';
      
      // 强制重新计算布局
      el.offsetHeight;
      
      // 重要：保持原有的 white-space 设置
      // 让浏览器自然计算文本尺寸
      const result = {
        width: el.scrollWidth,
        height: el.scrollHeight
      };
      
      // 恢复原始字体大小
      el.style.fontSize = originalFontSize;
      
      return result;
    }
    
    // 二分查找最佳字体大小
    let minSize = 10;
    let maxSize = Math.max(parseFloat(computedStyle.fontSize) || 16, 48);
    let bestSize = minSize;
    
    while (minSize <= maxSize) {
      const midSize = Math.floor((minSize + maxSize) / 2);
      const measurements = measureTextSize(midSize);
      
      if (measurements.width <= availableWidth && measurements.height <= availableHeight) {
        bestSize = midSize;
        minSize = midSize + 1;
      } else {
        maxSize = midSize - 1;
      }
    }
    
    // 应用最佳字体大小
    el.style.fontSize = bestSize + 'px';
    
  } catch (error) {
    console.warn('AutofitText directive error:', error);
  }
}

Vue.directive('autofit-text', {
  inserted: function(el, binding, vnode) {
    if (vnode.context && vnode.context.$nextTick) {
      vnode.context.$nextTick(() => {
        autoFit(el);
      });
    } else {
      // 备用方案
      setTimeout(() => {
        autoFit(el);
      }, 0);
    }
    
    // 防抖的resize监听器
    const debouncedAutoFit = debounce(() => autoFit(el), 300);
    window.addEventListener('resize', debouncedAutoFit);
    
    // 添加 MutationObserver 监听内容变化
    const observer = new MutationObserver(() => {
      debouncedAutoFit();
    });

    observer.observe(el, {
      childList: true,
      subtree: true,
      characterData: true
    });
    
    // 存储清理函数
    el._autofitCleanup = () => {
      window.removeEventListener('resize', debouncedAutoFit);
      if (observer) {
        observer.disconnect();
      }
    };
  },
  componentUpdated: function(el, binding, vnode) {
    if (vnode.context && vnode.context.$nextTick) {
      vnode.context.$nextTick(() => {
        autoFit(el);
      });
    } else {
      // 备用方案
      setTimeout(() => {
        autoFit(el);
      }, 0);
    }
  },
  unbind: function(el) {
    // 清理事件监听器
    if (el._autofitCleanup) {
      el._autofitCleanup();
    }
  }
})