import Vue from 'vue'
// 过滤内容中的链接地址-->手动可以点击
let content = (el, binding, vnode) => {
	let content = binding.value
	if (content && content.length) {
    let reg = /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi
		content = content.replace(reg, (match) => {
			var href = match
			if (match.toLowerCase().indexOf('http') === -1) {
				href = 'http://' + match
			}
			return "<a style='color: #10b3b7;' target=\"_blank\" href=\"" + href + '">' + match + '</a>'
		})
		el.innerHTML = content
		// 如果有链接,并且是在 iPad 上，就添加点击事件进行跳转拦截
		if (/(iPad)/i.test(navigator.userAgent)) {
			// 添加点击事件
			el.addEventListener('click', (e) => {
				// 获取跳转的链接地址
				let target = e.target.href
				if (target) {
					let dic = { url: target }
					try {
						window.webkit.messageHandlers.appOpenUrlByBrowser.postMessage(dic)
					} catch (ignore) {
					// continue regardless of error
					}
					try {
						window.android.appOpenUrlByBrowser(target)
					} catch (err) {
					// continue regardless of error
					}
					try {
						window.parent.postMessage('APP_OPEN_URL_BY_BROWSER', target)
					} catch (err) {
					// continue regardless of error
					}
				}
			})
		}
	}
}
Vue.directive('analysisUrl', {
  inserted: content,
	update: content
})
