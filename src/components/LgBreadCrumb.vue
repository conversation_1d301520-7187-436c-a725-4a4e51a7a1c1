<template>
  <!-- 当页面为左侧菜单栏的睡眠检查和 DLL 时不显示面包屑 -->
  <el-breadcrumb separator-class="el-icon-arrow-right"
                 v-if="(!firstVisit || (firstVisit && $route.fullPath !== '/lessons/weekly-lesson-planning/planner-list'))
                 && disappearBreadCrumbRoutes.indexOf($route.fullPath) === -1">
    <!-- 只有路由跳转至 Import Error 页面才会出现该面包屑 -->
    <el-breadcrumb-item v-if="$route.fullPath.indexOf('/admin/import-error-handle') !== -1">
      <a :href="importOrSyncUrl">{{ $t('loc.iptOrSync') }}</a>
    </el-breadcrumb-item>
    <el-breadcrumb-item v-for="(item, index) in levelList" :key="index">
      <a v-if="isReload(item, index)" @click="reloadPage(item.path)" class="route-link" :title="item.meta.breadcrumb">{{ item.meta.breadcrumb }}</a>
      <span v-else-if="index === levelList.length-1" :style="item.meta.style ? item.meta.style : ''" :title="item.meta.breadcrumb">{{ $route.fullPath.indexOf('detail?type=book') > -1 ? $t('loc.boo') : item.meta.breadcrumb }}</span>
      <a v-else-if="item.path.indexOf('/#/') > -1" :href="item.path" :title="item.meta.breadcrumb">{{ item.meta.breadcrumb }}</a>
      <a class="route-link" v-else @click="changeRoute(item)" :title="item.meta.breadcrumb"> {{ item.meta.breadcrumb }}</a>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'LgBreadCrumb',
  props: {
    levelList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      firstVisit: false,
      importOrSyncUrl: '/#/import_center/SyncBack', // 导入页面路由
      disappearBreadCrumbRoutes: ['/dll/dllList', '/infantSleep/infantSleepCheckList'] // 不显示面包屑的路由地址
    }
  },
  created () {
    // 获取是否已经进行过周计划功能引导
    this.isNeedWeeklyPlanGuide()
  },
  watch: {
    $route: {
      handler (to, from) {
        // 判断是否是从 BookDetail 中跳转出
        if (from.path.indexOf('/dashboard/engagement/detail') > -1) {
          // 设置 vuex 中的状态
          this.$store.dispatch('setIsBookDetail', true)
        } else {
          this.$store.dispatch('setIsBookDetail', false)
        }
      }
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    })
  },
  methods: {
    // 路由跳转
    changeRoute (item) {
      let path = item.path
      if (item.name === 'lesson-plan-reflection') {
        this.$router.go(-1)
      } else if (item.meta.pageName === 'unitImport') {
        this.$router.push({
          name: 'unit-planner-cg'
        })
      } else if (this.$route.fullPath === path || this.$route.path === path) {
        location.reload(path)
      } else {
        if (path && path.includes('/curriculum-genie/setting')) {
          // 设置按钮的返回面包屑点击后刷新页面
          location.reload()
        }
        this.$router.push({
          path: path
        })
      }
    },
    isNeedWeeklyPlanGuide () {
      this.firstVisit = JSON.parse(localStorage.getItem(this.currentUser.user_id + 'WEEKLY_PLAN_GUIDE'))
      this.$store.commit('SET_FIRSTVISIT', this.firstVisit)
    },
    // 刷新页面
    reloadPage (path) {
      // 如果路由中包含 Import Error 则点击 reload 当前页面
      if (path.indexOf('admin/import-error-handle') !== -1) {
        return
      }
      if (this.levelList[1] && this.levelList[1].path) {
        this.$router.push({
          path: this.levelList[1].path
        })
      }
      // 在侧边栏进入 DLL Library 中点击面包屑跳转到 DLL 页面
      if (path === '/dll') {
        this.$router.push({ path })
      }
    },
    // 是否刷新页面
    isReload (item, index) {
      if (item.path && this.levelList.find(item => item.name === 'in-kind-ignore-table')) {
        return false
      }
      if (this.levelList.find(item => item.name === 'SchoolMessageHistory')) {
        return false
      }
      // if (index === 0 && item.path.indexOf('/#/') < 0) {
      //   return true
      // }
      if (item.path === '/dashboard' && index === 0) {
        return true
      }
      // DRDP 报告页，全部面包屑点击刷新页面
      if (item.path.indexOf('/assessment-report') > -1 && index + 1 < this.levelList.length) {
        return true
      }
      if (item.path === '/lessons/lesson-curriculum' && this.levelList.find(item => item.name === 'lessonCurriculum')) {
        return true
      }
      return false
    }
  }
}
</script>

<style>
.el-breadcrumb {
  height: 54px;
  line-height: 54px;
  padding-left: 24px;
}

.el-breadcrumb__item {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.route-link {
  color: #676779;
}
</style>
