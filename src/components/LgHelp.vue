<template>
  <div class="lg-help"></div>
</template>

<script>
// import {login} from '@/utils/applozic'
// import { isSession } from '@/utils/quickblox'

export default {
  name: 'LgHelp',
  props: {
    currentUser: {
      type: [Object, String]
    },
    ishowHelpChat: {
      type: Boolean,
      default: false
    }
  },
  created () {
    this.init()
  },
  watch: {
    currentUser (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.handleChangeSuccess()
      }
    }
  },
  /* eslint-disable */
  methods: {
    init () {
      this.handleChangeSuccess()
      // 判断是否为当前焦点窗口，重新建立与环信的链接
      let hiddenProperty = 'hidden' in document ? 'hidden'
        : 'webkitHidden' in document ? 'webkitHidden'
          : 'mozHidden' in document ? 'mozHidden'
            : null
      let visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange')
      let onVisibilityChange = function () {
        if (!document[hiddenProperty]) {
          // isSession(true)
        }
      }
      document.addEventListener(visibilityChangeEvent, onVisibilityChange)
    },
    handleChangeSuccess () {
      localStorage.setItem('isContChat', true)
      let isContact = localStorage.getItem('isContChat')

      if (!this.currentUser) {
        // 如果没有登录，隐藏聊天帮助按钮
        try {
          this.hideHelpChat()
        } catch (e) {
          console.log('Hidden ZenDesk chat failed! ' + e)
        }
        return
      }
      // 如果用户已经登录，显示聊天帮助按钮
      if (this.currentUser) {
        if (isContact == 'true') {
          let href = window.location.href
          // 如果是webChat页面，不用初始化
          if (href.indexOf('webChat') < 0 && href.indexOf('curriculum-genie') < 0) {
            // isSession(true)
          }
        }
        try {
          this.showHelpChat(this.currentUser)
        } catch (e) {
          console.log('Load ZenDesk chat failed! ' + e)
        }
      }
    },
    showHelpChat (user) {
      let _this = this
      if (this.ishowHelpChat) {
        return
      }
      if (window.location.href.indexOf('webChat') >= 0 || window.location.href.indexOf('curriculum-genie') >= 0) {
        return
      }
      // ZenDesk
      window.zEmbed || (function (e, t) { var n; var o; var d; var i; var s; var a = []; var r = document.createElement('iframe'); window.zEmbed = function () { a.push(arguments) }, window.zE = window.zE || window.zEmbed, r.src = 'javascript:false', r.title = '', r.role = 'presentation', (r.frameElement || r).style.cssText = 'display: none', d = document.getElementsByTagName('script'), d = d[d.length - 1], d.parentNode.insertBefore(r, d), i = r.contentWindow, s = i.document; try { o = s } catch (e) { n = document.domain, r.src = 'javascript:var d=document.open();d.domain="' + n + '";void(0);', o = s } o.open()._l = function () { var e = this.createElement('script'); n && (this.domain = n), e.id = 'js-iframe-async', e.src = 'https://assets.zendesk.com/embeddable_framework/main.js', this.t = +new Date(), this.zendeskHost = 'learninggenie.zendesk.com', this.zEQueue = a, this.body.appendChild(e) }, o.write('<body onload="document._l();">'), o.close() }())
      zE(function () {
        $('.zEWidget-launcher').css('display', 'block')
        // 设置用户信息，参考 https://developer.zendesk.com/api-reference/widget/core/
        zE('webWidget','identify',{
          name: user && user.display_name ? user.display_name : "",
          email: user && user.email ? user.email : "",
        });
        // 监控 webwidget 展示
        // 设置iframe 背景颜色、宽度、高度、圆角
        setTimeout(function () {
          _this.helpMovable()
        }, 5000)
      
    
        zE('webWidget:on', 'open', function () {
          var mobile = document.getElementById("mobile");
          mobile.style.display = 'none';
        });
        zE('webWidget:on', 'close', function () {
          var mobile = document.getElementById("mobile");
          // mobile.style.display = 'none';
          // zE.hide()
          mobile.style.display = 'block';
        });
        // Percept AI   根据开关打开
        // if (user && user.amyOpen) {
        //   var d = document; var s = d.createElement('script'); s.type = 'text/javascript'; s.src = 'https://widget.percept.ai/widget/learninggenie'; var x = d.getElementsByTagName('script')[0]; x.parentNode.insertBefore(s, x)
        //   window.perceptBotConfig = {
        //     fullName: user.display_name,
        //     email: user.email
        //   }
        // }
      })
      this.$store.dispatch('setIshowHelpChat', true)
    },
    hideHelpChat () {
      if (!this.ishowHelpChat) {
        return
      }
      zE(function () {
        $('.zEWidget-launcher').css('display', 'none')
      })
      this.$store.dispatch('setIshowHelpChat', false)
    },
    helpMovable () {
      var dragBox = document.getElementById('launcher')
      dragBox.style.width = '40px'
      dragBox.style.height = '40px'
      var dragBoxDocument = dragBox.contentWindow.document
      var embed = dragBoxDocument.getElementById('Embed')
      var divs = embed.getElementsByTagName('div')
      if (divs && divs.length > 1) {
        // 设置帮助按钮位置
        var button = divs[0].getElementsByTagName('button')[0]
        button.className = ''
        button.style.width = '40px'
        button.style.height = '40px'
        button.style.borderRadius = '50%'
        button.style.border = 'none'
        button.style.background = '#10b3b7'
        button.style.color = '#fff'
        button.innerHTML = "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"10\" height=\"20\" viewBox=\"0 0 10 20\" fill=\"none\">\n" +
        "<path d=\"M5 16.2616C6.00418 16.2616 6.81818 17.0756 6.81818 18.0798C6.81818 19.084 6.00418 19.898 5 19.898C3.99582 19.898 3.18182 19.084 3.18182 18.0798C3.18182 17.0756 3.99582 16.2616 5 16.2616ZM5 0.352539C7.76145 0.352539 10 2.59112 10 5.35254C10 7.19036 9.45909 8.21436 8.08891 9.6409L7.78236 9.95308C6.65164 11.0838 6.36364 11.564 6.36364 12.6253C6.36364 13.3784 5.75309 13.9889 5 13.9889C4.24691 13.9889 3.63636 13.3784 3.63636 12.6253C3.63636 10.7874 4.17727 9.76345 5.54745 8.3369L5.854 8.02472C6.98473 6.89399 7.27273 6.41381 7.27273 5.35254C7.27273 4.09734 6.25527 3.07981 5 3.07981C3.82327 3.07981 2.85546 3.97412 2.73909 5.12016L2.72727 5.35254C2.72727 6.10565 2.11673 6.71618 1.36364 6.71618C0.610527 6.71618 0 6.10565 0 5.35254C0 2.59112 2.23855 0.352539 5 0.352539Z\" fill=\"white\"/>\n" +
        "</svg>"
      }

      var height = dragBox.offsetHeight

      var width = dragBox.offsetWidth

      var initRight = dragBox.style.right

      var initBottom = dragBox.style.bottom

      var topLayerHeight = 20

      initRight = initRight || 0
      initBottom = initBottom || 0

      this.initTopLayer(topLayerHeight, initRight, initBottom, width, height, dragBox)
      document.getElementById('mobile').addEventListener('click', function () {
        var mobile = document.getElementById('mobile')
        if (dragBox.style.right == '0px') {
          dragBox.style.right = '-105px'
          mobile.innerHTML = '<i class="fa fa-chevron-left" style="padding-right: 15px;" aria-hidden="true"></i>'
          mobile.style.marginTop = '-2px';
          mobile.style.paddingTop = '10px';
          mobile.style.marginRight = '-5px'
          mobile.style.marginLeft = '110px'
          mobile.style.width = '40px'
          mobile.style.height = '40px'
          mobile.style.color = '#fff'
          mobile.style.background = 'rgb(16, 179, 183)'
          mobile.style.borderRadius = '50%'
        } else {
          dragBox.style.right = '0px'
          mobile.innerHTML = '<i class="fa fa-times " style="padding-right: 20px;" aria-hidden="true"></i>'
          mobile.style.marginTop = '0px'
          mobile.style.paddingTop = '0px';
          mobile.style.marginRight = '0px'
          mobile.style.color = '#484848'
          mobile.style.background = 'unset'
        }
      })
      document.getElementById('mobile').addEventListener('mouseover', function (e) {
        document.getElementById('mobile').style.display = 'block'
      })
      document.getElementById('top-layer').addEventListener('mouseover', function (e) {
        var Embed = document.getElementById('webWidget')
        if (!Embed) {
          var mobile = document.getElementById('mobile')
          mobile.style.display = 'block';
        }
      })
      document.getElementById('top-layer').addEventListener('mouseout', function (e) {
        var mobile = document.getElementById('mobile')
        if (mobile.innerHTML == '<i class="fa fa-times" aria-hidden="true"></i>') {
          // mobile.style.display = 'none'
        } else {
          mobile.style.display = 'block';
        }
      })
    },
    initTopLayer (topLayerHeight, initRight, initBottom, width, height, dragBox) {
      var topLayer = document.getElementById('top-layer')
      if (!topLayer) {
        topLayer = document.createElement('div')
        var mobile = document.createElement('p')
        mobile.id = 'mobile'
        mobile.innerHTML = '<i class="fa fa-times" aria-hidden="true"></i>'
        mobile.style.cursor = 'pointer'
        mobile.style.fontSize = '16px'
        mobile.style.marginTop = '-2px'
        mobile.style.width = '32px'
        mobile.style.height = '50px'
        mobile.style.textAlign = 'center'
        mobile.style.marginLeft = '106px'
        document.body.appendChild(topLayer)
        topLayer.append(mobile)
      }
      topLayer.id = 'top-layer'
      topLayer.style.position = 'fixed'
      topLayer.style.width = '124px'
      topLayer.style.height = '0'
      // topLayer.style.backgroundColor = 'pink';
      topLayer.style.removeProperty('left')
      topLayer.style.removeProperty('top')
      topLayer.style.right = '-12px'
      topLayer.style.bottom = '57px'
      topLayer.style.margin = '5px 20px'
      topLayer.style.textAlign = 'right'
      topLayer.style.zIndex = 999999
      // topLayer.html('<a href="#">111</a>');
      return topLayer
    }
  },
  /* eslint-enable */
  // computed: {
  //   ...mapState({
  //     currentUser: state => state.user.currentUser,
  //     ishowHelpChat: state => state.chat.ishowHelpChat
  //   }),
  //   isChina () {
  //     return this.$store.getters.isChina == 'cn'
  //   }
  // }
}
</script>

<style>
.u-userLauncherColor:not([disabled]) svg path {
  fill: #fff !important;
}
</style>
