<template>
  <!-- 树形结构 -->
  <div class="tree-container tree-container-mobile lg-scrollbar-show" v-loading="loading">
    <el-tree
      :data="dataNodeList"
      :default-expanded-keys="defaultExpandedKeys"
      :show-checkbox="showCheckbox"
      :node-key="nodeKey"
      :icon-class="iconClass"
      :props="defaultProps"
      :empty-text="treeDataLoading ? ' ' : emptyText"
      :render-content="renderContent"
      @check="handleCheck"
      :filter-node-method="filterNode"
      @node-click="handleNodeClick"
      ref="tree">
    </el-tree>
  </div>
</template>

<script>
import { equalsIgnoreCase } from '@/utils/common'

/**
 * 判断是否是叶子节点
 * @param dataNode 节点
 * @returns 是否是叶子节点
 */
function isLeafNode (dataNode) {
  return dataNode && (!dataNode.children || dataNode.children.length === 0)
}

/**
 * 判断是否需要 select all 节点
 * @param dataNode 节点
 * @returns 是否需要 select all 节点
 */
function isSelectAllNeeded (dataNode) {
  return !isLeafNode(dataNode) && !dataNode.children.some(child => child.children.length)
}

/**
 * 转换节点列表
 * @param dataNodeList 节点列表
 * @returns 转换后的节点列表
 */
function convertDataNodeList (dataNodeList) {
  return dataNodeList.length === 0 ? [] : dataNodeList.map(convertDataNode)
}

/**
 * 转换节点, 添加 select all 节点
 * @param dataNode 节点
 * @returns 转换后的节点
 */
function convertDataNode (dataNode) {
  return isLeafNode(dataNode)
    ? dataNode
    : {
      ...dataNode,
      children: isSelectAllNeeded(dataNode) ? [
        { id: '#' + dataNode.id, name: 'Select all', virtual: true },
        ...convertDataNodeList(dataNode.children)
      ] : convertDataNodeList(dataNode.children)
    }
}

/**
 * 遍历所有节点, 执行回调函数
 * @param dataNodeList 节点列表
 * @param fn 回调函数
 */
function applyToAllNodes (dataNodeList, fn) {
  for (let dataNode of dataNodeList) {
    fn(dataNode)
    if (!isLeafNode(dataNode)) {
      applyToAllNodes(dataNode.children, fn)
    }
  }
}

/**
 * 判断所有子节点是否都选中
 * @param treeNode tree 组件内部节点
 */
function isAllChecked (treeNode) {
  return treeNode.childNodes.length !== 0 && treeNode.childNodes.every(child => child.checked)
}

/**
 * 判断是否存在选中的子节点
 * @param treeNode tree 组件内部节点
 */
function isAnyChecked (treeNode) {
  return treeNode.childNodes.length !== 0 && treeNode.childNodes.some(child => child.checked)
}

/**
 * 判断是否存在 select all 节点
 * @param dataNode 节点
 */
function hasSelectAll (dataNode) {
  return !isLeafNode(dataNode) && dataNode.children[0].virtual
}

export default {
  name: 'LgTree',

  props: {
    // 树形数据
    treeData: {
      type: Array,
      required: true,
      default: () => []
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 树形组件的属性配置
    defaultProps: {
      type: Object,
      required: true
    },
    // 搜索框的值
    searchQuery: {
      type: String,
      default: null
    },
    // 是否显示搜索框
    showSearch: {
      type: Boolean,
      default: true
    },
    // 是否显示复选框
    showCheckbox: {
      type: Boolean,
      default: true
    },
    // 节点唯一标识字段
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 图标样式类名
    iconClass: {
      type: String,
      default: 'tree-icon'
    },
    // 空数据提示文本
    emptyText: {
      type: String,
      default: 'No data'
    },
    // 是否启用 Select All 功能
    enableSelectAll: {
      type: Boolean,
      default: true
    },
    // 最大可选数量（用于限制选择）
    maxItems: {
      type: Number,
      default: -1
    },
    // 当前已选中的项目数量
    selectedCount: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      expandedState: {}, // 用于存储节点的展开状态
      treeDataLoading: false,
    }
  },

  watch: {
    // 监听 searchQuery 变化
    searchQuery(newValue, oldValue) {
      if(this.$refs.tree) {
        if (oldValue === '') {
          // 记录当前节点的展开状态
          this.recordNodeExpansionState()
        }

        if (newValue === '') {
          // 恢复所有节点的展开状态
          this.restoreNodeExpansionState()
        }

        // 在 tree 组件状态更改之前记录
        this.$refs.tree.filter(newValue);
        this.syncChecked()
      }
    },
    // 监听树形数据的变化
    treeData: {
      handler(newValue) {
        // 如果新的 treeData 为空，则设置 treeDataLoading 为 true，否则为 false
        this.treeDataLoading = newValue.length === 0;
      },
      deep: true
    }
  },

  computed: {
    /**
     * 数据节点列表, 根据配置决定是否添加 select all 节点
     */
    dataNodeList() {
      if (!this.enableSelectAll) {
        return this.treeData
      }
      return convertDataNodeList(this.treeData)
    },

    /**
     * 剩余可选择数量
     */
    remainingChoices() {
      if (this.maxItems === -1) return Infinity
      return this.maxItems - this.selectedCount
    },

    /**
     * 默认展开第一个节点
     */
    defaultExpandedKeys() {
      if (this.dataNodeList.length === 0) {
        return []
      }
      const expandedKeys = []
      const helper = (dataNode) => {
        if (isLeafNode(dataNode)) {
          return
        }
        expandedKeys.push(dataNode.id)
        helper(dataNode.children[0])
      }
      helper(this.dataNodeList[0])
      return expandedKeys
    }
  },

  methods: {
    /**
     * 记录所有节点的展开状态
     */
    recordNodeExpansionState() {
      this.expandedState = {}
      applyToAllNodes(this.dataNodeList, dataNode => {
        if (isLeafNode(dataNode)) {
          return
        }
        const treeNode = this.$refs.tree.getNode(dataNode.id)
        this.expandedState[dataNode.id] = treeNode.expanded
      })
    },

    /**
     * 恢复所有节点的展开状态
     */
    restoreNodeExpansionState() {
      applyToAllNodes(this.dataNodeList, dataNode => {
        if (isLeafNode(dataNode)) {
          return
        }
        const treeNode = this.$refs.tree.getNode(dataNode.id)
        treeNode.expanded = this.expandedState[dataNode.id]
      })
    },

    /**
     * 判断两个节点是否相同
     * @param n1
     * @param n2
     */
    isSameNode(n1, n2) {
      return equalsIgnoreCase(n1.id, n2.id)
    },

    /**
     * 同步选中状态
     */
    syncChecked() {
      if (!this.enableSelectAll) return
      
      this.$refs.tree.getHalfCheckedNodes()
        .filter(hasSelectAll)
        .map(dataNode => this.$refs.tree.getNode(dataNode.id))
        .forEach(({ childNodes: [ treeNode ] }) => {
          // 构造一个虚拟的父节点 (去除掉 select all 节点和被过滤掉的, 页面上能看到的样子)
          const parentTreeNode = { ...treeNode.parent, childNodes: treeNode.parent.childNodes.slice(1).filter(treeNode => this.isNotFilteredNode(treeNode.data)) }
          if (isAllChecked(parentTreeNode)) {
            // 所有子节点都选中, 设置 select all 节点为选中状态, 取消半选状态
            this.$refs.tree.setChecked(treeNode.data.id, true)
            treeNode.indeterminate = false
            treeNode.data.indeterminate = false
          } else if (isAnyChecked(parentTreeNode)) {
            // 半选
            // 样式会变
            treeNode.indeterminate = true
            // 控制 select all 选中时的逻辑
            treeNode.data.indeterminate = true
          } else {
            // 取消选中
            this.$refs.tree.setChecked(treeNode.data.id, false)
          }
        })
    },

    /**
     * 过滤节点
     * @param value
     * @param dataNode
     * @param treeNode
     */
    filterNode(value, dataNode, treeNode) {
      // select all 节点, 判断父节点的所有子节点是否有符合条件的节点
      return dataNode.virtual
        ? treeNode.parent.childNodes
          .map(child => child.data)
          .some(childDataNode => this.doFilterNode(value, childDataNode))
        : this.doFilterNode(value, dataNode)
    },

    /**
     * 执行节点过滤逻辑
     * @param value 搜索值
     * @param data 节点数据
     */
    doFilterNode(value, data) {
      // 仅输入空格,不会影响节点的展开状态
      if (!value || !value.trim()) return true;
      const lowerValue = value.toLowerCase().trim();
      return (data.abbreviation && data.abbreviation.toLowerCase().indexOf(lowerValue) !== -1) ||
        (data.name && data.name.toLowerCase().indexOf(lowerValue) !== -1) ||
        (data.description && data.description.toLowerCase().indexOf(lowerValue) !== -1);
    },

    /**
     * 是否是没有被过滤掉的节点
     * @param dataNode
     */
    isNotFilteredNode(dataNode) {
      return this.doFilterNode(this.searchQuery, dataNode)
    },

    /**
     * 点击节点
     * @param dataNode
     * @param treeNode
     */
    handleNodeClick(dataNode, treeNode) {
      this.delegateCheck(dataNode, !treeNode.checked)
    },

    /**
     * 点击复选框
     * @param dataNode
     */
    handleCheck(dataNode) {
      const treeNode = this.$refs.tree.getNode(dataNode.id)
      this.delegateCheck(dataNode, treeNode.checked)
    },

    /**
     * 代理选中, 统一处理选中状态
     * @param dataNode
     * @param checked
     */
    delegateCheck(dataNode, checked) {
      if (!isLeafNode(dataNode)) {
        return
      }

      if (dataNode.indeterminate) {
        // 对于 select all 没有可以选择的就取消选择
        checked = this.remainingChoices < 1 ? false : true
        dataNode.indeterminate = false
      }

      this.doCheck(dataNode, checked)
      this.syncChecked()
    },

    /**
     * 处理选中
     * @param dataNode
     * @param checked
     */
    doCheck(dataNode, checked) {
      this.$refs.tree.setChecked(dataNode.id, checked);
      
      // 如果选中且剩余可选择数量小于 1 则取消选中
      if (checked && this.remainingChoices < 1) {
        this.$refs.tree.setChecked(dataNode.id, !checked);
        this.showLimitWarning();
        return
      }

      // select all 节点
      if (dataNode.virtual && this.enableSelectAll) {
        // 获取所有子节点 (去除掉 select all 节点和被过滤掉的)
        const dataNodes = this.$refs.tree.getNode(dataNode.id.slice(1))
          .childNodes.slice(1).map(child => child.data)
          .filter(this.isNotFilteredNode)

        if (checked) {
          // 选中剩余可选择的节点
          dataNodes.filter(dataNode => !this.$refs.tree.getNode(dataNode.id).checked)
            .slice(0, this.remainingChoices)
            .forEach(dataNode => this.$refs.tree.setChecked(dataNode.id, checked))
        } else {
          // 取消选中所有子节点
          dataNodes.forEach(dataNode => this.$refs.tree.setChecked(dataNode.id, checked))
        }
      }

      // 获取所有选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes().filter(n => !n.virtual)
      const selectedItems = checkedNodes.filter(isLeafNode)
        .map(node => ({
          id: node.id,
          abbreviation: node.abbreviation,
          name: node.name
        }));

      // 触发选中变化事件
      this.$emit('selection-change', selectedItems)
    },

    /**
     * 显示选中数量警告
     */
    showLimitWarning() {
      this.$emit('limit-warning', {
        maxItems: this.maxItems,
        selectedCount: this.selectedCount
      })
    },

    /**
     * 渲染树形节点内容
     */
    renderContent(h, {node, data}) {
      // 如果父组件提供了自定义渲染函数，则使用父组件的
      if (this.$scopedSlots.default) {
        return this.$scopedSlots.default({ node, data })
      }

      // 默认渲染逻辑
      // name 样式
      const nameStyle = {
        color: '#111C1C',
        fontSize: '14px',
      };
      // description 样式
      const descriptionStyle = {
        color: '#999',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        maxWidth: 'calc(100% - 40px)' // 留出其他元素的空间，例如树形节点的展开/折叠按钮
      };
      
      // 如果同时有名字和缩写，展示缩写：名字
      if (data.name && data.abbreviation) {
        // 如果名字和缩写相同，只显示名字
        if (data.name === data.abbreviation) {
          return h('span', {style: nameStyle}, data.name);
        }
        return h('span', [
          h('span', {style: nameStyle}, data.abbreviation),
          h('span', {style: nameStyle}, ': '),
          h('span', {style: nameStyle}, data.name)
        ]);
      }
      // 如果没有描述，展示名字或缩写
      if (!data.description || data.name) {
        return h('span', {style: descriptionStyle}, [
          h('span', {style: nameStyle}, data.name || data.abbreviation)
        ]);
      }
      // 如果不同时有名字和缩写，展示缩写和描述
      return h('el-tooltip', {
        props: {
          effect: 'light',
          content: data.description,
          placement: 'top',
          openDelay: 500
        }
      }, [
        h('span', {style: descriptionStyle}, [
          h('span', {style: nameStyle}, data.abbreviation),
          h('span', {style: nameStyle}, ': '),
          h('span', {style: descriptionStyle}, data.description)
        ])
      ]);
    },

    // 公共方法：设置选中的节点
    setCheckedKeys(keys) {
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys(keys)
        this.syncChecked()
      }
    },

    // 公共方法：获取选中的节点
    getCheckedNodes() {
      if (this.$refs.tree) {
        return this.$refs.tree.getCheckedNodes().filter(n => !n.virtual)
      }
      return []
    },

    // 公共方法：获取选中的节点 keys
    getCheckedKeys() {
      if (this.$refs.tree) {
        return this.$refs.tree.getCheckedKeys()
      }
      return []
    },

    // 公共方法：清空搜索
    clearSearch() {
      this.searchQuery = ''
    },

    // 公共方法：展开所有节点
    expandAll() {
      if (this.$refs.tree) {
        this.$refs.tree.expandAll()
      }
    },

    // 公共方法：折叠所有节点
    collapseAll() {
      if (this.$refs.tree) {
        this.$refs.tree.collapseAll()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-container {
  width: 100%;

  /deep/ .el-tree .el-tree-node .el-tree-node__content {
    display: flex;
    align-items: center;

    .el-tree-node__expand-icon {
      width: 16px;
      height: 16px;
      padding: 0;
      margin: 0 8px;
      position: relative;
      border-radius: 4px;
      overflow: hidden;
      transition: transform 0.3s;

      &.expanded {
        transform: rotate(0deg);
      }

      // 非叶子节点不显示 checkbox
      &:not(.is-leaf) + .el-checkbox {
        display: none;
      }

      // 叶子节点不显示内容
      &.is-leaf {
        cursor: default;
        opacity: 0;
      }
    }

    .tree-icon {
      // 不允许收缩
      flex-shrink: 0;

      &.expanded::before {
        content: "-";
      }

      &::before {
        content: "+";
        font-weight: bold;
        color: #D9D9D9;
        background-color: #111C1C;
        position: absolute;
        top: -2px;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }
    }

    .el-checkbox {
      margin-right: 8px;
      margin-top: 2px;

      .el-checkbox__inner {
        width: 16px;
        height: 16px;

        &::after {
          top: 1px;
          left: 5px;
        }
      }
    }
  }
}

/deep/ .el-tree-node__content {
  height: 30px
}

/* 移动端适配样式 */
@media screen and (max-width: 768px) {
  
  .tree-container-mobile {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding-bottom: 80px;
  }
  
  .footer-container-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 12px 16px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 2000;
  }
  
  .mobile-button-group {
    width: 100%;
    display: flex;
    justify-content: space-between !important;
    gap: 12px;
    margin: 0 !important;
    
    .el-button {
      flex: 1;
      margin: 0 !important;
    }
  }
  
  .selected-items-container {
    margin-bottom: 0px;
  }
  
  .modal-header-mobile {
    padding: 0 16px;
  }
}
</style> 