<template>
  <div class="lg-loading" v-show="isLoading">
      <span>
          <i class="cover-loading"></i>
      </span>
  </div>
</template>

<script>
/*
  isLoading 是否显示loading
*/
export default {
  name: 'LgLoading',
  computed: {
    isLoading () {
      return this.$store.getters.isLoading
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.lg-loading {
  height: 100%;
  width: 100%;
  text-align: center;
  top: 0;
  background-color: rgba(100, 100, 100, 0.1);
  position: fixed;
  z-index: 9999;
  span {
    position: fixed;
    top: 45%;
    left: 50%;
    margin-left: -32px;
    .cover-loading {
      position: relative;
      display: inline-block;
      background-color: #8bc7ff;
      border-radius: 50%;
      width: 80px;
      height: 80px;
      border: 1px solid #8bc7ff;
      transform: rotate(0deg);
      overflow: hidden;
    }
    .cover-loading:before {
      animation: cover-loading 1s linear infinite;
      position: absolute;
      content: "";
      left: 0;
      top: 0;
      display: inline-block;
      background: url("https://d2urtjxi3o4r5s.cloudfront.net/images/genie_fff.png")
        19px 10px no-repeat;
      width: 80px;
      height: 80px;
    }
    .cover-loading:after {
      animation: cloudy 1s linear infinite;
      position: absolute;
      content: "";
      display: inline-block;
      border-radius: 50%;
      left: -12px;
      background: #ffffff;
      box-shadow: #ffffff 0 4px 0 2px, #ffffff -5px 8px 0 0, #ffffff 6px 2px 0 0,
        #ffffff 8px 7px 0 0, #ffffff 2px 8px 0 0, #ffffff -35px 58px 0 3px,
        #ffffff -50px 65px 0 2px, #ffffff -43px 58px 0 2px,
        #ffffff -32px 63px 0 3px, #ffffff -41px 65px 0 2px;

      width: 10px;
      height: 10px;
    }
  }
}
@keyframes cover-loading {
  0% {
    transform: translate(0, -2px);
  }
  50% {
    transform: translate(0, 8px);
  }
  100% {
    transform: translate(0, -2px);
  }
}
@keyframes cloudy {
  0% {
    transform: translate(-20px, 0);
  }
  100% {
    transform: translate(120px, 0);
  }
}
</style>
