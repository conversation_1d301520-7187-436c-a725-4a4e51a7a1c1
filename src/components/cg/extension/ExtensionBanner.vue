<template>
  <div v-if="showBanner" class="extension-banner banner-bg">
    <div class="banner-content">
      <div class="banner-text">
        <span>Unlock smarter teaching with Curriculum Genie  – Your AI Agent for P-12 curriculum and adaptive lesson planning! </span>
        <span class="rocket-icon">
            <img :src="require('@/assets/cg/images/banner/rocket.png')" class="rocket-img" />
        </span>
      </div>
      <div class="chrome-btn">
        <!-- 添加 logo -->
        <img :src="require('@/assets/cg/images/banner/banner-logo.png')" class="logo-img" />
        <a href="https://chromewebstore.google.com/detail/curriculum-genie/jbhknkdnmmcipjcfjngiojllgbgolmao" target="_blank">
          Add to Chrome for free
        </a>
      </div>
    </div>
    <button @click="closeBanner" class="close-btn">
          <!-- 导入 svg -->
          <img :src="require('@/assets/cg/images/banner/close.svg')" class="close-img" alt="close" />
        </button>
  </div>
</template>

<script>
import BrowserUtil from '@/utils/cg/browser'

export default {
  name: 'ExtensionBanner',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showBanner: false,
      isMobile: false // 是否是移动端
    }
  },
  computed: {
    currentUser() {
      return this.$store.state.cgAuth.user
    },
    hasPlugin() {
      return this.$store.state.cgAuth.hasPlugin
    }
  },
  watch: {
    currentUser: 'checkShouldShowBanner',
    hasPlugin: 'checkShouldShowBanner',
    '$route': 'checkShouldShowBanner'
  },
  methods: {
    // 从 localStorage 获取 Banner 显示状态
    getBannerState() {
      if (typeof localStorage === 'undefined') return null;
      const bannerState = localStorage.getItem('cg_banner_state');
      return bannerState ? JSON.parse(bannerState) : null;
    },

    // 保存 Banner 显示状态
    saveBannerState(state) {
      if (typeof localStorage === 'undefined') return;
      localStorage.setItem('cg_banner_state', JSON.stringify(state));
    },

    // 初始化 Banner 状态
    initBannerState() {
      if (typeof localStorage === 'undefined') return null;

      let state = this.getBannerState();
      if (!state) {
        // 生成一个会话 ID
        const sessionId = Date.now().toString() + Math.random().toString(36).substring(2, 9);

        state = {
          closedManually: false,
          showCount: 0,
          lastShowDate: '',
          installTime: null,
          sessionId: sessionId,
          closedDate: null
        };
        this.saveBannerState(state);
      } else if (!state.sessionId) {
        // 为旧的状态添加会话 ID
        state.sessionId = Date.now().toString() + Math.random().toString(36).substring(2, 9);

        // 确保 closedDate 字段存在
        if (state.closedDate === undefined) {
          state.closedDate = null;
        }

        this.saveBannerState(state);
      }
      return state;
    },

    // 检查是否应该显示 Banner
    checkShouldShowBanner() {
      // 如果不是 Chrome 或 Edge，不显示
      if (!(BrowserUtil.isChrome() || BrowserUtil.isEdge()) || this.isMobile) {
        this.showBanner = false;
        this.$emit('update:visible', false);
        return;
      }

      // 如果已安装插件，不显示
      if (this.hasPlugin) {
        this.showBanner = false;
        this.$emit('update:visible', false);
        return;
      }

      // 如果用户未登录，不显示
      if (!this.currentUser) {
        this.showBanner = false;
        this.$emit('update:visible', false);
        return;
      }

      // 获取 Banner 状态
      const state = this.getBannerState();
      if (!state) {
        this.showBanner = true;
        this.$emit('update:visible', true);
        this.recordBannerShow();
        return;
      }

      // 如果已经显示了 3 次，不显示
      if (state.showCount > 3) {
        this.showBanner = false;
        this.$emit('update:visible', false);
        return;
      }

      // 获取当前日期
      const today = new Date().toISOString().split('T')[0];

      // 如果用户今天手动关闭了，不显示
      if (state.closedManually && state.closedDate === today) {
        this.showBanner = false;
        this.$emit('update:visible', false);
        return;
      }

      // 如果不是同一天，则可以显示
      if (state.lastShowDate !== today) {
        this.showBanner = true;
        this.$emit('update:visible', true);
        this.recordBannerShow();
        return;
      }

      // 检查是否有卸载日期记录，如果有，则重置计数
      const uninstallDate = localStorage.getItem('cg_uninstall_date');
      if (uninstallDate) {
        // 如果上次卸载日期比 last_show_date 更晚，说明卸载后重新安装了
        if (state.lastShowDate && uninstallDate > state.lastShowDate) {
          // 重置计数
          state.showCount = 0;
          state.lastShowDate = '';
          state.closedManually = false;
          state.closedDate = null;
          this.saveBannerState(state);
          // 清除卸载日期记录
          localStorage.removeItem('cg_uninstall_date');
          this.showBanner = true;
          this.$emit('update:visible', true);
          this.recordBannerShow();
          return;
        }
      }

      this.showBanner = true;
      this.$emit('update:visible', true);
    },

    // 关闭 Banner
    closeBanner() {
      this.showBanner = false;

      const state = this.getBannerState() || this.initBannerState();
      if (state) {
        // 设置关闭标志和关闭日期
        state.closedManually = true;
        state.closedDate = new Date().toISOString().split('T')[0];
        this.saveBannerState(state);
      }

      // 通知父组件
      this.$emit('update:visible', false);

      // 添加: 直接在 localStorage 中设置可见性标志
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('cg_banner_visibility', 'false');
      }
    },

    // 记录 Banner 显示
    recordBannerShow() {
      const state = this.getBannerState() || this.initBannerState();
      if (!state) return;

      const today = new Date().toISOString().split('T')[0];

      // 如果不是同一天，增加显示次数
      if (state.lastShowDate !== today) {
        state.showCount += 1;
        state.lastShowDate = today;
        this.saveBannerState(state);
      }
    },

    // 处理横幅可见性变更事件
    handleBannerVisibilityChanged(event) {
      if (typeof event.detail && event.detail.visible === 'boolean' && event.detail.visible) {
        // 收到显示 banner 事件，重新检查是否应该显示
        this.checkShouldShowBanner();
      }
    },

    // 检查卸载后重装情况
    checkUninstallReinstall() {
      if (typeof localStorage !== 'undefined') {
        const installTime = localStorage.getItem('cg_extension_install_time');
        const uninstallDate = localStorage.getItem('cg_uninstall_date');
        const state = this.getBannerState();

        // 如果有卸载日期，说明是卸载后重新访问，应该重置状态
        if (uninstallDate && state) {
          // 重置状态但保留安装时间
          const installTimeValue = state.installTime;
          const sessionId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
          const newState = {
            closedManually: false,
            showCount: 0,
            lastShowDate: '',
            installTime: installTimeValue,
            sessionId: sessionId,
            closedDate: null
          };
          this.saveBannerState(newState);
          // 清除卸载日期记录
          localStorage.removeItem('cg_uninstall_date');
        } else if (state && state.installTime && installTime && Number(installTime) > state.installTime) {
          // 重置状态
          const sessionId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
          const newState = {
            closedManually: false,
            showCount: 0,
            lastShowDate: '',
            installTime: Number(installTime),
            sessionId: sessionId,
            closedDate: null
          };
          this.saveBannerState(newState);
        } else if (state && !state.installTime && installTime) {
          // 首次记录安装时间
          state.installTime = Number(installTime);
          this.saveBannerState(state);
        } else if (!installTime) {
          // 记录当前时间为安装时间
          const now = Date.now();
          localStorage.setItem('cg_extension_install_time', now.toString());
          if (state) {
            state.installTime = now;
            this.saveBannerState(state);
          }
        }
      }
    }
  },
  mounted() {
    // 确保只在浏览器环境中执行
    if (typeof localStorage === 'undefined' || typeof window === 'undefined') return;

    this.isMobile = window.innerWidth <= 768
    // 检查卸载后重装情况
    this.checkUninstallReinstall();

    // 初始化 Banner 状态
    this.initBannerState();

    // 检查是否应该显示 Banner
    this.checkShouldShowBanner();

    // 每次页面加载时，重新检查 banner 显示状态
    window.addEventListener('load', () => {
      this.checkShouldShowBanner();
    });
  }
}
</script>

<style scoped>
.extension-banner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
  position: relative;
}

.banner-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1rem;
  color: white;
  flex-wrap: nowrap;
}

.banner-text {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 1.2;
  padding-right: 1rem;
}

.rocket-icon {
  display: inline-flex;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

.rocket-img {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

.banner-bg {
  background: linear-gradient(89.99deg, #685EF5 0%, #00AFB6 83.25%, #98C21D 143.53%);
}

.close-btn {
  background-color: transparent;
  background-image: none;
  border: none;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  color: white;
}

.close-btn:hover {
  color: #e2e2e2;
}

.close-img {
  width: 15px;
  height: 15px;
  margin-left: 0.25rem;
}

.chrome-btn {
  background: #FF7F41;
  color: white;
  height: 36px;
  padding: 0 10px;
  margin-left: 10px;
  border-radius: 63.04px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.chrome-btn:hover {
  background: #e86a2f;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.logo-img {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    padding: 0.5rem 2rem 0.5rem 1rem; /* 右侧增加空间给关闭按钮 */
    align-items: flex-start;
  }

  .banner-text {
    margin-bottom: 0.5rem;
    padding-right: 0;
  }

  .chrome-btn {
    margin-left: 0;
    font-size: 13px;
    height: 32px;
  }

  .close-btn {
    top: 10px;
    transform: none;
  }
}

/* 超小屏幕设备适配 */
@media (max-width: 375px) {
  .banner-text {
    font-size: 12px;
  }

  .chrome-btn {
    font-size: 12px;
    padding: 0 8px;
  }
}
</style>