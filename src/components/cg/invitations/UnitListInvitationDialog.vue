<template>
  <div>
    <el-dialog
        :visible.sync="visible"
        :append-to-body="true"
        :lock-scroll="false"
        :modal="false"
        :close-on-click-modal="false"
        width="409px"
        custom-class="cg-unit-list-invitation-dialog"
    >
      <div class="relative">
        <!-- 关闭按钮 -->
        <div class="absolute right-2 top-2 z-10 cursor-pointer" @click="close">
          <i class="el-icon-close" style="font-size: 20px; color: black;"></i>
        </div>

        <div class="flex items-start">
          <div class="dialog-content flex-col">
            <!-- 标题文本 -->
            <div class="text-16px leading-1-2 font-semibold">
              <div class="bg-clip-text text-transparent" style="background-image: linear-gradient(91deg, #9969FF 6.35%, #0089FF 72.75%, #10B3B7 104.79%);">Share Curriculum Genie and</div>
              <div class="mt-1"><span class="text-9969FF">earn</span> <span class="text-FF7F41">&nbsp;2 more units&nbsp;</span><span class="text-0089FF">today.</span></div>
            </div>

            <!-- 分享按钮 -->
            <div class="cg-unit-list-shared mt-2 flex justify-center" style="background:#D6F0F0;">
              <!-- Facebook -->
              <div class="flex flex-col items-center gap-2 cursor-pointer" :class="{ 'opacity-50 cursor-not-allowed user-select-none': getShareLinkLoading }">
                <a :href="getShareSocialLink('Facebook', shareLinkStore.shareLink + '&f=Facebook', shareContent)"
                   target="_blank"
                   :style="getShareLinkLoading ? 'pointer-events: none;' : ''"
                   @click="createShareReward('Facebook')"
                   class="w-45px h-45px rounded-full bg-3875EA flex items-center justify-center">
                  <img class="cursor-pointer w-full h-full" :src="require('@/assets/cg/images/plans/share/facebook-icon.svg')" />
                </a>
                <span @click="openNewWindow(getShareSocialLink('Facebook', shareLinkStore.shareLink + '&f=Facebook', shareContent))" class="text-base text-111C1C text-14px font-semibold">Facebook</span>
              </div>
              <!-- Instagram -->
              <div class="flex flex-col items-center gap-2 cursor-pointer" :class="{ 'opacity-50 cursor-not-allowed user-select-none': getShareLinkLoading }">
                <a :href="getShareSocialLink('X', shareLinkStore.shareLink + '&f=Twitter', shareContent)" target="_blank"
                   class="w-45px h-45px rounded-full bg-000000 flex items-center justify-center"
                   :style="getShareLinkLoading ? 'pointer-events: none;' : ''"
                   @click="createShareReward('Twitter')">
                  <img class="cursor-pointer w-full h-full" :src="require('@/assets/cg/images/plans/share/x-icon.png')" />
                </a>
                <span @click="openNewWindow(getShareSocialLink('X', shareLinkStore.shareLink + '&f=Twitter', shareContent))" class="text-base text-111C1C text-14px font-semibold">X (Twitter)</span>
              </div>
            </div>
          </div>

          <!-- 精灵图片 -->
          <div class="flex-none">
            <img :src="require('@/assets/cg/images/plans/share/gift-lg.png')" class="w-140px relative top-13px" />
          </div>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { useInvitationApi } from '@/api/cg/invitation'
import ShareSuccessDialog from './ShareSuccessDialog.vue'
import { mapState, mapActions } from 'vuex'
import { getRandomDescription } from '@/utils/meta'

export default {
  components: {
    ShareSuccessDialog
  },
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      shareLinkStore: state => state.cgShareLink,
      authStore: state => state.cgAuth
    })
  },
  data() {
    return {
      visible: false, // 是否显示
      shareEmails: '', // 邮箱
      getShareLinkLoading: false, // 获取分享链接 loading
      shareContent: 'Check out Curriculum Genie! Discover amazing tools to enhance learning.', // 分享内容
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapActions('cgShareLink', ['refreshShareLink']),
    getRandomDescription,
    /**
     * 获取带有用户ID的缓存Key
     */
     getCacheKey () {
      const userId = this.authStore && this.authStore.user && this.authStore.user.user_id || 'guest'
      // 获取用户ID，如果用户未登录则使用默认值
      return `${userId}_CG_UNIT_LIST_INVITATION_LAST_SHOW`
    },

    /**
     * 更新最后显示时间
     */
    updateLastShowTime () {
      const cacheKey = this.getCacheKey()
      localStorage.setItem(cacheKey, Date.now().toString())
    },

    /**
     * 是否能打开单元列表分享弹窗
     */
    canOpenUnitListShareDialog () {
      // 生成带用户ID的缓存key
      const cacheKey = this.getCacheKey()
      const lastShowTime = localStorage.getItem(cacheKey)
      if (!lastShowTime) {
        // 如果没有记录，说明是第一次显示
        return true
      }

      // 安全地解析上次显示时间
      const lastShowTimeMs = parseInt(lastShowTime, 10)
      if (isNaN(lastShowTimeMs)) {
        // 如果解析失败，视为第一次显示
        return true
      }
      // 定义常量：7天的毫秒数
      const SEVEN_DAYS_MS = 7 * 24 * 60 * 60 * 1000
      // 计算距离上次显示的时间（毫秒）
      const timeDiff = Date.now() - lastShowTimeMs
      return timeDiff >= SEVEN_DAYS_MS
    },
    /**
     * 打开弹窗
     */
    async open() {
      // 如果不能打开弹窗，则不显示
      if (!this.canOpenUnitListShareDialog() || !this.$route.path.includes('/unit-planner')) {
        return
      }
      // 查最新的链接
      this.getNewInvitationLink()
      // 显示弹窗
      this.visible = true
      // 更新最后显示时间
      this.updateLastShowTime()
      // 打开弹窗埋点
      this.$analytics.sendEvent('cg_unit_list_share_card_exposure')
    },

    /**
     * 关闭弹窗
     */
    close() {
      this.visible = false
      // 关闭弹窗埋点
      this.$analytics.sendEvent('cg_unit_list_share_card_close')
    },

    /**
     * 关闭弹窗且没有埋点事件
     */
    closeNoAnalyticsEvent() {
      this.visible = false
    },

    /**
     * 获取分享链接
     */
    async getNewInvitationLink() {
      this.getShareLinkLoading = true
      await this.refreshShareLink()
      this.getShareLinkLoading = false
    },

    /**
     * 创建分享奖励
     */
    async createShareReward(type) {
      const invitationApi = useInvitationApi()
      // 创建分享奖励
      try {
        await invitationApi.createShareReward({ share_type: type })
        // 添加2分钟延迟 (120000毫秒 = 2分钟)
        // 向iframe发送刷新数据的消息
        setTimeout(() => {
          window.postMessage({ type: 'REFRESH_UNIT_USAGE_DATA' }, '*')
        }, 120000)
      } catch (error) {
        this.$message.error('Failed to create share reward')
      }
      const tempType = type.trim().toLowerCase()
      // 创建分享点击埋点
      let eventName = ''
      if (tempType === 'twitter') {
        eventName = 'cg_unit_list_share_card_click_x'
      } else if (tempType === 'facebook') {
        eventName = 'cg_unit_list_share_card_click_fb'
      }
      this.$analytics.sendEvent(eventName)
    },


    /**
     * 打开新窗口
     */
    openNewWindow(url) {
      window.open(url, '_blank')
    },

    /**
     * 获取社交分享链接
     */
    getShareSocialLink(platform, url, text) {
      const encodedUrl = encodeURIComponent(url)
      const encodedText = encodeURIComponent(text)
      // 获取随即的描述
      const description = this.getRandomDescription()
      const contentEncoded = encodeURIComponent(description)
      switch (platform) {
        case 'Facebook':
          return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
        case 'X':
          const xContent = contentEncoded ? `${contentEncoded}${encodedUrl}` : encodedUrl
          return `https://x.com/intent/tweet?text=${xContent}`
        default:
          return ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog__wrapper {
  pointer-events: none;
  background: none;
}
/* 对话框样式 */
/deep/ .cg-unit-list-invitation-dialog {
  pointer-events: auto;
  position: fixed;
  right: 24px;
  bottom: 24px;
  margin: 0;
  padding: 3px !important;
  border-radius: 12px;
  overflow: visible;
  background: linear-gradient(140deg, #AA89F2 0%, #3CA2FF 48.41%, #10B3B7 100%);
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);

  /* 隐藏头部 */
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    overflow: visible;
    border-radius: 10px;
    background: radial-gradient(86.66% 54.5% at 73.44% 37.8%, rgba(255, 215, 78, 0.06) 0%, rgba(255, 255, 255, 0.00) 100%), linear-gradient(169deg, rgba(255, 255, 255, 0.00) 37.36%, rgba(16, 179, 183, 0.12) 92.74%), linear-gradient(158deg, rgba(170, 137, 242, 0.20) 2.58%, rgba(255, 255, 255, 0.00) 59.46%), #FFF;
  }
}

.cg-unit-list-shared {
  display: flex;
  width: 238px;
  height: 100px;
  justify-content: center;
  align-items: center;
  gap: 30px;
  border-radius: 12px;
  border: 2px solid #FFF;
  background: #D6F0F0;
}

/* 对话框内容样式 */
.dialog-content {
  padding: 20px 0 20px 20px;
}

/* 文字渐变效果 */
.text-gradient {
  background: linear-gradient(to right, #9969FF, #0089FF, #10B3B7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* 警告提示样式 */
.el-alert {
  padding: 9px 16px;
  border-radius: 4px;
  font-size: 16px;
  background-color: #FCF6EC;
  border: 1px solid rgba(230, 162, 60, 0.3);
}

.el-alert .el-alert__content {
  padding: 0;
}

.el-alert .el-alert__icon {
  margin-right: 0;
}

/* 内层框样式 */
.inner-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05);
}

/* 工具类 */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.right-2 {
  right: 8px;
}

.top-2 {
  top: 8px;
}

.z-10 {
  z-index: 10;
}

.cursor-pointer {
  cursor: pointer;
}

.flex {
  display: flex;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.flex-col {
  flex-direction: column;
}

.flex-none {
  flex: none;
}

.gap-2 {
  gap: 8px;
}

.mt-1 {
  margin-top: 4px;
}

.mt-2 {
  margin-top: 8px;
}

.w-45px {
  width: 45px;
}

.h-45px {
  height: 45px;
}

.w-140px {
  width: 140px;
}

.top-13px {
  top: 13px;
}

.rounded-full {
  border-radius: 9999px;
}

.bg-3875EA {
  background-color: #3875EA;
}

.bg-000000 {
  background-color: #000000;
}

.text-16px {
  font-size: 16px;
}

.text-14px {
  font-size: 14px;
}

.leading-1-2 {
  line-height: 1.2;
}

.font-semibold {
  font-weight: 600;
}

.text-9969FF {
  color: #9969FF;
}

.text-FF7F41 {
  color: #FF7F41;
}

.text-0089FF {
  color: #0089FF;
}

.text-111C1C {
  color: #111C1C;
}

.text-base {
  font-size: 16px;
}

.opacity-50 {
  opacity: 0.5;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.user-select-none {
  user-select: none;
}

.bg-clip-text {
  background-clip: text;
}

.text-transparent {
  color: transparent;
}
</style>
