<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :append-to-body="true"
      :close-on-click-modal="false"
      :class="{'cg-invitation-dialog-open': visible}"
      custom-class="cg-invitation-dialog"
    >
      <div class="text-default">
        <!-- 头部 -->
        <div class="header-container">
          <!-- 背景图片 -->
          <div v-if="source === 'UNIT_DOWNLOAD'" class="background-image-unit">
            <img :src="require('@/assets/cg/images/plans/share/share-icon-07.png')" class="full-width" />
          </div>
          <div v-else class="background-image-default">
            <img :src="require('@/assets/cg/images/plans/share/share-icon-05.png')" class="full-width" />
          </div>
          <!-- 关闭按钮
          <div class="close-button" @click="close">
            <i class="el-icon-close font-size-20" />
          </div> -->
          <!-- 标题 -->
          <div class="title-container">
            <div>Refer Friends, Get Rewarded</div>
          </div>
          <!-- 描述 -->
          <div class="description-container">
            Earn <span class="highlight-text">2 free unit creation credits</span> for every referral! 🎉
          </div>
        </div>
        <!-- 分享部分 -->
        <div class="share-content-container share-content">
          <!-- 提示信息 -->
          <div v-if="shareLinkStore.invitationCount >= 6">
            <template v-if="shareLinkStore.invitationCount >= 6 && authStore.hasPlugin">
            <el-alert
              type="warning"
              :closable="false"
              class="w-full"
            >
              <template #default>
                <div class="alert-content">
                  <el-icon><Warning /></el-icon>
                  <span>Reward limit reached for now, but your creativity is unlimited!</span>
                </div>
              </template>
            </el-alert>
          </template>
          <template v-else-if="!authStore.hasPlugin">
            <div class="extension-container"
                  style="background: linear-gradient(90deg, #E3F1FF 0%, #EBE6FF 100%);">
              <div class="extension-text-container">
                <div class="extension-text-wrapper">
                  <span class="share-extension-content">Download to unlock 2 units and unlimited access to powerful teaching tools!</span>
                </div>
              </div>
              <div class="extension-button-container">
                  <el-button
                  type="primary"
                  class="share-extension-button-content"
                  size="default"
                  style="height: 45px; z-index: 10; pointer-events: auto !important; padding: 8px 12px;"
                  @click="handleInstallExtension">
                  <div class="button-content">
                    <img :src="require('@/assets/cg/images/home/<USER>')" class="chrome-logo" />
                    Add to Chrome
                  </div>
                </el-button>
              </div>
            </div>
          </template>
          </div>

          <!-- 分享操作 -->
          <div class="share-actions-container">
            <!-- 分享链接 -->
            <div>
              <!-- 标题 -->
              <div class="share-title">
                Share on social media
                <span class="share-subtitle">(Reward will be credited within 2 mins.)</span>
              </div>
              <!-- 分享按钮 -->
              <div class="share-buttons-container">
                <!-- Facebook -->
                <div class="share-button-wrapper" :class="{ 'disabled-share': getShareLinkLoading }">
                  <a :href="getShareSocialLink('Facebook', shareLinkStore.shareLink + '&f=Facebook', shareContent)"
                     target="_blank"
                     :style="getShareLinkLoading ? 'pointer-events: none;' : ''"
                     @click="createShareReward('Facebook')"
                     class="facebook-button">
                    <el-image class="button-image" :src="require('@/assets/cg/images/plans/share/facebook-icon.svg')" />
                  </a>
                  <span @click="openNewWindow(getShareSocialLink('Facebook', shareLinkStore.shareLink + '&f=Facebook', shareContent))" class="button-label">Facebook</span>
                </div>
                <!-- Instagram -->
                <div class="share-button-wrapper" :class="{ 'disabled-share': getShareLinkLoading }">
                    <a :href="getShareSocialLink('X', shareLinkStore.shareLink + '&f=Twitter', shareContent)" target="_blank"
                      class="twitter-button"
                      :style="getShareLinkLoading ? 'pointer-events: none;' : ''"
                       @click="createShareReward('Twitter')">
                      <el-image class="button-image" :src="require('@/assets/cg/images/plans/share/x-icon.png')" />
                    </a>
                  <span @click="openNewWindow(getShareSocialLink('X', shareLinkStore.shareLink + '&f=Twitter', shareContent))" class="button-label">X (Twitter)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <share-success-dialog
      v-model:visible="showShareSuccess"
      :share-link="shareLinkStore.shareLink"
      @share="createShareReward"
    />
  </div>
</template>

<script>

import ShareSuccessDialog from './ShareSuccessDialog.vue'
import { mapActions, mapState } from 'vuex'
import { getRandomDescription } from '@/utils/meta'
import { useInvitationApi } from '@/api/cg/invitation'


export default {
  components: {
    ShareSuccessDialog
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    // 来源
    source: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      shareLinkStore: state => state.cgShareLink,
      authStore: state => state.cgAuth
    })
  },
  data() {
    return {
      visible: false, // 是否显示
      shareEmails: '', // 邮箱
      getShareLinkLoading: false, // 获取分享链接 loading
      shareContent: 'Check out Curriculum Genie! Discover amazing tools to enhance learning.', // 分享内容
      submitInvitationLoading: false, // 提交邀请 loading
      showShareSuccess: false,
      debounceTimeout: null // 延迟执行函数的定时器
    }
  },
  setup() {
  },
  mounted() {
  },
  methods: {
    ...mapActions('cgShareLink', ['refreshShareLink']),
    /**
     * 打开弹窗
     */
    async open() {
      // 查最新的链接
      this.getNewInvitationLink()
      this.visible = true
      // unit 下载场景的埋点
      if (this.source === 'UNIT_DOWNLOAD') {
        this.$analytics.sendEvent('cg_unit_details_share_pop')
      } else {
        // 打开弹窗埋点
        this.$analytics.sendEvent('cg_web_invite_show')
      }
    },

    /**
     * 关闭弹窗
     */
    close() {
      this.visible = false
      // unit 下载场景的埋点
      if (this.source === 'UNIT_DOWNLOAD') {
        this.$analytics.sendEvent('cg_unit_details_share_pop_close')
      } else {
        // 关闭弹窗埋点
        this.$analytics.sendEvent('cg_web_invite_close')
      }
    },


    /**
     * 提交邀请
     */
    async submitInvitation() {
      let emails = []
      // 没有邮箱直接返回
      if (!this.shareEmails) {
        this.$message.error('Please enter the email address')
        return
      }
      // 先以逗号分割邮箱地址
      emails = this.shareEmails.split(',')
      let hasErrorEmail = false
      // 遍历邮箱地址，判断是否有不合法的邮箱地址
      emails.forEach((email) => {
        // 邮箱验证的正则表达式
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        // 更新邮箱地址的验证结果
        if (!emailPattern.test(email.trim()) || email.trim().length > 64) {
          hasErrorEmail = true
        }
      })
      // 如果有不合法的邮箱地址，则提示错误
      if (hasErrorEmail) {
        this.$message.error('Invalid email address!')
        return
      }

      // 开始 loading
      this.submitInvitationLoading = true
      const invitationApi = useInvitationApi()
      try {
        await invitationApi.sendInvitationEmail({ emails })
        // 发送成功提示
        this.$message.success('Invitation sent successfully!')
        // 清空输入框
        this.shareEmails = ''
        // 向父组件发送邀请更新事件
        this.$emit('invitation-updated')
        // 向iframe发送刷新数据的消息
        iframe.sendRefreshMessage()
      } catch (error) {
        console.error(error)
        this.$message.error('Failed to send invitation')
      } finally {
        this.submitInvitationLoading = false
      }
      // 提交邀请埋点
      this.$analytics.sendEvent('cg_web_invite_email')
    },

    /**
     * 创建分享奖励
     */
    async createShareReward(type, needAnalytics = true) {
      const invitationApi = useInvitationApi()
      // 创建分享奖励
      try {
        await invitationApi.createShareReward({ share_type: type })
        // 向父组件发送邀请更新事件
        this.$emit('invitation-updated')
        // 添加2分钟延迟 (120000毫秒 = 2分钟)
        // 向iframe发送刷新数据的消息
        setTimeout(() => {
          window.postMessage({ type: 'REFRESH_UNIT_USAGE_DATA' }, '*')
        }, 120000)
      } catch (error) {
        this.$message.error('Failed to create share reward')
        console.error(error)
      }
      const tempType = type.trim().toLowerCase()
      // 不需要埋点直接返回
      if (!needAnalytics) {
        return
      }
      // 创建分享点击埋点
      let eventName = ''
      // unit 下载场景的埋点
      if (this.source === 'UNIT_DOWNLOAD') {
        if (tempType === 'twitter') {
          eventName = 'cg_unit_details_share_pop_click_x'
        } else if (tempType === 'facebook') {
          eventName = 'cg_unit_details_share_pop_click_fb'
        }
      } else {
        if (tempType === 'twitter') {
          eventName = 'cg_web_invite_twitter'
        } else if (tempType === 'linkedin') {
          eventName = 'cg_web_invite_linkedln'
        } else if (tempType === 'facebook') {
          eventName = 'cg_web_invite_facebook'
        }
      }
      this.$analytics.sendEvent(eventName)
    },

    /**
     * 复制链接
     */
    async copyLink() {
      try {
        await navigator.clipboard.writeText(this.shareLinkStore.shareLink + '&f=Link')
        this.showShareSuccess = true
      } catch (err) {
        console.error('Failed to copy:', err)
        this.$message.error('Failed to copy link')
      }
      // 复制链接埋点
      this.$analytics.sendEvent('cg_web_invite_copylink')
    },

    /**
     * 获取分享链接
     */
    async getNewInvitationLink() {
      this.getShareLinkLoading = true
      await this.refreshShareLink()
      this.getShareLinkLoading = false
    },

    /**
     * 打开新窗口
     */
    openNewWindow(url) {
      window.open(url, '_blank')
    },

    /**
     * 获取社交分享链接
     */
    getShareSocialLink(platform, url, text) {
      const encodedUrl = encodeURIComponent(url)
      const encodedText = encodeURIComponent(text)
      // 获取随即的描述
      const description = getRandomDescription()
      const contentEncoded = encodeURIComponent(description)
      switch (platform) {
        case 'Facebook':
          return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
          case 'X':
            const xContent = contentEncoded ? `${contentEncoded}${encodedUrl}` : encodedUrl
            return `https://x.com/intent/tweet?text=${xContent}`
        default:
          return ''
      }
    },

    /**
     * 处理安装插件
     */
    handleInstallExtension() {
      // 发送安装插件的埋点事件
      this.$analytics.sendEvent('cg_web_invite_install_extension')
      // 打开 Chrome 商店链接
      window.open('https://chromewebstore.google.com/detail/curriculum-genie/jbhknkdnmmcipjcfjngiojllgbgolmao', '_blank')
    },

    /**
     * 创建分享奖励并跳转页面
     * @param type
     */
    createShareRewardAndToPage(type) {
      // 清除定时器, 防抖
      if (this.debounceTimeout) clearTimeout(this.debounceTimeout)

      this.debounceTimeout = setTimeout(() => {
        switch (type) {
          case 'facebook':
            this.createShareReward('facebook', false)
            this.openNewWindow(this.getShareSocialLink('Facebook', this.shareLinkStore.shareLink + '&f=Facebook', this.shareContent))
            break
          case 'twitter':
            this.createShareReward('twitter', false)
            this.openNewWindow(this.getShareSocialLink('X', this.shareLinkStore.shareLink + '&f=Twitter', this.shareContent))
            break
        }
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
.step-number {
  font-size: 1.25rem;
  font-weight: 600;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: none;
  border-radius: 10px;
  background: #D7E2FA;
  color: #4A81EF;
}
.step-title {
  font-size: 0.875rem;
  font-weight: 600;
}
.step-desc {
  font-size: 0.875rem;
}

.share-content {
  position: relative;
  &::before {
    content: "";
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(rgba(170, 137, 242, 0.2), rgba(16, 179, 183, 0.2), rgba(255, 255, 255, 0.2));
    border-radius: 20px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
  }
}

.share-extension-content {
/* Regular/16px */
font-family: Inter;
font-size: 16px;
font-style: normal;
font-weight: 400;
line-height: 24px; /* 150% */
}

.share-extension-button-content {
/* Regular/16px */
font-family: Inter;
font-size: 16px;
font-style: normal;
font-weight: 600;
line-height: 24px; /* 150% */
}

/* 新增的普通 CSS 样式 */
.header-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 2.25rem;
  margin-bottom: 2.25rem;
  position: relative;
}

.background-image-unit {
  position: absolute;
  top: -100px;
  z-index: 0;
  width: 100px;
  height: 100px;
}

.background-image-default {
  position: absolute;
  left: 0.375rem;
  top: -20px;
  z-index: 0;
  width: 100px;
  height: 80px;
}

.close-button {
  position: absolute;
  right: 1.5rem;
  top: -0.75rem;
  z-index: 10;
  cursor: pointer;
}

.title-container {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-default);
  display: flex;
  align-items: center;
  justify-content: center;
}

.description-container {
  margin-top: 0.75rem;
  font-size: 18px;
  text-align: center;
  color: var(--text-default);
}

.highlight-text {
  color: #FF7F41;
  font-weight: 600;
}

.share-content-container {
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 22px;
  padding-bottom: 22px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: white;
  border-radius: 20px;
  border: 1px solid white;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.extension-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0.5rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.extension-text-container {
  display: flex;
  align-items: center;
  width: 60%;
}

.extension-text-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.extension-button-container {
  display: flex;
  width: 40%;
  justify-content: flex-end;
}

.button-content {
  display: flex;
  align-items: center;
}

.chrome-logo {
  width: 30px;
  height: 30px;
  margin-right: 0.5rem;
}

.share-actions-container {
  display: flex;
  flex-direction: column;
  flex: auto;
}

.share-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111C1C;
}

.share-subtitle {
  font-size: 0.875rem;
  font-weight: 400;
  color: #676879;
}

.share-buttons-container {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
  gap: 105px;
}

.share-button-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.disabled-share {
  opacity: 0.5;
  cursor: not-allowed;
  user-select: none;
}

.facebook-button {
  width: 80px;
  height: 80px;
  border-radius: 9999px;
  background-color: #3875EA;
  display: flex;
  align-items: center;
  justify-content: center;
}

.twitter-button {
  width: 80px;
  height: 80px;
  border-radius: 9999px;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-image {
  cursor: pointer;
  width: 100%;
  height: 100%;
}

.button-label {
  font-size: 1rem;
  color: #111C1C;
  opacity: 0.8;
}

.full-width {
  width: 100%;
}
</style>

<style lang="scss" scoped>
// 弹窗样式
/deep/.cg-invitation-dialog {
    padding: 0 !important;
    border-radius: 20px 20px 20px 20px;
    background: linear-gradient(344deg, rgba(255, 255, 255, 0.40) 38.49%, rgba(255, 255, 255, 0.20) 54.5%, rgba(16, 179, 183, 0.20) 71.4%, rgba(170, 137, 242, 0.40) 93.6%), #FFF;
    width: 620px;
    // 隐藏头部
    .el-dialog__header {
      button {
        font-size: 20px;
      }
    }

    .el-dialog__body {
      padding: 0;
    }
}

// 警告提示样式
.el-alert {
  --el-alert-padding: 9px 16px;
  --el-alert-border-radius: 4px;
  --el-alert-title-font-size: 16px;
  --el-alert-description-font-size: 16px;
  --el-alert-warning-color: #E6A23C;
  background-color: #FCF6EC;
  border: 1px solid rgba(230, 162, 60, 0.3);

  .el-alert__content {
    padding: 0;
  }

  .el-alert__icon {
    margin-right: 0;
  }
}
</style>
