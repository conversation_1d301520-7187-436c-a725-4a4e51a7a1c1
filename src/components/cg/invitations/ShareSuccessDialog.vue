<template>
  <el-dialog
    v-model="internalVisible"
    width="400px"
    class="cg-share-success-dialog"
    align-center
    :show-close="false"
    @closed="closeDialog"
  >
    <div class="success-title">
      <PERSON> copied
    </div>
    <div class="success-info">
      Spread the word and share Curriculum Genie on:
    </div>
    <!-- 分享按钮 -->
    <div class="flex justify-center items-center gap-4 share-card">
      <a :href="getShareSocialLink('Facebook', shareLink + '&f=Facebook', shareContent)" 
         target="_blank" 
         @click="shareToSocial('Facebook')">
        <img class="w-10 h-10" src="/images/plans/share/fackbook-icon.png" alt="Facebook" />
      </a>
      <a :href="getShareSocialLink('X', shareLink + '&f=Twitter', shareContent)" 
         target="_blank" 
         @click="shareToSocial('Twitter')">
        <img class="w-10 h-10" src="/images/plans/share/x-icon.png" alt="Twitter" />
      </a>
      <a :href="getShareSocialLink('Linkedin', shareLink + '&f=LinkedIn', shareContent)" 
         target="_blank" 
         @click="shareToSocial('LinkedIn')">
        <img class="w-10 h-10" src="/images/plans/share/linkedin-icon.png" alt="LinkedIn" />
      </a>
    </div>
    <div class="success-close" @click="close">
      <img class="w-8 h-8" src="/images/plans/share/share-success-close.png" alt="Close" />
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { getShareSocialLink } from '@/utils/share-social'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shareLink: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      internalVisible: false,
      shareContent: 'Check out Curriculum Genie! Discover amazing tools to enhance learning.'
    }
  },
  mounted() {
    document.documentElement.style.setProperty(
      "--cg-share-success-image", 
      `url(/images/plans/share/share-success.png)`
    )
  },
  watch: {
    visible(val) {
      this.internalVisible = val
    }
  },
  methods: {
    getShareSocialLink,
    shareToSocial(type) {
      this.$emit('share', type)
      this.close()
    },
    close() {
      this.internalVisible = false
      this.$emit('update:visible', false)
    },
    closeDialog() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss">
.cg-share-success-dialog {
  background-color: transparent;
  box-shadow: none;
  .success-title{
    color: #111C1C;
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 130% */
  }
  .success-info{
    color: #111C1C;
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .el-dialog__body{
    position: relative;
    border-radius: 12px;
    &::before{
      content: "";
      height: 116px;
      width: 261px;
      display: block;
      position: absolute;
      top: -80px;
      left: calc(50% - 130px);
      background: transparent var(--cg-share-success-image) no-repeat center top;
      background-size: 261px 116px;
      box-shadow: none;
    }
    padding-top: 50px !important;
    padding-bottom: 36px !important;
    background: #fff;
    .share-card {
      gap: 12px;
      margin-top: 16px;
      & > div{
        flex: none;
      }
      p{
        display: none;
      }
    }
    .success-close{
      cursor: pointer;
      position: absolute;
      left: 170px;
      bottom: -48px;
      font-size: 32px;
      color: rgba(245, 246, 248, 1);
    }
  }
  .share-card {
    display: flex;
    gap: 20px;
    text-align: center;
    justify-content: center;
    & > div {
      flex: 1;
    }
    p {
      color: #111c1c;
      font-size: 12px;
      display: block;
      margin-top: 10px;
    }
    & > div {
      cursor: pointer;
    }
    img {
      width: 48px;
      height: 48px;
    }
  }
}
</style> 