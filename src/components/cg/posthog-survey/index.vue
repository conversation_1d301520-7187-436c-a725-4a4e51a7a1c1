<template>
    <div>
        <el-dialog
            :visible.sync="visible"
            :append-to-body="true"
            :modal="false"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            width="400px"
            custom-class="cg-unit-survey-dialog"
        >
            <!-- 关闭按钮 -->
            <div class="close-button" @click="close(true)">
                <i class="el-icon-close" style="font-size: 20px; color: #000;"></i>
            </div>

            <div class="survey-container">
                <!-- 第一步：满意度问题 -->
                <div v-if="currentStep === 1">
                    <!-- 标题文本 -->
                    <h2 class="survey-title">
                        How satisfied are you with the overall quality of this unit plan?
                    </h2>

                    <!-- 表情选择 -->
                    <div class="rating-container">
                        <!-- 表情图标区域 -->
                        <div class="emoji-container">
                            <div
                                v-for="(emoji, index) in emojis"
                                :key="index"
                                class="emoji-option"
                                @click="selectRating(index+1)"
                            >
                                <div class="emoji-circle">
                                    <component
                                        :is="emoji.icon"
                                        :class="{ 'selected-icon': selectedRating === index+1 }"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        <!-- 标签区域 -->
                        <div class="rating-labels">
                            <div class="rating-label">Very dissatisfied</div>
                            <div class="rating-label">Very satisfied</div>
                        </div>
                    </div>

                    <!-- 追问问题（仅在评分1-3时显示） -->
                    <div v-if="showFollowUpQuestion" class="follow-up-question">
                        <h3 class="follow-up-title">
                            Could you tell us what could be improved?
                        </h3>
                        <el-input
                            v-model="followUpAnswer"
                            type="textarea"
                            :rows="4"
                            placeholder="We'd love to hear your feedback to make things better for you."
                            :maxlength="500"
                            show-word-limit
                        ></el-input>
                    </div>

                    <!-- 提交按钮 -->
                    <el-button
                        type="primary"
                        class="submit-button"
                        :disabled="selectedRating === null"
                        size="large"
                        @click="submitFirstStep"
                    >
                        Submit
                    </el-button>
                </div>

                <!-- 第二步：推荐度问题 -->
                <div v-if="currentStep === 2" class="step-two">
                    <!-- 标题文本 -->
                    <h2 class="survey-title">
                        How likely are you to recommend Curriculum Genie to other teachers?
                    </h2>

                    <!-- 数字评分选择（按钮组形式） -->
                    <div class="rating-scale">
                        <div class="rating-buttons-container">
                            <el-button-group class="rating-button-group">
                                <el-button
                                    v-for="n in 11"
                                    :key="n-1"
                                    class="rating-number-button"
                                    :type="recommendRating === n-1 ? 'primary' : 'default'"
                                    @click="selectRecommendRating(n-1)"
                                >
                                    {{ n-1 }}
                                </el-button>
                            </el-button-group>
                        </div>
                        
                        <!-- 标签区域 -->
                        <div class="rating-labels">
                            <div class="rating-label">Not at all likely</div>
                            <div class="rating-label">Extremely likely</div>
                        </div>
                    </div>

                    <!-- 推荐度追问问题（仅在评分0-8时显示） -->
                    <div v-if="showRecommendFollowUp" class="follow-up-question">
                        <h3 class="follow-up-title">
                            What made you hesitant to recommend it?
                        </h3>
                        <el-input
                            v-model="recommendFollowUpAnswer"
                            type="textarea"
                            :rows="4"
                            placeholder="We'd love to hear your feedback to make things better for you."
                            :maxlength="500"
                            show-word-limit
                        ></el-input>
                    </div>

                    <!-- 提交按钮 -->
                    <el-button
                        type="primary"
                        size="large"
                        class="submit-button"
                        :disabled="recommendRating === null"
                        @click="submitFinalFeedback"
                    >
                        Submit
                    </el-button>
                </div>

                <!-- 第三步：感谢反馈 -->
                <div v-show="currentStep === 3" class="thank-you-container">
                    <!-- 感谢图标 -->
                    <div class="high-five-icon">
                        <!-- 使用SVG或图片 -->
                        <img :src="require('@/assets/cg/images/home/<USER>')" alt="">
                    </div>
                    
                    <!-- 感谢文字 -->
                    <h2 class="thank-you-title">
                        Thank you for your feedback!
                    </h2>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>

// 定义表情SVG组件
const VeryDissatisfiedIcon = {
    render(h) {
        return h('svg', {
            attrs: {
                width: 60,
                height: 60,
                viewBox: '0 0 60 60',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg'
            },
            class: 'emoji-svg'
        }, [
            h('path', {
                attrs: {
                    d: 'M29.9999 50.0922C27.3965 50.0922 24.8186 49.5725 22.4134 48.5628C20.0082 47.553 17.8228 46.0731 15.982 44.2073C14.1411 42.3416 12.6808 40.1266 11.6846 37.6889C10.6883 35.2512 10.1755 32.6385 10.1755 30C10.1755 27.3614 10.6883 24.7487 11.6846 22.311C12.6808 19.8733 14.1411 17.6583 15.982 15.7926C17.8228 13.9269 20.0082 12.4469 22.4134 11.4371C24.8186 10.4274 27.3965 9.90771 29.9999 9.90771C35.2576 9.90771 40.3 12.0246 44.0178 15.7926C47.7356 19.5606 49.8242 24.6712 49.8242 30C49.8242 35.3288 47.7356 40.4393 44.0178 44.2073C40.3 47.9753 35.2576 50.0922 29.9999 50.0922Z',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            }),
            h('path', {
                attrs: {
                    d: 'M21.189 38.9301L23.3917 36.6976L26.6957 38.9301L29.9998 36.6976L33.3038 38.9301L36.6079 36.6976L38.8106 38.9301M22.2903 28.8839L25.5944 25.5352L22.2903 22.1865M37.7093 28.8839L34.4052 25.5352L37.7093 22.1865',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            })
        ])
    }
}

const DissatisfiedIcon = {
    render(h) {
        return h('svg', {
            attrs: {
                width: 60,
                height: 60,
                viewBox: '0 0 60 60',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg'
            },
            class: 'emoji-svg'
        }, [
            h('path', {
                attrs: {
                    d: 'M30 50.0922C41.0457 50.0922 50 41.0457 50 30C50 18.9543 41.0457 9.90771 30 9.90771C18.9543 9.90771 10 18.9543 10 30C10 41.0457 18.9543 50.0922 30 50.0922Z',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            }),
            h('path', {
                attrs: {
                    d: 'M21 39C21 39 24.75 35 30 35C35.25 35 39 39 39 39M21 25H21.02M39 25H39.02',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            })
        ])
    }
}

const NeutralIcon = {
    render(h) {
        return h('svg', {
            attrs: {
                width: 60,
                height: 60,
                viewBox: '0 0 60 60',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg'
            },
            class: 'emoji-svg'
        }, [
            h('path', {
                attrs: {
                    d: 'M30 50.0922C41.0457 50.0922 50 41.0457 50 30C50 18.9543 41.0457 9.90771 30 9.90771C18.9543 9.90771 10 18.9543 10 30C10 41.0457 18.9543 50.0922 30 50.0922Z',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            }),
            h('path', {
                attrs: {
                    d: 'M21 36H39M21 25H21.02M39 25H39.02',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            })
        ])
    }
}

const SatisfiedIcon = {
    render(h) {
        return h('svg', {
            attrs: {
                width: 60,
                height: 60,
                viewBox: '0 0 60 60',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg'
            },
            class: 'emoji-svg'
        }, [
            h('path', {
                attrs: {
                    d: 'M30 50.0922C41.0457 50.0922 50 41.0457 50 30C50 18.9543 41.0457 9.90771 30 9.90771C18.9543 9.90771 10 18.9543 10 30C10 41.0457 18.9543 50.0922 30 50.0922Z',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            }),
            h('path', {
                attrs: {
                    d: 'M21 33C21 33 24.75 37 30 37C35.25 37 39 33 39 33M21 25H21.02M39 25H39.02',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            })
        ])
    }
}

const VerySatisfiedIcon = {
    render(h) {
        return h('svg', {
            attrs: {
                width: 60,
                height: 60,
                viewBox: '0 0 60 60',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg'
            },
            class: 'emoji-svg'
        }, [
            h('path', {
                attrs: {
                    d: 'M48.7227 29.4415C48.7225 25.3735 47.504 21.4013 45.2283 18.0504C42.9525 14.6995 39.7268 12.1276 35.9777 10.6749C32.2286 9.2222 28.1327 8.95711 24.2318 9.91471C20.3308 10.8723 16.8085 13.0075 14.1307 16.0378C11.4529 19.0681 9.74582 22.8509 9.23512 26.8859C8.72442 30.9208 9.4342 35.0178 11.2706 38.6351C13.107 42.2523 15.9835 45.2194 19.5197 47.1439C23.0558 49.0684 27.0851 49.8597 31.0746 49.4132M22.2902 24.9765H22.3123M35.5065 24.9765H35.5285',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            }),
            h('path', {
                attrs: {
                    d: 'M23.3916 36.1392C24.198 36.9245 25.1595 37.5276 26.2128 37.9086C27.2661 38.2896 28.3871 38.4398 29.5019 38.3493M44.3041 50.6502L49.8505 44.8904C50.5388 44.1679 50.9243 43.2032 50.9259 42.1988C50.9276 41.1944 50.5453 40.2284 49.8593 39.5035C49.5256 39.1502 49.125 38.8688 48.6814 38.6762C48.2378 38.4836 47.7605 38.3838 47.2779 38.3827C46.7953 38.3817 46.3175 38.4794 45.8731 38.6701C45.4287 38.8608 45.0269 39.1404 44.6918 39.4923L44.3217 39.8763L43.9517 39.4923C43.6178 39.1391 43.2171 38.8579 42.7734 38.6655C42.3298 38.4731 41.8523 38.3735 41.3698 38.3727C40.8872 38.3719 40.4094 38.4698 39.9651 38.6607C39.5208 38.8515 39.1191 39.1314 38.7841 39.4834C38.0964 40.2059 37.7114 41.1701 37.7098 42.174C37.7081 43.1778 38.09 44.1434 38.7753 44.8681L44.3041 50.6502Z',
                    stroke: '#676879',
                    'stroke-width': 4,
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round'
                },
                class: 'svg-path'
            })
        ])
    }
}

export default {
    components: {
        VeryDissatisfiedIcon,
        DissatisfiedIcon,
        NeutralIcon,
        SatisfiedIcon,
        VerySatisfiedIcon
    },
    data() {
        return {
            visible: false, // 是否显示
            currentStep: 1, // 当前步骤：1-满意度问题，2-推荐度问题，3-感谢
            
            // 第一步：满意度评价
            selectedRating: null, // 选择的评分
            followUpAnswer: '', // 追问的回答
            
            // 第二步：推荐度评价
            recommendRating: null, // 推荐度评分(0-10)
            recommendFollowUpAnswer: '', // 推荐度追问回答
            
            // 调查问卷 emoji
            emojis: [
                { icon: 'VeryDissatisfiedIcon', label: 'Very dissatisfied' },
                { icon: 'DissatisfiedIcon', label: 'Dissatisfied' },
                { icon: 'NeutralIcon', label: 'Neutral' },
                { icon: 'SatisfiedIcon', label: 'Satisfied' },
                { icon: 'VerySatisfiedIcon', label: 'Very satisfied' }
            ],
            survey: null,
            unitId: '', // 单元 ID
        }
    },
    computed: {
        // 判断是否需要显示满意度追问问题
        showFollowUpQuestion() {
            // 当用户选择1-3级表情时显示追问
            return this.selectedRating !== null && this.selectedRating <= 3;
        },
        // 判断是否需要显示推荐度追问问题
        showRecommendFollowUp() {
            // 当用户选择0-8分时显示追问
            return this.recommendRating !== null && this.recommendRating <= 8;
        }
    },
    async mounted () {
        // 获取 Posthog 调查问卷
        this.survey = await this.$analytics.getPosthogSurvey('PCG_CSAT_NPS', true)
    },
    methods: {
        /**
         * 打开弹窗
         */
        async open(unitId) {
            if (!this.survey) return;
            this.unitId = unitId;
            // 重置状态
            this.currentStep = 1;
            this.selectedRating = null;
            this.followUpAnswer = '';
            this.recommendRating = null;
            this.recommendFollowUpAnswer = '';
            
            // 显示弹窗
            this.visible = true;
            // 实现显示 Posthog 调查问卷
            this.$analytics.showPosthogSurvey(this.survey.id);
        },

        /**
         * 关闭弹窗
         */
        close(manual = false) {
            this.visible = false;
            // 问卷只回答到第二道题目就被关闭时, 发送 Posthog 调查问卷第一道题目的数据
            if (this.currentStep === 2 && manual) {
                const feedbackData = this.getSurveyData();
            
                // 发送 Posthog 调查问卷请求
                this.$analytics.sendPosthogSurvey(feedbackData);
            }
            // 发送关闭 Posthog 调查问卷
            this.$analytics.dismissPosthogSurvey(this.survey.id);
        },

        /**
         * 选择满意度评分
         */
        selectRating(rating) {
            this.selectedRating = rating;
        },
        
        /**
         * 选择推荐度评分
         */
        selectRecommendRating(rating) {
            this.recommendRating = rating;
        },

        /**
         * 提交第一步
         */
        async submitFirstStep() {
            if (this.selectedRating === null) return;
            
            // 进入第二步
            this.currentStep = 2;
        },
        
        /**
         * 提交最终反馈
         */
        async submitFinalFeedback() {
            if (this.recommendRating === null) return;
            
            // 准备完整的反馈数据
            const feedbackData = this.getSurveyData();
            
            // 发送 Posthog 调查问卷请求
            this.$analytics.sendPosthogSurvey(feedbackData);
            
            // 显示感谢页面
            this.currentStep = 3;
            
            // 1秒后自动关闭
            setTimeout(() => {
                this.close();
            }, 1000);
        },

        getSurveyData() {
            if (!this.survey) return;
            let surveyData = {
                $survey_id: this.survey.id,
                $survey_questions: this.survey.questions.map((question) => {
                    return {
                        id: question.id,
                        question: question.question
                    }
                })
            };
            // 遍历调查问卷的每个问题
            this.survey.questions.forEach((question, index) => {
                if (index === 0) {
                    surveyData[`$survey_response_${question.id}`] = this.selectedRating !== null ? String(this.selectedRating) : null;
                } else if (index === 1) {
                    surveyData[`$survey_response_${question.id}`] = this.showFollowUpQuestion ? this.followUpAnswer : '';
                } else if (index === 2) {
                    surveyData[`$survey_response_${question.id}`] = this.recommendRating !== null ? String(this.recommendRating) : null;
                } else if (index === 3 ) {
                    surveyData[`$survey_response_${question.id}`] = this.showRecommendFollowUp ? this.recommendFollowUpAnswer : '';
                } else if (index === 4) {
                    surveyData[`$survey_response_${question.id}`] = this.unitId;
                }
            });
            return surveyData;
        }
    }
}
</script>

<style lang="scss" scoped>

.el-dialog__wrapper {
  pointer-events: none;
  background: none;
}

/* 对话框样式 */
/deep/.cg-unit-survey-dialog {
    pointer-events: auto;
    position: fixed;
    right: 24px;
    bottom: 24px;
    margin: 0;
    padding: 3px !important;
    border-radius: 12px;
    overflow: visible;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);

    // 隐藏头部
    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        padding: 0;
        overflow: visible;
        border-radius: 10px;
    }

    .el-textarea__inner {
        max-height: 200px;
    }
}


/* 关闭按钮 */
.close-button {
    position: absolute;
    right: 8px;
    top: 8px;
    z-index: 10;
    cursor: pointer;
}

/* 满意度调查样式 */
.survey-container {
    padding: 24px;
    color: #333;
    
    .survey-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
    }
    
    .rating-container {
        margin-bottom: 16px;
        margin-top: 16px;
    }
    
    .emoji-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }
    
    .emoji-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
    }
    
    .emoji-circle {
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .rating-labels {
        display: flex;
        justify-content: space-between;
    }
    
    .rating-label {
        font-size: 14px;
    }
    
    .follow-up-question {
        margin-bottom: 16px;
        transition: all 0.3s ease;
    }
    
    .follow-up-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
    }
    
    .submit-button {
        width: 100%;
        height: 50px;
        
        span {
            font-size: 20px;
        }
    }
    
    .step-two {
        margin-top: 8px;
    }
    
    .rating-scale {
        margin-bottom: 16px;
    }
    
    .rating-buttons-container {
        display: flex;
        justify-content: space-between;
    }
    
    .rating-button-group {
        width: 100%;
        display: flex;
        
        .el-button {
            width: 32px;
            border-radius: 0;
            color: #111C1C;
            flex: 1;
            padding: 0;
            height: 48px;
            
            &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
            }
            
            &:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }

        .el-button--primary {
            color: #FFF;
        }
    }
    
    .thank-you-container {
        padding-top: 32px;
        padding-bottom: 32px;
        text-align: center;
        animation: fadeIn 0.5s ease-in-out;
    }
    
    .high-five-icon {
        display: flex;
        justify-content: center;
        margin-bottom: 16px;
    }
    
    .thank-you-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
    }
    
    /deep/ .selected-icon {
        .svg-path {
            stroke: #10B3B7 !important;
        }
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
}
</style>
