<template>
  <div>
    <!-- 反馈按钮 -->
    <el-button @click="open" class="adapt-import-feed-back-btn" plain>
      <span class="display-flex">
        <i class="lg-icon lg-icon-question"></i>
        <span style="vertical-align: text-bottom;">Feedback</span>
      </span>
    </el-button>

    <!-- 反馈弹窗 -->
    <el-dialog :title="$t('loc.adaptImportFeedbackTitle')" :visible.sync="dialogVisible" width="600px"
               :before-close="handleClose" custom-class="adapt-import-feed-back-dialog">
      <div>
        <div class="text-default font-size-16 font-weight-400 line-height-22">{{
            $t('loc.adaptImportFeedbackTip')
          }}
        </div>
        <div class="text-default font-size-16 font-weight-600 line-height-24 add-margin-t-16">
          {{ $t('loc.adaptImportFeedbackSelect') }}
        </div>
        <!-- 反馈选项 -->
        <div v-for="option in feedbackOptions" :key="option.label" class="adapt-import-feed-back-radio">
          <el-radio v-model="radio" :label="option.label">
            <span class="adapt-import-feed-back-radio-text">
              {{ $t(option.textKey) }}
            </span>
          </el-radio>
        </div>

        <!-- 反馈内容 -->
        <el-input class="add-margin-t-16" type="textarea" :rows="4" :placeholder="$t('loc.adaptImportFeedbackInput')"
                  v-model="content">
        </el-input>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{ $t('loc.cancel') }}</el-button>
        <el-button type="primary" :disabled="!content || !content.trim()" @click="submitFeedback">{{
            $t('loc.confirm')
          }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AdaptImportFeedBack',
  data() {
    return {
      dialogVisible: false, // 反馈弹窗是否显示
      radio: '1', // 反馈选项
      content: '', // 反馈内容
      survey: null, // 反馈调查
      feedbackOptions: [
        { label: '1', textKey: 'loc.adaptImportFeedbackSelect1' },
        { label: '2', textKey: 'loc.adaptImportFeedbackSelect2' },
        { label: '3', textKey: 'loc.adaptImportFeedbackSelect3' },
        { label: '4', textKey: 'loc.adaptImportFeedbackSelect4' },
        { label: '5', textKey: 'loc.adaptImportFeedbackSelect5' }
      ] // 反馈选项
    }
  },
  async mounted() {
    // 获取 Posthog 调查问卷
    this.survey = await this.$analytics.getPosthogSurvey('ADAPT_IMPORT_UNIT', false)
  },
  methods: {
    // 打开反馈弹窗
    open() {
      if (!this.survey) return
      this.radio = '1'
      this.content = ''
      this.dialogVisible = true
      // 实现显示 Posthog 调查问卷
      this.$analytics.showPosthogSurvey(this.survey.id)
    },
    
    // 提交反馈
    submitFeedback() {
      // 准备完整的反馈数据
      const feedbackData = this.getSurveyData()
      // 发送 Posthog 调查问卷请求
      this.$analytics.sendPosthogSurvey(feedbackData)
      this.dialogVisible = false
      this.$message.success(this.$t('loc.adaptImportFeedbackSuccess'))
    },
    
    getSurveyData() {
      if (!this.survey) return
      let surveyData = {
        $survey_id: this.survey.id,
        $survey_questions: this.survey.questions.map((question) => {
          return {
            id: question.id,
            question: question.question
          }
        })
      }
      // 遍历调查问卷的每个问题
      this.survey.questions.forEach((question, index) => {
        if (index === 0) {
          surveyData[`$survey_response_${question.id}`] = this.$t('loc.adaptImportFeedbackSelect' + this.radio)
        } else if (index === 1) {
          surveyData[`$survey_response_${question.id}`] = this.content
        }
      })
      return surveyData
    },
    
    // 关闭反馈弹窗
    handleClose() {
      // 发送关闭 Posthog 调查问卷
      this.$analytics.dismissPosthogSurvey(this.survey.id)
      this.dialogVisible = false
    }
    
  }
}
</script>

<style scoped lang="less">
.adapt-import-feed-back-btn {
  color: var(--111-c-1-c, #111C1C);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  
  i {
    font-size: 20px;
    margin-right: 8px;
  }
  
}

/deep/ .adapt-import-feed-back-dialog {
  .el-dialog__header {
    padding: 24px;
  }
  
  .el-dialog__body {
    padding: 0 24px;
    
    color: #111C1C;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
  
  .el-dialog__footer {
    padding: 24px;
  }
}

.adapt-import-feed-back-radio {
  padding-left: 16px;
  margin-top: 8px;
}

.adapt-import-feed-back-radio-text {
  color: var(--111-c-1-c, #111C1C);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}
</style>