<template>
  <el-dialog
    v-model="visible"
    width="1000px"
    modal-class="login-dialog"
    :close-on-click-modal="false"
    :show-close="false"
    @close="handleClose"
    class="vertical-center-dialog hide-header-dialog"
  >
    <!-- 内容分为左右两部分 -->
    <div class="flex">
      <!-- 左边 -->
      <div class="w-[500px]">
        <!-- 头部图片 -->
        <div class="w-full h-[600px]">
          <img src="/images/auth/login-banner.png" class="w-full h-full" />
        </div>
      </div>
      <!-- 右边 -->
      <div class="w-[500px]">
        <!-- 头部操作 -->
        <div class="flex justify-between items-center p-5 pb-0">
          <!-- 返回 -->
          <div class="cursor-pointer" @click="handleBack">
            <el-icon v-if="step !== 'login-options'">
              <ElIconArrowLeft />
            </el-icon>
          </div>
          <!-- 关闭 -->
          <div class="cursor-pointer" @click="handleClose">
            <el-icon>
              <ElIconClose class="" />
            </el-icon>
          </div>
        </div>
        <!-- Logo -->
        <div class="flex justify-center items-center">
          <img src="/images/logo.png" alt="Logo" class="w-12 h-12" />
        </div>
        <!-- 根据当前步骤显示不同组件 -->
        <div class="p-6 pb-16" v-if="visible">
          <login-options
            v-if="step === 'login-options'"
            @close="close"
            @emailLogin="handleEmailLogin"
            @loginSuccess="loginSuccess"
          />
          <!-- 账号密码登录 -->
          <password-login
            v-if="step === 'password-login'"
            :initialEmail="email"
            @forgotPassword="handleForgotPassword"
            @updateEmail="email = $event"
            @close="close"
            @back="step = 'login-options'"
            @loginSuccess="loginSuccess"
          />
          <!-- 邮箱验证 -->
          <verify-email
            v-if="step === 'verify-email'"
            :initialEmail="email"
            @verified="handleEmailVerified"
            @back="step = 'login-options'"
          />
          <!-- 设置密码 -->
          <set-password
            v-if="step === 'set-password'"
            :initialEmail="email"
            @close="close"
            @signIn="step = 'password-login'"
            @back="step = 'login-options'"
          />
          <!-- 忘记密码 -->
          <forget-password
            v-if="step === 'forget-password'"
            :initialEmail="email"
            @updateEmail="email = $event"
            @back="step = 'login-options'"
            @close="close"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import LoginOptions from './login-options.vue'
import PasswordLogin from './password-login.vue'
import VerifyEmail from './verify-email.vue'
import SetPassword from './set-password.vue'
import ForgetPassword from './forget-password.vue'

export default defineComponent({
  props: {
    // 登录成功回调
    onLoginSuccess: {
      type: Function,
      default: null
    }
  },

  components: {
    LoginOptions,
    PasswordLogin,
    VerifyEmail,
    SetPassword,
    ForgetPassword,
  },

  data() {
    return {
      visible: false,
      step: 'login-options', // login-options | password-login | verify-email | set-password | forget-password
      email: '',
    }
  },

  methods: {
    open() {
      this.visible = true
      this.step = 'login-options'
      this.email = ''
      // 进入登录页面埋点
      this.$analytics.sendEvent('cg_web_login_exposure')
    },

    close() {
      this.visible = false
      this.step = 'login-options'
      this.email = ''
    },

    handleBack() {
      // 如果是忘记密码页面，则返回登录
      if (this.step === 'forget-password') {
        this.step = 'password-login'
      } else {
        this.step = 'login-options'
      }
    },

    handleClose() {
      // 增加关闭登录弹窗埋点
      this.$analytics.sendEvent('cg_web_login_close')
      this.visible = false
      this.step = 'login-options'
      this.email = ''
    },

    async handleGoogleLogin() {
      // TODO: 实现 Google 登录逻辑
    },

    /**
     * 邮箱登录
     */
    async handleEmailLogin(email: string, is_exist: boolean) {
      this.email = email
      // 邮箱存在，下一步密码登录
      // 邮箱不存在，下一步邮箱验证
      this.step = is_exist ? 'password-login' : 'verify-email'
      // 进入登录或验证邮箱页面埋点
      const eventName = is_exist ? 'cg_web_login_email_exposure' : 'cg_web_emailverification_exposure'
      this.$analytics.sendEvent(eventName)
    },

    handleForgotPassword() {
      this.step = 'forget-password'
      // 点击忘记密码埋点
      this.$analytics.sendEvent('cg_web_login_forgot')
    },

    handleEmailVerified() {
      this.step = 'set-password'
      // 点击验证邮箱埋点
      this.$analytics.sendEvent('cg_web_signup_exposure')
    },

    /**
     * 登录成功
     */
    loginSuccess() {
      // 登录成功埋点
      this.$analytics.sendEvent('cg_k12_login_completelogin')
      // 如果有回调则执行回调，否则跳转到个人信息页面
      if (this.onLoginSuccess) {
        this.onLoginSuccess()
      } else {
        this.$router.push('/user-profile')
      }
    }
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-button--primary) {
  @apply px-2 py-3 !important;
  height: 48px !important;
  border-radius: 8px;
}
</style>

<style lang="scss">
.login-dialog {
  .el-dialog {
    border-radius: 20px;
    padding: 0;
  }
}
</style>
