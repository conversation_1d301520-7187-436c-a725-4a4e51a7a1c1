<template>
  <div class="flex flex-col items-center justify-center">
    <div class="w-[300px]">

      <div class="flex justify-center mb-[60px]">
        <span class="font-semibold text-2xl text-default">Continue with Email</span>
      </div>

      <div class="flex flex-col gap-6">
        <!-- Email Input -->
        <el-input
          v-model="email"
          type="email"
          name="email" autocomplete="username"
          placeholder="Your email"
          class="h-[48px]"
          @input="onEmailInput"
        />

        <!-- Password Input -->
        <el-input
          v-model="password"
          :type="showPassword ? 'text' : 'password'"
          name="password" autocomplete="current-password"
          placeholder="Your password"
          class="h-[48px]"          
          :class="{ 'is-invalid': isPasswordEmpty }"
          @input="updateErrorFlag"
        >
          <i slot="suffix" class="lg-icon" style="line-height: 48px; margin-right: 8px;" :class="{ 'lg-icon-eye-off': !showPassword, 'lg-icon-eye': showPassword }" @click="showPassword = !showPassword"></i>
        </el-input>
      </div>

      <!-- Forgot Password Link -->
      <div class="flex justify-end mt-3">
        <el-button
          link
          type="text"
          class="text-sm -my-3"
          @click="$emit('forgotPassword')"
        >
          Forgot password?
        </el-button>
      </div>

      <!-- Login Button -->
      <el-button
        type="primary"
        class="w-full h-[48px] text-base mt-6"
        :loading="isLoading"
        @click="handleLogin"
      >
        Sign In
      </el-button>

      <!-- Error Messages -->
      <div v-if="isPasswordEmpty" class="text-red-500 text-xs mt-1">
        Password is required
      </div>
      <div v-if="errorMessage" class="text-red-500 text-xs mt-1">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthApi } from '@/api/cg/auth'
import { mapActions } from 'vuex'

export default {
  name: 'CGPasswordLogin',

  props: {
    isLoginPage: {
      type: Boolean,
      default: false
    },
    initialEmail: {
      type: String,
      default: ''
    }
  },

  emits: ['close', 'back', 'forgotPassword', 'loginSuccess', 'updateEmail'],

  data() {
    return {
      email: this.initialEmail,
      password: '',
      isLoading: false,
      isPasswordEmpty: false,
      showPassword: false,
      errorMessage: ''
    }
  },

  methods: {
    ...mapActions('cgAuth', ['setAuthInfo','sendInstallGuideEvent']),
    onEmailInput(value) {
      this.email = value
      this.$emit('updateEmail', value)
    },

    updateErrorFlag() {
      this.isPasswordEmpty = false
      this.errorMessage = ''
    },

    async handleLogin() {
      if (this.isLoginPage) {
        // 点击登录埋点
        this.$analytics.sendEvent('lg_cg_login_signin')
      } else {
        // 点击注册埋点
        this.$analytics.sendEvent('cg_web_login_signin')
      }
      if (!this.password) {
        this.isPasswordEmpty = true
        return
      }

      this.isLoading = true
      this.errorMessage = ''

      try {
        const authApi = useAuthApi()

        const loginResult = await authApi.login({
          email: this.email,
          password: this.password,
          invitation_code: localStorage.getItem('invitationCode') || undefined,
          template_id: localStorage.getItem('templateId') || undefined,
          invitation_from: localStorage.getItem('invitationFrom') || undefined
        })
        
        // 更新认证信息
        await this.setAuthInfo({
          ...loginResult,
          sync: true,
          updateAuth: true
        })

        if (!this.isLoginPage) {
          this.$emit('loginSuccess')
          this.$emit('close')
        } else {
          this.$emit('close')
          this.$emit('loginSuccess')
        }
        this.$analytics.sendEvent('cg_user_logged_in')
        this.$message.success('Welcome to Curriculum Genie!')
        // 清理邀请码
        localStorage.removeItem('invitationCode')
        localStorage.removeItem('templateId')
        localStorage.removeItem('invitationFrom')
        // 登录后发送插件引导事件
        this.sendInstallGuideEvent()
      } catch (error) {
        this.errorMessage = error.message || 'Login failed. Please try again.'
        console.error('Login error:', error)
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 密码登录样式 */
</style> 