<template>
  <div class="flex flex-col items-center justify-center">
    <div class="items-center w-[300px]">

      <div class="flex justify-center gap-2 items-center mb-[60px]">
        <span class="font-semibold text-2xl text-default">Create an Account</span>
      </div>

      <!-- 表单区域 -->
      <div class="flex flex-col items-center justify-end">
        <!-- 邮箱 -->
        <el-input
          type="text"
          disabled
          placeholder="Email Address"
          class="h-12 text-base font-semibold email-disabled"
          :value="email"
        />

        <!-- 密码 -->
        <el-input
          :type="showPassword ? 'text' : 'password'"
          placeholder="Your Password"
          class="h-12 mt-6"
          v-model="password"
          @input="validatePasswordTemp"
          >
          <i slot="suffix" class="lg-icon" style="line-height: 48px; margin-right: 8px;" :class="{ 'lg-icon-eye-off': !showPassword, 'lg-icon-eye': showPassword }" @click="showPassword = !showPassword"></i>
        </el-input>

        <!-- 确认密码 -->
        <el-input
          :type="showAgainPassword ? 'text' : 'password'"
          placeholder="Confirm Password"
          class="h-12 mt-6"
          v-model="againPassword"
          @input="validatePasswordTemp"
          >
          <i slot="suffix" class="lg-icon" style="line-height: 48px; margin-right: 8px;" :class="{ 'lg-icon-eye-off': !showAgainPassword, 'lg-icon-eye': showAgainPassword }" @click="showAgainPassword = !showAgainPassword"></i>
        </el-input>

        <!-- 注册按钮 -->
        <el-button
          type="primary"
          class="w-full h-12 text-base mt-6"
          @click="handleSignUp"
          :loading="isLoading"
        >
          Sign Up
        </el-button>

        <!-- 密码强度提示 -->
        <div v-if="easyPasswordHint" class="text-xs text-red-500 mt-0 self-start">
          Password needs 8-20 characters, letters and numbers included.
        </div>

        <!-- 密码不匹配提示 -->
        <div v-if="showPasswordHint" class="text-xs text-red-500 mt-0 self-start">
          Passwords don't match.
        </div>
      </div>

      <!-- 登录链接 -->
      <div class="flex justify-center items-center text-sm mt-2 text-default">
        Already have an account?&nbsp;
        <el-button  type="text" class="" @click="handleSignIn">
          Sign In
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthApi } from '@/api/cg/auth'
import { mapActions, mapState } from 'vuex'
import { LESSON_PLAN_NEW_USER_UTC } from '../../../utils/const'
import tools from '../../../utils/tools'

export default {
  name: 'SetPassword',

  props: {
    initialEmail: {
      type: String,
      required: true
    },
    isLoginPage: {
      type: Boolean,
      default: false
    }
  },

  emits: ['signIn', 'close'],

  data() {
    return {
      email: this.initialEmail,
      password: '',
      againPassword: '',
      easyPasswordHint: false,
      showPasswordHint: false,
      isLoading: false,
      showPassword: false, // 是否显示密码
      showAgainPassword: false, // 是否显示确认密码
    }
  },

  created() {
  },
  computed: {
    ...mapState({
      cgCurrentUser: state => state.cgAuth.user
    })
  },
  methods: {
    ...mapActions('cgAuth', [ 'setToken', 'setUser', 'setAuthInfo']),
    validatePassword(pwd) {
      const hasNumber = /\d/.test(pwd)
      const hasLetter = /[a-zA-Z]/.test(pwd)
      const isValidLength = pwd.length >= 8 && pwd.length <= 20
      return hasNumber && hasLetter && isValidLength
    },

    validatePasswordTemp() {
      this.easyPasswordHint = !this.validatePassword(this.password)
      this.showPasswordHint = false
    },

    validatePasswordMatch() {
      const isMatch = this.password === this.againPassword
      this.showPasswordHint = !isMatch
      return isMatch
    },

    async handleSignUp() {
      // 校验密码
      if (!this.validatePassword(this.password)) {
        this.easyPasswordHint = true
        return
      }

      if (!this.validatePasswordMatch()) {
        this.showPasswordHint = true
        return
      }

      if (this.isLoginPage) {
        // 点击注册埋点
        this.$analytics.sendEvent('lg_cg_login_account_click_signup')
      } else {
        // 点击设置密码埋点
        this.$analytics.sendEvent('cg_web_account_click_signup')
      }

      this.isLoading = true

      const { register, getCurrentUser } = useAuthApi()
      try {
        // 注册用户
        const res = await register({
          email: this.email,
          password: this.password,
          invitation_code: localStorage.getItem('invitationCode') || undefined,
          template_id: localStorage.getItem('templateId') || undefined,
          invitation_from: localStorage.getItem('invitationFrom') || undefined
        })
        // // 用户 ID
        // const userId = res.user_id
        // const accessToken = res.access_token
        // // 存储认证信息
        // this.setToken(accessToken)
        // // 获取用户信息
        // const userInfo = await getCurrentUser({ user_id: userId, email: this.email })
        // // 存储用户信息
        // this.setUser(userInfo)

        // 新用户注册埋点
        if (this.isLoginPage && res && res.new_user) {
          // 发送注册成功埋点
          this.$analytics.sendEvent('cg_user_signed_up', { signup_method: 'email' })
        }

        // 更新认证信息
        await this.setAuthInfo({
          ...res,
          sync: true,
          updateAuth: true
        })

        // await this.setAuthHeader()
        // 清理邀请码
        localStorage.removeItem('invitationCode')
        localStorage.removeItem('templateId')
        localStorage.removeItem('invitationFrom')

        this.$message.success('Registration successful')
        // 跳转页面到 App 页面
        // 如果当前时间大于 LESSON_PLAN_NEW_USER_UTC，则跳转到 Lesson Plan 页面
        if (this.cgCurrentUser && tools.timeIsAfter(this.cgCurrentUser.created_at_utc, LESSON_PLAN_NEW_USER_UTC)) {
          this.$router.push('/lessons')
        } else {
          this.$router.push('/curriculum-genie/unit-planner')
        }
        this.$emit('close')
      } catch (error) {
        this.$message.error('Registration failed')
        console.error(error)
      } finally {
        this.isLoading = false
      }
    },

    handleSignIn() {
      // 点击登录埋点
      this.$analytics.sendEvent('cg_web_account_click_signin')
      // 返回登录页面
      this.$emit('signIn')
    }
  }
}
</script>

<style lang="scss" scoped>
/* 设置密码样式 */
</style> 