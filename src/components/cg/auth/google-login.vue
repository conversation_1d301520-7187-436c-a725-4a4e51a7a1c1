<template>
  <div class="flex justify-center gap-2 items-center">
    <div class="relative w-full">
      <!-- Google 登录按钮渲染 -->
      <div id="cg-google-login-button"
        @click="handleLogin"
        @mouseenter="isHovered = true"
        @mouseleave="isHovered = false"
        ref="googleLoginButtonRef"></div>
      <!-- 实际登录按钮 -->
      <el-button
        type="primary"
        style=""
        class="w-full large-btn"
        :class="{'is-hover': isHovered}"
        :disabled="scriptLoading"
        :loading="loginLoading"
        @click.stop
      >
        <img class="w-6 h-6" src="@/assets/cg/images/auth/google-logo.png"/>
        <span class="text-base ml-2">Sign in with Google</span>
      </el-button>
    </div>
  </div>
</template>

<script>
import { useAuthApi } from "@/api/cg/auth"
import { mapState, mapActions } from 'vuex'

// 假设 useAuthStore, ElMessage, useGsiScript, useRuntimeConfig, google, nextTick 在 Vue 2 环境中通过其他方式提供或已全局注册
// 例如: useAuthStore() 可能是 this.$store (Vuex)
// ElMessage 可能是 this.$message (Element UI)
// google 和 nextTick 需要确保在 Vue 2 环境中可用

export default {
  name: 'GoogleLogin',
  props: {
    isLoginPage: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      isHovered: false,
      scriptLoading: false, // 用于管理 Google GSI 脚本的加载状态
      loginLoading: false,
      scriptLoaded: false, // GSI 脚本是否已成功加载
      scriptLoadError: null, // GSI 脚本加载错误信息
      // renderOptions 来自原 Vue 3 代码，保持其结构
      renderOptions: {
        type: "rectangular",
        shape: "square",
        theme: "outline",
        size: "large",
        width: "400",
        height: "50",
        text: "signin",
        locale: 'en_US'
      },
      // config 和 googleLoginButtonRef 在 Vue 2 中通常通过 this.$refs 和配置方法获取
      // 我们将通过 getGoogleClientId 和 this.$refs.googleLoginButtonRef 来处理
    };
  },
  watch: {
    ...mapState('auth', ['authInfo']),
    scriptLoaded(loaded) {
      if (loaded) {
        this.initGoogleLogin();
      }
    },
    scriptLoadError(error) {
      if (error) {
        this.scriptLoading = false;
        console.error("Google GSI Script load failed:", error);
      }
    }
  },
  methods: {
    ...mapActions('cgAuth', ['setAuthInfo']),
    // 辅助方法：加载 Google GSI 脚本
    loadGoogleGsiScript() {
      if (this.scriptLoaded || (window.google && window.google.accounts)) {
        this.scriptLoaded = true; // 已加载，直接初始化
        // this.initGoogleLogin(); // watch 会处理
        return;
      }
      if (this.scriptLoading) return; // 避免重复加载

      this.scriptLoading = true;
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        this.scriptLoaded = true; // 触发 watch scriptLoaded
        // this.scriptLoading = false; // initGoogleLogin 之后再设置 false
      };
      script.onerror = (e) => {
        this.scriptLoadError = e; // 触发 watch scriptLoadError
      };
      document.head.appendChild(script);
    },

    // 处理登录成功回调
    async loginCallback(response) {
      this.loginLoading = true;
      const authApi = useAuthApi(); // 假设此函数按预期工作

      try {
        const loginResult = await authApi.googleLogin({
          id_token: response.credential,
          invitation_code: localStorage.getItem('invitationCode') || undefined,
          template_id: localStorage.getItem('templateId') || undefined,
          invitation_from: localStorage.getItem('invitationFrom') || undefined
        });
        
        // 假设 Vuex store 名为 'auth' (或根据您的 store 结构调整)
        await this.setAuthInfo({ // 替换 useAuthStore
          ...loginResult,
          sync: true,
          updateAuth: true
        });
        // await this.setAuthHeader()        

        this.$emit('success', response);
        this.$emit('loginSuccess');
        this.$analytics.sendEvent('cg_user_logged_in')
        this.$message.success('Welcome to Curriculum Genie!'); // 替换 ElMessage

        localStorage.removeItem('invitationCode');
        localStorage.removeItem('templateId')
        localStorage.removeItem('invitationFrom');

        this.$analytics.sendEvent('install_guide')
        
        const isNewUser = loginResult.new_user;
        if (isNewUser) {
          // 发送注册成功埋点
          this.$analytics.sendEvent('cg_user_signed_up', { signup_method: 'google' })
        }
      } catch (error) {
        console.error("Login failed:", error);
        this.$message.error('Login failed'); // 替换 ElMessage
        this.$emit('error', error); // 假设 error 结构兼容
      } finally {
        this.loginLoading = false;
      }
    },

    // 处理点击登录按钮
    async handleLogin() {
      if (!this.scriptLoaded) {
        this.loadGoogleGsiScript(); // 尝试加载脚本
        return;
      }
      if (!window.google || !window.google.accounts || !window.google.accounts.id) {
          this.scriptLoaded = false; // 重置状态，以便下次点击时能重新加载
          return;
      }

      // 确保 googleLoginButtonRef 存在
      const googleButtonContainer = this.$refs.googleLoginButtonRef;
      if (!googleButtonContainer) {
        console.error("googleLoginButtonRef is not available.");
        return;
      }

      // 原代码中 if (true || ...) 逻辑强制每次重新渲染，此处保留该行为意图
      // 即便 iframe 已存在，也重新 renderButton，然后模拟点击
      try {
        // 渲染 Google 登录按钮到指定的 DOM 元素
        window.google.accounts.id.renderButton(
          googleButtonContainer,
          this.renderOptions // 使用 data 中的 renderOptions
        );
        
        // 延迟以确保按钮被渲染和可交互
        this.$nextTick(() => { // 替换 nextTick
          const button = googleButtonContainer.querySelector('[role="button"]');
          if (button) {
            const stopPropagationListener = (e) => { // 移除 MouseEvent 类型注解
              e.stopPropagation();
            };
            try {
              button.addEventListener('click', stopPropagationListener);
              button.click();
            } finally {
              button.removeEventListener('click', stopPropagationListener);
            }
          } else {
            console.warn("Google's rendered button not found immediately after renderButton call.");
            // 尝试直接请求 code client，作为备选方案 (如果 renderButton 后找不到内部按钮)
            // this.requestCodeClient(); // 如果需要这种备选逻辑
          }
        });
      } catch(renderError){
        console.error("Error rendering or clicking Google button:", renderError);
      }

      // 登录相关埋点 (使用 Vuex)
      this.$analytics.sendEvent('cg_web_login_google')
      if (this.isLoginPage) { 
        this.$analytics.sendEvent('lg_k12_login_startlogin', { 'button_name': 'google' })
      } else {
        this.$analytics.sendEvent('cg_k12_login_startlogin', { 'button_name': 'google' })
      }
    },

    // 初始化 Google 登录客户端
    async initGoogleLogin() {
      if (this.scriptLoading && !this.scriptLoaded) { // 脚本还在加载中
          console.log("Waiting for GSI script to load before initializing...");
          return;
      }
      if (!window.google || !window.google.accounts || !window.google.accounts.id) {
        console.error("Google GSI script not available for initialization.");
        this.scriptLoadError = new Error("Google GSI script not available for initialization."); // 设置错误状态
        // this.scriptLoading = false; // 在 watch scriptLoadError 中处理
        return;
      }
      
      this.scriptLoading = true; // 标记开始初始化
      try {
        await window.google.accounts.id.initialize({
          client_id: process.env.VUE_APP_GOOGLE_CLIENT_ID, // 使用辅助方法获取 client_id
          callback: this.loginCallback
        });

        // 初始化成功后，首次渲染按钮
        this.$nextTick(() => { // 替换 nextTick
          if (this.$refs.googleLoginButtonRef) {
            window.google.accounts.id.renderButton(
              this.$refs.googleLoginButtonRef,
              this.renderOptions
            );
          } else {
             console.warn("googleLoginButtonRef not found for initial renderButton in initGoogleLogin.");
          }
        });
        this.scriptLoading = false; // 初始化完成
      } catch (error) {
        console.error("Google ID Initialization Error:", error);
        this.scriptLoadError = error; // 设置错误状态，会触发 watch
        // this.scriptLoading = false; // 在 watch scriptLoadError 中处理
      }
      // Google 登录提示 (如果需要可以取消注释)
      // window.google.accounts.id.prompt();
    }
  },
  mounted() {
    // 仅在客户端执行脚本加载
    if (typeof window !== 'undefined') {
      this.loadGoogleGsiScript(); // 尝试加载 GSI 脚本
    }
  }
};
</script>

<style lang="scss" scoped>
#cg-google-login-button {
  position: absolute;
  z-index: 999;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  opacity: 0;
  overflow: hidden;
}

.is-hover {
  color: #ffffff;
  border-color: #58cacd;
  background-color: #58cacd;
  outline: none;
}

</style>

<style lang="scss">
// 保持 Google 登录按钮和实际登录按钮高度一致
#cg-google-login-button {
  > div {
    height: 100%;
    > div {
      height: 100%;
      > div {
        height: 100%;
        width: 100%;
        z-index: 999;
      }
    }
  }
  iframe {
    width: 100% !important;
    margin: auto !important;
    position: absolute !important;
    height: 100% !important;
  }
}
</style>
