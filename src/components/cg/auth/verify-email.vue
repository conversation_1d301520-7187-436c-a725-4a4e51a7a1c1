<template>
  <div class="flex flex-col items-center justify-center" v-show="showEmailVerification"         
    @keyup.enter="verifyCode"
  >
    <!-- 内容 -->
    <div class="items-center w-[300px]">
      <!-- 标题 -->
      <div class="flex justify-center gap-2 items-center mb-[60px]">
        <span class="font-semibold text-2xl text-default">Email Verification</span>
      </div>
      <!-- 描述 -->
      <div class="flex justify-center items-center break-words text-base text-default">
        <span>
          Please check your inbox at
          <span class="text-primary">{{ email }}</span>
          for your verification code.
        </span>
      </div>
      <!-- 验证码输入框 -->
      <div class="input-box pt-6">
        <div class="flex flex-row justify-between items-center w-full" @paste="handlePaste">
          <el-input
            v-for="(item, index) in 6"
            :key="index"
            class="input-square"
            :class="{ 'input-verified': verificationInputs[index] }"
            v-model="verificationInputs[index]"
            @input="(value) => handleInput(value, index)"
            @keydown.native="(e) => handleKeydown(e, index)"
            @click="(e) => handleClick(e, index)"
          />
        </div>
      </div>

      <!-- 验证按钮 -->
      <div class="flex flex-col items-center mt-6 justify-end">
        <el-button
          type="primary"
          class="w-full h-12 text-base mb-6"
          :loading="verifyLoading"
          @click="verifyCode"
        >
          Verify
        </el-button>

        <!-- 提示信息 -->
        <div class="w-full text-left">
          <div v-show="showHint" class="text-xs -mt-5 text-danger">
            Incorrect verification code. Please try again.
          </div>
          <div v-show="showTimeout" class="text-xs -mt-5">
            The verification code has expired.
            <el-button link type="text" class="" @click="resendCode">
              Resend
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthApi } from '@/api/cg/auth'

export default {
  name: 'VerifyEmail',

  components: {
  },

  props: {
    initialEmail: {
      type: String,
      required: true
    },
    isLoginPage: {
      type: Boolean,
      default: false
    }
  },

  emits: ['goBack', 'closeWindow', 'closeToobar', 'setLanguage', 'verified'],

  data() {
    return {
      email: this.initialEmail,
      showEmailVerification: true,
      showHint: false,
      showTimeout: false,
      verifyLoading: false,
      verificationInputs: ['', '', '', '', '', '']
    }
  },

  mounted() {
    // 自动聚焦第一个输入框
    setTimeout(() => {
      const inputs = document.querySelectorAll('.el-input__inner')
      if (inputs[0]) {
        inputs[0].focus()
      }
    }, 0)
  },

  setup() {
    const authStore = useAuthStore()
    return {
      authStore
    }
  },

  watch: {
    verificationInputs: {
      handler(newVal) {
        // 检查所有输入框是否都已填写
        if (
          newVal.every(v => v) && // 所有输入框非空
          !this.verifyLoading     // 未处于 loading 状态
        ) {
          // 自动调用验证码校验
          this.verifyCode()
        }
      },
      deep: true // 深度监听数组
    }
  },

  methods: {
    handleClick(event, index) {
      const input = event.target
      if (this.verificationInputs[index]) {
        // 将光标移动到内容末尾
        input.setSelectionRange(1, 1)
      }
    },

    handleInput(value, index) {
      // 获取所有数字
      const numbers = value.replace(/\D/g, '').split('')

      // 如果没有数字输入，直接返回
      if (numbers.length === 0) {
        return
      }

      // 总是使用最后输入的数字
      this.verificationInputs[index] = numbers[numbers.length - 1]

      // 如果有有效输入且不是最后一个框，移动到下一个
      if (this.verificationInputs[index] && index < 5) {
        setTimeout(() => {
          const inputs = document.querySelectorAll('.el-input__inner')
          if (inputs[index + 1]) {
            inputs[index + 1].focus()
          }
        }, 0)
      }
    },

    handleKeydown(event, index) {
      // 处理删除键
      if (event.key === 'Backspace') {
        // 如果当前输入框有值，清空当前输入框
        if (this.verificationInputs[index]) {
          this.verificationInputs[index] = ''
        }
        // 如果当前输入框为空且不是第一个输入框，移动到前一个输入框并清空
        else if (index > 0) {
          this.verificationInputs[index - 1] = ''
          setTimeout(() => {
            const inputs = document.querySelectorAll('.el-input__inner')
            if (inputs[index - 1]) {
              inputs[index - 1].focus()
            }
          }, 0)
        }
      }
    },

    handlePaste(event) {
      event.preventDefault()

      const clipData = event.clipboardData || window.clipboardData
      const pastedData = clipData.getData('text/plain').trim()

      // 只允许数字
      if (!/^\d*$/.test(pastedData)) {
        return
      }

      const input = event.target
      const inputs = input.parentElement
        ? input.parentElement.querySelectorAll('.el-input__inner')
        : null

      // 获取当前焦点输入框的索引
      let focusIndex = 0
      if (inputs) {
        inputs.forEach((input, idx) => {
          if (document.activeElement === input) {
            focusIndex = idx
          }
        })
      }

      // 处理粘贴的数据
      let pasteValues = pastedData.split('')
      if (pasteValues.length === 6) {
        // 如果是6位数字，直接填充所有输入框
        this.verificationInputs = pasteValues
        if (inputs && inputs[5]) {
          inputs[5].focus()
        }
      } else {
        // 从当前位置开始填充
        const remainingSlots = 6 - focusIndex
        pasteValues = pasteValues.slice(0, remainingSlots)

        for (let i = 0; i < pasteValues.length; i++) {
          this.verificationInputs[focusIndex + i] = pasteValues[i]
        }

        // 聚焦到最后填充的输入框的下一个位置
        const nextFocusIndex = Math.min(focusIndex + pasteValues.length, 5)
        if (inputs && inputs[nextFocusIndex]) {
          inputs[nextFocusIndex].focus()
        }
      }
    },

    clearInputs() {
      this.verificationInputs = ['', '', '', '', '', '']
      // 聚焦第一个输入框
      const inputs = document.querySelectorAll('.el-input__inner')
      if (inputs && inputs.length > 0) {
        inputs[0].focus()
      }
    },

    async verifyCode() {
      if (this.isLoginPage) {
        // 点击验证埋点
        this.$analytics.sendEvent('lg_cg_login_emailverification_verify')
      } else {
        // 点击验证埋点
        this.$analytics.sendEvent('cg_web_emailverification_verify')
      }

      // 校验验证码
      if (this.verificationInputs.some(v => !v)) {
        this.showHint = true
        return
      }
      this.showHint = false

      this.verifyLoading = true
      const code = this.verificationInputs.join('')

      const { verifyEmailCode } = useAuthApi()
      try {
        const res = await verifyEmailCode({
          email: this.email,
          code
        })

        const { is_exist, is_correct } = res

        if (is_exist && is_correct) {
          this.showHint = false
          this.showTimeout = false
          this.$emit('verified')
        } else if (is_exist) {
          this.showHint = true
        } else {
          this.showTimeout = true
          this.showHint = false
        }
      } catch (error) {
        this.$message.error('Failed to verify email code')
        console.error(error)
      } finally {
        this.verifyLoading = false
      }
    },

    async resendCode() {
      // 点击重发埋点
      this.$analytics.sendEvent('cg_web_emailverification_click_resend')
      // 重置
      this.showTimeout = false
      this.clearInputs()

      const { sendVerifyEmailCode } = useAuthApi()

      try {
        await sendVerifyEmailCode(this.email)
      } catch (error) {
        this.$message.error('Failed to send verification code')
        console.error(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 验证邮箱样式 */
</style> 