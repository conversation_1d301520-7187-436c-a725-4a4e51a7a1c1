<template>
  <div class="login-options flex flex-col justify-center items-center">
    <img src="@/assets/cg/images/home/<USER>" style="width: 120px; margin-bottom: 20px;" />
    <div class="flex justify-center gap-2 items-center text-center">
      <div class="font-semibold text-2xl text-default">Welcome to K-12 Curriculum Genie</div>
    </div>

    <div class="flex justify-center gap-2 items-center mt-3 text-center">
      <span class="text-base text-default">Crafting Future-Ready Curriculum with AI</span>
    </div>

    <div class="w-[300px] mt-[60px]" :class="{ 'mt-[30px]': isLoginPage }">
      <client-only>
        <google-login ref="googleLogin" :isLoginPage="isLoginPage" @success="onGoogleLoginSuccess" @error="onGoogleLoginError"  @loginSuccess="onLoginSuccess"/>
      </client-only>

      <el-divider class="!my-8">or</el-divider>

      <el-form ref="emailForm" :model="form" :rules="rules" :show-message="false" @submit.prevent>
        <el-form-item prop="email" class="!mb-6">
          <el-input
            v-model="form.email"
            v-show="!loginLoading"            
            placeholder="Email Address"
            class="h-[48px]"
            size="large"
            name="email" autocomplete="username"
            @blur="onEmailBlur"
            @input="updateShowContinue"
            @keyup.enter="handleContinue"
          />
          <el-input
            v-if="loginLoading"
            disabled
            v-model="cgUser.email"
            placeholder="Email Address"
            class="h-[48px]"
            size="large"
            name="email" autocomplete="username"
            @blur="onEmailBlur"
            @input="updateShowContinue"
            @keyup.enter="handleContinue"
          />
        </el-form-item>

        <el-button
          type="primary"
          class="w-full text-base h-[48px]"
          :loading="continueLoading || loginLoading"
          :disabled="!showContinue"
          @click="handleContinue"
        >
          {{ !loginLoading ? 'Continue' : 'Loading...' }}
        </el-button>
      </el-form>

      <div
        v-if="form.email && !isEmailValid"
        class="flex justify-start items-center text-xs text-[#F56C6C] mt-2"
      >
        Please enter a valid email address
      </div>

      <!-- 切换到 Early Learning -->
      <div class="flex justify-center mt-9" v-if="isLoginPage && shouldShowSwitchButton">
        <el-button
          class="text-sm switch-to-early-learning"
          @click="switchToEarlyLearning"
        >
          <img :src="switchEarlyIcon" class="mr-2"  alt="Switch to Early Learning "/>
          <span class="text-center text-[#10b3b7] text-sm font-semibold font-['Inter']">Switch to Early Learning</span>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { useAuthApi } from '@/api/cg/auth'
import { mapState } from 'vuex'
import GoogleLogin from './google-login.vue'
import switchEarlyIcon from '@/assets/cg/images/login/switch.svg'

export default {
  name: 'CGLoginOptions',
  
  components: {
    GoogleLogin
  },

  props: {
    isLoginPage: {
      type: Boolean,
      default: false
    },
    loginLoading: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['close', 'emailLogin', 'loginSuccess'],

  data() {
    return {
      form: { // 邮箱登录表单内容
        email: ''
      },
      rules: { // 邮箱登录表单验证规则
        email: [
          { type: 'email', message: 'Please input correct email address', trigger: 'blur' }
        ]
      },
      googleLoading: false, // 谷歌登录按钮loading状态
      continueLoading: false, // 继续按钮loading状态
      showContinue: false, // 是否显示继续按钮
      enableEmailFormatCheck: false, // 是否启用邮箱格式校验
      // lg 平台 URL，根据环境配置
      lgWebPortalUrl: process.env.VUE_APP_LG_WEB_PORTAL_URL,
      fromInvitation: false // 邀请链接
    }
  },

  mounted () {
    this.$nextTick(() => {
      // 从邀请链接登录时，不显示切换按钮
      this.fromInvitation = !!localStorage.getItem('invitationCode')
    })
  },

  computed: {
    ...mapState({
      cgUser: state => state.cgAuth.user
    }),
    
    /**
     * 邮箱格式验证
     */
    isEmailValid() {
      // 没有内容或者正在输入时不校验
      if (!this.enableEmailFormatCheck || !this.form.email) {
        return true
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(this.form.email)
    },
    switchEarlyIcon() {
      return switchEarlyIcon
    },
    
    /**
     * 是否显示切换按钮，检查 URL 中是否有 s=0 参数
     */
    shouldShowSwitchButton() {
      return (!this.$route.query.s || this.$route.query.s.toLowerCase().trim() !== '0') && !this.fromInvitation
    }
  },

  watch: {
    'form.email'(newVal) {
      if (!newVal) {
        this.enableEmailFormatCheck = false
      }
    }
  },

  methods: {
     /**
     * 更新是否显示继续按钮
     */
     updateShowContinue() {
      this.showContinue = this.isEmailValid
    },

    /**
     * 下一步邮箱登录
     */
    async handleContinue() {
      if (this.isLoginPage) {
        // 点击继续埋点
        this.$analytics.sendEvent('lg_cg_login_continue')
        this.$analytics.sendEvent('lg_k12_login_startlogin', { 'button_name': 'continue' })
      } else {
        // 点击继续埋点
        this.$analytics.sendEvent('cg_web_login_continue')
        this.$analytics.sendEvent('cg_k12_login_startlogin', { 'button_name': 'continue' })
      }
      // 校验邮箱
      const isValid = await this.$refs.emailForm.validate()
      if (!isValid) {
        return
      }

      this.continueLoading = true
      const { checkEmail } = useAuthApi()
      try {
        // 调用接口检查邮箱是否存在
        const res = await checkEmail(this.form.email)
        // 邮箱是否存在
        let is_exist = res.is_exist
        // 返回检查结果
        this.$emit('emailLogin', this.form.email, is_exist)
      } catch (error) {
        console.error('checkEmail error', error)
      } finally {
        this.continueLoading = false
      }
    },

    async handleGoogleLogin() {
      this.googleLoading = true
      try {
        const googleLogin = this.$refs.googleLogin
        await googleLogin.handleGoogleLogin()
      } catch (error) {
        console.error('Google login error:', error)
      } finally {
        this.googleLoading = false
      }
    },

    onGoogleLoginSuccess(response) {
      // 处理登录成功
      this.$emit('close')
    },

    onGoogleLoginError(error) {
      // 处理登录错误
      console.error('Google login error:', error)
    },

    onLoginSuccess() {
      this.$emit('loginSuccess')
    },

    onEmailBlur() {
      if (this.form.email) {
        this.enableEmailFormatCheck = true
      }
    },

    /**
     * 切换到 Early Learning
     */
    switchToEarlyLearning() {
      // 切换到 Early Learning 埋点
      this.$analytics.sendEvent('lg_k12_login_gettolg')
      // 在顶级域名下，保存标识到 Cookie 中，设置 30 天过期时间
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30);
      document.cookie = `educationStage=EARLY_LEARNING; path=/; domain=.learning-genie.com; expires=${expirationDate.toUTCString()}`;
      // 跳转页面到 Early Learning
      window.location.href = this.lgWebPortalUrl + '/#/login'
    }
  }
}
</script>

<style lang="scss" scoped>
.switch-to-early-learning {
  height: 40px;
  padding: 10px;
  border-radius: 8px;
  border: 2px solid #10b3b7;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
</style> 