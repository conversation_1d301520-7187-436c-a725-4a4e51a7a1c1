<template>
  <div class="flex flex-col items-center justify-center">
    <div class="w-[300px]">
      <div class="items-center w-full">

        <div class="flex justify-center gap-2 items-center mb-[60px]">
          <span class="font-semibold text-2xl text-default">Reset Your Password</span>
        </div>

        <!-- 邮箱重置表单 -->
        <div class="flex flex-col items-center justify-end mb-[100px]">
          <el-input
            v-model="email"
            type="email"
            placeholder="Email Address"
            class="h-12 text-base"
            @input="validateEmail"
          />

          <el-button
            type="primary"
            class="w-full h-12 text-base mt-6"
            :disabled="showHint"
            :loading="sendEmailLoading"
            @click="handleSubmit"
          >
            Submit
          </el-button>

          <div v-if="showHint" class="text-sm mt-1">
            Please use the link sent to your email to reset your password.
          </div>

          <div v-if="!isEmailValid" class="text-red-500 text-sm mt-1">
            Please enter a valid email address.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthApi } from '@/api/cg/auth'

export default {
  name: 'CGForgetPassword',

  props: {
    isLoginPage: {
      type: Boolean,
      default: false
    },
    initialEmail: {
      type: String,
      default: ''
    }
  },

  emits: ['close', 'back', 'updateEmail'],

  data() {
    return {
      email: this.initialEmail,
      isEmailValid: true,
      showHint: false,
      sendEmailLoading: false
    }
  },


  watch: {
    email: {
      handler() {
        this.isEmailValid = true
        this.showHint = false
      }
    }
  },


 watch: {
    email: {
      handler() {
        this.isEmailValid = true
        this.showHint = false
      }
    }
  },

  methods: {
    validateEmail() {
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
      this.isEmailValid = emailPattern.test(this.email.trim()) && this.email.trim().length <= 64
      this.$emit('updateEmail', this.email)
    },

    async handleSubmit() {
      if (!this.isLoginPage) {
        // 点击忘记密码埋点
        this.$analytics.sendEvent('cg_web_login_submit')
      } else {
        // 点击重置密码埋点
        this.$analytics.sendEvent('lg_cg_login_submit')
      }
      // 验证邮箱
      this.validateEmail()
      if (!this.isEmailValid) return

      const authApi = useAuthApi()

      try {
        this.sendEmailLoading = true
        const res = await authApi.sendPasswordResetEmail(this.email)
        // 邮箱是否存在
        const { is_exist } = res
        if (is_exist) {
          this.$message.success('Reset email has been sent to your inbox.')
          this.showHint = true
        } else {
          this.isEmailValid = false
        }
      } catch (error) {
        console.error('Failed to send reset email:', error)
      } finally {
        this.sendEmailLoading = false
      }
    },

    goBack() {
      this.$emit('back')
    }
  }
}
</script>

<style lang="scss" scoped>
/* 忘记密码样式 */
</style> 