<template>
  <div :class="_size">
    <el-tabs v-if="tabs.length > 0" v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane :disabled="item.disabled" v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  name: 'LgTabs',
  props: {
    // 选项卡数据
    tabs: {
      type: Array,
      default () {
        return []
      }
    },
    // 选中的选项卡
    value: {
      type: String,
      default: '0'
    },
    // 选项卡大小
    size: {
      type: String,
      default: 'default'
    }
  },
  data () {
    return {
      activeTab: '0'
    }
  },
  watch: {
    value: {
      handler (vcal) {
        this.activeTab = vcal
      },
      immediate: true
    }
  },
  computed: {
    _size () {
      if (this.size == 'small') {
        return 'lg-tabs-small'
      } else if (this.size == 'mini') {
        return 'lg-tabs-mini'
      } else {
        return 'lg-tabs'
      }
    }
  },
  methods: {
    // 点击选项卡回调
    handleClick (tab) {
      this.$emit('change', tab)
    }
  }
}
</script>
