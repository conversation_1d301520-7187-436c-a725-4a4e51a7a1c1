<template>
  <div class="audio-container">
    <audio ref="audio" :src="src" @timeupdate="updateProgress" @play="play($event)"></audio>
    <div class="audio-controls">
      <i class="audio-play-btn lg-icon" @click="togglePlay" :class="playing ? 'lg-icon-pause' : 'lg-icon-play'"></i>
      <div class="audio-time">{{ currentTime }} / {{ duration }}</div>
      <div class="audio-progress" ref="progress" @mousedown="startDrag" @mousemove="drag" @mouseup="endDrag" @mouseleave="endDrag">
        <div class="audio-progress-bar" :style="{ width: progress + '%' }"></div>
        <div class="audio-progress-dragger" :style="{ left: progress + '%'}"></div>
      </div>
      <div class="audio-volume">
        <input type="range" min="0" max="1" step="0.01" v-model="volume" @change="changeVolume">
        <i class="audio-icon lg-icon" :class="audioIcon" @click="mute"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AudioPlayer',
  props: {
    src: String
  },
  data () {
    return {
      playing: false, // 是否正在播放
      progress: 0, // 进度条
      currentTime: '00:00', // 当前播放时间
      duration: '00:00', // 音频总时长
      dragging: false, // 是否正在拖动进度条
      volume: 0.5 // 音量大小
    }
  },
  computed: {
    // 根据音量大小设置音量图标
    audioIcon () {
      let volume = parseFloat(this.volume)
      if (volume === 0) {
        return 'lg-icon-sound-off'
      } else if (volume > 0 && volume < 0.3) {
        return 'lg-icon-sound-audible'
      } else if (volume >= 0.3 && volume < 0.6) {
        return 'lg-icon-sound-moderate'
      } else {
        return 'lg-icon-sound-loud'
      }
    }
  },
  mounted () {
    // 监听音频加载完成事件，设置时长
    const audio = this.$refs.audio
    audio.addEventListener('loadeddata', () => {
      this.duration = this.formatTime(audio.duration)
    })
  },
  methods: {
    // 播放事件通知父组件
    play (e) {
      this.$emit('play', e)
    },
    // 播放或暂停
    togglePlay () {
      const audio = this.$refs.audio
      if (this.playing) {
        audio.pause()
      } else {
        audio.play()
      }
      this.playing = !this.playing
      audio.addEventListener('ended', () => {
        this.playing = false
        this.currentTime = '00:00'
        this.progress = 0
      })
    },
    // 更新进度条
    updateProgress () {
      const audio = this.$refs.audio
      if (!this.dragging) {
        const progress = (audio.currentTime / audio.duration) * 100
        this.progress = progress
        this.currentTime = this.formatTime(audio.currentTime)
        this.duration = this.formatTime(audio.duration)
      }
    },
    // 格式化时间
    formatTime (time) {
      const minutes = Math.floor(time / 60)
      const seconds = Math.floor(time % 60)
      return `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`
    },
    // 开始拖动进度条
    startDrag (event) {
      this.dragging = true
    },
    // 拖动进度条
    drag (event) {
      if (this.dragging) {
        const progress = this.$refs.progress
        const audio = this.$refs.audio
        const rect = progress.getBoundingClientRect()
        const x = event.clientX - rect.left
        const width = rect.width
        const percent = Math.min(Math.max(x / width, 0), 1)
        this.progress = percent * 100
        const time = percent * audio.duration
        this.currentTime = this.formatTime(time)
      }
    },
    // 进度条拖动结束
    endDrag (event) {
      if (this.dragging) {
        const progress = this.$refs.progress
        const audio = this.$refs.audio
        const rect = progress.getBoundingClientRect()
        const x = event.clientX - rect.left
        const width = rect.width
        const percent = Math.min(Math.max(x / width, 0), 1)
        const time = percent * audio.duration
        audio.currentTime = time
        this.dragging = false
      }
    },
    // 改变音量
    changeVolume (event) {
      const audio = this.$refs.audio
      audio.volume = this.volume
    },
    // 静音或取消静音
    mute () {
      const audio = this.$refs.audio
      if (this.volume === 0) {
        this.volume = 0.5
        audio.volume = this.volume
      } else {
        audio.volume = 0
        this.volume = 0
      }
    }
  }
}
</script>

<style lang="less" scoped>
.audio-container {
  position: relative;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 50px;
  background: #DDF2F3;
  width: 280px;
}

.audio-controls {
  display: flex;
  align-items: center;
  position: relative;
}

.audio-play-btn {
  position: relative;
  background: #10b3b7;
  color: #fff;
  font-size: 24px;
  border-radius: 50%;
  width: 24px;
  height: 24px
}
.audio-play-btn::before {
  position: absolute;
  top: -5px;
}
.audio-progress {
  position: relative;
  width: 90px;
  height: 5px;
  background-color: #ccc;
  margin: 0 10px;
  cursor: pointer;
  border-radius: 5px;
}

.audio-progress:hover {
  .audio-progress-dragger {
    visibility: visible;
  }
}

.audio-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #10b3b7;
  border-radius: 20px;
}
.audio-progress-dragger {
  visibility: hidden;
  position: absolute;
  top: -2.5px;
  left: 0;
  width: 10px;
  height: 10px;
  background-color: #10b3b7;
  border-radius: 50%;
  pointer-events: none;
  margin-left: -5px;
}

.audio-time {
  margin-left: 8px;
  color: #10b3b7;
}

.audio-volume {
  position: absolute;
  height: 30px;
  right: -22px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.audio-volume input[type="range"] {
  display: none;
}

.audio-volume:hover input[type="range"] {
  display: block;
}

.audio-volume:hover {
  background: #fff;
  border-radius: 15px;
  padding: 3px;
}

input[type=range] {
  border-radius: 20px;
  -webkit-appearance: none;
  width: 50px;
  height: 5px;
  background-color: #ccc;
  outline: none;
  margin: 0 10px;
  // transform: rotate(-90deg);
}
input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 10px;
  height: 10px;
  background-color: #10b3b7;
  border-radius: 50%;
  cursor: pointer;
}
</style>
