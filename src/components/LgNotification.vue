<template>
    <!-- 通知 -->
    <div v-if="showNotification" class="notification" :class="`notification-`+position">
        <!-- 内容展示区域 -->
        <div class="content">
            <slot name="content"></slot>
        </div>
        <!-- 进度条区域 -->
        <div class="lg-progress-bar">
            <div v-show="progress>=0" class="lg-progress" :style="{ width: progress + '%', 'border-bottom-right-radius': progress == 100 ? '5px': '', 'transition': timer ? `width ${timer}s ease!important` : '' }"></div>
        </div>
        <!-- 关闭按钮 -->
        <i v-if="showCloseButton" class="close-button el-icon-close"  @click="handleClose"></i>
    </div>
</template>

<script>
export default {
    props: {
        // 显示位置
        position: {
            type: String,
            default: 'top-center'
        },
        // 进度条
        progress: {
            type: Number,
            default: undefined
        },
        // 是否显示关闭按钮
        showCloseButton: {
            type: Boolean,
            default: true
        },
        // 是否自动关闭
        autoClose: {
            type: Boolean,
            default: true
        },
        // 自动关闭时间
        duration: {
            type: Number,
            default: 5000
        },
        timer: {
            type: Number,
            default: 30
        }
    },
    data () {
        return {
            showNotification: false // 是否显示通知
        }
    },
    methods: {
        // 显示通知
        show () {
            this.showNotification = true
            // 自动关闭通知显示
            if (this.autoClose) {
                setTimeout(() => {
                    this.autoCliose()
                }, this.duration)
            }
        },
        // 关闭通知显示
        close () {
            this.showNotification = false
        },
        // 手动关闭
        handleClose () {
            this.showNotification = false
            this.$emit('close')
        }
    }
}
</script>

<style scoped>
.notification {
    position: fixed;
    background-color: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    z-index: 1000;
    width: 540px;
}

.notification-top-center {
    top: 70px;
    left: 50%;
    transform: translate(-50%, -50%);
}

.notification-top-left {
    top: 70px;
}

.notification-top-right {
    top: 70px;
    right: 20px;
}

.notification-bottom-center {
    bottom: 50px;
    left: 50%;
    transform: translate(-50%, -50%);
}

.notification-bottom-left {
    bottom: 50px;
}

.notification-bottom-right {
    bottom: 50px;
    right: 20px;
}

.content {
    padding: 12px 12px 8px;
}

.lg-progress-bar {
    width: 100%;
    height: 5px;
}

.lg-progress {
    height: 100%;
    transition: width 3.0s ease;
    background: linear-gradient(270.59deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%);
    border-bottom-left-radius: 5px;
}

.close-button {
    position: absolute;
    top: 5px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 14px;
}
</style>
