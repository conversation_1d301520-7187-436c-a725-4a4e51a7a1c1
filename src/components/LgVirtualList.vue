<template>
  <virtual-list 
    ref="virtualListRef"
    class="lg-scrollbar lg-virtual-list"
    :style="listStyle"
    :data-key="dataKey"
    :data-sources="dataSources"
    :data-component="dataComponent"
    :estimate-size="estimateSize"
    item-class="lg-virtual-list-item"
    :item-class-add="itemClassAdd"
    :extra-props="extraProps"
    :keeps="calculatedKeeps"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import VirtualList from 'vue-virtual-scroll-list'

export default {
  name: 'LgVirtualList',
  components: {
    VirtualList
  },
  props: {
    // 虚拟列表的基本配置
    dataSources: {
      // 虚拟列表的数据源数组
      type: Array, 
      default: () => []
    },
    dataComponent: {
      // 用于渲染每个列表项的组件
      type: [Object, Function],
      required: true
    },
    dataKey: {
      // 数据项的唯一标识字段名
      type: String,
      default: 'id'
    },
    estimateSize: {
      // 列表项的预估高度(像素)
      type: Number,
      default: 34
    },
    keeps: {
      // 保持渲染的列表项数量
      type: Number,
      default: null
    },
    height: {
      // 虚拟列表容器的高度
      type: [String, Number],
      default: '245px'
    },
    extraProps: {
      // 传递给列表项组件的额外 props
      type: Object,
      default: () => ({})
    },
    // 滚动相关配置
    scrollOffset: {
      // 滚动偏移量，用于调整滚动位置
      type: Number,
      default: 3
    }
  },
  computed: {
    listStyle() {
      // 获取外部设置的高度（以像素为单位）
      const containerHeight = this.getContainerHeight()
      
      // 根据 estimateSize 计算合理的兼容值
      // 当 estimateSize 较大时增加更多兼容空间
      const compatibleSpace = Math.max(this.estimateSize * 0.5, 20)
    
      // 计算所有数据项的总高度（增加动态兼容值）
      const totalItemsHeight = this.dataSources.length * this.estimateSize + compatibleSpace
      
      // 如果总高度和容器高度相差小于 compatibleSpace，则使用容器高度
      const heightDiff = Math.abs(totalItemsHeight - containerHeight)
      const finalHeight = heightDiff < compatibleSpace ? containerHeight : 
                         totalItemsHeight < containerHeight ? totalItemsHeight : containerHeight
      
      // 如果没有数据项，使用最小高度
      const height = this.dataSources.length === 0 ? '0px' : 
                     finalHeight > 0 ? `${finalHeight}px` : 
                     typeof this.height === 'number' ? `${this.height}px` : this.height
      
      return {
        height,
        'overflow-y': 'auto'
      }
    },
    itemClassAdd() {
      return (index) => `virtual-item-${index}`
    },
    // 动态计算 keeps 值
    calculatedKeeps() {
      // 如果外部传入了 keeps，优先使用外部传入的
      if (this.keeps) {
        return this.keeps
      }
      
      // 否则根据容器高度和项目高度动态计算
      // 获取容器高度（以像素为单位）
      const containerHeight = Math.max(this.getContainerHeight(), 0)
      // 计算可见项目数（容器高度 / 项目高度），确保 estimateSize 不为 0
      const estimateSize = Math.max(this.estimateSize || 1, 1)
      const visibleItems = Math.ceil(containerHeight / estimateSize)
      // 根据公式 keeps = visibleItems + buffer，buffer 约为 visibleItems / 3 向上取整
      // 确保 buffer 至少为 1
      const buffer = Math.max(Math.ceil(visibleItems / 3) + 1, 1)
      // 确保返回值至少为 1
      return Math.max(visibleItems + buffer, 1)
    }
  },
  
  methods: {
    /**
     * 获取容器高度（以像素为单位）
     */
    getContainerHeight() {
      let height = this.height
      
      // 如果高度是字符串（如 '245px'），提取数字部分
      if (typeof height === 'string') {
        const match = height.match(/(\d+)/)
        height = match ? parseInt(match[1], 10) : 245
      }
      
      return height || 245
    },

    /**
     * 获取虚拟列表实例
     */
    getVirtualListInstance() {
      return this.$refs.virtualListRef
    },

    /**
     * 获取选中项在数据源中的索引
     * @param {String|Number} itemId - 选中项的ID
     * @returns {Number} 索引值
     */
    getSelectedItemIndex(itemId) {
      if (!itemId || !this.dataSources) return 0
      
      const selectedIndex = this.dataSources.findIndex(item => 
        item[this.dataKey] === itemId
      )
      
      return selectedIndex >= 0 ? selectedIndex : 0
    },

    /**
     * 滚动到选中项
     * @param {String|Number} itemId - 要滚动到的项的ID
     * @param {Object} options - 滚动选项
     */
    scrollToItem(itemId, options = {}) {
      const virtualList = this.getVirtualListInstance()
      if (!virtualList) return

      const selectedIndex = this.getSelectedItemIndex(itemId)
      if (selectedIndex >= 0) {
        this.scrollVirtualList(virtualList, selectedIndex, options)
      }
    },
    
    /**
     * 处理虚拟列表滚动到指定位置
     * @param {Object} virtualList - 虚拟列表实例
     * @param {number} toIndex - 目标索引位置
     * @param {Object} options - 配置选项
     * @param {number} [options.maxAttempts=10] - 最大尝试次数
     * @param {number} [options.checkInterval=50] - 检查间隔时间(ms)
     * @param {number} [options.offset=3] - 滚动偏移量
     * @returns {void}
     */
    scrollVirtualList(virtualList, toIndex, options = {}) {
      const {
        maxAttempts = 10,
        checkInterval = 50,
        offset = this.scrollOffset
      } = options

      let attempts = 0

      const checkAndScroll = () => {
        if (virtualList && virtualList.$el && virtualList.$el.querySelector('[role="listitem"]')) {
          // virtualList.reset()
          virtualList.scrollToIndex(Math.max(0, toIndex - offset))
        } else if (attempts < maxAttempts) {
          attempts++
          setTimeout(checkAndScroll, checkInterval)
        }
      }

      setTimeout(checkAndScroll, checkInterval)
    },

    /**
     * 重置虚拟列表
     */
    reset() {
      const virtualList = this.getVirtualListInstance()
      if (virtualList && virtualList.reset) {
        virtualList.reset()
      }
    },

    /**
     * 滚动到指定索引
     * @param {Number} index - 目标索引
     */
    scrollToIndex(index) {
      const virtualList = this.getVirtualListInstance()
      if (virtualList && virtualList.scrollToIndex) {
        virtualList.scrollToIndex(index)
      }
    },
  }
}
</script>

<style lang="less" scoped>
</style> 