<template>
  <div v-if="showWarning" class="browser-warning-overlay">
    <span>Unsupported browser for Google sign-in.</span>
    <span>👉 Tap the ··· menu and select <span class="lg-color-warning">Open in Browser</span>.</span>
  </div>
</template>

<script>
import tools from '@/utils/tools'

export default {
  name: 'BrowserWarning',
  props: {

  },
  data() {
    return {
      showWarning: false
    }
  },
  mounted () {
    // 检测是否为不支持的浏览器
    this.showWarning = tools.isInUnsupportedWebView()
  }
};
</script>

<style scoped>
.browser-warning-overlay {
  position: fixed;
  bottom: 16px;
  left: 16px;
  right: 16px;
  padding: 10px;
  color: #fff;
  align-items: center;
  border-radius: 8px;
  border: 1px solid #676879;
  background: #3A3F51;
  opacity: 0.7;
  /* 卡片投影 */
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.10);
  display: flex;
  flex-direction: column;
  width: calc(100% - 32px); /* 确保宽度平铺，左右各留 16px 间距 */
}
</style>