/**
 * 课程助手状态管理器类
 * 用于管理课程助手相关的全局状态
 */
export class AssistantToolStateManager {
  constructor() {
    // console.log('【StateManager】初始化状态管理器')
    // 初始化状态对象
    this.state = {
      convertLessonLoading: false, // 课程转换加载状态
      generatedLesson: false, // 课程是否已生成
      lessonStepTwoGenerated: false,// 课程第二步内容是否生成结束
      generateLessonSlidesStreamLoading: false, // slides 流式生成状态
      assistantToolState: null, // 课程助手状态
      frameworkData: [], // 框架数据数组，需填充具体数据
      applyPrompting: false, // 是否正在应用提示中
    }
    // 订阅者集合,用于存储状态变化的回调函数
    this.subscribers = new Set()
  }

  /**
   * 通知所有订阅者状态发生变化
   */
  notifySubscribers() {
    // console.log('【StateManager】通知所有订阅者')
    this.subscribers.forEach(callback => callback(this.state))
  }
}

/**
 * Vue 混入对象,提供状态管理相关的方法和生命周期钩子
 */
export const assistantToolState = {
  inject: ['assistantToolStateManager'],

  data() {
    return {
      localState: { ...this.assistantToolStateManager.state } // 组件本地状态
    }
  },

  created() {
    // console.log(`【${this.$options.name}】组件创建，订阅状态变化`)
    this.assistantToolStateManager.subscribers.add(this.onStateChange)
  },

  beforeDestroy() {
    // console.log(`【${this.$options.name}】组件销毁，取消订阅`)
    this.assistantToolStateManager.subscribers.delete(this.onStateChange)
  },

  methods: {
    /**
     * 状态变化回调函数
     * @param {Object} newState 新的状态对象
     */
    onStateChange(newState) {
      // console.log(`【${this.$options.name}】收到状态更新:`, newState)
      this.localState = { ...newState }
    },

    /**
     * 更新课程生成状态
     * @param {boolean} loading 是否正在生成课程
     */
    updateConvertLessonLoading1(loading) {
      // console.log(`【${this.$options.name}】更新课程生成状态:`, loading)
      this.assistantToolStateManager.state.convertLessonLoading = loading
      this.assistantToolStateManager.notifySubscribers()
    },

    /**
     * 更新课程是否已生成状态
     * @param {boolean} generated 课程是否已生成
     */
    updateGeneratedLesson(generated) {
      // console.log(`【${this.$options.name}】更新课程已生成状态:`, generated)
      this.assistantToolStateManager.state.generatedLesson = generated
      this.assistantToolStateManager.notifySubscribers()
    },

    /**
     * 更新 LessonStepTwoGenerated
     * @param {Boolean} val 第二步状态
     */
    updateLessonStepTwoGeneratedSyncNotify (val) {
      this.assistantToolStateManager.state.lessonStepTwoGenerated = val
      this.assistantToolStateManager.notifySubscribers()
    },

    /**
     * 更新 generateLessonSlidesStreamLoading
     * @param {Boolean} val slides 流式状态同步
     */
    updateGenerateLessonSlidesStreamLoadingNotify (val) {
      this.assistantToolStateManager.state.generateLessonSlidesStreamLoading = val
      this.assistantToolStateManager.notifySubscribers()
    },

    /**
     * 更新课程助手信息
     * @param {Object} state 课程助手状态对象
     */
    updateAssistantToolState(state) {
      // console.log(`【${this.$options.name}】更新课程助手信息:`, state)
      this.assistantToolStateManager.state.assistantToolState = state
      this.assistantToolStateManager.notifySubscribers()
    },

    /**
     * 更新级联数据数组
     * @param {Array} data 级联数据数组
     */
    updateFrameworkData(data) {
      // console.log(`【${this.$options.name}】更新级联数据数组:`, data)
      this.assistantToolStateManager.state.frameworkData = data
      this.assistantToolStateManager.notifySubscribers()
    },

    /**
     * 更新是否正在应用提示状态
     * @param {boolean} applying 是否正在应用提示
     */
    updateApplyPrompting(applying) {
      // console.log(`【${this.$options.name}】更新是否正在应用提示状态:`, applying)
      this.assistantToolStateManager.state.applyPrompting = applying
      this.assistantToolStateManager.notifySubscribers()
    },
  }
} 