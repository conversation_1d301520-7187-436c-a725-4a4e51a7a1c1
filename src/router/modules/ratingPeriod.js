import LayoutAdminInkind from '@/views/layout/LayoutAdminInkind.vue'

const ratingPeriodRouter = {
  path: '/admin/rating_period',
  component: LayoutAdminInkind,
  children: [
    {
      path: '',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/ratingPeriodGroup.vue')
    },
    {
      path: 'guide',
      name: 'guide',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/guide.vue')
    },
    {
      name: 'setting',
      path: 'setting',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/settings.vue')
    },
    {
      path: 'choose_add_class',
      name: 'choose_add_class',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/chooseAddClass.vue')
    },
    {
      path: 'choose_set_period',
      name: 'choose_set_period',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/chooseSetPeriod.vue')
    },
    {
      path: 'step1/:type',
      name: 'step1/:type',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/settingStep1.vue')
    },
    {
      name: 'step2',
      path: 'step2',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/settingStep2.vue')
    },
    {
      path: 'step3',
      name: 'step3',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/settingStep3.vue')
    },
    {
      path: 'step4',
      name: 'step4',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/settingStep4.vue')
    },
    {
      path: 'preview',
      name: 'preview',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/preview.vue')
    },
    {
      path: 'setTransfer/:type',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/setTransferPolicy.vue')
    },
    {
      path: 'periodGroupList',
      name: 'periodGroupList',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/periodGroupList.vue')
    },
    {
      path: 'transition',
      name: 'transition',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/transitionView.vue')
    },
    {
      path: 'class_set_period',
      name: 'class_set_period',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/SetPeriodForNoSetted.vue')
    },
    {
      path: 'class_set_period_sftp',
      name: 'class_set_period_sftp',
      meta: {
        pageName: $i18n.t('loc.navRatingPeriodText'),
        activeMenu: 'ratingPeriodSetup'
      },
      component: () => import(/* webpackChunkName: "ratingPeriod" */ '@/views/ratingPeriod/emptyClassSetPeriod.vue')
    }
  ]
}
export default ratingPeriodRouter
