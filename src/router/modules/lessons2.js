import Layout2 from '../../views/layout/v2/Layout.vue'
import store from '@/store'

const Lesson = () => import(/* webpackChunkName: "curriculum-main" */ '../../views/modules/lesson2')
const LessonEditor = () => import(/* webpackChunkName: "lessons-editor" */ '../../views/modules/lesson2/lessonLibrary/editor')
const LessonRouterView = () => import(/* webpackChunkName: "curriculum-main" */ '../../views/modules/lesson2/LessonRouterView.vue')
// const UnitEditor = () => import( '@/views/modules/lesson2/unitPlanner/UnitEditor.vue')
// const EditPlan = () => import( '@/views/modules/lesson2/lessonPlan/EditPlan.vue')
// const ViewPlan = () => import( '@/views/modules/lesson2/lessonPlan/ViewPlan.vue')
// import { acrossRole } from '@/utils/common'

export default {
  path: '/lessons',
  component: Layout2,
  redirect: '/lessons/lesson-library/my-lessons',
  // meta: {
  //   breadcrumb: $i18n.t('loc.reflectiveLessonPlanning')
  // },
  children: [
    // 默认路由
    // {
    //   path: '/',
    //   name: 'lessons',
    //   component: Lesson
    // },
    {
      path: 'lesson-library',
      component: LessonRouterView,
      redirect: to => {
        const open = store.state.common.open
        const isAgencyUser = open && !open.educator
        return '/lessons/lesson-library/my-lessons'
      },
      meta: {
        // K 12 面包屑更新
        breadcrumb: 'Lesson Plan'
      },
      children: [
        // 公共课程列表
        // {
        //   path: 'public-lessons',
        //   name: 'PublicLessonList',
        //   component: Lesson,
        //   props: {
        //     submoduleProps: {
        //       submoduleName: 'PublicLessonList'
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonLibraryTabName1'),
        //     breadcrumb: $i18n.t('loc.lessonLibraryTabName1'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 机构课程列表
        // {
        //   path: 'agency-wide-lessons',
        //   name: 'AgencyLessonList',
        //   component: Lesson,
        //   props: {
        //     submoduleProps: {
        //       submoduleName: 'AgencyLessonList'
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonLibraryTabName2'),
        //     breadcrumb: $i18n.t('loc.lessonLibraryTabName2'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 教师课程列表
        {
          path: 'my-lessons',
          name: 'TeacherLessonList',
          component: Lesson,
          props: {
            submoduleProps: {
              submoduleName: 'TeacherLessonList',
              activeMenu: 'PublicLessonList'
            }
          },
          meta: {
            pageName: $i18n.t('loc.lessonLibraryTabName3'),
            breadcrumb: $i18n.t('loc.lessonLibraryTabName3'),
            activeMenu: 'PublicLessonList'
          }
        },
        // 管理端教师课程列表
        // {
        //   path: 'lessons-management',
        //   name: 'ManagedTeacherLessonList',
        //   component: Lesson,
        //   props: {
        //     submoduleProps: {
        //       submoduleName: 'ManagedTeacherLessonList'
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonLibraryTabName4'),
        //     breadcrumb: $i18n.t('loc.lessonLibraryTabName4'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 管理端机构课程列表
        // {
        //   path: 'agency-wide-lessons',
        //   name: 'ManageAgency',
        //   component: Lesson,
        //   props: {
        //     submoduleProps: {
        //       submoduleName: 'AgencyLessonList',
        //       submoduleProps: {
        //         submoduleName: 'ManageAgency'
        //       }
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonLibraryTabName2'),
        //     breadcrumb: $i18n.t('loc.lessonLibraryTabName2'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 管理端课程草稿列表
        // {
        //   path: 'agency-wide-lessons/draft',
        //   name: 'ManageDraft',
        //   component: Lesson,
        //   props: {
        //     submoduleProps: {
        //       submoduleName: 'AgencyLessonList',
        //       submoduleProps: {
        //         submoduleName: 'ManageDraft'
        //       }
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonLibraryTabName2'),
        //     breadcrumb: $i18n.t('loc.lessonLibraryTabName2'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 教师创建的课程草稿列表
        {
          path: 'my-lessons/draft',
          name: 'Draft',
          component: Lesson,
          props: {
            submoduleProps: {
              submoduleProps: {
                submoduleName: 'Draft'
              },
              submoduleName: 'TeacherLessonList'
            }
          },
          meta: {
            pageName: $i18n.t('loc.lessonLibraryTabName3'),
            breadcrumb: $i18n.t('loc.lessonLibraryTabName3'),
            activeMenu: 'PublicLessonList'
          }
        },
        // 教师创建的课程收藏课程列表
        // {
        //   path: 'my-lessons/my-favorites',
        //   name: 'Favorite',
        //   component: Lesson,
        //   props: {
        //     submoduleProps: {
        //       submoduleProps: {
        //         submoduleName: 'Favorite'
        //       },
        //       submoduleName: 'TeacherLessonList'
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonLibraryTabName3'),
        //     breadcrumb: $i18n.t('loc.lessonLibraryTabName3'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 教师创建的课程创建课程列表
        {
          path: 'my-lessons/my-creation',
          name: 'Create',
          component: Lesson,
          props: {
            submoduleProps: {
              submoduleProps: {
                submoduleName: 'Create'
              },
              submoduleName: 'TeacherLessonList'
            }
          },
          meta: {
            pageName: $i18n.t('loc.lessonLibraryTabName3'),
            breadcrumb: $i18n.t('loc.lessonLibraryTabName3'),
            activeMenu: 'PublicLessonList'
          }
        },
        // 教师创建的课程列表
        {
          path: 'my-lessons/my-creation',
          name: 'TeacherCreateLesson',
          component: Lesson,
          props: {
            submoduleName: 'LessonLibrary',
            submoduleProps: {
              submoduleName: 'TeacherLessonList',
              submoduleProps: {
                submoduleName: 'Create'
              }
            }
          },
          meta: {
            pageName: $i18n.t('loc.lessonLibraryTabName3'),
            breadcrumb: $i18n.t('loc.lessonLibraryTabName3'),
            activeMenu: 'PublicLessonList'
          }
        },
        // 教师创建的课程草稿列表
        {
          path: 'my-lesson/draft',
          name: 'TeacherDraftLesson',
          component: Lesson,
          props: {
            submoduleName: 'LessonLibrary',
            submoduleProps: {
              submoduleName: 'TeacherLessonList',
              submoduleProps: {
                submoduleName: 'Draft'
              }
            }
          },
          meta: {
            pageName: $i18n.t('loc.lessonLibraryTabName3'),
            breadcrumb: $i18n.t('loc.lessonLibraryTabName3'),
            activeMenu: 'PublicLessonList'
          }
        }
      ]
    },
    {
      path: 'lesson-library',
      component: LessonRouterView,
      meta: {
        breadcrumb: $i18n.t('loc.lessonPlan')
      },
      children: [
        // 公共课程详情
        // {
        //   path: 'public-lessons/:lessonId',
        //   name: 'PublicLessonDetail',
        //   component: () => import(/* webpackChunkName: "lessons" */ '../../views/modules/lesson2/lessonLibrary/publicLesson/detail'),
        //   props: router => {
        //     return {
        //       lessonId: router.params.lessonId
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonDetail'),
        //     breadcrumb: $i18n.t('loc.lessonDetail'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 管理端教师课程详情
        // {
        //   path: 'lessons-management/:lessonId',
        //   name: 'ManageTeacherLessonDetail',
        //   component: () => import(/* webpackChunkName: "lessons" */ '../../views/modules/lesson2/lessonLibrary/teacherManager/detail'),
        //   props: router => {
        //     return {
        //       lessonId: router.params.lessonId
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonDetail'),
        //     breadcrumb: $i18n.t('loc.lessonDetail'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 机构课程详情
        // {
        //   path: 'agency-wide-lessons/:lessonId',
        //   name: 'AgencyLessonDetail',
        //   component: () => import(/* webpackChunkName: "lessons" */ '../../views/modules/lesson2/lessonLibrary/agencyLesson/detail'),
        //   props: router => {
        //     return {
        //       lessonId: router.params.lessonId
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonDetail'),
        //     breadcrumb: $i18n.t('loc.lessonDetail'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // 老师创建的课程详情
        {
          path: 'my-lessons/:lessonId',
          name: 'MyLessonDetail',
          component: () => import(/* webpackChunkName: "lessons" */ '../../views/modules/lesson2/lessonLibrary/teacherLesson/detail'),
          props: router => {
            return {
              lessonId: router.params.lessonId
            }
          },
          meta: {
            pageName: $i18n.t('loc.lessonLibraryTabName3'),
            breadcrumb: $i18n.t('loc.lessonLibraryTabName3'),
            activeMenu: 'PublicLessonList'
          }
        },
        // 老师收藏的课程详情
        // {
        //   path: 'my-lessons/favorite-lesson/:lessonId',
        //   name: 'FavoriteLessonDetail',
        //   component: () => import(/* webpackChunkName: "lessons" */ '../../views/modules/lesson2/lessonLibrary/teacherLesson/favoriteDetail'),
        //   props: router => {
        //     return {
        //       lessonId: router.params.lessonId
        //     }
        //   },
        //   meta: {
        //     pageName: $i18n.t('loc.lessonDetail'),
        //     breadcrumb: $i18n.t('loc.lessonDetail'),
        //     activeMenu: 'PublicLessonList'
        //   }
        // },
        // lesson editor
        {
          path: 'edit-lesson/:lessonId?',
          name: 'EditLesson',
          component: LessonEditor,
          props: route => {
            return { ...route.params, showAssistantTool: true}
          },
          meta: {
            pageName: $i18n.t('loc.lessons2EditLessonTitle'),
            breadcrumb: $i18n.t('loc.lessons2EditLessonTitle'),
            activeMenu: 'PublicLessonList'
          }
        },
        // lesson editor
        {
          path: 'replicate/:lessonId?',
          name: 'Replicate',
          component: LessonEditor,
          props: route => {
            return { ...route.params, showAssistantTool: true}
          },
          meta: {
            pageName: $i18n.t('loc.lesson2NewLessonTitle'),
            breadcrumb: $i18n.t('loc.lesson2NewLessonTitle'),
            activeMenu: 'PublicLessonList'
          }
        },
        // lesson add
        {
          path: 'add-lesson/:lessonId?',
          name: 'AddLesson',
          component: LessonEditor,
          props: route => {
            return {
              ...route.params,
              showAssistantTool: true
            }
          },
          meta: {
            pageName: $i18n.t('loc.createLessonBtn'),
            breadcrumb: $i18n.t('loc.createLessonBtn'),
            activeMenu: 'PublicLessonList'
          }
        }
      ]
    },
    // curriculum
    // {
    //   path: 'lesson-curriculum',
    //   name: 'lessonCurriculumView',
    //   meta: {
    //     // hideBreadCrumb: true
    //     pageName: $i18n.t('loc.curriculum'),
    //     activeMenu: 'lessonCurriculum',
    //     breadcrumb: $i18n.t('loc.curriculum')
    //   },
    //   redirect: '/lessons/lesson-curriculum/curriculum-list',
    //   component: Lesson2Layout,
    //   children: [
    //     {
    //       path: 'curriculum-list',
    //       name: 'lessonCurriculum',
    //       meta: {
    //         hideBreadCrumb: true,
    //         pageName: $i18n.t('loc.curriculum'),
    //         activeMenu: 'lessonCurriculum'
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/index.vue')
    //     },
    //     {
    //       path: 'edit/:curriculumId',
    //       name: 'curriculumEdit',
    //       meta: {
    //         pageName: $i18n.t('loc.curriculum101'),
    //         activeMenu: 'lessonCurriculum',
    //         breadcrumb: $i18n.t('loc.curriculum101')
    //       },
    //       beforeEnter: (to,from,next) => {
    //         if (to.params.add) {
    //           to.meta.pageName = $i18n.t('loc.curriculum65')
    //           to.meta.breadcrumb = $i18n.t('loc.curriculum65')
    //         } else {
    //           to.meta.pageName = $i18n.t('loc.curriculum101')
    //           to.meta.breadcrumb = $i18n.t('loc.curriculum101')
    //         }
    //         next()
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/CurriculumEditor/index.vue')
    //     },
    //     {
    //       path: 'curriculum/:curriculumId/unit/:unitId/:frameworkId?/:weekNum?',
    //       name: 'curriculumUnitDetail',
    //       meta: {
    //         pageName: $i18n.t('loc.curriculum91'),
    //         activeMenu: 'lessonCurriculum',
    //         breadcrumb: $i18n.t('loc.curriculum91')
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/CurriculumUnitDetail/index.vue'),
    //       beforeEnter (to, from, next) {
    //         const curriculumName = to.params.curriculumName;
    //         if (curriculumName) {
    //           to.meta.pageName = curriculumName;
    //           to.meta.breadcrumb = curriculumName;
    //         }
    //         next();
    //       }
    //     },
    //     {
    //       path: 'curriculum-books/:id',
    //       name: 'curriculumBooks',
    //       meta: {
    //         pageName: $i18n.t('loc.curriculum14'),
    //         activeMenu: 'lessonCurriculum',
    //         breadcrumb: $i18n.t('loc.curriculum14')
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/CurriculumResource/Books/index.vue')
    //     },
    //     {
    //       path: 'curriculum-vocabularies/:id',
    //       name: 'curriculumVocabularies',
    //       meta: {
    //         pageName: $i18n.t('loc.curriculum15'),
    //         activeMenu: 'lessonCurriculum',
    //         breadcrumb: $i18n.t('loc.curriculum15')
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/CurriculumResource/Vocabularies/index.vue')
    //     },
    //     {
    //       path: 'curriculum-printables/:id',
    //       name: 'curriculumPrintables',
    //       meta: {
    //         pageName: $i18n.t('loc.curriculum16'),
    //         activeMenu: 'lessonCurriculum',
    //         breadcrumb: $i18n.t('loc.curriculum16')
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/CurriculumResource/Printables/index.vue')
    //     },
    //     {
    //       path: 'curriculum-activities/:id',
    //       name: 'curriculumActivities',
    //       meta: {
    //         pageName: $i18n.t('loc.curriculum17'),
    //         activeMenu: 'lessonCurriculum',
    //         breadcrumb: $i18n.t('loc.curriculum17')
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/CurriculumResource/Activities/index.vue')
    //     },
    //     {
    //       path: 'curriculum-measures/:id',
    //       name: 'curriculumMeasures',
    //       meta: {
    //         pageName: $i18n.t('loc.curriculum18'),
    //         activeMenu: 'lessonCurriculum',
    //         breadcrumb: $i18n.t('loc.curriculum18')
    //       },
    //       component: () => import(/* webpackChunkName: "lesson-curriculum" */ '@/views/modules/lesson2/lessonCurriculum/CurriculumResource/Measures/index.vue')
    //     }
    //   ]
    // },
    // // DataReview
    // {
    //   path: 'child-development-insights',
    //   component: Lesson2Layout,
    //   meta: {
    //     breadcrumb: $i18n.t('loc.lesson2TabName2')
    //   },
    //   children: [
    //     {
    //       path: '',
    //       redirect: '/lessons/child-development-insights/class-overview',
    //       component: () => import(/* webpackChunkName: "lessons" */ '@/views/modules/lesson2/dataReview/index.vue'),
    //       children: [
    //         {
    //           path: 'class-overview',
    //           name: 'classOverview',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName2'),
    //             activeMenu: 'classOverview',
    //             breadcrumb: $i18n.t('loc.classOverView')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons" */ '@/views/modules/lesson2/dataReview/NewClassOverview.vue')
    //         },
    //         {
    //           path: 'child-strength-growth-view',
    //           name: 'childOverview',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName2'),
    //             activeMenu: 'classOverview',
    //             breadcrumb: $i18n.t('loc.childStrengthGrowthView')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons" */ '@/views/modules/lesson2/dataReview/ChildView.vue')
    //         },
    //         {
    //           path: 'interest-view',
    //           name: 'interestView',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName2'),
    //             activeMenu: 'classOverview',
    //             breadcrumb: $i18n.t('loc.interestView')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons" */ '@/views/modules/lesson2/dataReview/Interest.vue')
    //         },
    //         {
    //           path: 'assessment-cohort-view',
    //           name: 'assessmentCompare',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName2'),
    //             activeMenu: 'classOverview',
    //             breadcrumb: $i18n.t('loc.assessmentCohortView')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons" */ '@/views/modules/lesson2/dataReview/assessmentCompare.vue')
    //         },
    //         {
    //           path: 'observation-view',
    //           name: 'onservationStatus',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName2'),
    //             activeMenu: 'classOverview',
    //             breadcrumb: $i18n.t('loc.observationStatus')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons" */ '@/views/modules/lesson2/dataReview/observationStatus.vue')
    //         }
    //       ]
    //     }
    //   ]
    // },
    {
      path: 'unit-planner',
      name: 'unitPlanner',
      component: LessonRouterView,
      meta: {
        activeMenu: 'unitPlanner',
        breadcrumb: 'Unit Planner'
      },
      redirect: '/curriculum-genie/unit-planner',
      children: [
        // {
        //     path: '',
        //     name: 'unitPlanner',
        //     meta: {
        //       activeMenu: 'unitPlanner',
        //       pageName: 'Unit Planner',
        //       hideBreadCrumb: true
        //     },
        //     component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/UnitLayout.vue')
        // },
        // {
        //   path: 'unit',
        //   name: 'unit',
        //   component: UnitEditor,
        //   children: [
        //     {
        //       path: '',
        //       name: 'unitCreator',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitOverviewStep.vue')
        //     },
        //     {
        //       path: 'weekly-plan-overview',
        //       name: 'weekly-plan-overview',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/WeeklyPlanOverviewStep.vue')
        //     }
        //   ]
        // },
        // {
        //   path: 'unit-editor/:unitId',
        //   name: 'unit-editor',
        //   component: UnitEditor,
        //   children: [
        //     {
        //       path: 'load-unit',
        //       name: 'loadUnit',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/LoadUnit.vue')
        //     },
        //     {
        //       path: 'unit-overview',
        //       name: 'unitOverview',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitOverviewStep.vue')
        //     },
        //     {
        //       path: 'weekly-plan-overview',
        //       name: 'weeklyPlanOverview',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/WeeklyPlanOverviewStep.vue')
        //     },
        //     {
        //       path: 'lesson-overview/:week',
        //       name: 'lessonOverview',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/LessonOverviewStep.vue')
        //     },
        //     {
        //       path: 'lesson-detail/:itemId?',
        //       name: 'lessonDetail',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitReview.vue')
        //     },
        //     {
        //       path: 'center-lesson-detail/:itemId?',
        //       name: 'centerLessonDetail',
        //       meta: {
        //         activeMenu: 'unitPlanner',
        //         pageName: 'Create Unit Planner',
        //         breadcrumb: 'Create Unit Planner'
        //       },
        //       component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitReview.vue')
        //     }
        //   ]
        // },
        // {
        //   path: 'unit-detail',
        //   name: 'unitDetail',
        //   meta: {
        //     activeMenu: 'unitPlanner',
        //     pageName: 'Unit Planner Details',
        //     breadcrumb: 'Unit Planner Details'
        //   },
        //   component: () => import(/* webpackChunkName: "unit-planner" */ '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetail.vue')
        // }
      ]
    },
    // lesson Plan
    // {
    //   path: 'weekly-lesson-planning',
    //   component: Lesson2Layout,
    //   meta: {
    //     activeMenu: 'list-plan',
    //     breadcrumb: $i18n.t('loc.lesson2TabName3')
    //   },
    //   children: [
    //     {
    //       path: '',
    //       redirect: '/lessons/weekly-lesson-planning/planner-list',
    //       name: 'plan',
    //       component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/WeeklyPlan.vue'),
    //       children: [
    //         {
    //           path: 'planner-list',
    //           name: 'list-plan',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName3'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: (store.getters.currentUser || {}).role2 === 'COLLABORATOR' ? $i18n.t('loc.weeklyPlannerList') : $i18n.t('loc.weeklyPlannersManagement')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/PlanList.vue')
    //         },
    //         {
    //           // path: 'assignedPlan/:adminId',
    //           path: 'create-virtual-shadow-list',
    //           name: 'assigned-plan',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName3'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.createVirtualShadowList')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/AssignedPlanList.vue')
    //         },
    //         {
    //           path: 'template-list',
    //           name: 'template-list',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName3'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.curriculum49')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/TemplatePlanTableList')
    //         },
    //         {
    //           path: 'shadowed-list',
    //           name: 'teacherShadow',
    //           meta: {
    //             pageName: $i18n.t('loc.virtualShadowingDetails'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.virtualShadowingDetails')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/TeacherShadowedList.vue')
    //         }
    //       ]
    //     },
    //     {
    //       path: 'template-list',
    //       name: 'example-plan',
    //       meta: {
    //         pageName: $i18n.t('loc.lesson2TabName3'),
    //         activeMenu: 'list-plan',
    //         breadcrumb: $i18n.t('loc.curriculum49')
    //       },
    //       component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/Layout.vue'),
    //       children: [
    //         {
    //           path: 'edit-template/:planId',
    //           name: 'edit-template',
    //           meta: {
    //             pageName: $i18n.t('loc.plan153'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.plan153')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/EditTemplate.vue')
    //         },
    //         {
    //           path: 'view-template/:planId',
    //           name: 'view-template',
    //           meta: {
    //             pageName: $i18n.t('loc.weeklyPlannerExemplarDetails'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.weeklyPlannerExemplarDetails')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/ViewTemplate.vue')
    //         }
    //       ]
    //     },
    //     {
    //       path: 'planner-list',
    //       name: 'normal-plan',
    //       meta: {
    //         pageName: $i18n.t('loc.lesson2TabName3'),
    //         activeMenu: 'list-plan',
    //         breadcrumb: (store.getters.currentUser || {}).role2 === 'COLLABORATOR' ? 'Weekly Planners List' : $i18n.t('loc.weeklyPlannersManagement')
    //       },
    //       component: Lesson2Layout,
    //       children: [
    //         {
    //           path: 'edit/:planId',
    //           name: 'edit-plan',
    //           meta: {
    //             pageName: $i18n.t('loc.plan2'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.plan2')
    //           },
    //           component: EditPlan
    //         },
    //         {
    //           path: 'view/:planId',
    //           name: 'view-plan',
    //           meta: {
    //             pageName: $i18n.t('loc.weeklyPlannerDetails'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.weeklyPlannerDetails')
    //           },
    //           component: ViewPlan
    //         },
    //         {
    //           path: 'edit-template/:planId',
    //           name: 'edit-agency-template',
    //           meta: {
    //             pageName: $i18n.t('loc.plan109'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.plan109')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/EditTemplate.vue')
    //         },
    //         {
    //           path: 'view',
    //           name: 'lesson-plan-reflection',
    //           meta: {
    //             pageName: $i18n.t('loc.weeklyPlannerDetails'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.weeklyPlannerDetails')
    //           },
    //           children: [
    //             {
    //               path: 'reflection/:lessonId/:planId/:objectId',
    //               name: 'lesson-reflection',
    //               meta: {
    //                 pageName: $i18n.t('loc.plan154'),
    //                 activeMenu: 'list-plan',
    //                 breadcrumb: $i18n.t('loc.plan154')
    //               },
    //               component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/LessonReflection.vue')
    //             }

    //           ],
    //           component: LessonRouterView
    //         }
    //       ]
    //     },
    //     {
    //       path: 'create-virtual-shadow-list',
    //       name: 'shadow-list',
    //       meta: {
    //         pageName: $i18n.t('loc.lesson2TabName3'),
    //         activeMenu: 'list-plan',
    //         breadcrumb: $i18n.t('loc.createVirtualShadowList')
    //       },
    //       component: Lesson2Layout,
    //       children: [
    //         {
    //           path: 'shared-plan/:shareUserId',
    //           name: 'shared-plan',
    //           meta: {
    //             pageName: $i18n.t('loc.lesson2TabName3'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: 'Weekly Planner'
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/TeacherSharedTable.vue')
    //         },
    //         {
    //           path: 'view/:planId',
    //           name: 'shared-plan-detail',
    //           meta: {
    //             pageName: $i18n.t('loc.virtualShadowingDetails'),
    //             activeMenu: 'list-plan',
    //             breadcrumb: $i18n.t('loc.virtualShadowingDetails')
    //           },
    //           component: () => import(/* webpackChunkName: "lessons-plan" */ '@/views/modules/lesson2/lessonPlan/ViewPlan.vue')
    //         }
    //       ]
    //     }
    //   ]
    // },
    // {
    //   path: 'manage-children',
    //   name: 'manageChildren',
    //   meta: {
    //     pageName: 'Manage Classroom Demographics',
    //     hideBreadCrumb: true
    //   },
    //   component: () => import(/* webpackChunkName: "lessons" */ '@/views/modules/lesson2/lessonPlan/ManageChildren.vue')
    // },
    // 单元课程的课程详情页
    {
      path: 'lesson-detail/:lessonId',
      name: 'unitPlanLessonDetail',
      component: () => import(/* webpackChunkName: "lessons" */ '../../views/modules/lesson2/lessonLibrary/publicLesson/detail'),
      props: router => {
        return {
          lessonId: router.params.lessonId
        }
      },
      meta: {
        pageName: $i18n.t('loc.lessonDetail'),
        hideBreadCrumb: true
      }
    },
    // Lesson Standard
    // {
    //   path: 'curriculum-Foundation-Standards',
    //   name: 'Lesson-Standard',
    //   component: Lesson,
    //   meta: {
    //     hideBreadCrumb: true,
    //     pageName: $i18n.t('loc.lesson2TabName4'),
    //     activeMenu: 'Lesson-Standard',
    //     breadcrumb: $i18n.t('loc.lesson2TabName4')
    //   },
    //   props: {
    //     submoduleName: 'LessonStandard'
    //   }
    // }
  ]
}
