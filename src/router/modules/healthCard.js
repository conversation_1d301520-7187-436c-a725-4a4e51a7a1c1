const healthCardRouter = {
  path: 'health_card',
  name: 'health_card',
  redirect:'/admin/health_card/card-list',
  component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/HealthCard"),
  children: [
    {
      path: 'creat-card/:guide',
      name: 'creat-card',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/CreatHealthCard")
    },
    {
      path: 'card-list',
      name: 'card-list',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/HealthCardList")
    },
    {
      path: 'template-info/:guide',
      name: 'template-info',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/TemplateInfo")
    },
    {
      path: 'setting-success/:type',
      name: 'setting-success',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/SettingSuccess")
    },
    {
      path: 'edit-card/:formId',
      name: 'edit-card',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/EditHealthCard")
    },
    {
      path: 'setting-card/:formId',
      name: 'setting-card',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/SettingHealthCard")
    },
    {
      path: 'view-card/:formId',
      name: 'view-card',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/ViewHealthCard")
    },
    {
      path: 'experimental-unit/:guide',
      name: 'experimental-unit',
      meta: {
        pageName: $i18n.t('loc.mgAS'),
        activeMenu: 'dailyHealthCard'
      },
      component: () => import(/* webpackChunkName: "healthCard" */ "@/views/admin/healthCard/ExperimentalUnit")
    },
  ]
}
export default healthCardRouter