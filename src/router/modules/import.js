// 导入模块路由
const importRouter = {
  path: 'import',
  name: 'import',
  meta: {
    breadcrumb: $i18n.t('loc.importTitle')
  },
  redirect: '/admin/import/mhs-setting',
  component: () => import(/* webpackChunkName: "importMhs" */ '@/views/admin/Import'),
  children: [
    // myHeadStart 设置页面
    {
      path: 'mhs-setting',
      name: 'mhs-setting',
      component: () => import(/* webpackChunkName: "importMhs" */ '@/views/admin/import/mhs/Setting'),
      meta: {
        titleKey: $i18n.t('loc.mhsTitle'),
        subTitleKey: $i18n.t('loc.mhsSubTitle'),
        pageName: $i18n.t('loc.mhsTitle'),
        breadcrumb: $i18n.t('loc.mhsTitle'),
        activeMenu: 'importSync'
      }
    },
    // clever 设置页面
    {
      path: 'clever-setting',
      name: 'clever-setting',
      component: () => import('@/views/admin/import/clever/Setting'),
      meta: {
        titleKey: $i18n.t('loc.cleverTitle'),
        subTitleKey: $i18n.t('loc.cleverSubTitle'),
        pageName: $i18n.t('loc.cleverTitle'),
        breadcrumb: $i18n.t('loc.cleverTitle'),
        activeMenu: 'importSync'
      }
    }
  ]
}
export default importRouter
