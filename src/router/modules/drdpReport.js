import Layout from '@/views/layout/v2/Layout.vue'
const drdpReportRouter = {
	path: '/assessment-report',
	name: 'assessmentReport',
	component: Layout,
	meta: {
		pageName: $i18n.t('loc.dataHub'),
		breadcrumb: $i18n.t('loc.dataHub'),
	},
	children: [
		{
			path: 'drdp-report',
			name: 'drdpReport',
			meta: {
				pageName: 'DRDP Report',
				breadcrumb: $i18n.t('loc.drdpReport')
			},
			component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ReportMenu.vue'),
			children: [
				{
					path: 'group-progress-report',
					name: 'GroupProgressReport',
					meta: {
						pageName: 'Group Progress Report',
						breadcrumb: $i18n.t('loc.groupReport'),
						activeMenu: 'GroupProgressReport'
					},
					// component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/GroupReport')
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/GroupReport')
					}
				},
				{
					path: 'child-progress-report',
					name: 'ChildProgressReport',
					meta: {
						pageName: 'Child Progress Report',
						breadcrumb: $i18n.t('loc.childPR'),
						activeMenu: 'ChildProgressReport'
					},
					// component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ChildProgressReport')
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ChildProgressReport')
					}
				},
				{
					path: 'parent-progress-report',
					name: 'ParentProgressReport',
					meta: {
						pageName: 'Parent Progress Report',
						breadcrumb: $i18n.t('loc.parentPR'),
						activeMenu: 'ParentProgressReport'
					},
					// component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ParentProgressReport')
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ParentProgressReport')
					}
				},
				{
					path: 'cohort-progress-report',
					name: 'CohortProgressReport',
					meta: {
						pageName: $i18n.t('loc.cohortProgressReport'),
						breadcrumb: $i18n.t('loc.cohortProgressReport'),
						activeMenu: 'CohortProgressReport'
					},
					// component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/CohortPeogressReport')
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/CohortProgressReport')
					}
				},
				{
					path: 'school-readiness-report',
					name: 'SchoolReadinessReport',
					meta: {
						pageName: $i18n.t('loc.readinessReport'),
						breadcrumb: $i18n.t('loc.readinessReport'),
						activeMenu: 'SchoolReadinessReport'
					},
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/SchoolReadinessReport')
					}
				},
				{
					path: 'class-planning-report',
					name: 'ClassPlanningReport',
					meta: {
						pageName: 'Class Planning Report',
						breadcrumb: $i18n.t('loc.classPlanReport'),
						activeMenu: 'ClassPlanningReport'
					},
					// component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ClassPlanningReport')
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ClassPlanningReport')
					}
				},
				{
					path: 'group-detail-report',
					name: 'GroupDetailReport',
					meta: {
						pageName: 'Group Detail Report',
						breadcrumb: $i18n.t('loc.groupDetailReport'),
						activeMenu: 'GroupDetailReport'
					},
					// component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/GroupDetailReport')
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/GroupDetailReport')
					}
				},
				{
					path: 'RatingCompletion',
					name: 'DevelopingPage',
					meta: {
						pageName: 'Rating Completion',
						breadcrumb: $i18n.t('loc.ratingCompletion')
					},
					components: {
						developing: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/components/PageDevelopment')
					}
				},
				{
					path: 'KsReadinessGoalsAnalysis',
					name: 'DevelopingPage',
					meta: {
						pageName: 'Ks Readiness Goals Analysis',
						breadcrumb: 'Ks Readiness Goals Analysis'
					},
					components: {
						developing: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/components/PageDevelopment')
					}
				}
			]
		},
		{
			path: 'state-report',
			name: 'stateReport',
			meta: {
				pageName: $i18n.t('loc.otherReport'),
				breadcrumb: $i18n.t('loc.otherReport')
			},
			component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ReportMenu.vue'),
			children: [
				{
					path: 'parent-progress-report',
					name: 'stateParentProgressReport',
					meta: {
						pageName: 'Parent Progress Report',
						breadcrumb: $i18n.t('loc.parentPR'),
						activeMenu: 'stateParentProgressReport'
					},
					// component: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ParentProgressReport')
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ParentProgressReport')
					}
				},
				{
					path: 'class-planning-report',
					name: 'stateClassPlanningReport',
					meta: {
						pageName: 'Class Planning Report',
						breadcrumb: $i18n.t('loc.classPlanReport'),
						activeMenu: 'stateClassPlanningReport'
					},
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/ClassPlanningReport')
					}
				},
				{
					path: 'group-detail-report',
					name: 'stateGroupDetailReport',
					meta: {
						pageName: 'Group Detail Report',
						breadcrumb: $i18n.t('loc.groupDetailReport'),
						activeMenu: 'stateGroupDetailReport'
					},
					components: {
						defult: () => import(/* webpackChunkName: "drdpReport" */ '@/views/datahub/GroupDetailReport')
					}
				}
			]
		}
	]
}
export default drdpReportRouter
