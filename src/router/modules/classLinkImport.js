// 导入模块路由
const importClassLinkRouter = {
    path: 'import',
    name: 'import',
    meta: {
      breadcrumb: $i18n.t('loc.importTitle')
    },
    redirect: '/admin/import/cl-setting',
    component: () => import(/* webpackChunkName: "classLinkImport" */ '@/views/admin/Import'),
    children: [
      // myHeadStart 设置页面
      {
        path: 'cl-setting',
        name: 'cl-setting',
        component: () => import(/* webpackChunkName: "classLinkImport" */ '@/views/admin/import/cl/Setting'),
        meta: {
          titleKey: $i18n.t('loc.clTitle'),
          subTitleKey: $i18n.t('loc.clSubTitle'),
          pageName: $i18n.t('loc.clTitle'),
          breadcrumb: $i18n.t('loc.clTitle'),
          activeMenu: 'importSync'
        }
      }
    ]
  }
  export default importClassLinkRouter
