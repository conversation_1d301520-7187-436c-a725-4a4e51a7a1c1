import Layout from '../../views/layout/v2/Layout.vue'
import SchoolMessage from '../../views/modules/message'

export default {
  path: '/messages/school-message',
  component: Layout,
  redirect: '/messages/school-message/send-message',
  meta: {
    v2: true,
    breadcrumb: $i18n.t('loc.messages'),
    hideBreadCrumb: false
  },
  children: [
    // 发送 school message
    {
      path: 'send-message',
      name: 'SendSchoolMessage',
      component: SchoolMessage,
      props: {
        submoduleName: 'SchoolMessage'
      },
      meta: {
        v2: true,
        pageName: $i18n.t('loc.schoolMessage'),
        breadcrumb: $i18n.t('loc.schoolMessage'),
        hideBreadCrumb: false,
        activeMenu: 'SendSchoolMessage'
      }
    },
    // school message 历史记录
    {
      path: 'history',
      name: 'SchoolMessageHistory',
      component: SchoolMessage,
      props: {
        submoduleName: 'MessageHistory'
      },
      meta: {
        v2: true,
        pageName: $i18n.t('loc.schoolMessage'),
        breadcrumb: $i18n.t('loc.schoolMessage'),
        hideBreadCrumb: false,
        activeMenu: 'SendSchoolMessage'
      }
    }
  ]
}
