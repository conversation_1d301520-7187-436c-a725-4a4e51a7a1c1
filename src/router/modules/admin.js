import LayoutAdmin from '@/views/layout/LayoutAdmin.vue'
import inkindRouter from './inkind'
import ratingPeriodRouter from './ratingPeriod'
import channelRouter from './channel'
import healthCardRouter from './healthCard'
import teacherCourses from './teacherCourses'
import importRouter from './import'
import importClassLinkRouter from './classLinkImport'
import dropOffNoteRouter from './dropOffNote'
import exportRouter from './export'
const adminRouter = {
    path: '/admin',
    component: LayoutAdmin,
    redirect: '/admin/custom_portfolio_tags',
    name: 'LayoutAdmin',
    // meta: {
    //   title: 'Demo'
    // },
    children: [
        // 自定义档案标签
        {
            path: 'custom_portfolio_tags',
            name: 'custom_portfolio_tags',
            meta: {
                pageName: $i18n.t('loc.ctmTag'),
                activeMenu: 'customPortfolioTags'
            },
            // component: LayoutAdmin,
            // meta: { title: 'dashboard', icon: 'dashboard' },
            component: () =>
                import(/* webpackChunkName: "admin" */ '@/views/admin/CustomPortfolioTags.vue')
        },
        // 注销账户
        {
            path: 'close_account',
            name: 'close_account',
            meta: {
              pageName: $i18n.t('loc.closeAcc'),
              activeMenu: 'closeAccount'
            },
            component: () =>
                import(/* webpackChunkName: "admin" */ '@/views/admin/CloseAccount.vue')
        },
        // inkind-设置
        {
            path: 'in-kind-setting',
            name: 'in-kind-setting',
            meta: {
                pageName: $i18n.t('loc.manageInkind'),
                activeMenu: 'inKindSetting'
            },
            component: () =>
                import(/* webpackChunkName: "admin" */ '@/views/admin/InkindSetting.vue')
        },
        // 导入
        {
            path: 'import-error-handle',
            name: 'import-error-handle',
            meta: {
              breadcrumb: 'Import History',
              hideBreadCrumb: true,
              activeMenu: 'importSync',
              pageName: 'Import/Sync'
            },
            component: () =>
                import(/* webpackChunkName: "admin" */ '@/views/admin/import/ImportErrorHandle.vue')
        },
        // 核心测评点设置
        {
            path: 'drdp-setting/:type',
            name: 'drdp-setting',
            meta: {
                pageName: $i18n.t('loc.drdpExport'),
                activeMenu: 'drdpSetting'
            },
            component: () =>
                import(/* webpackChunkName: "admin" */ '@/views/admin/keyMeasures/DRDPOnlineSetting.vue')
        },
        {
            path: 'drdp-setting',
            name: 'drdp-setting',
            meta: {
                pageName: $i18n.t('loc.drdpExport'),
                activeMenu: 'drdpSetting'
            },
            component: () =>
                import(/* webpackChunkName: "admin" */ '@/views/admin/keyMeasures/DRDPOnlineSetting.vue')
        },
        {
            path: 'key-measures',
            name: 'key-measures',
            meta: {
                pageName: $i18n.t('loc.drdpExport'),
                activeMenu: 'drdpSetting'
            },
            component: () =>
                import(/* webpackChunkName: "admin" */ '@/views/admin/keyMeasures/KeyMeasuresSetting.vue')
        },
        inkindRouter,
        // 设置周期
        ratingPeriodRouter,
        channelRouter,
        // 健康卡
        healthCardRouter,
        // 导入
        importRouter,
        // class link 导入
        importClassLinkRouter,
        // 导出
        exportRouter,
         // 课程配置
        {
          path: 'lesson_setting',
          name: 'lessonsSetting',
          meta: {
            pageName: $i18n.t('loc.lessonsSetting'),
            activeMenu: 'lessonsSetting'
          },
          component: () => import(/* webpackChunkName: "admin" */ '@/views/modules/lesson2/lessonsSetting')
        },
        // 老师培训课程统计
        teacherCourses,
        // drop-off note
        dropOffNoteRouter
    ]
}
export default adminRouter
