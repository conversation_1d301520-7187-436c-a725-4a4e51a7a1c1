import LayoutAdminInkind from '@/views/layout/LayoutAdminInkind.vue'
const inkindRouter = {
  path: '/admin/inkind',
  component: LayoutAdminInkind,
  redirect: '/admin/inkind/in-kind-nosetting',
  name: 'LayoutAdminInkind',
  children: [
    {
      path: 'introduction',
      name: 'introduction',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/Introduction.vue')
    },
    {
      path: 'download-template',
      name: 'download-template',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/DownloadTemplate.vue')
    },
    { // download-template  <- 已被跳过  ->set-academic
      path: 'added-classes',
      name: 'added-classes',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/AddedClasses.vue')
    },
    {
      path: 'set-academic',
      name: 'set-academic',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/SetAcademic.vue')
    },
    {
      path: 'athome-import',
      name: 'athome-import',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/AthomeImport.vue')
    },
    {
      path: 'volunteer-import',
      name: 'volunteer-import',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/VolunteerImport.vue')
    },
    {
      path: 'athome-file-review',
      name: 'athome-file-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/AthomeFileReview.vue')
    },
    {
      path: 'volunteer-file-review',
      name: 'volunteer-file-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/VolunteerFileReview.vue')
    },
    {
      path: 'select-classes',
      name: 'select-classes',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/SelectClasess.vue')
    },
    {
      path: 'athome-class-review',
      name: 'athome-class-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/AthomeClassReview.vue')
    },
    {
      path: 'volunteer-class-review',
      name: 'volunteer-class-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/VolunteerClassReview.vue')
    },
    {
      path: 'rate-settings',
      name: 'rate-settings',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/RateSettings.vue')
    },
    {
      path: 'athome-rate',
      name: 'athome-rate',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/AthomeRateSettings.vue')
    },
    {
      path: 'groups-rate',
      name: 'groups-rate',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/GroupRateSettings.vue')
    },
    {
      path: 'finished-setup',
      name: 'finished-setup',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/FinishedSetUp.vue')
    },
    {
      path: 'step-finished',
      name: 'step-finished',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/StepFinished.vue')
    },
    {
      path: 'step-volunteer',
      name: 'step-volunteer',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/StepVolunteer.vue')
    },
    {
      path: 'setup-volunteer',
      name: 'setup-volunteer',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/SetUpVolunteer.vue')
    },
    {
      path: 'volunteer-sameset',
      name: 'volunteer-sameset',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/VolunteerSameSet.vue')
    },
    {
      path: 'athome-edit-review',
      name: 'athome-edit-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/AthomeEditReview.vue')
    },
    {
      path: 'athome-guide-review',
      name: 'athome-guide-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/AthomeGuideReview.vue')
    },
    {
      path: 'volunteer-edit-review',
      name: 'volunteer-edit-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/VolunteerEditReview.vue')
    },
    {
      path: 'group-athome-import',
      name: 'group-athome-import',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupAthomeImport.vue')
    },
    {
      path: 'group-volunteer-import',
      name: 'group-volunteer-import',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupVolunteerImport.vue')
    },
    {
      path: 'group-athome-class-review',
      name: 'group-athome-class-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupAthomeClassReview.vue')
    },
    {
      path: 'group-athome-file-review',
      name: 'group-athome-file-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupAthomeFileReview.vue')
    },
    {
      path: 'group-rate-settings',
      name: 'group-rate-settings',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupRateSettings.vue')
    },
    {
      path: 'group-athome-rate',
      name: 'group-athome-rate',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupAthomeRateSettings.vue')
    },
    {
      path: 'group-select-classes',
      name: 'group-select-classes',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupSelectClasess.vue')
    },
    {
      path: 'group-step-finished',
      name: 'group-step-finished',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupStepFinished.vue')
    },
    {
      path: 'group-step-volunteer',
      name: 'group-step-volunteer',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupStepVolunteer.vue')
    },
    {
      path: 'group-athome-guide-review',
      name: 'group-athome-guide-review',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupAthomeGuideReview.vue')
    },
    {
      path: 'group-volunteer-sameset',
      name: 'group-volunteer-sameset',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/group/GroupVolunteerSameSet.vue')
    },
    {
      path: 'athome-book-media',
      name: 'athome-book-media',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/BookAndMedia.vue')
    },
    {
      path: 'in-kind-goals',
      name: 'in-kind-goals',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/goals/InKindGoals.vue')
    },
    {
      path: 'in-kind-goals-step1',
      name: 'in-kind-goals-step1',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/goals/InKindGoalsStep1.vue')
    },
    {
      path: 'in-kind-goals-step2',
      name: 'in-kind-goals-step2',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/goals/InKindGoalsStep2.vue')
    },
    {
      path: 'in-kind-goals-step3',
      name: 'in-kind-goals-step3',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/goals/InKindGoalsStep3.vue')
    },
    {
      path: 'in-kind-goals-editor',
      name: 'in-kind-goals-editor',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/goals/InKindGoalsEditor.vue')
    },
    {
      path: 'in-kind-goals-setting',
      name: 'in-kind-goals-setting',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/goals/InKindGoalsSetting.vue')
    },
    {
      path: 'in-kind-grants',
      name: 'in-kind-grants',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/grants/InKindGrants.vue')
    },
    {
      path: 'in-kind-grants-step1',
      name: 'in-kind-grants-step1',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/grants/InKindGrantsStep1.vue')
    },
    {
      path: 'in-kind-grants-step2',
      name: 'in-kind-grants-step2',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/grants/InKindGrantsStep2.vue')
    },
    {
      path: 'in-kind-grants-editor',
      name: 'in-kind-grants-editor',
      meta: {
        pageName: $i18n.t('loc.manageInkind'),
        activeMenu: 'inKindSetting'
      },
      component: () => import(/* webpackChunkName: "inkind" */ '@/views/admin/inkind/grants/InKindGrantsEditor.vue')
    }
  ]
}
export default inkindRouter
