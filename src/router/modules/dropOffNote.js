const dropOffNoteRouter = {
  path: 'drop-off-note',
  name: 'dropOffNote',
  redirect: '/admin/drop-off-note/card-list',
  component: () => import(/* webpackChunkName: "dropOffNote" */ '@/views/admin/DropOffNote/DropOffNoteEntrance'),
  children: [
    // drop-off note 问题列表和推送信息界面
    {
      path: 'card-list',
      name: 'cardList',
      meta: {
        pageName: $i18n.t('loc.dropOffNote'),
        activeMenu: 'dropOffNoteCard'
      },
      component: () => import(/* webpackChunkName: "dropOffNote" */ '@/views/admin/DropOffNote/DropOffNoteList')
    },
    // drop-off note 选择学校班级界面
    {
      path: 'select-site-group',
      name: 'selectSiteGroup',
      meta: {
        pageName: $i18n.t('loc.dropOffNote'),
        activeMenu: 'dropOffNoteCard'
      },
      component: () => import(/* webpackChunkName: "dropOffNote" */ '@/views/admin/DropOffNote/SelectSiteGroup')
    }
  ]
}
export default dropOffNoteRouter
