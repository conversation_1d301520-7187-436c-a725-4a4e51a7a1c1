import Layout from '@/views/magicCurriculum/Layout.vue'
import Home from '@/views/magicCurriculum/home/<USER>'

const magicCurriculumRouter = {
  path: '/magic-curriculum',
  component: Layout,
  children: [
    // 识别观察记录测评点、评分
    {
      path: 'home',
      name: 'magicHome',
      component: Home,
      children: [
        {
          path: '',
          name: 'observation-label',
          component: () => import('@/views/curriculum/observation/LabelObservation.vue'),
        },
        {
          path: 'observation-score-select',
          name: 'observation-score-select',
          component: () => import('@/views/curriculum/observation/ObservationScoreSelect.vue'),
        }
      ]
    },
    // 竞赛
    {
      path: 'contest',
      name: 'magicContest',
      meta: {
        activeMenu: 'unitPlanner',
        pageName: 'PTKLF Magicurriculum Contest',
        breadcrumb: 'PTKLF Magicurriculum Contest',
      },
      component: () => import('@/views/magicCurriculum/contest/Contest.vue'),
    },
    // 课程
    {
      path: 'unit-detail',
      name: 'magicUnitDetail',
      meta: {
        activeMenu: 'unitPlanner',
        pageName: 'Unit Planner Details',
        breadcrumb: 'Unit Planner Details',
      },
      component: () => import('@/views/magicCurriculum/components/unit/detail/UnitDetail.vue'),
    },
    {
      path: 'lessondetail/:lessonId',
      name: 'magicLessonDetail',
      meta: {
        activeMenu: 'lessondetail',
        pageName: 'Lesson Details',
        breadcrumb: 'Lesson Details',
      },
      component: () => import('@/views/magicCurriculum/components/lesson/LessonDetail.vue'),
    }
  ]
}
export default magicCurriculumRouter
