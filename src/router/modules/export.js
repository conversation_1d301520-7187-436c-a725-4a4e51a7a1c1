// 导出模块路由
const exportRouter = {
  path: 'export-sftp',
  name: 'export-sftp',
  meta: {
    hideBreadCrumb: false,
    breadcrumb: $i18n.t('loc.importTitle')
  },
  redirect: '/admin/export-sftp/setting',
  component: () => import(/* webpackChunkName: "exportSftp" */ '@/views/admin/export/Export'),
  children: [
    {
      path: 'setting',
      name: 'export-sftp-setting',
      component: () => import(/* webpackChunkName: "exportSftp" */ '@/views/admin/export/sftp/Setting'),
      meta: {
        hideBreadCrumb: false,
        pageName: $i18n.t('loc.exportRatingsToSis'),
        breadcrumb: $i18n.t('loc.exportRatingsToSis'),
        activeMenu: 'importSync'
      }
    }, {
      path: 'setting-detail',
      name: 'setting-detail',
      meta: {
        hideBreadCrumb: false,
        pageName: $i18n.t('loc.exportRatingsToSis'),
        breadcrumb: $i18n.t('loc.exportRatingsToSis'),
        activeMenu: 'importSync'
      },
      component: () => import(/* webpackChunkName: "exportSftp" */ '@/views/admin/export/sftp/SettingDetail')
    }, {
      path: 'export-history',
      name: 'export-history',
      meta: {
        pageName: $i18n.t('loc.importTitle'),
        breadcrumb: $i18n.t('loc.exportRatingsToSis'),
        activeMenu: 'importSync'
      },
      component: () => import(/* webpackChunkName: "exportSftp" */ '@/views/admin/export/sftp/ExportHistory')
    }
  ]
}
export default exportRouter
