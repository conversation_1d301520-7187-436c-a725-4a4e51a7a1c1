import LayoutAdminInkind from '@/views/layout/LayoutAdminInkind.vue'

const channelRouter = {
    path: '/admin/channel',
    component: LayoutAdminInkind,
    redirect: '/admin/channel/manage-channel',
    children: [
        // channel 管理
        {
            path: 'manage-channel',
            name: 'manage-channel',
            meta: {
                pageName: $i18n.t('loc.lgMediaManage'),
                activeMenu: 'learningMedia'
            },
            component: () =>
                import(/* webpackChunkName: "channel" */ '@/views/admin/channel/ManageLearningMedia.vue')
        },
        // 自定义 channel 添加
        {
            path: 'add-channel',
            name: 'add-channel',
            meta: {
                pageName: $i18n.t('loc.lgMediaManage'),
                activeMenu: 'learningMedia'
            },
            component: () =>
                import(/* webpackChunkName: "channel" */ '@/views/admin/channel/AddChannel.vue')
        },
        // 通过文件导入 channel
        {
            path: 'update-template',
            name: 'update-template',
            meta: {
                pageName: $i18n.t('loc.lgMediaManage'),
                activeMenu: 'learningMedia'
            },
            component: () =>
                import(/* webpackChunkName: "channel" */ '@/views/admin/channel/UpdateTemplate.vue')
        },
         // 通过 PlayList 导入 channel
         {
            path: 'playList-template',
            name: 'playList-template',
            meta: {
                pageName: $i18n.t('loc.lgMediaManage'),
                activeMenu: 'learningMedia'
            },
            component: () =>
                import(/* webpackChunkName: "channel" */ '@/views/admin/channel/PlayListTemplate.vue')
        },
        // 导入预览界面
        {
            path: 'import-preview',
            name: 'import-preview',
            meta: {
                pageName: $i18n.t('loc.lgMediaManage'),
                activeMenu: 'learningMedia'
            },
            component: () =>
                import(/* webpackChunkName: "channel" */ '@/views/admin/channel/ImportPreview.vue')
        },
         // 导入预览界面
        {
            path: 'playList-preview',
            name: 'playList-preview',
            meta: {
                pageName: $i18n.t('loc.lgMediaManage'),
                activeMenu: 'learningMedia'
            },
            component: () =>
                import(/* webpackChunkName: "channel" */ '@/views/admin/channel/PlayListPreview.vue')
        },
        // 创建视频问卷表单
        {
            path: 'edit-form',
            name: 'edit-form',
            meta: {
                pageName: $i18n.t('loc.lgMediaManage'),
                activeMenu: 'learningMedia'
            },
            component: () =>
                import(/* webpackChunkName: "channel" */ '@/views/admin/channel/EditForm.vue')
        }

    ]
}
export default channelRouter
