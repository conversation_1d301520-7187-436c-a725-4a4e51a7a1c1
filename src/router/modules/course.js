import Layout from '@/views/layout/v2/Layout.vue'

const drdpReportRouter = {
  path: '/courses',
  name: 'courses',
  component: Layout,
  children: [
    {
      path: 'video-lists',
      name: 'videoList',
      meta: {
        pageName: $i18n.t('loc.GenieCourses'),
        hideBreadCrumb: true
      },
      component: () => import(/* webpackChunkName: "course" */ '@/views/course/CourseEntrance.vue')
    },
    {
      path: 'video-details/:videoId',
      name: 'videoDetail',
      meta: {
        pageName: $i18n.t('loc.GenieCourses'),
        hideBreadCrumb: true
      },
      component: () => import(/* webpackChunkName: "course" */ '@/views/course/CourseEntrance.vue')
    },
    {
      path: 'certifications',
      name: 'certificateList',
      meta: {
        pageName: $i18n.t('loc.GenieCourses'),
        breadcrumb: $i18n.t('loc.GenieCourses'),
        hideBreadCrumb: true
      },
      component: () => import(/* webpackChunkName: "course" */ '@/views/course/CourseEntrance.vue')
    }
  ]

}
export default drdpReportRouter
