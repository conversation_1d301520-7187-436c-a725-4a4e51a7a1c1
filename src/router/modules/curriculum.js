import Layout2 from '../../views/layout/v2/Layout.vue'
// import Layout from '@/views/curriculum/Layout.vue'
// import UnitEditor from '@/views/curriculum/unit/UnitEditor.vue'
const NewUnitEditor = () => import('@/views/modules/lesson2/unitPlanner/UnitEditor.vue')
// import ObservationAndAssessment from '@/views/curriculum/observation/ObservationAndAssessment.vue'
const LessonRouterView = () => import('@/views/modules/lesson2/LessonRouterView.vue')
// import PromptMgmtLayout from '@/views/curriculum/prompt/management/Layout.vue'
// import CurriculumGenieRouterView from '@/views/curriculum/designer/CurriculumGenieRouterView.vue'
// import CurriculumGenieHistoryRouterView from '@/views/curriculum/designer/components/CurriculumGenieHistoryRouterView.vue'


const curriculumRouter = {
  path: '/curriculum-genie',
  component: Layout2,
  children: [
    // // 识别观察记录测评点、评分
    // {
    //   path: 'observation',
    //   name: 'label-observation',
    //   component: ObservationAndAssessment,
    //   children: [
    //     {
    //       path: '',
    //       name: 'observation-label',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/observation/LabelObservation.vue')
    //     },
    //     {
    //       path: 'observation-score-select',
    //       name: 'observation-score-select',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/observation/ObservationScoreSelect.vue')
    //     }
    //   ]
    // },
    // // 优化观察记录
    // {
    //   path: 'optimize-observation',
    //   name: 'optimize-observation',
    //   component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/observation/ImproveObservation.vue')
    // },
    // {
    //   path: 'designer',
    //   name: 'curriculum-cg-init-router-view',
    //   component: CurriculumGenieRouterView,
    //   meta: {
    //     breadcrumb: 'Curriculum Planner',
    //     hideBreadCrumb: true
    //   },
    //   children: [
    //     // Curriculum Designer 首页面
    //     {
    //       path: '',
    //       name: 'curriculum-create-cg',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/designer/index.vue'),
    //       meta: {
    //         hideBreadCrumb: true
    //       }
    //     },
    //     {
    //       path: 'curriculum-history',
    //       name: 'curriculum-cg-history-router-view',
    //       component: CurriculumGenieHistoryRouterView,
    //       meta: {
    //         breadcrumb: 'Curriculum History',
    //         hideBreadCrumb: true
    //       },
    //       children: [
    //         // Curriculum Designer 历史页面
    //         {
    //           path: '',
    //           name: 'designer-history',
    //           component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/designer/History.vue')
    //         },
    //         {
    //           path: 'unit-detail',
    //           name: 'unit-detail-cg-desinger',
    //           meta: {
    //             activeMenu: 'unitPlanner',
    //             pageName: 'Unit Planner Details',
    //             breadcrumb: 'Unit Planner Details'
    //           },
    //           component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetail.vue')
    //         },
    //         {
    //           path: 'designer/unit-editor/:unitId',
    //           name: 'unit-editor-cg-designer',
    //           component: NewUnitEditor,
    //           children: [
    //             {
    //               path: 'lesson-overview/:week',
    //               name: 'lesson-overview-cg-designer',
    //               meta: {
    //                 breadcrumb: 'Create Unit Planner'
    //               },
    //               component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/LessonOverviewStep.vue')
    //             },
    //             {
    //               path: 'lesson-detail/:itemId?',
    //               name: 'lesson-detail-cg-designer',
    //               meta: {
    //                 breadcrumb: 'Create Unit Planner'
    //               },
    //               component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitReview.vue')
    //             }
    //           ]
    //         }
    //       ]
    //     }
    //   ]
    // },
    // {
    //   path: 'designer/:curriculumId',
    //   name: 'curriculum-cg-router-view',
    //   component: CurriculumGenieRouterView,
    //   children: [
    //     // Curriculum Designer 首页面
    //     {
    //       path: '',
    //       name: 'curriculum-edit-cg',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/designer/index.vue')
    //     },

    //     // Curriculum UnitFoundations 页面
    //     {
    //       path: 'unit-foundation',
    //       name: 'unit-foundation',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/designer/UnitFoundations.vue')
    //     }
    //   ]
    // },
    // // LessonPlan List 首页面
    // {
    //   path: 'lesson-plan',
    //   component:  () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/lessonPlan/LessonPlanRouterView.vue'),
    //   redirect: '/curriculum-genie/lesson-plan',
    //   meta: {
    //     activeMenu: 'LessonPlan',
    //     breadcrumb: 'Lesson Plan'
    //   },
    //   children: [
    //     {
    //       path: '',
    //       name: 'lesson-plan-List',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/lessonPlan/index.vue'),
    //       meta: {
    //         activeMenu: 'LessonPlan',
    //         breadcrumb: 'Lesson Plan',
    //         hideBreadCrumb: true
    //       },
    //     },
    //     // lessonPlan 编辑页
    //     {
    //       path: 'edit-lesson/:lessonId?',
    //       name: 'EditLessonPlan',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/lessonLibrary/editor'),
    //       props: route => {
    //         return {
    //           ...route.params,
    //           showAssistantTool: true, // 控制 AssistantTool 组件显示
    //           aiAssistantDllOpen: true
    //         }
    //       },
    //       meta: {
    //         pageName: $i18n.t('loc.lessons2EditLessonTitle'),
    //         breadcrumb: $i18n.t('loc.lessons2EditLessonTitle'),
    //         activeMenu: 'PublicLessonList'
    //       }
    //     },
    //     // lessonPlan 新建页
    //     {
    //       path: 'add-lesson/:lessonId?',
    //       name: 'AddLessonPlan',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/lessonLibrary/editor'),
    //       props: route => {
    //         return {
    //           ...route.params,
    //           showAssistantTool: true, // 控制 AssistantTool 组件显示
    //           aiAssistantDllOpen: true
    //         }
    //       },
    //       meta: {
    //         pageName: $i18n.t('loc.createLessonBtn'),
    //         breadcrumb: $i18n.t('loc.createLessonBtn'),
    //         activeMenu: 'PublicLessonList'
    //       }
    //     },
    //     // lesson 复制页
    //     {
    //       path: 'replicate/:lessonId?',
    //       name: 'LessonPlanReplicate',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/lessonLibrary/editor/index'),
    //       props: route => {
    //         return {
    //           ...route.params,
    //           showAssistantTool: true, // 控制 AssistantTool 组件显示
    //           aiAssistantDllOpen: true
    //         }
    //       },
    //       meta: {
    //         pageName: $i18n.t('loc.lesson2NewLessonTitle'),
    //         breadcrumb: $i18n.t('loc.lesson2NewLessonTitle'),
    //         activeMenu: 'PublicLessonList'
    //       }
    //     },
    //   ]
    // },
    // {
    //   path: 'setting',
    //   component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/setting/SettingRouterView.vue'),
    //   redirect: '/curriculum-genie/setting',
    //   meta: {
    //     activeMenu: 'Settings',
    //     breadcrumb: $i18n.t('loc.settings'),
    //   },
    //   children: [
    //     // 自定义 Unit Foundation 编辑页
    //     {
    //       path: 'edit-unit-foundation',
    //       name: 'EditUnitFoundation',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/setting/components/EditUnitFoundation.vue'),
    //       meta: {
    //         pageName: $i18n.t('loc.customizeUnitFoundationModules'),
    //         breadcrumb: $i18n.t('loc.customizeUnitFoundationModules'),
    //         activeMenu: 'setting'
    //       }
    //     }
    //   ]
    // },
    //
    // {
    //   path: 'unit',
    //   name: 'unit-creator',
    //   component: UnitEditor,
    //   children: [
    //     {
    //       path: '',
    //       name: 'create-unit-overview',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/UnitOverviewStep.vue')
    //     }
    //   ]
    // },
    // // 班级学生名单列表
    // {
    //   path: 'roster',
    //   name: 'roster',
    //   component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/roster/RosterList.vue')
    // },
    // // 单元课程列表
    // {
    //   path: 'unit/list',
    //   name: 'unit-list',
    //   component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/UnitList.vue')
    // },
    // // 单元文档
    // {
    //   path: 'unit/document',
    //   name: 'unit-document',
    //   component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/UnitDocument.vue')
    // },
    // {
    //   path: 'unit/document/:unitId',
    //   name: 'unit-document-with-id',
    //   component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/UnitDocument.vue')
    // },
    // {
    //   path: 'unit/:unitId',
    //   name: 'unit-editor-cg-v1',
    //   component: UnitEditor,
    //   children: [
    //     {
    //       path: 'load-unit',
    //       name: 'load-unit',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/LoadUnit.vue')
    //     },
    //     {
    //       path: 'unit-overview',
    //       name: 'unit-overview',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/UnitOverviewStep.vue')
    //     },
    //     {
    //       path: 'weekly-plan-overview',
    //       name: 'weekly-plan-overview-cg-v1',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/WeeklyPlanOverviewStep.vue')
    //     },
    //     {
    //       path: 'lesson-overview/:week',
    //       name: 'lesson-overview',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/LessonOverviewStep.vue')
    //     },
    //     {
    //       path: 'lesson-detail',
    //       name: 'lesson-detail',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/unit/components/editor/UnitReview.vue')
    //     }
    //   ]
    // },
    // // 分析观察笔记
    // {
    //   path: 'analyse-observation',
    //   name: 'analyse-observation',
    //   component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/observation/AnalyseObservation.vue')
    // },
    // // Prompt 管理
    // {
    //   path: 'prompt-mgmt',
    //   name: 'prompt-mgmt-layout',
    //   component: PromptMgmtLayout,
    //   children: [
    //     {
    //       path: '',
    //       name: 'prompt-mgmt',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/prompt/management/PromptMgmt.vue')
    //     },
    //     {
    //       path: 'playground',
    //       name: 'prompt-playground',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/playground/Playground.vue')
    //     },
    //     {
    //       path: 'playground/:promptId',
    //       name: 'prompt-playground-with-id',
    //       component: () => import(/* webpackChunkName: "curriculum" */ '@/views/curriculum/playground/Playground.vue')
    //     }
    //   ]
    // },
    // 新版 Unit Planner 路由
    {
      path: 'unit-planner',
      component: LessonRouterView,
      meta: {
        activeMenu: 'unitPlanner',
        breadcrumb: 'Unit Planner'
      },
      redirect: '/curriculum-genie/unit-planner',
      children: [
        {
            path: '',
            name: 'unit-planner-cg',
            meta: {
              activeMenu: 'unitPlanner',
              pageName: 'Unit Planner',
              hideBreadCrumb: true
            },
            component: () => import(/* webpackChunkName: "curriculum-main" */ '@/views/modules/lesson2/unitPlanner/UnitLayout.vue')
        },
        {
          path: 'unit',
          name: 'unit-cg',
          component: NewUnitEditor,
          children: [
            {
              path: '',
              name: 'unit-creator-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "unit-creator" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitOverviewStep.vue')
            },
            {
              path: 'weekly-plan-overview',
              name: 'weekly-plan-overview-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/WeeklyPlanOverviewStep.vue')
            }
          ]
        },
        {
          path: 'unit-editor/:unitId',
          name: 'unit-editor-cg',
          component: NewUnitEditor,
          children: [
            {
              path: 'load-unit',
              name: 'load-unit-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/LoadUnit.vue')
            },
            {
              path: 'unit-overview',
              name: 'unit-overview-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitOverviewStep.vue')
            },
            {
              path: 'weekly-plan-overview',
              name: 'edit-weekly-plan-overview-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/WeeklyPlanOverviewStep.vue')
            },
            {
              path: 'lesson-overview/:week',
              name: 'lesson-overview-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/LessonOverviewStep.vue')
            },
            {
              path: 'lesson-detail/:itemId?',
              name: 'lesson-detail-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitReview.vue')
            },
            {
              path: 'center-lesson-detail/:itemId?',
              name: 'center-lesson-detail-cg',
              meta: {
                activeMenu: 'unitPlanner',
                pageName: 'Create Unit Planner',
                breadcrumb: 'Create Unit Planner'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitReview.vue')
            }
          ]
        },
        {
          path: 'unit-detail',
          name: 'unit-detail-cg',
          meta: {
            activeMenu: 'unitPlanner',
            pageName: 'Unit Planner Details',
            breadcrumb: 'Unit Planner Details'
          },
          component: () => import(/* webpackChunkName: "unit-detail" */ '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetail.vue')
        },
      ]
    },
  
    {
      path: 'unit-adapt',
      component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/adaptImportUnit/ImportLayout.vue'),
      meta: {
        activeMenu: 'unitImport',
        pageName: 'unitImport',
        breadcrumb: 'Unit Planner'
      },
      redirect: '/curriculum-genie/unit-adapt',
      children: [
        {
          path: 'import-unit',
          name: 'import-unit-cg',
          meta: {
            pageName: 'adaptExistingUnit',
            breadcrumb: 'Adapt Existing Unit'
          },
          component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/analysis/ImportUnit.vue')
        },
        {
          path: 'import-result/:id',
          name: 'import-result-cg',
          meta: {
            activeMenu: 'adaptExistingUnit',
            breadcrumb: 'Adapt Existing Unit'
          },
          component: () => import(/* webpackChunkName: "curriculum-main" */ '@/views/modules/lesson2/unitPlanner/components/analysis/ImportResult.vue')
        },
        {
          path: 'adapt-select/:id',
          name: 'adapt-select',
          meta: {
            activeMenu: 'adaptExistingUnit',
            breadcrumb: 'Adapt Existing Unit'
          },
          component: () => import(/* webpackChunkName: "curriculum-main" */ '@/views/modules/lesson2/unitPlanner/components/adaptImportUnit/AdaptSelect.vue')
        },
      
        {
          path: 'unit-planner',
          component: LessonRouterView,
          redirect: '/curriculum-genie/unit-planner',
          children: [
            {
              path: '',
              name: 'unit-planner-cg-adapt',
              meta: {
                activeMenu: 'unitImport',
                pageName: 'Unit Planner',
                hideBreadCrumb: true
              },
              component: () => import(/* webpackChunkName: "curriculum-main" */ '@/views/modules/lesson2/unitPlanner/UnitLayout.vue')
            },
            {
              path: 'unit',
              name: 'unit-cg-adapt',
              component: NewUnitEditor,
              children: [
                {
                  path: '',
                  name: 'unit-creator-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitOverviewStep.vue')
                },
                {
                  path: 'weekly-plan-overview',
                  name: 'weekly-plan-overview-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/WeeklyPlanOverviewStep.vue')
                }
              ]
            },
            {
              path: 'unit-editor/:unitId',
              name: 'unit-editor-cg-adapt',
              component: NewUnitEditor,
              children: [
                {
                  path: 'load-unit',
                  name: 'load-unit-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/LoadUnit.vue')
                },
                {
                  path: 'unit-overview',
                  name: 'unit-overview-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitOverviewStep.vue')
                },
                {
                  path: 'weekly-plan-overview',
                  name: 'edit-weekly-plan-overview-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/WeeklyPlanOverviewStep.vue')
                },
                {
                  path: 'lesson-overview/:week',
                  name: 'lesson-overview-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/LessonOverviewStep.vue')
                },
                {
                  path: 'lesson-detail/:itemId?',
                  name: 'lesson-detail-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitReview.vue')
                },
                {
                  path: 'center-lesson-detail/:itemId?',
                  name: 'center-lesson-detail-cg-adapt',
                  meta: {
                    activeMenu: 'unitImport',
                    pageName: 'Adapt Existing Unit',
                    breadcrumb: 'Adapt Existing Unit'
                  },
                  component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/editor/UnitReview.vue')
                }
              ]
            },
            {
              path: 'unit-detail',
              name: 'unit-detail-cg-adapt',
              meta: {
                activeMenu: 'unitImport',
                pageName: 'Unit Planner Details',
                breadcrumb: 'Unit Planner Details'
              },
              component: () => import(/* webpackChunkName: "curriculum" */ '@/views/modules/lesson2/unitPlanner/components/detail/UnitDetail.vue')
            },
          ]
        },
      ]
    },
  ]
}
export default curriculumRouter
