import Vue from 'vue'
import Router from 'vue-router'
/* layout */
import Layout from '@/views/layout/Layout.vue'
// import LessonRouterView from '../views/modules/lesson2/LessonRouterView.vue'
// import NewLayout from '@/views/layout/v2/Layout.vue'
// import CustomThemePreview from '@/views/customThemePreview/index.vue'
/* router map */
// import adminRouter from './modules/admin'
// import drdpReportRouter from './modules/drdpReport'
import lesson2Router from './modules/lessons2'
// import courseRouter from './modules/course'
// import schoolMessageRouter from './modules/message'
import curriculumRouter from './modules/curriculum'
// import curriculumManagementRouter from './modules/curriculumManagement'
// import magicCurriculumRouter from '@/router/modules/magicCurriculum';
import { platform, platformRedirectMap } from '@/utils/setBaseUrl' // 配置文件
import analytics from '@/utils/analytics' // 导入分析工具

Vue.use(Router)

// 默认重定向
const defaultRedirect = platformRedirectMap[platform] || '/login'

export const constantRouterMap = [
  // 初始化重定向
  {
    path: '',
    name: 'layout',
    component: Layout,
    redirect: defaultRedirect,
    meta: {
      init: true
    }
  },
  // // Magicurriculum
  // magicCurriculumRouter,
  // Curriculum Genie
  // // Curriculum Genie 后台管理
  // curriculumManagementRouter,
  // Curriculum Genie 登录页面
  // {
  //   path: '/curriculum-genie/login',
  //   name: 'curriculum-genie-login',
  //   meta: { requireAuth: true },
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/curriculum/management/Login.vue')
  // },
  // {
  //   path: '/preview',
  //   redirect: '/preview/new-theme',
  //   component: Layout,
  //   meta: {
  //     requireAuth: true,
  //     breadcrumb: 'Home',
  //     path: '/preview'
  //   },
  //   children: [
  //     {
  //       path: 'new-theme',
  //       name: 'newTheme',
  //       component: CustomThemePreview,
  //       meta: {
  //         v2: true,
  //         breadcrumb: '主题预览',
  //         path: '/preview/new-theme'
  //       }
  //     }
  //   ]
  // },
  // // 家长界面
  // {
  //   path: '/parent',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/parent/Parent'),
  //   meta: {
  //     v1: true
  //   }
  // },
  // // 修改个人信息界面
  // {
  //   path: '/edit-profile',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/parent/EditProfile'),
  //   meta: {
  //     v1: true
  //   }
  // },
  // 登录
  {
    path: '/login',
    name: 'cg-login',
    component: () => import(/* webpackChunkName: "curriculum-main" */ '@/views/cg/login.vue'),  
    meta: { requireAuth: true, verifySession: true }
  },
  curriculumRouter,
  lesson2Router
  // // 重置密码页面
  // {
  //   path: '/reset-password',
  //   name: 'resetPassword',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/resetPassword.vue'),
  //   meta: { requireAuth: true }
  // },
  // // 聊天
  // {
  //   path: '/webChat',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/chat/WebChat')
  // },
  // // Attendance Review
  // {
  //   path: '/attendance-review',
  //   component: NewLayout,
  //   redirect: '/attendance-review/centerList/review',
  //   meta: {
  //     pageName: 'Attendance',
  //     breadcrumb: $i18n.t('loc.attendance')
  //   },
  //   children: [
  //     {
  //       path: 'centerList',
  //       name: 'attendance-review-centerList',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/AttendanceReview.vue'),
  //       meta: {
  //         v2: true,
  //         pageName: 'Attendance Review',
  //         activeMenu: 'attendance-review-centerList',
  //         breadcrumb: 'Attendance Review'
  //       }
  //     },
  //     {
  //       path: 'centerList/review',
  //       name: 'attendance-centerList-review',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/AttendanceReview.vue'),
  //       meta: {
  //         v2: true,
  //         pageName: 'Sign-in/out Review',
  //         activeMenu: 'attendance-centerList-review',
  //         breadcrumb: 'Sign-in/out Review'
  //       }
  //     },
  //     {
  //       path: 'groupDetail',
  //       name: 'attendance-review-groupDetail',
  //       meta: {
  //         pageName: 'AttendanceReview',
  //         breadcrumb: 'Attendance Review',
  //         activeMenu: 'attendance-review-centerList'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/AttendanceReviewDetails')
  //     },
  //     {
  //       path: 'searchChild',
  //       name: 'attendance-review-searchChild',
  //       meta: {
  //         pageName: 'A~Z Search',
  //         breadcrumb: 'Search Child',
  //         activeMenu: 'attendance-review-centerList'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/SearchChild.vue')
  //     },
  //     {
  //       path: 'centerList/searchChild',
  //       name: 'attendance-signIn-searchChild',
  //       meta: {
  //         pageName: 'A~Z Search',
  //         breadcrumb: 'Search Child',
  //         activeMenu: 'attendance-centerList-review'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/SearchChild.vue')
  //     },
  //     {
  //       path: 'batchFillOutForm',
  //       name: 'batch-fill-out-form',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/components/BatchFillOutForm.vue')
  //     },
  //     {
  //       path: 'supplement/:groupId',
  //       name: 'supplement',
  //       meta: {
  //         pageName: $i18n.t('loc.updateAttendance'),
  //         breadcrumb: $i18n.t('loc.updateAttendance'),
  //         activeMenu: 'attendance-review-centerList'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/GroupSupplement.vue')
  //     },
  //     {
  //       path: 'batchFillInVHC/:groupId',
  //       name: 'batchFillInVHC',
  //       meta: {
  //         pageName: $i18n.t('loc.VisualHealthCheck'),
  //         breadcrumb: $i18n.t('loc.VisualHealthCheck'),
  //         activeMenu: 'attendance-review-centerList'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/BatchFillInVHC.vue')
  //     },
  //     {
  //       path: 'batchFillInVHC/center',
  //       name: 'signBatchFillInVHC',
  //       meta: {
  //         pageName: $i18n.t('loc.VisualHealthCheck'),
  //         breadcrumb: $i18n.t('loc.VisualHealthCheck'),
  //         activeMenu: 'attendance-centerList-review'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/BatchFillInVHC.vue')
  //     }
  //   ]
  // },
  // // in-kind
  // {
  //   path: '/inkindreview',
  //   component: NewLayout,
  //   redirect: '/inkindreview/inkindselect',
  //   meta: {
  //     breadcrumb: $i18n.t('loc.inKindReport')
  //   },
  //   children: [
  //     {
  //       path: 'inkindselect',
  //       name: 'in-kind-select',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/InkindSelect.vue'),
  //       meta: {
  //         v2: true,
  //         pageName: $i18n.t('loc.summaryReport'),
  //         breadcrumb: $i18n.t('loc.summaryReport'),
  //         activeMenu: 'in-kind-select'
  //       }
  //     },
  //     {
  //       path: 'inkindselect/goals',
  //       name: 'in-kind-goals',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/InkindSelect.vue'),
  //       meta: {
  //         v2: true,
  //         pageName: $i18n.t('loc.inKindGoals'),
  //         breadcrumb: $i18n.t('loc.inKindGoals'),
  //         activeMenu: 'in-kind-goals'
  //       }
  //     },
  //     {
  //       path: 'inkindselect/approve/:type?/:centerId?/:groupId?/:childId?/:showContentType?',
  //       name: 'in-kind-approve',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/InkindSelect.vue'),
  //       meta: {
  //         v2: true,
  //         pageName: $i18n.t('loc.inKindApproval'),
  //         breadcrumb: $i18n.t('loc.inKindApproval'),
  //         activeMenu: 'in-kind-approve'
  //       }
  //     },
  //     {
  //       path: 'inkindselect/assignment',
  //       name: 'in-kind-assignment',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/InkindSelect.vue'),
  //       meta: {
  //         v2: true,
  //         pageName: $i18n.t('loc.activityAssignment'),
  //         breadcrumb: $i18n.t('loc.activityAssignment'),
  //         activeMenu: 'in-kind-assignment'
  //       }
  //     },
  //     {
  //       path: 'inkindreport',
  //       name: 'in-kind',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/InkindReport.vue')
  //     },
  //     {
  //       path: 'in-kind-table/:default',
  //       name: 'in-kind-table',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/InKindTab.vue')
  //     },
  //     {
  //       path: 'statistics',
  //       name: 'in-kind-statistics',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/StatisticsTab.vue')
  //     },
  //     {
  //       path: 'in-kind-ignore-table/:fromDate',
  //       name: 'in-kind-ignore-table',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/IgnoreTab.vue'),
  //       meta: {
  //         v2: true,
  //         pageName: $i18n.t('loc.discardAct'),
  //         breadcrumb: $i18n.t('loc.discardAct'),
  //         activeMenu: 'in-kind-approve'
  //       }
  //     }
  //   ]
  // },
  // // 路由过大时，可以如示例所示切割出去
  // adminRouter,
  // // 报表
  // {
  //   path: '/dashboard',
  //   component: NewLayout,
  //   meta: {
  //     breadcrumb: $i18n.t('loc.dataHub'),
  //     hideBreadCrumb: true
  //   },
  //   redirect: '/dashboard/engagement',
  //   children: [
  //     {
  //       path: 'engagement',
  //       name: 'FamilyEngagement',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/Engagement.vue'),
  //       meta: {
  //         breadcrumb: 'Family Engagement',
  //         hideBreadCrumb: true,
  //         activeMenu: 'engagementChart'
  //       },
  //       children: [
  //         {
  //           path: '',
  //           name: 'engagementChart',
  //           meta: {
  //             pageName: 'Family Engagement',
  //             activeMenu: 'engagementChart'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/EngagementChart.vue')
  //         },
  //         {
  //           path: 'detail',
  //           name: 'engagementDetail',
  //           meta: {
  //             pageName: 'Learning Media',
  //             breadcrumb: 'Learning Media',
  //             activeMenu: 'engagementChart'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/EngagementDetail.vue')
  //         }, //
  //         {
  //           path: 'schoolMessage',
  //           name: 'EngagementSchool',
  //           meta: {
  //             pageName: $i18n.t('loc.schoolMessage'),
  //             breadcrumb: $i18n.t('loc.schoolMessage'),
  //             hideBreadCrumb: false,
  //             activeMenu: 'engagementChart'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/EngagementSchool.vue')
  //         },
  //         {
  //           path: 'EngagementDetail',
  //           name: 'EngagementDetail',
  //           meta: {
  //             pageName: 'Engagement Details',
  //             breadcrumb: 'Engagement Details',
  //             hideBreadCrumb: false,
  //             activeMenu: 'engagementChart'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/EngagementEngagementDetail.vue')
  //         },
  //         {
  //           path: 'ChatDetail',
  //           name: 'ChatDetail',
  //           meta: {
  //             pageName: 'Two-Way Communication',
  //             breadcrumb: 'Two-Way Communication',
  //             activeMenu: 'engagementChart'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/EngagementChatDetail')
  //         },
  //         {
  //           path: '/dashboard/engagement/dllDataDetails',
  //           name: 'dllBookDataDetails',
  //           meta: {
  //             pageName: 'DLL Data Overview',
  //             breadcrumb: 'DLL Data Overview',
  //             hideBreadCrumb: false,
  //             activeMenu: 'engagementChart'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/DLLDataDetail')
  //         },
  //         {
  //           path: '/dashboard/engagement/dllBookDataDetails',
  //           name: 'dllBookDataDetails',
  //           meta: {
  //             pageName: 'DLL Books Shared With Families',
  //             breadcrumb: 'DLL Books Shared With Families',
  //             hideBreadCrumb: false,
  //             activeMenu: 'engagementChart'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/DLLBookDataDetail')
  //         },
  //         {
  //           path: '/dashboard/engagement/dllDataDetails',
  //           name: 'dllBookDataDetails',
  //           component: LessonRouterView,
  //           meta: {
  //             pageName: 'DLL Data Overview',
  //             breadcrumb: 'DLL Data Overview',
  //             hideBreadCrumb: false,
  //             activeMenu: 'engagementChart'
  //           },
  //           children: [
  //             {
  //               path: 'dllTeacherDetails',
  //               name: 'dllTeacherDetails',
  //               meta: {
  //                 pageName: 'DLL At-home Vocabulary Activities',
  //                 breadcrumb: 'DLL At-home Vocabulary Activities',
  //                 hideBreadCrumb: false,
  //                 activeMenu: 'engagementChart'
  //               },
  //               component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/DLLTeacherDataDetails')
  //             }
  //           ]
  //         }
  //       ]
  //     },
  //     {
  //       path: 'dashboard-nodata',
  //       name: 'dashboard-nodata',
  //       meta: {
  //         pageName: 'Family Engagement',
  //         breadcrumb: 'Family Engagement',
  //         hideBreadCrumb: false,
  //         activeMenu: 'engagementChart'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/DashboardNodata.vue')
  //     }
  //   ]
  // },
  // // dll  book 统计详情
  // {
  //   path: '/dashboard',
  //   component: Layout,
  //   redirect: '/dashboard/engagement/dllBookDataDetails',
  //   children: [
  //     {
  //       path: '/dashboard/engagement/dllBookDataDetails',
  //       name: 'dllBookDataDetails',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dashboard/DLLBookDataDetail')
  //     }
  //   ]
  // },
  // // 反馈页面
  // {
  //   path: '/feedback',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/feedback/feedback.vue'),
  //   meta: { requireAuth: true }
  // },
  // {
  //   path: '/feedbackNo',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/feedback/feedbackNo.vue'),
  //   meta: { requireAuth: true }
  // },
  // {
  //   path: '/feedbackToEmail',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/feedback/feedbackToEmail.vue'),
  //   meta: { requireAuth: true }
  // },
  // {
  //   path: '/adownLoadApp',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/feedback/adownLoadApp.vue'),
  //   meta: { requireAuth: true }
  // },
  // // 健康卡统计
  // {
  //   path: '/healthstats',
  //   component: NewLayout,
  //   meta: {
  //     pageName: '',
  //     hideBreadCrumb: false,
  //     breadcrumb: $i18n.t('loc.attendance')
  //   },
  //   redirect: '/healthstats/child',
  //   children: [
  //     {
  //       path: '/healthstats/child',
  //       name: 'healthstats-child',
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/healthCard/healthCardStats/HealthCardStats.vue'),
  //       meta: {
  //         v2: true,
  //         hideBreadCrumb: false,
  //         pageName: 'Attendance Review',
  //         activeMenu: 'healthstats-child',
  //         breadcrumb: $i18n.t('loc.healthStatistics')
  //       }
  //     }
  //   ]
  // },
  // drdpReportRouter,
  // // Survey
  // {
  //   path: '/survey',
  //   component: NewLayout,
  //   redirect: '/survey',
  //   children: [{
  //     path: '/',
  //     name: 'surveyList',
  //     component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/survey/SurveyEntrance.vue'),
  //     meta: {
  //       hideBreadCrumb: true,
  //       pageName: $i18n.t('loc.survey'),
  //       activeMenu: 'surveyList'
  //     }
  //   }]
  // },
  
  // schoolMessageRouter,
  // // 培训课程路由
  // courseRouter,
  // // 教师端首页的DLL
  // {
  //   path: '/dllteacher',
  //   component: NewLayout,
  //   name: 'dllteacher',
  //   redirect: '/dllteacher/dllList',
  //   meta: {
  //     breadcrumb: 'Home'
  //   },
  //   children: [
  //     {
  //       path: 'dllList',
  //       name: 'dllTeacherList',
  //       meta: {
  //         pageName: 'DLL',
  //         breadcrumb: 'DLL',
  //         activeMenu: 'home'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLTeacherEntrance.vue'),
  //       children: [
  //         // 教师端DLL新评论的详情界面
  //         {
  //           path: 'dllCommentDetails',
  //           name: 'dllCommentDetails',
  //           meta: {
  //             pageName: 'DLL Comment Details',
  //             breadcrumb: 'DLL Comment Details',
  //             activeMenu: 'home'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLCommentDetails.vue')
  //         },
  //         // 教师端DLL详情界面
  //         {
  //           path: 'dllDetails',
  //           name: 'dllDetails',
  //           meta: {
  //             pageName: 'DLL Details',
  //             breadcrumb: 'DLL Details',
  //             activeMenu: 'home'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLDetails.vue')
  //         },
  //         {
  //           path: 'dllTemplateLibrary',
  //           name: 'dllLibrary',
  //           meta: {
  //             pageName: 'DLL Library',
  //             breadcrumb: 'DLL Library',
  //             activeMenu: 'home'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLTemplateLibrary.vue')
  //         }
  //       ]
  //     }
  //   ]
  // },
  // {
  //   path: '/dll',
  //   component: NewLayout,
  //   redirect: '/dll/dllList',
  //   meta: {
  //     breadcrumb: 'DLL'
  //   },
  //   children: [
  //     {
  //       path: 'dllList',
  //       name: 'dll',
  //       meta: {
  //         pageName: $i18n.t('loc.dll'),
  //         activeMenu: 'dll'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLTeacherEntrance.vue'),
  //       children: [
  //         // 教师端DLL新评论的详情界面
  //         {
  //           path: 'dllCommentDetails',
  //           name: 'dllCommentDetails',
  //           meta: {
  //             pageName: 'DLL Comment Details',
  //             breadcrumb: 'DLL Comment Details',
  //             activeMenu: 'dll'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLCommentDetails.vue')
  //         },
  //         // 教师端DLL详情界面
  //         {
  //           path: 'dllDetail',
  //           name: 'dllDetail',
  //           meta: {
  //             pageName: 'DLL Details',
  //             breadcrumb: 'DLL Details',
  //             activeMenu: 'dll'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLDetails.vue')
  //         },
  //         {
  //           path: 'dllTemplateLibrary',
  //           name: 'dllTemplateLibrary',
  //           meta: {
  //             pageName: 'DLL Library',
  //             breadcrumb: 'DLL Library',
  //             activeMenu: 'dll'
  //           },
  //           component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLTemplateLibrary.vue')
  //         }
  //       ]
  //     }
  //   ]
  // },
  // // dll book 新消息详情界面
  // {
  //   path: '/dllBookNewNotice',
  //   component: NewLayout,
  //   redirect: '/dllBookNewNotice/:centerId/:groupId/:studentId',
  //   meta: {
  //     breadcrumb: 'Home'
  //   },
  //   children: [
  //     {
  //       path: '/dllBookNewNotice/:centerId/:groupId/:studentId',
  //       name: 'dllBookCommentDetails',
  //       meta: {
  //         pageName: 'Comment Details',
  //         breadcrumb: 'Comment Details',
  //         activeMenu: 'home'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLBookCommentDetails.vue')
  //     }
  //   ]
  // },
  // // dll book详情界面
  // {
  //   path: '/dllBookDetails',
  //   component: NewLayout,
  //   redirect: '/dllBookDetails/:centerId/:groupId/:studentId/:bookId',
  //   meta: {
  //     breadcrumb: 'Home'
  //   },
  //   children: [
  //     {
  //       path: '/dllBookDetails/:centerId/:groupId/:studentId/:bookId',
  //       name: 'dllBookDetails',
  //       meta: {
  //         pageName: 'DLL Book Details',
  //         breadcrumb: 'DLL Book Details',
  //         activeMenu: 'home'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLBookDetails.vue')
  //     }
  //   ]
  // },
  // // dll 教练入口
  // {
  //   path: '/dllCoach',
  //   component: Layout,
  //   redirect: '/dllCoach',
  //   children: [{
  //     path: '/dllCoach',
  //     name: 'dllCoachList',
  //     component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/dll/DLLCoachEntrance.vue')
  //   }]
  // },
  // // 婴儿睡眠检测
  // {
  //   path: '/infantSleepCheck',
  //   component: NewLayout,
  //   redirect: '/infantSleepCheck/infantSleepCheckList',
  //   meta: {
  //     breadcrumb: $i18n.t('loc.home')
  //   },
  //   children: [
  //     {
  //       path: 'infantSleepCheckList',
  //       name: 'infantSleepCheck',
  //       meta: {
  //         pageName: $i18n.t('loc.infantSleepCheck'),
  //         breadcrumb: $i18n.t('loc.infantSleepCheck'),
  //         activeMenu: 'home'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/sleepcheck/InfantSleepCheckEntrance.vue')
  //     }
  //   ]
  // },
  // {
  //   path: '/infantSleep',
  //   component: NewLayout,
  //   redirect: '/infantSleep/infantSleepCheckList',
  //   children: [
  //     {
  //       path: 'infantSleepCheckList',
  //       name: 'infantSleep',
  //       meta: {
  //         pageName: $i18n.t('loc.infantSleepCheck'),
  //         activeMenu: 'infantSleep'
  //       },
  //       component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/sleepcheck/InfantSleepCheckEntrance.vue')
  //     }
  //   ]
  // },
  // // in-kind 填写界面
  // {
  //   path: '/inKindSubmission/:linkId/:userId/:agencyId/:agencyName',
  //   name: 'inKindSubmission',
  //   component: () => import(/* webpackChunkName: "routerIndex" */ '@/views/inkindreview/InKindSubmission'),
  //   meta: { requireAuth: true }
  // }

]

const router = new Router({
  mode: 'hash',
  base: process.env.BASE_URL,
  scrollBehavior: () => ({ y: 0 }), // 保存滚动位置
  routes: constantRouterMap
})

// 添加全局路由导航守卫，用于追踪页面访问数据
router.afterEach((to, from) => {
  let path = '';

  // 获取当前路由的规范化路径
  if (to.matched && to.matched.length > 0) {
    // 优先使用最后一个匹配的路由记录路径，避免携带动态参数
    path = to.matched[to.matched.length - 1].path;
  } else {
    // 如果没有匹配记录则使用完整路径
    path = to.path;
  }

  // 调用 analytics 追踪页面访问
  analytics.trackRouteChange(path);
})

// 当想要在非Vue的应用中调用时，通过$router
window.$router = router

export default router
