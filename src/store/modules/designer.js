const designer = {
  namespace: true,
  state: {
    id: null,
    curriculumName: '',
    framework: {},
    unitsFromDesigner: [], // 所有单元信息
    baseInfo: {
      frameworkId: '', // 框架id
      frameworkName: '', // 框架名称
      country: '', // 国家
      state: '', // 州
      city: '', // 城市
      grade: '', // 年级
      useLocation: true, // 是否使用位置信息
      language: '', // 语言
      useDomain: true, // 是否使用领域分配测评点
      domains: [], // 框架下测评点
      learnerProfileId: '', // 校训 ID
      rubrics: '', // 评估标准
      teachingTipsForLearnerProfile: 0, // 学习者素养培养指南
    }, // curriculum 基本信息
    frameworkData: [],
    rowColors: ['#EFF9E7','#DEF1F5','#F4E0ED','#E7E6FB']
  },
  actions: {
    setCurriculumId ({ commit }, id) {
      commit('SET_CURRICULUM_ID', id)
    },
    setCurriculumName ({ commit }, curriculumName) {
      commit('SET_CURRICULUM_NAME', curriculumName)
    },
    setBaseInfo ({ commit }, baseInfo) {
      commit('SET_BASE_INFO', baseInfo)
    },
    setFrame ({ commit }, framework) {
      commit('SET_FRAMEWORK', framework)
    },
    setUnitsFromDesigner ({ commit }, unitsFromDesigner) {
      commit('SET_UNITS', unitsFromDesigner)
    }
  },
  mutations: {
    SET_CURRICULUM_ID (state, id) {
      state.id = id
    },
    SET_CURRICULUM_NAME (state, curriculumName) {
      state.curriculumName = curriculumName
    },
    SET_BASE_INFO (state, baseInfo) {
      state.baseInfo = baseInfo
    },
    SET_FRAMEWORK (state, framework) {
      state.framework = framework
    },
    SET_UNITS (state, unitsFromDesigner) {
      // Vue.set(state, 'unitsFromDesigner', unitsFromDesigner)
      state.unitsFromDesigner = unitsFromDesigner
    },
    SET_UNIT_BASE_INFO (state, baseInfoAndUnitIndex) {
      const units = state.unitsFromDesigner
      if (baseInfoAndUnitIndex.unitIndex < units.length) {
        state.unitsFromDesigner[baseInfoAndUnitIndex.unitIndex].baseInfo = baseInfoAndUnitIndex.baseInfo
      }
    },
    SET_UNIT_WEEKLY_PLANS (state, weeklyPlansAndUnitIndex) {
      const units = state.unitsFromDesigner
      if (weeklyPlansAndUnitIndex.unitIndex < units.length) {
        // this.$set(state.unitsFromDesigner[weeklyPlansAndUnitIndex.unitIndex].weeklyPlans, 'weeklyPlans', weeklyPlansAndUnitIndex.weeklyPlans)
        state.unitsFromDesigner[weeklyPlansAndUnitIndex.unitIndex].weeklyPlans = weeklyPlansAndUnitIndex.weeklyPlans
      }
    },
    SET_FRAMEWORK_DATA (state, frameworkData) {
      state.frameworkData = frameworkData
    },
    RESET_CURRICULUM (state) {
      state.id = null
      state.curriculumName = ''
    }
  }
}

export default designer
