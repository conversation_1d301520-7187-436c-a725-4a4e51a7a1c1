import tools from '@/utils/tools'

const dashboard = {
  state: {
    // 选择的学校
    cacheSelected: {
      isAllGroup: false, // 是否为所有班级
      selectGroup: false, // 是否全选班级
      aliases: [], // 所有的评分周期
      agency: [], // 当前选择的评分周期
      alias: [], // 选中的评分周期 name
      selectAlias: '', // 选中的评分周期 displayName 翻译文字
      framework: {}, // 当前评估框架
      agencies: '', // 当前机构
      agencyId: '', // 当前评分周期id
      selectedCenters: [], // 选中的学校
      selectedCenterNames: '', // 当前显示的学校名字
      selectedCenterIds: '', // 当前显示的学校id
      centers: [{ selected: false }], // 当前机构下的所有学校
      center: {}, // 当前选择的学校
      groups: [], // 当前学校的所有班级
      group: [], // 当前学校的当前班级
      groupsLength: '', // 已选学校的所有班级数量
      selectedGroupIds: ''
    },
    // 选择的时间
    cacheDate: {
      fromDate: '',
      toDate: ''
    },
    // 选中的id
    cacheSelectedId: {
      agencyId: '',
      selectedCenterIds: '',
      selectedGroupIds: ''
    },
    // 默认选中类型
    cacheType:['Photos', 'Videos', 'Nap', 'Potty','Food','Bottle','Reminder','Diapers','Learning Media','Book','Mood','File Attachment'],
    // 图表选择数据
    echartSelect: {},
    cacheFilter: {},
    // 选择过滤小孩属性
    filterAttrs: {},
    // 图表数据
    chartStats: {
      // updateAtLocal: '2018/01/01 00:00:000',
      registerStats: { loading: true }, // 注册统计数据（家庭注册数量，孩子注册数量）
      notificationStats: { loading: true }, // 通知统计数据（通知总条数，平均通知打开率）
      activeStats: { loading: true }, // 登录统计数据(峰值，日活，月活，年活)
      chatStats: { loading: true }, // 聊天翻译统计数据（总消息数，使用翻译家庭数，翻译次数）
      eventStats: { loading: true }, // Event 统计数据(总活动数，平均回复率，回复 Yes 占比，回复 No 占比，平均参与率，总参与人数，父角色参与占比，母角色参与占比，其他角色参与占比 )
      inKindStats: { loading: true }, // In-kind 统计数据(总价值数，家庭活动总价值，家庭活动占比，校内活动总价值，校内活动占比)
      bookStats: { loading: true }, // Book 统计数据(阅读书籍总数，阅读字数总数，平均阅读级别，阅读时间总数，父角色阅读占比，母角色阅读占比，其他角色阅读占比 )
      mediaStats: { loading: true },
      engageStats: { loading: true },
      surveyStats: { loading: true },
      dllStats: { loading: true}
    },
    isFromBookDetail: false // 是否从BookDetail 跳转到Family Engagement
    // detail选择的学校
    // cacheSelectedDetail:'',
    // detail选择的时间
    // cacheDateDetail:''
  },
  mutations: {
    SET_CACHESELECTED (state, obj) {
      // 隐藏全局选择
      state.cacheSelected = obj
      // 图表选择分离
      let chartObj = {
        ...state.echartSelect,
        agencyId: obj.agencyId,
        selectedCenterIds: obj.selectedCenterIds,
        selectedGroupIds: obj.selectedGroupIds,
        selectedFilterView: obj.filterView
      }
      state.echartSelect = chartObj
      state.cacheSelectedId = { agencyId: obj.agencyId,selectedCenterIds: obj.selectedCenterIds,selectedGroupIds: obj.selectedGroupIds }
    },
    SET_FILTER_ATTRS (state, obj) {
      state.filterAttrs = obj
    },
    SET_CACHE_FILTER (state, obj) {
      state.cacheFilter = obj
    },
    SET_CACHEDATE (state, obj) {
      state.cacheDate = obj
      // 图表选择分离
      state.echartSelect = {
        ...state.echartSelect,
        fromDate: obj.fromDate,
        toDate: obj.toDate
      }
    },
    // SET_CHARTSTATS (state, obj) {
    //   state.chartStats = obj
    // },
    SET_REGISTERSTATS (state,obj) {
      state.chartStats.registerStats = {
        ...state.chartStats.registerStats,
        ...obj
      }
    },
    SET_NOTIFICATIONSTATS (state,obj) {
      state.chartStats.notificationStats = {
        ...state.chartStats.notificationStats,
        ...obj
      }
    },
    SET_ACTIVESTATS (state,obj) {
      state.chartStats.activeStats = {
        ...state.chartStats.activeStats,
        ...obj
      }
    },
    SET_CHATSTATS (state,obj) {
      state.chartStats.chatStats = {
        ...state.chartStats.chatStats,
        ...obj
      }
    },
    SET_EVENTSTATS (state,obj) {
      state.chartStats.eventStats = {
        ...state.chartStats.eventStats,
        ...obj
      }
    },
    SET_INKINDSTATS (state,obj) {
      state.chartStats.inKindStats = {
        ...state.chartStats.inKindStats,
        ...obj
      }
    },
    SET_BOOKSTATS (state,obj) {
      state.chartStats.bookStats = {
        ...state.chartStats.bookStats,
        ...obj
      }
    },
    SET_MEDIASTATS (state,obj) {
      state.chartStats.mediaStats = {
        ...state.chartStats.mediaStats,
        ...obj
      }
    },
    SET_ENGAGEMENTSTATS (state,obj) {
      state.chartStats.engageStats = {
        ...state.chartStats.engageStats,
        ...obj
      }
    },
    SET_SURVEY (state,obj) {
      state.chartStats.surveyStats = {
        ...state.chartStats.surveyStats,
        ...obj
      }
    },
    SET_DLL (state,obj) {
    state.chartStats.dllStats = {
      ...state.chartStats.dllStats,
      ...obj
    }
    },
    SET_ISBOOKDETAIL (state, obj) {
      state.isFromBookDetail = obj
    }
    // SET_CACHESELECTED_DETAIL (state, obj) {
    //   state.cacheSelectedDetail = obj
    // },
    // SET_CACHEDATE_DETAIL (state, obj) {
    //   state.cacheDateDetail = obj
    // }
  },
  actions: {
    setCacheSelectedAction ({ commit }, obj) {
      commit('SET_CACHESELECTED', obj)
      tools.localItem('cacheSelectedTemp', JSON.stringify(obj))
    },
    setFilterAttrs ({ commit }, obj) {
      commit('SET_FILTER_ATTRS', obj)
      tools.localItem('filterAttrsTemp', JSON.stringify(obj))
    },
    setCacheFilter ({ commit }, obj) {
      commit('SET_CACHE_FILTER', obj)
    },
    setCacheDateAction ({ commit }, obj) {
      commit('SET_CACHEDATE', obj)
      tools.localItem('cacheDateTemp', JSON.stringify(obj))
    },
    // setChartStatsAction ({ commit }, obj) {
    //   commit('SET_CHARTSTATS', obj)
    // },
    setRegisterAction ({ commit }, obj) {
      commit('SET_REGISTERSTATS', obj)
    },
    setNotificationAction ({ commit }, obj) {
      commit('SET_NOTIFICATIONSTATS', obj)
    },
    setActiveStatsAction ({ commit }, obj) {
      commit('SET_ACTIVESTATS', obj)
    },
    setChatStatsAction ({ commit }, obj) {
      commit('SET_CHATSTATS', obj)
    },
    setEventStatsAction ({ commit }, obj) {
      commit('SET_EVENTSTATS', obj)
    },
    setInKindStatsAction ({ commit }, obj) {
      commit('SET_INKINDSTATS', obj)
    },
    setBookStatsAction ({ commit }, obj) {
      commit('SET_BOOKSTATS', obj)
    },
    setMediaStatsAction ({ commit }, obj) {
      commit('SET_MEDIASTATS', obj)
    },
    setEngageStatsAction ({ commit }, obj) {
      commit('SET_ENGAGEMENTSTATS', obj)
    },
    setSurveyStatsAction ({ commit }, obj) {
      commit('SET_SURVEY', obj)
    },
    setDLLStatsAction ({ commit }, obj) {
      commit('SET_DLL', obj)
    },
    setIsBookDetail ({ commit }, obj) {
      commit('SET_ISBOOKDETAIL', obj)
    }

    // setCacheSelectedDetailAction ({ commit }, obj) {
    //   commit('SET_CACHESELECTED_DETAIL', obj)
    // },
    // setCacheDateDetailAction ({ commit }, obj) {
    //   commit('SET_CACHEDATE_DETAIL', obj)
    // }
  }
}

export default dashboard
