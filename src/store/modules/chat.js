const chat = {
  state: {
    isContChat: false, // 环信是否登录
    isTranslate: false, // 是否翻译
    webChatCount: 0, // 未读消息数量
    messages: {
      avatar_url: '',
      disname: ''
    },// 信息
    messagesHasMsg: false, // 信息列表显隐
    previousTime: '', // 上次等陆时间
    ishowHelpChat: false, // 是否显示帮助按钮
    push: {
      signVersion: 0,
      signVersionChildIds: [],
      healthCardVersion: 0,
      healthCardVersionChildIds: [],
      healthCheckCardVersion: 0,
      healthCheckCardVersionChildIds: []
    },
    login: null,
    chatListVersion: 0, // 聊天列表刷新
    listener: {
      newMessage: {}, // 新消息
      recallMessage: {}, // 撤回消息
      messageError: {} // 发消息失败消息
    },
    playAudio: '',
    sendMessageFailIds: [] // 发送消息失败的 ID
  },
  mutations: {
    SET_SEND_MESSAGE_FAIL_IDS (state, ids) {
      state.sendMessageFailIds = ids
    },
    SET_PLAY_AUDIO (state, playAudio) {
      state.playAudio = playAudio
    },
    RECALL_MESSAGE_LISTENER (state, recallMessage) {
      state.listener.recallMessage = recallMessage
    },
    SET_LOGIN (state, login) {
      state.login = login
    },
    NEW_MESSAGE_LISTENER (state, message) {
      state.listener.newMessage = message
    },
    MESSAGE_ERROR_LISTENER (state, error) {
      state.listener.messageError = error
    },
    ADD_CHAT_LIST_VERSION (state) {
      state.chatListVersion = state.chatListVersion + 1
    },
    ADD_SIGNVERSION (state, childIds) {
      state.push.signVersion = state.push.signVersion + 1
      state.push.signVersionChildIds = childIds ? childIds.trim().toUpperCase().split(',') : []
    },
    ADD_HEALTHCARDVERSION (state, childIds) {
      state.push.healthCardVersion = state.push.healthCardVersion + 1
      state.push.healthCardVersionChildIds = childIds ? childIds.trim().toUpperCase().split(',') : []
    },
    ADD_HEALTHCHECKCARDVERSION (state, childIds) {
      state.push.healthCheckCardVersion = state.push.healthCheckCardVersion + 1
      state.push.healthCheckCardVersionChildIds = childIds ? childIds.trim().toUpperCase().split(',') : []
    },
    SET_ISCONTCHAT (state, boole) {
      state.isContChat = boole
    },
    SET_ISTRANSLATE (state, boole) {
      state.isTranslate = boole
    },
    SET_WEBCHATCOUNT (state, boole) {
      state.webChatCount = boole
    },
    SET_MESSAGES (state, obj) {
      state.messages = obj
    },
    SET_MESSAGES_HASMSG (state, boole) {
      state.messagesHasMsg = boole
    },
    SET_PREVIOUSTIME (state, str) {
      state.previousTime = str
    },
    SET_ISHOWHELPCHAT (state, boole) {
      state.ishowHelpChat = boole
    }
  },
  actions: {
    setPlayAudio ({ commit }, playAudio) {
      commit('SET_PLAY_AUDIO', playAudio)
    },
    recallMessage ({ commit }, recallMessage) {
      commit('RECALL_MESSAGE_LISTENER', recallMessage)
    },
    setLogin ({ commit }, login) {
      commit('SET_LOGIN', login)
    },
    addNewMessage ({ commit }, message) {
      commit('NEW_MESSAGE_LISTENER', message)
    },
    sendMessageError ({ commit }, error) {
      commit('MESSAGE_ERROR_LISTENER', error)
    },
    addChatListVersion ({ commit }) {
      commit('ADD_CHAT_LIST_VERSION')
    },
    addSignVersion ({ commit }, childIds) {
      commit('ADD_SIGNVERSION', childIds)
    },
    addHealthCardVersion ({ commit }, childIds) {
      commit('ADD_HEALTHCARDVERSION', childIds)
    },
    addHealthCheckCardVersion ({ commit }, childIds) {
      commit('ADD_HEALTHCHECKCARDVERSION', childIds)
    },
    setIshowHelpChat ({ commit }, boole) {
      commit('SET_ISHOWHELPCHAT', boole)
    },
    setIsContChat ({ commit }, boole) {
      commit('SET_ISCONTCHAT', boole)
    },
    setIsTranslate ({ commit }, boole) {
      commit('SET_ISTRANSLATE', boole)
    },
    setWebChatCount ({ commit }, boole) {
      commit('SET_WEBCHATCOUNT', boole)
    },
    setMessages ({ commit }, obj) {
      commit('SET_MESSAGES', obj)
    },
    setMessagesHasMsg ({ commit }, boole) {
      commit('SET_MESSAGES_HASMSG', boole)
    },
    setPreviousTime ({ commit }, str) {
      commit('SET_PREVIOUSTIME', str)
    }
  }
}

export default chat
