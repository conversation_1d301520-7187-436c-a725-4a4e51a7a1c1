const attendance = {
  state: {
    translateResult: {}, // 翻译数据
    originalContentLangCode: '', // 当前内容的原始语言
    currentContentLangCode: '' // 当前内容的语言
  },
  mutations: {
    // 设置当前内容的原始语言
    SET_ORIGINAL_LANGUAGE (state, language) {
      state.originalContentLangCode = language
    },
    // 设置当前内容的当前语言
    SET_CURRENT_LANGUAGE (state, language) {
      state.currentContentLangCode = language
    },
    // 添加翻译
    ADD_TRANSLATE_RESULT (state, [language, key, value]) {
      if (!state.translateResult[language]) {
        state.translateResult[language] = new Map()
      }
      state.translateResult[language].set(key, value)
    },
    // 清空翻译
    CLEAR_TRANSLATE_RESULT (state, language) {
      state.translateResult[language] = new Map()
    },
    // 删除翻译
    DELETE_TRANSLATE_RESULT (state, [language, key]) {
      if (state.translateResult[language]) {
        state.translateResult[language].delete(key)
      }
    }
  },
  actions: {
    // 添加单个翻译
    addTranslateResult ({ commit }, [language, key, value]) {
      commit('ADD_TRANSLATE_RESULT', [language, key, value])
    },
    // 添加多个翻译
    addAllTranslateResult ({ commit }, [language, map]) {
      map.forEach((value, key) => {
        commit('ADD_TRANSLATE_RESULT', [language, key, value])
      })
    },
    // 清空翻译
    clearTranslateResult ({ commit }, language) {
      commit('CLEAR_TRANSLATE_RESULT', language)
    },
    // 删除翻译
    deleteTranslateResult ({ commit }, [language, key]) {
      commit('DELETE_TRANSLATE_RESULT', [language, key])
    }
  }
}

export default attendance
