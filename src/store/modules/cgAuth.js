/**
 * CG 认证相关状态管理
 */
import { useAuthApi } from '@/api/cg/auth'
import { useHealthApi } from '@/api/cg/health'
import { addEnvSuffix, EventName } from '@/constants/event'
import { setLocale } from '@/utils/i18n'
import { setPlatform } from '@/utils/setBaseUrl'
import { getCurrentUser as getCurrent } from '@/utils/common'


// 初始状态
const state = {
  user: null,
  token: null,
  hasPlugin: false, // 是否安装了插件
  showBanner: false, // 是否显示横幅
  showHeaderMask: false, // 是否显示头部遮罩
  isHandlingLogout: false // 是否正在处理登出操作
}

// getters
const getters = {
  // 获取用户信息
  getUser: state => state.user,
  // 获取认证令牌
  getToken: state => state.token,
  // 是否已登录
  isLoggedIn: state => !!state.token && !!state.user,
  // 是否安装了插件
  getHasPlugin: state => state.hasPlugin,
  // 是否显示横幅
  getShowBanner: state => state.showBanner,
  // 是否显示头部遮罩
  getShowHeaderMask: state => state.showHeaderMask,
  // 是否正在处理登出操作
  getIsHandlingLogout: state => state.isHandlingLogout
}

// actions
const actions = {
  /**
   * 初始化认证信息
   */
  async initAuthInfo({ commit, dispatch }) {
    let user = localStorage.getItem('cg_user')
    let token = ''
    const cookieRow = document.cookie.split('; ').find(row => row.startsWith('cg_session='))
    if (cookieRow) {
      token = cookieRow.split('=')[1]
    }
        
    // 可能是之前缓存的用户信息和 token，需要判断下 token 是否有效，无效则清空
    if (token) {
      // 判断 token 是否有效
      const { getCurrentUser } = useAuthApi()
      try {
        // 获取用户信息
        user = await getCurrentUser({user_id: '', email: ''})
      } catch (error) {
        // token 无效
        user = null
        token = ''
      }
    } else {
      // 没有 token 信息，则调用健康检查接口来进行 lambda 预启动
      const { healthCheck } = useHealthApi()
      try {
        await healthCheck()
      } catch (error) {
        // 健康检查失败不影响正常业务
      }
    }
    
    commit('SET_USER', user)
    commit('SET_TOKEN', token)

    // 读取插件安装状态
    const hasPlugin = localStorage.getItem('cg_has_plugin')
    if (hasPlugin === 'true') {
      commit('SET_HAS_PLUGIN', true)
    }
  },

  /**
   * 设置认证信息
   */
  async setAuthInfo({ commit, dispatch }, loginResult) {
    // 如果没有指定用户更新时间戳，则使用当前时间戳
    const sessionUpdateTime = loginResult.session_update_time || Date.now().toString()
    
    // 没有 Token 信息则清空认证信息
    if (!loginResult.access_token) {
      dispatch('clearAuth', { sessionUpdateTime, sync: loginResult.sync })
      return
    }
    
    // 存储认证信息
    commit('SET_TOKEN', loginResult.access_token)
    
    const { getCurrentUser } = useAuthApi()
    // 获取用户信息
    const userInfo = await getCurrentUser({ user_id: loginResult.user_id, email: '' })
    
    // 存储用户信息
    commit('SET_USER', { user: userInfo, sessionUpdateTime })

    // 如果要强制更新或用户是不同用户，则调用 setAuthHeader 方法
    if (loginResult.updateAuth || userInfo.email !== getCurrent().primary_email) {
      await dispatch('setAuthHeader', { code: '', token: loginResult.access_token, agent: 'CURRICULUM_PLUGIN', timezone: Intl.DateTimeFormat().resolvedOptions().timeZone })
    }

     // 向插件发送事件，更新认证信息
    if (loginResult.sync) {      
      // 当前会话更新时间
      const sessionUpdateTime = localStorage.getItem('cg_home_session_update_time')
      const sessionUpdateTimeNumber = sessionUpdateTime ? Number(sessionUpdateTime) : null
      
      const env = process.env.VUE_APP_CURRENTMODE
      const event = new CustomEvent(addEnvSuffix(EventName.UPDATE_SESSION_REQ, env), { detail: { token: loginResult.access_token, sessionUpdateTime: sessionUpdateTimeNumber } })
      window.dispatchEvent(event)
    }
    // 设置埋点配置
    // this.setAnalyticsConfig()
  },
  async setAuthHeader({ getters, dispatch }, { code, token, agent, timezone }) {
    if (!token) {
      return;
    }

    const project = 'CURRICULUM-PLUGIN';
    if (project) {
      setPlatform(project);
      await dispatch('curriculum/setIsCurriculumPlugin', project, { root: true });
    }
    setLocale('en-US');
    try {
      const tokenResponse = await dispatch('checkThirdPartyToken', { code, token, agent, timezone });
      if (!tokenResponse) {
        return;
      }
      await dispatch('saveUserInfo', { tokenResponse, agent });
      $analytics.initPostHog(getters.getUser);
      dispatch('getEventInfo', project);
    } catch (e) {
      console.log(e);
    }
  },

  getEventInfo({ dispatch }, project) {
    if (project !== 'CURRICULUM-PLUGIN') {
      return;
    }
    dispatch('getEventNotifyAction').then(() => {
      dispatch('getUserNeedGuideFeaturesAction');
    });
  },

  checkThirdPartyToken(_, { code, token, agent, timezone }) {
    return $axios.get($api.urls().checkThirdLoginToken +
      '?code=' + code +
      '&token=' + token +
      '&agent=' + agent +
      '&isLoggedIn=' + false +
      '&timezone=' + timezone || 'American/Los_Angeles'
    );
  },

  async saveUserInfo({ commit, dispatch }, { tokenResponse, type }) {
    if (tokenResponse && tokenResponse.loginResponse) {
      commit('SET_TOKEN', tokenResponse.loginResponse.token, { root: true });
      commit('SET_UID', tokenResponse.loginResponse.user_id, { root: true });
      commit('SET_CENTERID', tokenResponse.loginResponse.default_center_id, { root: true });
      await dispatch('setCurrentUserAction', tokenResponse.loginResponse, { root: true });
    }
    await dispatch('magicCurriculum/setRefreshMagicData', true, { root: true });
  },
  /**
   * 清除认证信息
   */
  clearAuth({ commit }, { sessionUpdateTime, sync } = {}) {
    commit('SET_USER', null)
    commit('SET_TOKEN', null)
    
    // 清除存储的数据
    localStorage.removeItem('cg_user')
    document.cookie = 'cg_session=; path=/;'
    // 保存当前时间戳
    localStorage.setItem('cg_home_session_update_time', sessionUpdateTime || Date.now().toString())    
    // 清空首次安装缓存
    localStorage.removeItem('cg_first_install')
    if (sync) {
      // 当前会话更新时间
      const sessionUpdateTime = localStorage.getItem('cg_home_session_update_time')
      const sessionUpdateTimeNumber = sessionUpdateTime ? Number(sessionUpdateTime) : null
      
      const env = process.env.VUE_APP_CURRENTMODE
      const event = new CustomEvent(addEnvSuffix(EventName.UPDATE_SESSION_REQ, env), { detail: { sessionUpdateTime: sessionUpdateTimeNumber } })
      window.dispatchEvent(event)
    }
  },

  /**
   * 发送安装引导事件
   */
  sendInstallGuideEvent() {
    // 获取缓存判断是否首次安装
    const firstInstall = localStorage.getItem('cg_first_install')
    // 非首次安装，不发送事件
    if (!firstInstall) {
      return
    }
    
    // 这里注释掉事件发送，需要根据 lg-web2 的事件系统实现
    // EventUtil.sendEvent('CG_SHOW_INSTALL_GUIDE_REQ', {})
    
    // 清空缓存
    localStorage.removeItem('cg_first_install')
  },
  /**
   * 获取会话更新时间戳
   */
  getSessionUpdateTime() {
    const sessionUpdateTime = localStorage.getItem('cg_home_session_update_time')
    return sessionUpdateTime ? Number(sessionUpdateTime) : null
  },

  /**
   * 会话信息是否是最新更新的
   */
  isSessionLatest(compareSessionUpdateTime) {
    // 当前会话更新时间
    const sessionUpdateTime = localStorage.getItem('cg_home_session_update_time')
    const sessionUpdateTimeNumber = sessionUpdateTime ? Number(sessionUpdateTime) : null
    // 如果没有会话更新时间，则返回 false
    if (!sessionUpdateTimeNumber || !compareSessionUpdateTime) {
      return false
    }
    // 比较会话更新时间
    return Number(sessionUpdateTimeNumber) >= Number(compareSessionUpdateTime)
  },
  /**
   * 设置是否安装了插件
   */
  setHasPlugin({ commit }, hasPlugin) {
    commit('SET_HAS_PLUGIN', hasPlugin)
    
    // 将插件安装状态保存到 localStorage
    if (hasPlugin) {
      localStorage.setItem('cg_has_plugin', 'true')
    } else {
      localStorage.removeItem('cg_has_plugin')
    }
  },

  /**
   * 设置是否显示横幅
   */
  setShowBanner({ commit }, showBanner) {
    commit('SET_SHOW_BANNER', showBanner)
  },

  /**
   * 设置是否显示头部遮罩
   */
  setShowHeaderMask({ commit }, showHeaderMask) {
    commit('SET_SHOW_HEADER_MASK', showHeaderMask)
  },

  /**
   * 设置是否正在处理登出操作
   */
  setIsHandlingLogout({ commit }, isHandling) {
    commit('SET_IS_HANDLING_LOGOUT', isHandling)
  },

  /**
   * 设置认证令牌
   */
  setToken({ commit }, token) {
    if (!token) {
      commit('SET_TOKEN', null)
      return
    }
    // 保存 token 到 cookie，30 天后过期
    const expirationDate = new Date()
    expirationDate.setDate(expirationDate.getDate() + 30)
    document.cookie = `cg_session=${token}; path=/; expires=${expirationDate.toUTCString()}`
  }
}

// mutations
const mutations = {
  SET_USER(state, payload) {
    if (!payload) {
      state.user = null
      return
    }
    
    let user = payload.user || payload
    let sessionUpdateTime = payload.sessionUpdateTime
    
    // 如果是字符串，则转换为对象
    if (typeof user === 'string') {
      user = JSON.parse(user)
    }
    
    // 保存到全局变量中
    state.user = user
    
    // 保存用户信息到 localStorage
    localStorage.setItem('cg_user', JSON.stringify(user))
    
    // 保存当前时间戳
    if (sessionUpdateTime) {
      localStorage.setItem('cg_home_session_update_time', sessionUpdateTime)
    } else {
      localStorage.setItem('cg_home_session_update_time', Date.now().toString())
    }
  },
  
  SET_TOKEN(state, token) {
    if (!token) {
      state.token = null
      return
    }
    
    state.token = token
    
    // 保存 token 到 cookie，30 天后过期
    const expirationDate = new Date()
    expirationDate.setDate(expirationDate.getDate() + 30)
    document.cookie = `cg_session=${token}; path=/; expires=${expirationDate.toUTCString()}`
  },
  
  SET_HAS_PLUGIN(state, hasPlugin) {
    state.hasPlugin = hasPlugin
  },

  SET_SHOW_BANNER(state, showBanner) {
    state.showBanner = showBanner
  },

  SET_SHOW_HEADER_MASK(state, showHeaderMask) {
    state.showHeaderMask = showHeaderMask
  },
  
  SET_IS_HANDLING_LOGOUT(state, isHandling) {
    state.isHandlingLogout = isHandling
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
} 