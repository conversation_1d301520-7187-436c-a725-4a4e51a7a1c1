import tools from '@/utils/tools'

const inkind = {
  state: {
    // 选择的学校
    centerId: '',
    // 选择学校后对应的州及汇率和班级情况
    inkindData: {},
    selectCenter: [],
    schoolYearData: {},
    selectChildrenMsg: {},
    haveIgnoreReportGroups: [], // 包含ignore报告的班级
    showInkindDatePicker: true, // 是否显示inkind时间选择框
    pendingOpen: false, // 是否显示pending红点
    selectRatifyNum: 0, // 选择的批准的数量
    ratifyTotal: 0, // 老师提交待批准的总数
    pendingTotal: 0, // 家长提交待审核的总数
    thirdTotal: 0, // 第三方待审核的总数
    ratifiedMode: 'TEACHER_APPROVE_SIGNATURE_AND_ADMIN_RATIFY', // 审核模式
    siteAdminThirdPartyApprovalOpen: true, // 园长审核第三方捐赠数据权限
    showFilterView: false, // 是否显示筛选
    inkindNewStatue: false // inkind 新功能显示
  },
  mutations: {
    SET_SHOW_FILTER_VIEW (state, obj) {
      state.showFilterView = obj
    },
    SET_RATIFY_MODE (state, mode) {
      state.ratifiedMode = mode
    },
    SET_SITE_ADMIN_THIRD_PARTY_APPROVAL_OPEN (state, open) {
      state.siteAdminThirdPartyApprovalOpen = open
    },
    SET_SELECT_RATIFY_NUM (state, num) {
      state.selectRatifyNum = num
    },
    // 设置老师提交待批准的总数
    SET_RATIFY_TOTAL (state, num) {
      state.ratifyTotal = num
    },
    // 设置家长提交待审核的总数
    SET_PENDING_TOTAL (state, num) {
      state.pendingTotal = num
    },
    // 设置第三方待审核的总数
    SET_THIRD_TOTAL (state, num) {
      state.thirdTotal = num
    },
    SET_CENTERID (state, obj) {
      state.centerId = obj
    },
    SET_INKINDDATA (state, obj) {
      state.inkindData = obj
    },
    SET_SELECTCENTER (state, obj) {
      state.selectCenter = obj
    },
    SET_SCHOOLYEAR (state, obj) {
      state.schoolYearData = obj
    },
    SET_SELECTCHILD (state, obj) {
      state.selectChildrenMsg = obj
    },
    SET_IGNOREGROUPS (state, obj) {
      state.haveIgnoreReportGroups = obj
    },
    SET_SHOWINKINDDATEPICKER (state, obj) {
      state.showInkindDatePicker = obj
    },
    SET_PENDINGOPEN (state, obj) {
      state.pendingOpen = obj
    },
    SET_INKINDNEWSTATUE (state, obj) {
      state.inkindNewStatue = obj
    }
  },
  actions: {
    setShowFilterViewAction ({ commit }, obj) {
      commit('SET_SHOW_FILTER_VIEW', obj)
    },
    setRatifyModeAction ({ commit }, mode) {
      commit('SET_RATIFY_MODE', mode)
    },
    setSiteAdminThirdPartyApprovalOpen ({ commit }, open) {
      commit('SET_SITE_ADMIN_THIRD_PARTY_APPROVAL_OPEN', open)
    },
    setSelectRatifyNumAction ({ commit }, num) {
      commit('SET_SELECT_RATIFY_NUM', num)
    },
    // 设置老师提交待批准的总数
    setRatifyTotalAction ({ commit }, num) {
      commit('SET_RATIFY_TOTAL', num)
    },
    // 设置家长提交待审核的总数
    setPendingTotalAction ({ commit }, num) {
      commit('SET_PENDING_TOTAL', num)
    },
    // 设置第三方待审核的总数
    setThirdTotalAction ({ commit }, num) {
      commit('SET_THIRD_TOTAL', num)
    },
    setCenterIdAction ({ commit }, obj) {
      commit('SET_CENTERID', obj)
    },
    setInkindDataAction ({ commit }, obj) {
      commit('SET_INKINDDATA', obj)
    },
    setSelectCenterAction ({ commit }, obj) {
      commit('SET_SELECTCENTER', obj)
      tools.localItem('selectCenter', JSON.stringify(obj))
    },
    setSchoolYear ({ commit }, obj) {
      commit('SET_SCHOOLYEAR', obj)
    },
    setSelectChildren ({ commit }, obj) {
      commit('SET_SELECTCHILD', obj)
      tools.localItem('selectChildMsg', JSON.stringify(obj))
    },
    setIgnoreGroupAction ({ commit }, obj) {
      commit('SET_IGNOREGROUPS', obj)
      tools.localItem('haveIgnoreReportGroups', JSON.stringify(obj))
    },
    setShowDatePicker ({ commit }, obj) {
      commit('SET_SHOWINKINDDATEPICKER', obj)
    },
    setPendingOpen ({ commit }, obj) {
      commit('SET_PENDINGOPEN', obj)
    },
    setInkindNewStatue ({ commit }, obj) {
      commit('SET_INKINDNEWSTATUE', obj)
    }
  }
}

export default inkind
