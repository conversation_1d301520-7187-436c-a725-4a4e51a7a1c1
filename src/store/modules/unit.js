const unit = {
  namespaced: true,
  state: {
    batchId: '', // 批量生成 ID
    batchTasks: null, // 任务列表
    mediaUploaderUploadSuccessFile: null, // 上传成功的文件
    appearNotice: false, // 是否显示通知
    maxRetryCount: 0, // 重置最大重试次数
    singleGenerate: false, // 是否单个生成
    unitAdaptGuide: false, // 是否显示 Unit 适配引导
    originUnitId: '', // 源单元 ID
    needReloadLesson: false, // 是否需要重新加载课程
    showSurvey: false, // 是否显示满意度调查问卷
    showSearchCover: true, // 是否显示搜索封面功能
    unitBaseInfo: {
      authorId: '', // 单元作者 ID
      exemplar: false, // 是否为范例
      progress: 0 // 单元进度
    } // 单元基本信息
  },
  actions: {
    setBatchId ({ commit }, batchId) {
      commit('SET_BATCH_ID', batchId)
    },
    setBatchTasks ({ commit }, tasks) {
      commit('SET_BATCH_TASKS', tasks)
    },
    setAppearNotice ({ commit }, appearNotice) {
      commit('SET_APPEAR_NOTICE', appearNotice)
    },
    setMediaUploaderUploadSuccessFile ({ commit }, file) {
      commit('SET_MEDIA_UPLOADER_UPLOAD_SUCCESS_FILE', file)
    },
    setResetMaxRetry ({ commit }, resetMaxRetry) {
      commit('SET_RESET_MAX_RETRY', resetMaxRetry)
    },
    setSingleGenerate ({ commit }, singleGenerate) {
      commit('SET_SINGLE_GENERATE', singleGenerate)
    },
    setUnitAdaptGuide ({ commit }, unitAdaptGuide) {
      commit('SET_UNIT_ADAPT_GUIDE', unitAdaptGuide)
    },
    setOriginUnitId ({ commit }, unitId) {
      commit('SET_ORIGIN_UNIT_ID', unitId)
    },
    setNeedReloadLesson ({ commit }, needReloadLesson) {
      commit('SET_NEED_RELOAD_LESSON', needReloadLesson)
    },
    setShowSurvey ({ commit }, showSurvey) {
      commit('SET_SHOW_SURVEY', showSurvey)
    },
    setShowSearchCover ({ commit }, showSearchCover) {
      commit('SET_SHOW_SEARCH_COVER', showSearchCover)
    },
    setUnitBaseInfo ({ commit }, unitBaseInfo) {
      commit('SET_UNIT_BASE_INFO', unitBaseInfo)
    }
  },
  mutations: {
    SET_BATCH_ID (state, batchId) {
      state.batchId = batchId
    },
    SET_BATCH_TASKS (state, tasks) {
      state.batchTasks = tasks
    },
    SET_MEDIA_UPLOADER_UPLOAD_SUCCESS_FILE (state, file) {
      state.mediaUploaderUploadSuccessFile = file
    },
    SET_APPEAR_NOTICE (state, appearNotice) {
      state.appearNotice = appearNotice
    },
    SET_RESET_MAX_RETRY (state, resetMaxRetry) {
      state.maxRetryCount = resetMaxRetry
    },
    SET_SINGLE_GENERATE (state, singleGenerate) {
      state.singleGenerate = singleGenerate
    },
    SET_UNIT_ADAPT_GUIDE (state, unitAdaptGuide) {
      state.unitAdaptGuide = unitAdaptGuide
    },
    SET_ORIGIN_UNIT_ID (state, unitId) {
      state.originUnitId = unitId
    },
    SET_NEED_RELOAD_LESSON(state, needReloadLesson) {
      state.needReloadLesson = needReloadLesson
    },
    SET_SHOW_SURVEY (state, showSurvey) {
      state.showSurvey = showSurvey
    },
    SET_SHOW_SEARCH_COVER (state, showSearchCover) {
      state.showSearchCover = showSearchCover
    },
    SET_UNIT_BASE_INFO (state, unitBaseInfo) {
      state.unitBaseInfo = unitBaseInfo
    }
  }
}
export default unit
