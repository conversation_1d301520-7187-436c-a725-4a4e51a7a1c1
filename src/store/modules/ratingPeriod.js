const inkind = {
  state: {
    schoolYears: {}, // 机构学年数据
    showTip: ''
  },
  mutations: {
    SET_SCHOOLYEARS (state, obj) {
      state.schoolYears = obj
    },
    SET_SHOWTIP (state, obj) {
      state.showTip = obj
    }
  },
  actions: {
    setSchoolYearsAction ({ commit }, obj) {
      commit('SET_SCHOOLYEARS', obj)
    },
    setShowTip ({ commit }, obj) {
      commit('SET_SHOWTIP', obj)
    }
  }
}

export default inkind
