const common = {
  state: {
    executeType: '',
    isChina: process.env.VUE_APP_REGION, // us、cn
    isLoading: false, // 是否加载首屏动画，true为加载，默认不加载
    unreadCount: 0, // 未读通知信息
    notifications: null, // 通知信息
    eventNotify: false, // event是否通知
    inkindNotify: false, // inkind红点提示
    inkindRatifyNotify: false, // inkind ratify 红点提示
    inkindThirdNotify: false, // inkind third 红点提示
    surveyNotify: false, // survey红点提示
    isShowAdavter: false, // 是否显示广告
    isShowIconAdavter: false,
    isShowIconAdavter0: false,
    showAside: true,
    open: null,
    tips: [],
    drdpSetting: false,
    noClassReloadingTitle: 0, // 健康卡设置班级为空时刷新标题头 防止未及时刷新可以误入出席功能
    refreshSignReview: [], // 获取健康审核数据的 Id, 用来进行是否有新消息的判断
    recipient: {}, // School Message选择的发送人
    skipType: '', // 菜单栏跳转类型，判断是从 Home 首页进入还是从侧边栏进入睡眠检查和 DLL
    dropOffNoteNewStatus: false, // Drop-off Note new 标签显示状态
    guideFeatures: null, // 引导功能
    profileOpenDropdown: false, // profile 的下拉框是否打开
    lgIsMobile: false
  },
  mutations: {
    SET_LG_IS_MOBILE (state, status) {
      state.lgIsMobile = status
    },
    // 设置profile 的下拉框的打开状态
    SET_PROFILE_OPEN_DROPDOWN (state, status) {
      state.profileOpenDropdown = status
    },
    SET_EXECUTETYPE (state, type) {
      state.executeType = type
    },
    SET_LOADING (state, flag) {
      state.isLoading = flag
    },
    SET_UNREADCOUNT (state, num) {
      state.unreadCount = num
    },
    SET_NOTIFICATIONS (state, data) {
      state.notifications = data
    },
    SET_EVENTNOTIFY (state, bool) {
      state.eventNotify = bool
    },
    // 设置 inkind 红点提示
    SET_INKIN_DNOTIFY (state, bool) {
      state.inkindNotify = bool
    },
    // 设置 inkind ratify 红点提示
    SET_IN_KIND_RATIFY_NOTIFY (state, bool) {
      state.inkindRatifyNotify = bool
    },
    // 设置 inkind third 红点提示
    SET_IN_KIND_THIRD_NOTIFY (state, bool) {
      state.inkindThirdNotify = bool
    },
    SET_SURVEYNOTIFY (state, bool) {
      state.surveyNotify = bool
    },
    //
    SET_ISSHOWADVTER (state, bool) {
      state.isShowAdavter = bool
    },
    SET_ISSHOWICONADVTER (state, bool) {
      state.isShowIconAdavter = bool
    },
    SET_ISSHOWICONADVTER0 (state, bool) {
      state.isShowIconAdavter0 = bool
    },
    SET_SHOWASIDE (state, bool) {
      state.showAside = bool
    },
    SET_DRDPSETTING (state, bool) {
      state.drdpSetting = bool
    },
    SET_OPEN (state, obj) {
      state.open = obj
    },
    SET_REFRESHSIGNREVIEW (state, array) {
      state.refreshSignReview = array
    },
    SET_RELOADINGTITLE_TITLE (state) {
      state.noClassReloadingTitle = state.noClassReloadingTitle + 1
    },
    SET_TIPS (state, array) {
      state.tips = array
    },
    SET_PASSWORD_NO_SETTING (state) {
      state.open.passwordNoSetting = false
    },
    SET_MESSAGES_RECIPIENT (state, val) {
      state.recipient = val
    },
    SET_SKIP_TYPE (state, val) {
      state.skipType = val
    },
    SET_DROP_OFF_NOTE_NEW_STATUS (state, val) {
      state.dropOffNoteNewStatus = val
    },
    SET_USER_NEED_GUIDE_FEATURES (state, val) {
      state.guideFeatures = val
    }
  },
  actions: {
    setExecuteType ({ commit }, type) {
      commit('SET_EXECUTETYPE', type)
    },
    setIsLoadingAction ({ commit }, flag) {
      commit('SET_LOADING', flag)
    },
    setRefreshSignReview ({ commit }, array) {
      commit('SET_REFRESHSIGNREVIEW', array)
    },
    getUnReadCountAction ({ commit }) {
      // return new Promise((resolve, reject) => {
      //   $axios.get($api.urls().msgTypes)
      //     .then(data => {
      //       commit('SET_UNREADCOUNT', 0)
      //       for (var i = 0; i < data.messageTypes.length; i++) {
      //         var message = data.messageTypes[i]
      //         if (message.type == 'RELEASE_NOTE') {
      //           let count = message.unreadCount
      //           if (parseInt(count) != 0) {
      //             commit('SET_UNREADCOUNT', count)
      //             resolve(true)
      //           }
      //         }
      //       }
      //       resolve(false)
      //       // if (data.messageTypes[0]) {
      //       //   let count = data.messageTypes[0].unreadCount
      //       //   if (parseInt(count) != 0) {
      //       //     commit('SET_UNREADCOUNT', count)
      //       //     resolve(true)
      //       //   }
      //       // } else {
      //       //   commit('SET_UNREADCOUNT', 0)
      //       //   resolve(false)
      //       // }
      //     }).catch(err => {
      //       reject(err)
      //     })
      //   let date = new Date()
      //   let today = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
      //   $axios.get($api.urls().point + '?isNew=true' + '&date=' + today).then(data => {
      //     localStorage.setItem('inkindStatus', JSON.stringify(data))
      //     localStorage.setItem('inkindUserStatus', JSON.stringify(data.inkindUserStatus))
      //     // 调用 SET_INKIN_DNOTIFY 方法，更新家长提交待审核的红点提示
      //     commit('SET_INKIN_DNOTIFY', data.hasPoint)
      //     // 调用 SET_IN_KIND_RATIFY_NOTIFY 方法，更新老师提交待批准的红点提示
      //     commit('SET_IN_KIND_RATIFY_NOTIFY', data.hasRatifyPoint)
      //     // 调用 SET_IN_KIND_THIRD_NOTIFY 方法，更新第三方提交的红点提示
      //     commit('SET_IN_KIND_THIRD_NOTIFY', data.hasThirdPoint)
      //     resolve(data.hasPoint)
      //   }).catch(err => {
      //     reject(err)
      //   })
      // })
    },
    // 获取 In-Kind 红点提示
    getInKindPointAction ({ commit }) {
      // return new Promise((resolve, reject) => {
      //   // 获取当前时间
      //   let date = new Date()
      //   // 获取当前时间的日期
      //   let today = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
      //   // 调用接口，获取 In-Kind 红点提示
      //   $axios.get($api.urls().point + '?isNew=true' + '&date=' + today).then(data => {
      //     // 将接口返回的数据存储到 localStorage 中
      //     localStorage.setItem('inkindStatus', JSON.stringify(data))
      //     // 将获取到 inkindUserStatus 存储到 localStorage 中
      //     localStorage.setItem('inkindUserStatus', JSON.stringify(data.inkindUserStatus))
      //     // 调用 SET_INKIN_DNOTIFY 方法，更新家长提交待审核的红点提示
      //     commit('SET_INKIN_DNOTIFY', data.hasPoint)
      //     // 调用 SET_IN_KIND_RATIFY_NOTIFY 方法，更新老师提交待批准的红点提示
      //     commit('SET_IN_KIND_RATIFY_NOTIFY', data.hasRatifyPoint)
      //     // 调用 SET_IN_KIND_THIRD_NOTIFY 方法，更新第三方提交的红点提示
      //     commit('SET_IN_KIND_THIRD_NOTIFY', data.hasThirdPoint)
      //     resolve(data.hasPoint)
      //   }).catch(err => {
      //     reject(err)
      //   })
      // })
    },
    getSurveyNotifyAction ({ commit }) {
      // return new Promise((resolve, reject) => {
      //   $axios.get($api.urls().getSurveyDot).then(data => {
      //     let bool = data.hasRedDot
      //     commit('SET_SURVEYNOTIFY', bool)
      //     resolve(bool)
      //   }).catch(err => {
      //     reject(err)
      //   })
      // })
    },
    setSurveyNotifyAction ({ commit }, data) {
      commit('SET_SURVEYNOTIFY', data)
    },
    setNotificationsAction ({ commit }, data) {
      commit('SET_NOTIFICATIONS', data)
    },
    getEventNotifyAction ({ commit }) {
      return new Promise((resolve, reject) => {
        // 获取当前机构
        let agency = sessionStorage.getItem('selectedAgency')
        let tempCurrentAgency = agency ? JSON.parse(agency) : ''
        let params = {}
        if (tempCurrentAgency && tempCurrentAgency.id && !tempCurrentAgency.self) {
          params.agencyId = tempCurrentAgency.id
        }
        $axios.get($api.urls().eventNotify, { params: params }).then(data => {
          let bool = data.notify
          commit('SET_EVENTNOTIFY', bool)
          commit('SET_OPEN', data)
          resolve(bool)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 获取用户功能引导
    getUserNeedGuideFeaturesAction ({ commit }) {
      return new Promise((resolve, reject) => {
        $axios.get($api.urls().getUserNeedGuideFeatures).then(data => {
          commit('SET_USER_NEED_GUIDE_FEATURES', data)
          resolve()
        }).catch(err => {
          reject(err)
        })
      })
    },
    getAgencyTipsAction ({ commit }) {
      // return new Promise((resolve, reject) => {
      //   $axios.get($api.urls().listAgencyTips).then(data => {
      //     let tipsList = data.tipsList
      //     commit('SET_TIPS', tipsList)
      //     resolve(tipsList)
      //   }).catch(err => {
      //     reject(err)
      //   })
      // })
    },
    setAgencyTipsAction ({ commit, state }, params) {
      return new Promise((resolve, reject) => {
        if (params.tips) {
          // 将 params.tips 按逗号分割成数组
          let paramsTips = params.tips.split(',')
          // paramsTips 和 state.tips 数组取交集,得到的数组使用逗号拼接为字符串再调用接口
          params.tips = paramsTips.filter(tip => state.tips.includes(tip)).join(',')
          if (params.tips) {
            $axios.post($api.urls().setAgencyTips, {}, { params: params }).then(data => {
              // 获取去除掉 params.tips 后的 tipsList 数组
              let tipsList = state.tips.filter(x => !paramsTips.includes(x))
              commit('SET_TIPS', tipsList)
              resolve(tipsList)
            }).catch(err => {
              reject(err)
            })
          }
        } else {
          resolve(state.tips)
        }
      })
    },
    setShowAdvterAction ({ commit }, flag) {
      commit('SET_ISSHOWADVTER', flag)
    },
    setShowIconAdvterAction ({ commit }, flag) {
      commit('SET_ISSHOWICONADVTER', flag)
    },
    setShowIconAdvterAction0 ({ commit }, flag) {
      commit('SET_ISSHOWICONADVTER0', flag)
    },
    setShowAside ({ commit }, flag) {
      commit('SET_SHOWASIDE', flag)
    },
    setDrdpSetting ({ commit }, flag) {
      commit('SET_DRDPSETTING', flag)
    },
    setReloadingTitle ({ commit }) {
      commit('SET_RELOADINGTITLE_TITLE')
    },
    setPasswordNoSetting ({ commit }) {
      commit('SET_PASSWORD_NO_SETTING')
    },
    setRecipient ({ commit }, val) {
      commit('SET_MESSAGES_RECIPIENT', val)
    },
    setSkipType ({ commit }, val) {
      commit('SET_SKIP_TYPE', val)
    },
    setDropOffNoteNewStatus ({ commit }, val) {
      commit('SET_DROP_OFF_NOTE_NEW_STATUS', val)
    }
  }
}

export default common
