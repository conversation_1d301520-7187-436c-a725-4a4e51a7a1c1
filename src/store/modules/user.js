import { getCurrentUser, setCurrentUser, setCurrentAgencies } from '@/utils/common'
import tools from '@/utils/tools'

const user = {
  state: {
    token: getCurrentUser() ? getCurrentUser().token : null,
    centerId: getCurrentUser() ? getCurrentUser().default_center_id : null,
    uid: getCurrentUser() ? getCurrentUser().user_id : null,
    currentUser: getCurrentUser(),
    currentPluginUser: {}, // 插件用户信息
    language: tools.localItem('NG_TRANSLATE_LANG_KEY'),
    currentRole: {},
    // 是否分享给 magic 用于判断是否显示分享按钮
    isSharedToMagic: false,
    viewSource: ''
  },
  mutations: {
    SET_IS_SHARED_TO_MAGIC: (state, isSharedToMagic) => {
      state.isSharedToMagic = isSharedToMagic
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_VIEW_SOURCE: (state, viewSource) => {
      state.viewSource = viewSource
    },
    SET_CURRENTUSER: (state, info) => {
      state.currentUser = info
    },
    SET_CURRENTUSER_PLUGIN: (state, info) => {
      state.currentPluginUser = info
    },
    SET_LANGUAGE: (state, lang) => {
      state.currentUser.language = lang
      state.language = lang
    },
    SET_CENTERID: (state, id) => {
      state.centerId = id
    },
    SET_UID: (state, id) => {
      state.uid = id
    },
    SET_CURRENTROLE: (state, obj) => {
      state.currentRole = obj
    }
  },
  actions: {
    // 更新设置用户信息
    setCurrentUserAction ({ commit }, user) {
      commit('SET_CURRENTUSER', user)
      // 修复登录页面已存在, 当前页面超时退出登录没有跳转登录页的问题
      commit('SET_TOKEN', user ? user.token : null)
      setCurrentUser(user)
    },
    // 用户名登录
    loginByUsernameAction ({ dispatch, commit }, userInfo) {
      const username = userInfo.username.trim()
      return new Promise((resolve, reject) => {
        $axios.post($api.urls().login,{ email: username,from: 'web',password: userInfo.password })
          .then(response => {
          // 存储token、uid、centerid
            commit('SET_TOKEN', response.token)
            commit('SET_UID', response.user_id)
            commit('SET_CENTERID', response.default_center_id)
            // 存储用户信息
            dispatch('setCurrentUserAction',response)
            // 用户是否退出
            tools.sessionItem('USER_SIGN_OUT',false)
            resolve()
          }).catch(error => {
            reject(error)
          })
      })
    },
    // 获取用户信息
    getUserInfoAction ({ dispatch,commit, state }) {
      let userInfo = state.currentUser
      return new Promise((resolve, reject) => {
        // store里面没有存储时,否则返回store数据
        if (!userInfo) {
          let cookie = getCurrentUser()
          if (!cookie || cookie.toString().trim() == '') {
            let error = {
              message: 'cookie 不存在'
            }
            reject(error)
          } else {
            try {
              // 如果有 cookie set userinfo
              let _temp = JSON.parse(cookie.toString().trim())
              dispatch('setCurrentUserAction',_temp)
              resolve(_temp)
            } catch (e) {
              // console.log(e, '第一个 try catch')
              try {
                let _temp = JSON.parse(cookie)
                dispatch('setCurrentUserAction',_temp)
                resolve(_temp)
              } catch (e) {
                // console.log(e, '第二个 try catch')
                // 如果失败返回 报错
                reject(e)
              }
            }
          }
        } else {
          resolve(userInfo)
        }
      })
    },
    // 设置用户当前语言
    setLanguageAction ({ commit, state }, lang) {
      return new Promise((resolve, reject) => {
        $axios.post($api.urls().language,{ language: lang })
          .then(response => {
          // 更新语言
            commit('SET_LANGUAGE',lang)
            // 更新cookie
            let user = state.currentUser
            user.language = lang
            setCurrentUser(user)
            resolve(response)
            // 切换语言，设置翻译开关
            tools.localItem('translateOpen', lang != 'en-US')
          }).catch(error => {
            reject(error)
          })
      })
    },
    // 当前用户功能权限
    setCurrentRoleAction ({ commit }, obj) {
      commit('SET_CURRENTROLE', obj)
    },
    // 设置来源
    setViewSourceAction ({ commit }, viewSource) {
      commit('SET_VIEW_SOURCE', viewSource)
    },
    // 设置是否分享给 magic
    setIsSharedToMagicAction ({ commit }, isSharedToMagic) {
      commit('SET_IS_SHARED_TO_MAGIC', isSharedToMagic)
    }
  }
}

export default user
