import _api from "@/api"
import { ENV_USER_ID, LESSON_DOWNLOAD_INFO_MAP, POPULAR_UNIT_LIST, UNIT_DOWNLOAD_INFO_MAP } from "./const"
import axios from '@/utils/axios'
import { getCurrentUser } from '@/utils/common'

const magicCurriculum = {
  namespaced: true,
  state: {
    // 校验 email 是否合法
    validEmail: false,
    // 是否允许发布 magic
    allowPublicMagic: false,
    // 发布的单元 id
    publishedIds: [],
    // signInfo
    signInfo: {},
    unitInfo: {},
    // 登录加载中
    loginLoading: false,
    // 是否需要刷新 magicData
    refreshMagicData: false,
    // 用户邮箱
    userEmail: '',
    // 定义用户 Id
    userId: ENV_USER_ID[process.env.VUE_APP_CURRENTMODE],
    // 要展示的 unit 信息
    popularUnit: POPULAR_UNIT_LIST,
    isMagic: false,
    isCompleteUnit: false, // 是否完成 unit
    // 存储当前高亮菜单
    magicPreviousPath: '',
    magicAllowCreateUnit: true, // 是否允许创建竞赛 unit
    // 单元相关的下载信息 map 对象, key 为 unitId, value 为下载信息 默认6个单元
    unitDownloadInfoMap: UNIT_DOWNLOAD_INFO_MAP,
    // 课程信息 map 对象, key 为 unitId, value 为课程信息 默认30个课程
    lessonDownloadInfoMap: LESSON_DOWNLOAD_INFO_MAP,
    magicCreatedUnits: [], // 已创建的单元列表
    magicAllowUnits: 3, // 已创建的单元列表
    unitUsageModel: {
      unitCount: 0, // 允许创建的单元数量
      weekCount: 4, // 允许创建的最大单元数量
      weeklyLessonCount: 0, // 每周课程数量
      weeklyCenterCount: 0, // 每周 center 数量
      udl: false, // 是否开启 UDL
      clr: false, // 是否开启 CLR
      atHomeActivity: false, // 是否开启 At Home Activity
      adaptUnit: false, // 是否开启 Adapt Unit
      teachingTipsForStandards: false, // 是否开启 Teaching Tips for Standards
      typicalBehaviorsAndObservationTips: false, // 是否开启 Typical Behaviors and Observation Tips
      downloadPdf: false, // 是否开启 Download PDF
      downloadWord: false, // 是否开启 Download Word
      exportToGoogleDrive: false, // 是否开启 Export to Google Drive
      eduprotocolsIntegration: false, // 是否开启 Eduprotocols Integration
      coverImage: false, // 是否开启封面
      invitationCount: 0 // 邀请数量
    },
    // 用户信息
    userInfo: {
      email: '',
      firstName: '',
      lastName: '',
      code: '',
      token: '',
      type: '',
      isThirdPart: false
    },
    showVideoGuide: false, // 视频引导弹窗
    checkCreateUnitLoading: true // 检查用户创建单元权限的加载状态
  },
  mutations: {
    SET_SHOW_VIDEO_GUIDE: (state, value) => {
      state.showVideoGuide = value
    },
    SET_POPULAR_UNIT: (state, popularUnit) => {
      state.popularUnit = popularUnit
    },
    SET_USER_INFO: (state, userInfo) => {
      state.userInfo = userInfo
    },
    SET_USER_INFO_THIRD_PART: (state, isThirdPart) => {
      state.userInfo.isThirdPart = isThirdPart
    },
    SET_ALLOW_MAGIC: (state, allowMagic) => {
      state.allowPublicMagic = allowMagic
    },
    SET_PUBLISHED_ID: (state, publishedIds) => {
      state.publishedIds = publishedIds
    },
    SET_UNIT_INFO(state, unitInfo) {
      state.unitInfo = unitInfo
    },
    SET_REFRESH_MAGIC_DATA: (state, refreshMagicData) => {
      state.refreshMagicData = refreshMagicData
    },
    SET_VALID_EMAIL: (state, validEmail) => {
      state.validEmail = validEmail
    },
    SET_SIGN_INFO: (state, signInfo) => {
      state.signInfo = signInfo
    },
    SET_IS_MAGIC: (state, isMagic) => {
      state.isMagic = isMagic
    },
    SET_MAGIC_PREVIOUS_PATH: (state, value) => {
      state.magicPreviousPath = value
    },
    SET_MAGIC_ALLOW_CREATE_UNIT: (state, value) => {
      state.magicAllowCreateUnit = value
    },
    SET_MAGIC_CREATED_UNITS: (state, value) => {
      state.magicCreatedUnits = value
    },
    SET_LOGIN_LOADING: (state, loginLoading) => {
      state.loginLoading = loginLoading
    },
    SET_UNIT_DOWNLOAD_INFO_MAP: (state, unitDownloadInfoMap) => {
      state.unitDownloadInfoMap = unitDownloadInfoMap
    },
    SET_LESSON_DOWNLOAD_INFO_MAP: (state, lessonDownloadInfoMap) => {
      state.lessonDownloadInfoMap = lessonDownloadInfoMap
    },
    SET_USER_EMAIL: (state, userEmail) => {
      state.userEmail = userEmail
    },
    SET_MAGIC_ALLOW_UNITS: (state, value) => {
      state.magicAllowUnits = value
    },
    SET_UNIT_USAGE_MODEL: (state, value) => {
      state.unitUsageModel = value
    },
    SET_CHECK_CREATE_UNIT_LOADING: (state, value) => {
      state.checkCreateUnitLoading = value
    },
    SET_IS_COMPLETE_UNIT: (state, value) => {
      state.isCompleteUnit = value
    }
  },
  actions: {
    // 登录注册检测视频引导是否需要展示
    checkVideoGuideStatus({ commit }) {
      let userIds = []
      let currentUserId = getCurrentUser() ? getCurrentUser().user_id : null
      try {
        userIds = JSON.parse(localStorage.getItem('videoGuideUserIds') || '[]')
      } catch (error) {
        userIds = []
      }
      if (!userIds.includes(currentUserId)) {
        commit('SET_SHOW_VIDEO_GUIDE', true)
        userIds.push(currentUserId)
        localStorage.setItem('videoGuideUserIds', JSON.stringify(userIds))
      }
    },
    setPopularUnit({commit}, popularUnit) {
      commit('SET_POPULAR_UNIT', popularUnit)
    },
    setRefreshMagicData({commit}, refreshMagicData) {
      commit('SET_REFRESH_MAGIC_DATA', refreshMagicData)
    },
    setValidEmail({commit}, validEmail) {
      commit('SET_VALID_EMAIL', validEmail)
    },
    setLoginLoading({commit}, loginLoading) {
      commit('SET_LOGIN_LOADING', loginLoading)
    },
    setSignInfo({commit}, signInfo) {
      commit('SET_SIGN_INFO', signInfo)
    },
    setIsMagic({commit}, isMagic) {
      commit('SET_IS_MAGIC', isMagic)
    },
    setUnitDownloadInfoMap({commit}, unitDownloadInfoMap) {
      commit('SET_UNIT_DOWNLOAD_INFO_MAP', unitDownloadInfoMap)
    },
    setLessonDownloadInfoMap({commit}, lessonDownloadInfoMap) {
      commit('SET_LESSON_DOWNLOAD_INFO_MAP', lessonDownload)
    },
    setUserEmail({commit}, userEmail) {
      commit('SET_USER_EMAIL', userEmail)
    },
    setUnitInfo({commit}, unitInfo) {
      commit('SET_UNIT_INFO', unitInfo)
    },
    setAllowMagic({commit}, allowMagic) {
      commit('SET_ALLOW_MAGIC', allowMagic)
    },
    setPublishedId({commit}, publishedIds) {
      commit('SET_PUBLISHED_ID', publishedIds)
    },
    /**
     * 检查当前用户是否有权限进行 Magic 操作
     */
    async checkCurrentUserToMagic({ commit }) {
      try {
        const res = await axios.get(_api.urls().checkCurrentUserToMagic)
        commit('SET_ALLOW_MAGIC', res.success)
        commit('SET_PUBLISHED_ID', res.ids || [])
      } catch (error) {
        console.log(error)
      }
    },   
    /**
     * 检查当前用户是否有权限创建单元
     */
    async checkCurrentUserCreateUnit({ commit }){
      try {
        commit('SET_CHECK_CREATE_UNIT_LOADING', true)
        const res = await axios.get(_api.urls().checkCurrentUserCreateUnit)
        commit('SET_MAGIC_ALLOW_CREATE_UNIT', !!res.success)
        commit('SET_MAGIC_CREATED_UNITS', res.ids || [])
        const unitCount = res.unitUsageModel.unitCount
        if (unitCount === 0) {
          commit('SET_MAGIC_ALLOW_UNITS', 0)
        } else {
          commit('SET_MAGIC_ALLOW_UNITS', unitCount || 3)
        }
        // 设置用户信息
        commit('SET_UNIT_USAGE_MODEL', res.unitUsageModel || {})
        return res
      } catch (error) {
        console.log(error)
        return error
      } finally {
        commit('SET_CHECK_CREATE_UNIT_LOADING', false)
      }
    },

    /**
     * 检查当前用户是否完成 unit
     */
    async checkCurrentUserCompleteUnit({ commit }) {
      const res = await axios.get(_api.urls().checkCurrentUserCompleteUnit)
      commit('SET_IS_COMPLETE_UNIT', res.success)
    },

    /**
     * 设置用户信息
     * @param commit
     * @param userInfo
     */
    setUserInfo({commit}, userInfo) {
      commit('SET_USER_INFO', userInfo)
    },
    /**
     * 设置用户是否是第三方登录
     * @param commit
     * @param isThirdPart
     */
    setUserInfoThirdPart({commit}, isThirdPart) {
      commit('SET_USER_INFO_THIRD_PART', isThirdPart)
    }
  }
}

export default magicCurriculum
