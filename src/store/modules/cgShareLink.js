/**
 * CG 分享链接相关状态管理
 */
import { useInvitationApi } from '@/api/cg/invitation'

// 初始状态
const state = {
  shareLink: '',
  invitationCount: 0
}

// getters
const getters = {
  // 获取格式化的分享链接
  formattedShareLink: state => {
    return state.shareLink ? state.shareLink.trim() : ''
  },

  // 链接是否存在
  hasLink: state => !!state.shareLink
}

// actions
const actions = {
  /**
   * 获取分享链接
   */
  async fetchShareLink({ commit, state }) {
    try {
      if (state.shareLink) {
        return state.shareLink
      }
      
      const { getInvitationLink } = useInvitationApi()
      const res = await getInvitationLink()
      
      commit('SET_SHARE_LINK', res.invite_link)
      commit('SET_INVITATION_COUNT', res.count)
      
      return state.shareLink
    } catch (error) {
      return null
    }
  },

  /**
   * 强制刷新分享链接
   */
  async refreshShareLink({ commit, dispatch }) {
    commit('SET_SHARE_LINK', '')
    return await dispatch('fetchShareLink')
  }
}

// mutations
const mutations = {
  SET_SHARE_LINK(state, link) {
    state.shareLink = link
  },
  
  SET_INVITATION_COUNT(state, count) {
    state.invitationCount = count
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
} 