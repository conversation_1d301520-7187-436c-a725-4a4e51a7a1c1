import { platform } from '@/utils/setBaseUrl'

const curriculum = {
  namespaced: true,
  state: {
    isCG: false, // 是否是 Curriculum Genie
    isMC: platform === 'MAGIC-CURRICULUM', // 是否是 Magicurriculum
    isCurriculumPlugin: true, // 是否是 Curriculum Genie 插件，默认是 true，否则会闪一下
    mcDefaultFrameworkName: 'California Preschool/Transitional Kindergarten Learning Foundations',
    prompt: {}, // 当前的 prompt
    unitPlanDragItemCount: 0, // 记录 unit plan 中的 item 更新次数, 为 0 表示当前更新结束
    lessonObjectiveType: '', // 用户选择的 objective 类型
    unit: {
      id: null,
      baseInfo: {
        domainIds: [], // 已选择的领域
        frameworkName: '',
        allDomains: [], // 所有领域选项
        showLessonTemplateTip: false, // 是否显示课程模板提示
        useLessonTemplate: false, // 是否使用课程模板
        classroomType: 'IN_PERSON' // 课堂类型：IN_PERSON 或 VIRTUAL
      },
      overview: {},
      customFoundationData: {}, // 单元自定义模块展示数据
      customFoundationInfos: {}, // 单元自定义模块完整信息
      weeklyPlans: [],
      unitOverviewPreviewDrag: false, // 全局 unitOverviewPreviewDrag 用于启用特殊情况下的拖拽预览
      unitOverviewPreviewDragPlanIds: new Set(), // 全局 unitOverviewPreviewDrag 拖拽时需要刷新的周计划 Id
      learnerProfileId: '', // 校训 ID
      rubrics: '', // 评估标准
      teachingTipsForLearnerProfile: '', // 学习者素养培养指南
    },
    generateUnitFoundationAndWeeklyThemeLoading: false, // 单元生成 foundations 和主题的 loading 状态
    isOldUnitData: false, //是否是旧数据
    currentWeeklyPlan: {}, // 当前周计划
    currentTab: '', // 当前的 tab
    customizePrompt: [], // 自定义的 prompt
    currentScene: '', // 当前的场景
    currentPromptId: '', // 当前的场景 Id
    editorInitialized: false, // 编辑器是否初始化，prompt 发生改变的时候应该发生变化
    systemTabs: ['LESSON_OVERVIEW', 'DEI_FOR_TEACHER', 'DEI_FOR_ADMINISTRATORS', 'DEI_FOR_ALL', 'LESSON_DETAIL', 'TYPICAL_BEHAVIOR', 'UNIVERSAL_DESIGN_FOR_LEARNING', 'CULTURALLY_RESPONSIVE_INSTRUCTION', 'HOME_ACTIVITY'], // 系统中所有的 tab
    promptSourcesList: [{
      steps: 1,
      title: 'Unit Foundations',
      sources: []
    },{
      steps: 2,
      title: 'Weekly Theme & Overview',
      sources: []
    },{
      steps: 3,
      title: 'Lesson Plan Ideas & Detail ',
      sources: []
    }],
    promptCustomDetailHasUpdated: false, // prompt 的自定义详情是否有更新
    unitHasUpdated: false, // 单元内容是否有更新
    domains: [],
    refreshExtFramework: false,
    frameworks: [
      {
        id: 'E163164F-BDCE-E411-AF66-02C72B94B99B',
        name: 'DRDP2015-INFANT-TODDLER Comprehensive View',
        standardName: 'DRDP measures',
      },
      {
        id: '32DF6B7B-A5A0-4B73-AF23-193175BC537C',
        name: 'DRDP2015-INFANT-TODDLER Essential View',
        standardName: 'DRDP measures',
      },
      {
        id: '********-BDCE-E411-AF66-02C72B94B99B',
        name: 'DRDP2015-PRESCHOOL Comprehensive view',
        standardName: 'DRDP measures',
      },
      {
        id: '49DA6264-E437-E611-AB42-06BC895D03FD',
        name: 'DRDP2015-Preschool Fundamental View',
        standardName: 'DRDP measures',
      },
      {
        id: '3FCFD736-4D4E-431C-A0AC-FCE0C2622B3F',
        name: 'DRDP2015-PRESCHOOL Essential View',
        standardName: 'DRDP measures',
      },
      {
        id: 'F22DE844-28E7-4924-9D5C-513E68584183',
        name: 'CA-CCSS (Common Core State Standards)',
        standardName: 'Common Core State Standards',
      },
      {
        id: '90270F89-2C82-41B0-BFC0-AA6AA0F324C3',
        name: 'Texas Prekindergarten Guidelines',
        standardName: 'Texas Prekindergarten Guidelines',
      },
      // {
      //   id: 'C9DB9398-668B-4B1D-8AD5-4F2A394A71D3',
      //   name: 'IL Preschool Rubrics',
      //   standardName: 'IL Preschool Rubrics',
      // },
      // {
      //   id: 'C325983A-80EE-4E4E-9CBE-23E9845C22E0',
      //   name: 'IL Kindergarten Rubrics',
      //   standardName: 'IL Kindergarten Rubrics',
      // },
      {
        id: 'E83EB44A-BD11-4003-B32A-79B17065A408',
        name: 'IL CCSS-K Rubrics',
        standardName: 'IL CCSS-K Rubrics',
      },
      // {
      //   id: 'B19DC9DA-66B2-4614-8957-69BA38458569',
      //   name: 'IL First Grade Rubrics',
      //   standardName: 'IL First Grade Rubrics',
      // },
      {
        id: 'CC463462-87DE-4E7F-A0DD-3E6B634FEBEC',
        name: 'HS-ELOF (Head Start Early Learning Outcomes Framework)',
        standardName: 'HS-ELOF Standards',
      },
      // {
      //   id: 'E7A73F4D-6DC7-42F8-ABB5-CFB54BB67844',
      //   name: 'California Preschool Learning Foundations',
      //   standardName: 'California Preschool Learning Foundations',
      // },
      {
        id: '67DAB346-B0F5-4496-A3EE-F0F1A1D3B894',
        name: 'Pennsylvania Learning Standards for Early Childhood',
        standardName: 'Pennsylvania Learning Standards for Early Childhood',
      },
      {
        id: '83720F24-A557-44CD-BE76-00A0A8A40DA3',
        name: 'Florida Early Learning and Developmental Standards',
        standardName: 'Florida Early Learning and Developmental Standards',
      },
      {
        id: 'A0E8F0A0-BB86-4CA3-9B5E-B40DF2B155EA',
        name: 'Ohio\'s Early Learning and Development Standards',
        standardName: 'Ohio\'s Early Learning and Development Standards',
      },
      {
        id: '0FD9A5BA-11DB-4A4F-981F-F2F9A882B058',
        name: 'Missouri Early Learning Standards',
        standardName: 'Missouri Early Learning Standards',
      },
      {
        id: 'F70B01FA-636E-4761-B0A8-15F1B05EF781',
        name: 'Illinois Early Learning and Development Standards',
        standardName: 'Illinois Early Learning and Development Standards',
      }
    ],
    // 年龄段
    grades: [
      { name: 'Grade 12', value: 'Grade 12'},
      { name: 'Grade 11', value: 'Grade 11'},
      { name: 'Grade 10', value: 'Grade 10'},
      { name: 'Grade 9', value: 'Grade 9' },
      { name: 'Grade 8', value: 'Grade 8' },
      { name: 'Grade 7', value: 'Grade 7' },
      { name: 'Grade 6', value: 'Grade 6' },
      { name: 'Grade 5', value: 'Grade 5' },
      { name: 'Grade 4', value: 'Grade 4' },
      { name: 'Grade 3', value: 'Grade 3' },
      { name: 'Grade 2', value: 'Grade 2' },
      { name: 'Grade 1', value: 'Grade 1' },
      { name: 'K (5-6)', value: 'K (5-6)' },
      { name: 'TK (4-5)', value: 'TK (4-5)' },
      { name: 'PS/PK (3-4)', value: 'PS/PK (3-4)' },
      { name: 'Toddler (2-3)', value: 'Toddler (2-3)' },
      { name: 'Young Toddler (1-2)', value: 'Young Toddler (1-2)' },
      { name: 'Infant (0-1)', value: 'Infant (0-1)' },
      { name: 'Mixed age group', value: 'Mixed age group'},
      // { name: 'Grade 3 (8-9)', value: 'Grade 3 (8-9)' },
      // { name: 'Grade 4 (9-10)', value: 'Grade 4 (9-10)' },
      // { name: 'Grade 5 (10-11)', value: 'Grade 5 (10-11)' },
      // { name: 'Grade 6 (11-12)', value: 'Grade 6 (11-12)' },
      // { name: 'Grade 7 (12-13)', value: 'Grade 7 (12-13)' },
      // { name: 'Grade 8 (13-14)', value: 'Grade 8 (13-14)' },
      // { name: 'Grade 9 (14-15)', value: 'Grade 9 (14-15)' },
      // { name: 'Grade 10 (15-16)', value: 'Grade 10 (15-16)' },
      // { name: 'Grade 11 (16-17)', value: 'Grade 11 (16-17)' },
      // { name: 'Grade 12 (17-18)', value: 'Grade 12 (17-18)' },
    ],
    // 年龄组成类型
    ageTypes: [
      { key: 'KToGrade2', val: new Set(['TK (4-5)', 'K (5-6)', 'Grade 1', 'Grade 2']) },
      { key: 'Grade3ToGrade12', val: new Set(['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']) }
    ],
    // 存储不同年龄组下的不同 level 对应的题目类型，对于两种年龄类型的 Remember Understand Apply 三种 level 对应的题目类型集合相同
    bloomQuizQuestionTypesByAgeTypeAndLevel: {
      KToGrade2: {
        Remember: [
          { value: 'FILL_IN_BLANK', label: 'lessonQuizQuestionType1', key: 'Fill in the Blank' },
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' }
        ],
        Understand: [
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' }
        ],
        Apply: [
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' }
        ],
        Analyze: [
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
          { value: 'CLOSE_READING_AND_INFERENCES', label: 'lessonQuizQuestionType9', key: 'Close Reading and Inference' }
        ],
        Evaluate: [
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' }
        ],
        Create: [
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
          { value: 'CREATIVE_CORNER', label: 'lessonQuizQuestionType14', key: 'Creative Corner' }
        ]
      },
      Grade3ToGrade12: {
        Remember: [
          { value: 'FILL_IN_BLANK', label: 'lessonQuizQuestionType1', key: 'Fill in the Blank' },
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' }
        ],
        Understand: [
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' }
        ],
        Apply: [
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' }
        ],
        Analyze: [
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
          { value: 'CLOSE_READING_AND_INFERENCES', label: 'lessonQuizQuestionType9', key: 'Close Reading and Inference' },
          { value: 'LONG_RESPONSE', label: 'lessonQuizQuestionType10', key: 'Long Response' },
          { value: 'RESEARCH_AND_REFLECT', label: 'lessonQuizQuestionType11', key: 'Research & Reflect' },
          { value: 'ESSAY_QUESTION', label: 'lessonQuizQuestionType12', key: 'Essay Question' },
          { value: 'CASE_STUDY', label: 'lessonQuizQuestionType13', key: 'Case Study' }
        ],
        Evaluate: [
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
          { value: 'LONG_RESPONSE', label: 'lessonQuizQuestionType10', key: 'Long Response' },
          { value: 'RESEARCH_AND_REFLECT', label: 'lessonQuizQuestionType11', key: 'Research & Reflect' },
          { value: 'ESSAY_QUESTION', label: 'lessonQuizQuestionType12', key: 'Essay Question' },
          { value: 'CASE_STUDY', label: 'lessonQuizQuestionType13', key: 'Case Study' }
        ],
        Create: [
          { value: 'CREATIVE_CORNER', label: 'lessonQuizQuestionType14', key: 'Creative Corner' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
          { value: 'LONG_RESPONSE', label: 'lessonQuizQuestionType10', key: 'Long Response' },
          { value: 'RESEARCH_AND_REFLECT', label: 'lessonQuizQuestionType11', key: 'Research & Reflect' },
          { value: 'ESSAY_QUESTION', label: 'lessonQuizQuestionType12', key: 'Essay Question' },
          { value: 'CASE_STUDY', label: 'lessonQuizQuestionType13', key: 'Case Study' }
        ]
      }
    },
    dokQuizQuestionTypesByAgeTypeAndLevel: {
      KToGrade2: {
        DOK1: [
          { value: 'FILL_IN_BLANK', label: 'lessonQuizQuestionType1', key: 'Fill in the Blank' },
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' }
        ],
        DOK2: [
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' }
        ],
        DOK3: [
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'CLOSE_READING_AND_INFERENCES', label: 'lessonQuizQuestionType9', key: 'Close Reading and Inference' }
        ],
        DOK4: [
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'CREATIVE_CORNER', label: 'lessonQuizQuestionType14', key: 'Creative Corner' }
        ]
      },
      Grade3ToGrade12: {
        DOK1: [
          { value: 'FILL_IN_BLANK', label: 'lessonQuizQuestionType1', key: 'Fill in the Blank' },
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' }
        ],
        DOK2: [
          { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
          { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
          { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' }
        ],
        DOK3: [
          { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
          { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
          { value: 'LONG_RESPONSE', label: 'lessonQuizQuestionType10', key: 'Long Response' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'CLOSE_READING_AND_INFERENCES', label: 'lessonQuizQuestionType9', key: 'Close Reading and Inference' },
          { value: 'CASE_STUDY', label: 'lessonQuizQuestionType13', key: 'Case Study' }
        ],
        DOK4: [
          { value: 'LONG_RESPONSE', label: 'lessonQuizQuestionType10', key: 'Long Response' },
          { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
          { value: 'ESSAY_QUESTION', label: 'lessonQuizQuestionType12', key: 'Essay Question' },
          { value: 'RESEARCH_AND_REFLECT', label: 'lessonQuizQuestionType11', key: 'Research & Reflect' },
          { value: 'CASE_STUDY', label: 'lessonQuizQuestionType13', key: 'Case Study' },
          { value: 'CREATIVE_CORNER', label: 'lessonQuizQuestionType14', key: 'Creative Corner' }
        ]
      }
    },
    // 存储不同年龄组合下的题目类型
    quizQuestionTypesByAgeType: {
      KToGrade2: [
        { value: 'FILL_IN_BLANK', label: 'lessonQuizQuestionType1', key: 'Fill in the Blank' },
        { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
        { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
        { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' },
        { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
        { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
        { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
        { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
        { value: 'CLOSE_READING_AND_INFERENCES', label: 'lessonQuizQuestionType9', key: 'Close Reading and Inference' },
        { value: 'CREATIVE_CORNER', label: 'lessonQuizQuestionType14', key: 'Creative Corner' }
      ],
      Grade3ToGrade12: [
        { value: 'FILL_IN_BLANK', label: 'lessonQuizQuestionType1', key: 'Fill in the Blank' },
        { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
        { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
        { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' },
        { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
        { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
        { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
        { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
        { value: 'CLOSE_READING_AND_INFERENCES', label: 'lessonQuizQuestionType9', key: 'Close Reading and Inference' },
        { value: 'LONG_RESPONSE', label: 'lessonQuizQuestionType10', key: 'Long Response' },
        { value: 'RESEARCH_AND_REFLECT', label: 'lessonQuizQuestionType11', key: 'Research & Reflect' },
        { value: 'ESSAY_QUESTION', label: 'lessonQuizQuestionType12', key: 'Essay Question' },
        { value: 'CASE_STUDY', label: 'lessonQuizQuestionType13', key: 'Case Study' },
        { value: 'CREATIVE_CORNER', label: 'lessonQuizQuestionType14', key: 'Creative Corner' }
      ]
    },
    // 所有的题目类型
    quizQuestionAllTypes: [
      { value: 'FILL_IN_BLANK', label: 'lessonQuizQuestionType1', key: 'Fill in the Blank' },
      { value: 'MULTIPLE_CHOICE', label: 'lessonQuizQuestionType2', key: 'Multiple Choice' },
      { value: 'MATCHING', label: 'lessonQuizQuestionType3', key: 'Matching' },
      { value: 'TRUE_OR_FALSE', label: 'lessonQuizQuestionType4', key: 'True or False' },
      { value: 'CHECKBOXES', label: 'lessonQuizQuestionType5', key: 'Checkboxes' },
      { value: 'SEQUENCING', label: 'lessonQuizQuestionType6', key: 'Sequencing' },
      { value: 'OPEN_ENDED_QUESTION', label: 'lessonQuizQuestionType7', key: 'Open-ended Question' },
      { value: 'SHORT_ANSWER', label: 'lessonQuizQuestionType8', key: 'Short Answer' },
      { value: 'CLOSE_READING_AND_INFERENCES', label: 'lessonQuizQuestionType9', key: 'Close Reading and Inference' },
      { value: 'LONG_RESPONSE', label: 'lessonQuizQuestionType10', key: 'Long Response' },
      { value: 'RESEARCH_AND_REFLECT', label: 'lessonQuizQuestionType11', key: 'Research & Reflect' },
      { value: 'ESSAY_QUESTION', label: 'lessonQuizQuestionType12', key: 'Essay Question' },
      { value: 'CASE_STUDY', label: 'lessonQuizQuestionType13', key: 'Case Study' },
      { value: 'CREATIVE_CORNER', label: 'lessonQuizQuestionType14', key: 'Creative Corner' }
    ],
    // quiz 的题目的 bloom taxonomy 类型
    quizBloomTaxonomyAllTypes: [
      { key: 'Remember', val: 'lessonQuizLevel1' },
      { key: 'Understand', val: 'lessonQuizLevel2' },
      { key: 'Apply', val: 'lessonQuizLevel3' },
      { key: 'Analyze', val: 'lessonQuizLevel4' },
      { key: 'Evaluate', val: 'lessonQuizLevel5' },
      { key: 'Create', val: 'lessonQuizLevel6' }
    ],
    quizDOKAllTypes: [
      { key: 'DOK1', val: 'lessonQuizLevelDOK1' },
      { key: 'DOK2', val: 'lessonQuizLevelDOK2' },
      { key: 'DOK3', val: 'lessonQuizLevelDOK3' },
      { key: 'DOK4', val: 'lessonQuizLevelDOK4' }
    ],
    frameworkDomains: {}, // 框架测评点信息
    frameworkMeasures: new Map(), // 框架测评点信息(包括领域，只有顶层和底层)
    frameworkMeasureAbbSet: new Set(), // 框架测评点缩写集合
    convertMappingAbbrToDomainAbbrMap: new Map(), // 负责转化特殊测评点的框架测评点信息
    gptSetting: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
    },
    planIds: [],
    // 模型列表
    models: [
      {
        name: 'GPT-3.5', value: 'gpt-3.5-turbo',
        defaultSvg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_4507_13978)">
                          <circle cx="12" cy="12" r="12" fill="#23B26B"/>
                          <path d="M18.127 11.015C18.2884 10.5397 18.3444 10.035 18.291 9.53595C18.2376 9.03688 18.0761 8.55544 17.8178 8.12509C17.4318 7.46935 16.8493 6.95158 16.1529 6.64503C15.4564 6.33848 14.6812 6.25865 13.9369 6.41685C13.5981 6.04151 13.1836 5.74216 12.7208 5.5385C12.258 5.33483 11.7573 5.23147 11.2517 5.23521C10.4922 5.23087 9.75066 5.46662 9.13306 5.90877C8.51546 6.35093 8.05333 6.9769 7.81265 7.69732C7.31699 7.79672 6.84815 8.00045 6.43728 8.29497C6.02641 8.58949 5.68291 8.96807 5.42959 9.40556C4.64324 10.7411 4.82254 12.4267 5.87289 13.5731C5.71127 14.0482 5.65509 14.5529 5.70824 15.052C5.76139 15.551 5.92259 16.0325 6.18065 16.463C6.56678 17.119 7.14946 17.6369 7.84621 17.9435C8.54296 18.2501 9.31848 18.3297 10.063 18.1712C10.4014 18.547 10.8158 18.8467 11.2787 19.0504C11.7416 19.2541 12.2425 19.3572 12.7482 19.3529C13.5084 19.3583 14.2508 19.1225 14.8685 18.6793C15.4862 18.2361 15.9475 17.6084 16.1858 16.8865C16.6815 16.7871 17.1503 16.5834 17.5612 16.2889C17.9721 15.9943 18.3156 15.6158 18.5689 15.1783C19.3609 13.8427 19.1774 12.1613 18.127 11.0136V11.015ZM12.7524 18.431C12.127 18.431 11.5185 18.215 11.0357 17.8169C11.0583 17.8041 11.0936 17.7858 11.1204 17.7689L13.9679 16.1453C14.0398 16.1063 14.0995 16.0482 14.1406 15.9775C14.1817 15.9068 14.2025 15.8261 14.2009 15.7444V11.7816L15.4037 12.4705C15.4178 12.4747 15.4263 12.4874 15.4263 12.5016V15.7839C15.4263 17.2437 14.2277 18.4267 12.7524 18.431ZM6.98959 16.0041C6.67579 15.4698 6.56173 14.8415 6.66771 14.231C6.68889 14.2451 6.72418 14.2663 6.75242 14.279L9.59995 15.9025C9.74395 15.9872 9.92183 15.9872 10.0701 15.9025L13.5487 13.9218V15.294C13.5487 15.3081 13.5444 15.3209 13.5303 15.3293L10.6503 16.9698C10.035 17.3194 9.30726 17.4141 8.62299 17.2336C7.93872 17.0531 7.35238 16.6118 6.98959 16.0041ZM6.23854 9.86721C6.55054 9.32932 7.04324 8.9185 7.63336 8.70674V12.054C7.63336 12.2164 7.7223 12.3703 7.86489 12.4536L11.3435 14.4357L10.1407 15.119C10.1349 15.1227 10.1283 15.1251 10.1214 15.1258C10.1145 15.1266 10.1076 15.1257 10.1011 15.1232L7.22112 13.4827C6.91765 13.3116 6.65119 13.0819 6.43719 12.8069C6.2232 12.5319 6.06594 12.2172 5.97453 11.881C5.88312 11.5448 5.85939 11.1938 5.90471 10.8483C5.95003 10.5029 6.0635 10.1699 6.23854 9.86862V9.86721ZM16.1322 12.1401L12.6536 10.158L13.8564 9.4705C13.8624 9.46649 13.8692 9.46401 13.8764 9.46327C13.8835 9.46253 13.8907 9.46356 13.8974 9.46626L16.7774 11.1067C17.0812 11.2776 17.3479 11.5072 17.562 11.7822C17.7762 12.0573 17.9333 12.3722 18.0244 12.7086C18.1155 13.0451 18.1386 13.3963 18.0925 13.7418C18.0464 14.0873 17.9319 14.4201 17.7557 14.7209C17.4397 15.258 16.9465 15.6683 16.3609 15.8813V12.534C16.3632 12.4534 16.343 12.3737 16.3026 12.3039C16.2622 12.2341 16.2032 12.1769 16.1322 12.1387V12.1401ZM17.3322 10.3571C17.3082 10.343 17.2729 10.3218 17.2461 10.3091L14.3971 8.68556C14.3258 8.64456 14.245 8.62298 14.1628 8.62298C14.0805 8.62298 13.9997 8.64456 13.9284 8.68556L10.4498 10.6663V9.29403C10.4498 9.27991 10.4541 9.26721 10.4682 9.25873L13.3482 7.61826C13.9637 7.26791 14.6922 7.17324 15.3769 7.35463C16.0615 7.53602 16.6476 7.97898 17.0089 8.58815C17.3223 9.12179 17.4381 9.74862 17.3308 10.3571H17.3322ZM9.79759 12.8023L8.59477 12.119C8.58831 12.1167 8.58268 12.1125 8.57865 12.107C8.57462 12.1014 8.57236 12.0948 8.57218 12.0879V8.80415C8.57218 7.34438 9.77501 6.15709 11.2545 6.16132C11.8795 6.16029 12.4852 6.3775 12.967 6.77544C12.9444 6.78815 12.9091 6.8065 12.8823 6.82344L10.0334 8.44697C9.96135 8.48572 9.90156 8.54375 9.86067 8.61457C9.81978 8.68538 9.7994 8.76618 9.80183 8.84791L9.79759 12.8009V12.8023ZM10.4498 11.4131L11.9999 10.5293L13.5515 11.4117V13.1764L12.0042 14.0587L10.4527 13.1764L10.4484 11.4117L10.4498 11.4131Z" fill="white"/>
                          </g>
                          <defs>
                          <clipPath id="clip0_4507_13978">
                          <rect width="24" height="24" fill="white"/>
                          </clipPath>
                          </defs>
                          </svg>
                        `
      },
      {
        name: 'GPT-4o', value: 'gpt-4o-2024-05-13',
        defaultSvg: `
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_4507_13975)">
          <circle cx="12" cy="12" r="12" fill="#BB6BD9"/>
          <path d="M18.127 11.015C18.2884 10.5397 18.3444 10.035 18.291 9.53595C18.2376 9.03688 18.0761 8.55544 17.8178 8.12509C17.4318 7.46935 16.8493 6.95158 16.1529 6.64503C15.4564 6.33848 14.6812 6.25865 13.9369 6.41685C13.5981 6.04151 13.1836 5.74216 12.7208 5.5385C12.258 5.33483 11.7573 5.23147 11.2517 5.23521C10.4922 5.23087 9.75066 5.46662 9.13306 5.90877C8.51546 6.35093 8.05333 6.9769 7.81265 7.69732C7.31699 7.79672 6.84815 8.00045 6.43728 8.29497C6.02641 8.58949 5.68291 8.96807 5.42959 9.40556C4.64324 10.7411 4.82254 12.4267 5.87289 13.5731C5.71127 14.0482 5.65509 14.5529 5.70824 15.052C5.76139 15.551 5.92259 16.0325 6.18065 16.463C6.56678 17.119 7.14946 17.6369 7.84621 17.9435C8.54296 18.2501 9.31848 18.3297 10.063 18.1712C10.4014 18.547 10.8158 18.8467 11.2787 19.0504C11.7416 19.2541 12.2425 19.3572 12.7482 19.3529C13.5084 19.3583 14.2508 19.1225 14.8685 18.6793C15.4862 18.2361 15.9475 17.6084 16.1858 16.8865C16.6815 16.7871 17.1503 16.5834 17.5612 16.2889C17.9721 15.9943 18.3156 15.6158 18.5689 15.1783C19.3609 13.8427 19.1774 12.1613 18.127 11.0136V11.015ZM12.7524 18.431C12.127 18.431 11.5185 18.215 11.0357 17.8169C11.0583 17.8041 11.0936 17.7858 11.1204 17.7689L13.9679 16.1453C14.0398 16.1063 14.0995 16.0482 14.1406 15.9775C14.1817 15.9068 14.2025 15.8261 14.2009 15.7444V11.7816L15.4037 12.4705C15.4178 12.4747 15.4263 12.4874 15.4263 12.5016V15.7839C15.4263 17.2437 14.2277 18.4267 12.7524 18.431ZM6.98959 16.0041C6.67579 15.4698 6.56173 14.8415 6.66771 14.231C6.68889 14.2451 6.72418 14.2663 6.75242 14.279L9.59995 15.9025C9.74395 15.9872 9.92183 15.9872 10.0701 15.9025L13.5487 13.9218V15.294C13.5487 15.3081 13.5444 15.3209 13.5303 15.3293L10.6503 16.9698C10.035 17.3194 9.30726 17.4141 8.62299 17.2336C7.93872 17.0531 7.35238 16.6118 6.98959 16.0041ZM6.23854 9.86721C6.55054 9.32932 7.04324 8.9185 7.63336 8.70674V12.054C7.63336 12.2164 7.7223 12.3703 7.86489 12.4536L11.3435 14.4357L10.1407 15.119C10.1349 15.1227 10.1283 15.1251 10.1214 15.1258C10.1145 15.1266 10.1076 15.1257 10.1011 15.1232L7.22112 13.4827C6.91765 13.3116 6.65119 13.0819 6.43719 12.8069C6.2232 12.5319 6.06594 12.2172 5.97453 11.881C5.88312 11.5448 5.85939 11.1938 5.90471 10.8483C5.95003 10.5029 6.0635 10.1699 6.23854 9.86862V9.86721ZM16.1322 12.1401L12.6536 10.158L13.8564 9.4705C13.8624 9.46649 13.8692 9.46401 13.8764 9.46327C13.8835 9.46253 13.8907 9.46356 13.8974 9.46626L16.7774 11.1067C17.0812 11.2776 17.3479 11.5072 17.562 11.7822C17.7762 12.0573 17.9333 12.3722 18.0244 12.7086C18.1155 13.0451 18.1386 13.3963 18.0925 13.7418C18.0464 14.0873 17.9319 14.4201 17.7557 14.7209C17.4397 15.258 16.9465 15.6683 16.3609 15.8813V12.534C16.3632 12.4534 16.343 12.3737 16.3026 12.3039C16.2622 12.2341 16.2032 12.1769 16.1322 12.1387V12.1401ZM17.3322 10.3571C17.3082 10.343 17.2729 10.3218 17.2461 10.3091L14.3971 8.68556C14.3258 8.64456 14.245 8.62298 14.1628 8.62298C14.0805 8.62298 13.9997 8.64456 13.9284 8.68556L10.4498 10.6663V9.29403C10.4498 9.27991 10.4541 9.26721 10.4682 9.25873L13.3482 7.61826C13.9637 7.26791 14.6922 7.17324 15.3769 7.35463C16.0615 7.53602 16.6476 7.97898 17.0089 8.58815C17.3223 9.12179 17.4381 9.74862 17.3308 10.3571H17.3322ZM9.79759 12.8023L8.59477 12.119C8.58831 12.1167 8.58268 12.1125 8.57865 12.107C8.57462 12.1014 8.57236 12.0948 8.57218 12.0879V8.80415C8.57218 7.34438 9.77501 6.15709 11.2545 6.16132C11.8795 6.16029 12.4852 6.3775 12.967 6.77544C12.9444 6.78815 12.9091 6.8065 12.8823 6.82344L10.0334 8.44697C9.96135 8.48572 9.90156 8.54375 9.86067 8.61457C9.81978 8.68538 9.7994 8.76618 9.80183 8.84791L9.79759 12.8009V12.8023ZM10.4498 11.4131L11.9999 10.5293L13.5515 11.4117V13.1764L12.0042 14.0587L10.4527 13.1764L10.4484 11.4117L10.4498 11.4131Z" fill="white"/>
          </g>
          <defs>
          <clipPath id="clip0_4507_13975">
          <rect width="24" height="24" fill="white"/>
          </clipPath>
          </defs>
          </svg>

      `
      },
      {
        name: 'GPT-4', value: 'gpt-4-1106-preview',
        defaultSvg: `
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_4507_13981)">
            <circle cx="12" cy="12" r="12" fill="#141414"/>
            <path d="M18.127 11.015C18.2884 10.5397 18.3444 10.035 18.291 9.53595C18.2376 9.03688 18.0761 8.55544 17.8178 8.12509C17.4318 7.46935 16.8493 6.95158 16.1529 6.64503C15.4564 6.33848 14.6812 6.25865 13.9369 6.41685C13.5981 6.04151 13.1836 5.74216 12.7208 5.5385C12.258 5.33483 11.7573 5.23147 11.2517 5.23521C10.4922 5.23087 9.75066 5.46662 9.13306 5.90877C8.51546 6.35093 8.05333 6.9769 7.81265 7.69732C7.31699 7.79672 6.84815 8.00045 6.43728 8.29497C6.02641 8.58949 5.68291 8.96807 5.42959 9.40556C4.64324 10.7411 4.82254 12.4267 5.87289 13.5731C5.71127 14.0482 5.65509 14.5529 5.70824 15.052C5.76139 15.551 5.92259 16.0325 6.18065 16.463C6.56678 17.119 7.14946 17.6369 7.84621 17.9435C8.54296 18.2501 9.31848 18.3297 10.063 18.1712C10.4014 18.547 10.8158 18.8467 11.2787 19.0504C11.7416 19.2541 12.2425 19.3572 12.7482 19.3529C13.5084 19.3583 14.2508 19.1225 14.8685 18.6793C15.4862 18.2361 15.9475 17.6084 16.1858 16.8865C16.6815 16.7871 17.1503 16.5834 17.5612 16.2889C17.9721 15.9943 18.3156 15.6158 18.5689 15.1783C19.3609 13.8427 19.1774 12.1613 18.127 11.0136V11.015ZM12.7524 18.431C12.127 18.431 11.5185 18.215 11.0357 17.8169C11.0583 17.8041 11.0936 17.7858 11.1204 17.7689L13.9679 16.1453C14.0398 16.1063 14.0995 16.0482 14.1406 15.9775C14.1817 15.9068 14.2025 15.8261 14.2009 15.7444V11.7816L15.4037 12.4705C15.4178 12.4747 15.4263 12.4874 15.4263 12.5016V15.7839C15.4263 17.2437 14.2277 18.4267 12.7524 18.431ZM6.98959 16.0041C6.67579 15.4698 6.56173 14.8415 6.66771 14.231C6.68889 14.2451 6.72418 14.2663 6.75242 14.279L9.59995 15.9025C9.74395 15.9872 9.92183 15.9872 10.0701 15.9025L13.5487 13.9218V15.294C13.5487 15.3081 13.5444 15.3209 13.5303 15.3293L10.6503 16.9698C10.035 17.3194 9.30726 17.4141 8.62299 17.2336C7.93872 17.0531 7.35238 16.6118 6.98959 16.0041ZM6.23854 9.86721C6.55054 9.32932 7.04324 8.9185 7.63336 8.70674V12.054C7.63336 12.2164 7.7223 12.3703 7.86489 12.4536L11.3435 14.4357L10.1407 15.119C10.1349 15.1227 10.1283 15.1251 10.1214 15.1258C10.1145 15.1266 10.1076 15.1257 10.1011 15.1232L7.22112 13.4827C6.91765 13.3116 6.65119 13.0819 6.43719 12.8069C6.2232 12.5319 6.06594 12.2172 5.97453 11.881C5.88312 11.5448 5.85939 11.1938 5.90471 10.8483C5.95003 10.5029 6.0635 10.1699 6.23854 9.86862V9.86721ZM16.1322 12.1401L12.6536 10.158L13.8564 9.4705C13.8624 9.46649 13.8692 9.46401 13.8764 9.46327C13.8835 9.46253 13.8907 9.46356 13.8974 9.46626L16.7774 11.1067C17.0812 11.2776 17.3479 11.5072 17.562 11.7822C17.7762 12.0573 17.9333 12.3722 18.0244 12.7086C18.1155 13.0451 18.1386 13.3963 18.0925 13.7418C18.0464 14.0873 17.9319 14.4201 17.7557 14.7209C17.4397 15.258 16.9465 15.6683 16.3609 15.8813V12.534C16.3632 12.4534 16.343 12.3737 16.3026 12.3039C16.2622 12.2341 16.2032 12.1769 16.1322 12.1387V12.1401ZM17.3322 10.3571C17.3082 10.343 17.2729 10.3218 17.2461 10.3091L14.3971 8.68556C14.3258 8.64456 14.245 8.62298 14.1628 8.62298C14.0805 8.62298 13.9997 8.64456 13.9284 8.68556L10.4498 10.6663V9.29403C10.4498 9.27991 10.4541 9.26721 10.4682 9.25873L13.3482 7.61826C13.9637 7.26791 14.6922 7.17324 15.3769 7.35463C16.0615 7.53602 16.6476 7.97898 17.0089 8.58815C17.3223 9.12179 17.4381 9.74862 17.3308 10.3571H17.3322ZM9.79759 12.8023L8.59477 12.119C8.58831 12.1167 8.58268 12.1125 8.57865 12.107C8.57462 12.1014 8.57236 12.0948 8.57218 12.0879V8.80415C8.57218 7.34438 9.77501 6.15709 11.2545 6.16132C11.8795 6.16029 12.4852 6.3775 12.967 6.77544C12.9444 6.78815 12.9091 6.8065 12.8823 6.82344L10.0334 8.44697C9.96135 8.48572 9.90156 8.54375 9.86067 8.61457C9.81978 8.68538 9.7994 8.76618 9.80183 8.84791L9.79759 12.8009V12.8023ZM10.4498 11.4131L11.9999 10.5293L13.5515 11.4117V13.1764L12.0042 14.0587L10.4527 13.1764L10.4484 11.4117L10.4498 11.4131Z" fill="white"/>
            </g>
            <defs>
            <clipPath id="clip0_4507_13981">
            <rect width="24" height="24" fill="white"/>
            </clipPath>
            </defs>
            </svg>
      `
      },
    ],
    // 当前模型
    currentModel: { name: 'GPT-3.5', value: 'gpt-3.5-turbo' },
    // 定义 prompt 的扩展参数
    promptExtensionParams: {},
    // 评分标签颜色
    ratingTagColorCodes: { // 评分标签颜色
      'discovering language': '#e4e7ee',
      'discovering english': '#cbd0df',
      'exploring english': '#b5bdd3',
      'developing english': '#a0adc9',
      'building english': '#8d9dbf',
      'integrating english': '#7a8eb6',
      'discovering spanish': '#cbd0df',
      'exploring spanish': '#b5bdd3',
      'developing spanish': '#a0adc9',
      'building spanish': '#8d9dbf',
      'integrating spanish': '#7a8eb6',
      'integrating earlier': '#e7e1ef',
      'integrating middle': '#ccc0dc',
      'integrating later': '#b6aac7',
      'building earlier': '#dce5ee',
      'building middle': '#b9ccdd',
      'building later': '#8baac7',
      'exploring earlier': '#dceedf',
      'exploring middle': '#b9dec1',
      'exploring later': '#8bc797',
      'responding earlier': '#f9e5bb',
      'responding later': '#f6d38f',
    },
    promptDebugging: false, // 是否开启提示调试
    promptManagement: false, // 是否开启提示管理
    showSmallAndLargeGroupFlag: true, // 是否显示小组和大组活动
    defaultTestUnitId: null, // 默认测试单元 ID
    // 下面是 labelObservation 中的数据
    // 观察记录内容, measureLevelRelations 中的 measure 和 ratingMeasureTableData 同步,若后续修改了 ratingMeasureTableData ,记得同步
    observationCopilotRequest: {
      frameworkId: '********-BDCE-E411-AF66-02C72B94B99B',
      observations: [{ observation: '', selectedMeasures: [], measureLevelRelations: [] }],
      ageGroup: '',
    },
    labelObservationLoading: false, // 生成测评点 Loading
    ratingObservationLoading: false, // 生成评分 loading
    hasInputObservation: [], // 记录下来的 observation 的值
    // 当做解析的容器
    labelResult: [], // 生成测评点结果
    samplesDialogVisible: false,
    // 显示的数据
    ratingResult: [],
    parseResults: [], // 解析出来的结果
    parseRatingResult: [],
    demoObservations: [
      // 示例观察记录
      'Greyson washes his hands and tries to open food and is able to feed himself using a spoon.',
      'During the outside play time, children played ball with the teacher, they ran around kicking the ball to each other',
    ],
    selectedMeasures: [],
    measures: [],
    tableData: [],
    ratingMeasureTableData: [],
    measureConsistencyTooltip:
      'Total number of measures consistent with teacher assignment / Total number of system-suggested measures',
    rules: {},
    loadFrameworkLoading: false,
    currentObservationIndex: 0,
    needClear: true,
    isGenieCurriculumToUnitPlanThird: false,
    bloomQuizSetting: null, // bloom 测评设置
    stationsSetting: {}, // station 变化设置
    centersSetting: {}, // center 变化设置
    analyzeUnitContent: null // 导入单元信息结果
  },
  mutations: {
    SET_UNIT (state, unit) {
      state.unit = unit
    },
    // 设置单元 ID
    SET_UNIT_ID (state, unitId) {
      state.unit.id = unitId
    },
    // 设置单元基本信息
    SET_BAE_INFO (state, baseInfo) {
      state.unit.baseInfo = baseInfo
    },
    // 设置是否为 genie 生成的 curriculum 进入的 Unit Plan 第三步
    SET_GENIE_CURRICULUM_TO_UNIT_PLAN_THIRD (state, status) {
      state.isGenieCurriculumToUnitPlanThird = status
    },
    SET_OPTIONS (state, allDomains) {
      state.unit.baseInfo.allDomains  = allDomains
    },
    // 设置单元基本信息
    SET_DOMAIN_IDS (state, domainIds) {
      state.unit.baseInfo.domainIds = domainIds
    },
    // 设置框架名称
    SET_FRAMEWORK_NAME (state, frameworkName) {
      state.unit.baseInfo.frameworkName = frameworkName
    },
    // 设置单元概览
    SET_OVERVIEW (state, overview) {
      state.unit.overview = overview
    },
    // 设置自定义 Foundation 数据
    SET_CUSTOM_FOUNDATION_DATA (state, customFoundationData) {
      state.unit.customFoundationData = customFoundationData
    },
    // 设置自定义 Foundation 信息
    SET_CUSTOM_FOUNDATION_INFO (state, customFoundationInfos) {
      state.unit.customFoundationInfos = customFoundationInfos
    },
    // 设置单元封面
    SET_UNIT_COVER (state, coverMedias) {
      state.unit.overview.coverMedias = coverMedias
    },
    // 设置全局 unitOverviewPreviewDrag 用于启用特殊情况下的拖拽预览
    SET_UNIT_OVERVIEW_PREVIEW_DRAG (state, unitOverviewPreviewDrag) {
      state.unit.unitOverviewPreviewDrag = unitOverviewPreviewDrag
    },
    // 设置 unitOverviewPreviewDragPlanIds
    SET_UNIT_OVERVIEW_PREVIEW_DRAG_PLAN_IDS (state, id) {
      if (!state.unit.unitOverviewPreviewDragPlanIds) {
        let set = new Set()
        set.add(id)
        state.unit.unitOverviewPreviewDragPlanIds = set
      } else {
        state.unit.unitOverviewPreviewDragPlanIds.add(id)
      }
    },
    // 清空 unitOverviewPreviewDragPlanIds
    CLEAR_UNIT_OVERVIEW_PREVIEW_DRAG_PLAN_IDS (state) {
      if (state.unit.unitOverviewPreviewDragPlanIds) {
        state.unit.unitOverviewPreviewDragPlanIds.clear()
      }
    },
    // unitPlanDragItemCount ++
    INC_UNIT_PLAN_DRAG_ITEM_COUNT (state) {
      if (!state.unitPlanDragItemCount) {
        state.unitPlanDragItemCount = 1
      } else {
        state.unitPlanDragItemCount = state.unitPlanDragItemCount + 1
      }
    },
    // unitPlanDragItemCount --
    DEC_UNIT_PLAN_DRAG_ITEM_COUNT (state) {
      if (state.unitPlanDragItemCount) {
        state.unitPlanDragItemCount = state.unitPlanDragItemCount - 1
      }
    },
    // 清空 unitPlanDragItemCount
    CLEAR_UNIT_PLAN_DRAG_ITEM_COUNT (state) {
      state.unitPlanDragItemCount = 0
    },
    SET_IS_OLD_UNIT(state,value) {
      state.isOldUnitData = value
    },
    // 设置周计划列表
    SET_WEEKLY_PLANS (state, weeklyPlans) {
      state.unit.weeklyPlans = [...weeklyPlans]
    },
    // 设置周计划ID列表
    SET_WEEKLY_PLANIDS (state, planIds) {
      state.planIds = planIds
    },
    // 设置 DEI Best Practice
    SET_DEI_BEST_PRACTICE (state, deiBestPractice) {
      state.unit.deiBestPractice = deiBestPractice
    },
    // 设置 TEACHING_TIPS 学习者素养培养指南
    SET_TEACHING_TIPS_FOR_LEARNER_PROFILE (state, data) {
      state.unit.teachingTipsForLearnerProfile = data
    },
    // 更新周计划
    UPDATE_WEEKLY_PLAN (state, weeklyPlan) {
      let weeklyPlans = state.unit.weeklyPlans
      let index = weeklyPlan.editIndex
      if (!index) {
        return
      }
      weeklyPlans.splice(index, 1, weeklyPlan)
    },
    // 设置领域/测评点
    SET_DOMAINS (state, domains) {
      state.domains = domains
    },
    SET_REFRESH_EXT_FRAMEWORK (state, refreshExtFramework) {
      state.refreshExtFramework = refreshExtFramework
    },
    // 设置框架领域/测评点
    SET_FRAMEWORK_DOMAINS (state, framework) {
      // 使用 cacheKey 作为缓存键（如果提供了），否则使用 frameworkId
      const cacheKey = framework.cacheKey || framework.frameworkId
      state.frameworkDomains[cacheKey] = framework.domains
    },
    // 设置框架领域/测评点
    SET_FRAMEWORK_DOMAINS_TOP_BOTTOM (state, framework) {
      state.frameworkMeasures.set(framework.frameworkId + framework.unitId,framework.domains)
    },
     // 设置框架领域/测评点缩写信息
     SET_FRAMEWORK_DOMAINS_TOP_BOTTOM_ABB (state, abbSet) {
      state.frameworkMeasureAbbSet = abbSet
    },
    // 设置框架测评点映射
    SET_CONVERT_MAPPING_ABBR_TO_DOMAIN_ABBR_MAP (state, convertMappingAbbrToDomainAbbr) {
      state.convertMappingAbbrToDomainAbbrMap = convertMappingAbbrToDomainAbbr
    },
    // 重置单元内容
    RESET_UNIT (state) {
      state.unit = {
        id: null,
        baseInfo: {
          domainIds: [], // 已选择的领域
          frameworkName: '',
          allDomains: [], // 所有领域选项
          showLessonTemplateTip: false, // 是否显示课程模板提示
          useLessonTemplate: false, // 是否使用课程模板
          classroomType: 'IN_PERSON' // 课堂类型：IN_PERSON 或 VIRTUAL
        },
        overview: {},
        customFoundationData: {},
        weeklyPlans: []
      }
    },
    SET_EDITOR_INITIALIZED (state, initialized) {
      state.editorInitialized = initialized
    },
    // 设置 GPT 设置参数
    SET_GPT_SETTING (state, gptSetting) {
      if (gptSetting.temperature) {
        gptSetting.temperature = parseFloat(gptSetting.temperature)
      }
      console.log(gptSetting)
      state.gptSetting = gptSetting
    },
    // 设置模型
    SET_CURRENT_MODEL (state, model) {
      state.currentModel = model
    },
    // 设置 prompt 扩展参数
    SET_PROMPT_EXTENSION_PARAMS (state, params) {
        state.promptExtensionParams = params
    },
    SET_PROMPT_DEBUGGING (state, promptDebugging) {
      state.promptDebugging = promptDebugging
    },
    SET_PROMPT_MANAGEMENT (state, promptManagement) {
      state.promptManagement = promptManagement
    },
    SET_SHOW_SMALL_AND_LARGE_GROUP_FLAG (state, showSmallAndLargeGroupFlag) {
      state.showSmallAndLargeGroupFlag = showSmallAndLargeGroupFlag
    },
    SET_DEFAULT_TEST_UNIT_ID (state, unitId) {
      state.defaultTestUnitId = unitId
    },
    // 设置 observationCopilotRequest 属性
    SET_OBSERVATION_COPILOT_REQUEST (state, observationCopilotRequest) {
      state.observationCopilotRequest = observationCopilotRequest
    },
    // 设置frameworkId
    SET_FRAMEWORK_ID (state, frameworkId) {
      state.observationCopilotRequest.frameworkId = frameworkId
    },

    // 设置observations
    SET_OBSERVATIONS (state, observations) {
      state.observationCopilotRequest.observations = observations
    },

    // 设置ageGroup
    SET_AGE_GROUP (state, ageGroup) {
      state.observationCopilotRequest.ageGroup = ageGroup
    },

    // 设置 labelObservationLoading 属性
    SET_LABEL_OBSERVATION_LOADING (state, loading) {
      state.labelObservationLoading = loading
    },
    // 设置 ratingObservationLoading 属性
    SET_RATING_OBSERVATION_LOADING (state, loading) {
      state.ratingObservationLoading = loading
    },
    // 设置 hasInputObservation 属性
    SET_HAS_INPUT_OBSERVATION (state, observation) {
      state.hasInputObservation = observation
    },
    // 设置 labelResult 属性
    SET_LABEL_RESULT (state, result) {
      state.labelResult = result
    },
    // 设置 samplesDialogVisible 属性
    SET_SAMPLES_DIALOG_VISIBLE (state, visible) {
      state.samplesDialogVisible = visible
    },
    // 设置 ratingResult 属性
    SET_RATING_RESULT (state, result) {
      state.ratingResult = result
    },
    // 设置 parseResults 属性
    SET_PARSE_RESULTS (state, results) {
      state.parseResults = results
    },
    // 设置 parseRatingResult 属性
    SET_PARSE_RATING_RESULT (state, result) {
      state.parseRatingResult = result
    },
    // 设置 selectedMeasures 属性
    SET_SELECTED_MEASURES (state, measures) {
      state.selectedMeasures = measures
    },
    // 设置 measures 属性
    SET_MEASURES (state, measures) {
      state.measures = measures
    },
    // 设置 tableData 属性
    SET_TABLE_DATA (state, data) {
      state.tableData = data
    },
    // 设置 ratingMeasureTableData 属性
    SET_RATING_MEASURE_TABLE_DATA (state, data) {
      state.ratingMeasureTableData = data
    },
    // 设置 measureConsistencyTooltip 属性
    SET_MEASURE_CONSISTENCY_TOOLTIP (state, tooltip) {
      state.measureConsistencyTooltip = tooltip
    },
    // 设置 rules 属性
    SET_RULES (state, rules) {
      state.rules = rules
    },
    // 设置 loadFrameworkLoading 属性
    SET_LOAD_FRAMEWORK_LOADING (state, loading) {
      state.loadFrameworkLoading = loading
    },
    // 设置 currentObservationIndex 属性
    SET_CURRENT_OBSERVATION_INDEX (state, index) {
      state.currentObservationIndex = index
    },
    // 设置 needClear 属性
    SET_NEED_CLEAR (state, index) {
      state.needClear = index
    },
    // 设置 Centers 组活动变化设置
    SET_CENTERS_SETTING (state, centersSetting) {
      state.centersSetting = centersSetting
    },
    // 设置 Stations 组活动变化设置
    SET_STATIONS_SETTING (state, stationsSetting) {
      state.stationsSetting = stationsSetting
    },
    // 设置 Quiz 等级是否是 bloom
    SET_IS_BLOOM_QUIZ (state, bloomQuizSetting) {
      state.bloomQuizSetting = bloomQuizSetting
    },
    // 设置 lessonObjectiveType
    SET_LESSON_OBJECTIVE_TYPE (state, lessonObjectiveType) {
      state.lessonObjectiveType = lessonObjectiveType
    },
    // 设置 prompt 集合
    SET_PROMPT_SOURCES_LIST (state, promptSourcesList) {
      // 响应式的更新 promptSourcesList
      // 使用 map 方法创建一个新的数组实例，目的是为了响应式的更新
      state.promptSourcesList = promptSourcesList.map(item => ({ ...item }))
    },
    // 设置系统 tab
    SET_SYSTEM_TABS (state, systemTabs) {
      state.systemTabs = systemTabs
    },
    // 设置当前 tab
    SET_CURRENT_TAB (state, currentTab) {
      state.currentTab = currentTab
    },
    // 设置当前的场景
    SET_CURRENT_SCENE (state, currentScene) {
      state.currentScene = currentScene
    },
    // 设置当前的场景 Id
    SET_CURRENT_PROMPT_ID (state, currentPromptId) {
        state.currentPromptId = currentPromptId
    },
    // 设置当前周计划
    SET_CURRENT_WEEKLY_PLAN (state, weeklyPlan) {
      state.currentWeeklyPlan = weeklyPlan
    },
    // 设置是否是 Curriculum Genie
    SET_IS_CG (state, isCG) {
      state.isCG = isCG
    },
    // 设置是否是 Magic Curriculum
    SET_IS_MC (state, isMC) {
      state.isMC = isMC
    },
    // 设置是否是 Curriculum Genie
    SET_IS_CURRICULUM_PLUGIN (state, isCurriculumPlugin) {
      state.isCurriculumPlugin = isCurriculumPlugin
    },
    // 设置单元 Foundation 生成中 loading 状态
    SET_UNIT_FOUNDATION_GENERATE_LOADING (state, value) {
      state.generateUnitFoundationAndWeeklyThemeLoading = value
    },
    SET_PROMPT (state, prompt) {
      state.prompt = prompt
    },
    SET_CUSTOMIZE_PROMPT (state, customizePrompt) {
      state.customizePrompt = customizePrompt
    },
    // 设置单元内容是否更新
    SET_UNIT_HAS_UPDATED (state, hasUpdated) {
      state.unitHasUpdated = hasUpdated
    },
    SET_PROMPT_CUSTOM_DETAIL_HAS_UPDATED (state, hasUpdated) {
      state.promptCustomDetailHasUpdated = hasUpdated
    },
    // 设置导入单元分析结果
    SET_ANALYZE_UNIT_CONTENT (state, analyzeUnitContent) {
      state.analyzeUnitContent = analyzeUnitContent
    },
    // 重置导入单元分析结果
    RESET_ANALYZE_UNIT_CONTENT (state) {
      state.analyzeUnitContent = null
    }
  },
  actions: {
    setUnit ({ commit }, unit) {
      commit('SET_UNIT', unit)
    },
    setWeeklyPlans ({ commit }, weeklyPlans) {
      commit('SET_WEEKLY_PLANS', weeklyPlans)
    },
    setWeeklyPlanIds ({ commit }, planIds) {
      commit('SET_WEEKLY_PLANIDS', planIds)
    },
    setCurrentModel ({ commit }, model) {
      commit('SET_CURRENT_MODEL', model)
    },
    setPromptExtensionParams ({ commit, state }, params) {
      // 判断 param 中是否已经存在了 params
      let newParams = state.promptExtensionParams
      // 遍历 params
      for (let key in params) {
        // 如果已经存在了，就更新
        newParams[key] = params[key]
      }
      commit('SET_PROMPT_EXTENSION_PARAMS', newParams)
    },
    getFrameworkDomains ({ commit, state }, payload) {
      // 处理参数：既可以是字符串形式的 frameworkId，也可以是对象形式的 { frameworkId, compress }
      let frameworkId, compress = null;

      if (typeof payload === 'string') {
        frameworkId = payload;
      } else {
        frameworkId = payload.frameworkId;
        compress = payload.compress;
      }

      // 是否传递了 compress 参数
      let isUnCompress = compress !== undefined && compress !== null && compress === false;

      
      return new Promise((resolve, reject) => {
        // 生成缓存 key，只有当 compress 为 false 时才添加标识
        // 这样当 compress 不传值或为 true 时，使用原始的 frameworkId 作为 cacheKey
        const cacheKey = isUnCompress ? frameworkId + '_uncompressed' : frameworkId;
        
        // 如果已存在，直接返回
        if (state.frameworkDomains[cacheKey]) {
          resolve(state.frameworkDomains[cacheKey])
          return
        }
        
        // 构建 URL 和查询参数
        let url = $api.urls().getMeasures2 + '?frameworkId=' + frameworkId
        if (isUnCompress) {
          url += '&compress=' + compress
        }
        
        // 处理测评点数据，添加 label 属性
        const processMeasures = (items) => {
          if (!items || !Array.isArray(items)) return items
          
          return items.map(item => {
            if (!item.label) {
              // 添加 label 属性
              if (item.abbreviation && item.name) {
                item.label = item.abbreviation + '-' + item.name
              } else {
                item.label = item.abbreviation ? item.abbreviation : item.name
              }
            }
            
            // 递归处理子节点
            if (item.children && Array.isArray(item.children)) {
              item.children = processMeasures(item.children)
            }
            
            return item
          })
        }

        $axios.get(url).then(res => {
          let measures = res.measures
          measures = processMeasures(measures)
          
          // 使用 cacheKey 作为缓存的键，但保持 frameworkId 的原始语义
          commit('SET_FRAMEWORK_DOMAINS', {
            frameworkId: frameworkId,
            cacheKey: cacheKey,
            domains: measures
          })
          resolve(measures)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 查询测评点(包括领域，只有顶层和底层)
    getMeasuresContainTopAndBottom({ commit, state }, param) {
      return new Promise((resolve, reject) => {
        // 如果已存在，直接返回
        if (state.frameworkMeasures.get(param.frameworkId + param.unitId)) {
          resolve(state.frameworkMeasures.get(param.frameworkId + param.unitId))
          return
        }
        $axios.get($api.urls().getMeasuresContainTopAndBottom ,{params: param}).then(res => {
          let measures = res.measures.map(domain => {
            domain.label = domain.abbreviation + '-' + domain.name
            domain.children = domain.children.map(measure => {
              measure.label = measure.abbreviation + '-' + measure.name
              return measure
            })
            return domain
          })
          // 获取得到测评点的映射值
          let convertMappingAbbrToDomainAbbr = new Map()
          // 如果有映射值
          if (res.mappingAbbrToDomainAbbr) {
            // 将映射值转化为 map
            let entries = Object.entries(res.mappingAbbrToDomainAbbr)
            entries.forEach(([key, value]) => {
              convertMappingAbbrToDomainAbbr.set(key, value)
            })
          }
          // 判断 convertMappingAbbrToDomainAbbr 是否是一个空的 map
          if (convertMappingAbbrToDomainAbbr.size > 0) {
            commit('SET_CONVERT_MAPPING_ABBR_TO_DOMAIN_ABBR_MAP', convertMappingAbbrToDomainAbbr)
          }
          commit('SET_FRAMEWORK_DOMAINS_TOP_BOTTOM', {
            frameworkId: param.frameworkId,
            unitId: param.unitId,
            domains: measures
          })

          // ABB缩写集合
          let abbSet = new Set()
          measures.forEach(domain => {
            if (domain && domain.abbreviation) {
              let domainAbbreviation = domain.abbreviation.trim().toUpperCase()
              abbSet.add(domainAbbreviation)
            }
            if (domain && domain.children) {
              domain.children.forEach(measure => {
                if (measure && measure.abbreviation) {
                  let measureAbbreviation = measure.abbreviation.trim().toUpperCase();
                  abbSet.add(measureAbbreviation)
                }
              })
            }
          })
          commit('SET_FRAMEWORK_DOMAINS_TOP_BOTTOM_ABB', abbSet)
          resolve(measures)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 设置 observationRequest
    setObservationRequest ({ commit }, observationRequest) {
      commit('SET_OBSERVATION_COPILOT_REQUEST', observationRequest)
    },
    // 设置frameworkId
    setFrameworkId ({ commit }, frameworkId) {
      commit('SET_FRAMEWORK_ID', frameworkId)
    },

    // 设置observations
    setObservations ({ commit }, observations) {
      commit('SET_OBSERVATIONS', observations)
    },

    // 设置ageGroup
    setAgeGroup ({ commit }, ageGroup) {
      commit('SET_AGE_GROUP', ageGroup)
    },

    // 设置labelObservationLoading
    setLabelObservationLoading ({ commit }, loading) {
      commit('SET_LABEL_OBSERVATION_LOADING', loading)
    },

    // 设置当前的周计划
    setCurrentWeekPlan ({ commit }, weeklyPlan) {
      commit('SET_CURRENT_WEEKLY_PLAN', weeklyPlan)
    },
    // 设置ratingObservationLoading
    setRatingObservationLoading ({ commit }, loading) {
      commit('SET_RATING_OBSERVATION_LOADING', loading)
    },

    // 添加已输入的observation
    addHasInputObservation ({ commit }, observation) {
      commit('ADD_HAS_INPUT_OBSERVATION', observation)
    },

    // 清空已输入的observation
    clearHasInputObservation ({ commit }) {
      commit('CLEAR_HAS_INPUT_OBSERVATION')
    },

    // 设置labelResult
    setLabelResult ({ commit }, result) {
      commit('SET_LABEL_RESULT', result)
    },

    // 设置samplesDialogVisible
    setSamplesDialogVisible ({ commit }, visible) {
      commit('SET_SAMPLES_DIALOG_VISIBLE', visible)
    },

    // 设置ratingResult
    setRatingResult ({ commit }, result) {
      commit('SET_RATING_RESULT', result)
    },

    // 设置parseResults
    setParseResults ({ commit }, results) {
      commit('SET_PARSE_RESULTS', results)
    },

    // 设置parseRatingResult
    setParseRatingResult ({ commit }, result) {
      commit('SET_PARSE_RATING_RESULT', result)
    },

    // 设置selectedMeasures
    setSelectedMeasures ({ commit }, measures) {
      commit('SET_SELECTED_MEASURES', measures)
    },

    // 设置measures
    setMeasures ({ commit }, measures) {
      commit('SET_MEASURES', measures)
    },

    // 设置tableData
    setTableData ({ commit }, tableData) {
      commit('SET_TABLE_DATA', tableData)
    },

    // 设置ratingMeasureTableData
    setRatingMeasureTableData ({ commit }, ratingMeasureTableData) {
      commit('SET_RATING_MEASURE_TABLE_DATA', ratingMeasureTableData)
    },

    // 设置measureConsistencyTooltip
    setMeasureConsistencyTooltip ({ commit }, tooltip) {
      commit('SET_MEASURE_CONSISTENCY_TOOLTIP', tooltip)
    },

    // 设置rules
    setRules ({ commit }, rules) {
      commit('SET_RULES', rules)
    },

    // 设置loadFrameworkLoading
    setLoadFrameworkLoading ({ commit }, loading) {
      commit('SET_LOAD_FRAMEWORK_LOADING', loading)
    },

    // 设置currentObservationIndex
    setCurrentObservationIndex ({ commit }, index) {
      commit('SET_CURRENT_OBSERVATION_INDEX', index)
    },
    // 设置 SET_NEED_CLEAR
    setNeedClear ({ commit }, index) {
      commit('SET_NEED_CLEAR', index)
    },
    // 设置 prompt 集合
    setPromptSourcesList ({ commit }, promptSourcesList) {
      commit('SET_PROMPT_SOURCES_LIST', promptSourcesList)
    },
    // updatePromptSourcesList
    updatePromptSourcesList ({ commit, state }, { step, sources }) {
      // 获取 promptSourcesList
      let promptSourcesList = state.promptSourcesList
      // 查找当前的 step
      let index = promptSourcesList.findIndex(item => item.steps === step)
      // 如果没有找到
      if (index === -1) {
        return
      }
      // 获取当前 step 中的 sources
      let needUpdateSources = promptSourcesList[index].sources
      // sources 是一个数组，如果一个 source 存在在 sources 中，同时存在在 needUpdateSources 中就更新，如果存在在 sources 中，不存在在 needUpdateSources 中就添加，如果不存在在 sources 中，存在在 needUpdateSources 中就删除
      sources.forEach(source => {
        let sourceIndex = needUpdateSources.findIndex(item => item.sourceName === source.sourceName)
        // 如果没有找到就添加
        if (sourceIndex === -1) {
          needUpdateSources.push(source)
        } else {
          // 更新 sources
          needUpdateSources.splice(sourceIndex, 1, source)
        }
      })
      // 更新 promptSourcesList 中的 sources
      promptSourcesList.splice(index, 1, {
        steps: step,
        title: promptSourcesList[index].title,
        sources: needUpdateSources
      })
      // 更新 promptSourcesList
      commit('SET_PROMPT_SOURCES_LIST', promptSourcesList)
    },
    // 设置系统 tab
    setSystemTabs ({ commit }, systemTabs) {
      commit('SET_SYSTEM_TABS', systemTabs)
    },
    // 设置当前 tab
    setCurrentTab ({ commit }, currentTab) {
      commit('SET_CURRENT_TAB', currentTab)
    },
    // 设置当前的场景
    setCurrentScene ({ commit }, currentScene) {
      commit('SET_CURRENT_SCENE', currentScene)
    },
    // 设置是否是 Curriculum Genie
    setIsCG ({ commit }, isCG) {
      commit('SET_IS_CG', isCG)
    },
    // 设置是否是 Magic Curriculum
    setIsMC ({ commit }, isMC) {
      commit('SET_IS_MC', isMC)
    },
    // 设置是否是 Curriculum Plugin
    setIsCurriculumPlugin ({ commit }, project) {
      commit('SET_IS_MC', project === 'MAGIC-CURRICULUM')
      commit('SET_IS_CG', project === 'FOLC')
      commit('SET_IS_CURRICULUM_PLUGIN', project === 'CURRICULUM-PLUGIN')
    },

    // 获取 centers 组设置
    getCentersSetting ({ commit, state }, isStation) {
      return new Promise((resolve, reject) => {
        // 获取设置
        let settings = isStation ? state.stationsSetting : state.centersSetting
        // 如果已存在，直接返回
        if (Object.keys(settings).length > 0) {
          resolve(settings)
          return
        }
        $axios.get($api.urls().getCentersActivityChangeSetting, { params: { isStation } }).then(res => {
          // 根据不同的类型，保存到 vuex 中
          if (isStation) {
            commit('SET_STATIONS_SETTING', res)
          } else {
            commit('SET_CENTERS_SETTING', res)
          }
          resolve(res)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 获取 quiz 难度模型设置
    getQuizBloomSetting ({ commit, state }) {
      return new Promise((resolve, reject) => {
        // 如果已存在，直接返回
        if (state.bloomQuizSetting !== null) {
          resolve(state.bloomQuizSetting)
          return
        }
        $axios.get($api.urls().getLessonQuizLevelStandard).then(res => {
          commit('SET_IS_BLOOM_QUIZ', res.bloom)
          resolve(res.bloom)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 设置 quiz 难度模型
    setQuizBloomSetting ({ commit }, bloomQuizSetting) {
      // 更新 vuex 中的 bloomQuizSetting
      commit('SET_IS_BLOOM_QUIZ', bloomQuizSetting)
      // 调接口更新 quiz 难度模型
      $axios.post($api.urls().updateLessonQuizLevelStandard, { bloom: bloomQuizSetting })
    },
    // 根据年龄获取题目类型
    getLessonQuizQuestionTypes ({ state }, ageGroupName) {
      return new Promise((resolve, reject) => {
        // 如果 ageGroupName 为空，直接返回
        if (!ageGroupName) {
          resolve([])
          return
        }
        const quizQuestionTypes = []
        for (const ageType of state.ageTypes) {
          try {
            // 根据年龄获取年龄组合类型名称
            if (ageType.val.has(ageGroupName)) {
              // 获取年龄组合类型下的题目类型
              quizQuestionTypes.push(...state.quizQuestionTypesByAgeType[ageType.key])
              break
            }
          } catch (e) {
            reject(e)
          }
        }
        resolve(quizQuestionTypes)
      })
    },
    // 根据年龄和题目类型获取可选的 level
    getLessonQuizQuestionLevels ({ state }, { ageGroupNameAndType = null, isBloom = true }) {
      return new Promise((resolve, reject) => {
        if (!ageGroupNameAndType) {
          resolve([])
          return
        }
        const quizQuestionLevels = []
        for (const ageType of state.ageTypes) {
          try {
            // 根据年龄和题目类型获取可选的 level
            if (ageType.val.has(ageGroupNameAndType.ageGroupName)) {
              // 获取年龄对应的年龄组合下的 level 映射题目类型对象
              const quizQuestionByAgeType = isBloom ? state.bloomQuizQuestionTypesByAgeTypeAndLevel[ageType.key] : state.dokQuizQuestionTypesByAgeTypeAndLevel[ageType.key]
              // 获取可选的 level
              const levels = Object.keys(quizQuestionByAgeType) // 获取年龄组合下的所有 level 名称
                // 遍历当前年龄组合下的所有 level 下的题目类型，如果与传入的题目类型匹配，则添加到可选的 level 数组中
                .filter(type => quizQuestionByAgeType[type].find(question => question.key === ageGroupNameAndType.type))
              if (levels && levels.length !== 0) {
                quizQuestionLevels.push(...levels)
                break
              }
            }
          } catch (e) {
            reject(e)
          }
        }
        resolve(quizQuestionLevels)
      })
    },
    // 获取 lessonObjectiveType
    getLessonObjectiveType ({ commit, state }) {
      return new Promise((resolve, reject) => {
        // 如果已存在，直接返回
        if (state.lessonObjectiveType) {
          resolve(state.lessonObjectiveType)
          return
        } else {
          $axios.get($api.urls().getLessonObjectiveType).then(res => {
            commit('SET_LESSON_OBJECTIVE_TYPE', res.objectiveType)
            resolve(res.objectiveType)
          }).catch(err => {
            reject(err)
          })
        }
      })
    },
    // 设置 lessonObjectiveType
    setLessonObjectiveType ({ commit }, lessonObjectiveType) {
      commit('SET_LESSON_OBJECTIVE_TYPE', lessonObjectiveType)
    }
  }
}

export default curriculum
