import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import common from './modules/common'
import user from './modules/user'
import chat from './modules/chat'
// import dashboard from './modules/dashboard'
// import attendance from './modules/attendance'
// import inkind from './modules/inkind'
// import ratingPeriod from './modules/ratingPeriod'
import lesson from './modules/lesson'
import curriculum from './modules/curriculum'
import unit from './modules/unit'
import translate from './modules/translate'
import designer from './modules/designer';
import magicCurriculum from './modules/magicCurriculum'
// 导入 CG 相关的 store 模块
import cgAuth from './modules/cgAuth'
import cgShareLink from './modules/cgShareLink'

Vue.use(Vuex)
// currentUser
const store = new Vuex.Store({
  modules: {
    common, // 公共模块
    user, // 用户信息
    chat, // 环信
    // dashboard, // 数据统计
    // inkind, // inkind传值
    // ratingPeriod, // 周期设置
    curriculum, // 课程
    lesson,
    // attendance, // 签到签退,
    translate, // 翻译
    unit, // 单元
    designer, // Curriculum 顶层设计首页
    magicCurriculum, // 魔法课程
    // CG 相关的 store 模块
    cgAuth, // CG 认证信息
    cgShareLink // CG 分享链接
  },
  getters,
  // plugins: [loggerPlugin] // 添加插件
})

export default store
