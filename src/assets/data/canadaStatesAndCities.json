[{"name": "Alberta", "code": "", "cities": ["Calgary", "Edmonton", "Red Deer", "Lethbridge", "Airdrie", "Wood Buffalo", "St. Albert", "Grande Prairie", "Medicine Hat", "Spruce Grove", "<PERSON><PERSON>", "<PERSON>", "Okotoks", "Fort Saskatchewan", "Chestermere", "Beaumont", "Lloydminster", "<PERSON><PERSON>", "Stony Plain", "Sylvan Lake", "Canmore", "Cold Lake", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "High River", "<PERSON><PERSON><PERSON>", "Wetaskiwin", "Morinville", "Blackfalds", "<PERSON><PERSON>", "Whitecourt", "Olds", "<PERSON><PERSON>", "Coaldale", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Innisfail", "Drayton Valley", "Ponoka", "Peace River", "Slave Lake", "Rocky Mountain House", "Devon", "<PERSON><PERSON><PERSON>", "Bonnyville", "<PERSON><PERSON><PERSON>", "St. Paul", "Vegreville", "Crowsnest Pass", "Redcliff", "Didsbury", "<PERSON><PERSON>", "<PERSON>", "Barrhead", "Vermilion", "Carstairs", "<PERSON>", "<PERSON><PERSON><PERSON>", "Pincher Creek", "Crossfield", "<PERSON><PERSON>", "Grande Cache", "High Level", "Penhold", "<PERSON>", "Three Hills", "Fort Macleod", "Athabasca", "Coalhurst", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Black Diamond", "Sexsmith", "<PERSON><PERSON><PERSON>", "High Prairie", "Turner Valley", "<PERSON>", "Beaverlodge", "<PERSON><PERSON><PERSON>", "Calmar", "<PERSON><PERSON>", "Redwater", "Tofield", "<PERSON>", "Bow Island", "Fox Creek", "Millet", "Picture Butte", "Vulcan", "Valleyview", "Lamont", "Waba<PERSON>", "Springbrook", "Wembley", "<PERSON> Accord", "Elk Point", "<PERSON><PERSON>", "Two Hills", "Bruederheim", "<PERSON><PERSON>", "Swan Hills", "Vauxhall", "<PERSON><PERSON>", "Legal", "Bass<PERSON>", "<PERSON>", "Irricana", "Eckville", "Alberta Beach", "Duchess", "Viking", "Bentley", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Oyen"]}, {"name": "British Columbia", "code": "", "cities": ["Vancouver", "Surrey", "Victoria", "<PERSON><PERSON>", "Richmond", "<PERSON><PERSON><PERSON>", "Abbotsford", "Coquitlam", "<PERSON><PERSON><PERSON>", "White Rock", "Delta", "<PERSON><PERSON>", "Kamloops", "Chilliwack", "Maple Ridge", "New Westminster", "Prince <PERSON>", "Port Coquitlam", "North Vancouver", "<PERSON>", "<PERSON><PERSON><PERSON>", "Langford Station", "West Vancouver", "Mission", "Campbell River", "Pen<PERSON><PERSON>", "East Kelowna", "Port Moody", "North Cowichan", "Langley", "Parksville", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Port Alberni", "Fort St. John", "Cranbrook", "Salmon Arm", "Pitt Meadows", "<PERSON><PERSON>", "Oak Bay", "Esquimalt", "Central Saanich", "Lake Country", "<PERSON><PERSON>", "Comox", "Terrace", "Powell River", "Trail", "Dawson Creek", "<PERSON>", "<PERSON>", "North Saanich", "Quesnel", "Williams <PERSON>", "<PERSON><PERSON><PERSON>", "Summerland", "View Royal", "<PERSON>", "Ladysmith", "Coldstream", "<PERSON><PERSON><PERSON>", "Castlegar", "Gibsons", "Qualicum Beach", "Kitimat", "Kimberley", "<PERSON><PERSON><PERSON>", "Kent", "Hope", "Peachland", "<PERSON>", "<PERSON><PERSON><PERSON>", "Creston", "Northern Rockies", "<PERSON><PERSON>", "<PERSON>", "Spallumcheen", "Osoyoos", "Met<PERSON>sin", "Westbank", "Cumberland", "Vanderhoof", "Bowen Island", "Grand Forks", "Port Hardy", "Sparwood", "Rossland", "<PERSON>", "Golden", "Fruitvale", "Invermere", "<PERSON>", "Lake Cowichan", "Cedar", "<PERSON><PERSON><PERSON>", "Houston", "Pemberton", "<PERSON><PERSON><PERSON>", "Princeton", "Cowichan Bay", "<PERSON><PERSON>", "Elkford", "Highlands", "Sicamous", "Chase", "Tumbler Ridge", "Anmore", "Clearwater", "Lillooet", "Logan Lake", "Port McNeill", "Tofino", "Burns Lake", "Saltair", "Lumby", "One Hundred Mile House", "Ucluelet", "Chetwynd", "Harrison Hot Springs", "Nisga'a", "Lakeview", "<PERSON><PERSON><PERSON><PERSON>", "Warfield", "Popkum", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nakusp", "Fort St. James", "Hilliers", "Ashcroft", "<PERSON><PERSON><PERSON><PERSON>", "Windermere", "Gold River", "Dunsmuir", "Barrière", "Lions Bay", "Telkwa", "Ootischenia", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Salmo", "Valemount", "Hudson Hope"]}, {"name": "Manitoba", "code": "", "cities": ["Winnipeg", "<PERSON>", "Steinbach", "Springfield", "Hanover", "<PERSON>", "Portage La Prairie", "<PERSON>", "<PERSON><PERSON><PERSON>", "St. Andrews", "<PERSON><PERSON>", "Selkirk", "Morden", "East St. Paul", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Rockwood", "<PERSON><PERSON><PERSON>", "The Pas", "West St. Paul", "La Broquerie", "Niverville", "Brokenhead", "Stonewall", "Ste. Anne", "<PERSON><PERSON> (Part)", "Neepawa", "<PERSON><PERSON>", "Headingley", "Altona", "Swan River", "<PERSON>", "Lorette", "Killarney - Turtle Mountain", "Woodlands", "Bifrost-Riverton", "<PERSON><PERSON>", "Hillsburg-Roblin-Shell River", "WestLake-Gladstone", "<PERSON>", "Beauséjour", "Lac du Bonnet", "Virden", "<PERSON>", "<PERSON><PERSON>", "North Cypress-Langford", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "West Interlake", "Prairie View", "Deloraine-Winchester", "Oakland-Wawanesa", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>", "Souris-Glenwood", "Riverdale", "Pembina", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yellowhead", "Swan Valley West", "Grey", "Gilbert <PERSON>", "Norfolk-Treherne", "<PERSON><PERSON><PERSON>", "Sifton", "Grassland", "<PERSON>", "Ste<PERSON> Rose", "Cartwright<PERSON><PERSON><PERSON>", "Mossey River", "Lakeshore", "Riding Mountain West", "Clanwilliam<PERSON><PERSON><PERSON><PERSON>", "Glenboro-South Cypress", "North Norfolk", "<PERSON><PERSON><PERSON>", "Minitonas-<PERSON>sman", "Carberry", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>y", "Blumenort", "<PERSON>", "Wasagamack", "<PERSON>", "Rosedale", "<PERSON><PERSON>", "Oakview", "Harrison Park", "Boisse<PERSON>in", "Pinawa", "Pipestone", "Prairie Lakes", "<PERSON><PERSON>", "Grahamdale", "<PERSON>", "St<PERSON>", "Landmark", "Powerview-Pine Falls", "St-Pierre-Jolys", "Arborg", "<PERSON>", "<PERSON><PERSON>", "Montcalm", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Teulon", "Minto-Odanah", "Glenella-Lansdowne", "Two Borders", "Winnipeg Beach", "Victoria", "<PERSON>", "<PERSON><PERSON>", "Argyle", "Hamiota", "<PERSON><PERSON>", "Grand View", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"name": "New Brunswick", "code": "", "cities": ["Moncton", "Saint <PERSON>", "Fredericton", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Riverview", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Bathurst", "Oromocto", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Beaubassin East / Beaubassin-est", "<PERSON>", "Sussex", "<PERSON><PERSON><PERSON><PERSON>", "Sackville", "Grand Falls", "Woodstock", "<PERSON>", "Saint Marys", "Memramcook", "Grand Bay-Westfield", "Shippagan", "Coverdale", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Beresford", "<PERSON><PERSON>", "New Maryland", "Dundas", "Simonds", "Alnwick", "Studholm", "<PERSON>", "Kingston", "<PERSON><PERSON><PERSON>", "Wellington", "<PERSON><PERSON><PERSON>", "Wakefield", "Cocagne", "Shippegan", "Lincoln", "Cap P<PERSON>", "Salisbury", "<PERSON><PERSON><PERSON><PERSON>", "Grand Manan", "<PERSON>", "Paquetville", "Minto", "Upper Miramichi", "<PERSON><PERSON><PERSON>", "Saint<PERSON><PERSON>", "Pennfield Ridge", "Northesk", "Kent", "Westfield Beach", "Allardville", "Saint<PERSON><PERSON>", "Saint Mary", "<PERSON> Rocher", "Eel River Crossing", "Manners Sutton", "Richibucto", "Saint-<PERSON>", "<PERSON>", "Maugerville", "Brighton", "<PERSON><PERSON><PERSON>", "Northampton", "Wicklow", "Neguac", "<PERSON><PERSON><PERSON>", "Southesk", "<PERSON><PERSON><PERSON>", "Florenceville", "Perth", "<PERSON><PERSON><PERSON>", "Belledune", "Na<PERSON>wigewauk", "<PERSON><PERSON><PERSON>", "<PERSON>", "Springfield", "St. George", "<PERSON>", "Southampton", "Denmark", "Sussex Corner", "Petitcodiac", "<PERSON><PERSON>", "Upham", "<PERSON><PERSON>", "Hillsborough", "<PERSON>ldford", "<PERSON>", "<PERSON><PERSON><PERSON>", "Richmond", "Saint-<PERSON><PERSON>", "Lamèque", "<PERSON>s<PERSON><PERSON>", "Queensbury", "New Bandon", "Peel", "<PERSON>", "Saint Martins", "Rogersville", "McAdam", "Newcastle", "<PERSON>", "Saint<PERSON><PERSON>", "Greenwich", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Atholville", "Durham", "<PERSON><PERSON>", "Botsford", "Plaster Rock", "<PERSON><PERSON><PERSON>", "Kedgwick", "Dorchester", "<PERSON><PERSON><PERSON>"]}, {"name": "Newfoundland and Labrador", "code": "", "cities": ["St. John's", "Conception Bay South", "Paradise", "Mount Pearl Park", "Corner Brook", "Grand Falls", "Gander", "Labrador City", "Portugal Cove-St. Philip's", "Happy Valley", "Torbay", "Stephenville", "<PERSON>", "Clarenville", "Carbonear", "Marystown", "Deer Lake", "<PERSON><PERSON>", "Channel-Port aux Basques", "Pasadena", "<PERSON>nti<PERSON>", "<PERSON><PERSON><PERSON>", "Lewisporte", "Bishops Falls", "Harbour Grace", "Springdale", "Botwood", "Spaniards Bay", "Holyrood", "Logy Bay-Middle Cove-Outer Cove", "<PERSON><PERSON><PERSON>", "Grand Bank", "St. Anthony", "Fogo Island", "Twillingate", "New-Wes-Valley", "<PERSON><PERSON><PERSON>", "Glovertown", "Pouch Cove", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Trinity Bay North", "Victoria", "Flat Rock", "Stephenville Crossing", "Witless Bay", "Harbour Breton", "Massey Drive", "Bay Bulls", "Upper Island Cove", "<PERSON>s Beach", "Gambo", "Humbermouth", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Irishtown-Summerside", "St. George's", "St. Lawrence", "St. Alban's", "Centreville-Wareham-Trinity", "<PERSON>in", "Harbour Main-Chapel's Cove-Lakeview", "Fortune", "<PERSON><PERSON>"]}, {"name": "Northwest Territories", "code": "", "cities": ["Yellowknife", "Hay River", "Inuvik", "Fort Smith", "<PERSON><PERSON><PERSON><PERSON>", "Fort Simpson"]}, {"name": "Nova Scotia", "code": "", "cities": ["Halifax", "Cape Breton", "Truro", "New Glasgow", "Inverness", "Kentville", "Chester", "Queens", "Amherst", "Bridgewater", "Church Point", "Argyle", "Yarmouth", "Barrington", "Antigonish", "Windsor", "Wolfville", "Stellarton", "Westville", "Port Hawkesbury", "Pictou", "Berwick", "Trenton", "Lunenburg", "<PERSON><PERSON><PERSON>", "Middleton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Falmouth", "Stewiacke", "Parr<PERSON>", "Oxford", "Centreville", "Wedgeport", "Mahone Bay"]}, {"name": "Nunavut", "code": "", "cities": ["Iqaluit", "<PERSON><PERSON>", "Arviat", "<PERSON>", "Cambridge Bay", "Igloolik", "Pond Inlet", "Pangnirtung", "Cape Dorset", "Gjoa Haven", "Repulse Bay", "Clyde River", "Taloyoak", "Kugluktuk"]}, {"name": "Ontario", "code": "", "cities": ["Toronto", "Ottawa", "<PERSON>", "Mississauga", "Brampton", "<PERSON>er", "London", "Markham", "Oshawa", "<PERSON>", "Windsor", "St. Catharines", "Oakville", "Richmond Hill", "Burlington", "Sudbury", "<PERSON><PERSON>", "Guelph", "W<PERSON>by", "Cambridge", "<PERSON>", "Ajax", "Waterloo", "Thunder Bay", "Brantford", "Chatham", "Clarington", "Pickering", "Niagara Falls", "Newmarket", "Peterborough", "Kawartha Lakes", "<PERSON><PERSON>", "Belleville", "Sarnia", "Sault Ste. Marie", "Welland", "Halton Hills", "Aurora", "North Bay", "St<PERSON>ffville", "Cornwall", "Georgina", "Woodstock", "Quinte West", "St<PERSON>", "New Tecumseth", "Innisfil", "Bradford West Gwillimbury", "<PERSON><PERSON>", "Lakeshore", "<PERSON><PERSON>", "Leamington", "East Gwillimbury", "Orangeville", "<PERSON><PERSON><PERSON>", "Stratford", "Fort Erie", "LaSalle", "Centre Wellington", "Grimsby", "King", "Woolwich", "Clarence-<PERSON><PERSON>", "Midland", "Lincoln", "Wasaga Beach", "Collingwood", "Strathroy-Caradoc", "<PERSON><PERSON>", "Amherstburg", "Tecumseh", "<PERSON><PERSON>", "Owen Sound", "Brockville", "Kingsville", "Springwater", "<PERSON><PERSON><PERSON>", "Uxbridge", "<PERSON><PERSON><PERSON>", "Essex", "Oro-Medonte", "Cobourg", "South Frontenac", "Port Colborne", "Huntsville", "<PERSON>", "Niagara-on-the-Lake", "Middlesex Centre", "<PERSON><PERSON><PERSON>", "Tillsonburg", "Pelham", "Petawawa", "North Grenville", "Loyalist", "Port Hope", "Pembroke", "Bracebridge", "Greater Napanee", "<PERSON><PERSON>", "Mississippi Mills", "St. Clair", "West Lincoln", "West Nipissing / Nipissing Ouest", "Clearview", "Thames Centre", "Carleton Place", "Guelph/Eramosa", "Central Elgin", "Saugeen Shores", "Ingersoll", "South Stormont", "Severn", "South Glengarry", "North Perth", "Trent Hills", "The Nation / La Nation", "West Grey", "<PERSON><PERSON><PERSON><PERSON>", "Perth East", "Wellington North", "Brighton", "Tiny", "Hawkesbury", "<PERSON>", "Erin", "Kincardine", "<PERSON>", "Arnprior", "North Dundas", "<PERSON><PERSON>", "Georgian Bluffs", "Norwich", "Meaford", "Adjala-Tosorontio", "Hamilton Township", "South Dundas", "Lambton Shores", "North Dumfries", "<PERSON>ton", "Rideau Lakes", "North Glengarry", "South Huron", "Penetanguishene", "<PERSON><PERSON>", "<PERSON><PERSON>", "Temiskaming Shores", "Grey Highlands", "Alfred and Plantagenet", "Elizabethtown-Kitley", "Smiths Falls", "<PERSON><PERSON>", "Leeds and the Thousand Islands", "Brockton", "Laurentian Valley", "Mono", "Malahi<PERSON>", "Huron East", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "West Perth", "<PERSON><PERSON><PERSON>", "Minto", "South Bruce Peninsula", "<PERSON><PERSON><PERSON>", "Plympton-Wyoming", "Kapuskasing", "Zorra", "Kirkland Lake", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Drummond/North Elmsley", "Hanover", "Dryden", "Fort Frances", "<PERSON><PERSON><PERSON>", "Stone Mills", "South-West Oxford", "Do<PERSON><PERSON><PERSON><PERSON>", "McNab/Braeside", "Central Huron", "Blandford-Blenheim", "<PERSON><PERSON>", "Augusta", "St. Marys", "Southgate", "Bluewater", "East Zorra-Tavistock", "Otonabee-South Monaghan", "Huron-Kinloss", "The Blue Mountains", "Whitewater Region", "Edwardsburgh/Cardinal", "Wainfleet", "North Stormont", "Alnwick/Haldimand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Parry Sound", "Muskoka Falls", "Perth", "<PERSON><PERSON><PERSON>", "North Middlesex", "<PERSON><PERSON><PERSON> et al", "Hindon Hill", "Tweed", "<PERSON>", "Petrolia", "Southwest Middlesex", "Front of Yonge", "Tay Valley", "South Bruce", "Ashfield-Colborne-Wawanosh", "Trent Lakes", "Gananoque", "Lanark Highlands", "<PERSON>", "Sioux Lookout", "Hearst", "Breslau", "Stirling-<PERSON>don", "Espanola", "West Elgin", "East Ferris", "North Huron", "Southwold", "Centre Hastings", "<PERSON>", "Greenstone", "Tyendinaga", "Iroquois Falls", "Havelock-Belmont-<PERSON><PERSON><PERSON>", "Central Frontenac", "<PERSON><PERSON>", "Madawaska Valley", "Deep River", "Asphodel-Norwood", "Red Lake", "Hastings Highlands", "<PERSON>", "Northern Bruce Peninsula", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marmora and Lake", "Bancroft", "<PERSON><PERSON>", "Dutton/Dunwich", "Perth South", "Montague", "Warwick", "Bonnechere Valley", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Blind River", "<PERSON><PERSON><PERSON>", "Highlands East", "East Hawkesbury", "Marathon", "<PERSON><PERSON><PERSON>", "Sables-Spanish Rivers", "Lake of Bays", "Merrickville", "Adelaide-Metcalfe", "Melancthon", "Laurentian Hills", "Grand Valley", "Admaston/Bromley", "North Algona Wilberforce", "<PERSON><PERSON>", "<PERSON>", "Enniskillen", "Atikokan", "Markstay", "Northeastern Manitoulin and the Islands", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "French River / Rivière des Français", "East Garafraxa", "Greater Madawaska", "Georgian Bay", "North Kawartha", "<PERSON>", "Black River-<PERSON><PERSON>", "Killaloe, Hagarty and Richards", "<PERSON><PERSON>", "Algonquin Highlands", "Addington Highlands", "<PERSON><PERSON><PERSON>", "Bon<PERSON>", "Central Manitoulin", "Mad<PERSON>", "<PERSON><PERSON>", "Dawn-E<PERSON>hemia", "<PERSON><PERSON><PERSON>", "Manitouwadge", "Wellington", "Frontenac Islands", "Point Edward", "North Frontenac", "<PERSON><PERSON><PERSON>", "Deseron<PERSON>", "<PERSON><PERSON><PERSON>", "Huron Shores", "Nipigon", "<PERSON><PERSON><PERSON>", "Terrace Bay", "<PERSON>, <PERSON> and Aberdeen Additional", "<PERSON><PERSON><PERSON><PERSON>, Lyndoch and Raglan", "Moosonee", "<PERSON><PERSON><PERSON>", "Strong", "<PERSON><PERSON>", "Armour", "Faraday", "Bayfield", "St.-<PERSON>", "Emo", "Smooth Rock Falls", "<PERSON><PERSON><PERSON>", "Thessalon", "<PERSON><PERSON><PERSON>", "St. Joseph", "Moonbeam", "Claremont", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hillsburgh", "Sagamok", "Hensall", "<PERSON><PERSON>", "Laird", "Tara", "Cobalt", "South River", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "South Algonquin", "Sioux Narrows-Nestor Falls", "Beachburg", "<PERSON>hr<PERSON><PERSON>", "Plantagenet", "Papineau<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Prince", "Athens", "Chatsworth", "Magnetawan"]}, {"name": "Prince Edward Island", "code": "", "cities": ["Charlottetown", "Summerside", "Stratford", "Cornwall", "Montague", "Kensington", "Miltonvale Park", "Alberton", "<PERSON><PERSON><PERSON>", "Malpeque"]}, {"name": "Quebec", "code": "", "cities": ["Montréal", "Quebec City", "<PERSON><PERSON>", "Gatineau", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saguenay", "Trois-Rivières", "Terrebonne", "Saint-<PERSON><PERSON><PERSON><PERSON>", "Saint-Jean-sur-Richelieu", "Brossard", "Repentigny", "Drummondville", "Châteauguay", "<PERSON><PERSON>", "<PERSON><PERSON>", "Blainville", "Lac-Brome", "Saint<PERSON><PERSON><PERSON><PERSON><PERSON>", "Beloeil", "Mascouche", "Shawinigan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dollard-des-Ormeaux", "Victoriaville", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vaudreuil-Dorion", "Salaberry-de-Valleyfield", "Rouyn-Noranda", "Boucherville", "<PERSON><PERSON><PERSON><PERSON>", "Côte-Saint-Luc", "Pointe<PERSON><PERSON>", "Val-d’Or", "Cha<PERSON>ly", "Alma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Magog", "Boisbriand", "Sainte-Thérèse", "La Prairie", "Saint-Bruno-de-Montarville", "Thetford Mines", "Sept-Îles", "<PERSON>", "Saint-Lin<PERSON><PERSON><PERSON><PERSON>", "L’Assomption", "Candiac", "Saint-Lambert", "Saint-<PERSON><PERSON><PERSON>", "Varennes", "Mont-Royal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rivière-du-Loup", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sainte-Marthe-sur-le-Lac", "Westmount", "Les <PERSON>x", "Kirkland", "Dorval", "Beaconsfield", "Mont-Saint-Hilaire", "Deux-Montagnes", "Saint<PERSON><PERSON><PERSON><PERSON>", "Sainte<PERSON><PERSON>", "Saint-Basile-le-Grand", "L’Ancienne-Lorette", "Saint-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cowansville", "Sainte-Anne-des-Plaines", "Gas<PERSON>é", "<PERSON><PERSON>urt", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Mont-Laurier", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bécancour", "<PERSON><PERSON><PERSON>", "Val-des-Monts", "Saint-<PERSON><PERSON>", "Sainte-Marie", "<PERSON>", "Prévost", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sainte-Agathe-des-Monts", "Les Îles-de-la-Madeleine", "<PERSON><PERSON><PERSON>", "L’Île-Perrot", "<PERSON><PERSON><PERSON>", "Cantley", "Notre-Dame-de-l'Île-Perrot", "<PERSON><PERSON><PERSON>", "La Tuque", "<PERSON><PERSON>", "Saint-F<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bois-des-Filion", "Marieville", "Saint<PERSON><PERSON><PERSON><PERSON>", "Stoneham-et-Tewkesbury", "Mont-Tremblant", "Saint-Zotique", "Saint<PERSON><PERSON>", "Lorraine", "Notre-Dame-des-Prairies", "Sainte-<PERSON><PERSON>", "Donnacona", "L’Epiphanie", "Pont-Rouge", "Coaticook", "La Pêche", "Otterburn Park", "Sainte-Brigitte-de-<PERSON>", "Sainte-Catherine-de-la-Jacques-<PERSON>", "Farnham", "<PERSON><PERSON>", "La Malbaie", "Boischatel", "Beauport", "Saint-Hippolyte", "Old Chelsea", "Saint-Apollinaire", "<PERSON><PERSON>", "Contrecoeur", "La Sarre", "<PERSON>", "Acton Vale", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chibougamau", "Coteau-du-Lac", "Saint<PERSON><PERSON><PERSON><PERSON>", "Les Cèdres", "Baie-Saint-Paul", "Brownsburg", "Asbestos", "Hampstead", "Saint-<PERSON>", "Plessisville", "Sainte-Anne-des-Monts", "Saint-Lambert-de-Lauzon", "Val-Shefford", "Port-Cartier", "Saint<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Beauceville", "<PERSON><PERSON><PERSON>", "Charlemagne", "Mont-Joli", "Pointe-Calumet", "Pontiac", "L'Ange-Gardien", "Saint-Félix-de-Valois", "McMasterville", "Saint-Calixte", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON>", "Verchères", "<PERSON><PERSON><PERSON>", "Princeville", "Saint-Césaire", "Val<PERSON>David", "Notre-Dame-du-Mont-Carmel", "Sainte-Martine", "Saint-Roch-de-l'Achigan", "Saint-<PERSON>", "Windsor", "Montréal-Ouest", "Témiscouata-sur-le-Lac", "Sainte-Anne-de-Bellevue", "Mont-Orford", "Saint-Germain-de-Grantham", "Saint-Cyrille-de-Wendover", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Warwick", "Napierville", "Waterloo", "<PERSON><PERSON><PERSON>", "Bert<PERSON>erville", "Rivière-Rouge", "Saint-Denis-de-<PERSON>rompton", "Amqui", "Saint-Mathias-sur-Richelieu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Château-<PERSON>er", "Montréal-Est", "Saint-<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "La Pocatière", "Roxton Pond", "Saint-Étienne-des-Grès", "Saint-Donat", "Métabetchouan-Lac-à-la-Croix", "Maniwaki", "Danville", "Lac<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "L’Islet-sur-Mer", "Carleton-sur-Mer", "<PERSON><PERSON>", "Morin-Heights", "<PERSON><PERSON><PERSON>", "Saint-Tite", "New Richmond", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-André-Avellin", "Saint-Ambroise-de-Kildare", "East Angus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-Prosper", "Ormstown", "Saint-A<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON>--Lac-Carré", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Havre-Saint-Pierre", "Saint-<PERSON><PERSON><PERSON>", "Trois-Pistoles", "Grande-Rivière", "Malartic", "Saint-<PERSON>", "Ascot Corner", "Fossambault-sur-le-Lac", "Sainte-Anne-des-Lacs", "Saint-Sulpice", "Saint-Al<PERSON>se-de-Granby", "Sainte<PERSON><PERSON>", "<PERSON><PERSON>", "Saint-Jean-Port-Joli", "Saint-André-d'Argenteuil", "Saint-Côme--Linière", "Forestville", "<PERSON>", "Richmond", "Saint-<PERSON>", "Paspeb<PERSON>", "Saint<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Portneuf", "<PERSON><PERSON>", "Saint-Alphon<PERSON><PERSON><PERSON>", "Val-Morin", "Clermont", "Saint<PERSON><PERSON>d'Arthabaska", "Mont-Saint-Grégoire", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-Libo<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Saint-Alexis-des-Monts", "Cap-Saint-Ignace", "Saint-<PERSON><PERSON><PERSON>-de-Lessard", "Stoke", "Cap Santé", "Saint<PERSON><PERSON><PERSON>", "Saint-Ferréol-les-Neiges", "Senneterre", "Saint-<PERSON>ieu-de-Beloeil", "Sainte-Marie-Madeleine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-Paul-d'Abbotsford", "Saint-<PERSON>", "Saint-Marc-des-Carrières", "<PERSON>stead", "Sainte-Anne-de-Beaupré", "Sainte-Luce", "Saint<PERSON><PERSON>", "Ferme-Neuve", "Yamachiche", "Adstock", "Bonaventure", "Pohénégamook", "Saint-<PERSON><PERSON><PERSON>", "Sainte-Marguerite-du-Lac-Masson", "Saint-<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Grenville-sur-la-Rouge", "Saint<PERSON><PERSON>", "Macamic", "Sainte-Anne-de-Sorel", "Rougemont", "Piedmont", "Lac-des-Écorces", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bedford", "Weedon-Centre", "<PERSON><PERSON><PERSON>", "Saint-<PERSON>-de-<PERSON>", "Huntingdon", "Saint-Bruno", "Laurier-Station", "Saint<PERSON><PERSON><PERSON><PERSON>", "Cap-Chat", "Notre-Dame-de-Lourdes", "Ville-Marie", "<PERSON><PERSON><PERSON>", "Neuville", "<PERSON>", "Saint-Chrysostome", "Saint-Damase", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Fermont", "La Présentation", "Sainte-Catherine-de-<PERSON>ley", "Saint-<PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON>", "Saint-<PERSON>", "Causapscal", "<PERSON>", "Sainte-Victoire-de-Sorel", "Port-Daniel--Gascons", "<PERSON>le", "Saint-Michel-des-Saints", "Saint<PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON>-de-<PERSON>", "<PERSON><PERSON><PERSON>", "Témiscaming", "Sainte-Geneviève-de-Berthier", "Sainte-Madeleine", "Sainte-Croix", "Valcourt", "<PERSON><PERSON><PERSON><PERSON>", "Waterville", "Mansfield-et-Pontefract", "Saint-Denis", "<PERSON>", "Saint-G<PERSON><PERSON>éon-de-Beauce", "Saint-Léonard-d'Aston", "Fort-Coulonge", "Albanel", "<PERSON><PERSON><PERSON><PERSON>", "Maskinongé", "Saint-Charles-de-Bellechasse", "<PERSON><PERSON>", "East Broughton", "Saint-Polycar<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-Côme", "Waskaganish", "Lebel-sur-Quévillon", "Pierreville", "Saint<PERSON><PERSON>", "Saint<PERSON><PERSON>", "Sainte-Cécile-de-Milton", "Saint-Roch-de-<PERSON>eu", "Saint-Na<PERSON>re", "Saint<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON>-<PERSON>", "Papineauville", "Saint-<PERSON><PERSON><PERSON><PERSON>de-Loyola", "Sainte-Anne-de-Sabrevois", "Sainte-Anne-de-la-Pérade", "Saint-Damien-de-<PERSON>", "<PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON>nce", "<PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON><PERSON>", "Saint-Alexandre-de-Kamouraska", "Saint-Marc-sur-Richelieu", "Mandeville", "<PERSON><PERSON>", "Saint<PERSON><PERSON>", "Lac-Nominingue", "<PERSON><PERSON><PERSON><PERSON>", "Saint-G<PERSON><PERSON><PERSON><PERSON>", "Kingsey Falls", "L'Ascension-de-Notre-Seigneur", "Barr<PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-François-du-Lac", "Vallée-Jonction", "Saint<PERSON><PERSON><PERSON><PERSON>", "Lac-Supérieur", "Les Escoumins", "Terrasse-Vaudreuil", "Rivière-Beaudette", "Saint-Bart<PERSON><PERSON><PERSON><PERSON>", "Austin", "Saint-Paul-de-l'Île-aux-Noix", "Saint-Cy<PERSON>rien-de-Napierville", "Déléage", "<PERSON><PERSON>", "Sainte-Béatrix", "Saint-Georges-de-<PERSON>acouna", "<PERSON><PERSON><PERSON><PERSON>", "Saint-Valérien-de-Milton", "<PERSON><PERSON><PERSON>", "Saint-Blaise-sur-Richelieu", "Saint-<PERSON>-de-Coleraine", "Pointe-<PERSON><PERSON>", "Grenville", "Saint-Michel-de-Bellechasse", "Sainte-Ang<PERSON><PERSON>-de-Monnoir", "<PERSON><PERSON><PERSON>", "Sacré-Coeur-Saguenay", "Saint-<PERSON>", "<PERSON><PERSON><PERSON>", "La Guadeloupe", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Povungnituk", "Pointe-des-Cascades", "Chambord", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hemmingford", "Saint-Pierre-de-l'Île-d'Orléans", "Saint-Clet", "Saint-Ours", "Sainte-Anne-de-la-Pocatière", "Notre-Dame-du-Bon-Conseil", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nouvelle", "<PERSON><PERSON><PERSON>", "Saint-Antoine-de-<PERSON>", "Saint-É<PERSON>-de-Caxton", "Price", "<PERSON><PERSON><PERSON><PERSON>Mineur", "Val-Joli", "Saint-Antoine-sur-Richelieu", "Saint-<PERSON><PERSON>", "Saint-<PERSON><PERSON>las-de-Kostka", "Frontenac", "Sainte-Émélie-de-l'Énergie", "Saint-Charles-sur-Richelieu", "Sainte-Hélène-de-Bagot", "Franklin Centre", "Mille-Isles", "Lyster", "Sainte-<PERSON><PERSON><PERSON>-<PERSON>-<PERSON>", "Saint-Benoît-Labre", "Maliotenam", "Cha<PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cleveland", "<PERSON><PERSON><PERSON>", "Saint-Laurent-de-l'Île-d'Orléans", "Saint-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-François-de-la-Rivière-du-Sud", "<PERSON><PERSON><PERSON>", "Shawville", "<PERSON><PERSON>", "Saint-Flavien", "Sainte-Marcelline-de-Kildare", "Rivière-Blanche", "<PERSON><PERSON><PERSON><PERSON>", "Sainte<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-<PERSON>", "<PERSON><PERSON><PERSON>", "Venise-en-Québec", "Saint-<PERSON>in", "Saint-<PERSON>", "Matagami", "Amherst", "Notre-Dame-du-Laus", "Saint-Tite-des-Caps", "<PERSON><PERSON><PERSON>", "Saint-Malachie", "Salluit", "Saint-Louis-de-Gonzague", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tring-Jonction", "<PERSON><PERSON><PERSON>", "Saint<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "L’ Îsle-Verte", "Palmarolle", "Saint-Odilon-de-Cranbourne", "La Doré", "Lac-au-Saumon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pointe-aux-Outardes", "Rivière-Héva", "<PERSON>", "Godmanchester", "<PERSON><PERSON><PERSON>", "Tingwick", "<PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON>-du-Parc", "Saint<PERSON><PERSON><PERSON><PERSON>", "Berthier-sur-Mer", "Frampton", "Chute-aux-Outardes", "New Carlisle", "Saint-Majorique-de-Grantham", "Wentworth-Nord", "Sainte-Ursule", "Nantes", "Lac-aux-Sables", "Vaudreuil-sur-le-Lac", "Amulet", "L’Avenir", "Pointe-à-la-Croix", "Hérouxville", "L'Isle-aux-Allumettes", "Sainte-Brigide-d'Iberville", "Les Éboulements", "Sainte-Barbe", "Saint-Louis-du-Ha! Ha!", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rivière-Bleue", "<PERSON><PERSON>", "Notre-Dame-du-Portage", "Saint<PERSON><PERSON><PERSON>", "Sainte-Anne-du-Sault", "La Conception", "L'Isle-aux-Coudres", "Sainte-Lucie-des-Laurentides", "Saint<PERSON><PERSON>", "Roxton Falls", "Clarendon", "Saint-<PERSON><PERSON><PERSON>", "Ra<PERSON>", "Saint-Z<PERSON><PERSON>", "Saint-<PERSON>", "Saint-<PERSON><PERSON><PERSON>-de-Lotbinière", "Saint-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Saint-Hubert-de-Rivière-du-Loup", "Saint-<PERSON>", "La Minerve", "Trécesson", "Notre-Dame-des-Pins", "Saint-Alban", "Saint-Pierre-les-Becquets", "Labrecque", "Sainte-Hénédine", "L'Anse-Saint-Jean", "Akwesasne", "Saint-Valère", "Saint-<PERSON><PERSON>-d'Arthabaska", "Saint-<PERSON><PERSON><PERSON>", "Saint-Modeste", "Saint-Siméon", "Saint-Bar<PERSON><PERSON><PERSON>", "Bury", "Lac-Bouchette", "Saint-Lazare-de-Bellechasse", "Saint-Michel-du-Squatec", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Saint<PERSON><PERSON>", "Sainte-Marie-Salomé", "Saint-<PERSON><PERSON><PERSON>", "Très-Saint-Sacrement", "Saints-Ang<PERSON>", "Saint<PERSON><PERSON><PERSON>ain-Premier", "Sainte-Agathe-de-Lotbinière", "Grande-Vallée", "Mont-Carmel", "Saint-<PERSON>", "Notre-Dame-des-Neiges", "Saint-Léon-de-Standon", "Sayabec", "Sainte-Sabine", "Saint-Maxime-du-Mont-Louis", "Blanc-Sa<PERSON><PERSON>", "Ayer’s Cliff", "<PERSON>", "Sainte-<PERSON>", "<PERSON><PERSON><PERSON>", "Sainte-Jeanne-d'Arc", "Sainte-<PERSON><PERSON><PERSON><PERSON>", "Girardville", "Saint-Bruno-<PERSON>", "Upton", "Saint-Na<PERSON>isse-de-Beaurivage", "Plaisance", "Roxton-Sud", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON><PERSON>de-Rimouski", "Saint-Patrice-de-Beaurivage", "Sainte-<PERSON><PERSON>", "Notre-Dame-du-Nord", "Saint-Aimé-des-Lacs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Coleraine", "Saint-Bonaventure", "Saint<PERSON><PERSON><PERSON><PERSON>", "Sainte-Geneviève-de-Batiscan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Riviere-Ouelle", "Stukely-Sud", "Saint-Georges-de-Clarenceville", "Sainte-Thérèse-de-Gaspé", "Sainte-Pétronille", "<PERSON><PERSON><PERSON>", "La Macaza", "Saint-Vallier", "Bristol", "Saint-<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Longue-Rive", "Saint-Léonard-de-Portneuf", "<PERSON><PERSON><PERSON><PERSON>", "Baie-du-Febvre", "Durham-Sud", "Melbourne", "Hébertville", "Lorrainville", "Saint<PERSON><PERSON><PERSON><PERSON>", "Eastman", "<PERSON><PERSON><PERSON><PERSON>", "Cookshire", "Laurie<PERSON>", "Ripon", "Henryville", "Gracefield", "Yamaska-Est", "Frelighsburg"]}, {"name": "Saskatchewan", "code": "", "cities": ["Saskatoon", "Regina", "<PERSON>", "<PERSON>", "Lloydminster", "Swift Current", "<PERSON><PERSON>", "North Battleford", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Estevan", "Martensville", "Corman Park No. 344", "Mel<PERSON>", "Humboldt", "Meadow Lake", "La Ronge", "<PERSON>lin Flon", "White City", "Kindersley", "<PERSON>", "Edenwold No. 158", "<PERSON><PERSON><PERSON>", "Battleford", "<PERSON> No. 461", "Buckland No. 491", "Tisdale", "La Loche", "Vanscoy No. 345", "Pelican Narrows", "Pilot <PERSON><PERSON>", "Unity", "Meadow Lake No. 588", "Moosomin", "Esterhazy", "Rosetown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rosthern No. 403", "Outlook", "Canora", "Biggar", "Maple Creek", "Dundurn No. 314", "Britannia No. 502", "<PERSON>", "Swift Current No. 137", "Blucher", "Lumsden No. 189", "Fort Qu’Appelle", "Indian Head", "Watrous", "Orkney No. 244", "<PERSON><PERSON><PERSON>", "Lumsden", "Regina Beach", "<PERSON><PERSON><PERSON>", "Wynyard", "Dalmeny", "Balgonie", "Rost<PERSON>n", "Shellbrook No. 493", "<PERSON><PERSON>", "Lang<PERSON>", "Hudson Bay", "Frenchman <PERSON>", "Wilton No. 472", "Torch River No. 488", "Shellbrook", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Laird No. 404", "Canwood No. 494", "Spiritwood No. 496", "Oxbow", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ile-à-la-Crosse", "Estevan No. 5", "<PERSON><PERSON><PERSON>", "South Qu'Appelle No. 157", "Mervin No. 499", "<PERSON><PERSON>", "Beaver River", "Moose Jaw No. 161", "Langenburg", "Maidstone", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Foam Lake", "Hudson Bay No. 394", "Waldheim", "Buffalo Narrows", "Air Ronge", "Weyburn No. 67", "Grenfell", "St. Louis No. 431", "Pinehouse", "Preeceville", "Maple Creek No. 111", "Birch Hills", "Kerr<PERSON><PERSON>", "<PERSON><PERSON>", "Kindersley No. 290", "<PERSON>", "Battle River No. 438", "<PERSON><PERSON><PERSON>", "Longlaketon No. 219", "Nipawin No. 487", "Duck Lake No. 463", "Gravelbourg", "Lajord No. 128"]}, {"name": "Yukon", "code": "", "cities": ["Whitehorse", "<PERSON>"]}]