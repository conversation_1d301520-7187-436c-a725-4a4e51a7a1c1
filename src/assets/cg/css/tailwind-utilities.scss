/**
 * 模拟 Tailwind CSS 工具类
 * 这个文件实现了 Tailwind CSS 常用的工具类，用于支持从 cg-homepage 迁移过来的组件
 */
 .tailwind-utilities {
  /* Flexbox & Grid */
  .flex { display: flex; }
  .flex-col { flex-direction: column; }
  .items-start { align-items: flex-start; }
  .items-center { align-items: center; }
  .justify-center { justify-content: center; }
  .justify-between { justify-content: space-between; }
  .gap-2 { gap: 0.5rem; }
  .gap-6 { gap: 1.5rem; }
  .gap-8 { gap: 2rem; }

  /* Width & Height */
  .w-full { width: 100%; }
  .w-auto { width: auto; }
  .w-\[300px\] { width: 300px; }
  .w-\[490px\] { width: 490px; }
  .w-\[500px\] { width: 500px; }
  .w-\[calc\(100\%-500px\)\] { width: calc(100% - 500px); }
  .h-full { height: 100%; }
  .h-\[48px\] { height: 48px; }
  .h-\[50px\] { height: 50px; }
  .h-15 { height: 3.75rem; }
  .h-6 { height: 1.5rem; }
  .h-8 { height: 2rem; }
  .h-10 { height: 2.5rem; }

  /* Spacing */
  .mt-2 { margin-top: 0.5rem; }
  .mt-3 { margin-top: 0.75rem; }
  .mt-4 { margin-top: 1rem; }
  .mt-8 { margin-top: 2rem; }
  .mt-9 { margin-top: 2.25rem; }
  .mt-16 { margin-top: 4rem; }
  .mt-\[30px\] { margin-top: 30px; }
  .mt-\[60px\] { margin-top: 60px; }
  .mr-2 { margin-right: 0.5rem; }
  .mr-6 { margin-right: 1.5rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mb-5 { margin-bottom: 1.25rem; }
  .mb-6 { margin-bottom: 1.5rem; }
  .mb-8 { margin-bottom: 2rem; }
  .mb-12 { margin-bottom: 3rem; }
  .ml-2 { margin-left: 0.5rem; }
  .ml-6 { margin-left: 1.5rem; }
  .p-3 { padding: 0.75rem; }
  .p-6 { padding: 1.5rem; }
  .px-4 { padding-left: 1rem; padding-right: 1rem; }
  .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
  .py-8 { padding-top: 2rem; padding-bottom: 2rem; }
  .pb-0 { padding-bottom: 0; }
  .pb-16 { padding-bottom: 4rem; }
  .pt-6 { padding-top: 1.5rem; }
  .pt-\[4\%\] { padding-top: 4%; }

  /* Typography */
  .text-xs { font-size: 0.75rem; line-height: 1rem; }
  .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .text-base { font-size: 1rem; line-height: 1.5rem; }
  .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .font-medium { font-weight: 500; }
  .font-semibold { font-weight: 600; }
  .font-bold { font-weight: 700; }
  .text-center { text-align: center; }
  .text-left { text-align: left; }

  /* Colors */
  .text-default { color: #111C1C; }
  .text-primary { color: #10B3B7; }
  .text-\[\#F56C6C\] { color: #F56C6C; }
  .text-\[\#10b3b7\] { color: #10b3b7; }
  .text-gray-500 { color: #6b7280; }
  .text-gray-600 { color: #4b5563; }
  .text-gray-700 { color: #374151; }
  .text-gray-800 { color: #1f2937; }
  .text-blue-500 { color: #3b82f6; }
  .text-blue-600 { color: #2563eb; }
  .text-blue-700 { color: #1d4ed8; }
  .text-green-700 { color: #047857; }
  .text-white { color: #ffffff; }
  .bg-white { background-color: #ffffff; }
  .bg-gray-50 { background-color: #f9fafb; }
  .bg-gray-200 { background-color: #e5e7eb; }
  .bg-blue-100 { background-color: #dbeafe; }
  .bg-blue-500 { background-color: #3b82f6; }
  .bg-blue-600 { background-color: #2563eb; }
  .bg-green-50 { background-color: #f0fdf4; }
  .border-gray-200 { border-color: #e5e7eb; }
  .border-t { border-top-width: 1px; }

  /* Utilities */
  .rounded { border-radius: 0.25rem; }
  .rounded-md { border-radius: 0.375rem; }
  .rounded-lg { border-radius: 0.5rem; }
  .rounded-full { border-radius: 9999px; }
  .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
  .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
  .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
  .tw-hidden .hidden { display: none; }
  .hidden { display: none; }
  .block { display: block; }
  .inline-block { display: inline-block; }
  .relative { position: relative; }
  .absolute { position: absolute; }
  .left-\[25px\] { left: 25px; }
  .right-0 { right: 0; }
  .cursor-pointer { cursor: pointer; }
  .z-50 { z-index: 50; }
  .overflow-y-auto { overflow-y: auto; }
  .overflow-hidden { overflow: hidden; }
  .space-x-4 > *:not(:first-child) { margin-left: 1rem; }
  .space-x-8 > *:not(:first-child) { margin-left: 2rem; }
  .space-y-2 > *:not(:first-child) { margin-top: 0.5rem; }
  .container { width: 100%; }
  .max-w-2xl { max-width: 42rem; }
  .max-w-7xl { max-width: 80rem; }
  .flex-auto { flex: 1 1 auto; }
  .grow-0 { flex-grow: 0; }
  .shrink-0 { flex-shrink: 0; }
  .grid { display: grid; }
  .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
  .transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
  .hover\:transform:hover { transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1); }
  .hover\:scale-105:hover { transform: scale(1.05); }
  .hover\:bg-gray-100:hover { background-color: #f3f4f6; }
  .hover\:bg-blue-500:hover { background-color: #3b82f6; }
  .hover\:bg-blue-600:hover { background-color: #2563eb; }
  .hover\:text-blue-500:hover { color: #3b82f6; }
  .hover\:text-blue-700:hover { color: #1d4ed8; }
  .mx-auto { margin-left: auto; margin-right: auto; }

  /* Responsive Design */
  @media (min-width: 640px) {
    .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  }

  @media (min-width: 768px) {
    .md\:flex { display: flex; }
    .md\:hidden { display: none; }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:items-start { align-items: flex-start; }
    .md\:flex-row { flex-direction: row; }
    .md\:mb-0 { margin-bottom: 0; }
    .md\:w-\[calc\(100\%-500px\)\] { width: calc(100% - 500px); }
  }

  @media (min-width: 1024px) {
    .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  }

  @media (max-width: 768px) {
    .login-dialog { padding: 0 16px; }
  }

  /* Important Modifiers */
  .\!mb-6 { margin-bottom: 1.5rem !important; }
  .\!my-8 { margin-top: 2rem !important; margin-bottom: 2rem !important; }

  /* 补充 Flexbox 相关类 */
  .flex-wrap { flex-wrap: wrap; }
  .flex-nowrap { flex-wrap: nowrap; }
  .items-end { align-items: flex-end; }
  .justify-end { justify-content: flex-end; }

  /* 补充宽高类 */
  .h-auto { height: auto; }

  /* 补充间距类 */
  .m-0 { margin: 0; }
  .m-2 { margin: 0.5rem; }
  .m-4 { margin: 1rem; }
  .my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
  .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }

  /* 补充边框相关类 */
  .border { border-width: 1px; }

  /* 登录页面特有的样式 */
  .login-dialog {
    .el-dialog {
      border-radius: 20px;
      padding: 0;
    }
  }

  /* 补充负边距类 */
  .-my-3 { margin-top: -0.75rem; margin-bottom: -0.75rem; }
  .-mt-1 { margin-top: -0.25rem; }
  .-mt-2 { margin-top: -0.5rem; }
  .-mt-5 { margin-top: -1.25rem; }
  .-mb-1 { margin-bottom: -0.25rem; }
  .-mb-2 { margin-bottom: -0.5rem; }

  /* 补充文本颜色类 */
  .text-red-500 { color: #ef4444; }
  .text-red-600 { color: #dc2626; }
  .text-red-700 { color: #b91c1c; }

  /* 密码登录组件特定样式 */
  .is-invalid {
    border-color: #F56C6C !important;
    
    .el-input__inner {
      border-color: #F56C6C !important;
    }
  }

  /* 输入框通用样式调整 */
  .el-input {
    &.h-\[48px\] {
      .el-input__inner {
        height: 48px;
        line-height: 48px;
      }
    }
  }

  /* 按钮组件样式调整 */
  .el-button {
    &.h-\[48px\] {
      height: 48px !important;
      line-height: 48px !important;
    }
  }

  /* 补充特定尺寸的外边距 */
  .mt-1 { margin-top: 0.25rem; }
  .mt-6 { margin-top: 1.5rem; }
  .mt-\[30px\] { margin-top: 30px; }
  .mb-\[60px\] { margin-bottom: 60px; }
  .mb-\[100px\] { margin-bottom: 100px; }

  /* 补充特定文本颜色 */
  .text-\[#F56C6C\] { color: #F56C6C; }

  /* 补充特定字体系列 */
  .font-\[\'Inter\'\] { font-family: 'Inter', sans-serif; }

  /* el-divider 修饰符 */
  .\!my-8 { margin-top: 2rem !important; margin-bottom: 2rem !important; }

  /* 补充高度类 */
  .h-12 { height: 3rem; } /* 对应 48px */
  .h-\[600px\] { height: 600px; }
  .w-12 { width: 3rem; }

  /* 按钮禁用状态样式 */
  .el-button {
    &[disabled] {
      opacity: 0.65;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  /* 输入框高度扩展 */
  .el-input {
    &.h-12 {
      .el-input__inner {
        height: 3rem;
        line-height: 3rem;
      }
    }
  }

  /* 补充padding类 */
  .p-5 { padding: 1.25rem; }
  .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

  /* 补充文本相关类 */
  .break-words { overflow-wrap: break-word; }

  /* 补充flex-row类 */
  .flex-row { flex-direction: row; }

  /* 垂直居中对话框 */
  .vertical-center-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .el-dialog {
      margin-top: 0 !important; 
    }
  }

  /* 隐藏对话框头部 */
  .hide-header-dialog {
    .el-dialog__header {
      display: none;
    }
  }

  /* 邮箱验证相关样式 */
  .input-square {
    width: 40px !important;
    height: 40px !important;
    margin: 0 5px;
    
    .el-input__inner {
      text-align: center;
      font-size: 1.25rem;
      padding: 0;
      height: 40px !important;
    }
  }

  .input-verified {
    .el-input__inner {
      color: #10B3B7;
      border-color: #10B3B7;
    }
  }

}