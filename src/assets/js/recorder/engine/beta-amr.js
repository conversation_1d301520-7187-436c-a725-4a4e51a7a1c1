/*
录音
https://github.com/xiangyuecn/Recorder
src: engine/beta-amr.js,engine/beta-amr-engine.js,engine/wav.js
*/
!function(){"use strict";Recorder.prototype.enc_amr={stable:!1,testmsg:"采样率比特率设置无效，只提供8000hz，AMR12.2(12.8kbps)"},Recorder.amr2wav=function(e,i,r){var n=new FileReader;n.onload=function(){var e=new Uint8Array(n.result);Recorder.AMR.decode(e,function(e){Recorder({type:"wav"}).mock(e,8e3).stop(function(e,r){i(e,r)},r)},r)},n.readAsArrayBuffer(e)},Recorder.prototype.amr=function(e,i,r){var n=this.set;e.length;n.bitRate=12.8;var t=n.sampleRate;if(8e3!=t)return console.log("amr mock start"),n.sampleRate=8e3,void Recorder(n).mock(e,t).stop(function(e,r){console.log("amr mock end"),i(e)},r);Recorder.AMR.encode(e,function(e){i(new Blob([e.buffer],{type:"audio/amr"}))})}}(),function(){"use strict";var p,e={decode:function(n,t,e){var o=this;if(String.fromCharCode.apply(null,n.subarray(0,this.AMR_HEADER.length))===this.AMR_HEADER){var a=this.Decoder_Interface_init(),f=new Int16Array(Math.floor(n.length/6*this.PCM_BUFFER_COUNT)),r=p._malloc(this.AMR_BUFFER_COUNT),s=new Uint8Array(p.HEAPU8.buffer,r,this.AMR_BUFFER_COUNT);r=p._malloc(2*this.PCM_BUFFER_COUNT);var l=new Int16Array(p.HEAPU8.buffer,r,this.PCM_BUFFER_COUNT),u=6,c=0,d=function(){for(var e=0;u+1<n.length&&c+1<f.length;){var r=o.SIZES[n[u]>>3&15];if(u+r+1>n.length)break;if(s.set(n.subarray(u,u+r+1)),o.Decoder_Interface_Decode(a,s.byteOffset,l.byteOffset,0),c+o.PCM_BUFFER_COUNT>f.length){var i=new Int16Array(2*f.length);i.set(f.subarray(0,c)),f=i}if(f.set(l,c),c+=o.PCM_BUFFER_COUNT,u+=r+1,2e4<(e+=r+1))return void setTimeout(d)}p._free(s.byteOffset),p._free(l.byteOffset),o.Decoder_Interface_exit(a),t(f.subarray(0,c))};d()}else e("非AMR音频数据")},encode:function(i,n){var t=this,o=this.Encoder_Interface_init(),e=p._malloc(2*this.PCM_BUFFER_COUNT),a=new Int16Array(p.HEAPU8.buffer,e,this.PCM_BUFFER_COUNT);e=p._malloc(this.AMR_BUFFER_COUNT);for(var f=new Uint8Array(p.HEAPU8.buffer,e,this.AMR_BUFFER_COUNT),s=this.SIZES[7]+1,l=new Uint8Array(Math.ceil(i.length/this.PCM_BUFFER_COUNT*s)+this.AMR_HEADER.length),r=0;r<this.AMR_HEADER.length;r++)l[r]=this.AMR_HEADER.charCodeAt(r);var u=0,c=this.AMR_HEADER.length,d=function(){for(var e=0;u+t.PCM_BUFFER_COUNT<i.length&&c+s<l.length;){a.set(i.subarray(u,u+t.PCM_BUFFER_COUNT));var r=t.Encoder_Interface_Encode(o,7,a.byteOffset,f.byteOffset,0);if(r!=s){console.error([r,s]);break}if(l.set(f.subarray(0,r),c),u+=t.PCM_BUFFER_COUNT,c+=r,4e4<(e+=t.PCM_BUFFER_COUNT))return void setTimeout(d)}p._free(a.byteOffset),p._free(f.byteOffset),t.Encoder_Interface_exit(o),n(l.subarray(0,c))};d()},Decoder_Interface_init:function(){return console.warn("Decoder_Interface_init not initialized."),0},Decoder_Interface_exit:function(e){console.warn("Decoder_Interface_exit not initialized.")},Decoder_Interface_Decode:function(e,r,i,n){console.warn("Decoder_Interface_Decode not initialized.")},Encoder_Interface_init:function(e){return console.warn("Encoder_Interface_init not initialized."),0},Encoder_Interface_exit:function(e){console.warn("Encoder_Interface_exit not initialized.")},Encoder_Interface_Encode:function(e,r,i,n,t){console.warn("Encoder_Interface_Encode not initialized.")},Mode:{MR475:0,MR515:1,MR59:2,MR67:3,MR74:4,MR795:5,MR102:6,MR122:7,MRDTX:8},SIZES:[12,13,15,17,19,20,26,31,5,6,5,5,0,0,0,0],AMR_BUFFER_COUNT:32,PCM_BUFFER_COUNT:160,AMR_HEADER:"#!AMR\n",WAV_HEADER_SIZE:44};(p={canvas:{},print:function(e){console.log(e)},_main:function(){return e.Decoder_Interface_init=p._Decoder_Interface_init,e.Decoder_Interface_exit=p._Decoder_Interface_exit,e.Decoder_Interface_Decode=p._Decoder_Interface_Decode,e.Encoder_Interface_init=p._Encoder_Interface_init,e.Encoder_Interface_exit=p._Encoder_Interface_exit,e.Encoder_Interface_Encode=p._Encoder_Interface_Encode,0}})||(p=(void 0!==p?p:null)||{});var r={};for(var i in p)p.hasOwnProperty(i)&&(r[i]=p[i]);var n="object"==typeof window,u="function"==typeof importScripts,t=!n&&!u;if(t)p.print||(p.print=print),"undefined"!=typeof printErr&&(p.printErr=printErr),"undefined"!=typeof read?p.read=read:p.read=function(){throw"no read() available (jsc?)"},p.readBinary=function(e){if("function"==typeof readbuffer)return new Uint8Array(readbuffer(e));var r=read(e,"binary");return w("object"==typeof r),r},"undefined"!=typeof scriptArgs?p.arguments=scriptArgs:void 0!==arguments&&(p.arguments=arguments);else{if(!n&&!u)throw"Unknown runtime environment. Where are we?";if(p.read=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},void 0!==arguments&&(p.arguments=arguments),"undefined"!=typeof console)p.print||(p.print=function(e){console.log(e)}),p.printErr||(p.printErr=function(e){console.log(e)});else{p.print||(p.print=function(e){})}u&&(p.load=importScripts),void 0===p.setWindowTitle&&(p.setWindowTitle=function(e){document.title=e})}for(var i in!p.load&&p.read&&(p.load=function(e){var r;r=p.read(e),eval.call(null,r)}),p.print||(p.print=function(){}),p.printErr||(p.printErr=p.print),p.arguments||(p.arguments=[]),p.thisProgram||(p.thisProgram="./this.program"),p.print=p.print,p.printErr=p.printErr,p.preRun=[],p.postRun=[],r)r.hasOwnProperty(i)&&(p[i]=r[i]);var v={setTempRet0:function(e){f=e},getTempRet0:function(){return f},stackSave:function(){return B},stackRestore:function(e){B=e},getNativeTypeSize:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:if("*"===e[e.length-1])return v.QUANTUM_SIZE;if("i"===e[0]){var r=parseInt(e.substr(1));return w(r%8==0),r/8}return 0}},getNativeFieldSize:function(e){return Math.max(v.getNativeTypeSize(e),v.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(e,r){return"double"===r||"i64"===r?7&e&&(w(4==(7&e)),e+=4):w(0==(3&e)),e},getAlignSize:function(e,r,i){return i||"i64"!=e&&"double"!=e?e?Math.min(r||(e?v.getNativeFieldSize(e):0),v.QUANTUM_SIZE):Math.min(r,8):8},dynCall:function(e,r,i){return i&&i.length?(i.splice||(i=Array.prototype.slice.call(i)),i.splice(0,0,r),p["dynCall_"+e].apply(null,i)):p["dynCall_"+e].call(null,r)},functionPointers:[],addFunction:function(e){for(var r=0;r<v.functionPointers.length;r++)if(!v.functionPointers[r])return v.functionPointers[r]=e,2*(1+r);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(e){v.functionPointers[(e-2)/2]=null},warnOnce:function(e){v.warnOnce.shown||(v.warnOnce.shown={}),v.warnOnce.shown[e]||(v.warnOnce.shown[e]=1,p.printErr(e))},funcWrappers:{},getFuncWrapper:function(e,r){w(r),v.funcWrappers[r]||(v.funcWrappers[r]={});var i=v.funcWrappers[r];return i[e]||(i[e]=function(){return v.dynCall(r,e,arguments)}),i[e]},getCompilerSetting:function(e){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(e){var r=B;return B=(B=B+e|0)+15&-16,r},staticAlloc:function(e){var r=P;return P=(P=P+e|0)+15&-16,r},dynamicAlloc:function(e){var r=x;if(j<=(x=(x=x+e|0)+15&-16)&&!void Be("Cannot enlarge memory arrays. Either (1) compile with -s TOTAL_MEMORY=X with X higher than the current value "+j+", (2) compile with ALLOW_MEMORY_GROWTH which adjusts the size at runtime but prevents some optimizations, or (3) set Module.TOTAL_MEMORY before the program runs."))return x=r,0;return r},alignMemory:function(e,r){return e=Math.ceil(e/(r||16))*(r||16)},makeBigInt:function(e,r,i){return i?+(e>>>0)+4294967296*+(r>>>0):+(e>>>0)+4294967296*+(0|r)},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};p.Runtime=v;var o,a,f,s=!1;function w(e,r){e||Be("Assertion failed: "+r)}function m(e,r,i,n){switch("*"===(i=i||"i8").charAt(i.length-1)&&(i="i32"),i){case"i1":case"i8":A[e>>0]=r;break;case"i16":S[e>>1]=r;break;case"i32":M[e>>2]=r;break;case"i64":a=[r>>>0,(o=r,1<=+ie(o)?0<o?(0|oe(+te(o/4294967296),4294967295))>>>0:~~+ne((o-+(~~o>>>0))/4294967296)>>>0:0)],M[e>>2]=a[0],M[e+4>>2]=a[1];break;case"float":O[e>>2]=r;break;case"double":L[e>>3]=r;break;default:Be("invalid type for setValue: "+i)}}function l(e,r,i){switch("*"===(r=r||"i8").charAt(r.length-1)&&(r="i32"),r){case"i1":case"i8":return A[e>>0];case"i16":return S[e>>1];case"i32":case"i64":return M[e>>2];case"float":return O[e>>2];case"double":return L[e>>3];default:Be("invalid type for setValue: "+r)}return null}p.setValue=m,p.getValue=l;var b=2,k=4;function c(e,r,i,n){var t,o;"number"==typeof e?(t=!0,o=e):(t=!1,o=e.length);var a,f="string"==typeof r?r:null;if(a=i==k?n:[Oe,v.stackAlloc,v.staticAlloc,v.dynamicAlloc][void 0===i?b:i](Math.max(o,f?1:r.length)),t){var s;n=a;for(w(0==(3&a)),s=a+(-4&o);n<s;n+=4)M[n>>2]=0;for(s=a+o;n<s;)A[n++>>0]=0;return a}if("i8"===f)return e.subarray||e.slice?D.set(e,a):D.set(new Uint8Array(e),a),a;for(var l,u,c,d=0;d<o;){var h=e[d];"function"==typeof h&&(h=v.getFunctionIndex(h)),0!==(l=f||r[d])?("i64"==l&&(l="i32"),m(a+d,h,l),c!==l&&(u=v.getNativeTypeSize(l),c=l),d+=u):d++}return a}function E(e,r){if(0===r||!e)return"";for(var i,n=0,t=0;n|=i=D[e+t>>0],(0!=i||r)&&(t++,!r||t!=r););r||(r=t);var o="";if(n<128){for(var a;0<r;)a=String.fromCharCode.apply(String,D.subarray(e,e+Math.min(r,1024))),o=o?o+a:a,e+=1024,r-=1024;return o}return p.UTF8ToString(e)}function d(e,r){for(var i,n,t,o,a,f="";;){if(!(i=e[r++]))return f;if(128&i)if(n=63&e[r++],192!=(224&i))if(t=63&e[r++],224==(240&i)?i=(15&i)<<12|n<<6|t:(o=63&e[r++],240==(248&i)?i=(7&i)<<18|n<<12|t<<6|o:(a=63&e[r++],i=248==(252&i)?(3&i)<<24|n<<18|t<<12|o<<6|a:(1&i)<<30|n<<24|t<<18|o<<12|a<<6|63&e[r++])),i<65536)f+=String.fromCharCode(i);else{var s=i-65536;f+=String.fromCharCode(55296|s>>10,56320|1023&s)}else f+=String.fromCharCode((31&i)<<6|n);else f+=String.fromCharCode(i)}}function h(e,r,i,n){if(!(0<n))return 0;for(var t=i,o=i+n-1,a=0;a<e.length;++a){var f=e.charCodeAt(a);if(55296<=f&&f<=57343&&(f=65536+((1023&f)<<10)|1023&e.charCodeAt(++a)),f<=127){if(o<=i)break;r[i++]=f}else if(f<=2047){if(o<=i+1)break;r[i++]=192|f>>6,r[i++]=128|63&f}else if(f<=65535){if(o<=i+2)break;r[i++]=224|f>>12,r[i++]=128|f>>6&63,r[i++]=128|63&f}else if(f<=2097151){if(o<=i+3)break;r[i++]=240|f>>18,r[i++]=128|f>>12&63,r[i++]=128|f>>6&63,r[i++]=128|63&f}else if(f<=67108863){if(o<=i+4)break;r[i++]=248|f>>24,r[i++]=128|f>>18&63,r[i++]=128|f>>12&63,r[i++]=128|f>>6&63,r[i++]=128|63&f}else{if(o<=i+5)break;r[i++]=252|f>>30,r[i++]=128|f>>24&63,r[i++]=128|f>>18&63,r[i++]=128|f>>12&63,r[i++]=128|f>>6&63,r[i++]=128|63&f}}return r[i]=0,i-t}function g(e){for(var r=0,i=0;i<e.length;++i){var n=e.charCodeAt(i);55296<=n&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++i)),n<=127?++r:r+=n<=2047?2:n<=65535?3:n<=2097151?4:n<=67108863?5:6}return r}function y(c){var e=!!p.___cxa_demangle;if(e)try{var r=Oe(c.length);ee(c.substr(1),r);var i=Oe(4),n=p.___cxa_demangle(r,0,0,i);if(0===l(i,"i32")&&n)return E(n)}catch(e){}finally{r&&Re(r),i&&Re(i),n&&Re(n)}var d=3,h={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},w=[],m=!0;var t=c;try{if("Object._main"==c||"_main"==c)return"main()";if("number"==typeof c&&(c=E(c)),"_"!==c[0])return c;if("_"!==c[1])return c;if("Z"!==c[2])return c;switch(c[3]){case"n":return"operator new()";case"d":return"operator delete()"}t=function e(r,i,n){i=i||1/0;var t,o="",a=[];if("N"===c[d]){if(t=function(){"K"===c[++d]&&d++;for(var e=[];"E"!==c[d];)if("S"!==c[d])if("C"!==c[d]){var r=parseInt(c.substr(d)),i=r.toString().length;if(!r||!i){d--;break}var n=c.substr(d+i,r);e.push(n),w.push(n),d+=i+r}else e.push(e[e.length-1]),d+=2;else{d++;var t=c.indexOf("_",d),o=c.substring(d,t)||0;e.push(w[o]||"?"),d=t+1}return d++,e}().join("::"),0==--i)return r?[t]:t}else if(("K"===c[d]||m&&"L"===c[d])&&d++,u=parseInt(c.substr(d))){var f=u.toString().length;t=c.substr(d+f,u),d+=f+u}if(m=!1,"I"===c[d]){d++;var s=e(!0);o+=e(!0,1,!0)[0]+" "+t+"<"+s.join(", ")+">"}else o=t;e:for(;d<c.length&&0<i--;){var l=c[d++];if(l in h)a.push(h[l]);else switch(l){case"P":a.push(e(!0,1,!0)[0]+"*");break;case"R":a.push(e(!0,1,!0)[0]+"&");break;case"L":d++;var u=c.indexOf("E",d)-d;a.push(c.substr(d,u)),d+=u+2;break;case"A":if(u=parseInt(c.substr(d)),d+=u.toString().length,"_"!==c[d])throw"?";d++,a.push(e(!0,1,!0)[0]+" ["+u+"]");break;case"E":break e;default:o+="?"+l;break e}}return n||1!==a.length||"void"!==a[0]||(a=[]),r?(o&&a.push(o+"?"),a):o+"("+a.join(", ")+")"}()}catch(e){t+="?"}return 0<=t.indexOf("?")&&!e&&v.warnOnce("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),t}function _(){return function(){var r=new Error;if(!r.stack){try{throw new Error(0)}catch(e){r=e}if(!r.stack)return"(no stack trace available)"}return r.stack.toString()}().replace(/__Z[\w\d_]+/g,function(e){var r=y(e);return e===r?e:e+" ["+r+"]"})}p.ALLOC_NORMAL=0,p.ALLOC_STACK=1,p.ALLOC_STATIC=b,p.ALLOC_DYNAMIC=3,p.ALLOC_NONE=k,p.allocate=c,p.getMemory=function(e){return C?void 0!==de&&!de.called||!K?v.dynamicAlloc(e):Oe(e):v.staticAlloc(e)},p.Pointer_stringify=E,p.AsciiToString=function(e){for(var r="";;){var i=A[e++>>0];if(!i)return r;r+=String.fromCharCode(i)}},p.stringToAscii=function(e,r){return re(e,r,!1)},p.UTF8ArrayToString=d,p.UTF8ToString=function(e){return d(D,e)},p.stringToUTF8Array=h,p.stringToUTF8=function(e,r,i){return h(e,D,r,i)},p.lengthBytesUTF8=g,p.UTF16ToString=function(e){for(var r=0,i="";;){var n=S[e+2*r>>1];if(0==n)return i;++r,i+=String.fromCharCode(n)}},p.stringToUTF16=function(e,r,i){if(void 0===i&&(i=2147483647),i<2)return 0;for(var n=r,t=(i-=2)<2*e.length?i/2:e.length,o=0;o<t;++o){var a=e.charCodeAt(o);S[r>>1]=a,r+=2}return S[r>>1]=0,r-n},p.lengthBytesUTF16=function(e){return 2*e.length},p.UTF32ToString=function(e){for(var r=0,i="";;){var n=M[e+4*r>>2];if(0==n)return i;if(++r,65536<=n){var t=n-65536;i+=String.fromCharCode(55296|t>>10,56320|1023&t)}else i+=String.fromCharCode(n)}},p.stringToUTF32=function(e,r,i){if(void 0===i&&(i=2147483647),i<4)return 0;for(var n=r,t=n+i-4,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(55296<=a&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o)),M[r>>2]=a,t<(r+=4)+4)break}return M[r>>2]=0,r-n},p.lengthBytesUTF32=function(e){for(var r=0,i=0;i<e.length;++i){var n=e.charCodeAt(i);55296<=n&&n<=57343&&++i,r+=4}return r},p.stackTrace=_;var A,D,S,R,M,N,O,L,F=4096;var I,T,P=0,C=!1,B=0,x=0;for(var U,z=p.TOTAL_STACK||65536,j=p.TOTAL_MEMORY||524288,q=65536;q<j||q<2*z;)q<16777216?q*=2:q+=16777216;function H(e){for(;0<e.length;){var r=e.shift();if("function"!=typeof r){var i=r.func;"number"==typeof i?void 0===r.arg?v.dynCall("v",i):v.dynCall("vi",i,[r.arg]):i(void 0===r.arg?null:r.arg)}else r()}}q!==j&&(p.printErr("increasing TOTAL_MEMORY to "+q+" to be compliant with the asm.js spec (and given that TOTAL_STACK="+z+")"),j=q),w("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&!!new Int32Array(1).subarray&&!!new Int32Array(1).set,"JS engine does not provide full typed array support"),U=new ArrayBuffer(j),A=new Int8Array(U),S=new Int16Array(U),M=new Int32Array(U),D=new Uint8Array(U),R=new Uint16Array(U),N=new Uint32Array(U),O=new Float32Array(U),L=new Float64Array(U),w((M[0]=255)===D[0]&&0===D[3],"Typed arrays 2 must be run on a little-endian system"),p.HEAP=void 0,p.buffer=U,p.HEAP8=A,p.HEAP16=S,p.HEAP32=M,p.HEAPU8=D,p.HEAPU16=R,p.HEAPU32=N,p.HEAPF32=O,p.HEAPF64=L;var Y=[],X=[],V=[],G=[],W=[],K=!1;function Z(){K||(K=!0,H(X))}function Q(e){Y.unshift(e)}function J(e){W.unshift(e)}function $(e,r,i){var n=0<i?i:g(e)+1,t=new Array(n),o=h(e,t,0,t.length);return r&&(t.length=o),t}function ee(e,r,i){for(var n=$(e,i),t=0;t<n.length;){var o=n[t];A[r+t>>0]=o,t+=1}}function re(e,r,i){for(var n=0;n<e.length;++n)A[r++>>0]=e.charCodeAt(n);i||(A[r>>0]=0)}p.addOnPreRun=Q,p.addOnInit=function(e){X.unshift(e)},p.addOnPreMain=function(e){V.unshift(e)},p.addOnExit=function(e){G.unshift(e)},p.addOnPostRun=J,p.intArrayFromString=$,p.intArrayToString=function(e){for(var r=[],i=0;i<e.length;i++){var n=e[i];255<n&&(n&=255),r.push(String.fromCharCode(n))}return r.join("")},p.writeStringToMemory=ee,p.writeArrayToMemory=function(e,r){for(var i=0;i<e.length;i++)A[r++>>0]=e[i]},p.writeAsciiToMemory=re,Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(e,r){var i=65535&e,n=65535&r;return i*n+((e>>>16)*n+i*(r>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(e){e>>>=0;for(var r=0;r<32;r++)if(e&1<<31-r)return r;return 32}),Math.clz32=Math.clz32;var ie=Math.abs,ne=(Math.cos,Math.sin,Math.tan,Math.acos,Math.asin,Math.atan,Math.atan2,Math.exp,Math.log,Math.sqrt,Math.ceil),te=Math.floor,oe=(Math.pow,Math.imul,Math.fround,Math.min),ae=(Math.clz32,0),fe=null,se=null;function le(e){ae++,p.monitorRunDependencies&&p.monitorRunDependencies(ae)}function ue(e){if(ae--,p.monitorRunDependencies&&p.monitorRunDependencies(ae),0==ae&&(null!==fe&&(clearInterval(fe),fe=null),se)){var r=se;se=null,r()}}p.addRunDependency=le,p.removeRunDependency=ue,p.preloadedImages={},p.preloadedAudios={};P=31784,X.push(),c([154,14,0,0,188,14,0,0,226,14,0,0,8,15,0,0,46,15,0,0,84,15,0,0,130,15,0,0,208,15,0,0,66,16,0,0,108,16,0,0,42,17,0,0,248,17,0,0,228,18,0,0,240,19,0,0,24,21,0,0,86,22,0,0,238,23,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,13,0,15,0,17,0,19,0,20,0,26,0,31,0,5,0,6,0,5,0,5,0,0,0,0,0,0,0,0,0,1,252,146,252,36,253,182,253,72,254,218,254,108,255,0,0,0,0,32,78,32,78,32,78,32,78,32,78,80,70,0,64,0,32,0,0,0,0,255,127,112,125,112,125,112,125,112,125,112,125,153,89,255,127,112,125,112,125,102,102,102,38,153,25,153,25,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,0,96,0,72,0,54,128,40,96,30,200,22,22,17,209,12,157,9,54,7,102,70,184,38,75,21,182,11,113,6,139,3,243,1,18,1,151,0,83,0,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,44,3,128,0,30,2,140,0,57,11,111,4,218,8,74,13,19,8,51,2,133,49,135,2,36,16,6,7,225,21,165,20,9,30,118,1,151,14,185,1,160,42,78,10,31,46,190,9,10,80,29,3,98,20,163,2,68,26,162,32,162,20,160,6,208,5,172,1,250,22,196,1,212,20,232,15,255,13,244,4,165,9,133,3,22,62,237,3,134,58,199,12,91,40,250,18,51,14,229,7,36,10,67,3,72,48,28,19,174,47,168,6,120,52,68,6,158,35,37,9,128,15,2,6,103,21,208,38,211,14,161,1,79,5,158,1,56,14,33,6,59,31,213,13,141,44,133,2,104,33,123,2,216,15,97,5,224,64,236,23,156,44,188,2,215,7,95,2,127,48,42,6,111,43,46,18,112,53,172,6,214,46,205,4,60,31,129,28,175,51,83,22,124,9,135,4,25,8,149,7,74,24,233,23,218,13,12,7,221,34,10,7,231,33,44,6,111,54,248,13,1,52,93,24,254,23,106,4,106,23,198,6,61,55,54,18,7,44,249,12,194,47,15,6,107,54,199,11,217,19,224,40,228,36,50,26,153,6,171,2,156,5,26,5,44,28,93,15,242,15,153,10,113,30,192,2,222,58,34,3,155,24,92,20,241,16,237,20,20,26,29,2,174,23,114,2,83,53,116,14,234,44,104,9,28,63,204,2,145,47,239,2,129,31,225,44,170,24,208,8,114,17,240,1,125,28,11,2,229,39,249,14,202,32,221,11,211,32,198,3,148,55,88,7,255,33,33,21,11,64,255,18,252,28,187,7,201,23,206,4,155,36,46,17,222,56,35,13,247,52,57,11,107,51,185,5,158,21,142,6,82,51,179,57,170,28,88,2,38,5,36,2,156,16,211,13,60,39,60,9,91,41,110,2,32,51,157,2,46,55,198,13,175,19,56,38,234,59,107,2,43,12,78,2,58,64,197,11,182,60,72,16,177,60,75,6,45,60,204,4,151,62,83,36,110,29,112,19,198,7,189,4,183,44,133,4,224,48,143,21,3,37,84,10,36,30,242,7,224,51,191,8,139,62,229,19,130,31,105,26,99,39,133,5,138,19,43,9,235,48,87,23,22,59,83,11,88,71,241,8,211,61,223,9,137,63,14,40,59,57,55,44,5,7,81,1,43,12,141,1,182,13,112,11,240,17,110,10,95,29,116,2,151,44,144,2,58,23,131,9,144,25,199,28,46,32,61,3,160,15,95,3,48,39,188,9,185,62,223,13,28,71,30,4,215,23,174,5,252,22,220,30,64,73,140,13,72,7,32,2,238,35,171,2,103,45,64,16,242,17,108,6,86,12,133,4,81,62,0,10,61,48,149,14,12,68,140,20,218,23,212,7,101,11,206,6,83,64,137,20,147,65,144,6,53,67,223,6,165,18,159,12,218,28,147,23,6,56,28,39,195,15,186,1,98,16,202,1,254,35,194,8,3,29,121,16,60,50,33,3,178,43,57,3,104,49,36,8,156,50,154,25,33,37,228,3,229,25,217,3,41,41,198,9,185,59,142,19,58,49,7,8,124,60,117,6,66,63,9,27,151,55,158,22,66,10,60,3,239,21,150,6,95,53,146,22,84,14,18,6,49,44,73,10,42,38,179,5,179,54,125,18,25,62,147,24,134,24,78,7,230,30,237,8,82,66,219,17,192,64,9,15,144,59,7,9,151,62,172,12,123,56,144,69,71,46,203,10,189,7,127,5,120,5,108,3,239,16,219,13,39,17,114,16,29,21,168,2,53,68,13,3,101,25,254,19,155,31,253,29,187,28,26,3,141,32,158,4,193,58,88,12,80,58,223,11,197,79,112,3,209,56,84,3,49,48,116,57,248,26,128,7,129,16,165,3,26,32,63,4,163,41,244,15,98,39,181,17,175,10,72,3,177,80,57,4,71,65,78,23,1,62,226,17,119,42,14,10,189,14,142,4,183,56,204,15,219,80,67,10,115,59,174,10,170,59,138,8,113,24,154,12,69,51,24,76,28,28,162,3,158,9,82,6,163,17,20,12,28,54,181,16,220,40,65,3,187,67,42,3,251,65,241,8,186,60,25,32,35,53,148,6,125,12,42,7,76,62,4,11,196,61,207,20,110,66,134,9,148,65,46,5,55,61,220,31,206,45,108,33,178,14,5,8,91,37,37,5,249,52,134,26,195,47,144,7,244,31,222,13,231,51,242,6,171,63,199,25,163,63,78,30,73,33,247,9,57,28,85,10,93,71,65,29,245,65,200,8,218,69,68,11,113,67,0,13,201,36,194,78,34,43,128,32,6,5,108,2,151,5,71,2,105,23,241,8,138,15,42,14,24,20,240,2,97,52,62,3,177,21,44,11,244,45,20,23,241,41,48,2,70,21,52,2,9,52,192,11,170,46,99,14,175,77,30,3,97,38,216,2,95,53,44,34,223,28,237,11,211,9,10,3,162,23,65,3,69,25,210,19,113,32,159,9,253,23,73,7,204,59,238,4,72,56,195,17,95,53,163,17,65,12,167,11,175,9,235,4,240,58,39,18,22,60,47,10,156,56,88,9,174,48,233,9,115,29,133,11,109,50,28,47,92,21,172,2,69,12,210,2,217,19,250,4,188,49,104,16,198,59,169,2,139,30,80,2,134,25,229,7,94,64,33,34,52,52,114,3,21,21,131,3,64,57,130,8,149,57,131,16,190,55,18,5,105,54,237,7,117,60,58,29,199,61,220,17,217,9,221,7,198,19,12,7,39,20,182,25,218,27,13,14,168,42,75,6,209,45,172,6,7,66,127,13,140,63,240,25,90,36,239,3,153,36,58,8,238,74,173,19,153,48,173,16,47,62,52,5,253,59,184,13,122,46,61,55,229,62,198,26,218,7,225,2,195,14,93,3,190,44,64,11,236,13,212,13,97,35,217,4,103,48,128,3,98,33,21,18,41,45,144,22,193,31,77,2,26,32,76,2,40,73,171,14,173,50,77,12,113,61,246,2,250,64,242,2,118,59,130,43,255,61,160,8,65,18,98,2,234,39,166,2,153,59,50,16,97,22,255,12,185,32,134,6,150,77,17,9,90,60,135,21,230,54,105,21,96,22,72,11,156,29,66,5,48,56,205,20,108,63,110,15,14,59,160,14,202,59,155,5,5,57,230,15,13,48,80,61,193,29,163,6,122,8,116,3,107,17,215,17,174,70,234,12,198,49,47,3,78,58,139,3,168,58,185,16,158,60,176,32,74,70,63,4,54,9,97,3,153,63,203,14,63,61,244,17,228,63,254,5,200,64,162,8,193,65,225,37,57,62,161,17,205,12,61,4,171,37,139,8,197,46,180,23,239,35,110,17,251,34,93,6,49,40,246,11,97,64,35,20,106,60,154,27,110,53,239,9,153,20,229,8,106,65,69,24,15,65,80,13,80,79,35,13,0,73,193,7,92,55,67,50,50,59,87,61,121,17,252,3,145,6,118,3,215,16,205,16,248,34,73,14,5,23,123,4,127,45,172,5,14,62,179,8,230,17,244,25,17,27,181,4,76,24,31,3,127,48,81,13,96,62,37,15,147,77,61,8,217,37,93,8,150,57,126,34,144,56,39,10,25,7,214,4,91,30,45,3,135,74,58,17,178,21,16,8,103,14,28,11,27,68,208,8,57,65,134,17,71,63,12,21,92,31,203,10,77,13,71,8,18,68,101,21,130,53,226,10,167,77,160,10,138,35,40,15,252,70,225,18,184,67,175,47,252,19,228,3,71,19,220,3,160,38,9,12,126,23,251,20,9,62,131,6,213,32,159,4,239,58,62,9,65,77,90,27,187,46,26,6,111,28,104,4,219,65,252,5,146,61,5,21,116,57,17,8,137,78,107,8,6,67,53,32,247,69,174,24,91,21,224,5,4,16,14,10,13,68,154,26,41,22,72,11,252,64,54,13,15,35,39,7,191,78,129,18,94,76,126,28,2,26,221,10,208,44,249,12,197,75,190,19,190,73,114,18,55,64,69,9,206,79,34,17,89,44,158,103,73,45,252,11,50,11,30,6,244,19,46,4,142,37,51,19,75,19,208,13,117,29,110,3,237,80,83,3,26,27,43,17,159,65,53,30,153,39,251,3,117,38,196,3,134,60,115,15,99,60,102,13,175,73,214,3,152,78,195,3,236,65,87,50,254,55,104,16,199,25,196,4,6,36,46,3,46,66,14,20,29,22,34,19,112,21,6,7,34,79,122,15,109,66,34,24,9,70,41,23,149,36,92,13,50,29,179,7,81,76,57,20,59,74,190,11,70,64,204,14,198,62,63,9,216,33,183,10,229,36,246,102,104,42,7,5,227,13,241,3,230,21,38,14,253,75,136,21,165,48,29,3,154,80,143,3,67,60,250,11,141,66,35,40,195,73,73,10,73,15,244,4,63,76,43,13,132,70,110,20,91,75,142,6,52,76,100,12,152,70,2,42,241,64,189,26,62,12,250,8,117,42,133,9,220,60,1,27,53,49,53,13,108,43,225,12,122,65,120,9,165,73,59,26,19,67,159,38,199,49,45,10,233,34,68,12,89,74,84,30,171,71,40,15,251,79,98,14,146,76,52,13,244,50,173,75,30,41,84,90,1,0,3,0,0,0,1,0,2,0,4,0,82,120,26,113,81,106,240,99,241,93,78,88,2,83,7,78,89,73,242,68,51,115,174,103,80,93,251,83,149,75,6,68,56,61,25,55,150,49,161,44,205,76,21,46,166,27,151,16,244,9,249,5,149,3,38,2,74,1,198,0,249,79,26,80,59,80,92,80,125,80,164,80,197,80,236,80,13,81,52,81,85,81,124,81,157,81,196,81,236,81,19,82,58,82,97,82,137,82,176,82,215,82,255,82,38,83,84,83,123,83,169,83,208,83,254,83,38,84,84,84,129,84,175,84,221,84,11,85,57,85,103,85,149,85,201,85,247,85,43,86,89,86,142,86,194,86,247,86,43,87,95,87,148,87,200,87,3,88,56,88,115,88,174,88,233,88,36,89,95,89,154,89,219,89,22,90,88,90,153,90,212,90,28,91,94,91,159,91,231,91,48,92,113,92,192,92,8,93,80,93,159,93,237,93,60,94,138,94,224,94,46,95,131,95,217,95,52,96,138,96,229,96,72,97,163,97,6,98,104,98,209,98,51,99,156,99,11,100,123,100,234,100,96,101,214,101,76,102,201,102,76,103,207,103,82,104,220,104,108,105,252,105,147,106,48,107,205,107,113,108,27,109,204,109,125,110,59,111,249,111,197,112,150,113,111,114,84,115,64,116,50,117,50,118,63,119,88,120,225,122,255,127,255,127,255,127,255,127,255,127,255,127,255,127,225,122,88,120,63,119,50,118,50,117,64,116,84,115,111,114,150,113,197,112,249,111,59,111,125,110,204,109,27,109,113,108,205,107,48,107,147,106,252,105,108,105,220,104,82,104,207,103,76,103,201,102,76,102,214,101,96,101,234,100,123,100,11,100,156,99,51,99,209,98,104,98,6,98,163,97,72,97,229,96,138,96,52,96,217,95,131,95,46,95,224,94,138,94,60,94,237,93,159,93,80,93,8,93,192,92,113,92,48,92,231,91,159,91,94,91,28,91,212,90,153,90,88,90,22,90,219,89,154,89,95,89,36,89,233,88,174,88,115,88,56,88,3,88,200,87,148,87,95,87,43,87,247,86,194,86,142,86,89,86,43,86,247,85,201,85,149,85,103,85,57,85,11,85,221,84,175,84,129,84,84,84,38,84,254,83,208,83,169,83,123,83,84,83,38,83,255,82,215,82,176,82,137,82,97,82,58,82,19,82,236,81,196,81,157,81,124,81,85,81,52,81,13,81,236,80,197,80,164,80,125,80,92,80,59,80,26,80,249,79,210,79,177,79,145,79,112,79,13,0,14,0,16,0,18,0,20,0,21,0,27,0,32,0,6,0,7,0,6,0,6,0,0,0,0,0,0,0,1,0,13,0,14,0,16,0,18,0,19,0,21,0,26,0,31,0,6,0,6,0,6,0,6,0,0,0,0,0,0,0,1,0,79,115,156,110,74,97,126,77,72,54,9,31,195,10,153,251,125,242,48,239,127,240,173,244,231,249,176,254,22,2,202,3,255,3,55,3,4,2,220,0,0,0,125,255,62,255,41,255,0,0,216,127,107,127,182,126,187,125,123,124,248,122,53,121,53,119,250,116,137,114,128,46,128,67,0,120,0,101,128,94,64,113,64,95,192,28,64,76,192,57,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,10,0,19,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,94,0,0,0,253,255,3,0,3,0,6,0,5,0,9,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,19,0,19,0,19,0,19,0,23,0,39,0,57,0,5,0,8,0,8,0,7,0,8,0,7,0,2,0,8,0,4,0,7,0,2,0,4,0,7,0,2,0,8,0,4,0,7,0,2,0,8,0,8,0,7,0,8,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,8,0,9,0,9,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,9,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,9,0,9,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,9,0,9,0,9,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,9,0,9,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,7,0,8,0,9,0,8,0,6,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,3,0,8,0,9,0,9,0,6,0,95,0,103,0,118,0,134,0,148,0,159,0,204,0,244,0,39,0,43,0,38,0,37,0,0,0,0,0,0,0,0,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,23,0,24,0,25,0,26,0,27,0,28,0,48,0,49,0,61,0,62,0,82,0,83,0,47,0,46,0,45,0,44,0,81,0,80,0,79,0,78,0,17,0,18,0,20,0,22,0,77,0,76,0,75,0,74,0,29,0,30,0,43,0,42,0,41,0,40,0,38,0,39,0,16,0,19,0,21,0,50,0,51,0,59,0,60,0,63,0,64,0,72,0,73,0,84,0,85,0,93,0,94,0,32,0,33,0,35,0,36,0,53,0,54,0,56,0,57,0,66,0,67,0,69,0,70,0,87,0,88,0,90,0,91,0,34,0,55,0,68,0,89,0,37,0,58,0,71,0,92,0,31,0,52,0,65,0,86,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,23,0,24,0,25,0,26,0,27,0,46,0,65,0,84,0,45,0,44,0,43,0,64,0,63,0,62,0,83,0,82,0,81,0,102,0,101,0,100,0,42,0,61,0,80,0,99,0,28,0,47,0,66,0,85,0,18,0,41,0,60,0,79,0,98,0,29,0,48,0,67,0,17,0,20,0,22,0,40,0,59,0,78,0,97,0,21,0,30,0,49,0,68,0,86,0,19,0,16,0,87,0,39,0,38,0,58,0,57,0,77,0,35,0,54,0,73,0,92,0,76,0,96,0,95,0,36,0,55,0,74,0,93,0,32,0,51,0,33,0,52,0,70,0,71,0,89,0,90,0,31,0,50,0,69,0,88,0,37,0,56,0,75,0,94,0,34,0,53,0,72,0,91,0,0,0,1,0,4,0,5,0,3,0,6,0,7,0,2,0,13,0,15,0,8,0,9,0,11,0,12,0,14,0,10,0,16,0,28,0,74,0,29,0,75,0,27,0,73,0,26,0,72,0,30,0,76,0,51,0,97,0,50,0,71,0,96,0,117,0,31,0,77,0,52,0,98,0,49,0,70,0,95,0,116,0,53,0,99,0,32,0,78,0,33,0,79,0,48,0,69,0,94,0,115,0,47,0,68,0,93,0,114,0,46,0,67,0,92,0,113,0,19,0,21,0,23,0,22,0,18,0,17,0,20,0,24,0,111,0,43,0,89,0,110,0,64,0,65,0,44,0,90,0,25,0,45,0,66,0,91,0,112,0,54,0,100,0,40,0,61,0,86,0,107,0,39,0,60,0,85,0,106,0,36,0,57,0,82,0,103,0,35,0,56,0,81,0,102,0,34,0,55,0,80,0,101,0,42,0,63,0,88,0,109,0,41,0,62,0,87,0,108,0,38,0,59,0,84,0,105,0,37,0,58,0,83,0,104,0,0,0,1,0,4,0,3,0,5,0,6,0,13,0,7,0,2,0,8,0,9,0,11,0,15,0,12,0,14,0,10,0,28,0,82,0,29,0,83,0,27,0,81,0,26,0,80,0,30,0,84,0,16,0,55,0,109,0,56,0,110,0,31,0,85,0,57,0,111,0,48,0,73,0,102,0,127,0,32,0,86,0,51,0,76,0,105,0,130,0,52,0,77,0,106,0,131,0,58,0,112,0,33,0,87,0,19,0,23,0,53,0,78,0,107,0,132,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,50,0,75,0,104,0,129,0,47,0,72,0,101,0,126,0,54,0,79,0,108,0,133,0,46,0,71,0,100,0,125,0,128,0,103,0,74,0,49,0,45,0,70,0,99,0,124,0,42,0,67,0,96,0,121,0,39,0,64,0,93,0,118,0,38,0,63,0,92,0,117,0,35,0,60,0,89,0,114,0,34,0,59,0,88,0,113,0,44,0,69,0,98,0,123,0,43,0,68,0,97,0,122,0,41,0,66,0,95,0,120,0,40,0,65,0,94,0,119,0,37,0,62,0,91,0,116,0,36,0,61,0,90,0,115,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,16,0,26,0,87,0,27,0,88,0,28,0,89,0,29,0,90,0,30,0,91,0,51,0,80,0,112,0,141,0,52,0,81,0,113,0,142,0,54,0,83,0,115,0,144,0,55,0,84,0,116,0,145,0,58,0,119,0,59,0,120,0,21,0,22,0,23,0,17,0,18,0,19,0,31,0,60,0,92,0,121,0,56,0,85,0,117,0,146,0,20,0,24,0,25,0,50,0,79,0,111,0,140,0,57,0,86,0,118,0,147,0,49,0,78,0,110,0,139,0,48,0,77,0,53,0,82,0,114,0,143,0,109,0,138,0,47,0,76,0,108,0,137,0,32,0,33,0,61,0,62,0,93,0,94,0,122,0,123,0,41,0,42,0,43,0,44,0,45,0,46,0,70,0,71,0,72,0,73,0,74,0,75,0,102,0,103,0,104,0,105,0,106,0,107,0,131,0,132,0,133,0,134,0,135,0,136,0,34,0,63,0,95,0,124,0,35,0,64,0,96,0,125,0,36,0,65,0,97,0,126,0,37,0,66,0,98,0,127,0,38,0,67,0,99,0,128,0,39,0,68,0,100,0,129,0,40,0,69,0,101,0,130,0,8,0,7,0,6,0,5,0,4,0,3,0,2,0,14,0,16,0,9,0,10,0,12,0,13,0,15,0,11,0,17,0,20,0,22,0,24,0,23,0,19,0,18,0,21,0,56,0,88,0,122,0,154,0,57,0,89,0,123,0,155,0,58,0,90,0,124,0,156,0,52,0,84,0,118,0,150,0,53,0,85,0,119,0,151,0,27,0,93,0,28,0,94,0,29,0,95,0,30,0,96,0,31,0,97,0,61,0,127,0,62,0,128,0,63,0,129,0,59,0,91,0,125,0,157,0,32,0,98,0,64,0,130,0,1,0,0,0,25,0,26,0,33,0,99,0,34,0,100,0,65,0,131,0,66,0,132,0,54,0,86,0,120,0,152,0,60,0,92,0,126,0,158,0,55,0,87,0,121,0,153,0,117,0,116,0,115,0,46,0,78,0,112,0,144,0,43,0,75,0,109,0,141,0,40,0,72,0,106,0,138,0,36,0,68,0,102,0,134,0,114,0,149,0,148,0,147,0,146,0,83,0,82,0,81,0,80,0,51,0,50,0,49,0,48,0,47,0,45,0,44,0,42,0,39,0,35,0,79,0,77,0,76,0,74,0,71,0,67,0,113,0,111,0,110,0,108,0,105,0,101,0,145,0,143,0,142,0,140,0,137,0,133,0,41,0,73,0,107,0,139,0,37,0,69,0,103,0,135,0,38,0,70,0,104,0,136,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,16,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,26,0,27,0,28,0,29,0,30,0,31,0,115,0,116,0,117,0,118,0,119,0,120,0,72,0,73,0,161,0,162,0,65,0,68,0,69,0,108,0,111,0,112,0,154,0,157,0,158,0,197,0,200,0,201,0,32,0,33,0,121,0,122,0,74,0,75,0,163,0,164,0,66,0,109,0,155,0,198,0,19,0,23,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,37,0,36,0,35,0,34,0,80,0,79,0,78,0,77,0,126,0,125,0,124,0,123,0,169,0,168,0,167,0,166,0,70,0,67,0,71,0,113,0,110,0,114,0,159,0,156,0,160,0,202,0,199,0,203,0,76,0,165,0,81,0,82,0,92,0,91,0,93,0,83,0,95,0,85,0,84,0,94,0,101,0,102,0,96,0,104,0,86,0,103,0,87,0,97,0,127,0,128,0,138,0,137,0,139,0,129,0,141,0,131,0,130,0,140,0,147,0,148,0,142,0,150,0,132,0,149,0,133,0,143,0,170,0,171,0,181,0,180,0,182,0,172,0,184,0,174,0,173,0,183,0,190,0,191,0,185,0,193,0,175,0,192,0,176,0,186,0,38,0,39,0,49,0,48,0,50,0,40,0,52,0,42,0,41,0,51,0,58,0,59,0,53,0,61,0,43,0,60,0,44,0,54,0,194,0,179,0,189,0,196,0,177,0,195,0,178,0,187,0,188,0,151,0,136,0,146,0,153,0,134,0,152,0,135,0,144,0,145,0,105,0,90,0,100,0,107,0,88,0,106,0,89,0,98,0,99,0,62,0,47,0,57,0,64,0,45,0,63,0,46,0,55,0,56,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,23,0,15,0,16,0,17,0,18,0,19,0,20,0,21,0,22,0,24,0,25,0,26,0,27,0,28,0,38,0,141,0,39,0,142,0,40,0,143,0,41,0,144,0,42,0,145,0,43,0,146,0,44,0,147,0,45,0,148,0,46,0,149,0,47,0,97,0,150,0,200,0,48,0,98,0,151,0,201,0,49,0,99,0,152,0,202,0,86,0,136,0,189,0,239,0,87,0,137,0,190,0,240,0,88,0,138,0,191,0,241,0,91,0,194,0,92,0,195,0,93,0,196,0,94,0,197,0,95,0,198,0,29,0,30,0,31,0,32,0,33,0,34,0,35,0,50,0,100,0,153,0,203,0,89,0,139,0,192,0,242,0,51,0,101,0,154,0,204,0,55,0,105,0,158,0,208,0,90,0,140,0,193,0,243,0,59,0,109,0,162,0,212,0,63,0,113,0,166,0,216,0,67,0,117,0,170,0,220,0,36,0,37,0,54,0,53,0,52,0,58,0,57,0,56,0,62,0,61,0,60,0,66,0,65,0,64,0,70,0,69,0,68,0,104,0,103,0,102,0,108,0,107,0,106,0,112,0,111,0,110,0,116,0,115,0,114,0,120,0,119,0,118,0,157,0,156,0,155,0,161,0,160,0,159,0,165,0,164,0,163,0,169,0,168,0,167,0,173,0,172,0,171,0,207,0,206,0,205,0,211,0,210,0,209,0,215,0,214,0,213,0,219,0,218,0,217,0,223,0,222,0,221,0,73,0,72,0,71,0,76,0,75,0,74,0,79,0,78,0,77,0,82,0,81,0,80,0,85,0,84,0,83,0,123,0,122,0,121,0,126,0,125,0,124,0,129,0,128,0,127,0,132,0,131,0,130,0,135,0,134,0,133,0,176,0,175,0,174,0,179,0,178,0,177,0,182,0,181,0,180,0,185,0,184,0,183,0,188,0,187,0,186,0,226,0,225,0,224,0,229,0,228,0,227,0,232,0,231,0,230,0,235,0,234,0,233,0,238,0,237,0,236,0,96,0,199,0,0,0,2,0,0,0,3,0,0,0,2,0,0,0,3,0,1,0,3,0,2,0,4,0,1,0,4,0,1,0,4,0,0,0,205,12,156,25,0,32,102,38,205,44,0,48,51,51,102,54,154,57,205,60,0,64,51,67,102,70,154,73,205,76,159,0,64,241,53,167,206,0,190,242,52,176,12,1,67,244,88,185,93,1,201,245,133,194,163,1,215,246,223,200,226,1,166,247,189,205,42,2,116,248,147,210,125,2,66,249,109,215,221,2,18,250,77,220,74,3,222,250,30,225,201,3,174,251,0,230,90,4,124,252,216,234,1,5,74,253,179,239,193,5,25,254,141,244,158,6,231,254,104,249,156,7,181,255,67,254,193,8,133,0,33,3,17,10,83,1,252,7,147,11,33,2,213,12,80,13,240,2,178,17,79,15,190,3,140,22,155,17,141,4,104,27,63,20,91,5,67,32,72,23,41,6,29,37,199,26,248,6,249,41,203,30,199,7,212,46,105,35,149,8,175,51,185,40,100,9,138,56,222,48,113,10,224,62,135,63,244,11,253,71,150,82,120,13,27,81,93,107,252,14,57,90,93,107,252,14,57,90,0,0,1,0,3,0,2,0,6,0,4,0,5,0,7,0,0,0,1,0,3,0,2,0,5,0,6,0,4,0,7,0,248,127,211,127,76,127,108,126,51,125,163,123,188,121,127,119,239,116,12,114,217,110,89,107,141,103,121,99,31,95,130,90,166,85,141,80,60,75,182,69,0,64,28,58,15,52,223,45,141,39,32,33,156,26,6,20,97,13,178,6,0,0,78,249,159,242,250,235,100,229,224,222,115,216,33,210,241,203,228,197,0,192,74,186,196,180,115,175,90,170,126,165,225,160,135,156,115,152,167,148,39,145,244,141,17,139,129,136,68,134,93,132,205,130,148,129,180,128,45,128,8,128,255,127,46,124,174,120,118,117,125,114,186,111,41,109,194,106,131,104,102,102,105,100,137,98,194,96,19,95,122,93,245,91,130,90,33,89,207,87,139,86,85,85,44,84,15,83,252,81,244,80,246,79,1,79,20,78,48,77,83,76,126,75,175,74,231,73,37,73,104,72,178,71,0,71,84,70,173,69,10,69,107,68,209,67,59,67,168,66,25,66,142,65,6,65,130,64,0,64,0,0,175,5,50,11,140,16,192,21,207,26,188,31,136,36,53,41,196,45,55,50,143,54,206,58,245,62,4,67,252,70,223,74,174,78,105,82,17,86,167,89,44,93,159,96,3,100,87,103,155,106,209,109,250,112,20,116,33,119,34,122,23,125,255,127,255,127,217,127,98,127,157,126,138,125,42,124,125,122,133,120,66,118,182,115,227,112,202,109,110,106,208,102,242,98,215,94,130,90,246,85,52,81,64,76,29,71,206,65,87,60,186,54,252,48,31,43,40,37,26,31,249,24,200,18,140,12,72,6,0,0,184,249,116,243,56,237,7,231,230,224,216,218,225,212,4,207,70,201,169,195,50,190,227,184,192,179,204,174,10,170,126,165,41,161,14,157,48,153,146,149,54,146,29,143,74,140,190,137,123,135,131,133,214,131,118,130,99,129,158,128,39,128,0,128,249,150,148,221,53,235,27,241,93,244,116,246,223,247,237,248,184,249,86,250,214,250,61,251,148,251,221,251,26,252,78,252,123,252,163,252,197,252,227,252,252,252,18,253,38,253,55,253,69,253,81,253,91,253,100,253,106,253,111,253,114,253,116,253,116,253,114,253,111,253,106,253,100,253,91,253,81,253,69,253,55,253,38,253,18,253,252,252,227,252,197,252,163,252,123,252,78,252,26,252,221,251,148,251,61,251,214,250,86,250,184,249,237,248,223,247,116,246,93,244,27,241,53,235,148,221,249,150,48,117,144,101,8,82,152,58,64,31,0,0,192,224,104,197,248,173,112,154,153,104,33,3,201,9,85,253,154,250,70,2,92,2,6,251,183,13,250,232,182,17,13,254,108,248,195,11,62,236,238,21,58,248,219,251,77,250,90,17,68,253,41,235,1,18,196,1,179,253,232,242,137,11,243,4,68,251,226,245,195,6,86,14,133,238,49,252,39,17,23,246,181,3,173,250,45,252,102,22,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,98,57,254,44,244,4,55,245,217,233,90,29,221,255,9,245,32,244,215,18,136,11,24,223,201,14,175,5,131,8,67,222,115,31,201,247,82,250,9,3,84,4,175,246,206,8,149,254,94,253,201,247,158,23,207,233,48,4,51,12,62,236,192,20,231,246,112,241,12,27,207,240,163,2,17,249,29,0,161,39,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,0,64,103,65,213,66,76,68,203,69,82,71,226,72,122,74,28,76,199,77,123,79,56,81,255,82,209,84,172,86,146,88,130,90,126,92,132,94,150,96,180,98,221,100,18,103,84,105,162,107,254,109,102,112,221,114,96,117,242,119,147,122,66,125,255,127,3,115,186,110,119,98,225,79,109,57,245,33,71,12,184,250,206,238,23,233,38,233,191,237,33,245,96,253,187,4,232,9,58,12,175,11,211,8,146,4,0,0,23,252,140,249,180,248,126,249,133,251,48,254,218,0,244,2,36,4,75,4,136,3,38,2,135,0,11,255,254,253,134,253,166,253,61,254,25,255,0,0,191,0,52,1,84,1,40,1,198,0,78,0,220,255,136,255,93,255,91,255,124,255,177,255,237,255,34,0,73,0,91,0,89,0,70,0,38,0,0,0,254,254,194,254,73,254,134,253,112,253,251,252,57,253,10,254,244,254,63,255,254,255,125,0,122,0,217,255,247,255,105,0,129,0,27,1,116,1,63,2,235,254,188,254,59,255,25,254,67,254,150,254,220,254,229,255,177,0,31,2,86,1,5,2,4,2,130,0,27,0,152,255,136,255,116,255,182,255,200,255,204,253,81,252,16,250,59,252,210,252,242,253,190,254,254,255,159,0,145,2,200,254,228,254,126,254,171,253,19,254,242,253,94,254,27,255,105,0,193,1,211,253,154,252,205,251,105,252,74,252,16,253,59,253,196,254,62,0,230,1,198,254,65,255,53,255,182,254,96,255,153,255,205,255,131,0,82,1,3,2,10,6,224,8,194,14,112,21,60,27,190,32,63,39,221,43,222,49,146,53,84,37,17,42,27,49,236,51,45,56,131,45,92,41,39,38,145,33,84,25,6,0,82,0,125,255,154,0,200,255,33,253,183,0,191,255,247,254,9,0,46,255,151,254,113,0,206,2,25,7,242,3,190,4,37,6,89,3,53,5,228,8,59,3,32,6,141,7,205,2,197,7,158,8,70,3,148,4,31,7,209,2,232,3,106,8,30,1,220,1,229,5,9,255,237,253,230,0,147,0,174,255,57,2,26,0,79,255,80,252,229,255,239,254,180,2,92,255,248,254,73,255,224,0,22,3,15,4,131,3,178,3,89,2,229,1,3,3,126,4,12,2,165,2,135,3,116,255,119,1,10,3,154,1,164,2,173,1,45,1,18,2,241,3,207,2,134,2,38,0,226,0,111,1,40,0,145,0,211,255,7,254,34,1,121,0,135,255,46,1,127,0,166,0,132,255,129,254,68,252,154,254,57,254,47,252,203,2,110,3,126,3,210,3,155,3,211,0,221,1,16,1,64,0,188,0,178,255,17,0,113,255,191,255,38,0,131,2,74,2,109,2,122,255,86,254,117,253,91,1,33,2,4,11,164,4,166,10,138,9,142,0,176,255,199,6,27,1,130,0,205,1,250,254,113,254,135,251,101,254,155,0,174,1,73,1,119,1,11,3,53,0,30,255,117,255,127,255,20,255,146,6,29,1,232,2,47,5,226,2,185,2,128,6,56,1,153,1,10,1,69,1,208,2,135,0,1,0,221,0,197,1,8,0,203,0,145,0,43,1,128,2,248,2,29,0,212,1,126,2,103,0,173,1,123,1,164,1,186,3,164,3,46,5,186,4,234,4,192,2,244,3,128,4,90,255,68,254,246,254,196,254,126,255,136,254,191,0,127,4,112,7,16,255,225,253,20,251,144,255,12,1,183,4,70,0,38,4,47,6,22,1,80,5,38,6,254,254,240,254,0,253,19,0,51,2,192,8,253,255,247,254,135,0,217,254,177,253,124,254,140,0,98,1,50,255,252,254,8,254,229,252,79,254,50,253,217,250,109,0,75,1,194,3,83,254,169,255,140,2,216,254,170,1,251,3,17,255,7,3,83,3,233,1,54,5,49,4,178,254,180,254,25,0,31,2,182,4,15,7,70,1,61,0,215,2,66,2,81,3,125,5,48,255,235,254,73,1,104,255,64,0,157,2,78,254,90,253,41,253,58,254,185,255,251,0,93,2,224,1,254,0,30,254,11,0,228,3,223,254,139,1,230,1,210,2,25,4,160,5,226,255,196,254,238,252,150,255,141,255,149,253,93,3,194,5,132,5,31,4,86,5,160,4,44,3,213,4,157,3,42,0,5,255,192,253,86,1,141,0,58,254,88,255,176,255,79,5,170,254,112,253,29,249,100,0,53,3,213,2,222,3,235,2,32,3,76,1,184,1,56,2,151,2,123,1,84,3,112,0,165,0,143,254,85,2,142,3,26,1,248,255,66,3,1,5,160,254,60,2,183,2,206,1,198,8,14,7,89,1,190,0,94,5,160,1,147,3,118,8,168,0,174,255,24,1,252,253,66,254,72,3,47,0,21,2,44,0,150,254,57,253,137,251,22,0,193,0,192,5,171,255,233,0,21,7,194,255,67,2,224,5,38,2,176,3,213,6,211,2,138,2,124,4,204,3,116,3,115,5,87,254,131,2,0,0,232,3,184,3,74,4,249,0,166,5,160,2,178,254,169,255,124,8,214,253,90,7,112,10,140,0,34,7,61,7,152,3,213,6,30,10,52,4,141,7,246,7,119,255,69,254,237,249,245,4,150,4,212,1,19,254,134,255,241,5,61,254,9,4,190,4,226,1,159,6,94,4,47,3,137,2,128,1,66,254,76,253,107,0,193,254,163,253,138,255,49,255,7,254,13,2,44,254,244,255,176,10,75,0,142,7,25,5,112,3,54,9,219,8,5,5,39,6,212,7,208,255,208,254,94,251,77,254,51,254,5,255,146,254,108,254,221,253,223,254,163,253,171,253,230,253,214,252,91,255,136,255,3,0,100,1,127,2,217,4,222,5,96,0,177,0,238,2,77,254,183,253,106,251,156,254,109,0,177,255,27,254,32,1,213,7,9,0,92,4,219,2,112,3,86,8,178,3,247,254,49,6,41,4,133,4,186,4,75,3,14,254,100,253,175,1,118,1,65,1,27,255,160,5,53,8,101,5,193,1,205,1,131,4,151,255,39,0,128,254,249,254,111,1,182,0,141,254,108,253,5,3,68,255,127,4,203,3,53,5,96,6,155,5,6,3,243,4,197,4,30,254,192,252,47,250,19,255,46,255,92,3,122,3,79,6,40,4,216,1,38,4,168,4,185,0,53,4,221,3,200,253,32,252,88,249,63,254,122,252,5,248,114,255,135,254,54,254,46,255,214,253,251,251,245,255,109,4,217,8,183,254,93,253,131,252,6,255,145,2,163,4,7,2,230,5,243,6,8,2,27,2,123,5,15,2,141,5,22,5,205,253,153,252,32,251,109,255,49,254,111,3,180,255,30,9,24,11,51,2,13,10,81,9,120,2,134,7,104,11,207,2,231,7,48,7,223,253,45,253,84,4,129,0,131,255,116,3,137,5,96,6,157,3,162,255,30,6,215,6,171,254,253,5,15,6,79,2,139,1,238,254,180,255,213,3,15,11,153,0,169,11,52,7,8,4,5,10,189,10,228,5,16,11,87,7,23,3,175,4,26,2,66,255,59,254,209,5,234,254,220,253,134,4,11,255,149,7,252,7,0,4,24,6,114,6,0,2,253,0,210,1,194,255,189,254,127,4,39,254,136,254,251,1,79,254,100,5,114,8,131,3,151,7,165,5,134,0,192,2,184,1,204,1,13,2,228,255,62,254,23,1,58,5,0,0,203,3,252,0,67,254,141,253,33,252,164,254,166,253,112,250,142,1,200,2,120,6,149,255,58,1,78,255,93,0,178,8,190,8,6,2,81,3,144,2,50,254,57,253,65,254,174,0,222,255,167,4,137,255,42,0,237,3,140,254,18,1,246,2,12,4,48,9,46,7,163,2,188,6,218,5,174,1,6,5,85,8,127,255,73,254,0,0,139,254,32,3,96,8,6,0,51,6,174,9,222,1,84,2,80,8,84,254,32,253,225,5,129,1,178,0,212,3,139,0,193,1,201,4,242,253,182,252,42,252,145,0,18,6,218,4,111,2,168,5,144,2,93,1,248,3,202,5,31,0,232,254,159,1,196,254,212,2,105,6,104,1,34,4,44,2,76,254,154,254,177,4,157,254,99,4,147,7,145,1,48,6,200,8,241,253,12,252,99,1,233,0,238,0,185,8,218,253,127,252,129,253,147,254,11,254,165,7,133,1,68,7,85,6,162,0,108,4,240,4,19,255,150,4,110,5,128,253,101,254,116,0,28,255,158,6,250,8,103,6,138,8,219,8,50,2,249,4,98,10,67,1,82,1,238,6,66,2,83,4,84,3,22,0,82,2,166,3,113,255,206,2,190,1,50,0,71,0,247,255,174,254,70,253,129,250,102,0,118,255,204,252,202,254,43,254,133,251,158,1,67,0,245,254,36,4,46,3,161,5,12,6,80,5,248,4,218,6,103,7,125,6,227,7,85,8,28,7,16,7,14,9,53,7,132,2,163,255,198,1,90,3,73,1,120,255,233,1,254,254,128,255,58,255,23,253,215,255,204,255,247,254,39,252,90,1,137,0,223,1,51,249,20,253,84,253,117,251,67,249,145,254,129,252,135,251,240,252,24,254,78,252,56,252,171,255,122,254,43,253,215,0,172,254,85,255,252,3,148,3,177,7,52,2,179,0,234,2,150,2,209,3,198,6,119,3,110,2,146,3,171,3,88,3,141,4,53,1,176,2,35,3,149,3,161,0,58,2,118,0,236,255,229,254,208,252,214,255,204,0,52,251,187,254,50,254,61,252,54,255,113,255,36,252,28,254,151,254,66,253,46,252,35,254,210,254,234,252,92,251,156,255,238,252,192,251,226,251,77,252,108,249,54,255,181,252,242,252,241,251,158,250,123,252,144,253,146,255,171,255,100,1,213,0,246,255,19,254,108,1,6,3,169,1,54,3,223,1,173,255,45,2,8,2,32,252,232,249,196,253,165,253,27,253,230,255,10,254,130,253,121,252,209,0,50,1,147,0,196,254,175,253,172,253,171,255,45,255,31,255,106,252,239,253,117,0,233,0,73,254,30,253,77,4,239,2,121,2,177,5,180,6,231,5,229,6,177,5,142,3,98,4,132,4,81,3,74,5,100,3,214,1,153,252,130,251,252,248,153,252,163,252,32,252,138,255,155,0,212,0,229,251,175,252,162,253,163,251,199,248,66,245,5,252,109,250,179,248,114,1,72,255,98,254,191,3,237,1,104,0,190,3,15,4,31,2,154,0,141,2,201,0,225,4,251,1,150,0,151,2,247,1,230,0,111,2,9,3,163,2,147,2,88,0,146,255,75,3,244,0,224,0,126,1,29,2,46,1,212,2,177,1,154,2,142,4,222,2,85,1,118,255,20,0,115,254,97,251,88,254,210,255,191,254,160,254,132,255,53,5,253,3,56,4,6,1,110,1,211,2,154,3,27,1,217,253,31,0,132,253,157,253,79,253,71,253,97,254,72,252,245,252,55,255,207,250,170,253,153,254,71,252,251,250,166,0,237,1,49,1,221,0,78,3,191,2],"i8",k,v.GLOBAL_BASE),c([98,2,72,3,168,3,6,3,45,253,212,250,19,251,155,254,255,251,148,250,184,251,160,250,147,254,120,250,167,248,160,253,250,248,65,249,94,253,223,253,107,251,65,253,166,2,18,3,148,0,133,255,184,2,8,5,132,2,94,1,246,255,158,1,102,2,15,0,137,0,88,1,45,255,210,252,24,250,205,252,121,254,94,252,180,253,47,0,177,253,126,252,115,252,183,251,93,255,8,251,113,251,99,255,72,250,11,250,123,254,6,251,92,251,144,253,159,2,213,0,198,1,124,0,238,254,243,253,39,253,16,254,104,255,192,250,122,0,135,0,167,244,179,253,118,254,64,249,185,1,206,255,196,5,136,3,19,3,60,1,236,0,72,254,165,254,217,0,157,1,113,252,107,252,121,0,57,254,92,252,202,0,164,255,47,254,137,254,232,1,134,1,218,1,108,3,217,2,60,1,233,248,224,250,99,253,87,0,194,3,176,1,51,2,7,255,222,251,250,0,29,1,81,4,117,4,171,1,184,2,242,251,128,249,210,249,76,252,90,1,160,0,203,254,240,254,166,252,158,2,112,2,226,4,80,252,104,254,102,253,162,253,192,254,128,254,20,254,230,0,65,0,78,1,206,255,240,255,240,255,78,253,139,250,255,6,180,6,119,5,174,9,15,8,124,5,221,4,191,5,146,5,130,254,243,251,254,255,173,0,114,254,121,4,211,5,232,7,9,7,4,3,250,4,226,5,149,5,199,6,209,7,55,4,194,4,249,4,126,251,197,248,207,250,216,252,147,251,184,251,61,254,247,251,70,249,65,0,66,2,172,255,60,250,126,246,14,249,3,253,170,250,18,254,38,255,174,253,93,252,81,1,20,255,50,2,53,9,102,10,146,7,209,5,252,4,106,3,189,0,102,1,118,1,17,250,23,247,214,246,57,252,9,251,209,247,140,253,92,251,250,249,125,6,19,4,34,2,53,2,37,4,220,2,192,255,188,252,78,254,76,254,160,255,203,0,54,4,192,4,100,6,139,3,254,5,218,3,70,1,197,3,77,3,142,0,172,255,197,0,214,1,75,9,34,6,109,4,214,1,190,4,139,1,96,5,176,4,101,4,18,4,92,1,225,253,46,251,136,254,41,255,75,255,225,1,101,248,171,249,46,255,18,253,95,251,134,1,29,0,113,254,27,0,52,3,212,4,243,2,183,2,211,3,153,1,82,255,173,4,11,4,144,3,76,5,54,7,32,252,99,250,228,1,51,250,92,249,208,0,100,254,180,4,152,5,241,254,128,3,120,4,96,254,241,6,154,5,96,249,172,245,52,255,3,249,241,249,9,4,136,249,233,249,23,5,27,251,203,249,57,4,99,253,185,251,190,255,86,253,64,1,167,254,147,2,49,1,45,4,244,250,220,252,237,255,157,249,245,250,29,0,109,249,15,254,71,0,225,254,249,255,156,255,18,254,62,252,19,255,84,3,89,7,204,6,63,251,149,250,227,0,108,253,46,1,117,1,96,0,63,4,233,4,206,251,123,249,160,0,229,1,28,8,6,7,90,252,36,255,40,2,172,253,156,253,237,0,80,1,184,6,111,3,131,2,117,2,178,1,243,4,10,2,97,6,15,0,244,0,71,254,195,5,205,2,184,0,27,7,54,6,173,6,220,3,5,1,169,3,45,8,41,9,240,5,91,8,66,7,70,6,191,253,189,253,77,251,68,252,135,0,24,254,48,254,51,0,174,254,139,253,164,254,45,253,122,4,25,8,162,5,144,8,186,5,143,3,92,250,220,249,26,247,120,5,198,2,17,5,55,5,121,2,160,3,154,5,146,8,34,10,118,9,156,8,89,7,214,3,194,8,62,7,124,1,24,3,121,4,193,255,229,253,158,1,4,255,60,252,198,254,19,251,85,253,244,252,193,252,242,253,19,252,126,249,145,251,88,254,181,249,60,254,213,254,244,4,24,4,130,2,123,4,85,3,88,3,93,253,176,254,139,0,220,8,63,5,138,5,29,0,0,3,29,3,56,251,167,1,52,2,218,250,198,251,245,0,234,250,212,252,61,2,238,250,175,249,134,2,56,252,66,3,211,2,225,3,116,6,235,7,65,255,207,252,176,1,150,2,60,0,198,0,114,2,229,3,50,5,112,6,171,7,9,5,195,249,163,255,211,255,192,251,37,0,172,255,117,6,47,10,33,9,41,4,248,7,73,9,115,4,22,9,70,8,91,3,101,1,230,5,152,2,203,4,75,4,223,1,80,5,144,3,105,7,218,6,227,7,144,4,117,7,248,6,143,1,34,0,0,1,175,253,208,254,227,251,35,2,158,6,127,5,135,2,157,255,171,254,212,5,111,6,166,4,38,0,124,253,44,255,139,1,78,3,222,0,64,253,3,253,52,253,44,253,84,248,12,245,106,255,35,1,174,255,209,4,179,5,239,3,116,255,101,255,153,0,183,1,41,1,32,6,7,250,102,254,132,253,0,6,199,1,19,255,208,250,117,255,252,254,19,2,42,2,100,3,13,1,240,4,94,2,23,255,115,3,207,1,230,2,88,2,136,255,183,255,165,1,212,0,73,254,198,255,36,3,250,250,39,251,216,2,38,1,22,254,50,0,177,253,119,252,26,251,42,0,81,253,147,0,231,255,17,1,84,2,201,254,189,4,89,2,14,253,81,3,72,2,173,1,95,2,75,2,166,253,90,255,205,1,228,252,201,252,9,3,100,5,142,3,219,6,119,0,137,5,204,3,37,255,144,252,196,249,231,251,14,252,182,1,55,253,157,250,78,0,0,0,65,254,101,251,144,251,217,250,219,249,200,8,231,6,29,5,178,3,47,6,152,5,126,4,226,1,180,1,43,254,172,251,106,2,65,254,58,252,64,4,28,251,21,250,142,255,176,251,40,248,189,253,210,0,101,2,241,1,73,248,99,250,130,2,11,251,168,252,243,3,146,249,95,251,39,4,237,249,96,253,180,4,100,249,166,251,111,2,45,252,210,250,3,251,27,2,109,255,126,3,182,250,127,252,78,254,120,3,219,1,172,1,153,0,128,254,82,1,44,250,1,254,103,1,50,252,165,251,42,254,105,0,218,253,165,2,87,252,135,251,109,3,124,1,252,254,210,0,149,6,156,3,232,4,239,6,166,4,71,4,139,5,119,2,21,2,115,2,43,1,165,254,101,254,234,253,135,2,118,253,29,0,173,253,134,254,169,250,27,6,122,5,97,4,185,5,65,4,130,5,136,2,208,247,190,251,250,255,55,1,62,255,155,252,129,253,193,252,160,1,118,251,56,251,69,5,33,251,83,252,21,7,111,247,61,248,197,1,149,253,169,250,68,252,186,249,76,248,29,250,105,251,223,251,176,251,135,254,89,2,201,0,84,7,57,3,118,1,82,254,213,250,29,0,139,250,31,251,205,250,17,252,32,250,192,3,135,250,39,248,197,0,157,250,99,248,20,255,203,251,123,0,166,1,103,2,245,4,34,2,206,254,246,5,136,3,170,4,252,6,153,4,142,253,140,252,10,250,199,0,254,2,224,5,215,251,94,3,197,0,246,251,19,249,137,252,224,252,145,0,87,2,146,251,249,253,114,2,75,251,122,248,244,1,114,252,239,251,141,250,60,250,225,249,55,252,245,253,74,3,34,0,2,7,134,2,94,3,73,251,160,248,22,252,178,255,247,255,96,253,20,4,247,2,80,0,168,253,115,4,251,3,57,0,208,7,142,5,191,252,134,5,97,4,78,251,94,6,236,4,51,254,140,5,220,4,1,6,207,3,253,0,229,254,68,1,153,254,87,2,61,255,106,0,76,2,62,0,181,253,11,253,133,2,205,0,51,0,177,4,246,2,71,251,161,2,122,254,144,253,45,6,173,3,105,255,255,3,223,2,4,11,21,5,178,2,210,254,12,2,157,255,124,252,204,249,91,251,60,4,251,0,238,0,222,7,0,7,242,3,221,4,97,6,205,6,53,251,252,249,72,251,147,253,200,1,147,255,40,0,191,255,20,3,219,252,69,253,186,250,185,253,136,3,64,3,223,252,20,2,82,2,180,7,128,5,71,5,103,251,168,248,190,247,251,252,56,2,180,3,9,252,55,4,236,4,169,251,226,1,126,255,242,6,20,4,12,3,45,250,245,0,144,3,196,254,139,251,107,252,232,253,94,250,214,246,239,252,246,249,60,248,45,248,1,1,141,3,199,248,135,253,71,251,254,249,130,248,226,251,70,6,191,8,40,6,201,253,36,250,248,249,1,251,195,0,89,5,207,252,37,1,195,4,243,253,118,2,173,4,94,249,135,246,208,248,209,254,219,2,235,2,111,251,5,255,13,1,74,252,181,255,148,6,98,251,59,254,237,3,193,249,73,2,122,1,229,247,197,253,85,254,239,253,121,251,109,251,229,254,51,255,204,253,228,252,222,4,205,2,229,8,159,3,27,2,58,254,47,2,184,1,51,253,180,5,79,6,250,251,28,4,74,6,111,251,118,255,79,3,226,0,39,0,156,253,29,251,150,255,39,253,117,253,200,3,22,5,54,253,132,253,191,6,97,1,45,4,154,1,226,252,100,255,75,4,194,253,150,3,190,1,226,250,244,3,210,1,128,5,55,6,253,2,149,5,100,5,221,6,157,7,164,7,74,9,42,6,255,7,100,8,148,3,98,0,249,255,101,7,138,5,93,8,92,1,125,5,43,6,152,0,110,4,9,7,245,254,154,0,115,5,114,251,213,1,30,4,138,251,107,254,207,251,195,250,40,247,211,249,148,254,101,3,170,6,118,251,37,2,14,6,55,251,116,248,126,249,51,250,71,248,249,247,65,249,118,252,158,255,151,248,233,0,212,5,124,3,108,0,181,254,64,249,110,251,92,249,220,251,188,7,254,6,210,251,51,249,139,248,245,255,3,6,37,5,192,249,94,0,241,1,165,1,187,1,59,255,214,249,163,254,30,252,169,253,229,253,116,4,59,252,117,250,127,255,195,250,175,0,65,254,137,254,31,5,7,8,141,254,118,253,205,254,207,251,93,2,109,1,247,247,143,255,174,1,140,2,146,3,199,3,12,252,206,249,237,246,225,5,224,4,47,2,6,1,26,254,111,254,65,249,62,5,10,6,50,0,56,0,176,1,182,254,119,0,164,253,19,250,200,251,214,252,178,3,103,4,31,4,136,250,89,249,80,249,10,251,64,253,219,250,39,3,29,7,119,4,200,10,70,6,123,8,96,4,153,1,106,255,109,255,148,1,191,3,135,9,119,7,141,8,118,252,115,255,158,252,120,252,114,255,54,254,211,253,60,253,113,249,194,252,105,250,209,249,206,248,190,250,194,251,188,249,240,254,147,3,84,251,4,3,32,4,130,253,46,251,151,248,12,254,175,255,202,252,247,250,179,249,33,253,139,255,17,3,168,0,190,251,109,4,154,3,184,251,22,253,104,5,31,1,221,253,217,251,160,250,103,247,76,251,128,247,222,249,35,249,25,250,63,247,253,252,55,249,75,4,62,3,204,249,212,2,219,4,250,249,181,2,37,3,102,249,16,255,129,6,92,249,252,255,100,253,101,8,48,3,18,4,206,252,207,248,22,0,4,253,5,254,193,1,129,251,151,253,33,1,181,252,196,249,16,255,242,1,22,255,111,253,16,253,224,1,142,6,193,254,31,254,193,0,213,252,171,0,137,255,176,247,54,255,176,252,181,6,116,4,164,6,67,0,239,255,66,0,244,255,102,249,187,253,152,255,240,254,204,251,94,251,203,248,136,254,140,251,98,252,92,254,198,255,253,254,112,253,146,251,215,253,252,6,203,4,199,1,129,0,206,1,185,1,16,255,240,253,72,3,2,2,130,0,181,255,90,4,111,2,153,0,216,0,44,4,52,2,250,255,236,254,95,4,215,2,190,0,188,255,192,2,50,1,119,0,248,254,73,1,61,0,156,255,156,0,108,1,123,0,183,0,48,255,85,255,133,255,220,0,191,255,206,254,194,255,146,1,17,0,108,253,86,252,246,254,0,0,129,1,235,0,20,1,29,1,64,1,12,1,176,254,56,255,44,253,17,0,172,255,125,1,224,253,173,1,238,1,7,2,139,255,32,1,48,1,73,1,131,2,157,0,189,2,252,1,176,4,113,2,28,3,96,2,230,3,165,1,236,1,120,2,180,4,12,3,190,1,132,0,233,4,76,3,35,2,193,1,61,3,146,2,29,2,214,1,108,4,234,4,150,3,127,2,35,2,51,0,167,1,23,1,9,0,136,1,83,0,94,0,30,2,31,2,229,0,109,255,58,255,129,0,194,0,71,255,161,252,215,250,210,254,30,0,171,253,139,253,237,255,114,0,124,252,199,251,210,1,97,1,53,250,219,249,15,0,113,255,84,249,245,247,17,253,196,0,172,248,237,247,126,253,254,254,225,246,66,250,62,254,204,253,184,253,70,255,152,252,98,254,243,248,36,252,155,251,226,250,42,253,151,251,28,0,169,0,241,251,160,252,50,253,10,255,228,1,36,0,23,255,207,255,9,1,67,0,33,1,211,1,178,0,31,2,42,3,28,2,84,0,26,1,160,2,191,2,49,252,247,252,129,0,31,1,86,252,29,255,187,3,83,2,175,249,223,254,68,3,137,2,201,248,41,255,82,4,206,2,14,248,195,251,138,2,184,1,203,247,239,253,139,3,63,2,37,248,176,254,158,2,204,0,171,246,76,253,104,1,137,0,148,247,100,247,247,255,24,1,246,254,119,0,39,0,193,0,78,0,197,255,136,255,226,0,49,252,166,252,243,252,185,251,149,253,99,254,61,254,182,252,64,251,215,250,211,252,141,252,160,250,177,249,118,254,84,254,31,253,167,251,219,253,234,252,144,252,49,252,57,252,126,253,39,252,138,252,7,251,175,250,39,254,220,252,135,250,129,250,160,0,247,254,105,252,237,254,8,255,6,255,50,253,132,254,97,0,153,255,137,254,27,255,97,254,63,255,121,255,213,253,116,2,105,1,119,0,216,0,67,2,108,1,135,1,209,0,122,2,10,2,102,255,108,255,14,2,133,1,170,0,33,0,105,0,11,1,64,0,124,1,33,250,24,252,226,255,143,254,210,251,58,0,135,2,223,0,16,250,221,254,109,2,51,1,5,250,156,0,250,2,148,1,19,248,141,0,222,2,243,1,199,248,118,253,50,1,0,2,69,255,152,255,197,255,182,1,134,0,26,255,156,0,70,255,195,255,252,254,240,255,10,0,199,253,253,255,91,254,215,254,67,249,247,253,166,254,178,0,174,250,197,255,212,255,157,0,158,247,51,254,42,254,163,254,134,247,255,255,143,254,135,255,213,249,139,254,124,252,9,252,163,251,177,253,155,253,240,252,207,253,122,0,181,255,63,254,252,255,85,255,133,255,140,254,192,0,168,0,180,255,124,255,252,0,149,255,84,1,210,0,136,1,253,1,16,1,181,0,147,255,145,0,218,0,119,0,96,254,249,254,229,1,9,1,75,255,248,255,226,254,226,0,12,255,38,255,69,0,222,254,98,255,191,0,255,255,192,255,176,253,166,255,213,0,160,255,255,0,179,1,178,0,176,255,143,254,238,255,223,255,176,255,214,255,159,1,140,0,34,255,119,4,139,2,137,2,73,1,255,2,44,2,249,0,235,0,180,3,157,1,186,1,23,1,141,0,83,1,100,1,45,2,42,254,86,255,99,0,237,0,199,253,224,252,96,1,53,2,26,1,217,1,214,1,76,1,57,255,78,253,252,250,107,252,63,255,86,254,224,252,158,251,230,255,141,254,22,254,63,255,125,2,83,2,7,2,74,1,152,1,141,255,79,0,12,0,221,1,87,0,153,255,136,254,102,253,165,254,235,254,221,254,2,254,31,254,169,0,41,1,195,252,30,253,51,255,85,255,192,254,228,253,72,1,27,1,165,252,66,252,186,1,254,255,44,2,174,2,130,0,56,0,103,5,244,3,243,2,171,1,100,2,229,2,116,2,41,2,173,254,228,252,134,0,21,1,135,253,195,251,254,255,10,255,144,252,245,251,185,249,216,251,30,252,38,254,142,251,24,254,98,254,229,252,73,0,50,255,248,255,117,255,183,1,204,0,80,255,190,253,23,0,131,0,243,254,11,253,65,255,245,0,147,255,174,254,112,0,60,1,120,0,106,254,138,255,99,2,76,255,70,255,123,253,115,0,83,255,34,0,250,253,23,254,105,255,61,0,185,253,180,252,220,0,118,255,87,253,4,252,135,1,239,255,170,253,191,254,157,0,217,254,129,0,155,0,98,252,149,252,37,252,29,1,241,0,173,255,131,255,131,255,108,2,85,2,176,1,92,0,137,1,78,0,153,1,61,0,119,254,29,253,99,254,20,253,83,0,54,0,105,1,27,0,196,251,130,0,175,254,74,253,227,249,41,1,62,1,237,255,175,248,36,0,51,0,195,254,237,246,10,255,231,0,172,255,254,246,241,252,40,0,77,255,71,247,94,252,38,254,50,254,14,253,170,255,224,254,142,253,149,246,57,254,193,255,171,0,181,251,186,251,230,255,113,255,87,251,57,254,106,254,131,254,163,253,46,255,160,255,205,255,188,253,36,254,236,254,241,255,85,251,134,253,77,251,143,252,134,254,35,255,99,253,72,252,82,2,178,0,109,254,92,253,251,2,71,1,89,2,34,1,172,0,44,1,203,0,157,0,200,255,176,254,100,1,24,0,28,255,216,254,253,254,227,255,70,255,7,1,160,1,14,0,159,254,117,1,244,255,40,255,1,1,96,0,174,0,57,0,10,250,152,253,70,252,13,254,15,254,104,255,179,254,125,0,105,0,200,0,179,0,159,255,181,254,32,255,253,2,185,2,248,2,0,1,45,1,59,0,199,1,171,255,204,0,32,1,254,253,240,0,251,0,147,255,0,1,161,1,222,255,99,254,101,0,174,1,128,1,156,0,225,255,246,255,206,0,170,1,77,2,145,0,143,0,71,0,40,3,138,3,77,1,93,1,218,3,170,3,77,2,75,1,20,5,56,3,187,0,253,1,38,4,141,2,123,1,210,1,182,5,169,3,145,1,18,1,19,3,93,3,9,1,2,0,97,2,41,2,28,0,49,1,158,3,84,1,106,0,130,1,241,0,245,254,109,255,225,0,78,255,234,253,91,1,246,1,125,253,131,254,141,1,30,0,117,253,35,253,77,254,142,1,105,254,42,253,28,254,8,255,235,252,110,252,74,254,36,254,14,254,122,254,75,0,217,254,60,252,178,253,162,253,150,0,135,255,207,255,101,255,178,255,167,3,38,2,133,1,38,0,191,254,127,0,168,1,59,1,227,254,143,255,27,1,3,1,146,2,203,0,66,1,230,1,135,3,249,1,236,2,161,1,99,2,167,1,43,2,0,2,239,0,173,255,190,253,237,255,173,254,37,253,93,1,13,0,90,252,137,250,142,255,152,254,107,0,180,2,182,0,90,0,37,251,254,249,241,249,43,253,200,253,121,252,173,250,243,253,251,253,171,252,163,252,20,252,88,255,78,253,189,252,63,0,119,255,212,253,221,253,144,0,226,254,207,252,229,1,63,1,109,255,104,254,14,2,246,0,165,254,78,254,41,1,228,255,222,254,41,254,170,251,251,250,52,254,153,254,36,252,230,252,67,5,19,5,178,2,11,2,192,4,44,4,70,4,245,2,57,3,116,4,240,2,238,1,228,4,85,5,171,4,130,3,9,2,29,4,20,2,176,1,178,254,40,255,199,254,249,254,96,255,52,0,40,254,101,255,127,0,136,0,132,254,44,0,83,3,154,1,94,255,23,254,123,0,1,255,228,252,101,253,66,4,149,3,21,3,237,1,117,5,173,4,46,2,202,0,205,255,138,255,170,254,67,253,83,0,108,0,214,255,71,254,61,0,95,0,31,1,0,1,229,255,89,0,12,2,19,2,95,1,227,0,80,2,33,2,185,2,155,0,92,255,51,1,126,2,18,1,23,254,206,255,242,2,240,0,90,255,132,255,140,255,189,253,68,251,193,255,190,0,217,254,240,251,240,250,147,0,136,254,79,255,143,255,73,3,217,4,27,4,156,2,2,0,37,1,39,2,48,1,184,251,71,252,8,255,120,1,18,253,59,252,87,0,4,2,237,254,252,253,177,2,135,1,133,254,125,253,108,3,82,2,122,254,11,252,123,253,61,2,149,255,200,253,79,253,198,252,255,251,229,255,184,254,53,255,93,3,237,2,36,2,233,0,132,249,237,251,195,1,108,0,108,253,148,253,174,1,236,0,21,0,116,254,122,251,137,253,92,5,18,5,199,3,65,2,101,4,101,4,77,2,198,1,189,254,159,252,45,254,153,0,44,254,69,253,220,252,3,254,120,254,50,253,52,255,221,255,165,253,187,251,201,253,94,255,7,254,20,252,154,255,94,1,219,0,224,0,167,1,252,0,139,1,79,2,96,2,107,1,22,253,160,255,117,1,172,0,171,0,39,1,202,2,83,1,233,0,77,0,107,0,21,1,157,0,153,0,13,254,156,254,11,6,49,4,64,2,238,1,220,254,173,254,8,254,176,253,121,252,184,255,149,253,31,254,198,249,163,251,201,253,2,255,231,252,5,254,204,253,221,254,20,254,236,253,246,1,48,2,130,254,171,1,88,2,230,0,29,255,221,1,251,0,75,0,29,1,74,3,45,3,220,1,226,250,203,250,186,0,121,1,181,253,107,252,131,2,125,1,94,251,215,253,155,1,82,0,153,251,204,252,82,255,228,253,164,253,119,0,31,2,205,0,132,254,145,2,141,3,55,2,112,0,214,254,138,254,114,0,167,252,5,255,56,0,159,0,145,1,89,1,222,255,116,255,145,255,161,253,41,0,102,2,99,1,142,255,179,255,218,1,66,2,56,0,170,5,156,3,74,4,140,5,229,2,144,1,246,0,22,0,76,2,57,1,135,255,71,1,63,3,216,1,142,251,160,253,88,3,40,2,39,251,208,251,126,2,88,2,154,254,254,0,179,254,209,254,122,253,227,2,102,1,74,0,202,4,135,6,197,4,81,3,193,8,88,6,215,3,124,2,49,7,197,5,237,2,128,1,94,1,7,1,87,0,128,0,146,248,83,252,112,255,192,255,58,249,1,255,32,1,225,255,172,245,42,251,110,1,235,0,149,249,188,251,192,250,208,254,227,253,205,251,164,251,123,0,102,251,4,255,208,252,76,255,8,252,21,2,53,2,233,0,25,254,82,254,68,255,78,1,99,3,212,4,22,2,171,0,202,249,185,249,123,2,118,2,108,247,54,1,156,3,156,1,202,246,184,254,188,3,17,2,177,245,135,254,118,2,22,1,214,245,61,1,31,3,43,1,154,246,133,0,84,1,31,0,148,247,68,250,131,0,125,0,96,251,22,254,117,255,46,0,24,253,191,1,123,3,52,2,67,0,61,254,134,2,92,2,215,253,83,254,148,252,140,1,162,0,190,255,25,5,147,3,223,1,67,2,64,4,26,3,194,1,22,1,54,2,68,1,223,251,102,255,148,0,79,255,15,246,168,0,46,4,80,2,209,246,214,255,51,3,89,1,216,246,61,253,209,2,250,0,129,247,39,250,203,254,122,0,178,255,183,255,120,0,173,0,252,255,6,1,249,254,251,254,81,254,192,255,107,254,36,253,207,245,116,0,173,255,63,255,11,250,80,252,35,254,43,253,4,254,51,1,170,0,172,0,64,3,161,1,64,3,174,2,31,255,177,0,126,3,50,3,30,254,123,254,255,4,15,4,129,254,201,0,162,254,40,0,218,2,123,2,226,0,14,2,247,1,206,1,82,1,142,1,23,2,202,2,40,0,230,254,202,5,191,5,61,4,219,2,25,6,48,4,141,3,181,2,139,5,2,5,121,3,111,3,129,4,216,2,162,4,72,3,30,255,106,4,181,3,177,2,18,254,38,252,236,249,128,255,200,253,47,253,55,253,230,255,61,1,12,2,70,0,135,0,107,254,159,252,26,249,116,253,82,255,223,252,117,3,5,3,103,255,165,255,75,4,239,2,6,254,131,251,85,3,134,2,241,0,14,3,7,2,27,2,61,7,164,6,77,4,172,2,31,251,50,250,48,254,188,0,131,252,127,250,224,250,171,254,121,255,182,1,81,255,18,0,87,4,208,3,63,1,208,0,106,250,24,249,83,0,202,1,238,253,24,252,51,1,129,0,184,252,241,255,227,255,156,254,113,252,100,252,133,251,14,255,137,255,240,253,127,0,123,255,7,253,3,253,190,0,173,255,197,254,127,3,10,2,231,0,34,255,102,0,193,255,84,254,60,1,187,2,123,1,70,0,25,0,204,2,58,1,148,255,251,1,106,3,54,2,238,0,108,0,173,3,7,2,195,0,169,1,196,255,85,254,1,1,139,0,153,255,138,253,190,1,78,1,114,1,156,1,48,0,84,255,78,253,229,254,45,2,187,0,226,254,158,0,227,1,140,0,14,1,168,254,137,253,156,3,67,2,140,255,132,0,142,0,210,1,188,255,192,255,230,0,111,255,210,254,226,253,221,252,112,252,250,3,225,2,251,252,247,3,118,2,41,1,220,245,95,0,189,1,80,1,182,247,235,1,254,1,191,0,27,251,161,0,254,255,188,254,86,250,135,253,56,253,151,255,182,252,2,255,101,254,100,0,128,253,222,254,242,3,251,2,118,253,57,1,145,4,218,2,140,0,249,1,6,4,254,2,4,3,31,1,43,4,55,3,239,1,237,2,49,1,67,1,92,255,206,1,78,0,143,1,170,254,150,252,69,0,85,2,240,255,108,2,109,2,81,1,118,255,68,254,247,254,218,0,84,0,62,254,185,3,154,2,34,255,221,252,29,2,92,2,103,252,160,250,244,0,116,0,183,252,45,253,118,2,76,2,140,0,151,2,38,1,112,1,167,3,22,4,113,3,247,2,210,6,184,5,148,3,116,2,180,1,195,3,25,1,1,0,137,255,74,0,30,2,213,0,1,0,201,253,45,1,241,0,4,1,179,1,222,0,140,1,168,3,189,3,84,4,191,2,254,1,250,1,40,3,222,1,89,2,182,2,192,3,108,2,204,3,229,2,212,3,88,2,66,3,205,2,255,2,172,2,131,2,204,3,167,3,126,2,245,1,149,2,208,2,83,3,151,255,136,253,209,254,139,255,83,254,130,0,21,3,186,1,246,253,68,255,192,2,117,1,9,253,42,0,46,3,11,2,237,253,143,251,117,1,66,2,86,253,77,251,57,254,29,1,117,251,215,249,182,251,44,0,81,0,174,255,200,2,107,1,221,1,246,0,186,3,110,2,68,6,86,6,253,4,123,3,129,5,91,3,156,3,124,3,6,3,17,4,179,3,118,4,40,0,222,253,181,255,32,1,152,253,150,255,71,253,230,255,87,255,96,255,133,252,29,253,233,254,128,254,251,251,162,254,245,6,28,5,22,4,48,3,44,6,253,5,192,5,154,4,225,5,52,4,192,4,131,3,122,3,136,3,52,2,142,2,152,3,180,2,253,3,88,3,19,254,132,0,177,0,249,1,71,0,195,0,228,255,97,0,200,1,95,1,92,255,88,0,183,1,22,1,216,255,94,1,115,5,181,3,234,0,161,255,219,252,40,254,38,0,93,255,111,1,158,255,233,1,11,2,1,4,154,4,188,4,138,3,63,1,34,5,46,3,205,1,133,255,225,253,220,252,191,1,20,253,188,254,127,252,153,251,31,253,11,254,235,252,55,253,203,2,9,3,215,4,154,3,157,7,147,7,88,5,97,3,218,2,112,3,246,2,132,1,153,252,198,1,17,0,5,255,131,254,214,252,209,249,239,0,247,253,58,252,232,252,3,1,134,252,178,250,254,252,183,255,166,0,93,1,44,255,67,1,184,252,211,254,217,1,179,1,89,253,48,254,216,2,95,1,100,255,57,255,155,2,176,1,29,0,4,255,159,1,224,1,37,253,133,254,145,0,47,2,240,253,137,253,122,251,97,255,189,1,17,1,123,0,127,2,117,1,130,255,32,3,56,2,84,0,94,255,208,2,200,2,194,252,232,253,71,255,222,0,152,1,196,1,245,1,3,3,127,252,181,250,189,255,186,1,232,252,130,250,54,2,90,2,167,0,186,254,253,1,74,1,161,255,142,253,38,253,168,254,132,6,193,4,11,3,199,1,36,5,60,3,72,2,207,2,148,1,225,255,245,3,21,3,89,0,107,0,123,3,37,2,103,3,45,6,149,3,159,2,98,3,199,5,9,5,86,3,135,1,44,4,98,4,44,3,78,0,206,253,89,1,51,2,173,1,153,255,161,1,19,3,134,255,75,254,155,1,20,3,111,252,95,254,90,2,242,2,30,255,240,255,151,0,248,2,68,253,118,0,152,255,242,255,152,251,48,0,28,1,137,1,122,254,93,254,129,253,140,255,114,252,50,1,60,1,243,255,183,4,216,3,53,3,157,2,85,251,75,253,140,0,43,255,140,252,96,254,57,255,210,253,152,253,245,0,108,254,104,253,6,1,56,0,151,253,44,253,171,255,21,254,192,254,112,253,198,253,193,252,127,255,240,253,30,250,193,255,145,254,127,254,154,254,191,254,4,0,51,0,146,254,42,255,63,1,255,1,146,0,159,2,239,255,221,254,146,255,208,1,117,255,16,254,54,255,220,0,200,254,137,253,108,253,183,255,113,253,204,252,106,253,115,253,248,250,167,252,82,254,71,252,65,252,248,254,207,255,44,254,184,255,131,254,162,254,205,253,63,255,105,254,55,0,104,254,221,252,11,0,203,254,137,2,188,0,58,255,0,254,205,1,177,255,54,254,218,250,249,254,122,255,245,253,135,249,77,254,17,254,3,253,57,0,165,254,98,254,178,1,139,251,14,255,104,253,167,252,34,0,188,255,61,253,174,254,163,1,163,0,226,255,250,254,57,254,235,252,106,250,47,253,238,3,152,2,13,1,25,0,107,2,4,1,183,0,96,0,56,252,178,250,124,254,135,0,75,253,67,3,200,1,154,0,81,4,191,2,57,2,107,1,89,6,46,5,217,3,236,2,36,255,219,0,76,0,48,255,81,250,130,249,49,0,149,0,60,252,84,255,16,253,176,254,113,2,209,0,6,255,190,255,7,252,186,252,254,255,61,1,136,247,51,250,118,255,123,0,172,248,205,247,247,253,85,0,57,252,146,254,73,253,143,252,103,252,13,252,5,253,75,252,132,255,0,255,160,254,108,253,178,0,207,1,98,1,48,1,48,249,177,253,230,254,79,0,55,247,175,0,99,3,243,1,118,255,76,255,75,255,235,255,13,247,39,251,52,254,248,253,253,252,195,1,246,255,204,254,15,1,191,255,4,0,214,0,233,254,77,254,213,255,164,254,98,253,35,0,191,255,45,255,38,3,23,2,85,0,41,1,57,0,239,0,210,2,237,1,225,0,149,2,72,3,35,2,228,253,136,254,14,0,93,1,213,1,209,2,75,1,162,0,224,253,16,253,194,255,246,255,142,1,168,255,212,2,189,2,237,255,235,253,162,255,89,2,136,0,185,255,87,253,21,253,90,255,168,254,5,1,206,255,161,0,204,255,229,1,81,1,117,249,50,0,190,0,163,255,22,247,25,255,62,255,174,255,161,255,173,253,102,255,128,0,126,3,245,1,76,2,201,1,167,254,206,0,122,0,110,0,137,253,29,255,199,253,3,0,152,1,239,0,141,1,226,0,59,255,254,255,128,0,235,1,1,5,136,3,36,1,215,0,26,2,50,1,3,1,253,1,91,253,233,251,13,0,65,1,89,253,180,253,154,254,44,255,210,253,243,0,134,2,223,1,230,1,86,1,122,2,20,2,107,0,34,3,75,1,136,0,144,255,114,254,249,251,226,254,186,254,63,253,32,1,16,1,19,5,120,4,154,4,92,3,89,254,121,0,127,254,108,255,217,254,210,254,190,252,205,252,16,0,232,255,55,255,36,254,43,2,91,0,11,255,38,1,218,255,133,254,62,252,59,251,89,251,18,250,239,254,117,254,122,254,11,252,123,253,61,2,205,248,250,251,249,1,212,1,232,2,179,3,97,2,237,1,79,253,108,251,140,253,121,255,254,251,195,0,155,1,196,0,46,6,123,4,63,2,81,1,41,251,247,252,120,253,114,255,83,2,57,3,199,3,223,2,74,251,54,252,175,255,170,254,23,253,13,0,184,255,119,1,198,1,19,0,127,5,153,3,145,249,84,255,93,3,50,2,160,3,1,6,39,4,228,2,88,246,72,252,8,1,82,0,10,254,59,252,202,250,123,0,99,3,212,4,22,2,171,0,240,246,52,254,12,3,107,1,90,251,151,253,252,0,195,255,82,255,34,0,243,3,20,3,227,246,247,0,167,1,153,0,240,255,157,254,6,1,193,1,216,249,207,251,224,253,141,254,153,253,207,254,27,4,37,3,175,2,16,2,6,0,74,255,167,3,107,3,234,3,41,3,199,0,1,1,126,0,76,0,184,253,142,251,87,2,44,2,175,251,145,250,201,249,249,253,47,252,211,250,108,0,91,1,46,253,49,252,109,1,101,0,111,255,169,2,249,0,103,255,0,0,178,254,198,253,159,0,156,1,29,1,176,254,151,253,71,252,58,252,119,3,177,2,29,251,84,0,71,255,114,254,176,253,177,1,20,4,141,2,85,0,73,1,216,255,105,1,79,254,63,253,210,1,62,2,102,255,142,2,80,2,34,1,89,255,72,0,93,1,175,0,162,2,41,1,209,3,208,2,211,4,180,4,245,2,232,1,112,254,243,254,26,2,116,1,186,250,149,250,86,251,165,255,238,4,108,3,7,3,188,2,169,253,218,255,82,254,46,253,184,7,94,6,223,3,96,2,111,0,20,1,30,255,160,255,77,252,124,254,245,255,249,255,209,254,237,253,185,252,82,1,198,6,174,6,125,5,245,3,252,253,169,252,123,253,210,0,80,253,96,254,1,2,230,0,202,252,131,253,134,251,192,254,72,252,110,253,74,253,183,0,142,255,145,253,50,3,162,2,65,255,52,255,219,2,123,2,51,0,197,4,115,3,64,2,70,252,81,254,58,3,86,2,170,254,13,253,124,252,105,254,154,251,158,254,50,255,0,254,221,253,214,252,155,254,148,253,66,0,3,2,183,255,102,254,152,252,79,252,92,250,53,251,191,0,239,255,224,253,25,255,252,249,224,253,123,252,138,252,134,252,242,249,19,246,205,252,54,252,175,0,198,252,46,251,6,253,169,253,234,255,122,2,213,252,37,252,122,252,189,254,203,0,26,0,129,254,21,255,243,252,113,254,238,4,138,3,92,252,137,250,156,250,144,253,93,0,87,0,98,254,229,253,77,253,37,0,121,2,254,1,125,254,36,254,206,250,143,1,66,0,7,1,105,254,207,255,177,254,95,254,17,4,73,7,245,252,191,251,96,250,22,253,166,252,64,3,187,253,9,253,141,254,95,253,6,254,40,8,208,253,134,253,101,251,15,1,241,0,14,0,74,254,12,255,115,254,207,1,178,4,23,4,162,253,227,252,98,250,205,255,189,254,225,1,32,255,184,253,241,253,238,1,113,3,170,2,79,254,206,254,22,252,42,2,147,2,222,0,171,0,96,255,159,254,169,2,6,7,29,6,172,252,99,251,97,249,176,254,102,253,114,0,187,253,12,253,24,253,61,255,119,1,241,1,47,254,220,252,182,251,154,0,26,1,125,255,206,255,65,255,49,253,67,1,220,2,6,6,46,253,205,252,132,250,105,0,6,255,185,0,78,255,10,254,26,253,65,1,254,1,87,4,189,254,201,253,58,252,127,0,228,1,82,1,96,255,52,0,174,254,220,2,87,5,18,6,142,253,222,252,96,249,226,254,182,253,164,2,73,253,169,254,142,254,22,254,39,1,101,7,138,253,194,253,10,252,176,255,133,2,187,255,250,255,194,254,148,254,14,3,170,5,14,4,199,254,35,253,141,250,120,0,60,0,221,1,248,254,183,253,133,255,199,2,221,4,121,2,165,255,157,254,8,252,3,3,246,2,5,1,253,0,81,0,38,254,162,3,167,8,184,6,216,252,181,251,123,248,208,253,242,252,169,0,220,252,206,251,68,255,142,253,201,255,125,5,74,253,52,253,86,251,108,253,98,1,73,1,254,253,201,255,225,253,110,1,9,4,158,4,110,253,65,252,179,250,201,255,72,255,93,0,163,253,226,254,106,253,148,1,193,1,59,3,226,254,162,254,17,251,116,2,50,1,227,0,240,255,147,0,145,253,186,0,155,3,98,8,94,253,134,252,186,249,69,254,28,255,83,1,143,254,234,252,103,254,231,0,86,0,189,5,64,254,187,253,219,251,82,2,194,1,79,255,132,255,86,255,65,254,159,2,135,4,124,5,36,254,101,253,25,250,179,255,118,255,204,2,79,255,140,254,131,254,195,1,166,3,147,3,6,255,80,254,202,252,16,1,60,3,190,1,26,0,19,0,225,255,186,2,156,6,120,8,122,253,47,252,124,248,77,255,39,254,12,1,133,254,23,253,77,253,11,0,127,0,9,4,24,254,107,252,199,252,61,0,67,1,135,0,147,0,111,255,82,253,173,2,18,3,146,6,6,254,176,252,239,250,35,0,90,0,222,0,233,255,166,254,98,253,199,1,79,2,7,5,53,255,175,253,194,251,140,2,96,1,181,1,39,0,63,0,55,254,73,3,241,4,57,8,248,253,142,252,208,249,184,254,57,253,141,5,172,253,170,254,186,255,209,0,173,0,136,7,89,254,170,253,103,252,165,1,93,2,218,255,254,255,11,255,129,255,128,3,177,7,111,4,133,254,250,253,213,249,173,0,118,0,241,2,201,255,131,254,204,255,217,3,253,3,241,2,254,255,221,254,133,252,241,2,224,3,167,1,8,1,131,0,60,255,127,3,226,8,239,9,133,253,192,251,61,246,239,253,42,252,14,2,4,253,194,252,220,253,76,254,60,1,87,2,93,253,84,252,22,253,199,255,236,0,245,255,55,255,175,255,226,252,16,0,77,3,22,6,31,253,39,252,68,251,44,254,17,0,34,1,233,254,184,253,68,253,183,0,54,3,193,2,247,254,20,254,93,251,165,1,152,0,212,1,122,254,166,0,244,254,39,0,14,6,76,7,133,253,58,252,221,249,59,254,20,254,142,3,228,254,253,251,181,255,75,255,123,255,60,7,67,254,144,253,106,251,164,1,111,1,207,255,123,254,44,255,87,255,195,2,49,4,184,4,229,253,58,253,87,250,83,0,93,255,228,1,20,255,225,253,157,254,82,1,151,4,46,3,10,255,203,254,66,252,94,2,248,2,60,0,166,0,248,255,93,255,206,254,57,7,3,10,21,253,255,251,9,249,93,254,66,254,209,0,50,253,202,253,234,253,6,254,181,2,89,3,49,254,71,253,198,251,69,1,175,1,50,255,241,255,248,255,5,253,33,2,151,3,238,5,157,253,241,252,223,250,0,1,201,255,208,0,91,255,164,254,106,253,65,1,168,2,162,3,186,254,83,254,73,252,228,1,190,1,58,2,59,255,72,0,183,255,141,3,175,5,205,6,205,253,31,253,74,248,132,255,96,254,206,2,34,254,108,254,198,254,240,255,190,1,100,6,217,253,231,253,18,253,198,255,126,2,214,0,55,0,71,255,241,254,124,4,21,5,188,4,29,254,97,253,16,251,117,0,29,1,31,2,52,255,121,254,145,255,1,2,2,6,86,3,142,255,66,255,46,252,109,3,83,2,208,1,4,1,4,1,201,254,236,2,235,8,168,8,251,253,79,252,133,247,186,254,60,253,122,1,212,252,77,253,24,255,208,253,175,2,129,5,36,253,78,253,188,252,153,254,133,2,130,1,247,254,62,0,90,253,145,0,108,6,184,4,213,253,36,252,47,251,178,255,14,0,114,0,185,254,154,254,23,254,136,1,165,2,185,2,55,255,20,255,140,251,181,2,193,1,178,0,13,255,0,1,79,254,99,2,105,5,152,9,156,253,123,252,72,250,205,254,239,255,243,1,197,254,101,253,2,255,0,1,172,1,183,5,26,254,90,254,224,251,143,2,114,1,18,0,154,255,71,255,236,254,243,2,42,6,55,5,24,254,165,253,118,250,182,0,163,255,102,3,183,255,54,254,164,254,67,3,94,3,189,3,230,254,179,254,22,253,35,2,71,3,172,1,17,1,167,255,13,0,172,3,172,6,16,10,94,254,196,251,34,249,212,255,154,254,3,1,15,254,125,253,208,253,99,0,45,2,193,3,91,254,2,253,107,252,39,1,70,1,184,0,175,0,15,0,142,253,20,2,110,3,189,7,69,254,0,253,5,251,221,0,156,0,12,1,39,0,149,254,7,254,183,2,4,3,116,4,94,255,53,254,112,252,197,2,188,1,146,2,25,0,47,1,200,254,244,4,130,5,179,6,215,254,2,253,212,248,249,254,148,255,46,4,106,254,243,255,127,255,57,0,182,1,174,10,138,254,25,254,189,252,48,1,184,2,164,0,104,0,21,255,5,0,75,6,108,7,119,5,27,255,186,253,211,250,149,1,192,0,49,3,169,255,74,254,111,0,4,4,175,4,225,3,68,0,81,255,90,252,9,4,93,4,195,1,222,1,200,0,8,255,79,8,136,10,250,7,189,252,213,250,173,247,225,252,76,253,210,1,212,252,248,251,43,254,146,253,32,1,152,3,67,253,183,252,210,251,101,254,0,2,8,0,122,254,165,255,24,253,226,255,19,4,137,4,202,252,132,251,124,251,218,254,210,255,110,0,101,254,138,254,90,253,214,0,19,2,156,2,106,254,92,254,86,251,231,1,232,0,47,1,194,254,91,0,40,254,123,0,208,4,141,9,46,253,72,252,41,250,30,253,93,253,52,5,225,253,162,253,45,255,161,255,158,255,228,5,219,253,254,253,87,251,217,1,211,0,73,0,224,254,144,255,123,254,25,2,52,5,234,4,201,253,13,253,247,249,71,0,229,254,120,2,86,255,31,254,19,254,169,2,234,3,49,3,156,254,181,254,147,252,163,1,194,2,90,1,241,0,222,255,186,254,121,1,158,7,91,7,41,253,205,251,167,249,23,255,225,253,116,0,244,253,218,252,183,253,183,255,222,1,217,2,224,254,99,252,137,251,173,0,191,1,204,255,68,0,27,255,162,253,193,1,17,2,5,7,177,253,149,252,173,250,183,0,112,255,68,1,153,255,60,254,102,253,111,2,232,1,152,4,18,255,1,254,20,252,70,1,40,2,202,1,136,0,108,0,193,254,114,2,63,5,91,7,22,254,122,253,62,249,70,255,63,254,216,3,30,253,180,255,86,255,218,253,243,2,0,10,16,254,2,254,77,252,210,0,182,2,204,255,84,0,190,254,57,255,66,4,89,6,200,4,136,254,165,253,140,250,87,1,74,0,120,2,81,255,10,254,224,255,204,3,52,5,222,2,52,0,217,254,167,251,41,4,150,3,160,0,137,1,107,0,115,254,190,4,89,10,205,6,136,253,79,251,157,248,49,253,235,254,97,1,117,253,144,252,134,255,45,255,209,0,58,5,206,253,54,253,221,251,48,255,132,1,159,0,192,254,195,255,217,253,37,1,68,4,163,5,120,253,159,252,27,251,207,255,113,255,49,1,111,254,29,255,183,253,49,2,20,2,159,3,139,255,69,254,92,251,251,1,180,1,36,1,177,255,233,0,54,254,159,2,1,4,92,9,135,253,182,252,11,250,204,254,226,254,128,2,139,254,147,253,105,254,162,1,253,0,25,5,197,254,187,253,143,251,60,2,173,2,231,254,61,0,188,255,141,254,223,3,77,4,218,5,19,254,85,253,174,250,209,255,164,0,192,2,0,255,198,254,244,254,119,2,181,3,28,4,138,255,164,254,191,252,68,0,156,4,56,2,152,0,117,0,34,0,89,4,110,7,191,8,167,253,65,252,86,249,113,255,23,254,224,1,180,254,113,253,194,253,54,0,97,1,168,4,50,254,116,253,228,252,150,0,37,2,112,0,195,0,145,255,253,253,167,2,84,4,111,6,210,253,19,253,63,251,247,255,16,1,85,1,203,255,247,254,233,253,233,1,75,3,18,5,136,255,30,254,248,251,120,2,31,2,152,1,179,0,50,1,242,253,100,4,184,5,196,8,95,254,238,252,230,249,32,255,128,254,84,5,135,254,53,254,231,255,129,1,233,1,126,8,180,254,117,253,195,252,32,2,41,2,61,0,22,0,143,255,167,255,104,4,189,6,244,5,40,255,139,254,139,249,161,0,60,1,140,3,91,255,34,255,189,255,82,5,151,4,21,3,73,0,4,255,1,253,226,2,164,3,104,2,106,1,246,0,130,255,19,3,94,10,211,11,77,253,174,251,114,247,203,253,180,253,12,2,178,253,45,252,22,254,249,254,141,1,214,3,191,253,187,252,79,252,234,255,179,1,207,255,66,255,138,255,139,253,168,255,216,4,233,5,132,253,229,251,5,252,221,254,189,0,3,1,255,254,42,254,139,253,145,0,177,3,126,3,186,254,148,254,186,251,31,2,4,1,118,2,54,255,189,0,47,255,101,1,99,5,43,8,199,253,205,251,87,250,54,253,17,255,151,3,92,254,63,253,172,255,147,255,142,255,103,9,99,254,239,253,103,251,226,1,112,1,131,0,70,255,184,255,125,255,93,3,231,4,196,4,157,253,110,253,195,250,227,0,135,255,119,2,80,255,23,254,38,255,233,2,151,4,189,3,191,254,108,255,88,252,159,2,198,3,216,0,84,1,253,255,113,255,213,1,56,7,133,9,39,253,63,252,109,249,43,255,2,255,65,1,1,254,74,254,247,253,130,255,213,2,135,3,172,254,83,253,248,251,60,1,224,1,20,0,23,0,167,255,217,253,97,1,27,4,253,6,224,253,11,253,172,250,42,1,231,255,180,1,156,255,120,254,249,253,211,1,242,2,54,4,46,255,114,254,202,251,108,2,146,2,118,2],"i8",k,v.GLOBAL_BASE+10240),c([33,0,147,0,78,255,153,3,151,6,129,7,187,254,240,253,70,248,2,0,227,254,142,3,141,254,22,254,26,255,0,0,85,2,218,7,16,254,117,254,190,252,37,0,177,3,245,0,181,0,96,255,112,255,201,5,93,5,77,5,157,254,167,253,10,251,42,1,66,1,160,2,63,255,176,254,77,0,65,4,253,5,154,3,177,0,217,255,155,251,228,3,13,3,24,2,200,1,110,1,80,254,135,5,136,9,231,8,46,254,10,253,235,246,209,254,3,254,131,1,41,253,211,253,66,0,111,255,131,2,224,4,224,253,92,253,108,252,31,255,94,3,76,2,104,255,40,0,235,253,167,1,143,5,22,6,196,253,181,252,135,251,128,255,85,0,205,1,18,255,255,254,184,253,93,2,236,2,93,3,24,0,54,255,127,250,29,3,231,1,47,1,75,255,108,1,74,255,104,2,98,5,126,11,18,254,172,252,95,250,220,254,61,0,44,3,172,255,45,253,74,255,43,2,20,2,226,5,147,254,19,254,223,251,54,3,76,2,11,0,242,255,238,255,26,255,233,3,121,5,171,5,38,254,199,253,244,250,46,1,62,0,38,4,186,255,136,254,34,255,214,3,206,3,125,4,60,255,22,255,229,252,223,1,74,4,243,1,106,1,58,0,70,0,123,4,21,8,41,11,25,254,146,252,224,248,73,0,224,254,92,1,154,254,12,254,4,254,199,0,209,2,218,4,178,255,71,253,229,252,105,1,24,2,196,0,118,1,110,0,33,253,79,3,27,4,104,7,146,254,55,253,98,251,59,1,64,1,173,1,72,0,41,255,62,254,247,2,118,3,83,5,226,255,84,254,190,252,93,3,115,2,28,3,118,0,212,1,233,254,75,5,91,7,101,7,68,255,126,253,180,249,63,0,81,255,174,4,94,254,45,255,51,0,158,1,75,2,41,10,22,255,211,253,166,252,168,1,121,3,222,0,136,0,155,255,83,0,133,5,230,8,103,5,172,255,67,254,147,250,158,1,57,1,21,4,29,0,169,254,65,0,16,6,111,6,212,3,183,0,165,255,195,252,249,4,133,5,104,1,41,2,16,1,149,255,51,6,77,12,43,10,104,5,29,8,92,13,244,19,86,26,186,31,135,38,84,43,170,49,133,53,61,254,215,251,239,253,231,250,62,254,12,253,15,254,161,252,128,254,149,253,99,254,99,253,195,254,230,253,181,254,212,253,98,254,4,254,88,254,134,254,238,254,188,254,78,254,154,253,30,255,12,254,24,255,254,253,249,254,135,254,214,254,102,254,105,255,58,253,82,255,206,252,107,255,100,254,100,255,83,254,224,254,50,254,70,255,53,255,86,255,210,254,65,255,191,254,125,255,109,255,215,254,117,254,28,255,42,255,11,255,64,255,189,255,196,254,185,255,185,254,152,255,51,255,162,255,73,255,113,255,218,255,63,255,161,255,16,0,180,255,132,255,8,255,23,0,19,255,24,0,12,255,18,0,120,255,44,0,145,255,223,255,232,255,231,255,0,0,149,0,19,0,23,0,113,255,158,0,87,255,174,0,75,255,133,0,201,255,165,0,230,255,111,0,84,0,98,0,75,0,87,0,183,0,141,255,245,255,248,255,130,0,11,0,170,0,254,0,77,0,205,0,17,0,183,0,112,0,6,1,194,0,202,0,31,1,95,0,189,0,214,255,151,255,234,0,179,0,39,0,186,0,163,0,89,1,76,1,199,0,43,1,161,0,202,255,29,1,178,255,25,1,123,255,141,0,74,255,111,0,249,0,85,1,15,1,108,1,93,0,147,1,75,0,135,1,92,0,254,1,118,255,220,0,71,255,227,255,222,255,105,1,141,255,64,1,3,0,42,2,99,0,30,1,218,0,79,2,11,255,150,1,244,254,197,1,0,0,68,2,25,0,94,2,19,1,20,2,148,0,194,1,183,255,227,2,227,254,6,2,224,254,94,0,53,255,162,2,116,255,182,255,205,0,202,2,142,255,43,1,176,0,155,3,182,0,45,2,240,0,193,2,240,255,1,2,229,1,81,2,37,1,128,1,195,1,105,2,218,255,50,0,51,2,17,2,47,1,209,0,203,1,107,1,177,1,196,1,194,1,198,1,111,1,94,2,221,1,229,2,176,1,97,1,112,1,11,1,105,1,204,2,17,1,71,2,197,1,166,0,254,1,172,0,201,0,117,2,18,1,191,0,56,2,127,2,46,1,42,1,122,2,131,1,131,2,94,1,75,2,48,2,100,2,53,2,88,2,20,3,231,1,160,2,0,2,247,3,65,1,77,1,101,1,86,3,131,255,157,1,218,1,200,2,17,0,105,255,52,2,29,1,14,1,15,255,203,3,121,3,233,1,220,0,254,1,128,3,37,2,156,3,71,1,57,3,34,1,143,3,28,2,84,4,158,0,37,3,199,0,189,3,255,1,218,2,100,0,106,3,13,0,23,3,179,1,120,2,164,2,204,3,249,0,132,3,211,1,194,4,13,3,50,4,73,2,17,3,233,255,157,2,11,1,19,4,107,2,60,4,103,2,121,4,110,2,137,3,148,3,25,4,80,0,75,1,72,2,51,4,89,0,127,2,220,3,193,3,2,3,208,2,30,3,187,2,236,1,191,1,131,3,115,2,15,1,164,4,213,2,53,5,87,0,91,2,64,3,67,6,104,2,103,4,122,3,225,5,232,3,132,4,98,3,241,3,227,3,59,3,125,4,90,3,49,3,170,5,5,3,40,5,244,1,109,5,56,1,129,4,236,255,60,4,64,0,3,5,2,0,148,4,143,1,77,7,2,2,170,6,246,1,100,6,118,3,242,5,160,1,88,2,107,4,70,5,251,4,110,5,121,3,3,7,146,3,230,6,227,0,159,4,226,4,34,7,249,1,62,7,151,3,49,9,57,255,175,1,152,0,199,6,43,255,228,255,136,1,54,5,103,255,204,255,210,3,127,4,189,254,112,254,45,3,167,6,120,255,84,0,169,5,223,7,181,254,113,255,119,255,168,4,0,255,22,2,99,255,7,4,205,254,73,254,30,2,219,2,183,254,92,254,159,255,104,2,150,254,88,255,190,254,110,1,9,255,146,255,45,255,89,0,60,255,203,254,20,0,59,0,148,254,49,254,226,254,89,0,176,254,175,0,80,254,141,0,133,254,66,255,78,254,60,255,177,255,150,0,234,254,29,255,232,254,166,0,213,253,90,254,101,255,29,2,146,254,54,0,227,255,173,255,211,254,250,252,186,0,116,2,115,254,248,254,242,0,37,1,59,255,183,253,124,0,154,1,53,0,123,255,10,0,84,1,198,253,215,251,65,0,66,254,68,0,19,254,127,1,169,3,155,254,57,253,153,254,6,255,91,253,212,251,36,1,230,255,107,1,6,0,95,2,33,5,129,255,246,255,233,5,94,7,201,2,204,3,189,5,133,8,163,5,224,7,161,249,192,249,252,248,14,247,253,251,22,249,180,251,23,248,3,251,148,250,169,250,2,250,77,252,75,250,52,252,12,250,25,252,58,251,4,252,108,251,209,252,37,252,32,252,165,250,64,251,18,252,247,250,186,251,24,253,12,251,13,253,243,250,162,252,101,252,119,252,40,252,90,253,229,251,83,253,230,251,193,251,39,252,218,251,89,253,35,252,127,253,153,251,48,252,6,253,114,253,134,252,218,252,191,252,189,251,62,253,139,253,147,253,218,252,128,253,212,252,249,252,134,253,245,252,225,253,28,252,203,253,205,251,188,253,222,253,157,253,196,253,149,253,8,253,222,254,145,252,242,253,201,252,50,254,229,252,3,255,215,253,97,254,179,253,73,254,235,253,172,254,76,253,89,252,7,254,252,252,66,253,149,251,249,254,206,254,53,252,29,254,67,254,182,255,213,253,220,253,154,253,127,255,75,253,22,255,116,254,10,255,37,254,6,255,247,254,108,254,136,254,254,253,95,254,2,254,212,254,199,254,178,254,104,253,49,254,210,252,126,254,64,253,175,254,153,253,22,255,55,255,23,255,17,255,89,255,201,253,53,255,149,253,109,255,97,254,141,255,160,254,90,255,18,253,85,255,7,253,242,254,145,252,248,254,121,252,145,254,24,253,43,0,37,254,14,0,115,253,43,0,98,253,11,0,64,254,197,255,247,253,130,255,137,255,101,255,155,253,214,255,161,252,229,255,93,252,136,0,29,254,183,0,44,254,55,0,214,254,55,0,208,254,57,1,159,253,57,1,48,253,66,1,89,255,100,0,227,253,253,255,137,255,145,255,69,255,233,0,20,255,4,1,22,255,26,0,91,255,134,0,211,255,216,255,219,253,104,1,53,255,122,1,124,254,194,1,129,254,19,1,20,0,182,0,153,255,246,0,145,255,175,1,37,0,206,1,110,255,231,1,99,255,228,254,197,255,247,1,72,255,24,0,53,0,253,255,54,0,122,0,3,1,77,1,66,0,228,1,104,0,180,1,68,0,195,0,116,0,190,0,206,0,13,1,247,255,226,1,96,1,126,1,29,1,143,1,21,1,196,1,0,1,69,0,186,0,13,0,41,1,243,255,3,1,161,255,30,0,56,0,138,1,196,0,169,1,205,0,200,1,25,1,65,2,15,0,191,0,119,1,34,1,151,1,64,2,200,255,227,0,32,2,149,1,0,0,37,2,164,255,16,2,27,255,95,1,11,255,82,1,150,254,179,1,167,0,15,2,181,255,46,1,91,0,56,3,129,0,87,2,240,1,167,2,186,0,237,2,153,0,225,2,231,254,88,2,164,254,103,2,20,255,1,3,41,0,113,3,38,0,122,3,36,255,73,3,155,254,115,3,119,254,135,3,134,253,218,1,68,254,82,3,81,255,166,2,19,254,242,0,249,253,17,3,54,253,70,2,227,253,110,1,225,253,178,1,171,253,244,1,3,253,222,0,66,253,149,3,25,253,194,3,155,252,245,1,125,252,36,2,133,254,200,0,77,254,157,0,205,252,214,0,163,252,157,0,154,253,40,0,136,253,94,0,141,252,202,255,27,253,4,2,11,254,42,1,154,253,85,255,154,252,95,255,159,252,233,255,206,252,93,0,9,252,245,254,106,253,153,254,219,253,2,0,70,254,135,255,135,254,0,0,29,255,33,0,98,254,130,255,127,255,212,0,90,252,34,0,198,251,230,254,161,251,244,254,58,253,199,252,92,254,65,255,204,251,96,252,107,252,163,255,140,253,154,254,97,0,7,0,50,255,119,254,155,255,24,0,53,255,38,0,88,255,83,0,169,253,89,254,233,254,170,1,68,253,118,0,181,255,206,0,43,252,95,253,88,253,161,1,145,254,37,0,233,254,218,1,127,255,194,254,63,1,40,1,142,253,217,255,87,1,90,2,72,253,217,255,209,254,172,3,104,0,233,0,132,254,137,0,220,255,13,1,181,255,42,255,120,0,43,0,239,253,35,254,203,1,164,0,54,255,27,255,207,255,89,255,97,2,24,3,98,0,36,255,147,3,148,0,37,1,27,1,101,3,91,0,63,2,138,1,70,1,178,255,205,2,67,0,109,1,189,254,104,2,220,255,219,2,27,0,107,2,238,0,120,2,17,1,192,1,99,0,33,3,220,1,101,3,17,1,173,2,64,0,21,3,72,0,253,3,217,0,25,3,203,1,222,2,104,1,134,2,224,1,104,1,66,1,173,1,208,1,126,2,174,1,244,2,107,1,232,3,148,1,171,2,16,2,90,2,103,2,143,2,157,1,178,3,175,2,169,3,90,2,136,3,92,2,43,2,225,2,18,3,150,2,211,1,142,2,106,1,77,2,161,3,198,2,242,1,222,1,159,1,164,1,181,2,115,3,45,3,171,2,13,3,157,3,145,3,171,3,214,2,220,2,235,1,85,3,19,2,180,3,222,2,195,3,59,1,40,3,249,2,243,2,120,4,248,2,143,2,52,4,58,3,33,4,67,4,70,3,235,3,40,3,23,4,109,4,147,2,77,4,224,3,26,4,50,4,51,4,203,3,182,2,202,4,30,4,59,2,73,3,116,3,124,5,99,5,72,4,56,4,93,3,207,4,223,2,4,5,248,2,248,4,223,3,87,5,29,4,233,4,188,2,26,4,22,2,220,3,197,1,240,4,87,2,116,4,167,2,85,6,47,3,104,5,9,2,37,5,137,1,28,6,37,3,168,5,174,2,44,4,136,2,107,3,51,1,59,4,105,1,23,4,61,1,137,5,196,3,163,2,59,2,128,4,79,0,90,4,209,255,250,5,55,1,185,6,58,1,142,4,177,2,2,2,162,255,93,1,26,1,132,5,72,1,1,4,231,1,191,255,57,0,37,3,202,3,36,0,62,0,1,3,249,254,23,3,166,254,125,2,187,2,119,255,108,2,22,2,29,2,33,253,194,0,199,2,44,1,244,254,161,252,158,3,1,3,60,253,84,254,250,1,174,0,132,252,138,253,179,1,35,2,101,250,254,254,109,2,215,1,6,252,168,250,119,254,9,2,104,252,82,253,231,255,20,0,42,252,124,251,84,1,9,0,234,249,145,251,160,254,48,0,213,249,110,254,137,252,6,0,124,251,136,252,220,253,160,254,149,249,112,251,97,255,98,2,24,248,61,252,31,255,193,0,136,249,88,248,11,255,19,254,60,252,112,249,88,252,133,253,237,250,48,249,148,250,164,253,252,249,189,252,139,250,121,255,204,249,222,254,122,249,56,253,37,248,160,249,129,249,229,255,46,247,213,252,123,251,184,0,15,251,189,0,169,250,74,2,37,248,201,0,234,252,200,2,70,251,3,0,247,251,40,3,29,251,62,3,145,255,123,2,156,249,191,1,49,254,75,252,67,254,96,252,8,254,118,251,11,254,69,251,144,0,161,254,140,254,228,251,229,254,221,251,233,254,157,251,193,253,98,250,181,253,178,249,89,252,40,252,229,0,178,2,103,252,49,253,109,254,82,5,83,253,47,254,106,3,141,1,3,254,210,255,61,1,54,5,27,254,200,1,45,3,183,1,101,254,83,1,130,3,43,4,87,254,46,0,161,5,241,1,115,252,224,252,185,5,22,4,2,255,191,254,150,5,141,4,68,0,94,1,10,4,154,2,114,1,11,0,31,5,22,3,143,0,232,0,17,4,26,6,142,255,151,2,80,6,54,4,198,1,67,2,251,4,16,4,180,255,141,3,240,2,43,4,153,0,0,2,92,1,190,4,102,2,129,1,51,7,40,3,13,1,10,4,203,0,62,4,140,2,249,3,247,6,106,4,173,1,47,5,131,1,104,5,207,255,159,4,184,255,191,4,96,254,233,3,32,2,213,6,160,254,199,4,10,254,175,4,179,253,57,2,29,255,94,6,114,255,42,6,26,255,179,6,54,253,8,5,186,252,118,5,107,4,77,5,48,255,208,4,181,1,197,3,95,252,50,3,43,3,130,5,91,3,227,5,164,0,188,4,107,5,1,7,228,1,82,7,200,1,15,8,228,3,146,4,46,5,122,5,36,5,80,5,111,4,238,4,210,4,82,6,81,5,232,6,141,5,203,4,48,6,67,5,86,3,160,2,149,6,30,6,115,4,246,4,224,7,33,7,237,6,45,6,252,5,180,5,207,5,178,3,123,6,253,3,208,6,188,4,112,5,209,3,236,6,137,4,34,7,140,4,182,6,149,5,181,7,55,6,161,4,96,3,84,8,37,4,7,7,46,3,46,7,245,2,56,8,35,5,6,8,234,4,65,8,147,3,27,9,162,3,187,5,123,4,30,10,159,5,197,8,208,6,42,8,84,6,54,9,174,5,106,10,226,5,84,7,45,7,22,8,183,7,203,6,41,6,170,2,9,5,48,6,253,7,174,5,50,8,194,9,212,7,151,10,18,8,214,2,52,6,196,10,32,9,228,0,79,3,152,9,123,6,36,0,45,1,150,7,165,7,66,254,160,255,106,8,116,5,253,5,77,4,14,0,96,2,101,252,36,253,103,5,190,7,65,5,184,3,88,253,65,1,1,5,244,4,198,249,109,1,173,3,178,3,55,249,202,252,70,9,227,10,29,7,228,10,236,248,29,247,169,248,23,246,152,249,200,248,97,249,44,248,60,251,136,248,59,251,198,247,233,249,204,249,219,249,236,249,85,251,177,249,56,251,65,249,177,250,129,251,176,249,100,248,6,251,145,250,231,250,133,250,185,249,101,251,116,249,225,250,93,250,58,250,169,250,126,252,24,251,221,251,205,250,146,251,42,252,147,251,131,251,32,250,200,251,228,250,4,252,97,251,44,252,50,250,57,252,41,250,36,252,102,252,233,251,203,251,186,252,101,251,166,252,58,251,149,251,239,251,216,251,1,253,152,252,123,251,67,253,144,252,62,253,118,252,250,252,8,252,190,253,200,251,223,252,58,250,177,253,169,251,176,253,134,251,55,253,148,250,128,253,160,250,171,253,221,251,96,254,121,252,82,253,192,252,107,253,60,253,68,254,156,252,22,254,103,252,138,254,248,252,149,253,110,251,183,253,219,253,255,252,229,252,77,254,109,253,238,253,27,253,14,254,187,252,155,254,171,253,233,254,153,252,13,255,137,252,230,254,103,253,232,254,101,253,91,255,208,253,118,254,121,252,150,254,102,254,64,254,185,253,103,254,194,253,199,254,155,254,131,253,220,253,198,253,76,254,128,252,8,254,130,254,11,253,198,255,31,254,91,255,150,253,65,255,138,254,22,255,130,254,34,255,85,253,231,255,32,254,94,254,153,254,38,253,159,254,188,254,99,255,80,254,190,254,118,254,209,254,228,254,152,255,167,253,223,254,212,253,60,255,180,253,106,255,109,253,160,253,39,254,232,255,188,255,64,254,38,254,248,255,6,254,211,255,20,253,72,255,180,252,4,255,123,252,165,255,184,253,159,255,116,253,138,0,4,253,125,255,90,253,244,255,98,253,165,0,253,254,253,255,184,252,149,255,115,252,37,0,32,252,44,0,170,252,97,254,185,252,13,0,23,252,241,254,254,251,203,254,226,252,34,254,192,252,24,254,81,252,168,0,168,251,125,254,95,251,155,255,97,251,216,255,83,252,196,254,250,251,254,252,236,251,143,253,199,251,230,253,56,251,213,254,224,250,76,254,83,251,105,253,113,251,95,255,64,251,78,253,43,251,193,252,104,250,48,253,133,250,19,254,126,252,28,253,102,252,223,252,178,251,110,254,213,249,60,252,219,251,130,253,11,251,98,250,37,250,90,252,34,250,129,252,194,249,204,253,69,249,51,253,162,253,171,253,114,251,195,251,167,250,44,254,102,248,43,250,210,248,71,252,116,248,93,252,37,250,68,255,157,249,91,254,79,250,174,254,88,250,234,255,106,248,90,254,42,248,7,255,16,254,142,255,138,248,13,253,247,250,174,0,85,250,147,255,30,254,255,254,59,251,4,254,175,249,151,0,98,249,208,0,114,253,107,0,141,249,29,0,139,251,23,1,65,251,50,1,52,251,6,254,38,253,81,255,44,251,155,255,55,252,39,2,154,252,22,1,201,252,59,1,205,253,120,1,229,251,228,0,5,254,24,1,169,253,25,1,10,253,253,0,207,254,123,1,13,253,122,255,157,253,148,2,200,252,24,2,207,252,134,2,99,254,49,0,171,254,177,0,59,254,14,2,30,254,77,2,185,255,83,1,111,253,8,1,12,255,39,1,19,255,59,1,125,254,57,2,6,254,247,255,135,254,14,0,96,255,149,2,40,255,40,0,204,254,210,255,95,0,214,0,14,255,167,0,170,255,192,0,200,255,27,0,180,255,31,0,36,0,53,1,150,255,74,255,143,255,74,0,71,254,234,255,23,0,139,0,81,0,245,255,44,0,15,0,169,255,119,255,138,255,49,255,98,255,198,255,16,1,164,255,100,255,71,254,8,0,120,255,128,0,35,255,101,0,38,255,40,0,59,255,180,255,56,254,9,0,67,254,33,0,89,254,226,0,60,0,73,0,34,255,156,0,113,254,24,1,194,254,245,0,171,254,166,0,13,254,83,1,66,255,71,1,37,255,69,1,119,255,167,255,172,253,100,0,141,253,144,0,91,253,231,1,28,0,252,0,121,254,214,0,215,255,26,1,228,255,99,0,226,254,75,1,49,0,203,1,124,254,53,2,143,254,180,1,28,0,80,1,247,255,141,1,89,255,106,2,34,0,84,2,239,255,49,2,116,255,43,1,79,0,10,2,125,0,203,0,2,0,244,0,32,1,255,0,211,0,175,0,82,0,84,2,187,0,5,2,108,0,125,1,255,0,109,1,41,1,241,1,96,1,71,1,174,255,25,0,210,0,115,1,245,0,5,1,3,0,33,2,193,1,140,0,38,1,44,0,39,1,212,0,91,1,244,0,238,1,75,1,16,2,201,0,51,1,93,1,155,1,101,2,28,1,102,2,157,1,208,1,66,1,112,2,141,1,97,0,200,0,96,255,128,1,149,0,106,1,239,1,13,2,13,1,73,2,33,0,235,1,135,255,177,1,171,1,99,2,242,1,4,2,171,0,187,1,241,1,154,2,184,1,19,1,54,2,63,2,146,0,127,2,155,0,158,2,223,255,173,0,212,0,184,2,90,255,89,2,65,255,183,2,23,254,247,1,175,0,230,2,214,0,220,1,116,1,59,4,66,2,18,2,74,2,9,3,169,1,106,3,59,1,73,3,118,1,80,3,91,255,53,2,35,0,223,3,217,255,38,4,73,1,200,2,18,3,72,3,133,2,27,3,149,2,164,2,59,2,150,3,120,2,55,4,161,2,49,3,62,1,132,1,106,3,244,3,52,2,80,3,112,3,108,2,45,2,223,1,159,2,197,1,180,2,212,1,72,3,130,2,76,3,133,2,250,1,172,1,129,3,55,2,69,3,131,1,194,3,243,1,179,2,49,2,171,3,158,3,15,3,40,1,22,3,12,1,4,4,18,2,106,3,73,1,36,2,143,0,163,2,35,1,247,1,66,0,17,4,103,1,18,3,97,0,37,3,33,0,69,3,214,1,255,1,49,0,68,4,71,1,150,4,67,1,3,0,242,0,104,3,218,1,177,2,173,1,49,5,166,2,18,4,108,2,85,4,152,2,65,1,193,0,121,3,182,3,129,4,106,3,125,3,123,2,109,3,94,3,180,3,145,3,13,5,153,2,40,5,127,2,229,3,25,3,122,5,6,4,152,4,244,3,86,4,191,3,130,5,157,3,123,5,147,3,31,2,94,3,92,4,198,4,67,3,166,4,67,3,166,4,191,3,124,4,123,4,96,5,20,5,169,4,135,5,207,4,55,5,61,5,234,2,68,4,175,6,3,5,109,5,49,4,54,5,30,6,129,4,195,5,109,6,113,4,33,7,196,4,32,4,102,5,241,5,194,6,96,6,9,6,84,6,6,6,87,3,60,6,97,3,131,6,181,2,117,3,180,6,239,5,143,4,16,5,161,8,224,6,160,7,213,5,228,7,202,5,254,5,74,7,158,6,216,7,30,6,236,2,225,6,57,3,38,1,112,5,60,4,10,8,109,2,35,5,109,1,7,5,198,0,4,4,232,1,128,5,249,0,147,1,246,3,25,6,68,1,107,1,109,6,20,4,193,0,111,1,242,7,67,7,5,255,67,2,238,2,226,3,13,255,30,0,45,5,111,3,228,255,87,255,112,2,149,3,59,254,159,0,186,0,90,5,154,253,6,0,25,2,136,1,162,255,221,254,13,3,229,0,128,255,214,254,245,0,235,1,67,253,120,253,204,3,21,3,11,254,128,253,178,0,255,0,147,254,122,254,1,255,61,1,66,252,218,254,65,255,228,0,249,252,65,254,157,0,19,255,111,253,48,253,105,254,92,0,139,255,157,253,78,1,26,255,89,253,196,251,112,255,195,254,123,252,163,252,30,253,152,254,171,255,41,253,166,255,237,252,100,0,234,255,121,254,249,254,200,255,183,255,175,254,14,253,5,0,67,255,62,253,144,253,89,0,168,254,121,255,167,251,159,254,19,255,84,253,145,251,237,254,178,251,243,254,77,251,152,0,145,0,46,253,48,251,49,0,80,0,32,251,248,252,8,255,135,1,36,253,221,253,213,1,218,0,1,255,160,252,69,0,110,1,90,255,27,254,80,253,191,0,68,251,84,251,86,255,87,255,228,250,161,249,65,1,214,1,117,250,37,251,192,255,16,1,175,250,8,255,236,1,53,2,47,253,159,253,195,0,229,1,195,253,123,255,171,1,202,0,85,255,138,255,199,0,63,2,2,0,225,255,182,2,243,2,170,250,217,255,40,2,45,2,23,254,15,1,168,2,25,2,13,0,59,254,87,3,186,3,123,255,204,255,175,255,226,2,111,251,125,2,31,4,35,4,161,255,164,2,235,4,57,4,233,1,49,1,63,254,186,3,234,253,228,3,55,252,98,3,222,251,35,4,242,250,106,2,120,250,105,2,54,254,86,5,97,255,29,7,250,252,240,253,242,255,86,4,78,251,123,252,252,252,177,1,24,251,25,251,13,252,210,254,166,253,183,253,9,253,174,249,8,253,243,249,184,252,127,248,208,252,229,253,23,249,69,247,29,255,220,255,14,248,217,248,197,247,154,251,89,246,232,248,66,250,252,0,115,245,97,254,197,253,45,254,229,5,18,6,132,8,183,7,22,9,228,7,191,248,111,249,191,248,37,249,248,247,130,251,170,247,138,249,173,249,181,251,88,249,149,251,191,250,184,249,177,250,154,249,198,250,243,250,211,250,15,251,128,249,143,249,49,250,173,252,190,250,216,248,123,250,116,247,254,250,87,253,7,249,143,249,58,252,198,251,97,251,116,249,226,251,207,251,138,251,122,251,73,251,24,253,6,251,27,252,90,252,153,250,97,252,120,250,14,252,231,250,241,252,69,252,231,251,124,252,31,252,207,252,31,253,201,252,52,252,91,251,30,253,186,251,30,253,126,251,240,252,223,252,214,252,238,252,132,252,248,253,24,252,206,252,124,253,59,252,191,253,142,252,227,253,74,253,97,253,107,252,173,253,126,253,122,253,153,253,68,252,147,253,99,252,253,253,41,253,29,254,209,252,27,254,184,252,190,253,72,254,55,253,190,253,187,254,111,253,98,253,126,254,198,253,71,254,102,253,254,253,237,252,120,254,239,253,246,253,59,254,25,254,89,254,152,253,183,253,151,253,99,255,106,253,244,254,88,253,164,254,190,254,189,254,136,253,68,254,208,254,82,254,180,254,54,254,235,254,44,254,109,253,231,252,193,254,132,253,29,255,214,253,139,254,165,254,178,254,46,255,56,254,64,255,238,253,14,255,40,255,58,255,146,254,142,254,174,254,95,255,103,254,20,253,149,255,132,254,218,254,125,253,33,255,103,253,22,255,27,253,115,255,16,254,126,255,2,254,117,255,185,254,84,255,207,254,206,254,188,253,92,255,249,254,250,254,84,255,189,255,110,254,31,0,146,254,246,255,76,254,170,255,241,253,71,0,135,254,234,255,159,253,244,255,90,253,189,255,193,254,63,0,65,255,35,0,75,255,217,255,14,255,126,0,89,255,116,255,224,253,155,0,215,254,174,0,215,254,38,0,248,255,117,0,132,254,197,0,60,254,240,0,246,253,223,0,153,255,110,0,69,255,87,0,101,255,169,0,209,255,157,0,26,0,173,255,156,255,128,0,80,0,209,0,194,255,6,0,7,0,22,0,5,0,62,1,236,255,248,0,211,255,56,255,193,255,156,0,187,255,250,0,73,255,113,1,130,255,143,255,180,255,114,255,134,255,192,255,2,255,225,255,35,0,79,255,185,255,249,255,171,0,93,0,27,0,108,0,212,0,182,254,47,255,133,255,186,255,233,254,95,0,160,255,20,0,68,255,195,255,198,254,87,0,212,254,178,255,158,254,122,255,11,0,122,0,116,255,122,0,237,254,152,0,219,254,140,0,174,255,138,0,191,254,145,255,32,254,100,255,153,254,76,0,2,255,216,255,133,253,160,255,246,253,79,0,5,254,8,0,244,254,47,1,229,253,68,0,66,254,61,0,246,253,50,1,111,0,189,0,77,254,122,0,133,254,166,0,197,253,114,254,136,253,182,255,21,253,161,255,57,254,194,0,72,252,83,0,226,252,192,0,13,253,192,0,243,252,94,255,149,253,234,0,105,253,215,254,24,254,147,255,60,252,124,255,186,252,188,255,181,252,58,0,168,251,170,255,219,252,213,254,80,252,3,255,246,252,206,255,59,252,219,253,160,254,158,255,32,252,169,254,163,251,197,254,163,251,205,254,125,251,138,254,131,253,26,255,114,251,213,255,237,250,156,255,99,252,119,254,6,251,168,253,79,253,126,255,57,250,200,254,215,250,2,255,72,250,70,254,244,250,155,253,19,251,9,254,35,250,144,254,214,250,26,0,104,250,190,255,49,249,95,255,148,249,45,254,32,249,220,253,143,250,200,253,236,249,153,252,41,250,246,251,149,250,197,253,131,248,240,253,9,249,133,255,151,248,25,255,250,247,189,254,252,247,118,252,72,248,201,253,131,248,148,253,1,248,35,252,203,251,142,254,17,248,64,253,205,246,19,253,76,245,191,251,139,248,159,0,36,248,248,0,142,253,133,255,221,246,62,252,99,253,104,254,157,250,106,251,60,254,148,254,236,251,33,253,124,255,183,0,172,249,16,253,221,253,205,254,247,252,19,251,158,255,41,0,144,252,189,251,255,254,97,0,190,249,215,248,31,0,230,255,124,253,207,253,76,255,222,253,127,254,185,251,102,254,222,252,98,254,197,252,55,254,54,252,22,254,171,251,41,255,108,252,112,255,87,252,19,254,11,251,251,253,29,250,181,0,101,0,180,254,135,252,188,252,87,252,209,253,83,254,139,253,221,253,73,255,175,254,223,253,174,255,6,255,226,254,5,0,124,255,164,254,4,255,219,254,40,254,98,255,100,0,227,255,197,0,20,255,88,254,163,252,43,255,116,255,249,255,85,254,69,254,187,0,159,255,84,253,32,253,219,254,2,1,144,254,104,255,106,255,136,1,159,253,175,0,114,255,43,1,118,255,152,0,137,255,73,1,26,254,204,255,37,1,198,0,73,255,117,0,175,0,75,1,198,255,238,254,231,0,44,1,224,254,74,1,207,254,116,1,145,255,153,1,247,255,167,1,83,0,0,1,67,0,111,1,237,255,248,0,91,0,113,0,221,255,150,1,65,255,154,0,238,0,40,1,5,0,197,0,141,0,221,0,57,1,198,0,211,0,165,1,244,0,78,1,88,0,170,1,13,255,198,1,202,0,40,2,251,255,147,1,35,1,185,0,219,0,45,1,251,0,138,0,128,0,69,0,197,0,32,1,116,255,195,255,188,0,105,1,197,0,86,2,186,1,17,1,34,1,143,0,216,1,226,1,157,0,114,1,159,1,65,1,116,1,129,1,146,1,40,2,155,0,24,0,38,2,7,1,245,255,21,0,104,1,227,0,147,0,2,255,168,1,97,0,110,1,243,255,119,1,141,0,193,1,232,0,140,1,251,1,218,1,16,1,189,2,68,1,106,1,209,255,75,2,148,0,31,2,69,0,144,1,205,255,49,2,59,0,220,0,246,255,96,1,147,0,206,0,211,0,141,2,185,0,51,2,41,1,53,2,28,1,82,2,121,0,254,2,192,0,142,1,118,0,130,2,178,1,233,0,8,1,225,1,211,1,129,0,91,255,187,2,239,0,90,0,26,0,86,1,218,1,201,255,27,0,132,1,94,0,84,255,0,0,213,2,123,1,196,255,81,1,114,1,209,1,95,0,63,1,38,3,83,2,78,0,4,1,241,1,83,3,210,0,48,2,202,1,62,2,48,254,202,0,241,1,113,2,54,255,152,0,48,0,200,2,236,255,54,2,100,0,203,2,199,1,212,1,155,1,93,2,63,1,134,2,195,0,103,2,145,1,26,2,168,2,227,2,201,0,155,2,178,1,186,3,198,1,169,1,134,2,235,1,94,2,169,2,160,1,252,1,241,1,54,3,170,1,47,3,148,2,135,2,116,2,204,2,185,2,210,1,106,2,201,1,173,2,204,1,109,1,53,1,209,2,55,2,68,3,89,2,97,2,44,1,57,3,203,1,175,3,175,2,169,2,21,2,147,3,86,2,79,2,243,0,108,3,195,1,106,3,164,1,18,3,61,1,220,2,220,0,154,3,61,1,84,4,111,1,19,2,210,1,4,4,137,2,29,4,103,2,10,4,41,2,61,3,90,2,253,3,31,3,159,3,35,3,110,3,251,2,31,3,240,1,93,5,5,3,73,2,2,3,35,3,162,3,75,4,25,3,198,4,94,3,185,4,127,3,1,4,215,2,4,3,77,3,148,4,91,4,99,3,253,3,62,3,245,3,73,3,142,3,250,1,191,2,215,4,53,4,108,2,51,3,172,4,59,4,131,4,57,4,118,4,139,3,11,6,97,4,29,5,136,2,63,5,100,2,204,5,220,3,199,5,169,3,217,3,48,5,187,3,61,5,173,1,142,3,73,3,58,5,52,2,155,4,156,1,132,4,147,5,40,5,154,5,50,5,128,2,248,2,190,6,130,5,190,0,43,2,49,4,237,3,170,1,1,1,71,3,212,3,235,0,231,0,240,5,143,4,109,0,37,1,246,3,33,6,49,1,142,0,124,4,27,2,221,254,148,255,189,4,204,3,22,0,40,255,155,2,60,3,30,254,182,1,197,1,151,5,187,253,90,254,21,3,131,1,154,254,58,254,174,0,12,3,220,255,140,254,134,1,122,255,139,253,160,0,206,254,239,2,22,251,181,254,177,0,10,2,8,255,62,2,5,255,127,2,237,253,151,1,172,253,138,1,93,254,21,3,151,253,33,3,38,252,143,1,167,252,215,2,249,255,6,2,65,253,54,1,137,251,232,255,22,252,31,1,64,252,107,1,237,250,56,1,2,250,245,0,235,249,49,1,28,0,153,0,165,252,81,255,223,255,76,1,138,250,102,255,212,0,154,1,175,253,59,255,188,251,64,253,120,252,191,255,26,1,111,1,106,252,82,253,89,1,93,0,254,254,155,254,184,2,132,2,75,253,228,255,192,1,237,1,239,254,193,0,15,2,34,2,13,255,255,253,128,1,120,255,17,1,159,254,0,2,114,255,25,2,58,255,173,3,238,2,83,0,248,0,66,2,93,3,200,255,80,2,74,3,44,0,124,3,24,0,33,0,122,3,240,255,214,3,63,3,118,5,255,5,106,7,180,6,96,5,156,7,185,5,22,252,95,252,184,251,77,251,127,253,93,252,164,253,63,252,245,252,95,253,189,252,236,252,96,254,104,253,54,254,2,253,116,253,247,253,106,253,17,254,1,252,3,254,1,252,84,254,68,254,216,253,144,254,63,254,33,254,45,255,226,251,121,252,196,254,7,255,199,253,177,253,199,253,237,254,227,253,65,255,52,253,68,255,182,252,248,254,179,254,8,255,194,254,28,255,237,254,1,0,201,253,28,255,141,255,35,255,18,255,138,254,59,255,5,254,34,255,189,253,254,254,80,254,195,255,12,255,167,254,2,0,174,254,39,0,41,255,87,255,198,255,0,0,200,255,250,255,53,255,125,255,1,0,70,255,251,255,45,255,6,0,132,254,11,0,94,254,140,255,131,0,122,255,113,0,89,0,252,255,71,0,254,255,237,255,64,255,6,1,24,0,189,0,151,0,123,255,147,255,186,0,103,255,166,0,37,255,37,0,139,0,193,0,171,0,81,1,124,0,158,0,195,255,141,0,226,0,243,255,190,0,231,0,34,0,98,1,109,0,60,1,201,0,244,0,164,0,74,1,171,255,134,1,172,255,254,0,71,1,1,1,79,1,235,1,147,0,220,1,105,0,54,0,77,0,181,1,114,1,165,1,58,1,193,1,86,1,73,1,126,0,161,2,36,1,59,2,132,1,243,0,193,0,141,2,64,1,109,2,24,1,194,0,124,1,5,2,69,2,45,0,67,1,111,0,166,1,233,1,139,1,222,2,22,2,110,2,34,2,230,1,246,1,62,1,60,2,189,0,38,2,129,1,166,1,99,255,153,0,131,255,126,1,59,255,130,1,249,254,78,1,228,0,185,2,68,255,1,0,51,0,41,1,5,254,213,0,136,254,141,1,232,255,255,0,221,253,89,0,10,254,162,255,131,1,179,0,148,253,68,0,84,253,112,0,126,253,162,254,252,254,172,0,74,254,188,254,8,1,136,2,60,252,252,255,159,251,7,0,122,255,134,0,147,251,206,254,143,0,96,0,92,254,15,254,59,251,162,254,9,250,83,253,95,255,72,0,105,3,179,2,220,2,27,1,153,3,97,1,78,1,219,1,71,4,53,3,96,3,12,2,75,3,241,1,202,2,199,2,20,3,238,2,52,4,202,2,180,4,241,2,65,2,150,2,124,245,170,192,38,3,44,7,95,251,33,228,37,12,28,4,40,248,202,208,85,16,107,5,192,249,99,218,69,9,145,5,232,249,78,219,176,12,193,7,210,251,214,230,35,7,16,9,184,252,64,236,173,3,242,12,199,254,163,248,47,9,161,11,41,254,234,244,32,14,116,9,247,252,183,237,123,13,24,12,98,254,70,246,139,11,205,16,72,0,178,1,56,7,148,17,139,0,68,3,44,15,40,21,157,1,180,9,163,4,42,28,67,3,166,19,11,12,40,35,139,4,90,27,216,28,115,3,37,247,177,202,74,23,226,5,58,250,60,221,35,20,86,8,61,252,88,233,8,31,217,7,228,251,65,231,107,25,202,8,139,252,49,235,246,29,192,10,180,253,47,242,64,23,200,11,60,254,92,245,34,19,180,14,131,255,17,253,77,27,4,14,60,255,103,251,238,31,138,15,213,255,252,254,176,23,52,17,107,0,133,2,29,30,223,19,64,1,136,7,147,21,133,23,57,2,98,13,89,30,214,27,50,3,62,19,172,23,2,31,209,3,253,22,218,21,223,44,243,5,212,35,85,41,76,5,159,249,153,217,89,35,61,6,145,250,68,223,66,38,243,7,247,251,180,231,242,34,111,9,244,252,164,237,56,40,24,10,87,253,253,239,191,36,174,10,171,253,245,241,252,33,146,12,156,254,160,247,29,38,67,13,235,254,123,249,193,39,52,15,181,255,58,254,210,35,176,17,148,0,123,3,168,39,140,19,40,1,245,6,154,35,103,22,241,1,177,11,4,41,122,24,116,2,198,14,126,39,207,29,151,3,158,21,140,34,23,34,93,4,72,26,252,34,208,48,112,6,193,38,124,50,208,3,185,247,47,206,171,44,219,6,28,251,141,226,106,47,24,9,189,252,96,236,124,44,64,9,214,252,248,236,204,41,248,11,83,254,236,245,44,48,45,11,238,253,136,243,202,45,255,12,205,254,200,248,6,44,116,14,106,255,120,252,109,42,61,17,110,0,151,2,50,47,181,17,150,0,134,3,19,44,85,20,98,1,84,8,184,46,161,24,125,2,253,14,159,43,110,29,132,3,44,21,96,47,137,32,25,4,168,24,217,42,25,42,149,5,156,33,60,40,224,67,87,8,53,50,75,54,145,6,220,250,15,225,36,49,253,7,254,251,221,231,209,51,135,9,2,253,254,237,209,54,173,11,47,254,14,245,140,52,26,12,99,254,78,246,108,48,74,14,89,255,18,252,198,52,196,14,137,255,55,253,80,50,176,16,62,0,118,1,221,52,253,18,253,0,243,5,123,49,81,21,168,1,248,9,30,54,218,23,78,2,223,13,231,50,83,25,166,2,244,15,245,52,41,30,169,3,7,22,157,50,95,36,189,4,136,28,146,53,31,45,252,5,5,36,47,49,102,59,146,7,147,45,9,59,4,6,91,250,4,222,224,58,29,9,192,252,113,236,191,56,207,9,45,253,0,239,100,57,127,12,147,254,107,247,22,60,232,13,49,255,33,251,53,55,120,15,206,255,212,254,254,58,140,16,50,0,42,1,252,55,216,18,242,0,174,5,254,57,75,21,166,1,238,9,202,59,195,23,72,2,190,13,249,55,232,26,0,3,15,18,212,58,9,30,162,3,226,21,70,56,210,36,207,4,245,28,27,60,13,38,0,5,26,30,232,57,191,55,52,7,94,43,32,53,107,97,109,10,195,62,12,64,177,7,198,251,139,230,177,65,16,11,223,253,45,243,97,61,27,11,229,253,80,243,232,62,8,13,209,254,223,248,0,64,123,15,207,255,218,254,44,66,227,17,165,0,224,3,95,61,247,17,171,0,6,4,94,63,72,21,165,1,233,9,192,65,238,24,143,2,105,15,129,61,229,27,53,3,80,19,198,63,45,29,120,3,223,20,227,64,176,33,76,4,222,25,132,66,178,40,99,5,111,32,33,62,41,46,29,6,207,36,238,65,98,57,95,7,96,44,131,64,134,81,102,9,147,56,222,70,35,8,25,252,131,232,201,75,106,12,137,254,47,247,100,68,98,13,248,254,203,249,86,78,187,15,231,255,105,255,149,70,153,16,54,0,70,1,8,74,202,19,58,1,98,7,47,69,26,21,153,1,157,9,123,77,48,24,98,2,92,14,30,70,102,27,27,3,176,18,70,83,197,30,198,3,184,22,246,69,73,36,186,4,115,28,200,74,74,36,186,4,116,28,37,80,117,44,230,5,129,35,155,70,149,56,74,7,226,43,31,78,218,69,129,8,52,51,154,73,252,127,0,12,62,72,61,42,81,112,63,11,181,67,0,80,225,10,198,253,153,242,153,73,194,25,191,2,139,16,81,24,245,28,108,3,156,20,51,67,204,40,103,5,133,32,122,84,245,4,61,249,74,215,143,82,71,17,113,0,171,2,40,44,20,6,106,250,95,222,61,74,20,50,150,6,164,39,215,67,194,9,37,253,210,238,194,69,225,18,244,0,192,5,10,39,194,9,37,253,210,238,122,68,184,30,196,3,170,22,174,55,92,7,133,251,5,229,20,62,81,12,125,254,233,246,61,26,10,7,67,251,121,227,10,71,225,78,53,9,109,55,102,70,215,11,67,254,138,245,71,65,225,22,16,2,109,12,143,34,174,15,226,255,76,255,20,62,10,35,134,4,60,27,102,70,112,5,198,249,129,218,71,65,0,16,0,0,0,0,0,32,143,2,108,245,79,192,133,59,102,54,16,7,132,42,174,55,40,12,106,254,116,246,10,55,61,18,193,0,141,4,30,21,143,10,154,253,143,241,122,52,153,25,182,2,84,16,163,48,133,3,67,247,100,203,163,48,102,10,131,253,7,241,184,14,143,2,108,245,79,192,153,57,215,91,22,10,183,60,225,74,153,9,13,253,62,238,184,78,215,19,62,1,121,7,225,26,0,16,0,0,0,0,0,80,112,33,65,4,156,25,204,76,225,2,26,246,105,196,61,74,163,16,58,0,91,1,184,30,40,8,29,252,151,232,204,44,0,48,87,6,43,38,20,62,194,5,26,250,126,220,112,61,20,18,180,0,62,4,215,35,153,5,240,249,131,219,184,62,92,27,25,3,164,18,235,57,225,2,26,246,105,196,225,58,204,8,140,252,55,235,215,19,204,4,12,249,38,214,215,51,174,67,83,8,27,50,163,64,30,9,193,252,118,236,225,58,184,22,6,2,46,12,92,15,102,14,100,255,86,252,174,55,153,33,72,4,198,25,235,65,10,3,106,246,74,198,225,58,225,14,149,255,122,253,174,23,102,2,12,245,17,190,122,36,40,36,180,4,83,28,215,51,225,6,33,251,172,226,215,51,194,13,33,255,193,250,153,9,174,7,196,251,127,230,204,44,153,21,187,1,108,10,245,40,225,2,26,246,105,196,112,45,122,12,145,254,92,247,194,5,10,3,106,246,74,198,0,64,248,65,226,67,190,69,142,71,82,73,12,75,188,76,98,78,0,80,150,81,35,83,170,84,42,86,163,87,22,89,130,90,234,91,76,93,168,94,0,96,83,97,161,98,236,99,49,101,115,102,177,103,235,104,34,106,85,107,132,108,177,109,218,110,0,112,35,113,67,114,97,115,123,116,147,117,169,118,188,119,204,120,218,121,230,122,239,123,247,124,252,125,255,126,255,127,255,127,61,10,63,10,69,10,78,10,91,10,108,10,129,10,153,10,181,10,212,10,248,10,31,11,74,11,120,11,170,11,224,11,25,12,86,12,151,12,219,12,35,13,110,13,189,13,15,14,101,14,190,14,27,15,123,15,223,15,70,16,176,16,30,17,143,17,3,18,123,18,245,18,115,19,244,19,120,20,0,21,138,21,23,22,168,22,59,23,209,23,106,24,6,25,165,25,70,26,234,26,145,27,59,28,231,28,149,29,70,30,250,30,176,31,104,32,35,33,224,33,159,34,97,35,36,36,234,36,178,37,124,38,71,39,21,40,228,40,181,41,136,42,93,43,51,44,11,45,228,45,191,46,155,47,121,48,88,49,56,50,26,51,252,51,224,52,196,53,170,54,145,55,120,56,96,57,73,58,51,59,29,60,8,61,243,61,223,62,203,63,184,64,165,65,146,66,127,67,108,68,90,69,71,70,52,71,33,72,14,73,251,73,231,74,211,75,191,76,170,77,149,78,126,79,104,80,80,81,56,82,31,83,5,84,234,84,207,85,178,86,148,87,116,88,84,89,50,90,15,91,235,91,197,92,157,93,117,94,74,95,30,96,240,96,192,97,143,98,91,99,38,100,239,100,181,101,122,102,60,103,253,103,187,104,119,105,48,106,232,106,156,107,79,108,255,108,172,109,87,110,255,110,165,111,71,112,231,112,133,113,31,114,183,114,75,115,221,115,108,116,248,116,129,117,6,118,137,118,8,119,133,119,254,119,116,120,230,120,86,121,194,121,42,122,144,122,242,122,80,123,171,123,3,124,87,124,167,124,244,124,62,125,132,125,198,125,5,126,64,126,120,126,172,126,220,126,9,127,49,127,87,127,120,127,150,127,176,127,199,127,217,127,232,127,243,127,251,127,255,127,255,127,229,127,153,127,25,127,103,126,129,125],"i8",k,v.GLOBAL_BASE+20480),c([106,124,33,123,167,121,252,119,34,118,24,116,223,113,122,111,231,108,41,106,65,103,47,100,245,96,149,93,15,90,101,86,153,82,171,78,158,74,116,70,45,66,204,61,82,57,193,52,27,48,98,43,151,38,189,33,213,28,226,23,230,18,226,13,216,8,203,3,61,10,64,10,73,10,88,10,108,10,135,10,167,10,205,10,249,10,43,11,99,11,160,11,227,11,44,12,122,12,207,12,40,13,136,13,237,13,87,14,199,14,60,15,183,15,55,16,189,16,71,17,215,17,108,18,6,19,165,19,73,20,242,20,159,21,82,22,9,23,196,23,133,24,73,25,18,26,224,26,177,27,135,28,97,29,62,30,32,31,5,32,238,32,219,33,203,34,191,35,182,36,176,37,174,38,174,39,177,40,184,41,193,42,204,43,218,44,235,45,254,46,19,48,42,49,67,50,94,51,123,52,154,53,186,54,219,55,254,56,34,58,71,59,109,60,148,61,188,62,228,63,13,65,54,66,96,67,138,68,180,69,221,70,7,72,48,73,89,74,130,75,169,76,208,77,246,78,27,80,63,81,98,82,132,83,164,84,194,85,223,86,250,87,19,89,43,90,64,91,83,92,99,93,113,94,125,95,134,96,140,97,143,98,144,99,141,100,135,101,126,102,114,103,98,104,79,105,56,106,30,107,255,107,221,108,183,109,140,110,94,111,43,112,244,112,185,113,121,114,53,115,236,115,158,116,76,117,245,117,153,118,55,119,209,119,102,120,246,120,129,121,6,122,134,122,1,123,118,123,230,123,81,124,182,124,21,125,111,125,195,125,17,126,90,126,157,126,219,126,18,127,68,127,112,127,150,127,183,127,209,127,230,127,244,127,253,127,255,127,255,127,244,127,208,127,149,127,66,127,215,126,85,126,188,125,12,125,69,124,104,123,117,122,108,121,78,120,28,119,213,117,122,116,13,115,140,113,250,111,87,110,162,108,222,106,11,105,40,103,57,101,60,99,51,97,30,95,255,92,215,90,165,88,108,86,44,84,229,81,154,79,74,77,247,74,161,72,74,70,243,67,156,65,71,63,244,60,164,58,88,56,18,54,209,51,152,49,103,47,62,45,31,43,11,41,2,39,5,37,21,35,51,33,95,31,155,29,231,27,67,26,177,24,49,23,195,21,105,20,34,19,239,17,209,16,201,15,214,14,249,13,50,13,130,12,232,11,102,11,252,10,169,10,109,10,73,10,61,10,61,10,63,10,67,10,74,10,84,10,96,10,111,10,129,10,150,10,174,10,200,10,229,10,5,11,39,11,77,11,117,11,159,11,205,11,253,11,48,12,101,12,157,12,216,12,22,13,86,13,153,13,222,13,38,14,113,14,190,14,13,15,96,15,181,15,12,16,102,16,194,16,33,17,130,17,230,17,76,18,180,18,31,19,140,19,252,19,110,20,226,20,88,21,209,21,76,22,201,22,72,23,202,23,77,24,211,24,91,25,229,25,113,26,254,26,142,27,32,28,180,28,74,29,225,29,123,30,22,31,179,31,82,32,242,32,149,33,57,34,222,34,133,35,46,36,216,36,132,37,50,38,224,38,145,39,66,40,245,40,169,41,95,42,22,43,206,43,135,44,66,45,253,45,186,46,120,47,54,48,246,48,183,49,120,50,59,51,254,51,194,52,135,53,77,54,19,55,218,55,161,56,106,57,50,58,252,58,197,59,144,60,90,61,37,62,240,62,188,63,136,64,84,65,32,66,236,66,185,67,133,68,82,69,30,70,235,70,183,71,132,72,80,73,28,74,231,74,179,75,126,76,73,77,19,78,221,78,166,79,111,80,56,81,0,82,199,82,142,83,84,84,25,85,221,85,161,86,100,87,38,88,231,88,167,89,103,90,37,91,226,91,158,92,89,93,19,94,204,94,131,95,57,96,238,96,162,97,84,98,5,99,181,99,99,100,15,101,186,101,100,102,12,103,178,103,87,104,250,104,155,105,59,106,217,106,117,107,16,108,168,108,63,109,211,109,102,110,247,110,134,111,19,112,158,112,39,113,174,113,50,114,181,114,53,115,179,115,47,116,169,116,33,117,150,117,9,118,122,118,232,118,84,119,190,119,37,120,138,120,236,120,76,121,170,121,5,122,94,122,180,122,7,123,88,123,167,123,242,123,60,124,130,124,198,124,8,125,71,125,131,125,188,125,243,125,39,126,89,126,136,126,180,126,221,126,4,127,40,127,73,127,103,127,131,127,156,127,178,127,197,127,214,127,228,127,239,127,247,127,253,127,255,127,255,127,97,125,160,117,15,105,48,88,181,67,116,44,98,19,68,101,99,111,100,101,114,0,101,110,99,111,100,101,114,0],"i8",k,v.GLOBAL_BASE+30720);var ce=v.alignMemory(c(12,"i8",b),8);function de(e){var r,i=de;i.called||(0<(r=x)%4096&&(r+=4096-r%4096),x=r,i.called=!0,w(v.dynamicAlloc),i.alloc=v.dynamicAlloc,v.dynamicAlloc=function(){Be("cannot dynamically allocate, sbrk now has control")});var n=x;if(0!=e&&!i.alloc(e))return-1>>>0;return n}function he(e){return p.___errno_location&&(M[p.___errno_location()>>2]=e),e}w(ce%8==0);var we={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};p._memcpy=Le,p._memmove=Me,p._memset=Ne;var me={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"},pe={ttys:[],init:function(){},shutdown:function(){},register:function(e,r){pe.ttys[e]={input:[],output:[],ops:r},Ee.registerDevice(e,pe.stream_ops)},stream_ops:{open:function(e){var r=pe.ttys[e.node.rdev];if(!r)throw new Ee.ErrnoError(we.ENODEV);e.tty=r,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,r,i,n,t){if(!e.tty||!e.tty.ops.get_char)throw new Ee.ErrnoError(we.ENXIO);for(var o=0,a=0;a<n;a++){var f;try{f=e.tty.ops.get_char(e.tty)}catch(e){throw new Ee.ErrnoError(we.EIO)}if(void 0===f&&0===o)throw new Ee.ErrnoError(we.EAGAIN);if(null==f)break;o++,r[i+a]=f}return o&&(e.node.timestamp=Date.now()),o},write:function(e,r,i,n,t){if(!e.tty||!e.tty.ops.put_char)throw new Ee.ErrnoError(we.ENXIO);for(var o=0;o<n;o++)try{e.tty.ops.put_char(e.tty,r[i+o])}catch(e){throw new Ee.ErrnoError(we.EIO)}return n&&(e.node.timestamp=Date.now()),o}},default_tty_ops:{get_char:function(e){if(!e.input.length){var r=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(r=window.prompt("Input: "))&&(r+="\n"):"function"==typeof readline&&null!==(r=readline())&&(r+="\n"),!r)return null;e.input=$(r,!0)}return e.input.shift()},put_char:function(e,r){null===r||10===r?(p.print(d(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&0<e.output.length&&(p.print(d(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,r){null===r||10===r?(p.printErr(d(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&0<e.output.length&&(p.printErr(d(e.output,0)),e.output=[])}}},ve={ops_table:null,mount:function(e){return ve.createNode(null,"/",16895,0)},createNode:function(e,r,i,n){if(Ee.isBlkdev(i)||Ee.isFIFO(i))throw new Ee.ErrnoError(we.EPERM);ve.ops_table||(ve.ops_table={dir:{node:{getattr:ve.node_ops.getattr,setattr:ve.node_ops.setattr,lookup:ve.node_ops.lookup,mknod:ve.node_ops.mknod,rename:ve.node_ops.rename,unlink:ve.node_ops.unlink,rmdir:ve.node_ops.rmdir,readdir:ve.node_ops.readdir,symlink:ve.node_ops.symlink},stream:{llseek:ve.stream_ops.llseek}},file:{node:{getattr:ve.node_ops.getattr,setattr:ve.node_ops.setattr},stream:{llseek:ve.stream_ops.llseek,read:ve.stream_ops.read,write:ve.stream_ops.write,allocate:ve.stream_ops.allocate,mmap:ve.stream_ops.mmap,msync:ve.stream_ops.msync}},link:{node:{getattr:ve.node_ops.getattr,setattr:ve.node_ops.setattr,readlink:ve.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ve.node_ops.getattr,setattr:ve.node_ops.setattr},stream:Ee.chrdev_stream_ops}});var t=Ee.createNode(e,r,i,n);return Ee.isDir(t.mode)?(t.node_ops=ve.ops_table.dir.node,t.stream_ops=ve.ops_table.dir.stream,t.contents={}):Ee.isFile(t.mode)?(t.node_ops=ve.ops_table.file.node,t.stream_ops=ve.ops_table.file.stream,t.usedBytes=0,t.contents=null):Ee.isLink(t.mode)?(t.node_ops=ve.ops_table.link.node,t.stream_ops=ve.ops_table.link.stream):Ee.isChrdev(t.mode)&&(t.node_ops=ve.ops_table.chrdev.node,t.stream_ops=ve.ops_table.chrdev.stream),t.timestamp=Date.now(),e&&(e.contents[r]=t),t},getFileDataAsRegularArray:function(e){if(e.contents&&e.contents.subarray){for(var r=[],i=0;i<e.usedBytes;++i)r.push(e.contents[i]);return r}return e.contents},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array},expandFileStorage:function(e,r){if(e.contents&&e.contents.subarray&&r>e.contents.length&&(e.contents=ve.getFileDataAsRegularArray(e),e.usedBytes=e.contents.length),!e.contents||e.contents.subarray){var i=e.contents?e.contents.buffer.byteLength:0;if(r<=i)return;r=Math.max(r,i*(i<1048576?2:1.125)|0),0!=i&&(r=Math.max(r,256));var n=e.contents;return e.contents=new Uint8Array(r),void(0<e.usedBytes&&e.contents.set(n.subarray(0,e.usedBytes),0))}for(!e.contents&&0<r&&(e.contents=[]);e.contents.length<r;)e.contents.push(0)},resizeFileStorage:function(e,r){if(e.usedBytes!=r){if(0==r)return e.contents=null,void(e.usedBytes=0);if(!e.contents||e.contents.subarray){var i=e.contents;return e.contents=new Uint8Array(new ArrayBuffer(r)),i&&e.contents.set(i.subarray(0,Math.min(r,e.usedBytes))),void(e.usedBytes=r)}if(e.contents||(e.contents=[]),e.contents.length>r)e.contents.length=r;else for(;e.contents.length<r;)e.contents.push(0);e.usedBytes=r}},node_ops:{getattr:function(e){var r={};return r.dev=Ee.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,Ee.isDir(e.mode)?r.size=4096:Ee.isFile(e.mode)?r.size=e.usedBytes:Ee.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.timestamp),r.mtime=new Date(e.timestamp),r.ctime=new Date(e.timestamp),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp),void 0!==r.size&&ve.resizeFileStorage(e,r.size)},lookup:function(e,r){throw Ee.genericErrors[we.ENOENT]},mknod:function(e,r,i,n){return ve.createNode(e,r,i,n)},rename:function(e,r,i){if(Ee.isDir(e.mode)){var n;try{n=Ee.lookupNode(r,i)}catch(e){}if(n)for(var t in n.contents)throw new Ee.ErrnoError(we.ENOTEMPTY)}delete e.parent.contents[e.name],e.name=i,(r.contents[i]=e).parent=r},unlink:function(e,r){delete e.contents[r]},rmdir:function(e,r){var i=Ee.lookupNode(e,r);for(var n in i.contents)throw new Ee.ErrnoError(we.ENOTEMPTY);delete e.contents[r]},readdir:function(e){var r=[".",".."];for(var i in e.contents)e.contents.hasOwnProperty(i)&&r.push(i);return r},symlink:function(e,r,i){var n=ve.createNode(e,r,41471,0);return n.link=i,n},readlink:function(e){if(!Ee.isLink(e.mode))throw new Ee.ErrnoError(we.EINVAL);return e.link}},stream_ops:{read:function(e,r,i,n,t){var o=e.node.contents;if(t>=e.node.usedBytes)return 0;var a=Math.min(e.node.usedBytes-t,n);if(w(0<=a),8<a&&o.subarray)r.set(o.subarray(t,t+a),i);else for(var f=0;f<a;f++)r[i+f]=o[t+f];return a},write:function(e,r,i,n,t,o){if(!n)return 0;var a=e.node;if(a.timestamp=Date.now(),r.subarray&&(!a.contents||a.contents.subarray)){if(o)return a.contents=r.subarray(i,i+n),a.usedBytes=n;if(0===a.usedBytes&&0===t)return a.contents=new Uint8Array(r.subarray(i,i+n)),a.usedBytes=n;if(t+n<=a.usedBytes)return a.contents.set(r.subarray(i,i+n),t),n}if(ve.expandFileStorage(a,t+n),a.contents.subarray&&r.subarray)a.contents.set(r.subarray(i,i+n),t);else for(var f=0;f<n;f++)a.contents[t+f]=r[i+f];return a.usedBytes=Math.max(a.usedBytes,t+n),n},llseek:function(e,r,i){var n=r;if(1===i?n+=e.position:2===i&&Ee.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new Ee.ErrnoError(we.EINVAL);return n},allocate:function(e,r,i){ve.expandFileStorage(e.node,r+i),e.node.usedBytes=Math.max(e.node.usedBytes,r+i)},mmap:function(e,r,i,n,t,o,a){if(!Ee.isFile(e.node.mode))throw new Ee.ErrnoError(we.ENODEV);var f,s,l=e.node.contents;if(2&a||l.buffer!==r&&l.buffer!==r.buffer){if((0<t||t+n<e.node.usedBytes)&&(l=l.subarray?l.subarray(t,t+n):Array.prototype.slice.call(l,t,t+n)),s=!0,!(f=Oe(n)))throw new Ee.ErrnoError(we.ENOMEM);r.set(l,f)}else s=!1,f=l.byteOffset;return{ptr:f,allocated:s}},msync:function(e,r,i,n,t){if(!Ee.isFile(e.node.mode))throw new Ee.ErrnoError(we.ENODEV);if(2&t)return 0;ve.stream_ops.write(e,r,0,n,i,!1);return 0}}},be={dbs:{},indexedDB:function(){if("undefined"!=typeof indexedDB)return indexedDB;var e=null;return"object"==typeof window&&(e=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB),w(e,"IDBFS used, but indexedDB not supported"),e},DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:function(e){return ve.mount.apply(null,arguments)},syncfs:function(r,o,a){be.getLocalSet(r,function(e,t){if(e)return a(e);be.getRemoteSet(r,function(e,r){if(e)return a(e);var i=o?r:t,n=o?t:r;be.reconcile(i,n,a)})})},getDB:function(e,r){var i,n=be.dbs[e];if(n)return r(null,n);try{i=be.indexedDB().open(e,be.DB_VERSION)}catch(e){return r(e)}i.onupgradeneeded=function(e){var r,i=e.target.result,n=e.target.transaction;(r=i.objectStoreNames.contains(be.DB_STORE_NAME)?n.objectStore(be.DB_STORE_NAME):i.createObjectStore(be.DB_STORE_NAME)).indexNames.contains("timestamp")||r.createIndex("timestamp","timestamp",{unique:!1})},i.onsuccess=function(){n=i.result,be.dbs[e]=n,r(null,n)},i.onerror=function(e){r(this.error),e.preventDefault()}},getLocalSet:function(e,r){var i={};function n(e){return"."!==e&&".."!==e}function t(r){return function(e){return ge.join2(r,e)}}for(var o=Ee.readdir(e.mountpoint).filter(n).map(t(e.mountpoint));o.length;){var a,f=o.pop();try{a=Ee.stat(f)}catch(e){return r(e)}Ee.isDir(a.mode)&&o.push.apply(o,Ee.readdir(f).filter(n).map(t(f))),i[f]={timestamp:a.mtime}}return r(null,{type:"local",entries:i})},getRemoteSet:function(e,n){var t={};be.getDB(e.mountpoint,function(e,i){if(e)return n(e);var r=i.transaction([be.DB_STORE_NAME],"readonly");r.onerror=function(e){n(this.error),e.preventDefault()},r.objectStore(be.DB_STORE_NAME).index("timestamp").openKeyCursor().onsuccess=function(e){var r=e.target.result;if(!r)return n(null,{type:"remote",db:i,entries:t});t[r.primaryKey]={timestamp:r.key},r.continue()}})},loadLocalEntry:function(e,r){var i,n;try{n=Ee.lookupPath(e).node,i=Ee.stat(e)}catch(e){return r(e)}return Ee.isDir(i.mode)?r(null,{timestamp:i.mtime,mode:i.mode}):Ee.isFile(i.mode)?(n.contents=ve.getFileDataAsTypedArray(n),r(null,{timestamp:i.mtime,mode:i.mode,contents:n.contents})):r(new Error("node type not supported"))},storeLocalEntry:function(e,r,i){try{if(Ee.isDir(r.mode))Ee.mkdir(e,r.mode);else{if(!Ee.isFile(r.mode))return i(new Error("node type not supported"));Ee.writeFile(e,r.contents,{encoding:"binary",canOwn:!0})}Ee.chmod(e,r.mode),Ee.utime(e,r.timestamp,r.timestamp)}catch(e){return i(e)}i(null)},removeLocalEntry:function(e,r){try{Ee.lookupPath(e);var i=Ee.stat(e);Ee.isDir(i.mode)?Ee.rmdir(e):Ee.isFile(i.mode)&&Ee.unlink(e)}catch(e){return r(e)}r(null)},loadRemoteEntry:function(e,r,i){var n=e.get(r);n.onsuccess=function(e){i(null,e.target.result)},n.onerror=function(e){i(this.error),e.preventDefault()}},storeRemoteEntry:function(e,r,i,n){var t=e.put(i,r);t.onsuccess=function(){n(null)},t.onerror=function(e){n(this.error),e.preventDefault()}},removeRemoteEntry:function(e,r,i){var n=e.delete(r);n.onsuccess=function(){i(null)},n.onerror=function(e){i(this.error),e.preventDefault()}},reconcile:function(n,t,r){var o=0,a=[];Object.keys(n.entries).forEach(function(e){var r=n.entries[e],i=t.entries[e];(!i||r.timestamp>i.timestamp)&&(a.push(e),o++)});var i=[];if(Object.keys(t.entries).forEach(function(e){t.entries[e];n.entries[e]||(i.push(e),o++)}),!o)return r(null);var f=0,e=("remote"===n.type?n.db:t.db).transaction([be.DB_STORE_NAME],"readwrite"),s=e.objectStore(be.DB_STORE_NAME);function l(e){return e?l.errored?void 0:(l.errored=!0,r(e)):++f>=o?r(null):void 0}e.onerror=function(e){l(this.error),e.preventDefault()},a.sort().forEach(function(i){"local"===t.type?be.loadRemoteEntry(s,i,function(e,r){if(e)return l(e);be.storeLocalEntry(i,r,l)}):be.loadLocalEntry(i,function(e,r){if(e)return l(e);be.storeRemoteEntry(s,i,r,l)})}),i.sort().reverse().forEach(function(e){"local"===t.type?be.removeLocalEntry(e,l):be.removeRemoteEntry(s,e,l)})}},ke={DIR_MODE:16895,FILE_MODE:33279,reader:null,mount:function(e){w(u),ke.reader||(ke.reader=new FileReaderSync);var o=ke.createNode(null,"/",ke.DIR_MODE,0),a={};function n(e){for(var r=e.split("/"),i=o,n=0;n<r.length-1;n++){var t=r.slice(0,n+1).join("/");a[t]||(a[t]=ke.createNode(i,t,ke.DIR_MODE,0)),i=a[t]}return i}function t(e){var r=e.split("/");return r[r.length-1]}return Array.prototype.forEach.call(e.opts.files||[],function(e){ke.createNode(n(e.name),t(e.name),ke.FILE_MODE,0,e,e.lastModifiedDate)}),(e.opts.blobs||[]).forEach(function(e){ke.createNode(n(e.name),t(e.name),ke.FILE_MODE,0,e.data)}),(e.opts.packages||[]).forEach(function(i){i.metadata.files.forEach(function(e){var r=e.filename.substr(1);ke.createNode(n(r),t(r),ke.FILE_MODE,0,i.blob.slice(e.start,e.end))})}),o},createNode:function(e,r,i,n,t,o){var a=Ee.createNode(e,r,i);return a.mode=i,a.node_ops=ke.node_ops,a.stream_ops=ke.stream_ops,a.timestamp=(o||new Date).getTime(),w(ke.FILE_MODE!==ke.DIR_MODE),i===ke.FILE_MODE?(a.size=t.size,a.contents=t):(a.size=4096,a.contents={}),e&&(e.contents[r]=a),a},node_ops:{getattr:function(e){return{dev:1,ino:void 0,mode:e.mode,nlink:1,uid:0,gid:0,rdev:void 0,size:e.size,atime:new Date(e.timestamp),mtime:new Date(e.timestamp),ctime:new Date(e.timestamp),blksize:4096,blocks:Math.ceil(e.size/4096)}},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp)},lookup:function(e,r){throw new Ee.ErrnoError(we.ENOENT)},mknod:function(e,r,i,n){throw new Ee.ErrnoError(we.EPERM)},rename:function(e,r,i){throw new Ee.ErrnoError(we.EPERM)},unlink:function(e,r){throw new Ee.ErrnoError(we.EPERM)},rmdir:function(e,r){throw new Ee.ErrnoError(we.EPERM)},readdir:function(e){throw new Ee.ErrnoError(we.EPERM)},symlink:function(e,r,i){throw new Ee.ErrnoError(we.EPERM)},readlink:function(e){throw new Ee.ErrnoError(we.EPERM)}},stream_ops:{read:function(e,r,i,n,t){if(t>=e.node.size)return 0;var o=e.node.contents.slice(t,t+n),a=ke.reader.readAsArrayBuffer(o);return r.set(new Uint8Array(a),i),o.size},write:function(e,r,i,n,t){throw new Ee.ErrnoError(we.EIO)},llseek:function(e,r,i){var n=r;if(1===i?n+=e.position:2===i&&Ee.isFile(e.node.mode)&&(n+=e.node.size),n<0)throw new Ee.ErrnoError(we.EINVAL);return n}}},Ee=(c(1,"i32*",b),c(1,"i32*",b),c(1,"i32*",b),{root:null,mounts:[],devices:[null],streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,handleFSError:function(e){if(!(e instanceof Ee.ErrnoError))throw e+" : "+_();return he(e.errno)},lookupPath:function(e,r){if(r=r||{},!(e=ge.resolve(Ee.cwd(),e)))return{path:"",node:null};var i={follow_mount:!0,recurse_count:0};for(var n in i)void 0===r[n]&&(r[n]=i[n]);if(8<r.recurse_count)throw new Ee.ErrnoError(we.ELOOP);for(var t=ge.normalizeArray(e.split("/").filter(function(e){return!!e}),!1),o=Ee.root,a="/",f=0;f<t.length;f++){var s=f===t.length-1;if(s&&r.parent)break;if(o=Ee.lookupNode(o,t[f]),a=ge.join2(a,t[f]),Ee.isMountpoint(o)&&(!s||s&&r.follow_mount)&&(o=o.mounted.root),!s||r.follow)for(var l=0;Ee.isLink(o.mode);){var u=Ee.readlink(a);if(a=ge.resolve(ge.dirname(a),u),o=Ee.lookupPath(a,{recurse_count:r.recurse_count}).node,40<l++)throw new Ee.ErrnoError(we.ELOOP)}}return{path:a,node:o}},getPath:function(e){for(var r;;){if(Ee.isRoot(e)){var i=e.mount.mountpoint;return r?"/"!==i[i.length-1]?i+"/"+r:i+r:i}r=r?e.name+"/"+r:e.name,e=e.parent}},hashName:function(e,r){for(var i=0,n=0;n<r.length;n++)i=(i<<5)-i+r.charCodeAt(n)|0;return(e+i>>>0)%Ee.nameTable.length},hashAddNode:function(e){var r=Ee.hashName(e.parent.id,e.name);e.name_next=Ee.nameTable[r],Ee.nameTable[r]=e},hashRemoveNode:function(e){var r=Ee.hashName(e.parent.id,e.name);if(Ee.nameTable[r]===e)Ee.nameTable[r]=e.name_next;else for(var i=Ee.nameTable[r];i;){if(i.name_next===e){i.name_next=e.name_next;break}i=i.name_next}},lookupNode:function(e,r){var i=Ee.mayLookup(e);if(i)throw new Ee.ErrnoError(i,e);for(var n=Ee.hashName(e.id,r),t=Ee.nameTable[n];t;t=t.name_next){var o=t.name;if(t.parent.id===e.id&&o===r)return t}return Ee.lookup(e,r)},createNode:function(e,r,i,n){if(!Ee.FSNode){Ee.FSNode=function(e,r,i,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=Ee.nextInode++,this.name=r,this.mode=i,this.node_ops={},this.stream_ops={},this.rdev=n},Ee.FSNode.prototype={};Object.defineProperties(Ee.FSNode.prototype,{read:{get:function(){return 365==(365&this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146==(146&this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return Ee.isDir(this.mode)}},isDevice:{get:function(){return Ee.isChrdev(this.mode)}}})}var t=new Ee.FSNode(e,r,i,n);return Ee.hashAddNode(t),t},destroyNode:function(e){Ee.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return 49152==(49152&e)},flagModes:{r:0,rs:1052672,"r+":2,w:577,wx:705,xw:705,"w+":578,"wx+":706,"xw+":706,a:1089,ax:1217,xa:1217,"a+":1090,"ax+":1218,"xa+":1218},modeStringToFlags:function(e){var r=Ee.flagModes[e];if(void 0===r)throw new Error("Unknown file open mode: "+e);return r},flagsToPermissionString:function(e){var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:function(e,r){return Ee.ignorePermissions?0:(-1===r.indexOf("r")||292&e.mode)&&(-1===r.indexOf("w")||146&e.mode)&&(-1===r.indexOf("x")||73&e.mode)?0:we.EACCES},mayLookup:function(e){var r=Ee.nodePermissions(e,"x");return r||(e.node_ops.lookup?0:we.EACCES)},mayCreate:function(e,r){try{Ee.lookupNode(e,r);return we.EEXIST}catch(e){}return Ee.nodePermissions(e,"wx")},mayDelete:function(e,r,i){var n;try{n=Ee.lookupNode(e,r)}catch(e){return e.errno}var t=Ee.nodePermissions(e,"wx");if(t)return t;if(i){if(!Ee.isDir(n.mode))return we.ENOTDIR;if(Ee.isRoot(n)||Ee.getPath(n)===Ee.cwd())return we.EBUSY}else if(Ee.isDir(n.mode))return we.EISDIR;return 0},mayOpen:function(e,r){return e?Ee.isLink(e.mode)?we.ELOOP:Ee.isDir(e.mode)&&(0!=(2097155&r)||512&r)?we.EISDIR:Ee.nodePermissions(e,Ee.flagsToPermissionString(r)):we.ENOENT},MAX_OPEN_FDS:4096,nextfd:function(e,r){e=e||0,r=r||Ee.MAX_OPEN_FDS;for(var i=e;i<=r;i++)if(!Ee.streams[i])return i;throw new Ee.ErrnoError(we.EMFILE)},getStream:function(e){return Ee.streams[e]},createStream:function(e,r,i){Ee.FSStream||(Ee.FSStream=function(){},Ee.FSStream.prototype={},Object.defineProperties(Ee.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}}));var n=new Ee.FSStream;for(var t in e)n[t]=e[t];e=n;var o=Ee.nextfd(r,i);return e.fd=o,Ee.streams[o]=e},closeStream:function(e){Ee.streams[e]=null},chrdev_stream_ops:{open:function(e){var r=Ee.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new Ee.ErrnoError(we.ESPIPE)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,r){return e<<8|r},registerDevice:function(e,r){Ee.devices[e]={stream_ops:r}},getDevice:function(e){return Ee.devices[e]},getMounts:function(e){for(var r=[],i=[e];i.length;){var n=i.pop();r.push(n),i.push.apply(i,n.mounts)}return r},syncfs:function(r,i){"function"==typeof r&&(i=r,r=!1);var n=Ee.getMounts(Ee.root.mount),t=0;function o(e){if(e)return o.errored?void 0:(o.errored=!0,i(e));++t>=n.length&&i(null)}n.forEach(function(e){if(!e.type.syncfs)return o(null);e.type.syncfs(e,r,o)})},mount:function(e,r,i){var n,t="/"===i,o=!i;if(t&&Ee.root)throw new Ee.ErrnoError(we.EBUSY);if(!t&&!o){var a=Ee.lookupPath(i,{follow_mount:!1});if(i=a.path,n=a.node,Ee.isMountpoint(n))throw new Ee.ErrnoError(we.EBUSY);if(!Ee.isDir(n.mode))throw new Ee.ErrnoError(we.ENOTDIR)}var f={type:e,opts:r,mountpoint:i,mounts:[]},s=e.mount(f);return(s.mount=f).root=s,t?Ee.root=s:n&&(n.mounted=f,n.mount&&n.mount.mounts.push(f)),s},unmount:function(e){var r=Ee.lookupPath(e,{follow_mount:!1});if(!Ee.isMountpoint(r.node))throw new Ee.ErrnoError(we.EINVAL);var i=r.node,n=i.mounted,t=Ee.getMounts(n);Object.keys(Ee.nameTable).forEach(function(e){for(var r=Ee.nameTable[e];r;){var i=r.name_next;-1!==t.indexOf(r.mount)&&Ee.destroyNode(r),r=i}}),i.mounted=null;var o=i.mount.mounts.indexOf(n);w(-1!==o),i.mount.mounts.splice(o,1)},lookup:function(e,r){return e.node_ops.lookup(e,r)},mknod:function(e,r,i){var n=Ee.lookupPath(e,{parent:!0}).node,t=ge.basename(e);if(!t||"."===t||".."===t)throw new Ee.ErrnoError(we.EINVAL);var o=Ee.mayCreate(n,t);if(o)throw new Ee.ErrnoError(o);if(!n.node_ops.mknod)throw new Ee.ErrnoError(we.EPERM);return n.node_ops.mknod(n,t,r,i)},create:function(e,r){return r=void 0!==r?r:438,r&=4095,r|=32768,Ee.mknod(e,r,0)},mkdir:function(e,r){return r=void 0!==r?r:511,r&=1023,r|=16384,Ee.mknod(e,r,0)},mkdev:function(e,r,i){return void 0===i&&(i=r,r=438),r|=8192,Ee.mknod(e,r,i)},symlink:function(e,r){if(!ge.resolve(e))throw new Ee.ErrnoError(we.ENOENT);var i=Ee.lookupPath(r,{parent:!0}).node;if(!i)throw new Ee.ErrnoError(we.ENOENT);var n=ge.basename(r),t=Ee.mayCreate(i,n);if(t)throw new Ee.ErrnoError(t);if(!i.node_ops.symlink)throw new Ee.ErrnoError(we.EPERM);return i.node_ops.symlink(i,n,e)},rename:function(r,i){var e,n,t=ge.dirname(r),o=ge.dirname(i),a=ge.basename(r),f=ge.basename(i);try{e=Ee.lookupPath(r,{parent:!0}).node,n=Ee.lookupPath(i,{parent:!0}).node}catch(e){throw new Ee.ErrnoError(we.EBUSY)}if(!e||!n)throw new Ee.ErrnoError(we.ENOENT);if(e.mount!==n.mount)throw new Ee.ErrnoError(we.EXDEV);var s,l=Ee.lookupNode(e,a),u=ge.relative(r,o);if("."!==u.charAt(0))throw new Ee.ErrnoError(we.EINVAL);if("."!==(u=ge.relative(i,t)).charAt(0))throw new Ee.ErrnoError(we.ENOTEMPTY);try{s=Ee.lookupNode(n,f)}catch(e){}if(l!==s){var c=Ee.isDir(l.mode),d=Ee.mayDelete(e,a,c);if(d)throw new Ee.ErrnoError(d);if(d=s?Ee.mayDelete(n,f,c):Ee.mayCreate(n,f))throw new Ee.ErrnoError(d);if(!e.node_ops.rename)throw new Ee.ErrnoError(we.EPERM);if(Ee.isMountpoint(l)||s&&Ee.isMountpoint(s))throw new Ee.ErrnoError(we.EBUSY);if(n!==e&&(d=Ee.nodePermissions(e,"w")))throw new Ee.ErrnoError(d);try{Ee.trackingDelegate.willMovePath&&Ee.trackingDelegate.willMovePath(r,i)}catch(e){console.log("FS.trackingDelegate['willMovePath']('"+r+"', '"+i+"') threw an exception: "+e.message)}Ee.hashRemoveNode(l);try{e.node_ops.rename(l,n,f)}catch(e){throw e}finally{Ee.hashAddNode(l)}try{Ee.trackingDelegate.onMovePath&&Ee.trackingDelegate.onMovePath(r,i)}catch(e){console.log("FS.trackingDelegate['onMovePath']('"+r+"', '"+i+"') threw an exception: "+e.message)}}},rmdir:function(r){var e=Ee.lookupPath(r,{parent:!0}).node,i=ge.basename(r),n=Ee.lookupNode(e,i),t=Ee.mayDelete(e,i,!0);if(t)throw new Ee.ErrnoError(t);if(!e.node_ops.rmdir)throw new Ee.ErrnoError(we.EPERM);if(Ee.isMountpoint(n))throw new Ee.ErrnoError(we.EBUSY);try{Ee.trackingDelegate.willDeletePath&&Ee.trackingDelegate.willDeletePath(r)}catch(e){console.log("FS.trackingDelegate['willDeletePath']('"+r+"') threw an exception: "+e.message)}e.node_ops.rmdir(e,i),Ee.destroyNode(n);try{Ee.trackingDelegate.onDeletePath&&Ee.trackingDelegate.onDeletePath(r)}catch(e){console.log("FS.trackingDelegate['onDeletePath']('"+r+"') threw an exception: "+e.message)}},readdir:function(e){var r=Ee.lookupPath(e,{follow:!0}).node;if(!r.node_ops.readdir)throw new Ee.ErrnoError(we.ENOTDIR);return r.node_ops.readdir(r)},unlink:function(r){var e=Ee.lookupPath(r,{parent:!0}).node,i=ge.basename(r),n=Ee.lookupNode(e,i),t=Ee.mayDelete(e,i,!1);if(t)throw t===we.EISDIR&&(t=we.EPERM),new Ee.ErrnoError(t);if(!e.node_ops.unlink)throw new Ee.ErrnoError(we.EPERM);if(Ee.isMountpoint(n))throw new Ee.ErrnoError(we.EBUSY);try{Ee.trackingDelegate.willDeletePath&&Ee.trackingDelegate.willDeletePath(r)}catch(e){console.log("FS.trackingDelegate['willDeletePath']('"+r+"') threw an exception: "+e.message)}e.node_ops.unlink(e,i),Ee.destroyNode(n);try{Ee.trackingDelegate.onDeletePath&&Ee.trackingDelegate.onDeletePath(r)}catch(e){console.log("FS.trackingDelegate['onDeletePath']('"+r+"') threw an exception: "+e.message)}},readlink:function(e){var r=Ee.lookupPath(e).node;if(!r)throw new Ee.ErrnoError(we.ENOENT);if(!r.node_ops.readlink)throw new Ee.ErrnoError(we.EINVAL);return ge.resolve(Ee.getPath(r.parent),r.node_ops.readlink(r))},stat:function(e,r){var i=Ee.lookupPath(e,{follow:!r}).node;if(!i)throw new Ee.ErrnoError(we.ENOENT);if(!i.node_ops.getattr)throw new Ee.ErrnoError(we.EPERM);return i.node_ops.getattr(i)},lstat:function(e){return Ee.stat(e,!0)},chmod:function(e,r,i){var n;"string"==typeof e?n=Ee.lookupPath(e,{follow:!i}).node:n=e;if(!n.node_ops.setattr)throw new Ee.ErrnoError(we.EPERM);n.node_ops.setattr(n,{mode:4095&r|-4096&n.mode,timestamp:Date.now()})},lchmod:function(e,r){Ee.chmod(e,r,!0)},fchmod:function(e,r){var i=Ee.getStream(e);if(!i)throw new Ee.ErrnoError(we.EBADF);Ee.chmod(i.node,r)},chown:function(e,r,i,n){var t;"string"==typeof e?t=Ee.lookupPath(e,{follow:!n}).node:t=e;if(!t.node_ops.setattr)throw new Ee.ErrnoError(we.EPERM);t.node_ops.setattr(t,{timestamp:Date.now()})},lchown:function(e,r,i){Ee.chown(e,r,i,!0)},fchown:function(e,r,i){var n=Ee.getStream(e);if(!n)throw new Ee.ErrnoError(we.EBADF);Ee.chown(n.node,r,i)},truncate:function(e,r){if(r<0)throw new Ee.ErrnoError(we.EINVAL);var i;"string"==typeof e?i=Ee.lookupPath(e,{follow:!0}).node:i=e;if(!i.node_ops.setattr)throw new Ee.ErrnoError(we.EPERM);if(Ee.isDir(i.mode))throw new Ee.ErrnoError(we.EISDIR);if(!Ee.isFile(i.mode))throw new Ee.ErrnoError(we.EINVAL);var n=Ee.nodePermissions(i,"w");if(n)throw new Ee.ErrnoError(n);i.node_ops.setattr(i,{size:r,timestamp:Date.now()})},ftruncate:function(e,r){var i=Ee.getStream(e);if(!i)throw new Ee.ErrnoError(we.EBADF);if(0==(2097155&i.flags))throw new Ee.ErrnoError(we.EINVAL);Ee.truncate(i.node,r)},utime:function(e,r,i){var n=Ee.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(r,i)})},open:function(r,e,i,n,t){if(""===r)throw new Ee.ErrnoError(we.ENOENT);var o;if(i=void 0===i?438:i,i=64&(e="string"==typeof e?Ee.modeStringToFlags(e):e)?4095&i|32768:0,"object"==typeof r)o=r;else{r=ge.normalize(r);try{o=Ee.lookupPath(r,{follow:!(131072&e)}).node}catch(e){}}var a=!1;if(64&e)if(o){if(128&e)throw new Ee.ErrnoError(we.EEXIST)}else o=Ee.mknod(r,i,0),a=!0;if(!o)throw new Ee.ErrnoError(we.ENOENT);if(Ee.isChrdev(o.mode)&&(e&=-513),65536&e&&!Ee.isDir(o.mode))throw new Ee.ErrnoError(we.ENOTDIR);if(!a){var f=Ee.mayOpen(o,e);if(f)throw new Ee.ErrnoError(f)}512&e&&Ee.truncate(o,0),e&=-641;var s=Ee.createStream({node:o,path:Ee.getPath(o),flags:e,seekable:!0,position:0,stream_ops:o.stream_ops,ungotten:[],error:!1},n,t);s.stream_ops.open&&s.stream_ops.open(s),!p.logReadFiles||1&e||(Ee.readFiles||(Ee.readFiles={}),r in Ee.readFiles||(Ee.readFiles[r]=1,p.printErr("read file: "+r)));try{if(Ee.trackingDelegate.onOpenFile){var l=0;1!=(2097155&e)&&(l|=Ee.tracking.openFlags.READ),0!=(2097155&e)&&(l|=Ee.tracking.openFlags.WRITE),Ee.trackingDelegate.onOpenFile(r,l)}}catch(e){console.log("FS.trackingDelegate['onOpenFile']('"+r+"', flags) threw an exception: "+e.message)}return s},close:function(e){e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{Ee.closeStream(e.fd)}},llseek:function(e,r,i){if(!e.seekable||!e.stream_ops.llseek)throw new Ee.ErrnoError(we.ESPIPE);return e.position=e.stream_ops.llseek(e,r,i),e.ungotten=[],e.position},read:function(e,r,i,n,t){if(n<0||t<0)throw new Ee.ErrnoError(we.EINVAL);if(1==(2097155&e.flags))throw new Ee.ErrnoError(we.EBADF);if(Ee.isDir(e.node.mode))throw new Ee.ErrnoError(we.EISDIR);if(!e.stream_ops.read)throw new Ee.ErrnoError(we.EINVAL);var o=!0;if(void 0===t)t=e.position,o=!1;else if(!e.seekable)throw new Ee.ErrnoError(we.ESPIPE);var a=e.stream_ops.read(e,r,i,n,t);return o||(e.position+=a),a},write:function(e,r,i,n,t,o){if(n<0||t<0)throw new Ee.ErrnoError(we.EINVAL);if(0==(2097155&e.flags))throw new Ee.ErrnoError(we.EBADF);if(Ee.isDir(e.node.mode))throw new Ee.ErrnoError(we.EISDIR);if(!e.stream_ops.write)throw new Ee.ErrnoError(we.EINVAL);1024&e.flags&&Ee.llseek(e,0,2);var a=!0;if(void 0===t)t=e.position,a=!1;else if(!e.seekable)throw new Ee.ErrnoError(we.ESPIPE);var f=e.stream_ops.write(e,r,i,n,t,o);a||(e.position+=f);try{e.path&&Ee.trackingDelegate.onWriteToFile&&Ee.trackingDelegate.onWriteToFile(e.path)}catch(e){console.log("FS.trackingDelegate['onWriteToFile']('"+path+"') threw an exception: "+e.message)}return f},allocate:function(e,r,i){if(r<0||i<=0)throw new Ee.ErrnoError(we.EINVAL);if(0==(2097155&e.flags))throw new Ee.ErrnoError(we.EBADF);if(!Ee.isFile(e.node.mode)&&!Ee.isDir(node.mode))throw new Ee.ErrnoError(we.ENODEV);if(!e.stream_ops.allocate)throw new Ee.ErrnoError(we.EOPNOTSUPP);e.stream_ops.allocate(e,r,i)},mmap:function(e,r,i,n,t,o,a){if(1==(2097155&e.flags))throw new Ee.ErrnoError(we.EACCES);if(!e.stream_ops.mmap)throw new Ee.ErrnoError(we.ENODEV);return e.stream_ops.mmap(e,r,i,n,t,o,a)},msync:function(e,r,i,n,t){return e&&e.stream_ops.msync?e.stream_ops.msync(e,r,i,n,t):0},munmap:function(e){return 0},ioctl:function(e,r,i){if(!e.stream_ops.ioctl)throw new Ee.ErrnoError(we.ENOTTY);return e.stream_ops.ioctl(e,r,i)},readFile:function(e,r){if((r=r||{}).flags=r.flags||"r",r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var i,n=Ee.open(e,r.flags),t=Ee.stat(e).size,o=new Uint8Array(t);return Ee.read(n,o,0,t,0),"utf8"===r.encoding?i=d(o,0):"binary"===r.encoding&&(i=o),Ee.close(n),i},writeFile:function(e,r,i){if((i=i||{}).flags=i.flags||"w",i.encoding=i.encoding||"utf8","utf8"!==i.encoding&&"binary"!==i.encoding)throw new Error('Invalid encoding type "'+i.encoding+'"');var n=Ee.open(e,i.flags,i.mode);if("utf8"===i.encoding){var t=new Uint8Array(g(r)+1),o=h(r,t,0,t.length);Ee.write(n,t,0,o,0,i.canOwn)}else"binary"===i.encoding&&Ee.write(n,r,0,r.length,0,i.canOwn);Ee.close(n)},cwd:function(){return Ee.currentPath},chdir:function(e){var r=Ee.lookupPath(e,{follow:!0});if(!Ee.isDir(r.node.mode))throw new Ee.ErrnoError(we.ENOTDIR);var i=Ee.nodePermissions(r.node,"x");if(i)throw new Ee.ErrnoError(i);Ee.currentPath=r.path},createDefaultDirectories:function(){Ee.mkdir("/tmp"),Ee.mkdir("/home"),Ee.mkdir("/home/<USER>")},createDefaultDevices:function(){var e;if(Ee.mkdir("/dev"),Ee.registerDevice(Ee.makedev(1,3),{read:function(){return 0},write:function(e,r,i,n,t){return n}}),Ee.mkdev("/dev/null",Ee.makedev(1,3)),pe.register(Ee.makedev(5,0),pe.default_tty_ops),pe.register(Ee.makedev(6,0),pe.default_tty1_ops),Ee.mkdev("/dev/tty",Ee.makedev(5,0)),Ee.mkdev("/dev/tty1",Ee.makedev(6,0)),"undefined"!=typeof crypto){var r=new Uint8Array(1);e=function(){return crypto.getRandomValues(r),r[0]}}else e=function(){return 256*Math.random()|0};Ee.createDevice("/dev","random",e),Ee.createDevice("/dev","urandom",e),Ee.mkdir("/dev/shm"),Ee.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){Ee.mkdir("/proc"),Ee.mkdir("/proc/self"),Ee.mkdir("/proc/self/fd"),Ee.mount({mount:function(){var e=Ee.createNode("/proc/self","fd",16895,73);return e.node_ops={lookup:function(e,r){var i=+r,n=Ee.getStream(i);if(!n)throw new Ee.ErrnoError(we.EBADF);var t={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return t.parent=t}},e}},{},"/proc/self/fd")},createStandardStreams:function(){p.stdin?Ee.createDevice("/dev","stdin",p.stdin):Ee.symlink("/dev/tty","/dev/stdin"),p.stdout?Ee.createDevice("/dev","stdout",null,p.stdout):Ee.symlink("/dev/tty","/dev/stdout"),p.stderr?Ee.createDevice("/dev","stderr",null,p.stderr):Ee.symlink("/dev/tty1","/dev/stderr");var e=Ee.open("/dev/stdin","r");w(0===e.fd,"invalid handle for stdin ("+e.fd+")");var r=Ee.open("/dev/stdout","w");w(1===r.fd,"invalid handle for stdout ("+r.fd+")");var i=Ee.open("/dev/stderr","w");w(2===i.fd,"invalid handle for stderr ("+i.fd+")")},ensureErrnoError:function(){Ee.ErrnoError||(Ee.ErrnoError=function(e,r){this.node=r,this.setErrno=function(e){for(var r in this.errno=e,we)if(we[r]===e){this.code=r;break}},this.setErrno(e),this.message=me[e]},Ee.ErrnoError.prototype=new Error,Ee.ErrnoError.prototype.constructor=Ee.ErrnoError,[we.ENOENT].forEach(function(e){Ee.genericErrors[e]=new Ee.ErrnoError(e),Ee.genericErrors[e].stack="<generic error, no stack>"}))},staticInit:function(){Ee.ensureErrnoError(),Ee.nameTable=new Array(4096),Ee.mount(ve,{},"/"),Ee.createDefaultDirectories(),Ee.createDefaultDevices(),Ee.createSpecialDirectories(),Ee.filesystems={MEMFS:ve,IDBFS:be,NODEFS:{},WORKERFS:ke}},init:function(e,r,i){w(!Ee.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),Ee.init.initialized=!0,Ee.ensureErrnoError(),p.stdin=e||p.stdin,p.stdout=r||p.stdout,p.stderr=i||p.stderr,Ee.createStandardStreams()},quit:function(){Ee.init.initialized=!1;var e=p._fflush;e&&e(0);for(var r=0;r<Ee.streams.length;r++){var i=Ee.streams[r];i&&Ee.close(i)}},getMode:function(e,r){var i=0;return e&&(i|=365),r&&(i|=146),i},joinPath:function(e,r){var i=ge.join.apply(null,e);return r&&"/"==i[0]&&(i=i.substr(1)),i},absolutePath:function(e,r){return ge.resolve(r,e)},standardizePath:function(e){return ge.normalize(e)},findObject:function(e,r){var i=Ee.analyzePath(e,r);return i.exists?i.object:(he(i.error),null)},analyzePath:function(e,r){try{e=(n=Ee.lookupPath(e,{follow:!r})).path}catch(e){}var i={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=Ee.lookupPath(e,{parent:!0});i.parentExists=!0,i.parentPath=n.path,i.parentObject=n.node,i.name=ge.basename(e),n=Ee.lookupPath(e,{follow:!r}),i.exists=!0,i.path=n.path,i.object=n.node,i.name=n.node.name,i.isRoot="/"===n.path}catch(e){i.error=e.errno}return i},createFolder:function(e,r,i,n){var t=ge.join2("string"==typeof e?e:Ee.getPath(e),r),o=Ee.getMode(i,n);return Ee.mkdir(t,o)},createPath:function(e,r,i,n){e="string"==typeof e?e:Ee.getPath(e);for(var t=r.split("/").reverse();t.length;){var o=t.pop();if(o){var a=ge.join2(e,o);try{Ee.mkdir(a)}catch(e){}e=a}}return a},createFile:function(e,r,i,n,t){var o=ge.join2("string"==typeof e?e:Ee.getPath(e),r),a=Ee.getMode(n,t);return Ee.create(o,a)},createDataFile:function(e,r,i,n,t,o){var a=r?ge.join2("string"==typeof e?e:Ee.getPath(e),r):e,f=Ee.getMode(n,t),s=Ee.create(a,f);if(i){if("string"==typeof i){for(var l=new Array(i.length),u=0,c=i.length;u<c;++u)l[u]=i.charCodeAt(u);i=l}Ee.chmod(s,146|f);var d=Ee.open(s,"w");Ee.write(d,i,0,i.length,0,o),Ee.close(d),Ee.chmod(s,f)}return s},createDevice:function(e,r,s,a){var i=ge.join2("string"==typeof e?e:Ee.getPath(e),r),n=Ee.getMode(!!s,!!a);Ee.createDevice.major||(Ee.createDevice.major=64);var t=Ee.makedev(Ee.createDevice.major++,0);return Ee.registerDevice(t,{open:function(e){e.seekable=!1},close:function(e){a&&a.buffer&&a.buffer.length&&a(10)},read:function(e,r,i,n,t){for(var o=0,a=0;a<n;a++){var f;try{f=s()}catch(e){throw new Ee.ErrnoError(we.EIO)}if(void 0===f&&0===o)throw new Ee.ErrnoError(we.EAGAIN);if(null==f)break;o++,r[i+a]=f}return o&&(e.node.timestamp=Date.now()),o},write:function(e,r,i,n,t){for(var o=0;o<n;o++)try{a(r[i+o])}catch(e){throw new Ee.ErrnoError(we.EIO)}return n&&(e.node.timestamp=Date.now()),o}}),Ee.mkdev(i,n,t)},createLink:function(e,r,i,n,t){var o=ge.join2("string"==typeof e?e:Ee.getPath(e),r);return Ee.symlink(i,o)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;var r=!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!p.read)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=$(p.read(e.url),!0),e.usedBytes=e.contents.length}catch(e){r=!1}return r||he(we.EIO),r},createLazyFile:function(e,r,a,i,n){function t(){this.lengthKnown=!1,this.chunks=[]}if(t.prototype.get=function(e){if(!(e>this.length-1||e<0)){var r=e%this.chunkSize,i=e/this.chunkSize|0;return this.getter(i)[r]}},t.prototype.setDataGetter=function(e){this.getter=e},t.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",a,!1),e.send(null),!(200<=e.status&&e.status<300||304===e.status))throw new Error("Couldn't load "+a+". Status: "+e.status);var r,n=Number(e.getResponseHeader("Content-length")),i=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,t=1048576;i||(t=n);var o=this;o.setDataGetter(function(e){var r=e*t,i=(e+1)*t-1;if(i=Math.min(i,n-1),void 0===o.chunks[e]&&(o.chunks[e]=function(e,r){if(r<e)throw new Error("invalid range ("+e+", "+r+") or no bytes requested!");if(n-1<r)throw new Error("only "+n+" bytes available! programmer error!");var i=new XMLHttpRequest;if(i.open("GET",a,!1),n!==t&&i.setRequestHeader("Range","bytes="+e+"-"+r),"undefined"!=typeof Uint8Array&&(i.responseType="arraybuffer"),i.overrideMimeType&&i.overrideMimeType("text/plain; charset=x-user-defined"),i.send(null),!(200<=i.status&&i.status<300||304===i.status))throw new Error("Couldn't load "+a+". Status: "+i.status);return void 0!==i.response?new Uint8Array(i.response||[]):$(i.responseText||"",!0)}(r,i)),void 0===o.chunks[e])throw new Error("doXHR failed!");return o.chunks[e]}),this._length=n,this._chunkSize=t,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!u)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var o=new t;Object.defineProperty(o,"length",{get:function(){return this.lengthKnown||this.cacheLength(),this._length}}),Object.defineProperty(o,"chunkSize",{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}});var f={isDevice:!1,contents:o}}else f={isDevice:!1,url:a};var s=Ee.createFile(e,r,f,i,n);f.contents?s.contents=f.contents:f.url&&(s.contents=null,s.url=f.url),Object.defineProperty(s,"usedBytes",{get:function(){return this.contents.length}});var l={};return Object.keys(s.stream_ops).forEach(function(e){var r=s.stream_ops[e];l[e]=function(){if(!Ee.forceLoadFile(s))throw new Ee.ErrnoError(we.EIO);return r.apply(null,arguments)}}),l.read=function(e,r,i,n,t){if(!Ee.forceLoadFile(s))throw new Ee.ErrnoError(we.EIO);var o=e.node.contents;if(t>=o.length)return 0;var a=Math.min(o.length-t,n);if(w(0<=a),o.slice)for(var f=0;f<a;f++)r[i+f]=o[t+f];else for(f=0;f<a;f++)r[i+f]=o.get(t+f);return a},s.stream_ops=l,s},createPreloadedFile:function(t,o,e,a,f,s,l,u,c,d){Ae.init();var h=o?ge.resolve(ge.join2(t,o)):t;function r(r){function i(e){d&&d(),u||Ee.createDataFile(t,o,e,a,f,c),s&&s(),ue()}var n=!1;p.preloadPlugins.forEach(function(e){n||e.canHandle(h)&&(e.handle(r,h,i,function(){l&&l(),ue()}),n=!0)}),n||i(r)}le(),"string"==typeof e?Ae.asyncLoad(e,function(e){r(e)},l):r(e)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(r,f,s){f=f||function(){},s=s||function(){};var e=Ee.indexedDB();try{var l=e.open(Ee.DB_NAME(),Ee.DB_VERSION)}catch(e){return s(e)}l.onupgradeneeded=function(){console.log("creating db"),l.result.createObjectStore(Ee.DB_STORE_NAME)},l.onsuccess=function(){var e=l.result.transaction([Ee.DB_STORE_NAME],"readwrite"),i=e.objectStore(Ee.DB_STORE_NAME),n=0,t=0,o=r.length;function a(){0==t?f():s()}r.forEach(function(e){var r=i.put(Ee.analyzePath(e).object.contents,e);r.onsuccess=function(){++n+t==o&&a()},r.onerror=function(){n+ ++t==o&&a()}}),e.onerror=s},l.onerror=s},loadFilesFromDB:function(f,s,l){s=s||function(){},l=l||function(){};var e=Ee.indexedDB();try{var u=e.open(Ee.DB_NAME(),Ee.DB_VERSION)}catch(e){return l(e)}u.onupgradeneeded=l,u.onsuccess=function(){var e=u.result;try{var r=e.transaction([Ee.DB_STORE_NAME],"readonly")}catch(e){return void l(e)}var i=r.objectStore(Ee.DB_STORE_NAME),n=0,t=0,o=f.length;function a(){0==t?s():l()}f.forEach(function(e){var r=i.get(e);r.onsuccess=function(){Ee.analyzePath(e).exists&&Ee.unlink(e),Ee.createDataFile(ge.dirname(e),ge.basename(e),r.result,!0,!0,!0),++n+t==o&&a()},r.onerror=function(){n+ ++t==o&&a()}}),r.onerror=l},u.onerror=l}}),ge={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,r){for(var i=0,n=e.length-1;0<=n;n--){var t=e[n];"."===t?e.splice(n,1):".."===t?(e.splice(n,1),i++):i&&(e.splice(n,1),i--)}if(r)for(;i--;i)e.unshift("..");return e},normalize:function(e){var r="/"===e.charAt(0),i="/"===e.substr(-1);return(e=ge.normalizeArray(e.split("/").filter(function(e){return!!e}),!r).join("/"))||r||(e="."),e&&i&&(e+="/"),(r?"/":"")+e},dirname:function(e){var r=ge.splitPath(e),i=r[0],n=r[1];return i||n?(n&&(n=n.substr(0,n.length-1)),i+n):"."},basename:function(e){if("/"===e)return"/";var r=e.lastIndexOf("/");return-1===r?e:e.substr(r+1)},extname:function(e){return ge.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return ge.normalize(e.join("/"))},join2:function(e,r){return ge.normalize(e+"/"+r)},resolve:function(){for(var e="",r=!1,i=arguments.length-1;-1<=i&&!r;i--){var n=0<=i?arguments[i]:Ee.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,r="/"===n.charAt(0)}return(r?"/":"")+(e=ge.normalizeArray(e.split("/").filter(function(e){return!!e}),!r).join("/"))||"."},relative:function(e,r){function i(e){for(var r=0;r<e.length&&""===e[r];r++);for(var i=e.length-1;0<=i&&""===e[i];i--);return i<r?[]:e.slice(r,i-r+1)}e=ge.resolve(e).substr(1),r=ge.resolve(r).substr(1);for(var n=i(e.split("/")),t=i(r.split("/")),o=Math.min(n.length,t.length),a=o,f=0;f<o;f++)if(n[f]!==t[f]){a=f;break}var s=[];for(f=a;f<n.length;f++)s.push("..");return(s=s.concat(t.slice(a))).join("/")}};function ye(e,r){if(Ae.mainLoop.timingMode=e,Ae.mainLoop.timingValue=r,!Ae.mainLoop.func)return 1;if(0==e)Ae.mainLoop.scheduler=function(){setTimeout(Ae.mainLoop.runner,r)},Ae.mainLoop.method="timeout";else if(1==e)Ae.mainLoop.scheduler=function(){Ae.requestAnimationFrame(Ae.mainLoop.runner)},Ae.mainLoop.method="rAF";else if(2==e){if(!window.setImmediate){var i=[];window.addEventListener("message",function(e){e.source===window&&"__emcc"===e.data&&(e.stopPropagation(),i.shift()())},!0),window.setImmediate=function(e){i.push(e),window.postMessage("__emcc","*")}}Ae.mainLoop.scheduler=function(){window.setImmediate(Ae.mainLoop.runner)},Ae.mainLoop.method="immediate"}return 0}function _e(t,e,r,o,i){p.noExitRuntime=!0,w(!Ae.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),Ae.mainLoop.func=t,Ae.mainLoop.arg=o;var a=Ae.mainLoop.currentlyRunningMainloop;if(Ae.mainLoop.runner=function(){if(!s){if(0<Ae.mainLoop.queue.length){var e=Date.now(),r=Ae.mainLoop.queue.shift();if(r.func(r.arg),Ae.mainLoop.remainingBlockers){var i=Ae.mainLoop.remainingBlockers,n=i%1==0?i-1:Math.floor(i);r.counted?Ae.mainLoop.remainingBlockers=n:(n+=.5,Ae.mainLoop.remainingBlockers=(8*i+n)/9)}return console.log('main loop blocker "'+r.name+'" took '+(Date.now()-e)+" ms"),Ae.mainLoop.updateStatus(),void setTimeout(Ae.mainLoop.runner,0)}a<Ae.mainLoop.currentlyRunningMainloop||(Ae.mainLoop.currentFrameNumber=Ae.mainLoop.currentFrameNumber+1|0,1==Ae.mainLoop.timingMode&&1<Ae.mainLoop.timingValue&&Ae.mainLoop.currentFrameNumber%Ae.mainLoop.timingValue!=0?Ae.mainLoop.scheduler():("timeout"===Ae.mainLoop.method&&p.ctx&&(p.printErr("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),Ae.mainLoop.method=""),Ae.mainLoop.runIter(function(){void 0!==o?v.dynCall("vi",t,[o]):v.dynCall("v",t)}),a<Ae.mainLoop.currentlyRunningMainloop||("object"==typeof SDL&&SDL.audio&&SDL.audio.queueNewAudioData&&SDL.audio.queueNewAudioData(),Ae.mainLoop.scheduler())))}},i||(e&&0<e?ye(0,1e3/e):ye(1,1),Ae.mainLoop.scheduler()),r)throw"SimulateInfiniteLoop"}var Ae={mainLoop:{scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){Ae.mainLoop.scheduler=null,Ae.mainLoop.currentlyRunningMainloop++},resume:function(){Ae.mainLoop.currentlyRunningMainloop++;var e=Ae.mainLoop.timingMode,r=Ae.mainLoop.timingValue,i=Ae.mainLoop.func;Ae.mainLoop.func=null,_e(i,0,!1,Ae.mainLoop.arg,!0),ye(e,r),Ae.mainLoop.scheduler()},updateStatus:function(){if(p.setStatus){var e=p.statusMessage||"Please wait...",r=Ae.mainLoop.remainingBlockers,i=Ae.mainLoop.expectedBlockers;r?r<i?p.setStatus(e+" ("+(i-r)+"/"+i+")"):p.setStatus(e):p.setStatus("")}},runIter:function(e){if(!s){if(p.preMainLoop)if(!1===p.preMainLoop())return;try{e()}catch(e){if(e instanceof Fe)return;throw e&&"object"==typeof e&&e.stack&&p.printErr("exception thrown: "+[e,e.stack]),e}p.postMainLoop&&p.postMainLoop()}}},isFullScreen:!1,pointerLock:!1,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(p.preloadPlugins||(p.preloadPlugins=[]),!Ae.initted){Ae.initted=!0;try{new Blob,Ae.hasBlobConstructor=!0}catch(e){Ae.hasBlobConstructor=!1,console.log("warning: no blob constructor, cannot create blobs with mimetypes")}Ae.BlobBuilder="undefined"!=typeof MozBlobBuilder?MozBlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:Ae.hasBlobConstructor?null:console.log("warning: no BlobBuilder"),Ae.URLObject="undefined"!=typeof window?window.URL?window.URL:window.webkitURL:void 0,p.noImageDecoding||void 0!==Ae.URLObject||(console.log("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),p.noImageDecoding=!0);var e={canHandle:function(e){return!p.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(e)},handle:function(r,i,n,t){var e=null;if(Ae.hasBlobConstructor)try{(e=new Blob([r],{type:Ae.getMimetype(i)})).size!==r.length&&(e=new Blob([new Uint8Array(r).buffer],{type:Ae.getMimetype(i)}))}catch(e){v.warnOnce("Blob constructor present but fails: "+e+"; falling back to blob builder")}if(!e){var o=new Ae.BlobBuilder;o.append(new Uint8Array(r).buffer),e=o.getBlob()}var a=Ae.URLObject.createObjectURL(e),f=new Image;f.onload=function(){w(f.complete,"Image "+i+" could not be decoded");var e=document.createElement("canvas");e.width=f.width,e.height=f.height,e.getContext("2d").drawImage(f,0,0),p.preloadedImages[i]=e,Ae.URLObject.revokeObjectURL(a),n&&n(r)},f.onerror=function(e){console.log("Image "+a+" could not be decoded"),t&&t()},f.src=a}};p.preloadPlugins.push(e);var r={canHandle:function(e){return!p.noAudioDecoding&&e.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(r,i,n,e){var t=!1;function o(e){t||(t=!0,p.preloadedAudios[i]=e,n&&n(r))}function a(){t||(t=!0,p.preloadedAudios[i]=new Audio,e&&e())}if(!Ae.hasBlobConstructor)return a();try{var f=new Blob([r],{type:Ae.getMimetype(i)})}catch(e){return a()}var s=Ae.URLObject.createObjectURL(f),l=new Audio;l.addEventListener("canplaythrough",function(){o(l)},!1),l.onerror=function(e){t||(console.log("warning: browser could not fully decode audio "+i+", trying slower base64 approach"),l.src="data:audio/x-"+i.substr(-3)+";base64,"+function(e){for(var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i="",n=0,t=0,o=0;o<e.length;o++)for(n=n<<8|e[o],t+=8;6<=t;){var a=n>>t-6&63;t-=6,i+=r[a]}return 2==t?(i+=r[(3&n)<<4],i+="=="):4==t&&(i+=r[(15&n)<<2],i+="="),i}(r),o(l))},l.src=s,Ae.safeSetTimeout(function(){o(l)},1e4)}};p.preloadPlugins.push(r);var i=p.canvas;i&&(i.requestPointerLock=i.requestPointerLock||i.mozRequestPointerLock||i.webkitRequestPointerLock||i.msRequestPointerLock||function(){},i.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||function(){},i.exitPointerLock=i.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",n,!1),document.addEventListener("mozpointerlockchange",n,!1),document.addEventListener("webkitpointerlockchange",n,!1),document.addEventListener("mspointerlockchange",n,!1),p.elementPointerLock&&i.addEventListener("click",function(e){!Ae.pointerLock&&i.requestPointerLock&&(i.requestPointerLock(),e.preventDefault())},!1))}function n(){Ae.pointerLock=document.pointerLockElement===i||document.mozPointerLockElement===i||document.webkitPointerLockElement===i||document.msPointerLockElement===i}},createContext:function(e,r,i,n){if(r&&p.ctx&&e==p.canvas)return p.ctx;var t,o;if(r){var a={antialias:!1,alpha:!1};if(n)for(var f in n)a[f]=n[f];(o=GL.createContext(e,a))&&(t=GL.getContext(o).GLctx),e.style.backgroundColor="black"}else t=e.getContext("2d");return t?(i&&(r||w("undefined"==typeof GLctx,"cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),p.ctx=t,r&&GL.makeContextCurrent(o),p.useWebGL=r,Ae.moduleContextCreatedCallbacks.forEach(function(e){e()}),Ae.init()),t):null},destroyContext:function(e,r,i){},fullScreenHandlersInstalled:!1,lockPointer:void 0,resizeCanvas:void 0,requestFullScreen:function(e,r,i){Ae.lockPointer=e,Ae.resizeCanvas=r,Ae.vrDevice=i,void 0===Ae.lockPointer&&(Ae.lockPointer=!0),void 0===Ae.resizeCanvas&&(Ae.resizeCanvas=!1),void 0===Ae.vrDevice&&(Ae.vrDevice=null);var n=p.canvas;function t(){Ae.isFullScreen=!1;var e=n.parentNode;(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===e?(n.cancelFullScreen=document.cancelFullScreen||document.mozCancelFullScreen||document.webkitCancelFullScreen||document.msExitFullscreen||document.exitFullscreen||function(){},n.cancelFullScreen=n.cancelFullScreen.bind(document),Ae.lockPointer&&n.requestPointerLock(),Ae.isFullScreen=!0,Ae.resizeCanvas&&Ae.setFullScreenCanvasSize()):(e.parentNode.insertBefore(n,e),e.parentNode.removeChild(e),Ae.resizeCanvas&&Ae.setWindowedCanvasSize()),p.onFullScreen&&p.onFullScreen(Ae.isFullScreen),Ae.updateCanvasDimensions(n)}Ae.fullScreenHandlersInstalled||(Ae.fullScreenHandlersInstalled=!0,document.addEventListener("fullscreenchange",t,!1),document.addEventListener("mozfullscreenchange",t,!1),document.addEventListener("webkitfullscreenchange",t,!1),document.addEventListener("MSFullscreenChange",t,!1));var o=document.createElement("div");n.parentNode.insertBefore(o,n),o.appendChild(n),o.requestFullScreen=o.requestFullScreen||o.mozRequestFullScreen||o.msRequestFullscreen||(o.webkitRequestFullScreen?function(){o.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null),i?o.requestFullScreen({vrDisplay:i}):o.requestFullScreen()},nextRAF:0,fakeRequestAnimationFrame:function(e){var r=Date.now();if(0===Ae.nextRAF)Ae.nextRAF=r+1e3/60;else for(;r+2>=Ae.nextRAF;)Ae.nextRAF+=1e3/60;var i=Math.max(Ae.nextRAF-r,0);setTimeout(e,i)},requestAnimationFrame:function(e){"undefined"==typeof window?Ae.fakeRequestAnimationFrame(e):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||Ae.fakeRequestAnimationFrame),window.requestAnimationFrame(e))},safeCallback:function(e){return function(){if(!s)return e.apply(null,arguments)}},allowAsyncCallbacks:!0,queuedAsyncCallbacks:[],pauseAsyncCallbacks:function(){Ae.allowAsyncCallbacks=!1},resumeAsyncCallbacks:function(){if(Ae.allowAsyncCallbacks=!0,0<Ae.queuedAsyncCallbacks.length){var e=Ae.queuedAsyncCallbacks;Ae.queuedAsyncCallbacks=[],e.forEach(function(e){e()})}},safeRequestAnimationFrame:function(e){return Ae.requestAnimationFrame(function(){s||(Ae.allowAsyncCallbacks?e():Ae.queuedAsyncCallbacks.push(e))})},safeSetTimeout:function(e,r){return p.noExitRuntime=!0,setTimeout(function(){s||(Ae.allowAsyncCallbacks?e():Ae.queuedAsyncCallbacks.push(e))},r)},safeSetInterval:function(e,r){return p.noExitRuntime=!0,setInterval(function(){s||Ae.allowAsyncCallbacks&&e()},r)},getMimetype:function(e){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[e.substr(e.lastIndexOf(".")+1)]},getUserMedia:function(e){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia),window.getUserMedia(e)},getMovementX:function(e){return e.movementX||e.mozMovementX||e.webkitMovementX||0},getMovementY:function(e){return e.movementY||e.mozMovementY||e.webkitMovementY||0},getMouseWheelDelta:function(e){var r=0;switch(e.type){case"DOMMouseScroll":r=e.detail;break;case"mousewheel":r=e.wheelDelta;break;case"wheel":r=e.deltaY;break;default:throw"unrecognized mouse wheel event: "+e.type}return r},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(e){if(Ae.pointerLock)"mousemove"!=e.type&&"mozMovementX"in e?Ae.mouseMovementX=Ae.mouseMovementY=0:(Ae.mouseMovementX=Ae.getMovementX(e),Ae.mouseMovementY=Ae.getMovementY(e)),"undefined"!=typeof SDL?(Ae.mouseX=SDL.mouseX+Ae.mouseMovementX,Ae.mouseY=SDL.mouseY+Ae.mouseMovementY):(Ae.mouseX+=Ae.mouseMovementX,Ae.mouseY+=Ae.mouseMovementY);else{var r=p.canvas.getBoundingClientRect(),i=p.canvas.width,n=p.canvas.height,t=void 0!==window.scrollX?window.scrollX:window.pageXOffset,o=void 0!==window.scrollY?window.scrollY:window.pageYOffset;if("touchstart"===e.type||"touchend"===e.type||"touchmove"===e.type){var a=e.touch;if(void 0===a)return;var f=a.pageX-(t+r.left),s=a.pageY-(o+r.top),l={x:f*=i/r.width,y:s*=n/r.height};if("touchstart"===e.type)Ae.lastTouches[a.identifier]=l,Ae.touches[a.identifier]=l;else if("touchend"===e.type||"touchmove"===e.type){var u=Ae.touches[a.identifier];u||(u=l),Ae.lastTouches[a.identifier]=u,Ae.touches[a.identifier]=l}return}var c=e.pageX-(t+r.left),d=e.pageY-(o+r.top);c*=i/r.width,d*=n/r.height,Ae.mouseMovementX=c-Ae.mouseX,Ae.mouseMovementY=d-Ae.mouseY,Ae.mouseX=c,Ae.mouseY=d}},xhrLoad:function(e,r,i){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):i()},n.onerror=i,n.send(null)},asyncLoad:function(r,i,n,t){Ae.xhrLoad(r,function(e){w(e,'Loading data file "'+r+'" failed (no arrayBuffer).'),i(new Uint8Array(e)),t||ue()},function(e){if(!n)throw'Loading data file "'+r+'" failed.';n()}),t||le()},resizeListeners:[],updateResizeListeners:function(){var r=p.canvas;Ae.resizeListeners.forEach(function(e){e(r.width,r.height)})},setCanvasSize:function(e,r,i){var n=p.canvas;Ae.updateCanvasDimensions(n,e,r),i||Ae.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullScreenCanvasSize:function(){if("undefined"!=typeof SDL){var e=N[SDL.screen+0*v.QUANTUM_SIZE>>2];e|=8388608,M[SDL.screen+0*v.QUANTUM_SIZE>>2]=e}Ae.updateResizeListeners()},setWindowedCanvasSize:function(){if("undefined"!=typeof SDL){var e=N[SDL.screen+0*v.QUANTUM_SIZE>>2];e&=-8388609,M[SDL.screen+0*v.QUANTUM_SIZE>>2]=e}Ae.updateResizeListeners()},updateCanvasDimensions:function(e,r,i){r&&i?(e.widthNative=r,e.heightNative=i):(r=e.widthNative,i=e.heightNative);var n=r,t=i;if(p.forcedAspectRatio&&0<p.forcedAspectRatio&&(n/t<p.forcedAspectRatio?n=Math.round(t*p.forcedAspectRatio):t=Math.round(n/p.forcedAspectRatio)),(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===e.parentNode&&"undefined"!=typeof screen){var o=Math.min(screen.width/n,screen.height/t);n=Math.round(n*o),t=Math.round(t*o)}Ae.resizeCanvas?(e.width!=n&&(e.width=n),e.height!=t&&(e.height=t),void 0!==e.style&&(e.style.removeProperty("width"),e.style.removeProperty("height"))):(e.width!=r&&(e.width=r),e.height!=i&&(e.height=i),void 0!==e.style&&(n!=r||t!=i?(e.style.setProperty("width",n+"px","important"),e.style.setProperty("height",t+"px","important")):(e.style.removeProperty("width"),e.style.removeProperty("height"))))},wgetRequests:{},nextWgetRequestHandle:0,getNextWgetRequestHandle:function(){var e=Ae.nextWgetRequestHandle;return Ae.nextWgetRequestHandle++,e}};p.requestFullScreen=function(e,r,i){Ae.requestFullScreen(e,r,i)},p.requestAnimationFrame=function(e){Ae.requestAnimationFrame(e)},p.setCanvasSize=function(e,r,i){Ae.setCanvasSize(e,r,i)},p.pauseMainLoop=function(){Ae.mainLoop.pause()},p.resumeMainLoop=function(){Ae.mainLoop.resume()},p.getUserMedia=function(){Ae.getUserMedia()},p.createContext=function(e,r,i,n){return Ae.createContext(e,r,i,n)},Ee.staticInit(),X.unshift(function(){p.noFSInit||Ee.init.initialized||Ee.init()}),V.push(function(){Ee.ignorePermissions=!1}),G.push(function(){Ee.quit()}),p.FS_createFolder=Ee.createFolder,p.FS_createPath=Ee.createPath,p.FS_createDataFile=Ee.createDataFile,p.FS_createPreloadedFile=Ee.createPreloadedFile,p.FS_createLazyFile=Ee.createLazyFile,p.FS_createLink=Ee.createLink,p.FS_createDevice=Ee.createDevice,p.FS_unlink=Ee.unlink,X.unshift(function(){pe.init()}),G.push(function(){pe.shutdown()}),I=B=v.alignMemory(P),C=!0,T=I+z,w((x=v.alignMemory(T))<j,"TOTAL_MEMORY not big enough for stack"),p.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0},p.asmLibraryArg={abort:Be,assert:w,_sysconf:function(e){switch(e){case 30:return F;case 85:return q/F;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"==typeof navigator&&navigator.hardwareConcurrency||1}return he(we.EINVAL),-1},_pthread_self:function(){return 0},_abort:function(){p.abort()},___setErrNo:he,_sbrk:de,_time:function(e){var r=Date.now()/1e3|0;return e&&(M[e>>2]=r),r},_emscripten_set_main_loop_timing:ye,_emscripten_memcpy_big:function(e,r,i){return D.set(D.subarray(r,r+i),e),e},_emscripten_set_main_loop:_e,STACKTOP:B,STACK_MAX:T,tempDoublePtr:ce,ABORT:s};var De,Se=function(e,r,i){"use asm";var Xe=new e.Int8Array(i);var Ve=new e.Int16Array(i);var Ge=new e.Int32Array(i);var s=new e.Uint8Array(i);var We=new e.Uint16Array(i);var n=new e.Uint32Array(i);var t=new e.Float32Array(i);var o=new e.Float64Array(i);var Ke=r.STACKTOP|0;var a=r.STACK_MAX|0;var f=r.tempDoublePtr|0;var l=r.ABORT|0;var u=0;var c=0;var d=0;var h=0;var w=e.NaN,m=e.Infinity;var p=0,v=0,b=0,k=0,E=0.0,g=0,y=0,_=0,A=0.0;var D=0;var S=0;var R=0;var M=0;var N=0;var O=0;var L=0;var F=0;var I=0;var T=0;var P=e.Math.floor;var C=e.Math.abs;var B=e.Math.sqrt;var x=e.Math.pow;var U=e.Math.cos;var z=e.Math.sin;var j=e.Math.tan;var q=e.Math.acos;var H=e.Math.asin;var Y=e.Math.atan;var X=e.Math.atan2;var V=e.Math.exp;var G=e.Math.log;var W=e.Math.ceil;var Ze=e.Math.imul;var K=e.Math.min;var Z=e.Math.clz32;var Q=r.abort;var J=r.assert;var $=r._sysconf;var ee=r._pthread_self;var re=r._abort;var ie=r.___setErrNo;var ne=r._sbrk;var te=r._time;var oe=r._emscripten_set_main_loop_timing;var ae=r._emscripten_memcpy_big;var fe=r._emscripten_set_main_loop;var se=0.0;function le(e){e=e|0;var r=0;r=Ke;Ke=Ke+e|0;Ke=Ke+15&-16;return r|0}function ue(){return Ke|0}function ce(e){e=e|0;Ke=e}function de(e,r){e=e|0;r=r|0;Ke=e;a=r}function he(e,r){e=e|0;r=r|0;if(!u){u=e;c=r}}function we(e){e=e|0;Xe[f>>0]=Xe[e>>0];Xe[f+1>>0]=Xe[e+1>>0];Xe[f+2>>0]=Xe[e+2>>0];Xe[f+3>>0]=Xe[e+3>>0]}function me(e){e=e|0;Xe[f>>0]=Xe[e>>0];Xe[f+1>>0]=Xe[e+1>>0];Xe[f+2>>0]=Xe[e+2>>0];Xe[f+3>>0]=Xe[e+3>>0];Xe[f+4>>0]=Xe[e+4>>0];Xe[f+5>>0]=Xe[e+5>>0];Xe[f+6>>0]=Xe[e+6>>0];Xe[f+7>>0]=Xe[e+7>>0]}function pe(e){e=e|0;D=e}function ve(){return D|0}function be(){var e=0,r=0;r=Ke;Ke=Ke+16|0;e=r;Ge[e>>2]=0;Fr(e,31756)|0;Ke=r;return Ge[e>>2]|0}function ke(e){e=e|0;var r=0,i=0;r=Ke;Ke=Ke+16|0;i=r;Ge[i>>2]=e;Ir(i);Ke=r;return}function Ee(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;Se(e,(n|0)==0?(s[r>>0]|0)>>>3&15:15,r+1|0,i,2)|0;return}function ge(e){e=e|0;var r=0;r=Ut(8)|0;Cr(r,r+4|0,e)|0;return r|0}function ye(e){e=e|0;Br(e,e+4|0);zt(e);return}function _e(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0;t=Ke;Ke=Ke+16|0;o=t;Ge[o>>2]=r;i=(xr(Ge[e>>2]|0,Ge[e+4>>2]|0,r,i,n,o,3)|0)<<16>>16;Xe[n>>0]=s[n>>0]|0|4;Ke=t;return i|0}function Ae(e){e=e|0;if(!e)e=-1;else{Ve[e>>1]=4096;e=0}return e|0}function De(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0;u=Ge[o>>2]|0;w=t<<16>>16>0;if(w){a=0;f=0;do{l=Ve[i+(a<<1)>>1]|0;l=Ze(l,l)|0;if((l|0)!=1073741824){s=(l<<1)+f|0;if((l^f|0)>0&(s^f|0)<0){Ge[o>>2]=1;f=(f>>>31)+2147483647|0}else f=s}else{Ge[o>>2]=1;f=2147483647}a=a+1|0}while((a&65535)<<16>>16!=t<<16>>16);if((f|0)==2147483647){Ge[o>>2]=u;l=0;s=0;do{f=Ve[i+(l<<1)>>1]>>2;f=Ze(f,f)|0;if((f|0)!=1073741824){a=(f<<1)+s|0;if((f^s|0)>0&(a^s|0)<0){Ge[o>>2]=1;s=(s>>>31)+2147483647|0}else s=a}else{Ge[o>>2]=1;s=2147483647}l=l+1|0}while((l&65535)<<16>>16!=t<<16>>16)}else h=8}else{f=0;h=8}if((h|0)==8)s=f>>4;if(!s){Ve[e>>1]=0;return}d=((kt(s)|0)&65535)+65535|0;f=d<<16>>16;if((d&65535)<<16>>16>0){a=s<<f;if((a>>f|0)==(s|0))s=a;else s=s>>31^2147483647}else{f=0-f<<16;if((f|0)<2031616)s=s>>(f>>16);else s=0}c=Ft(s,o)|0;a=Ge[o>>2]|0;if(w){f=0;s=0;do{u=Ve[r+(f<<1)>>1]|0;u=Ze(u,u)|0;if((u|0)!=1073741824){l=(u<<1)+s|0;if((u^s|0)>0&(l^s|0)<0){Ge[o>>2]=1;s=(s>>>31)+2147483647|0}else s=l}else{Ge[o>>2]=1;s=2147483647}f=f+1|0}while((f&65535)<<16>>16!=t<<16>>16);if((s|0)==2147483647){Ge[o>>2]=a;u=0;s=0;do{l=Ve[r+(u<<1)>>1]>>2;l=Ze(l,l)|0;if((l|0)!=1073741824){f=(l<<1)+s|0;if((l^s|0)>0&(f^s|0)<0){Ge[o>>2]=1;s=(s>>>31)+2147483647|0}else s=f}else{Ge[o>>2]=1;s=2147483647}u=u+1|0}while((u&65535)<<16>>16!=t<<16>>16)}else h=29}else{s=0;h=29}if((h|0)==29)s=s>>4;if(!s)l=0;else{f=(kt(s)|0)<<16>>16;a=d-f|0;l=a&65535;s=(Kn(c,Ft(s<<f,o)|0)|0)<<16>>16;f=s<<7;a=a<<16>>16;if(l<<16>>16>0)a=l<<16>>16<31?f>>a:0;else{h=0-a<<16>>16;a=f<<h;a=(a>>h|0)==(f|0)?a:s>>24^2147483647}l=(Ze(((at(a,o)|0)<<9)+32768>>16,32767-(n&65535)<<16>>16)|0)>>>15<<16>>16}a=Ve[e>>1]|0;if(w){s=n<<16>>16;f=0;while(1){n=((Ze(a<<16>>16,s)|0)>>>15&65535)+l|0;a=n&65535;Ve[i>>1]=(Ze(Ve[i>>1]|0,n<<16>>16)|0)>>>12;f=f+1<<16>>16;if(f<<16>>16>=t<<16>>16)break;else i=i+2|0}}Ve[e>>1]=a;return}function Qe(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0;a=Ge[n>>2]|0;t=i<<16>>16>0;if(t){f=0;o=0;do{l=Ve[r+(f<<1)>>1]|0;l=Ze(l,l)|0;if((l|0)!=1073741824){s=(l<<1)+o|0;if((l^o|0)>0&(s^o|0)<0){Ge[n>>2]=1;o=(o>>>31)+2147483647|0}else o=s}else{Ge[n>>2]=1;o=2147483647}f=f+1|0}while((f&65535)<<16>>16!=i<<16>>16);if((o|0)==2147483647){Ge[n>>2]=a;l=0;a=0;do{s=Ve[r+(l<<1)>>1]>>2;s=Ze(s,s)|0;if((s|0)!=1073741824){f=(s<<1)+a|0;if((s^a|0)>0&(f^a|0)<0){Ge[n>>2]=1;a=(a>>>31)+2147483647|0}else a=f}else{Ge[n>>2]=1;a=2147483647}l=l+1|0}while((l&65535)<<16>>16!=i<<16>>16)}else d=8}else{o=0;d=8}if((d|0)==8)a=o>>4;if(!a)return;c=((kt(a)|0)&65535)+65535|0;s=c<<16>>16;if((c&65535)<<16>>16>0){f=a<<s;if((f>>s|0)==(a|0))a=f;else a=a>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)a=a>>(s>>16);else a=0}u=Ft(a,n)|0;a=Ge[n>>2]|0;if(t){f=0;o=0;do{l=Ve[e+(f<<1)>>1]|0;l=Ze(l,l)|0;if((l|0)!=1073741824){s=(l<<1)+o|0;if((l^o|0)>0&(s^o|0)<0){Ge[n>>2]=1;o=(o>>>31)+2147483647|0}else o=s}else{Ge[n>>2]=1;o=2147483647}f=f+1|0}while((f&65535)<<16>>16!=i<<16>>16);if((o|0)==2147483647){Ge[n>>2]=a;a=0;f=0;do{l=Ve[e+(a<<1)>>1]>>2;l=Ze(l,l)|0;if((l|0)!=1073741824){s=(l<<1)+f|0;if((l^f|0)>0&(s^f|0)<0){Ge[n>>2]=1;f=(f>>>31)+2147483647|0}else f=s}else{Ge[n>>2]=1;f=2147483647}a=a+1|0}while((a&65535)<<16>>16!=i<<16>>16)}else d=28}else{o=0;d=28}if((d|0)==28)f=o>>4;if(!f)t=0;else{l=kt(f)|0;s=l<<16>>16;if(l<<16>>16>0){a=f<<s;if((a>>s|0)==(f|0))f=a;else f=f>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)f=f>>(s>>16);else f=0}a=c-(l&65535)|0;s=a&65535;o=(Kn(u,Ft(f,n)|0)|0)<<16>>16;t=o<<7;a=a<<16>>16;if(s<<16>>16>0)t=s<<16>>16<31?t>>a:0;else{c=0-a<<16>>16;e=t<<c;t=(e>>c|0)==(t|0)?e:o>>24^2147483647}t=at(t,n)|0;if((t|0)>4194303)t=2147483647;else t=(t|0)<-4194304?-2147483648:t<<9;t=Ft(t,n)|0}o=(i&65535)+65535&65535;if(o<<16>>16<=-1)return;l=t<<16>>16;s=i+-1<<16>>16<<16>>16;while(1){a=r+(s<<1)|0;t=Ze(Ve[a>>1]|0,l)|0;do{if((t|0)!=1073741824){f=t<<1;if((f|0)<=268435455)if((f|0)<-268435456){Ve[a>>1]=-32768;break}else{Ve[a>>1]=t>>>12;break}else d=52}else{Ge[n>>2]=1;d=52}}while(0);if((d|0)==52){d=0;Ve[a>>1]=32767}o=o+-1<<16>>16;if(o<<16>>16<=-1)break;else s=s+-1|0}return}function Se(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0;s=Ke;Ke=Ke+496|0;f=s;a=(t|0)==2;do{if(!(a&1|(t|0)==4)){if(t){e=-1;Ke=s;return e|0}a=Ve[i>>1]|0;r=i+490|0;t=i+2|0;o=0;while(1){Ve[f+(o<<1)>>1]=Ve[t>>1]|0;o=o+1|0;if((o|0)==244)break;else t=t+2|0}o=a<<16>>16;if(a<<16>>16==7){t=492;r=Ge[e+1760>>2]|0;break}else{t=492;r=Ve[r>>1]|0;break}}else{o=e+1168|0;if(a){Pr(r,i,f,o);o=604}else{ze(r,i,f,o);o=3436}t=Ve[o+(r<<1)>>1]|0;do{if(r>>>0>=8){if((r|0)==8){r=Ve[f+76>>1]<<2|(Ve[f+74>>1]<<1|Ve[f+72>>1]);o=(Ve[f+70>>1]|0)==0?4:5;break}if(r>>>0<15){e=-1;Ke=s;return e|0}else{r=Ge[e+1760>>2]|0;o=7;break}}else o=0}while(0);if(t<<16>>16==-1){e=-1;Ke=s;return e|0}}}while(0);Tr(e,r,f,o,n);Ge[e+1760>>2]=r;e=t;Ke=s;return e|0}function Re(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0;m=Ke;Ke=Ke+48|0;h=m+20|0;w=m;t=h;n=t+20|0;do{Ve[t>>1]=Ve[e>>1]|0;t=t+2|0;e=e+2|0}while((t|0)<(n|0));e=Ve[h+18>>1]|0;d=(e&65535)-((e&65535)>>>15&65535)|0;e:do{if(((d<<16>>31^d)&65535)<<16>>16<=4095){n=9;d=9;while(1){e=e<<16>>16;e=(e<<19>>19|0)==(e|0)?e<<3:e>>>15^32767;c=r+(n<<1)|0;Ve[c>>1]=e;e=e<<16>>16;e=Ze(e,e)|0;if((e|0)==1073741824){Ge[i>>2]=1;t=2147483647}else t=e<<1;e=2147483647-t|0;if((e&t|0)<0){Ge[i>>2]=1;e=2147483647}l=kt(e)|0;u=15-(l&65535)&65535;o=l<<16>>16;if(l<<16>>16>0){t=e<<o;if((t>>o|0)!=(e|0))t=e>>31^2147483647}else{t=0-o<<16;if((t|0)<2031616)t=e>>(t>>16);else t=0}t=Kn(16384,Ft(t,i)|0)|0;do{if(d<<16>>16>0){l=n+-1|0;a=t<<16>>16;f=d<<16>>16;s=0;while(1){n=We[h+(s<<1)>>1]|0;e=n<<16;o=Ze(Ve[h+(l-s<<1)>>1]|0,Ve[c>>1]|0)|0;if((o|0)==1073741824){Ge[i>>2]=1;t=2147483647}else t=o<<1;o=e-t|0;if(((o^e)&(t^e)|0)<0){Ge[i>>2]=1;o=(n>>>15)+2147483647|0}o=Ze((Ft(o,i)|0)<<16>>16,a)|0;if((o|0)==1073741824){Ge[i>>2]=1;o=2147483647}else o=o<<1;o=ut(o,u,i)|0;t=o-(o>>>31)|0;if((t>>31^t|0)>32767){o=24;break}Ve[w+(s<<1)>>1]=o;s=s+1|0;if((f|0)<=(s|0)){o=26;break}}if((o|0)==24){o=0;t=r;n=t+20|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(n|0));e=10}else if((o|0)==26){o=0;if(d<<16>>16>0)e=d;else{o=28;break}}t=e+-1<<16>>16;Yt(h|0,w|0,((t&65535)<<1)+2|0)|0;n=t<<16>>16}else o=28}while(0);if((o|0)==28){e=d+-1<<16>>16;if(e<<16>>16>-1){n=e<<16>>16;t=32767}else break}e=Ve[h+(n<<1)>>1]|0;d=(e&65535)-((e&65535)>>>15&65535)|0;if(((d<<16>>31^d)&65535)<<16>>16>4095)break e;else d=t}Ke=m;return}}while(0);t=r;n=t+20|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(n|0));Ke=m;return}function Me(e,r){e=e|0;r=r|0;var i=0,n=0,t=0,o=0,a=0;if(r<<16>>16<=0){e=0;return e|0}n=Ge[e>>2]|0;t=0;i=0;do{a=n&1;i=a|i<<1&131070;o=n>>1;n=(a|0)==(n>>>28&1|0)?o:o|1073741824;t=t+1<<16>>16}while(t<<16>>16<r<<16>>16);Ge[e>>2]=n;a=i&65535;return a|0}function Ne(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0;t=r;n=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(n|0));n=0;t=Ge[e>>2]|0;do{s=t&1;f=t>>1;f=(s|0)==(t>>>28&1|0)?f:f|1073741824;o=f&1;a=f>>1;Ge[e>>2]=(o|0)==(f>>>28&1|0)?a:a|1073741824;o=Gn((Ze(s<<1|o,1310720)|0)>>>17&65535,n,i)|0;s=Ge[e>>2]|0;a=s&1;f=s>>1;t=(a|0)==(s>>>28&1|0)?f:f|1073741824;Ge[e>>2]=t;Ve[r+(o<<16>>16<<1)>>1]=((a&65535)<<13&65535)+-4096<<16>>16;n=n+1<<16>>16}while(n<<16>>16<10);return}function Je(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0;a=Ve[e>>1]|0;if((a*31821|0)==1073741824){Ge[o>>2]=1;f=1073741823}else f=a*63642>>1;a=f+13849|0;if((f|0)>-1&(a^f|0)<0){Ge[o>>2]=1;a=(f>>>31)+2147483647|0}Ve[e>>1]=a;if(r<<16>>16<=0)return;f=0;a=t+((a&127)<<1)|0;while(1){Ve[n+(f<<1)>>1]=(-65536<<Ve[i+(f<<1)>>1]>>>16^65535)&We[a>>1];f=f+1|0;if((f&65535)<<16>>16==r<<16>>16)break;else a=a+2|0}return}function Oe(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+122|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function $e(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0;f=159;a=0;while(1){l=Ve[i+(f<<1)>>1]|0;l=Ze(l,l)|0;l=(l|0)==1073741824?2147483647:l<<1;o=l+a|0;if((l^a|0)>-1&(o^a|0)<0){Ge[t>>2]=1;a=(a>>>31)+2147483647|0}else a=o;if((f|0)>0)f=f+-1|0;else{f=a;break}}t=f>>>14&65535;a=32767;o=59;while(1){l=Ve[e+(o<<1)>>1]|0;a=l<<16>>16<a<<16>>16?l:a;if((o|0)>0)o=o+-1|0;else break}l=(f|0)>536870911?32767:t;t=a<<16>>16;o=t<<20>>16;f=a<<16>>16>0?32767:-32768;i=55;a=Ve[e>>1]|0;while(1){s=Ve[e+(i<<1)>>1]|0;a=a<<16>>16<s<<16>>16?s:a;if((i|0)>1)i=i+-1|0;else break}i=Ve[e+80>>1]|0;s=Ve[e+82>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+84>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+86>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+88>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+90>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+92>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+94>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+96>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+98>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+100>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+102>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+104>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+106>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+108>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+110>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+112>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+114>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=Ve[e+116>>1]|0;i=i<<16>>16<s<<16>>16?s:i;s=e+118|0;c=Ve[s>>1]|0;do{if((l+-21&65535)<17557&a<<16>>16>20?(l<<16>>16|0)<(((t<<4|0)==(o|0)?o:f)|0)?1:(i<<16>>16<c<<16>>16?c:i)<<16>>16<1953:0){a=e+120|0;o=Ve[a>>1]|0;if(o<<16>>16>29){Ve[a>>1]=30;i=a;f=1;break}else{f=(o&65535)+1&65535;Ve[a>>1]=f;i=a;f=f<<16>>16>1&1;break}}else u=14}while(0);if((u|0)==14){i=e+120|0;Ve[i>>1]=0;f=0}a=0;do{c=a;a=a+1|0;Ve[e+(c<<1)>>1]=Ve[e+(a<<1)>>1]|0}while((a|0)!=59);Ve[s>>1]=l;a=Ve[i>>1]|0;a=a<<16>>16>15?16383:a<<16>>16>8?15565:13926;o=rt(r+8|0,5)|0;if((Ve[i>>1]|0)>20){if(((rt(r,9)|0)<<16>>16|0)>(a|0))u=20}else if((o<<16>>16|0)>(a|0))u=20;if((u|0)==20){Ve[n>>1]=0;return f|0}o=(We[n>>1]|0)+1&65535;if(o<<16>>16>10){Ve[n>>1]=10;return f|0}else{Ve[n>>1]=o;return f|0}return 0}function Le(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+18|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function er(e,r,i,n,t,o,a,f,s,l,u,c){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0;g=e+2|0;Ve[e>>1]=Ve[g>>1]|0;y=e+4|0;Ve[g>>1]=Ve[y>>1]|0;_=e+6|0;Ve[y>>1]=Ve[_>>1]|0;A=e+8|0;Ve[_>>1]=Ve[A>>1]|0;D=e+10|0;Ve[A>>1]=Ve[D>>1]|0;S=e+12|0;Ve[D>>1]=Ve[S>>1]|0;Ve[S>>1]=i;p=0;E=0;do{d=t+(E<<1)|0;w=Ct(Ve[d>>1]|0,Ve[n+(E<<1)>>1]|0,c)|0;w=(w&65535)-((w&65535)>>>15&65535)|0;w=w<<16>>31^w;k=((Et(w&65535)|0)&65535)+65535|0;h=k<<16>>16;if((k&65535)<<16>>16<0){m=0-h<<16;if((m|0)<983040)v=w<<16>>16>>(m>>16)&65535;else v=0}else{m=w<<16>>16;w=m<<h;if((w<<16>>16>>h|0)==(m|0))v=w&65535;else v=(m>>>15^32767)&65535}b=Et(Ve[d>>1]|0)|0;w=Ve[d>>1]|0;h=b<<16>>16;if(b<<16>>16<0){m=0-h<<16;if((m|0)<983040)m=w<<16>>16>>(m>>16)&65535;else m=0}else{m=w<<16>>16;w=m<<h;if((w<<16>>16>>h|0)==(m|0))m=w&65535;else m=(m>>>15^32767)&65535}h=Kn(v,m)|0;m=(k&65535)+2-(b&65535)|0;w=m&65535;do{if(m&32768){if(w<<16>>16!=-32768){k=0-m|0;m=k<<16>>16;if((k&65535)<<16>>16<0){m=0-m<<16;if((m|0)>=983040){m=0;break}m=h<<16>>16>>(m>>16)&65535;break}}else m=32767;w=h<<16>>16;h=w<<m;if((h<<16>>16>>m|0)==(w|0))m=h&65535;else m=(w>>>15^32767)&65535}else m=It(h,w,c)|0}while(0);p=Gn(p,m,c)|0;E=E+1|0}while((E|0)!=10);m=p&65535;w=p<<16>>16>5325;p=e+14|0;if(w){t=(We[p>>1]|0)+1&65535;Ve[p>>1]=t;if(t<<16>>16>10)Ve[e+16>>1]=0}else Ve[p>>1]=0;switch(r|0){case 0:case 1:case 2:case 3:case 6:break;default:{S=e+16|0;c=i;i=Ve[S>>1]|0;i=i&65535;i=i+1|0;i=i&65535;Ve[S>>1]=i;return c|0}}v=(a|o)<<16>>16==0;b=l<<16>>16==0;k=r>>>0<3;p=m+(k&((b|(v&(f<<16>>16==0|s<<16>>16==0)|u<<16>>16<2))^1)?61030:62259)&65535;p=p<<16>>16>0?p:0;if(p<<16>>16<=2048){p=p<<16>>16;if((p<<18>>18|0)==(p|0))s=p<<2;else s=p>>>15^32767}else s=8192;f=e+16|0;u=w|(Ve[f>>1]|0)<40;p=Ve[y>>1]|0;if((p*6554|0)==1073741824){Ge[c>>2]=1;w=2147483647}else w=p*13108|0;p=Ve[_>>1]|0;m=p*6554|0;if((m|0)!=1073741824){p=(p*13108|0)+w|0;if((m^w|0)>0&(p^w|0)<0){Ge[c>>2]=1;p=(w>>>31)+2147483647|0}}else{Ge[c>>2]=1;p=2147483647}m=Ve[A>>1]|0;w=m*6554|0;if((w|0)!=1073741824){m=(m*13108|0)+p|0;if((w^p|0)>0&(m^p|0)<0){Ge[c>>2]=1;m=(p>>>31)+2147483647|0}}else{Ge[c>>2]=1;m=2147483647}p=Ve[D>>1]|0;w=p*6554|0;if((w|0)!=1073741824){p=(p*13108|0)+m|0;if((w^m|0)>0&(p^m|0)<0){Ge[c>>2]=1;w=(m>>>31)+2147483647|0}else w=p}else{Ge[c>>2]=1;w=2147483647}p=Ve[S>>1]|0;m=p*6554|0;if((m|0)!=1073741824){p=(p*13108|0)+w|0;if((m^w|0)>0&(p^w|0)<0){Ge[c>>2]=1;p=(w>>>31)+2147483647|0}}else{Ge[c>>2]=1;p=2147483647}w=Ft(p,c)|0;if(k&((v|b)^1)){p=Ve[e>>1]|0;if((p*4681|0)==1073741824){Ge[c>>2]=1;w=2147483647}else w=p*9362|0;p=Ve[g>>1]|0;m=p*4681|0;if((m|0)!=1073741824){p=(p*9362|0)+w|0;if((m^w|0)>0&(p^w|0)<0){Ge[c>>2]=1;w=(w>>>31)+2147483647|0}else w=p}else{Ge[c>>2]=1;w=2147483647}p=Ve[y>>1]|0;m=p*4681|0;if((m|0)!=1073741824){p=(p*9362|0)+w|0;if((m^w|0)>0&(p^w|0)<0){Ge[c>>2]=1;w=(w>>>31)+2147483647|0}else w=p}else{Ge[c>>2]=1;w=2147483647}p=Ve[_>>1]|0;m=p*4681|0;if((m|0)!=1073741824){p=(p*9362|0)+w|0;if((m^w|0)>0&(p^w|0)<0){Ge[c>>2]=1;p=(w>>>31)+2147483647|0}}else{Ge[c>>2]=1;p=2147483647}m=Ve[A>>1]|0;w=m*4681|0;if((w|0)!=1073741824){m=(m*9362|0)+p|0;if((w^p|0)>0&(m^p|0)<0){Ge[c>>2]=1;p=(p>>>31)+2147483647|0}else p=m}else{Ge[c>>2]=1;p=2147483647}m=Ve[D>>1]|0;w=m*4681|0;if((w|0)!=1073741824){m=(m*9362|0)+p|0;if((w^p|0)>0&(m^p|0)<0){Ge[c>>2]=1;m=(p>>>31)+2147483647|0}}else{Ge[c>>2]=1;m=2147483647}w=Ve[S>>1]|0;d=w*4681|0;if((d|0)!=1073741824){h=(w*9362|0)+m|0;if((d^m|0)>0&(h^m|0)<0){Ge[c>>2]=1;h=(m>>>31)+2147483647|0}}else{Ge[c>>2]=1;h=2147483647}w=Ft(h,c)|0}p=u?8192:s<<16>>16;d=Ze(p,i<<16>>16)|0;if((d|0)==1073741824){Ge[c>>2]=1;m=2147483647}else m=d<<1;w=w<<16>>16;h=w<<13;if((h|0)!=1073741824){d=m+(w<<14)|0;if((m^h|0)>0&(d^m|0)<0){Ge[c>>2]=1;m=(m>>>31)+2147483647|0}else m=d}else{Ge[c>>2]=1;m=2147483647}d=Ze(w,p)|0;if((d|0)==1073741824){Ge[c>>2]=1;h=2147483647}else h=d<<1;d=m-h|0;if(((d^m)&(h^m)|0)<0){Ge[c>>2]=1;d=(m>>>31)+2147483647|0}S=d<<2;i=f;c=Ft((S>>2|0)==(d|0)?S:d>>31^2147483647,c)|0;S=Ve[i>>1]|0;S=S&65535;S=S+1|0;S=S&65535;Ve[i>>1]=S;return c|0}function rr(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0;n=r;t=n+80|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));n=0;do{a=Ve[e+(n<<1)>>1]|0;t=((a&8)<<10&65535^8192)+-4096<<16>>16;o=n<<16;a=((Ve[i+((a&7)<<1)>>1]|0)*327680|0)+o>>16;Ve[r+(a<<1)>>1]=t;o=((Ve[i+((We[e+(n+5<<1)>>1]&7)<<1)>>1]|0)*327680|0)+o>>16;if((o|0)<(a|0))t=0-(t&65535)&65535;a=r+(o<<1)|0;Ve[a>>1]=(We[a>>1]|0)+(t&65535);n=n+1|0}while((n|0)!=5);return}function ir(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;t=r<<16>>16;n=(t<<1&2|1)+((t>>>1&7)*5|0)|0;r=t>>>4&3;r=((t>>>6&7)*5|0)+((r|0)==3?4:r)|0;t=i;o=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(o|0));e=e<<16>>16;Ve[i+(n<<1)>>1]=(0-(e&1)&16383)+57344;Ve[i+(r<<1)>>1]=(0-(e>>>1&1)&16383)+57344;return}function nr(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0;o=i<<16>>16;f=o>>>3;e=e<<16>>16;e=((e<<17>>17|0)==(e|0)?e<<1:e>>>15^32767)+(f&8)<<16;f=(We[n+(e+65536>>16<<1)>>1]|0)+((f&7)*5|0)|0;i=r<<16>>16;a=(0-(i&1)&16383)+57344&65535;e=t+((We[n+(e>>16<<1)>>1]|0)+((o&7)*5|0)<<16>>16<<1)|0;r=t;o=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(o|0));Ve[e>>1]=a;Ve[t+(f<<16>>16<<1)>>1]=(0-(i>>>1&1)&16383)+57344;return}function tr(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0;r=r<<16>>16;n=(r&7)*5|0;t=(r>>>2&2|1)+((r>>>4&7)*5|0)|0;r=(r>>>6&2)+2+((r>>>8&7)*5|0)|0;o=i;a=o+80|0;do{Ve[o>>1]=0;o=o+2|0}while((o|0)<(a|0));e=e<<16>>16;Ve[i+(n<<1)>>1]=(0-(e&1)&16383)+57344;Ve[i+(t<<1)>>1]=(0-(e>>>1&1)&16383)+57344;Ve[i+(r<<1)>>1]=(0-(e>>>2&1)&16383)+57344;return}function or(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0;r=r<<16>>16;a=Ve[i+((r&7)<<1)>>1]|0;f=Ve[i+((r>>>3&7)<<1)>>1]|0;o=Ve[i+((r>>>6&7)<<1)>>1]|0;i=(r>>>9&1)+3+((Ve[i+((r>>>10&7)<<1)>>1]|0)*5|0)|0;r=n;t=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(t|0));e=e<<16>>16;Ve[n+(a*327680>>16<<1)>>1]=(0-(e&1)&16383)+57344;Ve[n+((f*327680|0)+65536>>16<<1)>>1]=(0-(e>>>1&1)&16383)+57344;Ve[n+((o*327680|0)+131072>>16<<1)>>1]=(0-(e>>>2&1)&16383)+57344;Ve[n+(i<<16>>16<<1)>>1]=(0-(e>>>3&1)&16383)+57344;return}function ar(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0;d=Ke;Ke=Ke+32|0;c=d+16|0;u=d;o=r;t=o+80|0;do{Ve[o>>1]=0;o=o+2|0}while((o|0)<(t|0));t=Ve[e>>1]|0;Ve[c>>1]=t;Ve[c+2>>1]=Ve[e+2>>1]|0;Ve[c+4>>1]=Ve[e+4>>1]|0;Ve[c+6>>1]=Ve[e+6>>1]|0;s=Ve[e+8>>1]|0;Fe(s>>>3&65535,s&7,0,4,1,u,i);s=Ve[e+10>>1]|0;Fe(s>>>3&65535,s&7,2,6,5,u,i);s=Ve[e+12>>1]|0;n=s>>2;do{if((n*25|0)!=1073741824){o=(Ze(n,1638400)|0)+786432>>21;n=o*6554>>15;if((n|0)>32767){Ge[i>>2]=1;a=1;f=1;e=163835;l=6;break}e=(n<<16>>16)*5|0;a=n&1;if((e|0)==1073741824){Ge[i>>2]=1;f=0;e=65535}else{f=0;l=6}}else{Ge[i>>2]=1;a=0;n=0;f=0;o=0;e=0;l=6}}while(0);if((l|0)==6)e=e&65535;l=o-e|0;a=a<<16>>16==0?l:4-l|0;l=a<<16>>16;Ve[u+6>>1]=Gn(((a<<17>>17|0)==(l|0)?a<<1:l>>>15^32767)&65535,s&1,i)|0;if(f){Ge[i>>2]=1;n=32767}l=n<<16>>16;Ve[u+14>>1]=((n<<17>>17|0)==(l|0)?n<<1:l>>>15^32767)+(s>>>1&1);n=0;while(1){t=t<<16>>16==0?8191:-8191;l=(Ve[u+(n<<1)>>1]<<2)+n<<16;o=l>>16;if((l|0)<2621440)Ve[r+(o<<1)>>1]=t;a=(Ve[u+(n+4<<1)>>1]<<2)+n<<16;e=a>>16;if((e|0)<(o|0))t=0-(t&65535)&65535;if((a|0)<2621440){l=r+(e<<1)|0;Ve[l>>1]=(We[l>>1]|0)+(t&65535)}n=n+1|0;if((n|0)==4)break;t=Ve[c+(n<<1)>>1]|0}Ke=d;return}function Fe(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0,h=0;s=e<<16>>16>124?124:e;e=(s<<16>>16)*1311>>15;h=(e|0)>32767;if(!h){f=e<<16>>16;if((f*25|0)==1073741824){Ge[a>>2]=1;f=1073741823}else d=4}else{Ge[a>>2]=1;f=32767;d=4}if((d|0)==4)f=(f*50|0)>>>1;u=(s&65535)-f|0;f=(u<<16>>16)*6554>>15;c=(f|0)>32767;if(!c){s=f<<16>>16;if((s*5|0)==1073741824){Ge[a>>2]=1;l=1073741823}else d=9}else{Ge[a>>2]=1;s=32767;d=9}if((d|0)==9)l=(s*10|0)>>>1;u=u-l|0;d=u<<16>>16;s=r<<16>>16;l=s>>2;s=s-(l<<2)|0;Ve[o+(i<<16>>16<<1)>>1]=((u<<17>>17|0)==(d|0)?u<<1:d>>>15^32767)+(s&1);if(c){Ge[a>>2]=1;f=32767}i=f<<16>>16;Ve[o+(n<<16>>16<<1)>>1]=((f<<17>>17|0)==(i|0)?f<<1:i>>>15^32767)+(s<<16>>17);if(h){Ge[a>>2]=1;e=32767}n=e<<16>>16;Ve[o+(t<<16>>16<<1)>>1]=Gn(l&65535,((e<<17>>17|0)==(n|0)?e<<1:n>>>15^32767)&65535,a)|0;return}function Ie(e){e=e|0;var r=0,i=0,n=0,t=0;if(!e){t=-1;return t|0}et(e+1168|0);Ve[e+460>>1]=40;Ge[e+1164>>2]=0;r=e+646|0;i=e+1216|0;n=e+462|0;t=n+22|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));Ce(r,Ge[i>>2]|0)|0;Ue(e+686|0)|0;xe(e+700|0)|0;Le(e+608|0)|0;je(e+626|0,Ge[i>>2]|0)|0;Oe(e+484|0)|0;qe(e+730|0)|0;Be(e+748|0)|0;Zn(e+714|0)|0;fr(e,0)|0;t=0;return t|0}function fr(e,r){e=e|0;r=r|0;var i=0,n=0;if(!e){e=-1;return e|0}Ge[e+388>>2]=e+308;Vt(e|0,0,308)|0;r=(r|0)!=8;if(r){i=e+412|0;n=i+20|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Ve[e+392>>1]=3e4;Ve[e+394>>1]=26e3;Ve[e+396>>1]=21e3;Ve[e+398>>1]=15e3;Ve[e+400>>1]=8e3;Ve[e+402>>1]=0;Ve[e+404>>1]=-8e3;Ve[e+406>>1]=-15e3;Ve[e+408>>1]=-21e3;Ve[e+410>>1]=-26e3}Ve[e+432>>1]=0;Ve[e+434>>1]=40;Ge[e+1164>>2]=0;Ve[e+436>>1]=0;Ve[e+438>>1]=0;Ve[e+440>>1]=0;Ve[e+460>>1]=40;Ve[e+462>>1]=0;Ve[e+464>>1]=0;if(r){i=e+442|0;n=i+18|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));i=e+466|0;n=i+18|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Le(e+608|0)|0;n=e+1216|0;je(e+626|0,Ge[n>>2]|0)|0;Ce(e+646|0,Ge[n>>2]|0)|0;Ue(e+686|0)|0;xe(e+700|0)|0;Zn(e+714|0)|0}else{i=e+466|0;n=i+18|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Le(e+608|0)|0;Ce(e+646|0,Ge[e+1216>>2]|0)|0;Ue(e+686|0)|0;xe(e+700|0)|0}Oe(e+484|0)|0;Ve[e+606>>1]=21845;qe(e+730|0)|0;if(!r){e=0;return e|0}Be(e+748|0)|0;e=0;return e|0}function Te(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0,G=0,W=0,K=0,Z=0,Q=0,J=0,$=0,ee=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0,fe=0,se=0,le=0,ue=0,ce=0,de=0,he=0,we=0,me=0,pe=0,ve=0,be=0,ke=0,Ee=0,ge=0,ye=0,_e=0,Ae=0,De=0,Se=0,Re=0,Me=0,Ne=0,Oe=0,Le=0,Fe=0,Ie=0,Te=0,Pe=0,Ce=0,Be=0,xe=0,Ue=0,ze=0,je=0,qe=0,He=0,Ye=0;Ye=Ke;Ke=Ke+336|0;d=Ye+236|0;c=Ye+216|0;qe=Ye+112|0;je=Ye+12|0;Ce=Ye+256|0;xe=Ye+136|0;Be=Ye+32|0;Te=Ye+8|0;Pe=Ye+6|0;ze=Ye+4|0;Ue=Ye+2|0;He=Ye;Ne=e+1164|0;Oe=e+748|0;Le=vr(Oe,n,Ne)|0;if(Le){fr(e,8)|0;mr(Oe,e+412|0,e+646|0,e+714|0,e+608|0,Le,r,i,e+1168|0,t,o,Ne);He=e+666|0;pt(He,e+392|0,10,Ne);Ar(e+626|0,He,Ne);He=e+1156|0;Ge[He>>2]=Le;Ke=Ye;return}switch(n|0){case 1:{a=1;b=6;break}case 2:case 7:{Je(e+606|0,Ve[(Ge[e+1256>>2]|0)+(r<<1)>>1]|0,Ge[(Ge[e+1260>>2]|0)+(r<<2)>>2]|0,i,Ge[e+1276>>2]|0,Ne);b=9;break}case 3:{b=9;break}default:{a=0;b=6}}do{if((b|0)==6){n=e+440|0;if((Ve[n>>1]|0)==6){Ve[n>>1]=5;Re=0;Me=0;break}else{Ve[n>>1]=0;Re=0;Me=0;break}}else if((b|0)==9){n=e+440|0;Re=(We[n>>1]|0)+1&65535;Ve[n>>1]=Re<<16>>16>6?6:Re;Re=1;Me=1;a=0}}while(0);_e=e+1156|0;switch(Ge[_e>>2]|0){case 1:{Ve[n>>1]=5;Ve[e+436>>1]=0;break}case 2:{Ve[n>>1]=5;Ve[e+436>>1]=1;break}default:{}}s=e+646|0;Ae=e+666|0;f=qe;l=Ae;u=f+20|0;do{Xe[f>>0]=Xe[l>>0]|0;f=f+1|0;l=l+1|0}while((f|0)<(u|0));De=(r|0)!=7;Se=e+1168|0;if(De){hr(s,r,Me,i,Se,d,Ne);f=e+392|0;tt(f,d,o,Ne);i=i+6|0}else{wr(s,Me,i,Se,c,d,Ne);f=e+392|0;it(f,c,d,o,Ne);i=i+10|0}l=d;u=f+20|0;do{Ve[f>>1]=Ve[l>>1]|0;f=f+2|0;l=l+2|0}while((f|0)<(u|0));ye=r>>>0>1;y=r>>>0<4&1;ge=(r|0)==5;Ee=ge?10:5;ge=ge?19:9;D=e+434|0;S=143-ge&65535;R=e+460|0;M=e+462|0;N=e+464|0;_=r>>>0>2;O=e+388|0;L=(r|0)==0;F=r>>>0<2;I=e+1244|0;T=e+432|0;P=r>>>0<6;C=e+1168|0;B=(r|0)==6;x=Me<<16>>16==0;U=e+714|0;z=e+686|0;j=e+436|0;q=e+700|0;H=(r|0)==7;Y=e+482|0;X=r>>>0<3;V=e+608|0;G=e+626|0;W=e+438|0;K=r>>>0<7;Z=e+730|0;A=Re^1;Q=a<<16>>16!=0;ke=Q?Me^1:0;J=e+442|0;$=e+458|0;ee=e+412|0;re=e+80|0;ie=e+1236|0;ne=e+1240|0;te=e+468|0;oe=e+466|0;ae=e+470|0;fe=e+472|0;se=e+474|0;le=e+476|0;ue=e+478|0;ce=e+480|0;de=e+444|0;he=e+446|0;we=e+448|0;me=e+450|0;pe=e+452|0;ve=e+454|0;be=e+456|0;k=0;E=0;h=0;w=0;g=-1;while(1){g=(g<<16>>16)+1|0;u=g&65535;E=1-(E<<16>>16)|0;p=E&65535;c=ye&h<<16>>16==80?0:h;m=i+2|0;d=Ve[i>>1]|0;e:do{if(De){v=Ve[D>>1]|0;f=(v&65535)-Ee&65535;f=f<<16>>16<20?20:f;l=(f&65535)+ge&65535;s=l<<16>>16>143;lr(d,s?S:f,s?143:l,c,v,Te,Pe,y,Ne);c=Ve[Te>>1]|0;Ve[R>>1]=c;if(Re){d=Ve[D>>1]|0;if(d<<16>>16<143){d=(d&65535)+1&65535;Ve[D>>1]=d}Ve[Te>>1]=d;Ve[Pe>>1]=0;if((Ve[M>>1]|0)!=0?!(_|(Ve[N>>1]|0)<5):0){Ve[Te>>1]=c;d=c;c=0}else c=0}else{d=c;c=Ve[Pe>>1]|0}yt(Ge[O>>2]|0,d,c,40,1,Ne);if(F){c=i+6|0;nr(u,Ve[i+4>>1]|0,Ve[m>>1]|0,Ge[I>>2]|0,Ce,Ne);i=Ve[T>>1]|0;v=i<<16>>16;d=v<<1;if((d|0)==(v<<17>>16|0)){l=L;break}l=L;d=i<<16>>16>0?32767:-32768;break}switch(r|0){case 2:{c=i+6|0;ir(Ve[i+4>>1]|0,Ve[m>>1]|0,Ce);i=Ve[T>>1]|0;v=i<<16>>16;d=v<<1;if((d|0)==(v<<17>>16|0)){l=L;break e}l=L;d=i<<16>>16>0?32767:-32768;break e}case 3:{c=i+6|0;tr(Ve[i+4>>1]|0,Ve[m>>1]|0,Ce);i=Ve[T>>1]|0;v=i<<16>>16;d=v<<1;if((d|0)==(v<<17>>16|0)){l=L;break e}l=L;d=i<<16>>16>0?32767:-32768;break e}default:{if(P){c=i+6|0;or(Ve[i+4>>1]|0,Ve[m>>1]|0,Ge[C>>2]|0,Ce);i=Ve[T>>1]|0;v=i<<16>>16;d=v<<1;if((d|0)==(v<<17>>16|0)){l=L;break e}l=L;d=i<<16>>16>0?32767:-32768;break e}if(!B){l=L;b=44;break e}ar(m,Ce,Ne);d=i+16|0;i=Ve[T>>1]|0;v=i<<16>>16;u=v<<1;if((u|0)==(v<<17>>16|0)){c=d;l=L;d=u;break e}c=d;l=L;d=i<<16>>16>0?32767:-32768;break e}}}else{ur(d,18,143,c,Te,Pe,Ne);if(x?c<<16>>16==0|d<<16>>16<61:0){d=Ve[Te>>1]|0;c=Ve[Pe>>1]|0}else{Ve[R>>1]=Ve[Te>>1]|0;d=Ve[D>>1]|0;Ve[Te>>1]=d;Ve[Pe>>1]=0;c=0}yt(Ge[O>>2]|0,d,c,40,0,Ne);l=0;b=44}}while(0);if((b|0)==44){b=0;if(Re)Er(z,Ve[n>>1]|0,ze,Ne);else Ve[ze>>1]=dr(r,Ve[m>>1]|0,Ge[ne>>2]|0)|0;gr(z,Me,Ve[j>>1]|0,ze,Ne);rr(i+4|0,Ce,Ge[C>>2]|0);d=i+24|0;i=Ve[ze>>1]|0;v=i<<16>>16;u=v<<1;if((u|0)==(v<<17>>16|0)){c=d;d=u}else{c=d;d=i<<16>>16>0?32767:-32768}}i=Ve[Te>>1]|0;e:do{if(i<<16>>16<40){f=d<<16>>16;s=i;d=i<<16>>16;while(1){u=Ce+(d<<1)|0;i=(Ze(Ve[Ce+(d-(s<<16>>16)<<1)>>1]|0,f)|0)>>15;if((i|0)>32767){Ge[Ne>>2]=1;i=32767}v=i&65535;Ve[He>>1]=v;Ve[u>>1]=Gn(Ve[u>>1]|0,v,Ne)|0;d=d+1|0;if((d&65535)<<16>>16==40)break e;s=Ve[Te>>1]|0}}}while(0);e:do{if(l){l=(E&65535|0)==0;if(l){i=c;u=w}else{i=c+2|0;u=Ve[c>>1]|0}if(x)sr(U,r,u,Ce,p,ze,Ue,Se,Ne);else{Er(z,Ve[n>>1]|0,ze,Ne);br(q,U,Ve[n>>1]|0,Ue,Ne)}gr(z,Me,Ve[j>>1]|0,ze,Ne);kr(q,Me,Ve[j>>1]|0,Ue,Ne);c=Ve[ze>>1]|0;d=c<<16>>16>13017?13017:c;if(l)b=80;else v=u}else{i=c+2|0;d=Ve[c>>1]|0;switch(r|0){case 1:case 2:case 3:case 4:case 6:{if(x)sr(U,r,d,Ce,p,ze,Ue,Se,Ne);else{Er(z,Ve[n>>1]|0,ze,Ne);br(q,U,Ve[n>>1]|0,Ue,Ne)}gr(z,Me,Ve[j>>1]|0,ze,Ne);kr(q,Me,Ve[j>>1]|0,Ue,Ne);c=Ve[ze>>1]|0;d=c<<16>>16>13017?13017:c;if(!B){u=w;b=80;break e}if((Ve[D>>1]|0)<=45){u=w;b=80;break e}u=w;d=d<<16>>16>>>2&65535;b=80;break e}case 5:{if(Re)Er(z,Ve[n>>1]|0,ze,Ne);else Ve[ze>>1]=dr(5,d,Ge[ne>>2]|0)|0;gr(z,Me,Ve[j>>1]|0,ze,Ne);if(x)cr(U,5,Ve[i>>1]|0,Ce,Ge[ie>>2]|0,Ue,Ne);else br(q,U,Ve[n>>1]|0,Ue,Ne);kr(q,Me,Ve[j>>1]|0,Ue,Ne);d=Ve[ze>>1]|0;i=c+4|0;c=d;u=w;d=d<<16>>16>13017?13017:d;b=80;break e}default:{if(x)cr(U,r,d,Ce,Ge[ie>>2]|0,Ue,Ne);else br(q,U,Ve[n>>1]|0,Ue,Ne);kr(q,Me,Ve[j>>1]|0,Ue,Ne);d=Ve[ze>>1]|0;c=d;u=w;b=80;break e}}}}while(0);if((b|0)==80){b=0;Ve[T>>1]=c<<16>>16>13017?13017:c;v=u}d=d<<16>>16;d=(d<<17>>17|0)==(d|0)?d<<1:d>>>15^32767;p=(d&65535)<<16>>16>16384;e:do{if(p){m=d<<16>>16;if(H)c=0;else{c=0;while(1){d=(Ze(Ve[(Ge[O>>2]|0)+(c<<1)>>1]|0,m)|0)>>15;if((d|0)>32767){Ge[Ne>>2]=1;d=32767}Ve[He>>1]=d;d=Ze(Ve[ze>>1]|0,d<<16>>16)|0;if((d|0)==1073741824){Ge[Ne>>2]=1;d=2147483647}else d=d<<1;Ve[xe+(c<<1)>>1]=Ft(d,Ne)|0;c=c+1|0;if((c|0)==40)break e}}do{d=(Ze(Ve[(Ge[O>>2]|0)+(c<<1)>>1]|0,m)|0)>>15;if((d|0)>32767){Ge[Ne>>2]=1;d=32767}Ve[He>>1]=d;d=Ze(Ve[ze>>1]|0,d<<16>>16)|0;if((d|0)!=1073741824){d=d<<1;if((d|0)<0)d=~((d^-2)>>1);else b=88}else{Ge[Ne>>2]=1;d=2147483647;b=88}if((b|0)==88){b=0;d=d>>1}Ve[xe+(c<<1)>>1]=Ft(d,Ne)|0;c=c+1|0}while((c|0)!=40)}}while(0);if(x){Ve[oe>>1]=Ve[te>>1]|0;Ve[te>>1]=Ve[ae>>1]|0;Ve[ae>>1]=Ve[fe>>1]|0;Ve[fe>>1]=Ve[se>>1]|0;Ve[se>>1]=Ve[le>>1]|0;Ve[le>>1]=Ve[ue>>1]|0;Ve[ue>>1]=Ve[ce>>1]|0;Ve[ce>>1]=Ve[Y>>1]|0;Ve[Y>>1]=Ve[ze>>1]|0}if((Re|(Ve[j>>1]|0)!=0?X&(Ve[M>>1]|0)!=0:0)?(Fe=Ve[ze>>1]|0,Fe<<16>>16>12288):0){b=(((Fe<<16>>16)+118784|0)>>>1)+12288&65535;Ve[ze>>1]=b<<16>>16>14745?14745:b}_r(qe,Ae,h,je,Ne);d=er(V,r,Ve[Ue>>1]|0,je,G,Me,Ve[j>>1]|0,a,Ve[W>>1]|0,Ve[M>>1]|0,Ve[N>>1]|0,Ne)|0;switch(r|0){case 0:case 1:case 2:case 3:case 6:{u=Ve[ze>>1]|0;m=1;break}default:{d=Ve[Ue>>1]|0;u=Ve[ze>>1]|0;if(K)m=1;else{c=u<<16>>16;if(u<<16>>16<0)c=~((c^-2)>>1);else c=c>>>1;u=c&65535;m=2}}}f=u<<16>>16;h=m&65535;c=Ge[O>>2]|0;w=0;do{c=c+(w<<1)|0;Ve[Be+(w<<1)>>1]=Ve[c>>1]|0;c=Ze(Ve[c>>1]|0,f)|0;if((c|0)==1073741824){Ge[Ne>>2]=1;s=2147483647}else s=c<<1;l=Ze(Ve[Ue>>1]|0,Ve[Ce+(w<<1)>>1]|0)|0;if((l|0)!=1073741824){c=(l<<1)+s|0;if((l^s|0)>0&(c^s|0)<0){Ge[Ne>>2]=1;c=(s>>>31)+2147483647|0}}else{Ge[Ne>>2]=1;c=2147483647}b=c<<h;b=Ft((b>>h|0)==(c|0)?b:c>>31^2147483647,Ne)|0;c=Ge[O>>2]|0;Ve[c+(w<<1)>>1]=b;w=w+1|0}while((w|0)!=40);Sr(Z);if((X?(Ve[N>>1]|0)>3:0)?!((Ve[M>>1]|0)==0|A):0)Dr(Z);Rr(Z,r,Be,d,Ve[ze>>1]|0,Ce,u,m,Se,Ne);d=0;l=0;do{c=Ve[Be+(l<<1)>>1]|0;c=Ze(c,c)|0;if((c|0)!=1073741824){u=(c<<1)+d|0;if((c^d|0)>0&(u^d|0)<0){Ge[Ne>>2]=1;d=(d>>>31)+2147483647|0}else d=u}else{Ge[Ne>>2]=1;d=2147483647}l=l+1|0}while((l|0)!=40);if((d|0)<0)d=~((d^-2)>>1);else d=d>>1;d=Pt(d,He,Ne)|0;u=((Ve[He>>1]|0)>>>1)+15|0;c=u&65535;u=u<<16>>16;if(c<<16>>16>0)if(c<<16>>16<31){d=d>>u;b=135}else{d=0;b=137}else{m=0-u<<16>>16;b=d<<m;d=(b>>m|0)==(d|0)?b:d>>31^2147483647;b=135}if((b|0)==135){b=0;if((d|0)<0)d=~((d^-4)>>2);else b=137}if((b|0)==137){b=0;d=d>>>2}d=d&65535;do{if(X?(Ie=Ve[N>>1]|0,Ie<<16>>16>5):0)if(Ve[M>>1]|0)if((Ve[n>>1]|0)<4){if(Q){if(!(Re|(Ve[W>>1]|0)!=0))b=145}else if(!Re)b=145;if((b|0)==145?(0,(Ve[j>>1]|0)==0):0){b=147;break}yr(Be,d,J,Ie,Ve[j>>1]|0,ke,Ne)|0;b=147}else b=147;else b=151;else b=147}while(0);do{if((b|0)==147){b=0;if(Ve[M>>1]|0){if(!Re?(Ve[j>>1]|0)==0:0){b=151;break}if((Ve[n>>1]|0)>=4)b=151}else b=151}}while(0);if((b|0)==151){b=0;Ve[J>>1]=Ve[de>>1]|0;Ve[de>>1]=Ve[he>>1]|0;Ve[he>>1]=Ve[we>>1]|0;Ve[we>>1]=Ve[me>>1]|0;Ve[me>>1]=Ve[pe>>1]|0;Ve[pe>>1]=Ve[ve>>1]|0;Ve[ve>>1]=Ve[be>>1]|0;Ve[be>>1]=Ve[$>>1]|0;Ve[$>>1]=d}if(p){d=0;do{p=xe+(d<<1)|0;Ve[p>>1]=Gn(Ve[p>>1]|0,Ve[Be+(d<<1)>>1]|0,Ne)|0;d=d+1|0}while((d|0)!=40);Qe(Be,xe,40,Ne);Ge[Ne>>2]=0;Bt(o,xe,t+(k<<1)|0,40,ee,0)}else{Ge[Ne>>2]=0;Bt(o,Be,t+(k<<1)|0,40,ee,0)}if(!(Ge[Ne>>2]|0))Xt(ee|0,t+(k+30<<1)|0,20)|0;else{u=193;while(1){c=e+(u<<1)|0;p=Ve[c>>1]|0;d=p<<16>>16;if(p<<16>>16<0)d=~((d^-4)>>2);else d=d>>>2;Ve[c>>1]=d;if((u|0)>0)u=u+-1|0;else{u=39;break}}while(1){c=Be+(u<<1)|0;p=Ve[c>>1]|0;d=p<<16>>16;if(p<<16>>16<0)d=~((d^-4)>>2);else d=d>>>2;Ve[c>>1]=d;if((u|0)>0)u=u+-1|0;else break}Bt(o,Be,t+(k<<1)|0,40,ee,1)}Xt(e|0,re|0,308)|0;Ve[D>>1]=Ve[Te>>1]|0;d=k+40|0;h=d&65535;if(h<<16>>16>=160)break;else{k=d<<16>>16;o=o+22|0;w=v}}Ve[M>>1]=$e(e+484|0,e+466|0,t,N,Ne)|0;pr(Oe,Ae,t,Ne);Ve[j>>1]=Me;Ve[W>>1]=a;Ar(e+626|0,Ae,Ne);He=_e;Ge[He>>2]=Le;Ke=Ye;return}function sr(e,r,i,n,t,o,a,f,s){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;var l=0,u=0,c=0,d=0,h=0;h=Ke;Ke=Ke+16|0;c=h+2|0;d=h;i=i<<16>>16;i=(i<<18>>18|0)==(i|0)?i<<2:i>>>15^32767;switch(r|0){case 3:case 4:case 6:{u=i<<16>>16;i=Ge[f+84>>2]|0;Ve[o>>1]=Ve[i+(u<<1)>>1]|0;f=Ve[i+(u+1<<1)>>1]|0;l=Ve[i+(u+3<<1)>>1]|0;o=Ve[i+(u+2<<1)>>1]|0;break}case 0:{f=(i&65535)+(t<<16>>16<<1^2)|0;f=(f&65535)<<16>>16>1022?1022:f<<16>>16;Ve[o>>1]=Ve[782+(f<<1)>>1]|0;o=Ve[782+(f+1<<1)>>1]|0;ft(o<<16>>16,d,c,s);Ve[d>>1]=(We[d>>1]|0)+65524;f=Tt(Ve[c>>1]|0,5,s)|0;u=Ve[d>>1]|0;u=Gn(f,((u<<26>>26|0)==(u|0)?u<<10:u>>>15^32767)&65535,s)|0;f=Ve[c>>1]|0;i=Ve[d>>1]|0;if((i*24660|0)==1073741824){Ge[s>>2]=1;t=2147483647}else t=i*49320|0;l=(f<<16>>16)*24660>>15;i=t+(l<<1)|0;if((t^l|0)>0&(i^t|0)<0){Ge[s>>2]=1;i=(t>>>31)+2147483647|0}l=i<<13;f=o;l=Ft((l>>13|0)==(i|0)?l:i>>31^2147483647,s)|0;o=u;break}default:{u=i<<16>>16;i=Ge[f+80>>2]|0;Ve[o>>1]=Ve[i+(u<<1)>>1]|0;f=Ve[i+(u+1<<1)>>1]|0;l=Ve[i+(u+3<<1)>>1]|0;o=Ve[i+(u+2<<1)>>1]|0}}Qn(e,r,n,d,c,0,0,s);t=Ze((gt(14,Ve[c>>1]|0,s)|0)<<16>>16,f<<16>>16)|0;if((t|0)==1073741824){Ge[s>>2]=1;i=2147483647}else i=t<<1;f=10-(We[d>>1]|0)|0;t=f&65535;f=f<<16>>16;if(t<<16>>16>0){d=t<<16>>16<31?i>>f:0;d=d>>>16;d=d&65535;Ve[a>>1]=d;Jn(e,o,l);Ke=h;return}else{s=0-f<<16>>16;d=i<<s;d=(d>>s|0)==(i|0)?d:i>>31^2147483647;d=d>>>16;d=d&65535;Ve[a>>1]=d;Jn(e,o,l);Ke=h;return}}function lr(e,r,i,n,t,o,a,f,s){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;if(!(n<<16>>16)){f=e<<16>>16;if(e<<16>>16>=197){Ve[o>>1]=f+65424;Ve[a>>1]=0;return}t=((f<<16)+131072>>16)*10923>>15;if((t|0)>32767){Ge[s>>2]=1;t=32767}e=(t&65535)+19|0;Ve[o>>1]=e;Ve[a>>1]=f+58-((e*196608|0)>>>16);return}if(!(f<<16>>16)){s=e<<16>>16<<16;e=((s+131072>>16)*21846|0)+-65536>>16;Ve[o>>1]=e+(r&65535);Ve[a>>1]=((s+-131072|0)>>>16)-((e*196608|0)>>>16);return}if((Ct(t,r,s)|0)<<16>>16>5)t=(r&65535)+5&65535;f=i<<16>>16;f=(f-(t&65535)&65535)<<16>>16>4?f+65532&65535:t;t=e<<16>>16;if(e<<16>>16<4){Ve[o>>1]=((((f&65535)<<16)+-327680|0)>>>16)+t;Ve[a>>1]=0;return}t=t<<16;if(e<<16>>16<12){s=(((t+-327680>>16)*10923|0)>>>15<<16)+-65536|0;e=s>>16;Ve[o>>1]=(f&65535)+e;Ve[a>>1]=((t+-589824|0)>>>16)-(s>>>15)-e;return}else{Ve[o>>1]=((t+-786432+((f&65535)<<16)|0)>>>16)+1;Ve[a>>1]=0;return}}function ur(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;if(n<<16>>16){a=(We[t>>1]|0)+65531|0;a=(a<<16>>16|0)<(r<<16>>16|0)?r:a&65535;i=i<<16>>16;r=e<<16>>16<<16;e=((r+327680>>16)*10924|0)+-65536>>16;Ve[t>>1]=(((((a&65535)<<16)+589824>>16|0)>(i|0)?i+65527&65535:a)&65535)+e;Ve[o>>1]=((r+-196608|0)>>>16)-((e*393216|0)>>>16);return}n=e<<16>>16;if(e<<16>>16<463){e=((((n<<16)+327680>>16)*10924|0)>>>16)+17|0;Ve[t>>1]=e;Ve[o>>1]=n+105-((e*393216|0)>>>16);return}else{Ve[t>>1]=n+65168;Ve[o>>1]=0;return}}function cr(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0;u=Ke;Ke=Ke+16|0;s=u+6|0;f=u+4|0;Qn(e,r,n,s,f,u+2|0,u,a);l=(i&31)*3|0;n=t+(l<<1)|0;if(!((Ct(r&65535,7,a)|0)<<16>>16)){s=gt(Ve[s>>1]|0,Ve[f>>1]|0,a)|0;f=s<<16>>16;f=(Ze(((s<<20>>20|0)==(f|0)?s<<4:f>>>15^32767)<<16>>16,Ve[n>>1]|0)|0)>>15;if((f|0)>32767){Ge[a>>2]=1;f=32767}n=f<<16;i=n>>16;if((f<<17>>17|0)==(i|0))f=n>>15;else f=i>>>15^32767}else{i=gt(14,Ve[f>>1]|0,a)|0;i=Ze(i<<16>>16,Ve[n>>1]|0)|0;if((i|0)==1073741824){Ge[a>>2]=1;n=2147483647}else n=i<<1;i=Ct(9,Ve[s>>1]|0,a)|0;f=i<<16>>16;if(i<<16>>16>0)f=i<<16>>16<31?n>>f:0;else{a=0-f<<16>>16;f=n<<a;f=(f>>a|0)==(n|0)?f:n>>31^2147483647}f=f>>>16}Ve[o>>1]=f;Jn(e,Ve[t+(l+1<<1)>>1]|0,Ve[t+(l+2<<1)>>1]|0);Ke=u;return}function dr(e,r,i){e=e|0;r=r|0;i=i|0;r=Ve[i+(r<<16>>16<<1)>>1]|0;if((e|0)!=7){e=r;return e|0}e=r&65532;return e|0}function hr(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0;v=Ke;Ke=Ke+48|0;h=v+20|0;p=v;m=Ge[t+44>>2]|0;w=Ge[t+64>>2]|0;f=Ge[t+4>>2]|0;d=Ge[t+12>>2]|0;l=Ge[t+20>>2]|0;s=Ge[t+56>>2]|0;if(!(i<<16>>16)){u=r>>>0<2;if(u){i=765;c=508;l=Ge[t+52>>2]|0}else{t=(r|0)==5;i=t?1533:765;c=2044;f=t?s:f}s=Ve[n>>1]|0;i=((s*196608>>16|0)>(i&65535|0)?i:s*3&65535)<<16>>16;s=Ve[f+(i<<1)>>1]|0;Ve[h>>1]=s;Ve[h+2>>1]=Ve[f+(i+1<<1)>>1]|0;Ve[h+4>>1]=Ve[f+(i+2<<1)>>1]|0;i=Ve[n+2>>1]|0;if(u)i=i<<16>>16<<1&65535;u=(i<<16>>16)*196608|0;u=(u|0)>100466688?1533:u>>16;Ve[h+6>>1]=Ve[d+(u<<1)>>1]|0;Ve[h+8>>1]=Ve[d+(u+1<<1)>>1]|0;Ve[h+10>>1]=Ve[d+(u+2<<1)>>1]|0;n=Ve[n+4>>1]|0;n=((n<<18>>16|0)>(c&65535|0)?c:n<<2&65535)<<16>>16;Ve[h+12>>1]=Ve[l+(n<<1)>>1]|0;Ve[h+14>>1]=Ve[l+((n|1)<<1)>>1]|0;Ve[h+16>>1]=Ve[l+((n|2)<<1)>>1]|0;Ve[h+18>>1]=Ve[l+((n|3)<<1)>>1]|0;if((r|0)==8){i=0;while(1){w=e+(i<<1)|0;Ve[p+(i<<1)>>1]=Gn(s,Gn(Ve[m+(i<<1)>>1]|0,Ve[w>>1]|0,a)|0,a)|0;Ve[w>>1]=s;i=i+1|0;if((i|0)==10)break;s=Ve[h+(i<<1)>>1]|0}Ot(p,205,10,a);f=e+20|0;s=p;i=f+20|0;do{Xe[f>>0]=Xe[s>>0]|0;f=f+1|0;s=s+1|0}while((f|0)<(i|0));pt(p,o,10,a);Ke=v;return}else f=0;do{s=e+(f<<1)|0;i=(Ze(Ve[w+(f<<1)>>1]|0,Ve[s>>1]|0)|0)>>15;if((i|0)>32767){Ge[a>>2]=1;i=32767}n=Gn(Ve[m+(f<<1)>>1]|0,i&65535,a)|0;r=Ve[h+(f<<1)>>1]|0;Ve[p+(f<<1)>>1]=Gn(r,n,a)|0;Ve[s>>1]=r;f=f+1|0}while((f|0)!=10);Ot(p,205,10,a);f=e+20|0;s=p;i=f+20|0;do{Xe[f>>0]=Xe[s>>0]|0;f=f+1|0;s=s+1|0}while((f|0)<(i|0));pt(p,o,10,a);Ke=v;return}else{f=0;do{i=(Ve[e+20+(f<<1)>>1]|0)*29491>>15;if((i|0)>32767){Ge[a>>2]=1;i=32767}s=(Ve[m+(f<<1)>>1]|0)*3277>>15;if((s|0)>32767){Ge[a>>2]=1;s=32767}Ve[p+(f<<1)>>1]=Gn(s&65535,i&65535,a)|0;f=f+1|0}while((f|0)!=10);if((r|0)==8){f=0;do{w=e+(f<<1)|0;h=Gn(Ve[m+(f<<1)>>1]|0,Ve[w>>1]|0,a)|0;Ve[w>>1]=Ct(Ve[p+(f<<1)>>1]|0,h,a)|0;f=f+1|0}while((f|0)!=10);Ot(p,205,10,a);f=e+20|0;s=p;i=f+20|0;do{Xe[f>>0]=Xe[s>>0]|0;f=f+1|0;s=s+1|0}while((f|0)<(i|0));pt(p,o,10,a);Ke=v;return}else f=0;do{s=e+(f<<1)|0;i=(Ze(Ve[w+(f<<1)>>1]|0,Ve[s>>1]|0)|0)>>15;if((i|0)>32767){Ge[a>>2]=1;i=32767}h=Gn(Ve[m+(f<<1)>>1]|0,i&65535,a)|0;Ve[s>>1]=Ct(Ve[p+(f<<1)>>1]|0,h,a)|0;f=f+1|0}while((f|0)!=10);Ot(p,205,10,a);f=e+20|0;s=p;i=f+20|0;do{Xe[f>>0]=Xe[s>>0]|0;f=f+1|0;s=s+1|0}while((f|0)<(i|0));pt(p,o,10,a);Ke=v;return}}function Pe(e,r,i){e=e|0;r=r|0;i=i|0;Xt(e|0,i+((r<<16>>16)*10<<1)|0,20)|0;return}function wr(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0;v=Ke;Ke=Ke+80|0;d=v+60|0;h=v+40|0;m=v+20|0;p=v;w=Ge[n+48>>2]|0;l=Ge[n+24>>2]|0;u=Ge[n+28>>2]|0;c=Ge[n+32>>2]|0;if(r<<16>>16){f=0;do{d=w+(f<<1)|0;i=Gn(((Ve[d>>1]|0)*1639|0)>>>15&65535,((Ve[e+20+(f<<1)>>1]|0)*31128|0)>>>15&65535,a)|0;Ve[m+(f<<1)>>1]=i;Ve[p+(f<<1)>>1]=i;h=e+(f<<1)|0;Ve[h>>1]=Ct(i,Gn(Ve[d>>1]|0,((Ve[h>>1]|0)*21299|0)>>>15&65535,a)|0,a)|0;f=f+1|0}while((f|0)!=10);Ot(m,205,10,a);Ot(p,205,10,a);f=e+20|0;n=p;r=f+20|0;do{Xe[f>>0]=Xe[n>>0]|0;f=f+1|0;n=n+1|0}while((f|0)<(r|0));pt(m,t,10,a);pt(p,o,10,a);Ke=v;return}r=Ge[n+16>>2]|0;n=Ge[n+8>>2]|0;s=Ve[i>>1]|0;s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16;Ve[d>>1]=Ve[n+(s<<1)>>1]|0;Ve[d+2>>1]=Ve[n+(s+1<<1)>>1]|0;Ve[h>>1]=Ve[n+(s+2<<1)>>1]|0;Ve[h+2>>1]=Ve[n+(s+3<<1)>>1]|0;s=Ve[i+2>>1]|0;s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16;Ve[d+4>>1]=Ve[r+(s<<1)>>1]|0;Ve[d+6>>1]=Ve[r+(s+1<<1)>>1]|0;Ve[h+4>>1]=Ve[r+(s+2<<1)>>1]|0;Ve[h+6>>1]=Ve[r+(s+3<<1)>>1]|0;s=Ve[i+4>>1]|0;n=s<<16>>16;if(s<<16>>16<0)r=~((n^-2)>>1);else r=n>>>1;s=r<<16>>16;s=((r<<18>>18|0)==(s|0)?r<<2:s>>>15^32767)<<16>>16;f=l+(s+1<<1)|0;r=Ve[l+(s<<1)>>1]|0;if(!(n&1)){Ve[d+8>>1]=r;Ve[d+10>>1]=Ve[f>>1]|0;Ve[h+8>>1]=Ve[l+(s+2<<1)>>1]|0;Ve[h+10>>1]=Ve[l+(s+3<<1)>>1]|0}else{if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[d+8>>1]=r;r=Ve[f>>1]|0;if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[d+10>>1]=r;r=Ve[l+(s+2<<1)>>1]|0;if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[h+8>>1]=r;r=Ve[l+(s+3<<1)>>1]|0;if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[h+10>>1]=r}f=Ve[i+6>>1]|0;f=((f<<18>>18|0)==(f|0)?f<<2:f>>>15^32767)<<16>>16;Ve[d+12>>1]=Ve[u+(f<<1)>>1]|0;Ve[d+14>>1]=Ve[u+(f+1<<1)>>1]|0;Ve[h+12>>1]=Ve[u+(f+2<<1)>>1]|0;Ve[h+14>>1]=Ve[u+(f+3<<1)>>1]|0;f=Ve[i+8>>1]|0;f=((f<<18>>18|0)==(f|0)?f<<2:f>>>15^32767)<<16>>16;Ve[d+16>>1]=Ve[c+(f<<1)>>1]|0;Ve[d+18>>1]=Ve[c+(f+1<<1)>>1]|0;Ve[h+16>>1]=Ve[c+(f+2<<1)>>1]|0;Ve[h+18>>1]=Ve[c+(f+3<<1)>>1]|0;f=0;do{n=e+(f<<1)|0;r=(Ve[n>>1]|0)*21299>>15;if((r|0)>32767){Ge[a>>2]=1;r=32767}c=Gn(Ve[w+(f<<1)>>1]|0,r&65535,a)|0;Ve[m+(f<<1)>>1]=Gn(Ve[d+(f<<1)>>1]|0,c,a)|0;i=Ve[h+(f<<1)>>1]|0;Ve[p+(f<<1)>>1]=Gn(i,c,a)|0;Ve[n>>1]=i;f=f+1|0}while((f|0)!=10);Ot(m,205,10,a);Ot(p,205,10,a);f=e+20|0;n=p;r=f+20|0;do{Xe[f>>0]=Xe[n>>0]|0;f=f+1|0;n=n+1|0}while((f|0)<(r|0));pt(m,t,10,a);pt(p,o,10,a);Ke=v;return}function Ce(e,r){e=e|0;r=r|0;var i=0,n=0;if(!e){n=-1;return n|0}i=e;n=i+20|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Xt(e+20|0,r|0,20)|0;n=0;return n|0}function Be(e){e=e|0;var r=0,i=0,n=0,t=0,o=0;if(!e){o=-1;return o|0}Ve[e>>1]=0;Ve[e+2>>1]=8192;r=e+4|0;Ve[r>>1]=3500;Ve[e+6>>1]=3500;Ge[e+8>>2]=1887529304;Ve[e+12>>1]=3e4;Ve[e+14>>1]=26e3;Ve[e+16>>1]=21e3;Ve[e+18>>1]=15e3;Ve[e+20>>1]=8e3;Ve[e+22>>1]=0;Ve[e+24>>1]=-8e3;Ve[e+26>>1]=-15e3;Ve[e+28>>1]=-21e3;Ve[e+30>>1]=-26e3;Ve[e+32>>1]=3e4;Ve[e+34>>1]=26e3;Ve[e+36>>1]=21e3;Ve[e+38>>1]=15e3;Ve[e+40>>1]=8e3;Ve[e+42>>1]=0;Ve[e+44>>1]=-8e3;Ve[e+46>>1]=-15e3;Ve[e+48>>1]=-21e3;Ve[e+50>>1]=-26e3;Ve[e+212>>1]=0;Ve[e+374>>1]=0;Ve[e+392>>1]=0;i=e+52|0;Ve[i>>1]=1384;Ve[e+54>>1]=2077;Ve[e+56>>1]=3420;Ve[e+58>>1]=5108;Ve[e+60>>1]=6742;Ve[e+62>>1]=8122;Ve[e+64>>1]=9863;Ve[e+66>>1]=11092;Ve[e+68>>1]=12714;Ve[e+70>>1]=13701;n=e+72|0;t=i;o=n+20|0;do{Xe[n>>0]=Xe[t>>0]|0;n=n+1|0;t=t+1|0}while((n|0)<(o|0));n=e+92|0;t=i;o=n+20|0;do{Xe[n>>0]=Xe[t>>0]|0;n=n+1|0;t=t+1|0}while((n|0)<(o|0));n=e+112|0;t=i;o=n+20|0;do{Xe[n>>0]=Xe[t>>0]|0;n=n+1|0;t=t+1|0}while((n|0)<(o|0));n=e+132|0;t=i;o=n+20|0;do{Xe[n>>0]=Xe[t>>0]|0;n=n+1|0;t=t+1|0}while((n|0)<(o|0));n=e+152|0;t=i;o=n+20|0;do{Xe[n>>0]=Xe[t>>0]|0;n=n+1|0;t=t+1|0}while((n|0)<(o|0));n=e+172|0;t=i;o=n+20|0;do{Xe[n>>0]=Xe[t>>0]|0;n=n+1|0;t=t+1|0}while((n|0)<(o|0));n=e+192|0;t=i;o=n+20|0;do{Xe[n>>0]=Xe[t>>0]|0;n=n+1|0;t=t+1|0}while((n|0)<(o|0));Vt(e+214|0,0,160)|0;Ve[e+376>>1]=3500;Ve[e+378>>1]=3500;o=Ve[r>>1]|0;Ve[e+380>>1]=o;Ve[e+382>>1]=o;Ve[e+384>>1]=o;Ve[e+386>>1]=o;Ve[e+388>>1]=o;Ve[e+390>>1]=o;Ve[e+394>>1]=0;Ve[e+396>>1]=7;Ve[e+398>>1]=32767;Ve[e+400>>1]=0;Ve[e+402>>1]=0;Ve[e+404>>1]=0;Ge[e+408>>2]=1;Ve[e+412>>1]=0;o=0;return o|0}function mr(e,r,i,n,t,o,a,f,s,l,u,c){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0;V=Ke;Ke=Ke+304|0;B=V+192|0;T=V+168|0;U=V+148|0;H=V+216|0;z=V+146|0;j=V+144|0;P=V+124|0;C=V+104|0;x=V+84|0;q=V+60|0;F=V+40|0;L=V;X=e+404|0;Y=e+400|0;if((Ve[X>>1]|0)!=0?(Ve[Y>>1]|0)!=0:0){O=e+394|0;Ve[O>>1]=Ve[636+(a<<1)>>1]|0;E=Ve[e+212>>1]|0;k=E+10|0;Xt(e+52+(((k&65535|0)==80?0:k<<16>>16)<<1)|0,e+52+(E<<1)|0,20)|0;E=Ve[e+392>>1]|0;k=E+1|0;Ve[e+376+(((k&65535|0)==8?0:k<<16>>16)<<1)>>1]=Ve[e+376+(E<<1)>>1]|0;k=e+4|0;Ve[k>>1]=0;E=L+36|0;g=L+32|0;y=L+28|0;_=L+24|0;A=L+20|0;D=L+16|0;S=L+12|0;R=L+8|0;M=L+4|0;N=e+52|0;w=L;I=w+40|0;do{Ge[w>>2]=0;w=w+4|0}while((w|0)<(I|0));h=0;d=7;while(1){I=Ve[e+376+(d<<1)>>1]|0;b=I<<16>>16;if(I<<16>>16<0)b=~((b^-8)>>3);else b=b>>>3;h=Gn(h,b&65535,c)|0;Ve[k>>1]=h;p=d*10|0;w=9;while(1){m=L+(w<<2)|0;v=Ge[m>>2]|0;I=Ve[e+52+(w+p<<1)>>1]|0;b=I+v|0;if((I^v|0)>-1&(b^v|0)<0){Ge[c>>2]=1;b=(v>>>31)+2147483647|0}Ge[m>>2]=b;if((w|0)>0)w=w+-1|0;else break}if((d|0)<=0)break;else d=d+-1|0}Ve[F+18>>1]=(Ge[E>>2]|0)>>>3;Ve[F+16>>1]=(Ge[g>>2]|0)>>>3;Ve[F+14>>1]=(Ge[y>>2]|0)>>>3;Ve[F+12>>1]=(Ge[_>>2]|0)>>>3;Ve[F+10>>1]=(Ge[A>>2]|0)>>>3;Ve[F+8>>1]=(Ge[D>>2]|0)>>>3;Ve[F+6>>1]=(Ge[S>>2]|0)>>>3;Ve[F+4>>1]=(Ge[R>>2]|0)>>>3;Ve[F+2>>1]=(Ge[M>>2]|0)>>>3;Ve[F>>1]=(Ge[L>>2]|0)>>>3;pt(F,e+12|0,10,c);Ve[k>>1]=Ct(Ve[k>>1]|0,Ve[O>>1]|0,c)|0;Yt(e+214|0,N|0,160)|0;F=9;while(1){I=Ve[e+214+(F+70<<1)>>1]|0;m=I<<16>>16;L=Ve[e+214+(F+60<<1)>>1]|0;w=(L<<16>>16)+m|0;if((L^I)<<16>>16>-1&(w^m|0)<0){Ge[c>>2]=1;w=(m>>>31)+2147483647|0}I=Ve[e+214+(F+50<<1)>>1]|0;m=I+w|0;if((I^w|0)>-1&(m^w|0)<0){Ge[c>>2]=1;m=(w>>>31)+2147483647|0}I=Ve[e+214+(F+40<<1)>>1]|0;w=I+m|0;if((I^m|0)>-1&(w^m|0)<0){Ge[c>>2]=1;w=(m>>>31)+2147483647|0}I=Ve[e+214+(F+30<<1)>>1]|0;m=I+w|0;if((I^w|0)>-1&(m^w|0)<0){Ge[c>>2]=1;m=(w>>>31)+2147483647|0}I=Ve[e+214+(F+20<<1)>>1]|0;w=I+m|0;if((I^m|0)>-1&(w^m|0)<0){Ge[c>>2]=1;w=(m>>>31)+2147483647|0}I=Ve[e+214+(F+10<<1)>>1]|0;m=I+w|0;if((I^w|0)>-1&(m^w|0)<0){Ge[c>>2]=1;w=(w>>>31)+2147483647|0}else w=m;I=Ve[e+214+(F<<1)>>1]|0;m=I+w|0;if((I^w|0)>-1&(m^w|0)<0){Ge[c>>2]=1;m=(w>>>31)+2147483647|0}if((m|0)<0)m=~((m^-8)>>3);else m=m>>>3;b=m&65535;p=Ve[654+(F<<1)>>1]|0;v=7;while(1){d=e+214+((v*10|0)+F<<1)|0;m=Ct(Ve[d>>1]|0,b,c)|0;Ve[d>>1]=m;m=(Ze(p,m<<16>>16)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[d>>1]=m;h=(m&65535)-(m>>>15&1)|0;h=h<<16>>31^h;w=h&65535;if(w<<16>>16>655)w=(((h<<16>>16)+261489|0)>>>2)+655&65535;w=w<<16>>16>1310?1310:w;if(!(m&32768))m=w;else m=0-(w&65535)&65535;Ve[d>>1]=m;if((v|0)>0)v=v+-1|0;else break}if((F|0)>0)F=F+-1|0;else break}}if(Ve[Y>>1]|0){b=e+32|0;v=e+12|0;w=b;p=v;I=w+20|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));p=e+4|0;h=Ve[p>>1]|0;d=e+6|0;Ve[d>>1]=h;do{if(Ve[e+402>>1]|0){w=Ve[e>>1]|0;Ve[e>>1]=0;w=w<<16>>16<32?w:32;I=w<<16>>16;m=I<<10;if((m|0)!=(I<<26>>16|0)){Ge[c>>2]=1;m=w<<16>>16>0?32767:-32768}if(w<<16>>16>1)m=Kn(1024,m&65535)|0;else m=16384;Ve[e+2>>1]=m;Pe(i,Ve[f>>1]|0,Ge[s+60>>2]|0);hr(i,8,0,f+2|0,s,v,c);w=i;I=w+20|0;do{Xe[w>>0]=0;w=w+1|0}while((w|0)<(I|0));h=Ve[f+8>>1]|0;h=h<<16>>16==0?-32768:((h+64&65535)>127?h<<16>>16>0?32767:32768:h<<16>>16<<9)+60416&65535;Ve[p>>1]=h;if((Ve[e+412>>1]|0)!=0?(Ge[e+408>>2]|0)!=0:0)break;w=b;p=v;I=w+20|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));Ve[d>>1]=h}}while(0);w=h<<16>>16;if(h<<16>>16<0)w=~((w^-2)>>1);else w=w>>>1;w=w+56536|0;m=w<<16;if((m|0)>0)w=0;else w=(m|0)<-946077696?-14436:w&65535;Ve[n>>1]=w;Ve[n+2>>1]=w;Ve[n+4>>1]=w;Ve[n+6>>1]=w;f=((w<<16>>16)*5443|0)>>>15&65535;Ve[n+8>>1]=f;Ve[n+10>>1]=f;Ve[n+12>>1]=f;Ve[n+14>>1]=f}w=((Ve[636+(a<<1)>>1]|0)*104864|0)>>>15<<16;if((w|0)<0)w=~((w>>16^-32)>>5);else w=w>>21;a=e+394|0;Ve[a>>1]=Gn(((Ve[a>>1]|0)*29491|0)>>>15&65535,w&65535,c)|0;n=(We[e>>1]<<16)+65536|0;w=n>>16;s=e+2|0;w=(Ze(((n<<10>>26|0)==(w|0)?n>>>6:w>>>15^32767)<<16>>16,Ve[s>>1]|0)|0)>>15;if((w|0)>32767){Ge[c>>2]=1;w=32767}h=w&65535;if(h<<16>>16<=1024)if(h<<16>>16<-2048)v=-32768;else v=w<<4&65535;else v=16384;f=e+4|0;b=v<<16>>16;m=Ze(Ve[f>>1]|0,b)|0;if((m|0)==1073741824){Ge[c>>2]=1;F=2147483647}else F=m<<1;m=(Ze(Ve[e+30>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}k=m&65535;Ve[B+18>>1]=k;m=(Ze(Ve[e+28>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+16>>1]=m;m=(Ze(Ve[e+26>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+14>>1]=m;m=(Ze(Ve[e+24>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+12>>1]=m;m=(Ze(Ve[e+22>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+10>>1]=m;m=(Ze(Ve[e+20>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+8>>1]=m;m=(Ze(Ve[e+18>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+6>>1]=m;m=(Ze(Ve[e+16>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+4>>1]=m;m=(Ze(Ve[e+14>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B+2>>1]=m;m=(Ze(Ve[e+12>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[B>>1]=m;n=e+6|0;b=16384-(v&65535)<<16>>16;m=Ze(Ve[n>>1]|0,b)|0;if((m|0)!=1073741824){w=(m<<1)+F|0;if((m^F|0)>0&(w^F|0)<0){Ge[c>>2]=1;L=(F>>>31)+2147483647|0}else L=w}else{Ge[c>>2]=1;L=2147483647}w=k;p=9;while(1){h=B+(p<<1)|0;m=(Ze(Ve[e+32+(p<<1)>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}w=Gn(w,m&65535,c)|0;Ve[h>>1]=w;I=w<<16>>16;m=I<<1;if((m|0)!=(I<<17>>16|0)){Ge[c>>2]=1;m=w<<16>>16>0?32767:-32768}Ve[h>>1]=m;m=p+-1|0;if((p|0)<=0)break;w=Ve[B+(m<<1)>>1]|0;p=m}F=e+374|0;m=((We[F>>1]<<16)+-161021952>>16)*9830>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}m=4096-(m&65535)|0;w=m<<16;if((w|0)>268369920)b=32767;else b=(w|0)<0?0:m<<19>>16;O=e+8|0;m=Me(O,3)|0;vt(B,P,10,c);w=C;p=P;I=w+20|0;do{Ve[w>>1]=Ve[p>>1]|0;w=w+2|0;p=p+2|0}while((w|0)<(I|0));w=(m<<16>>16)*10|0;p=9;while(1){h=C+(p<<1)|0;d=Ve[h>>1]|0;m=(Ze(Ve[e+214+(p+w<<1)>>1]|0,b)|0)>>15;if((m|0)>32767){Ge[c>>2]=1;m=32767}Ve[h>>1]=Gn(d,m&65535,c)|0;if((p|0)>0)p=p+-1|0;else break}Ot(P,205,10,c);Ot(C,205,10,c);w=i+20|0;p=P;I=w+20|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));pt(P,B,10,c);pt(C,x,10,c);ct(B,T,c);ct(x,q,c);w=u;p=T;I=w+22|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));w=u+22|0;p=T;I=w+22|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));w=u+44|0;p=T;I=w+22|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));w=u+66|0;p=T;I=w+22|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));Re(T+2|0,U,c);m=0;w=32767;do{h=Ve[U+(m<<1)>>1]|0;h=Ze(h,h)|0;if(h>>>0<1073741824)h=32767-(h>>>15)|0;else{Ge[c>>2]=1;h=0}w=(Ze(h<<16>>16,w<<16>>16)|0)>>15;if((w|0)>32767){Ge[c>>2]=1;w=32767}m=m+1|0}while((m|0)!=10);ft(w<<16>>16,z,j,c);w=(We[z>>1]<<16)+-983040|0;h=w>>16;h=It(Ct(0,Gn(((w<<12>>28|0)==(h|0)?w>>>4:h>>>15^32767)&65535,It(Ve[j>>1]|0,3,c)|0,c)|0,c)|0,1,c)|0;w=(Ve[F>>1]|0)*29491>>15;if((w|0)>32767){Ge[c>>2]=1;w=32767}m=h<<16>>16;h=m*3277>>15;if((h|0)>32767){Ge[c>>2]=1;h=32767}Ve[F>>1]=Gn(w&65535,h&65535,c)|0;h=L>>10;d=h+262144|0;if((h|0)>-1&(d^h|0)<0){Ge[c>>2]=1;d=(h>>>31)+2147483647|0}j=m<<4;h=d-j|0;if(((h^d)&(d^j)|0)<0){Ge[c>>2]=1;d=(d>>>31)+2147483647|0}else d=h;j=Ve[a>>1]<<5;h=j+d|0;if((j^d|0)>-1&(h^d|0)<0){Ge[c>>2]=1;h=(d>>>31)+2147483647|0}m=(gt(h>>>16&65535,h>>>1&32767,c)|0)<<16>>16;Ne(O,H,c);d=39;while(1){w=H+(d<<1)|0;h=(Ze(Ve[w>>1]|0,m)|0)>>15;if((h|0)>32767){Ge[c>>2]=1;h=32767}Ve[w>>1]=h;if((d|0)>0)d=d+-1|0;else break}Bt(q,H,l,40,r,1);Ne(O,H,c);d=39;while(1){w=H+(d<<1)|0;h=(Ze(Ve[w>>1]|0,m)|0)>>15;if((h|0)>32767){Ge[c>>2]=1;h=32767}Ve[w>>1]=h;if((d|0)>0)d=d+-1|0;else break}Bt(q,H,l+80|0,40,r,1);Ne(O,H,c);d=39;while(1){w=H+(d<<1)|0;h=(Ze(Ve[w>>1]|0,m)|0)>>15;if((h|0)>32767){Ge[c>>2]=1;h=32767}Ve[w>>1]=h;if((d|0)>0)d=d+-1|0;else break}Bt(q,H,l+160|0,40,r,1);Ne(O,H,c);w=39;while(1){d=H+(w<<1)|0;h=(Ze(Ve[d>>1]|0,m)|0)>>15;if((h|0)>32767){Ge[c>>2]=1;h=32767}Ve[d>>1]=h;if((w|0)>0)w=w+-1|0;else break}Bt(q,H,l+240|0,40,r,1);Ve[t+14>>1]=20;Ve[t+16>>1]=0;if((o|0)==2){h=Ve[e>>1]|0;h=h<<16>>16>32?32:h<<16>>16<1?8:h;l=h<<16>>16;d=l<<10;if((d|0)!=(l<<26>>16|0)){Ge[c>>2]=1;d=h<<16>>16>0?32767:-32768}Ve[s>>1]=Kn(1024,d&65535)|0;Ve[e>>1]=0;w=e+32|0;p=e+12|0;I=w+20|0;do{Xe[w>>0]=Xe[p>>0]|0;w=w+1|0;p=p+1|0}while((w|0)<(I|0));c=Ve[f>>1]|0;Ve[n>>1]=c;Ve[f>>1]=(c&65535)+65280}if(!(Ve[Y>>1]|0)){Ke=V;return}do{if(!(Ve[e+402>>1]|0)){if(Ve[X>>1]|0)break;Ke=V;return}}while(0);Ve[e>>1]=0;Ve[e+412>>1]=1;Ke=V;return}function pr(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0;s=Ke;Ke=Ke+16|0;a=s+2|0;f=s;Ve[f>>1]=0;o=e+212|0;t=(We[o>>1]|0)+10|0;t=(t&65535|0)==80?0:t&65535;Ve[o>>1]=t;Xt(e+52+(t<<16>>16<<1)|0,r|0,20)|0;t=0;o=159;while(1){l=Ve[i+(o<<1)>>1]|0;l=Ze(l,l)|0;l=(l|0)==1073741824?2147483647:l<<1;r=l+t|0;if((l^t|0)>-1&(r^t|0)<0){Ge[n>>2]=1;t=(t>>>31)+2147483647|0}else t=r;if((o|0)>0)o=o+-1|0;else break}ft(t,a,f,n);t=Ve[a>>1]|0;l=t<<16>>16;r=l<<10;if((r|0)!=(l<<26>>16|0)){Ge[n>>2]=1;r=t<<16>>16>0?32767:-32768}Ve[a>>1]=r;l=Ve[f>>1]|0;t=l<<16>>16;if(l<<16>>16<0)t=~((t^-32)>>5);else t=t>>>5;f=e+392|0;l=(We[f>>1]|0)+1|0;l=(l&65535|0)==8?0:l&65535;Ve[f>>1]=l;Ve[e+376+(l<<16>>16<<1)>>1]=t+57015+r;Ke=s;return}function vr(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0;s=(r|0)==4;l=(r|0)==5;u=(r|0)==6;n=Ge[e+408>>2]|0;e:do{if((r+-4|0)>>>0<3)f=4;else{if((n+-1|0)>>>0<2)switch(r|0){case 2:case 3:case 7:{f=4;break e}default:{}}Ve[e>>1]=0;a=0}}while(0);if((f|0)==4){e:do{if((n|0)==2){switch(r|0){case 2:case 4:case 6:case 7:break;default:{t=1;break e}}t=2}else t=1}while(0);a=(We[e>>1]|0)+1&65535;Ve[e>>1]=a;a=(r|0)!=5&a<<16>>16>50?2:t}o=e+398|0;if(l&(Ve[e+412>>1]|0)==0){Ve[o>>1]=0;t=0}else t=Ve[o>>1]|0;t=Gn(t,1,i)|0;Ve[o>>1]=t;i=e+404|0;Ve[i>>1]=0;e:do{switch(r|0){case 2:case 4:case 5:case 6:case 7:{if(!((r|0)==7&(a|0)==0)){if(t<<16>>16>30){Ve[i>>1]=1;Ve[o>>1]=0;Ve[e+396>>1]=0;break e}t=e+396|0;n=Ve[t>>1]|0;if(!(n<<16>>16)){Ve[o>>1]=0;break e}else{Ve[t>>1]=(n&65535)+65535;break e}}else f=14;break}default:f=14}}while(0);if((f|0)==14)Ve[e+396>>1]=7;if(!a)return a|0;t=e+400|0;Ve[t>>1]=0;n=e+402|0;Ve[n>>1]=0;if(s){Ve[t>>1]=1;return a|0}if(l){Ve[t>>1]=1;Ve[n>>1]=1;return a|0}if(!u)return a|0;Ve[t>>1]=1;Ve[i>>1]=0;return a|0}function xe(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=1;Ve[e+2>>1]=1;Ve[e+4>>1]=1;Ve[e+6>>1]=1;Ve[e+8>>1]=1;Ve[e+10>>1]=0;Ve[e+12>>1]=1;e=0;return e|0}function br(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0;s=Ke;Ke=Ke+16|0;f=s+2|0;a=s;o=rt(e,5)|0;e=e+10|0;if((Ct(o,Ve[e>>1]|0,t)|0)<<16>>16>0)o=Ve[e>>1]|0;o=(Ze(Ve[674+(i<<16>>16<<1)>>1]|0,o<<16>>16)|0)>>15;if((o|0)>32767){Ge[t>>2]=1;o=32767}Ve[n>>1]=o;$n(r,f,a,t);Jn(r,Ve[f>>1]|0,Ve[a>>1]|0);Ke=s;return}function kr(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;if(!(r<<16>>16)){if(i<<16>>16){r=e+12|0;if((Ct(Ve[n>>1]|0,Ve[r>>1]|0,t)|0)<<16>>16>0)Ve[n>>1]=Ve[r>>1]|0}else r=e+12|0;Ve[r>>1]=Ve[n>>1]|0}Ve[e+10>>1]=Ve[n>>1]|0;t=e+2|0;Ve[e>>1]=Ve[t>>1]|0;i=e+4|0;Ve[t>>1]=Ve[i>>1]|0;t=e+6|0;Ve[i>>1]=Ve[t>>1]|0;e=e+8|0;Ve[t>>1]=Ve[e>>1]|0;Ve[e>>1]=Ve[n>>1]|0;return}function Er(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0;t=rt(e,5)|0;e=e+10|0;if((Ct(t,Ve[e>>1]|0,n)|0)<<16>>16>0)t=Ve[e>>1]|0;t=(Ze(Ve[688+(r<<16>>16<<1)>>1]|0,t<<16>>16)|0)>>15;if((t|0)<=32767){n=t;n=n&65535;Ve[i>>1]=n;return}Ge[n>>2]=1;n=32767;n=n&65535;Ve[i>>1]=n;return}function Ue(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=1640;Ve[e+2>>1]=1640;Ve[e+4>>1]=1640;Ve[e+6>>1]=1640;Ve[e+8>>1]=1640;Ve[e+10>>1]=0;Ve[e+12>>1]=16384;e=0;return e|0}function gr(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;if(!(r<<16>>16)){if(i<<16>>16){r=e+12|0;if((Ct(Ve[n>>1]|0,Ve[r>>1]|0,t)|0)<<16>>16>0)Ve[n>>1]=Ve[r>>1]|0}else r=e+12|0;Ve[r>>1]=Ve[n>>1]|0}n=Ve[n>>1]|0;r=e+10|0;Ve[r>>1]=n;if((Ct(n,16384,t)|0)<<16>>16>0){Ve[r>>1]=16384;r=16384}else r=Ve[r>>1]|0;t=e+2|0;Ve[e>>1]=Ve[t>>1]|0;n=e+4|0;Ve[t>>1]=Ve[n>>1]|0;t=e+6|0;Ve[n>>1]=Ve[t>>1]|0;e=e+8|0;Ve[t>>1]=Ve[e>>1]|0;Ve[e>>1]=r;return}function yr(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0;s=rt(i,9)|0;l=Ve[i+16>>1]|0;f=l<<16>>16;i=(f+(Ve[i+14>>1]|0)|0)>>>1;i=(f|0)<(i<<16>>16|0)?l:i&65535;if(!(r<<16>>16>5?s<<16>>16>r<<16>>16:0))return 0;f=i<<16>>16;f=((f<<18>>18|0)==(f|0)?f<<2:f>>>15^32767)&65535;if(!(n<<16>>16>6&t<<16>>16==0))f=Ct(f,i,a)|0;s=s<<16>>16>f<<16>>16?f:s;l=Et(r)|0;f=l<<16>>16;if(l<<16>>16<0){i=0-f<<16;if((i|0)<983040)f=r<<16>>16>>(i>>16)&65535;else f=0}else{i=r<<16>>16;t=i<<f;if((t<<16>>16>>f|0)==(i|0))f=t&65535;else f=(i>>>15^32767)&65535}n=Ze((Kn(16383,f)|0)<<16>>16,s<<16>>16)|0;if((n|0)==1073741824){Ge[a>>2]=1;t=2147483647}else t=n<<1;n=Ct(20,l,a)|0;f=n<<16>>16;if(n<<16>>16>0)n=n<<16>>16<31?t>>f:0;else{r=0-f<<16>>16;n=t<<r;n=(n>>r|0)==(t|0)?n:t>>31^2147483647}n=(n|0)>32767?32767:n&65535;n=o<<16>>16!=0&n<<16>>16>3072?3072:n<<16>>16;i=0;do{t=e+(i<<1)|0;f=Ze(Ve[t>>1]|0,n)|0;if((f|0)==1073741824){Ge[a>>2]=1;f=2147483647}else f=f<<1;Ve[t>>1]=f>>>11;i=i+1|0}while((i|0)!=40);return 0}function ze(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0;t=Ge[n+104>>2]|0;o=Ge[n+96>>2]|0;if(e>>>0>=8){Ve[i>>1]=(s[r>>0]|0)>>>4&1;Ve[i+2>>1]=(s[r>>0]|0)>>>5&1;Ve[i+4>>1]=(s[r>>0]|0)>>>6&1;Ve[i+6>>1]=(s[r>>0]|0)>>>7&255;t=t+(e<<1)|0;if((Ve[t>>1]|0)>1){e=1;n=1;o=4}else return;while(1){a=r+e|0;e=o|1;Ve[i+(o<<16>>16<<1)>>1]=s[a>>0]&1;Ve[i+(e<<16>>16<<1)>>1]=(s[a>>0]|0)>>>1&1;f=o|3;Ve[i+(e+1<<16>>16<<16>>16<<1)>>1]=(s[a>>0]|0)>>>2&1;Ve[i+(f<<16>>16<<1)>>1]=(s[a>>0]|0)>>>3&1;Ve[i+(f+1<<16>>16<<16>>16<<1)>>1]=(s[a>>0]|0)>>>4&1;Ve[i+(f+2<<16>>16<<16>>16<<1)>>1]=(s[a>>0]|0)>>>5&1;Ve[i+(f+3<<16>>16<<16>>16<<1)>>1]=(s[a>>0]|0)>>>6&1;Ve[i+(f+4<<16>>16<<16>>16<<1)>>1]=(s[a>>0]|0)>>>7&255;n=n+1<<16>>16;if(n<<16>>16<(Ve[t>>1]|0)){e=n<<16>>16;o=o+8<<16>>16}else break}return}f=Ge[(Ge[n+100>>2]|0)+(e<<2)>>2]|0;Ve[i+(Ve[f>>1]<<1)>>1]=(s[r>>0]|0)>>>4&1;Ve[i+(Ve[f+2>>1]<<1)>>1]=(s[r>>0]|0)>>>5&1;Ve[i+(Ve[f+4>>1]<<1)>>1]=(s[r>>0]|0)>>>6&1;Ve[i+(Ve[f+6>>1]<<1)>>1]=(s[r>>0]|0)>>>7&255;a=t+(e<<1)|0;if((Ve[a>>1]|0)<=1)return;n=o+(e<<1)|0;t=1;e=1;o=4;while(1){t=r+t|0;o=o<<16>>16;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=s[t>>0]&1;o=o+1|0;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=(s[t>>0]|0)>>>1&1;o=o+1|0;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=(s[t>>0]|0)>>>2&1;o=o+1|0;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=(s[t>>0]|0)>>>3&1;o=o+1|0;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=(s[t>>0]|0)>>>4&1;o=o+1|0;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=(s[t>>0]|0)>>>5&1;o=o+1|0;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=(s[t>>0]|0)>>>6&1;o=o+1|0;if((o|0)<(Ve[n>>1]|0)){Ve[i+(Ve[f+(o<<1)>>1]<<1)>>1]=(s[t>>0]|0)>>>7&1;o=o+1|0}}}}}}}}e=e+1<<16>>16;if(e<<16>>16<(Ve[a>>1]|0))t=e<<16>>16;else break}return}function _r(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0;switch(i<<16>>16){case 0:{s=9;while(1){f=Ve[e+(s<<1)>>1]|0;i=f<<16>>16;if(f<<16>>16<0)i=~((i^-4)>>2);else i=i>>>2;a=Ve[r+(s<<1)>>1]|0;o=a<<16>>16;if(a<<16>>16<0)a=~((o^-4)>>2);else a=o>>>2;Ve[n+(s<<1)>>1]=Gn((f&65535)-i&65535,a&65535,t)|0;if((s|0)>0)s=s+-1|0;else break}return}case 40:{a=9;while(1){t=Ve[e+(a<<1)>>1]|0;i=t<<16>>16;if(t<<16>>16<0)o=~((i^-2)>>1);else o=i>>>1;t=Ve[r+(a<<1)>>1]|0;i=t<<16>>16;if(t<<16>>16<0)i=~((i^-2)>>1);else i=i>>>1;Ve[n+(a<<1)>>1]=i+o;if((a|0)>0)a=a+-1|0;else break}return}case 80:{s=9;while(1){f=Ve[e+(s<<1)>>1]|0;i=f<<16>>16;if(f<<16>>16<0)f=~((i^-4)>>2);else f=i>>>2;i=Ve[r+(s<<1)>>1]|0;o=i<<16>>16;if(i<<16>>16<0)a=~((o^-4)>>2);else a=o>>>2;Ve[n+(s<<1)>>1]=Gn(f&65535,(i&65535)-a&65535,t)|0;if((s|0)>0)s=s+-1|0;else break}return}case 120:{Ve[n+18>>1]=Ve[r+18>>1]|0;Ve[n+16>>1]=Ve[r+16>>1]|0;Ve[n+14>>1]=Ve[r+14>>1]|0;Ve[n+12>>1]=Ve[r+12>>1]|0;Ve[n+10>>1]=Ve[r+10>>1]|0;Ve[n+8>>1]=Ve[r+8>>1]|0;Ve[n+6>>1]=Ve[r+6>>1]|0;Ve[n+4>>1]=Ve[r+4>>1]|0;Ve[n+2>>1]=Ve[r+2>>1]|0;Ve[n>>1]=Ve[r>>1]|0;return}default:return}}function je(e,r){e=e|0;r=r|0;if(!e){e=-1;return e|0}Xt(e|0,r|0,20)|0;e=0;return e|0}function Ar(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0,l=0;l=0;do{s=e+(l<<1)|0;n=Ve[s>>1]|0;a=n&65535;f=a<<16;n=n<<16>>16;if((n*5243|0)==1073741824){Ge[i>>2]=1;o=2147483647}else o=n*10486|0;t=f-o|0;if(((t^f)&(o^f)|0)<0){Ge[i>>2]=1;o=(a>>>15)+2147483647|0}else o=t;n=Ve[r+(l<<1)>>1]|0;t=n*5243|0;if((t|0)!=1073741824){n=(n*10486|0)+o|0;if((t^o|0)>0&(n^o|0)<0){Ge[i>>2]=1;n=(o>>>31)+2147483647|0}}else{Ge[i>>2]=1;n=2147483647}Ve[s>>1]=Ft(n,i)|0;l=l+1|0}while((l|0)!=10);return}function qe(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+18|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function Dr(e){e=e|0;Ve[e+14>>1]=1;return}function Sr(e){e=e|0;Ve[e+14>>1]=0;return}function Rr(e,r,i,n,t,o,a,f,s,l){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;var u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0;D=Ke;Ke=Ke+160|0;y=D+80|0;_=D;b=Ge[s+120>>2]|0;k=Ge[s+124>>2]|0;E=Ge[s+128>>2]|0;v=Ge[s+132>>2]|0;c=e+6|0;p=e+8|0;Ve[p>>1]=Ve[c>>1]|0;w=e+4|0;Ve[c>>1]=Ve[w>>1]|0;m=e+2|0;Ve[w>>1]=Ve[m>>1]|0;Ve[m>>1]=Ve[e>>1]|0;Ve[e>>1]=t;s=t<<16>>16<14746?t<<16>>16>9830&1:2;u=e+12|0;t=Ve[u>>1]|0;d=t<<15;do{if((d|0)<=536870911)if((d|0)<-536870912){Ge[l>>2]=1;t=-2147483648;break}else{t=t<<17;break}else{Ge[l>>2]=1;t=2147483647}}while(0);g=n<<16>>16;h=e+16|0;if((Ft(t,l)|0)<<16>>16>=n<<16>>16){d=Ve[h>>1]|0;if(d<<16>>16>0){d=(d&65535)+65535&65535;Ve[h>>1]=d}if(!(d<<16>>16)){t=(Ve[e>>1]|0)<9830;t=(Ve[m>>1]|0)<9830?t?2:1:t&1;if((Ve[w>>1]|0)<9830)t=(t&65535)+1&65535;if((Ve[c>>1]|0)<9830)t=(t&65535)+1&65535;if((Ve[p>>1]|0)<9830)t=(t&65535)+1&65535;d=0;s=t<<16>>16>2?0:s}}else{Ve[h>>1]=2;d=2}m=s<<16>>16;p=e+10|0;m=(d<<16>>16==0?(m|0)>((Ve[p>>1]|0)+1|0):0)?m+65535&65535:s;e=(Ve[e+14>>1]|0)==1?0:n<<16>>16<10?2:m<<16>>16<2&d<<16>>16>0?(m&65535)+1&65535:m;Ve[p>>1]=e;Ve[u>>1]=n;switch(r|0){case 4:case 6:case 7:break;default:if(e<<16>>16<2){d=0;s=0;c=o;u=y;while(1){if(!(Ve[c>>1]|0))t=0;else{s=s<<16>>16;Ve[_+(s<<1)>>1]=d;t=Ve[c>>1]|0;s=s+1&65535}Ve[u>>1]=t;Ve[c>>1]=0;d=d+1<<16>>16;if(d<<16>>16>=40){p=s;break}else{c=c+2|0;u=u+2|0}}m=e<<16>>16==0;m=(r|0)==5?m?b:k:m?E:v;if(p<<16>>16>0){w=0;do{h=Ve[_+(w<<1)>>1]|0;s=h<<16>>16;e=Ve[y+(s<<1)>>1]|0;if(h<<16>>16<40){d=e<<16>>16;c=39-h&65535;u=h;s=o+(s<<1)|0;t=m;while(1){r=(Ze(Ve[t>>1]|0,d)|0)>>>15&65535;Ve[s>>1]=Gn(Ve[s>>1]|0,r,l)|0;u=u+1<<16>>16;if(u<<16>>16>=40)break;else{s=s+2|0;t=t+2|0}}if(h<<16>>16>0){s=m+(c+1<<1)|0;A=36}}else{s=m;A=36}if((A|0)==36){A=0;t=e<<16>>16;d=0;c=o;while(1){r=(Ze(Ve[s>>1]|0,t)|0)>>>15&65535;Ve[c>>1]=Gn(Ve[c>>1]|0,r,l)|0;d=d+1<<16>>16;if(d<<16>>16>=h<<16>>16)break;else{c=c+2|0;s=s+2|0}}}w=w+1|0}while((w&65535)<<16>>16!=p<<16>>16)}}}w=a<<16>>16;m=g<<1;t=f<<16>>16;u=0-t<<16;s=u>>16;if(f<<16>>16>0){d=0;c=i;while(1){e=Ze(Ve[i+(d<<1)>>1]|0,w)|0;if((e|0)==1073741824){Ge[l>>2]=1;u=2147483647}else u=e<<1;f=Ze(m,Ve[o>>1]|0)|0;e=f+u|0;if((f^u|0)>-1&(e^u|0)<0){Ge[l>>2]=1;e=(u>>>31)+2147483647|0}f=e<<t;Ve[c>>1]=Ft((f>>t|0)==(e|0)?f:e>>31^2147483647,l)|0;d=d+1|0;if((d|0)==40)break;else{o=o+2|0;c=c+2|0}}Ke=D;return}if((u|0)<2031616){d=0;c=i;while(1){e=Ze(Ve[i+(d<<1)>>1]|0,w)|0;if((e|0)==1073741824){Ge[l>>2]=1;u=2147483647}else u=e<<1;f=Ze(m,Ve[o>>1]|0)|0;e=f+u|0;if((f^u|0)>-1&(e^u|0)<0){Ge[l>>2]=1;e=(u>>>31)+2147483647|0}Ve[c>>1]=Ft(e>>s,l)|0;d=d+1|0;if((d|0)==40)break;else{o=o+2|0;c=c+2|0}}Ke=D;return}else{c=0;u=i;while(1){e=Ze(Ve[i+(c<<1)>>1]|0,w)|0;if((e|0)==1073741824){Ge[l>>2]=1;e=2147483647}else e=e<<1;f=Ze(m,Ve[o>>1]|0)|0;if((f^e|0)>-1&(f+e^e|0)<0)Ge[l>>2]=1;Ve[u>>1]=Ft(0,l)|0;c=c+1|0;if((c|0)==40)break;else{o=o+2|0;u=u+2|0}}Ke=D;return}}function He(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;e=0;return e|0}function Ye(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0;if(i<<16>>16<=0)return;t=e+10|0;s=e+8|0;u=e+4|0;c=e+6|0;d=e+2|0;o=Ve[u>>1]|0;a=Ve[c>>1]|0;f=Ve[e>>1]|0;l=Ve[d>>1]|0;h=0;while(1){w=Ve[t>>1]|0;m=Ve[s>>1]|0;Ve[t>>1]=m;p=Ve[r>>1]|0;Ve[s>>1]=p;w=((p<<16>>16)*7699|0)+((Ze(f<<16>>16,-7667)|0)+(((o<<16>>16)*15836|0)+((a<<16>>16)*15836>>15))+((Ze(l<<16>>16,-7667)|0)>>15))+(Ze(m<<16>>16,-15398)|0)+((w<<16>>16)*7699|0)|0;m=w<<3;w=(m>>3|0)==(w|0)?m:w>>31^2147483647;m=w<<1;Ve[r>>1]=Ft((m>>1|0)==(w|0)?m:w>>31^2147483647,n)|0;f=Ve[u>>1]|0;Ve[e>>1]=f;l=Ve[c>>1]|0;Ve[d>>1]=l;o=w>>>16&65535;Ve[u>>1]=o;a=(w>>>1)-(w>>16<<15)&65535;Ve[c>>1]=a;h=h+1<<16>>16;if(h<<16>>16>=i<<16>>16)break;else r=r+2|0}return}function Mr(e){e=e|0;if(!e)e=-1;else{Ve[e>>1]=0;e=0}return e|0}function Nr(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0;f=n<<16>>16;o=r+(f+-1<<1)|0;f=f+-2|0;s=Ve[o>>1]|0;if(n<<16>>16<2)n=i<<16>>16;else{n=i<<16>>16;a=0;r=r+(f<<1)|0;while(1){i=(Ze(Ve[r>>1]|0,n)|0)>>15;if((i|0)>32767){Ge[t>>2]=1;i=32767}Ve[o>>1]=Ct(Ve[o>>1]|0,i&65535,t)|0;o=o+-2|0;a=a+1<<16>>16;if((a<<16>>16|0)>(f|0))break;else r=r+-2|0}}n=(Ze(Ve[e>>1]|0,n)|0)>>15;if((n|0)<=32767){f=n;f=f&65535;a=Ve[o>>1]|0;t=Ct(a,f,t)|0;Ve[o>>1]=t;Ve[e>>1]=s;return}Ge[t>>2]=1;f=32767;f=f&65535;a=Ve[o>>1]|0;t=Ct(a,f,t)|0;Ve[o>>1]=t;Ve[e>>1]=s;return}function Or(e){e=e|0;var r=0,i=0,n=0;if(!e){n=-1;return n|0}Vt(e+104|0,0,340)|0;r=e+102|0;i=e;n=i+100|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Ae(r)|0;Mr(e+100|0)|0;n=0;return n|0}function Lr(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0;b=Ke;Ke=Ke+96|0;w=b+22|0;m=b;p=b+44|0;Xt(e+124|0,i|0,320)|0;u=p+22|0;c=e+100|0;d=e+80|0;h=e+102|0;if((r&-2|0)==6){l=0;while(1){xt(n,702,w);xt(n,722,m);s=e+104+(l+10<<1)|0;Lt(w,s,e,40);a=p;o=w;r=a+22|0;do{Ve[a>>1]=Ve[o>>1]|0;a=a+2|0;o=o+2|0}while((a|0)<(r|0));a=u;r=a+22|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(r|0));Bt(m,p,p,22,u,0);r=0;a=21;do{o=Ve[p+(a<<16>>16<<1)>>1]|0;o=Ze(o,o)|0;if((o|0)==1073741824){v=7;break}f=o<<1;o=f+r|0;if((f^r|0)>-1&(o^r|0)<0){Ge[t>>2]=1;r=(r>>>31)+2147483647|0}else r=o;a=a+-1<<16>>16}while(a<<16>>16>-1);if((v|0)==7){v=0;Ge[t>>2]=1}f=r>>>16&65535;o=20;r=0;a=20;while(1){o=Ze(Ve[p+(o+1<<1)>>1]|0,Ve[p+(o<<1)>>1]|0)|0;if((o|0)==1073741824){v=13;break}k=o<<1;o=k+r|0;if((k^r|0)>-1&(o^r|0)<0){Ge[t>>2]=1;r=(r>>>31)+2147483647|0}else r=o;o=(a&65535)+-1<<16>>16;if(o<<16>>16>-1){o=o<<16>>16;a=a+-1|0}else break}if((v|0)==13){v=0;Ge[t>>2]=1}r=r>>16;if((r|0)<1)r=0;else r=Kn((r*26214|0)>>>15&65535,f)|0;Nr(c,e,r,40,t);r=i+(l<<1)|0;Bt(m,e,r,40,d,1);De(h,s,r,29491,40,t);r=(l<<16)+2621440|0;if((r|0)<10485760){l=r>>16;n=n+22|0}else break}a=e+104|0;o=e+424|0;r=a+20|0;do{Xe[a>>0]=Xe[o>>0]|0;a=a+1|0;o=o+1|0}while((a|0)<(r|0));Ke=b;return}else{l=0;while(1){xt(n,742,w);xt(n,762,m);s=e+104+(l+10<<1)|0;Lt(w,s,e,40);a=p;o=w;r=a+22|0;do{Ve[a>>1]=Ve[o>>1]|0;a=a+2|0;o=o+2|0}while((a|0)<(r|0));a=u;r=a+22|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(r|0));Bt(m,p,p,22,u,0);r=0;a=21;do{o=Ve[p+(a<<16>>16<<1)>>1]|0;o=Ze(o,o)|0;if((o|0)==1073741824){v=22;break}k=o<<1;o=k+r|0;if((k^r|0)>-1&(o^r|0)<0){Ge[t>>2]=1;r=(r>>>31)+2147483647|0}else r=o;a=a+-1<<16>>16}while(a<<16>>16>-1);if((v|0)==22){v=0;Ge[t>>2]=1}f=r>>>16&65535;o=20;r=0;a=20;while(1){o=Ze(Ve[p+(o+1<<1)>>1]|0,Ve[p+(o<<1)>>1]|0)|0;if((o|0)==1073741824){v=28;break}k=o<<1;o=k+r|0;if((k^r|0)>-1&(o^r|0)<0){Ge[t>>2]=1;r=(r>>>31)+2147483647|0}else r=o;o=(a&65535)+-1<<16>>16;if(o<<16>>16>-1){o=o<<16>>16;a=a+-1|0}else break}if((v|0)==28){v=0;Ge[t>>2]=1}r=r>>16;if((r|0)<1)r=0;else r=Kn((r*26214|0)>>>15&65535,f)|0;Nr(c,e,r,40,t);r=i+(l<<1)|0;Bt(m,e,r,40,d,1);De(h,s,r,29491,40,t);r=(l<<16)+2621440|0;if((r|0)<10485760){l=r>>16;n=n+22|0}else break}a=e+104|0;o=e+424|0;r=a+20|0;do{Xe[a>>0]=Xe[o>>0]|0;a=a+1|0;o=o+1|0}while((a|0)<(r|0));Ke=b;return}}function Fr(e,r){e=e|0;r=r|0;var i=0,n=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;i=Ut(1764)|0;if(!i){e=-1;return e|0}if((Ie(i)|0)<<16>>16==0?(n=i+1748|0,(He(n)|0)<<16>>16==0):0){fr(i,0)|0;Or(i+1304|0)|0;He(n)|0;Ge[i+1760>>2]=0;Ge[e>>2]=i;e=0;return e|0}r=Ge[i>>2]|0;if(!r){e=-1;return e|0}zt(r);Ge[i>>2]=0;e=-1;return e|0}function Ir(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function Tr(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0;v=Ke;Ke=Ke+208|0;p=v+88|0;m=v;w=e+1164|0;o=Ge[e+1256>>2]|0;if((n+-5|0)>>>0<2){h=o+16|0;if((Ve[h>>1]|0)>0){d=Ge[(Ge[e+1260>>2]|0)+32>>2]|0;c=0;o=0;while(1){u=d+(c<<1)|0;s=Ve[u>>1]|0;if(s<<16>>16>0){f=i;l=0;a=0;while(1){a=We[f>>1]|a<<1&131070;l=l+1<<16>>16;if(l<<16>>16>=s<<16>>16)break;else f=f+2|0}a=a&65535}else a=0;Ve[p+(c<<1)>>1]=a;o=o+1<<16>>16;if(o<<16>>16<(Ve[h>>1]|0)){i=i+(Ve[u>>1]<<1)|0;c=o<<16>>16}else break}}}else{d=o+(r<<1)|0;if((Ve[d>>1]|0)>0){h=Ge[(Ge[e+1260>>2]|0)+(r<<2)>>2]|0;u=0;o=0;while(1){c=h+(u<<1)|0;s=Ve[c>>1]|0;if(s<<16>>16>0){f=i;l=0;a=0;while(1){a=We[f>>1]|a<<1&131070;l=l+1<<16>>16;if(l<<16>>16>=s<<16>>16)break;else f=f+2|0}a=a&65535}else a=0;Ve[p+(u<<1)>>1]=a;o=o+1<<16>>16;if(o<<16>>16<(Ve[d>>1]|0)){i=i+(Ve[c>>1]<<1)|0;u=o<<16>>16}else break}}}Te(e,r,p,n,t,m);Lr(e+1304|0,r,t,m,w);Ye(e+1748|0,t,160,w);o=0;do{e=t+(o<<1)|0;Ve[e>>1]=We[e>>1]&65528;o=o+1|0}while((o|0)!=160);Ke=v;return}function Pr(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;o=Ge[n+100>>2]|0;a=(We[(Ge[n+96>>2]|0)+(e<<1)>>1]|0)+65535|0;n=a&65535;t=n<<16>>16>-1;if(e>>>0<8){if(!t)return;o=Ge[o+(e<<2)>>2]|0;t=a<<16>>16;while(1){Ve[i+(Ve[o+(t<<1)>>1]<<1)>>1]=(s[r+(t>>3)>>0]|0)>>>(t&7^7)&1;n=n+-1<<16>>16;if(n<<16>>16>-1)t=n<<16>>16;else break}return}else{if(!t)return;t=a<<16>>16;while(1){Ve[i+(t<<1)>>1]=(s[r+(t>>3)>>0]|0)>>>(t&7^7)&1;n=n+-1<<16>>16;if(n<<16>>16>-1)t=n<<16>>16;else break}return}}function Cr(e,r,i){e=e|0;r=r|0;i=i|0;e=Dn(e,i,31764)|0;return((yn(r)|0|e)<<16>>16!=0)<<31>>31|0}function Br(e,r){e=e|0;r=r|0;Sn(e);_n(r);return}function xr(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0;c=Ke;Ke=Ke+512|0;f=c+8|0;s=c+4|0;l=c;Ge[l>>2]=0;u=a<<16>>16==3;if(!((a&65535)<2|u&1)){if(a<<16>>16!=2){t=-1;Ke=c;return t|0}Rn(e,i,n,f+2|0,l);e=Ge[l>>2]|0;Ge[o>>2]=e;An(r,e,s);r=Ge[s>>2]|0;Ve[f>>1]=r;Ve[f+490>>1]=(r|0)==3?-1:i&65535;Xe[t>>0]=r;r=1;do{f=f+1|0;Xe[t+r>>0]=Xe[f>>0]|0;r=r+1|0}while((r|0)!=492);f=492;Ke=c;return f|0}Rn(e,i,n,f,l);An(r,Ge[l>>2]|0,s);n=Ge[s>>2]|0;if((n|0)!=3){r=Ge[l>>2]|0;Ge[o>>2]=r;if((r|0)==8){switch(n|0){case 1:{Ve[f+70>>1]=0;break}case 2:{l=f+70|0;Ve[l>>1]=We[l>>1]|0|1;break}default:{}}Ve[f+72>>1]=i&1;Ve[f+74>>1]=i>>>1&1;Ve[f+76>>1]=i>>>2&1;r=8}}else{Ge[o>>2]=15;r=15}if(u){yi(r,f,t,(Ge[e+4>>2]|0)+2392|0);t=Ve[3404+(Ge[o>>2]<<16>>16<<1)>>1]|0;Ke=c;return t|0}switch(a<<16>>16){case 0:{gi(r,f,t,(Ge[e+4>>2]|0)+2392|0);t=Ve[3404+(Ge[o>>2]<<16>>16<<1)>>1]|0;Ke=c;return t|0}case 1:{Ei(r,f,t,(Ge[e+4>>2]|0)+2392|0);t=Ve[3436+(Ge[o>>2]<<16>>16<<1)>>1]|0;Ke=c;return t|0}default:{t=-1;Ke=c;return t|0}}return 0}function Ur(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0;y=Ke;Ke=Ke+480|0;g=y;o=240;l=t;s=e;f=g;a=0;while(1){E=((Ze(Ve[l>>1]|0,Ve[s>>1]|0)|0)+16384|0)>>>15;Ve[f>>1]=E;E=E<<16;a=(Ze(E>>15,E>>16)|0)+a|0;if((a|0)<0){u=4;break}o=o+-1|0;if(!((o&65535)<<16>>16)){o=0;break}else{l=l+2|0;s=s+2|0;f=f+2|0}}if((u|0)==4){a=o&65535;f=240-o|0;if(!(a<<16>>16))o=0;else{l=a;s=t+(f<<1)|0;o=e+(f<<1)|0;a=g+(f<<1)|0;while(1){Ve[a>>1]=((Ze(Ve[s>>1]|0,Ve[o>>1]|0)|0)+16384|0)>>>15;l=l+-1<<16>>16;if(!(l<<16>>16)){o=0;break}else{s=s+2|0;o=o+2|0;a=a+2|0}}}do{s=o&65535;o=120;f=g;a=0;while(1){E=(Ve[f>>1]|0)>>>2;b=f+2|0;Ve[f>>1]=E;E=E<<16>>16;E=Ze(E,E)|0;k=(Ve[b>>1]|0)>>>2;Ve[b>>1]=k;k=k<<16>>16;a=((Ze(k,k)|0)+E<<1)+a|0;o=o+-1<<16>>16;if(!(o<<16>>16))break;else f=f+4|0}o=s+4|0}while((a|0)<1)}E=a+1|0;k=(kt(E)|0)<<16>>16;E=E<<k;Ve[i>>1]=E>>>16;Ve[n>>1]=(E>>>1)-(E>>16<<15);E=g+478|0;l=r<<16>>16;if(r<<16>>16<=0){g=k-o|0;g=g&65535;Ke=y;return g|0}m=g+476|0;p=k+1|0;v=239-l|0;b=g+(236-l<<1)|0;r=l;i=i+(l<<1)|0;n=n+(l<<1)|0;while(1){u=Ze((v>>>1)+65535&65535,-2)|0;s=g+(u+236<<1)|0;u=b+(u<<1)|0;t=240-r|0;w=t+-1|0;f=g+(w<<1)|0;e=w>>>1&65535;t=g+(t+-2<<1)|0;l=Ze(Ve[E>>1]|0,Ve[f>>1]|0)|0;if(!(e<<16>>16)){u=t;s=m}else{h=m;d=E;while(1){a=f+-4|0;c=d+-4|0;l=(Ze(Ve[h>>1]|0,Ve[t>>1]|0)|0)+l|0;e=e+-1<<16>>16;l=(Ze(Ve[c>>1]|0,Ve[a>>1]|0)|0)+l|0;if(!(e<<16>>16))break;else{t=f+-6|0;h=d+-6|0;f=a;d=c}}}if(w&1)l=(Ze(Ve[s>>1]|0,Ve[u>>1]|0)|0)+l|0;w=l<<p;Ve[i>>1]=w>>>16;Ve[n>>1]=(w>>>1)-(w>>16<<15);if((r&65535)+-1<<16>>16<<16>>16>0){v=v+1|0;b=b+2|0;r=r+-1|0;i=i+-2|0;n=n+-2|0}else break}g=k-o|0;g=g&65535;Ke=y;return g|0}function zr(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0;R=Ke;Ke=Ke+3440|0;S=R+3420|0;y=R+3400|0;_=R+3224|0;D=R;E=R+3320|0;A=R+3240|0;g=R+24|0;ui(i,e,E,2,f);gn(E,r,A,_,5,y,5,f);si(i,A,g,f);kn(10,5,5,E,g,y,_,D,f);r=n;f=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(f|0));Ve[o>>1]=65535;Ve[o+2>>1]=65535;Ve[o+4>>1]=65535;Ve[o+6>>1]=65535;Ve[o+8>>1]=65535;d=0;h=D;w=S;do{e=Ve[h>>1]|0;h=h+2|0;s=(e*6554|0)>>>15;l=s<<16>>16;r=n+(e<<1)|0;f=Ve[r>>1]|0;if((Ve[A+(e<<1)>>1]|0)>0){Ve[r>>1]=f+4096;Ve[w>>1]=8192;u=s}else{Ve[r>>1]=f+61440;Ve[w>>1]=-8192;u=l+8|0}w=w+2|0;c=u&65535;r=e-(s<<2)-l<<16>>16;s=o+(r<<1)|0;f=Ve[s>>1]|0;e=f<<16>>16;do{if(f<<16>>16>=0){l=u<<16>>16;if(!((l^e)&8)){r=o+(r+5<<1)|0;if((e|0)>(l|0)){Ve[r>>1]=f;Ve[s>>1]=c;break}else{Ve[r>>1]=c;break}}else{r=o+(r+5<<1)|0;if((e&7)>>>0>(l&7)>>>0){Ve[r>>1]=c;break}else{Ve[r>>1]=f;Ve[s>>1]=c;break}}}else Ve[s>>1]=c}while(0);d=d+1<<16>>16}while(d<<16>>16<10);w=S+2|0;d=S+4|0;u=S+6|0;l=S+8|0;s=S+10|0;r=S+12|0;f=S+14|0;e=S+16|0;m=S+18|0;p=40;v=i+(0-(Ve[D>>1]|0)<<1)|0;b=i+(0-(Ve[D+2>>1]|0)<<1)|0;k=i+(0-(Ve[D+4>>1]|0)<<1)|0;E=i+(0-(Ve[D+6>>1]|0)<<1)|0;g=i+(0-(Ve[D+8>>1]|0)<<1)|0;y=i+(0-(Ve[D+10>>1]|0)<<1)|0;_=i+(0-(Ve[D+12>>1]|0)<<1)|0;A=i+(0-(Ve[D+14>>1]|0)<<1)|0;n=i+(0-(Ve[D+16>>1]|0)<<1)|0;h=i+(0-(Ve[D+18>>1]|0)<<1)|0;c=t;while(1){I=(Ze(Ve[S>>1]|0,Ve[v>>1]|0)|0)>>7;F=(Ze(Ve[w>>1]|0,Ve[b>>1]|0)|0)>>7;L=(Ze(Ve[d>>1]|0,Ve[k>>1]|0)|0)>>7;O=(Ze(Ve[u>>1]|0,Ve[E>>1]|0)|0)>>7;N=(Ze(Ve[l>>1]|0,Ve[g>>1]|0)|0)>>7;M=(Ze(Ve[s>>1]|0,Ve[y>>1]|0)|0)>>7;D=(Ze(Ve[r>>1]|0,Ve[_>>1]|0)|0)>>7;i=(Ze(Ve[f>>1]|0,Ve[A>>1]|0)|0)>>>7;t=(Ze(Ve[e>>1]|0,Ve[n>>1]|0)|0)>>>7;Ve[c>>1]=(I+128+F+L+O+N+M+D+i+t+((Ze(Ve[m>>1]|0,Ve[h>>1]|0)|0)>>>7)|0)>>>8;p=p+-1<<16>>16;if(!(p<<16>>16))break;else{v=v+2|0;b=b+2|0;k=k+2|0;E=E+2|0;g=g+2|0;y=y+2|0;_=_+2|0;A=A+2|0;n=n+2|0;h=h+2|0;c=c+2|0}}r=0;do{f=o+(r<<1)|0;e=Ve[f>>1]|0;if((r|0)<5)e=(We[a+((e&7)<<1)>>1]|e&8)&65535;else e=Ve[a+((e&7)<<1)>>1]|0;Ve[f>>1]=e;r=r+1|0}while((r|0)!=10);Ke=R;return}function jr(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0;C=Ke;Ke=Ke+3456|0;L=C+3448|0;N=C+3360|0;R=C+3368|0;d=C+3280|0;O=C+3200|0;M=C;I=(n&65535)<<17;P=i<<16>>16;F=i<<16>>16<40;if(F){n=I>>16;i=P;do{l=(Ze(Ve[r+(i-P<<1)>>1]|0,n)|0)>>15;if((l|0)>32767){Ge[f>>2]=1;l=32767}S=r+(i<<1)|0;Ve[S>>1]=Gn(Ve[S>>1]|0,l&65535,f)|0;i=i+1|0}while((i&65535)<<16>>16!=40)}ui(r,e,R,1,f);En(R,O,d,8);si(r,O,M,f);S=N+2|0;Ve[N>>1]=0;Ve[S>>1]=1;e=1;l=0;c=1;d=0;u=-1;do{A=Ve[2830+(d<<1)>>1]|0;D=A<<16>>16;_=0;do{g=Ve[2834+(_<<1)>>1]|0;y=g<<16>>16;E=e;b=D;v=c;k=A;p=u;while(1){s=Ve[R+(b<<1)>>1]|0;w=Ve[M+(b*80|0)+(b<<1)>>1]|0;i=y;c=1;m=g;e=g;u=-1;while(1){n=Gn(s,Ve[R+(i<<1)>>1]|0,f)|0;n=n<<16>>16;n=(Ze(n,n)|0)>>>15;h=(Ve[M+(b*80|0)+(i<<1)>>1]<<15)+32768+((Ve[M+(i*80|0)+(i<<1)>>1]|0)+w<<14)|0;if(((Ze(n<<16>>16,c<<16>>16)|0)-(Ze(h>>16,u<<16>>16)|0)<<1|0)>0){c=h>>>16&65535;e=m;u=n&65535}h=i+5|0;m=h&65535;if(m<<16>>16>=40)break;else i=h<<16>>16}if(((Ze(u<<16>>16,v<<16>>16)|0)-(Ze(c<<16>>16,p<<16>>16)|0)<<1|0)>0){Ve[N>>1]=k;Ve[S>>1]=e;l=k}else{e=E;c=v;u=p}h=b+5|0;k=h&65535;if(k<<16>>16>=40)break;else{E=e;b=h<<16>>16;v=c;p=u}}_=_+1|0}while((_|0)!=4);d=d+1|0}while((d|0)!=2);w=e;m=l;n=t;i=n+80|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(i|0));c=m;i=0;h=0;n=0;while(1){l=c<<16>>16;s=Ve[O+(l<<1)>>1]|0;e=(l*6554|0)>>>15;c=e<<16;d=c>>15;u=l-(d+(e<<3)<<16>>17)|0;switch(u<<16>>16|0){case 0:{d=c>>10;e=1;break}case 1:{if(!((i&65535)<<16>>16))e=0;else{d=e<<22>>16|16;e=1}break}case 2:{d=e<<22>>16|32;e=1;break}case 3:{d=e<<17>>16|1;e=0;break}case 4:{d=e<<22>>16|48;e=1;break}default:{d=e;e=u&65535}}d=d&65535;u=t+(l<<1)|0;if(s<<16>>16>0){Ve[u>>1]=8191;Ve[L+(i<<1)>>1]=32767;l=e<<16>>16;if(e<<16>>16<0){l=0-l<<16;if((l|0)<983040)l=1>>>(l>>16)&65535;else l=0}else{M=1<<l;l=(M<<16>>16>>l|0)==1?M&65535:32767}n=Gn(n,l,f)|0}else{Ve[u>>1]=-8192;Ve[L+(i<<1)>>1]=-32768}l=Gn(h,d,f)|0;i=i+1|0;if((i|0)==2){h=l;break}c=Ve[N+(i<<1)>>1]|0;h=l}Ve[a>>1]=n;d=L+2|0;c=Ve[L>>1]|0;e=0;u=r+(0-(m<<16>>16)<<1)|0;l=r+(0-(w<<16>>16)<<1)|0;do{n=Ze(Ve[u>>1]|0,c)|0;u=u+2|0;if((n|0)!=1073741824?(T=n<<1,!((n|0)>0&(T|0)<0)):0)s=T;else{Ge[f>>2]=1;s=2147483647}i=Ze(Ve[d>>1]|0,Ve[l>>1]|0)|0;l=l+2|0;if((i|0)!=1073741824){n=(i<<1)+s|0;if((i^s|0)>0&(n^s|0)<0){Ge[f>>2]=1;n=(s>>>31)+2147483647|0}}else{Ge[f>>2]=1;n=2147483647}Ve[o+(e<<1)>>1]=Ft(n,f)|0;e=e+1|0}while((e|0)!=40);if(!F){Ke=C;return h|0}i=I>>16;n=P;do{s=(Ze(Ve[t+(n-P<<1)>>1]|0,i)|0)>>15;if((s|0)>32767){Ge[f>>2]=1;s=32767}o=t+(n<<1)|0;Ve[o>>1]=Gn(Ve[o>>1]|0,s&65535,f)|0;n=n+1|0}while((n&65535)<<16>>16!=40);Ke=C;return h|0}function qr(e,r,i,n,t,o,a,f,s,l){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;var u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0;E=Ke;Ke=Ke+3456|0;w=E+3360|0;m=E+3368|0;p=E+3280|0;v=E+3200|0;b=E;k=t<<16>>16;d=k<<1;if((d|0)==(k<<17>>16|0))h=d;else{Ge[l>>2]=1;h=t<<16>>16>0?32767:-32768}k=n<<16>>16;u=n<<16>>16<40;if(u){t=h<<16>>16;c=k;do{n=i+(c<<1)|0;d=(Ze(Ve[i+(c-k<<1)>>1]|0,t)|0)>>15;if((d|0)>32767){Ge[l>>2]=1;d=32767}Ve[n>>1]=Gn(Ve[n>>1]|0,d&65535,l)|0;c=c+1|0}while((c&65535)<<16>>16!=40)}ui(i,r,m,1,l);En(m,v,p,8);si(i,v,b,l);Hr(e,m,b,s,w);d=Yr(e,w,v,o,i,a,f,l)|0;if(!u){Ke=E;return d|0}c=h<<16>>16;t=k;do{n=o+(t<<1)|0;u=(Ze(Ve[o+(t-k<<1)>>1]|0,c)|0)>>15;if((u|0)>32767){Ge[l>>2]=1;u=32767}Ve[n>>1]=Gn(Ve[n>>1]|0,u&65535,l)|0;t=t+1|0}while((t&65535)<<16>>16!=40);Ke=E;return d|0}function Hr(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0;g=t+2|0;Ve[t>>1]=0;Ve[g>>1]=1;k=e<<16>>16<<1;o=1;E=0;e=-1;do{b=(E<<3)+k<<16>>16;s=Ve[n+(b<<1)>>1]|0;b=Ve[n+((b|1)<<1)>>1]|0;a=s<<16>>16;e:do{if(s<<16>>16<40){v=b<<16>>16;if(b<<16>>16<40)p=o;else while(1){if((e<<16>>16|0)<(0-(o<<16>>16)|0)){Ve[t>>1]=s;Ve[g>>1]=b;f=1;e=-1}else f=o;o=a+5|0;s=o&65535;if(s<<16>>16>=40){o=f;break e}else{a=o<<16>>16;o=f}}while(1){w=Ve[i+(a*80|0)+(a<<1)>>1]|0;h=We[r+(a<<1)>>1]|0;d=v;o=1;m=b;f=b;l=-1;while(1){c=(We[r+(d<<1)>>1]|0)+h<<16>>16;c=(Ze(c,c)|0)>>>15;u=(Ve[i+(a*80|0)+(d<<1)>>1]<<15)+32768+((Ve[i+(d*80|0)+(d<<1)>>1]|0)+w<<14)|0;if(((Ze(c<<16>>16,o<<16>>16)|0)-(Ze(u>>16,l<<16>>16)|0)<<1|0)>0){o=u>>>16&65535;f=m;l=c&65535}u=d+5|0;m=u&65535;if(m<<16>>16>=40)break;else d=u<<16>>16}if(((Ze(l<<16>>16,p<<16>>16)|0)-(Ze(o<<16>>16,e<<16>>16)|0)<<1|0)>0){Ve[t>>1]=s;Ve[g>>1]=f;e=l}else o=p;a=a+5|0;s=a&65535;if(s<<16>>16>=40)break;else{a=a<<16>>16;p=o}}}}while(0);E=E+1|0}while((E|0)!=2);return}function Yr(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0;s=n;l=s+80|0;do{Ve[s>>1]=0;s=s+2|0}while((s|0)<(l|0));s=Ve[r>>1]|0;d=(s*6554|0)>>>15;l=d<<16>>16;c=(748250>>>((s+(Ze(l,-5)|0)<<16>>16)+((e<<16>>16)*5|0)|0)&1|0)==0;u=(Ve[i+(s<<1)>>1]|0)>0;h=u?32767:-32768;Ve[n+(s<<1)>>1]=u?8191:-8192;s=r+2|0;e=Ve[s>>1]|0;n=n+(e<<1)|0;if((Ve[i+(e<<1)>>1]|0)>0){Ve[n>>1]=8191;i=32767;n=(u&1|2)&65535}else{Ve[n>>1]=-8192;i=-32768;n=u&1}d=((e*6554|0)>>>15<<3)+(c?d:l+64|0)&65535;Ve[a>>1]=n;c=0;u=t+(0-(Ve[r>>1]|0)<<1)|0;n=t+(0-(Ve[s>>1]|0)<<1)|0;do{s=Ze(h,Ve[u>>1]|0)|0;u=u+2|0;if((s|0)==1073741824){Ge[f>>2]=1;e=2147483647}else e=s<<1;l=Ze(i,Ve[n>>1]|0)|0;n=n+2|0;if((l|0)!=1073741824){s=(l<<1)+e|0;if((l^e|0)>0&(s^e|0)<0){Ge[f>>2]=1;s=(e>>>31)+2147483647|0}}else{Ge[f>>2]=1;s=2147483647}Ve[o+(c<<1)>>1]=Ft(s,f)|0;c=c+1|0}while((c|0)!=40);return d|0}function Xr(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0;q=Ke;Ke=Ke+3440|0;T=q+3360|0;P=q+3280|0;B=q+3200|0;C=q;U=(n&65535)<<17;j=i<<16>>16;x=i<<16>>16<40;if(x){i=U>>16;s=j;do{n=(Ze(Ve[r+(s-j<<1)>>1]|0,i)|0)>>15;if((n|0)>32767){Ge[f>>2]=1;n=32767}I=r+(s<<1)|0;Ve[I>>1]=Gn(Ve[I>>1]|0,n&65535,f)|0;s=s+1|0}while((s&65535)<<16>>16!=40)}ui(r,e,T,1,f);En(T,B,P,6);si(r,B,C,f);I=1;l=2;u=1;n=0;s=1;e=-1;c=1;while(1){F=2;w=2;while(1){N=0;O=0;L=c;M=w;while(1){if(O<<16>>16<40){A=L<<16>>16;D=L<<16>>16<40;S=M<<16>>16;R=M<<16>>16<40;y=O<<16>>16;_=O;while(1){if((Ve[P+(y<<1)>>1]|0)>-1){k=Ve[C+(y*80|0)+(y<<1)>>1]|0;if(D){E=We[T+(y<<1)>>1]|0;b=A;h=1;g=L;i=L;w=0;d=-1;while(1){p=(We[T+(b<<1)>>1]|0)+E|0;v=p<<16>>16;v=(Ze(v,v)|0)>>>15;m=(Ve[C+(y*80|0)+(b<<1)>>1]<<15)+32768+((Ve[C+(b*80|0)+(b<<1)>>1]|0)+k<<14)|0;if(((Ze(v<<16>>16,h<<16>>16)|0)-(Ze(m>>16,d<<16>>16)|0)<<1|0)>0){h=m>>>16&65535;i=g;w=p&65535;d=v&65535}m=b+5|0;g=m&65535;if(g<<16>>16>=40)break;else b=m<<16>>16}}else{h=1;i=L;w=0}if(R){E=w&65535;g=i<<16>>16;b=(h<<16>>16<<14)+32768|0;v=S;w=1;k=M;d=M;h=-1;while(1){p=(We[T+(v<<1)>>1]|0)+E<<16>>16;p=(Ze(p,p)|0)>>>15;m=b+(Ve[C+(v*80|0)+(v<<1)>>1]<<12)+((Ve[C+(y*80|0)+(v<<1)>>1]|0)+(Ve[C+(g*80|0)+(v<<1)>>1]|0)<<13)|0;if(((Ze(p<<16>>16,w<<16>>16)|0)-(Ze(m>>16,h<<16>>16)|0)<<1|0)>0){w=m>>>16&65535;d=k;h=p&65535}m=v+5|0;k=m&65535;if(k<<16>>16>=40){b=w;v=h;break}else v=m<<16>>16}}else{b=1;d=M;v=-1}w=Ze(v<<16>>16,s<<16>>16)|0;if((w|0)==1073741824){Ge[f>>2]=1;m=2147483647}else m=w<<1;w=Ze(b<<16>>16,e<<16>>16)|0;if((w|0)==1073741824){Ge[f>>2]=1;h=2147483647}else h=w<<1;w=m-h|0;if(((w^m)&(h^m)|0)<0){Ge[f>>2]=1;w=(m>>>31)+2147483647|0}g=(w|0)>0;l=g?d:l;u=g?i:u;n=g?_:n;s=g?b:s;e=g?v:e}w=y+5|0;_=w&65535;if(_<<16>>16>=40)break;else y=w<<16>>16}}N=N+1<<16>>16;if(N<<16>>16>=3)break;else{R=M;M=L;L=O;O=R}}i=F+2|0;w=i&65535;if(w<<16>>16>=5)break;else F=i&65535}i=I+2|0;c=i&65535;if(c<<16>>16<4)I=i&65535;else{w=l;l=u;break}}i=t;s=i+80|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(s|0));v=n<<16>>16;e=Ve[B+(v<<1)>>1]|0;n=(v*6554|0)>>>15;i=n<<16;s=v-(((i>>16)*327680|0)>>>16)|0;switch(s<<16>>16|0){case 1:{n=i>>12;break}case 2:{n=i>>8;s=2;break}case 3:{n=n<<20>>16|8;s=1;break}case 4:{n=n<<24>>16|128;s=2;break}default:{}}i=t+(v<<1)|0;if(e<<16>>16>0){Ve[i>>1]=8191;g=32767;u=65536<<(s<<16>>16)>>>16&65535}else{Ve[i>>1]=-8192;g=-32768;u=0}m=l<<16>>16;l=Ve[B+(m<<1)>>1]|0;i=(m*6554|0)>>>15;s=i<<16;e=m-(((s>>16)*327680|0)>>>16)|0;switch(e<<16>>16|0){case 1:{i=s>>12;break}case 2:{i=s>>8;e=2;break}case 3:{i=i<<20>>16|8;e=1;break}case 4:{i=i<<24>>16|128;e=2;break}default:{}}s=t+(m<<1)|0;if(l<<16>>16>0){Ve[s>>1]=8191;p=32767;u=(65536<<(e<<16>>16)>>>16)+(u&65535)&65535}else{Ve[s>>1]=-8192;p=-32768}c=i+n|0;h=w<<16>>16;l=Ve[B+(h<<1)>>1]|0;n=(h*6554|0)>>>15;i=n<<16;s=h-(((i>>16)*327680|0)>>>16)|0;switch(s<<16>>16|0){case 1:{i=i>>12;break}case 2:{i=i>>8;s=2;break}case 3:{i=n<<20>>16|8;s=1;break}case 4:{i=n<<24>>16|128;s=2;break}default:i=n}n=t+(h<<1)|0;if(l<<16>>16>0){Ve[n>>1]=8191;w=32767;n=(65536<<(s<<16>>16)>>>16)+(u&65535)&65535}else{Ve[n>>1]=-8192;w=-32768;n=u}d=c+i|0;Ve[a>>1]=n;u=0;c=r+(0-v<<1)|0;e=r+(0-m<<1)|0;l=r+(0-h<<1)|0;do{n=Ze(Ve[c>>1]|0,g)|0;c=c+2|0;if((n|0)!=1073741824?(z=n<<1,!((n|0)>0&(z|0)<0)):0)s=z;else{Ge[f>>2]=1;s=2147483647}n=Ze(Ve[e>>1]|0,p)|0;e=e+2|0;if((n|0)!=1073741824){i=(n<<1)+s|0;if((n^s|0)>0&(i^s|0)<0){Ge[f>>2]=1;i=(s>>>31)+2147483647|0}}else{Ge[f>>2]=1;i=2147483647}s=Ze(Ve[l>>1]|0,w)|0;l=l+2|0;if((s|0)!=1073741824){n=(s<<1)+i|0;if((s^i|0)>0&(n^i|0)<0){Ge[f>>2]=1;n=(i>>>31)+2147483647|0}}else{Ge[f>>2]=1;n=2147483647}Ve[o+(u<<1)>>1]=Ft(n,f)|0;u=u+1|0}while((u|0)!=40);n=d&65535;if(!x){Ke=q;return n|0}s=U>>16;i=j;do{e=(Ze(Ve[t+(i-j<<1)>>1]|0,s)|0)>>15;if((e|0)>32767){Ge[f>>2]=1;e=32767}o=t+(i<<1)|0;Ve[o>>1]=Gn(Ve[o>>1]|0,e&65535,f)|0;i=i+1|0}while((i&65535)<<16>>16!=40);Ke=q;return n|0}function Vr(e,r,i,n,t,o,a,f,s){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;var l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0,G=0,W=0,K=0,Z=0,Q=0,J=0;J=Ke;Ke=Ke+3456|0;G=J+3448|0;X=J+3360|0;q=J+3368|0;H=J+3280|0;V=J+3200|0;Y=J;K=(n&65535)<<17;Q=i<<16>>16;W=i<<16>>16<40;if(W){i=K>>16;l=Q;do{n=(Ze(Ve[r+(l-Q<<1)>>1]|0,i)|0)>>15;if((n|0)>32767){Ge[s>>2]=1;n=32767}j=r+(l<<1)|0;Ve[j>>1]=Gn(Ve[j>>1]|0,n&65535,s)|0;l=l+1|0}while((l&65535)<<16>>16!=40)}ui(r,e,q,1,s);En(q,V,H,4);si(r,V,Y,s);U=X+2|0;Ve[X>>1]=0;z=X+4|0;Ve[U>>1]=1;j=X+6|0;Ve[z>>1]=2;Ve[j>>1]=3;h=3;c=2;u=1;n=0;i=1;l=-1;d=3;do{T=0;P=0;C=d;B=1;x=2;while(1){if(P<<16>>16<40){M=B<<16>>16;N=B<<16>>16<40;O=x<<16>>16;L=x<<16>>16<40;F=C<<16>>16;I=C<<16>>16<40;R=P<<16>>16;S=c;A=u;_=i;D=P;while(1){if((Ve[H+(R<<1)>>1]|0)>-1){m=Ve[Y+(R*80|0)+(R<<1)>>1]|0;if(N){w=We[q+(R<<1)>>1]|0;p=M;g=1;c=B;u=B;k=0;E=-1;while(1){b=(We[q+(p<<1)>>1]|0)+w|0;v=b<<16>>16;v=(Ze(v,v)|0)>>>15;y=(Ve[Y+(R*80|0)+(p<<1)>>1]<<15)+32768+((Ve[Y+(p*80|0)+(p<<1)>>1]|0)+m<<14)|0;if(((Ze(v<<16>>16,g<<16>>16)|0)-(Ze(y>>16,E<<16>>16)|0)<<1|0)>0){g=y>>>16&65535;u=c;k=b&65535;E=v&65535}y=p+5|0;c=y&65535;if(c<<16>>16>=40)break;else p=y<<16>>16}}else{g=1;u=B;k=0}if(L){i=k&65535;e=u<<16>>16;m=(g<<16>>16<<14)+32768|0;p=O;y=1;w=x;c=x;E=0;k=-1;while(1){b=(We[q+(p<<1)>>1]|0)+i|0;v=b<<16>>16;v=(Ze(v,v)|0)>>>15;g=m+(Ve[Y+(p*80|0)+(p<<1)>>1]<<12)+((Ve[Y+(R*80|0)+(p<<1)>>1]|0)+(Ve[Y+(e*80|0)+(p<<1)>>1]|0)<<13)|0;if(((Ze(v<<16>>16,y<<16>>16)|0)-(Ze(g>>16,k<<16>>16)|0)<<1|0)>0){y=g>>>16&65535;c=w;E=b&65535;k=v&65535}g=p+5|0;w=g&65535;if(w<<16>>16>=40)break;else p=g<<16>>16}}else{y=1;c=x;E=0}if(I){m=E&65535;w=c<<16>>16;e=u<<16>>16;v=(y&65535)<<16|32768;b=F;i=1;p=C;g=C;y=-1;while(1){k=(We[q+(b<<1)>>1]|0)+m<<16>>16;k=(Ze(k,k)|0)>>>15;E=(Ve[Y+(b*80|0)+(b<<1)>>1]<<12)+v+((Ve[Y+(e*80|0)+(b<<1)>>1]|0)+(Ve[Y+(w*80|0)+(b<<1)>>1]|0)+(Ve[Y+(R*80|0)+(b<<1)>>1]|0)<<13)|0;if(((Ze(k<<16>>16,i<<16>>16)|0)-(Ze(E>>16,y<<16>>16)|0)<<1|0)>0){i=E>>>16&65535;g=p;y=k&65535}E=b+5|0;p=E&65535;if(p<<16>>16>=40)break;else b=E<<16>>16}}else{i=1;g=C;y=-1}if(((Ze(y<<16>>16,_<<16>>16)|0)-(Ze(i<<16>>16,l<<16>>16)|0)<<1|0)>0){Ve[X>>1]=D;Ve[U>>1]=u;Ve[z>>1]=c;Ve[j>>1]=g;h=g;n=D;l=y}else{c=S;u=A;i=_}}else{c=S;u=A;i=_}b=R+5|0;D=b&65535;if(D<<16>>16>=40)break;else{R=b<<16>>16;S=c;A=u;_=i}}}T=T+1<<16>>16;if(T<<16>>16>=4)break;else{F=x;I=C;x=B;B=P;C=F;P=I}}d=d+1<<16>>16}while(d<<16>>16<5);y=h;g=c;E=u;k=n;n=t;i=n+80|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(i|0));e=k;i=0;l=0;n=0;while(1){c=e<<16>>16;d=Ve[V+(c<<1)>>1]|0;e=c*13108>>16;u=c-((e*327680|0)>>>16)|0;e=Ve[f+(e<<1)>>1]|0;switch(u<<16>>16|0){case 1:{h=e<<16>>16<<3&65535;break}case 2:{h=e<<16>>16<<6&65535;break}case 3:{h=e<<16>>16<<10&65535;break}case 4:{h=((e&65535)<<10|512)&65535;u=3;break}default:h=e}e=t+(c<<1)|0;if(d<<16>>16>0){Ve[e>>1]=8191;e=32767;n=(65536<<(u<<16>>16)>>>16)+(n&65535)&65535}else{Ve[e>>1]=-8192;e=-32768}Ve[G+(i<<1)>>1]=e;l=(h&65535)+(l&65535)|0;i=i+1|0;if((i|0)==4){b=l;break}e=Ve[X+(i<<1)>>1]|0}Ve[a>>1]=n;m=G+2|0;p=G+4|0;v=G+6|0;e=Ve[G>>1]|0;w=0;u=r+(0-(k<<16>>16)<<1)|0;c=r+(0-(E<<16>>16)<<1)|0;d=r+(0-(g<<16>>16)<<1)|0;h=r+(0-(y<<16>>16)<<1)|0;do{n=Ze(Ve[u>>1]|0,e)|0;u=u+2|0;if((n|0)!=1073741824?(Z=n<<1,!((n|0)>0&(Z|0)<0)):0)l=Z;else{Ge[s>>2]=1;l=2147483647}n=Ze(Ve[m>>1]|0,Ve[c>>1]|0)|0;c=c+2|0;if((n|0)!=1073741824){i=(n<<1)+l|0;if((n^l|0)>0&(i^l|0)<0){Ge[s>>2]=1;i=(l>>>31)+2147483647|0}}else{Ge[s>>2]=1;i=2147483647}n=Ze(Ve[p>>1]|0,Ve[d>>1]|0)|0;d=d+2|0;if((n|0)!=1073741824){l=(n<<1)+i|0;if((n^i|0)>0&(l^i|0)<0){Ge[s>>2]=1;l=(i>>>31)+2147483647|0}}else{Ge[s>>2]=1;l=2147483647}i=Ze(Ve[v>>1]|0,Ve[h>>1]|0)|0;h=h+2|0;if((i|0)!=1073741824){n=(i<<1)+l|0;if((i^l|0)>0&(n^l|0)<0){Ge[s>>2]=1;n=(l>>>31)+2147483647|0}}else{Ge[s>>2]=1;n=2147483647}Ve[o+(w<<1)>>1]=Ft(n,s)|0;w=w+1|0}while((w|0)!=40);n=b&65535;if(((Q<<16)+-2621440|0)>-1|W^1){Ke=J;return n|0}l=K>>16;i=Q;do{e=(Ze(Ve[t+(i-Q<<1)>>1]|0,l)|0)>>15;if((e|0)>32767){Ge[s>>2]=1;e=32767}o=t+(i<<1)|0;Ve[o>>1]=Gn(Ve[o>>1]|0,e&65535,s)|0;i=i+1|0}while((i&65535)<<16>>16!=40);Ke=J;return n|0}function Gr(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0;I=Ke;Ke=Ke+3440|0;m=I+3424|0;M=I+3408|0;N=I+3240|0;p=I+3224|0;S=I+3328|0;w=I+3248|0;R=I+24|0;F=I+16|0;L=I;li(i,e,S,2,4,4,a);gn(S,r,w,N,4,M,4,a);si(i,w,R,a);kn(8,4,4,S,R,M,N,p,a);r=n;e=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(e|0));Ve[L>>1]=-1;Ve[F>>1]=-1;A=L+2|0;Ve[A>>1]=-1;D=F+2|0;Ve[D>>1]=-1;S=L+4|0;Ve[S>>1]=-1;R=F+4|0;Ve[R>>1]=-1;N=L+6|0;Ve[N>>1]=-1;M=F+6|0;Ve[M>>1]=-1;d=0;do{u=Ve[p+(d<<1)>>1]|0;r=u>>>2;s=r&65535;e=u&3;l=(Ve[w+(u<<1)>>1]|0)>0;u=n+(u<<1)|0;h=l&1^1;Ve[u>>1]=(We[u>>1]|0)+(l?8191:57345);Ve[m+(d<<1)>>1]=l?32767:-32768;l=L+(e<<1)|0;u=Ve[l>>1]|0;do{if(u<<16>>16>=0){c=F+(e<<1)|0;f=(u<<16>>16|0)<=(r<<16>>16|0);r=L+((e|4)<<1)|0;if((h&65535|0)==(We[c>>1]&1|0))if(f){Ve[r>>1]=s;break}else{Ve[r>>1]=u;Ve[l>>1]=s;Ve[c>>1]=h;break}else if(f){Ve[r>>1]=u;Ve[l>>1]=s;Ve[c>>1]=h;break}else{Ve[r>>1]=s;break}}else{Ve[l>>1]=s;Ve[F+(e<<1)>>1]=h}}while(0);d=d+1|0}while((d|0)!=8);v=m+2|0;b=m+4|0;k=m+6|0;E=m+8|0;g=m+10|0;y=m+12|0;_=m+14|0;m=Ve[m>>1]|0;d=0;c=i+(0-(Ve[p>>1]|0)<<1)|0;u=i+(0-(Ve[p+2>>1]|0)<<1)|0;l=i+(0-(Ve[p+4>>1]|0)<<1)|0;s=i+(0-(Ve[p+6>>1]|0)<<1)|0;r=i+(0-(Ve[p+8>>1]|0)<<1)|0;e=i+(0-(Ve[p+10>>1]|0)<<1)|0;f=i+(0-(Ve[p+12>>1]|0)<<1)|0;i=i+(0-(Ve[p+14>>1]|0)<<1)|0;do{h=Ze(Ve[c>>1]|0,m)|0;c=c+2|0;if((h|0)!=1073741824?(O=h<<1,!((h|0)>0&(O|0)<0)):0)h=O;else{Ge[a>>2]=1;h=2147483647}w=Ze(Ve[v>>1]|0,Ve[u>>1]|0)|0;u=u+2|0;if((w|0)!=1073741824){n=(w<<1)+h|0;if((w^h|0)>0&(n^h|0)<0){Ge[a>>2]=1;h=(h>>>31)+2147483647|0}else h=n}else{Ge[a>>2]=1;h=2147483647}w=Ze(Ve[b>>1]|0,Ve[l>>1]|0)|0;l=l+2|0;if((w|0)!=1073741824){n=(w<<1)+h|0;if((w^h|0)>0&(n^h|0)<0){Ge[a>>2]=1;n=(h>>>31)+2147483647|0}}else{Ge[a>>2]=1;n=2147483647}w=Ze(Ve[k>>1]|0,Ve[s>>1]|0)|0;s=s+2|0;if((w|0)!=1073741824){h=(w<<1)+n|0;if((w^n|0)>0&(h^n|0)<0){Ge[a>>2]=1;h=(n>>>31)+2147483647|0}}else{Ge[a>>2]=1;h=2147483647}w=Ze(Ve[E>>1]|0,Ve[r>>1]|0)|0;r=r+2|0;if((w|0)!=1073741824){n=(w<<1)+h|0;if((w^h|0)>0&(n^h|0)<0){Ge[a>>2]=1;n=(h>>>31)+2147483647|0}}else{Ge[a>>2]=1;n=2147483647}w=Ze(Ve[g>>1]|0,Ve[e>>1]|0)|0;e=e+2|0;if((w|0)!=1073741824){h=(w<<1)+n|0;if((w^n|0)>0&(h^n|0)<0){Ge[a>>2]=1;h=(n>>>31)+2147483647|0}}else{Ge[a>>2]=1;h=2147483647}w=Ze(Ve[y>>1]|0,Ve[f>>1]|0)|0;f=f+2|0;if((w|0)!=1073741824){n=(w<<1)+h|0;if((w^h|0)>0&(n^h|0)<0){Ge[a>>2]=1;n=(h>>>31)+2147483647|0}}else{Ge[a>>2]=1;n=2147483647}w=Ze(Ve[_>>1]|0,Ve[i>>1]|0)|0;i=i+2|0;if((w|0)!=1073741824){h=(w<<1)+n|0;if((w^n|0)>0&(h^n|0)<0){Ge[a>>2]=1;h=(n>>>31)+2147483647|0}}else{Ge[a>>2]=1;h=2147483647}Ve[t+(d<<1)>>1]=Ft(h,a)|0;d=d+1|0}while((d|0)!=40);Ve[o>>1]=Ve[F>>1]|0;Ve[o+2>>1]=Ve[D>>1]|0;Ve[o+4>>1]=Ve[R>>1]|0;Ve[o+6>>1]=Ve[M>>1]|0;e=Ve[L>>1]|0;r=Ve[L+8>>1]|0;f=Ve[A>>1]|0;Ve[o+8>>1]=r<<1&2|e&1|f<<2&4|(((r>>1)*327680|0)+(e>>>1<<16)+(Ze(f>>1,1638400)|0)|0)>>>13&65528;f=Ve[S>>1]|0;e=Ve[L+12>>1]|0;r=Ve[L+10>>1]|0;Ve[o+10>>1]=e<<1&2|f&1|r<<2&4|(((e>>1)*327680|0)+(f>>>1<<16)+(Ze(r>>1,1638400)|0)|0)>>>13&65528;r=Ve[L+14>>1]|0;f=Ve[N>>1]|0;e=f<<16>>16>>>1;if(!(r&2)){t=e;a=r<<16>>16;F=a>>1;F=F*327680|0;t=t<<16;F=t+F|0;F=F<<5;F=F>>16;F=F|12;F=F*2622|0;F=F>>>16;t=f&65535;t=t&1;a=a<<17;a=a&131072;F=F<<18;a=F|a;a=a>>>16;t=a|t;t=t&65535;o=o+12|0;Ve[o>>1]=t;Ke=I;return}t=4-(e<<16>>16)|0;a=r<<16>>16;F=a>>1;F=F*327680|0;t=t<<16;F=t+F|0;F=F<<5;F=F>>16;F=F|12;F=F*2622|0;F=F>>>16;t=f&65535;t=t&1;a=a<<17;a=a&131072;F=F<<18;a=F|a;a=a>>>16;t=a|t;t=t&65535;o=o+12|0;Ve[o>>1]=t;Ke=I;return}function Wr(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0;m=i<<16>>16;o=0-m|0;i=t+(o<<2)|0;t=((m-(n<<16>>16)|0)>>>2)+1&65535;if(t<<16>>16<=0)return;m=r<<16>>16>>>1&65535;if(!(m<<16>>16)){while(1){Ge[i>>2]=0;Ge[i+4>>2]=0;Ge[i+8>>2]=0;Ge[i+12>>2]=0;if(t<<16>>16>1){i=i+16|0;t=t+-1<<16>>16}else break}return}w=e+(o<<1)|0;while(1){l=w+4|0;c=Ve[l>>1]|0;f=Ve[w>>1]|0;u=c;s=m;d=e;h=w;w=w+8|0;a=0;o=0;n=0;r=0;while(1){v=Ve[d>>1]|0;p=(Ze(f<<16>>16,v)|0)+a|0;a=Ve[h+2>>1]|0;o=(Ze(a,v)|0)+o|0;f=(Ze(u<<16>>16,v)|0)+n|0;n=Ve[h+6>>1]|0;u=(Ze(n,v)|0)+r|0;r=Ve[d+2>>1]|0;a=p+(Ze(r,a)|0)|0;o=o+(Ze(c<<16>>16,r)|0)|0;l=l+4|0;n=f+(Ze(r,n)|0)|0;f=Ve[l>>1]|0;r=u+(Ze(f<<16>>16,r)|0)|0;s=s+-1<<16>>16;if(!(s<<16>>16))break;v=c;u=f;c=Ve[h+8>>1]|0;d=d+4|0;h=h+4|0;f=v}Ge[i>>2]=a<<1;Ge[i+4>>2]=o<<1;Ge[i+8>>2]=n<<1;Ge[i+12>>2]=r<<1;if(t<<16>>16<=1)break;else{i=i+16|0;t=t+-1<<16>>16}}return}function Kr(e,r,i,n,t,o,a,f,s){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;var l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0;E=Ke;Ke=Ke+16|0;b=E+2|0;k=E;do{if(t<<16>>16>0){w=n<<16>>16;p=0;c=0;n=0;u=0;m=0;while(1){l=Ve[e+(p<<1)>>1]|0;d=l<<16>>16;c=(Ze(d,d)|0)+c|0;d=Ve[r+(p<<1)>>1]|0;n=(Ze(d,d)|0)+n|0;u=(Ze(Ve[i+(p<<1)>>1]|0,d)|0)+u|0;d=Ze(d,w)|0;if((d|0)==1073741824){Ge[s>>2]=1;h=2147483647}else h=d<<1;d=h<<1;d=(Ct(l,Ft((d>>1|0)==(h|0)?d:h>>31^2147483647,s)|0,s)|0)<<16>>16;d=Ze(d,d)|0;if((d|0)!=1073741824){l=(d<<1)+m|0;if((d^m|0)>0&(l^m|0)<0){Ge[s>>2]=1;l=(m>>>31)+2147483647|0}}else{Ge[s>>2]=1;l=2147483647}p=p+1|0;if((p&65535)<<16>>16==t<<16>>16){m=l;break}else m=l}c=c<<1;n=n<<1;u=u<<1;if((c|0)>=0){if((c|0)<400){l=m;v=14;break}}else{Ge[s>>2]=1;c=2147483647}h=kt(c)|0;d=h<<16>>16;if(h<<16>>16>0){l=c<<d;if((l>>d|0)!=(c|0))l=c>>31^2147483647}else{l=0-d<<16;if((l|0)<2031616)l=c>>(l>>16);else l=0}Ve[o>>1]=l>>>16;c=n;w=u;l=m;n=15-(h&65535)&65535}else{n=0;u=0;l=0;v=14}}while(0);if((v|0)==14){Ve[o>>1]=0;c=n;w=u;n=-15}Ve[a>>1]=n;if((c|0)<0){Ge[s>>2]=1;c=2147483647}d=kt(c)|0;u=d<<16>>16;if(d<<16>>16>0){n=c<<u;if((n>>u|0)!=(c|0))n=c>>31^2147483647}else{n=0-u<<16;if((n|0)<2031616)n=c>>(n>>16);else n=0}Ve[o+2>>1]=n>>>16;Ve[a+2>>1]=15-(d&65535);c=kt(w)|0;u=c<<16>>16;if(c<<16>>16>0){n=w<<u;if((n>>u|0)!=(w|0))n=w>>31^2147483647}else{n=0-u<<16;if((n|0)<2031616)n=w>>(n>>16);else n=0}Ve[o+4>>1]=n>>>16;Ve[a+4>>1]=2-(c&65535);c=kt(l)|0;n=c<<16>>16;if(c<<16>>16>0){u=l<<n;if((u>>n|0)!=(l|0))u=l>>31^2147483647}else{n=0-n<<16;if((n|0)<2031616)u=l>>(n>>16);else u=0}n=u>>>16&65535;l=15-(c&65535)&65535;Ve[o+6>>1]=n;Ve[a+6>>1]=l;if((u>>16|0)<=0){s=0;Ve[f>>1]=s;Ke=E;return}u=Ve[o>>1]|0;if(!(u<<16>>16)){s=0;Ve[f>>1]=s;Ke=E;return}n=Kn(It(u,1,s)|0,n)|0;n=(n&65535)<<16;u=((Ct(l,Ve[a>>1]|0,s)|0)&65535)+3|0;l=u&65535;u=u<<16>>16;if(l<<16>>16>0)l=l<<16>>16<31?n>>u:0;else{a=0-u<<16>>16;l=n<<a;l=(l>>a|0)==(n|0)?l:n>>31^2147483647}ft(l,b,k,s);k=Bi((We[b>>1]|0)+65509&65535,Ve[k>>1]|0,s)|0;b=k<<13;s=Ft((b>>13|0)==(k|0)?b:k>>31^2147483647,s)|0;Ve[f>>1]=s;Ke=E;return}function Zr(e,r,i,n,t,o,a,f,s,l,u){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;var c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0;E=Ke;Ke=Ke+80|0;v=E;Ve[a>>1]=Ve[o>>1]|0;Ve[f>>1]=Ve[o+2>>1]|0;h=Ve[o+4>>1]|0;if(h<<16>>16==-32768)h=32767;else h=0-(h&65535)&65535;Ve[a+2>>1]=h;Ve[f+2>>1]=(We[o+6>>1]|0)+1;switch(e|0){case 0:case 5:{p=0;d=0;c=0;m=0;break}default:{p=0;d=1;c=1;m=1}}while(1){w=(Ve[t+(p<<1)>>1]|0)>>>3;Ve[v+(p<<1)>>1]=w;w=w<<16>>16;h=Ze(w,w)|0;if((h|0)!=1073741824){o=(h<<1)+d|0;if((h^d|0)>0&(o^d|0)<0){Ge[u>>2]=1;d=(d>>>31)+2147483647|0}else d=o}else{Ge[u>>2]=1;d=2147483647}h=Ze(Ve[r+(p<<1)>>1]|0,w)|0;if((h|0)!=1073741824){o=(h<<1)+c|0;if((h^c|0)>0&(o^c|0)<0){Ge[u>>2]=1;c=(c>>>31)+2147483647|0}else c=o}else{Ge[u>>2]=1;c=2147483647}h=Ze(Ve[n+(p<<1)>>1]|0,w)|0;if((h|0)!=1073741824){o=(h<<1)+m|0;if((h^m|0)>0&(o^m|0)<0){Ge[u>>2]=1;o=(m>>>31)+2147483647|0}}else{Ge[u>>2]=1;o=2147483647}p=p+1|0;if((p|0)==40){n=o;w=c;break}else m=o}c=kt(d)|0;o=c<<16>>16;if(c<<16>>16>0){h=d<<o;if((h>>o|0)!=(d|0))h=d>>31^2147483647}else{h=0-o<<16;if((h|0)<2031616)h=d>>(h>>16);else h=0}t=a+4|0;Ve[t>>1]=h>>>16;r=f+4|0;Ve[r>>1]=-3-(c&65535);d=kt(w)|0;o=d<<16>>16;if(d<<16>>16>0){h=w<<o;if((h>>o|0)!=(w|0))h=w>>31^2147483647}else{h=0-o<<16;if((h|0)<2031616)h=w>>(h>>16);else h=0}o=h>>>16;Ve[a+6>>1]=(o|0)==32768?32767:0-o&65535;Ve[f+6>>1]=7-(d&65535);d=kt(n)|0;o=d<<16>>16;if(d<<16>>16>0){h=n<<o;if((h>>o|0)!=(n|0))h=n>>31^2147483647}else{h=0-o<<16;if((h|0)<2031616)h=n>>(h>>16);else h=0}Ve[a+8>>1]=h>>>16;Ve[f+8>>1]=7-(d&65535);switch(e|0){case 0:case 5:{h=0;c=0;break}default:{Ke=E;return}}do{c=(Ze(Ve[v+(h<<1)>>1]|0,Ve[i+(h<<1)>>1]|0)|0)+c|0;h=h+1|0}while((h|0)!=40);o=c<<1;h=kt(o)|0;d=h<<16>>16;if(h<<16>>16>0){c=o<<d;if((c>>d|0)==(o|0)){b=c;k=40}else{b=o>>31^2147483647;k=40}}else{c=0-d<<16;if((c|0)<2031616){b=o>>(c>>16);k=40}}if((k|0)==40?(b>>16|0)>=1:0){u=It(b>>>16&65535,1,u)|0;Ve[s>>1]=Kn(u,Ve[t>>1]|0)|0;Ve[l>>1]=65528-(h&65535)-(We[r>>1]|0);Ke=E;return}Ve[s>>1]=0;Ve[l>>1]=0;Ke=E;return}function Qr(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;o=0;t=0;do{a=Ve[e+(o<<1)>>1]|0;t=(Ze(a,a)|0)+t|0;o=o+1|0}while((o|0)!=40);if((t|0)<0){Ge[n>>2]=1;t=2147483647}n=kt(t)|0;e=n<<16>>16;if(n<<16>>16>0){o=t<<e;if((o>>e|0)==(t|0))t=o;else t=t>>31^2147483647}else{e=0-e<<16;if((e|0)<2031616)t=t>>(e>>16);else t=0}Ve[i>>1]=t>>>16;Ve[r>>1]=16-(n&65535);return}function Jr(e,r,i,n,t,o,a,f,s,l,u,c,d){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;d=d|0;var h=0,w=0,m=0,p=0;w=Ke;Ke=Ke+16|0;h=w;if(l>>>0<2){a=qr(u,e,r,i,n,a,f,h,Ge[c+76>>2]|0,d)|0;d=Ge[s>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;Ge[s>>2]=d+4;Ve[d+2>>1]=a;Ke=w;return}switch(l|0){case 2:{a=jr(e,r,i,n,a,f,h,d)|0;d=Ge[s>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;Ge[s>>2]=d+4;Ve[d+2>>1]=a;Ke=w;return}case 3:{a=Xr(e,r,i,n,a,f,h,d)|0;d=Ge[s>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;Ge[s>>2]=d+4;Ve[d+2>>1]=a;Ke=w;return}default:{if((l&-2|0)==4){a=Vr(e,r,i,n,a,f,h,Ge[c+36>>2]|0,d)|0;d=Ge[s>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;Ge[s>>2]=d+4;Ve[d+2>>1]=a;Ke=w;return}if((l|0)!=6){u=t<<16>>16;u=(u<<17>>17|0)==(u|0)?u<<1:u>>>15^32767;t=i<<16>>16<40;if(!t){zr(e,o,r,a,f,Ge[s>>2]|0,Ge[c+36>>2]|0,d);Ge[s>>2]=(Ge[s>>2]|0)+20;Ke=w;return}h=i<<16>>16;l=u<<16>>16;n=h;do{p=(Ze(Ve[r+(n-h<<1)>>1]|0,l)|0)>>>15&65535;m=r+(n<<1)|0;Ve[m>>1]=Gn(Ve[m>>1]|0,p,d)|0;n=n+1|0}while((n&65535)<<16>>16!=40);zr(e,o,r,a,f,Ge[s>>2]|0,Ge[c+36>>2]|0,d);Ge[s>>2]=(Ge[s>>2]|0)+20;if(!t){Ke=w;return}t=i<<16>>16;l=u<<16>>16;h=t;do{n=(Ze(Ve[a+(h-t<<1)>>1]|0,l)|0)>>15;if((n|0)>32767){Ge[d>>2]=1;n=32767}p=a+(h<<1)|0;Ve[p>>1]=Gn(Ve[p>>1]|0,n&65535,d)|0;h=h+1|0}while((h&65535)<<16>>16!=40);Ke=w;return}c=n<<16>>16;c=(c<<17>>17|0)==(c|0)?c<<1:c>>>15^32767;u=i<<16>>16<40;if(!u){Gr(e,o,r,a,f,Ge[s>>2]|0,d);Ge[s>>2]=(Ge[s>>2]|0)+14;Ke=w;return}h=i<<16>>16;l=c<<16>>16;n=h;do{t=(Ze(Ve[r+(n-h<<1)>>1]|0,l)|0)>>15;if((t|0)>32767){Ge[d>>2]=1;t=32767}p=r+(n<<1)|0;Ve[p>>1]=Gn(Ve[p>>1]|0,t&65535,d)|0;n=n+1|0}while((n&65535)<<16>>16!=40);Gr(e,o,r,a,f,Ge[s>>2]|0,d);Ge[s>>2]=(Ge[s>>2]|0)+14;if(!u){Ke=w;return}t=i<<16>>16;l=c<<16>>16;h=t;do{n=(Ze(Ve[a+(h-t<<1)>>1]|0,l)|0)>>15;if((n|0)>32767){Ge[d>>2]=1;n=32767}p=a+(h<<1)|0;Ve[p>>1]=Gn(Ve[p>>1]|0,n&65535,d)|0;h=h+1|0}while((h&65535)<<16>>16!=40);Ke=w;return}}}function $r(e){e=e|0;var r=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(4)|0;if(!r){e=-1;return e|0}if(!((Ki(r)|0)<<16>>16)){Zi(Ge[r>>2]|0)|0;Ge[e>>2]=r;e=0;return e|0}else{Qi(r);zt(r);e=-1;return e|0}return 0}function ei(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;Qi(r);zt(Ge[e>>2]|0);Ge[e>>2]=0;return}function ri(e){e=e|0;if(!e){e=-1;return e|0}Zi(Ge[e>>2]|0)|0;e=0;return e|0}function ii(e,r,i,n,t,o,a,f,s,l,u,c,d,h,w,m,p,v,b,k){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;p=p|0;v=v|0;b=b|0;k=k|0;var E=0,g=0,y=0,_=0;g=Ke;Ke=Ke+16|0;_=g+2|0;y=g;Ve[d>>1]=Ji(Ge[e>>2]|0,i,t,a,s,o,40,n,h,y,_,k)|0;e=Ve[_>>1]|0;n=Ge[p>>2]|0;Ge[p>>2]=n+2;Ve[n>>1]=e;yt(a,Ve[d>>1]|0,Ve[h>>1]|0,40,Ve[y>>1]|0,k);fi(a,o,c,40);Ve[w>>1]=Fi(i,s,c,m,40,k)|0;Ve[v>>1]=32767;if(l<<16>>16!=0?(E=Ve[w>>1]|0,E<<16>>16>15565):0)E=Tn(r,E,k)|0;else E=0;if(i>>>0<2){_=Ve[w>>1]|0;Ve[w>>1]=_<<16>>16>13926?13926:_;if(E<<16>>16)Ve[v>>1]=15565}else{if(E<<16>>16){Ve[v>>1]=15565;Ve[w>>1]=15565}if((i|0)==7){y=vn(7,Ve[v>>1]|0,w,0,0,b,k)|0;_=Ge[p>>2]|0;Ge[p>>2]=_+2;Ve[_>>1]=y}}d=Ve[w>>1]|0;E=0;while(1){y=Ze(Ve[c>>1]|0,d)|0;Ve[u>>1]=(We[s>>1]|0)-(y>>>14);y=(Ze(Ve[a>>1]|0,d)|0)>>>14;_=f+(E<<1)|0;Ve[_>>1]=(We[_>>1]|0)-y;E=E+1|0;if((E|0)==40)break;else{a=a+2|0;s=s+2|0;u=u+2|0;c=c+2|0}}Ke=g;return}function ni(e,r){e=e|0;r=r|0;var i=0,n=0,t=0,o=0;o=Ke;Ke=Ke+16|0;t=o;if(!e){e=-1;Ke=o;return e|0}Ge[e>>2]=0;i=Ut(2532)|0;Ge[t>>2]=i;if(!i){e=-1;Ke=o;return e|0}et(i+2392|0);Ge[i+2188>>2]=0;Ge[(Ge[t>>2]|0)+2192>>2]=0;Ge[(Ge[t>>2]|0)+2196>>2]=0;Ge[(Ge[t>>2]|0)+2200>>2]=0;Ge[(Ge[t>>2]|0)+2204>>2]=0;Ge[(Ge[t>>2]|0)+2208>>2]=0;Ge[(Ge[t>>2]|0)+2212>>2]=0;Ge[(Ge[t>>2]|0)+2220>>2]=0;n=Ge[t>>2]|0;Ge[n+2216>>2]=r;Ge[n+2528>>2]=0;i=n;if(((((((($r(n+2196|0)|0)<<16>>16==0?(dt(n+2192|0)|0)<<16>>16==0:0)?(Ri(n+2200|0)|0)<<16>>16==0:0)?(rn(n+2204|0)|0)<<16>>16==0:0)?(On(n+2208|0)|0)<<16>>16==0:0)?(Cn(n+2212|0)|0)<<16>>16==0:0)?(di(n+2220|0,Ge[n+2432>>2]|0)|0)<<16>>16==0:0)?(Yi(n+2188|0)|0)<<16>>16==0:0){oi(n)|0;Ge[e>>2]=i;e=0;Ke=o;return e|0}ti(t);e=-1;Ke=o;return e|0}function ti(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;Xi(r+2188|0);wt((Ge[e>>2]|0)+2192|0);Mi((Ge[e>>2]|0)+2200|0);ei((Ge[e>>2]|0)+2196|0);tn((Ge[e>>2]|0)+2204|0);Fn((Ge[e>>2]|0)+2208|0);xn((Ge[e>>2]|0)+2212|0);wi((Ge[e>>2]|0)+2220|0);zt(Ge[e>>2]|0);Ge[e>>2]=0;return}function oi(e){e=e|0;var r=0,i=0,n=0,t=0;if(!e){t=-1;return t|0}Ge[e+652>>2]=e+320;Ge[e+640>>2]=e+240;Ge[e+644>>2]=e+160;Ge[e+648>>2]=e+80;Ge[e+1264>>2]=e+942;Ge[e+1912>>2]=e+1590;n=e+1938|0;Ge[e+2020>>2]=n;Ge[e+2384>>2]=e+2304;r=e+2028|0;Ge[e+2024>>2]=e+2108;Ge[e+2528>>2]=0;Vt(e|0,0,640)|0;Vt(e+1282|0,0,308)|0;Vt(e+656|0,0,286)|0;i=e+2224|0;t=n+80|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));n=r;t=n+80|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));r=e+1268|0;n=i;t=n+80|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));Ve[r>>1]=40;Ve[e+1270>>1]=40;Ve[e+1272>>1]=40;Ve[e+1274>>1]=40;Ve[e+1276>>1]=40;Vi(Ge[e+2188>>2]|0)|0;ht(Ge[e+2192>>2]|0)|0;ri(Ge[e+2196>>2]|0)|0;Ni(Ge[e+2200>>2]|0)|0;nn(Ge[e+2204>>2]|0)|0;Ln(Ge[e+2208>>2]|0)|0;Bn(Ge[e+2212>>2]|0)|0;hi(Ge[e+2220>>2]|0,Ge[e+2432>>2]|0)|0;Ve[e+2388>>1]=0;t=0;return t|0}function ai(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0,G=0,W=0,K=0,Z=0,Q=0,J=0,$=0,ee=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0,fe=0,se=0,le=0,ue=0,ce=0,de=0,he=0;he=Ke;Ke=Ke+1184|0;q=he;u=he+1096|0;c=he+1008|0;s=he+904|0;fe=he+928|0;se=he+824|0;V=he+744|0;ue=he+664|0;ce=he+584|0;W=he+328|0;te=he+504|0;oe=he+424|0;le=he+344|0;de=he+248|0;G=he+168|0;ee=he+88|0;ie=he+68|0;ne=he+48|0;re=he+28|0;ae=he+24|0;J=he+22|0;Z=he+20|0;X=he+16|0;H=he+12|0;Y=he+10|0;Q=he+8|0;K=he+6|0;$=he+4|0;Ge[q>>2]=n;j=e+2528|0;a=e+652|0;Yt(Ge[a>>2]|0,i|0,320)|0;Ge[t>>2]=r;l=e+2216|0;if(!(Ge[l>>2]|0)){i=e+2220|0;n=0}else{n=Hn(Ge[e+2212>>2]|0,Ge[a>>2]|0,j)|0;z=e+2220|0;i=z;n=vi(Ge[z>>2]|0,n,t,j)|0}z=e+2392|0;Gi(Ge[e+2188>>2]|0,r,Ge[e+644>>2]|0,Ge[e+648>>2]|0,u,z,j);f=e+2192|0;mt(Ge[f>>2]|0,r,Ge[t>>2]|0,u,c,s,q,j);pi(Ge[i>>2]|0,s,Ge[a>>2]|0,j);if((Ge[t>>2]|0)==8){mi(Ge[i>>2]|0,n,Ge[(Ge[f>>2]|0)+40>>2]|0,(Ge[e+2200>>2]|0)+32|0,q,j);Vt(e+1282|0,0,308)|0;a=e+2244|0;h=a+20|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));a=e+2284|0;h=a+20|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));a=Ge[e+2020>>2]|0;h=a+80|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));a=e+2028|0;h=a+80|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));ht(Ge[f>>2]|0)|0;a=Ge[f>>2]|0;i=s;h=a+20|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));a=(Ge[f>>2]|0)+20|0;i=s;h=a+20|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));ri(Ge[e+2196>>2]|0)|0;Ve[e+2388>>1]=0;U=0}else U=In(Ge[e+2208>>2]|0,Ge[f>>2]|0,j)|0;C=e+640|0;f=e+2264|0;a=e+1264|0;i=e+2204|0;n=e+2212|0;B=e+1268|0;x=e+1278|0;an(r,2842,2862,2882,u,0,Ge[C>>2]|0,f,Ge[a>>2]|0,j);if(r>>>0>1){Wi(Ge[i>>2]|0,Ge[n>>2]|0,r,Ge[a>>2]|0,X,B,x,0,Ge[l>>2]|0,j);an(r,2842,2862,2882,u,80,Ge[C>>2]|0,f,Ge[a>>2]|0,j);Wi(Ge[i>>2]|0,Ge[n>>2]|0,r,(Ge[a>>2]|0)+160|0,X+2|0,B,x,1,Ge[l>>2]|0,j)}else{an(r,2842,2862,2882,u,80,Ge[C>>2]|0,f,Ge[a>>2]|0,j);Wi(Ge[i>>2]|0,Ge[n>>2]|0,r,Ge[a>>2]|0,X,B,x,1,Ge[l>>2]|0,j);Ve[X+2>>1]=Ve[X>>1]|0}if(Ge[l>>2]|0)qn(Ge[n>>2]|0,X,j);if((Ge[t>>2]|0)==8){ce=e+656|0;de=e+976|0;Yt(ce|0,de|0,286)|0;de=e+320|0;Yt(e|0,de|0,320)|0;Ke=he;return 0}y=e+2224|0;_=e+2244|0;A=e+2284|0;D=e+2388|0;S=e+2020|0;R=e+1916|0;M=e+1912|0;N=e+2024|0;O=e+2384|0;L=e+2196|0;F=e+2208|0;I=e+2464|0;T=e+2200|0;P=e+2224|0;k=e+2244|0;E=e+1270|0;g=e+1280|0;b=0;l=0;s=0;m=0;p=0;f=0;v=-1;while(1){d=v;v=v+1<<16>>16;m=1-(m<<16>>16)|0;n=m&65535;w=(m&65535|0)!=0;i=Ge[t>>2]|0;a=(i|0)==0;do{if(w)if(a){a=ie;i=y;h=a+20|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));a=ne;i=_;h=a+20|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));a=re;i=A;h=a+20|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));Ve[ae>>1]=Ve[D>>1]|0;r=(Ge[C>>2]|0)+(b<<1)|0;a=20;break}else{r=(Ge[C>>2]|0)+(b<<1)|0;a=19;break}else{r=(Ge[C>>2]|0)+(b<<1)|0;if(a)a=20;else a=19}}while(0);if((a|0)==19)Mn(i,2842,2862,2882,u,c,r,A,k,Ge[S>>2]|0,R,(Ge[M>>2]|0)+(b<<1)|0,Ge[N>>2]|0,fe,te,Ge[O>>2]|0);else if((a|0)==20?(0,Mn(0,2842,2862,2882,u,c,r,A,ne,Ge[S>>2]|0,R,(Ge[M>>2]|0)+(b<<1)|0,Ge[N>>2]|0,fe,te,Ge[O>>2]|0),w):0){a=ee;i=Ge[N>>2]|0;h=a+80|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0))}a=oe;i=te;h=a+80|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));ii(Ge[L>>2]|0,Ge[F>>2]|0,Ge[t>>2]|0,p,X,Ge[N>>2]|0,(Ge[M>>2]|0)+(b<<1)|0,oe,fe,U,se,ue,H,Y,Q,W,q,$,Ge[I>>2]|0,j);switch(d<<16>>16){case-1:{if((Ve[x>>1]|0)>0)Ve[E>>1]=Ve[H>>1]|0;break}case 2:{if((Ve[g>>1]|0)>0)Ve[B>>1]=Ve[H>>1]|0;break}default:{}}Jr(se,Ge[N>>2]|0,Ve[H>>1]|0,Ve[D>>1]|0,Ve[Q>>1]|0,oe,V,ce,q,Ge[t>>2]|0,v,z,j);Oi(Ge[T>>2]|0,Ge[t>>2]|0,te,(Ge[M>>2]|0)+(b<<1)|0,V,fe,se,ue,ce,W,n,Ve[$>>1]|0,J,Z,Q,K,q,z,j);Pn(Ge[F>>2]|0,Ve[Q>>1]|0,j);r=Ge[t>>2]|0;do{if(!r)if(w){a=le;i=fe;h=a+80|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));a=de;i=ce;h=a+80|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));a=G;i=V;h=a+80|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));s=Ve[H>>1]|0;l=Ve[Y>>1]|0;Nn(Ge[C>>2]|0,0,p,Ve[Q>>1]|0,Ve[K>>1]|0,c,o,fe,V,ue,ce,ie,A,ne,Ge[M>>2]|0,D,j);Ve[D>>1]=Ve[ae>>1]|0;f=p;break}else{a=A;i=re;h=a+20|0;do{Ve[a>>1]=Ve[i>>1]|0;a=a+2|0;i=i+2|0}while((a|0)<(h|0));w=f<<16>>16;yt((Ge[M>>2]|0)+(w<<1)|0,s,l,40,1,j);fi((Ge[M>>2]|0)+(w<<1)|0,ee,ue,40);Nn(Ge[C>>2]|0,Ge[t>>2]|0,f,Ve[J>>1]|0,Ve[Z>>1]|0,c+-22|0,o,le,G,ue,de,P,A,k,Ge[M>>2]|0,ae,j);Mn(Ge[t>>2]|0,2842,2862,2882,u,c,(Ge[C>>2]|0)+(b<<1)|0,A,k,Ge[S>>2]|0,R,(Ge[M>>2]|0)+(b<<1)|0,Ge[N>>2]|0,fe,te,Ge[O>>2]|0);yt((Ge[M>>2]|0)+(b<<1)|0,Ve[H>>1]|0,Ve[Y>>1]|0,40,1,j);fi((Ge[M>>2]|0)+(b<<1)|0,Ge[N>>2]|0,ue,40);Nn(Ge[C>>2]|0,Ge[t>>2]|0,p,Ve[Q>>1]|0,Ve[K>>1]|0,c,o,fe,V,ue,ce,P,A,k,Ge[M>>2]|0,D,j);break}else Nn(Ge[C>>2]|0,r,p,Ve[Q>>1]|0,Ve[K>>1]|0,c,o,fe,V,ue,ce,P,A,k,Ge[M>>2]|0,D,j)}while(0);r=b+40|0;p=r&65535;if(p<<16>>16>=160)break;else{b=r<<16>>16;u=u+22|0;c=c+22|0}}Yt(e+1282|0,e+1602|0,308)|0;ce=e+656|0;de=e+976|0;Yt(ce|0,de|0,286)|0;de=e+320|0;Yt(e|0,de|0,320)|0;Ke=he;return 0}function fi(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0;h=n<<16>>16;if(n<<16>>16>1)d=1;else return;while(1){t=Ve[e>>1]|0;f=r+(d+-1<<1)|0;n=Ze(Ve[r+(d<<1)>>1]|0,t)|0;l=Ve[f>>1]|0;t=Ze(l<<16>>16,t)|0;a=(d+131071|0)>>>1;s=a&65535;o=Ve[e+2>>1]|0;if(!(s<<16>>16)){r=f;a=l}else{u=(a<<1)+131070&131070;c=d-u|0;a=e;do{m=(Ze(l<<16>>16,o)|0)+n|0;w=a;a=a+4|0;n=Ve[f+-2>>1]|0;o=(Ze(n,o)|0)+t|0;t=Ve[a>>1]|0;f=f+-4|0;n=m+(Ze(t,n)|0)|0;l=Ve[f>>1]|0;t=o+(Ze(l<<16>>16,t)|0)|0;s=s+-1<<16>>16;o=Ve[w+6>>1]|0}while(s<<16>>16!=0);a=r+(c+-3<<1)|0;e=e+(u+2<<1)|0;r=a;a=Ve[a>>1]|0}n=(Ze(a<<16>>16,o)|0)+n|0;Ve[i>>1]=t>>>12;Ve[i+2>>1]=n>>>12;n=(d<<16)+131072>>16;if((n|0)<(h|0)){i=i+4|0;e=e+(1-d<<1)|0;d=n}else break}return}function si(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0;_=Ke;Ke=Ke+80|0;y=_;a=20;o=e;t=1;while(1){g=Ve[o>>1]|0;g=(Ze(g,g)|0)+t|0;t=Ve[o+2>>1]|0;t=g+(Ze(t,t)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else o=o+4|0}t=t<<1;if((t|0)<0){o=20;t=e;n=y;while(1){Ve[n>>1]=(Ve[t>>1]|0)>>>1;Ve[n+2>>1]=(Ve[t+2>>1]|0)>>>1;o=o+-1<<16>>16;if(!(o<<16>>16)){g=y;break}else{t=t+4|0;n=n+4|0}}}else{t=at(t>>1,n)|0;if((t|0)<16777215)t=((t>>9)*32440|0)>>>15<<16>>16;else t=32440;a=20;o=e;n=y;while(1){Ve[n>>1]=((Ze(Ve[o>>1]|0,t)|0)+32|0)>>>6;Ve[n+2>>1]=((Ze(Ve[o+2>>1]|0,t)|0)+32|0)>>>6;a=a+-1<<16>>16;if(!(a<<16>>16)){g=y;break}else{o=o+4|0;n=n+4|0}}}a=20;o=g;n=i+3198|0;t=0;while(1){E=Ve[o>>1]|0;E=(Ze(E,E)|0)+t|0;Ve[n>>1]=(E+16384|0)>>>15;k=Ve[o+2>>1]|0;t=(Ze(k,k)|0)+E|0;Ve[n+-82>>1]=(t+16384|0)>>>15;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{o=o+4|0;n=n+-164|0}}E=r+78|0;k=1;while(1){t=39-k|0;e=i+3120+(t<<1)|0;n=i+(t*80|0)+78|0;t=r+(t<<1)|0;s=y+(k<<1)|0;o=65575-k|0;f=o&65535;a=Ve[g>>1]|0;if(!(f<<16>>16)){f=E;o=0}else{m=o+65535&65535;v=m*41|0;b=(Ze(k,-40)|0)-v|0;p=0-k|0;v=p-v|0;p=p-m|0;w=k+m|0;h=Ve[s>>1]|0;c=g;d=E;l=i+((38-k|0)*80|0)+78|0;o=0;u=0;while(1){s=s+2|0;o=(Ze(h<<16>>16,a)|0)+o|0;c=c+2|0;h=Ve[s>>1]|0;u=(Ze(h<<16>>16,a)|0)+u|0;D=t;t=t+-2|0;a=Ve[t>>1]|0;A=Ve[d>>1]<<1;D=(Ze((Ze(A,Ve[D>>1]|0)|0)>>16,(o<<1)+32768>>16)|0)>>>15&65535;Ve[n>>1]=D;Ve[e>>1]=D;a=(Ze((Ze(A,a)|0)>>16,(u<<1)+32768>>16)|0)>>>15&65535;Ve[e+-2>>1]=a;Ve[l>>1]=a;f=f+-1<<16>>16;a=Ve[c>>1]|0;if(!(f<<16>>16))break;else{d=d+-2|0;e=e+-82|0;n=n+-82|0;l=l+-82|0}}s=y+(w+1<<1)|0;f=r+(38-m<<1)|0;t=r+(p+38<<1)|0;e=i+3040+(v+38<<1)|0;n=i+3040+(b+38<<1)|0}D=(Ze(Ve[s>>1]|0,a)|0)+o|0;D=(Ze((D<<1)+32768>>16,(Ze(Ve[f>>1]<<1,Ve[t>>1]|0)|0)>>16)|0)>>>15&65535;Ve[e>>1]=D;Ve[n>>1]=D;n=(k<<16)+131072|0;if((n|0)<2621440)k=n>>16;else break}Ke=_;return}function li(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0;w=Ke;Ke=Ke+160|0;h=w;if(t<<16>>16>0){c=o&65535;d=0;f=5;do{if((d|0)<40){u=d;l=d&65535;o=0;while(1){if(l<<16>>16<40){l=l<<16>>16;s=0;do{s=(Ze(Ve[e+(l-u<<1)>>1]|0,Ve[r+(l<<1)>>1]|0)|0)+s|0;l=l+1|0}while((l&65535)<<16>>16!=40)}else s=0;s=s<<1;Ge[h+(u<<2)>>2]=s;s=Pi(s)|0;o=(s|0)>(o|0)?s:o;s=u+c|0;l=s&65535;if(l<<16>>16>=40)break;else u=s<<16>>16}}else o=0;f=(o>>1)+f|0;d=d+1|0}while((d&65535)<<16>>16!=t<<16>>16)}else f=5;n=((kt(f)|0)&65535)-(n&65535)|0;o=n<<16>>16;s=0-o<<16;f=(s|0)<2031616;s=s>>16;if((n&65535)<<16>>16>0)if(f){f=0;do{n=Ge[h+(f<<2)>>2]|0;r=n<<o;Ve[i+(f<<1)>>1]=Ft((r>>o|0)==(n|0)?r:n>>31^2147483647,a)|0;f=f+1|0}while((f|0)!=40);Ke=w;return}else{f=0;do{n=Ge[h+(f<<2)>>2]|0;r=n<<o;Ve[i+(f<<1)>>1]=Ft((r>>o|0)==(n|0)?r:n>>31^2147483647,a)|0;f=f+1|0}while((f|0)!=40);Ke=w;return}else if(f){f=0;do{Ve[i+(f<<1)>>1]=Ft(Ge[h+(f<<2)>>2]>>s,a)|0;f=f+1|0}while((f|0)!=40);Ke=w;return}else{f=0;do{Ve[i+(f<<1)>>1]=Ft(0,a)|0;f=f+1|0}while((f|0)!=40);Ke=w;return}}function ui(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0;y=Ke;Ke=Ke+160|0;g=y;b=e+2|0;k=Ve[e>>1]|0;E=0;t=5;do{v=E;f=0;while(1){u=r+(v<<1)|0;p=40-v|0;o=(p+131071|0)>>>1&65535;s=r+(v+1<<1)|0;a=Ze(Ve[u>>1]<<1,k)|0;if(!(o<<16>>16))o=b;else{m=131111-v+131070&131070;w=v+m|0;h=b;d=e;c=u;while(1){l=c+4|0;u=d+4|0;a=(Ze(Ve[s>>1]<<1,Ve[h>>1]|0)|0)+a|0;o=o+-1<<16>>16;a=(Ze(Ve[l>>1]<<1,Ve[u>>1]|0)|0)+a|0;if(!(o<<16>>16))break;else{s=c+6|0;h=d+6|0;d=u;c=l}}s=r+(w+3<<1)|0;o=e+(m+3<<1)|0}if(!(p&1))a=(Ze(Ve[s>>1]<<1,Ve[o>>1]|0)|0)+a|0;Ge[g+(v<<2)>>2]=a;a=(a|0)<0?0-a|0:a;f=(a|0)>(f|0)?a:f;a=v+5|0;if((a&65535)<<16>>16<40)v=a<<16>>16;else break}t=(f>>1)+t|0;E=E+1|0}while((E|0)!=5);n=((kt(t)|0)&65535)-(n&65535)|0;a=n<<16>>16;t=0-a<<16;f=t>>16;if((n&65535)<<16>>16>0){o=20;t=g;while(1){g=Ge[t>>2]|0;n=g<<a;Ve[i>>1]=(((n>>a|0)==(g|0)?n:g>>31^2147483647)+32768|0)>>>16;g=Ge[t+4>>2]|0;n=g<<a;Ve[i+2>>1]=(((n>>a|0)==(g|0)?n:g>>31^2147483647)+32768|0)>>>16;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{i=i+4|0;t=t+8|0}}Ke=y;return}if((t|0)<2031616){o=20;t=g;while(1){Ve[i>>1]=((Ge[t>>2]>>f)+32768|0)>>>16;Ve[i+2>>1]=((Ge[t+4>>2]>>f)+32768|0)>>>16;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{i=i+4|0;t=t+8|0}}Ke=y;return}else{Ve[i>>1]=0;g=i+4|0;Ve[i+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;n=g+4|0;Ve[g+2>>1]=0;Ve[n>>1]=0;g=n+4|0;Ve[n+2>>1]=0;Ve[g>>1]=0;Ve[g+2>>1]=0;Ke=y;return}}function ci(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;a=(Kn(16383,r)|0)<<16>>16;r=Ze(a,r<<16>>16)|0;if((r|0)==1073741824){Ge[n>>2]=1;t=2147483647}else t=r<<1;o=(Ze(a,i<<16>>16)|0)>>15;r=t+(o<<1)|0;if((t^o|0)>0&(r^t|0)<0){Ge[n>>2]=1;r=(t>>>31)+2147483647|0}t=2147483647-r|0;i=t>>16;r=Ze(i,a)|0;if((r|0)==1073741824){Ge[n>>2]=1;o=2147483647}else o=r<<1;a=(Ze((t>>>1)-(i<<15)<<16>>16,a)|0)>>15;r=o+(a<<1)|0;if((o^a|0)>0&(r^o|0)<0){Ge[n>>2]=1;r=(o>>>31)+2147483647|0}o=r>>16;a=e>>16;i=Ze(o,a)|0;i=(i|0)==1073741824?2147483647:i<<1;t=(Ze((r>>>1)-(o<<15)<<16>>16,a)|0)>>15;n=(t<<1)+i|0;n=(t^i|0)>0&(n^i|0)<0?(i>>>31)+2147483647|0:n;a=(Ze(o,(e>>>1)-(a<<15)<<16>>16)|0)>>15;e=n+(a<<1)|0;e=(n^a|0)>0&(e^n|0)<0?(n>>>31)+2147483647|0:e;n=e<<2;return((n>>2|0)==(e|0)?n:e>>31^2147483647)|0}function di(e,r){e=e|0;r=r|0;var i=0,n=0,t=0,o=0;if(!e){o=-1;return o|0}Ge[e>>2]=0;i=Ut(192)|0;if(!i){o=-1;return o|0}n=i+176|0;Ve[n>>1]=0;Ve[n+2>>1]=0;Ve[n+4>>1]=0;Ve[n+6>>1]=0;Ve[n+8>>1]=0;Ve[n+10>>1]=0;n=i;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+20|0;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+40|0;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+60|0;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+80|0;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+100|0;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+120|0;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+140|0;t=r;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=i+160|0;o=n+20|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(o|0));Ve[i+188>>1]=7;Ve[i+190>>1]=32767;Ge[e>>2]=i;o=0;return o|0}function hi(e,r){e=e|0;r=r|0;var i=0,n=0,t=0;if(!e){t=-1;return t|0}i=e+176|0;Ve[i>>1]=0;Ve[i+2>>1]=0;Ve[i+4>>1]=0;Ve[i+6>>1]=0;Ve[i+8>>1]=0;Ve[i+10>>1]=0;i=e;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+20|0;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+40|0;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+60|0;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+80|0;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+100|0;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+120|0;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+140|0;n=r;t=i+20|0;do{Ve[i>>1]=Ve[n>>1]|0;i=i+2|0;n=n+2|0}while((i|0)<(t|0));i=e+160|0;t=i+20|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(t|0));Ve[e+188>>1]=7;Ve[e+190>>1]=32767;t=1;return t|0}function wi(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function mi(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0;D=Ke;Ke=Ke+112|0;y=D+80|0;_=D+60|0;A=D+40|0;g=D;if(r<<16>>16==0?(a=e+178|0,(Ve[a>>1]|0)!=0):0){A=e+180|0;o=e+182|0;i=a;A=Ve[A>>1]|0;n=Ge[t>>2]|0;_=n+2|0;Ve[n>>1]=A;o=Ve[o>>1]|0;A=n+4|0;Ve[_>>1]=o;_=e+184|0;_=Ve[_>>1]|0;o=n+6|0;Ve[A>>1]=_;A=e+186|0;A=Ve[A>>1]|0;e=n+8|0;Ve[o>>1]=A;i=Ve[i>>1]|0;n=n+10|0;Ge[t>>2]=n;Ve[e>>1]=i;Ke=D;return}m=g+36|0;p=g+32|0;v=g+28|0;b=g+24|0;k=g+20|0;E=g+16|0;d=g+12|0;h=g+8|0;w=g+4|0;r=g;a=r+40|0;do{Ge[r>>2]=0;r=r+4|0}while((r|0)<(a|0));c=7;r=0;while(1){u=Ve[e+160+(c<<1)>>1]|0;a=u<<16>>16;if(u<<16>>16<0)a=~((a^-4)>>2);else a=a>>>2;r=Gn(r,a&65535,o)|0;s=c*10|0;u=9;while(1){l=g+(u<<2)|0;f=Ge[l>>2]|0;S=Ve[e+(u+s<<1)>>1]|0;a=S+f|0;if((S^f|0)>-1&(a^f|0)<0){Ge[o>>2]=1;a=(f>>>31)+2147483647|0}Ge[l>>2]=a;if((u|0)>0)u=u+-1|0;else break}if((c|0)>0)c=c+-1|0;else break}a=r<<16>>16;if(r<<16>>16<0)a=~((a^-2)>>1);else a=a>>>1;Ve[_+18>>1]=(Ge[m>>2]|0)>>>3;Ve[_+16>>1]=(Ge[p>>2]|0)>>>3;Ve[_+14>>1]=(Ge[v>>2]|0)>>>3;Ve[_+12>>1]=(Ge[b>>2]|0)>>>3;Ve[_+10>>1]=(Ge[k>>2]|0)>>>3;Ve[_+8>>1]=(Ge[E>>2]|0)>>>3;Ve[_+6>>1]=(Ge[d>>2]|0)>>>3;Ve[_+4>>1]=(Ge[h>>2]|0)>>>3;Ve[_+2>>1]=(Ge[w>>2]|0)>>>3;Ve[_>>1]=(Ge[g>>2]|0)>>>3;r=e+178|0;a=(((a<<16)+167772160|0)>>>16)+128|0;Ve[r>>1]=a;a=a<<16;if((a|0)<0)a=~((a>>16^-256)>>8);else a=a>>24;Ve[r>>1]=a;if((a|0)<=63){if((a|0)<0){Ve[r>>1]=0;a=0}}else{Ve[r>>1]=63;a=63}S=Ct(a<<8&65535,11560,o)|0;S=S<<16>>16>0?0:S<<16>>16<-14436?-14436:S;Ve[n>>1]=S;Ve[n+2>>1]=S;Ve[n+4>>1]=S;Ve[n+6>>1]=S;S=((S<<16>>16)*5443|0)>>>15&65535;Ve[n+8>>1]=S;Ve[n+10>>1]=S;Ve[n+12>>1]=S;Ve[n+14>>1]=S;vt(_,y,10,o);Ot(y,205,10,o);pt(y,_,10,o);n=e+182|0;S=e+180|0;_t(i,8,_,A,n,S,o);o=n;n=r;S=Ve[S>>1]|0;i=Ge[t>>2]|0;A=i+2|0;Ve[i>>1]=S;o=Ve[o>>1]|0;S=i+4|0;Ve[A>>1]=o;A=e+184|0;A=Ve[A>>1]|0;o=i+6|0;Ve[S>>1]=A;e=e+186|0;e=Ve[e>>1]|0;S=i+8|0;Ve[o>>1]=e;e=Ve[n>>1]|0;i=i+10|0;Ge[t>>2]=i;Ve[S>>1]=e;Ke=D;return}function pi(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0;l=Ke;Ke=Ke+16|0;a=l+2|0;s=l;f=e+176|0;o=(We[f>>1]|0)+1|0;o=(o&65535|0)==8?0:o&65535;Ve[f>>1]=o;o=e+((o<<16>>16)*10<<1)|0;t=o+20|0;do{Ve[o>>1]=Ve[r>>1]|0;o=o+2|0;r=r+2|0}while((o|0)<(t|0));r=0;t=160;while(1){o=Ve[i>>1]|0;r=(Ze(o<<1,o)|0)+r|0;if((r|0)<0){r=2147483647;break}t=t+-1<<16>>16;if(!(t<<16>>16))break;else i=i+2|0}ft(r,a,s,n);r=Ve[a>>1]|0;a=r<<16>>16;i=a<<10;if((i|0)!=(a<<26>>16|0)){Ge[n>>2]=1;i=r<<16>>16>0?32767:-32768}Ve[e+160+(Ve[f>>1]<<1)>>1]=(((Ve[s>>1]|0)>>>5)+i<<16)+-558432256>>17;Ke=l;return}function vi(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;o=e+190|0;a=Gn(Ve[o>>1]|0,1,n)|0;Ve[o>>1]=a;t=e+188|0;do{if(!(r<<16>>16)){e=Ve[t>>1]|0;if(!(e<<16>>16)){Ve[o>>1]=0;Ge[i>>2]=8;e=1;break}o=(e&65535)+65535&65535;Ve[t>>1]=o;if((Gn(a,o,n)|0)<<16>>16<30){Ge[i>>2]=8;e=0}else e=0}else{Ve[t>>1]=7;e=0}}while(0);return e|0}function bi(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;if(!(o<<16>>16)){o=e<<16>>16;if(((o<<16)+-5570560|0)<65536){r=(o*3|0)+-58+(r<<16>>16)|0;r=r&65535;return r|0}else{r=o+112|0;r=r&65535;return r|0}}if(!(a<<16>>16)){f=(e&65535)-(n&65535)<<16;r=(r<<16>>16)+2+(f>>15)+(f>>16)|0;r=r&65535;return r|0}n=n<<16>>16;n=(((i&65535)-n<<16)+-327680|0)>0?n+5&65535:i;t=t<<16>>16;i=e<<16>>16;n=(((t-(n&65535)<<16)+-262144|0)>0?t+65532&65535:n)<<16>>16;t=n*196608|0;e=t+-393216>>16;o=((r&65535)<<16)+(i*196608|0)>>16;if(!(e-o&32768)){r=i+5-n|0;r=r&65535;return r|0}if((t+196608>>16|0)>(o|0)){r=o+3-e|0;r=r&65535;return r|0}else{r=i+11-n|0;r=r&65535;return r|0}return 0}function ki(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;t=e<<16>>16;do{if(!(n<<16>>16))if(e<<16>>16<95){t=((t*393216|0)+-6881280>>16)+(r<<16>>16)|0;break}else{t=t+368|0;break}else t=((((t-(i&65535)|0)*393216|0)+196608|0)>>>16)+(r&65535)|0}while(0);return t&65535|0}function Ei(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0;t=Ge[n+96>>2]|0;if(e>>>0<8){s=(Ge[n+100>>2]|0)+(e<<2)|0;f=Ge[s>>2]|0;Xe[i>>0]=Ve[r+(Ve[f>>1]<<1)>>1]<<4|e|Ve[r+(Ve[f+2>>1]<<1)>>1]<<5|Ve[r+(Ve[f+4>>1]<<1)>>1]<<6|Ve[r+(Ve[f+6>>1]<<1)>>1]<<7;f=t+(e<<1)|0;n=Ve[f>>1]|0;if((n+-7|0)>4){t=4;a=4;e=1;while(1){l=Ve[r+(Ve[(Ge[s>>2]|0)+(t<<1)>>1]<<1)>>1]|0;n=i+(e<<16>>16)|0;Xe[n>>0]=l;l=We[r+(Ve[(Ge[s>>2]|0)+((a|1)<<16>>16<<1)>>1]<<1)>>1]<<1|l&65535;Xe[n>>0]=l;l=We[r+(Ve[(Ge[s>>2]|0)+((a|2)<<16>>16<<1)>>1]<<1)>>1]<<2|l;Xe[n>>0]=l;l=We[r+(Ve[(Ge[s>>2]|0)+((a|3)<<16>>16<<1)>>1]<<1)>>1]<<3|l;Xe[n>>0]=l;l=We[r+(Ve[(Ge[s>>2]|0)+(a+4<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<4|l;Xe[n>>0]=l;l=We[r+(Ve[(Ge[s>>2]|0)+(a+5<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<5|l;Xe[n>>0]=l;l=We[r+(Ve[(Ge[s>>2]|0)+(a+6<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<6|l;Xe[n>>0]=l;o=a+8<<16>>16;e=e+1<<16>>16;Xe[n>>0]=We[r+(Ve[(Ge[s>>2]|0)+(a+7<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<7|l;t=o<<16>>16;n=Ve[f>>1]|0;if((t|0)>=(n+-7|0))break;else a=o}}else{o=4;e=1}f=n+4&7;if(!f)return;t=i+(e<<16>>16)|0;Xe[t>>0]=0;n=0;a=0;e=0;while(1){a=(We[r+(Ve[(Ge[s>>2]|0)+(o<<16>>16<<1)>>1]<<1)>>1]&255)<<n|a&255;Xe[t>>0]=a;e=e+1<<16>>16;n=e<<16>>16;if((n|0)>=(f|0))break;else o=o+1<<16>>16}return}if((e|0)==15){Xe[i>>0]=15;return}Xe[i>>0]=Ve[r>>1]<<4|e|Ve[r+2>>1]<<5|Ve[r+4>>1]<<6|Ve[r+6>>1]<<7;n=t+(e<<1)|0;e=Ve[n>>1]|0;t=((e&65535)<<16)+262144>>16;s=t&-8;a=(s+524281|0)>>>3&65535;if(a<<16>>16>0){t=((t&-8)+524281|0)>>>3;f=((t<<3)+524280&524280)+12|0;o=1;e=r+8|0;while(1){Xe[i+(o<<16>>16)>>0]=We[e+2>>1]<<1|We[e>>1]|We[e+4>>1]<<2|We[e+6>>1]<<3|We[e+8>>1]<<4|We[e+10>>1]<<5|We[e+12>>1]<<6|We[e+14>>1]<<7;if(a<<16>>16>1){a=a+-1<<16>>16;o=o+1<<16>>16;e=e+16|0}else break}e=Ve[n>>1]|0;o=(t<<16)+65536>>16}else{f=4;o=1}e=(0-s|4)+(e&65535)<<16;a=e>>16;if(!a)return;o=i+o|0;Xe[o>>0]=0;if((e|0)>0){e=0;t=0;n=0}else return;do{t=t&255|Ve[r+(f+e<<1)>>1]<<e;Xe[o>>0]=t;n=n+1<<16>>16;e=n<<16>>16}while((e|0)<(a|0));return}function gi(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0;u=Ge[n+100>>2]|0;l=Ge[n+96>>2]|0;Xe[i>>0]=e&15;l=l+(e<<1)|0;t=Ve[l>>1]|0;if(e>>>0>=8){f=((t&65535)<<16)+-458752|0;if((f|0)>0){s=1;a=r;while(1){r=a+16|0;n=s+1<<16>>16;Xe[i+(s<<16>>16)>>0]=We[a+14>>1]|We[a+12>>1]<<1|((We[a+2>>1]<<6|We[a>>1]<<7|We[a+4>>1]<<5|We[a+6>>1]<<4)&240|We[a+8>>1]<<3|We[a+10>>1]<<2)&252;f=f+-524288&-65536;if((f|0)<=0)break;else{s=n;a=r}}t=Ve[l>>1]|0}else n=1;s=t&7;t=i+(n<<16>>16)|0;Xe[t>>0]=0;if(!s)return;else{o=0;a=0;f=0;n=r}while(1){a=a&255|Ve[n>>1]<<7-o;Xe[t>>0]=a;f=f+1<<16>>16;o=f<<16>>16;if((o|0)>=(s|0))break;else n=n+2|0}return}a=t<<16>>16;if(t<<16>>16>7){t=u+(e<<2)|0;n=0;s=0;o=1;while(1){c=We[r+(Ve[(Ge[t>>2]|0)+(n<<1)>>1]<<1)>>1]<<7;a=i+(o<<16>>16)|0;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|1)<<16>>16<<1)>>1]<<1)>>1]<<6|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|2)<<16>>16<<1)>>1]<<1)>>1]<<5|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|3)<<16>>16<<1)>>1]<<1)>>1]<<4|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|4)<<16>>16<<1)>>1]<<1)>>1]<<3|c&240;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|5)<<16>>16<<1)>>1]<<1)>>1]<<2|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|6)<<16>>16<<1)>>1]<<1)>>1]<<1|c;Xe[a>>0]=c;f=s+8<<16>>16;o=o+1<<16>>16;Xe[a>>0]=c&254|We[r+(Ve[(Ge[t>>2]|0)+((s|7)<<16>>16<<1)>>1]<<1)>>1];n=f<<16>>16;a=Ve[l>>1]|0;if((n|0)>=(a+-7|0))break;else s=f}}else{f=0;o=1}l=a&7;s=i+(o<<16>>16)|0;Xe[s>>0]=0;if(!l)return;o=u+(e<<2)|0;t=0;n=0;a=0;while(1){n=(We[r+(Ve[(Ge[o>>2]|0)+(f<<16>>16<<1)>>1]<<1)>>1]&255)<<7-t|n&255;Xe[s>>0]=n;a=a+1<<16>>16;t=a<<16>>16;if((t|0)>=(l|0))break;else f=f+1<<16>>16}return}function yi(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0;u=Ge[n+100>>2]|0;l=Ge[n+96>>2]|0;Xe[i>>0]=e<<3;l=l+(e<<1)|0;t=Ve[l>>1]|0;if(e>>>0>=8){f=((t&65535)<<16)+-458752|0;if((f|0)>0){s=1;a=r;while(1){r=a+16|0;n=s+1<<16>>16;Xe[i+(s<<16>>16)>>0]=We[a+14>>1]|We[a+12>>1]<<1|((We[a+2>>1]<<6|We[a>>1]<<7|We[a+4>>1]<<5|We[a+6>>1]<<4)&240|We[a+8>>1]<<3|We[a+10>>1]<<2)&252;f=f+-524288&-65536;if((f|0)<=0)break;else{s=n;a=r}}t=Ve[l>>1]|0}else n=1;s=t&7;t=i+(n<<16>>16)|0;Xe[t>>0]=0;if(!s)return;else{o=0;a=0;f=0;n=r}while(1){a=a&255|Ve[n>>1]<<7-o;Xe[t>>0]=a;f=f+1<<16>>16;o=f<<16>>16;if((o|0)>=(s|0))break;else n=n+2|0}return}a=t<<16>>16;if(t<<16>>16>7){t=u+(e<<2)|0;n=0;s=0;o=1;while(1){c=We[r+(Ve[(Ge[t>>2]|0)+(n<<1)>>1]<<1)>>1]<<7;a=i+(o<<16>>16)|0;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|1)<<16>>16<<1)>>1]<<1)>>1]<<6|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|2)<<16>>16<<1)>>1]<<1)>>1]<<5|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|3)<<16>>16<<1)>>1]<<1)>>1]<<4|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|4)<<16>>16<<1)>>1]<<1)>>1]<<3|c&240;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|5)<<16>>16<<1)>>1]<<1)>>1]<<2|c;Xe[a>>0]=c;c=We[r+(Ve[(Ge[t>>2]|0)+((s|6)<<16>>16<<1)>>1]<<1)>>1]<<1|c;Xe[a>>0]=c;f=s+8<<16>>16;o=o+1<<16>>16;Xe[a>>0]=c&254|We[r+(Ve[(Ge[t>>2]|0)+((s|7)<<16>>16<<1)>>1]<<1)>>1];n=f<<16>>16;a=Ve[l>>1]|0;if((n|0)>=(a+-7|0))break;else s=f}}else{f=0;o=1}l=a&7;s=i+(o<<16>>16)|0;Xe[s>>0]=0;if(!l)return;o=u+(e<<2)|0;t=0;n=0;a=0;while(1){n=(We[r+(Ve[(Ge[o>>2]|0)+(f<<16>>16<<1)>>1]<<1)>>1]&255)<<7-t|n&255;Xe[s>>0]=n;a=a+1<<16>>16;t=a<<16>>16;if((t|0)>=(l|0))break;else f=f+1<<16>>16}return}function _i(e){e=e|0;var r=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(16)|0;if(!r){e=-1;return e|0}Ve[r>>1]=0;Ve[r+2>>1]=0;Ve[r+4>>1]=0;Ve[r+6>>1]=0;Ve[r+8>>1]=0;Ve[r+10>>1]=0;Ve[r+12>>1]=0;Ve[r+14>>1]=0;Ge[e>>2]=r;e=0;return e|0}function Ai(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;Ve[e+12>>1]=0;Ve[e+14>>1]=0;e=0;return e|0}function Di(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function Si(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0;f=r<<16>>16<2722?0:r<<16>>16<5444?1:2;a=Tt(i,1,t)|0;l=e+4|0;if(!(i<<16>>16>200?a<<16>>16>(Ve[l>>1]|0):0)){a=Ve[e>>1]|0;if(a<<16>>16){o=a+-1<<16>>16;Ve[e>>1]=o;o=o<<16>>16!=0;s=5}}else{Ve[e>>1]=8;o=1;s=5}if((s|0)==5)if((f&65535)<2&o)f=(f&65535)+1&65535;s=e+6|0;Ve[s>>1]=r;o=rt(s,5)|0;if(!(f<<16>>16!=0|o<<16>>16>5443))if(o<<16>>16<0)o=16384;else{o=o<<16>>16;o=(((o<<18>>18|0)==(o|0)?o<<2:o>>>15^32767)<<16>>16)*24660>>15;if((o|0)>32767){Ge[t>>2]=1;o=32767}o=16384-o&65535}else o=0;a=e+2|0;if(!(Ve[a>>1]|0))o=It(o,1,t)|0;Ve[n>>1]=o;Ve[a>>1]=o;Ve[l>>1]=i;n=e+12|0;Ve[e+14>>1]=Ve[n>>1]|0;i=e+10|0;Ve[n>>1]=Ve[i>>1]|0;e=e+8|0;Ve[i>>1]=Ve[e>>1]|0;Ve[e>>1]=Ve[s>>1]|0;return}function Ri(e){e=e|0;var r=0,i=0,n=0,t=0,o=0,a=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(68)|0;n=r;if(!r){e=-1;return e|0}Ge[r+28>>2]=0;t=r+64|0;Ge[t>>2]=0;o=r+32|0;if(((Zn(o)|0)<<16>>16==0?(a=r+48|0,(Zn(a)|0)<<16>>16==0):0)?(_i(t)|0)<<16>>16==0:0){i=r+32|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(i|0));Zn(o)|0;Zn(a)|0;Ai(Ge[t>>2]|0)|0;Ge[e>>2]=n;e=0;return e|0}Di(t);zt(r);e=-1;return e|0}function Mi(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;Di(r+64|0);zt(Ge[e>>2]|0);Ge[e>>2]=0;return}function Ni(e){e=e|0;var r=0,i=0,n=0;if(!e){n=-1;return n|0}r=e+32|0;i=e;n=i+32|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Zn(r)|0;Zn(e+48|0)|0;Ai(Ge[e+64>>2]|0)|0;n=0;return n|0}function Oi(e,r,i,n,t,o,a,f,s,l,u,c,d,h,w,m,p,v,b){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;p=p|0;v=v|0;b=b|0;var k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0;N=Ke;Ke=Ke+48|0;E=N+34|0;y=N+32|0;A=N+30|0;_=N+28|0;g=N+18|0;k=N+8|0;D=N+6|0;S=N+4|0;R=N+2|0;M=N;if(r){u=e+32|0;Qn(u,r,t,E,y,D,S,b);do{if((r|0)!=7){Zr(r,o,a,f,s,l,g,k,M,R,b);if((r|0)==5){mn(Ge[e+64>>2]|0,i,n,t,g,k,Ve[D>>1]|0,Ve[S>>1]|0,Ve[E>>1]|0,Ve[y>>1]|0,40,Ve[M>>1]|0,Ve[R>>1]|0,c,w,m,A,_,p,v,b);break}else{e=bn(r,Ve[E>>1]|0,Ve[y>>1]|0,g,k,c,w,m,A,_,v,b)|0;o=Ge[p>>2]|0;Ge[p>>2]=o+2;Ve[o>>1]=e;break}}else{Ve[m>>1]=Li(a,s,b)|0;e=pn(7,Ve[E>>1]|0,Ve[y>>1]|0,m,A,_,Ge[v+68>>2]|0,b)|0;o=Ge[p>>2]|0;Ge[p>>2]=o+2;Ve[o>>1]=e}}while(0);Jn(u,Ve[A>>1]|0,Ve[_>>1]|0);Ke=N;return}if(!(u<<16>>16)){Qn(e+48|0,0,t,E,y,D,S,b);Zr(0,o,a,f,s,l,g,k,M,R,b);Qr(o,D,S,b);o=hn(e+32|0,Ve[e>>1]|0,Ve[e+2>>1]|0,e+8|0,e+18|0,Ve[e+4>>1]|0,Ve[e+6>>1]|0,t,Ve[E>>1]|0,Ve[y>>1]|0,k,g,Ve[D>>1]|0,Ve[S>>1]|0,c,d,h,w,m,b)|0;Ve[Ge[e+28>>2]>>1]=o;Ke=N;return}u=Ge[p>>2]|0;Ge[p>>2]=u+2;Ge[e+28>>2]=u;u=e+48|0;i=e+32|0;d=i;d=We[d>>1]|We[d+2>>1]<<16;i=i+4|0;i=We[i>>1]|We[i+2>>1]<<16;p=u;h=p;Ve[h>>1]=d;Ve[h+2>>1]=d>>>16;p=p+4|0;Ve[p>>1]=i;Ve[p+2>>1]=i>>>16;p=e+40|0;i=p;i=We[i>>1]|We[i+2>>1]<<16;p=p+4|0;p=We[p>>1]|We[p+2>>1]<<16;h=e+56|0;d=h;Ve[d>>1]=i;Ve[d+2>>1]=i>>>16;h=h+4|0;Ve[h>>1]=p;Ve[h+2>>1]=p>>>16;h=e+2|0;Qn(u,0,t,e,h,D,S,b);Zr(0,o,a,f,s,l,e+18|0,e+8|0,M,R,b);f=(We[R>>1]|0)+1|0;p=Ve[M>>1]|0;d=f<<16>>16;if((f&65535)<<16>>16<0){v=0-d<<16;if((v|0)<983040)v=p<<16>>16>>(v>>16)&65535;else v=0}else{p=p<<16>>16;v=p<<d;if((v<<16>>16>>d|0)==(p|0))v=v&65535;else v=(p>>>15^32767)&65535}Ve[m>>1]=v;Qr(o,e+4|0,e+6|0,b);dn(u,Ve[e>>1]|0,Ve[h>>1]|0,Ve[R>>1]|0,Ve[M>>1]|0,b);Ke=N;return}function Li(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;t=10;i=e;n=r;e=0;while(1){e=(Ze(Ve[n>>1]>>1,Ve[i>>1]|0)|0)+e|0;e=e+(Ze(Ve[n+2>>1]>>1,Ve[i+2>>1]|0)|0)|0;e=e+(Ze(Ve[n+4>>1]>>1,Ve[i+4>>1]|0)|0)|0;e=e+(Ze(Ve[n+6>>1]>>1,Ve[i+6>>1]|0)|0)|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{i=i+8|0;n=n+8|0}}i=e<<1;t=kt(i|1)|0;o=t<<16>>16;i=(t<<16>>16<17?i>>17-o:i<<o+-17)&65535;if(i<<16>>16<1){r=0;return r|0}else{t=20;n=r;e=0}while(1){r=Ve[n>>1]>>1;r=((Ze(r,r)|0)>>>2)+e|0;e=Ve[n+2>>1]>>1;e=r+((Ze(e,e)|0)>>>2)|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else n=n+4|0}e=e<<3;t=kt(e)|0;r=t<<16>>16;i=Kn(i,(t<<16>>16<16?e>>16-r:e<<r+-16)&65535)|0;r=(o<<16)+327680-(r<<16)|0;e=r>>16;if((r|0)>65536)e=i<<16>>16>>e+-1;else e=i<<16>>16<<1-e;r=e&65535;return r|0}function Fi(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0;Ge[o>>2]=0;u=t<<16>>16;s=u>>>2&65535;d=s<<16>>16==0;if(d)f=0;else{l=s;a=i;f=0;while(1){h=Ve[a>>1]|0;h=(Ze(h,h)|0)+f|0;f=Ve[a+2>>1]|0;f=h+(Ze(f,f)|0)|0;h=Ve[a+4>>1]|0;h=f+(Ze(h,h)|0)|0;f=Ve[a+6>>1]|0;f=h+(Ze(f,f)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else a=a+8|0}}if(!((f>>>31^1)&(f|0)<1073741824)){f=u>>>1&65535;if(!(f<<16>>16))f=1;else{a=f;l=i;f=0;while(1){h=Ve[l>>1]>>2;h=(Ze(h,h)|0)+f|0;f=Ve[l+2>>1]>>2;f=h+(Ze(f,f)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else l=l+4|0}f=f<<1|1}h=(kt(f)|0)<<16>>16;c=h+65532&65535;h=Ft(f<<h,o)|0}else{u=f<<1|1;h=kt(u)|0;c=h;h=Ft(u<<(h<<16>>16),o)|0}Ge[o>>2]=0;do{if(!(t<<16>>16)){f=1;w=14}else{u=t;l=r;f=i;t=0;while(1){m=Ze(Ve[f>>1]|0,Ve[l>>1]|0)|0;a=m+t|0;if((m^t|0)>0&(a^t|0)<0)break;u=u+-1<<16>>16;if(!(u<<16>>16)){w=13;break}else{l=l+2|0;f=f+2|0;t=a}}if((w|0)==13){f=a<<1|1;w=14;break}Ge[o>>2]=1;if(d)f=1;else{f=r;a=0;while(1){a=(Ze(Ve[i>>1]>>2,Ve[f>>1]|0)|0)+a|0;a=a+(Ze(Ve[i+2>>1]>>2,Ve[f+2>>1]|0)|0)|0;a=a+(Ze(Ve[i+4>>1]>>2,Ve[f+4>>1]|0)|0)|0;a=a+(Ze(Ve[i+6>>1]>>2,Ve[f+6>>1]|0)|0)|0;s=s+-1<<16>>16;if(!(s<<16>>16))break;else{f=f+8|0;i=i+8|0}}f=a<<1|1}i=(kt(f)|0)<<16>>16;a=i+65532&65535;i=Ft(f<<i,o)|0}}while(0);if((w|0)==14){i=kt(f)|0;a=i;i=Ft(f<<(i<<16>>16),o)|0}Ve[n>>1]=h;f=c<<16>>16;Ve[n+2>>1]=15-f;Ve[n+4>>1]=i;a=a<<16>>16;Ve[n+6>>1]=15-a;if(i<<16>>16<4){m=0;return m|0}a=It(Kn(i<<16>>16>>>1&65535,h)|0,a-f&65535,o)|0;a=a<<16>>16>19661?19661:a;if((e|0)!=7){m=a;return m|0}m=a&65532;return m|0}function Ii(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0;s=(n&65535)+65535&65535;if(s<<16>>16>t<<16>>16){c=n+-1<<16>>16<<16>>16;n=-2147483648;while(1){l=Ge[e+(0-c<<2)>>2]|0;f=l<<1;l=(f>>1|0)==(l|0)?f:l>>31^2147483647;f=Ge[e+(~c<<2)>>2]|0;u=l-f|0;if(((u^l)&(l^f)|0)<0){Ge[a>>2]=1;u=(l>>>31)+2147483647|0}l=Ge[e+(1-c<<2)>>2]|0;f=u-l|0;if(((f^u)&(l^u)|0)<0){Ge[a>>2]=1;f=(u>>>31)+2147483647|0}u=Pi(f)|0;n=(u|0)<(n|0)?n:u;s=s+-1<<16>>16;if(s<<16>>16<=t<<16>>16){t=n;break}else c=c+-1|0}}else t=-2147483648;e=i<<16>>16>0;if(e){n=0;f=r;s=0;while(1){u=Ve[f>>1]|0;u=Ze(u,u)|0;if((u|0)!=1073741824){l=(u<<1)+s|0;if((u^s|0)>0&(l^s|0)<0){Ge[a>>2]=1;s=(s>>>31)+2147483647|0}else s=l}else{Ge[a>>2]=1;s=2147483647}n=n+1<<16>>16;if(n<<16>>16>=i<<16>>16)break;else f=f+2|0}if(e){e=0;c=r;n=r+-2|0;f=0;while(1){u=Ze(Ve[n>>1]|0,Ve[c>>1]|0)|0;if((u|0)!=1073741824){l=(u<<1)+f|0;if((u^f|0)>0&(l^f|0)<0){Ge[a>>2]=1;f=(f>>>31)+2147483647|0}else f=l}else{Ge[a>>2]=1;f=2147483647}e=e+1<<16>>16;if(e<<16>>16>=i<<16>>16)break;else{c=c+2|0;n=n+2|0}}}else f=0}else{s=0;f=0}n=s<<1;n=(n>>1|0)==(s|0)?n:s>>31^2147483647;i=f<<1;i=(i>>1|0)==(f|0)?i:f>>31^2147483647;s=n-i|0;if(((s^n)&(i^n)|0)<0){Ge[a>>2]=1;s=(n>>>31)+2147483647|0}e=Pi(s)|0;c=((kt(t)|0)&65535)+65535|0;s=c<<16>>16;if((c&65535)<<16>>16>0){n=t<<s;if((n>>s|0)!=(t|0))n=t>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)n=t>>(s>>16);else n=0}u=kt(e)|0;f=u<<16>>16;if(u<<16>>16>0){s=e<<f;if((s>>f|0)==(e|0))d=33;else{s=e>>31^2147483647;d=33}}else{s=0-f<<16;if((s|0)<2031616){s=e>>(s>>16);d=33}else l=0}if((d|0)==33)if(s>>>0>65535)l=Kn(n>>>16&65535,s>>>16&65535)|0;else l=0;s=u&65535;d=(c&65535)-s|0;n=d&65535;if(!(d&32768)){a=It(l,n,a)|0;Ve[o>>1]=a;return 0}if(n<<16>>16!=-32768){a=s-c|0;f=a<<16>>16;if((a&65535)<<16>>16<0){f=0-f<<16;if((f|0)>=983040){a=0;Ve[o>>1]=a;return 0}a=l<<16>>16>>(f>>16)&65535;Ve[o>>1]=a;return 0}}else f=32767;n=l<<16>>16;s=n<<f;if((s<<16>>16>>f|0)==(n|0)){a=s&65535;Ve[o>>1]=a;return 0}a=(n>>>15^32767)&65535;Ve[o>>1]=a;return 0}function Ti(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;if(i<<16>>16)r=r<<16>>16<<1&65535;if(r<<16>>16<0){e=e+-2|0;r=(r&65535)+6&65535}i=r<<16>>16;n=6-i<<16>>16;r=(Ze(Ve[3468+(i<<1)>>1]|0,Ve[e>>1]|0)|0)+16384|0;r=r+(Ze(Ve[3468+(n<<1)>>1]|0,Ve[e+2>>1]|0)|0)|0;r=r+(Ze(Ve[3468+(i+6<<1)>>1]|0,Ve[e+-2>>1]|0)|0)|0;r=r+(Ze(Ve[3468+(n+6<<1)>>1]|0,Ve[e+4>>1]|0)|0)|0;r=(Ze(Ve[3468+(i+12<<1)>>1]|0,Ve[e+-4>>1]|0)|0)+r|0;r=r+(Ze(Ve[3468+(n+12<<1)>>1]|0,Ve[e+6>>1]|0)|0)|0;i=r+(Ze(Ve[3468+(i+18<<1)>>1]|0,Ve[e+-6>>1]|0)|0)|0;return(i+(Ze(Ve[3468+(n+18<<1)>>1]|0,Ve[e+8>>1]|0)|0)|0)>>>15&65535|0}function Pi(e){e=e|0;e=e-(e>>>31)|0;return e>>31^e|0}function Ci(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0;if(!(e<<16>>16))return;else{t=3518;o=3538;n=i}while(1){n=n+2|0;r=r+2|0;s=Ve[r>>1]|0;f=Ve[t>>1]|0;i=Ze(f,s)|0;i=(i|0)==1073741824?2147483647:i<<1;s=(Ze(Ve[o>>1]|0,s)|0)>>15;a=(s<<1)+i|0;a=(i^s|0)>0&(a^i|0)<0?(i>>>31)+2147483647|0:a;f=(Ze(f,Ve[n>>1]|0)|0)>>15;i=a+(f<<1)|0;i=(a^f|0)>0&(i^a|0)<0?(a>>>31)+2147483647|0:i;Ve[r>>1]=i>>>16;Ve[n>>1]=(i>>>1)-(i>>16<<15);e=e+-1<<16>>16;if(!(e<<16>>16))break;else{t=t+2|0;o=o+2|0}}return}function Bi(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0;n=e&65535;t=n<<16;r=r<<16>>16;e=(r<<1)+t|0;if(!((r^t|0)>0&(e^t|0)<0)){t=e;return t|0}Ge[i>>2]=1;t=(n>>>15)+2147483647|0;return t|0}function xi(e){e=e|0;var r=0,i=0,n=0;if(!e){n=-1;return n|0}Ge[e>>2]=0;r=Ut(22)|0;if(!r){n=-1;return n|0}Ve[r>>1]=4096;i=r+2|0;n=i+20|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Ge[e>>2]=r;n=0;return n|0}function Ui(e){e=e|0;var r=0;if(!e){r=-1;return r|0}Ve[e>>1]=4096;e=e+2|0;r=e+20|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function zi(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function ji(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0;I=Ke;Ke=Ke+96|0;L=I+66|0;F=I+44|0;O=I+22|0;f=I;S=r+2|0;N=i+2|0;M=(Ve[N>>1]<<1)+(We[S>>1]<<16)|0;a=Pi(M)|0;a=ci(a,Ve[r>>1]|0,Ve[i>>1]|0,o)|0;if((M|0)>0)a=Hi(a)|0;A=a>>16;Ve[t>>1]=Ft(a,o)|0;b=a>>20;R=L+2|0;Ve[R>>1]=b;M=F+2|0;Ve[M>>1]=(a>>>5)-(b<<15);b=Ze(A,A)|0;b=(b|0)==1073741824?2147483647:b<<1;A=(Ze((a>>>1)-(A<<15)<<16>>16,A)|0)>>15;D=A<<1;_=D+b|0;_=(A^b|0)>0&(_^b|0)<0?(b>>>31)+2147483647|0:_;D=_+D|0;D=2147483647-(Pi((_^A|0)>0&(D^_|0)<0?(_>>>31)+2147483647|0:D)|0)|0;_=D>>16;A=Ve[r>>1]|0;b=Ze(_,A)|0;b=(b|0)==1073741824?2147483647:b<<1;A=(Ze((D>>>1)-(_<<15)<<16>>16,A)|0)>>15;D=(A<<1)+b|0;D=(A^b|0)>0&(D^b|0)<0?(b>>>31)+2147483647|0:D;_=(Ze(Ve[i>>1]|0,_)|0)>>15;b=D+(_<<1)|0;b=(D^_|0)>0&(b^D|0)<0?(D>>>31)+2147483647|0:b;D=kt(b)|0;b=b<<(D<<16>>16);_=O+2|0;A=f+2|0;s=b;b=(b>>>1)-(b>>16<<15)|0;k=f+4|0;E=O+4|0;g=2;y=2;while(1){v=s>>>16;a=v&65535;w=b&65535;m=y+-1|0;u=L+(m<<1)|0;p=F+(m<<1)|0;h=1;d=u;c=p;l=S;f=N;s=0;while(1){T=Ve[l>>1]|0;P=((Ze(Ve[c>>1]|0,T)|0)>>15)+s|0;s=Ve[d>>1]|0;s=P+(Ze(s,T)|0)+((Ze(s,Ve[f>>1]|0)|0)>>15)|0;h=h+1<<16>>16;if((h<<16>>16|0)>=(y|0))break;else{d=d+-2|0;c=c+-2|0;l=l+2|0;f=f+2|0}}P=(We[r+(y<<1)>>1]<<16)+(s<<5)+(Ve[i+(y<<1)>>1]<<1)|0;s=ci(Pi(P)|0,a,w,o)|0;if((P|0)>0)s=Hi(s)|0;f=D<<16>>16;if(D<<16>>16>0){a=s<<f;if((a>>f|0)!=(s|0))a=s>>31^2147483647}else{f=0-f<<16;if((f|0)<2031616)a=s>>(f>>16);else a=0}h=a>>16;if((y|0)<5)Ve[t+(m<<1)>>1]=(a+32768|0)>>>16;P=(a>>>16)-(a>>>31)|0;if(((P<<16>>31^P)&65535)<<16>>16>32750){a=16;break}c=(a>>>1)-(h<<15)<<16>>16;d=1;s=p;f=_;l=A;while(1){T=(Ze(Ve[s>>1]|0,h)|0)>>15;p=Ve[u>>1]|0;P=(Ze(p,c)|0)>>15;p=Ze(p,h)|0;P=p+T+(Ve[F+(d<<1)>>1]|0)+(Ve[L+(d<<1)>>1]<<15)+P|0;Ve[f>>1]=P>>>15;Ve[l>>1]=P&32767;d=d+1|0;if((d&65535)<<16>>16==g<<16>>16)break;else{u=u+-2|0;s=s+-2|0;f=f+2|0;l=l+2|0}}Ve[E>>1]=a>>20;Ve[k>>1]=(a>>>5)-(Ve[O+(y<<1)>>1]<<15);T=Ze(h,h)|0;T=(T|0)==1073741824?2147483647:T<<1;a=(Ze(c,h)|0)>>15;P=a<<1;f=P+T|0;f=(a^T|0)>0&(f^T|0)<0?(T>>>31)+2147483647|0:f;P=f+P|0;P=2147483647-(Pi((f^a|0)>0&(P^f|0)<0?(f>>>31)+2147483647|0:P)|0)|0;f=P>>16;a=v<<16>>16;a=((Ze(f,b<<16>>16)|0)>>15)+(Ze(f,a)|0)+((Ze((P>>>1)-(f<<15)<<16>>16,a)|0)>>15)<<1;f=(kt(a)|0)<<16>>16;a=a<<f;P=y<<1;Yt(R|0,_|0,P|0)|0;Yt(M|0,A|0,P|0)|0;y=y+1|0;if((y|0)>=11){a=20;break}else{D=f+(D&65535)&65535;s=a;b=(a>>1)-(a>>16<<15)|0;k=k+2|0;E=E+2|0;g=g+1<<16>>16}}if((a|0)==16){a=n+22|0;do{Ve[n>>1]=Ve[e>>1]|0;n=n+2|0;e=e+2|0}while((n|0)<(a|0));P=t;T=P;Ve[T>>1]=0;Ve[T+2>>1]=0>>>16;P=P+4|0;Ve[P>>1]=0;Ve[P+2>>1]=0>>>16;Ke=I;return 0}else if((a|0)==20){Ve[n>>1]=4096;P=((Ve[M>>1]|0)+8192+(Ve[R>>1]<<15)|0)>>>14&65535;Ve[n+2>>1]=P;Ve[e+2>>1]=P;P=((Ve[F+4>>1]|0)+8192+(Ve[L+4>>1]<<15)|0)>>>14&65535;Ve[n+4>>1]=P;Ve[e+4>>1]=P;P=((Ve[F+6>>1]|0)+8192+(Ve[L+6>>1]<<15)|0)>>>14&65535;Ve[n+6>>1]=P;Ve[e+6>>1]=P;P=((Ve[F+8>>1]|0)+8192+(Ve[L+8>>1]<<15)|0)>>>14&65535;Ve[n+8>>1]=P;Ve[e+8>>1]=P;P=((Ve[F+10>>1]|0)+8192+(Ve[L+10>>1]<<15)|0)>>>14&65535;Ve[n+10>>1]=P;Ve[e+10>>1]=P;P=((Ve[F+12>>1]|0)+8192+(Ve[L+12>>1]<<15)|0)>>>14&65535;Ve[n+12>>1]=P;Ve[e+12>>1]=P;P=((Ve[F+14>>1]|0)+8192+(Ve[L+14>>1]<<15)|0)>>>14&65535;Ve[n+14>>1]=P;Ve[e+14>>1]=P;P=((Ve[F+16>>1]|0)+8192+(Ve[L+16>>1]<<15)|0)>>>14&65535;Ve[n+16>>1]=P;Ve[e+16>>1]=P;P=((Ve[F+18>>1]|0)+8192+(Ve[L+18>>1]<<15)|0)>>>14&65535;Ve[n+18>>1]=P;Ve[e+18>>1]=P;P=((Ve[F+20>>1]|0)+8192+(Ve[L+20>>1]<<15)|0)>>>14&65535;Ve[n+20>>1]=P;Ve[e+20>>1]=P;Ke=I;return 0}return 0}function qi(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;n=e>>16;Ve[r>>1]=n;Ve[i>>1]=(e>>>1)-(n<<15);return}function Hi(e){e=e|0;return((e|0)==-2147483648?2147483647:0-e|0)|0}function Yi(e){e=e|0;var r=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(4)|0;if(!r){e=-1;return e|0}Ge[r>>2]=0;if(!((xi(r)|0)<<16>>16)){Ui(Ge[r>>2]|0)|0;Ge[e>>2]=r;e=0;return e|0}else{zi(r);zt(r);e=-1;return e|0}return 0}function Xi(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zi(r);zt(Ge[e>>2]|0);Ge[e>>2]=0;return}function Vi(e){e=e|0;if(!e){e=-1;return e|0}Ui(Ge[e>>2]|0)|0;e=0;return e|0}function Gi(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0;u=Ke;Ke=Ke+64|0;l=u+48|0;s=u+22|0;f=u;if((r|0)==7){i=Ge[o+116>>2]|0;Ur(n,10,f,s,Ge[o+112>>2]|0,a)|0;Ci(10,f,s,a);ji(Ge[e>>2]|0,f,s,t+22|0,l,a)|0;Ur(n,10,f,s,i,a)|0;Ci(10,f,s,a);ji(Ge[e>>2]|0,f,s,t+66|0,l,a)|0;Ke=u;return}else{Ur(i,10,f,s,Ge[o+108>>2]|0,a)|0;Ci(10,f,s,a);ji(Ge[e>>2]|0,f,s,t+66|0,l,a)|0;Ke=u;return}}function Wi(e,r,i,n,t,o,a,f,s,l){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;if((i|0)==6){Ve[t>>1]=on(e,r,n,20,143,80,o,a,f,s,l)|0;return}Ve[a>>1]=0;Ve[a+2>>1]=0;if(i>>>0<2){Ve[t>>1]=$i(r,i,n,20,143,160,f,s,l)|0;return}if(i>>>0<6){Ve[t>>1]=$i(r,i,n,20,143,80,f,s,l)|0;return}else{Ve[t>>1]=$i(r,i,n,18,143,80,f,s,l)|0;return}}function Ki(e){e=e|0;var r=0;if((e|0)!=0?(Ge[e>>2]=0,r=Ut(2)|0,(r|0)!=0):0){Ve[r>>1]=0;Ge[e>>2]=r;r=0}else r=-1;return r|0}function Zi(e){e=e|0;if(!e)e=-1;else{Ve[e>>1]=0;e=0}return e|0}function Qi(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function Ji(e,r,i,n,t,o,a,f,s,l,u,c){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0;H=Ke;Ke=Ke+240|0;v=H+160|0;b=H+80|0;B=H;C=Ve[3558+(r*18|0)>>1]|0;q=Ve[3558+(r*18|0)+2>>1]|0;d=Ve[3558+(r*18|0)+4>>1]|0;x=Ve[3558+(r*18|0)+6>>1]|0;m=Ve[3558+(r*18|0)+12>>1]|0;w=Ve[3558+(r*18|0)+14>>1]|0;h=Ve[3558+(r*18|0)+16>>1]|0;e:do{switch(f<<16>>16){case 0:case 80:if(r>>>0<2&f<<16>>16==80){U=(We[e>>1]|0)-(m&65535)|0;U=(U<<16>>16|0)<(h<<16>>16|0)?h:U&65535;P=w<<16>>16;z=(U&65535)+P&65535;j=z<<16>>16>143;U=j?143-P&65535:U;z=j?143:z;j=1;break e}else{U=(We[i+((f<<16>>16!=0&1)<<1)>>1]|0)-(We[3558+(r*18|0)+8>>1]|0)|0;U=(U<<16>>16|0)<(h<<16>>16|0)?h:U&65535;P=Ve[3558+(r*18|0)+10>>1]|0;z=(U&65535)+P&65535;j=z<<16>>16>143;U=j?143-P&65535:U;z=j?143:z;j=0;break e}default:{U=(We[e>>1]|0)-(m&65535)|0;U=(U<<16>>16|0)<(h<<16>>16|0)?h:U&65535;P=w<<16>>16;z=(U&65535)+P&65535;j=z<<16>>16>143;U=j?143-P&65535:U;z=j?143:z;j=1}}}while(0);T=U&65535;f=T+65532|0;p=f&65535;I=(z&65535)+4&65535;P=f<<16>>16;f=0-(f&65535)|0;m=f&65535;fi(n+(f<<16>>16<<1)|0,o,v,a);f=a<<16>>16;A=f>>>1&65535;k=A<<16>>16==0;if(k)a=1;else{a=A;h=v;i=b;w=0;while(1){F=Ve[h>>1]|0;Ve[i>>1]=F>>>2;F=(Ze(F,F)|0)+w|0;w=Ve[h+2>>1]|0;Ve[i+2>>1]=w>>>2;w=F+(Ze(w,w)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{h=h+4|0;i=i+4|0}}a=(w|0)<33554433}F=a?0:2;_=a?v:b;E=a?v:b;e:do{if(p<<16>>16<=I<<16>>16){g=f+-1|0;N=_+(g<<1)|0;O=o+(g<<1)|0;L=_+(f+-2<<1)|0;S=g>>>1;R=S&65535;y=R<<16>>16==0;M=a?12:14;S=(S<<1)+131070&131070;i=f+-3-S|0;D=_+(i<<1)|0;S=_+(f+-4-S<<1)|0;o=o+(i<<1)|0;if(!k){k=P;while(1){b=A;v=E;h=t;w=0;a=0;while(1){b=b+-1<<16>>16;f=Ve[v>>1]|0;w=(Ze(f,Ve[h>>1]|0)|0)+w|0;f=(Ze(f,f)|0)+a|0;a=Ve[v+2>>1]|0;w=w+(Ze(a,Ve[h+2>>1]|0)|0)|0;a=f+(Ze(a,a)|0)|0;if(!(b<<16>>16))break;else{v=v+4|0;h=h+4|0}}v=at(a<<1,c)|0;a=v>>16;h=w<<1>>16;b=Ze(a,h)|0;b=(b|0)==1073741824?2147483647:b<<1;h=(Ze((v>>>1)-(a<<15)<<16>>16,h)|0)>>15;v=(h<<1)+b|0;v=(h^b|0)>0&(v^b|0)<0?(b>>>31)+2147483647|0:v;a=(Ze(a,w&32767)|0)>>15;b=v+(a<<1)|0;Ve[B+(k-P<<1)>>1]=(v^a|0)>0&(b^v|0)<0?(v>>>31)+65535|0:b;if(p<<16>>16!=I<<16>>16){m=m+-1<<16>>16;b=Ve[n+(m<<16>>16<<1)>>1]|0;if(y){v=g;a=L;w=O;h=N}else{v=R;a=L;w=O;h=N;while(1){k=(Ze(Ve[w>>1]|0,b)|0)>>M;Ve[h>>1]=k+(We[a>>1]|0);k=(Ze(Ve[w+-2>>1]|0,b)|0)>>M;Ve[h+-2>>1]=k+(We[a+-2>>1]|0);v=v+-1<<16>>16;if(!(v<<16>>16)){v=i;a=S;w=o;h=D;break}else{a=a+-4|0;w=w+-4|0;h=h+-4|0}}}k=(Ze(Ve[w>>1]|0,b)|0)>>M;Ve[h>>1]=k+(We[a>>1]|0);Ve[_+(v+-1<<1)>>1]=b>>F}p=p+1<<16>>16;if(p<<16>>16>I<<16>>16)break e;else k=p<<16>>16}}if(y){a=_+(f+-2<<1)|0;w=P;while(1){at(0,c)|0;Ve[B+(w-P<<1)>>1]=0;if(p<<16>>16!=I<<16>>16){m=m+-1<<16>>16;t=Ve[n+(m<<16>>16<<1)>>1]|0;R=(Ze(Ve[O>>1]|0,t)|0)>>M;Ve[N>>1]=R+(We[L>>1]|0);Ve[a>>1]=t>>F}p=p+1<<16>>16;if(p<<16>>16>I<<16>>16)break e;else w=p<<16>>16}}v=_+(i+-1<<1)|0;a=P;while(1){at(0,c)|0;Ve[B+(a-P<<1)>>1]=0;if(p<<16>>16!=I<<16>>16){m=m+-1<<16>>16;a=Ve[n+(m<<16>>16<<1)>>1]|0;w=R;h=L;i=O;f=N;while(1){t=(Ze(Ve[i>>1]|0,a)|0)>>M;Ve[f>>1]=t+(We[h>>1]|0);t=(Ze(Ve[i+-2>>1]|0,a)|0)>>M;Ve[f+-2>>1]=t+(We[h+-2>>1]|0);w=w+-1<<16>>16;if(!(w<<16>>16))break;else{h=h+-4|0;i=i+-4|0;f=f+-4|0}}t=(Ze(Ve[o>>1]|0,a)|0)>>M;Ve[D>>1]=t+(We[S>>1]|0);Ve[v>>1]=a>>F}p=p+1<<16>>16;if(p<<16>>16>I<<16>>16)break;else a=p<<16>>16}}}while(0);p=U<<16>>16;i=T+1&65535;if(i<<16>>16>z<<16>>16)o=U;else{m=U;f=Ve[B+(p-P<<1)>>1]|0;while(1){w=Ve[B+((i<<16>>16)-P<<1)>>1]|0;h=w<<16>>16<f<<16>>16;m=h?m:i;i=i+1<<16>>16;if(i<<16>>16>z<<16>>16){o=m;break}else f=h?f:w}}e:do{if(!(j<<16>>16==0?o<<16>>16>C<<16>>16:0)){if(!(r>>>0<4&j<<16>>16!=0)){m=B+((o<<16>>16)-P<<1)|0;w=Ti(m,d,q,c)|0;i=(d&65535)+1&65535;if(i<<16>>16<=x<<16>>16)while(1){h=Ti(m,i,q,c)|0;f=h<<16>>16>w<<16>>16;d=f?i:d;i=i+1<<16>>16;if(i<<16>>16>x<<16>>16)break;else w=f?h:w}if((r+-7|0)>>>0<2){x=d<<16>>16==-3;i=(x<<31>>31)+o<<16>>16;d=x?3:d;break}switch(d<<16>>16){case-2:{i=o+-1<<16>>16;d=1;break e}case 2:{i=o+1<<16>>16;d=-1;break e}default:{i=o;break e}}}C=Ve[e>>1]|0;C=((C<<16>>16)-p|0)>5?p+5&65535:C;f=z<<16>>16;C=(f-(C<<16>>16)|0)>4?f+65532&65535:C;f=o<<16>>16;i=C<<16>>16;if((f|0)==(i+-1|0)?1:o<<16>>16==C<<16>>16){m=B+(f-P<<1)|0;f=Ti(m,d,q,c)|0;i=(d&65535)+1&65535;if(i<<16>>16<=x<<16>>16)while(1){w=Ti(m,i,q,c)|0;h=w<<16>>16>f<<16>>16;d=h?i:d;i=i+1<<16>>16;if(i<<16>>16>x<<16>>16)break;else f=h?w:f}if((r+-7|0)>>>0<2){x=d<<16>>16==-3;i=(x<<31>>31)+o<<16>>16;d=x?3:d;break}switch(d<<16>>16){case-2:{i=o+-1<<16>>16;d=1;break e}case 2:{i=o+1<<16>>16;d=-1;break e}default:{i=o;break e}}}if((f|0)==(i+-2|0)){i=B+(f-P<<1)|0;f=Ti(i,0,q,c)|0;if((r|0)!=8){d=0;m=1;while(1){w=Ti(i,m,q,c)|0;h=w<<16>>16>f<<16>>16;d=h?m:d;m=m+1<<16>>16;if(m<<16>>16>x<<16>>16)break;else f=h?w:f}if((r+-7|0)>>>0>=2)switch(d<<16>>16){case-2:{i=o+-1<<16>>16;d=1;break e}case 2:{i=o+1<<16>>16;d=-1;break e}default:{i=o;break e}}}else d=0;x=d<<16>>16==-3;i=(x<<31>>31)+o<<16>>16;d=x?3:d;break}if((f|0)==(i+1|0)){m=B+(f-P<<1)|0;i=Ti(m,d,q,c)|0;f=(d&65535)+1&65535;if(f<<16>>16<=0)while(1){h=Ti(m,f,q,c)|0;w=h<<16>>16>i<<16>>16;d=w?f:d;f=f+1<<16>>16;if(f<<16>>16>0)break;else i=w?h:i}if((r+-7|0)>>>0<2){x=d<<16>>16==-3;i=(x<<31>>31)+o<<16>>16;d=x?3:d;break}switch(d<<16>>16){case-2:{i=o+-1<<16>>16;d=1;break e}case 2:{i=o+1<<16>>16;d=-1;break e}default:{i=o;break e}}}else{i=o;d=0}}else{i=o;d=0}}while(0);if((r+-7|0)>>>0>1){x=e;e=bi(i,d,Ve[e>>1]|0,U,z,j,r>>>0<4&1,c)|0;Ve[u>>1]=e;Ve[x>>1]=i;Ve[l>>1]=q;Ve[s>>1]=d;Ke=H;return i|0}else{c=ki(i,d,U,j,c)|0;Ve[u>>1]=c;Ve[e>>1]=i;Ve[l>>1]=q;Ve[s>>1]=d;Ke=H;return i|0}return 0}function $i(e,r,i,n,t,o,a,f,s){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;var l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0;S=Ke;Ke=Ke+1200|0;A=S+1188|0;_=S+580|0;D=S+578|0;y=S+576|0;b=S;E=S+582|0;g=(f|0)!=0;do{if(g)if(r>>>0<2){jn(e,1,s);break}else{jn(e,0,s);break}}while(0);k=t<<16>>16;c=0-k|0;u=i+(c<<1)|0;c=c&65535;m=o<<16>>16;do{if(c<<16>>16<o<<16>>16){w=c;h=u;c=0;while(1){p=Ve[h>>1]|0;c=(Ze(p<<1,p)|0)+c|0;if((c|0)<0)break;w=w+1<<16>>16;if(w<<16>>16>=o<<16>>16){v=14;break}else h=h+2|0}if((v|0)==14){if((c|0)<1048576){v=15;break}Yt(E|0,u|0,m+k<<1|0)|0;p=0;break}l=m+k|0;d=l>>>1;w=d&65535;if(!(w<<16>>16))c=E;else{p=((d<<1)+131070&131070)+2|0;m=p-k|0;h=E;while(1){Ve[h>>1]=(Ve[u>>1]|0)>>>3;Ve[h+2>>1]=(Ve[u+2>>1]|0)>>>3;w=w+-1<<16>>16;if(!(w<<16>>16))break;else{u=u+4|0;h=h+4|0}}u=i+(m<<1)|0;c=E+(p<<1)|0}if(!(l&1))p=3;else{Ve[c>>1]=(Ve[u>>1]|0)>>>3;p=3}}else v=15}while(0);if((v|0)==15){p=m+k|0;c=p>>>1;d=c&65535;if(!(d<<16>>16))c=E;else{m=((c<<1)+131070&131070)+2|0;h=m-k|0;w=E;while(1){Ve[w>>1]=Ve[u>>1]<<3;Ve[w+2>>1]=Ve[u+2>>1]<<3;d=d+-1<<16>>16;if(!(d<<16>>16))break;else{u=u+4|0;w=w+4|0}}u=i+(h<<1)|0;c=E+(m<<1)|0}if(!(p&1))p=-3;else{Ve[c>>1]=Ve[u>>1]<<3;p=-3}}m=b+(k<<2)|0;h=E+(k<<1)|0;Wr(h,o,t,n,m);l=(r|0)==7&1;c=n<<16>>16;u=c<<2;if((u|0)!=(c<<18>>16|0)){Ge[s>>2]=1;u=n<<16>>16>0?32767:-32768}w=en(e,m,h,p,l,o,t,u&65535,A,f,s)|0;c=c<<1;d=en(e,m,h,p,l,o,u+65535&65535,c&65535,_,f,s)|0;c=en(e,m,h,p,l,o,c+65535&65535,n,D,f,s)|0;if(a<<16>>16==1&g){Ii(m,h,o,t,n,y,s)|0;Un(e,Ve[y>>1]|0)}u=Ve[A>>1]|0;l=Ve[_>>1]|0;if(((u<<16>>16)*55706>>16|0)>=(l<<16>>16|0)){_=u;A=w;_=_<<16>>16;_=_*55706|0;_=_>>16;D=Ve[D>>1]|0;D=D<<16>>16;D=(_|0)<(D|0);D=D?c:A;Ke=S;return D|0}Ve[A>>1]=l;_=l;A=d;_=_<<16>>16;_=_*55706|0;_=_>>16;D=Ve[D>>1]|0;D=D<<16>>16;D=(_|0)<(D|0);D=D?c:A;Ke=S;return D|0}function en(e,r,i,n,t,o,a,f,s,l,u){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;var c=0,d=0,h=0,w=0,m=0;if(a<<16>>16<f<<16>>16){f=-2147483648;h=a}else{h=a;c=-2147483648;d=r+(0-(a<<16>>16)<<2)|0;r=a;while(1){a=Ge[d>>2]|0;m=(a|0)<(c|0);r=m?r:h;c=m?c:a;h=h+-1<<16>>16;if(h<<16>>16<f<<16>>16){f=c;h=r;break}else d=d+4|0}}r=o<<16>>16>>>2&65535;if(!(r<<16>>16))r=0;else{c=r;a=i+(0-(h<<16>>16)<<1)|0;r=0;while(1){m=Ve[a>>1]|0;m=(Ze(m,m)|0)+r|0;r=Ve[a+2>>1]|0;r=m+(Ze(r,r)|0)|0;m=Ve[a+4>>1]|0;m=r+(Ze(m,m)|0)|0;r=Ve[a+6>>1]|0;r=m+(Ze(r,r)|0)|0;c=c+-1<<16>>16;if(!(c<<16>>16))break;else a=a+8|0}r=r<<1}if(l)zn(e,f,r,u);r=at(r,u)|0;a=t<<16>>16!=0;if(a)r=(r|0)>1073741823?2147483647:r<<1;t=f>>16;e=r>>16;u=Ze(e,t)|0;u=(u|0)==1073741824?2147483647:u<<1;r=(Ze((r>>>1)-(e<<15)<<16>>16,t)|0)>>15;m=(r<<1)+u|0;m=(r^u|0)>0&(m^u|0)<0?(u>>>31)+2147483647|0:m;t=(Ze(e,(f>>>1)-(t<<15)<<16>>16)|0)>>15;r=m+(t<<1)|0;r=(m^t|0)>0&(r^m|0)<0?(m>>>31)+2147483647|0:r;if(!a){Ve[s>>1]=r;return h|0}a=n<<16>>16;if(n<<16>>16>0)if(n<<16>>16<31){a=r>>a;w=16}else a=0;else{w=0-a<<16>>16;a=r<<w;a=(a>>w|0)==(r|0)?a:r>>31^2147483647;w=16}if((w|0)==16){if((a|0)>65535){Ve[s>>1]=32767;return h|0}if((a|0)<-65536){Ve[s>>1]=-32768;return h|0}}Ve[s>>1]=a>>>1;return h|0}function rn(e){e=e|0;var r=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(6)|0;if(!r){e=-1;return e|0}Ve[r>>1]=40;Ve[r+2>>1]=0;Ve[r+4>>1]=0;Ge[e>>2]=r;e=0;return e|0}function nn(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=40;Ve[e+2>>1]=0;Ve[e+4>>1]=0;e=0;return e|0}function tn(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function on(e,r,i,n,t,o,a,f,s,l,u){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;var c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0;M=Ke;Ke=Ke+1200|0;k=M+1186|0;E=M+1184|0;R=M+1182|0;b=M;y=M+576|0;g=t<<16>>16;S=y+(g<<1)|0;c=(0-g&65535)<<16>>16<o<<16>>16;if(c){m=0-t<<16>>16<<16>>16;d=0;do{w=Ve[i+(m<<1)>>1]|0;w=Ze(w,w)|0;if((w|0)!=1073741824){h=(w<<1)+d|0;if((w^d|0)>0&(h^d|0)<0){Ge[u>>2]=1;d=(d>>>31)+2147483647|0}else d=h}else{Ge[u>>2]=1;d=2147483647}m=m+1|0}while((m&65535)<<16>>16!=o<<16>>16)}else d=0;if((2147483646-d&d|0)>=0)if((d|0)==2147483647){if(c){d=0-t<<16>>16<<16>>16;do{Ve[y+(d+g<<1)>>1]=It(Ve[i+(d<<1)>>1]|0,3,u)|0;d=d+1|0}while((d&65535)<<16>>16!=o<<16>>16)}}else p=14;else{Ge[u>>2]=1;p=14}do{if((p|0)==14){if((1048575-d&d|0)<0){Ge[u>>2]=1;d=(d>>>31)+2147483647|0}else d=d+-1048576|0;if((d|0)>=0){if(!c)break;D=0-t<<16>>16<<16>>16;Yt(y+(g+D<<1)|0,i+(D<<1)|0,(((o+t<<16>>16)+-1&65535)<<1)+2|0)|0;break}if(c){d=0-t<<16>>16<<16>>16;do{D=Ve[i+(d<<1)>>1]|0;Ve[y+(d+g<<1)>>1]=(D<<19>>19|0)==(D|0)?D<<3:D>>>15^32767;d=d+1|0}while((d&65535)<<16>>16!=o<<16>>16)}}}while(0);A=b+(g<<2)|0;Wr(S,o,t,n,A);m=Ve[e>>1]|0;D=e+4|0;_=f+(s<<16>>16<<1)|0;e:do{if(t<<16>>16<n<<16>>16)v=t;else{if((Ve[D>>1]|0)<=0){i=t;f=-2147483648;w=t;p=3402;while(1){qi(Ge[b+(g-(i<<16>>16)<<2)>>2]|0,k,E,u);h=Ve[E>>1]|0;d=Ve[p>>1]|0;m=Ze(d,Ve[k>>1]|0)|0;if((m|0)==1073741824){Ge[u>>2]=1;c=2147483647}else c=m<<1;v=(Ze(d,h<<16>>16)|0)>>15;m=c+(v<<1)|0;if((c^v|0)>0&(m^c|0)<0){Ge[u>>2]=1;m=(c>>>31)+2147483647|0}h=(m|0)<(f|0);w=h?w:i;i=i+-1<<16>>16;if(i<<16>>16<n<<16>>16){v=w;break e}else{f=h?f:m;p=p+-2|0}}}f=t;c=-2147483648;w=t;v=2902+(g+123-(m<<16>>16)<<1)|0;i=3402;while(1){qi(Ge[b+(g-(f<<16>>16)<<2)>>2]|0,k,E,u);p=Ve[E>>1]|0;h=Ve[i>>1]|0;m=Ze(h,Ve[k>>1]|0)|0;if((m|0)==1073741824){Ge[u>>2]=1;d=2147483647}else d=m<<1;p=(Ze(h,p<<16>>16)|0)>>15;m=d+(p<<1)|0;if((d^p|0)>0&(m^d|0)<0){Ge[u>>2]=1;m=(d>>>31)+2147483647|0}qi(m,k,E,u);p=Ve[E>>1]|0;h=Ve[v>>1]|0;m=Ze(h,Ve[k>>1]|0)|0;if((m|0)==1073741824){Ge[u>>2]=1;d=2147483647}else d=m<<1;p=(Ze(h,p<<16>>16)|0)>>15;m=d+(p<<1)|0;if((d^p|0)>0&(m^d|0)<0){Ge[u>>2]=1;m=(d>>>31)+2147483647|0}h=(m|0)<(c|0);w=h?w:f;f=f+-1<<16>>16;if(f<<16>>16<n<<16>>16){v=w;break}else{c=h?c:m;v=v+-2|0;i=i+-2|0}}}}while(0);if(o<<16>>16>0){f=0;i=S;p=y+(g-(v<<16>>16)<<1)|0;w=0;d=0;while(1){m=Ve[p>>1]|0;h=Ze(m,Ve[i>>1]|0)|0;if((h|0)!=1073741824){c=(h<<1)+w|0;if((h^w|0)>0&(c^w|0)<0){Ge[u>>2]=1;w=(w>>>31)+2147483647|0}else w=c}else{Ge[u>>2]=1;w=2147483647}c=Ze(m,m)|0;if((c|0)!=1073741824){h=(c<<1)+d|0;if((c^d|0)>0&(h^d|0)<0){Ge[u>>2]=1;d=(d>>>31)+2147483647|0}else d=h}else{Ge[u>>2]=1;d=2147483647}f=f+1<<16>>16;if(f<<16>>16>=o<<16>>16)break;else{i=i+2|0;p=p+2|0}}}else{w=0;d=0}h=(l|0)==0;if(!h){jn(r,0,u);zn(r,w,d,u)}c=(Ft(d,u)|0)<<16>>16;if((c*13107|0)==1073741824){Ge[u>>2]=1;d=2147483647}else d=c*26214|0;c=w-d|0;if(((c^w)&(d^w)|0)<0){Ge[u>>2]=1;c=(w>>>31)+2147483647|0}l=Ft(c,u)|0;Ve[_>>1]=l;if(l<<16>>16>0){c=a+6|0;Ve[a+8>>1]=Ve[c>>1]|0;l=a+4|0;Ve[c>>1]=Ve[l>>1]|0;c=a+2|0;Ve[l>>1]=Ve[c>>1]|0;Ve[c>>1]=Ve[a>>1]|0;Ve[a>>1]=v;Ve[e>>1]=rt(a,5)|0;Ve[e+2>>1]=32767;c=32767}else{Ve[e>>1]=v;e=e+2|0;c=((Ve[e>>1]|0)*29491|0)>>>15&65535;Ve[e>>1]=c}Ve[D>>1]=((Ct(c,9830,u)|0)&65535)>>>15^1;if(h){Ke=M;return v|0}if((Ct(s,1,u)|0)<<16>>16){Ke=M;return v|0}Ii(A,S,o,t,n,R,u)|0;Un(r,Ve[R>>1]|0);Ke=M;return v|0}function an(e,r,i,n,t,o,a,f,s,l){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;var u=0,c=0;l=Ke;Ke=Ke+48|0;c=l+22|0;u=l;r=e>>>0<6?r:i;i=o<<16>>16>0?22:0;e=t+(i<<1)|0;xt(e,r,c);xt(e,n,u);e=o<<16>>16;o=s+(e<<1)|0;Lt(c,a+(e<<1)|0,o,40);Bt(u,o,o,40,f,1);i=t+(((i<<16)+720896|0)>>>16<<1)|0;xt(i,r,c);xt(i,n,u);e=(e<<16)+2621440>>16;s=s+(e<<1)|0;Lt(c,a+(e<<1)|0,s,40);Bt(u,s,s,40,f,1);Ke=l;return}function fn(e){e=e|0;var r=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(12)|0;if(!r){e=-1;return e|0}Ve[r>>1]=0;Ve[r+2>>1]=0;Ve[r+4>>1]=0;Ve[r+6>>1]=0;Ve[r+8>>1]=0;Ve[r+10>>1]=0;Ge[e>>2]=r;e=0;return e|0}function sn(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;e=0;return e|0}function ln(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function un(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0;c=e+10|0;t=Ve[c>>1]|0;d=e+8|0;n=Ve[d>>1]|0;if(!(i<<16>>16)){e=n;u=t;Ve[c>>1]=u;Ve[d>>1]=e;return}f=e+4|0;s=e+6|0;l=e+2|0;a=Ve[s>>1]|0;u=Ve[f>>1]|0;o=i;i=t;while(1){h=(Ze(Ve[e>>1]|0,-3733)|0)+(((u<<16>>16)*7807|0)+((a<<16>>16)*7807>>15))|0;Ve[e>>1]=u;h=h+((Ze(Ve[l>>1]|0,-3733)|0)>>15)|0;Ve[l>>1]=a;h=((i<<16>>16)*1899|0)+h+(Ze(n<<16>>16,-3798)|0)|0;i=Ve[r>>1]|0;h=h+((i<<16>>16)*1899|0)|0;Ve[r>>1]=(h+2048|0)>>>12;t=h>>>12;u=t&65535;Ve[f>>1]=u;a=(h<<3)-(t<<15)&65535;Ve[s>>1]=a;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{h=n;r=r+2|0;n=i;i=h}}Ve[c>>1]=n;Ve[d>>1]=i;return}function cn(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0;t=Ve[(Ge[n+88>>2]|0)+(e<<1)>>1]|0;if(!(t<<16>>16))return;f=i;a=Ge[(Ge[n+92>>2]|0)+(e<<2)>>2]|0;while(1){i=Ve[a>>1]|0;if(!(i<<16>>16))i=0;else{e=Ve[r>>1]|0;o=i;n=f+((i<<16>>16)+-1<<1)|0;while(1){i=e<<16>>16;Ve[n>>1]=i&1;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{e=i>>>1&65535;n=n+-2|0}}i=Ve[a>>1]|0}r=r+2|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{f=f+(i<<16>>16<<1)|0;a=a+2|0}}return}function dn(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0;u=Ke;Ke=Ke+16|0;s=u+2|0;l=u;a=t<<16>>16;if(t<<16>>16<1){o=-5443;l=-32768;Jn(e,l,o);Ke=u;return}f=gt(14,i,o)|0;if((a|0)<(f<<16>>16|0))i=n;else{i=(n&65535)+1&65535;t=a>>>1&65535}n=Kn(t,f&65535)|0;Ve[l>>1]=n;ft(n<<16>>16,s,l,o);Ve[s>>1]=((((i&65535)-(r&65535)<<16)+-65536|0)>>>16)+(We[s>>1]|0);n=Tt(Ve[l>>1]|0,5,o)|0;a=Ve[s>>1]|0;n=((a&65535)<<10)+(n&65535)&65535;if(n<<16>>16>18284){o=3037;l=18284;Jn(e,l,o);Ke=u;return}t=Ve[l>>1]|0;a=a<<16>>16;if((a*24660|0)==1073741824){Ge[o>>2]=1;i=2147483647}else i=a*49320|0;l=(t<<16>>16)*24660>>15;a=i+(l<<1)|0;if((i^l|0)>0&(a^i|0)<0){Ge[o>>2]=1;a=(i>>>31)+2147483647|0}l=a<<13;o=Ft((l>>13|0)==(a|0)?l:a>>31^2147483647,o)|0;l=n;Jn(e,l,o);Ke=u;return}function hn(e,r,i,n,t,o,a,f,s,l,u,c,d,h,w,m,p,v,b,k){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;p=p|0;v=v|0;b=b|0;k=k|0;var E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0,G=0,W=0,K=0,Z=0,Q=0,J=0,$=0,ee=0,re=0,ie=0;ie=Ke;Ke=Ke+80|0;J=ie+66|0;$=ie+64|0;ee=ie+62|0;re=ie+60|0;C=ie+40|0;B=ie+20|0;T=ie;Ve[J>>1]=r;Ve[$>>1]=s;Ve[ee>>1]=l;I=gt(14,i,k)|0;Q=I&65535;Ve[re>>1]=Q;P=gt(14,l,k)|0;F=(We[n>>1]|0)+65523|0;Ve[T>>1]=F;S=(We[n+2>>1]|0)+65522|0;R=T+2|0;Ve[R>>1]=S;M=((r&65535)<<16)+-720896|0;_=M>>16;M=(M>>>15)+15+(We[n+4>>1]|0)|0;N=T+4|0;Ve[N>>1]=M;O=(We[n+6>>1]|0)+_|0;L=T+6|0;Ve[L>>1]=O;_=_+1+(We[n+8>>1]|0)|0;A=T+8|0;Ve[A>>1]=_;E=(We[u>>1]|0)+65523&65535;Ve[T+10>>1]=E;D=(We[u+2>>1]|0)+65522&65535;Ve[T+12>>1]=D;g=((s&65535)<<16)+-720896|0;n=g>>16;g=(g>>>15)+15+(We[u+4>>1]|0)&65535;Ve[T+14>>1]=g;y=(We[u+6>>1]|0)+n&65535;Ve[T+16>>1]=y;n=n+1+(We[u+8>>1]|0)&65535;Ve[T+18>>1]=n;K=(o&65535)-(d&65535)<<16;s=K>>16;if((K|0)>0){l=a;i=h<<16>>16>>s&65535}else{l=a<<16>>16>>0-s&65535;i=h}if((Tt(i,1,k)|0)<<16>>16>l<<16>>16)i=1;else i=(((l<<16>>16)+3>>2|0)>(i<<16>>16|0))<<31>>31;u=F+i&65535;Ve[T>>1]=u;K=S+i&65535;Ve[R>>1]=K;W=M+i&65535;Ve[N>>1]=W;G=O+i&65535;Ve[L>>1]=G;V=_+i&65535;Ve[A>>1]=V;s=n<<16>>16>u<<16>>16?n:u;s=y<<16>>16>s<<16>>16?y:s;s=g<<16>>16>s<<16>>16?g:s;s=D<<16>>16>s<<16>>16?D:s;s=E<<16>>16>s<<16>>16?E:s;s=V<<16>>16>s<<16>>16?V:s;s=G<<16>>16>s<<16>>16?G:s;s=W<<16>>16>s<<16>>16?W:s;s=(K<<16>>16>s<<16>>16?K:s)+1&65535;n=0;while(1){i=s-(u&65535)|0;u=i&65535;l=We[t>>1]<<16;i=i<<16>>16;if(u<<16>>16>0)u=u<<16>>16<31?l>>i:0;else{K=0-i<<16>>16;u=l<<K;u=(u>>K|0)==(l|0)?u:l>>31^2147483647}K=u>>16;Ve[C+(n<<1)>>1]=K;Ve[B+(n<<1)>>1]=(u>>>1)-(K<<15);n=n+1|0;if((n|0)==5){i=5;l=c;break}u=Ve[T+(n<<1)>>1]|0;t=t+2|0}while(1){n=s-(E&65535)|0;E=n&65535;u=We[l>>1]<<16;n=n<<16>>16;if(E<<16>>16>0)u=E<<16>>16<31?u>>n:0;else{W=0-n<<16>>16;K=u<<W;u=(K>>W|0)==(u|0)?K:u>>31^2147483647}K=u>>16;Ve[C+(i<<1)>>1]=K;Ve[B+(i<<1)>>1]=(u>>>1)-(K<<15);u=i+1|0;if((u&65535)<<16>>16==10)break;E=Ve[T+(u<<1)>>1]|0;i=u;l=l+2|0}x=I<<16>>16;U=Ve[C>>1]|0;z=Ve[B>>1]|0;j=Ve[C+2>>1]|0;q=Ve[B+2>>1]|0;H=Ve[C+4>>1]|0;Y=Ve[B+4>>1]|0;X=Ve[C+6>>1]|0;V=Ve[B+6>>1]|0;G=Ve[C+8>>1]|0;W=Ve[B+8>>1]|0;K=w&65535;d=P<<16>>16;o=Ve[C+10>>1]|0;y=Ve[B+10>>1]|0;g=Ve[C+12>>1]|0;t=Ve[B+12>>1]|0;i=Ve[C+14>>1]|0;l=Ve[B+14>>1]|0;n=Ve[C+16>>1]|0;E=Ve[B+16>>1]|0;_=Ve[C+18>>1]|0;B=Ve[B+18>>1]|0;s=2147483647;C=0;u=0;A=782;do{T=Ve[A>>1]|0;O=(Ze(x,Ve[A+2>>1]|0)|0)>>>15<<16;c=O>>16;M=T<<1;F=(Ze(M,T)|0)>>16;h=Ze(F,U)|0;if((h|0)==1073741824){Ge[k>>2]=1;L=2147483647}else L=h<<1;P=(Ze(z,F)|0)>>15;h=L+(P<<1)|0;if((L^P|0)>0&(h^L|0)<0){Ge[k>>2]=1;h=(L>>>31)+2147483647|0}F=Ze(j,T)|0;if((F|0)==1073741824){Ge[k>>2]=1;L=2147483647}else L=F<<1;P=(Ze(q,T)|0)>>15;F=L+(P<<1)|0;if((L^P|0)>0&(F^L|0)<0){Ge[k>>2]=1;F=(L>>>31)+2147483647|0}O=(Ze(O>>15,c)|0)>>16;L=Ze(H,O)|0;if((L|0)==1073741824){Ge[k>>2]=1;N=2147483647}else N=L<<1;P=(Ze(Y,O)|0)>>15;L=N+(P<<1)|0;if((N^P|0)>0&(L^N|0)<0){Ge[k>>2]=1;L=(N>>>31)+2147483647|0}O=Ze(X,c)|0;if((O|0)==1073741824){Ge[k>>2]=1;N=2147483647}else N=O<<1;P=(Ze(V,c)|0)>>15;O=N+(P<<1)|0;if((N^P|0)>0&(O^N|0)<0){Ge[k>>2]=1;P=(N>>>31)+2147483647|0}else P=O;N=(Ze(M,c)|0)>>16;O=Ze(G,N)|0;if((O|0)==1073741824){Ge[k>>2]=1;M=2147483647}else M=O<<1;I=(Ze(W,N)|0)>>15;O=M+(I<<1)|0;if((M^I|0)>0&(O^M|0)<0){Ge[k>>2]=1;O=(M>>>31)+2147483647|0}N=Ve[A+4>>1]|0;M=Ve[A+6>>1]|0;A=A+8|0;if((T-K&65535)<<16>>16<1?(Z=N<<16>>16,N<<16>>16<=w<<16>>16):0){S=(Ze(M<<16>>16,d)|0)>>>15<<16;T=S>>16;D=Z<<1;M=(Ze(D,Z)|0)>>16;N=Ze(o,M)|0;if((N|0)==1073741824){Ge[k>>2]=1;R=2147483647}else R=N<<1;I=(Ze(y,M)|0)>>15;N=R+(I<<1)|0;if((R^I|0)>0&(N^R|0)<0){Ge[k>>2]=1;N=(R>>>31)+2147483647|0}M=Ze(g,Z)|0;if((M|0)==1073741824){Ge[k>>2]=1;R=2147483647}else R=M<<1;I=(Ze(t,Z)|0)>>15;M=R+(I<<1)|0;if((R^I|0)>0&(M^R|0)<0){Ge[k>>2]=1;I=(R>>>31)+2147483647|0}else I=M;R=(Ze(S>>15,T)|0)>>16;M=Ze(i,R)|0;if((M|0)==1073741824){Ge[k>>2]=1;S=2147483647}else S=M<<1;c=(Ze(l,R)|0)>>15;M=S+(c<<1)|0;if((S^c|0)>0&(M^S|0)<0){Ge[k>>2]=1;c=(S>>>31)+2147483647|0}else c=M;M=Ze(n,T)|0;if((M|0)==1073741824){Ge[k>>2]=1;R=2147483647}else R=M<<1;S=(Ze(E,T)|0)>>15;M=R+(S<<1)|0;if((R^S|0)>0&(M^R|0)<0){Ge[k>>2]=1;a=(R>>>31)+2147483647|0}else a=M;R=(Ze(D,T)|0)>>16;M=Ze(_,R)|0;if((M|0)==1073741824){Ge[k>>2]=1;S=2147483647}else S=M<<1;T=(Ze(B,R)|0)>>15;M=S+(T<<1)|0;if((S^T|0)>0&(M^S|0)<0){Ge[k>>2]=1;M=(S>>>31)+2147483647|0}T=F+h+L+P+O+N+I+c+a+M|0;P=(T|0)<(s|0);s=P?T:s;u=P?C:u}C=C+1<<16>>16}while(C<<16>>16<256);w=(u&65535)<<18>>16;wn(e,782+(w<<1)|0,Q,r,m,p,k);Qn(e,0,f,$,ee,J,re,k);f=(gt(14,Ve[ee>>1]|0,k)|0)&65535;wn(e,782+((w|2)<<1)|0,f,Ve[$>>1]|0,v,b,k);Ke=ie;return u|0}function wn(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0;u=Ke;Ke=Ke+16|0;s=u+2|0;l=u;Ve[t>>1]=Ve[r>>1]|0;f=Ve[r+2>>1]|0;i=Ze(i<<16>>16<<1,f)|0;t=10-(n&65535)|0;r=t&65535;t=t<<16>>16;if(r<<16>>16>0)r=r<<16>>16<31?i>>t:0;else{t=0-t<<16>>16;r=i<<t;r=(r>>t|0)==(i|0)?r:i>>31^2147483647}Ve[o>>1]=r>>>16;ft(f,s,l,a);Ve[s>>1]=(We[s>>1]|0)+65524;t=Tt(Ve[l>>1]|0,5,a)|0;n=Ve[s>>1]|0;t=((n&65535)<<10)+(t&65535)&65535;i=Ve[l>>1]|0;n=n<<16>>16;if((n*24660|0)==1073741824){Ge[a>>2]=1;r=2147483647}else r=n*49320|0;l=(i<<16>>16)*24660>>15;n=r+(l<<1)|0;if(!((r^l|0)>0&(n^r|0)<0)){a=n;a=a<<13;a=a+32768|0;a=a>>>16;a=a&65535;Jn(e,t,a);Ke=u;return}Ge[a>>2]=1;a=(r>>>31)+2147483647|0;a=a<<13;a=a+32768|0;a=a>>>16;a=a&65535;Jn(e,t,a);Ke=u;return}function mn(e,r,i,n,t,o,a,f,s,l,u,c,d,h,w,m,p,v,b,k,E){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;p=p|0;v=v|0;b=b|0;k=k|0;E=E|0;var g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0,G=0,W=0,K=0,Z=0,Q=0,J=0,$=0,ee=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0;ae=Ke;Ke=Ke+80|0;ne=ae+72|0;te=ae+70|0;oe=ae+68|0;re=ae+66|0;ie=ae+56|0;G=ae+24|0;V=ae+12|0;Y=ae+48|0;X=ae+40|0;U=ae+34|0;j=ae+22|0;B=ae+6|0;x=ae;vn(5,h,w,B,x,Ge[k+72>>2]|0,E)|0;_=gt(14,l,E)|0;z=k+68|0;C=Ge[z>>2]|0;H=s<<16>>16;q=H+65526|0;h=(We[o>>1]|0)+65523&65535;Ve[ie>>1]=h;k=(We[o+2>>1]|0)+65522&65535;Ve[ie+2>>1]=k;J=q<<16>>16;$=((q<<17>>17|0)==(J|0)?q<<1:J>>>15^32767)+15+(We[o+4>>1]|0)&65535;Ve[ie+4>>1]=$;ee=(We[o+6>>1]|0)+J&65535;Ve[ie+6>>1]=ee;o=J+1+(We[o+8>>1]|0)&65535;Ve[ie+8>>1]=o;k=k<<16>>16>h<<16>>16?k:h;k=$<<16>>16>k<<16>>16?$:k;k=ee<<16>>16>k<<16>>16?ee:k;k=(Gn(o<<16>>16>k<<16>>16?o:k,1,E)|0)&65535;o=h;h=0;while(1){l=k-(o&65535)|0;o=l&65535;y=We[t+(h<<1)>>1]<<16;l=l<<16>>16;if(o<<16>>16>0)l=o<<16>>16<31?y>>l:0;else{ee=0-l<<16>>16;l=y<<ee;l=(l>>ee|0)==(y|0)?l:y>>31^2147483647}qi(l,G+(h<<1)|0,V+(h<<1)|0,E);l=h+1|0;if((l|0)==5)break;o=Ve[ie+(l<<1)>>1]|0;h=l}T=G+2|0;P=V+2|0;ee=_<<16>>16;W=G+4|0;K=V+4|0;Z=G+6|0;Q=V+6|0;J=G+8|0;$=V+8|0;S=0;o=2147483647;t=0;l=0;while(1){I=Ve[B+(t<<1)>>1]|0;_=Ze(I,I)|0;if(_>>>0>1073741823){Ge[E>>2]=1;_=32767}else _=_>>>15;k=Ve[V>>1]|0;y=_<<16>>16;_=Ze(y,Ve[G>>1]|0)|0;if((_|0)==1073741824){Ge[E>>2]=1;h=2147483647}else h=_<<1;F=(Ze(k<<16>>16,y)|0)>>15;_=h+(F<<1)|0;if((h^F|0)>0&(_^h|0)<0){Ge[E>>2]=1;_=(h>>>31)+2147483647|0}k=Ve[P>>1]|0;y=Ze(Ve[T>>1]|0,I)|0;if((y|0)!=1073741824){h=(y<<1)+_|0;if((y^_|0)>0&(h^_|0)<0){Ge[E>>2]=1;h=(_>>>31)+2147483647|0}}else{Ge[E>>2]=1;h=2147483647}_=(Ze(k<<16>>16,I)|0)>>15;if((_|0)>32767){Ge[E>>2]=1;_=32767}F=_<<16;_=(F>>15)+h|0;if((F>>16^h|0)>0&(_^h|0)<0){Ge[E>>2]=1;F=(h>>>31)+2147483647|0}else F=_;O=(F>>>31)+2147483647|0;L=t&65535;_=S;M=0;N=C;do{y=(Ze(Ve[N>>1]|0,ee)|0)>>15;N=N+6|0;if((y|0)>32767){Ge[E>>2]=1;y=32767}R=y<<16>>16;y=Ze(R,R)|0;if((y|0)==1073741824){Ge[E>>2]=1;D=2147483647}else D=y<<1;qi(D,ne,te,E);y=Ze(R,I)|0;if((y|0)==1073741824){Ge[E>>2]=1;D=2147483647}else D=y<<1;qi(D,oe,re,E);h=Ve[K>>1]|0;A=Ve[te>>1]|0;y=Ve[W>>1]|0;k=Ve[ne>>1]|0;S=Ze(k,y)|0;if((S|0)!=1073741824){D=(S<<1)+F|0;if((S^F|0)>0&(D^F|0)<0){Ge[E>>2]=1;D=O}}else{Ge[E>>2]=1;D=2147483647}S=(Ze(A<<16>>16,y)|0)>>15;if((S|0)>32767){Ge[E>>2]=1;S=32767}A=S<<16;S=(A>>15)+D|0;if((A>>16^D|0)>0&(S^D|0)<0){Ge[E>>2]=1;S=(D>>>31)+2147483647|0}D=(Ze(k,h<<16>>16)|0)>>15;if((D|0)>32767){Ge[E>>2]=1;D=32767}A=D<<16;D=(A>>15)+S|0;if((A>>16^S|0)>0&(D^S|0)<0){Ge[E>>2]=1;D=(S>>>31)+2147483647|0}y=Ve[Q>>1]|0;S=Ze(Ve[Z>>1]|0,R)|0;if((S|0)!=1073741824){A=(S<<1)+D|0;if((S^D|0)>0&(A^D|0)<0){Ge[E>>2]=1;A=(D>>>31)+2147483647|0}}else{Ge[E>>2]=1;A=2147483647}y=(Ze(y<<16>>16,R)|0)>>15;if((y|0)>32767){Ge[E>>2]=1;y=32767}R=y<<16;y=(R>>15)+A|0;if((R>>16^A|0)>0&(y^A|0)<0){Ge[E>>2]=1;y=(A>>>31)+2147483647|0}k=Ve[$>>1]|0;A=Ve[re>>1]|0;h=Ve[J>>1]|0;g=Ve[oe>>1]|0;S=Ze(g,h)|0;do{if((S|0)==1073741824){Ge[E>>2]=1;S=2147483647}else{D=(S<<1)+y|0;if(!((S^y|0)>0&(D^y|0)<0)){S=D;break}Ge[E>>2]=1;S=(y>>>31)+2147483647|0}}while(0);D=(Ze(A<<16>>16,h)|0)>>15;if((D|0)>32767){Ge[E>>2]=1;D=32767}R=D<<16;D=(R>>15)+S|0;if((R>>16^S|0)>0&(D^S|0)<0){Ge[E>>2]=1;D=(S>>>31)+2147483647|0}y=(Ze(g,k<<16>>16)|0)>>15;if((y|0)>32767){Ge[E>>2]=1;y=32767}R=y<<16;y=(R>>15)+D|0;if((R>>16^D|0)>0&(y^D|0)<0){Ge[E>>2]=1;y=(D>>>31)+2147483647|0}R=(y|0)<(o|0);_=R?M:_;l=R?L:l;o=R?y:o;M=M+1<<16>>16}while(M<<16>>16<32);t=t+1|0;if((t|0)==3){y=_;t=l;break}else S=_}P=(y<<16>>16)*3|0;o=Ve[C+(P<<1)>>1]|0;Ve[p>>1]=Ve[C+(P+1<<1)>>1]|0;Ve[v>>1]=Ve[C+(P+2<<1)>>1]|0;o=Ze(o<<16>>16,ee)|0;if((o|0)==1073741824){Ge[E>>2]=1;_=2147483647}else _=o<<1;P=9-H|0;C=P&65535;P=P<<16>>16;T=C<<16>>16>0;if(T)_=C<<16>>16<31?_>>P:0;else{F=0-P<<16>>16;I=_<<F;_=(I>>F|0)==(_|0)?I:_>>31^2147483647}Ve[m>>1]=_>>>16;I=t<<16>>16;B=Ve[B+(I<<1)>>1]|0;Ve[w>>1]=B;x=Ve[x+(I<<1)>>1]|0;Kr(r,i,n,B,u,Y,X,U,E);Si(e,Ve[U>>1]|0,Ve[m>>1]|0,j,E);if(!((Ve[Y>>1]|0)!=0&(Ve[j>>1]|0)>0)){E=y;p=Ge[b>>2]|0;m=p+2|0;Ve[p>>1]=x;p=p+4|0;Ge[b>>2]=p;Ve[m>>1]=E;Ke=ae;return}R=Y+6|0;Ve[R>>1]=f;D=X+6|0;Ve[D>>1]=a;s=((Ct(d,s,E)|0)&65535)+10|0;k=s<<16>>16;if((s&65535)<<16>>16<0){l=0-k<<16;if((l|0)<983040)c=c<<16>>16>>(l>>16)&65535;else c=0}else{l=c<<16>>16;h=l<<k;if((h<<16>>16>>k|0)==(l|0))c=h&65535;else c=(l>>>15^32767)&65535}o=Ve[w>>1]|0;_=Ve[j>>1]|0;z=Ge[z>>2]|0;h=Ve[m>>1]|0;j=10-H|0;k=j<<16>>16;if((j&65535)<<16>>16<0){l=0-k<<16;if((l|0)<983040)f=h<<16>>16>>(l>>16)&65535;else f=0}else{l=h<<16>>16;h=l<<k;if((h<<16>>16>>k|0)==(l|0))f=h&65535;else f=(l>>>15^32767)&65535}t=o<<16>>16;l=Ze(t,t)|0;if(l>>>0>1073741823){Ge[E>>2]=1;o=32767}else o=l>>>15;y=Gn(32767-(_&65535)&65535,1,E)|0;_=_<<16>>16;l=Ze(Ve[Y+2>>1]|0,_)|0;if((l|0)==1073741824){Ge[E>>2]=1;l=2147483647}else l=l<<1;j=l<<1;l=Ze(((j>>1|0)==(l|0)?j:l>>31^2147418112)>>16,o<<16>>16)|0;if((l|0)==1073741824){Ge[E>>2]=1;S=2147483647}else S=l<<1;A=(We[X+2>>1]|0)+65521|0;k=A&65535;l=Ze(Ve[Y+4>>1]|0,_)|0;if((l|0)==1073741824){Ge[E>>2]=1;o=2147483647}else o=l<<1;l=o<<1;l=(Ze(((l>>1|0)==(o|0)?l:o>>31^2147418112)>>16,t)|0)>>15;if((l|0)>32767){Ge[E>>2]=1;l=32767}Ve[W>>1]=l;o=q&65535;Ve[ne>>1]=o;o=Gn(Ve[X+4>>1]|0,o,E)|0;l=Ze(Ve[R>>1]|0,_)|0;if((l|0)==1073741824){Ge[E>>2]=1;l=2147483647}else l=l<<1;g=l<<1;Ve[Z>>1]=((g>>1|0)==(l|0)?g:l>>31^2147418112)>>>16;g=((H<<17>>17|0)==(H|0)?H<<1:H>>>15^32767)+65529&65535;Ve[ne>>1]=g;g=Gn(Ve[D>>1]|0,g,E)|0;l=(Ze(Ve[R>>1]|0,y<<16>>16)|0)>>15;if((l|0)>32767){Ge[E>>2]=1;l=32767}Ve[J>>1]=l;y=Gn(g,1,E)|0;h=Ze(Ve[Y>>1]|0,_)|0;if((h|0)==1073741824){Ge[E>>2]=1;l=2147483647}else l=h<<1;D=Pt(l,ne,E)|0;t=(We[ne>>1]|0)+47|0;Ve[ne>>1]=t;t=(We[X>>1]|0)-(t&65535)|0;_=t+31&65535;_=k<<16>>16>_<<16>>16?k:_;_=o<<16>>16>_<<16>>16?o:_;_=g<<16>>16>_<<16>>16?g:_;_=(y<<16>>16>_<<16>>16?y:_)<<16>>16;h=_-(A&65535)|0;l=h&65535;h=h<<16>>16;if(l<<16>>16>0)F=l<<16>>16<31?S>>h:0;else{X=0-h<<16>>16;F=S<<X;F=(F>>X|0)==(S|0)?F:S>>31^2147483647}k=_-(o&65535)|0;l=k&65535;h=We[W>>1]<<16;k=k<<16>>16;if(l<<16>>16>0)h=l<<16>>16<31?h>>k:0;else{Y=0-k<<16>>16;X=h<<Y;h=(X>>Y|0)==(h|0)?X:h>>31^2147483647}qi(h,W,K,E);g=_-(g&65535)|0;h=g&65535;k=We[Z>>1]<<16;g=g<<16>>16;if(h<<16>>16>0)h=h<<16>>16<31?k>>g:0;else{X=0-g<<16>>16;h=k<<X;h=(h>>X|0)==(k|0)?h:k>>31^2147483647}qi(h,Z,Q,E);g=_-(y&65535)|0;h=g&65535;k=We[J>>1]<<16;g=g<<16>>16;if(h<<16>>16>0)h=h<<16>>16<31?k>>g:0;else{X=0-g<<16>>16;h=k<<X;h=(h>>X|0)==(k|0)?h:k>>31^2147483647}qi(h,J,$,E);g=_+65505|0;Ve[ne>>1]=g;g=g-(t&65535)|0;h=It(g&65535,1,E)|0;k=h<<16>>16;if(h<<16>>16>0)k=h<<16>>16<31?D>>k:0;else{X=0-k<<16>>16;k=D<<X;k=(k>>X|0)==(D|0)?k:D>>31^2147483647}do{if(!(g&1))S=k;else{qi(k,G,V,E);h=Ve[V>>1]|0;k=Ve[G>>1]|0;if((k*23170|0)==1073741824){Ge[E>>2]=1;g=2147483647}else g=k*46340|0;G=(h<<16>>16)*23170>>15;k=g+(G<<1)|0;if(!((g^G|0)>0&(k^g|0)<0)){S=k;break}Ge[E>>2]=1;S=(g>>>31)+2147483647|0}}while(0);R=(F>>>31)+2147483647|0;D=2147483647;A=0;k=0;M=z;while(1){h=(Ze(Ve[M>>1]|0,ee)|0)>>15;M=M+6|0;if((h|0)>32767){Ge[E>>2]=1;h=32767}g=h&65535;if(g<<16>>16>=f<<16>>16)break;o=h<<16>>16;h=Ze(o,o)|0;if((h|0)==1073741824){Ge[E>>2]=1;l=2147483647}else l=h<<1;qi(l,te,oe,E);h=(Ct(g,c,E)|0)<<16>>16;h=Ze(h,h)|0;if((h|0)==1073741824){Ge[E>>2]=1;h=2147483647}else h=h<<1;qi(h,re,ie,E);g=Ve[K>>1]|0;l=Ze(Ve[W>>1]|0,o)|0;do{if((l|0)==1073741824){Ge[E>>2]=1;l=2147483647}else{h=(l<<1)+F|0;if(!((l^F|0)>0&(h^F|0)<0)){l=h;break}Ge[E>>2]=1;l=R}}while(0);h=(Ze(g<<16>>16,o)|0)>>15;if((h|0)>32767){Ge[E>>2]=1;h=32767}G=h<<16;h=(G>>15)+l|0;if((G>>16^l|0)>0&(h^l|0)<0){Ge[E>>2]=1;h=(l>>>31)+2147483647|0}t=Ve[Q>>1]|0;y=Ve[oe>>1]|0;o=Ve[Z>>1]|0;_=Ve[te>>1]|0;l=Ze(_,o)|0;do{if((l|0)==1073741824){Ge[E>>2]=1;g=2147483647}else{g=(l<<1)+h|0;if(!((l^h|0)>0&(g^h|0)<0))break;Ge[E>>2]=1;g=(h>>>31)+2147483647|0}}while(0);l=(Ze(y<<16>>16,o)|0)>>15;if((l|0)>32767){Ge[E>>2]=1;l=32767}G=l<<16;l=(G>>15)+g|0;if((G>>16^g|0)>0&(l^g|0)<0){Ge[E>>2]=1;l=(g>>>31)+2147483647|0}h=(Ze(_,t<<16>>16)|0)>>15;if((h|0)>32767){Ge[E>>2]=1;h=32767}G=h<<16;h=(G>>15)+l|0;if((G>>16^l|0)>0&(h^l|0)<0){Ge[E>>2]=1;h=(l>>>31)+2147483647|0}h=Pt(h,ne,E)|0;g=It(Ve[ne>>1]|0,1,E)|0;l=g<<16>>16;if(g<<16>>16>0)g=g<<16>>16<31?h>>l:0;else{G=0-l<<16>>16;g=h<<G;g=(g>>G|0)==(h|0)?g:h>>31^2147483647}h=g-S|0;if(((h^g)&(g^S)|0)<0){Ge[E>>2]=1;h=(g>>>31)+2147483647|0}h=(Ft(h,E)|0)<<16>>16;h=Ze(h,h)|0;if((h|0)==1073741824){Ge[E>>2]=1;g=2147483647}else g=h<<1;_=Ve[$>>1]|0;o=Ve[ie>>1]|0;y=Ve[J>>1]|0;t=Ve[re>>1]|0;l=Ze(t,y)|0;do{if((l|0)==1073741824){Ge[E>>2]=1;h=2147483647}else{h=(l<<1)+g|0;if(!((l^g|0)>0&(h^g|0)<0))break;Ge[E>>2]=1;h=(g>>>31)+2147483647|0}}while(0);l=(Ze(o<<16>>16,y)|0)>>15;if((l|0)>32767){Ge[E>>2]=1;l=32767}G=l<<16;l=(G>>15)+h|0;if((G>>16^h|0)>0&(l^h|0)<0){Ge[E>>2]=1;l=(h>>>31)+2147483647|0}h=(Ze(t,_<<16>>16)|0)>>15;if((h|0)>32767){Ge[E>>2]=1;h=32767}G=h<<16;h=(G>>15)+l|0;if((G>>16^l|0)>0&(h^l|0)<0){Ge[E>>2]=1;h=(l>>>31)+2147483647|0}l=(h|0)<(D|0);k=l?A:k;A=A+1<<16>>16;if(A<<16>>16>=32)break;else D=l?h:D}oe=(k<<16>>16)*3|0;g=Ve[z+(oe<<1)>>1]|0;Ve[p>>1]=Ve[z+(oe+1<<1)>>1]|0;Ve[v>>1]=Ve[z+(oe+2<<1)>>1]|0;g=Ze(g<<16>>16,ee)|0;if((g|0)==1073741824){Ge[E>>2]=1;g=2147483647}else g=g<<1;if(T)g=C<<16>>16<31?g>>P:0;else{p=0-P<<16>>16;E=g<<p;g=(E>>p|0)==(g|0)?E:g>>31^2147483647}Ve[m>>1]=g>>>16;E=k;p=Ge[b>>2]|0;m=p+2|0;Ve[p>>1]=x;p=p+4|0;Ge[b>>2]=p;Ve[m>>1]=E;Ke=ae;return}function pn(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0;d=(e|0)==7;s=Ve[n>>1]|0;if(d){s=s<<16>>16>>>1&65535;c=gt(r,i,f)|0;r=c<<16;e=r>>16;if((c<<20>>20|0)==(e|0))e=r>>12;else e=e>>>15^32767}else{c=gt(r,i,f)|0;r=c<<16;e=r>>16;if((c<<21>>21|0)==(e|0))e=r>>11;else e=e>>>15^32767}c=e<<16>>16;f=s<<16>>16;r=f-((Ze(c,Ve[a>>1]|0)|0)>>>15&65535)|0;r=((r&32768|0)!=0?0-r|0:r)&65535;l=1;e=0;u=a;while(1){u=u+6|0;s=f-((Ze(Ve[u>>1]|0,c)|0)>>>15&65535)|0;i=s<<16;s=(i|0)<0?0-(i>>16)|0:s;i=(s<<16>>16|0)<(r<<16>>16|0);e=i?l:e;l=l+1<<16>>16;if(l<<16>>16>=32)break;else r=i?s&65535:r}u=(e<<16>>16)*196608>>16;Ve[n>>1]=(Ze(Ve[a+(u<<1)>>1]|0,c)|0)>>>15<<(d&1);Ve[t>>1]=Ve[a+(u+1<<1)>>1]|0;Ve[o>>1]=Ve[a+(u+2<<1)>>1]|0;return e|0}function vn(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0;f=Ct(Ve[i>>1]|0,Ve[o>>1]|0,a)|0;f=(f&65535)-((f&65535)>>>15&65535)|0;f=(f<<16>>31^f)&65535;l=0;u=1;while(1){s=Ve[o+(u<<1)>>1]|0;if(s<<16>>16>r<<16>>16)s=f;else{s=Ct(Ve[i>>1]|0,s,a)|0;s=(s&65535)-((s&65535)>>>15&65535)|0;s=(s<<16>>31^s)&65535;d=s<<16>>16<f<<16>>16;s=d?s:f;l=d?u&65535:l}u=u+1|0;if((u|0)==16)break;else f=s}if((e|0)!=5){f=Ve[o+(l<<16>>16<<1)>>1]|0;if((e|0)==7){Ve[i>>1]=f&65532;return l|0}else{Ve[i>>1]=f;return l|0}}s=l<<16>>16;switch(l<<16>>16){case 0:{f=0;break}case 15:{c=8;break}default:if((Ve[o+(s+1<<1)>>1]|0)>r<<16>>16)c=8;else f=s+65535&65535}if((c|0)==8)f=s+65534&65535;Ve[t>>1]=f;d=f<<16>>16;Ve[n>>1]=Ve[o+(d<<1)>>1]|0;d=d+1|0;Ve[t+2>>1]=d;d=d<<16>>16;Ve[n+2>>1]=Ve[o+(d<<1)>>1]|0;d=d+1|0;Ve[t+4>>1]=d;Ve[n+4>>1]=Ve[o+(d<<16>>16<<1)>>1]|0;Ve[i>>1]=Ve[o+(s<<1)>>1]|0;return l|0}function bn(e,r,i,n,t,o,a,f,s,l,u,c){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0;F=Ke;Ke=Ke+32|0;w=F+20|0;m=F+10|0;h=F;switch(e|0){case 3:case 4:case 6:{u=u+84|0;L=128;break}default:{u=u+80|0;L=64}}O=Ge[u>>2]|0;d=gt(14,i,c)|0;N=r<<16>>16;M=N+65525|0;e=(We[t>>1]|0)+65523&65535;Ve[h>>1]=e;r=(We[t+2>>1]|0)+65522&65535;Ve[h+2>>1]=r;R=M<<16>>16;R=Gn(Ve[t+4>>1]|0,((M<<17>>17|0)==(R|0)?M<<1:R>>>15^32767)+15&65535,c)|0;Ve[h+4>>1]=R;M=Gn(Ve[t+6>>1]|0,M&65535,c)|0;Ve[h+6>>1]=M;t=Gn(Ve[t+8>>1]|0,N+65526&65535,c)|0;Ve[h+8>>1]=t;r=r<<16>>16>e<<16>>16?r:e;r=R<<16>>16>r<<16>>16?R:r;r=M<<16>>16>r<<16>>16?M:r;r=(t<<16>>16>r<<16>>16?t:r)+1&65535;t=0;while(1){i=r-(e&65535)|0;u=i&65535;e=We[n+(t<<1)>>1]<<16;i=i<<16>>16;if(u<<16>>16>0)u=u<<16>>16<31?e>>i:0;else{M=0-i<<16>>16;u=e<<M;u=(u>>M|0)==(e|0)?u:e>>31^2147483647}qi(u,w+(t<<1)|0,m+(t<<1)|0,c);u=t+1|0;if((u|0)==5)break;e=Ve[h+(u<<1)>>1]|0;t=u}M=d<<16>>16;E=Ve[w>>1]|0;g=Ve[m>>1]|0;y=Ve[w+2>>1]|0;_=Ve[m+2>>1]|0;A=Ve[w+4>>1]|0;D=Ve[m+4>>1]|0;S=Ve[w+6>>1]|0;R=Ve[m+6>>1]|0;k=Ve[w+8>>1]|0;p=Ve[m+8>>1]|0;r=2147483647;v=0;u=0;b=O;while(1){t=Ve[b>>1]|0;if(t<<16>>16>o<<16>>16)d=r;else{d=(Ze(Ve[b+2>>1]|0,M)|0)>>15;if((d|0)>32767){Ge[c>>2]=1;d=32767}m=t<<16>>16;t=Ze(m,m)|0;if(t>>>0>1073741823){Ge[c>>2]=1;h=32767}else h=t>>>15;i=d<<16>>16;d=Ze(i,i)|0;if(d>>>0>1073741823){Ge[c>>2]=1;w=32767}else w=d>>>15;n=(Ze(i,m)|0)>>15;if((n|0)>32767){Ge[c>>2]=1;n=32767}d=h<<16>>16;h=Ze(E,d)|0;if((h|0)==1073741824){Ge[c>>2]=1;t=2147483647}else t=h<<1;d=(Ze(g,d)|0)>>15;h=t+(d<<1)|0;if((t^d|0)>0&(h^t|0)<0){Ge[c>>2]=1;h=(t>>>31)+2147483647|0}d=Ze(y,m)|0;if((d|0)==1073741824){Ge[c>>2]=1;t=2147483647}else t=d<<1;m=(Ze(_,m)|0)>>15;d=t+(m<<1)|0;if((t^m|0)>0&(d^t|0)<0){Ge[c>>2]=1;d=(t>>>31)+2147483647|0}t=d+h|0;if((d^h|0)>-1&(t^h|0)<0){Ge[c>>2]=1;t=(h>>>31)+2147483647|0}d=w<<16>>16;h=Ze(A,d)|0;if((h|0)==1073741824){Ge[c>>2]=1;e=2147483647}else e=h<<1;m=(Ze(D,d)|0)>>15;h=e+(m<<1)|0;if((e^m|0)>0&(h^e|0)<0){Ge[c>>2]=1;h=(e>>>31)+2147483647|0}d=h+t|0;if((h^t|0)>-1&(d^t|0)<0){Ge[c>>2]=1;e=(t>>>31)+2147483647|0}else e=d;d=Ze(S,i)|0;if((d|0)==1073741824){Ge[c>>2]=1;h=2147483647}else h=d<<1;m=(Ze(R,i)|0)>>15;d=h+(m<<1)|0;if((h^m|0)>0&(d^h|0)<0){Ge[c>>2]=1;d=(h>>>31)+2147483647|0}t=d+e|0;if((d^e|0)>-1&(t^e|0)<0){Ge[c>>2]=1;h=(e>>>31)+2147483647|0}else h=t;t=n<<16>>16;d=Ze(k,t)|0;if((d|0)==1073741824){Ge[c>>2]=1;e=2147483647}else e=d<<1;m=(Ze(p,t)|0)>>15;d=e+(m<<1)|0;if((e^m|0)>0&(d^e|0)<0){Ge[c>>2]=1;t=(e>>>31)+2147483647|0}else t=d;d=t+h|0;if((t^h|0)>-1&(d^h|0)<0){Ge[c>>2]=1;d=(h>>>31)+2147483647|0}m=(d|0)<(r|0);d=m?d:r;u=m?v:u}b=b+8|0;v=v+1<<16>>16;if((v<<16>>16|0)>=(L|0))break;else r=d}o=u<<16>>16;o=((o<<18>>18|0)==(o|0)?o<<2:o>>>15^32767)<<16>>16;Ve[a>>1]=Ve[O+(o<<1)>>1]|0;r=Ve[O+(o+1<<1)>>1]|0;Ve[s>>1]=Ve[O+(o+2<<1)>>1]|0;Ve[l>>1]=Ve[O+(o+3<<1)>>1]|0;r=Ze(r<<16>>16,M)|0;if((r|0)==1073741824){Ge[c>>2]=1;e=2147483647}else e=r<<1;i=10-N|0;r=i&65535;i=i<<16>>16;if(r<<16>>16>0){c=r<<16>>16<31?e>>i:0;c=c>>>16;c=c&65535;Ve[f>>1]=c;Ke=F;return u|0}else{s=0-i<<16>>16;c=e<<s;c=(c>>s|0)==(e|0)?c:e>>31^2147483647;c=c>>>16;c=c&65535;Ve[f>>1]=c;Ke=F;return u|0}return 0}function kn(e,r,i,n,t,o,a,f,s){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;var l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0,G=0,W=0,K=0,Z=0,Q=0,J=0,$=0,ee=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0,fe=0,se=0,le=0,ue=0,ce=0,de=0,he=0,we=0,me=0,pe=0,ve=0,be=0;be=Ke;Ke=Ke+160|0;ve=be;u=e<<16>>16;me=e<<16>>16==10;pe=Ve[a+(Ve[o>>1]<<1)>>1]|0;if(e<<16>>16>0){s=0;l=f;while(1){Ve[l>>1]=s;s=s+1<<16>>16;if(s<<16>>16>=e<<16>>16)break;else l=l+2|0}}if(i<<16>>16<=1){Ke=be;return}he=o+2|0;we=pe<<16>>16;ue=n+(we<<1)|0;ce=t+(we*80|0)+(we<<1)|0;de=o+6|0;V=r&65535;G=o+4|0;W=o+10|0;K=o+8|0;Z=o+14|0;Q=o+12|0;J=o+18|0;$=o+16|0;ee=f+2|0;re=f+4|0;ie=f+6|0;ne=f+8|0;te=f+10|0;oe=f+12|0;ae=f+14|0;fe=f+16|0;se=f+18|0;le=e<<16>>16>2;Y=o+(u+-1<<1)|0;q=1;X=1;C=0;B=0;H=-1;while(1){j=Ve[a+(Ve[he>>1]<<1)>>1]|0;z=j<<16>>16;r=(We[n+(z<<1)>>1]|0)+(We[ue>>1]|0)|0;l=(Ve[t+(we*80|0)+(z<<1)>>1]<<13)+32768+((Ve[t+(z*80|0)+(z<<1)>>1]|0)+(Ve[ce>>1]|0)<<12)|0;u=Ve[de>>1]|0;if(u<<16>>16<40){u=u<<16>>16;c=ve;while(1){x=(Ve[t+(u*80|0)+(u<<1)>>1]|0)>>>1;P=Ve[t+(u*80|0)+(we<<1)>>1]|0;U=Ve[t+(u*80|0)+(z<<1)>>1]|0;Ve[c>>1]=r+(We[n+(u<<1)>>1]|0);Ve[c+2>>1]=(P+2+x+U|0)>>>2;u=u+V|0;if((u&65535)<<16>>16<40){u=u<<16>>16;c=c+4|0}else break}A=Ve[de>>1]|0}else A=u;r=Ve[G>>1]|0;_=l>>12;u=r<<16>>16;e:do{if(r<<16>>16<40){y=A<<16>>16;if(A<<16>>16<40){c=1;h=r;m=A;w=0;d=-1}else while(1){u=u+V|0;if((u&65535)<<16>>16<40)u=u<<16>>16;else{c=1;U=r;x=A;u=0;break e}}while(1){g=((Ve[t+(u*80|0)+(u<<1)>>1]|0)+_>>1)+(Ve[t+(u*80|0)+(we<<1)>>1]|0)+(Ve[t+(u*80|0)+(z<<1)>>1]|0)|0;E=We[n+(u<<1)>>1]|0;b=y;k=A;v=ve;p=w;while(1){l=(We[v>>1]|0)+E|0;s=l<<16>>16;s=(Ze(s,s)|0)>>>15;w=(g+(Ve[t+(u*80|0)+(b<<1)>>1]|0)>>2)+(Ve[v+2>>1]|0)>>1;if((Ze(s<<16>>16,c<<16>>16)|0)>(Ze(w,d<<16>>16)|0)){c=w&65535;h=r;m=k;w=l&65535;d=s&65535}else w=p;l=b+V|0;k=l&65535;if(k<<16>>16>=40)break;else{b=l<<16>>16;v=v+4|0;p=w}}u=u+V|0;r=u&65535;if(r<<16>>16<40)u=u<<16>>16;else{U=h;x=m;u=w;break}}}else{c=1;U=r;x=A;u=0}}while(0);h=c<<16>>16<<15;c=Ve[W>>1]|0;if(c<<16>>16<40){l=U<<16>>16;s=x<<16>>16;r=u&65535;c=c<<16>>16;u=ve;while(1){F=Ve[t+(c*80|0)+(c<<1)>>1]>>1;L=Ve[t+(c*80|0)+(we<<1)>>1]|0;I=Ve[t+(c*80|0)+(z<<1)>>1]|0;T=Ve[t+(c*80|0)+(l<<1)>>1]|0;P=Ve[t+(c*80|0)+(s<<1)>>1]|0;Ve[u>>1]=(We[n+(c<<1)>>1]|0)+r;Ve[u+2>>1]=(L+2+F+I+T+P|0)>>>2;c=c+V|0;if((c&65535)<<16>>16<40){c=c<<16>>16;u=u+4|0}else break}F=Ve[W>>1]|0}else F=c;d=Ve[K>>1]|0;c=d<<16>>16;e:do{if(d<<16>>16<40){D=U<<16>>16;S=x<<16>>16;R=F<<16>>16;A=h+32768|0;if(F<<16>>16<40){w=1;h=d;r=F;m=d;u=0;d=-1}else while(1){c=c+V|0;if((c&65535)<<16>>16<40)c=c<<16>>16;else{c=1;P=d;T=F;u=0;break e}}while(1){s=We[n+(c<<1)>>1]|0;_=(Ve[t+(c*80|0)+(z<<1)>>1]|0)+(Ve[t+(c*80|0)+(we<<1)>>1]|0)+(Ve[t+(c*80|0)+(D<<1)>>1]|0)+(Ve[t+(c*80|0)+(S<<1)>>1]|0)|0;y=A+(Ve[t+(c*80|0)+(c<<1)>>1]<<11)|0;E=R;b=F;g=ve;while(1){p=(We[g>>1]|0)+s|0;l=y+(Ve[g+2>>1]<<14)+(_+(Ve[t+(c*80|0)+(E<<1)>>1]|0)<<12)|0;v=p<<16>>16;v=(Ze(v,v)|0)>>>15;if((Ze(v<<16>>16,w<<16>>16)|0)>(Ze(l>>16,d<<16>>16)|0)){w=l>>>16&65535;k=m;r=b;u=p&65535;d=v&65535}else k=h;h=E+V|0;b=h&65535;if(b<<16>>16>=40){h=k;break}else{E=h<<16>>16;h=k;g=g+4|0}}c=c+V|0;m=c&65535;if(m<<16>>16<40)c=c<<16>>16;else{c=w;P=h;T=r;break}}}else{c=1;P=d;T=F;u=0}}while(0);w=c<<16>>16<<15;c=Ve[Z>>1]|0;if(c<<16>>16<40){l=U<<16>>16;s=x<<16>>16;d=P<<16>>16;h=T<<16>>16;r=u&65535;c=c<<16>>16;u=ve;while(1){M=Ve[t+(c*80|0)+(c<<1)>>1]>>1;R=Ve[t+(we*80|0)+(c<<1)>>1]|0;N=Ve[t+(z*80|0)+(c<<1)>>1]|0;O=Ve[t+(l*80|0)+(c<<1)>>1]|0;L=Ve[t+(s*80|0)+(c<<1)>>1]|0;F=Ve[t+(d*80|0)+(c<<1)>>1]|0;I=Ve[t+(h*80|0)+(c<<1)>>1]|0;Ve[u>>1]=(We[n+(c<<1)>>1]|0)+r;Ve[u+2>>1]=(R+4+M+N+O+L+F+I|0)>>>3;c=c+V|0;if((c&65535)<<16>>16<40){c=c<<16>>16;u=u+4|0}else break}r=Ve[Z>>1]|0}else r=c;m=Ve[Q>>1]|0;if(m<<16>>16<40){F=U<<16>>16;M=x<<16>>16;R=P<<16>>16;S=T<<16>>16;D=r<<16>>16;A=r<<16>>16<40;N=w+32768|0;L=m<<16>>16;s=1;k=m;b=r;O=m;h=0;c=-1;while(1){if(A){w=We[n+(L<<1)>>1]|0;u=(Ve[t+(L*80|0)+(z<<1)>>1]|0)+(Ve[t+(L*80|0)+(we<<1)>>1]|0)+(Ve[t+(L*80|0)+(F<<1)>>1]|0)+(Ve[t+(L*80|0)+(M<<1)>>1]|0)+(Ve[t+(L*80|0)+(R<<1)>>1]|0)+(Ve[t+(L*80|0)+(S<<1)>>1]|0)|0;d=N+(Ve[t+(L*80|0)+(L<<1)>>1]<<10)|0;v=D;m=r;y=b;_=ve;while(1){g=(We[_>>1]|0)+w|0;b=d+(Ve[_+2>>1]<<14)+(u+(Ve[t+(L*80|0)+(v<<1)>>1]|0)<<11)|0;E=g<<16>>16;E=(Ze(E,E)|0)>>>15;if((Ze(E<<16>>16,s<<16>>16)|0)>(Ze(b>>16,c<<16>>16)|0)){s=b>>>16&65535;k=O;b=m;h=g&65535;c=E&65535}else b=y;p=v+V|0;m=p&65535;if(m<<16>>16>=40)break;else{v=p<<16>>16;y=b;_=_+4|0}}}m=L+V|0;O=m&65535;if(O<<16>>16>=40){I=b;break}else L=m<<16>>16}}else{s=1;k=m;I=r;h=0;c=-1}if(me){v=s<<16>>16<<15;c=Ve[J>>1]|0;if(c<<16>>16<40){u=U<<16>>16;r=x<<16>>16;l=P<<16>>16;s=T<<16>>16;w=k<<16>>16;m=I<<16>>16;d=h&65535;c=c<<16>>16;h=ve;while(1){R=Ve[t+(c*80|0)+(c<<1)>>1]>>1;S=Ve[t+(we*80|0)+(c<<1)>>1]|0;M=Ve[t+(z*80|0)+(c<<1)>>1]|0;N=Ve[t+(u*80|0)+(c<<1)>>1]|0;O=Ve[t+(r*80|0)+(c<<1)>>1]|0;L=Ve[t+(l*80|0)+(c<<1)>>1]|0;F=Ve[t+(s*80|0)+(c<<1)>>1]|0;C=Ve[t+(w*80|0)+(c<<1)>>1]|0;B=Ve[t+(m*80|0)+(c<<1)>>1]|0;Ve[h>>1]=(We[n+(c<<1)>>1]|0)+d;Ve[h+2>>1]=(S+4+R+M+N+O+L+F+C+B|0)>>>3;c=c+V|0;if((c&65535)<<16>>16<40){c=c<<16>>16;h=h+4|0}else break}F=Ve[J>>1]|0}else F=c;w=Ve[$>>1]|0;if(w<<16>>16<40){R=U<<16>>16;S=x<<16>>16;D=P<<16>>16;l=T<<16>>16;M=k<<16>>16;N=I<<16>>16;O=F<<16>>16;L=F<<16>>16<40;A=v+32768|0;u=w<<16>>16;s=1;m=w;h=F;r=w;c=-1;while(1){if(L){v=We[n+(u<<1)>>1]|0;d=(Ve[t+(z*80|0)+(u<<1)>>1]|0)+(Ve[t+(we*80|0)+(u<<1)>>1]|0)+(Ve[t+(R*80|0)+(u<<1)>>1]|0)+(Ve[t+(S*80|0)+(u<<1)>>1]|0)+(Ve[t+(D*80|0)+(u<<1)>>1]|0)+(Ve[t+(l*80|0)+(u<<1)>>1]|0)+(Ve[t+(M*80|0)+(u<<1)>>1]|0)+(Ve[t+(N*80|0)+(u<<1)>>1]|0)|0;w=A+(Ve[t+(u*80|0)+(u<<1)>>1]<<9)|0;_=O;E=F;y=ve;while(1){g=(We[y>>1]|0)+v<<16>>16;g=(Ze(g,g)|0)>>>15;b=w+(Ve[y+2>>1]<<13)+(d+(Ve[t+(u*80|0)+(_<<1)>>1]|0)<<10)|0;if((Ze(g<<16>>16,s<<16>>16)|0)>(Ze(b>>16,c<<16>>16)|0)){s=b>>>16&65535;m=r;h=E;c=g&65535}p=_+V|0;E=p&65535;if(E<<16>>16>=40)break;else{_=p<<16>>16;y=y+4|0}}}w=u+V|0;r=w&65535;if(r<<16>>16>=40)break;else u=w<<16>>16}}else{s=1;m=w;h=F;c=-1}}else{m=C;h=B}if((Ze(c<<16>>16,q<<16>>16)|0)>(Ze(s<<16>>16,H<<16>>16)|0)){Ve[f>>1]=pe;Ve[ee>>1]=j;Ve[re>>1]=U;Ve[ie>>1]=x;Ve[ne>>1]=P;Ve[te>>1]=T;Ve[oe>>1]=k;Ve[ae>>1]=I;if(me){Ve[fe>>1]=m;Ve[se>>1]=h}}else{s=q;c=H}u=Ve[he>>1]|0;if(le){r=1;l=2;while(1){Ve[o+(r<<1)>>1]=Ve[o+(l<<1)>>1]|0;l=l+1|0;if((l&65535)<<16>>16==e<<16>>16)break;else r=r+1|0}}Ve[Y>>1]=u;X=X+1<<16>>16;if(X<<16>>16>=i<<16>>16)break;else{q=s;C=m;B=h;H=c}}Ke=be;return}function En(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0;f=39;while(1){a=e+(f<<1)|0;o=Ve[a>>1]|0;t=r+(f<<1)|0;if(o<<16>>16>-1)Ve[t>>1]=32767;else{Ve[t>>1]=-32767;if(o<<16>>16==-32768)o=32767;else o=0-(o&65535)&65535;Ve[a>>1]=o}Ve[i+(f<<1)>>1]=o;if((f|0)>0)f=f+-1|0;else break}l=8-(n<<16>>16)|0;if((l|0)>0){s=0;t=0}else return;do{n=0;e=0;a=32767;while(1){r=Ve[i+(n<<1)>>1]|0;f=r<<16>>16>-1?r<<16>>16<a<<16>>16:0;t=f?e:t;o=n+5|0;e=o&65535;if(e<<16>>16>=40)break;else{n=o<<16>>16;a=f?r:a}}Ve[i+(t<<16>>16<<1)>>1]=-1;s=s+1<<16>>16}while((s<<16>>16|0)<(l|0));s=0;do{r=1;e=1;o=32767;while(1){n=Ve[i+(r<<1)>>1]|0;f=n<<16>>16>-1?n<<16>>16<o<<16>>16:0;t=f?e:t;a=r+5|0;e=a&65535;if(e<<16>>16>=40)break;else{r=a<<16>>16;o=f?n:o}}Ve[i+(t<<16>>16<<1)>>1]=-1;s=s+1<<16>>16}while((s<<16>>16|0)<(l|0));s=0;do{r=2;e=2;o=32767;while(1){n=Ve[i+(r<<1)>>1]|0;f=n<<16>>16>-1?n<<16>>16<o<<16>>16:0;t=f?e:t;a=r+5|0;e=a&65535;if(e<<16>>16>=40)break;else{r=a<<16>>16;o=f?n:o}}Ve[i+(t<<16>>16<<1)>>1]=-1;s=s+1<<16>>16}while((s<<16>>16|0)<(l|0));s=0;while(1){r=3;e=3;o=32767;while(1){n=Ve[i+(r<<1)>>1]|0;f=n<<16>>16>-1?n<<16>>16<o<<16>>16:0;t=f?e:t;a=r+5|0;e=a&65535;if(e<<16>>16>=40){o=t;break}else{r=a<<16>>16;o=f?n:o}}Ve[i+(o<<16>>16<<1)>>1]=-1;s=s+1<<16>>16;if((s<<16>>16|0)>=(l|0)){t=0;break}else t=o}do{r=4;e=4;s=32767;while(1){n=Ve[i+(r<<1)>>1]|0;f=n<<16>>16>-1?n<<16>>16<s<<16>>16:0;o=f?e:o;a=r+5|0;e=a&65535;if(e<<16>>16>=40)break;else{r=a<<16>>16;s=f?n:s}}Ve[i+(o<<16>>16<<1)>>1]=-1;t=t+1<<16>>16}while((t<<16>>16|0)<(l|0));return}function gn(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0;g=Ke;Ke=Ke+80|0;E=g;d=40;h=r;w=e;l=256;u=256;while(1){s=Ve[h>>1]|0;h=h+2|0;s=Ze(s,s)|0;if((s|0)!=1073741824){c=(s<<1)+l|0;if((s^l|0)>0&(c^l|0)<0){Ge[f>>2]=1;l=(l>>>31)+2147483647|0}else l=c}else{Ge[f>>2]=1;l=2147483647}k=Ve[w>>1]|0;u=(Ze(k<<1,k)|0)+u|0;d=d+-1<<16>>16;if(!(d<<16>>16))break;else w=w+2|0}k=at(l,f)|0;v=k<<5;k=((v>>5|0)==(k|0)?v:k>>31^2147418112)>>16;v=(at(u,f)|0)<<5>>16;b=39;m=r+78|0;p=E+78|0;s=i+78|0;while(1){w=Ze(Ve[m>>1]|0,k)|0;m=m+-2|0;h=w<<1;r=e+(b<<1)|0;l=Ve[r>>1]|0;d=Ze(l<<16>>16,v)|0;if((d|0)!=1073741824){c=(d<<1)+h|0;if((d^h|0)>0&(c^h|0)<0){Ge[f>>2]=1;c=(w>>>30&1)+2147483647|0}}else{Ge[f>>2]=1;c=2147483647}u=c<<10;u=Ft((u>>10|0)==(c|0)?u:c>>31^2147483647,f)|0;if(u<<16>>16>-1)Ve[s>>1]=32767;else{Ve[s>>1]=-32767;if(u<<16>>16==-32768)u=32767;else u=0-(u&65535)&65535;if(l<<16>>16==-32768)c=32767;else c=0-(l&65535)&65535;Ve[r>>1]=c}s=s+-2|0;Ve[p>>1]=u;if((b|0)<=0)break;else{b=b+-1|0;p=p+-2|0}}r=t<<16>>16;if(t<<16>>16<=0){Ve[o+(r<<1)>>1]=Ve[o>>1]|0;Ke=g;return}w=a&65535;h=0;d=-1;s=0;while(1){if((h|0)<40){u=h;c=h&65535;l=-1;while(1){f=Ve[E+(u<<1)>>1]|0;a=f<<16>>16>l<<16>>16;l=a?f:l;s=a?c:s;u=u+w|0;c=u&65535;if(c<<16>>16>=40)break;else u=u<<16>>16}}else l=-1;Ve[n+(h<<1)>>1]=s;if(l<<16>>16>d<<16>>16)Ve[o>>1]=h;else l=d;h=h+1|0;if((h&65535)<<16>>16==t<<16>>16)break;else d=l}s=Ve[o>>1]|0;Ve[o+(r<<1)>>1]=s;if(t<<16>>16>1)l=1;else{Ke=g;return}do{n=s+1<<16>>16;s=n<<16>>16>=t<<16>>16?0:n;Ve[o+(l<<1)>>1]=s;Ve[o+(l+r<<1)>>1]=s;l=l+1|0}while((l&65535)<<16>>16!=t<<16>>16);Ke=g;return}function yn(e){e=e|0;var r=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(12)|0;if(!r){e=-1;return e|0}Ve[r>>1]=8;Ge[e>>2]=r;Ve[r+2>>1]=3;Ve[r+4>>1]=0;Ge[r+8>>2]=0;e=0;return e|0}function _n(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function An(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;do{if((r|0)==8){n=e+2|0;t=(Ve[n>>1]|0)+-1<<16>>16;Ve[n>>1]=t;r=e+8|0;if(!(Ge[r>>2]|0)){Ge[i>>2]=1;Ve[n>>1]=3;break}o=e+4|0;if(t<<16>>16>2&(Ve[o>>1]|0)>0){Ge[i>>2]=2;Ve[o>>1]=(Ve[o>>1]|0)+-1<<16>>16;break}if(!(t<<16>>16)){Ge[i>>2]=2;Ve[n>>1]=Ve[e>>1]|0;break}else{Ge[i>>2]=3;break}}else{Ve[e+2>>1]=Ve[e>>1]|0;Ge[i>>2]=0;r=e+8|0}}while(0);Ge[r>>2]=Ge[i>>2];return}function Dn(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;i=Ut(12)|0;n=i;if(!i){e=-1;return e|0}Ge[i>>2]=0;t=i+4|0;Ge[t>>2]=0;o=i+8|0;Ge[o>>2]=r;if((fn(i)|0)<<16>>16==0?(ni(t,Ge[o>>2]|0)|0)<<16>>16==0:0){sn(Ge[i>>2]|0)|0;oi(Ge[t>>2]|0)|0;Ge[e>>2]=n;e=0;return e|0}ln(i);ti(t);zt(i);e=-1;return e|0}function Sn(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;ln(r);ti((Ge[e>>2]|0)+4|0);zt(Ge[e>>2]|0);Ge[e>>2]=0;return}function Rn(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0;s=Ke;Ke=Ke+448|0;a=s+320|0;f=s;Vt(n|0,0,488)|0;o=0;do{l=i+(o<<1)|0;Ve[l>>1]=(We[l>>1]|0)&65528;o=o+1|0}while((o|0)!=160);un(Ge[e>>2]|0,i,160);l=e+4|0;ai(Ge[l>>2]|0,r,i,a,t,f)|0;cn(Ge[t>>2]|0,a,n,(Ge[l>>2]|0)+2392|0);Ke=s;return}function Mn(e,r,i,n,t,o,a,f,s,l,u,c,d,h,w,m){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;var p=0,v=0,b=0;b=Ke;Ke=Ke+48|0;p=b+22|0;v=b;xt(t,(e&-2|0)==6?i:r,p);xt(t,n,v);i=u;r=p;t=i+22|0;do{Ve[i>>1]=Ve[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(t|0));Bt(o,u,d,40,l,0);Bt(v,d,d,40,l,0);Lt(o,a,w,40);i=c;r=w;t=i+80|0;do{Ve[i>>1]=Ve[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(t|0));Bt(o,c,m,40,f,0);Lt(p,m,h,40);Bt(v,h,h,40,s,0);Ke=b;return}function Nn(e,r,i,n,t,o,a,f,s,l,u,c,d,h,w,m,p){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;s=s|0;l=l|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;p=p|0;var v=0,b=0,k=0,E=0,g=0;if((r|0)==7){k=11;r=n<<16>>16>>>1&65535;v=2}else{k=13;r=n;v=1}Ve[m>>1]=n<<16>>16<13017?n:13017;b=i<<16>>16;w=w+(b<<1)|0;m=r<<16>>16;t=t<<16>>16;i=20;r=s;p=w;while(1){s=p+2|0;g=Ze(Ve[p>>1]|0,m)|0;E=Ze(Ve[s>>1]|0,m)|0;g=(Ze(Ve[r>>1]|0,t)|0)+g<<1;E=(Ze(Ve[r+2>>1]|0,t)|0)+E<<1<<v;Ve[p>>1]=((g<<v)+32768|0)>>>16;Ve[s>>1]=(E+32768|0)>>>16;i=i+-1<<16>>16;if(!(i<<16>>16))break;else{r=r+4|0;p=p+4|0}}r=n<<16>>16;Bt(o,w,a+(b<<1)|0,40,c,1);i=30;p=0;while(1){E=i+b|0;Ve[d+(p<<1)>>1]=(We[e+(E<<1)>>1]|0)-(We[a+(E<<1)>>1]|0);E=Ze(Ve[l+(i<<1)>>1]|0,r)|0;g=(Ze(Ve[u+(i<<1)>>1]|0,t)|0)>>k;Ve[h+(p<<1)>>1]=(We[f+(i<<1)>>1]|0)-(E>>>14)-g;p=p+1|0;if((p|0)==10)break;else i=i+1|0}return}function On(e){e=e|0;var r=0;if(!e){e=-1;return e|0}Ge[e>>2]=0;r=Ut(16)|0;if(!r){e=-1;return e|0}Ve[r>>1]=0;Ve[r+2>>1]=0;Ve[r+4>>1]=0;Ve[r+6>>1]=0;Ve[r+8>>1]=0;Ve[r+10>>1]=0;Ve[r+12>>1]=0;Ve[r+14>>1]=0;Ge[e>>2]=r;e=0;return e|0}function Ln(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;Ve[e+12>>1]=0;Ve[e+14>>1]=0;e=0;return e|0}function Fn(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function In(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0;n=We[r+6>>1]|0;i=We[r+8>>1]|0;t=n-i|0;t=(t&65535|0)!=32767?t&65535:32767;o=We[r+10>>1]|0;i=i-o|0;t=(i<<16>>16|0)<(t<<16>>16|0)?i&65535:t;i=We[r+12>>1]|0;o=o-i|0;t=(o<<16>>16|0)<(t<<16>>16|0)?o&65535:t;o=We[r+14>>1]|0;i=i-o|0;t=(i<<16>>16|0)<(t<<16>>16|0)?i&65535:t;o=o-(We[r+16>>1]|0)|0;i=Ve[r+2>>1]|0;a=We[r+4>>1]|0;r=(i&65535)-a|0;r=(r&65535|0)!=32767?r&65535:32767;n=a-n|0;if(((o<<16>>16|0)<(t<<16>>16|0)?o&65535:t)<<16>>16<1500?1:(((n<<16>>16|0)<(r<<16>>16|0)?n&65535:r)<<16>>16|0)<((i<<16>>16>32e3?600:i<<16>>16>30500?800:1100)|0)){o=(Ve[e>>1]|0)+1<<16>>16;a=o<<16>>16>11;Ve[e>>1]=a?12:o;return a&1|0}else{Ve[e>>1]=0;return 0}return 0}function Tn(e,r,i){e=e|0;r=r|0;i=i|0;r=It(r,3,i)|0;r=Gn(r,Ve[e+2>>1]|0,i)|0;r=Gn(r,Ve[e+4>>1]|0,i)|0;r=Gn(r,Ve[e+6>>1]|0,i)|0;r=Gn(r,Ve[e+8>>1]|0,i)|0;r=Gn(r,Ve[e+10>>1]|0,i)|0;r=Gn(r,Ve[e+12>>1]|0,i)|0;return(Gn(r,Ve[e+14>>1]|0,i)|0)<<16>>16>15565|0}function Pn(e,r,i){e=e|0;r=r|0;i=i|0;var n=0;i=e+4|0;Ve[e+2>>1]=Ve[i>>1]|0;n=e+6|0;Ve[i>>1]=Ve[n>>1]|0;i=e+8|0;Ve[n>>1]=Ve[i>>1]|0;n=e+10|0;Ve[i>>1]=Ve[n>>1]|0;i=e+12|0;Ve[n>>1]=Ve[i>>1]|0;e=e+14|0;Ve[i>>1]=Ve[e>>1]|0;Ve[e>>1]=r<<16>>16>>>3;return}function Cn(e){e=e|0;var r=0,i=0,n=0;if(!e){n=-1;return n|0}Ge[e>>2]=0;r=Ut(128)|0;if(!r){n=-1;return n|0}i=r+72|0;n=i+46|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Ve[r>>1]=150;Ve[r+36>>1]=150;Ve[r+18>>1]=150;Ve[r+54>>1]=0;Ve[r+2>>1]=150;Ve[r+38>>1]=150;Ve[r+20>>1]=150;Ve[r+56>>1]=0;Ve[r+4>>1]=150;Ve[r+40>>1]=150;Ve[r+22>>1]=150;Ve[r+58>>1]=0;Ve[r+6>>1]=150;Ve[r+42>>1]=150;Ve[r+24>>1]=150;Ve[r+60>>1]=0;Ve[r+8>>1]=150;Ve[r+44>>1]=150;Ve[r+26>>1]=150;Ve[r+62>>1]=0;Ve[r+10>>1]=150;Ve[r+46>>1]=150;Ve[r+28>>1]=150;Ve[r+64>>1]=0;Ve[r+12>>1]=150;Ve[r+48>>1]=150;Ve[r+30>>1]=150;Ve[r+66>>1]=0;Ve[r+14>>1]=150;Ve[r+50>>1]=150;Ve[r+32>>1]=150;Ve[r+68>>1]=0;Ve[r+16>>1]=150;Ve[r+52>>1]=150;Ve[r+34>>1]=150;Ve[r+70>>1]=0;Ve[r+118>>1]=13106;Ve[r+120>>1]=0;Ve[r+122>>1]=0;Ve[r+124>>1]=0;Ve[r+126>>1]=13106;Ge[e>>2]=r;n=0;return n|0}function Bn(e){e=e|0;var r=0,i=0;if(!e){i=-1;return i|0}r=e+72|0;i=r+46|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(i|0));Ve[e>>1]=150;Ve[e+36>>1]=150;Ve[e+18>>1]=150;Ve[e+54>>1]=0;Ve[e+2>>1]=150;Ve[e+38>>1]=150;Ve[e+20>>1]=150;Ve[e+56>>1]=0;Ve[e+4>>1]=150;Ve[e+40>>1]=150;Ve[e+22>>1]=150;Ve[e+58>>1]=0;Ve[e+6>>1]=150;Ve[e+42>>1]=150;Ve[e+24>>1]=150;Ve[e+60>>1]=0;Ve[e+8>>1]=150;Ve[e+44>>1]=150;Ve[e+26>>1]=150;Ve[e+62>>1]=0;Ve[e+10>>1]=150;Ve[e+46>>1]=150;Ve[e+28>>1]=150;Ve[e+64>>1]=0;Ve[e+12>>1]=150;Ve[e+48>>1]=150;Ve[e+30>>1]=150;Ve[e+66>>1]=0;Ve[e+14>>1]=150;Ve[e+50>>1]=150;Ve[e+32>>1]=150;Ve[e+68>>1]=0;Ve[e+16>>1]=150;Ve[e+52>>1]=150;Ve[e+34>>1]=150;Ve[e+70>>1]=0;Ve[e+118>>1]=13106;Ve[e+120>>1]=0;Ve[e+122>>1]=0;Ve[e+124>>1]=0;Ve[e+126>>1]=13106;i=0;return i|0}function xn(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function Un(e,r){e=e|0;r=r|0;Ve[e+118>>1]=r;return}function zn(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0;i=Ft(i,n)|0;if(i<<16>>16<=0)return;i=i<<16>>16;if((i*21298|0)==1073741824){Ge[n>>2]=1;t=2147483647}else t=i*42596|0;i=r-t|0;if(((i^r)&(t^r)|0)<0){Ge[n>>2]=1;i=(r>>>31)+2147483647|0}if((i|0)<=0)return;e=e+104|0;Ve[e>>1]=We[e>>1]|0|16384;return}function jn(e,r,i){e=e|0;r=r|0;i=i|0;var n=0;e=e+104|0;n=It(Ve[e>>1]|0,1,i)|0;Ve[e>>1]=n;if(!(r<<16>>16))return;Ve[e>>1]=(It(n,1,i)|0)&65535|8192;return}function qn(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;t=e+112|0;n=Ct(Ve[t>>1]|0,Ve[r>>1]|0,i)|0;n=(n&65535)-((n&65535)>>>15&65535)|0;n=((n<<16>>31^n)&65535)<<16>>16<4;o=Ve[r>>1]|0;Ve[t>>1]=o;r=r+2|0;o=Ct(o,Ve[r>>1]|0,i)|0;o=(o&65535)-((o&65535)>>>15&65535)|0;n=((o<<16>>31^o)&65535)<<16>>16<4?n?2:1:n&1;Ve[t>>1]=Ve[r>>1]|0;t=e+102|0;Ve[t>>1]=It(Ve[t>>1]|0,1,i)|0;r=e+110|0;if((Gn(Ve[r>>1]|0,n,i)|0)<<16>>16<=3){Ve[r>>1]=n;return}Ve[t>>1]=We[t>>1]|0|16384;Ve[r>>1]=n;return}function Hn(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0;D=Ke;Ke=Ke+352|0;l=D+24|0;_=D;a=0;t=0;do{n=Ve[r+(a+-40<<1)>>1]|0;n=Ze(n,n)|0;if((n|0)!=1073741824){o=(n<<1)+t|0;if((n^t|0)>0&(o^t|0)<0){Ge[i>>2]=1;t=(t>>>31)+2147483647|0}else t=o}else{Ge[i>>2]=1;t=2147483647}a=a+1|0}while((a|0)!=160);u=t;if((343039-u&u|0)<0){Ge[i>>2]=1;t=(u>>>31)+2147483647|0}else t=u+-343040|0;if((t|0)<0){y=e+102|0;Ve[y>>1]=We[y>>1]&16383}s=u+-15e3|0;c=(14999-u&u|0)<0;if(c){Ge[i>>2]=1;o=(u>>>31)+2147483647|0}else o=s;if((o|0)<0){y=e+108|0;Ve[y>>1]=We[y>>1]&16383}n=e+72|0;f=e+74|0;o=Ve[n>>1]|0;a=Ve[f>>1]|0;t=0;do{y=t<<2;E=Ct((Ve[r+(y<<1)>>1]|0)>>>2&65535,((o<<16>>16)*21955|0)>>>15&65535,i)|0;v=((E<<16>>16)*21955|0)>>>15&65535;p=Gn(o,v,i)|0;k=y|1;g=Ct((Ve[r+(k<<1)>>1]|0)>>>2&65535,((a<<16>>16)*6390|0)>>>15&65535,i)|0;b=((g<<16>>16)*6390|0)>>>15&65535;o=Gn(a,b,i)|0;Ve[l+(y<<1)>>1]=Gn(p,o,i)|0;Ve[l+(k<<1)>>1]=Ct(p,o,i)|0;k=y|2;o=Ct((Ve[r+(k<<1)>>1]|0)>>>2&65535,v,i)|0;E=Gn(E,((o<<16>>16)*21955|0)>>>15&65535,i)|0;y=y|3;a=Ct((Ve[r+(y<<1)>>1]|0)>>>2&65535,b,i)|0;g=Gn(g,((a<<16>>16)*6390|0)>>>15&65535,i)|0;Ve[l+(k<<1)>>1]=Gn(E,g,i)|0;Ve[l+(y<<1)>>1]=Ct(E,g,i)|0;t=t+1|0}while((t|0)!=40);Ve[n>>1]=o;Ve[f>>1]=a;a=e+76|0;o=e+80|0;t=0;do{y=t<<2;Yn(l+(y<<1)|0,l+((y|2)<<1)|0,a,i);Yn(l+((y|1)<<1)|0,l+((y|3)<<1)|0,o,i);t=t+1|0}while((t|0)!=40);a=e+84|0;o=e+86|0;t=e+92|0;n=0;do{y=n<<3;Xn(l+(y<<1)|0,l+((y|4)<<1)|0,a,i);Xn(l+((y|2)<<1)|0,l+((y|6)<<1)|0,o,i);Xn(l+((y|3)<<1)|0,l+((y|7)<<1)|0,t,i);n=n+1|0}while((n|0)!=20);a=e+88|0;o=e+90|0;t=0;do{y=t<<4;Xn(l+(y<<1)|0,l+((y|8)<<1)|0,a,i);Xn(l+((y|4)<<1)|0,l+((y|12)<<1)|0,o,i);t=t+1|0}while((t|0)!=10);m=Vn(l,e+70|0,32,40,4,1,15,i)|0;Ve[_+16>>1]=m;p=Vn(l,e+68|0,16,20,8,7,16,i)|0;Ve[_+14>>1]=p;v=Vn(l,e+66|0,16,20,8,3,16,i)|0;Ve[_+12>>1]=v;b=Vn(l,e+64|0,16,20,8,2,16,i)|0;Ve[_+10>>1]=b;k=Vn(l,e+62|0,16,20,8,6,16,i)|0;Ve[_+8>>1]=k;E=Vn(l,e+60|0,8,10,16,4,16,i)|0;Ve[_+6>>1]=E;g=Vn(l,e+58|0,8,10,16,12,16,i)|0;Ve[_+4>>1]=g;y=Vn(l,e+56|0,8,10,16,8,16,i)|0;Ve[_+2>>1]=y;w=Vn(l,e+54|0,8,10,16,0,16,i)|0;Ve[_>>1]=w;a=0;n=0;do{o=e+(n<<1)|0;r=Et(Ve[o>>1]|0)|0;o=Ve[o>>1]|0;t=r<<16>>16;if(r<<16>>16<0){f=0-t<<16;if((f|0)<983040)f=o<<16>>16>>(f>>16)&65535;else f=0}else{f=o<<16>>16;o=f<<t;if((o<<16>>16>>t|0)==(f|0))f=o&65535;else f=(f>>>15^32767)&65535}o=Kn(It(Ve[_+(n<<1)>>1]|0,1,i)|0,f)|0;h=Ct(r,5,i)|0;t=h<<16>>16;if(h<<16>>16<0){f=0-t<<16;if((f|0)<983040)f=o<<16>>16>>(f>>16);else f=0}else{o=o<<16>>16;f=o<<t;if((f<<16>>16>>t|0)!=(o|0))f=o>>>15^32767}f=f<<16>>16;f=Ze(f,f)|0;if((f|0)!=1073741824){o=(f<<1)+a|0;if((f^a|0)>0&(o^a|0)<0){Ge[i>>2]=1;a=(a>>>31)+2147483647|0}else a=o}else{Ge[i>>2]=1;a=2147483647}n=n+1|0}while((n|0)!=9);h=a<<6;a=(((h>>6|0)==(a|0)?h:a>>31^2147418112)>>16)*3641>>15;if((a|0)>32767){Ge[i>>2]=1;a=32767}h=Ve[e>>1]|0;f=h<<16>>16;d=Ve[e+2>>1]|0;o=(d<<16>>16)+f|0;if((d^h)<<16>>16>-1&(o^f|0)<0){Ge[i>>2]=1;o=(f>>>31)+2147483647|0}h=Ve[e+4>>1]|0;f=h+o|0;if((h^o|0)>-1&(f^o|0)<0){Ge[i>>2]=1;f=(o>>>31)+2147483647|0}h=Ve[e+6>>1]|0;o=h+f|0;if((h^f|0)>-1&(o^f|0)<0){Ge[i>>2]=1;o=(f>>>31)+2147483647|0}h=Ve[e+8>>1]|0;f=h+o|0;if((h^o|0)>-1&(f^o|0)<0){Ge[i>>2]=1;f=(o>>>31)+2147483647|0}h=Ve[e+10>>1]|0;o=h+f|0;if((h^f|0)>-1&(o^f|0)<0){Ge[i>>2]=1;o=(f>>>31)+2147483647|0}h=Ve[e+12>>1]|0;f=h+o|0;if((h^o|0)>-1&(f^o|0)<0){Ge[i>>2]=1;f=(o>>>31)+2147483647|0}h=Ve[e+14>>1]|0;o=h+f|0;if((h^f|0)>-1&(o^f|0)<0){Ge[i>>2]=1;o=(f>>>31)+2147483647|0}h=Ve[e+16>>1]|0;f=h+o|0;if((h^o|0)>-1&(f^o|0)<0){Ge[i>>2]=1;f=(o>>>31)+2147483647|0}d=f<<13;d=((d>>13|0)==(f|0)?d:f>>31^2147418112)>>>16&65535;f=(Ze((Ct(d,0,i)|0)<<16>>16,-2808)|0)>>15;if((f|0)>32767){Ge[i>>2]=1;f=32767}l=Gn(f&65535,1260,i)|0;h=e+100|0;f=It(Ve[h>>1]|0,1,i)|0;if((a<<16>>16|0)>((l<<16>>16<720?720:l<<16>>16)|0))f=(f&65535|16384)&65535;Ve[h>>1]=f;if(c){Ge[i>>2]=1;s=(u>>>31)+2147483647|0}t=Ve[e+118>>1]|0;c=e+126|0;f=Ve[c>>1]|0;n=f<<16>>16<19660;n=t<<16>>16<f<<16>>16?n?2621:6553:n?2621:655;r=f&65535;a=r<<16;f=Ze(n,f<<16>>16)|0;if((f|0)==1073741824){Ge[i>>2]=1;f=2147483647}else f=f<<1;o=a-f|0;if(((o^a)&(f^a)|0)<0){Ge[i>>2]=1;o=(r>>>15)+2147483647|0}a=Ze(n,t<<16>>16)|0;do{if((a|0)==1073741824){Ge[i>>2]=1;f=2147483647}else{f=o+(a<<1)|0;if(!((o^a|0)>0&(f^o|0)<0))break;Ge[i>>2]=1;f=(o>>>31)+2147483647|0}}while(0);r=Ft(f,i)|0;u=(s|0)>-1;Ve[c>>1]=u?r<<16>>16<13106?13106:r:13106;r=e+106|0;Ve[r>>1]=It(Ve[r>>1]|0,1,i)|0;o=e+108|0;f=It(Ve[o>>1]|0,1,i)|0;Ve[o>>1]=f;a=Ve[c>>1]|0;e:do{if(u){do{if(a<<16>>16>19660)Ve[r>>1]=We[r>>1]|16384;else{if(a<<16>>16>16383)break;a=e+116|0;f=0;break e}}while(0);Ve[o>>1]=f&65535|16384;A=62}else A=62}while(0);do{if((A|0)==62){f=e+116|0;if(a<<16>>16<=22936){a=f;f=0;break}a=f;f=Gn(Ve[f>>1]|0,1,i)|0}}while(0);Ve[a>>1]=f;if((Ve[r>>1]&32640)!=32640){l=(Ve[o>>1]&32767)==32767;Ve[e+122>>1]=l&1;if(l)A=67}else{Ve[e+122>>1]=1;A=67}do{if((A|0)==67){a=e+98|0;if((Ve[a>>1]|0)>=5)break;Ve[a>>1]=5}}while(0);l=e+102|0;do{if((Ve[l>>1]&24576)==24576)A=71;else{if((Ve[e+104>>1]&31744)==31744){A=71;break}if(!(Ve[h>>1]&32640)){Ve[e+98>>1]=20;o=32767;break}else{o=w;a=0;f=0}while(1){n=Ve[e+18+(a<<1)>>1]|0;t=o<<16>>16>n<<16>>16;s=t?o:n;o=t?n:o;s=s<<16>>16<184?184:s;o=o<<16>>16<184?184:o;n=Et(o)|0;t=n<<16>>16;do{if(n<<16>>16<0){r=0-t<<16;if((r|0)>=983040){r=0;break}r=o<<16>>16>>(r>>16)&65535}else{r=o<<16>>16;o=r<<t;if((o<<16>>16>>t|0)==(r|0)){r=o&65535;break}r=(r>>>15^32767)&65535}}while(0);s=Kn(It(s,1,i)|0,r)|0;f=Gn(f,It(s,Ct(8,n,i)|0,i)|0,i)|0;a=a+1|0;if((a|0)==9)break;o=Ve[_+(a<<1)>>1]|0}if(f<<16>>16>1e3){Ve[e+98>>1]=20;o=32767;break}o=Ve[h>>1]|0;a=e+98|0;f=Ve[a>>1]|0;do{if(!(o&16384))A=86;else{if(!(f<<16>>16)){f=o;break}f=Ct(f,1,i)|0;Ve[a>>1]=f;A=86}}while(0);if((A|0)==86){if(f<<16>>16==20){o=32767;break}f=Ve[h>>1]|0}o=(f&16384)==0?16383:3276}}while(0);if((A|0)==71){Ve[e+98>>1]=20;o=32767}a=w;f=0;while(1){s=e+18+(f<<1)|0;r=bt(o,Ct(a,Ve[s>>1]|0,i)|0,i)|0;Ve[s>>1]=Gn(Ve[s>>1]|0,r,i)|0;f=f+1|0;if((f|0)==9)break;a=Ve[_+(f<<1)>>1]|0}do{if(!(Ve[h>>1]&30720)){if(Ve[l>>1]&30720){A=95;break}if(!(Ve[e+114>>1]|0)){t=2097;n=1638;r=2}else A=95}else A=95}while(0);do{if((A|0)==95){if((Ve[e+98>>1]|0)==0?(Ve[e+114>>1]|0)==0:0){t=1867;n=491;r=2;break}t=1638;n=0;r=0}}while(0);o=0;do{a=e+(o<<1)|0;f=Ct(Ve[e+36+(o<<1)>>1]|0,Ve[a>>1]|0,i)|0;if(f<<16>>16<0){f=bt(t,f,i)|0;f=Gn(-2,Gn(Ve[a>>1]|0,f,i)|0,i)|0;f=f<<16>>16<40?40:f}else{f=bt(n,f,i)|0;f=Gn(r,Gn(Ve[a>>1]|0,f,i)|0,i)|0;f=f<<16>>16>16e3?16e3:f}Ve[a>>1]=f;o=o+1|0}while((o|0)!=9);Ve[e+36>>1]=w;Ve[e+38>>1]=y;Ve[e+40>>1]=g;Ve[e+42>>1]=E;Ve[e+44>>1]=k;Ve[e+46>>1]=b;Ve[e+48>>1]=v;Ve[e+50>>1]=p;Ve[e+52>>1]=m;a=d<<16>>16>100;o=a?7:4;a=a?4:5;if(!u){Ve[e+94>>1]=0;Ve[e+96>>1]=0;Ve[e+114>>1]=0;Ve[e+116>>1]=0;i=0;e=e+120|0;Ve[e>>1]=i;Ke=D;return i|0}t=e+114|0;n=Ve[t>>1]|0;do{if((Ve[e+116>>1]|0)<=100){if(n<<16>>16)break;n=Ve[h>>1]|0;do{if(!(n&16368)){if((Ve[c>>1]|0)>21298)n=1;else break;e=e+120|0;Ve[e>>1]=n;Ke=D;return n|0}}while(0);t=e+94|0;if(!(n&16384)){Ve[t>>1]=0;n=e+96|0;t=Ve[n>>1]|0;if(t<<16>>16<=0){i=0;e=e+120|0;Ve[e>>1]=i;Ke=D;return i|0}Ve[n>>1]=Ct(t,1,i)|0;i=1;e=e+120|0;Ve[e>>1]=i;Ke=D;return i|0}else{i=Gn(Ve[t>>1]|0,1,i)|0;Ve[t>>1]=i;if((i<<16>>16|0)<(a|0)){i=1;e=e+120|0;Ve[e>>1]=i;Ke=D;return i|0}Ve[e+96>>1]=o;i=1;e=e+120|0;Ve[e>>1]=i;Ke=D;return i|0}}else{if(n<<16>>16>=250)break;Ve[t>>1]=250;n=250}}while(0);Ve[e+94>>1]=4;Ve[t>>1]=Ct(n,1,i)|0;i=1;e=e+120|0;Ve[e>>1]=i;Ke=D;return i|0}function Yn(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;t=(Ve[i>>1]|0)*21955>>15;if((t|0)>32767){Ge[n>>2]=1;t=32767}o=Ct(Ve[e>>1]|0,t&65535,n)|0;t=(o<<16>>16)*21955>>15;if((t|0)>32767){Ge[n>>2]=1;t=32767}a=Gn(Ve[i>>1]|0,t&65535,n)|0;Ve[i>>1]=o;i=i+2|0;t=(Ve[i>>1]|0)*6390>>15;if((t|0)>32767){Ge[n>>2]=1;t=32767}o=Ct(Ve[r>>1]|0,t&65535,n)|0;t=(o<<16>>16)*6390>>15;if((t|0)>32767){Ge[n>>2]=1;t=32767}t=Gn(Ve[i>>1]|0,t&65535,n)|0;Ve[i>>1]=o;Ve[e>>1]=It(Gn(a,t,n)|0,1,n)|0;Ve[r>>1]=It(Ct(a,t,n)|0,1,n)|0;return}function Xn(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0;t=(Ve[i>>1]|0)*13363>>15;if((t|0)>32767){Ge[n>>2]=1;t=32767}o=Ct(Ve[r>>1]|0,t&65535,n)|0;t=(o<<16>>16)*13363>>15;if((t|0)>32767){Ge[n>>2]=1;t=32767}t=Gn(Ve[i>>1]|0,t&65535,n)|0;Ve[i>>1]=o;Ve[r>>1]=It(Ct(Ve[e>>1]|0,t,n)|0,1,n)|0;Ve[e>>1]=It(Gn(Ve[e>>1]|0,t,n)|0,1,n)|0;return}function Vn(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0;if(i<<16>>16<n<<16>>16){c=t<<16>>16;s=o<<16>>16;d=i<<16>>16;l=0;do{h=Ve[e+((Ze(d,c)|0)+s<<1)>>1]|0;h=(h&65535)-((h&65535)>>>15&65535)|0;h=(h<<16>>31^h)<<16;u=(h>>15)+l|0;if((h>>16^l|0)>0&(u^l|0)<0){Ge[f>>2]=1;l=(l>>>31)+2147483647|0}else l=u;d=d+1|0}while((d&65535)<<16>>16!=n<<16>>16);d=l}else d=0;l=Ve[r>>1]|0;h=Ct(16,a,f)|0;s=h<<16>>16;if(h<<16>>16>0){n=l<<s;if((n>>s|0)!=(l|0))n=l>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)n=l>>(s>>16);else n=0}s=n+d|0;if((n^d|0)>-1&(s^d|0)<0){Ge[f>>2]=1;s=(d>>>31)+2147483647|0}h=a<<16>>16;a=a<<16>>16>0;if(a){n=d<<h;if((n>>h|0)!=(d|0))n=d>>31^2147483647}else{n=0-h<<16;if((n|0)<2031616)n=d>>(n>>16);else n=0}Ve[r>>1]=n>>>16;if(i<<16>>16>0){c=t<<16>>16;l=o<<16>>16;u=0;do{o=Ve[e+((Ze(u,c)|0)+l<<1)>>1]|0;o=(o&65535)-((o&65535)>>>15&65535)|0;o=(o<<16>>31^o)<<16;n=(o>>15)+s|0;if((o>>16^s|0)>0&(n^s|0)<0){Ge[f>>2]=1;s=(s>>>31)+2147483647|0}else s=n;u=u+1|0}while((u&65535)<<16>>16!=i<<16>>16)}if(a){n=s<<h;if((n>>h|0)==(s|0)){f=n;f=f>>>16;f=f&65535;return f|0}f=s>>31^2147483647;f=f>>>16;f=f&65535;return f|0}else{n=0-h<<16;if((n|0)>=2031616){f=0;f=f>>>16;f=f&65535;return f|0}f=s>>(n>>16);f=f>>>16;f=f&65535;return f|0}return 0}function Gn(e,r,i){e=e|0;r=r|0;i=i|0;e=(r<<16>>16)+(e<<16>>16)|0;if((e|0)<=32767){if((e|0)<-32768){Ge[i>>2]=1;e=-32768}}else{Ge[i>>2]=1;e=32767}return e&65535|0}function Wn(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0;y=Ke;Ke=Ke+32|0;E=y+12|0;g=y;Ve[E>>1]=1024;Ve[g>>1]=1024;s=Ve[e+2>>1]|0;a=Ve[e+20>>1]|0;n=((a+s|0)>>>2)+64512|0;Ve[E+2>>1]=n;a=((s-a|0)>>>2)+1024|0;Ve[g+2>>1]=a;s=Ve[e+4>>1]|0;t=Ve[e+18>>1]|0;n=((t+s|0)>>>2)-n|0;Ve[E+4>>1]=n;a=((s-t|0)>>>2)+a|0;Ve[g+4>>1]=a;t=Ve[e+6>>1]|0;s=Ve[e+16>>1]|0;n=((s+t|0)>>>2)-n|0;Ve[E+6>>1]=n;a=((t-s|0)>>>2)+a|0;Ve[g+6>>1]=a;s=Ve[e+8>>1]|0;t=Ve[e+14>>1]|0;n=((t+s|0)>>>2)-n|0;Ve[E+8>>1]=n;a=((s-t|0)>>>2)+a|0;Ve[g+8>>1]=a;t=Ve[e+10>>1]|0;s=Ve[e+12>>1]|0;n=((s+t|0)>>>2)-n|0;Ve[E+10>>1]=n;Ve[g+10>>1]=((t-s|0)>>>2)+a;a=Ve[3454]|0;s=a<<16>>16;e=Ve[E+2>>1]|0;t=(e<<16>>16<<14)+(s<<10)|0;p=t&-65536;t=(t>>>1)-(t>>16<<15)<<16;k=(((Ze(t>>16,s)|0)>>15)+(Ze(p>>16,s)|0)<<2)+-16777216|0;k=(Ve[E+4>>1]<<14)+k|0;f=k>>16;k=(k>>>1)-(f<<15)<<16;p=(((Ze(k>>16,s)|0)>>15)+(Ze(f,s)|0)<<2)-((t>>15)+p)|0;p=(Ve[E+6>>1]<<14)+p|0;t=p>>16;p=(p>>>1)-(t<<15)<<16;f=(((Ze(p>>16,s)|0)>>15)+(Ze(t,s)|0)<<2)-((k>>15)+(f<<16))|0;f=(Ve[E+8>>1]<<14)+f|0;k=f>>16;t=(n<<16>>3)+((((Ze((f>>>1)-(k<<15)<<16>>16,s)|0)>>15)+(Ze(k,s)|0)<<1)-((p>>15)+(t<<16)))|0;p=E+4|0;s=E;k=0;f=0;n=0;m=E+10|0;t=(t+33554432|0)>>>0<67108863?t>>>10&65535:(t|0)>33554431?32767:-32768;e:while(1){v=e<<16>>16<<14;w=s+6|0;h=s+8|0;d=f<<16>>16;while(1){if((d|0)>=60)break e;s=(d&65535)+1<<16>>16;l=Ve[6908+(s<<16>>16<<1)>>1]|0;b=l<<16>>16;f=v+(b<<10)|0;o=f&-65536;f=(f>>>1)-(f>>16<<15)<<16;u=(((Ze(f>>16,b)|0)>>15)+(Ze(o>>16,b)|0)<<2)+-16777216|0;c=Ve[p>>1]|0;u=(c<<16>>16<<14)+u|0;D=u>>16;u=(u>>>1)-(D<<15)<<16;o=(((Ze(u>>16,b)|0)>>15)+(Ze(D,b)|0)<<2)-((f>>15)+o)|0;f=Ve[w>>1]|0;o=(f<<16>>16<<14)+o|0;e=o>>16;o=(o>>>1)-(e<<15)<<16;D=(((Ze(o>>16,b)|0)>>15)+(Ze(e,b)|0)<<2)-((u>>15)+(D<<16))|0;u=Ve[h>>1]|0;D=(u<<16>>16<<14)+D|0;A=D>>16;e=(((Ze((D>>>1)-(A<<15)<<16>>16,b)|0)>>15)+(Ze(A,b)|0)<<1)-((o>>15)+(e<<16))|0;o=Ve[m>>1]|0;e=(o<<16>>16<<13)+e|0;e=(e+33554432|0)>>>0<67108863?e>>>10&65535:(e|0)>33554431?32767:-32768;if((Ze(e<<16>>16,t<<16>>16)|0)<1){b=s;s=c;break}else{d=d+1|0;a=l;t=e}}p=o<<16>>16<<13;m=s<<16>>16<<14;c=f<<16>>16<<14;h=u<<16>>16<<14;o=l<<16>>16;d=4;while(1){A=(a<<16>>16>>>1)+(o>>>1)|0;o=A<<16;w=o>>16;o=v+(o>>6)|0;D=o&-65536;o=(o>>>1)-(o>>16<<15)<<16;u=m+((((Ze(o>>16,w)|0)>>15)+(Ze(D>>16,w)|0)<<2)+-16777216)|0;s=u>>16;u=(u>>>1)-(s<<15)<<16;D=c+((((Ze(u>>16,w)|0)>>15)+(Ze(s,w)|0)<<2)-((o>>15)+D))|0;o=D>>16;D=(D>>>1)-(o<<15)<<16;s=h+((((Ze(D>>16,w)|0)>>15)+(Ze(o,w)|0)<<2)-((u>>15)+(s<<16)))|0;u=s>>16;A=A&65535;o=p+((((Ze((s>>>1)-(u<<15)<<16>>16,w)|0)>>15)+(Ze(u,w)|0)<<1)-((D>>15)+(o<<16)))|0;o=(o+33554432|0)>>>0<67108863?o>>>10&65535:(o|0)>33554431?32767:-32768;D=(Ze(o<<16>>16,e<<16>>16)|0)<1;w=D?l:A;e=D?e:o;a=D?A:a;t=D?o:t;d=d+-1<<16>>16;o=w<<16>>16;if(!(d<<16>>16)){l=o;f=a;a=w;break}else l=w}s=n<<16>>16;o=e<<16>>16;e=(t&65535)-o|0;t=e<<16;if(t){D=(e&65535)-(e>>>15&1)|0;D=D<<16>>31^D;e=(Et(D&65535)|0)<<16>>16;e=(Ze((Kn(16383,D<<16>>16<<e&65535)|0)<<16>>16,(f&65535)-l<<16>>16)|0)>>19-e;if((t|0)<0)e=0-(e<<16>>16)|0;a=l-((Ze(e<<16>>16,o)|0)>>>10)&65535}Ve[r+(s<<1)>>1]=a;t=k<<16>>16==0?g:E;A=a<<16>>16;e=Ve[t+2>>1]|0;o=(e<<16>>16<<14)+(A<<10)|0;D=o&-65536;o=(o>>>1)-(o>>16<<15)<<16;v=(((Ze(o>>16,A)|0)>>15)+(Ze(D>>16,A)|0)<<2)+-16777216|0;v=(Ve[t+4>>1]<<14)+v|0;p=v>>16;v=(v>>>1)-(p<<15)<<16;D=(((Ze(v>>16,A)|0)>>15)+(Ze(p,A)|0)<<2)-((o>>15)+D)|0;D=(Ve[t+6>>1]<<14)+D|0;o=D>>16;D=(D>>>1)-(o<<15)<<16;p=(((Ze(D>>16,A)|0)>>15)+(Ze(o,A)|0)<<2)-((v>>15)+(p<<16))|0;p=(Ve[t+8>>1]<<14)+p|0;v=p>>16;n=n+1<<16>>16;o=(((Ze((p>>>1)-(v<<15)<<16>>16,A)|0)>>15)+(Ze(v,A)|0)<<1)-((D>>15)+(o<<16))|0;o=(Ve[t+10>>1]<<13)+o|0;if(n<<16>>16<10){p=t+4|0;s=t;k=k^1;f=b;m=t+10|0;t=(o+33554432|0)>>>0<67108863?o>>>10&65535:(o|0)>33554431?32767:-32768}else{_=13;break}}if((_|0)==13){Ke=y;return}Ve[r>>1]=Ve[i>>1]|0;Ve[r+2>>1]=Ve[i+2>>1]|0;Ve[r+4>>1]=Ve[i+4>>1]|0;Ve[r+6>>1]=Ve[i+6>>1]|0;Ve[r+8>>1]=Ve[i+8>>1]|0;Ve[r+10>>1]=Ve[i+10>>1]|0;Ve[r+12>>1]=Ve[i+12>>1]|0;Ve[r+14>>1]=Ve[i+14>>1]|0;Ve[r+16>>1]=Ve[i+16>>1]|0;Ve[r+18>>1]=Ve[i+18>>1]|0;Ke=y;return}function Kn(e,r){e=e|0;r=r|0;var i=0,n=0,t=0,o=0,a=0,f=0;t=r<<16>>16;if(e<<16>>16<1?1:e<<16>>16>r<<16>>16){t=0;return t|0}if(e<<16>>16==r<<16>>16){t=32767;return t|0}n=t<<1;i=t<<2;o=e<<16>>16<<3;e=(o|0)<(i|0);o=o-(e?0:i)|0;e=e?0:4;a=(o|0)<(n|0);o=o-(a?0:n)|0;r=(o|0)<(t|0);e=(r&1|(a?e:e|2))<<3^8;r=o-(r?0:t)<<3;if((r|0)>=(i|0)){r=r-i|0;e=e&65528|4}o=(r|0)<(n|0);a=r-(o?0:n)|0;r=(a|0)<(t|0);e=(r&1^1|(o?e:e|2))<<16>>13;r=a-(r?0:t)<<3;if((r|0)>=(i|0)){r=r-i|0;e=e&65528|4}o=(r|0)<(n|0);a=r-(o?0:n)|0;r=(a|0)<(t|0);e=(r&1^1|(o?e:e|2))<<16>>13;r=a-(r?0:t)<<3;if((r|0)>=(i|0)){r=r-i|0;e=e&65528|4}f=(r|0)<(n|0);o=r-(f?0:n)|0;a=(o|0)<(t|0);r=(a&1^1|(f?e:e|2))<<16>>13;e=o-(a?0:t)<<3;if((e|0)>=(i|0)){e=e-i|0;r=r&65528|4}f=(e|0)<(n|0);f=((e-(f?0:n)|0)>=(t|0)|(f?r:r|2))&65535;return f|0}function Zn(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=-14336;Ve[e+8>>1]=-2381;Ve[e+2>>1]=-14336;Ve[e+10>>1]=-2381;Ve[e+4>>1]=-14336;Ve[e+12>>1]=-2381;Ve[e+6>>1]=-14336;Ve[e+14>>1]=-2381;e=0;return e|0}function Qn(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0;h=Ke;Ke=Ke+16|0;c=h+2|0;d=h;s=0;l=10;while(1){u=Ve[i>>1]|0;u=((Ze(u,u)|0)>>>3)+s|0;s=Ve[i+2>>1]|0;s=u+((Ze(s,s)|0)>>>3)|0;u=Ve[i+4>>1]|0;u=s+((Ze(u,u)|0)>>>3)|0;s=Ve[i+6>>1]|0;s=u+((Ze(s,s)|0)>>>3)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else i=i+8|0}l=s<<4;l=(l|0)<0?2147483647:l;if((r|0)==7){ft(((Ft(l,f)|0)<<16>>16)*52428|0,c,d,f);u=We[c>>1]<<16;l=Ve[d>>1]<<1;r=Ve[e+8>>1]|0;s=(r<<16>>16)*88|0;if(r<<16>>16>-1&(s|0)<-783741){Ge[f>>2]=1;i=2147483647}else i=s+783741|0;r=(Ve[e+10>>1]|0)*74|0;s=r+i|0;if((r^i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;i=(i>>>31)+2147483647|0}else i=s;r=(Ve[e+12>>1]|0)*44|0;s=r+i|0;if((r^i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;i=(i>>>31)+2147483647|0}else i=s;e=(Ve[e+14>>1]|0)*24|0;s=e+i|0;if((e^i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}e=u+-1966080+l|0;i=s-e|0;if(((i^s)&(s^e)|0)<0){Ge[f>>2]=1;i=(s>>>31)+2147483647|0}f=i>>17;Ve[n>>1]=f;f=(i>>2)-(f<<15)|0;f=f&65535;Ve[t>>1]=f;Ke=h;return}u=kt(l)|0;s=u<<16>>16;if(u<<16>>16>0){i=l<<s;if((i>>s|0)==(l|0))l=i;else l=l>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)l=l>>(s>>16);else l=0}st(l,u,c,d);c=Ze(Ve[c>>1]|0,-49320)|0;s=(Ze(Ve[d>>1]|0,-24660)|0)>>15;s=(s&65536|0)==0?s:s|-65536;d=s<<1;i=d+c|0;if((d^c|0)>-1&(i^d|0)<0){Ge[f>>2]=1;i=(s>>>30&1)+2147483647|0}switch(r|0){case 6:{s=i+2134784|0;if((i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}break}case 5:{Ve[a>>1]=l>>>16;Ve[o>>1]=-11-(u&65535);s=i+2183936|0;if((i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}break}case 4:{s=i+2085632|0;if((i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}break}case 3:{s=i+2065152|0;if((i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}break}default:{s=i+2134784|0;if((i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}}}do{if((s|0)<=2097151)if((s|0)<-2097152){Ge[f>>2]=1;i=-2147483648;break}else{i=s<<10;break}else{Ge[f>>2]=1;i=2147483647}}while(0);a=(Ve[e>>1]|0)*11142|0;s=a+i|0;if((a^i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}a=(Ve[e+2>>1]|0)*9502|0;i=a+s|0;if((a^s|0)>-1&(i^s|0)<0){Ge[f>>2]=1;i=(s>>>31)+2147483647|0}a=(Ve[e+4>>1]|0)*5570|0;s=a+i|0;if((a^i|0)>-1&(s^i|0)<0){Ge[f>>2]=1;s=(i>>>31)+2147483647|0}e=(Ve[e+6>>1]|0)*3112|0;i=e+s|0;if((e^s|0)>-1&(i^s|0)<0){Ge[f>>2]=1;i=(s>>>31)+2147483647|0}i=Ze(i>>16,(r|0)==4?10878:10886)|0;if((i|0)<0)i=~((i^-256)>>8);else i=i>>8;Ve[n>>1]=i>>>16;if((i|0)<0)s=~((i^-2)>>1);else s=i>>1;n=i>>16<<15;i=s-n|0;if(((i^s)&(n^s)|0)>=0){f=i;f=f&65535;Ve[t>>1]=f;Ke=h;return}Ge[f>>2]=1;f=(s>>>31)+2147483647|0;f=f&65535;Ve[t>>1]=f;Ke=h;return}function Jn(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;t=e+4|0;Ve[e+6>>1]=Ve[t>>1]|0;o=e+12|0;Ve[e+14>>1]=Ve[o>>1]|0;n=e+2|0;Ve[t>>1]=Ve[n>>1]|0;t=e+10|0;Ve[o>>1]=Ve[t>>1]|0;Ve[n>>1]=Ve[e>>1]|0;n=e+8|0;Ve[t>>1]=Ve[n>>1]|0;Ve[n>>1]=r;Ve[e>>1]=i;return}function $n(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0;o=Gn(0,Ve[e+8>>1]|0,n)|0;o=Gn(o,Ve[e+10>>1]|0,n)|0;o=Gn(o,Ve[e+12>>1]|0,n)|0;o=Gn(o,Ve[e+14>>1]|0,n)|0;t=o<<16>>16>>2;t=(o<<16>>16<0?t|49152:t)&65535;Ve[r>>1]=t<<16>>16<-2381?-2381:t;r=Gn(0,Ve[e>>1]|0,n)|0;r=Gn(r,Ve[e+2>>1]|0,n)|0;r=Gn(r,Ve[e+4>>1]|0,n)|0;n=Gn(r,Ve[e+6>>1]|0,n)|0;e=n<<16>>16>>2;e=(n<<16>>16<0?e|49152:e)&65535;Ve[i>>1]=e<<16>>16<-14336?-14336:e;return}function et(e){e=e|0;Ge[e>>2]=6892;Ge[e+4>>2]=8180;Ge[e+8>>2]=21e3;Ge[e+12>>2]=9716;Ge[e+16>>2]=22024;Ge[e+20>>2]=12788;Ge[e+24>>2]=24072;Ge[e+28>>2]=26120;Ge[e+32>>2]=28168;Ge[e+36>>2]=6876;Ge[e+40>>2]=7452;Ge[e+44>>2]=8140;Ge[e+48>>2]=20980;Ge[e+52>>2]=16884;Ge[e+56>>2]=17908;Ge[e+60>>2]=7980;Ge[e+64>>2]=8160;Ge[e+68>>2]=6678;Ge[e+72>>2]=6646;Ge[e+76>>2]=6614;Ge[e+80>>2]=29704;Ge[e+84>>2]=28680;Ge[e+88>>2]=3720;Ge[e+92>>2]=8;Ge[e+96>>2]=4172;Ge[e+100>>2]=44;Ge[e+104>>2]=3436;Ge[e+108>>2]=30316;Ge[e+112>>2]=30796;Ge[e+116>>2]=31276;Ge[e+120>>2]=7472;Ge[e+124>>2]=7552;Ge[e+128>>2]=7632;Ge[e+132>>2]=7712;return}function rt(e,r){e=e|0;r=r|0;var i=0,n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0;c=Ke;Ke=Ke+48|0;l=c+18|0;u=c;s=r<<16>>16;Yt(u|0,e|0,s<<1|0)|0;if(r<<16>>16>0){i=0;n=0}else{u=s>>1;u=l+(u<<1)|0;u=Ve[u>>1]|0;u=u<<16>>16;u=e+(u<<1)|0;u=Ve[u>>1]|0;Ke=c;return u|0}do{f=0;a=-32767;while(1){t=Ve[u+(f<<1)>>1]|0;o=t<<16>>16<a<<16>>16;n=o?n:f&65535;f=f+1|0;if((f&65535)<<16>>16==r<<16>>16)break;else a=o?a:t}Ve[u+(n<<16>>16<<1)>>1]=-32768;Ve[l+(i<<1)>>1]=n;i=i+1|0}while((i&65535)<<16>>16!=r<<16>>16);u=s>>1;u=l+(u<<1)|0;u=Ve[u>>1]|0;u=u<<16>>16;u=e+(u<<1)|0;u=Ve[u>>1]|0;Ke=c;return u|0}function it(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0;o=Ke;Ke=Ke+32|0;a=o;A=r+2|0;_=a+2|0;Ve[a>>1]=((Ve[r>>1]|0)>>>1)+((Ve[e>>1]|0)>>>1);y=r+4|0;g=a+4|0;Ve[_>>1]=((Ve[A>>1]|0)>>>1)+((Ve[e+2>>1]|0)>>>1);E=r+6|0;k=a+6|0;Ve[g>>1]=((Ve[y>>1]|0)>>>1)+((Ve[e+4>>1]|0)>>>1);b=r+8|0;v=a+8|0;Ve[k>>1]=((Ve[E>>1]|0)>>>1)+((Ve[e+6>>1]|0)>>>1);p=r+10|0;m=a+10|0;Ve[v>>1]=((Ve[b>>1]|0)>>>1)+((Ve[e+8>>1]|0)>>>1);w=r+12|0;h=a+12|0;Ve[m>>1]=((Ve[p>>1]|0)>>>1)+((Ve[e+10>>1]|0)>>>1);d=r+14|0;c=a+14|0;Ve[h>>1]=((Ve[w>>1]|0)>>>1)+((Ve[e+12>>1]|0)>>>1);u=r+16|0;l=a+16|0;Ve[c>>1]=((Ve[d>>1]|0)>>>1)+((Ve[e+14>>1]|0)>>>1);s=r+18|0;f=a+18|0;Ve[l>>1]=((Ve[u>>1]|0)>>>1)+((Ve[e+16>>1]|0)>>>1);Ve[f>>1]=((Ve[s>>1]|0)>>>1)+((Ve[e+18>>1]|0)>>>1);ct(a,n,t);ct(r,n+22|0,t);Ve[a>>1]=((Ve[i>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[_>>1]=((Ve[i+2>>1]|0)>>>1)+((Ve[A>>1]|0)>>>1);Ve[g>>1]=((Ve[i+4>>1]|0)>>>1)+((Ve[y>>1]|0)>>>1);Ve[k>>1]=((Ve[i+6>>1]|0)>>>1)+((Ve[E>>1]|0)>>>1);Ve[v>>1]=((Ve[i+8>>1]|0)>>>1)+((Ve[b>>1]|0)>>>1);Ve[m>>1]=((Ve[i+10>>1]|0)>>>1)+((Ve[p>>1]|0)>>>1);Ve[h>>1]=((Ve[i+12>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[c>>1]=((Ve[i+14>>1]|0)>>>1)+((Ve[d>>1]|0)>>>1);Ve[l>>1]=((Ve[i+16>>1]|0)>>>1)+((Ve[u>>1]|0)>>>1);Ve[f>>1]=((Ve[i+18>>1]|0)>>>1)+((Ve[s>>1]|0)>>>1);ct(a,n+44|0,t);ct(i,n+66|0,t);Ke=o;return}function nt(e,r,i,n,t){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;var o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0;o=Ke;Ke=Ke+32|0;a=o;A=r+2|0;_=a+2|0;Ve[a>>1]=((Ve[r>>1]|0)>>>1)+((Ve[e>>1]|0)>>>1);y=r+4|0;g=a+4|0;Ve[_>>1]=((Ve[A>>1]|0)>>>1)+((Ve[e+2>>1]|0)>>>1);E=r+6|0;k=a+6|0;Ve[g>>1]=((Ve[y>>1]|0)>>>1)+((Ve[e+4>>1]|0)>>>1);b=r+8|0;v=a+8|0;Ve[k>>1]=((Ve[E>>1]|0)>>>1)+((Ve[e+6>>1]|0)>>>1);p=r+10|0;m=a+10|0;Ve[v>>1]=((Ve[b>>1]|0)>>>1)+((Ve[e+8>>1]|0)>>>1);w=r+12|0;h=a+12|0;Ve[m>>1]=((Ve[p>>1]|0)>>>1)+((Ve[e+10>>1]|0)>>>1);d=r+14|0;c=a+14|0;Ve[h>>1]=((Ve[w>>1]|0)>>>1)+((Ve[e+12>>1]|0)>>>1);u=r+16|0;l=a+16|0;Ve[c>>1]=((Ve[d>>1]|0)>>>1)+((Ve[e+14>>1]|0)>>>1);s=r+18|0;f=a+18|0;Ve[l>>1]=((Ve[u>>1]|0)>>>1)+((Ve[e+16>>1]|0)>>>1);Ve[f>>1]=((Ve[s>>1]|0)>>>1)+((Ve[e+18>>1]|0)>>>1);ct(a,n,t);Ve[a>>1]=((Ve[i>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[_>>1]=((Ve[i+2>>1]|0)>>>1)+((Ve[A>>1]|0)>>>1);Ve[g>>1]=((Ve[i+4>>1]|0)>>>1)+((Ve[y>>1]|0)>>>1);Ve[k>>1]=((Ve[i+6>>1]|0)>>>1)+((Ve[E>>1]|0)>>>1);Ve[v>>1]=((Ve[i+8>>1]|0)>>>1)+((Ve[b>>1]|0)>>>1);Ve[m>>1]=((Ve[i+10>>1]|0)>>>1)+((Ve[p>>1]|0)>>>1);Ve[h>>1]=((Ve[i+12>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[c>>1]=((Ve[i+14>>1]|0)>>>1)+((Ve[d>>1]|0)>>>1);Ve[l>>1]=((Ve[i+16>>1]|0)>>>1)+((Ve[u>>1]|0)>>>1);Ve[f>>1]=((Ve[i+18>>1]|0)>>>1)+((Ve[s>>1]|0)>>>1);ct(a,n+44|0,t);Ke=o;return}function tt(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0;t=Ke;Ke=Ke+32|0;o=t;L=Ve[e>>1]|0;Ve[o>>1]=L-(L>>>2)+((Ve[r>>1]|0)>>>2);L=e+2|0;M=Ve[L>>1]|0;F=r+2|0;O=o+2|0;Ve[O>>1]=M-(M>>>2)+((Ve[F>>1]|0)>>>2);M=e+4|0;D=Ve[M>>1]|0;N=r+4|0;R=o+4|0;Ve[R>>1]=D-(D>>>2)+((Ve[N>>1]|0)>>>2);D=e+6|0;y=Ve[D>>1]|0;S=r+6|0;A=o+6|0;Ve[A>>1]=y-(y>>>2)+((Ve[S>>1]|0)>>>2);y=e+8|0;k=Ve[y>>1]|0;_=r+8|0;g=o+8|0;Ve[g>>1]=k-(k>>>2)+((Ve[_>>1]|0)>>>2);k=e+10|0;p=Ve[k>>1]|0;E=r+10|0;b=o+10|0;Ve[b>>1]=p-(p>>>2)+((Ve[E>>1]|0)>>>2);p=e+12|0;h=Ve[p>>1]|0;v=r+12|0;m=o+12|0;Ve[m>>1]=h-(h>>>2)+((Ve[v>>1]|0)>>>2);h=e+14|0;u=Ve[h>>1]|0;w=r+14|0;d=o+14|0;Ve[d>>1]=u-(u>>>2)+((Ve[w>>1]|0)>>>2);u=e+16|0;f=Ve[u>>1]|0;c=r+16|0;l=o+16|0;Ve[l>>1]=f-(f>>>2)+((Ve[c>>1]|0)>>>2);f=e+18|0;I=Ve[f>>1]|0;s=r+18|0;a=o+18|0;Ve[a>>1]=I-(I>>>2)+((Ve[s>>1]|0)>>>2);ct(o,i,n);Ve[o>>1]=((Ve[e>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[O>>1]=((Ve[L>>1]|0)>>>1)+((Ve[F>>1]|0)>>>1);Ve[R>>1]=((Ve[M>>1]|0)>>>1)+((Ve[N>>1]|0)>>>1);Ve[A>>1]=((Ve[D>>1]|0)>>>1)+((Ve[S>>1]|0)>>>1);Ve[g>>1]=((Ve[y>>1]|0)>>>1)+((Ve[_>>1]|0)>>>1);Ve[b>>1]=((Ve[k>>1]|0)>>>1)+((Ve[E>>1]|0)>>>1);Ve[m>>1]=((Ve[p>>1]|0)>>>1)+((Ve[v>>1]|0)>>>1);Ve[d>>1]=((Ve[h>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[l>>1]=((Ve[u>>1]|0)>>>1)+((Ve[c>>1]|0)>>>1);Ve[a>>1]=((Ve[f>>1]|0)>>>1)+((Ve[s>>1]|0)>>>1);ct(o,i+22|0,n);I=Ve[r>>1]|0;Ve[o>>1]=I-(I>>>2)+((Ve[e>>1]|0)>>>2);e=Ve[F>>1]|0;Ve[O>>1]=e-(e>>>2)+((Ve[L>>1]|0)>>>2);e=Ve[N>>1]|0;Ve[R>>1]=e-(e>>>2)+((Ve[M>>1]|0)>>>2);e=Ve[S>>1]|0;Ve[A>>1]=e-(e>>>2)+((Ve[D>>1]|0)>>>2);e=Ve[_>>1]|0;Ve[g>>1]=e-(e>>>2)+((Ve[y>>1]|0)>>>2);e=Ve[E>>1]|0;Ve[b>>1]=e-(e>>>2)+((Ve[k>>1]|0)>>>2);e=Ve[v>>1]|0;Ve[m>>1]=e-(e>>>2)+((Ve[p>>1]|0)>>>2);e=Ve[w>>1]|0;Ve[d>>1]=e-(e>>>2)+((Ve[h>>1]|0)>>>2);e=Ve[c>>1]|0;Ve[l>>1]=e-(e>>>2)+((Ve[u>>1]|0)>>>2);e=Ve[s>>1]|0;Ve[a>>1]=e-(e>>>2)+((Ve[f>>1]|0)>>>2);ct(o,i+44|0,n);ct(r,i+66|0,n);Ke=t;return}function ot(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0;t=Ke;Ke=Ke+32|0;o=t;L=Ve[e>>1]|0;Ve[o>>1]=L-(L>>>2)+((Ve[r>>1]|0)>>>2);L=e+2|0;M=Ve[L>>1]|0;F=r+2|0;O=o+2|0;Ve[O>>1]=M-(M>>>2)+((Ve[F>>1]|0)>>>2);M=e+4|0;D=Ve[M>>1]|0;N=r+4|0;R=o+4|0;Ve[R>>1]=D-(D>>>2)+((Ve[N>>1]|0)>>>2);D=e+6|0;y=Ve[D>>1]|0;S=r+6|0;A=o+6|0;Ve[A>>1]=y-(y>>>2)+((Ve[S>>1]|0)>>>2);y=e+8|0;k=Ve[y>>1]|0;_=r+8|0;g=o+8|0;Ve[g>>1]=k-(k>>>2)+((Ve[_>>1]|0)>>>2);k=e+10|0;p=Ve[k>>1]|0;E=r+10|0;b=o+10|0;Ve[b>>1]=p-(p>>>2)+((Ve[E>>1]|0)>>>2);p=e+12|0;h=Ve[p>>1]|0;v=r+12|0;m=o+12|0;Ve[m>>1]=h-(h>>>2)+((Ve[v>>1]|0)>>>2);h=e+14|0;u=Ve[h>>1]|0;w=r+14|0;d=o+14|0;Ve[d>>1]=u-(u>>>2)+((Ve[w>>1]|0)>>>2);u=e+16|0;f=Ve[u>>1]|0;c=r+16|0;l=o+16|0;Ve[l>>1]=f-(f>>>2)+((Ve[c>>1]|0)>>>2);f=e+18|0;I=Ve[f>>1]|0;s=r+18|0;a=o+18|0;Ve[a>>1]=I-(I>>>2)+((Ve[s>>1]|0)>>>2);ct(o,i,n);Ve[o>>1]=((Ve[e>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[O>>1]=((Ve[L>>1]|0)>>>1)+((Ve[F>>1]|0)>>>1);Ve[R>>1]=((Ve[M>>1]|0)>>>1)+((Ve[N>>1]|0)>>>1);Ve[A>>1]=((Ve[D>>1]|0)>>>1)+((Ve[S>>1]|0)>>>1);Ve[g>>1]=((Ve[y>>1]|0)>>>1)+((Ve[_>>1]|0)>>>1);Ve[b>>1]=((Ve[k>>1]|0)>>>1)+((Ve[E>>1]|0)>>>1);Ve[m>>1]=((Ve[p>>1]|0)>>>1)+((Ve[v>>1]|0)>>>1);Ve[d>>1]=((Ve[h>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[l>>1]=((Ve[u>>1]|0)>>>1)+((Ve[c>>1]|0)>>>1);Ve[a>>1]=((Ve[f>>1]|0)>>>1)+((Ve[s>>1]|0)>>>1);ct(o,i+22|0,n);r=Ve[r>>1]|0;Ve[o>>1]=r-(r>>>2)+((Ve[e>>1]|0)>>>2);e=Ve[F>>1]|0;Ve[O>>1]=e-(e>>>2)+((Ve[L>>1]|0)>>>2);e=Ve[N>>1]|0;Ve[R>>1]=e-(e>>>2)+((Ve[M>>1]|0)>>>2);e=Ve[S>>1]|0;Ve[A>>1]=e-(e>>>2)+((Ve[D>>1]|0)>>>2);e=Ve[_>>1]|0;Ve[g>>1]=e-(e>>>2)+((Ve[y>>1]|0)>>>2);e=Ve[E>>1]|0;Ve[b>>1]=e-(e>>>2)+((Ve[k>>1]|0)>>>2);e=Ve[v>>1]|0;Ve[m>>1]=e-(e>>>2)+((Ve[p>>1]|0)>>>2);e=Ve[w>>1]|0;Ve[d>>1]=e-(e>>>2)+((Ve[h>>1]|0)>>>2);e=Ve[c>>1]|0;Ve[l>>1]=e-(e>>>2)+((Ve[u>>1]|0)>>>2);e=Ve[s>>1]|0;Ve[a>>1]=e-(e>>>2)+((Ve[f>>1]|0)>>>2);ct(o,i+44|0,n);Ke=t;return}function at(e,r){e=e|0;r=r|0;var i=0,n=0;if((e|0)<1){r=1073741823;return r|0}i=(kt(e)|0)<<16>>16;r=30-i|0;e=e<<i>>(r&1^1);i=(e>>25<<16)+-1048576>>16;n=Ve[7030+(i<<1)>>1]|0;r=(n<<16)-(Ze(n-(We[7030+(i+1<<1)>>1]|0)<<16>>15,e>>>10&32767)|0)>>(r<<16>>17)+1;return r|0}function ft(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;n=kt(e)|0;st(e<<(n<<16>>16),n,r,i);return}function st(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;if((e|0)<1){Ve[i>>1]=0;i=0;Ve[n>>1]=i;return}else{Ve[i>>1]=30-(r&65535);i=(e>>25<<16)+-2097152>>16;r=Ve[7128+(i<<1)>>1]|0;i=((r<<16)-(Ze(e>>>9&65534,r-(We[7128+(i+1<<1)>>1]|0)<<16>>16)|0)|0)>>>16&65535;Ve[n>>1]=i;return}}function lt(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0;n=e+2|0;i=Ve[n>>1]|0;Ve[r>>1]=i;t=e+4|0;Ve[r+2>>1]=(We[t>>1]|0)-(We[e>>1]|0);Ve[r+4>>1]=(We[e+6>>1]|0)-(We[n>>1]|0);n=e+8|0;Ve[r+6>>1]=(We[n>>1]|0)-(We[t>>1]|0);Ve[r+8>>1]=(We[e+10>>1]|0)-(We[e+6>>1]|0);t=e+12|0;Ve[r+10>>1]=(We[t>>1]|0)-(We[n>>1]|0);Ve[r+12>>1]=(We[e+14>>1]|0)-(We[e+10>>1]|0);Ve[r+14>>1]=(We[e+16>>1]|0)-(We[t>>1]|0);Ve[r+16>>1]=(We[e+18>>1]|0)-(We[e+14>>1]|0);Ve[r+18>>1]=16384-(We[e+16>>1]|0);e=10;t=r;while(1){i=i<<16>>16;r=(i<<16)+-120782848|0;if((r|0)>0)r=1843-((r>>16)*12484>>16)|0;else r=3427-((i*56320|0)>>>16)|0;n=t+2|0;Ve[t>>1]=r<<3;e=e+-1<<16>>16;if(!(e<<16>>16))break;i=Ve[n>>1]|0;t=n}return}function ut(e,r,i){e=e|0;r=r|0;i=i|0;i=r<<16>>16;if(r<<16>>16>31){r=0;return r|0}if(r<<16>>16>0)return((1<<i+-1&e|0)!=0&1)+(r<<16>>16<31?e>>i:0)|0;i=0-i<<16>>16;r=e<<i;r=(r>>i|0)==(e|0)?r:e>>31^2147483647;return r|0}function ct(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0;m=Ke;Ke=Ke+48|0;h=m+24|0;w=m;c=h+4|0;Ge[h>>2]=16777216;n=0-(Ve[e>>1]|0)|0;d=h+8|0;Ge[c>>2]=n<<10;t=Ve[e+4>>1]|0;s=n>>6;Ge[d>>2]=33554432-(((Ze((n<<9)-(s<<15)<<16>>16,t)|0)>>15)+(Ze(s,t)|0)<<2);s=h+4|0;t=(Ge[s>>2]|0)-(t<<10)|0;Ge[s>>2]=t;s=h+12|0;n=h+4|0;Ge[s>>2]=t;i=Ve[e+8>>1]|0;o=t;l=1;while(1){f=s+-4|0;a=Ge[f>>2]|0;u=a>>16;Ge[s>>2]=o+t-(((Ze((a>>>1)-(u<<15)<<16>>16,i)|0)>>15)+(Ze(u,i)|0)<<2);if((l|0)==2)break;o=Ge[s+-12>>2]|0;s=f;t=a;l=l+1|0}Ge[n>>2]=(Ge[n>>2]|0)-(i<<10);i=h+16|0;n=Ge[h+8>>2]|0;Ge[i>>2]=n;f=Ve[e+12>>1]|0;t=n;s=1;while(1){a=i+-4|0;o=Ge[a>>2]|0;u=o>>16;Ge[i>>2]=t+n-(((Ze((o>>>1)-(u<<15)<<16>>16,f)|0)>>15)+(Ze(u,f)|0)<<2);if((s|0)==3)break;t=Ge[i+-12>>2]|0;i=a;n=o;s=s+1|0}i=h+4|0;Ge[i>>2]=(Ge[i>>2]|0)-(f<<10);i=h+20|0;t=Ge[h+12>>2]|0;Ge[i>>2]=t;n=Ve[e+16>>1]|0;o=t;s=1;while(1){f=i+-4|0;a=Ge[f>>2]|0;u=a>>16;Ge[i>>2]=o+t-(((Ze((a>>>1)-(u<<15)<<16>>16,n)|0)>>15)+(Ze(u,n)|0)<<2);if((s|0)==4)break;o=Ge[i+-12>>2]|0;i=f;t=a;s=s+1|0}s=h+4|0;Ge[s>>2]=(Ge[s>>2]|0)-(n<<10);Ge[w>>2]=16777216;s=0-(Ve[e+2>>1]|0)|0;u=w+8|0;Ge[w+4>>2]=s<<10;n=Ve[e+6>>1]|0;l=s>>6;Ge[u>>2]=33554432-(((Ze((s<<9)-(l<<15)<<16>>16,n)|0)>>15)+(Ze(l,n)|0)<<2);l=w+4|0;n=(Ge[l>>2]|0)-(n<<10)|0;Ge[l>>2]=n;l=w+12|0;s=w+4|0;Ge[l>>2]=n;f=Ve[e+10>>1]|0;t=n;i=1;while(1){a=l+-4|0;o=Ge[a>>2]|0;p=o>>16;Ge[l>>2]=t+n-(((Ze((o>>>1)-(p<<15)<<16>>16,f)|0)>>15)+(Ze(p,f)|0)<<2);if((i|0)==2)break;t=Ge[l+-12>>2]|0;l=a;n=o;i=i+1|0}Ge[s>>2]=(Ge[s>>2]|0)-(f<<10);s=w+16|0;n=Ge[w+8>>2]|0;Ge[s>>2]=n;f=Ve[e+14>>1]|0;t=n;i=1;while(1){a=s+-4|0;o=Ge[a>>2]|0;p=o>>16;Ge[s>>2]=t+n-(((Ze((o>>>1)-(p<<15)<<16>>16,f)|0)>>15)+(Ze(p,f)|0)<<2);if((i|0)==3)break;t=Ge[s+-12>>2]|0;s=a;n=o;i=i+1|0}i=w+4|0;Ge[i>>2]=(Ge[i>>2]|0)-(f<<10);i=w+20|0;f=Ge[w+12>>2]|0;Ge[i>>2]=f;n=Ve[e+18>>1]|0;a=f;s=1;while(1){t=i+-4|0;o=Ge[t>>2]|0;p=o>>16;Ge[i>>2]=a+f-(((Ze((o>>>1)-(p<<15)<<16>>16,n)|0)>>15)+(Ze(p,n)|0)<<2);if((s|0)==4)break;a=Ge[i+-12>>2]|0;i=t;f=o;s=s+1|0}a=(Ge[w+4>>2]|0)-(n<<10)|0;l=h+20|0;f=w+20|0;s=Ge[h+16>>2]|0;e=(Ge[l>>2]|0)+s|0;Ge[l>>2]=e;l=Ge[w+16>>2]|0;p=(Ge[f>>2]|0)-l|0;Ge[f>>2]=p;f=Ge[h+12>>2]|0;s=s+f|0;Ge[h+16>>2]=s;o=Ge[w+12>>2]|0;l=l-o|0;Ge[w+16>>2]=l;n=Ge[d>>2]|0;f=f+n|0;Ge[h+12>>2]=f;t=Ge[u>>2]|0;d=o-t|0;Ge[w+12>>2]=d;o=Ge[c>>2]|0;u=n+o|0;Ge[h+8>>2]=u;c=t-a|0;Ge[w+8>>2]=c;h=o+(Ge[h>>2]|0)|0;w=a-(Ge[w>>2]|0)|0;Ve[r>>1]=4096;h=h+4096|0;Ve[r+2>>1]=(h+w|0)>>>13;Ve[r+20>>1]=(h-w|0)>>>13;w=u+4096|0;Ve[r+4>>1]=(w+c|0)>>>13;Ve[r+18>>1]=(w-c|0)>>>13;w=f+4096|0;Ve[r+6>>1]=(w+d|0)>>>13;Ve[r+16>>1]=(w-d|0)>>>13;w=s+4096|0;Ve[r+8>>1]=(w+l|0)>>>13;Ve[r+14>>1]=(w-l|0)>>>13;w=e+4096|0;Ve[r+10>>1]=(w+p|0)>>>13;Ve[r+12>>1]=(w-p|0)>>>13;Ke=m;return}function dt(e){e=e|0;var r=0,i=0,n=0,t=0,o=0;if(!e){o=-1;return o|0}Ge[e>>2]=0;r=Ut(44)|0;if(!r){o=-1;return o|0}i=r+40|0;if((Rt(i)|0)<<16>>16){o=-1;return o|0}n=r;t=7452;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));n=r+20|0;t=7452;o=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(o|0));Mt(Ge[i>>2]|0)|0;Ge[e>>2]=r;o=0;return o|0}function ht(e){e=e|0;var r=0,i=0,n=0;if(!e){n=-1;return n|0}r=e;i=7452;n=r+20|0;do{Ve[r>>1]=Ve[i>>1]|0;r=r+2|0;i=i+2|0}while((r|0)<(n|0));r=e+20|0;i=7452;n=r+20|0;do{Ve[r>>1]=Ve[i>>1]|0;r=r+2|0;i=i+2|0}while((r|0)<(n|0));Mt(Ge[e+40>>2]|0)|0;n=0;return n|0}function wt(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;Nt(r+40|0);zt(Ge[e>>2]|0);Ge[e>>2]=0;return}function mt(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0;d=Ke;Ke=Ke+64|0;c=d+44|0;s=d+24|0;l=d+4|0;u=d;if((r|0)==7){Wn(n+22|0,s,e,f);Wn(n+66|0,o,s,f);nt(e,s,o,n,f);if((i|0)==8)n=6;else{Dt(Ge[e+40>>2]|0,s,o,l,c,Ge[a>>2]|0,f);it(e+20|0,l,c,t,f);t=(Ge[a>>2]|0)+10|0;n=7}}else{Wn(n+66|0,o,e,f);ot(e,o,n,f);if((i|0)==8)n=6;else{_t(Ge[e+40>>2]|0,r,o,c,Ge[a>>2]|0,u,f);tt(e+20|0,c,t,f);t=(Ge[a>>2]|0)+6|0;n=7}}if((n|0)==6){n=e;t=n+20|0;do{Ve[n>>1]=Ve[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(t|0));Ke=d;return}else if((n|0)==7){Ge[a>>2]=t;n=e;t=n+20|0;do{Ve[n>>1]=Ve[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(t|0));n=e+20|0;o=c;t=n+20|0;do{Ve[n>>1]=Ve[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(t|0));Ke=d;return}}function pt(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;if(i<<16>>16>0)n=0;else return;do{o=Ve[e+(n<<1)>>1]|0;a=o>>8;t=Ve[7194+(a<<1)>>1]|0;Ve[r+(n<<1)>>1]=((Ze((Ve[7194+(a+1<<1)>>1]|0)-t|0,o&255)|0)>>>8)+t;n=n+1|0}while((n&65535)<<16>>16!=i<<16>>16);return}function vt(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;n=(i<<16>>16)+-1|0;i=n&65535;if(i<<16>>16<=-1)return;t=63;a=r+(n<<1)|0;o=e+(n<<1)|0;while(1){e=Ve[o>>1]|0;r=t;while(1){n=r<<16>>16;t=Ve[7194+(n<<1)>>1]|0;if(e<<16>>16>t<<16>>16)r=r+-1<<16>>16;else break}Ve[a>>1]=(((Ze(Ve[7324+(n<<1)>>1]|0,(e<<16>>16)-(t<<16>>16)|0)|0)+2048|0)>>>12)+(n<<8);i=i+-1<<16>>16;if(i<<16>>16>-1){t=r;a=a+-2|0;o=o+-2|0}else break}return}function bt(e,r,i){e=e|0;r=r|0;i=i|0;e=(Ze(r<<16>>16,e<<16>>16)|0)+16384>>15;e=e|0-(e&65536);if((e|0)<=32767){if((e|0)<-32768){Ge[i>>2]=1;e=-32768}}else{Ge[i>>2]=1;e=32767}return e&65535|0}function kt(e){e=e|0;var r=0;e:do{if((e|0)!=0?(r=e-(e>>>31)|0,r=r>>31^r,(r&1073741824|0)==0):0){e=r;r=0;while(1){if(e&536870912){e=7;break}if(e&268435456){e=8;break}if(e&134217728){e=9;break}r=r+4<<16>>16;e=e<<4;if(e&1073741824)break e}if((e|0)==7){r=r|1;break}else if((e|0)==8){r=r|2;break}else if((e|0)==9){r=r|3;break}}else r=0}while(0);return r|0}function Et(e){e=e|0;var r=0,i=0;if(!(e<<16>>16)){i=0;return i|0}r=(e&65535)-((e&65535)>>>15&65535)|0;r=(r<<16>>31^r)<<16;e=r>>16;if(!(e&16384)){i=r;r=0}else{i=0;return i|0}while(1){if(e&8192){e=r;i=7;break}if(e&4096){e=r;i=8;break}if(e&2048){e=r;i=9;break}r=r+4<<16>>16;i=i<<4;e=i>>16;if(e&16384){e=r;i=10;break}}if((i|0)==7){i=e|1;return i|0}else if((i|0)==8){i=e|2;return i|0}else if((i|0)==9){i=e|3;return i|0}else if((i|0)==10)return e|0;return 0}function gt(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;r=r<<16>>16;if((r&134217727|0)==33554432){Ge[i>>2]=1;r=2147483647}else r=r<<6;n=r>>>16&31;o=Ve[7792+(n<<1)>>1]|0;t=o<<16;r=Ze(o-(We[7792+(n+1<<1)>>1]|0)<<16>>16,r>>>1&32767)|0;if((r|0)==1073741824){Ge[i>>2]=1;n=2147483647}else n=r<<1;r=t-n|0;if(((r^t)&(n^t)|0)>=0){o=r;e=e&65535;e=30-e|0;e=e&65535;i=ut(o,e,i)|0;return i|0}Ge[i>>2]=1;o=(o>>>15&1)+2147483647|0;e=e&65535;e=30-e|0;e=e&65535;i=ut(o,e,i)|0;return i|0}function yt(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0;d=Ke;Ke=Ke+48|0;c=d;u=0-(i&65535)|0;u=t<<16>>16==0?u:u<<1&131070;i=u&65535;u=(i<<16>>16<0?u+6|0:u)<<16>>16;o=6-u|0;Ve[c>>1]=Ve[7858+(u<<1)>>1]|0;Ve[c+2>>1]=Ve[7858+(o<<1)>>1]|0;Ve[c+4>>1]=Ve[7858+(u+6<<1)>>1]|0;Ve[c+6>>1]=Ve[7858+(o+6<<1)>>1]|0;Ve[c+8>>1]=Ve[7858+(u+12<<1)>>1]|0;Ve[c+10>>1]=Ve[7858+(o+12<<1)>>1]|0;Ve[c+12>>1]=Ve[7858+(u+18<<1)>>1]|0;Ve[c+14>>1]=Ve[7858+(o+18<<1)>>1]|0;Ve[c+16>>1]=Ve[7858+(u+24<<1)>>1]|0;Ve[c+18>>1]=Ve[7858+(o+24<<1)>>1]|0;Ve[c+20>>1]=Ve[7858+(u+30<<1)>>1]|0;Ve[c+22>>1]=Ve[7858+(o+30<<1)>>1]|0;Ve[c+24>>1]=Ve[7858+(u+36<<1)>>1]|0;Ve[c+26>>1]=Ve[7858+(o+36<<1)>>1]|0;Ve[c+28>>1]=Ve[7858+(u+42<<1)>>1]|0;Ve[c+30>>1]=Ve[7858+(o+42<<1)>>1]|0;Ve[c+32>>1]=Ve[7858+(u+48<<1)>>1]|0;Ve[c+34>>1]=Ve[7858+(o+48<<1)>>1]|0;Ve[c+36>>1]=Ve[7858+(u+54<<1)>>1]|0;Ve[c+38>>1]=Ve[7858+(o+54<<1)>>1]|0;o=n<<16>>16>>>1&65535;if(!(o<<16>>16)){Ke=d;return}u=e+((i<<16>>16>>15<<16>>16)-(r<<16>>16)<<1)|0;while(1){l=u+2|0;a=Ve[l>>1]|0;r=a;n=u;f=5;s=c;t=16384;i=16384;while(1){w=Ve[s>>1]|0;m=(Ze(w,r<<16>>16)|0)+i|0;h=Ve[l+-2>>1]|0;i=(Ze(h,w)|0)+t|0;w=n;n=n+4|0;p=Ve[s+2>>1]|0;i=i+(Ze(p,a<<16>>16)|0)|0;t=Ve[n>>1]|0;p=m+(Ze(t,p)|0)|0;l=l+-4|0;m=Ve[s+4>>1]|0;h=p+(Ze(m,h)|0)|0;r=Ve[l>>1]|0;m=i+(Ze(r<<16>>16,m)|0)|0;i=Ve[s+6>>1]|0;t=m+(Ze(i,t)|0)|0;a=Ve[w+6>>1]|0;i=h+(Ze(a<<16>>16,i)|0)|0;if(f<<16>>16<=1)break;else{f=f+-1<<16>>16;s=s+8|0}}Ve[e>>1]=t>>>15;Ve[e+2>>1]=i>>>15;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{u=u+4|0;e=e+4|0}}Ke=d;return}function _t(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0;D=Ke;Ke=Ke+144|0;p=D+120|0;g=D+100|0;_=D+80|0;A=D+60|0;y=D+40|0;h=D+20|0;w=D;vt(i,p,10,a);lt(p,g,a);if((r|0)==8){Ve[o>>1]=0;s=2147483647;m=0;while(1){u=m*10|0;i=0;l=0;do{E=(We[7980+(l+u<<1)>>1]|0)+(We[8140+(l<<1)>>1]|0)|0;Ve[w+(l<<1)>>1]=E;E=(We[p+(l<<1)>>1]|0)-(E&65535)|0;Ve[h+(l<<1)>>1]=E;E=E<<16;i=(Ze(E>>15,E>>16)|0)+i|0;l=l+1|0}while((l|0)!=10);if((i|0)<(s|0)){v=A;d=h;c=v+20|0;do{Ve[v>>1]=Ve[d>>1]|0;v=v+2|0;d=d+2|0}while((v|0)<(c|0));v=_;d=w;c=v+20|0;do{Ve[v>>1]=Ve[d>>1]|0;v=v+2|0;d=d+2|0}while((v|0)<(c|0));v=e;d=7980+(u<<1)|0;c=v+20|0;do{Ve[v>>1]=Ve[d>>1]|0;v=v+2|0;d=d+2|0}while((v|0)<(c|0));Ve[o>>1]=m}else i=s;m=m+1|0;if((m|0)==8)break;else s=i}}else{i=0;do{E=Ze(Ve[8160+(i<<1)>>1]|0,Ve[e+(i<<1)>>1]|0)|0;E=(E>>>15)+(We[8140+(i<<1)>>1]|0)|0;Ve[_+(i<<1)>>1]=E;Ve[A+(i<<1)>>1]=(We[p+(i<<1)>>1]|0)-E;i=i+1|0}while((i|0)!=10)}do{if(r>>>0>=2){E=A+2|0;k=A+4|0;b=We[A>>1]|0;v=Ve[g>>1]<<1;p=We[E>>1]|0;h=Ve[g+2>>1]<<1;d=We[k>>1]|0;c=Ve[g+4>>1]<<1;if((r|0)==5){w=2147483647;o=0;i=0;m=17908;while(1){l=(Ze(b-(We[m>>1]|0)<<16>>16,v)|0)>>16;l=Ze(l,l)|0;u=(Ze(p-(We[m+2>>1]|0)<<16>>16,h)|0)>>16;l=(Ze(u,u)|0)+l|0;u=(Ze(d-(We[m+4>>1]|0)<<16>>16,c)|0)>>16;u=l+(Ze(u,u)|0)|0;l=(u|0)<(w|0);i=l?o:i;o=o+1<<16>>16;if(o<<16>>16>=512)break;else{w=l?u:w;m=m+6|0}}u=(i<<16>>16)*3|0;Ve[A>>1]=Ve[17908+(u<<1)>>1]|0;Ve[E>>1]=Ve[17908+(u+1<<1)>>1]|0;Ve[k>>1]=Ve[17908+(u+2<<1)>>1]|0;Ve[t>>1]=i;u=A+6|0;l=A+8|0;b=A+10|0;m=We[u>>1]|0;o=Ve[g+6>>1]<<1;w=We[l>>1]|0;h=Ve[g+8>>1]<<1;d=We[b>>1]|0;c=Ve[g+10>>1]<<1;f=2147483647;p=0;i=0;v=9716;while(1){s=(Ze(o,m-(We[v>>1]|0)<<16>>16)|0)>>16;s=Ze(s,s)|0;r=(Ze(h,w-(We[v+2>>1]|0)<<16>>16)|0)>>16;s=(Ze(r,r)|0)+s|0;r=(Ze(c,d-(We[v+4>>1]|0)<<16>>16)|0)>>16;r=s+(Ze(r,r)|0)|0;s=(r|0)<(f|0);i=s?p:i;p=p+1<<16>>16;if(p<<16>>16>=512)break;else{f=s?r:f;v=v+6|0}}f=(i<<16>>16)*3|0;Ve[u>>1]=Ve[9716+(f<<1)>>1]|0;Ve[l>>1]=Ve[9716+(f+1<<1)>>1]|0;Ve[b>>1]=Ve[9716+(f+2<<1)>>1]|0;Ve[t+2>>1]=i;f=A+12|0;Ve[t+4>>1]=At(f,12788,g+12|0,512)|0;p=E;m=k;i=b;s=A;break}else{w=2147483647;o=0;i=0;m=8180;while(1){l=(Ze(b-(We[m>>1]|0)<<16>>16,v)|0)>>16;l=Ze(l,l)|0;u=(Ze(p-(We[m+2>>1]|0)<<16>>16,h)|0)>>16;l=(Ze(u,u)|0)+l|0;u=(Ze(d-(We[m+4>>1]|0)<<16>>16,c)|0)>>16;u=l+(Ze(u,u)|0)|0;l=(u|0)<(w|0);i=l?o:i;o=o+1<<16>>16;if(o<<16>>16>=256)break;else{w=l?u:w;m=m+6|0}}u=(i<<16>>16)*3|0;Ve[A>>1]=Ve[8180+(u<<1)>>1]|0;Ve[E>>1]=Ve[8180+(u+1<<1)>>1]|0;Ve[k>>1]=Ve[8180+(u+2<<1)>>1]|0;Ve[t>>1]=i;u=A+6|0;l=A+8|0;b=A+10|0;m=We[u>>1]|0;o=Ve[g+6>>1]<<1;w=We[l>>1]|0;h=Ve[g+8>>1]<<1;d=We[b>>1]|0;c=Ve[g+10>>1]<<1;f=2147483647;p=0;i=0;v=9716;while(1){s=(Ze(o,m-(We[v>>1]|0)<<16>>16)|0)>>16;s=Ze(s,s)|0;r=(Ze(h,w-(We[v+2>>1]|0)<<16>>16)|0)>>16;s=(Ze(r,r)|0)+s|0;r=(Ze(c,d-(We[v+4>>1]|0)<<16>>16)|0)>>16;r=s+(Ze(r,r)|0)|0;s=(r|0)<(f|0);i=s?p:i;p=p+1<<16>>16;if(p<<16>>16>=512)break;else{f=s?r:f;v=v+6|0}}f=(i<<16>>16)*3|0;Ve[u>>1]=Ve[9716+(f<<1)>>1]|0;Ve[l>>1]=Ve[9716+(f+1<<1)>>1]|0;Ve[b>>1]=Ve[9716+(f+2<<1)>>1]|0;Ve[t+2>>1]=i;f=A+12|0;Ve[t+4>>1]=At(f,12788,g+12|0,512)|0;p=E;m=k;i=b;s=A;break}}else{k=A+2|0;E=A+4|0;u=We[A>>1]|0;l=Ve[g>>1]<<1;s=We[k>>1]|0;f=Ve[g+2>>1]<<1;r=We[E>>1]|0;c=Ve[g+4>>1]<<1;w=2147483647;o=0;i=0;m=8180;while(1){h=(Ze(l,u-(We[m>>1]|0)<<16>>16)|0)>>16;h=Ze(h,h)|0;d=(Ze(f,s-(We[m+2>>1]|0)<<16>>16)|0)>>16;h=(Ze(d,d)|0)+h|0;d=(Ze(c,r-(We[m+4>>1]|0)<<16>>16)|0)>>16;d=h+(Ze(d,d)|0)|0;h=(d|0)<(w|0);i=h?o:i;o=o+1<<16>>16;if(o<<16>>16>=256)break;else{w=h?d:w;m=m+6|0}}u=(i<<16>>16)*3|0;Ve[A>>1]=Ve[8180+(u<<1)>>1]|0;Ve[k>>1]=Ve[8180+(u+1<<1)>>1]|0;Ve[E>>1]=Ve[8180+(u+2<<1)>>1]|0;Ve[t>>1]=i;u=A+6|0;l=A+8|0;b=A+10|0;m=We[u>>1]|0;o=Ve[g+6>>1]<<1;w=We[l>>1]|0;h=Ve[g+8>>1]<<1;d=We[b>>1]|0;c=Ve[g+10>>1]<<1;f=2147483647;p=0;i=0;v=9716;while(1){s=(Ze(o,m-(We[v>>1]|0)<<16>>16)|0)>>16;s=Ze(s,s)|0;r=(Ze(h,w-(We[v+2>>1]|0)<<16>>16)|0)>>16;s=(Ze(r,r)|0)+s|0;r=(Ze(c,d-(We[v+4>>1]|0)<<16>>16)|0)>>16;r=s+(Ze(r,r)|0)|0;s=(r|0)<(f|0);i=s?p:i;p=p+1<<16>>16;if(p<<16>>16>=256)break;else{f=s?r:f;v=v+12|0}}f=(i<<16>>16)*6|0;Ve[u>>1]=Ve[9716+(f<<1)>>1]|0;Ve[l>>1]=Ve[9716+((f|1)<<1)>>1]|0;Ve[b>>1]=Ve[9716+(f+2<<1)>>1]|0;Ve[t+2>>1]=i;f=A+12|0;Ve[t+4>>1]=At(f,16884,g+12|0,128)|0;p=k;m=E;i=b;s=A}}while(0);v=e;d=A;c=v+20|0;do{Ve[v>>1]=Ve[d>>1]|0;v=v+2|0;d=d+2|0}while((v|0)<(c|0));Ve[y>>1]=(We[_>>1]|0)+(We[s>>1]|0);Ve[y+2>>1]=(We[_+2>>1]|0)+(We[p>>1]|0);Ve[y+4>>1]=(We[_+4>>1]|0)+(We[m>>1]|0);Ve[y+6>>1]=(We[_+6>>1]|0)+(We[u>>1]|0);Ve[y+8>>1]=(We[_+8>>1]|0)+(We[l>>1]|0);Ve[y+10>>1]=(We[_+10>>1]|0)+(We[i>>1]|0);Ve[y+12>>1]=(We[_+12>>1]|0)+(We[f>>1]|0);Ve[y+14>>1]=(We[_+14>>1]|0)+(We[A+14>>1]|0);Ve[y+16>>1]=(We[_+16>>1]|0)+(We[A+16>>1]|0);Ve[y+18>>1]=(We[_+18>>1]|0)+(We[A+18>>1]|0);Ot(y,205,10,a);pt(y,n,10,a);Ke=D;return}function At(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0;v=e+2|0;b=e+4|0;k=e+6|0;if(n<<16>>16>0){u=We[e>>1]|0;c=Ve[i>>1]<<1;d=We[v>>1]|0;h=Ve[i+2>>1]<<1;w=We[b>>1]|0;m=Ve[i+4>>1]<<1;p=We[k>>1]|0;t=Ve[i+6>>1]<<1;f=2147483647;s=0;i=0;l=r;while(1){o=(Ze(c,u-(We[l>>1]|0)<<16>>16)|0)>>16;o=Ze(o,o)|0;a=(Ze(h,d-(We[l+2>>1]|0)<<16>>16)|0)>>16;o=(Ze(a,a)|0)+o|0;a=(Ze(m,w-(We[l+4>>1]|0)<<16>>16)|0)>>16;a=o+(Ze(a,a)|0)|0;o=(Ze(t,p-(We[l+6>>1]|0)<<16>>16)|0)>>16;o=a+(Ze(o,o)|0)|0;a=(o|0)<(f|0);i=a?s:i;s=s+1<<16>>16;if(s<<16>>16>=n<<16>>16)break;else{f=a?o:f;l=l+8|0}}}else i=0;n=i<<16>>16<<2;p=n|1;Ve[e>>1]=Ve[r+(n<<1)>>1]|0;Ve[v>>1]=Ve[r+(p<<1)>>1]|0;Ve[b>>1]=Ve[r+(p+1<<1)>>1]|0;Ve[k>>1]=Ve[r+((n|3)<<1)>>1]|0;return i|0}function Dt(e,r,i,n,t,o,a){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;var f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0;L=Ke;Ke=Ke+192|0;l=L+160|0;s=L+140|0;D=L+120|0;S=L+100|0;R=L+80|0;M=L+60|0;f=L+40|0;N=L+20|0;O=L;vt(r,l,10,a);vt(i,s,10,a);lt(l,D,a);lt(s,S,a);u=0;i=R;r=M;c=f;while(1){A=(((Ve[e+(u<<1)>>1]|0)*21299|0)>>>15)+(We[20980+(u<<1)>>1]|0)|0;Ve[i>>1]=A;Ve[r>>1]=(We[l>>1]|0)-A;Ve[c>>1]=(We[s>>1]|0)-A;u=u+1|0;if((u|0)==10)break;else{l=l+2|0;s=s+2|0;i=i+2|0;r=r+2|0;c=c+2|0}}Ve[o>>1]=St(M,f,21e3,Ve[D>>1]|0,Ve[D+2>>1]|0,Ve[S>>1]|0,Ve[S+2>>1]|0,128)|0;Ve[o+2>>1]=St(M+4|0,f+4|0,22024,Ve[D+4>>1]|0,Ve[D+6>>1]|0,Ve[S+4>>1]|0,Ve[S+6>>1]|0,256)|0;g=M+8|0;y=f+8|0;_=M+10|0;A=f+10|0;i=Ve[g>>1]|0;d=Ve[D+8>>1]<<1;h=Ve[_>>1]|0;w=Ve[D+10>>1]<<1;m=Ve[y>>1]|0;p=Ve[S+8>>1]<<1;v=Ve[A>>1]|0;b=Ve[S+10>>1]<<1;s=2147483647;k=0;c=0;E=24072;r=0;while(1){l=Ve[E>>1]|0;u=(Ze(i-l<<16>>16,d)|0)>>16;u=Ze(u,u)|0;l=(Ze(l+i<<16>>16,d)|0)>>16;l=Ze(l,l)|0;F=Ve[E+2>>1]|0;I=(Ze(h-F<<16>>16,w)|0)>>16;u=(Ze(I,I)|0)+u|0;F=(Ze(F+h<<16>>16,w)|0)>>16;l=(Ze(F,F)|0)+l|0;if((u|0)<(s|0)|(l|0)<(s|0)){I=Ve[E+4>>1]|0;F=(Ze(m-I<<16>>16,p)|0)>>16;F=(Ze(F,F)|0)+u|0;I=(Ze(I+m<<16>>16,p)|0)>>16;I=(Ze(I,I)|0)+l|0;l=Ve[E+6>>1]|0;u=(Ze(v-l<<16>>16,b)|0)>>16;u=F+(Ze(u,u)|0)|0;l=(Ze(l+v<<16>>16,b)|0)>>16;l=I+(Ze(l,l)|0)|0;I=(u|0)<(s|0);u=I?u:s;F=(l|0)<(u|0);u=F?l:u;c=I|F?k:c;r=F?1:I?0:r}else u=s;k=k+1<<16>>16;if(k<<16>>16>=256)break;else{s=u;E=E+8|0}}u=c<<16>>16;l=u<<2;c=l|1;s=24072+(c<<1)|0;i=Ve[24072+(l<<1)>>1]|0;if(!(r<<16>>16)){Ve[g>>1]=i;Ve[_>>1]=Ve[s>>1]|0;Ve[y>>1]=Ve[24072+(c+1<<1)>>1]|0;Ve[A>>1]=Ve[24072+((l|3)<<1)>>1]|0;r=u<<1}else{Ve[g>>1]=0-(i&65535);Ve[_>>1]=0-(We[s>>1]|0);Ve[y>>1]=0-(We[24072+(c+1<<1)>>1]|0);Ve[A>>1]=0-(We[24072+((l|3)<<1)>>1]|0);r=u<<1&65534|1}Ve[o+4>>1]=r;Ve[o+6>>1]=St(M+12|0,f+12|0,26120,Ve[D+12>>1]|0,Ve[D+14>>1]|0,Ve[S+12>>1]|0,Ve[S+14>>1]|0,256)|0;Ve[o+8>>1]=St(M+16|0,f+16|0,28168,Ve[D+16>>1]|0,Ve[D+18>>1]|0,Ve[S+16>>1]|0,Ve[S+18>>1]|0,64)|0;s=0;l=N;u=O;i=R;r=M;while(1){F=We[i>>1]|0;Ve[l>>1]=F+(We[r>>1]|0);I=Ve[f>>1]|0;Ve[u>>1]=F+(I&65535);Ve[e+(s<<1)>>1]=I;s=s+1|0;if((s|0)==10)break;else{l=l+2|0;u=u+2|0;i=i+2|0;r=r+2|0;f=f+2|0}}Ot(N,205,10,a);Ot(O,205,10,a);pt(N,n,10,a);pt(O,t,10,a);Ke=L;return}function St(e,r,i,n,t,o,a,f){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;a=a|0;f=f|0;var s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0;h=Ve[e>>1]|0;k=e+2|0;m=Ve[k>>1]|0;v=Ve[r>>1]|0;E=r+2|0;b=Ve[E>>1]|0;if(f<<16>>16>0){d=n<<16>>16<<1;c=t<<16>>16<<1;u=o<<16>>16<<1;t=a<<16>>16<<1;o=2147483647;s=0;n=0;l=i;while(1){a=(Ze(d,h-(Ve[l>>1]|0)|0)|0)>>16;a=Ze(a,a)|0;if(((a|0)<(o|0)?(w=(Ze(c,m-(Ve[l+2>>1]|0)|0)|0)>>16,w=(Ze(w,w)|0)+a|0,(w|0)<(o|0)):0)?(p=(Ze(u,v-(Ve[l+4>>1]|0)|0)|0)>>16,p=(Ze(p,p)|0)+w|0,(p|0)<(o|0)):0){a=(Ze(t,b-(Ve[l+6>>1]|0)|0)|0)>>16;a=(Ze(a,a)|0)+p|0;g=(a|0)<(o|0);a=g?a:o;n=g?s:n}else a=o;s=s+1<<16>>16;if(s<<16>>16>=f<<16>>16)break;else{o=a;l=l+8|0}}}else n=0;g=n<<16>>16<<2;f=g|1;Ve[e>>1]=Ve[i+(g<<1)>>1]|0;Ve[k>>1]=Ve[i+(f<<1)>>1]|0;Ve[r>>1]=Ve[i+(f+1<<1)>>1]|0;Ve[E>>1]=Ve[i+((g|3)<<1)>>1]|0;return n|0}function Rt(e){e=e|0;var r=0,i=0,n=0;if(!e){n=-1;return n|0}Ge[e>>2]=0;r=Ut(20)|0;if(!r){n=-1;return n|0}i=r;n=i+20|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(n|0));Ge[e>>2]=r;n=0;return n|0}function Mt(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+20|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function Nt(e){e=e|0;var r=0;if(!e)return;r=Ge[e>>2]|0;if(!r)return;zt(r);Ge[e>>2]=0;return}function Ot(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0;if(i<<16>>16<=0)return;t=r<<16>>16;o=r&65535;a=0;while(1){n=Ve[e>>1]|0;if(n<<16>>16<r<<16>>16){Ve[e>>1]=r;n=(r<<16>>16)+t|0}else n=(n&65535)+o|0;a=a+1<<16>>16;if(a<<16>>16>=i<<16>>16)break;else{r=n&65535;e=e+2|0}}return}function Lt(e,r,i,n){e=e|0;r=r|0;i=i|0;n=n|0;var t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0;t=n<<16>>16;n=t>>>2&65535;if(!(n<<16>>16))return;d=t+-1|0;b=e+20|0;w=r+(t+-4<<1)|0;m=r+(t+-3<<1)|0;p=r+(t+-2<<1)|0;v=r+(d<<1)|0;h=r+(t+-11<<1)|0;d=i+(d<<1)|0;while(1){r=Ve[b>>1]|0;a=5;f=b;s=h;l=h+-2|0;u=h+-4|0;c=h+-6|0;o=2048;e=2048;t=2048;i=2048;while(1){o=(Ze(Ve[s>>1]|0,r)|0)+o|0;e=(Ze(Ve[l>>1]|0,r)|0)+e|0;t=(Ze(Ve[u>>1]|0,r)|0)+t|0;r=(Ze(Ve[c>>1]|0,r)|0)+i|0;i=Ve[f+-2>>1]|0;o=o+(Ze(Ve[s+2>>1]|0,i)|0)|0;e=e+(Ze(Ve[l+2>>1]|0,i)|0)|0;t=t+(Ze(Ve[u+2>>1]|0,i)|0)|0;f=f+-4|0;i=r+(Ze(Ve[c+2>>1]|0,i)|0)|0;a=a+-1<<16>>16;r=Ve[f>>1]|0;if(!(a<<16>>16))break;else{s=s+4|0;l=l+4|0;u=u+4|0;c=c+4|0}}s=(Ze(Ve[v>>1]|0,r)|0)+o|0;l=(Ze(Ve[p>>1]|0,r)|0)+e|0;u=(Ze(Ve[m>>1]|0,r)|0)+t|0;c=(Ze(Ve[w>>1]|0,r)|0)+i|0;Ve[d>>1]=s>>>12;Ve[d+-2>>1]=l>>>12;Ve[d+-4>>1]=u>>>12;Ve[d+-6>>1]=c>>>12;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{w=w+-8|0;m=m+-8|0;p=p+-8|0;v=v+-8|0;h=h+-8|0;d=d+-8|0}}return}function Ft(e,r){e=e|0;r=r|0;var i=0;i=e+32768|0;if((e|0)>-1&(i^e|0)<0){Ge[r>>2]=1;i=(e>>>31)+2147483647|0}return i>>>16&65535|0}function It(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0;n=r<<16>>16;if(!(r<<16>>16))return e|0;if(r<<16>>16>0){e=e<<16>>16>>(r<<16>>16>15?15:n)&65535;return e|0}t=0-n|0;r=e<<16>>16;t=(t&65535)<<16>>16>15?15:t<<16>>16;n=r<<t;if((n<<16>>16>>t|0)==(r|0)){t=n&65535;return t|0}Ge[i>>2]=1;t=e<<16>>16>0?32767:-32768;return t|0}function Tt(e,r,i){e=e|0;r=r|0;i=i|0;if(r<<16>>16>15){r=0;return r|0}i=It(e,r,i)|0;if(r<<16>>16>0)return i+((1<<(r<<16>>16)+-1&e<<16>>16|0)!=0&1)<<16>>16|0;else{r=i;return r|0}return 0}function Pt(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0;if((e|0)<1){Ve[r>>1]=0;i=0;return i|0}t=(kt(e)|0)&65534;o=t&65535;t=t<<16>>16;if(o<<16>>16>0){n=e<<t;if((n>>t|0)!=(e|0))n=e>>31^2147483647}else{t=0-t<<16;if((t|0)<2031616)n=e>>(t>>16);else n=0}Ve[r>>1]=o;r=n>>>25&63;r=r>>>0>15?r+-16|0:r;o=Ve[30216+(r<<1)>>1]|0;e=o<<16;n=Ze(o-(We[30216+(r+1<<1)>>1]|0)<<16>>16,n>>>10&32767)|0;if((n|0)==1073741824){Ge[i>>2]=1;t=2147483647}else t=n<<1;n=e-t|0;if(((n^e)&(t^e)|0)>=0){i=n;return i|0}Ge[i>>2]=1;i=(o>>>15&1)+2147483647|0;return i|0}function Ct(e,r,i){e=e|0;r=r|0;i=i|0;e=(e<<16>>16)-(r<<16>>16)|0;if((e+32768|0)>>>0<=65535){i=e;i=i&65535;return i|0}Ge[i>>2]=1;i=(e|0)>32767?32767:-32768;i=i&65535;return i|0}function Bt(e,r,i,n,t,o){e=e|0;r=r|0;i=i|0;n=n|0;t=t|0;o=o|0;var a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0;A=Ke;Ke=Ke+48|0;d=A;s=d;a=t;f=s+20|0;do{Ve[s>>1]=Ve[a>>1]|0;s=s+2|0;a=a+2|0}while((s|0)<(f|0));c=d+18|0;p=e+2|0;v=e+4|0;h=r+20|0;b=e+6|0;k=e+8|0;E=e+10|0;g=e+12|0;y=e+14|0;_=e+16|0;w=e+18|0;m=e+20|0;f=Ve[c>>1]|0;a=5;l=r;u=i;s=d+20|0;while(1){R=Ve[e>>1]|0;S=(Ze(R,Ve[l>>1]|0)|0)+2048|0;R=(Ze(Ve[l+2>>1]|0,R)|0)+2048|0;d=f<<16>>16;S=S-(Ze(d,Ve[p>>1]|0)|0)|0;D=Ve[v>>1]|0;d=R-(Ze(d,D)|0)|0;R=Ve[c+-2>>1]|0;D=S-(Ze(R,D)|0)|0;S=Ve[b>>1]|0;R=d-(Ze(S,R)|0)|0;d=Ve[c+-4>>1]|0;S=D-(Ze(d,S)|0)|0;D=Ve[k>>1]|0;d=R-(Ze(D,d)|0)|0;R=Ve[c+-6>>1]|0;D=S-(Ze(R,D)|0)|0;S=Ve[E>>1]|0;R=d-(Ze(R,S)|0)|0;d=Ve[c+-8>>1]|0;S=D-(Ze(d,S)|0)|0;D=Ve[g>>1]|0;d=R-(Ze(D,d)|0)|0;R=Ve[c+-10>>1]|0;D=S-(Ze(R,D)|0)|0;S=Ve[y>>1]|0;R=d-(Ze(S,R)|0)|0;d=Ve[c+-12>>1]|0;S=D-(Ze(d,S)|0)|0;D=Ve[_>>1]|0;d=R-(Ze(d,D)|0)|0;R=Ve[c+-14>>1]|0;D=S-(Ze(R,D)|0)|0;S=Ve[w>>1]|0;R=d-(Ze(S,R)|0)|0;d=Ve[c+-16>>1]|0;S=D-(Ze(d,S)|0)|0;D=Ve[m>>1]|0;d=R-(Ze(D,d)|0)|0;D=S-(Ze(Ve[c+-18>>1]|0,D)|0)|0;D=(D+134217728|0)>>>0<268435455?D>>>12&65535:(D|0)>134217727?32767:-32768;d=d-(Ze(Ve[p>>1]|0,D<<16>>16)|0)|0;c=s+2|0;Ve[s>>1]=D;Ve[u>>1]=D;f=(d+134217728|0)>>>0<268435455?d>>>12&65535:(d|0)>134217727?32767:-32768;Ve[c>>1]=f;Ve[u+2>>1]=f;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{l=l+4|0;u=u+4|0;s=s+4|0}}n=(n<<16>>16)+-10|0;s=n>>>1&65535;if(s<<16>>16){d=i+18|0;f=r+16|0;c=Ve[d>>1]|0;l=h;a=i+20|0;while(1){D=Ve[e>>1]|0;u=(Ze(D,Ve[l>>1]|0)|0)+2048|0;D=(Ze(Ve[f+6>>1]|0,D)|0)+2048|0;f=Ve[p>>1]|0;S=c<<16>>16;u=u-(Ze(S,f)|0)|0;R=Ve[v>>1]|0;S=D-(Ze(S,R)|0)|0;D=Ve[d+-2>>1]|0;R=u-(Ze(D,R)|0)|0;u=Ve[b>>1]|0;D=S-(Ze(u,D)|0)|0;S=Ve[d+-4>>1]|0;u=R-(Ze(S,u)|0)|0;R=Ve[k>>1]|0;S=D-(Ze(R,S)|0)|0;D=Ve[d+-6>>1]|0;R=u-(Ze(D,R)|0)|0;u=Ve[E>>1]|0;D=S-(Ze(D,u)|0)|0;S=Ve[d+-8>>1]|0;u=R-(Ze(S,u)|0)|0;R=Ve[g>>1]|0;S=D-(Ze(R,S)|0)|0;D=Ve[d+-10>>1]|0;R=u-(Ze(D,R)|0)|0;u=Ve[y>>1]|0;D=S-(Ze(u,D)|0)|0;S=Ve[d+-12>>1]|0;u=R-(Ze(S,u)|0)|0;R=Ve[_>>1]|0;S=D-(Ze(S,R)|0)|0;D=Ve[d+-14>>1]|0;R=u-(Ze(D,R)|0)|0;u=Ve[w>>1]|0;D=S-(Ze(u,D)|0)|0;S=Ve[d+-16>>1]|0;u=R-(Ze(S,u)|0)|0;R=Ve[m>>1]|0;S=D-(Ze(R,S)|0)|0;R=u-(Ze(Ve[d+-18>>1]|0,R)|0)|0;u=l+4|0;R=(R+134217728|0)>>>0<268435455?R>>>12&65535:(R|0)>134217727?32767:-32768;f=S-(Ze(f,R<<16>>16)|0)|0;d=a+2|0;Ve[a>>1]=R;do{if((f+134217728|0)>>>0>=268435455){a=a+4|0;if((f|0)>134217727){Ve[d>>1]=32767;f=32767;break}else{Ve[d>>1]=-32768;f=-32768;break}}else{f=f>>>12&65535;Ve[d>>1]=f;a=a+4|0}}while(0);s=s+-1<<16>>16;if(!(s<<16>>16))break;else{R=l;c=f;l=u;f=R}}}if(!(o<<16>>16)){Ke=A;return}s=t;a=i+(n<<1)|0;f=s+20|0;do{Ve[s>>1]=Ve[a>>1]|0;s=s+2|0;a=a+2|0}while((s|0)<(f|0));Ke=A;return}function xt(e,r,i){e=e|0;r=r|0;i=i|0;Ve[i>>1]=Ve[e>>1]|0;Ve[i+2>>1]=((Ze(Ve[r>>1]|0,Ve[e+2>>1]|0)|0)+16384|0)>>>15;Ve[i+4>>1]=((Ze(Ve[r+2>>1]|0,Ve[e+4>>1]|0)|0)+16384|0)>>>15;Ve[i+6>>1]=((Ze(Ve[r+4>>1]|0,Ve[e+6>>1]|0)|0)+16384|0)>>>15;Ve[i+8>>1]=((Ze(Ve[r+6>>1]|0,Ve[e+8>>1]|0)|0)+16384|0)>>>15;Ve[i+10>>1]=((Ze(Ve[r+8>>1]|0,Ve[e+10>>1]|0)|0)+16384|0)>>>15;Ve[i+12>>1]=((Ze(Ve[r+10>>1]|0,Ve[e+12>>1]|0)|0)+16384|0)>>>15;Ve[i+14>>1]=((Ze(Ve[r+12>>1]|0,Ve[e+14>>1]|0)|0)+16384|0)>>>15;Ve[i+16>>1]=((Ze(Ve[r+14>>1]|0,Ve[e+16>>1]|0)|0)+16384|0)>>>15;Ve[i+18>>1]=((Ze(Ve[r+16>>1]|0,Ve[e+18>>1]|0)|0)+16384|0)>>>15;Ve[i+20>>1]=((Ze(Ve[r+18>>1]|0,Ve[e+20>>1]|0)|0)+16384|0)>>>15;return}function Ut(e){e=e|0;var r=0,i=0,n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0,R=0,M=0,N=0,O=0,L=0,F=0,I=0,T=0,P=0,C=0,B=0,x=0,U=0,z=0,j=0,q=0,H=0,Y=0,X=0,V=0;do{if(e>>>0<245){v=e>>>0<11?16:e+11&-8;e=v>>>3;c=Ge[26]|0;s=c>>>e;if(s&3){n=(s&1^1)+e|0;r=n<<1;i=144+(r<<2)|0;r=144+(r+2<<2)|0;t=Ge[r>>2]|0;o=t+8|0;a=Ge[o>>2]|0;do{if((i|0)==(a|0))Ge[26]=c&~(1<<n);else{if(a>>>0>=(Ge[30]|0)>>>0?(u=a+12|0,(Ge[u>>2]|0)==(t|0)):0){Ge[u>>2]=i;Ge[r>>2]=a;break}re()}}while(0);X=n<<3;Ge[t+4>>2]=X|3;X=t+(X|4)|0;Ge[X>>2]=Ge[X>>2]|1;break}r=Ge[28]|0;if(v>>>0>r>>>0){if(s){t=2<<e;t=s<<e&(t|0-t);t=(t&0-t)+-1|0;o=t>>>12&16;t=t>>>o;n=t>>>5&8;t=t>>>n;i=t>>>2&4;t=t>>>i;a=t>>>1&2;t=t>>>a;f=t>>>1&1;f=(n|o|i|a|f)+(t>>>f)|0;t=f<<1;a=144+(t<<2)|0;t=144+(t+2<<2)|0;i=Ge[t>>2]|0;o=i+8|0;n=Ge[o>>2]|0;do{if((a|0)==(n|0)){Ge[26]=c&~(1<<f);d=r}else{if(n>>>0>=(Ge[30]|0)>>>0?(l=n+12|0,(Ge[l>>2]|0)==(i|0)):0){Ge[l>>2]=a;Ge[t>>2]=n;d=Ge[28]|0;break}re()}}while(0);X=f<<3;r=X-v|0;Ge[i+4>>2]=v|3;s=i+v|0;Ge[i+(v|4)>>2]=r|1;Ge[i+X>>2]=r;if(d){i=Ge[31]|0;n=d>>>3;a=n<<1;f=144+(a<<2)|0;t=Ge[26]|0;n=1<<n;if(t&n){t=144+(a+2<<2)|0;a=Ge[t>>2]|0;if(a>>>0<(Ge[30]|0)>>>0)re();else{w=t;m=a}}else{Ge[26]=t|n;w=144+(a+2<<2)|0;m=f}Ge[w>>2]=i;Ge[m+12>>2]=i;Ge[i+8>>2]=m;Ge[i+12>>2]=f}Ge[28]=r;Ge[31]=s;break}e=Ge[27]|0;if(e){t=(e&0-e)+-1|0;Y=t>>>12&16;t=t>>>Y;H=t>>>5&8;t=t>>>H;X=t>>>2&4;t=t>>>X;a=t>>>1&2;t=t>>>a;s=t>>>1&1;s=Ge[408+((H|Y|X|a|s)+(t>>>s)<<2)>>2]|0;t=(Ge[s+4>>2]&-8)-v|0;a=s;while(1){f=Ge[a+16>>2]|0;if(!f){f=Ge[a+20>>2]|0;if(!f){r=t;break}}a=(Ge[f+4>>2]&-8)-v|0;X=a>>>0<t>>>0;t=X?a:t;a=f;s=X?f:s}e=Ge[30]|0;if(s>>>0>=e>>>0?(E=s+v|0,s>>>0<E>>>0):0){n=Ge[s+24>>2]|0;f=Ge[s+12>>2]|0;do{if((f|0)==(s|0)){a=s+20|0;f=Ge[a>>2]|0;if(!f){a=s+16|0;f=Ge[a>>2]|0;if(!f){b=0;break}}while(1){o=f+20|0;t=Ge[o>>2]|0;if(t){f=t;a=o;continue}o=f+16|0;t=Ge[o>>2]|0;if(!t)break;else{f=t;a=o}}if(a>>>0<e>>>0)re();else{Ge[a>>2]=0;b=f;break}}else{a=Ge[s+8>>2]|0;if((a>>>0>=e>>>0?(i=a+12|0,(Ge[i>>2]|0)==(s|0)):0)?(h=f+8|0,(Ge[h>>2]|0)==(s|0)):0){Ge[i>>2]=f;Ge[h>>2]=a;b=f;break}re()}}while(0);do{if(n){a=Ge[s+28>>2]|0;o=408+(a<<2)|0;if((s|0)==(Ge[o>>2]|0)){Ge[o>>2]=b;if(!b){Ge[27]=Ge[27]&~(1<<a);break}}else{if(n>>>0<(Ge[30]|0)>>>0)re();a=n+16|0;if((Ge[a>>2]|0)==(s|0))Ge[a>>2]=b;else Ge[n+20>>2]=b;if(!b)break}o=Ge[30]|0;if(b>>>0<o>>>0)re();Ge[b+24>>2]=n;a=Ge[s+16>>2]|0;do{if(a)if(a>>>0<o>>>0)re();else{Ge[b+16>>2]=a;Ge[a+24>>2]=b;break}}while(0);a=Ge[s+20>>2]|0;if(a)if(a>>>0<(Ge[30]|0)>>>0)re();else{Ge[b+20>>2]=a;Ge[a+24>>2]=b;break}}}while(0);if(r>>>0<16){X=r+v|0;Ge[s+4>>2]=X|3;X=s+(X+4)|0;Ge[X>>2]=Ge[X>>2]|1}else{Ge[s+4>>2]=v|3;Ge[s+(v|4)>>2]=r|1;Ge[s+(r+v)>>2]=r;n=Ge[28]|0;if(n){i=Ge[31]|0;t=n>>>3;a=t<<1;f=144+(a<<2)|0;o=Ge[26]|0;t=1<<t;if(o&t){a=144+(a+2<<2)|0;o=Ge[a>>2]|0;if(o>>>0<(Ge[30]|0)>>>0)re();else{k=a;g=o}}else{Ge[26]=o|t;k=144+(a+2<<2)|0;g=f}Ge[k>>2]=i;Ge[g+12>>2]=i;Ge[i+8>>2]=g;Ge[i+12>>2]=f}Ge[28]=r;Ge[31]=E}o=s+8|0;break}re()}else V=154}else V=154}else if(e>>>0<=4294967231){e=e+11|0;g=e&-8;c=Ge[27]|0;if(c){s=0-g|0;e=e>>>8;if(e)if(g>>>0>16777215)u=31;else{E=(e+1048320|0)>>>16&8;V=e<<E;k=(V+520192|0)>>>16&4;V=V<<k;u=(V+245760|0)>>>16&2;u=14-(k|E|u)+(V<<u>>>15)|0;u=g>>>(u+7|0)&1|u<<1}else u=0;e=Ge[408+(u<<2)>>2]|0;e:do{if(!e){f=0;e=0;V=86}else{i=s;f=0;r=g<<((u|0)==31?0:25-(u>>>1)|0);l=e;e=0;while(1){n=Ge[l+4>>2]&-8;s=n-g|0;if(s>>>0<i>>>0)if((n|0)==(g|0)){n=l;e=l;V=90;break e}else e=l;else s=i;V=Ge[l+20>>2]|0;l=Ge[l+16+(r>>>31<<2)>>2]|0;f=(V|0)==0|(V|0)==(l|0)?f:V;if(!l){V=86;break}else{i=s;r=r<<1}}}}while(0);if((V|0)==86){if((f|0)==0&(e|0)==0){e=2<<u;e=c&(e|0-e);if(!e){v=g;V=154;break}e=(e&0-e)+-1|0;b=e>>>12&16;e=e>>>b;m=e>>>5&8;e=e>>>m;k=e>>>2&4;e=e>>>k;E=e>>>1&2;e=e>>>E;f=e>>>1&1;f=Ge[408+((m|b|k|E|f)+(e>>>f)<<2)>>2]|0;e=0}if(!f){m=s;w=e}else{n=f;V=90}}if((V|0)==90)while(1){V=0;E=(Ge[n+4>>2]&-8)-g|0;f=E>>>0<s>>>0;s=f?E:s;e=f?n:e;f=Ge[n+16>>2]|0;if(f){n=f;V=90;continue}n=Ge[n+20>>2]|0;if(!n){m=s;w=e;break}else V=90}if((w|0)!=0?m>>>0<((Ge[28]|0)-g|0)>>>0:0){e=Ge[30]|0;if(w>>>0>=e>>>0?(F=w+g|0,w>>>0<F>>>0):0){s=Ge[w+24>>2]|0;f=Ge[w+12>>2]|0;do{if((f|0)==(w|0)){a=w+20|0;f=Ge[a>>2]|0;if(!f){a=w+16|0;f=Ge[a>>2]|0;if(!f){_=0;break}}while(1){o=f+20|0;t=Ge[o>>2]|0;if(t){f=t;a=o;continue}o=f+16|0;t=Ge[o>>2]|0;if(!t)break;else{f=t;a=o}}if(a>>>0<e>>>0)re();else{Ge[a>>2]=0;_=f;break}}else{a=Ge[w+8>>2]|0;if((a>>>0>=e>>>0?(p=a+12|0,(Ge[p>>2]|0)==(w|0)):0)?(v=f+8|0,(Ge[v>>2]|0)==(w|0)):0){Ge[p>>2]=f;Ge[v>>2]=a;_=f;break}re()}}while(0);do{if(s){f=Ge[w+28>>2]|0;a=408+(f<<2)|0;if((w|0)==(Ge[a>>2]|0)){Ge[a>>2]=_;if(!_){Ge[27]=Ge[27]&~(1<<f);break}}else{if(s>>>0<(Ge[30]|0)>>>0)re();a=s+16|0;if((Ge[a>>2]|0)==(w|0))Ge[a>>2]=_;else Ge[s+20>>2]=_;if(!_)break}f=Ge[30]|0;if(_>>>0<f>>>0)re();Ge[_+24>>2]=s;a=Ge[w+16>>2]|0;do{if(a)if(a>>>0<f>>>0)re();else{Ge[_+16>>2]=a;Ge[a+24>>2]=_;break}}while(0);a=Ge[w+20>>2]|0;if(a)if(a>>>0<(Ge[30]|0)>>>0)re();else{Ge[_+20>>2]=a;Ge[a+24>>2]=_;break}}}while(0);e:do{if(m>>>0>=16){Ge[w+4>>2]=g|3;Ge[w+(g|4)>>2]=m|1;Ge[w+(m+g)>>2]=m;f=m>>>3;if(m>>>0<256){o=f<<1;n=144+(o<<2)|0;t=Ge[26]|0;a=1<<f;if(t&a){a=144+(o+2<<2)|0;o=Ge[a>>2]|0;if(o>>>0<(Ge[30]|0)>>>0)re();else{A=a;D=o}}else{Ge[26]=t|a;A=144+(o+2<<2)|0;D=n}Ge[A>>2]=F;Ge[D+12>>2]=F;Ge[w+(g+8)>>2]=D;Ge[w+(g+12)>>2]=n;break}i=m>>>8;if(i)if(m>>>0>16777215)f=31;else{Y=(i+1048320|0)>>>16&8;X=i<<Y;H=(X+520192|0)>>>16&4;X=X<<H;f=(X+245760|0)>>>16&2;f=14-(H|Y|f)+(X<<f>>>15)|0;f=m>>>(f+7|0)&1|f<<1}else f=0;a=408+(f<<2)|0;Ge[w+(g+28)>>2]=f;Ge[w+(g+20)>>2]=0;Ge[w+(g+16)>>2]=0;o=Ge[27]|0;t=1<<f;if(!(o&t)){Ge[27]=o|t;Ge[a>>2]=F;Ge[w+(g+24)>>2]=a;Ge[w+(g+12)>>2]=F;Ge[w+(g+8)>>2]=F;break}i=Ge[a>>2]|0;r:do{if((Ge[i+4>>2]&-8|0)!=(m|0)){f=m<<((f|0)==31?0:25-(f>>>1)|0);while(1){r=i+16+(f>>>31<<2)|0;a=Ge[r>>2]|0;if(!a)break;if((Ge[a+4>>2]&-8|0)==(m|0)){R=a;break r}else{f=f<<1;i=a}}if(r>>>0<(Ge[30]|0)>>>0)re();else{Ge[r>>2]=F;Ge[w+(g+24)>>2]=i;Ge[w+(g+12)>>2]=F;Ge[w+(g+8)>>2]=F;break e}}else R=i}while(0);i=R+8|0;r=Ge[i>>2]|0;X=Ge[30]|0;if(r>>>0>=X>>>0&R>>>0>=X>>>0){Ge[r+12>>2]=F;Ge[i>>2]=F;Ge[w+(g+8)>>2]=r;Ge[w+(g+12)>>2]=R;Ge[w+(g+24)>>2]=0;break}else re()}else{X=m+g|0;Ge[w+4>>2]=X|3;X=w+(X+4)|0;Ge[X>>2]=Ge[X>>2]|1}}while(0);o=w+8|0;break}re()}else{v=g;V=154}}else{v=g;V=154}}else{v=-1;V=154}}while(0);e:do{if((V|0)==154){e=Ge[28]|0;if(e>>>0>=v>>>0){r=e-v|0;i=Ge[31]|0;if(r>>>0>15){Ge[31]=i+v;Ge[28]=r;Ge[i+(v+4)>>2]=r|1;Ge[i+e>>2]=r;Ge[i+4>>2]=v|3}else{Ge[28]=0;Ge[31]=0;Ge[i+4>>2]=e|3;V=i+(e+4)|0;Ge[V>>2]=Ge[V>>2]|1}o=i+8|0;break}e=Ge[29]|0;if(e>>>0>v>>>0){V=e-v|0;Ge[29]=V;o=Ge[32]|0;Ge[32]=o+v;Ge[o+(v+4)>>2]=V|1;Ge[o+4>>2]=v|3;o=o+8|0;break}if(!(Ge[144]|0))qt();c=v+48|0;i=Ge[146]|0;u=v+47|0;n=i+u|0;i=0-i|0;l=n&i;if(l>>>0>v>>>0){e=Ge[136]|0;if((e|0)!=0?(R=Ge[134]|0,F=R+l|0,F>>>0<=R>>>0|F>>>0>e>>>0):0){o=0;break}r:do{if(!(Ge[137]&4)){e=Ge[32]|0;i:do{if(e){f=552;while(1){s=Ge[f>>2]|0;if(s>>>0<=e>>>0?(y=f+4|0,(s+(Ge[y>>2]|0)|0)>>>0>e>>>0):0){o=f;e=y;break}f=Ge[f+8>>2]|0;if(!f){V=172;break i}}s=n-(Ge[29]|0)&i;if(s>>>0<2147483647){f=ne(s|0)|0;F=(f|0)==((Ge[o>>2]|0)+(Ge[e>>2]|0)|0);e=F?s:0;if(F){if((f|0)!=(-1|0)){D=f;b=e;V=192;break r}}else V=182}else e=0}else V=172}while(0);do{if((V|0)==172){o=ne(0)|0;if((o|0)!=(-1|0)){e=o;s=Ge[145]|0;f=s+-1|0;if(!(f&e))s=l;else s=l-e+(f+e&0-s)|0;e=Ge[134]|0;f=e+s|0;if(s>>>0>v>>>0&s>>>0<2147483647){F=Ge[136]|0;if((F|0)!=0?f>>>0<=e>>>0|f>>>0>F>>>0:0){e=0;break}f=ne(s|0)|0;V=(f|0)==(o|0);e=V?s:0;if(V){D=o;b=e;V=192;break r}else V=182}else e=0}else e=0}}while(0);i:do{if((V|0)==182){o=0-s|0;do{if(c>>>0>s>>>0&(s>>>0<2147483647&(f|0)!=(-1|0))?(S=Ge[146]|0,S=u-s+S&0-S,S>>>0<2147483647):0)if((ne(S|0)|0)==(-1|0)){ne(o|0)|0;break i}else{s=S+s|0;break}}while(0);if((f|0)!=(-1|0)){D=f;b=s;V=192;break r}}}while(0);Ge[137]=Ge[137]|4;V=189}else{e=0;V=189}}while(0);if((((V|0)==189?l>>>0<2147483647:0)?(M=ne(l|0)|0,N=ne(0)|0,M>>>0<N>>>0&((M|0)!=(-1|0)&(N|0)!=(-1|0))):0)?(O=N-M|0,L=O>>>0>(v+40|0)>>>0,L):0){D=M;b=L?O:e;V=192}if((V|0)==192){s=(Ge[134]|0)+b|0;Ge[134]=s;if(s>>>0>(Ge[135]|0)>>>0)Ge[135]=s;m=Ge[32]|0;r:do{if(m){o=552;do{e=Ge[o>>2]|0;s=o+4|0;f=Ge[s>>2]|0;if((D|0)==(e+f|0)){I=e;T=s;P=f;C=o;V=202;break}o=Ge[o+8>>2]|0}while((o|0)!=0);if(((V|0)==202?(Ge[C+12>>2]&8|0)==0:0)?m>>>0<D>>>0&m>>>0>=I>>>0:0){Ge[T>>2]=P+b;V=(Ge[29]|0)+b|0;X=m+8|0;X=(X&7|0)==0?0:0-X&7;Y=V-X|0;Ge[32]=m+X;Ge[29]=Y;Ge[m+(X+4)>>2]=Y|1;Ge[m+(V+4)>>2]=40;Ge[33]=Ge[148];break}s=Ge[30]|0;if(D>>>0<s>>>0){Ge[30]=D;s=D}f=D+b|0;e=552;while(1){if((Ge[e>>2]|0)==(f|0)){o=e;f=e;V=210;break}e=Ge[e+8>>2]|0;if(!e){f=552;break}}if((V|0)==210)if(!(Ge[f+12>>2]&8)){Ge[o>>2]=D;h=f+4|0;Ge[h>>2]=(Ge[h>>2]|0)+b;h=D+8|0;h=(h&7|0)==0?0:0-h&7;u=D+(b+8)|0;u=(u&7|0)==0?0:0-u&7;f=D+(u+b)|0;w=h+v|0;d=D+w|0;e=f-(D+h)-v|0;Ge[D+(h+4)>>2]=v|3;i:do{if((f|0)!=(m|0)){if((f|0)==(Ge[31]|0)){V=(Ge[28]|0)+e|0;Ge[28]=V;Ge[31]=d;Ge[D+(w+4)>>2]=V|1;Ge[D+(V+w)>>2]=V;break}r=b+4|0;a=Ge[D+(r+u)>>2]|0;if((a&3|0)==1){l=a&-8;n=a>>>3;n:do{if(a>>>0>=256){i=Ge[D+((u|24)+b)>>2]|0;o=Ge[D+(b+12+u)>>2]|0;t:do{if((o|0)==(f|0)){t=u|16;o=D+(r+t)|0;a=Ge[o>>2]|0;if(!a){o=D+(t+b)|0;a=Ge[o>>2]|0;if(!a){q=0;break}}while(1){t=a+20|0;n=Ge[t>>2]|0;if(n){a=n;o=t;continue}t=a+16|0;n=Ge[t>>2]|0;if(!n)break;else{a=n;o=t}}if(o>>>0<s>>>0)re();else{Ge[o>>2]=0;q=a;break}}else{t=Ge[D+((u|8)+b)>>2]|0;do{if(t>>>0>=s>>>0){s=t+12|0;if((Ge[s>>2]|0)!=(f|0))break;a=o+8|0;if((Ge[a>>2]|0)!=(f|0))break;Ge[s>>2]=o;Ge[a>>2]=t;q=o;break t}}while(0);re()}}while(0);if(!i)break;s=Ge[D+(b+28+u)>>2]|0;a=408+(s<<2)|0;do{if((f|0)!=(Ge[a>>2]|0)){if(i>>>0<(Ge[30]|0)>>>0)re();a=i+16|0;if((Ge[a>>2]|0)==(f|0))Ge[a>>2]=q;else Ge[i+20>>2]=q;if(!q)break n}else{Ge[a>>2]=q;if(q)break;Ge[27]=Ge[27]&~(1<<s);break n}}while(0);s=Ge[30]|0;if(q>>>0<s>>>0)re();Ge[q+24>>2]=i;f=u|16;a=Ge[D+(f+b)>>2]|0;do{if(a)if(a>>>0<s>>>0)re();else{Ge[q+16>>2]=a;Ge[a+24>>2]=q;break}}while(0);f=Ge[D+(r+f)>>2]|0;if(!f)break;if(f>>>0<(Ge[30]|0)>>>0)re();else{Ge[q+20>>2]=f;Ge[f+24>>2]=q;break}}else{a=Ge[D+((u|8)+b)>>2]|0;o=Ge[D+(b+12+u)>>2]|0;t=144+(n<<1<<2)|0;do{if((a|0)!=(t|0)){if(a>>>0>=s>>>0?(Ge[a+12>>2]|0)==(f|0):0)break;re()}}while(0);if((o|0)==(a|0)){Ge[26]=Ge[26]&~(1<<n);break}do{if((o|0)==(t|0))B=o+8|0;else{if(o>>>0>=s>>>0?(x=o+8|0,(Ge[x>>2]|0)==(f|0)):0){B=x;break}re()}}while(0);Ge[a+12>>2]=o;Ge[B>>2]=a}}while(0);f=D+((l|u)+b)|0;e=l+e|0}f=f+4|0;Ge[f>>2]=Ge[f>>2]&-2;Ge[D+(w+4)>>2]=e|1;Ge[D+(e+w)>>2]=e;f=e>>>3;if(e>>>0<256){o=f<<1;n=144+(o<<2)|0;t=Ge[26]|0;a=1<<f;do{if(!(t&a)){Ge[26]=t|a;H=144+(o+2<<2)|0;Y=n}else{a=144+(o+2<<2)|0;o=Ge[a>>2]|0;if(o>>>0>=(Ge[30]|0)>>>0){H=a;Y=o;break}re()}}while(0);Ge[H>>2]=d;Ge[Y+12>>2]=d;Ge[D+(w+8)>>2]=Y;Ge[D+(w+12)>>2]=n;break}i=e>>>8;do{if(!i)f=0;else{if(e>>>0>16777215){f=31;break}Y=(i+1048320|0)>>>16&8;V=i<<Y;H=(V+520192|0)>>>16&4;V=V<<H;f=(V+245760|0)>>>16&2;f=14-(H|Y|f)+(V<<f>>>15)|0;f=e>>>(f+7|0)&1|f<<1}}while(0);a=408+(f<<2)|0;Ge[D+(w+28)>>2]=f;Ge[D+(w+20)>>2]=0;Ge[D+(w+16)>>2]=0;o=Ge[27]|0;t=1<<f;if(!(o&t)){Ge[27]=o|t;Ge[a>>2]=d;Ge[D+(w+24)>>2]=a;Ge[D+(w+12)>>2]=d;Ge[D+(w+8)>>2]=d;break}i=Ge[a>>2]|0;n:do{if((Ge[i+4>>2]&-8|0)!=(e|0)){f=e<<((f|0)==31?0:25-(f>>>1)|0);while(1){r=i+16+(f>>>31<<2)|0;a=Ge[r>>2]|0;if(!a)break;if((Ge[a+4>>2]&-8|0)==(e|0)){X=a;break n}else{f=f<<1;i=a}}if(r>>>0<(Ge[30]|0)>>>0)re();else{Ge[r>>2]=d;Ge[D+(w+24)>>2]=i;Ge[D+(w+12)>>2]=d;Ge[D+(w+8)>>2]=d;break i}}else X=i}while(0);i=X+8|0;r=Ge[i>>2]|0;V=Ge[30]|0;if(r>>>0>=V>>>0&X>>>0>=V>>>0){Ge[r+12>>2]=d;Ge[i>>2]=d;Ge[D+(w+8)>>2]=r;Ge[D+(w+12)>>2]=X;Ge[D+(w+24)>>2]=0;break}else re()}else{V=(Ge[29]|0)+e|0;Ge[29]=V;Ge[32]=d;Ge[D+(w+4)>>2]=V|1}}while(0);o=D+(h|8)|0;break e}else f=552;while(1){o=Ge[f>>2]|0;if(o>>>0<=m>>>0?(a=Ge[f+4>>2]|0,t=o+a|0,t>>>0>m>>>0):0)break;f=Ge[f+8>>2]|0}f=o+(a+-39)|0;f=o+(a+-47+((f&7|0)==0?0:0-f&7))|0;s=m+16|0;f=f>>>0<s>>>0?m:f;a=f+8|0;o=D+8|0;o=(o&7|0)==0?0:0-o&7;V=b+-40-o|0;Ge[32]=D+o;Ge[29]=V;Ge[D+(o+4)>>2]=V|1;Ge[D+(b+-36)>>2]=40;Ge[33]=Ge[148];o=f+4|0;Ge[o>>2]=27;Ge[a>>2]=Ge[138];Ge[a+4>>2]=Ge[139];Ge[a+8>>2]=Ge[140];Ge[a+12>>2]=Ge[141];Ge[138]=D;Ge[139]=b;Ge[141]=0;Ge[140]=a;a=f+28|0;Ge[a>>2]=7;if((f+32|0)>>>0<t>>>0)do{V=a;a=a+4|0;Ge[a>>2]=7}while((V+8|0)>>>0<t>>>0);if((f|0)!=(m|0)){e=f-m|0;Ge[o>>2]=Ge[o>>2]&-2;Ge[m+4>>2]=e|1;Ge[f>>2]=e;t=e>>>3;if(e>>>0<256){a=t<<1;f=144+(a<<2)|0;o=Ge[26]|0;n=1<<t;if(o&n){i=144+(a+2<<2)|0;r=Ge[i>>2]|0;if(r>>>0<(Ge[30]|0)>>>0)re();else{U=i;z=r}}else{Ge[26]=o|n;U=144+(a+2<<2)|0;z=f}Ge[U>>2]=m;Ge[z+12>>2]=m;Ge[m+8>>2]=z;Ge[m+12>>2]=f;break}i=e>>>8;if(i)if(e>>>0>16777215)a=31;else{X=(i+1048320|0)>>>16&8;V=i<<X;Y=(V+520192|0)>>>16&4;V=V<<Y;a=(V+245760|0)>>>16&2;a=14-(Y|X|a)+(V<<a>>>15)|0;a=e>>>(a+7|0)&1|a<<1}else a=0;n=408+(a<<2)|0;Ge[m+28>>2]=a;Ge[m+20>>2]=0;Ge[s>>2]=0;i=Ge[27]|0;r=1<<a;if(!(i&r)){Ge[27]=i|r;Ge[n>>2]=m;Ge[m+24>>2]=n;Ge[m+12>>2]=m;Ge[m+8>>2]=m;break}i=Ge[n>>2]|0;i:do{if((Ge[i+4>>2]&-8|0)!=(e|0)){a=e<<((a|0)==31?0:25-(a>>>1)|0);while(1){r=i+16+(a>>>31<<2)|0;n=Ge[r>>2]|0;if(!n)break;if((Ge[n+4>>2]&-8|0)==(e|0)){j=n;break i}else{a=a<<1;i=n}}if(r>>>0<(Ge[30]|0)>>>0)re();else{Ge[r>>2]=m;Ge[m+24>>2]=i;Ge[m+12>>2]=m;Ge[m+8>>2]=m;break r}}else j=i}while(0);i=j+8|0;r=Ge[i>>2]|0;V=Ge[30]|0;if(r>>>0>=V>>>0&j>>>0>=V>>>0){Ge[r+12>>2]=m;Ge[i>>2]=m;Ge[m+8>>2]=r;Ge[m+12>>2]=j;Ge[m+24>>2]=0;break}else re()}}else{V=Ge[30]|0;if((V|0)==0|D>>>0<V>>>0)Ge[30]=D;Ge[138]=D;Ge[139]=b;Ge[141]=0;Ge[35]=Ge[144];Ge[34]=-1;i=0;do{V=i<<1;X=144+(V<<2)|0;Ge[144+(V+3<<2)>>2]=X;Ge[144+(V+2<<2)>>2]=X;i=i+1|0}while((i|0)!=32);V=D+8|0;V=(V&7|0)==0?0:0-V&7;X=b+-40-V|0;Ge[32]=D+V;Ge[29]=X;Ge[D+(V+4)>>2]=X|1;Ge[D+(b+-36)>>2]=40;Ge[33]=Ge[148]}}while(0);r=Ge[29]|0;if(r>>>0>v>>>0){V=r-v|0;Ge[29]=V;o=Ge[32]|0;Ge[32]=o+v;Ge[o+(v+4)>>2]=V|1;Ge[o+4>>2]=v|3;o=o+8|0;break}}Ge[(jt()|0)>>2]=12;o=0}else o=0}}while(0);return o|0}function zt(e){e=e|0;var r=0,i=0,n=0,t=0,o=0,a=0,f=0,s=0,l=0,u=0,c=0,d=0,h=0,w=0,m=0,p=0,v=0,b=0,k=0,E=0,g=0,y=0,_=0,A=0,D=0,S=0;e:do{if(e){t=e+-8|0;l=Ge[30]|0;r:do{if(t>>>0>=l>>>0?(n=Ge[e+-4>>2]|0,i=n&3,(i|0)!=1):0){E=n&-8;g=e+(E+-8)|0;do{if(!(n&1)){t=Ge[t>>2]|0;if(!i)break e;u=-8-t|0;d=e+u|0;h=t+E|0;if(d>>>0<l>>>0)break r;if((d|0)==(Ge[31]|0)){o=e+(E+-4)|0;t=Ge[o>>2]|0;if((t&3|0)!=3){S=d;o=h;break}Ge[28]=h;Ge[o>>2]=t&-2;Ge[e+(u+4)>>2]=h|1;Ge[g>>2]=h;break e}i=t>>>3;if(t>>>0<256){n=Ge[e+(u+8)>>2]|0;o=Ge[e+(u+12)>>2]|0;t=144+(i<<1<<2)|0;do{if((n|0)!=(t|0)){if(n>>>0>=l>>>0?(Ge[n+12>>2]|0)==(d|0):0)break;re()}}while(0);if((o|0)==(n|0)){Ge[26]=Ge[26]&~(1<<i);S=d;o=h;break}do{if((o|0)==(t|0))r=o+8|0;else{if(o>>>0>=l>>>0?(a=o+8|0,(Ge[a>>2]|0)==(d|0)):0){r=a;break}re()}}while(0);Ge[n+12>>2]=o;Ge[r>>2]=n;S=d;o=h;break}a=Ge[e+(u+24)>>2]|0;t=Ge[e+(u+12)>>2]|0;do{if((t|0)==(d|0)){n=e+(u+20)|0;t=Ge[n>>2]|0;if(!t){n=e+(u+16)|0;t=Ge[n>>2]|0;if(!t){c=0;break}}while(1){i=t+20|0;r=Ge[i>>2]|0;if(r){t=r;n=i;continue}i=t+16|0;r=Ge[i>>2]|0;if(!r)break;else{t=r;n=i}}if(n>>>0<l>>>0)re();else{Ge[n>>2]=0;c=t;break}}else{n=Ge[e+(u+8)>>2]|0;if((n>>>0>=l>>>0?(f=n+12|0,(Ge[f>>2]|0)==(d|0)):0)?(s=t+8|0,(Ge[s>>2]|0)==(d|0)):0){Ge[f>>2]=t;Ge[s>>2]=n;c=t;break}re()}}while(0);if(a){t=Ge[e+(u+28)>>2]|0;n=408+(t<<2)|0;if((d|0)==(Ge[n>>2]|0)){Ge[n>>2]=c;if(!c){Ge[27]=Ge[27]&~(1<<t);S=d;o=h;break}}else{if(a>>>0<(Ge[30]|0)>>>0)re();t=a+16|0;if((Ge[t>>2]|0)==(d|0))Ge[t>>2]=c;else Ge[a+20>>2]=c;if(!c){S=d;o=h;break}}n=Ge[30]|0;if(c>>>0<n>>>0)re();Ge[c+24>>2]=a;t=Ge[e+(u+16)>>2]|0;do{if(t)if(t>>>0<n>>>0)re();else{Ge[c+16>>2]=t;Ge[t+24>>2]=c;break}}while(0);t=Ge[e+(u+20)>>2]|0;if(t)if(t>>>0<(Ge[30]|0)>>>0)re();else{Ge[c+20>>2]=t;Ge[t+24>>2]=c;S=d;o=h;break}else{S=d;o=h}}else{S=d;o=h}}else{S=t;o=E}}while(0);if(S>>>0<g>>>0?(w=e+(E+-4)|0,m=Ge[w>>2]|0,(m&1|0)!=0):0){if(!(m&2)){if((g|0)==(Ge[32]|0)){D=(Ge[29]|0)+o|0;Ge[29]=D;Ge[32]=S;Ge[S+4>>2]=D|1;if((S|0)!=(Ge[31]|0))break e;Ge[31]=0;Ge[28]=0;break e}if((g|0)==(Ge[31]|0)){D=(Ge[28]|0)+o|0;Ge[28]=D;Ge[31]=S;Ge[S+4>>2]=D|1;Ge[S+D>>2]=D;break e}s=(m&-8)+o|0;i=m>>>3;do{if(m>>>0>=256){r=Ge[e+(E+16)>>2]|0;o=Ge[e+(E|4)>>2]|0;do{if((o|0)==(g|0)){t=e+(E+12)|0;o=Ge[t>>2]|0;if(!o){t=e+(E+8)|0;o=Ge[t>>2]|0;if(!o){y=0;break}}while(1){n=o+20|0;i=Ge[n>>2]|0;if(i){o=i;t=n;continue}n=o+16|0;i=Ge[n>>2]|0;if(!i)break;else{o=i;t=n}}if(t>>>0<(Ge[30]|0)>>>0)re();else{Ge[t>>2]=0;y=o;break}}else{t=Ge[e+E>>2]|0;if((t>>>0>=(Ge[30]|0)>>>0?(b=t+12|0,(Ge[b>>2]|0)==(g|0)):0)?(k=o+8|0,(Ge[k>>2]|0)==(g|0)):0){Ge[b>>2]=o;Ge[k>>2]=t;y=o;break}re()}}while(0);if(r){o=Ge[e+(E+20)>>2]|0;t=408+(o<<2)|0;if((g|0)==(Ge[t>>2]|0)){Ge[t>>2]=y;if(!y){Ge[27]=Ge[27]&~(1<<o);break}}else{if(r>>>0<(Ge[30]|0)>>>0)re();o=r+16|0;if((Ge[o>>2]|0)==(g|0))Ge[o>>2]=y;else Ge[r+20>>2]=y;if(!y)break}o=Ge[30]|0;if(y>>>0<o>>>0)re();Ge[y+24>>2]=r;t=Ge[e+(E+8)>>2]|0;do{if(t)if(t>>>0<o>>>0)re();else{Ge[y+16>>2]=t;Ge[t+24>>2]=y;break}}while(0);i=Ge[e+(E+12)>>2]|0;if(i)if(i>>>0<(Ge[30]|0)>>>0)re();else{Ge[y+20>>2]=i;Ge[i+24>>2]=y;break}}}else{n=Ge[e+E>>2]|0;o=Ge[e+(E|4)>>2]|0;t=144+(i<<1<<2)|0;do{if((n|0)!=(t|0)){if(n>>>0>=(Ge[30]|0)>>>0?(Ge[n+12>>2]|0)==(g|0):0)break;re()}}while(0);if((o|0)==(n|0)){Ge[26]=Ge[26]&~(1<<i);break}do{if((o|0)==(t|0))p=o+8|0;else{if(o>>>0>=(Ge[30]|0)>>>0?(v=o+8|0,(Ge[v>>2]|0)==(g|0)):0){p=v;break}re()}}while(0);Ge[n+12>>2]=o;Ge[p>>2]=n}}while(0);Ge[S+4>>2]=s|1;Ge[S+s>>2]=s;if((S|0)==(Ge[31]|0)){Ge[28]=s;break e}else o=s}else{Ge[w>>2]=m&-2;Ge[S+4>>2]=o|1;Ge[S+o>>2]=o}t=o>>>3;if(o>>>0<256){n=t<<1;o=144+(n<<2)|0;r=Ge[26]|0;i=1<<t;if(r&i){i=144+(n+2<<2)|0;r=Ge[i>>2]|0;if(r>>>0<(Ge[30]|0)>>>0)re();else{_=i;A=r}}else{Ge[26]=r|i;_=144+(n+2<<2)|0;A=o}Ge[_>>2]=S;Ge[A+12>>2]=S;Ge[S+8>>2]=A;Ge[S+12>>2]=o;break e}r=o>>>8;if(r)if(o>>>0>16777215)t=31;else{_=(r+1048320|0)>>>16&8;A=r<<_;e=(A+520192|0)>>>16&4;A=A<<e;t=(A+245760|0)>>>16&2;t=14-(e|_|t)+(A<<t>>>15)|0;t=o>>>(t+7|0)&1|t<<1}else t=0;i=408+(t<<2)|0;Ge[S+28>>2]=t;Ge[S+20>>2]=0;Ge[S+16>>2]=0;r=Ge[27]|0;n=1<<t;i:do{if(r&n){i=Ge[i>>2]|0;n:do{if((Ge[i+4>>2]&-8|0)!=(o|0)){t=o<<((t|0)==31?0:25-(t>>>1)|0);while(1){r=i+16+(t>>>31<<2)|0;n=Ge[r>>2]|0;if(!n)break;if((Ge[n+4>>2]&-8|0)==(o|0)){D=n;break n}else{t=t<<1;i=n}}if(r>>>0<(Ge[30]|0)>>>0)re();else{Ge[r>>2]=S;Ge[S+24>>2]=i;Ge[S+12>>2]=S;Ge[S+8>>2]=S;break i}}else D=i}while(0);r=D+8|0;i=Ge[r>>2]|0;A=Ge[30]|0;if(i>>>0>=A>>>0&D>>>0>=A>>>0){Ge[i+12>>2]=S;Ge[r>>2]=S;Ge[S+8>>2]=i;Ge[S+12>>2]=D;Ge[S+24>>2]=0;break}else re()}else{Ge[27]=r|n;Ge[i>>2]=S;Ge[S+24>>2]=i;Ge[S+12>>2]=S;Ge[S+8>>2]=S}}while(0);S=(Ge[34]|0)+-1|0;Ge[34]=S;if(!S)r=560;else break e;while(1){r=Ge[r>>2]|0;if(!r)break;else r=r+8|0}Ge[34]=-1;break e}}}while(0);re()}}while(0);return}function jt(){var e=0;if(!0)e=600;else e=Ge[(ee()|0)+60>>2]|0;return e|0}function qt(){var e=0;do{if(!(Ge[144]|0)){e=$(30)|0;if(!(e+-1&e)){Ge[146]=e;Ge[145]=e;Ge[147]=-1;Ge[148]=-1;Ge[149]=0;Ge[137]=0;Ge[144]=(te(0)|0)&-16^1431655768;break}else re()}}while(0);return}function Ht(){}function Yt(e,r,i){e=e|0;r=r|0;i=i|0;var n=0;if((i|0)>=4096)return ae(e|0,r|0,i|0)|0;n=e|0;if((e&3)==(r&3)){while(e&3){if(!i)return n|0;Xe[e>>0]=Xe[r>>0]|0;e=e+1|0;r=r+1|0;i=i-1|0}while((i|0)>=4){Ge[e>>2]=Ge[r>>2];e=e+4|0;r=r+4|0;i=i-4|0}}while((i|0)>0){Xe[e>>0]=Xe[r>>0]|0;e=e+1|0;r=r+1|0;i=i-1|0}return n|0}function Xt(e,r,i){e=e|0;r=r|0;i=i|0;var n=0;if((r|0)<(e|0)&(e|0)<(r+i|0)){n=e;r=r+i|0;e=e+i|0;while((i|0)>0){e=e-1|0;r=r-1|0;i=i-1|0;Xe[e>>0]=Xe[r>>0]|0}e=n}else Yt(e,r,i)|0;return e|0}function Vt(e,r,i){e=e|0;r=r|0;i=i|0;var n=0,t=0,o=0,a=0;n=e+i|0;if((i|0)>=20){r=r&255;o=e&3;a=r|r<<8|r<<16|r<<24;t=n&~3;if(o){o=e+4-o|0;while((e|0)<(o|0)){Xe[e>>0]=r;e=e+1|0}}while((e|0)<(t|0)){Ge[e>>2]=a;e=e+4|0}}while((e|0)<(n|0)){Xe[e>>0]=r;e=e+1|0}return e-i|0}return{_free:zt,___errno_location:jt,_memmove:Xt,_Decoder_Interface_Decode:Ee,_Decoder_Interface_exit:ke,_Encoder_Interface_init:ge,_memset:Vt,_malloc:Ut,_memcpy:Yt,_Encoder_Interface_exit:ye,_Decoder_Interface_init:be,_Encoder_Interface_Encode:_e,runPostSets:Ht,stackAlloc:le,stackSave:ue,stackRestore:ce,establishStackSpace:de,setThrew:he,setTempRet0:pe,getTempRet0:ve}}(p.asmGlobalArg,p.asmLibraryArg,U),Re=(p._Encoder_Interface_Encode=Se._Encoder_Interface_Encode,p._free=Se._free),Me=(p.runPostSets=Se.runPostSets,p._memmove=Se._memmove),Ne=(p._Decoder_Interface_exit=Se._Decoder_Interface_exit,p._Encoder_Interface_init=Se._Encoder_Interface_init,p._memset=Se._memset),Oe=p._malloc=Se._malloc,Le=p._memcpy=Se._memcpy;p._Decoder_Interface_Decode=Se._Decoder_Interface_Decode,p._Decoder_Interface_init=Se._Decoder_Interface_init,p._Encoder_Interface_exit=Se._Encoder_Interface_exit,p.___errno_location=Se.___errno_location;function Fe(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}v.stackAlloc=Se.stackAlloc,v.stackSave=Se.stackSave,v.stackRestore=Se.stackRestore,v.establishStackSpace=Se.establishStackSpace,v.setTempRet0=Se.setTempRet0,v.getTempRet0=Se.getTempRet0,(Fe.prototype=new Error).constructor=Fe;var Ie=null;function Te(e){function r(){p.calledRun||(p.calledRun=!0,s||(Z(),H(V),p.onRuntimeInitialized&&p.onRuntimeInitialized(),p._main&&xe&&p.callMain(e),function(){if(p.postRun)for("function"==typeof p.postRun&&(p.postRun=[p.postRun]);p.postRun.length;)J(p.postRun.shift());H(W)}()))}e=e||p.arguments,null===Ie&&(Ie=Date.now()),0<ae||(!function(){if(p.preRun)for("function"==typeof p.preRun&&(p.preRun=[p.preRun]);p.preRun.length;)Q(p.preRun.shift());H(Y)}(),0<ae||p.calledRun||(p.setStatus?(p.setStatus("Running..."),setTimeout(function(){setTimeout(function(){p.setStatus("")},1),r()},1)):r()))}function Pe(e,r){if(!r||!p.noExitRuntime)throw p.noExitRuntime||(s=!0,e,B=De,H(G),!0,p.onExit&&p.onExit(e)),t&&"function"==typeof quit&&quit(e),new Fe(e)}se=function e(){p.calledRun||Te(),p.calledRun||(se=e)},p.callMain=p.callMain=function(e){w(0==ae,"cannot call main when async dependencies remain! (listen on __ATMAIN__)"),w(0==Y.length,"cannot call main when preRun functions remain to be called"),e=e||[],Z();var r=e.length+1;function i(){for(var e=0;e<3;e++)n.push(0)}var n=[c($(p.thisProgram),"i8",0)];i();for(var t=0;t<r-1;t+=1)n.push(c($(e[t]),"i8",0)),i();n.push(0),n=c(n,"i32",0),De=v.stackSave();try{Pe(p._main(r,n,0),!0)}catch(e){if(e instanceof Fe)return;if("SimulateInfiniteLoop"==e)return p.noExitRuntime=!0,void v.stackRestore(De);throw e&&"object"==typeof e&&e.stack&&p.printErr("exception thrown: "+[e,e.stack]),e}finally{!0}},p.run=p.run=Te,p.exit=p.exit=Pe;var Ce=[];function Be(r){void 0!==r?(p.print(r),p.printErr(r),r=JSON.stringify(r)):r="",s=!0,1;var i="abort("+r+") at "+_()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";throw Ce&&Ce.forEach(function(e){i=e(i,r)}),i}if(p.abort=p.abort=Be,p.preInit)for("function"==typeof p.preInit&&(p.preInit=[p.preInit]);0<p.preInit.length;)p.preInit.pop()();var xe=!0;p.noInitialRun&&(xe=!1),p.noExitRuntime=!0,Te(),Recorder.AMR=e}(),function(){"use strict";Recorder.prototype.enc_wav={stable:!0,testmsg:"支持位数8位、16位（填在比特率里面），采样率取值无限制"},Recorder.prototype.wav=function(e,r,i){var n=this.set,t=e.length,o=n.sampleRate,a=8==n.bitRate?8:16,f=t*(a/8),s=new ArrayBuffer(44+f),l=new DataView(s),u=0,c=function(e){for(var r=0;r<e.length;r++,u++)l.setUint8(u,e.charCodeAt(r))},d=function(e){l.setUint16(u,e,!0),u+=2},h=function(e){l.setUint32(u,e,!0),u+=4};if(c("RIFF"),h(36+f),c("WAVE"),c("fmt "),h(16),d(1),d(1),h(o),h(o*(a/8)),d(a/8),d(a),c("data"),h(f),8==a)for(var w=0;w<t;w++,u++){var m=128+(e[w]>>8);l.setInt8(u,m,!0)}else for(w=0;w<t;w++,u+=2)l.setInt16(u,e[w],!0);r(new Blob([l.buffer],{type:"audio/wav"}))}}();