/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/dtmf.encode.js
*/
!function(){"use strict";Recorder.DTMF_Encode=function(t,e,r,n){for(var a=Math.floor(e*(r||100)/1e3),i=Math.floor(e*(null==n?50:n)/1e3),s=new Int16Array(a+2*i),o=new Int16Array(a+2*i),u=f[t][0],c=f[t][1],h=0;h<a;h++){var d=.3*Math.sin(2*Math.PI*u*(h/e)),k=.3*Math.sin(2*Math.PI*c*(h/e));s[h+i]=32767*Math.max(-1,Math.min(1,d)),o[h+i]=32767*Math.max(-1,Math.min(1,k))}return l(s,0,o,0),s},Recorder.DTMF_EncodeMix=function(t){return new e(t)};var e=function(t){var e=this;for(var r in e.set={duration:100,mute:25,interval:200},t)e.set[r]=t[r];e.keys="",e.idx=0,e.state={keyIdx:-1,skip:0}};e.prototype={add:function(t){this.keys+=t},mix:function(t,e,r){r||(r=0);var n=this,a=n.set,i=[],s=n.state,o=0;t:for(var u=r;u<t.length;u++){var c=t[u],h=n.keys.charAt(n.idx);if(h)for(;h;){if(s.skip){var d=c.length-o;if(d<=s.skip){s.skip-=d,o=0;continue t}o+=s.skip,s.skip=0}var k=s.keyPcm;s.keyIdx==n.idx&&s.cur>=k.length&&(s.keyIdx=-1),s.keyIdx!=n.idx&&(k=Recorder.DTMF_Encode(h,e,a.duration,a.mute),s.keyIdx=n.idx,s.cur=0,s.keyPcm=k,i.push({key:h,data:k}));var f=l(c,o,k,s.cur,!0);if(s.cur=f.cur,o=f.last,f.cur>=k.length&&(n.idx++,h=n.keys.charAt(n.idx),s.skip=Math.floor(e*(a.interval-a.duration-2*a.mute)/1e3)),f.last>=c.length){o=0;continue t}}else s.skip=Math.max(0,s.skip-c.length)}return{newEncodes:i,hasNext:n.idx<n.keys.length}}};var l=function(t,e,r,n,a){for(var i=e,s=n;;i++,s++){if(i>=t.length||s>=r.length)return{last:i,cur:s};a&&(t[i]=0);var o,u=t[i],c=r[s];o=u<0&&c<0?u+c-u*c/-32767:u+c-u*c/32767,t[i]=o}},f={1:[697,1209],2:[697,1336],3:[697,1477],A:[697,1633],4:[770,1209],5:[770,1336],6:[770,1477],B:[770,1633],7:[852,1209],8:[852,1336],9:[852,1477],C:[852,1633],"*":[941,1209],0:[941,1336],"#":[941,1477],D:[941,1633]}}();