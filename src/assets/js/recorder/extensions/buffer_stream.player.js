/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/buffer_stream.player.js
*/
!function(){"use strict";var l=function(e){return new t(e)},t=function(e){var t={play:!0,realtime:!0};for(var r in e)t[r]=e[r];this.set=e=t,e.onInputError||(e.onInputError=function(e,t){console.error("[BufferStreamPlayer]"+e)})};t.prototype=l.prototype={getAudioSrc:function(){return this._src||(this._src=(window.URL||webkitURL).createObjectURL(this.getMediaStream())),this._src},getMediaStream:function(){if(!this._dest)throw new Error("BufferStreamPlayer未start");return this._dest.stream},start:function(t,r){var a=this;a.inputN=0,a.inputQueueIdx=0,a.inputQueue=[],a.bufferSampleRate=0,a.audioBuffer=0,a.pcmBuffer=[[],[]];var e=function(e){r&&r("浏览器不支持打开BufferStreamPlayer"+(e?"："+e:""))},n=1;if(Recorder.Support()){var u=Recorder.Ctx.createBufferSource();u.start&&void 0!==u.onended||(n=0)}else n=0;if(n){var f=function(){var e=Recorder.Ctx.createMediaStreamDestination();e.channelCount=1,a._dest=e,t&&t(),a._inputProcess(),i?(console.warn("BufferStreamPlayer：此浏览器的AudioBuffer实现不支持动态特性，采用兼容模式"),a._writeInt=setInterval(function(){a._writeBad()},10)):a._writeInt=setInterval(function(){a._writeBuffer()},500)},i=l.BadAudioBuffer;if(a.__abTest||null!=i)setTimeout(f);else{var o=l({play:!1,sampleRate:8e3});o.__abTest=1,o.start(function(){var n=Recorder({type:"unknown",sourceStream:o.getMediaStream(),onProcess:function(e){for(var t=e[e.length-1],r=1,a=0;a<t.length;a++)if(0!=t[a]){r=0;break}r&&e.length<5||(n.close(),o.stop(),l.BadAudioBuffer=i=r,f())}});n.open(function(){n.start()},e)},e);for(var c=new Int16Array(8e3),s=0;s<8e3;s++)c[s]=~~(32767*Math.random()*2-32767);o.input(c)}}else e("")},stop:function(){var e=this;clearInterval(e._writeInt),e.inputQueue=0,e._src&&((window.URL||webkitURL).revokeObjectURL(e._src),e._src=0);var t=e.bufferSource;t&&(t.disconnect(),t.stop()),e.bufferSource=0,e.audioBuffer=0},input:function(e){var t=this,r=t.set,a=++t.inputN;if(!t.inputQueue)throw new Error("未调用start方法");r.decode?n(e,function(e){t.inputQueue&&(_(e.data,e.sampleRate),t._input2(a,e.data,e.sampleRate))},function(e){t._inputErr(e,a)}):t._input2(a,e,r.sampleRate)},_input2:function(r,e,a){var n=this,t=n.set;t.transform?t.transform(e,a,function(e,t){n.inputQueue&&(a=t||a,n._input3(r,e,a))},function(e){n._inputErr(e,r)}):n._input3(r,e,a)},_input3:function(e,t,r){var a=this;t&&t.subarray?r?a.bufferSampleRate&&a.bufferSampleRate!=r?a._inputErr("input调用失败：data的sampleRate="+r+"和之前的="+a.bufferSampleRate+"不同",e):(a.bufferSampleRate||(a.bufferSampleRate=r),a.inputQueue[e]=t,a._dest&&a._inputProcess()):a._inputErr("input调用失败：未提供sampleRate",e):a._inputErr("input调用失败：非pcm[Int16,...]输入时，必须解码或者使用transform转换",e)},_inputErr:function(e,t){this.inputQueue[t]=1,this.set.onInputError(e,t)},_inputProcess:function(){var e=this;if(e.bufferSampleRate){for(var t=e.inputQueue,r=e.inputQueueIdx+1;r<t.length;r++){var a=t[r];if(1!=a){if(!a)return;t[e.inputQueueIdx=r]=null;var n=e.pcmBuffer,u=n[0],f=n[1];if(u.length){if(f.length){var i=new Int16Array(u.length+f.length);i.set(u),i.set(f,u.length),n[0]=i}}else n[0]=f;n[1]=a}else e.inputQueueIdx=r}l.BadAudioBuffer?e._writeBad():e.audioBuffer?e._writeBuffer():e._createBuffer(!0)}},_createBuffer:function(e){var t=this,r=t.set;if(e||t.audioBuffer){var a=Recorder.Ctx,n=t.bufferSampleRate,u=60*n,f=a.createBuffer(1,u,n),i=a.createBufferSource();i.channelCount=1,i.buffer=f,i.connect(t._dest),r.play&&i.connect(a.destination),i.onended=function(){i.disconnect(),i.stop(),t._createBuffer()},i.start(),t.bufferSource=i,t.audioBuffer=f,t.audioBufferIdx=0,t._createBufferTime=Date.now(),t._writeBuffer()}},_writeBuffer:function(){var e=this,t=e.set,r=e.audioBuffer,a=e.bufferSampleRate,n=e.audioBufferIdx;if(r){var u=Math.floor((Date.now()-e._createBufferTime)/1e3*a);e.audioBufferIdx+.005*a<u&&(e.audioBufferIdx=u);var f=Math.max(0,e.audioBufferIdx-u),i=r.length-e.audioBufferIdx;if(!((i=Math.min(i,~~(.8*a)-f))<1)){var o=e.pcmBuffer,c=o[0],s=o[1];if(c.length+s.length!=0){for(var l=0,d=1,h=t.realtime;h;){var p=f+c.length;if(p<.3*a){var m=Math.floor(.15*a-p);0==n&&0<m&&(e.audioBufferIdx=Math.max(e.audioBufferIdx,m)),h=!1;break}var B=3*a-f;c.length>B&&(c=c.subarray(c.length-B),o[0]=c),d=1.6,l=Math.min(i,Math.floor((c.length+s.length)/d));break}h||(l=Math.min(i,c.length+s.length)),l&&(e.audioBufferIdx=e._subWrite(r,l,e.audioBufferIdx,d))}}}},_writeBad:function(){var e=this,t=e.set,r=e.audioBuffer,a=e.bufferSampleRate,n=Recorder.Ctx;if(r){var u=r.length/a*1e3;if(Date.now()-e._createBufferTime<u-5)return}var f=~~(.8*a),i=t.PlayBufferDisable?0:a/1e3*300,o=e.pcmBuffer,c=o[0],s=o[1],l=c.length+s.length;if(!(0==l||l<i)){for(var d=0,h=1,p=t.realtime;p;){if(c.length<.3*a){p=!1;break}var m=3*a;c.length>m&&(c=c.subarray(c.length-m),o[0]=c),h=1.6,d=Math.min(f,Math.floor((c.length+s.length)/h));break}if(p||(d=Math.min(f,c.length+s.length)),d){r=n.createBuffer(1,d,a),e._subWrite(r,d,0,h),_(r.getChannelData(0),a);var B=n.createBufferSource();B.channelCount=1,B.buffer=r,B.connect(e._dest),t.play&&B.connect(n.destination),B.start(),e.bufferSource=B,e.audioBuffer=r,e._createBufferTime=Date.now()}}},_subWrite:function(e,t,r,a){for(var n=this.pcmBuffer,u=n[0],f=n[1],i=new Int16Array(t),o=0,c=0,s=0;c<t&&s<u.length;)i[c++]=u[o],s+=a,o=Math.round(s);if(o>=u.length){for(u=new Int16Array(0),o=s=0;c<t&&s<f.length;)i[c++]=f[o],s+=a,o=Math.round(s);f=o>=f.length?new Int16Array(0):f.subarray(o),n[1]=f}else u=u.subarray(o);n[0]=u;var l=e.getChannelData(0);for(o=0;o<t;o++,r++)l[r]=i[o]/32767;return r}};var _=l.FadeInOut=function(e,t){for(var r=t/1e3*1,a=0;a<r;a++)e[a]*=a/r;var n=e.length;for(a=n-r;a<n;a++)e[a]*=(n-a)/r},n=l.DecodeAudio=function(e,f,t){Recorder.Support()?Recorder.Ctx.decodeAudioData(e,function(e){for(var t=e.getChannelData(0),r=e.sampleRate,a=new Int16Array(t.length),n=0;n<t.length;n++){var u=Math.max(-1,Math.min(1,t[n]));u=u<0?32768*u:32767*u,a[n]=u}f&&f({sampleRate:r,duration:Math.round(t.length/r*1e3),data:a})},function(e){t&&t("音频解码失败:"+(e&&e.message||"-"))}):t&&t("浏览器不支持音频解码")};Recorder.BufferStreamPlayer=l}();