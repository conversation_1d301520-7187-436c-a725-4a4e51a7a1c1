/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/sonic.js
*/
!function(){"use strict";function o(t){var r=function(t){this.set=t;var r=function(t){var c,a,f,s,o=65,n=400,u=4e3,g=12,A=601,j=[0,0,0,0,0,0,0,-1,-1,-2,-2,-3,-4,-6,-7,-9,-10,-12,-14,-17,-19,-21,-24,-26,-29,-32,-34,-37,-40,-42,-44,-47,-48,-50,-51,-52,-53,-53,-53,-52,-50,-48,-46,-43,-39,-34,-29,-22,-16,-8,0,9,19,29,41,53,65,79,92,107,121,137,152,168,184,200,215,231,247,262,276,291,304,317,328,339,348,357,363,369,372,374,375,373,369,363,355,345,332,318,300,281,259,234,208,178,147,113,77,39,0,-41,-85,-130,-177,-225,-274,-324,-375,-426,-478,-530,-581,-632,-682,-731,-779,-825,-870,-912,-951,-989,-1023,-1053,-1080,-1104,-1123,-1138,-1149,-1154,-1155,-1151,-1141,-1125,-1105,-1078,-1046,-1007,-963,-913,-857,-796,-728,-655,-576,-492,-403,-309,-210,-107,0,111,225,342,462,584,708,833,958,1084,1209,1333,1455,1575,1693,1807,1916,2022,2122,2216,2304,2384,2457,2522,2579,2625,2663,2689,2706,2711,2705,2687,2657,2614,2559,2491,2411,2317,2211,2092,1960,1815,1658,1489,1308,1115,912,698,474,241,0,-249,-506,-769,-1037,-1310,-1586,-1864,-2144,-2424,-2703,-2980,-3254,-3523,-3787,-4043,-4291,-4529,-4757,-4972,-5174,-5360,-5531,-5685,-5819,-5935,-6029,-6101,-6150,-6175,-6175,-6149,-6096,-6015,-5905,-5767,-5599,-5401,-5172,-4912,-4621,-4298,-3944,-3558,-3141,-2693,-2214,-1705,-1166,-597,0,625,1277,1955,2658,3386,4135,4906,5697,6506,7332,8173,9027,9893,10769,11654,12544,13439,14335,15232,16128,17019,17904,18782,19649,20504,21345,22170,22977,23763,24527,25268,25982,26669,27327,27953,28547,29107,29632,30119,30569,30979,31349,31678,31964,32208,32408,32565,32677,32744,32767,32744,32677,32565,32408,32208,31964,31678,31349,30979,30569,30119,29632,29107,28547,27953,27327,26669,25982,25268,24527,23763,22977,22170,21345,20504,19649,18782,17904,17019,16128,15232,14335,13439,12544,11654,10769,9893,9027,8173,7332,6506,5697,4906,4135,3386,2658,1955,1277,625,0,-597,-1166,-1705,-2214,-2693,-3141,-3558,-3944,-4298,-4621,-4912,-5172,-5401,-5599,-5767,-5905,-6015,-6096,-6149,-6175,-6175,-6150,-6101,-6029,-5935,-5819,-5685,-5531,-5360,-5174,-4972,-4757,-4529,-4291,-4043,-3787,-3523,-3254,-2980,-2703,-2424,-2144,-1864,-1586,-1310,-1037,-769,-506,-249,0,241,474,698,912,1115,1308,1489,1658,1815,1960,2092,2211,2317,2411,2491,2559,2614,2657,2687,2705,2711,2706,2689,2663,2625,2579,2522,2457,2384,2304,2216,2122,2022,1916,1807,1693,1575,1455,1333,1209,1084,958,833,708,584,462,342,225,111,0,-107,-210,-309,-403,-492,-576,-655,-728,-796,-857,-913,-963,-1007,-1046,-1078,-1105,-1125,-1141,-1151,-1155,-1154,-1149,-1138,-1123,-1104,-1080,-1053,-1023,-989,-951,-912,-870,-825,-779,-731,-682,-632,-581,-530,-478,-426,-375,-324,-274,-225,-177,-130,-85,-41,0,39,77,113,147,178,208,234,259,281,300,318,332,345,355,363,369,373,375,374,372,369,363,357,348,339,328,317,304,291,276,262,247,231,215,200,184,168,152,137,121,107,92,79,65,53,41,29,19,9,0,-8,-16,-22,-29,-34,-39,-43,-46,-48,-50,-52,-53,-53,-53,-52,-51,-50,-48,-47,-44,-42,-40,-37,-34,-32,-29,-26,-24,-21,-19,-17,-14,-12,-10,-9,-7,-6,-4,-3,-2,-2,-1,-1,0,0,0,0,0,0,0],i=0,e=0,l=0,h=0,I=0,O=0,v=!1,d=0,x=0,p=0,w=0,m=0,M=0,y=0,k=0,b=0,R=0,S=0,L=0,U=0,_=0,P=0,F=0,T=0;function W(t,r){r*=x;var o=new Int16Array(r),n=t.length<=r?t.length:r;return ot.arraycopy(t,0,o,0,n),o}function B(t,r,o,n,e){ot.arraycopy(o,n*x,t,r*x,e*x)}function C(t,r){b=Math.floor(t/n),R=Math.floor(t/o),p=S=2*R,c=new Int16Array(S*r),m=S,a=new Int16Array(S*r),w=S,f=new Int16Array(S*r),s=new Int16Array(S),U=t,x=r,_=O=I=0}function D(t){m<y+t&&(a=W(a,m+=(m>>1)+t))}function N(t){p<M+t&&(c=W(c,p+=(p>>1)+t))}function Q(t,r,o){D(o),B(a,y,t,r,o),y+=o}function V(t){var r=L;return S<r&&(r=S),Q(c,t,r),L-=r,r}function E(t,r,o){var n,e=Math.floor(S/o),i=x*o;r*=x;for(var a=0;a<e;a++){for(var c=n=0;c<i;c++)n+=t[r+a*i+c];n=Math.floor(n/i),s[a]=n}}function q(t,r,o,n){var e=0,i=255,a=1,c=0;r*=x;for(var f=o;f<=n;f++){for(var s=0,u=0;u<f;u++){var l=t[r+u],h=t[r+f+u];s+=h<=l?l-h:h-l}s*e<a*f&&(a=s,e=f),c*f<s*i&&(c=s,i=f)}return F=Math.floor(a/e),T=Math.floor(c/i),e}function z(t,r,o){var n,e,i=1;if(u<U&&0==d&&(i=Math.floor(U/u)),1==x&&1==i)n=q(t,r,b,R);else if(E(t,r,i),n=q(s,0,Math.floor(b/i),Math.floor(R/i)),1!=i){var a=(n*=i)-(i<<2),c=n+(i<<2);a<b&&(a=b),R<c&&(c=R),1==x?n=q(t,r,a,c):(E(t,r,1),n=q(s,0,a,c))}return e=function(t,r,o){if(0==t||0==_)return!1;if(o){if(3*t<r)return!1;if(2*t<=3*P)return!1}else if(t<=P)return!1;return!0}(F,T,o)?_:n,P=F,_=n,e}function G(t,r,o,n,e,i,a,c){for(var f=0;f<r;f++)for(var s=n*r+f,u=c*r+f,l=i*r+f,h=0;h<t;h++)o[s]=Math.floor((e[l]*(t-h)+a[u]*h)/t),s+=r,l+=r,u+=r}function H(t,r,o,n,e,i,a,c,f){for(var s=0;s<r;s++)for(var u=e*r+s,l=f*r+s,h=a*r+s,v=0;v<t+o;v++)v<o?(n[u]=Math.floor(i[h]*(t-v)/t),h+=r):(v<t?(n[u]=Math.floor((i[h]*(t-v)+c[l]*(v-o))/t),h+=r):n[u]=Math.floor(c[l]*(v-o)/t),l+=r),u+=r}function J(t){var r=y-t;w<k+r&&(f=W(f,w+=(w>>1)+r)),B(f,k,a,t,r),y=t,k+=r}function K(t){0!=t&&(B(f,0,f,t,k-t),k-=t)}function X(t){return 0<=t?1:-1}function Y(t,r,o,n){var e,i,a,c,f,s,u,l,h,v,d,p,w,m=0,M=O*o,y=I*n,k=(I+1)*n,b=k-M-1,R=k-y,S=0;for(e=0;e<g;e++)f=e,s=b,u=R,void 0,l=Math.floor((A-1)/g),h=Math.floor(f*l+s*l/u),v=h+1,d=f*l*u+s*l-h*u,p=j[h],w=j[v],i=Math.floor((p*(u-d)+w*d<<1)/u),a=t[r+e*x]*i,(c=X(m))!=X(m+=a)&&X(a)==c&&(S+=c);return 0<S?32767:S<0?-32768:m>>16&65535}function Z(t,r,o,n){var e;return 2<=o?e=Math.floor(n/(o-1)):(e=n,L=Math.floor(n*(2-o)/(o-1))),D(e),G(e,x,a,y,t,r,t,r+n),y+=e,e}function $(t,r,o,n){var e;return o<.5?e=Math.floor(n*o/(1-o)):(e=n,L=Math.floor(n*(2*o-1)/(1-o))),D(n+e),B(a,y,t,r,n),G(e,x,a,y+n,t,r+n,t,r),y+=n+e,e}function tt(t){var r,o,n=M,e=0;if(!(M<S)){for(;0<L?(o=V(e),e+=o):(r=z(c,e,!0),1<t?(o=Z(c,e,t,r),e+=r+o):(o=$(c,e,t,r),e+=o)),e+S<=n;);var i,a;B(c,0,c,i=e,a=M-i),M=a}}function r(){var t=y,r=i/l,o=h;v||(o*=l),1.00001<r||r<.99999?tt(r):(Q(c,0,M),M=0),v?1!=l&&function(t){var r,o,n=0;if(y==t)return;J(t);for(;S<=k-n;)r=z(f,n,!1),D(o=Math.floor(r/l)),1<=l?G(o,x,a,y,f,n,f,n+r-o):H(r,x,o-r,a,y,f,n,f,n),y+=o,n+=r;K(n)}(t):1!=o&&function(t,r){var o,n=Math.floor(U/t),e=U;for(;16384<n||16384<e;)n>>=1,e>>=1;if(y==r)return;for(J(r),o=0;o<k-1;o++){for(;O*e<(I+1)*n;){D(1);for(var i=0;i<x;i++)a[y*x+i]=Y(f,o*x+i,e,n);O++,y++}if(++I==e){if(I=0,O!=n)throw new Error("Assertion failed: newRatePosition != newSampleRate\n");O=0}}K(o)}(o,t),1!=e&&function(t,r,o,n){for(var e=Math.floor(4096*n),i=r*x,a=i+o*x,c=i;c<a;c++){var f=t[c]*e>>12;32767<f?f=32767:f<-32767&&(f=-32767),t[c]=f}}(a,t,y-t,e)}function rt(t){!function(t,r){if(0==r)return;N(r),B(c,M,t,0,r),M+=r}(t,t?t.length:0),r()}return t.setPitch=function(t){l=t},t.setRate=function(t){h!=t&&(h=t,O=I=0)},t.setSpeed=function(t){i=t},t.setVolume=function(t){e=t},t.setChordPitch=function(t){v=t},t.setQuality=function(t){d=t},{New:function(t,r){C(t,r),h=e=l=i=1,v=!1,d=O=I=0},flushStream:function(){var t=M,r=i/l,o=h*l,n=Math.floor(y+Math.floor((t/r+k)/o+.5));N(t+2*S);for(var e=0;e<2*S*x;e++)c[t*x+e]=0;M+=2*S,rt(null),n<y&&(y=n),k=L=M=0},writeShortToStream:rt,readShortFromStream:function(){var t=y,r=new Int16Array(t);return 0==t||(B(r,0,a,0,t),B(a,0,a,t,0),y=0),r}}}(this);(this.sonic=r).New(t.sampleRate,1)};r.prototype=o.prototype={input:function(t){return this.sonic.writeShortToStream(t),this.sonic.readShortFromStream()},flush:function(){return this.sonic.flushStream(),this.sonic.readShortFromStream()}};var ot={arraycopy:function(t,r,o,n,e){for(var i=0;i<e;i++)o[n+i]=t[r+i]}};return new r(t)}var a;Recorder.Sonic=o,Recorder.BindDestroy("sonicWorker",function(){console.log("sonicWorker Destroy"),a&&a.terminate(),a=null});var c={id:0};o.Async=function(t){var r=a;try{if(!r){var o=");var wk_ctxs={};self.onmessage="+function(t){var r=t.data,o=wk_ctxs[r.id];if("init"==r.action)wk_ctxs[r.id]={sampleRate:r.sampleRate,sonicObj:wk_sonic({sampleRate:r.sampleRate})};else if(!o)return;switch(r.action){case"flush":var n=o.sonicObj.flush();self.postMessage({action:r.action,id:r.id,call:r.call,pcm:n}),o.sonicObj=null,delete wk_ctxs[r.id];break;case"input":n=o.sonicObj.input(r.pcm),self.postMessage({action:r.action,id:r.id,call:r.call,pcm:n});break;default:/^set/.test(r.action)&&o.sonicObj[r.action](r.param)}},n=Recorder.Sonic.toString(),e=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_sonic=(",n,o],{type:"text/javascript"}));r=new Worker(e),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(e)},1e4),r.onmessage=function(t){var r=c[t.data.id];if(r){var o=r.cbs[t.data.call];o&&o(t.data)}}}var i=new f(r,t);return i.id=++c.id,c[i.id]=i,r.postMessage({action:"init",id:i.id,sampleRate:t.sampleRate,x:new Int16Array(5)}),a=r,i}catch(t){return r&&r.terminate(),console.error(t),null}};var f=function(t,r){this.worker=t,this.set=r,this.cbs={i:0}};f.prototype={cb:function(r){var o=this,n="cb"+ ++o.cbs.i;return o.cbs[n]=function(t){delete o.cbs[n],r(t)},n},flush:function(n){var e=this;e.worker&&e.worker.postMessage({action:"flush",id:e.id,call:e.cb(function(t){n&&n(t.pcm),e.worker=null,delete c[e.id];var r=-1;for(var o in c)r++;r&&console.warn("sonic worker剩"+r+"个在串行等待")})})},input:function(t,r){this.worker&&this.worker.postMessage({action:"input",id:this.id,pcm:t,call:this.cb(function(t){r&&r(t.pcm)})})}};var t=function(r){f.prototype[r]=function(t){this.worker&&this.worker.postMessage({action:r,id:this.id,param:t})}};t("setPitch"),t("setRate"),t("setSpeed"),t("setVolume"),t("setChordPitch"),t("setQuality")}();