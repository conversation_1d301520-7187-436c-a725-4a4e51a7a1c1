const steps = [
  {
    element: '#create-new-unit',
    popover: {
      title: 'Create New Unit',
      description: '<div>' +
        '<ul class="font-size-16" style="color: #111C1C;height: fit-content;width: 320px; padding-right: 0px;">' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Click the “+ New Unit” button to quickly create your unit.' + '</li>' +
        '</ul></div>',
      position: 'bottom-center',
      prevBtnText: '(1/3)'
    }
  },
  {
    element: '#adapt-unit',
    popover: {
      title: 'Adapt Unit',
      description: '<div>' +
        '<ul class="font-size-16" style="color: #111C1C;height: fit-content;width: 320px; padding-right: 0px;">' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Adapt units to fit your classroom by creating personalized UDL and CLR strategies that support IEP, ELD children, and children from diverse cultural backgrounds.' + '</li>' +
        '</ul></div>',
      position: 'bottom-center',
      prevBtnText: '(2/3)'
    }
  },
  {
    element: '#exemplar-unit',
    popover: {
      title: 'Exemplar Unit',
      description: '<div style="height: 175px!important;margin-bottom: 20px">' +
        '<ul class="font-size-16" style="color: #111C1C;height: fit-content;width: 320px; padding-right: 0px;">' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Explore the unit details and get ready to adapt it with our tailored features, personalized for your classroom\'s unique needs.' + '</li>' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Download or export to your drive for convenient offline access and easy integration!' + '</li>' +
        '</ul></div>',
      position: 'right-center',
      showButtons: ['next', 'previous'],
      prevBtnText: '(3/3)'
    }
  }
]
export default steps
