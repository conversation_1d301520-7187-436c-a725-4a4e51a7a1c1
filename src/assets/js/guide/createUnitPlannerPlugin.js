const steps = [
  {
    element: '#effortless-unit-setup',
    popover: {
      title: 'Effortless Unit Setup',
      description: '<div style="height: 96px!important;">' +
        '<ul class="font-size-16" style="color: #111C1C;height: fit-content;width: 320px; padding-right: 0px;">' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Use Exemplars to kickstart your unit planning with ease.' + '</li>' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Leverage Prompt History to quickly reuse past inputs for faster planning!' + '</li>' +
        '</ul></div>',
      position: 'right',
      prevBtnText: '(1/4)'
    }
  },
  {
    element: '#description-assistant',
    popover: {
      title: 'Description Assistant',
      description: '<div>' +
        '<ul class="font-size-16" style="color: #111C1C;height: fit-content;width: 320px; padding-right: 0px;">' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Use the Description Assistant to get helpful prompts, guiding you to include key details like unit overview, books you\'ll use, student preferences, and classroom setup.' + '</li>' +
        '</ul></div>',
      position: 'right-center',
      prevBtnText: '(2/4)'
    }
  },
  {
    element: '#standard-alignment',
    popover: {
      title: 'Standard Alignment',
      description: '<div>' +
        '<ul class="font-size-16" style="color: #111C1C;height: fit-content;width: 320px; padding-right: 0px;">' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Align your unit lessons with the standards from over 50 states, ensuring effective, standards-based instruction with AI-driven support.' + '</li>' +
        '</ul></div>',
      position: 'right-center',
      prevBtnText: '(3/4)'
    }
  },
  {
    element: '#localized-curriculum',
    popover: {
      title: 'Localized Curriculum',
      description: '<div>' +
        '<ul class="font-size-16" style="color: #111C1C;height: fit-content;width: 320px; padding-right: 0px;">' +
        '<li class="font-size-16" style="color: #111C1C">' + 'Localizes curriculum to engage students in meaningful and relevant learning experiences about what matters most to them.' + '</li>' +
        '</ul></div>',
      position: 'right',
      showSkip: false, // 隐藏 skip 按钮
      prevBtnText: '(4/4)'
    }
  }
]
export default steps
