/* 移动端适配样式 */
@media screen and (max-width: 768px) {

  .h-full {
    height: auto !important;
  }

  /* 通用样式 */
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
  
  .mobile-padding {
    padding: 10px !important;
  }
  
  .mobile-margin {
    margin: 10px 0 !important;
  }
  
  .mobile-no-margin {
    margin: 0 !important;
  }
  
  .mobile-flex-column {
    flex-direction: column !important;
  }
  
  .mobile-text-center {
    text-align: center !important;
  }
  
  /* Unit Planner 特定样式 */
  .unit-steps {
    width: 100% !important;
    margin: 10px auto !important;
  }
  
  /* UnitOverviewStep 组件样式 */
  .unit-info-card {
    position: relative !important;
    top: 0 !important;
    margin-bottom: 15px !important;
  }
  
  .sticky-bottom {
    position: relative !important;
    bottom: auto !important;
    z-index: 1 !important;
  }
  
  /* WeeklyPlanOverviewStep 组件样式 */
  .weekly-plan-overview-card .operation-panel {
    position: relative !important;
    top: 0 !important;
  }
  
  /* LessonOverviewStep 组件样式 */
  .lesson-overview-step .display-flex {
    gap: 10px !important;
  }
  
  /* LessonDetail 组件样式 */
  .cover-container {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  .add-margin-l-24 {
    margin-left: 0 !important;
    margin-top: 15px !important;
  }
  
  /* 课程材料部分样式 */
  .lesson-material-input {
    flex-direction: column !important;
    gap: 0 !important;
    .media-uploader-small {
      margin-left: 0 !important;
    }
  }
  
  .lesson-material-editor-textarea-top {
    width: 100% !important;
  }
  
  .media-uploader-small {
    width: 100% !important;
    margin-top: 15px !important;
  }
  
  /* 对话框样式 */
  .el-dialog {
    width: 95% !important;
    margin: 10px auto !important;
    
    .el-dialog__body {
      max-height: calc(90vh - 100px) !important;
      overflow-y: auto !important;
    }
  }
  
  /* Message Box 样式 */
  .el-message-box {
    width: 90% !important;
    max-width: 90vw !important;
    
    .el-message-box__content {
      max-height: calc(90vh - 150px) !important;
      overflow-y: auto !important;
      word-break: break-word !important;
      max-width: 100% !important;
      
      .el-message-box__message {
        word-break: break-word !important;
        white-space: normal !important;
        line-height: 1.5 !important;
      }
    }

    .el-message-box__input {
      .el-input {
        width: 100% !important;
      }
    }

    .el-message-box__btns {
      .el-button {
        margin-left: 8px !important;
        min-width: 80px !important;
      }
    }
  }
  
  /* 表单样式 */
  .el-form-item {
    margin-bottom: 15px !important;
  }
  
  /* 按钮组样式 */
  .bottom-btn-group {
    flex-direction: column !important;
    gap: 10px !important;
  }
  
  .bottom-btn-group .el-button {
    width: 100% !important;
  }
  
  /* 卡片样式 */
  .el-card {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  /* 步骤条样式 */
  .el-steps {
    padding: 0 10px !important;
  }
  
  .el-step__title {
    font-size: 12px !important;
  }
  
  /* 标签页样式 */
  .el-tabs__item {
    padding: 0 10px !important;
  }
  
  /* 下拉框样式 */
  .el-select-dropdown {
    width: 90vw !important;
    min-width: 90vw !important;
    left: 5vw !important;
  }
  
  /* 底部按钮样式 */
  .lesson-overview-step {
    .display-flex {
      gap: 10px !important;
    }

    .bottom-btn-group {
      margin-bottom: 16px !important;
    }
  }
  
  /* 课程概览卡片样式 */
  .lesson-overview-card {
    .el-select {
      width: 100% !important;
    }
  }
  
  /* 课程幻灯片样式 */
  .lesson-slide {
    &-card {
      padding: 16px !important;
      background-position: right bottom !important;
      background-size: 200px !important;
      
      &::before {
        background: linear-gradient(180deg, #F5FEFF 70%, transparent) !important;
      }
    }
    
    &-content {
      flex-direction: column !important;
    }
    
    &-info {
      flex-direction: column !important;
      align-items: center !important;
      position: relative !important;
    }
    
    &-cover {
      width: 100% !important;
      min-width: 100% !important;
      margin-right: 0 !important;
      margin-bottom: 16px !important;
      
      img, .slide-cover {
        left: 0 !important;
        top: 0 !important;
      }
    }
    
    &-detail {
      margin-left: 0 !important;
      width: 100% !important;
      text-align: center !important;
      
      .lesson-slide-title {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 8px !important;
      }
    }
    
    &-actions {
      justify-content: center !important;
      margin-top: 16px !important;
      
      .el-button {
        min-width: 120px !important;
      }
    }

    &-regenerate {
      position: absolute !important;
      right: 0 !important;
      top: 0 !important;
      margin: 0 !important;
    }
  }
  
  /* 幻灯片预览弹窗样式 */
  .lesson-slide-preview-dialog {
    .el-dialog__body {
      padding: 0 16px !important;
      
      .display-flex {
        flex-direction: column !important;
        
        & > div:first-child {
          width: 100% !important;
          margin-bottom: 16px !important;
        }
        
        & > div:last-child {
          width: 100% !important;
          margin-left: 0 !important;
          padding-right: 0 !important;
        }
      }

      .lesson-lecture-slides {
        height: 260px !important;
      }
    }
  }

  .lesson-detail-item-action {
    display: flex !important;
    justify-content: flex-end !important;
    align-items: center !important;
  }

  .formative-quiz-card {
    .el-card__header > div {
      flex-direction: column !important;
      justify-content: flex-start !important;
      align-items: flex-start !important;
      > div:first-child {
        margin-bottom: 8px !important;
      }
      > div:last-child {
        display: flex !important;
      }
    }
  }

  .quiz-header-group {
    flex-direction: column !important;
    height: 100% !important;
    gap: 8px !important;
  }

  /* 内容区域添加底部边距 */
  .unit-review-container {
    padding-bottom: 64px !important; /* 底部按钮区域的高度 + 额外边距 */
  }

  /* 课程详情底部按钮样式 */
  .desktop-actions {
    display: none !important;
  }

  .mobile-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
    padding: 16px !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: white !important;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
    z-index: 100 !important;

    .action-container-row {
      display: flex !important;
      flex-direction: row !important;
      flex-wrap: unset !important;
      gap: 8px !important;
      width: 100% !important;

      &:first-child {
        .el-button {
          flex: 1 1 0 !important;
          margin: 0 !important;
          height: 40px !important;
          padding: 0 8px !important;
        }
      }

      &:not(:first-child) {
        .el-button {
          flex: 1 1 calc(50% - 4px) !important;
          margin: 0 !important;
          height: 40px !important;
        }
      }
    }
  }

  .batch-generate.position-absolute {
    position: relative !important;
  }

  /* 课程详情顶部导航栏样式 */
  .unit-review-container {
    .display-flex.justify-content {
      position: relative !important;
      display: flex;
      flex-direction: row !important;
      align-items: center !important;
      padding: 12px 16px !important;
      gap: 12px !important;
      
      /* 返回按钮和标题样式 */
      .position-absolute {
        position: static !important;
        
        &.lg-pointer {
          flex: none !important;
          
          .font-size-16 {
            display: none !important; /* 隐藏返回文字,只显示图标 */
          }
        }
      }
      
      /* 标题样式 */
      .font-size-18 {
        flex: 1 !important;
        text-align: center !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
      
      /* 批量生成和反馈按钮容器 */
      .batch-generate {
        position: static !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        flex: none !important;
        
        .ai-btn {
          padding: 0 12px !important;
          height: 32px !important;
          
          .lg-icon {
            margin-right: 4px !important;
          }
        }
      }
    }
  }
  .unit-detail-container {
    margin: 0 !important;
    padding: 0 12px !important;
  }
  .lesson-share-info {
    &> :first-child {
      left: 0 !important;
    }
  }

  /* 课程概览详情样式 */
  .lesson-overview-detail {
    .preview-container {
      margin: 0 !important;
    }
  }

  /* 课程概览底部按钮组样式 */
  .lesson-overview-bottom-btn-group {
    .back-btn-group {
      margin-bottom: 0 !important;
    }
    .update-btn-group {
      margin-top: 0 !important;
      .el-button {
        margin-top: 0 !important;
      }
    }
  }

  .lesson-detail-header-btn-group {
    flex-direction: column !important;
  }

  /* 级联选择器样式修复 */
  .el-cascader__dropdown {
    max-width: 98vw !important;
  }

  .measures-apply-dialog {
    margin-top: 1vh !important;
  }
}

/* 平板适配样式 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .tablet-flex-wrap {
    flex-wrap: wrap !important;
  }
  
  .tablet-col-12 {
    width: 50% !important;
    flex: 0 0 50% !important;
  }
  
  .unit-steps {
    width: 90% !important;
  }
}

/* 桌面端样式 */
@media screen and (min-width: 769px) {
  .desktop-actions {
    display: flex !important;
  }

  .mobile-actions {
    display: none !important;
  }
} 