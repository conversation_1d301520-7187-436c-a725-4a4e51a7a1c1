/* 错误提示信息 */
.el-form-item__error{
    font-size: 13px;
}
/* safair input光标问题 */
.el-input__inner {
    font-size: 14px;
    line-height: 1.42857143;
}
/* 自定义警告弹窗样式 */
.lg-modal-warning{
  border: none;
  width: 600px;
  vertical-align: top;
  position: relative;
  top: 50px;
  .el-message-box__header{
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
    padding: 15px;
    .el-message-box__title{
      color: #a94442;
      font-size: 24px;
      line-height: 34px;
    }
    .el-message-box__headerbtn{
      display: none;
    }
  }
  .el-message-box__content{
    font-size: 18px;
    padding: 21px 15px;
    text-indent:2em;
  }
  .el-message-box__btns{
    overflow: hidden;
    padding: 15px 15px 5px 15px;
    border-top: 1px solid rgb(229, 229, 229);
    .el-button{
      font-size: 14px;
    }
    .el-button{
      float: left;
      &:hover,&:focus,&:active{
        color: #111C1C;
        background-color: #edf1f2;
        border-color: #c7d3d6
      }
    }
    .el-button:last-child{
      float: right;
      color: #fff;
      background-color: #f05050;
      border-color: #f05050;
      &:hover,&:focus,&:active{
        color: #fff;
        background-color: #ee3939;
        border-color: #F56C6C;
        box-shadow: 0 0 0 3px rgba(245, 108, 108, 0.4)!important;
      }
    }
    .el-button:active {
        color: #fff;
        background-color: #ee3939;
        border-color: #ed2a2a;
    }
  }
}
/* 状态提示窗 */
.el-message{
  font-size: 18px;
  .el-message__content{
    font-size: inherit;
  }
}

/* 日期选择器 */
.dashboard-date {
  .el-date-editor.el-input {
    width: 150px;
  }
  .el-date-editor.el-input.selectYear {
    .el-input__inner {
      height: 32px !important;
      padding: 8px 20px 8px 35px !important;
    }
// .el-date-editor.el-input{
//   width: 170px;
//   .el-input__inner{
//     padding-left: 10px;
//   }
//   .el-input__prefix{
//     border-left: 1px solid #cfdadd;
//     right: 5px;
//     left: unset;
//     width: 25px;
//     color: #111C1C;
//     .el-input__icon{
//       padding-left: 5px;
//     }
//   }
// }
// {
  .el-date-editor.el-input.selectYear{
    .el-input__inner{
        height:32px !important;
        padding:8px 20px 8px 35px !important;
      }
      .el-input__icon {
        line-height: 32px !important;
      }
    }
  }
}
    // Tab 切换
  .tabSetting {
    margin-top:10px;
    border:0;
    -webkit-box-shadow: 0 0px 0px 0 rgba(255,255,255,0), 0 0 6px 0 rgba(0,0,0,0);
    box-shadow: 0 0px 0px 0 rgba(0,0,0,0), 0 0 6px 0 rgba(0,0,0,0);
    // font-family:"HelveticaNeueLTPro-Bd";
    >.el-tabs__header {
      background-color:#fff;
    }
    .el-tabs__item {
      display: -webkit-box;
      -webkit-box-orient: horizontal;
      -webkit-box-pack: center;
      -webkit-box-align: center;
      height: 45px;
      line-height:normal;
      font-size: 16px;
    }
    >.el-tabs__header .el-tabs__item{
      width: 50px;
      word-wrap: break-word;
      padding: 0 0 0 5px;
      .el-icon-edit{
        opacity :0;
      }
    }
    >.el-tabs__header .el-tabs__item.is-active {
      border-top: 3px solid #0095C1;
      color: #0095C1;
      .el-icon-edit{
        color:#D3DCE6;
        opacity :1;
      }
    }
    .el-tabs__nav {
      white-space: pre-wrap;

    }
    .el-tabs__item:focus.is-active.is-focus:not(:active) {
        -webkit-box-shadow: 0 0 0px 0px #fff inset;
        box-shadow: 0 0 0px 0px #fff inset;
        border-radius: 0px
    }
  }
  .el-tabs--bottom .el-tabs--left .el-tabs__item:last-child,.el-tabs--bottom .el-tabs--right .el-tabs__item:last-child,.el-tabs--bottom.el-tabs--border-card .el-tabs__item:last-child,.el-tabs--bottom.el-tabs--card .el-tabs__item:last-child,.el-tabs--top .el-tabs--left .el-tabs__item:last-child,.el-tabs--top .el-tabs--right .el-tabs__item:last-child,.el-tabs--top.el-tabs--border-card .el-tabs__item:last-child,.el-tabs--top.el-tabs--card .el-tabs__item:last-child {
      padding-right: 0px;
      padding-left: 2px;
  }
  .el-tabs--top.el-tabs--border-card .el-tabs__item:nth-child(2){
    padding-left: 2px;
  }
.dashboard-date {
  .el-date-editor.el-input{
    width: 150px;
  }
}

  /* 弹框 */
.rateEdit{
  border-radius:6px;
  box-sizing:border-box;
  //font-family: 'HelveticaNeue-Roman';
  .el-dialog__body {
    padding: 0 15px 0 20px;
  }
  .el-dialog__footer{
    padding:0 10px 10px;
  }
}

/* button 默认按钮悬浮样式 */
/* .el-button:focus, .el-button:hover{
  color: #111C1C;
  background-color: #edf1f2;
  border-color: #c7d3d6;
} */
/* font awesome 图标 */
.fa{
  padding-right: 4px;
}

/* 自定义注销账户弹框 */


.closeAccInputBox{
  // height:322px;
  z-index:99;

  .el-input{
    margin-top:24px;
    margin-bottom: 15px;
  }
  .el-message-box__errormsg{
    display:none;
  }
}
.el-message-box-font {
  .el-message-box__btns {
    button{
      font-size: 14px;
    }
  }
}
.overflowHidden{
  overflow: hidden;
}
.closeAccOverview{
  z-index:999;
  .closeAccInputDelige .closeAccInputImg,.closeAccInputDelige p
  {
    float:left;
  }
  .closeAccInputImg{
    text-align:center;
    width:25%;
  }
  .closeAccInputDelige p{
    width:75%;
    font-size:20px;
    text-align:left;
    margin-bottom:10px;
    line-height: 1.42857143;
  }
}

/*按钮样式修改*/
button:focus{
  outline:none;
}
.btn-danger-alt {
    color: #f05050 !important;
    background-color: #fff !important;
    border-color: #f05050 !important;

}
.btn-danger-alt {
    color: #f05050 !important;
    background-color: #fff;
    border-color: #f05050 !important;
    outline:none;
}
.btn-danger-alt:hover { //
    -webkit-box-shadow:  0 0 0 2px #f05050;
    -moz-box-shadow:  0 0 0 2px #f05050;
    box-shadow:  0 0 0 2px #f05050;
    color: #f05050 !important;
}

/* 时间选择器panel sider */
.el-picker-panel__sidebar {
  border-bottom-left-radius: 8px!important;
  border-top-left-radius: 8px!important;
  box-shadow: -5px 0 10px -5px #e0e3e3!important;
}

/* 新注销账户弹框 */
.closeBox {
  .el-dialog__header{
    padding:15px;
    .el-dialog__title {
      font-size:24px;
      color: #111C1C;
    }
  }
  .el-dialog__body {
    padding:15px;
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;
  }
  .el-dialog__footer{
    padding:15px;
    span{
      font-size:14px;
    }
  }
}
.font-black{
  color: #333333;
}
.closeAccDelige{
    text-align: center;
    margin-top:24px;
    p{
      padding:0;
      font-size:20px;
      text-align:left;
      margin-bottom:10px;
      line-height: 1.42857143;
    }
  }
.closeAccInputDelige .closeAccInputImg,.closeAccInputDelige p
  {
    float:left;
  }
  .closeAccInputImg{
    text-align:center;
    width:25%;
  }
  .closeAccInputDelige p{
    width:75%;
    font-size:20px;
    text-align:left;
    margin-bottom:10px;
    line-height: 1.42857143;
  }


// 设置汇率页面按钮
.el-button--danger{
  background-color: #f56c6c;
  border-color: #f56c6c;
  &:active {
      color: #fff;
      background-color: #ee3939;
      border-color: #ed2a2a;
  }
  &:focus,&:hover {
      background: #F78989;
      border-color: #F78989;
      color: #fff;
  }
  &.is-active,&:active {
      background: #FE1C41;
      border-color: #FE1C41;
      color: #fff;
      opacity: 1;
  }
}
// 表单错误提醒
.el-form-item__error{
  // left:90px;
  // font-family:"HelveticaNeueLTPro-Bd";
}
.approvalDiaLog{
  .el-dialog__header{
   display: none;
  }
  .el-dialog__body{
    .el-dialog__headerbtn{
      position: inherit !important;
      right: 0!important;
      top: 10px!important;
      float: right;
    }
  }
}
.subDiaLog{
  .el-dialog__header{
    padding: 10px 10px;
  }
  .el-dialog__body{
    padding: 10px 10px;
  }
  .el-dialog__footer{
    padding: 10px 10px;
  }
}
.inKindGroupTab{
  font-size: 13px;
  z-index: 0;
  .el-table__row td{
    padding: 2px 5px;
    height: 55px;
  }
  .el-table__header-wrapper{
    .cell{
      font-weight: normal;
      color: rgba(161,161,161,1);
    }
    table{
      tr:hover{
        .closeIcon{
          display: none;
        }
      }
      tr{
        th{
          div{
            //word-break:break-all;
            white-space: normal;
            word-wrap:break-word;
            word-break: keep-all;
          }
        }
      }
    }
  }
}
.tabHeadTH{
  .el-table__header-wrapper{
    table{
      th{
        padding: 0;
      }
    }
  }
  .el-table__body-wrapper{
    .cell{
      //span{
      //  word-break: break-word;
      //}
      div{
        word-break: keep-all;
      }
    }
  }
}
.position-static{
  .el-table{
    position: static;
  }
}
.titleDiaLog{
  .el-dialog__header{
    .el-dialog__title{
      font-weight: normal;
      color: #303133;
    }
  }
  .el-dialog__body{
    padding: 10px 20px;
  }
  .el-dialog__footer{
    padding: 5px 10px 15px 10px;
  }
}
.headerBorder{
  .el-dialog__header{
    border-bottom: 1px solid #eee;
  }
}
.dataPicker{
  .el-main{
    padding: 5px 0;
  }
}
.changeYearQuto{
  .el-dialog__body {
    padding: 10px 20px;
  }
  .el-form-item{
    margin-bottom: 0
  }
}
.editQuto{
  .yearSpan{
    width: 30px;
    font-size: 20px;
    color: #0095C1;
    position: absolute;
    left: 13px;
    top: 17px;
    display: block;
    z-index: 10;
  }
  .editType{
    position:relative;
  }
  .el-input__inner{
    margin: 18px 0 0px 0;
    padding-left:30px;
    font-size:16px;
  }
  .el-input__prefix,.el-input__suffix {
    top: 9px;
  }
}
.settingRateinput {
  .el-input__inner{
    height: 32px;
    width: 100px;
    padding: 0 25px;
  }
  .el-form-item__error {
    top: 43px;
  }
}

.isError {
    .el-input__inner{
      border-color: #f56c6c;
    }
    .el-input__inner:focus{
      border-color: #f56c6c;
    }
  }

.isErrorLength{
  .el-input__suffix{
    .el-input__suffix-inner{
      .el-input__count{
        color: #f56c6c;
      }
    }
  }
  .el-input__count{
    color: #f56c6c;
  }
}
//文本提示
.typesettingPopper.el-popover {
  height:auto;
  background:rgba(255,255,255,1);
  box-shadow:0px 3px 9px 0px rgba(0, 0, 0, 0.3);
  border:1px solid rgba(220,223,230,1);
  opacity:0.97;
  border-radius:6px;
  color:#444444;
  padding:18px;
  h3{
    margin:0;
    height:32px;
  }
  .popper{
    color: #606266;
    clear: both;
    span:first-child{
      width:2%;
    }
    span:last-child{
      width:97%;
    }
  }
  p{
    line-height: 22px;
    text-align: left;
  }
  .popperP{
    height:98px;
    color: #444444;
  }
  button{
    padding:8px 12px;
    color:#fff;
  }
}
.rateSettingPopper.el-popover {
  .popperP{
    height:35px;
  }
}
.perSettingPopper.el-popover {
  .popperP{
    height:53px;
  }
}
.editDecriptMoney{
  width: 245px;
  position: relative;
  .el-input__inner{
    padding-left: 25px;
    font-size: 20px;
  }
  .el-dialog__footer {
      padding:0 20px 20px;
  }
}
.redTip{
  .el-badge__content{
    line-height: 16px;
    min-width: 20px;
    height: 20px;
    padding: 0 4px;
  }
}
.selectYearsBtn{
  width: 110px!important;
  .el-input__inner{
    padding-right: 12px;
  }
}
.inKindPageInation{
  .el-pagination{
    .el-pager li{
      font-size: 15px;
    }
  }
}
.el-table__row:hover  {
  .closeIcon{
    display: block;
  }
}
.hideTabContent{
  .el-tabs__content{
    display: none;
  }
  .el-tabs__item{
    font-size: 16px;
  }
}
.hideDialogHead{
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
  .el-dialog__header{
    display: none;
  }
  .el-dialog__body{
    padding: 0;
  }
}
.el-button--primary.is-disabled,.el-button--primary.is-disabled:active,.el-button--primary.is-disabled:focus,.el-button--primary.is-disabled:hover {
  color: #fff;
  background-color: #6A6A6A;
  border-color: #6A6A6A;
}
.newPoint{
  padding: 0px 6px 2px;
  background-color: #F56C6C;
  color: #fff;
  font-size: 12px;
  border-radius: 10px;
  margin-left:10px;
}

//tab 切换
.el-tabs__item.is-active {
  border-bottom: 2px solid #409eff;
}
.tabs{
  .el-tabs__item.is-active {
    border-bottom: 2px solid #10B3B7;
  }
  .el-tabs__item {
    padding: 0 25px;
    font-size: 20px;
  }
  .el-icon-arrow-left{
    font-size: 20px;
    font-weight: bold;
  }
  .el-icon-arrow-right{
    font-size: 20px;
    font-weight: bold;
  }
  .el-tabs__item.is-top:last-child {
    padding-right: 25px;
  }
  .el-tabs__item.is-top:nth-child(2) {
    padding-left: 25px;
  }
}
.el-tabs__active-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  // background-color: none;
  background-color: #fff;
  z-index: 1;
  transition: transform .3s cubic-bezier(.645,.045,.355,1);
  list-style: none;
  display:none;
}
.showIcon{
  .el-dialog__header{
    .el-dialog__headerbtn{
      display: none;
    }
  }
  .el-dialog__body{
    .closeBtn{
      display: block;
    }
  }
}
.showIcon2{
  .el-dialog__header{
    .el-dialog__headerbtn{
      display: block;
    }
  }
  .el-dialog__body{
    .closeBtn{
      display: none;
    }
  }
}

.el-autocomplete-suggestion{
  z-index:1500 !important;
}
.inkindDownText{
  .el-select-dropdown__item{
    white-space: pre-wrap;
    overflow: auto;
    height: auto;
    min-width: 255px;
  }
}
// 引导页的
.introjs-skipbutton,.introjs-prevbutton{
  display:none !important;
}
.introjs-nextbutton,.introjs-donebutton{
  background: #0095C1 !important;
  color: #fff !important;
  padding: 5px 8px !important;
  text-shadow: 0px 0px 0 #fff !important;
  border-color: #0095C1 !important;
}
.introjs-donebutton{
  display: block !important;
}
.guideBox{
  // position: relative !important;
  width: 355px !important;
  height: 120px;
  max-width: 500px !important;
}
.guideBox2{
  left: -45px;
}
.guidBg{
    position: absolute;
    background: url(../img/inKind/guide.png) no-repeat 0 0;
    width: 430px;
    height: 195px;
    top: -40px;
    left: -10px;
    padding: 60px 20px 0 25px;
}
.guidBg2{
    position: absolute;
    background: url(../img/inKind/guide2.png) no-repeat 0 0;
    width: 430px;
    height: 195px;
    left: -50px;
    top: -40px;
    padding: 56px 30px 0 135px;
}
.guideIcon{
  width: 15px;
  height: 10px;
  display: inline-block;
  margin-right: 5px;
  background: url('../../assets/img/inKind/linkState.png') no-repeat 0 0;
}
.guidBg3{
    padding: 55px 30px 0 125px;
}
.guidBg4{
  padding: 65px 100px 0 25px;
}
.sanjiao{
  left:142px;
}
.buttonRight{
  position: absolute;
  bottom: 31px;
  right: 30px;
}
.buttonRight{
  position: absolute;
  bottom: 14px;
  right: 30px;
}
.buttonleft{
  position: absolute;
  bottom: 30px;
  left: 30px;
}
.inkindTabGuideBox{
  width: 334px !important;
  height: 95px;
  background-color: #fff;
  position: absolute;
  top: 208px;
  left: 44%;
  font-size: 16px;
  color: #494949;
  padding: 16px 16px;
  .introjs-tooltipbuttons{
    .introjs-button{
      width: 60px;
      top: 66px;
      right: 6px;
      position: absolute;
      text-align: center;
    }
  }
}
.inkindTabGuideBox .guidBg{
  position: absolute;
  background: url(../img/inKind/guide1.png) no-repeat 0 0;
  width: 530px;
  height: 250px;
  top: -69px;
  left: -167px;
  .guideSpan{
    position: absolute;
    left: 187px;
    top: 90px;
    width: 291px;
  }
}
.inkindTabGuideBox2{
  width: 334px !important;
  height: 116px;
  background-color: #fff;
  position: absolute;
  top: 208px;
  left: 44%;
  font-size: 16px;
  color: #494949;
  padding: 16px 16px;
  .introjs-tooltipbuttons{
    .introjs-button{
      width: 80px;
      top: 87px;
      right: 17px;
      position: absolute;
      text-align: center;
    }
  }
}
.inkindTabGuideBox2 .guidBg{
  left: -83px;
  position: absolute;
  background: url(../img/inKind/guide2.png) no-repeat 0 0;
  width: 430px;
  height: 195px;
  top: -26px;
  .guideSpan{
    position: absolute;
    left: 121px;
    top: 50px;
    width: 277px;
  }
}
.hoverApproval{
  text-align: left;
}
.inputLeft{
  .el-form-item__label {
    font-size: 16px;
  }
}
.inkindTableFont{
  .el-table__header-wrapper {
    .cell{
      font-size: 14px;
    }
  }

  .el-table__body-wrapper {
    .cell{
      font-size: 14px;
    }
  }
}

// 鼠标放上显示
.descriptTooltip {
  font-size: 16px;
}

.zoomInImg {
  .el-dialog__header {
    padding:0;
  }

  .el-dialog__body {
    padding:0;
  }
}

#pendingList th td,#reviewedList th td {
  border-top: 0;
}
#pendingList td, #reviewedList td {
  border-bottom: 0;
  border-top: 1px solid #ebeef5;
  color: #2E2E2E;
}
#pendingList .el-table__expanded-cell, #reviewedList .el-table__expanded-cell {
  padding: 4px;
  border-top: 0;
  // color: #9C9C9C;
}
#pendingList .el-table__expanded-cell td {
  border-top: 0;
  padding-left: 62px;
}
#reviewedList .el-table__expanded-cell td {
  border-top: 0;
  padding-left: 15px;
}
.el-table--enable-row-hover .el-table__body tr:hover>td {
  background-color: #fff;
}

.ratingPeriodSelectYear{
  width: 500px;
  margin: 20px auto 0;
  ul {
    border-bottom: 0!important;
    .el-submenu__title {
      font-size: 24px;
      //font-weight: 700;
      margin-left: -18%;
      color: #111C1CFF!important;
      border-bottom: 0!important;
    }
    .el-menu--horizontal{
      color: red!important;
    }
    li {
      margin-left: 30%;
    }
  }
}

.questionnaire-select {
  white-space: inherit;
  .el-checkbox__label {
    color:#606266 !important;
    font-size: 16px!important;
 }
  .el-radio__label {
    font-size: 16px!important;
    color:#606266 !important;
  }
  .el-radio__input {
    vertical-align: top;
    .el-radio__inner{
      height: 16px;
      width: 16px;
    }
  }
}

.rating-period-select {
  white-space: inherit;
  .el-radio__label {
    font-size: 18px!important;
  }
  .el-radio__input {
    vertical-align: top;
    .el-radio__inner{
      height: 18px;
      width: 18px;
    }
  }
}

.rating-period-select2 {
  .el-radio__label {
    font-size: 22px!important;
  }
  .el-radio__input {
    vertical-align: top;
    .el-radio__inner{
      height: 22px;
      width: 22px;
    }
  }
}
.el-tabs__item.is-active {
  border-bottom:2px solid #10B3B7;
}
.ratingPeriodTab {
  .el-tabs__header {
    background: #ecf0f1;
    padding: 0 2%;
    .el-tabs__nav {
      .el-tabs__item {
        font-size: 20px!important;
        color: black;
      }
    }
  }
}

.ratingPeriodTab2 {
  .el-tabs__header {
    .el-tabs__nav {
      .el-tabs__item {
        font-size: 16px !important;
      }
    }
  }
  .el-tabs__item:hover {
    background-color: #10B3B7;
    cursor: pointer;
    color: #ffffff;
  }

}

.ratingPeriodPreview {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 0 20px;
  }
  .el-dialog__footer {
    text-align: center;
    padding: 0 20px 16px;
  }
}

.input-error {
  .el-input__inner {
    border-color: #f00!important;
  }
}

.set-transfer-policy {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  .el-radio__input {
    float: left;
    display: inline-block;
  }
  .el-radio__label {
    float: left;
    display: inline-block;
    width: 80%;
  }
}

.period-perview {
  .el-dialog {
    margin-top: 24px!important;
  }
  .el-dialog__header {
    text-align: center;
    padding: 16px 20px;
    .el-dialog__title {
      font-size: 22px;
    }
  }
  .el-dialog__body {
    background: #f0f3f3;
    padding: 8px 20px;
  }
  .el-dialog__footer {
    padding: 10px 20px;
  }
}

.clear-dialog-padding {
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #E5E5E5;
  }
  .el-dialog__body {
    padding: 8px 20px;
  }
  .el-dialog__footer {
    border-top: 1px solid #E5E5E5;
    padding: 10px 20px;
  }
}

.clear-dialog-padding-no-bottom {
  .el-dialog__header {
    padding: 16px 20px 5px 20px;
  }
  .el-dialog__body {
    padding: 8px 20px;
  }
  .el-dialog__footer {
    padding: 10px 20px;
  }
}

.add-dialog-title {
  .el-dialog__header {
    .el-dialog__title {
      font-size: 22px;
    }
  }
}

.setCutOffDialog {
  .el-dialog__footer {
   line-height: 42px;
  }
}

.select-type {
  background: #10B3B7!important;
  color: #fff;
  .el-radio__label {
    color: #fff!important;
  }
}

.video-dialog-padding {
  .el-dialog__body {
    padding: 8px 40px;
  }
}

.period-picker {
    line-height: 10px!important;
}

.edit-period-dialog {
  .el-dialog {
    margin-top: 40px!important;
  }
}

.deletePeriodIcon {
  .el-message-box__content {
    .el-message-box__status {
      display: none;
    }
    .el-message-box__message {
      padding-left: 12px!important;
    }
  }
}

.periodSearch {
  .el-input__inner{
    border-radius: 20px;
  }
}

.period-popper {
  max-width: 400px;
  font-size: 14px;
}

.showEditPeriodName {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}

.addNewYearTip {
  .el-dialog__body {
    padding: 15px 20px;
  }
  .el-dialog__footer {
    text-align: center;
  }
}
.inkind-name{
  .el-input__inner{
    padding-right: 70px!important;
  }
  .el-textarea__inner{
    padding: 5px 50px 5px 10px!important;
  }
}
.inputError{
   .el-textarea__inner{
    border-color: #f56c6c !important;
  }
}
.editSelectGroup {
  .el-checkbox__label {
    vertical-align: text-top;
  }
}

.haveClassNoSet {
  .el-dialog__header {
    .el-dialog__title {
      color: red!important;
    }
  }
  .el-dialog__body {
    padding: 10px 20px!important;
  }
}

.stepBox {
  .el-steps--horizontal {
    width: 820px;
    margin: 16px auto 0;
    .el-step__head.is-process {
      color: #13B6B4;
      border-color: #13B6B4;
    }
    .el-step__title.is-process {
      font-weight: 700;
      color: #13B6B4;
    }
  }
}

.periodSelectGroup {
  .el-checkbox__input {
    vertical-align: super;
  }
  .el-checkbox__label {
    vertical-align: text-bottom;
  }
}
.checkBokCursor {
  .el-checkbox__labe{
    color: #c0c4cc!important;
    cursor: pointer!important;
  }
}

.collapseTilte {
  div{
    .el-collapse-item__header{
      background-color: #f6f8f8;
      border-radius: 9px;
      border: 0px solid #ebeef5;
      margin-top: 5px;
    }
  }
  .el-collapse-item__wrap{
    border: 0px solid #ebeef5;
  }
}

.datePicker {
  .el-range-separator{
    padding: 0;
  }
}

.card-collapse{
  border-top: 0px solid #ebeef5;
  border-bottom: 0px solid #ebeef5;
  .el-collapse-item__wrap{
    border-bottom: 0px solid #ebeef5;
  }
  .el-collapse-item__header{
    border-bottom: 0px solid #ebeef5;
  }
  .el-collapse-item__content {
    padding-bottom: 0px;
  }
}
.table-scrollbar{
  .el-table__body-wrapper::-webkit-scrollbar { /*滚动条整体样式*/
      height: 10px; /*高宽分别对应横竖滚动条的尺寸*/
      width: 10px;
    }
    .el-table__body-wrapper::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #D8D8D8;
    }
    .el-table__body-wrapper::-webkit-scrollbar-track { /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      background: #EDEDED;
    }
}

.edit-dialog {
  .el-dialog {
    border-radius: 5px;
  }
  .el-dialog__body{
    padding: 10px 20px;
  }
  .el-dialog__header {
    padding: 15px 15px;
    border-bottom: 1px solid #E5E5E5;
    .el-dialog__title{
      color: #111C1C;
      font-size: 20px;
      font-weight: 500;

    }
  }
}
.confirmation-dialog{
  .el-dialog {
    border-radius: 5px;
  }
  .el-dialog__body{
    padding: 10px 20px;
  }
  .el-dialog__header {
    padding: 15px 15px;
    // border-bottom: 1px solid #E5E5E5;
    .el-dialog__title{
      color: #111C1C;
      font-size: 20px;
      font-weight: 500;
    }
  }
}
.select-dialog{
  .el-dialog {
    border-radius: 5px;
  }
  .el-dialog__body{
    padding: 0px 20px;
  }
  .el-dialog__footer {
    padding: 5px 15px 10px 15px;
  }
  .el-dialog__header {
    padding: 15px 15px;
    // border-bottom: 1px solid #E5E5E5;
    .el-dialog__title{
      color: #111C1C;
      font-size: 20px;
      font-weight: 500;
    }
  }
}
.info-dialogInfo{
  .el-dialog{
    border-radius: 10px;
  }
  .el-dialog__header{
    padding: 0px;
  }
  .el-dialog__body{
    padding: 15px 10px;
  }
  .el-dialog__footer {
    border-top: 1px solid #E5E5E5;
    padding: 10px 15px;
  }
}


.alert-dialog {
  .el-dialog {
    border-radius: 5px;
    overflow:hidden;
  }
  .el-dialog__header {
    padding: 15px 15px;
    border-bottom: 1px solid #E5E5E5;
    background-color: #f2dede;
    .el-dialog__title{
      color: #111C1C;
      font-size: 20px;
      font-weight: 500;
    }
  }
  .el-dialog__body {
    padding: 8px 15px;
  }
  .el-dialog__footer {
    border-top: 1px solid #E5E5E5;
    padding: 10px 15px;
  }
}

.bigSelect{
  .el-checkbox{
    margin-top: 4px;
  }
  .el-checkbox__inner{
    height: 16px;
    width: 16px;
  }
  .el-checkbox__inner::after{
    font-size: 18px;
    left: 5px;
    top: 2px;
  }
  .is-indeterminate .el-checkbox__inner::before{
    top: 6px;
  }
}
.maxSelect{
  .el-checkbox__inner{
    height: 20px;
    width: 20px;
  }
  .el-checkbox__inner::after{
    left: 7px;
    top: 4px;
  }
}
.borderSelect{
  .el-checkbox__inner{
    border: 1px solid #8a8a8a;
  }
}
.input-prepend{
  .el-input-group__prepend {
    background-color: #ffffff;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 0px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 10px;
    width: 1px;
    white-space: nowrap;
}
}
.el-progress-1{
  margin-left:30px;
}
.el-dialog-period{
  .el-dialog {
    border-radius: 5px;
    overflow:hidden;
  }
  .el-dialog__header {
    padding: 15px 15px;
  }
  .el-dialog__body {
    padding: 10px 10px;
  }
}
  .tabspanel {
    .el-tabs__header {
      .el-tabs__nav-wrap{
        .el-tabs__nav-scroll{
            .el-tabs__nav{
              .el-tabs__item:nth-child(2) {
              padding-left: 20px !important;
            }
          }
        }
    }
  }
}

.el-dialog{
  border-radius: 8px;
}
.sbButton.el-button {
  background: #fff;
  border-color: #10B3B7;
  color: #10B3B7;
}
.sbButton.el-button:focus, .sbButton.el-button:hover {
  color: #10B3B7;
  background: #e7f7f8;
  border-color: #10B3B7;
}
.selectMennu{
  color: #40c2c5;
  background: #e7f7f8;
}
// Form 表单后置必选
.required {
  .el-form-item__label:after {
    content: " *";
    color: red;
  }
}
.el-upload__input{
  display: none !important;
}
.uploadPhoto{
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 110px;
  }
  .el-upload-list__item{
    width: 100px;
    height: 100px;
    div{
      width: 100%;
      height: 100%;
    }
  }
}
.notUpload{
  .el-upload{
    display: none !important;
  }
}
.dialogForm {
  margin-bottom: 5px;
  .el-form-item__content {
    .el-form-item__error {
      position: static !important;
    }
  }
}
.pagination .el-pagination.is-background .btn-next,
.pagination .el-pagination.is-background .btn-prev,
.pagination .el-pagination.is-background .el-pager li {
  margin: 0px;
  background-color: #ffffff;
  color: #606266;
  min-width: 30px;
  border-radius: 2px;
  padding: 0 6px;
  border: 1px solid #dbdbdb;
}
.hideTabsContent{
  .el-tabs__content{
    display: none;
  }
  .el-tabs__nav-wrap::after {
    width:0%
  }
}
.tabs{
  .el-tabs__header{
    margin: 0;
  }
  .el-tabs__content{
    padding-top: 15px;
    border: 1px solid #E4E7ED;
      border-top: none;
  }
}
.ellipsis{
  .cell{
     white-space: nowrap;
  }
}

.hideHeadTable {
  .el-table__header-wrapper {
    display: none;
  }
}
.hideEmptyTable{
  .el-table__empty-block{
    display: none;
  }
}

.top-left-triangle{
  width: 0;
  height: 0;
  border-width: 0 0 12px 12px ;
  border-style: solid;
  position: absolute;
  left: 0px;
  z-index: 1000;
}
.color_fdb900{
  border-color: transparent transparent transparent #fdb900;
}

.color_433dff{
  border-color: transparent transparent transparent #433dff;
}

.color_706ecb{
  border-color: transparent transparent transparent #706ecb;
}

.color_fdb900{
  border-color: transparent transparent transparent #fdb900;
}

.color_ec726f{
  border-color: transparent transparent transparent #ec726f;
}
.color_e37fd7{
  border-color: transparent transparent transparent #e37fd7;
}
.color_38b2b8{
  border-color: transparent transparent transparent #38b2b8;
}
.color_91ca22{
  border-color: transparent transparent transparent #91ca22;
}

.new-font-color{
  color:#323338
}
//图像缩放
.img-arithmetic{
  image-rendering: -webkit-optimize-contrast;
}
.myTooltip{
  width: 250px !important;
}
.icon {
  color: #fff
}

// 提示框
.lg-message-box {
  .el-message-box__message {
    font-size: 16px;
  }
  .el-message-box__btns span {
    font-size: 14px;
  }
}

/* 周计划confirm提示框 */
.custom-message-box {
  .el-message-box__title {
    font-size: 20px;
    font-weight: bold;
  }
  .el-message-box__message {
    font-size: 16px;
  }
  .el-message-box__btns span {
    font-size: 14px;
  }
}

.modal-p-x {
  .el-dialog__body {
    padding: 0 20px;
  }
}

.lg-el-dialog {
  .el-dialog__body {
    font-size: 16px;
    padding: 0 20px;
  }
}

/* 没有头部的弹窗 */
.no-header-dialog .el-dialog__header {
  padding: 0;
}
.no-header-dialog .el-dialog__headerbtn {
  z-index: 10;
}

/* 没有边距的弹窗 */
.no-body-border-dialog .el-dialog__body {
  padding: 0;
}

.dialog-h-80 .el-dialog {
  height: 80%;
}

.dialog-content-h-full {
  .el-dialog {
    display: flex;
    flex-direction: column;
    .el-dialog__body {
      height: 100%;
      min-height: 0;
    }
  }
}

.full-dialog {
  display: flex;
  padding: 20px;
  .el-dialog {
    height: 90vh;
    width: 90%;
    margin: auto !important;
    display: flex;
    flex-direction: column;
    .el-dialog__body {
        flex: auto;
        min-height: 0;
        display: flex;
        flex-direction: column;
        .no-shrink {
            flex: auto;
            min-height: 0;
        }
    }
  }
}

.error-input {
  .el-textarea__inner, .el-input__inner {
    border-color: #F05050 !important;
  }
}

.text-button-danger {
  i, i:hover, span, span:hover {
    color: #ee3939;
  }
}

// 图片预览关闭按钮
.el-image-viewer__close {
  font-size: 24px;
  color: #fff;
  background-color: #606266;
}

// 视频播放弹窗
.video-modal {
  position: fixed;
  top: 35%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 0px 0px rgba(0,0,0,.3);
  box-sizing: border-box;
  background: #00000000;
  -webkit-box-sizing: border-box;
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    text-align: center;
    padding: 0;
  }
  .el-dialog__headerbtn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    opacity: .8;
    top: 45px;
    right: 45px;
    position: fixed;
    background-color: #606266;
    font-size: 24px;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
}

// 语音播放组件
.audio-item{
  width:115px;
  height:30px;
  background:rgba(19,182,180,1);
  border:1px solid rgba(19,182,180,1);
  border-radius:3px;
  padding: 5px 15px;
}
.audio-item span{
  color: #fff;
  display: inline-block;
  vertical-align: top;
  margin-left: 10px;
  line-height: 16px;
}
.audio-img{
  width: 14px;
  height: 19px;
  overflow: hidden;
  background: url("../img/lesson2/plan/audio_img.png") no-repeat -28px 0;
  display: inline-block;
  vertical-align: top;
}
.audio-img-run{
  animation:run 800ms steps(3,end) infinite;
  -webkit-animation:run 800ms steps(3,end) infinite;
}
@keyframes run{
  0% {
      background-position:0;
  }
  100% {
  background-position:-42px 0;
  }
}

.interpretation-tooltip {
  max-width: 250px;
}

// DRDP 图标
.drdp-report {
  h2 {
    font-size: 24px;
    line-height: 28px;
    font-weight: 500;
    margin: 0 0 20px 0;
    padding: 0;
  }

  h2[class*=icon-] {
    background-size: 30px 26px !important;
    background-position: 0 0 !important;
    background-repeat: no-repeat !important;
    padding-left: 45px !important;
    padding-bottom: 4px !important;
  }

  .icon-atl-reg {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-atl-reg-ce7acb14aa94ff803a6798f0fea54285864da0a3ee330a07028f6654a35ab5bb.svg)!important
  }

  .icon-cog {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-cog-4c0272dd1faf4557549459d4853cf509f0e794bda5e084c6603914ccab0484cb.svg)!important
  }

  .icon-math {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-cog-4c0272dd1faf4557549459d4853cf509f0e794bda5e084c6603914ccab0484cb.svg)!important
  }

  .icon-sci {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-cog-4c0272dd1faf4557549459d4853cf509f0e794bda5e084c6603914ccab0484cb.svg)!important
  }

  .icon-eld {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-eld-25358f82fa2c4df619dee77d8d2b50f7548961a7d6d63b118e9384b0b19347ea.svg)!important
  }

  .icon-hss {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-hss-c76b7f4143ec63ed2001286603d216387b49243ed40ba98c2d03350a9bb72c17.svg)!important
  }

  .icon-lld {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-lld-74ec2af97c350567aca1b4292e76b51dcbc9b0919c97ff131d255ebac28bd7fc.svg)!important
  }

  .icon-lit {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-lld-74ec2af97c350567aca1b4292e76b51dcbc9b0919c97ff131d255ebac28bd7fc.svg)!important
  }

  .icon-lang {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-lld-74ec2af97c350567aca1b4292e76b51dcbc9b0919c97ff131d255ebac28bd7fc.svg)!important
  }

  .icon-pd-hlth {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-pd-hlth-3aaebbc873e174799f84638934f485c63832c036a41f0e0ce7d7224d180ec2d9.svg)!important
  }

  .icon-pd {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-pd-hlth-3aaebbc873e174799f84638934f485c63832c036a41f0e0ce7d7224d180ec2d9.svg)!important
  }

  .icon-hlth {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-hlth-fd0c24f68d805e9c2993da75b50fe2992ba22d9a4d3e1cfc98b3b45bf7ddf490.svg)!important
  }

  .icon-sed {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-sed-73b26fa8bcdba7da2b686164cce6cf14ceb650ba745cbd2d8e009a8ed492cc47.svg)!important
  }

  .icon-vpa {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-vpa-7db23f6be45804cb7b431a3610ce281ba180e3faedd85bbd4e8829a110e6c1d3.svg)!important
  }

  .icon-ssd {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-ssd-73b26fa8bcdba7da2b686164cce6cf14ceb650ba745cbd2d8e009a8ed492cc47.svg)!important
  }

  .icon-span {
    background-image: url(https://d2urtjxi3o4r5s.cloudfront.net/drdp-assets/icon-span-14e50a27b637e0dcf8ad371d46acc9eda689be9f55e324cea8fee37e79ad7d58.svg)!important
  }
}

// 按钮样式
.btn-bord-primary, .btn-bord-primary:hover {
  border: 1px solid #10B3B7;
  color: #10B3B7;
}

// 平铺树状图样式
.tile-tree > {
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > {
    display: flex;
    flex-wrap: wrap;
    padding-left: 36px;
    .el-tree-node > {
      width: 33.33%;
      padding-top: 5px;
      .el-tree-node__content {
        padding-left: 0 !important;
        cursor: default;
      }
    }
  }
  .el-tree > .el-tree-node > .el-tree-node__content {
    cursor: default;
  }
  .el-tree > .el-tree-node > .el-tree-node__content > .el-tree-node__expand-icon {
    display: none;
  }
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__content > {
    padding-top: 10px;
  }
}

.tile-tree {
  font-size: 15px;
  .el-checkbox {
    margin-bottom: 0;
  }
  .el-tree-node__content:hover, .el-tree-node:focus>.el-tree-node__content {
    background-color: inherit;
  }
}

// 平铺树状图样式
.tile-tree-2 > {
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > {
    padding-left: 18px;
  }
  
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children >{
    padding-left: 18px;
  }

  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > {
    display: flex;
    flex-wrap: wrap;
    padding-left: 18px;
    .el-tree-node > {
      width: 33.33%;
      // padding-top: 5px;
      .el-tree-node__content {
        padding-left: 0 !important;
        cursor: default;
      }    
    }
  }
  .el-tree > .el-tree-node > .el-tree-node__content {
    cursor: default;
  }
  .el-tree > .el-tree-node > .el-tree-node__content > .el-tree-node__expand-icon {
    display: none;
  }
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__content > {
    padding-top: 10px;
  }
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__content > {
    padding-top: 10px;
  }
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__content > {
    padding-top: 10px;
  }
}

.tile-tree-2 {
  font-size: 15px;
  .el-checkbox {
    margin-bottom: 0;
  }
  .el-tree-node__content:hover, .el-tree-node:focus>.el-tree-node__content {
    background-color: inherit;
  }
}

.program-li-a {
  padding-right: 40px !important;
  display: block;
}
.program-margin-right {
  padding-right: 24px !important;
  display: block;
}

.no-outline-btn, .no-outline-btn:focus {
  outline: none !important;
}

.new-filter-btn {
  color: #10B3B7 !important;
}
.new-filter-btn:hover {
  color: #0e9ca0 !important;
}
.eidt-filter-icon {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
}
.confirm-dialog {

  .el-message-box__header {
    padding: 20px;
    padding-bottom: 10px;

    .el-message-box__title {
      height: 15px;
      color: #333333;
      font-size: 20px;
    }
  }

  .el-message-box__message {
    color: #606266;
    padding: 0px 5px;
    font-size: 16px
  }

  .el-message-box__btns {
    padding-bottom: 5px;

    .el-button--small {
      padding: 10px 15px;
      font-size: 14px;
      border-radius: 4px;
    }
  }
}

.el-picker-panel {
  border-radius: 8px;
}
/* 隐藏时间选择器小尖角 */
.el-popper[x-placement^=bottom] .popper__arrow::after {
  display: none;
}

.el-popper[x-placement^=bottom] .popper__arrow {
  display: none;
}
.el-picker-panel__sidebar {
  width: max-content;
}
.batch-distance-learning {
  .el-form-item__label{
    color: #111C1C
  }
  .el-radio-button__inner {
    color: #111C1C
  }
}

.lg-table-cell-break-word {
  .cell {
    word-break: break-word !important;
  }
}

// 没有 toolbar 的富文本编辑器，并且 font-size 为 14px，浮动在上方的时候，边框颜色为主题色
.lg-no-toolbar-editor .quill-editor {
  line-height: normal;
  height: 100%;

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.42 !important;
  }

  // 隐藏功能栏
  .ql-toolbar {
    display: none !important;
  }

  .ql-container {
    font-size: 14px !important;
    border: 1px dashed transparent !important;
    resize: none !important;
    overflow: hidden !important;
    border-radius: 4px !important;
  }

  .ql-editor {
    border: 1px dashed transparent !important;
  }

  .ql-editor:hover {
    border: 1px dashed var(--color-inner-dashed-border) !important;
  }

  .ql-editor:focus {
    border: 1px dashed var(--color-primary) !important;
  }

  .ql-editor {
    overflow-y: auto;
    min-height: 33px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
    &.ql-blank:before {
      font-style: normal;
    }
  }

  /* 修改富文本编辑器的placeholder的默认颜色：字体 */

  .ql-editor[data-placeholder]:before {
    /* 此处设置 placeholder 样式 */
    font-style: normal;
    font-size: 14px;
  }
  .ql-editor ol, .ql-editor ul {
    padding-left: 0px !important;
  }

  .ql-editor ol li:not(.ql-direction-rtl), .ql-editor ul li:not(.ql-direction-rtl) {
    padding-left: 0px !important;
  }
}

.lesson-download-pdf {
  //使下拉弹出框左移
  transform: translateX(-80px);
  border-radius: 4px;

  //设置下拉框选项样式
  .el-select-dropdown__item {
    height: 40px;
    width: 281px;
    // width: 278px;
    padding: 10px;
    gap: 10px;
  }

  .el-select-dropdown__item:hover {
    color:#000 !important;
  }

  .el-dropdown-menu__item:hover {
    color:#000 !important;
  }

  .el-select-dropdown__list {
    width: 294px;
    // height: 209px;
    padding: 8px;
    gap: 4px;
  }
}

.width-600-confirm-message-box {
  width: 650px;
  .el-message-box__content {
    width: 650px;
  }
}

.width-418-confirm-message-box {
  width: 418px;
  .el-message-box__content {
    width: 418px;
  }
}

.unit-planer-unitInfoFormSelect {
  .el-scrollbar__thumb {
    background-color: #c5c7d0 !important;
    width: 5px !important;
  }

  // 去除横向滚动条
  .el-scrollbar__bar.is-horizontal {
    display: none !important;
  }
}
.unit-planer-unitInfoFormSelect-width{
  width: 22%;
}
.lesson-plan-ideas-popover{
  background-color: #878BF9 !important;
  border-radius: 8px;
  padding: 24px;
  z-index: 1900 !important;
  .popper__arrow {
    top: -5px !important;
    display: unset !important;
    border-bottom-color: #878BF9 !important;
  }
}

.entried-card-popover .popper__arrow{
  display: block !important;
  border-bottom-color: #fff !important;
}

.notification_top {
  z-index: 2000;
  width: auto;
  align-items: center;
  background-color: #F0F9EB;
  max-width: 700px;
}

.notification_top_center {
  position: fixed;
  left: calc(50vw - 350px) !important;
  z-index: 2000;
  width: auto;
  align-items: center;
  background-color: #F0F9EB;
  max-width: 700px;
}

.differentiation-content ul {
  padding-left: 0px !important;
  height: fit-content;
  display: block;
  margin: 0 !important;
  line-height: 1.5 !important; /* 可根据需要调整行间距 */
}

.differentiation-content p, .differentiation-content li {
  padding: 0;
  margin: 0;
}
.curriculum-popover {
  padding: 0;
}
.max-height-300 {
  max-height: 300px;
}

.el-tag {
  transition: none;
}
