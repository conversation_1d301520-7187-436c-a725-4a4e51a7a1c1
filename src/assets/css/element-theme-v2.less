/* 全局颜色变量 */
@color-primary: #10b3b7;
@color-primary-light: #ddf2f3;
@color-primary-opacity: #10b3b766;
@color-success: #67c23a;
@color-success-light: #96c13d;
@color-success-opacity: #67c23a66;
@color-warning-dark: #ff7f41;
@color-warning-dark-opacity: #ff7f4166;
@color-warning: #e6a23c;
@color-warning-opacity: #e6a23c66;
@color-danger: #f56c6c;
@color-danger-opacity: #f56c6c66;
@color-info: #909399;
@color-info-opacity: #90939966;
@color-text-primary: #111C1C;
@color-text-primary-opacity: #111C1C66;
@color-text-secondary: #323338;
@color-text-disabled: #32333866;
@color-text-placeholder: #676879;
@color-text-placeholder-opacity: #67687966;
@color-border: #dcdfe6;
@color-border-opacity: #dcdfe666;
@color-white: #ffffff;
@color-gray: #e6e9f1;
@color-gray-opacity: #e6e9f166;
@color-gray-white:#ebeef5;
@color-gray-white-opacity:#ebeef566;
@color-page-background-white: #f5f6f8;
@color-table-header-background: #fafafa;
@color-step-divider: #c5c7d0;
@color-element-disabled: #c0c4cc;


/* 全文Inter字体 */
body {
  font-family: Inter !important;
  color: @color-text-primary;
  font-feature-settings: 'tnum' on, 'lnum' on;
}

/* 常用字体 */
.title-font-24 {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
}

.title-font-20 {
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
}

.title-font-18 {
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

.title-font-18-regular {
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
}

.title-font-16-bold {
  font-size: 16px;
  font-weight: 800;
  line-height: 24px;
}

.title-font-16 {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.title-font-16-regular {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.title-font-14 {
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}

.title-font-14-regular {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.title-font-14-bold {
  font-size: 14px;
  font-weight: 800;
  line-height: 22px;
}

.title-font-12 {
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
}

.title-font-12-regular {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

.font-weight-600 {
 font-weight: 600;
}

/* 常用间距 */
/* 常用的 margin */
.lg-no-margin {
  margin: 0px !important;
}
.lg-margin-4 {
  margin: 4px;
}

.lg-margin-8 {
  margin: 8px;
}

.lg-margin-12 {
  margin: 12px;
}

.lg-margin-16 {
  margin: 16px;
}

.lg-margin-20 {
  margin: 20px;
}

.lg-margin-24 {
  margin: 24px;
}

.lg-margin-32 {
  margin: 32px;
}

.lg-margin-left-4 {
  margin-left: 4px;
}

.lg-margin-left-8 {
  margin-left: 8px;
}

.lg-margin-left-12 {
  margin-left: 12px;
}

.lg-margin-left-16 {
  margin-left: 16px;
}

.lg-margin-left-20 {
  margin-left: 20px;
}

.lg-margin-left-24 {
  margin-left: 24px;
}

.lg-margin-left-32 {
  margin-left: 32px;
}

.lg-margin-right-4 {
  margin-right: 4px;
}

.lg-margin-right-8 {
  margin-right: 8px;
}

.lg-margin-right-12 {
  margin-right: 12px;
}

.lg-margin-right-16 {
  margin-right: 16px;
}

.lg-margin-right-20 {
  margin-right: 20px;
}

.lg-margin-right-24 {
  margin-right: 24px;
}

.lg-margin-right-32 {
  margin-right: 32px;
}

.lg-margin-top-2 {
  margin-top: 2px;
}

.lg-margin-top-4 {
  margin-top: 4px;
}

.lg-margin-top-8 {
  margin-top: 8px;
}

.lg-margin-top-12 {
  margin-top: 12px;
}

.lg-margin-top-16 {
  margin-top: 16px;
}

.lg-margin-top-20 {
  margin-top: 20px;
}

.lg-margin-top-24 {
  margin-top: 24px;
}

.lg-margin-top-32 {
  margin-top: 32px;
}

.lg-margin-bottom-0 {
  margin-bottom: 0 !important;
}

.lg-margin-bottom-4 {
  margin-bottom: 4px;
}

.lg-margin-bottom-8 {
  margin-bottom: 8px;
}

.lg-margin-bottom-12 {
  margin-bottom: 12px;
}

.lg-margin-bottom-16 {
  margin-bottom: 16px;
}

.lg-margin-bottom-20 {
  margin-bottom: 20px;
}

.lg-margin-bottom-24 {
  margin-bottom: 24px;
}

.lg-margin-bottom-32 {
  margin-bottom: 32px;
}

.lg-margin-l-r-4 {
  margin-left: 4px;
  margin-right: 4px;
}

.lg-margin-l-r-8 {
  margin-left: 8px;
  margin-right: 8px;
}

.lg-margin-l-r-12 {
  margin-left: 12px;
  margin-right: 12px;
}

.lg-margin-l-r-16 {
  margin-left: 16px;
  margin-right: 16px;
}

.lg-margin-l-r-20 {
  margin-left: 20px;
  margin-right: 20px;
}

.lg-margin-l-r-24 {
  margin-left: 24px;
  margin-right: 24px;
}

.lg-margin-l-r-32 {
  margin-left: 32px;
  margin-right: 32px;
}

.lg-margin-t-b-4 {
  margin-top: 4px;
  margin-bottom: 4px;
}

.lg-margin-t-b-8 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.lg-margin-t-b-12 {
  margin-top: 12px;
  margin-bottom: 12px;
}

.lg-margin-t-b-16 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.lg-margin-t-b-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.lg-margin-t-b-24 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.lg-margin-t-b-32 {
  margin-top: 32px;
  margin-bottom: 32px;
}

/* 常用的 padding */
.lg-no-padding {
  padding: 0px !important;
}

.lg-padding-4 {
  padding: 4px;
}

.lg-padding-8 {
  padding: 8px;
}

.lg-padding-12 {
  padding: 12px;
}

.lg-padding-16 {
  padding: 16px !important;
}

.lg-padding-20 {
  padding: 20px;
}

.lg-padding-24 {
  padding: 24px;
}

.lg-padding-32 {
  padding: 32px;
}

.lg-padding-left-4 {
  padding-left: 4px;
}

.lg-padding-left-8 {
  padding-left: 8px;
}

.lg-padding-left-12 {
  padding-left: 12px;
}

.lg-padding-left-16 {
  padding-left: 16px;
}

.lg-padding-left-20 {
  padding-left: 20px;
}

.lg-padding-left-24 {
  padding-left: 24px;
}

.lg-padding-left-32 {
  padding-left: 32px;
}

.lg-padding-right-4 {
  padding-right: 4px;
}

.lg-padding-right-8 {
  padding-right: 8px;
}

.lg-padding-right-12 {
  padding-right: 12px;
}

.lg-padding-right-16 {
  padding-right: 16px;
}

.lg-padding-right-20 {
  padding-right: 20px;
}

.lg-padding-right-24 {
  padding-right: 24px;
}

.lg-padding-right-32 {
  padding-right: 32px;
}

.lg-padding-top-4 {
  padding-top: 4px;
}

.lg-padding-top-8 {
  padding-top: 8px;
}

.lg-padding-top-12 {
  padding-top: 12px;
}

.lg-padding-top-16 {
  padding-top: 16px;
}

.lg-padding-top-20 {
  padding-top: 20px;
}

.lg-padding-top-24 {
  padding-top: 24px;
}

.lg-padding-top-32 {
  padding-top: 32px;
}

.lg-padding-bottom-4 {
  padding-bottom: 4px;
}

.lg-padding-bottom-8 {
  padding-bottom: 8px;
}

.lg-padding-bottom-12 {
  padding-bottom: 12px;
}

.lg-padding-bottom-16 {
  padding-bottom: 16px;
}

.lg-padding-bottom-20 {
  padding-bottom: 20px;
}

.lg-padding-bottom-24 {
  padding-bottom: 24px;
}

.lg-padding-bottom-32 {
  padding-bottom: 32px;
}

.lg-padding-l-r-4 {
  padding-left: 4px;
  padding-right: 4px;
}

.lg-padding-l-r-8 {
  padding-left: 8px;
  padding-right: 8px;
}

.lg-padding-l-r-12 {
  padding-left: 12px;
  padding-right: 12px;
}

.lg-padding-l-r-16 {
  padding-left: 16px;
  padding-right: 16px;
}

.lg-padding-l-r-20 {
  padding-left: 20px;
  padding-right: 20px;
}

.lg-padding-l-r-24 {
  padding-left: 24px;
  padding-right: 24px;
}

.lg-padding-l-r-32 {
  padding-left: 32px;
  padding-right: 32px;
}

.lg-padding-t-b-2 {
  padding-top: 2px;
  padding-bottom: 2px;
}

.lg-padding-t-b-4 {
  padding-top: 4px;
  padding-bottom: 4px;
}

.lg-padding-t-b-8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.lg-padding-t-b-12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.lg-padding-t-b-16 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.lg-padding-t-b-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.lg-padding-t-b-24 {
  padding-top: 24px;
  padding-bottom: 24px;
}

.lg-padding-t-b-32 {
  padding-top: 32px;
  padding-bottom: 32px;
}

/* 圆角 */
.lg-border-radius-4 {
  border-radius: 4px;
}

.lg-border-top-left-radius-4 {
  border-top-left-radius: 4px;
}

.lg-border-top-right-radius-4 {
  border-top-right-radius: 4px;
}

.lg-border-bottom-left-radius-4 {
  border-bottom-left-radius: 4px;
}

.lg-border-bottom-right-radius-4 {
  border-bottom-right-radius: 4px;
}

.lg-border-radius-8 {
  border-radius: 8px;
}

.lg-border-top-left-radius-8 {
  border-top-left-radius: 8px;
}

.lg-border-top-right-radius-8 {
  border-top-right-radius: 8px;
}

.lg-border-bottom-left-radius-8 {
  border-bottom-left-radius: 8px;
}

.lg-border-bottom-right-radius-8 {
  border-bottom-right-radius: 8px;
}

/* 投影 */
.lg-box-shadow, .lg-box-shadow-hover {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025)
}

/* 选中投影 */
.lg-box-shadow-hover:hover, .lg-box-shadow-hover:focus, .lg-box-shadow-hover:active {
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.2);
}

/* 带有下划线的超链接 */
.lg-link-under-line {
  text-decoration: underline !important;
  font-weight: 600;
  color: @color-primary !important;
}

/* 超链接 */
.lg-link > a {
  text-decoration: none !important;
  color: @color-primary !important;
}

/* 常用的颜色 */
/* 主色 */
.lg-color-primary {
  color: @color-primary;
}

/* 主色浅 */
.lg-color-primary-light {
  color: @color-primary-light;
}

/* 辅助色橙色 */
.lg-color-warning-dark {
  color: @color-warning-dark;
}

/* 成功色 */
.lg-color-success {
  color: @color-success;
}

/* 成功色浅 */
.lg-color-success-light {
  color: @color-success-light;
}

/* 危险色 */
.lg-color-danger {
  color: @color-danger;
}

/* 警告色 */
.lg-color-warning {
  color: @color-warning;
}

/* 信息色 */
.lg-color-info {
  color: @color-info;
}

/* 一级文字 */
.lg-color-text-primary {
  color: @color-text-primary;
}

/* 二级文字 */
.lg-color-text-secondary {
  color: @color-text-secondary;
}

/* 提示文字 */
.lg-color-text-placeholder {
  color: @color-text-placeholder;
}

/* 禁用文字 */
.lg-color-text-disabled {
  color: @color-text-disabled;
}

/* 边框色 */
.lg-color-border {
  color: @color-border;
}

/* 图表颜色 */
.lg-color-chart-1 {
  color: #10B3B7;
}

.lg-color-chart-2 {
  color: #F2C94C;
}

.lg-color-chart-3 {
  color: #FF7F41;
}

.lg-color-chart-4 {
  color: #96C13D;
}

.lg-color-chart-5 {
  color: #73DCE9;
}

.lg-color-chart-6 {
  color: #BB6BD9;
}

.lg-color-chart-7 {
  color: #2D9CDB;
}

.lg-color-chart-8 {
  color: #6FCF97;
}

.lg-color-chart-9 {
  color: #878BF9;
}

.lg-color-chart-10 {
  color: #FE7B83;
}


/* 背景色 */
/* 弹窗背景色 */
.lg-color-background-modal {
  color: #3a3f5166;
}

/* 页面背景色 */
.lg-color-background-page {
  color: @color-page-background-white;
}

/* 表头背景 */
.lg-color-background-table-header {
  color: @color-table-header-background;
}

/* 白色 */
.lg-color-white {
  color: @color-white;
}

/* button 默认 border 2px 字体加粗*/
.el-button {
  border-width: 2px;
  font-weight: 600;
}

button[disabled="disabled"],
button[disabled="true"],
.disabled {
    cursor: not-allowed !important;
    opacity: 0.4 !important;
}

/* default button padding 覆盖 */
.el-button {
  padding: 0px 16px;
  height: 40px;
}

.el-button-icon {
  padding: 0 8px;
  height: 40px;
}

/* small circle button padding  */
.el-button--small.is-circle {
  padding: 9px !important;
}

/* medium button padding 覆盖 */
.el-button--medium {
  padding: 0px 16px !important;
  height: 36px;
}

/* small button padding 覆盖 */
.el-button--small {
  padding: 0px 12px !important;
  font-size: 14px;
  border-radius: 4px;
  height: 32px;
}

/* mini button padding 覆盖 */
.el-button--mini {
  padding: 3px 8px !important;
  height: 28px;
}

/* 文字按钮无border */
.el-button--text {
  border: none;
  padding: 12px 0px !important;
}

/* default button 选中 覆盖 */
.el-button--primary:focus,.el-button--primary:hover,.el-button--primary-toogle {
  color: @color-white;
  background: @color-primary;
  border-color: @color-primary;
  box-shadow: 0 0 0 3px @color-primary-opacity;
}

/* success button 选中覆盖 */
.el-button--success:focus,.el-button--success:hover,.el-button--success-toggle {
  color: @color-white;
  background: @color-success;
  border-color: transparent;
  box-shadow: 0 0 0 3px @color-success-opacity;
}

/* warning button 选中覆盖 */
.el-button--warning:focus,.el-button--warning:hover,.el-button--warning-toggle {
  color: @color-white;
  background: @color-warning;
  border-color: transparent;
  box-shadow: 0 0 0 3px @color-warning-opacity;
}

/* danger button 选中覆盖 */
.el-button--danger:focus,.el-button--danger:hover,.el-button--danger-toggle {
  color: @color-white;
  background: @color-danger;
  border-color: transparent;
  box-shadow: 0 0 0 3px @color-danger-opacity;
}

/* info button 选中覆盖 */
.el-button--info:focus,.el-button--info:hover,.el-button--info-toggle {
  color: @color-white;
  background: @color-info;
  border-color: transparent;
  box-shadow: 0 0 0 3px @color-info-opacity;
}

/* primary plain button 覆盖 */
.el-button--primary.is-plain {
  color: @color-primary;
  background: @color-white;
  border-color: @color-primary;
  font-weight: 600;
}

/* primary plain button 选中覆盖  */
.el-button--primary.is-plain:focus,.el-button--primary.is-plain:hover,.el-button--primary-plain-toogle {
  color: @color-primary;
  background: @color-white;
  border-color: @color-primary !important;
  box-shadow: 0 0 0 3px @color-primary-opacity;
}

/* default plain button 覆盖 */
.el-button--default.is-plain {
  color: @color-text-primary;
  background: @color-white;
  border-color: @color-border;
  font-weight: 400;
}

/* default plain button 选中覆盖 */
.el-button--default.is-plain:focus,.el-button--default.is-plain:hover,.el-button--default-plain-toogle {
  color: @color-text-primary;
  background: @color-white;
  border-color: @color-border !important;
  box-shadow: 0 0 0 3px @color-border-opacity;
}

/* success plain button 覆盖 */
.el-button--success.is-plain {
  color: @color-success;
  background: @color-white;
  border-color: @color-success;
  font-weight: 400;
}

/* success plain button 选中覆盖 */
.el-button--success.is-plain:focus,.el-button--success.is-plain:hover,.el-button--success-plain-toogle {
  color: @color-success;
  background: @color-white;
  border-color: @color-success-opacity !important;
  box-shadow: 0 0 0 3px @color-success-opacity;
}

/* warning plain button 覆盖 */
.el-button--warning.is-plain {
  color: @color-warning;
  background: @color-white;
  border-color: @color-warning;
  font-weight: 400;
}

/* warning plain button 选中覆盖 */
.el-button--warning.is-plain:focus,.el-button--warning.is-plain:hover,.el-button--warning-plain-toogle {
  color: @color-warning;
  background: @color-white;
  border-color: @color-warning-opacity !important;
  box-shadow: 0 0 0 3px @color-warning-opacity;
}

/* danger plain button 覆盖 */
.el-button--danger.is-plain {
  color: @color-danger;
  background: @color-white;
  border-color: @color-danger;
  font-weight: 400;
}

/* danger plain button 选中覆盖 */
.el-button--danger.is-plain:focus,.el-button--danger.is-plain:hover,.el-button--danger-plain-toogle {
  color: @color-danger;
  background: @color-white;
  border-color: @color-danger;
  box-shadow: 0 0 0 3px @color-danger-opacity;
}

/* info plain button 覆盖 */
.el-button--info.is-plain {
  color: @color-info;
  background: @color-white;
  border-color: @color-info;
  font-weight: 400;
}

/* info plain button 选中覆盖 */
.el-button--info.is-plain:focus,.el-button--info.is-plain:hover,.el-button--info-plain-toogle {
  color: @color-info;
  background: @color-white;
  border-color: @color-info-opacity !important;
  box-shadow: 0 0 0 3px @color-info-opacity;
}

/* 橙色 button */
.el-button-warning-dark {
  background: @color-warning-dark;
  color: @color-white;
  border-color: @color-warning-dark;
}

/* 橙色 button 选中覆盖 */
.el-button-warning-dark:focus,.el-button-warning-dark:hover,.el-button-warning-dark-toogle {
  background: @color-warning-dark;
  color: @color-white;
  border-color: @color-warning-dark !important;
  box-shadow: 0 0 0 3px @color-warning-dark-opacity;
}

/* 灰色 button */
.el-button-gray {
  background: @color-gray;
  color: @color-text-secondary;
  border-color: @color-gray;
}

/* 灰色 button 选中覆盖 */
.el-button-gray:focus,.el-button-gray:hover,.el-button-gray-toogle {
  background: @color-gray;
  color: @color-text-secondary;
  border-color: @color-gray-opacity !important;
  box-shadow: 0 0 0 3px @color-gray-opacity;
}

/* plain 按钮禁用时背景白色 */
.el-button.is-plain.is-disabled {
  background: @color-white !important;
}

/* 按钮禁用状态时取消投影 */
.el-button.is-disabled, .el-button.is-plain.is-disabled {
  box-shadow: none;
}

/* 按钮禁用状态 */
.el-button--primary.is-disabled, .el-button--primary.is-disabled:active, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:hover {
  color: @color-white!important;
  background: @color-primary!important;
  border-color: @color-primary!important;
}

.el-button-warning-dark.is-disabled, .el-button-warning-dark.is-disabled:active, .el-button-warning-dark.is-disabled:focus, .el-button-warning-dark.is-disabled:hover {
  color: @color-white!important;
  background: @color-warning-dark!important;
  border-color: @color-warning-dark!important;
}

.el-button-gray.is-disabled, .el-button-gray.is-disabled:active, .el-button-gray.is-disabled:focus, .el-button-gray.is-disabled:hover {
  color: @color-text-primary-opacity!important;
  background: @color-gray!important;
  border-color: @color-gray-opacity!important;
}

.el-button.el-button--primary.is-disabled.is-plain, .el-button.el-button--primary.is-disabled.is-plain:focus, .el-button.el-button--primary.is-disabled.is-plain:hover {
  color: @color-primary-opacity!important;
  background: @color-white!important;
  border-color: @color-primary-opacity!important;
}

.el-button.is-disabled.is-plain, .el-button.is-disabled.is-plain:focus, .el-button.is-disabled.is-plain:hover {
  color: @color-text-primary-opacity!important;
  background: @color-white!important;
  border-color: @color-border-opacity!important;
}

/* AI 相关按钮 */
.ai-btn, .ai-btn:hover, .ai-btn:focus, .ai-btn.is-disabled, .ai-btn.is-disabled:hover, .ai-btn.is-disabled:focus {
  background: linear-gradient(271deg, #2D9CDB 0.32%, #878BF9 67.59%, #BB6BD9 142.72%) !important;
  border-color: transparent !important;
  border: 0 !important;
  color: #FFFFFF;
  transition: all 0.3s ease;
}

.ai-btn:hover {
  box-shadow: 0 0 0 3px rgba(45, 156, 219, 0.3) !important;
}

.ai-btn:focus {
  box-shadow: 0 0 0 3px rgba(45, 156, 219, 0.3) !important;
}

.ai-btn:active {
  opacity: 0.5 !important;
}

/* 点击之前 */
.ai-btn.is-opacity-50, .ai-btn.is-opacity-50:hover, .ai-btn.is-opacity-50:focus, .ai-btn.is-opacity-50.is-disabled, .ai-btn.is-opacity-50.is-disabled:hover, .ai-btn.is-opacity-50.is-disabled:focus{
  background: linear-gradient(271deg, rgba(45, 156, 219, 0.40) 0.32%, rgba(135, 139, 249, 0.40) 67.59%, rgba(187, 107, 217, 0.40) 142.72%) !important;
}

/* 输入框 border 覆盖 */
.border-bold.el-input__inner, .border-bold .el-input__inner {
  border-width: 2px;
  border-color: @color-border;
  color: @color-text-primary;
}

.el-input__inner, .el-textarea__inner {
  color: @color-text-primary;
}
/* 输入框选中覆盖 */
.el-input__inner:focus, .el-textarea__inner:focus {
  border-color: @color-primary;
  color: @color-text-primary;
}

/* 搜索框左右图表边距 12px 覆盖 */
.el-input__icon {
  color: @color-text-secondary;
  width: 28px;
  display: inline-block;
}

/* 输入框禁用 */
.el-input.is-disabled .el-input__inner {
  background: @color-gray-white;
  color: @color-text-disabled;
}

/* 输入框 placeholder 文字颜色 */
.el-input input::placeholder,.el-textarea textarea::placeholder {
  color: @color-text-placeholder;
}

/* 搜索框禁用时icon 颜色 */
.el-input .el-input__icon {
  color: @color-text-secondary;
  font-weight: 600;
}

/* 输入框激活光标颜色高亮 */
.active-input-cater .el-textarea__inner:focus,
.active-input-cater .el-input__inner:focus {
  caret-color: @color-primary
}

/* 隐藏文本输入框大小调节 */
.no-resize .el-textarea__inner {
  resize: none;
}

/* select 选项 hover 覆盖*/
.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  background: @color-primary-light;
  color: @color-primary;
}
.el-select-dropdown__item.is-disabled.hover,.el-select-dropdown__item.is-disabled:hover {
  background: @color-gray-white;
  color: @color-element-disabled;
}

.el-select .el-input .el-select__caret {
  color: @color-text-secondary;
  font-weight: 400;
}

/* table 样式覆盖 */
.el-table, .el-table thead {
  color: @color-text-primary !important;
}

/* 表格内容padding 16px */
.el-table .cell, .el-table th.el-table__cell>.cell,.el-table--border .el-table__cell:first-child .cell {
  padding-left: 16px;
  padding-right: 16px;
}

/* 表格斑马线颜色 */
.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: @color-page-background-white;
}
/* 表头高度 */
.el-table--fit {
  border-top: 1px solid @color-border;
}

/* 表头背景颜色 */
.el-table th.el-table__cell {
  height: 60px;
  background: @color-table-header-background;
}

/* table 行高度 */
.el-table tr {
  height: 54px;
}

/* 边框颜色 */
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-color: @color-border;
}

.el-table .el-table__cell {
  padding: 0;
}

/* switch 开关不透明度覆盖 */
.el-switch.is-disabled {
  opacity: 0.4;
}

/* message box 样式覆盖 */
.el-message-box {
  padding-bottom: 24px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
  border-radius: 8px;
}

/* message box 标题 */
.el-message-box__title {
  color: @color-text-primary;
  font-weight: 600;
  font-size: 20px;
}

/* message box 边距 */
.el-message-box__header {
  padding: 24px;
  padding-bottom: 0;
}

/* message box 内容边距 */
.el-message-box__content {
  padding: 16px 24px;
  font-size: 16px;
  color: @color-text-primary;
}

/* message box 按钮边距 */
.el-message-box__btns {
  padding: 0px 24px;
}

.el-message-box__btns button {
  padding: 9px 16px;
  line-height: 18px;
  border-radius: 4px;
  height: 40px;
}

/* message box 关闭按钮位置 */
.el-message-box__headerbtn {
  top: 22px;
  right: 24px;
}

/* 消息提示自定义深色样式 */
/* 成功深色背景 */
.message-success-dark {
  background: @color-success;
}

/* 警告深色背景 */
.message-warn-dark {
  background: @color-warning;
}

/* 危险深色背景 */
.message-error-dark {
  background: @color-danger;
}

/* 信息深色背景 */
.message-info-dark {
  background: @color-info;
}

/* 深色message box 内容字体颜色 */
.message-success-dark p, .message-success-dark i, .message-warn-dark p, .message-warn-dark i, .message-error-dark p, .message-error-dark i, .message-info-dark p, .message-info-dark i{
  color:@color-white !important;
}

/* message box 字体 600 加粗 */
.el-message__content {
  font-size: 16px !important;
  font-weight: 600 !important;
}

/* 成功提示关闭按钮颜色 */
.el-message--success .el-message__closeBtn {
  color: @color-success;
}

/* 警告提示关闭按钮颜色 */
.el-message--warning .el-message__closeBtn {
  color: @color-warning;
}

/* 错误提示关闭按钮颜色 */
.el-message--error .el-message__closeBtn {
  color: @color-danger;
}

/* 信息提示关闭按钮颜色 */
.el-message--info .el-message__closeBtn {
  color: @color-info;
}

.el-radio__inner, .el-checkbox__inner {
  border-width: 2px;
}
.el-checkbox__inner::after {
  top: 0px;
  left: 3px;
}
/* 多选框 为全选 时 中间横杆居中样式覆盖 */
.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  content: "";
  position: absolute;
  display: block;
  background-color: #fff;
  height: 1px;
  width: 6px;
  -webkit-transform: scale(.5);
  transform: scale(.5);
  left: 50%;
  right: 0;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 单、多选框 label 样式覆盖 */
.el-radio__label,.el-checkbox__label  {
  font-weight: 400;
  color: @color-text-primary !important;
}

.el-radio__input.is-disabled+span.el-radio__label, .el-checkbox__input.is-disabled+span.el-checkbox__label {
  color: @color-text-disabled;
}

/* 分页样式 */
/* 页码激活样式覆盖 */
.el-pager li.active {
  color: @color-white !important;
  background: @color-primary !important;
  cursor: default;
  font-weight: 600;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  line-height: 32px;
}

.el-pager li{
  width: 32px;
  height: 32px;
  line-height: 32px;
}
.el-pagination button, .el-pagination span:not([class*=suffix]){
  height: 32px;
  line-height: 32px;
}
/* 下一页图标 */
.el-pagination .el-icon-arrow-right:before {
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 32px;
  color: @color-info;
  border-radius: 4px;
}


/* 上一页图标 */
.el-pagination .el-icon-arrow-left:before {
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 32px;
  color: @color-info;
  border-radius: 4px;
}

/* 分页总数样式覆盖 */
.el-pagination__total {
  color: @color-text-primary;
}

/* 分页上一页按钮 */
.el-pagination .btn-prev {
  padding-right: 6px;
}

/* 分页下一页按钮 */
.el-pagination .btn-next {
  padding-left: 0px;
}

.el-pagination {
  font-weight: 400;
  color: @color-text-primary;
}

/* 分页页码hover */
.el-pager li:hover, .el-pagination .btn-next .el-icon:hover, .el-pagination .btn-prev .el-icon:hover {
  background: @color-gray-white-opacity;
  color: @color-text-primary;
  border-radius: 4px;
}

/* 每个页码的大小样式覆盖 */
.el-pagination--small .el-pager li{
  border-color: transparent;
  font-size: 14px;
  line-height: 32px;
  height: 32px;
  min-width: 32px;
  margin-right: 6px;
}

/* total与页码对齐样式覆盖 */
.el-pagination--small span:not([class*=suffix]) {
  height: 32px;
  line-height: 32px;
}

/* 页码最后一页样式覆盖 */
.el-pagination--small .btn-next, .el-pagination--small .btn-prev, .el-pagination--small .el-pager li, .el-pagination--small .el-pager li.btn-quicknext, .el-pagination--small .el-pager li.btn-quickprev, .el-pagination--small .el-pager li:last-child {
  border-color: transparent;
  font-size: 14px;
  line-height: 32px;
  height: 32px;
  min-width: 32px;
}
/* 每页显示条数输入框高度 覆盖 */
.el-input--mini .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-input--medium .el-input__icon {
  height: 36px;
  line-height: 36px;
}

/* tooltip提示样式覆盖 */
.el-tooltip__popper.is-dark {
  background: #000000CC;
  color: @color-white;
  font-size: 14px;
}

/* 引导样式重写 */
/* 标题样式覆盖 */
.driver-popover-title {
  font-family: unset !important;
  font-size: 20px !important;
  font-weight: 600 !important;
  margin: 0 !important;
  color: @color-text-primary;
}

/* 内容区样式覆盖 */
.driver-popover-description {
  font-family: unset !important;
  padding: 12px 15px 0px 15px;
  font-size: 16px !important;
  color: @color-text-primary !important;
}

/* 底部样式覆盖 */
.driver-popover-footer {
  padding: 16px 0px 0px 0px!important;
  margin-top: 0px !important;
}

/* 关闭按钮样式覆盖 */
.driver-close-btn {
  height: 40px;
  padding: 3px 10px 3px 0px!important;
  font-family: unset !important;
  font-size: 16px !important;
  border: none !important;
  background: transparent !important;
  color: @color-text-secondary !important;
}
div#driver-page-overlay {
  opacity: 0.7 !important;
  background: #3a3f51 !important;
}

/* 引导项样式覆盖 */
#driver-popover-item {
  max-width: none!important;
  padding: 24px!important;
  border-radius: 8px!important;
}

/* 引导项箭头样式 */
div#driver-popover-item .driver-popover-tip {
  border: 8px solid @color-white;
}

div#driver-popover-item .driver-popover-tip.left {
  left: -16px;
}

div#driver-popover-item .driver-popover-tip.right {
  right: -16px;
}

div#driver-popover-item .driver-popover-tip.top {
  top: -16px;
}

div#driver-popover-item .driver-popover-tip.bottom {
  bottom: -16px;
}

/* 上一步骤按钮样式覆盖 */
.driver-prev-btn {
  height: 40px;
  font-style: normal!important;
  font-size: 16px !important;
  font-weight: 600!important;
  pointer-events: none;
  border: none !important;
  background: transparent !important;
  color: @color-primary !important;
}

/* 下一步骤按钮样式覆盖 */
.driver-next-btn  {
  height: 40px;
  font-family: unset !important;
  font-size: 14px !important;
  background: @color-primary !important;
  border-color: @color-primary !important;
  border-radius: 4px !important;
  color: @color-white !important;
  text-shadow: none !important;
  padding: 8px 16px !important;
}

/* 无序列表添加小点 */
.driver-popover-description ul li {
  list-style: disc!important;
}

/* 内容 宽度大小覆盖 */
.driver-popover-description ul {
  width: 322.37px;
  height: 120px;
  margin-left: 10px;
  padding-right: 35px;
}

/* 步骤条 */
/* 正在进行时样式覆盖 */
/* 背景颜色 */
.el-step__head.is-process .el-step__icon{
  background: @color-success!important;
}

/* 字体颜色 */
.el-step__head.is-process .el-step__icon-inner {
  color: @color-white!important;
}

/* 取消边框 */
.el-step__head.is-process .el-step__icon.is-text {
  border: none!important;
}

/* 正在进行时添加外部圈 */
.el-step__head.is-process .el-step__icon::before {
  content: '';
  display: inline-block;
  position: absolute;
  width: 36px;
  height: 36px;
  border: 2px solid @color-success;
  border-radius: 50%;
}

.el-step__icon-inner {
  font-weight: 400;
  color: @color-text-primary;
}

/* 步骤标题样式覆盖 */
.el-step__title {
  font-size: 14px !important;
  line-height: 22px !important;
  color: @color-text-primary !important;
  margin-top: 6px !important;
}

.el-step__title.is-process {
  color: @color-text-primary !important;
  font-weight: 400 !important;
}

.el-step__title.is-wait {
  color: @color-text-primary !important;
}

/* 已经完成 */
/* 已经完成后样式覆盖 */
/* 背景颜色 */
.el-step__head.is-finish .el-step__icon{
  background: @color-success !important;
}

/* 字体颜色 */
.el-step__head.is-finish .el-step__icon-inner {
  color: @color-white!important;
}

/* 取消边框 */
.el-step__head.is-finish .el-step__icon.is-text {
  border: none!important;
}

/* 步骤之间的线条颜色覆盖 */
.el-step__head.is-finish {
  border-color: @color-step-divider !important;
}

/* 步骤之间的线条居中显示 */
.el-step__line {
  width: 50px;
  background: @color-step-divider !important;
  height: 1.5px!important;
  left: 100%!important;
  transform: translateX(-50%)!important;
}

/* 滚动时才显示滚动条的滚动条 */
.lg-scrollbar {
  overflow: auto!important;
}

/* 鼠标悬浮时才显示滚动条的滚动条 */
.lg-scrollbar:hover {
  overflow: auto!important;
}

/* 常显的滚动条 */
.lg-scrollbar-show {
  overflow: auto;
}

/* 不显示滚动条的滚动条 */
.lg-scrollbar-hidden {
  overflow: auto;
  scrollbar-width: none;
}

.el-table__body-wrapper::-webkit-scrollbar,.el-drawer__body::-webkit-scrollbar, .lg-scrollbar-show::-webkit-scrollbar {
  height: 8px;
  width: 5px;
}

/* 滚动条背景颜色 */
.el-table__body-wrapper::-webkit-scrollbar-thumb,.el-drawer__body::-webkit-scrollbar-thumb, .lg-scrollbar-show::-webkit-scrollbar-thumb, .lg-scrollbar-healthstats::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: @color-step-divider;
}

/* 滚动条大小 */
.lg-scrollbar::-webkit-scrollbar,.el-drawer__body::-webkit-scrollbar, .lg-scrollbar-show::-webkit-scrollbar {
  height: 10px;
  width: 5px;
}

/* 滚动条背景颜色 */
.lg-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: transparent;
}

.lg-scrollbar:hover::-webkit-scrollbar-thumb {
  background: @color-step-divider !important;
}

.lg-scrollbar-hidden::-webkit-scrollbar {
  height: 0px;
  width: 0px;
}

.lg-scrollbar-healthstats::-webkit-scrollbar {
  height: 10px;
  width: 5px;
}

/* 面包屑分隔符 */
.el-breadcrumb__separator[class*=icon] {
  margin: 0 4px;
  color: @color-primary;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner, .el-breadcrumb__item:last-child .el-breadcrumb__inner a, .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover, .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: @color-text-primary;
}


.el-breadcrumb__inner a, .el-breadcrumb__inner.is-link {
  font-weight: 400;
  color: @color-primary;
}

.el-breadcrumb__inner a:hover, .el-breadcrumb__inner.is-link:hover {
  font-weight: 600;
}

/* 一级选项卡 */
.lg-tabs {
  .el-tabs__header {
    border: none !important;
    margin: 0 !important;
  }
  .el-tabs__nav {
    padding: 6px !important;
    background: @color-primary-light;
    border-radius: 4px !important;
    gap: 6px;
    display: inline-flex;
    border: none !important;
  }
  .el-tabs__item.is-active {
    background: @color-primary !important;
    color: @color-white;
    border-radius: 4px;
  }
  .el-tabs__item {
    color: @color-primary;
    font-weight: 600;
    font-size: 16px;
    height: 36px !important;
    line-height: 36px !important;
    padding: 0px 12px !important;
    border-width: 0 !important;
  }
  .el-tabs__item:not(.is-disabled):hover {
    background: #CFF0F1;
    border-radius: 4px;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__nav-wrap.is-scrollable {
    padding: 0px 24px;
    background: @color-primary-light;
    border-radius: 6px;
  }
  .el-tabs__nav-wrap.is-scrollable .el-tabs__nav {
    padding: 6px 0px !important;
  }
  .el-tabs__nav-prev {
    left: 6px;
    line-height: 48px;
  }
  .el-tabs__nav-next {
    right: 6px;
    line-height: 48px;
  }
}

/* 二级选项卡 */
.lg-tabs-small {
  .el-tabs__header {
    border: none !important;
    margin: 0 !important;
  }
  .el-tabs__nav {
    padding: 4px !important;
    background: @color-gray-white;
    border-radius: 4px !important;
    gap: 4px;
    display: inline-flex;
    border: none !important;
  }
  .el-tabs__item.is-active {
    background: #FFF !important;
    color: @color-primary ;
    font-weight: 600;
    border-radius: 4px;
  }
  .el-tabs__item {
    color: @color-text-primary;
    height: 32px !important;
    line-height: 32px !important;
    padding: 0px 12px !important;
    border-width: 0 !important;
  }
  .el-tabs__item:hover {
    background: @color-gray;
    border-radius: 4px;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__nav-wrap.is-scrollable {
    padding: 0px 24px;
    background: @color-gray-white;
    border-radius: 4px;
  }
  .el-tabs__nav-wrap.is-scrollable .el-tabs__nav {
    padding: 4px 0px !important;
  }
  .el-tabs__nav-prev {
    left: 6px;
    line-height: 40px;
  }
  .el-tabs__nav-next {
    right: 6px;
    line-height: 40px;
  }
}

/* 三级选项卡 */
.lg-tabs-mini {
  .el-tabs__header {
    margin: 0 !important;
  }
  .el-tabs__nav {
    padding: 4px !important;
    background: @color-white;
    border-radius: 4px !important;
    gap: 4px;
    display: inline-flex;
    border: 1px solid @color-border !important;
  }
  .el-tabs__item.is-active {
    background: @color-primary-light !important;
    color: @color-primary ;
    border-radius: 4px;
    font-weight: 600;
  }
  .el-tabs__item {
    color: @color-text-primary;
    font-weight: 400;
    height: 32px !important;
    line-height: 32px !important;
    padding: 0px 12px !important;
    border-width: 0 !important;
  }
  .el-tabs__item:hover {
    background: @color-gray-white;
    border-radius: 4px;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__nav-wrap.is-scrollable {
    padding: 0px 24px;
    background: @color-white;
    border-radius: 4px;
    border: 1px solid @color-border !important;
  }
  .el-tabs__nav-wrap.is-scrollable .el-tabs__nav {
    border: none !important;
    padding: 4px 0px !important;
  }
  .el-tabs__nav-prev {
    left: 6px;
    line-height: 40px;
  }
  .el-tabs__nav-next {
    right: 6px;
    line-height: 40px;
  }
}

.animate-row {
  -webkit-transition: height .8s cubic-bezier(0, 1.05, 0, 1);
  -moz-transition: height .8s cubic-bezier(0, 1.05, 0, 1);
  -ms-transition: height .8s cubic-bezier(0, 1.05, 0, 1);
  -o-transition: height .8s cubic-bezier(0, 1.05, 0, 1);
  transition: height .8s cubic-bezier(0, 1.05, 0, 1);

  -webkit-transition: top .8s cubic-bezier(0, 1.05, 0, 1);
  -moz-transition: top .8s cubic-bezier(0, 1.05, 0, 1);
  -ms-transition: top .8s cubic-bezier(0, 1.05, 0, 1);
  -o-transition: top .8s cubic-bezier(0, 1.05, 0, 1);
  transition: top .8s cubic-bezier(0, 1.05, 0, 1);
}

.animate-column {
  -webkit-transition: width .8s cubic-bezier(0, 1.05, 0, 1);
  -moz-transition: width .8s cubic-bezier(0, 1.05, 0, 1);
  -ms-transition: width .8s cubic-bezier(0, 1.05, 0, 1);
  -o-transition: width .8s cubic-bezier(0, 1.05, 0, 1);
  transition: width .8s cubic-bezier(0, 1.05, 0, 1);

  -webkit-transition: left .8s cubic-bezier(0, 1.05, 0, 1);
  -moz-transition: left .8s cubic-bezier(0, 1.05, 0, 1);
  -ms-transition: left .8s cubic-bezier(0, 1.05, 0, 1);
  -o-transition: left .8s cubic-bezier(0, 1.05, 0, 1);
  transition: left .8s cubic-bezier(0, 1.05, 0, 1);
}

/* el-tree 文字颜色 */
.el-tree {
  color: @color-text-primary;
}

/* 弹窗背景色 */
.v-modal {
  opacity: 0.7 !important;
  background: #3a3f51 !important;
}

/* 弹窗居中 */
.lg-modal-center {
  .el-dialog {
    max-height: 70vh;
    margin-top: 15vh;
    margin-bottom: 15vh;
  }

  /* 弹窗body滚动 */
  .el-dialog {
    .el-dialog__body {
      max-height: 500px !important;
      min-height: 100px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        height: 10px;
        width: 5px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: @color-step-divider;
      }
    }
  }
}

.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  font-size: 20px;
  font-weight: 600;
  color: @color-text-primary;
}

.el-dialog__body {
  color: @color-text-primary;
}

// 全局 dialog 上下居中
// .el-dialog {
//   display: flex;
//   display: -ms-flex; /* 兼容IE */
//   flex-direction: column;
//   -ms-flex-direction: column; /* 兼容IE */
//   margin:0 !important;
//   position:absolute;
//   top:50%;
//   left:50%;
//   transform:translate(-50%,-50%);
//   max-height:calc(100% - 30px);
//   max-width:calc(100% - 30px);
// }
// .el-dialog .el-dialog__body{
//   max-height: 100%;
//   flex: 1;
//   -ms-flex: 1 1 auto; /* 兼容IE */
//   overflow-y: auto;
//   overflow-x: hidden;
//   &::-webkit-scrollbar {
//     height: 10px;
//     width: 5px;
//   }
//   &::-webkit-scrollbar-thumb {
//     border-radius: 10px;
//     background: @color-step-divider;
//   }
// }

svg.lg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

// 重写element-ui 的箭头为lg-icon
.el-icon-arrow-right::before {
  content: "\e65b" !important;
  font-family: 'lg-icon' !important;
}

.el-icon-arrow-left:before {
  content: "\e65a";
  font-family: 'lg-icon' !important;
}
.el-icon-arrow-down:before {
  content: "\e65d";
  font-family: 'lg-icon' !important;
}
.el-icon-arrow-up:before {
  content: "\e65c";
  font-family: 'lg-icon' !important;
}
.el-button .lg-icon {
  font-weight: 400;
  font-size: 16px;
}
.el-form-item__label {
  color: #111c1c;
}
.el-table__body-wrapper {
  scrollbar-width: thin;
}

/* 横轴纵轴高和宽一致的滚动条 */
.lg-scrollbar-small {
  overflow: auto!important;
}

/* 横轴纵轴高和宽一致的滚动条 鼠标悬浮时才显示滚动条的滚动条 */
.lg-scrollbar-small:hover {
  overflow: auto!important;
}

/* 横轴纵轴高和宽一致的滚动条 滚动条大小 */
.lg-scrollbar-small::-webkit-scrollbar,.el-drawer__body::-webkit-scrollbar, .lg-scrollbar-show::-webkit-scrollbar {
  height: 5px;
  width: 5px;
}

/* 横轴纵轴高和宽一致的滚动条 滚动条背景颜色 */
.lg-scrollbar-small::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: transparent;
}

/* 横轴纵轴高和宽一致的滚动条 滚动条背景颜色 */
.lg-scrollbar-small:hover::-webkit-scrollbar-thumb {
  background: @color-step-divider !important;
}