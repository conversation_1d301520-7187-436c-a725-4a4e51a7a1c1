<svg width="1440" height="487" viewBox="0 0 1440 487" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.2" x="1450.5" y="486.5" width="1450" height="501" transform="rotate(-180 1450.5 486.5)" fill="url(#paint0_linear_455_1071)" stroke="#E1F8FF"/>
<g opacity="0.05" filter="url(#filter0_b_455_1071)">
<circle cx="1267" cy="114" r="87" fill="url(#paint1_linear_455_1071)"/>
</g>
<g opacity="0.1" filter="url(#filter1_b_455_1071)">
<circle cx="211" cy="135" r="75" fill="url(#paint2_linear_455_1071)" fill-opacity="0.8"/>
</g>
<defs>
<filter id="filter0_b_455_1071" x="1176" y="23" width="182" height="182" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_455_1071"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_455_1071" result="shape"/>
</filter>
<filter id="filter1_b_455_1071" x="132" y="56" width="158" height="158" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_455_1071"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_455_1071" result="shape"/>
</filter>
<linearGradient id="paint0_linear_455_1071" x1="2817.5" y1="430.5" x2="1401" y2="1171" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.27735" stop-color="#69E4FF"/>
<stop offset="0.953" stop-color="#F1A0FF"/>
</linearGradient>
<linearGradient id="paint1_linear_455_1071" x1="1201.75" y1="47.1964" x2="1310.11" y2="101.96" gradientUnits="userSpaceOnUse">
<stop stop-color="#791AFF"/>
<stop offset="1" stop-color="#7DA1FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_455_1071" x1="180" y1="143.5" x2="247" y2="96" gradientUnits="userSpaceOnUse">
<stop stop-color="#D47DFF" stop-opacity="0"/>
<stop offset="1" stop-color="#51C8FF"/>
</linearGradient>
</defs>
</svg>
