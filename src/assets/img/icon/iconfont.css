@font-face {
  font-family: "iconfont"; /* Project id 2661306 */
  src: url('iconfont.woff2?t=1626514101354') format('woff2'),
       url('iconfont.woff?t=1626514101354') format('woff'),
       url('iconfont.ttf?t=1626514101354') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-laba:before {
  content: "\e6b2";
}

.icon-qr:before {
  content: "\71";
}

.icon-uncheck:before {
  content: "\e72f";
}

.icon-check:before {
  content: "\e731";
}

