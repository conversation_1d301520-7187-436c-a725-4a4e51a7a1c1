<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <link id="faviconIcon" rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>Curriculum Genie</title>
    <!-- font-awesome -->
    <link rel="stylesheet" href="<%= BASE_URL %>lib/font-awesome/css/font-awesome.min.css">
    <!-- bootstrap3 -->
    <link rel="stylesheet" href="<%= BASE_URL %>lib/bootstrap3.3.7/css/bootstrap.min.css">
    <!-- custom -->
    <!-- <link rel="stylesheet" href="<%= BASE_URL %>lib/custom/fonts/index.css"> -->
    <link rel="stylesheet" href="<%= BASE_URL %>lib/custom/theme/index.css">
    <!-- <link rel="stylesheet" href="<%= BASE_URL %>lib/custom/theme/display.css"> -->
    <!-- <link rel="stylesheet" type="text/css" href="https://d2urtjxi3o4r5s.cloudfront.net/css/DRDP-Template.css"> -->
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9LSY1Q2T3S" defer></script>

    <!-- 引入自定义字体样式 -->
    <link rel="stylesheet" href="<%= BASE_URL %>lib/custom/fonts/index.css">
    <!-- 引入自定义响应式布局样式 -->
    <link rel="stylesheet" href="<%= BASE_URL %>lib/custom/theme/display.css">


    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());

        gtag('config', 'G-9LSY1Q2T3S');

        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(function (registrations) {
                for (let registration of registrations) {
                    registration.unregister();
                }
            });
        }
    </script>
    <script>
        !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init Ie Ts Ms Ee Es Rs capture Ge calculateEventProperties Os register register_once register_for_session unregister unregister_for_session js getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Ds Fs createPersonProfile Ls Ps opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Cs debug I As getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
        posthog.init('phc_h6jjapPCV2g85hambnsNiB1Au1Z62UsylgiWVnRWgvA', {
            api_host: 'https://us.i.posthog.com',
            defaults: '2025-05-24',
            person_profiles: 'always',
            disable_web_experiments: false,
        })
    </script>
</head>
<body>
<noscript>
    <strong>We're sorry but Learning Genie doesn't work properly without JavaScript enabled. Please enable it to
        continue.</strong>
</noscript>
<div id="app"></div>
<!-- lib -->
<script type="text/javascript" src="https://apis.google.com/js/api.js" defer></script>
<script src="https://accounts.google.com/gsi/client" defer></script>
<script type="text/javascript"
        src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js" defer></script>
<script type="text/javascript" src="<%= BASE_URL %>lib/aws-sdk/aws-sdk-2.1285.0.min.js" defer></script>
<script src="<%= BASE_URL %>lib/jquery3.3.1/jquery.min.js"></script>
<script src="<%= BASE_URL %>lib/bootstrap3.3.7/js/bootstrap.min.js" defer></script>
</body>
</html>
