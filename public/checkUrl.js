// utils.js
function handleFbclid() {
  const currentUrl = window.location.href
  const url = new URL(currentUrl)
  const fbclid = url.searchParams.get('fbclid')
  const unitId = url.searchParams.get('unitId')
  if (currentUrl.indexOf('#') !== -1) {
    return
  }
  // 分享外链 # 会被截取，这里手动加上
  if (fbclid || unitId) {
    const path = url.pathname
    const search = url.search // 保留查询参数
    const newUrl = `${window.location.origin}#${path}${search}` // 在#后面添加查询参数
    window.location.replace(newUrl)
  }
}
if (typeof window !== 'undefined') {
  handleFbclid()
  // 处理逻辑...
}
