// eslint-disable-next-line no-undef
importScripts('./workbox-sw.js');

// 初始化 Workbox
workbox.setConfig({
  debug: false, // 开发环境启用调试模式
});

const {registerRoute} = workbox.routing;
const {CacheFirst, StaleWhileRevalidate} = workbox.strategies;
const {ExpirationPlugin} = workbox.expiration;
const {CacheableResponsePlugin} = workbox.cacheableResponse;

// 缓存名称
const CACHE_NAME = 'genie-resource-cache-v1';

// 定义需要缓存的 CSS 文件
const cssFiles = [
  'lib/font-awesome/css/font-awesome.min.css',
  'lib/simple-line-icons/css/simple-line-icons.css',
  'lib/bootstrap3.3.7/css/bootstrap.min.css',
  'lib/custom/fonts/index.css',
  'lib/custom/theme/index.css',
  'lib/custom/theme/display.css',
  'lib/icon/iconfont.css',
];

// 定义需要缓存的 JS 文件
const jsFiles = [
  'lib/icon/iconfont.js',
  'checkUrl.js',
  'lib/aws-sdk/aws-sdk-2.1285.0.min.js',
  'lib/jquery3.3.1/jquery.min.js',
  'lib/bootstrap3.3.7/js/bootstrap.min.js'
];

// 注册路由，使用 CacheFirst 策略缓存 CSS 和 JS 文件
[...cssFiles, ...jsFiles].forEach((file) => {
  registerRoute(
    new RegExp(`${file}$`),
    new CacheFirst({
      cacheName: CACHE_NAME,
      plugins: [
        new ExpirationPlugin({
          maxEntries: 500, // 最多缓存 500 个资源
          maxAgeSeconds: 24 * 60 * 60, // 缓存有效期为 30 天
        }),
      ],
    })
  );
});

// 注册一个静态资源的路由，使用 CacheFirst 策略缓存其他静态资源
registerRoute(
  new RegExp(`.*\\.(?:js|css|png|jpg|jpeg|svg|gif|ico|woff|woff2|ttf|eot|otf|json)$`),
  new CacheFirst({
    cacheName: CACHE_NAME,
    plugins: [
      new ExpirationPlugin({
        maxEntries: 500, // 最多缓存 500 个资源
        maxAgeSeconds: 24 * 60 * 60, // 缓存有效期为 30 天
      }),
    ],
  })
);

// 激活事件，删除旧的缓存
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames
          .filter((cacheName) => cacheName !== CACHE_NAME)
          .map((cacheName) => caches.delete(cacheName))
      );
    })
  );
  self.skipWaiting()
});

// 跳过等待，立即激活新的 Service Worker
self.addEventListener('install', (event) => {
  event.waitUntil(self.skipWaiting());
});