(function(modules){var installedModules={};function __webpack_require__(moduleId){if(installedModules[moduleId]){return installedModules[moduleId].exports}var module=installedModules[moduleId]={exports:{},id:moduleId,loaded:false};modules[moduleId].call(module.exports,module,module.exports,__webpack_require__);module.loaded=true;return module.exports}__webpack_require__.m=modules;__webpack_require__.c=installedModules;__webpack_require__.p="./";return __webpack_require__(0)})({0:function(module,exports,__webpack_require__){module.exports=__webpack_require__(246)},183:function(module,exports,__webpack_require__){var __WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__;(function(){var root=this;var previousUnderscore=root._;var ArrayProto=Array.prototype,ObjProto=Object.prototype,FuncProto=Function.prototype;var push=ArrayProto.push,slice=ArrayProto.slice,toString=ObjProto.toString,hasOwnProperty=ObjProto.hasOwnProperty;var nativeIsArray=Array.isArray,nativeKeys=Object.keys,nativeBind=FuncProto.bind,nativeCreate=Object.create;var Ctor=function(){};var _=function(obj){if(obj instanceof _){return obj}if(!(this instanceof _)){return new _(obj)}this._wrapped=obj};if(true){if(typeof module!=="undefined"&&module.exports){exports=module.exports=_}exports._=_}else{root._=_}_.VERSION="1.8.3";var optimizeCb=function(func,context,argCount){if(context===void 0){return func}switch(argCount==null?3:argCount){case 1:return function(value){return func.call(context,value)};case 2:return function(value,other){return func.call(context,value,other)};case 3:return function(value,index,collection){return func.call(context,value,index,collection)};case 4:return function(accumulator,value,index,collection){return func.call(context,accumulator,value,index,collection)}}return function(){return func.apply(context,arguments)}};var cb=function(value,context,argCount){if(value==null){return _.identity}if(_.isFunction(value)){return optimizeCb(value,context,argCount)}if(_.isObject(value)){return _.matcher(value)}return _.property(value)};_.iteratee=function(value,context){return cb(value,context,Infinity)};var createAssigner=function(keysFunc,undefinedOnly){return function(obj){var length=arguments.length;if(length<2||obj==null){return obj}for(var index=1;index<length;index++){var source=arguments[index],keys=keysFunc(source),l=keys.length;for(var i=0;i<l;i++){var key=keys[i];if(!undefinedOnly||obj[key]===void 0){obj[key]=source[key]}}}return obj}};var baseCreate=function(prototype){if(!_.isObject(prototype)){return{}}if(nativeCreate){return nativeCreate(prototype)}Ctor.prototype=prototype;var result=new Ctor;Ctor.prototype=null;return result};var property=function(key){return function(obj){return obj==null?void 0:obj[key]}};var MAX_ARRAY_INDEX=Math.pow(2,53)-1;var getLength=property("length");var isArrayLike=function(collection){var length=getLength(collection);return typeof length=="number"&&length>=0&&length<=MAX_ARRAY_INDEX};_.each=_.forEach=function(obj,iteratee,context){iteratee=optimizeCb(iteratee,context);var i,length;if(isArrayLike(obj)){for(i=0,length=obj.length;i<length;i++){iteratee(obj[i],i,obj)}}else{var keys=_.keys(obj);for(i=0,length=keys.length;i<length;i++){iteratee(obj[keys[i]],keys[i],obj)}}return obj};_.map=_.collect=function(obj,iteratee,context){iteratee=cb(iteratee,context);var keys=!isArrayLike(obj)&&_.keys(obj),length=(keys||obj).length,results=Array(length);for(var index=0;index<length;index++){var currentKey=keys?keys[index]:index;results[index]=iteratee(obj[currentKey],currentKey,obj)}return results};function createReduce(dir){function iterator(obj,iteratee,memo,keys,index,length){for(;index>=0&&index<length;index+=dir){var currentKey=keys?keys[index]:index;memo=iteratee(memo,obj[currentKey],currentKey,obj)}return memo}return function(obj,iteratee,memo,context){iteratee=optimizeCb(iteratee,context,4);var keys=!isArrayLike(obj)&&_.keys(obj),length=(keys||obj).length,index=dir>0?0:length-1;if(arguments.length<3){memo=obj[keys?keys[index]:index];index+=dir}return iterator(obj,iteratee,memo,keys,index,length)}}_.reduce=_.foldl=_.inject=createReduce(1);_.reduceRight=_.foldr=createReduce(-1);_.find=_.detect=function(obj,predicate,context){var key;if(isArrayLike(obj)){key=_.findIndex(obj,predicate,context)}else{key=_.findKey(obj,predicate,context)}if(key!==void 0&&key!==-1){return obj[key]}};_.filter=_.select=function(obj,predicate,context){var results=[];predicate=cb(predicate,context);_.each(obj,function(value,index,list){if(predicate(value,index,list)){results.push(value)}});return results};_.reject=function(obj,predicate,context){return _.filter(obj,_.negate(cb(predicate)),context)};_.every=_.all=function(obj,predicate,context){predicate=cb(predicate,context);var keys=!isArrayLike(obj)&&_.keys(obj),length=(keys||obj).length;for(var index=0;index<length;index++){var currentKey=keys?keys[index]:index;if(!predicate(obj[currentKey],currentKey,obj)){return false}}return true};_.some=_.any=function(obj,predicate,context){predicate=cb(predicate,context);
var keys=!isArrayLike(obj)&&_.keys(obj),length=(keys||obj).length;for(var index=0;index<length;index++){var currentKey=keys?keys[index]:index;if(predicate(obj[currentKey],currentKey,obj)){return true}}return false};_.contains=_.includes=_.include=function(obj,item,fromIndex,guard){if(!isArrayLike(obj)){obj=_.values(obj)}if(typeof fromIndex!="number"||guard){fromIndex=0}return _.indexOf(obj,item,fromIndex)>=0};_.invoke=function(obj,method){var args=slice.call(arguments,2);var isFunc=_.isFunction(method);return _.map(obj,function(value){var func=isFunc?method:value[method];return func==null?func:func.apply(value,args)})};_.pluck=function(obj,key){return _.map(obj,_.property(key))};_.where=function(obj,attrs){return _.filter(obj,_.matcher(attrs))};_.findWhere=function(obj,attrs){return _.find(obj,_.matcher(attrs))};_.max=function(obj,iteratee,context){var result=-Infinity,lastComputed=-Infinity,value,computed;if(iteratee==null&&obj!=null){obj=isArrayLike(obj)?obj:_.values(obj);for(var i=0,length=obj.length;i<length;i++){value=obj[i];if(value>result){result=value}}}else{iteratee=cb(iteratee,context);_.each(obj,function(value,index,list){computed=iteratee(value,index,list);if(computed>lastComputed||computed===-Infinity&&result===-Infinity){result=value;lastComputed=computed}})}return result};_.min=function(obj,iteratee,context){var result=Infinity,lastComputed=Infinity,value,computed;if(iteratee==null&&obj!=null){obj=isArrayLike(obj)?obj:_.values(obj);for(var i=0,length=obj.length;i<length;i++){value=obj[i];if(value<result){result=value}}}else{iteratee=cb(iteratee,context);_.each(obj,function(value,index,list){computed=iteratee(value,index,list);if(computed<lastComputed||computed===Infinity&&result===Infinity){result=value;lastComputed=computed}})}return result};_.shuffle=function(obj){var set=isArrayLike(obj)?obj:_.values(obj);var length=set.length;var shuffled=Array(length);for(var index=0,rand;index<length;index++){rand=_.random(0,index);if(rand!==index){shuffled[index]=shuffled[rand]}shuffled[rand]=set[index]}return shuffled};_.sample=function(obj,n,guard){if(n==null||guard){if(!isArrayLike(obj)){obj=_.values(obj)}return obj[_.random(obj.length-1)]}return _.shuffle(obj).slice(0,Math.max(0,n))};_.sortBy=function(obj,iteratee,context){iteratee=cb(iteratee,context);return _.pluck(_.map(obj,function(value,index,list){return{value:value,index:index,criteria:iteratee(value,index,list)}}).sort(function(left,right){var a=left.criteria;var b=right.criteria;if(a!==b){if(a>b||a===void 0){return 1}if(a<b||b===void 0){return -1}}return left.index-right.index}),"value")};var group=function(behavior){return function(obj,iteratee,context){var result={};iteratee=cb(iteratee,context);_.each(obj,function(value,index){var key=iteratee(value,index,obj);behavior(result,value,key)});return result}};_.groupBy=group(function(result,value,key){if(_.has(result,key)){result[key].push(value)}else{result[key]=[value]}});_.indexBy=group(function(result,value,key){result[key]=value});_.countBy=group(function(result,value,key){if(_.has(result,key)){result[key]++}else{result[key]=1}});_.toArray=function(obj){if(!obj){return[]}if(_.isArray(obj)){return slice.call(obj)}if(isArrayLike(obj)){return _.map(obj,_.identity)}return _.values(obj)};_.size=function(obj){if(obj==null){return 0}return isArrayLike(obj)?obj.length:_.keys(obj).length};_.partition=function(obj,predicate,context){predicate=cb(predicate,context);var pass=[],fail=[];_.each(obj,function(value,key,obj){(predicate(value,key,obj)?pass:fail).push(value)});return[pass,fail]};_.first=_.head=_.take=function(array,n,guard){if(array==null){return void 0}if(n==null||guard){return array[0]}return _.initial(array,array.length-n)};_.initial=function(array,n,guard){return slice.call(array,0,Math.max(0,array.length-(n==null||guard?1:n)))};_.last=function(array,n,guard){if(array==null){return void 0}if(n==null||guard){return array[array.length-1]}return _.rest(array,Math.max(0,array.length-n))};_.rest=_.tail=_.drop=function(array,n,guard){return slice.call(array,n==null||guard?1:n)};_.compact=function(array){return _.filter(array,_.identity)};var flatten=function(input,shallow,strict,startIndex){var output=[],idx=0;for(var i=startIndex||0,length=getLength(input);i<length;i++){var value=input[i];if(isArrayLike(value)&&(_.isArray(value)||_.isArguments(value))){if(!shallow){value=flatten(value,shallow,strict)}var j=0,len=value.length;output.length+=len;while(j<len){output[idx++]=value[j++]}}else{if(!strict){output[idx++]=value}}}return output};_.flatten=function(array,shallow){return flatten(array,shallow,false)};_.without=function(array){return _.difference(array,slice.call(arguments,1))};_.uniq=_.unique=function(array,isSorted,iteratee,context){if(!_.isBoolean(isSorted)){context=iteratee;iteratee=isSorted;isSorted=false}if(iteratee!=null){iteratee=cb(iteratee,context)}var result=[];var seen=[];for(var i=0,length=getLength(array);i<length;i++){var value=array[i],computed=iteratee?iteratee(value,i,array):value;
if(isSorted){if(!i||seen!==computed){result.push(value)}seen=computed}else{if(iteratee){if(!_.contains(seen,computed)){seen.push(computed);result.push(value)}}else{if(!_.contains(result,value)){result.push(value)}}}}return result};_.union=function(){return _.uniq(flatten(arguments,true,true))};_.intersection=function(array){var result=[];var argsLength=arguments.length;for(var i=0,length=getLength(array);i<length;i++){var item=array[i];if(_.contains(result,item)){continue}for(var j=1;j<argsLength;j++){if(!_.contains(arguments[j],item)){break}}if(j===argsLength){result.push(item)}}return result};_.difference=function(array){var rest=flatten(arguments,true,true,1);return _.filter(array,function(value){return !_.contains(rest,value)})};_.zip=function(){return _.unzip(arguments)};_.unzip=function(array){var length=array&&_.max(array,getLength).length||0;var result=Array(length);for(var index=0;index<length;index++){result[index]=_.pluck(array,index)}return result};_.object=function(list,values){var result={};for(var i=0,length=getLength(list);i<length;i++){if(values){result[list[i]]=values[i]}else{result[list[i][0]]=list[i][1]}}return result};function createPredicateIndexFinder(dir){return function(array,predicate,context){predicate=cb(predicate,context);var length=getLength(array);var index=dir>0?0:length-1;for(;index>=0&&index<length;index+=dir){if(predicate(array[index],index,array)){return index}}return -1}}_.findIndex=createPredicateIndexFinder(1);_.findLastIndex=createPredicateIndexFinder(-1);_.sortedIndex=function(array,obj,iteratee,context){iteratee=cb(iteratee,context,1);var value=iteratee(obj);var low=0,high=getLength(array);while(low<high){var mid=Math.floor((low+high)/2);if(iteratee(array[mid])<value){low=mid+1}else{high=mid}}return low};function createIndexFinder(dir,predicateFind,sortedIndex){return function(array,item,idx){var i=0,length=getLength(array);if(typeof idx=="number"){if(dir>0){i=idx>=0?idx:Math.max(idx+length,i)}else{length=idx>=0?Math.min(idx+1,length):idx+length+1}}else{if(sortedIndex&&idx&&length){idx=sortedIndex(array,item);return array[idx]===item?idx:-1}}if(item!==item){idx=predicateFind(slice.call(array,i,length),_.isNaN);return idx>=0?idx+i:-1}for(idx=dir>0?i:length-1;idx>=0&&idx<length;idx+=dir){if(array[idx]===item){return idx}}return -1}}_.indexOf=createIndexFinder(1,_.findIndex,_.sortedIndex);_.lastIndexOf=createIndexFinder(-1,_.findLastIndex);_.range=function(start,stop,step){if(stop==null){stop=start||0;start=0}step=step||1;var length=Math.max(Math.ceil((stop-start)/step),0);var range=Array(length);for(var idx=0;idx<length;idx++,start+=step){range[idx]=start}return range};var executeBound=function(sourceFunc,boundFunc,context,callingContext,args){if(!(callingContext instanceof boundFunc)){return sourceFunc.apply(context,args)}var self=baseCreate(sourceFunc.prototype);var result=sourceFunc.apply(self,args);if(_.isObject(result)){return result}return self};_.bind=function(func,context){if(nativeBind&&func.bind===nativeBind){return nativeBind.apply(func,slice.call(arguments,1))}if(!_.isFunction(func)){throw new TypeError("Bind must be called on a function")}var args=slice.call(arguments,2);var bound=function(){return executeBound(func,bound,context,this,args.concat(slice.call(arguments)))};return bound};_.partial=function(func){var boundArgs=slice.call(arguments,1);var bound=function(){var position=0,length=boundArgs.length;var args=Array(length);for(var i=0;i<length;i++){args[i]=boundArgs[i]===_?arguments[position++]:boundArgs[i]}while(position<arguments.length){args.push(arguments[position++])}return executeBound(func,bound,this,this,args)};return bound};_.bindAll=function(obj){var i,length=arguments.length,key;if(length<=1){throw new Error("bindAll must be passed function names")}for(i=1;i<length;i++){key=arguments[i];obj[key]=_.bind(obj[key],obj)}return obj};_.memoize=function(func,hasher){var memoize=function(key){var cache=memoize.cache;var address=""+(hasher?hasher.apply(this,arguments):key);if(!_.has(cache,address)){cache[address]=func.apply(this,arguments)}return cache[address]};memoize.cache={};return memoize};_.delay=function(func,wait){var args=slice.call(arguments,2);return setTimeout(function(){return func.apply(null,args)},wait)};_.defer=_.partial(_.delay,_,1);_.throttle=function(func,wait,options){var context,args,result;var timeout=null;var previous=0;if(!options){options={}}var later=function(){previous=options.leading===false?0:_.now();timeout=null;result=func.apply(context,args);if(!timeout){context=args=null}};return function(){var now=_.now();if(!previous&&options.leading===false){previous=now}var remaining=wait-(now-previous);context=this;args=arguments;if(remaining<=0||remaining>wait){if(timeout){clearTimeout(timeout);timeout=null}previous=now;result=func.apply(context,args);if(!timeout){context=args=null}}else{if(!timeout&&options.trailing!==false){timeout=setTimeout(later,remaining)}}return result}};_.debounce=function(func,wait,immediate){var timeout,args,context,timestamp,result;
var later=function(){var last=_.now()-timestamp;if(last<wait&&last>=0){timeout=setTimeout(later,wait-last)}else{timeout=null;if(!immediate){result=func.apply(context,args);if(!timeout){context=args=null}}}};return function(){context=this;args=arguments;timestamp=_.now();var callNow=immediate&&!timeout;if(!timeout){timeout=setTimeout(later,wait)}if(callNow){result=func.apply(context,args);context=args=null}return result}};_.wrap=function(func,wrapper){return _.partial(wrapper,func)};_.negate=function(predicate){return function(){return !predicate.apply(this,arguments)}};_.compose=function(){var args=arguments;var start=args.length-1;return function(){var i=start;var result=args[start].apply(this,arguments);while(i--){result=args[i].call(this,result)}return result}};_.after=function(times,func){return function(){if(--times<1){return func.apply(this,arguments)}}};_.before=function(times,func){var memo;return function(){if(--times>0){memo=func.apply(this,arguments)}if(times<=1){func=null}return memo}};_.once=_.partial(_.before,2);var hasEnumBug=!{toString:null}.propertyIsEnumerable("toString");var nonEnumerableProps=["valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"];function collectNonEnumProps(obj,keys){var nonEnumIdx=nonEnumerableProps.length;var constructor=obj.constructor;var proto=(_.isFunction(constructor)&&constructor.prototype)||ObjProto;var prop="constructor";if(_.has(obj,prop)&&!_.contains(keys,prop)){keys.push(prop)}while(nonEnumIdx--){prop=nonEnumerableProps[nonEnumIdx];if(prop in obj&&obj[prop]!==proto[prop]&&!_.contains(keys,prop)){keys.push(prop)}}}_.keys=function(obj){if(!_.isObject(obj)){return[]}if(nativeKeys){return nativeKeys(obj)}var keys=[];for(var key in obj){if(_.has(obj,key)){keys.push(key)}}if(hasEnumBug){collectNonEnumProps(obj,keys)}return keys};_.allKeys=function(obj){if(!_.isObject(obj)){return[]}var keys=[];for(var key in obj){keys.push(key)}if(hasEnumBug){collectNonEnumProps(obj,keys)}return keys};_.values=function(obj){var keys=_.keys(obj);var length=keys.length;var values=Array(length);for(var i=0;i<length;i++){values[i]=obj[keys[i]]}return values};_.mapObject=function(obj,iteratee,context){iteratee=cb(iteratee,context);var keys=_.keys(obj),length=keys.length,results={},currentKey;for(var index=0;index<length;index++){currentKey=keys[index];results[currentKey]=iteratee(obj[currentKey],currentKey,obj)}return results};_.pairs=function(obj){var keys=_.keys(obj);var length=keys.length;var pairs=Array(length);for(var i=0;i<length;i++){pairs[i]=[keys[i],obj[keys[i]]]}return pairs};_.invert=function(obj){var result={};var keys=_.keys(obj);for(var i=0,length=keys.length;i<length;i++){result[obj[keys[i]]]=keys[i]}return result};_.functions=_.methods=function(obj){var names=[];for(var key in obj){if(_.isFunction(obj[key])){names.push(key)}}return names.sort()};_.extend=createAssigner(_.allKeys);_.extendOwn=_.assign=createAssigner(_.keys);_.findKey=function(obj,predicate,context){predicate=cb(predicate,context);var keys=_.keys(obj),key;for(var i=0,length=keys.length;i<length;i++){key=keys[i];if(predicate(obj[key],key,obj)){return key}}};_.pick=function(object,oiteratee,context){var result={},obj=object,iteratee,keys;if(obj==null){return result}if(_.isFunction(oiteratee)){keys=_.allKeys(obj);iteratee=optimizeCb(oiteratee,context)}else{keys=flatten(arguments,false,false,1);iteratee=function(value,key,obj){return key in obj};obj=Object(obj)}for(var i=0,length=keys.length;i<length;i++){var key=keys[i];var value=obj[key];if(iteratee(value,key,obj)){result[key]=value}}return result};_.omit=function(obj,iteratee,context){if(_.isFunction(iteratee)){iteratee=_.negate(iteratee)}else{var keys=_.map(flatten(arguments,false,false,1),String);iteratee=function(value,key){return !_.contains(keys,key)}}return _.pick(obj,iteratee,context)};_.defaults=createAssigner(_.allKeys,true);_.create=function(prototype,props){var result=baseCreate(prototype);if(props){_.extendOwn(result,props)}return result};_.clone=function(obj){if(!_.isObject(obj)){return obj}return _.isArray(obj)?obj.slice():_.extend({},obj)};_.tap=function(obj,interceptor){interceptor(obj);return obj};_.isMatch=function(object,attrs){var keys=_.keys(attrs),length=keys.length;if(object==null){return !length}var obj=Object(object);for(var i=0;i<length;i++){var key=keys[i];if(attrs[key]!==obj[key]||!(key in obj)){return false}}return true};var eq=function(a,b,aStack,bStack){if(a===b){return a!==0||1/a===1/b}if(a==null||b==null){return a===b}if(a instanceof _){a=a._wrapped}if(b instanceof _){b=b._wrapped}var className=toString.call(a);if(className!==toString.call(b)){return false}switch(className){case"[object RegExp]":case"[object String]":return""+a===""+b;case"[object Number]":if(+a!==+a){return +b!==+b}return +a===0?1/+a===1/b:+a===+b;case"[object Date]":case"[object Boolean]":return +a===+b}var areArrays=className==="[object Array]";if(!areArrays){if(typeof a!="object"||typeof b!="object"){return false
}var aCtor=a.constructor,bCtor=b.constructor;if(aCtor!==bCtor&&!(_.isFunction(aCtor)&&aCtor instanceof aCtor&&_.isFunction(bCtor)&&bCtor instanceof bCtor)&&("constructor" in a&&"constructor" in b)){return false}}aStack=aStack||[];bStack=bStack||[];var length=aStack.length;while(length--){if(aStack[length]===a){return bStack[length]===b}}aStack.push(a);bStack.push(b);if(areArrays){length=a.length;if(length!==b.length){return false}while(length--){if(!eq(a[length],b[length],aStack,bStack)){return false}}}else{var keys=_.keys(a),key;length=keys.length;if(_.keys(b).length!==length){return false}while(length--){key=keys[length];if(!(_.has(b,key)&&eq(a[key],b[key],aStack,bStack))){return false}}}aStack.pop();bStack.pop();return true};_.isEqual=function(a,b){return eq(a,b)};_.isEmpty=function(obj){if(obj==null){return true}if(isArrayLike(obj)&&(_.isArray(obj)||_.isString(obj)||_.isArguments(obj))){return obj.length===0}return _.keys(obj).length===0};_.isElement=function(obj){return !!(obj&&obj.nodeType===1)};_.isArray=nativeIsArray||function(obj){return toString.call(obj)==="[object Array]"};_.isObject=function(obj){var type=typeof obj;return type==="function"||type==="object"&&!!obj};_.each(["Arguments","Function","String","Number","Date","RegExp","Error"],function(name){_["is"+name]=function(obj){return toString.call(obj)==="[object "+name+"]"}});if(!_.isArguments(arguments)){_.isArguments=function(obj){return _.has(obj,"callee")}}if(typeof/./!="function"&&typeof Int8Array!="object"){_.isFunction=function(obj){return typeof obj=="function"||false}}_.isFinite=function(obj){return isFinite(obj)&&!isNaN(parseFloat(obj))};_.isNaN=function(obj){return _.isNumber(obj)&&obj!==+obj};_.isBoolean=function(obj){return obj===true||obj===false||toString.call(obj)==="[object Boolean]"};_.isNull=function(obj){return obj===null};_.isUndefined=function(obj){return obj===void 0};_.has=function(obj,key){return obj!=null&&hasOwnProperty.call(obj,key)};_.noConflict=function(){root._=previousUnderscore;return this};_.identity=function(value){return value};_.constant=function(value){return function(){return value}};_.noop=function(){};_.property=property;_.propertyOf=function(obj){return obj==null?function(){}:function(key){return obj[key]}};_.matcher=_.matches=function(attrs){attrs=_.extendOwn({},attrs);return function(obj){return _.isMatch(obj,attrs)}};_.times=function(n,iteratee,context){var accum=Array(Math.max(0,n));iteratee=optimizeCb(iteratee,context,1);for(var i=0;i<n;i++){accum[i]=iteratee(i)}return accum};_.random=function(min,max){if(max==null){max=min;min=0}return min+Math.floor(Math.random()*(max-min+1))};_.now=Date.now||function(){return new Date().getTime()};var escapeMap={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};var unescapeMap=_.invert(escapeMap);var createEscaper=function(map){var escaper=function(match){return map[match]};var source="(?:"+_.keys(map).join("|")+")";var testRegexp=RegExp(source);var replaceRegexp=RegExp(source,"g");return function(string){string=string==null?"":""+string;return testRegexp.test(string)?string.replace(replaceRegexp,escaper):string}};_.escape=createEscaper(escapeMap);_.unescape=createEscaper(unescapeMap);_.result=function(object,property,fallback){var value=object==null?void 0:object[property];if(value===void 0){value=fallback}return _.isFunction(value)?value.call(object):value};var idCounter=0;_.uniqueId=function(prefix){var id=++idCounter+"";return prefix?prefix+id:id};_.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var noMatch=/(.)^/;var escapes={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"};var escaper=/\\|'|\r|\n|\u2028|\u2029/g;var escapeChar=function(match){return"\\"+escapes[match]};_.template=function(text,settings,oldSettings){if(!settings&&oldSettings){settings=oldSettings}settings=_.defaults({},settings,_.templateSettings);var matcher=RegExp([(settings.escape||noMatch).source,(settings.interpolate||noMatch).source,(settings.evaluate||noMatch).source].join("|")+"|$","g");var index=0;var source="__p+='";text.replace(matcher,function(match,escape,interpolate,evaluate,offset){source+=text.slice(index,offset).replace(escaper,escapeChar);index=offset+match.length;if(escape){source+="'+\n((__t=("+escape+"))==null?'':_.escape(__t))+\n'"}else{if(interpolate){source+="'+\n((__t=("+interpolate+"))==null?'':__t)+\n'"}else{if(evaluate){source+="';\n"+evaluate+"\n__p+='"}}}return match});source+="';\n";if(!settings.variable){source="with(obj||{}){\n"+source+"}\n"}source="var __t,__p='',__j=Array.prototype.join,"+"print=function(){__p+=__j.call(arguments,'');};\n"+source+"return __p;\n";try{var render=new Function(settings.variable||"obj","_",source)}catch(e){e.source=source;throw e}var template=function(data){return render.call(this,data,_)};var argument=settings.variable||"obj";template.source="function("+argument+"){\n"+source+"}";return template};_.chain=function(obj){var instance=_(obj);
instance._chain=true;return instance};var result=function(instance,obj){return instance._chain?_(obj).chain():obj};_.mixin=function(obj){_.each(_.functions(obj),function(name){var func=_[name]=obj[name];_.prototype[name]=function(){var args=[this._wrapped];push.apply(args,arguments);return result(this,func.apply(_,args))}})};_.mixin(_);_.each(["pop","push","reverse","shift","sort","splice","unshift"],function(name){var method=ArrayProto[name];_.prototype[name]=function(){var obj=this._wrapped;method.apply(obj,arguments);if((name==="shift"||name==="splice")&&obj.length===0){delete obj[0]}return result(this,obj)}});_.each(["concat","join","slice"],function(name){var method=ArrayProto[name];_.prototype[name]=function(){return result(this,method.apply(this._wrapped,arguments))}});_.prototype.value=function(){return this._wrapped};_.prototype.valueOf=_.prototype.toJSON=_.prototype.value;_.prototype.toString=function(){return""+this._wrapped};if(true){!(__WEBPACK_AMD_DEFINE_ARRAY__=[],__WEBPACK_AMD_DEFINE_RESULT__=function(){return _}.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__),__WEBPACK_AMD_DEFINE_RESULT__!==undefined&&(module.exports=__WEBPACK_AMD_DEFINE_RESULT__))}}.call(this))},205:function(module,exports,__webpack_require__){var _typeof=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(obj){return typeof obj}:function(obj){return obj&&typeof Symbol==="function"&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj};(function(){var EMPTYFN=function EMPTYFN(){};var _code=__webpack_require__(206).code;var WEBIM_FILESIZE_LIMIT=10485760;var _createStandardXHR=function _createStandardXHR(){try{return new window.XMLHttpRequest()}catch(e){return false}};var _createActiveXHR=function _createActiveXHR(){try{return new window.ActiveXObject("Microsoft.XMLHTTP")}catch(e){return false}};var _xmlrequest=function _xmlrequest(crossDomain){crossDomain=crossDomain||true;var temp=_createStandardXHR()||_createActiveXHR();if("withCredentials" in temp){return temp}if(!crossDomain){return temp}if(typeof window.XDomainRequest==="undefined"){return temp}var xhr=new XDomainRequest();xhr.readyState=0;xhr.status=100;xhr.onreadystatechange=EMPTYFN;xhr.onload=function(){xhr.readyState=4;xhr.status=200;var xmlDoc=new ActiveXObject("Microsoft.XMLDOM");xmlDoc.async="false";xmlDoc.loadXML(xhr.responseText);xhr.responseXML=xmlDoc;xhr.response=xhr.responseText;xhr.onreadystatechange()};xhr.ontimeout=xhr.onerror=function(){xhr.readyState=4;xhr.status=500;xhr.onreadystatechange()};return xhr};var _hasFlash=function(){if("ActiveXObject" in window){try{return new ActiveXObject("ShockwaveFlash.ShockwaveFlash")}catch(ex){return 0}}else{if(navigator.plugins&&navigator.plugins.length>0){return navigator.plugins["Shockwave Flash"]}}return 0}();var _tmpUtilXHR=_xmlrequest(),_hasFormData=typeof FormData!=="undefined",_hasBlob=typeof Blob!=="undefined",_isCanSetRequestHeader=_tmpUtilXHR.setRequestHeader||false,_hasOverrideMimeType=_tmpUtilXHR.overrideMimeType||false,_isCanUploadFileAsync=_isCanSetRequestHeader&&_hasFormData,_isCanUploadFile=_isCanUploadFileAsync||_hasFlash,_isCanDownLoadFile=_isCanSetRequestHeader&&(_hasBlob||_hasOverrideMimeType);if(!Object.keys){Object.keys=function(){var hasOwnProperty=Object.prototype.hasOwnProperty,hasDontEnumBug=!{toString:null}.propertyIsEnumerable("toString"),dontEnums=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],dontEnumsLength=dontEnums.length;return function(obj){if((typeof obj==="undefined"?"undefined":_typeof(obj))!=="object"&&(typeof obj!=="function"||obj===null)){throw new TypeError("Object.keys called on non-object")}var result=[],prop,i;for(prop in obj){if(hasOwnProperty.call(obj,prop)){result.push(prop)}}if(hasDontEnumBug){for(i=0;i<dontEnumsLength;i++){if(hasOwnProperty.call(obj,dontEnums[i])){result.push(dontEnums[i])}}}return result}}()}var utils={hasFormData:_hasFormData,hasBlob:_hasBlob,emptyfn:EMPTYFN,isCanSetRequestHeader:_isCanSetRequestHeader,hasOverrideMimeType:_hasOverrideMimeType,isCanUploadFileAsync:_isCanUploadFileAsync,isCanUploadFile:_isCanUploadFile,isCanDownLoadFile:_isCanDownLoadFile,isSupportWss:function(){var notSupportList=[/MQQBrowser[\/]5([.]\d+)?\sTBS/];if(!window.WebSocket){return false}var ua=window.navigator.userAgent;for(var i=0,l=notSupportList.length;i<l;i++){if(notSupportList[i].test(ua)){return false}}return true}(),getIEVersion:function(){var ua=navigator.userAgent,matches,tridentMap={"4":8,"5":9,"6":10,"7":11};matches=ua.match(/MSIE (\d+)/i);if(matches&&matches[1]){return +matches[1]}matches=ua.match(/Trident\/(\d+)/i);if(matches&&matches[1]){return tridentMap[matches[1]]||null}return null}(),stringify:function stringify(json){if(typeof JSON!=="undefined"&&JSON.stringify){return JSON.stringify(json)}else{var s="",arr=[];var iterate=function iterate(json){var isArr=false;if(Object.prototype.toString.call(json)==="[object Array]"){arr.push("]","[");isArr=true}else{if(Object.prototype.toString.call(json)==="[object Object]"){arr.push("}","{")
}}for(var o in json){if(Object.prototype.toString.call(json[o])==="[object Null]"){json[o]="null"}else{if(Object.prototype.toString.call(json[o])==="[object Undefined]"){json[o]="undefined"}}if(json[o]&&_typeof(json[o])==="object"){s+=","+(isArr?"":'"'+o+'":'+(isArr?'"':""))+iterate(json[o])+""}else{s+=',"'+(isArr?"":o+'":"')+json[o]+'"'}}if(s!=""){s=s.slice(1)}return arr.pop()+s+arr.pop()};return iterate(json)}},login:function login(options){var options=options||{};var suc=options.success||EMPTYFN;var err=options.error||EMPTYFN;var appKey=options.appKey||"";var devInfos=appKey.split("#");if(devInfos.length!==2){err({type:_code.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR});return false}var orgName=devInfos[0];var appName=devInfos[1];var https=https||options.https;var user=options.user||"";var pwd=options.pwd||"";var apiUrl=options.apiUrl;var loginJson={grant_type:"password",username:user,password:pwd,timestamp:+new Date()};var loginfo=utils.stringify(loginJson);var options={url:apiUrl+"/"+orgName+"/"+appName+"/token",dataType:"json",data:loginfo,success:suc,error:err};return utils.ajax(options)},getFileUrl:function getFileUrl(fileInputId){var uri={url:"",filename:"",filetype:"",data:""};var fileObj=typeof fileInputId==="string"?document.getElementById(fileInputId):fileInputId;if(!utils.isCanUploadFileAsync||!fileObj){return uri}try{if(window.URL.createObjectURL){var fileItems=fileObj.files;if(fileItems.length>0){var u=fileItems.item(0);uri.data=u;uri.url=window.URL.createObjectURL(u);uri.filename=u.name||""}}else{var u=document.getElementById(fileInputId).value;uri.url=u;var pos1=u.lastIndexOf("/");var pos2=u.lastIndexOf("\\");var pos=Math.max(pos1,pos2);if(pos<0){uri.filename=u}else{uri.filename=u.substring(pos+1)}}var index=uri.filename.lastIndexOf(".");if(index!=-1){uri.filetype=uri.filename.substring(index+1).toLowerCase()}return uri}catch(e){throw e}},getFileSize:function getFileSize(file){var fileSize=this.getFileLength(file);if(fileSize>10000000){return false}var kb=Math.round(fileSize/1000);if(kb<1000){fileSize=kb+" KB"}else{if(kb>=1000){var mb=kb/1000;if(mb<1000){fileSize=mb.toFixed(1)+" MB"}else{var gb=mb/1000;fileSize=gb.toFixed(1)+" GB"}}}return fileSize},getFileLength:function getFileLength(file){var fileLength=0;if(file){if(file.files){if(file.files.length>0){fileLength=file.files[0].size}}else{if(file.select&&"ActiveXObject" in window){file.select();var fileobject=new ActiveXObject("Scripting.FileSystemObject");var file=fileobject.GetFile(file.value);fileLength=file.Size}}}return fileLength},hasFlash:_hasFlash,trim:function trim(str){str=typeof str==="string"?str:"";return str.trim?str.trim():str.replace(/^\s|\s$/g,"")},parseEmoji:function parseEmoji(msg){if(typeof WebIM.Emoji==="undefined"||typeof WebIM.Emoji.map==="undefined"){return msg}else{var emoji=WebIM.Emoji,reg=null;for(var face in emoji.map){if(emoji.map.hasOwnProperty(face)){while(msg.indexOf(face)>-1){msg=msg.replace(face,'<img class="emoji" src="'+emoji.path+emoji.map[face]+'" />')}}}return msg}},parseLink:function parseLink(msg){var reg=/(https?\:\/\/|www\.)([a-zA-Z0-9-]+(\.[a-zA-Z0-9]+)+)(\:[0-9]{2,4})?\/?((\.[:_0-9a-zA-Z-]+)|[:_0-9a-zA-Z-]*\/?)*\??[:_#@*&%0-9a-zA-Z-/=]*/gm;msg=msg.replace(reg,function(v){var prefix=/^https?/gm.test(v);return"<a href='"+(prefix?v:"//"+v)+"' target='_blank'>"+v+"</a>"});return msg},parseJSON:function parseJSON(data){if(window.JSON&&window.JSON.parse){return window.JSON.parse(data+"")}var requireNonComma,depth=null,str=utils.trim(data+"");return str&&!utils.trim(str.replace(/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g,function(token,comma,open,close){if(requireNonComma&&comma){depth=0}if(depth===0){return token}requireNonComma=open||comma;depth+=!close-!open;return""}))?Function("return "+str)():Function("Invalid JSON: "+data)()},parseUploadResponse:function parseUploadResponse(response){return response.indexOf("callback")>-1?response.slice(9,-1):response},parseDownloadResponse:function parseDownloadResponse(response){return response&&response.type&&response.type==="application/json"||0>Object.prototype.toString.call(response).indexOf("Blob")?this.url+"?token=":window.URL.createObjectURL(response)},uploadFile:function uploadFile(options){var options=options||{};options.onFileUploadProgress=options.onFileUploadProgress||EMPTYFN;options.onFileUploadComplete=options.onFileUploadComplete||EMPTYFN;options.onFileUploadError=options.onFileUploadError||EMPTYFN;options.onFileUploadCanceled=options.onFileUploadCanceled||EMPTYFN;var acc=options.accessToken||this.context.accessToken;if(!acc){options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_NO_LOGIN,id:options.id});return}var orgName,appName,devInfos;var appKey=options.appKey||this.context.appKey||"";if(appKey){devInfos=appKey.split("#");orgName=devInfos[0];appName=devInfos[1]}if(!orgName&&!appName){options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_ERROR,id:options.id});
return}var apiUrl=options.apiUrl;var uploadUrl=apiUrl+"/"+orgName+"/"+appName+"/chatfiles";if(!utils.isCanUploadFileAsync){if(utils.hasFlash&&typeof options.flashUpload==="function"){options.flashUpload&&options.flashUpload(uploadUrl,options)}else{options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_BROWSER_ERROR,id:options.id})}return}var fileSize=options.file.data?options.file.data.size:undefined;if(fileSize>WEBIM_FILESIZE_LIMIT){options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_ERROR,id:options.id});return}else{if(fileSize<=0){options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_ERROR,id:options.id});return}}var xhr=utils.xmlrequest();var onError=function onError(e){options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_ERROR,id:options.id,xhr:xhr})};if(xhr.upload){xhr.upload.addEventListener("progress",options.onFileUploadProgress,false)}if(xhr.addEventListener){xhr.addEventListener("abort",options.onFileUploadCanceled,false);xhr.addEventListener("load",function(e){try{var json=utils.parseJSON(xhr.responseText);try{options.onFileUploadComplete(json)}catch(e){options.onFileUploadError({type:_code.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:e})}}catch(e){options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_ERROR,data:xhr.responseText,id:options.id,xhr:xhr})}},false);xhr.addEventListener("error",onError,false)}else{if(xhr.onreadystatechange){xhr.onreadystatechange=function(){if(xhr.readyState===4){if(ajax.status===200){try{var json=utils.parseJSON(xhr.responseText);options.onFileUploadComplete(json)}catch(e){options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_ERROR,data:xhr.responseText,id:options.id,xhr:xhr})}}else{options.onFileUploadError({type:_code.WEBIM_UPLOADFILE_ERROR,data:xhr.responseText,id:options.id,xhr:xhr})}}else{xhr.abort();options.onFileUploadCanceled()}}}}xhr.open("POST",uploadUrl);xhr.setRequestHeader("restrict-access","true");xhr.setRequestHeader("Accept","*/*");xhr.setRequestHeader("Authorization","Bearer "+acc);var formData=new FormData();formData.append("file",options.file.data);xhr.send(formData)},download:function download(options){options.onFileDownloadComplete=options.onFileDownloadComplete||EMPTYFN;options.onFileDownloadError=options.onFileDownloadError||EMPTYFN;var accessToken=options.accessToken||this.context.accessToken;if(!accessToken){options.onFileDownloadError({type:_code.WEBIM_DOWNLOADFILE_NO_LOGIN,id:options.id});return}var onError=function onError(e){options.onFileDownloadError({type:_code.WEBIM_DOWNLOADFILE_ERROR,id:options.id,xhr:xhr})};if(!utils.isCanDownLoadFile){options.onFileDownloadComplete();return}var xhr=utils.xmlrequest();if("addEventListener" in xhr){xhr.addEventListener("load",function(e){options.onFileDownloadComplete(xhr.response,xhr)},false);xhr.addEventListener("error",onError,false)}else{if("onreadystatechange" in xhr){xhr.onreadystatechange=function(){if(xhr.readyState===4){if(ajax.status===200){options.onFileDownloadComplete(xhr.response,xhr)}else{options.onFileDownloadError({type:_code.WEBIM_DOWNLOADFILE_ERROR,id:options.id,xhr:xhr})}}else{xhr.abort();options.onFileDownloadError({type:_code.WEBIM_DOWNLOADFILE_ERROR,id:options.id,xhr:xhr})}}}}var method=options.method||"GET";var resType=options.responseType||"blob";var mimeType=options.mimeType||"text/plain; charset=x-user-defined";xhr.open(method,options.url);if(typeof Blob!=="undefined"){xhr.responseType=resType}else{xhr.overrideMimeType(mimeType)}var innerHeaer={"X-Requested-With":"XMLHttpRequest","Accept":"application/octet-stream","share-secret":options.secret,"Authorization":"Bearer "+accessToken};var headers=options.headers||{};for(var key in headers){innerHeaer[key]=headers[key]}for(var key in innerHeaer){if(innerHeaer[key]){xhr.setRequestHeader(key,innerHeaer[key])}}xhr.send(null)},parseTextMessage:function parseTextMessage(message,faces){if(typeof message!=="string"){return}if(Object.prototype.toString.call(faces)!=="[object Object]"){return{isemoji:false,body:[{type:"txt",data:message}]}}var receiveMsg=message;var emessage=[];var expr=/\[[^[\]]{2,3}\]/mg;var emoji=receiveMsg.match(expr);if(!emoji||emoji.length<1){return{isemoji:false,body:[{type:"txt",data:message}]}}var isemoji=false;for(var i=0;i<emoji.length;i++){var tmsg=receiveMsg.substring(0,receiveMsg.indexOf(emoji[i])),existEmoji=WebIM.Emoji.map[emoji[i]];if(tmsg){emessage.push({type:"txt",data:tmsg})}if(!existEmoji){emessage.push({type:"txt",data:emoji[i]});continue}var emojiStr=WebIM.Emoji.map?WebIM.Emoji.path+existEmoji:null;if(emojiStr){isemoji=true;emessage.push({type:"emoji",data:emojiStr})}else{emessage.push({type:"txt",data:emoji[i]})}var restMsgIndex=receiveMsg.indexOf(emoji[i])+emoji[i].length;receiveMsg=receiveMsg.substring(restMsgIndex)}if(receiveMsg){emessage.push({type:"txt",data:receiveMsg})}if(isemoji){return{isemoji:isemoji,body:emessage}}return{isemoji:false,body:[{type:"txt",data:message}]}},parseUri:function parseUri(){var pattern=/([^\?|&])\w+=([^&]+)/g;var uri={};if(window.location.search){var args=window.location.search.match(pattern);
for(var i in args){var str=args[i];var eq=str.indexOf("=");var key=str.substr(0,eq);var value=str.substr(eq+1);uri[key]=value}}return uri},parseHrefHash:function parseHrefHash(){var pattern=/([^\#|&])\w+=([^&]+)/g;var uri={};if(window.location.hash){var args=window.location.hash.match(pattern);for(var i in args){var str=args[i];var eq=str.indexOf("=");var key=str.substr(0,eq);var value=str.substr(eq+1);uri[key]=value}}return uri},xmlrequest:_xmlrequest,getXmlFirstChild:function getXmlFirstChild(data,tagName){var children=data.getElementsByTagName(tagName);if(children.length==0){return null}else{return children[0]}},ajax:function ajax(options){var dataType=options.dataType||"text";var suc=options.success||EMPTYFN;var error=options.error||EMPTYFN;var xhr=utils.xmlrequest();xhr.onreadystatechange=function(){if(xhr.readyState===4){var status=xhr.status||0;if(status===200){try{switch(dataType){case"text":suc(xhr.responseText);return;case"json":var json=utils.parseJSON(xhr.responseText);suc(json,xhr);return;case"xml":if(xhr.responseXML&&xhr.responseXML.documentElement){suc(xhr.responseXML.documentElement,xhr)}else{error({type:_code.WEBIM_CONNCTION_AJAX_ERROR,data:xhr.responseText})}return}suc(xhr.response||xhr.responseText,xhr)}catch(e){error({type:_code.WEBIM_CONNCTION_AJAX_ERROR,data:e})}return}else{error({type:_code.WEBIM_CONNCTION_AJAX_ERROR,data:xhr.responseText});return}}if(xhr.readyState===0){error({type:_code.WEBIM_CONNCTION_AJAX_ERROR,data:xhr.responseText})}};if(options.responseType){if(xhr.responseType){xhr.responseType=options.responseType}}if(options.mimeType){if(utils.hasOverrideMimeType){xhr.overrideMimeType(options.mimeType)}}var type=options.type||"POST",data=options.data||null,tempData="";if(type.toLowerCase()==="get"&&data){for(var o in data){if(data.hasOwnProperty(o)){tempData+=o+"="+data[o]+"&"}}tempData=tempData?tempData.slice(0,-1):tempData;options.url+=(options.url.indexOf("?")>0?"&":"?")+(tempData?tempData+"&":tempData)+"_v="+new Date().getTime();data=null;tempData=null}xhr.open(type,options.url);if(utils.isCanSetRequestHeader){var headers=options.headers||{};for(var key in headers){if(headers.hasOwnProperty(key)){xhr.setRequestHeader(key,headers[key])}}}xhr.send(data);return xhr},ts:function ts(){var d=new Date();var Hours=d.getHours();var Minutes=d.getMinutes();var Seconds=d.getSeconds();var Milliseconds=d.getMilliseconds();return(Hours<10?"0"+Hours:Hours)+":"+(Minutes<10?"0"+Minutes:Minutes)+":"+(Seconds<10?"0"+Seconds:Seconds)+":"+Milliseconds+" "},getObjectKey:function getObjectKey(obj,val){for(var key in obj){if(obj[key]==val){return key}}return""},sprintf:function sprintf(){var arg=arguments,str=arg[0]||"",i,len;for(i=1,len=arg.length;i<len;i++){str=str.replace(/%s/,arg[i])}return str},setCookie:function setCookie(name,value,days){var cookie=name+"="+encodeURIComponent(value);if(typeof days=="number"){cookie+="; max-age: "+days*60*60*24}document.cookie=cookie},getCookie:function getCookie(){var allCookie={};var all=document.cookie;if(all===""){return allCookie}var list=all.split("; ");for(var i=0;i<list.length;i++){var cookie=list[i];var p=cookie.indexOf("=");var name=cookie.substring(0,p);var value=cookie.substring(p+1);value=decodeURIComponent(value);allCookie[name]=value}return allCookie}};exports.utils=utils})()},206:function(module,exports){(function(){exports.code={WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR:0,WEBIM_CONNCTION_OPEN_ERROR:1,WEBIM_CONNCTION_AUTH_ERROR:2,WEBIM_CONNCTION_OPEN_USERGRID_ERROR:3,WEBIM_CONNCTION_ATTACH_ERROR:4,WEBIM_CONNCTION_ATTACH_USERGRID_ERROR:5,WEBIM_CONNCTION_REOPEN_ERROR:6,WEBIM_CONNCTION_SERVER_CLOSE_ERROR:7,WEBIM_CONNCTION_SERVER_ERROR:8,WEBIM_CONNCTION_IQ_ERROR:9,WEBIM_CONNCTION_PING_ERROR:10,WEBIM_CONNCTION_NOTIFYVERSION_ERROR:11,WEBIM_CONNCTION_GETROSTER_ERROR:12,WEBIM_CONNCTION_CROSSDOMAIN_ERROR:13,WEBIM_CONNCTION_LISTENING_OUTOF_MAXRETRIES:14,WEBIM_CONNCTION_RECEIVEMSG_CONTENTERROR:15,WEBIM_CONNCTION_DISCONNECTED:16,WEBIM_CONNCTION_AJAX_ERROR:17,WEBIM_CONNCTION_JOINROOM_ERROR:18,WEBIM_CONNCTION_GETROOM_ERROR:19,WEBIM_CONNCTION_GETROOMINFO_ERROR:20,WEBIM_CONNCTION_GETROOMMEMBER_ERROR:21,WEBIM_CONNCTION_GETROOMOCCUPANTS_ERROR:22,WEBIM_CONNCTION_LOAD_CHATROOM_ERROR:23,WEBIM_CONNCTION_NOT_SUPPORT_CHATROOM_ERROR:24,WEBIM_CONNCTION_JOINCHATROOM_ERROR:25,WEBIM_CONNCTION_QUITCHATROOM_ERROR:26,WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR:27,WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR:28,WEBIM_CONNCTION_SESSIONID_NOT_ASSIGN_ERROR:29,WEBIM_CONNCTION_RID_NOT_ASSIGN_ERROR:30,WEBIM_CONNCTION_CALLBACK_INNER_ERROR:31,WEBIM_CONNCTION_CLIENT_OFFLINE:32,WEBIM_CONNCTION_CLIENT_LOGOUT:33,WEBIM_CONNCTION_CLIENT_TOO_MUCH_ERROR:34,WEBIM_CONNECTION_ACCEPT_INVITATION_FROM_GROUP:35,WEBIM_CONNECTION_DECLINE_INVITATION_FROM_GROUP:36,WEBIM_CONNECTION_ACCEPT_JOIN_GROUP:37,WEBIM_CONNECTION_DECLINE_JOIN_GROUP:38,WEBIM_CONNECTION_CLOSED:39,WEBIM_UPLOADFILE_BROWSER_ERROR:100,WEBIM_UPLOADFILE_ERROR:101,WEBIM_UPLOADFILE_NO_LOGIN:102,WEBIM_UPLOADFILE_NO_FILE:103,WEBIM_DOWNLOADFILE_ERROR:200,WEBIM_DOWNLOADFILE_NO_LOGIN:201,WEBIM_DOWNLOADFILE_BROWSER_ERROR:202,WEBIM_MESSAGE_REC_TEXT:300,WEBIM_MESSAGE_REC_TEXT_ERROR:301,WEBIM_MESSAGE_REC_EMOTION:302,WEBIM_MESSAGE_REC_PHOTO:303,WEBIM_MESSAGE_REC_AUDIO:304,WEBIM_MESSAGE_REC_AUDIO_FILE:305,WEBIM_MESSAGE_REC_VEDIO:306,WEBIM_MESSAGE_REC_VEDIO_FILE:307,WEBIM_MESSAGE_REC_FILE:308,WEBIM_MESSAGE_SED_TEXT:309,WEBIM_MESSAGE_SED_EMOTION:310,WEBIM_MESSAGE_SED_PHOTO:311,WEBIM_MESSAGE_SED_AUDIO:312,WEBIM_MESSAGE_SED_AUDIO_FILE:313,WEBIM_MESSAGE_SED_VEDIO:314,WEBIM_MESSAGE_SED_VEDIO_FILE:315,WEBIM_MESSAGE_SED_FILE:316,WEBIM_MESSAGE_SED_ERROR:317,STATUS_INIT:400,STATUS_DOLOGIN_USERGRID:401,STATUS_DOLOGIN_IM:402,STATUS_OPENED:403,STATUS_CLOSING:404,STATUS_CLOSED:405,STATUS_ERROR:406}
})()},210:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(212),__webpack_require__(213),__webpack_require__(214),__webpack_require__(215),__webpack_require__(216),__webpack_require__(217),__webpack_require__(218),__webpack_require__(219),__webpack_require__(220),__webpack_require__(221),__webpack_require__(222),__webpack_require__(223),__webpack_require__(224),__webpack_require__(225),__webpack_require__(226),__webpack_require__(227),__webpack_require__(228),__webpack_require__(229),__webpack_require__(230),__webpack_require__(231),__webpack_require__(232),__webpack_require__(233),__webpack_require__(234),__webpack_require__(235),__webpack_require__(236),__webpack_require__(237),__webpack_require__(238),__webpack_require__(239),__webpack_require__(240),__webpack_require__(241),__webpack_require__(242),__webpack_require__(243))}else{if(typeof define==="function"&&define.amd){define(["./core","./x64-core","./lib-typedarrays","./enc-utf16","./enc-base64","./md5","./sha1","./sha256","./sha224","./sha512","./sha384","./sha3","./ripemd160","./hmac","./pbkdf2","./evpkdf","./cipher-core","./mode-cfb","./mode-ctr","./mode-ctr-gladman","./mode-ofb","./mode-ecb","./pad-ansix923","./pad-iso10126","./pad-iso97971","./pad-zeropadding","./pad-nopadding","./format-hex","./aes","./tripledes","./rc4","./rabbit","./rabbit-legacy"],factory)}else{root.CryptoJS=factory(root.CryptoJS)}}}(this,function(CryptoJS){return CryptoJS}))},211:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory()}else{if(typeof define==="function"&&define.amd){define([],factory)}else{root.CryptoJS=factory()}}}(this,function(){var CryptoJS=CryptoJS||(function(Math,undefined){var create=Object.create||(function(){function F(){}return function(obj){var subtype;F.prototype=obj;subtype=new F();F.prototype=null;return subtype}}());var C={};var C_lib=C.lib={};var Base=C_lib.Base=(function(){return{extend:function(overrides){var subtype=create(this);if(overrides){subtype.mixIn(overrides)}if(!subtype.hasOwnProperty("init")||this.init===subtype.init){subtype.init=function(){subtype.$super.init.apply(this,arguments)}}subtype.init.prototype=subtype;subtype.$super=this;return subtype},create:function(){var instance=this.extend();instance.init.apply(instance,arguments);return instance},init:function(){},mixIn:function(properties){for(var propertyName in properties){if(properties.hasOwnProperty(propertyName)){this[propertyName]=properties[propertyName]}}if(properties.hasOwnProperty("toString")){this.toString=properties.toString}},clone:function(){return this.init.prototype.extend(this)}}}());var WordArray=C_lib.WordArray=Base.extend({init:function(words,sigBytes){words=this.words=words||[];if(sigBytes!=undefined){this.sigBytes=sigBytes}else{this.sigBytes=words.length*4}},toString:function(encoder){return(encoder||Hex).stringify(this)},concat:function(wordArray){var thisWords=this.words;var thatWords=wordArray.words;var thisSigBytes=this.sigBytes;var thatSigBytes=wordArray.sigBytes;this.clamp();if(thisSigBytes%4){for(var i=0;i<thatSigBytes;i++){var thatByte=(thatWords[i>>>2]>>>(24-(i%4)*8))&255;thisWords[(thisSigBytes+i)>>>2]|=thatByte<<(24-((thisSigBytes+i)%4)*8)}}else{for(var i=0;i<thatSigBytes;i+=4){thisWords[(thisSigBytes+i)>>>2]=thatWords[i>>>2]}}this.sigBytes+=thatSigBytes;return this},clamp:function(){var words=this.words;var sigBytes=this.sigBytes;words[sigBytes>>>2]&=4294967295<<(32-(sigBytes%4)*8);words.length=Math.ceil(sigBytes/4)},clone:function(){var clone=Base.clone.call(this);clone.words=this.words.slice(0);return clone},random:function(nBytes){var words=[];var r=(function(m_w){var m_w=m_w;var m_z=987654321;var mask=4294967295;return function(){m_z=(36969*(m_z&65535)+(m_z>>16))&mask;m_w=(18000*(m_w&65535)+(m_w>>16))&mask;var result=((m_z<<16)+m_w)&mask;result/=4294967296;result+=0.5;return result*(Math.random()>0.5?1:-1)}});for(var i=0,rcache;i<nBytes;i+=4){var _r=r((rcache||Math.random())*4294967296);rcache=_r()*987654071;words.push((_r()*4294967296)|0)}return new WordArray.init(words,nBytes)}});var C_enc=C.enc={};var Hex=C_enc.Hex={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var hexChars=[];for(var i=0;i<sigBytes;i++){var bite=(words[i>>>2]>>>(24-(i%4)*8))&255;hexChars.push((bite>>>4).toString(16));hexChars.push((bite&15).toString(16))}return hexChars.join("")},parse:function(hexStr){var hexStrLength=hexStr.length;var words=[];for(var i=0;i<hexStrLength;i+=2){words[i>>>3]|=parseInt(hexStr.substr(i,2),16)<<(24-(i%8)*4)}return new WordArray.init(words,hexStrLength/2)}};var Latin1=C_enc.Latin1={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var latin1Chars=[];for(var i=0;i<sigBytes;i++){var bite=(words[i>>>2]>>>(24-(i%4)*8))&255;latin1Chars.push(String.fromCharCode(bite))}return latin1Chars.join("")},parse:function(latin1Str){var latin1StrLength=latin1Str.length;
var words=[];for(var i=0;i<latin1StrLength;i++){words[i>>>2]|=(latin1Str.charCodeAt(i)&255)<<(24-(i%4)*8)}return new WordArray.init(words,latin1StrLength)}};var Utf8=C_enc.Utf8={stringify:function(wordArray){try{return decodeURIComponent(escape(Latin1.stringify(wordArray)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(utf8Str){return Latin1.parse(unescape(encodeURIComponent(utf8Str)))}};var BufferedBlockAlgorithm=C_lib.BufferedBlockAlgorithm=Base.extend({reset:function(){this._data=new WordArray.init();this._nDataBytes=0},_append:function(data){if(typeof data=="string"){data=Utf8.parse(data)}this._data.concat(data);this._nDataBytes+=data.sigBytes},_process:function(doFlush){var data=this._data;var dataWords=data.words;var dataSigBytes=data.sigBytes;var blockSize=this.blockSize;var blockSizeBytes=blockSize*4;var nBlocksReady=dataSigBytes/blockSizeBytes;if(doFlush){nBlocksReady=Math.ceil(nBlocksReady)}else{nBlocksReady=Math.max((nBlocksReady|0)-this._minBufferSize,0)}var nWordsReady=nBlocksReady*blockSize;var nBytesReady=Math.min(nWordsReady*4,dataSigBytes);if(nWordsReady){for(var offset=0;offset<nWordsReady;offset+=blockSize){this._doProcessBlock(dataWords,offset)}var processedWords=dataWords.splice(0,nWordsReady);data.sigBytes-=nBytesReady}return new WordArray.init(processedWords,nBytesReady)},clone:function(){var clone=Base.clone.call(this);clone._data=this._data.clone();return clone},_minBufferSize:0});var Hasher=C_lib.Hasher=BufferedBlockAlgorithm.extend({cfg:Base.extend(),init:function(cfg){this.cfg=this.cfg.extend(cfg);this.reset()},reset:function(){BufferedBlockAlgorithm.reset.call(this);this._doReset()},update:function(messageUpdate){this._append(messageUpdate);this._process();return this},finalize:function(messageUpdate){if(messageUpdate){this._append(messageUpdate)}var hash=this._doFinalize();return hash},blockSize:512/32,_createHelper:function(hasher){return function(message,cfg){return new hasher.init(cfg).finalize(message)}},_createHmacHelper:function(hasher){return function(message,key){return new C_algo.HMAC.init(hasher,key).finalize(message)}}});var C_algo=C.algo={};return C}(Math));return CryptoJS}))},212:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(undefined){var C=CryptoJS;var C_lib=C.lib;var Base=C_lib.Base;var X32WordArray=C_lib.WordArray;var C_x64=C.x64={};var X64Word=C_x64.Word=Base.extend({init:function(high,low){this.high=high;this.low=low}});var X64WordArray=C_x64.WordArray=Base.extend({init:function(words,sigBytes){words=this.words=words||[];if(sigBytes!=undefined){this.sigBytes=sigBytes}else{this.sigBytes=words.length*8}},toX32:function(){var x64Words=this.words;var x64WordsLength=x64Words.length;var x32Words=[];for(var i=0;i<x64WordsLength;i++){var x64Word=x64Words[i];x32Words.push(x64Word.high);x32Words.push(x64Word.low)}return X32WordArray.create(x32Words,this.sigBytes)},clone:function(){var clone=Base.clone.call(this);var words=clone.words=this.words.slice(0);var wordsLength=words.length;for(var i=0;i<wordsLength;i++){words[i]=words[i].clone()}return clone}})}());return CryptoJS}))},213:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){if(typeof ArrayBuffer!="function"){return}var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var superInit=WordArray.init;var subInit=WordArray.init=function(typedArray){if(typedArray instanceof ArrayBuffer){typedArray=new Uint8Array(typedArray)}if(typedArray instanceof Int8Array||(typeof Uint8ClampedArray!=="undefined"&&typedArray instanceof Uint8ClampedArray)||typedArray instanceof Int16Array||typedArray instanceof Uint16Array||typedArray instanceof Int32Array||typedArray instanceof Uint32Array||typedArray instanceof Float32Array||typedArray instanceof Float64Array){typedArray=new Uint8Array(typedArray.buffer,typedArray.byteOffset,typedArray.byteLength)}if(typedArray instanceof Uint8Array){var typedArrayByteLength=typedArray.byteLength;var words=[];for(var i=0;i<typedArrayByteLength;i++){words[i>>>2]|=typedArray[i]<<(24-(i%4)*8)}superInit.call(this,words,typedArrayByteLength)}else{superInit.apply(this,arguments)}};subInit.prototype=WordArray}());return CryptoJS.lib.WordArray}))},214:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var C_enc=C.enc;var Utf16BE=C_enc.Utf16=C_enc.Utf16BE={stringify:function(wordArray){var words=wordArray.words;
var sigBytes=wordArray.sigBytes;var utf16Chars=[];for(var i=0;i<sigBytes;i+=2){var codePoint=(words[i>>>2]>>>(16-(i%4)*8))&65535;utf16Chars.push(String.fromCharCode(codePoint))}return utf16Chars.join("")},parse:function(utf16Str){var utf16StrLength=utf16Str.length;var words=[];for(var i=0;i<utf16StrLength;i++){words[i>>>1]|=utf16Str.charCodeAt(i)<<(16-(i%2)*16)}return WordArray.create(words,utf16StrLength*2)}};C_enc.Utf16LE={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var utf16Chars=[];for(var i=0;i<sigBytes;i+=2){var codePoint=swapEndian((words[i>>>2]>>>(16-(i%4)*8))&65535);utf16Chars.push(String.fromCharCode(codePoint))}return utf16Chars.join("")},parse:function(utf16Str){var utf16StrLength=utf16Str.length;var words=[];for(var i=0;i<utf16StrLength;i++){words[i>>>1]|=swapEndian(utf16Str.charCodeAt(i)<<(16-(i%2)*16))}return WordArray.create(words,utf16StrLength*2)}};function swapEndian(word){return((word<<8)&4278255360)|((word>>>8)&16711935)}}());return CryptoJS.enc.Utf16}))},215:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var C_enc=C.enc;var Base64=C_enc.Base64={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var map=this._map;wordArray.clamp();var base64Chars=[];for(var i=0;i<sigBytes;i+=3){var byte1=(words[i>>>2]>>>(24-(i%4)*8))&255;var byte2=(words[(i+1)>>>2]>>>(24-((i+1)%4)*8))&255;var byte3=(words[(i+2)>>>2]>>>(24-((i+2)%4)*8))&255;var triplet=(byte1<<16)|(byte2<<8)|byte3;for(var j=0;(j<4)&&(i+j*0.75<sigBytes);j++){base64Chars.push(map.charAt((triplet>>>(6*(3-j)))&63))}}var paddingChar=map.charAt(64);if(paddingChar){while(base64Chars.length%4){base64Chars.push(paddingChar)}}return base64Chars.join("")},parse:function(base64Str){var base64StrLength=base64Str.length;var map=this._map;var reverseMap=this._reverseMap;if(!reverseMap){reverseMap=this._reverseMap=[];for(var j=0;j<map.length;j++){reverseMap[map.charCodeAt(j)]=j}}var paddingChar=map.charAt(64);if(paddingChar){var paddingIndex=base64Str.indexOf(paddingChar);if(paddingIndex!==-1){base64StrLength=paddingIndex}}return parseLoop(base64Str,base64StrLength,reverseMap)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function parseLoop(base64Str,base64StrLength,reverseMap){var words=[];var nBytes=0;for(var i=0;i<base64StrLength;i++){if(i%4){var bits1=reverseMap[base64Str.charCodeAt(i-1)]<<((i%4)*2);var bits2=reverseMap[base64Str.charCodeAt(i)]>>>(6-(i%4)*2);words[nBytes>>>2]|=(bits1|bits2)<<(24-(nBytes%4)*8);nBytes++}}return WordArray.create(words,nBytes)}}());return CryptoJS.enc.Base64}))},216:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(Math){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C.algo;var T=[];(function(){for(var i=0;i<64;i++){T[i]=(Math.abs(Math.sin(i+1))*4294967296)|0}}());var MD5=C_algo.MD5=Hasher.extend({_doReset:function(){this._hash=new WordArray.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(M,offset){for(var i=0;i<16;i++){var offset_i=offset+i;var M_offset_i=M[offset_i];M[offset_i]=((((M_offset_i<<8)|(M_offset_i>>>24))&16711935)|(((M_offset_i<<24)|(M_offset_i>>>8))&4278255360))}var H=this._hash.words;var M_offset_0=M[offset+0];var M_offset_1=M[offset+1];var M_offset_2=M[offset+2];var M_offset_3=M[offset+3];var M_offset_4=M[offset+4];var M_offset_5=M[offset+5];var M_offset_6=M[offset+6];var M_offset_7=M[offset+7];var M_offset_8=M[offset+8];var M_offset_9=M[offset+9];var M_offset_10=M[offset+10];var M_offset_11=M[offset+11];var M_offset_12=M[offset+12];var M_offset_13=M[offset+13];var M_offset_14=M[offset+14];var M_offset_15=M[offset+15];var a=H[0];var b=H[1];var c=H[2];var d=H[3];a=FF(a,b,c,d,M_offset_0,7,T[0]);d=FF(d,a,b,c,M_offset_1,12,T[1]);c=FF(c,d,a,b,M_offset_2,17,T[2]);b=FF(b,c,d,a,M_offset_3,22,T[3]);a=FF(a,b,c,d,M_offset_4,7,T[4]);d=FF(d,a,b,c,M_offset_5,12,T[5]);c=FF(c,d,a,b,M_offset_6,17,T[6]);b=FF(b,c,d,a,M_offset_7,22,T[7]);a=FF(a,b,c,d,M_offset_8,7,T[8]);d=FF(d,a,b,c,M_offset_9,12,T[9]);c=FF(c,d,a,b,M_offset_10,17,T[10]);b=FF(b,c,d,a,M_offset_11,22,T[11]);a=FF(a,b,c,d,M_offset_12,7,T[12]);d=FF(d,a,b,c,M_offset_13,12,T[13]);c=FF(c,d,a,b,M_offset_14,17,T[14]);b=FF(b,c,d,a,M_offset_15,22,T[15]);a=GG(a,b,c,d,M_offset_1,5,T[16]);d=GG(d,a,b,c,M_offset_6,9,T[17]);c=GG(c,d,a,b,M_offset_11,14,T[18]);b=GG(b,c,d,a,M_offset_0,20,T[19]);a=GG(a,b,c,d,M_offset_5,5,T[20]);d=GG(d,a,b,c,M_offset_10,9,T[21]);c=GG(c,d,a,b,M_offset_15,14,T[22]);
b=GG(b,c,d,a,M_offset_4,20,T[23]);a=GG(a,b,c,d,M_offset_9,5,T[24]);d=GG(d,a,b,c,M_offset_14,9,T[25]);c=GG(c,d,a,b,M_offset_3,14,T[26]);b=GG(b,c,d,a,M_offset_8,20,T[27]);a=GG(a,b,c,d,M_offset_13,5,T[28]);d=GG(d,a,b,c,M_offset_2,9,T[29]);c=GG(c,d,a,b,M_offset_7,14,T[30]);b=GG(b,c,d,a,M_offset_12,20,T[31]);a=HH(a,b,c,d,M_offset_5,4,T[32]);d=HH(d,a,b,c,M_offset_8,11,T[33]);c=HH(c,d,a,b,M_offset_11,16,T[34]);b=HH(b,c,d,a,M_offset_14,23,T[35]);a=HH(a,b,c,d,M_offset_1,4,T[36]);d=HH(d,a,b,c,M_offset_4,11,T[37]);c=HH(c,d,a,b,M_offset_7,16,T[38]);b=HH(b,c,d,a,M_offset_10,23,T[39]);a=HH(a,b,c,d,M_offset_13,4,T[40]);d=HH(d,a,b,c,M_offset_0,11,T[41]);c=HH(c,d,a,b,M_offset_3,16,T[42]);b=HH(b,c,d,a,M_offset_6,23,T[43]);a=HH(a,b,c,d,M_offset_9,4,T[44]);d=HH(d,a,b,c,M_offset_12,11,T[45]);c=HH(c,d,a,b,M_offset_15,16,T[46]);b=HH(b,c,d,a,M_offset_2,23,T[47]);a=II(a,b,c,d,M_offset_0,6,T[48]);d=II(d,a,b,c,M_offset_7,10,T[49]);c=II(c,d,a,b,M_offset_14,15,T[50]);b=II(b,c,d,a,M_offset_5,21,T[51]);a=II(a,b,c,d,M_offset_12,6,T[52]);d=II(d,a,b,c,M_offset_3,10,T[53]);c=II(c,d,a,b,M_offset_10,15,T[54]);b=II(b,c,d,a,M_offset_1,21,T[55]);a=II(a,b,c,d,M_offset_8,6,T[56]);d=II(d,a,b,c,M_offset_15,10,T[57]);c=II(c,d,a,b,M_offset_6,15,T[58]);b=II(b,c,d,a,M_offset_13,21,T[59]);a=II(a,b,c,d,M_offset_4,6,T[60]);d=II(d,a,b,c,M_offset_11,10,T[61]);c=II(c,d,a,b,M_offset_2,15,T[62]);b=II(b,c,d,a,M_offset_9,21,T[63]);H[0]=(H[0]+a)|0;H[1]=(H[1]+b)|0;H[2]=(H[2]+c)|0;H[3]=(H[3]+d)|0},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<(24-nBitsLeft%32);var nBitsTotalH=Math.floor(nBitsTotal/4294967296);var nBitsTotalL=nBitsTotal;dataWords[(((nBitsLeft+64)>>>9)<<4)+15]=((((nBitsTotalH<<8)|(nBitsTotalH>>>24))&16711935)|(((nBitsTotalH<<24)|(nBitsTotalH>>>8))&4278255360));dataWords[(((nBitsLeft+64)>>>9)<<4)+14]=((((nBitsTotalL<<8)|(nBitsTotalL>>>24))&16711935)|(((nBitsTotalL<<24)|(nBitsTotalL>>>8))&4278255360));data.sigBytes=(dataWords.length+1)*4;this._process();var hash=this._hash;var H=hash.words;for(var i=0;i<4;i++){var H_i=H[i];H[i]=(((H_i<<8)|(H_i>>>24))&16711935)|(((H_i<<24)|(H_i>>>8))&4278255360)}return hash},clone:function(){var clone=Hasher.clone.call(this);clone._hash=this._hash.clone();return clone}});function FF(a,b,c,d,x,s,t){var n=a+((b&c)|(~b&d))+x+t;return((n<<s)|(n>>>(32-s)))+b}function GG(a,b,c,d,x,s,t){var n=a+((b&d)|(c&~d))+x+t;return((n<<s)|(n>>>(32-s)))+b}function HH(a,b,c,d,x,s,t){var n=a+(b^c^d)+x+t;return((n<<s)|(n>>>(32-s)))+b}function II(a,b,c,d,x,s,t){var n=a+(c^(b|~d))+x+t;return((n<<s)|(n>>>(32-s)))+b}C.MD5=Hasher._createHelper(MD5);C.HmacMD5=Hasher._createHmacHelper(MD5)}(Math));return CryptoJS.MD5}))},217:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C.algo;var W=[];var SHA1=C_algo.SHA1=Hasher.extend({_doReset:function(){this._hash=new WordArray.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(M,offset){var H=this._hash.words;var a=H[0];var b=H[1];var c=H[2];var d=H[3];var e=H[4];for(var i=0;i<80;i++){if(i<16){W[i]=M[offset+i]|0}else{var n=W[i-3]^W[i-8]^W[i-14]^W[i-16];W[i]=(n<<1)|(n>>>31)}var t=((a<<5)|(a>>>27))+e+W[i];if(i<20){t+=((b&c)|(~b&d))+1518500249}else{if(i<40){t+=(b^c^d)+1859775393}else{if(i<60){t+=((b&c)|(b&d)|(c&d))-1894007588}else{t+=(b^c^d)-899497514}}}e=d;d=c;c=(b<<30)|(b>>>2);b=a;a=t}H[0]=(H[0]+a)|0;H[1]=(H[1]+b)|0;H[2]=(H[2]+c)|0;H[3]=(H[3]+d)|0;H[4]=(H[4]+e)|0},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<(24-nBitsLeft%32);dataWords[(((nBitsLeft+64)>>>9)<<4)+14]=Math.floor(nBitsTotal/4294967296);dataWords[(((nBitsLeft+64)>>>9)<<4)+15]=nBitsTotal;data.sigBytes=dataWords.length*4;this._process();return this._hash},clone:function(){var clone=Hasher.clone.call(this);clone._hash=this._hash.clone();return clone}});C.SHA1=Hasher._createHelper(SHA1);C.HmacSHA1=Hasher._createHmacHelper(SHA1)}());return CryptoJS.SHA1}))},218:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(Math){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C.algo;var H=[];var K=[];(function(){function isPrime(n){var sqrtN=Math.sqrt(n);for(var factor=2;factor<=sqrtN;factor++){if(!(n%factor)){return false}}return true}function getFractionalBits(n){return((n-(n|0))*4294967296)|0}var n=2;var nPrime=0;
while(nPrime<64){if(isPrime(n)){if(nPrime<8){H[nPrime]=getFractionalBits(Math.pow(n,1/2))}K[nPrime]=getFractionalBits(Math.pow(n,1/3));nPrime++}n++}}());var W=[];var SHA256=C_algo.SHA256=Hasher.extend({_doReset:function(){this._hash=new WordArray.init(H.slice(0))},_doProcessBlock:function(M,offset){var H=this._hash.words;var a=H[0];var b=H[1];var c=H[2];var d=H[3];var e=H[4];var f=H[5];var g=H[6];var h=H[7];for(var i=0;i<64;i++){if(i<16){W[i]=M[offset+i]|0}else{var gamma0x=W[i-15];var gamma0=((gamma0x<<25)|(gamma0x>>>7))^((gamma0x<<14)|(gamma0x>>>18))^(gamma0x>>>3);var gamma1x=W[i-2];var gamma1=((gamma1x<<15)|(gamma1x>>>17))^((gamma1x<<13)|(gamma1x>>>19))^(gamma1x>>>10);W[i]=gamma0+W[i-7]+gamma1+W[i-16]}var ch=(e&f)^(~e&g);var maj=(a&b)^(a&c)^(b&c);var sigma0=((a<<30)|(a>>>2))^((a<<19)|(a>>>13))^((a<<10)|(a>>>22));var sigma1=((e<<26)|(e>>>6))^((e<<21)|(e>>>11))^((e<<7)|(e>>>25));var t1=h+sigma1+ch+K[i]+W[i];var t2=sigma0+maj;h=g;g=f;f=e;e=(d+t1)|0;d=c;c=b;b=a;a=(t1+t2)|0}H[0]=(H[0]+a)|0;H[1]=(H[1]+b)|0;H[2]=(H[2]+c)|0;H[3]=(H[3]+d)|0;H[4]=(H[4]+e)|0;H[5]=(H[5]+f)|0;H[6]=(H[6]+g)|0;H[7]=(H[7]+h)|0},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<(24-nBitsLeft%32);dataWords[(((nBitsLeft+64)>>>9)<<4)+14]=Math.floor(nBitsTotal/4294967296);dataWords[(((nBitsLeft+64)>>>9)<<4)+15]=nBitsTotal;data.sigBytes=dataWords.length*4;this._process();return this._hash},clone:function(){var clone=Hasher.clone.call(this);clone._hash=this._hash.clone();return clone}});C.SHA256=Hasher._createHelper(SHA256);C.HmacSHA256=Hasher._createHmacHelper(SHA256)}(Math));return CryptoJS.SHA256}))},219:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(218))}else{if(typeof define==="function"&&define.amd){define(["./core","./sha256"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var C_algo=C.algo;var SHA256=C_algo.SHA256;var SHA224=C_algo.SHA224=SHA256.extend({_doReset:function(){this._hash=new WordArray.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var hash=SHA256._doFinalize.call(this);hash.sigBytes-=4;return hash}});C.SHA224=SHA256._createHelper(SHA224);C.HmacSHA224=SHA256._createHmacHelper(SHA224)}());return CryptoJS.SHA224}))},220:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(212))}else{if(typeof define==="function"&&define.amd){define(["./core","./x64-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var Hasher=C_lib.Hasher;var C_x64=C.x64;var X64Word=C_x64.Word;var X64WordArray=C_x64.WordArray;var C_algo=C.algo;function X64Word_create(){return X64Word.create.apply(X64Word,arguments)}var K=[X64Word_create(1116352408,3609767458),X64Word_create(1899447441,602891725),X64Word_create(3049323471,3964484399),X64Word_create(3921009573,2173295548),X64Word_create(961987163,4081628472),X64Word_create(1508970993,3053834265),X64Word_create(2453635748,2937671579),X64Word_create(2870763221,3664609560),X64Word_create(3624381080,2734883394),X64Word_create(310598401,1164996542),X64Word_create(607225278,1323610764),X64Word_create(1426881987,3590304994),X64Word_create(1925078388,4068182383),X64Word_create(2162078206,991336113),X64Word_create(2614888103,633803317),X64Word_create(3248222580,3479774868),X64Word_create(3835390401,2666613458),X64Word_create(4022224774,944711139),X64Word_create(264347078,2341262773),X64Word_create(604807628,2007800933),X64Word_create(770255983,1495990901),X64Word_create(1249150122,1856431235),X64Word_create(1555081692,3175218132),X64Word_create(1996064986,2198950837),X64Word_create(2554220882,3999719339),X64Word_create(2821834349,766784016),X64Word_create(2952996808,2566594879),X64Word_create(3210313671,3203337956),X64Word_create(3336571891,1034457026),X64Word_create(3584528711,2466948901),X64Word_create(113926993,3758326383),X64Word_create(338241895,168717936),X64Word_create(666307205,1188179964),X64Word_create(773529912,1546045734),X64Word_create(1294757372,1522805485),X64Word_create(1396182291,2643833823),X64Word_create(1695183700,2343527390),X64Word_create(1986661051,1014477480),X64Word_create(2177026350,1206759142),X64Word_create(2456956037,344077627),X64Word_create(2730485921,1290863460),X64Word_create(2820302411,3158454273),X64Word_create(3259730800,3505952657),X64Word_create(3345764771,106217008),X64Word_create(3516065817,3606008344),X64Word_create(3600352804,1432725776),X64Word_create(4094571909,1467031594),X64Word_create(275423344,851169720),X64Word_create(430227734,3100823752),X64Word_create(506948616,1363258195),X64Word_create(659060556,3750685593),X64Word_create(883997877,3785050280),X64Word_create(958139571,3318307427),X64Word_create(1322822218,3812723403),X64Word_create(1537002063,2003034995),X64Word_create(1747873779,3602036899),X64Word_create(1955562222,1575990012),X64Word_create(2024104815,1125592928),X64Word_create(2227730452,2716904306),X64Word_create(2361852424,442776044),X64Word_create(2428436474,593698344),X64Word_create(2756734187,3733110249),X64Word_create(3204031479,2999351573),X64Word_create(3329325298,3815920427),X64Word_create(3391569614,3928383900),X64Word_create(3515267271,566280711),X64Word_create(3940187606,3454069534),X64Word_create(4118630271,4000239992),X64Word_create(116418474,1914138554),X64Word_create(174292421,2731055270),X64Word_create(289380356,3203993006),X64Word_create(460393269,320620315),X64Word_create(685471733,587496836),X64Word_create(852142971,1086792851),X64Word_create(1017036298,365543100),X64Word_create(1126000580,2618297676),X64Word_create(1288033470,3409855158),X64Word_create(1501505948,4234509866),X64Word_create(1607167915,987167468),X64Word_create(1816402316,1246189591)];
var W=[];(function(){for(var i=0;i<80;i++){W[i]=X64Word_create()}}());var SHA512=C_algo.SHA512=Hasher.extend({_doReset:function(){this._hash=new X64WordArray.init([new X64Word.init(1779033703,4089235720),new X64Word.init(3144134277,2227873595),new X64Word.init(1013904242,4271175723),new X64Word.init(2773480762,1595750129),new X64Word.init(1359893119,2917565137),new X64Word.init(2600822924,725511199),new X64Word.init(528734635,4215389547),new X64Word.init(1541459225,327033209)])},_doProcessBlock:function(M,offset){var H=this._hash.words;var H0=H[0];var H1=H[1];var H2=H[2];var H3=H[3];var H4=H[4];var H5=H[5];var H6=H[6];var H7=H[7];var H0h=H0.high;var H0l=H0.low;var H1h=H1.high;var H1l=H1.low;var H2h=H2.high;var H2l=H2.low;var H3h=H3.high;var H3l=H3.low;var H4h=H4.high;var H4l=H4.low;var H5h=H5.high;var H5l=H5.low;var H6h=H6.high;var H6l=H6.low;var H7h=H7.high;var H7l=H7.low;var ah=H0h;var al=H0l;var bh=H1h;var bl=H1l;var ch=H2h;var cl=H2l;var dh=H3h;var dl=H3l;var eh=H4h;var el=H4l;var fh=H5h;var fl=H5l;var gh=H6h;var gl=H6l;var hh=H7h;var hl=H7l;for(var i=0;i<80;i++){var Wi=W[i];if(i<16){var Wih=Wi.high=M[offset+i*2]|0;var Wil=Wi.low=M[offset+i*2+1]|0}else{var gamma0x=W[i-15];var gamma0xh=gamma0x.high;var gamma0xl=gamma0x.low;var gamma0h=((gamma0xh>>>1)|(gamma0xl<<31))^((gamma0xh>>>8)|(gamma0xl<<24))^(gamma0xh>>>7);var gamma0l=((gamma0xl>>>1)|(gamma0xh<<31))^((gamma0xl>>>8)|(gamma0xh<<24))^((gamma0xl>>>7)|(gamma0xh<<25));var gamma1x=W[i-2];var gamma1xh=gamma1x.high;var gamma1xl=gamma1x.low;var gamma1h=((gamma1xh>>>19)|(gamma1xl<<13))^((gamma1xh<<3)|(gamma1xl>>>29))^(gamma1xh>>>6);var gamma1l=((gamma1xl>>>19)|(gamma1xh<<13))^((gamma1xl<<3)|(gamma1xh>>>29))^((gamma1xl>>>6)|(gamma1xh<<26));var Wi7=W[i-7];var Wi7h=Wi7.high;var Wi7l=Wi7.low;var Wi16=W[i-16];var Wi16h=Wi16.high;var Wi16l=Wi16.low;var Wil=gamma0l+Wi7l;var Wih=gamma0h+Wi7h+((Wil>>>0)<(gamma0l>>>0)?1:0);var Wil=Wil+gamma1l;var Wih=Wih+gamma1h+((Wil>>>0)<(gamma1l>>>0)?1:0);var Wil=Wil+Wi16l;var Wih=Wih+Wi16h+((Wil>>>0)<(Wi16l>>>0)?1:0);Wi.high=Wih;Wi.low=Wil}var chh=(eh&fh)^(~eh&gh);var chl=(el&fl)^(~el&gl);var majh=(ah&bh)^(ah&ch)^(bh&ch);var majl=(al&bl)^(al&cl)^(bl&cl);var sigma0h=((ah>>>28)|(al<<4))^((ah<<30)|(al>>>2))^((ah<<25)|(al>>>7));var sigma0l=((al>>>28)|(ah<<4))^((al<<30)|(ah>>>2))^((al<<25)|(ah>>>7));var sigma1h=((eh>>>14)|(el<<18))^((eh>>>18)|(el<<14))^((eh<<23)|(el>>>9));var sigma1l=((el>>>14)|(eh<<18))^((el>>>18)|(eh<<14))^((el<<23)|(eh>>>9));var Ki=K[i];var Kih=Ki.high;var Kil=Ki.low;var t1l=hl+sigma1l;var t1h=hh+sigma1h+((t1l>>>0)<(hl>>>0)?1:0);var t1l=t1l+chl;var t1h=t1h+chh+((t1l>>>0)<(chl>>>0)?1:0);var t1l=t1l+Kil;var t1h=t1h+Kih+((t1l>>>0)<(Kil>>>0)?1:0);var t1l=t1l+Wil;var t1h=t1h+Wih+((t1l>>>0)<(Wil>>>0)?1:0);var t2l=sigma0l+majl;var t2h=sigma0h+majh+((t2l>>>0)<(sigma0l>>>0)?1:0);hh=gh;hl=gl;gh=fh;gl=fl;fh=eh;fl=el;el=(dl+t1l)|0;eh=(dh+t1h+((el>>>0)<(dl>>>0)?1:0))|0;dh=ch;dl=cl;ch=bh;cl=bl;bh=ah;bl=al;al=(t1l+t2l)|0;ah=(t1h+t2h+((al>>>0)<(t1l>>>0)?1:0))|0}H0l=H0.low=(H0l+al);H0.high=(H0h+ah+((H0l>>>0)<(al>>>0)?1:0));H1l=H1.low=(H1l+bl);H1.high=(H1h+bh+((H1l>>>0)<(bl>>>0)?1:0));H2l=H2.low=(H2l+cl);H2.high=(H2h+ch+((H2l>>>0)<(cl>>>0)?1:0));H3l=H3.low=(H3l+dl);H3.high=(H3h+dh+((H3l>>>0)<(dl>>>0)?1:0));H4l=H4.low=(H4l+el);H4.high=(H4h+eh+((H4l>>>0)<(el>>>0)?1:0));H5l=H5.low=(H5l+fl);H5.high=(H5h+fh+((H5l>>>0)<(fl>>>0)?1:0));H6l=H6.low=(H6l+gl);H6.high=(H6h+gh+((H6l>>>0)<(gl>>>0)?1:0));H7l=H7.low=(H7l+hl);H7.high=(H7h+hh+((H7l>>>0)<(hl>>>0)?1:0))},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<(24-nBitsLeft%32);dataWords[(((nBitsLeft+128)>>>10)<<5)+30]=Math.floor(nBitsTotal/4294967296);dataWords[(((nBitsLeft+128)>>>10)<<5)+31]=nBitsTotal;data.sigBytes=dataWords.length*4;this._process();var hash=this._hash.toX32();return hash},clone:function(){var clone=Hasher.clone.call(this);clone._hash=this._hash.clone();return clone},blockSize:1024/32});C.SHA512=Hasher._createHelper(SHA512);C.HmacSHA512=Hasher._createHmacHelper(SHA512)}());return CryptoJS.SHA512}))},221:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(212),__webpack_require__(220))}else{if(typeof define==="function"&&define.amd){define(["./core","./x64-core","./sha512"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_x64=C.x64;var X64Word=C_x64.Word;var X64WordArray=C_x64.WordArray;var C_algo=C.algo;var SHA512=C_algo.SHA512;var SHA384=C_algo.SHA384=SHA512.extend({_doReset:function(){this._hash=new X64WordArray.init([new X64Word.init(3418070365,3238371032),new X64Word.init(1654270250,914150663),new X64Word.init(2438529370,812702999),new X64Word.init(355462360,4144912697),new X64Word.init(1731405415,4290775857),new X64Word.init(2394180231,1750603025),new X64Word.init(3675008525,1694076839),new X64Word.init(1203062813,3204075428)])
},_doFinalize:function(){var hash=SHA512._doFinalize.call(this);hash.sigBytes-=16;return hash}});C.SHA384=SHA512._createHelper(SHA384);C.HmacSHA384=SHA512._createHmacHelper(SHA384)}());return CryptoJS.SHA384}))},222:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(212))}else{if(typeof define==="function"&&define.amd){define(["./core","./x64-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(Math){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_x64=C.x64;var X64Word=C_x64.Word;var C_algo=C.algo;var RHO_OFFSETS=[];var PI_INDEXES=[];var ROUND_CONSTANTS=[];(function(){var x=1,y=0;for(var t=0;t<24;t++){RHO_OFFSETS[x+5*y]=((t+1)*(t+2)/2)%64;var newX=y%5;var newY=(2*x+3*y)%5;x=newX;y=newY}for(var x=0;x<5;x++){for(var y=0;y<5;y++){PI_INDEXES[x+5*y]=y+((2*x+3*y)%5)*5}}var LFSR=1;for(var i=0;i<24;i++){var roundConstantMsw=0;var roundConstantLsw=0;for(var j=0;j<7;j++){if(LFSR&1){var bitPosition=(1<<j)-1;if(bitPosition<32){roundConstantLsw^=1<<bitPosition}else{roundConstantMsw^=1<<(bitPosition-32)}}if(LFSR&128){LFSR=(LFSR<<1)^113}else{LFSR<<=1}}ROUND_CONSTANTS[i]=X64Word.create(roundConstantMsw,roundConstantLsw)}}());var T=[];(function(){for(var i=0;i<25;i++){T[i]=X64Word.create()}}());var SHA3=C_algo.SHA3=Hasher.extend({cfg:Hasher.cfg.extend({outputLength:512}),_doReset:function(){var state=this._state=[];for(var i=0;i<25;i++){state[i]=new X64Word.init()}this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(M,offset){var state=this._state;var nBlockSizeLanes=this.blockSize/2;for(var i=0;i<nBlockSizeLanes;i++){var M2i=M[offset+2*i];var M2i1=M[offset+2*i+1];M2i=((((M2i<<8)|(M2i>>>24))&16711935)|(((M2i<<24)|(M2i>>>8))&4278255360));M2i1=((((M2i1<<8)|(M2i1>>>24))&16711935)|(((M2i1<<24)|(M2i1>>>8))&4278255360));var lane=state[i];lane.high^=M2i1;lane.low^=M2i}for(var round=0;round<24;round++){for(var x=0;x<5;x++){var tMsw=0,tLsw=0;for(var y=0;y<5;y++){var lane=state[x+5*y];tMsw^=lane.high;tLsw^=lane.low}var Tx=T[x];Tx.high=tMsw;Tx.low=tLsw}for(var x=0;x<5;x++){var Tx4=T[(x+4)%5];var Tx1=T[(x+1)%5];var Tx1Msw=Tx1.high;var Tx1Lsw=Tx1.low;var tMsw=Tx4.high^((Tx1Msw<<1)|(Tx1Lsw>>>31));var tLsw=Tx4.low^((Tx1Lsw<<1)|(Tx1Msw>>>31));for(var y=0;y<5;y++){var lane=state[x+5*y];lane.high^=tMsw;lane.low^=tLsw}}for(var laneIndex=1;laneIndex<25;laneIndex++){var lane=state[laneIndex];var laneMsw=lane.high;var laneLsw=lane.low;var rhoOffset=RHO_OFFSETS[laneIndex];if(rhoOffset<32){var tMsw=(laneMsw<<rhoOffset)|(laneLsw>>>(32-rhoOffset));var tLsw=(laneLsw<<rhoOffset)|(laneMsw>>>(32-rhoOffset))}else{var tMsw=(laneLsw<<(rhoOffset-32))|(laneMsw>>>(64-rhoOffset));var tLsw=(laneMsw<<(rhoOffset-32))|(laneLsw>>>(64-rhoOffset))}var TPiLane=T[PI_INDEXES[laneIndex]];TPiLane.high=tMsw;TPiLane.low=tLsw}var T0=T[0];var state0=state[0];T0.high=state0.high;T0.low=state0.low;for(var x=0;x<5;x++){for(var y=0;y<5;y++){var laneIndex=x+5*y;var lane=state[laneIndex];var TLane=T[laneIndex];var Tx1Lane=T[((x+1)%5)+5*y];var Tx2Lane=T[((x+2)%5)+5*y];lane.high=TLane.high^(~Tx1Lane.high&Tx2Lane.high);lane.low=TLane.low^(~Tx1Lane.low&Tx2Lane.low)}}var lane=state[0];var roundConstant=ROUND_CONSTANTS[round];lane.high^=roundConstant.high;lane.low^=roundConstant.low}},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;var blockSizeBits=this.blockSize*32;dataWords[nBitsLeft>>>5]|=1<<(24-nBitsLeft%32);dataWords[((Math.ceil((nBitsLeft+1)/blockSizeBits)*blockSizeBits)>>>5)-1]|=128;data.sigBytes=dataWords.length*4;this._process();var state=this._state;var outputLengthBytes=this.cfg.outputLength/8;var outputLengthLanes=outputLengthBytes/8;var hashWords=[];for(var i=0;i<outputLengthLanes;i++){var lane=state[i];var laneMsw=lane.high;var laneLsw=lane.low;laneMsw=((((laneMsw<<8)|(laneMsw>>>24))&16711935)|(((laneMsw<<24)|(laneMsw>>>8))&4278255360));laneLsw=((((laneLsw<<8)|(laneLsw>>>24))&16711935)|(((laneLsw<<24)|(laneLsw>>>8))&4278255360));hashWords.push(laneLsw);hashWords.push(laneMsw)}return new WordArray.init(hashWords,outputLengthBytes)},clone:function(){var clone=Hasher.clone.call(this);var state=clone._state=this._state.slice(0);for(var i=0;i<25;i++){state[i]=state[i].clone()}return clone}});C.SHA3=Hasher._createHelper(SHA3);C.HmacSHA3=Hasher._createHmacHelper(SHA3)}(Math));return CryptoJS.SHA3}))},223:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(Math){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C.algo;var _zl=WordArray.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]);
var _zr=WordArray.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]);var _sl=WordArray.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]);var _sr=WordArray.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]);var _hl=WordArray.create([0,1518500249,1859775393,2400959708,2840853838]);var _hr=WordArray.create([1352829926,1548603684,1836072691,2053994217,0]);var RIPEMD160=C_algo.RIPEMD160=Hasher.extend({_doReset:function(){this._hash=WordArray.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(M,offset){for(var i=0;i<16;i++){var offset_i=offset+i;var M_offset_i=M[offset_i];M[offset_i]=((((M_offset_i<<8)|(M_offset_i>>>24))&16711935)|(((M_offset_i<<24)|(M_offset_i>>>8))&4278255360))}var H=this._hash.words;var hl=_hl.words;var hr=_hr.words;var zl=_zl.words;var zr=_zr.words;var sl=_sl.words;var sr=_sr.words;var al,bl,cl,dl,el;var ar,br,cr,dr,er;ar=al=H[0];br=bl=H[1];cr=cl=H[2];dr=dl=H[3];er=el=H[4];var t;for(var i=0;i<80;i+=1){t=(al+M[offset+zl[i]])|0;if(i<16){t+=f1(bl,cl,dl)+hl[0]}else{if(i<32){t+=f2(bl,cl,dl)+hl[1]}else{if(i<48){t+=f3(bl,cl,dl)+hl[2]}else{if(i<64){t+=f4(bl,cl,dl)+hl[3]}else{t+=f5(bl,cl,dl)+hl[4]}}}}t=t|0;t=rotl(t,sl[i]);t=(t+el)|0;al=el;el=dl;dl=rotl(cl,10);cl=bl;bl=t;t=(ar+M[offset+zr[i]])|0;if(i<16){t+=f5(br,cr,dr)+hr[0]}else{if(i<32){t+=f4(br,cr,dr)+hr[1]}else{if(i<48){t+=f3(br,cr,dr)+hr[2]}else{if(i<64){t+=f2(br,cr,dr)+hr[3]}else{t+=f1(br,cr,dr)+hr[4]}}}}t=t|0;t=rotl(t,sr[i]);t=(t+er)|0;ar=er;er=dr;dr=rotl(cr,10);cr=br;br=t}t=(H[1]+cl+dr)|0;H[1]=(H[2]+dl+er)|0;H[2]=(H[3]+el+ar)|0;H[3]=(H[4]+al+br)|0;H[4]=(H[0]+bl+cr)|0;H[0]=t},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<(24-nBitsLeft%32);dataWords[(((nBitsLeft+64)>>>9)<<4)+14]=((((nBitsTotal<<8)|(nBitsTotal>>>24))&16711935)|(((nBitsTotal<<24)|(nBitsTotal>>>8))&4278255360));data.sigBytes=(dataWords.length+1)*4;this._process();var hash=this._hash;var H=hash.words;for(var i=0;i<5;i++){var H_i=H[i];H[i]=(((H_i<<8)|(H_i>>>24))&16711935)|(((H_i<<24)|(H_i>>>8))&4278255360)}return hash},clone:function(){var clone=Hasher.clone.call(this);clone._hash=this._hash.clone();return clone}});function f1(x,y,z){return((x)^(y)^(z))}function f2(x,y,z){return(((x)&(y))|((~x)&(z)))}function f3(x,y,z){return(((x)|(~(y)))^(z))}function f4(x,y,z){return(((x)&(z))|((y)&(~(z))))}function f5(x,y,z){return((x)^((y)|(~(z))))}function rotl(x,n){return(x<<n)|(x>>>(32-n))}C.RIPEMD160=Hasher._createHelper(RIPEMD160);C.HmacRIPEMD160=Hasher._createHmacHelper(RIPEMD160)}(Math));return CryptoJS.RIPEMD160}))},224:function(module,exports,__webpack_require__){(function(root,factory){if(true){module.exports=exports=factory(__webpack_require__(211))}else{if(typeof define==="function"&&define.amd){define(["./core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var Base=C_lib.Base;var C_enc=C.enc;var Utf8=C_enc.Utf8;var C_algo=C.algo;var HMAC=C_algo.HMAC=Base.extend({init:function(hasher,key){hasher=this._hasher=new hasher.init();if(typeof key=="string"){key=Utf8.parse(key)}var hasherBlockSize=hasher.blockSize;var hasherBlockSizeBytes=hasherBlockSize*4;if(key.sigBytes>hasherBlockSizeBytes){key=hasher.finalize(key)}key.clamp();var oKey=this._oKey=key.clone();var iKey=this._iKey=key.clone();var oKeyWords=oKey.words;var iKeyWords=iKey.words;for(var i=0;i<hasherBlockSize;i++){oKeyWords[i]^=1549556828;iKeyWords[i]^=909522486}oKey.sigBytes=iKey.sigBytes=hasherBlockSizeBytes;this.reset()},reset:function(){var hasher=this._hasher;hasher.reset();hasher.update(this._iKey)},update:function(messageUpdate){this._hasher.update(messageUpdate);return this},finalize:function(messageUpdate){var hasher=this._hasher;var innerHash=hasher.finalize(messageUpdate);hasher.reset();var hmac=hasher.finalize(this._oKey.clone().concat(innerHash));return hmac}})}())}))},225:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(217),__webpack_require__(224))}else{if(typeof define==="function"&&define.amd){define(["./core","./sha1","./hmac"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var Base=C_lib.Base;var WordArray=C_lib.WordArray;var C_algo=C.algo;var SHA1=C_algo.SHA1;var HMAC=C_algo.HMAC;var PBKDF2=C_algo.PBKDF2=Base.extend({cfg:Base.extend({keySize:128/32,hasher:SHA1,iterations:1}),init:function(cfg){this.cfg=this.cfg.extend(cfg)
},compute:function(password,salt){var cfg=this.cfg;var hmac=HMAC.create(cfg.hasher,password);var derivedKey=WordArray.create();var blockIndex=WordArray.create([1]);var derivedKeyWords=derivedKey.words;var blockIndexWords=blockIndex.words;var keySize=cfg.keySize;var iterations=cfg.iterations;while(derivedKeyWords.length<keySize){var block=hmac.update(salt).finalize(blockIndex);hmac.reset();var blockWords=block.words;var blockWordsLength=blockWords.length;var intermediate=block;for(var i=1;i<iterations;i++){intermediate=hmac.finalize(intermediate);hmac.reset();var intermediateWords=intermediate.words;for(var j=0;j<blockWordsLength;j++){blockWords[j]^=intermediateWords[j]}}derivedKey.concat(block);blockIndexWords[0]++}derivedKey.sigBytes=keySize*4;return derivedKey}});C.PBKDF2=function(password,salt,cfg){return PBKDF2.create(cfg).compute(password,salt)}}());return CryptoJS.PBKDF2}))},226:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(217),__webpack_require__(224))}else{if(typeof define==="function"&&define.amd){define(["./core","./sha1","./hmac"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var Base=C_lib.Base;var WordArray=C_lib.WordArray;var C_algo=C.algo;var MD5=C_algo.MD5;var EvpKDF=C_algo.EvpKDF=Base.extend({cfg:Base.extend({keySize:128/32,hasher:MD5,iterations:1}),init:function(cfg){this.cfg=this.cfg.extend(cfg)},compute:function(password,salt){var cfg=this.cfg;var hasher=cfg.hasher.create();var derivedKey=WordArray.create();var derivedKeyWords=derivedKey.words;var keySize=cfg.keySize;var iterations=cfg.iterations;while(derivedKeyWords.length<keySize){if(block){hasher.update(block)}var block=hasher.update(password).finalize(salt);hasher.reset();for(var i=1;i<iterations;i++){block=hasher.finalize(block);hasher.reset()}derivedKey.concat(block)}derivedKey.sigBytes=keySize*4;return derivedKey}});C.EvpKDF=function(password,salt,cfg){return EvpKDF.create(cfg).compute(password,salt)}}());return CryptoJS.EvpKDF}))},227:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(226))}else{if(typeof define==="function"&&define.amd){define(["./core","./evpkdf"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.lib.Cipher||(function(undefined){var C=CryptoJS;var C_lib=C.lib;var Base=C_lib.Base;var WordArray=C_lib.WordArray;var BufferedBlockAlgorithm=C_lib.BufferedBlockAlgorithm;var C_enc=C.enc;var Utf8=C_enc.Utf8;var Base64=C_enc.Base64;var C_algo=C.algo;var EvpKDF=C_algo.EvpKDF;var Cipher=C_lib.Cipher=BufferedBlockAlgorithm.extend({cfg:Base.extend(),createEncryptor:function(key,cfg){return this.create(this._ENC_XFORM_MODE,key,cfg)},createDecryptor:function(key,cfg){return this.create(this._DEC_XFORM_MODE,key,cfg)},init:function(xformMode,key,cfg){this.cfg=this.cfg.extend(cfg);this._xformMode=xformMode;this._key=key;this.reset()},reset:function(){BufferedBlockAlgorithm.reset.call(this);this._doReset()},process:function(dataUpdate){this._append(dataUpdate);return this._process()},finalize:function(dataUpdate){if(dataUpdate){this._append(dataUpdate)}var finalProcessedData=this._doFinalize();return finalProcessedData},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:(function(){function selectCipherStrategy(key){if(typeof key=="string"){return PasswordBasedCipher}else{return SerializableCipher}}return function(cipher){return{encrypt:function(message,key,cfg){return selectCipherStrategy(key).encrypt(cipher,message,key,cfg)},decrypt:function(ciphertext,key,cfg){return selectCipherStrategy(key).decrypt(cipher,ciphertext,key,cfg)}}}}())});var StreamCipher=C_lib.StreamCipher=Cipher.extend({_doFinalize:function(){var finalProcessedBlocks=this._process(!!"flush");return finalProcessedBlocks},blockSize:1});var C_mode=C.mode={};var BlockCipherMode=C_lib.BlockCipherMode=Base.extend({createEncryptor:function(cipher,iv){return this.Encryptor.create(cipher,iv)},createDecryptor:function(cipher,iv){return this.Decryptor.create(cipher,iv)},init:function(cipher,iv){this._cipher=cipher;this._iv=iv}});var CBC=C_mode.CBC=(function(){var CBC=BlockCipherMode.extend();CBC.Encryptor=CBC.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;xorBlock.call(this,words,offset,blockSize);cipher.encryptBlock(words,offset);this._prevBlock=words.slice(offset,offset+blockSize)}});CBC.Decryptor=CBC.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var thisBlock=words.slice(offset,offset+blockSize);cipher.decryptBlock(words,offset);xorBlock.call(this,words,offset,blockSize);this._prevBlock=thisBlock}});function xorBlock(words,offset,blockSize){var iv=this._iv;if(iv){var block=iv;this._iv=undefined}else{var block=this._prevBlock}for(var i=0;
i<blockSize;i++){words[offset+i]^=block[i]}}return CBC}());var C_pad=C.pad={};var Pkcs7=C_pad.Pkcs7={pad:function(data,blockSize){var blockSizeBytes=blockSize*4;var nPaddingBytes=blockSizeBytes-data.sigBytes%blockSizeBytes;var paddingWord=(nPaddingBytes<<24)|(nPaddingBytes<<16)|(nPaddingBytes<<8)|nPaddingBytes;var paddingWords=[];for(var i=0;i<nPaddingBytes;i+=4){paddingWords.push(paddingWord)}var padding=WordArray.create(paddingWords,nPaddingBytes);data.concat(padding)},unpad:function(data){var nPaddingBytes=data.words[(data.sigBytes-1)>>>2]&255;data.sigBytes-=nPaddingBytes}};var BlockCipher=C_lib.BlockCipher=Cipher.extend({cfg:Cipher.cfg.extend({mode:CBC,padding:Pkcs7}),reset:function(){Cipher.reset.call(this);var cfg=this.cfg;var iv=cfg.iv;var mode=cfg.mode;if(this._xformMode==this._ENC_XFORM_MODE){var modeCreator=mode.createEncryptor}else{var modeCreator=mode.createDecryptor;this._minBufferSize=1}if(this._mode&&this._mode.__creator==modeCreator){this._mode.init(this,iv&&iv.words)}else{this._mode=modeCreator.call(mode,this,iv&&iv.words);this._mode.__creator=modeCreator}},_doProcessBlock:function(words,offset){this._mode.processBlock(words,offset)},_doFinalize:function(){var padding=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){padding.pad(this._data,this.blockSize);var finalProcessedBlocks=this._process(!!"flush")}else{var finalProcessedBlocks=this._process(!!"flush");padding.unpad(finalProcessedBlocks)}return finalProcessedBlocks},blockSize:128/32});var CipherParams=C_lib.CipherParams=Base.extend({init:function(cipherParams){this.mixIn(cipherParams)},toString:function(formatter){return(formatter||this.formatter).stringify(this)}});var C_format=C.format={};var OpenSSLFormatter=C_format.OpenSSL={stringify:function(cipherParams){var ciphertext=cipherParams.ciphertext;var salt=cipherParams.salt;if(salt){var wordArray=WordArray.create([1398893684,1701076831]).concat(salt).concat(ciphertext)}else{var wordArray=ciphertext}return wordArray.toString(Base64)},parse:function(openSSLStr){var ciphertext=Base64.parse(openSSLStr);var ciphertextWords=ciphertext.words;if(ciphertextWords[0]==1398893684&&ciphertextWords[1]==1701076831){var salt=WordArray.create(ciphertextWords.slice(2,4));ciphertextWords.splice(0,4);ciphertext.sigBytes-=16}return CipherParams.create({ciphertext:ciphertext,salt:salt})}};var SerializableCipher=C_lib.SerializableCipher=Base.extend({cfg:Base.extend({format:OpenSSLFormatter}),encrypt:function(cipher,message,key,cfg){cfg=this.cfg.extend(cfg);var encryptor=cipher.createEncryptor(key,cfg);var ciphertext=encryptor.finalize(message);var cipherCfg=encryptor.cfg;return CipherParams.create({ciphertext:ciphertext,key:key,iv:cipherCfg.iv,algorithm:cipher,mode:cipherCfg.mode,padding:cipherCfg.padding,blockSize:cipher.blockSize,formatter:cfg.format})},decrypt:function(cipher,ciphertext,key,cfg){cfg=this.cfg.extend(cfg);ciphertext=this._parse(ciphertext,cfg.format);var plaintext=cipher.createDecryptor(key,cfg).finalize(ciphertext.ciphertext);return plaintext},_parse:function(ciphertext,format){if(typeof ciphertext=="string"){return format.parse(ciphertext,this)}else{return ciphertext}}});var C_kdf=C.kdf={};var OpenSSLKdf=C_kdf.OpenSSL={execute:function(password,keySize,ivSize,salt){if(!salt){salt=WordArray.random(64/8)}var key=EvpKDF.create({keySize:keySize+ivSize}).compute(password,salt);var iv=WordArray.create(key.words.slice(keySize),ivSize*4);key.sigBytes=keySize*4;return CipherParams.create({key:key,iv:iv,salt:salt})}};var PasswordBasedCipher=C_lib.PasswordBasedCipher=SerializableCipher.extend({cfg:SerializableCipher.cfg.extend({kdf:OpenSSLKdf}),encrypt:function(cipher,message,password,cfg){cfg=this.cfg.extend(cfg);var derivedParams=cfg.kdf.execute(password,cipher.keySize,cipher.ivSize);cfg.iv=derivedParams.iv;var ciphertext=SerializableCipher.encrypt.call(this,cipher,message,derivedParams.key,cfg);ciphertext.mixIn(derivedParams);return ciphertext},decrypt:function(cipher,ciphertext,password,cfg){cfg=this.cfg.extend(cfg);ciphertext=this._parse(ciphertext,cfg.format);var derivedParams=cfg.kdf.execute(password,cipher.keySize,cipher.ivSize,ciphertext.salt);cfg.iv=derivedParams.iv;var plaintext=SerializableCipher.decrypt.call(this,cipher,ciphertext,derivedParams.key,cfg);return plaintext}})}())}))},228:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.mode.CFB=(function(){var CFB=CryptoJS.lib.BlockCipherMode.extend();CFB.Encryptor=CFB.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;generateKeystreamAndEncrypt.call(this,words,offset,blockSize,cipher);this._prevBlock=words.slice(offset,offset+blockSize)}});CFB.Decryptor=CFB.extend({processBlock:function(words,offset){var cipher=this._cipher;
var blockSize=cipher.blockSize;var thisBlock=words.slice(offset,offset+blockSize);generateKeystreamAndEncrypt.call(this,words,offset,blockSize,cipher);this._prevBlock=thisBlock}});function generateKeystreamAndEncrypt(words,offset,blockSize,cipher){var iv=this._iv;if(iv){var keystream=iv.slice(0);this._iv=undefined}else{var keystream=this._prevBlock}cipher.encryptBlock(keystream,0);for(var i=0;i<blockSize;i++){words[offset+i]^=keystream[i]}}return CFB}());return CryptoJS.mode.CFB}))},229:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.mode.CTR=(function(){var CTR=CryptoJS.lib.BlockCipherMode.extend();var Encryptor=CTR.Encryptor=CTR.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var iv=this._iv;var counter=this._counter;if(iv){counter=this._counter=iv.slice(0);this._iv=undefined}var keystream=counter.slice(0);cipher.encryptBlock(keystream,0);counter[blockSize-1]=(counter[blockSize-1]+1)|0;for(var i=0;i<blockSize;i++){words[offset+i]^=keystream[i]}}});CTR.Decryptor=Encryptor;return CTR}());return CryptoJS.mode.CTR}))},230:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.mode.CTRGladman=(function(){var CTRGladman=CryptoJS.lib.BlockCipherMode.extend();function incWord(word){if(((word>>24)&255)===255){var b1=(word>>16)&255;var b2=(word>>8)&255;var b3=word&255;if(b1===255){b1=0;if(b2===255){b2=0;if(b3===255){b3=0}else{++b3}}else{++b2}}else{++b1}word=0;word+=(b1<<16);word+=(b2<<8);word+=b3}else{word+=(1<<24)}return word}function incCounter(counter){if((counter[0]=incWord(counter[0]))===0){counter[1]=incWord(counter[1])}return counter}var Encryptor=CTRGladman.Encryptor=CTRGladman.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var iv=this._iv;var counter=this._counter;if(iv){counter=this._counter=iv.slice(0);this._iv=undefined}incCounter(counter);var keystream=counter.slice(0);cipher.encryptBlock(keystream,0);for(var i=0;i<blockSize;i++){words[offset+i]^=keystream[i]}}});CTRGladman.Decryptor=Encryptor;return CTRGladman}());return CryptoJS.mode.CTRGladman}))},231:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.mode.OFB=(function(){var OFB=CryptoJS.lib.BlockCipherMode.extend();var Encryptor=OFB.Encryptor=OFB.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var iv=this._iv;var keystream=this._keystream;if(iv){keystream=this._keystream=iv.slice(0);this._iv=undefined}cipher.encryptBlock(keystream,0);for(var i=0;i<blockSize;i++){words[offset+i]^=keystream[i]}}});OFB.Decryptor=Encryptor;return OFB}());return CryptoJS.mode.OFB}))},232:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.mode.ECB=(function(){var ECB=CryptoJS.lib.BlockCipherMode.extend();ECB.Encryptor=ECB.extend({processBlock:function(words,offset){this._cipher.encryptBlock(words,offset)}});ECB.Decryptor=ECB.extend({processBlock:function(words,offset){this._cipher.decryptBlock(words,offset)}});return ECB}());return CryptoJS.mode.ECB}))},233:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.pad.AnsiX923={pad:function(data,blockSize){var dataSigBytes=data.sigBytes;var blockSizeBytes=blockSize*4;var nPaddingBytes=blockSizeBytes-dataSigBytes%blockSizeBytes;var lastBytePos=dataSigBytes+nPaddingBytes-1;data.clamp();data.words[lastBytePos>>>2]|=nPaddingBytes<<(24-(lastBytePos%4)*8);data.sigBytes+=nPaddingBytes},unpad:function(data){var nPaddingBytes=data.words[(data.sigBytes-1)>>>2]&255;data.sigBytes-=nPaddingBytes}};return CryptoJS.pad.Ansix923}))},234:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))
}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.pad.Iso10126={pad:function(data,blockSize){var blockSizeBytes=blockSize*4;var nPaddingBytes=blockSizeBytes-data.sigBytes%blockSizeBytes;data.concat(CryptoJS.lib.WordArray.random(nPaddingBytes-1)).concat(CryptoJS.lib.WordArray.create([nPaddingBytes<<24],1))},unpad:function(data){var nPaddingBytes=data.words[(data.sigBytes-1)>>>2]&255;data.sigBytes-=nPaddingBytes}};return CryptoJS.pad.Iso10126}))},235:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.pad.Iso97971={pad:function(data,blockSize){data.concat(CryptoJS.lib.WordArray.create([2147483648],1));CryptoJS.pad.ZeroPadding.pad(data,blockSize)},unpad:function(data){CryptoJS.pad.ZeroPadding.unpad(data);data.sigBytes--}};return CryptoJS.pad.Iso97971}))},236:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.pad.ZeroPadding={pad:function(data,blockSize){var blockSizeBytes=blockSize*4;data.clamp();data.sigBytes+=blockSizeBytes-((data.sigBytes%blockSizeBytes)||blockSizeBytes)},unpad:function(data){var dataWords=data.words;var i=data.sigBytes-1;while(!((dataWords[i>>>2]>>>(24-(i%4)*8))&255)){i--}data.sigBytes=i+1}};return CryptoJS.pad.ZeroPadding}))},237:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){CryptoJS.pad.NoPadding={pad:function(){},unpad:function(){}};return CryptoJS.pad.NoPadding}))},238:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(undefined){var C=CryptoJS;var C_lib=C.lib;var CipherParams=C_lib.CipherParams;var C_enc=C.enc;var Hex=C_enc.Hex;var C_format=C.format;var HexFormatter=C_format.Hex={stringify:function(cipherParams){return cipherParams.ciphertext.toString(Hex)},parse:function(input){var ciphertext=Hex.parse(input);return CipherParams.create({ciphertext:ciphertext})}}}());return CryptoJS.format.Hex}))},239:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(215),__webpack_require__(216),__webpack_require__(226),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./enc-base64","./md5","./evpkdf","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var BlockCipher=C_lib.BlockCipher;var C_algo=C.algo;var SBOX=[];var INV_SBOX=[];var SUB_MIX_0=[];var SUB_MIX_1=[];var SUB_MIX_2=[];var SUB_MIX_3=[];var INV_SUB_MIX_0=[];var INV_SUB_MIX_1=[];var INV_SUB_MIX_2=[];var INV_SUB_MIX_3=[];(function(){var d=[];for(var i=0;i<256;i++){if(i<128){d[i]=i<<1}else{d[i]=(i<<1)^283}}var x=0;var xi=0;for(var i=0;i<256;i++){var sx=xi^(xi<<1)^(xi<<2)^(xi<<3)^(xi<<4);sx=(sx>>>8)^(sx&255)^99;SBOX[x]=sx;INV_SBOX[sx]=x;var x2=d[x];var x4=d[x2];var x8=d[x4];var t=(d[sx]*257)^(sx*16843008);SUB_MIX_0[x]=(t<<24)|(t>>>8);SUB_MIX_1[x]=(t<<16)|(t>>>16);SUB_MIX_2[x]=(t<<8)|(t>>>24);SUB_MIX_3[x]=t;var t=(x8*16843009)^(x4*65537)^(x2*257)^(x*16843008);INV_SUB_MIX_0[sx]=(t<<24)|(t>>>8);INV_SUB_MIX_1[sx]=(t<<16)|(t>>>16);INV_SUB_MIX_2[sx]=(t<<8)|(t>>>24);INV_SUB_MIX_3[sx]=t;if(!x){x=xi=1}else{x=x2^d[d[d[x8^x2]]];xi^=d[d[xi]]}}}());var RCON=[0,1,2,4,8,16,32,64,128,27,54];var AES=C_algo.AES=BlockCipher.extend({_doReset:function(){if(this._nRounds&&this._keyPriorReset===this._key){return}var key=this._keyPriorReset=this._key;var keyWords=key.words;var keySize=key.sigBytes/4;var nRounds=this._nRounds=keySize+6;var ksRows=(nRounds+1)*4;var keySchedule=this._keySchedule=[];for(var ksRow=0;ksRow<ksRows;ksRow++){if(ksRow<keySize){keySchedule[ksRow]=keyWords[ksRow]}else{var t=keySchedule[ksRow-1];if(!(ksRow%keySize)){t=(t<<8)|(t>>>24);t=(SBOX[t>>>24]<<24)|(SBOX[(t>>>16)&255]<<16)|(SBOX[(t>>>8)&255]<<8)|SBOX[t&255];t^=RCON[(ksRow/keySize)|0]<<24}else{if(keySize>6&&ksRow%keySize==4){t=(SBOX[t>>>24]<<24)|(SBOX[(t>>>16)&255]<<16)|(SBOX[(t>>>8)&255]<<8)|SBOX[t&255]}}keySchedule[ksRow]=keySchedule[ksRow-keySize]^t
}}var invKeySchedule=this._invKeySchedule=[];for(var invKsRow=0;invKsRow<ksRows;invKsRow++){var ksRow=ksRows-invKsRow;if(invKsRow%4){var t=keySchedule[ksRow]}else{var t=keySchedule[ksRow-4]}if(invKsRow<4||ksRow<=4){invKeySchedule[invKsRow]=t}else{invKeySchedule[invKsRow]=INV_SUB_MIX_0[SBOX[t>>>24]]^INV_SUB_MIX_1[SBOX[(t>>>16)&255]]^INV_SUB_MIX_2[SBOX[(t>>>8)&255]]^INV_SUB_MIX_3[SBOX[t&255]]}}},encryptBlock:function(M,offset){this._doCryptBlock(M,offset,this._keySchedule,SUB_MIX_0,SUB_MIX_1,SUB_MIX_2,SUB_MIX_3,SBOX)},decryptBlock:function(M,offset){var t=M[offset+1];M[offset+1]=M[offset+3];M[offset+3]=t;this._doCryptBlock(M,offset,this._invKeySchedule,INV_SUB_MIX_0,INV_SUB_MIX_1,INV_SUB_MIX_2,INV_SUB_MIX_3,INV_SBOX);var t=M[offset+1];M[offset+1]=M[offset+3];M[offset+3]=t},_doCryptBlock:function(M,offset,keySchedule,SUB_MIX_0,SUB_MIX_1,SUB_MIX_2,SUB_MIX_3,SBOX){var nRounds=this._nRounds;var s0=M[offset]^keySchedule[0];var s1=M[offset+1]^keySchedule[1];var s2=M[offset+2]^keySchedule[2];var s3=M[offset+3]^keySchedule[3];var ksRow=4;for(var round=1;round<nRounds;round++){var t0=SUB_MIX_0[s0>>>24]^SUB_MIX_1[(s1>>>16)&255]^SUB_MIX_2[(s2>>>8)&255]^SUB_MIX_3[s3&255]^keySchedule[ksRow++];var t1=SUB_MIX_0[s1>>>24]^SUB_MIX_1[(s2>>>16)&255]^SUB_MIX_2[(s3>>>8)&255]^SUB_MIX_3[s0&255]^keySchedule[ksRow++];var t2=SUB_MIX_0[s2>>>24]^SUB_MIX_1[(s3>>>16)&255]^SUB_MIX_2[(s0>>>8)&255]^SUB_MIX_3[s1&255]^keySchedule[ksRow++];var t3=SUB_MIX_0[s3>>>24]^SUB_MIX_1[(s0>>>16)&255]^SUB_MIX_2[(s1>>>8)&255]^SUB_MIX_3[s2&255]^keySchedule[ksRow++];s0=t0;s1=t1;s2=t2;s3=t3}var t0=((SBOX[s0>>>24]<<24)|(SBOX[(s1>>>16)&255]<<16)|(SBOX[(s2>>>8)&255]<<8)|SBOX[s3&255])^keySchedule[ksRow++];var t1=((SBOX[s1>>>24]<<24)|(SBOX[(s2>>>16)&255]<<16)|(SBOX[(s3>>>8)&255]<<8)|SBOX[s0&255])^keySchedule[ksRow++];var t2=((SBOX[s2>>>24]<<24)|(SBOX[(s3>>>16)&255]<<16)|(SBOX[(s0>>>8)&255]<<8)|SBOX[s1&255])^keySchedule[ksRow++];var t3=((SBOX[s3>>>24]<<24)|(SBOX[(s0>>>16)&255]<<16)|(SBOX[(s1>>>8)&255]<<8)|SBOX[s2&255])^keySchedule[ksRow++];M[offset]=t0;M[offset+1]=t1;M[offset+2]=t2;M[offset+3]=t3},keySize:256/32});C.AES=BlockCipher._createHelper(AES)}());return CryptoJS.AES}))},240:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(215),__webpack_require__(216),__webpack_require__(226),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./enc-base64","./md5","./evpkdf","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var WordArray=C_lib.WordArray;var BlockCipher=C_lib.BlockCipher;var C_algo=C.algo;var PC1=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4];var PC2=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32];var BIT_SHIFTS=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28];var SBOX_P=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}];
var SBOX_MASK=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679];var DES=C_algo.DES=BlockCipher.extend({_doReset:function(){var key=this._key;var keyWords=key.words;var keyBits=[];for(var i=0;i<56;i++){var keyBitPos=PC1[i]-1;keyBits[i]=(keyWords[keyBitPos>>>5]>>>(31-keyBitPos%32))&1}var subKeys=this._subKeys=[];for(var nSubKey=0;nSubKey<16;nSubKey++){var subKey=subKeys[nSubKey]=[];var bitShift=BIT_SHIFTS[nSubKey];for(var i=0;i<24;i++){subKey[(i/6)|0]|=keyBits[((PC2[i]-1)+bitShift)%28]<<(31-i%6);subKey[4+((i/6)|0)]|=keyBits[28+(((PC2[i+24]-1)+bitShift)%28)]<<(31-i%6)}subKey[0]=(subKey[0]<<1)|(subKey[0]>>>31);for(var i=1;i<7;i++){subKey[i]=subKey[i]>>>((i-1)*4+3)}subKey[7]=(subKey[7]<<5)|(subKey[7]>>>27)}var invSubKeys=this._invSubKeys=[];for(var i=0;i<16;i++){invSubKeys[i]=subKeys[15-i]}},encryptBlock:function(M,offset){this._doCryptBlock(M,offset,this._subKeys)},decryptBlock:function(M,offset){this._doCryptBlock(M,offset,this._invSubKeys)},_doCryptBlock:function(M,offset,subKeys){this._lBlock=M[offset];this._rBlock=M[offset+1];exchangeLR.call(this,4,252645135);exchangeLR.call(this,16,65535);exchangeRL.call(this,2,858993459);exchangeRL.call(this,8,16711935);exchangeLR.call(this,1,1431655765);for(var round=0;round<16;round++){var subKey=subKeys[round];var lBlock=this._lBlock;var rBlock=this._rBlock;var f=0;for(var i=0;i<8;i++){f|=SBOX_P[i][((rBlock^subKey[i])&SBOX_MASK[i])>>>0]}this._lBlock=rBlock;this._rBlock=lBlock^f}var t=this._lBlock;this._lBlock=this._rBlock;this._rBlock=t;exchangeLR.call(this,1,1431655765);exchangeRL.call(this,8,16711935);exchangeRL.call(this,2,858993459);exchangeLR.call(this,16,65535);exchangeLR.call(this,4,252645135);M[offset]=this._lBlock;M[offset+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function exchangeLR(offset,mask){var t=((this._lBlock>>>offset)^this._rBlock)&mask;this._rBlock^=t;this._lBlock^=t<<offset}function exchangeRL(offset,mask){var t=((this._rBlock>>>offset)^this._lBlock)&mask;this._lBlock^=t;this._rBlock^=t<<offset}C.DES=BlockCipher._createHelper(DES);var TripleDES=C_algo.TripleDES=BlockCipher.extend({_doReset:function(){var key=this._key;var keyWords=key.words;this._des1=DES.createEncryptor(WordArray.create(keyWords.slice(0,2)));this._des2=DES.createEncryptor(WordArray.create(keyWords.slice(2,4)));this._des3=DES.createEncryptor(WordArray.create(keyWords.slice(4,6)))},encryptBlock:function(M,offset){this._des1.encryptBlock(M,offset);this._des2.decryptBlock(M,offset);this._des3.encryptBlock(M,offset)},decryptBlock:function(M,offset){this._des3.decryptBlock(M,offset);this._des2.encryptBlock(M,offset);this._des1.decryptBlock(M,offset)},keySize:192/32,ivSize:64/32,blockSize:64/32});C.TripleDES=BlockCipher._createHelper(TripleDES)}());return CryptoJS.TripleDES}))},241:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(215),__webpack_require__(216),__webpack_require__(226),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./enc-base64","./md5","./evpkdf","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var StreamCipher=C_lib.StreamCipher;var C_algo=C.algo;var RC4=C_algo.RC4=StreamCipher.extend({_doReset:function(){var key=this._key;var keyWords=key.words;var keySigBytes=key.sigBytes;var S=this._S=[];for(var i=0;i<256;i++){S[i]=i}for(var i=0,j=0;i<256;i++){var keyByteIndex=i%keySigBytes;var keyByte=(keyWords[keyByteIndex>>>2]>>>(24-(keyByteIndex%4)*8))&255;j=(j+S[i]+keyByte)%256;var t=S[i];S[i]=S[j];S[j]=t}this._i=this._j=0},_doProcessBlock:function(M,offset){M[offset]^=generateKeystreamWord.call(this)},keySize:256/32,ivSize:0});function generateKeystreamWord(){var S=this._S;var i=this._i;var j=this._j;var keystreamWord=0;for(var n=0;n<4;n++){i=(i+1)%256;j=(j+S[i])%256;var t=S[i];S[i]=S[j];S[j]=t;keystreamWord|=S[(S[i]+S[j])%256]<<(24-n*8)}this._i=i;this._j=j;return keystreamWord}C.RC4=StreamCipher._createHelper(RC4);var RC4Drop=C_algo.RC4Drop=RC4.extend({cfg:RC4.cfg.extend({drop:192}),_doReset:function(){RC4._doReset.call(this);for(var i=this.cfg.drop;i>0;i--){generateKeystreamWord.call(this)}}});C.RC4Drop=StreamCipher._createHelper(RC4Drop)}());return CryptoJS.RC4}))},242:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(215),__webpack_require__(216),__webpack_require__(226),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./enc-base64","./md5","./evpkdf","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var StreamCipher=C_lib.StreamCipher;var C_algo=C.algo;var S=[];var C_=[];var G=[];var Rabbit=C_algo.Rabbit=StreamCipher.extend({_doReset:function(){var K=this._key.words;var iv=this.cfg.iv;for(var i=0;
i<4;i++){K[i]=(((K[i]<<8)|(K[i]>>>24))&16711935)|(((K[i]<<24)|(K[i]>>>8))&4278255360)}var X=this._X=[K[0],(K[3]<<16)|(K[2]>>>16),K[1],(K[0]<<16)|(K[3]>>>16),K[2],(K[1]<<16)|(K[0]>>>16),K[3],(K[2]<<16)|(K[1]>>>16)];var C=this._C=[(K[2]<<16)|(K[2]>>>16),(K[0]&4294901760)|(K[1]&65535),(K[3]<<16)|(K[3]>>>16),(K[1]&4294901760)|(K[2]&65535),(K[0]<<16)|(K[0]>>>16),(K[2]&4294901760)|(K[3]&65535),(K[1]<<16)|(K[1]>>>16),(K[3]&4294901760)|(K[0]&65535)];this._b=0;for(var i=0;i<4;i++){nextState.call(this)}for(var i=0;i<8;i++){C[i]^=X[(i+4)&7]}if(iv){var IV=iv.words;var IV_0=IV[0];var IV_1=IV[1];var i0=(((IV_0<<8)|(IV_0>>>24))&16711935)|(((IV_0<<24)|(IV_0>>>8))&4278255360);var i2=(((IV_1<<8)|(IV_1>>>24))&16711935)|(((IV_1<<24)|(IV_1>>>8))&4278255360);var i1=(i0>>>16)|(i2&4294901760);var i3=(i2<<16)|(i0&65535);C[0]^=i0;C[1]^=i1;C[2]^=i2;C[3]^=i3;C[4]^=i0;C[5]^=i1;C[6]^=i2;C[7]^=i3;for(var i=0;i<4;i++){nextState.call(this)}}},_doProcessBlock:function(M,offset){var X=this._X;nextState.call(this);S[0]=X[0]^(X[5]>>>16)^(X[3]<<16);S[1]=X[2]^(X[7]>>>16)^(X[5]<<16);S[2]=X[4]^(X[1]>>>16)^(X[7]<<16);S[3]=X[6]^(X[3]>>>16)^(X[1]<<16);for(var i=0;i<4;i++){S[i]=(((S[i]<<8)|(S[i]>>>24))&16711935)|(((S[i]<<24)|(S[i]>>>8))&4278255360);M[offset+i]^=S[i]}},blockSize:128/32,ivSize:64/32});function nextState(){var X=this._X;var C=this._C;for(var i=0;i<8;i++){C_[i]=C[i]}C[0]=(C[0]+1295307597+this._b)|0;C[1]=(C[1]+3545052371+((C[0]>>>0)<(C_[0]>>>0)?1:0))|0;C[2]=(C[2]+886263092+((C[1]>>>0)<(C_[1]>>>0)?1:0))|0;C[3]=(C[3]+1295307597+((C[2]>>>0)<(C_[2]>>>0)?1:0))|0;C[4]=(C[4]+3545052371+((C[3]>>>0)<(C_[3]>>>0)?1:0))|0;C[5]=(C[5]+886263092+((C[4]>>>0)<(C_[4]>>>0)?1:0))|0;C[6]=(C[6]+1295307597+((C[5]>>>0)<(C_[5]>>>0)?1:0))|0;C[7]=(C[7]+3545052371+((C[6]>>>0)<(C_[6]>>>0)?1:0))|0;this._b=(C[7]>>>0)<(C_[7]>>>0)?1:0;for(var i=0;i<8;i++){var gx=X[i]+C[i];var ga=gx&65535;var gb=gx>>>16;var gh=((((ga*ga)>>>17)+ga*gb)>>>15)+gb*gb;var gl=(((gx&4294901760)*gx)|0)+(((gx&65535)*gx)|0);G[i]=gh^gl}X[0]=(G[0]+((G[7]<<16)|(G[7]>>>16))+((G[6]<<16)|(G[6]>>>16)))|0;X[1]=(G[1]+((G[0]<<8)|(G[0]>>>24))+G[7])|0;X[2]=(G[2]+((G[1]<<16)|(G[1]>>>16))+((G[0]<<16)|(G[0]>>>16)))|0;X[3]=(G[3]+((G[2]<<8)|(G[2]>>>24))+G[1])|0;X[4]=(G[4]+((G[3]<<16)|(G[3]>>>16))+((G[2]<<16)|(G[2]>>>16)))|0;X[5]=(G[5]+((G[4]<<8)|(G[4]>>>24))+G[3])|0;X[6]=(G[6]+((G[5]<<16)|(G[5]>>>16))+((G[4]<<16)|(G[4]>>>16)))|0;X[7]=(G[7]+((G[6]<<8)|(G[6]>>>24))+G[5])|0}C.Rabbit=StreamCipher._createHelper(Rabbit)}());return CryptoJS.Rabbit}))},243:function(module,exports,__webpack_require__){(function(root,factory,undef){if(true){module.exports=exports=factory(__webpack_require__(211),__webpack_require__(215),__webpack_require__(216),__webpack_require__(226),__webpack_require__(227))}else{if(typeof define==="function"&&define.amd){define(["./core","./enc-base64","./md5","./evpkdf","./cipher-core"],factory)}else{factory(root.CryptoJS)}}}(this,function(CryptoJS){(function(){var C=CryptoJS;var C_lib=C.lib;var StreamCipher=C_lib.StreamCipher;var C_algo=C.algo;var S=[];var C_=[];var G=[];var RabbitLegacy=C_algo.RabbitLegacy=StreamCipher.extend({_doReset:function(){var K=this._key.words;var iv=this.cfg.iv;var X=this._X=[K[0],(K[3]<<16)|(K[2]>>>16),K[1],(K[0]<<16)|(K[3]>>>16),K[2],(K[1]<<16)|(K[0]>>>16),K[3],(K[2]<<16)|(K[1]>>>16)];var C=this._C=[(K[2]<<16)|(K[2]>>>16),(K[0]&4294901760)|(K[1]&65535),(K[3]<<16)|(K[3]>>>16),(K[1]&4294901760)|(K[2]&65535),(K[0]<<16)|(K[0]>>>16),(K[2]&4294901760)|(K[3]&65535),(K[1]<<16)|(K[1]>>>16),(K[3]&4294901760)|(K[0]&65535)];this._b=0;for(var i=0;i<4;i++){nextState.call(this)}for(var i=0;i<8;i++){C[i]^=X[(i+4)&7]}if(iv){var IV=iv.words;var IV_0=IV[0];var IV_1=IV[1];var i0=(((IV_0<<8)|(IV_0>>>24))&16711935)|(((IV_0<<24)|(IV_0>>>8))&4278255360);var i2=(((IV_1<<8)|(IV_1>>>24))&16711935)|(((IV_1<<24)|(IV_1>>>8))&4278255360);var i1=(i0>>>16)|(i2&4294901760);var i3=(i2<<16)|(i0&65535);C[0]^=i0;C[1]^=i1;C[2]^=i2;C[3]^=i3;C[4]^=i0;C[5]^=i1;C[6]^=i2;C[7]^=i3;for(var i=0;i<4;i++){nextState.call(this)}}},_doProcessBlock:function(M,offset){var X=this._X;nextState.call(this);S[0]=X[0]^(X[5]>>>16)^(X[3]<<16);S[1]=X[2]^(X[7]>>>16)^(X[5]<<16);S[2]=X[4]^(X[1]>>>16)^(X[7]<<16);S[3]=X[6]^(X[3]>>>16)^(X[1]<<16);for(var i=0;i<4;i++){S[i]=(((S[i]<<8)|(S[i]>>>24))&16711935)|(((S[i]<<24)|(S[i]>>>8))&4278255360);M[offset+i]^=S[i]}},blockSize:128/32,ivSize:64/32});function nextState(){var X=this._X;var C=this._C;for(var i=0;i<8;i++){C_[i]=C[i]}C[0]=(C[0]+1295307597+this._b)|0;C[1]=(C[1]+3545052371+((C[0]>>>0)<(C_[0]>>>0)?1:0))|0;C[2]=(C[2]+886263092+((C[1]>>>0)<(C_[1]>>>0)?1:0))|0;C[3]=(C[3]+1295307597+((C[2]>>>0)<(C_[2]>>>0)?1:0))|0;C[4]=(C[4]+3545052371+((C[3]>>>0)<(C_[3]>>>0)?1:0))|0;C[5]=(C[5]+886263092+((C[4]>>>0)<(C_[4]>>>0)?1:0))|0;C[6]=(C[6]+1295307597+((C[5]>>>0)<(C_[5]>>>0)?1:0))|0;C[7]=(C[7]+3545052371+((C[6]>>>0)<(C_[6]>>>0)?1:0))|0;this._b=(C[7]>>>0)<(C_[7]>>>0)?1:0;for(var i=0;i<8;i++){var gx=X[i]+C[i];var ga=gx&65535;var gb=gx>>>16;var gh=((((ga*ga)>>>17)+ga*gb)>>>15)+gb*gb;var gl=(((gx&4294901760)*gx)|0)+(((gx&65535)*gx)|0);
G[i]=gh^gl}X[0]=(G[0]+((G[7]<<16)|(G[7]>>>16))+((G[6]<<16)|(G[6]>>>16)))|0;X[1]=(G[1]+((G[0]<<8)|(G[0]>>>24))+G[7])|0;X[2]=(G[2]+((G[1]<<16)|(G[1]>>>16))+((G[0]<<16)|(G[0]>>>16)))|0;X[3]=(G[3]+((G[2]<<8)|(G[2]>>>24))+G[1])|0;X[4]=(G[4]+((G[3]<<16)|(G[3]>>>16))+((G[2]<<16)|(G[2]>>>16)))|0;X[5]=(G[5]+((G[4]<<8)|(G[4]>>>24))+G[3])|0;X[6]=(G[6]+((G[5]<<16)|(G[5]>>>16))+((G[4]<<16)|(G[4]>>>16)))|0;X[7]=(G[7]+((G[6]<<8)|(G[6]>>>24))+G[5])|0}C.RabbitLegacy=StreamCipher._createHelper(RabbitLegacy)}());return CryptoJS.RabbitLegacy}))},246:function(module,exports,__webpack_require__){module.exports=__webpack_require__(247)},247:function(module,exports,__webpack_require__){var _version="1.4.2";var _code=__webpack_require__(206).code;var _utils=__webpack_require__(205).utils;var _msg=__webpack_require__(248);var _message=_msg._msg;var _msgHash={};var Queue=__webpack_require__(249).Queue;var CryptoJS=__webpack_require__(210);var _=__webpack_require__(183);window.URL=window.URL||window.webkitURL||window.mozURL||window.msURL;if(window.XDomainRequest){XDomainRequest.prototype.oldsend=XDomainRequest.prototype.send;XDomainRequest.prototype.send=function(){XDomainRequest.prototype.oldsend.apply(this,arguments);this.readyState=2}}Strophe.Request.prototype._newXHR=function(){var xhr=_utils.xmlrequest(true);if(xhr.overrideMimeType){xhr.overrideMimeType("text/xml")}xhr.onreadystatechange=this.func.bind(null,this);return xhr};Strophe.Websocket.prototype._closeSocket=function(){if(this.socket){var me=this;setTimeout(function(){try{me.socket.close()}catch(e){}},0)}else{this.socket=null}};Strophe.Websocket.prototype._onMessage=function(message){logMessage(message);var elem,data;if(message.data.indexOf("<close ")===0){elem=new DOMParser().parseFromString(message.data,"text/xml").documentElement;var see_uri=elem.getAttribute("see-other-uri");if(see_uri){this._conn._changeConnectStatus(Strophe.Status.REDIRECT,"Received see-other-uri, resetting connection");this._conn.reset();this._conn.service=see_uri;this._connect()}else{this._conn._doDisconnect("receive <close> from server")}return}else{if(message.data.search("<open ")===0){elem=new DOMParser().parseFromString(message.data,"text/xml").documentElement;if(!this._handleStreamStart(elem)){return}}else{data=this._streamWrap(message.data);elem=new DOMParser().parseFromString(data,"text/xml").documentElement}}if(this._check_streamerror(elem,Strophe.Status.ERROR)){return}if(this._conn.disconnecting&&elem.firstChild.nodeName==="presence"&&elem.firstChild.getAttribute("type")==="unavailable"){this._conn.xmlInput(elem);this._conn.rawInput(Strophe.serialize(elem));return}this._conn._dataRecv(elem,message.data)};var _listenNetwork=function _listenNetwork(onlineCallback,offlineCallback){if(window.addEventListener){window.addEventListener("online",onlineCallback);window.addEventListener("offline",offlineCallback)}else{if(window.attachEvent){if(document.body){document.body.attachEvent("ononline",onlineCallback);document.body.attachEvent("onoffline",offlineCallback)}else{window.attachEvent("load",function(){document.body.attachEvent("ononline",onlineCallback);document.body.attachEvent("onoffline",offlineCallback)})}}else{}}};var _parseRoom=function _parseRoom(result){var rooms=[];var items=result.getElementsByTagName("item");if(items){for(var i=0;i<items.length;i++){var item=items[i];var roomJid=item.getAttribute("jid");var tmp=roomJid.split("@")[0];var room={jid:roomJid,name:item.getAttribute("name"),roomId:tmp.split("_")[1]};rooms.push(room)}}return rooms};var _parseRoomOccupants=function _parseRoomOccupants(result){var occupants=[];var items=result.getElementsByTagName("item");if(items){for(var i=0;i<items.length;i++){var item=items[i];var room={jid:item.getAttribute("jid"),name:item.getAttribute("name")};occupants.push(room)}}return occupants};var _parseResponseMessage=function _parseResponseMessage(msginfo){var parseMsgData={errorMsg:true,data:[]};var msgBodies=msginfo.getElementsByTagName("body");if(msgBodies){for(var i=0;i<msgBodies.length;i++){var msgBody=msgBodies[i];var childNodes=msgBody.childNodes;if(childNodes&&childNodes.length>0){var childNode=msgBody.childNodes[0];if(childNode.nodeType==Strophe.ElementType.TEXT){var jsondata=childNode.wholeText||childNode.nodeValue;jsondata=jsondata.replace("\n","<br>");try{var data=eval("("+jsondata+")");parseMsgData.errorMsg=false;parseMsgData.data=[data]}catch(e){}}}}var delayTags=msginfo.getElementsByTagName("delay");if(delayTags&&delayTags.length>0){var delayTag=delayTags[0];var delayMsgTime=delayTag.getAttribute("stamp");if(delayMsgTime){parseMsgData.delayTimeStamp=delayMsgTime}}}else{var childrens=msginfo.childNodes;if(childrens&&childrens.length>0){var child=msginfo.childNodes[0];if(child.nodeType==Strophe.ElementType.TEXT){try{var data=eval("("+child.nodeValue+")");parseMsgData.errorMsg=false;parseMsgData.data=[data]}catch(e){}}}}return parseMsgData};var _parseNameFromJidFn=function _parseNameFromJidFn(jid,domain){domain=domain||"";var tempstr=jid;
var findex=tempstr.indexOf("_");if(findex!==-1){tempstr=tempstr.substring(findex+1)}var atindex=tempstr.indexOf("@"+domain);if(atindex!==-1){tempstr=tempstr.substring(0,atindex)}return tempstr};var _parseFriend=function _parseFriend(queryTag,conn,from){var rouster=[];var items=queryTag.getElementsByTagName("item");if(items){for(var i=0;i<items.length;i++){var item=items[i];var jid=item.getAttribute("jid");if(!jid){continue}var subscription=item.getAttribute("subscription");var friend={subscription:subscription,jid:jid};var ask=item.getAttribute("ask");if(ask){friend.ask=ask}var name=item.getAttribute("name");if(name){friend.name=name}else{var n=_parseNameFromJidFn(jid);friend.name=n}var groups=[];Strophe.forEachChild(item,"group",function(group){groups.push(Strophe.getText(group))});friend.groups=groups;rouster.push(friend);if(conn&&subscription=="from"&&!ask){conn.subscribe({toJid:jid})}if(conn&&subscription=="to"){conn.subscribed({toJid:jid})}}}return rouster};var _login=function _login(options,conn){var accessToken=options.access_token||"";if(accessToken==""){var loginfo=_utils.stringify(options);conn.onError({type:_code.WEBIM_CONNCTION_OPEN_USERGRID_ERROR,data:options});return}conn.context.accessToken=options.access_token;conn.context.accessTokenExpires=options.expires_in;var stropheConn=null;if(conn.isOpening()&&conn.context.stropheConn){stropheConn=conn.context.stropheConn}else{if(conn.isOpened()&&conn.context.stropheConn){stropheConn=conn.getStrophe()}else{stropheConn=conn.getStrophe()}}var callback=function callback(status,msg){_loginCallback(status,msg,conn)};conn.context.stropheConn=stropheConn;if(conn.route){stropheConn.connect(conn.context.jid,"$t$"+accessToken,callback,conn.wait,conn.hold,conn.route)}else{stropheConn.connect(conn.context.jid,"$t$"+accessToken,callback,conn.wait,conn.hold)}};var _parseMessageType=function _parseMessageType(msginfo){var receiveinfo=msginfo.getElementsByTagName("received"),inviteinfo=msginfo.getElementsByTagName("invite"),deliveryinfo=msginfo.getElementsByTagName("delivery"),acked=msginfo.getElementsByTagName("acked"),error=msginfo.getElementsByTagName("error"),msgtype="normal";if(receiveinfo&&receiveinfo.length>0&&receiveinfo[0].namespaceURI==="urn:xmpp:receipts"){msgtype="received"}else{if(inviteinfo&&inviteinfo.length>0){msgtype="invite"}else{if(deliveryinfo&&deliveryinfo.length>0){msgtype="delivery"}else{if(acked&&acked.length){msgtype="acked"}else{if(error&&error.length){var errorItem=error[0],userMuted=errorItem.getElementsByTagName("user-muted");if(userMuted&&userMuted.length){msgtype="userMuted"}}}}}}return msgtype};var _handleMessageQueue=function _handleMessageQueue(conn){for(var i in _msgHash){if(_msgHash.hasOwnProperty(i)){_msgHash[i].send(conn)}}};var _loginCallback=function _loginCallback(status,msg,conn){var conflict,error;if(msg==="conflict"){conflict=true}if(status==Strophe.Status.CONNFAIL){error={type:_code.WEBIM_CONNCTION_SERVER_CLOSE_ERROR,msg:msg,reconnect:true};conflict&&(error.conflict=true);conn.onError(error)}else{if(status==Strophe.Status.ATTACHED||status==Strophe.Status.CONNECTED){conn.autoReconnectNumTotal=0;conn.intervalId=setInterval(function(){conn.handelSendQueue()},200);var handleMessage=function handleMessage(msginfo){var delivery=msginfo.getElementsByTagName("delivery");var acked=msginfo.getElementsByTagName("acked");if(delivery.length){conn.handleDeliveredMessage(msginfo);return true}if(acked.length){conn.handleAckedMessage(msginfo);return true}var type=_parseMessageType(msginfo);switch(type){case"received":conn.handleReceivedMessage(msginfo);return true;case"invite":conn.handleInviteMessage(msginfo);return true;case"delivery":conn.handleDeliveredMessage(msginfo);return true;case"acked":conn.handleAckedMessage(msginfo);return true;case"userMuted":conn.handleMutedMessage(msginfo);return true;default:conn.handleMessage(msginfo);return true}};var handlePresence=function handlePresence(msginfo){conn.handlePresence(msginfo);return true};var handlePing=function handlePing(msginfo){conn.handlePing(msginfo);return true};var handleIqRoster=function handleIqRoster(msginfo){conn.handleIqRoster(msginfo);return true};var handleIqPrivacy=function handleIqPrivacy(msginfo){conn.handleIqPrivacy(msginfo);return true};var handleIq=function handleIq(msginfo){conn.handleIq(msginfo);return true};conn.addHandler(handleMessage,null,"message",null,null,null);conn.addHandler(handlePresence,null,"presence",null,null,null);conn.addHandler(handlePing,"urn:xmpp:ping","iq","get",null,null);conn.addHandler(handleIqRoster,"jabber:iq:roster","iq","set",null,null);conn.addHandler(handleIqPrivacy,"jabber:iq:privacy","iq","set",null,null);conn.addHandler(handleIq,null,"iq",null,null,null);conn.registerConfrIQHandler&&conn.registerConfrIQHandler();conn.context.status=_code.STATUS_OPENED;var supportRecMessage=[_code.WEBIM_MESSAGE_REC_TEXT,_code.WEBIM_MESSAGE_REC_EMOJI];if(_utils.isCanDownLoadFile){supportRecMessage.push(_code.WEBIM_MESSAGE_REC_PHOTO);supportRecMessage.push(_code.WEBIM_MESSAGE_REC_AUDIO_FILE)
}var supportSedMessage=[_code.WEBIM_MESSAGE_SED_TEXT];if(_utils.isCanUploadFile){supportSedMessage.push(_code.WEBIM_MESSAGE_REC_PHOTO);supportSedMessage.push(_code.WEBIM_MESSAGE_REC_AUDIO_FILE)}conn.notifyVersion();conn.retry&&_handleMessageQueue(conn);conn.heartBeat();conn.isAutoLogin&&conn.setPresence();conn.onOpened({canReceive:supportRecMessage,canSend:supportSedMessage,accessToken:conn.context.accessToken})}else{if(status==Strophe.Status.DISCONNECTING){if(conn.isOpened()){conn.stopHeartBeat();conn.context.status=_code.STATUS_CLOSING;error={type:_code.WEBIM_CONNCTION_SERVER_CLOSE_ERROR,msg:msg,reconnect:true};conflict&&(error.conflict=true);conn.onError(error)}}else{if(status==Strophe.Status.DISCONNECTED){if(conn.isOpened()){if(conn.autoReconnectNumTotal<conn.autoReconnectNumMax){conn.reconnect();return}else{error={type:_code.WEBIM_CONNCTION_DISCONNECTED};conn.onError(error)}}conn.context.status=_code.STATUS_CLOSED;conn.clear();conn.onClosed()}else{if(status==Strophe.Status.AUTHFAIL){error={type:_code.WEBIM_CONNCTION_AUTH_ERROR};conflict&&(error.conflict=true);conn.onError(error);conn.clear()}else{if(status==Strophe.Status.ERROR){conn.context.status=_code.STATUS_ERROR;error={type:_code.WEBIM_CONNCTION_SERVER_ERROR};conflict&&(error.conflict=true);conn.onError(error)}}}}}}conn.context.status_now=status};var _getJid=function _getJid(options,conn){var jid=options.toJid||"";if(jid===""){var appKey=conn.context.appKey||"";var toJid=appKey+"_"+options.to+"@"+conn.domain;if(options.resource){toJid=toJid+"/"+options.resource}jid=toJid}return jid};var _getJidByName=function _getJidByName(name,conn){var options={to:name};return _getJid(options,conn)};var _validCheck=function _validCheck(options,conn){options=options||{};if(options.user==""){conn.onError({type:_code.WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR});return false}var user=options.user+""||"";var appKey=options.appKey||"";var devInfos=appKey.split("#");if(devInfos.length!==2){conn.onError({type:_code.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR});return false}var orgName=devInfos[0];var appName=devInfos[1];if(!orgName){conn.onError({type:_code.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR});return false}if(!appName){conn.onError({type:_code.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR});return false}var jid=appKey+"_"+user.toLowerCase()+"@"+conn.domain,resource=options.resource||"webim";if(conn.isMultiLoginSessions){resource+=user+new Date().getTime()+Math.floor(Math.random().toFixed(6)*1000000)}conn.context.jid=jid+"/"+resource;conn.context.userId=user;conn.context.appKey=appKey;conn.context.appName=appName;conn.context.orgName=orgName;return true};var _getXmppUrl=function _getXmppUrl(baseUrl,https){if(/^(ws|http)s?:\/\/?/.test(baseUrl)){return baseUrl}var url={prefix:"http",base:"://"+baseUrl,suffix:"/http-bind/"};if(https&&_utils.isSupportWss){url.prefix="wss";url.suffix="/ws/"}else{if(https){url.prefix="https"}else{if(window.WebSocket){url.prefix="ws";url.suffix="/ws/"}}}return url.prefix+url.base+url.suffix};var connection=function connection(options){if(!this instanceof connection){return new connection(options)}var options=options||{};this.isHttpDNS=options.isHttpDNS||false;this.isMultiLoginSessions=options.isMultiLoginSessions||false;this.wait=options.wait||30;this.retry=options.retry||false;this.https=options.https||location.protocol==="https:";this.url=_getXmppUrl(options.url,this.https);this.hold=options.hold||1;this.route=options.route||null;this.domain=options.domain||"easemob.com";this.inactivity=options.inactivity||30;this.heartBeatWait=options.heartBeatWait||4500;this.maxRetries=options.maxRetries||5;this.isAutoLogin=options.isAutoLogin===false?false:true;this.pollingTime=options.pollingTime||800;this.stropheConn=false;this.autoReconnectNumMax=options.autoReconnectNumMax||0;this.autoReconnectNumTotal=0;this.autoReconnectInterval=options.autoReconnectInterval||0;this.context={status:_code.STATUS_INIT};this.sendQueue=new Queue();this.intervalId=null;this.apiUrl=options.apiUrl||"";this.isWindowSDK=options.isWindowSDK||false;this.encrypt=options.encrypt||{encrypt:{type:"none"}};this.delivery=options.delivery||false;this.user="";this.orgName="";this.appName="";this.token="";this.dnsArr=["https://rs.easemob.com","https://rsbak.easemob.com","http://*************","http://**************"];this.dnsIndex=0;this.dnsTotal=this.dnsArr.length;this.restHosts=null;this.restIndex=0;this.restTotal=0;this.xmppHosts=null;this.xmppIndex=0;this.xmppTotal=0;this.groupOption={}};connection.prototype.registerUser=function(options){if(location.protocol!="https:"&&this.isHttpDNS){this.dnsIndex=0;this.getHttpDNS(options,"signup")}else{this.signup(options)}};connection.prototype.handelSendQueue=function(){var options=this.sendQueue.pop();if(options!==null){this.sendReceiptsMessage(options)}};connection.prototype.listen=function(options){this.onOpened=options.onOpened||_utils.emptyfn;this.onClosed=options.onClosed||_utils.emptyfn;this.onTextMessage=options.onTextMessage||_utils.emptyfn;this.onEmojiMessage=options.onEmojiMessage||_utils.emptyfn;
this.onPictureMessage=options.onPictureMessage||_utils.emptyfn;this.onAudioMessage=options.onAudioMessage||_utils.emptyfn;this.onVideoMessage=options.onVideoMessage||_utils.emptyfn;this.onFileMessage=options.onFileMessage||_utils.emptyfn;this.onLocationMessage=options.onLocationMessage||_utils.emptyfn;this.onCmdMessage=options.onCmdMessage||_utils.emptyfn;this.onPresence=options.onPresence||_utils.emptyfn;this.onRoster=options.onRoster||_utils.emptyfn;this.onError=options.onError||_utils.emptyfn;this.onReceivedMessage=options.onReceivedMessage||_utils.emptyfn;this.onInviteMessage=options.onInviteMessage||_utils.emptyfn;this.onDeliverdMessage=options.onDeliveredMessage||_utils.emptyfn;this.onReadMessage=options.onReadMessage||_utils.emptyfn;this.onMutedMessage=options.onMutedMessage||_utils.emptyfn;this.onOffline=options.onOffline||_utils.emptyfn;this.onOnline=options.onOnline||_utils.emptyfn;this.onConfirmPop=options.onConfirmPop||_utils.emptyfn;this.onCreateGroup=options.onCreateGroup||_utils.emptyfn;this.onUpdateMyGroupList=options.onUpdateMyGroupList||_utils.emptyfn;this.onUpdateMyRoster=options.onUpdateMyRoster||_utils.emptyfn;this.onBlacklistUpdate=options.onBlacklistUpdate||_utils.emptyfn;_listenNetwork(this.onOnline,this.onOffline)};connection.prototype.heartBeat=function(){var forcing=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var me=this;var isNeed=!/^ws|wss/.test(me.url)||/mobile/.test(navigator.userAgent);if(this.heartBeatID||!forcing&&!isNeed){return}var options={toJid:this.domain,type:"normal"};this.heartBeatID=setInterval(function(){me.ping(options)},this.heartBeatWait)};connection.prototype.stopHeartBeat=function(){if(typeof this.heartBeatID=="number"){this.heartBeatID=clearInterval(this.heartBeatID)}};connection.prototype.sendReceiptsMessage=function(options){var dom=$msg({from:this.context.jid||"",to:this.domain,id:options.id||""}).c("received",{xmlns:"urn:xmpp:receipts",id:options.id||""});this.sendCommand(dom.tree())};connection.prototype.cacheReceiptsMessage=function(options){this.sendQueue.push(options)};connection.prototype.getStrophe=function(){if(location.protocol!="https:"&&this.isHttpDNS){var url="";var host=this.xmppHosts[this.xmppIndex];var domain=_utils.getXmlFirstChild(host,"domain");var ip=_utils.getXmlFirstChild(host,"ip");if(ip){url=ip.textContent;var port=_utils.getXmlFirstChild(host,"port");if(port.textContent!="80"){url+=":"+port.textContent}}else{url=domain.textContent}if(url!=""){var parter=/(.+\/\/).+(\/.+)/;this.url=this.url.replace(parter,"$1"+url+"$2")}}var stropheConn=new Strophe.Connection(this.url,{inactivity:this.inactivity,maxRetries:this.maxRetries,pollingTime:this.pollingTime});return stropheConn};connection.prototype.getHostsByTag=function(data,tagName){var tag=_utils.getXmlFirstChild(data,tagName);if(!tag){console.log(tagName+" hosts error");return null}var hosts=tag.getElementsByTagName("hosts");if(hosts.length==0){console.log(tagName+" hosts error2");return null}return hosts[0].getElementsByTagName("host")};connection.prototype.getRestFromHttpDNS=function(options,type){if(this.restIndex>this.restTotal){console.log("rest hosts all tried,quit");return}var url="";var host=this.restHosts[this.restIndex];var domain=_utils.getXmlFirstChild(host,"domain");var ip=_utils.getXmlFirstChild(host,"ip");if(ip){var port=_utils.getXmlFirstChild(host,"port");url=(location.protocol==="https:"?"https:":"http:")+"//"+ip.textContent+":"+port.textContent}else{url=(location.protocol==="https:"?"https:":"http:")+"//"+domain.textContent}if(url!=""){this.apiUrl=url;options.apiUrl=url}if(type=="login"){this.login(options)}else{this.signup(options)}};connection.prototype.getHttpDNS=function(options,type){if(this.restHosts){this.getRestFromHttpDNS(options,type);return}var self=this;var suc=function suc(data,xhr){data=new DOMParser().parseFromString(data,"text/xml").documentElement;var restHosts=self.getHostsByTag(data,"rest");if(!restHosts){console.log("rest hosts error3");return}self.restHosts=restHosts;self.restTotal=restHosts.length;var xmppHosts=self.getHostsByTag(data,"xmpp");if(!xmppHosts){console.log("xmpp hosts error3");return}self.xmppHosts=xmppHosts;self.xmppTotal=xmppHosts.length;self.getRestFromHttpDNS(options,type)};var error=function error(res,xhr,msg){console.log("getHttpDNS error",res,msg);self.dnsIndex++;if(self.dnsIndex<self.dnsTotal){self.getHttpDNS(options,type)}};var options2={url:this.dnsArr[this.dnsIndex]+"/easemob/server.xml",dataType:"text",type:"GET",data:{app_key:encodeURIComponent(options.appKey)},success:suc||_utils.emptyfn,error:error||_utils.emptyfn};_utils.ajax(options2)};connection.prototype.signup=function(options){var self=this;var orgName=options.orgName||"";var appName=options.appName||"";var appKey=options.appKey||"";var suc=options.success||EMPTYFN;var err=options.error||EMPTYFN;if(!orgName&&!appName&&appKey){var devInfos=appKey.split("#");if(devInfos.length===2){orgName=devInfos[0];appName=devInfos[1]}}if(!orgName&&!appName){err({type:_code.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR});
return}var error=function error(res,xhr,msg){if(location.protocol!="https:"&&self.isHttpDNS){if(self.restIndex+1<self.restTotal){self.restIndex++;self.getRestFromHttpDNS(options,"signup");return}}self.clear();err(res)};var https=options.https||https;var apiUrl=options.apiUrl;var restUrl=apiUrl+"/"+orgName+"/"+appName+"/users";var userjson={username:options.username,password:options.password,nickname:options.nickname||""};var userinfo=_utils.stringify(userjson);var options2={url:restUrl,dataType:"json",data:userinfo,success:suc,error:error};_utils.ajax(options2)};connection.prototype.open=function(options){var appkey=options.appKey,orgName=appkey.split("#")[0],appName=appkey.split("#")[1];this.orgName=orgName;this.appName=appName;if(options.accessToken){this.token=options.accessToken}if(options.xmppURL){this.url=_getXmppUrl(options.xmppURL,this.https)}if(location.protocol!="https:"&&this.isHttpDNS){this.dnsIndex=0;this.getHttpDNS(options,"login")}else{this.login(options)}};connection.prototype.login=function(options){this.user=options.user;var pass=_validCheck(options,this);if(!pass){return}var conn=this;if(conn.isOpened()){return}if(options.accessToken){options.access_token=options.accessToken;conn.context.restTokenData=options;_login(options,conn)}else{var apiUrl=options.apiUrl;var userId=this.context.userId;var pwd=options.pwd||"";var appName=this.context.appName;var orgName=this.context.orgName;var suc=function suc(data,xhr){conn.context.status=_code.STATUS_DOLOGIN_IM;conn.context.restTokenData=data;if(options.success){options.success(data)}conn.token=data.access_token;_login(data,conn)};var error=function error(res,xhr,msg){if(options.error){options.error()}if(location.protocol!="https:"&&conn.isHttpDNS){if(conn.restIndex+1<conn.restTotal){conn.restIndex++;conn.getRestFromHttpDNS(options,"login");return}}conn.clear();if(res.error&&res.error_description){conn.onError({type:_code.WEBIM_CONNCTION_OPEN_USERGRID_ERROR,data:res,xhr:xhr})}else{conn.onError({type:_code.WEBIM_CONNCTION_OPEN_ERROR,data:res,xhr:xhr})}};this.context.status=_code.STATUS_DOLOGIN_USERGRID;var loginJson={grant_type:"password",username:userId,password:pwd,timestamp:+new Date()};var loginfo=_utils.stringify(loginJson);var options2={url:apiUrl+"/"+orgName+"/"+appName+"/token",dataType:"json",data:loginfo,success:suc||_utils.emptyfn,error:error||_utils.emptyfn};_utils.ajax(options2)}};connection.prototype.attach=function(options){var pass=_validCheck(options,this);if(!pass){return}options=options||{};var accessToken=options.accessToken||"";if(accessToken==""){this.onError({type:_code.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR});return}var sid=options.sid||"";if(sid===""){this.onError({type:_code.WEBIM_CONNCTION_SESSIONID_NOT_ASSIGN_ERROR});return}var rid=options.rid||"";if(rid===""){this.onError({type:_code.WEBIM_CONNCTION_RID_NOT_ASSIGN_ERROR});return}var stropheConn=this.getStrophe();this.context.accessToken=accessToken;this.context.stropheConn=stropheConn;this.context.status=_code.STATUS_DOLOGIN_IM;var conn=this;var callback=function callback(status,msg){_loginCallback(status,msg,conn)};var jid=this.context.jid;var wait=this.wait;var hold=this.hold;var wind=this.wind||5;stropheConn.attach(jid,sid,rid,callback,wait,hold,wind)};connection.prototype.close=function(reason){this.stopHeartBeat();var status=this.context.status;if(status==_code.STATUS_INIT){return}if(this.isClosed()||this.isClosing()){return}this.context.status=_code.STATUS_CLOSING;this.context.stropheConn.disconnect(reason)};connection.prototype.addHandler=function(handler,ns,name,type,id,from,options){this.context.stropheConn.addHandler(handler,ns,name,type,id,from,options)};connection.prototype.notifyVersion=function(suc,fail){var jid=_getJid({},this);var dom=$iq({from:this.context.jid||"",to:this.domain,type:"result"}).c("query",{xmlns:"jabber:iq:version"}).c("name").t("easemob").up().c("version").t(_version).up().c("os").t("webim");var suc=suc||_utils.emptyfn;var error=fail||this.onError;var failFn=function failFn(ele){error({type:_code.WEBIM_CONNCTION_NOTIFYVERSION_ERROR,data:ele})};this.context.stropheConn.sendIQ(dom.tree(),suc,failFn);return};connection.prototype.handlePresence=function(msginfo){if(this.isClosed()){return}var from=msginfo.getAttribute("from")||"";var to=msginfo.getAttribute("to")||"";var type=msginfo.getAttribute("type")||"";var presence_type=msginfo.getAttribute("presence_type")||"";var fromUser=_parseNameFromJidFn(from);var toUser=_parseNameFromJidFn(to);var isCreate=false;var isMemberJoin=false;var isDecline=false;var isApply=false;var info={from:fromUser,to:toUser,fromJid:from,toJid:to,type:type,chatroom:msginfo.getElementsByTagName("roomtype").length?true:false};var showTags=msginfo.getElementsByTagName("show");if(showTags&&showTags.length>0){var showTag=showTags[0];info.show=Strophe.getText(showTag)}var statusTags=msginfo.getElementsByTagName("status");if(statusTags&&statusTags.length>0){var statusTag=statusTags[0];info.status=Strophe.getText(statusTag);
info.code=statusTag.getAttribute("code")}var priorityTags=msginfo.getElementsByTagName("priority");if(priorityTags&&priorityTags.length>0){var priorityTag=priorityTags[0];info.priority=Strophe.getText(priorityTag)}var error=msginfo.getElementsByTagName("error");if(error&&error.length>0){var error=error[0];info.error={code:error.getAttribute("code")}}var destroy=msginfo.getElementsByTagName("destroy");if(destroy&&destroy.length>0){var destroy=destroy[0];info.destroy=true;var reason=destroy.getElementsByTagName("reason");if(reason&&reason.length>0){info.reason=Strophe.getText(reason[0])}}var members=msginfo.getElementsByTagName("item");if(members&&members.length>0){var member=members[0];var role=member.getAttribute("role");var jid=member.getAttribute("jid");var affiliation=member.getAttribute("affiliation");if(role=="none"&&jid){var kickedMember=_parseNameFromJidFn(jid);var actor=member.getElementsByTagName("actor")[0];var actorNick=actor.getAttribute("nick");info.actor=actorNick;info.kicked=kickedMember}if(role=="moderator"&&info.code=="201"){if(affiliation==="owner"){info.type="createGroupACK";isCreate=true}}}var x=msginfo.getElementsByTagName("x");if(x&&x.length>0){var apply=x[0].getElementsByTagName("apply");var accept=x[0].getElementsByTagName("accept");var item=x[0].getElementsByTagName("item");var decline=x[0].getElementsByTagName("decline");var addAdmin=x[0].getElementsByTagName("add_admin");var removeAdmin=x[0].getElementsByTagName("remove_admin");var addMute=x[0].getElementsByTagName("add_mute");var removeMute=x[0].getElementsByTagName("remove_mute");if(apply&&apply.length>0){isApply=true;info.toNick=apply[0].getAttribute("toNick");info.type="joinGroupNotifications";var groupJid=apply[0].getAttribute("to");var gid=groupJid.split("@")[0].split("_");gid=gid[gid.length-1];info.gid=gid}else{if(accept&&accept.length>0){info.type="joinPublicGroupSuccess"}else{if(item&&item.length>0){var affiliation=item[0].getAttribute("affiliation"),role=item[0].getAttribute("role");if(affiliation=="member"||role=="participant"){isMemberJoin=true;info.mid=info.fromJid.split("/");info.mid=info.mid[info.mid.length-1];info.type="memberJoinPublicGroupSuccess"}}else{if(decline&&decline.length){isDecline=true;var gid=decline[0].getAttribute("fromNick");var owner=_parseNameFromJidFn(decline[0].getAttribute("from"));info.type="joinPublicGroupDeclined";info.owner=owner;info.gid=gid}else{if(addAdmin&&addAdmin.length>0){var gid=_parseNameFromJidFn(addAdmin[0].getAttribute("mucjid"));var owner=_parseNameFromJidFn(addAdmin[0].getAttribute("from"));info.owner=owner;info.gid=gid;info.type="addAdmin"}else{if(removeAdmin&&removeAdmin.length>0){var gid=_parseNameFromJidFn(removeAdmin[0].getAttribute("mucjid"));var owner=_parseNameFromJidFn(removeAdmin[0].getAttribute("from"));info.owner=owner;info.gid=gid;info.type="removeAdmin"}else{if(addMute&&addMute.length>0){var gid=_parseNameFromJidFn(addMute[0].getAttribute("mucjid"));var owner=_parseNameFromJidFn(addMute[0].getAttribute("from"));info.owner=owner;info.gid=gid;info.type="addMute"}else{if(removeMute&&removeMute.length>0){var gid=_parseNameFromJidFn(removeMute[0].getAttribute("mucjid"));var owner=_parseNameFromJidFn(removeMute[0].getAttribute("from"));info.owner=owner;info.gid=gid;info.type="removeMute"}}}}}}}}}if(info.chatroom){info.presence_type=presence_type;info.original_type=info.type;var reflectUser=from.slice(from.lastIndexOf("/")+1);if(reflectUser===this.context.userId){if(info.type===""&&!info.code){info.type="joinChatRoomSuccess"}else{if(presence_type==="unavailable"||info.type==="unavailable"){if(!info.status){info.type="leaveChatRoom"}else{if(info.code==110){info.type="leaveChatRoom"}else{if(info.error&&info.error.code==406){info.type="reachChatRoomCapacity"}}}}}}}else{info.presence_type=presence_type;info.original_type=type;if(/subscribe/.test(info.type)){}else{if(type==""&&!info.status&&!info.error&&!isCreate&&!isApply&&!isMemberJoin&&!isDecline){console.log(2222222,msginfo,info,isApply)}else{if(presence_type==="unavailable"||type==="unavailable"){if(info.destroy){info.type="deleteGroupChat"}else{if(info.code==307||info.code==321){var nick=msginfo.getAttribute("nick");if(!nick){info.type="leaveGroup"}else{info.type="removedFromGroup"}}}}}}}this.onPresence(info,msginfo)};connection.prototype.handlePing=function(e){if(this.isClosed()){return}var id=e.getAttribute("id");var from=e.getAttribute("from");var to=e.getAttribute("to");var dom=$iq({from:to,to:from,id:id,type:"result"});this.sendCommand(dom.tree())};connection.prototype.handleIq=function(iq){return true};connection.prototype.handleIqPrivacy=function(msginfo){var list=msginfo.getElementsByTagName("list");if(list.length==0){return}this.getBlacklist()};connection.prototype.handleIqRoster=function(e){var id=e.getAttribute("id");var from=e.getAttribute("from")||"";var name=_parseNameFromJidFn(from);var curJid=this.context.jid;var curUser=this.context.userId;var iqresult=$iq({type:"result",id:id,from:curJid});this.sendCommand(iqresult.tree());
var msgBodies=e.getElementsByTagName("query");if(msgBodies&&msgBodies.length>0){var queryTag=msgBodies[0];var rouster=_parseFriend(queryTag,this,from);this.onRoster(rouster)}return true};connection.prototype.handleMessage=function(msginfo){var self=this;if(this.isClosed()){return}var id=msginfo.getAttribute("id")||"";this.cacheReceiptsMessage({id:id});var parseMsgData=_parseResponseMessage(msginfo);if(parseMsgData.errorMsg){this.handlePresence(msginfo);return}var error=msginfo.getElementsByTagName("error");var errorCode="";var errorText="";var errorBool=false;if(error.length>0){errorBool=true;errorCode=error[0].getAttribute("code");var textDOM=error[0].getElementsByTagName("text");errorText=textDOM[0].textContent||textDOM[0].text}var msgDatas=parseMsgData.data;for(var i in msgDatas){if(!msgDatas.hasOwnProperty(i)){continue}var msg=msgDatas[i];if(!msg.from||!msg.to){continue}var from=(msg.from+"").toLowerCase();var too=(msg.to+"").toLowerCase();var extmsg=msg.ext||{};var chattype="";var typeEl=msginfo.getElementsByTagName("roomtype");if(typeEl.length){chattype=typeEl[0].getAttribute("type")||"chat"}else{chattype=msginfo.getAttribute("type")||"chat"}var msgBodies=msg.bodies;if(!msgBodies||msgBodies.length==0){continue}var msgBody=msg.bodies[0];var type=msgBody.type;try{switch(type){case"txt":var receiveMsg=msgBody.msg;if(self.encrypt.type==="base64"){receiveMsg=atob(receiveMsg)}else{if(self.encrypt.type==="aes"){var key=CryptoJS.enc.Utf8.parse(self.encrypt.key);var iv=CryptoJS.enc.Utf8.parse(self.encrypt.iv);var mode=self.encrypt.mode.toLowerCase();var option={};if(mode==="cbc"){option={iv:iv,mode:CryptoJS.mode.CBC,padding:CryptoJS.pad.Pkcs7}}else{if(mode==="ebc"){option={mode:CryptoJS.mode.ECB,padding:CryptoJS.pad.Pkcs7}}}var encryptedBase64Str=receiveMsg;var decryptedData=CryptoJS.AES.decrypt(encryptedBase64Str,key,option);var decryptedStr=decryptedData.toString(CryptoJS.enc.Utf8);receiveMsg=decryptedStr}}var emojibody=_utils.parseTextMessage(receiveMsg,WebIM.Emoji);if(emojibody.isemoji){var msg={id:id,type:chattype,from:from,to:too,delay:parseMsgData.delayTimeStamp,data:emojibody.body,ext:extmsg};!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onEmojiMessage(msg)}else{var msg={id:id,type:chattype,from:from,to:too,delay:parseMsgData.delayTimeStamp,data:receiveMsg,ext:extmsg};!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onTextMessage(msg)}break;case"img":var rwidth=0;var rheight=0;if(msgBody.size){rwidth=msgBody.size.width;rheight=msgBody.size.height}var msg={id:id,type:chattype,from:from,to:too,url:location.protocol!="https:"&&self.isHttpDNS?self.apiUrl+msgBody.url.substr(msgBody.url.indexOf("/",9)):msgBody.url,secret:msgBody.secret,filename:msgBody.filename,thumb:msgBody.thumb,thumb_secret:msgBody.thumb_secret,file_length:msgBody.file_length||"",width:rwidth,height:rheight,filetype:msgBody.filetype||"",accessToken:this.context.accessToken||"",ext:extmsg,delay:parseMsgData.delayTimeStamp};!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onPictureMessage(msg);break;case"audio":var msg={id:id,type:chattype,from:from,to:too,url:location.protocol!="https:"&&self.isHttpDNS?self.apiUrl+msgBody.url.substr(msgBody.url.indexOf("/",9)):msgBody.url,secret:msgBody.secret,filename:msgBody.filename,length:msgBody.length||"",file_length:msgBody.file_length||"",filetype:msgBody.filetype||"",accessToken:this.context.accessToken||"",ext:extmsg,delay:parseMsgData.delayTimeStamp};!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onAudioMessage(msg);break;case"file":var msg={id:id,type:chattype,from:from,to:too,url:location.protocol!="https:"&&self.isHttpDNS?self.apiUrl+msgBody.url.substr(msgBody.url.indexOf("/",9)):msgBody.url,secret:msgBody.secret,filename:msgBody.filename,file_length:msgBody.file_length,accessToken:this.context.accessToken||"",ext:extmsg,delay:parseMsgData.delayTimeStamp};!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onFileMessage(msg);break;case"loc":var msg={id:id,type:chattype,from:from,to:too,addr:msgBody.addr,lat:msgBody.lat,lng:msgBody.lng,ext:extmsg,delay:parseMsgData.delayTimeStamp};!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onLocationMessage(msg);break;case"video":var msg={id:id,type:chattype,from:from,to:too,url:location.protocol!="https:"&&self.isHttpDNS?self.apiUrl+msgBody.url.substr(msgBody.url.indexOf("/",9)):msgBody.url,secret:msgBody.secret,filename:msgBody.filename,file_length:msgBody.file_length,accessToken:this.context.accessToken||"",ext:extmsg,delay:parseMsgData.delayTimeStamp};!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onVideoMessage(msg);break;case"cmd":var msg={id:id,from:from,to:too,action:msgBody.action,ext:extmsg,delay:parseMsgData.delayTimeStamp};
!msg.delay&&delete msg.delay;msg.error=errorBool;msg.errorText=errorText;msg.errorCode=errorCode;this.onCmdMessage(msg);break}if(self.delivery){var msgId=self.getUniqueId();var bodyId=msg.id;var deliverMessage=new WebIM.message("delivery",msgId);deliverMessage.set({id:bodyId,to:msg.from});self.send(deliverMessage.body)}}catch(e){this.onError({type:_code.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:e})}}};connection.prototype.handleDeliveredMessage=function(message){var id=message.id;var body=message.getElementsByTagName("body");var mid=0;if(isNaN(body[0].innerHTML)){mid=body[1].innerHTML}else{mid=body[0].innerHTML}var msg={mid:mid};this.onDeliverdMessage(msg);this.sendReceiptsMessage({id:id})};connection.prototype.handleAckedMessage=function(message){var id=message.id;var body=message.getElementsByTagName("body");var mid=0;if(isNaN(body[0].innerHTML)){mid=body[1].innerHTML}else{mid=body[0].innerHTML}var msg={mid:mid};this.onReadMessage(msg);this.sendReceiptsMessage({id:id})};connection.prototype.handleReceivedMessage=function(message){try{var received=message.getElementsByTagName("received");var mid=received[0].getAttribute("mid");var body=message.getElementsByTagName("body");var id=body[0].innerHTML;var msg={mid:mid,id:id};this.onReceivedMessage(msg)}catch(e){this.onError({type:_code.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:e})}var rcv=message.getElementsByTagName("received"),id,mid;if(rcv.length>0){if(rcv[0].childNodes&&rcv[0].childNodes.length>0){id=rcv[0].childNodes[0].nodeValue}else{id=rcv[0].innerHTML||rcv[0].innerText}mid=rcv[0].getAttribute("mid")}if(_msgHash[id]){try{_msgHash[id].msg.success instanceof Function&&_msgHash[id].msg.success(id,mid)}catch(e){this.onError({type:_code.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:e})}delete _msgHash[id]}};connection.prototype.handleInviteMessage=function(message){var form=null;var invitemsg=message.getElementsByTagName("invite");var reasonDom=message.getElementsByTagName("reason")[0];var reasonMsg=reasonDom.textContent;var id=message.getAttribute("id")||"";this.sendReceiptsMessage({id:id});if(invitemsg&&invitemsg.length>0){var fromJid=invitemsg[0].getAttribute("from");form=_parseNameFromJidFn(fromJid)}var xmsg=message.getElementsByTagName("x");var roomid=null;if(xmsg&&xmsg.length>0){for(var i=0;i<xmsg.length;i++){if("jabber:x:conference"===xmsg[i].namespaceURI){var roomjid=xmsg[i].getAttribute("jid");roomid=_parseNameFromJidFn(roomjid)}}}this.onInviteMessage({type:"invite",from:form,roomid:roomid,reason:reasonMsg})};connection.prototype.handleMutedMessage=function(message){var id=message.id;this.onMutedMessage({mid:id})};connection.prototype.sendCommand=function(dom,id){if(this.isOpened()){this.context.stropheConn.send(dom)}else{this.onError({type:_code.WEBIM_CONNCTION_DISCONNECTED,reconnect:true})}};connection.prototype.getUniqueId=function(prefix){if(this.autoIncrement){this.autoIncrement++}else{this.autoIncrement=1}var cdate=new Date();var offdate=new Date(2010,1,1);var offset=cdate.getTime()-offdate.getTime();var hexd=parseFloat(offset).toString(16)+this.autoIncrement;if(typeof prefix==="string"||typeof prefix==="number"){return prefix+"_"+hexd}else{return"WEBIM_"+hexd}};connection.prototype.send=function(messageSource){var self=this;var message=messageSource;if(message.type==="txt"){if(this.encrypt.type==="base64"){message=_.clone(messageSource);message.msg=btoa(message.msg)}else{if(this.encrypt.type==="aes"){message=_.clone(messageSource);var key=CryptoJS.enc.Utf8.parse(this.encrypt.key);var iv=CryptoJS.enc.Utf8.parse(this.encrypt.iv);var mode=this.encrypt.mode.toLowerCase();var option={};if(mode==="cbc"){option={iv:iv,mode:CryptoJS.mode.CBC,padding:CryptoJS.pad.Pkcs7}}else{if(mode==="ebc"){option={mode:CryptoJS.mode.ECB,padding:CryptoJS.pad.Pkcs7}}}var encryptedData=CryptoJS.AES.encrypt(message.msg,key,option);message.msg=encryptedData.toString()}}}if(this.isWindowSDK){WebIM.doQuery('{"type":"sendMessage","to":"'+message.to+'","message_type":"'+message.type+'","msg":"'+encodeURI(message.msg)+'","chatType":"'+message.chatType+'"}',function(response){},function(code,msg){var message={data:{data:"send"},type:_code.WEBIM_MESSAGE_SED_ERROR};self.onError(message)})}else{if(Object.prototype.toString.call(message)==="[object Object]"){var appKey=this.context.appKey||"";var toJid=appKey+"_"+message.to+"@"+this.domain;if(message.group){toJid=appKey+"_"+message.to+"@conference."+this.domain}if(message.resource){toJid=toJid+"/"+message.resource}message.toJid=toJid;message.id=message.id||this.getUniqueId();_msgHash[message.id]=new _message(message);_msgHash[message.id].send(this)}else{if(typeof message==="string"){_msgHash[message]&&_msgHash[message].send(this)}}}};connection.prototype.addRoster=function(options){var jid=_getJid(options,this);var name=options.name||"";var groups=options.groups||"";var iq=$iq({type:"set"});iq.c("query",{xmlns:"jabber:iq:roster"});iq.c("item",{jid:jid,name:name});if(groups){for(var i=0;i<groups.length;i++){iq.c("group").t(groups[i]).up()
}}var suc=options.success||_utils.emptyfn;var error=options.error||_utils.emptyfn;this.context.stropheConn.sendIQ(iq.tree(),suc,error)};connection.prototype.removeRoster=function(options){var jid=_getJid(options,this);var iq=$iq({type:"set"}).c("query",{xmlns:"jabber:iq:roster"}).c("item",{jid:jid,subscription:"remove"});var suc=options.success||_utils.emptyfn;var error=options.error||_utils.emptyfn;this.context.stropheConn.sendIQ(iq,suc,error)};connection.prototype.getRoster=function(options){var conn=this;var dom=$iq({type:"get"}).c("query",{xmlns:"jabber:iq:roster"});var options=options||{};var suc=options.success||this.onRoster;var completeFn=function completeFn(ele){var rouster=[];var msgBodies=ele.getElementsByTagName("query");if(msgBodies&&msgBodies.length>0){var queryTag=msgBodies[0];rouster=_parseFriend(queryTag)}suc(rouster,ele)};var error=options.error||this.onError;var failFn=function failFn(ele){error({type:_code.WEBIM_CONNCTION_GETROSTER_ERROR,data:ele})};if(this.isOpened()){this.context.stropheConn.sendIQ(dom.tree(),completeFn,failFn)}else{error({type:_code.WEBIM_CONNCTION_DISCONNECTED})}};connection.prototype.subscribe=function(options){var jid=_getJid(options,this);var pres=$pres({to:jid,type:"subscribe"});if(options.message){pres.c("status").t(options.message).up()}if(options.nick){pres.c("nick",{"xmlns":"http://jabber.org/protocol/nick"}).t(options.nick)}this.sendCommand(pres.tree())};connection.prototype.subscribed=function(options){var jid=_getJid(options,this);var pres=$pres({to:jid,type:"subscribed"});if(options.message){pres.c("status").t(options.message).up()}this.sendCommand(pres.tree())};connection.prototype.unsubscribe=function(options){var jid=_getJid(options,this);var pres=$pres({to:jid,type:"unsubscribe"});if(options.message){pres.c("status").t(options.message)}this.sendCommand(pres.tree())};connection.prototype.unsubscribed=function(options){var jid=_getJid(options,this);var pres=$pres({to:jid,type:"unsubscribed"});if(options.message){pres.c("status").t(options.message).up()}this.sendCommand(pres.tree())};connection.prototype.joinPublicGroup=function(options){var roomJid=this.context.appKey+"_"+options.roomId+"@conference."+this.domain;var room_nick=roomJid+"/"+this.context.userId;var suc=options.success||_utils.emptyfn;var err=options.error||_utils.emptyfn;var errorFn=function errorFn(ele){err({type:_code.WEBIM_CONNCTION_JOINROOM_ERROR,data:ele})};var iq=$pres({from:this.context.jid,to:room_nick}).c("x",{xmlns:Strophe.NS.MUC});this.context.stropheConn.sendIQ(iq.tree(),suc,errorFn)};connection.prototype.listRooms=function(options){var iq=$iq({to:options.server||"conference."+this.domain,from:this.context.jid,type:"get"}).c("query",{xmlns:Strophe.NS.DISCO_ITEMS});var suc=options.success||_utils.emptyfn;var error=options.error||this.onError;var completeFn=function completeFn(result){var rooms=[];rooms=_parseRoom(result);try{suc(rooms)}catch(e){error({type:_code.WEBIM_CONNCTION_GETROOM_ERROR,data:e})}};var err=options.error||_utils.emptyfn;var errorFn=function errorFn(ele){err({type:_code.WEBIM_CONNCTION_GETROOM_ERROR,data:ele})};this.context.stropheConn.sendIQ(iq.tree(),completeFn,errorFn)};connection.prototype.queryRoomMember=function(options){var domain=this.domain;var members=[];var iq=$iq({to:this.context.appKey+"_"+options.roomId+"@conference."+this.domain,type:"get"}).c("query",{xmlns:Strophe.NS.MUC+"#admin"}).c("item",{affiliation:"member"});var suc=options.success||_utils.emptyfn;var completeFn=function completeFn(result){var items=result.getElementsByTagName("item");if(items){for(var i=0;i<items.length;i++){var item=items[i];var mem={jid:item.getAttribute("jid"),affiliation:"member"};members.push(mem)}}suc(members)};var err=options.error||_utils.emptyfn;var errorFn=function errorFn(ele){err({type:_code.WEBIM_CONNCTION_GETROOMMEMBER_ERROR,data:ele})};this.context.stropheConn.sendIQ(iq.tree(),completeFn,errorFn)};connection.prototype.queryRoomInfo=function(options){var domain=this.domain;var iq=$iq({to:this.context.appKey+"_"+options.roomId+"@conference."+domain,type:"get"}).c("query",{xmlns:Strophe.NS.DISCO_INFO});var suc=options.success||_utils.emptyfn;var members=[];var completeFn=function completeFn(result){var settings="";var features=result.getElementsByTagName("feature");if(features){settings=features[1].getAttribute("var")+"|"+features[3].getAttribute("var")+"|"+features[4].getAttribute("var")}switch(settings){case"muc_public|muc_membersonly|muc_notallowinvites":settings="PUBLIC_JOIN_APPROVAL";break;case"muc_public|muc_open|muc_notallowinvites":settings="PUBLIC_JOIN_OPEN";break;case"muc_hidden|muc_membersonly|muc_allowinvites":settings="PRIVATE_MEMBER_INVITE";break;case"muc_hidden|muc_membersonly|muc_notallowinvites":settings="PRIVATE_OWNER_INVITE";break}var owner="";var fields=result.getElementsByTagName("field");var fieldValues={};if(fields){for(var i=0;i<fields.length;i++){var field=fields[i];var fieldVar=field.getAttribute("var");var fieldSimplify=fieldVar.split("_")[1];
switch(fieldVar){case"muc#roominfo_occupants":case"muc#roominfo_maxusers":case"muc#roominfo_affiliations":case"muc#roominfo_description":fieldValues[fieldSimplify]=field.textContent||field.text||"";break;case"muc#roominfo_owner":var mem={jid:(field.textContent||field.text)+"@"+domain,affiliation:"owner"};members.push(mem);fieldValues[fieldSimplify]=field.textContent||field.text;break}}fieldValues["name"]=result.getElementsByTagName("identity")[0].getAttribute("name")}suc(settings,members,fieldValues)};var err=options.error||_utils.emptyfn;var errorFn=function errorFn(ele){err({type:_code.WEBIM_CONNCTION_GETROOMINFO_ERROR,data:ele})};this.context.stropheConn.sendIQ(iq.tree(),completeFn,errorFn)};connection.prototype.queryRoomOccupants=function(options){var suc=options.success||_utils.emptyfn;var completeFn=function completeFn(result){var occupants=[];occupants=_parseRoomOccupants(result);suc(occupants)};var err=options.error||_utils.emptyfn;var errorFn=function errorFn(ele){err({type:_code.WEBIM_CONNCTION_GETROOMOCCUPANTS_ERROR,data:ele})};var attrs={xmlns:Strophe.NS.DISCO_ITEMS};var info=$iq({from:this.context.jid,to:this.context.appKey+"_"+options.roomId+"@conference."+this.domain,type:"get"}).c("query",attrs);this.context.stropheConn.sendIQ(info.tree(),completeFn,errorFn)};connection.prototype.setUserSig=function(desc){var dom=$pres({xmlns:"jabber:client"});desc=desc||"";dom.c("status").t(desc);this.sendCommand(dom.tree())};connection.prototype.setPresence=function(type,status){var dom=$pres({xmlns:"jabber:client"});if(type){if(status){dom.c("show").t(type);dom.up().c("status").t(status)}else{dom.c("show").t(type)}}this.sendCommand(dom.tree())};connection.prototype.getPresence=function(){var dom=$pres({xmlns:"jabber:client"});var conn=this;this.sendCommand(dom.tree())};connection.prototype.ping=function(options){var options=options||{};var jid=_getJid(options,this);var dom=$iq({from:this.context.jid||"",to:jid,type:"get"}).c("ping",{xmlns:"urn:xmpp:ping"});var suc=options.success||_utils.emptyfn;var error=options.error||this.onError;var failFn=function failFn(ele){error({type:_code.WEBIM_CONNCTION_PING_ERROR,data:ele})};if(this.isOpened()){this.context.stropheConn.sendIQ(dom.tree(),suc,failFn)}else{error({type:_code.WEBIM_CONNCTION_DISCONNECTED})}return};connection.prototype.isOpened=function(){return this.context.status==_code.STATUS_OPENED};connection.prototype.isOpening=function(){var status=this.context.status;return status==_code.STATUS_DOLOGIN_USERGRID||status==_code.STATUS_DOLOGIN_IM};connection.prototype.isClosing=function(){return this.context.status==_code.STATUS_CLOSING};connection.prototype.isClosed=function(){return this.context.status==_code.STATUS_CLOSED};connection.prototype.clear=function(){var key=this.context.appKey;if(this.errorType!=_code.WEBIM_CONNCTION_DISCONNECTED){this.context={status:_code.STATUS_INIT,appKey:key}}if(this.intervalId){clearInterval(this.intervalId)}this.restIndex=0;this.xmppIndex=0;if(this.errorType==_code.WEBIM_CONNCTION_CLIENT_LOGOUT||this.errorType==-1){var message={data:{data:"logout"},type:_code.WEBIM_CONNCTION_CLIENT_LOGOUT};this.onError(message)}};connection.prototype.getChatRooms=function(options){if(!_utils.isCanSetRequestHeader){conn.onError({type:_code.WEBIM_CONNCTION_NOT_SUPPORT_CHATROOM_ERROR});return}var conn=this,token=options.accessToken||this.context.accessToken;if(token){var apiUrl=options.apiUrl;var appName=this.context.appName;var orgName=this.context.orgName;if(!appName||!orgName){conn.onError({type:_code.WEBIM_CONNCTION_AUTH_ERROR});return}var suc=function suc(data,xhr){typeof options.success==="function"&&options.success(data)};var error=function error(res,xhr,msg){if(res.error&&res.error_description){conn.onError({type:_code.WEBIM_CONNCTION_LOAD_CHATROOM_ERROR,msg:res.error_description,data:res,xhr:xhr})}};var pageInfo={pagenum:parseInt(options.pagenum)||1,pagesize:parseInt(options.pagesize)||20};var opts={url:apiUrl+"/"+orgName+"/"+appName+"/chatrooms",dataType:"json",type:"GET",headers:{"Authorization":"Bearer "+token},data:pageInfo,success:suc||_utils.emptyfn,error:error||_utils.emptyfn};_utils.ajax(opts)}else{conn.onError({type:_code.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR})}};connection.prototype.joinChatRoom=function(options){var roomJid=this.context.appKey+"_"+options.roomId+"@conference."+this.domain;var room_nick=roomJid+"/"+this.context.userId;var suc=options.success||_utils.emptyfn;var err=options.error||_utils.emptyfn;var errorFn=function errorFn(ele){err({type:_code.WEBIM_CONNCTION_JOINCHATROOM_ERROR,data:ele})};var iq=$pres({from:this.context.jid,to:room_nick}).c("x",{xmlns:Strophe.NS.MUC+"#user"}).c("item",{affiliation:"member",role:"participant"}).up().up().c("roomtype",{xmlns:"easemob:x:roomtype",type:"chatroom"});this.context.stropheConn.sendIQ(iq.tree(),suc,errorFn)};connection.prototype.quitChatRoom=function(options){var roomJid=this.context.appKey+"_"+options.roomId+"@conference."+this.domain;var room_nick=roomJid+"/"+this.context.userId;
var suc=options.success||_utils.emptyfn;var err=options.error||_utils.emptyfn;var errorFn=function errorFn(ele){err({type:_code.WEBIM_CONNCTION_QUITCHATROOM_ERROR,data:ele})};var iq=$pres({from:this.context.jid,to:room_nick,type:"unavailable"}).c("x",{xmlns:Strophe.NS.MUC+"#user"}).c("item",{affiliation:"none",role:"none"}).up().up().c("roomtype",{xmlns:"easemob:x:roomtype",type:"chatroom"});this.context.stropheConn.sendIQ(iq.tree(),suc,errorFn)};connection.prototype._onReceiveInviteFromGroup=function(info){info=eval("("+info+")");var self=this;var options={title:"Group invitation",msg:info.user+" invites you to join into group:"+info.group_id,agree:function agree(){WebIM.doQuery('{"type":"acceptInvitationFromGroup","id":"'+info.group_id+'","user":"'+info.user+'"}',function(response){},function(code,msg){var message={data:{data:"acceptInvitationFromGroup error:"+msg},type:_code.WEBIM_CONNECTION_ACCEPT_INVITATION_FROM_GROUP};self.onError(message)})},reject:function reject(){WebIM.doQuery('{"type":"declineInvitationFromGroup","id":"'+info.group_id+'","user":"'+info.user+'"}',function(response){},function(code,msg){var message={data:{data:"declineInvitationFromGroup error:"+msg},type:_code.WEBIM_CONNECTION_DECLINE_INVITATION_FROM_GROUP};self.onError(message)})}};this.onConfirmPop(options)};connection.prototype._onReceiveInviteAcceptionFromGroup=function(info){info=eval("("+info+")");var options={title:"Group invitation response",msg:info.user+" agreed to join into group:"+info.group_id,agree:function agree(){}};this.onConfirmPop(options)};connection.prototype._onReceiveInviteDeclineFromGroup=function(info){info=eval("("+info+")");var options={title:"Group invitation response",msg:info.user+" rejected to join into group:"+info.group_id,agree:function agree(){}};this.onConfirmPop(options)};connection.prototype._onAutoAcceptInvitationFromGroup=function(info){info=eval("("+info+")");var options={title:"Group invitation",msg:"You had joined into the group:"+info.group_name+" automatically.Inviter:"+info.user,agree:function agree(){}};this.onConfirmPop(options)};connection.prototype._onLeaveGroup=function(info){info=eval("("+info+")");var options={title:"Group notification",msg:"You have been out of the group:"+info.group_id+".Reason:"+info.msg,agree:function agree(){}};this.onConfirmPop(options)};connection.prototype._onReceiveJoinGroupApplication=function(info){info=eval("("+info+")");var self=this;var options={title:"Group join application",msg:info.user+" applys to join into group:"+info.group_id,agree:function agree(){WebIM.doQuery('{"type":"acceptJoinGroupApplication","id":"'+info.group_id+'","user":"'+info.user+'"}',function(response){},function(code,msg){var message={data:{data:"acceptJoinGroupApplication error:"+msg},type:_code.WEBIM_CONNECTION_ACCEPT_JOIN_GROUP};self.onError(message)})},reject:function reject(){WebIM.doQuery('{"type":"declineJoinGroupApplication","id":"'+info.group_id+'","user":"'+info.user+'"}',function(response){},function(code,msg){var message={data:{data:"declineJoinGroupApplication error:"+msg},type:_code.WEBIM_CONNECTION_DECLINE_JOIN_GROUP};self.onError(message)})}};this.onConfirmPop(options)};connection.prototype._onReceiveAcceptionFromGroup=function(info){info=eval("("+info+")");var options={title:"Group notification",msg:"You had joined into the group:"+info.group_name+".",agree:function agree(){}};this.onConfirmPop(options)};connection.prototype._onReceiveRejectionFromGroup=function(){info=eval("("+info+")");var options={title:"Group notification",msg:"You have been rejected to join into the group:"+info.group_name+".",agree:function agree(){}};this.onConfirmPop(options)};connection.prototype._onUpdateMyGroupList=function(options){this.onUpdateMyGroupList(options)};connection.prototype._onUpdateMyRoster=function(options){this.onUpdateMyRoster(options)};connection.prototype.reconnect=function(){var that=this;setTimeout(function(){_login(that.context.restTokenData,that)},(this.autoReconnectNumTotal==0?0:this.autoReconnectInterval)*1000);this.autoReconnectNumTotal++};connection.prototype.closed=function(){var message={data:{data:"Closed error"},type:_code.WEBIM_CONNECTION_CLOSED};this.onError(message)};function _parsePrivacy(iq){var list=[];var items=iq.getElementsByTagName("item");if(items){for(var i=0;i<items.length;i++){var item=items[i];var jid=item.getAttribute("value");var order=item.getAttribute("order");var type=item.getAttribute("type");if(!jid){continue}var n=_parseNameFromJidFn(jid);list[n]={type:type,order:order,jid:jid,name:n}}}return list}connection.prototype.getBlacklist=function(options){options=options||{};var iq=$iq({type:"get"});var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var me=this;iq.c("query",{xmlns:"jabber:iq:privacy"}).c("list",{name:"special"});this.context.stropheConn.sendIQ(iq.tree(),function(iq){me.onBlacklistUpdate(_parsePrivacy(iq));sucFn()},function(){me.onBlacklistUpdate([]);errFn()})};connection.prototype.addToBlackList=function(options){var iq=$iq({type:"set"});
var blacklist=options.list||{};var type=options.type||"jid";var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var piece=iq.c("query",{xmlns:"jabber:iq:privacy"}).c("list",{name:"special"});var keys=Object.keys(blacklist);var len=keys.length;var order=2;for(var i=0;i<len;i++){var item=blacklist[keys[i]];var type=item.type||"jid";var jid=item.jid;piece=piece.c("item",{action:"deny",order:order++,type:type,value:jid}).c("message");if(i!==len-1){piece=piece.up().up()}}this.context.stropheConn.sendIQ(piece.tree(),sucFn,errFn)};connection.prototype.removeFromBlackList=function(options){var iq=$iq({type:"set"});var blacklist=options.list||{};var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var piece=iq.c("query",{xmlns:"jabber:iq:privacy"}).c("list",{name:"special"});var keys=Object.keys(blacklist);var len=keys.length;for(var i=0;i<len;i++){var item=blacklist[keys[i]];var type=item.type||"jid";var jid=item.jid;var order=item.order;piece=piece.c("item",{action:"deny",order:order,type:type,value:jid}).c("message");if(i!==len-1){piece=piece.up().up()}}this.context.stropheConn.sendIQ(piece.tree(),sucFn,errFn)};connection.prototype._getGroupJid=function(to){var appKey=this.context.appKey||"";return appKey+"_"+to+"@conference."+this.domain};connection.prototype.addToGroupBlackList=function(options){var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var jid=_getJid(options,this);var affiliation="admin";var to=this._getGroupJid(options.roomId);var iq=$iq({type:"set",to:to});iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation}).c("item",{affiliation:"outcast",jid:jid});this.context.stropheConn.sendIQ(iq.tree(),sucFn,errFn)};function _parseGroupBlacklist(iq){var list={};var items=iq.getElementsByTagName("item");if(items){for(var i=0;i<items.length;i++){var item=items[i];var jid=item.getAttribute("jid");var affiliation=item.getAttribute("affiliation");var nick=item.getAttribute("nick");if(!jid){continue}var n=_parseNameFromJidFn(jid);list[n]={jid:jid,affiliation:affiliation,nick:nick,name:n}}}return list}connection.prototype.getGroupBlacklist=function(options){var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var affiliation="admin";var to=this._getGroupJid(options.roomId);var iq=$iq({type:"get",to:to});iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation}).c("item",{affiliation:"outcast"});this.context.stropheConn.sendIQ(iq.tree(),function(msginfo){sucFn(_parseGroupBlacklist(msginfo))},function(){errFn()})};connection.prototype.removeGroupMemberFromBlacklist=function(options){var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var jid=_getJid(options,this);var affiliation="admin";var to=this._getGroupJid(options.roomId);var iq=$iq({type:"set",to:to});iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation}).c("item",{affiliation:"none",jid:jid});this.context.stropheConn.sendIQ(iq.tree(),function(msginfo){sucFn()},function(){errFn()})};connection.prototype.changeGroupSubject=function(options){var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var affiliation="owner";var to=this._getGroupJid(options.roomId);var iq=$iq({type:"set",to:to});iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation}).c("x",{type:"submit",xmlns:"jabber:x:data"}).c("field",{"var":"FORM_TYPE"}).c("value").t("http://jabber.org/protocol/muc#roomconfig").up().up().c("field",{"var":"muc#roomconfig_roomname"}).c("value").t(options.subject).up().up().c("field",{"var":"muc#roomconfig_roomdesc"}).c("value").t(options.description);this.context.stropheConn.sendIQ(iq.tree(),function(msginfo){sucFn()},function(){errFn()})};connection.prototype.destroyGroup=function(options){var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var affiliation="owner";var to=this._getGroupJid(options.roomId);var iq=$iq({type:"set",to:to});iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation}).c("destroy").c("reason").t(options.reason||"");this.context.stropheConn.sendIQ(iq.tree(),function(msginfo){sucFn()},function(){errFn()})};connection.prototype.leaveGroupBySelf=function(options){var self=this;var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var jid=_getJid(options,this);var affiliation="admin";var to=this._getGroupJid(options.roomId);var iq=$iq({type:"set",to:to});iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation}).c("item",{affiliation:"none",jid:jid});this.context.stropheConn.sendIQ(iq.tree(),function(msgInfo){sucFn(msgInfo);var pres=$pres({type:"unavailable",to:to+"/"+self.context.userId});self.sendCommand(pres.tree())},function(errInfo){errFn(errInfo)})};connection.prototype.leaveGroup=function(options){var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var list=options.list||[];var affiliation="admin";var to=this._getGroupJid(options.roomId);
var iq=$iq({type:"set",to:to});var piece=iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation});var keys=Object.keys(list);var len=keys.length;for(var i=0;i<len;i++){var name=list[keys[i]];var jid=_getJidByName(name,this);piece=piece.c("item",{affiliation:"none",jid:jid}).up().c("item",{role:"none",jid:jid}).up()}this.context.stropheConn.sendIQ(iq.tree(),function(msgInfo){sucFn(msgInfo)},function(errInfo){errFn(errInfo)})};connection.prototype.addGroupMembers=function(options){var sucFn=options.success||_utils.emptyfn;var errFn=options.error||_utils.emptyfn;var list=options.list||[];var affiliation="admin";var to=this._getGroupJid(options.roomId);var iq=$iq({type:"set",to:to});var piece=iq.c("query",{xmlns:"http://jabber.org/protocol/muc#"+affiliation});var len=list.length;for(var i=0;i<len;i++){var name=list[i];var jid=_getJidByName(name,this);piece=piece.c("item",{affiliation:"member",jid:jid}).up();var dom=$msg({to:to}).c("x",{xmlns:"http://jabber.org/protocol/muc#user"}).c("invite",{to:jid}).c("reason").t(options.reason||"");this.sendCommand(dom.tree())}this.context.stropheConn.sendIQ(iq.tree(),function(msgInfo){sucFn(msgInfo)},function(errInfo){errFn(errInfo)})};connection.prototype.acceptInviteFromGroup=function(options){options.success=function(){};this.addGroupMembers(options)};connection.prototype.rejectInviteFromGroup=function(options){};connection.prototype.createGroupAsync=function(p){var roomId=p.from;var me=this;var toRoom=this._getGroupJid(roomId);var to=toRoom+"/"+this.context.userId;var options=this.groupOption;var suc=p.success||_utils.emptyfn;var iq=$iq({type:"get",to:toRoom}).c("query",{xmlns:"http://jabber.org/protocol/muc#owner"});me.context.stropheConn.sendIQ(iq.tree(),function(msgInfo){if("setAttribute" in msgInfo){var x=msgInfo.getElementsByTagName("x")[0];x.setAttribute("type","submit")}else{Strophe.forEachChild(msgInfo,"x",function(field){field.setAttribute("type","submit")})}Strophe.info("step 5 ----------");Strophe.forEachChild(x,"field",function(field){var fieldVar=field.getAttribute("var");var valueDom=field.getElementsByTagName("value")[0];Strophe.info(fieldVar);switch(fieldVar){case"muc#roomconfig_maxusers":_setText(valueDom,options.optionsMaxUsers||200);break;case"muc#roomconfig_roomname":_setText(valueDom,options.subject||"");break;case"muc#roomconfig_roomdesc":_setText(valueDom,options.description||"");break;case"muc#roomconfig_publicroom":_setText(valueDom,+options.optionsPublic);break;case"muc#roomconfig_membersonly":_setText(valueDom,+options.optionsMembersOnly);break;case"muc#roomconfig_moderatedroom":_setText(valueDom,+options.optionsModerate);break;case"muc#roomconfig_persistentroom":_setText(valueDom,1);break;case"muc#roomconfig_allowinvites":_setText(valueDom,+options.optionsAllowInvites);break;case"muc#roomconfig_allowvisitornickchange":_setText(valueDom,0);break;case"muc#roomconfig_allowvisitorstatus":_setText(valueDom,0);break;case"allow_private_messages":_setText(valueDom,0);break;case"allow_private_messages_from_visitors":_setText(valueDom,"nobody");break;default:break}});var iq=$iq({to:toRoom,type:"set"}).c("query",{xmlns:"http://jabber.org/protocol/muc#owner"}).cnode(x);me.context.stropheConn.sendIQ(iq.tree(),function(msgInfo){me.addGroupMembers({list:options.members,roomId:roomId});suc(options)},function(errInfo){})},function(errInfo){})};connection.prototype.createGroup=function(options){this.groupOption=options;var roomId=+new Date();var toRoom=this._getGroupJid(roomId);var to=toRoom+"/"+this.context.userId;var pres=$pres({to:to}).c("x",{xmlns:"http://jabber.org/protocol/muc"}).up().c("create",{xmlns:"http://jabber.org/protocol/muc"}).up();this.sendCommand(pres.tree())};connection.prototype.createGroupNew=function(opt){opt.data.owner=this.user;var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/chatgroups",dataType:"json",type:"POST",data:JSON.stringify(opt.data),headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=function(respData){opt.success(respData);this.onCreateGroup(respData)}.bind(this);options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.blockGroup=function(opt){var groupId=opt.groupId;groupId="notification_ignore_"+groupId;var data={entities:[]};data.entities[0]={};data.entities[0][groupId]=true;var options={type:"PUT",url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"users"+"/"+this.user,data:JSON.stringify(data),headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.joinGroup=function(opt){var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+opt.groupId+"/"+"apply",type:"POST",dataType:"json",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;
WebIM.utils.ajax(options)};connection.prototype.listGroups=function(opt){var requestData=[];requestData["limit"]=opt.limit;requestData["cursor"]=opt.cursor;if(!requestData["cursor"]){delete requestData["cursor"]}if(isNaN(opt.limit)){throw'The parameter "limit" should be a number';return}var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/publicchatgroups",type:"GET",dataType:"json",data:requestData,headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.getGroupInfo=function(opt){var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/chatgroups/"+opt.groupId,type:"GET",dataType:"json",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.getGroup=function(opt){var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"users"+"/"+this.user+"/"+"joined_chatgroups",dataType:"json",type:"GET",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.listGroupMember=function(opt){if(isNaN(opt.pageNum)||opt.pageNum<=0){throw'The parameter "pageNum" should be a positive number';return}else{if(isNaN(opt.pageSize)||opt.pageSize<=0){throw'The parameter "pageSize" should be a positive number';return}else{if(opt.groupId===null&&typeof opt.groupId==="undefined"){throw'The parameter "groupId" should be added';return}}}var requestData=[],groupId=opt.groupId;requestData["pagenum"]=opt.pageNum;requestData["pagesize"]=opt.pageSize;var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/chatgroups"+"/"+groupId+"/users",dataType:"json",type:"GET",data:requestData,headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.mute=function(opt){var groupId=opt.groupId,requestData={"usernames":[opt.username],"mute_duration":opt.muteDuration},options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"mute",dataType:"json",type:"POST",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"},data:JSON.stringify(requestData)};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.removeMute=function(opt){var groupId=opt.groupId,username=opt.username;var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"mute"+"/"+username,dataType:"json",type:"DELETE",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.getGroupAdmin=function(opt){var groupId=opt.groupId;var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/chatgroups"+"/"+groupId+"/admin",dataType:"json",type:"GET",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.getMuted=function(opt){var groupId=opt.groupId;var options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/chatgroups"+"/"+groupId+"/mute",dataType:"json",type:"GET",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.setAdmin=function(opt){var groupId=opt.groupId,requestData={newadmin:opt.username},options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"admin",type:"POST",dataType:"json",data:JSON.stringify(requestData),headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.removeAdmin=function(opt){var groupId=opt.groupId,username=opt.username,options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"admin"+"/"+username,type:"DELETE",dataType:"json",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.agreeJoinGroup=function(opt){var groupId=opt.groupId,requestData={"applicant":opt.applicant,"verifyResult":true,"reason":"no clue"},options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"apply_verify",type:"POST",dataType:"json",data:JSON.stringify(requestData),headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};
options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.rejectJoinGroup=function(opt){var groupId=opt.groupId,requestData={"applicant":opt.applicant,"verifyResult":false,"reason":"no clue"},options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"apply_verify",type:"POST",dataType:"json",data:JSON.stringify(requestData),headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.groupBlockSingle=function(opt){var groupId=opt.groupId,username=opt.username,options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"blocks"+"/"+"users"+"/"+username,type:"POST",dataType:"json",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.groupBlockMulti=function(opt){var groupId=opt.groupId,usernames=opt.usernames,requestData={usernames:usernames},options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"blocks"+"/"+"users",data:JSON.stringify(requestData),type:"POST",dataType:"json",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.removeGroupBlockSingle=function(opt){var groupId=opt.groupId,username=opt.username,options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"blocks"+"/"+"users"+"/"+username,type:"DELETE",dataType:"json",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};connection.prototype.removeGroupBlockMulti=function(opt){var groupId=opt.groupId,username=opt.username.join(","),options={url:this.apiUrl+"/"+this.orgName+"/"+this.appName+"/"+"chatgroups"+"/"+groupId+"/"+"blocks"+"/"+"users"+"/"+username,type:"DELETE",dataType:"json",headers:{"Authorization":"Bearer "+this.token,"Content-Type":"application/json"}};options.success=opt.success||_utils.emptyfn;options.error=opt.error||_utils.emptyfn;WebIM.utils.ajax(options)};function _setText(valueDom,v){if("textContent" in valueDom){valueDom.textContent=v}else{if("text" in valueDom){valueDom.text=v}else{}}}var WebIM=window.WebIM||{};WebIM.connection=connection;WebIM.utils=_utils;WebIM.statusCode=_code;WebIM.message=_msg.message;WebIM.doQuery=function(str,suc,fail){if(typeof window.cefQuery==="undefined"){return}window.cefQuery({request:str,persistent:false,onSuccess:suc,onFailure:fail})};function logMessage(message){WebIM&&WebIM.config.isDebug&&console.log(WebIM.utils.ts()+"[recv] ",message.data)}if(WebIM&&WebIM.config.isDebug){Strophe.Connection.prototype.rawOutput=function(data){console.log("%c "+WebIM.utils.ts()+"[send] "+data,"background-color: #e2f7da")}}if(WebIM&&WebIM.config&&WebIM.config.isSandBox){WebIM.config.xmppURL=WebIM.config.xmppURL.replace(".easemob.","-sandbox.easemob.");WebIM.config.apiURL=WebIM.config.apiURL.replace(".easemob.","-sdb.easemob.")}module.exports=WebIM;if(false){module.hot.accept()}},248:function(module,exports,__webpack_require__){var CryptoJS=__webpack_require__(210);(function(){var _utils=__webpack_require__(205).utils;var Message=function Message(type,id){if(!this instanceof Message){return new Message(type)}this._msg={};if(typeof Message[type]==="function"){Message[type].prototype.setGroup=this.setGroup;this._msg=new Message[type](id)}return this._msg};Message.prototype.setGroup=function(group){this.body.group=group};Message.read=function(id){this.id=id;this.type="read"};Message.read.prototype.set=function(opt){this.body={ackId:opt.id,to:opt.to}};Message.delivery=function(id){this.id=id;this.type="delivery"};Message.delivery.prototype.set=function(opt){this.body={bodyId:opt.id,to:opt.to}};Message.txt=function(id){this.id=id;this.type="txt";this.body={}};Message.txt.prototype.set=function(opt){this.value=opt.msg;this.body={id:this.id,to:opt.to,msg:this.value,type:this.type,roomType:opt.roomType,ext:opt.ext||{},success:opt.success,fail:opt.fail};!opt.roomType&&delete this.body.roomType};Message.cmd=function(id){this.id=id;this.type="cmd";this.body={}};Message.cmd.prototype.set=function(opt){this.value="";this.body={to:opt.to,action:opt.action,msg:this.value,type:this.type,roomType:opt.roomType,ext:opt.ext||{},success:opt.success};!opt.roomType&&delete this.body.roomType};Message.location=function(id){this.id=id;this.type="loc";this.body={}};Message.location.prototype.set=function(opt){this.body={to:opt.to,type:this.type,roomType:opt.roomType,addr:opt.addr,lat:opt.lat,lng:opt.lng,ext:opt.ext||{}}};Message.img=function(id){this.id=id;
this.type="img";this.body={}};Message.img.prototype.set=function(opt){opt.file=opt.file||_utils.getFileUrl(opt.fileInputId);this.value=opt.file;this.body={id:this.id,file:this.value,apiUrl:opt.apiUrl,to:opt.to,type:this.type,ext:opt.ext||{},roomType:opt.roomType,onFileUploadError:opt.onFileUploadError,onFileUploadComplete:opt.onFileUploadComplete,success:opt.success,fail:opt.fail,flashUpload:opt.flashUpload,width:opt.width,height:opt.height,body:opt.body,uploadError:opt.uploadError,uploadComplete:opt.uploadComplete};!opt.roomType&&delete this.body.roomType};Message.audio=function(id){this.id=id;this.type="audio";this.body={}};Message.audio.prototype.set=function(opt){opt.file=opt.file||_utils.getFileUrl(opt.fileInputId);this.value=opt.file;this.filename=opt.filename||this.value.filename;this.body={id:this.id,file:this.value,filename:this.filename,apiUrl:opt.apiUrl,to:opt.to,type:this.type,ext:opt.ext||{},length:opt.length||0,roomType:opt.roomType,file_length:opt.file_length,onFileUploadError:opt.onFileUploadError,onFileUploadComplete:opt.onFileUploadComplete,success:opt.success,fail:opt.fail,flashUpload:opt.flashUpload,body:opt.body};!opt.roomType&&delete this.body.roomType};Message.file=function(id){this.id=id;this.type="file";this.body={}};Message.file.prototype.set=function(opt){opt.file=opt.file||_utils.getFileUrl(opt.fileInputId);this.value=opt.file;this.filename=opt.filename||this.value.filename;this.body={id:this.id,file:this.value,filename:this.filename,apiUrl:opt.apiUrl,to:opt.to,type:this.type,ext:opt.ext||{},roomType:opt.roomType,onFileUploadError:opt.onFileUploadError,onFileUploadComplete:opt.onFileUploadComplete,success:opt.success,fail:opt.fail,flashUpload:opt.flashUpload,body:opt.body};!opt.roomType&&delete this.body.roomType};Message.video=function(id){};Message.video.prototype.set=function(opt){};var _Message=function _Message(message){if(!this instanceof _Message){return new _Message(message,conn)}this.msg=message};_Message.prototype.send=function(conn){var me=this;var _send=function _send(message){message.ext=message.ext||{};message.ext.weichat=message.ext.weichat||{};message.ext.weichat.originType=message.ext.weichat.originType||"webim";var dom;var json={from:conn.context.userId||"",to:message.to,bodies:[message.body],ext:message.ext||{}};var jsonstr=_utils.stringify(json);dom=$msg({type:message.group||"chat",to:message.toJid,id:message.id,xmlns:"jabber:client"}).c("body").t(jsonstr);if(message.roomType){dom.up().c("roomtype",{xmlns:"easemob:x:roomtype",type:"chatroom"})}if(message.bodyId){dom=$msg({to:message.toJid,id:message.id,xmlns:"jabber:client"}).c("body").t(message.bodyId);var delivery={xmlns:"urn:xmpp:receipts",id:message.bodyId};dom.up().c("delivery",delivery)}if(message.ackId){dom=$msg({to:message.toJid,id:message.id,xmlns:"jabber:client"}).c("body").t(message.ackId);var read={xmlns:"urn:xmpp:receipts",id:message.ackId};dom.up().c("acked",read)}setTimeout(function(){if(typeof _msgHash!=="undefined"&&_msgHash[message.id]){_msgHash[message.id].msg.fail instanceof Function&&_msgHash[message.id].msg.fail(message.id)}},60000);conn.sendCommand(dom.tree(),message.id)};if(me.msg.file){if(me.msg.body&&me.msg.body.url){_send(me.msg);return}var _tmpComplete=me.msg.onFileUploadComplete;var _complete=function _complete(data){if(data.entities[0]["file-metadata"]){var file_len=data.entities[0]["file-metadata"]["content-length"];me.msg.filetype=data.entities[0]["file-metadata"]["content-type"];if(file_len>204800){me.msg.thumbnail=true}}me.msg.body={type:me.msg.type||"file",url:(location.protocol!="https:"&&conn.isHttpDNS?conn.apiUrl+data.uri.substr(data.uri.indexOf("/",9)):data.uri)+"/"+data.entities[0]["uuid"],secret:data.entities[0]["share-secret"],filename:me.msg.file.filename||me.msg.filename,size:{width:me.msg.width||0,height:me.msg.height||0},length:me.msg.length||0,file_length:me.msg.ext.file_length||0,filetype:me.msg.filetype};_send(me.msg);_tmpComplete instanceof Function&&_tmpComplete(data,me.msg.id)};me.msg.onFileUploadComplete=_complete;_utils.uploadFile.call(conn,me.msg)}else{me.msg.body={type:me.msg.type==="chat"?"txt":me.msg.type,msg:me.msg.msg};if(me.msg.type==="cmd"){me.msg.body.action=me.msg.action}else{if(me.msg.type==="loc"){me.msg.body.addr=me.msg.addr;me.msg.body.lat=me.msg.lat;me.msg.body.lng=me.msg.lng}}_send(me.msg)}};exports._msg=_Message;exports.message=Message})()},249:function(module,exports){(function(){function Array_h(length){this.array=length===undefined?[]:new Array(length)}Array_h.prototype={length:function length(){return this.array.length},at:function at(index){return this.array[index]},set:function set(index,obj){this.array[index]=obj},push:function push(obj){return this.array.push(obj)},slice:function slice(start,end){return this.array=this.array.slice(start,end)},concat:function concat(array){this.array=this.array.concat(array)},remove:function remove(index,count){count=count===undefined?1:count;this.array.splice(index,count)},join:function join(separator){return this.array.join(separator)
},clear:function clear(){this.array.length=0}};var Queue=function Queue(){this._array_h=new Array_h()};Queue.prototype={_index:0,push:function push(obj){this._array_h.push(obj)},pop:function pop(){var ret=null;if(this._array_h.length()){ret=this._array_h.at(this._index);if(++this._index*2>=this._array_h.length()){this._array_h.slice(this._index);this._index=0}}return ret},head:function head(){var ret=null,len=this._array_h.length();if(len){ret=this._array_h.at(len-1)}return ret},tail:function tail(){var ret=null,len=this._array_h.length();if(len){ret=this._array_h.at(this._index)}return ret},length:function length(){return this._array_h.length()-this._index},empty:function empty(){return this._array_h.length()===0},clear:function clear(){this._array_h.clear()}};exports.Queue=Queue})()}});