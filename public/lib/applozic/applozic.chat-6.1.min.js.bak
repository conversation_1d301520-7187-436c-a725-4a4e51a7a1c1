(function(){var e,t,r,o,n={}.hasOwnProperty,s=[].slice;e={LF:"\n",NULL:"\0"},r=function(){var t;function r(e,t,r){this.command=e,this.headers=null!=t?t:{},this.body=null!=r?r:""}return r.prototype.toString=function(){var t,o,s,a,i;for(o in t=[this.command],(s=!1===this.headers["content-length"])&&delete this.headers["content-length"],i=this.headers)n.call(i,o)&&(a=i[o],t.push(o+":"+a));return this.body&&!s&&t.push("content-length:"+r.sizeOfUTF8(this.body)),t.push(e.LF+this.body),t.join(e.LF)},r.sizeOfUTF8=function(e){return e?encodeURI(e).match(/%..|./g).length:0},t=function(t){var o,n,s,a,i,c,l,u,p,d,f,g,m,h,y,v,b;for(a=t.search(RegExp(""+e.LF+e.LF)),s=(i=t.substring(0,a).split(e.LF)).shift(),c={},g=function(e){return e.replace(/^\s+|\s+$/g,"")},m=0,y=(v=i.reverse()).length;m<y;m++)u=(d=v[m]).indexOf(":"),c[g(d.substring(0,u))]=g(d.substring(u+1));if(o="",f=a+2,c["content-length"])p=parseInt(c["content-length"]),o=(""+t).substring(f,f+p);else for(n=null,l=h=f,b=t.length;(f<=b?h<b:h>b)&&(n=t.charAt(l))!==e.NULL;l=f<=b?++h:--h)o+=n;return new r(s,c,o)},r.unmarshall=function(r){var o;return function(){var n,s,a,i;for(i=[],n=0,s=(a=r.split(RegExp(""+e.NULL+e.LF+"*"))).length;n<s;n++)(null!=(o=a[n])?o.length:void 0)>0&&i.push(t(o));return i}()},r.marshall=function(t,o,n){return new r(t,o,n).toString()+e.NULL},r}(),t=function(){var t;function n(e){this.ws=e,this.ws.binaryType="arraybuffer",this.counter=0,this.connected=!1,this.heartbeat={outgoing:1e4,incoming:1e4},this.maxWebSocketFrameSize=16384,this.subscriptions={}}return n.prototype.debug=function(e){var t;return"undefined"!=typeof window&&null!==window&&null!=(t=window.console)?t.log(e):void 0},t=function(){return Date.now?Date.now():(new Date).valueOf},n.prototype._transmit=function(e,t,o){var n;for(n=r.marshall(e,t,o),"function"==typeof this.debug&&this.debug(">>> "+n);;){if(!(n.length>this.maxWebSocketFrameSize))return this.ws.send(n);this.ws.send(n.substring(0,this.maxWebSocketFrameSize)),n=n.substring(this.maxWebSocketFrameSize),"function"==typeof this.debug&&this.debug("remaining = "+n.length)}},n.prototype._setupHeartbeat=function(r){var n,s,a,i,c,l;if((c=r.version)===o.VERSIONS.V1_1||c===o.VERSIONS.V1_2)return s=(l=function(){var e,t,o,n;for(n=[],e=0,t=(o=r["heart-beat"].split(",")).length;e<t;e++)i=o[e],n.push(parseInt(i));return n}())[0],n=l[1],0!==this.heartbeat.outgoing&&0!==n&&(a=Math.max(this.heartbeat.outgoing,n),"function"==typeof this.debug&&this.debug("send PING every "+a+"ms"),this.pinger=o.setInterval(a,function(t){return function(){return t.ws.send(e.LF),"function"==typeof t.debug?t.debug(">>> PING"):void 0}}(this))),0!==this.heartbeat.incoming&&0!==s?(a=Math.max(this.heartbeat.incoming,s),"function"==typeof this.debug&&this.debug("check PONG every "+a+"ms"),this.ponger=o.setInterval(a,function(e){return function(){var r;if((r=t()-e.serverActivity)>2*a)return"function"==typeof e.debug&&e.debug("did not receive server activity for the last "+r+"ms"),e.ws.close()}}(this))):void 0},n.prototype._parseConnect=function(){var e,t,r,o;switch(o={},(e=1<=arguments.length?s.call(arguments,0):[]).length){case 2:o=e[0],t=e[1];break;case 3:e[1]instanceof Function?(o=e[0],t=e[1],r=e[2]):(o.login=e[0],o.passcode=e[1],t=e[2]);break;case 4:o.login=e[0],o.passcode=e[1],t=e[2],r=e[3];break;default:o.login=e[0],o.passcode=e[1],t=e[2],r=e[3],o.host=e[4]}return[o,t,r]},n.prototype.connect=function(){var n,a,i,c;return n=1<=arguments.length?s.call(arguments,0):[],c=this._parseConnect.apply(this,n),i=c[0],this.connectCallback=c[1],a=c[2],"function"==typeof this.debug&&this.debug("Opening Web Socket..."),this.ws.onmessage=function(o){return function(n){var s,i,c,l,u,p,d,f,g,m,h,y;if(l="undefined"!=typeof ArrayBuffer&&n.data instanceof ArrayBuffer?(s=new Uint8Array(n.data),"function"==typeof o.debug&&o.debug("--- got data length: "+s.length),function(){var e,t,r;for(r=[],e=0,t=s.length;e<t;e++)i=s[e],r.push(String.fromCharCode(i));return r}().join("")):n.data,o.serverActivity=t(),l!==e.LF){for("function"==typeof o.debug&&o.debug("<<< "+l),y=[],g=0,m=(h=r.unmarshall(l)).length;g<m;g++)switch((u=h[g]).command){case"CONNECTED":"function"==typeof o.debug&&o.debug("connected to server "+u.headers.server),o.connected=!0,o._setupHeartbeat(u.headers),y.push("function"==typeof o.connectCallback?o.connectCallback(u):void 0);break;case"MESSAGE":f=u.headers.subscription,(d=o.subscriptions[f]||o.onreceive)?(c=o,p=u.headers["message-id"],u.ack=function(e){return null==e&&(e={}),c.ack(p,f,e)},u.nack=function(e){return null==e&&(e={}),c.nack(p,f,e)},y.push(d(u))):y.push("function"==typeof o.debug?o.debug("Unhandled received MESSAGE: "+u):void 0);break;case"RECEIPT":y.push("function"==typeof o.onreceipt?o.onreceipt(u):void 0);break;case"ERROR":y.push("function"==typeof a?a(u):void 0);break;default:y.push("function"==typeof o.debug?o.debug("Unhandled frame: "+u):void 0)}return y}"function"==typeof o.debug&&o.debug("<<< PONG")}}(this),this.ws.onclose=function(e){return function(){var t;return t="Whoops! Lost connection to "+e.ws.url,"function"==typeof e.debug&&e.debug(t),e._cleanUp(),"function"==typeof a?a(t):void 0}}(this),this.ws.onopen=function(e){return function(){return"function"==typeof e.debug&&e.debug("Web Socket Opened..."),i["accept-version"]=o.VERSIONS.supportedVersions(),i["heart-beat"]=[e.heartbeat.outgoing,e.heartbeat.incoming].join(","),e._transmit("CONNECT",i)}}(this)},n.prototype.disconnect=function(e,t){return null==t&&(t={}),this._transmit("DISCONNECT",t),this.ws.onclose=null,this.ws.close(),this._cleanUp(),"function"==typeof e?e():void 0},n.prototype._cleanUp=function(){if(this.connected=!1,this.pinger&&o.clearInterval(this.pinger),this.ponger)return o.clearInterval(this.ponger)},n.prototype.send=function(e,t,r){return null==t&&(t={}),null==r&&(r=""),t.destination=e,this._transmit("SEND",t,r)},n.prototype.subscribe=function(e,t,r){var o;return null==r&&(r={}),r.id||(r.id="sub-"+this.counter++),r.destination=e,this.subscriptions[r.id]=t,this._transmit("SUBSCRIBE",r),o=this,{id:r.id,unsubscribe:function(){return o.unsubscribe(r.id)}}},n.prototype.unsubscribe=function(e){return delete this.subscriptions[e],this._transmit("UNSUBSCRIBE",{id:e})},n.prototype.begin=function(e){var t,r;return r=e||"tx-"+this.counter++,this._transmit("BEGIN",{transaction:r}),t=this,{id:r,commit:function(){return t.commit(r)},abort:function(){return t.abort(r)}}},n.prototype.commit=function(e){return this._transmit("COMMIT",{transaction:e})},n.prototype.abort=function(e){return this._transmit("ABORT",{transaction:e})},n.prototype.ack=function(e,t,r){return null==r&&(r={}),r["message-id"]=e,r.subscription=t,this._transmit("ACK",r)},n.prototype.nack=function(e,t,r){return null==r&&(r={}),r["message-id"]=e,r.subscription=t,this._transmit("NACK",r)},n}(),o={VERSIONS:{V1_0:"1.0",V1_1:"1.1",V1_2:"1.2",supportedVersions:function(){return"1.1,1.0"}},client:function(e,r){var n;return null==r&&(r=["v10.stomp","v11.stomp"]),n=new(o.WebSocketClass||WebSocket)(e,r),new t(n)},over:function(e){return new t(e)},Frame:r},"undefined"!=typeof exports&&null!==exports&&(exports.Stomp=o),"undefined"!=typeof window&&null!==window?(o.setInterval=function(e,t){return window.setInterval(t,e)},o.clearInterval=function(e){return window.clearInterval(e)},window.Stomp=o):exports||(self.Stomp=o)}).call(this),function(e,t){"object"==typeof exports?module.exports=exports=t():"function"==typeof define&&define.amd?define([],t):e.CryptoJS=t()}(this,(function(){var e,t,r,o,n,s,a,i,c,l,u,p,d,f,g,m,h,y,v,b,_,S,A,M,k,C,w,I,E,L,T,U,B,O,x,G,P,N,z,R,K,D,j,H,F,J,V,q,W,$,Y,X,Z,Q,ee,te,re,oe,ne,se,ae,ie,ce,le,ue,pe,de,fe,ge,me,he,ye,ve,be,_e,Se,Ae,Me=Me||function(e){var t;if("undefined"!=typeof window&&window.crypto&&(t=window.crypto),!t&&"undefined"!=typeof window&&window.msCrypto&&(t=window.msCrypto),!t&&"undefined"!=typeof global&&global.crypto&&(t=global.crypto),!t&&"function"==typeof require)try{t=require("crypto")}catch(t){}function r(){if(t){if("function"==typeof t.getRandomValues)try{return t.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof t.randomBytes)try{return t.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")}var o=Object.create||function(e){var t;return n.prototype=e,t=new n,n.prototype=null,t};function n(){}var s={},a=s.lib={},i=a.Base={extend:function(e){var t=o(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),(t.init.prototype=t).$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=a.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,r=e.words,o=this.sigBytes,n=e.sigBytes;if(this.clamp(),o%4)for(var s=0;s<n;s++){var a=r[s>>>2]>>>24-s%4*8&255;t[o+s>>>2]|=a<<24-(o+s)%4*8}else for(s=0;s<n;s+=4)t[o+s>>>2]=r[s>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],o=0;o<e;o+=4)t.push(r());return new c.init(t,e)}}),l=s.enc={},u=l.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,o=[],n=0;n<r;n++){var s=t[n>>>2]>>>24-n%4*8&255;o.push((s>>>4).toString(16)),o.push((15&s).toString(16))}return o.join("")},parse:function(e){for(var t=e.length,r=[],o=0;o<t;o+=2)r[o>>>3]|=parseInt(e.substr(o,2),16)<<24-o%8*4;return new c.init(r,t/2)}},p=l.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,o=[],n=0;n<r;n++){var s=t[n>>>2]>>>24-n%4*8&255;o.push(String.fromCharCode(s))}return o.join("")},parse:function(e){for(var t=e.length,r=[],o=0;o<t;o++)r[o>>>2]|=(255&e.charCodeAt(o))<<24-o%4*8;return new c.init(r,t)}},d=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(p.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return p.parse(unescape(encodeURIComponent(e)))}},f=a.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,o=this._data,n=o.words,s=o.sigBytes,a=this.blockSize,i=s/(4*a),l=(i=t?e.ceil(i):e.max((0|i)-this._minBufferSize,0))*a,u=e.min(4*l,s);if(l){for(var p=0;p<l;p+=a)this._doProcessBlock(n,p);r=n.splice(0,l),o.sigBytes-=u}return new c.init(r,u)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),g=(a.Hasher=f.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new g.HMAC.init(e,r).finalize(t)}}}),s.algo={});return s}(Math);function ke(e,t,r){return e^t^r}function Ce(e,t,r){return e&t|~e&r}function we(e,t,r){return(e|~t)^r}function Ie(e,t,r){return e&r|t&~r}function Ee(e,t,r){return e^(t|~r)}function Le(e,t){return e<<t|e>>>32-t}function Te(e,t,r,o){var n,s=this._iv;s?(n=s.slice(0),this._iv=void 0):n=this._prevBlock,o.encryptBlock(n,0);for(var a=0;a<r;a++)e[t+a]^=n[a]}function Ue(e){if(255==(e>>24&255)){var t=e>>16&255,r=e>>8&255,o=255&e;255===t?(t=0,255===r?(r=0,255===o?o=0:++o):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=o}else e+=1<<24;return e}function Be(){for(var e=this._X,t=this._C,r=0;r<8;r++)pe[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<pe[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<pe[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<pe[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<pe[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<pe[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<pe[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<pe[6]>>>0?1:0)|0,this._b=t[7]>>>0<pe[7]>>>0?1:0,r=0;r<8;r++){var o=e[r]+t[r],n=65535&o,s=o>>>16,a=((n*n>>>17)+n*s>>>15)+s*s,i=((4294901760&o)*o|0)+((65535&o)*o|0);de[r]=a^i}e[0]=de[0]+(de[7]<<16|de[7]>>>16)+(de[6]<<16|de[6]>>>16)|0,e[1]=de[1]+(de[0]<<8|de[0]>>>24)+de[7]|0,e[2]=de[2]+(de[1]<<16|de[1]>>>16)+(de[0]<<16|de[0]>>>16)|0,e[3]=de[3]+(de[2]<<8|de[2]>>>24)+de[1]|0,e[4]=de[4]+(de[3]<<16|de[3]>>>16)+(de[2]<<16|de[2]>>>16)|0,e[5]=de[5]+(de[4]<<8|de[4]>>>24)+de[3]|0,e[6]=de[6]+(de[5]<<16|de[5]>>>16)+(de[4]<<16|de[4]>>>16)|0,e[7]=de[7]+(de[6]<<8|de[6]>>>24)+de[5]|0}function Oe(){for(var e=this._X,t=this._C,r=0;r<8;r++)_e[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<_e[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<_e[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<_e[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<_e[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<_e[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<_e[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<_e[6]>>>0?1:0)|0,this._b=t[7]>>>0<_e[7]>>>0?1:0,r=0;r<8;r++){var o=e[r]+t[r],n=65535&o,s=o>>>16,a=((n*n>>>17)+n*s>>>15)+s*s,i=((4294901760&o)*o|0)+((65535&o)*o|0);Se[r]=a^i}e[0]=Se[0]+(Se[7]<<16|Se[7]>>>16)+(Se[6]<<16|Se[6]>>>16)|0,e[1]=Se[1]+(Se[0]<<8|Se[0]>>>24)+Se[7]|0,e[2]=Se[2]+(Se[1]<<16|Se[1]>>>16)+(Se[0]<<16|Se[0]>>>16)|0,e[3]=Se[3]+(Se[2]<<8|Se[2]>>>24)+Se[1]|0,e[4]=Se[4]+(Se[3]<<16|Se[3]>>>16)+(Se[2]<<16|Se[2]>>>16)|0,e[5]=Se[5]+(Se[4]<<8|Se[4]>>>24)+Se[3]|0,e[6]=Se[6]+(Se[5]<<16|Se[5]>>>16)+(Se[4]<<16|Se[4]>>>16)|0,e[7]=Se[7]+(Se[6]<<8|Se[6]>>>24)+Se[5]|0}return e=Me.lib.WordArray,Me.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,o=this._map;e.clamp();for(var n=[],s=0;s<r;s+=3)for(var a=(t[s>>>2]>>>24-s%4*8&255)<<16|(t[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|t[s+2>>>2]>>>24-(s+2)%4*8&255,i=0;i<4&&s+.75*i<r;i++)n.push(o.charAt(a>>>6*(3-i)&63));var c=o.charAt(64);if(c)for(;n.length%4;)n.push(c);return n.join("")},parse:function(t){var r=t.length,o=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var s=0;s<o.length;s++)n[o.charCodeAt(s)]=s}var a=o.charAt(64);if(a){var i=t.indexOf(a);-1!==i&&(r=i)}return function(t,r,o){for(var n=[],s=0,a=0;a<r;a++)if(a%4){var i=o[t.charCodeAt(a-1)]<<a%4*2|o[t.charCodeAt(a)]>>>6-a%4*2;n[s>>>2]|=i<<24-s%4*8,s++}return e.create(n,s)}(t,r,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},function(e){var t=Me,r=t.lib,o=r.WordArray,n=r.Hasher,s=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var i=s.MD5=n.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var o=t+r,n=e[o];e[o]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var s=this._hash.words,i=e[t+0],d=e[t+1],f=e[t+2],g=e[t+3],m=e[t+4],h=e[t+5],y=e[t+6],v=e[t+7],b=e[t+8],_=e[t+9],S=e[t+10],A=e[t+11],M=e[t+12],k=e[t+13],C=e[t+14],w=e[t+15],I=s[0],E=s[1],L=s[2],T=s[3];I=c(I,E,L,T,i,7,a[0]),T=c(T,I,E,L,d,12,a[1]),L=c(L,T,I,E,f,17,a[2]),E=c(E,L,T,I,g,22,a[3]),I=c(I,E,L,T,m,7,a[4]),T=c(T,I,E,L,h,12,a[5]),L=c(L,T,I,E,y,17,a[6]),E=c(E,L,T,I,v,22,a[7]),I=c(I,E,L,T,b,7,a[8]),T=c(T,I,E,L,_,12,a[9]),L=c(L,T,I,E,S,17,a[10]),E=c(E,L,T,I,A,22,a[11]),I=c(I,E,L,T,M,7,a[12]),T=c(T,I,E,L,k,12,a[13]),L=c(L,T,I,E,C,17,a[14]),I=l(I,E=c(E,L,T,I,w,22,a[15]),L,T,d,5,a[16]),T=l(T,I,E,L,y,9,a[17]),L=l(L,T,I,E,A,14,a[18]),E=l(E,L,T,I,i,20,a[19]),I=l(I,E,L,T,h,5,a[20]),T=l(T,I,E,L,S,9,a[21]),L=l(L,T,I,E,w,14,a[22]),E=l(E,L,T,I,m,20,a[23]),I=l(I,E,L,T,_,5,a[24]),T=l(T,I,E,L,C,9,a[25]),L=l(L,T,I,E,g,14,a[26]),E=l(E,L,T,I,b,20,a[27]),I=l(I,E,L,T,k,5,a[28]),T=l(T,I,E,L,f,9,a[29]),L=l(L,T,I,E,v,14,a[30]),I=u(I,E=l(E,L,T,I,M,20,a[31]),L,T,h,4,a[32]),T=u(T,I,E,L,b,11,a[33]),L=u(L,T,I,E,A,16,a[34]),E=u(E,L,T,I,C,23,a[35]),I=u(I,E,L,T,d,4,a[36]),T=u(T,I,E,L,m,11,a[37]),L=u(L,T,I,E,v,16,a[38]),E=u(E,L,T,I,S,23,a[39]),I=u(I,E,L,T,k,4,a[40]),T=u(T,I,E,L,i,11,a[41]),L=u(L,T,I,E,g,16,a[42]),E=u(E,L,T,I,y,23,a[43]),I=u(I,E,L,T,_,4,a[44]),T=u(T,I,E,L,M,11,a[45]),L=u(L,T,I,E,w,16,a[46]),I=p(I,E=u(E,L,T,I,f,23,a[47]),L,T,i,6,a[48]),T=p(T,I,E,L,v,10,a[49]),L=p(L,T,I,E,C,15,a[50]),E=p(E,L,T,I,h,21,a[51]),I=p(I,E,L,T,M,6,a[52]),T=p(T,I,E,L,g,10,a[53]),L=p(L,T,I,E,S,15,a[54]),E=p(E,L,T,I,d,21,a[55]),I=p(I,E,L,T,b,6,a[56]),T=p(T,I,E,L,w,10,a[57]),L=p(L,T,I,E,y,15,a[58]),E=p(E,L,T,I,k,21,a[59]),I=p(I,E,L,T,m,6,a[60]),T=p(T,I,E,L,A,10,a[61]),L=p(L,T,I,E,f,15,a[62]),E=p(E,L,T,I,_,21,a[63]),s[0]=s[0]+I|0,s[1]=s[1]+E|0,s[2]=s[2]+L|0,s[3]=s[3]+T|0},_doFinalize:function(){var t=this._data,r=t.words,o=8*this._nDataBytes,n=8*t.sigBytes;r[n>>>5]|=128<<24-n%32;var s=e.floor(o/4294967296),a=o;r[15+(64+n>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),r[14+(64+n>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var i=this._hash,c=i.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return i},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,r,o,n,s,a){var i=e+(t&r|~t&o)+n+a;return(i<<s|i>>>32-s)+t}function l(e,t,r,o,n,s,a){var i=e+(t&o|r&~o)+n+a;return(i<<s|i>>>32-s)+t}function u(e,t,r,o,n,s,a){var i=e+(t^r^o)+n+a;return(i<<s|i>>>32-s)+t}function p(e,t,r,o,n,s,a){var i=e+(r^(t|~o))+n+a;return(i<<s|i>>>32-s)+t}t.MD5=n._createHelper(i),t.HmacMD5=n._createHmacHelper(i)}(Math),r=(t=Me).lib,o=r.WordArray,n=r.Hasher,s=t.algo,a=[],i=s.SHA1=n.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,o=r[0],n=r[1],s=r[2],i=r[3],c=r[4],l=0;l<80;l++){if(l<16)a[l]=0|e[t+l];else{var u=a[l-3]^a[l-8]^a[l-14]^a[l-16];a[l]=u<<1|u>>>31}var p=(o<<5|o>>>27)+c+a[l];p+=l<20?1518500249+(n&s|~n&i):l<40?1859775393+(n^s^i):l<60?(n&s|n&i|s&i)-1894007588:(n^s^i)-899497514,c=i,i=s,s=n<<30|n>>>2,n=o,o=p}r[0]=r[0]+o|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+i|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,o=8*e.sigBytes;return t[o>>>5]|=128<<24-o%32,t[14+(64+o>>>9<<4)]=Math.floor(r/4294967296),t[15+(64+o>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}}),t.SHA1=n._createHelper(i),t.HmacSHA1=n._createHmacHelper(i),function(e){var t=Me,r=t.lib,o=r.WordArray,n=r.Hasher,s=t.algo,a=[],i=[];!function(){function t(t){for(var r=e.sqrt(t),o=2;o<=r;o++)if(!(t%o))return;return 1}function r(e){return 4294967296*(e-(0|e))|0}for(var o=2,n=0;n<64;)t(o)&&(n<8&&(a[n]=r(e.pow(o,.5))),i[n]=r(e.pow(o,1/3)),n++),o++}();var c=[],l=s.SHA256=n.extend({_doReset:function(){this._hash=new o.init(a.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,o=r[0],n=r[1],s=r[2],a=r[3],l=r[4],u=r[5],p=r[6],d=r[7],f=0;f<64;f++){if(f<16)c[f]=0|e[t+f];else{var g=c[f-15],m=(g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3,h=c[f-2],y=(h<<15|h>>>17)^(h<<13|h>>>19)^h>>>10;c[f]=m+c[f-7]+y+c[f-16]}var v=o&n^o&s^n&s,b=(o<<30|o>>>2)^(o<<19|o>>>13)^(o<<10|o>>>22),_=d+((l<<26|l>>>6)^(l<<21|l>>>11)^(l<<7|l>>>25))+(l&u^~l&p)+i[f]+c[f];d=p,p=u,u=l,l=a+_|0,a=s,s=n,n=o,o=_+(b+v)|0}r[0]=r[0]+o|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+l|0,r[5]=r[5]+u|0,r[6]=r[6]+p|0,r[7]=r[7]+d|0},_doFinalize:function(){var t=this._data,r=t.words,o=8*this._nDataBytes,n=8*t.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(64+n>>>9<<4)]=e.floor(o/4294967296),r[15+(64+n>>>9<<4)]=o,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=n._createHelper(l),t.HmacSHA256=n._createHmacHelper(l)}(Math),function(){var e=Me.lib.WordArray,t=Me.enc;function r(e){return e<<8&4278255360|e>>>8&16711935}t.Utf16=t.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,o=[],n=0;n<r;n+=2){var s=t[n>>>2]>>>16-n%4*8&65535;o.push(String.fromCharCode(s))}return o.join("")},parse:function(t){for(var r=t.length,o=[],n=0;n<r;n++)o[n>>>1]|=t.charCodeAt(n)<<16-n%2*16;return e.create(o,2*r)}},t.Utf16LE={stringify:function(e){for(var t=e.words,o=e.sigBytes,n=[],s=0;s<o;s+=2){var a=r(t[s>>>2]>>>16-s%4*8&65535);n.push(String.fromCharCode(a))}return n.join("")},parse:function(t){for(var o=t.length,n=[],s=0;s<o;s++)n[s>>>1]|=r(t.charCodeAt(s)<<16-s%2*16);return e.create(n,2*o)}}}(),function(){if("function"==typeof ArrayBuffer){var e=Me.lib.WordArray,t=e.init;(e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var r=e.byteLength,o=[],n=0;n<r;n++)o[n>>>2]|=e[n]<<24-n%4*8;t.call(this,o,r)}else t.apply(this,arguments)}).prototype=e}}(),Math,l=(c=Me).lib,u=l.WordArray,p=l.Hasher,d=c.algo,f=u.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),g=u.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),m=u.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),h=u.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),y=u.create([0,1518500249,1859775393,2400959708,2840853838]),v=u.create([1352829926,1548603684,1836072691,2053994217,0]),b=d.RIPEMD160=p.extend({_doReset:function(){this._hash=u.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var o=t+r,n=e[o];e[o]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var s,a,i,c,l,u,p,d,b,_,S,A=this._hash.words,M=y.words,k=v.words,C=f.words,w=g.words,I=m.words,E=h.words;for(u=s=A[0],p=a=A[1],d=i=A[2],b=c=A[3],_=l=A[4],r=0;r<80;r+=1)S=s+e[t+C[r]]|0,S+=r<16?ke(a,i,c)+M[0]:r<32?Ce(a,i,c)+M[1]:r<48?we(a,i,c)+M[2]:r<64?Ie(a,i,c)+M[3]:Ee(a,i,c)+M[4],S=(S=Le(S|=0,I[r]))+l|0,s=l,l=c,c=Le(i,10),i=a,a=S,S=u+e[t+w[r]]|0,S+=r<16?Ee(p,d,b)+k[0]:r<32?Ie(p,d,b)+k[1]:r<48?we(p,d,b)+k[2]:r<64?Ce(p,d,b)+k[3]:ke(p,d,b)+k[4],S=(S=Le(S|=0,E[r]))+_|0,u=_,_=b,b=Le(d,10),d=p,p=S;S=A[1]+i+b|0,A[1]=A[2]+c+_|0,A[2]=A[3]+l+u|0,A[3]=A[4]+s+p|0,A[4]=A[0]+a+d|0,A[0]=S},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,o=8*e.sigBytes;t[o>>>5]|=128<<24-o%32,t[14+(64+o>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var n=this._hash,s=n.words,a=0;a<5;a++){var i=s[a];s[a]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}return n},clone:function(){var e=p.clone.call(this);return e._hash=this._hash.clone(),e}}),c.RIPEMD160=p._createHelper(b),c.HmacRIPEMD160=p._createHmacHelper(b),_=Me.lib.Base,S=Me.enc.Utf8,Me.algo.HMAC=_.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=S.parse(t));var r=e.blockSize,o=4*r;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var n=this._oKey=t.clone(),s=this._iKey=t.clone(),a=n.words,i=s.words,c=0;c<r;c++)a[c]^=1549556828,i[c]^=909522486;n.sigBytes=s.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}}),k=(M=(A=Me).lib).Base,C=M.WordArray,I=(w=A.algo).SHA1,E=w.HMAC,L=w.PBKDF2=k.extend({cfg:k.extend({keySize:4,hasher:I,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,o=E.create(r.hasher,e),n=C.create(),s=C.create([1]),a=n.words,i=s.words,c=r.keySize,l=r.iterations;a.length<c;){var u=o.update(t).finalize(s);o.reset();for(var p=u.words,d=p.length,f=u,g=1;g<l;g++){f=o.finalize(f),o.reset();for(var m=f.words,h=0;h<d;h++)p[h]^=m[h]}n.concat(u),i[0]++}return n.sigBytes=4*c,n}}),A.PBKDF2=function(e,t,r){return L.create(r).compute(e,t)},B=(U=(T=Me).lib).Base,O=U.WordArray,G=(x=T.algo).MD5,P=x.EvpKDF=B.extend({cfg:B.extend({keySize:4,hasher:G,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,o=this.cfg,n=o.hasher.create(),s=O.create(),a=s.words,i=o.keySize,c=o.iterations;a.length<i;){r&&n.update(r),r=n.update(e).finalize(t),n.reset();for(var l=1;l<c;l++)r=n.finalize(r),n.reset();s.concat(r)}return s.sigBytes=4*i,s}}),T.EvpKDF=function(e,t,r){return P.create(r).compute(e,t)},z=(N=Me).lib.WordArray,R=N.algo,K=R.SHA256,D=R.SHA224=K.extend({_doReset:function(){this._hash=new z.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=K._doFinalize.call(this);return e.sigBytes-=4,e}}),N.SHA224=K._createHelper(D),N.HmacSHA224=K._createHmacHelper(D),j=Me.lib,H=j.Base,F=j.WordArray,(J=Me.x64={}).Word=H.extend({init:function(e,t){this.high=e,this.low=t}}),J.WordArray=H.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],o=0;o<t;o++){var n=e[o];r.push(n.high),r.push(n.low)}return F.create(r,this.sigBytes)},clone:function(){for(var e=H.clone.call(this),t=e.words=this.words.slice(0),r=t.length,o=0;o<r;o++)t[o]=t[o].clone();return e}}),function(e){var t=Me,r=t.lib,o=r.WordArray,n=r.Hasher,s=t.x64.Word,a=t.algo,i=[],c=[],l=[];!function(){for(var e=1,t=0,r=0;r<24;r++){i[e+5*t]=(r+1)*(r+2)/2%64;var o=(2*e+3*t)%5;e=t%5,t=o}for(e=0;e<5;e++)for(t=0;t<5;t++)c[e+5*t]=t+(2*e+3*t)%5*5;for(var n=1,a=0;a<24;a++){for(var u=0,p=0,d=0;d<7;d++){if(1&n){var f=(1<<d)-1;f<32?p^=1<<f:u^=1<<f-32}128&n?n=n<<1^113:n<<=1}l[a]=s.create(u,p)}}();var u=[];!function(){for(var e=0;e<25;e++)u[e]=s.create()}();var p=a.SHA3=n.extend({cfg:n.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,o=this.blockSize/2,n=0;n<o;n++){var s=e[t+2*n],a=e[t+2*n+1];s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(E=r[n]).high^=a,E.low^=s}for(var p=0;p<24;p++){for(var d=0;d<5;d++){for(var f=0,g=0,m=0;m<5;m++)f^=(E=r[d+5*m]).high,g^=E.low;var h=u[d];h.high=f,h.low=g}for(d=0;d<5;d++){var y=u[(d+4)%5],v=u[(d+1)%5],b=v.high,_=v.low;for(f=y.high^(b<<1|_>>>31),g=y.low^(_<<1|b>>>31),m=0;m<5;m++)(E=r[d+5*m]).high^=f,E.low^=g}for(var S=1;S<25;S++){var A=(E=r[S]).high,M=E.low,k=i[S];g=k<32?(f=A<<k|M>>>32-k,M<<k|A>>>32-k):(f=M<<k-32|A>>>64-k,A<<k-32|M>>>64-k);var C=u[c[S]];C.high=f,C.low=g}var w=u[0],I=r[0];for(w.high=I.high,w.low=I.low,d=0;d<5;d++)for(m=0;m<5;m++){var E=r[S=d+5*m],L=u[S],T=u[(d+1)%5+5*m],U=u[(d+2)%5+5*m];E.high=L.high^~T.high&U.high,E.low=L.low^~T.low&U.low}E=r[0];var B=l[p];E.high^=B.high,E.low^=B.low}},_doFinalize:function(){var t=this._data,r=t.words,n=(this._nDataBytes,8*t.sigBytes),s=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(e.ceil((1+n)/s)*s>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var a=this._state,i=this.cfg.outputLength/8,c=i/8,l=[],u=0;u<c;u++){var p=a[u],d=p.high,f=p.low;d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),l.push(f),l.push(d)}return new o.init(l,i)},clone:function(){for(var e=n.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});t.SHA3=n._createHelper(p),t.HmacSHA3=n._createHmacHelper(p)}(Math),function(){var e=Me,t=e.lib.Hasher,r=e.x64,o=r.Word,n=r.WordArray,s=e.algo;function a(){return o.create.apply(o,arguments)}var i=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],c=[];!function(){for(var e=0;e<80;e++)c[e]=a()}();var l=s.SHA512=t.extend({_doReset:function(){this._hash=new n.init([new o.init(1779033703,4089235720),new o.init(3144134277,2227873595),new o.init(1013904242,4271175723),new o.init(2773480762,1595750129),new o.init(1359893119,2917565137),new o.init(2600822924,725511199),new o.init(528734635,4215389547),new o.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,o=r[0],n=r[1],s=r[2],a=r[3],l=r[4],u=r[5],p=r[6],d=r[7],f=o.high,g=o.low,m=n.high,h=n.low,y=s.high,v=s.low,b=a.high,_=a.low,S=l.high,A=l.low,M=u.high,k=u.low,C=p.high,w=p.low,I=d.high,E=d.low,L=f,T=g,U=m,B=h,O=y,x=v,G=b,P=_,N=S,z=A,R=M,K=k,D=C,j=w,H=I,F=E,J=0;J<80;J++){var V,q,W=c[J];if(J<16)q=W.high=0|e[t+2*J],V=W.low=0|e[t+2*J+1];else{var $=c[J-15],Y=$.high,X=$.low,Z=(Y>>>1|X<<31)^(Y>>>8|X<<24)^Y>>>7,Q=(X>>>1|Y<<31)^(X>>>8|Y<<24)^(X>>>7|Y<<25),ee=c[J-2],te=ee.high,re=ee.low,oe=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ne=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),se=c[J-7],ae=se.high,ie=se.low,ce=c[J-16],le=ce.high,ue=ce.low;q=(q=(q=Z+ae+((V=Q+ie)>>>0<Q>>>0?1:0))+oe+((V+=ne)>>>0<ne>>>0?1:0))+le+((V+=ue)>>>0<ue>>>0?1:0),W.high=q,W.low=V}var pe,de=N&R^~N&D,fe=z&K^~z&j,ge=L&U^L&O^U&O,me=T&B^T&x^B&x,he=(L>>>28|T<<4)^(L<<30|T>>>2)^(L<<25|T>>>7),ye=(T>>>28|L<<4)^(T<<30|L>>>2)^(T<<25|L>>>7),ve=(N>>>14|z<<18)^(N>>>18|z<<14)^(N<<23|z>>>9),be=(z>>>14|N<<18)^(z>>>18|N<<14)^(z<<23|N>>>9),_e=i[J],Se=_e.high,Ae=_e.low,Me=H+ve+((pe=F+be)>>>0<F>>>0?1:0),ke=ye+me;H=D,F=j,D=R,j=K,R=N,K=z,N=G+(Me=(Me=(Me=Me+de+((pe+=fe)>>>0<fe>>>0?1:0))+Se+((pe+=Ae)>>>0<Ae>>>0?1:0))+q+((pe+=V)>>>0<V>>>0?1:0))+((z=P+pe|0)>>>0<P>>>0?1:0)|0,G=O,P=x,O=U,x=B,U=L,B=T,L=Me+(he+ge+(ke>>>0<ye>>>0?1:0))+((T=pe+ke|0)>>>0<pe>>>0?1:0)|0}g=o.low=g+T,o.high=f+L+(g>>>0<T>>>0?1:0),h=n.low=h+B,n.high=m+U+(h>>>0<B>>>0?1:0),v=s.low=v+x,s.high=y+O+(v>>>0<x>>>0?1:0),_=a.low=_+P,a.high=b+G+(_>>>0<P>>>0?1:0),A=l.low=A+z,l.high=S+N+(A>>>0<z>>>0?1:0),k=u.low=k+K,u.high=M+R+(k>>>0<K>>>0?1:0),w=p.low=w+j,p.high=C+D+(w>>>0<j>>>0?1:0),E=d.low=E+F,d.high=I+H+(E>>>0<F>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,o=8*e.sigBytes;return t[o>>>5]|=128<<24-o%32,t[30+(128+o>>>10<<5)]=Math.floor(r/4294967296),t[31+(128+o>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(l),e.HmacSHA512=t._createHmacHelper(l)}(),q=(V=Me).x64,W=q.Word,$=q.WordArray,Y=V.algo,X=Y.SHA512,Z=Y.SHA384=X.extend({_doReset:function(){this._hash=new $.init([new W.init(3418070365,3238371032),new W.init(1654270250,914150663),new W.init(2438529370,812702999),new W.init(355462360,4144912697),new W.init(1731405415,4290775857),new W.init(2394180231,1750603025),new W.init(3675008525,1694076839),new W.init(1203062813,3204075428)])},_doFinalize:function(){var e=X._doFinalize.call(this);return e.sigBytes-=16,e}}),V.SHA384=X._createHelper(Z),V.HmacSHA384=X._createHmacHelper(Z),Me.lib.Cipher||function(){var e=Me,t=e.lib,r=t.Base,o=t.WordArray,n=t.BufferedBlockAlgorithm,s=e.enc,a=(s.Utf8,s.Base64),i=e.algo.EvpKDF,c=t.Cipher=n.extend({cfg:r.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){n.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(e){return{encrypt:function(t,r,o){return l(r).encrypt(e,t,r,o)},decrypt:function(t,r,o){return l(r).decrypt(e,t,r,o)}}}});function l(e){return"string"==typeof e?_:v}t.StreamCipher=c.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u,p=e.mode={},d=t.BlockCipherMode=r.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),f=p.CBC=((u=d.extend()).Encryptor=u.extend({processBlock:function(e,t){var r=this._cipher,o=r.blockSize;g.call(this,e,t,o),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+o)}}),u.Decryptor=u.extend({processBlock:function(e,t){var r=this._cipher,o=r.blockSize,n=e.slice(t,t+o);r.decryptBlock(e,t),g.call(this,e,t,o),this._prevBlock=n}}),u);function g(e,t,r){var o,n=this._iv;n?(o=n,this._iv=void 0):o=this._prevBlock;for(var s=0;s<r;s++)e[t+s]^=o[s]}var m=(e.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,s=n<<24|n<<16|n<<8|n,a=[],i=0;i<n;i+=4)a.push(s);var c=o.create(a,n);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},h=(t.BlockCipher=c.extend({cfg:c.cfg.extend({mode:f,padding:m}),reset:function(){var e;c.reset.call(this);var t=this.cfg,r=t.iv,o=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=o.createEncryptor:(e=o.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(o,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),t.CipherParams=r.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}})),y=(e.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?o.create([1398893684,1701076831]).concat(r).concat(t):t).toString(a)},parse:function(e){var t,r=a.parse(e),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(t=o.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),h.create({ciphertext:r,salt:t})}},v=t.SerializableCipher=r.extend({cfg:r.extend({format:y}),encrypt:function(e,t,r,o){o=this.cfg.extend(o);var n=e.createEncryptor(r,o),s=n.finalize(t),a=n.cfg;return h.create({ciphertext:s,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:o.format})},decrypt:function(e,t,r,o){return o=this.cfg.extend(o),t=this._parse(t,o.format),e.createDecryptor(r,o).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),b=(e.kdf={}).OpenSSL={execute:function(e,t,r,n){n=n||o.random(8);var s=i.create({keySize:t+r}).compute(e,n),a=o.create(s.words.slice(t),4*r);return s.sigBytes=4*t,h.create({key:s,iv:a,salt:n})}},_=t.PasswordBasedCipher=v.extend({cfg:v.cfg.extend({kdf:b}),encrypt:function(e,t,r,o){var n=(o=this.cfg.extend(o)).kdf.execute(r,e.keySize,e.ivSize);o.iv=n.iv;var s=v.encrypt.call(this,e,t,n.key,o);return s.mixIn(n),s},decrypt:function(e,t,r,o){o=this.cfg.extend(o),t=this._parse(t,o.format);var n=o.kdf.execute(r,e.keySize,e.ivSize,t.salt);return o.iv=n.iv,v.decrypt.call(this,e,t,n.key,o)}})}(),Me.mode.CFB=((Q=Me.lib.BlockCipherMode.extend()).Encryptor=Q.extend({processBlock:function(e,t){var r=this._cipher,o=r.blockSize;Te.call(this,e,t,o,r),this._prevBlock=e.slice(t,t+o)}}),Q.Decryptor=Q.extend({processBlock:function(e,t){var r=this._cipher,o=r.blockSize,n=e.slice(t,t+o);Te.call(this,e,t,o,r),this._prevBlock=n}}),Q),Me.mode.ECB=((ee=Me.lib.BlockCipherMode.extend()).Encryptor=ee.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),ee.Decryptor=ee.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),ee),Me.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,o=4*t,n=o-r%o,s=r+n-1;e.clamp(),e.words[s>>>2]|=n<<24-s%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},Me.pad.Iso10126={pad:function(e,t){var r=4*t,o=r-e.sigBytes%r;e.concat(Me.lib.WordArray.random(o-1)).concat(Me.lib.WordArray.create([o<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},Me.pad.Iso97971={pad:function(e,t){e.concat(Me.lib.WordArray.create([2147483648],1)),Me.pad.ZeroPadding.pad(e,t)},unpad:function(e){Me.pad.ZeroPadding.unpad(e),e.sigBytes--}},Me.mode.OFB=(re=(te=Me.lib.BlockCipherMode.extend()).Encryptor=te.extend({processBlock:function(e,t){var r=this._cipher,o=r.blockSize,n=this._iv,s=this._keystream;n&&(s=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(s,0);for(var a=0;a<o;a++)e[t+a]^=s[a]}}),te.Decryptor=re,te),Me.pad.NoPadding={pad:function(){},unpad:function(){}},oe=Me.lib.CipherParams,ne=Me.enc.Hex,Me.format.Hex={stringify:function(e){return e.ciphertext.toString(ne)},parse:function(e){var t=ne.parse(e);return oe.create({ciphertext:t})}},function(){var e=Me,t=e.lib.BlockCipher,r=e.algo,o=[],n=[],s=[],a=[],i=[],c=[],l=[],u=[],p=[],d=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,f=0;for(t=0;t<256;t++){var g=f^f<<1^f<<2^f<<3^f<<4;g=g>>>8^255&g^99,o[r]=g;var m=e[n[g]=r],h=e[m],y=e[h],v=257*e[g]^16843008*g;s[r]=v<<24|v>>>8,a[r]=v<<16|v>>>16,i[r]=v<<8|v>>>24,c[r]=v,v=16843009*y^65537*h^257*m^16843008*r,l[g]=v<<24|v>>>8,u[g]=v<<16|v>>>16,p[g]=v<<8|v>>>24,d[g]=v,r?(r=m^e[e[e[y^m]]],f^=e[e[f]]):r=f=1}}();var f=[0,1,2,4,8,16,32,64,128,27,54],g=r.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,n=4*(1+(this._nRounds=6+r)),s=this._keySchedule=[],a=0;a<n;a++)a<r?s[a]=t[a]:(g=s[a-1],a%r?6<r&&a%r==4&&(g=o[g>>>24]<<24|o[g>>>16&255]<<16|o[g>>>8&255]<<8|o[255&g]):(g=o[(g=g<<8|g>>>24)>>>24]<<24|o[g>>>16&255]<<16|o[g>>>8&255]<<8|o[255&g],g^=f[a/r|0]<<24),s[a]=s[a-r]^g);for(var i=this._invKeySchedule=[],c=0;c<n;c++){if(a=n-c,c%4)var g=s[a];else g=s[a-4];i[c]=c<4||a<=4?g:l[o[g>>>24]]^u[o[g>>>16&255]]^p[o[g>>>8&255]]^d[o[255&g]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,i,c,o)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,l,u,p,d,n),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,o,n,s,a,i){for(var c=this._nRounds,l=e[t]^r[0],u=e[t+1]^r[1],p=e[t+2]^r[2],d=e[t+3]^r[3],f=4,g=1;g<c;g++){var m=o[l>>>24]^n[u>>>16&255]^s[p>>>8&255]^a[255&d]^r[f++],h=o[u>>>24]^n[p>>>16&255]^s[d>>>8&255]^a[255&l]^r[f++],y=o[p>>>24]^n[d>>>16&255]^s[l>>>8&255]^a[255&u]^r[f++],v=o[d>>>24]^n[l>>>16&255]^s[u>>>8&255]^a[255&p]^r[f++];l=m,u=h,p=y,d=v}m=(i[l>>>24]<<24|i[u>>>16&255]<<16|i[p>>>8&255]<<8|i[255&d])^r[f++],h=(i[u>>>24]<<24|i[p>>>16&255]<<16|i[d>>>8&255]<<8|i[255&l])^r[f++],y=(i[p>>>24]<<24|i[d>>>16&255]<<16|i[l>>>8&255]<<8|i[255&u])^r[f++],v=(i[d>>>24]<<24|i[l>>>16&255]<<16|i[u>>>8&255]<<8|i[255&p])^r[f++],e[t]=m,e[t+1]=h,e[t+2]=y,e[t+3]=v},keySize:8});e.AES=t._createHelper(g)}(),function(){var e=Me,t=e.lib,r=t.WordArray,o=t.BlockCipher,n=e.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],i=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],u=n.DES=o.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var o=s[r]-1;t[r]=e[o>>>5]>>>31-o%32&1}for(var n=this._subKeys=[],c=0;c<16;c++){var l=n[c]=[],u=i[c];for(r=0;r<24;r++)l[r/6|0]|=t[(a[r]-1+u)%28]<<31-r%6,l[4+(r/6|0)]|=t[28+(a[r+24]-1+u)%28]<<31-r%6;for(l[0]=l[0]<<1|l[0]>>>31,r=1;r<7;r++)l[r]=l[r]>>>4*(r-1)+3;l[7]=l[7]<<5|l[7]>>>27}var p=this._invSubKeys=[];for(r=0;r<16;r++)p[r]=n[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],p.call(this,4,252645135),p.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),p.call(this,1,1431655765);for(var o=0;o<16;o++){for(var n=r[o],s=this._lBlock,a=this._rBlock,i=0,u=0;u<8;u++)i|=c[u][((a^n[u])&l[u])>>>0];this._lBlock=a,this._rBlock=s^i}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,p.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),p.call(this,16,65535),p.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function p(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function d(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}e.DES=o._createHelper(u);var f=n.TripleDES=o.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),o=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=u.createEncryptor(r.create(t)),this._des2=u.createEncryptor(r.create(o)),this._des3=u.createEncryptor(r.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=o._createHelper(f)}(),function(){var e=Me,t=e.lib.StreamCipher,r=e.algo,o=r.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,o=this._S=[],n=0;n<256;n++)o[n]=n;n=0;for(var s=0;n<256;n++){var a=n%r,i=t[a>>>2]>>>24-a%4*8&255;s=(s+o[n]+i)%256;var c=o[n];o[n]=o[s],o[s]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=n.call(this)},keySize:8,ivSize:0});function n(){for(var e=this._S,t=this._i,r=this._j,o=0,n=0;n<4;n++){r=(r+e[t=(t+1)%256])%256;var s=e[t];e[t]=e[r],e[r]=s,o|=e[(e[t]+e[r])%256]<<24-8*n}return this._i=t,this._j=r,o}e.RC4=t._createHelper(o);var s=r.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var e=this.cfg.drop;0<e;e--)n.call(this)}});e.RC4Drop=t._createHelper(s)}(),Me.mode.CTRGladman=(ae=(se=Me.lib.BlockCipherMode.extend()).Encryptor=se.extend({processBlock:function(e,t){var r,o=this._cipher,n=o.blockSize,s=this._iv,a=this._counter;s&&(a=this._counter=s.slice(0),this._iv=void 0),0===((r=a)[0]=Ue(r[0]))&&(r[1]=Ue(r[1]));var i=a.slice(0);o.encryptBlock(i,0);for(var c=0;c<n;c++)e[t+c]^=i[c]}}),se.Decryptor=ae,se),ce=(ie=Me).lib.StreamCipher,le=ie.algo,ue=[],pe=[],de=[],fe=le.Rabbit=ce.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var o=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(r=this._b=0;r<4;r++)Be.call(this);for(r=0;r<8;r++)n[r]^=o[r+4&7];if(t){var s=t.words,a=s[0],i=s[1],c=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),u=c>>>16|4294901760&l,p=l<<16|65535&c;for(n[0]^=c,n[1]^=u,n[2]^=l,n[3]^=p,n[4]^=c,n[5]^=u,n[6]^=l,n[7]^=p,r=0;r<4;r++)Be.call(this)}},_doProcessBlock:function(e,t){var r=this._X;Be.call(this),ue[0]=r[0]^r[5]>>>16^r[3]<<16,ue[1]=r[2]^r[7]>>>16^r[5]<<16,ue[2]=r[4]^r[1]>>>16^r[7]<<16,ue[3]=r[6]^r[3]>>>16^r[1]<<16;for(var o=0;o<4;o++)ue[o]=16711935&(ue[o]<<8|ue[o]>>>24)|4278255360&(ue[o]<<24|ue[o]>>>8),e[t+o]^=ue[o]},blockSize:4,ivSize:2}),ie.Rabbit=ce._createHelper(fe),Me.mode.CTR=(me=(ge=Me.lib.BlockCipherMode.extend()).Encryptor=ge.extend({processBlock:function(e,t){var r=this._cipher,o=r.blockSize,n=this._iv,s=this._counter;n&&(s=this._counter=n.slice(0),this._iv=void 0);var a=s.slice(0);r.encryptBlock(a,0),s[o-1]=s[o-1]+1|0;for(var i=0;i<o;i++)e[t+i]^=a[i]}}),ge.Decryptor=me,ge),ye=(he=Me).lib.StreamCipher,ve=he.algo,be=[],_e=[],Se=[],Ae=ve.RabbitLegacy=ye.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],o=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]],n=this._b=0;n<4;n++)Oe.call(this);for(n=0;n<8;n++)o[n]^=r[n+4&7];if(t){var s=t.words,a=s[0],i=s[1],c=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),u=c>>>16|4294901760&l,p=l<<16|65535&c;for(o[0]^=c,o[1]^=u,o[2]^=l,o[3]^=p,o[4]^=c,o[5]^=u,o[6]^=l,o[7]^=p,n=0;n<4;n++)Oe.call(this)}},_doProcessBlock:function(e,t){var r=this._X;Oe.call(this),be[0]=r[0]^r[5]>>>16^r[3]<<16,be[1]=r[2]^r[7]>>>16^r[5]<<16,be[2]=r[4]^r[1]>>>16^r[7]<<16,be[3]=r[6]^r[3]>>>16^r[1]<<16;for(var o=0;o<4;o++)be[o]=16711935&(be[o]<<8|be[o]>>>24)|4278255360&(be[o]<<24|be[o]>>>8),e[t+o]^=be[o]},blockSize:4,ivSize:2}),he.RabbitLegacy=ye._createHelper(Ae),Me.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){var t=e.words,r=e.sigBytes-1;for(r=e.sigBytes-1;0<=r;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},Me}));var MCK_LABELS,MCK_BASE_URL,w=window,d=document,MCK_CURR_LATITIUDE=40.7324319,MCK_CURR_LONGITUDE=-73.82480777777776,mckUtils=new MckUtils,mckDateUtils=new MckDateUtils,mckContactUtils=new MckContactUtils,mckMapUtils=new MckMapUtils;function MckUtils(){var e=this,t=["p","div","pre","form"];e.init=function(){var e=MCK_CONTEXTPATH?MCK_CONTEXTPATH+"/v2/tab/initialize.page":"https://apps.applozic.com/v2/tab/initialize.page",t=MCK_CONTEXTPATH?MCK_CONTEXTPATH+"/rest/ws/message/list":"https://apps.applozic.com/rest/ws/message/list";$applozic.ajax({url:e,contentType:"application/json",type:"OPTIONS"}).done((function(e){})),$applozic.ajax({url:t,contentType:"application/json",type:"OPTIONS"}).done((function(e){}))},e.showElement=function(e){("object"!=typeof e&&void 0!==e&&null!==typeof e||e&&"object"==typeof e&&0!==e.length)&&(e.classList.remove("n-vis"),e.classList.add("vis"))},e.hideElement=function(e){("object"!=typeof e&&void 0!==e&&null!==typeof e||e&&"object"==typeof e&&0!==e.length)&&(e.classList.remove("vis"),e.classList.add("n-vis"))},e.badgeCountOnLaucher=function(e,t){var r=document.getElementById("applozic-badge-count");!0===e&&t>0&&(r.innerHTML=t<99?t:"99+",r.classList.add("mck-badge-count")),!0===e&&0===t&&(r.innerHTML="",r.classList.remove("mck-badge-count"))},e.randomId=function(){return w.Math.random().toString(36).substring(7)},e.textVal=function(e){for(var r=[],o=[],n=function(){r.push(o.join("")),o=[]},s=function(e){if(3===e.nodeType)o.push(e.nodeValue);else if(1===e.nodeType){var r=e.tagName.toLowerCase(),a=-1!==t.indexOf(r);if(a&&o.length&&n(),"img"===r){var i=e.getAttribute("alt")||"";return void(i&&o.push(i))}if("style"===r)return;"br"===r&&n();for(var c=e.childNodes,l=0;l<c.length;l++)s(c[l]);a&&o.length&&n()}},a=e.childNodes,i=0;i<a.length;i++)s(a[i]);return o.length&&n(),r.join("\n")},e.mouseX=function(e){return e.pageX?e.pageX:e.clientX?e.clientX+(d.documentElement.scrollLeft?d.documentElement.scrollLeft:d.body.scrollLeft):null},e.mouseY=function(e){return e.pageY?e.pageY:e.clientY?e.clientY+(d.documentElement.scrollTop?d.documentElement.scrollTop:d.body.scrollTop):null},e.startsWith=function(e,t){if(null===t||void 0===e)return!1;var r=t.length;if(e.length<r)return!1;for(--r;r>=0&&e[r]===t[r];--r)continue;return r<0},e.setEndOfContenteditable=function(e){var t,r;document.createRange?((t=document.createRange()).selectNodeContents(e),t.collapse(!1),(r=window.getSelection()).removeAllRanges(),r.addRange(t)):document.selection&&((t=document.body.createTextRange()).moveToElementText(e),t.collapse(!1),t.select())},e.b64EncodeUnicode=function(e){return btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,(function(e,t){return String.fromCharCode("0x"+t)})))},e.b64DecodeUnicode=function(e){return decodeURIComponent(Array.prototype.map.call(atob(e),(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))},e.checkIfB64Encoded=function(e){return/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/.test(e)},e.encrypt=function(e,t){if(!t)return e;var r=CryptoJS.enc.Utf8.parse(t);return CryptoJS.AES.encrypt(e,r,{mode:CryptoJS.mode.ECB,padding:CryptoJS.pad.ZeroPadding}).toString()},e.decrypt=function(e,t){if(!t)return e;var r=CryptoJS.enc.Utf8.parse(t),o=CryptoJS.lib.CipherParams.create({ciphertext:CryptoJS.enc.Base64.parse(e)});return CryptoJS.AES.decrypt(o,r,{mode:CryptoJS.mode.ECB,padding:CryptoJS.pad.ZeroPadding}).toString(CryptoJS.enc.Utf8).replace(/\\u0000/g,"").replace(/^\s*|\s*[\x00-\x10]*$/g,"")},e.ajax=function(t){var r=e.extendObject({},{},t);!t.skipEncryption&&t.encryptionKey&&("post"===r.type.toLowerCase()&&(r.data=encrypt(t.data,t.encryptionKey)),r.success=function(r){var o=e.decrypt(r,t.encryptionKey);mckUtils.isJsonString(o)?t.success(JSON.parse(o)):t.success(o)}),$applozic.ajax(r)},e.isJsonString=function(e){try{JSON.parse(e)}catch(e){return!1}return isNaN(e)},e.extendObject=function(){var t={},r=!1,o=0,n=arguments.length;"[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(r=arguments[0],o++);for(var s=function(o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r&&"[object Object]"===Object.prototype.toString.call(o[n])?t[n]=e.extendObject(!0,t[n],o[n]):t[n]=o[n])};o<n;o++){var a=arguments[o];s(a)}return t}}function MckContactUtils(){var e=this;e.getContactId=function(t){var r=t.contactId;return e.formatContactId(r)},e.formatContactId=function(e){return e&&(0===e.indexOf("+")&&(e=e.substring(1)),e=(e=decodeURIComponent(e)).replace(/\%/g,"PERCENTAGE").replace(/\~/g,"TILDE").replace(/\!/g,"EXCLAMATION_MARK").replace(/\*/g,"STAR").replace(/\(/g,"LEFT_PARENTHESIS").replace(/\)/g,"RIGHT_PARENTHESIS").replace(/\-/g,"DASH").replace(/\@/g,"AT").replace(/\./g,"DOT").replace(/\#/g,"HASH").replace(/\|/g,"VBAR").replace(/\+/g,"PLUS").replace(/\;/g,"SCOLON").replace(/\?/g,"QMARK").replace(/\//g,"FORWARDSLASH").trim()),e}}function MckMapUtils(){this.getCurrentLocation=function(e,t){w.navigator.geolocation.getCurrentPosition(e,t)},this.getSelectedLocation=function(){return{lat:MCK_CURR_LATITIUDE,lon:MCK_CURR_LONGITUDE}}}function MckDateUtils(){this.getDate=function(e){var t=new Date(parseInt(e,10)),r=new Date;return r.getDate()===t.getDate()&&r.getMonth()===t.getMonth()&&r.getYear()===t.getYear()?n(t,"h:MM TT",!1):n(t,"mmm d, h:MM TT",!1)},this.getLastSeenAtStatus=function(e){var t=new Date(parseInt(e,10)),r=new Date;if(r.getDate()===t.getDate()&&r.getMonth()===t.getMonth()&&r.getYear()===t.getYear()){var o=r.getHours()-t.getHours(),s=w.Math.floor((r.getTime()-t.getTime())/6e4);return s<60?s<=1?MCK_LABELS["last.seen"]+" 1 "+MCK_LABELS.min+" "+MCK_LABELS.ago:MCK_LABELS["last.seen"]+" "+s+MCK_LABELS.mins+" "+MCK_LABELS.ago:1===o?MCK_LABELS["last.seen"]+" 1 "+MCK_LABELS.hour+" "+MCK_LABELS.ago:MCK_LABELS["last.seen"]+" "+o+MCK_LABELS.hours+" "+MCK_LABELS.ago}return r.getDate()-t.getDate()==1&&r.getMonth()===t.getMonth()&&r.getYear()===t.getYear()?MCK_LABELS["last.seen.on"]+" "+MCK_LABELS.yesterday:MCK_LABELS["last.seen.on"]+" "+n(t,"mmm d",!1)},this.getTimeOrDate=function(e,t){var r=new Date(parseInt(e,10)),o=new Date;return t?o.getDate()===r.getDate()&&o.getMonth()===r.getMonth()&&o.getYear()===r.getYear()?n(r,"h:MM TT",!1):n(r,"mmm d",!1):n(r,"mmm d, h:MM TT",!1)},this.getSystemDate=function(e){var t=new Date(parseInt(e,10));return n(t,"mmm d, h:MM TT",!1)},this.convertMilisIntoTime=function(e){parseInt(e%1e3/100);var t=parseInt(e/1e3%60),r=parseInt(e/6e4%60),o=parseInt(e/36e5%24);return o>0?o+" Hr "+r+" Min "+t+" Sec":r>0?r+" Min "+t+" Sec":t+" Sec "};var e,t,r,o,n=(e=/d{1,4}|m{1,4}|yy(?:yy)?|([HhMsTt])\1?|[LloSZ]|"[^"]*"|'[^']*'/g,t=/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,r=/[^-+\dA-Z]/g,o=function(e,t){for(e=String(e),t=t||2;e.length<t;)e="0"+e;return e},function(s,a,i){var c=n;if(1!==arguments.length||"[object String]"!==Object.prototype.toString.call(s)||/\d/.test(s)||(a=s,s=void 0),s=s?new Date(s):new Date,isNaN(s))throw SyntaxError("invalid date");"UTC:"===(a=String(a)).slice(0,4)&&(a=a.slice(4),i=!0);var l=i?"getUTC":"get",u=s[l+"Date"](),p=s[l+"Day"](),d=s[l+"Month"](),f=s[l+"FullYear"](),g=s[l+"Hours"](),m=s[l+"Minutes"](),h=s[l+"Seconds"](),y=s[l+"Milliseconds"](),v=i?0:s.getTimezoneOffset(),b={d:u,dd:o(u),ddd:c.i18n.dayNames[p],dddd:c.i18n.dayNames[p+7],m:d+1,mm:o(d+1),mmm:c.i18n.monthNames[d],mmmm:c.i18n.monthNames[d+12],yy:String(f).slice(2),yyyy:f,h:g%12||12,hh:o(g%12||12),H:g,HH:o(g),M:m,MM:o(m),s:h,ss:o(h),l:o(y,3),L:o(y>99?w.Math.round(y/10):y),t:g<12?"a":"p",tt:g<12?MCK_LABELS["time.format.am"]||"am":MCK_LABELS["time.format.pm"]||"pm",T:g<12?"A":"P",TT:g<12?MCK_LABELS["time.format.AM"]||"AM":MCK_LABELS["time.format.PM"]||"PM",Z:i?"UTC":(String(s).match(t)||[""]).pop().replace(r,""),o:(v>0?"-":"+")+o(100*w.Math.floor(w.Math.abs(v)/60)+w.Math.abs(v)%60,4),S:["th","st","nd","rd"][u%10>3?0:(u%100-u%10!=10)*u%10]};return a.replace(e,(function(e){return e in b?b[e]:e.slice(1,e.length-1)}))});n.masks={default:"mmm d, yyyy h:MM TT",fullDateFormat:"mmm d, yyyy h:MM TT",onlyDateFormat:"mmm d",onlyTimeFormat:"h:MM TT",mailDateFormat:"mmm d, yyyy",mediumDate:"mmm d, yyyy",longDate:"mmmm d, yyyy",fullDate:"dddd, mmmm d, yyyy",shortTime:"h:MM TT",mediumTime:"h:MM:ss TT",longTime:"h:MM:ss TT Z",isoDate:"yyyy-mm-dd",isoTime:"HH:MM:ss",isoDateTime:"yyyy-mm-dd'T'HH:MM:ss",isoUtcDateTime:"UTC:yyyy-mm-dd'T'HH:MM:ss'Z'"},n.i18n={dayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","January","February","March","April","May","June","July","August","September","October","November","December"]}}!function(e){"use strict";"undefined"==typeof Applozic?e.Applozic=function(){var e={init:function(){}};return e}():console.log("Applozic already defined.")}(window);var ALStorage=function(e){var t,r,o=[],n=[],s=[],a=[],i=new MckUtils;return{updateLatestMessage:function(e){var t=[];t.push(e),ALStorage.updateLatestMessageArray(t),ALStorage.updateMckMessageArray(t)},getLatestMessageArray:function(){return ALStorage.isSessionStorageAvailable()?JSON.parse(w.sessionStorage.getItem("mckLatestMessageArray")):o},getFriendListGroupName:function(){return ALStorage.isSessionStorageAvailable()?w.sessionStorage.getItem("friendListGroupName"):t},setFriendListGroupName:function(e){ALStorage.isSessionStorageAvailable()?w.sessionStorage.setItem("friendListGroupName",e):t=e},setFriendListGroupType:function(e){ALStorage.isSessionStorageAvailable()?w.sessionStorage.setItem("friendListGroupType",e):r=e},getFriendListGroupType:function(){return ALStorage.isSessionStorageAvailable()?w.sessionStorage.getItem("friendListGroupType"):r},setLatestMessageArray:function(e){ALStorage.isSessionStorageAvailable()?w.sessionStorage.setItem("mckLatestMessageArray",w.JSON.stringify(e)):o=e},updateLatestMessageArray:function(e){if(ALStorage.isSessionStorageAvailable()){var t=JSON.parse(w.sessionStorage.getItem("mckLatestMessageArray"));return null!==t?(t=t.concat(e),w.sessionStorage.setItem("mckLatestMessageArray",w.JSON.stringify(t))):w.sessionStorage.setItem("mckLatestMessageArray",w.JSON.stringify(e)),e}return o=o.concat(e)},getMckMessageArray:function(){return ALStorage.isSessionStorageAvailable()?JSON.parse(w.sessionStorage.getItem("mckMessageArray")):n},clearMckMessageArray:function(){ALStorage.isSessionStorageAvailable()?(w.sessionStorage.removeItem("mckMessageArray"),w.sessionStorage.removeItem("mckLatestMessageArray")):(n.length=0,o.length=0)},clearAppHeaders:function(){ALStorage.isSessionStorageAvailable()&&w.sessionStorage.removeItem("chatheaders")},setAppHeaders:function(e){if(ALStorage.isSessionStorageAvailable()){var t=i.b64EncodeUnicode(JSON.stringify(e));w.sessionStorage.setItem("chatheaders",t)}},getAppHeaders:function(){if(ALStorage.isSessionStorageAvailable()){var e=w.sessionStorage.getItem("chatheaders");return e=e?JSON.parse(i.checkIfB64Encoded(e)?i.b64DecodeUnicode(e):e):{}}},getMessageByKey:function(e){return s[e]},updateMckMessageArray:function(e){for(var t=0;t<e.length;t++){var r=e[t];s[r.key]=r}if(ALStorage.isSessionStorageAvailable()){var o=JSON.parse(w.sessionStorage.getItem("mckMessageArray"));return null!==o?(o=o.concat(e),w.sessionStorage.setItem("mckMessageArray",w.JSON.stringify(o))):w.sessionStorage.setItem("mckMessageArray",w.JSON.stringify(e)),e}return n=n.concat(e)},getMckContactNameArray:function(){return ALStorage.isSessionStorageAvailable()?JSON.parse(w.sessionStorage.getItem("mckContactNameArray")):a},setMckContactNameArray:function(e){ALStorage.isSessionStorageAvailable()?w.sessionStorage.setItem("mckContactNameArray",w.JSON.stringify(e)):a=e},updateMckContactNameArray:function(e){if(ALStorage.isSessionStorageAvailable()){var t=JSON.parse(w.sessionStorage.getItem("mckContactNameArray"));return null!==t&&(e=e.concat(t)),w.sessionStorage.setItem("mckContactNameArray",w.JSON.stringify(e)),e}return a=a.concat(e)},clearMckContactNameArray:function(){ALStorage.isSessionStorageAvailable()?w.sessionStorage.removeItem("mckContactNameArray"):a.length=0},clearSessionStorageElements:function(){ALStorage.clearMckMessageArray(),ALStorage.clearAppHeaders(),ALStorage.clearMckContactNameArray()},isSessionStorageAvailable:function(){try{return void 0!==w.sessionStorage}catch(e){return!1}}}}(window);!function(e){"use strict";"undefined"==typeof ALApiService?e.Applozic.ALApiService=function(t){var r={},o="",n=new MckUtils;r.DEFAULT_ENCRYPTED_APP_VERSION=112;var s,a,i,c,l,u,p="https://apps.applozic.com";function d(e){var t="";for(var r in e)t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r])+"&";return t.substring(0,t.length-1)}return r.getFileUrl=function(){return"https://applozic.appspot.com"},r.initServerUrl=function(e){p=e},r.login=function(e){r.connect(e)},r.connect=function(e){var t=e.data.alUser.appVersionCode||r.DEFAULT_ENCRYPTED_APP_VERSION;o=e.data.alUser.applicationId,p=e.data.baseUrl?e.data.baseUrl:"https://apps.applozic.com",u=e.data.alUser.fileupload,e.data.alUser.appVersionCode=t,r.ajax({url:p+"/v2/tab/initialize.page",skipEncryption:!0,type:"post",async:void 0===e.async||e.async,data:JSON.stringify(e.data.alUser),contentType:"application/json",headers:{"Application-Key":o},success:function(n){c=btoa(n.userId+":"+n.deviceKey),a=n.deviceKey,s=e.data.alUser.password,i=e.data.alUser.appModuleName,r.AUTH_TOKEN=n.authToken,r.setAjaxHeaders(c,o,n.deviceKey,e.data.alUser.password,e.data.alUser.appModuleName),r.setEncryptionKeys(n.encryptionKey,n.userEncryptionKey),"object"==typeof n&&(n.appVersionCode=t),e.success&&e.success(n)},error:function(t){e.error&&e.error(t)}})},r.addRequestHeaders=function(e){r.AUTH_TOKEN?e.setRequestHeader("X-Authorization",r.AUTH_TOKEN):(c&&(e.setRequestHeader("Authorization","Basic "+c),e.setRequestHeader("Application-User","Basic "+c)),a&&e.setRequestHeader("Device-Key",a),s&&e.setRequestHeader("Access-Token",s),i&&e.setRequestHeader("App-Module-Name",i),e.setRequestHeader("UserId-Enabled",!0)),e.setRequestHeader("Application-Key",o)},r.getAttachmentHeaders=function(){var e={};return r.AUTH_TOKEN?e["X-Authorization"]=r.AUTH_TOKEN:(e={"UserId-Enabled":!0,Authorization:"Basic "+c,"Application-User":"Basic "+c,"Application-Key":o,"Device-Key":a},s&&(e["Access-Token"]=s)),e},r.getEncryptionKey=function(){return l},r.setEncryptionKeys=function(e,t){l=e,t},r.setAjaxHeaders=function(e,t,r,n,l){o=t,c=e,a=r,s=n,i=l},r.ajax=function(e){var t=function(){for(var e=1;e<arguments.length;e++)for(var t in arguments[e])arguments[e].hasOwnProperty(t)&&(arguments[0][t]=arguments[e][t]);return arguments[0]}({},{},e);!e.skipEncryption&&l&&("post"===t.type.toLowerCase()&&(t.data=n.encrypt(e.data,l)),t.success=function(t){var r=n.decrypt(t,l);n.isJsonString(r)?e.success(JSON.parse(r)):e.success(r)});var s,a,i=new XMLHttpRequest,c=!0;(void 0!==t.async||e.async)&&(c=t.async);var u=t.type.toUpperCase();"GET"===u&&void 0!==t.data&&(t.url=t.url+"?"+t.data),i.open(u,t.url,c),"POST"!==u&&"GET"!==u||(a=void 0===t.contentType?"application/x-www-form-urlencoded; charset=UTF-8":t.contentType,i.setRequestHeader("Content-Type",a)),p=p||"https://apps.applozic.com",-1!==t.url.indexOf(p)&&r.addRequestHeaders(i),-1!==t.url.indexOf("applozic.appspot.com")&&i.setRequestHeader("Application-Key",o),void 0===t.data?i.send():i.send(t.data),i.onreadystatechange=function(){if(4===i.readyState)if(200===i.status){var e=i.getResponseHeader("Content-Type");void 0!==e&&"null"!==e&&null!==e||(e=""),s=-1!=e.toLowerCase().indexOf("text/html")?i.responseXML:-1!=e.toLowerCase().indexOf("application/json")?JSON.parse(i.responseText):i.responseText,t.success(s)}else t.error(s)}},r.getMessages=function(e){e.data.userId||e.data.groupId?"undefined"===e.data.pageSize&&(e.data.pageSize=30):void 0===e.data.mainPageSize&&(e.data.mainPageSize=60);var t=d(e.data),o=new Object;r.ajax({url:p+"/rest/ws/message/list?"+t,async:void 0===e.async||e.async,type:"get",success:function(t){o.status="success",o.data=t,e.success&&e.success(o)},error:function(t,r,n){o.status="error",e.error&&e.error(o)}})},r.sendMessage=function(e){r.ajax({type:"POST",url:p+"/rest/ws/message/send",global:!1,data:JSON.stringify(e.data.message),async:void 0===e.async||e.async,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.sendDeliveryUpdate=function(e){r.ajax({url:p+"/rest/ws/message/delivered",data:"key="+e.data.key,global:!1,type:"get",async:void 0===e.async||e.async,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.sendReadUpdate=function(e){r.ajax({url:p+"/rest/ws/message/read",data:"key="+e.data.key,global:!1,type:"get",async:void 0===e.async||e.async,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.deleteMessage=function(e){r.ajax({url:p+"/rest/ws/message/delete?key="+e.data.key,global:!1,type:"get",async:void 0===e.async||e.async,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.updateReplyMessage=function(e){r.ajax({url:p+"/rest/ws/message/detail?keys="+e.data.key,type:"get",async:void 0===e.async||e.async,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.deleteConversation=function(e){r.ajax({url:p+"/rest/ws/message/delete/conversation",type:"get",async:void 0===e.async||e.async,global:!1,data:d(e.data),success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.createGroup=function(e){r.ajax({url:p+"/rest/ws/group/create",global:!1,data:JSON.stringify(e.data.group),type:"post",async:void 0===e.async||e.async,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.loadGroups=function(e){e.baseUrl&&(p=e.baseUrl),r.ajax({url:p+"/rest/ws/group/list",type:"get",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.getGroupInfo=function(e){var t=e.data.groupId?"?groupId="+e.data.groupId:"?clientGroupId="+e.data.clientGroupId;r.ajax({url:p+"/rest/ws/group/v2/info"+t,type:"get",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.addGroupMember=function(e){r.ajax({url:p+"/rest/ws/group/add/member",type:"POST",data:JSON.stringify(e.data.group),async:void 0===e.async||e.async,global:!1,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.removeGroupMember=function(e){r.ajax({url:p+"/rest/ws/group/remove/member",type:"POST",data:JSON.stringify(e.data),async:void 0===e.async||e.async,global:!1,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.groupLeave=function(e){r.ajax({url:p+"/rest/ws/group/left",type:"POST",data:JSON.stringify(e.data),async:void 0===e.async||e.async,global:!1,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.groupUpdate=function(e){r.ajax({url:p+"/rest/ws/group/update",type:"POST",data:JSON.stringify(e.data),async:void 0===e.async||e.async,global:!1,contentType:"application/json",success:function(t){e.success&&e.success(t,e.data)},error:function(t){e.error&&e.error(t)}})},r.isUserPresentInGroup=function(e){r.ajax({url:p+"/rest/ws/group/check/user?userId="+e.data.userId+"&clientGroupId="+e.data.clientGroupId,type:"get",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.groupUserCount=function(e){r.ajax({url:p+"/rest/ws/group/user/count?clientGroupIds="+e.data.clientGroupId,type:"get",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.groupDelete=function(e){r.ajax({url:p+"/rest/ws/group/left?clientGroupId="+e.data.clientGroupId,type:"GET",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.createUserFriendList=function(e){r.ajax({url:p+"/rest/ws/group/"+e.data.group.groupName+"/add/",type:"POST",async:void 0===e.async||e.async,global:!1,data:JSON.stringify(e.data.group.groupMemberList),contentType:"application/json",success:function(t){e.success&&(e.success(t),ALStorage.setFriendListGroupName(e.data.group.groupName))},error:function(t){e.error&&e.error(t)}})},r.createOpenFriendList=function(e){r.ajax({url:p+"/rest/ws/group/"+e.data.group.groupName+"/add/members",type:"POST",data:JSON.stringify(e.data.group),async:void 0===e.async||e.async,global:!1,contentType:"application/json",success:function(t){e.success&&(e.success(t),ALStorage.setFriendListGroupName(e.data.group.groupName),ALStorage.setFriendListGroupType(e.data.group.type))},error:function(t){e.error&&e.error(t)}})},r.getFriendList=function(e){var t="null"!==e.data.type?"/get?groupType=9":"/get";e.data.url=e.data.url?e.data.url:t,r.ajax({url:p+"/rest/ws/group/"+e.data.groupName+e.data.url,type:"GET",async:void 0===e.data.async||e.data.async,global:!1,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.removeUserFromFriendList=function(e){var t=e.group.type?"/remove?userId="+e.group.userId+"&groupType=9":"/remove?userId="+e.group.userId;r.ajax({url:p+"/rest/ws/group/"+e.group.groupName+t,type:"Post",contentType:"application/json",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.deleteFriendList=function(e){var t=e.group.type?"/delete?groupType=9":"/delete";r.ajax({url:p+"/rest/ws/group/"+e.group.groupName+t,type:"GET",contentType:"application/json",async:void 0===e.async||e.async,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.getUserDetail=function(e){r.ajax({url:p+"/rest/ws/user/v2/detail",data:JSON.stringify({userIdList:e.data}),type:"POST",async:void 0===e.async||e.async,global:!1,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.updateUserDetail=function(e){r.ajax({url:p+"/rest/ws/user/update",data:JSON.stringify(e.data),type:"POST",async:void 0===e.async||e.async,global:!1,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.updatePassword=function(e){r.ajax({url:p+"/rest/ws/user/update/password?oldPassword="+e.data.oldPassword+"&newPassword="+e.data.newPassword,type:"GET",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.getContactList=function(e){var t=e.baseUrl?e.baseUrl:p;r.ajax({url:t+e.url,type:"GET",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.userChatMute=function(e){r.ajax({url:p+"/rest/ws/user/chat/mute?userId="+e.data.userId+"&notificationAfterTime="+e.data.notificationAfterTime,type:"post",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.groupMute=function(e){var t={};t.clientGroupId=e.data.clientGroupId,t.notificationAfterTime=e.data.notificationAfterTime,r.ajax({url:p+"/rest/ws/group/user/update",type:"post",data:JSON.stringify(t),contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.syncMuteUserList=function(e){r.ajax({url:p+"/rest/ws/user/chat/mute/list",type:"get",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.blockUser=function(e){r.ajax({url:p+"/rest/ws/user/block?userId="+e.data.userId+"&block="+e.data.isBlock,type:"GET",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.unBlockUser=function(e){r.ajax({url:p+"/rest/ws/user/unblock?userId="+e.data.userId,type:"GET",async:void 0===e.async||e.async,global:!1,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.sendConversationCloseUpdate=function(e){var t="id="+e.conversationId;r.ajax({url:p+"/rest/ws/conversation/close",data:t,global:!1,type:"get",success:function(){},error:function(){}})},r.fileUpload=function(e){r.ajax({type:"GET",skipEncryption:!0,url:e.data.url,global:!1,data:"data="+(new Date).getTime(),crosDomain:!0,success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.sendAttachment=function(t){"awsS3Server"===u?e.Applozic.ALApiService.sendAttachmentToAWS(t):"googleCloud"===u?e.Applozic.ALApiService.sendAttachmentToCloud(t):e.Applozic.ALApiService.sendAttachmentToGoogleServer(t)},r.sendAttachmentToGoogleServer=function(e){var t=new XMLHttpRequest;r.ajax({type:"GET",skipEncryption:!0,url:void 0!==e.url?e.url:"https://applozic.appspot.com/rest/ws/aws/file/url",global:!1,data:"data="+(new Date).getTime(),crosDomain:!0,success:function(o){t.addEventListener("load",(function(t){var r=JSON.parse(this.responseText),o=e.data.messagePxy;r&&(o.fileMeta=r.fileMeta,Applozic.ALApiService.sendMessage({data:{message:o},success:function(t){e.success&&(t.fileMeta=r.fileMeta,e.success(t))},error:function(t){e.error&&e.error(t)}}))}));var n=new FormData,s=e.data.file;n.append("files[]",s),t.open("POST",o,!0),r.addRequestHeaders(t),t.send(n)},error:function(t){e.error&&e.error(t)}})},r.sendAttachmentToAWS=function(e){var t=new FormData,o=new XMLHttpRequest,n=p+"/rest/ws/upload/image";o.addEventListener("load",(function(t){var r=JSON.parse(this.responseText),o=e.data.messagePxy;r&&(o.fileMeta=r,Applozic.ALApiService.sendMessage({data:{message:o},success:function(t){e.success&&(t.fileMeta=r,e.success(t))},error:function(t){e.error&&e.error(t)}}))})),t.append("file",e.data.file),o.open("post",n,!0),r.addRequestHeaders(o),o.send(t)},r.sendAttachmentToCloud=function(e){var t=new FormData,o=new XMLHttpRequest,n=void 0!==e.cloudUploadUrl?e.cloudUploadUrl:"https://googleupload.applozic.com/files/upload/";o.addEventListener("load",(function(t){var r=JSON.parse(this.responseText),o=e.data.messagePxy;r&&(o.fileMeta=r.fileMeta,Applozic.ALApiService.sendMessage({data:{message:o},success:function(t){e.success&&(e.fileMeta=r.fileMeta,e.success(t))},error:function(t){e.error&&e.error(t)}}))})),t.append("files[]",e.data.file),o.open("post",n,!0),r.addRequestHeaders(o),o.send(t)},r.deleteFileMeta=function(e){r.ajax({url:e.data.url,skipEncryption:!0,type:"post",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.addMessageInbox=function(e){r.ajax({type:"GET",url:p+"/rest/ws/message/add/inbox",global:!1,data:"sender="+encodeURIComponent(e.data.sender)+"&messageContent="+encodeURIComponent(e.data.messageContent),contentType:"text/plain",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.conversationReadUpdate=function(e){r.ajax({url:p+"/rest/ws/message/read/conversation",data:e.data,global:!1,type:"get",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})},r.sendSubscriptionIdToServer=function(e){var t=e.data.subscriptionId;r.ajax({url:p+"/rest/ws/plugin/update/sw/id",skipEncryption:!0,type:"post",data:"registrationId="+t,success:function(e){},error:function(e,t,r){401===e.status&&(ALStorage.clearSessionStorageElements(),console.log("Please reload page."))}})},r.getTopicId=function(e){var t="id="+e.data.conversationId;r.ajax({url:p+"/rest/ws/conversation/topicId?"+t,type:"get",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})},r.getContactDisplayName=function(e){var t=e.data.userIdArray;if(t.length>0&&t[0]){for(var o="",n=t.filter((function(e,r){return t.indexOf(e)===r})),s=0;s<n.length;s++){var a=n[s];void 0===MCK_CONTACT_NAME_MAP[a]&&(o+="userIds="+encodeURIComponent(a)+"&")}o.lastIndexOf("&")===o.length-1&&(o=o.substring(0,o.length-1)),o&&r.ajax({url:p+"/rest/ws/user/info",data:o,global:!1,type:"get",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})}},r.getUserStatus=function(e){r.ajax({url:p+"/rest/ws/user/chat/status",type:"get",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})},r.fetchConversationByTopicId=function(e){var t="topic="+e.data.topicId;if(e.data.tabId)t+=""+e.data.isGroup=="true"?"&groupId="+e.data.tabId:"&userId="+encodeURIComponent(e.data.tabId);else{if(!e.data.clientGroupId)return!1;t+="&clientGroupId="+e.data.clientGroupId}e.data.pageSize&&(t+="&pageSize="+e.data.pageSize),r.ajax({url:p+"/rest/ws/conversation/get",data:t,type:"get",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})},r.getConversationId=function(e){r.ajax({url:p+"/rest/ws/conversation/id",global:!1,data:w.JSON.stringify(e.data),type:"post",contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})},r.registerClientApi=function(e){r.ajax({url:p+"/rest/ws/register/client",type:"post",data:JSON.stringify(e.data.userPxy),contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})},r.logout=function(e){r.ajax({url:p+"/rest/ws/device/logout",type:"post",async:void 0===e.async||e.async,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r.getUsersByRole=function(e){var t=d(e.data);r.ajax({url:p+"/rest/ws/user/v3/filter?"+t,global:!1,type:"get",contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.success(t)}})},r.pushNotificationLogout=function(e){r.ajax({url:p+"/rest/ws/device/logout",type:"post",async:void 0===e.async||e.async,contentType:"application/json",success:function(t){e.success&&e.success(t)},error:function(t){e.error&&e.error(t)}})},r}():console.log("ALApiService already defined.")}(window),function(e){"use strict";"undefined"==typeof ALSocket?e.Applozic.ALSocket=function(){var t,r={events:{}},o=null,n=null;r.stompClient=null;var s,a,i="";r.typingSubscriber=null,r.openGroupSubscriber=[];var c=[];r.mck_typing_status=0;var l="",u="socket.applozic.com",p="80";r.MCK_TOKEN,r.USER_DEVICE_KEY,r.USER_ENCRYPTION_KEY,r.APP_VERSION_CODE,r.AUTH_TOKEN;var d=new MckUtils,f=!0;return r.init=function(o,n,s){var a={};o&&(t=o),void 0!==n&&(r.MCK_TOKEN=n.token,r.APP_VERSION_CODE=n.appVersionCode,r.USER_DEVICE_KEY=n.deviceKey,(n.encryptionKey||parseInt(n.appVersionCode)>=e.Applozic.ALApiService.DEFAULT_ENCRYPTED_APP_VERSION)&&(r.USER_ENCRYPTION_KEY=n.userEncryptionKey),u=n.websocketUrl,p=void 0===n.websocketPort?d.startsWith(u,"https")?"15675":"15674":n.websocketPort,r.AUTH_TOKEN=n.authToken,a.socketUserId=n.appId||o,a.socketPassword=n.authToken),r.events=s,void 0!==u&&navigator.onLine&&e.WebSocket&&(u=u.replace("https://",""),l=new WebSocket("wss://"+u+":"+p+"/ws"),r.stompClient=Stomp.over(l),r.stompClient.heartbeat.outgoing=1e4,r.stompClient.heartbeat.incoming=0,r.stompClient.reconnect_delay=3e4,r.stompClient.debug=null,r.stompClient.onclose=function(){r.disconnect()},r.stompClient.connect(a.socketUserId,a.socketPassword,r.onConnect,r.onError,"/"),e.addEventListener("beforeunload",(function(e){var t;e.target.activeElement&&(t=e.target.activeElement.href),t&&0!==t.length||r.disconnect()})))},r.setOnline=function(){void 0!==e.Applozic.ALSocket&&e.Applozic.ALSocket.sendStatus(1)},r.checkConnected=function(e){r.stompClient.connected&&(s&&clearInterval(s),a&&clearInterval(a),s=setInterval((function(){r.connectToSocket(e)}),6e5),a=setInterval((function(){r.sendStatus(1)}),12e5)),r.connectToSocket(e)},r.connectToSocket=function(e){"function"==typeof r.events.connectToSocket&&r.events.connectToSocket(e)},r.stopConnectedCheck=function(){s&&clearInterval(s),a&&clearInterval(a),s="",a="",r.disconnect()},r.disconnect=function(){r.stompClient&&r.stompClient.connected&&(r.sendStatus(0),r.stompClient.disconnect(),"object"==typeof l&&(l.close(),l=""))},r.unsubscibeToTypingChannel=function(){r.stompClient&&r.stompClient.connected&&r.typingSubscriber&&(1===r.mck_typing_status&&r.sendTypingStatus(0,i),r.typingSubscriber.unsubscribe()),r.typingSubscriber=null},r.unsubscibeToNotification=function(){r.stompClient&&r.stompClient.connected&&(o&&o.unsubscribe(),n&&n.unsubscribe()),o=n=null},r.subscibeToTypingChannel=function(e){r.stompClient&&r.stompClient.connected?r.typingSubscriber=r.stompClient.subscribe("/topic/typing-"+t+"-"+e,r.onTypingStatus):r.reconnect()},r.subscribeToOpenGroup=function(e){if(console.log("adding subscription"),r.stompClient&&r.stompClient.connected){var o=r.stompClient.subscribe("/topic/group-"+t+"-"+e.contactId,r.onOpenGroupMessage);r.openGroupSubscriber.push(o.id),c[e.contactId]=o.id}else r.reconnect()},r.sendTypingStatus=function(e,o,n,s){if(r.mck_typing_status=o,r.stompClient&&r.stompClient.connected){if(1===e&&1===r.mck_typing_status&&r.stompClient.send("/topic/typing-"+t+"-"+i,{"content-type":"text/plain"},t+","+n+","+e),s){if(s===i&&e===r.mck_typing_status&&1===e)return;i=s,r.stompClient.send("/topic/typing-"+t+"-"+s,{"content-type":"text/plain"},t+","+n+","+e),setTimeout((function(){r.mck_typing_status=0}),6e4)}else 0===e&&r.stompClient.send("/topic/typing-"+t+"-"+i,{"content-type":"text/plain"},t+","+n+","+e);r.mck_typing_status=e}},r.onTypingStatus=function(e){"function"==typeof r.events.onTypingStatus&&r.events.onTypingStatus(e)},r.reconnect=function(){if(f){f=!1,r.unsubscibeToTypingChannel(),r.unsubscibeToNotification(),r.disconnect();var e={};e.token=r.MCK_TOKEN,e.deviceKey=r.USER_DEVICE_KEY,e.userEncryptionKey=r.USER_ENCRYPTION_KEY,e.websocketUrl=u,e.websocketPort=p,e.authToken=r.AUTH_TOKEN,r.init(t,e,r.events)}},r.onError=function(e){console.log("Error in channel notification. "+e),"function"==typeof r.events.onConnectFailed&&setTimeout((function(){r.events.onConnectFailed(),f=!0}),3e4)},r.sendStatus=function(e){r.stompClient&&r.stompClient.connected&&r.stompClient.send("/topic/status-v2",{"content-type":"text/plain"},r.MCK_TOKEN+","+r.USER_DEVICE_KEY+","+e)},r.sendMessageStatus=function(e,t,o){r.stompClient&&r.stompClient.connected&&r.stompClient.send("/topic/message-status",{"content-type":"text/plain"},o+","+e+","+t)},r.onConnect=function(){f=!0,r.stompClient.connected?((o||n)&&r.unsubscibeToNotification(),r.handleOnConnect()):setTimeout((function(){r.handleOnConnect()}),5e3),"function"==typeof r.events.onConnect&&r.events.onConnect()},r.handleOnConnect=function(){var e="/topic/"+r.MCK_TOKEN,t="/topic/encr-"+r.MCK_TOKEN;o=r.stompClient.subscribe(e,r.onStompMessage),r.USER_ENCRYPTION_KEY&&(n=r.stompClient.subscribe(t,r.onStompMessage)),r.sendStatus(1),r.checkConnected(!0)},r.onOpenGroupMessage=function(e){"function"==typeof r.events.onOpenGroupMessage&&r.events.onOpenGroupMessage(e)},r.onStompMessage=function(e){var t;null!=o&&o.id===e.headers.subscription?t=e.body:null!=n&&n.id===e.headers.subscription&&(t=d.decrypt(e.body,r.USER_ENCRYPTION_KEY)),r.onMessage(t)},r.onMessage=function(e){if(d.isJsonString(e)){var t=JSON.parse(e),o=t.type;if("function"==typeof r.events.onMessage&&r.events.onMessage(t),"APPLOZIC_04"===o||"MESSAGE_DELIVERED"===o)r.events.onMessageDelivered(t);else if("APPLOZIC_08"===o||"MT_MESSAGE_DELIVERED_READ"===o)r.events.onMessageRead(t);else if("APPLOZIC_05"===o)r.events.onMessageDeleted(t);else if("APPLOZIC_27"===o)r.events.onConversationDeleted(t);else if("APPLOZIC_11"===o)r.events.onUserConnect(t.message);else if("APPLOZIC_12"===o){var n=t.message.split(",")[0],s=t.message.split(",")[1];r.events.onUserDisconnect({userId:n,lastSeenAtTime:s})}else if("APPLOZIC_29"===o)r.events.onConversationReadFromOtherSource(t);else if("APPLOZIC_28"===o)r.events.onConversationRead(t);else if("APPLOZIC_16"===o){var a=t.message.split(":")[0];n=t.message.split(":")[1];r.events.onUserBlocked({status:a,userId:n})}else if("APPLOZIC_17"===o){a=t.message.split(":")[0],n=t.message.split(":")[1];r.events.onUserUnblocked({status:a,userId:n})}else if("APPLOZIC_18"===o)r.events.onUserActivated();else if("APPLOZIC_19"===o)r.events.onUserDeactivated();else{var i=t.message;if("APPLOZIC_03"===o)r.events.onMessageSentUpdate({messageKey:i.key});else if("APPLOZIC_01"===o||"MESSAGE_RECEIVED"===o){var c=alMessageService.getMessageFeed(i);r.events.onMessageReceived({message:c})}else if("APPLOZIC_02"===o){c=alMessageService.getMessageFeed(i);r.events.onMessageSent({message:c})}}}},r}():console.log("ALSocket already defined.")}(window);var mckNotificationUtils=new MckNotificationUtils,alNotificationService=new AlNotificationService;function AlNotificationService(){var e,t,r=this;r.init=function(t){e="boolean"==typeof t.swNotification&&t.swNotification,t.contactDisplayImage,t.notificationIconLink,"boolean"==typeof t.desktopNotification&&t.desktopNotification},r.unsubscribeToServiceWorker=function(){t&&navigator.serviceWorker.ready.then((function(e){t.unsubscribe().then((function(e){t=null,console.log("Unsubscribed to notification successfully")}))}))},r.sendSubscriptionIdToServer=function(){if(t){var e=t.endpoint.split("/").slice(-1)[0];e&&window.Applozic.ALApiService.sendSubscriptionIdToServer({data:{subscriptionId:e},success:function(e){},error:function(){}})}},r.subscribeToServiceWorker=function(){e&&"serviceWorker"in navigator&&(navigator.serviceWorker.register("./service-worker.js",{scope:"./"}),navigator.serviceWorker.ready.then((function(e){e.pushManager.subscribe({userVisibleOnly:!0}).then((function(e){console.log("The reg ID is:: ",e.endpoint.split("/").slice(-1)),t=e,r.sendSubscriptionIdToServer()}))})))}}function MckNotificationUtils(){var e=this,t=window.top,r=["granted","default","denied"],o=function(){var e=!1;try{e=!!(t.Notification||t.webkitNotifications||navigator.mozNotification||t.external&&void 0!==t.external.msIsSiteMode())}catch(e){}return e}(),n=Math.floor(10*Math.random()+1),s=function(){};e.permissionLevel=function(){var e;if(o)return t.Notification&&t.Notification.permissionLevel?e=t.Notification.permissionLevel():t.webkitNotifications&&t.webkitNotifications.checkPermission?e=r[t.webkitNotifications.checkPermission()]:t.Notification&&t.Notification.permission?e=t.Notification.permission:navigator.mozNotification?e="granted":t.external&&void 0!==t.external.msIsSiteMode()&&(e=t.external.msIsSiteMode()?"granted":"default"),e},e.requestPermission=function(e){var r,o=(r=e)&&r.constructor===Function?e:s;t.webkitNotifications&&t.webkitNotifications.checkPermission?t.webkitNotifications.requestPermission(o):t.Notification&&t.Notification.requestPermission&&t.Notification.requestPermission(o)},e.isChrome=function(){return/chrom(e|ium)/.test(t.navigator.userAgent.toLowerCase())},e.getNotification=function(r,o,s,a){var i;if(a&&(a.play(),setTimeout((function(){a.stop()}),1e3)),t.Notification){var c={icon:o,body:s};(i=new t.Notification(r,c)).onclick=function(){t.focus(),this.close()}}else t.webkitNotifications?(i=t.webkitNotifications.createNotification(o,r,s),a&&i.show(),e.isChrome()&&(i.onclick=function(){t.focus(),this.cancel()}),i.show(),setTimeout((function(){i.cancel()}),3e4)):navigator.mozNotification?(i=navigator.mozNotification.createNotification(r,s,o)).show():t.external&&t.external.msIsSiteMode()&&(t.external.msSiteModeClearIconOverlay(),t.external.msSiteModeSetIconOverlay(o,r),t.external.msSiteModeActivate(),i={ieVerification:n+1});return i},e.sendDesktopNotification=function(r,o,n,s){if("granted"!==e.permissionLevel()&&t.Notification.requestPermission(),"granted"===e.permissionLevel()){var a;a=s?e.getNotification(r,o,n,s):e.getNotification(r,o,n);var i=e.getWrapper(a);a&&!a.ieVerification&&a.addEventListener&&a.addEventListener("show",(function(){var e=i;t.setTimeout((function(){e.close()}),3e4)}))}},e.getWrapper=function(e){return{close:function(){e&&(e.close?e.close():e.cancel?e.cancel():t.external&&t.external.msIsSiteMode()&&e.ieVerification===n&&t.external.msSiteModeClearIconOverlay())}}}}var mckGroupUtils=new MckGroupUtils,mckGroupService=new MckGroupService;function MckGroupUtils(){this.GROUP_ROLE_MAP=[0,1,2,3],this.GROUP_TYPE_MAP=[1,2,5,6,7,9,10],this.CONVERSATION_STATUS_MAP=["DEFAULT","NEW","OPEN"],this.ROLE_MAP={0:"User",1:"Admin",2:"Moderator",3:"Member"},this.getDeletedAtTime=function(e){if("object"==typeof MCK_GROUP_MAP[e])return MCK_GROUP_MAP[e].deletedAtTime},this.leaveGroup=function(e){return"object"!=typeof e?"Unsupported Format. Please check format":"function"==typeof e.callback?void 0!==e.groupId&&""!==e.groupId||void 0!==e.clientGroupId&&""!==e.clientGroupId?(e.apzCallback=mckGroupLayout.onGroupLeft,mckGroupService.leaveGroup(e),"success"):void e.callback({status:"error",errorMessage:"GroupId or Client GroupId Required"}):"Callback Function Required"},this.initGroupTab=function(e,t){if("object"==typeof e){var r=e.users;return void 0===r||r.length<1?"Users List Required":r.length>MCK_GROUP_MAX_SIZE?"Users limit exceeds "+MCK_GROUP_MAX_SIZE+". Max number of users allowed is "+MCK_GROUP_MAX_SIZE+".":e.groupName?void 0===e.type?"Group type required":-1===mckGroupUtils.GROUP_TYPE_MAP.indexOf(e.type)?"Invalid group type":("function"==typeof t&&t(e),"success"):"Group name required"}return"Unsupported format. Please check format"},this.getGroup=function(e){return"object"==typeof MCK_GROUP_MAP[e]?MCK_GROUP_MAP[e]:void 0},this.getGroupByClientGroupId=function(e){return"object"==typeof MCK_CLIENT_GROUP_MAP[e]?MCK_CLIENT_GROUP_MAP[e]:void 0},this.addGroup=function(e){var t=e.name?e.name:e.id,r=[];e&&e.groupUsers&&e.groupUsers.forEach((function(e,t){e.userId&&(r[e.userId]=e)}));var o=void 0!==e.removedMembersId?e.removedMembersId:[],n={contactId:e.id.toString(),htmlId:mckContactUtils.formatContactId(""+e.id),displayName:t,value:e.id.toString(),adminName:e.adminId?e.adminId:e.adminName,type:e.type,members:e.membersId?e.membersId:e.membersName,imageUrl:e.imageUrl,users:r,userCount:e.userCount,removedMembersId:o,clientGroupId:e.clientGroupId,isGroup:!0,deletedAtTime:e.deletedAtTime,metadata:e.metadata};return MCK_GROUP_MAP[e.id]=n,e.clientGroupId&&(MCK_CLIENT_GROUP_MAP[e.clientGroupId]=n),n},this.createGroup=function(e){var t={contactId:e.toString(),htmlId:mckContactUtils.formatContactId(""+e),displayName:e.toString(),value:e.toString(),type:2,adminName:"",imageUrl:"",userCount:"",users:[],removedMembersId:[],clientGroupId:"",isGroup:!0,deletedAtTime:""};return MCK_GROUP_MAP[e]=t,t}}function MckGroupService(){var e,t,r,o,n=this;MCK_GROUP_ARRAY=new Array,n.addGroups=function(e){var t=e.data;MCK_GROUP_ARRAY.length=0,t&&t.forEach((function(e,t){if(void 0!==e.id){e=mckGroupUtils.addGroup(e);MCK_GROUP_ARRAY.push(e)}}))},n.removeGroupMember=function(e){return"object"!=typeof e?"Unsupported Format. Please check format":"function"==typeof e.callback?void 0!==e.groupId&&""!==e.groupId||void 0!==e.clientGroupId&&""!==e.clientGroupId?void 0===e.userId||""===e.userId?void e.callback({status:"error",errorMessage:"UserId required"}):(e.apzCallback=mckGroupLayout.onRemovedGroupMember,mckGroupService.removeGroupMemberFromChat(e),"success"):void e.callback({status:"error",errorMessage:"GroupId or clientGroupId required"}):"Callback function required"},n.createGroup=function(e,t){if("object"==typeof e){if("function"==typeof e.callback){var r=e.users;return void 0===r||r.length<1?void e.callback({status:"error",errorMessage:"Users list required"}):r.length>o?void e.callback({status:"error",errorMessage:"Users limit exceeds "+o+". Max number of users allowed is "+o+"."}):e.groupName?void 0===e.type||""===e.type?void e.callback({status:"error",errorMessage:"Group type required"}):-1===mckGroupUtils.GROUP_TYPE_MAP.indexOf(e.type)?void e.callback({status:"error",errorMessage:"Invalid group type"}):("function"==typeof t&&t(e),"success"):void e.callback({status:"error",errorMessage:"Group name required"})}return"Callback function required"}return"Unsupported Format. Please check format"},n.init=function(n){e=n.visitor,t=e?"guest":n.userId&&n.userId.toString().trim(),r=n.openGroupSettings,o=n.maxGroupSize},n.getGroupList=function(e){return"function"==typeof e.callback?(e.apzCallback=n.addGroups,n.loadGroups(e),"success"):"Callback Function Required"},n.loadGroups=function(e){var t=new Object;window.Applozic.ALApiService.loadGroups({baseUrl:MCK_BASE_URL,success:function(r){"success"===r.status?(t.status="success",t.data=r.response,e.apzCallback&&e.apzCallback(t)):t.status="error",e.callback&&e.callback(t)},error:function(){console.log("Unable to load groups. Please reload page."),t.status="error",e.callback&&e.callback(t),e.apzCallback&&e.apzCallback(t)}})},n.getGroupFeed=function(e){var t={};if("function"==typeof e.callback||"function"==typeof e.apzCallback){var r=new Object;if(e.groupId)t.groupId=e.groupId;else{if(!e.clientGroupId)return void("function"==typeof e.callback&&(r.status="error",r.errorMessage="GroupId or Client GroupId Required",e.callback(r)));t.clientGroupId=e.clientGroupId}e.conversationId&&(t.conversationId=e.conversationId),Applozic.ALApiService.getGroupInfo({data:t,success:function(t){if("success"===t.status){var r=t.response;if(r+""=="null"||"object"!=typeof r)t.status="error",t.errorMessage="GroupId not found";else{var o=mckGroupUtils.addGroup(r);t.status="success",t.data=o}}else"error"===t.status&&(t.status="error",t.errorMessage=t.errorResponse[0].description);e.callback&&e.callback(t),e.apzCallback&&("success"===t.status&&(t.data=r),e.apzCallback(t,e))},error:function(){console.log("Unable to load group. Please reload page."),r.status="error",r.errorMessage="Please reload page.",e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r,e)}})}},n.leaveGroup=function(e){var t={},r=new Object;if(e.groupId)t.groupId=e.groupId;else{if(!e.clientGroupId)return r.status="error",r.errorMessage="GroupId or Client GroupId Required",void(e.callback&&e.callback(r));t.clientGroupId=e.clientGroupId}Applozic.ALApiService.groupLeave({data:t,success:function(t){if("success"===t.status){if(e.clientGroupId){var o=mckGroupUtils.getGroupByClientGroupId(e.clientGroupId);"object"==typeof o&&(e.groupInfo=o.contactId)}r.status="success",r.data={groupId:e.groupId}}else r.status="error",r.errorMessage=t.errorResponse[0].description;e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r,{groupId:e.groupId})},error:function(){console.log("Unable to process your request. Please reload page."),r.status="error",r.errorMessage="",e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r)}})},n.removeGroupMemberFromChat=function(e){var t={},r=new Object;if(e.groupId)t.groupId=e.groupId;else{if(!e.clientGroupId)return r.status="error",r.errorMessage="GroupId or Client GroupId Required",void("function"==typeof e.callback&&e.callback(r));t.clientGroupId=e.clientGroupId}t.userId=e.userId,Applozic.ALApiService.removeGroupMember({data:t,success:function(t){if("success"===t.status){if(e.clientGroupId){var r=mckGroupUtils.getGroupByClientGroupId(e.clientGroupId);"object"==typeof r&&(e.groupId=r.contactId)}t.status="success"}else t.status="error",t.errorMessage=data.errorResponse[0].description;e.callback&&e.callback(t),e.apzCallback&&e.apzCallback(t,e)},error:function(){console.log("Unable to process your request. Please reload page."),r.status="error",r.errorMessage="",e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r),e.apzCallback(r)}})},n.addGroupMember=function(e){var t={},r=new Object;if(e.groupId)t.groupId=e.groupId;else{if(!e.clientGroupId)return void("function"==typeof e.callback&&e.callback(r));t.clientGroupId=e.clientGroupId}t.userId=e.userId,void 0!==e.role&&(t.role=e.role),Applozic.ALApiService.addGroupMember({data:{group:t},success:function(t){if("success"===t.status){if(e.clientGroupId){var o=mckGroupUtils.getGroupByClientGroupId(e.clientGroupId);"object"==typeof o&&(e.groupId=o.contactId)}r.status="success",r.data=t.response}else r.status="error",r.errorMessage=t.errorResponse[0].description;e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r,e)},error:function(){console.log("Unable to process your request. Please reload page."),r.status="error",r.errorMessage="",e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r)}})},n.updateGroupInfo=function(e){var t={},r=new Object;if(e.groupId)t.groupId=e.groupId;else{if(!e.clientGroupId)return void("function"==typeof e.callback&&(r.status="error",r.errorMessage="GroupId or Client GroupId Required",e.callback(r)));t.clientGroupId=e.clientGroupId}e.name&&(t.newName=e.name),e.imageUrl&&(t.imageUrl=e.imageUrl),e.users&&e.users.length>0&&(t.users=e.users),Applozic.ALApiService.groupUpdate({data:t,success:function(t,o){if("success"===t.status){if(e.clientGroupId)"object"==typeof(o=mckGroupLayout.getGroupByClientGroupId(e.clientGroupId))&&(e.groupId=o.contactId);r.status="success",r.data=t.response}else r.status="error",r.errorMessage=t.errorResponse[0].description;e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r,{groupId:e.groupId,groupInfo:o,users:e.users})},error:function(){console.log("Unable to process your request. Please reload page."),r.status="error",r.errorMessage="Unable to process your request. Please reload page.",e.callback&&e.callback(r),e.apzCallback&&e.apzCallback(r)}})},n.sendGroupMessage=function(e){if("object"==typeof e){var t=(e=mckUtils.extendObject(!0,{},message_default_options,e)).message;if(!e.groupId&&!e.clientGroupId)return"groupId or clientGroupId required";if(void 0===t||""===t)return"message field required";if(e.type>12)return"invalid message type";t=t&&t.trim();var r={type:e.messageType,contentType:e.type,message:t};if(e.groupId)r.groupId=e.groupId.toString().trim();else if(e.clientGroupId){if(void 0===mckGroupUtils.getGroupByClientGroupId(e.clientGroupId))return"group not found";r.clientGroupId=e.clientGroupId.toString().trim()}return mckMessageService.sendMessage(r),"success"}return"Unsupported format. Please check format"},n.getContactFromGroupOfTwo=function(e,r){for(var o,n=0;n<e.members.length;n++)if(o=""+e.members[n],t!==o)return"function"==typeof r&&r(o),o},n.addGroupFromMessage=function(e,t,r){var o=e.groupId,s=mckGroupUtils.getGroup(""+o);void 0===s&&(s=mckGroupUtils.createGroup(o),n.loadGroups({apzCallback:n.addGroups})),"function"==typeof r&&r(s,e,t)},n.isGroupDeleted=function(e,t){if(t){var r=mckGroupUtils.getDeletedAtTime(e);return void 0!==r&&r>0}return!1},n.loadGroupsCallback=function(e){var t=e.data;MCK_GROUP_ARRAY.length=0,t&&t.groupUsers&&t.groupUsers.forEach((function(e,t){if(void 0!==e.id){e=mckGroupUtils.addGroup(e);MCK_GROUP_ARRAY.push(e)}}))},n.getGroupDisplayName=function(e){if("object"==typeof MCK_GROUP_MAP[e]){var r=MCK_GROUP_MAP[e],o=r.displayName,s=[];if(7===r.type){var a=n.getContactFromGroupOfTwo(r);void 0!==a&&(alUserService.MCK_USER_DETAIL_MAP[a]?alUserService.MCK_USER_DETAIL_MAP[a]&&alUserService.MCK_USER_DETAIL_MAP[a].displayName&&(o=alUserService.MCK_USER_DETAIL_MAP[a].displayName):(s.push(a),window.Applozic.ALApiService.getUserDetail({data:s,success:function(e){e&&e.response&&e.response.length>0&&e.response.forEach((function(e,t){alUserService.MCK_USER_DETAIL_MAP[e.userId]=e,alUserService.MCK_USER_DETAIL_MAP[a]&&alUserService.MCK_USER_DETAIL_MAP[a].displayName&&(o=alUserService.MCK_USER_DETAIL_MAP[a].displayName)}))}})))}if(3===r.type&&-1!==o.indexOf(t)&&(o=o.replace(t,"").replace(":",""),"function"==typeof MCK_GETUSERNAME)){var i=MCK_GETUSERNAME(o);o=i||o}return o||5!==r.type||(o="Broadcast"),o||(o=r.contactId),o}return e},n.getGroupImage=function(e){return e?'<img src="'+e+'"/>':'<img src="'+MCK_BASE_URL+'/resources/sidebox/css/app/images/mck-icon-group.png"/>'},n.getGroupDefaultIcon=function(){return'<div class="mck-group-icon-default"></div>'},n.addMemberToGroup=function(e,t){return"object"==typeof e.members&&(-1===e.members.indexOf(t)&&e.members.push(t),"object"==typeof e.removedMembersId&&-1!==e.removedMembersId.indexOf(t)&&e.removedMembersId.splice(e.removedMembersId.indexOf(t),1),MCK_GROUP_MAP[e.contactId]=e),e},n.removeMemberFromGroup=function(e,t){return"object"!=typeof e.removedMembersId||e.removedMembersId.length<1?(e.removedMembersId=[],e.removedMembersId.push(t)):-1===e.removedMembersId.indexOf(t)&&e.removedMembersId.push(t),MCK_GROUP_MAP[e.contactId]=e,e},n.authenticateGroupUser=function(e){var r=!1;if(!n.isGroupLeft(e)&&e.members.length>0)for(var o=0;o<e.members.length;o++)if(t===""+e.members[o])return r=!0,!0;return r},n.isAppendOpenGroupContextMenu=function(e){return 0!==r.deleteChatAccess&&(!!mckGroupService.authenticateGroupUser(e)&&(e.adminName===t||2===r.deleteChatAccess))},n.isGroupLeft=function(e){var r=!1;return e.removedMembersId&&e.removedMembersId.length>0&&e.removedMembersId.forEach((function(e,o){e===t&&(r=!0)})),r}}var alUserService=new AlUserService;function AlUserService(){var e=this;e.MCK_USER_DETAIL_MAP=[],e.MCK_BLOCKED_TO_MAP=[];new Array;e.updateUserStatus=function(e,t){if("object"==typeof alUserService.MCK_USER_DETAIL_MAP[e.userId]){var r=alUserService.MCK_USER_DETAIL_MAP[e.userId];0===e.status?(r.connected=!1,r.lastSeenAtTime=e.lastSeenAtTime):1===e.status&&(r.connected=!0)}else{var o=new Array;o.push(e.userId),"function"==typeof t&&t(o)}},e.getUserDetail=function(e){return"object"==typeof alUserService.MCK_USER_DETAIL_MAP[e]?alUserService.MCK_USER_DETAIL_MAP[e]:void 0},e.loadUserProfile=function(t){if(void 0!==t){"string"!=typeof t&&(t=String(t));var r=[],o=""+t.split(",")[0];r.push(o),e.loadUserProfiles(r)}},e.loadUserProfiles=function(e,t){"function"==typeof t&&t(e,[])},e.getUserStatus=function(e,t){var r=new Object;window.Applozic.ALApiService.getUserStatus({success:function(o){o.users.length>0&&([],"function"==typeof t&&t(o)),r.status="success",r.data=o,e.callback&&e.callback(r)},error:function(){r.status="error",e.callback&&e.callback(r)}})},e.blockUser=function(e,t,r){if(e&&void 0!==t){var o="userId="+e+"&block="+t;mckUtils.ajax({url:MCK_BASE_URL+"/rest/ws/user/block",type:"get",data:o,encryptionKey:window.Applozic.ALApiService.getEncryptionKey(),success:function(o){"object"==typeof o&&"success"===o.status&&(alUserService.MCK_BLOCKED_TO_MAP[e]=t,"function"==typeof r&&r(e))},error:function(){}})}}}var alFileService=new AlFileService;function AlFileService(){var e,t,r,o,n=this;n.init=function(e){t=e.fileBaseUrl,btoa(e.userId+":"+e.deviceKey),e.deviceKey},n.get=function(t){t.appId,e=t.customUploadUrl,t.fileupload,r=t.mapStaticAPIkey,t.accessToken,t.appModuleName,o=t.genereateCloudFileUrl},n.deleteFileMeta=function(e){window.Applozic.ALApiService.deleteFileMeta({data:{blobKey:e,url:t+"/rest/ws/aws/file/delete?key="+e},success:function(e){console.log(e)},error:function(){}})},n.getFilePreviewPath=function(e){return"object"==typeof e?'<a href="'+t+"/rest/ws/aws/file/"+e.blobKey+'" target="_blank">'+e.name+"</a>":""},n.getFilePreviewSize=function(e){return e?e>1048576?"("+parseInt(e/1048576)+" MB)":e>1024?"("+parseInt(e/1024)+" KB)":"("+parseInt(e)+" B)":""},n.getFileurl=function(r){var o;return"object"==typeof r.fileMeta?r.fileMeta.hasOwnProperty("url")?-1!==r.fileMeta.url.indexOf("www.googleapis.com")?(n.generateCloudUrl(r.fileMeta.blobKey,(function(e){o=e})),o):""+r.fileMeta.url:r.fileMeta.thumbnailUrl==="thumbnail_"+r.fileMeta.name?e+"/files/"+r.fileMeta.name:t+"/rest/ws/aws/file/"+r.fileMeta.blobKey:""},n.generateCloudUrl=function(e,t){var r=o.replace("{key}",e),n=window.Applozic.ALApiService.getAttachmentHeaders();mckUtils.ajax({type:"get",async:!1,skipEncryption:!0,headers:n,url:r,success:function(e){"function"==typeof t&&t(e)},error:function(e){console.log("error while getting token"+e)}})},n.getFilePath=function(t){if(2===t.contentType)try{var o=JSON.parse(t.message);if(o.lat&&o.lon)return'<a href="http://maps.google.com/maps?z=17&t=m&q=loc:'+o.lat+","+o.lon+'" target="_blank"><img src="https://maps.googleapis.com/maps/api/staticmap?zoom=17&size=200x150&center='+o.lat+","+o.lon+"&maptype=roadmap&markers=color:red|"+o.lat+","+o.lon+"&key="+r+'"/></a>'}catch(e){if(-1!==t.message.indexOf(","))return'<a href="http://maps.google.com/maps?z=17&t=m&q=loc:'+t.message+'" target="_blank"><img src="https://maps.googleapis.com/maps/api/staticmap?zoom=17&size=200x150&center='+t.message+"&maptype=roadmap&markers=color:red|"+t.message+"&key="+r+'" /></a>'}var s,a;return"object"==typeof t.fileMeta?-1!==t.fileMeta.contentType.indexOf("image")?-1!==t.fileMeta.contentType.indexOf("svg")?'<a href="#" role="link" target="_self" class="file-preview-link fancybox-media imageview" data-type="'+t.fileMeta.contentType+'" data-url="'+n.getFileurl(t)+'" data-name="'+t.fileMeta.name+'"><img src="'+n.getFileurl(t)+'" area-hidden="true"></img></a>':5===t.contentType?'<a href="#" role="link" target="_self" class="file-preview-link fancybox-media imageview" data-type="'+t.fileMeta.contentType+'" data-url="'+t.fileMeta.blobKey+'" data-name="'+t.fileMeta.name+'"><img src="'+t.fileMeta.blobKey+'" area-hidden="true"></img></a>':t.fileMeta.hasOwnProperty("url")?-1!==t.fileMeta.url.indexOf("www.googleapis.com")?(n.generateCloudUrl(t.fileMeta.thumbnailBlobKey,(function(e){s=e})),'<a href="#" role="link" target="_self" class="file-preview-link fancybox-media imageview" data-type="'+t.fileMeta.contentType+'" data-url="" data-blobKey="'+t.fileMeta.blobKey+'" data-name="'+t.fileMeta.name+'"><img src="'+s+'" area-hidden="true"></img></a>'):'<a href="#" role="link" target="_self" class="file-preview-link fancybox-media imageview" data-type="'+t.fileMeta.contentType+'" data-url="'+n.getFileurl(t)+'" data-name="'+t.fileMeta.name+'"><img src="'+t.fileMeta.thumbnailUrl+'" area-hidden="true"></img></a>':t.fileMeta.thumbnailUrl==="thumbnail_"+t.fileMeta.name?'<a href="#" role="link" target="_self" class="file-preview-link fancybox-media imageview" data-type="'+t.fileMeta.contentType+'" data-url="'+n.getFileurl(t)+'" data-name="'+t.fileMeta.name+'"><img src="'+e+"/files/thumbnail_"+t.fileMeta.name+'" area-hidden="true"></img></a>':'<a href="#" role="link" target="_self" class="file-preview-link fancybox-media imageview" data-type="'+t.fileMeta.contentType+'" data-url="'+n.getFileurl(t)+'" data-name="'+t.fileMeta.name+'"><img src="'+t.fileMeta.thumbnailUrl+'" area-hidden="true"></img></a>':-1!==t.fileMeta.contentType.indexOf("video")?t.fileMeta.hasOwnProperty("url")&&-1!==t.fileMeta.url.indexOf("www.googleapis.com")?(n.generateCloudUrl(t.fileMeta.blobKey,(function(e){a=e})),'<a href="#" target="_self"><video controls class="mck-video-player" onplay="alFileService.updateAudVidUrl(this);" data-cloud-service="google_cloud" data-blobKey="'+t.fileMeta.blobKey+'"><source src="'+a+'" type="video/mp4"><source src="'+a+'" type="video/ogg"></video>'):'<a href= "#" target="_self"><video controls class="mck-video-player"><source src="'+n.getFileurl(t)+'" type="video/mp4"><source src="'+n.getFileurl(t)+'" type="video/ogg"></video></a>':-1!==t.fileMeta.contentType.indexOf("audio")?t.fileMeta.hasOwnProperty("url")&&-1!==t.fileMeta.url.indexOf("www.googleapis.com")?(n.generateCloudUrl(t.fileMeta.blobKey,(function(e){a=e})),'<a href="#" target="_self"><audio controls class="mck-audio-player" onplay="alFileService.updateAudVidUrl(this);" data-cloud-service="google_cloud" data-blobKey="'+t.fileMeta.blobKey+'"><source src="'+a+'" type="audio/ogg"><source src="'+a+'" type="audio/mpeg"></audio><p class="mck-file-tag"></p></a>'):'<a href="#" target="_self"><audio controls class="mck-audio-player"><source src="'+n.getFileurl(t)+'" type="audio/ogg"><source src="'+n.getFileurl(t)+'" type="audio/mpeg"></audio><p class="mck-file-tag"></p></a>':'<a href="#" role="link" class="file-preview-link" target="_self"></a>':""},n.updateAudVidUrl=function(e){var t=e.dataset.blobkey,r=(new Date).getTime(),o=e.currentSrc;r>=1e3*n.fetchQueryString("Expires",o)&&(n.generateCloudUrl(t,(function(e){getUrl=e})),e.src=getUrl)},this.fetchQueryString=function(e,t){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var r=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(t);return null==r&&console.log("The parameter is null for the searchedquery"),r[1]},n.getFileAttachment=function(e){if("object"==typeof e.fileMeta)return(-1!==e.fileMeta.contentType.indexOf("image")||-1!==e.fileMeta.contentType.indexOf("audio")||-1!==e.fileMeta.contentType.indexOf("video"))&&e.fileMeta.hasOwnProperty("url")&&-1!==e.fileMeta.url.indexOf("www.googleapis.com")?'<a href="javascript:void(0);" role="link" target="_self"  class="file-preview-link" data-blobKey="'+e.fileMeta.blobKey+'" data-cloud-service="google_cloud"><span class="file-detail mck-image-download"><span class="mck-file-name"><span class="mck-attachement-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20"><g data-name="Group 122"><path fill="none" d="M0 0h24v24H0z" data-name="Rectangle 1"/><path d="M19.00483767 16.29529691l-11.90272845-.0417193a4.358 4.358 0 0 1-4.32607928-4.32607929A4.259 4.259 0 0 1 7.0483691 7.65515915l10.48639356.03394113v.70710678L7.07241074 8.3382243a3.61826547 3.61826547 0 1 0 .00141421 7.2365308l11.89990002.03889087a2.647 2.647 0 0 0 2.68700577-2.68700576 2.688 2.688 0 0 0-2.70680476-2.70680476l-10.15476048-.0615183a1.774 1.774 0 0 0-1.75998878 1.75998879 1.8 1.8 0 0 0 1.76776695 1.76776695l8.82681395.02899138v.70710678l-8.81832866-.02333453a2.491 2.491 0 0 1-2.47840927-2.47840926 2.46 2.46 0 0 1 2.46426713-2.46426714l10.18375186.0311127a3.462 3.462 0 0 1 3.4400745 3.4400745 3.424 3.424 0 0 1-3.4202755 3.3679496z" data-name="Path 1"/></g></svg></span>&nbsp;'+e.fileMeta.name+'</span>&nbsp;<span class="file-size">'+alFileService.getFilePreviewSize(e.fileMeta.size)+"</span></span></a>":'<a href="'+n.getFileurl(e)+'" role="link" target="_self"  class="file-preview-link"><span class="file-detail mck-image-download"><span class="mck-file-name"><span class="mck-attachement-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20"><g data-name="Group 122"><path fill="none" d="M0 0h24v24H0z" data-name="Rectangle 1"/><path d="M19.00483767 16.29529691l-11.90272845-.0417193a4.358 4.358 0 0 1-4.32607928-4.32607929A4.259 4.259 0 0 1 7.0483691 7.65515915l10.48639356.03394113v.70710678L7.07241074 8.3382243a3.61826547 3.61826547 0 1 0 .00141421 7.2365308l11.89990002.03889087a2.647 2.647 0 0 0 2.68700577-2.68700576 2.688 2.688 0 0 0-2.70680476-2.70680476l-10.15476048-.0615183a1.774 1.774 0 0 0-1.75998878 1.75998879 1.8 1.8 0 0 0 1.76776695 1.76776695l8.82681395.02899138v.70710678l-8.81832866-.02333453a2.491 2.491 0 0 1-2.47840927-2.47840926 2.46 2.46 0 0 1 2.46426713-2.46426714l10.18375186.0311127a3.462 3.462 0 0 1 3.4400745 3.4400745 3.424 3.424 0 0 1-3.4202755 3.3679496z" data-name="Path 1"/></g></svg></span>&nbsp;'+e.fileMeta.name+'</span>&nbsp;<span class="file-size">'+alFileService.getFilePreviewSize(e.fileMeta.size)+"</span></span></a>"},n.getFileIcon=function(e){return e.fileMetaKey&&"object"==typeof e.fileMeta?-1!==e.fileMeta.contentType.indexOf("image")?'<span class="mck-icon--camera"><svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24"><circle cx="12" cy="12" r="3.2" fill="rgba(38,50,56,.52)"/><path d="M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="rgba(38,50,56,.52)"/><path d="M0 0h24v24H0z" fill="none"/></svg></span>&nbsp;<span>Image</span>':-1!==e.fileMeta.contentType.indexOf("audio")?'<span class="mck-attachement-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20"><g data-name="Group 122"><path fill="none" d="M0 0h24v24H0z" data-name="Rectangle 1"/><path d="M19.00483767 16.29529691l-11.90272845-.0417193a4.358 4.358 0 0 1-4.32607928-4.32607929A4.259 4.259 0 0 1 7.0483691 7.65515915l10.48639356.03394113v.70710678L7.07241074 8.3382243a3.61826547 3.61826547 0 1 0 .00141421 7.2365308l11.89990002.03889087a2.647 2.647 0 0 0 2.68700577-2.68700576 2.688 2.688 0 0 0-2.70680476-2.70680476l-10.15476048-.0615183a1.774 1.774 0 0 0-1.75998878 1.75998879 1.8 1.8 0 0 0 1.76776695 1.76776695l8.82681395.02899138v.70710678l-8.81832866-.02333453a2.491 2.491 0 0 1-2.47840927-2.47840926 2.46 2.46 0 0 1 2.46426713-2.46426714l10.18375186.0311127a3.462 3.462 0 0 1 3.4400745 3.4400745 3.424 3.424 0 0 1-3.4202755 3.3679496z" data-name="Path 1"/></g></svg></span>&nbsp;<span>Audio</span>':-1!==e.fileMeta.contentType.indexOf("video")?'<span class="mck-icon--video"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22"><path fill="rgba(38,50,56,.52)" d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l2.29 2.29c.63.63 1.71.18 1.71-.71V8.91c0-.89-1.08-1.34-1.71-.71L17 10.5z"/></svg></span>&nbsp;<span class="mck-icon-video-text">Video</span>':'<span class="mck-attachement-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20"><g data-name="Group 122"><path fill="none" d="M0 0h24v24H0z" data-name="Rectangle 1"/><path d="M19.00483767 16.29529691l-11.90272845-.0417193a4.358 4.358 0 0 1-4.32607928-4.32607929A4.259 4.259 0 0 1 7.0483691 7.65515915l10.48639356.03394113v.70710678L7.07241074 8.3382243a3.61826547 3.61826547 0 1 0 .00141421 7.2365308l11.89990002.03889087a2.647 2.647 0 0 0 2.68700577-2.68700576 2.688 2.688 0 0 0-2.70680476-2.70680476l-10.15476048-.0615183a1.774 1.774 0 0 0-1.75998878 1.75998879 1.8 1.8 0 0 0 1.76776695 1.76776695l8.82681395.02899138v.70710678l-8.81832866-.02333453a2.491 2.491 0 0 1-2.47840927-2.47840926 2.46 2.46 0 0 1 2.46426713-2.46426714l10.18375186.0311127a3.462 3.462 0 0 1 3.4400745 3.4400745 3.424 3.424 0 0 1-3.4202755 3.3679496z" data-name="Path 1"/></g></svg></span>&nbsp;<span>File</span>':""},n.downloadfile=function(){var e=n.getFileurl(msg),t=document.createElement("a");t.download=thefilename,t.setAttribute("href",e);var r="data:text/csv;charset=utf-8;base64,"+someb64data;t.href=r,document.body.appendChild(t),t.click(),document.body.removeChild(t)}}!function(e){"use strict";"undefined"==typeof AlCustomService?e.Applozic.AlCustomService=function(){var t={logout:function(){void 0!==e.Applozic.ALSocket&&(e.Applozic.ALApiService.AUTH_TOKEN=null,e.Applozic.ALApiService.setAjaxHeaders("","","","",""),e.Applozic.ALApiService.setEncryptionKeys(null,null),e.Applozic.ALSocket.disconnect(),ALStorage.clearSessionStorageElements())}};return t}():console.log("ALCustomService already defined.")}(window);var mckLabels=new MckLabels;function MckLabels(){this.getLabels=function(){return{"conversations.title":"Conversations","start.new":"Start New","search.contacts":"Contacts","search.groups":"Groups","empty.groups":"No groups yet!","empty.contacts":"No contacts yet!","empty.messages":"No messages yet!","no.more.messages":"No more messages!","empty.conversations":"No conversations yet!","no.more.conversations":"No more conversations!","search.placeholder":"Search...","location.placeholder":"Enter a location","create.group.title":"Create Group","members.title":"Members","add.members.title":"Add Member","remove.member":"Remove Member","change.role":"Change Role","group.info.update":"Update","group.info.updating":"Updating...","add.group.icon":"Add Group Icon","group.deleted":"Group has been deleted","change.group.icon":"Change Group Icon","group.title":"Group Title","group.type":"Group Type","group.create.submit":"Creating Group...",blocked:"You have blocked this user","group.chat.disabled":"You are no longer part of this group!","block.user.alert":"Are you sure you want to block this user?","unblock.user.alert":"Are you sure you want to unblock this user?","exit.group.alert":"Are you sure you want to exit this group?","remove.member.alert":"Are you sure you want to remove this member?","clear.messages.alert":"Are you sure you want to delete all the conversation?",typing:"typing...","is.typing":"is typing...",online:"Online","clear.messages":"Clear Messages",delete:"Delete",reply:"Reply",forward:"Forward",copy:"Copy","block.user":"Block User","unblock.user":"Unblock User","group.info.title":"Group Info","exit.group":"Exit Group","location.share.title":"Location Sharing","my.location":"My Location",send:"Send","send.message":"Send Message",smiley:"Smiley",close:"Close",edit:"Edit",save:"Save","file.attachment":"Files & Photos","file.attach.title":"Attach File","last.seen":"Last seen","last.seen.on":"Last seen on",hour:" hour",min:" min",yesterday:"yesterday",hours:" hours",mins:" mins","time.format.AM":"AM","time.format.PM":"PM","time.format.am":"am","time.format.pm":"pm","user.delete":"This user has been deleted",ago:"ago",admin:"Admin",user:"User",moderator:"Moderator",member:"Member",public:"Public",private:"Private",open:"Open",you:"You",userIdPattern:"[^!$%^&*()]+",charsNotAllowedMessage:"Following characters are not allowed: !$%^&*()","group.metadata":{CREATE_GROUP_MESSAGE:":adminName created group :groupName",REMOVE_MEMBER_MESSAGE:":adminName removed :userName",ADD_MEMBER_MESSAGE:":adminName added :userName",JOIN_MEMBER_MESSAGE:":userName joined",GROUP_NAME_CHANGE_MESSAGE:"Group name changed to :groupName",GROUP_ICON_CHANGE_MESSAGE:"Group icon changed",GROUP_LEFT_MESSAGE:":userName left",DELETED_GROUP_MESSAGE:":adminName deleted group",GROUP_USER_ROLE_UPDATED_MESSAGE:":userName is :role now",GROUP_META_DATA_UPDATED_MESSAGE:"",ALERT:"",HIDE:""}}},this.setLabels=function(){$applozic("#mck-conversation-title").html(MCK_LABELS["conversations.title"]).attr("title",MCK_LABELS["conversations.title"]),$applozic("#mck-msg-new, #mck-sidebox-search .mck-box-title").html(MCK_LABELS["start.new"]).attr("title",MCK_LABELS["start.new"]),$applozic("#mck-contact-search-tab strong").html(MCK_LABELS["search.contacts"]).attr("title",MCK_LABELS["search.contacts"]),$applozic("#mck-group-search-tab strong").html(MCK_LABELS["search.groups"]).attr("title",MCK_LABELS["search.groups"]),$applozic("#mck-contact-search-input, #mck-group-search-input, #mck-group-member-search").attr("placeholder",MCK_LABELS["search.placeholder"]),$applozic("#mck-loc-address").attr("placeholder",MCK_LABELS["location.placeholder"]),$applozic("#mck-no-conversations").html(MCK_LABELS["empty.conversations"]),$applozic("#mck-no-messages").html(MCK_LABELS["empty.messages"]),$applozic("#mck-no-more-conversations").html(MCK_LABELS["no.more.conversations"]),$applozic("#mck-no-more-messages").html(MCK_LABELS["no.more.messages"]),$applozic("#mck-no-search-contacts").html(MCK_LABELS["empty.contacts"]),$applozic("#mck-no-search-groups").html(MCK_LABELS["empty.groups"]),$applozic("#mck-new-group, #mck-group-create-tab .mck-box-title, #mck-btn-group-create").html(MCK_LABELS["create.group.title"]).attr("title",MCK_LABELS["create.group.title"]),$applozic("#mck-gc-overlay-label").html(MCK_LABELS["add.group.icon"]),$applozic("#mck-msg-error").html(MCK_LABELS["group.deleted"]),$applozic("#mck-gc-title-label").html(MCK_LABELS["group.title"]),$applozic("#mck-gc-type-label").html(MCK_LABELS["group.type"]),$applozic("#mck-group-info-btn, #mck-group-info-tab .mck-box-title").html(MCK_LABELS["group.info.title"]).attr("title",MCK_LABELS["group.info.title"]),$applozic("#mck-gi-overlay-label").html(MCK_LABELS["change.group.icon"]),$applozic("#mck-group-member-title").html(MCK_LABELS["members.title"]).attr("title",MCK_LABELS["members.title"]),$applozic("#mck-group-add-member .blk-lg-9, #mck-gm-search-box .mck-box-title").html(MCK_LABELS["add.members.title"]).attr("title",MCK_LABELS["add.members.title"]),$applozic("#mck-btn-group-update").html(MCK_LABELS["group.info.update"]).attr("title",MCK_LABELS["group.info.update"]),$applozic("#mck-leave-group-btn, #mck-btn-group-exit").html(MCK_LABELS["exit.group"]).attr("title",MCK_LABELS["exit.group"]),$applozic("#mck-btn-leave-group, #mck-btn-group-exit").html(MCK_LABELS["exit.group"]).attr("title",MCK_LABELS["exit.group"]),$applozic("#mck-typing-label").html(MCK_LABELS.typing),$applozic("#mck-btn-clear-messages").html(MCK_LABELS["clear.messages"]).attr("title",MCK_LABELS["clear.messages"]),$applozic("#mck-block-button").html(MCK_LABELS["block.user"]).attr("title",MCK_LABELS["block.user"]),$applozic("#mck-loc-box .mck-box-title, #mck-share-loc-label").html(MCK_LABELS["location.share.title"]).attr("title",MCK_LABELS["location.share.title"]),$applozic("#mck-btn-loc").attr("title",MCK_LABELS["location.share.title"]),$applozic("#mck-file-up-label").html(MCK_LABELS["file.attachment"]),$applozic("#mck-file-up").attr("title",MCK_LABELS["file.attachment"]),$applozic(".mck-file-attach-label").attr("title",MCK_LABELS["file.attach.title"]),$applozic("#mck-my-loc").html(MCK_LABELS["my.location"]).attr("title",MCK_LABELS["my.location"]),$applozic("#mck-btn-close-loc-box").html(MCK_LABELS.close).attr("title",MCK_LABELS.close),$applozic("#mck-loc-submit").html(MCK_LABELS.send).attr("title",MCK_LABELS.send),$applozic("#mck-msg-sbmt").attr("title",MCK_LABELS["send.message"]),$applozic("#mck-btn-smiley").attr("title",MCK_LABELS.smiley),$applozic("#mck-group-name-save").attr("title",MCK_LABELS.save),$applozic("#mck-btn-group-icon-save").attr("title",MCK_LABELS.save),$applozic("#mck-group-name-edit").attr("title",MCK_LABELS.edit),$applozic("#mck-contact-search-input").attr("title",MCK_LABELS.charsNotAllowedMessage)}}var alMessageService=new AlMessageService;function AlMessageService(){var e,t,r,o=this;o.init=function(o){r=o.fileBaseUrl,e=o.visitor,t=e?"guest":o&&o.userId&&o.userId.toString().trim()},o.getConversation=function(e){o.getTopicId({conversationId:e.conversationId},(function(e){mckMessageLayout.populateMessage(e.messageType,e.message,e.notifyUser)}))},o.addWelcomeMessage=function(e){return"object"!=typeof e?"Unsupported format. Please check format":void 0===e.sender||""===e.sender?"Sender Field Required":void 0===e.messageContent||""===e.messageContent?"Message Content Required":void mckMessageService.sendWelcomeMessage(e)},o.getUserIdFromMessage=function(e){var t=e.to;return t.lastIndexOf(",")===t.length-1&&(t=t.substring(0,t.length-1)),t.split(",")},o.isValidMetaData=function(e){return!e.metadata||"HIDDEN"!==e.metadata.category&&"ARCHIVE"!==e.metadata.category},o.getStatusIconName=function(e){return 7===e.type||6===e.type||4===e.type||0===e.type?"":5===e.status?"mck-icon-read":4===e.status?"mck-icon-delivered":3===e.type||5===e.type||1===e.type&&(0===e.source||1===e.source)?"mck-icon-sent":""},o.addMessageToTab=function(e,t,r){var o={to:e.to,groupId:e.groupId,deviceKey:e.deviceKey,contentType:e.contentType,message:e.message,conversationId:e.conversationId,topicId:e.topicId,sendToDevice:!0,createdAtTime:(new Date).getTime(),key:e.key,storeOnDevice:!0,sent:!1,read:!0,metadata:e.metadata?e.metadata:""};o.type=e.type?e.type:5,e.fileMeta&&(o.fileMeta=e.fileMeta),"function"==typeof r&&r(o,t)},o.getMessages=function(e){var t={};e.startTime&&(t.endTime=e.startTime),void 0!==e.userId&&""!==e.userId?(e.isGroup?t.groupId=e.userId:t.userId=e.userId,t.pageSize=30,(IS_MCK_TOPIC_HEADER||IS_MCK_TOPIC_BOX)&&e.conversationId&&(t.conversationId=e.conversationId,void 0===MCK_TAB_CONVERSATION_MAP[e.userId]&&(t.conversationReq=!0))):t.mainPageSize=100,window.Applozic.ALApiService.getMessages({data:t,success:e.callback,error:e.callback})},o.getMessageList=function(e,t){var r=e.id,o={},n={};e.startTime&&(o.endTime=e.startTime),void 0!==e.clientGroupId&&""!==e.clientGroupId?(e.pageSize?o.pageSize=e.pageSize:o.pageSize=50,o.clientGroupId=e.clientGroupId,n={clientGroupId:e.clientGroupId}):void 0!==r&&""!==r?(e.pageSize?o.pageSize=e.pageSize:o.pageSize=50,""+e.isGroup=="true"?o.groupId=r:o.userId=r,n={id:r}):(e.mainPageSize?o.mainPageSize=e.pageSize:o.mainPageSize=50,n={id:""}),e.topicId&&(r||e.clientGroupId)&&(e.conversationId&&(o.conversationId=e.conversationId),e.topicId&&(n.topicId=e.topicId)),window.Applozic.ALApiService.getMessages({data:o,success:function(r){var o=r.data;if(n.status="success",void 0===o.message||0===o.message.length)n.messages=[];else{var s=o.message,a=new Array;s.forEach((function(e,r){"function"==typeof t&&t(e)})),n.messages=a}o.groupFeeds.length>0&&(n.id=o.groupFeeds[0].id),e.callback(o)},error:function(t){n.status="error",e.callback(n)}})},o.getReplyMessageByKey=function(e){return void 0===ALStorage.getMessageByKey(e)&&window.Applozic.ALApiService.updateReplyMessage({data:{key:e},async:!1,success:function(e){ALStorage.updateMckMessageArray(e)}}),ALStorage.getMessageByKey(e)},o.sendDeliveryUpdate=function(e){window.Applozic.ALApiService.sendDeliveryUpdate({data:{key:e.pairedMessageKey},success:function(){},error:function(){}})},o.sendReadUpdate=function(e){void 0!==e&&""!==e&&window.Applozic.ALApiService.sendReadUpdate({data:{key:e},success:function(){},error:function(){}})},o.fetchConversationByTopicId=function(e,t){window.Applozic.ALApiService.fetchConversationByTopicId({data:e,success:function(e){if("object"==typeof e&&"success"===e.status){var r=e.response;if(r&&r.length>0&&r.forEach((function(e,t){if(MCK_CONVERSATION_MAP[e.id]=e,MCK_TOPIC_CONVERSATION_MAP[e.topicId]=[e.id],e.topicDetail)try{MCK_TOPIC_DETAIL_MAP[e.topicId]=JSON.parse(e.topicDetail)}catch(e){w.console.log("Incorect Topic Detail!")}if(params.tabId&&void 0!==MCK_TAB_CONVERSATION_MAP[params.tabId]){var r=MCK_TAB_CONVERSATION_MAP[params.tabId];r.push(e),MCK_TAB_CONVERSATION_MAP[params.tabId]=r}})),params.isExtMessageList)if(r.length>0)params.conversationId=r[0].id,params.pageSize=50,"function"==typeof t&&t(params);else if("function"==typeof params.callback){var o={};params.tabId?(o.id=params.tabId,o.isGroup=params.isGroup):params.clientGroupId&&(o.clientGroupId=params.clientGroupId),o.topicId=params.topicId,o.status="success",o.messages=[],params.callback(o)}}else if(params.isExtMessageList&&"function"==typeof params.callback){o={};params.tabId?o.id=params.tabId:params.clientGroupId&&(o.clientGroupId=params.clientGroupId),o.topicId=params.topicId,o.status="error",o.errorMessage="Unable to process request. Please try again.",params.callback(o)}},error:function(){if("function"==typeof params.callback){var e={};params.tabId?e.id=params.tabId:params.clientGroupId&&(e.clientGroupId=params.clientGroupId),e.topicId=params.topicId,e.status="error",e.errorMessage="Unable to process request. Please try again.",params.callback(e)}}})},o.getTopicId=function(e,r){if(e.conversationId){var n="id="+e.conversationId;window.Applozic.ALApiService.getTopicId({data:{conversationId:e.conversationId},success:function(s){if("object"==typeof n&&"success"===n.status){var a=n.response;if("object"==typeof a){if(MCK_TOPIC_CONVERSATION_MAP[a.topicId]=[e.conversationId],MCK_CONVERSATION_MAP[e.conversationId]=a,a.topicDetail)try{MCK_TOPIC_DETAIL_MAP[a.topicId]=JSON.parse(a.topicDetail)}catch(e){w.console.log("Incorect Topic Detail!")}if("function"==typeof MCK_PRICE_DETAIL&&e.priceText&&(MCK_PRICE_DETAIL({custId:t,suppId:e.suppId,productId:a.topicId,price:e.priceText}),o.sendConversationCloseUpdate(e.conversationId)),e.messageType&&"object"==typeof e.message){var i=e.message.groupId?e.message.groupId:e.message.to;if(void 0!==MCK_TAB_CONVERSATION_MAP[i]){var c=MCK_TAB_CONVERSATION_MAP[i];c.push(a),MCK_TAB_CONVERSATION_MAP[i]=c}(void 0===e.populate||e.populate)&&"function"==typeof r&&r(e)}"function"==typeof e.callback&&e.callback(a)}}},error:function(){}})}},o.sendConversationCloseUpdate=function(e){if(e){window.Applozic.ALApiService.sendConversationCloseUpdate({conversationId:e,success:function(e){},error:function(){}})}},o.dispatchMessage=function(e){if("object"===e.messagePxy){var t=e.messagePxy;if(e.topicId){var r=MCK_TOPIC_DETAIL_MAP[e.topicId];if("object"==typeof r&&"undefined"!==r.title)if(t.message||(t.message=r.title&&r.title.trim()),e.conversationId)t.conversationId=e.conversationId;else if(e.topicId){var o={topicId:e.topicId};"object"==typeof r&&(o.topicDetail=w.JSON.stringify(r)),t.conversationPxy=o}if(!t.message&&r.link){var n={blobKey:r.link.trim(),contentType:"image/png"};t.fileMeta=n,t.contentType=5,FILE_META=[],FILE_META.push(n)}}e.isGroup?t.groupId=e.tabId:t.to=e.tabId,mckMessageService.sendMessage(t)}},o.sendVideoCallMessage=function(e,r,o,n,s,a){var i="CALL_MISSED"==r?"Missed Call":"CALL_REJECTED"==r?"Call Rejected":"";""!=i&&null!=i||(i="video message");var c={to:s,type:5,contentType:o,message:i,metadata:{MSG_TYPE:r,CALL_ID:e,CALL_AUDIO_ONLY:n},senderName:t};return a(c),c},o.sendVideoCallEndMessage=function(e,t,r,o,n,s,a){var i="";n&&(i=mckDateUtils.convertMilisIntoTime(n));var c="CALL_MISSED"==t?"Missed Call":"CALL_REJECTED"==t?"Call Rejected":"CALL_END"==t?"Call End \n Duration: "+i:"video message";""!=c&&null!=c||(c="video message");var l={to:s,type:5,contentType:r,message:c,metadata:{MSG_TYPE:t,CALL_ID:e,CALL_AUDIO_ONLY:o,CALL_DURATION:n}};return a(l),l},o.getMessageFeed=function(e){var o={};if(r=window.Applozic.ALApiService.getFileUrl(),o.key=e.key,o.contentType=e.contentType,o.timeStamp=e.createdAtTime,o.message=e.message,o.from=4===e.type?e.to:t,e.groupId?o.to=e.groupId:o.to=5===e.type?e.to:t,o.status="read",o.type=4===e.type?"inbox":"outbox",5===e.type&&(3===e.status?o.status="sent":4===e.status&&(o.status="delivered")),"object"==typeof e.fileMeta){var n=mckUtils.extendObject({},{},e.fileMeta);void 0!==n.url&&""!==n.url||(n.url=r+"/rest/ws/aws/file/"+e.fileMeta.blobKey),delete n.blobKey,o.file=n}return o.source=e.source,o.metadata=e.metadata,o}}