# vue

指令中`***`为配置环境，目前存在local,test,stage,prod（其中local没有build配置)

## Project setup

```base
npm install
```

### Compiles and hot-reloads for development

```base
npm run ***-serve
```

### Compiles and minifies for production

```base
npm run ***-build
```

### Lints and fixes files

```base
npm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

## Vue UI

前提条件是全局安装有vue cli3

注意：关于vueUI 启动的服务可能存在登录请求错误的问题，原因在于，服务启动时，vueUI自带有默认变量，需要通过UI界面的变量按钮修改为`(unset)`
github issues解决方案地址[https://github.com/vuejs/vue-cli/issues/2927](https://github.com/vuejs/vue-cli/issues/2927)（2018/12/13有效）

### 安装Vue Cli3

```base
npm install -g @vue/cli
```

### 启动图形界面

```base
vue ui
```