{"name": "vue", "version": "0.1.0", "private": true, "scripts": {"lint": "vue-cli-service lint ", "fix": "vue-cli-service lint --fix", "dev": "vue-cli-service serve --mode dev ", "test-serve": "vue-cli-service serve --mode test", "stage-serve": "vue-cli-service serve --mode stage", "prod-serve": "vue-cli-service serve --mode prod", "dev-build": "vue-cli-service build --mode dev", "test-build": "vue-cli-service build --mode test", "stage-build": "vue-cli-service build --mode stage", "prod-build": "vue-cli-service build --mode prod", "test:e2e": "vue-cli-service test:e2e", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@datadog/browser-rum": "^6.6.4", "@microsoft/fetch-event-source": "^2.0.1", "@wchbrad/vue-easy-tree": "^1.0.13", "canvas-confetti": "^1.9.3", "crypto-js": "^4.2.0", "diff": "^5.1.0", "diff2html": "^3.4.35", "driver.js": "^0.9.8", "element-ui": "^2.15.7", "html2canvas": "^1.0.0-rc.7", "moment": "^2.23.0", "nprogress": "^0.2.0", "print-js": "^1.6.0", "qs": "^6.12.3", "quill": "^1.3.7", "sortablejs": "^1.15.0", "ts-ebml": "^2.0.2", "uuid": "^8.3.2", "vue": "^2.5.17", "vue-flex-waterfall": "^1.0.8", "vue-i18n": "^8.0.0", "vue-mobile-audio": "^0.1.3", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vue-virtual-scroll-list": "^2.3.5", "vuex": "^3.0.1", "workbox-webpack-plugin": "^7.3.0", "workbox-window": "^7.3.0"}, "devDependencies": {"@kazupon/vue-i18n-loader": "^0.3.0", "@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-e2e-cypress": "^3.3.0", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-plugin-unit-mocha": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.20", "axios": "^0.21.1", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "bootstrap": "^3.3.7", "chai": "^4.1.2", "compression-webpack-plugin": "^3.0.0", "element-theme": "^2.0.1", "element-theme-chalk": "^2.4.11", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0-0", "jquery": "^3.3.1", "js-htmlencode": "0.3.0", "less": "^3.0.4", "less-loader": "^4.1.0", "node-sass": "^4.9.2", "optimize-css-assets-webpack-plugin": "^6.0.1", "sass-loader": "^7.0.3", "vue-cli-plugin-axios": "0.0.4", "vue-cli-plugin-element": "^1.0.0", "vue-cli-plugin-i18n": "^0.5.1", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "element-theme": {"browsers": ["ie > 9", "last 2 versions"], "out": "./public/lib/custom/theme", "config": "./element-variables.scss", "theme": "element-theme-chalk", "minimize": true}}