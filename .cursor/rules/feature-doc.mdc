---
description: 需求文档，有且仅当使用编写详细设计的时候使用
globs: 
alwaysApply: false
---
以下是根据您的要求编写的详细设计，已将其修改为 Cursor 规则格式：

### Cursor 规则详细设计

1. **规则名称**: Cursor 必须遵守的规则

2. **规则描述**:
   - 所有涉及到后台代码的请求必须包含具体的 API 链接，例如 `/api/v1/invitation`。
   - 无论是后台还是前台的修改，都必须详细列出具体的步骤，例如：
     1. 参数校验
     2. 查询数据库
     3. 拼凑数据
     4. 返回对应的结果
   - 每个步骤都应尽可能详细，避免笼统的描述。
   - 返回的结果应使用 Markdown 格式。
   - 任何逻辑都必须结合对应的代码，避免凭空想象。
   - 不需要进行具体的代码实现，尽量使用伪代码。涉及到修改代码时，需标注文件及具体方法，例如 `app.vue#setupHeaderHeightListener()`。

3. **示例步骤**:
   - **步骤 1**: 参数校验
     - 检查请求参数是否完整，类型是否正确。
     - 示例伪代码：
       ```pseudo
       if (request.params.isValid()) {
           // 继续处理
       } else {
           return errorResponse("参数无效");
       }
       ```

   - **步骤 2**: 查询数据库
     - 根据请求参数查询数据库，获取相关数据。
     - 示例伪代码：
       ```pseudo
       data = database.query("SELECT * FROM invitations WHERE id = ?", request.params.id);
       ```

   - **步骤 3**: 拼凑数据
     - 将查询到的数据进行格式化，准备返回给前端。
     - 示例伪代码：
       ```pseudo
       responseData = formatData(data);
       ```

   - **步骤 4**: 返回对应的结果
     - 将拼凑好的数据返回给前端。
     - 示例伪代码：
       ```pseudo
       return successResponse(responseData);
       ```
