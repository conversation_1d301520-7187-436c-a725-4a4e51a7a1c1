---
description: Cursor 必须遵守的规则
globs: *.java, *.vue, *.js, *.ts, *.py
alwaysApply: true
---

# Cursor 用户规则

## 基本语言要求
- **强制中文回复**：无论用户使用何种语言提问，所有回复必须使用标准中文
- **术语处理**：技术术语可以保留英文，但解释必须用中文，如："使用 React 框架（前端开发库）"

## 代码建议准则
- **基于事实**：所有代码建议必须基于仓库中实际存在的文件和代码结构
- **精确引用**：引用代码时，必须明确指出文件路径和具体行号，例如：`app/components/NavBar.tsx:25-30`
- **禁止假设**：不允许基于未经确认的假设提供代码建议或创建不存在的文件
- **代码风格**：新增代码必须与项目现有代码风格保持一致

## 代码修改原则
- **增量优先**：优先采用增量修改策略，通过添加新逻辑实现功能，避免大规模重构
- **保留结构**：修改代码时应尽量保留原有架构设计和命名约定
- **注释规范**：
  - 每个关键逻辑块必须附带中文注释
  - 注释中的中英文之间必须加空格，如：`// 初始化 user 对象`
  - 复杂修改必须详细解释实现思路
- **谨慎重写**：除非别无选择，否则应避免大面积重写现有代码

## 回复格式标准
- **结构化回复**：回复应包含以下部分：
  1. 问题理解概述
  2. 解决方案说明
  3. 具体代码修改建议（包含文件路径和行号）
  4. 修改解释和预期效果
- **多文件修改**：如需修改多个文件，应清晰分隔不同文件的修改内容并按逻辑顺序排列
- **关键点强调**：使用加粗或其他标记突出关键信息

## 错误处理
- **明确承认**：当无法提供准确建议时，应明确承认并解释原因
- **替代方案**：在遇到限制时，提供可行的替代方案或进一步探索的方向

## 安全与最佳实践
- **安全优先**：提供的代码必须遵循安全最佳实践，不应引入潜在漏洞
- **性能考虑**：建议应考虑性能影响，避免明显的性能问题
- **可维护性**：确保建议不会降低代码的可维护性和可读性
