html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,font,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,figcaption,figure,footer,header,hgroup,menu,nav,section,summary,time,mark,audio,video{border:0;margin:0;outline:0;padding:0;vertical-align:baseline;}:focus{outline:0;}body{background:#fff;color:#111C1C;font-family:"Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif, 'YaHei';font-size:20px;}
.padding-lr{
    padding-left: 25px;
    padding-right: 25px;
}
.padding-lr-5{
    padding-left: 5px;
    padding-right: 5px;
}
.padding-l{
    padding-left: 15px;
}
.padding-r{
    padding-right: 15px;
}
.margin-b{
    margin-bottom: 10px;
}
.font-size-37{
    font-size: 37px;
}
.font-size-30{
    font-size: 30px;
}
.font-size-27{
    font-size: 27px;
}
.font-size-22{
    font-size: 22px;
}
.font-size-16{
    font-size: 16px;
}
.font-size-15{
    font-size: 15px;
}
.font-size-13{
    font-size: 13px;
}
.color-gray{
    color:#9A9EA0;
}
.fontw600{
    font-weight: 600;
}
.border-right{
    border-right: 1px solid #ECEDF1;
}
.border-left{
    border-left: 1px solid #ECEDF1;
}
.border-top{
    border-top: 1px solid #ECEDF1;
}
.fl{
    float: left;
}
.fr{
    float: right;
}
.full{
    width:100%;
}
.text-right{
    text-align: right;
}
.text-left{
    text-align: left;
}
.main{
    width: 1000px;
    margin: 0px auto;
    background: #f2f2f2;
}
.header{
    text-align: center;
    background: #fff;
    height: 78px;
    position: relative;
}
.header h1{
    font-size: 25px;
    height: 76px;
    line-height: 76px;
}
.line-wrap{
    position: relative;
}
.line-wrap img{
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.menu{
    background: #fff;
    height: 85px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
}
.menu-item{
    font-size: 20px;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    /* align-items: center; */
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding-right: 66px;
}
.menu-item-head{
    color: #76AB3C;
    line-height: 45px;
}
.menu-item-content{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.arc-wrap{
    background: #fff;
    height: 19px;
}
.dashboard-date{
    font-size: 20px;
    height: 76px;
    line-height: 76px;
    text-align: center;
}
.dashboard-main{
    /* background:skyblue; */
    margin-bottom: 15px;
}
.main-row{
    margin-left:  -6px;
    margin-right: -6px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
}
.main-col{
    float: left;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-left: 6px;
    padding-right: 6px;
    width: 50%;
    margin-top: 10px;
}
.main-item{
    background: #fff;
    -webkit-box-shadow:0px 2px 9px 1px rgba(219,224,223,1);
            box-shadow:0px 2px 9px 1px rgba(219,224,223,1);
    border:1px solid rgba(233,236,235,1);
    height: 265px;
}
.main-item-login{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    height: 150px;
}
.main-login-half{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    color: #fff;
}
.main-login-family{
    background:rgba(0,149,193,1);
    -webkit-box-shadow:0px 2px 9px 1px rgba(219,224,223,1);
            box-shadow:0px 2px 9px 1px rgba(219,224,223,1);
}
.main-login-child{
    background:rgba(132,190,67,1);
    -webkit-box-shadow:0px 2px 9px 1px rgba(219,224,223,1);
            box-shadow:0px 2px 9px 1px rgba(219,224,223,1);
}
.main-item-head{
    font-size: 16px;
    padding: 13px 15px;
    height: 44px;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    font-weight: 600;
}
.main-item-login .main-item-content{
    height: 90px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    font-size: 37px;
}
.main-item-notice{
    height: 150px;
}
.main-item-notice .main-item-content{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 90px;
}
.main-notice-item{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
}
.main-notice-item .main-notice-num{
    font-size: 37px;
}
.main-notice-item .main-notice-txt{
    font-size: 13px;
}

.main-item-chat .main-item-content{
    padding: 0 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    text-align: center;
}
.main-chat-l{
    width: 264px;
}
.main-chat-r{
    width: 163px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    height: 210px;
}
.main-chat-rt{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.main-chat-rb{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
}
.main-item-chart{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
}
.main-item-chart .main-item-content{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0 20px;
}
.main-item-chart .main-chart-t{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: center;
    height: 60px;
}
.main-item-chart .main-chart-t-item{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.main-item-chart .main-chart-b{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.main-item-chart .main-chart-b-item{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    text-align: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
}
.main-item-chart .dashboard-pie{
    width: 150px !important;
    flex: none;
}
.main-item-chart .main-chart-b-item:first-child{
    width: 130px;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
}
.main-item-inkind .main-chart-b-item:first-child{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.main-chart-scalewrap{
    font-size: 13px;
    margin-bottom: 5px;
}
.main-chart-scalewrap p{
    margin-bottom: 2px;
}
.chart-item-circle{
    width:10px;
    height:10px;
    border-radius:50%;
    display: inline-block;
    margin-right: 5px;
}
.chart-item-circle-green{
    background:rgba(162,207,112,1);
}
.chart-item-circle-blue{
    background:rgba(0,149,193,1);
}
.chart-item-circle-yellow{
    background:rgba(255,205,81,1);
}
.main-item-inkind .main-inkind-t{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    height: 50px;
    margin-bottom: 20px;
}
.main-inkind-t .font-size-13{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.main-inkind-t .font-size-37{
    -webkit-box-flex: 3;
        -ms-flex: 3;
            flex: 3;
    padding-left: 70px;
}
.main-item-inkind .main-chart-b {
    margin: 0 auto;
    width: 80%;
}
.main-item-event .main-progress{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0 5px;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    width: 150px;
}
.main-progress .main-progress-t{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    /* flex: 1; */
    height: 30px;
}
.main-progress-t .font-size-13{
    text-align: left;
}
.main-progress-b .main-progress-wrap{
    height: 6px;
    line-height: 0;
    background: #F1F1F1;
    border-radius: 3px;
    margin-top: 8px;
    margin-bottom: 4px;
}
.main-progress-b .main-progress-yes{
    background: #0095C0;
    display: inline-block;
    height: 100%;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}
.main-progress-b .main-progress-no{
    background: #FFCD51;
    display: inline-block;
    height: 100%;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.main-progress .main-progress-b{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.main-progress .main-rate-wrap{
    font-size: 12px;
}