<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>pdf</title>
    <script src="./echarts.common.min.js"></script>
    <style>
        .header,.line-wrap{position:relative}.menu,.menu-item{display:-webkit-box;display:-ms-flexbox;-webkit-box-direction:normal}.main-col,.main-row{-webkit-box-sizing:border-box}.main-item,.main-login-family{-webkit-box-shadow:0 2px 9px 1px rgba(219,224,223,1)}.fontw600,.main-item-head{font-weight:600}a,abbr,acronym,address,applet,article,aside,audio,big,blockquote,body,canvas,caption,cite,code,dd,del,details,dfn,div,dl,dt,em,fieldset,figcaption,figure,font,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,iframe,ins,kbd,label,legend,li,mark,menu,nav,object,ol,p,pre,q,s,samp,section,small,span,strike,strong,sub,summary,sup,table,tbody,td,tfoot,th,thead,time,tr,tt,ul,var,video{border:0;margin:0;outline:0;padding:0;vertical-align:baseline}:focus{outline:0}body{background:#fff;color:#111C1C;font-family:"Source Sans Pro","Helvetica Neue",Helvetica,Arial,sans-serif,YaHei;font-size:20px}.padding-lr{padding-left:25px;padding-right:25px}.padding-lr-5{padding-left:5px;padding-right:5px}.padding-l{padding-left:15px}.padding-r{padding-right:15px}.margin-b{margin-bottom:10px}.font-size-37{font-size:37px}.font-size-30{font-size:30px}.font-size-27{font-size:27px}.font-size-16{font-size:16px}.font-size-13{font-size:13px}.color-gray{color:#9A9EA0}.border-right{border-right:1px solid #ECEDF1}.border-left{border-left:1px solid #ECEDF1}.border-top{border-top:1px solid #ECEDF1}.fl{float:left}.fr{float:right}.full{width:100%}.text-right{text-align:right}.text-left{text-align:left}.main{width:1000px;margin:0 auto;background:#f2f2f2}.arc-wrap,.header,.main-item,.menu{background:#fff}.header{text-align:center;height:78px}.header h1{font-size:25px;height:76px;line-height:76px}.line-wrap img{width:100%;position:absolute;bottom:0;left:0;right:0}.menu{height:85px;display:flex;-webkit-box-orient:horizontal;-ms-flex-direction:row;flex-direction:row}.menu-item{font-size:20px;height:100%;display:flex;-webkit-box-orient:vertical;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;padding-right:66px}.main-item-login,.main-login-half{display:-webkit-box;display:-ms-flexbox}.menu-item-head{color:#76AB3C;line-height:45px}.menu-item-content{-webkit-box-flex:1;-ms-flex:1;flex:1}.arc-wrap{height:19px}.dashboard-date{font-size:20px;height:76px;line-height:76px;text-align:center}.dashboard-main{margin-bottom:15px}.main-row{margin-left:-6px;margin-right:-6px;box-sizing:border-box;overflow:hidden}.main-col{float:left;box-sizing:border-box;padding-left:6px;padding-right:6px;width:50%;margin-top:10px}.main-item{box-shadow:0 2px 9px 1px rgba(219,224,223,1);border:1px solid rgba(233,236,235,1);height:265px}.main-item-login{display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;height:150px}.main-chat-r,.main-login-half,.main-notice-item{-webkit-box-orient:vertical;-webkit-box-direction:normal}.main-login-half{display:flex;-ms-flex-direction:column;flex-direction:column;-webkit-box-flex:1;-ms-flex:1;flex:1;color:#fff}.main-login-family{background:rgba(0,149,193,1);box-shadow:0 2px 9px 1px rgba(219,224,223,1)}.main-login-child{background:rgba(132,190,67,1);-webkit-box-shadow:0 2px 9px 1px rgba(219,224,223,1);box-shadow:0 2px 9px 1px rgba(219,224,223,1)}.main-item-head{font-size:16px;padding:13px 15px;height:44px;-webkit-box-sizing:border-box;box-sizing:border-box}.main-item-login .main-item-content{height:90px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;font-size:37px}.main-item-notice{height:150px}.main-item-notice .main-item-content{display:-webkit-box;display:-ms-flexbox;display:flex;height:90px}.main-notice-item{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.main-notice-item .main-notice-num{font-size:37px}.main-notice-item .main-notice-txt{font-size:13px}.main-item-chat .main-item-content{padding:0 20px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;text-align:center}.main-chat-r,.main-chat-rb{display:-webkit-box;display:-ms-flexbox}.main-chat-l{width:264px}.main-chat-r{width:163px;display:flex;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:210px}.main-chat-rt{-webkit-box-flex:1;-ms-flex:1;flex:1}.main-chat-rb{-webkit-box-flex:1;-ms-flex:1;flex:1;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.main-item-chart,.main-item-chart .main-item-content{display:-webkit-box;display:-ms-flexbox;-webkit-box-orient:vertical;-webkit-box-direction:normal}.main-item-chart{display:flex;-ms-flex-direction:column;flex-direction:column}.main-item-chart .main-item-content{display:flex;-ms-flex-direction:column;flex-direction:column;padding:0 20px}.main-item-chart .main-chart-t{display:-webkit-box;display:-ms-flexbox;display:flex;text-align:center;height:60px}.main-item-chart .main-chart-t-item{-webkit-box-flex:1;-ms-flex:1;flex:1}.main-item-chart .main-chart-b{display:-webkit-box;display:-ms-flexbox;display:flex}.main-item-chart .main-chart-b-item{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-ms-flex:1;flex:1;text-align:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.main-item-chart .dashboard-pie{width:150px!important;flex:none}.main-item-chart .main-chart-b-item:first-child{width:130px;-webkit-box-flex:0;-ms-flex:none;flex:none}.main-item-inkind .main-chart-b-item:first-child{-webkit-box-flex:1;-ms-flex:1;flex:1}.main-chart-scalewrap{font-size:13px;margin-bottom:5px}.main-chart-scalewrap p{margin-bottom:2px}.chart-item-circle{width:10px;height:10px;border-radius:50%;display:inline-block;margin-right:5px}.main-item-event .main-progress,.main-item-inkind .main-inkind-t{display:-webkit-box;display:-ms-flexbox;-webkit-box-direction:normal}.chart-item-circle-green{background:rgba(162,207,112,1)}.chart-item-circle-blue{background:rgba(0,149,193,1)}.chart-item-circle-yellow{background:rgba(255,205,81,1)}.main-item-inkind .main-inkind-t{display:flex;-webkit-box-orient:horizontal;-ms-flex-direction:row;flex-direction:row;-webkit-box-align:center;-ms-flex-align:center;align-items:center;height:50px;margin-bottom:20px}.main-inkind-t .font-size-13{-webkit-box-flex:1;-ms-flex:1;flex:1}.main-inkind-t .font-size-37{-webkit-box-flex:3;-ms-flex:3;flex:3;padding-left:70px}.main-item-inkind .main-chart-b{margin:0 auto;width:80%}.main-item-event .main-progress{display:flex;-webkit-box-orient:vertical;-ms-flex-direction:column;flex-direction:column;padding:0 5px;-webkit-box-flex:0;-ms-flex:none;flex:none;width:150px}.main-progress .main-progress-t{display:-webkit-box;display:-ms-flexbox;display:flex;height:30px}.main-progress-t .font-size-13{text-align:left}.main-progress-b .main-progress-wrap{height:6px;line-height:0;background:#F1F1F1;border-radius:3px;margin-top:8px;margin-bottom:4px}.main-progress-b .main-progress-yes{background:#0095C0;display:inline-block;height:100%;border-top-left-radius:3px;border-bottom-left-radius:3px}.main-progress-b .main-progress-no{background:#FFCD51;display:inline-block;height:100%;border-top-right-radius:3px;border-bottom-right-radius:3px}.main-progress .main-progress-b{-webkit-box-flex:1;-ms-flex:1;flex:1}.main-progress .main-rate-wrap{font-size:12px}
    </style>
</head>
<!--  wkhtmltopdf -s A4 -T 0 -B 0 -L 0 -R 0 --image-dpi 150 --javascript-delay 2000 dashboardPdf.html ***.pdf -->
<body>
    <div class="main">
        <header class="header padding-lr">
            <h1>Family Engagement Dashboard</h1>
            <div class="line-wrap padding-lr">
                <img src="./dashboard-line.png" alt="">
            </div>
        </header>
        <menu class="menu padding-lr" id="menu">
            <!-- <div class="menu-item">
                <div class="menu-item-head">Agency</div>
                <div class="menu-item-content">Learning Genie Academy</div>
            </div>
            <div class="menu-item">
                <div class="menu-item-head">Center</div>
                <div class="menu-item-content">Happy School</div>
            </div>
            <div class="menu-item">
                <div class="menu-item-head">Class</div>
                <div class="menu-item-content">Happy Class</div>
            </div> -->
        </menu>
        <section class="arc-wrap line-wrap">
            <img src="./dashboard-arc.png" alt="">
        </section>
        <section class="dashboard-date" id="menuDate">
            <!-- <p>08/07/2018-09/07/2018</p> -->
        </section>
        <main class="dashboard-main">
            <div class="main-row padding-lr" style="margin-bottom: 30px;">
                <!-- register -->
                <div class="main-col" id="registWrap">
                    <div class="main-item-login">
                        <div class="main-login-half main-login-family">
                            <div class="main-item-head">Total Families Registered</div>
                            <div class="main-item-content" id="totalFamily">15</div>
                        </div>
                        <div class="main-login-half main-login-child" id="registHalfWrap" style="margin-left:10px;display: block;width: 229px;">
                            <div class="main-item-head">Total Child Count</div>
                            <div class="main-item-content" id="totalChild">15</div>
                        </div>
                    </div>
                </div>
                <div class="main-col" id="registChildWrap" style="display: none">
                    <div class="main-item-login">
                        <div class="main-login-half main-login-child">
                            <div class="main-item-head">Total Child Count</div>
                            <div class="main-item-content" id="totalChild1">15</div>
                        </div>
                    </div>
                </div>
                <!-- notification -->
                <div class="main-col" id="noticeWrap">
                    <div class="main-item main-item-notice">
                        <div class="main-item-head">School Notification</div>
                        <div class="main-item-content">
                            <div class="main-notice-item border-right" style="width: 233px;">
                                <div class="main-notice-num" id="noticeNum">3</div>
                                <div class="main-notice-txt" v-t="'loc.tolSchNotify'">Total school notifications</div>
                            </div>
                            <div class="main-notice-item" style="width: 233px;">
                                <div class="main-notice-num"><span id="noticeRate">92</span>%</div>
                                <div class="main-notice-txt">Average open rate</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- active -->
                <div class="main-col" id="activeWrap">
                    <div class="main-item">
                        <div class="main-item-head" id="activeTxt">Daily Active Parent App Users</div>
                        <div class="dashboard-bar" id="actUser" style="width: 100%;height: 220px;"></div>
                    </div>
                </div>
                <!-- two way -->
                <div class="main-col" id="chatWrap">
                    <div class="main-item main-item-chat">
                        <div class="main-item-head" translate="twoWaycom">Two-Way Communication</div>
                        <div class="main-item-content">
                            <div class="main-chat-l">
                                <div class="font-size-37" id="chatMsg">123</div>
                                <div class="font-size-13">Total message sent</div>
                                <div class="font-size-13 color-gray">(Parents, Staff and Admin)</div>
                            </div>
                            <div class="main-chat-r">
                                <div class="main-chat-rt">
                                    <div class="font-size-27" id="chatTranFamily">2</div>
                                    <div class="font-size-13">Number of families using translation</div>
                                </div>
                                <div class="main-chat-rb border-top">
                                    <div class="font-size-27" id="chatTranMsg">28</div>
                                    <div class="font-size-13">Total number of translated messages</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- event -->
                <div class="main-col" id="eventWrap">
                    <div class="main-item main-item-chart main-item-event">
                        <div class="main-item-head">Event</div>
                        <div class="main-item-content">
                            <div class="main-chart-t margin-b">
                                <div class="main-chart-t-item padding-lr-5" style="width: 126px;">
                                    <div class="font-size-30" id="eventTotal">2</div>
                                    <div class="font-size-13">Total number of events</div>
                                </div>
                                <div class="main-chart-t-item border-right border-left main-progress" style="width: 152px;">
                                    <div class="main-progress-t" style="margin-bottom: 10px;">
                                        <div class="font-size-13" style="width: 90px;">Average response rate</div>
                                        <div class="font-size-30" style="width: 60px;text-align: right;"><span id="eventAvgRate">76</span>%</div>
                                    </div>
                                    <div class="main-progress-b" style="height: 32px;">
                                        <div class="main-progress-wrap">
                                            <span class="main-progress-yes fl" id="resYesRateBar" style="width: 90%"></span>
                                            <span class="main-progress-no fl"  id="resNoRateBar"style="width: 10%"></span>
                                        </div>
                                        <div class="main-rate-wrap">
                                            <span class="main-rate-yes fl">Yes <b id="resYesRateNum">90</b><b>%</b></span>
                                            <span class="main-rate-no fr">No <b id="resNoRateNum">10</b><b>%</b></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="main-chart-t-item" style="width: 126px;">
                                    <div class="font-size-30"><span id="eventAvgParRate">68</span>%</div>
                                    <div class="font-size-13">Average parent participation rate</div>
                                </div>
                            </div>
                            <div class="main-chart-b">
                                <div class="main-chart-b-item main-chart-b-first">
                                    <div class="font-size-27" id="eventTolNum">28</div>
                                    <div class="font-size-13">Total attendees</div>
                                </div>
                                <div class="dashboard-pie main-chart-b-item" id="eventPie" style="width: 100%;height: 130px;"></div>
                                <div class="main-chart-b-item" style="width: 147px;">
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-green"></i>Father
                                            Figure:</p>
                                        <span class="fontw600 fl padding-l" id="eventFathNum">10</span>
                                        <span class="fontw600 fr padding-r"><span id="eventFathRate">35</span>%</span>
                                    </div>
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-blue"></i>Mother
                                            Figure:</p>
                                        <span class="fontw600 fl padding-l" id="eventMothNum">10</span>
                                        <span class="fontw600 fr padding-r"><span id="eventMothRate">35</span>%</span>
                                    </div>
                                    <div class="main-chart-scalewrap" id="eventOthWrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-yellow"></i>Other:</p>
                                        <span class="fontw600 fl padding-l" id="eventOthNum">2</span>
                                        <span class="fontw600 fr padding-r"><span id="eventOthRate">5</span>%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- in-kind -->
                <div class="main-col" id="inKindWrap">
                    <div class="main-item main-item-chart main-item-inkind">
                        <div class="main-item-head">In-kind</div>
                        <div class="main-item-content">
                            <div class="main-inkind-t">
                                <div class="font-size-13">Total value:</div>
                                <div class="font-size-37" id="kindTolVal">$<span>6,211</span></div>
                            </div>
                            <div class="main-chart-b">
                                <div class="dashboard-pie main-chart-b-item" id="inKindPie" style="width: 100%;height: 130px;"></div>
                                <div class="main-chart-b-item">
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-green"></i>At
                                            home activity:</p>
                                        <span class="fontw600 fl padding-l">$<span id="kindHomeNum">4,037</span></span>
                                        <span class="fontw600 fr padding-r"><span  id="kindHomeRate">65</span>%</span>
                                    </div>
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-blue"></i>In
                                            school activity:</p>
                                        <span class="fontw600 fl padding-l">$<span id="kindSchNum">4,037</span></span>
                                        <span class="fontw600 fr padding-r"><span  id="kindSchRate">35</span>%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- media -->
                <div class="main-col">
                    <div class="main-item main-item-chart">
                        <div class="main-item-head">Book</div>
                        <div class="main-item-content">
                            <div class="main-chart-t margin-b">
                                <div class="main-chart-t-item" style="width: 140px;">
                                    <div class="font-size-30" id="bookTotal">5</div>
                                    <div class="font-size-13">Total books read</div>
                                </div>
                                <div class="main-chart-t-item border-right border-left" style="width: 140px;">
                                    <div class="font-size-30" id="bookTolWord">6,012</div>
                                    <div class="font-size-13">Total words</div>
                                </div>
                                <div class="main-chart-t-item" style="width: 140px;">
                                    <div class="font-size-30" id="bookAvgRead">3.5</div>
                                    <div class="font-size-13">Average reading level</div>
                                </div>
                            </div>
                            <div class="main-chart-b">
                                <div class="main-chart-b-item main-chart-b-first">
                                    <div><span class="font-size-27" id="bookTolHour">12</span>&nbsp;hr</div>
                                    <div class="font-size-13">Total reading hours</div>
                                </div>
                                <div class="dashboard-pie main-chart-b-item" id="bookPie" style="width: 100%;height: 130px;"></div>
                                <div class="main-chart-b-item" style="width: 147px;">
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-green"></i>Father
                                            Figure:</p>
                                        <span class="fl padding-l"><span class="fontw600" id="bookFathNum">3</span>&nbsp;hr</span>
                                        <span class="fontw600 fr padding-r"><span id="bookFathRate">31</span>%</span>
                                    </div>
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-blue"></i>Mother
                                            Figure:</p>
                                        <span class="fl padding-l"><span class="fontw600" id="bookMothNum">3</span>&nbsp;hr</span>
                                        <span class="fontw600 fr padding-r"><span id="bookMothRate">31</span>%</span>
                                    </div>
                                    <div class="main-chart-scalewrap" id="bookOthWrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-yellow"></i>Other:</p>
                                        <span class="fl padding-l"><span class="fontw600" id="bookOthNum">3</span>&nbsp;hr</span>
                                        <span class="fontw600 fr padding-r"><span id="bookOthRate">31</span>%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- learning media -->
                <div class="main-col">
                    <div class="main-item main-item-chart">
                        <div class="main-item-head">Learning Media</div>
                        <div class="main-item-content">
                            <div class="main-chart-t margin-b">
                                <div class="main-chart-t-item">
                                    <div class="font-size-30" id="mediaTotal">6</div>
                                    <div class="font-size-13">Total videos watched</div>
                                </div>
                                <div class="main-chart-t-item border-left">
                                    <div class="font-size-30" id="mediaTolView">36</div>
                                    <div class="font-size-13">Total view counts</div>
                                </div>
                            </div>
                            <div class="main-chart-b">
                                <div class="main-chart-b-item main-chart-b-first">
                                    <div><span class="font-size-27" id="mediaHour">13</span>&nbsp;hr</div>
                                    <div class="font-size-13">Total viewing duration</div>
                                </div>
                                <div class="dashboard-pie main-chart-b-item" id="mediaPie" style="width: 100%;height: 130px;"></div>
                                <div class="main-chart-b-item" style="width: 147px;">
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-green"></i>Father
                                            Figure:</p>
                                        <span class="fl padding-l"><span class="fontw600" id="mediaFathNum">3</span>&nbsp;hr</span>
                                        <span class="fontw600 fr padding-r"><span id="mediaFathRate">31</span>%</span>
                                    </div>
                                    <div class="main-chart-scalewrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-blue"></i>Mother
                                            Figure:</p>
                                        <span class="fl padding-l"><span class="fontw600" id="mediaMothNum">3</span>&nbsp;hr</span>
                                        <span class="fontw600 fr padding-r"><span id="mediaMothRate">31</span>%</span>
                                    </div>
                                    <div class="main-chart-scalewrap" id="mediaOthWrap">
                                        <p class="text-left full"><i class="chart-item-circle chart-item-circle-yellow"></i>Other:</p>
                                        <span class="fl padding-l"><span class="fontw600" id="mediaOthNum">3</span>&nbsp;hr</span>
                                        <span class="fontw600 fr padding-r"><span id="mediaOthRate">31</span>%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script>
        var jsonData = {
            agencyName:"Learning Genie Academy",
            centerName:"Happy School",
            className:"Happy Class",
            fromData:"08/07/2018",
            toData:"09/07/2018",
            noticeOpen: true, // 学校通知开关
            chatOpen: true, // 双向沟通开关
            eventOpen: true, // event开关
            inKindOpen: true, // in-kind开关
            registerStats:{"totalFamily":2600,"totalChild":4110000},
            activeStats: { type: 'DAY',loginCounts: [{date: '08/07',count: 0}, {date: '08/14',count: 0}, {date: '08/21',count: 0}, {date: '08/28',count: 0}, {date: '09/04', count: 0}]},
            notificationStats:{"totalNotification":3,"averageOpenRate":90},
            chatStats:{"totalMessage":10,"totalTranslatedFamily":20,"totalTranslatedMessage":30},
            eventStats:{"totalEvent":0,"avgResponseRate":0,"responseAttendRate":0,"responseNotAttendRate":0,"avgAttendRate":0,"totalAttendNum":0,"fatherAttendNum":10,"fatherAttendRate":0,"motherAttendNum":8,"motherAttendRate":0,"otherAttendNum":2,"otherAttendRate":0},
            inKindStats:{"totalValue":341648,"atHomeValue":81995,"atHomeRate":65,"inSchoolValue":25236,"inSchoolRate":35},
            bookStats:{"totalBook":0,"totalWord":0,"avgReadLevel":0,"totalHour":0,"fatherNum":0,"fatherRate":0,"motherNum":0,"motherRate":0,"otherNum":0,"otherRate":0},
            mediaStats:{"totalVideo":0,"totalView":0,"totalHour":0,"fatherNum":30,"fatherRate":0,"motherNum":20,"motherRate":0,"otherNum":10,"otherRate":0},
        }
        // 初始化
        init()

        function init() {
            // 设置机构、学校、班级
            setMenu()
            // 设置起止日期
            setMenuDate()
            // 日活/月活/年活
            setActive()
            // book
            setBook()
            // learning Media
            setMedia()
            // 学校通知
            if(jsonData.noticeOpen){
                document.getElementById('registHalfWrap').style.display = 'block'
                document.getElementById('noticeWrap').setAttribute('style','display:block')
                document.getElementById('registChildWrap').setAttribute('style','display:none')
                setVal('noticeNum',numFormat(jsonData.notificationStats.totalNotification))
                setVal('noticeRate',jsonData.notificationStats.averageOpenRate)
                // 注册统计
                setVal('totalFamily',numFormat(jsonData.registerStats.totalFamily))
                setVal('totalChild',numFormat(jsonData.registerStats.totalChild))
            }else{
                document.getElementById('registHalfWrap').style.display = 'none'
                document.getElementById('noticeWrap').setAttribute('style','display:none')
                document.getElementById('registChildWrap').setAttribute('style','display:block')
                // 注册统计
                setVal('totalFamily',numFormat(jsonData.registerStats.totalFamily))
                setVal('totalChild1',numFormat(jsonData.registerStats.totalChild))
            }
            // 双向沟通
            if(jsonData.chatOpen){
                setVal('chatMsg',numFormat(jsonData.chatStats.totalMessage))
                setVal('chatTranFamily',numFormat(jsonData.chatStats.totalTranslatedFamily))
                setVal('chatTranMsg',numFormat(jsonData.chatStats.totalTranslatedMessage))
            }else{
                document.getElementById('chatWrap').setAttribute('style','display:none')
            }
            // event
            if(jsonData.eventOpen){
                setEvent()
            }else{
                document.getElementById('eventWrap').setAttribute('style','display:none')
            }
            // in-kinde
            if(jsonData.inKindOpen){
                setInKind()
            }else{
                document.getElementById('inKindWrap').setAttribute('style','display:none')
            }
        }
        function setMenu(){
            var el = document.getElementById('menu');
            var data = jsonData;
            var str='<div class="menu-item">'+
                        '<div class="menu-item-head">Agency</div>'+
                        ' <div class="menu-item-content">'+data.agencyName+'</div>'+
                    '</div>'+
                    '<div class="menu-item">'+
                        '<div class="menu-item-head">Center</div>'+
                        '<div class="menu-item-content">'+data.centerName+'</div>'+
                    '</div>'+
                    '<div class="menu-item">'+
                        '<div class="menu-item-head">Class</div>'+
                        '<div class="menu-item-content">'+data.className+'</div>'+
                    '</div>'
            el.innerHTML = str
        }
        function setMenuDate(){
            var el = document.getElementById('menuDate');
            var data = jsonData;
            var str='<p>'+jsonData.fromData+'-'+jsonData.toData+'</p>'
            el.innerHTML = str;
        }
        function setVal(id,val){
            var el = document.getElementById(id);
            el.innerHTML = val;
        }
        // 日活
        function setActive(){
            var data = jsonData.activeStats;
            var activeTxt = '';
            switch (data.type) {
                case 'YEAR':
                    activeTxt = 'Annual Active Parent App Users';
                    break;
                case 'MONTH':
                    activeTxt = 'Monthly Active Parent App Users';
                    break;
                default:
                    activeTxt = 'Daily Active Parent App Users';
                    break;
            }
            setVal('activeTxt',activeTxt);
            var actUserData = data.loginCounts;
            var dataX = getDataX(actUserData);
            var dataY = getDataY(actUserData);
            var dataShadow = getDataShadow(dataY);
            var actUserOption = {
                animation: false,
                grid: {
                    top: '30',
                    left: '50',
                    right: '30',
                    bottom: '30'
                },
                // tooltip: {},
                xAxis: {
                    data: dataX,
                    axisLabel: {},
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    z: 10
                },
                yAxis: {
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#111C1C'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#f1f1f1'
                        }
                    }
                },
                series: [{ // For shadow
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: 'rgba(241,241,241,1)'
                            }
                        },
                        barGap: '-100%',
                        barCategoryGap: '40%',
                        data: dataShadow,
                        animation: false,
                        silent: true
                    },
                    {
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: '#0095C1'
                            },
                            emphasis: {
                                color: '#05a3d2'
                            }
                        },
                        data: dataY
                    }
                ]
            }
            echarts.init(document.getElementById('actUser')).setOption(actUserOption);
        }
        function getDataX(actUserData) {
            var arr = [];
            for (var index = 0; index < actUserData.length; index++) {
                var el = actUserData[index];
                arr.push(el.date);
            }
            return arr;
        }
        function getDataY(actUserData) {
            var arr = [];
            for (var index = 0; index < actUserData.length; index++) {
                var el = actUserData[index];
                arr.push(el.count);
            }
            return arr;
        }
        function getDataShadow(dataY) {
            var num = dataY;
            var max = Math.max.apply(null, num);
            var yMax = computedMax(max);

            var arr = [];
            var data = dataY;
            for (var i = 0; i < data.length; i++) {
                arr.push(yMax);
            }
            return arr;
        }

        function setEvent(){
            var data = jsonData.eventStats;
            setVal('eventTotal',numFormat(data.totalEvent));
            setVal('eventAvgRate',data.avgResponseRate);
            setVal('resYesRateNum',data.responseAttendRate);
            setVal('resNoRateNum',data.responseNotAttendRate);
            setVal('eventAvgParRate',data.avgAttendRate);
            setVal('eventTolNum',numFormat(data.totalAttendNum));

            setVal('eventFathNum',numFormat(data.fatherAttendNum));
            setVal('eventFathRate',data.fatherAttendRate);
            setVal('eventMothNum',numFormat(data.motherAttendNum));
            setVal('eventMothRate',data.motherAttendRate);

            if(data.otherAttendNum == '0'){
                document.getElementById('eventOthWrap').setAttribute('style','display:none')
            }else{
                setVal('eventOthNum',numFormat(data.otherAttendNum));
                setVal('eventOthRate',data.otherAttendRate);
            }
            document.getElementById('resYesRateBar').style.width = data.responseAttendRate +'%';
            document.getElementById('resNoRateBar').style.width = data.responseNotAttendRate + '%';
            var eventData = {
                noData:false,
                data:[
                    {value:data.fatherAttendNum,name:'father'},
                    {value:data.motherAttendNum,name:'mother'},
                    {value:data.otherAttendNum,name:'other'}
                ]
            }
            if(data.fatherAttendNum == '0' && data.motherAttendNum == '0'&&data.otherAttendNum == '0'){
                eventData.noData = true
            }
            echarts.init(document.getElementById('eventPie')).setOption(pieOption(eventData));
        }
        function setInKind(){
            var data = jsonData.inKindStats;
            setVal('kindTolVal',numFormat(data.totalValue));
            setVal('kindHomeNum',numFormat(data.atHomeValue));
            setVal('kindHomeRate',data.atHomeRate);
            setVal('kindSchNum',numFormat(data.inSchoolValue));
            setVal('kindSchRate',data.inSchoolRate);
            var chartData = {
                noData:false,
                data:[
                    {value:data.atHomeValue,name:'Home'},
                    {value:data.inSchoolValue,name:'School'}
                ]
            }
            if(data.atHomeValue == '0' && data.inSchoolValue == '0'){
                chartData.noData = true
            }
            echarts.init(document.getElementById('inKindPie')).setOption(pieOption(chartData));
        }
        function setBook(){
            var data = jsonData.bookStats;
            setVal('bookTotal',numFormat(data.totalBook));
            setVal('bookTolWord',numFormat(data.totalWord));
            setVal('bookAvgRead',numFormat(data.avgReadLevel));

            setVal('bookTolHour',numFormat(data.totalHour));

            setVal('bookFathNum',numFormat(data.fatherNum));
            setVal('bookFathRate',data.fatherRate);
            setVal('bookMothNum',numFormat(data.motherNum));
            setVal('bookMothRate',data.motherRate);

            if(data.otherNum == '0'){
                document.getElementById('bookOthWrap').setAttribute('style','display:none')
            }else{
                setVal('bookOthNum',numFormat(data.otherNum));
                setVal('bookOthRate',data.otherRate);
            }
            var chartData = {
                noData:false,
                data:[
                    {value:data.fatherNum,name:'father'},
                    {value:data.motherNum,name:'mother'},
                    {value:data.otherNum,name:'other'}
                ]
            }
            if(data.fatherNum == '0' && data.motherNum == '0'&&data.otherNum == '0'){
                chartData.noData = true
            }
            echarts.init(document.getElementById('bookPie')).setOption(pieOption(chartData));
        }
        function setMedia(){
            var data = jsonData.mediaStats;
            setVal('mediaTotal',numFormat(data.totalVideo));
            setVal('mediaTolView',numFormat(data.totalView));

            setVal('mediaHour',numFormat(data.totalHour));

            setVal('mediaFathNum',numFormat(data.fatherNum));
            setVal('mediaFathRate',data.fatherRate);
            setVal('mediaMothNum',numFormat(data.motherNum));
            setVal('mediaMothRate',data.motherRate);

            if(data.otherNum == '0'){
                document.getElementById('mediaOthWrap').setAttribute('style','display:none')
            }else{
                setVal('mediaOthNum',numFormat(data.otherNum));
                setVal('mediaOthRate',data.otherRate);
            }
            var chartData = {
                noData:false,
                data:[
                    {value:data.fatherNum,name:'father'},
                    {value:data.motherNum,name:'mother'},
                    {value:data.otherNum,name:'other'}
                ]
            }
            if(data.fatherNum == '0' && data.motherNum == '0'&&data.otherNum == '0'){
                chartData.noData = true
            }
            echarts.init(document.getElementById('mediaPie')).setOption(pieOption(chartData));
        }
        function computedMax(num) {
            var temp = num;
            var arr = String(num).split('');
            var scale = Math.pow(10, arr.length - 1);
            temp = Math.ceil(temp / scale) * scale;
            if(temp == '0'){
                temp = 10
            }
            return temp
        }
        function pieOption(chartData) {
            var noData = chartData.noData;
            var data = chartData.data;
            var pieOption = {
                animation: false,
                tooltip: {},
                series: [{
                    // name: this.origin.name,
                    type: 'pie',
                    radius: ['50%', '80%'],
                    color: noData ? ['#F1F1F1'] : ['#A2CF70', '#0095C1', '#FFCD51'],
                    hoverAnimation: !noData,
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            show: false
                        }
                    },
                    // emphasis: this.emphasis,
                    silent: true,
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data: noData ? [{value: 0}] : data
                },
                {
                    // name: '',
                    type: 'pie',
                    radius: ['40%', '45%'],
                    color: '#F1F1F1',
                    avoidLabelOverlap: false,
                    hoverAnimation: false,
                    label: {
                        normal: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            show: false
                        }
                    },
                    silent: true,
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    tooltip: { // 禁止鼠标悬停显示提示框
                        show: false
                    },
                    data: [{
                        value: 1
                    }]
                }]
            }
            return pieOption
        }
        function numFormat (value) {
            if (!value) return '0'
            var intPart = Number(value) | 0 // 获取整数部分
            var intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
            return intPartFormat
        }
    </script>
</body>

</html>